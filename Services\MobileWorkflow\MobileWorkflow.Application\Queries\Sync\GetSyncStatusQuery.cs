using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Queries.Sync;

public class GetSyncStatusQuery : IRequest<SyncOperationResult>
{
    public Guid SyncOperationId { get; set; }
}

public class GetSyncStatusQueryHandler : IRequestHandler<GetSyncStatusQuery, SyncOperationResult>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<GetSyncStatusQueryHandler> _logger;

    public GetSyncStatusQueryHandler(
        IAdvancedSyncService syncService,
        ILogger<GetSyncStatusQueryHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<SyncOperationResult> Handle(GetSyncStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting sync status for operation {SyncOperationId}", request.SyncOperationId);

            var result = await _syncService.GetSyncStatusAsync(request.SyncOperationId, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetSyncStatusQuery for operation {SyncOperationId}", request.SyncOperationId);
            return new SyncOperationResult
            {
                Success = false,
                Message = $"Failed to get sync status: {ex.Message}"
            };
        }
    }
}

public class GetPendingConflictsQuery : IRequest<List<ConflictResolutionDto>>
{
    public Guid UserId { get; set; }
}

public class GetPendingConflictsQueryHandler : IRequestHandler<GetPendingConflictsQuery, List<ConflictResolutionDto>>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<GetPendingConflictsQueryHandler> _logger;

    public GetPendingConflictsQueryHandler(
        IAdvancedSyncService syncService,
        ILogger<GetPendingConflictsQueryHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<List<ConflictResolutionDto>> Handle(GetPendingConflictsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting pending conflicts for user {UserId}", request.UserId);

            var conflicts = await _syncService.GetPendingConflictsAsync(request.UserId, cancellationToken);

            return conflicts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetPendingConflictsQuery for user {UserId}", request.UserId);
            return new List<ConflictResolutionDto>();
        }
    }
}

public class GetSyncStatisticsQuery : IRequest<SyncStatistics>
{
    public Guid UserId { get; set; }
    public DateTime? FromDate { get; set; }
}

public class GetSyncStatisticsQueryHandler : IRequestHandler<GetSyncStatisticsQuery, SyncStatistics>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<GetSyncStatisticsQueryHandler> _logger;

    public GetSyncStatisticsQueryHandler(
        IAdvancedSyncService syncService,
        ILogger<GetSyncStatisticsQueryHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<SyncStatistics> Handle(GetSyncStatisticsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting sync statistics for user {UserId}", request.UserId);

            var statistics = await _syncService.GetSyncStatisticsAsync(request.UserId, request.FromDate, cancellationToken);

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetSyncStatisticsQuery for user {UserId}", request.UserId);
            return new SyncStatistics();
        }
    }
}

public class GetPendingSyncItemsQuery : IRequest<List<SyncItem>>
{
    public Guid UserId { get; set; }
    public int Priority { get; set; } = 0;
}

public class GetPendingSyncItemsQueryHandler : IRequestHandler<GetPendingSyncItemsQuery, List<SyncItem>>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<GetPendingSyncItemsQueryHandler> _logger;

    public GetPendingSyncItemsQueryHandler(
        IAdvancedSyncService syncService,
        ILogger<GetPendingSyncItemsQueryHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<List<SyncItem>> Handle(GetPendingSyncItemsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting pending sync items for user {UserId} with priority {Priority}",
                request.UserId, request.Priority);

            var items = await _syncService.GetPendingSyncItemsAsync(request.UserId, request.Priority, cancellationToken);

            return items;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetPendingSyncItemsQuery for user {UserId}", request.UserId);
            return new List<SyncItem>();
        }
    }
}

public class GetOfflineDataQuery : IRequest<List<OfflineSyncQueueDto>>
{
    public Guid UserId { get; set; }
    public bool UnsyncedOnly { get; set; } = true;
    public string? DataType { get; set; }
    public int? Priority { get; set; }
}

public class GetOfflineDataQueryHandler : IRequestHandler<GetOfflineDataQuery, List<OfflineSyncQueueDto>>
{
    private readonly IOfflineDataRepository _offlineDataRepository;
    private readonly ILogger<GetOfflineDataQueryHandler> _logger;

    public GetOfflineDataQueryHandler(
        IOfflineDataRepository offlineDataRepository,
        ILogger<GetOfflineDataQueryHandler> logger)
    {
        _offlineDataRepository = offlineDataRepository;
        _logger = logger;
    }

    public async Task<List<OfflineSyncQueueDto>> Handle(GetOfflineDataQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting offline data for user {UserId}", request.UserId);

            List<MobileWorkflow.Application.DTOs.OfflineData> offlineData;

            if (request.UnsyncedOnly)
            {
                var domainData = await _offlineDataRepository.GetUnsyncedDataAsync(request.UserId, cancellationToken);
                offlineData = domainData.Select(ConvertToDto).ToList();
            }
            else
            {
                var domainData = await _offlineDataRepository.GetByUserIdAsync(request.UserId, cancellationToken);
                offlineData = domainData.Select(ConvertToDto).ToList();
            }

            // Apply filters
            if (!string.IsNullOrEmpty(request.DataType))
            {
                offlineData = offlineData.Where(d => d.DataType.Equals(request.DataType, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (request.Priority.HasValue)
            {
                offlineData = offlineData.Where(d => d.Priority == request.Priority.Value).ToList();
            }

            return offlineData.Select(MapToOfflineSyncQueueDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetOfflineDataQuery for user {UserId}", request.UserId);
            return new List<OfflineSyncQueueDto>();
        }
    }

    private OfflineSyncQueueDto MapToOfflineSyncQueueDto(OfflineData offlineData)
    {
        return new OfflineSyncQueueDto
        {
            Id = offlineData.Id,
            UserId = offlineData.UserId,
            DataType = offlineData.DataType,
            Action = offlineData.Action,
            Data = offlineData.Data,
            Priority = offlineData.Priority,
            CreatedOffline = offlineData.CreatedOffline,
            IsSynced = offlineData.IsSynced,
            SyncAttempts = offlineData.SyncAttempts,
            SyncError = offlineData.SyncError,
            DataSizeBytes = CalculateDataSize(offlineData.Data),
            DataHash = CalculateDataHash(offlineData.Data)
        };
    }

    private long CalculateDataSize(Dictionary<string, object> data)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(data);
        return System.Text.Encoding.UTF8.GetByteCount(json);
    }

    private string CalculateDataHash(Dictionary<string, object> data)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });

        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(json));
        return Convert.ToBase64String(hashBytes);
    }

    private MobileWorkflow.Application.DTOs.OfflineData ConvertToDto(MobileWorkflow.Domain.Entities.OfflineData entity)
    {
        return new MobileWorkflow.Application.DTOs.OfflineData
        {
            Id = entity.Id,
            UserId = entity.UserId,
            MobileSessionId = entity.MobileSessionId,
            MobileAppId = entity.MobileAppId,
            DataType = entity.DataType,
            Action = entity.Action,
            Data = entity.Data,
            CreatedOffline = entity.CreatedOffline,
            SyncedAt = entity.SyncedAt,
            IsSynced = entity.IsSynced,
            SyncError = entity.SyncError,
            SyncAttempts = entity.SyncAttempts,
            Priority = entity.Priority,
            ConflictResolution = entity.ConflictResolution,
            Metadata = entity.Metadata,
            CreatedAt = entity.CreatedOffline,
            LastModified = entity.UpdatedAt,
            Status = entity.IsSynced ? "Synced" : "Pending"
        };
    }
}
