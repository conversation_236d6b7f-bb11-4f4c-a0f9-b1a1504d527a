using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Query to get comprehensive carrier performance summary for dashboard
/// </summary>
public record GetCarrierPerformanceSummaryQuery(
    Guid CarrierId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeEarningsVisualization = true,
    bool IncludeOrderMetrics = true,
    bool IncludeOnTimeDelivery = true,
    bool IncludeTrends = true,
    bool IncludeComparisons = true
) : IRequest<CarrierPerformanceSummaryDto>;
