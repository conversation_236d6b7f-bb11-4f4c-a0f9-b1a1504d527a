# Audit & Compliance Service Setup and Run Script

param(
    [switch]$SetupDatabase,
    [switch]$CreateMigration,
    [switch]$UpdateDatabase,
    [switch]$RunService,
    [switch]$RunTests,
    [string]$MigrationName = "InitialCreate"
)

$ErrorActionPreference = "Stop"

Write-Host "=== Audit & Compliance Service Setup ===" -ForegroundColor Green

# Set working directory to the service root
$ServiceRoot = $PSScriptRoot
Set-Location $ServiceRoot

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "dotnet")) {
    Write-Error ".NET SDK is not installed or not in PATH"
    exit 1
}

if (-not (Test-Command "psql")) {
    Write-Warning "PostgreSQL client (psql) is not installed or not in PATH. Database setup will be skipped."
}

# Setup database
if ($SetupDatabase) {
    Write-Host "Setting up database..." -ForegroundColor Yellow
    
    if (Test-Command "psql") {
        try {
            # Run database setup script
            psql -h localhost -p 5432 -U postgres -f "database-setup.sql"
            Write-Host "Database setup completed successfully!" -ForegroundColor Green
        }
        catch {
            Write-Warning "Database setup failed. Please run the database-setup.sql script manually."
        }
    }
    else {
        Write-Warning "PostgreSQL client not found. Please run the database-setup.sql script manually."
    }
}

# Create migration
if ($CreateMigration) {
    Write-Host "Creating Entity Framework migration..." -ForegroundColor Yellow
    
    Set-Location "AuditCompliance.Infrastructure"
    
    try {
        dotnet ef migrations add $MigrationName --startup-project ../AuditCompliance.API --context AuditComplianceDbContext
        Write-Host "Migration '$MigrationName' created successfully!" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to create migration: $_"
        exit 1
    }
    
    Set-Location $ServiceRoot
}

# Update database
if ($UpdateDatabase) {
    Write-Host "Updating database with migrations..." -ForegroundColor Yellow
    
    Set-Location "AuditCompliance.Infrastructure"
    
    try {
        dotnet ef database update --startup-project ../AuditCompliance.API --context AuditComplianceDbContext
        Write-Host "Database updated successfully!" -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to update database: $_"
        exit 1
    }
    
    Set-Location $ServiceRoot
}

# Build the solution
Write-Host "Building the solution..." -ForegroundColor Yellow

try {
    dotnet build
    Write-Host "Build completed successfully!" -ForegroundColor Green
}
catch {
    Write-Error "Build failed: $_"
    exit 1
}

# Run tests
if ($RunTests) {
    Write-Host "Running tests..." -ForegroundColor Yellow
    
    try {
        dotnet test AuditCompliance.Tests --verbosity normal
        Write-Host "Tests completed!" -ForegroundColor Green
    }
    catch {
        Write-Warning "Some tests failed. Check the output above for details."
    }
}

# Run the service
if ($RunService) {
    Write-Host "Starting Audit & Compliance Service..." -ForegroundColor Yellow
    
    Set-Location "AuditCompliance.API"
    
    try {
        dotnet run
    }
    catch {
        Write-Error "Failed to start service: $_"
        exit 1
    }
}

if (-not $SetupDatabase -and -not $CreateMigration -and -not $UpdateDatabase -and -not $RunService -and -not $RunTests) {
    Write-Host @"
Usage: .\setup-and-run.ps1 [options]

Options:
  -SetupDatabase     Setup PostgreSQL database and user
  -CreateMigration   Create Entity Framework migration (use -MigrationName to specify name)
  -UpdateDatabase    Apply migrations to database
  -RunService        Start the Audit & Compliance Service
  -RunTests          Run unit and integration tests
  -MigrationName     Name for the migration (default: InitialCreate)

Examples:
  .\setup-and-run.ps1 -SetupDatabase -CreateMigration -UpdateDatabase -RunService
  .\setup-and-run.ps1 -CreateMigration -MigrationName "AddRatingSystem"
  .\setup-and-run.ps1 -RunTests
  .\setup-and-run.ps1 -RunService
"@ -ForegroundColor Cyan
}

Write-Host "=== Script completed ===" -ForegroundColor Green
