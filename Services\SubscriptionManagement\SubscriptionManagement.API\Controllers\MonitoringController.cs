using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    [Route("api/monitoring")]
    public class MonitoringController : BaseController
    {
        private readonly INotificationHistoryRepository _notificationHistoryRepository;
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly INotificationTrackingService _trackingService;

        public MonitoringController(
            INotificationHistoryRepository notificationHistoryRepository,
            ISubscriptionPaymentProofRepository paymentProofRepository,
            INotificationTrackingService trackingService)
        {
            _notificationHistoryRepository = notificationHistoryRepository;
            _paymentProofRepository = paymentProofRepository;
            _trackingService = trackingService;
        }

        /// <summary>
        /// Get payment verification metrics
        /// </summary>
        [HttpGet("payment-verification-metrics")]
        [ProducesResponseType(typeof(PaymentVerificationMetricsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPaymentVerificationMetrics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
                var to = toDate ?? DateTime.UtcNow;

                var metrics = new PaymentVerificationMetricsDto
                {
                    FromDate = from,
                    ToDate = to
                };

                // Get verification counts by status
                metrics.TotalUploads = await _paymentProofRepository.GetCountByDateRangeAsync(from, to);
                metrics.VerifiedCount = await _paymentProofRepository.GetCountByStatusAndDateRangeAsync(PaymentProofStatus.Verified, from, to);
                metrics.RejectedCount = await _paymentProofRepository.GetCountByStatusAndDateRangeAsync(PaymentProofStatus.Rejected, from, to);
                metrics.PendingCount = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.Pending);

                // Calculate rates
                if (metrics.TotalUploads > 0)
                {
                    metrics.VerificationRate = (double)metrics.VerifiedCount / metrics.TotalUploads * 100;
                    metrics.RejectionRate = (double)metrics.RejectedCount / metrics.TotalUploads * 100;
                }

                // Get average verification time
                metrics.AverageVerificationTimeHours = await _paymentProofRepository.GetAverageVerificationTimeAsync(from, to);

                // Get verification counts by payment method
                metrics.VerificationsByPaymentMethod = await _paymentProofRepository.GetVerificationCountsByPaymentMethodAsync(from, to);

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get notification delivery metrics
        /// </summary>
        [HttpGet("notification-delivery-metrics")]
        [ProducesResponseType(typeof(NotificationDeliveryMetricsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetNotificationDeliveryMetrics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? channel = null)
        {
            try
            {
                var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
                var to = toDate ?? DateTime.UtcNow;

                var analytics = await _trackingService.GetNotificationAnalyticsAsync(from, to, channel);

                var metrics = new NotificationDeliveryMetricsDto
                {
                    FromDate = from,
                    ToDate = to,
                    Channel = channel,
                    TotalNotifications = analytics.TotalNotifications,
                    DeliveryRate = analytics.DeliveryRate,
                    ReadRate = analytics.ReadRate,
                    ClickRate = analytics.ClickRate,
                    AverageDeliveryTimeMinutes = analytics.AverageDeliveryTime.TotalMinutes,
                    NotificationsByStatus = analytics.NotificationsByStatus,
                    NotificationsByChannel = analytics.NotificationsByChannel,
                    NotificationsByType = analytics.NotificationsByType.ToDictionary(kv => kv.Key.ToString(), kv => kv.Value)
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get conversion tracking metrics
        /// </summary>
        [HttpGet("conversion-metrics")]
        [ProducesResponseType(typeof(ConversionMetricsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetConversionMetrics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] NotificationType? notificationType = null)
        {
            try
            {
                var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
                var to = toDate ?? DateTime.UtcNow;

                var conversionAnalytics = await _trackingService.GetConversionAnalyticsAsync(from, to, notificationType);

                var metrics = new ConversionMetricsDto
                {
                    FromDate = from,
                    ToDate = to,
                    NotificationType = notificationType,
                    TotalReminders = conversionAnalytics.TotalReminders,
                    SubscriptionsRenewed = conversionAnalytics.SubscriptionsRenewed,
                    ConversionRate = conversionAnalytics.ConversionRate,
                    AverageTimeToConversionHours = conversionAnalytics.AverageTimeToConversion.TotalHours,
                    ConversionsByChannel = conversionAnalytics.ConversionsByChannel.ToDictionary(
                        kv => kv.Key,
                        kv => new ConversionByChannelDto
                        {
                            Channel = kv.Value.Channel,
                            TotalSent = kv.Value.TotalSent,
                            Conversions = kv.Value.Conversions,
                            ConversionRate = kv.Value.ConversionRate,
                            AverageTimeToConversionHours = kv.Value.AverageTimeToConversion.TotalHours
                        })
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get bulk notification processing metrics
        /// </summary>
        [HttpGet("bulk-processing-metrics")]
        [ProducesResponseType(typeof(BulkProcessingMetricsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBulkProcessingMetrics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
                var to = toDate ?? DateTime.UtcNow;

                // TODO: Implement bulk processing metrics collection
                var metrics = new BulkProcessingMetricsDto
                {
                    FromDate = from,
                    ToDate = to,
                    TotalBulkOperations = 0,
                    TotalNotificationsProcessed = 0,
                    AverageProcessingTimeSeconds = 0,
                    AverageBatchSize = 0,
                    SuccessRate = 0,
                    ProcessingRatePerSecond = 0
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get system performance metrics
        /// </summary>
        [HttpGet("performance-metrics")]
        [ProducesResponseType(typeof(PerformanceMetricsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPerformanceMetrics()
        {
            try
            {
                var metrics = new PerformanceMetricsDto
                {
                    Timestamp = DateTime.UtcNow,
                    // TODO: Implement actual performance metrics collection
                    AverageResponseTimeMs = 0,
                    RequestsPerSecond = 0,
                    ErrorRate = 0,
                    CacheHitRate = 0,
                    DatabaseConnectionPoolUsage = 0,
                    MemoryUsageMB = GC.GetTotalMemory(false) / 1024 / 1024,
                    ActiveConnections = 0
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get alerts and warnings
        /// </summary>
        [HttpGet("alerts")]
        [ProducesResponseType(typeof(List<SystemAlertDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSystemAlerts()
        {
            try
            {
                var alerts = new List<SystemAlertDto>();

                // Check for high number of pending payment proofs
                var pendingProofs = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.Pending);
                if (pendingProofs > 50)
                {
                    alerts.Add(new SystemAlertDto
                    {
                        Id = Guid.NewGuid(),
                        Type = "Warning",
                        Title = "High Number of Pending Payment Proofs",
                        Message = $"There are {pendingProofs} payment proofs pending verification",
                        Severity = "Medium",
                        CreatedAt = DateTime.UtcNow,
                        Category = "PaymentVerification"
                    });
                }

                // Check for failed notifications
                var failedNotifications = await _notificationHistoryRepository.GetCountByStatusAsync("Failed");
                if (failedNotifications > 20)
                {
                    alerts.Add(new SystemAlertDto
                    {
                        Id = Guid.NewGuid(),
                        Type = "Error",
                        Title = "High Number of Failed Notifications",
                        Message = $"There are {failedNotifications} failed notifications that may need attention",
                        Severity = "High",
                        CreatedAt = DateTime.UtcNow,
                        Category = "Notifications"
                    });
                }

                // Check for old pending notifications
                var oldPendingNotifications = await _trackingService.GetScheduledNotificationsReadyToSendAsync();
                var overdueCount = oldPendingNotifications.Count(n => n.ScheduledAt < DateTime.UtcNow.AddHours(-1));
                if (overdueCount > 0)
                {
                    alerts.Add(new SystemAlertDto
                    {
                        Id = Guid.NewGuid(),
                        Type = "Warning",
                        Title = "Overdue Scheduled Notifications",
                        Message = $"There are {overdueCount} notifications that are overdue for sending",
                        Severity = "Medium",
                        CreatedAt = DateTime.UtcNow,
                        Category = "Notifications"
                    });
                }

                return Ok(alerts);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }

    // Monitoring DTOs
    public class PaymentVerificationMetricsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalUploads { get; set; }
        public int VerifiedCount { get; set; }
        public int RejectedCount { get; set; }
        public int PendingCount { get; set; }
        public double VerificationRate { get; set; }
        public double RejectionRate { get; set; }
        public double AverageVerificationTimeHours { get; set; }
        public Dictionary<string, int> VerificationsByPaymentMethod { get; set; } = new();
    }

    public class NotificationDeliveryMetricsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string? Channel { get; set; }
        public int TotalNotifications { get; set; }
        public double DeliveryRate { get; set; }
        public double ReadRate { get; set; }
        public double ClickRate { get; set; }
        public double AverageDeliveryTimeMinutes { get; set; }
        public Dictionary<string, int> NotificationsByStatus { get; set; } = new();
        public Dictionary<string, int> NotificationsByChannel { get; set; } = new();
        public Dictionary<string, int> NotificationsByType { get; set; } = new();
    }

    public class ConversionMetricsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public NotificationType? NotificationType { get; set; }
        public int TotalReminders { get; set; }
        public int SubscriptionsRenewed { get; set; }
        public double ConversionRate { get; set; }
        public double AverageTimeToConversionHours { get; set; }
        public Dictionary<string, ConversionByChannelDto> ConversionsByChannel { get; set; } = new();
    }

    public class ConversionByChannelDto
    {
        public string Channel { get; set; } = string.Empty;
        public int TotalSent { get; set; }
        public int Conversions { get; set; }
        public double ConversionRate { get; set; }
        public double AverageTimeToConversionHours { get; set; }
    }

    public class BulkProcessingMetricsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalBulkOperations { get; set; }
        public int TotalNotificationsProcessed { get; set; }
        public double AverageProcessingTimeSeconds { get; set; }
        public double AverageBatchSize { get; set; }
        public double SuccessRate { get; set; }
        public double ProcessingRatePerSecond { get; set; }
    }

    public class PerformanceMetricsDto
    {
        public DateTime Timestamp { get; set; }
        public double AverageResponseTimeMs { get; set; }
        public double RequestsPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public double CacheHitRate { get; set; }
        public double DatabaseConnectionPoolUsage { get; set; }
        public long MemoryUsageMB { get; set; }
        public int ActiveConnections { get; set; }
    }

    public class SystemAlertDto
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }
}
