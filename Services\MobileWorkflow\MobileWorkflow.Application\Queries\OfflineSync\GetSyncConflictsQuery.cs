using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.OfflineSync
{
    public class GetSyncConflictsQuery : IRequest<List<ConflictResolutionDto>>
    {
        public Guid UserId { get; set; }
        public string? DataType { get; set; }
        public string? Status { get; set; } // Pending, Resolved, Failed
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public bool IncludeResolved { get; set; } = false;
    }
}
