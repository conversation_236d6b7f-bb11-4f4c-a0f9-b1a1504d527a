using Shared.Domain.Common;

namespace AnalyticsBIService.Infrastructure.Persistence;

/// <summary>
/// ETL execution entity for tracking ETL pipeline execution instances
/// </summary>
public class ETLExecution : BaseEntity
{
    public Guid PipelineId { get; private set; }
    public string Status { get; private set; } // Running, Completed, Failed, Cancelled
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public string? ErrorMessage { get; private set; }
    public long RecordsProcessed { get; private set; }
    public long RecordsSuccessful { get; private set; }
    public long RecordsFailed { get; private set; }
    public string? ExecutionLog { get; private set; }
    public string? ExecutionMetadata { get; private set; } // JSON serialized metadata
    public string? TriggeredBy { get; private set; } // Manual, Scheduled, API
    public Guid? TriggeredByUserId { get; private set; }
    public TimeSpan? Duration { get; private set; }

    // Private constructor for EF Core
    private ETLExecution()
    {
        Status = string.Empty;
    }

    public ETLExecution(
        Guid pipelineId,
        string? triggeredBy = null,
        Guid? triggeredByUserId = null)
    {
        PipelineId = pipelineId;
        Status = "Running";
        StartTime = DateTime.UtcNow;
        TriggeredBy = triggeredBy;
        TriggeredByUserId = triggeredByUserId;
        RecordsProcessed = 0;
        RecordsSuccessful = 0;
        RecordsFailed = 0;
    }

    public void Complete(long recordsProcessed, long recordsSuccessful, long recordsFailed, string? executionLog = null)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete execution with status: {Status}");

        Status = "Completed";
        EndTime = DateTime.UtcNow;
        Duration = EndTime.Value - StartTime;
        RecordsProcessed = recordsProcessed;
        RecordsSuccessful = recordsSuccessful;
        RecordsFailed = recordsFailed;
        ExecutionLog = executionLog;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Fail(string errorMessage, string? executionLog = null)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot fail execution with status: {Status}");

        Status = "Failed";
        EndTime = DateTime.UtcNow;
        Duration = EndTime.Value - StartTime;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        ExecutionLog = executionLog;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Cancel()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot cancel execution with status: {Status}");

        Status = "Cancelled";
        EndTime = DateTime.UtcNow;
        Duration = EndTime.Value - StartTime;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateProgress(long recordsProcessed, long recordsSuccessful, long recordsFailed)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot update progress for execution with status: {Status}");

        RecordsProcessed = recordsProcessed;
        RecordsSuccessful = recordsSuccessful;
        RecordsFailed = recordsFailed;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetExecutionMetadata(string metadata)
    {
        ExecutionMetadata = metadata;
        UpdatedAt = DateTime.UtcNow;
    }

    public bool IsCompleted()
    {
        return Status is "Completed" or "Failed" or "Cancelled";
    }

    public decimal GetSuccessRate()
    {
        return RecordsProcessed > 0 ? (decimal)RecordsSuccessful / RecordsProcessed * 100 : 0;
    }
}
