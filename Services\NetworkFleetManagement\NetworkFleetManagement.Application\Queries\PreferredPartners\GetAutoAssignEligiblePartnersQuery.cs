using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetAutoAssignEligiblePartnersQuery : IRequest<List<PreferredPartnerSummaryDto>>
{
    public Guid UserId { get; set; }
    public PreferredPartnerType PartnerType { get; set; }
    public string? Route { get; set; }
    public string? LoadType { get; set; }
    public int? Limit { get; set; }
}
