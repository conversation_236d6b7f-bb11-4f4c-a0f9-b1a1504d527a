using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class BarcodeScanning : ValueObject
{
    public string Format { get; private set; }
    public string Data { get; private set; }
    public DateTime ScannedAt { get; private set; }
    public bool IsValid { get; private set; }

    private BarcodeScanning() 
    { 
        Format = string.Empty;
        Data = string.Empty;
    }

    public BarcodeScanning(string format, string data, bool isValid = true)
    {
        Format = format ?? throw new ArgumentNullException(nameof(format));
        Data = data ?? throw new ArgumentNullException(nameof(data));
        IsValid = isValid;
        ScannedAt = DateTime.UtcNow;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Format;
        yield return Data;
        yield return ScannedAt;
        yield return IsValid;
    }
}