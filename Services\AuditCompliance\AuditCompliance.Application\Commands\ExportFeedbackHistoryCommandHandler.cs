using System.Text;
using System.Text.Json;
using MediatR;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Services;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Repositories;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Commands
{
    public class ExportFeedbackHistoryCommandHandler : IRequestHandler<ExportFeedbackHistoryCommand, FeedbackExportResponseDto>
    {
        private readonly IServiceProviderRatingRepository _ratingRepository;
        private readonly IFileStorageService _fileStorageService;
        private readonly IAuditService _auditService;
        private readonly ILogger<ExportFeedbackHistoryCommandHandler> _logger;

        public ExportFeedbackHistoryCommandHandler(
            IServiceProviderRatingRepository ratingRepository,
            IFileStorageService fileStorageService,
            IAuditService auditService,
            ILogger<ExportFeedbackHistoryCommandHandler> logger)
        {
            _ratingRepository = ratingRepository;
            _fileStorageService = fileStorageService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<FeedbackExportResponseDto> Handle(ExportFeedbackHistoryCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting feedback history export for user {UserId}", request.RequestedBy);

            try
            {
                // Log audit trail for export request
                await _auditService.LogDataExportAsync(
                    "FeedbackHistory", 
                    request.RequestedBy, 
                    request.RequestedByRole, 
                    request.IpAddress, 
                    request.UserAgent,
                    new Dictionary<string, object>
                    {
                        ["UserIds"] = request.UserIds.Count,
                        ["TransportCompanyIds"] = request.TransportCompanyIds.Count,
                        ["FromDate"] = request.FromDate?.ToString("yyyy-MM-dd"),
                        ["ToDate"] = request.ToDate?.ToString("yyyy-MM-dd"),
                        ["MinRating"] = request.MinRating,
                        ["MaxRating"] = request.MaxRating,
                        ["Status"] = request.Status?.ToString(),
                        ["AnonymizeData"] = request.AnonymizeData,
                        ["MaxRecords"] = request.MaxRecords
                    });

                // Get filtered feedback data
                var feedbackData = await GetFilteredFeedbackDataAsync(request);
                
                // Apply record limit if specified
                if (request.MaxRecords.HasValue && feedbackData.Count > request.MaxRecords.Value)
                {
                    feedbackData = feedbackData.Take(request.MaxRecords.Value).ToList();
                }

                // Anonymize data if requested
                if (request.AnonymizeData)
                {
                    AnonymizeFeedbackData(feedbackData);
                }

                // Generate export content based on format
                var exportContent = await GenerateExportContentAsync(feedbackData, request);
                
                // Generate unique filename
                var exportId = Guid.NewGuid().ToString("N")[..8];
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                var fileName = $"feedback_history_{timestamp}_{exportId}.{GetFileExtension(request.Format)}";
                
                // Save to file storage
                var fileUrl = await _fileStorageService.SaveExportFileAsync(fileName, exportContent, GetContentType(request.Format));
                
                // Create summary
                var summary = CreateFeedbackSummary(feedbackData);
                
                var response = new FeedbackExportResponseDto
                {
                    FileName = fileName,
                    FileUrl = fileUrl,
                    Format = request.Format,
                    RecordCount = feedbackData.Count,
                    UserCount = feedbackData.Select(f => f.ShipperId).Distinct().Count(),
                    TransportCompanyCount = feedbackData.Select(f => f.TransportCompanyId).Distinct().Count(),
                    FileSizeBytes = Encoding.UTF8.GetByteCount(exportContent),
                    GeneratedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(7), // Files expire after 7 days
                    ExportId = exportId,
                    Summary = summary,
                    DataAnonymized = request.AnonymizeData
                };

                _logger.LogInformation("Successfully generated feedback history export: {FileName} with {RecordCount} records", 
                    fileName, feedbackData.Count);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating feedback history export for user {UserId}", request.RequestedBy);
                throw;
            }
        }

        private async Task<List<FeedbackExportEntryDto>> GetFilteredFeedbackDataAsync(ExportFeedbackHistoryCommand request)
        {
            var filters = new Dictionary<string, object>();
            
            if (request.UserIds?.Any() == true)
                filters["UserIds"] = request.UserIds;
            
            if (request.TransportCompanyIds?.Any() == true)
                filters["TransportCompanyIds"] = request.TransportCompanyIds;
            
            if (request.FromDate.HasValue)
                filters["FromDate"] = request.FromDate.Value;
            
            if (request.ToDate.HasValue)
                filters["ToDate"] = request.ToDate.Value;
            
            if (request.MinRating.HasValue)
                filters["MinRating"] = request.MinRating.Value;
            
            if (request.MaxRating.HasValue)
                filters["MaxRating"] = request.MaxRating.Value;
            
            if (request.Status.HasValue)
                filters["Status"] = request.Status.Value;
            
            if (!request.IncludeAnonymous)
                filters["ExcludeAnonymous"] = true;

            var ratings = await _ratingRepository.GetRatingsForExportAsync(filters);
            
            return ratings.Select(MapToExportDto).ToList();
        }

        private FeedbackExportEntryDto MapToExportDto(ServiceProviderRating rating)
        {
            return new FeedbackExportEntryDto
            {
                Id = rating.Id,
                ShipperId = rating.ShipperId,
                ShipperName = rating.ShipperName,
                TransportCompanyId = rating.TransportCompanyId,
                TransportCompanyName = rating.TransportCompanyName,
                OrderId = rating.OrderId,
                OrderNumber = rating.OrderNumber,
                TripId = rating.TripId,
                TripNumber = rating.TripNumber,
                OverallRating = rating.OverallRating.NumericValue,
                Status = rating.Status,
                ReviewTitle = rating.ReviewTitle,
                ReviewComment = rating.ReviewComment,
                IsAnonymous = rating.IsAnonymous,
                ServiceCompletedAt = rating.ServiceCompletedAt,
                ReviewSubmittedAt = rating.ReviewSubmittedAt,
                CreatedAt = rating.CreatedAt,
                CategoryRatings = rating.CategoryRatings.Select(cr => new CategoryRatingExportDto
                {
                    Category = cr.Category,
                    CategoryName = cr.Category.ToString(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList(),
                ReportedIssues = rating.ReportedIssues.Select(ri => new ServiceIssueExportDto
                {
                    Id = ri.Id,
                    IssueType = ri.IssueType,
                    IssueTypeName = ri.IssueType.ToString(),
                    Priority = ri.Priority,
                    Description = ri.Description,
                    Evidence = ri.Evidence,
                    ReportedAt = ri.ReportedAt
                }).ToList()
            };
        }

        private void AnonymizeFeedbackData(List<FeedbackExportEntryDto> feedbackData)
        {
            var random = new Random();
            
            foreach (var feedback in feedbackData)
            {
                // Anonymize shipper information
                feedback.ShipperName = $"Shipper_{random.Next(1000, 9999)}";
                
                // Anonymize review content if not already anonymous
                if (!feedback.IsAnonymous)
                {
                    if (!string.IsNullOrWhiteSpace(feedback.ReviewTitle))
                    {
                        feedback.ReviewTitle = "[ANONYMIZED]";
                    }
                    
                    if (!string.IsNullOrWhiteSpace(feedback.ReviewComment))
                    {
                        feedback.ReviewComment = "[ANONYMIZED]";
                    }
                }
                
                // Anonymize category rating comments
                foreach (var categoryRating in feedback.CategoryRatings)
                {
                    if (!string.IsNullOrWhiteSpace(categoryRating.Comment))
                    {
                        categoryRating.Comment = "[ANONYMIZED]";
                    }
                }
                
                // Anonymize issue descriptions and evidence
                foreach (var issue in feedback.ReportedIssues)
                {
                    issue.Description = "[ANONYMIZED]";
                    if (!string.IsNullOrWhiteSpace(issue.Evidence))
                    {
                        issue.Evidence = "[ANONYMIZED]";
                    }
                }
            }
        }

        private async Task<string> GenerateExportContentAsync(List<FeedbackExportEntryDto> feedbackData, ExportFeedbackHistoryCommand request)
        {
            return request.Format switch
            {
                ExportFormat.CSV => await GenerateCsvContentAsync(feedbackData, request),
                ExportFormat.Excel => await GenerateExcelContentAsync(feedbackData, request),
                ExportFormat.PDF => await GeneratePdfContentAsync(feedbackData, request),
                ExportFormat.JSON => await GenerateJsonContentAsync(feedbackData, request),
                _ => throw new ArgumentException($"Unsupported export format: {request.Format}")
            };
        }

        private async Task<string> GenerateCsvContentAsync(List<FeedbackExportEntryDto> feedbackData, ExportFeedbackHistoryCommand request)
        {
            var csv = new StringBuilder();
            
            // Generate headers based on selected columns or default columns
            var columns = GetSelectedColumns(request);
            csv.AppendLine(string.Join(",", columns.Select(c => $"\"{c}\"")));
            
            // Generate data rows
            foreach (var feedback in feedbackData)
            {
                var row = new List<string>();
                
                foreach (var column in columns)
                {
                    var value = GetColumnValue(feedback, column);
                    row.Add($"\"{value?.ToString()?.Replace("\"", "\"\"") ?? ""}\"");
                }
                
                csv.AppendLine(string.Join(",", row));
            }
            
            return csv.ToString();
        }

        private async Task<string> GenerateExcelContentAsync(List<FeedbackExportEntryDto> feedbackData, ExportFeedbackHistoryCommand request)
        {
            // For now, return CSV format
            // In a real implementation, you would use EPPlus or ClosedXML
            return await GenerateCsvContentAsync(feedbackData, request);
        }

        private async Task<string> GeneratePdfContentAsync(List<FeedbackExportEntryDto> feedbackData, ExportFeedbackHistoryCommand request)
        {
            // For now, return a simple text representation
            // In a real implementation, you would use a PDF library like iTextSharp
            var content = new StringBuilder();
            content.AppendLine("FEEDBACK HISTORY REPORT");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            content.AppendLine($"Total Records: {feedbackData.Count}");
            content.AppendLine($"Data Anonymized: {request.AnonymizeData}");
            content.AppendLine(new string('=', 50));
            
            foreach (var feedback in feedbackData)
            {
                content.AppendLine($"Rating ID: {feedback.Id}");
                content.AppendLine($"Shipper: {feedback.ShipperName}");
                content.AppendLine($"Transport Company: {feedback.TransportCompanyName}");
                content.AppendLine($"Overall Rating: {feedback.OverallRating}/5");
                content.AppendLine($"Status: {feedback.Status}");
                content.AppendLine($"Service Completed: {feedback.ServiceCompletedAt:yyyy-MM-dd}");
                if (!string.IsNullOrWhiteSpace(feedback.ReviewComment))
                {
                    content.AppendLine($"Comment: {feedback.ReviewComment}");
                }
                content.AppendLine(new string('-', 30));
            }
            
            return content.ToString();
        }

        private async Task<string> GenerateJsonContentAsync(List<FeedbackExportEntryDto> feedbackData, ExportFeedbackHistoryCommand request)
        {
            return JsonSerializer.Serialize(feedbackData, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }

        private FeedbackExportSummaryDto CreateFeedbackSummary(List<FeedbackExportEntryDto> feedbackData)
        {
            if (!feedbackData.Any())
            {
                return new FeedbackExportSummaryDto();
            }

            return new FeedbackExportSummaryDto
            {
                TotalRatings = feedbackData.Count,
                AnonymousRatings = feedbackData.Count(f => f.IsAnonymous),
                AverageRating = feedbackData.Average(f => f.OverallRating),
                StatusBreakdown = feedbackData.GroupBy(f => f.Status).ToDictionary(g => g.Key, g => g.Count()),
                RatingDistribution = feedbackData.GroupBy(f => Math.Floor(f.OverallRating).ToString()).ToDictionary(g => g.Key, g => g.Count()),
                TotalIssuesReported = feedbackData.Sum(f => f.ReportedIssues.Count),
                EarliestRating = feedbackData.Min(f => f.CreatedAt),
                LatestRating = feedbackData.Max(f => f.CreatedAt)
            };
        }

        private List<string> GetSelectedColumns(ExportFeedbackHistoryCommand request)
        {
            if (request.SelectedColumns?.Any() == true)
            {
                return request.SelectedColumns;
            }
            
            // Default columns
            return new List<string>
            {
                "Id", "ShipperName", "TransportCompanyName", "OverallRating", "Status", 
                "ReviewTitle", "ReviewComment", "ServiceCompletedAt", "ReviewSubmittedAt", "CreatedAt"
            };
        }

        private object? GetColumnValue(FeedbackExportEntryDto feedback, string column)
        {
            return column switch
            {
                "Id" => feedback.Id,
                "ShipperId" => feedback.ShipperId,
                "ShipperName" => feedback.ShipperName,
                "TransportCompanyId" => feedback.TransportCompanyId,
                "TransportCompanyName" => feedback.TransportCompanyName,
                "OrderId" => feedback.OrderId,
                "TripId" => feedback.TripId,
                "OverallRating" => feedback.OverallRating,
                "Status" => feedback.Status.ToString(),
                "ReviewTitle" => feedback.ReviewTitle,
                "ReviewComment" => feedback.ReviewComment,
                "IsAnonymous" => feedback.IsAnonymous,
                "ServiceCompletedAt" => feedback.ServiceCompletedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                "ReviewSubmittedAt" => feedback.ReviewSubmittedAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                "CreatedAt" => feedback.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                "ReportedIssuesCount" => feedback.ReportedIssues.Count,
                "IssueTypes" => string.Join(", ", feedback.ReportedIssues.Select(i => i.IssueTypeName)),
                _ => null
            };
        }

        private static string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => "csv",
                ExportFormat.Excel => "xlsx",
                ExportFormat.PDF => "pdf",
                ExportFormat.JSON => "json",
                _ => "txt"
            };
        }

        private static string GetContentType(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.CSV => "text/csv",
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.PDF => "application/pdf",
                ExportFormat.JSON => "application/json",
                _ => "text/plain"
            };
        }
    }
}
