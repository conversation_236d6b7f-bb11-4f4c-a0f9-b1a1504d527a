using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetworkFleetManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPreferredPartners : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "preferred_partners",
                schema: "network_fleet",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PreferenceLevel = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActivatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeactivatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeactivationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    PreferredCommissionRate = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    PreferredServiceRate = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    IsExclusive = table.Column<bool>(type: "boolean", nullable: false),
                    ExclusivityExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AgreementNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AgreementStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AgreementEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PaymentTermsDays = table.Column<int>(type: "integer", nullable: true),
                    SpecialTerms = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AutoAssignEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AutoAssignThreshold = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 2, nullable: true),
                    PreferredRoutes = table.Column<string>(type: "jsonb", nullable: false),
                    PreferredLoadTypes = table.Column<string>(type: "jsonb", nullable: false),
                    ExcludedRoutes = table.Column<string>(type: "jsonb", nullable: false),
                    ExcludedLoadTypes = table.Column<string>(type: "jsonb", nullable: false),
                    NotifyOnNewOpportunities = table.Column<bool>(type: "boolean", nullable: false),
                    NotifyOnPerformanceChanges = table.Column<bool>(type: "boolean", nullable: false),
                    NotifyOnContractExpiry = table.Column<bool>(type: "boolean", nullable: false),
                    LastCollaborationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    overall_rating = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 2, nullable: false),
                    on_time_performance = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: false),
                    quality_score = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 2, nullable: false),
                    communication_rating = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 2, nullable: false),
                    total_orders = table.Column<int>(type: "integer", nullable: false),
                    completed_orders = table.Column<int>(type: "integer", nullable: false),
                    cancelled_orders = table.Column<int>(type: "integer", nullable: false),
                    average_response_time = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: false),
                    performance_last_updated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_preferred_partners", x => x.Id);
                });

            // Create indexes
            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_user_id",
                schema: "network_fleet",
                table: "preferred_partners",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_partner_id",
                schema: "network_fleet",
                table: "preferred_partners",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_user_partner_type",
                schema: "network_fleet",
                table: "preferred_partners",
                columns: new[] { "UserId", "PartnerType" });

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_user_status",
                schema: "network_fleet",
                table: "preferred_partners",
                columns: new[] { "UserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_user_preference_priority",
                schema: "network_fleet",
                table: "preferred_partners",
                columns: new[] { "UserId", "PreferenceLevel", "Priority" });

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_auto_assign",
                schema: "network_fleet",
                table: "preferred_partners",
                column: "AutoAssignEnabled");

            migrationBuilder.CreateIndex(
                name: "ix_preferred_partners_last_collaboration",
                schema: "network_fleet",
                table: "preferred_partners",
                column: "LastCollaborationDate");

            migrationBuilder.CreateIndex(
                name: "uq_preferred_partners_user_partner",
                schema: "network_fleet",
                table: "preferred_partners",
                columns: new[] { "UserId", "PartnerId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "preferred_partners",
                schema: "network_fleet");
        }
    }
}
