namespace MobileWorkflow.Application.DTOs;

public class BackgroundJobDto
{
    public Guid Id { get; set; }
    public string JobType { get; set; } = string.Empty;
    public string JobName { get; set; } = string.Empty;
    public string Queue { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int Priority { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Result { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public double? Duration { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorStackTrace { get; set; }
    public int RetryCount { get; set; }
    public int MaxRetries { get; set; }
    public DateTime? NextRetryAt { get; set; }
    public string? ParentJobId { get; set; }
    public List<string> ChildJobIds { get; set; } = new();
    public string? CreatedBy { get; set; }
    public string? WorkerId { get; set; }
    public Dictionary<string, object> Progress { get; set; } = new();
    public double ProgressPercentage { get; set; }
    public string EstimatedTimeRemaining { get; set; } = string.Empty;
    public double Age { get; set; }
    public double? ExecutionTime { get; set; }
    public bool IsReadyToRun { get; set; }
    public bool CanRetry { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsRunning { get; set; }
}

public class JobQueueDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int MaxConcurrency { get; set; }
    public int Priority { get; set; }
    public bool IsActive { get; set; }
    public bool IsPaused { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Statistics { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool CanProcessJobs { get; set; }
}

public class JobWorkerDto
{
    public Guid Id { get; set; }
    public string WorkerId { get; set; } = string.Empty;
    public string WorkerName { get; set; } = string.Empty;
    public string MachineName { get; set; } = string.Empty;
    public string ProcessId { get; set; } = string.Empty;
    public List<string> SupportedQueues { get; set; } = new();
    public List<string> SupportedJobTypes { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime LastHeartbeat { get; set; }
    public int MaxConcurrentJobs { get; set; }
    public int CurrentJobCount { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Statistics { get; set; } = new();
    public bool IsAvailable { get; set; }
    public bool IsOnline { get; set; }
}

public class JobExecutionDto
{
    public Guid Id { get; set; }
    public Guid JobId { get; set; }
    public int ExecutionNumber { get; set; }
    public string WorkerId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public double? Duration { get; set; }
    public Dictionary<string, object> Result { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string? ErrorStackTrace { get; set; }
    public Dictionary<string, object> ExecutionContext { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

public class JobProcessingStatsDto
{
    public int TotalJobs { get; set; }
    public int PendingJobs { get; set; }
    public int RunningJobs { get; set; }
    public int CompletedJobs { get; set; }
    public int FailedJobs { get; set; }
    public int CancelledJobs { get; set; }
    public int ScheduledJobs { get; set; }
    public int TotalWorkers { get; set; }
    public int ActiveWorkers { get; set; }
    public int BusyWorkers { get; set; }
    public int OfflineWorkers { get; set; }
    public int TotalQueues { get; set; }
    public int ActiveQueues { get; set; }
    public int PausedQueues { get; set; }
    public double AverageExecutionTime { get; set; }
    public Dictionary<string, int> JobsByQueue { get; set; } = new();
    public Dictionary<string, int> JobsByStatus { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

// Request DTOs
public class EnqueueJobRequest
{
    public string JobType { get; set; } = string.Empty;
    public string JobName { get; set; } = string.Empty;
    public string Queue { get; set; } = "default";
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Priority { get; set; } = 0;
    public Guid? ParentJobId { get; set; }
    public string? CreatedBy { get; set; }
}

public class ScheduleJobRequest
{
    public string JobType { get; set; } = string.Empty;
    public string JobName { get; set; } = string.Empty;
    public string Queue { get; set; } = "default";
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Priority { get; set; } = 0;
    public DateTime ScheduledAt { get; set; }
    public Guid? ParentJobId { get; set; }
    public string? CreatedBy { get; set; }
}

public class CreateJobQueueRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int MaxConcurrency { get; set; } = 1;
    public int Priority { get; set; } = 0;
    public Dictionary<string, object>? Configuration { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class RegisterWorkerRequest
{
    public string WorkerId { get; set; } = string.Empty;
    public string WorkerName { get; set; } = string.Empty;
    public string MachineName { get; set; } = string.Empty;
    public string ProcessId { get; set; } = string.Empty;
    public List<string> SupportedQueues { get; set; } = new();
    public List<string> SupportedJobTypes { get; set; } = new();
    public int MaxConcurrentJobs { get; set; } = 1;
}

public class UpdateJobProgressRequest
{
    public Dictionary<string, object> Progress { get; set; } = new();
}

public class CompleteJobRequest
{
    public Dictionary<string, object> Result { get; set; } = new();
    public Dictionary<string, object>? PerformanceMetrics { get; set; }
}

public class FailJobRequest
{
    public string ErrorMessage { get; set; } = string.Empty;
    public string? ErrorStackTrace { get; set; }
    public Dictionary<string, object>? PerformanceMetrics { get; set; }
}

public class JobScheduleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string JobType { get; set; } = string.Empty;
    public string Queue { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string CronExpression { get; set; } = string.Empty;
    public DateTime? NextRun { get; set; }
    public DateTime? LastRun { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class JobBatchDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Guid> JobIds { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int TotalJobs { get; set; }
    public int CompletedJobs { get; set; }
    public int FailedJobs { get; set; }
    public double ProgressPercentage { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class JobPipelineDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<JobPipelineStageDto> Stages { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class JobPipelineStageDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Order { get; set; }
    public string JobType { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public Guid? JobId { get; set; }
    public bool IsParallel { get; set; }
    public Dictionary<string, object> Conditions { get; set; } = new();
}

public class JobRetryPolicyDto
{
    public string Name { get; set; } = string.Empty;
    public int MaxRetries { get; set; }
    public TimeSpan InitialDelay { get; set; }
    public TimeSpan MaxDelay { get; set; }
    public double BackoffMultiplier { get; set; }
    public List<string> RetryableExceptions { get; set; } = new();
    public List<string> NonRetryableExceptions { get; set; } = new();
}

public class JobMonitoringDto
{
    public Guid JobId { get; set; }
    public string JobName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public double? Duration { get; set; }
    public double ProgressPercentage { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
    public List<string> Alerts { get; set; } = new();
}

public class JobPerformanceDto
{
    public string JobType { get; set; } = string.Empty;
    public string Queue { get; set; } = string.Empty;
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public double SuccessRate { get; set; }
    public double AverageExecutionTime { get; set; }
    public double MinExecutionTime { get; set; }
    public double MaxExecutionTime { get; set; }
    public double P95ExecutionTime { get; set; }
    public double P99ExecutionTime { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class JobHealthCheckDto
{
    public string Component { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Message { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
    public DateTime CheckedAt { get; set; }
    public TimeSpan ResponseTime { get; set; }
}
