using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using Shared.Messaging;
using Shared.Domain.Common;
using TripManagement.Domain.Entities;
using Shared.Domain.Common;
using TripManagement.Domain.Repositories;
using Shared.Domain.Common;
using TripManagement.Application.Services;
using Shared.Domain.Common;

namespace TripManagement.Application.Commands.SubmitTripFeedback;

public class SubmitTripFeedbackCommandHandler : IRequestHandler<SubmitTripFeedbackCommand, SubmitTripFeedbackResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly ITripFeedbackRepository _feedbackRepository;
    private readonly IFeedbackAnalysisService _analysisService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<SubmitTripFeedbackCommandHandler> _logger;

    public SubmitTripFeedbackCommandHandler(
        ITripRepository tripRepository,
        ITripFeedbackRepository feedbackRepository,
        IFeedbackAnalysisService analysisService,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<SubmitTripFeedbackCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _feedbackRepository = feedbackRepository;
        _analysisService = analysisService;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<SubmitTripFeedbackResponse> Handle(SubmitTripFeedbackCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Submitting trip feedback for trip {TripId} by reviewer {ReviewerId}", 
            request.TripId, request.ReviewerId);

        try
        {
            // Get trip details
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                return new SubmitTripFeedbackResponse
                {
                    Success = false,
                    Message = $"Trip {request.TripId} not found"
                };
            }

            // Validate trip is completed
            if (trip.Status != Domain.Enums.TripStatus.Completed)
            {
                return new SubmitTripFeedbackResponse
                {
                    Success = false,
                    Message = "Feedback can only be submitted for completed trips"
                };
            }

            // Check if feedback already exists
            var existingFeedback = await _feedbackRepository.GetByTripAndReviewerAsync(
                request.TripId, request.ReviewerId, cancellationToken);
            
            if (existingFeedback != null)
            {
                return new SubmitTripFeedbackResponse
                {
                    Success = false,
                    Message = "Feedback has already been submitted for this trip"
                };
            }

            // Create feedback entity
            var feedback = new TripFeedback(
                request.TripId,
                request.ReviewerId,
                request.ReviewerRole,
                request.RevieweeId,
                request.RevieweeRole,
                request.OverallRating,
                request.Comments,
                request.FeedbackType,
                request.IsAnonymous);

            // Update detailed ratings
            feedback.UpdateDetailedRatings(
                request.ServiceQualityRating,
                request.TimelinessRating,
                request.CommunicationRating,
                request.ProfessionalismRating,
                request.VehicleConditionRating,
                request.CargoHandlingRating);

            // Add additional feedback data
            feedback.AddPositiveAspects(request.PositiveAspects);
            feedback.AddImprovementAreas(request.ImprovementAreas);
            feedback.AddTags(request.Tags);

            // Set trip context
            feedback.SetTripContext(
                trip.StartedAt ?? trip.CreatedAt,
                trip.CompletedAt ?? DateTime.UtcNow,
                trip.Route?.DistanceKm ?? 0,
                trip.IsDelayed(),
                trip.GetDelayDuration());

            // Add metadata
            foreach (var metadata in request.Metadata)
            {
                feedback.AddMetadata(metadata.Key, metadata.Value);
            }

            // Save feedback
            await _feedbackRepository.AddAsync(feedback);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Analyze feedback
            var analysis = await _analysisService.AnalyzeFeedbackAsync(feedback, cancellationToken);

            // Generate alerts and recommendations
            var alerts = GenerateAlerts(feedback, analysis);
            var recommendations = GenerateRecommendations(feedback, analysis);

            // Publish integration events
            await PublishFeedbackEvents(feedback, analysis, cancellationToken);

            // Send notifications if requested
            if (request.NotifyReviewee && !request.IsAnonymous)
            {
                await SendFeedbackNotifications(feedback, trip, cancellationToken);
            }

            // Handle follow-up requests
            if (request.RequestFollowUp)
            {
                await ScheduleFollowUp(feedback, cancellationToken);
            }

            var response = new SubmitTripFeedbackResponse
            {
                Success = true,
                Message = "Trip feedback submitted successfully",
                FeedbackId = feedback.Id,
                Analysis = new FeedbackAnalysisResult
                {
                    IsPositiveFeedback = analysis.IsPositive,
                    RequiresAttention = analysis.RequiresAttention,
                    HasRedFlags = analysis.HasRedFlags,
                    IdentifiedIssues = analysis.Issues,
                    PositiveHighlights = analysis.Highlights,
                    SentimentScore = analysis.SentimentScore,
                    SentimentCategory = analysis.SentimentCategory,
                    SuggestedActions = analysis.SuggestedActions
                },
                Alerts = alerts,
                Recommendations = recommendations,
                SubmittedAt = feedback.SubmittedAt
            };

            _logger.LogInformation("Trip feedback submitted successfully. FeedbackId: {FeedbackId}", feedback.Id);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting trip feedback for trip {TripId}", request.TripId);
            return new SubmitTripFeedbackResponse
            {
                Success = false,
                Message = $"Error submitting feedback: {ex.Message}"
            };
        }
    }

    private List<string> GenerateAlerts(TripFeedback feedback, FeedbackAnalysis analysis)
    {
        var alerts = new List<string>();

        if (feedback.IsLowRating())
        {
            alerts.Add("Low rating feedback requires immediate attention");
        }

        if (analysis.HasRedFlags)
        {
            alerts.Add("Feedback contains red flag indicators");
        }

        if (feedback.WasDelayed && feedback.TimelinessRating <= 2)
        {
            alerts.Add("Poor timeliness rating on delayed trip");
        }

        if (feedback.VehicleConditionRating <= 2)
        {
            alerts.Add("Vehicle condition concerns reported");
        }

        return alerts;
    }

    private List<string> GenerateRecommendations(TripFeedback feedback, FeedbackAnalysis analysis)
    {
        var recommendations = new List<string>();

        if (feedback.CommunicationRating <= 3)
        {
            recommendations.Add("Consider communication training for driver");
        }

        if (feedback.VehicleConditionRating <= 3)
        {
            recommendations.Add("Schedule vehicle inspection and maintenance");
        }

        if (feedback.CargoHandlingRating <= 3)
        {
            recommendations.Add("Review cargo handling procedures with driver");
        }

        if (feedback.IsHighRating())
        {
            recommendations.Add("Consider recognizing excellent performance");
        }

        return recommendations;
    }

    private async Task PublishFeedbackEvents(TripFeedback feedback, FeedbackAnalysis analysis, CancellationToken cancellationToken)
    {
        await _messageBroker.PublishAsync("trip.feedback.submitted", new
        {
            FeedbackId = feedback.Id,
            TripId = feedback.TripId,
            ReviewerId = feedback.ReviewerId,
            ReviewerRole = feedback.ReviewerRole,
            RevieweeId = feedback.RevieweeId,
            RevieweeRole = feedback.RevieweeRole,
            OverallRating = feedback.OverallRating,
            IsPositive = analysis.IsPositive,
            RequiresAttention = analysis.RequiresAttention,
            HasRedFlags = analysis.HasRedFlags,
            SubmittedAt = feedback.SubmittedAt
        }, cancellationToken);

        if (analysis.RequiresAttention)
        {
            await _messageBroker.PublishAsync("trip.feedback.attention_required", new
            {
                FeedbackId = feedback.Id,
                TripId = feedback.TripId,
                RevieweeId = feedback.RevieweeId,
                RevieweeRole = feedback.RevieweeRole,
                Issues = analysis.Issues,
                SentimentScore = analysis.SentimentScore,
                Priority = analysis.HasRedFlags ? "High" : "Medium"
            }, cancellationToken);
        }
    }

    private async Task SendFeedbackNotifications(TripFeedback feedback, Trip trip, CancellationToken cancellationToken)
    {
        await _messageBroker.PublishAsync("communication.send.notification", new
        {
            MessageType = "TripFeedbackReceived",
            Subject = $"Feedback Received for Trip {trip.TripNumber}",
            Content = $"You have received feedback for trip {trip.TripNumber}. Overall rating: {feedback.OverallRating}/5",
            Priority = feedback.IsLowRating() ? "High" : "Medium",
            RelatedEntityId = feedback.TripId,
            RelatedEntityType = "Trip",
            Recipients = new[]
            {
                new { UserId = feedback.RevieweeId, UserType = feedback.RevieweeRole }
            },
            Tags = new[] { "feedback", "trip", "rating" },
            Metadata = new
            {
                FeedbackId = feedback.Id,
                TripId = feedback.TripId,
                OverallRating = feedback.OverallRating,
                IsPositive = feedback.IsHighRating()
            }
        }, cancellationToken);
    }

    private async Task ScheduleFollowUp(TripFeedback feedback, CancellationToken cancellationToken)
    {
        // Schedule follow-up feedback request after 7 days
        await _messageBroker.PublishAsync("trip.feedback.followup_scheduled", new
        {
            FeedbackId = feedback.Id,
            TripId = feedback.TripId,
            ReviewerId = feedback.ReviewerId,
            ScheduledFor = DateTime.UtcNow.AddDays(7),
            FollowUpType = "PostFeedback"
        }, cancellationToken);
    }
}

// Supporting interfaces and classes that might need to be implemented
public interface ITripFeedbackRepository
{
    Task<TripFeedback?> GetByTripAndReviewerAsync(Guid tripId, Guid reviewerId, CancellationToken cancellationToken = default);
    Task AddAsync(TripFeedback feedback);
    Task UpdateAsync(TripFeedback feedback);
    Task<List<TripFeedback>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<TripFeedback>> GetByRevieweeAsync(Guid revieweeId, CancellationToken cancellationToken = default);
}

public interface IFeedbackAnalysisService
{
    Task<FeedbackAnalysis> AnalyzeFeedbackAsync(TripFeedback feedback, CancellationToken cancellationToken = default);
}

public class FeedbackAnalysis
{
    public bool IsPositive { get; set; }
    public bool RequiresAttention { get; set; }
    public bool HasRedFlags { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Highlights { get; set; } = new();
    public decimal SentimentScore { get; set; }
    public string SentimentCategory { get; set; } = string.Empty;
    public List<string> SuggestedActions { get; set; } = new();
}

