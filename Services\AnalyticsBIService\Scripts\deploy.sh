#!/bin/bash

# Analytics & BI Service Deployment Script
# This script handles deployment to different environments

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_NAME="analytics-bi-service"
DOCKER_IMAGE="tli/analytics-bi-service"

# Default values
ENVIRONMENT="development"
VERSION="latest"
REGISTRY=""
NAMESPACE="tli-analytics"
KUBECONFIG=""
DOCKER_COMPOSE_FILE="docker-compose.yml"
BUILD_ARGS=""
PUSH_IMAGE="false"
DEPLOY_K8S="false"
SKIP_BUILD="false"
SKIP_TESTS="false"
VERBOSE="false"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Analytics & BI Service Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV       Target environment (development, staging, production) [default: development]
    -v, --version VERSION       Image version/tag [default: latest]
    -r, --registry REGISTRY     Docker registry URL
    -n, --namespace NAMESPACE   Kubernetes namespace [default: tli-analytics]
    -k, --kubeconfig PATH       Path to kubeconfig file
    -f, --compose-file FILE     Docker compose file [default: docker-compose.yml]
    -b, --build-args ARGS       Additional Docker build arguments
    -p, --push                  Push image to registry
    -d, --deploy-k8s            Deploy to Kubernetes
    -s, --skip-build            Skip Docker build
    -t, --skip-tests            Skip running tests
    --verbose                   Enable verbose output
    -h, --help                  Show this help message

EXAMPLES:
    # Development deployment with Docker Compose
    $0 -e development

    # Production deployment to Kubernetes
    $0 -e production -v 1.0.0 -r registry.tli-platform.com -p -d

    # Staging deployment with custom namespace
    $0 -e staging -n tli-analytics-staging -d

    # Build and push image only
    $0 -v 1.2.3 -r registry.tli-platform.com -p -s

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -k|--kubeconfig)
                KUBECONFIG="$2"
                shift 2
                ;;
            -f|--compose-file)
                DOCKER_COMPOSE_FILE="$2"
                shift 2
                ;;
            -b|--build-args)
                BUILD_ARGS="$2"
                shift 2
                ;;
            -p|--push)
                PUSH_IMAGE="true"
                shift
                ;;
            -d|--deploy-k8s)
                DEPLOY_K8S="true"
                shift
                ;;
            -s|--skip-build)
                SKIP_BUILD="true"
                shift
                ;;
            -t|--skip-tests)
                SKIP_TESTS="true"
                shift
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Set environment-specific configurations
configure_environment() {
    case $ENVIRONMENT in
        development)
            DOCKER_COMPOSE_FILE="docker-compose.yml"
            ;;
        staging)
            DOCKER_COMPOSE_FILE="docker-compose.staging.yml"
            ;;
        production)
            DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
            ;;
    esac

    if [[ -n "$REGISTRY" ]]; then
        DOCKER_IMAGE="$REGISTRY/$SERVICE_NAME"
    fi

    log_info "Using Docker image: $DOCKER_IMAGE:$VERSION"
    log_info "Using compose file: $DOCKER_COMPOSE_FILE"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi

    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi

    # Check kubectl if deploying to Kubernetes
    if [[ "$DEPLOY_K8S" == "true" ]]; then
        if ! command -v kubectl &> /dev/null; then
            log_error "kubectl is not installed or not in PATH"
            exit 1
        fi

        # Set kubeconfig if provided
        if [[ -n "$KUBECONFIG" ]]; then
            export KUBECONFIG="$KUBECONFIG"
            log_info "Using kubeconfig: $KUBECONFIG"
        fi

        # Test kubectl connectivity
        if ! kubectl cluster-info &> /dev/null; then
            log_error "Cannot connect to Kubernetes cluster"
            exit 1
        fi
    fi

    # Check .NET SDK for building
    if [[ "$SKIP_BUILD" == "false" ]]; then
        if ! command -v dotnet &> /dev/null; then
            log_error ".NET SDK is not installed or not in PATH"
            exit 1
        fi
    fi

    log_success "Prerequisites check passed"
}

# Run tests
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "Skipping tests"
        return 0
    fi

    log_info "Running tests..."
    cd "$PROJECT_ROOT"

    # Unit tests
    log_info "Running unit tests..."
    dotnet test AnalyticsBIService.Tests/UnitTests/ --configuration Release --logger "console;verbosity=minimal"

    # Integration tests
    log_info "Running integration tests..."
    dotnet test AnalyticsBIService.Tests/IntegrationTests/ --configuration Release --logger "console;verbosity=minimal"

    log_success "All tests passed"
}

# Build Docker image
build_image() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_warning "Skipping Docker build"
        return 0
    fi

    log_info "Building Docker image..."
    cd "$PROJECT_ROOT"

    local build_cmd="docker build -t $DOCKER_IMAGE:$VERSION"
    
    if [[ -n "$BUILD_ARGS" ]]; then
        build_cmd="$build_cmd $BUILD_ARGS"
    fi

    build_cmd="$build_cmd ."

    if [[ "$VERBOSE" == "true" ]]; then
        log_info "Build command: $build_cmd"
    fi

    eval "$build_cmd"

    # Tag as latest if not already
    if [[ "$VERSION" != "latest" ]]; then
        docker tag "$DOCKER_IMAGE:$VERSION" "$DOCKER_IMAGE:latest"
    fi

    log_success "Docker image built successfully"
}

# Push Docker image
push_image() {
    if [[ "$PUSH_IMAGE" == "false" ]]; then
        log_info "Skipping image push"
        return 0
    fi

    if [[ -z "$REGISTRY" ]]; then
        log_error "Registry not specified. Use -r/--registry option"
        exit 1
    fi

    log_info "Pushing Docker image to registry..."

    docker push "$DOCKER_IMAGE:$VERSION"
    
    if [[ "$VERSION" != "latest" ]]; then
        docker push "$DOCKER_IMAGE:latest"
    fi

    log_success "Docker image pushed successfully"
}

# Deploy with Docker Compose
deploy_compose() {
    if [[ "$DEPLOY_K8S" == "true" ]]; then
        log_info "Skipping Docker Compose deployment (Kubernetes deployment enabled)"
        return 0
    fi

    log_info "Deploying with Docker Compose..."
    cd "$PROJECT_ROOT"

    if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
        log_error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
        exit 1
    fi

    # Set environment variables
    export VERSION="$VERSION"
    export DOCKER_IMAGE="$DOCKER_IMAGE"

    # Deploy
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d

    log_success "Docker Compose deployment completed"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    if [[ "$DEPLOY_K8S" == "false" ]]; then
        log_info "Skipping Kubernetes deployment"
        return 0
    fi

    log_info "Deploying to Kubernetes..."
    cd "$PROJECT_ROOT"

    # Create namespace if it doesn't exist
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

    # Apply Kubernetes manifests
    log_info "Applying Kubernetes manifests..."
    
    # Apply in order
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/timescaledb-deployment.yaml
    kubectl apply -f k8s/redis-deployment.yaml
    kubectl apply -f k8s/analytics-api-deployment.yaml
    kubectl apply -f k8s/ingress.yaml

    # Wait for deployment to be ready
    log_info "Waiting for deployment to be ready..."
    kubectl rollout status deployment/analytics-api -n "$NAMESPACE" --timeout=300s

    # Show deployment status
    kubectl get pods -n "$NAMESPACE"
    kubectl get services -n "$NAMESPACE"

    log_success "Kubernetes deployment completed"
}

# Health check
health_check() {
    log_info "Performing health check..."

    local health_url=""
    
    if [[ "$DEPLOY_K8S" == "true" ]]; then
        # Get service endpoint
        local service_ip=$(kubectl get service analytics-api-service -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
        health_url="http://$service_ip/api/analytics/health"
    else
        # Docker Compose deployment
        health_url="http://localhost:5004/api/analytics/health"
    fi

    log_info "Health check URL: $health_url"

    # Wait for service to be ready
    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi

        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done

    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add cleanup logic here if needed
}

# Main deployment function
main() {
    log_info "Starting Analytics & BI Service deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Timestamp: $(date)"

    # Set trap for cleanup
    trap cleanup EXIT

    # Validate and configure
    validate_environment
    configure_environment
    check_prerequisites

    # Build and test
    run_tests
    build_image
    push_image

    # Deploy
    deploy_compose
    deploy_kubernetes

    # Verify deployment
    health_check

    log_success "Deployment completed successfully!"
    log_info "Service is now available and healthy"
}

# Parse arguments and run main function
parse_args "$@"
main
