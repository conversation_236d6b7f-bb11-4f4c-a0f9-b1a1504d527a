using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class PerformanceMetric : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid? SessionId { get; private set; }
    public string MetricType { get; private set; } // PageLoad, ApiCall, DatabaseQuery, FileUpload, etc.
    public string MetricName { get; private set; }
    public string Category { get; private set; } // Performance, Network, Database, UI, etc.
    public double Value { get; private set; }
    public string Unit { get; private set; } // ms, seconds, bytes, count, percentage
    public DateTime Timestamp { get; private set; }
    public string Platform { get; private set; }
    public string AppVersion { get; private set; }
    public string? ScreenName { get; private set; }
    public string? OperationName { get; private set; }
    public Dictionary<string, object> Tags { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private PerformanceMetric() { } // EF Core

    public PerformanceMetric(
        Guid userId,
        string metricType,
        string metricName,
        string category,
        double value,
        string unit,
        string platform,
        string appVersion,
        Guid? sessionId = null)
    {
        UserId = userId;
        SessionId = sessionId;
        MetricType = metricType ?? throw new ArgumentNullException(nameof(metricType));
        MetricName = metricName ?? throw new ArgumentNullException(nameof(metricName));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Value = value;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));

        Timestamp = DateTime.UtcNow;
        Tags = new Dictionary<string, object>();
        Context = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new PerformanceMetricRecordedEvent(Id, UserId, MetricType, MetricName, Value, Unit));
    }

    public void SetScreenName(string screenName)
    {
        ScreenName = screenName;
    }

    public void SetOperationName(string operationName)
    {
        OperationName = operationName;
    }

    public void AddTag(string key, object value)
    {
        Tags[key] = value;
    }

    public void AddContext(string key, object value)
    {
        Context[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsSlowPerformance()
    {
        return MetricType.ToLowerInvariant() switch
        {
            "pageload" => Value > 3000, // > 3 seconds
            "apicall" => Value > 5000, // > 5 seconds
            "databasequery" => Value > 1000, // > 1 second
            "fileupload" => Value > 10000, // > 10 seconds
            _ => false
        };
    }

    public string GetPerformanceGrade()
    {
        if (IsSlowPerformance())
            return "Poor";

        return MetricType.ToLowerInvariant() switch
        {
            "pageload" when Value <= 1000 => "Excellent",
            "pageload" when Value <= 2000 => "Good",
            "pageload" => "Fair",
            "apicall" when Value <= 1000 => "Excellent",
            "apicall" when Value <= 3000 => "Good",
            "apicall" => "Fair",
            _ => "Good"
        };
    }

    public T? GetTag<T>(string key, T? defaultValue = default)
    {
        if (Tags.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetContext<T>(string key, T? defaultValue = default)
    {
        if (Context.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class CrashReport : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid? SessionId { get; private set; }
    public string CrashType { get; private set; } // Exception, ANR, OutOfMemory, etc.
    public string ErrorMessage { get; private set; }
    public string? StackTrace { get; private set; }
    public string Platform { get; private set; }
    public string AppVersion { get; private set; }
    public string DeviceInfo { get; private set; }
    public string? ScreenName { get; private set; }
    public DateTime Timestamp { get; private set; }
    public bool IsFatal { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public Dictionary<string, object> UserActions { get; private set; } // Actions leading to crash
    public Dictionary<string, object> SystemInfo { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private CrashReport() { } // EF Core

    public CrashReport(
        Guid userId,
        string crashType,
        string errorMessage,
        string platform,
        string appVersion,
        string deviceInfo,
        bool isFatal = true,
        Guid? sessionId = null)
    {
        UserId = userId;
        SessionId = sessionId;
        CrashType = crashType ?? throw new ArgumentNullException(nameof(crashType));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        IsFatal = isFatal;

        Timestamp = DateTime.UtcNow;
        Context = new Dictionary<string, object>();
        UserActions = new Dictionary<string, object>();
        SystemInfo = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new CrashReportCreatedEvent(Id, UserId, CrashType, IsFatal, Platform, AppVersion));
    }

    public void SetStackTrace(string stackTrace)
    {
        StackTrace = stackTrace;
    }

    public void SetScreenName(string screenName)
    {
        ScreenName = screenName;
    }

    public void AddContext(string key, object value)
    {
        Context[key] = value;
    }

    public void AddUserAction(string action, DateTime timestamp, Dictionary<string, object>? details = null)
    {
        var actionKey = $"{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
        UserActions[actionKey] = new
        {
            Action = action,
            Timestamp = timestamp,
            Details = details ?? new Dictionary<string, object>()
        };
    }

    public void AddSystemInfo(string key, object value)
    {
        SystemInfo[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public string GetSeverityLevel()
    {
        if (IsFatal)
            return "Critical";

        return CrashType.ToLowerInvariant() switch
        {
            "outofmemory" => "High",
            "anr" => "High",
            "exception" => "Medium",
            "warning" => "Low",
            _ => "Medium"
        };
    }

    public bool IsRecurringCrash(List<CrashReport> recentCrashes)
    {
        return recentCrashes.Count(c =>
            c.ErrorMessage == ErrorMessage &&
            c.CrashType == CrashType &&
            c.Timestamp > DateTime.UtcNow.AddHours(-24)) > 3;
    }

    public Dictionary<string, object> GetCrashFingerprint()
    {
        return new Dictionary<string, object>
        {
            ["crashType"] = CrashType,
            ["errorMessage"] = ErrorMessage,
            ["platform"] = Platform,
            ["appVersion"] = AppVersion,
            ["stackTraceHash"] = StackTrace?.GetHashCode() ?? 0
        };
    }
}

public class UserBehaviorPattern : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string PatternType { get; private set; } // Navigation, Usage, Engagement, etc.
    public string PatternName { get; private set; }
    public Dictionary<string, object> PatternData { get; private set; }
    public DateTime DetectedAt { get; private set; }
    public DateTime? LastUpdated { get; private set; }
    public int Frequency { get; private set; }
    public double Confidence { get; private set; } // 0-1 confidence score
    public string Platform { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private UserBehaviorPattern() { } // EF Core

    public UserBehaviorPattern(
        Guid userId,
        string patternType,
        string patternName,
        Dictionary<string, object> patternData,
        string platform,
        double confidence = 0.5)
    {
        UserId = userId;
        PatternType = patternType ?? throw new ArgumentNullException(nameof(patternType));
        PatternName = patternName ?? throw new ArgumentNullException(nameof(patternName));
        PatternData = patternData ?? throw new ArgumentNullException(nameof(patternData));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Confidence = Math.Max(0, Math.Min(1, confidence));

        DetectedAt = DateTime.UtcNow;
        Frequency = 1;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new UserBehaviorPatternDetectedEvent(Id, UserId, PatternType, PatternName, Confidence));
    }

    public void UpdatePattern(Dictionary<string, object> newPatternData, double newConfidence)
    {
        PatternData = newPatternData ?? throw new ArgumentNullException(nameof(newPatternData));
        Confidence = Math.Max(0, Math.Min(1, newConfidence));
        LastUpdated = DateTime.UtcNow;
        Frequency++;

        AddDomainEvent(new UserBehaviorPatternUpdatedEvent(Id, UserId, PatternName, Frequency, Confidence));
    }

    public void IncreaseConfidence(double increment)
    {
        Confidence = Math.Min(1, Confidence + increment);
        LastUpdated = DateTime.UtcNow;
    }

    public void DecreaseConfidence(double decrement)
    {
        Confidence = Math.Max(0, Confidence - decrement);
        LastUpdated = DateTime.UtcNow;
    }

    public bool IsHighConfidence()
    {
        return Confidence >= 0.8;
    }

    public bool IsFrequentPattern()
    {
        return Frequency >= 5;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetPatternData<T>(string key, T? defaultValue = default)
    {
        if (PatternData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


