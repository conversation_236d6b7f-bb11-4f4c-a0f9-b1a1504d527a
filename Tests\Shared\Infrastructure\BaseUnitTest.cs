using AutoFixture;
using AutoFixture.Xunit2;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Tests.Shared.Infrastructure;

/// <summary>
/// Base class for unit tests that provides common testing utilities
/// </summary>
public abstract class BaseUnitTest
{
    protected readonly IFixture Fixture;
    protected readonly Mock<ILogger> MockLogger;

    protected BaseUnitTest()
    {
        Fixture = new Fixture();
        MockLogger = new Mock<ILogger>();
        
        // Configure AutoFixture to handle common scenarios
        ConfigureFixture();
    }

    private void ConfigureFixture()
    {
        // Customize AutoFixture behavior
        Fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => Fixture.Behaviors.Remove(b));
        Fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        
        // Configure string generation
        Fixture.Customize<string>(composer => composer.FromFactory(() => Guid.NewGuid().ToString()));
    }

    /// <summary>
    /// Create a mock of the specified type
    /// </summary>
    protected Mock<T> CreateMock<T>() where T : class => new Mock<T>();

    /// <summary>
    /// Create a mock logger for the specified type
    /// </summary>
    protected Mock<ILogger<T>> CreateMockLogger<T>() => new Mock<ILogger<T>>();

    /// <summary>
    /// Create an instance of the specified type with auto-generated data
    /// </summary>
    protected T Create<T>() => Fixture.Create<T>();

    /// <summary>
    /// Create multiple instances of the specified type
    /// </summary>
    protected IEnumerable<T> CreateMany<T>(int count = 3) => Fixture.CreateMany<T>(count);

    /// <summary>
    /// Verify that a mock was called with specific parameters
    /// </summary>
    protected void VerifyMockCall<T>(Mock<T> mock, System.Linq.Expressions.Expression<Action<T>> expression, Times times)
        where T : class
    {
        mock.Verify(expression, times);
    }

    /// <summary>
    /// Verify that a mock was called once
    /// </summary>
    protected void VerifyMockCallOnce<T>(Mock<T> mock, System.Linq.Expressions.Expression<Action<T>> expression)
        where T : class
    {
        mock.Verify(expression, Times.Once);
    }

    /// <summary>
    /// Verify that a mock was never called
    /// </summary>
    protected void VerifyMockNeverCalled<T>(Mock<T> mock, System.Linq.Expressions.Expression<Action<T>> expression)
        where T : class
    {
        mock.Verify(expression, Times.Never);
    }
}

/// <summary>
/// AutoData attribute for xUnit tests that uses the configured fixture
/// </summary>
public class AutoMoqDataAttribute : AutoDataAttribute
{
    public AutoMoqDataAttribute() : base(() =>
    {
        var fixture = new Fixture();
        fixture.Customize(new AutoMoqCustomization { ConfigureMembers = true });
        return fixture;
    })
    {
    }
}

/// <summary>
/// InlineAutoData attribute for xUnit tests
/// </summary>
public class InlineAutoMoqDataAttribute : InlineAutoDataAttribute
{
    public InlineAutoMoqDataAttribute(params object[] values) : base(new AutoMoqDataAttribute(), values)
    {
    }
}
