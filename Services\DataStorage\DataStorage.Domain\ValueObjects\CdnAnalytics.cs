using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class CacheInvalidationResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? InvalidationId { get; private set; }
    public List<string> InvalidatedPaths { get; private set; }
    public DateTime RequestedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public CacheInvalidationStatus Status { get; private set; }

    private CacheInvalidationResult()
    {
        InvalidatedPaths = new List<string>();
    }

    public static CacheInvalidationResult Success(
        string invalidationId,
        List<string> invalidatedPaths,
        CacheInvalidationStatus status = CacheInvalidationStatus.InProgress)
    {
        return new CacheInvalidationResult
        {
            IsSuccessful = true,
            InvalidationId = invalidationId,
            InvalidatedPaths = invalidatedPaths ?? new List<string>(),
            RequestedAt = DateTime.UtcNow,
            Status = status
        };
    }

    public static CacheInvalidationResult Failure(string errorMessage)
    {
        return new CacheInvalidationResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            RequestedAt = DateTime.UtcNow,
            Status = CacheInvalidationStatus.Failed,
            InvalidatedPaths = new List<string>()
        };
    }
}

public class CacheStatistics
{
    public string EndpointId { get; private set; }
    public TimeSpan Period { get; private set; }
    public long TotalRequests { get; private set; }
    public long CacheHits { get; private set; }
    public long CacheMisses { get; private set; }
    public double CacheHitRatio { get; private set; }
    public long BytesServed { get; private set; }
    public long BytesFromCache { get; private set; }
    public long BytesFromOrigin { get; private set; }
    public Dictionary<string, long> RequestsByCountry { get; private set; }
    public Dictionary<string, long> RequestsByStatusCode { get; private set; }

    public CacheStatistics(
        string endpointId,
        TimeSpan period,
        long totalRequests,
        long cacheHits,
        long cacheMisses,
        long bytesServed,
        long bytesFromCache,
        long bytesFromOrigin,
        Dictionary<string, long>? requestsByCountry = null,
        Dictionary<string, long>? requestsByStatusCode = null)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Period = period;
        TotalRequests = totalRequests;
        CacheHits = cacheHits;
        CacheMisses = cacheMisses;
        CacheHitRatio = totalRequests > 0 ? (double)cacheHits / totalRequests * 100 : 0;
        BytesServed = bytesServed;
        BytesFromCache = bytesFromCache;
        BytesFromOrigin = bytesFromOrigin;
        RequestsByCountry = requestsByCountry ?? new Dictionary<string, long>();
        RequestsByStatusCode = requestsByStatusCode ?? new Dictionary<string, long>();
    }
}

public class CacheStatus
{
    public string Path { get; private set; }
    public CdnProvider Provider { get; private set; }
    public bool IsCached { get; private set; }
    public DateTime? CachedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public TimeSpan? TimeToLive { get; private set; }
    public string? ETag { get; private set; }
    public long? ContentLength { get; private set; }
    public string? ContentType { get; private set; }

    public CacheStatus(
        string path,
        CdnProvider provider,
        bool isCached,
        DateTime? cachedAt = null,
        DateTime? expiresAt = null,
        string? etag = null,
        long? contentLength = null,
        string? contentType = null)
    {
        Path = path ?? throw new ArgumentNullException(nameof(path));
        Provider = provider;
        IsCached = isCached;
        CachedAt = cachedAt;
        ExpiresAt = expiresAt;
        TimeToLive = expiresAt.HasValue && cachedAt.HasValue ? expiresAt.Value - cachedAt.Value : null;
        ETag = etag;
        ContentLength = contentLength;
        ContentType = contentType;
    }
}

public class PerformanceAnalysis
{
    public string EndpointId { get; private set; }
    public TimeSpan Period { get; private set; }
    public double AverageResponseTime { get; private set; }
    public double MedianResponseTime { get; private set; }
    public double P95ResponseTime { get; private set; }
    public double P99ResponseTime { get; private set; }
    public double Throughput { get; private set; }
    public double ErrorRate { get; private set; }
    public List<PerformanceMetric> MetricsByRegion { get; private set; }
    public List<PerformanceMetric> MetricsByContentType { get; private set; }

    public PerformanceAnalysis(
        string endpointId,
        TimeSpan period,
        double averageResponseTime,
        double medianResponseTime,
        double p95ResponseTime,
        double p99ResponseTime,
        double throughput,
        double errorRate,
        List<PerformanceMetric>? metricsByRegion = null,
        List<PerformanceMetric>? metricsByContentType = null)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Period = period;
        AverageResponseTime = averageResponseTime;
        MedianResponseTime = medianResponseTime;
        P95ResponseTime = p95ResponseTime;
        P99ResponseTime = p99ResponseTime;
        Throughput = throughput;
        ErrorRate = errorRate;
        MetricsByRegion = metricsByRegion ?? new List<PerformanceMetric>();
        MetricsByContentType = metricsByContentType ?? new List<PerformanceMetric>();
    }
}

public class PerformanceMetric
{
    public string Category { get; private set; }
    public string Name { get; private set; }
    public double Value { get; private set; }
    public string Unit { get; private set; }
    public DateTime Timestamp { get; private set; }

    public PerformanceMetric(string category, string name, double value, string unit, DateTime timestamp = default)
    {
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Value = value;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Timestamp = timestamp == default ? DateTime.UtcNow : timestamp;
    }
}

public class PerformanceRecommendation
{
    public PerformanceRecommendationType Type { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public PerformanceImpact Impact { get; private set; }
    public ImplementationComplexity Complexity { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public PerformanceRecommendation(
        PerformanceRecommendationType type,
        string title,
        string description,
        PerformanceImpact impact,
        ImplementationComplexity complexity,
        Dictionary<string, object>? parameters = null)
    {
        Type = type;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Impact = impact;
        Complexity = complexity;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class CompressionResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public CompressionOptions AppliedOptions { get; private set; }
    public double EstimatedSavings { get; private set; }
    public List<string> AffectedMimeTypes { get; private set; }

    private CompressionResult()
    {
        AppliedOptions = CompressionOptions.Default;
        AffectedMimeTypes = new List<string>();
    }

    public static CompressionResult Success(
        CompressionOptions appliedOptions,
        double estimatedSavings,
        List<string> affectedMimeTypes)
    {
        return new CompressionResult
        {
            IsSuccessful = true,
            AppliedOptions = appliedOptions,
            EstimatedSavings = estimatedSavings,
            AffectedMimeTypes = affectedMimeTypes ?? new List<string>()
        };
    }

    public static CompressionResult Failure(string errorMessage)
    {
        return new CompressionResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            EstimatedSavings = 0,
            AffectedMimeTypes = new List<string>()
        };
    }
}

public class EdgeLocation
{
    public string Id { get; private set; }
    public string Name { get; private set; }
    public string City { get; private set; }
    public string Country { get; private set; }
    public string Region { get; private set; }
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public CdnProvider Provider { get; private set; }
    public bool IsActive { get; private set; }

    public EdgeLocation(
        string id,
        string name,
        string city,
        string country,
        string region,
        double latitude,
        double longitude,
        CdnProvider provider,
        bool isActive = true)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = name ?? throw new ArgumentNullException(nameof(name));
        City = city ?? throw new ArgumentNullException(nameof(city));
        Country = country ?? throw new ArgumentNullException(nameof(country));
        Region = region ?? throw new ArgumentNullException(nameof(region));
        Latitude = latitude;
        Longitude = longitude;
        Provider = provider;
        IsActive = isActive;
    }
}

public class GeographicDistribution
{
    public string EndpointId { get; private set; }
    public TimeSpan Period { get; private set; }
    public Dictionary<string, long> RequestsByCountry { get; private set; }
    public Dictionary<string, long> RequestsByRegion { get; private set; }
    public Dictionary<string, double> AverageLatencyByCountry { get; private set; }
    public Dictionary<string, double> CacheHitRatioByCountry { get; private set; }

    public GeographicDistribution(
        string endpointId,
        TimeSpan period,
        Dictionary<string, long> requestsByCountry,
        Dictionary<string, long> requestsByRegion,
        Dictionary<string, double> averageLatencyByCountry,
        Dictionary<string, double> cacheHitRatioByCountry)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Period = period;
        RequestsByCountry = requestsByCountry ?? new Dictionary<string, long>();
        RequestsByRegion = requestsByRegion ?? new Dictionary<string, long>();
        AverageLatencyByCountry = averageLatencyByCountry ?? new Dictionary<string, double>();
        CacheHitRatioByCountry = cacheHitRatioByCountry ?? new Dictionary<string, double>();
    }
}

public class LatencyAnalysis
{
    public string EndpointId { get; private set; }
    public List<LatencyMeasurement> Measurements { get; private set; }
    public double AverageLatency { get; private set; }
    public double MinLatency { get; private set; }
    public double MaxLatency { get; private set; }
    public double MedianLatency { get; private set; }

    public LatencyAnalysis(string endpointId, List<LatencyMeasurement> measurements)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Measurements = measurements ?? new List<LatencyMeasurement>();
        
        if (Measurements.Any())
        {
            var latencies = Measurements.Select(m => m.LatencyMs).ToList();
            AverageLatency = latencies.Average();
            MinLatency = latencies.Min();
            MaxLatency = latencies.Max();
            MedianLatency = latencies.OrderBy(x => x).Skip(latencies.Count / 2).First();
        }
    }
}

public class LatencyMeasurement
{
    public string TestLocation { get; private set; }
    public double LatencyMs { get; private set; }
    public DateTime Timestamp { get; private set; }
    public bool IsSuccessful { get; private set; }

    public LatencyMeasurement(string testLocation, double latencyMs, bool isSuccessful = true, DateTime timestamp = default)
    {
        TestLocation = testLocation ?? throw new ArgumentNullException(nameof(testLocation));
        LatencyMs = latencyMs;
        IsSuccessful = isSuccessful;
        Timestamp = timestamp == default ? DateTime.UtcNow : timestamp;
    }
}
