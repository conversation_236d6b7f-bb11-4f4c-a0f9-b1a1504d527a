using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqMilestoneProgressConfiguration : IEntityTypeConfiguration<RfqMilestoneProgress>
{
    public void Configure(EntityTypeBuilder<RfqMilestoneProgress> builder)
    {
        builder.ToTable("RfqMilestoneProgress");

        builder.HasKey(rmp => rmp.Id);

        builder.Property(rmp => rmp.Id)
            .ValueGeneratedOnAdd();

        builder.Property(rmp => rmp.RfqMilestoneAssignmentId)
            .IsRequired();

        builder.Property(rmp => rmp.MilestoneStepId)
            .IsRequired();

        builder.Property(rmp => rmp.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rmp => rmp.PayoutPercentage)
            .IsRequired()
            .HasPrecision(5, 2);

        builder.Property(rmp => rmp.StartedAt)
            .IsRequired(false);

        builder.Property(rmp => rmp.CompletedAt)
            .IsRequired(false);

        builder.Property(rmp => rmp.CompletedBy)
            .IsRequired(false);

        builder.Property(rmp => rmp.CompletedByName)
            .HasMaxLength(200);

        builder.Property(rmp => rmp.Notes)
            .HasMaxLength(2000);

        builder.Property(rmp => rmp.DocumentationUrls)
            .HasMaxLength(4000);

        builder.Property(rmp => rmp.RequiresApproval)
            .IsRequired();

        builder.Property(rmp => rmp.ApprovedAt)
            .IsRequired(false);

        builder.Property(rmp => rmp.ApprovedBy)
            .IsRequired(false);

        builder.Property(rmp => rmp.ApprovedByName)
            .HasMaxLength(200);

        builder.Property(rmp => rmp.ApprovalNotes)
            .HasMaxLength(2000);

        builder.Property(rmp => rmp.RejectedAt)
            .IsRequired(false);

        builder.Property(rmp => rmp.RejectedBy)
            .IsRequired(false);

        builder.Property(rmp => rmp.RejectionReason)
            .HasMaxLength(1000);

        // Relationships
        builder.HasOne(rmp => rmp.RfqMilestoneAssignment)
            .WithMany(rma => rma.MilestoneProgress)
            .HasForeignKey(rmp => rmp.RfqMilestoneAssignmentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(rmp => rmp.MilestoneStep)
            .WithMany()
            .HasForeignKey(rmp => rmp.MilestoneStepId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(rmp => rmp.RfqMilestoneAssignmentId)
            .HasDatabaseName("IX_RfqMilestoneProgress_AssignmentId");

        builder.HasIndex(rmp => rmp.MilestoneStepId)
            .HasDatabaseName("IX_RfqMilestoneProgress_StepId");

        builder.HasIndex(rmp => rmp.Status)
            .HasDatabaseName("IX_RfqMilestoneProgress_Status");

        builder.HasIndex(rmp => rmp.CompletedAt)
            .HasDatabaseName("IX_RfqMilestoneProgress_CompletedAt");

        builder.HasIndex(rmp => new { rmp.RfqMilestoneAssignmentId, rmp.MilestoneStepId })
            .HasDatabaseName("IX_RfqMilestoneProgress_AssignmentId_StepId")
            .IsUnique();
    }
}
