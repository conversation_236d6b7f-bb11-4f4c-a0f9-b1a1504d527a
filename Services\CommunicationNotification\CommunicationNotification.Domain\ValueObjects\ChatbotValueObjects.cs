﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Chatbot configuration value object
/// </summary>
public class ChatbotConfiguration : ValueObject
{
    public string NLPProvider { get; private set; }
    public string ModelVersion { get; private set; }
    public decimal ConfidenceThreshold { get; private set; }
    public bool EnableSpellCheck { get; private set; }
    public bool EnableSentimentAnalysis { get; private set; }
    public bool EnableEntityExtraction { get; private set; }
    public bool EnableContextAwareness { get; private set; }
    public int MaxRetries { get; private set; }
    public TimeSpan ResponseTimeout { get; private set; }
    public Dictionary<string, string> ProviderSettings { get; private set; }
    public List<string> StopWords { get; private set; }
    public Dictionary<string, object> AdvancedSettings { get; private set; }

    private ChatbotConfiguration()
    {
        NLPProvider = "OpenAI";
        ModelVersion = "gpt-3.5-turbo";
        ProviderSettings = new Dictionary<string, string>();
        StopWords = new List<string>();
        AdvancedSettings = new Dictionary<string, object>();
    }

    public ChatbotConfiguration(
        string nlpProvider,
        string modelVersion,
        decimal confidenceThreshold = 0.7m,
        bool enableSpellCheck = true,
        bool enableSentimentAnalysis = true,
        bool enableEntityExtraction = true,
        bool enableContextAwareness = true,
        int maxRetries = 3,
        TimeSpan? responseTimeout = null,
        Dictionary<string, string>? providerSettings = null,
        List<string>? stopWords = null,
        Dictionary<string, object>? advancedSettings = null)
    {
        NLPProvider = nlpProvider;
        ModelVersion = modelVersion;
        ConfidenceThreshold = confidenceThreshold;
        EnableSpellCheck = enableSpellCheck;
        EnableSentimentAnalysis = enableSentimentAnalysis;
        EnableEntityExtraction = enableEntityExtraction;
        EnableContextAwareness = enableContextAwareness;
        MaxRetries = maxRetries;
        ResponseTimeout = responseTimeout ?? TimeSpan.FromSeconds(30);
        ProviderSettings = providerSettings ?? new Dictionary<string, string>();
        StopWords = stopWords ?? new List<string>();
        AdvancedSettings = advancedSettings ?? new Dictionary<string, object>();
    }

    public static ChatbotConfiguration Default()
    {
        return new ChatbotConfiguration("OpenAI", "gpt-3.5-turbo");
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return NLPProvider;
        yield return ModelVersion;
        yield return ConfidenceThreshold;
        yield return EnableSpellCheck;
        yield return EnableSentimentAnalysis;
        yield return EnableEntityExtraction;
        yield return EnableContextAwareness;
        yield return MaxRetries;
        yield return ResponseTimeout;

        foreach (var kvp in ProviderSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var word in StopWords.OrderBy(x => x))
        {
            yield return word;
        }

        foreach (var kvp in AdvancedSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Chatbot analytics value object
/// </summary>
public class ChatbotAnalytics : ValueObject
{
    public int TotalConversations { get; private set; }
    public int ActiveConversations { get; private set; }
    public int CompletedConversations { get; private set; }
    public int AbandonedConversations { get; private set; }
    public decimal AverageConversationDuration { get; private set; }
    public decimal AverageResponseTime { get; private set; }
    public decimal UserSatisfactionScore { get; private set; }
    public decimal IntentRecognitionAccuracy { get; private set; }
    public decimal FallbackRate { get; private set; }
    public Dictionary<string, int> TopIntents { get; private set; }
    public Dictionary<string, int> UnrecognizedQueries { get; private set; }
    public Dictionary<string, decimal> LanguageUsage { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private ChatbotAnalytics()
    {
        TopIntents = new Dictionary<string, int>();
        UnrecognizedQueries = new Dictionary<string, int>();
        LanguageUsage = new Dictionary<string, decimal>();
    }

    public ChatbotAnalytics(
        int totalConversations,
        int activeConversations,
        int completedConversations,
        int abandonedConversations,
        decimal averageConversationDuration,
        decimal averageResponseTime,
        decimal userSatisfactionScore,
        decimal intentRecognitionAccuracy,
        decimal fallbackRate,
        Dictionary<string, int>? topIntents = null,
        Dictionary<string, int>? unrecognizedQueries = null,
        Dictionary<string, decimal>? languageUsage = null)
    {
        TotalConversations = totalConversations;
        ActiveConversations = activeConversations;
        CompletedConversations = completedConversations;
        AbandonedConversations = abandonedConversations;
        AverageConversationDuration = averageConversationDuration;
        AverageResponseTime = averageResponseTime;
        UserSatisfactionScore = userSatisfactionScore;
        IntentRecognitionAccuracy = intentRecognitionAccuracy;
        FallbackRate = fallbackRate;
        TopIntents = topIntents ?? new Dictionary<string, int>();
        UnrecognizedQueries = unrecognizedQueries ?? new Dictionary<string, int>();
        LanguageUsage = languageUsage ?? new Dictionary<string, decimal>();
        LastUpdated = DateTime.UtcNow;
    }

    public static ChatbotAnalytics Empty()
    {
        return new ChatbotAnalytics(0, 0, 0, 0, 0, 0, 0, 0, 0);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalConversations;
        yield return ActiveConversations;
        yield return CompletedConversations;
        yield return AbandonedConversations;
        yield return AverageConversationDuration;
        yield return AverageResponseTime;
        yield return UserSatisfactionScore;
        yield return IntentRecognitionAccuracy;
        yield return FallbackRate;
        yield return LastUpdated;

        foreach (var kvp in TopIntents.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in UnrecognizedQueries.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in LanguageUsage.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Chatbot conversation session value object
/// </summary>
public class ChatbotConversation : ValueObject
{
    public Guid SessionId { get; private set; }
    public Guid ChatbotId { get; private set; }
    public Guid UserId { get; private set; }
    public string UserName { get; private set; }
    public NotificationChannel Channel { get; private set; }
    public string Language { get; private set; }
    public ConversationStatus Status { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? EndedAt { get; private set; }
    public List<ChatbotMessage> Messages { get; private set; }
    public Dictionary<string, string> Context { get; private set; }
    public Dictionary<string, object> ExtractedEntities { get; private set; }
    public string? CurrentIntent { get; private set; }
    public decimal? SatisfactionScore { get; private set; }
    public int TurnCount { get; private set; }

    private ChatbotConversation()
    {
        UserName = string.Empty;
        Language = "en-US";
        Messages = new List<ChatbotMessage>();
        Context = new Dictionary<string, string>();
        ExtractedEntities = new Dictionary<string, object>();
    }

    public ChatbotConversation(
        Guid chatbotId,
        Guid userId,
        string userName,
        NotificationChannel channel,
        string language = "en-US")
    {
        if (chatbotId == Guid.Empty)
            throw new ArgumentException("Chatbot ID cannot be empty", nameof(chatbotId));
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));
        if (string.IsNullOrWhiteSpace(userName))
            throw new ArgumentException("User name cannot be empty", nameof(userName));

        SessionId = Guid.NewGuid();
        ChatbotId = chatbotId;
        UserId = userId;
        UserName = userName;
        Channel = channel;
        Language = language;
        Status = ConversationStatus.Active;
        StartedAt = DateTime.UtcNow;
        Messages = new List<ChatbotMessage>();
        Context = new Dictionary<string, string>();
        ExtractedEntities = new Dictionary<string, object>();
        TurnCount = 0;
    }

    public static ChatbotConversation Create(
        Guid chatbotId,
        Guid userId,
        string userName,
        NotificationChannel channel,
        string language = "en-US")
    {
        return new ChatbotConversation(chatbotId, userId, userName, channel, language);
    }

    public void AddMessage(ChatbotMessage message)
    {
        if (message == null)
            throw new ArgumentNullException(nameof(message));

        Messages.Add(message);

        if (message.IsFromUser)
        {
            TurnCount++;
        }
    }

    public void SetCurrentIntent(string intent)
    {
        CurrentIntent = intent;
    }

    public void AddContext(string key, string value)
    {
        Context[key] = value;
    }

    public void AddExtractedEntity(string entityName, object value)
    {
        ExtractedEntities[entityName] = value;
    }

    public void Complete(decimal? satisfactionScore = null)
    {
        Status = ConversationStatus.Completed;
        EndedAt = DateTime.UtcNow;
        SatisfactionScore = satisfactionScore;
    }

    public void Abandon()
    {
        Status = ConversationStatus.Abandoned;
        EndedAt = DateTime.UtcNow;
    }

    public void TransferToAgent()
    {
        Status = ConversationStatus.TransferredToAgent;
    }

    public TimeSpan GetDuration()
    {
        var endTime = EndedAt ?? DateTime.UtcNow;
        return endTime - StartedAt;
    }

    public ChatbotMessage? GetLastUserMessage()
    {
        return Messages.LastOrDefault(m => m.IsFromUser);
    }

    public ChatbotMessage? GetLastBotMessage()
    {
        return Messages.LastOrDefault(m => !m.IsFromUser);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SessionId;
        yield return ChatbotId;
        yield return UserId;
        yield return UserName;
        yield return Channel;
        yield return Language;
        yield return Status;
        yield return StartedAt;
        yield return EndedAt ?? DateTime.MinValue;
        yield return CurrentIntent ?? string.Empty;
        yield return SatisfactionScore ?? 0;
        yield return TurnCount;

        foreach (var message in Messages)
        {
            yield return message;
        }

        foreach (var kvp in Context.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in ExtractedEntities.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Chatbot message value object
/// </summary>
public class ChatbotMessage : ValueObject
{
    public Guid Id { get; private set; }
    public string Text { get; private set; }
    public bool IsFromUser { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string? Intent { get; private set; }
    public decimal? Confidence { get; private set; }
    public Dictionary<string, object> Entities { get; private set; }
    public string? Sentiment { get; private set; }
    public ResponseType ResponseType { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    private ChatbotMessage()
    {
        Text = string.Empty;
        Entities = new Dictionary<string, object>();
        Metadata = new Dictionary<string, string>();
    }

    public ChatbotMessage(
        string text,
        bool isFromUser,
        string? intent = null,
        decimal? confidence = null,
        Dictionary<string, object>? entities = null,
        string? sentiment = null,
        ResponseType responseType = ResponseType.Text,
        Dictionary<string, string>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(text))
            throw new ArgumentException("Message text cannot be empty", nameof(text));

        Id = Guid.NewGuid();
        Text = text;
        IsFromUser = isFromUser;
        Timestamp = DateTime.UtcNow;
        Intent = intent;
        Confidence = confidence;
        Entities = entities ?? new Dictionary<string, object>();
        Sentiment = sentiment;
        ResponseType = responseType;
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static ChatbotMessage FromUser(
        string text,
        string? intent = null,
        decimal? confidence = null,
        Dictionary<string, object>? entities = null,
        string? sentiment = null)
    {
        return new ChatbotMessage(text, true, intent, confidence, entities, sentiment);
    }

    public static ChatbotMessage FromBot(
        string text,
        ResponseType responseType = ResponseType.Text,
        Dictionary<string, string>? metadata = null)
    {
        return new ChatbotMessage(text, false, null, null, null, null, responseType, metadata);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Id;
        yield return Text;
        yield return IsFromUser;
        yield return Timestamp;
        yield return Intent ?? string.Empty;
        yield return Confidence ?? 0;
        yield return Sentiment ?? string.Empty;
        yield return ResponseType;

        foreach (var kvp in Entities.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in Metadata.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// NLP processing result value object
/// </summary>
public class NLPResult : ValueObject
{
    public string Text { get; private set; }
    public string? Intent { get; private set; }
    public decimal Confidence { get; private set; }
    public Dictionary<string, object> Entities { get; private set; }
    public string? Sentiment { get; private set; }
    public decimal SentimentScore { get; private set; }
    public string Language { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private NLPResult()
    {
        Text = string.Empty;
        Language = string.Empty;
        Entities = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
    }

    public NLPResult(
        string text,
        string? intent,
        decimal confidence,
        Dictionary<string, object>? entities = null,
        string? sentiment = null,
        decimal sentimentScore = 0,
        string language = "en-US",
        bool isSuccessful = true,
        string? errorMessage = null,
        Dictionary<string, object>? metadata = null)
    {
        Text = text;
        Intent = intent;
        Confidence = confidence;
        Entities = entities ?? new Dictionary<string, object>();
        Sentiment = sentiment;
        SentimentScore = sentimentScore;
        Language = language;
        IsSuccessful = isSuccessful;
        ErrorMessage = errorMessage;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public static NLPResult Success(
        string text,
        string intent,
        decimal confidence,
        Dictionary<string, object>? entities = null,
        string? sentiment = null,
        decimal sentimentScore = 0,
        string language = "en-US")
    {
        return new NLPResult(text, intent, confidence, entities, sentiment, sentimentScore, language, true);
    }

    public static NLPResult Failure(string text, string errorMessage)
    {
        return new NLPResult(text, null, 0, null, null, 0, "en-US", false, errorMessage);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Text;
        yield return Intent ?? string.Empty;
        yield return Confidence;
        yield return Sentiment ?? string.Empty;
        yield return SentimentScore;
        yield return Language;
        yield return IsSuccessful;
        yield return ErrorMessage ?? string.Empty;

        foreach (var kvp in Entities.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in Metadata.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Conversation status enumeration
/// </summary>
public enum ConversationStatus
{
    Active = 1,
    Completed = 2,
    Abandoned = 3,
    TransferredToAgent = 4,
    Error = 5
}


