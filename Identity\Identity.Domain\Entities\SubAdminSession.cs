using System;
using Identity.Domain.Common;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public enum SessionStatus
    {
        Active,
        Expired,
        Terminated,
        Revoked
    }

    public class SubAdminSession : Entity
    {
        public Guid SubAdminId { get; private set; }
        public string SessionToken { get; private set; }
        public string IpAddress { get; private set; }
        public string UserAgent { get; private set; }
        public DateTime ExpiresAt { get; private set; }
        public SessionStatus Status { get; private set; }
        public DateTime? LastActivityAt { get; private set; }
        public string Location { get; private set; }
        public bool IsEmergencyAccess { get; private set; }
        public string EmergencyReason { get; private set; }
        public Guid? AuthorizedBy { get; private set; }

        private SubAdminSession() { }

        public SubAdminSession(
            Guid subAdminId, 
            string sessionToken, 
            string ipAddress, 
            string userAgent, 
            DateTime expiresAt,
            string location = null,
            bool isEmergencyAccess = false,
            string emergencyReason = null,
            Guid? authorizedBy = null)
        {
            if (string.IsNullOrWhiteSpace(sessionToken))
                throw new DomainException("Session token cannot be empty");

            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new DomainException("IP address cannot be empty");

            if (expiresAt <= DateTime.UtcNow)
                throw new DomainException("Session expiry must be in the future");

            SubAdminId = subAdminId;
            SessionToken = sessionToken;
            IpAddress = ipAddress;
            UserAgent = userAgent ?? string.Empty;
            ExpiresAt = expiresAt;
            Status = SessionStatus.Active;
            LastActivityAt = DateTime.UtcNow;
            Location = location ?? string.Empty;
            IsEmergencyAccess = isEmergencyAccess;
            EmergencyReason = emergencyReason ?? string.Empty;
            AuthorizedBy = authorizedBy;
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateActivity()
        {
            if (Status != SessionStatus.Active)
                throw new DomainException("Cannot update activity for inactive session");

            if (DateTime.UtcNow >= ExpiresAt)
            {
                Status = SessionStatus.Expired;
                UpdatedAt = DateTime.UtcNow;
                throw new DomainException("Session has expired");
            }

            LastActivityAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Terminate()
        {
            if (Status == SessionStatus.Active)
            {
                Status = SessionStatus.Terminated;
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void Revoke()
        {
            if (Status == SessionStatus.Active)
            {
                Status = SessionStatus.Revoked;
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void ExtendExpiry(DateTime newExpiresAt)
        {
            if (Status != SessionStatus.Active)
                throw new DomainException("Cannot extend expired or terminated session");

            if (newExpiresAt <= DateTime.UtcNow)
                throw new DomainException("New expiry must be in the future");

            ExpiresAt = newExpiresAt;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsValid()
        {
            return Status == SessionStatus.Active && DateTime.UtcNow < ExpiresAt;
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow >= ExpiresAt || Status == SessionStatus.Expired;
        }

        public void CheckAndUpdateStatus()
        {
            if (Status == SessionStatus.Active && DateTime.UtcNow >= ExpiresAt)
            {
                Status = SessionStatus.Expired;
                UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
