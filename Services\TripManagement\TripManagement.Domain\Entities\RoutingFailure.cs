using Shared.Domain.Common;
using TripManagement.Domain.Events;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Entity representing routing failures for analysis and reporting
/// </summary>
public class RoutingFailure : AggregateRoot
{
    public Guid TripId { get; private set; }
    public string? OrderNumber { get; private set; }
    public FailureType FailureType { get; private set; }
    public FailureSeverity Severity { get; private set; }
    public string FailureReason { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty; // Route, Vehicle, Driver, System, External
    public Location? FailureLocation { get; private set; }
    public DateTime FailureTime { get; private set; }
    public DateTime? DetectedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public TimeSpan? ResolutionTime { get; private set; }
    public FailureStatus Status { get; private set; }
    public string? ResolutionAction { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public decimal? ImpactCost { get; private set; }
    public TimeSpan? DelayTime { get; private set; }
    public int? AffectedCustomers { get; private set; }
    public bool IsRecurring { get; private set; }
    public string? RootCause { get; private set; }
    public string? PreventiveAction { get; private set; }
    public Dictionary<string, object> TechnicalDetails { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    // Private constructor for EF Core
    private RoutingFailure() { }

    public RoutingFailure(
        Guid tripId,
        string? orderNumber,
        FailureType failureType,
        FailureSeverity severity,
        string failureReason,
        string description,
        string category,
        Location? failureLocation = null,
        DateTime? failureTime = null,
        decimal? impactCost = null,
        TimeSpan? delayTime = null,
        int? affectedCustomers = null,
        Dictionary<string, object>? technicalDetails = null,
        Dictionary<string, object>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(failureReason))
            throw new ArgumentException("Failure reason cannot be empty", nameof(failureReason));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        TripId = tripId;
        OrderNumber = orderNumber?.Trim();
        FailureType = failureType;
        Severity = severity;
        FailureReason = failureReason.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        FailureLocation = failureLocation;
        FailureTime = failureTime ?? DateTime.UtcNow;
        DetectedAt = DateTime.UtcNow;
        Status = FailureStatus.Open;
        ImpactCost = impactCost;
        DelayTime = delayTime;
        AffectedCustomers = affectedCustomers;
        IsRecurring = false;
        TechnicalDetails = technicalDetails ?? new Dictionary<string, object>();
        Metadata = metadata ?? new Dictionary<string, object>();

        // Add domain event
        AddDomainEvent(new RoutingFailureDetectedEvent(Id, TripId, FailureType, Severity, FailureReason));
    }

    public void Resolve(Guid resolvedBy, string resolutionAction, string? rootCause = null, string? preventiveAction = null)
    {
        if (Status == FailureStatus.Resolved)
            throw new InvalidOperationException("Failure is already resolved");

        Status = FailureStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
        ResolutionAction = resolutionAction?.Trim();
        RootCause = rootCause?.Trim();
        PreventiveAction = preventiveAction?.Trim();

        if (DetectedAt.HasValue)
        {
            ResolutionTime = ResolvedAt.Value - DetectedAt.Value;
        }

        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new RoutingFailureResolvedEvent(Id, TripId, resolvedBy, resolutionAction, ResolutionTime));
    }

    public void Escalate(FailureSeverity newSeverity, string escalationReason)
    {
        if (newSeverity <= Severity)
            throw new ArgumentException("New severity must be higher than current severity");

        var oldSeverity = Severity;
        Severity = newSeverity;
        Status = FailureStatus.Escalated;

        // Add escalation to metadata
        var escalationInfo = new
        {
            PreviousSeverity = oldSeverity.ToString(),
            NewSeverity = newSeverity.ToString(),
            EscalationReason = escalationReason,
            EscalatedAt = DateTime.UtcNow
        };

        Metadata[$"Escalation_{DateTime.UtcNow:yyyyMMddHHmmss}"] = escalationInfo;
        SetUpdatedAt();

        // Add domain event
        AddDomainEvent(new RoutingFailureEscalatedEvent(Id, TripId, oldSeverity, newSeverity, escalationReason));
    }

    public void MarkAsRecurring(string recurrencePattern)
    {
        IsRecurring = true;
        Metadata["RecurrencePattern"] = recurrencePattern;
        Metadata["MarkedRecurringAt"] = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void UpdateImpactAssessment(decimal? impactCost, TimeSpan? delayTime, int? affectedCustomers)
    {
        ImpactCost = impactCost;
        DelayTime = delayTime;
        AffectedCustomers = affectedCustomers;
        SetUpdatedAt();
    }

    public void AddTechnicalDetail(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Technical detail key cannot be empty", nameof(key));

        TechnicalDetails[key] = value;
        SetUpdatedAt();
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value;
        SetUpdatedAt();
    }

    public bool IsHighImpact()
    {
        return Severity == FailureSeverity.Critical ||
               (ImpactCost.HasValue && ImpactCost.Value > 1000) ||
               (AffectedCustomers.HasValue && AffectedCustomers.Value > 10);
    }

    public bool IsLongRunning()
    {
        if (!DetectedAt.HasValue) return false;

        var duration = Status == FailureStatus.Resolved && ResolvedAt.HasValue
            ? ResolvedAt.Value - DetectedAt.Value
            : DateTime.UtcNow - DetectedAt.Value;

        return duration.TotalHours > 4; // Consider failures taking more than 4 hours as long-running
    }

    public TimeSpan GetAge()
    {
        return DateTime.UtcNow - (DetectedAt ?? CreatedAt);
    }

    public string GetImpactLevel()
    {
        if (IsHighImpact()) return "High";
        if (Severity == FailureSeverity.Medium || (ImpactCost.HasValue && ImpactCost.Value > 100)) return "Medium";
        return "Low";
    }
}


