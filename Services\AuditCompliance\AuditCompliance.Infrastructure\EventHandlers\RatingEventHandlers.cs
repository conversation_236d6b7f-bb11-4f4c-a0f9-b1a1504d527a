using MediatR;
using Shared.Messaging;
using Shared.Messaging.Events;
using AuditCompliance.Domain.Events;
using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.EventHandlers;

/// <summary>
/// Handles service provider rating domain events and publishes integration events
/// </summary>
public class ServiceProviderRatingCreatedEventHandler : INotificationHandler<ServiceProviderRatingCreatedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ServiceProviderRatingCreatedEventHandler> _logger;

    public ServiceProviderRatingCreatedEventHandler(
        IMessageBroker messageBroker,
        ILogger<ServiceProviderRatingCreatedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(ServiceProviderRatingCreatedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.ServiceProviderRatingCreatedEvent
            {
                RatingId = notification.Rating.Id,
                ShipperId = notification.Rating.ShipperId,
                ShipperName = notification.Rating.ShipperName,
                TransportCompanyId = notification.Rating.TransportCompanyId,
                TransportCompanyName = notification.Rating.TransportCompanyName,
                OrderId = notification.Rating.OrderId,
                OrderNumber = notification.Rating.OrderNumber,
                TripId = notification.Rating.TripId,
                TripNumber = notification.Rating.TripNumber,
                OverallRating = notification.Rating.OverallRating.NumericValue,
                ReviewTitle = notification.Rating.ReviewTitle,
                ReviewComment = notification.Rating.ReviewComment,
                IsAnonymous = notification.Rating.IsAnonymous,
                ServiceCompletedAt = notification.Rating.ServiceCompletedAt,
                CreatedAt = notification.Rating.CreatedAt,
                CategoryRatings = notification.Rating.CategoryRatings.Select(cr => new CategoryRatingData
                {
                    Category = cr.Category.ToString(),
                    CategoryName = cr.GetCategoryName(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList()
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published ServiceProviderRatingCreatedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ServiceProviderRatingCreatedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
    }
}

public class ServiceProviderRatingSubmittedEventHandler : INotificationHandler<ServiceProviderRatingSubmittedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<ServiceProviderRatingSubmittedEventHandler> _logger;

    public ServiceProviderRatingSubmittedEventHandler(
        IMessageBroker messageBroker,
        IServiceProviderRatingRepository ratingRepository,
        ILogger<ServiceProviderRatingSubmittedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task Handle(ServiceProviderRatingSubmittedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            // Get all ratings for the transport company to calculate statistics
            var allRatings = await _ratingRepository.GetRatingsByTransportCompanyAsync(
                notification.Rating.TransportCompanyId, cancellationToken);

            var activeRatings = allRatings.Where(r => r.Status == Domain.Enums.RatingStatus.Active).ToList();
            var newAverageRating = activeRatings.Any() ? activeRatings.Average(r => r.OverallRating.NumericValue) : 0;

            var integrationEvent = new Shared.Messaging.Events.ServiceProviderRatingSubmittedEvent
            {
                RatingId = notification.Rating.Id,
                ShipperId = notification.Rating.ShipperId,
                TransportCompanyId = notification.Rating.TransportCompanyId,
                TransportCompanyName = notification.Rating.TransportCompanyName,
                OverallRating = notification.Rating.OverallRating.NumericValue,
                SubmittedAt = notification.Rating.ReviewSubmittedAt ?? DateTime.UtcNow,
                IsPositiveRating = notification.Rating.OverallRating.IsPositive,
                TotalRatingsForCompany = activeRatings.Count,
                NewAverageRating = newAverageRating
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            // Publish rating statistics update event
            var categoryAverages = activeRatings
                .SelectMany(r => r.CategoryRatings)
                .GroupBy(cr => cr.Category)
                .Select(g => new CategoryRatingAverage
                {
                    Category = g.Key.ToString(),
                    CategoryName = g.First().GetCategoryName(),
                    AverageRating = g.Average(cr => cr.Rating.NumericValue),
                    TotalRatings = g.Count()
                }).ToList();

            var ratingUpdateEvent = new TransportCompanyRatingUpdatedEvent
            {
                TransportCompanyId = notification.Rating.TransportCompanyId,
                TransportCompanyName = notification.Rating.TransportCompanyName,
                NewAverageRating = newAverageRating,
                PreviousAverageRating = activeRatings.Count > 1 
                    ? activeRatings.Where(r => r.Id != notification.Rating.Id).Average(r => r.OverallRating.NumericValue) 
                    : 0,
                TotalRatings = activeRatings.Count,
                NewRatingsCount = 1,
                UpdatedAt = DateTime.UtcNow,
                IsSignificantChange = Math.Abs(newAverageRating - (activeRatings.Count > 1 
                    ? activeRatings.Where(r => r.Id != notification.Rating.Id).Average(r => r.OverallRating.NumericValue) 
                    : 0)) > 0.5m,
                CategoryAverages = categoryAverages
            };

            await _messageBroker.PublishAsync(ratingUpdateEvent, cancellationToken);

            _logger.LogInformation("Published ServiceProviderRatingSubmittedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ServiceProviderRatingSubmittedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
    }
}

public class ServiceProviderRatingFlaggedEventHandler : INotificationHandler<ServiceProviderRatingFlaggedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ServiceProviderRatingFlaggedEventHandler> _logger;

    public ServiceProviderRatingFlaggedEventHandler(
        IMessageBroker messageBroker,
        ILogger<ServiceProviderRatingFlaggedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(ServiceProviderRatingFlaggedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.ServiceProviderRatingFlaggedEvent
            {
                RatingId = notification.Rating.Id,
                TransportCompanyId = notification.Rating.TransportCompanyId,
                TransportCompanyName = notification.Rating.TransportCompanyName,
                Reason = notification.Reason,
                FlaggedAt = DateTime.UtcNow,
                FlaggedBy = "System", // Could be enhanced to track who flagged it
                RequiresReview = true
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published ServiceProviderRatingFlaggedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ServiceProviderRatingFlaggedEvent for rating: {RatingId}", 
                notification.Rating.Id);
        }
    }
}

public class ServiceIssueReportedEventHandler : INotificationHandler<ServiceIssueReportedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ServiceIssueReportedEventHandler> _logger;

    public ServiceIssueReportedEventHandler(
        IMessageBroker messageBroker,
        ILogger<ServiceIssueReportedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(ServiceIssueReportedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.ServiceIssueReportedEvent
            {
                IssueId = notification.Issue.Id,
                RatingId = notification.Rating.Id,
                ShipperId = notification.Rating.ShipperId,
                ShipperName = notification.Rating.ShipperName,
                TransportCompanyId = notification.Rating.TransportCompanyId,
                TransportCompanyName = notification.Rating.TransportCompanyName,
                IssueType = notification.Issue.IssueType.ToString(),
                Priority = notification.Issue.Priority.ToString(),
                Description = notification.Issue.Description,
                Evidence = notification.Issue.Evidence,
                ReportedAt = notification.Issue.ReportedAt,
                IsHighPriority = notification.Issue.IsHighPriority()
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published ServiceIssueReportedEvent for issue: {IssueId}", 
                notification.Issue.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing ServiceIssueReportedEvent for issue: {IssueId}", 
                notification.Issue.Id);
        }
    }
}

public class PreferredProviderAddedEventHandler : INotificationHandler<PreferredProviderAddedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<PreferredProviderAddedEventHandler> _logger;

    public PreferredProviderAddedEventHandler(
        IMessageBroker messageBroker,
        ILogger<PreferredProviderAddedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(PreferredProviderAddedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.PreferredProviderAddedEvent
            {
                NetworkId = notification.PreferredProvider.Id,
                ShipperId = notification.PreferredProvider.ShipperId,
                ShipperName = notification.PreferredProvider.ShipperName,
                TransportCompanyId = notification.PreferredProvider.TransportCompanyId,
                TransportCompanyName = notification.PreferredProvider.TransportCompanyName,
                AverageRating = notification.PreferredProvider.AverageRating,
                TotalOrders = notification.PreferredProvider.TotalOrders,
                CompletedOrders = notification.PreferredProvider.CompletedOrders,
                CompletionRate = notification.PreferredProvider.CompletionRate,
                PreferenceRank = notification.PreferredProvider.PreferenceRank,
                AddedAt = notification.PreferredProvider.CreatedAt
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published PreferredProviderAddedEvent for network: {NetworkId}", 
                notification.PreferredProvider.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing PreferredProviderAddedEvent for network: {NetworkId}", 
                notification.PreferredProvider.Id);
        }
    }
}

public class PreferredProviderRemovedEventHandler : INotificationHandler<PreferredProviderRemovedEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<PreferredProviderRemovedEventHandler> _logger;

    public PreferredProviderRemovedEventHandler(
        IMessageBroker messageBroker,
        ILogger<PreferredProviderRemovedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(PreferredProviderRemovedEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            var integrationEvent = new Shared.Messaging.Events.PreferredProviderRemovedEvent
            {
                NetworkId = notification.PreferredProvider.Id,
                ShipperId = notification.PreferredProvider.ShipperId,
                ShipperName = notification.PreferredProvider.ShipperName,
                TransportCompanyId = notification.PreferredProvider.TransportCompanyId,
                TransportCompanyName = notification.PreferredProvider.TransportCompanyName,
                Reason = notification.Reason,
                RemovedAt = DateTime.UtcNow
            };

            await _messageBroker.PublishAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Published PreferredProviderRemovedEvent for network: {NetworkId}", 
                notification.PreferredProvider.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing PreferredProviderRemovedEvent for network: {NetworkId}", 
                notification.PreferredProvider.Id);
        }
    }
}
