using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.DTOs;

public class RfqDetailDto
{
    public Guid Id { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string RfqNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public LoadDetailsDto LoadDetails { get; set; } = new();
    public RouteDetailsDto RouteDetails { get; set; } = new();
    public RfqRequirementsDto Requirements { get; set; } = new();
    public RfqStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string? ClosureReason { get; set; }
    public MoneyDto? BudgetRange { get; set; }
    public string? SpecialInstructions { get; set; }
    public bool IsUrgent { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
    public RfqTimeframeDto? Timeframe { get; set; }
    public List<TimeframeExtensionDto> TimeframeExtensions { get; set; } = new();
    public List<RfqBidSummaryDto> Bids { get; set; } = new();
    public List<DocumentDto> Documents { get; set; } = new();

    // New Shipper Portal Features
    public List<PreferredPartnerDto> PreferredPartners { get; set; } = new();
    public MilestoneTemplateDto? MilestoneTemplate { get; set; }
    public List<TransporterRatingDto> TransporterRatings { get; set; } = new();
    public List<BrokerRatingDto> BrokerRatings { get; set; } = new();
    public bool HasPreferredPartnerFilter { get; set; }
}

public class RfqSummaryDto
{
    public Guid Id { get; set; }
    public string RfqNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public LoadType LoadType { get; set; }
    public VehicleType RequiredVehicleType { get; set; }
    public string PickupCity { get; set; } = string.Empty;
    public string PickupState { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
    public string DeliveryState { get; set; } = string.Empty;
    public DateTime PreferredPickupDate { get; set; }
    public DateTime PreferredDeliveryDate { get; set; }
    public RfqStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public MoneyDto? BudgetRange { get; set; }
    public bool IsUrgent { get; set; }
    public int BidCount { get; set; }
}

public class RfqBidDetailDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BidNumber { get; set; } = string.Empty;
    public MoneyDto QuotedPrice { get; set; } = new();
    public string? ProposedTerms { get; set; }
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public string? VehicleDetails { get; set; }
    public string? DriverDetails { get; set; }
    public string? AdditionalServices { get; set; }
    public BidStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public DateTime? AcceptedAt { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectionReason { get; set; }
    public string? Notes { get; set; }
    public bool IsCounterOffer { get; set; }
    public Guid? OriginalBidId { get; set; }
    public List<DocumentDto> Documents { get; set; } = new();
}

public class RfqBidSummaryDto
{
    public Guid Id { get; set; }
    public Guid BrokerId { get; set; }
    public string BidNumber { get; set; } = string.Empty;
    public MoneyDto QuotedPrice { get; set; } = new();
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public BidStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public bool IsCounterOffer { get; set; }
}

public class LoadDetailsDto
{
    public string Description { get; set; } = string.Empty;
    public LoadType LoadType { get; set; }
    public WeightDto Weight { get; set; } = new();
    public VolumeDto? Volume { get; set; }
    public DimensionsDto? Dimensions { get; set; }
    public int Quantity { get; set; }
    public string? PackagingType { get; set; }
    public VehicleType RequiredVehicleType { get; set; }
    public bool RequiresSpecialHandling { get; set; }
    public string? SpecialHandlingInstructions { get; set; }
    public bool IsHazardous { get; set; }
    public string? HazardousClassification { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public TemperatureRangeDto? TemperatureRange { get; set; }
    public List<string> Photos { get; set; } = new();
}

public class RouteDetailsDto
{
    public AddressDto PickupAddress { get; set; } = new();
    public AddressDto DeliveryAddress { get; set; } = new();
    public DateTime PreferredPickupDate { get; set; }
    public DateTime PreferredDeliveryDate { get; set; }
    public TimeWindowDto? PickupTimeWindow { get; set; }
    public TimeWindowDto? DeliveryTimeWindow { get; set; }
    public DistanceDto? EstimatedDistance { get; set; }
    public TimeSpan? EstimatedDuration { get; set; }
    public List<AddressDto> IntermediateStops { get; set; } = new();
    public string? RouteNotes { get; set; }
    public bool IsFlexiblePickup { get; set; }
    public bool IsFlexibleDelivery { get; set; }
}

public class RfqRequirementsDto
{
    public VehicleType RequiredVehicleType { get; set; }
    public int? MinVehicleCapacity { get; set; }
    public bool RequiresInsurance { get; set; }
    public decimal? MinInsuranceAmount { get; set; }
    public bool RequiresLicense { get; set; }
    public List<string> RequiredLicenses { get; set; } = new();
    public bool RequiresExperience { get; set; }
    public int? MinYearsExperience { get; set; }
    public bool RequiresBackground { get; set; }
    public bool RequiresTracking { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public TemperatureRangeDto? TemperatureRange { get; set; }
    public List<string> AdditionalRequirements { get; set; } = new();
}

public class AddressDto
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? SpecialInstructions { get; set; }
    public string FullAddress { get; set; } = string.Empty;
}

public class MoneyDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string FormattedAmount { get; set; } = string.Empty;
}

public class WeightDto
{
    public decimal Value { get; set; }
    public WeightUnit Unit { get; set; }
    public decimal ValueInKg { get; set; }
}

public class VolumeDto
{
    public decimal Value { get; set; }
    public VolumeUnit Unit { get; set; }
    public decimal ValueInCubicMeters { get; set; }
}

public class DimensionsDto
{
    public decimal Length { get; set; }
    public decimal Width { get; set; }
    public decimal Height { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal Volume { get; set; }
}

public class TemperatureRangeDto
{
    public decimal MinTemperature { get; set; }
    public decimal MaxTemperature { get; set; }
    public string Unit { get; set; } = string.Empty;
}

public class TimeWindowDto
{
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public string DisplayTime { get; set; } = string.Empty;
}

public class DistanceDto
{
    public decimal Value { get; set; }
    public DistanceUnit Unit { get; set; }
    public decimal ValueInKm { get; set; }
}

public class DocumentDto
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DocumentType DocumentType { get; set; }
    public string? Description { get; set; }
    public DateTime UploadedAt { get; set; }
    public Guid UploadedBy { get; set; }
}

// Bid DTOs
public class BidDetailDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BidNumber { get; set; } = string.Empty;
    public MoneyDto QuotedPrice { get; set; } = new();
    public string? ProposedTerms { get; set; }
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public string? VehicleDetails { get; set; }
    public string? DriverDetails { get; set; }
    public string? AdditionalServices { get; set; }
    public BidStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public DateTime? AcceptedAt { get; set; }
    public DateTime? RejectedAt { get; set; }
    public string? RejectionReason { get; set; }
    public string? Notes { get; set; }
    public bool IsCounterOffer { get; set; }
    public Guid? OriginalBidId { get; set; }
    public RfqSummaryDto Rfq { get; set; } = new();
    public List<DocumentDto> Documents { get; set; } = new();
}

public class BidSummaryDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public string BidNumber { get; set; } = string.Empty;
    public string RfqTitle { get; set; } = string.Empty;
    public string RfqNumber { get; set; } = string.Empty;
    public MoneyDto QuotedPrice { get; set; } = new();
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public BidStatus Status { get; set; }
    public DateTime SubmittedAt { get; set; }
    public bool IsCounterOffer { get; set; }
    public string PickupCity { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
}



// Timeframe DTOs
public class RfqTimeframeDto
{
    public int Duration { get; set; }
    public TimeframeUnit Unit { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public bool AllowExtensions { get; set; }
    public int MaxExtensions { get; set; }
    public int ExtensionCount { get; set; }
    public bool IsExpired { get; set; }
    public bool IsApproachingExpiration { get; set; }
    public bool CanExtend { get; set; }
    public TimeSpan RemainingTime { get; set; }
    public int TotalMinutes { get; set; }
}

public class TimeframeExtensionDto
{
    public int Duration { get; set; }
    public TimeframeUnit Unit { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime ExtendedAt { get; set; }
    public Guid ExtendedBy { get; set; }
    public DateTime NewExpiresAt { get; set; }
    public int ExtensionMinutes { get; set; }
}

// Timeline DTOs
public class RfqTimelineEventDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string EventDescription { get; set; } = string.Empty;
    public DateTime EventTimestamp { get; set; }
    public Guid? ActorId { get; set; }
    public string? ActorName { get; set; }
    public string? ActorRole { get; set; }
    public string? AdditionalData { get; set; }
    public string? PreviousStatus { get; set; }
    public string? NewStatus { get; set; }
}

public class RfqRoutingHistoryDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public string Action { get; set; } = string.Empty;
    public Guid? FromEntityId { get; set; }
    public string? FromEntityName { get; set; }
    public string? FromEntityType { get; set; }
    public Guid? ToEntityId { get; set; }
    public string? ToEntityName { get; set; }
    public string? ToEntityType { get; set; }
    public DateTime RoutedAt { get; set; }
    public Guid? RoutedBy { get; set; }
    public string? RoutedByName { get; set; }
    public string? RoutingReason { get; set; }
    public string? RoutingNotes { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ResponseDeadline { get; set; }
    public DateTime? RespondedAt { get; set; }
    public string? ResponseNotes { get; set; }
    public bool IsSuccessful { get; set; }
    public string? FailureReason { get; set; }
    public int SequenceNumber { get; set; }
}

public class RfqTagDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public string TagType { get; set; } = string.Empty;
    public string TagName { get; set; } = string.Empty;
    public string? TagDescription { get; set; }
    public string? TagColor { get; set; }
    public DateTime AppliedAt { get; set; }
    public Guid? AppliedBy { get; set; }
    public string? AppliedByName { get; set; }
    public bool IsSystemGenerated { get; set; }
    public string? AutoTagReason { get; set; }
    public Dictionary<string, object>? TagMetadata { get; set; }
    public bool IsActive { get; set; }
    public DateTime? RemovedAt { get; set; }
    public Guid? RemovedBy { get; set; }
    public string? RemovalReason { get; set; }
}

public class RfqMilestoneDetailsDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public Guid MilestoneTemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string TemplateDescription { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; }
    public string? AssignedByName { get; set; }
    public bool IsActive { get; set; }
    public MoneyDto? TotalContractValue { get; set; }
    public string? CustomPayoutStructure { get; set; }
    public List<MilestoneStepDto> Steps { get; set; } = new();
    public List<MilestoneProgressDto> Progress { get; set; } = new();
    public MoneyDto? CompletedPayoutAmount { get; set; }
    public MoneyDto? RemainingPayoutAmount { get; set; }
    public decimal CompletionPercentage { get; set; }
    public int CompletedStepsCount { get; set; }
    public int TotalStepsCount { get; set; }
}

public class MilestoneStepDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int StepOrder { get; set; }
    public decimal PayoutPercentage { get; set; }
    public string StepType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool RequiresDocumentation { get; set; }
    public bool RequiresApproval { get; set; }
    public string? DocumentationRequirements { get; set; }
    public string? ApprovalCriteria { get; set; }
    public int? EstimatedDurationHours { get; set; }
}

public class MilestoneProgressDto
{
    public Guid Id { get; set; }
    public Guid MilestoneStepId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal PayoutPercentage { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletedByName { get; set; }
    public string? Notes { get; set; }
    public bool RequiresApproval { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedByName { get; set; }
    public string? ApprovalNotes { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsPendingApproval { get; set; }
    public bool IsInProgress { get; set; }
}

public class RfqNegotiationDetailsDto
{
    public Guid RfqId { get; set; }
    public bool HasPriceExpectations { get; set; }
    public PriceExpectationsDto? PriceExpectations { get; set; }
    public bool HasReverseAuction { get; set; }
    public ReverseAuctionSettingsDto? ReverseAuctionSettings { get; set; }
    public bool IsReverseAuctionActive { get; set; }
    public bool IsPriceExpectationExpired { get; set; }
    public TimeSpan? ReverseAuctionTimeRemaining { get; set; }
    public bool CanExtendReverseAuction { get; set; }
    public List<BrokerCommentDto> BrokerComments { get; set; } = new();
    public int TotalBrokerComments { get; set; }
    public bool HasActiveNegotiation { get; set; }
}

public class PriceExpectationsDto
{
    public MoneyDto? MinExpectedPrice { get; set; }
    public MoneyDto? MaxExpectedPrice { get; set; }
    public MoneyDto? TargetPrice { get; set; }
    public string ExpectationType { get; set; } = string.Empty;
    public bool IsFlexible { get; set; }
    public string? PriceJustification { get; set; }
    public DateTime? PriceValidUntil { get; set; }
    public bool AllowCounterOffers { get; set; }
    public decimal? MaxCounterOfferVariance { get; set; }
    public string? PricingNotes { get; set; }
    public bool IsExpired { get; set; }
}

public class ReverseAuctionSettingsDto
{
    public bool IsEnabled { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public MoneyDto? StartingPrice { get; set; }
    public MoneyDto? ReservePrice { get; set; }
    public decimal? MinimumBidDecrement { get; set; }
    public int? MaxBidders { get; set; }
    public bool AllowBidExtensions { get; set; }
    public int? ExtensionMinutes { get; set; }
    public bool IsPublicAuction { get; set; }
    public string? AuctionRules { get; set; }
    public bool IsActive { get; set; }
    public bool HasStarted { get; set; }
    public bool HasEnded { get; set; }
    public TimeSpan? RemainingTime { get; set; }
}

public class BrokerCommentDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public string CommentType { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsInternal { get; set; }
    public bool IsVisible { get; set; }
    public Guid? ParentCommentId { get; set; }
    public string? Tags { get; set; }
    public int Priority { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsExpired { get; set; }
    public bool IsReply { get; set; }
    public List<BrokerCommentDto> Replies { get; set; } = new();
}

// Pagination result
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}

// ===== SHIPPER PORTAL FEATURE DTOs =====

// Preferred Partner DTOs
public class PreferredPartnerDto
{
    public Guid Id { get; set; }
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty; // "Transporter" or "Broker"
    public string CompanyName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public decimal PerformanceRating { get; set; }
    public int CompletedTrips { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public DateTime AddedAt { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> VehicleTypes { get; set; } = new();
}

// Milestone Template DTOs
public class MilestoneTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<MilestoneStepTemplateDto> Steps { get; set; } = new();
    public int TotalSteps { get; set; }
    public decimal TotalPayoutPercentage { get; set; }
    public bool RequiresApproval { get; set; }
    public string? ApprovalWorkflow { get; set; }
}

public class MilestoneStepTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int StepOrder { get; set; }
    public decimal PayoutPercentage { get; set; }
    public string StepType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool RequiresDocumentation { get; set; }
    public bool RequiresApproval { get; set; }
    public string? DocumentationRequirements { get; set; }
    public string? ApprovalCriteria { get; set; }
    public int? EstimatedDurationHours { get; set; }
}

// Rating DTOs
public class TransporterRatingDto
{
    public Guid TransporterId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal SafetyRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public DateTime LastTripDate { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> VehicleTypes { get; set; } = new();
    public bool IsPreferredPartner { get; set; }
    public MoneyDto? AverageQuotePrice { get; set; }
    public string? Notes { get; set; }
}

public class BrokerRatingDto
{
    public Guid BrokerId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public decimal ResponseTimeRating { get; set; }
    public decimal QuoteAccuracyRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal NetworkQualityRating { get; set; }
    public int TotalRfqs { get; set; }
    public int SuccessfulMatches { get; set; }
    public decimal MatchSuccessRate { get; set; }
    public DateTime LastRfqDate { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> Specializations { get; set; } = new();
    public bool IsPreferredPartner { get; set; }
    public MoneyDto? AverageCommissionRate { get; set; }
    public string? Notes { get; set; }
}

// Quote Comparison DTOs
public class QuoteComparisonDto
{
    public Guid RfqId { get; set; }
    public string RfqTitle { get; set; } = string.Empty;
    public List<QuoteComparisonItemDto> Quotes { get; set; } = new();
    public QuoteComparisonSummaryDto Summary { get; set; } = new();
    public List<string> ComparisonCriteria { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public string? RecommendedQuoteId { get; set; }
    public string? RecommendationReason { get; set; }
}

public class QuoteComparisonItemDto
{
    public Guid BidId { get; set; }
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string BidNumber { get; set; } = string.Empty;
    public MoneyDto QuotedPrice { get; set; } = new();
    public DateTime EstimatedPickupDate { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public decimal BrokerRating { get; set; }
    public string? VehicleDetails { get; set; }
    public string? DriverDetails { get; set; }
    public string? AdditionalServices { get; set; }
    public List<string> Advantages { get; set; } = new();
    public List<string> Disadvantages { get; set; } = new();
    public bool IsRecommended { get; set; }
    public string? RecommendationScore { get; set; }
    public bool IsCounterOffer { get; set; }
    public Guid? OriginalBidId { get; set; }
}

public class QuoteComparisonSummaryDto
{
    public MoneyDto LowestPrice { get; set; } = new();
    public MoneyDto HighestPrice { get; set; } = new();
    public MoneyDto AveragePrice { get; set; } = new();
    public DateTime EarliestPickup { get; set; }
    public DateTime LatestPickup { get; set; }
    public DateTime EarliestDelivery { get; set; }
    public DateTime LatestDelivery { get; set; }
    public decimal HighestRatedBroker { get; set; }
    public decimal LowestRatedBroker { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalQuotes { get; set; }
    public int CounterOffers { get; set; }
    public MoneyDto PriceDifference { get; set; } = new();
    public TimeSpan DeliveryTimeRange { get; set; }
}

// Negotiation DTOs
public class NegotiationDto
{
    public Guid Id { get; set; }
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public Guid OriginalBidId { get; set; }
    public string NegotiationStatus { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<CounterOfferDto> CounterOffers { get; set; } = new();
    public int TotalRounds { get; set; }
    public MoneyDto OriginalPrice { get; set; } = new();
    public MoneyDto? FinalPrice { get; set; }
    public string? FinalTerms { get; set; }
    public bool IsActive { get; set; }
    public string? Notes { get; set; }
}

public class CounterOfferDto
{
    public Guid Id { get; set; }
    public Guid NegotiationId { get; set; }
    public Guid BidId { get; set; }
    public string OfferType { get; set; } = string.Empty; // "Shipper" or "Broker"
    public MoneyDto OfferedPrice { get; set; } = new();
    public string? OfferedTerms { get; set; }
    public DateTime? OfferedPickupDate { get; set; }
    public DateTime? OfferedDeliveryDate { get; set; }
    public string? AdditionalServices { get; set; }
    public string? CounterOfferReason { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? RespondedAt { get; set; }
    public string? ResponseNotes { get; set; }
    public bool IsAccepted { get; set; }
    public bool IsRejected { get; set; }
    public bool IsExpired { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
