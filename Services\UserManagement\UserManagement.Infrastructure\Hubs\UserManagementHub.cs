using Microsoft.AspNetCore.SignalR;

namespace UserManagement.Infrastructure.Hubs
{
    public class UserManagementHub : Hub<IUserManagementClient>
    {
        public override async Task OnConnectedAsync()
        {
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            await base.OnDisconnectedAsync(exception);
        }
    }
}
