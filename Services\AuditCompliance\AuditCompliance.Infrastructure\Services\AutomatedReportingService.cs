using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Reporting;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Service for automated compliance reporting
/// </summary>
public class AutomatedReportingService : IAutomatedReportingService
{
    private readonly AuditComplianceDbContext _context;
    private readonly IReportTemplateEngine _templateEngine;
    private readonly IReportExportService _exportService;
    private readonly IAdvancedAnalyticsService _analyticsService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<AutomatedReportingService> _logger;

    public AutomatedReportingService(
        AuditComplianceDbContext context,
        IReportTemplateEngine templateEngine,
        IReportExportService exportService,
        IAdvancedAnalyticsService analyticsService,
        ITenantContext tenantContext,
        ILogger<AutomatedReportingService> logger)
    {
        _context = context;
        _templateEngine = templateEngine;
        _exportService = exportService;
        _analyticsService = analyticsService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task<Guid> CreateReportScheduleAsync(
        CreateReportScheduleDto scheduleDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating report schedule: {Name}", scheduleDto.Name);

            // Validate template exists
            var template = await _context.ReportTemplates
                .FirstOrDefaultAsync(t => t.Id == scheduleDto.TemplateId, cancellationToken);

            if (template == null)
            {
                throw new InvalidOperationException($"Report template {scheduleDto.TemplateId} not found");
            }

            var schedule = new ReportSchedule(
                scheduleDto.Name,
                scheduleDto.Description,
                scheduleDto.TemplateId,
                scheduleDto.Schedule,
                scheduleDto.OutputFormat,
                scheduleDto.Recipients,
                scheduleDto.Parameters,
                _tenantContext.TenantId);

            if (!scheduleDto.IsActive)
            {
                schedule.Deactivate();
            }

            _context.ReportSchedules.Add(schedule);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created report schedule {ScheduleId}", schedule.Id);
            return schedule.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report schedule");
            throw;
        }
    }

    public async Task<bool> UpdateReportScheduleAsync(
        Guid scheduleId,
        UpdateReportScheduleDto scheduleDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var schedule = await _context.ReportSchedules
                .FirstOrDefaultAsync(s => s.Id == scheduleId, cancellationToken);

            if (schedule == null)
            {
                _logger.LogWarning("Report schedule not found: {ScheduleId}", scheduleId);
                return false;
            }

            schedule.UpdateDetails(
                scheduleDto.Name,
                scheduleDto.Description,
                scheduleDto.Schedule,
                scheduleDto.OutputFormat,
                scheduleDto.Recipients,
                scheduleDto.Parameters);

            if (scheduleDto.IsActive && !schedule.IsActive)
            {
                schedule.Activate();
            }
            else if (!scheduleDto.IsActive && schedule.IsActive)
            {
                schedule.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated report schedule {ScheduleId}", scheduleId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report schedule {ScheduleId}", scheduleId);
            throw;
        }
    }

    public async Task<bool> DeleteReportScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var schedule = await _context.ReportSchedules
                .FirstOrDefaultAsync(s => s.Id == scheduleId, cancellationToken);

            if (schedule == null)
            {
                _logger.LogWarning("Report schedule not found: {ScheduleId}", scheduleId);
                return false;
            }

            _context.ReportSchedules.Remove(schedule);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted report schedule {ScheduleId}", scheduleId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting report schedule {ScheduleId}", scheduleId);
            throw;
        }
    }

    public async Task<ReportScheduleDto?> GetReportScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var schedule = await _context.ReportSchedules
                .Include(s => s.Template)
                .FirstOrDefaultAsync(s => s.Id == scheduleId, cancellationToken);

            return schedule != null ? MapToScheduleDto(schedule) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report schedule {ScheduleId}", scheduleId);
            throw;
        }
    }

    public async Task<List<ReportScheduleDto>> GetReportSchedulesAsync(
        Guid? tenantId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.ReportSchedules
                .Include(s => s.Template)
                .AsQueryable();

            if (tenantId.HasValue)
            {
                query = query.Where(s => s.TenantId == tenantId);
            }
            else if (_tenantContext.HasTenant)
            {
                query = query.Where(s => s.TenantId == _tenantContext.TenantId);
            }

            var schedules = await query.ToListAsync(cancellationToken);
            return schedules.Select(MapToScheduleDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report schedules");
            throw;
        }
    }

    public async Task<Guid> GenerateReportAsync(
        GenerateReportDto reportDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating report for template {TemplateId}", reportDto.TemplateId);

            var template = await _context.ReportTemplates
                .FirstOrDefaultAsync(t => t.Id == reportDto.TemplateId, cancellationToken);

            if (template == null)
            {
                throw new InvalidOperationException($"Report template {reportDto.TemplateId} not found");
            }

            var execution = new ReportExecution(
                null, // No schedule for manual generation
                reportDto.TemplateId,
                reportDto.OutputFormat,
                reportDto.Parameters,
                "Manual", // Executed by
                _tenantContext.TenantId);

            _context.ReportExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            // Start report generation in background
            _ = Task.Run(async () => await ExecuteReportGenerationAsync(execution.Id, cancellationToken));

            return execution.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report");
            throw;
        }
    }

    public async Task<List<ReportTemplateDto>> GetReportTemplatesAsync(
        string? category = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.ReportTemplates.AsQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(t => t.Category == category);
            }

            if (_tenantContext.HasTenant)
            {
                query = query.Where(t => t.TenantId == _tenantContext.TenantId || t.TenantId == null);
            }

            var templates = await query
                .Where(t => t.IsActive)
                .OrderBy(t => t.Category)
                .ThenBy(t => t.Name)
                .ToListAsync(cancellationToken);

            return templates.Select(MapToTemplateDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates");
            throw;
        }
    }

    public async Task<Guid> CreateReportTemplateAsync(
        CreateReportTemplateDto templateDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating report template: {Name}", templateDto.Name);

            var template = new ReportTemplate(
                templateDto.Name,
                templateDto.Description,
                templateDto.Category,
                templateDto.Type,
                templateDto.Content,
                templateDto.DataSources,
                templateDto.Parameters.Select(MapFromParameterDto).ToList(),
                templateDto.SupportedFormats,
                "System", // Created by
                _tenantContext.TenantId);

            _context.ReportTemplates.Add(template);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created report template {TemplateId}", template.Id);
            return template.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report template");
            throw;
        }
    }

    public async Task<bool> UpdateReportTemplateAsync(
        Guid templateId,
        UpdateReportTemplateDto templateDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _context.ReportTemplates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

            if (template == null)
            {
                _logger.LogWarning("Report template not found: {TemplateId}", templateId);
                return false;
            }

            template.UpdateDetails(
                templateDto.Name,
                templateDto.Description,
                templateDto.Category,
                templateDto.Content,
                templateDto.DataSources,
                templateDto.Parameters.Select(MapFromParameterDto).ToList(),
                templateDto.SupportedFormats,
                "System"); // Updated by

            if (templateDto.IsActive && !template.IsActive)
            {
                template.Activate();
            }
            else if (!templateDto.IsActive && template.IsActive)
            {
                template.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated report template {TemplateId}", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<List<ReportExecutionDto>> GetReportExecutionHistoryAsync(
        Guid? scheduleId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.ReportExecutions
                .Include(e => e.Template)
                .Include(e => e.Schedule)
                .AsQueryable();

            if (scheduleId.HasValue)
            {
                query = query.Where(e => e.ScheduleId == scheduleId);
            }

            if (fromDate.HasValue)
            {
                query = query.Where(e => e.StartedAt >= fromDate);
            }

            if (toDate.HasValue)
            {
                query = query.Where(e => e.StartedAt <= toDate);
            }

            if (_tenantContext.HasTenant)
            {
                query = query.Where(e => e.TenantId == _tenantContext.TenantId);
            }

            var executions = await query
                .OrderByDescending(e => e.StartedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return executions.Select(MapToExecutionDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report execution history");
            throw;
        }
    }

    public async Task<GeneratedReportDto?> GetReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _context.ReportExecutions
                .Include(e => e.Template)
                .FirstOrDefaultAsync(e => e.Id == reportId, cancellationToken);

            return execution != null ? MapToGeneratedReportDto(execution) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report {ReportId}", reportId);
            throw;
        }
    }

    public async Task<ReportFileDto?> DownloadReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _context.ReportExecutions
                .FirstOrDefaultAsync(e => e.Id == reportId && e.Status == "Completed", cancellationToken);

            if (execution == null || string.IsNullOrEmpty(execution.FilePath))
            {
                return null;
            }

            // Read file from storage
            var fileBytes = await File.ReadAllBytesAsync(execution.FilePath, cancellationToken);
            var fileName = Path.GetFileName(execution.FilePath);
            var contentType = GetContentType(execution.OutputFormat);

            return new ReportFileDto
            {
                FileName = fileName,
                ContentType = contentType,
                Content = fileBytes,
                Size = fileBytes.Length,
                GeneratedAt = execution.CompletedAt ?? execution.StartedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading report {ReportId}", reportId);
            throw;
        }
    }

    public async Task<Guid> ExportReportAsync(
        ExportReportDto exportDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting report {ReportId} to {Format}", exportDto.ReportId, exportDto.Format);

            var exportId = Guid.NewGuid();
            
            // Start export in background
            _ = Task.Run(async () => await ExecuteReportExportAsync(exportId, exportDto, cancellationToken));

            return exportId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report");
            throw;
        }
    }

    public async Task<ExportStatusDto> GetExportStatusAsync(
        Guid exportId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically be stored in a cache or database
            // For now, return a placeholder status
            return new ExportStatusDto
            {
                ExportId = exportId,
                Status = "Completed",
                ProgressPercentage = 100,
                StartedAt = DateTime.UtcNow.AddMinutes(-2),
                CompletedAt = DateTime.UtcNow,
                DownloadUrl = $"/api/reports/exports/{exportId}/download"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting export status {ExportId}", exportId);
            throw;
        }
    }

    public async Task ProcessScheduledReportsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing scheduled reports");

            var now = DateTime.UtcNow;
            var dueSchedules = await _context.ReportSchedules
                .Where(s => s.IsActive && (s.NextExecution == null || s.NextExecution <= now))
                .ToListAsync(cancellationToken);

            foreach (var schedule in dueSchedules)
            {
                try
                {
                    await ExecuteScheduledReportAsync(schedule, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing scheduled report {ScheduleId}", schedule.Id);
                }
            }

            _logger.LogInformation("Processed {Count} scheduled reports", dueSchedules.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled reports");
            throw;
        }
    }

    // Additional methods will be implemented in continuation due to length constraints
    public async Task<ReportTemplateValidationResult> ValidateReportTemplateAsync(
        ReportTemplateDto template,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return new ReportTemplateValidationResult { IsValid = true };
    }

    public async Task<ComplianceMetricsDto> GetComplianceMetricsAsync(
        ComplianceMetricsRequestDto request,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder - would use analytics service
        return new ComplianceMetricsDto
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Overview = new ComplianceOverviewMetricsDto()
        };
    }

    public async Task<Guid> GenerateRegulatoryReportAsync(
        RegulatoryReportRequestDto request,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return Guid.NewGuid();
    }

    public async Task<List<ReportDistributionDto>> GetReportDistributionAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return new List<ReportDistributionDto>();
    }

    public async Task<bool> UpdateReportDistributionAsync(
        Guid scheduleId,
        List<ReportDistributionDto> distribution,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return true;
    }

    public async Task<bool> SendReportNotificationAsync(
        Guid reportId,
        List<string> recipients,
        CancellationToken cancellationToken = default)
    {
        // Implementation placeholder
        return true;
    }

    // Private helper methods
    private async Task ExecuteReportGenerationAsync(Guid executionId, CancellationToken cancellationToken)
    {
        // Implementation for background report generation
        _logger.LogInformation("Executing report generation for {ExecutionId}", executionId);
    }

    private async Task ExecuteReportExportAsync(Guid exportId, ExportReportDto exportDto, CancellationToken cancellationToken)
    {
        // Implementation for background report export
        _logger.LogInformation("Executing report export for {ExportId}", exportId);
    }

    private async Task ExecuteScheduledReportAsync(dynamic schedule, CancellationToken cancellationToken)
    {
        // Implementation for scheduled report execution
        _logger.LogInformation("Executing scheduled report {ScheduleId}", schedule.Id);
    }

    private string GetContentType(string format)
    {
        return format.ToLower() switch
        {
            "pdf" => "application/pdf",
            "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "csv" => "text/csv",
            "json" => "application/json",
            _ => "application/octet-stream"
        };
    }

    // Mapping methods
    private ReportScheduleDto MapToScheduleDto(dynamic schedule)
    {
        return new ReportScheduleDto
        {
            Id = schedule.Id,
            Name = schedule.Name,
            Description = schedule.Description,
            TemplateId = schedule.TemplateId,
            TemplateName = schedule.Template?.Name ?? "",
            Schedule = schedule.Schedule,
            IsActive = schedule.IsActive,
            LastExecuted = schedule.LastExecuted,
            NextExecution = schedule.NextExecution,
            OutputFormat = schedule.OutputFormat,
            Recipients = JsonSerializer.Deserialize<List<string>>(schedule.Recipients ?? "[]") ?? new List<string>(),
            Parameters = JsonSerializer.Deserialize<Dictionary<string, object>>(schedule.Parameters ?? "{}") ?? new Dictionary<string, object>(),
            CreatedAt = schedule.CreatedAt,
            CreatedBy = schedule.CreatedBy,
            TenantId = schedule.TenantId
        };
    }

    private ReportTemplateDto MapToTemplateDto(dynamic template)
    {
        return new ReportTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            Category = template.Category,
            Type = template.Type,
            Content = template.Content,
            DataSources = JsonSerializer.Deserialize<List<string>>(template.DataSources ?? "[]") ?? new List<string>(),
            Parameters = new List<ReportParameterDto>(), // Would be properly mapped
            SupportedFormats = JsonSerializer.Deserialize<List<string>>(template.SupportedFormats ?? "[]") ?? new List<string>(),
            IsActive = template.IsActive,
            CreatedAt = template.CreatedAt,
            CreatedBy = template.CreatedBy,
            UpdatedAt = template.UpdatedAt,
            UpdatedBy = template.UpdatedBy,
            TenantId = template.TenantId
        };
    }

    private ReportExecutionDto MapToExecutionDto(dynamic execution)
    {
        return new ReportExecutionDto
        {
            Id = execution.Id,
            ScheduleId = execution.ScheduleId,
            ScheduleName = execution.Schedule?.Name ?? "",
            TemplateId = execution.TemplateId,
            TemplateName = execution.Template?.Name ?? "",
            Status = execution.Status,
            StartedAt = execution.StartedAt,
            CompletedAt = execution.CompletedAt,
            ErrorMessage = execution.ErrorMessage,
            OutputFormat = execution.OutputFormat,
            FileSizeBytes = execution.FileSizeBytes,
            FilePath = execution.FilePath,
            Parameters = JsonSerializer.Deserialize<Dictionary<string, object>>(execution.Parameters ?? "{}") ?? new Dictionary<string, object>(),
            ExecutedBy = execution.ExecutedBy,
            TenantId = execution.TenantId
        };
    }

    private GeneratedReportDto MapToGeneratedReportDto(dynamic execution)
    {
        return new GeneratedReportDto
        {
            Id = execution.Id,
            Name = execution.Template?.Name ?? "Report",
            Description = execution.Template?.Description ?? "",
            TemplateId = execution.TemplateId,
            TemplateName = execution.Template?.Name ?? "",
            Status = execution.Status,
            GeneratedAt = execution.StartedAt,
            GeneratedBy = execution.ExecutedBy,
            OutputFormat = execution.OutputFormat,
            FileSizeBytes = execution.FileSizeBytes ?? 0,
            FilePath = execution.FilePath,
            Metadata = new Dictionary<string, object>(),
            Recipients = new List<string>(),
            IsDistributed = false,
            ExpiresAt = execution.StartedAt.AddDays(30) // 30 days retention
        };
    }

    private dynamic MapFromParameterDto(ReportParameterDto dto)
    {
        // This would map to actual ReportParameter entity
        return new { };
    }
}
