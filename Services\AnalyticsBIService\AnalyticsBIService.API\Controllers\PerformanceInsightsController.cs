using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Performance;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Controller for performance insights and role-based analytics
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PerformanceInsightsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PerformanceInsightsController> _logger;

    public PerformanceInsightsController(IMediator mediator, ILogger<PerformanceInsightsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get comprehensive performance insights for the current user
    /// </summary>
    /// <param name="userType">User type for role-specific insights</param>
    /// <returns>Performance insights</returns>
    [HttpGet]
    public async Task<ActionResult<PerformanceInsightsDto>> GetPerformanceInsights(
        [FromQuery] UserType? userType = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var currentUserType = userType ?? GetCurrentUserType();

            if (!userId.HasValue || !currentUserType.HasValue)
            {
                return BadRequest("User ID and User Type are required");
            }

            _logger.LogInformation("Getting performance insights for user {UserId} of type {UserType}", 
                userId, currentUserType);

            var query = new GetPerformanceInsightsQuery(userId.Value, currentUserType.Value);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance insights");
            return StatusCode(500, "An error occurred while retrieving performance insights");
        }
    }

    /// <summary>
    /// Get performance insights for a specific user (admin only)
    /// </summary>
    /// <param name="userId">Target user ID</param>
    /// <param name="userType">User type</param>
    /// <returns>Performance insights</returns>
    [HttpGet("user/{userId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceInsightsDto>> GetUserPerformanceInsights(
        Guid userId,
        [FromQuery] UserType userType)
    {
        try
        {
            _logger.LogInformation("Admin getting performance insights for user {UserId} of type {UserType}", 
                userId, userType);

            var query = new GetPerformanceInsightsQuery(userId, userType);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance insights for user {UserId}", userId);
            return StatusCode(500, "An error occurred while retrieving performance insights");
        }
    }

    /// <summary>
    /// Get quote to order conversion rate
    /// </summary>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Conversion rate percentage</returns>
    [HttpGet("conversion-rate")]
    public async Task<ActionResult<decimal>> GetQuoteToOrderConversionRate(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (!userId.HasValue)
            {
                return BadRequest("User ID is required");
            }

            _logger.LogInformation("Getting quote to order conversion rate for user {UserId}", userId);

            var query = new GetQuoteToOrderConversionRateQuery(userId.Value, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quote to order conversion rate");
            return StatusCode(500, "An error occurred while calculating conversion rate");
        }
    }

    /// <summary>
    /// Get expired RFQ ratio
    /// </summary>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Expired RFQ ratio percentage</returns>
    [HttpGet("expired-rfq-ratio")]
    public async Task<ActionResult<decimal>> GetExpiredRFQRatio(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (!userId.HasValue)
            {
                return BadRequest("User ID is required");
            }

            _logger.LogInformation("Getting expired RFQ ratio for user {UserId}", userId);

            var query = new GetExpiredRFQRatioQuery(userId.Value, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expired RFQ ratio");
            return StatusCode(500, "An error occurred while calculating expired RFQ ratio");
        }
    }

    /// <summary>
    /// Get driver performance metrics
    /// </summary>
    /// <param name="driverId">Driver ID (optional, defaults to current user if they are a driver)</param>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Driver performance metrics</returns>
    [HttpGet("driver-performance")]
    public async Task<ActionResult<DriverPerformanceDto>> GetDriverPerformance(
        [FromQuery] Guid? driverId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var targetDriverId = driverId ?? GetCurrentUserId();
            if (!targetDriverId.HasValue)
            {
                return BadRequest("Driver ID is required");
            }

            _logger.LogInformation("Getting driver performance for driver {DriverId}", targetDriverId);

            var query = new GetDriverPerformanceQuery(targetDriverId.Value, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver performance");
            return StatusCode(500, "An error occurred while retrieving driver performance");
        }
    }

    /// <summary>
    /// Get milestone compliance rate
    /// </summary>
    /// <param name="fromDate">Start date (optional)</param>
    /// <param name="toDate">End date (optional)</param>
    /// <returns>Milestone compliance rate percentage</returns>
    [HttpGet("milestone-compliance")]
    public async Task<ActionResult<decimal>> GetMilestoneComplianceRate(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (!userId.HasValue)
            {
                return BadRequest("User ID is required");
            }

            _logger.LogInformation("Getting milestone compliance rate for user {UserId}", userId);

            var query = new GetMilestoneComplianceRateQuery(userId.Value, fromDate, toDate);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone compliance rate");
            return StatusCode(500, "An error occurred while calculating milestone compliance rate");
        }
    }

    /// <summary>
    /// Get performance trends
    /// </summary>
    /// <param name="userType">User type for role-specific trends</param>
    /// <param name="period">Time period for grouping (Daily, Weekly, Monthly)</param>
    /// <returns>List of performance trend data points</returns>
    [HttpGet("trends")]
    public async Task<ActionResult<List<PerformanceTrendDto>>> GetPerformanceTrends(
        [FromQuery] UserType? userType = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var userId = GetCurrentUserId();
            var currentUserType = userType ?? GetCurrentUserType();

            if (!userId.HasValue || !currentUserType.HasValue)
            {
                return BadRequest("User ID and User Type are required");
            }

            _logger.LogInformation("Getting performance trends for user {UserId} with period {Period}", 
                userId, period);

            var query = new GetPerformanceTrendsQuery(userId.Value, currentUserType.Value, period);
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance trends");
            return StatusCode(500, "An error occurred while retrieving performance trends");
        }
    }

    // Helper methods
    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private UserType? GetCurrentUserType()
    {
        var userTypeClaim = User.FindFirst("UserType")?.Value;
        return Enum.TryParse<UserType>(userTypeClaim, out var userType) ? userType : null;
    }
}
