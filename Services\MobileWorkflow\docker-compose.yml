version: '3.8'

services:
  # TimescaleDB Database
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: mobileworkflow-timescaledb
    environment:
      POSTGRES_DB: mobileworkflow_db
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/01-setup.sql
    networks:
      - mobileworkflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timescale -d mobileworkflow_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: mobileworkflow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mobileworkflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: mobileworkflow-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - mobileworkflow-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Mobile & Workflow Service
  mobileworkflow-service:
    build:
      context: .
      dockerfile: MobileWorkflow.API/Dockerfile
    container_name: mobileworkflow-service
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=timescaledb;Port=5432;Database=mobileworkflow_db;Username=timescale;Password=timescale
      - Services__IdentityService__BaseUrl=http://host.docker.internal:5001
      - Services__UserManagement__BaseUrl=http://host.docker.internal:5002
      - Services__TripManagement__BaseUrl=http://host.docker.internal:5003
      - Services__OrderManagement__BaseUrl=http://host.docker.internal:5004
      - Services__NetworkFleetManagement__BaseUrl=http://host.docker.internal:5005
      - Services__CommunicationNotification__BaseUrl=http://host.docker.internal:5006
      - Services__FinancialPayment__BaseUrl=http://host.docker.internal:5007
      - Services__SubscriptionManagement__BaseUrl=http://host.docker.internal:5008
      - Redis__ConnectionString=redis:6379
      - RabbitMQ__ConnectionString=amqp://admin:admin123@rabbitmq:5672/
    ports:
      - "5014:80"
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - mobileworkflow-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: mobileworkflow-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - mobileworkflow-service
    networks:
      - mobileworkflow-network
    restart: unless-stopped

  # Prometheus Monitoring (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: mobileworkflow-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mobileworkflow-network
    restart: unless-stopped

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: mobileworkflow-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - mobileworkflow-network
    restart: unless-stopped

volumes:
  timescaledb_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mobileworkflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
