namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Report schedule data transfer object for AnalyticsBIService
/// </summary>
public class ReportScheduleDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string ReportType { get; set; } = string.Empty;
    public string ReportName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ScheduleExpression { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> ExportFormats { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? NextExecutionTime { get; set; }
}

/// <summary>
/// Report schedule configuration data transfer object
/// </summary>
public class ReportScheduleConfigDto
{
    public string ScheduleType { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly", "Custom"
    public string CronExpression { get; set; } = string.Empty;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string TimeZone { get; set; } = "UTC";
    public bool IsEnabled { get; set; } = true;
    public List<string> Recipients { get; set; } = new();
    public List<string> ExportFormats { get; set; } = new();
    public Dictionary<string, object> NotificationSettings { get; set; } = new();
}

/// <summary>
/// Visualization configuration data transfer object
/// </summary>
public class VisualizationConfigDto
{
    public string Type { get; set; } = string.Empty; // "Chart", "Table", "Graph", "Map"
    public string ChartType { get; set; } = string.Empty; // "Bar", "Line", "Pie", "Scatter"
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> DataSeries { get; set; } = new();
    public Dictionary<string, string> Styling { get; set; } = new();
    public bool IsInteractive { get; set; } = true;
}
