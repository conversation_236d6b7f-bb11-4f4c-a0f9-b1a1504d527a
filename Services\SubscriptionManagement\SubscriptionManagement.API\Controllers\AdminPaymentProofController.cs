using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.VerifyPaymentProof;
using SubscriptionManagement.Application.Commands.RejectPaymentProof;
using SubscriptionManagement.Application.Queries.GetPaymentProofs;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    [Route("api/admin/payment-proofs")]
    public class AdminPaymentProofController : BaseController
    {
        /// <summary>
        /// Get payment proofs with filtering options (Admin only)
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(PaymentProofListDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPaymentProofs(
            [FromQuery] PaymentProofStatus? status = null,
            [FromQuery] Guid? userId = null,
            [FromQuery] Guid? subscriptionId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = new GetPaymentProofsQuery
                {
                    Status = status,
                    UserId = userId,
                    SubscriptionId = subscriptionId,
                    FromDate = fromDate,
                    ToDate = toDate,
                    PageNumber = pageNumber,
                    PageSize = Math.Min(pageSize, 100) // Limit page size to 100
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get pending payment proofs for review (Admin only)
        /// </summary>
        [HttpGet("pending")]
        [ProducesResponseType(typeof(PaymentProofListDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPendingPaymentProofs(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = new GetPaymentProofsQuery
                {
                    Status = PaymentProofStatus.Pending,
                    PageNumber = pageNumber,
                    PageSize = Math.Min(pageSize, 100)
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Verify a payment proof (Admin only)
        /// </summary>
        [HttpPut("{id}/verify")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> VerifyPaymentProof(Guid id, [FromBody] VerifyPaymentProofRequest request)
        {
            try
            {
                var adminUserId = GetCurrentUserId();
                var command = new VerifyPaymentProofCommand
                {
                    PaymentProofId = id,
                    VerifiedByUserId = adminUserId,
                    VerificationNotes = request.VerificationNotes
                };

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Payment proof verified successfully", paymentProofId = id });
                }

                return BadRequest(new { message = "Failed to verify payment proof" });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Reject a payment proof (Admin only)
        /// </summary>
        [HttpPut("{id}/reject")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RejectPaymentProof(Guid id, [FromBody] RejectPaymentProofRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.RejectionReason))
                {
                    return BadRequest(new { message = "Rejection reason is required" });
                }

                var adminUserId = GetCurrentUserId();
                var command = new RejectPaymentProofCommand
                {
                    PaymentProofId = id,
                    RejectedByUserId = adminUserId,
                    RejectionReason = request.RejectionReason,
                    VerificationNotes = request.VerificationNotes
                };

                var result = await Mediator.Send(command);

                if (result)
                {
                    return Ok(new { message = "Payment proof rejected successfully", paymentProofId = id, reason = request.RejectionReason });
                }

                return BadRequest(new { message = "Failed to reject payment proof" });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get payment proof statistics (Admin only)
        /// </summary>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPaymentProofStatistics()
        {
            try
            {
                // TODO: Implement payment proof statistics
                return Ok(new { message = "Payment proof statistics endpoint - to be implemented" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}
