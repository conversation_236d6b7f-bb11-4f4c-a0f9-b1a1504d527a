using TripManagement.Domain.Entities;

namespace TripManagement.Domain.Repositories;

public interface IEmergencyAlertRepository
{
    Task<EmergencyAlert?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetAlertsByStatusAsync(EmergencyAlertStatus status, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetAlertsByTypeAsync(string alertType, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetAlertsBySeverityAsync(string severity, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetAlertsInTimeRangeAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetUnacknowledgedAlertsAsync(CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetEscalatedAlertsAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveAlertCountByDriverAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<List<EmergencyAlert>> GetRecentAlertsByDriverAsync(Guid driverId, int hours = 24, CancellationToken cancellationToken = default);
    Task AddAsync(EmergencyAlert alert, CancellationToken cancellationToken = default);
    void Update(EmergencyAlert alert);
    void Delete(EmergencyAlert alert);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
}
