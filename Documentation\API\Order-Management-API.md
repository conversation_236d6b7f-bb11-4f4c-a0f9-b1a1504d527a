# 📦 Order Management Service API Documentation

## 📋 Overview

The Order Management Service handles RFQ (Request for Quote) creation, bid management, order processing, and invoice generation. This service is 96% complete and production-ready.

**Base URL**: `/api/v1/orders` (via API Gateway)  
**Direct URL**: `http://localhost:5004`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation           | Permission              | Roles                     |
| ------------------- | ----------------------- | ------------------------- |
| Create RFQ          | `orders.rfq.create`     | Shipper, Broker           |
| View RFQ            | `orders.rfq.read`       | All authenticated users   |
| Publish RFQ         | `orders.rfq.publish`    | Shipper, Broker           |
| Submit Bid          | `orders.bid.create`     | Transporter, Broker       |
| Accept/Reject Bid   | `orders.bid.manage`     | Ship<PERSON>, Broker           |
| Create Order        | `orders.create`         | Ship<PERSON>, <PERSON>roker           |
| View Orders         | `orders.read`           | Order participants        |
| Update Order Status | `orders.update`         | Admin, Order participants |
| Force Award         | `orders.force_award`    | Admin only                |
| Generate Invoice    | `orders.invoice.create` | System, Admin             |

## 📊 Core Endpoints

### 1. RFQ Management

#### Create RFQ

```http
POST /api/v1/orders/rfq
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Transport goods from Mumbai to Delhi",
  "description": "Urgent delivery required for electronics",
  "cargoDetails": {
    "cargoType": "Electronics|Perishable|Hazardous|General|Automotive|Textile",
    "weight": 1500.5,
    "weightUnit": "KG|LB|TON",
    "volume": 25.0,
    "volumeUnit": "CBM|CFT",
    "quantity": 100,
    "quantityUnit": "Pieces|Boxes|Pallets|Containers",
    "value": 50000.0,
    "currency": "INR|USD|EUR",
    "packagingType": "Boxes|Pallets|Loose|Containers",
    "specialInstructions": "Handle with care, fragile items",
    "hazardousDetails": null
  },
  "routeDetails": {
    "pickupLocation": {
      "address": "123 Industrial Area, Mumbai, Maharashtra, India",
      "coordinates": {
        "latitude": 19.0760,
        "longitude": 72.8777
      },
      "contactPerson": "John Doe",
      "contactPhone": "+91-9876543210",
      "contactEmail": "<EMAIL>",
      "preferredTimeSlot": {
        "startTime": "09:00",
        "endTime": "17:00"
      },
      "specialInstructions": "Loading dock available"
    },
    "deliveryLocation": {
      "address": "456 Business District, Delhi, India",
      "coordinates": {
        "latitude": 28.6139,
        "longitude": 77.2090
      },
      "contactPerson": "Jane Smith",
      "contactPhone": "+91-9876543211",
      "contactEmail": "<EMAIL>",
      "preferredTimeSlot": {
        "startTime": "10:00",
        "endTime": "18:00"
      },
      "specialInstructions": "Security clearance required"
    },
    "intermediateStops": [
      {
        "address": "789 Warehouse, Pune, Maharashtra, India",
        "stopType": "Pickup|Delivery|Rest",
        "estimatedDuration": 60,
        "specialInstructions": "Additional cargo pickup"
      }
    ],
    "totalDistance": 1400.5,
    "estimatedDuration": 1440
  },
  "timeRequirements": {
    "pickupDate": "2024-02-01T09:00:00Z",
    "deliveryDate": "2024-02-02T18:00:00Z",
    "isFlexible": true,
    "urgencyLevel": "Low|Medium|High|Critical"
  },
  "vehicleRequirements": {
    "vehicleType": "Truck|Trailer|Container|Van|Bike",
    "vehicleSize": "Small|Medium|Large|XLarge",
    "specialFeatures": ["GPS Tracking", "Temperature Control", "Security"],
    "driverRequirements": ["Valid License", "Experience > 5 years"]
  },
  "budgetExpectations": {
    "minBudget": 15000.0,
    "maxBudget": 25000.0,
    "currency": "INR",
    "paymentTerms": "Advance|COD|Credit30|Credit60",
    "includesTax": true
  },
  "milestoneTemplate": "standard-cargo",
  "biddingDeadline": "2024-01-25T23:59:59Z",
  "autoAward": false,
  "visibility": "Public|Private|Network"
}
```

**Response:**

```json
{
  "id": "rfq-uuid",
  "rfqNumber": "RFQ-2024-001234",
  "status": "Draft|Published|Closed|Awarded|Cancelled",
  "createdAt": "2024-01-20T10:00:00Z",
  "publishedAt": null,
  "biddingDeadline": "2024-01-25T23:59:59Z",
  "estimatedValue": 20000.0,
  "bidCount": 0,
  "viewCount": 0,
  "shortlistedCarriers": []
}
```

#### Get RFQ Details

```http
GET /api/v1/orders/rfq/{rfqId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "rfq-uuid",
  "rfqNumber": "RFQ-2024-001234",
  "title": "Transport goods from Mumbai to Delhi",
  "description": "Urgent delivery required for electronics",
  "status": "Published",
  "createdBy": "user-uuid",
  "createdAt": "2024-01-20T10:00:00Z",
  "publishedAt": "2024-01-20T11:00:00Z",
  "biddingDeadline": "2024-01-25T23:59:59Z",
  "cargoDetails": {
    /* ... */
  },
  "routeDetails": {
    /* ... */
  },
  "timeRequirements": {
    /* ... */
  },
  "vehicleRequirements": {
    /* ... */
  },
  "budgetExpectations": {
    /* ... */
  },
  "milestoneTemplate": "standard-cargo",
  "visibility": "Public",
  "bidCount": 5,
  "viewCount": 25,
  "shortlistedCarriers": ["carrier-uuid-1", "carrier-uuid-2"],
  "attachments": [
    {
      "id": "attachment-uuid",
      "fileName": "cargo_details.pdf",
      "fileUrl": "https://storage.tli.com/rfq/attachment-uuid.pdf",
      "fileSize": 1024000,
      "uploadedAt": "2024-01-20T10:30:00Z"
    }
  ],
  "timeline": [
    {
      "event": "RFQCreated",
      "timestamp": "2024-01-20T10:00:00Z",
      "description": "RFQ created successfully"
    },
    {
      "event": "RFQPublished",
      "timestamp": "2024-01-20T11:00:00Z",
      "description": "RFQ published to network"
    }
  ]
}
```

#### Publish RFQ

```http
PUT /api/v1/orders/rfq/{rfqId}/publish
Authorization: Bearer <token>
Content-Type: application/json

{
  "targetCarriers": ["carrier-uuid-1", "carrier-uuid-2"],
  "broadcastToNetwork": true,
  "publishMessage": "Urgent requirement, please submit competitive bids"
}
```

#### Close RFQ

```http
PUT /api/v1/orders/rfq/{rfqId}/close
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Bidding deadline reached",
  "notifyBidders": true
}
```

#### Get Published RFQs

```http
GET /api/v1/orders/rfq/published
Authorization: Bearer <token>
```

**Query Parameters:**

- `cargoType`: Filter by cargo type
- `routeFrom`: Filter by pickup location
- `routeTo`: Filter by delivery location
- `minBudget`: Minimum budget filter
- `maxBudget`: Maximum budget filter
- `urgencyLevel`: Filter by urgency
- `vehicleType`: Filter by vehicle type
- `pickupDateFrom`: Filter by pickup date range
- `pickupDateTo`: Filter by pickup date range
- `page`: Page number
- `pageSize`: Items per page
- `sortBy`: Sort field
- `sortDirection`: asc|desc

### 2. Bid Management

#### Submit Bid

```http
POST /api/v1/orders/rfq/{rfqId}/bids
Authorization: Bearer <token>
Content-Type: application/json

{
  "quotedPrice": 18000.0,
  "currency": "INR",
  "validityPeriod": 7,
  "proposedPickupDate": "2024-02-01T10:00:00Z",
  "proposedDeliveryDate": "2024-02-02T16:00:00Z",
  "vehicleDetails": {
    "vehicleId": "vehicle-uuid",
    "vehicleNumber": "MH-01-AB-1234",
    "vehicleType": "Truck",
    "capacity": "10 Ton",
    "features": ["GPS Tracking", "Temperature Control"]
  },
  "driverDetails": {
    "driverId": "driver-uuid",
    "driverName": "Rajesh Kumar",
    "driverPhone": "+91-9876543210",
    "experience": "8 years",
    "rating": 4.5
  },
  "serviceInclusions": [
    "Loading/Unloading",
    "Insurance Coverage",
    "Real-time Tracking",
    "24/7 Support"
  ],
  "terms": "Payment within 30 days of delivery",
  "specialOffers": "10% discount for repeat customers",
  "comments": "We guarantee on-time delivery with live tracking",
  "attachments": [
    {
      "fileName": "vehicle_documents.pdf",
      "fileUrl": "https://storage.tli.com/bids/attachment-uuid.pdf"
    }
  ]
}
```

**Response:**

```json
{
  "id": "bid-uuid",
  "bidNumber": "BID-2024-001234",
  "rfqId": "rfq-uuid",
  "bidderId": "user-uuid",
  "status": "Submitted|UnderReview|Shortlisted|Accepted|Rejected|Withdrawn",
  "quotedPrice": 18000.0,
  "submittedAt": "2024-01-22T14:30:00Z",
  "validUntil": "2024-01-29T14:30:00Z",
  "ranking": 2,
  "competitiveScore": 85.5
}
```

#### Get Bid Details

```http
GET /api/v1/orders/bids/{bidId}
Authorization: Bearer <token>
```

#### Accept Bid

```http
PUT /api/v1/orders/bids/{bidId}/accept
Authorization: Bearer <token>
Content-Type: application/json

{
  "acceptanceComments": "Best price and good track record",
  "negotiatedPrice": 17500.0,
  "paymentTerms": "50% advance, 50% on delivery",
  "specialConditions": "Insurance must be maintained throughout transit"
}
```

#### Reject Bid

```http
PUT /api/v1/orders/bids/{bidId}/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "rejectionReason": "Price too high",
  "rejectionComments": "Please submit a more competitive quote",
  "allowResubmission": true
}
```

#### Withdraw Bid

```http
PUT /api/v1/orders/bids/{bidId}/withdraw
Authorization: Bearer <token>
Content-Type: application/json

{
  "withdrawalReason": "Vehicle not available",
  "comments": "Unexpected maintenance required"
}
```

### 3. Order Management

#### Create Order from Accepted Bid

```http
POST /api/v1/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "rfqId": "rfq-uuid",
  "acceptedBidId": "bid-uuid",
  "finalPrice": 17500.0,
  "paymentTerms": "50% advance, 50% on delivery",
  "specialInstructions": "Handle with extra care",
  "milestones": [
    {
      "name": "Pickup Completed",
      "description": "Cargo picked up from source",
      "payoutPercentage": 30.0,
      "requiredDocuments": ["Pickup Receipt", "Loading Photos"]
    },
    {
      "name": "In Transit",
      "description": "Cargo in transit to destination",
      "payoutPercentage": 20.0,
      "requiredDocuments": ["Transit Update"]
    },
    {
      "name": "Delivery Completed",
      "description": "Cargo delivered successfully",
      "payoutPercentage": 50.0,
      "requiredDocuments": ["POD", "Delivery Photos", "Customer Signature"]
    }
  ]
}
```

**Response:**

```json
{
  "id": "order-uuid",
  "orderNumber": "ORD-2024-001234",
  "status": "Created|Confirmed|InProgress|Completed|Cancelled",
  "rfqId": "rfq-uuid",
  "acceptedBidId": "bid-uuid",
  "shipperId": "shipper-uuid",
  "transporterId": "transporter-uuid",
  "finalPrice": 17500.0,
  "createdAt": "2024-01-25T16:00:00Z",
  "estimatedCompletionDate": "2024-02-02T18:00:00Z"
}
```

#### Get Order Details

```http
GET /api/v1/orders/{orderId}
Authorization: Bearer <token>
```

#### Get Order Timeline

```http
GET /api/v1/orders/{orderId}/timeline
Authorization: Bearer <token>
```

**Response:**

```json
{
  "orderId": "order-uuid",
  "timeline": [
    {
      "id": "timeline-uuid",
      "event": "OrderCreated",
      "timestamp": "2024-01-25T16:00:00Z",
      "description": "Order created from accepted bid",
      "performedBy": "shipper-uuid",
      "metadata": {
        "bidId": "bid-uuid",
        "finalPrice": 17500.0
      }
    },
    {
      "id": "timeline-uuid-2",
      "event": "OrderConfirmed",
      "timestamp": "2024-01-25T16:30:00Z",
      "description": "Order confirmed by transporter",
      "performedBy": "transporter-uuid",
      "metadata": {
        "vehicleAssigned": "vehicle-uuid",
        "driverAssigned": "driver-uuid"
      }
    }
  ],
  "milestones": [
    {
      "id": "milestone-uuid",
      "name": "Pickup Completed",
      "status": "Pending|InProgress|Completed|Failed",
      "payoutPercentage": 30.0,
      "completedAt": null,
      "documents": [],
      "location": {
        "latitude": 19.076,
        "longitude": 72.8777,
        "address": "123 Industrial Area, Mumbai"
      }
    }
  ]
}
```

#### Confirm Order

```http
PUT /api/v1/orders/{orderId}/confirm
Authorization: Bearer <token>
Content-Type: application/json

{
  "vehicleId": "vehicle-uuid",
  "driverId": "driver-uuid",
  "estimatedPickupTime": "2024-02-01T10:00:00Z",
  "estimatedDeliveryTime": "2024-02-02T16:00:00Z",
  "confirmationComments": "Vehicle and driver assigned, ready to proceed"
}
```

#### Update Order Status

```http
PUT /api/v1/orders/{orderId}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "InProgress",
  "comments": "Trip started, en route to pickup location",
  "location": {
    "latitude": 19.0760,
    "longitude": 72.8777
  },
  "timestamp": "2024-02-01T09:00:00Z"
}
```

#### Force Award Order (Admin Only)

```http
PUT /api/v1/orders/rfq/{rfqId}/force-award
Authorization: Bearer <token>
Content-Type: application/json

{
  "selectedBidId": "bid-uuid",
  "reason": "Best overall proposal considering all factors",
  "adminComments": "Manual award due to special circumstances",
  "notifyParticipants": true
}
```

### 4. Search & Filtering

#### Get Orders with Filtering

```http
GET /api/v1/orders
Authorization: Bearer <token>
```

**Query Parameters:**

- `status`: Filter by order status
- `paymentStatus`: Filter by payment status
- `fromDate`: Filter by date range (from)
- `toDate`: Filter by date range (to)
- `searchTerm`: Search in order details
- `isUrgent`: Filter urgent orders
- `shipperId`: Filter by shipper
- `transporterId`: Filter by transporter
- `minValue`: Minimum order value
- `maxValue`: Maximum order value
- `cargoType`: Filter by cargo type
- `routeFrom`: Filter by pickup location
- `routeTo`: Filter by delivery location
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `sortBy`: Sort field (default: "CreatedAt")
- `sortDirection`: asc|desc (default: "desc")

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/orders/hub`

**Events:**

- `RFQPublished` - New RFQ published
- `BidSubmitted` - New bid submitted
- `BidAccepted` - Bid accepted
- `BidRejected` - Bid rejected
- `OrderCreated` - New order created
- `OrderStatusChanged` - Order status updated
- `MilestoneCompleted` - Milestone completed
- `PaymentStatusChanged` - Payment status updated

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/orders/hub')
  .build()

connection.on('BidSubmitted', (data) => {
  console.log('New bid received:', data)
  // Update bid list in UI
})

connection.on('OrderStatusChanged', (data) => {
  console.log('Order status updated:', data)
  // Update order tracking UI
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                    | Description                         |
| ---- | -------------------------- | ----------------------------------- |
| 400  | `INVALID_RFQ_DATA`         | Invalid RFQ data provided           |
| 400  | `BIDDING_CLOSED`           | Bidding period has ended            |
| 400  | `INVALID_BID_AMOUNT`       | Bid amount outside acceptable range |
| 401  | `UNAUTHORIZED`             | Authentication required             |
| 403  | `INSUFFICIENT_PERMISSIONS` | Insufficient permissions            |
| 404  | `RFQ_NOT_FOUND`            | RFQ not found                       |
| 404  | `BID_NOT_FOUND`            | Bid not found                       |
| 404  | `ORDER_NOT_FOUND`          | Order not found                     |
| 409  | `RFQ_ALREADY_PUBLISHED`    | RFQ is already published            |
| 409  | `BID_ALREADY_SUBMITTED`    | Bid already submitted for this RFQ  |
| 409  | `ORDER_ALREADY_EXISTS`     | Order already exists for this bid   |
| 422  | `VALIDATION_FAILED`        | Input validation failed             |
| 429  | `RATE_LIMIT_EXCEEDED`      | Too many requests                   |

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Input validation failed",
    "details": [
      {
        "field": "cargoDetails.weight",
        "message": "Weight must be greater than 0"
      }
    ],
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
