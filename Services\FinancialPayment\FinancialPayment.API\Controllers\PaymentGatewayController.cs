using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PaymentGatewayController : ControllerBase
{
    private readonly IPaymentGatewayFactory _gatewayFactory;
    private readonly ILogger<PaymentGatewayController> _logger;

    public PaymentGatewayController(
        IPaymentGatewayFactory gatewayFactory,
        ILogger<PaymentGatewayController> logger)
    {
        _gatewayFactory = gatewayFactory;
        _logger = logger;
    }

    /// <summary>
    /// Get available payment gateways
    /// </summary>
    [HttpGet("available")]
    public ActionResult<IEnumerable<string>> GetAvailableGateways()
    {
        try
        {
            var gateways = _gatewayFactory.GetAvailableGateways();
            return Ok(gateways);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving available payment gateways");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process payment through specific gateway
    /// </summary>
    [HttpPost("process")]
    [Authorize(Roles = "Admin,Broker,TransportCompany")]
    public async Task<ActionResult<PaymentResult>> ProcessPayment([FromBody] ProcessPaymentRequest request)
    {
        try
        {
            var gateway = _gatewayFactory.GetGateway(request.GatewayName);
            
            var paymentRequest = new PaymentRequest
            {
                Amount = new Money(request.Amount, request.Currency),
                UserId = request.UserId,
                PaymentMethodId = request.PaymentMethodId,
                Description = request.Description,
                Metadata = request.Metadata ?? new Dictionary<string, object>(),
                CallbackUrl = request.CallbackUrl,
                CancelUrl = request.CancelUrl
            };

            var result = await gateway.ProcessPaymentAsync(paymentRequest);
            
            _logger.LogInformation("Payment processed via {Gateway} for user {UserId}: {Success}",
                request.GatewayName, request.UserId, result.IsSuccess);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment via {Gateway} for user {UserId}",
                request.GatewayName, request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process refund through specific gateway
    /// </summary>
    [HttpPost("refund")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<RefundResult>> ProcessRefund([FromBody] ProcessRefundRequest request)
    {
        try
        {
            var gateway = _gatewayFactory.GetGateway(request.GatewayName);
            
            var refundRequest = new RefundRequest
            {
                TransactionId = request.TransactionId,
                Amount = new Money(request.Amount, request.Currency),
                Reason = request.Reason,
                Metadata = request.Metadata ?? new Dictionary<string, object>()
            };

            var result = await gateway.ProcessRefundAsync(refundRequest);
            
            _logger.LogInformation("Refund processed via {Gateway} for transaction {TransactionId}: {Success}",
                request.GatewayName, request.TransactionId, result.IsSuccess);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund via {Gateway} for transaction {TransactionId}",
                request.GatewayName, request.TransactionId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create payment intent through specific gateway
    /// </summary>
    [HttpPost("create-intent")]
    [Authorize(Roles = "Admin,Broker,TransportCompany")]
    public async Task<ActionResult<PaymentResult>> CreatePaymentIntent([FromBody] CreatePaymentIntentRequest request)
    {
        try
        {
            var gateway = _gatewayFactory.GetGateway(request.GatewayName);
            
            var intentRequest = new PaymentIntentRequest
            {
                Amount = new Money(request.Amount, request.Currency),
                UserId = request.UserId,
                PaymentMethodId = request.PaymentMethodId,
                Description = request.Description,
                Metadata = request.Metadata ?? new Dictionary<string, object>(),
                AutoCapture = request.AutoCapture
            };

            var result = await gateway.CreatePaymentIntentAsync(intentRequest);
            
            _logger.LogInformation("Payment intent created via {Gateway} for user {UserId}: {Success}",
                request.GatewayName, request.UserId, result.IsSuccess);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent via {Gateway} for user {UserId}",
                request.GatewayName, request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment status from specific gateway
    /// </summary>
    [HttpGet("status/{gatewayName}/{transactionId}")]
    public async Task<ActionResult<PaymentStatus>> GetPaymentStatus(string gatewayName, string transactionId)
    {
        try
        {
            var gateway = _gatewayFactory.GetGateway(gatewayName);
            var status = await gateway.GetPaymentStatusAsync(transactionId);
            
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment status from {Gateway} for transaction {TransactionId}",
                gatewayName, transactionId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate webhook from specific gateway
    /// </summary>
    [HttpPost("webhook/{gatewayName}")]
    [AllowAnonymous]
    public async Task<ActionResult> ValidateWebhook(string gatewayName, [FromBody] WebhookRequest request)
    {
        try
        {
            var gateway = _gatewayFactory.GetGateway(gatewayName);
            var isValid = await gateway.ValidateWebhookAsync(request.Payload, request.Signature);
            
            if (isValid)
            {
                _logger.LogInformation("Valid webhook received from {Gateway}", gatewayName);
                return Ok();
            }
            else
            {
                _logger.LogWarning("Invalid webhook received from {Gateway}", gatewayName);
                return BadRequest("Invalid webhook signature");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating webhook from {Gateway}", gatewayName);
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request/Response DTOs
public class ProcessPaymentRequest
{
    public string GatewayName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public Guid UserId { get; set; }
    public string PaymentMethodId { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
    public string? CallbackUrl { get; set; }
    public string? CancelUrl { get; set; }
}

public class ProcessRefundRequest
{
    public string GatewayName { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public string Reason { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
}

public class CreatePaymentIntentRequest
{
    public string GatewayName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public Guid UserId { get; set; }
    public string? PaymentMethodId { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
    public bool AutoCapture { get; set; } = true;
}

public class WebhookRequest
{
    public string Payload { get; set; } = string.Empty;
    public string Signature { get; set; } = string.Empty;
}
