using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Services;

public class MilestoneValidationService : IMilestoneValidationService
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMilestoneStepRepository _stepRepository;
    private readonly IMilestonePayoutRuleRepository _payoutRuleRepository;
    private readonly IRoleTemplateMappingsRepository _roleMappingsRepository;
    private readonly ILogger<MilestoneValidationService> _logger;

    public MilestoneValidationService(
        IMilestoneTemplateRepository templateRepository,
        IMilestoneStepRepository stepRepository,
        IMilestonePayoutRuleRepository payoutRuleRepository,
        IRoleTemplateMappingsRepository roleMappingsRepository,
        ILogger<MilestoneValidationService> logger)
    {
        _templateRepository = templateRepository;
        _stepRepository = stepRepository;
        _payoutRuleRepository = payoutRuleRepository;
        _roleMappingsRepository = roleMappingsRepository;
        _logger = logger;
    }

    public async Task<MilestoneTemplateValidationResult> ValidateTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MilestoneTemplateValidationResult
            {
                IsValid = false,
                Errors = new List<string> { "Template not found" }
            };
        }

        return await ValidateTemplateAsync(template, cancellationToken);
    }

    public async Task<MilestoneTemplateValidationResult> ValidateTemplateAsync(MilestoneTemplate template, CancellationToken cancellationToken = default)
    {
        var result = new MilestoneTemplateValidationResult();
        var errors = new List<string>();
        var warnings = new List<string>();

        // Basic template validation
        errors.AddRange(template.GetValidationErrors());

        // Step sequence validation
        var stepSequenceErrors = await ValidateStepSequenceAsync(template.Id, cancellationToken);
        errors.AddRange(stepSequenceErrors);

        // Payout rules validation
        var payoutErrors = await ValidatePayoutRulesAsync(template.Id, cancellationToken);
        errors.AddRange(payoutErrors);

        // Role mappings validation
        var roleMappingErrors = await ValidateRoleMappingsAsync(template.Id, cancellationToken);
        errors.AddRange(roleMappingErrors);

        // Calculate total payout percentage
        var totalPayoutPercentage = template.Steps
            .SelectMany(s => s.PayoutRules)
            .Sum(r => r.PayoutPercentage);

        // Add warnings
        if (totalPayoutPercentage < 100)
        {
            warnings.Add($"Total payout percentage is {totalPayoutPercentage}%, which is less than 100%");
        }

        if (template.Steps.Count == 0)
        {
            warnings.Add("Template has no milestone steps defined");
        }

        if (!template.Steps.Any(s => s.IsRequired))
        {
            warnings.Add("Template has no required milestone steps");
        }

        result.IsValid = !errors.Any();
        result.Errors = errors;
        result.Warnings = warnings;
        result.TotalPayoutPercentage = totalPayoutPercentage;
        result.ValidationDetails = new Dictionary<string, object>
        {
            { "StepCount", template.Steps.Count },
            { "RequiredStepCount", template.Steps.Count(s => s.IsRequired) },
            { "PayoutRuleCount", template.Steps.SelectMany(s => s.PayoutRules).Count() },
            { "ActiveStepCount", template.Steps.Count(s => s.IsActive) },
            { "RoleMappingCount", template.RoleMappings.Count },
            { "IsActive", template.IsActive },
            { "IsDefault", template.IsDefault }
        };

        return result;
    }

    public async Task<List<string>> ValidateStepSequenceAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();
        var steps = await _stepRepository.GetByTemplateIdAsync(templateId, cancellationToken);

        if (!steps.Any())
        {
            return errors; // Empty is valid
        }

        var sequences = steps.Select(s => s.SequenceNumber).ToList();
        var distinctSequences = sequences.Distinct().ToList();

        // Check for duplicates
        if (sequences.Count != distinctSequences.Count)
        {
            errors.Add("Duplicate sequence numbers found in milestone steps");
        }

        // Check if sequences start from 1 and are consecutive
        var sortedSequences = distinctSequences.OrderBy(s => s).ToList();
        for (int i = 0; i < sortedSequences.Count; i++)
        {
            if (sortedSequences[i] != i + 1)
            {
                errors.Add("Milestone step sequence numbers must be consecutive starting from 1");
                break;
            }
        }

        return errors;
    }

    public async Task<List<string>> ValidatePayoutRulesAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();
        var totalPercentage = await _payoutRuleRepository.GetTotalPayoutPercentageByTemplateAsync(templateId, cancellationToken);

        if (Math.Abs(totalPercentage - 100.0m) > 0.01m)
        {
            errors.Add($"Total payout percentage is {totalPercentage}%, but must equal 100%");
        }

        // Validate individual step payout rules
        var steps = await _stepRepository.GetByTemplateIdAsync(templateId, cancellationToken);
        foreach (var step in steps)
        {
            var stepTotal = await _payoutRuleRepository.GetTotalPayoutPercentageByStepAsync(step.Id, cancellationToken);
            if (stepTotal > 100)
            {
                errors.Add($"Step '{step.Name}' has payout rules totaling {stepTotal}%, which exceeds 100%");
            }
        }

        return errors;
    }

    public async Task<List<string>> ValidateRoleMappingsAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();
        var mappings = await _roleMappingsRepository.GetByTemplateIdAsync(templateId, cancellationToken);

        // Check for duplicate default mappings per role
        var defaultMappings = mappings.Where(m => m.IsDefault).GroupBy(m => m.RoleName);
        foreach (var group in defaultMappings)
        {
            if (group.Count() > 1)
            {
                errors.Add($"Multiple default mappings found for role '{group.Key}'");
            }
        }

        return errors;
    }

    public async Task<bool> CanTemplateBeDeletedAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
        if (template == null)
        {
            return false;
        }

        return template.CanBeDeleted();
    }

    public async Task<bool> CanStepBeDeletedAsync(Guid stepId, CancellationToken cancellationToken = default)
    {
        var step = await _stepRepository.GetByIdAsync(stepId, cancellationToken);
        if (step == null)
        {
            return false;
        }

        return step.CanBeDeleted();
    }

    public async Task<List<string>> GetTemplateUsageWarningsAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var warnings = new List<string>();
        var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);

        if (template == null)
        {
            return warnings;
        }

        if (template.UsageCount > 0)
        {
            warnings.Add($"Template is currently in use by {template.UsageCount} entities");
        }

        if (template.IsDefault)
        {
            warnings.Add("Template is set as default for its type");
        }

        var roleMappings = await _roleMappingsRepository.GetByTemplateIdAsync(templateId, cancellationToken);
        if (roleMappings.Any())
        {
            var roles = string.Join(", ", roleMappings.Select(m => m.RoleName).Distinct());
            warnings.Add($"Template is mapped to roles: {roles}");
        }

        return warnings;
    }

    public async Task<Dictionary<string, object>> GetTemplateHealthCheckAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var healthCheck = new Dictionary<string, object>();

        try
        {
            var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                healthCheck["Status"] = "NotFound";
                return healthCheck;
            }

            var validationResult = await ValidateTemplateAsync(template, cancellationToken);
            var usageWarnings = await GetTemplateUsageWarningsAsync(templateId, cancellationToken);

            healthCheck["Status"] = validationResult.IsValid ? "Healthy" : "Unhealthy";
            healthCheck["IsValid"] = validationResult.IsValid;
            healthCheck["ErrorCount"] = validationResult.Errors.Count;
            healthCheck["WarningCount"] = validationResult.Warnings.Count + usageWarnings.Count;
            healthCheck["TotalPayoutPercentage"] = validationResult.TotalPayoutPercentage;
            healthCheck["StepCount"] = template.Steps.Count;
            healthCheck["ActiveStepCount"] = template.Steps.Count(s => s.IsActive);
            healthCheck["RequiredStepCount"] = template.Steps.Count(s => s.IsRequired);
            healthCheck["PayoutRuleCount"] = template.Steps.SelectMany(s => s.PayoutRules).Count();
            healthCheck["RoleMappingCount"] = template.RoleMappings.Count;
            healthCheck["UsageCount"] = template.UsageCount;
            healthCheck["IsActive"] = template.IsActive;
            healthCheck["IsDefault"] = template.IsDefault;
            healthCheck["LastUpdated"] = template.UpdatedAt ?? template.CreatedAt;

            if (validationResult.Errors.Any())
            {
                healthCheck["Errors"] = validationResult.Errors;
            }

            if (validationResult.Warnings.Any() || usageWarnings.Any())
            {
                healthCheck["Warnings"] = validationResult.Warnings.Concat(usageWarnings).ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check for template: {TemplateId}", templateId);
            healthCheck["Status"] = "Error";
            healthCheck["Error"] = ex.Message;
        }

        return healthCheck;
    }
}
