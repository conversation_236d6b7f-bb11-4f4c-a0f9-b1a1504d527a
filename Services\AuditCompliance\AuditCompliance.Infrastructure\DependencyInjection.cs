using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Infrastructure.Persistence;
using AuditCompliance.Infrastructure.Repositories;
using AuditCompliance.Infrastructure.Services;
using AuditCompliance.Infrastructure.EventHandlers;
using AuditCompliance.Infrastructure.MultiTenant;
using Shared.Messaging;
using MediatR;

namespace AuditCompliance.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<AuditComplianceDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Repositories
        services.AddScoped<IAuditLogRepository, AuditLogRepository>();
        services.AddScoped<IComplianceReportRepository, ComplianceReportRepository>();
        services.AddScoped<IServiceProviderRatingRepository, ServiceProviderRatingRepository>();
        services.AddScoped<IPreferredProviderNetworkRepository, PreferredProviderNetworkRepository>();

        // Enhanced audit & compliance repositories
        services.AddScoped<IModuleRegistryRepository, ModuleRegistryRepository>();
        services.AddScoped<IModuleAccessControlRepository, ModuleAccessControlRepository>();
        services.AddScoped<ICustomComplianceReportTemplateRepository, CustomComplianceReportTemplateRepository>();
        services.AddScoped<IRetentionPolicyManagementRepository, RetentionPolicyManagementRepository>();

        // Services
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IComplianceService, ComplianceService>();
        services.AddScoped<IRatingService, RatingService>();
        services.AddScoped<IPreferredProviderService, PreferredProviderService>();
        services.AddScoped<IAdvancedAnalyticsService, AdvancedAnalyticsService>();
        services.AddScoped<IRealTimeDashboardService, RealTimeDashboardService>();
        services.AddScoped<IMobileService, MobileService>();
        services.AddScoped<ITenantService, TenantService>();
        services.AddScoped<ITenantContext, TenantContext>();
        services.AddScoped<ITenantDbContextFactory, TenantDbContextFactory>();
        services.AddScoped<IAutomatedReportingService, AutomatedReportingService>();
        services.AddScoped<IReportTemplateEngine, ReportTemplateEngine>();
        services.AddScoped<IReportExportService, ReportExportService>();
        services.AddScoped<IRiskAssessmentService, RiskAssessmentService>();
        services.AddScoped<IRiskScoringEngine, RiskScoringEngine>();
        services.AddScoped<IRiskMitigationEngine, RiskMitigationEngine>();
        services.AddScoped<IWorkflowService, WorkflowService>();
        services.AddScoped<IWorkflowEngine, WorkflowEngine>();
        services.AddScoped<IWorkflowNotificationService, WorkflowNotificationService>();

        // Enhanced audit & compliance services
        services.AddScoped<IModuleRegistryService, ModuleRegistryService>();
        services.AddScoped<IDigitalSignatureService, DigitalSignatureService>();
        services.AddScoped<IFileStorageService, FileStorageService>();
        services.AddSingleton<IBackgroundJobService, BackgroundJobService>();
        services.AddScoped<IExportValidationService, ExportValidationService>();

        // Memory cache for module registry
        services.AddMemoryCache();

        // Background services
        services.AddHostedService<ReportSchedulingService>();
        services.AddHostedService<BackgroundJobService>();

        // SignalR
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
        });

        // Redis for SignalR backplane (if configured)
        var redisConnectionString = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddSignalR().AddStackExchangeRedis(redisConnectionString, options =>
            {
                options.Configuration.ChannelPrefix = "TLI-Compliance";
            });
        }

        // Messaging
        services.AddMessaging(configuration);

        // Event Handlers
        services.AddScoped<INotificationHandler<Domain.Events.AuditLogCreatedEvent>, AuditLogCreatedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ComplianceReportCreatedEvent>, ComplianceReportCreatedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ComplianceReportCompletedEvent>, ComplianceReportCompletedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ServiceProviderRatingCreatedEvent>, ServiceProviderRatingCreatedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ServiceProviderRatingSubmittedEvent>, ServiceProviderRatingSubmittedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ServiceProviderRatingFlaggedEvent>, ServiceProviderRatingFlaggedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.ServiceIssueReportedEvent>, ServiceIssueReportedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.PreferredProviderAddedEvent>, PreferredProviderAddedEventHandler>();
        services.AddScoped<INotificationHandler<Domain.Events.PreferredProviderRemovedEvent>, PreferredProviderRemovedEventHandler>();

        return services;
    }
}
