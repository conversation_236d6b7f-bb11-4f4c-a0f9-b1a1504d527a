using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Infrastructure.Persistence;
using AnalyticsBIService.Application.DTOs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using System.Xml;
using System.IO.Compression;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of data export service
/// </summary>
public class DataExportService : IDataExportService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<DataExportService> _logger;
    private readonly string _exportPath;

    public DataExportService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<DataExportService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
        _exportPath = Path.Combine(Path.GetTempPath(), "exports");
        Directory.CreateDirectory(_exportPath);
    }

    public async Task<ExportResultDto> ExportToExcelAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting data to Excel for user {UserId}", request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName(request.FileName, "xlsx");
            var filePath = Path.Combine(_exportPath, fileName);

            // Create export record
            var export = await CreateExportRecordAsync(exportId, request, "Excel", fileName, cancellationToken);

            // Get data
            var data = await GetExportDataAsync(request, cancellationToken);

            // Generate Excel file (simplified - would use a library like EPPlus in real implementation)
            await GenerateExcelFileAsync(data, filePath, request.Options, cancellationToken);

            // Update export record
            await CompleteExportAsync(export, filePath, data.Count, cancellationToken);

            return CreateExportResult(export, filePath, data.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to Excel");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportToPdfAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting data to PDF for user {UserId}", request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName(request.FileName, "pdf");
            var filePath = Path.Combine(_exportPath, fileName);

            var export = await CreateExportRecordAsync(exportId, request, "PDF", fileName, cancellationToken);
            var data = await GetExportDataAsync(request, cancellationToken);

            // Generate PDF file (simplified - would use a library like iTextSharp in real implementation)
            await GeneratePdfFileAsync(data, filePath, request.Options, cancellationToken);

            await CompleteExportAsync(export, filePath, data.Count, cancellationToken);

            return CreateExportResult(export, filePath, data.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to PDF");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportToCsvAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting data to CSV for user {UserId}", request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName(request.FileName, "csv");
            var filePath = Path.Combine(_exportPath, fileName);

            var export = await CreateExportRecordAsync(exportId, request, "CSV", fileName, cancellationToken);
            var data = await GetExportDataAsync(request, cancellationToken);

            await GenerateCsvFileAsync(data, filePath, request.Options, cancellationToken);

            await CompleteExportAsync(export, filePath, data.Count, cancellationToken);

            return CreateExportResult(export, filePath, data.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to CSV");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportToJsonAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting data to JSON for user {UserId}", request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName(request.FileName, "json");
            var filePath = Path.Combine(_exportPath, fileName);

            var export = await CreateExportRecordAsync(exportId, request, "JSON", fileName, cancellationToken);
            var data = await GetExportDataAsync(request, cancellationToken);

            await GenerateJsonFileAsync(data, filePath, request.Options, cancellationToken);

            await CompleteExportAsync(export, filePath, data.Count, cancellationToken);

            return CreateExportResult(export, filePath, data.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to JSON");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportToXmlAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting data to XML for user {UserId}", request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName(request.FileName, "xml");
            var filePath = Path.Combine(_exportPath, fileName);

            var export = await CreateExportRecordAsync(exportId, request, "XML", fileName, cancellationToken);
            var data = await GetExportDataAsync(request, cancellationToken);

            await GenerateXmlFileAsync(data, filePath, request.Options, cancellationToken);

            await CompleteExportAsync(export, filePath, data.Count, cancellationToken);

            return CreateExportResult(export, filePath, data.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to XML");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportDashboardAsImageAsync(DashboardExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting dashboard {DashboardId} as image for user {UserId}", request.DashboardId, request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName($"dashboard_{request.DashboardId}", request.Format.ToLower());
            var filePath = Path.Combine(_exportPath, fileName);

            // This would integrate with a headless browser service like Puppeteer
            // For now, creating a placeholder image
            await GeneratePlaceholderImageAsync(filePath, request.Width, request.Height, "Dashboard Export");

            var fileInfo = new FileInfo(filePath);
            var export = new DataExport
            {
                Id = exportId,
                UserId = request.UserId,
                FileName = fileName,
                FilePath = filePath,
                Format = request.Format,
                Status = "Completed",
                FileSize = fileInfo.Length,
                RecordCount = 1,
                CreatedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(7)
            };

            _context.DataExports.Add(export);
            await _context.SaveChangesAsync(cancellationToken);

            return CreateExportResult(export, filePath, 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting dashboard as image");
            throw;
        }
    }

    public async Task<ExportResultDto> ExportChartAsImageAsync(ChartExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Exporting chart {ChartId} as image for user {UserId}", request.ChartId, request.UserId);

            var exportId = Guid.NewGuid();
            var fileName = GenerateFileName($"chart_{request.ChartId}", request.Format.ToLower());
            var filePath = Path.Combine(_exportPath, fileName);

            // This would integrate with a chart rendering service
            // For now, creating a placeholder image
            await GeneratePlaceholderImageAsync(filePath, request.Width, request.Height, "Chart Export");

            var fileInfo = new FileInfo(filePath);
            var export = new DataExport
            {
                Id = exportId,
                UserId = request.UserId,
                FileName = fileName,
                FilePath = filePath,
                Format = request.Format,
                Status = "Completed",
                FileSize = fileInfo.Length,
                RecordCount = 1,
                CreatedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(7)
            };

            _context.DataExports.Add(export);
            await _context.SaveChangesAsync(cancellationToken);

            return CreateExportResult(export, filePath, 1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting chart as image");
            throw;
        }
    }

    public async Task<BulkExportResultDto> BulkExportAsync(BulkExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting bulk export for user {UserId} with {Count} exports", request.UserId, request.Exports.Count);

            var bulkExportId = Guid.NewGuid();
            var exports = new List<ExportResultDto>();
            var successfulExports = 0;
            var failedExports = 0;

            // Process each export
            foreach (var exportRequest in request.Exports)
            {
                try
                {
                    var result = exportRequest.Format.ToUpper() switch
                    {
                        "CSV" => await ExportToCsvAsync(exportRequest, cancellationToken),
                        "JSON" => await ExportToJsonAsync(exportRequest, cancellationToken),
                        "XML" => await ExportToXmlAsync(exportRequest, cancellationToken),
                        "EXCEL" => await ExportToExcelAsync(exportRequest, cancellationToken),
                        "PDF" => await ExportToPdfAsync(exportRequest, cancellationToken),
                        _ => await ExportToCsvAsync(exportRequest, cancellationToken)
                    };

                    exports.Add(result);
                    successfulExports++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to export data for bulk export");
                    failedExports++;
                }
            }

            // Create package if requested
            string packageFilePath = "";
            long packageFileSize = 0;

            if (request.PackageFormat.ToUpper() == "ZIP" && exports.Any())
            {
                var packageFileName = GenerateFileName(request.PackageName, "zip");
                packageFilePath = Path.Combine(_exportPath, packageFileName);
                await CreateZipPackageAsync(exports, packageFilePath, cancellationToken);
                packageFileSize = new FileInfo(packageFilePath).Length;
            }

            return new BulkExportResultDto
            {
                BulkExportId = bulkExportId,
                Status = failedExports == 0 ? "Completed" : "Partial",
                Exports = exports,
                PackageFileName = Path.GetFileName(packageFilePath),
                PackageFilePath = packageFilePath,
                PackageFileSize = packageFileSize,
                CreatedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                ProcessingTime = TimeSpan.FromSeconds(exports.Count * 2), // Simplified
                SuccessfulExports = successfulExports,
                FailedExports = failedExports,
                DownloadUrl = GenerateDownloadUrl(bulkExportId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in bulk export");
            throw;
        }
    }

    public async Task<ExportStatusDto> GetExportStatusAsync(Guid exportId, CancellationToken cancellationToken = default)
    {
        try
        {
            var export = await _context.DataExports
                .FirstOrDefaultAsync(e => e.Id == exportId, cancellationToken);

            if (export == null)
                throw new InvalidOperationException($"Export {exportId} not found");

            return new ExportStatusDto
            {
                ExportId = exportId,
                Status = export.Status,
                ProgressPercentage = export.Status == "Completed" ? 100 : export.Status == "Processing" ? 50 : 0,
                CurrentStep = export.Status,
                ProcessedRecords = export.RecordCount,
                TotalRecords = export.RecordCount,
                StartedAt = export.CreatedAt,
                ElapsedTime = DateTime.UtcNow - export.CreatedAt,
                EstimatedTimeRemaining = export.Status == "Completed" ? TimeSpan.Zero : TimeSpan.FromMinutes(1),
                ErrorMessage = export.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting export status");
            throw;
        }
    }

    public async Task<List<ExportHistoryDto>> GetExportHistoryAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var exports = await _context.DataExports
                .Where(e => e.UserId == userId)
                .OrderByDescending(e => e.CreatedAt)
                .Take(50)
                .ToListAsync(cancellationToken);

            return exports.Select(e => new ExportHistoryDto
            {
                ExportId = e.Id,
                FileName = e.FileName,
                Format = e.Format,
                Status = e.Status,
                FileSize = e.FileSize,
                RecordCount = e.RecordCount,
                CreatedAt = e.CreatedAt,
                CompletedAt = e.CompletedAt,
                IsAvailable = File.Exists(e.FilePath) && DateTime.UtcNow < e.ExpiresAt,
                ExpiresAt = e.ExpiresAt,
                DataSource = e.DataSource
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting export history");
            return new List<ExportHistoryDto>();
        }
    }

    public async Task<FileDownloadDto> DownloadExportAsync(Guid exportId, CancellationToken cancellationToken = default)
    {
        try
        {
            var export = await _context.DataExports
                .FirstOrDefaultAsync(e => e.Id == exportId, cancellationToken);

            if (export == null)
                throw new InvalidOperationException($"Export {exportId} not found");

            if (!File.Exists(export.FilePath))
                throw new InvalidOperationException("Export file not found or has expired");

            var fileContent = await File.ReadAllBytesAsync(export.FilePath, cancellationToken);
            var contentType = GetContentType(export.Format);

            return new FileDownloadDto
            {
                FileName = export.FileName,
                ContentType = contentType,
                FileContent = fileContent,
                FileSize = fileContent.Length,
                LastModified = File.GetLastWriteTime(export.FilePath)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading export");
            throw;
        }
    }

    public async Task<ScheduledExportDto> ScheduleRecurringExportAsync(ScheduleExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Scheduling recurring export for user {UserId}", request.UserId);

            var schedule = new AnalyticsBIService.Infrastructure.Persistence.ScheduledExport(
                request.UserId,
                $"Scheduled Export - {request.ExportRequest.DataSource}",
                string.Empty, // description
                JsonSerializer.Serialize(request.ExportRequest),
                request.ScheduleType,
                request.CronExpression,
                request.StartDate,
                request.DeliveryMethod,
                JsonSerializer.Serialize(request.DeliveryOptions),
                request.EndDate
            );

            _context.ScheduledExports.Add(schedule);
            await _context.SaveChangesAsync(cancellationToken);

            return new ScheduledExportDto
            {
                ScheduleId = schedule.Id,
                Name = schedule.Name,
                ExportRequest = request.ExportRequest,
                ScheduleType = schedule.ScheduleType,
                CronExpression = schedule.CronExpression,
                NextRunDate = schedule.NextRunDate,
                LastRunDate = schedule.LastRunDate,
                Status = "Active",
                IsActive = schedule.IsActive,
                CreatedAt = schedule.CreatedAt,
                RecentRuns = new List<ScheduledExportRunDto>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling recurring export");
            throw;
        }
    }

    public async Task<bool> CancelExportAsync(Guid exportId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var export = await _context.DataExports
                .FirstOrDefaultAsync(e => e.Id == exportId && e.UserId == userId, cancellationToken);

            if (export == null)
                return false;

            if (export.Status == "Processing")
            {
                export.Status = "Cancelled";
                export.CompletedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync(cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling export");
            return false;
        }
    }

    public async Task<List<ExportTemplateDto>> GetExportTemplatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return GetBuiltInExportTemplates();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting export templates");
            return new List<ExportTemplateDto>();
        }
    }

    public async Task<ExportValidationResult> ValidateExportRequestAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new ExportValidationResult { IsValid = true };

            // Validate data source
            if (string.IsNullOrEmpty(request.DataSource))
            {
                result.Errors.Add("Data source is required");
                result.IsValid = false;
            }

            // Validate fields
            if (!request.Fields.Any())
            {
                result.Warnings.Add("No fields specified - all available fields will be exported");
            }

            // Validate date range
            if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
            {
                result.Errors.Add("Start date cannot be after end date");
                result.IsValid = false;
            }

            // Estimate export size
            var estimatedRecords = await EstimateRecordCountAsync(request, cancellationToken);
            var estimatedSize = EstimateFileSize(estimatedRecords, request.Fields.Count, request.Format);

            result.EstimatedSize = new EstimatedExportSizeDto
            {
                EstimatedRecords = estimatedRecords,
                EstimatedFileSizeBytes = estimatedSize,
                EstimatedFileSizeFormatted = FormatFileSize(estimatedSize),
                ExceedsLimits = estimatedRecords > 100000 || estimatedSize > 100 * 1024 * 1024, // 100MB limit
                LimitExceededMessage = estimatedRecords > 100000 ? "Export exceeds maximum record limit" :
                                     estimatedSize > 100 * 1024 * 1024 ? "Export exceeds maximum file size limit" : null
            };

            result.EstimatedDuration = TimeSpan.FromSeconds(Math.Max(1, estimatedRecords / 1000)); // 1000 records per second

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating export request");
            return new ExportValidationResult
            {
                IsValid = false,
                Errors = new List<string> { "Validation failed due to an internal error" }
            };
        }
    }

    // Helper methods
    private string GenerateFileName(string baseName, string extension)
    {
        if (string.IsNullOrEmpty(baseName))
            baseName = "export";

        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        return $"{baseName}_{timestamp}.{extension}";
    }

    private async Task<DataExport> CreateExportRecordAsync(Guid exportId, ExportRequest request, string format, string fileName, CancellationToken cancellationToken)
    {
        var export = new DataExport
        {
            Id = exportId,
            UserId = request.UserId,
            DataSource = request.DataSource,
            FileName = fileName,
            Format = format,
            Status = "Processing",
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddDays(7),
            RequestData = JsonSerializer.Serialize(request)
        };

        _context.DataExports.Add(export);
        await _context.SaveChangesAsync(cancellationToken);

        return export;
    }

    private async Task<List<Dictionary<string, object>>> GetExportDataAsync(ExportRequest request, CancellationToken cancellationToken)
    {
        // This would query actual data from the database based on the request
        // For now, returning sample data
        var data = new List<Dictionary<string, object>>();
        var random = new Random();

        var recordCount = request.MaxRows ?? random.Next(100, 1000);

        for (int i = 0; i < recordCount; i++)
        {
            var record = new Dictionary<string, object>();

            foreach (var field in request.Fields.Any() ? request.Fields : GetDefaultFields(request.DataSource))
            {
                record[field] = GenerateSampleValue(field, random);
            }

            data.Add(record);
        }

        return data;
    }

    private List<string> GetDefaultFields(string dataSource)
    {
        return dataSource.ToLower() switch
        {
            "analytics_events" => new List<string> { "id", "event_name", "user_id", "timestamp", "properties" },
            "metrics" => new List<string> { "id", "name", "value", "unit", "created_at" },
            "users" => new List<string> { "id", "email", "user_type", "created_at", "last_login" },
            _ => new List<string> { "id", "name", "value", "created_at" }
        };
    }

    private object GenerateSampleValue(string field, Random random)
    {
        return field.ToLower() switch
        {
            "id" => Guid.NewGuid().ToString(),
            "email" => $"user{random.Next(1, 1000)}@example.com",
            "name" or "event_name" => $"Sample {field} {random.Next(1, 100)}",
            "value" => Math.Round((decimal)(random.NextDouble() * 1000), 2),
            "timestamp" or "created_at" or "last_login" => DateTime.UtcNow.AddDays(-random.Next(0, 365)),
            "user_type" => random.Next(0, 3) switch { 0 => "Admin", 1 => "User", _ => "Guest" },
            "properties" => JsonSerializer.Serialize(new { key1 = "value1", key2 = random.Next(1, 100) }),
            _ => $"Sample {field} value"
        };
    }

    private async Task GenerateCsvFileAsync(List<Dictionary<string, object>> data, string filePath, ExportOptionsDto options, CancellationToken cancellationToken)
    {
        using var writer = new StreamWriter(filePath, false, Encoding.GetEncoding(options.Encoding));

        if (data.Any())
        {
            // Write headers
            var headers = data.First().Keys;
            await writer.WriteLineAsync(string.Join(options.Delimiter, headers));

            // Write data
            foreach (var record in data)
            {
                var values = headers.Select(h => FormatValue(record.GetValueOrDefault(h), options));
                await writer.WriteLineAsync(string.Join(options.Delimiter, values));
            }
        }
    }

    private async Task GenerateJsonFileAsync(List<Dictionary<string, object>> data, string filePath, ExportOptionsDto options, CancellationToken cancellationToken)
    {
        var jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        var exportData = new
        {
            metadata = options.IncludeMetadata ? new
            {
                exportedAt = DateTime.UtcNow,
                recordCount = data.Count,
                version = "1.0"
            } : null,
            data = data
        };

        var json = JsonSerializer.Serialize(exportData, jsonOptions);
        await File.WriteAllTextAsync(filePath, json, Encoding.GetEncoding(options.Encoding), cancellationToken);
    }

    private async Task GenerateXmlFileAsync(List<Dictionary<string, object>> data, string filePath, ExportOptionsDto options, CancellationToken cancellationToken)
    {
        using var writer = XmlWriter.Create(filePath, new XmlWriterSettings
        {
            Indent = true,
            Encoding = Encoding.GetEncoding(options.Encoding),
            Async = true
        });

        await writer.WriteStartDocumentAsync();
        await writer.WriteStartElementAsync(null, "export", null);

        if (options.IncludeMetadata)
        {
            await writer.WriteStartElementAsync(null, "metadata", null);
            await writer.WriteElementStringAsync(null, "exportedAt", null, DateTime.UtcNow.ToString("O"));
            await writer.WriteElementStringAsync(null, "recordCount", null, data.Count.ToString());
            await writer.WriteEndElementAsync();
        }

        await writer.WriteStartElementAsync(null, "data", null);

        foreach (var record in data)
        {
            await writer.WriteStartElementAsync(null, "record", null);
            foreach (var kvp in record)
            {
                await writer.WriteElementStringAsync(null, kvp.Key, null, kvp.Value?.ToString() ?? "");
            }
            await writer.WriteEndElementAsync();
        }

        await writer.WriteEndElementAsync(); // data
        await writer.WriteEndElementAsync(); // export
        await writer.WriteEndDocumentAsync();
    }

    private async Task GenerateExcelFileAsync(List<Dictionary<string, object>> data, string filePath, ExportOptionsDto options, CancellationToken cancellationToken)
    {
        // This would use a library like EPPlus to generate actual Excel files
        // For now, creating a CSV file with .xlsx extension as placeholder
        await GenerateCsvFileAsync(data, filePath, options, cancellationToken);
    }

    private async Task GeneratePdfFileAsync(List<Dictionary<string, object>> data, string filePath, ExportOptionsDto options, CancellationToken cancellationToken)
    {
        // This would use a library like iTextSharp to generate actual PDF files
        // For now, creating a simple text file with .pdf extension as placeholder
        var content = new StringBuilder();
        content.AppendLine("PDF Export Report");
        content.AppendLine($"Generated: {DateTime.UtcNow}");
        content.AppendLine($"Records: {data.Count}");
        content.AppendLine();

        if (data.Any())
        {
            var headers = data.First().Keys;
            content.AppendLine(string.Join("\t", headers));

            foreach (var record in data.Take(10)) // Limit for demo
            {
                var values = headers.Select(h => record.GetValueOrDefault(h)?.ToString() ?? "");
                content.AppendLine(string.Join("\t", values));
            }
        }

        await File.WriteAllTextAsync(filePath, content.ToString(), cancellationToken);
    }

    private async Task GeneratePlaceholderImageAsync(string filePath, int width, int height, string text)
    {
        // This would generate an actual image using a graphics library
        // For now, creating a simple text file as placeholder
        var content = $"Image Export: {text}\nDimensions: {width}x{height}\nGenerated: {DateTime.UtcNow}";
        await File.WriteAllTextAsync(filePath, content);
    }

    private string FormatValue(object? value, ExportOptionsDto options)
    {
        if (value == null) return "";

        return value switch
        {
            DateTime dt => dt.ToString(options.DateFormat),
            decimal d => d.ToString(options.NumberFormat),
            double d => d.ToString(options.NumberFormat),
            float f => f.ToString(options.NumberFormat),
            string s when s.Contains(options.Delimiter) => $"\"{s}\"",
            _ => value.ToString() ?? ""
        };
    }

    private async Task CompleteExportAsync(DataExport export, string filePath, int recordCount, CancellationToken cancellationToken)
    {
        var fileInfo = new FileInfo(filePath);

        export.FilePath = filePath;
        export.FileSize = fileInfo.Length;
        export.RecordCount = recordCount;
        export.Status = "Completed";
        export.CompletedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync(cancellationToken);
    }

    private ExportResultDto CreateExportResult(DataExport export, string filePath, int recordCount)
    {
        return new ExportResultDto
        {
            ExportId = export.Id,
            Status = export.Status,
            FileName = export.FileName,
            FilePath = filePath,
            FileSize = export.FileSize,
            Format = export.Format,
            RecordCount = recordCount,
            CreatedAt = export.CreatedAt,
            CompletedAt = export.CompletedAt,
            ProcessingTime = export.CompletedAt - export.CreatedAt,
            DownloadUrl = GenerateDownloadUrl(export.Id),
            ExpiresAt = export.ExpiresAt,
            Metadata = new ExportMetadataDto
            {
                DataSource = export.DataSource,
                GeneratedBy = "Analytics & BI Service",
                Version = "1.0"
            }
        };
    }

    private async Task CreateZipPackageAsync(List<ExportResultDto> exports, string packagePath, CancellationToken cancellationToken)
    {
        using var archive = ZipFile.Open(packagePath, ZipArchiveMode.Create);

        foreach (var export in exports)
        {
            if (File.Exists(export.FilePath))
            {
                archive.CreateEntryFromFile(export.FilePath, export.FileName);
            }
        }
    }

    private string GenerateDownloadUrl(Guid exportId)
    {
        return $"/api/analytics/exports/{exportId}/download";
    }

    private string GetContentType(string format)
    {
        return format.ToUpper() switch
        {
            "CSV" => "text/csv",
            "JSON" => "application/json",
            "XML" => "application/xml",
            "PDF" => "application/pdf",
            "EXCEL" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "PNG" => "image/png",
            "JPG" or "JPEG" => "image/jpeg",
            "ZIP" => "application/zip",
            _ => "application/octet-stream"
        };
    }

    private DateTime CalculateNextRunDate(string scheduleType, string cronExpression, DateTime startDate)
    {
        // Simplified calculation - would use a proper cron library in real implementation
        return scheduleType.ToUpper() switch
        {
            "DAILY" => startDate.Date.AddDays(1),
            "WEEKLY" => startDate.Date.AddDays(7),
            "MONTHLY" => startDate.Date.AddMonths(1),
            _ => startDate.Date.AddDays(1)
        };
    }

    private async Task<int> EstimateRecordCountAsync(ExportRequest request, CancellationToken cancellationToken)
    {
        // This would query the actual database to estimate record count
        // For now, returning a random estimate
        var random = new Random();
        return random.Next(100, 10000);
    }

    private long EstimateFileSize(int recordCount, int fieldCount, string format)
    {
        var avgFieldSize = 20; // Average field size in bytes
        var recordSize = fieldCount * avgFieldSize;

        return format.ToUpper() switch
        {
            "CSV" => recordCount * recordSize,
            "JSON" => (long)(recordCount * recordSize * 1.5), // JSON overhead
            "XML" => recordCount * recordSize * 2, // XML overhead
            "EXCEL" => (long)(recordCount * recordSize * 1.2), // Excel compression
            "PDF" => recordCount * recordSize * 3, // PDF formatting overhead
            _ => recordCount * recordSize
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private List<ExportTemplateDto> GetBuiltInExportTemplates()
    {
        return new List<ExportTemplateDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Analytics Events Export",
                Description = "Export user analytics events data",
                Category = "Analytics",
                DataSource = "analytics_events",
                DefaultFields = new List<string> { "id", "event_name", "user_id", "timestamp", "properties" },
                DefaultOptions = new ExportOptionsDto
                {
                    DateFormat = "yyyy-MM-dd HH:mm:ss",
                    IncludeHeaders = true,
                    Encoding = "UTF-8"
                },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Performance Metrics Export",
                Description = "Export performance and KPI metrics",
                Category = "Performance",
                DataSource = "metrics",
                DefaultFields = new List<string> { "id", "name", "value", "unit", "category", "created_at" },
                DefaultOptions = new ExportOptionsDto
                {
                    NumberFormat = "0.00",
                    IncludeMetadata = true
                },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "User Data Export",
                Description = "Export user registration and profile data",
                Category = "Users",
                DataSource = "users",
                DefaultFields = new List<string> { "id", "email", "user_type", "status", "created_at", "last_login" },
                DefaultOptions = new ExportOptionsDto
                {
                    DateFormat = "yyyy-MM-dd",
                    IncludeHeaders = true
                },
                IsCustomizable = true
            }
        };
    }

    public async Task<ExportResultDto> ExportUsageLogsAsync(UsageLogsExportRequestDto request)
    {
        try
        {
            _logger.LogInformation("Starting usage logs export for user {UserId}", request.RequestedBy);

            // Validate request
            ValidateUsageLogsRequest(request);

            // Collect data from multiple sources
            var usageLogs = await CollectUsageLogsFromSourcesAsync(request);

            // Apply filters and limits
            var filteredLogs = ApplyFiltersAndLimits(usageLogs, request);

            // Generate export file
            var exportResult = await GenerateUsageLogsExportAsync(filteredLogs, request);

            // Log audit trail
            await LogUsageLogsExportAsync(request, exportResult);

            _logger.LogInformation("Usage logs export completed: {FileName} with {RecordCount} records",
                exportResult.FileName, exportResult.RecordCount);

            return exportResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting usage logs for user {UserId}", request.RequestedBy);
            throw;
        }
    }

    private void ValidateUsageLogsRequest(UsageLogsExportRequestDto request)
    {
        if (request.FromDate.HasValue && request.ToDate.HasValue)
        {
            var dateRange = request.ToDate.Value - request.FromDate.Value;
            if (dateRange.TotalDays > 90)
            {
                throw new ArgumentException("Date range cannot exceed 90 days");
            }
        }

        if (request.MaxRecords.HasValue && request.MaxRecords.Value > 100000)
        {
            throw new ArgumentException("Maximum records limit is 100,000");
        }

        if (request.UserIds.Count > 1000)
        {
            throw new ArgumentException("Maximum 1000 users can be selected");
        }
    }

    private async Task<List<UsageLogEntryDto>> CollectUsageLogsFromSourcesAsync(UsageLogsExportRequestDto request)
    {
        var allLogs = new List<UsageLogEntryDto>();

        // Collect from Analytics service (current service)
        if (request.DataSources.Contains("Analytics") || !request.DataSources.Any())
        {
            var analyticsLogs = await GetAnalyticsLogsAsync(request);
            allLogs.AddRange(analyticsLogs);
        }

        // Collect from external services via HTTP clients
        if (request.DataSources.Contains("Orders") || !request.DataSources.Any())
        {
            var orderLogs = await GetOrderLogsAsync(request);
            allLogs.AddRange(orderLogs);
        }

        if (request.DataSources.Contains("RFQs") || !request.DataSources.Any())
        {
            var rfqLogs = await GetRfqLogsAsync(request);
            allLogs.AddRange(rfqLogs);
        }

        if (request.DataSources.Contains("Trips") || !request.DataSources.Any())
        {
            var tripLogs = await GetTripLogsAsync(request);
            allLogs.AddRange(tripLogs);
        }

        return allLogs.OrderBy(l => l.Timestamp).ToList();
    }

    private async Task<List<UsageLogEntryDto>> GetAnalyticsLogsAsync(UsageLogsExportRequestDto request)
    {
        // This would query the local analytics events
        // For now, return mock data
        return new List<UsageLogEntryDto>
        {
            new()
            {
                UserId = Guid.NewGuid(),
                UserType = "TransportCompany",
                DataSource = "Analytics",
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Event",
                Action = "Login",
                Timestamp = DateTime.UtcNow.AddHours(-1),
                SessionId = Guid.NewGuid().ToString(),
                IpAddress = "***********",
                Properties = new Dictionary<string, object> { ["browser"] = "Chrome", ["version"] = "91.0" },
                Metadata = new Dictionary<string, object> { ["source"] = "web" }
            }
        };
    }

    private async Task<List<UsageLogEntryDto>> GetOrderLogsAsync(UsageLogsExportRequestDto request)
    {
        // This would make HTTP call to OrderManagement service
        // For now, return mock data
        return new List<UsageLogEntryDto>
        {
            new()
            {
                UserId = Guid.NewGuid(),
                UserType = "Shipper",
                DataSource = "Orders",
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Order",
                Action = "Create",
                Timestamp = DateTime.UtcNow.AddHours(-2),
                Properties = new Dictionary<string, object> { ["order_value"] = 5000, ["status"] = "Created" }
            }
        };
    }

    private async Task<List<UsageLogEntryDto>> GetRfqLogsAsync(UsageLogsExportRequestDto request)
    {
        // This would make HTTP call to OrderManagement service for RFQs
        // For now, return mock data
        return new List<UsageLogEntryDto>
        {
            new()
            {
                UserId = Guid.NewGuid(),
                UserType = "Broker",
                DataSource = "RFQs",
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "RFQ",
                Action = "Create",
                Timestamp = DateTime.UtcNow.AddHours(-3),
                Properties = new Dictionary<string, object> { ["rfq_value"] = 8000, ["route"] = "Mumbai-Delhi" }
            }
        };
    }

    private async Task<List<UsageLogEntryDto>> GetTripLogsAsync(UsageLogsExportRequestDto request)
    {
        // This would make HTTP call to TripManagement service
        // For now, return mock data
        return new List<UsageLogEntryDto>
        {
            new()
            {
                UserId = Guid.NewGuid(),
                UserType = "Carrier",
                DataSource = "Trips",
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Trip",
                Action = "Complete",
                Timestamp = DateTime.UtcNow.AddHours(-4),
                Properties = new Dictionary<string, object> { ["distance"] = 1200, ["vehicle_type"] = "Truck" }
            }
        };
    }

    private List<UsageLogEntryDto> ApplyFiltersAndLimits(List<UsageLogEntryDto> logs, UsageLogsExportRequestDto request)
    {
        var filtered = logs.AsQueryable();

        // Apply user filter
        if (request.UserIds.Any())
        {
            filtered = filtered.Where(l => request.UserIds.Contains(l.UserId));
        }

        // Apply date range filter
        if (request.FromDate.HasValue)
        {
            filtered = filtered.Where(l => l.Timestamp >= request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            filtered = filtered.Where(l => l.Timestamp <= request.ToDate.Value);
        }

        // Apply activity type filter
        if (request.ActivityTypes.Any())
        {
            filtered = filtered.Where(l => request.ActivityTypes.Contains(l.Action));
        }

        // Apply user type filter
        if (request.UserType.HasValue)
        {
            filtered = filtered.Where(l => l.UserType == request.UserType.ToString());
        }

        // Apply record limit
        if (request.MaxRecords.HasValue)
        {
            filtered = filtered.Take(request.MaxRecords.Value);
        }

        return filtered.ToList();
    }

    private async Task<ExportResultDto> GenerateUsageLogsExportAsync(List<UsageLogEntryDto> logs, UsageLogsExportRequestDto request)
    {
        var exportId = Guid.NewGuid().ToString("N")[..8];
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var fileName = $"usage_logs_{timestamp}_{exportId}.{GetFileExtension(request.Format)}";

        string content = request.Format switch
        {
            ExportFormat.CSV => GenerateCsvContent(logs, request),
            ExportFormat.Excel => GenerateExcelContent(logs, request),
            ExportFormat.JSON => GenerateJsonContent(logs, request),
            _ => throw new ArgumentException($"Unsupported format: {request.Format}")
        };

        // Save file (mock implementation)
        var fileUrl = $"https://storage.tli-platform.com/exports/{fileName}";
        var fileSizeBytes = System.Text.Encoding.UTF8.GetByteCount(content);

        return new ExportResultDto
        {
            FileName = fileName,
            FileUrl = fileUrl,
            Format = request.Format.ToString(),
            RecordCount = logs.Count,
            FileSizeBytes = fileSizeBytes,
            GeneratedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddDays(7)
        };
    }

    private string GenerateCsvContent(List<UsageLogEntryDto> logs, UsageLogsExportRequestDto request)
    {
        var csv = new StringBuilder();

        // Headers
        var headers = new[] { "UserId", "UserType", "DataSource", "EntityId", "EntityType", "Action", "Timestamp", "SessionId", "IpAddress" };
        csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

        // Data rows
        foreach (var log in logs)
        {
            var row = new[]
            {
                log.UserId.ToString(),
                log.UserType,
                log.DataSource,
                log.EntityId,
                log.EntityType,
                log.Action,
                log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss"),
                log.SessionId,
                log.IpAddress
            };
            csv.AppendLine(string.Join(",", row.Select(r => $"\"{r}\"")));
        }

        return csv.ToString();
    }

    private string GenerateExcelContent(List<UsageLogEntryDto> logs, UsageLogsExportRequestDto request)
    {
        // For now, return CSV format
        // In a real implementation, you would use EPPlus or similar library
        return GenerateCsvContent(logs, request);
    }

    private string GenerateJsonContent(List<UsageLogEntryDto> logs, UsageLogsExportRequestDto request)
    {
        return System.Text.Json.JsonSerializer.Serialize(logs, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });
    }

    private async Task LogUsageLogsExportAsync(UsageLogsExportRequestDto request, ExportResultDto result)
    {
        // Log the export operation for audit purposes
        _logger.LogInformation("Usage logs export completed: User {UserId}, Records {RecordCount}, File {FileName}",
            request.RequestedBy, result.RecordCount, result.FileName);
    }

    private static string GetFileExtension(ExportFormat format)
    {
        return format switch
        {
            ExportFormat.CSV => "csv",
            ExportFormat.Excel => "xlsx",
            ExportFormat.JSON => "json",
            _ => "txt"
        };
    }
}

// Entity classes for database
public class DataExport
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public int RecordCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string RequestData { get; set; } = string.Empty;
}

public class ScheduledExport
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ExportRequest { get; set; } = string.Empty;
    public string ScheduleType { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public string DeliveryMethod { get; set; } = string.Empty;
    public string DeliveryOptions { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}
