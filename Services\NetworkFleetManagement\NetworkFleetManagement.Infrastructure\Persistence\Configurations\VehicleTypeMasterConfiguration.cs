using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class VehicleTypeMasterConfiguration : IEntityTypeConfiguration<VehicleTypeMaster>
{
    public void Configure(EntityTypeBuilder<VehicleTypeMaster> builder)
    {
        builder.ToTable("vehicle_type_masters");

        builder.HasKey(v => v.Id);

        builder.Property(v => v.Id)
            .ValueGeneratedNever();

        builder.Property(v => v.Code)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(v => v.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(v => v.Description)
            .HasMaxLength(500);

        builder.Property(v => v.Category)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(v => v.MinLoadCapacityKg)
            .HasColumnType("decimal(10,2)");

        builder.Property(v => v.MaxLoadCapacityKg)
            .HasColumnType("decimal(10,2)");

        builder.Property(v => v.MinVolumeCapacityM3)
            .HasColumnType("decimal(10,2)");

        builder.Property(v => v.MaxVolumeCapacityM3)
            .HasColumnType("decimal(10,2)");

        builder.Property(v => v.SpecialRequirements)
            .HasMaxLength(1000);

        builder.Property(v => v.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(v => v.SortOrder)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(v => v.IconUrl)
            .HasMaxLength(500);

        // Configure AdditionalProperties as JSON
        builder.Property(v => v.AdditionalProperties)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(v => v.CreatedAt)
            .IsRequired();

        builder.Property(v => v.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(v => v.Code)
            .IsUnique()
            .HasDatabaseName("IX_VehicleTypeMasters_Code");

        builder.HasIndex(v => v.Name)
            .HasDatabaseName("IX_VehicleTypeMasters_Name");

        builder.HasIndex(v => v.Category)
            .HasDatabaseName("IX_VehicleTypeMasters_Category");

        builder.HasIndex(v => v.IsActive)
            .HasDatabaseName("IX_VehicleTypeMasters_IsActive");

        builder.HasIndex(v => v.SortOrder)
            .HasDatabaseName("IX_VehicleTypeMasters_SortOrder");

        builder.HasIndex(v => new { v.Category, v.IsActive, v.SortOrder })
            .HasDatabaseName("IX_VehicleTypeMasters_Category_IsActive_SortOrder");

        builder.HasIndex(v => new { v.MinLoadCapacityKg, v.MaxLoadCapacityKg })
            .HasDatabaseName("IX_VehicleTypeMasters_LoadCapacity");

        builder.HasIndex(v => new { v.MinVolumeCapacityM3, v.MaxVolumeCapacityM3 })
            .HasDatabaseName("IX_VehicleTypeMasters_VolumeCapacity");

        // Constraints
        builder.HasCheckConstraint("CK_VehicleTypeMasters_LoadCapacity", 
            "\"MinLoadCapacityKg\" IS NULL OR \"MaxLoadCapacityKg\" IS NULL OR \"MinLoadCapacityKg\" <= \"MaxLoadCapacityKg\"");

        builder.HasCheckConstraint("CK_VehicleTypeMasters_VolumeCapacity", 
            "\"MinVolumeCapacityM3\" IS NULL OR \"MaxVolumeCapacityM3\" IS NULL OR \"MinVolumeCapacityM3\" <= \"MaxVolumeCapacityM3\"");

        builder.HasCheckConstraint("CK_VehicleTypeMasters_PositiveCapacities", 
            "(\"MinLoadCapacityKg\" IS NULL OR \"MinLoadCapacityKg\" >= 0) AND " +
            "(\"MaxLoadCapacityKg\" IS NULL OR \"MaxLoadCapacityKg\" >= 0) AND " +
            "(\"MinVolumeCapacityM3\" IS NULL OR \"MinVolumeCapacityM3\" >= 0) AND " +
            "(\"MaxVolumeCapacityM3\" IS NULL OR \"MaxVolumeCapacityM3\" >= 0)");
    }
}
