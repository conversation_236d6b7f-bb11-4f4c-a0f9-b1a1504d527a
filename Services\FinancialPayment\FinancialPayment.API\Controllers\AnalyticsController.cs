using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AnalyticsController : ControllerBase
{
    private readonly IPaymentAnalyticsService _analyticsService;
    private readonly ILogger<AnalyticsController> _logger;

    public AnalyticsController(
        IPaymentAnalyticsService analyticsService,
        ILogger<AnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// Get payment analytics summary
    /// </summary>
    [HttpPost("summary")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<PaymentAnalyticsSummary>> GetAnalyticsSummary([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var summary = await _analyticsService.GetAnalyticsSummaryAsync(filter);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment KPIs
    /// </summary>
    [HttpPost("kpis")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<List<PaymentKPI>>> GetKPIs([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var kpis = await _analyticsService.GetKPIsAsync(filter);
            return Ok(kpis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting KPIs");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get volume chart data
    /// </summary>
    [HttpPost("charts/volume")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ChartData>> GetVolumeChart([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var chartData = await _analyticsService.GetVolumeChartAsync(filter);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting volume chart data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get transaction count chart data
    /// </summary>
    [HttpPost("charts/transactions")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ChartData>> GetTransactionCountChart([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var chartData = await _analyticsService.GetTransactionCountChartAsync(filter);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction count chart data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment method distribution chart
    /// </summary>
    [HttpPost("charts/payment-methods")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ChartData>> GetPaymentMethodDistribution([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var chartData = await _analyticsService.GetPaymentMethodDistributionAsync(filter);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment method distribution");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get gateway performance chart
    /// </summary>
    [HttpPost("charts/gateway-performance")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ChartData>> GetGatewayPerformance([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var chartData = await _analyticsService.GetGatewayPerformanceAsync(filter);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting gateway performance data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get success rate trend chart
    /// </summary>
    [HttpPost("charts/success-rate")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<ChartData>> GetSuccessRateTrend([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var chartData = await _analyticsService.GetSuccessRateTrendAsync(filter);
            return Ok(chartData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting success rate trend");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get revenue trends
    /// </summary>
    [HttpPost("trends/revenue")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<List<PaymentTrend>>> GetRevenueTrends([FromBody] PaymentAnalyticsFilter filter)
    {
        try
        {
            var trends = await _analyticsService.GetRevenueTrendsAsync(filter);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue trends");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate analytics report
    /// </summary>
    [HttpPost("reports/generate")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<PaymentReport>> GenerateReport([FromBody] GenerateReportRequest request)
    {
        try
        {
            var report = await _analyticsService.GenerateReportAsync(request);
            
            _logger.LogInformation("Report generation initiated: {ReportId}", report.Id);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user reports
    /// </summary>
    [HttpGet("reports")]
    public async Task<ActionResult<List<PaymentReport>>> GetReports([FromQuery] Guid? userId = null)
    {
        try
        {
            var reports = await _analyticsService.GetReportsAsync(userId);
            return Ok(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create analytics dashboard
    /// </summary>
    [HttpPost("dashboards")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<PaymentDashboard>> CreateDashboard([FromBody] CreateDashboardRequest request)
    {
        try
        {
            var dashboard = await _analyticsService.CreateDashboardAsync(request);
            
            _logger.LogInformation("Dashboard created: {DashboardId}", dashboard.Id);
            return CreatedAtAction(nameof(GetDashboard), new { id = dashboard.Id }, dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get dashboard by ID
    /// </summary>
    [HttpGet("dashboards/{id}")]
    public async Task<ActionResult<PaymentDashboard>> GetDashboard(Guid id)
    {
        try
        {
            var dashboards = await _analyticsService.GetDashboardsAsync();
            var dashboard = dashboards.FirstOrDefault(d => d.Id == id);
            
            if (dashboard == null)
            {
                return NotFound($"Dashboard {id} not found");
            }

            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard {DashboardId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user dashboards
    /// </summary>
    [HttpGet("dashboards")]
    public async Task<ActionResult<List<PaymentDashboard>>> GetDashboards([FromQuery] Guid? userId = null)
    {
        try
        {
            var dashboards = await _analyticsService.GetDashboardsAsync(userId);
            return Ok(dashboards);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboards");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update dashboard
    /// </summary>
    [HttpPut("dashboards/{id}")]
    [Authorize(Roles = "Admin,Finance,Manager")]
    public async Task<ActionResult<PaymentDashboard>> UpdateDashboard(
        Guid id, 
        [FromBody] UpdateDashboardRequest request)
    {
        try
        {
            var dashboard = await _analyticsService.UpdateDashboardAsync(id, request);
            
            _logger.LogInformation("Dashboard updated: {DashboardId}", id);
            return Ok(dashboard);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Dashboard not found: {DashboardId}", id);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating dashboard {DashboardId}", id);
            return StatusCode(500, "Internal server error");
        }
    }
}
