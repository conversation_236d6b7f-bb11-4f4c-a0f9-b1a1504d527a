namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Data pipeline data transfer object
/// </summary>
public class DataPipelineDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Active, Inactive, Error, Paused
    public DateTime CreatedAt { get; set; }
    public DateTime? LastRun { get; set; }
    public DateTime? NextRun { get; set; }
    public string Schedule { get; set; } = string.Empty; // Cron expression
    public List<string> DataSources { get; set; } = new();
    public List<string> Destinations { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<DataPipelineStepDto> Steps { get; set; } = new();
    public DataPipelineMetricsDto Metrics { get; set; } = new();

    // Properties required by DataExportService
    public Guid UserId { get; set; }
    public string PipelineName { get; set; } = string.Empty;
    public DataSourceConfigDto? SourceConfig { get; set; }
    public DataTransformationConfigDto? TransformationConfig { get; set; }
    public DataDestinationConfigDto? DestinationConfig { get; set; }
    public PipelineScheduleConfigDto? ScheduleConfig { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Data pipeline request data transfer object
/// </summary>
public class DataPipelineRequestDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Schedule { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<string> Destinations { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<DataPipelineStepDto> Steps { get; set; } = new();
    public Guid RequestedBy { get; set; }

    // Properties required by DataExportService
    public Guid UserId { get; set; }
    public string PipelineName { get; set; } = string.Empty;
    public DataSourceConfigDto? SourceConfig { get; set; }
    public DataTransformationConfigDto? TransformationConfig { get; set; }
    public DataDestinationConfigDto? DestinationConfig { get; set; }
    public PipelineScheduleConfigDto? ScheduleConfig { get; set; }
}

/// <summary>
/// Data pipeline step data transfer object
/// </summary>
public class DataPipelineStepDto
{
    public int Order { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Extract, Transform, Load, Validate
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// Data pipeline metrics data transfer object
/// </summary>
public class DataPipelineMetricsDto
{
    public long TotalRuns { get; set; }
    public long SuccessfulRuns { get; set; }
    public long FailedRuns { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public long TotalRecordsProcessed { get; set; }
    public DateTime? LastSuccessfulRun { get; set; }
    public DateTime? LastFailedRun { get; set; }
    public string? LastError { get; set; }
}

/// <summary>
/// Pipeline schedule configuration data transfer object
/// </summary>
public class PipelineScheduleConfigDto
{
    public string ScheduleType { get; set; } = string.Empty; // "Manual", "Scheduled", "Triggered"
    public string CronExpression { get; set; } = string.Empty;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string TimeZone { get; set; } = "UTC";
    public bool IsEnabled { get; set; } = true;
    public Dictionary<string, object> TriggerConditions { get; set; } = new();
    public int MaxRetries { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Data source configuration data transfer object
/// </summary>
public class DataSourceConfigDto
{
    public string SourceType { get; set; } = string.Empty; // "Database", "API", "File", "Stream"
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> Tables { get; set; } = new();
    public string Query { get; set; } = string.Empty;
    public Dictionary<string, string> Headers { get; set; } = new();
    public bool IsEncrypted { get; set; }
}

/// <summary>
/// Data transformation configuration data transfer object
/// </summary>
public class DataTransformationConfigDto
{
    public List<string> TransformationSteps { get; set; } = new();
    public Dictionary<string, object> Rules { get; set; } = new();
    public List<string> Filters { get; set; } = new();
    public Dictionary<string, string> FieldMappings { get; set; } = new();
    public List<string> Aggregations { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
}

/// <summary>
/// Data destination configuration data transfer object
/// </summary>
public class DataDestinationConfigDto
{
    public string DestinationType { get; set; } = string.Empty; // "Database", "File", "API", "Stream"
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string TargetTable { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public Dictionary<string, string> Headers { get; set; } = new();
    public bool IsEncrypted { get; set; }
}
