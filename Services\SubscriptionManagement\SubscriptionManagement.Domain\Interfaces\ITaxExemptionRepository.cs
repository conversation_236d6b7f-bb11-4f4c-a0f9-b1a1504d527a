using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface ITaxExemptionRepository
    {
        Task<TaxExemption?> GetByIdAsync(Guid id);
        Task<List<TaxExemption>> GetByUserIdAsync(Guid userId);
        Task<List<TaxExemption>> GetActiveByUserIdAsync(Guid userId);
        Task<List<TaxExemption>> GetByExemptionNumberAsync(string exemptionNumber);
        Task<List<TaxExemption>> GetExpiringExemptionsAsync(int daysThreshold = 30);
        Task<List<TaxExemption>> GetUnverifiedExemptionsAsync();
        Task<List<TaxExemption>> GetByRegionAsync(string region);
        Task<List<TaxExemption>> GetByTaxTypeAsync(TaxType taxType);
        Task<TaxExemption> AddAsync(TaxExemption taxExemption);
        Task<TaxExemption> UpdateAsync(TaxExemption taxExemption);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> IsUserExemptAsync(Guid userId, TaxType taxType, string region, DateTime? date = null);
        Task<List<TaxExemption>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10);
        Task<int> GetCountAsync();
        Task<int> GetCountByUserIdAsync(Guid userId);
    }
}
