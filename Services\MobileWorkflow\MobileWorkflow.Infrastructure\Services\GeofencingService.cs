using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IGeofencingService
{
    Task<GeofenceDto> CreateGeofenceAsync(CreateGeofenceRequest request, CancellationToken cancellationToken = default);
    Task<GeofenceDto?> GetGeofenceAsync(Guid geofenceId, CancellationToken cancellationToken = default);
    Task<List<GeofenceDto>> GetGeofencesAsync(bool? isActive = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateGeofenceAsync(Guid geofenceId, UpdateGeofenceRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteGeofenceAsync(Guid geofenceId, CancellationToken cancellationToken = default);
    Task<LocationUpdateResult> ProcessLocationUpdateAsync(ProcessLocationUpdateRequest request, CancellationToken cancellationToken = default);
    Task<List<GeofenceEventDto>> GetGeofenceEventsAsync(Guid? geofenceId = null, Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<UserGeofenceStatusDto>> GetUserGeofenceStatusesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<GeofenceAnalyticsDto> GetGeofenceAnalyticsAsync(Guid? geofenceId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<GeofenceDto>> GetNearbyGeofencesAsync(double latitude, double longitude, double radiusMeters = 1000, CancellationToken cancellationToken = default);
    Task<bool> TestGeofenceAsync(Guid geofenceId, double latitude, double longitude, CancellationToken cancellationToken = default);
    Task ProcessGeofenceEventsAsync(CancellationToken cancellationToken = default);
    Task CleanupOldLocationDataAsync(CancellationToken cancellationToken = default);
}

public class GeofencingService : IGeofencingService
{
    private readonly IGeofenceRepository _geofenceRepository;
    private readonly IGeofenceEventRepository _eventRepository;
    private readonly IUserGeofenceStatusRepository _statusRepository;
    private readonly ILocationUpdateRepository _locationRepository;
    private readonly IGeofenceActionService _actionService;
    private readonly ILogger<GeofencingService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public GeofencingService(
        IGeofenceRepository geofenceRepository,
        IGeofenceEventRepository eventRepository,
        IUserGeofenceStatusRepository statusRepository,
        ILocationUpdateRepository locationRepository,
        IGeofenceActionService actionService,
        ILogger<GeofencingService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _geofenceRepository = geofenceRepository;
        _eventRepository = eventRepository;
        _statusRepository = statusRepository;
        _locationRepository = locationRepository;
        _actionService = actionService;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<GeofenceDto> CreateGeofenceAsync(CreateGeofenceRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating geofence {Name} of type {Type}", request.Name, request.Type);

            var geofence = new Geofence(
                request.Name,
                request.Description,
                request.Type,
                request.Coordinates,
                request.TriggerType,
                request.CreatedBy);

            if (request.Radius.HasValue)
            {
                geofence.UpdateCoordinates(request.Coordinates, request.Radius.Value);
            }

            if (request.DwellTime.HasValue || request.TriggerActions != null)
            {
                geofence.UpdateTriggerSettings(request.TriggerType, request.DwellTime, request.TriggerActions);
            }

            if (request.Conditions != null)
            {
                geofence.UpdateConditions(request.Conditions);
            }

            if (request.TargetUserIds != null)
            {
                foreach (var userId in request.TargetUserIds)
                {
                    geofence.AddTargetUser(userId);
                }
            }

            if (request.TargetUserGroups != null)
            {
                foreach (var groupName in request.TargetUserGroups)
                {
                    geofence.AddTargetUserGroup(groupName);
                }
            }

            _geofenceRepository.Add(geofence);
            await _geofenceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Geofence {GeofenceId} created successfully", geofence.Id);

            return MapToGeofenceDto(geofence);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating geofence {Name}", request.Name);
            throw;
        }
    }

    public async Task<GeofenceDto?> GetGeofenceAsync(Guid geofenceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var geofence = await _geofenceRepository.GetByIdAsync(geofenceId, cancellationToken);
            if (geofence == null)
            {
                return null;
            }

            return MapToGeofenceDto(geofence);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geofence {GeofenceId}", geofenceId);
            return null;
        }
    }

    public async Task<List<GeofenceDto>> GetGeofencesAsync(bool? isActive = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var geofences = await _geofenceRepository.GetGeofencesAsync(isActive, cancellationToken);
            return geofences.Select(MapToGeofenceDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geofences");
            return new List<GeofenceDto>();
        }
    }

    public async Task<bool> UpdateGeofenceAsync(Guid geofenceId, UpdateGeofenceRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var geofence = await _geofenceRepository.GetByIdAsync(geofenceId, cancellationToken);
            if (geofence == null)
            {
                return false;
            }

            if (request.Coordinates != null)
            {
                geofence.UpdateCoordinates(request.Coordinates, request.Radius);
            }

            if (request.TriggerType != null || request.DwellTime.HasValue || request.TriggerActions != null)
            {
                geofence.UpdateTriggerSettings(
                    request.TriggerType ?? geofence.TriggerType,
                    request.DwellTime ?? geofence.DwellTime,
                    request.TriggerActions);
            }

            if (request.Conditions != null)
            {
                geofence.UpdateConditions(request.Conditions);
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                {
                    geofence.Activate();
                }
                else
                {
                    geofence.Deactivate();
                }
            }

            await _geofenceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Geofence {GeofenceId} updated successfully", geofenceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating geofence {GeofenceId}", geofenceId);
            return false;
        }
    }

    public async Task<bool> DeleteGeofenceAsync(Guid geofenceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var geofence = await _geofenceRepository.GetByIdAsync(geofenceId, cancellationToken);
            if (geofence == null)
            {
                return false;
            }

            _geofenceRepository.Remove(geofence);
            await _geofenceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Geofence {GeofenceId} deleted successfully", geofenceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting geofence {GeofenceId}", geofenceId);
            return false;
        }
    }

    public async Task<LocationUpdateResult> ProcessLocationUpdateAsync(ProcessLocationUpdateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing location update for user {UserId} at {Latitude}, {Longitude}",
                request.UserId, request.Latitude, request.Longitude);

            // Create location update record
            var locationUpdate = new LocationUpdate(
                request.UserId,
                request.Latitude,
                request.Longitude,
                request.Timestamp ?? DateTime.UtcNow,
                request.LocationContext);

            locationUpdate.SetLocationDetails(
                request.Accuracy,
                request.Altitude,
                request.Speed,
                request.Bearing,
                request.DeviceId,
                request.Provider);

            _locationRepository.Add(locationUpdate);

            // Get active geofences
            var activeGeofences = await GetActiveGeofencesForUserAsync(request.UserId, cancellationToken);
            var triggeredEvents = new List<GeofenceEventDto>();

            foreach (var geofence in activeGeofences)
            {
                var isInside = geofence.IsLocationInside(request.Latitude, request.Longitude);
                var currentStatus = await _statusRepository.GetByUserAndGeofenceAsync(request.UserId, geofence.Id, cancellationToken);

                if (currentStatus == null)
                {
                    // First time tracking this user for this geofence
                    currentStatus = new UserGeofenceStatus(
                        geofence.Id,
                        request.UserId,
                        isInside ? "Inside" : "Outside");

                    _statusRepository.Add(currentStatus);

                    if (isInside && ShouldTrigger(geofence, "Enter"))
                    {
                        var enterEvent = await CreateGeofenceEventAsync(geofence.Id, request.UserId, "Enter", request, cancellationToken);
                        triggeredEvents.Add(enterEvent);
                    }
                }
                else
                {
                    var wasInside = currentStatus.IsInside();

                    if (isInside && !wasInside && ShouldTrigger(geofence, "Enter"))
                    {
                        // User entered geofence
                        currentStatus.UpdateStatus("Inside", request.Latitude, request.Longitude);
                        var enterEvent = await CreateGeofenceEventAsync(geofence.Id, request.UserId, "Enter", request, cancellationToken);
                        triggeredEvents.Add(enterEvent);
                    }
                    else if (!isInside && wasInside && ShouldTrigger(geofence, "Exit"))
                    {
                        // User exited geofence
                        currentStatus.UpdateStatus("Outside", request.Latitude, request.Longitude);
                        var exitEvent = await CreateGeofenceEventAsync(geofence.Id, request.UserId, "Exit", request, cancellationToken);
                        triggeredEvents.Add(exitEvent);
                    }
                    else if (isInside && wasInside)
                    {
                        // User still inside, check for dwell
                        currentStatus.UpdateLocation(request.Latitude, request.Longitude);

                        if (geofence.DwellTime.HasValue && !currentStatus.IsDwelling())
                        {
                            var sessionTime = currentStatus.GetCurrentSessionTime();
                            if (sessionTime >= geofence.DwellTime.Value && ShouldTrigger(geofence, "Dwell"))
                            {
                                currentStatus.UpdateStatus("Dwelling", request.Latitude, request.Longitude);
                                var dwellEvent = await CreateGeofenceEventAsync(geofence.Id, request.UserId, "Dwell", request, cancellationToken);
                                triggeredEvents.Add(dwellEvent);
                            }
                        }
                    }
                }
            }

            locationUpdate.MarkAsProcessed();
            await _locationRepository.SaveChangesAsync(cancellationToken);
            await _statusRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Location update processed for user {UserId}, triggered {EventCount} events",
                request.UserId, triggeredEvents.Count);

            return new LocationUpdateResult
            {
                IsSuccess = true,
                TriggeredEvents = triggeredEvents,
                ProcessedGeofences = activeGeofences.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing location update for user {UserId}", request.UserId);
            return new LocationUpdateResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<GeofenceEventDto>> GetGeofenceEventsAsync(Guid? geofenceId = null, Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var events = await _eventRepository.GetEventsAsync(geofenceId, userId, from, cancellationToken);

            return events.Select(MapToGeofenceEventDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geofence events");
            return new List<GeofenceEventDto>();
        }
    }

    public async Task<List<UserGeofenceStatusDto>> GetUserGeofenceStatusesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var statuses = await _statusRepository.GetByUserIdAsync(userId, cancellationToken);
            return statuses.Select(MapToUserGeofenceStatusDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user geofence statuses for user {UserId}", userId);
            return new List<UserGeofenceStatusDto>();
        }
    }

    public async Task<GeofenceAnalyticsDto> GetGeofenceAnalyticsAsync(Guid? geofenceId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var events = await _eventRepository.GetEventsAsync(geofenceId, null, from, cancellationToken);
            var statuses = geofenceId.HasValue
                ? await _statusRepository.GetByGeofenceIdAsync(geofenceId.Value, cancellationToken)
                : await _statusRepository.GetAllAsync(cancellationToken);

            var analytics = new GeofenceAnalyticsDto
            {
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalEvents = events.Count,
                EnterEvents = events.Count(e => e.EventType == "Enter"),
                ExitEvents = events.Count(e => e.EventType == "Exit"),
                DwellEvents = events.Count(e => e.EventType == "Dwell"),
                UniqueUsers = events.Select(e => e.UserId).Distinct().Count(),
                ActiveUsers = statuses.Count(s => s.IsInside()),
                EventsByHour = events.GroupBy(e => e.EventTime.Hour).ToDictionary(g => g.Key, g => g.Count()),
                EventsByDay = events.GroupBy(e => e.EventTime.Date).ToDictionary(g => g.Key, g => g.Count()),
                AverageDwellTime = CalculateAverageDwellTime(statuses),
                MostActiveGeofences = events.GroupBy(e => e.GeofenceId)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geofence analytics");
            return new GeofenceAnalyticsDto { FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30), ToDate = DateTime.UtcNow };
        }
    }

    public async Task<List<GeofenceDto>> GetNearbyGeofencesAsync(double latitude, double longitude, double radiusMeters = 1000, CancellationToken cancellationToken = default)
    {
        try
        {
            var allGeofences = await _geofenceRepository.GetActiveGeofencesAsync(cancellationToken);
            var nearbyGeofences = new List<Geofence>();

            foreach (var geofence in allGeofences)
            {
                var distance = geofence.CalculateDistanceToCenter(latitude, longitude);
                if (distance <= radiusMeters)
                {
                    nearbyGeofences.Add(geofence);
                }
            }

            return nearbyGeofences.Select(MapToGeofenceDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting nearby geofences");
            return new List<GeofenceDto>();
        }
    }

    public async Task<bool> TestGeofenceAsync(Guid geofenceId, double latitude, double longitude, CancellationToken cancellationToken = default)
    {
        try
        {
            var geofence = await _geofenceRepository.GetByIdAsync(geofenceId, cancellationToken);
            if (geofence == null)
            {
                return false;
            }

            return geofence.IsLocationInside(latitude, longitude);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing geofence {GeofenceId}", geofenceId);
            return false;
        }
    }

    public async Task ProcessGeofenceEventsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var unprocessedEvents = await _eventRepository.GetUnprocessedEventsAsync(cancellationToken);

            foreach (var geofenceEvent in unprocessedEvents)
            {
                try
                {
                    var geofence = await _geofenceRepository.GetByIdAsync(geofenceEvent.GeofenceId, cancellationToken);
                    if (geofence == null)
                    {
                        geofenceEvent.MarkAsProcessed(new Dictionary<string, object> { ["error"] = "Geofence not found" });
                        continue;
                    }

                    var processingResult = await _actionService.ExecuteGeofenceActionsAsync(
                        geofence,
                        geofenceEvent,
                        cancellationToken);

                    geofenceEvent.MarkAsProcessed(processingResult);

                    _logger.LogDebug("Processed geofence event {EventId} for geofence {GeofenceId}",
                        geofenceEvent.Id, geofenceEvent.GeofenceId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing geofence event {EventId}", geofenceEvent.Id);
                    geofenceEvent.MarkAsProcessed(new Dictionary<string, object>
                    {
                        ["error"] = ex.Message,
                        ["processed_at"] = DateTime.UtcNow
                    });
                }
            }

            if (unprocessedEvents.Any())
            {
                await _eventRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Processed {Count} geofence events", unprocessedEvents.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing geofence events");
        }
    }

    public async Task CleanupOldLocationDataAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var retentionDays = _configuration.GetValue<int>("Geofencing:LocationDataRetentionDays", 30);
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            var oldLocationUpdates = await _locationRepository.GetOlderThanAsync(cutoffDate, cancellationToken);
            var oldEvents = await _eventRepository.GetOlderThanAsync(cutoffDate, cancellationToken);

            foreach (var locationUpdate in oldLocationUpdates)
            {
                _locationRepository.Remove(locationUpdate);
            }

            foreach (var geofenceEvent in oldEvents)
            {
                _eventRepository.Remove(geofenceEvent);
            }

            var totalDeleted = oldLocationUpdates.Count + oldEvents.Count;
            if (totalDeleted > 0)
            {
                await _locationRepository.SaveChangesAsync(cancellationToken);
                await _eventRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Cleaned up {Count} old location records", totalDeleted);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old location data");
        }
    }

    // Helper methods
    private async Task<List<Geofence>> GetActiveGeofencesForUserAsync(Guid userId, CancellationToken cancellationToken)
    {
        var cacheKey = $"user_geofences_{userId}";
        if (_cache.TryGetValue(cacheKey, out List<Geofence>? cachedGeofences))
        {
            return cachedGeofences!;
        }

        var allActiveGeofences = await _geofenceRepository.GetActiveGeofencesAsync(cancellationToken);

        // TODO: Get user groups from user service
        var userGroups = new List<string>(); // This would come from user service

        var userGeofences = allActiveGeofences
            .Where(g => g.IsUserTargeted(userId, userGroups))
            .ToList();

        _cache.Set(cacheKey, userGeofences, TimeSpan.FromMinutes(5));
        return userGeofences;
    }

    private bool ShouldTrigger(Geofence geofence, string eventType)
    {
        return geofence.TriggerType.Equals("Both", StringComparison.OrdinalIgnoreCase) ||
               geofence.TriggerType.Equals(eventType, StringComparison.OrdinalIgnoreCase);
    }

    private async Task<GeofenceEventDto> CreateGeofenceEventAsync(
        Guid geofenceId,
        Guid userId,
        string eventType,
        ProcessLocationUpdateRequest request,
        CancellationToken cancellationToken)
    {
        var geofenceEvent = new GeofenceEvent(
            geofenceId,
            userId,
            eventType,
            request.Latitude,
            request.Longitude,
            request.LocationContext);

        geofenceEvent.SetLocationDetails(
            request.Accuracy,
            request.Speed,
            request.Bearing,
            request.DeviceId);

        _eventRepository.Add(geofenceEvent);

        return MapToGeofenceEventDto(geofenceEvent);
    }

    private double CalculateAverageDwellTime(List<UserGeofenceStatus> statuses)
    {
        var dwellTimes = statuses
            .Where(s => s.TotalDwellTime.HasValue)
            .Select(s => s.TotalDwellTime!.Value.TotalMinutes)
            .ToList();

        return dwellTimes.Any() ? dwellTimes.Average() : 0;
    }

    // Mapping methods
    private GeofenceDto MapToGeofenceDto(Geofence geofence)
    {
        return new GeofenceDto
        {
            Id = geofence.Id,
            Name = geofence.Name,
            Description = geofence.Description,
            Type = geofence.Type,
            IsActive = geofence.IsActive,
            Coordinates = geofence.Coordinates,
            Radius = geofence.Radius,
            TriggerType = geofence.TriggerType,
            DwellTime = geofence.DwellTime,
            TriggerActions = geofence.TriggerActions,
            Conditions = geofence.Conditions,
            TargetUserGroups = geofence.TargetUserGroups,
            TargetUserIds = geofence.TargetUserIds,
            CreatedAt = geofence.CreatedAt,
            UpdatedAt = geofence.UpdatedAt,
            CreatedBy = geofence.CreatedBy,
            UpdatedBy = geofence.UpdatedBy
        };
    }

    private GeofenceEventDto MapToGeofenceEventDto(GeofenceEvent geofenceEvent)
    {
        return new GeofenceEventDto
        {
            Id = geofenceEvent.Id,
            GeofenceId = geofenceEvent.GeofenceId,
            UserId = geofenceEvent.UserId,
            EventType = geofenceEvent.EventType,
            EventTime = geofenceEvent.EventTime,
            Latitude = geofenceEvent.Latitude,
            Longitude = geofenceEvent.Longitude,
            Accuracy = geofenceEvent.Accuracy,
            Speed = geofenceEvent.Speed,
            Bearing = geofenceEvent.Bearing,
            DeviceId = geofenceEvent.DeviceId,
            LocationContext = geofenceEvent.LocationContext,
            TriggerData = geofenceEvent.TriggerData,
            IsProcessed = geofenceEvent.IsProcessed,
            ProcessedAt = geofenceEvent.ProcessedAt,
            ProcessingResult = geofenceEvent.ProcessingResult
        };
    }

    private UserGeofenceStatusDto MapToUserGeofenceStatusDto(UserGeofenceStatus status)
    {
        return new UserGeofenceStatusDto
        {
            Id = status.Id,
            GeofenceId = status.GeofenceId,
            UserId = status.UserId,
            Status = status.Status,
            StatusChangedAt = status.StatusChangedAt,
            EnteredAt = status.EnteredAt,
            ExitedAt = status.ExitedAt,
            DwellStartedAt = status.DwellStartedAt,
            TotalDwellTime = status.TotalDwellTime,
            EntryCount = status.EntryCount,
            LastLatitude = status.LastLatitude,
            LastLongitude = status.LastLongitude,
            LastLocationUpdate = status.LastLocationUpdate,
            CurrentDwellTime = status.GetCurrentDwellTime(),
            CurrentSessionTime = status.GetCurrentSessionTime(),
            IsInside = status.IsInside(),
            IsOutside = status.IsOutside(),
            IsDwelling = status.IsDwelling()
        };
    }
}

// Supporting interfaces and classes
public interface IGeofenceActionService
{
    Task<Dictionary<string, object>> ExecuteGeofenceActionsAsync(Geofence geofence, GeofenceEvent geofenceEvent, CancellationToken cancellationToken = default);
}

public class LocationUpdateResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<GeofenceEventDto> TriggeredEvents { get; set; } = new();
    public int ProcessedGeofences { get; set; }
}
