using Shared.Domain.Common;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.ValueObjects;
using MonitoringObservability.Domain.Events;

namespace MonitoringObservability.Domain.Entities;

public class Metric : AggregateRoot
{
    public string Name { get; private set; }
    public string ServiceName { get; private set; }
    public string Description { get; private set; }
    public MetricType Type { get; private set; }
    public string Unit { get; private set; }
    public MonitoringCategory Category { get; private set; }
    
    // Current state
    public MetricValue CurrentValue { get; private set; }
    public DateTime LastUpdated { get; private set; }
    public bool IsActive { get; private set; }
    
    // Thresholds and alerting
    public AlertThreshold? AlertThreshold { get; private set; }
    public bool AlertingEnabled { get; private set; }
    
    // Metadata
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }
    
    // Retention and aggregation
    public TimeSpan RetentionPeriod { get; private set; }
    public TimeSpan AggregationInterval { get; private set; }
    
    // Collections
    private readonly List<MetricDataPoint> _dataPoints = new();
    
    public IReadOnlyCollection<MetricDataPoint> DataPoints => _dataPoints.AsReadOnly();

    private Metric()
    {
        Name = string.Empty;
        ServiceName = string.Empty;
        Description = string.Empty;
        Unit = string.Empty;
        CurrentValue = null!;
        Tags = new Dictionary<string, string>();
        Metadata = new Dictionary<string, string>();
    }

    public Metric(
        string name,
        string serviceName,
        string description,
        MetricType type,
        string unit,
        MonitoringCategory category,
        TimeSpan? retentionPeriod = null,
        TimeSpan? aggregationInterval = null,
        Dictionary<string, string>? tags = null,
        Dictionary<string, string>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Metric name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(serviceName))
            throw new ArgumentException("Service name cannot be empty", nameof(serviceName));

        Id = Guid.NewGuid();
        Name = name;
        ServiceName = serviceName;
        Description = description;
        Type = type;
        Unit = unit;
        Category = category;
        IsActive = true;
        RetentionPeriod = retentionPeriod ?? TimeSpan.FromDays(30);
        AggregationInterval = aggregationInterval ?? TimeSpan.FromMinutes(1);
        Tags = tags ?? new Dictionary<string, string>();
        Metadata = metadata ?? new Dictionary<string, string>();
        LastUpdated = DateTime.UtcNow;
        
        // Initialize with zero value
        CurrentValue = new MetricValue(0, unit, type);
    }

    public void RecordValue(double value, DateTime? timestamp = null, Dictionary<string, string>? additionalTags = null)
    {
        var recordTimestamp = timestamp ?? DateTime.UtcNow;
        
        // Merge tags
        var allTags = new Dictionary<string, string>(Tags);
        if (additionalTags != null)
        {
            foreach (var tag in additionalTags)
            {
                allTags[tag.Key] = tag.Value;
            }
        }

        // Create new metric value
        var metricValue = new MetricValue(value, Unit, Type, recordTimestamp, allTags);
        CurrentValue = metricValue;
        LastUpdated = recordTimestamp;

        // Add data point
        var dataPoint = new MetricDataPoint(Id, value, recordTimestamp, allTags);
        _dataPoints.Add(dataPoint);

        // Check thresholds if alerting is enabled
        if (AlertingEnabled && AlertThreshold != null)
        {
            var severity = AlertThreshold.EvaluateThreshold(value);
            if (severity >= AlertSeverity.Warning)
            {
                AddDomainEvent(new ThresholdBreachedEvent(ServiceName, Name, value, AlertThreshold.CriticalThreshold, severity));
            }
        }

        // Raise metric recorded event
        AddDomainEvent(new MetricRecordedEvent(ServiceName, Name, value, Unit, Type, allTags));

        // Clean up old data points
        CleanupOldDataPoints();
    }

    public void SetAlertThreshold(AlertThreshold threshold)
    {
        AlertThreshold = threshold ?? throw new ArgumentNullException(nameof(threshold));
        AlertingEnabled = true;
    }

    public void DisableAlerting()
    {
        AlertingEnabled = false;
    }

    public void EnableAlerting()
    {
        if (AlertThreshold == null)
            throw new InvalidOperationException("Cannot enable alerting without setting a threshold first");

        AlertingEnabled = true;
    }

    public void UpdateMetadata(Dictionary<string, string> metadata)
    {
        Metadata = new Dictionary<string, string>(metadata);
    }

    public void AddTag(string key, string value)
    {
        Tags[key] = value;
    }

    public void RemoveTag(string key)
    {
        Tags.Remove(key);
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public IEnumerable<MetricDataPoint> GetDataPointsInRange(DateTime from, DateTime to)
    {
        return _dataPoints.Where(dp => dp.Timestamp >= from && dp.Timestamp <= to)
                         .OrderBy(dp => dp.Timestamp);
    }

    public double GetAverageValue(TimeSpan period)
    {
        var cutoff = DateTime.UtcNow - period;
        var recentPoints = _dataPoints.Where(dp => dp.Timestamp >= cutoff);
        
        return recentPoints.Any() ? recentPoints.Average(dp => dp.Value) : 0;
    }

    public double GetMaxValue(TimeSpan period)
    {
        var cutoff = DateTime.UtcNow - period;
        var recentPoints = _dataPoints.Where(dp => dp.Timestamp >= cutoff);
        
        return recentPoints.Any() ? recentPoints.Max(dp => dp.Value) : 0;
    }

    public double GetMinValue(TimeSpan period)
    {
        var cutoff = DateTime.UtcNow - period;
        var recentPoints = _dataPoints.Where(dp => dp.Timestamp >= cutoff);
        
        return recentPoints.Any() ? recentPoints.Min(dp => dp.Value) : 0;
    }

    private void CleanupOldDataPoints()
    {
        var cutoff = DateTime.UtcNow - RetentionPeriod;
        _dataPoints.RemoveAll(dp => dp.Timestamp < cutoff);
    }
}

public class MetricDataPoint : BaseEntity
{
    public Guid MetricId { get; private set; }
    public double Value { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }

    private MetricDataPoint()
    {
        Tags = new Dictionary<string, string>();
    }

    public MetricDataPoint(Guid metricId, double value, DateTime timestamp, Dictionary<string, string>? tags = null)
    {
        Id = Guid.NewGuid();
        MetricId = metricId;
        Value = value;
        Timestamp = timestamp;
        Tags = tags ?? new Dictionary<string, string>();
    }
}

public class HealthCheck : AggregateRoot
{
    public string Name { get; private set; }
    public string ServiceName { get; private set; }
    public string Description { get; private set; }
    public string Endpoint { get; private set; }
    public TimeSpan Interval { get; private set; }
    public TimeSpan Timeout { get; private set; }
    public bool IsEnabled { get; private set; }
    
    // Current state
    public HealthStatus CurrentStatus { get; private set; }
    public DateTime LastChecked { get; private set; }
    public TimeSpan LastDuration { get; private set; }
    public string? LastErrorMessage { get; private set; }
    
    // Statistics
    public int ConsecutiveFailures { get; private set; }
    public int TotalChecks { get; private set; }
    public int SuccessfulChecks { get; private set; }
    public double SuccessRate => TotalChecks > 0 ? (double)SuccessfulChecks / TotalChecks * 100 : 0;
    
    // Configuration
    public int MaxRetries { get; private set; }
    public Dictionary<string, string> Headers { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }

    private HealthCheck()
    {
        Name = string.Empty;
        ServiceName = string.Empty;
        Description = string.Empty;
        Endpoint = string.Empty;
        Headers = new Dictionary<string, string>();
        Tags = new Dictionary<string, string>();
    }

    public HealthCheck(
        string name,
        string serviceName,
        string description,
        string endpoint,
        TimeSpan interval,
        TimeSpan timeout,
        int maxRetries = 3,
        Dictionary<string, string>? headers = null,
        Dictionary<string, string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Health check name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(serviceName))
            throw new ArgumentException("Service name cannot be empty", nameof(serviceName));

        Id = Guid.NewGuid();
        Name = name;
        ServiceName = serviceName;
        Description = description;
        Endpoint = endpoint;
        Interval = interval;
        Timeout = timeout;
        MaxRetries = maxRetries;
        IsEnabled = true;
        CurrentStatus = HealthStatus.Unknown;
        Headers = headers ?? new Dictionary<string, string>();
        Tags = tags ?? new Dictionary<string, string>();
    }

    public void RecordCheckResult(HealthStatus status, TimeSpan duration, string? errorMessage = null)
    {
        var previousStatus = CurrentStatus;
        CurrentStatus = status;
        LastChecked = DateTime.UtcNow;
        LastDuration = duration;
        LastErrorMessage = errorMessage;
        TotalChecks++;

        if (status == HealthStatus.Healthy)
        {
            SuccessfulChecks++;
            ConsecutiveFailures = 0;
        }
        else
        {
            ConsecutiveFailures++;
        }

        // Raise events
        AddDomainEvent(new HealthCheckExecutedEvent(ServiceName, status, duration, errorMessage));

        if (previousStatus != status)
        {
            AddDomainEvent(new ServiceHealthChangedEvent(ServiceName, previousStatus, status, errorMessage));
        }
    }

    public void Enable()
    {
        IsEnabled = true;
    }

    public void Disable()
    {
        IsEnabled = false;
    }

    public void UpdateConfiguration(TimeSpan interval, TimeSpan timeout, int maxRetries)
    {
        Interval = interval;
        Timeout = timeout;
        MaxRetries = maxRetries;
    }

    public void UpdateHeaders(Dictionary<string, string> headers)
    {
        Headers = new Dictionary<string, string>(headers);
    }

    public bool ShouldExecuteCheck()
    {
        if (!IsEnabled)
            return false;

        return DateTime.UtcNow - LastChecked >= Interval;
    }
}
