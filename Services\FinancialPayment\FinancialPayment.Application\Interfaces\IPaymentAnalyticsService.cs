using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IPaymentAnalyticsService
{
    Task<PaymentAnalyticsSummary> GetAnalyticsSummaryAsync(PaymentAnalyticsFilter filter);
    Task<List<PaymentKPI>> GetKPIsAsync(PaymentAnalyticsFilter filter);
    Task<ChartData> GetVolumeChartAsync(PaymentAnalyticsFilter filter);
    Task<ChartData> GetTransactionCountChartAsync(PaymentAnalyticsFilter filter);
    Task<ChartData> GetPaymentMethodDistributionAsync(PaymentAnalyticsFilter filter);
    Task<ChartData> GetGatewayPerformanceAsync(PaymentAnalyticsFilter filter);
    Task<ChartData> GetSuccessRateTrendAsync(PaymentAnalyticsFilter filter);
    Task<List<PaymentTrend>> GetRevenueTrendsAsync(PaymentAnalyticsFilter filter);
    Task<PaymentReport> GenerateReportAsync(GenerateReportRequest request);
    Task<List<PaymentReport>> GetReportsAsync(Guid? userId = null);
    Task<PaymentDashboard> CreateDashboardAsync(CreateDashboardRequest request);
    Task<List<PaymentDashboard>> GetDashboardsAsync(Guid? userId = null);
    Task<PaymentDashboard> UpdateDashboardAsync(Guid dashboardId, UpdateDashboardRequest request);
}

public interface IPaymentMetricsRepository
{
    Task<PaymentMetric?> GetByIdAsync(Guid id);
    Task<List<PaymentMetric>> GetMetricsAsync(string metricName, DateTime fromDate, DateTime toDate, string? dimension = null);
    Task<List<PaymentMetric>> GetMetricsByDimensionAsync(string dimension, string dimensionValue, DateTime fromDate, DateTime toDate);
    Task AddAsync(PaymentMetric metric);
    Task UpdateAsync(PaymentMetric metric);
    Task DeleteAsync(Guid id);
    Task<List<PaymentMetric>> GetLatestMetricsAsync(List<string> metricNames);
}

public interface IPaymentReportRepository
{
    Task<PaymentReport?> GetByIdAsync(Guid id);
    Task<List<PaymentReport>> GetByUserAsync(Guid userId);
    Task<List<PaymentReport>> GetByTypeAsync(string reportType);
    Task<List<PaymentReport>> GetByStatusAsync(ReportStatus status);
    Task AddAsync(PaymentReport report);
    Task UpdateAsync(PaymentReport report);
    Task DeleteAsync(Guid id);
}

public interface IPaymentDashboardRepository
{
    Task<PaymentDashboard?> GetByIdAsync(Guid id);
    Task<List<PaymentDashboard>> GetByUserAsync(Guid userId);
    Task<List<PaymentDashboard>> GetPublicDashboardsAsync();
    Task AddAsync(PaymentDashboard dashboard);
    Task UpdateAsync(PaymentDashboard dashboard);
    Task DeleteAsync(Guid id);
}

public interface IPaymentDataAggregator
{
    Task<PaymentAnalyticsSummary> AggregatePaymentDataAsync(PaymentAnalyticsFilter filter);
    Task<List<PaymentTrend>> AggregateVolumeDataAsync(PaymentAnalyticsFilter filter);
    Task<List<PaymentTrend>> AggregateTransactionCountAsync(PaymentAnalyticsFilter filter);
    Task<Dictionary<string, decimal>> AggregatePaymentMethodDataAsync(PaymentAnalyticsFilter filter);
    Task<Dictionary<string, decimal>> AggregateGatewayDataAsync(PaymentAnalyticsFilter filter);
    Task<List<PaymentTrend>> AggregateSuccessRateAsync(PaymentAnalyticsFilter filter);
}

public interface IReportGenerator
{
    Task<string> GenerateExcelReportAsync(PaymentAnalyticsSummary data, string reportName);
    Task<string> GeneratePdfReportAsync(PaymentAnalyticsSummary data, string reportName);
    Task<string> GenerateCsvReportAsync(List<PaymentTrend> data, string reportName);
    Task<byte[]> GenerateChartImageAsync(ChartData chartData, string format = "png");
}

// Request/Response DTOs
public class GenerateReportRequest
{
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public string Format { get; set; } = "excel"; // excel, pdf, csv
    public PaymentAnalyticsFilter Filter { get; set; } = new();
    public Guid? GeneratedBy { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class CreateDashboardRequest
{
    public string DashboardName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? OwnerId { get; set; }
    public bool IsPublic { get; set; } = false;
    public int RefreshIntervalMinutes { get; set; } = 15;
    public List<CreateWidgetRequest> Widgets { get; set; } = new();
}

public class CreateWidgetRequest
{
    public string WidgetName { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int Position { get; set; } = 0;
}

public class UpdateDashboardRequest
{
    public string? DashboardName { get; set; }
    public string? Description { get; set; }
    public bool? IsPublic { get; set; }
    public int? RefreshIntervalMinutes { get; set; }
    public List<UpdateWidgetRequest>? Widgets { get; set; }
}

public class UpdateWidgetRequest
{
    public Guid? WidgetId { get; set; }
    public string? WidgetName { get; set; }
    public string? WidgetType { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public int? Position { get; set; }
    public bool? IsVisible { get; set; }
}

public class AnalyticsConfiguration
{
    public bool EnableRealTimeAnalytics { get; set; } = true;
    public int MetricsRetentionDays { get; set; } = 365;
    public int ReportRetentionDays { get; set; } = 90;
    public int MaxReportsPerUser { get; set; } = 50;
    public int MaxDashboardsPerUser { get; set; } = 10;
    public int MaxWidgetsPerDashboard { get; set; } = 20;
    public List<string> AllowedReportFormats { get; set; } = new() { "excel", "pdf", "csv" };
    public List<string> AllowedChartTypes { get; set; } = new() { "line", "bar", "pie", "doughnut" };
    public Dictionary<string, string> DefaultColors { get; set; } = new();
    public string ReportsStoragePath { get; set; } = "reports";
}

public class MetricsCalculationRequest
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public List<string> MetricNames { get; set; } = new();
    public List<string> Dimensions { get; set; } = new();
    public bool ForceRecalculation { get; set; } = false;
}

public class DashboardData
{
    public PaymentDashboard Dashboard { get; set; } = new("Default Dashboard", "Default dashboard description");
    public List<WidgetData> WidgetData { get; set; } = new();
    public DateTime LastRefreshed { get; set; }
}

public class WidgetData
{
    public Guid WidgetId { get; set; }
    public string WidgetType { get; set; } = string.Empty;
    public object Data { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
}
