using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AttachMilestoneTemplate;

public class AttachMilestoneTemplateCommandHandler : IRequestHandler<AttachMilestoneTemplateCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMilestoneTemplateRepository _milestoneTemplateRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AttachMilestoneTemplateCommandHandler> _logger;

    public AttachMilestoneTemplateCommandHandler(
        IRfqRepository rfqRepository,
        IMilestoneTemplateRepository milestoneTemplateRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<AttachMilestoneTemplateCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _milestoneTemplateRepository = milestoneTemplateRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(AttachMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Attaching milestone template {TemplateId} to RFQ {RfqId} by user {AssignedBy}",
            request.MilestoneTemplateId, request.RfqId, request.AssignedBy);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return false;
            }

            // Get the milestone template
            var milestoneTemplate = await _milestoneTemplateRepository.GetByIdAsync(request.MilestoneTemplateId, cancellationToken);
            if (milestoneTemplate == null)
            {
                _logger.LogWarning("Milestone template {TemplateId} not found", request.MilestoneTemplateId);
                return false;
            }

            // Validate template is active
            if (!milestoneTemplate.IsActive)
            {
                _logger.LogWarning("Cannot attach inactive milestone template {TemplateId}", request.MilestoneTemplateId);
                return false;
            }

            // Validate payout structure
            if (!milestoneTemplate.IsValidPayoutStructure())
            {
                _logger.LogWarning("Milestone template {TemplateId} has invalid payout structure (total: {Total}%)", 
                    request.MilestoneTemplateId, milestoneTemplate.GetTotalPayoutPercentage());
                return false;
            }

            // Create milestone assignment
            var assignment = new RfqMilestoneAssignment(
                request.RfqId,
                request.MilestoneTemplateId,
                request.AssignedBy,
                request.AssignedByName,
                request.TotalContractValue,
                request.CustomPayoutStructure,
                request.AssignmentMetadata);

            // Attach to RFQ (this will deactivate any existing assignments)
            rfq.AttachMilestoneTemplate(assignment);

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var timelineEvent = RfqTimeline.CreateEvent(
                    request.RfqId,
                    RfqTimelineEventType.MilestoneTemplateAttached,
                    $"Milestone template '{milestoneTemplate.Name}' attached to RFQ",
                    request.AssignedBy,
                    request.AssignedByName,
                    "Transport Company",
                    $"{{\"milestoneTemplateId\": \"{request.MilestoneTemplateId}\", \"templateName\": \"{milestoneTemplate.Name}\", \"templateType\": \"{milestoneTemplate.TemplateType}\", \"totalSteps\": {milestoneTemplate.Steps.Count}, \"totalContractValue\": \"{request.TotalContractValue?.Amount} {request.TotalContractValue?.Currency}\"}}");

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully attached milestone template {TemplateId} to RFQ {RfqId}", 
                request.MilestoneTemplateId, request.RfqId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error attaching milestone template {TemplateId} to RFQ {RfqId}", 
                request.MilestoneTemplateId, request.RfqId);
            throw;
        }
    }
}
