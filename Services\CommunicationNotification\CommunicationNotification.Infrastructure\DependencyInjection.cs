using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Infrastructure.Configuration;
using CommunicationNotification.Infrastructure.ExternalServices;
using CommunicationNotification.Infrastructure.HealthChecks;
using CommunicationNotification.Infrastructure.Hubs;
using CommunicationNotification.Infrastructure.Persistence;
using CommunicationNotification.Infrastructure.Providers;
using CommunicationNotification.Infrastructure.Services;
using CommunicationNotification.Infrastructure.Webhooks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure;

/// <summary>
/// Dependency injection configuration for Communication & Notification Infrastructure layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add infrastructure services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Add HTTP clients for external providers
        services.AddHttpClients(configuration);

        // Add notification providers
        services.AddNotificationProviders(configuration);

        // Add webhook services
        services.AddWebhookServices();

        // Add other infrastructure services
        services.AddInfrastructureServices(configuration);

        // Add SignalR
        services.AddSignalRServices(configuration);

        return services;
    }

    /// <summary>
    /// Add HTTP clients for external providers
    /// </summary>
    private static IServiceCollection AddHttpClients(this IServiceCollection services, IConfiguration configuration)
    {
        // WhatsApp HTTP client
        services.AddHttpClient<WhatsAppBusinessProvider>((serviceProvider, client) =>
        {
            var whatsAppConfig = configuration.GetSection("WhatsApp").Get<WhatsAppConfiguration>();
            if (whatsAppConfig != null)
            {
                client.BaseAddress = new Uri(whatsAppConfig.BaseUrl);
                client.Timeout = TimeSpan.FromSeconds(whatsAppConfig.TimeoutSeconds);
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {whatsAppConfig.AccessToken}");
                client.DefaultRequestHeaders.Add("User-Agent", "TLI-Communication-Service/1.0");
            }
        });

        // Twilio HTTP client (for SMS and Voice)
        services.AddHttpClient("TwilioProvider", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // SendGrid HTTP client (for Email)
        services.AddHttpClient("SendGridProvider", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // Firebase HTTP client (for Push notifications)
        services.AddHttpClient("FirebaseProvider", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        return services;
    }

    /// <summary>
    /// Add notification providers
    /// </summary>
    private static IServiceCollection AddNotificationProviders(this IServiceCollection services, IConfiguration configuration)
    {
        // WhatsApp provider
        services.AddScoped<IWhatsAppProvider, WhatsAppBusinessProvider>();

        // SMS provider
        services.AddScoped<ISmsProvider, TwilioSmsProvider>();

        // Email provider
        services.AddScoped<IEmailProvider, SendGridEmailProvider>();

        // Push notification provider
        services.AddScoped<IPushNotificationProvider, FirebasePushProvider>();

        // Voice provider
        services.AddScoped<IVoiceProvider, TwilioVoiceProvider>();

        // Register all providers as generic notification providers
        services.AddScoped<INotificationProvider>(serviceProvider =>
            serviceProvider.GetRequiredService<IWhatsAppProvider>());
        services.AddScoped<INotificationProvider>(serviceProvider =>
            serviceProvider.GetRequiredService<ISmsProvider>());
        services.AddScoped<INotificationProvider>(serviceProvider =>
            serviceProvider.GetRequiredService<IEmailProvider>());
        services.AddScoped<INotificationProvider>(serviceProvider =>
            serviceProvider.GetRequiredService<IPushNotificationProvider>());
        services.AddScoped<INotificationProvider>(serviceProvider =>
            serviceProvider.GetRequiredService<IVoiceProvider>());

        // Notification provider factory
        services.AddScoped<INotificationProviderFactory, NotificationProviderFactory>();

        return services;
    }

    /// <summary>
    /// Add webhook services
    /// </summary>
    private static IServiceCollection AddWebhookServices(this IServiceCollection services)
    {
        services.AddScoped<IWhatsAppWebhookService, WhatsAppWebhookService>();

        return services;
    }

    /// <summary>
    /// Add other infrastructure services
    /// </summary>
    private static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configuration binding
        services.Configure<WhatsAppConfiguration>(configuration.GetSection("WhatsApp"));
        services.Configure<TwilioConfiguration>(configuration.GetSection("Twilio"));
        services.Configure<SendGridConfiguration>(configuration.GetSection("SendGrid"));
        services.Configure<FirebaseConfiguration>(configuration.GetSection("Firebase"));

        // Core notification service
        services.AddScoped<INotificationService, NotificationOrchestrationService>();

        // Chat services
        services.AddScoped<IChatService, ChatService>();
        services.AddScoped<ICommunicationHub, SignalRCommunicationHub>();
        services.AddScoped<IConnectionTrackingService, InMemoryConnectionTrackingService>();
        services.AddScoped<IOfflineMessageService, OfflineMessageService>();

        // Localization services
        services.AddScoped<ILocalizationService, LocalizationService>();
        services.AddScoped<ITemplateService, TemplateService>();
        services.AddScoped<ITranslationService, TranslationService>();
        services.AddScoped<ILocalizationRepository, Repositories.InMemoryLocalizationRepository>();

        // Translation providers
        services.AddScoped<ITranslationProvider, GoogleTranslateProvider>();
        services.AddHttpClient<GoogleTranslateProvider>();
        services.AddHttpClient<AzureTranslatorProvider>();

        // Language context services
        services.AddHttpContextAccessor();
        services.AddScoped<ILanguageContext, Middleware.LanguageContext>();
        services.AddScoped<Middleware.LocalizedNotificationService>();

        // Driver communication services
        services.AddScoped<IDriverCommunicationService, DriverCommunicationService>();
        services.AddScoped<IVoiceInstructionService, VoiceInstructionService>();
        services.AddScoped<IDriverOfflineService, DriverOfflineService>();

        // Audit and compliance services
        services.AddScoped<ICommunicationAuditService, CommunicationAuditService>();
        services.AddScoped<IAuditRepository, Repositories.InMemoryAuditRepository>();
        services.AddScoped<IComplianceRepository, Repositories.InMemoryComplianceRepository>();
        services.AddHostedService<ComplianceMonitoringService>();

        // Database context
        services.AddDbContext<Persistence.CommunicationDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection") ??
                "Host=localhost;Port=5432;Database=tli_communication_dev;User Id=timescale;Password=timescale;Include Error Detail=true";

            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsAssembly(typeof(Infrastructure.AssemblyReference).Assembly.FullName);
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });

            if (configuration.GetValue<bool>("Database:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            if (configuration.GetValue<bool>("Database:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // Unit of Work pattern
        services.AddScoped<Application.Interfaces.IUnitOfWork, Persistence.UnitOfWork>();

        // Repository pattern
        services.AddScoped<Application.Interfaces.INotificationRepository, Repositories.NotificationRepository>();
        services.AddScoped<Application.Interfaces.IMessageRepository, Repositories.MessageRepository>();
        services.AddScoped<Application.Interfaces.IUserPreferenceRepository, Repositories.UserPreferenceRepository>();
        services.AddScoped<Application.Interfaces.IConversationRepository, Repositories.ConversationRepository>();

        // Database seeder
        services.AddScoped<Persistence.Seed.DatabaseSeeder>();

        // CQRS with MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(typeof(Application.AssemblyReference).Assembly);
            cfg.RegisterServicesFromAssembly(typeof(Infrastructure.AssemblyReference).Assembly);
        });

        // External service providers
        services.AddHttpClient<TwilioSmsProvider>();
        services.AddHttpClient<SendGridEmailProvider>();
        services.AddHttpClient<WhatsAppBusinessProvider>();
        services.AddScoped<TwilioSmsProvider>();
        services.AddScoped<SendGridEmailProvider>();
        services.AddScoped<FirebasePushProvider>();
        services.AddScoped<TwilioVoiceProvider>();
        services.AddScoped<WhatsAppBusinessProvider>();

        // SendGrid client
        services.AddScoped<SendGrid.ISendGridClient>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var apiKey = configuration.GetSection("SendGrid:ApiKey").Value ?? "";
            return new SendGrid.SendGridClient(apiKey);
        });

        // External service factory
        services.AddScoped<IExternalServiceFactory, ExternalServiceFactory>();

        // External service health monitoring
        services.AddSingleton<ExternalServiceHealthCheckService>();
        services.AddHostedService<ExternalServiceHealthCheckService>(provider =>
            provider.GetRequiredService<ExternalServiceHealthCheckService>());

        // Rate limiting service
        services.AddMemoryCache();
        services.AddScoped<IRateLimitingService, InMemoryRateLimitingService>();

        // Metrics service
        services.AddScoped<INotificationMetricsService, NotificationMetricsService>();

        // Health checks
        services.AddNotificationHealthChecks();

        // API configuration
        services.AddApiConfiguration(configuration);

        // Additional health checks
        services.AddScoped<HealthChecks.DatabaseHealthCheck>();
        services.AddScoped<HealthChecks.ExternalServicesHealthCheck>();
        services.AddScoped<HealthChecks.SignalRHealthCheck>();
        services.AddScoped<HealthChecks.MemoryHealthCheck>();
        services.AddScoped<HealthChecks.ApplicationHealthCheck>();

        // Authorization handlers
        services.AddScoped<Configuration.SameUserAuthorizationHandler>();

        // TODO: Add other infrastructure services when implemented
        // services.AddScoped<ILocalizationService, LocalizationService>();
        // services.AddScoped<ITemplateService, TemplateService>();
        // services.AddScoped<IChatService, ChatService>();
        // services.AddScoped<IAuditService, AuditService>();

        return services;
    }

    /// <summary>
    /// Add SignalR services
    /// </summary>
    private static IServiceCollection AddSignalRServices(this IServiceCollection services, IConfiguration configuration)
    {
        var signalRBuilder = services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
            options.HandshakeTimeout = TimeSpan.FromSeconds(15);
        });

        // Add Redis backplane if configured
        var redisConnectionString = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            signalRBuilder.AddStackExchangeRedis(redisConnectionString, options =>
            {
                options.Configuration.ChannelPrefix = "TLI-Chat";
            });
        }

        return services;
    }
}

/// <summary>
/// Notification provider factory implementation
/// </summary>
public class NotificationProviderFactory : INotificationProviderFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationProviderFactory> _logger;

    public NotificationProviderFactory(IServiceProvider serviceProvider, ILogger<NotificationProviderFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public INotificationProvider GetProvider(Domain.Enums.NotificationChannel channel)
    {
        try
        {
            return channel switch
            {
                Domain.Enums.NotificationChannel.WhatsApp => _serviceProvider.GetRequiredService<IWhatsAppProvider>(),
                Domain.Enums.NotificationChannel.Sms => _serviceProvider.GetRequiredService<ISmsProvider>(),
                Domain.Enums.NotificationChannel.Email => _serviceProvider.GetRequiredService<IEmailProvider>(),
                Domain.Enums.NotificationChannel.Push => _serviceProvider.GetRequiredService<IPushNotificationProvider>(),
                Domain.Enums.NotificationChannel.Voice => _serviceProvider.GetRequiredService<IVoiceProvider>(),
                _ => throw new NotSupportedException($"Notification channel {channel} is not supported")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get provider for channel {Channel}", channel);
            throw;
        }
    }

    public List<INotificationProvider> GetAllProviders()
    {
        var providers = new List<INotificationProvider>();

        try
        {
            // Add WhatsApp provider if available
            var whatsAppProvider = _serviceProvider.GetService<IWhatsAppProvider>();
            if (whatsAppProvider != null)
            {
                providers.Add(whatsAppProvider);
            }

            // Add SMS provider if available
            var smsProvider = _serviceProvider.GetService<ISmsProvider>();
            if (smsProvider != null)
            {
                providers.Add(smsProvider);
            }

            // Add Email provider if available
            var emailProvider = _serviceProvider.GetService<IEmailProvider>();
            if (emailProvider != null)
            {
                providers.Add(emailProvider);
            }

            // Add Push notification provider if available
            var pushProvider = _serviceProvider.GetService<IPushNotificationProvider>();
            if (pushProvider != null)
            {
                providers.Add(pushProvider);
            }

            // Add Voice provider if available
            var voiceProvider = _serviceProvider.GetService<IVoiceProvider>();
            if (voiceProvider != null)
            {
                providers.Add(voiceProvider);
            }

            return providers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all providers");
            return providers;
        }
    }

    public bool IsProviderAvailable(Domain.Enums.NotificationChannel channel)
    {
        try
        {
            var provider = GetProvider(channel);
            return provider.IsAvailableAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check provider availability for channel {Channel}", channel);
            return false;
        }
    }
}
