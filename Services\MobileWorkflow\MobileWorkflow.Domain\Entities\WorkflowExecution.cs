
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class WorkflowExecution : AggregateRoot
{
    public Guid WorkflowId { get; private set; }
    public Guid TriggeredBy { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public string Status { get; private set; } // Running, Completed, Failed, Cancelled
    public Dictionary<string, object> InputData { get; private set; }
    public Dictionary<string, object> OutputData { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? TriggerSource { get; private set; }
    public int CurrentStepIndex { get; private set; }
    public string? CurrentStepId { get; private set; }
    public Dictionary<string, object> ExecutionLog { get; private set; }

    // Navigation properties
    public virtual Workflow Workflow { get; private set; } = null!;
    public virtual ICollection<WorkflowTask> Tasks { get; private set; } = new List<WorkflowTask>();

    private WorkflowExecution() { } // EF Core

    public WorkflowExecution(
        Guid workflowId,
        Guid triggeredBy,
        Dictionary<string, object> inputData,
        string? triggerSource = null)
    {
        WorkflowId = workflowId;
        TriggeredBy = triggeredBy;
        StartTime = DateTime.UtcNow;
        Status = "Running";
        InputData = inputData ?? new Dictionary<string, object>();
        OutputData = new Dictionary<string, object>();
        Context = new Dictionary<string, object>();
        TriggerSource = triggerSource;
        CurrentStepIndex = 0;
        ExecutionLog = new Dictionary<string, object>();

        AddDomainEvent(new WorkflowExecutionStartedEvent(Id, WorkflowId, "Unknown", TriggeredBy.ToString()));
    }

    public void MoveToNextStep(string stepId)
    {
        CurrentStepIndex++;
        CurrentStepId = stepId;
        LogStep($"Moved to step {CurrentStepIndex}: {stepId}");
    }

    public void UpdateContext(string key, object value)
    {
        Context[key] = value;
        LogStep($"Context updated: {key}");
    }

    public void SetOutput(Dictionary<string, object> outputData)
    {
        OutputData = outputData ?? throw new ArgumentNullException(nameof(outputData));
        LogStep("Output data set");
    }

    public void Complete()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete execution with status: {Status}");

        Status = "Completed";
        EndTime = DateTime.UtcNow;
        LogStep("Execution completed successfully");

        var duration = EndTime.Value - StartTime;
        AddDomainEvent(new WorkflowExecutionCompletedEvent(Id, WorkflowId, "Unknown", true));
    }

    public void Fail(string errorMessage)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot fail execution with status: {Status}");

        Status = "Failed";
        EndTime = DateTime.UtcNow;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        LogStep($"Execution failed: {errorMessage}");

        AddDomainEvent(new WorkflowExecutionFailedEvent(Id, WorkflowId, "Unknown", errorMessage));
    }

    public void Cancel()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot cancel execution with status: {Status}");

        Status = "Cancelled";
        EndTime = DateTime.UtcNow;
        LogStep("Execution cancelled");

        AddDomainEvent(new WorkflowExecutionCompletedEvent(Id, WorkflowId, "Unknown", false)); // Cancelled = not successful
    }

    public WorkflowTask CreateTask(string name, string type, Dictionary<string, object> parameters, Guid? assignedTo = null)
    {
        var task = new WorkflowTask(Id, name, type, parameters, assignedTo);
        Tasks.Add(task);
        LogStep($"Task created: {name} ({type})");
        return task;
    }

    private void LogStep(string message)
    {
        var timestamp = DateTime.UtcNow;
        var logKey = $"step_{CurrentStepIndex}_{timestamp:yyyyMMddHHmmss}";
        ExecutionLog[logKey] = new { Timestamp = timestamp, Message = message, StepIndex = CurrentStepIndex, StepId = CurrentStepId };
    }

    public TimeSpan GetExecutionDuration()
    {
        var endTime = EndTime ?? DateTime.UtcNow;
        return endTime - StartTime;
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Cancelled";
    }
}



