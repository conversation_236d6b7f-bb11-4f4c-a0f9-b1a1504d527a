using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Security
{
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private readonly RateLimitOptions _options;

        public RateLimitingMiddleware(
            RequestDelegate next,
            IMemoryCache cache,
            ILogger<RateLimitingMiddleware> logger,
            RateLimitOptions options)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip rate limiting for non-notification endpoints
            if (!ShouldApplyRateLimit(context.Request.Path))
            {
                await _next(context);
                return;
            }

            var clientId = GetClientIdentifier(context);
            var endpoint = GetEndpointKey(context.Request.Path);
            var cacheKey = $"rate_limit:{clientId}:{endpoint}";

            var rateLimitInfo = _cache.GetOrCreate(cacheKey, entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.WindowMinutes);
                return new RateLimitInfo
                {
                    RequestCount = 0,
                    WindowStart = DateTime.UtcNow
                };
            });

            // Check if window has expired
            if (DateTime.UtcNow - rateLimitInfo.WindowStart > TimeSpan.FromMinutes(_options.WindowMinutes))
            {
                rateLimitInfo.RequestCount = 0;
                rateLimitInfo.WindowStart = DateTime.UtcNow;
            }

            rateLimitInfo.RequestCount++;

            var limit = GetRateLimitForEndpoint(endpoint);
            
            if (rateLimitInfo.RequestCount > limit)
            {
                _logger.LogWarning("Rate limit exceeded for client {ClientId} on endpoint {Endpoint}. Count: {Count}, Limit: {Limit}", 
                    clientId, endpoint, rateLimitInfo.RequestCount, limit);

                await HandleRateLimitExceeded(context, rateLimitInfo, limit);
                return;
            }

            // Add rate limit headers
            context.Response.Headers.Add("X-RateLimit-Limit", limit.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", Math.Max(0, limit - rateLimitInfo.RequestCount).ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", rateLimitInfo.WindowStart.AddMinutes(_options.WindowMinutes).ToString("O"));

            await _next(context);
        }

        private bool ShouldApplyRateLimit(PathString path)
        {
            var pathValue = path.Value?.ToLowerInvariant() ?? "";
            return pathValue.Contains("/api/admin/notifications") || 
                   pathValue.Contains("/api/admin/payment-proofs") ||
                   pathValue.Contains("/api/subscriptions") && pathValue.Contains("/payment-proof");
        }

        private string GetClientIdentifier(HttpContext context)
        {
            // Try to get user ID from claims first
            var userId = context.User?.FindFirst("sub")?.Value ?? 
                        context.User?.FindFirst("userId")?.Value;
            
            if (!string.IsNullOrEmpty(userId))
            {
                return $"user:{userId}";
            }

            // Fall back to IP address
            var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            return $"ip:{ipAddress}";
        }

        private string GetEndpointKey(PathString path)
        {
            var pathValue = path.Value?.ToLowerInvariant() ?? "";
            
            if (pathValue.Contains("/api/admin/notifications/subscription-alerts"))
                return "bulk_alerts";
            else if (pathValue.Contains("/api/admin/notifications"))
                return "manual_notifications";
            else if (pathValue.Contains("/api/admin/payment-proofs"))
                return "payment_verification";
            else if (pathValue.Contains("/payment-proof"))
                return "payment_upload";
            
            return "general";
        }

        private int GetRateLimitForEndpoint(string endpoint)
        {
            return endpoint switch
            {
                "bulk_alerts" => _options.BulkAlertsLimit,
                "manual_notifications" => _options.ManualNotificationsLimit,
                "payment_verification" => _options.PaymentVerificationLimit,
                "payment_upload" => _options.PaymentUploadLimit,
                _ => _options.GeneralLimit
            };
        }

        private async Task HandleRateLimitExceeded(HttpContext context, RateLimitInfo rateLimitInfo, int limit)
        {
            context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
            context.Response.ContentType = "application/json";

            var resetTime = rateLimitInfo.WindowStart.AddMinutes(_options.WindowMinutes);
            var retryAfter = (int)(resetTime - DateTime.UtcNow).TotalSeconds;

            context.Response.Headers.Add("Retry-After", retryAfter.ToString());
            context.Response.Headers.Add("X-RateLimit-Limit", limit.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", "0");
            context.Response.Headers.Add("X-RateLimit-Reset", resetTime.ToString("O"));

            var response = new
            {
                error = "Rate limit exceeded",
                message = $"Too many requests. Limit: {limit} requests per {_options.WindowMinutes} minutes",
                retryAfter = retryAfter,
                resetTime = resetTime
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }
    }

    public class RateLimitInfo
    {
        public int RequestCount { get; set; }
        public DateTime WindowStart { get; set; }
    }

    public class RateLimitOptions
    {
        public int WindowMinutes { get; set; } = 15;
        public int GeneralLimit { get; set; } = 100;
        public int ManualNotificationsLimit { get; set; } = 50;
        public int BulkAlertsLimit { get; set; } = 10;
        public int PaymentVerificationLimit { get; set; } = 200;
        public int PaymentUploadLimit { get; set; } = 20;
    }
}
