using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IBusinessProcessAutomationService
{
    Task<AutomationResult> CreateBusinessProcessAsync(BusinessProcessDefinition definition, CancellationToken cancellationToken = default);
    Task<AutomationResult> TriggerProcessAsync(string processName, Dictionary<string, object> triggerData, Guid triggeredBy, CancellationToken cancellationToken = default);
    Task<List<BusinessProcess>> GetActiveProcessesAsync(CancellationToken cancellationToken = default);
    Task<BusinessProcessStatus> GetProcessStatusAsync(Guid processId, CancellationToken cancellationToken = default);
    Task<bool> UpdateProcessConfigurationAsync(Guid processId, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
    Task<List<ProcessTemplate>> GetProcessTemplatesAsync(string category, CancellationToken cancellationToken = default);
    Task<AutomationMetrics> GetAutomationMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

public class BusinessProcessAutomationService : IBusinessProcessAutomationService
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowExecutionRepository _executionRepository;
    private readonly IWorkflowExecutionEngine _executionEngine;
    private readonly IMemoryCache _cache;
    private readonly ILogger<BusinessProcessAutomationService> _logger;

    public BusinessProcessAutomationService(
        IWorkflowRepository workflowRepository,
        IWorkflowExecutionRepository executionRepository,
        IWorkflowExecutionEngine executionEngine,
        IMemoryCache cache,
        ILogger<BusinessProcessAutomationService> logger)
    {
        _workflowRepository = workflowRepository;
        _executionRepository = executionRepository;
        _executionEngine = executionEngine;
        _cache = cache;
        _logger = logger;
    }

    public async Task<AutomationResult> CreateBusinessProcessAsync(BusinessProcessDefinition definition, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating business process: {ProcessName}", definition.Name);

        try
        {
            // Validate process definition
            var validationResult = ValidateProcessDefinition(definition);
            if (!validationResult.IsValid)
            {
                return new AutomationResult { IsSuccess = false, ErrorMessage = validationResult.ErrorMessage };
            }

            // Convert to workflow
            var workflow = new Workflow(
                definition.Name,
                definition.Description,
                definition.Category,
                definition.Version,
                ConvertToWorkflowDefinition(definition),
                definition.Configuration,
                definition.TriggerType,
                definition.TriggerConfiguration,
                definition.CreatedBy
            );

            var savedWorkflow = await _workflowRepository.AddAsync(workflow, cancellationToken);

            _logger.LogInformation("Business process created successfully with ID: {ProcessId}", savedWorkflow.Id);

            return new AutomationResult 
            { 
                IsSuccess = true, 
                ProcessId = savedWorkflow.Id,
                Message = "Business process created successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating business process: {ProcessName}", definition.Name);
            return new AutomationResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<AutomationResult> TriggerProcessAsync(string processName, Dictionary<string, object> triggerData, Guid triggeredBy, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Triggering business process: {ProcessName} by user: {TriggeredBy}", processName, triggeredBy);

        try
        {
            var workflow = await _workflowRepository.GetByNameAsync(processName, cancellationToken);
            if (workflow == null)
            {
                return new AutomationResult { IsSuccess = false, ErrorMessage = $"Process '{processName}' not found" };
            }

            if (!workflow.IsActive)
            {
                return new AutomationResult { IsSuccess = false, ErrorMessage = $"Process '{processName}' is not active" };
            }

            // Check trigger conditions
            if (!EvaluateTriggerConditions(workflow.TriggerConfiguration, triggerData))
            {
                return new AutomationResult { IsSuccess = false, ErrorMessage = "Trigger conditions not met" };
            }

            var executionResult = await _executionEngine.ExecuteWorkflowAsync(
                workflow.Id, 
                triggeredBy, 
                triggerData, 
                "BusinessProcessAutomation", 
                cancellationToken
            );

            if (executionResult.IsSuccess)
            {
                _logger.LogInformation("Business process triggered successfully. Execution ID: {ExecutionId}", executionResult.ExecutionId);
            }

            return new AutomationResult 
            { 
                IsSuccess = executionResult.IsSuccess,
                ProcessId = workflow.Id,
                ExecutionId = executionResult.ExecutionId,
                Message = executionResult.Message,
                ErrorMessage = executionResult.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering business process: {ProcessName}", processName);
            return new AutomationResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<List<BusinessProcess>> GetActiveProcessesAsync(CancellationToken cancellationToken = default)
    {
        var workflows = await _workflowRepository.GetActiveWorkflowsAsync(cancellationToken);
        
        var processes = new List<BusinessProcess>();
        foreach (var workflow in workflows)
        {
            var recentExecutions = await _executionRepository.GetByWorkflowIdAsync(workflow.Id, cancellationToken);
            
            processes.Add(new BusinessProcess
            {
                Id = workflow.Id,
                Name = workflow.Name,
                Description = workflow.Description,
                Category = workflow.Category,
                IsActive = workflow.IsActive,
                TriggerType = workflow.TriggerType,
                LastExecuted = workflow.LastExecuted,
                ExecutionCount = workflow.ExecutionCount,
                RecentExecutions = recentExecutions.Take(5).Select(e => new ProcessExecution
                {
                    Id = e.Id,
                    Status = e.Status,
                    StartTime = e.StartTime,
                    EndTime = e.EndTime,
                    TriggeredBy = e.TriggeredBy
                }).ToList()
            });
        }

        return processes;
    }

    public async Task<BusinessProcessStatus> GetProcessStatusAsync(Guid processId, CancellationToken cancellationToken = default)
    {
        var workflow = await _workflowRepository.GetByIdAsync(processId, cancellationToken);
        if (workflow == null)
        {
            return new BusinessProcessStatus { ProcessId = processId, Status = "NotFound" };
        }

        var executions = await _executionRepository.GetByWorkflowIdAsync(processId, cancellationToken);
        var runningExecutions = executions.Where(e => e.Status == "Running").ToList();

        return new BusinessProcessStatus
        {
            ProcessId = processId,
            ProcessName = workflow.Name,
            Status = workflow.IsActive ? "Active" : "Inactive",
            RunningExecutions = runningExecutions.Count,
            TotalExecutions = workflow.ExecutionCount,
            LastExecuted = workflow.LastExecuted,
            SuccessRate = CalculateSuccessRate(executions),
            AverageExecutionTime = CalculateAverageExecutionTime(executions)
        };
    }

    public async Task<bool> UpdateProcessConfigurationAsync(Guid processId, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(processId, cancellationToken);
            if (workflow == null) return false;

            workflow.UpdateConfiguration(configuration);
            await _workflowRepository.UpdateAsync(workflow, cancellationToken);

            _logger.LogInformation("Process configuration updated for process: {ProcessId}", processId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating process configuration for process: {ProcessId}", processId);
            return false;
        }
    }

    public async Task<List<ProcessTemplate>> GetProcessTemplatesAsync(string category, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"process_templates_{category}";
        
        if (_cache.TryGetValue(cacheKey, out List<ProcessTemplate>? cachedTemplates) && cachedTemplates != null)
        {
            return cachedTemplates;
        }

        var templates = GetBuiltInProcessTemplates(category);
        _cache.Set(cacheKey, templates, TimeSpan.FromHours(1));
        
        return templates;
    }

    public async Task<AutomationMetrics> GetAutomationMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var executions = await _executionRepository.GetExecutionsInDateRangeAsync(startDate, endDate, cancellationToken);
        
        var metrics = new AutomationMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalExecutions = executions.Count(),
            SuccessfulExecutions = executions.Count(e => e.Status == "Completed"),
            FailedExecutions = executions.Count(e => e.Status == "Failed"),
            AverageExecutionTime = CalculateAverageExecutionTime(executions),
            ProcessBreakdown = GetProcessBreakdown(executions),
            DailyExecutionCounts = GetDailyExecutionCounts(executions, startDate, endDate)
        };

        metrics.SuccessRate = metrics.TotalExecutions > 0 
            ? (double)metrics.SuccessfulExecutions / metrics.TotalExecutions * 100 
            : 0;

        return metrics;
    }

    private ProcessValidationResult ValidateProcessDefinition(BusinessProcessDefinition definition)
    {
        if (string.IsNullOrEmpty(definition.Name))
            return new ProcessValidationResult { IsValid = false, ErrorMessage = "Process name is required" };

        if (definition.Steps == null || !definition.Steps.Any())
            return new ProcessValidationResult { IsValid = false, ErrorMessage = "Process must have at least one step" };

        // Validate each step
        foreach (var step in definition.Steps)
        {
            if (string.IsNullOrEmpty(step.Name))
                return new ProcessValidationResult { IsValid = false, ErrorMessage = "All steps must have a name" };

            if (string.IsNullOrEmpty(step.Type))
                return new ProcessValidationResult { IsValid = false, ErrorMessage = "All steps must have a type" };
        }

        return new ProcessValidationResult { IsValid = true };
    }

    private Dictionary<string, object> ConvertToWorkflowDefinition(BusinessProcessDefinition definition)
    {
        return new Dictionary<string, object>
        {
            { "steps", definition.Steps.Select(s => new Dictionary<string, object>
                {
                    { "id", s.Id },
                    { "name", s.Name },
                    { "type", s.Type },
                    { "parameters", s.Parameters }
                }).ToList()
            },
            { "metadata", new Dictionary<string, object>
                {
                    { "created_from_template", definition.TemplateId ?? "custom" },
                    { "automation_level", definition.AutomationLevel },
                    { "sla_hours", definition.SLAHours }
                }
            }
        };
    }

    private bool EvaluateTriggerConditions(Dictionary<string, object> triggerConfig, Dictionary<string, object> triggerData)
    {
        // Simple condition evaluation - in real implementation, use a proper rules engine
        if (!triggerConfig.TryGetValue("conditions", out var conditionsObj))
            return true; // No conditions means always trigger

        // For now, just return true - implement proper condition evaluation based on your needs
        return true;
    }

    private double CalculateSuccessRate(IEnumerable<WorkflowExecution> executions)
    {
        var completedExecutions = executions.Where(e => e.IsCompleted()).ToList();
        if (!completedExecutions.Any()) return 0;

        var successful = completedExecutions.Count(e => e.Status == "Completed");
        return (double)successful / completedExecutions.Count * 100;
    }

    private TimeSpan CalculateAverageExecutionTime(IEnumerable<WorkflowExecution> executions)
    {
        var completedExecutions = executions.Where(e => e.IsCompleted()).ToList();
        if (!completedExecutions.Any()) return TimeSpan.Zero;

        var totalTicks = completedExecutions.Sum(e => e.GetExecutionDuration().Ticks);
        return new TimeSpan(totalTicks / completedExecutions.Count);
    }

    private Dictionary<string, int> GetProcessBreakdown(IEnumerable<WorkflowExecution> executions)
    {
        return executions
            .GroupBy(e => e.Workflow?.Category ?? "Unknown")
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private Dictionary<DateTime, int> GetDailyExecutionCounts(IEnumerable<WorkflowExecution> executions, DateTime startDate, DateTime endDate)
    {
        var dailyCounts = new Dictionary<DateTime, int>();
        
        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var count = executions.Count(e => e.StartTime.Date == date);
            dailyCounts[date] = count;
        }

        return dailyCounts;
    }

    private List<ProcessTemplate> GetBuiltInProcessTemplates(string category)
    {
        var templates = new List<ProcessTemplate>();

        switch (category.ToLower())
        {
            case "orderprocessing":
                templates.AddRange(GetOrderProcessingTemplates());
                break;
            case "tripmanagement":
                templates.AddRange(GetTripManagementTemplates());
                break;
            case "documentverification":
                templates.AddRange(GetDocumentVerificationTemplates());
                break;
            case "useronboarding":
                templates.AddRange(GetUserOnboardingTemplates());
                break;
            default:
                templates.AddRange(GetGeneralTemplates());
                break;
        }

        return templates;
    }

    private List<ProcessTemplate> GetOrderProcessingTemplates()
    {
        return new List<ProcessTemplate>
        {
            new ProcessTemplate
            {
                Id = "order-rfq-processing",
                Name = "RFQ Processing Workflow",
                Description = "Automated workflow for processing RFQ requests",
                Category = "OrderProcessing",
                Steps = new List<ProcessStepTemplate>
                {
                    new ProcessStepTemplate { Name = "Validate RFQ", Type = "Automated", Description = "Validate RFQ data and requirements" },
                    new ProcessStepTemplate { Name = "Find Carriers", Type = "Automated", Description = "Find suitable carriers for the RFQ" },
                    new ProcessStepTemplate { Name = "Send Notifications", Type = "Notification", Description = "Notify carriers about new RFQ" },
                    new ProcessStepTemplate { Name = "Collect Bids", Type = "Manual", Description = "Wait for carrier bids" },
                    new ProcessStepTemplate { Name = "Evaluate Bids", Type = "Approval", Description = "Evaluate and approve best bid" }
                }
            }
        };
    }

    private List<ProcessTemplate> GetTripManagementTemplates()
    {
        return new List<ProcessTemplate>
        {
            new ProcessTemplate
            {
                Id = "trip-completion-workflow",
                Name = "Trip Completion Workflow",
                Description = "Automated workflow for trip completion and POD processing",
                Category = "TripManagement",
                Steps = new List<ProcessStepTemplate>
                {
                    new ProcessStepTemplate { Name = "Verify POD", Type = "Automated", Description = "Verify proof of delivery documents" },
                    new ProcessStepTemplate { Name = "Update Trip Status", Type = "Automated", Description = "Update trip status to completed" },
                    new ProcessStepTemplate { Name = "Process Payment", Type = "Automated", Description = "Process payment to carrier" },
                    new ProcessStepTemplate { Name = "Send Completion Notification", Type = "Notification", Description = "Notify all parties of trip completion" }
                }
            }
        };
    }

    private List<ProcessTemplate> GetDocumentVerificationTemplates()
    {
        return new List<ProcessTemplate>
        {
            new ProcessTemplate
            {
                Id = "document-verification-workflow",
                Name = "Document Verification Workflow",
                Description = "Automated workflow for verifying uploaded documents",
                Category = "DocumentVerification",
                Steps = new List<ProcessStepTemplate>
                {
                    new ProcessStepTemplate { Name = "Scan Document", Type = "Automated", Description = "Scan document for quality and readability" },
                    new ProcessStepTemplate { Name = "Extract Data", Type = "Automated", Description = "Extract key data from document" },
                    new ProcessStepTemplate { Name = "Verify Against Database", Type = "Automated", Description = "Verify document data against external databases" },
                    new ProcessStepTemplate { Name = "Manual Review", Type = "Manual", Description = "Manual review if automated verification fails" },
                    new ProcessStepTemplate { Name = "Approve/Reject", Type = "Approval", Description = "Final approval or rejection of document" }
                }
            }
        };
    }

    private List<ProcessTemplate> GetUserOnboardingTemplates()
    {
        return new List<ProcessTemplate>
        {
            new ProcessTemplate
            {
                Id = "driver-onboarding-workflow",
                Name = "Driver Onboarding Workflow",
                Description = "Complete workflow for onboarding new drivers",
                Category = "UserOnboarding",
                Steps = new List<ProcessStepTemplate>
                {
                    new ProcessStepTemplate { Name = "Collect Documents", Type = "Manual", Description = "Collect required documents from driver" },
                    new ProcessStepTemplate { Name = "Verify Identity", Type = "Automated", Description = "Verify driver identity using Aadhar/PAN" },
                    new ProcessStepTemplate { Name = "Background Check", Type = "Automated", Description = "Perform background verification" },
                    new ProcessStepTemplate { Name = "Training Assignment", Type = "Automated", Description = "Assign mandatory training modules" },
                    new ProcessStepTemplate { Name = "Final Approval", Type = "Approval", Description = "Final approval for driver activation" }
                }
            }
        };
    }

    private List<ProcessTemplate> GetGeneralTemplates()
    {
        return new List<ProcessTemplate>
        {
            new ProcessTemplate
            {
                Id = "simple-approval-workflow",
                Name = "Simple Approval Workflow",
                Description = "Basic approval workflow template",
                Category = "General",
                Steps = new List<ProcessStepTemplate>
                {
                    new ProcessStepTemplate { Name = "Submit Request", Type = "Manual", Description = "Submit request for approval" },
                    new ProcessStepTemplate { Name = "Review Request", Type = "Approval", Description = "Review and approve/reject request" },
                    new ProcessStepTemplate { Name = "Send Notification", Type = "Notification", Description = "Notify requester of decision" }
                }
            }
        };
    }
}

// Data Models
public class BusinessProcessDefinition
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0";
    public string TriggerType { get; set; } = "Manual";
    public Dictionary<string, object> TriggerConfiguration { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<ProcessStep> Steps { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public string? TemplateId { get; set; }
    public string AutomationLevel { get; set; } = "Semi-Automated";
    public int SLAHours { get; set; } = 24;
}

public class ProcessStep
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class AutomationResult
{
    public bool IsSuccess { get; set; }
    public Guid ProcessId { get; set; }
    public Guid ExecutionId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class BusinessProcess
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string TriggerType { get; set; } = string.Empty;
    public DateTime? LastExecuted { get; set; }
    public int ExecutionCount { get; set; }
    public List<ProcessExecution> RecentExecutions { get; set; } = new();
}

public class ProcessExecution
{
    public Guid Id { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public Guid TriggeredBy { get; set; }
}

public class BusinessProcessStatus
{
    public Guid ProcessId { get; set; }
    public string ProcessName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int RunningExecutions { get; set; }
    public int TotalExecutions { get; set; }
    public DateTime? LastExecuted { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
}

public class ProcessTemplate
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<ProcessStepTemplate> Steps { get; set; } = new();
}

public class ProcessStepTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class AutomationMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan AverageExecutionTime { get; set; }
    public Dictionary<string, int> ProcessBreakdown { get; set; } = new();
    public Dictionary<DateTime, int> DailyExecutionCounts { get; set; } = new();
}

public class ProcessValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
}
