using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.Entities
{
    public class TaxExemption : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public string ExemptionType { get; private set; }
        public string ExemptionNumber { get; private set; }
        public string IssuingAuthority { get; private set; }
        public DateTime ValidFrom { get; private set; }
        public DateTime ValidTo { get; private set; }
        public List<TaxType> ExemptTaxTypes { get; private set; }
        public List<string> ApplicableRegions { get; private set; }
        public bool IsActive { get; private set; }
        public string? DocumentPath { get; private set; }
        public DateTime? VerifiedAt { get; private set; }
        public Guid? VerifiedByUserId { get; private set; }
        public string? VerificationNotes { get; private set; }

        private TaxExemption()
        {
            ExemptionType = string.Empty;
            ExemptionNumber = string.Empty;
            IssuingAuthority = string.Empty;
            ExemptTaxTypes = new List<TaxType>();
            ApplicableRegions = new List<string>();
        }

        public TaxExemption(Guid userId, string exemptionType, string exemptionNumber, 
            string issuingAuthority, DateTime validFrom, DateTime validTo,
            List<TaxType> exemptTaxTypes, List<string> applicableRegions, string? documentPath = null)
        {
            if (userId == Guid.Empty)
                throw new SubscriptionDomainException("User ID cannot be empty");

            if (string.IsNullOrWhiteSpace(exemptionType))
                throw new SubscriptionDomainException("Exemption type cannot be empty");

            if (string.IsNullOrWhiteSpace(exemptionNumber))
                throw new SubscriptionDomainException("Exemption number cannot be empty");

            if (string.IsNullOrWhiteSpace(issuingAuthority))
                throw new SubscriptionDomainException("Issuing authority cannot be empty");

            if (validFrom >= validTo)
                throw new SubscriptionDomainException("Valid from date must be before valid to date");

            if (exemptTaxTypes == null || !exemptTaxTypes.Any())
                throw new SubscriptionDomainException("At least one exempt tax type must be specified");

            if (applicableRegions == null || !applicableRegions.Any())
                throw new SubscriptionDomainException("At least one applicable region must be specified");

            UserId = userId;
            ExemptionType = exemptionType;
            ExemptionNumber = exemptionNumber;
            IssuingAuthority = issuingAuthority;
            ValidFrom = validFrom;
            ValidTo = validTo;
            ExemptTaxTypes = exemptTaxTypes.Distinct().ToList();
            ApplicableRegions = applicableRegions.Select(r => r.ToUpperInvariant()).Distinct().ToList();
            IsActive = true;
            DocumentPath = documentPath;

            AddDomainEvent(new TaxExemptionCreatedEvent(Id, userId, exemptionType, exemptionNumber));
        }

        public void UpdateDetails(string exemptionType, string exemptionNumber, string issuingAuthority,
            DateTime validFrom, DateTime validTo, List<TaxType> exemptTaxTypes, List<string> applicableRegions)
        {
            if (string.IsNullOrWhiteSpace(exemptionType))
                throw new SubscriptionDomainException("Exemption type cannot be empty");

            if (string.IsNullOrWhiteSpace(exemptionNumber))
                throw new SubscriptionDomainException("Exemption number cannot be empty");

            if (string.IsNullOrWhiteSpace(issuingAuthority))
                throw new SubscriptionDomainException("Issuing authority cannot be empty");

            if (validFrom >= validTo)
                throw new SubscriptionDomainException("Valid from date must be before valid to date");

            if (exemptTaxTypes == null || !exemptTaxTypes.Any())
                throw new SubscriptionDomainException("At least one exempt tax type must be specified");

            if (applicableRegions == null || !applicableRegions.Any())
                throw new SubscriptionDomainException("At least one applicable region must be specified");

            ExemptionType = exemptionType;
            ExemptionNumber = exemptionNumber;
            IssuingAuthority = issuingAuthority;
            ValidFrom = validFrom;
            ValidTo = validTo;
            ExemptTaxTypes = exemptTaxTypes.Distinct().ToList();
            ApplicableRegions = applicableRegions.Select(r => r.ToUpperInvariant()).Distinct().ToList();
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxExemptionUpdatedEvent(Id, UserId, exemptionType, exemptionNumber));
        }

        public void Verify(Guid verifiedByUserId, string? verificationNotes = null)
        {
            if (verifiedByUserId == Guid.Empty)
                throw new SubscriptionDomainException("Verified by user ID cannot be empty");

            VerifiedAt = DateTime.UtcNow;
            VerifiedByUserId = verifiedByUserId;
            VerificationNotes = verificationNotes;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxExemptionVerifiedEvent(Id, UserId, verifiedByUserId));
        }

        public void Activate()
        {
            if (IsActive)
                return;

            IsActive = true;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxExemptionActivatedEvent(Id, UserId));
        }

        public void Deactivate()
        {
            if (!IsActive)
                return;

            IsActive = false;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxExemptionDeactivatedEvent(Id, UserId));
        }

        public void UpdateDocumentPath(string documentPath)
        {
            if (string.IsNullOrWhiteSpace(documentPath))
                throw new SubscriptionDomainException("Document path cannot be empty");

            DocumentPath = documentPath;
            UpdatedAt = DateTime.UtcNow;
        }

        public bool IsValidOn(DateTime date)
        {
            return IsActive && date >= ValidFrom && date <= ValidTo;
        }

        public bool IsExemptFromTax(TaxType taxType, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            
            return IsValidOn(checkDate) &&
                   ExemptTaxTypes.Contains(taxType) &&
                   ApplicableRegions.Contains(region.ToUpperInvariant());
        }

        public bool RequiresVerification()
        {
            return !VerifiedAt.HasValue;
        }

        public bool IsExpiringSoon(int daysThreshold = 30)
        {
            return IsActive && (ValidTo - DateTime.UtcNow).Days <= daysThreshold;
        }
    }
}
