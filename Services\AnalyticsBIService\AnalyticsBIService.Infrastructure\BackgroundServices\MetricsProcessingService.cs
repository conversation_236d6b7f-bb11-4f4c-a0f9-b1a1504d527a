using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;

namespace AnalyticsBIService.Infrastructure.BackgroundServices;

/// <summary>
/// Background service for processing metrics and analytics events
/// </summary>
public class MetricsProcessingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MetricsProcessingService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromMinutes(5);

    public MetricsProcessingService(
        IServiceProvider serviceProvider,
        ILogger<MetricsProcessingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Metrics Processing Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessMetricsAsync(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Metrics Processing Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Metrics Processing Service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Metrics Processing Service stopped");
    }

    private async Task ProcessMetricsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var analyticsEventRepository = scope.ServiceProvider.GetRequiredService<IAnalyticsEventRepository>();
        var metricRepository = scope.ServiceProvider.GetRequiredService<IMetricRepository>();
        var dataAggregationService = scope.ServiceProvider.GetRequiredService<IDataAggregationService>();
        var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

        try
        {
            _logger.LogDebug("Starting metrics processing cycle");

            // Process unprocessed analytics events
            await ProcessUnprocessedEventsAsync(analyticsEventRepository, metricRepository, cancellationToken);

            // Calculate aggregated metrics
            await CalculateAggregatedMetricsAsync(dataAggregationService, cacheService, cancellationToken);

            // Update metric trends
            await UpdateMetricTrendsAsync(metricRepository, dataAggregationService, cacheService, cancellationToken);

            // Clean up old processed events
            await CleanupOldEventsAsync(analyticsEventRepository, cancellationToken);

            _logger.LogDebug("Metrics processing cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during metrics processing");
            throw;
        }
    }

    private async Task ProcessUnprocessedEventsAsync(
        IAnalyticsEventRepository analyticsEventRepository,
        IMetricRepository metricRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var unprocessedEvents = await analyticsEventRepository.GetUnprocessedEventsAsync(100, cancellationToken);

            if (!unprocessedEvents.Any())
            {
                _logger.LogDebug("No unprocessed events found");
                return;
            }

            _logger.LogInformation("Processing {EventCount} unprocessed events", unprocessedEvents.Count());

            foreach (var analyticsEvent in unprocessedEvents)
            {
                try
                {
                    await ProcessSingleEventAsync(analyticsEvent, metricRepository, cancellationToken);

                    // Mark event as processed
                    analyticsEvent.MarkAsProcessed();
                    await analyticsEventRepository.UpdateAsync(analyticsEvent, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing event {EventId}", analyticsEvent.Id);

                    // Mark event as failed
                    analyticsEvent.MarkProcessingFailed();
                    await analyticsEventRepository.UpdateAsync(analyticsEvent, cancellationToken);
                }
            }

            _logger.LogInformation("Completed processing {EventCount} events", unprocessedEvents.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing unprocessed events");
            throw;
        }
    }

    private async Task ProcessSingleEventAsync(
        AnalyticsEvent analyticsEvent,
        IMetricRepository metricRepository,
        CancellationToken cancellationToken)
    {
        // Create metrics based on the analytics event
        var metrics = GenerateMetricsFromEvent(analyticsEvent);

        foreach (var metric in metrics)
        {
            await metricRepository.AddAsync(metric, cancellationToken);
        }

        _logger.LogDebug("Generated {MetricCount} metrics from event {EventId}",
            metrics.Count, analyticsEvent.Id);
    }

    private List<Metric> GenerateMetricsFromEvent(AnalyticsEvent analyticsEvent)
    {
        var metrics = new List<Metric>();

        // Generate different types of metrics based on event name
        switch (analyticsEvent.EventName?.ToLowerInvariant() ?? string.Empty)
        {
            case "order_created":
                metrics.Add(CreateMetric(analyticsEvent, MetricType.OrderCount, MetricCategory.Operational, 1m));
                break;

            case "trip_completed":
                metrics.Add(CreateMetric(analyticsEvent, MetricType.TripCount, MetricCategory.Operational, 1m));
                if (analyticsEvent.Properties.ContainsKey("distance"))
                {
                    if (decimal.TryParse(analyticsEvent.Properties["distance"]?.ToString(), out var distance))
                    {
                        metrics.Add(CreateMetric(analyticsEvent, MetricType.Distance, MetricCategory.Operational, distance));
                    }
                }
                break;

            case "payment_processed":
                if (analyticsEvent.Properties.ContainsKey("amount"))
                {
                    if (decimal.TryParse(analyticsEvent.Properties["amount"]?.ToString(), out var amount))
                    {
                        metrics.Add(CreateMetric(analyticsEvent, MetricType.Revenue, MetricCategory.Financial, amount));
                    }
                }
                break;

            case "user_login":
                metrics.Add(CreateMetric(analyticsEvent, MetricType.UserActivity, MetricCategory.Customer, 1m));
                break;

            default:
                // Generic event count metric
                metrics.Add(CreateMetric(analyticsEvent, MetricType.EventCount, MetricCategory.Operational, 1m));
                break;
        }

        return metrics;
    }

    private Metric CreateMetric(
        AnalyticsEvent analyticsEvent,
        MetricType metricType,
        MetricCategory category,
        decimal value)
    {
        var metricValue = new MetricValue(value, metricType, "numeric", DateTime.UtcNow);
        var timePeriod = new TimePeriodValue(DateTime.UtcNow.Date, DateTime.UtcNow.Date.AddDays(1), TimePeriod.Daily);

        return new Metric(
            name: $"{analyticsEvent.EventType}_{metricType}",
            description: $"Metric for {analyticsEvent.EventType}",
            type: metricType,
            category: (KPICategory)category,
            value: metricValue,
            period: timePeriod,
            dataSource: DataSourceType.Application,
            userId: analyticsEvent.EntityId,
            userType: null,
            target: null,
            tags: new Dictionary<string, string>
            {
                ["source"] = "MetricsProcessingService",
                ["eventId"] = analyticsEvent.Id.ToString(),
                ["entityType"] = analyticsEvent.EntityType ?? "Unknown"
            }
        );
    }

    private async Task CalculateAggregatedMetricsAsync(
        IDataAggregationService dataAggregationService,
        ICacheService cacheService,
        CancellationToken cancellationToken)
    {
        try
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddHours(-1); // Last hour

            // Calculate aggregations for different categories
            var categories = Enum.GetValues<MetricCategory>();

            foreach (var category in categories)
            {
                try
                {
                    var aggregations = await dataAggregationService.AggregateMetricsAsync(
                        startDate, endDate, category, AnalyticsBIService.Application.Interfaces.AggregationType.Sum, cancellationToken);

                    var cacheKey = $"hourly_aggregation_{category}_{endDate:yyyyMMddHH}";
                    await cacheService.SetAsync(cacheKey, aggregations, TimeSpan.FromHours(2), cancellationToken);

                    _logger.LogDebug("Calculated aggregations for category {Category}", category);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error calculating aggregations for category {Category}", category);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating aggregated metrics");
            throw;
        }
    }

    private async Task UpdateMetricTrendsAsync(
        IMetricRepository metricRepository,
        IDataAggregationService dataAggregationService,
        ICacheService cacheService,
        CancellationToken cancellationToken)
    {
        try
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-7); // Last week

            var metricTypes = Enum.GetValues<MetricType>();

            foreach (var metricType in metricTypes)
            {
                try
                {
                    var trendData = await dataAggregationService.CalculateTrendDataAsync(
                        MetricCategory.System, metricType, startDate, endDate,
                        TimeSpan.FromHours(1), cancellationToken);

                    // Store trend data in cache for quick access
                    var cacheKey = $"trend_data_{metricType}_{endDate:yyyyMMdd}";
                    await cacheService.SetAsync(cacheKey, trendData, TimeSpan.FromHours(6), cancellationToken);

                    _logger.LogDebug("Updated trend data for metric type {MetricType}", metricType);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error updating trend data for metric type {MetricType}", metricType);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating metric trends");
            throw;
        }
    }

    private async Task CleanupOldEventsAsync(
        IAnalyticsEventRepository analyticsEventRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-30); // Keep events for 30 days
            await analyticsEventRepository.DeleteOldProcessedEventsAsync(cutoffDate, cancellationToken);
            _logger.LogInformation("Completed cleanup of old processed events older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old events");
            throw;
        }
    }
}
