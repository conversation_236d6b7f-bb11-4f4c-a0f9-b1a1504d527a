using MediatR;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Commands;

// Health check management commands
public class CreateHealthCheckCommand : IRequest<HealthCheckDto>
{
    public string Name { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public TimeSpan Interval { get; set; }
    public TimeSpan Timeout { get; set; }
    public int MaxRetries { get; set; } = 3;
    public Dictionary<string, string>? Headers { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class UpdateHealthCheckCommand : IRequest<HealthCheckDto>
{
    public Guid HealthCheckId { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Endpoint { get; set; }
    public TimeSpan? Interval { get; set; }
    public TimeSpan? Timeout { get; set; }
    public int? MaxRetries { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class EnableHealthCheckCommand : IRequest<HealthCheckDto>
{
    public Guid HealthCheckId { get; set; }
}

public class DisableHealthCheckCommand : IRequest<HealthCheckDto>
{
    public Guid HealthCheckId { get; set; }
}

public class ExecuteHealthCheckCommand : IRequest<HealthCheckDto>
{
    public Guid HealthCheckId { get; set; }
    public bool ForceExecution { get; set; } = false;
}

public class ExecuteAllHealthChecksCommand : IRequest<IEnumerable<HealthCheckDto>>
{
    public string? ServiceName { get; set; }
}

public class RecordHealthCheckResultCommand : IRequest<HealthCheckDto>
{
    public Guid HealthCheckId { get; set; }
    public HealthStatus Status { get; set; }
    public TimeSpan Duration { get; set; }
    public string? ErrorMessage { get; set; }
}

public class DeleteHealthCheckCommand : IRequest<Unit>
{
    public Guid HealthCheckId { get; set; }
}

// Incident management commands
public class CreateIncidentCommand : IRequest<IncidentDto>
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IncidentSeverity Severity { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public Guid CreatedByUserId { get; set; }
    public string? Component { get; set; }
    public int ImpactLevel { get; set; } = 3;
    public int UrgencyLevel { get; set; } = 3;
    public string? ImpactDescription { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class UpdateIncidentStatusCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public IncidentStatus NewStatus { get; set; }
    public string UpdateMessage { get; set; } = string.Empty;
    public Guid UpdatedByUserId { get; set; }
}

public class AssignIncidentCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public Guid AssignedToUserId { get; set; }
    public string AssignedToUserName { get; set; } = string.Empty;
    public Guid AssignedByUserId { get; set; }
}

public class UpdateIncidentSeverityCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public IncidentSeverity NewSeverity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public Guid UpdatedByUserId { get; set; }
}

public class UpdateIncidentImpactCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public int ImpactLevel { get; set; }
    public int UrgencyLevel { get; set; }
    public string? ImpactDescription { get; set; }
    public Guid UpdatedByUserId { get; set; }
}

public class ResolveIncidentCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public string ResolutionSummary { get; set; } = string.Empty;
    public string? RootCause { get; set; }
    public string? PreventiveMeasures { get; set; }
    public Guid ResolvedByUserId { get; set; }
}

public class AddIncidentCommentCommand : IRequest<IncidentCommentDto>
{
    public Guid IncidentId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public bool IsInternal { get; set; } = false;
}

public class LinkAlertToIncidentCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public Guid AlertId { get; set; }
    public string AlertTitle { get; set; } = string.Empty;
}

public class UnlinkAlertFromIncidentCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public Guid AlertId { get; set; }
}

public class CreateIncidentFromAlertCommand : IRequest<IncidentDto>
{
    public Guid AlertId { get; set; }
    public Guid CreatedByUserId { get; set; }
    public string? AdditionalDescription { get; set; }
    public IncidentSeverity? OverrideSeverity { get; set; }
}

public class AddIncidentCustomFieldCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public object FieldValue { get; set; } = null!;
    public Guid UpdatedByUserId { get; set; }
}

public class RemoveIncidentCustomFieldCommand : IRequest<IncidentDto>
{
    public Guid IncidentId { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public Guid UpdatedByUserId { get; set; }
}

// Notification commands
public class SendNotificationCommand : IRequest<Unit>
{
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
}



public class SendIncidentNotificationCommand : IRequest<Unit>
{
    public Guid IncidentId { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public string? CustomMessage { get; set; }
}

public class SendBulkNotificationsCommand : IRequest<Unit>
{
    public NotificationChannel Channel { get; set; }
    public IEnumerable<string> Recipients { get; set; } = new List<string>();
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
}

// System monitoring commands
public class RecordSystemMetricsCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class RecordDatabaseMetricsCommand : IRequest<Unit>
{
    public string ServiceName { get; set; } = string.Empty;
    public int ConnectionCount { get; set; }
    public double QueryTime { get; set; }
    public int ActiveTransactions { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class EvaluateThresholdsCommand : IRequest<IEnumerable<AlertDto>>
{
    public string? ServiceName { get; set; }
    public string? MetricName { get; set; }
}

public class CleanupOldDataCommand : IRequest<Unit>
{
    public TimeSpan? MetricRetentionPeriod { get; set; }
    public TimeSpan? LogRetentionPeriod { get; set; }
    public TimeSpan? AlertRetentionPeriod { get; set; }
}
