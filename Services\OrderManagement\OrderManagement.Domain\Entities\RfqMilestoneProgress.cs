using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing progress on a specific milestone step for an RFQ
/// </summary>
public class RfqMilestoneProgress : BaseEntity
{
    public Guid RfqMilestoneAssignmentId { get; private set; }
    public Guid MilestoneStepId { get; private set; }
    public MilestoneProgressStatus Status { get; private set; }
    public decimal PayoutPercentage { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Guid? CompletedBy { get; private set; }
    public string? CompletedByName { get; private set; }
    public string? Notes { get; private set; }
    public string? DocumentationUrls { get; private set; }
    public bool RequiresApproval { get; private set; }
    public DateTime? ApprovedAt { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public string? ApprovedByName { get; private set; }
    public string? ApprovalNotes { get; private set; }
    public DateTime? RejectedAt { get; private set; }
    public Guid? RejectedBy { get; private set; }
    public string? RejectedByName { get; private set; }
    public string? RejectionReason { get; private set; }

    // Navigation properties
    public RfqMilestoneAssignment RfqMilestoneAssignment { get; private set; } = null!;
    public MilestoneStep MilestoneStep { get; private set; } = null!;

    private RfqMilestoneProgress() { } // EF Constructor

    public RfqMilestoneProgress(
        Guid rfqMilestoneAssignmentId,
        Guid milestoneStepId,
        decimal payoutPercentage,
        bool requiresApproval = false)
    {
        RfqMilestoneAssignmentId = rfqMilestoneAssignmentId;
        MilestoneStepId = milestoneStepId;
        PayoutPercentage = payoutPercentage;
        Status = MilestoneProgressStatus.NotStarted;
        RequiresApproval = requiresApproval;
    }

    public void Start(string? notes = null)
    {
        if (Status != MilestoneProgressStatus.NotStarted)
            throw new InvalidOperationException($"Cannot start milestone step in status {Status}");

        Status = MilestoneProgressStatus.InProgress;
        StartedAt = DateTime.UtcNow;
        Notes = notes;
    }

    public void Complete(
        Guid completedBy,
        string? completedByName = null,
        string? notes = null,
        string? documentationUrls = null)
    {
        if (Status != MilestoneProgressStatus.InProgress)
            throw new InvalidOperationException($"Cannot complete milestone step in status {Status}");

        Status = RequiresApproval ? MilestoneProgressStatus.PendingApproval : MilestoneProgressStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        CompletedBy = completedBy;
        CompletedByName = completedByName;
        Notes = notes;
        DocumentationUrls = documentationUrls;
    }

    public void Approve(
        Guid approvedBy,
        string? approvedByName = null,
        string? approvalNotes = null)
    {
        if (Status != MilestoneProgressStatus.PendingApproval)
            throw new InvalidOperationException($"Cannot approve milestone step in status {Status}");

        Status = MilestoneProgressStatus.Completed;
        ApprovedAt = DateTime.UtcNow;
        ApprovedBy = approvedBy;
        ApprovedByName = approvedByName;
        ApprovalNotes = approvalNotes;
    }

    public void Reject(
        Guid rejectedBy,
        string rejectionReason,
        string? rejectedByName = null)
    {
        if (Status != MilestoneProgressStatus.PendingApproval)
            throw new InvalidOperationException($"Cannot reject milestone step in status {Status}");

        Status = MilestoneProgressStatus.InProgress; // Back to in progress for rework
        RejectedAt = DateTime.UtcNow;
        RejectedBy = rejectedBy;
        RejectedByName = rejectedByName;
        RejectionReason = rejectionReason;
    }

    public void Reset()
    {
        Status = MilestoneProgressStatus.NotStarted;
        StartedAt = null;
        CompletedAt = null;
        CompletedBy = null;
        CompletedByName = null;
        Notes = null;
        DocumentationUrls = null;
        ApprovedAt = null;
        ApprovedBy = null;
        ApprovedByName = null;
        ApprovalNotes = null;
        RejectedAt = null;
        RejectedBy = null;
        RejectionReason = null;
    }

    public bool IsCompleted => Status == MilestoneProgressStatus.Completed;
    public bool IsPendingApproval => Status == MilestoneProgressStatus.PendingApproval;
    public bool IsInProgress => Status == MilestoneProgressStatus.InProgress;
    public bool IsNotStarted => Status == MilestoneProgressStatus.NotStarted;

    public TimeSpan? GetDuration()
    {
        if (StartedAt == null) return null;
        var endTime = CompletedAt ?? DateTime.UtcNow;
        return endTime - StartedAt.Value;
    }
}
