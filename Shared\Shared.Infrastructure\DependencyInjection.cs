using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Http;
using Shared.Infrastructure.Monitoring;
using StackExchange.Redis;

namespace Shared.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddSharedInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Add HTTP clients
            services.AddHttpClients();

            // Add Redis caching
            services.AddRedisCache(configuration);

            // Add metrics and monitoring
            services.AddMetrics();
            services.AddScoped<IPerformanceMetrics, PerformanceMetrics>();

            // Add caching service
            services.AddScoped<ICachingService, CachingService>();

            return services;
        }

        private static IServiceCollection AddRedisCache(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";

            services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                return ConnectionMultiplexer.Connect(connectionString);
            });

            services.AddScoped<ICacheService, RedisCacheService>();

            return services;
        }
    }
}
