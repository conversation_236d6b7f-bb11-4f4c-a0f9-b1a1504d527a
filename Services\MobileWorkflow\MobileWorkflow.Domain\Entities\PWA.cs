using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class PWAInstallation : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string DeviceId { get; private set; }
    public string Platform { get; private set; } // Web, Android, iOS
    public string UserAgent { get; private set; }
    public string AppVersion { get; private set; }
    public DateTime InstalledAt { get; private set; }
    public DateTime? LastActiveAt { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, object> InstallationData { get; private set; }
    public Dictionary<string, object> DeviceCapabilities { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<PWASession> Sessions { get; private set; } = new List<PWASession>();
    public virtual ICollection<ServiceWorkerEvent> ServiceWorkerEvents { get; private set; } = new List<ServiceWorkerEvent>();

    private PWAInstallation() { } // EF Core

    public PWAInstallation(
        Guid userId,
        string deviceId,
        string platform,
        string userAgent,
        string appVersion,
        Dictionary<string, object> installationData)
    {
        UserId = userId;
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        UserAgent = userAgent ?? throw new ArgumentNullException(nameof(userAgent));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        InstallationData = installationData ?? throw new ArgumentNullException(nameof(installationData));

        InstalledAt = DateTime.UtcNow;
        LastActiveAt = DateTime.UtcNow;
        IsActive = true;
        DeviceCapabilities = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new PWAInstallationCreatedEvent(Id, UserId, DeviceId, Platform, AppVersion));
    }

    public void UpdateLastActive()
    {
        LastActiveAt = DateTime.UtcNow;
    }

    public void UpdateDeviceCapabilities(Dictionary<string, object> capabilities)
    {
        DeviceCapabilities = capabilities ?? throw new ArgumentNullException(nameof(capabilities));

        AddDomainEvent(new PWAInstallationCapabilitiesUpdatedEvent(Id, UserId, DeviceId));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new PWAInstallationDeactivatedEvent(Id, UserId, DeviceId));
    }

    public void Reactivate()
    {
        IsActive = true;
        LastActiveAt = DateTime.UtcNow;

        AddDomainEvent(new PWAInstallationReactivatedEvent(Id, UserId, DeviceId));
    }

    public bool SupportsFeature(string feature)
    {
        return DeviceCapabilities.TryGetValue(feature, out var value) && value is bool supported && supported;
    }

    public TimeSpan? GetTimeSinceLastActive()
    {
        return LastActiveAt.HasValue ? DateTime.UtcNow - LastActiveAt.Value : null;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetInstallationData<T>(string key, T? defaultValue = default)
    {
        if (InstallationData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetDeviceCapability<T>(string key, T? defaultValue = default)
    {
        if (DeviceCapabilities.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class PWASession : AggregateRoot
{
    public Guid PWAInstallationId { get; private set; }
    public Guid UserId { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? EndedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public string SessionType { get; private set; } // Online, Offline, Mixed
    public bool IsActive { get; private set; }
    public Dictionary<string, object> SessionData { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual PWAInstallation PWAInstallation { get; private set; } = null!;

    private PWASession() { } // EF Core

    public PWASession(
        Guid pwaInstallationId,
        Guid userId,
        string sessionType)
    {
        PWAInstallationId = pwaInstallationId;
        UserId = userId;
        SessionType = sessionType ?? throw new ArgumentNullException(nameof(sessionType));

        StartedAt = DateTime.UtcNow;
        IsActive = true;
        SessionData = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new PWASessionStartedEvent(Id, PWAInstallationId, UserId, SessionType));
    }

    public void End()
    {
        if (!IsActive)
            throw new InvalidOperationException("Session is already ended");

        EndedAt = DateTime.UtcNow;
        Duration = EndedAt - StartedAt;
        IsActive = false;

        AddDomainEvent(new PWASessionEndedEvent(Id, PWAInstallationId, UserId, Duration?.TotalMinutes));
    }

    public void AddSessionData(string key, object value)
    {
        SessionData[key] = value;
    }

    public void AddPerformanceMetric(string key, object value)
    {
        PerformanceMetrics[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetSessionData<T>(string key, T? defaultValue = default)
    {
        if (SessionData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string key, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class ServiceWorkerEvent : AggregateRoot
{
    public Guid PWAInstallationId { get; private set; }
    public string EventType { get; private set; } // Install, Activate, Fetch, Push, Sync, Message
    public DateTime EventTime { get; private set; }
    public string? EventData { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> EventContext { get; private set; }
    public Dictionary<string, object> PerformanceData { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual PWAInstallation PWAInstallation { get; private set; } = null!;

    private ServiceWorkerEvent() { } // EF Core

    public ServiceWorkerEvent(
        Guid pwaInstallationId,
        string eventType,
        Dictionary<string, object> eventContext,
        bool isSuccessful = true)
    {
        PWAInstallationId = pwaInstallationId;
        EventType = eventType ?? throw new ArgumentNullException(nameof(eventType));
        EventContext = eventContext ?? throw new ArgumentNullException(nameof(eventContext));
        IsSuccessful = isSuccessful;

        EventTime = DateTime.UtcNow;
        PerformanceData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new ServiceWorkerEventCreatedEvent(Id, PWAInstallationId, EventType, IsSuccessful));
    }

    public void SetEventData(string eventData)
    {
        EventData = eventData;
    }

    public void SetError(string errorMessage)
    {
        IsSuccessful = false;
        ErrorMessage = errorMessage;

        AddDomainEvent(new ServiceWorkerEventFailedEvent(Id, PWAInstallationId, EventType, errorMessage));
    }

    public void AddPerformanceData(string key, object value)
    {
        PerformanceData[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetEventContext<T>(string key, T? defaultValue = default)
    {
        if (EventContext.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceData<T>(string key, T? defaultValue = default)
    {
        if (PerformanceData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class PWAManifest : AggregateRoot
{
    public string Name { get; private set; }
    public string ShortName { get; private set; }
    public string Description { get; private set; }
    public string StartUrl { get; private set; }
    public string Display { get; private set; } // fullscreen, standalone, minimal-ui, browser
    public string Orientation { get; private set; } // any, natural, landscape, portrait
    public string ThemeColor { get; private set; }
    public string BackgroundColor { get; private set; }
    public string Scope { get; private set; }
    public List<PWAIcon> Icons { get; private set; }
    public Dictionary<string, object> ManifestData { get; private set; }
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }

    private PWAManifest() { } // EF Core

    public PWAManifest(
        string name,
        string shortName,
        string description,
        string startUrl,
        string display,
        string themeColor,
        string backgroundColor,
        string version,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        ShortName = shortName ?? throw new ArgumentNullException(nameof(shortName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        StartUrl = startUrl ?? throw new ArgumentNullException(nameof(startUrl));
        Display = display ?? throw new ArgumentNullException(nameof(display));
        ThemeColor = themeColor ?? throw new ArgumentNullException(nameof(themeColor));
        BackgroundColor = backgroundColor ?? throw new ArgumentNullException(nameof(backgroundColor));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        Orientation = "any";
        Scope = "/";
        Icons = new List<PWAIcon>();
        ManifestData = new Dictionary<string, object>();
        IsActive = true;
        CreatedAt = DateTime.UtcNow;

        AddDomainEvent(new PWAManifestCreatedEvent(Id, Name, Version, CreatedBy));
    }

    public void UpdateManifest(
        string? name = null,
        string? shortName = null,
        string? description = null,
        string? startUrl = null,
        string? display = null,
        string? themeColor = null,
        string? backgroundColor = null,
        string? updatedBy = null)
    {
        if (!string.IsNullOrEmpty(name)) Name = name;
        if (!string.IsNullOrEmpty(shortName)) ShortName = shortName;
        if (!string.IsNullOrEmpty(description)) Description = description;
        if (!string.IsNullOrEmpty(startUrl)) StartUrl = startUrl;
        if (!string.IsNullOrEmpty(display)) Display = display;
        if (!string.IsNullOrEmpty(themeColor)) ThemeColor = themeColor;
        if (!string.IsNullOrEmpty(backgroundColor)) BackgroundColor = backgroundColor;

        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new PWAManifestUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void AddIcon(PWAIcon icon)
    {
        Icons.Add(icon ?? throw new ArgumentNullException(nameof(icon)));
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemoveIcon(PWAIcon icon)
    {
        if (Icons.Remove(icon))
        {
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void SetScope(string scope)
    {
        Scope = scope ?? throw new ArgumentNullException(nameof(scope));
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetOrientation(string orientation)
    {
        Orientation = orientation ?? throw new ArgumentNullException(nameof(orientation));
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new PWAManifestActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new PWAManifestDeactivatedEvent(Id, Name));
    }

    public void AddManifestData(string key, object value)
    {
        ManifestData[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetManifestData<T>(string key, T? defaultValue = default)
    {
        if (ManifestData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class PWAIcon
{
    public string Src { get; set; } = string.Empty;
    public string Sizes { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Purpose { get; set; } = "any"; // any, maskable, monochrome
}

public class OfflineCache : AggregateRoot
{
    public string CacheName { get; private set; }
    public string CacheVersion { get; private set; }
    public List<string> CachedUrls { get; private set; }
    public long TotalSize { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? LastAccessedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public string CacheStrategy { get; private set; } // CacheFirst, NetworkFirst, StaleWhileRevalidate
    public Dictionary<string, object> CacheMetadata { get; private set; }
    public bool IsActive { get; private set; }

    private OfflineCache() { } // EF Core

    public OfflineCache(
        string cacheName,
        string cacheVersion,
        string cacheStrategy)
    {
        CacheName = cacheName ?? throw new ArgumentNullException(nameof(cacheName));
        CacheVersion = cacheVersion ?? throw new ArgumentNullException(nameof(cacheVersion));
        CacheStrategy = cacheStrategy ?? throw new ArgumentNullException(nameof(cacheStrategy));

        CachedUrls = new List<string>();
        TotalSize = 0;
        CreatedAt = DateTime.UtcNow;
        IsActive = true;
        CacheMetadata = new Dictionary<string, object>();

        AddDomainEvent(new OfflineCacheCreatedEvent(Id, CacheName, CacheVersion, CacheStrategy));
    }

    public void AddUrl(string url, long size)
    {
        if (!CachedUrls.Contains(url))
        {
            CachedUrls.Add(url);
            TotalSize += size;
            LastAccessedAt = DateTime.UtcNow;
        }
    }

    public void RemoveUrl(string url, long size)
    {
        if (CachedUrls.Remove(url))
        {
            TotalSize = Math.Max(0, TotalSize - size);
        }
    }

    public void UpdateLastAccessed()
    {
        LastAccessedAt = DateTime.UtcNow;
    }

    public void SetExpiration(DateTime expiresAt)
    {
        ExpiresAt = expiresAt;
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new OfflineCacheDeactivatedEvent(Id, CacheName, CacheVersion));
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public void AddMetadata(string key, object value)
    {
        CacheMetadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (CacheMetadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


