using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class CarrierDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string? BusinessLicenseNumber { get; set; }
    public string? TaxIdentificationNumber { get; set; }
    public CarrierStatus Status { get; set; }
    public OnboardingStatus OnboardingStatus { get; set; }
    public PerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public LocationDto? BusinessAddress { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Counts
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
    public int AvailableVehicleCount { get; set; }
    public int AvailableDriverCount { get; set; }
}

public class CarrierSummaryDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public CarrierStatus Status { get; set; }
    public OnboardingStatus OnboardingStatus { get; set; }
    public decimal Rating { get; set; }
    public int TotalTrips { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateCarrierDto
{
    public Guid UserId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string? BusinessLicenseNumber { get; set; }
    public string? TaxIdentificationNumber { get; set; }
    public LocationDto? BusinessAddress { get; set; }
    public string? Notes { get; set; }
}

public class UpdateCarrierDto
{
    public string CompanyName { get; set; } = string.Empty;
    public string ContactPersonName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string? BusinessLicenseNumber { get; set; }
    public string? TaxIdentificationNumber { get; set; }
    public LocationDto? BusinessAddress { get; set; }
    public string? Notes { get; set; }
}

public class UpdateCarrierStatusDto
{
    public CarrierStatus Status { get; set; }
    public string? Reason { get; set; }
}

public class UpdateCarrierOnboardingStatusDto
{
    public OnboardingStatus Status { get; set; }
}

public class LocationDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
    public DateTime? Timestamp { get; set; }
}

public class PerformanceMetricsDto
{
    public decimal Rating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public int CancelledTrips { get; set; }
    public decimal OnTimePercentage { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal CancellationRate { get; set; }
    public DateTime LastUpdated { get; set; }
}
