namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Custom report data transfer object
/// </summary>
public class CustomReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModified { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> Tags { get; set; } = new();

    // Properties required by DataExportService
    public Guid UserId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public Dictionary<string, object> Filters { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public Dictionary<string, string> Aggregations { get; set; } = new();
    public List<VisualizationConfigDto> Visualizations { get; set; } = new();
    public ReportScheduleConfigDto? ScheduleConfig { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Custom report request data transfer object
/// </summary>
public class CustomReportRequestDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public Guid RequestedBy { get; set; }

    // Properties required by DataExportService
    public Guid UserId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public Dictionary<string, object> Filters { get; set; } = new();
    public List<string> Columns { get; set; } = new();
    public Dictionary<string, string> Aggregations { get; set; } = new();
    public List<VisualizationConfigDto> Visualizations { get; set; } = new();
    public ReportScheduleConfigDto? ScheduleConfig { get; set; }
}
