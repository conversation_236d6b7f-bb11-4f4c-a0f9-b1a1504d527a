using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for report template operations
/// </summary>
public class ReportTemplateRepository : OptimizedRepositoryBase<ReportTemplate, Guid>, IReportTemplateRepository
{
    public ReportTemplateRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics) : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get report templates by category
    /// </summary>
    public async Task<IEnumerable<ReportTemplate>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportTemplate>()
            .Where(rt => rt.Category == category)
            .OrderBy(rt => rt.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get active report templates
    /// </summary>
    public async Task<IEnumerable<ReportTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportTemplate>()
            .Where(rt => rt.IsActive)
            .OrderBy(rt => rt.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get report templates by user role
    /// </summary>
    public async Task<IEnumerable<ReportTemplate>> GetByUserRoleAsync(string userRole, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportTemplate>()
            .Where(rt => rt.UserType.ToString() == userRole && rt.IsActive)
            .OrderBy(rt => rt.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Search report templates by name or description
    /// </summary>
    public async Task<IEnumerable<ReportTemplate>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var lowerSearchTerm = searchTerm.ToLower();

        return await Context.Set<ReportTemplate>()
            .Where(rt => rt.Name.ToLower().Contains(lowerSearchTerm) ||
                        rt.Description.ToLower().Contains(lowerSearchTerm))
            .OrderBy(rt => rt.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get templates by transport company
    /// </summary>
    public async Task<IEnumerable<ReportTemplate>> GetByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportTemplate>()
            .Where(rt => rt.CreatedBy == transportCompanyId && rt.IsActive)
            .OrderBy(rt => rt.Name)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Add custom template
    /// </summary>
    public async Task<ReportTemplate> AddCustomTemplateAsync(ReportTemplate template, CancellationToken cancellationToken = default)
    {
        await Context.Set<ReportTemplate>().AddAsync(template, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
        return template;
    }
}
