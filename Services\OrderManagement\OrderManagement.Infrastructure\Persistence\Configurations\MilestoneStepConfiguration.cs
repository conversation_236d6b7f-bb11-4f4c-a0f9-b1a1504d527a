using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class MilestoneStepConfiguration : IEntityTypeConfiguration<MilestoneStep>
{
    public void Configure(EntityTypeBuilder<MilestoneStep> builder)
    {
        builder.ToTable("MilestoneSteps");

        builder.<PERSON>Key(ms => ms.Id);

        builder.Property(ms => ms.Id)
            .ValueGeneratedOnAdd();

        builder.Property(ms => ms.MilestoneTemplateId)
            .IsRequired();

        builder.Property(ms => ms.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ms => ms.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(ms => ms.StepOrder)
            .IsRequired();

        builder.Property(ms => ms.PayoutPercentage)
            .IsRequired()
            .HasPrecision(5, 2);

        builder.Property(ms => ms.StepType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ms => ms.IsRequired)
            .IsRequired();

        builder.Property(ms => ms.RequiresDocumentation)
            .IsRequired();

        builder.Property(ms => ms.RequiresApproval)
            .IsRequired();

        builder.Property(ms => ms.DocumentationRequirements)
            .HasMaxLength(1000);

        builder.Property(ms => ms.ApprovalCriteria)
            .HasMaxLength(1000);

        builder.Property(ms => ms.EstimatedDurationHours)
            .IsRequired(false);

        // Configure StepMetadata as JSON
        builder.Property(ms => ms.StepMetadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        // Relationships
        builder.HasOne(ms => ms.MilestoneTemplate)
            .WithMany(mt => mt.Steps)
            .HasForeignKey(ms => ms.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ms => ms.MilestoneTemplateId)
            .HasDatabaseName("IX_MilestoneSteps_MilestoneTemplateId");

        builder.HasIndex(ms => ms.StepOrder)
            .HasDatabaseName("IX_MilestoneSteps_StepOrder");

        builder.HasIndex(ms => ms.StepType)
            .HasDatabaseName("IX_MilestoneSteps_StepType");

        builder.HasIndex(ms => new { ms.MilestoneTemplateId, ms.StepOrder })
            .HasDatabaseName("IX_MilestoneSteps_TemplateId_StepOrder")
            .IsUnique();
    }
}
