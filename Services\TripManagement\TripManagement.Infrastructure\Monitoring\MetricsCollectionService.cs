using Microsoft.Extensions.Logging;
using System.Diagnostics.Metrics;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;

namespace TripManagement.Infrastructure.Monitoring;

public class MetricsCollectionService : IMetricsCollectionService
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<MetricsCollectionService> _logger;
    
    private readonly Meter _meter;
    private readonly Counter<int> _tripCreatedCounter;
    private readonly Counter<int> _tripCompletedCounter;
    private readonly Counter<int> _tripCancelledCounter;
    private readonly Counter<int> _exceptionReportedCounter;
    private readonly Histogram<double> _tripDurationHistogram;
    private readonly Histogram<double> _tripDistanceHistogram;
    private readonly ObservableGauge<int> _activeTripsGauge;
    private readonly ObservableGauge<int> _availableDriversGauge;
    private readonly ObservableGauge<int> _availableVehiclesGauge;

    public MetricsCollectionService(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        ILogger<MetricsCollectionService> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _logger = logger;

        _meter = new Meter("TripManagement.Service");
        
        // Counters
        _tripCreatedCounter = _meter.CreateCounter<int>("trip_created_total", "count", "Total number of trips created");
        _tripCompletedCounter = _meter.CreateCounter<int>("trip_completed_total", "count", "Total number of trips completed");
        _tripCancelledCounter = _meter.CreateCounter<int>("trip_cancelled_total", "count", "Total number of trips cancelled");
        _exceptionReportedCounter = _meter.CreateCounter<int>("exception_reported_total", "count", "Total number of exceptions reported");
        
        // Histograms
        _tripDurationHistogram = _meter.CreateHistogram<double>("trip_duration_hours", "hours", "Trip duration in hours");
        _tripDistanceHistogram = _meter.CreateHistogram<double>("trip_distance_km", "kilometers", "Trip distance in kilometers");
        
        // Gauges
        _activeTripsGauge = _meter.CreateObservableGauge<int>("active_trips", "count", "Number of active trips", GetActiveTripsCount);
        _availableDriversGauge = _meter.CreateObservableGauge<int>("available_drivers", "count", "Number of available drivers", GetAvailableDriversCount);
        _availableVehiclesGauge = _meter.CreateObservableGauge<int>("available_vehicles", "count", "Number of available vehicles", GetAvailableVehiclesCount);
    }

    public void RecordTripCreated(Guid tripId, Guid carrierId, bool isUrgent)
    {
        var tags = new KeyValuePair<string, object?>[]
        {
            new("carrier_id", carrierId.ToString()),
            new("is_urgent", isUrgent.ToString())
        };

        _tripCreatedCounter.Add(1, tags);
        _logger.LogDebug("Recorded trip created metric for trip {TripId}", tripId);
    }

    public void RecordTripCompleted(Guid tripId, TimeSpan duration, double distanceKm, bool wasDelayed)
    {
        var tags = new KeyValuePair<string, object?>[]
        {
            new("was_delayed", wasDelayed.ToString())
        };

        _tripCompletedCounter.Add(1, tags);
        _tripDurationHistogram.Record(duration.TotalHours, tags);
        _tripDistanceHistogram.Record(distanceKm, tags);
        
        _logger.LogDebug("Recorded trip completed metrics for trip {TripId}: Duration={Duration}, Distance={Distance}km", 
            tripId, duration, distanceKm);
    }

    public void RecordTripCancelled(Guid tripId, string reason)
    {
        var tags = new KeyValuePair<string, object?>[]
        {
            new("cancellation_reason", reason)
        };

        _tripCancelledCounter.Add(1, tags);
        _logger.LogDebug("Recorded trip cancelled metric for trip {TripId}, reason: {Reason}", tripId, reason);
    }

    public void RecordExceptionReported(Guid tripId, ExceptionType exceptionType, string severity)
    {
        var tags = new KeyValuePair<string, object?>[]
        {
            new("exception_type", exceptionType.ToString()),
            new("severity", severity)
        };

        _exceptionReportedCounter.Add(1, tags);
        _logger.LogDebug("Recorded exception reported metric for trip {TripId}: Type={Type}, Severity={Severity}", 
            tripId, exceptionType, severity);
    }

    public async Task<TripManagementMetrics> CollectMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Collecting trip management metrics");

            var activeTrips = await _tripRepository.GetActiveTripsAsync(cancellationToken);
            var availableDrivers = await _driverRepository.GetAvailableDriversAsync(cancellationToken);
            var availableVehicles = await _vehicleRepository.GetAvailableVehiclesAsync(cancellationToken);

            // Calculate additional metrics
            var overdueTrips = activeTrips.Where(t => t.IsDelayed()).ToList();
            var tripsInException = await _tripRepository.GetTripsByStatusAsync(TripStatus.Exception, cancellationToken);

            var metrics = new TripManagementMetrics
            {
                ActiveTrips = activeTrips.Count,
                OverdueTrips = overdueTrips.Count,
                TripsInException = tripsInException.Count,
                AvailableDrivers = availableDrivers.Count,
                AvailableVehicles = availableVehicles.Count,
                DriverUtilizationRate = CalculateDriverUtilizationRate(availableDrivers),
                VehicleUtilizationRate = CalculateVehicleUtilizationRate(availableVehicles),
                AverageDeliveryTime = await CalculateAverageDeliveryTimeAsync(cancellationToken),
                OnTimeDeliveryRate = await CalculateOnTimeDeliveryRateAsync(cancellationToken),
                CollectedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Collected trip management metrics: ActiveTrips={ActiveTrips}, OverdueTrips={OverdueTrips}", 
                metrics.ActiveTrips, metrics.OverdueTrips);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting trip management metrics");
            throw;
        }
    }

    private async Task<int> GetActiveTripsCount()
    {
        try
        {
            var activeTrips = await _tripRepository.GetActiveTripsAsync();
            return activeTrips.Count;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting active trips count for metrics");
            return 0;
        }
    }

    private async Task<int> GetAvailableDriversCount()
    {
        try
        {
            var availableDrivers = await _driverRepository.GetAvailableDriversAsync();
            return availableDrivers.Count;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting available drivers count for metrics");
            return 0;
        }
    }

    private async Task<int> GetAvailableVehiclesCount()
    {
        try
        {
            var availableVehicles = await _vehicleRepository.GetAvailableVehiclesAsync();
            return availableVehicles.Count;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting available vehicles count for metrics");
            return 0;
        }
    }

    private double CalculateDriverUtilizationRate(List<Domain.Entities.Driver> availableDrivers)
    {
        // This would calculate the percentage of drivers currently on trips
        // For now, return a placeholder calculation
        return availableDrivers.Count > 0 ? 75.0 : 0.0;
    }

    private double CalculateVehicleUtilizationRate(List<Domain.Entities.Vehicle> availableVehicles)
    {
        // This would calculate the percentage of vehicles currently in use
        // For now, return a placeholder calculation
        return availableVehicles.Count > 0 ? 80.0 : 0.0;
    }

    private async Task<double> CalculateAverageDeliveryTimeAsync(CancellationToken cancellationToken)
    {
        try
        {
            // This would calculate average delivery time from completed trips
            // For now, return a placeholder value
            return 4.5; // hours
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating average delivery time");
            return 0.0;
        }
    }

    private async Task<double> CalculateOnTimeDeliveryRateAsync(CancellationToken cancellationToken)
    {
        try
        {
            // This would calculate on-time delivery rate from completed trips
            // For now, return a placeholder value
            return 85.5; // percentage
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating on-time delivery rate");
            return 0.0;
        }
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}

public record TripManagementMetrics
{
    public int ActiveTrips { get; init; }
    public int OverdueTrips { get; init; }
    public int TripsInException { get; init; }
    public int AvailableDrivers { get; init; }
    public int AvailableVehicles { get; init; }
    public double DriverUtilizationRate { get; init; }
    public double VehicleUtilizationRate { get; init; }
    public double AverageDeliveryTime { get; init; }
    public double OnTimeDeliveryRate { get; init; }
    public DateTime CollectedAt { get; init; }
}
