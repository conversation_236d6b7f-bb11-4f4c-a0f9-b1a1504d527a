using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Application.Queries.Analytics;

/// <summary>
/// Query to get analytics events with filtering
/// </summary>
public record GetAnalyticsEventsQuery(
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    AnalyticsEventType? EventType = null,
    DataSourceType? DataSource = null,
    Guid? UserId = null,
    UserType? UserType = null,
    string? EventName = null,
    int PageNumber = 1,
    int PageSize = 50
) : IRequest<PagedResult<AnalyticsEventDto>>;

/// <summary>
/// Query to get analytics event by ID
/// </summary>
public record GetAnalyticsEventByIdQuery(
    Guid EventId
) : IRequest<AnalyticsEventDto?>;

/// <summary>
/// Query to get user activity events
/// </summary>
public record GetUserActivityEventsQuery(
    Guid UserId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    string? EventName = null,
    int PageNumber = 1,
    int PageSize = 50
) : IRequest<PagedResult<AnalyticsEventDto>>;

/// <summary>
/// Query to get business transaction events
/// </summary>
public record GetBusinessTransactionEventsQuery(
    DataSourceType? DataSource = null,
    string? EntityType = null,
    Guid? EntityId = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    int PageNumber = 1,
    int PageSize = 50
) : IRequest<PagedResult<AnalyticsEventDto>>;

/// <summary>
/// Query to get event statistics
/// </summary>
public record GetEventStatisticsQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod GroupBy = TimePeriod.Daily,
    AnalyticsEventType? EventType = null,
    DataSourceType? DataSource = null,
    UserType? UserType = null
) : IRequest<List<EventStatisticsDto>>;

/// <summary>
/// Query to get top events by count
/// </summary>
public record GetTopEventsQuery(
    DateTime FromDate,
    DateTime ToDate,
    int TopCount = 10,
    AnalyticsEventType? EventType = null,
    DataSourceType? DataSource = null
) : IRequest<List<TopEventDto>>;

/// <summary>
/// Query to get metrics with filtering
/// </summary>
public record GetMetricsQuery(
    string? MetricName = null,
    KPICategory? Category = null,
    MetricType? Type = null,
    Guid? UserId = null,
    UserType? UserType = null,
    DataSourceType? DataSource = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    int PageNumber = 1,
    int PageSize = 50
) : IRequest<PagedResult<MetricDto>>;

/// <summary>
/// Query to get metric by ID
/// </summary>
public record GetMetricByIdQuery(
    Guid MetricId
) : IRequest<MetricDto?>;

/// <summary>
/// Query to get metrics by name with time series data
/// </summary>
public record GetMetricTimeSeriesQuery(
    string MetricName,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    Guid? UserId = null,
    UserType? UserType = null
) : IRequest<MetricTimeSeriesDto>;

/// <summary>
/// Query to get KPI performance summary
/// </summary>
public record GetKPIPerformanceSummaryQuery(
    KPICategory? Category = null,
    Guid? UserId = null,
    UserType? UserType = null,
    PerformanceStatus? PerformanceStatus = null
) : IRequest<List<KPIPerformanceDto>>;

/// <summary>
/// Query to get real-time metrics
/// </summary>
public record GetRealTimeMetricsQuery(
    List<string> MetricNames,
    Guid? UserId = null,
    UserType? UserType = null
) : IRequest<Dictionary<string, RealTimeMetricDto>>;

/// <summary>
/// Query to get metric trends
/// </summary>
public record GetMetricTrendsQuery(
    List<string> MetricNames,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    Guid? UserId = null
) : IRequest<Dictionary<string, MetricTrendDto>>;

/// <summary>
/// Query to compare metrics across users
/// </summary>
public record CompareMetricsQuery(
    List<Guid> UserIds,
    string MetricName,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<MetricComparisonDto>;

/// <summary>
/// Query to get underperforming KPIs
/// </summary>
public record GetUnderperformingKPIsQuery(
    Guid? UserId = null,
    UserType? UserType = null,
    KPICategory? Category = null,
    int MaxResults = 10
) : IRequest<List<UnderperformingKPIDto>>;

/// <summary>
/// Query to get analytics summary for dashboard
/// </summary>
public record GetAnalyticsSummaryQuery(
    Guid? UserId = null,
    UserType? UserType = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null
) : IRequest<AnalyticsSummaryDto>;

/// <summary>
/// Query to get user engagement metrics
/// </summary>
public record GetUserEngagementMetricsQuery(
    DateTime FromDate,
    DateTime ToDate,
    UserType? UserType = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<UserEngagementMetricsDto>;

/// <summary>
/// Query to get platform performance metrics
/// </summary>
public record GetPlatformPerformanceMetricsQuery(
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<PlatformPerformanceMetricsDto>;
