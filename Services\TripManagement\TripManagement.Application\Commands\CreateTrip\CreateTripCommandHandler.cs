using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Application.Commands.CreateTrip;

public class CreateTripCommandHandler : IRequestHandler<CreateTripCommand, Guid>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CreateTripCommandHandler> _logger;

    public CreateTripCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<CreateTripCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateTripCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating trip for order {OrderId}", request.OrderId);

        // Check if trip already exists for this order
        var existingTrip = await _tripRepository.GetByOrderIdAsync(request.OrderId, cancellationToken);
        if (existingTrip != null)
        {
            _logger.LogWarning("Trip already exists for order {OrderId}", request.OrderId);
            throw new InvalidOperationException($"Trip already exists for order {request.OrderId}");
        }

        // Create route
        var startLocation = new Location(
            request.Route.StartLocation.Latitude,
            request.Route.StartLocation.Longitude,
            request.Route.StartLocation.Timestamp,
            request.Route.StartLocation.Altitude,
            request.Route.StartLocation.Accuracy,
            request.Route.StartLocation.Address,
            request.Route.StartLocation.City,
            request.Route.StartLocation.State,
            request.Route.StartLocation.Country,
            request.Route.StartLocation.PostalCode);

        var endLocation = new Location(
            request.Route.EndLocation.Latitude,
            request.Route.EndLocation.Longitude,
            request.Route.EndLocation.Timestamp,
            request.Route.EndLocation.Altitude,
            request.Route.EndLocation.Accuracy,
            request.Route.EndLocation.Address,
            request.Route.EndLocation.City,
            request.Route.EndLocation.State,
            request.Route.EndLocation.Country,
            request.Route.EndLocation.PostalCode);

        var waypoints = request.Route.Waypoints.Select(w => new Location(
            w.Latitude,
            w.Longitude,
            w.Timestamp,
            w.Altitude,
            w.Accuracy,
            w.Address,
            w.City,
            w.State,
            w.Country,
            w.PostalCode)).ToList();

        var route = new Route(
            startLocation,
            endLocation,
            waypoints,
            request.Route.EstimatedDistanceKm,
            request.Route.EstimatedDuration,
            request.Route.RouteInstructions);

        // Create trip
        var trip = new Trip(
            request.OrderId,
            request.CarrierId,
            route,
            request.EstimatedStartTime,
            request.EstimatedEndTime,
            request.SpecialInstructions,
            request.IsUrgent,
            request.EstimatedDistanceKm);

        // Add stops
        foreach (var stopDto in request.Stops.OrderBy(s => s.SequenceNumber))
        {
            var stopLocation = new Location(
                stopDto.Location.Latitude,
                stopDto.Location.Longitude,
                stopDto.Location.Timestamp,
                stopDto.Location.Altitude,
                stopDto.Location.Accuracy,
                stopDto.Location.Address,
                stopDto.Location.City,
                stopDto.Location.State,
                stopDto.Location.Country,
                stopDto.Location.PostalCode);

            var tripStop = new TripStop(
                trip.Id,
                stopDto.StopType,
                stopDto.SequenceNumber,
                stopLocation,
                stopDto.ContactName,
                stopDto.ContactPhone,
                stopDto.ScheduledArrival,
                stopDto.ScheduledDeparture,
                stopDto.Instructions);

            trip.AddStop(tripStop);
        }

        // Save trip
        await _tripRepository.AddAsync(trip, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("trip.created", new
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            OrderId = trip.OrderId,
            CarrierId = trip.CarrierId,
            EstimatedStartTime = trip.EstimatedStartTime,
            EstimatedEndTime = trip.EstimatedEndTime,
            EstimatedDistanceKm = trip.EstimatedDistanceKm,
            IsUrgent = trip.IsUrgent,
            CreatedAt = trip.CreatedAt
        });

        _logger.LogInformation("Successfully created trip {TripId} with number {TripNumber}",
            trip.Id, trip.TripNumber);

        return trip.Id;
    }
}
