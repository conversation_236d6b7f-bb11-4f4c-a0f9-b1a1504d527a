using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqTimelineConfiguration : IEntityTypeConfiguration<RfqTimeline>
{
    public void Configure(EntityTypeBuilder<RfqTimeline> builder)
    {
        builder.ToTable("RfqTimelines");

        builder.HasKey(rt => rt.Id);

        builder.Property(rt => rt.Id)
            .ValueGeneratedOnAdd();

        builder.Property(rt => rt.RfqId)
            .IsRequired();

        builder.Property(rt => rt.EventType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rt => rt.EventDescription)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(rt => rt.EventTimestamp)
            .IsRequired();

        builder.Property(rt => rt.ActorId)
            .IsRequired(false);

        builder.Property(rt => rt.ActorName)
            .HasMaxLength(200);

        builder.Property(rt => rt.ActorRole)
            .HasMaxLength(100);

        builder.Property(rt => rt.AdditionalData)
            .HasMaxLength(2000);

        builder.Property(rt => rt.PreviousStatus)
            .IsRequired(false)
            .HasConversion<int>();

        builder.Property(rt => rt.NewStatus)
            .IsRequired(false)
            .HasConversion<int>();

        // Relationships
        builder.HasOne(rt => rt.RFQ)
            .WithMany(r => r.TimelineEvents)
            .HasForeignKey(rt => rt.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rt => rt.RfqId)
            .HasDatabaseName("IX_RfqTimelines_RfqId");

        builder.HasIndex(rt => rt.EventTimestamp)
            .HasDatabaseName("IX_RfqTimelines_EventTimestamp");

        builder.HasIndex(rt => rt.EventType)
            .HasDatabaseName("IX_RfqTimelines_EventType");

        builder.HasIndex(rt => rt.ActorId)
            .HasDatabaseName("IX_RfqTimelines_ActorId");
    }
}
