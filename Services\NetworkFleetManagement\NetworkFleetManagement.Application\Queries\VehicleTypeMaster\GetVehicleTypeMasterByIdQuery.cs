using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

/// <summary>
/// Query to get a vehicle type master by ID
/// </summary>
public class GetVehicleTypeMasterByIdQuery : IRequest<VehicleTypeMasterDto?>
{
    public Guid Id { get; set; }

    public GetVehicleTypeMasterByIdQuery(Guid id)
    {
        Id = id;
    }
}
