using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for compliance metrics
/// </summary>
public class ComplianceMetricsRequest : ValueObject
{
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    public List<string>? ComplianceTypes { get; init; }
    public List<string>? Departments { get; init; }
    public string? GroupBy { get; init; }
    public bool IncludeDetails { get; init; }
    public string? Format { get; init; }

    public ComplianceMetricsRequest(
        DateTime fromDate,
        DateTime toDate,
        List<string>? complianceTypes = null,
        List<string>? departments = null,
        string? groupBy = null,
        bool includeDetails = false,
        string? format = null)
    {
        FromDate = fromDate;
        ToDate = toDate;
        ComplianceTypes = complianceTypes;
        Departments = departments;
        GroupBy = groupBy;
        IncludeDetails = includeDetails;
        Format = format;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FromDate;
        yield return ToDate;
        yield return GroupBy ?? string.Empty;
        yield return IncludeDetails;
        yield return Format ?? string.Empty;
        
        if (ComplianceTypes != null)
        {
            foreach (var type in ComplianceTypes.OrderBy(x => x))
            {
                yield return type;
            }
        }
        
        if (Departments != null)
        {
            foreach (var dept in Departments.OrderBy(x => x))
            {
                yield return dept;
            }
        }
    }
}
