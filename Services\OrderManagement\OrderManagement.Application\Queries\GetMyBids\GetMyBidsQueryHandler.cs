using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetMyBids;

public class GetMyBidsQueryHandler : IRequestHandler<GetMyBidsQuery, PagedResult<BidSummaryDto>>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMyBidsQueryHandler> _logger;

    public GetMyBidsQueryHandler(
        IRfqBidRepository bidRepository,
        IMapper mapper,
        ILogger<GetMyBidsQueryHandler> logger)
    {
        _bidRepository = bidRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<BidSummaryDto>> Handle(GetMyBidsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting bids for broker {BrokerId}, Page: {Page}, PageSize: {PageSize}",
            request.BrokerId, request.Page, request.PageSize);

        // Parse status filter if provided
        BidStatus? statusFilter = null;
        if (!string.IsNullOrEmpty(request.Status) && Enum.TryParse<BidStatus>(request.Status, true, out var parsedStatus))
        {
            statusFilter = parsedStatus;
        }

        // Get paginated bids
        var bids = await _bidRepository.GetByBrokerAsync(
            request.BrokerId,
            request.Page,
            request.PageSize,
            statusFilter,
            request.FromDate,
            request.ToDate,
            cancellationToken);

        // Get total count for pagination
        var totalCount = await _bidRepository.GetCountByBrokerAsync(
            request.BrokerId,
            statusFilter,
            request.FromDate,
            request.ToDate,
            cancellationToken);

        var bidDtos = _mapper.Map<List<BidSummaryDto>>(bids);

        _logger.LogInformation("Retrieved {Count} bids out of {TotalCount} for broker {BrokerId}",
            bids.Count, totalCount, request.BrokerId);

        return new PagedResult<BidSummaryDto>
        {
            Items = bidDtos,
            TotalCount = totalCount,
            Page = request.Page,
            PageSize = request.PageSize
        };
    }
}
