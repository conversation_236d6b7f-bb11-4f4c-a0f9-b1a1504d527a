using MediatR;

namespace SubscriptionManagement.Application.Commands.ExtendSubscription
{
    public class ExtendSubscriptionCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public int ExtensionDays { get; set; }
        public string? Reason { get; set; }
        public bool ApplyAsGracePeriod { get; set; } = false;
        public Guid ExtendedByUserId { get; set; }
    }
}
