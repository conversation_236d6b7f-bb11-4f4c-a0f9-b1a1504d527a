using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class WorkflowConfiguration : IEntityTypeConfiguration<Workflow>
{
    public void Configure(EntityTypeBuilder<Workflow> builder)
    {
        builder.ToTable("workflows");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.Category)
            .HasColumnName("category")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Version)
            .HasColumnName("version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.TriggerType)
            .HasColumnName("trigger_type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.LastExecuted)
            .HasColumnName("last_executed");

        builder.Property(e => e.ExecutionCount)
            .HasColumnName("execution_count")
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasMany(e => e.Executions)
            .WithOne(ex => ex.Workflow)
            .HasForeignKey(ex => ex.WorkflowId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Tasks)
            .WithOne(t => t.Workflow)
            .HasForeignKey(t => t.WorkflowId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

public class WorkflowExecutionConfiguration : IEntityTypeConfiguration<WorkflowExecution>
{
    public void Configure(EntityTypeBuilder<WorkflowExecution> builder)
    {
        builder.ToTable("workflow_executions");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowId)
            .HasColumnName("workflow_id")
            .IsRequired();

        builder.Property(e => e.TriggeredBy)
            .HasColumnName("triggered_by")
            .IsRequired();

        builder.Property(e => e.StartTime)
            .HasColumnName("start_time")
            .IsRequired();

        builder.Property(e => e.EndTime)
            .HasColumnName("end_time");

        builder.Property(e => e.Status)
            .HasColumnName("status")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ErrorMessage)
            .HasColumnName("error_message")
            .HasMaxLength(2000);

        builder.Property(e => e.TriggerSource)
            .HasColumnName("trigger_source")
            .HasMaxLength(200);

        builder.Property(e => e.CurrentStepIndex)
            .HasColumnName("current_step_index")
            .IsRequired();

        builder.Property(e => e.CurrentStepId)
            .HasColumnName("current_step_id")
            .HasMaxLength(200);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.Workflow)
            .WithMany(w => w.Executions)
            .HasForeignKey(e => e.WorkflowId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Tasks)
            .WithOne(t => t.WorkflowExecution)
            .HasForeignKey(t => t.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public class WorkflowTaskConfiguration : IEntityTypeConfiguration<WorkflowTask>
{
    public void Configure(EntityTypeBuilder<WorkflowTask> builder)
    {
        builder.ToTable("workflow_tasks");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.WorkflowId)
            .HasColumnName("workflow_id");

        builder.Property(e => e.WorkflowExecutionId)
            .HasColumnName("workflow_execution_id");

        builder.Property(e => e.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.Type)
            .HasColumnName("type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Status)
            .HasColumnName("status")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Priority)
            .HasColumnName("priority")
            .IsRequired();

        builder.Property(e => e.AssignedTo)
            .HasColumnName("assigned_to");

        builder.Property(e => e.AssignedToRole)
            .HasColumnName("assigned_to_role")
            .HasMaxLength(100);

        builder.Property(e => e.DueDate)
            .HasColumnName("due_date");

        builder.Property(e => e.StartedAt)
            .HasColumnName("started_at");

        builder.Property(e => e.CompletedAt)
            .HasColumnName("completed_at");

        builder.Property(e => e.ErrorMessage)
            .HasColumnName("error_message")
            .HasMaxLength(2000);

        builder.Property(e => e.SLA)
            .HasColumnName("sla");

        builder.Property(e => e.IsOverdue)
            .HasColumnName("is_overdue")
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.Workflow)
            .WithMany(w => w.Tasks)
            .HasForeignKey(e => e.WorkflowId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.WorkflowExecution)
            .WithMany(ex => ex.Tasks)
            .HasForeignKey(e => e.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
