using Shared.Domain;
using Shared.Domain.Common;
using NetworkFleetManagement.Domain.Services;

namespace NetworkFleetManagement.Domain.Entities;

public class UsageAnalyticsRecord : BaseEntity
{
    public string EventType { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty;
    public Guid? EntityId { get; private set; }
    public string? EntityType { get; private set; }
    public Guid? UserId { get; private set; }
    public string? UserType { get; private set; }
    
    // Metrics
    public Dictionary<string, object> Metrics { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();
    public Dictionary<string, string> Tags { get; private set; } = new();
    
    // Timing
    public DateTime EventTimestamp { get; private set; }
    public TimeSpan? Duration { get; private set; }
    
    // Location (for fleet tracking)
    public double? Latitude { get; private set; }
    public double? Longitude { get; private set; }
    public string? LocationName { get; private set; }
    
    // Performance metrics
    public double? PerformanceScore { get; private set; }
    public string? PerformanceGrade { get; private set; }
    
    // Status
    public string Status { get; private set; } = "active";
    public bool IsProcessed { get; private set; }
    public DateTime? ProcessedAt { get; private set; }

    private UsageAnalyticsRecord() { } // EF Core

    public UsageAnalyticsRecord(
        string eventType,
        string category,
        Guid? entityId = null,
        string? entityType = null,
        Guid? userId = null,
        string? userType = null)
    {
        EventType = eventType;
        Category = category;
        EntityId = entityId;
        EntityType = entityType;
        UserId = userId;
        UserType = userType;
        EventTimestamp = DateTime.UtcNow;
        Status = "active";
    }

    public void SetMetrics(Dictionary<string, object> metrics)
    {
        Metrics = metrics ?? new Dictionary<string, object>();
    }

    public void AddMetric(string key, object value)
    {
        Metrics[key] = value;
    }

    public void SetMetadata(Dictionary<string, object> metadata)
    {
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public void SetTags(Dictionary<string, string> tags)
    {
        Tags = tags ?? new Dictionary<string, string>();
    }

    public void AddTag(string key, string value)
    {
        Tags[key] = value;
    }

    public void SetDuration(TimeSpan duration)
    {
        Duration = duration;
    }

    public void SetLocation(double latitude, double longitude, string? locationName = null)
    {
        Latitude = latitude;
        Longitude = longitude;
        LocationName = locationName;
    }

    public void SetPerformanceScore(double score, string? grade = null)
    {
        PerformanceScore = score;
        PerformanceGrade = grade;
    }

    public void MarkAsProcessed()
    {
        IsProcessed = true;
        ProcessedAt = DateTime.UtcNow;
    }

    public void SetStatus(string status)
    {
        Status = status;
    }

    // Helper methods for specific event types
    public static UsageAnalyticsRecord CreateVehicleUsageRecord(
        Guid vehicleId,
        VehicleUsageType usageType,
        Dictionary<string, object>? metadata = null)
    {
        var record = new UsageAnalyticsRecord(
            usageType.ToString(),
            "Vehicle",
            vehicleId,
            "Vehicle");

        if (metadata != null)
            record.SetMetadata(metadata);

        record.AddTag("category", "fleet");
        record.AddTag("type", "vehicle_usage");

        return record;
    }

    public static UsageAnalyticsRecord CreateDriverActivityRecord(
        Guid driverId,
        DriverActivityType activityType,
        Dictionary<string, object>? metadata = null)
    {
        var record = new UsageAnalyticsRecord(
            activityType.ToString(),
            "Driver",
            driverId,
            "Driver");

        if (metadata != null)
            record.SetMetadata(metadata);

        record.AddTag("category", "driver");
        record.AddTag("type", "driver_activity");

        return record;
    }

    public static UsageAnalyticsRecord CreateNetworkEventRecord(
        Guid networkId,
        NetworkEventType eventType,
        Dictionary<string, object>? metadata = null)
    {
        var record = new UsageAnalyticsRecord(
            eventType.ToString(),
            "Network",
            networkId,
            "BrokerCarrierNetwork");

        if (metadata != null)
            record.SetMetadata(metadata);

        record.AddTag("category", "network");
        record.AddTag("type", "network_event");

        return record;
    }

    public static UsageAnalyticsRecord CreateSystemEventRecord(
        SystemEventType eventType,
        Dictionary<string, object>? metadata = null)
    {
        var record = new UsageAnalyticsRecord(
            eventType.ToString(),
            "System",
            null,
            "System");

        if (metadata != null)
            record.SetMetadata(metadata);

        record.AddTag("category", "system");
        record.AddTag("type", "system_event");

        return record;
    }
}

public class AnalyticsAggregation : BaseEntity
{
    public string MetricType { get; private set; } = string.Empty;
    public string AggregationType { get; private set; } = string.Empty; // daily, weekly, monthly
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    
    public Guid? EntityId { get; private set; }
    public string? EntityType { get; private set; }
    
    public Dictionary<string, object> AggregatedValues { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();
    
    public int RecordCount { get; private set; }
    public DateTime LastCalculatedAt { get; private set; }

    private AnalyticsAggregation() { } // EF Core

    public AnalyticsAggregation(
        string metricType,
        string aggregationType,
        DateTime periodStart,
        DateTime periodEnd,
        Guid? entityId = null,
        string? entityType = null)
    {
        MetricType = metricType;
        AggregationType = aggregationType;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        EntityId = entityId;
        EntityType = entityType;
        LastCalculatedAt = DateTime.UtcNow;
    }

    public void SetAggregatedValues(Dictionary<string, object> values, int recordCount)
    {
        AggregatedValues = values ?? new Dictionary<string, object>();
        RecordCount = recordCount;
        LastCalculatedAt = DateTime.UtcNow;
    }

    public void SetMetadata(Dictionary<string, object> metadata)
    {
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}


