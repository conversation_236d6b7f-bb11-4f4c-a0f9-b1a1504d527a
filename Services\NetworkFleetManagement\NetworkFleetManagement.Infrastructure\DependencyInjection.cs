using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.Services;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Caching;
using NetworkFleetManagement.Infrastructure.HealthChecks;
using NetworkFleetManagement.Infrastructure.MessageBus;
using NetworkFleetManagement.Infrastructure.Monitoring;
using NetworkFleetManagement.Infrastructure.Persistence;
using NetworkFleetManagement.Infrastructure.Performance;
using NetworkFleetManagement.Infrastructure.Repositories;

namespace NetworkFleetManagement.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<NetworkFleetDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Repositories
        services.AddScoped<ICarrierRepository, CarrierRepository>();
        services.AddScoped<IVehicleRepository, VehicleRepository>();
        services.AddScoped<IDriverRepository, DriverRepository>();
        services.AddScoped<IBrokerCarrierNetworkRepository, BrokerCarrierNetworkRepository>();
        services.AddScoped<IPreferredPartnerRepository, PreferredPartnerRepository>();
        services.AddScoped<NetworkFleetManagement.Domain.Interfaces.IVehicleTypeMasterRepository, VehicleTypeMasterRepository>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Message Bus
        services.AddMessageBus(configuration);
        services.AddScoped<IIntegrationEventPublisher, IntegrationEventPublisher>();

        // Caching
        services.AddMemoryCache();
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = configuration.GetConnectionString("Redis");
        });
        services.AddScoped<IDistributedCacheService, DistributedCacheService>();

        // Performance & Monitoring
        services.AddScoped<IQueryOptimizationService, QueryOptimizationService>();
        services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();

        // Health Checks
        services.AddHealthChecks()
            .AddDbContext<NetworkFleetDbContext>()
            .AddCheck<RabbitMQHealthCheck>("rabbitmq");

        return services;
    }
}
