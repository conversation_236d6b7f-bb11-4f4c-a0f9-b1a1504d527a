using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Driver communication API controller
/// </summary>
[ApiController]
[Route("api/driver-communication")]
[Authorize]
public class DriverCommunicationController : ControllerBase
{
    private readonly IDriverCommunicationService _driverCommunicationService;
    private readonly IVoiceInstructionService _voiceInstructionService;
    private readonly IDriverOfflineService _driverOfflineService;

    public DriverCommunicationController(
        IDriverCommunicationService driverCommunicationService,
        IVoiceInstructionService voiceInstructionService,
        IDriverOfflineService driverOfflineService)
    {
        _driverCommunicationService = driverCommunicationService;
        _voiceInstructionService = voiceInstructionService;
        _driverOfflineService = driverOfflineService;
    }

    /// <summary>
    /// Send trip assignment to driver
    /// </summary>
    [HttpPost("trip-assignment")]
    [Authorize(Roles = "Admin,Dispatcher,Broker")]
    public async Task<IActionResult> SendTripAssignment(
        [FromBody] DriverTripAssignmentRequest request,
        CancellationToken cancellationToken = default)
    {
        var assignment = new DriverTripAssignment
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            PickupLocation = request.PickupLocation,
            DropoffLocation = request.DropoffLocation,
            PickupTime = request.PickupTime,
            CustomerName = request.CustomerName,
            EstimatedDistance = request.EstimatedDistance,
            EstimatedDuration = TimeSpan.FromMinutes(request.EstimatedDurationMinutes),
            EstimatedFare = request.EstimatedFare,
            PreferredLanguage = request.PreferredLanguage,
            EnableVoiceInstructions = request.EnableVoiceInstructions,
            AssignmentType = request.AssignmentType,
            IsUrgent = request.IsUrgent,
            Metadata = request.Metadata ?? new()
        };

        var result = await _driverCommunicationService.SendTripAssignmentAsync(assignment, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                MessageId = result.MessageId,
                DriverWasOnline = result.DriverWasOnline,
                Message = result.Message ?? "Trip assignment sent successfully"
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage });
    }

    /// <summary>
    /// Send trip update to driver
    /// </summary>
    [HttpPost("trip-update")]
    [Authorize(Roles = "Admin,Dispatcher,Broker,Customer")]
    public async Task<IActionResult> SendTripUpdate(
        [FromBody] DriverTripUpdateRequest request,
        CancellationToken cancellationToken = default)
    {
        var update = new DriverTripUpdate
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            UpdateType = request.UpdateType,
            PreferredLanguage = request.PreferredLanguage,
            Priority = request.Priority,
            Parameters = request.Parameters ?? new(),
            Timestamp = DateTime.UtcNow
        };

        var result = await _driverCommunicationService.SendTripUpdateAsync(update, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                MessageId = result.MessageId,
                DriverWasOnline = result.DriverWasOnline
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage });
    }

    /// <summary>
    /// Send navigation instruction to driver
    /// </summary>
    [HttpPost("navigation")]
    [Authorize(Roles = "Admin,Dispatcher,NavigationService")]
    public async Task<IActionResult> SendNavigationInstruction(
        [FromBody] DriverNavigationRequest request,
        CancellationToken cancellationToken = default)
    {
        var instruction = new DriverNavigationInstruction
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            Instruction = request.Instruction,
            Distance = request.Distance,
            Direction = request.Direction,
            StreetName = request.StreetName,
            Landmark = request.Landmark,
            InstructionType = request.InstructionType,
            CurrentLocation = new GeoLocation
            {
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                Accuracy = request.Accuracy
            },
            PreferredLanguage = request.PreferredLanguage,
            EnableVoice = request.EnableVoice,
            PhoneNumber = request.PhoneNumber
        };

        var result = await _driverCommunicationService.SendNavigationInstructionAsync(instruction, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                MessageId = result.MessageId,
                DriverWasOnline = result.DriverWasOnline,
                Message = result.Message
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage });
    }

    /// <summary>
    /// Send emergency alert to driver
    /// </summary>
    [HttpPost("emergency-alert")]
    [Authorize(Roles = "Admin,Dispatcher,EmergencyService")]
    public async Task<IActionResult> SendEmergencyAlert(
        [FromBody] DriverEmergencyAlertRequest request,
        CancellationToken cancellationToken = default)
    {
        var alert = new DriverEmergencyAlert
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            AlertType = request.AlertType,
            Severity = request.Severity,
            Message = request.Message,
            Location = request.Location,
            PreferredLanguage = request.PreferredLanguage,
            RequiresResponse = request.RequiresResponse,
            PhoneNumber = request.PhoneNumber,
            Metadata = request.Metadata ?? new()
        };

        var result = await _driverCommunicationService.SendEmergencyAlertAsync(alert, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                MessageId = result.MessageId,
                Message = result.Message ?? "Emergency alert sent successfully"
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage });
    }

    /// <summary>
    /// Get driver offline messages
    /// </summary>
    [HttpGet("offline-messages/{driverId}")]
    [Authorize(Roles = "Admin,Dispatcher,Driver")]
    public async Task<IActionResult> GetOfflineMessages(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        // Ensure drivers can only access their own messages
        if (User.IsInRole("Driver"))
        {
            var currentDriverId = GetCurrentUserId();
            if (currentDriverId != driverId)
            {
                return Forbid("Drivers can only access their own messages");
            }
        }

        var messages = await _driverCommunicationService.GetOfflineMessagesAsync(driverId, cancellationToken);

        return Ok(new
        {
            DriverId = driverId,
            MessageCount = messages.Count,
            Messages = messages.Select(m => new
            {
                MessageId = m.MessageId,
                ConversationId = m.ConversationId,
                SenderId = m.SenderId,
                Content = m.Content,
                MessageType = m.MessageType.ToString(),
                CreatedAt = m.CreatedAt,
                IsDelivered = m.IsDelivered,
                Metadata = m.Metadata
            })
        });
    }

    /// <summary>
    /// Mark messages as delivered
    /// </summary>
    [HttpPost("messages/delivered")]
    [Authorize(Roles = "Driver")]
    public async Task<IActionResult> MarkMessagesAsDelivered(
        [FromBody] MarkMessagesDeliveredRequest request,
        CancellationToken cancellationToken = default)
    {
        var currentDriverId = GetCurrentUserId();
        var success = await _driverCommunicationService.MarkMessagesAsDeliveredAsync(
            currentDriverId, 
            request.MessageIds, 
            cancellationToken);

        if (success)
        {
            return Ok(new
            {
                Success = true,
                Message = $"Marked {request.MessageIds.Count} messages as delivered"
            });
        }

        return BadRequest(new { Success = false, Error = "Failed to mark messages as delivered" });
    }

    /// <summary>
    /// Check driver online status
    /// </summary>
    [HttpGet("status/{driverId}")]
    [Authorize(Roles = "Admin,Dispatcher,Broker")]
    public async Task<IActionResult> GetDriverStatus(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        var isOnline = await _driverCommunicationService.IsDriverOnlineAsync(driverId, cancellationToken);
        var offlineStatus = await _driverOfflineService.GetDriverOfflineStatusAsync(driverId, cancellationToken);

        return Ok(new
        {
            DriverId = driverId,
            IsOnline = isOnline,
            LastSeen = offlineStatus.LastSeen,
            PendingMessageCount = offlineStatus.PendingMessageCount,
            OfflineDataSize = offlineStatus.OfflineDataSize,
            LastSyncTime = offlineStatus.LastSyncTime,
            OfflineCapabilityEnabled = offlineStatus.OfflineCapabilityEnabled
        });
    }

    /// <summary>
    /// Send voice instruction to driver
    /// </summary>
    [HttpPost("voice-instruction")]
    [Authorize(Roles = "Admin,Dispatcher")]
    public async Task<IActionResult> SendVoiceInstruction(
        [FromBody] VoiceInstructionRequest request,
        CancellationToken cancellationToken = default)
    {
        var instruction = new VoiceInstruction
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            Message = request.Message,
            Language = request.Language,
            Priority = request.Priority,
            InstructionType = request.InstructionType,
            PhoneNumber = request.PhoneNumber,
            Metadata = request.Metadata ?? new()
        };

        var result = await _voiceInstructionService.SendVoiceInstructionAsync(instruction, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                CallId = result.CallId,
                Cost = result.Cost,
                Message = "Voice instruction sent successfully"
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage });
    }

    /// <summary>
    /// Get driver voice settings
    /// </summary>
    [HttpGet("voice-settings/{driverId}")]
    [Authorize(Roles = "Admin,Dispatcher,Driver")]
    public async Task<IActionResult> GetVoiceSettings(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        // Ensure drivers can only access their own settings
        if (User.IsInRole("Driver"))
        {
            var currentDriverId = GetCurrentUserId();
            if (currentDriverId != driverId)
            {
                return Forbid("Drivers can only access their own settings");
            }
        }

        var settings = await _voiceInstructionService.GetDriverVoiceSettingsAsync(driverId, cancellationToken);

        return Ok(settings);
    }

    /// <summary>
    /// Update driver voice settings
    /// </summary>
    [HttpPut("voice-settings/{driverId}")]
    [Authorize(Roles = "Driver")]
    public async Task<IActionResult> UpdateVoiceSettings(
        Guid driverId,
        [FromBody] VoiceInstructionSettings settings,
        CancellationToken cancellationToken = default)
    {
        var currentDriverId = GetCurrentUserId();
        if (currentDriverId != driverId)
        {
            return Forbid("Drivers can only update their own settings");
        }

        var success = await _voiceInstructionService.UpdateDriverVoiceSettingsAsync(driverId, settings, cancellationToken);

        if (success)
        {
            return Ok(new { Success = true, Message = "Voice settings updated successfully" });
        }

        return BadRequest(new { Success = false, Error = "Failed to update voice settings" });
    }

    /// <summary>
    /// Sync offline data
    /// </summary>
    [HttpPost("offline/sync")]
    [Authorize(Roles = "Driver")]
    public async Task<IActionResult> SyncOfflineData(
        [FromBody] OfflineSyncRequest request,
        CancellationToken cancellationToken = default)
    {
        var currentDriverId = GetCurrentUserId();
        var result = await _driverOfflineService.SyncOfflineDataAsync(currentDriverId, request.Actions, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                Message = result.Message,
                SyncResults = result.Data
            });
        }

        return BadRequest(new { Success = false, Error = result.Message });
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }
}

// Request models
public class DriverTripAssignmentRequest
{
    public Guid DriverId { get; set; }
    public Guid TripId { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DropoffLocation { get; set; } = string.Empty;
    public DateTime PickupTime { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public double EstimatedDistance { get; set; }
    public int EstimatedDurationMinutes { get; set; }
    public decimal EstimatedFare { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool EnableVoiceInstructions { get; set; } = true;
    public TripAssignmentType AssignmentType { get; set; } = TripAssignmentType.Normal;
    public bool IsUrgent { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class DriverTripUpdateRequest
{
    public Guid DriverId { get; set; }
    public Guid TripId { get; set; }
    public TripUpdateType UpdateType { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public Priority Priority { get; set; } = Priority.Normal;
    public Dictionary<string, object>? Parameters { get; set; }
}

public class DriverNavigationRequest
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public string Instruction { get; set; } = string.Empty;
    public double Distance { get; set; }
    public string Direction { get; set; } = string.Empty;
    public string? StreetName { get; set; }
    public string? Landmark { get; set; }
    public NavigationInstructionType InstructionType { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool EnableVoice { get; set; } = true;
    public string? PhoneNumber { get; set; }
}

public class DriverEmergencyAlertRequest
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public EmergencyAlertType AlertType { get; set; }
    public EmergencySeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Location { get; set; }
    public Language PreferredLanguage { get; set; } = Language.English;
    public bool RequiresResponse { get; set; } = true;
    public string? PhoneNumber { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class MarkMessagesDeliveredRequest
{
    public List<Guid> MessageIds { get; set; } = new();
}

public class VoiceInstructionRequest
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public string Message { get; set; } = string.Empty;
    public Language Language { get; set; } = Language.English;
    public VoicePriority Priority { get; set; } = VoicePriority.Normal;
    public VoiceInstructionType InstructionType { get; set; } = VoiceInstructionType.General;
    public string? PhoneNumber { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

public class OfflineSyncRequest
{
    public List<DriverOfflineAction> Actions { get; set; } = new();
}
