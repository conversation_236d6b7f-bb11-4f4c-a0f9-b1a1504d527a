using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Shipper;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Shipper analytics controller for SLA performance, cost analysis, provider comparison, and business reporting
/// </summary>
[ApiController]
[Route("api/shipper/analytics")]
[Authorize(Roles = "Shipper,Admin")]
public class ShipperAnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ShipperAnalyticsController> _logger;

    public ShipperAnalyticsController(IMediator mediator, ILogger<ShipperAnalyticsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get shipper dashboard with comprehensive analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Shipper dashboard data</returns>
    [HttpGet("{shipperId}/dashboard")]
    public async Task<ActionResult<ShipperDashboardDto>> GetDashboard(
        Guid shipperId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetShipperDashboardQuery(shipperId, fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipper dashboard for {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper SLA performance analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for SLA analysis</param>
    /// <param name="toDate">End date for SLA analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeSLAMetrics">Include detailed SLA metrics</param>
    /// <param name="includeKPIPerformance">Include KPI performance analysis</param>
    /// <param name="includeComplianceAnalysis">Include compliance analysis</param>
    /// <returns>Shipper SLA performance data</returns>
    [HttpGet("{shipperId}/sla-performance")]
    public async Task<ActionResult<ShipperSLAPerformanceDto>> GetSLAPerformance(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeSLAMetrics = true,
        [FromQuery] bool includeKPIPerformance = true,
        [FromQuery] bool includeComplianceAnalysis = true)
    {
        try
        {
            var query = new GetShipperSLAPerformanceQuery(shipperId, fromDate, toDate, period, includeSLAMetrics, includeKPIPerformance);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SLA performance for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper cost analysis
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for cost analysis</param>
    /// <param name="toDate">End date for cost analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeCostPerTrip">Include cost per trip analysis</param>
    /// <param name="includeFreightOptimization">Include freight optimization</param>
    /// <param name="includeRouteEfficiency">Include route efficiency analysis</param>
    /// <returns>Shipper cost analysis data</returns>
    [HttpGet("{shipperId}/cost-analysis")]
    public async Task<ActionResult<ShipperCostAnalysisDto>> GetCostAnalysis(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeCostPerTrip = true,
        [FromQuery] bool includeFreightOptimization = true,
        [FromQuery] bool includeRouteEfficiency = true)
    {
        try
        {
            var query = new GetShipperCostAnalysisQuery(shipperId, fromDate, toDate, period, includeCostPerTrip, includeFreightOptimization, includeRouteEfficiency);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost analysis for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper provider comparison
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for provider comparison</param>
    /// <param name="toDate">End date for provider comparison</param>
    /// <param name="providerType">Filter by provider type</param>
    /// <param name="includePerformanceMetrics">Include performance metrics</param>
    /// <param name="includeCostComparison">Include cost comparison</param>
    /// <param name="includeServiceQuality">Include service quality comparison</param>
    /// <returns>Shipper provider comparison data</returns>
    [HttpGet("{shipperId}/provider-comparison")]
    public async Task<ActionResult<ShipperProviderComparisonDto>> GetProviderComparison(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? providerType = null,
        [FromQuery] bool includePerformanceMetrics = true,
        [FromQuery] bool includeCostComparison = true,
        [FromQuery] bool includeServiceQuality = true)
    {
        try
        {
            var query = new GetShipperProviderComparisonQuery(shipperId, fromDate, toDate, null, providerType, includePerformanceMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting provider comparison for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper business reporting
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for business reporting</param>
    /// <param name="toDate">End date for business reporting</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeShipmentReports">Include shipment reports</param>
    /// <param name="includeTrendAnalysis">Include trend analysis</param>
    /// <param name="includeYearOverYear">Include year-over-year comparison</param>
    /// <returns>Shipper business reporting data</returns>
    [HttpGet("{shipperId}/business-reporting")]
    public async Task<ActionResult<ShipperBusinessReportingDto>> GetBusinessReporting(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeShipmentReports = true,
        [FromQuery] bool includeTrendAnalysis = true,
        [FromQuery] bool includeYearOverYear = true)
    {
        try
        {
            var query = new GetShipperBusinessReportingQuery(shipperId, fromDate, toDate, period, includeShipmentReports, includeTrendAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting business reporting for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper route efficiency analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for route efficiency analysis</param>
    /// <param name="toDate">End date for route efficiency analysis</param>
    /// <param name="originCity">Filter by origin city</param>
    /// <param name="destinationCity">Filter by destination city</param>
    /// <param name="includeOptimizationRecommendations">Include optimization recommendations</param>
    /// <returns>Shipper route efficiency data</returns>
    [HttpGet("{shipperId}/route-efficiency")]
    public async Task<ActionResult<ShipperRouteEfficiencyDto>> GetRouteEfficiency(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? originCity = null,
        [FromQuery] string? destinationCity = null,
        [FromQuery] bool includeOptimizationRecommendations = true)
    {
        try
        {
            var query = new GetShipperRouteEfficiencyQuery(shipperId, fromDate, toDate, originCity, destinationCity, includeOptimizationRecommendations);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting route efficiency for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper freight optimization analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for freight optimization analysis</param>
    /// <param name="toDate">End date for freight optimization analysis</param>
    /// <param name="includeLoadOptimization">Include load optimization</param>
    /// <param name="includeCarrierOptimization">Include carrier optimization</param>
    /// <param name="includeRouteOptimization">Include route optimization</param>
    /// <returns>Shipper freight optimization data</returns>
    [HttpGet("{shipperId}/freight-optimization")]
    public async Task<ActionResult<ShipperFreightOptimizationDto>> GetFreightOptimization(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] bool includeLoadOptimization = true,
        [FromQuery] bool includeCarrierOptimization = true,
        [FromQuery] bool includeRouteOptimization = true)
    {
        try
        {
            var query = new GetShipperFreightOptimizationQuery(shipperId, fromDate, toDate, includeLoadOptimization, includeCarrierOptimization, includeRouteOptimization);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting freight optimization for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper service quality analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for service quality analysis</param>
    /// <param name="toDate">End date for service quality analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="providerType">Filter by provider type</param>
    /// <param name="includeQualityMetrics">Include quality metrics</param>
    /// <returns>Shipper service quality data</returns>
    [HttpGet("{shipperId}/service-quality")]
    public async Task<ActionResult<ShipperServiceQualityDto>> GetServiceQuality(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? providerType = null,
        [FromQuery] bool includeQualityMetrics = true)
    {
        try
        {
            var query = new GetShipperServiceQualityQuery(shipperId, fromDate, toDate, period, includeQualityMetrics, true);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service quality for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper shipment trends analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for shipment trends analysis</param>
    /// <param name="toDate">End date for shipment trends analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeVolumeAnalysis">Include volume analysis</param>
    /// <param name="includeSeasonalTrends">Include seasonal trends</param>
    /// <param name="includeForecasting">Include forecasting</param>
    /// <returns>Shipper shipment trends data</returns>
    [HttpGet("{shipperId}/shipment-trends")]
    public async Task<ActionResult<ShipperShipmentTrendsDto>> GetShipmentTrends(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeVolumeAnalysis = true,
        [FromQuery] bool includeSeasonalTrends = true,
        [FromQuery] bool includeForecasting = true)
    {
        try
        {
            var query = new GetShipperShipmentTrendsQuery(shipperId, fromDate, toDate, period, includeVolumeAnalysis, includeSeasonalTrends, includeForecasting);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipment trends for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper financial performance
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for financial analysis</param>
    /// <param name="toDate">End date for financial analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeCostBreakdown">Include cost breakdown</param>
    /// <param name="includeBudgetAnalysis">Include budget analysis</param>
    /// <param name="includeROIAnalysis">Include ROI analysis</param>
    /// <returns>Shipper financial performance data</returns>
    [HttpGet("{shipperId}/financial-performance")]
    public async Task<ActionResult<ShipperFinancialPerformanceDto>> GetFinancialPerformance(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeCostBreakdown = true,
        [FromQuery] bool includeBudgetAnalysis = true,
        [FromQuery] bool includeROIAnalysis = true)
    {
        try
        {
            var query = new AnalyticsBIService.Application.Queries.Shipper.GetShipperFinancialPerformanceQuery(shipperId, fromDate, toDate, period, includeCostBreakdown, includeBudgetAnalysis, includeROIAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial performance for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper compliance analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for compliance analysis</param>
    /// <param name="toDate">End date for compliance analysis</param>
    /// <param name="complianceType">Filter by compliance type</param>
    /// <param name="includeViolationTracking">Include violation tracking</param>
    /// <param name="includeRegulatoryReporting">Include regulatory reporting</param>
    /// <returns>Shipper compliance analytics data</returns>
    [HttpGet("{shipperId}/compliance")]
    public async Task<ActionResult<ShipperComplianceAnalyticsDto>> GetComplianceAnalytics(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? complianceType = null,
        [FromQuery] bool includeViolationTracking = true,
        [FromQuery] bool includeRegulatoryReporting = true)
    {
        try
        {
            var query = new GetShipperComplianceAnalyticsQuery(shipperId, fromDate, toDate, complianceType, null, includeViolationTracking);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance analytics for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper competitive benchmarking
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for benchmarking analysis</param>
    /// <param name="toDate">End date for benchmarking analysis</param>
    /// <param name="benchmarkType">Type of benchmarking (cost, performance, service)</param>
    /// <param name="includeIndustryComparison">Include industry comparison</param>
    /// <param name="includeMarketAnalysis">Include market analysis</param>
    /// <returns>Shipper competitive benchmarking data</returns>
    [HttpGet("{shipperId}/competitive-benchmarking")]
    public async Task<ActionResult<ShipperCompetitiveBenchmarkingDto>> GetCompetitiveBenchmarking(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? benchmarkType = null,
        [FromQuery] bool includeIndustryComparison = true,
        [FromQuery] bool includeMarketAnalysis = true)
    {
        try
        {
            var query = new GetShipperCompetitiveBenchmarkingQuery(shipperId, fromDate, toDate, benchmarkType, null, includeIndustryComparison);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competitive benchmarking for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get shipper sustainability analytics
    /// </summary>
    /// <param name="shipperId">Shipper ID</param>
    /// <param name="fromDate">Start date for sustainability analysis</param>
    /// <param name="toDate">End date for sustainability analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeCarbonFootprint">Include carbon footprint analysis</param>
    /// <param name="includeGreenLogistics">Include green logistics metrics</param>
    /// <returns>Shipper sustainability analytics data</returns>
    [HttpGet("{shipperId}/sustainability")]
    public async Task<ActionResult<ShipperSustainabilityAnalyticsDto>> GetSustainabilityAnalytics(
        Guid shipperId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeCarbonFootprint = true,
        [FromQuery] bool includeGreenLogistics = true)
    {
        try
        {
            var query = new GetShipperSustainabilityAnalyticsQuery(shipperId, fromDate, toDate, period, includeCarbonFootprint, includeGreenLogistics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sustainability analytics for shipper {ShipperId}", shipperId);
            return StatusCode(500, "Internal server error");
        }
    }
}
