using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Enums;

namespace UserManagement.Domain.Repositories
{
    public interface IKycApprovalHistoryRepository
    {
        Task<KycApprovalHistoryEntry> GetByIdAsync(Guid id);
        Task<IEnumerable<KycApprovalHistoryEntry>> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<KycApprovalHistoryEntry>> GetByDocumentIdAsync(Guid documentId);
        Task<IEnumerable<KycApprovalHistoryEntry>> GetByProcessorAsync(Guid processorId);
        Task<(IEnumerable<KycApprovalHistoryEntry> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            Guid? userId = null,
            string documentType = null,
            KycDecision? decision = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);
        Task AddAsync(KycApprovalHistoryEntry entry);
        Task UpdateAsync(KycApprovalHistoryEntry entry);
        Task DeleteAsync(Guid id);
        Task<IEnumerable<KycApprovalHistoryEntry>> GetRecentDecisionsAsync(int days = 30);
        Task<Dictionary<KycDecision, int>> GetDecisionStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<KycApprovalHistoryEntry>> GetAutoProcessedEntriesAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}
