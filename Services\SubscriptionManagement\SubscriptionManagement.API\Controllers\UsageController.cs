using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Services;
using System.Security.Claims;

namespace SubscriptionManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsageController : ControllerBase
{
    private readonly IUsageTrackingService _usageTrackingService;
    private readonly ILogger<UsageController> _logger;

    public UsageController(
        IUsageTrackingService usageTrackingService,
        ILogger<UsageController> logger)
    {
        _usageTrackingService = usageTrackingService;
        _logger = logger;
    }

    /// <summary>
    /// Record feature usage for the current user
    /// </summary>
    [HttpPost("record")]
    public async Task<IActionResult> RecordUsage([FromBody] RecordUsageRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            
            await _usageTrackingService.RecordUsageAsync(
                userId, 
                request.FeatureType, 
                request.Count, 
                request.Metadata);
            
            return Ok(new { message = "Usage recorded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording usage for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to record usage" });
        }
    }

    /// <summary>
    /// Check if user can use a feature
    /// </summary>
    [HttpGet("can-use/{featureType}")]
    public async Task<IActionResult> CanUseFeature(FeatureType featureType, [FromQuery] int count = 1)
    {
        try
        {
            var userId = GetCurrentUserId();
            var canUse = await _usageTrackingService.CanUseFeatureAsync(userId, featureType, count);
            var remaining = await _usageTrackingService.GetRemainingUsageAsync(userId, featureType);
            
            return Ok(new 
            { 
                canUse, 
                remaining,
                featureType = featureType.ToString(),
                requestedCount = count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking feature usage for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to check feature usage" });
        }
    }

    /// <summary>
    /// Get current period usage summary
    /// </summary>
    [HttpGet("current-period")]
    public async Task<IActionResult> GetCurrentPeriodUsage()
    {
        try
        {
            var userId = GetCurrentUserId();
            var usage = await _usageTrackingService.GetCurrentPeriodUsageAsync(userId);
            var limits = await _usageTrackingService.GetFeatureLimitsAsync(userId);
            var resetDate = await _usageTrackingService.GetUsagePeriodResetDateAsync(userId);
            
            var result = usage.Select(kvp => new
            {
                FeatureType = kvp.Key.ToString(),
                CurrentUsage = kvp.Value,
                Limit = limits.ContainsKey(kvp.Key) ? limits[kvp.Key] : (int?)null,
                IsUnlimited = _usageTrackingService.IsFeatureUnlimitedAsync(userId, kvp.Key).Result,
                PercentageUsed = limits.ContainsKey(kvp.Key) && limits[kvp.Key] > 0 
                    ? (double)kvp.Value / limits[kvp.Key] * 100 
                    : 0
            });
            
            return Ok(new 
            { 
                usage = result,
                periodResetDate = resetDate,
                currentDate = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current period usage for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to get usage data" });
        }
    }

    /// <summary>
    /// Get usage history
    /// </summary>
    [HttpGet("history")]
    public async Task<IActionResult> GetUsageHistory(
        [FromQuery] FeatureType? featureType = null,
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var history = await _usageTrackingService.GetUsageHistoryAsync(userId, featureType, from, to);
            
            var result = history.Select(h => new
            {
                h.Id,
                FeatureType = h.FeatureType.ToString(),
                h.UsageCount,
                h.UsageDate,
                h.PeriodStart,
                h.PeriodEnd,
                h.Metadata
            });
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to get usage history" });
        }
    }

    /// <summary>
    /// Get usage analytics
    /// </summary>
    [HttpGet("analytics")]
    public async Task<IActionResult> GetUsageAnalytics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var analytics = await _usageTrackingService.GetUsageAnalyticsAsync(userId, from, to);
            
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage analytics for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to get usage analytics" });
        }
    }

    /// <summary>
    /// Get feature limits for current user
    /// </summary>
    [HttpGet("limits")]
    public async Task<IActionResult> GetFeatureLimits()
    {
        try
        {
            var userId = GetCurrentUserId();
            var limits = await _usageTrackingService.GetFeatureLimitsAsync(userId);
            
            var result = limits.Select(kvp => new
            {
                FeatureType = kvp.Key.ToString(),
                Limit = kvp.Value,
                IsUnlimited = _usageTrackingService.IsFeatureUnlimitedAsync(userId, kvp.Key).Result
            });
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature limits for user {UserId}", GetCurrentUserId());
            return StatusCode(500, new { message = "Failed to get feature limits" });
        }
    }

    /// <summary>
    /// Get users near limit (Admin only)
    /// </summary>
    [HttpGet("admin/near-limit/{featureType}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetUsersNearLimit(FeatureType featureType, [FromQuery] double threshold = 0.8)
    {
        try
        {
            var users = await _usageTrackingService.GetUsersNearLimitAsync(featureType, threshold);
            
            return Ok(new 
            { 
                featureType = featureType.ToString(),
                threshold,
                usersNearLimit = users,
                count = users.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users near limit for feature {FeatureType}", featureType);
            return StatusCode(500, new { message = "Failed to get users near limit" });
        }
    }

    /// <summary>
    /// Get users over limit (Admin only)
    /// </summary>
    [HttpGet("admin/over-limit/{featureType}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetUsersOverLimit(FeatureType featureType)
    {
        try
        {
            var users = await _usageTrackingService.GetUsersOverLimitAsync(featureType);
            
            return Ok(new 
            { 
                featureType = featureType.ToString(),
                usersOverLimit = users,
                count = users.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users over limit for feature {FeatureType}", featureType);
            return StatusCode(500, new { message = "Failed to get users over limit" });
        }
    }

    /// <summary>
    /// Get average usage statistics (Admin only)
    /// </summary>
    [HttpGet("admin/average")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetAverageUsage(
        [FromQuery] DateTime from,
        [FromQuery] DateTime to)
    {
        try
        {
            var averageUsage = await _usageTrackingService.GetAverageUsageAsync(from, to);
            
            var result = averageUsage.Select(kvp => new
            {
                FeatureType = kvp.Key.ToString(),
                AverageUsage = kvp.Value
            });
            
            return Ok(new 
            { 
                period = new { from, to },
                averageUsage = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting average usage from {From} to {To}", from, to);
            return StatusCode(500, new { message = "Failed to get average usage" });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user ID in token");
    }
}

// Request models
public class RecordUsageRequest
{
    public FeatureType FeatureType { get; set; }
    public int Count { get; set; } = 1;
    public string? Metadata { get; set; }
}
