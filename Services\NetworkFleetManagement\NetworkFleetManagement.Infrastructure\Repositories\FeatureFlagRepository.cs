using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class FeatureFlagRepository : IFeatureFlagRepository
{
    private readonly NetworkFleetDbContext _context;
    private readonly ILogger<FeatureFlagRepository> _logger;

    public FeatureFlagRepository(NetworkFleetDbContext context, ILogger<FeatureFlagRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<FeatureFlag?> GetByKeyAsync(string key)
    {
        try
        {
            return await _context.FeatureFlags
                .FirstOrDefaultAsync(f => f.Key == key && !f.IsArchived);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag by key: {Key}", key);
            return null;
        }
    }

    public async Task<FeatureFlag?> GetByIdAsync(Guid id)
    {
        try
        {
            return await _context.FeatureFlags
                .FirstOrDefaultAsync(f => f.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag by id: {Id}", id);
            return null;
        }
    }

    public async Task<List<FeatureFlag>> GetAllAsync()
    {
        try
        {
            return await _context.FeatureFlags
                .Where(f => !f.IsArchived)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all feature flags");
            return new List<FeatureFlag>();
        }
    }

    public async Task<List<FeatureFlag>> GetActiveAsync()
    {
        try
        {
            return await _context.FeatureFlags
                .Where(f => f.IsEnabled && !f.IsArchived)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active feature flags");
            return new List<FeatureFlag>();
        }
    }

    public async Task<List<FeatureFlag>> GetByEnvironmentAsync(string environment)
    {
        try
        {
            return await _context.FeatureFlags
                .Where(f => f.Environment == environment && !f.IsArchived)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flags by environment: {Environment}", environment);
            return new List<FeatureFlag>();
        }
    }

    public async Task<List<FeatureFlag>> GetByTagsAsync(string tags)
    {
        try
        {
            return await _context.FeatureFlags
                .Where(f => f.Tags != null && f.Tags.Contains(tags) && !f.IsArchived)
                .OrderBy(f => f.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flags by tags: {Tags}", tags);
            return new List<FeatureFlag>();
        }
    }

    public async Task<FeatureFlag> CreateAsync(FeatureFlag featureFlag)
    {
        try
        {
            _context.FeatureFlags.Add(featureFlag);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Created feature flag: {Key}", featureFlag.Key);
            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag: {Key}", featureFlag.Key);
            throw;
        }
    }

    public async Task<FeatureFlag> UpdateAsync(FeatureFlag featureFlag)
    {
        try
        {
            _context.FeatureFlags.Update(featureFlag);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated feature flag: {Key}", featureFlag.Key);
            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag: {Key}", featureFlag.Key);
            throw;
        }
    }

    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var featureFlag = await GetByIdAsync(id);
            if (featureFlag != null)
            {
                featureFlag.Archive();
                await UpdateAsync(featureFlag);
                _logger.LogInformation("Archived feature flag: {Id}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting feature flag: {Id}", id);
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetUsageStatsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var featureFlag = await GetByIdAsync(featureFlagId);
            if (featureFlag == null) return new Dictionary<string, int>();

            var usageHistory = featureFlag.UsageHistory.AsQueryable();
            
            if (from.HasValue)
                usageHistory = usageHistory.Where(u => u.Timestamp >= from.Value);
            
            if (to.HasValue)
                usageHistory = usageHistory.Where(u => u.Timestamp <= to.Value);

            return usageHistory
                .GroupBy(u => u.Variant ?? "default")
                .ToDictionary(g => g.Key, g => g.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage stats for feature flag: {Id}", featureFlagId);
            return new Dictionary<string, int>();
        }
    }

    public async Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var featureFlag = await GetByIdAsync(featureFlagId);
            if (featureFlag == null) return new List<FeatureFlagUsage>();

            var usageHistory = featureFlag.UsageHistory.AsQueryable();
            
            if (from.HasValue)
                usageHistory = usageHistory.Where(u => u.Timestamp >= from.Value);
            
            if (to.HasValue)
                usageHistory = usageHistory.Where(u => u.Timestamp <= to.Value);

            return usageHistory.OrderByDescending(u => u.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history for feature flag: {Id}", featureFlagId);
            return new List<FeatureFlagUsage>();
        }
    }

    public async Task<Dictionary<string, object>> GetAnalyticsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var usageStats = await GetUsageStatsAsync(featureFlagId, from, to);
            var usageHistory = await GetUsageHistoryAsync(featureFlagId, from, to);

            return new Dictionary<string, object>
            {
                ["totalUsage"] = usageHistory.Count,
                ["uniqueUsers"] = usageHistory.Select(u => u.UserId).Distinct().Count(),
                ["variantDistribution"] = usageStats,
                ["usageOverTime"] = usageHistory
                    .GroupBy(u => u.Timestamp.Date)
                    .ToDictionary(g => g.Key.ToString("yyyy-MM-dd"), g => g.Count())
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics for feature flag: {Id}", featureFlagId);
            return new Dictionary<string, object>();
        }
    }

    public async Task<Dictionary<string, object>> GetABTestResultsAsync(string key, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var featureFlag = await GetByKeyAsync(key);
            if (featureFlag == null) return new Dictionary<string, object>();

            return await GetAnalyticsAsync(featureFlag.Id, from, to);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test results for: {Key}", key);
            return new Dictionary<string, object>();
        }
    }

    public async Task RecordConversionAsync(string key, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null)
    {
        try
        {
            var featureFlag = await GetByKeyAsync(key);
            if (featureFlag != null)
            {
                featureFlag.RecordUsage(userId, variant, conversionData);
                await UpdateAsync(featureFlag);
                _logger.LogInformation("Recorded conversion for feature flag: {Key}, User: {UserId}", key, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording conversion for feature flag: {Key}, User: {UserId}", key, userId);
        }
    }
}
