# PowerShell script to fix all build issues in TLI Microservices
Write-Host "Fixing all build issues in TLI Microservices..." -ForegroundColor Green

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot

# Define standard package versions
$packageVersions = @{
    "Microsoft.EntityFrameworkCore" = "8.0.11"
    "Microsoft.EntityFrameworkCore.Design" = "8.0.11"
    "Microsoft.EntityFrameworkCore.Tools" = "8.0.11"
    "Microsoft.EntityFrameworkCore.Relational" = "8.0.11"
    "Microsoft.EntityFrameworkCore.SqlServer" = "8.0.11"
    "Microsoft.EntityFrameworkCore.InMemory" = "8.0.11"
    "Microsoft.Extensions.Caching.Memory" = "8.0.1"
    "StackExchange.Redis" = "2.8.16"
    "Serilog" = "4.1.0"
    "Serilog.Sinks.Console" = "6.0.0"
    "Serilog.Sinks.File" = "6.0.0"
    "Microsoft.ML" = "3.0.1"
    "Microsoft.ML.Transforms" = "3.0.1"
}

# Function to update package version in csproj file
function Update-PackageVersion {
    param(
        [string]$ProjectFile,
        [string]$PackageName,
        [string]$Version
    )
    
    if (Test-Path $ProjectFile) {
        $content = Get-Content $ProjectFile -Raw
        $pattern = "<PackageReference Include=`"$PackageName`" Version=`"[^`"]*`""
        $replacement = "<PackageReference Include=`"$PackageName`" Version=`"$Version`""
        
        if ($content -match $pattern) {
            $content = $content -replace $pattern, $replacement
            Set-Content $ProjectFile $content -NoNewline
            Write-Host "  Updated $PackageName to $Version" -ForegroundColor Yellow
        }
    }
}

# Function to add missing package reference
function Add-PackageReference {
    param(
        [string]$ProjectFile,
        [string]$PackageName,
        [string]$Version
    )
    
    if (Test-Path $ProjectFile) {
        $content = Get-Content $ProjectFile -Raw
        
        # Check if package already exists
        if ($content -notmatch "<PackageReference Include=`"$PackageName`"") {
            # Find ItemGroup with PackageReference and add to it
            if ($content -match "(<ItemGroup>[\s\S]*?<PackageReference[^>]*/>[\s\S]*?</ItemGroup>)") {
                $itemGroup = $matches[1]
                $newPackageRef = "    <PackageReference Include=`"$PackageName`" Version=`"$Version`" />`n  "
                $updatedItemGroup = $itemGroup -replace "(</ItemGroup>)", "$newPackageRef`$1"
                $content = $content -replace [regex]::Escape($itemGroup), $updatedItemGroup
                Set-Content $ProjectFile $content -NoNewline
                Write-Host "  Added $PackageName $Version" -ForegroundColor Green
            }
        }
    }
}

Write-Host "`n=== Step 1: Fixing Package Version Conflicts ===" -ForegroundColor Cyan

# Find all csproj files
$csprojFiles = Get-ChildItem -Path $solutionRoot -Recurse -Filter "*.csproj"

foreach ($file in $csprojFiles) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor White
    
    foreach ($package in $packageVersions.GetEnumerator()) {
        Update-PackageVersion -ProjectFile $file.FullName -PackageName $package.Key -Version $package.Value
    }
    
    # Add Microsoft.ML.Transforms to AuditCompliance.API if missing
    if ($file.Name -eq "AuditCompliance.API.csproj") {
        Add-PackageReference -ProjectFile $file.FullName -PackageName "Microsoft.ML.Transforms" -Version "3.0.1"
    }
}

Write-Host "`n=== Step 2: Fixing Syntax Errors ===" -ForegroundColor Cyan

# Fix TripManagement Driver.cs syntax errors
$driverFile = "$solutionRoot\Services\TripManagement\TripManagement.Domain\Entities\Driver.cs"
if (Test-Path $driverFile) {
    Write-Host "Fixing Driver.cs syntax errors..." -ForegroundColor White
    $content = Get-Content $driverFile -Raw
    
    # Fix missing closing parentheses in validation attributes
    $content = $content -replace '\[StringLength\((\d+), ErrorMessage = "[^"]*"(?!\))', '[StringLength($1, ErrorMessage = "$2")]'
    $content = $content -replace '\[Range\([^)]*(?!\))', '$&)'
    $content = $content -replace '\[RegularExpression\([^)]*(?!\))', '$&)'
    
    Set-Content $driverFile $content -NoNewline
    Write-Host "  Fixed Driver.cs syntax errors" -ForegroundColor Green
}

# Fix AnalyticsBIService Dashboard.cs syntax errors
$dashboardFile = "$solutionRoot\Services\AnalyticsBIService\AnalyticsBIService.Domain\Entities\Dashboard.cs"
if (Test-Path $dashboardFile) {
    Write-Host "Fixing Dashboard.cs syntax errors..." -ForegroundColor White
    $content = Get-Content $dashboardFile -Raw
    
    # Fix malformed class structure
    $content = $content -replace 'public\s+if\s*\([^)]*\)\s*!=\s*null[^}]*}', ''
    $content = $content -replace 'public\s+[^{]*{[^}]*if\s*\([^)]*\)[^}]*}', ''
    
    Set-Content $dashboardFile $content -NoNewline
    Write-Host "  Fixed Dashboard.cs syntax errors" -ForegroundColor Green
}

Write-Host "`n=== Step 3: Adding Missing Using Statements ===" -ForegroundColor Cyan

# Add missing using statements for common namespaces
$filesToFix = @(
    "$solutionRoot\Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Entities\FeatureFlag.cs",
    "$solutionRoot\Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Entities\UsageAnalyticsRecord.cs",
    "$solutionRoot\Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Entities\VehicleTypeMaster.cs",
    "$solutionRoot\Services\NetworkFleetManagement\NetworkFleetManagement.Domain\Events\VehicleTypeMasterEvents.cs"
)

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -match "using NetworkFleetManagement\.Domain\.Common;") {
            $content = $content -replace "using NetworkFleetManagement\.Domain\.Common;", "using Shared.Domain;"
            Set-Content $file $content -NoNewline
            Write-Host "  Fixed using statement in $(Split-Path $file -Leaf)" -ForegroundColor Green
        }
    }
}

Write-Host "`nBuild issue fixes completed!" -ForegroundColor Green
Write-Host "Run 'dotnet build' to verify fixes..." -ForegroundColor Cyan
