using AutoMapper;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Entities;
using DataStorage.Domain.ValueObjects;

namespace DataStorage.Application.Mappings;

public class DocumentMappingProfile : Profile
{
    public DocumentMappingProfile()
    {
        // Document mappings
        CreateMap<Document, DocumentDto>()
            .ForMember(dest => dest.AccessUrl, opt => opt.MapFrom(src => src.StorageLocation.GetAccessUrl()))
            .ForMember(dest => dest.ThumbnailUrl, opt => opt.Ignore()); // Set separately in handlers

        CreateMap<DataStorage.Domain.Entities.DocumentVersion, DocumentVersionDto>();
        CreateMap<DocumentAccess, DocumentAccessDto>();

        // Value object mappings
        CreateMap<FileMetadata, FileMetadataDto>();
        CreateMap<StorageLocation, StorageLocationDto>()
            .ForMember(dest => dest.FullPath, opt => opt.MapFrom(src => src.GetFullPath()))
            .ForMember(dest => dest.AccessUrl, opt => opt.MapFrom(src => src.GetAccessUrl()));

        // Reverse mappings for commands
        CreateMap<FileMetadataDto, FileMetadata>()
            .ConstructUsing(src => new FileMetadata(
                src.FileName,
                src.ContentType,
                src.FileSizeBytes,
                src.Checksum,
                src.CustomProperties));

        CreateMap<StorageLocationDto, StorageLocation>()
            .ConstructUsing(src => new StorageLocation(
                src.Provider,
                src.ContainerName,
                src.FilePath,
                src.CDNUrl,
                src.Region,
                src.ProviderSpecificSettings));
    }
}
