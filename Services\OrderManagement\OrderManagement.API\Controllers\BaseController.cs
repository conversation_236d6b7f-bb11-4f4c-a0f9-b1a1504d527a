using Microsoft.AspNetCore.Mvc;
using MediatR;
using System.Security.Claims;

namespace OrderManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public abstract class BaseController : ControllerBase
{
    private IMediator? _mediator;
    protected IMediator Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<IMediator>();

    private ILogger? _logger;
    protected ILogger Logger => _logger ??= HttpContext.RequestServices.GetRequiredService<ILogger<BaseController>>();

    protected Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        return userId;
    }

    protected string? GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value;
    }

    protected string? GetCurrentUserName()
    {
        return User.FindFirst("name")?.Value ??
               User.FindFirst("preferred_username")?.Value ??
               User.Identity?.Name ??
               "Unknown";
    }

    protected bool IsAdmin()
    {
        return User.IsInRole("Admin");
    }

    protected IActionResult HandleException(Exception ex)
    {
        // Log the exception here if needed
        return ex switch
        {
            UnauthorizedAccessException => Unauthorized(new { message = ex.Message }),
            ArgumentException => BadRequest(new { message = ex.Message }),
            InvalidOperationException => BadRequest(new { message = ex.Message }),
            KeyNotFoundException => NotFound(new { message = ex.Message }),
            _ => StatusCode(500, new { message = "An error occurred while processing your request" })
        };
    }
}
