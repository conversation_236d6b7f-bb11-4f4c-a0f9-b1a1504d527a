using Shared.Domain.Common;
using UserManagement.Domain.Enums;

namespace UserManagement.Domain.Events;

/// <summary>
/// Domain event raised when a user is flagged
/// </summary>
public class UserFlaggedEvent : DomainEvent
{
    public Guid UserId { get; }
    public Guid FlagId { get; }
    public string FlagType { get; }
    public string Reason { get; }
    public string Severity { get; }
    public Guid CreatedBy { get; }

    public UserFlaggedEvent(
        Guid userId,
        Guid flagId,
        string flagType,
        string reason,
        string severity,
        Guid createdBy)
    {
        UserId = userId;
        FlagId = flagId;
        FlagType = flagType;
        Reason = reason;
        Severity = severity;
        CreatedBy = createdBy;
    }
}
