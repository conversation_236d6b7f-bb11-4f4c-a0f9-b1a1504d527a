using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class TaxConfigurationTests
{
    private readonly TaxJurisdiction _indiaJurisdiction = new("India", "Maharashtra", "Mumbai", LocationType.City);
    private readonly string _createdBy = "test_user";

    [Fact]
    public void Constructor_ShouldCreateTaxConfiguration_WithValidParameters()
    {
        // Arrange
        var name = "India Tax Configuration";
        var description = "Default tax configuration for India";
        var effectiveFrom = DateTime.UtcNow;
        var effectiveTo = DateTime.UtcNow.AddYears(1);
        var isDefault = true;
        var priority = 1;

        // Act
        var taxConfig = new TaxConfiguration(
            name,
            description,
            _indiaJurisdiction,
            effectiveFrom,
            _createdBy,
            effectiveTo,
            isDefault,
            priority);

        // Assert
        taxConfig.Name.Should().Be(name);
        taxConfig.Description.Should().Be(description);
        taxConfig.Jurisdiction.Should().Be(_indiaJurisdiction);
        taxConfig.EffectiveFrom.Should().Be(effectiveFrom);
        taxConfig.EffectiveTo.Should().Be(effectiveTo);
        taxConfig.IsDefault.Should().Be(isDefault);
        taxConfig.Priority.Should().Be(priority);
        taxConfig.CreatedBy.Should().Be(_createdBy);
        taxConfig.Status.Should().Be(TaxConfigurationStatus.Active);
        taxConfig.EnableGstCalculation.Should().BeFalse(); // Default value
        taxConfig.EnableTdsCalculation.Should().BeFalse(); // Default value
        taxConfig.DefaultCurrency.Should().Be("INR"); // Default value
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenNameIsEmpty()
    {
        // Act & Assert
        var action = () => new TaxConfiguration(
            "",
            "Description",
            _indiaJurisdiction,
            DateTime.UtcNow,
            _createdBy);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Tax configuration name cannot be empty*");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenDescriptionIsEmpty()
    {
        // Act & Assert
        var action = () => new TaxConfiguration(
            "Name",
            "",
            _indiaJurisdiction,
            DateTime.UtcNow,
            _createdBy);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Tax configuration description cannot be empty*");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenJurisdictionIsNull()
    {
        // Act & Assert
        var action = () => new TaxConfiguration(
            "Name",
            "Description",
            null!,
            DateTime.UtcNow,
            _createdBy);

        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenCreatedByIsEmpty()
    {
        // Act & Assert
        var action = () => new TaxConfiguration(
            "Name",
            "Description",
            _indiaJurisdiction,
            DateTime.UtcNow,
            "");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Created by cannot be empty*");
    }

    [Fact]
    public void UpdateBasicDetails_ShouldUpdateProperties_WhenValidParameters()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        var newName = "Updated Tax Configuration";
        var newDescription = "Updated description";
        var newJurisdiction = new TaxJurisdiction("India", "Karnataka", "Bangalore", LocationType.City);
        var newEffectiveFrom = DateTime.UtcNow.AddDays(1);
        var newEffectiveTo = DateTime.UtcNow.AddYears(2);
        var modifiedBy = "admin_user";
        var newPriority = 5;

        // Act
        taxConfig.UpdateBasicDetails(newName, newDescription, newJurisdiction, newEffectiveFrom, newEffectiveTo, modifiedBy, newPriority);

        // Assert
        taxConfig.Name.Should().Be(newName);
        taxConfig.Description.Should().Be(newDescription);
        taxConfig.Jurisdiction.Should().Be(newJurisdiction);
        taxConfig.EffectiveFrom.Should().Be(newEffectiveFrom);
        taxConfig.EffectiveTo.Should().Be(newEffectiveTo);
        taxConfig.Priority.Should().Be(newPriority);
        taxConfig.ModifiedBy.Should().Be(modifiedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
        taxConfig.ModifiedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void UpdateTaxSettings_ShouldUpdateTaxSettings_WhenValidParameters()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        var modifiedBy = "admin_user";

        // Act
        taxConfig.UpdateTaxSettings(
            enableGstCalculation: true,
            enableTdsCalculation: true,
            enableReverseCharge: true,
            requireHsnCode: true,
            defaultGstRate: 18.0m,
            defaultTdsRate: 2.0m,
            defaultCurrency: "INR",
            modifiedBy);

        // Assert
        taxConfig.EnableGstCalculation.Should().BeTrue();
        taxConfig.EnableTdsCalculation.Should().BeTrue();
        taxConfig.EnableReverseCharge.Should().BeTrue();
        taxConfig.RequireHsnCode.Should().BeTrue();
        taxConfig.DefaultGstRate.Should().Be(18.0m);
        taxConfig.DefaultTdsRate.Should().Be(2.0m);
        taxConfig.DefaultCurrency.Should().Be("INR");
        taxConfig.ModifiedBy.Should().Be(modifiedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void UpdateTaxSettings_ShouldThrowException_WhenGstRateIsNegative()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();

        // Act & Assert
        var action = () => taxConfig.UpdateTaxSettings(
            true, true, false, false, -5.0m, 2.0m, "INR", "admin");

        action.Should().Throw<ArgumentException>()
            .WithMessage("GST rate cannot be negative*");
    }

    [Fact]
    public void UpdateTaxSettings_ShouldThrowException_WhenTdsRateIsNegative()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();

        // Act & Assert
        var action = () => taxConfig.UpdateTaxSettings(
            true, true, false, false, 18.0m, -2.0m, "INR", "admin");

        action.Should().Throw<ArgumentException>()
            .WithMessage("TDS rate cannot be negative*");
    }

    [Fact]
    public void SetAsDefault_ShouldSetIsDefaultToTrue()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        var modifiedBy = "admin_user";

        // Act
        taxConfig.SetAsDefault(modifiedBy);

        // Assert
        taxConfig.IsDefault.Should().BeTrue();
        taxConfig.ModifiedBy.Should().Be(modifiedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void RemoveAsDefault_ShouldSetIsDefaultToFalse()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        taxConfig.SetAsDefault("admin");
        var modifiedBy = "admin_user";

        // Act
        taxConfig.RemoveAsDefault(modifiedBy);

        // Assert
        taxConfig.IsDefault.Should().BeFalse();
        taxConfig.ModifiedBy.Should().Be(modifiedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void Activate_ShouldSetStatusToActive()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        taxConfig.Deactivate("admin", "Test deactivation");
        var activatedBy = "admin_user";

        // Act
        taxConfig.Activate(activatedBy);

        // Assert
        taxConfig.Status.Should().Be(TaxConfigurationStatus.Active);
        taxConfig.ModifiedBy.Should().Be(activatedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void Deactivate_ShouldSetStatusToInactive()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        var deactivatedBy = "admin_user";
        var reason = "Configuration no longer needed";

        // Act
        taxConfig.Deactivate(deactivatedBy, reason);

        // Assert
        taxConfig.Status.Should().Be(TaxConfigurationStatus.Inactive);
        taxConfig.ModifiedBy.Should().Be(deactivatedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void Archive_ShouldSetStatusToArchived()
    {
        // Arrange
        var taxConfig = CreateTaxConfiguration();
        var archivedBy = "admin_user";
        var reason = "Old configuration";

        // Act
        taxConfig.Archive(archivedBy, reason);

        // Assert
        taxConfig.Status.Should().Be(TaxConfigurationStatus.Archived);
        taxConfig.ModifiedBy.Should().Be(archivedBy);
        taxConfig.ModifiedAt.Should().NotBeNull();
    }

    [Fact]
    public void IsEffectiveOn_ShouldReturnTrue_WhenDateIsWithinEffectivePeriod()
    {
        // Arrange
        var effectiveFrom = DateTime.UtcNow.AddDays(-1);
        var effectiveTo = DateTime.UtcNow.AddDays(1);
        var taxConfig = new TaxConfiguration(
            "Test Config",
            "Test Description",
            _indiaJurisdiction,
            effectiveFrom,
            _createdBy,
            effectiveTo);

        var testDate = DateTime.UtcNow;

        // Act
        var result = taxConfig.IsEffectiveOn(testDate);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsEffectiveOn_ShouldReturnFalse_WhenDateIsBeforeEffectiveFrom()
    {
        // Arrange
        var effectiveFrom = DateTime.UtcNow.AddDays(1);
        var taxConfig = new TaxConfiguration(
            "Test Config",
            "Test Description",
            _indiaJurisdiction,
            effectiveFrom,
            _createdBy);

        var testDate = DateTime.UtcNow;

        // Act
        var result = taxConfig.IsEffectiveOn(testDate);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsEffectiveOn_ShouldReturnFalse_WhenDateIsAfterEffectiveTo()
    {
        // Arrange
        var effectiveFrom = DateTime.UtcNow.AddDays(-2);
        var effectiveTo = DateTime.UtcNow.AddDays(-1);
        var taxConfig = new TaxConfiguration(
            "Test Config",
            "Test Description",
            _indiaJurisdiction,
            effectiveFrom,
            _createdBy,
            effectiveTo);

        var testDate = DateTime.UtcNow;

        // Act
        var result = taxConfig.IsEffectiveOn(testDate);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsEffectiveOn_ShouldReturnTrue_WhenEffectiveToIsNull()
    {
        // Arrange
        var effectiveFrom = DateTime.UtcNow.AddDays(-1);
        var taxConfig = new TaxConfiguration(
            "Test Config",
            "Test Description",
            _indiaJurisdiction,
            effectiveFrom,
            _createdBy,
            null); // No end date

        var testDate = DateTime.UtcNow.AddDays(100); // Far future

        // Act
        var result = taxConfig.IsEffectiveOn(testDate);

        // Assert
        result.Should().BeTrue();
    }

    private TaxConfiguration CreateTaxConfiguration()
    {
        return new TaxConfiguration(
            "Test Tax Configuration",
            "Test description",
            _indiaJurisdiction,
            DateTime.UtcNow,
            _createdBy);
    }
}
