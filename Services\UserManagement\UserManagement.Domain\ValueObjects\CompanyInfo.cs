using Shared.Domain.ValueObjects;

namespace UserManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing company information
/// </summary>
public class CompanyInfo : ValueObject
{
    public string CompanyName { get; private set; }
    public string? CompanyType { get; private set; }
    public string? RegistrationNumber { get; private set; }
    public string? GstNumber { get; private set; }
    public string? PanNumber { get; private set; }
    public string? TaxIdentificationNumber { get; private set; }
    public DateTime? IncorporationDate { get; private set; }
    public string? Website { get; private set; }
    public string? Description { get; private set; }
    public string? EmployeeCount { get; private set; }
    public string? AnnualRevenue { get; private set; }
    public string? BusinessLicense { get; private set; }
    public string? CompanyLogoUrl { get; private set; }

    private CompanyInfo()
    {
        CompanyName = string.Empty;
    }

    public CompanyInfo(
        string companyName,
        string? companyType = null,
        string? registrationNumber = null,
        string? gstNumber = null,
        string? panNumber = null,
        string? taxIdentificationNumber = null,
        DateTime? incorporationDate = null,
        string? website = null,
        string? description = null,
        string? employeeCount = null,
        string? annualRevenue = null,
        string? businessLicense = null,
        string? companyLogoUrl = null)
    {
        if (string.IsNullOrWhiteSpace(companyName))
            throw new ArgumentException("Company name cannot be empty", nameof(companyName));

        CompanyName = companyName.Trim();
        CompanyType = companyType?.Trim();
        RegistrationNumber = registrationNumber?.Trim();
        GstNumber = gstNumber?.Trim();
        PanNumber = panNumber?.Trim();
        TaxIdentificationNumber = taxIdentificationNumber?.Trim();
        IncorporationDate = incorporationDate;
        Website = website?.Trim();
        Description = description?.Trim();
        EmployeeCount = employeeCount?.Trim();
        AnnualRevenue = annualRevenue?.Trim();
        BusinessLicense = businessLicense?.Trim();
        CompanyLogoUrl = companyLogoUrl?.Trim();
    }

    public static CompanyInfo Create(
        string companyName,
        string? companyType = null,
        string? registrationNumber = null,
        string? gstNumber = null,
        string? panNumber = null,
        string? taxIdentificationNumber = null,
        DateTime? incorporationDate = null,
        string? website = null,
        string? description = null,
        string? employeeCount = null,
        string? annualRevenue = null,
        string? businessLicense = null,
        string? companyLogoUrl = null)
    {
        return new CompanyInfo(companyName, companyType, registrationNumber, gstNumber, panNumber,
            taxIdentificationNumber, incorporationDate, website, description, employeeCount,
            annualRevenue, businessLicense, companyLogoUrl);
    }

    public CompanyInfo WithCompanyName(string companyName)
    {
        return new CompanyInfo(companyName, CompanyType, RegistrationNumber, GstNumber, PanNumber,
            TaxIdentificationNumber, IncorporationDate, Website, Description, EmployeeCount,
            AnnualRevenue, BusinessLicense, CompanyLogoUrl);
    }

    public CompanyInfo WithGstNumber(string? gstNumber)
    {
        return new CompanyInfo(CompanyName, CompanyType, RegistrationNumber, gstNumber, PanNumber,
            TaxIdentificationNumber, IncorporationDate, Website, Description, EmployeeCount,
            AnnualRevenue, BusinessLicense, CompanyLogoUrl);
    }

    public CompanyInfo WithPanNumber(string? panNumber)
    {
        return new CompanyInfo(CompanyName, CompanyType, RegistrationNumber, GstNumber, panNumber,
            TaxIdentificationNumber, IncorporationDate, Website, Description, EmployeeCount,
            AnnualRevenue, BusinessLicense, CompanyLogoUrl);
    }

    public CompanyInfo WithLogo(string? companyLogoUrl)
    {
        return new CompanyInfo(CompanyName, CompanyType, RegistrationNumber, GstNumber, PanNumber,
            TaxIdentificationNumber, IncorporationDate, Website, Description, EmployeeCount,
            AnnualRevenue, BusinessLicense, companyLogoUrl);
    }

    public bool HasTaxRegistration() => !string.IsNullOrWhiteSpace(GstNumber) || !string.IsNullOrWhiteSpace(TaxIdentificationNumber);

    public bool HasPanNumber() => !string.IsNullOrWhiteSpace(PanNumber);

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return CompanyName;
        yield return CompanyType ?? string.Empty;
        yield return RegistrationNumber ?? string.Empty;
        yield return GstNumber ?? string.Empty;
        yield return PanNumber ?? string.Empty;
        yield return TaxIdentificationNumber ?? string.Empty;
        yield return IncorporationDate ?? DateTime.MinValue;
        yield return Website ?? string.Empty;
        yield return Description ?? string.Empty;
        yield return EmployeeCount ?? string.Empty;
        yield return AnnualRevenue ?? string.Empty;
        yield return BusinessLicense ?? string.Empty;
        yield return CompanyLogoUrl ?? string.Empty;
    }
}
