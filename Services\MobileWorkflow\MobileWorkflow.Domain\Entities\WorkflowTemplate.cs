using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class WorkflowTemplate : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // OrderProcessing, TripManagement, DocumentVerification, etc.
    public string Industry { get; private set; } // Logistics, Healthcare, Finance, etc.
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsPublic { get; private set; }
    public Dictionary<string, object> TemplateDefinition { get; private set; }
    public Dictionary<string, object> DefaultConfiguration { get; private set; }
    public Dictionary<string, object> ParameterSchema { get; private set; }
    public Dictionary<string, object> ValidationRules { get; private set; }
    public List<string> Tags { get; private set; }
    public List<string> RequiredRoles { get; private set; }
    public Dictionary<string, object> Prerequisites { get; private set; }
    public int UsageCount { get; private set; }
    public double AverageRating { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<Workflow> CreatedWorkflows { get; private set; } = new List<Workflow>();
    public virtual ICollection<WorkflowTemplateRating> Ratings { get; private set; } = new List<WorkflowTemplateRating>();

    private WorkflowTemplate() { } // EF Core

    public WorkflowTemplate(
        string name,
        string displayName,
        string description,
        string category,
        string industry,
        string version,
        Dictionary<string, object> templateDefinition,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Industry = industry ?? throw new ArgumentNullException(nameof(industry));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        TemplateDefinition = templateDefinition ?? throw new ArgumentNullException(nameof(templateDefinition));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsPublic = false;
        CreatedAt = DateTime.UtcNow;
        DefaultConfiguration = new Dictionary<string, object>();
        ParameterSchema = new Dictionary<string, object>();
        ValidationRules = new Dictionary<string, object>();
        Tags = new List<string>();
        RequiredRoles = new List<string>();
        Prerequisites = new Dictionary<string, object>();
        UsageCount = 0;
        AverageRating = 0.0;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new WorkflowTemplateCreatedEvent(Id, Name, Category, Industry, CreatedBy));
    }

    public void UpdateDefinition(Dictionary<string, object> newDefinition, string updatedBy)
    {
        TemplateDefinition = newDefinition ?? throw new ArgumentNullException(nameof(newDefinition));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateDefinitionUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> newConfiguration, string updatedBy)
    {
        DefaultConfiguration = newConfiguration ?? throw new ArgumentNullException(nameof(newConfiguration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateParameterSchema(Dictionary<string, object> schema, string updatedBy)
    {
        ParameterSchema = schema ?? throw new ArgumentNullException(nameof(schema));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateParameterSchemaUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateValidationRules(Dictionary<string, object> rules, string updatedBy)
    {
        ValidationRules = rules ?? throw new ArgumentNullException(nameof(rules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateValidationRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void AddTag(string tag)
    {
        if (!Tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
        {
            Tags.Add(tag);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new WorkflowTemplateTagAddedEvent(Id, Name, tag));
        }
    }

    public void RemoveTag(string tag)
    {
        if (Tags.RemoveAll(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new WorkflowTemplateTagRemovedEvent(Id, Name, tag));
        }
    }

    public void AddRequiredRole(string role)
    {
        if (!RequiredRoles.Contains(role, StringComparer.OrdinalIgnoreCase))
        {
            RequiredRoles.Add(role);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new WorkflowTemplateRequiredRoleAddedEvent(Id, Name, role));
        }
    }

    public void RemoveRequiredRole(string role)
    {
        if (RequiredRoles.RemoveAll(r => r.Equals(role, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new WorkflowTemplateRequiredRoleRemovedEvent(Id, Name, role));
        }
    }

    public void SetPrerequisite(string key, object value)
    {
        Prerequisites[key] = value;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplatePrerequisiteSetEvent(Id, Name, key));
    }

    public void RemovePrerequisite(string key)
    {
        if (Prerequisites.Remove(key))
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new WorkflowTemplatePrerequisiteRemovedEvent(Id, Name, key));
        }
    }

    public void MakePublic()
    {
        IsPublic = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateMadePublicEvent(Id, Name));
    }

    public void MakePrivate()
    {
        IsPublic = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateMadePrivateEvent(Id, Name));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateDeactivatedEvent(Id, Name));
    }

    public void IncrementUsage()
    {
        UsageCount++;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateUsageIncrementedEvent(Id, Name, UsageCount));
    }

    public void UpdateRating(double newRating)
    {
        AverageRating = newRating;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateRatingUpdatedEvent(Id, Name, newRating));
    }

    public void UpdateVersion(string newVersion, string updatedBy)
    {
        var oldVersion = Version;
        Version = newVersion ?? throw new ArgumentNullException(nameof(newVersion));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTemplateVersionUpdatedEvent(Id, Name, oldVersion, newVersion, UpdatedBy));
    }

    public bool HasTag(string tag)
    {
        return Tags.Contains(tag, StringComparer.OrdinalIgnoreCase);
    }

    public bool RequiresRole(string role)
    {
        return RequiredRoles.Contains(role, StringComparer.OrdinalIgnoreCase);
    }

    public bool CanBeUsedBy(List<string> userRoles)
    {
        if (RequiredRoles.Count == 0)
        {
            return true;
        }

        return RequiredRoles.Any(role => userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
    }

    public Dictionary<string, object> GetParameterDefaults()
    {
        var defaults = new Dictionary<string, object>();

        if (ParameterSchema.TryGetValue("properties", out var properties) && properties is Dictionary<string, object> props)
        {
            foreach (var prop in props)
            {
                if (prop.Value is Dictionary<string, object> propDef && propDef.TryGetValue("default", out var defaultValue))
                {
                    defaults[prop.Key] = defaultValue;
                }
            }
        }

        return defaults;
    }

    public List<string> ValidateParameters(Dictionary<string, object> parameters)
    {
        var errors = new List<string>();

        // Check required parameters
        if (ParameterSchema.TryGetValue("required", out var required) && required is List<string> requiredParams)
        {
            foreach (var param in requiredParams)
            {
                if (!parameters.ContainsKey(param))
                {
                    errors.Add($"Required parameter '{param}' is missing");
                }
            }
        }

        // Validate parameter types and constraints
        if (ParameterSchema.TryGetValue("properties", out var properties) && properties is Dictionary<string, object> props)
        {
            foreach (var param in parameters)
            {
                if (props.TryGetValue(param.Key, out var propDef) && propDef is Dictionary<string, object> definition)
                {
                    var validationErrors = ValidateParameter(param.Key, param.Value, definition);
                    errors.AddRange(validationErrors);
                }
            }
        }

        return errors;
    }

    private List<string> ValidateParameter(string paramName, object value, Dictionary<string, object> definition)
    {
        var errors = new List<string>();

        // Type validation
        if (definition.TryGetValue("type", out var expectedType) && expectedType is string typeStr)
        {
            if (!IsValidType(value, typeStr))
            {
                errors.Add($"Parameter '{paramName}' must be of type {typeStr}");
            }
        }

        // Additional validations can be added here (min/max values, patterns, etc.)

        return errors;
    }

    private bool IsValidType(object value, string expectedType)
    {
        return expectedType.ToLowerInvariant() switch
        {
            "string" => value is string,
            "number" => value is int or long or float or double or decimal,
            "boolean" => value is bool,
            "array" => value is Array or System.Collections.IList,
            "object" => value is Dictionary<string, object>,
            _ => true
        };
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


