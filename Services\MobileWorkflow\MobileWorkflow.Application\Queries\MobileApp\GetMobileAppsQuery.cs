using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.MobileApp;

public class GetMobileAppsQuery : IRequest<IEnumerable<MobileAppDto>>
{
    public string? Platform { get; set; }
    public bool? IsActive { get; set; }
    public bool? SupportsOffline { get; set; }
}

public class GetMobileAppByIdQuery : IRequest<MobileAppDto?>
{
    public Guid Id { get; set; }

    public GetMobileAppByIdQuery(Guid id)
    {
        Id = id;
    }
}

public class GetMobileAppByPackageIdQuery : IRequest<MobileAppDto?>
{
    public string PackageId { get; set; }

    public GetMobileAppByPackageIdQuery(string packageId)
    {
        PackageId = packageId;
    }
}
