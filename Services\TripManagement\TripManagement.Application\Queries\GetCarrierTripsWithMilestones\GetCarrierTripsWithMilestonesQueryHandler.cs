using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TripManagement.Application.DTOs;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Application.Queries.GetCarrierTripsWithMilestones;

public class GetCarrierTripsWithMilestonesQueryHandler : IRequestHandler<GetCarrierTripsWithMilestonesQuery, PagedResult<TripDashboardDto>>
{
    private readonly TripManagementDbContext _context;
    private readonly IMapper _mapper;

    public GetCarrierTripsWithMilestonesQueryHandler(TripManagementDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PagedResult<TripDashboardDto>> Handle(GetCarrierTripsWithMilestonesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
                .ThenInclude(s => s.ProofOfDeliveries)
            .Include(t => t.LocationUpdates)
            .Include(t => t.Exceptions)
            .Include(t => t.Documents)
            .Where(t => t.CarrierId == request.CarrierId);

        // Apply filters
        if (request.Status.HasValue)
        {
            query = query.Where(t => t.Status == request.Status.Value);
        }

        if (request.FromDate.HasValue)
        {
            query = query.Where(t => t.CreatedAt >= request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            query = query.Where(t => t.CreatedAt <= request.ToDate.Value);
        }

        if (request.IsDelayed.HasValue)
        {
            var now = DateTime.UtcNow;
            if (request.IsDelayed.Value)
            {
                query = query.Where(t => 
                    (t.Status == Domain.Enums.TripStatus.InProgress && t.EstimatedEndTime < now) ||
                    (t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue && t.CompletedAt.Value > t.EstimatedEndTime));
            }
            else
            {
                query = query.Where(t => 
                    !(t.Status == Domain.Enums.TripStatus.InProgress && t.EstimatedEndTime < now) &&
                    !(t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue && t.CompletedAt.Value > t.EstimatedEndTime));
            }
        }

        if (request.HasExceptions.HasValue)
        {
            if (request.HasExceptions.Value)
            {
                query = query.Where(t => t.Exceptions.Any());
            }
            else
            {
                query = query.Where(t => !t.Exceptions.Any());
            }
        }

        if (request.IsCompleted.HasValue)
        {
            if (request.IsCompleted.Value)
            {
                query = query.Where(t => t.Status == Domain.Enums.TripStatus.Completed);
            }
            else
            {
                query = query.Where(t => t.Status != Domain.Enums.TripStatus.Completed);
            }
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(t => 
                t.TripNumber.ToLower().Contains(searchTerm) ||
                (t.Driver != null && (t.Driver.FirstName + " " + t.Driver.LastName).ToLower().Contains(searchTerm)) ||
                (t.Vehicle != null && t.Vehicle.RegistrationNumber.ToLower().Contains(searchTerm)));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and get results
        var trips = await query
            .OrderByDescending(t => t.CreatedAt)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs with enhanced information
        var tripDashboardDtos = trips.Select(trip =>
        {
            var now = DateTime.UtcNow;
            
            // Calculate status information
            var statusDisplayName = GetStatusDisplayName(trip.Status);
            var statusColor = GetStatusColor(trip.Status);
            
            // Calculate delay information
            var isDelayed = (trip.Status == Domain.Enums.TripStatus.InProgress && trip.EstimatedEndTime < now) ||
                           (trip.Status == Domain.Enums.TripStatus.Completed && trip.CompletedAt.HasValue && trip.CompletedAt.Value > trip.EstimatedEndTime);
            var isOnTime = !isDelayed;
            var delayDuration = isDelayed && trip.CompletedAt.HasValue 
                ? trip.CompletedAt.Value - trip.EstimatedEndTime 
                : isDelayed && trip.Status == Domain.Enums.TripStatus.InProgress 
                    ? now - trip.EstimatedEndTime 
                    : null;

            // Calculate duration
            var estimatedDuration = trip.EstimatedEndTime - trip.EstimatedStartTime;
            var actualDuration = trip.CompletedAt.HasValue && trip.StartedAt.HasValue 
                ? trip.CompletedAt.Value - trip.StartedAt.Value 
                : null;

            // Calculate progress (simplified - based on completed stops)
            var completedStops = trip.Stops.Count(s => s.Status == Domain.Enums.TripStopStatus.Completed);
            var totalStops = trip.Stops.Count;
            var progressPercentage = totalStops > 0 ? (decimal)completedStops / totalStops * 100 : 0;

            // Create milestone DTOs (placeholder - would integrate with MobileWorkflow service)
            var milestones = CreateMockMilestones(trip);
            var completedMilestones = milestones.Count(m => m.IsCompleted);
            var totalMilestones = milestones.Count;
            var milestoneCompletionRate = totalMilestones > 0 ? (decimal)completedMilestones / totalMilestones * 100 : 0;

            // Create stop summaries
            var stopSummaries = trip.Stops.Select(stop => new TripStopSummaryDto
            {
                Id = stop.Id,
                StopType = stop.StopType,
                SequenceNumber = stop.SequenceNumber,
                LocationName = stop.Location?.Address ?? $"Stop {stop.SequenceNumber}",
                Status = stop.Status,
                StatusDisplayName = GetStopStatusDisplayName(stop.Status),
                StatusColor = GetStopStatusColor(stop.Status),
                ScheduledArrival = stop.ScheduledArrival,
                ActualArrival = stop.ActualArrival,
                IsDelayed = stop.IsDelayed(),
                DelayDuration = stop.IsDelayed() && stop.ActualArrival.HasValue && stop.ScheduledArrival.HasValue 
                    ? stop.ActualArrival.Value - stop.ScheduledArrival.Value 
                    : null,
                HasProofOfDelivery = stop.ProofOfDeliveries.Any(),
                ProofOfDeliveryCount = stop.ProofOfDeliveries.Count
            }).OrderBy(s => s.SequenceNumber).ToList();

            // Create POD summaries
            var podSummaries = trip.Stops
                .SelectMany(stop => stop.ProofOfDeliveries.Select(pod => new ProofOfDeliverySummaryDto
                {
                    Id = pod.Id,
                    RecipientName = pod.RecipientName,
                    DeliveredAt = pod.DeliveredAt,
                    HasSignature = !string.IsNullOrEmpty(pod.RecipientSignature),
                    HasPhoto = !string.IsNullOrEmpty(pod.PhotoUrl),
                    IsDigitalSignature = pod.IsDigitalSignature,
                    DeliveredBy = pod.DeliveredBy,
                    StopId = stop.Id,
                    StopLocationName = stop.Location?.Address ?? $"Stop {stop.SequenceNumber}"
                }))
                .OrderByDescending(p => p.DeliveredAt)
                .ToList();

            // Create exception summaries
            var exceptionSummaries = trip.Exceptions.Select(ex => new TripExceptionSummaryDto
            {
                Id = ex.Id,
                ExceptionType = ex.ExceptionType,
                ExceptionTypeName = ex.ExceptionType.ToString(),
                Description = ex.Description,
                ReportedAt = ex.ReportedAt,
                IsResolved = ex.IsResolved,
                Status = ex.IsResolved ? "Resolved" : "Active",
                StatusColor = ex.IsResolved ? "Green" : "Red",
                Priority = GetExceptionPriority(ex.ExceptionType)
            }).OrderByDescending(e => e.ReportedAt).ToList();

            // Get current location (latest location update)
            var currentLocation = trip.LocationUpdates
                .OrderByDescending(lu => lu.Timestamp)
                .FirstOrDefault()?.Location;

            return new TripDashboardDto
            {
                Id = trip.Id,
                OrderId = trip.OrderId,
                TripNumber = trip.TripNumber,
                CarrierId = trip.CarrierId,
                DriverId = trip.DriverId,
                DriverName = trip.Driver?.FullName,
                VehicleId = trip.VehicleId,
                VehicleRegistration = trip.Vehicle?.RegistrationNumber,
                VehicleDisplayName = trip.Vehicle?.DisplayName,
                Status = trip.Status,
                StatusDisplayName = statusDisplayName,
                StatusColor = statusColor,
                
                // Timeline
                CreatedAt = trip.CreatedAt,
                AssignedAt = trip.AssignedAt,
                StartedAt = trip.StartedAt,
                CompletedAt = trip.CompletedAt,
                EstimatedStartTime = trip.EstimatedStartTime,
                EstimatedEndTime = trip.EstimatedEndTime,
                
                // Performance
                IsDelayed = isDelayed,
                IsOnTime = isOnTime,
                DelayDuration = delayDuration,
                EstimatedDistanceKm = trip.EstimatedDistanceKm,
                ActualDistanceKm = trip.ActualDistanceKm,
                EstimatedDuration = estimatedDuration,
                ActualDuration = actualDuration,
                
                // Route and Location
                Route = _mapper.Map<RouteDto>(trip.Route),
                CurrentLocation = currentLocation != null ? _mapper.Map<LocationDto>(currentLocation) : null,
                ProgressPercentage = progressPercentage,
                
                // Milestones
                Milestones = milestones,
                CompletedMilestones = completedMilestones,
                TotalMilestones = totalMilestones,
                MilestoneCompletionRate = milestoneCompletionRate,
                
                // Stops
                Stops = stopSummaries,
                CompletedStops = completedStops,
                TotalStops = totalStops,
                
                // POD
                HasProofOfDelivery = podSummaries.Any(),
                ProofOfDeliveryCount = podSummaries.Count,
                ProofOfDeliveries = podSummaries,
                
                // Exceptions
                HasExceptions = exceptionSummaries.Any(),
                ActiveExceptions = exceptionSummaries.Count(e => !e.IsResolved),
                ResolvedExceptions = exceptionSummaries.Count(e => e.IsResolved),
                RecentExceptions = exceptionSummaries.Take(5).ToList(),
                
                // Additional
                IsUrgent = trip.IsUrgent,
                SpecialInstructions = trip.SpecialInstructions,
                Notes = trip.Notes,
                EstimatedRevenue = null, // Would be calculated based on order information
                ActualRevenue = null // Would be calculated based on completed trip
            };
        }).ToList();

        return new PagedResult<TripDashboardDto>
        {
            Items = tripDashboardDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    private static string GetStatusDisplayName(Domain.Enums.TripStatus status)
    {
        return status switch
        {
            Domain.Enums.TripStatus.Created => "Created",
            Domain.Enums.TripStatus.Assigned => "Assigned",
            Domain.Enums.TripStatus.InProgress => "In Progress",
            Domain.Enums.TripStatus.Completed => "Completed",
            Domain.Enums.TripStatus.Cancelled => "Cancelled",
            Domain.Enums.TripStatus.OnHold => "On Hold",
            Domain.Enums.TripStatus.Exception => "Exception",
            _ => status.ToString()
        };
    }

    private static string GetStatusColor(Domain.Enums.TripStatus status)
    {
        return status switch
        {
            Domain.Enums.TripStatus.Created => "Blue",
            Domain.Enums.TripStatus.Assigned => "Orange",
            Domain.Enums.TripStatus.InProgress => "Blue",
            Domain.Enums.TripStatus.Completed => "Green",
            Domain.Enums.TripStatus.Cancelled => "Red",
            Domain.Enums.TripStatus.OnHold => "Orange",
            Domain.Enums.TripStatus.Exception => "Red",
            _ => "Gray"
        };
    }

    private static string GetStopStatusDisplayName(Domain.Enums.TripStopStatus status)
    {
        return status switch
        {
            Domain.Enums.TripStopStatus.Pending => "Pending",
            Domain.Enums.TripStopStatus.Arrived => "Arrived",
            Domain.Enums.TripStopStatus.InProgress => "In Progress",
            Domain.Enums.TripStopStatus.Completed => "Completed",
            Domain.Enums.TripStopStatus.Skipped => "Skipped",
            Domain.Enums.TripStopStatus.Failed => "Failed",
            _ => status.ToString()
        };
    }

    private static string GetStopStatusColor(Domain.Enums.TripStopStatus status)
    {
        return status switch
        {
            Domain.Enums.TripStopStatus.Pending => "Gray",
            Domain.Enums.TripStopStatus.Arrived => "Orange",
            Domain.Enums.TripStopStatus.InProgress => "Blue",
            Domain.Enums.TripStopStatus.Completed => "Green",
            Domain.Enums.TripStopStatus.Skipped => "Orange",
            Domain.Enums.TripStopStatus.Failed => "Red",
            _ => "Gray"
        };
    }

    private static string GetExceptionPriority(Domain.Enums.ExceptionType exceptionType)
    {
        return exceptionType switch
        {
            Domain.Enums.ExceptionType.Accident => "High",
            Domain.Enums.ExceptionType.Breakdown => "High",
            Domain.Enums.ExceptionType.LoadDamage => "High",
            Domain.Enums.ExceptionType.Delay => "Medium",
            Domain.Enums.ExceptionType.TrafficDelay => "Medium",
            Domain.Enums.ExceptionType.WeatherDelay => "Medium",
            Domain.Enums.ExceptionType.CustomerUnavailable => "Medium",
            Domain.Enums.ExceptionType.RouteDeviation => "Low",
            Domain.Enums.ExceptionType.FuelIssue => "Medium",
            Domain.Enums.ExceptionType.DriverIssue => "Medium",
            _ => "Low"
        };
    }

    private static List<TripMilestoneDto> CreateMockMilestones(Domain.Entities.Trip trip)
    {
        // This is a placeholder implementation
        // In a real implementation, this would integrate with the MobileWorkflow service
        var milestones = new List<TripMilestoneDto>();

        if (trip.Status != Domain.Enums.TripStatus.Created)
        {
            milestones.Add(new TripMilestoneDto
            {
                Id = Guid.NewGuid(),
                Name = "Trip Assigned",
                Description = "Trip has been assigned to driver and vehicle",
                SequenceNumber = 1,
                IsCompleted = trip.AssignedAt.HasValue,
                CompletedAt = trip.AssignedAt,
                Status = trip.AssignedAt.HasValue ? "Completed" : "Pending",
                StatusColor = trip.AssignedAt.HasValue ? "Green" : "Gray",
                PayoutPercentage = 10
            });
        }

        if (trip.Status == Domain.Enums.TripStatus.InProgress || trip.Status == Domain.Enums.TripStatus.Completed)
        {
            milestones.Add(new TripMilestoneDto
            {
                Id = Guid.NewGuid(),
                Name = "Trip Started",
                Description = "Trip has been started by the driver",
                SequenceNumber = 2,
                IsCompleted = trip.StartedAt.HasValue,
                CompletedAt = trip.StartedAt,
                Status = trip.StartedAt.HasValue ? "Completed" : "Pending",
                StatusColor = trip.StartedAt.HasValue ? "Green" : "Gray",
                PayoutPercentage = 20
            });
        }

        if (trip.Status == Domain.Enums.TripStatus.Completed)
        {
            milestones.Add(new TripMilestoneDto
            {
                Id = Guid.NewGuid(),
                Name = "Trip Completed",
                Description = "Trip has been completed successfully",
                SequenceNumber = 3,
                IsCompleted = trip.CompletedAt.HasValue,
                CompletedAt = trip.CompletedAt,
                Status = trip.CompletedAt.HasValue ? "Completed" : "Pending",
                StatusColor = trip.CompletedAt.HasValue ? "Green" : "Gray",
                PayoutPercentage = 70
            });
        }

        return milestones.OrderBy(m => m.SequenceNumber).ToList();
    }
}
