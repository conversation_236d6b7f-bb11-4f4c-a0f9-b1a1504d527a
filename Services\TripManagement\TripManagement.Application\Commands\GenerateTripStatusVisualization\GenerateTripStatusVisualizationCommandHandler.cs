using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Commands.GenerateTripStatusVisualization;

public class GenerateTripStatusVisualizationCommandHandler : IRequestHandler<GenerateTripStatusVisualizationCommand, TripStatusVisualizationDto>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMilestoneRepository _milestoneRepository;
    private readonly INotificationService _notificationService;
    private readonly ILocationTrackingService _locationTrackingService;
    private readonly IDelayDetectionService _delayDetectionService;
    private readonly ITripMetricsService _metricsService;
    private readonly ILogger<GenerateTripStatusVisualizationCommandHandler> _logger;

    public GenerateTripStatusVisualizationCommandHandler(
        ITripRepository tripRepository,
        IMilestoneRepository milestoneRepository,
        INotificationService notificationService,
        ILocationTrackingService locationTrackingService,
        IDelayDetectionService delayDetectionService,
        ITripMetricsService metricsService,
        ILogger<GenerateTripStatusVisualizationCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _milestoneRepository = milestoneRepository;
        _notificationService = notificationService;
        _locationTrackingService = locationTrackingService;
        _delayDetectionService = delayDetectionService;
        _metricsService = metricsService;
        _logger = logger;
    }

    public async Task<TripStatusVisualizationDto> Handle(GenerateTripStatusVisualizationCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating trip status visualization for trip {TripId} by user {UserId}", 
            request.TripId, request.RequestingUserId);

        try
        {
            // Get trip details
            var trip = await _tripRepository.GetByIdWithDetailsAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Validate user access
            await ValidateUserAccess(trip, request.RequestingUserId, cancellationToken);

            // Build visualization
            var visualization = new TripStatusVisualizationDto
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                Status = trip.Status,
                CreatedAt = trip.CreatedAt,
                EstimatedCompletionDate = trip.EstimatedCompletionDate,
                ActualCompletionDate = trip.ActualCompletionDate,
                OverallProgress = CalculateOverallProgress(trip),
                LastUpdated = DateTime.UtcNow
            };

            // Generate timeline if requested
            if (request.IncludeTimeline)
            {
                visualization.Timeline = await GenerateTimelineVisualization(trip, request.DetailLevel, cancellationToken);
            }

            // Generate milestones if requested
            if (request.IncludeMilestones)
            {
                visualization.Milestones = await GenerateMilestoneVisualizations(trip, request.DetailLevel, cancellationToken);
            }

            // Generate trip legs visualization
            visualization.Legs = await GenerateTripLegVisualizations(trip, request.DetailLevel, cancellationToken);

            // Get active notifications if requested
            if (request.IncludeNotifications)
            {
                visualization.ActiveNotifications = await GetActiveNotifications(trip.Id, cancellationToken);
            }

            // Generate delay alerts if requested
            if (request.IncludeDelayAlerts)
            {
                visualization.DelayAlerts = await GenerateDelayAlerts(trip, cancellationToken);
            }

            // Calculate metrics if requested
            if (request.IncludeMetrics)
            {
                visualization.Metrics = await GenerateTripMetrics(trip, cancellationToken);
            }

            // Get current location if requested
            if (request.IncludeLocationTracking && request.IncludeRealTimeData)
            {
                visualization.CurrentLocation = await GetCurrentLocationTracking(trip.Id, cancellationToken);
            }

            // Get exceptions if requested
            if (request.IncludeExceptions)
            {
                visualization.Exceptions = await GenerateExceptionVisualizations(trip, cancellationToken);
            }

            // Determine critical issues and next actions
            visualization.HasCriticalIssues = HasCriticalIssues(visualization);
            visualization.NextAction = DetermineNextAction(visualization);

            _logger.LogInformation("Successfully generated trip status visualization for trip {TripId}", request.TripId);

            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating trip status visualization for trip {TripId}", request.TripId);
            throw;
        }
    }

    private async Task ValidateUserAccess(Domain.Entities.Trip trip, Guid userId, CancellationToken cancellationToken)
    {
        // Validate user has access to view this trip
        // This would typically check user roles and permissions
        // For now, we'll assume access is granted
    }

    private decimal CalculateOverallProgress(Domain.Entities.Trip trip)
    {
        if (!trip.Milestones.Any())
            return 0;

        var completedMilestones = trip.Milestones.Count(m => m.Status == Domain.Enums.MilestoneStatus.Completed);
        return (decimal)completedMilestones / trip.Milestones.Count * 100;
    }

    private async Task<TimelineVisualizationDto> GenerateTimelineVisualization(
        Domain.Entities.Trip trip, 
        VisualizationDetailLevel detailLevel, 
        CancellationToken cancellationToken)
    {
        var timeline = new TimelineVisualizationDto
        {
            StartDate = trip.ScheduledStartDate,
            EndDate = trip.ScheduledEndDate,
            TotalDays = (trip.ScheduledEndDate - trip.ScheduledStartDate).Days,
            ElapsedDays = (DateTime.UtcNow - trip.ScheduledStartDate).Days,
            TimelineProgress = CalculateTimelineProgress(trip)
        };

        timeline.RemainingDays = timeline.TotalDays - timeline.ElapsedDays;
        timeline.IsOnSchedule = await _delayDetectionService.IsTripOnScheduleAsync(trip.Id, cancellationToken);
        
        if (!timeline.IsOnSchedule)
        {
            timeline.DelayDuration = await _delayDetectionService.GetTripDelayDurationAsync(trip.Id, cancellationToken);
            timeline.TimelineStatus = DetermineTimelineStatus(timeline.DelayDuration);
        }
        else
        {
            timeline.TimelineStatus = "OnTime";
        }

        // Generate timeline events
        timeline.Events = await GenerateTimelineEvents(trip, detailLevel, cancellationToken);
        timeline.Milestones = await GenerateTimelineMilestones(trip, cancellationToken);

        return timeline;
    }

    private async Task<List<MilestoneVisualizationDto>> GenerateMilestoneVisualizations(
        Domain.Entities.Trip trip, 
        VisualizationDetailLevel detailLevel, 
        CancellationToken cancellationToken)
    {
        var milestones = new List<MilestoneVisualizationDto>();

        foreach (var milestone in trip.Milestones.OrderBy(m => m.ScheduledDateTime))
        {
            var visualization = new MilestoneVisualizationDto
            {
                MilestoneId = milestone.Id,
                Name = milestone.Name,
                Description = milestone.Description,
                Type = milestone.Type.ToString(),
                ScheduledDateTime = milestone.ScheduledDateTime,
                ActualDateTime = milestone.ActualDateTime,
                Status = milestone.Status.ToString(),
                Progress = CalculateMilestoneProgress(milestone),
                Location = MapToLocationDto(milestone.Location),
                IsOverdue = milestone.IsOverdue(),
                Style = GetMilestoneVisualizationStyle(milestone)
            };

            if (milestone.IsOverdue())
            {
                visualization.DelayDuration = DateTime.UtcNow - milestone.ScheduledDateTime;
                visualization.DelayReason = await GetDelayReason(milestone.Id, cancellationToken);
            }

            // Add requirements if detailed level
            if (detailLevel >= VisualizationDetailLevel.Detailed)
            {
                visualization.Requirements = await GetMilestoneRequirements(milestone.Id, cancellationToken);
                visualization.Documents = await GetMilestoneDocuments(milestone.Id, cancellationToken);
            }

            // Add notification settings
            visualization.NotificationSettings = await GetMilestoneNotificationSettings(milestone.Id, cancellationToken);

            // Determine available actions
            visualization.AvailableActions = DetermineMilestoneActions(milestone);

            milestones.Add(visualization);
        }

        return milestones;
    }

    private async Task<List<TripLegVisualizationDto>> GenerateTripLegVisualizations(
        Domain.Entities.Trip trip, 
        VisualizationDetailLevel detailLevel, 
        CancellationToken cancellationToken)
    {
        var legs = new List<TripLegVisualizationDto>();

        foreach (var leg in trip.TripLegs.OrderBy(l => l.SequenceNumber))
        {
            var legVisualization = new TripLegVisualizationDto
            {
                LegId = leg.Id,
                SequenceNumber = leg.SequenceNumber,
                LegName = $"Leg {leg.SequenceNumber}",
                OriginLocation = MapToLocationDto(leg.OriginLocation),
                DestinationLocation = MapToLocationDto(leg.DestinationLocation),
                Status = leg.Status.ToString(),
                Progress = CalculateLegProgress(leg),
                ScheduledStartTime = leg.ScheduledStartTime,
                ScheduledEndTime = leg.ScheduledEndTime,
                ActualStartTime = leg.ActualStartTime,
                ActualEndTime = leg.ActualEndTime,
                EstimatedDistance = leg.EstimatedDistance,
                ActualDistance = leg.ActualDistance,
                EstimatedDuration = leg.EstimatedDuration,
                ActualDuration = leg.ActualDuration
            };

            // Add assigned resources
            if (leg.AssignedVehicleId.HasValue)
            {
                legVisualization.AssignedVehicle = await GetVehicleInfo(leg.AssignedVehicleId.Value, cancellationToken);
            }

            if (leg.AssignedDriverId.HasValue)
            {
                legVisualization.AssignedDriver = await GetDriverInfo(leg.AssignedDriverId.Value, cancellationToken);
            }

            // Add leg milestones
            legVisualization.LegMilestones = await GetLegMilestones(leg.Id, cancellationToken);

            // Add route visualization
            legVisualization.Route = await GenerateRouteVisualization(leg, cancellationToken);

            // Check for issues
            legVisualization.HasIssues = await HasLegIssues(leg.Id, cancellationToken);
            if (legVisualization.HasIssues)
            {
                legVisualization.Issues = await GetLegIssues(leg.Id, cancellationToken);
            }

            legs.Add(legVisualization);
        }

        return legs;
    }

    private async Task<List<NotificationDto>> GetActiveNotifications(Guid tripId, CancellationToken cancellationToken)
    {
        return await _notificationService.GetActiveTripNotificationsAsync(tripId, cancellationToken);
    }

    private async Task<List<DelayAlertDto>> GenerateDelayAlerts(Domain.Entities.Trip trip, CancellationToken cancellationToken)
    {
        return await _delayDetectionService.GenerateDelayAlertsAsync(trip.Id, cancellationToken);
    }

    private async Task<TripMetricsDto> GenerateTripMetrics(Domain.Entities.Trip trip, CancellationToken cancellationToken)
    {
        return await _metricsService.CalculateTripMetricsAsync(trip.Id, cancellationToken);
    }

    private async Task<LocationTrackingDto> GetCurrentLocationTracking(Guid tripId, CancellationToken cancellationToken)
    {
        return await _locationTrackingService.GetCurrentLocationAsync(tripId, cancellationToken);
    }

    private async Task<List<ExceptionVisualizationDto>> GenerateExceptionVisualizations(Domain.Entities.Trip trip, CancellationToken cancellationToken)
    {
        var exceptions = new List<ExceptionVisualizationDto>();
        
        foreach (var exception in trip.Exceptions.Where(e => e.Status != Domain.Enums.ExceptionStatus.Resolved))
        {
            exceptions.Add(new ExceptionVisualizationDto
            {
                ExceptionId = exception.Id,
                Type = exception.Type.ToString(),
                Title = exception.Title,
                Description = exception.Description,
                OccurredAt = exception.OccurredAt,
                Severity = exception.Severity.ToString(),
                Status = exception.Status.ToString(),
                Resolution = exception.Resolution,
                ResolvedAt = exception.ResolvedAt,
                ResolvedBy = exception.ResolvedBy,
                RequiresEscalation = exception.RequiresEscalation,
                Style = GetExceptionVisualizationStyle(exception)
            });
        }

        return exceptions;
    }

    // Helper methods
    private decimal CalculateTimelineProgress(Domain.Entities.Trip trip)
    {
        var totalDuration = trip.ScheduledEndDate - trip.ScheduledStartDate;
        var elapsed = DateTime.UtcNow - trip.ScheduledStartDate;
        
        if (totalDuration.TotalDays <= 0) return 100;
        
        var progress = (decimal)(elapsed.TotalDays / totalDuration.TotalDays) * 100;
        return Math.Max(0, Math.Min(100, progress));
    }

    private string DetermineTimelineStatus(TimeSpan? delayDuration)
    {
        if (!delayDuration.HasValue) return "OnTime";
        
        return delayDuration.Value.TotalHours switch
        {
            <= 2 => "MinorDelay",
            <= 24 => "Delayed",
            _ => "Critical"
        };
    }

    private decimal CalculateMilestoneProgress(Domain.Entities.Milestone milestone)
    {
        return milestone.Status switch
        {
            Domain.Enums.MilestoneStatus.Completed => 100,
            Domain.Enums.MilestoneStatus.InProgress => 50,
            _ => 0
        };
    }

    private decimal CalculateLegProgress(Domain.Entities.TripLeg leg)
    {
        return leg.Status switch
        {
            Domain.Enums.TripLegStatus.Completed => 100,
            Domain.Enums.TripLegStatus.InProgress => 50,
            _ => 0
        };
    }

    private LocationDto MapToLocationDto(Domain.ValueObjects.Location location)
    {
        return new LocationDto
        {
            Latitude = location.Latitude,
            Longitude = location.Longitude,
            Address = location.Address,
            City = location.City,
            State = location.State,
            Country = location.Country,
            PostalCode = location.PostalCode
        };
    }

    private VisualizationStyleDto GetMilestoneVisualizationStyle(Domain.Entities.Milestone milestone)
    {
        return milestone.Status switch
        {
            Domain.Enums.MilestoneStatus.Completed => new VisualizationStyleDto { Color = "#28a745", Icon = "check-circle" },
            Domain.Enums.MilestoneStatus.InProgress => new VisualizationStyleDto { Color = "#007bff", Icon = "clock" },
            Domain.Enums.MilestoneStatus.Pending when milestone.IsOverdue() => new VisualizationStyleDto { Color = "#dc3545", Icon = "exclamation-triangle" },
            _ => new VisualizationStyleDto { Color = "#6c757d", Icon = "circle" }
        };
    }

    private VisualizationStyleDto GetExceptionVisualizationStyle(Domain.Entities.TripException exception)
    {
        return exception.Severity switch
        {
            Domain.Enums.ExceptionSeverity.Critical => new VisualizationStyleDto { Color = "#dc3545", Icon = "exclamation-triangle" },
            Domain.Enums.ExceptionSeverity.High => new VisualizationStyleDto { Color = "#fd7e14", Icon = "exclamation-circle" },
            Domain.Enums.ExceptionSeverity.Medium => new VisualizationStyleDto { Color = "#ffc107", Icon = "info-circle" },
            _ => new VisualizationStyleDto { Color = "#17a2b8", Icon = "info" }
        };
    }

    private bool HasCriticalIssues(TripStatusVisualizationDto visualization)
    {
        return visualization.DelayAlerts.Any(a => a.Severity == "Critical") ||
               visualization.Exceptions.Any(e => e.Severity == "Critical") ||
               visualization.Milestones.Any(m => m.IsOverdue && m.IsCritical);
    }

    private string? DetermineNextAction(TripStatusVisualizationDto visualization)
    {
        if (visualization.HasCriticalIssues)
            return "Address critical issues immediately";
        
        var nextMilestone = visualization.Milestones
            .Where(m => m.Status == "Pending")
            .OrderBy(m => m.ScheduledDateTime)
            .FirstOrDefault();
            
        return nextMilestone != null ? $"Prepare for {nextMilestone.Name}" : "Monitor trip progress";
    }

    // Placeholder methods for external service calls
    private async Task<List<TimelineEventDto>> GenerateTimelineEvents(Domain.Entities.Trip trip, VisualizationDetailLevel detailLevel, CancellationToken cancellationToken)
    {
        // Implementation would generate timeline events
        return new List<TimelineEventDto>();
    }

    private async Task<List<TimelineMilestoneDto>> GenerateTimelineMilestones(Domain.Entities.Trip trip, CancellationToken cancellationToken)
    {
        // Implementation would generate timeline milestones
        return new List<TimelineMilestoneDto>();
    }

    private async Task<string?> GetDelayReason(Guid milestoneId, CancellationToken cancellationToken)
    {
        // Implementation would get delay reason
        return "Traffic delay";
    }

    private async Task<List<RequirementDto>> GetMilestoneRequirements(Guid milestoneId, CancellationToken cancellationToken)
    {
        // Implementation would get milestone requirements
        return new List<RequirementDto>();
    }

    private async Task<List<DocumentDto>> GetMilestoneDocuments(Guid milestoneId, CancellationToken cancellationToken)
    {
        // Implementation would get milestone documents
        return new List<DocumentDto>();
    }

    private async Task<NotificationSettingsDto> GetMilestoneNotificationSettings(Guid milestoneId, CancellationToken cancellationToken)
    {
        // Implementation would get notification settings
        return new NotificationSettingsDto();
    }

    private List<string> DetermineMilestoneActions(Domain.Entities.Milestone milestone)
    {
        var actions = new List<string>();
        
        if (milestone.Status == Domain.Enums.MilestoneStatus.Pending)
        {
            actions.Add("Mark as In Progress");
            actions.Add("Mark as Completed");
        }
        
        if (milestone.Status == Domain.Enums.MilestoneStatus.InProgress)
        {
            actions.Add("Mark as Completed");
            actions.Add("Report Issue");
        }
        
        return actions;
    }

    private async Task<VehicleInfoDto?> GetVehicleInfo(Guid vehicleId, CancellationToken cancellationToken)
    {
        // Implementation would get vehicle info from NetworkFleetManagement service
        return new VehicleInfoDto { VehicleId = vehicleId, VehicleNumber = "V001" };
    }

    private async Task<DriverInfoDto?> GetDriverInfo(Guid driverId, CancellationToken cancellationToken)
    {
        // Implementation would get driver info from NetworkFleetManagement service
        return new DriverInfoDto { DriverId = driverId, Name = "John Doe" };
    }

    private async Task<List<MilestoneVisualizationDto>> GetLegMilestones(Guid legId, CancellationToken cancellationToken)
    {
        // Implementation would get leg-specific milestones
        return new List<MilestoneVisualizationDto>();
    }

    private async Task<RouteVisualizationDto> GenerateRouteVisualization(Domain.Entities.TripLeg leg, CancellationToken cancellationToken)
    {
        // Implementation would generate route visualization
        return new RouteVisualizationDto();
    }

    private async Task<bool> HasLegIssues(Guid legId, CancellationToken cancellationToken)
    {
        // Implementation would check for leg issues
        return false;
    }

    private async Task<List<string>> GetLegIssues(Guid legId, CancellationToken cancellationToken)
    {
        // Implementation would get leg issues
        return new List<string>();
    }
}
