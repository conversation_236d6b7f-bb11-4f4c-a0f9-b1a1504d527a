using UserManagement.Domain.Entities;
using MediatR;

namespace UserManagement.Application.Commands.TransportCompanyOCR;

/// <summary>
/// Command to process Transport Company documents with OCR preview and validation
/// </summary>
public class ProcessTransportCompanyDocumentWithOCRCommand : IRequest<ProcessTransportCompanyDocumentWithOCRResult>
{
    public Guid TransportCompanyId { get; set; }
    public DocumentType DocumentType { get; set; }
    public string FileName { get; set; } = string.Empty;
    public byte[] FileContent { get; set; } = Array.Empty<byte>();
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public bool IsPreviewMode { get; set; } = true;
    public bool AutoValidate { get; set; } = true;
    public bool AutoCorrect { get; set; } = false;
    public decimal MinimumConfidenceThreshold { get; set; } = 0.7m;
    public Dictionary<string, object> ExistingData { get; set; } = new();
    public bool OverwriteExistingData { get; set; } = false;
    public List<string> RequiredFields { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Guid ProcessedByUserId { get; set; }
}

/// <summary>
/// Result of processing Transport Company document with OCR
/// </summary>
public class ProcessTransportCompanyDocumentWithOCRResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? DocumentId { get; set; }
    public string? PreviewToken { get; set; }
    public OCRExtractionResult ExtractionResult { get; set; } = new();
    public DocumentValidationResult ValidationResult { get; set; } = new();
    public DocumentPreviewData PreviewData { get; set; } = new();
    public List<OCRSuggestion> Suggestions { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public Dictionary<string, object> ProcessingMetadata { get; set; } = new();
}

/// <summary>
/// OCR extraction result
/// </summary>
public class OCRExtractionResult
{
    public decimal OverallConfidence { get; set; }
    public Dictionary<string, OCRFieldResult> ExtractedFields { get; set; } = new();
    public List<OCRTextRegion> TextRegions { get; set; } = new();
    public DocumentQualityMetrics QualityMetrics { get; set; } = new();
    public string OCRProvider { get; set; } = string.Empty;
    public TimeSpan ProcessingTime { get; set; }
    public Dictionary<string, object> RawOCRData { get; set; } = new();
    public Dictionary<string, object> ExtractedData { get; set; } = new();
}

/// <summary>
/// Individual OCR field result
/// </summary>
public class OCRFieldResult
{
    public string FieldName { get; set; } = string.Empty;
    public string ExtractedValue { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public BoundingBox BoundingBox { get; set; } = new();
    public bool IsRequired { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationIssues { get; set; } = new();
    public string? SuggestedValue { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// OCR text region
/// </summary>
public class OCRTextRegion
{
    public string Text { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public BoundingBox BoundingBox { get; set; } = new();
    public string RegionType { get; set; } = string.Empty; // Header, Body, Footer, Table, etc.
}

/// <summary>
/// Bounding box for OCR regions
/// </summary>
public class BoundingBox
{
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public decimal Confidence { get; set; }
}

/// <summary>
/// Document quality metrics
/// </summary>
public class DocumentQualityMetrics
{
    public decimal ImageQuality { get; set; }
    public decimal TextClarity { get; set; }
    public decimal DocumentCompleteness { get; set; }
    public bool IsBlurry { get; set; }
    public bool IsSkewed { get; set; }
    public bool HasGlare { get; set; }
    public bool IsPartiallyVisible { get; set; }
    public List<string> QualityIssues { get; set; } = new();
    public List<string> ImprovementSuggestions { get; set; } = new();

    // Additional properties for compatibility
    public decimal OverallScore { get; set; }
    public decimal Clarity { get; set; }
    public decimal Brightness { get; set; }
    public decimal Contrast { get; set; }
    public decimal Resolution { get; set; }
    public decimal ColorDepth { get; set; }
    public long FileSize { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Document validation result
/// </summary>
public class DocumentValidationResult
{
    public bool IsValid { get; set; }
    public decimal ValidationScore { get; set; }
    public List<ValidationIssue> Issues { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
    public Dictionary<string, bool> FieldValidations { get; set; } = new();
    public List<string> MissingRequiredFields { get; set; } = new();
    public List<string> InvalidFields { get; set; } = new();
    public DocumentAuthenticityResult AuthenticityResult { get; set; } = new();

    // Additional properties for compatibility
    public decimal ConfidenceScore { get; set; }
    public List<ValidationIssue> ValidatedFields { get; set; } = new();
}

/// <summary>
/// Validation issue
/// </summary>
public class ValidationIssue
{
    public string IssueType { get; set; } = string.Empty;
    public string FieldName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Critical, High, Medium, Low
    public string Recommendation { get; set; } = string.Empty;
    public bool IsBlocker { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Validation warning
/// </summary>
public class ValidationWarning
{
    public string WarningType { get; set; } = string.Empty;
    public string FieldName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Suggestion { get; set; } = string.Empty;
    public bool RequiresAttention { get; set; }
}

/// <summary>
/// Document authenticity result
/// </summary>
public class DocumentAuthenticityResult
{
    public bool IsAuthentic { get; set; }
    public decimal AuthenticityScore { get; set; }
    public List<string> AuthenticityChecks { get; set; } = new();
    public List<string> SuspiciousElements { get; set; } = new();
    public string AuthenticityProvider { get; set; } = string.Empty;
}

/// <summary>
/// Document preview data
/// </summary>
public class DocumentPreviewData
{
    public string DocumentType { get; set; } = string.Empty;
    public Dictionary<string, object> ExtractedData { get; set; } = new();
    public Dictionary<string, object> MergedData { get; set; } = new();
    public List<DataConflict> DataConflicts { get; set; } = new();
    public List<PreviewField> PreviewFields { get; set; } = new();
    public string PreviewImageUrl { get; set; } = string.Empty;
    public List<AnnotatedRegion> AnnotatedRegions { get; set; } = new();
}

/// <summary>
/// Data conflict between extracted and existing data
/// </summary>
public class DataConflict
{
    public string FieldName { get; set; } = string.Empty;
    public string ExistingValue { get; set; } = string.Empty;
    public string ExtractedValue { get; set; } = string.Empty;
    public decimal ExtractedConfidence { get; set; }
    public string RecommendedAction { get; set; } = string.Empty;
    public string ConflictReason { get; set; } = string.Empty;
}

/// <summary>
/// Preview field for UI display
/// </summary>
public class PreviewField
{
    public string FieldName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string OriginalValue { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public bool IsEditable { get; set; } = true;
    public bool IsRequired { get; set; }
    public bool IsValid { get; set; }
    public bool HasConflict { get; set; }
    public string FieldType { get; set; } = string.Empty; // Text, Date, Number, Email, etc.
    public List<string> ValidationRules { get; set; } = new();
    public BoundingBox? SourceRegion { get; set; }
}

/// <summary>
/// Annotated region for preview display
/// </summary>
public class AnnotatedRegion
{
    public string FieldName { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public BoundingBox BoundingBox { get; set; } = new();
    public string Color { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public bool IsHighlighted { get; set; }
}

/// <summary>
/// OCR suggestion for improvement
/// </summary>
public class OCRSuggestion
{
    public string SuggestionType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ActionRequired { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public Dictionary<string, object> SuggestionData { get; set; } = new();
}

/// <summary>
/// Command to confirm OCR processed document
/// </summary>
public class ConfirmOCRProcessedDocumentCommand : IRequest<ConfirmOCRProcessedDocumentResult>
{
    public string PreviewToken { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public Dictionary<string, object> ConfirmedData { get; set; } = new();
    public List<string> UserCorrections { get; set; } = new();
    public bool OverrideValidationIssues { get; set; } = false;
    public string? UserNotes { get; set; }
    public Guid ConfirmedByUserId { get; set; }
}

/// <summary>
/// Result of confirming OCR processed document
/// </summary>
public class ConfirmOCRProcessedDocumentResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? DocumentId { get; set; }
    public DateTime ConfirmedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> FinalData { get; set; } = new();
}
