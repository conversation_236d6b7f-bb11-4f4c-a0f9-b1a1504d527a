using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.EventHandlers;

/// <summary>
/// Event handlers for automatically updating RFQ timeline when domain events occur
/// </summary>
public class RfqTimelineEventHandlers :
    INotificationHandler<RfqPublishedDomainEvent>,
    INotificationHandler<RfqClosedDomainEvent>,
    INotificationHandler<RfqExpiredDomainEvent>,
    INotificationHandler<RfqTimeframeExtendedDomainEvent>,
    INotificationHandler<RfqApproachingExpirationDomainEvent>
{
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly ILogger<RfqTimelineEventHandlers> _logger;

    public RfqTimelineEventHandlers(
        IRfqTimelineRepository timelineRepository,
        ILogger<RfqTimelineEventHandlers> logger)
    {
        _timelineRepository = timelineRepository;
        _logger = logger;
    }

    public async Task Handle(RfqPublishedDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating timeline event for RFQ published: {RfqId}", notification.RfqId);

        var timelineEvent = RfqTimeline.CreateEvent(
            notification.RfqId,
            RfqTimelineEventType.Published,
            $"RFQ '{notification.Title}' was published and is now available for bidding",
            notification.TransportCompanyId,
            null, // ActorName - could be populated from user service
            "Transport Company",
            null,
            RfqStatus.Draft,
            RfqStatus.Published);

        await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
    }

    public async Task Handle(RfqClosedDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating timeline event for RFQ closed: {RfqId}", notification.RfqId);

        var timelineEvent = RfqTimeline.CreateEvent(
            notification.RfqId,
            RfqTimelineEventType.Closed,
            $"RFQ was closed. Reason: {notification.Reason}",
            null,
            null,
            "System",
            $"{{\"reason\": \"{notification.Reason}\"}}",
            null,
            RfqStatus.Closed);

        await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
    }

    public async Task Handle(RfqExpiredDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating timeline event for RFQ expired: {RfqId}", notification.RfqId);

        var timelineEvent = RfqTimeline.CreateEvent(
            notification.RfqId,
            RfqTimelineEventType.Expired,
            $"RFQ '{notification.Title}' has expired and is no longer accepting bids",
            null,
            null,
            "System",
            $"{{\"expiresAt\": \"{notification.ExpiresAt:yyyy-MM-ddTHH:mm:ssZ}\"}}",
            RfqStatus.Published,
            RfqStatus.Expired);

        await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
    }

    public async Task Handle(RfqTimeframeExtendedDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating timeline event for RFQ timeframe extended: {RfqId}", notification.RfqId);

        var timelineEvent = RfqTimeline.CreateEvent(
            notification.RfqId,
            RfqTimelineEventType.TimeframeExtended,
            $"RFQ timeframe was extended by {notification.Extension.Duration} {notification.Extension.Unit}. New expiration: {notification.NewExpiresAt:yyyy-MM-dd HH:mm}",
            notification.Extension.ExtendedBy,
            null,
            "Transport Company",
            $"{{\"extensionDuration\": {notification.Extension.Duration}, \"extensionUnit\": \"{notification.Extension.Unit}\", \"newExpiresAt\": \"{notification.NewExpiresAt:yyyy-MM-ddTHH:mm:ssZ}\", \"reason\": \"{notification.Extension.Reason}\"}}");

        await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
    }

    public async Task Handle(RfqApproachingExpirationDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating timeline event for RFQ approaching expiration: {RfqId}", notification.RfqId);

        var timelineEvent = RfqTimeline.CreateEvent(
            notification.RfqId,
            RfqTimelineEventType.Other,
            $"RFQ '{notification.Title}' is approaching expiration. Time remaining: {notification.RemainingTime.TotalHours:F1} hours",
            null,
            null,
            "System",
            $"{{\"expiresAt\": \"{notification.ExpiresAt:yyyy-MM-ddTHH:mm:ssZ}\", \"remainingHours\": {notification.RemainingTime.TotalHours}}}");

        await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
    }
}
