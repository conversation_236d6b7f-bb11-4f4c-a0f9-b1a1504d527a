using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Domain.Repositories
{
    public interface IPermissionRepository
    {
        Task<Permission> GetByIdAsync(Guid id);
        Task<Permission> GetByNameAsync(string name);
        Task<IEnumerable<Permission>> GetAllAsync();
        Task<IEnumerable<Permission>> GetByRoleIdAsync(Guid roleId);
        Task AddAsync(Permission permission);
        Task UpdateAsync(Permission permission);
        Task DeleteAsync(Permission permission);
        Task<bool> ExistsByNameAsync(string name);
    }
}
