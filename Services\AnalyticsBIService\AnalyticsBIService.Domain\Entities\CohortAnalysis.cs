using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Cohort analysis entity for user behavior tracking
/// </summary>
public class CohortAnalysis : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Definition { get; private set; }
    public string Metrics { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public bool IsActive { get; private set; }
    public int TotalUsers { get; private set; }
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }

    private CohortAnalysis()
    {
        Name = string.Empty;
        Description = string.Empty;
        Definition = string.Empty;
        Metrics = string.Empty;
    }

    public CohortAnalysis(
        string name,
        string description,
        string definition,
        string metrics,
        Guid createdBy,
        UserType userType,
        DateTime periodStart,
        DateTime periodEnd)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Definition = definition ?? throw new ArgumentNullException(nameof(definition));
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        CreatedBy = createdBy;
        UserType = userType;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        IsActive = true;
        TotalUsers = 0;
    }

    public void UpdateMetrics(string metrics)
    {
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        SetUpdatedAt();
    }

    public void UpdateTotalUsers(int totalUsers)
    {
        TotalUsers = totalUsers;
        SetUpdatedAt();
    }

    public void UpdatePeriod(DateTime periodStart, DateTime periodEnd)
    {
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateDefinition(string definition)
    {
        Definition = definition ?? throw new ArgumentNullException(nameof(definition));
        SetUpdatedAt();
    }
}
