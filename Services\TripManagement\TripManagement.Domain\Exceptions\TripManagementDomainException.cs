namespace TripManagement.Domain.Exceptions;

public abstract class TripManagementDomainException : Exception
{
    protected TripManagementDomainException(string message) : base(message) { }
    protected TripManagementDomainException(string message, Exception innerException) : base(message, innerException) { }
}

public class TripNotFoundException : TripManagementDomainException
{
    public TripNotFoundException(Guid tripId) 
        : base($"Trip with ID '{tripId}' was not found.")
    {
    }
}

public class TripInvalidStateException : TripManagementDomainException
{
    public TripInvalidStateException(Guid tripId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on trip '{tripId}' in state '{currentState}'.")
    {
    }
}

public class TripLegNotFoundException : TripManagementDomainException
{
    public TripLegNotFoundException(Guid tripLegId)
        : base($"Trip leg with ID '{tripLegId}' was not found.")
    {
    }
}

public class InvalidTripOperationException : TripManagementDomainException
{
    public InvalidTripOperationException(string operation, string reason)
        : base($"Cannot perform operation '{operation}': {reason}")
    {
    }
}

public class TripDocumentNotFoundException : TripManagementDomainException
{
    public TripDocumentNotFoundException(Guid documentId)
        : base($"Trip document with ID '{documentId}' was not found.")
    {
    }
}

public class InvalidTripDocumentException : TripManagementDomainException
{
    public InvalidTripDocumentException(string reason)
        : base($"Invalid trip document: {reason}")
    {
    }
}

public class TripExceptionNotFoundException : TripManagementDomainException
{
    public TripExceptionNotFoundException(Guid exceptionId)
        : base($"Trip exception with ID '{exceptionId}' was not found.")
    {
    }
}

public class InvalidLocationException : TripManagementDomainException
{
    public InvalidLocationException(string reason)
        : base($"Invalid location: {reason}")
    {
    }
}

public class RouteOptimizationException : TripManagementDomainException
{
    public RouteOptimizationException(string reason)
        : base($"Route optimization failed: {reason}")
    {
    }
}

public class GeofenceViolationException : TripManagementDomainException
{
    public GeofenceViolationException(Guid tripId, string geofenceName)
        : base($"Trip '{tripId}' violated geofence '{geofenceName}'.")
    {
    }
}

public class ETACalculationException : TripManagementDomainException
{
    public ETACalculationException(string reason)
        : base($"ETA calculation failed: {reason}")
    {
    }
}

public class DelayAlertException : TripManagementDomainException
{
    public DelayAlertException(string reason)
        : base($"Delay alert error: {reason}")
    {
    }
}

public class InvalidDelayThresholdException : TripManagementDomainException
{
    public InvalidDelayThresholdException(string reason)
        : base($"Invalid delay threshold: {reason}")
    {
    }
}

public class TripTrackingException : TripManagementDomainException
{
    public TripTrackingException(string reason)
        : base($"Trip tracking error: {reason}")
    {
    }
}

public class ProofOfDeliveryException : TripManagementDomainException
{
    public ProofOfDeliveryException(string reason)
        : base($"Proof of delivery error: {reason}")
    {
    }
}

public class VehicleAssignmentException : TripManagementDomainException
{
    public VehicleAssignmentException(string reason)
        : base($"Vehicle assignment error: {reason}")
    {
    }
}

public class DriverAssignmentException : TripManagementDomainException
{
    public DriverAssignmentException(string reason)
        : base($"Driver assignment error: {reason}")
    {
    }
}
