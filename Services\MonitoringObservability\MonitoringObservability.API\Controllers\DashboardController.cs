using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class DashboardController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(IMediator mediator, ILogger<DashboardController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get monitoring dashboard data
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<MonitoringDashboardDto>> GetDashboard(
        [FromQuery] DashboardType type = DashboardType.System,
        [FromQuery] string? serviceName = null,
        [FromQuery] TimeSpan? timeRange = null)
    {
        try
        {
            var query = new GetMonitoringDashboardQuery
            {
                Type = type,
                ServiceName = serviceName,
                TimeRange = timeRange ?? TimeSpan.FromHours(24)
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get monitoring dashboard");
            return StatusCode(500, "Failed to retrieve monitoring dashboard");
        }
    }

    /// <summary>
    /// Get system overview
    /// </summary>
    [HttpGet("overview")]
    public async Task<ActionResult<SystemOverviewDto>> GetSystemOverview([FromQuery] TimeSpan? timeRange = null)
    {
        try
        {
            var query = new GetSystemOverviewQuery { TimeRange = timeRange ?? TimeSpan.FromHours(1) };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get system overview");
            return StatusCode(500, "Failed to retrieve system overview");
        }
    }

    /// <summary>
    /// Get performance metrics for a service
    /// </summary>
    [HttpGet("services/{serviceName}/performance")]
    public async Task<ActionResult<PerformanceMetricsDto>> GetServicePerformance(
        string serviceName, 
        [FromQuery] TimeSpan? period = null)
    {
        try
        {
            var query = new GetPerformanceMetricsQuery
            {
                ServiceName = serviceName,
                Period = period ?? TimeSpan.FromMinutes(5)
            };

            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance metrics for service {ServiceName}", serviceName);
            return StatusCode(500, "Failed to retrieve service performance metrics");
        }
    }

    /// <summary>
    /// Get real-time metrics for multiple services
    /// </summary>
    [HttpGet("realtime")]
    public async Task<ActionResult<RealTimeMetricsDto>> GetRealTimeMetrics([FromQuery] string[]? serviceNames = null)
    {
        try
        {
            var tasks = new List<Task<ServiceHealthSummaryDto?>>();
            var services = serviceNames ?? new[] { "Identity", "UserManagement", "SubscriptionManagement", "OrderManagement", "DataStorage" };

            foreach (var serviceName in services)
            {
                var query = new GetServiceHealthSummaryQuery
                {
                    ServiceName = serviceName,
                    IncludeMetrics = true,
                    IncludeAlerts = true,
                    IncludeIncidents = true
                };
                tasks.Add(_mediator.Send(query));
            }

            var results = await Task.WhenAll(tasks);
            var validResults = results.Where(r => r != null).Cast<ServiceHealthSummaryDto>().ToList();

            var realTimeMetrics = new RealTimeMetricsDto
            {
                Services = validResults,
                TotalServices = validResults.Count,
                HealthyServices = validResults.Count(s => s.OverallStatus == HealthStatus.Healthy),
                DegradedServices = validResults.Count(s => s.OverallStatus == HealthStatus.Degraded),
                UnhealthyServices = validResults.Count(s => s.OverallStatus == HealthStatus.Unhealthy),
                TotalActiveAlerts = validResults.Sum(s => s.ActiveAlerts),
                TotalOpenIncidents = validResults.Sum(s => s.OpenIncidents),
                LastUpdated = DateTime.UtcNow
            };

            return Ok(realTimeMetrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get real-time metrics");
            return StatusCode(500, "Failed to retrieve real-time metrics");
        }
    }

    /// <summary>
    /// Get alert trends
    /// </summary>
    [HttpGet("alerts/trends")]
    public async Task<ActionResult<AlertTrendsDto>> GetAlertTrends(
        [FromQuery] TimeSpan? period = null,
        [FromQuery] string? serviceName = null)
    {
        try
        {
            var timeRange = period ?? TimeSpan.FromDays(7);
            var fromDate = DateTime.UtcNow - timeRange;

            var severityCountQuery = new GetAlertCountBySeverityQuery { FromDate = fromDate, ServiceName = serviceName };
            var serviceCountQuery = new GetAlertCountByServiceQuery { FromDate = fromDate };

            var severityCount = await _mediator.Send(severityCountQuery);
            var serviceCount = await _mediator.Send(serviceCountQuery);

            var trends = new AlertTrendsDto
            {
                Period = timeRange,
                ServiceName = serviceName,
                CountBySeverity = severityCount,
                CountByService = serviceCount,
                TotalAlerts = severityCount.Values.Sum(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alert trends");
            return StatusCode(500, "Failed to retrieve alert trends");
        }
    }

    /// <summary>
    /// Get incident trends
    /// </summary>
    [HttpGet("incidents/trends")]
    public async Task<ActionResult<IncidentTrendsDto>> GetIncidentTrends(
        [FromQuery] TimeSpan? period = null,
        [FromQuery] string? serviceName = null)
    {
        try
        {
            var timeRange = period ?? TimeSpan.FromDays(7);
            var fromDate = DateTime.UtcNow - timeRange;

            var severityCountQuery = new GetIncidentCountBySeverityQuery { FromDate = fromDate, ServiceName = serviceName };
            var serviceCountQuery = new GetIncidentCountByServiceQuery { FromDate = fromDate };
            var avgResolutionQuery = new GetAverageResolutionTimeQuery { FromDate = fromDate, ServiceName = serviceName };

            var severityCount = await _mediator.Send(severityCountQuery);
            var serviceCount = await _mediator.Send(serviceCountQuery);
            var avgResolutionTime = await _mediator.Send(avgResolutionQuery);

            var trends = new IncidentTrendsDto
            {
                Period = timeRange,
                ServiceName = serviceName,
                CountBySeverity = severityCount,
                CountByService = serviceCount,
                AverageResolutionTimeMinutes = avgResolutionTime,
                TotalIncidents = severityCount.Values.Sum(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get incident trends");
            return StatusCode(500, "Failed to retrieve incident trends");
        }
    }

    /// <summary>
    /// Get service availability report
    /// </summary>
    [HttpGet("availability")]
    public async Task<ActionResult<ServiceAvailabilityReportDto>> GetServiceAvailability(
        [FromQuery] string[]? serviceNames = null,
        [FromQuery] TimeSpan? period = null)
    {
        try
        {
            var timeRange = period ?? TimeSpan.FromDays(30);
            var services = serviceNames ?? new[] { "Identity", "UserManagement", "SubscriptionManagement", "OrderManagement", "DataStorage" };

            var availabilityTasks = services.Select(async serviceName =>
            {
                var uptimeQuery = new GetServiceUptimeQuery { ServiceName = serviceName, Period = timeRange };
                var uptime = await _mediator.Send(uptimeQuery);

                return new ServiceAvailabilityDto
                {
                    ServiceName = serviceName,
                    UptimePercentage = uptime,
                    Period = timeRange
                };
            });

            var availabilities = await Task.WhenAll(availabilityTasks);

            var report = new ServiceAvailabilityReportDto
            {
                Services = availabilities.ToList(),
                Period = timeRange,
                OverallAvailability = availabilities.Average(a => a.UptimePercentage),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get service availability report");
            return StatusCode(500, "Failed to retrieve service availability report");
        }
    }

    /// <summary>
    /// Get health check status summary
    /// </summary>
    [HttpGet("health-summary")]
    public async Task<ActionResult<HealthCheckSummaryDto>> GetHealthCheckSummary()
    {
        try
        {
            var query = new GetHealthStatusSummaryQuery();
            var statusCounts = await _mediator.Send(query);

            var summary = new HealthCheckSummaryDto
            {
                StatusCounts = statusCounts,
                TotalHealthChecks = statusCounts.Values.Sum(),
                HealthyPercentage = statusCounts.TryGetValue(HealthStatus.Healthy, out var healthy) 
                    ? (double)healthy / statusCounts.Values.Sum() * 100 : 0,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get health check summary");
            return StatusCode(500, "Failed to retrieve health check summary");
        }
    }
}

// Response DTOs
public class RealTimeMetricsDto
{
    public List<ServiceHealthSummaryDto> Services { get; set; } = new();
    public int TotalServices { get; set; }
    public int HealthyServices { get; set; }
    public int DegradedServices { get; set; }
    public int UnhealthyServices { get; set; }
    public int TotalActiveAlerts { get; set; }
    public int TotalOpenIncidents { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class AlertTrendsDto
{
    public TimeSpan Period { get; set; }
    public string? ServiceName { get; set; }
    public Dictionary<AlertSeverity, int> CountBySeverity { get; set; } = new();
    public Dictionary<string, int> CountByService { get; set; } = new();
    public int TotalAlerts { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class IncidentTrendsDto
{
    public TimeSpan Period { get; set; }
    public string? ServiceName { get; set; }
    public Dictionary<IncidentSeverity, int> CountBySeverity { get; set; } = new();
    public Dictionary<string, int> CountByService { get; set; } = new();
    public double AverageResolutionTimeMinutes { get; set; }
    public int TotalIncidents { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class ServiceAvailabilityDto
{
    public string ServiceName { get; set; } = string.Empty;
    public double UptimePercentage { get; set; }
    public TimeSpan Period { get; set; }
}

public class ServiceAvailabilityReportDto
{
    public List<ServiceAvailabilityDto> Services { get; set; } = new();
    public TimeSpan Period { get; set; }
    public double OverallAvailability { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class HealthCheckSummaryDto
{
    public Dictionary<HealthStatus, int> StatusCounts { get; set; } = new();
    public int TotalHealthChecks { get; set; }
    public double HealthyPercentage { get; set; }
    public DateTime GeneratedAt { get; set; }
}
