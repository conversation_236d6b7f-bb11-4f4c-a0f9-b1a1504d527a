using MediatR;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Queries;

// Audit Log Queries
public class GetAuditLogByIdQuery : IRequest<AuditLogDto?>
{
    public Guid Id { get; set; }
}

public class GetAuditTrailQuery : IRequest<AuditTrailResultDto>
{
    public Guid? UserId { get; set; }
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public AuditEventType? EventType { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public bool IncludeSensitiveData { get; set; } = false;

    // New module-specific filtering properties
    public string? ModuleName { get; set; }
    public List<string> ModuleNames { get; set; } = new();
    public List<string> ComplianceFlags { get; set; } = new();
    public Dictionary<string, object> CustomFilters { get; set; } = new();
    public bool IncludeModuleMetadata { get; set; } = false;
}

public class GetSecurityEventsQuery : IRequest<List<AuditLogDto>>
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

// Compliance Report Queries
public class GetComplianceReportByIdQuery : IRequest<ComplianceReportDto?>
{
    public Guid Id { get; set; }
}

public class GetComplianceReportByNumberQuery : IRequest<ComplianceReportDto?>
{
    public string ReportNumber { get; set; } = string.Empty;
}

public class GetComplianceReportsQuery : IRequest<ComplianceReportResultDto>
{
    public ComplianceStandard? Standard { get; set; }
    public ComplianceStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? GeneratedBy { get; set; }
    public bool? IsAutomated { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class GetComplianceSummaryQuery : IRequest<List<ComplianceSummaryDto>>
{
    // No parameters needed - returns summary for all standards
}

public class GetPendingComplianceReportsQuery : IRequest<List<ComplianceReportDto>>
{
    // No parameters needed - returns all pending reports
}

// Service Provider Rating Queries
public class GetServiceProviderRatingByIdQuery : IRequest<ServiceProviderRatingDto?>
{
    public Guid Id { get; set; }
}

public class GetServiceProviderRatingsQuery : IRequest<RatingResultDto>
{
    public Guid? ShipperId { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public RatingStatus? Status { get; set; }
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class GetTransportCompanyRatingSummaryQuery : IRequest<TransportCompanyRatingSummaryDto>
{
    public Guid TransportCompanyId { get; set; }
}

public class GetTransportCompanyRatingsQuery : IRequest<List<ServiceProviderRatingDto>>
{
    public Guid TransportCompanyId { get; set; }
    public RatingStatus? Status { get; set; }
    public int? Limit { get; set; }
}

// Preferred Provider Network Queries
public class GetPreferredProviderNetworkQuery : IRequest<List<PreferredProviderNetworkDto>>
{
    public Guid ShipperId { get; set; }
}

public class GetPreferredProviderByIdQuery : IRequest<PreferredProviderNetworkDto?>
{
    public Guid Id { get; set; }
}

public class GetPreferredProviderByShipperAndProviderQuery : IRequest<PreferredProviderNetworkDto?>
{
    public Guid ShipperId { get; set; }
    public Guid TransportCompanyId { get; set; }
}

// Service Issue Queries
public class GetServiceIssuesByRatingQuery : IRequest<List<ServiceIssueDto>>
{
    public Guid ServiceProviderRatingId { get; set; }
}

public class GetServiceIssuesByTransportCompanyQuery : IRequest<List<ServiceIssueDto>>
{
    public Guid TransportCompanyId { get; set; }
    public IssueResolutionStatus? Status { get; set; }
    public IssuePriority? MinPriority { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetServiceIssuesByShipperQuery : IRequest<List<ServiceIssueDto>>
{
    public Guid ShipperId { get; set; }
    public IssueResolutionStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
