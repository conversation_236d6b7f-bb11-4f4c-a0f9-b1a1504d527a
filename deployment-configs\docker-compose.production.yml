version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15
    container_name: tli-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  redis:
    image: redis:7-alpine
    container_name: tli-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  rabbitmq:
    image: rabbitmq:3-management
    container_name: tli-rabbitmq-prod
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
      RABBITMQ_VM_MEMORY_HIGH_WATERMARK: 0.8
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # API Gateway
  apigateway:
    image: tli/apigateway:${TLI_VERSION:-latest}
    container_name: tli-apigateway-prod
    ports:
      - '5000:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_ApiGateway;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - Redis__ConnectionString=redis:6379,password=${REDIS_PASSWORD}
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=${RABBITMQ_DEFAULT_USER}
      - RabbitMQ__Password=${RABBITMQ_DEFAULT_PASS}
      - Ocelot__GlobalConfiguration__BaseUrl=https://${DOMAIN_NAME}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Identity Service
  identity-api:
    image: tli/identity-service:${TLI_VERSION:-latest}
    container_name: tli-identity-prod
    ports:
      - '5001:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_Identity;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - JwtSettings__ExpiryMinutes=60
      - Redis__ConnectionString=redis:6379,password=${REDIS_PASSWORD}
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=${RABBITMQ_DEFAULT_USER}
      - RabbitMQ__Password=${RABBITMQ_DEFAULT_PASS}
      - Email__Provider=${EMAIL_PROVIDER}
      - Email__SMTP__Host=${SMTP_HOST}
      - Email__SMTP__Port=${SMTP_PORT}
      - Email__SMTP__Username=${SMTP_USERNAME}
      - Email__SMTP__Password=${SMTP_PASSWORD}
      - SMS__Provider=${SMS_PROVIDER}
      - SMS__Twilio__AccountSid=${TWILIO_ACCOUNT_SID}
      - SMS__Twilio__AuthToken=${TWILIO_AUTH_TOKEN}
      - SMS__Twilio__FromNumber=${TWILIO_FROM_NUMBER}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # User Management Service
  usermanagement-api:
    image: tli/usermanagement-service:${TLI_VERSION:-latest}
    container_name: tli-usermanagement-prod
    ports:
      - '5002:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_UserManagement;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - Redis__ConnectionString=redis:6379,password=${REDIS_PASSWORD}
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=${RABBITMQ_DEFAULT_USER}
      - RabbitMQ__Password=${RABBITMQ_DEFAULT_PASS}
      - Services__Identity__BaseUrl=http://identity-api:8080
      - Services__DataStorage__BaseUrl=http://datastorage-api:8080
      - KYC__DocumentStorage__Provider=${STORAGE_PROVIDER}
      - KYC__DocumentStorage__Path=${STORAGE_PATH}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      identity-api:
        condition: service_healthy
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Subscription Management Service
  subscription-api:
    image: tli/subscription-service:${TLI_VERSION:-latest}
    container_name: tli-subscription-prod
    ports:
      - '5003:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_SubscriptionManagement;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - Redis__ConnectionString=redis:6379,password=${REDIS_PASSWORD}
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=${RABBITMQ_DEFAULT_USER}
      - RabbitMQ__Password=${RABBITMQ_DEFAULT_PASS}
      - Services__UserManagement__BaseUrl=http://usermanagement-api:8080
      - Services__FinancialPayment__BaseUrl=http://payment-api:8080
      - Payment__Razorpay__KeyId=${RAZORPAY_KEY_ID}
      - Payment__Razorpay__KeySecret=${RAZORPAY_KEY_SECRET}
      - Payment__Stripe__PublishableKey=${STRIPE_PUBLISHABLE_KEY}
      - Payment__Stripe__SecretKey=${STRIPE_SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      usermanagement-api:
        condition: service_healthy
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Order Management Service
  order-api:
    image: tli/order-service:${TLI_VERSION:-latest}
    container_name: tli-order-prod
    ports:
      - '5004:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_OrderManagement;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__TimescaleConnection=Host=postgres;Port=5432;Database=TLI_OrderManagement_Timescale;User Id=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER}
      - JwtSettings__Audience=${JWT_AUDIENCE}
      - Redis__ConnectionString=redis:6379,password=${REDIS_PASSWORD}
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=${RABBITMQ_DEFAULT_USER}
      - RabbitMQ__Password=${RABBITMQ_DEFAULT_PASS}
      - Services__UserManagement__BaseUrl=http://usermanagement-api:8080
      - Services__NetworkFleetManagement__BaseUrl=http://fleet-api:8080
      - Services__CommunicationNotification__BaseUrl=http://communication-api:8080
      - Maps__GoogleMaps__ApiKey=${GOOGLE_MAPS_API_KEY}
      - RfqExpiration__DefaultTimeframeDurationHours=72
      - RfqExpiration__MaxExtensionsAllowed=3
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      usermanagement-api:
        condition: service_healthy
    networks:
      - tli-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  storage_data:
    driver: local
  logs_data:
    driver: local

networks:
  tli-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
