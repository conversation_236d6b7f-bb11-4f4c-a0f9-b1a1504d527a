using Xunit;
using FluentAssertions;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;

namespace AnalyticsBIService.Tests.Unit.Domain;

/// <summary>
/// Simple domain tests that work with actual domain entity constructors
/// </summary>
public class SimpleDomainTests
{
    [Fact]
    public void Report_Constructor_Should_Create_Valid_Report()
    {
        // Arrange
        var name = "Test Report";
        var description = "Test Description";
        var generatedBy = Guid.NewGuid();
        var reportType = ReportType.PerformanceAnalysis;
        var userType = UserType.Admin;
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-7), DateTime.UtcNow, TimePeriod.Weekly);

        // Act
        var report = new Report(name, description, reportType, generatedBy, userType, period);

        // Assert
        report.Should().NotBeNull();
        report.Name.Should().Be(name);
        report.Description.Should().Be(description);
        report.GeneratedBy.Should().Be(generatedBy);
        report.Type.Should().Be(reportType);
        report.UserType.Should().Be(userType);
        report.Period.Should().Be(period);
        report.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Dashboard_Constructor_Should_Create_Valid_Dashboard()
    {
        // Arrange
        var name = "Test Dashboard";
        var description = "Test Description";
        var dashboardType = DashboardType.AdminPlatformPerformance;
        var userId = Guid.NewGuid();
        var userType = UserType.Admin;

        // Act
        var dashboard = new Dashboard(name, description, dashboardType, userId, userType);

        // Assert
        dashboard.Should().NotBeNull();
        dashboard.Name.Should().Be(name);
        dashboard.Description.Should().Be(description);
        dashboard.Type.Should().Be(dashboardType);
        dashboard.UserId.Should().Be(userId);
        dashboard.UserType.Should().Be(userType);
    }

    [Fact]
    public void Alert_Constructor_Should_Create_Valid_Alert()
    {
        // Arrange
        var name = "Test Alert";
        var description = "Test Description";
        var severity = AlertSeverity.High;
        var message = "Test alert message";
        var userId = Guid.NewGuid();

        // Act
        var alert = new Alert(name, description, severity, message, userId: userId);

        // Assert
        alert.Should().NotBeNull();
        alert.Name.Should().Be(name);
        alert.Description.Should().Be(description);
        alert.Severity.Should().Be(severity);
        alert.Message.Should().Be(message);
        alert.UserId.Should().Be(userId);
        alert.IsActive.Should().BeTrue();
        alert.Status.Should().Be(AlertStatus.Active);
    }

    [Fact]
    public void Report_Should_Have_Valid_Timestamps()
    {
        // Arrange
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow, TimePeriod.Daily);

        // Act
        var report = new Report(
            "Test Report",
            "Description",
            ReportType.PerformanceAnalysis,
            Guid.NewGuid(),
            UserType.Admin,
            period);

        // Assert
        report.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        report.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        report.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Dashboard_Should_Have_Valid_Timestamps()
    {
        // Arrange & Act
        var dashboard = new Dashboard(
            "Test Dashboard", 
            "Description",
            DashboardType.AdminPlatformPerformance,
            Guid.NewGuid(),
            UserType.Admin);

        // Assert
        dashboard.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        dashboard.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Alert_Should_Have_Valid_Timestamps()
    {
        // Arrange & Act
        var alert = new Alert(
            "Test Alert",
            "Description", 
            AlertSeverity.High,
            "Test message",
            userId: Guid.NewGuid());

        // Assert
        alert.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        alert.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        alert.TriggeredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(ReportType.PerformanceAnalysis)]
    [InlineData(ReportType.RevenueAnalysis)]
    [InlineData(ReportType.OperationalEfficiency)]
    [InlineData(ReportType.CustomReport)]
    public void Report_Should_Accept_Valid_ReportTypes(ReportType reportType)
    {
        // Arrange
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow, TimePeriod.Daily);

        // Act
        var report = new Report("Test", "Description", reportType, Guid.NewGuid(), UserType.Admin, period);

        // Assert
        report.Type.Should().Be(reportType);
    }

    [Theory]
    [InlineData(AlertStatus.Active)]
    [InlineData(AlertStatus.Pending)]
    [InlineData(AlertStatus.InProgress)]
    [InlineData(AlertStatus.Resolved)]
    public void Alert_Should_Accept_Valid_AlertStatus(AlertStatus status)
    {
        // Arrange & Act
        var alert = new Alert("Test", "Description", AlertSeverity.Medium, "Message", userId: Guid.NewGuid());

        // Assert - Alert starts as Active, but we can verify the enum values exist
        AlertStatus.Active.Should().NotBe(default(AlertStatus));
        status.Should().NotBe(default(AlertStatus));
    }

    [Fact]
    public void Multiple_Entities_Should_Have_Unique_Ids()
    {
        // Arrange
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow, TimePeriod.Daily);

        // Act
        var report1 = new Report("Report 1", "Description", ReportType.PerformanceAnalysis, Guid.NewGuid(), UserType.Admin, period);
        var report2 = new Report("Report 2", "Description", ReportType.PerformanceAnalysis, Guid.NewGuid(), UserType.Admin, period);

        var dashboard1 = new Dashboard("Dashboard 1", "Description", DashboardType.AdminPlatformPerformance, Guid.NewGuid(), UserType.Admin);
        var dashboard2 = new Dashboard("Dashboard 2", "Description", DashboardType.AdminPlatformPerformance, Guid.NewGuid(), UserType.Admin);

        var alert1 = new Alert("Alert 1", "Description", AlertSeverity.High, "Message", userId: Guid.NewGuid());
        var alert2 = new Alert("Alert 2", "Description", AlertSeverity.High, "Message", userId: Guid.NewGuid());

        // Assert
        var allIds = new[] { report1.Id, report2.Id, dashboard1.Id, dashboard2.Id, alert1.Id, alert2.Id };
        allIds.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public void Report_UpdateName_Should_Work()
    {
        // Arrange
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow, TimePeriod.Daily);
        var report = new Report("Original Name", "Description", ReportType.PerformanceAnalysis, Guid.NewGuid(), UserType.Admin, period);
        var newName = "Updated Name";

        // Act
        report.UpdateName(newName);

        // Assert
        report.Name.Should().Be(newName);
    }

    [Fact]
    public void Alert_Acknowledge_Should_Work()
    {
        // Arrange
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Message", userId: Guid.NewGuid());
        var acknowledgedBy = Guid.NewGuid();

        // Act
        alert.Acknowledge(acknowledgedBy);

        // Assert
        alert.IsAcknowledged.Should().BeTrue();
        alert.AcknowledgedBy.Should().Be(acknowledgedBy);
        alert.AcknowledgedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Alert_Resolve_Should_Work()
    {
        // Arrange
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Message", userId: Guid.NewGuid());
        var resolvedBy = Guid.NewGuid();
        var resolution = "Fixed the issue";

        // Act
        alert.Resolve(resolvedBy, resolution);

        // Assert
        alert.IsActive.Should().BeFalse();
        alert.ResolvedBy.Should().Be(resolvedBy);
        alert.Resolution.Should().Be(resolution);
        alert.ResolvedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Dashboard_SetPublic_Should_Work()
    {
        // Arrange
        var dashboard = new Dashboard("Test Dashboard", "Description", DashboardType.AdminPlatformPerformance, Guid.NewGuid(), UserType.Admin);

        // Act
        dashboard.SetPublic(true);

        // Assert
        dashboard.IsPublic.Should().BeTrue();
    }

    [Fact]
    public void Entities_Should_Support_Basic_Operations()
    {
        // Arrange
        var period = new TimePeriodValue(DateTime.UtcNow.AddDays(-1), DateTime.UtcNow, TimePeriod.Daily);
        
        var report = new Report("Test Report", "Description", ReportType.PerformanceAnalysis, Guid.NewGuid(), UserType.Admin, period);
        var dashboard = new Dashboard("Test Dashboard", "Description", DashboardType.AdminPlatformPerformance, Guid.NewGuid(), UserType.Admin);
        var alert = new Alert("Test Alert", "Description", AlertSeverity.High, "Test message", userId: Guid.NewGuid());

        // Act & Assert - Basic property access should work
        report.Id.Should().NotBeEmpty();
        report.Name.Should().NotBeNullOrEmpty();
        report.Description.Should().NotBeNullOrEmpty();

        dashboard.Id.Should().NotBeEmpty();
        dashboard.Name.Should().NotBeNullOrEmpty();
        dashboard.Description.Should().NotBeNullOrEmpty();

        alert.Id.Should().NotBeEmpty();
        alert.Name.Should().NotBeNullOrEmpty();
        alert.Description.Should().NotBeNullOrEmpty();
        alert.Severity.Should().NotBe(default(AlertSeverity));
    }
}
