﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// IVR analytics value object
/// </summary>
public class IVRAnalytics : ValueObject
{
    public int TotalCalls { get; private set; }
    public int CompletedCalls { get; private set; }
    public int AbandonedCalls { get; private set; }
    public decimal AverageCallDuration { get; private set; } // in seconds
    public decimal CompletionRate { get; private set; }
    public decimal AbandonmentRate { get; private set; }
    public Dictionary<string, int> MenuUsage { get; private set; }
    public Dictionary<string, int> OptionSelections { get; private set; }
    public Dictionary<string, decimal> MenuDurations { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private IVRAnalytics()
    {
        MenuUsage = new Dictionary<string, int>();
        OptionSelections = new Dictionary<string, int>();
        MenuDurations = new Dictionary<string, decimal>();
    }

    public IVRAnalytics(
        int totalCalls,
        int completedCalls,
        int abandonedCalls,
        decimal averageCallDuration,
        Dictionary<string, int>? menuUsage = null,
        Dictionary<string, int>? optionSelections = null,
        Dictionary<string, decimal>? menuDurations = null)
    {
        TotalCalls = totalCalls;
        CompletedCalls = completedCalls;
        AbandonedCalls = abandonedCalls;
        AverageCallDuration = averageCallDuration;
        MenuUsage = menuUsage ?? new Dictionary<string, int>();
        OptionSelections = optionSelections ?? new Dictionary<string, int>();
        MenuDurations = menuDurations ?? new Dictionary<string, decimal>();
        LastUpdated = DateTime.UtcNow;

        // Calculate rates
        CompletionRate = totalCalls > 0 ? (decimal)completedCalls / totalCalls * 100 : 0;
        AbandonmentRate = totalCalls > 0 ? (decimal)abandonedCalls / totalCalls * 100 : 0;
    }

    public static IVRAnalytics Empty()
    {
        return new IVRAnalytics(0, 0, 0, 0);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalCalls;
        yield return CompletedCalls;
        yield return AbandonedCalls;
        yield return AverageCallDuration;
        yield return CompletionRate;
        yield return AbandonmentRate;
        yield return LastUpdated;

        foreach (var kvp in MenuUsage.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in OptionSelections.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in MenuDurations.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// IVR session value object representing an active call session
/// </summary>
public class IVRSession : ValueObject
{
    public Guid SessionId { get; private set; }
    public string CallId { get; private set; }
    public string PhoneNumber { get; private set; }
    public Guid IVRSystemId { get; private set; }
    public Guid? CurrentMenuId { get; private set; }
    public IVRSessionStatus Status { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? EndedAt { get; private set; }
    public Dictionary<string, string> Variables { get; private set; }
    public List<IVRSessionStep> Steps { get; private set; }
    public string Language { get; private set; }
    public int RetryCount { get; private set; }
    public string? LastInput { get; private set; }
    public string? LastError { get; private set; }

    private IVRSession()
    {
        CallId = string.Empty;
        PhoneNumber = string.Empty;
        Variables = new Dictionary<string, string>();
        Steps = new List<IVRSessionStep>();
        Language = "en-US";
    }

    public IVRSession(
        string callId,
        string phoneNumber,
        Guid ivrSystemId,
        string language = "en-US")
    {
        if (string.IsNullOrWhiteSpace(callId))
            throw new ArgumentException("Call ID cannot be empty", nameof(callId));
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));
        if (ivrSystemId == Guid.Empty)
            throw new ArgumentException("IVR system ID cannot be empty", nameof(ivrSystemId));

        SessionId = Guid.NewGuid();
        CallId = callId;
        PhoneNumber = phoneNumber;
        IVRSystemId = ivrSystemId;
        Status = IVRSessionStatus.Active;
        StartedAt = DateTime.UtcNow;
        Variables = new Dictionary<string, string>();
        Steps = new List<IVRSessionStep>();
        Language = language;
        RetryCount = 0;
    }

    public static IVRSession Create(string callId, string phoneNumber, Guid ivrSystemId, string language = "en-US")
    {
        return new IVRSession(callId, phoneNumber, ivrSystemId, language);
    }

    public void NavigateToMenu(Guid menuId)
    {
        CurrentMenuId = menuId;
        AddStep(new IVRSessionStep(IVRStepType.MenuNavigation, menuId.ToString(), DateTime.UtcNow));
    }

    public void SetVariable(string key, string value)
    {
        Variables[key] = value;
        AddStep(new IVRSessionStep(IVRStepType.VariableSet, $"{key}={value}", DateTime.UtcNow));
    }

    public string? GetVariable(string key)
    {
        return Variables.TryGetValue(key, out var value) ? value : null;
    }

    public void RecordInput(string input)
    {
        LastInput = input;
        AddStep(new IVRSessionStep(IVRStepType.UserInput, input, DateTime.UtcNow));
    }

    public void RecordError(string error)
    {
        LastError = error;
        RetryCount++;
        AddStep(new IVRSessionStep(IVRStepType.Error, error, DateTime.UtcNow));
    }

    public void Complete()
    {
        Status = IVRSessionStatus.Completed;
        EndedAt = DateTime.UtcNow;
        AddStep(new IVRSessionStep(IVRStepType.SessionEnd, "Completed", DateTime.UtcNow));
    }

    public void Abandon()
    {
        Status = IVRSessionStatus.Abandoned;
        EndedAt = DateTime.UtcNow;
        AddStep(new IVRSessionStep(IVRStepType.SessionEnd, "Abandoned", DateTime.UtcNow));
    }

    public void Fail(string reason)
    {
        Status = IVRSessionStatus.Failed;
        EndedAt = DateTime.UtcNow;
        LastError = reason;
        AddStep(new IVRSessionStep(IVRStepType.SessionEnd, $"Failed: {reason}", DateTime.UtcNow));
    }

    public TimeSpan GetDuration()
    {
        var endTime = EndedAt ?? DateTime.UtcNow;
        return endTime - StartedAt;
    }

    public IVRSessionStep? GetLastStep()
    {
        return Steps.LastOrDefault();
    }

    public List<IVRSessionStep> GetStepsByType(IVRStepType stepType)
    {
        return Steps.Where(s => s.StepType == stepType).ToList();
    }

    private void AddStep(IVRSessionStep step)
    {
        Steps.Add(step);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SessionId;
        yield return CallId;
        yield return PhoneNumber;
        yield return IVRSystemId;
        yield return CurrentMenuId ?? Guid.Empty;
        yield return Status;
        yield return StartedAt;
        yield return EndedAt ?? DateTime.MinValue;
        yield return Language;
        yield return RetryCount;
        yield return LastInput ?? string.Empty;
        yield return LastError ?? string.Empty;

        foreach (var kvp in Variables.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var step in Steps)
        {
            yield return step;
        }
    }
}

/// <summary>
/// IVR session step representing an action in the session
/// </summary>
public class IVRSessionStep : ValueObject
{
    public IVRStepType StepType { get; private set; }
    public string Data { get; private set; }
    public DateTime Timestamp { get; private set; }
    public TimeSpan? Duration { get; private set; }

    private IVRSessionStep()
    {
        Data = string.Empty;
    }

    public IVRSessionStep(IVRStepType stepType, string data, DateTime timestamp, TimeSpan? duration = null)
    {
        StepType = stepType;
        Data = data ?? string.Empty;
        Timestamp = timestamp;
        Duration = duration;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return StepType;
        yield return Data;
        yield return Timestamp;
        yield return Duration ?? TimeSpan.Zero;
    }
}

/// <summary>
/// Voice recognition result value object
/// </summary>
public class VoiceRecognitionResult : ValueObject
{
    public string RecognizedText { get; private set; }
    public decimal Confidence { get; private set; }
    public string Language { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private VoiceRecognitionResult()
    {
        RecognizedText = string.Empty;
        Language = string.Empty;
        Metadata = new Dictionary<string, object>();
    }

    public VoiceRecognitionResult(
        string recognizedText,
        decimal confidence,
        string language,
        bool isSuccessful,
        string? errorMessage = null,
        Dictionary<string, object>? metadata = null)
    {
        RecognizedText = recognizedText ?? string.Empty;
        Confidence = confidence;
        Language = language;
        IsSuccessful = isSuccessful;
        ErrorMessage = errorMessage;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public static VoiceRecognitionResult Success(string recognizedText, decimal confidence, string language)
    {
        return new VoiceRecognitionResult(recognizedText, confidence, language, true);
    }

    public static VoiceRecognitionResult Failure(string errorMessage)
    {
        return new VoiceRecognitionResult(string.Empty, 0, string.Empty, false, errorMessage);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return RecognizedText;
        yield return Confidence;
        yield return Language;
        yield return IsSuccessful;
        yield return ErrorMessage ?? string.Empty;

        foreach (var kvp in Metadata.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// IVR session status enumeration
/// </summary>
public enum IVRSessionStatus
{
    Active = 1,
    Completed = 2,
    Abandoned = 3,
    Failed = 4,
    Transferred = 5
}

/// <summary>
/// IVR step type enumeration
/// </summary>
public enum IVRStepType
{
    SessionStart = 1,
    MenuNavigation = 2,
    UserInput = 3,
    VariableSet = 4,
    ActionExecuted = 5,
    Error = 6,
    SessionEnd = 7,
    Transfer = 8,
    VoiceRecognition = 9
}


