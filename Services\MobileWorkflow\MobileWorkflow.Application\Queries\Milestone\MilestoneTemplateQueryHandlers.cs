using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Queries.Milestone;

public class GetMilestoneTemplateByIdQueryHandler : IRequestHandler<GetMilestoneTemplateByIdQuery, MilestoneTemplateDto?>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMilestoneTemplateByIdQueryHandler> _logger;

    public GetMilestoneTemplateByIdQueryHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<GetMilestoneTemplateByIdQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto?> Handle(GetMilestoneTemplateByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                return null;
            }

            var templateDto = _mapper.Map<MilestoneTemplateDto>(template);
            templateDto.TotalPayoutPercentage = template.Steps
                .SelectMany(s => s.PayoutRules)
                .Sum(r => r.PayoutPercentage);
            templateDto.IsValid = template.ValidatePayoutPercentages();
            templateDto.ValidationErrors = template.GetValidationErrors();

            return templateDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template by ID: {Id}", request.Id);
            throw;
        }
    }
}

public class GetMilestoneTemplateByNameQueryHandler : IRequestHandler<GetMilestoneTemplateByNameQuery, MilestoneTemplateDto?>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMilestoneTemplateByNameQueryHandler> _logger;

    public GetMilestoneTemplateByNameQueryHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<GetMilestoneTemplateByNameQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto?> Handle(GetMilestoneTemplateByNameQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var template = await _templateRepository.GetByNameAsync(request.Name, cancellationToken);
            if (template == null)
            {
                return null;
            }

            var templateDto = _mapper.Map<MilestoneTemplateDto>(template);
            templateDto.TotalPayoutPercentage = template.Steps
                .SelectMany(s => s.PayoutRules)
                .Sum(r => r.PayoutPercentage);
            templateDto.IsValid = template.ValidatePayoutPercentages();
            templateDto.ValidationErrors = template.GetValidationErrors();

            return templateDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template by name: {Name}", request.Name);
            throw;
        }
    }
}

public class GetActiveMilestoneTemplatesQueryHandler : IRequestHandler<GetActiveMilestoneTemplatesQuery, IEnumerable<MilestoneTemplateDto>>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetActiveMilestoneTemplatesQueryHandler> _logger;

    public GetActiveMilestoneTemplatesQueryHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<GetActiveMilestoneTemplatesQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<MilestoneTemplateDto>> Handle(GetActiveMilestoneTemplatesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var templates = await _templateRepository.GetActiveTemplatesAsync(cancellationToken);
            var templateDtos = new List<MilestoneTemplateDto>();

            foreach (var template in templates)
            {
                var templateDto = _mapper.Map<MilestoneTemplateDto>(template);
                templateDto.TotalPayoutPercentage = template.Steps
                    .SelectMany(s => s.PayoutRules)
                    .Sum(r => r.PayoutPercentage);
                templateDto.IsValid = template.ValidatePayoutPercentages();
                templateDto.ValidationErrors = template.GetValidationErrors();
                templateDtos.Add(templateDto);
            }

            return templateDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active milestone templates");
            throw;
        }
    }
}

public class GetMilestoneTemplatePreviewsQueryHandler : IRequestHandler<GetMilestoneTemplatePreviewsQuery, IEnumerable<MilestoneTemplatePreviewDto>>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMilestoneTemplatePreviewsQueryHandler> _logger;

    public GetMilestoneTemplatePreviewsQueryHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<GetMilestoneTemplatePreviewsQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<MilestoneTemplatePreviewDto>> Handle(GetMilestoneTemplatePreviewsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var templates = await _templateRepository.GetAllAsync(cancellationToken);

            // Apply filters
            var filteredTemplates = templates.AsEnumerable();

            if (!string.IsNullOrEmpty(request.Type))
            {
                filteredTemplates = filteredTemplates.Where(t => t.Type.Equals(request.Type, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(request.Category))
            {
                filteredTemplates = filteredTemplates.Where(t => t.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase));
            }

            if (request.IsActive.HasValue)
            {
                filteredTemplates = filteredTemplates.Where(t => t.IsActive == request.IsActive.Value);
            }

            if (request.IsDefault.HasValue)
            {
                filteredTemplates = filteredTemplates.Where(t => t.IsDefault == request.IsDefault.Value);
            }

            // Apply pagination
            var pagedTemplates = filteredTemplates
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize);

            var previewDtos = new List<MilestoneTemplatePreviewDto>();

            foreach (var template in pagedTemplates)
            {
                var previewDto = _mapper.Map<MilestoneTemplatePreviewDto>(template);
                previewDto.StepCount = template.Steps.Count;
                previewDto.TotalPayoutPercentage = template.Steps
                    .SelectMany(s => s.PayoutRules)
                    .Sum(r => r.PayoutPercentage);
                previewDto.IsValid = template.ValidatePayoutPercentages();
                previewDtos.Add(previewDto);
            }

            return previewDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestone template previews");
            throw;
        }
    }
}

public class ValidateMilestoneTemplateQueryHandler : IRequestHandler<ValidateMilestoneTemplateQuery, MilestoneTemplateValidationResult>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly ILogger<ValidateMilestoneTemplateQueryHandler> _logger;

    public ValidateMilestoneTemplateQueryHandler(
        IMilestoneTemplateRepository templateRepository,
        ILogger<ValidateMilestoneTemplateQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    public async Task<MilestoneTemplateValidationResult> Handle(ValidateMilestoneTemplateQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                return new MilestoneTemplateValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { "Template not found" }
                };
            }

            var validationErrors = template.GetValidationErrors();
            var totalPayoutPercentage = template.Steps
                .SelectMany(s => s.PayoutRules)
                .Sum(r => r.PayoutPercentage);

            var warnings = new List<string>();
            if (totalPayoutPercentage < 100)
            {
                warnings.Add($"Total payout percentage is {totalPayoutPercentage}%, which is less than 100%");
            }

            return new MilestoneTemplateValidationResult
            {
                IsValid = !validationErrors.Any(),
                Errors = validationErrors,
                Warnings = warnings,
                TotalPayoutPercentage = totalPayoutPercentage,
                ValidationDetails = new Dictionary<string, object>
                {
                    { "StepCount", template.Steps.Count },
                    { "RequiredStepCount", template.Steps.Count(s => s.IsRequired) },
                    { "PayoutRuleCount", template.Steps.SelectMany(s => s.PayoutRules).Count() }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating milestone template: {Id}", request.Id);
            throw;
        }
    }
}
