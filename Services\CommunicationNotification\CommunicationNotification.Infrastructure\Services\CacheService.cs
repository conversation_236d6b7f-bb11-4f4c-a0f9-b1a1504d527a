using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Advanced caching service with multi-level caching and performance optimization
/// </summary>
public interface ICacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class;
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task<TimeSpan?> GetTtlAsync(string key, CancellationToken cancellationToken = default);
    Task RefreshAsync(string key, CancellationToken cancellationToken = default);
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class;
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class;
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class;
    Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default);
    Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
    Task<double> IncrementAsync(string key, double value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);
}

public class CacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IMemoryCache _memoryCache;
    private readonly IConnectionMultiplexer? _redis;
    private readonly IDatabase? _redisDatabase;
    private readonly ILogger<CacheService> _logger;
    private readonly CacheConfiguration _configuration;
    private readonly JsonSerializerOptions _jsonOptions;

    public CacheService(
        IDistributedCache distributedCache,
        IMemoryCache memoryCache,
        IConnectionMultiplexer? redis,
        ILogger<CacheService> logger,
        IOptions<CacheConfiguration> configuration)
    {
        _distributedCache = distributedCache;
        _memoryCache = memoryCache;
        _redis = redis;
        _redisDatabase = redis?.GetDatabase();
        _logger = logger;
        _configuration = configuration.Value;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var cacheKey = GenerateCacheKey(key);
            
            // Try L1 cache (memory) first for frequently accessed data
            if (_configuration.EnableL1Cache && _memoryCache.TryGetValue(cacheKey, out T? memoryValue))
            {
                _logger.LogDebug("Cache hit (L1) for key: {Key}", cacheKey);
                return memoryValue;
            }

            // Try L2 cache (distributed/Redis)
            var cachedData = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                var value = JsonSerializer.Deserialize<T>(cachedData, _jsonOptions);
                
                // Store in L1 cache for faster subsequent access
                if (_configuration.EnableL1Cache && value != null)
                {
                    var l1Expiry = TimeSpan.FromMinutes(_configuration.L1CacheExpiryMinutes);
                    _memoryCache.Set(cacheKey, value, l1Expiry);
                }
                
                _logger.LogDebug("Cache hit (L2) for key: {Key}", cacheKey);
                return value;
            }

            _logger.LogDebug("Cache miss for key: {Key}", cacheKey);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var cacheKey = GenerateCacheKey(key);
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var cacheExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);

            // Set in distributed cache
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = cacheExpiry
            };
            
            await _distributedCache.SetStringAsync(cacheKey, serializedValue, options, cancellationToken);

            // Set in L1 cache if enabled
            if (_configuration.EnableL1Cache)
            {
                var l1Expiry = TimeSpan.FromMinutes(Math.Min(_configuration.L1CacheExpiryMinutes, cacheExpiry.TotalMinutes));
                _memoryCache.Set(cacheKey, value, l1Expiry);
            }

            _logger.LogDebug("Set cache value for key: {Key} with expiry: {Expiry}", cacheKey, cacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey(key);
            
            // Remove from distributed cache
            await _distributedCache.RemoveAsync(cacheKey, cancellationToken);
            
            // Remove from L1 cache
            if (_configuration.EnableL1Cache)
            {
                _memoryCache.Remove(cacheKey);
            }

            _logger.LogDebug("Removed cache value for key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_redisDatabase == null)
            {
                _logger.LogWarning("Redis not available for pattern-based cache removal");
                return;
            }

            var server = _redis!.GetServer(_redis.GetEndPoints().First());
            var keys = server.Keys(pattern: GenerateCacheKey(pattern));
            
            foreach (var key in keys)
            {
                await _redisDatabase.KeyDeleteAsync(key);
                
                // Remove from L1 cache if enabled
                if (_configuration.EnableL1Cache)
                {
                    _memoryCache.Remove(key.ToString());
                }
            }

            _logger.LogDebug("Removed cache values matching pattern: {Pattern}", pattern);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache values by pattern: {Pattern}", pattern);
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = GenerateCacheKey(key);
            
            // Check L1 cache first
            if (_configuration.EnableL1Cache && _memoryCache.TryGetValue(cacheKey, out _))
            {
                return true;
            }

            // Check distributed cache
            if (_redisDatabase != null)
            {
                return await _redisDatabase.KeyExistsAsync(cacheKey);
            }

            // Fallback to checking if value exists
            var value = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            return !string.IsNullOrEmpty(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public async Task<TimeSpan?> GetTtlAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_redisDatabase == null)
            {
                return null;
            }

            var cacheKey = GenerateCacheKey(key);
            var ttl = await _redisDatabase.KeyTimeToLiveAsync(cacheKey);
            return ttl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting TTL for key: {Key}", key);
            return null;
        }
    }

    public async Task RefreshAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_redisDatabase == null)
            {
                return;
            }

            var cacheKey = GenerateCacheKey(key);
            await _redisDatabase.KeyTouchAsync(cacheKey);
            
            _logger.LogDebug("Refreshed cache key: {Key}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing cache key: {Key}", key);
        }
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class
    {
        var result = new Dictionary<string, T?>();
        
        try
        {
            var cacheKeys = keys.Select(GenerateCacheKey).ToArray();
            var values = await _distributedCache.GetAsync(cacheKeys.First(), cancellationToken); // Simplified for demo
            
            // In a real implementation, you'd use Redis MGET for better performance
            foreach (var key in keys)
            {
                var value = await GetAsync<T>(key, cancellationToken);
                result[key] = value;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple cache values");
        }

        return result;
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var tasks = keyValuePairs.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiry, cancellationToken));
            await Task.WhenAll(tasks);
            
            _logger.LogDebug("Set {Count} cache values", keyValuePairs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple cache values");
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiry = null, CancellationToken cancellationToken = default) where T : class
    {
        var value = await GetAsync<T>(key, cancellationToken);
        if (value != null)
        {
            return value;
        }

        value = await factory();
        if (value != null)
        {
            await SetAsync(key, value, expiry, cancellationToken);
        }

        return value;
    }

    public async Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        try
        {
            var pattern = $"*{_configuration.TagSeparator}{tag}{_configuration.TagSeparator}*";
            await RemoveByPatternAsync(pattern, cancellationToken);
            
            _logger.LogDebug("Invalidated cache entries with tag: {Tag}", tag);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache tag: {Tag}", tag);
        }
    }

    public async Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_redisDatabase == null)
            {
                throw new InvalidOperationException("Redis not available for increment operations");
            }

            var cacheKey = GenerateCacheKey(key);
            var result = await _redisDatabase.StringIncrementAsync(cacheKey, value);
            
            if (expiry.HasValue)
            {
                await _redisDatabase.KeyExpireAsync(cacheKey, expiry.Value);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing cache value for key: {Key}", key);
            throw;
        }
    }

    public async Task<double> IncrementAsync(string key, double value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_redisDatabase == null)
            {
                throw new InvalidOperationException("Redis not available for increment operations");
            }

            var cacheKey = GenerateCacheKey(key);
            var result = await _redisDatabase.StringIncrementAsync(cacheKey, value);
            
            if (expiry.HasValue)
            {
                await _redisDatabase.KeyExpireAsync(cacheKey, expiry.Value);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing cache value for key: {Key}", key);
            throw;
        }
    }

    private string GenerateCacheKey(string key)
    {
        return $"{_configuration.KeyPrefix}:{key}";
    }
}

/// <summary>
/// Cache configuration options
/// </summary>
public class CacheConfiguration
{
    public bool EnableCaching { get; set; } = true;
    public bool EnableL1Cache { get; set; } = true;
    public int DefaultExpiryMinutes { get; set; } = 15;
    public int L1CacheExpiryMinutes { get; set; } = 5;
    public string KeyPrefix { get; set; } = "tli_comm";
    public string TagSeparator { get; set; } = ":tag:";
    public int MaxL1CacheSize { get; set; } = 1000;
    public Dictionary<string, CachePolicy> Policies { get; set; } = new();
}

/// <summary>
/// Cache policy for specific data types
/// </summary>
public class CachePolicy
{
    public string Name { get; set; } = string.Empty;
    public int ExpiryMinutes { get; set; } = 15;
    public bool EnableL1Cache { get; set; } = true;
    public int L1ExpiryMinutes { get; set; } = 5;
    public List<string> Tags { get; set; } = new();
}
