using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Template management service implementation
/// </summary>
public class TemplateService : ITemplateService
{
    private readonly ILocalizationService _localizationService;
    private readonly ITemplateRepository _templateRepository;
    private readonly ILogger<TemplateService> _logger;

    public TemplateService(
        ILocalizationService localizationService,
        ITemplateRepository templateRepository,
        ILogger<TemplateService> logger)
    {
        _localizationService = localizationService;
        _templateRepository = templateRepository;
        _logger = logger;
    }

    public async Task<MessageContent> RenderNotificationTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
            
            if (template == null)
            {
                _logger.LogWarning("Notification template '{TemplateKey}' not found for language '{Language}'", templateKey, language);
                return MessageContent.Create("Notification", "You have a new notification", language);
            }

            var renderedContent = await _localizationService.RenderTemplateAsync(templateKey, language, parameters, cancellationToken);
            var renderedSubject = template.Subject != null 
                ? RenderTemplateContent(template.Subject, parameters) 
                : "Notification";

            return MessageContent.Create(renderedSubject, renderedContent, language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render notification template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return MessageContent.Create("Notification", "You have a new notification", language);
        }
    }

    public async Task<string> RenderWhatsAppTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _localizationService.RenderTemplateAsync(templateKey, language, parameters, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render WhatsApp template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return "Message template not available";
        }
    }

    public async Task<string> RenderEmailTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        bool isHtml = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var htmlTemplateKey = isHtml ? $"{templateKey}_html" : $"{templateKey}_text";
            var template = await _localizationService.GetTemplateAsync(htmlTemplateKey, language, cancellationToken);
            
            if (template == null)
            {
                // Fallback to base template
                template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
            }

            if (template == null)
            {
                _logger.LogWarning("Email template '{TemplateKey}' not found for language '{Language}'", templateKey, language);
                return isHtml ? "<p>Email content not available</p>" : "Email content not available";
            }

            return RenderTemplateContent(template.Content, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render email template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return isHtml ? "<p>Email content not available</p>" : "Email content not available";
        }
    }

    public async Task<string> RenderSmsTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var smsTemplateKey = $"{templateKey}_sms";
            var template = await _localizationService.GetTemplateAsync(smsTemplateKey, language, cancellationToken);
            
            if (template == null)
            {
                // Fallback to base template
                template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
            }

            if (template == null)
            {
                _logger.LogWarning("SMS template '{TemplateKey}' not found for language '{Language}'", templateKey, language);
                return "SMS content not available";
            }

            var rendered = RenderTemplateContent(template.Content, parameters);
            
            // Ensure SMS length limits (160 characters for single SMS)
            if (rendered.Length > 160)
            {
                _logger.LogWarning("SMS template '{TemplateKey}' rendered content exceeds 160 characters", templateKey);
            }

            return rendered;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render SMS template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return "SMS content not available";
        }
    }

    public async Task<List<TemplateInfo>> GetAvailableTemplatesAsync(
        string? category = null,
        Language? language = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = new List<TemplateInfo>();
            var languages = language.HasValue ? new[] { language.Value } : _localizationService.GetSupportedLanguages();

            foreach (var lang in languages)
            {
                var categoryTemplates = category != null 
                    ? await _localizationService.GetTemplatesAsync(category, lang, cancellationToken)
                    : await GetAllTemplatesAsync(lang, cancellationToken);

                templates.AddRange(categoryTemplates.Select(t => new TemplateInfo
                {
                    Key = t.Key,
                    Name = t.Name,
                    Description = t.Description,
                    Category = t.Category,
                    Language = t.Language,
                    Parameters = ExtractTemplateParameters(t.Content),
                    IsActive = t.IsActive,
                    LastUpdated = t.UpdatedAt
                }));
            }

            return templates.OrderBy(t => t.Category).ThenBy(t => t.Name).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available templates");
            return new List<TemplateInfo>();
        }
    }

    public async Task<bool> ValidateTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
            
            if (template == null)
            {
                return false;
            }

            var requiredParameters = ExtractTemplateParameters(template.Content);
            var missingParameters = requiredParameters.Where(p => !parameters.ContainsKey(p)).ToList();

            if (missingParameters.Any())
            {
                _logger.LogWarning("Template '{TemplateKey}' validation failed. Missing parameters: {MissingParameters}", 
                    templateKey, string.Join(", ", missingParameters));
                return false;
            }

            // Try to render the template to check for any issues
            var rendered = RenderTemplateContent(template.Content, parameters);
            return !string.IsNullOrEmpty(rendered);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate template '{TemplateKey}' for language '{Language}'", templateKey, language);
            return false;
        }
    }

    public async Task<TemplatePreview> PreviewTemplateAsync(
        string templateKey,
        Language language,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
            
            if (template == null)
            {
                return new TemplatePreview
                {
                    TemplateKey = templateKey,
                    Language = language,
                    IsValid = false,
                    ErrorMessage = "Template not found"
                };
            }

            var renderedContent = RenderTemplateContent(template.Content, parameters);
            var renderedSubject = template.Subject != null 
                ? RenderTemplateContent(template.Subject, parameters) 
                : null;

            return new TemplatePreview
            {
                TemplateKey = templateKey,
                Language = language,
                Subject = renderedSubject,
                Content = renderedContent,
                RequiredParameters = ExtractTemplateParameters(template.Content),
                ProvidedParameters = parameters.Keys.ToList(),
                IsValid = true,
                CharacterCount = renderedContent.Length,
                WordCount = renderedContent.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to preview template '{TemplateKey}' for language '{Language}'", templateKey, language);
            
            return new TemplatePreview
            {
                TemplateKey = templateKey,
                Language = language,
                IsValid = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<Dictionary<Language, string>> RenderMultiLanguageTemplateAsync(
        string templateKey,
        Dictionary<string, object> parameters,
        List<Language>? languages = null,
        CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<Language, string>();
        var targetLanguages = languages ?? _localizationService.GetSupportedLanguages();

        foreach (var language in targetLanguages)
        {
            try
            {
                var rendered = await _localizationService.RenderTemplateAsync(templateKey, language, parameters, cancellationToken);
                result[language] = rendered;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to render template '{TemplateKey}' for language '{Language}'", templateKey, language);
                result[language] = $"Template not available in {language}";
            }
        }

        return result;
    }

    private async Task<List<LocalizedTemplate>> GetAllTemplatesAsync(Language language, CancellationToken cancellationToken)
    {
        // This would need to be implemented in the repository to get all templates
        // For now, return empty list as placeholder
        return new List<LocalizedTemplate>();
    }

    private static string RenderTemplateContent(string template, Dictionary<string, object> parameters)
    {
        var result = template;
        
        foreach (var parameter in parameters)
        {
            // Support multiple placeholder formats
            var placeholders = new[]
            {
                $"{{{{{parameter.Key}}}}}", // {{key}}
                $"{{{parameter.Key}}}", // {key}
                $"[{parameter.Key}]", // [key]
                $"%{parameter.Key}%" // %key%
            };

            var value = parameter.Value?.ToString() ?? string.Empty;
            
            foreach (var placeholder in placeholders)
            {
                result = result.Replace(placeholder, value);
            }
        }

        return result;
    }

    private static List<string> ExtractTemplateParameters(string template)
    {
        var parameters = new HashSet<string>();
        
        // Extract parameters in different formats
        var patterns = new[]
        {
            @"\{\{(\w+)\}\}", // {{parameter}}
            @"\{(\w+)\}", // {parameter}
            @"\[(\w+)\]", // [parameter]
            @"%(\w+)%" // %parameter%
        };

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(template, pattern);
            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    parameters.Add(match.Groups[1].Value);
                }
            }
        }

        return parameters.ToList();
    }
}

/// <summary>
/// Template information
/// </summary>
public class TemplateInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Language Language { get; set; }
    public List<string> Parameters { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Template preview result
/// </summary>
public class TemplatePreview
{
    public string TemplateKey { get; set; } = string.Empty;
    public Language Language { get; set; }
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public List<string> RequiredParameters { get; set; } = new();
    public List<string> ProvidedParameters { get; set; } = new();
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public int CharacterCount { get; set; }
    public int WordCount { get; set; }
}
