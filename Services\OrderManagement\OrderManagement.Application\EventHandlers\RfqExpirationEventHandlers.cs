using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Application.EventHandlers;

/// <summary>
/// Event handler for RFQ expired domain events
/// </summary>
public class RfqExpiredEventHandler : INotificationHandler<RfqExpiredDomainEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqExpiredEventHandler> _logger;

    public RfqExpiredEventHandler(
        IMessageBroker messageBroker,
        ILogger<RfqExpiredEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqExpiredDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing RFQ expired event for RFQ {RfqId}", notification.RfqId);

        try
        {
            // Publish integration event for RFQ expiration
            await _messageBroker.PublishAsync("rfq.expired", new
            {
                RfqId = notification.RfqId,
                TransportCompanyId = notification.TransportCompanyId,
                Title = notification.Title,
                ExpiresAt = notification.ExpiresAt,
                ExpiredAt = DateTime.UtcNow,
                EventType = "RfqExpired",
                NotificationRequired = true,
                NotificationChannels = new[] { "Email", "Push", "SMS" },
                NotificationTemplate = "rfq_expired",
                NotificationData = new
                {
                    RfqTitle = notification.Title,
                    ExpiredAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC")
                }
            }, cancellationToken);

            _logger.LogInformation("Published RFQ expired event for RFQ {RfqId}", notification.RfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing RFQ expired event for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }
}

/// <summary>
/// Event handler for RFQ timeframe extended domain events
/// </summary>
public class RfqTimeframeExtendedEventHandler : INotificationHandler<RfqTimeframeExtendedDomainEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqTimeframeExtendedEventHandler> _logger;

    public RfqTimeframeExtendedEventHandler(
        IMessageBroker messageBroker,
        ILogger<RfqTimeframeExtendedEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqTimeframeExtendedDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing RFQ timeframe extended event for RFQ {RfqId}", notification.RfqId);

        try
        {
            // Publish integration event for RFQ timeframe extension
            await _messageBroker.PublishAsync("rfq.timeframe.extended", new
            {
                RfqId = notification.RfqId,
                TransportCompanyId = notification.TransportCompanyId,
                Extension = new
                {
                    Duration = notification.Extension.Duration,
                    Unit = notification.Extension.Unit.ToString(),
                    Reason = notification.Extension.Reason,
                    ExtendedAt = notification.Extension.ExtendedAt,
                    ExtendedBy = notification.Extension.ExtendedBy,
                    NewExpiresAt = notification.Extension.NewExpiresAt
                },
                NewExpiresAt = notification.NewExpiresAt,
                EventType = "RfqTimeframeExtended",
                NotificationRequired = true,
                NotificationChannels = new[] { "Email", "Push" },
                NotificationTemplate = "rfq_timeframe_extended",
                NotificationData = new
                {
                    ExtensionDuration = $"{notification.Extension.Duration} {notification.Extension.Unit}",
                    NewExpiresAt = notification.NewExpiresAt.ToString("yyyy-MM-dd HH:mm:ss UTC"),
                    Reason = notification.Extension.Reason
                }
            }, cancellationToken);

            _logger.LogInformation("Published RFQ timeframe extended event for RFQ {RfqId}", notification.RfqId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing RFQ timeframe extended event for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }
}

/// <summary>
/// Event handler for RFQ approaching expiration domain events
/// </summary>
public class RfqApproachingExpirationEventHandler : INotificationHandler<RfqApproachingExpirationDomainEvent>
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<RfqApproachingExpirationEventHandler> _logger;

    public RfqApproachingExpirationEventHandler(
        IMessageBroker messageBroker,
        ILogger<RfqApproachingExpirationEventHandler> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task Handle(RfqApproachingExpirationDomainEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing RFQ approaching expiration event for RFQ {RfqId}", notification.RfqId);

        try
        {
            var hoursRemaining = notification.RemainingTime.TotalHours;
            var urgencyLevel = hoursRemaining switch
            {
                <= 1 => "Critical",
                <= 6 => "High",
                <= 12 => "Medium",
                _ => "Low"
            };

            // Publish integration event for RFQ approaching expiration
            await _messageBroker.PublishAsync("rfq.approaching.expiration", new
            {
                RfqId = notification.RfqId,
                TransportCompanyId = notification.TransportCompanyId,
                Title = notification.Title,
                ExpiresAt = notification.ExpiresAt,
                RemainingTime = new
                {
                    TotalHours = notification.RemainingTime.TotalHours,
                    TotalMinutes = notification.RemainingTime.TotalMinutes,
                    DisplayText = FormatRemainingTime(notification.RemainingTime)
                },
                UrgencyLevel = urgencyLevel,
                EventType = "RfqApproachingExpiration",
                NotificationRequired = true,
                NotificationChannels = GetNotificationChannels(hoursRemaining),
                NotificationTemplate = GetNotificationTemplate(hoursRemaining),
                NotificationData = new
                {
                    RfqTitle = notification.Title,
                    ExpiresAt = notification.ExpiresAt.ToString("yyyy-MM-dd HH:mm:ss UTC"),
                    RemainingTime = FormatRemainingTime(notification.RemainingTime),
                    UrgencyLevel = urgencyLevel
                }
            }, cancellationToken);

            _logger.LogInformation("Published RFQ approaching expiration event for RFQ {RfqId}, remaining time: {RemainingTime}",
                notification.RfqId, FormatRemainingTime(notification.RemainingTime));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing RFQ approaching expiration event for RFQ {RfqId}", notification.RfqId);
            throw;
        }
    }

    private static string[] GetNotificationChannels(double hoursRemaining)
    {
        return hoursRemaining switch
        {
            <= 1 => new[] { "Email", "Push", "SMS" }, // Critical - all channels
            <= 6 => new[] { "Email", "Push" },        // High - email and push
            _ => new[] { "Email" }                     // Medium/Low - email only
        };
    }

    private static string GetNotificationTemplate(double hoursRemaining)
    {
        return hoursRemaining switch
        {
            <= 1 => "rfq_expiring_critical",
            <= 6 => "rfq_expiring_soon",
            <= 12 => "rfq_expiring_today",
            _ => "rfq_expiring_reminder"
        };
    }

    private static string FormatRemainingTime(TimeSpan remainingTime)
    {
        if (remainingTime.TotalDays >= 1)
            return $"{(int)remainingTime.TotalDays} day(s) {remainingTime.Hours} hour(s)";
        
        if (remainingTime.TotalHours >= 1)
            return $"{(int)remainingTime.TotalHours} hour(s) {remainingTime.Minutes} minute(s)";
        
        return $"{remainingTime.Minutes} minute(s)";
    }
}
