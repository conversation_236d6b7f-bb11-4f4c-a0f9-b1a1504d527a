using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Collections.Concurrent;

namespace FinancialPayment.Infrastructure.Services;

public class UsageAnalyticsService : IUsageAnalyticsService
{
    private readonly ILogger<UsageAnalyticsService> _logger;
    private readonly MonitoringConfiguration _configuration;
    
    // In-memory storage for demo purposes
    private readonly ConcurrentBag<UsageAnalytics> _usageRecords;

    public UsageAnalyticsService(
        ILogger<UsageAnalyticsService> logger,
        IOptions<MonitoringConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _usageRecords = new ConcurrentBag<UsageAnalytics>();
    }

    public async Task RecordUsageAsync(string resourceType, string resourceName, string action, bool isSuccess, Guid? userId = null, TimeSpan? duration = null, Dictionary<string, object>? properties = null)
    {
        try
        {
            if (!_configuration.EnableUsageAnalytics) return;

            var usage = new UsageAnalytics(
                resourceType,
                resourceName,
                action,
                isSuccess,
                userId,
                Guid.NewGuid().ToString(), // Session ID
                duration,
                isSuccess ? null : "ERROR",
                isSuccess ? null : "Operation failed",
                null,
                properties);

            _usageRecords.Add(usage);

            _logger.LogDebug("Recorded usage: {ResourceType}/{ResourceName} - {Action} - Success: {IsSuccess}",
                resourceType, resourceName, action, isSuccess);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording usage for {ResourceType}/{ResourceName}", resourceType, resourceName);
        }
    }

    public async Task<UsageAnalyticsSummary> GetUsageAnalyticsAsync(DateTime? fromDate = null, DateTime? toDate = null, string? resourceType = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var to = toDate ?? DateTime.UtcNow;

            var filteredRecords = _usageRecords
                .Where(u => u.Timestamp >= from && u.Timestamp <= to)
                .Where(u => string.IsNullOrEmpty(resourceType) || u.ResourceType == resourceType)
                .ToList();

            var totalRequests = filteredRecords.Count;
            var successfulRequests = filteredRecords.Count(u => u.IsSuccess);
            var failedRequests = totalRequests - successfulRequests;
            var successRate = totalRequests > 0 ? (decimal)successfulRequests / totalRequests * 100 : 0;

            var averageResponseTime = filteredRecords
                .Where(u => u.Duration.HasValue)
                .Select(u => u.Duration!.Value)
                .DefaultIfEmpty(TimeSpan.Zero)
                .Average(d => d.TotalMilliseconds);

            var uniqueUsers = filteredRecords
                .Where(u => u.UserId.HasValue)
                .Select(u => u.UserId!.Value)
                .Distinct()
                .Count();

            var requestsByResourceType = filteredRecords
                .GroupBy(u => u.ResourceType)
                .ToDictionary(g => g.Key, g => g.Count());

            var requestsByHour = filteredRecords
                .GroupBy(u => u.Timestamp.Hour)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var topErrors = filteredRecords
                .Where(u => !u.IsSuccess && !string.IsNullOrEmpty(u.ErrorCode))
                .GroupBy(u => new { u.ErrorCode, u.ErrorMessage })
                .Select(g => new ErrorSummary
                {
                    ErrorCode = g.Key.ErrorCode!,
                    ErrorMessage = g.Key.ErrorMessage ?? "Unknown error",
                    Count = g.Count(),
                    FirstOccurrence = g.Min(u => u.Timestamp),
                    LastOccurrence = g.Max(u => u.Timestamp),
                    AffectedResources = g.Select(u => u.ResourceName).Distinct().ToList()
                })
                .OrderByDescending(e => e.Count)
                .Take(10)
                .ToList();

            return new UsageAnalyticsSummary
            {
                PeriodStart = from,
                PeriodEnd = to,
                TotalRequests = totalRequests,
                SuccessfulRequests = successfulRequests,
                FailedRequests = failedRequests,
                SuccessRate = successRate,
                AverageResponseTime = TimeSpan.FromMilliseconds(averageResponseTime),
                UniqueUsers = uniqueUsers,
                RequestsByResourceType = requestsByResourceType,
                RequestsByHour = requestsByHour,
                TopErrors = topErrors
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage analytics");
            return new UsageAnalyticsSummary
            {
                PeriodStart = fromDate ?? DateTime.UtcNow.AddDays(-7),
                PeriodEnd = toDate ?? DateTime.UtcNow
            };
        }
    }

    public async Task<List<UsageAnalytics>> GetUsageHistoryAsync(string? resourceName = null, Guid? userId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var to = toDate ?? DateTime.UtcNow;

            var filteredRecords = _usageRecords
                .Where(u => u.Timestamp >= from && u.Timestamp <= to)
                .Where(u => string.IsNullOrEmpty(resourceName) || u.ResourceName == resourceName)
                .Where(u => !userId.HasValue || u.UserId == userId)
                .OrderByDescending(u => u.Timestamp)
                .Take(1000) // Limit to prevent memory issues
                .ToList();

            return await Task.FromResult(filteredRecords);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history");
            return new List<UsageAnalytics>();
        }
    }

    public async Task<List<TopUsedResource>> GetTopUsedResourcesAsync(string resourceType, DateTime? fromDate = null, DateTime? toDate = null, int limit = 10)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var to = toDate ?? DateTime.UtcNow;

            var topResources = _usageRecords
                .Where(u => u.Timestamp >= from && u.Timestamp <= to && u.ResourceType == resourceType)
                .GroupBy(u => u.ResourceName)
                .Select(g => new TopUsedResource
                {
                    ResourceName = g.Key,
                    ResourceType = resourceType,
                    UsageCount = g.Count(),
                    UniqueUsers = g.Where(u => u.UserId.HasValue).Select(u => u.UserId!.Value).Distinct().Count(),
                    AverageResponseTime = TimeSpan.FromMilliseconds(
                        g.Where(u => u.Duration.HasValue)
                         .Select(u => u.Duration!.Value.TotalMilliseconds)
                         .DefaultIfEmpty(0)
                         .Average()),
                    SuccessRate = g.Count() > 0 ? (decimal)g.Count(u => u.IsSuccess) / g.Count() * 100 : 0
                })
                .OrderByDescending(r => r.UsageCount)
                .Take(limit)
                .ToList();

            return await Task.FromResult(topResources);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top used resources for {ResourceType}", resourceType);
            return new List<TopUsedResource>();
        }
    }

    public async Task<List<UserActivitySummary>> GetUserActivityAsync(DateTime? fromDate = null, DateTime? toDate = null, int limit = 100)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var to = toDate ?? DateTime.UtcNow;

            var userActivities = _usageRecords
                .Where(u => u.Timestamp >= from && u.Timestamp <= to && u.UserId.HasValue)
                .GroupBy(u => u.UserId!.Value)
                .Select(g => new UserActivitySummary
                {
                    UserId = g.Key,
                    TotalRequests = g.Count(),
                    SuccessfulRequests = g.Count(u => u.IsSuccess),
                    FirstActivity = g.Min(u => u.Timestamp),
                    LastActivity = g.Max(u => u.Timestamp),
                    ResourcesUsed = g.Select(u => u.ResourceName).Distinct().ToList(),
                    TotalActiveTime = g.Max(u => u.Timestamp) - g.Min(u => u.Timestamp)
                })
                .OrderByDescending(a => a.TotalRequests)
                .Take(limit)
                .ToList();

            return await Task.FromResult(userActivities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user activity");
            return new List<UserActivitySummary>();
        }
    }

    public async Task<ApiUsageMetrics> GetApiUsageMetricsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var to = toDate ?? DateTime.UtcNow;

            var apiRecords = _usageRecords
                .Where(u => u.Timestamp >= from && u.Timestamp <= to && u.ResourceType == "API")
                .ToList();

            var totalApiCalls = apiRecords.Count;

            var callsByEndpoint = apiRecords
                .GroupBy(u => u.ResourceName)
                .ToDictionary(g => g.Key, g => g.Count());

            var callsByStatusCode = apiRecords
                .GroupBy(u => u.IsSuccess ? "200" : "500")
                .ToDictionary(g => g.Key, g => g.Count());

            var averageResponseTimeByEndpoint = apiRecords
                .Where(u => u.Duration.HasValue)
                .GroupBy(u => u.ResourceName)
                .ToDictionary(
                    g => g.Key,
                    g => TimeSpan.FromMilliseconds(g.Average(u => u.Duration!.Value.TotalMilliseconds)));

            var callsByUserAgent = apiRecords
                .Where(u => u.Properties.ContainsKey("user_agent"))
                .GroupBy(u => u.Properties["user_agent"].ToString() ?? "Unknown")
                .ToDictionary(g => g.Key, g => g.Count());

            // Simulate rate limit violations
            var rateLimitViolations = new List<ApiRateLimitViolation>();

            return new ApiUsageMetrics
            {
                PeriodStart = from,
                PeriodEnd = to,
                TotalApiCalls = totalApiCalls,
                CallsByEndpoint = callsByEndpoint,
                CallsByStatusCode = callsByStatusCode,
                AverageResponseTimeByEndpoint = averageResponseTimeByEndpoint,
                CallsByUserAgent = callsByUserAgent,
                RateLimitViolations = rateLimitViolations
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting API usage metrics");
            return new ApiUsageMetrics
            {
                PeriodStart = fromDate ?? DateTime.UtcNow.AddDays(-7),
                PeriodEnd = toDate ?? DateTime.UtcNow
            };
        }
    }
}
