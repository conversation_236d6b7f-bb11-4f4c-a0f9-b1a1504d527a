using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IHsnCodeRepository
{
    Task<HsnCode?> GetByIdAsync(Guid id);
    Task<HsnCode?> GetByCodeAsync(string code);
    Task<List<HsnCode>> GetAllAsync();
    Task<List<HsnCode>> GetActiveAsync();
    Task<List<HsnCode>> GetByStatusAsync(HsnCodeStatus status);
    Task<List<HsnCode>> GetByChapterAsync(string chapter);
    Task<List<HsnCode>> GetBySectionAsync(string section);
    Task<List<HsnCode>> GetByGstRateAsync(GstRate gstRate);
    Task<List<HsnCode>> GetByCategoryAsync(string category);
    Task<List<HsnCode>> GetEffectiveOnDateAsync(DateTime date);
    Task<List<HsnCode>> GetByCodePatternAsync(string pattern);
    Task<List<HsnCode>> GetByDescriptionContainsAsync(string description);
    Task<List<HsnCode>> GetByCreatedByAsync(string createdBy);
    Task<List<HsnCode>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<List<HsnCode>> GetWithGstMappingsAsync();
    Task<List<HsnCode>> GetByServiceCategoryAsync(ServiceCategory serviceCategory);
    Task<HsnCode> AddAsync(HsnCode hsnCode);
    Task UpdateAsync(HsnCode hsnCode);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> ExistsByCodeAsync(string code, Guid? excludeId = null);
    Task<int> GetCountByStatusAsync(HsnCodeStatus status);
    Task<List<HsnCode>> SearchAsync(string searchTerm, int skip = 0, int take = 50);
    Task<List<HsnCodeHistory>> GetHistoryAsync(Guid hsnCodeId);
    Task<List<HsnCodeGstMapping>> GetGstMappingsAsync(Guid hsnCodeId);
    Task<GstRate?> GetApplicableGstRateAsync(string code, DateTime date);
    Task<List<HsnCode>> BulkAddAsync(List<HsnCode> hsnCodes);
    Task BulkUpdateAsync(List<HsnCode> hsnCodes);
    Task<List<HsnCode>> GetExpiringSoonAsync(DateTime beforeDate);
}
