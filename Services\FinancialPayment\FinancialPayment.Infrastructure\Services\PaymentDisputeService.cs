using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class PaymentDisputeService : IPaymentDisputeService
{
    private readonly IPaymentDisputeRepository _disputeRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PaymentDisputeService> _logger;

    public PaymentDisputeService(
        IPaymentDisputeRepository disputeRepository,
        IUnitOfWork unitOfWork,
        ILogger<PaymentDisputeService> logger)
    {
        _disputeRepository = disputeRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<PaymentDispute> CreateDisputeAsync(
        Guid orderId,
        Guid initiatedBy,
        ParticipantRole initiatorRole,
        string title,
        string description,
        Money disputedAmount,
        DisputeCategory category,
        DisputePriority priority = DisputePriority.Medium,
        Guid? escrowAccountId = null,
        Guid? settlementId = null)
    {
        _logger.LogInformation("Creating payment dispute for order {OrderId} by {InitiatedBy}", orderId, initiatedBy);

        try
        {
            var dispute = new PaymentDispute(
                orderId,
                initiatedBy,
                initiatorRole,
                title,
                description,
                disputedAmount,
                category,
                priority,
                escrowAccountId,
                settlementId);

            await _disputeRepository.AddAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully created payment dispute {DisputeId} for order {OrderId}",
                dispute.Id, orderId);

            return dispute;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment dispute for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<bool> AddCommentAsync(Guid disputeId, Guid authorId, ParticipantRole authorRole, string content, bool isInternal = false)
    {
        _logger.LogInformation("Adding comment to dispute {DisputeId} by {AuthorId}", disputeId, authorId);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.AddComment(authorId, authorRole, content, isInternal);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully added comment to dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding comment to dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> AddDocumentAsync(Guid disputeId, string fileName, string fileUrl, string description, Guid uploadedBy)
    {
        _logger.LogInformation("Adding document to dispute {DisputeId}", disputeId);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.AddDocument(fileName, fileUrl, description, uploadedBy);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully added document to dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding document to dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> EscalateDisputeAsync(Guid disputeId, DisputePriority newPriority, string reason)
    {
        _logger.LogInformation("Escalating dispute {DisputeId} to priority {NewPriority}", disputeId, newPriority);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.Escalate(newPriority, reason);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully escalated dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error escalating dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> StartInvestigationAsync(Guid disputeId, Guid investigatorId)
    {
        _logger.LogInformation("Starting investigation for dispute {DisputeId} by {InvestigatorId}", disputeId, investigatorId);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.StartInvestigation(investigatorId);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully started investigation for dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting investigation for dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> ResolveDisputeAsync(Guid disputeId, Money resolvedAmount, string resolution, Guid resolvedBy)
    {
        _logger.LogInformation("Resolving dispute {DisputeId} with amount {ResolvedAmount}", disputeId, resolvedAmount);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.Resolve(resolvedAmount, resolution, resolvedBy);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully resolved dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> CloseDisputeAsync(Guid disputeId, string reason, Guid closedBy)
    {
        _logger.LogInformation("Closing dispute {DisputeId} with reason: {Reason}", disputeId, reason);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.Close(reason, closedBy);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully closed dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<bool> ReopenDisputeAsync(Guid disputeId, string reason, Guid reopenedBy)
    {
        _logger.LogInformation("Reopening dispute {DisputeId} with reason: {Reason}", disputeId, reason);

        try
        {
            var dispute = await _disputeRepository.GetByIdAsync(disputeId);
            if (dispute == null)
            {
                throw new InvalidOperationException($"Payment dispute {disputeId} not found");
            }

            dispute.Reopen(reason, reopenedBy);

            _disputeRepository.UpdateAsync(dispute);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully reopened dispute {DisputeId}", disputeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reopening dispute {DisputeId}", disputeId);
            throw;
        }
    }

    public async Task<PaymentDispute?> GetDisputeByIdAsync(Guid disputeId)
    {
        return await _disputeRepository.GetByIdAsync(disputeId);
    }

    public async Task<List<PaymentDispute>> GetDisputesByOrderIdAsync(Guid orderId)
    {
        return await _disputeRepository.GetByOrderIdAsync(orderId);
    }

    public async Task<List<PaymentDispute>> GetDisputesByInitiatorAsync(Guid initiatorId)
    {
        return await _disputeRepository.GetByInitiatorIdAsync(initiatorId);
    }

    public async Task<List<PaymentDispute>> GetDisputesByStatusAsync(DisputeStatus status)
    {
        return await _disputeRepository.GetByStatusAsync(status);
    }

    public async Task<List<PaymentDispute>> GetDisputesByCategoryAsync(DisputeCategory category)
    {
        return await _disputeRepository.GetByCategoryAsync(category);
    }

    public async Task<List<PaymentDispute>> GetDisputesByPriorityAsync(DisputePriority priority)
    {
        return await _disputeRepository.GetByPriorityAsync(priority);
    }
}
