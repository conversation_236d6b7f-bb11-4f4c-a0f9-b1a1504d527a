using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetOrders;

public class GetOrdersQuery : IRequest<PagedResult<OrderSummaryDto>>
{
    public Guid? UserId { get; set; } // For filtering by user (TransportCompany, Broker, or Carrier)
    public string? UserRole { get; set; } // "TransportCompany", "Broker", "Carrier"
    public OrderStatus? Status { get; set; }
    public PaymentStatus? PaymentStatus { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; } // Search in order number, title, pickup/delivery cities
    public bool? IsUrgent { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt"; // CreatedAt, OrderNumber, Status, etc.
    public string? SortDirection { get; set; } = "desc"; // asc, desc
}
