using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application;
using SubscriptionManagement.Infrastructure;
using SubscriptionManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Caching;
using Shared.Messaging;
using System.CommandLine;

namespace SubscriptionManagement.Tests
{
    public class TestRunner
    {
        public static async Task<int> Main(string[] args)
        {
            var rootCommand = new RootCommand("Subscription Management Test Runner");

            var unitTestCommand = new Command("unit", "Run unit tests");
            var integrationTestCommand = new Command("integration", "Run integration tests");
            var performanceTestCommand = new Command("performance", "Run performance tests");
            var allTestsCommand = new Command("all", "Run all tests");

            // Add test category options
            var categoryOption = new Option<string[]>(
                "--category",
                description: "Test categories to run")
            {
                AllowMultipleArgumentsPerToken = true
            };

            var verboseOption = new Option<bool>(
                "--verbose",
                description: "Enable verbose logging");

            var parallelOption = new Option<bool>(
                "--parallel",
                description: "Run tests in parallel")
            {
                IsRequired = false
            };
            parallelOption.SetDefaultValue(true);

            // Add options to commands
            foreach (var command in new[] { unitTestCommand, integrationTestCommand, performanceTestCommand, allTestsCommand })
            {
                command.AddOption(categoryOption);
                command.AddOption(verboseOption);
                command.AddOption(parallelOption);
            }

            // Set up command handlers
            unitTestCommand.SetHandler(async (string[] categories, bool verbose, bool parallel) =>
            {
                await RunTests("unit", categories, verbose, parallel);
            }, categoryOption, verboseOption, parallelOption);

            integrationTestCommand.SetHandler(async (string[] categories, bool verbose, bool parallel) =>
            {
                await RunTests("integration", categories, verbose, parallel);
            }, categoryOption, verboseOption, parallelOption);

            performanceTestCommand.SetHandler(async (string[] categories, bool verbose, bool parallel) =>
            {
                await RunTests("performance", categories, verbose, parallel);
            }, categoryOption, verboseOption, parallelOption);

            allTestsCommand.SetHandler(async (string[] categories, bool verbose, bool parallel) =>
            {
                await RunTests("all", categories, verbose, parallel);
            }, categoryOption, verboseOption, parallelOption);

            rootCommand.AddCommand(unitTestCommand);
            rootCommand.AddCommand(integrationTestCommand);
            rootCommand.AddCommand(performanceTestCommand);
            rootCommand.AddCommand(allTestsCommand);

            return await rootCommand.InvokeAsync(args);
        }

        private static async Task RunTests(string testType, string[] categories, bool verbose, bool parallel)
        {
            Console.WriteLine($"Running {testType} tests...");
            
            if (categories.Any())
            {
                Console.WriteLine($"Categories: {string.Join(", ", categories)}");
            }

            var serviceProvider = await SetupTestEnvironment(verbose);
            
            try
            {
                switch (testType.ToLower())
                {
                    case "unit":
                        await RunUnitTests(serviceProvider, categories, parallel);
                        break;
                    case "integration":
                        await RunIntegrationTests(serviceProvider, categories, parallel);
                        break;
                    case "performance":
                        await RunPerformanceTests(serviceProvider, categories);
                        break;
                    case "all":
                        await RunUnitTests(serviceProvider, categories, parallel);
                        await RunIntegrationTests(serviceProvider, categories, parallel);
                        await RunPerformanceTests(serviceProvider, categories);
                        break;
                }

                Console.WriteLine($"✅ {testType} tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ {testType} tests failed: {ex.Message}");
                throw;
            }
            finally
            {
                serviceProvider.Dispose();
            }
        }

        private static async Task<IServiceProvider> SetupTestEnvironment(bool verbose)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .Build();

            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                if (verbose)
                {
                    builder.SetMinimumLevel(LogLevel.Debug);
                }
                else
                {
                    builder.SetMinimumLevel(LogLevel.Information);
                }
            });

            // Add test database
            var useInMemoryDb = configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase");
            if (useInMemoryDb)
            {
                services.AddDbContext<SubscriptionDbContext>(options =>
                    options.UseInMemoryDatabase("SubscriptionManagement_Test"));
            }
            else
            {
                services.AddDbContext<SubscriptionDbContext>(options =>
                    options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));
            }

            // Add application layers
            services.AddApplication();
            services.AddInfrastructure(configuration);

            // Add caching for tests
            if (configuration.GetValue<bool>("TestSettings:EnableIntegrationTests"))
            {
                services.AddCaching(configuration);
            }
            else
            {
                // Use in-memory cache for unit tests
                services.AddMemoryCache();
            }

            // Add messaging for tests
            services.AddMessaging(configuration.GetValue<string>("RabbitMQ:HostName", "localhost"));

            var serviceProvider = services.BuildServiceProvider();

            // Initialize test database
            await InitializeTestDatabase(serviceProvider, configuration);

            return serviceProvider;
        }

        private static async Task InitializeTestDatabase(IServiceProvider serviceProvider, IConfiguration configuration)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<SubscriptionDbContext>();

            var useInMemoryDb = configuration.GetValue<bool>("TestSettings:UseInMemoryDatabase");
            if (useInMemoryDb)
            {
                await context.Database.EnsureCreatedAsync();
            }
            else
            {
                await context.Database.MigrateAsync();
            }

            // Seed test data if configured
            if (configuration.GetValue<bool>("TestSettings:TestDataSeed"))
            {
                await SeedTestData(context);
            }
        }

        private static async Task SeedTestData(SubscriptionDbContext context)
        {
            // Add test plans if they don't exist
            if (!context.Plans.Any())
            {
                var basicPlan = new SubscriptionManagement.Domain.Entities.Plan(
                    "Basic Plan",
                    "Basic subscription plan",
                    SubscriptionManagement.Domain.ValueObjects.Money.Create(9.99m, "USD"),
                    SubscriptionManagement.Domain.Enums.BillingCycle.Monthly,
                    SubscriptionManagement.Domain.Enums.PlanType.Basic,
                    SubscriptionManagement.Domain.Enums.UserType.Individual
                );

                var premiumPlan = new SubscriptionManagement.Domain.Entities.Plan(
                    "Premium Plan",
                    "Premium subscription plan",
                    SubscriptionManagement.Domain.ValueObjects.Money.Create(19.99m, "USD"),
                    SubscriptionManagement.Domain.Enums.BillingCycle.Monthly,
                    SubscriptionManagement.Domain.Enums.PlanType.Premium,
                    SubscriptionManagement.Domain.Enums.UserType.Individual
                );

                context.Plans.AddRange(basicPlan, premiumPlan);
                await context.SaveChangesAsync();
            }
        }

        private static async Task RunUnitTests(IServiceProvider serviceProvider, string[] categories, bool parallel)
        {
            Console.WriteLine("🧪 Running unit tests...");
            
            // In a real implementation, you would use a test framework like xUnit
            // Here we're simulating the test execution
            var testClasses = new[]
            {
                typeof(UpgradeSubscriptionCommandHandlerTests),
                typeof(UsageTrackingServiceTests),
                typeof(SubscriptionMetricsServiceTests),
                typeof(FeatureFlagServiceTests)
            };

            var tasks = new List<Task>();
            
            foreach (var testClass in testClasses)
            {
                if (categories.Any() && !categories.Any(c => testClass.Name.Contains(c, StringComparison.OrdinalIgnoreCase)))
                {
                    continue;
                }

                if (parallel)
                {
                    tasks.Add(Task.Run(() => ExecuteTestClass(testClass, serviceProvider)));
                }
                else
                {
                    await ExecuteTestClass(testClass, serviceProvider);
                }
            }

            if (parallel && tasks.Any())
            {
                await Task.WhenAll(tasks);
            }

            Console.WriteLine($"✅ Unit tests completed. Executed {testClasses.Length} test classes.");
        }

        private static async Task RunIntegrationTests(IServiceProvider serviceProvider, string[] categories, bool parallel)
        {
            Console.WriteLine("🔗 Running integration tests...");
            
            var testClasses = new[]
            {
                typeof(CacheIntegrationTests)
            };

            foreach (var testClass in testClasses)
            {
                if (categories.Any() && !categories.Any(c => testClass.Name.Contains(c, StringComparison.OrdinalIgnoreCase)))
                {
                    continue;
                }

                await ExecuteTestClass(testClass, serviceProvider);
            }

            Console.WriteLine($"✅ Integration tests completed. Executed {testClasses.Length} test classes.");
        }

        private static async Task RunPerformanceTests(IServiceProvider serviceProvider, string[] categories)
        {
            Console.WriteLine("⚡ Running performance tests...");
            
            // Performance tests are typically not run in parallel due to resource constraints
            Console.WriteLine("Performance tests would be executed here using NBomber and BenchmarkDotNet");
            
            // Simulate performance test execution
            await Task.Delay(1000);
            
            Console.WriteLine("✅ Performance tests completed.");
        }

        private static async Task ExecuteTestClass(Type testClass, IServiceProvider serviceProvider)
        {
            Console.WriteLine($"  Running {testClass.Name}...");
            
            // In a real implementation, you would use reflection to find and execute test methods
            // This is a simplified simulation
            await Task.Delay(100); // Simulate test execution time
            
            Console.WriteLine($"  ✅ {testClass.Name} completed");
        }
    }
}
