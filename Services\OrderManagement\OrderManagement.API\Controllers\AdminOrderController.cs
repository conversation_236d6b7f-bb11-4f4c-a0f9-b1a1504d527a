using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.ForceOrderClosure;
using OrderManagement.Application.Commands.BulkUpdateOrderStatus;
using OrderManagement.Application.Commands.AdminOverrideOrderStatus;
using OrderManagement.Application.Queries.GetOrdersForAdmin;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/admin/orders")]
[ApiVersion("1.0")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminOrderController : BaseController
{
    /// <summary>
    /// Get orders with admin-specific details and filtering
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<AdminOrderDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOrders(
        [FromQuery] AdminOrderFilterRequest filter)
    {
        try
        {
            var query = new GetOrdersForAdminQuery
            {
                Page = filter.Page,
                PageSize = filter.PageSize,
                Status = filter.Status,
                TransportCompanyId = filter.TransportCompanyId,
                BrokerId = filter.BrokerId,
                CarrierId = filter.CarrierId,
                CreatedFrom = filter.CreatedFrom,
                CreatedTo = filter.CreatedTo,
                OrderNumber = filter.OrderNumber,
                IncludeTimeline = filter.IncludeTimeline,
                IncludeDocuments = filter.IncludeDocuments,
                IncludeAuditTrail = filter.IncludeAuditTrail,
                SortBy = filter.SortBy,
                SortDirection = filter.SortDirection
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving orders for admin");
            return StatusCode(500, "An error occurred while retrieving orders");
        }
    }

    /// <summary>
    /// Get detailed order information for admin
    /// </summary>
    [HttpGet("{orderId:guid}")]
    [ProducesResponseType(typeof(AdminOrderDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOrderDetail(Guid orderId)
    {
        try
        {
            var query = new GetOrderDetailForAdminQuery { OrderId = orderId };
            var result = await Mediator.Send(query);

            if (result == null)
                return NotFound($"Order {orderId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving order detail for admin: {OrderId}", orderId);
            return StatusCode(500, "An error occurred while retrieving order details");
        }
    }

    /// <summary>
    /// Force close an order with admin override
    /// </summary>
    [HttpPost("{orderId:guid}/force-close")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> ForceCloseOrder(
        Guid orderId,
        [FromBody] ForceOrderClosureRequest request)
    {
        try
        {
            var command = new ForceOrderClosureCommand
            {
                OrderId = orderId,
                Reason = request.Reason,
                AdminNotes = request.AdminNotes,
                NotifyParties = request.NotifyParties,
                AdminUserId = GetCurrentUserId(),
                AdminUserName = GetCurrentUserName(),
                IpAddress = GetClientIpAddress(),
                UserAgent = GetUserAgent()
            };

            await Mediator.Send(command);

            Logger.LogInformation("Order {OrderId} force closed by admin {AdminId}", 
                orderId, GetCurrentUserId());

            return Ok(new { Message = "Order force closed successfully" });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid force closure request for order {OrderId}", orderId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error force closing order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while force closing the order");
        }
    }

    /// <summary>
    /// Override order status with admin privileges
    /// </summary>
    [HttpPost("{orderId:guid}/override-status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> OverrideOrderStatus(
        Guid orderId,
        [FromBody] AdminOverrideStatusRequest request)
    {
        try
        {
            var command = new AdminOverrideOrderStatusCommand
            {
                OrderId = orderId,
                NewStatus = request.NewStatus,
                Reason = request.Reason,
                AdminNotes = request.AdminNotes,
                BypassValidation = request.BypassValidation,
                AdminUserId = GetCurrentUserId(),
                AdminUserName = GetCurrentUserName(),
                IpAddress = GetClientIpAddress(),
                UserAgent = GetUserAgent()
            };

            await Mediator.Send(command);

            Logger.LogInformation("Order {OrderId} status overridden to {NewStatus} by admin {AdminId}", 
                orderId, request.NewStatus, GetCurrentUserId());

            return Ok(new { Message = "Order status overridden successfully" });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid status override request for order {OrderId}", orderId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error overriding status for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while overriding order status");
        }
    }

    /// <summary>
    /// Bulk update order statuses
    /// </summary>
    [HttpPost("bulk-update-status")]
    [ProducesResponseType(typeof(BulkUpdateResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> BulkUpdateOrderStatus([FromBody] BulkUpdateOrderStatusRequest request)
    {
        try
        {
            var command = new BulkUpdateOrderStatusCommand
            {
                OrderIds = request.OrderIds,
                NewStatus = request.NewStatus,
                Reason = request.Reason,
                AdminNotes = request.AdminNotes,
                AdminUserId = GetCurrentUserId(),
                AdminUserName = GetCurrentUserName(),
                IpAddress = GetClientIpAddress(),
                UserAgent = GetUserAgent()
            };

            var result = await Mediator.Send(command);

            Logger.LogInformation("Bulk status update completed by admin {AdminId}: {SuccessCount} successful, {FailureCount} failed", 
                GetCurrentUserId(), result.SuccessCount, result.FailureCount);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid bulk update request");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error performing bulk status update");
            return StatusCode(500, "An error occurred while performing bulk update");
        }
    }

    /// <summary>
    /// Get order audit trail
    /// </summary>
    [HttpGet("{orderId:guid}/audit-trail")]
    [ProducesResponseType(typeof(List<OrderAuditTrailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOrderAuditTrail(Guid orderId)
    {
        try
        {
            var query = new GetOrderAuditTrailQuery { OrderId = orderId };
            var result = await Mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving audit trail for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while retrieving audit trail");
        }
    }

    /// <summary>
    /// Add admin note to order
    /// </summary>
    [HttpPost("{orderId:guid}/notes")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> AddAdminNote(
        Guid orderId,
        [FromBody] AddAdminNoteRequest request)
    {
        try
        {
            var command = new AddAdminNoteCommand
            {
                OrderId = orderId,
                Note = request.Note,
                IsInternal = request.IsInternal,
                AdminUserId = GetCurrentUserId(),
                AdminUserName = GetCurrentUserName()
            };

            var noteId = await Mediator.Send(command);

            Logger.LogInformation("Admin note added to order {OrderId} by admin {AdminId}", 
                orderId, GetCurrentUserId());

            return CreatedAtAction(nameof(GetOrderDetail), new { orderId }, new { NoteId = noteId });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid admin note request for order {OrderId}", orderId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error adding admin note to order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while adding admin note");
        }
    }

    /// <summary>
    /// Get order statistics for admin dashboard
    /// </summary>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(AdminOrderStatisticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetOrderStatistics([FromQuery] AdminStatisticsRequest request)
    {
        try
        {
            var query = new GetAdminOrderStatisticsQuery
            {
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                TransportCompanyId = request.TransportCompanyId,
                BrokerId = request.BrokerId
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving order statistics for admin");
            return StatusCode(500, "An error occurred while retrieving statistics");
        }
    }

    private string? GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString();
    }

    private string? GetUserAgent()
    {
        return HttpContext.Request.Headers["User-Agent"].FirstOrDefault();
    }
}
