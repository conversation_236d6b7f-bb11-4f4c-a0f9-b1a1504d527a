# TLI Logistics Microservices - Getting Started

## 🎉 Welcome to TLI Logistics Microservices!

This is a comprehensive logistics microservices solution built with .NET 8, featuring clean architecture, domain-driven design, and modern development practices for logistics and supply chain management.

## 📋 What's Included

### ✅ Complete Solution Structure

- **Master Solution File**: `TLIMicroservices.sln` with all projects organized
- **Identity Microservice**: Full-featured authentication and user management
- **API Gateway**: Ocelot-based gateway for routing and load balancing
- **Shared Components**: Reusable libraries for messaging, domain, and infrastructure
- **Logistics-Ready Architecture**: Foundation for building logistics services

### ✅ Infrastructure Components

- **PostgreSQL**: Database for all services
- **RabbitMQ**: Message broker for inter-service communication
- **Redis**: Caching and session storage
- **Docker**: Complete containerization setup

### ✅ Development Tools

- **PowerShell Scripts**: Automated setup and management
- **Docker Compose**: Local development environment
- **Health Checks**: Service monitoring and diagnostics
- **Comprehensive Documentation**: API docs, deployment guides, and more

## 🚀 Quick Start (5 Minutes)

### 1. Prerequisites Check

Ensure you have:

- ✅ .NET 8 SDK
- ✅ Docker Desktop
- ✅ Visual Studio 2022 or VS Code

### 2. One-Command Setup

```powershell
# Clone and setup everything
.\scripts\setup-dev-environment.ps1
```

### 3. Start Services

```powershell
# Start all services
.\scripts\start-services.ps1
```

### 4. Access Your Services

- **API Gateway**: http://localhost:5000/swagger
- **Identity API**: http://localhost:5001/swagger
- **RabbitMQ Management**: http://localhost:15672 (guest/guest)

## 📁 Project Structure

```
TLIMicroservices/
├── 📁 Services/
│   ├── 📁 Identity/              # Authentication & User Management
│   │   ├── Identity.API/         # REST API endpoints
│   │   ├── Identity.Application/ # Business logic & use cases
│   │   ├── Identity.Domain/      # Domain entities & rules
│   │   ├── Identity.Infrastructure/ # Data access & external services
│   │   └── Identity.Tests/       # Unit & integration tests
│
├── 📁 Shared/                    # Reusable components
│   ├── Shared.Domain/           # Common domain primitives
│   ├── Shared.Infrastructure/   # Infrastructure utilities
│   └── Shared.Messaging/        # Event messaging with RabbitMQ
├── 📁 ApiGateway/               # Ocelot API Gateway
├── 📁 scripts/                  # PowerShell automation scripts
├── 📁 docs/                     # Comprehensive documentation
├── 📁 templates/                # New service templates
├── 🐳 docker-compose.yml        # Container orchestration
└── 📄 TLIMicroservices.sln     # Master solution file
```

## 🛠️ Available Scripts

### Development Scripts

```powershell
# Setup development environment
.\scripts\setup-dev-environment.ps1

# Start infrastructure (PostgreSQL, RabbitMQ, Redis)
.\scripts\start-infrastructure.ps1

# Start all microservices
.\scripts\start-services.ps1

# Stop all services
.\scripts\stop-services.ps1

# Build entire solution
.\scripts\build-all.ps1

# Run all tests
.\scripts\run-tests.ps1
```

### Service Management

```powershell
# Create a new microservice
.\templates\create-new-service.ps1 -ServiceName "YourService"
```

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

### 1. Start Infrastructure

```bash
docker-compose up -d postgres rabbitmq redis
```

### 2. Build Solution

```bash
dotnet build TLIMicroservices.sln
```

### 3. Run Services

```bash
# Terminal 1: Identity Service
cd Identity/Identity.API
dotnet run

# Terminal 2: API Gateway
cd ApiGateway
dotnet run
```

## 🧪 Testing Your Setup

### 1. Health Checks

```bash
# Check API Gateway health
curl http://localhost:5000/health

# Check Identity Service health
curl http://localhost:5001/health
```

### 2. API Testing

```bash
# Register a new user
curl -X POST http://localhost:5000/api/v1/identity/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123!",
    "phoneNumber": "+**********",
    "countryCode": "US",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### 3. Run Tests

```bash
dotnet test TLIMicroservices.sln
```

## 📚 Next Steps

### 1. Explore the Documentation

- 📖 [Development Guide](docs/DEVELOPMENT_GUIDE.md)
- 🔌 [API Documentation](docs/API_DOCUMENTATION.md)
- 🚀 [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)

### 2. Add Your First Logistics Microservice

```powershell
# Create a new logistics service
.\templates\create-new-service.ps1 -ServiceName "Shipment"

# Add to solution
dotnet sln add Services/Shipment/Shipment.Domain/Shipment.Domain.csproj
# ... add other projects

# Update API Gateway routing in ApiGateway/ocelot.json
```

**Suggested Logistics Services:**

- **Shipment**: Track packages and deliveries
- **Fleet**: Manage vehicles and drivers
- **Route**: Optimize delivery routes
- **Warehouse**: Inventory and storage management
- **Order**: Order processing and fulfillment

### 3. Customize Configuration

- Update connection strings in `appsettings.Development.json`
- Modify JWT settings for your environment
- Configure RabbitMQ and Redis connections

## 🆘 Troubleshooting

### Common Issues

#### Build Errors

```bash
# Clean and rebuild
dotnet clean TLIMicroservices.sln
dotnet build TLIMicroservices.sln
```

#### Docker Issues

```bash
# Restart Docker containers
docker-compose down
docker-compose up -d
```

#### Port Conflicts

- API Gateway: Port 5000
- Identity Service: Port 5001
- PostgreSQL: Port 5432
- RabbitMQ: Ports 5672, 15672
- Redis: Port 6379

### Getting Help

1. Check the logs in the `logs/` directory
2. Use health check endpoints: `/health`
3. Review the troubleshooting section in [Development Guide](docs/DEVELOPMENT_GUIDE.md)

## 🎯 Key Features

### ✨ Identity Service

- User registration and authentication
- JWT token management
- Role-based authorization
- Email confirmation
- Password management
- Two-factor authentication support

### ✨ API Gateway

- Request routing and load balancing
- Centralized authentication
- Rate limiting and throttling
- Request/response transformation
- Health monitoring

### ✨ Shared Components

- Event-driven messaging with RabbitMQ
- Common domain primitives
- Centralized logging with Serilog
- Caching with Redis
- Health checks and monitoring

### ✨ Development Experience

- Clean Architecture principles
- Domain-Driven Design patterns
- CQRS and MediatR
- Comprehensive testing
- Docker containerization
- Automated scripts

## 🚀 Production Deployment

### Docker Deployment

```bash
# Build and deploy with Docker Compose
docker-compose up --build -d
```

### Cloud Deployment

- AWS ECS/Fargate
- Azure Container Instances
- Google Cloud Run
- Kubernetes

See [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) for detailed instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the coding standards
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Happy Coding! 🎉**

For questions or support, please check the documentation or create an issue in the repository.
