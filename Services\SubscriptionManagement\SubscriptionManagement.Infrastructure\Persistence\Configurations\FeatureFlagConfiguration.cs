using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Persistence.Configurations;

public class FeatureFlagConfiguration : IEntityTypeConfiguration<FeatureFlag>
{
    public void Configure(EntityTypeBuilder<FeatureFlag> builder)
    {
        builder.ToTable("FeatureFlags");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Key)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Description)
            .HasMaxLength(500);

        builder.Property(f => f.Type)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(f => f.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(f => f.StartDate);

        builder.Property(f => f.EndDate);

        builder.Property(f => f.TargetAudience)
            .HasMaxLength(200);

        builder.Property(f => f.ABTestConfiguration)
            .HasMaxLength(1000);

        builder.Property(f => f.DefaultValue)
            .HasMaxLength(500);

        builder.Property(f => f.Variants)
            .HasMaxLength(1000);

        // Configure the Metadata property as JSON
        builder.Property(f => f.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb"); // PostgreSQL JSONB type

        builder.Property(f => f.RolloutPercentage)
            .HasDefaultValue(0);

        builder.Property(f => f.CreatedAt)
            .IsRequired();

        builder.Property(f => f.UpdatedAt);

        // Indexes
        builder.HasIndex(f => f.Key)
            .IsUnique();

        builder.HasIndex(f => f.Name);

        builder.HasIndex(f => f.Type);

        builder.HasIndex(f => f.Status);

        // Relationships
        builder.HasMany(f => f.Rules)
            .WithOne()
            .HasForeignKey("FeatureFlagId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(f => f.UsageHistory)
            .WithOne()
            .HasForeignKey("FeatureFlagId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}
