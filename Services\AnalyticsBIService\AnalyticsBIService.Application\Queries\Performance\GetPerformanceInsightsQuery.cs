using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Performance;

/// <summary>
/// Query to get performance insights for a specific user
/// </summary>
public class GetPerformanceInsightsQuery : IRequest<PerformanceInsightsDto>
{
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }

    public GetPerformanceInsightsQuery(Guid userId, UserType userType)
    {
        UserId = userId;
        UserType = userType;
    }
}

/// <summary>
/// Query to get quote to order conversion rate
/// </summary>
public class GetQuoteToOrderConversionRateQuery : IRequest<decimal>
{
    public Guid UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetQuoteToOrderConversionRateQuery(Guid userId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        UserId = userId;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get expired RFQ ratio
/// </summary>
public class GetExpiredRFQRatioQuery : IRequest<decimal>
{
    public Guid UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetExpiredRFQRatioQuery(Guid userId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        UserId = userId;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get driver performance metrics
/// </summary>
public class GetDriverPerformanceQuery : IRequest<DriverPerformanceDto>
{
    public Guid DriverId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetDriverPerformanceQuery(Guid driverId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        DriverId = driverId;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get milestone compliance rate
/// </summary>
public class GetMilestoneComplianceRateQuery : IRequest<decimal>
{
    public Guid UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetMilestoneComplianceRateQuery(Guid userId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        UserId = userId;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to get performance trends
/// </summary>
public class GetPerformanceTrendsQuery : IRequest<List<PerformanceTrendDto>>
{
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
    public TimePeriod Period { get; set; } = TimePeriod.Daily;

    public GetPerformanceTrendsQuery(Guid userId, UserType userType, TimePeriod period = TimePeriod.Daily)
    {
        UserId = userId;
        UserType = userType;
        Period = period;
    }
}
