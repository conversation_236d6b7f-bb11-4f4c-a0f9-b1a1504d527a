using FluentValidation;
using Identity.Domain.Entities;

namespace Identity.Application.Auth.Commands.RegisterUser
{
    public class RegisterUserCommandValidator : AbstractValidator<RegisterUserCommand>
    {
        public RegisterUserCommandValidator()
        {
            RuleFor(x => x.Username)
                .NotEmpty()
                .WithMessage("Username is required")
                .Length(3, 50)
                .WithMessage("Username must be between 3 and 50 characters")
                .Matches(@"^[a-zA-Z0-9_.-]+$")
                .WithMessage("Username can only contain letters, numbers, dots, hyphens, and underscores");

            RuleFor(x => x.Email)
                .NotEmpty()
                .WithMessage("Email is required")
                .EmailAddress()
                .WithMessage("Email must be a valid email address")
                .MaximumLength(320)
                .WithMessage("Email cannot exceed 320 characters");

            RuleFor(x => x.Password)
                .NotEmpty()
                .WithMessage("Password is required")
                .MinimumLength(6)
                .WithMessage("Password must be at least 6 characters long")
                .MaximumLength(100)
                .WithMessage("Password cannot exceed 100 characters");

            RuleFor(x => x.PhoneNumber)
                .Matches(@"^\+?[1-9]\d{1,14}$")
                .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber))
                .WithMessage("Phone number must be a valid international format");

            RuleFor(x => x.CountryCode)
                .Length(2, 3)
                .When(x => !string.IsNullOrWhiteSpace(x.CountryCode))
                .WithMessage("Country code must be 2 or 3 characters");

            RuleFor(x => x.FirstName)
                .MaximumLength(100)
                .WithMessage("First name cannot exceed 100 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.FirstName));

            RuleFor(x => x.LastName)
                .MaximumLength(100)
                .WithMessage("Last name cannot exceed 100 characters")
                .When(x => !string.IsNullOrWhiteSpace(x.LastName));

            RuleFor(x => x.UserType)
                .IsInEnum()
                .WithMessage("UserType must be a valid value: Admin, SubAdmin, TransportCompany, Broker, Carrier, Driver, or Shipper")
                .Must(BeAValidUserTypeForRegistration)
                .WithMessage("UserType must be one of: TransportCompany, Broker, Carrier, Driver, or Shipper. Admin and SubAdmin accounts cannot be created through public registration.");
        }

        private static bool BeAValidUserTypeForRegistration(UserType userType)
        {
            // Restrict Admin and SubAdmin from public registration for security
            return userType != UserType.Admin && userType != UserType.SubAdmin;
        }
    }
}
