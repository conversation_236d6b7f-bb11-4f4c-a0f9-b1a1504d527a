# AnalyticsBIService Production Deployment Script (PowerShell)
# This script deploys the AnalyticsBIService to production environment

param(
    [switch]$SkipBackup,
    [switch]$Force
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$ComposeFile = Join-Path $ProjectDir "docker-compose.prod.yml"
$EnvFile = Join-Path $ProjectDir ".env"

# Functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    try {
        $dockerVersion = docker --version
        if (-not $dockerVersion) {
            throw "Docker not found"
        }
    }
    catch {
        Write-Error "Docker is not installed or not in PATH. Please install Docker Desktop."
        exit 1
    }
    
    try {
        docker info | Out-Null
    }
    catch {
        Write-Error "Docker is not running. Please start Docker Desktop."
        exit 1
    }
    
    # Check if Docker Compose is available
    try {
        $composeVersion = docker-compose --version
        if (-not $composeVersion) {
            # Try docker compose (newer syntax)
            docker compose version | Out-Null
        }
    }
    catch {
        Write-Error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    }
    
    # Check if environment file exists
    if (-not (Test-Path $EnvFile)) {
        Write-Error "Environment file not found at $EnvFile"
        Write-Info "Please copy .env.template to .env and configure it with your production values."
        exit 1
    }
    
    # Check if compose file exists
    if (-not (Test-Path $ComposeFile)) {
        Write-Error "Docker Compose file not found at $ComposeFile"
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

function Test-Environment {
    Write-Info "Validating environment configuration..."
    
    # Read environment file
    $envContent = Get-Content $EnvFile | Where-Object { $_ -match "^[^#].*=" }
    $envVars = @{}
    
    foreach ($line in $envContent) {
        $parts = $line -split "=", 2
        if ($parts.Length -eq 2) {
            $envVars[$parts[0].Trim()] = $parts[1].Trim()
        }
    }
    
    # Check required environment variables
    $requiredVars = @(
        "POSTGRES_PASSWORD",
        "REDIS_PASSWORD", 
        "JWT_SECRET_KEY",
        "GRAFANA_ADMIN_PASSWORD"
    )
    
    foreach ($var in $requiredVars) {
        if (-not $envVars.ContainsKey($var) -or [string]::IsNullOrWhiteSpace($envVars[$var])) {
            Write-Error "Required environment variable $var is not set"
            exit 1
        }
    }
    
    # Check JWT secret key length (should be at least 32 characters)
    if ($envVars["JWT_SECRET_KEY"].Length -lt 32) {
        Write-Error "JWT_SECRET_KEY must be at least 32 characters long"
        exit 1
    }
    
    Write-Success "Environment validation passed"
}

function Backup-ExistingData {
    if ($SkipBackup) {
        Write-Info "Skipping backup as requested"
        return
    }
    
    Write-Info "Creating backup of existing data..."
    
    # Create backup directory with timestamp
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupDir = Join-Path $ProjectDir "backups\$timestamp"
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # Backup database if container exists
    $dbContainer = docker ps -a --format "table {{.Names}}" | Select-String "tli-analytics-timescaledb-prod"
    if ($dbContainer) {
        Write-Info "Backing up database..."
        try {
            docker exec tli-analytics-timescaledb-prod pg_dump -U timescale TLI_AnalyticsBI | Out-File -FilePath (Join-Path $backupDir "database_backup.sql") -Encoding UTF8
        }
        catch {
            Write-Warning "Database backup failed: $_"
        }
    }
    
    # Backup volumes
    $volume = docker volume ls --format "table {{.Name}}" | Select-String "timescaledb_data"
    if ($volume) {
        Write-Info "Backing up database volume..."
        try {
            docker run --rm -v timescaledb_data:/data -v "${backupDir}:/backup" alpine tar czf /backup/timescaledb_data.tar.gz -C /data .
        }
        catch {
            Write-Warning "Volume backup failed: $_"
        }
    }
    
    Write-Success "Backup completed in $backupDir"
}

function Build-Images {
    Write-Info "Building Docker images..."
    
    Set-Location $ProjectDir
    
    # Build the application image
    try {
        docker-compose -f $ComposeFile build --no-cache
    }
    catch {
        # Try newer docker compose syntax
        docker compose -f $ComposeFile build --no-cache
    }
    
    Write-Success "Docker images built successfully"
}

function Deploy-Services {
    Write-Info "Deploying services..."
    
    Set-Location $ProjectDir
    
    # Stop existing services
    Write-Info "Stopping existing services..."
    try {
        docker-compose -f $ComposeFile down
    }
    catch {
        try {
            docker compose -f $ComposeFile down
        }
        catch {
            Write-Warning "Failed to stop existing services: $_"
        }
    }
    
    # Start infrastructure services first
    Write-Info "Starting infrastructure services..."
    try {
        docker-compose -f $ComposeFile up -d timescaledb redis
    }
    catch {
        docker compose -f $ComposeFile up -d timescaledb redis
    }
    
    # Wait for infrastructure services to be ready
    Write-Info "Waiting for infrastructure services to be ready..."
    Start-Sleep -Seconds 30
    
    # Start application services
    Write-Info "Starting application services..."
    try {
        docker-compose -f $ComposeFile up -d
    }
    catch {
        docker compose -f $ComposeFile up -d
    }
    
    Write-Success "Services deployed successfully"
}

function Test-Deployment {
    Write-Info "Verifying deployment..."
    
    # Wait for services to start
    Start-Sleep -Seconds 60
    
    # Check service health
    $services = @("tli-analytics-api-prod", "tli-analytics-timescaledb-prod", "tli-analytics-redis-prod")
    
    foreach ($service in $services) {
        $containerStatus = docker ps --format "table {{.Names}}`t{{.Status}}" | Select-String $service
        if ($containerStatus -and $containerStatus -match "Up") {
            Write-Success "$service is running"
        }
        else {
            Write-Error "$service is not running"
            docker logs $service --tail 20
            exit 1
        }
    }
    
    # Test API health endpoint
    Write-Info "Testing API health endpoint..."
    $apiPort = 5014  # Default port
    
    for ($i = 1; $i -le 10; $i++) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$apiPort/health" -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Success "API health check passed"
                break
            }
        }
        catch {
            if ($i -eq 10) {
                Write-Error "API health check failed after 10 attempts"
                exit 1
            }
            Write-Info "Waiting for API to be ready... (attempt $i/10)"
            Start-Sleep -Seconds 10
        }
    }
    
    Write-Success "Deployment verification completed"
}

function Show-DeploymentInfo {
    Write-Info "Deployment Information:"
    Write-Host "==========================" -ForegroundColor Cyan
    Write-Host "API URL: http://localhost:5014" -ForegroundColor White
    Write-Host "Grafana URL: http://localhost:3000" -ForegroundColor White
    Write-Host "Prometheus URL: http://localhost:9090" -ForegroundColor White
    Write-Host "==========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Info "To view logs: docker logs tli-analytics-api-prod"
    Write-Info "To stop services: docker-compose -f docker-compose.prod.yml down"
    Write-Info "To view service status: docker-compose -f docker-compose.prod.yml ps"
}

# Main execution
function Main {
    Write-Info "Starting AnalyticsBIService production deployment..."
    
    Test-Prerequisites
    Test-Environment
    
    # Ask for confirmation unless Force is specified
    if (-not $Force) {
        $confirmation = Read-Host "Are you sure you want to deploy to production? (y/N)"
        if ($confirmation -ne "y" -and $confirmation -ne "Y") {
            Write-Info "Deployment cancelled"
            exit 0
        }
    }
    
    Backup-ExistingData
    Build-Images
    Deploy-Services
    Test-Deployment
    Show-DeploymentInfo
    
    Write-Success "AnalyticsBIService production deployment completed successfully!"
}

# Run main function
Main
