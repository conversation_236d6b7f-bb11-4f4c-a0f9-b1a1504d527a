# PowerShell script to build TLI Microservices excluding Analytics BI services
# This script builds all microservices except Analytics BI components

param(
    [string]$Configuration = "Release",
    [switch]$SkipTests,
    [switch]$Verbose
)

Write-Host "Building TLI Microservices Solution (Excluding Analytics BI)..." -ForegroundColor Green

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot
$solutionFile = "$solutionRoot\TLIMicroservices.sln"

# Check if solution file exists
if (-not (Test-Path $solutionFile)) {
    Write-Host "✗ Solution file not found: $solutionFile" -ForegroundColor Red
    exit 1
}

Write-Host "Solution file: $solutionFile" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration" -ForegroundColor Cyan

# Define projects to exclude (Analytics BI Service)
$excludedProjects = @(
    "Services\AnalyticsBIService\AnalyticsBIService.API\AnalyticsBIService.API.csproj",
    "Services\AnalyticsBIService\AnalyticsBIService.Application\AnalyticsBIService.Application.csproj",
    "Services\AnalyticsBIService\AnalyticsBIService.Domain\AnalyticsBIService.Domain.csproj",
    "Services\AnalyticsBIService\AnalyticsBIService.Infrastructure\AnalyticsBIService.Infrastructure.csproj",
    "Services\AnalyticsBIService\AnalyticsBIService.Tests\AnalyticsBIService.Tests.csproj"
)

# Get all projects in the solution
Write-Host "`nAnalyzing solution projects..." -ForegroundColor Yellow
$allProjects = dotnet sln $solutionFile list | Where-Object { $_ -match "\.csproj$" }
$projectsToBuild = $allProjects | Where-Object { $excludedProjects -notcontains $_ }

Write-Host "Total projects in solution: $($allProjects.Count)" -ForegroundColor Cyan
Write-Host "Projects to exclude: $($excludedProjects.Count)" -ForegroundColor Red
Write-Host "Projects to build: $($projectsToBuild.Count)" -ForegroundColor Green

# Display excluded projects
Write-Host "`nExcluded Analytics BI projects:" -ForegroundColor Red
foreach ($project in $excludedProjects) {
    Write-Host "  ✗ $project" -ForegroundColor Red
}

# Clean the solution
Write-Host "`nCleaning solution..." -ForegroundColor Yellow
dotnet clean $solutionFile --configuration $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Clean failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Clean completed" -ForegroundColor Green

# Restore packages
Write-Host "`nRestoring NuGet packages..." -ForegroundColor Yellow
dotnet restore $solutionFile
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Restore failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Restore completed" -ForegroundColor Green

# Build projects in dependency order
Write-Host "`nBuilding projects in dependency order..." -ForegroundColor Yellow

# Define build groups for better dependency management
$buildGroups = @{
    "Shared Components" = @(
        "Shared\TLI.Shared.Domain\TLI.Shared.Domain.csproj",
        "Shared\Shared.Domain\Shared.Domain.csproj",
        "Shared\Shared.Messaging\Shared.Messaging.csproj",
        "Shared\TLI.Shared.Application\TLI.Shared.Application.csproj",
        "Shared\Shared.Infrastructure\Shared.Infrastructure.csproj",
        "Shared\TLI.Shared.Infrastructure\TLI.Shared.Infrastructure.csproj",
        "Shared\TLI.Shared.API\TLI.Shared.API.csproj"
    )
    "Identity Service" = @(
        "Identity\Identity.Domain\Identity.Domain.csproj",
        "Identity\Identity.Application\Identity.Application.csproj",
        "Identity\Identity.Infrastructure\Identity.Infrastructure.csproj",
        "Identity\Identity.API\Identity.API.csproj",
        "Identity\Identity.Tests\Identity.Tests.csproj"
    )
    "Core Business Services" = @(
        "Services\UserManagement\UserManagement.Domain\UserManagement.Domain.csproj",
        "Services\UserManagement\UserManagement.Application\UserManagement.Application.csproj",
        "Services\UserManagement\UserManagement.Infrastructure\UserManagement.Infrastructure.csproj",
        "Services\UserManagement\UserManagement.API\UserManagement.API.csproj",
        "Services\UserManagement\UserManagement.Tests\UserManagement.Tests.csproj",
        "Services\SubscriptionManagement\SubscriptionManagement.Domain\SubscriptionManagement.Domain.csproj",
        "Services\SubscriptionManagement\SubscriptionManagement.Application\SubscriptionManagement.Application.csproj",
        "Services\SubscriptionManagement\SubscriptionManagement.Infrastructure\SubscriptionManagement.Infrastructure.csproj",
        "Services\SubscriptionManagement\SubscriptionManagement.API\SubscriptionManagement.API.csproj",
        "Services\SubscriptionManagement\SubscriptionManagement.Tests\SubscriptionManagement.Tests.csproj",
        "Services\OrderManagement\OrderManagement.Domain\OrderManagement.Domain.csproj",
        "Services\OrderManagement\OrderManagement.Application\OrderManagement.Application.csproj",
        "Services\OrderManagement\OrderManagement.Infrastructure\OrderManagement.Infrastructure.csproj",
        "Services\OrderManagement\OrderManagement.API\OrderManagement.API.csproj",
        "Services\OrderManagement\OrderManagement.IntegrationTests\OrderManagement.IntegrationTests.csproj",
        "Services\TripManagement\TripManagement.Domain\TripManagement.Domain.csproj",
        "Services\TripManagement\TripManagement.Application\TripManagement.Application.csproj",
        "Services\TripManagement\TripManagement.Infrastructure\TripManagement.Infrastructure.csproj",
        "Services\TripManagement\TripManagement.API\TripManagement.API.csproj",
        "Services\TripManagement\TripManagement.Tests\TripManagement.Tests.csproj"
    )
    "Supporting Services" = @(
        "Services\NetworkFleetManagement\NetworkFleetManagement.Domain\NetworkFleetManagement.Domain.csproj",
        "Services\NetworkFleetManagement\NetworkFleetManagement.Application\NetworkFleetManagement.Application.csproj",
        "Services\NetworkFleetManagement\NetworkFleetManagement.Infrastructure\NetworkFleetManagement.Infrastructure.csproj",
        "Services\NetworkFleetManagement\NetworkFleetManagement.API\NetworkFleetManagement.API.csproj",
        "Services\NetworkFleetManagement\NetworkFleetManagement.Tests\NetworkFleetManagement.Tests.csproj",
        "Services\CommunicationNotification\CommunicationNotification.Domain\CommunicationNotification.Domain.csproj",
        "Services\CommunicationNotification\CommunicationNotification.Application\CommunicationNotification.Application.csproj",
        "Services\CommunicationNotification\CommunicationNotification.Infrastructure\CommunicationNotification.Infrastructure.csproj",
        "Services\CommunicationNotification\CommunicationNotification.API\CommunicationNotification.API.csproj",
        "Services\CommunicationNotification\CommunicationNotification.Tests\CommunicationNotification.Tests.csproj",
        "Services\DataStorage\DataStorage.Domain\DataStorage.Domain.csproj",
        "Services\DataStorage\DataStorage.Application\DataStorage.Application.csproj",
        "Services\DataStorage\DataStorage.Infrastructure\DataStorage.Infrastructure.csproj",
        "Services\DataStorage\DataStorage.API\DataStorage.API.csproj",
        "Services\MonitoringObservability\MonitoringObservability.Domain\MonitoringObservability.Domain.csproj",
        "Services\MonitoringObservability\MonitoringObservability.Application\MonitoringObservability.Application.csproj",
        "Services\MonitoringObservability\MonitoringObservability.Infrastructure\MonitoringObservability.Infrastructure.csproj",
        "Services\MonitoringObservability\MonitoringObservability.API\MonitoringObservability.API.csproj"
    )
    "Additional Services" = @(
        "Services\AuditCompliance\AuditCompliance.Domain\AuditCompliance.Domain.csproj",
        "Services\AuditCompliance\AuditCompliance.Application\AuditCompliance.Application.csproj",
        "Services\AuditCompliance\AuditCompliance.Infrastructure\AuditCompliance.Infrastructure.csproj",
        "Services\AuditCompliance\AuditCompliance.API\AuditCompliance.API.csproj",
        "Services\AuditCompliance\AuditCompliance.Tests\AuditCompliance.Tests.csproj",
        "Services\ContentManagement\ContentManagement.Domain\ContentManagement.Domain.csproj",
        "Services\ContentManagement\ContentManagement.Application\ContentManagement.Application.csproj",
        "Services\ContentManagement\ContentManagement.Infrastructure\ContentManagement.Infrastructure.csproj",
        "Services\ContentManagement\ContentManagement.API\ContentManagement.API.csproj",
        "Services\ContentManagement\ContentManagement.Tests\ContentManagement.Tests.csproj",
        "Services\FinancialPayment\FinancialPayment.Domain\FinancialPayment.Domain.csproj",
        "Services\FinancialPayment\FinancialPayment.Application\FinancialPayment.Application.csproj",
        "Services\FinancialPayment\FinancialPayment.Infrastructure\FinancialPayment.Infrastructure.csproj",
        "Services\FinancialPayment\FinancialPayment.API\FinancialPayment.API.csproj",
        "Services\FinancialPayment\FinancialPayment.Tests\FinancialPayment.Tests.csproj",
        "Services\MobileWorkflow\MobileWorkflow.Domain\MobileWorkflow.Domain.csproj",
        "Services\MobileWorkflow\MobileWorkflow.Application\MobileWorkflow.Application.csproj",
        "Services\MobileWorkflow\MobileWorkflow.Infrastructure\MobileWorkflow.Infrastructure.csproj",
        "Services\MobileWorkflow\MobileWorkflow.API\MobileWorkflow.API.csproj",
        "Services\MobileWorkflow\MobileWorkflow.Tests\MobileWorkflow.Tests.csproj"
    )
    "API Gateway" = @(
        "ApiGateway\ApiGateway.csproj"
    )
}

$buildErrors = @()
$successfulBuilds = @()
$totalProjects = 0

foreach ($groupName in $buildGroups.Keys) {
    Write-Host "`nBuilding $groupName..." -ForegroundColor Magenta

    foreach ($project in $buildGroups[$groupName]) {
        if ($projectsToBuild -contains $project) {
            $totalProjects++
            $projectName = Split-Path $project -Leaf
            Write-Host "  [$totalProjects] Building $projectName..." -ForegroundColor Cyan

            $buildArgs = @("build", "$solutionRoot\$project", "--configuration", $Configuration, "--no-restore")
            if ($Verbose) { $buildArgs += "--verbosity", "detailed" }

            & dotnet @buildArgs
            if ($LASTEXITCODE -eq 0) {
                Write-Host "    ✓ $projectName built successfully" -ForegroundColor Green
                $successfulBuilds += $project
            } else {
                Write-Host "    ✗ $projectName build failed" -ForegroundColor Red
                $buildErrors += $project
            }
        }
    }
}

# Build summary
Write-Host "`n" + "="*80 -ForegroundColor Cyan
Write-Host "BUILD SUMMARY" -ForegroundColor Cyan
Write-Host "="*80 -ForegroundColor Cyan

Write-Host "`nTotal projects processed: $totalProjects" -ForegroundColor Cyan
Write-Host "Successful builds: $($successfulBuilds.Count)" -ForegroundColor Green
Write-Host "Failed builds: $($buildErrors.Count)" -ForegroundColor Red
Write-Host "Excluded (Analytics BI): $($excludedProjects.Count)" -ForegroundColor Yellow

if ($buildErrors.Count -gt 0) {
    Write-Host "`nFailed builds:" -ForegroundColor Red
    foreach ($project in $buildErrors) {
        $projectName = Split-Path $project -Leaf
        Write-Host "  ✗ $projectName" -ForegroundColor Red
    }
    
    Write-Host "`n❌ Build completed with errors!" -ForegroundColor Red
    Write-Host "Please check the build output above for details." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All projects built successfully!" -ForegroundColor Green
    Write-Host "Analytics BI services were excluded as requested." -ForegroundColor Yellow
    
    if (-not $SkipTests) {
        Write-Host "`nRunning tests..." -ForegroundColor Yellow
        $testProjects = $successfulBuilds | Where-Object { $_ -match "\.Tests\." -or $_ -match "\.IntegrationTests\." }
        
        if ($testProjects.Count -gt 0) {
            foreach ($testProject in $testProjects) {
                $projectName = Split-Path $testProject -Leaf
                Write-Host "  Running tests for $projectName..." -ForegroundColor Cyan
                dotnet test "$solutionRoot\$testProject" --configuration $Configuration --no-build --verbosity normal
            }
        } else {
            Write-Host "  No test projects found to run." -ForegroundColor Yellow
        }
    }
    
    Write-Host "`nYou can now run the services using:" -ForegroundColor Cyan
    Write-Host "  .\scripts\start-services.ps1" -ForegroundColor White
}
