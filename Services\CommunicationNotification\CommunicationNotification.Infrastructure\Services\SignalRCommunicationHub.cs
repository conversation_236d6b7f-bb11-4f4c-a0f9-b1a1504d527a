using CommunicationNotification.Application.Interfaces;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// SignalR-based communication hub implementation
/// </summary>
public class SignalRCommunicationHub : ICommunicationHub
{
    private readonly IHubContext<Hubs.ChatHub, Hubs.IChatClient> _hubContext;
    private readonly ILogger<SignalRCommunicationHub> _logger;
    private readonly IConnectionTrackingService _connectionTracker;

    public SignalRCommunicationHub(
        IHubContext<Hubs.ChatHub, Hubs.IChatClient> hubContext,
        ILogger<SignalRCommunicationHub> logger,
        IConnectionTrackingService connectionTracker)
    {
        _hubContext = hubContext;
        _logger = logger;
        _connectionTracker = connectionTracker;
    }

    public async Task SendToUserAsync(Guid userId, object message, CancellationToken cancellationToken = default)
    {
        try
        {
            await _hubContext.Clients.Group($"user_{userId}").ReceiveMessage(
                Guid.NewGuid(),
                Guid.Empty,
                message.ToString() ?? "",
                "System",
                DateTime.UtcNow);

            _logger.LogDebug("Message sent to user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send message to user {UserId}", userId);
            throw;
        }
    }

    public async Task SendToGroupAsync(string groupName, object message, CancellationToken cancellationToken = default)
    {
        try
        {
            await _hubContext.Clients.Group(groupName).ReceiveMessage(
                Guid.NewGuid(),
                Guid.Empty,
                message.ToString() ?? "",
                "System",
                DateTime.UtcNow);

            _logger.LogDebug("Message sent to group {GroupName}", groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send message to group {GroupName}", groupName);
            throw;
        }
    }

    public async Task AddToGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            var connectionIds = await _connectionTracker.GetUserConnectionsAsync(userId);
            
            foreach (var connectionId in connectionIds)
            {
                await _hubContext.Groups.AddToGroupAsync(connectionId, groupName, cancellationToken);
            }

            _logger.LogDebug("User {UserId} added to group {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add user {UserId} to group {GroupName}", userId, groupName);
            throw;
        }
    }

    public async Task RemoveFromGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            var connectionIds = await _connectionTracker.GetUserConnectionsAsync(userId);
            
            foreach (var connectionId in connectionIds)
            {
                await _hubContext.Groups.RemoveFromGroupAsync(connectionId, groupName, cancellationToken);
            }

            _logger.LogDebug("User {UserId} removed from group {GroupName}", userId, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove user {UserId} from group {GroupName}", userId, groupName);
            throw;
        }
    }

    public async Task<List<Guid>> GetOnlineUsersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _connectionTracker.GetOnlineUsersAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get online users");
            throw;
        }
    }

    public async Task<bool> IsUserOnlineAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var connections = await _connectionTracker.GetUserConnectionsAsync(userId);
            return connections.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if user {UserId} is online", userId);
            return false;
        }
    }
}

/// <summary>
/// Service for tracking user connections
/// </summary>
public interface IConnectionTrackingService
{
    /// <summary>
    /// Add user connection
    /// </summary>
    Task AddConnectionAsync(Guid userId, string connectionId);

    /// <summary>
    /// Remove user connection
    /// </summary>
    Task RemoveConnectionAsync(string connectionId);

    /// <summary>
    /// Get user connections
    /// </summary>
    Task<List<string>> GetUserConnectionsAsync(Guid userId);

    /// <summary>
    /// Get online users
    /// </summary>
    Task<List<Guid>> GetOnlineUsersAsync();

    /// <summary>
    /// Get user ID by connection ID
    /// </summary>
    Task<Guid?> GetUserIdByConnectionAsync(string connectionId);
}

/// <summary>
/// In-memory connection tracking service
/// </summary>
public class InMemoryConnectionTrackingService : IConnectionTrackingService
{
    private readonly Dictionary<Guid, HashSet<string>> _userConnections = new();
    private readonly Dictionary<string, Guid> _connectionUsers = new();
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    private readonly ILogger<InMemoryConnectionTrackingService> _logger;

    public InMemoryConnectionTrackingService(ILogger<InMemoryConnectionTrackingService> logger)
    {
        _logger = logger;
    }

    public async Task AddConnectionAsync(Guid userId, string connectionId)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (!_userConnections.ContainsKey(userId))
            {
                _userConnections[userId] = new HashSet<string>();
            }

            _userConnections[userId].Add(connectionId);
            _connectionUsers[connectionId] = userId;

            _logger.LogDebug("Connection {ConnectionId} added for user {UserId}", connectionId, userId);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task RemoveConnectionAsync(string connectionId)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (_connectionUsers.TryGetValue(connectionId, out var userId))
            {
                _connectionUsers.Remove(connectionId);

                if (_userConnections.ContainsKey(userId))
                {
                    _userConnections[userId].Remove(connectionId);
                    
                    if (_userConnections[userId].Count == 0)
                    {
                        _userConnections.Remove(userId);
                    }
                }

                _logger.LogDebug("Connection {ConnectionId} removed for user {UserId}", connectionId, userId);
            }
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<List<string>> GetUserConnectionsAsync(Guid userId)
    {
        await _semaphore.WaitAsync();
        try
        {
            return _userConnections.TryGetValue(userId, out var connections) 
                ? connections.ToList() 
                : new List<string>();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<List<Guid>> GetOnlineUsersAsync()
    {
        await _semaphore.WaitAsync();
        try
        {
            return _userConnections.Keys.ToList();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<Guid?> GetUserIdByConnectionAsync(string connectionId)
    {
        await _semaphore.WaitAsync();
        try
        {
            return _connectionUsers.TryGetValue(connectionId, out var userId) ? userId : null;
        }
        finally
        {
            _semaphore.Release();
        }
    }
}

/// <summary>
/// Redis-based connection tracking service for distributed scenarios
/// </summary>
public class RedisConnectionTrackingService : IConnectionTrackingService
{
    private readonly ILogger<RedisConnectionTrackingService> _logger;

    public RedisConnectionTrackingService(ILogger<RedisConnectionTrackingService> logger)
    {
        _logger = logger;
    }

    public async Task AddConnectionAsync(Guid userId, string connectionId)
    {
        // TODO: Implement Redis-based connection tracking
        _logger.LogWarning("Redis connection tracking not implemented yet");
    }

    public async Task RemoveConnectionAsync(string connectionId)
    {
        // TODO: Implement Redis-based connection tracking
        _logger.LogWarning("Redis connection tracking not implemented yet");
    }

    public async Task<List<string>> GetUserConnectionsAsync(Guid userId)
    {
        // TODO: Implement Redis-based connection tracking
        return new List<string>();
    }

    public async Task<List<Guid>> GetOnlineUsersAsync()
    {
        // TODO: Implement Redis-based connection tracking
        return new List<Guid>();
    }

    public async Task<Guid?> GetUserIdByConnectionAsync(string connectionId)
    {
        // TODO: Implement Redis-based connection tracking
        return null;
    }
}
