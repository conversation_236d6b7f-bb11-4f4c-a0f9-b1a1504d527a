using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Domain.Repositories;

public interface IWorkflowRepository
{
    Task<Workflow?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Workflow?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Workflow>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<IEnumerable<Workflow>> GetActiveWorkflowsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Workflow>> GetByTriggerTypeAsync(string triggerType, CancellationToken cancellationToken = default);
    Task<IEnumerable<Workflow>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<Workflow> AddAsync(Workflow workflow, CancellationToken cancellationToken = default);
    Task<Workflow> UpdateAsync(Workflow workflow, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Workflow>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
}

public interface IWorkflowExecutionRepository
{
    Task<WorkflowExecution?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetByWorkflowIdAsync(Guid workflowId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetRecentExecutionsAsync(int count, CancellationToken cancellationToken = default);
    Task<WorkflowExecution> AddAsync(WorkflowExecution execution, CancellationToken cancellationToken = default);
    Task<WorkflowExecution> UpdateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<int> GetExecutionCountByWorkflowIdAsync(Guid workflowId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowExecution>> GetExecutionsInDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

public interface IWorkflowTaskRepository
{
    Task<WorkflowTask?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByWorkflowExecutionIdAsync(Guid executionId, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByAssignedToAsync(Guid assignedTo, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByAssignedToRoleAsync(string role, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByTypeAsync(string type, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetByPriorityAsync(int priority, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetOverdueTasksAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetPendingTasksAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetTasksDueSoonAsync(TimeSpan timeWindow, CancellationToken cancellationToken = default);
    Task<WorkflowTask> AddAsync(WorkflowTask task, CancellationToken cancellationToken = default);
    Task<WorkflowTask> UpdateAsync(WorkflowTask task, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<int> GetTaskCountByStatusAsync(string status, CancellationToken cancellationToken = default);
    Task<IEnumerable<WorkflowTask>> GetTasksInDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}
