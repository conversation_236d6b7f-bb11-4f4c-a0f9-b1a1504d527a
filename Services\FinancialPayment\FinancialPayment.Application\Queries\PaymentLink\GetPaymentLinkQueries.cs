using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using MediatR;

namespace FinancialPayment.Application.Queries.PaymentLink;

/// <summary>
/// Query to get payment link by token
/// </summary>
public class GetPaymentLinkByTokenQuery : IRequest<PaymentLinkDetailsDto?>
{
    public string Token { get; set; } = string.Empty;
    public string? AccessIpAddress { get; set; }
    public string? UserAgent { get; set; }
    public bool RecordAccess { get; set; } = true;
}

/// <summary>
/// Query to get payment links for a transport company
/// </summary>
public class GetTransportCompanyPaymentLinksQuery : IRequest<PaymentLinkListDto>
{
    public Guid TransportCompanyId { get; set; }
    public PaymentLinkStatus? Status { get; set; }
    public PaymentLinkType? LinkType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// Query to get payment link analytics for a transport company
/// </summary>
public class GetPaymentLinkAnalyticsQuery : IRequest<PaymentLinkAnalyticsDto>
{
    public Guid TransportCompanyId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public PaymentLinkType? LinkType { get; set; }
    public bool IncludeDetailedBreakdown { get; set; } = true;
}

/// <summary>
/// Query to get payment link status tracking
/// </summary>
public class GetPaymentLinkStatusQuery : IRequest<PaymentLinkStatusDto>
{
    public Guid PaymentLinkId { get; set; }
    public bool IncludeAccessLogs { get; set; } = false;
    public bool IncludePaymentAttempts { get; set; } = false;
}

/// <summary>
/// DTOs for payment link queries
/// </summary>
public class PaymentLinkDetailsDto
{
    public Guid Id { get; set; }
    public string LinkToken { get; set; } = string.Empty;
    public string PublicUrl { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public PaymentLinkStatus Status { get; set; }
    public PaymentLinkType LinkType { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public string? PaymentTransactionId { get; set; }
    public bool IsExpired { get; set; }
    public TimeSpan TimeUntilExpiry { get; set; }
    public int AccessCount { get; set; }
    public DateTime? LastAccessedAt { get; set; }

    // Contact details
    public ContactDetailsDto ShipperDetails { get; set; } = new();
    public ContactDetailsDto TransportCompanyDetails { get; set; } = new();

    // Related entities
    public Guid? OrderId { get; set; }
    public Guid? InvoiceId { get; set; }
    public string? OrderNumber { get; set; }
    public string? InvoiceNumber { get; set; }

    // Settings
    public bool RequiresAuthentication { get; set; }
    public bool SendEmailNotification { get; set; }
    public bool SendSmsNotification { get; set; }
    public List<string> AllowedIpAddresses { get; set; } = new();

    // Security info
    public bool IsAccessible { get; set; }
    public List<string> AccessRestrictions { get; set; } = new();
}

public class ContactDetailsDto
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? CompanyName { get; set; }
    public AddressDto? Address { get; set; }
}

public class AddressDto
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string FullAddress { get; set; } = string.Empty;
}

public class PaymentLinkListDto
{
    public List<PaymentLinkSummaryDto> PaymentLinks { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public PaymentLinkListSummary Summary { get; set; } = new();
}

public class PaymentLinkSummaryDto
{
    public Guid Id { get; set; }
    public string LinkToken { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public PaymentLinkStatus Status { get; set; }
    public PaymentLinkType LinkType { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public bool IsExpired { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public string ShipperEmail { get; set; } = string.Empty;
    public int AccessCount { get; set; }
    public int PaymentAttempts { get; set; }
    public string? OrderNumber { get; set; }
    public string? InvoiceNumber { get; set; }
}

public class PaymentLinkListSummary
{
    public int TotalLinks { get; set; }
    public int ActiveLinks { get; set; }
    public int PaidLinks { get; set; }
    public int ExpiredLinks { get; set; }
    public int CancelledLinks { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal PendingAmount { get; set; }
    public double ConversionRate { get; set; }
}

public class PaymentLinkAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }

    // Overall metrics
    public PaymentLinkMetrics OverallMetrics { get; set; } = new();

    // Breakdown by type
    public Dictionary<string, PaymentLinkMetrics> ByLinkType { get; set; } = new();

    // Breakdown by status
    public Dictionary<string, PaymentLinkMetrics> ByStatus { get; set; } = new();

    // Time-based trends
    public List<PaymentLinkTrendData> DailyTrends { get; set; } = new();
    public List<PaymentLinkTrendData> MonthlyTrends { get; set; } = new();

    // Performance metrics
    public PaymentLinkPerformanceMetrics Performance { get; set; } = new();
}

public class PaymentLinkMetrics
{
    public int TotalLinks { get; set; }
    public int ActiveLinks { get; set; }
    public int PaidLinks { get; set; }
    public int ExpiredLinks { get; set; }
    public int CancelledLinks { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal PendingAmount { get; set; }
    public double ConversionRate { get; set; }
    public double AverageAmount { get; set; }
}

public class PaymentLinkTrendData
{
    public DateTime Date { get; set; }
    public int LinksCreated { get; set; }
    public int LinksPaid { get; set; }
    public int LinksExpired { get; set; }
    public decimal AmountCreated { get; set; }
    public decimal AmountPaid { get; set; }
    public double ConversionRate { get; set; }
}

public class PaymentLinkPerformanceMetrics
{
    public double AveragePaymentTime { get; set; } // in hours
    public double AverageAccessCount { get; set; }
    public int TotalAccesses { get; set; }
    public int TotalPaymentAttempts { get; set; }
    public double PaymentSuccessRate { get; set; }
    public Dictionary<string, int> TopFailureReasons { get; set; } = new();
    public Dictionary<string, double> PaymentTimeByType { get; set; } = new();
}

public class PaymentLinkStatusDto
{
    public Guid Id { get; set; }
    public PaymentLinkStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public bool IsExpired { get; set; }
    public bool IsAccessible { get; set; }
    public TimeSpan TimeUntilExpiry { get; set; }
    public int AccessCount { get; set; }
    public int PaymentAttemptsCount { get; set; }
    public DateTime? LastAccessedAt { get; set; }
    public string? LastAccessedFromIp { get; set; }
    public List<PaymentLinkAccessLogDto> AccessLogs { get; set; } = new();
    public List<PaymentLinkAttemptDto> PaymentAttempts { get; set; } = new();
}

public class PaymentLinkAccessLogDto
{
    public DateTime AccessedAt { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }
    public string? Referrer { get; set; }
}

public class PaymentLinkAttemptDto
{
    public DateTime AttemptedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public PaymentAttemptStatus Status { get; set; }
    public string PaymentMethodId { get; set; } = string.Empty;
    public string? GatewayTransactionId { get; set; }
    public string? FailureReason { get; set; }
}
