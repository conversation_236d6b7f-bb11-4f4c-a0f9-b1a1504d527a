using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Mobile event entity for tracking mobile app events
/// </summary>
public class MobileEvent : BaseEntity
{
    public Guid UserId { get; private set; }
    public string AppId { get; private set; }
    public string EventName { get; private set; }
    public string SessionId { get; private set; }
    public string DeviceId { get; private set; }
    public string Properties { get; private set; } // JSON string
    public DateTime Timestamp { get; private set; }
    public string DeviceInfo { get; private set; } // JSON string
    public string? Location { get; private set; }

    private MobileEvent()
    {
        AppId = string.Empty;
        EventName = string.Empty;
        SessionId = string.Empty;
        DeviceId = string.Empty;
        Properties = string.Empty;
        DeviceInfo = string.Empty;
    }

    public MobileEvent(
        Guid userId,
        string appId,
        string eventName,
        string sessionId,
        string deviceId,
        string properties,
        DateTime timestamp,
        string deviceInfo,
        string? location = null)
    {
        UserId = userId;
        AppId = appId ?? throw new ArgumentNullException(nameof(appId));
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        Properties = properties ?? throw new ArgumentNullException(nameof(properties));
        Timestamp = timestamp;
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        Location = location;
    }

    public void UpdateLocation(string location)
    {
        Location = location;
        SetUpdatedAt();
    }

    public void UpdateProperties(string properties)
    {
        Properties = properties ?? throw new ArgumentNullException(nameof(properties));
        SetUpdatedAt();
    }
}
