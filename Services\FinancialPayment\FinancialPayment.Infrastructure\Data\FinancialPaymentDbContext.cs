using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data.Configurations;
using Shared.Infrastructure.Persistence;

namespace FinancialPayment.Infrastructure.Data;

public class FinancialPaymentDbContext : BaseDbContext
{
    public FinancialPaymentDbContext(DbContextOptions<FinancialPaymentDbContext> options) : base(options)
    {
    }

    // Escrow entities
    public DbSet<EscrowAccount> EscrowAccounts { get; set; }
    public DbSet<EscrowTransaction> EscrowTransactions { get; set; }
    public DbSet<EscrowMilestone> EscrowMilestones { get; set; }

    // Settlement entities
    public DbSet<Settlement> Settlements { get; set; }
    public DbSet<SettlementDistribution> SettlementDistributions { get; set; }

    // Commission entities
    public DbSet<Commission> Commissions { get; set; }
    public DbSet<CommissionAdjustment> CommissionAdjustments { get; set; }

    // Dispute entities
    public DbSet<PaymentDispute> PaymentDisputes { get; set; }
    public DbSet<DisputeComment> DisputeComments { get; set; }
    public DbSet<DisputeDocument> DisputeDocuments { get; set; }

    // Tax entities
    public DbSet<TaxRule> TaxRules { get; set; }
    public DbSet<TaxRuleCondition> TaxRuleConditions { get; set; }
    public DbSet<TaxCalculation> TaxCalculations { get; set; }
    public DbSet<TaxCalculationLine> TaxCalculationLines { get; set; }

    // Tax Configuration entities
    public DbSet<TaxConfiguration> TaxConfigurations { get; set; }
    public DbSet<TaxConfigurationHistory> TaxConfigurationHistories { get; set; }
    public DbSet<TaxConfigurationRule> TaxConfigurationRules { get; set; }
    public DbSet<GstConfiguration> GstConfigurations { get; set; }
    public DbSet<GstConfigurationHistory> GstConfigurationHistories { get; set; }
    public DbSet<TdsConfiguration> TdsConfigurations { get; set; }
    public DbSet<TdsConfigurationHistory> TdsConfigurationHistories { get; set; }
    public DbSet<HsnCode> HsnCodes { get; set; }
    public DbSet<HsnCodeHistory> HsnCodeHistories { get; set; }
    public DbSet<HsnCodeGstMapping> HsnCodeGstMappings { get; set; }

    // Reconciliation entities
    public DbSet<PaymentReconciliation> PaymentReconciliations { get; set; }
    public DbSet<ReconciliationItem> ReconciliationItems { get; set; }
    public DbSet<ReconciliationDiscrepancy> ReconciliationDiscrepancies { get; set; }

    // Fraud Detection entities
    public DbSet<FraudDetectionRule> FraudDetectionRules { get; set; }
    public DbSet<FraudRuleCondition> FraudRuleConditions { get; set; }
    public DbSet<FraudAssessment> FraudAssessments { get; set; }
    public DbSet<FraudAssessmentResult> FraudAssessmentResults { get; set; }
    public DbSet<FraudAlert> FraudAlerts { get; set; }

    // Financial Reporting entities
    public DbSet<FinancialReport> FinancialReports { get; set; }
    public DbSet<ReportSection> ReportSections { get; set; }
    public DbSet<ReportTemplate> ReportTemplates { get; set; }
    public DbSet<TemplateSection> TemplateSections { get; set; }

    // Feature Flag entities
    public DbSet<FeatureFlag> FeatureFlags { get; set; }
    public DbSet<FeatureFlagRule> FeatureFlagRules { get; set; }
    public DbSet<FeatureFlagVariant> FeatureFlagVariants { get; set; }
    public DbSet<FeatureFlagAuditLog> FeatureFlagAuditLogs { get; set; }

    // Monitoring entities
    public DbSet<SystemMetric> SystemMetrics { get; set; }
    public DbSet<PerformanceMetric> PerformanceMetrics { get; set; }
    public DbSet<Alert> Alerts { get; set; }
    public DbSet<HealthCheck> HealthChecks { get; set; }
    public DbSet<UsageAnalytics> UsageAnalytics { get; set; }
    public DbSet<MonitoringDashboard> MonitoringDashboards { get; set; }
    public DbSet<DashboardWidget> DashboardWidgets { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfiguration(new EscrowAccountConfiguration());
        modelBuilder.ApplyConfiguration(new EscrowTransactionConfiguration());
        modelBuilder.ApplyConfiguration(new EscrowMilestoneConfiguration());
        modelBuilder.ApplyConfiguration(new SettlementConfiguration());
        modelBuilder.ApplyConfiguration(new SettlementDistributionConfiguration());
        modelBuilder.ApplyConfiguration(new CommissionConfiguration());
        modelBuilder.ApplyConfiguration(new CommissionAdjustmentConfiguration());
        modelBuilder.ApplyConfiguration(new PaymentDisputeConfiguration());
        modelBuilder.ApplyConfiguration(new DisputeCommentConfiguration());
        modelBuilder.ApplyConfiguration(new DisputeDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new TaxRuleConfiguration());
        modelBuilder.ApplyConfiguration(new TaxRuleConditionConfiguration());
        modelBuilder.ApplyConfiguration(new TaxCalculationConfiguration());
        modelBuilder.ApplyConfiguration(new TaxCalculationLineConfiguration());

        // Tax Configuration configurations
        modelBuilder.ApplyConfiguration(new TaxConfigurationConfiguration());
        modelBuilder.ApplyConfiguration(new TaxConfigurationHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new TaxConfigurationRuleConfiguration());
        modelBuilder.ApplyConfiguration(new GstConfigurationConfiguration());
        modelBuilder.ApplyConfiguration(new GstConfigurationHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new TdsConfigurationConfiguration());
        modelBuilder.ApplyConfiguration(new TdsConfigurationHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new HsnCodeConfiguration());
        modelBuilder.ApplyConfiguration(new HsnCodeHistoryConfiguration());
        modelBuilder.ApplyConfiguration(new HsnCodeGstMappingConfiguration());

        // Configure schema
        modelBuilder.HasDefaultSchema("financial_payment");
    }
}
