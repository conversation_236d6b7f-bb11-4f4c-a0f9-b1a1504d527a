using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.TransportCompany;

/// <summary>
/// Query to get transport company performance dashboard
/// </summary>
public record GetTransportCompanyDashboardQuery(
    Guid TransportCompanyId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<TransportCompanyDashboardDto>;

/// <summary>
/// Query to get transport company performance metrics
/// </summary>
public record GetTransportCompanyPerformanceQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeRFQAnalysis = true,
    bool IncludeBrokerPerformance = true,
    bool IncludeDeliveryMetrics = true
) : IRequest<TransportCompanyPerformanceDto>;

/// <summary>
/// Query to get RFQ conversion analytics for transport company
/// </summary>
public record GetRFQConversionAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? BrokerId = null,
    string? RouteType = null
) : IRequest<RFQConversionAnalyticsDto>;

/// <summary>
/// Query to get broker response time analytics for transport company
/// </summary>
public record GetBrokerResponseTimeAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    List<Guid>? BrokerIds = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<BrokerResponseTimeAnalyticsDto>;

/// <summary>
/// Query to get delivery performance analytics for transport company
/// </summary>
public record GetDeliveryPerformanceAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteType = null,
    string? CarrierType = null
) : IRequest<DeliveryPerformanceAnalyticsDto>;

/// <summary>
/// Query to get cost analysis for transport company
/// </summary>
public record GetTransportCompanyCostAnalysisQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludePricingOptimization = true,
    bool IncludeProfitabilityAnalysis = true
) : IRequest<TransportCompanyCostAnalysisDto>;

/// <summary>
/// Query to get market intelligence for transport company
/// </summary>
public record GetTransportCompanyMarketIntelligenceQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null,
    bool IncludeCompetitiveAnalysis = true
) : IRequest<TransportCompanyMarketIntelligenceDto>;

/// <summary>
/// Query to get broker performance comparison for transport company
/// </summary>
public record GetBrokerPerformanceComparisonQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    List<Guid>? BrokerIds = null,
    string? ComparisonMetric = null
) : IRequest<BrokerPerformanceComparisonDto>;

/// <summary>
/// Query to get predictive insights for transport company (Pro/Enterprise)
/// </summary>
public record GetTransportCompanyPredictiveInsightsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    List<string>? PredictionTypes = null,
    int ForecastDays = 30
) : IRequest<TransportCompanyPredictiveInsightsDto>;

/// <summary>
/// Query to get customer-specific analytics for transport company
/// </summary>
public record GetCustomerSpecificAnalyticsQuery(
    Guid TransportCompanyId,
    Guid? CustomerId = null,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    bool IncludeShipmentHistory = true,
    bool IncludePerformanceMetrics = true
) : IRequest<CustomerSpecificAnalyticsDto>;

/// <summary>
/// Query to get route optimization analytics for transport company
/// </summary>
public record GetRouteOptimizationAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    string? OriginCity = null,
    string? DestinationCity = null,
    string? RouteType = null
) : IRequest<RouteOptimizationAnalyticsDto>;

/// <summary>
/// Query to get pricing optimization recommendations for transport company
/// </summary>
public record GetPricingOptimizationQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    string? ServiceType = null,
    string? RouteType = null,
    bool IncludeMarketComparison = true
) : IRequest<PricingOptimizationDto>;

/// <summary>
/// Query to get operational efficiency metrics for transport company
/// </summary>
public record GetOperationalEfficiencyQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeResourceUtilization = true,
    bool IncludeProcessEfficiency = true
) : IRequest<OperationalEfficiencyDto>;

/// <summary>
/// Query to get customer acquisition metrics for transport company
/// </summary>
public record GetCustomerAcquisitionMetricsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    string? AcquisitionChannel = null
) : IRequest<CustomerAcquisitionMetricsDto>;

/// <summary>
/// Query to get service quality analytics for transport company
/// </summary>
public record GetServiceQualityAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeCustomerFeedback = true,
    bool IncludeQualityScores = true
) : IRequest<ServiceQualityAnalyticsDto>;

/// <summary>
/// Query to get financial performance analytics for transport company
/// </summary>
public record GetTransportCompanyFinancialPerformanceQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeRevenueBreakdown = true,
    bool IncludeProfitMargins = true,
    bool IncludeCostAnalysis = true
) : IRequest<TransportCompanyFinancialPerformanceDto>;

/// <summary>
/// Query to get network efficiency analytics for transport company
/// </summary>
public record GetNetworkEfficiencyAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeBrokerNetwork = true,
    bool IncludeCarrierNetwork = true,
    bool IncludeRouteEfficiency = true
) : IRequest<NetworkEfficiencyAnalyticsDto>;

/// <summary>
/// Query to get compliance and regulatory analytics for transport company
/// </summary>
public record GetTransportCompanyComplianceAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    string? ComplianceType = null,
    string? Region = null
) : IRequest<TransportCompanyComplianceAnalyticsDto>;

/// <summary>
/// Query to get business growth analytics for transport company
/// </summary>
public record GetBusinessGrowthAnalyticsQuery(
    Guid TransportCompanyId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeMarketExpansion = true,
    bool IncludeServiceExpansion = true
) : IRequest<BusinessGrowthAnalyticsDto>;
