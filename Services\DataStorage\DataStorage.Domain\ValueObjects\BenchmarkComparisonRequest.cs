using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for benchmark comparison
/// </summary>
public class BenchmarkComparisonRequest : ValueObject
{
    public List<Guid> BenchmarkIds { get; init; }
    public string ComparisonType { get; init; }
    public List<string> MetricsToCompare { get; init; }
    public string? GroupBy { get; init; }
    public bool IncludeStatistics { get; init; }
    public string? OutputFormat { get; init; }

    public BenchmarkComparisonRequest(
        List<Guid> benchmarkIds,
        string comparisonType,
        List<string> metricsToCompare,
        string? groupBy = null,
        bool includeStatistics = true,
        string? outputFormat = null)
    {
        BenchmarkIds = benchmarkIds;
        ComparisonType = comparisonType;
        MetricsToCompare = metricsToCompare;
        GroupBy = groupBy;
        IncludeStatistics = includeStatistics;
        OutputFormat = outputFormat;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ComparisonType;
        yield return GroupBy ?? string.Empty;
        yield return IncludeStatistics;
        yield return OutputFormat ?? string.Empty;
        
        foreach (var id in BenchmarkIds.OrderBy(x => x))
        {
            yield return id;
        }
        
        foreach (var metric in MetricsToCompare.OrderBy(x => x))
        {
            yield return metric;
        }
    }
}
