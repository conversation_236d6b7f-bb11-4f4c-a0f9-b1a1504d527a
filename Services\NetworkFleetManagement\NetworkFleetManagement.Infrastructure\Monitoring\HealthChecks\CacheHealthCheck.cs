using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Shared.Infrastructure.Caching;

namespace NetworkFleetManagement.Infrastructure.Monitoring.HealthChecks;

public class CacheHealthCheck : IHealthCheck
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<CacheHealthCheck> _logger;

    public CacheHealthCheck(ICacheService cacheService, ILogger<CacheHealthCheck> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var testKey = "health_check_test";
            var testValue = new { timestamp = DateTime.UtcNow, test = "cache_health_check" };
            
            // Test cache write
            await _cacheService.SetAsync(testKey, testValue, TimeSpan.FromMinutes(1));
            
            // Test cache read
            var retrievedValue = await _cacheService.GetAsync<object>(testKey);
            
            if (retrievedValue == null)
            {
                return HealthCheckResult.Degraded("Cache write/read test failed");
            }

            // Test cache delete
            await _cacheService.RemoveAsync(testKey);
            
            var data = new Dictionary<string, object>
            {
                ["cache_type"] = "Redis",
                ["test_completed"] = true,
                ["checked_at"] = DateTime.UtcNow
            };

            _logger.LogDebug("Cache health check passed");
            
            return HealthCheckResult.Healthy("Cache is healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Cache health check failed");
            return HealthCheckResult.Unhealthy("Cache health check failed", ex);
        }
    }
}
