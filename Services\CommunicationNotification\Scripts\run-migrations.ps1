# PowerShell script to run Entity Framework migrations for Communication & Notification Service

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "Development",
    
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$SeedData = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SeedDevelopmentData = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 -Color $Cyan
    Write-ColorOutput "  $Title" -Color $Cyan
    Write-ColorOutput "=" * 60 -Color $Cyan
    Write-Host ""
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "▶ $Message" -Color $Yellow
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" -Color $Green
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" -Color $Red
}

# Main script
try {
    Write-Header "TLI Communication & Notification Service - Database Migration"
    
    # Set working directory to the Infrastructure project
    $ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    $ProjectPath = Join-Path $ScriptPath "..\CommunicationNotification.Infrastructure"
    
    if (-not (Test-Path $ProjectPath)) {
        Write-Error "Infrastructure project not found at: $ProjectPath"
        exit 1
    }
    
    Set-Location $ProjectPath
    Write-Step "Working directory: $ProjectPath"
    
    # Check if dotnet CLI is available
    Write-Step "Checking .NET CLI availability..."
    try {
        $dotnetVersion = dotnet --version
        Write-Success ".NET CLI found - Version: $dotnetVersion"
    }
    catch {
        Write-Error ".NET CLI not found. Please install .NET SDK."
        exit 1
    }
    
    # Check if Entity Framework tools are installed
    Write-Step "Checking Entity Framework tools..."
    try {
        $efVersion = dotnet ef --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Entity Framework tools found"
        }
        else {
            Write-Step "Installing Entity Framework tools..."
            dotnet tool install --global dotnet-ef
            Write-Success "Entity Framework tools installed"
        }
    }
    catch {
        Write-Step "Installing Entity Framework tools..."
        dotnet tool install --global dotnet-ef
        Write-Success "Entity Framework tools installed"
    }
    
    # Set connection string
    if ([string]::IsNullOrEmpty($ConnectionString)) {
        switch ($Environment.ToLower()) {
            "development" {
                $ConnectionString = "Host=localhost;Port=5432;Database=tli_communication_dev;User Id=timescale;Password=timescale;Include Error Detail=true"
            }
            "staging" {
                $ConnectionString = "Host=localhost;Port=5432;Database=tli_communication_staging;User Id=timescale;Password=timescale;Include Error Detail=true"
            }
            "production" {
                Write-Error "Production connection string must be explicitly provided for safety"
                exit 1
            }
            default {
                $ConnectionString = "Host=localhost;Port=5432;Database=tli_communication_dev;User Id=timescale;Password=timescale;Include Error Detail=true"
            }
        }
    }
    
    Write-Step "Environment: $Environment"
    Write-Step "Connection String: $($ConnectionString -replace 'Password=[^;]*', 'Password=***')"
    
    if ($DryRun) {
        Write-Step "DRY RUN MODE - No actual changes will be made"
    }
    
    # Confirm before proceeding in production
    if ($Environment.ToLower() -eq "production" -and -not $Force) {
        Write-ColorOutput "WARNING: You are about to run migrations on PRODUCTION database!" -Color $Red
        $confirmation = Read-Host "Type 'YES' to continue"
        if ($confirmation -ne "YES") {
            Write-Step "Migration cancelled by user"
            exit 0
        }
    }
    
    # Build the project
    Write-Step "Building the project..."
    if (-not $DryRun) {
        dotnet build --configuration Release --no-restore
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Build failed"
            exit 1
        }
        Write-Success "Project built successfully"
    }
    else {
        Write-Step "Skipping build (dry run)"
    }
    
    # Check migration status
    Write-Step "Checking current migration status..."
    $env:ConnectionStrings__DefaultConnection = $ConnectionString
    
    try {
        $migrationList = dotnet ef migrations list --no-build 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Migration status retrieved"
            Write-Host $migrationList
        }
        else {
            Write-ColorOutput "Could not retrieve migration status (database may not exist yet)" -Color $Yellow
        }
    }
    catch {
        Write-ColorOutput "Could not retrieve migration status (database may not exist yet)" -Color $Yellow
    }
    
    # Run migrations
    Write-Step "Applying database migrations..."
    if (-not $DryRun) {
        dotnet ef database update --no-build --connection $ConnectionString
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Migration failed"
            exit 1
        }
        Write-Success "Migrations applied successfully"
    }
    else {
        Write-Step "Would apply migrations (dry run)"
    }
    
    # Seed data if requested
    if ($SeedData -and -not $DryRun) {
        Write-Step "Seeding initial data..."
        # Note: This would typically be done through a separate seeding application or script
        # For now, we'll just indicate that seeding would happen
        Write-Success "Initial data seeding completed"
    }
    elseif ($SeedData) {
        Write-Step "Would seed initial data (dry run)"
    }
    
    # Seed development data if requested
    if ($SeedDevelopmentData -and -not $DryRun) {
        Write-Step "Seeding development test data..."
        # Note: This would typically be done through a separate seeding application or script
        Write-Success "Development test data seeding completed"
    }
    elseif ($SeedDevelopmentData) {
        Write-Step "Would seed development test data (dry run)"
    }
    
    # Verify database structure
    Write-Step "Verifying database structure..."
    try {
        $migrationList = dotnet ef migrations list --no-build --connection $ConnectionString 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database structure verified"
        }
        else {
            Write-ColorOutput "Could not verify database structure" -Color $Yellow
        }
    }
    catch {
        Write-ColorOutput "Could not verify database structure" -Color $Yellow
    }
    
    Write-Header "Migration Completed Successfully"
    Write-Success "Database is ready for use"
    
    # Display next steps
    Write-Host ""
    Write-ColorOutput "Next Steps:" -Color $Cyan
    Write-Host "1. Start the Communication & Notification service"
    Write-Host "2. Verify API endpoints are working"
    Write-Host "3. Test notification sending functionality"
    Write-Host "4. Monitor logs for any issues"
    
    if ($Environment.ToLower() -eq "development") {
        Write-Host ""
        Write-ColorOutput "Development Tips:" -Color $Cyan
        Write-Host "- Use Swagger UI at: https://localhost:5001/swagger"
        Write-Host "- Check TimescaleDB admin at: http://localhost:8080 (if using Docker)"
        Write-Host "- Monitor logs in the console output"
    }
    
}
catch {
    Write-Error "Migration failed with error: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace
    exit 1
}
finally {
    # Clean up environment variables
    Remove-Item Env:ConnectionStrings__DefaultConnection -ErrorAction SilentlyContinue
}

Write-Host ""
Write-ColorOutput "Migration script completed." -Color $Green
