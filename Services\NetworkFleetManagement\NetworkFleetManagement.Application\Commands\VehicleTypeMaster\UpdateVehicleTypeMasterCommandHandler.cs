using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Interfaces;
using Shared.Domain.Common;
using Shared.Messaging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Handler for updating an existing vehicle type master
/// </summary>
public class UpdateVehicleTypeMasterCommandHandler : IRequestHandler<UpdateVehicleTypeMasterCommand, VehicleTypeMasterDto>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateVehicleTypeMasterCommandHandler> _logger;

    public UpdateVehicleTypeMasterCommandHandler(
        IVehicleTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        I<PERSON>apper mapper,
        IMessageBroker messageBroker,
        ILogger<UpdateVehicleTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterDto> Handle(UpdateVehicleTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating vehicle type master with ID: {Id}", request.Id);

            // Get existing entity
            var vehicleTypeMaster = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (vehicleTypeMaster == null)
            {
                throw new InvalidOperationException($"Vehicle type master with ID '{request.Id}' not found");
            }

            var oldName = vehicleTypeMaster.Name;
            var oldCategory = vehicleTypeMaster.Category;

            // Update entity
            vehicleTypeMaster.UpdateDetails(
                request.Name,
                request.Description,
                request.Category,
                request.MinLoadCapacityKg,
                request.MaxLoadCapacityKg,
                request.MinVolumeCapacityM3,
                request.MaxVolumeCapacityM3,
                request.SpecialRequirements,
                request.SortOrder,
                request.IconUrl,
                request.AdditionalProperties);

            // Update in repository
            _repository.Update(vehicleTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("vehicletypemaster.updated", new
            {
                Id = vehicleTypeMaster.Id,
                Code = vehicleTypeMaster.Code,
                Name = vehicleTypeMaster.Name,
                Category = vehicleTypeMaster.Category,
                OldName = oldName,
                OldCategory = oldCategory,
                IsActive = vehicleTypeMaster.IsActive,
                UpdatedAt = vehicleTypeMaster.UpdatedAt
            }, cancellationToken);

            // Map to DTO and return
            var result = _mapper.Map<VehicleTypeMasterDto>(vehicleTypeMaster);

            _logger.LogInformation("Successfully updated vehicle type master with ID: {Id}", request.Id);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle type master with ID: {Id}", request.Id);
            throw;
        }
    }
}

