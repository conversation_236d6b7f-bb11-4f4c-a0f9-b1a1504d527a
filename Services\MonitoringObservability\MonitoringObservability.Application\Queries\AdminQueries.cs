using MediatR;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Application.Commands;
using DomainEnums = MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Queries;

// Detailed System Metrics Queries
public class GetDetailedSystemMetricsQuery : IRequest<DetailedMetricsDto>
{
    public List<string>? ServiceNames { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromHours(6);
    public MetricType? MetricType { get; set; }
    public bool IncludeTrends { get; set; } = true;
    public bool IncludeAnomalies { get; set; } = true;
    public bool IncludeThresholds { get; set; } = true;
}

public class GetMetricTrendsQuery : IRequest<IEnumerable<MetricTrendDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan AnalysisPeriod { get; set; } = TimeSpan.FromDays(7);
    public double SignificanceThreshold { get; set; } = 0.1; // 10% change
    public bool OnlySignificantTrends { get; set; } = true;
}

public class GetMetricAnomaliesQuery : IRequest<IEnumerable<MetricAnomalyDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan DetectionWindow { get; set; } = TimeSpan.FromHours(24);
    public double ConfidenceThreshold { get; set; } = 0.8; // 80% confidence
    public AnomalySeverity? MinimumSeverity { get; set; }
}

// Capacity Planning Queries
public class GetCapacityPlanningQuery : IRequest<CapacityPlanningDto>
{
    public TimeSpan AnalysisWindow { get; set; } = TimeSpan.FromDays(30);
    public TimeSpan ProjectionPeriod { get; set; } = TimeSpan.FromDays(90);
    public List<string>? ServiceNames { get; set; }
    public bool IncludeResourceRecommendations { get; set; } = true;
    public bool IncludeScalingRecommendations { get; set; } = true;
    public double CapacityThreshold { get; set; } = 0.8; // 80% capacity threshold
}

public class GetResourceUtilizationTrendsQuery : IRequest<IEnumerable<ResourceUtilizationTrendDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
    public ResourceType? ResourceType { get; set; }
    public bool IncludeProjections { get; set; } = true;
}

public class GetCapacityForecastQuery : IRequest<CapacityForecastDto>
{
    public string? ServiceName { get; set; }
    public TimeSpan HistoricalPeriod { get; set; } = TimeSpan.FromDays(90);
    public TimeSpan ForecastPeriod { get; set; } = TimeSpan.FromDays(180);
    public double GrowthRateConfidence { get; set; } = 0.8;
}

// Escalation Rules Queries
public class GetEscalationRulesQuery : IRequest<IEnumerable<EscalationRuleDto>>
{
    public string? ServiceName { get; set; }
    public AlertSeverity? AlertSeverity { get; set; }
    public bool? IsEnabled { get; set; }
}

public class GetEscalationRuleByIdQuery : IRequest<EscalationRuleDto?>
{
    public Guid RuleId { get; set; }
}

public class GetEscalationHistoryQuery : IRequest<IEnumerable<EscalationHistoryDto>>
{
    public Guid? AlertId { get; set; }
    public Guid? IncidentId { get; set; }
    public TimeSpan? Period { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

// Notification Configuration Queries
public class GetNotificationConfigurationQuery : IRequest<NotificationConfigurationDto>
{
}

public class GetNotificationStatisticsQuery : IRequest<NotificationStatisticsDto>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(7);
    public NotificationChannel? Channel { get; set; }
    public bool IncludeFailureAnalysis { get; set; } = true;
}

public class GetNotificationHistoryQuery : IRequest<IEnumerable<NotificationHistoryDto>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(7);
    public NotificationChannel? Channel { get; set; }
    public string? Recipient { get; set; }
    public bool? Success { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 100;
}

// Maintenance Queries
public class GetMaintenanceRecommendationsQuery : IRequest<MaintenanceRecommendationsDto>
{
    public TimeSpan AnalysisPeriod { get; set; } = TimeSpan.FromDays(30);
    public bool IncludeDataCleanup { get; set; } = true;
    public bool IncludePerformanceOptimization { get; set; } = true;
    public bool IncludeSecurityRecommendations { get; set; } = true;
    public bool IncludeCapacityRecommendations { get; set; } = true;
}

public class GetScheduledMaintenanceQuery : IRequest<IEnumerable<ScheduledMaintenanceDto>>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public MaintenanceStatus? Status { get; set; }
    public bool IncludeCompleted { get; set; } = false;
}

public class GetMaintenanceHistoryQuery : IRequest<IEnumerable<MaintenanceHistoryDto>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(90);
    public MaintenanceTaskType? TaskType { get; set; }
    public bool? Success { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

// System Health and Performance Queries
public class GetSystemHealthTrendQuery : IRequest<SystemHealthTrendDto>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
    public TimeSpan SampleInterval { get; set; } = TimeSpan.FromHours(1);
    public bool IncludeServiceBreakdown { get; set; } = true;
}

public class GetPerformanceBenchmarksQuery : IRequest<PerformanceBenchmarksDto>
{
    public string? ServiceName { get; set; }
    public TimeSpan ComparisonPeriod { get; set; } = TimeSpan.FromDays(30);
    public bool IncludeIndustryBenchmarks { get; set; } = false;
    public bool IncludeHistoricalComparison { get; set; } = true;
}

public class GetSystemBottlenecksQuery : IRequest<IEnumerable<SystemBottleneckDto>>
{
    public TimeSpan AnalysisPeriod { get; set; } = TimeSpan.FromDays(7);
    public double ImpactThreshold { get; set; } = 0.1; // 10% performance impact
    public bool IncludeRecommendations { get; set; } = true;
}

// Security and Compliance Queries
public class GetSecurityAuditResultsQuery : IRequest<IEnumerable<SecurityAuditResultDto>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
    public SecurityCheckType? CheckType { get; set; }
    public SecuritySeverity? MinimumSeverity { get; set; }
    public bool OnlyUnresolved { get; set; } = false;
}

public class GetComplianceStatusQuery : IRequest<ComplianceStatusDto>
{
    public List<ComplianceStandard> Standards { get; set; } = new();
    public TimeSpan AuditPeriod { get; set; } = TimeSpan.FromDays(90);
    public bool IncludeRecommendations { get; set; } = true;
}

public class GetDataRetentionStatusQuery : IRequest<DataRetentionStatusDto>
{
    public List<DataType> DataTypes { get; set; } = new();
    public bool IncludeStorageUsage { get; set; } = true;
    public bool IncludeCleanupRecommendations { get; set; } = true;
}

// User Management Queries
public class GetMonitoringUsersQuery : IRequest<IEnumerable<MonitoringUserDto>>
{
    public bool IncludeInactive { get; set; } = false;
    public string? Role { get; set; }
    public DateTime? LastActiveAfter { get; set; }
}

public class GetUserActivityQuery : IRequest<IEnumerable<UserActivityDto>>
{
    public Guid? UserId { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
    public UserActivityType? ActivityType { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 100;
}

public class GetUserNotificationPreferencesQuery : IRequest<UserNotificationPreferencesDto?>
{
    public Guid UserId { get; set; }
}

// Report Queries
public class GetScheduledReportsQuery : IRequest<IEnumerable<ScheduledReportDto>>
{
    public bool IncludeInactive { get; set; } = false;
    public DomainEnums.ReportType? ReportType { get; set; }
    public Guid? CreatedBy { get; set; }
}

public class GetReportHistoryQuery : IRequest<IEnumerable<ReportHistoryDto>>
{
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(30);
    public DomainEnums.ReportType? ReportType { get; set; }
    public Guid? GeneratedBy { get; set; }
    public bool? Success { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

// Supporting DTOs for queries
public class ResourceUtilizationTrendDto
{
    public string ServiceName { get; set; } = string.Empty;
    public ResourceType ResourceType { get; set; }
    public List<ResourceDataPointDto> DataPoints { get; set; } = new();
    public TrendDirection Trend { get; set; }
    public double TrendPercentage { get; set; }
    public ResourceUtilizationDto? ProjectedUtilization { get; set; }
}

public class ResourceDataPointDto
{
    public DateTime Timestamp { get; set; }
    public double Utilization { get; set; }
    public double Capacity { get; set; }
    public Dictionary<string, double> Metrics { get; set; } = new();
}

public class EscalationRuleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ServiceName { get; set; }
    public AlertSeverity? AlertSeverity { get; set; }
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModified { get; set; }
}

public class EscalationLevelDto
{
    public int Level { get; set; }
    public TimeSpan Delay { get; set; }
    public List<string> Recipients { get; set; } = new();
    public List<NotificationChannel> Channels { get; set; } = new();
    public string? EscalationMessage { get; set; }
}

public class EscalationHistoryDto
{
    public Guid Id { get; set; }
    public Guid? AlertId { get; set; }
    public Guid? IncidentId { get; set; }
    public int EscalationLevel { get; set; }
    public DateTime EscalatedAt { get; set; }
    public Guid EscalatedBy { get; set; }
    public string Reason { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public bool NotificationSent { get; set; }
}

// Additional supporting enums
public enum MaintenanceStatus
{
    Scheduled = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum SecuritySeverity
{
    Info = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum UserActivityType
{
    Login = 0,
    AlertAction = 1,
    IncidentAction = 2,
    ConfigurationChange = 3,
    ReportGeneration = 4,
    SystemAdministration = 5
}
