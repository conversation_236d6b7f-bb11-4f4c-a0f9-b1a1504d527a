using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Repositories;
using FinancialPayment.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FinancialPayment.Application.Commands.PaymentLink;

/// <summary>
/// Handler for creating payment links
/// </summary>
public class CreatePaymentLinkCommandHandler : IRequestHandler<CreatePaymentLinkCommand, CreatePaymentLinkResult>
{
    private readonly IPaymentLinkRepository _paymentLinkRepository;
    private readonly IPaymentLinkNotificationService _notificationService;
    private readonly ITransportCompanyService _transportCompanyService;
    private readonly IShipperService _shipperService;
    private readonly ILogger<CreatePaymentLinkCommandHandler> _logger;

    public CreatePaymentLinkCommandHandler(
        IPaymentLinkRepository paymentLinkRepository,
        IPaymentLinkNotificationService notificationService,
        ITransportCompanyService transportCompanyService,
        IShipperService shipperService,
        ILogger<CreatePaymentLinkCommandHandler> logger)
    {
        _paymentLinkRepository = paymentLinkRepository;
        _notificationService = notificationService;
        _transportCompanyService = transportCompanyService;
        _shipperService = shipperService;
        _logger = logger;
    }

    public async Task<CreatePaymentLinkResult> Handle(CreatePaymentLinkCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating payment link for Transport Company {TransportCompanyId} and Shipper {ShipperId}",
            request.TransportCompanyId, request.ShipperId);

        try
        {
            // Validate the request
            var validationResult = await ValidateRequest(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new CreatePaymentLinkResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Validation failed",
                    ValidationErrors = validationResult.Errors,
                    CreatedAt = DateTime.UtcNow
                };
            }

            // Create contact details
            var shipperContactDetails = CreateShipperContactDetails(request);
            var transportCompanyContactDetails = CreateTransportCompanyContactDetails(request);

            // Create payment link settings
            var settings = CreatePaymentLinkSettings(request);

            // Determine expiry date
            var expiryDate = request.CustomExpiryDate ?? 
                           DateTime.UtcNow.AddDays(request.ExpiryDays ?? settings.DefaultExpiryDays);

            // Create the payment link
            var amount = new Money(request.Amount, request.Currency);
            var paymentLink = new Domain.Entities.PaymentLink(
                request.TransportCompanyId,
                request.ShipperId,
                amount,
                request.Description,
                request.LinkType,
                shipperContactDetails,
                transportCompanyContactDetails,
                settings,
                request.OrderId,
                request.InvoiceId,
                expiryDate);

            // Save to repository
            await _paymentLinkRepository.AddAsync(paymentLink, cancellationToken);

            // Send notifications if enabled
            var notificationResults = new List<string>();
            if (request.SendEmailNotification)
            {
                var emailResult = await _notificationService.SendPaymentLinkCreatedEmailAsync(
                    paymentLink, request.CustomSettings, cancellationToken);
                if (emailResult.IsSuccess)
                    notificationResults.Add("Email sent successfully");
                else
                    notificationResults.Add($"Email failed: {emailResult.ErrorMessage}");
            }

            if (request.SendSmsNotification && !string.IsNullOrWhiteSpace(request.ShipperPhone))
            {
                var smsResult = await _notificationService.SendPaymentLinkCreatedSmsAsync(
                    paymentLink, request.CustomSettings, cancellationToken);
                if (smsResult.IsSuccess)
                    notificationResults.Add("SMS sent successfully");
                else
                    notificationResults.Add($"SMS failed: {smsResult.ErrorMessage}");
            }

            var result = new CreatePaymentLinkResult
            {
                IsSuccess = true,
                PaymentLinkId = paymentLink.Id,
                LinkToken = paymentLink.LinkToken,
                PublicUrl = paymentLink.PublicUrl,
                ExpiresAt = paymentLink.ExpiresAt,
                CreatedAt = DateTime.UtcNow,
                Summary = paymentLink.GetSummary(),
                Metadata = new Dictionary<string, object>
                {
                    ["NotificationResults"] = notificationResults,
                    ["LinkType"] = request.LinkType.ToString(),
                    ["OrderId"] = request.OrderId?.ToString() ?? "N/A",
                    ["InvoiceId"] = request.InvoiceId?.ToString() ?? "N/A"
                }
            };

            _logger.LogInformation("Successfully created payment link {PaymentLinkId} with token {LinkToken}",
                paymentLink.Id, paymentLink.LinkToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment link for Transport Company {TransportCompanyId}",
                request.TransportCompanyId);

            return new CreatePaymentLinkResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                CreatedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ValidationResult> ValidateRequest(CreatePaymentLinkCommand request, CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        // Validate amount
        if (request.Amount <= 0)
            errors.Add("Amount must be greater than zero");

        // Validate required fields
        if (string.IsNullOrWhiteSpace(request.Description))
            errors.Add("Description is required");

        if (string.IsNullOrWhiteSpace(request.ShipperName))
            errors.Add("Shipper name is required");

        if (string.IsNullOrWhiteSpace(request.ShipperEmail))
            errors.Add("Shipper email is required");

        if (string.IsNullOrWhiteSpace(request.TransportCompanyName))
            errors.Add("Transport company name is required");

        if (string.IsNullOrWhiteSpace(request.TransportCompanyEmail))
            errors.Add("Transport company email is required");

        // Validate email formats
        if (!string.IsNullOrWhiteSpace(request.ShipperEmail) && !IsValidEmail(request.ShipperEmail))
            errors.Add("Invalid shipper email format");

        if (!string.IsNullOrWhiteSpace(request.TransportCompanyEmail) && !IsValidEmail(request.TransportCompanyEmail))
            errors.Add("Invalid transport company email format");

        // Validate expiry date
        if (request.CustomExpiryDate.HasValue && request.CustomExpiryDate.Value <= DateTime.UtcNow)
            errors.Add("Custom expiry date must be in the future");

        // Validate transport company exists
        var transportCompanyExists = await _transportCompanyService.ExistsAsync(request.TransportCompanyId, cancellationToken);
        if (!transportCompanyExists)
            errors.Add("Transport company not found");

        // Validate shipper exists
        var shipperExists = await _shipperService.ExistsAsync(request.ShipperId, cancellationToken);
        if (!shipperExists)
            errors.Add("Shipper not found");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private ContactDetails CreateShipperContactDetails(CreatePaymentLinkCommand request)
    {
        Address? address = null;
        if (!string.IsNullOrWhiteSpace(request.ShipperStreet) &&
            !string.IsNullOrWhiteSpace(request.ShipperCity) &&
            !string.IsNullOrWhiteSpace(request.ShipperState) &&
            !string.IsNullOrWhiteSpace(request.ShipperPostalCode))
        {
            address = new Address(
                request.ShipperStreet,
                request.ShipperCity,
                request.ShipperState,
                request.ShipperPostalCode,
                request.ShipperCountry ?? "India");
        }

        return new ContactDetails(
            request.ShipperName,
            request.ShipperEmail,
            request.ShipperPhone,
            request.ShipperCompanyName,
            address);
    }

    private ContactDetails CreateTransportCompanyContactDetails(CreatePaymentLinkCommand request)
    {
        return new ContactDetails(
            request.TransportCompanyName,
            request.TransportCompanyEmail,
            request.TransportCompanyPhone);
    }

    private PaymentLinkSettings CreatePaymentLinkSettings(CreatePaymentLinkCommand request)
    {
        return new PaymentLinkSettings(
            defaultExpiryDays: request.ExpiryDays ?? 7,
            maxAccessAttempts: 100,
            requiresAuthentication: request.RequiresAuthentication,
            sendEmailNotification: request.SendEmailNotification,
            sendSmsNotification: request.SendSmsNotification,
            allowPartialPayments: false,
            enablePaymentReminders: request.EnablePaymentReminders,
            reminderIntervalHours: request.ReminderIntervalHours,
            maxReminders: request.MaxReminders,
            allowedIpAddresses: request.AllowedIpAddresses.Any() ? request.AllowedIpAddresses : null,
            notificationEmails: request.NotificationEmails.Any() ? request.NotificationEmails : null,
            notificationPhones: request.NotificationPhones.Any() ? request.NotificationPhones : null,
            customSettings: request.CustomSettings);
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}

/// <summary>
/// Interface for payment link notification service
/// </summary>
public interface IPaymentLinkNotificationService
{
    Task<NotificationResult> SendPaymentLinkCreatedEmailAsync(
        Domain.Entities.PaymentLink paymentLink,
        Dictionary<string, object> customSettings,
        CancellationToken cancellationToken = default);

    Task<NotificationResult> SendPaymentLinkCreatedSmsAsync(
        Domain.Entities.PaymentLink paymentLink,
        Dictionary<string, object> customSettings,
        CancellationToken cancellationToken = default);

    Task<NotificationResult> SendPaymentLinkReminderAsync(
        Domain.Entities.PaymentLink paymentLink,
        List<string> channels,
        string? customMessage = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for transport company service
/// </summary>
public interface ITransportCompanyService
{
    Task<bool> ExistsAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<string> GetCompanyNameAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<ContactDetails> GetContactDetailsAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for shipper service
/// </summary>
public interface IShipperService
{
    Task<bool> ExistsAsync(Guid shipperId, CancellationToken cancellationToken = default);
    Task<string> GetShipperNameAsync(Guid shipperId, CancellationToken cancellationToken = default);
    Task<ContactDetails> GetContactDetailsAsync(Guid shipperId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Notification result
/// </summary>
public class NotificationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ExternalId { get; set; }
    public DateTime SentAt { get; set; }
}
