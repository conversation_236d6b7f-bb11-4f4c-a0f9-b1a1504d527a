using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class SecurityConfiguration
{
    public string EndpointId { get; private set; }
    public bool IsHttpsEnabled { get; private set; }
    public bool IsHttpsRedirectEnabled { get; private set; }
    public bool IsHstsEnabled { get; private set; }
    public TimeSpan HstsMaxAge { get; private set; }
    public List<SecurityHeader> SecurityHeaders { get; private set; }
    public List<AccessRule> AccessRules { get; private set; }
    public WafConfiguration? WafConfiguration { get; private set; }
    public DateTime LastModified { get; private set; }

    public SecurityConfiguration(
        string endpointId,
        bool isHttpsEnabled = true,
        bool isHttpsRedirectEnabled = true,
        bool isHstsEnabled = true,
        TimeSpan hstsMaxAge = default,
        List<SecurityHeader>? securityHeaders = null,
        List<AccessRule>? accessRules = null,
        WafConfiguration? wafConfiguration = null)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        IsHttpsEnabled = isHttpsEnabled;
        IsHttpsRedirectEnabled = isHttpsRedirectEnabled;
        IsHstsEnabled = isHstsEnabled;
        HstsMaxAge = hstsMaxAge == default ? TimeSpan.FromDays(365) : hstsMaxAge;
        SecurityHeaders = securityHeaders ?? GetDefaultSecurityHeaders();
        AccessRules = accessRules ?? new List<AccessRule>();
        WafConfiguration = wafConfiguration;
        LastModified = DateTime.UtcNow;
    }

    private static List<SecurityHeader> GetDefaultSecurityHeaders()
    {
        return new List<SecurityHeader>
        {
            new("X-Content-Type-Options", "nosniff"),
            new("X-Frame-Options", "DENY"),
            new("X-XSS-Protection", "1; mode=block"),
            new("Referrer-Policy", "strict-origin-when-cross-origin"),
            new("Content-Security-Policy", "default-src 'self'")
        };
    }
}

public class SecurityHeader
{
    public string Name { get; private set; }
    public string Value { get; private set; }
    public bool IsEnabled { get; private set; }

    public SecurityHeader(string name, string value, bool isEnabled = true)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Value = value ?? throw new ArgumentNullException(nameof(value));
        IsEnabled = isEnabled;
    }
}

public class AccessRule
{
    public string Id { get; private set; }
    public string Name { get; private set; }
    public AccessRuleType Type { get; private set; }
    public AccessRuleAction Action { get; private set; }
    public int Priority { get; private set; }
    public List<AccessCondition> Conditions { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    public AccessRule(
        string name,
        AccessRuleType type,
        AccessRuleAction action,
        List<AccessCondition> conditions,
        int priority = 0,
        bool isEnabled = true,
        DateTime? expiresAt = null)
    {
        Id = Guid.NewGuid().ToString();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Action = action;
        Conditions = conditions ?? throw new ArgumentNullException(nameof(conditions));
        Priority = priority;
        IsEnabled = isEnabled;
        CreatedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
    }
}

public class AccessCondition
{
    public AccessConditionType Type { get; private set; }
    public AccessConditionOperator Operator { get; private set; }
    public List<string> Values { get; private set; }
    public bool IsCaseSensitive { get; private set; }

    public AccessCondition(
        AccessConditionType type,
        AccessConditionOperator @operator,
        List<string> values,
        bool isCaseSensitive = false)
    {
        Type = type;
        Operator = @operator;
        Values = values ?? throw new ArgumentNullException(nameof(values));
        IsCaseSensitive = isCaseSensitive;
    }
}

public class WafConfiguration
{
    public bool IsEnabled { get; private set; }
    public WafMode Mode { get; private set; }
    public List<WafRule> Rules { get; private set; }
    public List<string> WhitelistedIps { get; private set; }
    public List<string> BlacklistedIps { get; private set; }
    public RateLimitConfiguration? RateLimitConfiguration { get; private set; }

    public WafConfiguration(
        bool isEnabled = true,
        WafMode mode = WafMode.Prevention,
        List<WafRule>? rules = null,
        List<string>? whitelistedIps = null,
        List<string>? blacklistedIps = null,
        RateLimitConfiguration? rateLimitConfiguration = null)
    {
        IsEnabled = isEnabled;
        Mode = mode;
        Rules = rules ?? GetDefaultWafRules();
        WhitelistedIps = whitelistedIps ?? new List<string>();
        BlacklistedIps = blacklistedIps ?? new List<string>();
        RateLimitConfiguration = rateLimitConfiguration;
    }

    private static List<WafRule> GetDefaultWafRules()
    {
        return new List<WafRule>
        {
            new("SQL Injection Protection", WafRuleType.SqlInjection, WafRuleAction.Block),
            new("XSS Protection", WafRuleType.CrossSiteScripting, WafRuleAction.Block),
            new("CSRF Protection", WafRuleType.CrossSiteRequestForgery, WafRuleAction.Block),
            new("Bot Protection", WafRuleType.BotDetection, WafRuleAction.Challenge)
        };
    }
}

public class WafRule
{
    public string Id { get; private set; }
    public string Name { get; private set; }
    public WafRuleType Type { get; private set; }
    public WafRuleAction Action { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public WafRule(
        string name,
        WafRuleType type,
        WafRuleAction action,
        bool isEnabled = true,
        int priority = 0,
        Dictionary<string, object>? parameters = null)
    {
        Id = Guid.NewGuid().ToString();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Action = action;
        IsEnabled = isEnabled;
        Priority = priority;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class RateLimitConfiguration
{
    public int RequestsPerMinute { get; private set; }
    public int RequestsPerHour { get; private set; }
    public int RequestsPerDay { get; private set; }
    public RateLimitAction Action { get; private set; }
    public TimeSpan BlockDuration { get; private set; }
    public List<string> ExemptedIps { get; private set; }

    public RateLimitConfiguration(
        int requestsPerMinute = 100,
        int requestsPerHour = 1000,
        int requestsPerDay = 10000,
        RateLimitAction action = RateLimitAction.Throttle,
        TimeSpan blockDuration = default,
        List<string>? exemptedIps = null)
    {
        RequestsPerMinute = requestsPerMinute;
        RequestsPerHour = requestsPerHour;
        RequestsPerDay = requestsPerDay;
        Action = action;
        BlockDuration = blockDuration == default ? TimeSpan.FromMinutes(15) : blockDuration;
        ExemptedIps = exemptedIps ?? new List<string>();
    }
}

// Additional helper classes for CDN management
public class CdnHealthCheck
{
    public string EndpointId { get; private set; }
    public CdnHealthStatus Status { get; private set; }
    public double ResponseTime { get; private set; }
    public int StatusCode { get; private set; }
    public string? ErrorMessage { get; private set; }
    public DateTime CheckedAt { get; private set; }
    public List<EdgeLocationHealth> EdgeLocationHealth { get; private set; }

    public CdnHealthCheck(
        string endpointId,
        CdnHealthStatus status,
        double responseTime,
        int statusCode,
        string? errorMessage = null,
        List<EdgeLocationHealth>? edgeLocationHealth = null)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Status = status;
        ResponseTime = responseTime;
        StatusCode = statusCode;
        ErrorMessage = errorMessage;
        CheckedAt = DateTime.UtcNow;
        EdgeLocationHealth = edgeLocationHealth ?? new List<EdgeLocationHealth>();
    }
}

public class EdgeLocationHealth
{
    public string LocationId { get; private set; }
    public string LocationName { get; private set; }
    public CdnHealthStatus Status { get; private set; }
    public double ResponseTime { get; private set; }
    public DateTime CheckedAt { get; private set; }

    public EdgeLocationHealth(
        string locationId,
        string locationName,
        CdnHealthStatus status,
        double responseTime)
    {
        LocationId = locationId ?? throw new ArgumentNullException(nameof(locationId));
        LocationName = locationName ?? throw new ArgumentNullException(nameof(locationName));
        Status = status;
        ResponseTime = responseTime;
        CheckedAt = DateTime.UtcNow;
    }
}

public class CdnUsageReport
{
    public string EndpointId { get; private set; }
    public TimeSpan Period { get; private set; }
    public long TotalRequests { get; private set; }
    public long TotalBandwidth { get; private set; }
    public double AverageResponseTime { get; private set; }
    public double CacheHitRatio { get; private set; }
    public Dictionary<string, long> TopCountries { get; private set; }
    public Dictionary<string, long> TopContentTypes { get; private set; }
    public Dictionary<string, long> TopStatusCodes { get; private set; }
    public decimal EstimatedCost { get; private set; }

    public CdnUsageReport(
        string endpointId,
        TimeSpan period,
        long totalRequests,
        long totalBandwidth,
        double averageResponseTime,
        double cacheHitRatio,
        Dictionary<string, long> topCountries,
        Dictionary<string, long> topContentTypes,
        Dictionary<string, long> topStatusCodes,
        decimal estimatedCost)
    {
        EndpointId = endpointId ?? throw new ArgumentNullException(nameof(endpointId));
        Period = period;
        TotalRequests = totalRequests;
        TotalBandwidth = totalBandwidth;
        AverageResponseTime = averageResponseTime;
        CacheHitRatio = cacheHitRatio;
        TopCountries = topCountries ?? new Dictionary<string, long>();
        TopContentTypes = topContentTypes ?? new Dictionary<string, long>();
        TopStatusCodes = topStatusCodes ?? new Dictionary<string, long>();
        EstimatedCost = estimatedCost;
    }
}
