using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Threshold entity for monitoring and alerting
/// </summary>
public class Threshold : BaseEntity
{
    public string Name { get; private set; } = string.Empty;
    public string MetricType { get; private set; } = string.Empty;
    public string MetricName { get; private set; } = string.Empty; // Alias for MetricType
    public ThresholdType ThresholdType { get; private set; }
    public decimal WarningThreshold { get; private set; }
    public decimal WarningValue { get; private set; } // Alias for WarningThreshold
    public decimal CriticalThreshold { get; private set; }
    public decimal CriticalValue { get; private set; } // Alias for CriticalThreshold
    public ComparisonOperator ComparisonOperator { get; private set; }
    public bool IsEnabled { get; private set; }
    public bool IsActive { get; private set; } // Alias for IsEnabled
    public string EntityType { get; private set; } = string.Empty;
    public UserRole UserRole { get; private set; }
    public Guid UserId { get; private set; } // User ID for threshold ownership
    public List<Guid> NotificationRecipients { get; private set; } = new();
    public Dictionary<string, object> Configuration { get; private set; } = new();
    public DateTime? LastEvaluatedAt { get; private set; }
    public DateTime? LastTriggeredAt { get; private set; }
    public int TriggerCount { get; private set; }

    private Threshold() { }

    public Threshold(
        string name,
        string metricType,
        decimal warningThreshold,
        decimal criticalThreshold,
        ComparisonOperator comparisonOperator,
        string entityType,
        UserRole userRole,
        Guid userId = default,
        ThresholdType thresholdType = ThresholdType.Performance,
        List<Guid>? notificationRecipients = null,
        Dictionary<string, object>? configuration = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be null or empty", nameof(name));

        if (string.IsNullOrWhiteSpace(metricType))
            throw new ArgumentException("Metric type cannot be null or empty", nameof(metricType));

        if (string.IsNullOrWhiteSpace(entityType))
            throw new ArgumentException("Entity type cannot be null or empty", nameof(entityType));

        Name = name;
        MetricType = metricType;
        MetricName = metricType; // Set alias
        ThresholdType = thresholdType;
        WarningThreshold = warningThreshold;
        WarningValue = warningThreshold; // Set alias
        CriticalThreshold = criticalThreshold;
        CriticalValue = criticalThreshold; // Set alias
        ComparisonOperator = comparisonOperator;
        EntityType = entityType;
        UserRole = userRole;
        UserId = userId;
        IsEnabled = true;
        IsActive = true; // Set alias
        NotificationRecipients = notificationRecipients ?? new List<Guid>();
        Configuration = configuration ?? new Dictionary<string, object>();
        TriggerCount = 0;
    }

    public bool EvaluateCondition(decimal currentValue)
    {
        LastEvaluatedAt = DateTime.UtcNow;
        SetUpdatedAt();

        return ComparisonOperator switch
        {
            ComparisonOperator.GreaterThan => currentValue > CriticalThreshold,
            ComparisonOperator.LessThan => currentValue < CriticalThreshold,
            ComparisonOperator.GreaterThanOrEqual => currentValue >= CriticalThreshold,
            ComparisonOperator.LessThanOrEqual => currentValue <= CriticalThreshold,
            ComparisonOperator.Equal => Math.Abs(currentValue - CriticalThreshold) < 0.001m,
            ComparisonOperator.NotEqual => Math.Abs(currentValue - CriticalThreshold) >= 0.001m,
            _ => false
        };
    }

    public AlertSeverity EvaluateSeverity(decimal currentValue)
    {
        var criticalTriggered = ComparisonOperator switch
        {
            ComparisonOperator.GreaterThan => currentValue > CriticalThreshold,
            ComparisonOperator.LessThan => currentValue < CriticalThreshold,
            ComparisonOperator.GreaterThanOrEqual => currentValue >= CriticalThreshold,
            ComparisonOperator.LessThanOrEqual => currentValue <= CriticalThreshold,
            ComparisonOperator.Equal => Math.Abs(currentValue - CriticalThreshold) < 0.001m,
            ComparisonOperator.NotEqual => Math.Abs(currentValue - CriticalThreshold) >= 0.001m,
            _ => false
        };

        if (criticalTriggered)
            return AlertSeverity.Critical;

        var warningTriggered = ComparisonOperator switch
        {
            ComparisonOperator.GreaterThan => currentValue > WarningThreshold,
            ComparisonOperator.LessThan => currentValue < WarningThreshold,
            ComparisonOperator.GreaterThanOrEqual => currentValue >= WarningThreshold,
            ComparisonOperator.LessThanOrEqual => currentValue <= WarningThreshold,
            ComparisonOperator.Equal => Math.Abs(currentValue - WarningThreshold) < 0.001m,
            ComparisonOperator.NotEqual => Math.Abs(currentValue - WarningThreshold) >= 0.001m,
            _ => false
        };

        return warningTriggered ? AlertSeverity.Warning : AlertSeverity.Info;
    }

    public void RecordTrigger()
    {
        LastTriggeredAt = DateTime.UtcNow;
        TriggerCount++;
        SetUpdatedAt();
    }

    public void Enable()
    {
        IsEnabled = true;
        IsActive = true; // Update alias
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        IsActive = false; // Update alias
        SetUpdatedAt();
    }

    public void UpdateThresholds(decimal warningThreshold, decimal criticalThreshold)
    {
        WarningThreshold = warningThreshold;
        WarningValue = warningThreshold; // Update alias
        CriticalThreshold = criticalThreshold;
        CriticalValue = criticalThreshold; // Update alias
        SetUpdatedAt();
    }

    public void AddNotificationRecipient(Guid userId)
    {
        if (!NotificationRecipients.Contains(userId))
        {
            NotificationRecipients.Add(userId);
            SetUpdatedAt();
        }
    }

    public void RemoveNotificationRecipient(Guid userId)
    {
        if (NotificationRecipients.Remove(userId))
        {
            SetUpdatedAt();
        }
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? new Dictionary<string, object>();
        SetUpdatedAt();
    }
}
