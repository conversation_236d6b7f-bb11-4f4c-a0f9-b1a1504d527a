using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class NotificationCampaign : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public Guid TemplateId { get; private set; }
    public string Status { get; private set; } // Draft, Scheduled, Running, Paused, Completed, Cancelled
    public string Type { get; private set; } // Immediate, Scheduled, Triggered, Recurring
    public DateTime? ScheduledAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Dictionary<string, object> TargetingCriteria { get; private set; }
    public Dictionary<string, object> Variables { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public List<string> TargetPlatforms { get; private set; }
    public int EstimatedRecipients { get; private set; }
    public int ActualRecipients { get; private set; }
    public int DeliveredCount { get; private set; }
    public int FailedCount { get; private set; }
    public int OpenedCount { get; private set; }
    public int ClickedCount { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual NotificationTemplate Template { get; private set; } = null!;
    public virtual ICollection<NotificationDelivery> Deliveries { get; private set; } = new List<NotificationDelivery>();

    private NotificationCampaign() { } // EF Core

    public NotificationCampaign(
        string name,
        string displayName,
        string description,
        Guid templateId,
        string type,
        Dictionary<string, object> targetingCriteria,
        List<string> targetPlatforms,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        TemplateId = templateId;
        Type = type ?? throw new ArgumentNullException(nameof(type));
        TargetingCriteria = targetingCriteria ?? throw new ArgumentNullException(nameof(targetingCriteria));
        TargetPlatforms = targetPlatforms ?? throw new ArgumentNullException(nameof(targetPlatforms));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        Status = "Draft";
        CreatedAt = DateTime.UtcNow;
        Variables = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();
        EstimatedRecipients = 0;
        ActualRecipients = 0;
        DeliveredCount = 0;
        FailedCount = 0;
        OpenedCount = 0;
        ClickedCount = 0;
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();

        AddDomainEvent(new NotificationCampaignCreatedEvent(Id, Name, Type, CreatedBy));
    }

    public void UpdateTargetingCriteria(Dictionary<string, object> newCriteria, string updatedBy)
    {
        if (Status != "Draft")
            throw new InvalidOperationException($"Cannot update targeting criteria for campaign with status: {Status}");

        TargetingCriteria = newCriteria ?? throw new ArgumentNullException(nameof(newCriteria));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignTargetingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateVariables(Dictionary<string, object> variables, string updatedBy)
    {
        Variables = variables ?? throw new ArgumentNullException(nameof(variables));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignVariablesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void Schedule(DateTime scheduledAt, string updatedBy)
    {
        if (Status != "Draft")
            throw new InvalidOperationException($"Cannot schedule campaign with status: {Status}");

        if (scheduledAt <= DateTime.UtcNow)
            throw new ArgumentException("Scheduled time must be in the future");

        ScheduledAt = scheduledAt;
        Status = "Scheduled";
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignScheduledEvent(Id, Name, ScheduledAt.Value, UpdatedBy));
    }

    public void Start(int actualRecipients)
    {
        if (Status != "Scheduled" && Status != "Draft")
            throw new InvalidOperationException($"Cannot start campaign with status: {Status}");

        Status = "Running";
        StartedAt = DateTime.UtcNow;
        ActualRecipients = actualRecipients;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignStartedEvent(Id, Name, ActualRecipients));
    }

    public void Pause(string updatedBy)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot pause campaign with status: {Status}");

        Status = "Paused";
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignPausedEvent(Id, Name, UpdatedBy));
    }

    public void Resume(string updatedBy)
    {
        if (Status != "Paused")
            throw new InvalidOperationException($"Cannot resume campaign with status: {Status}");

        Status = "Running";
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignResumedEvent(Id, Name, UpdatedBy));
    }

    public void Complete()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete campaign with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignCompletedEvent(Id, Name, DeliveredCount, FailedCount));
    }

    public void Cancel(string updatedBy)
    {
        if (Status == "Completed" || Status == "Cancelled")
            throw new InvalidOperationException($"Cannot cancel campaign with status: {Status}");

        Status = "Cancelled";
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationCampaignCancelledEvent(Id, Name, UpdatedBy));
    }

    public void RecordDelivery(bool isSuccessful)
    {
        if (isSuccessful)
        {
            DeliveredCount++;
        }
        else
        {
            FailedCount++;
        }

        UpdatedAt = DateTime.UtcNow;
    }

    public void RecordOpen()
    {
        OpenedCount++;
        UpdatedAt = DateTime.UtcNow;
    }

    public void RecordClick()
    {
        ClickedCount++;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetEstimatedRecipients(int count)
    {
        EstimatedRecipients = count;
        UpdatedAt = DateTime.UtcNow;
    }

    public double GetDeliveryRate()
    {
        return ActualRecipients > 0 ? (double)DeliveredCount / ActualRecipients * 100 : 0;
    }

    public double GetOpenRate()
    {
        return DeliveredCount > 0 ? (double)OpenedCount / DeliveredCount * 100 : 0;
    }

    public double GetClickRate()
    {
        return DeliveredCount > 0 ? (double)ClickedCount / DeliveredCount * 100 : 0;
    }

    public double GetClickThroughRate()
    {
        return OpenedCount > 0 ? (double)ClickedCount / OpenedCount * 100 : 0;
    }

    public bool IsActive()
    {
        return Status == "Running" || Status == "Scheduled";
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Cancelled";
    }

    public bool CanBeModified()
    {
        return Status == "Draft";
    }

    public bool CanBeStarted()
    {
        return Status == "Draft" || Status == "Scheduled";
    }

    public TimeSpan? GetDuration()
    {
        if (StartedAt.HasValue)
        {
            var endTime = CompletedAt ?? DateTime.UtcNow;
            return endTime - StartedAt.Value;
        }
        return null;
    }

    public bool IsScheduledForNow()
    {
        return Status == "Scheduled" && ScheduledAt.HasValue && ScheduledAt.Value <= DateTime.UtcNow;
    }

    public Dictionary<string, object> GetPerformanceMetrics()
    {
        return new Dictionary<string, object>
        {
            ["estimatedRecipients"] = EstimatedRecipients,
            ["actualRecipients"] = ActualRecipients,
            ["deliveredCount"] = DeliveredCount,
            ["failedCount"] = FailedCount,
            ["openedCount"] = OpenedCount,
            ["clickedCount"] = ClickedCount,
            ["deliveryRate"] = GetDeliveryRate(),
            ["openRate"] = GetOpenRate(),
            ["clickRate"] = GetClickRate(),
            ["clickThroughRate"] = GetClickThroughRate(),
            ["duration"] = GetDuration()?.TotalMinutes
        };
    }

    private void SetDefaultConfiguration()
    {
        Configuration["batch_size"] = 1000;
        Configuration["delay_between_batches"] = 1000; // milliseconds
        Configuration["max_retries"] = 3;
        Configuration["retry_delay"] = 5000; // milliseconds
        Configuration["track_opens"] = true;
        Configuration["track_clicks"] = true;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool MatchesTargetingCriteria(Dictionary<string, object> userAttributes)
    {
        foreach (var criteria in TargetingCriteria)
        {
            if (!EvaluateCriteria(criteria.Key, criteria.Value, userAttributes))
            {
                return false;
            }
        }
        return true;
    }

    private bool EvaluateCriteria(string criteriaKey, object criteriaValue, Dictionary<string, object> userAttributes)
    {
        if (!userAttributes.TryGetValue(criteriaKey, out var userValue))
        {
            return false;
        }

        if (criteriaValue is Dictionary<string, object> criteriaDict)
        {
            // Handle complex criteria like {"age": {"min": 18, "max": 65}}
            if (criteriaDict.TryGetValue("min", out var minValue) && criteriaDict.TryGetValue("max", out var maxValue))
            {
                if (userValue is IComparable comparableUserValue &&
                    minValue is IComparable comparableMin &&
                    maxValue is IComparable comparableMax)
                {
                    return comparableUserValue.CompareTo(comparableMin) >= 0 &&
                           comparableUserValue.CompareTo(comparableMax) <= 0;
                }
            }
        }
        else if (criteriaValue is List<object> criteriaList)
        {
            // Handle list criteria like {"platform": ["iOS", "Android"]}
            return criteriaList.Contains(userValue);
        }
        else
        {
            // Simple equality check
            return Equals(criteriaValue, userValue);
        }

        return false;
    }
}


