using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetInvoices;

public class GetInvoicesQueryHandler : IRequestHandler<GetInvoicesQuery, PagedResult<InvoiceSummaryDto>>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInvoicesQueryHandler> _logger;

    public GetInvoicesQueryHandler(
        IInvoiceRepository invoiceRepository,
        IMapper mapper,
        ILogger<GetInvoicesQueryHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<InvoiceSummaryDto>> Handle(GetInvoicesQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting invoices for user {UserId}, page {Page}, pageSize {PageSize}", 
            request.UserId, request.Page, request.PageSize);

        // Validate pagination parameters
        if (request.Page < 1) request.Page = 1;
        if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 20;

        try
        {
            // Get invoices with filters
            var (invoices, totalCount) = await GetInvoicesWithFilters(request, cancellationToken);

            // Map to DTOs
            var invoiceDtos = _mapper.Map<List<InvoiceSummaryDto>>(invoices);

            var result = new PagedResult<InvoiceSummaryDto>
            {
                Items = invoiceDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully retrieved {Count} invoices out of {TotalCount} for user {UserId}", 
                invoiceDtos.Count, totalCount, request.UserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving invoices for user {UserId}", request.UserId);
            throw;
        }
    }

    private async Task<(IEnumerable<Domain.Entities.Invoice> Invoices, int TotalCount)> GetInvoicesWithFilters(
        GetInvoicesQuery request, CancellationToken cancellationToken)
    {
        // Get paginated results
        var (invoices, totalCount) = await _invoiceRepository.GetPagedAsync(
            request.Page,
            request.PageSize,
            request.Status,
            cancellationToken);

        // Apply additional filters
        var filteredInvoices = invoices.AsEnumerable();

        // Filter by order ID if specified
        if (request.OrderId.HasValue)
        {
            filteredInvoices = filteredInvoices.Where(i => i.OrderId == request.OrderId.Value);
        }

        // Filter by date range
        if (request.FromDate.HasValue)
        {
            filteredInvoices = filteredInvoices.Where(i => i.CreatedAt >= request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            filteredInvoices = filteredInvoices.Where(i => i.CreatedAt <= request.ToDate.Value);
        }

        // Filter by due date (for overdue invoices)
        if (request.DueBefore.HasValue)
        {
            filteredInvoices = filteredInvoices.Where(i => i.DueDate <= request.DueBefore.Value);
        }

        // Filter overdue invoices only
        if (request.OverdueOnly == true)
        {
            var today = DateTime.UtcNow.Date;
            filteredInvoices = filteredInvoices.Where(i => 
                i.DueDate < today && 
                i.Status != InvoiceStatus.Paid && 
                i.Status != InvoiceStatus.Cancelled);
        }

        // Search functionality
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            filteredInvoices = filteredInvoices.Where(i => 
                i.InvoiceNumber.ToLower().Contains(searchTerm));
        }

        // Authorization filter - only show invoices for orders the user is involved in
        if (request.UserId.HasValue)
        {
            // Note: This requires loading the Order navigation property
            // In a real implementation, you might want to do this filtering at the repository level
            // for better performance
            filteredInvoices = filteredInvoices.Where(i => 
                i.Order.TransportCompanyId == request.UserId.Value ||
                i.Order.BrokerId == request.UserId.Value ||
                i.Order.CarrierId == request.UserId.Value);
        }

        // Apply sorting
        filteredInvoices = ApplySorting(filteredInvoices, request.SortBy, request.SortDirection);

        var finalInvoices = filteredInvoices.ToList();
        var finalCount = filteredInvoices.Count();

        return (finalInvoices, finalCount);
    }

    private static IEnumerable<Domain.Entities.Invoice> ApplySorting(
        IEnumerable<Domain.Entities.Invoice> invoices, 
        string? sortBy, 
        string? sortDirection)
    {
        var isDescending = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);

        return sortBy?.ToLower() switch
        {
            "invoicenumber" => isDescending 
                ? invoices.OrderByDescending(i => i.InvoiceNumber)
                : invoices.OrderBy(i => i.InvoiceNumber),
            "status" => isDescending 
                ? invoices.OrderByDescending(i => i.Status)
                : invoices.OrderBy(i => i.Status),
            "amount" => isDescending 
                ? invoices.OrderByDescending(i => i.TotalAmount.Amount)
                : invoices.OrderBy(i => i.TotalAmount.Amount),
            "duedate" => isDescending 
                ? invoices.OrderByDescending(i => i.DueDate)
                : invoices.OrderBy(i => i.DueDate),
            "invoicedate" => isDescending 
                ? invoices.OrderByDescending(i => i.InvoiceDate)
                : invoices.OrderBy(i => i.InvoiceDate),
            "paiddate" => isDescending 
                ? invoices.OrderByDescending(i => i.PaidDate)
                : invoices.OrderBy(i => i.PaidDate),
            _ => isDescending 
                ? invoices.OrderByDescending(i => i.CreatedAt)
                : invoices.OrderBy(i => i.CreatedAt)
        };
    }
}
