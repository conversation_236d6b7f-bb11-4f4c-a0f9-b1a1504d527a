using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.PauseSubscription
{
    public class PauseSubscriptionCommandHandler : IRequestHandler<PauseSubscriptionCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<PauseSubscriptionCommandHandler> _logger;

        public PauseSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<PauseSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(PauseSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Pausing subscription {SubscriptionId} for user {UserId}", 
                request.SubscriptionId, request.UserId);

            // Get subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // Authorization check
            if (subscription.UserId != request.UserId)
            {
                throw new SubscriptionDomainException("You can only pause your own subscription");
            }

            // Validate subscription can be paused
            if (subscription.Status != SubscriptionStatus.Active)
            {
                throw new SubscriptionDomainException("Only active subscriptions can be paused");
            }

            // Calculate refund if requested
            decimal refundAmount = 0;
            if (request.ProcessRefund)
            {
                refundAmount = CalculatePauseRefund(subscription);
            }

            // Process refund if applicable
            if (refundAmount > 0 && request.ProcessRefund)
            {
                var refundResult = await _paymentService.ProcessRefundAsync(subscription, refundAmount);
                if (!refundResult.IsSuccess)
                {
                    _logger.LogWarning("Refund processing failed for subscription {SubscriptionId}: {Error}", 
                        request.SubscriptionId, refundResult.ErrorMessage);
                    // Continue with pause even if refund fails
                }
            }

            // Pause the subscription
            subscription.Pause(request.PauseUntil, request.PauseReason);
            
            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish pause event
            await _messageBroker.PublishAsync("subscription.paused", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                PlanId = subscription.PlanId,
                PauseReason = request.PauseReason,
                PausedAt = DateTime.UtcNow,
                PauseUntil = request.PauseUntil,
                RefundAmount = refundAmount,
                Currency = subscription.CurrentPrice.Currency
            });

            _logger.LogInformation("Successfully paused subscription {SubscriptionId} for user {UserId}", 
                request.SubscriptionId, request.UserId);

            return true;
        }

        private decimal CalculatePauseRefund(Domain.Entities.Subscription subscription)
        {
            var now = DateTime.UtcNow;
            var daysInCurrentPeriod = (subscription.NextBillingDate - subscription.StartDate).Days;
            var daysRemaining = (subscription.NextBillingDate - now).Days;
            
            if (daysRemaining <= 0) return 0;

            var dailyRate = subscription.CurrentPrice.Amount / daysInCurrentPeriod;
            return dailyRate * daysRemaining;
        }
    }
}
