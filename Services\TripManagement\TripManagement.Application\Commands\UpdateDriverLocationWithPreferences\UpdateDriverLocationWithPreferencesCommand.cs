using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Repositories;
using TripManagement.Domain.ValueObjects;
using TLI.Shared.Infrastructure.Messaging;

namespace TripManagement.Application.Commands.UpdateDriverLocationWithPreferences;

public class UpdateDriverLocationWithPreferencesCommand : IRequest<UpdateDriverLocationWithPreferencesResponse>
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public DateTime GpsTimestamp { get; set; } = DateTime.UtcNow;
    public LocationUpdateSource Source { get; set; } = LocationUpdateSource.GPS;
    public string? LocationDescription { get; set; }
    public bool ForceUpdate { get; set; } = false; // Override location sharing preferences
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class UpdateDriverLocationWithPreferencesResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public bool LocationUpdated { get; set; }
    public bool LocationSharingEnabled { get; set; }
    public DateTime? LastGpsTimestamp { get; set; }
    public double? LastLatitude { get; set; }
    public double? LastLongitude { get; set; }
    public double? LastAccuracy { get; set; }
    public string? LocationDescription { get; set; }
    public bool IsLocationStale { get; set; }
    public bool IsLocationAccurate { get; set; }
    public TimeSpan TimeSinceLastUpdate { get; set; }
    public List<string> Warnings { get; set; } = new();
    public LocationUpdateMetrics Metrics { get; set; } = new();
}

public class LocationUpdateMetrics
{
    public double DistanceFromPreviousMeters { get; set; }
    public double SpeedKmh { get; set; }
    public bool IsMoving { get; set; }
    public bool IsWithinAccuracyThreshold { get; set; }
    public bool IsSignificantUpdate { get; set; }
    public string GeofenceStatus { get; set; } = "Unknown";
    public List<string> GeofenceZones { get; set; } = new();
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}

// Command Handler

public class UpdateDriverLocationWithPreferencesCommandHandler : IRequestHandler<UpdateDriverLocationWithPreferencesCommand, UpdateDriverLocationWithPreferencesResponse>
{
    private readonly IDriverRepository _driverRepository;
    private readonly IDriverLocationPreferencesRepository _preferencesRepository;
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateDriverLocationWithPreferencesCommandHandler> _logger;

    public UpdateDriverLocationWithPreferencesCommandHandler(
        IDriverRepository driverRepository,
        IDriverLocationPreferencesRepository preferencesRepository,
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<UpdateDriverLocationWithPreferencesCommandHandler> logger)
    {
        _driverRepository = driverRepository;
        _preferencesRepository = preferencesRepository;
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<UpdateDriverLocationWithPreferencesResponse> Handle(UpdateDriverLocationWithPreferencesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing location update for driver {DriverId}", request.DriverId);

            // Get driver
            var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
            if (driver == null)
            {
                return new UpdateDriverLocationWithPreferencesResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Driver not found"
                };
            }

            // Get location preferences
            var preferences = await _preferencesRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            if (preferences == null)
            {
                // Create default preferences if none exist
                preferences = new DriverLocationPreferencesEntity(request.DriverId);
                await _preferencesRepository.AddAsync(preferences, cancellationToken);
            }

            var warnings = new List<string>();
            var locationUpdated = false;

            // Check if location sharing is enabled or force update is requested
            if (!preferences.LocationSharingEnabled && !request.ForceUpdate)
            {
                warnings.Add("Location sharing is disabled for this driver");
                return CreateResponse(false, preferences, warnings, "Location sharing is disabled");
            }

            // Validate location accuracy
            if (request.Accuracy.HasValue && request.Accuracy.Value > preferences.LocationAccuracyThresholdMeters)
            {
                warnings.Add($"Location accuracy ({request.Accuracy:F1}m) exceeds threshold ({preferences.LocationAccuracyThresholdMeters:F1}m)");
            }

            // Create location object
            var location = new Location(
                request.Latitude,
                request.Longitude,
                request.GpsTimestamp,
                request.Altitude,
                request.Accuracy,
                request.LocationDescription);

            // Calculate metrics
            var metrics = await CalculateLocationMetrics(preferences, location, request, cancellationToken);

            // Update driver location
            driver.UpdateLocation(location);
            _driverRepository.Update(driver);

            // Update preferences with new location data
            preferences.UpdateLastLocation(
                request.Latitude,
                request.Longitude,
                request.Accuracy,
                request.LocationDescription);
            _preferencesRepository.Update(preferences);

            // Update trip location if trip is specified
            if (request.TripId.HasValue)
            {
                var trip = await _tripRepository.GetByIdAsync(request.TripId.Value, cancellationToken);
                if (trip != null)
                {
                    trip.UpdateLocation(location, request.Source);
                    _tripRepository.Update(trip);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            locationUpdated = true;

            // Publish location update events
            await PublishLocationUpdateEvents(request, preferences, metrics, cancellationToken);

            _logger.LogDebug("Location updated successfully for driver {DriverId}", request.DriverId);

            return CreateResponse(true, preferences, warnings, null, locationUpdated, metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location for driver {DriverId}", request.DriverId);
            return new UpdateDriverLocationWithPreferencesResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<LocationUpdateMetrics> CalculateLocationMetrics(
        DriverLocationPreferencesEntity preferences,
        Location newLocation,
        UpdateDriverLocationWithPreferencesCommand request,
        CancellationToken cancellationToken)
    {
        var metrics = new LocationUpdateMetrics
        {
            SpeedKmh = request.Speed ?? 0,
            IsWithinAccuracyThreshold = request.Accuracy.HasValue && request.Accuracy.Value <= preferences.LocationAccuracyThresholdMeters,
            ProcessedAt = DateTime.UtcNow
        };

        // Calculate distance from previous location
        if (preferences.LastLatitude.HasValue && preferences.LastLongitude.HasValue)
        {
            var previousLocation = new Location(preferences.LastLatitude.Value, preferences.LastLongitude.Value);
            var distanceKm = newLocation.CalculateDistanceKm(previousLocation);
            metrics.DistanceFromPreviousMeters = distanceKm * 1000;
        }

        // Determine if moving
        metrics.IsMoving = metrics.SpeedKmh > 5.0; // Moving if speed > 5 km/h

        // Determine if significant update
        metrics.IsSignificantUpdate = metrics.DistanceFromPreviousMeters > 50 || // Moved more than 50 meters
                                    Math.Abs(metrics.SpeedKmh - (request.Speed ?? 0)) > 10 || // Speed changed by more than 10 km/h
                                    preferences.GetTimeSinceLastUpdate().TotalMinutes > 5; // More than 5 minutes since last update

        return metrics;
    }

    private async Task PublishLocationUpdateEvents(
        UpdateDriverLocationWithPreferencesCommand request,
        DriverLocationPreferencesEntity preferences,
        LocationUpdateMetrics metrics,
        CancellationToken cancellationToken)
    {
        // Publish to real-time tracking service
        await _messageBroker.PublishAsync("driver.location.updated", new
        {
            DriverId = request.DriverId,
            TripId = request.TripId,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            Altitude = request.Altitude,
            Accuracy = request.Accuracy,
            Speed = request.Speed,
            Heading = request.Heading,
            GpsTimestamp = request.GpsTimestamp,
            Source = request.Source.ToString(),
            LocationDescription = request.LocationDescription,
            Metrics = metrics,
            UpdatedAt = DateTime.UtcNow
        }, cancellationToken);

        // Publish to analytics service
        await _messageBroker.PublishAsync("analytics.driver.location", new
        {
            DriverId = request.DriverId,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            Speed = request.Speed,
            Accuracy = request.Accuracy,
            Timestamp = request.GpsTimestamp,
            IsSignificantUpdate = metrics.IsSignificantUpdate,
            DistanceFromPreviousMeters = metrics.DistanceFromPreviousMeters
        }, cancellationToken);
    }

    private UpdateDriverLocationWithPreferencesResponse CreateResponse(
        bool isSuccess,
        DriverLocationPreferencesEntity preferences,
        List<string> warnings,
        string? errorMessage = null,
        bool locationUpdated = false,
        LocationUpdateMetrics? metrics = null)
    {
        return new UpdateDriverLocationWithPreferencesResponse
        {
            IsSuccess = isSuccess,
            ErrorMessage = errorMessage,
            LocationUpdated = locationUpdated,
            LocationSharingEnabled = preferences.LocationSharingEnabled,
            LastGpsTimestamp = preferences.LastGpsTimestamp,
            LastLatitude = preferences.LastLatitude,
            LastLongitude = preferences.LastLongitude,
            LastAccuracy = preferences.LastAccuracy,
            LocationDescription = preferences.LastKnownLocation,
            IsLocationStale = preferences.IsLocationDataStale(),
            IsLocationAccurate = preferences.IsLocationAccurate(),
            TimeSinceLastUpdate = preferences.GetTimeSinceLastUpdate(),
            Warnings = warnings,
            Metrics = metrics ?? new LocationUpdateMetrics()
        };
    }
}
