using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqMilestoneAssignmentConfiguration : IEntityTypeConfiguration<RfqMilestoneAssignment>
{
    public void Configure(EntityTypeBuilder<RfqMilestoneAssignment> builder)
    {
        builder.ToTable("RfqMilestoneAssignments");

        builder.HasKey(rma => rma.Id);

        builder.Property(rma => rma.Id)
            .ValueGeneratedOnAdd();

        builder.Property(rma => rma.RfqId)
            .IsRequired();

        builder.Property(rma => rma.MilestoneTemplateId)
            .IsRequired();

        builder.Property(rma => rma.AssignedAt)
            .IsRequired();

        builder.Property(rma => rma.AssignedBy)
            .IsRequired();

        builder.Property(rma => rma.AssignedByName)
            .HasMaxLength(200);

        builder.Property(rma => rma.IsActive)
            .IsRequired();

        builder.Property(rma => rma.DeactivatedAt)
            .IsRequired(false);

        builder.Property(rma => rma.DeactivatedBy)
            .IsRequired(false);

        builder.Property(rma => rma.DeactivationReason)
            .HasMaxLength(500);

        // Configure TotalContractValue as owned entity
        builder.OwnsOne(rma => rma.TotalContractValue, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("TotalContractValue_Amount")
                .HasPrecision(18, 2);

            money.Property(m => m.Currency)
                .HasColumnName("TotalContractValue_Currency")
                .HasMaxLength(3);
        });

        builder.Property(rma => rma.CustomPayoutStructure)
            .HasMaxLength(2000);

        // Configure AssignmentMetadata as JSON
        builder.Property(rma => rma.AssignmentMetadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        // Relationships
        builder.HasOne(rma => rma.RFQ)
            .WithMany(r => r.MilestoneAssignments)
            .HasForeignKey(rma => rma.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(rma => rma.MilestoneTemplate)
            .WithMany(mt => mt.RfqAssignments)
            .HasForeignKey(rma => rma.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(rma => rma.MilestoneProgress)
            .WithOne(rmp => rmp.RfqMilestoneAssignment)
            .HasForeignKey(rmp => rmp.RfqMilestoneAssignmentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rma => rma.RfqId)
            .HasDatabaseName("IX_RfqMilestoneAssignments_RfqId");

        builder.HasIndex(rma => rma.MilestoneTemplateId)
            .HasDatabaseName("IX_RfqMilestoneAssignments_MilestoneTemplateId");

        builder.HasIndex(rma => rma.IsActive)
            .HasDatabaseName("IX_RfqMilestoneAssignments_IsActive");

        builder.HasIndex(rma => rma.AssignedAt)
            .HasDatabaseName("IX_RfqMilestoneAssignments_AssignedAt");

        builder.HasIndex(rma => new { rma.RfqId, rma.IsActive })
            .HasDatabaseName("IX_RfqMilestoneAssignments_RfqId_IsActive");
    }
}
