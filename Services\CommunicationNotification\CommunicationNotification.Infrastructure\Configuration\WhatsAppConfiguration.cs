namespace CommunicationNotification.Infrastructure.Configuration;

/// <summary>
/// WhatsApp Business API configuration
/// </summary>
public class WhatsAppConfiguration
{
    /// <summary>
    /// WhatsApp Business API base URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://graph.facebook.com/v18.0";

    /// <summary>
    /// Access token for WhatsApp Business API
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Phone number ID from WhatsApp Business API
    /// </summary>
    public string PhoneNumberId { get; set; } = string.Empty;

    /// <summary>
    /// Business account ID
    /// </summary>
    public string BusinessAccountId { get; set; } = string.Empty;

    /// <summary>
    /// Webhook verify token
    /// </summary>
    public string WebhookVerifyToken { get; set; } = string.Empty;

    /// <summary>
    /// Webhook URL for receiving status updates
    /// </summary>
    public string WebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Maximum retry attempts for failed messages
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Timeout for API calls in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Enable webhook for delivery status updates
    /// </summary>
    public bool EnableWebhook { get; set; } = true;

    /// <summary>
    /// Default language code for templates
    /// </summary>
    public string DefaultLanguageCode { get; set; } = "en";

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public WhatsAppRateLimitConfiguration RateLimit { get; set; } = new();
}

/// <summary>
/// WhatsApp rate limiting configuration
/// </summary>
public class WhatsAppRateLimitConfiguration
{
    /// <summary>
    /// Maximum messages per second
    /// </summary>
    public int MessagesPerSecond { get; set; } = 10;

    /// <summary>
    /// Maximum messages per minute
    /// </summary>
    public int MessagesPerMinute { get; set; } = 600;

    /// <summary>
    /// Maximum messages per hour
    /// </summary>
    public int MessagesPerHour { get; set; } = 10000;

    /// <summary>
    /// Enable rate limiting
    /// </summary>
    public bool EnableRateLimit { get; set; } = true;
}

/// <summary>
/// Twilio configuration
/// </summary>
public class TwilioConfiguration
{
    /// <summary>
    /// Twilio Account SID
    /// </summary>
    public string AccountSid { get; set; } = string.Empty;

    /// <summary>
    /// Twilio Auth Token
    /// </summary>
    public string AuthToken { get; set; } = string.Empty;

    /// <summary>
    /// From phone number for SMS and voice
    /// </summary>
    public string FromPhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// Status callback URL for delivery receipts
    /// </summary>
    public string StatusCallbackUrl { get; set; } = string.Empty;

    /// <summary>
    /// Maximum retry attempts for failed messages
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Timeout for API calls in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// SendGrid configuration
/// </summary>
public class SendGridConfiguration
{
    /// <summary>
    /// SendGrid API key
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// From email address
    /// </summary>
    public string FromEmail { get; set; } = string.Empty;

    /// <summary>
    /// From name
    /// </summary>
    public string FromName { get; set; } = string.Empty;

    /// <summary>
    /// Enable click tracking
    /// </summary>
    public bool EnableClickTracking { get; set; } = true;

    /// <summary>
    /// Enable open tracking
    /// </summary>
    public bool EnableOpenTracking { get; set; } = true;

    /// <summary>
    /// Webhook URL for email events
    /// </summary>
    public string WebhookUrl { get; set; } = string.Empty;

    /// <summary>
    /// Maximum retry attempts for failed emails
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;
}

/// <summary>
/// Firebase configuration
/// </summary>
public class FirebaseConfiguration
{
    /// <summary>
    /// Firebase project ID
    /// </summary>
    public string ProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Path to service account key file
    /// </summary>
    public string ServiceAccountKeyPath { get; set; } = string.Empty;

    /// <summary>
    /// Service account key JSON content
    /// </summary>
    public string ServiceAccountKeyJson { get; set; } = string.Empty;

    /// <summary>
    /// Default notification icon
    /// </summary>
    public string DefaultIcon { get; set; } = "ic_notification";

    /// <summary>
    /// Default notification color
    /// </summary>
    public string DefaultColor { get; set; } = "#FF0000";

    /// <summary>
    /// Enable analytics
    /// </summary>
    public bool EnableAnalytics { get; set; } = true;
}
