using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Services;

public interface IUsageTrackingService
{
    // Usage recording
    Task RecordUsageAsync(Guid userId, FeatureType featureType, int count = 1, string? metadata = null);
    Task<bool> CanUseFeatureAsync(Guid userId, FeatureType featureType, int requestedCount = 1);
    Task<int> GetRemainingUsageAsync(Guid userId, FeatureType featureType);
    
    // Usage queries
    Task<List<UsageRecord>> GetUsageHistoryAsync(Guid userId, FeatureType? featureType = null, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<FeatureType, int>> GetCurrentPeriodUsageAsync(Guid userId);
    Task<Dictionary<FeatureType, int>> GetUsageSummaryAsync(Guid userId, DateTime from, DateTime to);
    
    // Limit management
    Task<Dictionary<FeatureType, int>> GetFeatureLimitsAsync(Guid userId);
    Task<bool> IsFeatureUnlimitedAsync(Guid userId, FeatureType featureType);
    Task<DateTime> GetUsagePeriodResetDateAsync(Guid userId);
    
    // Analytics
    Task<Dictionary<string, object>> GetUsageAnalyticsAsync(Guid userId, DateTime? from = null, DateTime? to = null);
    Task<List<UsageRecord>> GetTopUsersAsync(FeatureType featureType, int count = 10, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<FeatureType, double>> GetAverageUsageAsync(DateTime from, DateTime to);
    
    // Alerts and notifications
    Task<List<Guid>> GetUsersNearLimitAsync(FeatureType featureType, double threshold = 0.8);
    Task<List<Guid>> GetUsersOverLimitAsync(FeatureType featureType);
    Task SendUsageAlertAsync(Guid userId, FeatureType featureType, int currentUsage, int limit);
}
