using CommunicationNotification.Application.Queries.Analytics;
using CommunicationNotification.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Analytics API controller for communication metrics and insights
/// </summary>
[ApiController]
[Route("api/communication/analytics")]
[Authorize]
public class AnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;

    public AnalyticsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get message performance metrics
    /// </summary>
    [HttpGet("performance")]
    public async Task<IActionResult> GetMessagePerformance(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetMessagePerformanceQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            MessageType = messageType,
            Channel = channel,
            UserId = userId
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get channel performance comparison
    /// </summary>
    [HttpGet("channels")]
    public async Task<IActionResult> GetChannelPerformance(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] List<NotificationChannel>? channels = null,
        [FromQuery] bool includeHourlyDistribution = true,
        CancellationToken cancellationToken = default)
    {
        var query = new GetChannelPerformanceQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            Channels = channels,
            IncludeHourlyDistribution = includeHourlyDistribution
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get user engagement metrics
    /// </summary>
    [HttpGet("engagement/{userId}")]
    public async Task<IActionResult> GetUserEngagement(
        Guid userId,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] bool includeMessageTypePreferences = true,
        CancellationToken cancellationToken = default)
    {
        var query = new GetUserEngagementQuery
        {
            UserId = userId,
            StartDate = startDate,
            EndDate = endDate,
            IncludeMessageTypePreferences = includeMessageTypePreferences
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get time-based analytics trends
    /// </summary>
    [HttpGet("trends")]
    public async Task<IActionResult> GetTimeBasedAnalytics(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] TimePeriodType periodType = TimePeriodType.Day,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetTimeBasedAnalyticsQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            PeriodType = periodType,
            MessageType = messageType,
            Channel = channel,
            UserId = userId
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get real-time dashboard data
    /// </summary>
    [HttpGet("dashboard/realtime")]
    public async Task<IActionResult> GetRealTimeDashboard(
        [FromQuery] UserRole? userRole = null,
        [FromQuery] List<string>? metricTypes = null,
        [FromQuery] int refreshIntervalSeconds = 30,
        CancellationToken cancellationToken = default)
    {
        var currentUserRole = GetCurrentUserRole();
        var actualUserRole = userRole ?? currentUserRole;
        var currentUserId = GetCurrentUserId();

        var query = new GetRealTimeDashboardQuery
        {
            UserRole = actualUserRole,
            UserId = currentUserId,
            MetricTypes = metricTypes,
            RefreshIntervalSeconds = refreshIntervalSeconds
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get cost analysis metrics
    /// </summary>
    [HttpGet("costs")]
    public async Task<IActionResult> GetCostAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] string? campaignId = null,
        [FromQuery] bool groupByChannel = true,
        [FromQuery] bool groupByMessageType = false,
        [FromQuery] bool groupByDay = false,
        CancellationToken cancellationToken = default)
    {
        var query = new GetCostAnalysisQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            Channel = channel,
            CampaignId = campaignId,
            GroupByChannel = groupByChannel,
            GroupByMessageType = groupByMessageType,
            GroupByDay = groupByDay
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get A/B test performance comparison
    /// </summary>
    [HttpGet("ab-tests/{testId}")]
    public async Task<IActionResult> GetABTestPerformance(
        string testId,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] List<string>? variants = null,
        [FromQuery] bool includeStatisticalSignificance = true,
        CancellationToken cancellationToken = default)
    {
        var query = new GetABTestPerformanceQuery
        {
            TestId = testId,
            StartDate = startDate,
            EndDate = endDate,
            Variants = variants,
            IncludeStatisticalSignificance = includeStatisticalSignificance
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get top performing message templates
    /// </summary>
    [HttpGet("templates/top")]
    public async Task<IActionResult> GetTopPerformingTemplates(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] int limit = 10,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] string orderBy = "EngagementScore",
        CancellationToken cancellationToken = default)
    {
        var query = new GetTopPerformingTemplatesQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            Limit = limit,
            MessageType = messageType,
            Channel = channel,
            OrderBy = orderBy
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get user behavior insights
    /// </summary>
    [HttpGet("insights/behavior")]
    public async Task<IActionResult> GetUserBehaviorInsights(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] UserRole? userRole = null,
        [FromQuery] List<string>? insightTypes = null,
        [FromQuery] int minimumSampleSize = 100,
        CancellationToken cancellationToken = default)
    {
        var query = new GetUserBehaviorInsightsQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            UserRole = userRole,
            InsightTypes = insightTypes,
            MinimumSampleSize = minimumSampleSize
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get message delivery funnel analytics
    /// </summary>
    [HttpGet("funnel")]
    public async Task<IActionResult> GetMessageDeliveryFunnel(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] string? campaignId = null,
        [FromQuery] bool groupByChannel = false,
        CancellationToken cancellationToken = default)
    {
        var query = new GetMessageDeliveryFunnelQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            MessageType = messageType,
            Channel = channel,
            CampaignId = campaignId,
            GroupByChannel = groupByChannel
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get user segment analytics
    /// </summary>
    [HttpGet("segments")]
    public async Task<IActionResult> GetUserSegmentAnalytics(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] List<string>? segmentCriteria = null,
        [FromQuery] int minimumSegmentSize = 50,
        CancellationToken cancellationToken = default)
    {
        var query = new GetUserSegmentAnalyticsQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            SegmentCriteria = segmentCriteria,
            MinimumSegmentSize = minimumSegmentSize
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    /// <summary>
    /// Get message heatmap data
    /// </summary>
    [HttpGet("heatmap")]
    public async Task<IActionResult> GetMessageHeatmap(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string heatmapType = "HourlyEngagement",
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] string timeZone = "UTC",
        CancellationToken cancellationToken = default)
    {
        var query = new GetMessageHeatmapQuery
        {
            StartDate = startDate,
            EndDate = endDate,
            HeatmapType = heatmapType,
            MessageType = messageType,
            Channel = channel,
            TimeZone = timeZone
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
            return Ok(result.Data);

        return BadRequest(result.ErrorMessage);
    }

    private UserRole GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst(ClaimTypes.Role)?.Value;
        return Enum.TryParse<UserRole>(roleClaim, out var role) ? role : UserRole.Admin;
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }
}
