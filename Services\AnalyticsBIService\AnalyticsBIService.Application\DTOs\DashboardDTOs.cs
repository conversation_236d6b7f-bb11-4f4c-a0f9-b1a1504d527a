using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Custom visualization request data transfer object
/// </summary>
public class CustomVisualizationRequest
{
    [Required]
    public string VisualizationType { get; set; } = string.Empty; // "Chart", "Table", "KPI", "Map"

    [Required]
    public string ChartType { get; set; } = string.Empty; // "Line", "Bar", "Pie", "Scatter", "Heatmap"

    [Required]
    public List<string> DataSources { get; set; } = new();

    [Required]
    public List<string> Metrics { get; set; } = new();

    public List<string> Dimensions { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public string GroupBy { get; set; } = string.Empty;
    public string SortBy { get; set; } = string.Empty;
    public string SortOrder { get; set; } = "ASC"; // "ASC", "DESC"
    public int? Limit { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public Dictionary<string, object> ChartOptions { get; set; } = new();
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;

    // Properties required by DataVisualizationService
    public string Type { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public Dictionary<string, object> DataMapping { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> Interactivity { get; set; } = new();
    public Dictionary<string, object> Aggregations { get; set; } = new();
}

/// <summary>
/// Filter criteria data transfer object
/// </summary>
public class FilterCriteriaDto
{
    public string FieldName { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty; // "=", "!=", ">", "<", ">=", "<=", "IN", "NOT IN", "LIKE"
    public object Value { get; set; } = new();
    public List<object> Values { get; set; } = new(); // For IN/NOT IN operators
    public string LogicalOperator { get; set; } = "AND"; // "AND", "OR"
}

/// <summary>
/// Dashboard widget request data transfer object
/// </summary>
public class DashboardWidgetRequest
{
    [Required]
    public string WidgetType { get; set; } = string.Empty; // "Chart", "KPI", "Table", "Map", "Text"

    [Required]
    public string Title { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;
    public int Width { get; set; } = 4; // Grid width (1-12)
    public int Height { get; set; } = 4; // Grid height
    public int PositionX { get; set; } = 0;
    public int PositionY { get; set; } = 0;
    public CustomVisualizationRequest? VisualizationConfig { get; set; }
    public Dictionary<string, object> WidgetOptions { get; set; } = new();
    public List<string> AllowedRoles { get; set; } = new();
    public bool IsPublic { get; set; } = false;
    public string RefreshInterval { get; set; } = "5m"; // "1m", "5m", "15m", "30m", "1h"
}

/// <summary>
/// Widget interaction data transfer object
/// </summary>
public class WidgetInteractionDto
{
    public string WidgetId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string InteractionType { get; set; } = string.Empty; // "View", "Click", "Drill-down", "Export", "Refresh"
    public DateTime InteractionTime { get; set; }
    public Dictionary<string, object> InteractionData { get; set; } = new();
    public string SessionId { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
}

/// <summary>
/// Custom widget data transfer object
/// </summary>
public class CustomWidgetDto
{
    public string WidgetId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime LastModified { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = false;
    public int ViewCount { get; set; }
    public decimal AverageRating { get; set; }
    public List<string> Tags { get; set; } = new();
    public CustomVisualizationRequest Configuration { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Create widget request data transfer object
/// </summary>
public class CreateWidgetRequest
{
    [Required]
    public string Title { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    [Required]
    public string WidgetType { get; set; } = string.Empty;

    [Required]
    public CustomVisualizationRequest Configuration { get; set; } = new();

    public List<string> Tags { get; set; } = new();
    public bool IsPublic { get; set; } = false;
    public string DashboardId { get; set; } = string.Empty;
    public int? PositionX { get; set; }
    public int? PositionY { get; set; }
    public int Width { get; set; } = 4;
    public int Height { get; set; } = 4;
}

/// <summary>
/// Custom dashboard data transfer object
/// </summary>
public class CustomDashboardDto
{
    public string Id { get; set; } = string.Empty;
    public string DashboardId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime CreatedDate { get; set; }
    public DateTime LastModified { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsPublic { get; set; } = false;
    public bool IsDefault { get; set; } = false;
    public List<string> SharedWith { get; set; } = new();
    public List<CustomWidgetDto> Widgets { get; set; } = new();
    public Dictionary<string, object> Layout { get; set; } = new();
    public Dictionary<string, object> Filters { get; set; } = new();
    public int RefreshInterval { get; set; } = 300; // seconds
    public string Theme { get; set; } = "default";
    public List<string> Tags { get; set; } = new();
    public int ViewCount { get; set; }
    public DateTime LastViewed { get; set; }
}



/// <summary>
/// Dashboard filter data transfer object
/// </summary>
public class DashboardFilterDto
{
    public string FilterId { get; set; } = string.Empty;
    public string FilterName { get; set; } = string.Empty;
    public string FilterType { get; set; } = string.Empty; // "Date", "Select", "MultiSelect", "Text", "Number"
    public string DataSource { get; set; } = string.Empty;
    public string FieldName { get; set; } = string.Empty;
    public List<FilterOptionDto> Options { get; set; } = new();
    public object DefaultValue { get; set; } = new();
    public bool IsRequired { get; set; } = false;
    public bool IsGlobal { get; set; } = false;
    public Dictionary<string, object> FilterConfig { get; set; } = new();
}

/// <summary>
/// Filter option data transfer object
/// </summary>
public class FilterOptionDto
{
    public string Label { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public bool IsSelected { get; set; } = false;
    public string Group { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Dashboard theme data transfer object
/// </summary>
public class DashboardThemeDto
{
    public string ThemeId { get; set; } = string.Empty;
    public string ThemeName { get; set; } = string.Empty;
    public string PrimaryColor { get; set; } = string.Empty;
    public string SecondaryColor { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string TextColor { get; set; } = string.Empty;
    public string FontFamily { get; set; } = string.Empty;
    public Dictionary<string, string> ColorPalette { get; set; } = new();
    public Dictionary<string, object> ChartStyles { get; set; } = new();
    public bool IsDefault { get; set; } = false;
    public bool IsCustom { get; set; } = false;
}

/// <summary>
/// Dashboard export request data transfer object
/// </summary>
public class DashboardExportRequest
{
    [Required]
    public string DashboardId { get; set; } = string.Empty;

    [Required]
    public ExportFormat Format { get; set; }

    public List<string> WidgetIds { get; set; } = new(); // Empty means all widgets
    public bool IncludeData { get; set; } = true;
    public bool IncludeCharts { get; set; } = true;
    public string PageSize { get; set; } = "A4"; // "A4", "A3", "Letter", "Legal"
    public string Orientation { get; set; } = "Portrait"; // "Portrait", "Landscape"
    public Dictionary<string, object> ExportOptions { get; set; } = new();
    public DateTime? DataStartDate { get; set; }
    public DateTime? DataEndDate { get; set; }
}

/// <summary>
/// Dashboard performance metrics data transfer object
/// </summary>
public class DashboardPerformanceDto
{
    public string DashboardId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime MeasuredAt { get; set; } = DateTime.UtcNow;
    public decimal LoadTime { get; set; }
    public decimal DataFreshness { get; set; }
    public decimal QueryPerformance { get; set; }
    public decimal VisualizationPerformance { get; set; }
    public decimal ErrorRate { get; set; }
    public decimal AverageLoadTime { get; set; }
    public decimal AverageQueryTime { get; set; }
    public int TotalViews { get; set; }
    public int UniqueUsers { get; set; }
    public decimal BounceRate { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public List<WidgetPerformanceDto> WidgetPerformance { get; set; } = new();
    public List<UserEngagementDto> UserEngagement { get; set; } = new();
    public List<OptimizationSuggestionDto> OptimizationSuggestions { get; set; } = new();
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }
}

/// <summary>
/// Widget performance data transfer object
/// </summary>
public class WidgetPerformanceDto
{
    public string WidgetId { get; set; } = string.Empty;
    public string WidgetTitle { get; set; } = string.Empty;
    public decimal AverageLoadTime { get; set; }
    public int ViewCount { get; set; }
    public int InteractionCount { get; set; }
    public decimal ErrorRate { get; set; }
    public string MostCommonError { get; set; } = string.Empty;
    public decimal UserSatisfactionScore { get; set; }
}


