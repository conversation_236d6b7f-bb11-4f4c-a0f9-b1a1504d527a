﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Recurrence pattern for recurring schedules
/// </summary>
public class RecurrencePattern : ValueObject
{
    public RecurrenceType Type { get; private set; }
    public int Interval { get; private set; }
    public List<DayOfWeek> DaysOfWeek { get; private set; }
    public List<int> DaysOfMonth { get; private set; }
    public List<int> MonthsOfYear { get; private set; }
    public TimeSpan? TimeOfDay { get; private set; }
    public DateTime? EndDate { get; private set; }
    public int? MaxOccurrences { get; private set; }
    public Dictionary<string, object> CustomParameters { get; private set; }

    private RecurrencePattern()
    {
        DaysOfWeek = new List<DayOfWeek>();
        DaysOfMonth = new List<int>();
        MonthsOfYear = new List<int>();
        CustomParameters = new Dictionary<string, object>();
    }

    public RecurrencePattern(
        RecurrenceType type,
        int interval = 1,
        List<DayOfWeek>? daysOfWeek = null,
        List<int>? daysOfMonth = null,
        List<int>? monthsOfYear = null,
        TimeSpan? timeOfDay = null,
        DateTime? endDate = null,
        int? maxOccurrences = null,
        Dictionary<string, object>? customParameters = null)
    {
        if (interval <= 0)
            throw new ArgumentException("Interval must be positive", nameof(interval));

        Type = type;
        Interval = interval;
        DaysOfWeek = daysOfWeek ?? new List<DayOfWeek>();
        DaysOfMonth = daysOfMonth ?? new List<int>();
        MonthsOfYear = monthsOfYear ?? new List<int>();
        TimeOfDay = timeOfDay;
        EndDate = endDate;
        MaxOccurrences = maxOccurrences;
        CustomParameters = customParameters ?? new Dictionary<string, object>();

        ValidatePattern();
    }

    public static RecurrencePattern Daily(int interval = 1, TimeSpan? timeOfDay = null, DateTime? endDate = null)
    {
        return new RecurrencePattern(RecurrenceType.Daily, interval, timeOfDay: timeOfDay, endDate: endDate);
    }

    public static RecurrencePattern Weekly(List<DayOfWeek> daysOfWeek, int interval = 1, TimeSpan? timeOfDay = null, DateTime? endDate = null)
    {
        return new RecurrencePattern(RecurrenceType.Weekly, interval, daysOfWeek, timeOfDay: timeOfDay, endDate: endDate);
    }

    public static RecurrencePattern Monthly(List<int> daysOfMonth, int interval = 1, TimeSpan? timeOfDay = null, DateTime? endDate = null)
    {
        return new RecurrencePattern(RecurrenceType.Monthly, interval, daysOfMonth: daysOfMonth, timeOfDay: timeOfDay, endDate: endDate);
    }

    public static RecurrencePattern Yearly(List<int> monthsOfYear, List<int> daysOfMonth, int interval = 1, TimeSpan? timeOfDay = null, DateTime? endDate = null)
    {
        return new RecurrencePattern(RecurrenceType.Yearly, interval, daysOfMonth: daysOfMonth, monthsOfYear: monthsOfYear, timeOfDay: timeOfDay, endDate: endDate);
    }

    public DateTime? GetNextOccurrence(DateTime fromDate)
    {
        if (EndDate.HasValue && fromDate >= EndDate.Value)
            return null;

        return Type switch
        {
            RecurrenceType.Daily => GetNextDailyOccurrence(fromDate),
            RecurrenceType.Weekly => GetNextWeeklyOccurrence(fromDate),
            RecurrenceType.Monthly => GetNextMonthlyOccurrence(fromDate),
            RecurrenceType.Yearly => GetNextYearlyOccurrence(fromDate),
            RecurrenceType.Custom => GetNextCustomOccurrence(fromDate),
            _ => null
        };
    }

    private DateTime? GetNextDailyOccurrence(DateTime fromDate)
    {
        var nextDate = fromDate.AddDays(Interval);
        if (TimeOfDay.HasValue)
        {
            nextDate = nextDate.Date.Add(TimeOfDay.Value);
        }
        return nextDate;
    }

    private DateTime? GetNextWeeklyOccurrence(DateTime fromDate)
    {
        if (!DaysOfWeek.Any())
            return null;

        var currentDate = fromDate.Date;
        var daysToAdd = 1;

        while (daysToAdd <= 7 * Interval)
        {
            var candidateDate = currentDate.AddDays(daysToAdd);
            if (DaysOfWeek.Contains(candidateDate.DayOfWeek))
            {
                if (TimeOfDay.HasValue)
                {
                    candidateDate = candidateDate.Add(TimeOfDay.Value);
                }
                return candidateDate;
            }
            daysToAdd++;
        }

        return null;
    }

    private DateTime? GetNextMonthlyOccurrence(DateTime fromDate)
    {
        if (!DaysOfMonth.Any())
            return null;

        var currentDate = fromDate.Date;
        var monthsToAdd = 1;

        while (monthsToAdd <= 12)
        {
            var candidateMonth = currentDate.AddMonths(monthsToAdd);
            var daysInMonth = DateTime.DaysInMonth(candidateMonth.Year, candidateMonth.Month);

            foreach (var day in DaysOfMonth.Where(d => d <= daysInMonth).OrderBy(d => d))
            {
                var candidateDate = new DateTime(candidateMonth.Year, candidateMonth.Month, day);
                if (candidateDate > fromDate)
                {
                    if (TimeOfDay.HasValue)
                    {
                        candidateDate = candidateDate.Add(TimeOfDay.Value);
                    }
                    return candidateDate;
                }
            }
            monthsToAdd++;
        }

        return null;
    }

    private DateTime? GetNextYearlyOccurrence(DateTime fromDate)
    {
        if (!MonthsOfYear.Any() || !DaysOfMonth.Any())
            return null;

        var currentDate = fromDate.Date;
        var yearsToAdd = 1;

        while (yearsToAdd <= 5)
        {
            var candidateYear = currentDate.AddYears(yearsToAdd);

            foreach (var month in MonthsOfYear.OrderBy(m => m))
            {
                var daysInMonth = DateTime.DaysInMonth(candidateYear.Year, month);

                foreach (var day in DaysOfMonth.Where(d => d <= daysInMonth).OrderBy(d => d))
                {
                    var candidateDate = new DateTime(candidateYear.Year, month, day);
                    if (candidateDate > fromDate)
                    {
                        if (TimeOfDay.HasValue)
                        {
                            candidateDate = candidateDate.Add(TimeOfDay.Value);
                        }
                        return candidateDate;
                    }
                }
            }
            yearsToAdd++;
        }

        return null;
    }

    private DateTime? GetNextCustomOccurrence(DateTime fromDate)
    {
        // Custom recurrence logic would be implemented here
        // For now, return null
        return null;
    }

    private void ValidatePattern()
    {
        switch (Type)
        {
            case RecurrenceType.Weekly when !DaysOfWeek.Any():
                throw new ArgumentException("Weekly recurrence requires at least one day of week");
            case RecurrenceType.Monthly when !DaysOfMonth.Any():
                throw new ArgumentException("Monthly recurrence requires at least one day of month");
            case RecurrenceType.Yearly when !MonthsOfYear.Any() || !DaysOfMonth.Any():
                throw new ArgumentException("Yearly recurrence requires months and days of month");
        }

        if (DaysOfMonth.Any(d => d < 1 || d > 31))
            throw new ArgumentException("Days of month must be between 1 and 31");

        if (MonthsOfYear.Any(m => m < 1 || m > 12))
            throw new ArgumentException("Months of year must be between 1 and 12");
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Type;
        yield return Interval;
        yield return TimeOfDay ?? TimeSpan.Zero;
        yield return EndDate ?? DateTime.MinValue;
        yield return MaxOccurrences ?? 0;

        foreach (var day in DaysOfWeek.OrderBy(d => d))
            yield return day;

        foreach (var day in DaysOfMonth.OrderBy(d => d))
            yield return day;

        foreach (var month in MonthsOfYear.OrderBy(m => m))
            yield return month;

        foreach (var kvp in CustomParameters.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Delivery window for message scheduling
/// </summary>
public class DeliveryWindow : ValueObject
{
    public TimeSpan StartTime { get; private set; }
    public TimeSpan EndTime { get; private set; }
    public List<DayOfWeek> AllowedDays { get; private set; }
    public List<string> ExcludedDates { get; private set; }
    public List<string> ExcludedTimeZones { get; private set; }
    public bool RespectUserTimeZone { get; private set; }
    public bool SkipWeekends { get; private set; }
    public bool SkipHolidays { get; private set; }
    public Dictionary<string, object> CustomRules { get; private set; }

    private DeliveryWindow()
    {
        AllowedDays = new List<DayOfWeek>();
        ExcludedDates = new List<string>();
        ExcludedTimeZones = new List<string>();
        CustomRules = new Dictionary<string, object>();
    }

    public DeliveryWindow(
        TimeSpan startTime,
        TimeSpan endTime,
        List<DayOfWeek>? allowedDays = null,
        List<string>? excludedDates = null,
        List<string>? excludedTimeZones = null,
        bool respectUserTimeZone = true,
        bool skipWeekends = false,
        bool skipHolidays = false,
        Dictionary<string, object>? customRules = null)
    {
        if (startTime >= endTime)
            throw new ArgumentException("Start time must be before end time");

        StartTime = startTime;
        EndTime = endTime;
        AllowedDays = allowedDays ?? Enum.GetValues<DayOfWeek>().ToList();
        ExcludedDates = excludedDates ?? new List<string>();
        ExcludedTimeZones = excludedTimeZones ?? new List<string>();
        RespectUserTimeZone = respectUserTimeZone;
        SkipWeekends = skipWeekends;
        SkipHolidays = skipHolidays;
        CustomRules = customRules ?? new Dictionary<string, object>();
    }

    public static DeliveryWindow Default()
    {
        return new DeliveryWindow(TimeSpan.FromHours(8), TimeSpan.FromHours(20));
    }

    public static DeliveryWindow BusinessHours()
    {
        var businessDays = new List<DayOfWeek>
        {
            DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday,
            DayOfWeek.Thursday, DayOfWeek.Friday
        };
        return new DeliveryWindow(TimeSpan.FromHours(9), TimeSpan.FromHours(17), businessDays);
    }

    public static DeliveryWindow AllDay()
    {
        return new DeliveryWindow(TimeSpan.Zero, TimeSpan.FromHours(24));
    }

    public bool IsWithinWindow(DateTime dateTime, string timeZone)
    {
        var localTime = RespectUserTimeZone ? ConvertToTimeZone(dateTime, timeZone) : dateTime;

        // Check if day is allowed
        if (!AllowedDays.Contains(localTime.DayOfWeek))
            return false;

        // Check if it's a weekend and weekends are skipped
        if (SkipWeekends && (localTime.DayOfWeek == DayOfWeek.Saturday || localTime.DayOfWeek == DayOfWeek.Sunday))
            return false;

        // Check time window
        var timeOfDay = localTime.TimeOfDay;
        if (timeOfDay < StartTime || timeOfDay > EndTime)
            return false;

        // Check excluded dates
        var dateString = localTime.ToString("yyyy-MM-dd");
        if (ExcludedDates.Contains(dateString))
            return false;

        // Check excluded time zones
        if (ExcludedTimeZones.Contains(timeZone))
            return false;

        return true;
    }

    public DateTime GetNextValidTime(DateTime fromDateTime, string timeZone)
    {
        var currentTime = RespectUserTimeZone ? ConvertToTimeZone(fromDateTime, timeZone) : fromDateTime;
        var maxDaysToCheck = 30; // Prevent infinite loops
        var daysChecked = 0;

        while (daysChecked < maxDaysToCheck)
        {
            if (IsWithinWindow(currentTime, timeZone))
            {
                return RespectUserTimeZone ? ConvertFromTimeZone(currentTime, timeZone) : currentTime;
            }

            // Move to next valid time slot
            if (currentTime.TimeOfDay < StartTime)
            {
                currentTime = currentTime.Date.Add(StartTime);
            }
            else if (currentTime.TimeOfDay > EndTime)
            {
                currentTime = currentTime.Date.AddDays(1).Add(StartTime);
                daysChecked++;
            }
            else
            {
                currentTime = currentTime.AddMinutes(15); // Check every 15 minutes
            }
        }

        // Fallback to original time if no valid time found
        return fromDateTime;
    }

    private DateTime ConvertToTimeZone(DateTime utcDateTime, string timeZone)
    {
        try
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZoneInfo);
        }
        catch
        {
            return utcDateTime;
        }
    }

    private DateTime ConvertFromTimeZone(DateTime localDateTime, string timeZone)
    {
        try
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            return TimeZoneInfo.ConvertTimeToUtc(localDateTime, timeZoneInfo);
        }
        catch
        {
            return localDateTime;
        }
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return StartTime;
        yield return EndTime;
        yield return RespectUserTimeZone;
        yield return SkipWeekends;
        yield return SkipHolidays;

        foreach (var day in AllowedDays.OrderBy(d => d))
            yield return day;

        foreach (var date in ExcludedDates.OrderBy(d => d))
            yield return date;

        foreach (var tz in ExcludedTimeZones.OrderBy(t => t))
            yield return tz;

        foreach (var kvp in CustomRules.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Optimization settings for smart scheduling
/// </summary>
public class OptimizationSettings : ValueObject
{
    public bool EnableUserBehaviorOptimization { get; private set; }
    public bool EnableChannelOptimization { get; private set; }
    public bool EnableContentOptimization { get; private set; }
    public bool EnableTimingOptimization { get; private set; }
    public bool EnableFrequencyOptimization { get; private set; }
    public decimal OptimizationWeight { get; private set; }
    public TimeSpan LearningPeriod { get; private set; }
    public int MinimumDataPoints { get; private set; }
    public Dictionary<string, object> AlgorithmParameters { get; private set; }

    private OptimizationSettings()
    {
        AlgorithmParameters = new Dictionary<string, object>();
    }

    public OptimizationSettings(
        bool enableUserBehaviorOptimization = true,
        bool enableChannelOptimization = true,
        bool enableContentOptimization = false,
        bool enableTimingOptimization = true,
        bool enableFrequencyOptimization = true,
        decimal optimizationWeight = 0.7m,
        TimeSpan? learningPeriod = null,
        int minimumDataPoints = 100,
        Dictionary<string, object>? algorithmParameters = null)
    {
        EnableUserBehaviorOptimization = enableUserBehaviorOptimization;
        EnableChannelOptimization = enableChannelOptimization;
        EnableContentOptimization = enableContentOptimization;
        EnableTimingOptimization = enableTimingOptimization;
        EnableFrequencyOptimization = enableFrequencyOptimization;
        OptimizationWeight = optimizationWeight;
        LearningPeriod = learningPeriod ?? TimeSpan.FromDays(30);
        MinimumDataPoints = minimumDataPoints;
        AlgorithmParameters = algorithmParameters ?? new Dictionary<string, object>();
    }

    public static OptimizationSettings Default()
    {
        return new OptimizationSettings();
    }

    public static OptimizationSettings Disabled()
    {
        return new OptimizationSettings(false, false, false, false, false, 0);
    }

    public static OptimizationSettings Aggressive()
    {
        return new OptimizationSettings(true, true, true, true, true, 0.9m, TimeSpan.FromDays(7), 50);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return EnableUserBehaviorOptimization;
        yield return EnableChannelOptimization;
        yield return EnableContentOptimization;
        yield return EnableTimingOptimization;
        yield return EnableFrequencyOptimization;
        yield return OptimizationWeight;
        yield return LearningPeriod;
        yield return MinimumDataPoints;

        foreach (var kvp in AlgorithmParameters.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Schedule metrics for performance tracking
/// </summary>
public class ScheduleMetrics : ValueObject
{
    public int TotalExecutions { get; private set; }
    public int SuccessfulExecutions { get; private set; }
    public int FailedExecutions { get; private set; }
    public decimal SuccessRate { get; private set; }
    public decimal AverageDeliveryTime { get; private set; }
    public decimal AverageEngagementRate { get; private set; }
    public decimal OptimizationScore { get; private set; }
    public Dictionary<string, int> ChannelPerformance { get; private set; }
    public Dictionary<string, decimal> TimeSlotPerformance { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private ScheduleMetrics()
    {
        ChannelPerformance = new Dictionary<string, int>();
        TimeSlotPerformance = new Dictionary<string, decimal>();
    }

    public ScheduleMetrics(
        int totalExecutions,
        int successfulExecutions,
        int failedExecutions,
        decimal averageDeliveryTime,
        decimal averageEngagementRate,
        decimal optimizationScore,
        Dictionary<string, int>? channelPerformance = null,
        Dictionary<string, decimal>? timeSlotPerformance = null)
    {
        TotalExecutions = totalExecutions;
        SuccessfulExecutions = successfulExecutions;
        FailedExecutions = failedExecutions;
        SuccessRate = totalExecutions > 0 ? (decimal)successfulExecutions / totalExecutions * 100 : 0;
        AverageDeliveryTime = averageDeliveryTime;
        AverageEngagementRate = averageEngagementRate;
        OptimizationScore = optimizationScore;
        ChannelPerformance = channelPerformance ?? new Dictionary<string, int>();
        TimeSlotPerformance = timeSlotPerformance ?? new Dictionary<string, decimal>();
        LastUpdated = DateTime.UtcNow;
    }

    public static ScheduleMetrics Empty()
    {
        return new ScheduleMetrics(0, 0, 0, 0, 0, 0);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalExecutions;
        yield return SuccessfulExecutions;
        yield return FailedExecutions;
        yield return SuccessRate;
        yield return AverageDeliveryTime;
        yield return AverageEngagementRate;
        yield return OptimizationScore;
        yield return LastUpdated;

        foreach (var kvp in ChannelPerformance.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in TimeSlotPerformance.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Recurrence type enumeration
/// </summary>
public enum RecurrenceType
{
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Yearly = 4,
    Custom = 5
}

