using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.TransportCompany;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Transport company analytics controller for performance, RFQ, cost, and market intelligence analytics
/// </summary>
[ApiController]
[Route("api/transport-company/analytics")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyAnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyAnalyticsController> _logger;

    public TransportCompanyAnalyticsController(IMediator mediator, ILogger<TransportCompanyAnalyticsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get transport company dashboard with comprehensive analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Transport company dashboard data</returns>
    [HttpGet("{transportCompanyId}/dashboard")]
    public async Task<ActionResult<TransportCompanyDashboardDto>> GetDashboard(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetTransportCompanyDashboardQuery(transportCompanyId, fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transport company dashboard for {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get transport company performance metrics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for performance analysis</param>
    /// <param name="toDate">End date for performance analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeRFQAnalysis">Include RFQ analysis</param>
    /// <param name="includeBrokerPerformance">Include broker performance</param>
    /// <param name="includeDeliveryMetrics">Include delivery metrics</param>
    /// <returns>Transport company performance data</returns>
    [HttpGet("{transportCompanyId}/performance")]
    public async Task<ActionResult<TransportCompanyPerformanceDto>> GetPerformance(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeRFQAnalysis = true,
        [FromQuery] bool includeBrokerPerformance = true,
        [FromQuery] bool includeDeliveryMetrics = true)
    {
        try
        {
            var query = new GetTransportCompanyPerformanceQuery(transportCompanyId, fromDate, toDate, period, includeRFQAnalysis, includeBrokerPerformance, includeDeliveryMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get RFQ conversion analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for RFQ analysis</param>
    /// <param name="toDate">End date for RFQ analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="brokerId">Filter by specific broker</param>
    /// <param name="routeType">Filter by route type</param>
    /// <returns>RFQ conversion analytics data</returns>
    [HttpGet("{transportCompanyId}/rfq-conversion")]
    public async Task<ActionResult<RFQConversionAnalyticsDto>> GetRFQConversionAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? brokerId = null,
        [FromQuery] string? routeType = null)
    {
        try
        {
            var query = new GetRFQConversionAnalyticsQuery(transportCompanyId, fromDate, toDate, period, brokerId, routeType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting RFQ conversion analytics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker response time analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for response time analysis</param>
    /// <param name="toDate">End date for response time analysis</param>
    /// <param name="brokerIds">Filter by specific brokers</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Broker response time analytics data</returns>
    [HttpGet("{transportCompanyId}/broker-response-time")]
    public async Task<ActionResult<BrokerResponseTimeAnalyticsDto>> GetBrokerResponseTimeAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] List<Guid>? brokerIds = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetBrokerResponseTimeAnalyticsQuery(transportCompanyId, fromDate, toDate, brokerIds, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting broker response time analytics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get delivery performance analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for delivery analysis</param>
    /// <param name="toDate">End date for delivery analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="routeType">Filter by route type</param>
    /// <param name="carrierType">Filter by carrier type</param>
    /// <returns>Delivery performance analytics data</returns>
    [HttpGet("{transportCompanyId}/delivery-performance")]
    public async Task<ActionResult<DeliveryPerformanceAnalyticsDto>> GetDeliveryPerformanceAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? routeType = null,
        [FromQuery] string? carrierType = null)
    {
        try
        {
            var query = new GetDeliveryPerformanceAnalyticsQuery(transportCompanyId, fromDate, toDate, period, routeType, carrierType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery performance analytics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cost analysis
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for cost analysis</param>
    /// <param name="toDate">End date for cost analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includePricingOptimization">Include pricing optimization</param>
    /// <param name="includeProfitabilityAnalysis">Include profitability analysis</param>
    /// <returns>Cost analysis data</returns>
    [HttpGet("{transportCompanyId}/cost-analysis")]
    public async Task<ActionResult<TransportCompanyCostAnalysisDto>> GetCostAnalysis(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includePricingOptimization = true,
        [FromQuery] bool includeProfitabilityAnalysis = true)
    {
        try
        {
            var query = new GetTransportCompanyCostAnalysisQuery(transportCompanyId, fromDate, toDate, period, includePricingOptimization, includeProfitabilityAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cost analysis for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get market intelligence
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for market analysis</param>
    /// <param name="toDate">End date for market analysis</param>
    /// <param name="marketSegment">Filter by market segment</param>
    /// <param name="geographicRegion">Filter by geographic region</param>
    /// <param name="includeCompetitiveAnalysis">Include competitive analysis</param>
    /// <returns>Market intelligence data</returns>
    [HttpGet("{transportCompanyId}/market-intelligence")]
    public async Task<ActionResult<TransportCompanyMarketIntelligenceDto>> GetMarketIntelligence(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? marketSegment = null,
        [FromQuery] string? geographicRegion = null,
        [FromQuery] bool includeCompetitiveAnalysis = true)
    {
        try
        {
            var query = new GetTransportCompanyMarketIntelligenceQuery(transportCompanyId, fromDate, toDate, marketSegment, geographicRegion, includeCompetitiveAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting market intelligence for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker performance comparison
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for broker comparison</param>
    /// <param name="toDate">End date for broker comparison</param>
    /// <param name="brokerIds">Filter by specific brokers</param>
    /// <param name="comparisonMetric">Comparison metric to focus on</param>
    /// <returns>Broker performance comparison data</returns>
    [HttpGet("{transportCompanyId}/broker-comparison")]
    public async Task<ActionResult<BrokerPerformanceComparisonDto>> GetBrokerPerformanceComparison(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] List<Guid>? brokerIds = null,
        [FromQuery] string? comparisonMetric = null)
    {
        try
        {
            var query = new GetBrokerPerformanceComparisonQuery(transportCompanyId, fromDate, toDate, brokerIds, comparisonMetric);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting broker performance comparison for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get predictive insights (Pro/Enterprise feature)
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for prediction analysis</param>
    /// <param name="toDate">End date for prediction analysis</param>
    /// <param name="predictionTypes">Types of predictions to include</param>
    /// <param name="forecastDays">Number of days to forecast</param>
    /// <returns>Predictive insights data</returns>
    [HttpGet("{transportCompanyId}/predictive-insights")]
    [Authorize(Roles = "TransportCompany:Pro,TransportCompany:Enterprise,Admin")]
    public async Task<ActionResult<TransportCompanyPredictiveInsightsDto>> GetPredictiveInsights(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] List<string>? predictionTypes = null,
        [FromQuery] int forecastDays = 30)
    {
        try
        {
            var query = new GetTransportCompanyPredictiveInsightsQuery(transportCompanyId, fromDate, toDate, predictionTypes, forecastDays);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting predictive insights for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get customer-specific analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="customerId">Specific customer ID (optional)</param>
    /// <param name="fromDate">Start date for customer analysis</param>
    /// <param name="toDate">End date for customer analysis</param>
    /// <param name="includeShipmentHistory">Include shipment history</param>
    /// <param name="includePerformanceMetrics">Include performance metrics</param>
    /// <returns>Customer-specific analytics data</returns>
    [HttpGet("{transportCompanyId}/customer-analytics")]
    public async Task<ActionResult<CustomerSpecificAnalyticsDto>> GetCustomerSpecificAnalytics(
        Guid transportCompanyId,
        [FromQuery] Guid? customerId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool includeShipmentHistory = true,
        [FromQuery] bool includePerformanceMetrics = true)
    {
        try
        {
            var query = new GetCustomerSpecificAnalyticsQuery(transportCompanyId, customerId, fromDate, toDate, includeShipmentHistory, includePerformanceMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer-specific analytics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get route optimization analytics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for route analysis</param>
    /// <param name="toDate">End date for route analysis</param>
    /// <param name="originCity">Filter by origin city</param>
    /// <param name="destinationCity">Filter by destination city</param>
    /// <param name="routeType">Filter by route type</param>
    /// <returns>Route optimization analytics data</returns>
    [HttpGet("{transportCompanyId}/route-optimization")]
    public async Task<ActionResult<RouteOptimizationAnalyticsDto>> GetRouteOptimizationAnalytics(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? originCity = null,
        [FromQuery] string? destinationCity = null,
        [FromQuery] string? routeType = null)
    {
        try
        {
            var query = new GetRouteOptimizationAnalyticsQuery(transportCompanyId, fromDate, toDate, originCity, destinationCity, routeType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting route optimization analytics for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get pricing optimization recommendations
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for pricing analysis</param>
    /// <param name="toDate">End date for pricing analysis</param>
    /// <param name="serviceType">Filter by service type</param>
    /// <param name="routeType">Filter by route type</param>
    /// <param name="includeMarketComparison">Include market comparison</param>
    /// <returns>Pricing optimization data</returns>
    [HttpGet("{transportCompanyId}/pricing-optimization")]
    public async Task<ActionResult<PricingOptimizationDto>> GetPricingOptimization(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? serviceType = null,
        [FromQuery] string? routeType = null,
        [FromQuery] bool includeMarketComparison = true)
    {
        try
        {
            var query = new GetPricingOptimizationQuery(transportCompanyId, fromDate, toDate, serviceType, routeType, includeMarketComparison);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pricing optimization for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get operational efficiency metrics
    /// </summary>
    /// <param name="transportCompanyId">Transport company ID</param>
    /// <param name="fromDate">Start date for efficiency analysis</param>
    /// <param name="toDate">End date for efficiency analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeResourceUtilization">Include resource utilization</param>
    /// <param name="includeProcessEfficiency">Include process efficiency</param>
    /// <returns>Operational efficiency data</returns>
    [HttpGet("{transportCompanyId}/operational-efficiency")]
    public async Task<ActionResult<OperationalEfficiencyDto>> GetOperationalEfficiency(
        Guid transportCompanyId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeResourceUtilization = true,
        [FromQuery] bool includeProcessEfficiency = true)
    {
        try
        {
            var query = new GetOperationalEfficiencyQuery(transportCompanyId, fromDate, toDate, period, includeResourceUtilization, includeProcessEfficiency);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting operational efficiency for transport company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }
}
