using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;
using Shared.Domain.Common;

namespace OrderManagement.Application.Commands.MarkInvoicePaid;

public class MarkInvoicePaidCommandHandler : IRequestHandler<MarkInvoicePaidCommand, bool>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<MarkInvoicePaidCommandHandler> _logger;

    public MarkInvoicePaidCommandHandler(
        IInvoiceRepository invoiceRepository,
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<MarkInvoicePaidCommandHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(MarkInvoicePaidCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Marking invoice {InvoiceId} as paid by user {ProcessedBy} with reference {PaymentReference}",
            request.InvoiceId, request.ProcessedBy, request.PaymentReference);

        var invoice = await _invoiceRepository.GetByIdAsync(request.InvoiceId, cancellationToken);
        if (invoice == null)
        {
            _logger.LogWarning("Invoice {InvoiceId} not found", request.InvoiceId);
            throw new OrderManagementException("Invoice not found");
        }

        // Get the associated order for authorization and payment status update
        var order = await _orderRepository.GetByIdAsync(invoice.OrderId, cancellationToken);
        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found for invoice {InvoiceId}",
                invoice.OrderId, request.InvoiceId);
            throw new OrderManagementException("Associated order not found");
        }

        // Authorization check - only transport company, broker, or admin can mark invoices as paid
        if (order.TransportCompanyId != request.ProcessedBy &&
            order.BrokerId != request.ProcessedBy)
        {
            // Note: In a real system, you might want to add admin role checking here
            _logger.LogWarning("User {ProcessedBy} is not authorized to mark invoice {InvoiceId} as paid",
                request.ProcessedBy, request.InvoiceId);
            throw new OrderManagementException("Not authorized to mark this invoice as paid");
        }

        // Business rule validation
        if (invoice.Status == InvoiceStatus.Paid)
        {
            _logger.LogWarning("Invoice {InvoiceId} is already marked as paid", request.InvoiceId);
            throw new OrderManagementException("Invoice is already marked as paid");
        }

        if (invoice.Status == InvoiceStatus.Cancelled)
        {
            _logger.LogWarning("Cannot mark cancelled invoice {InvoiceId} as paid", request.InvoiceId);
            throw new OrderManagementException("Cannot mark cancelled invoice as paid");
        }

        if (invoice.Status == InvoiceStatus.Draft)
        {
            _logger.LogWarning("Cannot mark draft invoice {InvoiceId} as paid. Invoice must be sent first.", request.InvoiceId);
            throw new OrderManagementException("Cannot mark draft invoice as paid. Invoice must be sent first.");
        }

        try
        {
            var paymentReference = request.PaymentReference ?? $"PAY-{DateTime.UtcNow:yyyyMMddHHmmss}";
            var paidAt = request.PaidAt ?? DateTime.UtcNow;

            // Handle partial payments if specified
            if (request.PaidAmount.HasValue && request.PaidAmount.Value < invoice.TotalAmount.Amount)
            {
                // Note: The current Invoice entity doesn't support partial payments
                // This would need to be enhanced in the domain model to track partial payments
                _logger.LogInformation("Partial payment of {PaidAmount} received for invoice {InvoiceId} (Total: {TotalAmount})",
                    request.PaidAmount.Value, request.InvoiceId, invoice.TotalAmount.Amount);

                // For now, we'll treat any payment as full payment
                // In a real system, you'd want to track partial payments and update status accordingly
            }

            // Mark invoice as paid
            invoice.MarkAsPaid(paymentReference);

            // Update order payment status
            order.UpdatePaymentStatus(PaymentStatus.Paid);

            // Update repositories
            await _invoiceRepository.UpdateAsync(invoice, cancellationToken);
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("invoice.paid", new
            {
                InvoiceId = invoice.Id,
                InvoiceNumber = invoice.InvoiceNumber,
                OrderId = invoice.OrderId,
                OrderNumber = order.OrderNumber,
                Amount = new { Amount = invoice.TotalAmount.Amount, Currency = invoice.TotalAmount.Currency },
                PaidAmount = request.PaidAmount ?? invoice.TotalAmount.Amount,
                PaymentReference = paymentReference,
                PaidAt = paidAt,
                ProcessedBy = request.ProcessedBy,
                PaymentNotes = request.PaymentNotes
            }, cancellationToken);

            _logger.LogInformation("Successfully marked invoice {InvoiceId} as paid with reference {PaymentReference}",
                request.InvoiceId, paymentReference);

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to mark invoice {InvoiceId} as paid: {Message}",
                request.InvoiceId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking invoice {InvoiceId} as paid", request.InvoiceId);
            throw;
        }
    }
}

