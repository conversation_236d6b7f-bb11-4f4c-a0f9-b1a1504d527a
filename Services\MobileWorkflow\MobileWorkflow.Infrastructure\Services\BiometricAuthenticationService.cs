using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IBiometricAuthenticationService
{
    Task<BiometricProfileDto> CreateProfileAsync(CreateBiometricProfileRequest request, CancellationToken cancellationToken = default);
    Task<BiometricProfileDto?> GetProfileAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);
    Task<List<BiometricProfileDto>> GetUserProfilesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> EnableBiometricAsync(EnableBiometricRequest request, CancellationToken cancellationToken = default);
    Task<bool> DisableBiometricAsync(Guid userId, string deviceId, string biometricType, CancellationToken cancellationToken = default);
    Task<BiometricAuthenticationResult> AuthenticateAsync(BiometricAuthenticationRequest request, CancellationToken cancellationToken = default);
    Task<BiometricPolicyDto> CreatePolicyAsync(CreateBiometricPolicyRequest request, CancellationToken cancellationToken = default);
    Task<List<BiometricPolicyDto>> GetPoliciesAsync(string? platform = null, CancellationToken cancellationToken = default);
    Task<bool> UpdatePolicyAsync(Guid policyId, UpdateBiometricPolicyRequest request, CancellationToken cancellationToken = default);
    Task<BiometricCapabilitiesDto> GetDeviceCapabilitiesAsync(string platform, string deviceModel, CancellationToken cancellationToken = default);
    Task<List<BiometricAuthenticationAttemptDto>> GetAuthenticationHistoryAsync(Guid userId, string? deviceId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<BiometricSecurityReportDto> GetSecurityReportAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<bool> ValidateSecurityRequirementsAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);
    Task ProcessFailedAttemptsAsync(CancellationToken cancellationToken = default);
}

public class BiometricAuthenticationService : IBiometricAuthenticationService
{
    private readonly IBiometricProfileRepository _profileRepository;
    private readonly IBiometricPolicyRepository _policyRepository;
    private readonly IBiometricAuthenticationAttemptRepository _attemptRepository;
    private readonly IBiometricProviderService _providerService;
    private readonly ILogger<BiometricAuthenticationService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public BiometricAuthenticationService(
        IBiometricProfileRepository profileRepository,
        IBiometricPolicyRepository policyRepository,
        IBiometricAuthenticationAttemptRepository attemptRepository,
        IBiometricProviderService providerService,
        ILogger<BiometricAuthenticationService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _profileRepository = profileRepository;
        _policyRepository = policyRepository;
        _attemptRepository = attemptRepository;
        _providerService = providerService;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<BiometricProfileDto> CreateProfileAsync(CreateBiometricProfileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating biometric profile for user {UserId} on device {DeviceId}", request.UserId, request.DeviceId);

            // Check if profile already exists
            var existingProfile = await _profileRepository.GetByUserAndDeviceAsync(request.UserId, request.DeviceId, cancellationToken);
            if (existingProfile != null)
            {
                _logger.LogInformation("Biometric profile already exists for user {UserId} on device {DeviceId}", request.UserId, request.DeviceId);
                return MapToBiometricProfileDto(existingProfile);
            }

            // Get device capabilities
            var capabilities = await _providerService.GetDeviceCapabilitiesAsync(request.Platform, request.DeviceModel, cancellationToken);

            var profile = new BiometricProfile(
                request.UserId,
                request.DeviceId,
                request.Platform,
                capabilities.SupportedBiometrics);

            if (request.SecuritySettings != null)
            {
                profile.UpdateSecuritySettings(request.SecuritySettings);
            }

            _profileRepository.Add(profile);
            await _profileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Biometric profile {ProfileId} created successfully", profile.Id);

            return MapToBiometricProfileDto(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating biometric profile for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<BiometricProfileDto?> GetProfileAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var profile = await _profileRepository.GetByUserAndDeviceAsync(userId, deviceId, cancellationToken);
            if (profile == null)
            {
                return null;
            }

            return MapToBiometricProfileDto(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting biometric profile for user {UserId} on device {DeviceId}", userId, deviceId);
            return null;
        }
    }

    public async Task<List<BiometricProfileDto>> GetUserProfilesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var profiles = await _profileRepository.GetByUserIdAsync(userId, cancellationToken);
            return profiles.Select(MapToBiometricProfileDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting biometric profiles for user {UserId}", userId);
            return new List<BiometricProfileDto>();
        }
    }

    public async Task<bool> EnableBiometricAsync(EnableBiometricRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Enabling biometric {BiometricType} for user {UserId} on device {DeviceId}",
                request.BiometricType, request.UserId, request.DeviceId);

            var profile = await _profileRepository.GetByUserAndDeviceAsync(request.UserId, request.DeviceId, cancellationToken);
            if (profile == null)
            {
                _logger.LogWarning("Biometric profile not found for user {UserId} on device {DeviceId}", request.UserId, request.DeviceId);
                return false;
            }

            // Validate security requirements
            var isValid = await ValidateSecurityRequirementsAsync(request.UserId, request.DeviceId, cancellationToken);
            if (!isValid)
            {
                _logger.LogWarning("Security requirements not met for enabling biometric {BiometricType}", request.BiometricType);
                return false;
            }

            // Enroll biometric with provider
            var enrollmentResult = await _providerService.EnrollBiometricAsync(
                request.DeviceId,
                request.BiometricType,
                request.BiometricData,
                cancellationToken);

            if (!enrollmentResult.IsSuccess)
            {
                _logger.LogWarning("Biometric enrollment failed: {ErrorMessage}", enrollmentResult.ErrorMessage);
                return false;
            }

            profile.EnableBiometric(request.BiometricType, enrollmentResult.EnrollmentData);
            await _profileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Biometric {BiometricType} enabled successfully for user {UserId}", request.BiometricType, request.UserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling biometric {BiometricType} for user {UserId}", request.BiometricType, request.UserId);
            return false;
        }
    }

    public async Task<bool> DisableBiometricAsync(Guid userId, string deviceId, string biometricType, CancellationToken cancellationToken = default)
    {
        try
        {
            var profile = await _profileRepository.GetByUserAndDeviceAsync(userId, deviceId, cancellationToken);
            if (profile == null)
            {
                return false;
            }

            profile.DisableBiometric(biometricType);
            await _profileRepository.SaveChangesAsync(cancellationToken);

            // Remove biometric from provider
            await _providerService.RemoveBiometricAsync(deviceId, biometricType, cancellationToken);

            _logger.LogInformation("Biometric {BiometricType} disabled for user {UserId} on device {DeviceId}",
                biometricType, userId, deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling biometric {BiometricType} for user {UserId}", biometricType, userId);
            return false;
        }
    }

    public async Task<BiometricAuthenticationResult> AuthenticateAsync(BiometricAuthenticationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Authenticating user {UserId} with biometric {BiometricType} on device {DeviceId}",
                request.UserId, request.BiometricType, request.DeviceId);

            var profile = await _profileRepository.GetByUserAndDeviceAsync(request.UserId, request.DeviceId, cancellationToken);
            if (profile == null || !profile.IsActive)
            {
                return new BiometricAuthenticationResult
                {
                    IsSuccess = false,
                    ErrorCode = "PROFILE_NOT_FOUND",
                    ErrorMessage = "Biometric profile not found or inactive"
                };
            }

            if (!profile.IsBiometricEnabled(request.BiometricType))
            {
                return new BiometricAuthenticationResult
                {
                    IsSuccess = false,
                    ErrorCode = "BIOMETRIC_NOT_ENABLED",
                    ErrorMessage = $"Biometric {request.BiometricType} is not enabled"
                };
            }

            // Check for lockout
            var isLockedOut = await IsUserLockedOutAsync(request.UserId, request.DeviceId, cancellationToken);
            if (isLockedOut)
            {
                return new BiometricAuthenticationResult
                {
                    IsSuccess = false,
                    ErrorCode = "USER_LOCKED_OUT",
                    ErrorMessage = "User is locked out due to too many failed attempts"
                };
            }

            // Perform biometric authentication
            var authResult = await _providerService.AuthenticateAsync(
                request.DeviceId,
                request.BiometricType,
                request.BiometricData,
                request.Challenge,
                cancellationToken);

            // Record authentication attempt
            var attempt = new BiometricAuthenticationAttempt(
                profile.Id,
                request.UserId,
                request.DeviceId,
                request.BiometricType,
                authResult.IsSuccess ? "Success" : "Failed",
                new Dictionary<string, object>
                {
                    ["challenge"] = request.Challenge ?? "",
                    ["user_agent"] = request.UserAgent ?? "",
                    ["ip_address"] = request.IpAddress ?? "",
                    ["timestamp"] = DateTime.UtcNow
                });

            if (!authResult.IsSuccess)
            {
                attempt.SetError(authResult.ErrorCode, authResult.ErrorMessage);
            }

            _attemptRepository.Add(attempt);
            await _attemptRepository.SaveChangesAsync(cancellationToken);

            if (authResult.IsSuccess)
            {
                profile.UpdateLastUsed();
                await _profileRepository.SaveChangesAsync(cancellationToken);
            }

            return new BiometricAuthenticationResult
            {
                IsSuccess = authResult.IsSuccess,
                ErrorCode = authResult.ErrorCode,
                ErrorMessage = authResult.ErrorMessage,
                AuthenticationToken = authResult.AuthenticationToken,
                ExpiresAt = authResult.ExpiresAt,
                RequiresFallback = !authResult.IsSuccess && ShouldUseFallback(profile, request.BiometricType)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {UserId} with biometric {BiometricType}",
                request.UserId, request.BiometricType);

            return new BiometricAuthenticationResult
            {
                IsSuccess = false,
                ErrorCode = "AUTHENTICATION_ERROR",
                ErrorMessage = "An error occurred during authentication"
            };
        }
    }

    public async Task<BiometricPolicyDto> CreatePolicyAsync(CreateBiometricPolicyRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating biometric policy {Name} for platform {Platform}", request.Name, request.Platform);

            var policy = new BiometricPolicy(
                request.Name,
                request.Description,
                request.Platform,
                request.PolicyRules,
                request.CreatedBy);

            if (request.SecurityRequirements != null)
            {
                policy.UpdateSecurityRequirements(request.SecurityRequirements, request.CreatedBy);
            }

            if (request.FallbackOptions != null)
            {
                policy.UpdateFallbackOptions(request.FallbackOptions, request.CreatedBy);
            }

            _policyRepository.Add(policy);
            await _policyRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Biometric policy {PolicyId} created successfully", policy.Id);

            return MapToBiometricPolicyDto(policy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating biometric policy {Name}", request.Name);
            throw;
        }
    }

    public async Task<List<BiometricPolicyDto>> GetPoliciesAsync(string? platform = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var policies = await _policyRepository.GetPoliciesAsync(platform, cancellationToken);
            return policies.Select(MapToBiometricPolicyDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting biometric policies");
            return new List<BiometricPolicyDto>();
        }
    }

    public async Task<bool> UpdatePolicyAsync(Guid policyId, UpdateBiometricPolicyRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var policy = await _policyRepository.GetByIdAsync(policyId, cancellationToken);
            if (policy == null)
            {
                return false;
            }

            if (request.PolicyRules != null)
            {
                policy.UpdatePolicyRules(request.PolicyRules, request.UpdatedBy);
            }

            if (request.SecurityRequirements != null)
            {
                policy.UpdateSecurityRequirements(request.SecurityRequirements, request.UpdatedBy);
            }

            if (request.FallbackOptions != null)
            {
                policy.UpdateFallbackOptions(request.FallbackOptions, request.UpdatedBy);
            }

            await _policyRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Biometric policy {PolicyId} updated successfully", policyId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating biometric policy {PolicyId}", policyId);
            return false;
        }
    }

    public async Task<BiometricCapabilitiesDto> GetDeviceCapabilitiesAsync(string platform, string deviceModel, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"biometric_capabilities_{platform}_{deviceModel}";
            if (_cache.TryGetValue(cacheKey, out BiometricCapabilitiesDto? cachedCapabilities))
            {
                return cachedCapabilities!;
            }

            var capabilities = await _providerService.GetDeviceCapabilitiesAsync(platform, deviceModel, cancellationToken);

            var capabilitiesDto = new BiometricCapabilitiesDto
            {
                Platform = platform,
                DeviceModel = deviceModel,
                SupportedBiometrics = capabilities.SupportedBiometrics,
                HasHardwareSupport = capabilities.HasHardwareSupport,
                HasSecureEnclave = capabilities.HasSecureEnclave,
                SupportedSecurityLevels = capabilities.SupportedSecurityLevels,
                MaxEnrollments = capabilities.MaxEnrollments,
                RequiresUserPresence = capabilities.RequiresUserPresence,
                SupportsLivenessDetection = capabilities.SupportsLivenessDetection
            };

            _cache.Set(cacheKey, capabilitiesDto, TimeSpan.FromHours(24));

            return capabilitiesDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device capabilities for {Platform} {DeviceModel}", platform, deviceModel);
            return new BiometricCapabilitiesDto { Platform = platform, DeviceModel = deviceModel };
        }
    }

    public async Task<List<BiometricAuthenticationAttemptDto>> GetAuthenticationHistoryAsync(Guid userId, string? deviceId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var attempts = await _attemptRepository.GetByUserIdAsync(userId, deviceId, from, cancellationToken);

            return attempts.Select(MapToBiometricAuthenticationAttemptDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting authentication history for user {UserId}", userId);
            return new List<BiometricAuthenticationAttemptDto>();
        }
    }

    public async Task<BiometricSecurityReportDto> GetSecurityReportAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var attempts = await _attemptRepository.GetAttemptsAsync(userId, from, cancellationToken);
            var profiles = userId.HasValue
                ? await _profileRepository.GetByUserIdAsync(userId.Value, cancellationToken)
                : await _profileRepository.GetAllActiveAsync(cancellationToken);

            var report = new BiometricSecurityReportDto
            {
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalAttempts = attempts.Count,
                SuccessfulAttempts = attempts.Count(a => a.IsSuccessful()),
                FailedAttempts = attempts.Count(a => a.IsFailed()),
                CancelledAttempts = attempts.Count(a => a.WasCancelled()),
                SuccessRate = attempts.Count > 0 ? (double)attempts.Count(a => a.IsSuccessful()) / attempts.Count * 100 : 0,
                ActiveProfiles = profiles.Count(p => p.IsActive),
                TotalProfiles = profiles.Count,
                BiometricBreakdown = attempts.GroupBy(a => a.BiometricType).ToDictionary(g => g.Key, g => g.Count()),
                PlatformBreakdown = attempts.GroupBy(a => a.DeviceId).ToDictionary(g => g.Key, g => g.Count()),
                HourlyAttempts = attempts.GroupBy(a => a.AttemptedAt.Hour).ToDictionary(g => g.Key, g => g.Count()),
                SecurityAlerts = await GenerateSecurityAlertsAsync(attempts, cancellationToken)
            };

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating security report");
            return new BiometricSecurityReportDto { FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30), ToDate = DateTime.UtcNow };
        }
    }

    public async Task<bool> ValidateSecurityRequirementsAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var profile = await _profileRepository.GetByUserAndDeviceAsync(userId, deviceId, cancellationToken);
            if (profile == null)
            {
                return false;
            }

            var policies = await _policyRepository.GetActivePoliciesAsync(profile.Platform, cancellationToken);
            var deviceCapabilities = await _providerService.GetDeviceCapabilitiesAsync(profile.Platform, "Unknown", cancellationToken);

            foreach (var policy in policies)
            {
                // Check hardware security requirement
                var requiresHardwareSecurity = policy.GetSecurityRequirement<bool>("require_hardware_security", false);
                if (requiresHardwareSecurity && !deviceCapabilities.HasHardwareSupport)
                {
                    return false;
                }

                // Check secure enclave requirement
                var requiresSecureEnclave = policy.GetSecurityRequirement<bool>("require_secure_enclave", false);
                if (requiresSecureEnclave && !deviceCapabilities.HasSecureEnclave)
                {
                    return false;
                }

                // Check device lock requirement
                var requiresDeviceLock = policy.GetSecurityRequirement<bool>("require_screen_lock", false);
                if (requiresDeviceLock)
                {
                    var hasDeviceLock = await _providerService.CheckDeviceLockAsync(deviceId, cancellationToken);
                    if (!hasDeviceLock)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating security requirements for user {UserId} on device {DeviceId}", userId, deviceId);
            return false;
        }
    }

    public async Task ProcessFailedAttemptsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var lockoutThreshold = _configuration.GetValue<int>("BiometricAuth:LockoutThreshold", 5);
            var lockoutDuration = _configuration.GetValue<int>("BiometricAuth:LockoutDurationMinutes", 30);
            var timeWindow = TimeSpan.FromMinutes(15);

            var recentFailedAttempts = await _attemptRepository.GetRecentFailedAttemptsAsync(timeWindow, cancellationToken);

            var userDeviceGroups = recentFailedAttempts
                .GroupBy(a => new { a.UserId, a.DeviceId })
                .Where(g => g.Count() >= lockoutThreshold);

            foreach (var group in userDeviceGroups)
            {
                var profile = await _profileRepository.GetByUserAndDeviceAsync(group.Key.UserId, group.Key.DeviceId, cancellationToken);
                if (profile != null && profile.IsActive)
                {
                    profile.AddMetadata("locked_out_at", DateTime.UtcNow);
                    profile.AddMetadata("lockout_duration_minutes", lockoutDuration);
                    profile.AddMetadata("failed_attempts_count", group.Count());

                    _logger.LogWarning("User {UserId} locked out on device {DeviceId} due to {FailedAttempts} failed attempts",
                        group.Key.UserId, group.Key.DeviceId, group.Count());
                }
            }

            if (userDeviceGroups.Any())
            {
                await _profileRepository.SaveChangesAsync(cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing failed attempts");
        }
    }

    // Helper methods
    private async Task<bool> IsUserLockedOutAsync(Guid userId, string deviceId, CancellationToken cancellationToken)
    {
        try
        {
            var profile = await _profileRepository.GetByUserAndDeviceAsync(userId, deviceId, cancellationToken);
            if (profile == null)
            {
                return false;
            }

            var lockedOutAt = profile.GetMetadata<DateTime?>("locked_out_at");
            if (!lockedOutAt.HasValue)
            {
                return false;
            }

            var lockoutDuration = profile.GetMetadata<int>("lockout_duration_minutes", 30);
            var lockoutExpiry = lockedOutAt.Value.AddMinutes(lockoutDuration);

            if (DateTime.UtcNow >= lockoutExpiry)
            {
                // Lockout expired, remove metadata
                profile.Metadata.Remove("locked_out_at");
                profile.Metadata.Remove("lockout_duration_minutes");
                profile.Metadata.Remove("failed_attempts_count");
                await _profileRepository.SaveChangesAsync(cancellationToken);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking lockout status for user {UserId}", userId);
            return false;
        }
    }

    private bool ShouldUseFallback(BiometricProfile profile, string biometricType)
    {
        var allowFallback = profile.GetSecuritySetting<bool>("allow_fallback_to_passcode", true);
        return allowFallback;
    }

    private async Task<List<string>> GenerateSecurityAlertsAsync(List<BiometricAuthenticationAttempt> attempts, CancellationToken cancellationToken)
    {
        var alerts = new List<string>();

        // Check for unusual activity patterns
        var failureRate = attempts.Count > 0 ? (double)attempts.Count(a => a.IsFailed()) / attempts.Count : 0;
        if (failureRate > 0.3) // More than 30% failure rate
        {
            alerts.Add($"High failure rate detected: {failureRate:P1}");
        }

        // Check for multiple device attempts
        var deviceGroups = attempts.GroupBy(a => a.DeviceId).Where(g => g.Count() > 10);
        foreach (var group in deviceGroups)
        {
            alerts.Add($"High activity on device {group.Key}: {group.Count()} attempts");
        }

        // Check for off-hours activity
        var offHoursAttempts = attempts.Where(a => a.AttemptedAt.Hour < 6 || a.AttemptedAt.Hour > 22).Count();
        if (offHoursAttempts > attempts.Count * 0.2) // More than 20% off-hours
        {
            alerts.Add($"Unusual off-hours activity: {offHoursAttempts} attempts");
        }

        return alerts;
    }

    // Mapping methods
    private BiometricProfileDto MapToBiometricProfileDto(BiometricProfile profile)
    {
        return new BiometricProfileDto
        {
            Id = profile.Id,
            UserId = profile.UserId,
            DeviceId = profile.DeviceId,
            Platform = profile.Platform,
            SupportedBiometrics = profile.SupportedBiometrics,
            EnabledBiometrics = profile.EnabledBiometrics,
            IsActive = profile.IsActive,
            EnrolledAt = profile.EnrolledAt,
            LastUsedAt = profile.LastUsedAt,
            SecuritySettings = profile.SecuritySettings,
            HasAnyBiometricEnabled = profile.HasAnyBiometricEnabled()
        };
    }

    private BiometricPolicyDto MapToBiometricPolicyDto(BiometricPolicy policy)
    {
        return new BiometricPolicyDto
        {
            Id = policy.Id,
            Name = policy.Name,
            Description = policy.Description,
            Platform = policy.Platform,
            IsActive = policy.IsActive,
            IsDefault = policy.IsDefault,
            PolicyRules = policy.PolicyRules,
            SecurityRequirements = policy.SecurityRequirements,
            FallbackOptions = policy.FallbackOptions,
            AllowedBiometrics = policy.AllowedBiometrics,
            RequiredBiometrics = policy.RequiredBiometrics,
            CreatedAt = policy.CreatedAt,
            UpdatedAt = policy.UpdatedAt,
            CreatedBy = policy.CreatedBy,
            UpdatedBy = policy.UpdatedBy
        };
    }

    private BiometricAuthenticationAttemptDto MapToBiometricAuthenticationAttemptDto(BiometricAuthenticationAttempt attempt)
    {
        return new BiometricAuthenticationAttemptDto
        {
            Id = attempt.Id,
            UserId = attempt.UserId,
            DeviceId = attempt.DeviceId,
            BiometricType = attempt.BiometricType,
            Result = attempt.Result,
            AttemptedAt = attempt.AttemptedAt,
            ErrorCode = attempt.ErrorCode,
            ErrorMessage = attempt.ErrorMessage,
            AttemptContext = attempt.AttemptContext,
            SecurityMetrics = attempt.SecurityMetrics,
            IsSuccessful = attempt.IsSuccessful(),
            IsFailed = attempt.IsFailed(),
            WasCancelled = attempt.WasCancelled()
        };
    }
}

// Supporting interfaces and classes
public interface IBiometricProviderService
{
    Task<BiometricCapabilities> GetDeviceCapabilitiesAsync(string platform, string deviceModel, CancellationToken cancellationToken = default);
    Task<BiometricEnrollmentResult> EnrollBiometricAsync(string deviceId, string biometricType, Dictionary<string, object> biometricData, CancellationToken cancellationToken = default);
    Task<BiometricAuthResult> AuthenticateAsync(string deviceId, string biometricType, Dictionary<string, object> biometricData, string? challenge, CancellationToken cancellationToken = default);
    Task<bool> RemoveBiometricAsync(string deviceId, string biometricType, CancellationToken cancellationToken = default);
    Task<bool> CheckDeviceLockAsync(string deviceId, CancellationToken cancellationToken = default);
}

public class BiometricCapabilities
{
    public List<string> SupportedBiometrics { get; set; } = new();
    public bool HasHardwareSupport { get; set; }
    public bool HasSecureEnclave { get; set; }
    public List<string> SupportedSecurityLevels { get; set; } = new();
    public int MaxEnrollments { get; set; }
    public bool RequiresUserPresence { get; set; }
    public bool SupportsLivenessDetection { get; set; }
}

public class BiometricEnrollmentResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, object> EnrollmentData { get; set; } = new();
}

public class BiometricAuthResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public string? AuthenticationToken { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class BiometricAuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
    public string? AuthenticationToken { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool RequiresFallback { get; set; }
}
