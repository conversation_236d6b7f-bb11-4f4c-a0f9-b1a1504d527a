using ContentManagement.Application.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Infrastructure.Services;

/// <summary>
/// Background service for processing scheduled content publishing and unpublishing
/// </summary>
public class ScheduledPublishingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ScheduledPublishingService> _logger;
    private readonly TimeSpan _interval = TimeSpan.FromMinutes(5); // Check every 5 minutes

    public ScheduledPublishingService(
        IServiceProvider serviceProvider,
        ILogger<ScheduledPublishingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Scheduled Publishing Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessScheduledContentAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing scheduled content");
            }

            await Task.Delay(_interval, stoppingToken);
        }

        _logger.LogInformation("Scheduled Publishing Service stopped");
    }

    private async Task ProcessScheduledContentAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var publishingService = scope.ServiceProvider.GetRequiredService<IContentPublishingService>();

        try
        {
            // Process scheduled publishing
            await publishingService.ProcessScheduledPublishingAsync(cancellationToken);
            
            // Process scheduled unpublishing
            await publishingService.ProcessScheduledUnpublishingAsync(cancellationToken);

            _logger.LogDebug("Scheduled content processing completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process scheduled content");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Scheduled Publishing Service is stopping");
        await base.StopAsync(cancellationToken);
    }
}
