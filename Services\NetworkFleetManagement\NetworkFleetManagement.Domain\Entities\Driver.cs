using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class Driver : BaseEntity
{
    public Guid CarrierId { get; private set; }
    public Guid UserId { get; private set; }
    public string FirstName { get; private set; } = string.Empty;
    public string LastName { get; private set; } = string.Empty;
    public string PhoneNumber { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string LicenseNumber { get; private set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; private set; }
    public string? AadharNumber { get; private set; }
    public string? PANNumber { get; private set; }
    public DriverStatus Status { get; private set; }
    public OnboardingStatus OnboardingStatus { get; private set; }
    public Location? CurrentLocation { get; private set; }
    public PerformanceMetrics PerformanceMetrics { get; private set; }
    public string? ProfilePhotoUrl { get; private set; }
    public DateTime? LastActiveAt { get; private set; }
    public string? Notes { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime? VerifiedAt { get; private set; }

    // Route preferences and coverage areas
    private readonly List<string> _preferredRoutes = new();
    public IReadOnlyCollection<string> PreferredRoutes => _preferredRoutes.AsReadOnly();
    
    private readonly List<string> _operationalAreas = new();
    public IReadOnlyCollection<string> OperationalAreas => _operationalAreas.AsReadOnly();

    // Navigation properties
    public Carrier Carrier { get; private set; } = null!;
    
    private readonly List<DriverDocument> _documents = new();
    public IReadOnlyCollection<DriverDocument> Documents => _documents.AsReadOnly();

    private readonly List<DriverVehicleAssignment> _vehicleAssignments = new();
    public IReadOnlyCollection<DriverVehicleAssignment> VehicleAssignments => _vehicleAssignments.AsReadOnly();

    // Parameterless constructor for EF Core
    private Driver() 
    {
        PerformanceMetrics = new PerformanceMetrics();
    }

    public Driver(
        Guid carrierId,
        Guid userId,
        string firstName,
        string lastName,
        string phoneNumber,
        string email,
        string licenseNumber,
        DateTime licenseExpiryDate,
        string? aadharNumber = null,
        string? panNumber = null,
        string? profilePhotoUrl = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));
        
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));
        
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));
        
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));
        
        if (string.IsNullOrWhiteSpace(licenseNumber))
            throw new ArgumentException("License number cannot be empty", nameof(licenseNumber));
        
        if (licenseExpiryDate <= DateTime.UtcNow)
            throw new ArgumentException("License expiry date must be in the future", nameof(licenseExpiryDate));

        CarrierId = carrierId;
        UserId = userId;
        FirstName = firstName;
        LastName = lastName;
        PhoneNumber = phoneNumber;
        Email = email;
        LicenseNumber = licenseNumber;
        LicenseExpiryDate = licenseExpiryDate;
        AadharNumber = aadharNumber;
        PANNumber = panNumber;
        Status = DriverStatus.Available;
        OnboardingStatus = OnboardingStatus.NotStarted;
        PerformanceMetrics = new PerformanceMetrics();
        ProfilePhotoUrl = profilePhotoUrl;
        Notes = notes;
        IsVerified = false;
    }

    public string FullName => $"{FirstName} {LastName}";
    public bool IsLicenseValid => LicenseExpiryDate > DateTime.UtcNow;
    public bool IsAvailable => Status == DriverStatus.Available && IsLicenseValid && IsVerified;

    public void UpdateStatus(DriverStatus newStatus)
    {
        var oldStatus = Status;
        Status = newStatus;
        
        if (newStatus == DriverStatus.Available || newStatus == DriverStatus.OnTrip)
        {
            LastActiveAt = DateTime.UtcNow;
        }
        
        SetUpdatedAt();
        
        AddDomainEvent(new DriverStatusChangedEvent(Id, CarrierId, oldStatus, newStatus));
    }

    public void UpdateOnboardingStatus(OnboardingStatus newStatus)
    {
        OnboardingStatus = newStatus;
        
        if (newStatus == OnboardingStatus.Approved)
        {
            IsVerified = true;
            VerifiedAt = DateTime.UtcNow;
        }
        
        SetUpdatedAt();
    }

    public void UpdateLocation(Location location)
    {
        CurrentLocation = location;
        LastActiveAt = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void UpdatePerformanceMetrics(PerformanceMetrics newMetrics)
    {
        PerformanceMetrics = newMetrics;
        SetUpdatedAt();
    }

    public void AddPreferredRoute(string route)
    {
        if (string.IsNullOrWhiteSpace(route))
            throw new ArgumentException("Route cannot be empty", nameof(route));
        
        if (!_preferredRoutes.Contains(route))
        {
            _preferredRoutes.Add(route);
            SetUpdatedAt();
        }
    }

    public void RemovePreferredRoute(string route)
    {
        if (_preferredRoutes.Remove(route))
        {
            SetUpdatedAt();
        }
    }

    public void AddOperationalArea(string area)
    {
        if (string.IsNullOrWhiteSpace(area))
            throw new ArgumentException("Area cannot be empty", nameof(area));
        
        if (!_operationalAreas.Contains(area))
        {
            _operationalAreas.Add(area);
            SetUpdatedAt();
        }
    }

    public void RemoveOperationalArea(string area)
    {
        if (_operationalAreas.Remove(area))
        {
            SetUpdatedAt();
        }
    }

    public void AddDocument(DriverDocument document)
    {
        if (document.DriverId != Id)
            throw new InvalidOperationException("Document must belong to this driver");
        
        _documents.Add(document);
        SetUpdatedAt();
    }

    public bool RequiresLicenseRenewal(int daysThreshold = 30)
    {
        return LicenseExpiryDate <= DateTime.UtcNow.AddDays(daysThreshold);
    }

    public bool CanOperateInArea(string area)
    {
        return _operationalAreas.Contains(area) || _operationalAreas.Count == 0;
    }

    public bool PrefersRoute(string route)
    {
        return _preferredRoutes.Contains(route);
    }

    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        // Implementation would be added when integrating with shared domain events
    }
}

// Domain Events
public record DriverStatusChangedEvent(Guid DriverId, Guid CarrierId, DriverStatus OldStatus, DriverStatus NewStatus) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
