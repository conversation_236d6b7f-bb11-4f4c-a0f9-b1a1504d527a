using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class PaymentMetric : AggregateRoot
{
    public string MetricName { get; private set; }
    public string MetricType { get; private set; } // Count, Amount, Percentage, Rate
    public decimal Value { get; private set; }
    public string Unit { get; private set; } // INR, %, count, etc.
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public string Dimension { get; private set; } // Gateway, User, PaymentMethod, etc.
    public string? DimensionValue { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private PaymentMetric() { }

    public PaymentMetric(
        string metricName,
        string metricType,
        decimal value,
        string unit,
        DateTime periodStart,
        DateTime periodEnd,
        string dimension,
        string? dimensionValue = null,
        Dictionary<string, object>? metadata = null)
    {
        MetricName = metricName;
        MetricType = metricType;
        Value = value;
        Unit = unit;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        Dimension = dimension;
        DimensionValue = dimensionValue;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public void UpdateValue(decimal newValue)
    {
        Value = newValue;
        SetUpdatedAt();
    }
}

public class PaymentReport : AggregateRoot
{
    public string ReportName { get; private set; }
    public string ReportType { get; private set; }
    public DateTime GeneratedAt { get; private set; }
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public Guid? GeneratedBy { get; private set; }
    public ReportStatus Status { get; private set; }
    public string? FilePath { get; private set; }
    public string? FileFormat { get; private set; }
    public long? FileSizeBytes { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Summary { get; private set; }

    private PaymentReport() { }

    public PaymentReport(
        string reportName,
        string reportType,
        DateTime periodStart,
        DateTime periodEnd,
        Guid? generatedBy = null,
        Dictionary<string, object>? parameters = null)
    {
        ReportName = reportName;
        ReportType = reportType;
        GeneratedAt = DateTime.UtcNow;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        GeneratedBy = generatedBy;
        Status = ReportStatus.Pending;
        Parameters = parameters ?? new Dictionary<string, object>();
        Summary = new Dictionary<string, object>();
    }

    public void MarkAsGenerating()
    {
        Status = ReportStatus.Generating;
        SetUpdatedAt();
    }

    public void MarkAsCompleted(string filePath, string fileFormat, long fileSizeBytes, Dictionary<string, object>? summary = null)
    {
        Status = ReportStatus.Completed;
        FilePath = filePath;
        FileFormat = fileFormat;
        FileSizeBytes = fileSizeBytes;
        if (summary != null)
        {
            Summary = summary;
        }
        SetUpdatedAt();
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = ReportStatus.Failed;
        Summary["error"] = errorMessage;
        SetUpdatedAt();
    }
}

public class PaymentDashboard : AggregateRoot
{
    public string DashboardName { get; private set; }
    public string Description { get; private set; }
    public Guid? OwnerId { get; private set; }
    public bool IsPublic { get; private set; }
    public DateTime LastRefreshed { get; private set; }
    public int RefreshIntervalMinutes { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }

    // Navigation properties
    private readonly List<DashboardWidget> _widgets = new();
    public IReadOnlyList<DashboardWidget> Widgets => _widgets.AsReadOnly();

    private PaymentDashboard() { }

    public PaymentDashboard(
        string dashboardName,
        string description,
        Guid? ownerId = null,
        bool isPublic = false,
        int refreshIntervalMinutes = 15)
    {
        DashboardName = dashboardName;
        Description = description;
        OwnerId = ownerId;
        IsPublic = isPublic;
        LastRefreshed = DateTime.UtcNow;
        RefreshIntervalMinutes = refreshIntervalMinutes;
        Configuration = new Dictionary<string, object>();
    }

    public void AddWidget(string widgetName, string widgetType, Dictionary<string, object> configuration, int position = 0)
    {
        var widget = new DashboardWidget(Id, widgetName, widgetType, configuration, position);
        _widgets.Add(widget);
        SetUpdatedAt();
    }

    public void RemoveWidget(Guid widgetId)
    {
        var widget = _widgets.FirstOrDefault(w => w.Id == widgetId);
        if (widget != null)
        {
            _widgets.Remove(widget);
            SetUpdatedAt();
        }
    }

    public void UpdateRefreshInterval(int intervalMinutes)
    {
        RefreshIntervalMinutes = intervalMinutes;
        SetUpdatedAt();
    }

    public void MarkAsRefreshed()
    {
        LastRefreshed = DateTime.UtcNow;
        SetUpdatedAt();
    }
}



// Enums
public enum ReportStatus
{
    Pending = 1,
    Generating = 2,
    Completed = 3,
    Failed = 4
}

// Analytics DTOs
public class PaymentAnalyticsSummary
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public Money TotalVolume { get; set; } = Money.Zero("INR");
    public int TotalTransactions { get; set; }
    public Money AverageTransactionValue { get; set; } = Money.Zero("INR");
    public decimal SuccessRate { get; set; }
    public decimal FailureRate { get; set; }
    public decimal RefundRate { get; set; }
    public Money TotalRefunds { get; set; } = Money.Zero("INR");
    public Dictionary<string, decimal> PaymentMethodDistribution { get; set; } = new();
    public Dictionary<string, decimal> GatewayDistribution { get; set; } = new();
    public List<PaymentTrend> Volumetrends { get; set; } = new();
    public List<PaymentTrend> TransactionTrends { get; set; } = new();
}

public class PaymentTrend
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PaymentKPI
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal? PreviousValue { get; set; }
    public decimal? ChangePercentage { get; set; }
    public string Trend { get; set; } = "stable"; // up, down, stable
    public string Description { get; set; } = string.Empty;
}

public class PaymentAnalyticsFilter
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string> PaymentGateways { get; set; } = new();
    public List<string> PaymentMethods { get; set; } = new();
    public List<string> Statuses { get; set; } = new();
    public List<Guid> UserIds { get; set; } = new();
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public string? Currency { get; set; }
    public string GroupBy { get; set; } = "day"; // hour, day, week, month
}

public class ChartData
{
    public string ChartType { get; set; } = string.Empty; // line, bar, pie, doughnut
    public List<string> Labels { get; set; } = new();
    public List<ChartDataset> Datasets { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
}

public class ChartDataset
{
    public string Label { get; set; } = string.Empty;
    public List<decimal> Data { get; set; } = new();
    public string BackgroundColor { get; set; } = string.Empty;
    public string BorderColor { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
}


