using ContentManagement.Application.Commands.Policy;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Queries.Policy;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ContentManagement.API.Controllers;

/// <summary>
/// Controller for policy document management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PolicyController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PolicyController> _logger;

    public PolicyController(IMediator mediator, ILogger<PolicyController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get policy by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<PolicyDocumentDto>> GetById(Guid id, [FromQuery] bool includeAcceptances = false)
    {
        var query = new GetPolicyByIdQuery
        {
            Id = id,
            IncludeAcceptances = includeAcceptances,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Get policy by type
    /// </summary>
    [HttpGet("type/{policyType}")]
    [AllowAnonymous]
    public async Task<ActionResult<PolicyDocumentDto>> GetByType(
        Domain.Enums.PolicyType policyType,
        [FromQuery] bool currentVersionOnly = true,
        [FromQuery] bool includeAcceptances = false)
    {
        var query = new GetPolicyByTypeQuery
        {
            PolicyType = policyType,
            CurrentVersionOnly = currentVersionOnly,
            IncludeAcceptances = includeAcceptances,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Search policies with filters
    /// </summary>
    [HttpGet("search")]
    [AllowAnonymous]
    public async Task<ActionResult<PolicySearchResultDto>> Search([FromQuery] SearchPoliciesQuery query)
    {
        query.UserRoles = GetUserRoles();
        query.IsAuthenticated = User.Identity?.IsAuthenticated ?? false;

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get policies applicable to current user
    /// </summary>
    [HttpGet("applicable")]
    [Authorize]
    public async Task<ActionResult<List<PolicySummaryDto>>> GetApplicable(
        [FromQuery] bool requiresAcceptanceOnly = false,
        [FromQuery] bool currentVersionOnly = true,
        [FromQuery] bool effectiveOnly = true)
    {
        var query = new GetApplicablePoliciesQuery
        {
            UserRoles = GetUserRoles(),
            RequiresAcceptanceOnly = requiresAcceptanceOnly,
            CurrentVersionOnly = currentVersionOnly,
            EffectiveOnly = effectiveOnly
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get policies requiring acceptance
    /// </summary>
    [HttpGet("requiring-acceptance")]
    [Authorize]
    public async Task<ActionResult<List<PolicySummaryDto>>> GetRequiringAcceptance(
        [FromQuery] bool currentVersionOnly = true,
        [FromQuery] bool effectiveOnly = true)
    {
        var query = new GetPoliciesRequiringAcceptanceQuery
        {
            UserRoles = GetUserRoles(),
            CurrentVersionOnly = currentVersionOnly,
            EffectiveOnly = effectiveOnly
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get user policy status
    /// </summary>
    [HttpGet("user-status")]
    [Authorize]
    public async Task<ActionResult<UserPolicyStatusDto>> GetUserStatus([FromQuery] bool requiresAcceptanceOnly = false)
    {
        var query = new GetUserPolicyStatusQuery
        {
            UserId = GetUserId(),
            UserName = GetUserName(),
            UserRoles = GetUserRoles(),
            RequiresAcceptanceOnly = requiresAcceptanceOnly
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Create new policy document
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyDocumentDto>> Create([FromBody] CreatePolicyDocumentCommand command)
    {
        command.CreatedBy = GetUserId();
        command.CreatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Update existing policy document
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyDocumentDto>> Update(Guid id, [FromBody] UpdatePolicyDocumentCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Create new version of policy document
    /// </summary>
    [HttpPost("{id:guid}/versions")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyDocumentDto>> CreateVersion(Guid id, [FromBody] CreatePolicyVersionCommand command)
    {
        command.PolicyId = id;
        command.CreatedBy = GetUserId();
        command.CreatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Activate policy version as current
    /// </summary>
    [HttpPost("{id:guid}/activate")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyDocumentDto>> ActivateVersion(Guid id)
    {
        var command = new ActivatePolicyVersionCommand
        {
            Id = id,
            UpdatedBy = GetUserId(),
            UpdatedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Deactivate policy version
    /// </summary>
    [HttpPost("{id:guid}/deactivate")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyDocumentDto>> DeactivateVersion(Guid id)
    {
        var command = new DeactivatePolicyVersionCommand
        {
            Id = id,
            UpdatedBy = GetUserId(),
            UpdatedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Accept policy
    /// </summary>
    [HttpPost("{id:guid}/accept")]
    [Authorize]
    public async Task<ActionResult<PolicyAcceptanceDto>> AcceptPolicy(Guid id)
    {
        var command = new AcceptPolicyCommand
        {
            PolicyId = id,
            UserId = GetUserId(),
            UserName = GetUserName(),
            UserRole = GetUserRoles().FirstOrDefault() ?? "User",
            IpAddress = GetClientIpAddress(),
            UserAgent = Request.Headers.UserAgent.ToString()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Bulk accept multiple policies
    /// </summary>
    [HttpPost("bulk-accept")]
    [Authorize]
    public async Task<ActionResult<List<PolicyAcceptanceDto>>> BulkAcceptPolicies([FromBody] BulkAcceptPoliciesCommand command)
    {
        command.UserId = GetUserId();
        command.UserName = GetUserName();
        command.UserRole = GetUserRoles().FirstOrDefault() ?? "User";
        command.IpAddress = GetClientIpAddress();
        command.UserAgent = Request.Headers.UserAgent.ToString();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Get policy versions
    /// </summary>
    [HttpGet("type/{policyType}/versions")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<List<PolicySummaryDto>>> GetVersions(
        Domain.Enums.PolicyType policyType,
        [FromQuery] bool includeObsolete = false)
    {
        var query = new GetPolicyVersionsQuery
        {
            PolicyType = policyType,
            IncludeObsolete = includeObsolete
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get policy compliance report
    /// </summary>
    [HttpGet("{id:guid}/compliance-report")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyComplianceReportDto>> GetComplianceReport(
        Guid id,
        [FromQuery] List<string>? filterByRoles = null,
        [FromQuery] bool includeRecentAcceptances = true,
        [FromQuery] int recentAcceptancesLimit = 10)
    {
        var query = new GetPolicyComplianceReportQuery
        {
            PolicyId = id,
            FilterByRoles = filterByRoles,
            IncludeRecentAcceptances = includeRecentAcceptances,
            RecentAcceptancesLimit = recentAcceptancesLimit
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get policy acceptance history
    /// </summary>
    [HttpGet("acceptance-history")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<List<PolicyAcceptanceDto>>> GetAcceptanceHistory([FromQuery] GetPolicyAcceptanceHistoryQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get policy analytics
    /// </summary>
    [HttpGet("analytics")]
    [Authorize(Roles = "Admin,PolicyManager")]
    public async Task<ActionResult<PolicyAnalyticsDto>> GetAnalytics([FromQuery] GetPolicyAnalyticsQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }

    private List<string> GetUserRoles()
    {
        return User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
    }

    private string? GetClientIpAddress()
    {
        return Request.Headers["X-Forwarded-For"].FirstOrDefault() ?? 
               Request.HttpContext.Connection.RemoteIpAddress?.ToString();
    }
}
