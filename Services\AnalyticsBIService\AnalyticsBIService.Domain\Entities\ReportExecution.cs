using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Report execution entity for tracking report generation instances
/// </summary>
public class ReportExecution : BaseEntity
{
    public Guid ReportId { get; private set; }
    public Guid? TemplateId { get; private set; }
    public Guid UserId { get; private set; }
    public string Status { get; private set; }
    public string Parameters { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? OutputPath { get; private set; }
    public ExportFormat OutputFormat { get; private set; }
    public long? FileSizeBytes { get; private set; }
    public int? RecordCount { get; private set; }

    // Additional properties required by services
    public string? ReportData { get; private set; }
    public DateTime ExecutionStartTime => StartTime; // Alias for StartTime
    public DateTime? ExecutionEndTime => EndTime; // Alias for EndTime
    public bool IsManualExecution { get; private set; }
    public string? ReportName { get; private set; }
    public DateTime? ExecutedAt => EndTime; // Alias for EndTime
    public Guid? ExecutedBy => UserId; // Alias for UserId
    public TimeSpan? Duration => GetDuration(); // Use existing method
    public string? FilePath => OutputPath; // Alias for OutputPath
    public long? FileSize => FileSizeBytes; // Alias for FileSizeBytes
    public bool? DeliverySuccessful { get; private set; }
    public Guid? ScheduleId { get; private set; }
    public string? TriggerType { get; private set; }

    // Additional aliases for service compatibility
    public DateTime? StartedAt => StartTime; // Alias for StartTime
    public DateTime? CompletedAt => EndTime; // Alias for EndTime
    public string? ReportFilePath => OutputPath; // Alias for OutputPath
    public long? ReportFileSize => FileSizeBytes; // Alias for FileSizeBytes
    public Guid? ReportTemplateId => TemplateId; // Alias for TemplateId

    // Navigation properties
    public virtual Report? Report { get; private set; }
    public virtual ReportTemplate? Template { get; private set; }

    // Private constructor for EF Core
    private ReportExecution()
    {
        Status = string.Empty;
        Parameters = string.Empty;
    }

    /// <summary>
    /// Create a new report execution
    /// </summary>
    public static ReportExecution Create(
        Guid reportId,
        Guid? templateId,
        Guid userId,
        string parameters,
        ExportFormat outputFormat,
        bool isManualExecution = false,
        string? reportName = null,
        Guid? scheduleId = null,
        string? triggerType = null)
    {
        var execution = new ReportExecution
        {
            ReportId = reportId,
            TemplateId = templateId,
            UserId = userId,
            Status = "Started",
            Parameters = parameters,
            OutputFormat = outputFormat,
            StartTime = DateTime.UtcNow,
            IsManualExecution = isManualExecution,
            ReportName = reportName,
            ScheduleId = scheduleId,
            TriggerType = triggerType ?? (isManualExecution ? "Manual" : "Scheduled")
        };

        return execution;
    }

    /// <summary>
    /// Mark execution as completed
    /// </summary>
    public void MarkCompleted(string outputPath, long fileSizeBytes, int recordCount, string? reportData = null, bool? deliverySuccessful = null)
    {
        Status = "Completed";
        EndTime = DateTime.UtcNow;
        OutputPath = outputPath;
        FileSizeBytes = fileSizeBytes;
        RecordCount = recordCount;
        ReportData = reportData;
        DeliverySuccessful = deliverySuccessful;
        ErrorMessage = null;
    }

    /// <summary>
    /// Set delivery status
    /// </summary>
    public void SetDeliveryStatus(bool deliverySuccessful)
    {
        DeliverySuccessful = deliverySuccessful;
    }

    /// <summary>
    /// Mark execution as failed
    /// </summary>
    public void MarkFailed(string errorMessage)
    {
        Status = "Failed";
        EndTime = DateTime.UtcNow;
        ErrorMessage = errorMessage;
    }

    /// <summary>
    /// Mark execution as cancelled
    /// </summary>
    public void MarkCancelled()
    {
        Status = "Cancelled";
        EndTime = DateTime.UtcNow;
    }

    /// <summary>
    /// Update execution progress
    /// </summary>
    public void UpdateProgress(string status)
    {
        Status = status;
    }

    /// <summary>
    /// Set report data
    /// </summary>
    public void SetReportData(string reportData)
    {
        ReportData = reportData;
    }

    /// <summary>
    /// Get execution duration
    /// </summary>
    public TimeSpan? GetDuration()
    {
        if (EndTime.HasValue)
            return EndTime.Value - StartTime;

        return DateTime.UtcNow - StartTime;
    }

    /// <summary>
    /// Check if execution is completed
    /// </summary>
    public bool IsCompleted => Status == "Completed";

    /// <summary>
    /// Check if execution failed
    /// </summary>
    public bool IsFailed => Status == "Failed";

    /// <summary>
    /// Check if execution is running
    /// </summary>
    public bool IsRunning => Status == "Started" || Status == "Processing";
}
