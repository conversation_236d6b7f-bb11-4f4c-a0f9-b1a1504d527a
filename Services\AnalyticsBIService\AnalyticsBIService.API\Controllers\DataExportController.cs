using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using AnalyticsBIService.Domain.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Data export and integration controller for custom reports, data pipelines, and BI tool integrations
/// </summary>
[ApiController]
[Route("api/data-export")]
[Authorize]
public class DataExportController : ControllerBase
{
    private readonly IDataExportService _dataExportService;
    private readonly ILogger<DataExportController> _logger;

    public DataExportController(
        IDataExportService dataExportService,
        ILogger<DataExportController> logger)
    {
        _dataExportService = dataExportService;
        _logger = logger;
    }

    /// <summary>
    /// Export data in specified format
    /// </summary>
    /// <param name="request">Data export request</param>
    /// <returns>Export result with download link</returns>
    [HttpPost("export")]
    public async Task<ActionResult<ExportResultDto>> ExportData([FromBody] DataExportRequestDto request)
    {
        try
        {
            request.UserId = GetCurrentUserId();
            var result = await _dataExportService.ExportDataAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get supported export formats
    /// </summary>
    /// <returns>List of supported export formats</returns>
    [HttpGet("formats")]
    public async Task<ActionResult<List<ExportFormatDto>>> GetSupportedFormats()
    {
        try
        {
            var formats = await _dataExportService.GetSupportedFormatsAsync();
            return Ok(formats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting supported formats");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a custom report
    /// </summary>
    /// <param name="request">Custom report request</param>
    /// <returns>Created custom report</returns>
    [HttpPost("reports")]
    public async Task<ActionResult<CustomReportDto>> CreateCustomReport([FromBody] CustomReportRequestDto request)
    {
        try
        {
            request.UserId = GetCurrentUserId();
            var report = await _dataExportService.CreateCustomReportAsync(request);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get custom reports for the current user
    /// </summary>
    /// <returns>List of custom reports</returns>
    [HttpGet("reports")]
    public async Task<ActionResult<List<CustomReportDto>>> GetCustomReports()
    {
        try
        {
            var userId = GetCurrentUserId();
            var reports = await _dataExportService.GetCustomReportsAsync(userId);
            return Ok(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting custom reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export a custom report
    /// </summary>
    /// <param name="reportId">Report ID</param>
    /// <param name="format">Export format</param>
    /// <returns>Export result with download link</returns>
    [HttpPost("reports/{reportId}/export")]
    public async Task<ActionResult<ExportResultDto>> ExportCustomReport(
        Guid reportId,
        [FromQuery] ExportFormat format = ExportFormat.CSV)
    {
        try
        {
            var result = await _dataExportService.ExportCustomReportAsync(reportId, format);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting custom report {ReportId}", reportId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a data pipeline
    /// </summary>
    /// <param name="request">Data pipeline request</param>
    /// <returns>Created data pipeline</returns>
    [HttpPost("pipelines")]
    public async Task<ActionResult<DataPipelineDto>> CreateDataPipeline([FromBody] DataPipelineRequestDto request)
    {
        try
        {
            request.UserId = GetCurrentUserId();
            var pipeline = await _dataExportService.CreateDataPipelineAsync(request);
            return Ok(pipeline);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating data pipeline");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get data pipelines for the current user
    /// </summary>
    /// <returns>List of data pipelines</returns>
    [HttpGet("pipelines")]
    public async Task<ActionResult<List<DataPipelineDto>>> GetDataPipelines()
    {
        try
        {
            var userId = GetCurrentUserId();
            var pipelines = await _dataExportService.GetDataPipelinesAsync(userId);
            return Ok(pipelines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting data pipelines");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Execute a data pipeline
    /// </summary>
    /// <param name="pipelineId">Pipeline ID</param>
    /// <returns>Execution result</returns>
    [HttpPost("pipelines/{pipelineId}/execute")]
    public async Task<ActionResult<object>> ExecuteDataPipeline(Guid pipelineId)
    {
        try
        {
            var success = await _dataExportService.ExecuteDataPipelineAsync(pipelineId);
            return Ok(new { Success = success, Message = success ? "Pipeline executed successfully" : "Pipeline execution failed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing data pipeline {PipelineId}", pipelineId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get integration status for external BI tools
    /// </summary>
    /// <returns>Integration status</returns>
    [HttpGet("integrations/status")]
    public async Task<ActionResult<IntegrationStatusDto>> GetIntegrationStatus()
    {
        try
        {
            var userId = GetCurrentUserId();
            var status = await _dataExportService.GetIntegrationStatusAsync(userId);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting integration status");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report builder configuration
    /// </summary>
    /// <returns>Report builder configuration</returns>
    [HttpGet("report-builder/config")]
    public ActionResult<object> GetReportBuilderConfig()
    {
        try
        {
            var config = new
            {
                DataSources = new[]
                {
                    new { Id = "analytics_events", Name = "Analytics Events", Description = "User interaction and business events" },
                    new { Id = "metrics", Name = "Metrics", Description = "KPI and performance metrics" },
                    new { Id = "alerts", Name = "Alerts", Description = "System and business alerts" },
                    new { Id = "users", Name = "Users", Description = "User and role information" }
                },
                AvailableColumns = new
                {
                    analytics_events = new[] { "event_name", "user_id", "timestamp", "event_data", "session_id" },
                    metrics = new[] { "metric_name", "value", "category", "user_id", "created_at" },
                    alerts = new[] { "alert_type", "severity", "title", "message", "user_id", "created_at" },
                    users = new[] { "user_id", "role", "created_at", "last_login" }
                },
                AggregationFunctions = new[] { "COUNT", "SUM", "AVG", "MIN", "MAX", "DISTINCT" },
                FilterOperators = new[] { "=", "!=", ">", "<", ">=", "<=", "LIKE", "IN", "BETWEEN" },
                VisualizationTypes = new[]
                {
                    new { Type = "table", Name = "Table", Description = "Tabular data display" },
                    new { Type = "line_chart", Name = "Line Chart", Description = "Time series data" },
                    new { Type = "bar_chart", Name = "Bar Chart", Description = "Categorical data comparison" },
                    new { Type = "pie_chart", Name = "Pie Chart", Description = "Proportional data" },
                    new { Type = "scatter_plot", Name = "Scatter Plot", Description = "Correlation analysis" }
                },
                ScheduleOptions = new[]
                {
                    new { Value = "manual", Name = "Manual", Description = "Run manually" },
                    new { Value = "daily", Name = "Daily", Description = "Run daily at specified time" },
                    new { Value = "weekly", Name = "Weekly", Description = "Run weekly on specified day" },
                    new { Value = "monthly", Name = "Monthly", Description = "Run monthly on specified date" }
                }
            };

            return Ok(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report builder config");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get data pipeline templates
    /// </summary>
    /// <returns>Available pipeline templates</returns>
    [HttpGet("pipelines/templates")]
    public ActionResult<object> GetPipelineTemplates()
    {
        try
        {
            var templates = new[]
            {
                new
                {
                    Id = "daily_metrics_export",
                    Name = "Daily Metrics Export",
                    Description = "Export daily metrics to external BI tool",
                    Category = "BI Integration",
                    SourceType = "Database",
                    DestinationType = "External API",
                    Schedule = "Daily",
                    EstimatedRunTime = "5 minutes"
                },
                new
                {
                    Id = "user_analytics_sync",
                    Name = "User Analytics Sync",
                    Description = "Sync user analytics data to data warehouse",
                    Category = "Data Warehouse",
                    SourceType = "Analytics Events",
                    DestinationType = "Data Warehouse",
                    Schedule = "Hourly",
                    EstimatedRunTime = "10 minutes"
                },
                new
                {
                    Id = "alert_notification_pipeline",
                    Name = "Alert Notification Pipeline",
                    Description = "Process alerts and send notifications",
                    Category = "Notifications",
                    SourceType = "Alerts",
                    DestinationType = "Notification Service",
                    Schedule = "Real-time",
                    EstimatedRunTime = "1 minute"
                },
                new
                {
                    Id = "performance_report_generation",
                    Name = "Performance Report Generation",
                    Description = "Generate and distribute performance reports",
                    Category = "Reporting",
                    SourceType = "Multiple",
                    DestinationType = "Email/File Storage",
                    Schedule = "Weekly",
                    EstimatedRunTime = "15 minutes"
                }
            };

            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pipeline templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get BI tool integration options
    /// </summary>
    /// <returns>Available BI tool integrations</returns>
    [HttpGet("integrations/bi-tools")]
    public ActionResult<object> GetBIToolIntegrations()
    {
        try
        {
            var biTools = new[]
            {
                new
                {
                    Id = "tableau",
                    Name = "Tableau",
                    Description = "Connect to Tableau for advanced data visualization",
                    ConnectionType = "REST API",
                    AuthenticationMethods = new[] { "API Key", "OAuth 2.0" },
                    SupportedFormats = new[] { "CSV", "JSON", "Tableau Extract" },
                    Features = new[] { "Real-time sync", "Scheduled exports", "Custom dashboards" }
                },
                new
                {
                    Id = "powerbi",
                    Name = "Microsoft Power BI",
                    Description = "Integrate with Power BI for business intelligence",
                    ConnectionType = "REST API",
                    AuthenticationMethods = new[] { "Service Principal", "OAuth 2.0" },
                    SupportedFormats = new[] { "CSV", "JSON", "Power BI Dataset" },
                    Features = new[] { "Direct query", "Import mode", "Streaming datasets" }
                },
                new
                {
                    Id = "looker",
                    Name = "Looker",
                    Description = "Connect to Looker for data exploration",
                    ConnectionType = "API",
                    AuthenticationMethods = new[] { "API Key", "OAuth 2.0" },
                    SupportedFormats = new[] { "CSV", "JSON", "Looker API" },
                    Features = new[] { "Custom dimensions", "Real-time data", "Embedded analytics" }
                },
                new
                {
                    Id = "qlik",
                    Name = "Qlik Sense",
                    Description = "Integrate with Qlik Sense for associative analytics",
                    ConnectionType = "REST API",
                    AuthenticationMethods = new[] { "API Key", "Certificate" },
                    SupportedFormats = new[] { "CSV", "JSON", "QVD" },
                    Features = new[] { "Associative model", "Self-service BI", "Mobile analytics" }
                },
                new
                {
                    Id = "datastudio",
                    Name = "Google Data Studio",
                    Description = "Connect to Google Data Studio for reporting",
                    ConnectionType = "Community Connector",
                    AuthenticationMethods = new[] { "OAuth 2.0", "Service Account" },
                    SupportedFormats = new[] { "CSV", "JSON", "Google Sheets" },
                    Features = new[] { "Free tool", "Easy sharing", "Google ecosystem integration" }
                }
            };

            return Ok(biTools);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting BI tool integrations");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate data export request
    /// </summary>
    /// <param name="request">Export request to validate</param>
    /// <returns>Validation result</returns>
    [HttpPost("validate")]
    public ActionResult<object> ValidateExportRequest([FromBody] DataExportRequestDto request)
    {
        try
        {
            var validationResult = new
            {
                IsValid = true,
                Errors = new List<string>(),
                Warnings = new List<string>(),
                EstimatedRecords = 1000, // This would be calculated
                EstimatedFileSize = "2.5 MB", // This would be calculated
                EstimatedProcessingTime = "30 seconds" // This would be calculated
            };

            // Add validation logic here
            if (string.IsNullOrEmpty(request.DataType))
            {
                validationResult = validationResult with
                {
                    IsValid = false,
                    Errors = validationResult.Errors.Concat(new[] { "Data type is required" }).ToList()
                };
            }

            if (request.SelectedColumns?.Count > 50)
            {
                validationResult = validationResult with
                {
                    Warnings = validationResult.Warnings.Concat(new[] { "Large number of columns may impact performance" }).ToList()
                };
            }

            return Ok(validationResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating export request");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export per-user usage logs with cross-service data aggregation
    /// </summary>
    /// <param name="request">Usage logs export request</param>
    /// <returns>Export result with download link</returns>
    [HttpPost("usage-logs/export")]
    [Authorize(Roles = "Admin,Auditor")]
    public async Task<ActionResult<ExportResultDto>> ExportUsageLogs([FromBody] UsageLogsExportRequestDto request)
    {
        try
        {
            request.RequestedBy = GetCurrentUserId();
            request.RequestedByRole = GetCurrentUserRole();
            request.IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            request.UserAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            var result = await _dataExportService.ExportUsageLogsAsync(request);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting usage logs");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available data sources for usage logs export
    /// </summary>
    /// <returns>Available data sources and their columns</returns>
    [HttpGet("usage-logs/sources")]
    [Authorize(Roles = "Admin,Auditor")]
    public ActionResult<object> GetUsageLogsSources()
    {
        try
        {
            var sources = new
            {
                DataSources = new[]
                {
                    new
                    {
                        Name = "Orders",
                        Description = "Order creation, updates, and completion activities",
                        Columns = new[] { "order_id", "user_id", "action", "timestamp", "order_value", "status", "location" }
                    },
                    new
                    {
                        Name = "RFQs",
                        Description = "RFQ creation, bidding, and closure activities",
                        Columns = new[] { "rfq_id", "user_id", "action", "timestamp", "rfq_value", "status", "route" }
                    },
                    new
                    {
                        Name = "Trips",
                        Description = "Trip assignment, tracking, and completion activities",
                        Columns = new[] { "trip_id", "user_id", "action", "timestamp", "distance", "status", "vehicle_type" }
                    },
                    new
                    {
                        Name = "Analytics",
                        Description = "Platform usage and interaction analytics",
                        Columns = new[] { "event_name", "user_id", "timestamp", "session_id", "properties", "metadata" }
                    }
                },
                ActivityTypes = new[]
                {
                    "Login", "Logout", "Create", "Update", "Delete", "View", "Search",
                    "Export", "Import", "Approve", "Reject", "Submit", "Complete"
                },
                ExportFormats = new[] { "CSV", "Excel", "JSON" },
                MaxRecords = 100000,
                MaxDateRange = "90 days"
            };

            return Ok(sources);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage logs sources");
            return StatusCode(500, "Internal server error");
        }
    }

    // Helper methods
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user ID");
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
    }
}
