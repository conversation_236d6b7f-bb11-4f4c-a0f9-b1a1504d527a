using Shared.Domain.Common;
using TripManagement.Domain.Events;

namespace TripManagement.Domain.Entities;

/// <summary>
/// Master data entity for cancellation reasons
/// </summary>
public class CancellationReasonMaster : AggregateRoot
{
    public string Code { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Category { get; private set; } = string.Empty; // Customer, Carrier, System, Weather, etc.
    public string Severity { get; private set; } = string.Empty; // Low, Medium, High, Critical
    public bool RequiresApproval { get; private set; }
    public bool IsRefundable { get; private set; }
    public decimal RefundPercentage { get; private set; }
    public bool RequiresDocumentation { get; private set; }
    public string? DocumentationRequirements { get; private set; }
    public bool IsActive { get; private set; }
    public int SortOrder { get; private set; }
    public string? IconUrl { get; private set; }
    public Dictionary<string, object> AdditionalProperties { get; private set; } = new();

    // Private constructor for EF Core
    private CancellationReasonMaster() { }

    public CancellationReasonMaster(
        string code,
        string name,
        string description,
        string category,
        string severity = "Low",
        bool requiresApproval = false,
        bool isRefundable = false,
        decimal refundPercentage = 0m,
        bool requiresDocumentation = false,
        string? documentationRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("Cancellation reason code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Cancellation reason name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        Code = code.Trim().ToUpperInvariant();
        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        Severity = severity.Trim();
        RequiresApproval = requiresApproval;
        IsRefundable = isRefundable;
        RefundPercentage = refundPercentage;
        RequiresDocumentation = requiresDocumentation;
        DocumentationRequirements = documentationRequirements?.Trim();
        IsActive = true;
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();
        AdditionalProperties = additionalProperties ?? new Dictionary<string, object>();

        // Validate refund percentage
        ValidateRefundPercentage();

        // Add domain event
        AddDomainEvent(new CancellationReasonMasterCreatedEvent(Id, Code, Name, Category));
    }

    public void UpdateDetails(
        string name,
        string description,
        string category,
        string severity = "Low",
        bool requiresApproval = false,
        bool isRefundable = false,
        decimal refundPercentage = 0m,
        bool requiresDocumentation = false,
        string? documentationRequirements = null,
        int sortOrder = 0,
        string? iconUrl = null,
        Dictionary<string, object>? additionalProperties = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Cancellation reason name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be empty", nameof(category));

        var oldName = Name;
        var oldCategory = Category;

        Name = name.Trim();
        Description = description?.Trim() ?? string.Empty;
        Category = category.Trim();
        Severity = severity.Trim();
        RequiresApproval = requiresApproval;
        IsRefundable = isRefundable;
        RefundPercentage = refundPercentage;
        RequiresDocumentation = requiresDocumentation;
        DocumentationRequirements = documentationRequirements?.Trim();
        SortOrder = sortOrder;
        IconUrl = iconUrl?.Trim();

        if (additionalProperties != null)
        {
            AdditionalProperties = additionalProperties;
        }

        // Validate refund percentage
        ValidateRefundPercentage();

        SetUpdatedAt();

        // Add domain event if significant changes
        if (oldName != Name || oldCategory != Category)
        {
            AddDomainEvent(new CancellationReasonMasterUpdatedEvent(Id, Code, Name, Category, oldName, oldCategory));
        }
    }

    public void Activate()
    {
        if (IsActive) return;

        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new CancellationReasonMasterActivatedEvent(Id, Code, Name));
    }

    public void Deactivate()
    {
        if (!IsActive) return;

        IsActive = false;
        SetUpdatedAt();

        AddDomainEvent(new CancellationReasonMasterDeactivatedEvent(Id, Code, Name));
    }

    public void UpdateSortOrder(int newSortOrder)
    {
        if (SortOrder == newSortOrder) return;

        SortOrder = newSortOrder;
        SetUpdatedAt();
    }

    public void AddOrUpdateProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        AdditionalProperties[key] = value;
        SetUpdatedAt();
    }

    public void RemoveProperty(string key)
    {
        if (string.IsNullOrWhiteSpace(key)) return;

        if (AdditionalProperties.Remove(key))
        {
            SetUpdatedAt();
        }
    }

    public decimal CalculateRefundAmount(decimal originalAmount)
    {
        if (!IsRefundable) return 0m;

        return originalAmount * (RefundPercentage / 100m);
    }

    public bool IsHighSeverity()
    {
        return Severity.Equals("High", StringComparison.OrdinalIgnoreCase) ||
               Severity.Equals("Critical", StringComparison.OrdinalIgnoreCase);
    }

    private void ValidateRefundPercentage()
    {
        if (RefundPercentage < 0 || RefundPercentage > 100)
            throw new ArgumentException("Refund percentage must be between 0 and 100");
    }
}


