using ContentManagement.Domain.Enums;

namespace ContentManagement.Application.DTOs;

/// <summary>
/// Data transfer object for content
/// </summary>
public class ContentDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ContentType Type { get; set; }
    public ContentStatus Status { get; set; }
    public ContentVisibilityDto Visibility { get; set; } = null!;
    public string ContentBody { get; set; } = string.Empty;
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<string> Tags { get; set; } = new();
    public int SortOrder { get; set; }
    public PublishScheduleDto? PublishSchedule { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public Guid? ApprovedBy { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<ContentVersionDto> Versions { get; set; } = new();
    public List<ContentAttachmentDto> Attachments { get; set; } = new();
}

/// <summary>
/// Data transfer object for content visibility settings
/// </summary>
public class ContentVisibilityDto
{
    public VisibilityLevel Level { get; set; }
    public List<string> AllowedRoles { get; set; } = new();
    public DateTime? VisibleFrom { get; set; }
    public DateTime? VisibleUntil { get; set; }
}

/// <summary>
/// Data transfer object for publish schedule
/// </summary>
public class PublishScheduleDto
{
    public DateTime? PublishAt { get; set; }
    public DateTime? UnpublishAt { get; set; }
    public bool IsRecurring { get; set; }
    public RecurrencePattern Pattern { get; set; }
    public int? RecurrenceInterval { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
}

/// <summary>
/// Data transfer object for content version
/// </summary>
public class ContentVersionDto
{
    public Guid Id { get; set; }
    public Guid ContentId { get; set; }
    public int VersionNumber { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsPublished { get; set; }
}

/// <summary>
/// Data transfer object for content attachment
/// </summary>
public class ContentAttachmentDto
{
    public Guid Id { get; set; }
    public Guid ContentId { get; set; }
    public Guid DocumentId { get; set; }
    public AttachmentType Type { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int SortOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Simplified content DTO for lists and summaries
/// </summary>
public class ContentSummaryDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ContentType Type { get; set; }
    public ContentStatus Status { get; set; }
    public int SortOrder { get; set; }
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Content search result DTO
/// </summary>
public class ContentSearchResultDto
{
    public List<ContentSummaryDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}
