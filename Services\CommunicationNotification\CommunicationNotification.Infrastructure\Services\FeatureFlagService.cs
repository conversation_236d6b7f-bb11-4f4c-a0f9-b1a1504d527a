using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for feature flag management and evaluation with gradual rollout capabilities
/// </summary>
public class FeatureFlagService : IFeatureFlagService
{
    private readonly IFeatureFlagRepository _featureFlagRepository;
    private readonly IFeatureFlagAnalyticsService _analyticsService;
    private readonly IDistributedCache _cache;
    private readonly ILogger<FeatureFlagService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    public FeatureFlagService(
        IFeatureFlagRepository featureFlagRepository,
        IFeatureFlagAnalyticsService analyticsService,
        IDistributedCache cache,
        ILogger<FeatureFlagService> logger)
    {
        _featureFlagRepository = featureFlagRepository;
        _analyticsService = analyticsService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<FeatureFlag> CreateFeatureFlagAsync(
        string key,
        string name,
        string description,
        FeatureFlagType type,
        string environment,
        Guid createdByUserId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating feature flag {Key} in environment {Environment} by user {UserId}",
                key, environment, createdByUserId);

            // Check if feature flag already exists
            if (await _featureFlagRepository.ExistsAsync(key, environment, cancellationToken))
            {
                throw new InvalidOperationException($"Feature flag with key '{key}' already exists in environment '{environment}'");
            }

            var featureFlag = FeatureFlag.Create(
                key, name, description, type, environment, createdByUserId,
                startDate, endDate, rolloutConfiguration, targetingRules, defaultValue, configuration);

            var savedFeatureFlag = await _featureFlagRepository.AddAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(key, environment, cancellationToken);

            _logger.LogInformation("Successfully created feature flag {Key} with ID {Id}", key, savedFeatureFlag.Id);
            return savedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag {Key}", key);
            throw;
        }
    }

    public async Task<FeatureFlag?> GetFeatureFlagAsync(
        string key,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting feature flag {Key} in environment {Environment}", key, environment);

            var cacheKey = $"feature_flag_{key}_{environment ?? "default"}";
            var cachedFlag = await GetFromCacheAsync<FeatureFlag>(cacheKey, cancellationToken);
            if (cachedFlag != null)
            {
                return cachedFlag;
            }

            var featureFlag = await _featureFlagRepository.GetByKeyAsync(key, environment, cancellationToken);
            if (featureFlag != null)
            {
                await SetCacheAsync(cacheKey, featureFlag, cancellationToken);
            }

            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {Key}", key);
            throw;
        }
    }

    public async Task<FeatureFlag?> GetFeatureFlagByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting feature flag by ID {Id}", id);

            var cacheKey = $"feature_flag_id_{id}";
            var cachedFlag = await GetFromCacheAsync<FeatureFlag>(cacheKey, cancellationToken);
            if (cachedFlag != null)
            {
                return cachedFlag;
            }

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag != null)
            {
                await SetCacheAsync(cacheKey, featureFlag, cancellationToken);
            }

            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag by ID {Id}", id);
            throw;
        }
    }

    public async Task<List<FeatureFlag>> GetAllFeatureFlagsAsync(
        string? environment = null,
        FeatureFlagStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting all feature flags for environment {Environment} with status {Status}",
                environment, status);

            var cacheKey = $"feature_flags_{environment ?? "all"}_{status?.ToString() ?? "all"}";
            var cachedFlags = await GetFromCacheAsync<List<FeatureFlag>>(cacheKey, cancellationToken);
            if (cachedFlags != null)
            {
                return cachedFlags;
            }

            List<FeatureFlag> featureFlags;
            if (status.HasValue)
            {
                featureFlags = await _featureFlagRepository.GetByStatusAsync(status.Value, environment, cancellationToken);
            }
            else
            {
                featureFlags = await _featureFlagRepository.GetAllAsync(environment, cancellationToken);
            }

            await SetCacheAsync(cacheKey, featureFlags, TimeSpan.FromMinutes(5), cancellationToken);

            _logger.LogDebug("Retrieved {Count} feature flags", featureFlags.Count);
            return featureFlags;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all feature flags");
            throw;
        }
    }

    public async Task<FeatureFlag> UpdateFeatureFlagAsync(
        Guid id,
        string? name = null,
        string? description = null,
        RolloutConfiguration? rolloutConfiguration = null,
        TargetingRules? targetingRules = null,
        Dictionary<string, object>? defaultValue = null,
        Dictionary<string, object>? configuration = null,
        Guid? modifiedByUserId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating feature flag {Id}", id);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(name))
            {
                // Use reflection or add update methods to entity
                // For now, we'll create a new instance with updated values
            }

            if (rolloutConfiguration != null)
            {
                featureFlag.UpdateRolloutConfiguration(rolloutConfiguration);
            }

            if (targetingRules != null)
            {
                featureFlag.UpdateTargetingRules(targetingRules);
            }

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully updated feature flag {Id}", id);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag {Id}", id);
            throw;
        }
    }

    public async Task DeleteFeatureFlagAsync(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting feature flag {Id}", id);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            await _featureFlagRepository.DeleteAsync(id, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully deleted feature flag {Id}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting feature flag {Id}", id);
            throw;
        }
    }

    public async Task<FeatureFlag> AddVariantAsync(
        Guid featureFlagId,
        string variantName,
        string description,
        decimal trafficAllocation,
        object? value = null,
        Dictionary<string, object>? configuration = null,
        bool isControl = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adding variant {VariantName} to feature flag {FeatureFlagId}",
                variantName, featureFlagId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {featureFlagId} not found");
            }

            var variant = new FeatureFlagVariant(variantName, description, trafficAllocation, value, configuration, isControl);
            featureFlag.AddVariant(variant);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully added variant {VariantName} to feature flag {FeatureFlagId}",
                variantName, featureFlagId);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding variant {VariantName} to feature flag {FeatureFlagId}",
                variantName, featureFlagId);
            throw;
        }
    }

    public async Task<FeatureFlag> RemoveVariantAsync(
        Guid featureFlagId,
        string variantName,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Removing variant {VariantName} from feature flag {FeatureFlagId}",
                variantName, featureFlagId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {featureFlagId} not found");
            }

            featureFlag.RemoveVariant(variantName);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully removed variant {VariantName} from feature flag {FeatureFlagId}",
                variantName, featureFlagId);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing variant {VariantName} from feature flag {FeatureFlagId}",
                variantName, featureFlagId);
            throw;
        }
    }

    public async Task<FeatureFlag> ActivateFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Activating feature flag {Id} by user {UserId}", id, modifiedByUserId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            featureFlag.Activate(modifiedByUserId);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully activated feature flag {Id}", id);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating feature flag {Id}", id);
            throw;
        }
    }

    public async Task<FeatureFlag> PauseFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Pausing feature flag {Id} by user {UserId}", id, modifiedByUserId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            featureFlag.Pause(modifiedByUserId);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully paused feature flag {Id}", id);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing feature flag {Id}", id);
            throw;
        }
    }

    public async Task<FeatureFlag> CompleteFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Completing feature flag {Id} by user {UserId}", id, modifiedByUserId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            featureFlag.Complete(modifiedByUserId);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully completed feature flag {Id}", id);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing feature flag {Id}", id);
            throw;
        }
    }

    public async Task<FeatureFlag> ArchiveFeatureFlagAsync(
        Guid id,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Archiving feature flag {Id} by user {UserId}", id, modifiedByUserId);

            var featureFlag = await _featureFlagRepository.GetByIdAsync(id, cancellationToken);
            if (featureFlag == null)
            {
                throw new ArgumentException($"Feature flag with ID {id} not found");
            }

            featureFlag.Archive(modifiedByUserId);

            var updatedFeatureFlag = await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache
            await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);

            _logger.LogInformation("Successfully archived feature flag {Id}", id);
            return updatedFeatureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving feature flag {Id}", id);
            throw;
        }
    }

    public async Task<bool> IsEnabledAsync(
        string key,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if feature flag {Key} is enabled for user {UserId}", key, userId);

            var featureFlag = await GetFeatureFlagAsync(key, environment, cancellationToken);
            if (featureFlag == null)
            {
                _logger.LogDebug("Feature flag {Key} not found, returning false", key);
                return false;
            }

            var isEnabled = featureFlag.IsEnabledForUser(userId, context);

            // Record usage
            if (isEnabled)
            {
                await RecordUsageAsync(key, userId, null, context, environment, cancellationToken);
            }

            _logger.LogDebug("Feature flag {Key} is {Status} for user {UserId}", key, isEnabled ? "enabled" : "disabled", userId);
            return isEnabled;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if feature flag {Key} is enabled for user {UserId}", key, userId);
            return false; // Fail safe - return false on error
        }
    }

    public async Task<T?> GetValueAsync<T>(
        string key,
        Guid userId,
        T? defaultValue = default,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting value for feature flag {Key} for user {UserId}", key, userId);

            var featureFlag = await GetFeatureFlagAsync(key, environment, cancellationToken);
            if (featureFlag == null)
            {
                _logger.LogDebug("Feature flag {Key} not found, returning default value", key);
                return defaultValue;
            }

            var value = featureFlag.GetValueForUser(userId, context);

            // Record usage
            if (featureFlag.IsEnabledForUser(userId, context))
            {
                var variant = featureFlag.GetVariantForUser(userId, context)?.Name;
                await RecordUsageAsync(key, userId, variant, context, environment, cancellationToken);
            }

            if (value is T typedValue)
            {
                return typedValue;
            }

            // Try to convert the value
            try
            {
                if (value != null)
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
            }
            catch (Exception conversionEx)
            {
                _logger.LogWarning(conversionEx, "Failed to convert feature flag value to type {Type}", typeof(T).Name);
            }

            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting value for feature flag {Key} for user {UserId}", key, userId);
            return defaultValue; // Fail safe - return default value on error
        }
    }

    public async Task<string?> GetVariantAsync(
        string key,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting variant for feature flag {Key} for user {UserId}", key, userId);

            var featureFlag = await GetFeatureFlagAsync(key, environment, cancellationToken);
            if (featureFlag == null)
            {
                _logger.LogDebug("Feature flag {Key} not found, returning null", key);
                return null;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);

            // Record usage
            if (variant != null)
            {
                await RecordUsageAsync(key, userId, variant.Name, context, environment, cancellationToken);
            }

            _logger.LogDebug("Feature flag {Key} assigned variant {Variant} for user {UserId}", key, variant?.Name, userId);
            return variant?.Name;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variant for feature flag {Key} for user {UserId}", key, userId);
            return null; // Fail safe - return null on error
        }
    }

    public async Task<Dictionary<string, object?>> GetMultipleValuesAsync(
        List<string> keys,
        Guid userId,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting multiple feature flag values for user {UserId}", userId);

            var results = new Dictionary<string, object?>();

            foreach (var key in keys)
            {
                var value = await GetValueAsync<object>(key, userId, null, context, environment, cancellationToken);
                results[key] = value;
            }

            _logger.LogDebug("Retrieved {Count} feature flag values for user {UserId}", results.Count, userId);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple feature flag values for user {UserId}", userId);
            return new Dictionary<string, object?>();
        }
    }

    public async Task RecordUsageAsync(
        string key,
        Guid userId,
        string? variant = null,
        Dictionary<string, object>? context = null,
        string? environment = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Recording usage for feature flag {Key} for user {UserId}", key, userId);

            var featureFlag = await GetFeatureFlagAsync(key, environment, cancellationToken);
            if (featureFlag == null)
            {
                _logger.LogWarning("Cannot record usage for non-existent feature flag {Key}", key);
                return;
            }

            featureFlag.RecordUsage(userId, variant, context);
            await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

            // Invalidate cache to ensure fresh data
            await InvalidateFeatureFlagCacheAsync(key, environment, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording usage for feature flag {Key} for user {UserId}", key, userId);
            // Don't throw - usage recording should not break the main flow
        }
    }

    public async Task<FeatureFlagMetrics> GetMetricsAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting metrics for feature flag {FeatureFlagId}", featureFlagId);

            var metrics = await _analyticsService.CalculateMetricsAsync(featureFlagId, startDate, endDate, cancellationToken);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics for feature flag {FeatureFlagId}", featureFlagId);
            return FeatureFlagMetrics.Empty();
        }
    }

    public async Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(
        Guid featureFlagId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int? limit = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting usage history for feature flag {FeatureFlagId}", featureFlagId);

            var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, startDate, endDate, limit, cancellationToken);
            return usageHistory;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history for feature flag {FeatureFlagId}", featureFlagId);
            return new List<FeatureFlagUsage>();
        }
    }

    public async Task BulkUpdateRolloutAsync(
        List<Guid> featureFlagIds,
        decimal percentage,
        Guid modifiedByUserId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Bulk updating rollout percentage to {Percentage}% for {Count} feature flags",
                percentage, featureFlagIds.Count);

            foreach (var featureFlagId in featureFlagIds)
            {
                var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId, cancellationToken);
                if (featureFlag != null)
                {
                    var newRolloutConfig = new RolloutConfiguration(
                        percentage,
                        featureFlag.RolloutConfiguration.Strategy,
                        featureFlag.RolloutConfiguration.WhitelistedUsers,
                        featureFlag.RolloutConfiguration.BlacklistedUsers,
                        featureFlag.RolloutConfiguration.WhitelistedGroups,
                        featureFlag.RolloutConfiguration.BlacklistedGroups,
                        featureFlag.RolloutConfiguration.CustomRules);

                    featureFlag.UpdateRolloutConfiguration(newRolloutConfig);
                    await _featureFlagRepository.UpdateAsync(featureFlag, cancellationToken);

                    // Invalidate cache
                    await InvalidateFeatureFlagCacheAsync(featureFlag.Key, featureFlag.Environment, cancellationToken);
                }
            }

            _logger.LogInformation("Successfully updated rollout percentage for {Count} feature flags", featureFlagIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating rollout percentage");
            throw;
        }
    }

    public async Task<FeatureFlag> CloneFeatureFlagAsync(
        Guid sourceId,
        string targetEnvironment,
        string? newKey = null,
        Guid? createdByUserId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Cloning feature flag {SourceId} to environment {TargetEnvironment}", sourceId, targetEnvironment);

            var sourceFlag = await _featureFlagRepository.GetByIdAsync(sourceId, cancellationToken);
            if (sourceFlag == null)
            {
                throw new ArgumentException($"Source feature flag with ID {sourceId} not found");
            }

            var clonedKey = newKey ?? $"{sourceFlag.Key}_{targetEnvironment}";
            var userId = createdByUserId ?? sourceFlag.CreatedByUserId;

            // Check if cloned key already exists in target environment
            if (await _featureFlagRepository.ExistsAsync(clonedKey, targetEnvironment, cancellationToken))
            {
                throw new InvalidOperationException($"Feature flag with key '{clonedKey}' already exists in environment '{targetEnvironment}'");
            }

            var clonedFlag = FeatureFlag.Create(
                clonedKey,
                $"{sourceFlag.Name} (Cloned)",
                $"Cloned from {sourceFlag.Environment}: {sourceFlag.Description}",
                sourceFlag.Type,
                targetEnvironment,
                userId,
                sourceFlag.StartDate,
                sourceFlag.EndDate,
                sourceFlag.RolloutConfiguration,
                sourceFlag.TargetingRules,
                sourceFlag.DefaultValue,
                sourceFlag.Configuration);

            // Clone variants
            foreach (var variant in sourceFlag.Variants)
            {
                var clonedVariant = new FeatureFlagVariant(
                    variant.Name,
                    variant.Description,
                    variant.TrafficAllocation,
                    variant.Value,
                    variant.Configuration,
                    variant.IsControl);
                clonedFlag.AddVariant(clonedVariant);
            }

            var savedClonedFlag = await _featureFlagRepository.AddAsync(clonedFlag, cancellationToken);

            _logger.LogInformation("Successfully cloned feature flag {SourceId} to {ClonedId}", sourceId, savedClonedFlag.Id);
            return savedClonedFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cloning feature flag {SourceId}", sourceId);
            throw;
        }
    }

    // Private helper methods
    private async Task<T?> GetFromCacheAsync<T>(string key, CancellationToken cancellationToken) where T : class
    {
        try
        {
            var cachedData = await _cache.GetStringAsync(key, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<T>(cachedData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving data from cache for key {Key}", key);
        }

        return null;
    }

    private async Task SetCacheAsync<T>(string key, T data, CancellationToken cancellationToken)
    {
        await SetCacheAsync(key, data, _cacheExpiration, cancellationToken);
    }

    private async Task SetCacheAsync<T>(string key, T data, TimeSpan expiration, CancellationToken cancellationToken)
    {
        try
        {
            var serializedData = JsonSerializer.Serialize(data);
            await _cache.SetStringAsync(key, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error setting cache for key {Key}", key);
        }
    }

    private async Task InvalidateFeatureFlagCacheAsync(string key, string? environment, CancellationToken cancellationToken)
    {
        var keysToInvalidate = new[]
        {
            $"feature_flag_{key}_{environment ?? "default"}",
            $"feature_flags_{environment ?? "all"}_all",
            $"feature_flags_{environment ?? "all"}_Active",
            $"feature_flags_all_all"
        };

        foreach (var cacheKey in keysToInvalidate)
        {
            try
            {
                await _cache.RemoveAsync(cacheKey, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache key {Key}", cacheKey);
            }
        }
    }
}
