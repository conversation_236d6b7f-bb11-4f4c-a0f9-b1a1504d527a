using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface ISettlementRepository
{
    Task<Settlement?> GetByIdAsync(Guid id);
    Task<Settlement?> GetByOrderIdAsync(Guid orderId);
    Task<Settlement?> GetByTripIdAsync(Guid tripId);
    Task<Settlement?> GetBySettlementNumberAsync(string settlementNumber);
    Task<List<Settlement>> GetByCarrierIdAsync(Guid carrierId);
    Task<List<Settlement>> GetByBrokerIdAsync(Guid brokerId);
    Task<List<Settlement>> GetByStatusAsync(SettlementStatus status);
    Task<List<Settlement>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<Settlement> AddAsync(Settlement settlement);
    Task UpdateAsync(Settlement settlement);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<List<Settlement>> GetPendingSettlementsAsync();
    Task<decimal> GetTotalSettlementAmountAsync(Guid participantId, DateTime? from = null, DateTime? to = null);
}
