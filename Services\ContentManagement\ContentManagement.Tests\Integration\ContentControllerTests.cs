using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace ContentManagement.Tests.Integration;

public class ContentControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ContentControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.UseEnvironment("Testing");
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<ContentManagementDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<ContentManagementDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb");
                });

                // Build the service provider
                var sp = services.BuildServiceProvider();

                // Create a scope to obtain a reference to the database context
                using var scope = sp.CreateScope();
                var scopedServices = scope.ServiceProvider;
                var db = scopedServices.GetRequiredService<ContentManagementDbContext>();

                // Ensure the database is created
                db.Database.EnsureCreated();
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetContent_WithValidId_ShouldReturnContent()
    {
        // Arrange
        var contentId = await CreateTestContentAsync();

        // Act
        var response = await _client.GetAsync($"/api/content/{contentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        content.Should().NotBeNull();
        content!.Id.Should().Be(contentId);
        content.Title.Should().Be("Test Content");
    }

    [Fact]
    public async Task GetContent_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/content/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetContentBySlug_WithValidSlug_ShouldReturnContent()
    {
        // Arrange
        var contentId = await CreateTestContentAsync();
        var slug = "test-content";

        // Act
        var response = await _client.GetAsync($"/api/content/slug/{slug}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        content.Should().NotBeNull();
        content!.Id.Should().Be(contentId);
        content.Slug.Should().Be(slug);
    }

    [Fact]
    public async Task SearchContent_WithSearchTerm_ShouldReturnMatchingContent()
    {
        // Arrange
        await CreateTestContentAsync();
        var searchTerm = "Test";

        // Act
        var response = await _client.GetAsync($"/api/content/search?searchTerm={searchTerm}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var searchResult = await response.Content.ReadFromJsonAsync<ContentSearchResultDto>();
        searchResult.Should().NotBeNull();
        searchResult!.Items.Should().NotBeEmpty();
        searchResult.Items.Should().Contain(c => c.Title.Contains(searchTerm));
    }

    [Fact]
    public async Task CreateContent_WithValidData_ShouldCreateContent()
    {
        // Arrange
        var command = new CreateContentCommand
        {
            Title = "New Test Content",
            Slug = "new-test-content",
            Type = ContentType.General,
            ContentBody = "This is new test content body",
            Description = "Test description",
            Tags = new List<string> { "test", "content" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/content", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        content.Should().NotBeNull();
        content!.Title.Should().Be(command.Title);
        content.Slug.Should().Be(command.Slug);
        content.Type.Should().Be(command.Type);
        content.Status.Should().Be(ContentStatus.Draft);
    }

    [Fact]
    public async Task CreateContent_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateContentCommand
        {
            Title = "", // Invalid: empty title
            Slug = "test-slug",
            Type = ContentType.General,
            ContentBody = "Test body"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/content", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task UpdateContent_WithValidData_ShouldUpdateContent()
    {
        // Arrange
        var contentId = await CreateTestContentAsync();
        var command = new UpdateContentCommand
        {
            Title = "Updated Test Content",
            Description = "Updated description",
            ContentBody = "Updated content body",
            Tags = new List<string> { "updated", "test" }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/content/{contentId}", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        content.Should().NotBeNull();
        content!.Title.Should().Be(command.Title);
        content.Description.Should().Be(command.Description);
        content.ContentBody.Should().Be(command.ContentBody);
    }

    [Fact]
    public async Task SubmitForReview_WithValidContent_ShouldChangeStatus()
    {
        // Arrange
        var contentId = await CreateTestContentAsync();

        // Act
        var response = await _client.PostAsync($"/api/content/{contentId}/submit-for-review", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        content.Should().NotBeNull();
        content!.Status.Should().Be(ContentStatus.PendingReview);
    }

    [Fact]
    public async Task GetContentStatistics_ShouldReturnStatistics()
    {
        // Arrange
        await CreateTestContentAsync();

        // Act
        var response = await _client.GetAsync("/api/content/statistics");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var statistics = await response.Content.ReadFromJsonAsync<ContentStatisticsDto>();
        statistics.Should().NotBeNull();
        statistics!.TotalContent.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task DeleteContent_WithValidId_ShouldDeleteContent()
    {
        // Arrange
        var contentId = await CreateTestContentAsync();

        // Act
        var response = await _client.DeleteAsync($"/api/content/{contentId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify content is deleted
        var getResponse = await _client.GetAsync($"/api/content/{contentId}");
        getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    private async Task<Guid> CreateTestContentAsync()
    {
        var command = new CreateContentCommand
        {
            Title = "Test Content",
            Slug = "test-content",
            Type = ContentType.General,
            ContentBody = "This is test content body",
            Description = "Test description",
            Tags = new List<string> { "test", "content" },
            CreatedBy = Guid.NewGuid(),
            CreatedByName = "Test User"
        };

        var response = await _client.PostAsJsonAsync("/api/content", command);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadFromJsonAsync<ContentDto>();
        return content!.Id;
    }
}
