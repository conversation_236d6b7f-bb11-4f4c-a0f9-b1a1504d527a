using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.DTOs;

/// <summary>
/// DTO for Trip Ledger
/// </summary>
public class TripLedgerDto
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string LedgerNumber { get; set; } = string.Empty;
    
    // Financial Summary
    public MoneyDto TotalOrderValue { get; set; } = new();
    public MoneyDto TotalPaid { get; set; } = new();
    public MoneyDto TotalPending { get; set; } = new();
    public MoneyDto TotalCommissions { get; set; } = new();
    public MoneyDto TotalTaxes { get; set; } = new();
    public MoneyDto NetAmount { get; set; } = new();
    public MoneyDto OutstandingBalance { get; set; } = new();
    
    // Status and Dates
    public TripLedgerStatus Status { get; set; }
    public DateTime TripStartDate { get; set; }
    public DateTime? TripEndDate { get; set; }
    public DateTime? SettlementDate { get; set; }
    public DateTime LastUpdated { get; set; }
    
    // Collections
    public List<TripLedgerEntryDto> Entries { get; set; } = new();
    public List<TripLedgerMilestoneDto> Milestones { get; set; } = new();
    public List<TripLedgerAdjustmentDto> Adjustments { get; set; } = new();
    
    // Additional Info
    public string? Notes { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// DTO for Trip Ledger Summary
/// </summary>
public class TripLedgerSummaryDto
{
    public Guid LedgerId { get; set; }
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public string LedgerNumber { get; set; } = string.Empty;
    public string TripNumber { get; set; } = string.Empty;
    public TripLedgerStatus Status { get; set; }
    public MoneyDto TotalOrderValue { get; set; } = new();
    public MoneyDto TotalPaid { get; set; } = new();
    public MoneyDto OutstandingBalance { get; set; } = new();
    public decimal CompletionPercentage { get; set; }
    public decimal PaymentPercentage { get; set; }
    public bool IsOverdue { get; set; }
    public DateTime TripStartDate { get; set; }
    public DateTime? TripEndDate { get; set; }
    public DateTime LastUpdated { get; set; }
    
    // Quick stats
    public int TotalEntries { get; set; }
    public int TotalMilestones { get; set; }
    public int CompletedMilestones { get; set; }
    public int PendingAdjustments { get; set; }
}

/// <summary>
/// DTO for Trip Ledger Entry
/// </summary>
public class TripLedgerEntryDto
{
    public Guid Id { get; set; }
    public TripLedgerEntryType EntryType { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public string Description { get; set; } = string.Empty;
    public DateTime EntryDate { get; set; }
    public string? ReferenceNumber { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public string EntryTypeDisplayName => EntryType.GetDisplayName();
    public bool IsCredit => EntryType.IsCredit();
    public bool IsDebit => EntryType.IsDebit();
}

/// <summary>
/// DTO for Trip Ledger Milestone
/// </summary>
public class TripLedgerMilestoneDto
{
    public Guid Id { get; set; }
    public string MilestoneName { get; set; } = string.Empty;
    public MoneyDto Amount { get; set; } = new();
    public DateTime DueDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
    public DateTime? CompletedDate { get; set; }
    public string? CompletionNotes { get; set; }
    public int SortOrder { get; set; }
    public bool IsOverdue => !IsCompleted && DateTime.UtcNow > DueDate;
    public int DaysUntilDue => (DueDate - DateTime.UtcNow).Days;
}

/// <summary>
/// DTO for Trip Ledger Adjustment
/// </summary>
public class TripLedgerAdjustmentDto
{
    public Guid Id { get; set; }
    public TripLedgerAdjustmentType AdjustmentType { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
    public DateTime AdjustmentDate { get; set; }
    public TripLedgerAdjustmentStatus Status { get; set; }
    public string? ApprovalReference { get; set; }
    public string? RejectionReason { get; set; }
    public bool IsCredit => AdjustmentType == TripLedgerAdjustmentType.Credit;
    public bool RequiresApproval => Status == TripLedgerAdjustmentStatus.PendingApproval;
}

/// <summary>
/// DTO for Trip Ledger Balance
/// </summary>
public class TripLedgerBalanceDto
{
    public Guid LedgerId { get; set; }
    public Guid TripId { get; set; }
    public MoneyDto TotalOrderValue { get; set; } = new();
    public MoneyDto TotalPaid { get; set; } = new();
    public MoneyDto TotalPending { get; set; } = new();
    public MoneyDto TotalCommissions { get; set; } = new();
    public MoneyDto TotalTaxes { get; set; } = new();
    public MoneyDto NetAmount { get; set; } = new();
    public MoneyDto OutstandingBalance { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    
    // Calculated fields
    public decimal PaymentPercentage => TotalOrderValue.Amount > 0 ? 
        TotalPaid.Amount / TotalOrderValue.Amount * 100 : 0;
    public bool IsFullyPaid => OutstandingBalance.Amount <= 0;
    public bool HasOutstanding => OutstandingBalance.Amount > 0;
}

/// <summary>
/// Request DTO for creating Trip Ledger
/// </summary>
public class CreateTripLedgerDto
{
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public MoneyDto TotalOrderValue { get; set; } = new();
    public DateTime TripStartDate { get; set; }
    public string? Notes { get; set; }
}

/// <summary>
/// Request DTO for adding Trip Ledger Entry
/// </summary>
public class AddTripLedgerEntryDto
{
    public TripLedgerEntryType EntryType { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public string Description { get; set; } = string.Empty;
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Request DTO for adding Trip Ledger Milestone
/// </summary>
public class AddTripLedgerMilestoneDto
{
    public string MilestoneName { get; set; } = string.Empty;
    public MoneyDto Amount { get; set; } = new();
    public DateTime DueDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool IsCompleted { get; set; } = false;
    public DateTime? CompletedDate { get; set; }
}

/// <summary>
/// Request DTO for completing milestone
/// </summary>
public class CompleteMilestoneDto
{
    public DateTime CompletedDate { get; set; } = DateTime.UtcNow;
    public string? Notes { get; set; }
}

/// <summary>
/// Request DTO for adding Trip Ledger Adjustment
/// </summary>
public class AddTripLedgerAdjustmentDto
{
    public TripLedgerAdjustmentType AdjustmentType { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
    public Guid AdjustedBy { get; set; }
    public string? ApprovalReference { get; set; }
}

/// <summary>
/// Filter DTO for Trip Ledgers
/// </summary>
public class TripLedgerFilterDto
{
    public TripLedgerStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool? IsOverdue { get; set; }
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string SortBy { get; set; } = "LastUpdated";
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// Filter DTO for Trip Ledger Entries
/// </summary>
public class TripLedgerEntryFilterDto
{
    public List<TripLedgerEntryType> EntryTypes { get; set; } = new();
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string SortBy { get; set; } = "EntryDate";
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// DTO for Trip Ledger Analytics
/// </summary>
public class TripLedgerAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Summary metrics
    public int TotalLedgers { get; set; }
    public int ActiveLedgers { get; set; }
    public int CompletedLedgers { get; set; }
    public int SettledLedgers { get; set; }
    public int OverdueLedgers { get; set; }
    
    // Financial metrics
    public MoneyDto TotalOrderValue { get; set; } = new();
    public MoneyDto TotalPaid { get; set; } = new();
    public MoneyDto TotalOutstanding { get; set; } = new();
    public MoneyDto TotalCommissions { get; set; } = new();
    public MoneyDto TotalTaxes { get; set; } = new();
    
    // Performance metrics
    public decimal AveragePaymentPercentage { get; set; }
    public decimal OnTimePaymentRate { get; set; }
    public double AverageSettlementDays { get; set; }
    
    // Trends
    public List<TripLedgerTrendDataDto> MonthlyTrends { get; set; } = new();
    public List<TripLedgerCategoryBreakdownDto> CategoryBreakdown { get; set; } = new();
}

/// <summary>
/// DTO for Trip Ledger Trend Data
/// </summary>
public class TripLedgerTrendDataDto
{
    public DateTime Date { get; set; }
    public int LedgerCount { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public MoneyDto TotalPaid { get; set; } = new();
    public MoneyDto TotalOutstanding { get; set; } = new();
    public decimal PaymentPercentage { get; set; }
}

/// <summary>
/// DTO for Trip Ledger Category Breakdown
/// </summary>
public class TripLedgerCategoryBreakdownDto
{
    public string Category { get; set; } = string.Empty;
    public int Count { get; set; }
    public MoneyDto TotalAmount { get; set; } = new();
    public decimal Percentage { get; set; }
}

/// <summary>
/// Export format enum
/// </summary>
public enum ExportFormat
{
    Excel = 0,
    PDF = 1,
    CSV = 2,
    JSON = 3
}
