namespace OrderManagement.Domain.ValueObjects;

public record CustomerDetails
{
    public string CompanyName { get; init; } = string.Empty;
    public string Contact<PERSON>erson { get; init; } = string.Empty;
    public string ContactEmail { get; init; } = string.Empty;
    public string ContactPhone { get; init; } = string.Empty;
    public Address BillingAddress { get; init; } = null!;
    public string? TaxId { get; init; }
    public string? CustomerReference { get; init; }
    public string? PurchaseOrderNumber { get; init; }

    // Parameterless constructor for EF Core
    public CustomerDetails() { }

    public CustomerDetails(
        string companyName,
        string contactPerson,
        string contactEmail,
        string contactPhone,
        Address billingAddress,
        string? taxId = null,
        string? customerReference = null,
        string? purchaseOrderNumber = null)
    {
        if (string.IsNullOrWhiteSpace(companyName))
            throw new ArgumentException("Company name cannot be empty", nameof(companyName));

        if (string.IsNullOrWhiteSpace(contactPerson))
            throw new ArgumentException("Contact person cannot be empty", nameof(contactPerson));

        if (string.IsNullOrWhiteSpace(contactEmail))
            throw new ArgumentException("Contact email cannot be empty", nameof(contactEmail));

        if (string.IsNullOrWhiteSpace(contactPhone))
            throw new ArgumentException("Contact phone cannot be empty", nameof(contactPhone));

        CompanyName = companyName;
        ContactPerson = contactPerson;
        ContactEmail = contactEmail;
        ContactPhone = contactPhone;
        BillingAddress = billingAddress ?? throw new ArgumentNullException(nameof(billingAddress));
        TaxId = taxId;
        CustomerReference = customerReference;
        PurchaseOrderNumber = purchaseOrderNumber;
    }
}

public record BillingDetails
{
    public string BillingMethod { get; init; } = string.Empty; // Credit, Net30, COD, etc.
    public int PaymentTermsDays { get; init; }
    public string? PaymentInstructions { get; init; }
    public bool RequiresPurchaseOrder { get; init; }
    public string? PreferredInvoiceFormat { get; init; }
    public string? BillingContact { get; init; }
    public string? BillingEmail { get; init; }

    // Parameterless constructor for EF Core
    public BillingDetails() { }

    public BillingDetails(
        string billingMethod,
        int paymentTermsDays,
        string? paymentInstructions = null,
        bool requiresPurchaseOrder = false,
        string? preferredInvoiceFormat = null,
        string? billingContact = null,
        string? billingEmail = null)
    {
        if (string.IsNullOrWhiteSpace(billingMethod))
            throw new ArgumentException("Billing method cannot be empty", nameof(billingMethod));

        if (paymentTermsDays < 0)
            throw new ArgumentException("Payment terms days cannot be negative", nameof(paymentTermsDays));

        BillingMethod = billingMethod;
        PaymentTermsDays = paymentTermsDays;
        PaymentInstructions = paymentInstructions;
        RequiresPurchaseOrder = requiresPurchaseOrder;
        PreferredInvoiceFormat = preferredInvoiceFormat;
        BillingContact = billingContact;
        BillingEmail = billingEmail;
    }
}
