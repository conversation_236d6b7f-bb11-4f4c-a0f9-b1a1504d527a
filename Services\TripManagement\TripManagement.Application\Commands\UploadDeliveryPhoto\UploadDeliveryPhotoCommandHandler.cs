using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.UploadDeliveryPhoto;

public class UploadDeliveryPhotoCommandHandler : IRequestHandler<UploadDeliveryPhotoCommand, string>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<UploadDeliveryPhotoCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public UploadDeliveryPhotoCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        IFileStorageService fileStorageService,
        ILogger<UploadDeliveryPhotoCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _fileStorageService = fileStorageService;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<string> Handle(UploadDeliveryPhotoCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Uploading delivery photo for trip stop {TripStopId} by driver {DriverId}", 
                request.TripStopId, request.DriverId);

            // Find the trip stop
            var tripStop = await _tripRepository.GetTripStopByIdAsync(request.TripStopId, cancellationToken);
            if (tripStop == null)
            {
                _logger.LogWarning("Trip stop {TripStopId} not found", request.TripStopId);
                throw new ArgumentException("Trip stop not found");
            }

            // Verify the driver is assigned to this trip
            var trip = await _tripRepository.GetByIdAsync(tripStop.TripId, cancellationToken);
            if (trip == null || trip.DriverId != request.DriverId)
            {
                _logger.LogWarning("Driver {DriverId} is not assigned to trip {TripId}", 
                    request.DriverId, tripStop.TripId);
                throw new UnauthorizedAccessException("Driver not authorized for this trip");
            }

            // Validate photo file
            if (request.Photo == null || request.Photo.Length == 0)
            {
                throw new ArgumentException("Photo file is required");
            }

            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/webp" };
            if (!allowedTypes.Contains(request.Photo.ContentType.ToLower()))
            {
                throw new ArgumentException("Only JPEG, PNG, and WebP images are allowed");
            }

            const int maxFileSize = 10 * 1024 * 1024; // 10MB
            if (request.Photo.Length > maxFileSize)
            {
                throw new ArgumentException("Photo file size cannot exceed 10MB");
            }

            // Upload photo to storage
            var fileName = $"delivery_photo_{request.TripStopId}_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}.jpg";
            
            using var stream = request.Photo.OpenReadStream();
            var photoUrl = await _fileStorageService.UploadFileAsync(
                stream, 
                fileName, 
                request.Photo.ContentType, 
                cancellationToken: cancellationToken);

            // Create trip document for the photo
            var tripDocument = new TripDocument(
                tripId: trip.Id,
                documentType: DocumentType.Photo,
                fileName: fileName,
                fileUrl: photoUrl,
                description: request.Description ?? "Delivery photo",
                uploadedBy: request.DriverId.ToString());

            trip.AddDocument(tripDocument);

            // Update existing POD or create new one with photo
            var existingPod = tripStop.ProofOfDeliveries.FirstOrDefault();
            if (existingPod != null)
            {
                // Update existing POD with photo URL
                existingPod.UpdatePhoto(photoUrl);
            }
            else
            {
                // Create new POD with photo
                var proofOfDelivery = new ProofOfDelivery(
                    tripStopId: request.TripStopId,
                    recipientName: "Photo Documentation",
                    photoUrl: photoUrl,
                    notes: request.Description,
                    deliveredBy: request.DriverId.ToString());

                tripStop.AddProofOfDelivery(proofOfDelivery);
            }

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.photo_uploaded", new
            {
                TripId = trip.Id,
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                PhotoUrl = photoUrl,
                UploadedAt = DateTime.UtcNow,
                Description = request.Description
            }, cancellationToken);

            _logger.LogInformation("Delivery photo uploaded successfully for trip stop {TripStopId}. URL: {PhotoUrl}", 
                request.TripStopId, photoUrl);

            return photoUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading delivery photo for trip stop {TripStopId}", 
                request.TripStopId);
            throw;
        }
    }
}
