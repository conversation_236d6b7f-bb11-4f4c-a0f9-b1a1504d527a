using MediatR;
using TripManagement.Domain.Entities;

namespace TripManagement.Application.Commands.SubmitTripFeedback;

public record SubmitTripFeedbackCommand : IRequest<SubmitTripFeedbackResponse>
{
    public Guid TripId { get; init; }
    public Guid ReviewerId { get; init; }
    public string ReviewerRole { get; init; } = string.Empty;
    public Guid RevieweeId { get; init; }
    public string RevieweeRole { get; init; } = string.Empty;
    
    // Ratings (1-5 scale)
    public decimal OverallRating { get; init; }
    public decimal ServiceQualityRating { get; init; }
    public decimal TimelinessRating { get; init; }
    public decimal CommunicationRating { get; init; }
    public decimal ProfessionalismRating { get; init; }
    public decimal VehicleConditionRating { get; init; }
    public decimal CargoHandlingRating { get; init; }
    
    // Feedback content
    public string Comments { get; init; } = string.Empty;
    public List<string> PositiveAspects { get; init; } = new();
    public List<string> ImprovementAreas { get; init; } = new();
    public List<string> Tags { get; init; } = new();
    
    // Options
    public FeedbackType FeedbackType { get; init; } = FeedbackType.PostTrip;
    public bool IsAnonymous { get; init; } = false;
    public bool NotifyReviewee { get; init; } = true;
    public bool RequestFollowUp { get; init; } = false;
    
    // Additional data
    public Dictionary<string, object> Metadata { get; init; } = new();
}

public record SubmitTripFeedbackResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public Guid? FeedbackId { get; init; }
    public FeedbackAnalysisResult? Analysis { get; init; }
    public List<string> Alerts { get; init; } = new();
    public List<string> Recommendations { get; init; } = new();
    public DateTime SubmittedAt { get; init; }
}

public record FeedbackAnalysisResult
{
    public bool IsPositiveFeedback { get; init; }
    public bool RequiresAttention { get; init; }
    public bool HasRedFlags { get; init; }
    public List<string> IdentifiedIssues { get; init; } = new();
    public List<string> PositiveHighlights { get; init; } = new();
    public decimal SentimentScore { get; init; }
    public string SentimentCategory { get; init; } = string.Empty; // "Positive", "Neutral", "Negative"
    public List<string> SuggestedActions { get; init; } = new();
}
