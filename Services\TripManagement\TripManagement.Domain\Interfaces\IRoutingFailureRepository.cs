using TripManagement.Domain.Entities;
using Shared.Domain.Common;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.Interfaces;

/// <summary>
/// Repository interface for RoutingFailure entity
/// </summary>
public interface IRoutingFailureRepository
{
    // Basic CRUD operations
    Task<RoutingFailure?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoutingFailure>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RoutingFailure>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoutingFailure>> GetActiveFailuresAsync(CancellationToken cancellationToken = default);
    
    // Filtering and pagination
    Task<(IEnumerable<RoutingFailure> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<FailureType>? failureTypes = null,
        List<FailureSeverity>? severities = null,
        List<FailureStatus>? statuses = null,
        List<string>? categories = null,
        bool? isRecurring = null,
        decimal? minImpactCost = null,
        decimal? maxImpactCost = null,
        CancellationToken cancellationToken = default);
    
    // Report-specific queries
    Task<IEnumerable<RoutingFailure>> GetFailuresForReportAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<FailureType>? failureTypes = null,
        List<FailureSeverity>? severities = null,
        List<FailureStatus>? statuses = null,
        bool includeResolved = true,
        CancellationToken cancellationToken = default);
    
    // Pattern analysis queries
    Task<IEnumerable<RoutingFailure>> GetRecurringFailuresAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<FailureType, int>> GetFailureCountsByTypeAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<Dictionary<FailureSeverity, int>> GetFailureCountsBySeverityAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetFailureCountsByCategoryAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    
    // Impact analysis queries
    Task<decimal> GetTotalImpactCostAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<TimeSpan> GetTotalDelayTimeAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<int> GetTotalAffectedCustomersAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    
    // Resolution analysis queries
    Task<double> GetAverageResolutionTimeHoursAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<decimal> GetResolutionRateAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoutingFailure>> GetLongRunningFailuresAsync(int hoursThreshold = 4, CancellationToken cancellationToken = default);
    
    // Trend analysis queries
    Task<Dictionary<DateTime, int>> GetDailyFailureCountsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<Dictionary<DateTime, int>> GetDailyResolutionCountsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    
    // Root cause analysis queries
    Task<Dictionary<string, int>> GetRootCauseFrequencyAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoutingFailure>> GetFailuresByRootCauseAsync(string rootCause, CancellationToken cancellationToken = default);
    
    // Existence checks
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> HasActiveFailuresForTripAsync(Guid tripId, CancellationToken cancellationToken = default);
    
    // Modification operations
    void Add(RoutingFailure routingFailure);
    void Update(RoutingFailure routingFailure);
    void Delete(RoutingFailure routingFailure);
    
    // Bulk operations
    Task<IEnumerable<RoutingFailure>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    
    // Statistics
    Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetCriticalCountAsync(CancellationToken cancellationToken = default);
}


