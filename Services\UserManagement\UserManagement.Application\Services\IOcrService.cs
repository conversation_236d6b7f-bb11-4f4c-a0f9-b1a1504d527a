using UserManagement.Domain.Entities;

namespace UserManagement.Application.Services
{
    public interface IOcrService
    {
        Task<OcrResult> ExtractDataAsync(string filePath, DocumentType documentType, CancellationToken cancellationToken = default);
        Task<OcrResult> ExtractDataFromStreamAsync(Stream fileStream, string fileName, DocumentType documentType, CancellationToken cancellationToken = default);
        Task<bool> IsDocumentTypeSupported(DocumentType documentType);
        Task<DocumentValidationResult> ValidateDocumentAsync(string filePath, DocumentType documentType, CancellationToken cancellationToken = default);

        // Enhanced OCR methods for advanced processing
        Task<OcrExtractionResult> ExtractDataAsync(string filePath, string documentType, CancellationToken cancellationToken = default);
        Task<OcrComparisonResult> CompareExtractedDataAsync(OcrExtractionResult extractedData, Dictionary<string, string> userProvidedData, string documentType);
        Task<List<OcrValidationIssue>> ValidateDocumentQualityAsync(string filePath, string documentType);
        Task<decimal> CalculateConfidenceScoreAsync(OcrExtractionResult extractedData, Dictionary<string, string> userProvidedData);
    }

    public class OcrResult
    {
        public bool Success { get; set; }
        public string ExtractedText { get; set; } = string.Empty;
        public Dictionary<string, object> ExtractedData { get; set; } = new();
        public decimal ConfidenceScore { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public List<string> Warnings { get; set; } = new();
        public DocumentValidationResult ValidationResult { get; set; } = new();
    }

    public class DocumentValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationError> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> ExtractedFields { get; set; } = new();
        public decimal ConfidenceScore { get; set; }
        public List<ValidationIssue> ValidatedFields { get; set; } = new();
    }

    public class ValidationError
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }

    public class ValidationIssue
    {
        public string FieldName { get; set; } = string.Empty;
        public string ExtractedValue { get; set; } = string.Empty;
        public string UserProvidedValue { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
        public string MatchType { get; set; } = string.Empty;
    }

    public class DocumentQualityMetrics
    {
        public decimal OverallScore { get; set; }
        public decimal Clarity { get; set; }
        public decimal Brightness { get; set; }
        public decimal Contrast { get; set; }
        public decimal Resolution { get; set; }
        public decimal ColorDepth { get; set; }
        public long FileSize { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public enum DocumentField
    {
        // Aadhar Card
        AadharNumber,
        AadharName,
        AadharAddress,
        AadharDateOfBirth,
        AadharGender,

        // PAN Card
        PanNumber,
        PanName,
        PanFatherName,
        PanDateOfBirth,

        // Driving License
        LicenseNumber,
        LicenseName,
        LicenseAddress,
        LicenseIssueDate,
        LicenseExpiryDate,
        LicenseVehicleClass,

        // GST Certificate
        GstNumber,
        GstBusinessName,
        GstAddress,
        GstRegistrationDate,

        // Vehicle Registration
        VehicleNumber,
        VehicleOwnerName,
        VehicleModel,
        VehicleEngineNumber,
        VehicleChassisNumber,
        VehicleRegistrationDate,

        // Vehicle Insurance
        PolicyNumber,
        InsuredName,
        VehicleNumberInsurance,
        PolicyStartDate,
        PolicyEndDate,
        InsuranceCompany
    }

    // Enhanced OCR classes for advanced processing
    public class OcrExtractionResult
    {
        public Dictionary<string, string> Fields { get; set; } = new();
        public List<string> ExtractedText { get; set; } = new();
        public decimal Confidence { get; set; }
        public string Engine { get; set; }
        public DateTime ProcessedAt { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class OcrComparisonResult
    {
        public string DocumentType { get; set; }
        public decimal OverallConfidence { get; set; }
        public Dictionary<string, OcrFieldComparison> FieldComparisons { get; set; } = new();
        public List<string> ExtractedText { get; set; } = new();
        public List<OcrValidationIssue> ValidationIssues { get; set; } = new();
        public bool PassedValidation { get; set; }
        public string ProcessingEngine { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class OcrFieldComparison
    {
        public string FieldName { get; set; }
        public string ExtractedValue { get; set; }
        public string UserProvidedValue { get; set; }
        public bool IsMatch { get; set; }
        public decimal Confidence { get; set; }
        public string MatchType { get; set; } // Exact, Fuzzy, NoMatch
        public List<string> Suggestions { get; set; } = new();
    }

    public class OcrValidationIssue
    {
        public string IssueType { get; set; } // DocumentQuality, TextClarity, MissingField, InvalidFormat
        public string Description { get; set; }
        public string Severity { get; set; } // Low, Medium, High, Critical
        public string FieldName { get; set; }
        public string Recommendation { get; set; }
    }
}
