using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Admin;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.API.Controllers;


/// <summary>
/// Admin analytics controller for platform-wide analytics and business intelligence
/// </summary>
[ApiController]
[Route("api/admin/analytics")]
[Authorize(Roles = "Admin")]
public class AdminAnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AdminAnalyticsController> _logger;

    public AdminAnalyticsController(IMediator mediator, ILogger<AdminAnalyticsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get admin platform dashboard with comprehensive analytics
    /// </summary>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Admin platform dashboard data</returns>
    [HttpGet("dashboard")]
    public async Task<ActionResult<AdminPlatformDashboardDto>> GetAdminDashboard(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetAdminPlatformDashboardQuery(fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin dashboard");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get platform performance metrics
    /// </summary>
    /// <param name="fromDate">Start date for metrics</param>
    /// <param name="toDate">End date for metrics</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Platform performance metrics</returns>
    [HttpGet("performance")]
    public async Task<ActionResult<PlatformPerformanceMetricsDto>> GetPlatformPerformance(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetPlatformPerformanceMetricsQuery(fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting platform performance metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get real-time KPI monitoring data
    /// </summary>
    /// <returns>Real-time KPI monitoring data</returns>
    [HttpGet("kpis/realtime")]
    public async Task<ActionResult<RealTimeKPIMonitoringDto>> GetRealTimeKPIs()
    {
        try
        {
            var query = new GetRealTimeKPIMonitoringQuery();
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time KPIs");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get revenue analytics
    /// </summary>
    /// <param name="fromDate">Start date for revenue analysis</param>
    /// <param name="toDate">End date for revenue analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeSubscriptionBreakdown">Include subscription breakdown</param>
    /// <param name="includeChurnAnalysis">Include churn analysis</param>
    /// <returns>Revenue analytics data</returns>
    [HttpGet("revenue")]
    public async Task<ActionResult<RevenueAnalyticsDto>> GetRevenueAnalytics(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeSubscriptionBreakdown = true,
        [FromQuery] bool includeChurnAnalysis = true)
    {
        try
        {
            var query = new GetRevenueAnalyticsQuery(fromDate, toDate, period, includeSubscriptionBreakdown, includeChurnAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting revenue analytics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user growth tracking analytics
    /// </summary>
    /// <param name="fromDate">Start date for user growth analysis</param>
    /// <param name="toDate">End date for user growth analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="userType">Filter by user type</param>
    /// <returns>User growth tracking data</returns>
    [HttpGet("users/growth")]
    public async Task<ActionResult<UserGrowthTrackingDto>> GetUserGrowthTracking(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] UserType? userType = null)
    {
        try
        {
            var query = new GetUserGrowthTrackingQuery(fromDate, toDate, period, userType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user growth tracking");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get geographic performance analysis
    /// </summary>
    /// <param name="fromDate">Start date for geographic analysis</param>
    /// <param name="toDate">End date for geographic analysis</param>
    /// <param name="region">Filter by region</param>
    /// <param name="country">Filter by country</param>
    /// <param name="state">Filter by state</param>
    /// <returns>Geographic performance data</returns>
    [HttpGet("geographic")]
    public async Task<ActionResult<GeographicPerformanceDto>> GetGeographicPerformance(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? region = null,
        [FromQuery] string? country = null,
        [FromQuery] string? state = null)
    {
        try
        {
            var query = new GetGeographicPerformanceQuery(fromDate, toDate, region, country, state);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geographic performance");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get business intelligence reports
    /// </summary>
    /// <param name="fromDate">Start date for reports</param>
    /// <param name="toDate">End date for reports</param>
    /// <param name="reportType">Filter by report type</param>
    /// <param name="includeComplianceReports">Include compliance reports</param>
    /// <returns>List of business intelligence reports</returns>
    [HttpGet("reports")]
    public async Task<ActionResult<List<BusinessIntelligenceReportDto>>> GetBusinessIntelligenceReports(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] ReportType? reportType = null,
        [FromQuery] bool includeComplianceReports = true)
    {
        try
        {
            var query = new GetBusinessIntelligenceReportsQuery(fromDate, toDate, reportType, includeComplianceReports);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting business intelligence reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get customer NPS tracking
    /// </summary>
    /// <param name="fromDate">Start date for NPS tracking</param>
    /// <param name="toDate">End date for NPS tracking</param>
    /// <param name="userType">Filter by user type</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Customer NPS tracking data</returns>
    [HttpGet("nps")]
    public async Task<ActionResult<CustomerNPSTrackingDto>> GetCustomerNPSTracking(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] UserType? userType = null,
        [FromQuery] TimePeriod period = TimePeriod.Monthly)
    {
        try
        {
            var query = new GetCustomerNPSTrackingQuery(fromDate, toDate, userType, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer NPS tracking");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get subscription analytics
    /// </summary>
    /// <param name="fromDate">Start date for subscription analysis</param>
    /// <param name="toDate">End date for subscription analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeTierDistribution">Include tier distribution</param>
    /// <param name="includeConversionAnalysis">Include conversion analysis</param>
    /// <returns>Subscription analytics data</returns>
    [HttpGet("subscriptions")]
    public async Task<ActionResult<SubscriptionAnalyticsDto>> GetSubscriptionAnalytics(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeTierDistribution = true,
        [FromQuery] bool includeConversionAnalysis = true)
    {
        try
        {
            var query = new GetSubscriptionAnalyticsQuery(fromDate, toDate, period, includeTierDistribution, includeConversionAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription analytics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get platform usage reports
    /// </summary>
    /// <param name="fromDate">Start date for usage analysis</param>
    /// <param name="toDate">End date for usage analysis</param>
    /// <param name="userType">Filter by user type</param>
    /// <param name="dataSource">Filter by data source</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Platform usage report data</returns>
    [HttpGet("usage")]
    public async Task<ActionResult<PlatformUsageReportDto>> GetPlatformUsageReports(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] UserType? userType = null,
        [FromQuery] DataSourceType? dataSource = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetPlatformUsageReportsQuery(fromDate, toDate, userType, dataSource, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting platform usage reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get regulatory compliance reports
    /// </summary>
    /// <param name="fromDate">Start date for compliance analysis</param>
    /// <param name="toDate">End date for compliance analysis</param>
    /// <param name="complianceType">Filter by compliance type</param>
    /// <param name="region">Filter by region</param>
    /// <returns>List of regulatory compliance reports</returns>
    [HttpGet("compliance")]
    public async Task<ActionResult<List<RegulatoryComplianceReportDto>>> GetRegulatoryComplianceReports(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? complianceType = null,
        [FromQuery] string? region = null)
    {
        try
        {
            var query = new GetRegulatoryComplianceReportsQuery(fromDate, toDate, complianceType, region);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting regulatory compliance reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get audit trail
    /// </summary>
    /// <param name="fromDate">Start date for audit trail</param>
    /// <param name="toDate">End date for audit trail</param>
    /// <param name="userId">Filter by user ID</param>
    /// <param name="action">Filter by action</param>
    /// <param name="entityType">Filter by entity type</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated audit trail data</returns>
    [HttpGet("audit")]
    public async Task<ActionResult<PagedResult<AuditTrailDto>>> GetAuditTrail(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] Guid? userId = null,
        [FromQuery] string? action = null,
        [FromQuery] string? entityType = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var query = new GetAuditTrailQuery(fromDate, toDate, userId, action, entityType, page, pageSize);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit trail");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get stakeholder presentation data
    /// </summary>
    /// <param name="fromDate">Start date for presentation data</param>
    /// <param name="toDate">End date for presentation data</param>
    /// <param name="includeMetrics">Metrics to include</param>
    /// <param name="includeExecutiveSummary">Include executive summary</param>
    /// <param name="includeFinancialHighlights">Include financial highlights</param>
    /// <param name="includeOperationalMetrics">Include operational metrics</param>
    /// <returns>Stakeholder presentation data</returns>
    [HttpGet("presentation")]
    public async Task<ActionResult<StakeholderPresentationDataDto>> GetStakeholderPresentationData(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] List<string>? includeMetrics = null,
        [FromQuery] bool includeExecutiveSummary = true,
        [FromQuery] bool includeFinancialHighlights = true,
        [FromQuery] bool includeOperationalMetrics = true)
    {
        try
        {
            var query = new GetStakeholderPresentationDataQuery(fromDate, toDate, includeMetrics, includeExecutiveSummary, includeFinancialHighlights, includeOperationalMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stakeholder presentation data");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get transaction volume tracking
    /// </summary>
    /// <param name="fromDate">Start date for transaction tracking</param>
    /// <param name="toDate">End date for transaction tracking</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="dataSource">Filter by data source</param>
    /// <param name="transactionType">Filter by transaction type</param>
    /// <returns>Transaction volume tracking data</returns>
    [HttpGet("transactions/volume")]
    public async Task<ActionResult<TransactionVolumeTrackingDto>> GetTransactionVolumeTracking(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] DataSourceType? dataSource = null,
        [FromQuery] string? transactionType = null)
    {
        try
        {
            var query = new GetTransactionVolumeTrackingQuery(fromDate, toDate, period, dataSource, transactionType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction volume tracking");
            return StatusCode(500, "Internal server error");
        }
    }
}
