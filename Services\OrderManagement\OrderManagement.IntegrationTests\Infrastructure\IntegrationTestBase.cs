using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using OrderManagement.Infrastructure.Persistence;
using System.Net.Http.Headers;
using Xunit;

namespace OrderManagement.IntegrationTests.Infrastructure;

public abstract class IntegrationTestBase : IClassFixture<TestWebApplicationFactory<Program>>, IDisposable
{
    protected readonly TestWebApplicationFactory<Program> Factory;
    protected readonly HttpClient Client;

    protected IntegrationTestBase(TestWebApplicationFactory<Program> factory)
    {
        Factory = factory;

        // Configure the client with test authentication
        Client = Factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                services.AddAuthentication("Test")
                    .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>(
                        "Test", options => { });
            });
        }).CreateClient(new WebApplicationFactoryClientOptions
        {
            AllowAutoRedirect = false
        });

        // Set up authorization header
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Test");
    }

    protected async Task<OrderManagementDbContext> GetDbContextAsync()
    {
        return await Factory.GetDbContextAsync();
    }

    protected async Task SeedDatabaseAsync()
    {
        await Factory.SeedDatabaseAsync();
    }

    public void Dispose()
    {
        Client?.Dispose();
        GC.SuppressFinalize(this);
    }
}
