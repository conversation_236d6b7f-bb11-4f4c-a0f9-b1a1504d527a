using System.Text.Json.Serialization;

namespace Shared.Events;

/// <summary>
/// Base class for carrier user feature events
/// </summary>
public abstract class CarrierUserFeatureEventBase
{
    public Guid EventId { get; set; } = Guid.NewGuid();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Guid CarrierId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Event published when carrier performance metrics are updated
/// </summary>
public class CarrierPerformanceUpdatedEvent : CarrierUserFeatureEventBase
{
    public CarrierPerformanceUpdatedEvent()
    {
        EventType = "CarrierPerformanceUpdated";
    }

    public decimal OnTimeDeliveryPercentage { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalTripsCompleted { get; set; }
    public decimal TotalEarnings { get; set; }
    public DateTime PerformancePeriodStart { get; set; }
    public DateTime PerformancePeriodEnd { get; set; }
    public Dictionary<string, decimal> CategoryRatings { get; set; } = new();
    public List<string> PerformanceHighlights { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

/// <summary>
/// Event published when carrier rating changes significantly
/// </summary>
public class CarrierRatingChangedEvent : CarrierUserFeatureEventBase
{
    public CarrierRatingChangedEvent()
    {
        EventType = "CarrierRatingChanged";
    }

    public decimal CurrentRating { get; set; }
    public decimal PreviousRating { get; set; }
    public decimal RatingChange { get; set; }
    public string RatingCategory { get; set; } = string.Empty;
    public string ChangeType { get; set; } = string.Empty; // Improvement, Decline, Significant
    public int ReviewCount { get; set; }
    public DateTime RatingDate { get; set; }
    public List<string> RecentFeedback { get; set; } = new();
    public bool RequiresNotification { get; set; } = true;
}

/// <summary>
/// Event published when carrier achieves a performance milestone
/// </summary>
public class CarrierMilestoneAchievedEvent : CarrierUserFeatureEventBase
{
    public CarrierMilestoneAchievedEvent()
    {
        EventType = "CarrierMilestoneAchieved";
    }

    public string MilestoneType { get; set; } = string.Empty;
    public string MilestoneName { get; set; } = string.Empty;
    public decimal MilestoneValue { get; set; }
    public string MilestoneUnit { get; set; } = string.Empty;
    public DateTime AchievedDate { get; set; }
    public string AchievementLevel { get; set; } = string.Empty;
    public List<string> Rewards { get; set; } = new();
    public string NextMilestone { get; set; } = string.Empty;
    public decimal? NextMilestoneTarget { get; set; }
}

/// <summary>
/// Event published when carrier document is expiring
/// </summary>
public class CarrierDocumentExpiringEvent : CarrierUserFeatureEventBase
{
    public CarrierDocumentExpiringEvent()
    {
        EventType = "CarrierDocumentExpiring";
    }

    public string DocumentType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public int ThresholdDays { get; set; }
    public Dictionary<string, object> DocumentDetails { get; set; } = new();
    public bool RequiresImmediateAction { get; set; }
}

/// <summary>
/// Event published when carrier trip status changes
/// </summary>
public class CarrierTripStatusChangedEvent : CarrierUserFeatureEventBase
{
    public CarrierTripStatusChangedEvent()
    {
        EventType = "CarrierTripStatusChanged";
    }

    public Guid TripId { get; set; }
    public string TripStatus { get; set; } = string.Empty;
    public string PreviousStatus { get; set; } = string.Empty;
    public DateTime StatusChangeDate { get; set; }
    public string ChangeReason { get; set; } = string.Empty;
    public Dictionary<string, object> TripDetails { get; set; } = new();
    public List<string> MilestonesCompleted { get; set; } = new();
    public bool IsOnTime { get; set; }
    public DateTime? EstimatedDeliveryTime { get; set; }
    public DateTime? ActualDeliveryTime { get; set; }
}

/// <summary>
/// Event published when carrier vehicle status changes
/// </summary>
public class CarrierVehicleStatusChangedEvent : CarrierUserFeatureEventBase
{
    public CarrierVehicleStatusChangedEvent()
    {
        EventType = "CarrierVehicleStatusChanged";
    }

    public Guid VehicleId { get; set; }
    public string VehicleRegistration { get; set; } = string.Empty;
    public string VehicleStatus { get; set; } = string.Empty;
    public string PreviousStatus { get; set; } = string.Empty;
    public DateTime StatusChangeDate { get; set; }
    public string ChangeReason { get; set; } = string.Empty;
    public Dictionary<string, object> VehicleDetails { get; set; } = new();
    public List<string> DocumentsAffected { get; set; } = new();
    public bool RequiresAttention { get; set; }
}

/// <summary>
/// Event published when carrier driver status changes
/// </summary>
public class CarrierDriverStatusChangedEvent : CarrierUserFeatureEventBase
{
    public CarrierDriverStatusChangedEvent()
    {
        EventType = "CarrierDriverStatusChanged";
    }

    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string DriverStatus { get; set; } = string.Empty;
    public string PreviousStatus { get; set; } = string.Empty;
    public DateTime StatusChangeDate { get; set; }
    public string ChangeReason { get; set; } = string.Empty;
    public Dictionary<string, object> DriverDetails { get; set; } = new();
    public List<string> LicensesAffected { get; set; } = new();
    public bool RequiresAttention { get; set; }
}

/// <summary>
/// Event published when carrier earnings are updated
/// </summary>
public class CarrierEarningsUpdatedEvent : CarrierUserFeatureEventBase
{
    public CarrierEarningsUpdatedEvent()
    {
        EventType = "CarrierEarningsUpdated";
    }

    public decimal TotalEarnings { get; set; }
    public decimal PeriodEarnings { get; set; }
    public decimal EarningsChange { get; set; }
    public string EarningsPeriod { get; set; } = string.Empty;
    public DateTime EarningsDate { get; set; }
    public Dictionary<string, decimal> EarningsBreakdown { get; set; } = new();
    public List<string> EarningsSources { get; set; } = new();
    public bool MilestoneReached { get; set; }
    public decimal? NextEarningsMilestone { get; set; }
}

/// <summary>
/// Event published when carrier analytics data is updated
/// </summary>
public class CarrierAnalyticsUpdatedEvent : CarrierUserFeatureEventBase
{
    public CarrierAnalyticsUpdatedEvent()
    {
        EventType = "CarrierAnalyticsUpdated";
    }

    public string AnalyticsType { get; set; } = string.Empty; // Performance, Quoting, Rating, etc.
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }
    public Dictionary<string, object> AnalyticsData { get; set; } = new();
    public List<string> KeyInsights { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public bool RequiresAction { get; set; }
    public string ActionPriority { get; set; } = string.Empty;
}

/// <summary>
/// Event published when carrier feedback is received
/// </summary>
public class CarrierFeedbackReceivedEvent : CarrierUserFeatureEventBase
{
    public CarrierFeedbackReceivedEvent()
    {
        EventType = "CarrierFeedbackReceived";
    }

    public Guid FeedbackId { get; set; }
    public string FeedbackSource { get; set; } = string.Empty; // Broker, Shipper, System
    public string FeedbackType { get; set; } = string.Empty; // Rating, Comment, Complaint
    public decimal? Rating { get; set; }
    public string Comment { get; set; } = string.Empty;
    public string Sentiment { get; set; } = string.Empty; // Positive, Negative, Neutral
    public DateTime FeedbackDate { get; set; }
    public string Category { get; set; } = string.Empty; // Service, Communication, Timeliness
    public Dictionary<string, object> FeedbackDetails { get; set; } = new();
    public bool RequiresResponse { get; set; }
    public bool ImpactsRating { get; set; }
}
