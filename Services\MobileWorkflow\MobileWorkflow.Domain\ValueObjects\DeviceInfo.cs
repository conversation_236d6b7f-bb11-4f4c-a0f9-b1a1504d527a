
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace MobileWorkflow.Domain.ValueObjects;

public class DeviceInfo : ValueObject
{
    public string DeviceId { get; }
    public string DeviceName { get; }
    public string Platform { get; }
    public string OSVersion { get; }
    public string AppVersion { get; }
    public string DeviceModel { get; }
    public string Manufacturer { get; }
    public string ScreenResolution { get; }
    public bool HasBiometrics { get; }
    public bool HasCamera { get; }
    public bool HasGPS { get; }
    public bool HasNFC { get; }
    public bool HasBluetooth { get; }
    public Dictionary<string, object> Capabilities { get; }

    public DeviceInfo(
        string deviceId,
        string deviceName,
        string platform,
        string osVersion,
        string appVersion,
        string deviceModel,
        string manufacturer,
        string screenResolution,
        bool hasBiometrics = false,
        bool hasCamera = false,
        bool hasGPS = false,
        bool hasNFC = false,
        bool hasBluetooth = false,
        Dictionary<string, object>? capabilities = null)
    {
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        DeviceName = deviceName ?? throw new ArgumentNullException(nameof(deviceName));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        OSVersion = osVersion ?? throw new ArgumentNullException(nameof(osVersion));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceModel = deviceModel ?? throw new ArgumentNullException(nameof(deviceModel));
        Manufacturer = manufacturer ?? throw new ArgumentNullException(nameof(manufacturer));
        ScreenResolution = screenResolution ?? throw new ArgumentNullException(nameof(screenResolution));
        HasBiometrics = hasBiometrics;
        HasCamera = hasCamera;
        HasGPS = hasGPS;
        HasNFC = hasNFC;
        HasBluetooth = hasBluetooth;
        Capabilities = capabilities ?? new Dictionary<string, object>();
    }

    public bool SupportsFeature(string feature)
    {
        return feature.ToLower() switch
        {
            "biometrics" => HasBiometrics,
            "camera" => HasCamera,
            "gps" => HasGPS,
            "nfc" => HasNFC,
            "bluetooth" => HasBluetooth,
            _ => Capabilities.ContainsKey(feature) && Convert.ToBoolean(Capabilities[feature])
        };
    }

    public bool IsCompatibleWith(string minOSVersion, string minAppVersion)
    {
        // Simple version comparison - in real implementation, use proper version comparison
        return string.Compare(OSVersion, minOSVersion, StringComparison.OrdinalIgnoreCase) >= 0 &&
               string.Compare(AppVersion, minAppVersion, StringComparison.OrdinalIgnoreCase) >= 0;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return DeviceId;
        yield return Platform;
        yield return OSVersion;
        yield return AppVersion;
        yield return DeviceModel;
        yield return Manufacturer;
    }

    public override string ToString()
    {
        return $"{DeviceName} ({Platform} {OSVersion}) - App {AppVersion}";
    }
}


