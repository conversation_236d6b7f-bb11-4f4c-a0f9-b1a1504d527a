using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using UserManagement.Application.DTOs;
using Shared.Domain.Common;
using UserManagement.Application.Interfaces;
using Shared.Domain.Common;
using UserManagement.Domain.Entities;
using Shared.Domain.Common;
using UserManagement.Domain.Enums;
using Shared.Domain.Common;

namespace UserManagement.Application.Services;

public interface IEnhancedFeedbackService
{
    Task<Guid> SubmitOrderFeedbackAsync(OrderFeedbackRequest request, CancellationToken cancellationToken = default);
    Task<FeedbackAnalyticsResult> GetFeedbackAnalyticsAsync(FeedbackAnalyticsQuery query, CancellationToken cancellationToken = default);
    Task<List<RedFlagAlert>> DetectRedFlagsAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<FeedbackTrendAnalysis> GetFeedbackTrendsAsync(UserManagement.Application.DTOs.FeedbackTrendQuery query, CancellationToken cancellationToken = default);
    Task<List<OrderFeedbackDto>> GetOrderFeedbackAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<UserFeedbackSummary> GetUserFeedbackSummaryAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<FeedbackInsight>> GenerateFeedbackInsightsAsync(FeedbackInsightQuery query, CancellationToken cancellationToken = default);
}

public class EnhancedFeedbackService : IEnhancedFeedbackService
{
    private readonly IFeedbackRepository _feedbackRepository;
    private readonly IRedFlagDetectionService _redFlagService;
    private readonly IFeedbackAnalyticsEngine _analyticsEngine;
    private readonly INotificationService _notificationService;
    private readonly ILogger<EnhancedFeedbackService> _logger;

    public EnhancedFeedbackService(
        IFeedbackRepository feedbackRepository,
        IRedFlagDetectionService redFlagService,
        IFeedbackAnalyticsEngine analyticsEngine,
        INotificationService notificationService,
        ILogger<EnhancedFeedbackService> logger)
    {
        _feedbackRepository = feedbackRepository;
        _redFlagService = redFlagService;
        _analyticsEngine = analyticsEngine;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<Guid> SubmitOrderFeedbackAsync(OrderFeedbackRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Submitting order feedback for order {OrderId} by user {UserId}",
                request.OrderId, request.ReviewerId);

            // Create order feedback entity using constructor
            var feedback = new Feedback(
                request.OrderId,
                request.ReviewerId,
                request.ReviewerRole,
                request.RevieweeId,
                request.RevieweeRole,
                request.OverallRating,
                request.Comments,
                request.FeedbackType,
                request.IsAnonymous
            );

            // Update detailed ratings
            feedback.UpdateDetailedRatings(
                request.ServiceQualityRating,
                request.CommunicationRating,
                request.TimelinessRating,
                request.ProfessionalismRating
            );

            // Add categories and tags
            if (request.Categories?.Any() == true)
                feedback.AddCategories(request.Categories);

            if (request.Tags?.Any() == true)
                feedback.AddTags(request.Tags);

            // Add metadata
            if (request.Metadata?.Any() == true)
            {
                foreach (var kvp in request.Metadata)
                {
                    feedback.AddMetadata(kvp.Key, kvp.Value);
                }
            }

            // Save feedback
            await _feedbackRepository.AddAsync(feedback, cancellationToken);

            // Analyze for red flags
            var redFlags = await _redFlagService.AnalyzeFeedbackAsync(feedback, cancellationToken);
            if (redFlags.Any())
            {
                // Convert FeedbackRedFlag to RedFlagAlert
                var alertFlags = redFlags.Select(f => new RedFlagAlert
                {
                    UserId = feedback.RevieweeId,
                    AlertType = "Feedback",
                    FlagType = f.FlagType,
                    Description = f.Description,
                    CreatedAt = DateTime.UtcNow,
                    DetectedAt = f.DetectedAt,
                    Severity = f.Severity,
                    Count = 1,
                    FirstOccurrence = f.DetectedAt,
                    LastOccurrence = f.DetectedAt,
                    RelatedFeedbackIds = new List<Guid> { f.FeedbackId }
                }).ToList();

                await ProcessRedFlagsAsync(feedback, alertFlags, cancellationToken);
            }

            // Update user ratings
            await UpdateUserRatingsAsync(request.RevieweeId, request.RevieweeRole, cancellationToken);

            // Send notifications if needed
            if (!request.IsAnonymous && request.NotifyReviewee)
            {
                await _notificationService.SendFeedbackNotificationAsync(feedback, cancellationToken);
            }

            _logger.LogInformation("Order feedback submitted successfully: {FeedbackId}", feedback.Id);
            return feedback.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting order feedback for order {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<FeedbackAnalyticsResult> GetFeedbackAnalyticsAsync(FeedbackAnalyticsQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating feedback analytics for query: {Query}", query);

            var feedbacks = await _feedbackRepository.GetFeedbackForAnalyticsAsync(query, cancellationToken);

            var result = new FeedbackAnalyticsResult
            {
                TotalFeedbacks = feedbacks.Count,
                AverageRating = feedbacks.Any() ? (double)feedbacks.Average(f => f.OverallRating) : 0,
                AverageOverallRating = feedbacks.Any() ? feedbacks.Average(f => f.OverallRating) : 0,
                AverageServiceQualityRating = feedbacks.Any() ? feedbacks.Average(f => f.ServiceQualityRating) : 0m,
                AverageCommunicationRating = feedbacks.Any() ? feedbacks.Average(f => f.CommunicationRating) : 0m,
                AverageTimelinessRating = feedbacks.Any() ? feedbacks.Average(f => f.TimelinessRating) : 0m,
                AverageProfessionalismRating = feedbacks.Any() ? feedbacks.Average(f => f.ProfessionalismRating) : 0m,
                RatingDistribution = CalculateRatingDistribution(feedbacks),
                RecentFeedbacks = ConvertToOrderFeedbackDtos(feedbacks.OrderByDescending(f => f.SubmittedAt).Take(10).ToList()),
                CategoryBreakdown = CalculateCategoryBreakdown(feedbacks),
                TrendData = new List<TrendPoint>(), // Simplified for now
                TopIssues = new List<string>(), // Simplified for now
                ImprovementSuggestions = new List<string>(), // Simplified for now
                GeneratedAt = DateTime.UtcNow
            };

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating feedback analytics");
            throw;
        }
    }

    public async Task<List<RedFlagAlert>> DetectRedFlagsAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Detecting red flags for user {UserId} from {FromDate}", userId, fromDate);

            var feedbacks = await _feedbackRepository.GetRecentFeedbackAsync(userId, fromDate ?? DateTime.UtcNow.AddDays(-30), cancellationToken);
            var redFlags = new List<RedFlagAlert>();

            foreach (var feedback in feedbacks)
            {
                var flags = await _redFlagService.AnalyzeFeedbackAsync(feedback, cancellationToken);
                var alertFlags = flags.Select(f => new RedFlagAlert
                {
                    UserId = feedback.RevieweeId,
                    AlertType = "Feedback",
                    FlagType = f.FlagType,
                    Description = f.Description,
                    CreatedAt = DateTime.UtcNow,
                    DetectedAt = f.DetectedAt,
                    Severity = f.Severity,
                    Count = 1,
                    FirstOccurrence = f.DetectedAt,
                    LastOccurrence = f.DetectedAt,
                    RelatedFeedbackIds = new List<Guid> { f.FeedbackId }
                });
                redFlags.AddRange(alertFlags);
            }

            // Group and prioritize red flags
            var groupedFlags = redFlags
                .GroupBy(rf => new { rf.UserId, rf.FlagType })
                .Select(g => new RedFlagAlert
                {
                    UserId = g.Key.UserId,
                    FlagType = g.Key.FlagType,
                    Severity = g.Max(rf => rf.Severity),
                    Count = g.Count(),
                    FirstOccurrence = g.Min(rf => rf.DetectedAt),
                    LastOccurrence = g.Max(rf => rf.DetectedAt),
                    Description = $"{g.Key.FlagType}: {g.Count()} occurrences",
                    RelatedFeedbackIds = g.SelectMany(rf => rf.RelatedFeedbackIds).Distinct().ToList()
                })
                .OrderByDescending(rf => rf.Severity)
                .ThenByDescending(rf => rf.Count)
                .ToList();

            _logger.LogInformation("Detected {RedFlagCount} red flag groups", groupedFlags.Count);
            return groupedFlags;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting red flags");
            throw;
        }
    }

    public async Task<FeedbackTrendAnalysis> GetFeedbackTrendsAsync(UserManagement.Application.DTOs.FeedbackTrendQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing feedback trends for query: {Query}", query);

            // Repository expects DTOs.FeedbackTrendQuery, so use the query directly
            var feedbacks = await _feedbackRepository.GetFeedbackForTrendAnalysisAsync(query, cancellationToken);

            // Convert DTOs query to Services query for analytics engine
            var servicesQuery = new UserManagement.Application.Services.FeedbackTrendQuery
            {
                FromDate = DateTime.UtcNow.AddDays(-query.Days),
                ToDate = DateTime.UtcNow,
                UserId = query.UserId,
                GroupBy = "daily"
            };

            return await _analyticsEngine.AnalyzeTrendsAsync(feedbacks, servicesQuery, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing feedback trends");
            throw;
        }
    }

    public async Task<List<OrderFeedbackDto>> GetOrderFeedbackAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        try
        {
            var feedbacks = await _feedbackRepository.GetOrderFeedbackAsync(orderId, cancellationToken);

            return feedbacks.Select(f => new OrderFeedbackDto
            {
                Id = f.Id,
                OrderId = f.OrderId,
                ReviewerId = f.IsAnonymous ? null : f.ReviewerId,
                ReviewerRole = f.ReviewerRole,
                RevieweeId = f.RevieweeId,
                RevieweeRole = f.RevieweeRole,
                OverallRating = f.OverallRating,
                ServiceQualityRating = f.ServiceQualityRating,
                CommunicationRating = f.CommunicationRating,
                TimelinessRating = f.TimelinessRating,
                ProfessionalismRating = f.ProfessionalismRating,
                Comments = f.Comments,
                IsAnonymous = f.IsAnonymous,
                FeedbackType = f.FeedbackType,
                Categories = f.Categories,
                Tags = f.Tags,
                SubmittedAt = f.SubmittedAt
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order feedback for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<UserFeedbackSummary> GetUserFeedbackSummaryAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var feedbacks = await _feedbackRepository.GetUserFeedbackAsync(userId, cancellationToken);

            return new UserFeedbackSummary
            {
                UserId = userId,
                TotalFeedbacks = feedbacks.Count,
                AverageRating = feedbacks.Any() ? (double)feedbacks.Average(f => f.OverallRating) : 0,
                RatingDistribution = CalculateRatingDistributionFromDtos(feedbacks),
                RecentFeedbacks = feedbacks.OrderByDescending(f => f.SubmittedAt).Take(10).ToList(),
                TrendDirection = CalculateTrendDirectionFromDtos(feedbacks),
                LastFeedbackDate = feedbacks.Any() ? feedbacks.Max(f => f.SubmittedAt) : DateTime.MinValue,
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user feedback summary for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<FeedbackInsight>> GenerateFeedbackInsightsAsync(FeedbackInsightQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating feedback insights for query: {Query}", query);

            // Convert DTO query to Services query
            var servicesQuery = new UserManagement.Application.Services.FeedbackInsightsQuery
            {
                FromDate = DateTime.UtcNow.AddDays(-30), // Default 30 days
                ToDate = DateTime.UtcNow,
                UserId = query.UserId,
                Category = query.Category,
                Metrics = new List<string> { "rating", "sentiment", "trends" }
            };

            var insights = await _analyticsEngine.GenerateInsightsAsync(servicesQuery, cancellationToken);

            // Convert FeedbackInsights to List<FeedbackInsight>
            return insights.KeyFindings.Select((finding, index) => new FeedbackInsight
            {
                Id = Guid.NewGuid(),
                Type = "Finding",
                Category = query.Category ?? "General",
                Description = finding,
                Impact = 5, // Default impact
                Recommendation = insights.Recommendations.ElementAtOrDefault(index)?.Description ?? "No specific recommendation",
                SupportingData = new List<string> { finding },
                GeneratedAt = insights.GeneratedAt
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating feedback insights");
            throw;
        }
    }

    private async Task ProcessRedFlagsAsync(Feedback feedback, List<RedFlagAlert> redFlags, CancellationToken cancellationToken)
    {
        foreach (var redFlag in redFlags)
        {
            // Log red flag
            _logger.LogWarning("Red flag detected: {FlagType} for user {UserId} in feedback {FeedbackId}",
                redFlag.FlagType, redFlag.UserId, feedback.Id);

            // Save red flag record
            await _feedbackRepository.SaveRedFlagAsync(redFlag, cancellationToken);

            // Send notifications for high severity flags
            if (redFlag.Severity >= (int)RedFlagSeverity.High)
            {
                await _notificationService.SendRedFlagAlertAsync(redFlag, cancellationToken);
            }
        }
    }

    private async Task UpdateUserRatingsAsync(Guid userId, string userRole, CancellationToken cancellationToken)
    {
        var userFeedbacks = await _feedbackRepository.GetUserFeedbackAsync(userId, cancellationToken);

        if (userFeedbacks.Any())
        {
            var averageRating = userFeedbacks.Average(f => f.OverallRating);
            await _feedbackRepository.UpdateUserAverageRatingAsync(userId, averageRating, cancellationToken);
        }
    }

    private Dictionary<int, int> CalculateRatingDistribution(List<Feedback> feedbacks)
    {
        return feedbacks
            .GroupBy(f => (int)Math.Floor(f.OverallRating))
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private Dictionary<string, int> CalculateCategoryBreakdown(List<Feedback> feedbacks)
    {
        return feedbacks
            .SelectMany(f => f.Categories ?? new List<string>())
            .GroupBy(c => c)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private TrendDirection CalculateTrendDirection(List<Feedback> feedbacks)
    {
        if (feedbacks.Count < 2) return TrendDirection.Stable;

        var recentFeedbacks = feedbacks.OrderByDescending(f => f.SubmittedAt).Take(5);
        var olderFeedbacks = feedbacks.OrderByDescending(f => f.SubmittedAt).Skip(5).Take(5);

        if (!recentFeedbacks.Any() || !olderFeedbacks.Any()) return TrendDirection.Stable;

        var recentAverage = recentFeedbacks.Average(f => f.OverallRating);
        var olderAverage = olderFeedbacks.Average(f => f.OverallRating);

        var difference = recentAverage - olderAverage;

        return difference switch
        {
            > 0.2m => TrendDirection.Improving,
            < -0.2m => TrendDirection.Declining,
            _ => TrendDirection.Stable
        };
    }

    private TrendDirection CalculateTrendDirectionFromDtos(List<OrderFeedbackDto> feedbacks)
    {
        if (feedbacks.Count < 2) return TrendDirection.Stable;

        var recentFeedbacks = feedbacks.OrderByDescending(f => f.SubmittedAt).Take(5);
        var olderFeedbacks = feedbacks.OrderByDescending(f => f.SubmittedAt).Skip(5).Take(5);

        if (!recentFeedbacks.Any() || !olderFeedbacks.Any()) return TrendDirection.Stable;

        var recentAverage = recentFeedbacks.Average(f => f.OverallRating);
        var olderAverage = olderFeedbacks.Average(f => f.OverallRating);

        var difference = recentAverage - olderAverage;

        return difference switch
        {
            > 0.2m => TrendDirection.Improving,
            < -0.2m => TrendDirection.Declining,
            _ => TrendDirection.Stable
        };
    }

    private Dictionary<int, int> CalculateRatingDistributionFromDtos(List<OrderFeedbackDto> feedbacks)
    {
        return feedbacks
            .GroupBy(f => (int)Math.Round(f.OverallRating))
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private List<OrderFeedbackDto> ConvertToOrderFeedbackDtos(List<Feedback> feedbacks)
    {
        return feedbacks.Select(f => new OrderFeedbackDto
        {
            Id = f.Id,
            OrderId = f.OrderId,
            UserId = f.ReviewerId, // Using ReviewerId as UserId for compatibility
            ReviewerId = f.IsAnonymous ? null : f.ReviewerId,
            ReviewerRole = f.ReviewerRole,
            RevieweeId = f.RevieweeId,
            RevieweeRole = f.RevieweeRole,
            Rating = (int)f.OverallRating, // Convert decimal to int
            OverallRating = f.OverallRating,
            ServiceQualityRating = f.ServiceQualityRating,
            CommunicationRating = f.CommunicationRating,
            TimelinessRating = f.TimelinessRating,
            ProfessionalismRating = f.ProfessionalismRating,
            Comments = f.Comments,
            IsAnonymous = f.IsAnonymous,
            FeedbackType = f.FeedbackType,
            Categories = f.Categories,
            Tags = f.Tags,
            Metadata = f.Metadata,
            CreatedAt = f.CreatedAt,
            SubmittedAt = f.SubmittedAt
        }).ToList();
    }
}

