using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.ExtendSubscription
{
    public class ExtendSubscriptionCommandHandler : IRequestHandler<ExtendSubscriptionCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ExtendSubscriptionCommandHandler> _logger;

        public ExtendSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IMessageBroker messageBroker,
            ILogger<ExtendSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(ExtendSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Extending subscription {SubscriptionId} by {ExtensionDays} days for user {UserId} by admin {ExtendedByUserId}",
                request.SubscriptionId, request.ExtensionDays, request.UserId, request.ExtendedByUserId);

            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // For admin extensions, we use the subscription's actual UserId
            // Set the command's UserId to the subscription's UserId for proper authorization
            request.UserId = subscription.UserId;

            // Validate subscription can be extended
            if (subscription.Status == SubscriptionStatus.Cancelled)
            {
                throw new SubscriptionDomainException("Cannot extend a cancelled subscription");
            }

            // Store original values for event publishing
            var originalEndDate = subscription.EndDate;
            var originalNextBillingDate = subscription.NextBillingDate;
            var originalGracePeriodEndDate = subscription.GracePeriodEndDate;

            // Extend the subscription
            subscription.ExtendSubscription(
                request.ExtensionDays,
                request.Reason,
                request.ExtendedByUserId,
                request.ApplyAsGracePeriod);

            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish extension event
            await _messageBroker.PublishAsync("subscription.extended", new
            {
                SubscriptionId = subscription.Id,
                UserId = request.UserId,
                PlanId = subscription.PlanId,
                ExtensionDays = request.ExtensionDays,
                Reason = request.Reason,
                ExtendedByUserId = request.ExtendedByUserId,
                ApplyAsGracePeriod = request.ApplyAsGracePeriod,
                OriginalEndDate = originalEndDate,
                OriginalNextBillingDate = originalNextBillingDate,
                OriginalGracePeriodEndDate = originalGracePeriodEndDate,
                NewEndDate = subscription.EndDate,
                NewNextBillingDate = subscription.NextBillingDate,
                NewGracePeriodEndDate = subscription.GracePeriodEndDate,
                ExtendedAt = subscription.LastExtendedAt,
                Status = subscription.Status.ToString()
            });

            _logger.LogInformation("Successfully extended subscription {SubscriptionId} by {ExtensionDays} days for user {UserId} by admin {ExtendedByUserId}",
                request.SubscriptionId, request.ExtensionDays, request.UserId, request.ExtendedByUserId);

            return true;
        }
    }
}
