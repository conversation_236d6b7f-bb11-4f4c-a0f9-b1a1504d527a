using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetShipperOrderLifecycle;

public class GetShipperOrderLifecycleQuery : IRequest<ShipperOrderLifecycleAnalyticsDto>
{
    public Guid ShipperId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<OrderStatus>? StatusFilter { get; set; }
    public List<Guid>? ServiceProviderIds { get; set; } // Filter by specific brokers/carriers
    public List<LoadType>? LoadTypes { get; set; } // Filter by load types
    public List<string>? Routes { get; set; } // Filter by specific routes
    public bool IncludeDailyBreakdown { get; set; } = true;
    public bool IncludeStatusBreakdown { get; set; } = true;
    public bool IncludeProviderBreakdown { get; set; } = true;
    public bool IncludeRouteBreakdown { get; set; } = true;
    public bool IncludePerformanceMetrics { get; set; } = true;
    public bool IncludeServiceProviderComparison { get; set; } = true;

    public GetShipperOrderLifecycleQuery()
    {
        // Default to last 30 days if no dates specified
        ToDate = DateTime.UtcNow.Date.AddDays(1).AddTicks(-1); // End of today
        FromDate = ToDate.Value.AddDays(-30); // 30 days ago
    }

    public GetShipperOrderLifecycleQuery(Guid shipperId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        ShipperId = shipperId;
        FromDate = fromDate ?? DateTime.UtcNow.Date.AddDays(-30);
        ToDate = toDate ?? DateTime.UtcNow.Date.AddDays(1).AddTicks(-1);
    }
}
