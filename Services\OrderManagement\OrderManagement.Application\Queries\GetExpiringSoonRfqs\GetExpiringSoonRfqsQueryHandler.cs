using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetExpiringSoonRfqs;

public class GetExpiringSoonRfqsQueryHandler : IRequestHandler<GetExpiringSoonRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetExpiringSoonRfqsQueryHandler> _logger;

    public GetExpiringSoonRfqsQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetExpiringSoonRfqsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(GetExpiringSoonRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting RFQs expiring soon for transport company {TransportCompanyId}, Hours: {Hours}",
            request.TransportCompanyId, request.HoursBeforeExpiration);

        // Get RFQs expiring soon
        var expiringSoonRfqs = await _rfqRepository.GetRfqsExpiringSoonAsync(
            request.HoursBeforeExpiration, cancellationToken);

        // Filter by transport company
        var filteredRfqs = expiringSoonRfqs
            .Where(r => r.TransportCompanyId == request.TransportCompanyId)
            .ToList();

        // Apply pagination
        var totalCount = filteredRfqs.Count;
        var pagedRfqs = filteredRfqs
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(pagedRfqs);

        _logger.LogInformation("Retrieved {Count} RFQs expiring soon out of {TotalCount} for transport company {TransportCompanyId}",
            pagedRfqs.Count, totalCount, request.TransportCompanyId);

        return new PagedResult<RfqSummaryDto>
        {
            Items = rfqDtos,
            TotalCount = totalCount,
            Page = request.Page,
            PageSize = request.PageSize
        };
    }
}
