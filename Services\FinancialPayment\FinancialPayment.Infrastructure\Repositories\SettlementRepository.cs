using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class SettlementRepository : ISettlementRepository
{
    private readonly FinancialPaymentDbContext _context;

    public SettlementRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<Settlement?> GetByIdAsync(Guid id)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .FirstOrDefaultAsync(s => s.Id == id);
    }

    public async Task<Settlement?> GetByOrderIdAsync(Guid orderId)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .FirstOrDefaultAsync(s => s.OrderId == orderId);
    }

    public async Task<Settlement?> GetByTripIdAsync(Guid tripId)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .FirstOrDefaultAsync(s => s.TripId == tripId);
    }

    public async Task<Settlement?> GetBySettlementNumberAsync(string settlementNumber)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .FirstOrDefaultAsync(s => s.SettlementNumber == settlementNumber);
    }

    public async Task<List<Settlement>> GetByCarrierIdAsync(Guid carrierId)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .Where(s => s.Distributions.Any(d => d.RecipientId == carrierId && d.RecipientRole == ParticipantRole.Carrier))
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Settlement>> GetByBrokerIdAsync(Guid brokerId)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .Where(s => s.Distributions.Any(d => d.RecipientId == brokerId && d.RecipientRole == ParticipantRole.Broker))
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Settlement>> GetByStatusAsync(SettlementStatus status)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .Where(s => s.Status == status)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Settlement>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .Where(s => s.CreatedAt >= from && s.CreatedAt <= to)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();
    }

    public async Task<Settlement> AddAsync(Settlement settlement)
    {
        _context.Settlements.Add(settlement);
        return settlement;
    }

    public async Task UpdateAsync(Settlement settlement)
    {
        _context.Settlements.Update(settlement);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var settlement = await _context.Settlements.FindAsync(id);
        if (settlement != null)
        {
            _context.Settlements.Remove(settlement);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.Settlements.AnyAsync(s => s.Id == id);
    }

    public async Task<List<Settlement>> GetPendingSettlementsAsync()
    {
        return await _context.Settlements
            .Include(s => s.Distributions)
            .Include(s => s.EscrowAccount)
            .Where(s => s.Status == SettlementStatus.Created || s.Status == SettlementStatus.Processing)
            .OrderBy(s => s.CreatedAt)
            .ToListAsync();
    }

    public async Task<decimal> GetTotalSettlementAmountAsync(Guid participantId, DateTime? from = null, DateTime? to = null)
    {
        var query = _context.SettlementDistributions
            .Where(d => d.RecipientId == participantId && d.Status == DistributionStatus.Completed);

        if (from.HasValue)
            query = query.Where(d => d.ProcessedAt >= from.Value);

        if (to.HasValue)
            query = query.Where(d => d.ProcessedAt <= to.Value);

        return await query.SumAsync(d => d.Amount.Amount);
    }
}
