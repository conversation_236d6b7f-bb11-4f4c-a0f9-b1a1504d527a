using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using System.Text;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class ReportGenerator : IReportGenerator
{
    private readonly ILogger<ReportGenerator> _logger;
    private readonly AnalyticsConfiguration _configuration;

    public ReportGenerator(ILogger<ReportGenerator> logger, IOptions<AnalyticsConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<string> GenerateExcelReportAsync(PaymentAnalyticsSummary data, string reportName)
    {
        _logger.LogInformation("Generating Excel report {ReportName}", reportName);

        try
        {
            // For demo purposes, we'll create a simple CSV-like format
            // In a real implementation, you would use a library like EPPlus or ClosedXML
            
            var fileName = $"{reportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            // Ensure directory exists
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            content.AppendLine("Payment Analytics Report");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine($"Period: {data.PeriodStart:yyyy-MM-dd} to {data.PeriodEnd:yyyy-MM-dd}");
            content.AppendLine();
            
            content.AppendLine("Summary Metrics:");
            content.AppendLine($"Total Volume,{data.TotalVolume.Amount} {data.TotalVolume.Currency}");
            content.AppendLine($"Total Transactions,{data.TotalTransactions}");
            content.AppendLine($"Average Transaction Value,{data.AverageTransactionValue.Amount} {data.AverageTransactionValue.Currency}");
            content.AppendLine($"Success Rate,{data.SuccessRate:F2}%");
            content.AppendLine($"Failure Rate,{data.FailureRate:F2}%");
            content.AppendLine($"Refund Rate,{data.RefundRate:F2}%");
            content.AppendLine($"Total Refunds,{data.TotalRefunds.Amount} {data.TotalRefunds.Currency}");
            content.AppendLine();

            content.AppendLine("Payment Method Distribution:");
            foreach (var method in data.PaymentMethodDistribution)
            {
                content.AppendLine($"{method.Key},{method.Value:F2}%");
            }
            content.AppendLine();

            content.AppendLine("Gateway Distribution:");
            foreach (var gateway in data.GatewayDistribution)
            {
                content.AppendLine($"{gateway.Key},{gateway.Value:F2}%");
            }
            content.AppendLine();

            content.AppendLine("Volume Trends:");
            content.AppendLine("Date,Volume");
            foreach (var trend in data.Volumetrends)
            {
                content.AppendLine($"{trend.Label},{trend.Value}");
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("Excel report generated successfully at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating Excel report {ReportName}", reportName);
            throw;
        }
    }

    public async Task<string> GeneratePdfReportAsync(PaymentAnalyticsSummary data, string reportName)
    {
        _logger.LogInformation("Generating PDF report {ReportName}", reportName);

        try
        {
            // For demo purposes, we'll create a simple text format
            // In a real implementation, you would use a library like iTextSharp or PdfSharp
            
            var fileName = $"{reportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            // Ensure directory exists
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            content.AppendLine("PAYMENT ANALYTICS REPORT");
            content.AppendLine("========================");
            content.AppendLine();
            content.AppendLine($"Report Name: {reportName}");
            content.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            content.AppendLine($"Period: {data.PeriodStart:yyyy-MM-dd} to {data.PeriodEnd:yyyy-MM-dd}");
            content.AppendLine();
            
            content.AppendLine("SUMMARY METRICS");
            content.AppendLine("===============");
            content.AppendLine($"Total Volume: {data.TotalVolume.Amount:N2} {data.TotalVolume.Currency}");
            content.AppendLine($"Total Transactions: {data.TotalTransactions:N0}");
            content.AppendLine($"Average Transaction Value: {data.AverageTransactionValue.Amount:N2} {data.AverageTransactionValue.Currency}");
            content.AppendLine($"Success Rate: {data.SuccessRate:F2}%");
            content.AppendLine($"Failure Rate: {data.FailureRate:F2}%");
            content.AppendLine($"Refund Rate: {data.RefundRate:F2}%");
            content.AppendLine($"Total Refunds: {data.TotalRefunds.Amount:N2} {data.TotalRefunds.Currency}");
            content.AppendLine();

            content.AppendLine("PAYMENT METHOD DISTRIBUTION");
            content.AppendLine("===========================");
            foreach (var method in data.PaymentMethodDistribution)
            {
                content.AppendLine($"{method.Key}: {method.Value:F2}%");
            }
            content.AppendLine();

            content.AppendLine("GATEWAY DISTRIBUTION");
            content.AppendLine("===================");
            foreach (var gateway in data.GatewayDistribution)
            {
                content.AppendLine($"{gateway.Key}: {gateway.Value:F2}%");
            }
            content.AppendLine();

            content.AppendLine("VOLUME TRENDS");
            content.AppendLine("=============");
            foreach (var trend in data.Volumetrends.Take(10)) // Show top 10 trends
            {
                content.AppendLine($"{trend.Label}: {trend.Value:N2}");
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("PDF report generated successfully at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF report {ReportName}", reportName);
            throw;
        }
    }

    public async Task<string> GenerateCsvReportAsync(List<PaymentTrend> data, string reportName)
    {
        _logger.LogInformation("Generating CSV report {ReportName}", reportName);

        try
        {
            var fileName = $"{reportName}_{DateTime.UtcNow:yyyyMMddHHmmss}.csv";
            var filePath = Path.Combine(_configuration.ReportsStoragePath, fileName);
            
            // Ensure directory exists
            Directory.CreateDirectory(_configuration.ReportsStoragePath);

            var content = new StringBuilder();
            content.AppendLine("Date,Label,Value");
            
            foreach (var trend in data)
            {
                content.AppendLine($"{trend.Date:yyyy-MM-dd},{trend.Label},{trend.Value}");
            }

            await File.WriteAllTextAsync(filePath, content.ToString());

            _logger.LogInformation("CSV report generated successfully at {FilePath}", filePath);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CSV report {ReportName}", reportName);
            throw;
        }
    }

    public async Task<byte[]> GenerateChartImageAsync(ChartData chartData, string format = "png")
    {
        _logger.LogInformation("Generating chart image in {Format} format", format);

        try
        {
            // For demo purposes, we'll create a simple JSON representation
            // In a real implementation, you would use a charting library like Chart.js with headless browser
            // or a server-side charting library
            
            var chartJson = JsonSerializer.Serialize(chartData, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });

            var imageData = Encoding.UTF8.GetBytes(chartJson);

            await Task.CompletedTask;
            return imageData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating chart image");
            throw;
        }
    }
}
