using Shared.Domain.ValueObjects;

namespace NetworkFleetManagement.Domain.ValueObjects;

public class PerformanceMetrics : ValueObject
{
    public decimal Rating { get; private set; }
    public int TotalTrips { get; private set; }
    public int CompletedTrips { get; private set; }
    public int CancelledTrips { get; private set; }
    public decimal OnTimePercentage { get; private set; }
    public decimal CustomerSatisfactionScore { get; private set; }
    public DateTime LastUpdated { get; private set; }

    private PerformanceMetrics() { }

    public PerformanceMetrics(
        decimal rating = 0,
        int totalTrips = 0,
        int completedTrips = 0,
        int cancelledTrips = 0,
        decimal onTimePercentage = 0,
        decimal customerSatisfactionScore = 0)
    {
        if (rating < 0 || rating > 5)
            throw new ArgumentException("Rating must be between 0 and 5", nameof(rating));

        if (totalTrips < 0)
            throw new ArgumentException("Total trips cannot be negative", nameof(totalTrips));

        if (completedTrips < 0)
            throw new ArgumentException("Completed trips cannot be negative", nameof(completedTrips));

        if (cancelledTrips < 0)
            throw new ArgumentException("Cancelled trips cannot be negative", nameof(cancelledTrips));

        if (onTimePercentage < 0 || onTimePercentage > 100)
            throw new ArgumentException("On-time percentage must be between 0 and 100", nameof(onTimePercentage));

        if (customerSatisfactionScore < 0 || customerSatisfactionScore > 5)
            throw new ArgumentException("Customer satisfaction score must be between 0 and 5", nameof(customerSatisfactionScore));

        Rating = rating;
        TotalTrips = totalTrips;
        CompletedTrips = completedTrips;
        CancelledTrips = cancelledTrips;
        OnTimePercentage = onTimePercentage;
        CustomerSatisfactionScore = customerSatisfactionScore;
        LastUpdated = DateTime.UtcNow;
    }

    public decimal CompletionRate => TotalTrips > 0 ? (decimal)CompletedTrips / TotalTrips * 100 : 0;
    public decimal CancellationRate => TotalTrips > 0 ? (decimal)CancelledTrips / TotalTrips * 100 : 0;

    public PerformanceMetrics UpdateMetrics(
        decimal? newRating = null,
        int? additionalTrips = null,
        int? additionalCompletedTrips = null,
        int? additionalCancelledTrips = null,
        decimal? newOnTimePercentage = null,
        decimal? newCustomerSatisfactionScore = null)
    {
        return new PerformanceMetrics(
            newRating ?? Rating,
            TotalTrips + (additionalTrips ?? 0),
            CompletedTrips + (additionalCompletedTrips ?? 0),
            CancelledTrips + (additionalCancelledTrips ?? 0),
            newOnTimePercentage ?? OnTimePercentage,
            newCustomerSatisfactionScore ?? CustomerSatisfactionScore
        );
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Rating;
        yield return TotalTrips;
        yield return CompletedTrips;
        yield return CancelledTrips;
        yield return OnTimePercentage;
        yield return CustomerSatisfactionScore;
    }
}
