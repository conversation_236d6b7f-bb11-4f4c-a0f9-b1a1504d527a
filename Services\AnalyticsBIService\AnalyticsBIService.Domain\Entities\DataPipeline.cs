using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Data pipeline entity for ETL operations and data processing workflows
/// </summary>
public class DataPipeline : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Status { get; private set; } // Active, Inactive, Error, Paused
    public string Schedule { get; private set; } // Cron expression
    public string DataSources { get; private set; } // JSON serialized list
    public string Destinations { get; private set; } // JSON serialized list
    public string Configuration { get; private set; } // JSON serialized configuration
    public string Steps { get; private set; } // JSON serialized pipeline steps
    public DateTime? LastRun { get; private set; }
    public DateTime? NextRun { get; private set; }
    public long TotalRuns { get; private set; }
    public long SuccessfulRuns { get; private set; }
    public long FailedRuns { get; private set; }
    public decimal SuccessRate { get; private set; }
    public TimeSpan AverageExecutionTime { get; private set; }
    public long TotalRecordsProcessed { get; private set; }
    public DateTime? LastSuccessfulRun { get; private set; }
    public DateTime? LastFailedRun { get; private set; }
    public string? LastError { get; private set; }
    public Guid UserId { get; private set; }
    public string PipelineName { get; private set; }
    public string SourceConfig { get; private set; } // JSON serialized source configuration
    public string TransformationConfig { get; private set; } // JSON serialized transformation configuration
    public string DestinationConfig { get; private set; } // JSON serialized destination configuration
    public string ScheduleConfig { get; private set; } // JSON serialized schedule configuration
    public bool IsActive { get; private set; }

    // Private constructor for EF Core
    private DataPipeline()
    {
        Name = string.Empty;
        Description = string.Empty;
        Status = string.Empty;
        Schedule = string.Empty;
        DataSources = string.Empty;
        Destinations = string.Empty;
        Configuration = string.Empty;
        Steps = string.Empty;
        PipelineName = string.Empty;
        SourceConfig = string.Empty;
        TransformationConfig = string.Empty;
        DestinationConfig = string.Empty;
        ScheduleConfig = string.Empty;
    }

    public DataPipeline(
        string name,
        string description,
        string schedule,
        List<string> dataSources,
        List<string> destinations,
        Dictionary<string, object> configuration,
        Guid userId,
        string sourceConfig = "{}",
        string transformationConfig = "{}",
        string destinationConfig = "{}",
        string scheduleConfig = "{}")
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Schedule = schedule ?? string.Empty;
        DataSources = System.Text.Json.JsonSerializer.Serialize(dataSources ?? new List<string>());
        Destinations = System.Text.Json.JsonSerializer.Serialize(destinations ?? new List<string>());
        Configuration = System.Text.Json.JsonSerializer.Serialize(configuration ?? new Dictionary<string, object>());
        Steps = "[]"; // Empty steps initially
        Status = "Active";
        UserId = userId;
        PipelineName = name;
        SourceConfig = sourceConfig;
        TransformationConfig = transformationConfig;
        DestinationConfig = destinationConfig;
        ScheduleConfig = scheduleConfig;
        IsActive = true;
        TotalRuns = 0;
        SuccessfulRuns = 0;
        FailedRuns = 0;
        SuccessRate = 0;
        AverageExecutionTime = TimeSpan.Zero;
        TotalRecordsProcessed = 0;
    }

    public void UpdateStatus(string status)
    {
        Status = status ?? throw new ArgumentNullException(nameof(status));
        SetUpdatedAt();
    }

    public void UpdateSchedule(string schedule)
    {
        Schedule = schedule ?? string.Empty;
        SetUpdatedAt();
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = System.Text.Json.JsonSerializer.Serialize(configuration ?? new Dictionary<string, object>());
        SetUpdatedAt();
    }

    public void RecordExecution(bool success, TimeSpan executionTime, long recordsProcessed, string? error = null)
    {
        TotalRuns++;

        if (success)
        {
            SuccessfulRuns++;
            LastSuccessfulRun = DateTime.UtcNow;
            LastRun = DateTime.UtcNow;
        }
        else
        {
            FailedRuns++;
            LastFailedRun = DateTime.UtcNow;
            LastError = error;
        }

        // Update success rate
        SuccessRate = TotalRuns > 0 ? (decimal)SuccessfulRuns / TotalRuns * 100 : 0;

        // Update average execution time
        var totalTime = AverageExecutionTime.Ticks * (TotalRuns - 1) + executionTime.Ticks;
        AverageExecutionTime = new TimeSpan(totalTime / TotalRuns);

        // Update total records processed
        TotalRecordsProcessed += recordsProcessed;

        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        Status = "Active";
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        Status = "Inactive";
        SetUpdatedAt();
    }

    public void Pause()
    {
        Status = "Paused";
        SetUpdatedAt();
    }

    public void SetError(string error)
    {
        Status = "Error";
        LastError = error;
        SetUpdatedAt();
    }

    public List<string> GetDataSources()
    {
        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<string>>(DataSources) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    public List<string> GetDestinations()
    {
        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<string>>(Destinations) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    public Dictionary<string, object> GetConfiguration()
    {
        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(Configuration) ?? new Dictionary<string, object>();
        }
        catch
        {
            return new Dictionary<string, object>();
        }
    }
}
