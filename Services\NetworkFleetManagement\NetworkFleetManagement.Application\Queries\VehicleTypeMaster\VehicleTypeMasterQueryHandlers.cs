using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Interfaces;

namespace NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

/// <summary>
/// Handler for getting a vehicle type master by ID
/// </summary>
public class GetVehicleTypeMasterByIdQueryHandler : IRequestHandler<GetVehicleTypeMasterByIdQuery, VehicleTypeMasterDto?>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleTypeMasterByIdQueryHandler> _logger;

    public GetVehicleTypeMasterByIdQueryHandler(
        IVehicleTypeMasterRepository repository,
        IMapper mapper,
        ILogger<GetVehicleTypeMasterByIdQueryHandler> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterDto?> Handle(GetVehicleTypeMasterByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var vehicleTypeMaster = await _repository.GetByIdAsync(request.Id, cancellationToken);
            return vehicleTypeMaster != null ? _mapper.Map<VehicleTypeMasterDto>(vehicleTypeMaster) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle type master by ID: {Id}", request.Id);
            throw;
        }
    }
}

/// <summary>
/// Handler for getting a vehicle type master by code
/// </summary>
public class GetVehicleTypeMasterByCodeQueryHandler : IRequestHandler<GetVehicleTypeMasterByCodeQuery, VehicleTypeMasterDto?>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleTypeMasterByCodeQueryHandler> _logger;

    public GetVehicleTypeMasterByCodeQueryHandler(
        IVehicleTypeMasterRepository repository,
        IMapper mapper,
        ILogger<GetVehicleTypeMasterByCodeQueryHandler> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterDto?> Handle(GetVehicleTypeMasterByCodeQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var vehicleTypeMaster = await _repository.GetByCodeAsync(request.Code, cancellationToken);
            return vehicleTypeMaster != null ? _mapper.Map<VehicleTypeMasterDto>(vehicleTypeMaster) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle type master by code: {Code}", request.Code);
            throw;
        }
    }
}

/// <summary>
/// Handler for getting all vehicle type masters
/// </summary>
public class GetVehicleTypeMastersQueryHandler : IRequestHandler<GetVehicleTypeMastersQuery, IEnumerable<VehicleTypeMasterSummaryDto>>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleTypeMastersQueryHandler> _logger;

    public GetVehicleTypeMastersQueryHandler(
        IVehicleTypeMasterRepository repository,
        IMapper mapper,
        ILogger<GetVehicleTypeMastersQueryHandler> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<VehicleTypeMasterSummaryDto>> Handle(GetVehicleTypeMastersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            IEnumerable<Domain.Entities.VehicleTypeMaster> vehicleTypeMasters;

            if (request.ActiveOnly)
            {
                vehicleTypeMasters = await _repository.GetActiveAsync(cancellationToken);
            }
            else if (!string.IsNullOrWhiteSpace(request.Category))
            {
                vehicleTypeMasters = await _repository.GetByCategoryAsync(request.Category, cancellationToken);
            }
            else
            {
                vehicleTypeMasters = await _repository.GetAllAsync(cancellationToken);
            }

            return _mapper.Map<IEnumerable<VehicleTypeMasterSummaryDto>>(vehicleTypeMasters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle type masters");
            throw;
        }
    }
}

/// <summary>
/// Handler for getting paginated vehicle type masters
/// </summary>
public class GetVehicleTypeMastersPagedQueryHandler : IRequestHandler<GetVehicleTypeMastersPagedQuery, VehicleTypeMasterPagedResultDto>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleTypeMastersPagedQueryHandler> _logger;

    public GetVehicleTypeMastersPagedQueryHandler(
        IVehicleTypeMasterRepository repository,
        IMapper mapper,
        ILogger<GetVehicleTypeMastersPagedQueryHandler> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterPagedResultDto> Handle(GetVehicleTypeMastersPagedQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var (items, totalCount) = await _repository.GetPagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.Category,
                request.IsActive,
                cancellationToken);

            var summaryDtos = _mapper.Map<IEnumerable<VehicleTypeMasterSummaryDto>>(items);

            return new VehicleTypeMasterPagedResultDto
            {
                Items = summaryDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paginated vehicle type masters");
            throw;
        }
    }
}

/// <summary>
/// Handler for getting vehicle type master statistics
/// </summary>
public class GetVehicleTypeMasterStatsQueryHandler : IRequestHandler<GetVehicleTypeMasterStatsQuery, VehicleTypeMasterStatsDto>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly ILogger<GetVehicleTypeMasterStatsQueryHandler> _logger;

    public GetVehicleTypeMasterStatsQueryHandler(
        IVehicleTypeMasterRepository repository,
        ILogger<GetVehicleTypeMasterStatsQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterStatsDto> Handle(GetVehicleTypeMasterStatsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var totalCount = await _repository.GetTotalCountAsync(cancellationToken);
            var activeCount = await _repository.GetActiveCountAsync(cancellationToken);
            var categoryCounts = await _repository.GetCategoryCountsAsync(cancellationToken);
            var usageStats = await _repository.GetUsageStatisticsAsync(cancellationToken);

            return new VehicleTypeMasterStatsDto
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                InactiveCount = totalCount - activeCount,
                CategoryCounts = categoryCounts,
                UsageStatistics = usageStats
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle type master statistics");
            throw;
        }
    }
}
