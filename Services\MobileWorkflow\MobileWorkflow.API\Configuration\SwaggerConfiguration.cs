using Microsoft.OpenApi.Models;
using System.Reflection;

namespace MobileWorkflow.API.Configuration;

public static class SwaggerConfiguration
{
    public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "TLI Mobile & Workflow API",
                Version = "v1.0",
                Description = @"
                    <h2>TLI Mobile & Workflow Service API</h2>
                    <p>Comprehensive API for managing mobile workflows, milestone configurations, and business process automation.</p>
                    
                    <h3>Key Features:</h3>
                    <ul>
                        <li><strong>Milestone Templates:</strong> Create and manage milestone-based workflow templates</li>
                        <li><strong>Role-Based Assignment:</strong> Assign templates to user roles with conditional logic</li>
                        <li><strong>Payout Management:</strong> Configure percentage-based payout rules for milestones</li>
                        <li><strong>Workflow Execution:</strong> Execute and track milestone-based workflows</li>
                        <li><strong>Real-time Updates:</strong> Get real-time updates on workflow progress</li>
                    </ul>
                    
                    <h3>Authentication:</h3>
                    <p>All endpoints require JWT Bearer token authentication. Include the token in the Authorization header:</p>
                    <code>Authorization: Bearer &lt;your-jwt-token&gt;</code>
                    
                    <h3>Rate Limiting:</h3>
                    <ul>
                        <li>1000 requests per hour per API key</li>
                        <li>100 requests per minute burst limit</li>
                    </ul>
                ",
                Contact = new OpenApiContact
                {
                    Name = "TLI API Support",
                    Email = "<EMAIL>",
                    Url = new Uri("https://docs.tli.com/support")
                },
                License = new OpenApiLicense
                {
                    Name = "TLI API License",
                    Url = new Uri("https://tli.com/api-license")
                },
                TermsOfService = new Uri("https://tli.com/terms")
            });

            // Add JWT Authentication
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = @"JWT Authorization header using the Bearer scheme. 
                              Enter 'Bearer' [space] and then your token in the text input below.
                              Example: 'Bearer 12345abcdef'",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // Include XML comments
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Add custom schema filters
            c.SchemaFilter<EnumSchemaFilter>();
            c.OperationFilter<AuthorizeOperationFilter>();

            // Group endpoints by tags
            c.TagActionsBy(api => new[] { GetControllerTag(api.ActionDescriptor.RouteValues["controller"]) });
            c.DocInclusionPredicate((name, api) => true);

            // Add examples
            c.EnableAnnotations();
        });

        return services;
    }

    public static IApplicationBuilder UseSwaggerDocumentation(this IApplicationBuilder app, IWebHostEnvironment env)
    {
        app.UseSwagger(c =>
        {
            c.RouteTemplate = "api-docs/{documentName}/swagger.json";
        });

        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/api-docs/v1/swagger.json", "TLI Mobile & Workflow API v1");
            c.RoutePrefix = "api-docs";
            c.DocumentTitle = "TLI Mobile & Workflow API Documentation";
            
            // Customize UI
            c.DefaultModelsExpandDepth(2);
            c.DefaultModelExpandDepth(2);
            c.DisplayRequestDuration();
            c.EnableDeepLinking();
            c.EnableFilter();
            c.ShowExtensions();
            c.EnableValidator();
            
            // Custom CSS
            c.InjectStylesheet("/swagger-ui/custom.css");
            
            // OAuth configuration if needed
            c.OAuthClientId("tli-mobile-workflow-api");
            c.OAuthAppName("TLI Mobile & Workflow API");
            c.OAuthUsePkce();
        });

        return app;
    }

    private static string GetControllerTag(string? controllerName)
    {
        return controllerName switch
        {
            "MilestoneTemplate" => "Milestone Templates",
            "MilestoneStep" => "Milestone Steps",
            "MilestonePayoutRule" => "Payout Rules",
            "RoleTemplateMapping" => "Role Template Mappings",
            "Workflow" => "Workflows",
            "WorkflowExecution" => "Workflow Execution",
            "MobileApp" => "Mobile Applications",
            _ => controllerName ?? "General"
        };
    }
}

public class EnumSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type.IsEnum)
        {
            schema.Enum.Clear();
            Enum.GetNames(context.Type)
                .ToList()
                .ForEach(name => schema.Enum.Add(new Microsoft.OpenApi.Any.OpenApiString(name)));
        }
    }
}

public class AuthorizeOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var authAttributes = context.MethodInfo.DeclaringType?.GetCustomAttributes(true)
            .Union(context.MethodInfo.GetCustomAttributes(true))
            .OfType<AuthorizeAttribute>();

        if (authAttributes?.Any() == true)
        {
            operation.Responses.TryAdd("401", new OpenApiResponse { Description = "Unauthorized" });
            operation.Responses.TryAdd("403", new OpenApiResponse { Description = "Forbidden" });

            var roles = authAttributes
                .Where(a => !string.IsNullOrEmpty(a.Roles))
                .SelectMany(a => a.Roles!.Split(','))
                .Select(r => r.Trim())
                .Distinct()
                .ToList();

            if (roles.Any())
            {
                operation.Description += $"\n\n**Required Roles:** {string.Join(", ", roles)}";
            }
        }
    }
}

public static class SwaggerExamples
{
    public static class MilestoneTemplate
    {
        public const string CreateRequest = @"{
            ""name"": ""Standard Trip Milestones"",
            ""description"": ""Standard milestone template for trip completion"",
            ""type"": ""Trip"",
            ""category"": ""Logistics"",
            ""configuration"": {
                ""auto_advance"": true,
                ""require_photos"": false
            },
            ""metadata"": {
                ""version"": ""1.0"",
                ""department"": ""logistics""
            },
            ""steps"": [
                {
                    ""name"": ""Trip Started"",
                    ""description"": ""Driver has started the trip"",
                    ""sequenceNumber"": 1,
                    ""isRequired"": true,
                    ""triggerCondition"": ""status=started"",
                    ""payoutRules"": [
                        {
                            ""payoutPercentage"": 30.0,
                            ""triggerCondition"": ""status=started"",
                            ""description"": ""Payment for starting trip""
                        }
                    ]
                },
                {
                    ""name"": ""Trip Completed"",
                    ""description"": ""Trip has been completed"",
                    ""sequenceNumber"": 2,
                    ""isRequired"": true,
                    ""triggerCondition"": ""status=completed"",
                    ""payoutRules"": [
                        {
                            ""payoutPercentage"": 70.0,
                            ""triggerCondition"": ""status=completed"",
                            ""description"": ""Payment for completing trip""
                        }
                    ]
                }
            ]
        }";

        public const string Response = @"{
            ""id"": ""123e4567-e89b-12d3-a456-426614174000"",
            ""name"": ""Standard Trip Milestones"",
            ""description"": ""Standard milestone template for trip completion"",
            ""type"": ""Trip"",
            ""category"": ""Logistics"",
            ""isActive"": true,
            ""isDefault"": false,
            ""createdBy"": ""<EMAIL>"",
            ""createdAt"": ""2024-01-15T10:30:00Z"",
            ""updatedAt"": ""2024-01-20T14:45:00Z"",
            ""usageCount"": 0,
            ""totalPayoutPercentage"": 100.0,
            ""isValid"": true,
            ""configuration"": {
                ""auto_advance"": true,
                ""require_photos"": false
            },
            ""metadata"": {
                ""version"": ""1.0"",
                ""department"": ""logistics""
            }
        }";
    }

    public static class RoleTemplateMapping
    {
        public const string CreateRequest = @"{
            ""roleName"": ""Driver"",
            ""milestoneTemplateId"": ""123e4567-e89b-12d3-a456-426614174000"",
            ""isDefault"": true,
            ""priority"": 100,
            ""conditions"": ""{\""experience_years\"": \"">2\"", \""rating\"": \"">4.0\""}"",
            ""configuration"": {
                ""auto_assign"": true,
                ""notification_enabled"": true
            },
            ""metadata"": {
                ""department"": ""logistics"",
                ""region"": ""north""
            }
        }";
    }
}
