namespace UserManagement.Application.Interfaces;

public interface ILocalizationService
{
    Task<string> GetLocalizedStringAsync(string key, string language);
    Task<Dictionary<string, string>> GetLocalizedStringsAsync(string[] keys, string language);
    Task<List<SupportedLanguage>> GetSupportedLanguagesAsync();
    Task ClearCacheAsync(string? language = null);
    Task PreloadLanguageAsync(string language);
}

public class SupportedLanguage
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public bool IsDefault { get; set; }
}
