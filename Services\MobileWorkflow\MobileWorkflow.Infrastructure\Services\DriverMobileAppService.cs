using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IDriverMobileAppService
{
    Task<TripAssignmentResult> AcceptTripAssignmentAsync(Guid driverId, Guid tripId, CancellationToken cancellationToken = default);
    Task<TripAssignmentResult> RejectTripAssignmentAsync(Guid driverId, Guid tripId, string reason, CancellationToken cancellationToken = default);
    Task<bool> UpdateTripStatusAsync(Guid driverId, Guid tripId, string status, Dictionary<string, object> statusData, CancellationToken cancellationToken = default);
    Task<bool> UpdateDriverLocationAsync(Guid driverId, LocationUpdate locationUpdate, CancellationToken cancellationToken = default);
    Task<PODUploadResult> UploadPODAsync(Guid driverId, Guid tripId, PODData podData, CancellationToken cancellationToken = default);
    Task<DocumentUploadResult> UploadDocumentAsync(Guid driverId, DocumentUploadData documentData, CancellationToken cancellationToken = default);
    Task<List<TripAssignment>> GetPendingTripAssignmentsAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<DriverDashboard> GetDriverDashboardAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<List<EnhancedFeature>> GetEnhancedFeaturesAsync(Guid driverId, string subscriptionTier, CancellationToken cancellationToken = default);
    Task<bool> RecordOfflineActionAsync(Guid driverId, OfflineAction action, CancellationToken cancellationToken = default);

    // New methods for driver app features
    Task<DriverMobileConfig> GetDriverMobileConfigAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<bool> UpdateLocationPreferencesAsync(Guid driverId, LocationPreferencesRequest request, CancellationToken cancellationToken = default);
    Task<EmergencyAlertResult> SendEmergencyAlertAsync(Guid driverId, EmergencyAlertRequest request, CancellationToken cancellationToken = default);
    Task<PagedResult<CompletedTripDto>> GetCompletedTripsAsync(Guid driverId, int pageNumber, int pageSize, DateTime? startDate, DateTime? endDate, CancellationToken cancellationToken = default);
    Task<PagedResult<DriverFeedbackDto>> GetDriverFeedbackAsync(Guid driverId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    Task<DriverRatingStats> GetDriverRatingStatsAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<bool> UpdateLanguagePreferenceAsync(Guid driverId, string languageCode, CancellationToken cancellationToken = default);
    Task<Dictionary<string, string>> GetLocalizationStringsAsync(string languageCode, CancellationToken cancellationToken = default);

    // Enhanced driver profile methods
    Task<EnhancedDriverProfileResponse> GetEnhancedDriverProfileAsync(Guid driverId, EnhancedProfileRequest request, CancellationToken cancellationToken = default);
    Task<bool> UpdateDriverProfileAsync(Guid driverId, UpdateDriverProfileRequest request, CancellationToken cancellationToken = default);
    Task<EntityAssignmentDetails> GetEntityAssignmentDetailsAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<DocumentStatusSummary> GetDocumentStatusSummaryAsync(Guid driverId, CancellationToken cancellationToken = default);

    // Enhanced dashboard methods
    Task<EnhancedDashboardResponse> GetEnhancedDashboardAsync(Guid driverId, EnhancedDashboardRequest request, CancellationToken cancellationToken = default);
    Task<List<MapShortcutDto>> GetMapShortcutsAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<bool> AddMapShortcutAsync(Guid driverId, AddMapShortcutRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteMapShortcutAsync(Guid driverId, Guid shortcutId, CancellationToken cancellationToken = default);
}

public class DriverMobileAppService : IDriverMobileAppService
{
    private readonly IMobileSessionRepository _sessionRepository;
    private readonly IOfflineDataRepository _offlineDataRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<DriverMobileAppService> _logger;

    public DriverMobileAppService(
        IMobileSessionRepository sessionRepository,
        IOfflineDataRepository offlineDataRepository,
        IMemoryCache cache,
        ILogger<DriverMobileAppService> logger)
    {
        _sessionRepository = sessionRepository;
        _offlineDataRepository = offlineDataRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<TripAssignmentResult> AcceptTripAssignmentAsync(Guid driverId, Guid tripId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Driver {DriverId} accepting trip assignment {TripId}", driverId, tripId);

        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null)
            {
                return new TripAssignmentResult { IsSuccess = false, ErrorMessage = "No active session found" };
            }

            // Create offline data for trip acceptance
            var acceptanceData = new Dictionary<string, object>
            {
                { "tripId", tripId },
                { "driverId", driverId },
                { "action", "accept" },
                { "timestamp", DateTime.UtcNow },
                { "location", session.LastKnownLocation ?? "Unknown" }
            };

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "TripAssignment",
                "Accept",
                acceptanceData,
                priority: 1 // High priority
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);

            // Update session
            session.UpdateSessionData("last_trip_action", "accept");
            session.UpdateSessionData("last_trip_id", tripId);
            session.RecordOfflineAction();

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return new TripAssignmentResult
            {
                IsSuccess = true,
                TripId = tripId,
                Status = "Accepted",
                Message = "Trip assignment accepted successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting trip assignment for driver {DriverId}, trip {TripId}", driverId, tripId);
            return new TripAssignmentResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<TripAssignmentResult> RejectTripAssignmentAsync(Guid driverId, Guid tripId, string reason, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Driver {DriverId} rejecting trip assignment {TripId} with reason: {Reason}", driverId, tripId, reason);

        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null)
            {
                return new TripAssignmentResult { IsSuccess = false, ErrorMessage = "No active session found" };
            }

            var rejectionData = new Dictionary<string, object>
            {
                { "tripId", tripId },
                { "driverId", driverId },
                { "action", "reject" },
                { "reason", reason },
                { "timestamp", DateTime.UtcNow }
            };

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "TripAssignment",
                "Reject",
                rejectionData,
                priority: 2 // Medium priority
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);

            session.UpdateSessionData("last_trip_action", "reject");
            session.UpdateSessionData("last_trip_id", tripId);
            session.RecordOfflineAction();

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return new TripAssignmentResult
            {
                IsSuccess = true,
                TripId = tripId,
                Status = "Rejected",
                Message = "Trip assignment rejected successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting trip assignment for driver {DriverId}, trip {TripId}", driverId, tripId);
            return new TripAssignmentResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<bool> UpdateTripStatusAsync(Guid driverId, Guid tripId, string status, Dictionary<string, object> statusData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Driver {DriverId} updating trip {TripId} status to {Status}", driverId, tripId, status);

        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            var updateData = new Dictionary<string, object>
            {
                { "tripId", tripId },
                { "driverId", driverId },
                { "status", status },
                { "timestamp", DateTime.UtcNow },
                { "location", session.LastKnownLocation ?? "Unknown" }
            };

            // Merge additional status data
            foreach (var kvp in statusData)
            {
                updateData[kvp.Key] = kvp.Value;
            }

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "TripUpdate",
                "StatusUpdate",
                updateData,
                priority: GetStatusUpdatePriority(status)
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);

            session.UpdateSessionData("current_trip_status", status);
            session.UpdateSessionData("last_status_update", DateTime.UtcNow);
            session.RecordOfflineAction();

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating trip status for driver {DriverId}, trip {TripId}", driverId, tripId);
            return false;
        }
    }

    public async Task<bool> UpdateDriverLocationAsync(Guid driverId, LocationUpdate locationUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            var locationData = new Dictionary<string, object>
            {
                { "driverId", driverId },
                { "latitude", locationUpdate.Latitude },
                { "longitude", locationUpdate.Longitude },
                { "accuracy", locationUpdate.Accuracy },
                { "speed", locationUpdate.Speed },
                { "heading", locationUpdate.Heading },
                { "timestamp", locationUpdate.Timestamp },
                { "address", locationUpdate.Address ?? "Unknown" }
            };

            session.UpdateLocation($"{locationUpdate.Latitude},{locationUpdate.Longitude}");

            // Store location update as offline data for sync
            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "LocationUpdate",
                "Update",
                locationData,
                priority: 3 // Low priority for regular location updates
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);
            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<PODUploadResult> UploadPODAsync(Guid driverId, Guid tripId, PODData podData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Driver {DriverId} uploading POD for trip {TripId}", driverId, tripId);

        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null)
            {
                return new PODUploadResult { IsSuccess = false, ErrorMessage = "No active session found" };
            }

            var podUploadData = new Dictionary<string, object>
            {
                { "tripId", tripId },
                { "driverId", driverId },
                { "customerName", podData.CustomerName },
                { "customerSignature", podData.CustomerSignature },
                { "deliveryPhotos", podData.DeliveryPhotos },
                { "deliveryNotes", podData.DeliveryNotes },
                { "deliveryTime", podData.DeliveryTime },
                { "location", session.LastKnownLocation ?? "Unknown" },
                { "timestamp", DateTime.UtcNow }
            };

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "PODUpload",
                "Create",
                podUploadData,
                priority: 1 // High priority
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);

            session.UpdateSessionData("last_pod_upload", DateTime.UtcNow);
            session.UpdateSessionData("pod_trip_id", tripId);
            session.RecordOfflineAction();

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return new PODUploadResult
            {
                IsSuccess = true,
                PODId = offlineData.Id,
                TripId = tripId,
                Message = "POD uploaded successfully and queued for sync"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading POD for driver {DriverId}, trip {TripId}", driverId, tripId);
            return new PODUploadResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<DocumentUploadResult> UploadDocumentAsync(Guid driverId, DocumentUploadData documentData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Driver {DriverId} uploading document of type {DocumentType}", driverId, documentData.DocumentType);

        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null)
            {
                return new DocumentUploadResult { IsSuccess = false, ErrorMessage = "No active session found" };
            }

            var uploadData = new Dictionary<string, object>
            {
                { "driverId", driverId },
                { "documentType", documentData.DocumentType },
                { "fileName", documentData.FileName },
                { "fileSize", documentData.FileSize },
                { "fileData", documentData.FileData },
                { "description", documentData.Description },
                { "timestamp", DateTime.UtcNow }
            };

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "DocumentUpload",
                "Create",
                uploadData,
                priority: 2 // Medium priority
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);

            session.UpdateSessionData("last_document_upload", DateTime.UtcNow);
            session.UpdateSessionData("last_document_type", documentData.DocumentType);
            session.RecordOfflineAction();

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return new DocumentUploadResult
            {
                IsSuccess = true,
                DocumentId = offlineData.Id,
                DocumentType = documentData.DocumentType,
                Message = "Document uploaded successfully and queued for sync"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for driver {DriverId}", driverId);
            return new DocumentUploadResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<List<TripAssignment>> GetPendingTripAssignmentsAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        // In real implementation, this would call the Trip Management Service
        // For now, return mock data
        return new List<TripAssignment>
        {
            new TripAssignment
            {
                TripId = Guid.NewGuid(),
                PickupLocation = "Bangalore Airport",
                DropoffLocation = "MG Road",
                PickupTime = DateTime.UtcNow.AddMinutes(30),
                EstimatedDistance = 25.5,
                EstimatedDuration = TimeSpan.FromMinutes(45),
                PaymentAmount = 450.00m,
                CustomerName = "John Doe",
                Priority = "Normal",
                SpecialInstructions = "Handle with care"
            }
        };
    }

    public async Task<DriverDashboard> GetDriverDashboardAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
        var pendingSyncCount = await _offlineDataRepository.GetUnsyncedCountByUserIdAsync(driverId, cancellationToken);

        return new DriverDashboard
        {
            DriverId = driverId,
            IsOnline = session?.IsActive ?? false,
            CurrentLocation = session?.LastKnownLocation,
            PendingTrips = await GetPendingTripAssignmentsAsync(driverId, cancellationToken),
            TodaysEarnings = 1250.00m, // Mock data
            CompletedTrips = 8, // Mock data
            PendingSyncItems = pendingSyncCount,
            LastSyncTime = session?.LastSyncTime,
            OfflineActionsCount = session?.OfflineActionsCount ?? 0
        };
    }

    public async Task<List<EnhancedFeature>> GetEnhancedFeaturesAsync(Guid driverId, string subscriptionTier, CancellationToken cancellationToken = default)
    {
        var features = new List<EnhancedFeature>();

        switch (subscriptionTier.ToLower())
        {
            case "pro":
                features.AddRange(GetProFeatures());
                break;
            case "enterprise":
                features.AddRange(GetProFeatures());
                features.AddRange(GetEnterpriseFeatures());
                break;
            default:
                features.AddRange(GetBasicFeatures());
                break;
        }

        return features;
    }

    public async Task<bool> RecordOfflineActionAsync(Guid driverId, OfflineAction action, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            var actionData = new Dictionary<string, object>
            {
                { "driverId", driverId },
                { "actionType", action.ActionType },
                { "actionData", action.Data },
                { "timestamp", action.Timestamp }
            };

            var offlineData = new OfflineData(
                driverId,
                session.Id,
                "OfflineAction",
                action.ActionType,
                actionData,
                priority: action.Priority
            );

            await _offlineDataRepository.AddAsync(offlineData, cancellationToken);
            session.RecordOfflineAction();
            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording offline action for driver {DriverId}", driverId);
            return false;
        }
    }

    private int GetStatusUpdatePriority(string status)
    {
        return status.ToLower() switch
        {
            "emergency" => 1,
            "delivered" => 1,
            "picked_up" => 1,
            "in_transit" => 2,
            "arrived" => 2,
            _ => 3
        };
    }

    private List<EnhancedFeature> GetBasicFeatures()
    {
        return new List<EnhancedFeature>
        {
            new EnhancedFeature { Name = "Basic Trip Management", Enabled = true, Description = "Accept and manage trips" },
            new EnhancedFeature { Name = "POD Upload", Enabled = true, Description = "Upload proof of delivery" },
            new EnhancedFeature { Name = "Basic Navigation", Enabled = true, Description = "GPS navigation support" }
        };
    }

    private List<EnhancedFeature> GetProFeatures()
    {
        return new List<EnhancedFeature>
        {
            new EnhancedFeature { Name = "Advanced Analytics", Enabled = true, Description = "Detailed performance analytics" },
            new EnhancedFeature { Name = "Route Optimization", Enabled = true, Description = "AI-powered route optimization" },
            new EnhancedFeature { Name = "Priority Support", Enabled = true, Description = "24/7 priority customer support" },
            new EnhancedFeature { Name = "Bulk Trip Management", Enabled = true, Description = "Manage multiple trips efficiently" }
        };
    }

    private List<EnhancedFeature> GetEnterpriseFeatures()
    {
        return new List<EnhancedFeature>
        {
            new EnhancedFeature { Name = "Fleet Management", Enabled = true, Description = "Advanced fleet management tools" },
            new EnhancedFeature { Name = "Custom Integrations", Enabled = true, Description = "Custom API integrations" },
            new EnhancedFeature { Name = "Advanced Reporting", Enabled = true, Description = "Comprehensive business reports" },
            new EnhancedFeature { Name = "White Label Options", Enabled = true, Description = "Branded mobile app experience" }
        };
    }

    // New method implementations for driver app features
    public async Task<DriverMobileConfig> GetDriverMobileConfigAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting mobile config for driver {DriverId}", driverId);

            // In a real implementation, this would come from user preferences and system settings
            var config = new DriverMobileConfig
            {
                DriverId = driverId,
                SessionSettings = new SessionSettings
                {
                    InactivityTimeoutMinutes = 30,
                    AutoLockEnabled = true,
                    RememberDevice = false,
                    MaxConcurrentSessions = 1
                },
                SecuritySettings = new SecuritySettings
                {
                    BiometricAuthEnabled = false,
                    RequireOtpLogin = true,
                    OtpExpiryMinutes = 5,
                    MaxLoginAttempts = 3
                },
                LocationSettings = new LocationSettings
                {
                    LocationSharingEnabled = true,
                    ManualToggleAllowed = true,
                    LocationUpdateIntervalSeconds = 30,
                    ShowGpsTimestamp = true
                },
                LocalizationSettings = new LocalizationSettings
                {
                    LanguageCode = "en",
                    CountryCode = "US",
                    TimeZone = "UTC",
                    DateFormat = "MM/dd/yyyy",
                    TimeFormat = "HH:mm"
                }
            };

            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mobile config for driver {DriverId}", driverId);
            throw;
        }
    }

    public async Task<bool> UpdateLocationPreferencesAsync(Guid driverId, LocationPreferencesRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating location preferences for driver {DriverId}", driverId);

            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            // Update session data with new preferences
            session.UpdateSessionData("location_sharing_enabled", request.LocationSharingEnabled);
            session.UpdateSessionData("location_update_interval", request.UpdateIntervalSeconds);
            session.UpdateSessionData("preferences_updated_at", DateTime.UtcNow);

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            // In a real implementation, this would also update user preferences in the database
            // and possibly notify other services about the preference change

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location preferences for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<EmergencyAlertResult> SendEmergencyAlertAsync(Guid driverId, EmergencyAlertRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("Emergency alert triggered by driver {DriverId}: {AlertType}", driverId, request.AlertType);

            var alertId = Guid.NewGuid().ToString();
            var alertTime = DateTime.UtcNow;

            // Create emergency alert data
            var alertData = new Dictionary<string, object>
            {
                { "alertId", alertId },
                { "driverId", driverId },
                { "alertType", request.AlertType },
                { "message", request.Message ?? string.Empty },
                { "latitude", request.Latitude },
                { "longitude", request.Longitude },
                { "tripId", request.TripId ?? string.Empty },
                { "timestamp", alertTime },
                { "additionalData", request.AdditionalData }
            };

            // Store as high-priority offline data for immediate sync
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session != null)
            {
                var offlineData = new OfflineData(
                    driverId,
                    session.Id,
                    "EmergencyAlert",
                    request.AlertType,
                    alertData,
                    priority: 1 // Highest priority
                );

                await _offlineDataRepository.AddAsync(offlineData, cancellationToken);
                session.UpdateSessionData("last_emergency_alert", alertTime);
                await _sessionRepository.UpdateAsync(session, cancellationToken);
            }

            // In a real implementation, this would:
            // 1. Immediately notify emergency contacts
            // 2. Send alerts to fleet managers
            // 3. Integrate with emergency services if needed
            // 4. Trigger location tracking
            // 5. Send push notifications to relevant parties

            var notifiedContacts = new List<string> { "Fleet Manager", "Emergency Contact", "Support Team" };

            return new EmergencyAlertResult
            {
                IsSuccess = true,
                AlertId = alertId,
                AlertTime = alertTime,
                NotifiedContacts = notifiedContacts
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending emergency alert for driver {DriverId}", driverId);
            return new EmergencyAlertResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                AlertTime = DateTime.UtcNow
            };
        }
    }

    public async Task<PagedResult<CompletedTripDto>> GetCompletedTripsAsync(Guid driverId, int pageNumber, int pageSize, DateTime? startDate, DateTime? endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting completed trips for driver {DriverId}, page {PageNumber}", driverId, pageNumber);

            // In a real implementation, this would query the TripManagement service
            // For now, returning mock data
            var mockTrips = GenerateMockCompletedTrips(driverId, startDate, endDate);

            var totalCount = mockTrips.Count;
            var skip = (pageNumber - 1) * pageSize;
            var items = mockTrips.Skip(skip).Take(pageSize).ToList();

            return new PagedResult<CompletedTripDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting completed trips for driver {DriverId}", driverId);
            return new PagedResult<CompletedTripDto>();
        }
    }

    public async Task<PagedResult<DriverFeedbackDto>> GetDriverFeedbackAsync(Guid driverId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting feedback for driver {DriverId}, page {PageNumber}", driverId, pageNumber);

            // In a real implementation, this would query the feedback from TripManagement or AnalyticsBI service
            var mockFeedback = GenerateMockDriverFeedback(driverId);

            var totalCount = mockFeedback.Count;
            var skip = (pageNumber - 1) * pageSize;
            var items = mockFeedback.Skip(skip).Take(pageSize).ToList();

            return new PagedResult<DriverFeedbackDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feedback for driver {DriverId}", driverId);
            return new PagedResult<DriverFeedbackDto>();
        }
    }

    public async Task<DriverRatingStats> GetDriverRatingStatsAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting rating stats for driver {DriverId}", driverId);

            // In a real implementation, this would query the AnalyticsBI service
            var stats = new DriverRatingStats
            {
                DriverId = driverId,
                AverageRating = 4.7,
                TotalRatings = 156,
                RatingDistribution = new Dictionary<int, int>
                {
                    { 5, 89 },
                    { 4, 45 },
                    { 3, 15 },
                    { 2, 5 },
                    { 1, 2 }
                },
                RecentRating = 4.8,
                RecentRatingCount = 23,
                LastUpdated = DateTime.UtcNow
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating stats for driver {DriverId}", driverId);
            throw;
        }
    }

    public async Task<bool> UpdateLanguagePreferenceAsync(Guid driverId, string languageCode, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating language preference for driver {DriverId} to {LanguageCode}", driverId, languageCode);

            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            // Update session data with new language preference
            session.UpdateSessionData("language_code", languageCode);
            session.UpdateSessionData("language_updated_at", DateTime.UtcNow);

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            // In a real implementation, this would also:
            // 1. Update user preferences in UserManagement service
            // 2. Update notification language in CommunicationNotification service
            // 3. Cache the preference for quick access

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating language preference for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<Dictionary<string, string>> GetLocalizationStringsAsync(string languageCode, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting localization strings for language {LanguageCode}", languageCode);

            // Check cache first
            var cacheKey = $"localization_{languageCode}";
            if (_cache.TryGetValue(cacheKey, out Dictionary<string, string>? cachedStrings) && cachedStrings != null)
            {
                return cachedStrings;
            }

            // In a real implementation, this would load from a localization service or database
            var strings = GetMockLocalizationStrings(languageCode);

            // Cache for 1 hour
            _cache.Set(cacheKey, strings, TimeSpan.FromHours(1));

            return strings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting localization strings for language {LanguageCode}", languageCode);
            return new Dictionary<string, string>();
        }
    }

    // Helper methods for mock data generation
    private List<CompletedTripDto> GenerateMockCompletedTrips(Guid driverId, DateTime? startDate, DateTime? endDate)
    {
        var trips = new List<CompletedTripDto>();
        var random = new Random();
        var baseDate = startDate ?? DateTime.UtcNow.AddDays(-30);
        var endDateValue = endDate ?? DateTime.UtcNow;

        for (int i = 0; i < 25; i++)
        {
            var tripStart = baseDate.AddDays(random.Next(0, (int)(endDateValue - baseDate).TotalDays));
            var tripEnd = tripStart.AddHours(random.Next(1, 8));

            trips.Add(new CompletedTripDto
            {
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{1000 + i}",
                StartTime = tripStart,
                EndTime = tripEnd,
                OriginAddress = $"Origin Address {i + 1}",
                DestinationAddress = $"Destination Address {i + 1}",
                Distance = (decimal)(random.NextDouble() * 500 + 10),
                Earnings = (decimal)(random.NextDouble() * 500 + 50),
                Rating = Math.Round(random.NextDouble() * 2 + 3, 1), // 3.0 to 5.0
                CustomerFeedback = i % 3 == 0 ? $"Great service from driver!" : null,
                Status = "Completed"
            });
        }

        return trips.OrderByDescending(t => t.StartTime).ToList();
    }

    private List<DriverFeedbackDto> GenerateMockDriverFeedback(Guid driverId)
    {
        var feedback = new List<DriverFeedbackDto>();
        var random = new Random();
        var feedbackTypes = new[] { "Positive", "Neutral", "Negative" };
        var comments = new[]
        {
            "Excellent driver, very professional!",
            "Good service, on time delivery.",
            "Driver was courteous and helpful.",
            "Average service, nothing special.",
            "Could improve communication.",
            "Late delivery, but driver was apologetic."
        };

        for (int i = 0; i < 20; i++)
        {
            var rating = random.NextDouble() * 2 + 3; // 3.0 to 5.0
            var feedbackType = rating >= 4.5 ? "Positive" : rating >= 3.5 ? "Neutral" : "Negative";

            feedback.Add(new DriverFeedbackDto
            {
                FeedbackId = Guid.NewGuid(),
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{2000 + i}",
                Rating = Math.Round(rating, 1),
                Comments = i % 2 == 0 ? comments[random.Next(comments.Length)] : null,
                SubmittedAt = DateTime.UtcNow.AddDays(-random.Next(1, 60)),
                CustomerName = $"Customer {i + 1}",
                FeedbackType = feedbackType
            });
        }

        return feedback.OrderByDescending(f => f.SubmittedAt).ToList();
    }

    private Dictionary<string, string> GetMockLocalizationStrings(string languageCode)
    {
        return languageCode.ToLower() switch
        {
            "es" => new Dictionary<string, string>
            {
                { "welcome", "Bienvenido" },
                { "dashboard", "Tablero" },
                { "trips", "Viajes" },
                { "earnings", "Ganancias" },
                { "profile", "Perfil" },
                { "settings", "Configuración" },
                { "logout", "Cerrar sesión" },
                { "emergency", "Emergencia" },
                { "location", "Ubicación" },
                { "feedback", "Comentarios" },
                { "rating", "Calificación" },
                { "completed", "Completado" },
                { "pending", "Pendiente" },
                { "accept", "Aceptar" },
                { "reject", "Rechazar" },
                { "start_trip", "Iniciar viaje" },
                { "end_trip", "Finalizar viaje" },
                { "upload_pod", "Subir POD" },
                { "navigation", "Navegación" }
            },
            "hi" => new Dictionary<string, string>
            {
                { "welcome", "स्वागत है" },
                { "dashboard", "डैशबोर्ड" },
                { "trips", "यात्राएं" },
                { "earnings", "कमाई" },
                { "profile", "प्रोफ़ाइल" },
                { "settings", "सेटिंग्स" },
                { "logout", "लॉग आउट" },
                { "emergency", "आपातकाल" },
                { "location", "स्थान" },
                { "feedback", "फीडबैक" },
                { "rating", "रेटिंग" },
                { "completed", "पूर्ण" },
                { "pending", "लंबित" },
                { "accept", "स्वीकार करें" },
                { "reject", "अस्वीकार करें" },
                { "start_trip", "यात्रा शुरू करें" },
                { "end_trip", "यात्रा समाप्त करें" },
                { "upload_pod", "POD अपलोड करें" },
                { "navigation", "नेवीगेशन" }
            },
            _ => new Dictionary<string, string> // Default English
            {
                { "welcome", "Welcome" },
                { "dashboard", "Dashboard" },
                { "trips", "Trips" },
                { "earnings", "Earnings" },
                { "profile", "Profile" },
                { "settings", "Settings" },
                { "logout", "Logout" },
                { "emergency", "Emergency" },
                { "location", "Location" },
                { "feedback", "Feedback" },
                { "rating", "Rating" },
                { "completed", "Completed" },
                { "pending", "Pending" },
                { "accept", "Accept" },
                { "reject", "Reject" },
                { "start_trip", "Start Trip" },
                { "end_trip", "End Trip" },
                { "upload_pod", "Upload POD" },
                { "navigation", "Navigation" }
            }
        };
    }

    // Enhanced driver profile method implementations
    public async Task<EnhancedDriverProfileResponse> GetEnhancedDriverProfileAsync(Guid driverId, EnhancedProfileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting enhanced driver profile for driver {DriverId}", driverId);

            // In a real implementation, this would call NetworkFleetManagement service
            // For now, returning mock data
            var profile = new EnhancedDriverProfile
            {
                Id = driverId,
                FullName = "John Doe",
                PhoneNumber = "******-0123",
                Email = "<EMAIL>",
                Status = "Available",
                IsVerified = true,
                LastActiveAt = DateTime.UtcNow.AddMinutes(-15),

                EntityAssignment = new EntityAssignmentDetails
                {
                    CarrierId = Guid.NewGuid(),
                    CarrierName = "ABC Transport Ltd.",
                    CarrierType = "Fleet",
                    CarrierStatus = "Active",
                    CarrierContact = new ContactDetails
                    {
                        Phone = "******-0100",
                        Email = "<EMAIL>",
                        Address = "123 Transport Ave, City, State 12345"
                    },
                    FleetName = "Fleet Alpha",
                    FleetManager = "Jane Smith",
                    Hierarchy = new List<EntityHierarchy>
                    {
                        new EntityHierarchy { Level = "Company", Name = "ABC Transport Ltd.", Manager = "CEO John Smith", Contact = "******-0100" },
                        new EntityHierarchy { Level = "Fleet", Name = "Fleet Alpha", Manager = "Jane Smith", Contact = "******-0101" }
                    },
                    AssignedAt = DateTime.UtcNow.AddDays(-30)
                },

                Performance = new DriverPerformanceSummary
                {
                    AverageRating = 4.7,
                    TotalTrips = 156,
                    OnTimePerformance = 94.5,
                    SafetyScore = 98.2,
                    PerformanceGrade = "A",
                    Alerts = new List<string>()
                },

                DocumentStatus = new DocumentStatusSummary
                {
                    TotalDocuments = 5,
                    ValidDocuments = 4,
                    ExpiredDocuments = 0,
                    ExpiringDocuments = 1,
                    Documents = new List<DocumentStatusDto>
                    {
                        new DocumentStatusDto
                        {
                            Id = Guid.NewGuid(),
                            DocumentType = "License",
                            Status = "Valid",
                            ExpiryDate = DateTime.UtcNow.AddDays(45),
                            DaysUntilExpiry = 45,
                            IsExpired = false,
                            IsExpiringSoon = true
                        }
                    },
                    LastUpdated = DateTime.UtcNow
                },

                VehicleAssignments = new List<VehicleAssignmentInfo>
                {
                    new VehicleAssignmentInfo
                    {
                        VehicleId = Guid.NewGuid(),
                        VehicleNumber = "TRK-001",
                        VehicleType = "Truck",
                        IsActive = true,
                        AssignedAt = DateTime.UtcNow.AddDays(-15)
                    }
                }
            };

            return new EnhancedDriverProfileResponse
            {
                IsSuccess = true,
                Profile = profile
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced driver profile for driver {DriverId}", driverId);
            return new EnhancedDriverProfileResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> UpdateDriverProfileAsync(Guid driverId, UpdateDriverProfileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating driver profile for driver {DriverId}", driverId);

            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            // Update session data with profile changes
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
                session.UpdateSessionData("phone_number", request.PhoneNumber);

            if (!string.IsNullOrWhiteSpace(request.Email))
                session.UpdateSessionData("email", request.Email);

            if (!string.IsNullOrWhiteSpace(request.ProfilePhotoUrl))
                session.UpdateSessionData("profile_photo_url", request.ProfilePhotoUrl);

            if (request.PreferredRoutes.Any())
                session.UpdateSessionData("preferred_routes", request.PreferredRoutes);

            if (request.OperationalAreas.Any())
                session.UpdateSessionData("operational_areas", request.OperationalAreas);

            session.UpdateSessionData("preferences", request.Preferences);
            session.UpdateSessionData("profile_updated_at", DateTime.UtcNow);

            await _sessionRepository.UpdateAsync(session, cancellationToken);

            // In a real implementation, this would also update the NetworkFleetManagement service
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver profile for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<EntityAssignmentDetails> GetEntityAssignmentDetailsAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting entity assignment details for driver {DriverId}", driverId);

            // Mock implementation - would integrate with NetworkFleetManagement service
            return new EntityAssignmentDetails
            {
                CarrierId = Guid.NewGuid(),
                CarrierName = "ABC Transport Ltd.",
                CarrierType = "Fleet",
                CarrierStatus = "Active",
                CarrierContact = new ContactDetails
                {
                    Phone = "******-0100",
                    Email = "<EMAIL>",
                    Address = "123 Transport Ave, City, State 12345"
                },
                FleetName = "Fleet Alpha",
                FleetManager = "Jane Smith",
                Hierarchy = new List<EntityHierarchy>
                {
                    new EntityHierarchy { Level = "Company", Name = "ABC Transport Ltd.", Manager = "CEO John Smith", Contact = "******-0100" },
                    new EntityHierarchy { Level = "Division", Name = "Regional Division", Manager = "Mike Johnson", Contact = "******-0102" },
                    new EntityHierarchy { Level = "Fleet", Name = "Fleet Alpha", Manager = "Jane Smith", Contact = "******-0101" }
                },
                AssignedAt = DateTime.UtcNow.AddDays(-30)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity assignment details for driver {DriverId}", driverId);
            throw;
        }
    }

    public async Task<DocumentStatusSummary> GetDocumentStatusSummaryAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting document status summary for driver {DriverId}", driverId);

            // Mock implementation - would integrate with DocumentManagement service
            return new DocumentStatusSummary
            {
                TotalDocuments = 5,
                ValidDocuments = 4,
                ExpiredDocuments = 0,
                ExpiringDocuments = 1,
                Documents = new List<DocumentStatusDto>
                {
                    new DocumentStatusDto
                    {
                        Id = Guid.NewGuid(),
                        DocumentType = "License",
                        Status = "Valid",
                        ExpiryDate = DateTime.UtcNow.AddDays(45),
                        DaysUntilExpiry = 45,
                        IsExpired = false,
                        IsExpiringSoon = true
                    },
                    new DocumentStatusDto
                    {
                        Id = Guid.NewGuid(),
                        DocumentType = "Insurance",
                        Status = "Valid",
                        ExpiryDate = DateTime.UtcNow.AddDays(120),
                        DaysUntilExpiry = 120,
                        IsExpired = false,
                        IsExpiringSoon = false
                    }
                },
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document status summary for driver {DriverId}", driverId);
            throw;
        }
    }

    // Enhanced dashboard method implementations
    public async Task<EnhancedDashboardResponse> GetEnhancedDashboardAsync(Guid driverId, EnhancedDashboardRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting enhanced dashboard for driver {DriverId}", driverId);

            // In a real implementation, this would integrate with TripManagement service
            var dashboard = new EnhancedDashboard
            {
                DriverId = driverId,
                DriverName = "John Doe",
                Status = "Available",

                CurrentTrip = new CurrentTripInfo
                {
                    TripId = Guid.NewGuid(),
                    TripNumber = "TRP-001",
                    Status = "InProgress",
                    Origin = "Pickup Location",
                    Destination = "Delivery Location",
                    StartTime = DateTime.UtcNow.AddHours(-2),
                    EstimatedEndTime = DateTime.UtcNow.AddHours(1),
                    ProgressPercentage = 65.0
                },

                RatingStats = new RatingStatistics
                {
                    AverageRating = 4.7,
                    TotalRatings = 156,
                    RatingDistribution = new Dictionary<int, int>
                    {
                        { 5, 89 }, { 4, 45 }, { 3, 15 }, { 2, 5 }, { 1, 2 }
                    },
                    RecentRating = 4.8,
                    RecentRatingCount = 23,
                    RatingGrade = "A",
                    RatingHighlights = new List<string> { "Professional", "Punctual", "Helpful" }
                },

                RecentCompletedTrips = GenerateRecentTrips(request.RecentTripsCount),
                RecentFeedback = GenerateRecentFeedback(request.RecentFeedbackCount),
                MapShortcuts = await GetMapShortcutsAsync(driverId, cancellationToken),

                PerformanceMetrics = new PerformanceMetrics
                {
                    OnTimePerformance = 94.5,
                    SafetyScore = 98.2,
                    FuelEfficiency = 12.5,
                    CustomerSatisfaction = 4.7,
                    TotalTripsCompleted = 156,
                    PerformanceGrade = "A",
                    Alerts = new List<string>()
                },

                QuickStats = new QuickStats
                {
                    TripsToday = 3,
                    TripsThisWeek = 18,
                    TripsThisMonth = 72,
                    EarningsToday = 450.00m,
                    EarningsThisWeek = 2800.00m,
                    EarningsThisMonth = 12500.00m,
                    PendingTrips = 2,
                    DocumentsExpiringSoon = 1
                },

                Earnings = new EarningsSummary
                {
                    TotalEarnings = 45600.00m,
                    EarningsToday = 450.00m,
                    EarningsThisWeek = 2800.00m,
                    EarningsThisMonth = 12500.00m,
                    Documents = new List<DocumentStatusDto>(),
                    LastUpdated = DateTime.UtcNow
                }
            };

            return new EnhancedDashboardResponse
            {
                IsSuccess = true,
                Dashboard = dashboard
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced dashboard for driver {DriverId}", driverId);
            return new EnhancedDashboardResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<MapShortcutDto>> GetMapShortcutsAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting map shortcuts for driver {DriverId}", driverId);

            // Mock implementation - would store in database or cache
            return new List<MapShortcutDto>
            {
                new MapShortcutDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Home",
                    Description = "Driver's home location",
                    Latitude = 40.7128,
                    Longitude = -74.0060,
                    Address = "123 Home St, City, State",
                    ShortcutType = "Home",
                    NavigationUrl = "https://maps.google.com/?q=40.7128,-74.0060",
                    DistanceFromCurrentKm = 15.2,
                    EstimatedTimeMinutes = 25,
                    IsFavorite = true
                },
                new MapShortcutDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Main Depot",
                    Description = "Primary loading depot",
                    Latitude = 40.7589,
                    Longitude = -73.9851,
                    Address = "456 Depot Ave, City, State",
                    ShortcutType = "Depot",
                    NavigationUrl = "https://maps.google.com/?q=40.7589,-73.9851",
                    DistanceFromCurrentKm = 8.7,
                    EstimatedTimeMinutes = 18,
                    IsFavorite = true
                },
                new MapShortcutDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Fuel Station",
                    Description = "Preferred fuel station",
                    Latitude = 40.7505,
                    Longitude = -73.9934,
                    Address = "789 Fuel St, City, State",
                    ShortcutType = "FuelStation",
                    NavigationUrl = "https://maps.google.com/?q=40.7505,-73.9934",
                    DistanceFromCurrentKm = 3.2,
                    EstimatedTimeMinutes = 8,
                    IsFavorite = false
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting map shortcuts for driver {DriverId}", driverId);
            return new List<MapShortcutDto>();
        }
    }

    public async Task<bool> AddMapShortcutAsync(Guid driverId, AddMapShortcutRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adding map shortcut for driver {DriverId}: {Name}", driverId, request.Name);

            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            // Store shortcut in session data (in real implementation, would store in database)
            var shortcuts = session.GetSessionData<List<object>>("map_shortcuts") ?? new List<object>();
            shortcuts.Add(new
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                Address = request.Address,
                ShortcutType = request.ShortcutType,
                IsFavorite = request.IsFavorite,
                CreatedAt = DateTime.UtcNow
            });

            session.UpdateSessionData("map_shortcuts", shortcuts);
            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding map shortcut for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<bool> DeleteMapShortcutAsync(Guid driverId, Guid shortcutId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting map shortcut {ShortcutId} for driver {DriverId}", shortcutId, driverId);

            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(driverId, cancellationToken);
            if (session == null) return false;

            // Remove shortcut from session data (in real implementation, would delete from database)
            var shortcuts = session.GetSessionData<List<object>>("map_shortcuts") ?? new List<object>();
            // In real implementation, would filter by shortcut ID
            session.UpdateSessionData("map_shortcuts", shortcuts);
            await _sessionRepository.UpdateAsync(session, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting map shortcut for driver {DriverId}", driverId);
            return false;
        }
    }

    // Helper methods for generating mock data
    private List<CompletedTripSummary> GenerateRecentTrips(int count)
    {
        var trips = new List<CompletedTripSummary>();
        for (int i = 0; i < count; i++)
        {
            trips.Add(new CompletedTripSummary
            {
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{1000 + i}",
                StartTime = DateTime.UtcNow.AddDays(-i - 1),
                EndTime = DateTime.UtcNow.AddDays(-i - 1).AddHours(3),
                OriginAddress = $"Origin {i + 1}",
                DestinationAddress = $"Destination {i + 1}",
                Distance = 150 + (i * 25),
                Earnings = 250 + (i * 50),
                Rating = 4.5 + (i * 0.1),
                CustomerFeedback = i % 2 == 0 ? "Great service!" : null,
                Status = "Completed"
            });
        }
        return trips;
    }

    private List<FeedbackSummary> GenerateRecentFeedback(int count)
    {
        var feedback = new List<FeedbackSummary>();
        var feedbackTypes = new[] { "Positive", "Neutral", "Negative" };

        for (int i = 0; i < count; i++)
        {
            feedback.Add(new FeedbackSummary
            {
                FeedbackId = Guid.NewGuid(),
                TripId = Guid.NewGuid(),
                TripNumber = $"TRP{2000 + i}",
                Rating = 4.0 + (i * 0.3),
                Comments = i % 2 == 0 ? "Excellent driver, very professional!" : null,
                SubmittedAt = DateTime.UtcNow.AddDays(-i - 1),
                CustomerName = $"Customer {i + 1}",
                FeedbackType = feedbackTypes[i % feedbackTypes.Length]
            });
        }
        return feedback;
    }
}

// Data Models
public class TripAssignmentResult
{
    public bool IsSuccess { get; set; }
    public Guid TripId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class LocationUpdate
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double Accuracy { get; set; }
    public double Speed { get; set; }
    public double Heading { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Address { get; set; }
}

public class PODData
{
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerSignature { get; set; } = string.Empty;
    public List<string> DeliveryPhotos { get; set; } = new();
    public string DeliveryNotes { get; set; } = string.Empty;
    public DateTime DeliveryTime { get; set; }
}

public class PODUploadResult
{
    public bool IsSuccess { get; set; }
    public Guid PODId { get; set; }
    public Guid TripId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class DocumentUploadData
{
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileData { get; set; } = string.Empty; // Base64 encoded
    public string Description { get; set; } = string.Empty;
}

public class DocumentUploadResult
{
    public bool IsSuccess { get; set; }
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
}

public class TripAssignment
{
    public Guid TripId { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DropoffLocation { get; set; } = string.Empty;
    public DateTime PickupTime { get; set; }
    public double EstimatedDistance { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public decimal PaymentAmount { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string SpecialInstructions { get; set; } = string.Empty;
}

public class DriverDashboard
{
    public Guid DriverId { get; set; }
    public bool IsOnline { get; set; }
    public string? CurrentLocation { get; set; }
    public List<TripAssignment> PendingTrips { get; set; } = new();
    public decimal TodaysEarnings { get; set; }
    public int CompletedTrips { get; set; }
    public int PendingSyncItems { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public int OfflineActionsCount { get; set; }
}

public class EnhancedFeature
{
    public string Name { get; set; } = string.Empty;
    public bool Enabled { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class OfflineAction
{
    public string ActionType { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime Timestamp { get; set; }
    public int Priority { get; set; } = 2;
}

// New DTOs for driver app features
public class DriverMobileConfig
{
    public Guid DriverId { get; set; }
    public SessionSettings SessionSettings { get; set; } = new();
    public SecuritySettings SecuritySettings { get; set; } = new();
    public LocationSettings LocationSettings { get; set; } = new();
    public NotificationSettings NotificationSettings { get; set; } = new();
    public LocalizationSettings LocalizationSettings { get; set; } = new();
}

public class SessionSettings
{
    public int InactivityTimeoutMinutes { get; set; } = 30;
    public bool AutoLockEnabled { get; set; } = true;
    public bool RememberDevice { get; set; } = false;
    public int MaxConcurrentSessions { get; set; } = 1;
}

public class SecuritySettings
{
    public bool BiometricAuthEnabled { get; set; } = false;
    public bool RequireOtpLogin { get; set; } = true;
    public int OtpExpiryMinutes { get; set; } = 5;
    public int MaxLoginAttempts { get; set; } = 3;
}

public class LocationSettings
{
    public bool LocationSharingEnabled { get; set; } = true;
    public bool ManualToggleAllowed { get; set; } = true;
    public int LocationUpdateIntervalSeconds { get; set; } = 30;
    public bool ShowGpsTimestamp { get; set; } = true;
}

public class LocalizationSettings
{
    public string LanguageCode { get; set; } = "en";
    public string CountryCode { get; set; } = "US";
    public string TimeZone { get; set; } = "UTC";
    public string DateFormat { get; set; } = "MM/dd/yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
}

public class LocationPreferencesRequest
{
    public bool LocationSharingEnabled { get; set; }
    public int UpdateIntervalSeconds { get; set; } = 30;
}

public class EmergencyAlertRequest
{
    public string AlertType { get; set; } = "SOS"; // SOS, Accident, Breakdown, Medical
    public string? Message { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? TripId { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public class EmergencyAlertResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string AlertId { get; set; } = string.Empty;
    public DateTime AlertTime { get; set; }
    public List<string> NotifiedContacts { get; set; } = new();
}

public class CompletedTripDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string OriginAddress { get; set; } = string.Empty;
    public string DestinationAddress { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal Earnings { get; set; }
    public double Rating { get; set; }
    public string? CustomerFeedback { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class DriverFeedbackDto
{
    public Guid FeedbackId { get; set; }
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public double Rating { get; set; }
    public string? Comments { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string FeedbackType { get; set; } = string.Empty; // Positive, Negative, Neutral
}

public class DriverRatingStats
{
    public Guid DriverId { get; set; }
    public double AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Star -> Count
    public double RecentRating { get; set; } // Last 30 days
    public int RecentRatingCount { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
}
