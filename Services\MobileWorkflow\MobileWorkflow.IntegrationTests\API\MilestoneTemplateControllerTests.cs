using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace MobileWorkflow.IntegrationTests.API;

public class MilestoneTemplateControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public MilestoneTemplateControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task InitializeAsync()
    {
        // Seed test data for milestone templates
        await _factory.ExecuteDbContextAsync(async context =>
        {
            // Clear existing data
            context.MilestoneTemplates.RemoveRange(context.MilestoneTemplates);
            context.MilestoneSteps.RemoveRange(context.MilestoneSteps);
            context.MilestonePayoutRules.RemoveRange(context.MilestonePayoutRules);
            context.RoleTemplateMappings.RemoveRange(context.RoleTemplateMappings);
            await context.SaveChangesAsync();

            // Add test milestone templates
            var template1 = new Domain.Entities.MilestoneTemplate(
                "Standard Trip Template",
                "Standard milestone template for trip completion",
                "Trip",
                "Logistics",
                "<EMAIL>");

            var step1 = template1.AddStep("Trip Started", "Driver has started the trip", 1, true);
            var step2 = template1.AddStep("Trip Completed", "Driver has completed the trip", 2, true);
            
            step1.AddPayoutRule(30.0m, "status=started", "Payment for starting trip");
            step2.AddPayoutRule(70.0m, "status=completed", "Payment for completing trip");

            var template2 = new Domain.Entities.MilestoneTemplate(
                "Express Delivery Template",
                "Template for express delivery milestones",
                "Delivery",
                "Express",
                "<EMAIL>");

            var step3 = template2.AddStep("Package Picked", "Package picked from sender", 1, true);
            var step4 = template2.AddStep("Package Delivered", "Package delivered to recipient", 2, true);
            
            step3.AddPayoutRule(40.0m, "status=picked", "Payment for pickup");
            step4.AddPayoutRule(60.0m, "status=delivered", "Payment for delivery");

            context.MilestoneTemplates.AddRange(template1, template2);

            // Add role mappings
            var roleMapping1 = new Domain.Entities.RoleTemplateMappings(
                "Driver", template1.Id, "<EMAIL>", true, 100);
            var roleMapping2 = new Domain.Entities.RoleTemplateMappings(
                "DeliveryAgent", template2.Id, "<EMAIL>", true, 100);

            context.RoleTemplateMappings.AddRange(roleMapping1, roleMapping2);

            await context.SaveChangesAsync();
        });
    }

    public Task DisposeAsync() => Task.CompletedTask;

    [Fact]
    public async Task GetMilestoneTemplates_ShouldReturnAllTemplates()
    {
        // Act
        var response = await _client.GetAsync("/api/MilestoneTemplate");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var templates = JsonSerializer.Deserialize<List<MilestoneTemplateDto>>(content, _jsonOptions);
        
        templates.Should().NotBeNull();
        templates.Should().HaveCountGreaterOrEqualTo(2);
        templates.Should().Contain(t => t.Name == "Standard Trip Template");
        templates.Should().Contain(t => t.Name == "Express Delivery Template");
    }

    [Fact]
    public async Task GetMilestoneTemplate_WithValidId_ShouldReturnTemplate()
    {
        // Arrange
        var templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = context.MilestoneTemplates.First(t => t.Name == "Standard Trip Template");
            return template.Id;
        });

        // Act
        var response = await _client.GetAsync($"/api/MilestoneTemplate/{templateId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var template = JsonSerializer.Deserialize<MilestoneTemplateDto>(content, _jsonOptions);
        
        template.Should().NotBeNull();
        template.Name.Should().Be("Standard Trip Template");
        template.Type.Should().Be("Trip");
        template.Category.Should().Be("Logistics");
    }

    [Fact]
    public async Task GetMilestoneTemplate_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/MilestoneTemplate/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateMilestoneTemplate_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var request = new CreateMilestoneTemplateRequest
        {
            Name = "New Test Template",
            Description = "A new test template",
            Type = "Order",
            Category = "Pickup",
            Configuration = new Dictionary<string, object> { { "auto_advance", true } },
            Metadata = new Dictionary<string, object> { { "version", "1.0" } },
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Order Received",
                    Description = "Order has been received",
                    SequenceNumber = 1,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest
                        {
                            PayoutPercentage = 100.0m,
                            Description = "Full payment for order"
                        }
                    }
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/MilestoneTemplate", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var content = await response.Content.ReadAsStringAsync();
        var template = JsonSerializer.Deserialize<MilestoneTemplateDto>(content, _jsonOptions);
        
        template.Should().NotBeNull();
        template.Name.Should().Be(request.Name);
        template.Description.Should().Be(request.Description);
        template.Type.Should().Be(request.Type);
        template.Category.Should().Be(request.Category);

        // Verify in database
        var dbTemplate = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneTemplates
                .FirstOrDefault(t => t.Name == request.Name);
        });

        dbTemplate.Should().NotBeNull();
        dbTemplate!.Steps.Should().HaveCount(1);
        dbTemplate.Steps.First().PayoutRules.Should().HaveCount(1);
    }

    [Fact]
    public async Task CreateMilestoneTemplate_WithDuplicateName_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new CreateMilestoneTemplateRequest
        {
            Name = "Standard Trip Template", // Duplicate name
            Description = "A duplicate template",
            Type = "Trip",
            Category = "Logistics"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/MilestoneTemplate", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateMilestoneTemplate_WithInvalidPayoutPercentages_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new CreateMilestoneTemplateRequest
        {
            Name = "Invalid Payout Template",
            Description = "Template with invalid payout percentages",
            Type = "Trip",
            Category = "Logistics",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 60.0m }
                    }
                },
                new CreateMilestoneStepRequest
                {
                    Name = "Step 2",
                    Description = "Second step",
                    SequenceNumber = 2,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m } // Total = 110%
                    }
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/MilestoneTemplate", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task UpdateMilestoneTemplate_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = context.MilestoneTemplates.First(t => t.Name == "Standard Trip Template");
            return template.Id;
        });

        var request = new UpdateMilestoneTemplateRequest
        {
            Name = "Updated Standard Trip Template",
            Description = "Updated description for standard trip template",
            Configuration = new Dictionary<string, object> { { "updated", true } },
            Metadata = new Dictionary<string, object> { { "version", "2.0" } }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/MilestoneTemplate/{templateId}", request, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var template = JsonSerializer.Deserialize<MilestoneTemplateDto>(content, _jsonOptions);
        
        template.Should().NotBeNull();
        template.Name.Should().Be(request.Name);
        template.Description.Should().Be(request.Description);

        // Verify in database
        var dbTemplate = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneTemplates.FirstOrDefault(t => t.Id == templateId);
        });

        dbTemplate.Should().NotBeNull();
        dbTemplate!.Name.Should().Be(request.Name);
        dbTemplate.Description.Should().Be(request.Description);
    }

    [Fact]
    public async Task ActivateMilestoneTemplate_WithValidId_ShouldActivateSuccessfully()
    {
        // Arrange
        var templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = context.MilestoneTemplates.First(t => t.Name == "Standard Trip Template");
            template.Deactivate();
            await context.SaveChangesAsync();
            return template.Id;
        });

        // Act
        var response = await _client.PostAsync($"/api/MilestoneTemplate/{templateId}/activate", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        var dbTemplate = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneTemplates.FirstOrDefault(t => t.Id == templateId);
        });

        dbTemplate.Should().NotBeNull();
        dbTemplate!.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task DeactivateMilestoneTemplate_WithValidId_ShouldDeactivateSuccessfully()
    {
        // Arrange
        var templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = context.MilestoneTemplates.First(t => t.Name == "Standard Trip Template");
            return template.Id;
        });

        // Act
        var response = await _client.PostAsync($"/api/MilestoneTemplate/{templateId}/deactivate", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        var dbTemplate = await _factory.ExecuteDbContextAsync(async context =>
        {
            return context.MilestoneTemplates.FirstOrDefault(t => t.Id == templateId);
        });

        dbTemplate.Should().NotBeNull();
        dbTemplate!.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateMilestoneTemplate_WithValidTemplate_ShouldReturnValidationResult()
    {
        // Arrange
        var templateId = await _factory.ExecuteDbContextAsync(async context =>
        {
            var template = context.MilestoneTemplates.First(t => t.Name == "Standard Trip Template");
            return template.Id;
        });

        // Act
        var response = await _client.GetAsync($"/api/MilestoneTemplate/{templateId}/validate");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var validationResult = JsonSerializer.Deserialize<MilestoneTemplateValidationResult>(content, _jsonOptions);
        
        validationResult.Should().NotBeNull();
        validationResult.IsValid.Should().BeTrue();
        validationResult.TotalPayoutPercentage.Should().Be(100.0m);
    }

    [Fact]
    public async Task SearchMilestoneTemplates_WithSearchTerm_ShouldReturnMatchingTemplates()
    {
        // Act
        var response = await _client.GetAsync("/api/MilestoneTemplate/search?searchTerm=Trip");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var templates = JsonSerializer.Deserialize<List<MilestoneTemplateDto>>(content, _jsonOptions);
        
        templates.Should().NotBeNull();
        templates.Should().HaveCountGreaterOrEqualTo(1);
        templates.Should().OnlyContain(t => t.Name.Contains("Trip") || t.Description.Contains("Trip"));
    }

    [Fact]
    public async Task GetMilestoneTemplatesByType_WithValidType_ShouldReturnFilteredTemplates()
    {
        // Act
        var response = await _client.GetAsync("/api/MilestoneTemplate?type=Trip");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var templates = JsonSerializer.Deserialize<List<MilestoneTemplateDto>>(content, _jsonOptions);
        
        templates.Should().NotBeNull();
        templates.Should().OnlyContain(t => t.Type == "Trip");
    }
}
