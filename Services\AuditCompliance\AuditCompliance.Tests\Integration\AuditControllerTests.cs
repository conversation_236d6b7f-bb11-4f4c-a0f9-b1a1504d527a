using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using System.Net.Http.Json;
using System.Net;
using Xunit;
using FluentAssertions;
using AuditCompliance.Infrastructure.Persistence;
using AuditCompliance.Application.Commands;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Tests.Integration;

public class AuditControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public AuditControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<AuditComplianceDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add in-memory database for testing
                services.AddDbContext<AuditComplianceDbContext>(options =>
                {
                    options.UseInMemoryDatabase("TestDb");
                });

                // Build the service provider and create the database
                var sp = services.BuildServiceProvider();
                using var scope = sp.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AuditComplianceDbContext>();
                context.Database.EnsureCreated();
            });
        });

        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateAuditLog_WithValidData_ShouldReturnOk()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "User logged in successfully",
            UserId = Guid.NewGuid(),
            UserName = "testuser",
            UserRole = "Admin"
        };

        // Add authorization header (in real scenario, you'd use a proper JWT token)
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.PostAsJsonAsync("/api/audit/logs", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("AuditLogId");
    }

    [Fact]
    public async Task GetAuditLog_WithValidId_ShouldReturnAuditLog()
    {
        // Arrange
        var auditLogId = await CreateTestAuditLog();
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.GetAsync($"/api/audit/logs/{auditLogId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain(auditLogId.ToString());
    }

    [Fact]
    public async Task GetAuditLog_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();
        
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.GetAsync($"/api/audit/logs/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetAuditTrail_WithValidQuery_ShouldReturnResults()
    {
        // Arrange
        await CreateTestAuditLog();
        
        var query = new
        {
            PageNumber = 1,
            PageSize = 10,
            IncludeSensitiveData = false
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.PostAsJsonAsync("/api/audit/trail", query);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("AuditLogs");
        content.Should().Contain("TotalCount");
    }

    [Fact]
    public async Task GetSecurityEvents_WithValidDateRange_ShouldReturnEvents()
    {
        // Arrange
        await CreateTestSecurityEvent();
        
        var fromDate = DateTime.UtcNow.AddDays(-1);
        var toDate = DateTime.UtcNow.AddDays(1);

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        // Act
        var response = await _client.GetAsync($"/api/audit/security-events?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("UnauthorizedAccess");
    }

    [Fact]
    public async Task CreateAuditLog_WithoutAuthorization_ShouldReturnUnauthorized()
    {
        // Arrange
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "User logged in successfully"
        };

        // Act (without authorization header)
        var response = await _client.PostAsJsonAsync("/api/audit/logs", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    private async Task<Guid> CreateTestAuditLog()
    {
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UserLogin,
            Severity = AuditSeverity.Info,
            EntityType = "User",
            Action = "Login",
            Description = "Test user logged in",
            UserId = Guid.NewGuid(),
            UserName = "testuser",
            UserRole = "Admin"
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        var response = await _client.PostAsJsonAsync("/api/audit/logs", command);
        var content = await response.Content.ReadAsStringAsync();
        
        // Extract the GUID from the response (simplified for testing)
        // In a real scenario, you'd parse the JSON response properly
        var startIndex = content.IndexOf("\"AuditLogId\":\"") + 14;
        var endIndex = content.IndexOf("\"", startIndex);
        var guidString = content.Substring(startIndex, endIndex - startIndex);
        
        return Guid.Parse(guidString);
    }

    private async Task<Guid> CreateTestSecurityEvent()
    {
        var command = new CreateAuditLogCommand
        {
            EventType = AuditEventType.UnauthorizedAccess,
            Severity = AuditSeverity.High,
            EntityType = "Security",
            Action = "Unauthorized Access",
            Description = "Failed login attempt detected",
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "test-token");

        var response = await _client.PostAsJsonAsync("/api/audit/logs", command);
        var content = await response.Content.ReadAsStringAsync();
        
        var startIndex = content.IndexOf("\"AuditLogId\":\"") + 14;
        var endIndex = content.IndexOf("\"", startIndex);
        var guidString = content.Substring(startIndex, endIndex - startIndex);
        
        return Guid.Parse(guidString);
    }
}
