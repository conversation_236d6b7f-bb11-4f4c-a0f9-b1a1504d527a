﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;

namespace AuditCompliance.Domain.ValueObjects;

/// <summary>
/// Context information for audit events
/// </summary>
public class AuditContext : ValueObject
{
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public string? SessionId { get; private set; }
    public string? CorrelationId { get; private set; }
    public string? RequestId { get; private set; }
    public Dictionary<string, object> AdditionalData { get; private set; }

    private AuditContext() 
    {
        AdditionalData = new Dictionary<string, object>();
    }

    public AuditContext(
        string? ipAddress = null,
        string? userAgent = null,
        string? sessionId = null,
        string? correlationId = null,
        string? requestId = null,
        Dictionary<string, object>? additionalData = null)
    {
        IpAddress = ipAddress;
        UserAgent = userAgent;
        SessionId = sessionId;
        CorrelationId = correlationId;
        RequestId = requestId;
        AdditionalData = additionalData ?? new Dictionary<string, object>();
    }

    public static AuditContext Create(
        string? ipAddress = null,
        string? userAgent = null,
        string? sessionId = null,
        string? correlationId = null,
        string? requestId = null,
        Dictionary<string, object>? additionalData = null)
    {
        return new AuditContext(ipAddress, userAgent, sessionId, correlationId, requestId, additionalData);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IpAddress ?? string.Empty;
        yield return UserAgent ?? string.Empty;
        yield return SessionId ?? string.Empty;
        yield return CorrelationId ?? string.Empty;
        yield return RequestId ?? string.Empty;
        
        foreach (var kvp in AdditionalData.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

