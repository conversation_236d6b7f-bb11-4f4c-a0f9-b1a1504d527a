using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.CollectDigitalSignature;

public class CollectDigitalSignatureCommandHandler : IRequestHandler<CollectDigitalSignatureCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CollectDigitalSignatureCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;

    public CollectDigitalSignatureCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<CollectDigitalSignatureCommandHandler> logger,
        IMessageBroker messageBroker)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
    }

    public async Task<bool> Handle(CollectDigitalSignatureCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Collecting digital signature for trip stop {TripStopId} by driver {DriverId}", 
                request.TripStopId, request.DriverId);

            // Find the trip stop
            var tripStop = await _tripRepository.GetTripStopByIdAsync(request.TripStopId, cancellationToken);
            if (tripStop == null)
            {
                _logger.LogWarning("Trip stop {TripStopId} not found", request.TripStopId);
                return false;
            }

            // Verify the driver is assigned to this trip
            var trip = await _tripRepository.GetByIdAsync(tripStop.TripId, cancellationToken);
            if (trip == null || trip.DriverId != request.DriverId)
            {
                _logger.LogWarning("Driver {DriverId} is not assigned to trip {TripId}", 
                    request.DriverId, tripStop.TripId);
                return false;
            }

            // Create proof of delivery with digital signature
            var proofOfDelivery = new ProofOfDelivery(
                tripStopId: request.TripStopId,
                recipientName: request.RecipientName,
                recipientSignature: request.SignatureData,
                notes: request.Notes,
                isDigitalSignature: true,
                deliveredBy: request.DriverId.ToString());

            // Add POD to trip stop
            tripStop.AddProofOfDelivery(proofOfDelivery);

            // Mark trip stop as completed if it's a delivery stop
            if (tripStop.StopType == TripStopType.Delivery)
            {
                tripStop.Complete();
            }

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.signature_collected", new
            {
                TripId = trip.Id,
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                RecipientName = request.RecipientName,
                CollectedAt = DateTime.UtcNow,
                IsDigitalSignature = true
            }, cancellationToken);

            _logger.LogInformation("Digital signature collected successfully for trip stop {TripStopId}", 
                request.TripStopId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting digital signature for trip stop {TripStopId}", 
                request.TripStopId);
            throw;
        }
    }
}
