using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Carrier;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Carrier analytics controller for performance, delivery, efficiency, and earnings analytics
/// </summary>
[ApiController]
[Route("api/carrier/analytics")]
[Authorize(Roles = "Carrier,Admin")]
public class CarrierAnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CarrierAnalyticsController> _logger;

    public CarrierAnalyticsController(IMediator mediator, ILogger<CarrierAnalyticsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get carrier dashboard with comprehensive analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Carrier dashboard data</returns>
    [HttpGet("{carrierId}/dashboard")]
    public async Task<ActionResult<CarrierDashboardDto>> GetDashboard(
        Guid carrierId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetCarrierDashboardQuery(carrierId, fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier dashboard for {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier performance analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for performance analysis</param>
    /// <param name="toDate">End date for performance analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeDeliveryMetrics">Include delivery completion metrics</param>
    /// <param name="includeBrokerFeedback">Include broker feedback</param>
    /// <param name="includeImprovementAreas">Include improvement areas</param>
    /// <returns>Carrier performance analytics data</returns>
    [HttpGet("{carrierId}/performance")]
    public async Task<ActionResult<CarrierPerformanceAnalyticsDto>> GetPerformanceAnalytics(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeDeliveryMetrics = true,
        [FromQuery] bool includeBrokerFeedback = true,
        [FromQuery] bool includeImprovementAreas = true)
    {
        try
        {
            var query = new GetCarrierPerformanceAnalyticsQuery(carrierId, fromDate, toDate, period, includeDeliveryMetrics, includeBrokerFeedback, includeImprovementAreas);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance analytics for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier delivery performance
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for delivery analysis</param>
    /// <param name="toDate">End date for delivery analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="routeType">Filter by route type</param>
    /// <param name="includeCustomerFeedback">Include customer feedback</param>
    /// <returns>Carrier delivery performance data</returns>
    [HttpGet("{carrierId}/delivery-performance")]
    public async Task<ActionResult<CarrierDeliveryPerformanceDto>> GetDeliveryPerformance(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? routeType = null,
        [FromQuery] bool includeCustomerFeedback = true)
    {
        try
        {
            var query = new GetCarrierDeliveryPerformanceQuery(carrierId, fromDate, toDate, period, routeType, includeCustomerFeedback);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery performance for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier efficiency tracking
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for efficiency analysis</param>
    /// <param name="toDate">End date for efficiency analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeVehicleUtilization">Include vehicle utilization metrics</param>
    /// <param name="includeFuelEfficiency">Include fuel efficiency metrics</param>
    /// <param name="includeRouteEfficiency">Include route efficiency metrics</param>
    /// <returns>Carrier efficiency tracking data</returns>
    [HttpGet("{carrierId}/efficiency")]
    public async Task<ActionResult<CarrierEfficiencyTrackingDto>> GetEfficiencyTracking(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeVehicleUtilization = true,
        [FromQuery] bool includeFuelEfficiency = true,
        [FromQuery] bool includeRouteEfficiency = true)
    {
        try
        {
            var query = new GetCarrierEfficiencyTrackingQuery(carrierId, fromDate, toDate, period, includeVehicleUtilization, includeFuelEfficiency);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting efficiency tracking for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier earnings analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for earnings analysis</param>
    /// <param name="toDate">End date for earnings analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeIncomeTrends">Include income trends</param>
    /// <param name="includeBenchmarking">Include performance benchmarking</param>
    /// <param name="includeOptimization">Include optimization opportunities</param>
    /// <returns>Carrier earnings analytics data</returns>
    [HttpGet("{carrierId}/earnings")]
    public async Task<ActionResult<CarrierEarningsAnalyticsDto>> GetEarningsAnalytics(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeIncomeTrends = true,
        [FromQuery] bool includeBenchmarking = true,
        [FromQuery] bool includeOptimization = true)
    {
        try
        {
            var query = new GetCarrierEarningsAnalyticsQuery(carrierId, fromDate, toDate, period, includeIncomeTrends, includeBenchmarking);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earnings analytics for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier growth opportunities
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for growth analysis</param>
    /// <param name="toDate">End date for growth analysis</param>
    /// <param name="includeServiceImprovement">Include service improvement opportunities</param>
    /// <param name="includeIncomeOptimization">Include income optimization</param>
    /// <param name="includeMarketExpansion">Include market expansion opportunities</param>
    /// <returns>Carrier growth opportunities data</returns>
    [HttpGet("{carrierId}/growth-opportunities")]
    public async Task<ActionResult<CarrierGrowthOpportunitiesDto>> GetGrowthOpportunities(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] bool includeServiceImprovement = true,
        [FromQuery] bool includeIncomeOptimization = true,
        [FromQuery] bool includeMarketExpansion = true)
    {
        try
        {
            var query = new GetCarrierGrowthOpportunitiesQuery(carrierId, fromDate, toDate, includeServiceImprovement, includeIncomeOptimization, includeMarketExpansion);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting growth opportunities for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier service quality analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for service quality analysis</param>
    /// <param name="toDate">End date for service quality analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeBrokerFeedback">Include broker feedback</param>
    /// <param name="includeCustomerFeedback">Include customer feedback</param>
    /// <returns>Carrier service quality data</returns>
    [HttpGet("{carrierId}/service-quality")]
    public async Task<ActionResult<CarrierServiceQualityDto>> GetServiceQuality(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeBrokerFeedback = true,
        [FromQuery] bool includeCustomerFeedback = true)
    {
        try
        {
            var query = new GetCarrierServiceQualityMonitoringQuery(carrierId, fromDate, toDate, period, includeBrokerFeedback, includeCustomerFeedback);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service quality for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier route performance analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for route analysis</param>
    /// <param name="toDate">End date for route analysis</param>
    /// <param name="originCity">Filter by origin city</param>
    /// <param name="destinationCity">Filter by destination city</param>
    /// <param name="includeEfficiencyMetrics">Include efficiency metrics</param>
    /// <returns>Carrier route performance data</returns>
    [HttpGet("{carrierId}/route-performance")]
    public async Task<ActionResult<CarrierRoutePerformanceDto>> GetRoutePerformance(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? originCity = null,
        [FromQuery] string? destinationCity = null,
        [FromQuery] bool includeEfficiencyMetrics = true)
    {
        try
        {
            var query = new GetCarrierRoutePerformanceQuery(carrierId, fromDate, toDate, originCity, destinationCity, includeEfficiencyMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting route performance for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier competitive benchmarking
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for benchmarking analysis</param>
    /// <param name="toDate">End date for benchmarking analysis</param>
    /// <param name="benchmarkType">Type of benchmarking (performance, earnings, efficiency)</param>
    /// <param name="includeIndustryComparison">Include industry comparison</param>
    /// <param name="includeMarketPositioning">Include market positioning</param>
    /// <returns>Carrier competitive benchmarking data</returns>
    [HttpGet("{carrierId}/competitive-benchmarking")]
    public async Task<ActionResult<CarrierCompetitiveBenchmarkingDto>> GetCompetitiveBenchmarking(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? benchmarkType = null,
        [FromQuery] bool includeIndustryComparison = true,
        [FromQuery] bool includeMarketPositioning = true)
    {
        try
        {
            var query = new GetCarrierCompetitiveBenchmarkingQuery(carrierId, fromDate, toDate, benchmarkType, null, includeIndustryComparison);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competitive benchmarking for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier technology adoption analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for technology analysis</param>
    /// <param name="toDate">End date for technology analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeAppUsage">Include mobile app usage</param>
    /// <param name="includeFeatureAdoption">Include feature adoption</param>
    /// <param name="includeAutomationMetrics">Include automation metrics</param>
    /// <returns>Carrier technology adoption data</returns>
    [HttpGet("{carrierId}/technology-adoption")]
    public async Task<ActionResult<CarrierTechnologyAdoptionDto>> GetTechnologyAdoption(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeAppUsage = true,
        [FromQuery] bool includeFeatureAdoption = true,
        [FromQuery] bool includeAutomationMetrics = true)
    {
        try
        {
            var query = new GetCarrierTechnologyAdoptionQuery(carrierId, fromDate, toDate, period, includeAppUsage, includeFeatureAdoption);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting technology adoption for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier financial performance
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for financial analysis</param>
    /// <param name="toDate">End date for financial analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeEarningsBreakdown">Include earnings breakdown</param>
    /// <param name="includeCostAnalysis">Include cost analysis</param>
    /// <param name="includeProfitabilityAnalysis">Include profitability analysis</param>
    /// <returns>Carrier financial performance data</returns>
    [HttpGet("{carrierId}/financial-performance")]
    public async Task<ActionResult<CarrierFinancialPerformanceDto>> GetFinancialPerformance(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeEarningsBreakdown = true,
        [FromQuery] bool includeCostAnalysis = true,
        [FromQuery] bool includeProfitabilityAnalysis = true)
    {
        try
        {
            var query = new GetCarrierFinancialPerformanceQuery(carrierId, fromDate, toDate, period, includeEarningsBreakdown, includeCostAnalysis, includeProfitabilityAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial performance for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier vehicle utilization analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for utilization analysis</param>
    /// <param name="toDate">End date for utilization analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="vehicleId">Filter by specific vehicle</param>
    /// <param name="includeOptimizationRecommendations">Include optimization recommendations</param>
    /// <returns>Carrier vehicle utilization data</returns>
    [HttpGet("{carrierId}/vehicle-utilization")]
    public async Task<ActionResult<CarrierVehicleUtilizationDto>> GetVehicleUtilization(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] Guid? vehicleId = null,
        [FromQuery] bool includeOptimizationRecommendations = true)
    {
        try
        {
            var query = new GetCarrierVehicleUtilizationQuery(carrierId, fromDate, toDate, period, null, includeOptimizationRecommendations);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle utilization for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier customer satisfaction analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for satisfaction analysis</param>
    /// <param name="toDate">End date for satisfaction analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="customerType">Filter by customer type</param>
    /// <param name="includeFeedbackAnalysis">Include feedback analysis</param>
    /// <returns>Carrier customer satisfaction data</returns>
    [HttpGet("{carrierId}/customer-satisfaction")]
    public async Task<ActionResult<CarrierCustomerSatisfactionDto>> GetCustomerSatisfaction(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? customerType = null,
        [FromQuery] bool includeFeedbackAnalysis = true)
    {
        try
        {
            var query = new GetCarrierCustomerSatisfactionQuery(carrierId, fromDate, toDate, period, includeFeedbackAnalysis, true);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer satisfaction for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get comprehensive carrier performance summary for dashboard
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeEarningsVisualization">Include earnings visualization data</param>
    /// <param name="includeOrderMetrics">Include order metrics</param>
    /// <param name="includeOnTimeDelivery">Include on-time delivery metrics</param>
    /// <param name="includeTrends">Include trend analysis</param>
    /// <param name="includeComparisons">Include performance comparisons</param>
    /// <returns>Comprehensive carrier performance summary</returns>
    [HttpGet("{carrierId}/performance-summary")]
    public async Task<ActionResult<CarrierPerformanceSummaryDto>> GetPerformanceSummary(
        Guid carrierId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeEarningsVisualization = true,
        [FromQuery] bool includeOrderMetrics = true,
        [FromQuery] bool includeOnTimeDelivery = true,
        [FromQuery] bool includeTrends = true,
        [FromQuery] bool includeComparisons = true)
    {
        try
        {
            var query = new GetCarrierPerformanceSummaryQuery(
                carrierId, fromDate, toDate, period,
                includeEarningsVisualization, includeOrderMetrics,
                includeOnTimeDelivery, includeTrends, includeComparisons);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance summary for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get comprehensive carrier quoting history and analytics
    /// </summary>
    /// <param name="carrierId">Carrier ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeRFQResponseHistory">Include RFQ response history</param>
    /// <param name="includeQuoteSuccessRate">Include quote success rate analytics</param>
    /// <param name="includePricingAnalytics">Include pricing analytics</param>
    /// <param name="includeMarketComparison">Include market comparison</param>
    /// <param name="includeTrends">Include trend analysis</param>
    /// <param name="routeFilter">Filter by specific route</param>
    /// <param name="brokerFilter">Filter by specific broker</param>
    /// <returns>Comprehensive carrier quoting history</returns>
    [HttpGet("{carrierId}/quoting-history")]
    public async Task<ActionResult<CarrierQuotingHistoryDto>> GetQuotingHistory(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeRFQResponseHistory = true,
        [FromQuery] bool includeQuoteSuccessRate = true,
        [FromQuery] bool includePricingAnalytics = true,
        [FromQuery] bool includeMarketComparison = true,
        [FromQuery] bool includeTrends = true,
        [FromQuery] string? routeFilter = null,
        [FromQuery] string? brokerFilter = null)
    {
        try
        {
            var query = new GetCarrierQuotingHistoryQuery(
                carrierId, fromDate, toDate, period,
                includeRFQResponseHistory, includeQuoteSuccessRate,
                includePricingAnalytics, includeMarketComparison, includeTrends,
                routeFilter, brokerFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quoting history for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier RFQ response history
    /// </summary>
    [HttpGet("{carrierId}/rfq-response-history")]
    public async Task<ActionResult<CarrierRFQResponseHistoryDto>> GetRFQResponseHistory(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? routeFilter = null,
        [FromQuery] string? brokerFilter = null)
    {
        try
        {
            var query = new GetCarrierRFQResponseHistoryQuery(carrierId, fromDate, toDate, period, routeFilter, brokerFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting RFQ response history for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier quote success rate analytics
    /// </summary>
    [HttpGet("{carrierId}/quote-success-rate")]
    public async Task<ActionResult<CarrierQuoteSuccessRateDto>> GetQuoteSuccessRate(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? routeFilter = null,
        [FromQuery] string? brokerFilter = null)
    {
        try
        {
            var query = new GetCarrierQuoteSuccessRateQuery(carrierId, fromDate, toDate, period, routeFilter, brokerFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quote success rate for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier pricing analytics
    /// </summary>
    [HttpGet("{carrierId}/pricing-analytics")]
    public async Task<ActionResult<CarrierPricingAnalyticsDto>> GetPricingAnalytics(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? routeFilter = null,
        [FromQuery] string? serviceTypeFilter = null)
    {
        try
        {
            var query = new GetCarrierPricingAnalyticsQuery(carrierId, fromDate, toDate, period, routeFilter, serviceTypeFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pricing analytics for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier market comparison analytics
    /// </summary>
    [HttpGet("{carrierId}/market-comparison")]
    public async Task<ActionResult<CarrierMarketComparisonDto>> GetMarketComparison(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? routeFilter = null,
        [FromQuery] string? serviceTypeFilter = null)
    {
        try
        {
            var query = new GetCarrierMarketComparisonQuery(carrierId, fromDate, toDate, routeFilter, serviceTypeFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting market comparison for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get comprehensive carrier ratings and feedback analytics
    /// </summary>
    [HttpGet("{carrierId}/ratings")]
    public async Task<ActionResult<CarrierRatingsAnalyticsDto>> GetRatings(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeBrokerRatings = true,
        [FromQuery] bool includeShipperRatings = true,
        [FromQuery] bool includeRatingTrends = true,
        [FromQuery] bool includeFeedbackAnalysis = true,
        [FromQuery] bool includeCompetitiveAnalysis = true,
        [FromQuery] string? ratingCategory = null,
        [FromQuery] decimal? minRating = null,
        [FromQuery] decimal? maxRating = null)
    {
        try
        {
            var query = new GetCarrierRatingsQuery(
                carrierId, fromDate, toDate, period,
                includeBrokerRatings, includeShipperRatings, includeRatingTrends,
                includeFeedbackAnalysis, includeCompetitiveAnalysis,
                ratingCategory, minRating, maxRating);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ratings for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier rating summary
    /// </summary>
    [HttpGet("{carrierId}/rating-summary")]
    public async Task<ActionResult<CarrierRatingSummaryDto>> GetRatingSummary(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? ratingCategory = null)
    {
        try
        {
            var query = new GetCarrierRatingSummaryQuery(carrierId, fromDate, toDate, ratingCategory);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating summary for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier broker ratings
    /// </summary>
    [HttpGet("{carrierId}/broker-ratings")]
    public async Task<ActionResult<CarrierBrokerRatingsDto>> GetBrokerRatings(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? brokerFilter = null)
    {
        try
        {
            var query = new GetCarrierBrokerRatingsQuery(carrierId, fromDate, toDate, period, brokerFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting broker ratings for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier shipper ratings
    /// </summary>
    [HttpGet("{carrierId}/shipper-ratings")]
    public async Task<ActionResult<CarrierShipperRatingsDto>> GetShipperRatings(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? shipperFilter = null)
    {
        try
        {
            var query = new GetCarrierShipperRatingsQuery(carrierId, fromDate, toDate, period, shipperFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipper ratings for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier rating trends
    /// </summary>
    [HttpGet("{carrierId}/rating-trends")]
    public async Task<ActionResult<CarrierRatingTrendsDto>> GetRatingTrends(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? category = null)
    {
        try
        {
            var query = new GetCarrierRatingTrendsQuery(carrierId, fromDate, toDate, period, category);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating trends for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier feedback analysis
    /// </summary>
    [HttpGet("{carrierId}/feedback-analysis")]
    public async Task<ActionResult<CarrierFeedbackAnalysisDto>> GetFeedbackAnalysis(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? feedbackSource = null,
        [FromQuery] string? sentimentFilter = null)
    {
        try
        {
            var query = new GetCarrierFeedbackAnalysisQuery(carrierId, fromDate, toDate, feedbackSource, sentimentFilter);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feedback analysis for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier rating competitive analysis
    /// </summary>
    [HttpGet("{carrierId}/rating-competitive-analysis")]
    public async Task<ActionResult<CarrierRatingCompetitiveAnalysisDto>> GetRatingCompetitiveAnalysis(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? marketSegment = null,
        [FromQuery] string? geographicRegion = null)
    {
        try
        {
            var query = new GetCarrierRatingCompetitiveAnalysisQuery(carrierId, fromDate, toDate, marketSegment, geographicRegion);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating competitive analysis for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }
}
