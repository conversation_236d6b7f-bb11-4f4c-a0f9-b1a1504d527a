using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class Geofence : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Type { get; private set; } // Circular, Polygon, Route
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Coordinates { get; private set; }
    public double? Radius { get; private set; } // For circular geofences
    public string TriggerType { get; private set; } // Enter, Exit, Dwell, Both
    public TimeSpan? DwellTime { get; private set; } // Minimum time to trigger dwell
    public Dictionary<string, object> TriggerActions { get; private set; }
    public Dictionary<string, object> Conditions { get; private set; }
    public List<string> TargetUserGroups { get; private set; }
    public List<Guid> TargetUserIds { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<GeofenceEvent> Events { get; private set; } = new List<GeofenceEvent>();
    public virtual ICollection<UserGeofenceStatus> UserStatuses { get; private set; } = new List<UserGeofenceStatus>();

    private Geofence() { } // EF Core

    public Geofence(
        string name,
        string description,
        string type,
        Dictionary<string, object> coordinates,
        string triggerType,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Coordinates = coordinates ?? throw new ArgumentNullException(nameof(coordinates));
        TriggerType = triggerType ?? throw new ArgumentNullException(nameof(triggerType));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        TriggerActions = new Dictionary<string, object>();
        Conditions = new Dictionary<string, object>();
        TargetUserGroups = new List<string>();
        TargetUserIds = new List<Guid>();
        Metadata = new Dictionary<string, object>();

        ValidateGeofenceData();

        AddDomainEvent(new GeofenceCreatedEvent(Id, Name, Type, TriggerType, CreatedBy));
    }

    public void UpdateCoordinates(Dictionary<string, object> coordinates, double? radius = null)
    {
        Coordinates = coordinates ?? throw new ArgumentNullException(nameof(coordinates));
        Radius = radius;
        UpdatedAt = DateTime.UtcNow;

        ValidateGeofenceData();

        AddDomainEvent(new GeofenceCoordinatesUpdatedEvent(Id, Name, Type));
    }

    public void UpdateTriggerSettings(string triggerType, TimeSpan? dwellTime = null, Dictionary<string, object>? triggerActions = null)
    {
        TriggerType = triggerType ?? throw new ArgumentNullException(nameof(triggerType));
        DwellTime = dwellTime;

        if (triggerActions != null)
        {
            TriggerActions = triggerActions;
        }

        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new GeofenceTriggerSettingsUpdatedEvent(Id, Name, TriggerType, DwellTime));
    }

    public void UpdateConditions(Dictionary<string, object> conditions)
    {
        Conditions = conditions ?? throw new ArgumentNullException(nameof(conditions));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new GeofenceConditionsUpdatedEvent(Id, Name));
    }

    public void AddTargetUser(Guid userId)
    {
        if (!TargetUserIds.Contains(userId))
        {
            TargetUserIds.Add(userId);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GeofenceUserTargetedEvent(Id, Name, userId));
        }
    }

    public void RemoveTargetUser(Guid userId)
    {
        if (TargetUserIds.Remove(userId))
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GeofenceUserUntargetedEvent(Id, Name, userId));
        }
    }

    public void AddTargetUserGroup(string groupName)
    {
        if (!TargetUserGroups.Contains(groupName, StringComparer.OrdinalIgnoreCase))
        {
            TargetUserGroups.Add(groupName);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GeofenceGroupTargetedEvent(Id, Name, groupName));
        }
    }

    public void RemoveTargetUserGroup(string groupName)
    {
        if (TargetUserGroups.RemoveAll(g => g.Equals(groupName, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new GeofenceGroupUntargetedEvent(Id, Name, groupName));
        }
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new GeofenceActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new GeofenceDeactivatedEvent(Id, Name));
    }

    public bool IsUserTargeted(Guid userId, List<string> userGroups)
    {
        // If no specific targets, applies to all users
        if (TargetUserIds.Count == 0 && TargetUserGroups.Count == 0)
        {
            return true;
        }

        // Check direct user targeting
        if (TargetUserIds.Contains(userId))
        {
            return true;
        }

        // Check group targeting
        return TargetUserGroups.Any(targetGroup =>
            userGroups.Contains(targetGroup, StringComparer.OrdinalIgnoreCase));
    }

    public bool IsLocationInside(double latitude, double longitude)
    {
        return Type.ToLowerInvariant() switch
        {
            "circular" => IsLocationInCircle(latitude, longitude),
            "polygon" => IsLocationInPolygon(latitude, longitude),
            "route" => IsLocationOnRoute(latitude, longitude),
            _ => false
        };
    }

    public double CalculateDistanceToCenter(double latitude, double longitude)
    {
        if (Type.ToLowerInvariant() != "circular" ||
            !Coordinates.TryGetValue("latitude", out var latObj) ||
            !Coordinates.TryGetValue("longitude", out var lngObj) ||
            latObj is not double centerLat ||
            lngObj is not double centerLng)
        {
            return double.MaxValue;
        }

        return CalculateHaversineDistance(latitude, longitude, centerLat, centerLng);
    }

    public bool EvaluateConditions(Dictionary<string, object> context)
    {
        if (Conditions.Count == 0)
        {
            return true; // No conditions means always applies
        }

        foreach (var condition in Conditions)
        {
            if (!EvaluateCondition(condition.Key, condition.Value, context))
            {
                return false;
            }
        }

        return true;
    }

    private void ValidateGeofenceData()
    {
        switch (Type.ToLowerInvariant())
        {
            case "circular":
                if (!Coordinates.ContainsKey("latitude") || !Coordinates.ContainsKey("longitude"))
                {
                    throw new InvalidOperationException("Circular geofence requires latitude and longitude coordinates");
                }
                if (!Radius.HasValue || Radius.Value <= 0)
                {
                    throw new InvalidOperationException("Circular geofence requires a positive radius");
                }
                break;

            case "polygon":
                if (!Coordinates.ContainsKey("points") || Coordinates["points"] is not List<object> points || points.Count < 3)
                {
                    throw new InvalidOperationException("Polygon geofence requires at least 3 coordinate points");
                }
                break;

            case "route":
                if (!Coordinates.ContainsKey("waypoints") || Coordinates["waypoints"] is not List<object> waypoints || waypoints.Count < 2)
                {
                    throw new InvalidOperationException("Route geofence requires at least 2 waypoints");
                }
                break;

            default:
                throw new InvalidOperationException($"Unsupported geofence type: {Type}");
        }
    }

    private bool IsLocationInCircle(double latitude, double longitude)
    {
        if (!Coordinates.TryGetValue("latitude", out var latObj) ||
            !Coordinates.TryGetValue("longitude", out var lngObj) ||
            latObj is not double centerLat ||
            lngObj is not double centerLng ||
            !Radius.HasValue)
        {
            return false;
        }

        var distance = CalculateHaversineDistance(latitude, longitude, centerLat, centerLng);
        return distance <= Radius.Value;
    }

    private bool IsLocationInPolygon(double latitude, double longitude)
    {
        if (!Coordinates.TryGetValue("points", out var pointsObj) || pointsObj is not List<object> points)
        {
            return false;
        }

        var polygonPoints = new List<(double lat, double lng)>();
        foreach (var point in points)
        {
            if (point is Dictionary<string, object> pointDict &&
                pointDict.TryGetValue("latitude", out var latObj) &&
                pointDict.TryGetValue("longitude", out var lngObj) &&
                latObj is double lat && lngObj is double lng)
            {
                polygonPoints.Add((lat, lng));
            }
        }

        return IsPointInPolygon(latitude, longitude, polygonPoints);
    }

    private bool IsLocationOnRoute(double latitude, double longitude)
    {
        if (!Coordinates.TryGetValue("waypoints", out var waypointsObj) || waypointsObj is not List<object> waypoints)
        {
            return false;
        }

        var routePoints = new List<(double lat, double lng)>();
        foreach (var waypoint in waypoints)
        {
            if (waypoint is Dictionary<string, object> waypointDict &&
                waypointDict.TryGetValue("latitude", out var latObj) &&
                waypointDict.TryGetValue("longitude", out var lngObj) &&
                latObj is double lat && lngObj is double lng)
            {
                routePoints.Add((lat, lng));
            }
        }

        var tolerance = GetMetadata<double>("route_tolerance", 100.0); // 100 meters default

        for (int i = 0; i < routePoints.Count - 1; i++)
        {
            var distance = DistanceToLineSegment(latitude, longitude, routePoints[i], routePoints[i + 1]);
            if (distance <= tolerance)
            {
                return true;
            }
        }

        return false;
    }

    private double CalculateHaversineDistance(double lat1, double lng1, double lat2, double lng2)
    {
        const double R = 6371000; // Earth's radius in meters
        var dLat = ToRadians(lat2 - lat1);
        var dLng = ToRadians(lng2 - lng1);
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    private bool IsPointInPolygon(double latitude, double longitude, List<(double lat, double lng)> polygon)
    {
        var inside = false;
        var j = polygon.Count - 1;

        for (int i = 0; i < polygon.Count; j = i++)
        {
            if (((polygon[i].lng > longitude) != (polygon[j].lng > longitude)) &&
                (latitude < (polygon[j].lat - polygon[i].lat) * (longitude - polygon[i].lng) / (polygon[j].lng - polygon[i].lng) + polygon[i].lat))
            {
                inside = !inside;
            }
        }

        return inside;
    }

    private double DistanceToLineSegment(double pointLat, double pointLng, (double lat, double lng) lineStart, (double lat, double lng) lineEnd)
    {
        var A = pointLat - lineStart.lat;
        var B = pointLng - lineStart.lng;
        var C = lineEnd.lat - lineStart.lat;
        var D = lineEnd.lng - lineStart.lng;

        var dot = A * C + B * D;
        var lenSq = C * C + D * D;
        var param = lenSq != 0 ? dot / lenSq : -1;

        double xx, yy;

        if (param < 0)
        {
            xx = lineStart.lat;
            yy = lineStart.lng;
        }
        else if (param > 1)
        {
            xx = lineEnd.lat;
            yy = lineEnd.lng;
        }
        else
        {
            xx = lineStart.lat + param * C;
            yy = lineStart.lng + param * D;
        }

        return CalculateHaversineDistance(pointLat, pointLng, xx, yy);
    }

    private double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private bool EvaluateCondition(string conditionKey, object conditionValue, Dictionary<string, object> context)
    {
        if (!context.TryGetValue(conditionKey, out var contextValue))
        {
            return false;
        }

        if (conditionValue is Dictionary<string, object> conditionDict)
        {
            // Handle complex conditions like time ranges, date ranges, etc.
            if (conditionDict.TryGetValue("min", out var minValue) && conditionDict.TryGetValue("max", out var maxValue))
            {
                if (contextValue is IComparable comparableContext &&
                    minValue is IComparable comparableMin &&
                    maxValue is IComparable comparableMax)
                {
                    return comparableContext.CompareTo(comparableMin) >= 0 &&
                           comparableContext.CompareTo(comparableMax) <= 0;
                }
            }
        }
        else if (conditionValue is List<object> conditionList)
        {
            return conditionList.Contains(contextValue);
        }
        else
        {
            return Equals(conditionValue, contextValue);
        }

        return false;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetCoordinate<T>(string key, T? defaultValue = default)
    {
        if (Coordinates.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetTriggerAction<T>(string key, T? defaultValue = default)
    {
        if (TriggerActions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetCondition<T>(string key, T? defaultValue = default)
    {
        if (Conditions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


