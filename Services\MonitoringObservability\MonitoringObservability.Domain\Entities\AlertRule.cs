using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents an advanced alerting rule with complex conditions and routing
/// </summary>
public class AlertRule : AggregateRoot
{
    private readonly List<AlertRuleCondition> _conditions = new();
    private readonly List<AlertRuleAction> _actions = new();
    private readonly List<AlertRuleEscalation> _escalations = new();

    public AlertRule(string name, string description, AlertRuleType ruleType, 
        AlertSeverity defaultSeverity, bool isEnabled = true)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        RuleType = ruleType;
        DefaultSeverity = defaultSeverity;
        IsEnabled = isEnabled;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        Priority = 100; // Default priority
        EvaluationWindow = TimeSpan.FromMinutes(5);
        CooldownPeriod = TimeSpan.FromMinutes(15);
        Tags = new Dictionary<string, string>();
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AlertRule() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public AlertRuleType RuleType { get; private set; }
    public AlertSeverity DefaultSeverity { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; } // Higher number = higher priority
    public TimeSpan EvaluationWindow { get; private set; }
    public TimeSpan CooldownPeriod { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastTriggered { get; private set; }
    public int TriggerCount { get; private set; }
    public string? ServiceName { get; private set; }
    public string? MetricName { get; private set; }
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<AlertRuleCondition> Conditions => _conditions.AsReadOnly();
    public IReadOnlyList<AlertRuleAction> Actions => _actions.AsReadOnly();
    public IReadOnlyList<AlertRuleEscalation> Escalations => _escalations.AsReadOnly();

    // Computed properties
    public bool IsInCooldown => LastTriggered.HasValue && 
                               DateTime.UtcNow - LastTriggered.Value < CooldownPeriod;
    public bool HasComplexConditions => _conditions.Count > 1 || 
                                       _conditions.Any(c => c.ConditionType == AlertConditionType.Composite);

    public void UpdateRule(string? name = null, string? description = null, 
        AlertSeverity? defaultSeverity = null, bool? isEnabled = null, int? priority = null)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;
        
        if (!string.IsNullOrWhiteSpace(description))
            Description = description;
        
        if (defaultSeverity.HasValue)
            DefaultSeverity = defaultSeverity.Value;
        
        if (isEnabled.HasValue)
            IsEnabled = isEnabled.Value;
        
        if (priority.HasValue)
            Priority = priority.Value;

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AlertRuleUpdatedEvent(Id, Name));
    }

    public void SetServiceScope(string serviceName, string? metricName = null)
    {
        ServiceName = serviceName;
        MetricName = metricName;
        LastModified = DateTime.UtcNow;
    }

    public void SetTimingConfiguration(TimeSpan evaluationWindow, TimeSpan cooldownPeriod)
    {
        EvaluationWindow = evaluationWindow;
        CooldownPeriod = cooldownPeriod;
        LastModified = DateTime.UtcNow;
    }

    public void AddCondition(AlertRuleCondition condition)
    {
        if (condition == null) throw new ArgumentNullException(nameof(condition));
        
        _conditions.Add(condition);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AlertRuleConditionAddedEvent(Id, condition.Id));
    }

    public void RemoveCondition(Guid conditionId)
    {
        var condition = _conditions.FirstOrDefault(c => c.Id == conditionId);
        if (condition != null)
        {
            _conditions.Remove(condition);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AlertRuleConditionRemovedEvent(Id, conditionId));
        }
    }

    public void AddAction(AlertRuleAction action)
    {
        if (action == null) throw new ArgumentNullException(nameof(action));
        
        _actions.Add(action);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AlertRuleActionAddedEvent(Id, action.Id));
    }

    public void RemoveAction(Guid actionId)
    {
        var action = _actions.FirstOrDefault(a => a.Id == actionId);
        if (action != null)
        {
            _actions.Remove(action);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AlertRuleActionRemovedEvent(Id, actionId));
        }
    }

    public void AddEscalation(AlertRuleEscalation escalation)
    {
        if (escalation == null) throw new ArgumentNullException(nameof(escalation));
        
        _escalations.Add(escalation);
        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AlertRuleEscalationAddedEvent(Id, escalation.Id));
    }

    public void RemoveEscalation(Guid escalationId)
    {
        var escalation = _escalations.FirstOrDefault(e => e.Id == escalationId);
        if (escalation != null)
        {
            _escalations.Remove(escalation);
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AlertRuleEscalationRemovedEvent(Id, escalationId));
        }
    }

    public void RecordTrigger()
    {
        LastTriggered = DateTime.UtcNow;
        TriggerCount++;
        
        AddDomainEvent(new AlertRuleTriggeredEvent(Id, Name, TriggerCount));
    }

    public void Enable()
    {
        if (!IsEnabled)
        {
            IsEnabled = true;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AlertRuleEnabledEvent(Id, Name));
        }
    }

    public void Disable()
    {
        if (IsEnabled)
        {
            IsEnabled = false;
            LastModified = DateTime.UtcNow;
            
            AddDomainEvent(new AlertRuleDisabledEvent(Id, Name));
        }
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
        LastModified = DateTime.UtcNow;
    }

    public void RemoveTag(string key)
    {
        if (Tags.Remove(key))
        {
            LastModified = DateTime.UtcNow;
        }
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Metadata key cannot be null or empty", nameof(key));
        Metadata[key] = value;
        LastModified = DateTime.UtcNow;
    }

    public bool ShouldEvaluate()
    {
        return IsEnabled && !IsInCooldown;
    }

    public AlertRuleEscalation? GetNextEscalation(int currentLevel)
    {
        return _escalations
            .Where(e => e.Level > currentLevel)
            .OrderBy(e => e.Level)
            .FirstOrDefault();
    }

    public IEnumerable<AlertRuleAction> GetActionsForSeverity(AlertSeverity severity)
    {
        return _actions.Where(a => a.TriggerSeverity == null || a.TriggerSeverity == severity);
    }
}

/// <summary>
/// Represents a condition within an alert rule
/// </summary>
public class AlertRuleCondition
{
    public AlertRuleCondition(AlertConditionType conditionType, string expression, 
        AlertConditionOperator conditionOperator = AlertConditionOperator.And)
    {
        Id = Guid.NewGuid();
        ConditionType = conditionType;
        Expression = expression ?? throw new ArgumentNullException(nameof(expression));
        Operator = conditionOperator;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
        Parameters = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AlertRuleCondition() { }

    public Guid Id { get; private set; }
    public AlertConditionType ConditionType { get; private set; }
    public string Expression { get; private set; } = string.Empty;
    public AlertConditionOperator Operator { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; } = new();

    // Navigation properties
    public AlertRule AlertRule { get; private set; } = null!;

    public void UpdateExpression(string expression)
    {
        Expression = expression ?? throw new ArgumentNullException(nameof(expression));
    }

    public void SetParameter(string key, object value)
    {
        Parameters[key] = value;
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;
}

/// <summary>
/// Represents an action to be taken when an alert rule is triggered
/// </summary>
public class AlertRuleAction
{
    public AlertRuleAction(AlertActionType actionType, Dictionary<string, object> parameters,
        AlertSeverity? triggerSeverity = null)
    {
        Id = Guid.NewGuid();
        ActionType = actionType;
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        TriggerSeverity = triggerSeverity;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private AlertRuleAction() { }

    public Guid Id { get; private set; }
    public AlertActionType ActionType { get; private set; }
    public AlertSeverity? TriggerSeverity { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; } = new();
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation properties
    public AlertRule AlertRule { get; private set; } = null!;

    public void UpdateParameters(Dictionary<string, object> parameters)
    {
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
    }

    public void SetParameter(string key, object value)
    {
        Parameters[key] = value;
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;
}

/// <summary>
/// Represents an escalation level for an alert rule
/// </summary>
public class AlertRuleEscalation
{
    public AlertRuleEscalation(int level, TimeSpan delay, List<NotificationChannel> channels,
        List<string> recipients, AlertSeverity? escalatedSeverity = null)
    {
        Id = Guid.NewGuid();
        Level = level;
        Delay = delay;
        Channels = channels ?? throw new ArgumentNullException(nameof(channels));
        Recipients = recipients ?? throw new ArgumentNullException(nameof(recipients));
        EscalatedSeverity = escalatedSeverity;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private AlertRuleEscalation() { }

    public Guid Id { get; private set; }
    public int Level { get; private set; }
    public TimeSpan Delay { get; private set; }
    public List<NotificationChannel> Channels { get; private set; } = new();
    public List<string> Recipients { get; private set; } = new();
    public AlertSeverity? EscalatedSeverity { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation properties
    public AlertRule AlertRule { get; private set; } = null!;

    public void UpdateConfiguration(TimeSpan delay, List<NotificationChannel> channels, 
        List<string> recipients, AlertSeverity? escalatedSeverity = null)
    {
        Delay = delay;
        Channels = channels ?? throw new ArgumentNullException(nameof(channels));
        Recipients = recipients ?? throw new ArgumentNullException(nameof(recipients));
        EscalatedSeverity = escalatedSeverity;
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;
}
