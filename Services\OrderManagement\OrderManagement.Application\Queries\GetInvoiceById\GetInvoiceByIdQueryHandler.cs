using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Queries.GetInvoiceById;

public class GetInvoiceByIdQueryHandler : IRequestHandler<GetInvoiceByIdQuery, InvoiceDetailDto?>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInvoiceByIdQueryHandler> _logger;

    public GetInvoiceByIdQueryHandler(
        IInvoiceRepository invoiceRepository,
        IOrderRepository orderRepository,
        IMapper mapper,
        ILogger<GetInvoiceByIdQueryHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _orderRepository = orderRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<InvoiceDetailDto?> Handle(GetInvoiceByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting invoice {InvoiceId} for user {UserId}", 
            request.InvoiceId, request.UserId);

        try
        {
            var invoice = await _invoiceRepository.GetByIdAsync(request.InvoiceId, cancellationToken);
            
            if (invoice == null)
            {
                _logger.LogWarning("Invoice {InvoiceId} not found", request.InvoiceId);
                return null;
            }

            // Get the associated order for authorization
            var order = await _orderRepository.GetByIdAsync(invoice.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for invoice {InvoiceId}", 
                    invoice.OrderId, request.InvoiceId);
                throw new OrderManagementException("Associated order not found");
            }

            // Authorization check - only involved parties can view invoice details
            if (request.UserId.HasValue)
            {
                if (!IsAuthorizedToViewInvoice(order, request.UserId.Value))
                {
                    _logger.LogWarning("User {UserId} is not authorized to view invoice {InvoiceId}", 
                        request.UserId, request.InvoiceId);
                    throw new OrderManagementException("Not authorized to view this invoice");
                }
            }

            var invoiceDto = _mapper.Map<InvoiceDetailDto>(invoice);
            
            _logger.LogInformation("Successfully retrieved invoice {InvoiceId} for user {UserId}", 
                request.InvoiceId, request.UserId);
            
            return invoiceDto;
        }
        catch (OrderManagementException)
        {
            throw; // Re-throw business exceptions
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving invoice {InvoiceId} for user {UserId}", 
                request.InvoiceId, request.UserId);
            throw;
        }
    }

    private static bool IsAuthorizedToViewInvoice(Domain.Entities.Order order, Guid userId)
    {
        // Users can view invoices for orders they are involved in
        return order.TransportCompanyId == userId ||
               order.BrokerId == userId ||
               order.CarrierId == userId;
    }
}
