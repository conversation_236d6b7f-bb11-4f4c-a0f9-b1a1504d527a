using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetTaxCategories
{
    public class GetTaxCategoriesQueryHandler : IRequestHandler<GetTaxCategoriesQuery, List<TaxCategoryDto>>
    {
        private readonly ITaxCategoryRepository _taxCategoryRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<GetTaxCategoriesQueryHandler> _logger;

        public GetTaxCategoriesQueryHandler(
            ITaxCategoryRepository taxCategoryRepository,
            IMapper mapper,
            ILogger<GetTaxCategoriesQueryHandler> logger)
        {
            _taxCategoryRepository = taxCategoryRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<TaxCategoryDto>> Handle(GetTaxCategoriesQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting tax categories. ActiveOnly: {ActiveOnly}, Region: {Region}",
                request.ActiveOnly, request.Region);

            try
            {
                var taxCategories = request.ActiveOnly 
                    ? await _taxCategoryRepository.GetActiveAsync()
                    : await _taxCategoryRepository.GetAllAsync();

                // Filter by region if specified
                if (!string.IsNullOrEmpty(request.Region))
                {
                    var regionCategories = await _taxCategoryRepository.GetByRegionAsync(request.Region);
                    taxCategories = taxCategories.Intersect(regionCategories).ToList();
                }

                // Apply search filter if specified
                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchResults = await _taxCategoryRepository.SearchAsync(request.SearchTerm, request.Page, request.PageSize);
                    taxCategories = taxCategories.Intersect(searchResults).ToList();
                }

                // Apply pagination
                var pagedCategories = taxCategories
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                var result = _mapper.Map<List<TaxCategoryDto>>(pagedCategories);

                _logger.LogInformation("Successfully retrieved {Count} tax categories", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tax categories");
                throw;
            }
        }
    }
}
