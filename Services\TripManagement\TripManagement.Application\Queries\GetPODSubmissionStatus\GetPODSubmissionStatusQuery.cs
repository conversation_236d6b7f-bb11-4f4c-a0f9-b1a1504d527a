using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Repositories;

namespace TripManagement.Application.Queries.GetPODSubmissionStatus;

public class GetPODSubmissionStatusQuery : IRequest<GetPODSubmissionStatusResponse>
{
    public Guid PODId { get; set; }
    public Guid? TripId { get; set; }
    public Guid DriverId { get; set; }
    public bool IncludeDocuments { get; set; } = true;
    public bool IncludeProcessingHistory { get; set; } = true;
}

public class GetPODSubmissionStatusResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODSubmissionStatusDto? Status { get; set; }
}

public class PODSubmissionStatusDto
{
    public Guid PODId { get; set; }
    public string PODNumber { get; set; } = string.Empty;
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;

    // Status information
    public string Status { get; set; } = string.Empty;
    public string StatusDescription { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public DateTime? RejectedAt { get; set; }

    // Processing information
    public PODProcessingStatusInfo ProcessingInfo { get; set; } = new();

    // Delivery information
    public PODDeliveryInfo DeliveryInfo { get; set; } = new();

    // Documents and signature
    public List<PODDocumentInfo> Documents { get; set; } = new();
    public PODSignatureInfo? Signature { get; set; }

    // Verification and approval
    public PODVerificationInfo? VerificationInfo { get; set; }
    public PODApprovalInfo? ApprovalInfo { get; set; }

    // Issues and feedback
    public List<PODIssue> Issues { get; set; } = new();
    public List<PODFeedback> Feedback { get; set; } = new();

    // Actions available
    public List<string> AvailableActions { get; set; } = new();

    public DateTime LastUpdated { get; set; }
}

public class PODProcessingStatusInfo
{
    public string CurrentStage { get; set; } = string.Empty;
    public double OverallProgress { get; set; }
    public List<ProcessingStageInfo> Stages { get; set; } = new();
    public string? CurrentActivity { get; set; }
    public DateTime? EstimatedCompletionTime { get; set; }
    public bool IsProcessingComplete { get; set; }
    public string? ProcessingError { get; set; }
}

public class ProcessingStageInfo
{
    public string StageName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, InProgress, Completed, Failed, Skipped
    public double Progress { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? Description { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> SubActivities { get; set; } = new();
}

public class PODDeliveryInfo
{
    public string RecipientName { get; set; } = string.Empty;
    public string? RecipientTitle { get; set; }
    public string? RecipientPhone { get; set; }
    public string? RecipientEmail { get; set; }
    public DateTime DeliveryDateTime { get; set; }
    public string? DeliveryAddress { get; set; }
    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }
    public string? DeliveryNotes { get; set; }
    public List<string> DeliveryConditions { get; set; } = new();
    public bool LocationVerified { get; set; }
    public bool RecipientVerified { get; set; }
    public string? VerificationMethod { get; set; }
}

public class PODDocumentInfo
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Status { get; set; } = string.Empty; // Uploaded, Processing, Verified, Rejected
    public DateTime UploadedAt { get; set; }
    public string? Description { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string? DownloadUrl { get; set; }
    public List<string> ProcessingResults { get; set; } = new();
    public List<string> ValidationMessages { get; set; } = new();
}

public class PODSignatureInfo
{
    public Guid SignatureId { get; set; }
    public string SignerName { get; set; } = string.Empty;
    public string? SignerTitle { get; set; }
    public DateTime SignedAt { get; set; }
    public string SignatureFormat { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Valid, Invalid, Pending Verification
    public string? PreviewUrl { get; set; }
    public bool IsDigitalSignature { get; set; }
    public List<string> ValidationResults { get; set; } = new();
}

public class PODVerificationInfo
{
    public Guid VerificationId { get; set; }
    public string VerificationStatus { get; set; } = string.Empty; // Pending, InProgress, Completed, Failed
    public DateTime? VerificationStartedAt { get; set; }
    public DateTime? VerificationCompletedAt { get; set; }
    public string? VerifiedBy { get; set; }
    public string? VerificationMethod { get; set; } // Automated, Manual, Hybrid
    public List<VerificationCheck> Checks { get; set; } = new();
    public double VerificationScore { get; set; }
    public string? VerificationNotes { get; set; }
}

public class VerificationCheck
{
    public string CheckType { get; set; } = string.Empty;
    public string CheckName { get; set; } = string.Empty;
    public string Result { get; set; } = string.Empty; // Pass, Fail, Warning, Skipped
    public string? Details { get; set; }
    public double? Score { get; set; }
}

public class PODApprovalInfo
{
    public Guid ApprovalId { get; set; }
    public string ApprovalStatus { get; set; } = string.Empty; // Pending, Approved, Rejected, Escalated
    public DateTime? ApprovalRequestedAt { get; set; }
    public DateTime? ApprovalCompletedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public string? ApproverRole { get; set; }
    public string? ApprovalNotes { get; set; }
    public string? RejectionReason { get; set; }
    public List<ApprovalWorkflowStep> WorkflowSteps { get; set; } = new();
}

public class ApprovalWorkflowStep
{
    public string StepName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? AssignedTo { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? Comments { get; set; }
}

public class PODIssue
{
    public Guid IssueId { get; set; }
    public string IssueType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Low, Medium, High, Critical
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Open, InProgress, Resolved, Closed
    public DateTime ReportedAt { get; set; }
    public string? ReportedBy { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? Resolution { get; set; }
    public List<string> AffectedComponents { get; set; } = new();
}

public class PODFeedback
{
    public Guid FeedbackId { get; set; }
    public string FeedbackType { get; set; } = string.Empty; // Quality, Process, System, General
    public string? Title { get; set; }
    public string Message { get; set; } = string.Empty;
    public double? Rating { get; set; }
    public DateTime SubmittedAt { get; set; }
    public string? SubmittedBy { get; set; }
    public string? SubmitterRole { get; set; }
    public bool IsPublic { get; set; }
    public List<string> Tags { get; set; } = new();
}

// Query Handler

public class GetPODSubmissionStatusQueryHandler : IRequestHandler<GetPODSubmissionStatusQuery, GetPODSubmissionStatusResponse>
{
    private readonly IPODRepository _podRepository;
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly ILogger<GetPODSubmissionStatusQueryHandler> _logger;

    public GetPODSubmissionStatusQueryHandler(
        IPODRepository podRepository,
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        ILogger<GetPODSubmissionStatusQueryHandler> logger)
    {
        _podRepository = podRepository;
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _logger = logger;
    }

    public async Task<GetPODSubmissionStatusResponse> Handle(GetPODSubmissionStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting POD submission status for POD {PODId}", request.PODId);

            // Get POD
            var pod = await _podRepository.GetByIdAsync(request.PODId, cancellationToken);
            if (pod == null)
            {
                return new GetPODSubmissionStatusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "POD not found"
                };
            }

            // Verify driver access
            if (pod.DriverId != request.DriverId)
            {
                return new GetPODSubmissionStatusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = "Access denied"
                };
            }

            // Get related data
            var trip = await _tripRepository.GetByIdAsync(pod.TripId, cancellationToken);
            var driver = await _driverRepository.GetByIdAsync(pod.DriverId, cancellationToken);

            // Build status DTO
            var status = new PODSubmissionStatusDto
            {
                PODId = pod.Id,
                PODNumber = pod.PODNumber,
                TripId = pod.TripId,
                TripNumber = trip?.TripNumber ?? "Unknown",
                DriverId = pod.DriverId,
                DriverName = driver?.FullName ?? "Unknown",
                Status = pod.Status.ToString(),
                StatusDescription = GetStatusDescription(pod.Status),
                SubmittedAt = pod.SubmittedAt,
                ProcessedAt = pod.ProcessedAt,
                LastUpdated = pod.UpdatedAt,

                ProcessingInfo = BuildProcessingInfo(pod),
                DeliveryInfo = BuildDeliveryInfo(pod),
                AvailableActions = GetAvailableActions(pod)
            };

            // Include documents if requested
            if (request.IncludeDocuments)
            {
                status.Documents = BuildDocumentInfo(pod);
                status.Signature = BuildSignatureInfo(pod);
            }

            // Include processing history if requested
            if (request.IncludeProcessingHistory)
            {
                status.VerificationInfo = BuildVerificationInfo(pod);
                status.ApprovalInfo = BuildApprovalInfo(pod);
                status.Issues = BuildIssueInfo(pod);
                status.Feedback = BuildFeedbackInfo(pod);
            }

            return new GetPODSubmissionStatusResponse
            {
                IsSuccess = true,
                Status = status
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting POD submission status for POD {PODId}", request.PODId);
            return new GetPODSubmissionStatusResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private string GetStatusDescription(Domain.Enums.PODStatus status)
    {
        return status switch
        {
            Domain.Enums.PODStatus.Draft => "POD is being prepared",
            Domain.Enums.PODStatus.Preview => "POD is ready for review",
            Domain.Enums.PODStatus.Submitted => "POD has been submitted and is being processed",
            Domain.Enums.PODStatus.Processing => "POD is being verified and processed",
            Domain.Enums.PODStatus.Verified => "POD has been verified successfully",
            Domain.Enums.PODStatus.Approved => "POD has been approved",
            Domain.Enums.PODStatus.Rejected => "POD has been rejected and needs revision",
            Domain.Enums.PODStatus.Archived => "POD has been archived",
            _ => "Unknown status"
        };
    }

    private PODProcessingStatusInfo BuildProcessingInfo(Domain.Entities.POD pod)
    {
        var stages = new List<ProcessingStageInfo>
        {
            new ProcessingStageInfo
            {
                StageName = "Upload",
                Status = "Completed",
                Progress = 100.0,
                StartedAt = pod.SubmittedAt,
                CompletedAt = pod.SubmittedAt,
                Description = "Documents and signature uploaded successfully"
            },
            new ProcessingStageInfo
            {
                StageName = "Validation",
                Status = pod.Status >= Domain.Enums.PODStatus.Processing ? "Completed" : "Pending",
                Progress = pod.Status >= Domain.Enums.PODStatus.Processing ? 100.0 : 0.0,
                Description = "Validating document quality and signature"
            },
            new ProcessingStageInfo
            {
                StageName = "Verification",
                Status = pod.Status >= Domain.Enums.PODStatus.Verified ? "Completed" :
                        pod.Status == Domain.Enums.PODStatus.Processing ? "InProgress" : "Pending",
                Progress = pod.Status >= Domain.Enums.PODStatus.Verified ? 100.0 :
                          pod.Status == Domain.Enums.PODStatus.Processing ? 50.0 : 0.0,
                Description = "Verifying delivery details and recipient information"
            },
            new ProcessingStageInfo
            {
                StageName = "Approval",
                Status = pod.Status >= Domain.Enums.PODStatus.Approved ? "Completed" : "Pending",
                Progress = pod.Status >= Domain.Enums.PODStatus.Approved ? 100.0 : 0.0,
                Description = "Final approval and archival"
            }
        };

        var currentStage = stages.LastOrDefault(s => s.Status == "InProgress")?.StageName ??
                          stages.LastOrDefault(s => s.Status == "Completed")?.StageName ??
                          "Upload";

        var overallProgress = stages.Average(s => s.Progress);

        return new PODProcessingStatusInfo
        {
            CurrentStage = currentStage,
            OverallProgress = overallProgress,
            Stages = stages,
            IsProcessingComplete = pod.Status >= Domain.Enums.PODStatus.Approved,
            EstimatedCompletionTime = pod.Status < Domain.Enums.PODStatus.Approved ?
                DateTime.UtcNow.AddHours(2) : null
        };
    }

    private PODDeliveryInfo BuildDeliveryInfo(Domain.Entities.POD pod)
    {
        return new PODDeliveryInfo
        {
            RecipientName = pod.RecipientName,
            DeliveryDateTime = pod.DeliveryDateTime,
            DeliveryLatitude = pod.DeliveryLatitude,
            DeliveryLongitude = pod.DeliveryLongitude,
            LocationVerified = pod.DeliveryLatitude.HasValue && pod.DeliveryLongitude.HasValue,
            RecipientVerified = !string.IsNullOrWhiteSpace(pod.RecipientName)
        };
    }

    private List<PODDocumentInfo> BuildDocumentInfo(Domain.Entities.POD pod)
    {
        // Mock implementation - would map from actual POD documents
        return new List<PODDocumentInfo>
        {
            new PODDocumentInfo
            {
                DocumentId = Guid.NewGuid(),
                DocumentType = "Photo",
                FileName = "delivery_photo.jpg",
                ContentType = "image/jpeg",
                FileSize = 1024000,
                Status = "Verified",
                UploadedAt = pod.SubmittedAt,
                ThumbnailUrl = "/api/pod/thumbnail/123",
                DownloadUrl = "/api/pod/download/123"
            }
        };
    }

    private PODSignatureInfo? BuildSignatureInfo(Domain.Entities.POD pod)
    {
        // Mock implementation - would check if POD has signature
        return new PODSignatureInfo
        {
            SignatureId = Guid.NewGuid(),
            SignerName = pod.RecipientName,
            SignedAt = pod.DeliveryDateTime,
            SignatureFormat = "SVG",
            Status = "Valid",
            PreviewUrl = "/api/pod/signature-preview/123",
            IsDigitalSignature = false
        };
    }

    private PODVerificationInfo? BuildVerificationInfo(Domain.Entities.POD pod)
    {
        if (pod.Status < Domain.Enums.PODStatus.Processing) return null;

        return new PODVerificationInfo
        {
            VerificationId = Guid.NewGuid(),
            VerificationStatus = pod.Status >= Domain.Enums.PODStatus.Verified ? "Completed" : "InProgress",
            VerificationStartedAt = pod.ProcessedAt,
            VerificationCompletedAt = pod.Status >= Domain.Enums.PODStatus.Verified ? pod.ProcessedAt : null,
            VerificationMethod = "Automated",
            VerificationScore = 95.5,
            Checks = new List<VerificationCheck>
            {
                new VerificationCheck { CheckType = "Document", CheckName = "Image Quality", Result = "Pass", Score = 98.0 },
                new VerificationCheck { CheckType = "Signature", CheckName = "Signature Validity", Result = "Pass", Score = 93.0 },
                new VerificationCheck { CheckType = "Location", CheckName = "GPS Verification", Result = "Pass", Score = 95.0 }
            }
        };
    }

    private PODApprovalInfo? BuildApprovalInfo(Domain.Entities.POD pod)
    {
        if (pod.Status < Domain.Enums.PODStatus.Verified) return null;

        return new PODApprovalInfo
        {
            ApprovalId = Guid.NewGuid(),
            ApprovalStatus = pod.Status >= Domain.Enums.PODStatus.Approved ? "Approved" : "Pending",
            ApprovalRequestedAt = pod.ProcessedAt,
            ApprovalCompletedAt = pod.Status >= Domain.Enums.PODStatus.Approved ? pod.ProcessedAt : null,
            WorkflowSteps = new List<ApprovalWorkflowStep>
            {
                new ApprovalWorkflowStep
                {
                    StepName = "Supervisor Review",
                    Status = pod.Status >= Domain.Enums.PODStatus.Approved ? "Completed" : "Pending",
                    CompletedAt = pod.Status >= Domain.Enums.PODStatus.Approved ? pod.ProcessedAt : null
                }
            }
        };
    }

    private List<PODIssue> BuildIssueInfo(Domain.Entities.POD pod)
    {
        // Mock implementation - would query actual issues
        return new List<PODIssue>();
    }

    private List<PODFeedback> BuildFeedbackInfo(Domain.Entities.POD pod)
    {
        // Mock implementation - would query actual feedback
        return new List<PODFeedback>();
    }

    private List<string> GetAvailableActions(Domain.Entities.POD pod)
    {
        var actions = new List<string>();

        switch (pod.Status)
        {
            case Domain.Enums.PODStatus.Draft:
            case Domain.Enums.PODStatus.Preview:
                actions.AddRange(new[] { "Edit", "Submit", "Cancel" });
                break;
            case Domain.Enums.PODStatus.Submitted:
            case Domain.Enums.PODStatus.Processing:
                actions.AddRange(new[] { "View Status", "Contact Support" });
                break;
            case Domain.Enums.PODStatus.Rejected:
                actions.AddRange(new[] { "View Issues", "Resubmit", "Contact Support" });
                break;
            case Domain.Enums.PODStatus.Verified:
            case Domain.Enums.PODStatus.Approved:
                actions.AddRange(new[] { "View Details", "Download", "Share" });
                break;
        }

        return actions;
    }
}
