{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "SubscriptionManagement": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=SubscriptionManagement;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "Redis": "localhost:6379"}, "RazorPay": {"KeyId": "rzp_test_key_id", "KeySecret": "rzp_test_key_secret", "WebhookSecret": "webhook_secret"}, "RabbitMQ": {"HostName": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "/", "ExchangeName": "subscription_events"}, "Cache": {"Redis": {"ConnectionString": "localhost:6379", "InstanceName": "SubscriptionManagement", "DefaultExpiration": "00:30:00", "SlidingExpiration": "00:15:00"}, "Memory": {"SizeLimit": 104857600, "CompactionPercentage": 0.25}}, "Metrics": {"Enabled": true, "ExportInterval": "00:01:00", "Endpoints": {"Prometheus": "/metrics"}}, "HealthChecks": {"Enabled": true, "Endpoint": "/health", "DetailedEndpoint": "/health/detailed", "CheckInterval": "00:01:00"}, "PerformanceMonitoring": {"Enabled": true, "SampleRate": 1.0, "SlowQueryThreshold": "00:00:01", "EnableSqlCommandText": true}}