using NetworkFleetManagement.Domain.Entities;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Interfaces;

/// <summary>
/// Repository interface for VehicleTypeMaster entity
/// </summary>
public interface IVehicleTypeMasterRepository
{
    // Basic CRUD operations
    Task<VehicleTypeMaster?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<VehicleTypeMaster?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleTypeMaster>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleTypeMaster>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleTypeMaster>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    
    // Pagination and filtering
    Task<(IEnumerable<VehicleTypeMaster> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string? category = null, 
        bool? isActive = null,
        CancellationToken cancellationToken = default);
    
    // Capacity-based queries
    Task<IEnumerable<VehicleTypeMaster>> GetByCapacityRangeAsync(
        decimal? minLoadCapacity = null,
        decimal? maxLoadCapacity = null,
        decimal? minVolumeCapacity = null,
        decimal? maxVolumeCapacity = null,
        CancellationToken cancellationToken = default);
    
    // Sorting and ordering
    Task<IEnumerable<VehicleTypeMaster>> GetOrderedBySortOrderAsync(CancellationToken cancellationToken = default);
    Task<int> GetMaxSortOrderAsync(CancellationToken cancellationToken = default);
    
    // Existence checks
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string code, Guid excludeId, CancellationToken cancellationToken = default);
    
    // Categories
    Task<IEnumerable<string>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetCategoryCountsAsync(CancellationToken cancellationToken = default);
    
    // Modification operations
    void Add(VehicleTypeMaster vehicleTypeMaster);
    void Update(VehicleTypeMaster vehicleTypeMaster);
    void Delete(VehicleTypeMaster vehicleTypeMaster);
    
    // Bulk operations
    Task<IEnumerable<VehicleTypeMaster>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    Task<IEnumerable<VehicleTypeMaster>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default);
    
    // Analytics
    Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetUsageStatisticsAsync(CancellationToken cancellationToken = default);
}

