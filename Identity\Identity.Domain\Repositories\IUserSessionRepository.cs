using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Domain.Repositories
{
    public interface IUserSessionRepository
    {
        Task<UserSession> GetByIdAsync(Guid id);
        Task<UserSession> GetByTokenAsync(string token);
        Task<IEnumerable<UserSession>> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<UserSession>> GetActiveByUserIdAsync(Guid userId);
        Task AddAsync(UserSession session);
        Task UpdateAsync(UserSession session);
        Task DeleteAsync(UserSession session);
        Task DeleteExpiredSessionsAsync();
    }
}
