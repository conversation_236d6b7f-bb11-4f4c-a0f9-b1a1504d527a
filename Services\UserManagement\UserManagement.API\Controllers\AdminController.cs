using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UserManagement.Application.Admin.Queries.GetPendingApprovals;
using UserManagement.Application.Admin.Queries.GetDashboardStats;
using UserManagement.Application.Admin.Queries.GetUsersAdvanced;
using UserManagement.Application.Admin.Commands.ApproveUser;
using UserManagement.Application.Admin.Commands.RejectUser;
using UserManagement.Application.Admin.Commands.ApproveDocuments;
using UserManagement.Application.Admin.Commands.RejectDocuments;
using UserManagement.Application.Admin.Commands.ProcessKycWithOcr;
using UserManagement.Application.Admin.Commands.ManageUserAccount;
using UserManagement.Application.Admin.Queries.ExportUserSummary;
using UserManagement.Application.Admin.Queries.ExportKycDocuments;
using System.Security.Claims;

namespace UserManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : BaseController
    {
        [HttpGet("pending-approvals")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPendingApprovals([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 20)
        {
            var query = new GetPendingApprovalsQuery
            {
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("dashboard-stats")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetDashboardStats()
        {
            var query = new GetDashboardStatsQuery();
            var result = await Mediator.Send(query);
            return Ok(result);
        }

        [HttpPost("users/{userId}/approve")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ApproveUser(Guid userId, [FromBody] ApproveUserRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new ApproveUserCommand
            {
                UserId = userId,
                ApprovedBy = currentUserId,
                Notes = request?.Notes ?? string.Empty
            };

            var result = await Mediator.Send(command);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        [HttpPost("users/{userId}/reject")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RejectUser(Guid userId, [FromBody] RejectUserRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new RejectUserCommand
            {
                UserId = userId,
                Reason = request.Reason,
                Notes = request.Notes ?? string.Empty,
                RejectedBy = currentUserId
            };

            var result = await Mediator.Send(command);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        [HttpPost("documents/{submissionId}/approve")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ApproveDocuments(Guid submissionId, [FromBody] ApproveDocumentsRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new ApproveDocumentsCommand
            {
                SubmissionId = submissionId,
                Documents = request.Documents?.Select(d => new DocumentApprovalRequest
                {
                    DocumentType = d.DocumentType,
                    ReviewNotes = d.ReviewNotes
                }).ToList() ?? new List<DocumentApprovalRequest>(),
                ApprovedBy = currentUserId,
                Notes = request.Notes ?? string.Empty
            };

            var result = await Mediator.Send(command);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        [HttpPost("documents/{submissionId}/reject")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> RejectDocuments(Guid submissionId, [FromBody] RejectDocumentsRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new RejectDocumentsCommand
            {
                SubmissionId = submissionId,
                Documents = request.Documents?.Select(d => new DocumentRejectionRequest
                {
                    DocumentType = d.DocumentType,
                    RejectionReason = d.RejectionReason,
                    ReviewNotes = d.ReviewNotes,
                    RequireResubmission = d.RequireResubmission
                }).ToList() ?? new List<DocumentRejectionRequest>(),
                RejectedBy = currentUserId,
                Notes = request.Notes ?? string.Empty
            };

            var result = await Mediator.Send(command);

            if (result.Success)
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Get users with advanced filtering and search
        /// </summary>
        [HttpGet("users/advanced")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetUsersAdvanced(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string searchTerm = null,
            [FromQuery] string email = null,
            [FromQuery] string phoneNumber = null,
            [FromQuery] string companyName = null,
            [FromQuery] string userName = null,
            [FromQuery] string subscriptionPlan = null,
            [FromQuery] DateTime? planExpiryFrom = null,
            [FromQuery] DateTime? planExpiryTo = null,
            [FromQuery] DateTime? registrationDateFrom = null,
            [FromQuery] DateTime? registrationDateTo = null,
            [FromQuery] string status = null,
            [FromQuery] string userType = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] string kycStatus = null,
            [FromQuery] bool? kycCompleted = null,
            [FromQuery] DateTime? lastLoginFrom = null,
            [FromQuery] DateTime? lastLoginTo = null,
            [FromQuery] int? minRfqVolume = null,
            [FromQuery] int? maxRfqVolume = null,
            [FromQuery] string sortBy = "CreatedAt",
            [FromQuery] string sortDirection = "desc",
            [FromQuery] bool? hasSubscription = null,
            [FromQuery] bool? autoRenewalEnabled = null,
            [FromQuery] string region = null,
            [FromQuery] string state = null,
            [FromQuery] string city = null)
        {
            var query = new GetUsersAdvancedQuery
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                Email = email,
                PhoneNumber = phoneNumber,
                CompanyName = companyName,
                UserName = userName,
                SubscriptionPlan = subscriptionPlan,
                PlanExpiryFrom = planExpiryFrom,
                PlanExpiryTo = planExpiryTo,
                RegistrationDateFrom = registrationDateFrom,
                RegistrationDateTo = registrationDateTo,
                Status = Enum.TryParse<Domain.Entities.ProfileStatus>(status, true, out var statusEnum) ? statusEnum : null,
                UserType = Enum.TryParse<Domain.Entities.UserType>(userType, true, out var userTypeEnum) ? userTypeEnum : null,
                IsActive = isActive,
                KycStatus = kycStatus,
                KycCompleted = kycCompleted,
                LastLoginFrom = lastLoginFrom,
                LastLoginTo = lastLoginTo,
                MinRfqVolume = minRfqVolume,
                MaxRfqVolume = maxRfqVolume,
                SortBy = sortBy,
                SortDirection = sortDirection,
                HasSubscription = hasSubscription,
                AutoRenewalEnabled = autoRenewalEnabled,
                Region = region,
                State = state,
                City = city
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Process KYC document with OCR
        /// </summary>
        [HttpPost("kyc/process-ocr")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ProcessKycWithOcr([FromBody] ProcessKycWithOcrRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new ProcessKycWithOcrCommand
            {
                UserId = request.UserId,
                DocumentId = request.DocumentId,
                DocumentType = request.DocumentType,
                AutoApprove = request.AutoApprove,
                ConfidenceThreshold = request.ConfidenceThreshold,
                ProcessedBy = currentUserId,
                Notes = request.Notes
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// Manage user account (activate, deactivate, reset password, etc.)
        /// </summary>
        [HttpPost("users/{userId}/manage")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ManageUserAccount(Guid userId, [FromBody] ManageUserAccountRequest request)
        {
            var currentUserId = GetCurrentUserId();

            var command = new ManageUserAccountCommand
            {
                UserId = userId,
                Action = request.Action,
                Reason = request.Reason,
                ActionBy = currentUserId,
                Notes = request.Notes,
                NewPassword = request.NewPassword,
                RequirePasswordChangeOnNextLogin = request.RequirePasswordChangeOnNextLogin,
                SubscriptionPlan = request.SubscriptionPlan,
                SubscriptionExpiryDate = request.SubscriptionExpiryDate,
                AutoRenewalEnabled = request.AutoRenewalEnabled,
                PlanInfo = request.PlanInfo
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// Get KYC approval history for a user
        /// </summary>
        [HttpGet("users/{userId}/kyc-history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetKycApprovalHistory(Guid userId)
        {
            // This would be implemented with a GetKycApprovalHistoryQuery
            return Ok(new { message = "GetKycApprovalHistory endpoint - to be implemented" });
        }

        /// <summary>
        /// Get OCR comparison panel data
        /// </summary>
        [HttpGet("kyc/{documentId}/ocr-comparison")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetOcrComparison(Guid documentId)
        {
            // This would be implemented with a GetOcrComparisonQuery
            return Ok(new { message = "GetOcrComparison endpoint - to be implemented" });
        }

        /// <summary>
        /// Resend KYC upload prompt to user
        /// </summary>
        [HttpPost("users/{userId}/resend-kyc-prompt")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ResendKycUploadPrompt(Guid userId)
        {
            var currentUserId = GetCurrentUserId();

            var command = new ManageUserAccountCommand
            {
                UserId = userId,
                Action = UserAccountAction.ResendKycUploadPrompt,
                ActionBy = currentUserId,
                Reason = "Admin requested KYC upload prompt resend"
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// Export user summary report
        /// </summary>
        [HttpPost("users/export")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportUserSummary([FromBody] UserSummaryExportRequest request)
        {
            var currentUserId = GetCurrentUserId();
            var currentUserRole = GetCurrentUserRole();
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            var query = new ExportUserSummaryQuery
            {
                Format = request.Format,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                UserType = ConvertToEnumUserType(request.UserType),
                Status = ConvertToEnumProfileStatus(request.Status),
                Location = request.Location,
                Region = request.Region,
                IncludeActivityMetrics = request.IncludeActivityMetrics,
                IncludeDocumentSummary = request.IncludeDocumentSummary,
                IncludeSubscriptionInfo = request.IncludeSubscriptionInfo,
                SelectedColumns = request.SelectedColumns,
                MaxRecords = request.MaxRecords,
                RequestedBy = currentUserId,
                RequestedByRole = currentUserRole,
                IpAddress = ipAddress,
                UserAgent = userAgent
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get available export columns for user summary
        /// </summary>
        [HttpGet("users/export/columns")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public IActionResult GetExportColumns()
        {
            var columns = new
            {
                BasicInfo = new[]
                {
                    new { Key = "UserId", Name = "User ID", Description = "Unique user identifier" },
                    new { Key = "FirstName", Name = "First Name", Description = "User's first name" },
                    new { Key = "LastName", Name = "Last Name", Description = "User's last name" },
                    new { Key = "Email", Name = "Email", Description = "User's email address" },
                    new { Key = "PhoneNumber", Name = "Phone Number", Description = "User's phone number" },
                    new { Key = "UserType", Name = "User Type", Description = "Type of user account" },
                    new { Key = "Status", Name = "Status", Description = "Current profile status" }
                },
                CompanyInfo = new[]
                {
                    new { Key = "CompanyName", Name = "Company Name", Description = "Company name" },
                    new { Key = "GstNumber", Name = "GST Number", Description = "GST registration number" },
                    new { Key = "PanNumber", Name = "PAN Number", Description = "PAN card number" }
                },
                LocationInfo = new[]
                {
                    new { Key = "City", Name = "City", Description = "User's city" },
                    new { Key = "State", Name = "State", Description = "User's state" },
                    new { Key = "Country", Name = "Country", Description = "User's country" },
                    new { Key = "Region", Name = "Region", Description = "User's region" }
                },
                ActivityMetrics = new[]
                {
                    new { Key = "RfqCount", Name = "RFQ Count", Description = "Total RFQs created" },
                    new { Key = "OrderCount", Name = "Order Count", Description = "Total orders placed" },
                    new { Key = "TripCount", Name = "Trip Count", Description = "Total trips completed" },
                    new { Key = "TotalBusinessValue", Name = "Business Value", Description = "Total business value" },
                    new { Key = "LastLoginAt", Name = "Last Login", Description = "Last login timestamp" }
                },
                DocumentSummary = new[]
                {
                    new { Key = "TotalDocuments", Name = "Total Documents", Description = "Total documents uploaded" },
                    new { Key = "ApprovedDocuments", Name = "Approved Documents", Description = "Number of approved documents" },
                    new { Key = "PendingDocuments", Name = "Pending Documents", Description = "Number of pending documents" },
                    new { Key = "RejectedDocuments", Name = "Rejected Documents", Description = "Number of rejected documents" }
                },
                Timestamps = new[]
                {
                    new { Key = "CreatedAt", Name = "Created Date", Description = "Account creation date" },
                    new { Key = "UpdatedAt", Name = "Updated Date", Description = "Last profile update date" },
                    new { Key = "ApprovedAt", Name = "Approved Date", Description = "Profile approval date" }
                }
            };

            return Ok(columns);
        }

        /// <summary>
        /// Export KYC documents in bulk
        /// </summary>
        [HttpPost("documents/export")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportKycDocuments([FromBody] KycDocumentExportRequest request)
        {
            var currentUserId = GetCurrentUserId();
            var currentUserRole = GetCurrentUserRole();
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            var query = new ExportKycDocumentsQuery
            {
                UserIds = request.UserIds,
                DocumentTypes = request.DocumentTypes,
                Status = request.Status,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                UserType = request.UserType,
                IncludeMetadata = request.IncludeMetadata,
                GroupByUser = request.GroupByUser,
                GroupByDocumentType = request.GroupByDocumentType,
                MaxDocuments = request.MaxDocuments,
                RequestedBy = currentUserId,
                RequestedByRole = currentUserRole,
                IpAddress = ipAddress,
                UserAgent = userAgent
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get available document types for export
        /// </summary>
        [HttpGet("documents/export/types")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public IActionResult GetDocumentTypes()
        {
            var documentTypes = Enum.GetValues<Domain.Entities.DocumentType>()
                .Select(dt => new
                {
                    Value = dt.ToString(),
                    Name = dt.ToString().Replace("_", " "),
                    Description = GetDocumentTypeDescription(dt),
                    Category = GetDocumentTypeCategory(dt)
                })
                .ToList();

            return Ok(new { DocumentTypes = documentTypes });
        }

        private static string GetDocumentTypeDescription(Domain.Entities.DocumentType documentType)
        {
            return documentType switch
            {
                Domain.Entities.DocumentType.GstCertificate => "GST Registration Certificate",
                Domain.Entities.DocumentType.TradeLicense => "Trade License Document",
                Domain.Entities.DocumentType.PanCard => "PAN Card",
                Domain.Entities.DocumentType.AadharCard => "Aadhar Card",
                Domain.Entities.DocumentType.DrivingLicense => "Driving License",
                Domain.Entities.DocumentType.VehicleRegistration => "Vehicle Registration Certificate",
                Domain.Entities.DocumentType.VehicleInsurance => "Vehicle Insurance Document",
                Domain.Entities.DocumentType.ProfilePhoto => "Profile Photo",
                _ => documentType.ToString()
            };
        }

        private static string GetDocumentTypeCategory(Domain.Entities.DocumentType documentType)
        {
            return documentType switch
            {
                Domain.Entities.DocumentType.GstCertificate or
                Domain.Entities.DocumentType.TradeLicense or
                Domain.Entities.DocumentType.PanCard => "Company Documents",

                Domain.Entities.DocumentType.AadharCard or
                Domain.Entities.DocumentType.DrivingLicense => "Individual Documents",

                Domain.Entities.DocumentType.VehicleRegistration or
                Domain.Entities.DocumentType.VehicleInsurance => "Vehicle Documents",

                Domain.Entities.DocumentType.ProfilePhoto => "Profile Documents",

                _ => "Other"
            };
        }

        private static Domain.Enums.UserType? ConvertToEnumUserType(Domain.Entities.UserType? entityUserType)
        {
            if (!entityUserType.HasValue) return null;

            return entityUserType.Value switch
            {
                Domain.Entities.UserType.Admin => Domain.Enums.UserType.Admin,
                Domain.Entities.UserType.TransportCompany => Domain.Enums.UserType.Transporter,
                Domain.Entities.UserType.Broker => Domain.Enums.UserType.Broker,
                Domain.Entities.UserType.Shipper => Domain.Enums.UserType.Shipper,
                _ => null
            };
        }

        private static Domain.Enums.ProfileStatus? ConvertToEnumProfileStatus(Domain.Entities.ProfileStatus? entityStatus)
        {
            if (!entityStatus.HasValue) return null;

            return entityStatus.Value switch
            {
                Domain.Entities.ProfileStatus.Incomplete => Domain.Enums.ProfileStatus.Pending,
                Domain.Entities.ProfileStatus.Complete => Domain.Enums.ProfileStatus.Active,
                Domain.Entities.ProfileStatus.UnderReview => Domain.Enums.ProfileStatus.Pending,
                Domain.Entities.ProfileStatus.Approved => Domain.Enums.ProfileStatus.Active,
                Domain.Entities.ProfileStatus.Rejected => Domain.Enums.ProfileStatus.Suspended,
                _ => null
            };
        }
    }

    public class ApproveUserRequest
    {
        public string Notes { get; set; } = string.Empty;
    }

    public class RejectUserRequest
    {
        public string Reason { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }

    public class ApproveDocumentsRequest
    {
        public List<DocumentApprovalRequestDto> Documents { get; set; } = new();
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentApprovalRequestDto
    {
        public Domain.Entities.DocumentType DocumentType { get; set; }
        public string ReviewNotes { get; set; } = string.Empty;
    }

    public class RejectDocumentsRequest
    {
        public List<DocumentRejectionRequestDto> Documents { get; set; } = new();
        public string Notes { get; set; } = string.Empty;
    }

    public class DocumentRejectionRequestDto
    {
        public Domain.Entities.DocumentType DocumentType { get; set; }
        public string RejectionReason { get; set; } = string.Empty;
        public string ReviewNotes { get; set; } = string.Empty;
        public bool RequireResubmission { get; set; } = true;
    }

    public class ProcessKycWithOcrRequest
    {
        public Guid UserId { get; set; }
        public Guid DocumentId { get; set; }
        public string DocumentType { get; set; }
        public bool AutoApprove { get; set; } = false;
        public decimal ConfidenceThreshold { get; set; } = 0.85m;
        public string Notes { get; set; }
    }

    public class ManageUserAccountRequest
    {
        public UserAccountAction Action { get; set; }
        public string Reason { get; set; }
        public string Notes { get; set; }
        public string NewPassword { get; set; }
        public bool RequirePasswordChangeOnNextLogin { get; set; } = true;
        public string SubscriptionPlan { get; set; }
        public DateTime? SubscriptionExpiryDate { get; set; }
        public bool? AutoRenewalEnabled { get; set; }
        public PlanInformation PlanInfo { get; set; }
    }

    public class KycDocumentExportRequest
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<Domain.Entities.DocumentType> DocumentTypes { get; set; } = new();
        public Domain.Entities.DocumentStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public Domain.Entities.UserType? UserType { get; set; }
        public bool IncludeMetadata { get; set; } = true;
        public bool GroupByUser { get; set; } = true;
        public bool GroupByDocumentType { get; set; } = false;
        public int? MaxDocuments { get; set; }
    }
}
