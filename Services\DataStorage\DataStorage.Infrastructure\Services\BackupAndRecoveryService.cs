using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace DataStorage.Infrastructure.Services;

public partial class BackupAndRecoveryService : IBackupAndRecoveryService
{
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<BackupAndRecoveryService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<Guid, BackupInfo> _backups;
    private readonly ConcurrentDictionary<Guid, BackupSchedule> _backupSchedules;
    private readonly ConcurrentDictionary<Guid, ReplicationTarget> _replicationTargets;
    private readonly ConcurrentDictionary<Guid, DisasterRecoveryPlan> _disasterRecoveryPlans;
    private readonly ConcurrentDictionary<Guid, BackupAlert> _backupAlerts;
    private readonly ConcurrentDictionary<Guid, RecoveryPoint> _recoveryPoints;

    public BackupAndRecoveryService(
        IFileStorageService fileStorageService,
        ILogger<BackupAndRecoveryService> logger,
        IConfiguration configuration)
    {
        _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _backups = new ConcurrentDictionary<Guid, BackupInfo>();
        _backupSchedules = new ConcurrentDictionary<Guid, BackupSchedule>();
        _replicationTargets = new ConcurrentDictionary<Guid, ReplicationTarget>();
        _disasterRecoveryPlans = new ConcurrentDictionary<Guid, DisasterRecoveryPlan>();
        _backupAlerts = new ConcurrentDictionary<Guid, BackupAlert>();
        _recoveryPoints = new ConcurrentDictionary<Guid, RecoveryPoint>();
    }

    public async Task<BackupResult> CreateBackupAsync(BackupRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var backupId = Guid.NewGuid();

        try
        {
            _logger.LogInformation("Creating {BackupType} backup {BackupId} for {DocumentCount} documents",
                request.Type, backupId, request.DocumentIds.Count);

            // Simulate backup process
            await Task.Delay(2000, cancellationToken);

            // Calculate backup statistics
            var totalSizeBytes = request.DocumentIds.Count * 1024000L; // 1MB per document
            var compressedSizeBytes = request.CompressBackup ? (long)(totalSizeBytes * 0.7) : totalSizeBytes;
            var backupLocation = $"backup://{backupId}";

            // Create backup info
            var backupInfo = new BackupInfo(
                backupId: backupId,
                type: request.Type,
                createdAt: DateTime.UtcNow,
                createdBy: request.RequestedBy,
                status: BackupStatus.Completed,
                sizeBytes: totalSizeBytes,
                compressedSizeBytes: compressedSizeBytes,
                fileCount: request.DocumentIds.Count,
                location: backupLocation,
                verificationStatus: BackupVerificationStatus.NotVerified,
                description: request.Description,
                replicationTargets: request.ReplicationTargets,
                expiresAt: DateTime.UtcNow.Add(request.RetentionPolicy.RetentionPeriod),
                metadata: request.Metadata);

            _backups.TryAdd(backupId, backupInfo);

            // Create recovery point
            var recoveryPoint = new RecoveryPoint(
                recoveryPointId: Guid.NewGuid(),
                timestamp: DateTime.UtcNow,
                type: RecoveryPointType.Backup,
                description: $"{request.Type} backup created",
                sizeBytes: totalSizeBytes,
                isAvailable: true,
                availableBackups: new List<Guid> { backupId });

            _recoveryPoints.TryAdd(recoveryPoint.RecoveryPointId, recoveryPoint);

            stopwatch.Stop();

            _logger.LogInformation("Backup {BackupId} created successfully in {ElapsedMs}ms. " +
                "Size: {TotalSize} -> {CompressedSize} bytes",
                backupId, stopwatch.ElapsedMilliseconds, totalSizeBytes, compressedSizeBytes);

            return BackupResult.Success(
                backupId,
                request.Type,
                DateTime.UtcNow.Subtract(stopwatch.Elapsed),
                stopwatch.Elapsed,
                totalSizeBytes,
                compressedSizeBytes,
                request.DocumentIds.Count,
                backupLocation);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error creating backup {BackupId}", backupId);
            return BackupResult.Failure(backupId, request.Type, $"Backup failed: {ex.Message}", DateTime.UtcNow);
        }
    }

    public async Task<BackupResult> CreateIncrementalBackupAsync(BackupRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating incremental backup for {DocumentCount} documents", request.DocumentIds.Count);

            // Find the last full backup
            var lastFullBackup = _backups.Values
                .Where(b => b.Type == BackupType.Full && b.Status == BackupStatus.Completed)
                .OrderByDescending(b => b.CreatedAt)
                .FirstOrDefault();

            if (lastFullBackup == null)
            {
                _logger.LogWarning("No full backup found. Creating full backup instead of incremental");
                var fullRequest = new BackupRequest(
                    BackupType.Full,
                    request.RequestedBy,
                    request.DocumentIds,
                    request.Categories,
                    "Full backup (no previous backup found)",
                    request.CompressBackup,
                    request.EncryptBackup,
                    request.RetentionPolicy,
                    request.ReplicationTargets,
                    request.Metadata);

                return await CreateBackupAsync(fullRequest, cancellationToken);
            }

            // Create incremental backup with reduced size
            var incrementalRequest = new BackupRequest(
                BackupType.Incremental,
                request.RequestedBy,
                request.DocumentIds,
                request.Categories,
                request.Description ?? "Incremental backup",
                request.CompressBackup,
                request.EncryptBackup,
                request.RetentionPolicy,
                request.ReplicationTargets,
                request.Metadata);

            return await CreateBackupAsync(incrementalRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating incremental backup");
            throw;
        }
    }

    public async Task<BackupResult> CreateDifferentialBackupAsync(BackupRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating differential backup for {DocumentCount} documents", request.DocumentIds.Count);

            // Find the last full backup
            var lastFullBackup = _backups.Values
                .Where(b => b.Type == BackupType.Full && b.Status == BackupStatus.Completed)
                .OrderByDescending(b => b.CreatedAt)
                .FirstOrDefault();

            if (lastFullBackup == null)
            {
                _logger.LogWarning("No full backup found. Creating full backup instead of differential");
                var fullRequest = new BackupRequest(
                    BackupType.Full,
                    request.RequestedBy,
                    request.DocumentIds,
                    request.Categories,
                    "Full backup (no previous backup found)",
                    request.CompressBackup,
                    request.EncryptBackup,
                    request.RetentionPolicy,
                    request.ReplicationTargets,
                    request.Metadata);

                return await CreateBackupAsync(fullRequest, cancellationToken);
            }

            // Create differential backup
            var differentialRequest = new BackupRequest(
                BackupType.Differential,
                request.RequestedBy,
                request.DocumentIds,
                request.Categories,
                request.Description ?? "Differential backup",
                request.CompressBackup,
                request.EncryptBackup,
                request.RetentionPolicy,
                request.ReplicationTargets,
                request.Metadata);

            return await CreateBackupAsync(differentialRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating differential backup");
            throw;
        }
    }

    public async Task<List<BackupInfo>> GetBackupsAsync(BackupFilter? filter = null, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning

        try
        {
            var backups = _backups.Values.AsEnumerable();

            if (filter != null)
            {
                if (filter.Types.Any())
                    backups = backups.Where(b => filter.Types.Contains(b.Type));

                if (filter.Statuses.Any())
                    backups = backups.Where(b => filter.Statuses.Contains(b.Status));

                if (filter.StartDate.HasValue)
                    backups = backups.Where(b => b.CreatedAt >= filter.StartDate.Value);

                if (filter.EndDate.HasValue)
                    backups = backups.Where(b => b.CreatedAt <= filter.EndDate.Value);

                if (filter.CreatedBy.HasValue)
                    backups = backups.Where(b => b.CreatedBy == filter.CreatedBy.Value);
            }

            return backups.OrderByDescending(b => b.CreatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backups");
            throw;
        }
    }

    public async Task<BackupInfo> GetBackupInfoAsync(Guid backupId, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning

        try
        {
            if (_backups.TryGetValue(backupId, out var backupInfo))
            {
                return backupInfo;
            }

            throw new ArgumentException($"Backup {backupId} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting backup info for {BackupId}", backupId);
            throw;
        }
    }

    public async Task<bool> DeleteBackupAsync(Guid backupId, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning

        try
        {
            _logger.LogInformation("Deleting backup {BackupId}", backupId);

            var removed = _backups.TryRemove(backupId, out var backup);
            if (removed)
            {
                _logger.LogInformation("Backup {BackupId} deleted successfully", backupId);

                // Remove from recovery points
                var recoveryPointsToUpdate = _recoveryPoints.Values
                    .Where(rp => rp.AvailableBackups.Contains(backupId))
                    .ToList();

                foreach (var recoveryPoint in recoveryPointsToUpdate)
                {
                    var updatedBackups = recoveryPoint.AvailableBackups.Where(id => id != backupId).ToList();
                    var updatedRecoveryPoint = new RecoveryPoint(
                        recoveryPoint.RecoveryPointId,
                        recoveryPoint.Timestamp,
                        recoveryPoint.Type,
                        recoveryPoint.Description,
                        recoveryPoint.SizeBytes,
                        updatedBackups.Any(),
                        recoveryPoint.DocumentId,
                        updatedBackups);

                    _recoveryPoints.TryUpdate(recoveryPoint.RecoveryPointId, updatedRecoveryPoint, recoveryPoint);
                }
            }
            else
            {
                _logger.LogWarning("Backup {BackupId} not found", backupId);
            }

            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting backup {BackupId}", backupId);
            return false;
        }
    }
}
