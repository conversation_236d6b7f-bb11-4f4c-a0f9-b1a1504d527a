using ContentManagement.Application.Commands.Faq;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Queries.Faq;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ContentManagement.API.Controllers;

/// <summary>
/// Controller for FAQ management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FaqController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<FaqController> _logger;

    public FaqController(IMediator mediator, ILogger<FaqController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get FAQ by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<FaqItemDto>> GetById(Guid id, [FromQuery] bool includeVersions = false)
    {
        var query = new GetFaqByIdQuery
        {
            Id = id,
            IncludeVersions = includeVersions,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Search FAQs with filters
    /// </summary>
    [HttpGet("search")]
    [AllowAnonymous]
    public async Task<ActionResult<FaqSearchResultDto>> Search([FromQuery] SearchFaqsQuery query)
    {
        query.UserRoles = GetUserRoles();
        query.IsAuthenticated = User.Identity?.IsAuthenticated ?? false;

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get FAQs by category
    /// </summary>
    [HttpGet("category/{category}")]
    [AllowAnonymous]
    public async Task<ActionResult<List<FaqSummaryDto>>> GetByCategory(
        Domain.Enums.FaqCategory category,
        [FromQuery] Domain.Enums.ContentStatus? status = null,
        [FromQuery] bool publishedOnly = true)
    {
        var query = new GetFaqsByCategoryQuery
        {
            Category = category,
            Status = status,
            PublishedOnly = publishedOnly,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get highlighted FAQs
    /// </summary>
    [HttpGet("highlighted")]
    [AllowAnonymous]
    public async Task<ActionResult<List<FaqSummaryDto>>> GetHighlighted([FromQuery] int? limit = 10)
    {
        var query = new GetHighlightedFaqsQuery
        {
            Limit = limit,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get most viewed FAQs
    /// </summary>
    [HttpGet("most-viewed")]
    [AllowAnonymous]
    public async Task<ActionResult<List<FaqSummaryDto>>> GetMostViewed(
        [FromQuery] int? limit = 10,
        [FromQuery] Domain.Enums.FaqCategory? category = null,
        [FromQuery] DateTime? fromDate = null)
    {
        var query = new GetMostViewedFaqsQuery
        {
            Limit = limit,
            Category = category,
            FromDate = fromDate,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get highest rated FAQs
    /// </summary>
    [HttpGet("highest-rated")]
    [AllowAnonymous]
    public async Task<ActionResult<List<FaqSummaryDto>>> GetHighestRated(
        [FromQuery] int? limit = 10,
        [FromQuery] Domain.Enums.FaqCategory? category = null,
        [FromQuery] int? minimumRatings = 5)
    {
        var query = new GetHighestRatedFaqsQuery
        {
            Limit = limit,
            Category = category,
            MinimumRatings = minimumRatings,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Create new FAQ
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqItemDto>> Create([FromBody] CreateFaqItemCommand command)
    {
        command.CreatedBy = GetUserId();
        command.CreatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Update existing FAQ
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqItemDto>> Update(Guid id, [FromBody] UpdateFaqItemCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Update FAQ category
    /// </summary>
    [HttpPut("{id:guid}/category")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqItemDto>> UpdateCategory(Guid id, [FromBody] UpdateFaqCategoryCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Update FAQ role visibility
    /// </summary>
    [HttpPut("{id:guid}/role-visibility")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqItemDto>> UpdateRoleVisibility(Guid id, [FromBody] UpdateFaqRoleVisibilityCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Set FAQ highlight status
    /// </summary>
    [HttpPut("{id:guid}/highlight")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqItemDto>> SetHighlight(Guid id, [FromBody] SetFaqHighlightCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Record FAQ view (for analytics)
    /// </summary>
    [HttpPost("{id:guid}/view")]
    [AllowAnonymous]
    public async Task<ActionResult> RecordView(Guid id)
    {
        var command = new RecordFaqViewCommand { Id = id };
        var result = await _mediator.Send(command);
        
        if (result)
            return Ok();
        
        return NotFound();
    }

    /// <summary>
    /// Rate FAQ helpfulness
    /// </summary>
    [HttpPost("{id:guid}/rate")]
    [Authorize]
    public async Task<ActionResult<FaqItemDto>> RateHelpfulness(Guid id, [FromBody] RateFaqHelpfulnessCommand command)
    {
        command.Id = id;
        command.RatedBy = GetUserId();
        command.RatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Bulk update FAQ sort orders
    /// </summary>
    [HttpPut("bulk-sort-order")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<List<FaqSummaryDto>>> BulkUpdateSortOrder([FromBody] BulkUpdateFaqSortOrderCommand command)
    {
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Get FAQ category statistics
    /// </summary>
    [HttpGet("category-stats")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<List<FaqCategoryStatsDto>>> GetCategoryStats([FromQuery] bool includeEmpty = false)
    {
        var query = new GetFaqCategoryStatsQuery { IncludeEmpty = includeEmpty };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get FAQ analytics
    /// </summary>
    [HttpGet("analytics")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<FaqAnalyticsDto>> GetAnalytics([FromQuery] GetFaqAnalyticsQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }

    private List<string> GetUserRoles()
    {
        return User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
    }
}
