# TLI Microservices API Documentation

## Overview

This document provides comprehensive API documentation for the TLI Microservices Platform. All APIs are accessible through the API Gateway at `https://localhost:5000` and follow RESTful conventions.

## Authentication

### JWT Bearer Token

All protected endpoints require a valid JWT Bearer token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh-token-here",
  "expiresIn": 3600,
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "roles": ["User"]
  }
}
```

## API Endpoints

### 1. Identity Service (`/api/identity`, `/api/auth`)

#### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/register` | Register new user | No |
| POST | `/api/auth/login` | User login | No |
| POST | `/api/auth/refresh` | Refresh token | No |
| POST | `/api/auth/logout` | User logout | Yes |
| POST | `/api/auth/forgot-password` | Request password reset | No |
| POST | `/api/auth/reset-password` | Reset password | No |

#### User Registration

```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+91-9876543210",
  "userType": "Shipper" // Shipper, Transporter, Broker
}
```

### 2. User Management Service (`/api/users`, `/api/profiles`)

#### User Profile Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/users/{id}` | Get user by ID | Yes |
| PUT | `/api/users/{id}` | Update user profile | Yes |
| DELETE | `/api/users/{id}` | Delete user | Yes (Admin) |
| GET | `/api/users/company/{companyName}` | Get users by company | Yes |
| POST | `/api/users/{id}/verify-kyc` | Submit KYC documents | Yes |
| GET | `/api/users/{id}/kyc-status` | Get KYC status | Yes |

#### Get User Profile

```http
GET /api/users/123e4567-e89b-12d3-a456-426614174000
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+91-9876543210",
  "companyName": "ABC Logistics",
  "userType": "Shipper",
  "kycStatus": "Verified",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### 3. Order Management Service (`/api/orders`, `/api/rfq`, `/api/quotes`)

#### Order Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/orders` | Get user orders | Yes |
| POST | `/api/orders` | Create new order | Yes |
| GET | `/api/orders/{id}` | Get order details | Yes |
| PUT | `/api/orders/{id}` | Update order | Yes |
| DELETE | `/api/orders/{id}` | Cancel order | Yes |

#### RFQ (Request for Quote) Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/rfq` | Create RFQ | Yes |
| GET | `/api/rfq/{id}` | Get RFQ details | Yes |
| POST | `/api/rfq/{id}/quotes` | Submit quote for RFQ | Yes |
| GET | `/api/rfq/{id}/quotes` | Get quotes for RFQ | Yes |

#### Create Order

```http
POST /api/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "pickupLocation": {
    "address": "123 Main St, Mumbai, Maharashtra",
    "latitude": 19.0760,
    "longitude": 72.8777,
    "contactPerson": "John Doe",
    "contactPhone": "+91-9876543210"
  },
  "deliveryLocation": {
    "address": "456 Park Ave, Delhi, Delhi",
    "latitude": 28.6139,
    "longitude": 77.2090,
    "contactPerson": "Jane Smith",
    "contactPhone": "+91-9876543211"
  },
  "cargo": {
    "description": "Electronics Equipment",
    "weight": 500.5,
    "volume": 2.5,
    "value": 50000,
    "cargoType": "Electronics"
  },
  "requirements": {
    "vehicleType": "Truck",
    "pickupDate": "2024-02-01T09:00:00Z",
    "deliveryDate": "2024-02-03T17:00:00Z",
    "specialInstructions": "Handle with care"
  }
}
```

### 4. Trip Management Service (`/api/trips`, `/api/tracking`)

#### Trip Tracking

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/trips/{id}` | Get trip details | Yes |
| POST | `/api/trips/{id}/start` | Start trip | Yes |
| POST | `/api/trips/{id}/complete` | Complete trip | Yes |
| GET | `/api/trips/{id}/tracking` | Get real-time location | Yes |
| POST | `/api/trips/{id}/pod` | Submit POD | Yes |

#### Real-time Tracking

```http
GET /api/trips/123e4567-e89b-12d3-a456-426614174000/tracking
Authorization: Bearer <token>
```

**Response:**
```json
{
  "tripId": "123e4567-e89b-12d3-a456-426614174000",
  "currentLocation": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Current Location Address",
    "timestamp": "2024-01-15T14:30:00Z"
  },
  "status": "InTransit",
  "estimatedArrival": "2024-01-16T10:00:00Z",
  "distanceRemaining": 245.5,
  "milestones": [
    {
      "name": "Pickup Completed",
      "status": "Completed",
      "timestamp": "2024-01-15T09:00:00Z"
    },
    {
      "name": "In Transit",
      "status": "InProgress",
      "timestamp": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### 5. Financial Payment Service (`/api/payments`, `/api/invoices`)

#### Payment Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/payments` | Process payment | Yes |
| GET | `/api/payments/{id}` | Get payment details | Yes |
| GET | `/api/invoices` | Get user invoices | Yes |
| GET | `/api/invoices/{id}` | Get invoice details | Yes |
| POST | `/api/invoices/{id}/pay` | Pay invoice | Yes |

### 6. Subscription Management Service (`/api/subscriptions`, `/api/features`, `/api/billing`)

#### Subscription Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/subscriptions/plans` | Get available plans | No |
| POST | `/api/subscriptions` | Subscribe to plan | Yes |
| GET | `/api/subscriptions/current` | Get current subscription | Yes |
| PUT | `/api/subscriptions/upgrade` | Upgrade subscription | Yes |
| PUT | `/api/subscriptions/cancel` | Cancel subscription | Yes |

### 7. Communication & Notification Service (`/api/notifications`, `/api/messages`)

#### Notification Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/notifications` | Get user notifications | Yes |
| PUT | `/api/notifications/{id}/read` | Mark as read | Yes |
| POST | `/api/notifications/preferences` | Update preferences | Yes |
| POST | `/api/messages/send` | Send message | Yes |

## Error Handling

### Standard Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred.",
    "details": [
      {
        "field": "email",
        "message": "Email is required."
      }
    ],
    "timestamp": "2024-01-15T10:30:00Z",
    "traceId": "trace-id-here"
  }
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 204 | No Content |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Unprocessable Entity |
| 429 | Too Many Requests |
| 500 | Internal Server Error |

## Rate Limiting

API requests are rate-limited to prevent abuse:

- **General endpoints**: 100 requests per minute
- **Authentication endpoints**: 5 requests per minute
- **File upload endpoints**: 10 requests per minute

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## Pagination

List endpoints support pagination using query parameters:

```http
GET /api/orders?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc
```

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalPages": 5,
    "totalItems": 100,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Filtering and Searching

Most list endpoints support filtering and searching:

```http
GET /api/orders?status=Active&search=electronics&dateFrom=2024-01-01&dateTo=2024-01-31
```

## WebSocket Connections

Real-time features use WebSocket connections:

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/tracking")
    .build();

connection.start().then(function () {
    connection.invoke("JoinTripGroup", tripId);
});

connection.on("LocationUpdate", function (location) {
    // Handle real-time location update
});
```

## SDK and Client Libraries

Official SDKs are available for:

- **JavaScript/TypeScript**: `npm install @tli/api-client`
- **C#**: `dotnet add package TLI.ApiClient`
- **Python**: `pip install tli-api-client`
- **Java**: Available on Maven Central

## Testing

Use the provided Postman collection for API testing:

1. Import `docs/postman/TLI-Microservices.postman_collection.json`
2. Set up environment variables
3. Run the collection

## Support

For API support:

- **Documentation**: Check Swagger UI at `/swagger`
- **Issues**: Create GitHub issue
- **Email**: <EMAIL>
