﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Exceptions;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace Identity.Application.Roles.Commands.CreateRole
{
    public class CreateRoleCommandHandler : IRequestHandler<CreateRoleCommand, Guid>
    {
        private readonly Identity.Domain.Repositories.IRoleRepository _roleRepository;
        private readonly Identity.Domain.Repositories.IPermissionRepository _permissionRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateRoleCommandHandler> _logger;

        public CreateRoleCommandHandler(
            Identity.Domain.Repositories.IRoleRepository roleRepository,
            Identity.Domain.Repositories.IPermissionRepository permissionRepository,
            IMessageBroker messageBroker,
            ILogger<CreateRoleCommandHandler> logger)
        {
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateRoleCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if role name already exists
                var existingRole = await _roleRepository.GetByNameAsync(request.Name);
                if (existingRole != null)
                {
                    throw new DomainException("A role with this name already exists");
                }

                // Determine role type
                var roleType = request.IsTemplate ? RoleType.Template : 
                              request.ParentRoleId.HasValue ? RoleType.Inherited : 
                              RoleType.Custom;

                // Create the role
                var role = new Role(
                    request.Name,
                    request.Description,
                    request.CreatedBy,
                    false, // Not a system role
                    roleType,
                    request.ParentRoleId,
                    request.IsTemplate,
                    request.Department,
                    request.Priority);

                // Add permissions
                if (request.PermissionIds?.Count > 0)
                {
                    foreach (var permissionId in request.PermissionIds)
                    {
                        var permission = await _permissionRepository.GetByIdAsync(permissionId);
                        if (permission != null)
                        {
                            role.AddPermission(permission);
                        }
                    }
                }

                // If this is an inherited role, inherit from parent
                if (request.ParentRoleId.HasValue)
                {
                    var parentRole = await _roleRepository.GetByIdAsync(request.ParentRoleId.Value);
                    if (parentRole != null)
                    {
                        role.InheritFromParent(parentRole);
                    }
                }

                await _roleRepository.AddAsync(role);

                // Publish integration event
                await _messageBroker.PublishAsync("role.created", new
                {
                    RoleId = role.Id,
                    Name = request.Name,
                    Description = request.Description,
                    Department = request.Department,
                    IsTemplate = request.IsTemplate,
                    ParentRoleId = request.ParentRoleId,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTime.UtcNow,
                    PermissionCount = request.PermissionIds?.Count ?? 0
                });

                _logger.LogInformation("Role created successfully: {RoleId} - {RoleName}", role.Id, request.Name);

                return role.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role with name {RoleName}", request.Name);
                throw;
            }
        }
    }
}

