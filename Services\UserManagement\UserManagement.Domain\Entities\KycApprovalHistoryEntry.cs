using Shared.Domain.Common;
using UserManagement.Domain.Enums;

namespace UserManagement.Domain.Entities;

/// <summary>
/// Entity representing a KYC approval history entry
/// </summary>
public class KycApprovalHistoryEntry : BaseEntity
{
    public Guid UserId { get; private set; }
    public Guid ReviewerId { get; private set; }
    public KycDecision Decision { get; private set; }
    public string Comments { get; private set; } = string.Empty;
    public DateTime ReviewedAt { get; private set; }
    public string? DocumentsReviewed { get; private set; }
    public string? RejectionReason { get; private set; }
    public bool RequiresResubmission { get; private set; }
    public DateTime? ResubmissionDeadline { get; private set; }

    // Navigation properties
    public UserProfile? User { get; private set; }

    private KycApprovalHistoryEntry()
    {
        // EF Core constructor
    }

    public KycApprovalHistoryEntry(
        Guid userId,
        Guid reviewerId,
        KycDecision decision,
        string comments,
        string? documentsReviewed = null,
        string? rejectionReason = null,
        bool requiresResubmission = false,
        DateTime? resubmissionDeadline = null)
    {
        UserId = userId;
        ReviewerId = reviewerId;
        Decision = decision;
        Comments = comments ?? throw new ArgumentNullException(nameof(comments));
        DocumentsReviewed = documentsReviewed;
        RejectionReason = rejectionReason;
        RequiresResubmission = requiresResubmission;
        ResubmissionDeadline = resubmissionDeadline;
        ReviewedAt = DateTime.UtcNow;
    }

    public static KycApprovalHistoryEntry CreateApproval(
        Guid userId,
        Guid reviewerId,
        string comments,
        string? documentsReviewed = null)
    {
        return new KycApprovalHistoryEntry(
            userId,
            reviewerId,
            KycDecision.Approved,
            comments,
            documentsReviewed);
    }

    public static KycApprovalHistoryEntry CreateRejection(
        Guid userId,
        Guid reviewerId,
        string comments,
        string rejectionReason,
        bool requiresResubmission = true,
        DateTime? resubmissionDeadline = null)
    {
        return new KycApprovalHistoryEntry(
            userId,
            reviewerId,
            KycDecision.Rejected,
            comments,
            null,
            rejectionReason,
            requiresResubmission,
            resubmissionDeadline);
    }

    public static KycApprovalHistoryEntry CreateReviewRequest(
        Guid userId,
        Guid reviewerId,
        string comments,
        string? documentsReviewed = null)
    {
        return new KycApprovalHistoryEntry(
            userId,
            reviewerId,
            KycDecision.UnderReview,
            comments,
            documentsReviewed);
    }

    public void UpdateDecision(KycDecision newDecision, string updatedComments)
    {
        Decision = newDecision;
        Comments = updatedComments;
        ReviewedAt = DateTime.UtcNow;
    }
}
