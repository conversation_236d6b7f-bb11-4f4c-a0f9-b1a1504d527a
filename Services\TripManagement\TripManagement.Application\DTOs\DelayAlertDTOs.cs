using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs;

/// <summary>
/// Summary DTO for delay alerts
/// </summary>
public class DelayAlertSummaryDto
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public DelayAlertType AlertType { get; set; }
    public DelayAlertSeverity Severity { get; set; }
    public DelayAlertStatus Status { get; set; }
    public string Title { get; set; } = string.Empty;
    public TimeSpan DelayDuration { get; set; }
    public int EscalationLevel { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public bool IsExpired { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string? ShipperName { get; set; }
}

/// <summary>
/// Detailed DTO for delay alerts
/// </summary>
public class DelayAlertDetailsDto
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid? TripStopId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid? ShipperId { get; set; }
    public DelayAlertType AlertType { get; set; }
    public DelayAlertSeverity Severity { get; set; }
    public DelayAlertStatus Status { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TimeSpan DelayDuration { get; set; }
    public DateTime ScheduledTime { get; set; }
    public DateTime? EstimatedTime { get; set; }
    public DateTime? ActualTime { get; set; }
    public DelayReason DelayReason { get; set; }
    public string? DelayReasonDescription { get; set; }
    public int EscalationLevel { get; set; }
    public DateTime? LastEscalatedAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public Guid? AcknowledgedByUserId { get; set; }
    public string? AcknowledgmentNotes { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolutionNotes { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsExpired { get; set; }
    public int NotificationCount { get; set; }
    public DelayImpactAssessmentDto? ImpactAssessment { get; set; }
    
    // Trip information
    public string TripNumber { get; set; } = string.Empty;
    public string? ShipperName { get; set; }
    public string? DriverName { get; set; }
    public string? VehicleNumber { get; set; }
    public LocationDto? CurrentLocation { get; set; }
    public LocationDto? TargetLocation { get; set; }
    public double? DistanceRemainingKm { get; set; }
}

/// <summary>
/// DTO for delay impact assessment
/// </summary>
public class DelayImpactAssessmentDto
{
    public decimal FinancialImpact { get; set; }
    public string CustomerImpact { get; set; } = string.Empty;
    public string OperationalImpact { get; set; } = string.Empty;
    public int AffectedShipments { get; set; }
    public int AffectedCustomers { get; set; }
    public List<string> MitigationActions { get; set; } = new();
    public DateTime AssessedAt { get; set; }
    public Guid AssessedByUserId { get; set; }
    public string? AssessedByUserName { get; set; }
}

/// <summary>
/// DTO for location information
/// </summary>
public class LocationDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
}

/// <summary>
/// DTO for delay alert analytics
/// </summary>
public class DelayAlertAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Overall metrics
    public int TotalAlerts { get; set; }
    public int ActiveAlerts { get; set; }
    public int AcknowledgedAlerts { get; set; }
    public int ResolvedAlerts { get; set; }
    public int CancelledAlerts { get; set; }
    public double AverageDelayDuration { get; set; } // in minutes
    public double AverageResolutionTime { get; set; } // in minutes
    
    // Breakdown by severity
    public Dictionary<string, int> AlertsBySeverity { get; set; } = new();
    
    // Breakdown by reason
    public Dictionary<string, int> AlertsByReason { get; set; } = new();
    
    // Time-based trends
    public Dictionary<string, int> AlertsByHour { get; set; } = new();
    public Dictionary<string, decimal> FinancialImpactByDay { get; set; } = new();
    
    // Performance metrics
    public double ConversionRate { get; set; } // Percentage of alerts resolved
    public int TotalEscalations { get; set; }
    public double AverageEscalationLevel { get; set; }
}

/// <summary>
/// DTO for delay alert configuration
/// </summary>
public class DelayAlertConfigurationDto
{
    public Guid TransportCompanyId { get; set; }
    public bool IsEnabled { get; set; }
    public List<DelayThresholdDto> DelayThresholds { get; set; } = new();
    public List<int> EscalationIntervalMinutes { get; set; } = new();
    public int MaxEscalationLevel { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public List<string> EscalationRecipients { get; set; } = new();
    public bool AutoAcknowledge { get; set; }
    public int? AutoAcknowledgeDelayMinutes { get; set; }
    public bool AutoResolve { get; set; }
    public int? AutoResolveDelayMinutes { get; set; }
    public int? AlertExpiryHours { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public DateTime? LastUpdatedAt { get; set; }
    public Guid? LastUpdatedByUserId { get; set; }
}

/// <summary>
/// DTO for delay threshold configuration
/// </summary>
public class DelayThresholdDto
{
    public DelayAlertType AlertType { get; set; }
    public DelayAlertSeverity Severity { get; set; }
    public int ThresholdMinutes { get; set; }
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// DTO for delay alert list with pagination
/// </summary>
public class DelayAlertListDto
{
    public List<DelayAlertSummaryDto> DelayAlerts { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public DelayAlertListSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// DTO for delay alert list summary
/// </summary>
public class DelayAlertListSummaryDto
{
    public int TotalAlerts { get; set; }
    public int ActiveAlerts { get; set; }
    public int AcknowledgedAlerts { get; set; }
    public int ResolvedAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public int HighSeverityAlerts { get; set; }
    public double AverageDelayDuration { get; set; }
    public TimeSpan TotalDelayTime { get; set; }
}

/// <summary>
/// DTO for delay alert notification
/// </summary>
public class DelayAlertNotificationDto
{
    public Guid Id { get; set; }
    public Guid DelayAlertId { get; set; }
    public string Channel { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// DTO for delay alert dashboard
/// </summary>
public class DelayAlertDashboardDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Current status
    public int ActiveAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public int AlertsRequiringAttention { get; set; }
    public int AlertsEscalated { get; set; }
    
    // Recent alerts
    public List<DelayAlertSummaryDto> RecentAlerts { get; set; } = new();
    
    // Trends
    public List<DelayAlertTrendDto> DailyTrends { get; set; } = new();
    public List<DelayAlertTrendDto> HourlyTrends { get; set; } = new();
    
    // Performance metrics
    public double AverageResolutionTime { get; set; }
    public double AlertResolutionRate { get; set; }
    public int TotalTripsMonitored { get; set; }
    public double DelayRate { get; set; } // Percentage of trips with delays
}

/// <summary>
/// DTO for delay alert trends
/// </summary>
public class DelayAlertTrendDto
{
    public DateTime Date { get; set; }
    public int AlertsCreated { get; set; }
    public int AlertsResolved { get; set; }
    public int AlertsEscalated { get; set; }
    public double AverageDelayDuration { get; set; }
    public decimal TotalFinancialImpact { get; set; }
}
