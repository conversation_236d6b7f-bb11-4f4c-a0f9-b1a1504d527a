using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    [Route("api/admin/dashboard")]
    public class AdminDashboardController : BaseController
    {
        private readonly INotificationHistoryRepository _notificationHistoryRepository;
        private readonly ISubscriptionPaymentProofRepository _paymentProofRepository;
        private readonly INotificationTemplateService _templateService;
        private readonly INotificationTrackingService _trackingService;

        public AdminDashboardController(
            INotificationHistoryRepository notificationHistoryRepository,
            ISubscriptionPaymentProofRepository paymentProofRepository,
            INotificationTemplateService templateService,
            INotificationTrackingService trackingService)
        {
            _notificationHistoryRepository = notificationHistoryRepository;
            _paymentProofRepository = paymentProofRepository;
            _templateService = templateService;
            _trackingService = trackingService;
        }

        /// <summary>
        /// Get dashboard overview statistics
        /// </summary>
        [HttpGet("overview")]
        [ProducesResponseType(typeof(DashboardOverviewDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDashboardOverview()
        {
            try
            {
                var overview = new DashboardOverviewDto();

                // Payment proof statistics
                overview.PaymentProofs.Pending = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.Pending);
                overview.PaymentProofs.UnderReview = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.UnderReview);
                overview.PaymentProofs.Verified = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.Verified);
                overview.PaymentProofs.Rejected = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.Rejected);
                overview.PaymentProofs.RequiresAdditionalInfo = await _paymentProofRepository.GetCountByStatusAsync(PaymentProofStatus.RequiresAdditionalInfo);

                // Notification statistics
                overview.Notifications.Total = await _notificationHistoryRepository.GetCountAsync();
                overview.Notifications.Pending = await _notificationHistoryRepository.GetCountByStatusAsync("Pending");
                overview.Notifications.Sent = await _notificationHistoryRepository.GetCountByStatusAsync("Sent");
                overview.Notifications.Delivered = await _notificationHistoryRepository.GetCountByStatusAsync("Delivered");
                overview.Notifications.Failed = await _notificationHistoryRepository.GetCountByStatusAsync("Failed");

                // Recent activity
                var recentPaymentProofs = await _paymentProofRepository.GetPagedAsync(1, 5);
                overview.RecentPaymentProofs = recentPaymentProofs.Items.Select(p => new PaymentProofSummaryDto
                {
                    Id = p.Id,
                    SubscriptionId = p.SubscriptionId,
                    UserId = p.UserId,
                    Amount = p.Amount.Amount,
                    Currency = p.Amount.Currency,
                    Status = p.Status,
                    CreatedAt = p.CreatedAt
                }).ToList();

                var recentNotifications = await _notificationHistoryRepository.GetPagedAsync(1, 5);
                overview.RecentNotifications = recentNotifications.Items.Select(n => new NotificationSummaryDto
                {
                    Id = n.Id,
                    Type = n.Type,
                    Channel = n.Channel,
                    Status = n.Status,
                    CreatedAt = n.CreatedAt,
                    SentAt = n.SentAt
                }).ToList();

                return Ok(overview);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get notification analytics for dashboard
        /// </summary>
        [HttpGet("notification-analytics")]
        [ProducesResponseType(typeof(NotificationAnalyticsDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetNotificationAnalytics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? channel = null)
        {
            try
            {
                var analytics = await _trackingService.GetNotificationAnalyticsAsync(
                    fromDate ?? DateTime.UtcNow.AddDays(-30),
                    toDate ?? DateTime.UtcNow,
                    channel);

                var dto = new NotificationAnalyticsDto
                {
                    TotalNotifications = analytics.TotalNotifications,
                    DeliveryRate = analytics.DeliveryRate,
                    ReadRate = analytics.ReadRate,
                    ClickRate = analytics.ClickRate,
                    AverageDeliveryTimeMinutes = analytics.AverageDeliveryTime.TotalMinutes,
                    NotificationsByStatus = analytics.NotificationsByStatus,
                    NotificationsByChannel = analytics.NotificationsByChannel,
                    NotificationsByType = analytics.NotificationsByType.ToDictionary(
                        kv => kv.Key.ToString(), 
                        kv => kv.Value),
                    FromDate = analytics.FromDate,
                    ToDate = analytics.ToDate
                };

                return Ok(dto);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get payment proof verification queue
        /// </summary>
        [HttpGet("verification-queue")]
        [ProducesResponseType(typeof(VerificationQueueDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetVerificationQueue()
        {
            try
            {
                var pendingProofs = await _paymentProofRepository.GetProofsRequiringReviewAsync();
                
                var queue = new VerificationQueueDto
                {
                    TotalCount = pendingProofs.Count,
                    Items = pendingProofs.Take(20).Select(p => new PaymentProofDetailDto
                    {
                        Id = p.Id,
                        SubscriptionId = p.SubscriptionId,
                        UserId = p.UserId,
                        Amount = p.Amount.Amount,
                        Currency = p.Amount.Currency,
                        PaymentDate = p.PaymentDate,
                        ProofImageUrl = p.ProofImageUrl,
                        Notes = p.Notes,
                        Status = p.Status,
                        PaymentMethod = p.PaymentMethod,
                        TransactionReference = p.TransactionReference,
                        CreatedAt = p.CreatedAt,
                        DaysWaiting = (DateTime.UtcNow - p.CreatedAt).Days
                    }).ToList()
                };

                return Ok(queue);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get notification templates for UI selection
        /// </summary>
        [HttpGet("notification-templates")]
        [ProducesResponseType(typeof(List<NotificationTemplateOptionDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetNotificationTemplates(
            [FromQuery] NotificationType? type = null,
            [FromQuery] string? channel = null)
        {
            try
            {
                // TODO: Implement template retrieval
                var templates = new List<NotificationTemplateOptionDto>
                {
                    new NotificationTemplateOptionDto
                    {
                        Id = Guid.NewGuid(),
                        Name = "Subscription Reminder",
                        Type = NotificationType.SubscriptionReminder,
                        Channel = "Email",
                        Description = "Remind users about upcoming subscription renewal"
                    },
                    new NotificationTemplateOptionDto
                    {
                        Id = Guid.NewGuid(),
                        Name = "Payment Verified",
                        Type = NotificationType.PaymentProofVerified,
                        Channel = "Email",
                        Description = "Notify user when payment proof is verified"
                    }
                };

                if (type.HasValue)
                    templates = templates.Where(t => t.Type == type.Value).ToList();

                if (!string.IsNullOrEmpty(channel))
                    templates = templates.Where(t => t.Channel.Equals(channel, StringComparison.OrdinalIgnoreCase)).ToList();

                return Ok(templates);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Preview notification before sending
        /// </summary>
        [HttpPost("preview-notification")]
        [ProducesResponseType(typeof(NotificationPreviewDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> PreviewNotification([FromBody] PreviewNotificationRequest request)
        {
            try
            {
                // TODO: Implement notification preview
                var preview = new NotificationPreviewDto
                {
                    Subject = "Preview: Subscription Notification",
                    Body = "This is a preview of the notification content with variables replaced.",
                    Channel = request.Channel,
                    EstimatedVariables = request.Variables ?? new Dictionary<string, string>()
                };

                return Ok(preview);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }

    // DTOs for Admin UI
    public class DashboardOverviewDto
    {
        public PaymentProofStatsDto PaymentProofs { get; set; } = new();
        public NotificationStatsDto Notifications { get; set; } = new();
        public List<PaymentProofSummaryDto> RecentPaymentProofs { get; set; } = new();
        public List<NotificationSummaryDto> RecentNotifications { get; set; } = new();
    }

    public class PaymentProofStatsDto
    {
        public int Pending { get; set; }
        public int UnderReview { get; set; }
        public int Verified { get; set; }
        public int Rejected { get; set; }
        public int RequiresAdditionalInfo { get; set; }
    }

    public class NotificationStatsDto
    {
        public int Total { get; set; }
        public int Pending { get; set; }
        public int Sent { get; set; }
        public int Delivered { get; set; }
        public int Failed { get; set; }
    }

    public class PaymentProofSummaryDto
    {
        public Guid Id { get; set; }
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public PaymentProofStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class NotificationSummaryDto
    {
        public Guid Id { get; set; }
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? SentAt { get; set; }
    }

    public class NotificationAnalyticsDto
    {
        public int TotalNotifications { get; set; }
        public double DeliveryRate { get; set; }
        public double ReadRate { get; set; }
        public double ClickRate { get; set; }
        public double AverageDeliveryTimeMinutes { get; set; }
        public Dictionary<string, int> NotificationsByStatus { get; set; } = new();
        public Dictionary<string, int> NotificationsByChannel { get; set; } = new();
        public Dictionary<string, int> NotificationsByType { get; set; } = new();
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

    public class VerificationQueueDto
    {
        public int TotalCount { get; set; }
        public List<PaymentProofDetailDto> Items { get; set; } = new();
    }

    public class PaymentProofDetailDto
    {
        public Guid Id { get; set; }
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public string ProofImageUrl { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public PaymentProofStatus Status { get; set; }
        public string? PaymentMethod { get; set; }
        public string? TransactionReference { get; set; }
        public DateTime CreatedAt { get; set; }
        public int DaysWaiting { get; set; }
    }

    public class NotificationTemplateOptionDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class NotificationPreviewDto
    {
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string Channel { get; set; } = string.Empty;
        public Dictionary<string, string> EstimatedVariables { get; set; } = new();
    }
}
