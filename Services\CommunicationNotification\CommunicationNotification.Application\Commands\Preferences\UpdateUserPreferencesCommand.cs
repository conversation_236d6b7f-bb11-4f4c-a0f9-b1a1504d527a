using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Commands.Preferences;

/// <summary>
/// Command to update user communication preferences
/// </summary>
public class UpdateUserPreferencesCommand : CommandBase<CommandResult<UserPreferencesResult>>
{
    public Guid UserId { get; set; }
    public NotificationChannelPreferences ChannelPreferences { get; set; } = new();
    public LanguagePreferences LanguagePreferences { get; set; } = new();
    public DeliveryPreferences DeliveryPreferences { get; set; } = new();
    public PrivacyPreferences PrivacyPreferences { get; set; } = new();
    public Dictionary<string, object> CustomPreferences { get; set; } = new();
}

/// <summary>
/// Command to update channel preferences for specific message types
/// </summary>
public class UpdateChannelPreferencesCommand : CommandBase<CommandResult<ChannelPreferencesResult>>
{
    public Guid UserId { get; set; }
    public MessageType MessageType { get; set; }
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public List<NotificationChannel> DisabledChannels { get; set; } = new();
    public Dictionary<NotificationChannel, ChannelSettings> ChannelSettings { get; set; } = new();
}

/// <summary>
/// Command to update quiet hours settings
/// </summary>
public class UpdateQuietHoursCommand : CommandBase<CommandResult<QuietHoursResult>>
{
    public Guid UserId { get; set; }
    public bool EnableQuietHours { get; set; }
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public List<DayOfWeek> DaysOfWeek { get; set; } = new();
    public string TimeZone { get; set; } = "UTC";
    public List<MessageType> ExemptMessageTypes { get; set; } = new();
    public List<Priority> ExemptPriorities { get; set; } = new();
}

/// <summary>
/// Command to update language preferences
/// </summary>
public class UpdateLanguagePreferencesCommand : CommandBase<CommandResult<LanguagePreferencesResult>>
{
    public Guid UserId { get; set; }
    public Language PrimaryLanguage { get; set; }
    public List<Language> SecondaryLanguages { get; set; } = new();
    public bool EnableAutoTranslation { get; set; }
    public Dictionary<MessageType, Language> MessageTypeLanguages { get; set; } = new();
    public CulturePreferences CulturePreferences { get; set; } = new();
}

/// <summary>
/// Command to update delivery preferences
/// </summary>
public class UpdateDeliveryPreferencesCommand : CommandBase<CommandResult<DeliveryPreferencesResult>>
{
    public Guid UserId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public bool RequireReadReceipts { get; set; }
    public TimeSpan DeliveryTimeout { get; set; } = TimeSpan.FromMinutes(30);
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
    public bool EnableFailoverChannels { get; set; } = true;
    public Dictionary<NotificationChannel, DeliverySettings> ChannelDeliverySettings { get; set; } = new();
}

/// <summary>
/// Command to update privacy preferences
/// </summary>
public class UpdatePrivacyPreferencesCommand : CommandBase<CommandResult<PrivacyPreferencesResult>>
{
    public Guid UserId { get; set; }
    public bool AllowDataCollection { get; set; }
    public bool AllowPersonalization { get; set; }
    public bool AllowThirdPartySharing { get; set; }
    public DataRetentionPreference DataRetentionPreference { get; set; }
    public List<ConsentType> ConsentGiven { get; set; } = new();
    public List<ConsentType> ConsentWithdrawn { get; set; } = new();
    public Dictionary<string, bool> PrivacySettings { get; set; } = new();
}

/// <summary>
/// Command to subscribe to notification types
/// </summary>
public class SubscribeToNotificationsCommand : CommandBase<CommandResult<SubscriptionResult>>
{
    public Guid UserId { get; set; }
    public List<MessageType> MessageTypes { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> SubscriptionMetadata { get; set; } = new();
}

/// <summary>
/// Command to unsubscribe from notification types
/// </summary>
public class UnsubscribeFromNotificationsCommand : CommandBase<CommandResult<UnsubscriptionResult>>
{
    public Guid UserId { get; set; }
    public List<MessageType> MessageTypes { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public bool UnsubscribeFromAll { get; set; }
    public string? UnsubscriptionReason { get; set; }
}

// Supporting classes
public class NotificationChannelPreferences
{
    public NotificationChannel PrimaryChannel { get; set; } = NotificationChannel.Push;
    public List<NotificationChannel> SecondaryChannels { get; set; } = new();
    public Dictionary<MessageType, List<NotificationChannel>> MessageTypeChannels { get; set; } = new();
    public Dictionary<Priority, List<NotificationChannel>> PriorityChannels { get; set; } = new();
}

public class LanguagePreferences
{
    public Language PrimaryLanguage { get; set; } = Language.English;
    public List<Language> SecondaryLanguages { get; set; } = new();
    public bool EnableAutoTranslation { get; set; } = true;
    public Dictionary<MessageType, Language> MessageTypeLanguages { get; set; } = new();
}

public class DeliveryPreferences
{
    public bool RequireDeliveryConfirmation { get; set; } = true;
    public bool RequireReadReceipts { get; set; } = false;
    public TimeSpan DeliveryTimeout { get; set; } = TimeSpan.FromMinutes(30);
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
    public bool EnableFailoverChannels { get; set; } = true;
}

public class PrivacyPreferences
{
    public bool AllowDataCollection { get; set; } = true;
    public bool AllowPersonalization { get; set; } = true;
    public bool AllowThirdPartySharing { get; set; } = false;
    public DataRetentionPreference DataRetentionPreference { get; set; } = DataRetentionPreference.Standard;
    public List<ConsentType> ConsentGiven { get; set; } = new();
    public Dictionary<string, bool> PrivacySettings { get; set; } = new();
}

public class ChannelSettings
{
    public bool IsEnabled { get; set; } = true;
    public Dictionary<string, object> Settings { get; set; } = new();
    public TimeSpan? CustomTimeout { get; set; }
    public int? CustomRetryAttempts { get; set; }
}

public class DeliverySettings
{
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
    public Dictionary<string, object> ChannelSpecificSettings { get; set; } = new();
}

public class CulturePreferences
{
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public string TimeFormat { get; set; } = "HH:mm";
    public string NumberFormat { get; set; } = "en-US";
    public string CurrencyFormat { get; set; } = "USD";
    public string TimeZone { get; set; } = "UTC";
}

// Result classes
public class UserPreferencesResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> UpdatedFields { get; set; } = new();
}

public class ChannelPreferencesResult
{
    public Guid UserId { get; set; }
    public MessageType MessageType { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<NotificationChannel> UpdatedChannels { get; set; } = new();
}

public class QuietHoursResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; }
    public TimeOnly? NextQuietPeriodStart { get; set; }
    public TimeOnly? NextQuietPeriodEnd { get; set; }
}

public class LanguagePreferencesResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Language PrimaryLanguage { get; set; }
    public List<Language> SupportedLanguages { get; set; } = new();
}

public class DeliveryPreferencesResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Dictionary<NotificationChannel, bool> ChannelAvailability { get; set; } = new();
}

public class PrivacyPreferencesResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<ConsentType> ActiveConsents { get; set; } = new();
    public DateTime? ConsentExpiryDate { get; set; }
}

public class SubscriptionResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime SubscribedAt { get; set; }
    public List<MessageType> SubscribedMessageTypes { get; set; } = new();
    public List<string> SubscribedTags { get; set; } = new();
}

public class UnsubscriptionResult
{
    public Guid UserId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UnsubscribedAt { get; set; }
    public List<MessageType> UnsubscribedMessageTypes { get; set; } = new();
    public List<string> UnsubscribedTags { get; set; } = new();
    public bool IsCompleteUnsubscription { get; set; }
}

// Enums
public enum DataRetentionPreference
{
    Minimal,
    Standard,
    Extended,
    Custom
}

public enum ConsentType
{
    DataProcessing,
    Marketing,
    Analytics,
    ThirdPartySharing,
    Personalization,
    LocationTracking,
    CommunicationHistory
}

// Additional supporting classes for TimeOnly compatibility
public struct TimeOnly
{
    public int Hour { get; set; }
    public int Minute { get; set; }
    public int Second { get; set; }

    public TimeOnly(int hour, int minute, int second = 0)
    {
        Hour = hour;
        Minute = minute;
        Second = second;
    }

    public static TimeOnly FromTimeSpan(TimeSpan timeSpan)
    {
        return new TimeOnly(timeSpan.Hours, timeSpan.Minutes, timeSpan.Seconds);
    }

    public TimeSpan ToTimeSpan()
    {
        return new TimeSpan(Hour, Minute, Second);
    }

    public override string ToString()
    {
        return $"{Hour:D2}:{Minute:D2}:{Second:D2}";
    }
}
