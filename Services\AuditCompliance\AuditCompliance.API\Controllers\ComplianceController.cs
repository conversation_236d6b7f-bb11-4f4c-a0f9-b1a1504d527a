using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ComplianceController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ComplianceController> _logger;

    public ComplianceController(IMediator mediator, ILogger<ComplianceController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a new compliance report
    /// </summary>
    [HttpPost("reports")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<Guid>> CreateComplianceReport([FromBody] CreateComplianceReportCommand command)
    {
        try
        {
            // Set the generated by information from the current user
            command.GeneratedBy = GetCurrentUserId();
            command.GeneratedByName = GetCurrentUserName();

            var reportId = await _mediator.Send(command);
            return Ok(new { ReportId = reportId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating compliance report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance report by ID
    /// </summary>
    [HttpGet("reports/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceReportDto>> GetComplianceReport(Guid id)
    {
        try
        {
            var query = new GetComplianceReportByIdQuery { Id = id };
            var report = await _mediator.Send(query);
            
            if (report == null)
                return NotFound();

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance report: {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance report by report number
    /// </summary>
    [HttpGet("reports/by-number/{reportNumber}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceReportDto>> GetComplianceReportByNumber(string reportNumber)
    {
        try
        {
            var query = new GetComplianceReportByNumberQuery { ReportNumber = reportNumber };
            var report = await _mediator.Send(query);
            
            if (report == null)
                return NotFound();

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance report by number: {ReportNumber}", reportNumber);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance reports with filtering and pagination
    /// </summary>
    [HttpPost("reports/search")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceReportResultDto>> GetComplianceReports([FromBody] GetComplianceReportsQuery query)
    {
        try
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance summary for all standards
    /// </summary>
    [HttpGet("summary")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<List<ComplianceSummaryDto>>> GetComplianceSummary()
    {
        try
        {
            var query = new GetComplianceSummaryQuery();
            var summary = await _mediator.Send(query);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get pending compliance reports
    /// </summary>
    [HttpGet("reports/pending")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<List<ComplianceReportDto>>> GetPendingComplianceReports()
    {
        try
        {
            var query = new GetPendingComplianceReportsQuery();
            var reports = await _mediator.Send(query);
            return Ok(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending compliance reports");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Start processing a compliance report
    /// </summary>
    [HttpPost("reports/{id}/start")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult> StartComplianceReportProcessing(Guid id)
    {
        try
        {
            var command = new StartComplianceReportProcessingCommand { ReportId = id };
            var result = await _mediator.Send(command);
            
            if (!result)
                return NotFound();

            return Ok(new { Message = "Compliance report processing started" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting compliance report processing: {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Complete a compliance report
    /// </summary>
    [HttpPost("reports/{id}/complete")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult> CompleteComplianceReport(Guid id, [FromBody] CompleteComplianceReportCommand command)
    {
        try
        {
            command.ReportId = id;
            var result = await _mediator.Send(command);
            
            if (!result)
                return NotFound();

            return Ok(new { Message = "Compliance report completed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing compliance report: {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate automated compliance report
    /// </summary>
    [HttpPost("reports/automated")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<ComplianceReportDto>> GenerateAutomatedComplianceReport([FromBody] GenerateAutomatedComplianceReportCommand command)
    {
        try
        {
            command.GeneratedBy = GetCurrentUserId();
            command.GeneratedByName = GetCurrentUserName();

            var report = await _mediator.Send(command);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating automated compliance report");
            return StatusCode(500, "Internal server error");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("id");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) 
            ? userId 
            : Guid.Empty;
    }

    private string GetCurrentUserName()
    {
        return User.FindFirst("name")?.Value ?? 
               User.FindFirst("preferred_username")?.Value ?? 
               User.Identity?.Name ?? 
               "Unknown";
    }
}
