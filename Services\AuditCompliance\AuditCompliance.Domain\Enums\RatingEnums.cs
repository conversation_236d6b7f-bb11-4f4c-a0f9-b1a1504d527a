namespace AuditCompliance.Domain.Enums;

/// <summary>
/// Rating scale for service provider ratings
/// </summary>
public enum RatingScale
{
    OneStar = 1,
    TwoStars = 2,
    ThreeStars = 3,
    FourStars = 4,
    FiveStars = 5
}

/// <summary>
/// Categories for rating aspects
/// </summary>
public enum RatingCategory
{
    Overall = 1,
    Timeliness = 2,
    Communication = 3,
    ServiceQuality = 4,
    Professionalism = 5,
    Pricing = 6,
    Reliability = 7,
    Safety = 8,
    Documentation = 9,
    CustomerService = 10
}

/// <summary>
/// Status of a rating or review
/// </summary>
public enum RatingStatus
{
    Pending = 1,
    Active = 2,
    Flagged = 3,
    Removed = 4,
    UnderReview = 5
}

/// <summary>
/// Types of service issues that can be reported
/// </summary>
public enum ServiceIssueType
{
    DelayedDelivery = 1,
    DamagedGoods = 2,
    PoorCommunication = 3,
    UnprofessionalBehavior = 4,
    IncorrectDocumentation = 5,
    SafetyConcerns = 6,
    PricingDispute = 7,
    ServiceNotProvided = 8,
    QualityIssues = 9,
    Other = 10
}

/// <summary>
/// Priority levels for service issues
/// </summary>
public enum IssuePriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Status of service issue resolution
/// </summary>
public enum IssueResolutionStatus
{
    Reported = 1,
    Acknowledged = 2,
    InProgress = 3,
    Resolved = 4,
    Closed = 5,
    Escalated = 6
}
