using Shared.Domain.Common;
using Shared.Domain.ValueObjects;
using MonitoringObservability.Domain.Enums;
using Shared.Domain.ValueObjects;

namespace MonitoringObservability.Domain.ValueObjects;

public class MetricValue : ValueObject
{
    public double Value { get; private set; }
    public string Unit { get; private set; }
    public MetricType Type { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }

    private MetricValue()
    {
        Unit = string.Empty;
        Tags = new Dictionary<string, string>();
    }

    public MetricValue(double value, string unit, MetricType type, DateTime? timestamp = null, Dictionary<string, string>? tags = null)
    {
        if (string.IsNullOrWhiteSpace(unit))
            throw new ArgumentException("Unit cannot be empty", nameof(unit));

        Value = value;
        Unit = unit;
        Type = type;
        Timestamp = timestamp ?? DateTime.UtcNow;
        Tags = tags ?? new Dictionary<string, string>();
    }

    public static MetricValue Counter(double value, string unit = "count", Dictionary<string, string>? tags = null)
    {
        return new MetricValue(value, unit, MetricType.Counter, null, tags);
    }

    public static MetricValue Gauge(double value, string unit, Dictionary<string, string>? tags = null)
    {
        return new MetricValue(value, unit, MetricType.Gauge, null, tags);
    }

    public static MetricValue Timer(double milliseconds, Dictionary<string, string>? tags = null)
    {
        return new MetricValue(milliseconds, "ms", MetricType.Timer, null, tags);
    }

    public MetricValue WithTag(string key, string value)
    {
        var newTags = new Dictionary<string, string>(Tags)
        {
            [key] = value
        };
        return new MetricValue(Value, Unit, Type, Timestamp, newTags);
    }

    public MetricValue WithTimestamp(DateTime timestamp)
    {
        return new MetricValue(Value, Unit, Type, timestamp, Tags);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
        yield return Unit;
        yield return Type;
        yield return Timestamp;
        
        foreach (var tag in Tags.OrderBy(x => x.Key))
        {
            yield return tag.Key;
            yield return tag.Value;
        }
    }
}

public class PerformanceMetrics : ValueObject
{
    public double ResponseTime { get; private set; }
    public double Throughput { get; private set; }
    public double ErrorRate { get; private set; }
    public double CpuUsage { get; private set; }
    public double MemoryUsage { get; private set; }
    public double DiskUsage { get; private set; }
    public DateTime MeasuredAt { get; private set; }

    private PerformanceMetrics() { }

    public PerformanceMetrics(
        double responseTime,
        double throughput,
        double errorRate,
        double cpuUsage,
        double memoryUsage,
        double diskUsage,
        DateTime? measuredAt = null)
    {
        if (responseTime < 0) throw new ArgumentException("Response time cannot be negative");
        if (throughput < 0) throw new ArgumentException("Throughput cannot be negative");
        if (errorRate < 0 || errorRate > 100) throw new ArgumentException("Error rate must be between 0 and 100");
        if (cpuUsage < 0 || cpuUsage > 100) throw new ArgumentException("CPU usage must be between 0 and 100");
        if (memoryUsage < 0 || memoryUsage > 100) throw new ArgumentException("Memory usage must be between 0 and 100");
        if (diskUsage < 0 || diskUsage > 100) throw new ArgumentException("Disk usage must be between 0 and 100");

        ResponseTime = responseTime;
        Throughput = throughput;
        ErrorRate = errorRate;
        CpuUsage = cpuUsage;
        MemoryUsage = memoryUsage;
        DiskUsage = diskUsage;
        MeasuredAt = measuredAt ?? DateTime.UtcNow;
    }

    public bool IsHealthy()
    {
        return ResponseTime < 1000 && // Less than 1 second
               ErrorRate < 5 && // Less than 5% error rate
               CpuUsage < 80 && // Less than 80% CPU
               MemoryUsage < 85 && // Less than 85% memory
               DiskUsage < 90; // Less than 90% disk
    }

    public HealthStatus GetHealthStatus()
    {
        if (IsHealthy())
            return HealthStatus.Healthy;

        if (ResponseTime > 5000 || ErrorRate > 20 || CpuUsage > 95 || MemoryUsage > 95 || DiskUsage > 95)
            return HealthStatus.Unhealthy;

        return HealthStatus.Degraded;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ResponseTime;
        yield return Throughput;
        yield return ErrorRate;
        yield return CpuUsage;
        yield return MemoryUsage;
        yield return DiskUsage;
        yield return MeasuredAt;
    }
}

public class AlertThreshold : ValueObject
{
    public string MetricName { get; private set; }
    public double WarningThreshold { get; private set; }
    public double CriticalThreshold { get; private set; }
    public string Operator { get; private set; } // >, <, >=, <=, ==, !=
    public TimeSpan EvaluationWindow { get; private set; }

    private AlertThreshold()
    {
        MetricName = string.Empty;
        Operator = string.Empty;
    }

    public AlertThreshold(string metricName, double warningThreshold, double criticalThreshold, string @operator, TimeSpan evaluationWindow)
    {
        if (string.IsNullOrWhiteSpace(metricName))
            throw new ArgumentException("Metric name cannot be empty", nameof(metricName));

        if (string.IsNullOrWhiteSpace(@operator))
            throw new ArgumentException("Operator cannot be empty", nameof(@operator));

        var validOperators = new[] { ">", "<", ">=", "<=", "==", "!=" };
        if (!validOperators.Contains(@operator))
            throw new ArgumentException($"Invalid operator. Must be one of: {string.Join(", ", validOperators)}", nameof(@operator));

        MetricName = metricName;
        WarningThreshold = warningThreshold;
        CriticalThreshold = criticalThreshold;
        Operator = @operator;
        EvaluationWindow = evaluationWindow;
    }

    public AlertSeverity EvaluateThreshold(double value)
    {
        var exceedsCritical = Operator switch
        {
            ">" => value > CriticalThreshold,
            "<" => value < CriticalThreshold,
            ">=" => value >= CriticalThreshold,
            "<=" => value <= CriticalThreshold,
            "==" => Math.Abs(value - CriticalThreshold) < 0.001,
            "!=" => Math.Abs(value - CriticalThreshold) >= 0.001,
            _ => false
        };

        if (exceedsCritical)
            return AlertSeverity.Critical;

        var exceedsWarning = Operator switch
        {
            ">" => value > WarningThreshold,
            "<" => value < WarningThreshold,
            ">=" => value >= WarningThreshold,
            "<=" => value <= WarningThreshold,
            "==" => Math.Abs(value - WarningThreshold) < 0.001,
            "!=" => Math.Abs(value - WarningThreshold) >= 0.001,
            _ => false
        };

        return exceedsWarning ? AlertSeverity.Warning : AlertSeverity.Info;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MetricName;
        yield return WarningThreshold;
        yield return CriticalThreshold;
        yield return Operator;
        yield return EvaluationWindow;
    }
}
