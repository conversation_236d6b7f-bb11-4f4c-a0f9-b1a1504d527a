using MediatR;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Application.Commands.CreateCustomReportTemplate;

public class CreateCustomReportTemplateCommandHandler : IRequestHandler<CreateCustomReportTemplateCommand, CustomReportTemplateDto>
{
    private readonly IReportTemplateRepository _templateRepository;
    private readonly IReportConfigurationValidator _configurationValidator;
    private readonly IDataSourceValidator _dataSourceValidator;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateCustomReportTemplateCommandHandler> _logger;

    public CreateCustomReportTemplateCommandHandler(
        IReportTemplateRepository templateRepository,
        IReportConfigurationValidator configurationValidator,
        IDataSourceValidator dataSourceValidator,
        IUnitOfWork unitOfWork,
        ILogger<CreateCustomReportTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _configurationValidator = configurationValidator;
        _dataSourceValidator = dataSourceValidator;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<CustomReportTemplateDto> Handle(CreateCustomReportTemplateCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating custom report template '{Name}' by user {UserId}",
            request.Name, request.CreatedBy);

        try
        {
            // Validate template name uniqueness
            await ValidateTemplateNameUniqueness(request.Name, request.CreatedBy, cancellationToken);

            // Validate configuration if requested
            if (request.ValidateConfiguration)
            {
                await ValidateReportConfiguration(request.Configuration, cancellationToken);
            }

            // Apply base template if specified
            if (!string.IsNullOrEmpty(request.BasedOnTemplateId))
            {
                await ApplyBaseTemplate(request, cancellationToken);
            }

            // Create template
            var template = new CustomReportTemplateDto
            {
                TemplateId = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                CreatedBy = request.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                IsPublic = request.IsPublic,
                Tags = request.Tags,
                Configuration = request.Configuration,
                Parameters = request.Parameters,
                Metadata = new ReportTemplateMetadataDto
                {
                    UsageCount = 0,
                    Complexity = DetermineComplexity(request.Configuration),
                    RequiredPermissions = DetermineRequiredPermissions(request.Configuration),
                    Version = "1.0"
                }
            };

            // Save template
            await _templateRepository.CreateTemplateAsync(template, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created custom report template {TemplateId} with name '{Name}'",
                template.TemplateId, template.Name);

            return template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report template '{Name}'", request.Name);
            throw;
        }
    }

    private async Task ValidateTemplateNameUniqueness(string name, Guid createdBy, CancellationToken cancellationToken)
    {
        var existingTemplate = await _templateRepository.GetTemplateByNameAsync(name, createdBy, cancellationToken);
        if (existingTemplate != null)
        {
            throw new InvalidOperationException($"A template with the name '{name}' already exists for this user");
        }
    }

    private async Task ValidateReportConfiguration(ReportConfigurationDto configuration, CancellationToken cancellationToken)
    {
        // Validate data sources
        foreach (var dataSource in configuration.DataSources)
        {
            var isValid = await _dataSourceValidator.ValidateDataSourceAsync(dataSource, cancellationToken);
            if (!isValid)
            {
                throw new ArgumentException($"Invalid data source: {dataSource}");
            }
        }

        // Validate configuration structure
        var validationResult = await _configurationValidator.ValidateConfigurationAsync(configuration, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ArgumentException($"Invalid configuration: {string.Join(", ", validationResult.Errors)}");
        }

        // Validate fields
        await ValidateReportFields(configuration.Fields, configuration.DataSources, cancellationToken);

        // Validate visualizations
        await ValidateVisualizationConfigurations(configuration.Visualizations, configuration.Fields, cancellationToken);
    }

    private async Task ValidateReportFields(List<ReportFieldDto> fields, List<string> dataSources, CancellationToken cancellationToken)
    {
        foreach (var field in fields)
        {
            // Validate field exists in specified data source
            var fieldExists = await _dataSourceValidator.ValidateFieldExistsAsync(field.Source, field.FieldName, cancellationToken);
            if (!fieldExists)
            {
                throw new ArgumentException($"Field '{field.FieldName}' does not exist in data source '{field.Source}'");
            }

            // Validate aggregation type for field data type
            if (!string.IsNullOrEmpty(field.AggregationType))
            {
                var isValidAggregation = ValidateAggregationType(field.DataType, field.AggregationType);
                if (!isValidAggregation)
                {
                    throw new ArgumentException($"Aggregation type '{field.AggregationType}' is not valid for data type '{field.DataType}'");
                }
            }
        }
    }

    private async Task ValidateVisualizationConfigurations(List<ReportVisualizationConfigDto> visualizations, List<ReportFieldDto> fields, CancellationToken cancellationToken)
    {
        foreach (var viz in visualizations)
        {
            // Validate visualization type
            var validTypes = new[] { "Bar", "Line", "Pie", "Scatter", "Heatmap", "Table", "Gauge", "Funnel" };
            if (!validTypes.Contains(viz.Type))
            {
                throw new ArgumentException($"Invalid visualization type: {viz.Type}");
            }

            // Validate data fields exist
            foreach (var dataField in viz.DataFields)
            {
                var fieldExists = fields.Any(f => f.FieldName == dataField);
                if (!fieldExists)
                {
                    throw new ArgumentException($"Data field '{dataField}' referenced in visualization '{viz.Title}' does not exist in report fields");
                }
            }

            // Validate visualization-specific requirements
            await ValidateVisualizationSpecificRequirements(viz, fields, cancellationToken);
        }
    }

    private async Task ValidateVisualizationSpecificRequirements(ReportVisualizationConfigDto viz, List<ReportFieldDto> fields, CancellationToken cancellationToken)
    {
        switch (viz.Type.ToLower())
        {
            case "pie":
                if (viz.DataFields.Count != 2)
                {
                    throw new ArgumentException($"Pie chart '{viz.Title}' must have exactly 2 data fields (category and value)");
                }
                break;

            case "scatter":
                if (viz.DataFields.Count < 2)
                {
                    throw new ArgumentException($"Scatter plot '{viz.Title}' must have at least 2 data fields (X and Y axes)");
                }
                break;

            case "heatmap":
                if (viz.DataFields.Count < 3)
                {
                    throw new ArgumentException($"Heatmap '{viz.Title}' must have at least 3 data fields (X, Y, and value)");
                }
                break;
        }
    }

    private async Task ApplyBaseTemplate(CreateCustomReportTemplateCommand request, CancellationToken cancellationToken)
    {
        if (!Guid.TryParse(request.BasedOnTemplateId, out var templateId))
        {
            throw new ArgumentException($"Invalid template ID format: {request.BasedOnTemplateId}");
        }

        var baseTemplate = await _templateRepository.GetTemplateAsync(templateId, cancellationToken);
        if (baseTemplate == null)
        {
            throw new ArgumentException($"Base template {request.BasedOnTemplateId} not found");
        }

        // Merge configurations
        if (request.Configuration.DataSources.Count == 0)
        {
            request.Configuration.DataSources = baseTemplate.Configuration.DataSources;
        }

        if (request.Configuration.Fields.Count == 0)
        {
            request.Configuration.Fields = baseTemplate.Configuration.Fields;
        }

        if (request.Configuration.Visualizations.Count == 0)
        {
            request.Configuration.Visualizations = baseTemplate.Configuration.Visualizations;
        }

        // Merge parameters
        if (request.Parameters.Count == 0)
        {
            request.Parameters = baseTemplate.Parameters;
        }

        // Merge tags
        var baseTags = baseTemplate.Tags.Where(t => !request.Tags.Contains(t));
        request.Tags.AddRange(baseTags);
    }

    private string DetermineComplexity(ReportConfigurationDto configuration)
    {
        var complexityScore = 0;

        // Data sources complexity
        complexityScore += configuration.DataSources.Count * 2;

        // Fields complexity
        complexityScore += configuration.Fields.Count;
        complexityScore += configuration.Fields.Count(f => !string.IsNullOrEmpty(f.AggregationType)) * 2;

        // Filters complexity
        complexityScore += configuration.Filters.Count * 2;

        // Groupings complexity
        complexityScore += configuration.Groupings.Count * 3;

        // Visualizations complexity
        complexityScore += configuration.Visualizations.Count * 3;
        complexityScore += configuration.Visualizations.Count(v => v.Type == "Heatmap" || v.Type == "Scatter") * 2;

        return complexityScore switch
        {
            <= 10 => "Simple",
            <= 25 => "Medium",
            _ => "Complex"
        };
    }

    private List<string> DetermineRequiredPermissions(ReportConfigurationDto configuration)
    {
        var permissions = new List<string>();

        // Add permissions based on data sources
        foreach (var dataSource in configuration.DataSources)
        {
            permissions.Add($"Read_{dataSource}");
        }

        // Add visualization permissions if needed
        if (configuration.Visualizations.Any())
        {
            permissions.Add("CreateVisualizations");
        }

        // Add export permissions
        permissions.Add("ExportReports");

        return permissions.Distinct().ToList();
    }

    private bool ValidateAggregationType(string dataType, string aggregationType)
    {
        var numericTypes = new[] { "int", "decimal", "double", "float", "long" };
        var numericAggregations = new[] { "Sum", "Average", "Min", "Max" };

        if (numericAggregations.Contains(aggregationType) && !numericTypes.Contains(dataType.ToLower()))
        {
            return false;
        }

        return true;
    }
}

public class GenerateReportFromTemplateCommandHandler : IRequestHandler<GenerateReportFromTemplateCommand, ComprehensiveReportDto>
{
    private readonly IReportTemplateRepository _templateRepository;
    private readonly IReportGenerationService _reportGenerationService;
    private readonly IReportSchedulingService _schedulingService;
    private readonly ILogger<GenerateReportFromTemplateCommandHandler> _logger;

    public GenerateReportFromTemplateCommandHandler(
        IReportTemplateRepository templateRepository,
        IReportGenerationService reportGenerationService,
        IReportSchedulingService schedulingService,
        ILogger<GenerateReportFromTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _reportGenerationService = reportGenerationService;
        _schedulingService = schedulingService;
        _logger = logger;
    }

    public async Task<ComprehensiveReportDto> Handle(GenerateReportFromTemplateCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating report from template {TemplateId} by user {UserId}",
            request.TemplateId, request.RequestedBy);

        try
        {
            // Get template
            var template = await _templateRepository.GetTemplateAsync(request.TemplateId, cancellationToken);
            if (template == null)
            {
                throw new ArgumentException($"Template {request.TemplateId} not found");
            }

            // Validate parameters
            ValidateParameterValues(template.Parameters, request.ParameterValues);

            // Generate report
            var reportContent = await _reportGenerationService.GenerateReportFromTemplateAsync(
                template.Id,
                request.ParameterValues,
                cancellationToken);

            // Create comprehensive report DTO
            var report = new ComprehensiveReportDto
            {
                ReportId = Guid.NewGuid(),
                ReportName = template.Name,
                ReportType = "Custom",
                Content = reportContent,
                GeneratedAt = DateTime.UtcNow,
                GeneratedBy = request.RequestedBy,
                Parameters = request.ParameterValues,
                OutputFormat = request.OutputFormat,
                IncludeVisualizations = request.IncludeVisualizations,
                IncludeInsights = request.IncludeInsights
            };

            // Update template usage
            await _templateRepository.UpdateTemplateUsageAsync(request.TemplateId, cancellationToken);

            // Schedule report if requested
            if (request.ScheduleReport && !string.IsNullOrEmpty(request.ScheduleExpression))
            {
                await _schedulingService.ScheduleReportAsync(
                    request.TemplateId,
                    request.ScheduleExpression,
                    request.ParameterValues,
                    request.Recipients,
                    request.RequestedBy,
                    cancellationToken);
            }

            _logger.LogInformation("Successfully generated report {ReportId} from template {TemplateId}",
                report.ReportId, request.TemplateId);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report from template {TemplateId}", request.TemplateId);
            throw;
        }
    }

    private void ValidateParameterValues(List<ReportParameterDto> parameters, Dictionary<string, object> parameterValues)
    {
        foreach (var parameter in parameters.Where(p => p.IsRequired))
        {
            if (!parameterValues.ContainsKey(parameter.Name) || parameterValues[parameter.Name] == null)
            {
                throw new ArgumentException($"Required parameter '{parameter.Name}' is missing");
            }
        }

        foreach (var parameterValue in parameterValues)
        {
            var parameter = parameters.FirstOrDefault(p => p.Name == parameterValue.Key);
            if (parameter != null && parameter.PossibleValues.Any())
            {
                if (!parameter.PossibleValues.Contains(parameterValue.Value))
                {
                    throw new ArgumentException($"Invalid value for parameter '{parameter.Name}'. Allowed values: {string.Join(", ", parameter.PossibleValues)}");
                }
            }
        }
    }
}
