using Shared.Domain.Common;

namespace AuditCompliance.Domain.Events;

/// <summary>
/// Domain event raised when a feedback tag master is created
/// </summary>
public class FeedbackTagMasterCreatedEvent : DomainEvent
{
    public Guid FeedbackTagMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }

    public FeedbackTagMasterCreatedEvent(Guid feedbackTagMasterId, string code, string name, string category)
    {
        FeedbackTagMasterId = feedbackTagMasterId;
        Code = code;
        Name = name;
        Category = category;
    }
}

/// <summary>
/// Domain event raised when a feedback tag master is updated
/// </summary>
public class FeedbackTagMasterUpdatedEvent : DomainEvent
{
    public Guid FeedbackTagMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }
    public string OldName { get; }
    public string OldCategory { get; }

    public FeedbackTagMasterUpdatedEvent(Guid feedbackTagMasterId, string code, string name, string category, string oldName, string oldCategory)
    {
        FeedbackTagMasterId = feedbackTagMasterId;
        Code = code;
        Name = name;
        Category = category;
        OldName = oldName;
        OldCategory = oldCategory;
    }
}

/// <summary>
/// Domain event raised when a feedback tag master is activated
/// </summary>
public class FeedbackTagMasterActivatedEvent : DomainEvent
{
    public Guid FeedbackTagMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public FeedbackTagMasterActivatedEvent(Guid feedbackTagMasterId, string code, string name)
    {
        FeedbackTagMasterId = feedbackTagMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a feedback tag master is deactivated
/// </summary>
public class FeedbackTagMasterDeactivatedEvent : DomainEvent
{
    public Guid FeedbackTagMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public FeedbackTagMasterDeactivatedEvent(Guid feedbackTagMasterId, string code, string name)
    {
        FeedbackTagMasterId = feedbackTagMasterId;
        Code = code;
        Name = name;
    }
}



