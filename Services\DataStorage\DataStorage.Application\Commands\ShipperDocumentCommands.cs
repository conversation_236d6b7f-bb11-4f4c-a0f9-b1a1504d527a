using MediatR;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.Application.Commands;

// Shipper-specific document commands
public class UploadEWayBillCommand : IRequest<DocumentDto>
{
    public Guid ShipperId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string? EWayBillNumber { get; set; }
    public DateTime? ValidUntil { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class GenerateDigitalInvoiceCommand : IRequest<DocumentDto>
{
    public Guid ShipperId { get; set; }
    public Guid OrderId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public Dictionary<string, object> InvoiceData { get; set; } = new();
    public bool AutoGenerate { get; set; } = true;
}

public class UploadTaxDocumentCommand : IRequest<DocumentDto>
{
    public Guid ShipperId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public TaxDocumentType TaxDocumentType { get; set; }
    public string? GSTNumber { get; set; }
    public string? PANNumber { get; set; }
    public DateTime? TaxPeriodStart { get; set; }
    public DateTime? TaxPeriodEnd { get; set; }
}

public class UploadTransportDocumentationCommand : IRequest<DocumentDto>
{
    public Guid ShipperId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public Guid? RelatedOrderId { get; set; }
    public Guid? RelatedTripId { get; set; }
    public TransportDocumentType TransportDocumentType { get; set; }
}

public class ExportShipperDocumentsCommand : IRequest<DocumentDto>
{
    public Guid ShipperId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DocumentType? DocumentType { get; set; }
    public ExportFormat Format { get; set; } = ExportFormat.PDF;
    public bool IncludeMetadata { get; set; } = true;
    public string? ExportTitle { get; set; }
}

// Supporting enums
public enum TaxDocumentType
{
    GSTReturn = 0,
    IncomeTaxReturn = 1,
    TDSCertificate = 2,
    ServiceTaxReturn = 3,
    ProfessionalTaxReturn = 4,
    PANCard = 5,
    GSTCertificate = 6,
    TaxInvoice = 7,
    TaxReceipt = 8,
    Other = 99
}

public enum TransportDocumentType
{
    LoadingReceipt = 0,
    DeliveryReceipt = 1,
    TransportContract = 2,
    InsuranceDocument = 3,
    PermitDocument = 4,
    WeighbridgeSlip = 5,
    QualityCertificate = 6,
    CustomsDocument = 7,
    Other = 99
}

public enum ExportFormat
{
    PDF = 0,
    Excel = 1,
    CSV = 2,
    JSON = 3,
    ZIP = 4
}
