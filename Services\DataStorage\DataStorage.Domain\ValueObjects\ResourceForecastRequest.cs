using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for resource forecast
/// </summary>
public class ResourceForecastRequest : ValueObject
{
    public string ResourceType { get; init; }
    public DateTime HistoricalDataStart { get; init; }
    public DateTime HistoricalDataEnd { get; init; }
    public int ForecastDays { get; init; }
    public string ForecastModel { get; init; }
    public Dictionary<string, object> ModelParameters { get; init; }
    public List<string>? MetricsToForecast { get; init; }
    public double ConfidenceLevel { get; init; }

    public ResourceForecastRequest(
        string resourceType,
        DateTime historicalDataStart,
        DateTime historicalDataEnd,
        int forecastDays,
        string forecastModel = "Linear",
        Dictionary<string, object>? modelParameters = null,
        List<string>? metricsToForecast = null,
        double confidenceLevel = 0.95)
    {
        ResourceType = resourceType;
        HistoricalDataStart = historicalDataStart;
        HistoricalDataEnd = historicalDataEnd;
        ForecastDays = forecastDays;
        ForecastModel = forecastModel;
        ModelParameters = modelParameters ?? new Dictionary<string, object>();
        MetricsToForecast = metricsToForecast;
        ConfidenceLevel = confidenceLevel;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ResourceType;
        yield return HistoricalDataStart;
        yield return HistoricalDataEnd;
        yield return ForecastDays;
        yield return ForecastModel;
        yield return ConfidenceLevel;
        
        foreach (var param in ModelParameters.OrderBy(x => x.Key))
        {
            yield return param.Key;
            yield return param.Value?.ToString() ?? string.Empty;
        }
        
        if (MetricsToForecast != null)
        {
            foreach (var metric in MetricsToForecast.OrderBy(x => x))
            {
                yield return metric;
            }
        }
    }
}
