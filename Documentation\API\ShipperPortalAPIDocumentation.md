# 🚀 **TLI Shipper Portal API Documentation**

## **Overview**

The TLI Shipper Portal provides a comprehensive set of RESTful APIs for managing transportation logistics operations. This documentation covers all implemented features across multiple microservices with enterprise-grade functionality.

### **Architecture**

- **Microservices Architecture**: 10+ independent services
- **Event-Driven Communication**: RabbitMQ message broker
- **Authentication**: JWT Bearer tokens with refresh token support
- **Authorization**: Role-based access control (RBAC) with fine-grained permissions
- **API Gateway**: Centralized routing and rate limiting
- **Documentation**: OpenAPI 3.0 specifications

### **Base URLs**

- **Production**: `https://api.tli.com`
- **Staging**: `https://staging-api.tli.com`
- **Development**: `https://dev-api.tli.com`

### **Authentication**

All API endpoints require authentication unless otherwise specified. Include the JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### **Rate Limiting**

- **Standard Users**: 1000 requests per hour
- **Premium Users**: 5000 requests per hour
- **Enterprise Users**: 10000 requests per hour

### **Response Format**

All responses follow a consistent JSON structure:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### **Error Handling**

Error responses include detailed information:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

---

## **🎯 Service Endpoints Overview**

| Service             | Base Path             | Description                           |
| ------------------- | --------------------- | ------------------------------------- |
| Order Management    | `/api/orders`         | RFQ management, quotes, negotiations  |
| Trip Management     | `/api/trips`          | Multi-leg trips, tracking, milestones |
| Financial & Payment | `/api/payments`       | Escrow, settlements, invoicing        |
| Network & Fleet     | `/api/network`        | Carriers, vehicles, drivers           |
| Analytics & BI      | `/api/analytics`      | Reports, dashboards, insights         |
| User Management     | `/api/users`          | Authentication, profiles, roles       |
| Communication       | `/api/communications` | Notifications, messages               |
| Data & Storage      | `/api/storage`        | File uploads, documents               |
| Mobile & Workflow   | `/api/mobile`         | Mobile app support                    |
| Audit & Compliance  | `/api/audit`          | Audit trails, compliance              |

---

## **📋 Order Management Service**

### **Base Path**: `/api/orders`

#### **Enhanced RFQ Management**

##### **Create RFQ with Advanced Features**

```http
POST /api/orders/rfqs
```

**Request Body:**

```json
{
  "shipperId": "uuid",
  "title": "Freight shipment from NY to CA",
  "description": "Temperature-controlled shipment",
  "originLocation": {
    "address": "123 Main St, New York, NY 10001",
    "latitude": 40.7128,
    "longitude": -74.006,
    "contactPerson": "John Doe",
    "contactPhone": "******-0123"
  },
  "destinationLocation": {
    "address": "456 Oak Ave, Los Angeles, CA 90210",
    "latitude": 34.0522,
    "longitude": -118.2437,
    "contactPerson": "Jane Smith",
    "contactPhone": "******-0456"
  },
  "loadDetails": {
    "weight": 5000,
    "volume": 100,
    "dimensions": {
      "length": 10,
      "width": 8,
      "height": 6
    },
    "loadType": "General Freight",
    "specialRequirements": ["Temperature Controlled", "Fragile"],
    "hazmatClass": null
  },
  "scheduledPickupDate": "2024-02-01T09:00:00Z",
  "scheduledDeliveryDate": "2024-02-03T17:00:00Z",
  "budgetRange": {
    "min": 2000,
    "max": 3000,
    "currency": "USD"
  },
  "preferredCarriers": ["carrier-uuid-1", "carrier-uuid-2"],
  "autoAssignSettings": {
    "enabled": true,
    "maxResponseTime": "24:00:00",
    "minRating": 4.0,
    "preferredPartnersOnly": false
  },
  "standardTimeframe": {
    "responseDeadline": "2024-01-20T23:59:59Z",
    "extensionAllowed": true,
    "maxExtensionHours": 48
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "rfqId": "rfq_123456789",
    "rfqNumber": "RFQ-2024-001",
    "status": "Published",
    "createdAt": "2024-01-15T10:30:00Z",
    "expiresAt": "2024-01-20T23:59:59Z",
    "estimatedQuotes": 15,
    "notificationsSent": 25,
    "trackingUrl": "https://portal.tli.com/rfqs/rfq_123456789"
  }
}
```

##### **Advanced Quote Comparison**

```http
POST /api/orders/quotes/compare
```

**Request Body:**

```json
{
  "rfqId": "rfq_123456789",
  "quoteIds": ["quote_1", "quote_2", "quote_3"],
  "comparisonCriteria": [
    "price",
    "deliveryTime",
    "carrierRating",
    "safetyScore",
    "onTimePerformance"
  ],
  "weightings": {
    "price": 0.3,
    "deliveryTime": 0.2,
    "carrierRating": 0.2,
    "safetyScore": 0.15,
    "onTimePerformance": 0.15
  },
  "includeRecommendations": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "comparisonId": "comp_123456789",
    "rfqId": "rfq_123456789",
    "quotes": [
      {
        "quoteId": "quote_1",
        "carrierId": "carrier_1",
        "carrierName": "ABC Logistics",
        "totalScore": 8.5,
        "ranking": 1,
        "price": {
          "amount": 2500,
          "currency": "USD"
        },
        "deliveryTime": "48 hours",
        "carrierRating": 4.8,
        "safetyScore": 4.9,
        "onTimePerformance": 96.5,
        "strengths": ["Excellent safety record", "Fast delivery"],
        "considerations": ["Slightly higher price"],
        "recommendation": "Highly Recommended"
      }
    ],
    "summary": {
      "bestOverall": "quote_1",
      "bestPrice": "quote_3",
      "fastestDelivery": "quote_2",
      "highestRated": "quote_1"
    },
    "insights": [
      "Quote 1 offers the best balance of price and quality",
      "All quotes meet your delivery requirements"
    ]
  }
}
```

##### **Negotiation Workflow**

```http
POST /api/orders/negotiations/counter-offer
```

**Request Body:**

```json
{
  "quoteId": "quote_123456789",
  "counterOfferAmount": 2200,
  "counterOfferCurrency": "USD",
  "proposedChanges": {
    "deliveryDate": "2024-02-04T17:00:00Z",
    "additionalServices": ["Liftgate Service"],
    "paymentTerms": "Net 30"
  },
  "message": "We appreciate your quote. Can you match this price with the requested changes?",
  "expiresAt": "2024-01-18T23:59:59Z"
}
```

---

## **🚛 Trip Management Service**

### **Base Path**: `/api/trips`

#### **Multi-Leg Trip Management**

##### **Create Multi-Leg Trip**

```http
POST /api/trips/multi-leg
```

**Request Body:**

```json
{
  "orderId": "order_123456789",
  "tripName": "NY to CA via Chicago",
  "tripType": "Multi-Leg",
  "legs": [
    {
      "sequenceNumber": 1,
      "legName": "NY to Chicago",
      "originLocation": {
        "address": "123 Main St, New York, NY 10001",
        "latitude": 40.7128,
        "longitude": -74.006
      },
      "destinationLocation": {
        "address": "789 State St, Chicago, IL 60601",
        "latitude": 41.8781,
        "longitude": -87.6298
      },
      "scheduledStartTime": "2024-02-01T09:00:00Z",
      "scheduledEndTime": "2024-02-02T18:00:00Z",
      "estimatedDistance": 790.5,
      "estimatedDuration": "12:00:00",
      "milestones": [
        {
          "name": "Pickup Confirmation",
          "type": "Pickup",
          "scheduledDateTime": "2024-02-01T09:30:00Z",
          "location": {
            "address": "123 Main St, New York, NY 10001"
          },
          "requirements": ["Photo of BOL", "Signature"],
          "isRequired": true
        }
      ]
    }
  ],
  "overallMilestones": [
    {
      "name": "Trip Started",
      "type": "Checkpoint",
      "scheduledDateTime": "2024-02-01T09:00:00Z"
    }
  ]
}
```

##### **Enhanced Trip Status Visualization**

```http
GET /api/trips/{tripId}/status/visualization
```

**Query Parameters:**

- `includeTimeline`: boolean (default: true)
- `includeMilestones`: boolean (default: true)
- `includeNotifications`: boolean (default: true)
- `includeDelayAlerts`: boolean (default: true)
- `includeMetrics`: boolean (default: true)
- `includeLocationTracking`: boolean (default: true)
- `detailLevel`: enum (Basic, Standard, Detailed, Comprehensive)

**Response:**

```json
{
  "success": true,
  "data": {
    "tripId": "trip_123456789",
    "tripNumber": "TRP-2024-001",
    "status": "InProgress",
    "overallProgress": 65.5,
    "timeline": {
      "startDate": "2024-02-01T09:00:00Z",
      "endDate": "2024-02-03T17:00:00Z",
      "timelineProgress": 45.2,
      "isOnSchedule": false,
      "delayDuration": "02:30:00",
      "timelineStatus": "Delayed"
    },
    "milestones": [
      {
        "milestoneId": "milestone_1",
        "name": "Pickup Confirmation",
        "status": "Completed",
        "progress": 100,
        "scheduledDateTime": "2024-02-01T09:30:00Z",
        "actualDateTime": "2024-02-01T09:45:00Z",
        "isOverdue": false,
        "style": {
          "color": "#28a745",
          "icon": "check-circle"
        }
      }
    ],
    "currentLocation": {
      "latitude": 41.2619,
      "longitude": -95.8608,
      "address": "Interstate 80, Nebraska",
      "lastUpdated": "2024-02-02T14:30:00Z",
      "speed": 65.5,
      "isMoving": true,
      "estimatedTimeToNextMilestone": "04:30:00",
      "nextMilestoneName": "Chicago Arrival"
    },
    "delayAlerts": [
      {
        "alertId": "alert_1",
        "alertType": "MinorDelay",
        "title": "Traffic Delay",
        "description": "Heavy traffic causing 2.5 hour delay",
        "severity": "Medium",
        "detectedAt": "2024-02-02T12:00:00Z",
        "suggestedActions": ["Notify customer", "Adjust delivery schedule"]
      }
    ],
    "metrics": {
      "onTimePerformance": 87.5,
      "distanceEfficiency": 98.2,
      "timeEfficiency": 92.1,
      "performanceGrade": "B+"
    }
  }
}
```

---

## **💰 Financial & Payment Service**

### **Base Path**: `/api/payments`

#### **Enhanced Escrow Management**

##### **Create Escrow Account with Milestones**

```http
POST /api/payments/escrow
```

**Request Body:**

```json
{
  "orderId": "order_123456789",
  "transportCompanyId": "company_123",
  "brokerId": "broker_123",
  "carrierId": "carrier_123",
  "totalAmount": {
    "amount": 2500.0,
    "currency": "USD"
  },
  "fundingSource": "CreditCard",
  "paymentMilestones": [
    {
      "name": "Pickup Completion",
      "description": "Payment released upon successful pickup",
      "amount": {
        "amount": 1000.0,
        "currency": "USD"
      },
      "payoutPercentage": 40.0,
      "sequenceNumber": 1,
      "dueDate": "2024-02-01T18:00:00Z",
      "isRequired": true,
      "requiresApproval": false,
      "completionCriteria": ["POD Uploaded", "Customer Confirmation"]
    },
    {
      "name": "Delivery Completion",
      "description": "Final payment upon delivery",
      "amount": {
        "amount": 1500.0,
        "currency": "USD"
      },
      "payoutPercentage": 60.0,
      "sequenceNumber": 2,
      "dueDate": "2024-02-03T18:00:00Z",
      "isRequired": true,
      "requiresApproval": true,
      "completionCriteria": [
        "Delivery Confirmation",
        "Customer Signature",
        "Final Invoice"
      ]
    }
  ],
  "disputeSettings": {
    "allowDisputes": true,
    "disputeDeadlineDays": 7,
    "autoReleaseAfterDays": 14
  }
}
```

##### **Enhanced Escrow Account Details**

```http
GET /api/payments/escrow/{escrowAccountId}/enhanced
```

**Query Parameters:**

- `includeTransactions`: boolean
- `includeMilestoneDocuments`: boolean
- `includeDisputeHistory`: boolean

**Response:**

```json
{
  "success": true,
  "data": {
    "escrowAccountId": "escrow_123456789",
    "accountNumber": "ESC-2024-001",
    "status": "Active",
    "totalAmount": {
      "amount": 2500.0,
      "currency": "USD"
    },
    "availableBalance": {
      "amount": 2500.0,
      "currency": "USD"
    },
    "heldBalance": {
      "amount": 0.0,
      "currency": "USD"
    },
    "releasedBalance": {
      "amount": 0.0,
      "currency": "USD"
    },
    "milestones": [
      {
        "milestoneId": "milestone_1",
        "name": "Pickup Completion",
        "status": "Pending",
        "amount": {
          "amount": 1000.0,
          "currency": "USD"
        },
        "dueDate": "2024-02-01T18:00:00Z",
        "completionCriteria": ["POD Uploaded", "Customer Confirmation"],
        "documentsRequired": 2,
        "documentsUploaded": 0,
        "canRelease": false
      }
    ],
    "transactions": [
      {
        "transactionId": "txn_1",
        "type": "Deposit",
        "amount": {
          "amount": 2500.0,
          "currency": "USD"
        },
        "timestamp": "2024-01-15T10:30:00Z",
        "description": "Initial escrow funding"
      }
    ],
    "participants": {
      "shipper": {
        "id": "shipper_123",
        "name": "ABC Company",
        "role": "Payer"
      },
      "carrier": {
        "id": "carrier_123",
        "name": "XYZ Logistics",
        "role": "Payee"
      }
    }
  }
}
```

---

## **🚚 Network & Fleet Management Service**

### **Base Path**: `/api/network`

#### **Enhanced Preferred Partner Management**

##### **Create Partner Wishlist**

```http
POST /api/network/preferred-partners/wishlist
```

**Request Body:**

```json
{
  "wishlistName": "Premium Carriers",
  "description": "High-performance carriers for critical shipments",
  "partnerType": "Carrier",
  "partners": [
    {
      "partnerId": "carrier_123",
      "preferenceLevel": "Primary",
      "priority": 1,
      "preferredCommissionRate": 5.0,
      "autoAssignEnabled": true,
      "preferredRoutes": ["NY-CA", "TX-FL"],
      "notifyOnNewOpportunities": true
    }
  ],
  "settings": {
    "autoAddHighPerformers": true,
    "autoAddThreshold": 4.5,
    "enablePerformanceAlerts": true
  }
}
```

##### **Generate Partner Performance Analytics**

```http
POST /api/network/preferred-partners/analytics/generate
```

**Request Body:**

```json
{
  "partnerType": "Carrier",
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "scope": "PreferredPartnersOnly",
  "metrics": [
    "OnTimeDeliveryRate",
    "CustomerSatisfactionScore",
    "SafetyRating"
  ],
  "includePredictiveInsights": true,
  "includeRecommendations": true,
  "includeBenchmarking": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "analyticsId": "analytics_123456789",
    "generatedAt": "2024-01-15T10:30:00Z",
    "period": {
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-01-31T23:59:59Z"
    },
    "overallMetrics": {
      "averageOnTimeDeliveryRate": 94.2,
      "averageCustomerSatisfactionScore": 4.6,
      "averageSafetyRating": 4.8,
      "totalTripsCompleted": 1250,
      "totalPartnersAnalyzed": 25
    },
    "topPerformers": [
      {
        "partnerId": "carrier_123",
        "partnerName": "ABC Logistics",
        "overallScore": 9.2,
        "onTimeDeliveryRate": 98.5,
        "customerSatisfactionScore": 4.9,
        "safetyRating": 4.9,
        "performanceGrade": "A+",
        "strengths": [
          "Excellent safety record",
          "Outstanding customer service"
        ],
        "improvementAreas": []
      }
    ],
    "underPerformers": [],
    "insights": [
      {
        "type": "Performance",
        "title": "Exceptional Performance Period",
        "description": "All preferred partners exceeded performance targets",
        "importance": "High",
        "confidence": 95.5
      }
    ],
    "recommendations": [
      "Consider expanding partnership with ABC Logistics",
      "Implement performance-based incentives"
    ]
  }
}
```

##### **Get Enhanced Partner Recommendations**

```http
GET /api/network/preferred-partners/recommendations/enhanced
```

**Query Parameters:**

- `criteria`: enum (CostOptimized, QualityFocused, SpeedPrioritized, Balanced)
- `serviceAreas`: array of strings
- `loadTypes`: array of strings
- `budgetRange`: decimal
- `maxRecommendations`: integer (default: 10)
- `includePerformanceAnalysis`: boolean (default: true)

**Response:**

```json
{
  "success": true,
  "data": {
    "userId": "user_123",
    "generatedAt": "2024-01-15T10:30:00Z",
    "criteria": "Balanced",
    "totalRecommendations": 8,
    "recommendations": [
      {
        "partnerId": "carrier_456",
        "partnerName": "XYZ Transport",
        "partnerType": "Carrier",
        "recommendationScore": 8.7,
        "matchPercentage": 92.5,
        "recommendationReason": "Excellent overall rating, outstanding on-time delivery",
        "estimatedCost": {
          "amount": 2200,
          "currency": "USD"
        },
        "estimatedDeliveryTime": "48:00:00",
        "performanceRating": 4.8,
        "safetyRating": 4.9,
        "onTimeDeliveryRate": 96.8,
        "isNewPartner": true,
        "isAvailable": true,
        "strengths": ["High customer satisfaction", "Excellent safety record"],
        "considerations": ["New to your network"],
        "performanceAnalysis": {
          "totalTripsCompleted": 2450,
          "averageResponseTimeHours": 2.5,
          "reliabilityScore": 9.1,
          "qualityScore": 8.9
        }
      }
    ],
    "insights": [
      {
        "type": "Quality",
        "title": "High-Quality Partners Available",
        "description": "Found 8 partners with excellent ratings and performance",
        "impact": "Positive"
      }
    ],
    "suggestions": [
      {
        "action": "Add to Preferred Partners",
        "description": "Consider adding XYZ Transport to your preferred partners list",
        "priority": "High",
        "partnerIds": ["carrier_456"],
        "expectedBenefit": "Improved service quality and reliability"
      }
    ]
  }
}
```

---

## **📊 Analytics & BI Service**

### **Base Path**: `/api/analytics`

#### **Comprehensive Reporting**

##### **Generate Comprehensive Report**

```http
POST /api/analytics/comprehensive-reporting/generate
```

**Request Body:**

```json
{
  "reportType": "ShipperPortalSummary",
  "dateRange": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z",
    "period": "Monthly"
  },
  "dataSources": [
    "OrderManagement",
    "TripManagement",
    "FinancialPayment",
    "NetworkFleetManagement"
  ],
  "metrics": [
    "TotalOrders",
    "OnTimeDeliveryRate",
    "AverageOrderValue",
    "PartnerPerformance"
  ],
  "includeVisualizations": true,
  "includeInsights": true,
  "includePredictiveAnalytics": true,
  "outputFormat": "PDF"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "reportId": "report_123456789",
    "reportName": "ShipperPortalSummary Report - 2024-01-01 to 2024-01-31",
    "reportType": "ShipperPortalSummary",
    "generatedAt": "2024-01-15T10:30:00Z",
    "summary": {
      "totalDataPoints": 15420,
      "totalSections": 6,
      "totalVisualizations": 12,
      "totalInsights": 8,
      "performanceScore": 87.5,
      "keyFindings": [
        "Order volume increased 15% compared to previous month",
        "On-time delivery rate improved to 94.2%",
        "Customer satisfaction scores reached all-time high"
      ],
      "recommendations": [
        "Consider expanding partnership network",
        "Implement predictive analytics for demand forecasting"
      ]
    },
    "sections": [
      {
        "sectionId": "executive-summary",
        "title": "Executive Summary",
        "order": 1,
        "sectionType": "Summary"
      }
    ],
    "visualizations": [
      {
        "visualizationId": "viz_1",
        "title": "Monthly Order Volume Trend",
        "type": "Line",
        "configuration": {
          "xAxisLabel": "Date",
          "yAxisLabel": "Order Count",
          "showLegend": true
        }
      }
    ],
    "insights": [
      {
        "insightId": "insight_1",
        "type": "Trend",
        "title": "Positive Growth Trend",
        "description": "Order volume shows consistent upward trend",
        "importance": "High",
        "confidence": 92.5
      }
    ]
  }
}
```

##### **Cross-Service Analytics**

```http
POST /api/analytics/comprehensive-reporting/cross-service-analytics
```

**Request Body:**

```json
{
  "services": ["OrderManagement", "TripManagement", "FinancialPayment"],
  "dateRange": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z"
  },
  "metrics": ["OrderVolume", "TripCompletionRate", "PaymentProcessingTime"],
  "includeCorrelations": true,
  "includeInsights": true,
  "correlationThreshold": 0.5,
  "depth": "Standard"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "analytics_123456789",
    "generatedAt": "2024-01-15T10:30:00Z",
    "services": ["OrderManagement", "TripManagement", "FinancialPayment"],
    "serviceData": {
      "OrderManagement": {
        "serviceName": "OrderManagement",
        "healthStatus": "Healthy",
        "metrics": {
          "TotalOrders": 1250,
          "AverageOrderValue": 2500.5,
          "OrderCompletionRate": 98.2
        }
      }
    },
    "correlations": [
      {
        "service1": "OrderManagement",
        "service2": "TripManagement",
        "metric1": "OrderVolume",
        "metric2": "TripCompletionRate",
        "correlationCoefficient": 0.85,
        "correlationStrength": "Strong",
        "correlationType": "Positive",
        "significance": 0.95
      }
    ],
    "insights": [
      {
        "insightType": "Correlation",
        "title": "Strong Order-Trip Correlation",
        "description": "High correlation between order volume and trip completion rates",
        "importance": "High",
        "affectedServices": ["OrderManagement", "TripManagement"]
      }
    ],
    "summary": {
      "totalServices": 3,
      "totalDataPoints": 45000,
      "strongCorrelations": 2,
      "keyInsights": 5,
      "overallHealthScore": "92.5%"
    }
  }
}
```
