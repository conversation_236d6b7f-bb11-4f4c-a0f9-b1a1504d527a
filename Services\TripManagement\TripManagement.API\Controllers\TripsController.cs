using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.AssignTrip;
using TripManagement.Application.Commands.CreateTrip;
using TripManagement.Application.Commands.StartTrip;
using TripManagement.Application.Commands.CompleteTrip;
using TripManagement.Application.Commands.UpdateTripLocation;
using TripManagement.Application.Commands.CancelTrip;
using TripManagement.Application.Commands.ReassignDriver;
using TripManagement.Application.Commands.SubmitTripFeedback;
using TripManagement.Application.Queries.GetTripFeedbackAnalytics;
using TripManagement.Application.DTOs;
using TripManagement.Application.Queries.GetTripDetails;
using TripManagement.Application.Queries.GetTripsByCarrier;
using TripManagement.Application.Queries.GetCarrierTripsWithMilestones;
using TripManagement.Application.Queries.GetCarrierTripPerformance;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TripsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TripsController> _logger;

    public TripsController(IMediator mediator, ILogger<TripsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a new trip from an order
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<ActionResult<Guid>> CreateTrip([FromBody] CreateTripCommand command)
    {
        try
        {
            var tripId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetTripById), new { id = tripId }, tripId);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating trip");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating trip");
            return StatusCode(500, "An error occurred while creating the trip");
        }
    }

    /// <summary>
    /// Get trip details by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<TripDto>> GetTripById(Guid id)
    {
        try
        {
            var trip = await _mediator.Send(new GetTripDetailsQuery(id));

            if (trip == null)
                return NotFound($"Trip with ID {id} not found");

            return Ok(trip);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving trip {TripId}", id);
            return StatusCode(500, "An error occurred while retrieving the trip");
        }
    }

    /// <summary>
    /// Assign driver and vehicle to a trip
    /// </summary>
    [HttpPost("{id:guid}/assign")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult> AssignTrip(Guid id, [FromBody] AssignTripRequest request)
    {
        try
        {
            var command = new AssignTripCommand
            {
                TripId = id,
                DriverId = request.DriverId,
                VehicleId = request.VehicleId
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Trip assigned successfully" });

            return BadRequest("Failed to assign trip");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while assigning trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while assigning trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning trip {TripId}", id);
            return StatusCode(500, "An error occurred while assigning the trip");
        }
    }

    /// <summary>
    /// Update trip location (typically called by mobile app)
    /// </summary>
    [HttpPost("{id:guid}/location")]
    [Authorize(Roles = "Driver,Carrier")]
    public async Task<ActionResult> UpdateTripLocation(Guid id, [FromBody] UpdateLocationRequest request)
    {
        try
        {
            var command = new UpdateTripLocationCommand
            {
                TripId = id,
                Location = request.Location,
                Source = request.Source,
                Speed = request.Speed,
                Heading = request.Heading
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Location updated successfully" });

            return BadRequest("Failed to update location");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while updating location for trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating location for trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating location for trip {TripId}", id);
            return StatusCode(500, "An error occurred while updating the trip location");
        }
    }

    /// <summary>
    /// Start a trip
    /// </summary>
    [HttpPost("{id:guid}/start")]
    [Authorize(Roles = "Driver,Carrier")]
    public async Task<ActionResult> StartTrip(Guid id, [FromBody] StartTripRequest request)
    {
        try
        {
            var command = new StartTripCommand
            {
                TripId = id,
                DriverId = request.DriverId,
                ActualStartTime = request.ActualStartTime ?? DateTime.UtcNow,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Trip started successfully" });

            return BadRequest("Failed to start trip");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while starting trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while starting trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting trip {TripId}", id);
            return StatusCode(500, "An error occurred while starting the trip");
        }
    }

    /// <summary>
    /// Complete a trip
    /// </summary>
    [HttpPost("{id:guid}/complete")]
    [Authorize(Roles = "Driver,Carrier")]
    public async Task<ActionResult> CompleteTrip(Guid id, [FromBody] CompleteTripRequest request)
    {
        try
        {
            var command = new CompleteTripCommand
            {
                TripId = id,
                DriverId = request.DriverId,
                ActualEndTime = request.ActualEndTime ?? DateTime.UtcNow,
                ActualDistanceKm = request.ActualDistanceKm,
                CompletionNotes = request.CompletionNotes,
                ProofOfDeliveryUploaded = request.ProofOfDeliveryUploaded
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Trip completed successfully" });

            return BadRequest("Failed to complete trip");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while completing trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while completing trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing trip {TripId}", id);
            return StatusCode(500, "An error occurred while completing the trip");
        }
    }

    /// <summary>
    /// Get trips by carrier
    /// </summary>
    [HttpGet("carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult<PagedResult<TripSummaryDto>>> GetTripsByCarrier(
        Guid carrierId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? searchTerm = null)
    {
        try
        {
            Domain.Enums.TripStatus? tripStatus = null;
            if (!string.IsNullOrEmpty(status) && Enum.TryParse<Domain.Enums.TripStatus>(status, true, out var parsedStatus))
            {
                tripStatus = parsedStatus;
            }

            var query = new GetTripsByCarrierQuery
            {
                CarrierId = carrierId,
                Page = page,
                PageSize = pageSize,
                Status = tripStatus,
                FromDate = fromDate,
                ToDate = toDate,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trips for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving trips");
        }
    }

    /// <summary>
    /// Get trips dashboard for carrier with milestone tracking and POD information
    /// </summary>
    [HttpGet("dashboard/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<PagedResult<TripDashboardDto>>> GetCarrierTripsDashboard(
        Guid carrierId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] TripStatus? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool? isDelayed = null,
        [FromQuery] bool? hasExceptions = null,
        [FromQuery] bool? isCompleted = null,
        [FromQuery] string? searchTerm = null)
    {
        try
        {
            var query = new GetCarrierTripsWithMilestonesQuery(
                carrierId, pageNumber, pageSize, status, fromDate, toDate,
                isDelayed, hasExceptions, isCompleted, searchTerm);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trips dashboard for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving trips dashboard");
        }
    }

    /// <summary>
    /// Get trip performance metrics for carrier
    /// </summary>
    [HttpGet("performance/carrier/{carrierId:guid}")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<CarrierTripPerformanceDto>> GetCarrierTripPerformance(
        Guid carrierId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] bool includeTrends = true,
        [FromQuery] bool includeDriverPerformance = true,
        [FromQuery] bool includeVehiclePerformance = true,
        [FromQuery] int topPerformersCount = 5)
    {
        try
        {
            var query = new GetCarrierTripPerformanceQuery(
                carrierId, fromDate, toDate, includeTrends,
                includeDriverPerformance, includeVehiclePerformance, topPerformersCount);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trip performance for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving trip performance");
        }
    }

    /// <summary>
    /// Cancel a trip
    /// </summary>
    [HttpPost("{id:guid}/cancel")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult> CancelTrip(Guid id, [FromBody] CancelTripRequest request)
    {
        try
        {
            var command = new CancelTripCommand
            {
                TripId = id,
                Reason = request.Reason,
                CancelledBy = request.CancelledBy,
                AdditionalNotes = request.AdditionalNotes,
                NotifyStakeholders = request.NotifyStakeholders
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Trip cancelled successfully" });

            return BadRequest("Failed to cancel trip");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while cancelling trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while cancelling trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling trip {TripId}", id);
            return StatusCode(500, "An error occurred while cancelling the trip");
        }
    }

    /// <summary>
    /// Submit feedback for a completed trip
    /// </summary>
    [HttpPost("{id:guid}/feedback")]
    [Authorize(Roles = "Admin,Carrier,Broker,Shipper")]
    public async Task<ActionResult<SubmitTripFeedbackResponse>> SubmitTripFeedback(Guid id, [FromBody] SubmitTripFeedbackRequest request)
    {
        try
        {
            var command = new SubmitTripFeedbackCommand
            {
                TripId = id,
                ReviewerId = request.ReviewerId,
                ReviewerRole = request.ReviewerRole,
                RevieweeId = request.RevieweeId,
                RevieweeRole = request.RevieweeRole,
                OverallRating = request.OverallRating,
                ServiceQualityRating = request.ServiceQualityRating,
                TimelinessRating = request.TimelinessRating,
                CommunicationRating = request.CommunicationRating,
                ProfessionalismRating = request.ProfessionalismRating,
                VehicleConditionRating = request.VehicleConditionRating,
                CargoHandlingRating = request.CargoHandlingRating,
                Comments = request.Comments,
                PositiveAspects = request.PositiveAspects,
                ImprovementAreas = request.ImprovementAreas,
                Tags = request.Tags,
                FeedbackType = request.FeedbackType,
                IsAnonymous = request.IsAnonymous,
                NotifyReviewee = request.NotifyReviewee,
                RequestFollowUp = request.RequestFollowUp,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
                return BadRequest(result.Message);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting feedback for trip {TripId}", id);
            return StatusCode(500, "An error occurred while submitting feedback");
        }
    }

    /// <summary>
    /// Get feedback analytics for trips
    /// </summary>
    [HttpGet("feedback/analytics")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult<TripFeedbackAnalyticsResponse>> GetFeedbackAnalytics([FromQuery] GetFeedbackAnalyticsRequest request)
    {
        try
        {
            var query = new GetTripFeedbackAnalyticsQuery
            {
                CarrierId = request.CarrierId,
                DriverId = request.DriverId,
                VehicleId = request.VehicleId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                ReviewerRole = request.ReviewerRole,
                MinRating = request.MinRating,
                MaxRating = request.MaxRating,
                IncludeTrends = request.IncludeTrends,
                IncludeComparisons = request.IncludeComparisons,
                GroupBy = request.GroupBy
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feedback analytics");
            return StatusCode(500, "An error occurred while retrieving feedback analytics");
        }
    }

    /// <summary>
    /// Reassign driver for a trip
    /// </summary>
    [HttpPost("{id:guid}/reassign-driver")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult> ReassignDriver(Guid id, [FromBody] ReassignDriverRequest request)
    {
        try
        {
            var command = new ReassignDriverCommand
            {
                TripId = id,
                NewDriverId = request.NewDriverId,
                NewVehicleId = request.NewVehicleId,
                Reason = request.Reason,
                ReassignedBy = request.ReassignedBy,
                AdditionalNotes = request.AdditionalNotes,
                NotifyStakeholders = request.NotifyStakeholders
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Driver reassigned successfully" });

            return BadRequest("Failed to reassign driver");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while reassigning driver for trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while reassigning driver for trip {TripId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reassigning driver for trip {TripId}", id);
            return StatusCode(500, "An error occurred while reassigning the driver");
        }
    }
}

// Request DTOs
public record AssignTripRequest
{
    public Guid DriverId { get; init; }
    public Guid VehicleId { get; init; }
}

public record StartTripRequest
{
    public Guid DriverId { get; init; }
    public DateTime? ActualStartTime { get; init; }
    public string? Notes { get; init; }
}

public record CompleteTripRequest
{
    public Guid DriverId { get; init; }
    public DateTime? ActualEndTime { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public string? CompletionNotes { get; init; }
    public bool ProofOfDeliveryUploaded { get; init; }
}

public record UpdateLocationRequest
{
    public LocationDto Location { get; init; } = null!;
    public Domain.Enums.LocationUpdateSource Source { get; init; } = Domain.Enums.LocationUpdateSource.GPS;
    public double? Speed { get; init; }
    public double? Heading { get; init; }
}

public record CancelTripRequest
{
    public string Reason { get; init; } = string.Empty;
    public Guid CancelledBy { get; init; }
    public string? AdditionalNotes { get; init; }
    public bool NotifyStakeholders { get; init; } = true;
}

public record ReassignDriverRequest
{
    public Guid NewDriverId { get; init; }
    public Guid? NewVehicleId { get; init; }
    public string Reason { get; init; } = string.Empty;
    public Guid ReassignedBy { get; init; }
    public string? AdditionalNotes { get; init; }
    public bool NotifyStakeholders { get; init; } = true;
}

public record SubmitTripFeedbackRequest
{
    public Guid ReviewerId { get; init; }
    public string ReviewerRole { get; init; } = string.Empty;
    public Guid RevieweeId { get; init; }
    public string RevieweeRole { get; init; } = string.Empty;

    // Ratings (1-5 scale)
    public decimal OverallRating { get; init; }
    public decimal ServiceQualityRating { get; init; }
    public decimal TimelinessRating { get; init; }
    public decimal CommunicationRating { get; init; }
    public decimal ProfessionalismRating { get; init; }
    public decimal VehicleConditionRating { get; init; }
    public decimal CargoHandlingRating { get; init; }

    // Feedback content
    public string Comments { get; init; } = string.Empty;
    public List<string> PositiveAspects { get; init; } = new();
    public List<string> ImprovementAreas { get; init; } = new();
    public List<string> Tags { get; init; } = new();

    // Options
    public Domain.Entities.FeedbackType FeedbackType { get; init; } = Domain.Entities.FeedbackType.PostTrip;
    public bool IsAnonymous { get; init; } = false;
    public bool NotifyReviewee { get; init; } = true;
    public bool RequestFollowUp { get; init; } = false;

    // Additional data
    public Dictionary<string, object> Metadata { get; init; } = new();
}

public record GetFeedbackAnalyticsRequest
{
    public Guid? CarrierId { get; init; }
    public Guid? DriverId { get; init; }
    public Guid? VehicleId { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? ReviewerRole { get; init; }
    public decimal? MinRating { get; init; }
    public decimal? MaxRating { get; init; }
    public bool IncludeTrends { get; init; } = true;
    public bool IncludeComparisons { get; init; } = true;
    public string GroupBy { get; init; } = "Month";
}
