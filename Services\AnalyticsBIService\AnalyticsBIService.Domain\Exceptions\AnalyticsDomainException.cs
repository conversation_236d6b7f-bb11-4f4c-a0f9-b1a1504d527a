namespace AnalyticsBIService.Domain.Exceptions;

/// <summary>
/// Base exception for all analytics domain-related exceptions
/// </summary>
public abstract class AnalyticsDomainException : Exception
{
    protected AnalyticsDomainException(string message) : base(message)
    {
    }

    protected AnalyticsDomainException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when an analytics event is invalid
/// </summary>
public class InvalidAnalyticsEventException : AnalyticsDomainException
{
    public InvalidAnalyticsEventException(string message) : base(message)
    {
    }

    public InvalidAnalyticsEventException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when a metric calculation fails
/// </summary>
public class MetricCalculationException : AnalyticsDomainException
{
    public string MetricName { get; }

    public MetricCalculationException(string metricName, string message) : base(message)
    {
        MetricName = metricName;
    }

    public MetricCalculationException(string metricName, string message, Exception innerException) : base(message, innerException)
    {
        MetricName = metricName;
    }
}

/// <summary>
/// Exception thrown when an alert configuration is invalid
/// </summary>
public class InvalidAlertConfigurationException : AnalyticsDomainException
{
    public InvalidAlertConfigurationException(string message) : base(message)
    {
    }

    public InvalidAlertConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when a dashboard operation fails
/// </summary>
public class DashboardOperationException : AnalyticsDomainException
{
    public Guid DashboardId { get; }

    public DashboardOperationException(Guid dashboardId, string message) : base(message)
    {
        DashboardId = dashboardId;
    }

    public DashboardOperationException(Guid dashboardId, string message, Exception innerException) : base(message, innerException)
    {
        DashboardId = dashboardId;
    }
}

/// <summary>
/// Exception thrown when a report generation fails
/// </summary>
public class ReportGenerationException : AnalyticsDomainException
{
    public Guid ReportId { get; }

    public ReportGenerationException(Guid reportId, string message) : base(message)
    {
        ReportId = reportId;
    }

    public ReportGenerationException(Guid reportId, string message, Exception innerException) : base(message, innerException)
    {
        ReportId = reportId;
    }
}

/// <summary>
/// Exception thrown when data aggregation fails
/// </summary>
public class DataAggregationException : AnalyticsDomainException
{
    public string AggregationType { get; }

    public DataAggregationException(string aggregationType, string message) : base(message)
    {
        AggregationType = aggregationType;
    }

    public DataAggregationException(string aggregationType, string message, Exception innerException) : base(message, innerException)
    {
        AggregationType = aggregationType;
    }
}

/// <summary>
/// Exception thrown when real-time analytics processing fails
/// </summary>
public class RealTimeAnalyticsException : AnalyticsDomainException
{
    public RealTimeAnalyticsException(string message) : base(message)
    {
    }

    public RealTimeAnalyticsException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when analytics data validation fails
/// </summary>
public class AnalyticsDataValidationException : AnalyticsDomainException
{
    public string ValidationRule { get; }

    public AnalyticsDataValidationException(string validationRule, string message) : base(message)
    {
        ValidationRule = validationRule;
    }

    public AnalyticsDataValidationException(string validationRule, string message, Exception innerException) : base(message, innerException)
    {
        ValidationRule = validationRule;
    }
}
