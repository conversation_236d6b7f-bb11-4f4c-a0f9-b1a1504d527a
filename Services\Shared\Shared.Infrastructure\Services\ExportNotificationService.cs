using Microsoft.Extensions.Logging;
using System.Text;

namespace Shared.Infrastructure.Services
{
    /// <summary>
    /// Service for sending export completion notifications
    /// </summary>
    public interface IExportNotificationService
    {
        Task SendExportCompletionNotificationAsync(ExportCompletionNotification notification);
        Task SendExportFailureNotificationAsync(ExportFailureNotification notification);
        Task SendExportProgressNotificationAsync(ExportProgressNotification notification);
    }

    public class ExportNotificationService : IExportNotificationService
    {
        private readonly ILogger<ExportNotificationService> _logger;
        // In a real implementation, you would inject an email service like SendGrid, SMTP, etc.

        public ExportNotificationService(ILogger<ExportNotificationService> logger)
        {
            _logger = logger;
        }

        public async Task SendExportCompletionNotificationAsync(ExportCompletionNotification notification)
        {
            try
            {
                var emailContent = GenerateCompletionEmailContent(notification);
                
                // In a real implementation, send the actual email
                await SendEmailAsync(
                    to: notification.UserEmail,
                    subject: $"Export Completed: {notification.ExportType}",
                    content: emailContent,
                    isHtml: true
                );

                _logger.LogInformation("Export completion notification sent to {Email} for export {ExportId}", 
                    notification.UserEmail, notification.ExportId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send export completion notification to {Email}", notification.UserEmail);
            }
        }

        public async Task SendExportFailureNotificationAsync(ExportFailureNotification notification)
        {
            try
            {
                var emailContent = GenerateFailureEmailContent(notification);
                
                await SendEmailAsync(
                    to: notification.UserEmail,
                    subject: $"Export Failed: {notification.ExportType}",
                    content: emailContent,
                    isHtml: true
                );

                _logger.LogInformation("Export failure notification sent to {Email} for export {ExportId}", 
                    notification.UserEmail, notification.ExportId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send export failure notification to {Email}", notification.UserEmail);
            }
        }

        public async Task SendExportProgressNotificationAsync(ExportProgressNotification notification)
        {
            try
            {
                // Only send progress notifications for long-running exports
                if (notification.EstimatedRemainingTime > TimeSpan.FromMinutes(5))
                {
                    var emailContent = GenerateProgressEmailContent(notification);
                    
                    await SendEmailAsync(
                        to: notification.UserEmail,
                        subject: $"Export In Progress: {notification.ExportType}",
                        content: emailContent,
                        isHtml: true
                    );

                    _logger.LogInformation("Export progress notification sent to {Email} for export {ExportId}", 
                        notification.UserEmail, notification.ExportId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send export progress notification to {Email}", notification.UserEmail);
            }
        }

        private async Task SendEmailAsync(string to, string subject, string content, bool isHtml = false)
        {
            // Mock implementation - in reality, you would use an email service
            _logger.LogInformation("Sending email to {To} with subject: {Subject}", to, subject);
            
            // Simulate email sending delay
            await Task.Delay(100);
            
            // In a real implementation:
            // await _emailService.SendAsync(new EmailMessage
            // {
            //     To = to,
            //     Subject = subject,
            //     Body = content,
            //     IsHtml = isHtml
            // });
        }

        private string GenerateCompletionEmailContent(ExportCompletionNotification notification)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html><head><title>Export Completed</title></head><body>");
            html.AppendLine("<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>");
            
            html.AppendLine("<h2 style='color: #28a745;'>Export Completed Successfully</h2>");
            html.AppendLine($"<p>Your <strong>{notification.ExportType}</strong> export has been completed successfully.</p>");
            
            html.AppendLine("<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>");
            html.AppendLine("<h3>Export Details:</h3>");
            html.AppendLine("<ul>");
            html.AppendLine($"<li><strong>Export ID:</strong> {notification.ExportId}</li>");
            html.AppendLine($"<li><strong>File Name:</strong> {notification.FileName}</li>");
            html.AppendLine($"<li><strong>Records:</strong> {notification.RecordCount:N0}</li>");
            html.AppendLine($"<li><strong>File Size:</strong> {FormatFileSize(notification.FileSizeBytes)}</li>");
            html.AppendLine($"<li><strong>Generated:</strong> {notification.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC</li>");
            html.AppendLine($"<li><strong>Expires:</strong> {notification.ExpiresAt:yyyy-MM-dd HH:mm:ss} UTC</li>");
            html.AppendLine("</ul>");
            html.AppendLine("</div>");
            
            if (!string.IsNullOrEmpty(notification.DownloadUrl))
            {
                html.AppendLine("<div style='text-align: center; margin: 30px 0;'>");
                html.AppendLine($"<a href='{notification.DownloadUrl}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>Download Export File</a>");
                html.AppendLine("</div>");
            }
            
            html.AppendLine("<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 20px 0;'>");
            html.AppendLine("<p><strong>Important:</strong> This file will be automatically deleted after the expiration date for security purposes.</p>");
            html.AppendLine("</div>");
            
            html.AppendLine("<p>If you have any questions or need assistance, please contact our support team.</p>");
            html.AppendLine("<p>Best regards,<br>TLI Platform Team</p>");
            
            html.AppendLine("</div>");
            html.AppendLine("</body></html>");
            
            return html.ToString();
        }

        private string GenerateFailureEmailContent(ExportFailureNotification notification)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html><head><title>Export Failed</title></head><body>");
            html.AppendLine("<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>");
            
            html.AppendLine("<h2 style='color: #dc3545;'>Export Failed</h2>");
            html.AppendLine($"<p>Unfortunately, your <strong>{notification.ExportType}</strong> export could not be completed.</p>");
            
            html.AppendLine("<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>");
            html.AppendLine("<h3>Error Details:</h3>");
            html.AppendLine("<ul>");
            html.AppendLine($"<li><strong>Export ID:</strong> {notification.ExportId}</li>");
            html.AppendLine($"<li><strong>Failed At:</strong> {notification.FailedAt:yyyy-MM-dd HH:mm:ss} UTC</li>");
            html.AppendLine($"<li><strong>Error:</strong> {notification.ErrorMessage}</li>");
            html.AppendLine("</ul>");
            html.AppendLine("</div>");
            
            html.AppendLine("<div style='background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 20px 0;'>");
            html.AppendLine("<p><strong>What to do next:</strong></p>");
            html.AppendLine("<ul>");
            html.AppendLine("<li>Try reducing the date range or number of records</li>");
            html.AppendLine("<li>Check if your filters are too restrictive</li>");
            html.AppendLine("<li>Contact support if the problem persists</li>");
            html.AppendLine("</ul>");
            html.AppendLine("</div>");
            
            html.AppendLine("<p>If you continue to experience issues, please contact our support team with the Export ID above.</p>");
            html.AppendLine("<p>Best regards,<br>TLI Platform Team</p>");
            
            html.AppendLine("</div>");
            html.AppendLine("</body></html>");
            
            return html.ToString();
        }

        private string GenerateProgressEmailContent(ExportProgressNotification notification)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html><head><title>Export In Progress</title></head><body>");
            html.AppendLine("<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>");
            
            html.AppendLine("<h2 style='color: #17a2b8;'>Export In Progress</h2>");
            html.AppendLine($"<p>Your <strong>{notification.ExportType}</strong> export is currently being processed.</p>");
            
            html.AppendLine("<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>");
            html.AppendLine("<h3>Progress Details:</h3>");
            html.AppendLine("<ul>");
            html.AppendLine($"<li><strong>Export ID:</strong> {notification.ExportId}</li>");
            html.AppendLine($"<li><strong>Progress:</strong> {notification.ProgressPercentage:F1}%</li>");
            html.AppendLine($"<li><strong>Estimated Completion:</strong> {notification.EstimatedCompletionTime:yyyy-MM-dd HH:mm:ss} UTC</li>");
            html.AppendLine($"<li><strong>Records Processed:</strong> {notification.ProcessedRecords:N0}</li>");
            html.AppendLine("</ul>");
            html.AppendLine("</div>");
            
            // Progress bar
            html.AppendLine("<div style='background-color: #e9ecef; border-radius: 10px; margin: 20px 0;'>");
            html.AppendLine($"<div style='background-color: #17a2b8; height: 20px; border-radius: 10px; width: {notification.ProgressPercentage}%;'></div>");
            html.AppendLine("</div>");
            
            html.AppendLine("<p>You will receive another notification when the export is completed.</p>");
            html.AppendLine("<p>Best regards,<br>TLI Platform Team</p>");
            
            html.AppendLine("</div>");
            html.AppendLine("</body></html>");
            
            return html.ToString();
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// Export completion notification data
    /// </summary>
    public class ExportCompletionNotification
    {
        public string ExportId { get; set; } = string.Empty;
        public string ExportType { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    /// <summary>
    /// Export failure notification data
    /// </summary>
    public class ExportFailureNotification
    {
        public string ExportId { get; set; } = string.Empty;
        public string ExportType { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime FailedAt { get; set; }
    }

    /// <summary>
    /// Export progress notification data
    /// </summary>
    public class ExportProgressNotification
    {
        public string ExportId { get; set; } = string.Empty;
        public string ExportType { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public double ProgressPercentage { get; set; }
        public int ProcessedRecords { get; set; }
        public DateTime EstimatedCompletionTime { get; set; }
        public TimeSpan EstimatedRemainingTime { get; set; }
    }
}
