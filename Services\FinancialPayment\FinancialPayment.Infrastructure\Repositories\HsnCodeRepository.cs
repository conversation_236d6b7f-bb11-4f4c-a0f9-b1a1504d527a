using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class HsnCodeRepository : IHsnCodeRepository
{
    private readonly FinancialPaymentDbContext _context;

    public HsnCodeRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<HsnCode?> GetByIdAsync(Guid id)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .FirstOrDefaultAsync(h => h.Id == id);
    }

    public async Task<HsnCode?> GetByCodeAsync(string code)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .FirstOrDefaultAsync(h => h.CodeDetails.Code == code);
    }

    public async Task<List<HsnCode>> GetAllAsync()
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetActiveAsync()
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Status == HsnCodeStatus.Active)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByStatusAsync(HsnCodeStatus status)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Status == status)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByChapterAsync(string chapter)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Chapter == chapter)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetBySectionAsync(string section)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Section == section)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByGstRateAsync(GstRate gstRate)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CodeDetails.ApplicableGstRate == gstRate)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByCategoryAsync(string category)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CodeDetails.Category == category)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetEffectiveOnDateAsync(DateTime date)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Status == HsnCodeStatus.Active &&
                       h.EffectiveFrom <= date &&
                       (h.EffectiveTo == null || h.EffectiveTo >= date))
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByCodePatternAsync(string pattern)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CodeDetails.Code.StartsWith(pattern))
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByDescriptionContainsAsync(string description)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CodeDetails.Description.Contains(description))
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByCreatedByAsync(string createdBy)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CreatedBy == createdBy)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CreatedAt >= from && h.CreatedAt <= to)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetWithGstMappingsAsync()
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.GstMappings.Any())
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCode>> GetByServiceCategoryAsync(ServiceCategory serviceCategory)
    {
        // This is a simplified implementation - in reality, you'd have more complex mapping logic
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Status == HsnCodeStatus.Active)
            .OrderByDescending(h => h.CreatedAt)
            .ToListAsync();
    }

    public async Task<HsnCode> AddAsync(HsnCode hsnCode)
    {
        _context.HsnCodes.Add(hsnCode);
        return hsnCode;
    }

    public async Task UpdateAsync(HsnCode hsnCode)
    {
        _context.HsnCodes.Update(hsnCode);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var hsnCode = await _context.HsnCodes.FindAsync(id);
        if (hsnCode != null)
        {
            _context.HsnCodes.Remove(hsnCode);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.HsnCodes.AnyAsync(h => h.Id == id);
    }

    public async Task<bool> ExistsByCodeAsync(string code, Guid? excludeId = null)
    {
        var query = _context.HsnCodes.Where(h => h.CodeDetails.Code == code);
        if (excludeId.HasValue)
            query = query.Where(h => h.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<int> GetCountByStatusAsync(HsnCodeStatus status)
    {
        return await _context.HsnCodes.CountAsync(h => h.Status == status);
    }

    public async Task<List<HsnCode>> SearchAsync(string searchTerm, int skip = 0, int take = 50)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.CodeDetails.Code.Contains(searchTerm) || 
                       h.CodeDetails.Description.Contains(searchTerm) ||
                       h.CodeDetails.Category.Contains(searchTerm) ||
                       h.Chapter.Contains(searchTerm) ||
                       h.Section.Contains(searchTerm))
            .OrderByDescending(h => h.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<List<HsnCodeHistory>> GetHistoryAsync(Guid hsnCodeId)
    {
        return await _context.Set<HsnCodeHistory>()
            .Where(h => h.HsnCodeId == hsnCodeId)
            .OrderByDescending(h => h.ModifiedAt)
            .ToListAsync();
    }

    public async Task<List<HsnCodeGstMapping>> GetGstMappingsAsync(Guid hsnCodeId)
    {
        return await _context.Set<HsnCodeGstMapping>()
            .Where(m => m.HsnCodeId == hsnCodeId)
            .OrderByDescending(m => m.EffectiveFrom)
            .ToListAsync();
    }

    public async Task<GstRate?> GetApplicableGstRateAsync(string code, DateTime date)
    {
        var hsnCode = await _context.HsnCodes
            .Include(h => h.GstMappings)
            .FirstOrDefaultAsync(h => h.CodeDetails.Code == code && h.Status == HsnCodeStatus.Active);

        return hsnCode?.GetApplicableGstRate(date);
    }

    public async Task<List<HsnCode>> BulkAddAsync(List<HsnCode> hsnCodes)
    {
        _context.HsnCodes.AddRange(hsnCodes);
        return hsnCodes;
    }

    public async Task BulkUpdateAsync(List<HsnCode> hsnCodes)
    {
        _context.HsnCodes.UpdateRange(hsnCodes);
        await Task.CompletedTask;
    }

    public async Task<List<HsnCode>> GetExpiringSoonAsync(DateTime beforeDate)
    {
        return await _context.HsnCodes
            .Include(h => h.History)
            .Include(h => h.GstMappings)
            .Where(h => h.Status == HsnCodeStatus.Active &&
                       h.EffectiveTo.HasValue &&
                       h.EffectiveTo.Value <= beforeDate)
            .OrderBy(h => h.EffectiveTo)
            .ToListAsync();
    }
}
