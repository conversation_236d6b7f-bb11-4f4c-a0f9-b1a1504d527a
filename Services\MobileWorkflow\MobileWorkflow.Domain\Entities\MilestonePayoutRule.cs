
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class MilestonePayoutRule : AggregateRoot
{
    public Guid MilestoneStepId { get; private set; }
    public decimal PayoutPercentage { get; private set; }
    public string? TriggerCondition { get; private set; }
    public bool IsActive { get; private set; }
    public string? Description { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual MilestoneStep MilestoneStep { get; private set; } = null!;

    private MilestonePayoutRule() { } // EF Core

    public MilestonePayoutRule(
        Guid milestoneStepId,
        decimal payoutPercentage,
        string? triggerCondition = null,
        string? description = null)
    {
        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        MilestoneStepId = milestoneStepId;
        PayoutPercentage = payoutPercentage;
        TriggerCondition = triggerCondition;
        Description = description;

        IsActive = true;
        Configuration = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new MilestonePayoutRuleCreatedEvent(Id, MilestoneStepId, PayoutPercentage));
    }

    public void UpdatePayoutPercentage(decimal payoutPercentage)
    {
        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        var oldPercentage = PayoutPercentage;
        PayoutPercentage = payoutPercentage;

        AddDomainEvent(new MilestonePayoutRulePayoutPercentageUpdatedEvent(Id, MilestoneStepId, oldPercentage, payoutPercentage));
    }

    public void UpdateTriggerCondition(string? triggerCondition)
    {
        TriggerCondition = triggerCondition;

        AddDomainEvent(new MilestonePayoutRuleTriggerConditionUpdatedEvent(Id, MilestoneStepId, TriggerCondition));
    }

    public void UpdateDescription(string? description)
    {
        Description = description;

        AddDomainEvent(new MilestonePayoutRuleDescriptionUpdatedEvent(Id, MilestoneStepId, Description));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new MilestonePayoutRuleActivatedEvent(Id, MilestoneStepId, PayoutPercentage));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new MilestonePayoutRuleDeactivatedEvent(Id, MilestoneStepId, PayoutPercentage));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        AddDomainEvent(new MilestonePayoutRuleConfigurationUpdatedEvent(Id, MilestoneStepId));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool EvaluateTriggerCondition(Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(TriggerCondition))
        {
            return true; // No condition means always triggered
        }

        // Simple condition evaluation - can be enhanced with a proper expression evaluator
        try
        {
            // For now, support simple key-value conditions like "status=completed"
            if (TriggerCondition.Contains("="))
            {
                var parts = TriggerCondition.Split('=', 2);
                if (parts.Length == 2)
                {
                    var key = parts[0].Trim();
                    var expectedValue = parts[1].Trim();

                    if (context.TryGetValue(key, out var actualValue))
                    {
                        return actualValue?.ToString()?.Equals(expectedValue, StringComparison.OrdinalIgnoreCase) == true;
                    }
                }
            }

            return false;
        }
        catch
        {
            return false; // If condition evaluation fails, don't trigger
        }
    }

    public List<string> GetValidationErrors()
    {
        var errors = new List<string>();

        if (PayoutPercentage < 0 || PayoutPercentage > 100)
        {
            errors.Add("Payout percentage must be between 0 and 100");
        }

        if (!string.IsNullOrEmpty(TriggerCondition))
        {
            // Validate trigger condition format
            if (!TriggerCondition.Contains("="))
            {
                errors.Add("Trigger condition must be in format 'key=value'");
            }
        }

        return errors;
    }

    public bool CanBeDeleted()
    {
        return true; // Payout rules can generally be deleted
    }
}



