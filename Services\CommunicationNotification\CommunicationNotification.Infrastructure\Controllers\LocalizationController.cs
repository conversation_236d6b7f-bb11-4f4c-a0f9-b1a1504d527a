using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Localization management API controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class LocalizationController : ControllerBase
{
    private readonly ILocalizationService _localizationService;
    private readonly ITemplateService _templateService;
    private readonly ITranslationService _translationService;

    public LocalizationController(
        ILocalizationService localizationService,
        ITemplateService templateService,
        ITranslationService translationService)
    {
        _localizationService = localizationService;
        _templateService = templateService;
        _translationService = translationService;
    }

    /// <summary>
    /// Get localized text by key and language
    /// </summary>
    [HttpGet("text/{key}")]
    public async Task<IActionResult> GetLocalizedText(
        string key,
        [FromQuery] Language language = Language.English,
        CancellationToken cancellationToken = default)
    {
        var text = await _localizationService.GetLocalizedTextAsync(key, language, cancellationToken: cancellationToken);
        
        return Ok(new
        {
            Key = key,
            Language = language.ToString(),
            Text = text,
            IsAvailable = !string.IsNullOrEmpty(text) && text != key
        });
    }

    /// <summary>
    /// Get localized text with parameters
    /// </summary>
    [HttpPost("text/{key}/render")]
    public async Task<IActionResult> GetLocalizedTextWithParameters(
        string key,
        [FromBody] LocalizationRequest request,
        CancellationToken cancellationToken = default)
    {
        var text = await _localizationService.GetLocalizedTextAsync(
            key, 
            request.Language, 
            request.Parameters, 
            cancellationToken);
        
        return Ok(new
        {
            Key = key,
            Language = request.Language.ToString(),
            Text = text,
            Parameters = request.Parameters
        });
    }

    /// <summary>
    /// Get all available templates
    /// </summary>
    [HttpGet("templates")]
    public async Task<IActionResult> GetTemplates(
        [FromQuery] string? category = null,
        [FromQuery] Language? language = null,
        CancellationToken cancellationToken = default)
    {
        var templates = await _templateService.GetAvailableTemplatesAsync(category, language, cancellationToken);
        
        return Ok(new
        {
            Templates = templates,
            Count = templates.Count,
            Categories = templates.Select(t => t.Category).Distinct().ToList(),
            Languages = templates.Select(t => t.Language).Distinct().ToList()
        });
    }

    /// <summary>
    /// Get specific template
    /// </summary>
    [HttpGet("templates/{templateKey}")]
    public async Task<IActionResult> GetTemplate(
        string templateKey,
        [FromQuery] Language language = Language.English,
        CancellationToken cancellationToken = default)
    {
        var template = await _localizationService.GetTemplateAsync(templateKey, language, cancellationToken);
        
        if (template == null)
        {
            return NotFound(new { Message = $"Template '{templateKey}' not found for language '{language}'" });
        }

        return Ok(template);
    }

    /// <summary>
    /// Render template with parameters
    /// </summary>
    [HttpPost("templates/{templateKey}/render")]
    public async Task<IActionResult> RenderTemplate(
        string templateKey,
        [FromBody] TemplateRenderRequest request,
        CancellationToken cancellationToken = default)
    {
        var rendered = await _localizationService.RenderTemplateAsync(
            templateKey, 
            request.Language, 
            request.Parameters, 
            cancellationToken);
        
        return Ok(new
        {
            TemplateKey = templateKey,
            Language = request.Language.ToString(),
            RenderedContent = rendered,
            Parameters = request.Parameters
        });
    }

    /// <summary>
    /// Preview template with parameters
    /// </summary>
    [HttpPost("templates/{templateKey}/preview")]
    public async Task<IActionResult> PreviewTemplate(
        string templateKey,
        [FromBody] TemplateRenderRequest request,
        CancellationToken cancellationToken = default)
    {
        var preview = await _templateService.PreviewTemplateAsync(
            templateKey, 
            request.Language, 
            request.Parameters, 
            cancellationToken);
        
        return Ok(preview);
    }

    /// <summary>
    /// Validate template with parameters
    /// </summary>
    [HttpPost("templates/{templateKey}/validate")]
    public async Task<IActionResult> ValidateTemplate(
        string templateKey,
        [FromBody] TemplateRenderRequest request,
        CancellationToken cancellationToken = default)
    {
        var isValid = await _templateService.ValidateTemplateAsync(
            templateKey, 
            request.Language, 
            request.Parameters, 
            cancellationToken);
        
        return Ok(new
        {
            TemplateKey = templateKey,
            Language = request.Language.ToString(),
            IsValid = isValid,
            Parameters = request.Parameters
        });
    }

    /// <summary>
    /// Translate text between languages
    /// </summary>
    [HttpPost("translate")]
    public async Task<IActionResult> TranslateText(
        [FromBody] TranslationRequest request,
        CancellationToken cancellationToken = default)
    {
        var translation = await _translationService.TranslateTextAsync(
            request.Text,
            request.SourceLanguage,
            request.TargetLanguage,
            cancellationToken);
        
        return Ok(new
        {
            OriginalText = request.Text,
            TranslatedText = translation,
            SourceLanguage = request.SourceLanguage.ToString(),
            TargetLanguage = request.TargetLanguage.ToString()
        });
    }

    /// <summary>
    /// Translate to multiple languages
    /// </summary>
    [HttpPost("translate/multiple")]
    public async Task<IActionResult> TranslateToMultipleLanguages(
        [FromBody] MultiLanguageTranslationRequest request,
        CancellationToken cancellationToken = default)
    {
        var translations = await _translationService.TranslateToMultipleLanguagesAsync(
            request.Text,
            request.SourceLanguage,
            request.TargetLanguages,
            cancellationToken);
        
        return Ok(new
        {
            OriginalText = request.Text,
            SourceLanguage = request.SourceLanguage.ToString(),
            Translations = translations.ToDictionary(
                kvp => kvp.Key.ToString(),
                kvp => kvp.Value
            )
        });
    }

    /// <summary>
    /// Get supported languages
    /// </summary>
    [HttpGet("languages")]
    public IActionResult GetSupportedLanguages()
    {
        var languages = _localizationService.GetSupportedLanguages();
        
        return Ok(new
        {
            SupportedLanguages = languages.Select(l => new
            {
                Code = l.ToString(),
                Name = l.ToString(),
                Culture = _localizationService.GetCultureInfo(l).Name,
                DisplayName = _localizationService.GetCultureInfo(l).DisplayName
            }).ToList(),
            Count = languages.Count
        });
    }

    /// <summary>
    /// Get localization statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetStatistics(CancellationToken cancellationToken = default)
    {
        // This would need to be implemented in the localization repository
        var stats = new
        {
            TotalKeys = 100, // Placeholder
            Languages = _localizationService.GetSupportedLanguages().Select(l => new
            {
                Language = l.ToString(),
                TranslatedKeys = 85, // Placeholder
                CompletionPercentage = 85.0 // Placeholder
            }).ToList(),
            LastUpdated = DateTime.UtcNow
        };
        
        return Ok(stats);
    }

    /// <summary>
    /// Check if translation is available
    /// </summary>
    [HttpGet("text/{key}/availability")]
    public async Task<IActionResult> CheckTranslationAvailability(
        string key,
        [FromQuery] Language language = Language.English,
        CancellationToken cancellationToken = default)
    {
        var isAvailable = await _localizationService.IsTranslationAvailableAsync(key, language, cancellationToken);
        
        return Ok(new
        {
            Key = key,
            Language = language.ToString(),
            IsAvailable = isAvailable
        });
    }

    /// <summary>
    /// Detect language of text
    /// </summary>
    [HttpPost("detect-language")]
    public async Task<IActionResult> DetectLanguage(
        [FromBody] LanguageDetectionRequest request,
        CancellationToken cancellationToken = default)
    {
        var detectedLanguage = await _translationService.DetectLanguageAsync(request.Text, cancellationToken);
        
        return Ok(new
        {
            Text = request.Text,
            DetectedLanguage = detectedLanguage,
            Confidence = "High" // Placeholder
        });
    }

    /// <summary>
    /// Clear localization cache
    /// </summary>
    [HttpPost("cache/clear")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> ClearCache(
        [FromQuery] string? key = null,
        [FromQuery] Language? language = null)
    {
        await _localizationService.InvalidateCacheAsync(key, language);
        
        return Ok(new
        {
            Message = "Cache cleared successfully",
            Key = key,
            Language = language?.ToString()
        });
    }
}

/// <summary>
/// Localization request model
/// </summary>
public class LocalizationRequest
{
    public Language Language { get; set; } = Language.English;
    public Dictionary<string, object>? Parameters { get; set; }
}

/// <summary>
/// Template render request model
/// </summary>
public class TemplateRenderRequest
{
    public Language Language { get; set; } = Language.English;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Translation request model
/// </summary>
public class TranslationRequest
{
    public string Text { get; set; } = string.Empty;
    public Language SourceLanguage { get; set; } = Language.English;
    public Language TargetLanguage { get; set; } = Language.Hindi;
}

/// <summary>
/// Multi-language translation request model
/// </summary>
public class MultiLanguageTranslationRequest
{
    public string Text { get; set; } = string.Empty;
    public Language SourceLanguage { get; set; } = Language.English;
    public List<Language> TargetLanguages { get; set; } = new();
}

/// <summary>
/// Language detection request model
/// </summary>
public class LanguageDetectionRequest
{
    public string Text { get; set; } = string.Empty;
}
