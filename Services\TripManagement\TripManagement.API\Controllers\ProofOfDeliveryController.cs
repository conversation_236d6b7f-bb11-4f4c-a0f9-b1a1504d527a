using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.CollectDigitalSignature;
using TripManagement.Application.Commands.UploadDeliveryPhoto;
using TripManagement.Application.Commands.VerifyRecipient;
using TripManagement.Application.Queries.GetProofOfDelivery;
using TripManagement.Application.DTOs;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProofOfDeliveryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ProofOfDeliveryController> _logger;

    public ProofOfDeliveryController(IMediator mediator, ILogger<ProofOfDeliveryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Collect digital signature for delivery
    /// </summary>
    [HttpPost("signature")]
    [Authorize(Roles = "Driver")]
    public async Task<ActionResult> CollectDigitalSignature([FromBody] CollectDigitalSignatureRequest request)
    {
        try
        {
            var command = new CollectDigitalSignatureCommand
            {
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                RecipientName = request.RecipientName,
                SignatureData = request.SignatureData,
                Notes = request.Notes,
                DeliveredAt = request.DeliveredAt
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Digital signature collected successfully" });

            return BadRequest("Failed to collect digital signature");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while collecting signature for trip stop {TripStopId}",
                request.TripStopId);
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access while collecting signature for trip stop {TripStopId}",
                request.TripStopId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting digital signature for trip stop {TripStopId}",
                request.TripStopId);
            return StatusCode(500, "An error occurred while collecting the digital signature");
        }
    }

    /// <summary>
    /// Upload delivery photo
    /// </summary>
    [HttpPost("photo")]
    [Authorize(Roles = "Driver")]
    public async Task<ActionResult<string>> UploadDeliveryPhoto([FromForm] UploadDeliveryPhotoRequest request)
    {
        try
        {
            var command = new UploadDeliveryPhotoCommand
            {
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                Photo = request.Photo,
                Description = request.Description,
                TakenAt = request.TakenAt
            };

            var photoUrl = await _mediator.Send(command);

            return Ok(new { PhotoUrl = photoUrl, Message = "Delivery photo uploaded successfully" });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while uploading photo for trip stop {TripStopId}",
                request.TripStopId);
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access while uploading photo for trip stop {TripStopId}",
                request.TripStopId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading delivery photo for trip stop {TripStopId}",
                request.TripStopId);
            return StatusCode(500, "An error occurred while uploading the delivery photo");
        }
    }

    /// <summary>
    /// Verify recipient identity
    /// </summary>
    [HttpPost("verify-recipient")]
    [Authorize(Roles = "Driver")]
    public async Task<ActionResult> VerifyRecipient([FromBody] VerifyRecipientRequest request)
    {
        try
        {
            var command = new VerifyRecipientCommand
            {
                TripStopId = request.TripStopId,
                DriverId = request.DriverId,
                RecipientName = request.RecipientName,
                RecipientPhone = request.RecipientPhone,
                RecipientEmail = request.RecipientEmail,
                IdentificationNumber = request.IdentificationNumber,
                IdentificationType = request.IdentificationType,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Recipient verified successfully" });

            return BadRequest("Failed to verify recipient");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while verifying recipient for trip stop {TripStopId}",
                request.TripStopId);
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access while verifying recipient for trip stop {TripStopId}",
                request.TripStopId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying recipient for trip stop {TripStopId}",
                request.TripStopId);
            return StatusCode(500, "An error occurred while verifying the recipient");
        }
    }

    /// <summary>
    /// Get proof of delivery for a trip stop
    /// </summary>
    [HttpGet("trip-stop/{tripStopId:guid}")]
    [Authorize(Roles = "Admin,Carrier,Driver,Broker")]
    public async Task<ActionResult<ProofOfDeliveryDto>> GetProofOfDelivery(Guid tripStopId)
    {
        try
        {
            var query = new GetProofOfDeliveryQuery { TripStopId = tripStopId };
            var result = await _mediator.Send(query);

            if (result == null)
                return NotFound("Proof of delivery not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting proof of delivery for trip stop {TripStopId}", tripStopId);
            return StatusCode(500, "An error occurred while retrieving the proof of delivery");
        }
    }

    /// <summary>
    /// Get all proof of delivery documents for a trip
    /// </summary>
    [HttpGet("trip/{tripId:guid}")]
    [Authorize(Roles = "Admin,Carrier,Driver,Broker")]
    public async Task<ActionResult<List<ProofOfDeliveryDto>>> GetProofOfDeliveryByTrip(Guid tripId)
    {
        try
        {
            var query = new GetProofOfDeliveryByTripQuery { TripId = tripId };
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting proof of delivery documents for trip {TripId}", tripId);
            return StatusCode(500, "An error occurred while retrieving the proof of delivery documents");
        }
    }
}

// Request DTOs
public record CollectDigitalSignatureRequest
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public string SignatureData { get; init; } = string.Empty;
    public string? Notes { get; init; }
    public DateTime? DeliveredAt { get; init; }
}

public record UploadDeliveryPhotoRequest
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public IFormFile Photo { get; init; } = null!;
    public string? Description { get; init; }
    public DateTime? TakenAt { get; init; }
}

public record VerifyRecipientRequest
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public string? RecipientPhone { get; init; }
    public string? RecipientEmail { get; init; }
    public string? IdentificationNumber { get; init; }
    public string? IdentificationType { get; init; }
    public string? Notes { get; init; }
}
