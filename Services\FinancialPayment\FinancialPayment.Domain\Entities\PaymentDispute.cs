using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;

namespace FinancialPayment.Domain.Entities;

public class PaymentDispute : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public Guid? EscrowAccountId { get; private set; }
    public Guid? SettlementId { get; private set; }
    public Guid InitiatedBy { get; private set; }
    public ParticipantRole InitiatorRole { get; private set; }
    public string DisputeNumber { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public Money DisputedAmount { get; private set; }
    public DisputeCategory Category { get; private set; }
    public DisputeStatus Status { get; private set; }
    public DisputePriority Priority { get; private set; }

    public DateTime? ResolvedAt { get; private set; }
    public string? Resolution { get; private set; }
    public Money? ResolvedAmount { get; private set; }
    public Guid? ResolvedBy { get; private set; }

    // Navigation properties
    private readonly List<DisputeComment> _comments = new();
    public IReadOnlyList<DisputeComment> Comments => _comments.AsReadOnly();

    private readonly List<DisputeDocument> _documents = new();
    public IReadOnlyList<DisputeDocument> Documents => _documents.AsReadOnly();

    public EscrowAccount? EscrowAccount { get; private set; }
    public Settlement? Settlement { get; private set; }

    private PaymentDispute() { }

    public PaymentDispute(
        Guid orderId,
        Guid initiatedBy,
        ParticipantRole initiatorRole,
        string title,
        string description,
        Money disputedAmount,
        DisputeCategory category,
        DisputePriority priority = DisputePriority.Medium,
        Guid? escrowAccountId = null,
        Guid? settlementId = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty");

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty");

        if (disputedAmount == null || disputedAmount.Amount <= 0)
            throw new ArgumentException("Disputed amount must be greater than zero");

        Id = Guid.NewGuid();
        OrderId = orderId;
        EscrowAccountId = escrowAccountId;
        SettlementId = settlementId;
        InitiatedBy = initiatedBy;
        InitiatorRole = initiatorRole;
        DisputeNumber = GenerateDisputeNumber();
        Title = title;
        Description = description;
        DisputedAmount = disputedAmount;
        Category = category;
        Status = DisputeStatus.Open;
        Priority = priority;
        CreatedAt = DateTime.UtcNow;

        AddDomainEvent(new PaymentDisputeCreatedEvent(Id, OrderId, InitiatedBy, DisputedAmount, Category));
    }

    public void AddComment(Guid authorId, ParticipantRole authorRole, string content, bool isInternal = false)
    {
        if (Status == DisputeStatus.Resolved || Status == DisputeStatus.Closed)
            throw new InvalidOperationException("Cannot add comments to resolved or closed disputes");

        var comment = new DisputeComment(Id, authorId, authorRole, content, isInternal);
        _comments.Add(comment);
        AddDomainEvent(new DisputeCommentAddedEvent(comment.Id, Id, authorId, content));
    }

    public void AddDocument(string fileName, string fileUrl, string description, Guid uploadedBy)
    {
        if (Status == DisputeStatus.Resolved || Status == DisputeStatus.Closed)
            throw new InvalidOperationException("Cannot add documents to resolved or closed disputes");

        var document = new DisputeDocument(Id, fileName, fileUrl, description, uploadedBy);
        _documents.Add(document);
        AddDomainEvent(new DisputeDocumentAddedEvent(document.Id, Id, fileName));
    }

    public void Escalate(DisputePriority newPriority, string reason)
    {
        if (Status != DisputeStatus.Open && Status != DisputeStatus.InProgress)
            throw new InvalidOperationException("Only open or in-progress disputes can be escalated");

        if (newPriority <= Priority)
            throw new ArgumentException("New priority must be higher than current priority");

        Priority = newPriority;
        Status = DisputeStatus.Escalated;
        AddComment(Guid.Empty, ParticipantRole.Admin, $"Dispute escalated to {newPriority}: {reason}", true);
        AddDomainEvent(new PaymentDisputeEscalatedEvent(Id, OrderId, newPriority, reason));
    }

    public void StartInvestigation(Guid investigatorId)
    {
        if (Status != DisputeStatus.Open && Status != DisputeStatus.Escalated)
            throw new InvalidOperationException("Only open or escalated disputes can start investigation");

        Status = DisputeStatus.InProgress;
        AddComment(investigatorId, ParticipantRole.Admin, "Investigation started", true);
        AddDomainEvent(new PaymentDisputeInvestigationStartedEvent(Id, OrderId, investigatorId));
    }

    public void Resolve(Money resolvedAmount, string resolution, Guid resolvedBy)
    {
        if (Status != DisputeStatus.InProgress)
            throw new InvalidOperationException("Only in-progress disputes can be resolved");

        if (resolvedAmount.Currency != DisputedAmount.Currency)
            throw new ArgumentException("Resolved amount currency must match disputed amount currency");

        Status = DisputeStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        Resolution = resolution;
        ResolvedAmount = resolvedAmount;
        ResolvedBy = resolvedBy;

        AddComment(resolvedBy, ParticipantRole.Admin, $"Dispute resolved: {resolution}", false);
        AddDomainEvent(new PaymentDisputeResolvedEvent(Id, OrderId, resolvedAmount, resolution));
    }

    public void Close(string reason, Guid closedBy)
    {
        if (Status == DisputeStatus.Closed)
            throw new InvalidOperationException("Dispute is already closed");

        Status = DisputeStatus.Closed;
        ResolvedAt = DateTime.UtcNow;
        Resolution = reason;
        ResolvedBy = closedBy;

        AddComment(closedBy, ParticipantRole.Admin, $"Dispute closed: {reason}", true);
        AddDomainEvent(new PaymentDisputeClosedEvent(Id, OrderId, reason));
    }

    public void Reopen(string reason, Guid reopenedBy)
    {
        if (Status != DisputeStatus.Resolved && Status != DisputeStatus.Closed)
            throw new InvalidOperationException("Only resolved or closed disputes can be reopened");

        Status = DisputeStatus.Open;
        ResolvedAt = null;
        Resolution = null;
        ResolvedAmount = null;
        ResolvedBy = null;

        AddComment(reopenedBy, ParticipantRole.Admin, $"Dispute reopened: {reason}", false);
        AddDomainEvent(new PaymentDisputeReopenedEvent(Id, OrderId, reason));
    }

    private static string GenerateDisputeNumber()
    {
        return $"DSP{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }
}
