using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for Threshold
/// </summary>
public class ThresholdConfiguration : IEntityTypeConfiguration<Threshold>
{
    public void Configure(EntityTypeBuilder<Threshold> builder)
    {
        builder.ToTable("Thresholds");

        // Primary key
        builder.HasKey(t => t.Id);

        // Properties
        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(t => t.MetricType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.WarningThreshold)
            .HasPrecision(18, 4);

        builder.Property(t => t.CriticalThreshold)
            .HasPrecision(18, 4);

        builder.Property(t => t.ComparisonOperator)
            .HasConversion<int>();

        builder.Property(t => t.IsEnabled)
            .IsRequired();

        builder.Property(t => t.EntityType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.UserRole)
            .HasConversion<int>();

        // Complex properties
        builder.Property(t => t.NotificationRecipients)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("jsonb");

        builder.Property(t => t.Configuration)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(t => t.LastEvaluatedAt)
            .IsRequired(false);

        builder.Property(t => t.LastTriggeredAt)
            .IsRequired(false);

        builder.Property(t => t.TriggerCount)
            .HasDefaultValue(0);

        // Base entity properties
        builder.Property(t => t.CreatedAt)
            .IsRequired();

        builder.Property(t => t.UpdatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(t => t.MetricType)
            .HasDatabaseName("IX_Thresholds_MetricType");

        builder.HasIndex(t => t.EntityType)
            .HasDatabaseName("IX_Thresholds_EntityType");

        builder.HasIndex(t => t.UserRole)
            .HasDatabaseName("IX_Thresholds_UserRole");

        builder.HasIndex(t => t.IsEnabled)
            .HasDatabaseName("IX_Thresholds_IsEnabled");

        builder.HasIndex(t => new { t.MetricType, t.EntityType, t.IsEnabled })
            .HasDatabaseName("IX_Thresholds_Monitoring");

        builder.HasIndex(t => t.LastEvaluatedAt)
            .HasDatabaseName("IX_Thresholds_LastEvaluatedAt");
    }
}
