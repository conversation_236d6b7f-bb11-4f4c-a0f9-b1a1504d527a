using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Infrastructure.Services;
using System.Diagnostics;

namespace SubscriptionManagement.Infrastructure.HealthChecks
{
    public class SubscriptionHealthCheck : IHealthCheck
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly ISubscriptionCacheService _cacheService;
        private readonly ISubscriptionMetricsService _metricsService;
        private readonly ILogger<SubscriptionHealthCheck> _logger;

        public SubscriptionHealthCheck(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            ISubscriptionCacheService cacheService,
            ISubscriptionMetricsService metricsService,
            ILogger<SubscriptionHealthCheck> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _cacheService = cacheService;
            _metricsService = metricsService;
            _logger = logger;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var healthData = new Dictionary<string, object>();

            try
            {
                // Check database connectivity
                var dbHealthy = await CheckDatabaseHealth(healthData, cancellationToken);
                
                // Check cache connectivity
                var cacheHealthy = await CheckCacheHealth(healthData, cancellationToken);
                
                // Check business logic health
                var businessHealthy = await CheckBusinessHealth(healthData, cancellationToken);

                stopwatch.Stop();
                _metricsService.RecordHealthCheck("subscription_service", dbHealthy && cacheHealthy && businessHealthy, stopwatch.Elapsed);

                var overallHealthy = dbHealthy && cacheHealthy && businessHealthy;
                
                return overallHealthy 
                    ? HealthCheckResult.Healthy("Subscription service is healthy", healthData)
                    : HealthCheckResult.Degraded("Subscription service has issues", null, healthData);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _metricsService.RecordHealthCheck("subscription_service", false, stopwatch.Elapsed);
                
                _logger.LogError(ex, "Health check failed");
                return HealthCheckResult.Unhealthy("Subscription service is unhealthy", ex, healthData);
            }
        }

        private async Task<bool> CheckDatabaseHealth(Dictionary<string, object> healthData, CancellationToken cancellationToken)
        {
            try
            {
                var dbStopwatch = Stopwatch.StartNew();
                
                // Test subscription repository
                var subscriptionCount = await _subscriptionRepository.GetSubscriptionsCountAsync();
                
                // Test plan repository
                var planCount = await _planRepository.GetPlansCountAsync();
                
                dbStopwatch.Stop();
                
                healthData["database"] = new
                {
                    status = "healthy",
                    subscriptionCount,
                    planCount,
                    responseTimeMs = dbStopwatch.ElapsedMilliseconds
                };

                _metricsService.RecordHealthCheck("database", true, dbStopwatch.Elapsed);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database health check failed");
                healthData["database"] = new
                {
                    status = "unhealthy",
                    error = ex.Message
                };
                
                _metricsService.RecordHealthCheck("database", false, TimeSpan.Zero);
                return false;
            }
        }

        private async Task<bool> CheckCacheHealth(Dictionary<string, object> healthData, CancellationToken cancellationToken)
        {
            try
            {
                var cacheStopwatch = Stopwatch.StartNew();
                
                // Test cache by setting and getting a test value
                var testKey = $"health_check_{Guid.NewGuid()}";
                var testPlan = new SubscriptionManagement.Domain.Entities.Plan(
                    "Health Check Plan",
                    "Test plan for health check",
                    SubscriptionManagement.Domain.ValueObjects.Money.Create(0, "USD"),
                    SubscriptionManagement.Domain.Enums.BillingCycle.Monthly,
                    SubscriptionManagement.Domain.Enums.PlanType.Basic,
                    SubscriptionManagement.Domain.Enums.UserType.Individual
                );

                await _cacheService.SetPlanAsync(testPlan, TimeSpan.FromMinutes(1));
                var retrievedPlan = await _cacheService.GetPlanAsync(testPlan.Id);
                await _cacheService.RemovePlanAsync(testPlan.Id);
                
                cacheStopwatch.Stop();
                
                var cacheWorking = retrievedPlan != null;
                
                healthData["cache"] = new
                {
                    status = cacheWorking ? "healthy" : "degraded",
                    responseTimeMs = cacheStopwatch.ElapsedMilliseconds,
                    testSuccessful = cacheWorking
                };

                _metricsService.RecordHealthCheck("cache", cacheWorking, cacheStopwatch.Elapsed);
                return cacheWorking;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache health check failed");
                healthData["cache"] = new
                {
                    status = "unhealthy",
                    error = ex.Message
                };
                
                _metricsService.RecordHealthCheck("cache", false, TimeSpan.Zero);
                return false;
            }
        }

        private async Task<bool> CheckBusinessHealth(Dictionary<string, object> healthData, CancellationToken cancellationToken)
        {
            try
            {
                var businessStopwatch = Stopwatch.StartNew();
                
                // Check for any critical business logic issues
                var activeSubscriptionsCount = await _subscriptionRepository.GetActiveSubscriptionsCountAsync();
                var totalRevenue = await _subscriptionRepository.GetTotalRevenueAsync();
                var mrr = await _subscriptionRepository.GetMonthlyRecurringRevenueAsync();
                
                businessStopwatch.Stop();
                
                healthData["business"] = new
                {
                    status = "healthy",
                    activeSubscriptions = activeSubscriptionsCount,
                    totalRevenue,
                    monthlyRecurringRevenue = mrr,
                    responseTimeMs = businessStopwatch.ElapsedMilliseconds
                };

                _metricsService.RecordHealthCheck("business_logic", true, businessStopwatch.Elapsed);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Business health check failed");
                healthData["business"] = new
                {
                    status = "unhealthy",
                    error = ex.Message
                };
                
                _metricsService.RecordHealthCheck("business_logic", false, TimeSpan.Zero);
                return false;
            }
        }
    }
}
