using MediatR;
using Microsoft.AspNetCore.Http;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Commands.UploadPaymentProof
{
    public class UploadPaymentProofCommand : IRequest<UploadPaymentProofResponse>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "INR";
        public DateTime PaymentDate { get; set; }
        public IFormFile ProofImage { get; set; } = null!;
        public string? Notes { get; set; }
        public string? PaymentMethod { get; set; }
        public string? TransactionReference { get; set; }
    }
}
