using Microsoft.ML;
using Shared.Domain.Common;
using Microsoft.ML.Data;
using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;
using AuditCompliance.Application.Interfaces;
using Shared.Domain.Common;
using AuditCompliance.Application.DTOs.Analytics;
using Shared.Domain.Common;
using AuditCompliance.Domain.Interfaces;
using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using Shared.Domain.Common;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Advanced analytics service using ML.NET for compliance prediction and analysis
/// </summary>
public class AdvancedAnalyticsService : IAdvancedAnalyticsService
{
    private readonly MLContext _mlContext;
using Shared.Domain.Common;
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<AdvancedAnalyticsService> _logger;
    private readonly Dictionary<string, ITransformer> _trainedModels;

    public AdvancedAnalyticsService(
        IAuditLogRepository auditLogRepository,
        IComplianceReportRepository complianceReportRepository,
        ILogger<AdvancedAnalyticsService> logger)
    {
        _mlContext = new MLContext(seed: 0);
        _auditLogRepository = auditLogRepository;
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
        _trainedModels = new Dictionary<string, ITransformer>();
    }

    public async Task<ComplianceRiskPredictionDto> PredictComplianceRiskAsync(
        ComplianceRiskInputDto input,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Predicting compliance risk for entity {EntityId}", input.EntityId);

            // Get historical data for the entity
            var historicalData = await GetHistoricalComplianceDataAsync(
                input.EntityId, input.EntityType, cancellationToken);

            // Get or train the risk prediction model
            var model = await GetOrTrainRiskPredictionModelAsync(historicalData, cancellationToken);

            // Prepare input data
            var inputData = PrepareRiskPredictionInput(input);
            var inputDataView = _mlContext.Data.LoadFromEnumerable(new[] { inputData });

            // Make prediction
            var predictions = model.Transform(inputDataView);
            var predictionResult = _mlContext.Data.CreateEnumerable<RiskPredictionOutput>(predictions, false).First();

            // Calculate risk factors
            var riskFactors = CalculateRiskFactors(input, predictionResult);

            // Generate recommendations
            var recommendations = GenerateRiskRecommendations(predictionResult.RiskScore, riskFactors);

            return new ComplianceRiskPredictionDto
            {
                EntityId = input.EntityId,
                EntityType = input.EntityType,
                RiskScore = predictionResult.RiskScore,
                RiskLevel = GetRiskLevel(predictionResult.RiskScore),
                Confidence = predictionResult.Confidence,
                RiskFactors = riskFactors,
                Recommendations = recommendations,
                PredictionDate = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting compliance risk for entity {EntityId}", input.EntityId);
            throw;
        }
    }

    public async Task<List<ViolationPredictionDto>> PredictViolationsAsync(
        Guid entityId,
        string entityType,
        DateTime predictionPeriod,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Predicting violations for entity {EntityId} for period {Period}",
                entityId, predictionPeriod);

            var violations = new List<ViolationPredictionDto>();

            // Get historical violation patterns
            var historicalViolations = await GetHistoricalViolationDataAsync(
                entityId, entityType, cancellationToken);

            // Predict for each compliance standard
            foreach (var standard in Enum.GetValues<ComplianceStandard>())
            {
                var violationProbability = await PredictViolationProbabilityAsync(
                    entityId, entityType, standard, predictionPeriod, historicalViolations);

                if (violationProbability > 0.3f) // Only include significant probabilities
                {
                    violations.Add(new ViolationPredictionDto
                    {
                        EntityId = entityId,
                        EntityType = entityType,
                        Standard = standard,
                        ViolationType = GetViolationTypeForStandard(standard),
                        Probability = violationProbability,
                        PredictedDate = predictionPeriod,
                        Severity = GetViolationSeverity(violationProbability),
                        PreventionActions = GetPreventionActions(standard, violationProbability)
                    });
                }
            }

            return violations.OrderByDescending(v => v.Probability).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting violations for entity {EntityId}", entityId);
            throw;
        }
    }

    public async Task<ComplianceTrendAnalysisDto> AnalyzeComplianceTrendsAsync(
        ComplianceTrendInputDto input,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing compliance trends from {FromDate} to {ToDate}",
                input.FromDate, input.ToDate);

            // Get compliance data for the period
            var complianceData = await GetComplianceDataForPeriodAsync(input, cancellationToken);

            // Group data by specified period
            var groupedData = GroupComplianceDataByPeriod(complianceData, input.GroupBy ?? "Day");

            // Calculate trend data points
            var trendData = groupedData.Select(g => new ComplianceTrendDataPointDto
            {
                Date = g.Key,
                ComplianceScore = CalculateComplianceScore(g.Value),
                ViolationCount = g.Value.Count(d => d.HasViolation),
                AuditCount = g.Value.Count,
                StandardScores = CalculateStandardScores(g.Value)
            }).OrderBy(t => t.Date).ToList();

            // Calculate summary
            var summary = new ComplianceTrendSummaryDto
            {
                AverageComplianceScore = trendData.Average(t => t.ComplianceScore),
                TrendDirection = CalculateTrendDirection(trendData),
                TotalViolations = trendData.Sum(t => t.ViolationCount),
                TotalAudits = trendData.Sum(t => t.AuditCount),
                OverallTrend = GetOverallTrend(trendData)
            };

            // Generate insights
            var insights = GenerateTrendInsights(trendData, summary);

            return new ComplianceTrendAnalysisDto
            {
                FromDate = input.FromDate,
                ToDate = input.ToDate,
                TrendData = trendData,
                Summary = summary,
                Insights = insights
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing compliance trends");
            throw;
        }
    }

    public async Task<List<ComplianceAnomalyDto>> DetectComplianceAnomaliesAsync(
        DateTime fromDate,
        DateTime toDate,
        string? entityType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Detecting compliance anomalies from {FromDate} to {ToDate}",
                fromDate, toDate);

            var anomalies = new List<ComplianceAnomalyDto>();

            // Get compliance data for anomaly detection
            var complianceData = await GetComplianceDataForAnomalyDetectionAsync(
                fromDate, toDate, entityType, cancellationToken);

            // Apply statistical anomaly detection
            var statisticalAnomalies = DetectStatisticalAnomalies(complianceData);
            anomalies.AddRange(statisticalAnomalies);

            // Apply pattern-based anomaly detection
            var patternAnomalies = DetectPatternAnomalies(complianceData);
            anomalies.AddRange(patternAnomalies);

            // Apply ML-based anomaly detection if model is available
            if (_trainedModels.ContainsKey("AnomalyDetection"))
            {
                var mlAnomalies = await DetectMLAnomaliesAsync(complianceData, cancellationToken);
                anomalies.AddRange(mlAnomalies);
            }

            return anomalies.OrderByDescending(a => a.AnomalyScore).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting compliance anomalies");
            throw;
        }
    }

    public async Task<ComplianceInsightsDto> GenerateComplianceInsightsAsync(
        Guid? organizationId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating compliance insights for organization {OrganizationId}",
                organizationId);

            // Get comprehensive compliance data
            var complianceData = await GetComprehensiveComplianceDataAsync(organizationId, cancellationToken);

            // Generate overview
            var overview = GenerateComplianceOverview(complianceData);

            // Generate insights
            var insights = GenerateComplianceInsights(complianceData);

            // Generate recommendations
            var recommendations = GenerateComplianceRecommendations(complianceData, insights);

            // Calculate key metrics
            var keyMetrics = CalculateKeyComplianceMetrics(complianceData);

            return new ComplianceInsightsDto
            {
                GeneratedAt = DateTime.UtcNow,
                OrganizationId = organizationId,
                Overview = overview,
                Insights = insights,
                Recommendations = recommendations,
                KeyMetrics = keyMetrics
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating compliance insights");
            throw;
        }
    }

    public async Task<ModelTrainingResultDto> TrainModelsAsync(
        List<string>? modelTypes = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Training ML models: {ModelTypes}",
                string.Join(", ", modelTypes ?? new List<string> { "All" }));

            var results = new List<ModelTrainingResultDto>();

            // Default to all model types if none specified
            var modelsToTrain = modelTypes ?? new List<string>
            {
                "RiskPrediction",
                "ViolationPrediction",
                "AnomalyDetection",
                "TrendAnalysis"
            };

            foreach (var modelType in modelsToTrain)
            {
                var result = await TrainSpecificModelAsync(modelType, cancellationToken);
                results.Add(result);
            }

            // Return combined result
            return new ModelTrainingResultDto
            {
                ModelType = "Combined",
                Success = results.All(r => r.Success),
                ErrorMessage = results.Where(r => !r.Success).FirstOrDefault()?.ErrorMessage,
                TrainingStarted = results.Min(r => r.TrainingStarted),
                TrainingCompleted = results.Max(r => r.TrainingCompleted),
                TrainingDataSize = results.Sum(r => r.TrainingDataSize),
                Metrics = results.SelectMany(r => r.Metrics).ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training models");
            throw;
        }
    }

    public async Task<List<ModelPerformanceDto>> GetModelPerformanceAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var performance = new List<ModelPerformanceDto>();

            foreach (var model in _trainedModels)
            {
                var metrics = await EvaluateModelPerformanceAsync(model.Key, model.Value, cancellationToken);
                performance.Add(metrics);
            }

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting model performance");
            throw;
        }
    }

    public async Task<List<ComplianceScoreForecastDto>> ForecastComplianceScoreAsync(
        Guid entityId,
        string entityType,
        int forecastDays = 30,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Forecasting compliance score for entity {EntityId} for {Days} days",
                entityId, forecastDays);

            // Get historical compliance scores
            var historicalScores = await GetHistoricalComplianceScoresAsync(
                entityId, entityType, cancellationToken);

            // Get or train forecasting model
            var model = await GetOrTrainForecastingModelAsync(historicalScores, cancellationToken);

            // Generate forecasts
            var forecasts = new List<ComplianceScoreForecastDto>();
            var baseDate = DateTime.UtcNow.Date;

            for (int i = 1; i <= forecastDays; i++)
            {
                var forecastDate = baseDate.AddDays(i);
                var forecast = await GenerateForecastForDateAsync(
                    model, entityId, entityType, forecastDate, historicalScores);

                forecasts.Add(forecast);
            }

            return forecasts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting compliance score for entity {EntityId}", entityId);
            throw;
        }
    }

    // Private helper methods
    private async Task<List<ComplianceDataPoint>> GetHistoricalComplianceDataAsync(
        Guid entityId, string entityType, CancellationToken cancellationToken)
    {
        var auditLogs = await _auditLogRepository.GetAuditTrailAsync(
            entityId: entityId,
            entityType: entityType,
            fromDate: DateTime.UtcNow.AddMonths(-12),
            toDate: DateTime.UtcNow,
            cancellationToken: cancellationToken);

        var complianceReports = await _complianceReportRepository.GetReportsByEntityAsync(
            entityId, entityType, cancellationToken);

        var dataPoints = new List<ComplianceDataPoint>();

        // Convert audit logs to data points
        foreach (var log in auditLogs)
        {
            dataPoints.Add(new ComplianceDataPoint
            {
                Date = log.Timestamp,
                EntityId = entityId,
                EntityType = entityType,
                ComplianceScore = CalculateComplianceScoreFromAuditLog(log),
                HasViolation = log.Severity == AuditSeverity.Critical || log.Severity == AuditSeverity.High,
                Standard = GetStandardFromAuditLog(log),
                Features = ExtractFeaturesFromAuditLog(log)
            });
        }

        return dataPoints.OrderBy(d => d.Date).ToList();
    }

    private async Task<ITransformer> GetOrTrainRiskPredictionModelAsync(
        List<ComplianceDataPoint> historicalData, CancellationToken cancellationToken)
    {
        const string modelKey = "RiskPrediction";

        if (_trainedModels.ContainsKey(modelKey))
            return _trainedModels[modelKey];

        // Prepare training data
        var trainingData = historicalData.Select(d => new RiskPredictionInput
        {
            ViolationHistory = d.HasViolation ? 1.0f : 0.0f,
            AuditFrequency = CalculateAuditFrequency(d.EntityId, historicalData),
            ComplianceScore = d.ComplianceScore,
            EntityAge = CalculateEntityAge(d.EntityId),
            IndustryRisk = GetIndustryRiskScore(d.EntityType)
        }).ToList();

        var dataView = _mlContext.Data.LoadFromEnumerable(trainingData);

        // Create training pipeline
        var pipeline = _mlContext.Transforms.Concatenate("Features",
                nameof(RiskPredictionInput.ViolationHistory),
                nameof(RiskPredictionInput.AuditFrequency),
                nameof(RiskPredictionInput.ComplianceScore),
                nameof(RiskPredictionInput.EntityAge),
                nameof(RiskPredictionInput.IndustryRisk))
            .Append(_mlContext.Regression.Trainers.Sdca(labelColumnName: "Score", maximumNumberOfIterations: 100));

        var model = pipeline.Fit(dataView);
        _trainedModels[modelKey] = model;

        return model;
    }

    private RiskPredictionInput PrepareRiskPredictionInput(ComplianceRiskInputDto input)
    {
        return new RiskPredictionInput
        {
            ViolationHistory = GetViolationHistoryScore(input.EntityId),
            AuditFrequency = GetAuditFrequencyScore(input.EntityId),
            ComplianceScore = GetCurrentComplianceScore(input.EntityId),
            EntityAge = CalculateEntityAge(input.EntityId),
            IndustryRisk = GetIndustryRiskScore(input.EntityType)
        };
    }

    private List<RiskFactorDto> CalculateRiskFactors(
        ComplianceRiskInputDto input, RiskPredictionOutput prediction)
    {
        var factors = new List<RiskFactorDto>();

        // Analyze each input feature's contribution to risk
        var violationHistory = GetViolationHistoryScore(input.EntityId);
        if (violationHistory > 0.5f)
        {
            factors.Add(new RiskFactorDto
            {
                Name = "Violation History",
                Impact = violationHistory * 0.3f,
                Description = "Entity has a history of compliance violations",
                Category = "Historical"
            });
        }

        var auditFrequency = GetAuditFrequencyScore(input.EntityId);
        if (auditFrequency < 0.3f)
        {
            factors.Add(new RiskFactorDto
            {
                Name = "Low Audit Frequency",
                Impact = (1 - auditFrequency) * 0.2f,
                Description = "Entity is not audited frequently enough",
                Category = "Process"
            });
        }

        var complianceScore = GetCurrentComplianceScore(input.EntityId);
        if (complianceScore < 0.7f)
        {
            factors.Add(new RiskFactorDto
            {
                Name = "Low Compliance Score",
                Impact = (1 - complianceScore) * 0.4f,
                Description = "Current compliance score is below acceptable threshold",
                Category = "Current"
            });
        }

        var industryRisk = GetIndustryRiskScore(input.EntityType);
        if (industryRisk > 0.6f)
        {
            factors.Add(new RiskFactorDto
            {
                Name = "High Industry Risk",
                Impact = industryRisk * 0.1f,
                Description = "Entity operates in a high-risk industry",
                Category = "Industry"
            });
        }

        return factors.OrderByDescending(f => f.Impact).ToList();
    }

    private List<string> GenerateRiskRecommendations(float riskScore, List<RiskFactorDto> riskFactors)
    {
        var recommendations = new List<string>();

        if (riskScore >= 0.8f)
        {
            recommendations.Add("Immediate compliance review required");
            recommendations.Add("Implement enhanced monitoring procedures");
            recommendations.Add("Consider temporary operational restrictions");
        }
        else if (riskScore >= 0.6f)
        {
            recommendations.Add("Schedule comprehensive compliance audit");
            recommendations.Add("Review and update compliance procedures");
            recommendations.Add("Increase monitoring frequency");
        }
        else if (riskScore >= 0.4f)
        {
            recommendations.Add("Conduct targeted compliance assessment");
            recommendations.Add("Provide additional compliance training");
            recommendations.Add("Review compliance documentation");
        }

        // Add specific recommendations based on risk factors
        foreach (var factor in riskFactors.Where(f => f.Impact > 0.2f))
        {
            switch (factor.Category)
            {
                case "Historical":
                    recommendations.Add("Implement corrective action plan for past violations");
                    break;
                case "Process":
                    recommendations.Add("Increase audit frequency and scope");
                    break;
                case "Current":
                    recommendations.Add("Address current compliance gaps immediately");
                    break;
                case "Industry":
                    recommendations.Add("Implement industry-specific compliance measures");
                    break;
            }
        }

        return recommendations.Distinct().ToList();
    }

    private string GetRiskLevel(float riskScore)
    {
        return riskScore switch
        {
            >= 0.8f => "Critical",
            >= 0.6f => "High",
            >= 0.4f => "Medium",
            _ => "Low"
        };
    }

    // Additional helper methods for analytics calculations
    private float CalculateComplianceScoreFromAuditLog(dynamic auditLog)
    {
        // Calculate compliance score based on audit log severity and type
        return auditLog.Severity switch
        {
            AuditSeverity.Critical => 0.1f,
            AuditSeverity.High => 0.3f,
            AuditSeverity.Medium => 0.6f,
            AuditSeverity.Low => 0.8f,
            _ => 0.9f
        };
    }

    private ComplianceStandard GetStandardFromAuditLog(dynamic auditLog)
    {
        // Extract compliance standard from audit log context or flags
        if (auditLog.ComplianceFlags?.Contains("GDPR") == true)
            return ComplianceStandard.GDPR;
        if (auditLog.ComplianceFlags?.Contains("SOX") == true)
            return ComplianceStandard.SOX;
        if (auditLog.ComplianceFlags?.Contains("DataRetention") == true)
            return ComplianceStandard.DataRetention;

        return ComplianceStandard.AccessControl; // Default
    }

    private Dictionary<string, object> ExtractFeaturesFromAuditLog(dynamic auditLog)
    {
        return new Dictionary<string, object>
        {
            ["EventType"] = auditLog.EventType?.ToString() ?? "",
            ["Action"] = auditLog.Action ?? "",
            ["UserRole"] = auditLog.UserRole ?? "",
            ["Severity"] = auditLog.Severity.ToString(),
            ["HasOldValues"] = !string.IsNullOrEmpty(auditLog.OldValues),
            ["HasNewValues"] = !string.IsNullOrEmpty(auditLog.NewValues)
        };
    }

    private float CalculateAuditFrequency(Guid entityId, List<ComplianceDataPoint> historicalData)
    {
        var entityData = historicalData.Where(d => d.EntityId == entityId).ToList();
        if (!entityData.Any()) return 0f;

        var daysCovered = (entityData.Max(d => d.Date) - entityData.Min(d => d.Date)).Days;
        if (daysCovered == 0) return 1f;

        return Math.Min(1f, entityData.Count / (float)daysCovered * 30); // Normalize to monthly frequency
    }

    private float CalculateEntityAge(Guid entityId)
    {
        // This would typically come from entity creation date
        // For now, return a normalized age score (0-1)
        return 0.5f; // Placeholder
    }

    private float GetIndustryRiskScore(string entityType)
    {
        return entityType.ToLower() switch
        {
            "financial" => 0.9f,
            "healthcare" => 0.8f,
            "transport" => 0.7f,
            "logistics" => 0.6f,
            "technology" => 0.5f,
            _ => 0.4f
        };
    }

    private float GetViolationHistoryScore(Guid entityId)
    {
        // This would query historical violations for the entity
        // Return normalized score (0 = no violations, 1 = many violations)
        return 0.3f; // Placeholder
    }

    private float GetAuditFrequencyScore(Guid entityId)
    {
        // This would calculate how frequently the entity is audited
        // Return normalized score (0 = never audited, 1 = frequently audited)
        return 0.6f; // Placeholder
    }

    private float GetCurrentComplianceScore(Guid entityId)
    {
        // This would get the current compliance score for the entity
        // Return normalized score (0 = non-compliant, 1 = fully compliant)
        return 0.7f; // Placeholder
    }

    private async Task<List<ViolationDataPoint>> GetHistoricalViolationDataAsync(
        Guid entityId, string entityType, CancellationToken cancellationToken)
    {
        // Get historical violation data for pattern analysis
        var auditLogs = await _auditLogRepository.GetAuditTrailAsync(
            entityId: entityId,
            entityType: entityType,
            minSeverity: AuditSeverity.High,
            fromDate: DateTime.UtcNow.AddYears(-2),
            toDate: DateTime.UtcNow,
            cancellationToken: cancellationToken);

        return auditLogs.Where(log => log.Severity >= AuditSeverity.High)
            .Select(log => new ViolationDataPoint
            {
                Date = log.Timestamp,
                EntityId = entityId,
                ViolationType = log.EventType?.ToString() ?? "Unknown",
                Severity = log.Severity.ToString(),
                Standard = GetStandardFromAuditLog(log)
            }).ToList();
    }

    private async Task<float> PredictViolationProbabilityAsync(
        Guid entityId, string entityType, ComplianceStandard standard,
        DateTime predictionPeriod, List<ViolationDataPoint> historicalViolations)
    {
        // Simple probability calculation based on historical patterns
        var relevantViolations = historicalViolations
            .Where(v => v.Standard == standard && v.EntityId == entityId)
            .ToList();

        if (!relevantViolations.Any()) return 0f;

        // Calculate violation frequency
        var daysSinceLastViolation = (DateTime.UtcNow - relevantViolations.Max(v => v.Date)).Days;
        var averageDaysBetweenViolations = relevantViolations.Count > 1
            ? (relevantViolations.Max(v => v.Date) - relevantViolations.Min(v => v.Date)).Days / (relevantViolations.Count - 1)
            : 365;

        // Simple probability model
        var baseProbability = Math.Min(1f, relevantViolations.Count / 10f); // More violations = higher probability
        var timeFactor = Math.Max(0f, 1f - (daysSinceLastViolation / (float)averageDaysBetweenViolations));

        return Math.Min(1f, baseProbability * 0.7f + timeFactor * 0.3f);
    }

    private string GetViolationTypeForStandard(ComplianceStandard standard)
    {
        return standard switch
        {
            ComplianceStandard.GDPR => "Data Privacy Violation",
            ComplianceStandard.SOX => "Financial Reporting Violation",
            ComplianceStandard.DataRetention => "Data Retention Violation",
            ComplianceStandard.AccessControl => "Access Control Violation",
            _ => "Compliance Violation"
        };
    }

    private string GetViolationSeverity(float probability)
    {
        return probability switch
        {
            >= 0.8f => "Critical",
            >= 0.6f => "High",
            >= 0.4f => "Medium",
            _ => "Low"
        };
    }

    private List<string> GetPreventionActions(ComplianceStandard standard, float probability)
    {
        var actions = new List<string>();

        switch (standard)
        {
            case ComplianceStandard.GDPR:
                actions.AddRange(new[]
                {
                    "Review data processing activities",
                    "Update privacy policies",
                    "Conduct data protection impact assessment",
                    "Train staff on GDPR requirements"
                });
                break;
            case ComplianceStandard.SOX:
                actions.AddRange(new[]
                {
                    "Review financial controls",
                    "Update documentation procedures",
                    "Conduct internal audit",
                    "Strengthen segregation of duties"
                });
                break;
            case ComplianceStandard.DataRetention:
                actions.AddRange(new[]
                {
                    "Review data retention policies",
                    "Implement automated data purging",
                    "Update backup procedures",
                    "Train staff on retention requirements"
                });
                break;
            default:
                actions.AddRange(new[]
                {
                    "Review compliance procedures",
                    "Update documentation",
                    "Provide staff training",
                    "Implement monitoring controls"
                });
                break;
        }

        if (probability >= 0.7f)
        {
            actions.Insert(0, "Immediate management review required");
            actions.Insert(1, "Consider external compliance consultation");
        }

        return actions;
    }
}

// ML.NET data models
public class RiskPredictionInput
{
    public float ViolationHistory { get; set; }
    public float AuditFrequency { get; set; }
    public float ComplianceScore { get; set; }
    public float EntityAge { get; set; }
    public float IndustryRisk { get; set; }
}

public class RiskPredictionOutput
{
    [ColumnName("Score")]
    public float RiskScore { get; set; }

    public float Confidence { get; set; }
}

public class ComplianceDataPoint
{
    public DateTime Date { get; set; }
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public float ComplianceScore { get; set; }
    public bool HasViolation { get; set; }
    public ComplianceStandard Standard { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
}

public class ViolationDataPoint
{
    public DateTime Date { get; set; }
    public Guid EntityId { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public ComplianceStandard Standard { get; set; }
}
