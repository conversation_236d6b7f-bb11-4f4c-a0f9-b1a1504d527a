using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerCollaborationHistoryQueryHandler : IRequestHandler<GetPartnerCollaborationHistoryQuery, List<PartnerCollaborationSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPartnerCollaborationHistoryQueryHandler> _logger;

    public GetPartnerCollaborationHistoryQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPartnerCollaborationHistoryQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PartnerCollaborationSummaryDto>> Handle(GetPartnerCollaborationHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.PreferredPartnerId, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                return new List<PartnerCollaborationSummaryDto>();
            }

            // This would typically fetch from a collaboration/order service
            // For now, return mock data based on the partner's performance metrics
            var collaborations = new List<PartnerCollaborationSummaryDto>();

            // Generate sample collaboration history based on partner metrics
            var random = new Random();
            var totalDays = (request.ToDate - request.FromDate).Days;
            var collaborationCount = Math.Min(partner.PerformanceMetrics.TotalOrders, request.Limit ?? 50);

            for (int i = 0; i < collaborationCount; i++)
            {
                var collaborationDate = request.FromDate.AddDays(random.Next(totalDays));
                collaborations.Add(new PartnerCollaborationSummaryDto
                {
                    PartnerId = partner.PartnerId,
                    PartnerName = "Partner Name", // Would be fetched from partner service
                    PartnerType = partner.PartnerType.ToString(),
                    CollaborationDate = collaborationDate,
                    CollaborationType = "Order",
                    CollaborationId = Guid.NewGuid(),
                    Status = random.Next(10) < 9 ? "Completed" : "Failed", // 90% success rate
                    Value = (decimal)(random.NextDouble() * 10000 + 1000), // Random value between 1000-11000
                    Rating = (decimal)(random.NextDouble() * 2 + 3), // Random rating between 3-5
                    OnTime = random.Next(10) < 8, // 80% on time
                    Successful = random.Next(10) < 9 // 90% successful
                });
            }

            return collaborations.OrderByDescending(c => c.CollaborationDate).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving collaboration history for preferred partner {PreferredPartnerId}", 
                request.PreferredPartnerId);
            throw;
        }
    }
}
