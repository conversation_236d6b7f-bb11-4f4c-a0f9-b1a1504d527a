using System;
using System.Collections.Generic;
using MediatR;
using UserManagement.Domain.Entities;
using UserManagement.Domain.ValueObjects;

namespace UserManagement.Application.Admin.Queries.GetUsersAdvanced
{
    public class GetUsersAdvancedQuery : IRequest<GetUsersAdvancedResponse>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;

        // Search filters
        public string SearchTerm { get; set; } // Search by company name, user name, email, or mobile number
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string CompanyName { get; set; }
        public string UserName { get; set; }

        // Subscription filters
        public string SubscriptionPlan { get; set; }
        public DateTime? PlanExpiryFrom { get; set; }
        public DateTime? PlanExpiryTo { get; set; }

        // Registration date filters
        public DateTime? RegistrationDateFrom { get; set; }
        public DateTime? RegistrationDateTo { get; set; }

        // Status filters
        public ProfileStatus? Status { get; set; }
        public UserType? UserType { get; set; }
        public bool? IsActive { get; set; }

        // KYC filters
        public string KycStatus { get; set; }
        public bool? KycCompleted { get; set; }

        // Activity filters
        public DateTime? LastLoginFrom { get; set; }
        public DateTime? LastLoginTo { get; set; }
        public int? MinRfqVolume { get; set; }
        public int? MaxRfqVolume { get; set; }

        // Sorting options
        public string SortBy { get; set; } = "CreatedAt"; // CreatedAt, LastLogin, PlanExpiry, RfqVolume, CompanyName, Email
        public string SortDirection { get; set; } = "desc"; // asc, desc

        // Additional filters
        public bool? HasSubscription { get; set; }
        public bool? AutoRenewalEnabled { get; set; }
        public string Region { get; set; }
        public string State { get; set; }
        public string City { get; set; }
    }

    public class GetUsersAdvancedResponse
    {
        public List<UserAdvancedDto> Users { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
        public UserStatistics Statistics { get; set; }
    }

    public class UserAdvancedDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string DisplayName { get; set; }
        public string PhoneNumber { get; set; }
        public string CompanyName { get; set; }
        public UserType UserType { get; set; }
        public ProfileStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public bool IsActive { get; set; }

        // Personal Information
        public PersonalInfo PersonalInfo { get; set; }

        // Company Information
        public CompanyInfo CompanyInfo { get; set; }

        // Address Information
        public AddressInfo AddressInfo { get; set; }

        // Activity Information
        public ActivityInfo ActivityInfo { get; set; }

        // Document Summary
        public DocumentSummary DocumentSummary { get; set; }

        // Approval Information
        public DateTime? ApprovedAt { get; set; }
        public DateTime? RejectedAt { get; set; }

        // KYC Information
        public string KycStatus { get; set; }
        public DateTime? KycSubmittedAt { get; set; }
        public DateTime? KycApprovedAt { get; set; }
        public bool KycCompleted { get; set; }

        // Subscription Information
        public SubscriptionInfo Subscription { get; set; }

        // Activity Information
        public ActivityInfo Activity { get; set; }

        // Address Information
        public AddressInfo Address { get; set; }

        // Document Information
        public DocumentSummary Documents { get; set; }
    }

    public class SubscriptionInfo
    {
        public string PlanName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsActive { get; set; }
        public bool AutoRenewalEnabled { get; set; }
        public string Status { get; set; }
        public decimal? MonthlyFee { get; set; }
        public int? FeatureLimits { get; set; }
    }

    public class ActivityInfo
    {
        public DateTime? LastLoginAt { get; set; }
        public string LastLoginIp { get; set; }
        public int RfqCount { get; set; }
        public int OrderCount { get; set; }
        public int TripCount { get; set; }
        public decimal? TotalBusinessValue { get; set; }
    }

    public class AddressInfo
    {
        public string Street { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string Region { get; set; }
    }

    public class DocumentSummary
    {
        public int TotalDocuments { get; set; }
        public int ApprovedDocuments { get; set; }
        public int PendingDocuments { get; set; }
        public int RejectedDocuments { get; set; }
        public DateTime? LastDocumentUpload { get; set; }
    }

    public class UserStatistics
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int PendingApproval { get; set; }
        public int KycCompleted { get; set; }
        public int KycPending { get; set; }
        public int WithActiveSubscription { get; set; }
        public int WithExpiredSubscription { get; set; }
        public Dictionary<string, int> UsersByType { get; set; } = new();
        public Dictionary<string, int> UsersByStatus { get; set; } = new();
        public Dictionary<string, int> UsersByRegion { get; set; } = new();
    }

    // Export-related DTOs
    public enum ExportFormat
    {
        CSV,
        PDF,
        Excel
    }

    public class UserSummaryExportRequest
    {
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public UserType? UserType { get; set; }
        public ProfileStatus? Status { get; set; }
        public string? Location { get; set; }
        public string? Region { get; set; }
        public bool IncludeActivityMetrics { get; set; } = true;
        public bool IncludeDocumentSummary { get; set; } = true;
        public bool IncludeSubscriptionInfo { get; set; } = true;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
    }

    public class UserSummaryExportResponse
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; }
    }
}
