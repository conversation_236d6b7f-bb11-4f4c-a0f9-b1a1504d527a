# Milestone Configuration API Documentation

## Overview

The Milestone Configuration API provides comprehensive management of milestone templates, steps, payout rules, and role-based template assignments for the TLI Mobile & Workflow Service. This API enables dynamic configuration of milestone-based workflows for trips, orders, and other business processes.

## Base URL

```
https://api.tli.com/mobile-workflow/api
```

## Authentication

All endpoints require JWT Bearer token authentication:

```http
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### Milestone Templates

#### 1. Get All Milestone Templates

```http
GET /MilestoneTemplate
```

**Query Parameters:**
- `type` (optional): Filter by template type (Trip, Order, Delivery, etc.)
- `category` (optional): Filter by category (Logistics, Transportation, etc.)
- `isActive` (optional): Filter by active status (true/false)
- `isDefault` (optional): Filter by default status (true/false)

**Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Standard Trip Milestones",
    "description": "Standard milestone template for trip completion",
    "type": "Trip",
    "category": "Logistics",
    "isActive": true,
    "isDefault": true,
    "createdBy": "<EMAIL>",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-20T14:45:00Z",
    "usageCount": 150,
    "totalPayoutPercentage": 100.0,
    "isValid": true,
    "configuration": {
      "auto_advance": true,
      "require_photos": false
    },
    "metadata": {
      "version": "1.0",
      "department": "logistics"
    }
  }
]
```

#### 2. Get Milestone Template by ID

```http
GET /MilestoneTemplate/{id}
```

**Path Parameters:**
- `id`: Template UUID

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Standard Trip Milestones",
  "description": "Standard milestone template for trip completion",
  "type": "Trip",
  "category": "Logistics",
  "isActive": true,
  "isDefault": true,
  "createdBy": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00Z",
  "steps": [
    {
      "id": "456e7890-e89b-12d3-a456-************",
      "name": "Trip Started",
      "description": "Driver has started the trip",
      "sequenceNumber": 1,
      "isRequired": true,
      "isActive": true,
      "triggerCondition": "status=started",
      "totalPayoutPercentage": 30.0,
      "payoutRules": [
        {
          "id": "789e0123-e89b-12d3-a456-************",
          "payoutPercentage": 30.0,
          "triggerCondition": "status=started",
          "description": "Payment for starting trip",
          "isActive": true
        }
      ]
    }
  ]
}
```

#### 3. Create Milestone Template

```http
POST /MilestoneTemplate
```

**Request Body:**
```json
{
  "name": "Express Delivery Milestones",
  "description": "Milestone template for express delivery",
  "type": "Delivery",
  "category": "Express",
  "configuration": {
    "auto_advance": true,
    "timeout_minutes": 60
  },
  "metadata": {
    "priority": "high",
    "sla_hours": 4
  },
  "steps": [
    {
      "name": "Package Picked",
      "description": "Package picked from sender",
      "sequenceNumber": 1,
      "isRequired": true,
      "triggerCondition": "status=picked",
      "configuration": {
        "require_photo": true,
        "gps_verification": true
      },
      "payoutRules": [
        {
          "payoutPercentage": 40.0,
          "triggerCondition": "status=picked",
          "description": "Payment for pickup"
        }
      ]
    },
    {
      "name": "Package Delivered",
      "description": "Package delivered to recipient",
      "sequenceNumber": 2,
      "isRequired": true,
      "triggerCondition": "status=delivered",
      "payoutRules": [
        {
          "payoutPercentage": 60.0,
          "triggerCondition": "status=delivered",
          "description": "Payment for delivery"
        }
      ]
    }
  ]
}
```

**Response:** `201 Created` with created template object

#### 4. Update Milestone Template

```http
PUT /MilestoneTemplate/{id}
```

**Request Body:**
```json
{
  "name": "Updated Template Name",
  "description": "Updated description",
  "configuration": {
    "auto_advance": false,
    "require_approval": true
  },
  "metadata": {
    "version": "2.0",
    "last_review": "2024-01-20"
  }
}
```

**Response:** `200 OK` with updated template object

#### 5. Delete Milestone Template

```http
DELETE /MilestoneTemplate/{id}
```

**Response:** `204 No Content`

#### 6. Activate/Deactivate Template

```http
POST /MilestoneTemplate/{id}/activate
POST /MilestoneTemplate/{id}/deactivate
```

**Response:** `200 OK` with updated template object

#### 7. Set/Remove as Default

```http
POST /MilestoneTemplate/{id}/set-default
POST /MilestoneTemplate/{id}/remove-default
```

**Response:** `200 OK` with updated template object

#### 8. Validate Template

```http
GET /MilestoneTemplate/{id}/validate
```

**Response:**
```json
{
  "isValid": true,
  "totalPayoutPercentage": 100.0,
  "errors": [],
  "warnings": [
    "Template has not been used in the last 30 days"
  ],
  "validationDetails": {
    "stepCount": 3,
    "requiredStepCount": 2,
    "payoutRuleCount": 5,
    "activeStepCount": 3,
    "roleMappingCount": 2
  }
}
```

#### 9. Search Templates

```http
GET /MilestoneTemplate/search?searchTerm=delivery
```

**Response:** Array of matching templates

#### 10. Get Template Previews (Paginated)

```http
GET /MilestoneTemplate/previews?page=1&pageSize=10&type=Trip
```

**Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Standard Trip Milestones",
    "type": "Trip",
    "category": "Logistics",
    "stepCount": 3,
    "totalPayoutPercentage": 100.0,
    "isActive": true,
    "isDefault": true,
    "isValid": true,
    "usageCount": 150,
    "createdAt": "2024-01-15T10:30:00Z"
  }
]
```

#### 11. Reorder Steps

```http
POST /MilestoneTemplate/{id}/reorder-steps
```

**Request Body:**
```json
{
  "stepSequenceMap": {
    "456e7890-e89b-12d3-a456-************": 3,
    "789e0123-e89b-12d3-a456-************": 1,
    "abc1234d-e89b-12d3-a456-426614174003": 2
  }
}
```

**Response:** `200 OK` with updated template

#### 12. Bulk Operations

```http
POST /MilestoneTemplate/bulk
```

**Request Body:**
```json
{
  "templateIds": [
    "123e4567-e89b-12d3-a456-************",
    "456e7890-e89b-12d3-a456-************"
  ],
  "operation": "activate",
  "parameters": {
    "reason": "Bulk activation for Q1 rollout"
  }
}
```

**Response:**
```json
{
  "totalRequested": 2,
  "successful": 2,
  "failed": 0,
  "errors": [],
  "results": [
    {
      "templateId": "123e4567-e89b-12d3-a456-************",
      "success": true
    },
    {
      "templateId": "456e7890-e89b-12d3-a456-************",
      "success": true
    }
  ]
}
```

### Milestone Steps

#### 1. Get Steps for Template

```http
GET /milestone-templates/{templateId}/MilestoneStep
```

**Query Parameters:**
- `activeOnly` (optional): Return only active steps

**Response:** Array of milestone step objects

#### 2. Get Required Steps

```http
GET /milestone-templates/{templateId}/MilestoneStep/required
```

**Response:** Array of required milestone steps

#### 3. Create Milestone Step

```http
POST /milestone-templates/{templateId}/MilestoneStep
```

**Request Body:**
```json
{
  "name": "POD Verification",
  "description": "Verify proof of delivery",
  "sequenceNumber": 3,
  "isRequired": true,
  "triggerCondition": "pod_uploaded=true",
  "configuration": {
    "require_signature": true,
    "require_photo": true
  },
  "payoutRules": [
    {
      "payoutPercentage": 25.0,
      "triggerCondition": "pod_verified=true",
      "description": "Payment for POD verification"
    }
  ]
}
```

**Response:** `201 Created` with created step object

#### 4. Update Milestone Step

```http
PUT /milestone-templates/{templateId}/MilestoneStep/{stepId}
```

**Response:** `200 OK` with updated step object

#### 5. Delete Milestone Step

```http
DELETE /milestone-templates/{templateId}/MilestoneStep/{stepId}
```

**Response:** `204 No Content`

### Payout Rules

#### 1. Get Payout Rules for Step

```http
GET /milestone-steps/{stepId}/payout-rules
```

**Response:** Array of payout rule objects

#### 2. Create Payout Rule

```http
POST /milestone-steps/{stepId}/payout-rules
```

**Request Body:**
```json
{
  "payoutPercentage": 15.0,
  "triggerCondition": "quality_check=passed",
  "description": "Bonus for quality compliance",
  "configuration": {
    "auto_trigger": true,
    "delay_minutes": 0
  }
}
```

**Response:** `201 Created` with created rule object

#### 3. Get Total Payout for Template

```http
GET /milestone-templates/{templateId}/total-payout
```

**Response:**
```json
{
  "totalPercentage": 100.0
}
```

### Role Template Mappings

#### 1. Get All Role Mappings

```http
GET /RoleTemplateMapping
```

**Response:** Array of role mapping objects

#### 2. Get Mappings by Role

```http
GET /RoleTemplateMapping/by-role/{roleName}?activeOnly=true
```

**Response:** Array of mappings for the specified role

#### 3. Get Default Mapping for Role

```http
GET /RoleTemplateMapping/default/{roleName}
```

**Response:** Default template mapping for the role

#### 4. Get Best Match for Role

```http
POST /RoleTemplateMapping/best-match/{roleName}
```

**Request Body:**
```json
{
  "department": "logistics",
  "experience_level": "senior",
  "region": "north",
  "vehicle_type": "truck"
}
```

**Response:** Best matching template for the role and context

#### 5. Create Role Mapping

```http
POST /RoleTemplateMapping
```

**Request Body:**
```json
{
  "roleName": "SeniorDriver",
  "milestoneTemplateId": "123e4567-e89b-12d3-a456-************",
  "isDefault": false,
  "priority": 150,
  "conditions": "{\"experience_years\": \">5\", \"rating\": \">4.5\"}",
  "configuration": {
    "auto_assign": true,
    "notification_enabled": true
  }
}
```

**Response:** `201 Created` with created mapping object

## Error Responses

### Standard Error Format

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Template validation failed",
    "details": [
      "Total payout percentage must equal 100%",
      "Step sequence numbers must be consecutive"
    ],
    "timestamp": "2024-01-20T15:30:00Z",
    "traceId": "abc123def456"
  }
}
```

### Common Error Codes

- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: Insufficient permissions for the operation
- `404 Not Found`: Requested resource not found
- `409 Conflict`: Resource already exists or conflict with current state
- `422 Unprocessable Entity`: Business rule validation failed
- `500 Internal Server Error`: Unexpected server error

## Rate Limiting

- **Rate Limit**: 1000 requests per hour per API key
- **Burst Limit**: 100 requests per minute
- **Headers**: 
  - `X-RateLimit-Limit`: Total requests allowed
  - `X-RateLimit-Remaining`: Requests remaining
  - `X-RateLimit-Reset`: Time when limit resets

## Pagination

For endpoints that return lists, pagination is supported:

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 10, max: 100)

**Response Headers:**
- `X-Total-Count`: Total number of items
- `X-Page-Count`: Total number of pages
- `Link`: Navigation links (first, prev, next, last)

## Webhooks

The API supports webhooks for real-time notifications:

### Supported Events

- `milestone_template.created`
- `milestone_template.updated`
- `milestone_template.deleted`
- `milestone_template.activated`
- `milestone_template.deactivated`
- `milestone_step.added`
- `milestone_step.removed`
- `role_mapping.created`
- `role_mapping.updated`

### Webhook Payload Example

```json
{
  "event": "milestone_template.created",
  "timestamp": "2024-01-20T15:30:00Z",
  "data": {
    "templateId": "123e4567-e89b-12d3-a456-************",
    "name": "New Template",
    "type": "Trip",
    "createdBy": "<EMAIL>"
  }
}
```

## SDK and Code Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const client = axios.create({
  baseURL: 'https://api.tli.com/mobile-workflow/api',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
});

// Get all templates
const templates = await client.get('/MilestoneTemplate');

// Create new template
const newTemplate = await client.post('/MilestoneTemplate', {
  name: 'My Template',
  description: 'Template description',
  type: 'Trip',
  category: 'Logistics'
});
```

### Python

```python
import requests

headers = {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
}

base_url = 'https://api.tli.com/mobile-workflow/api'

# Get all templates
response = requests.get(f'{base_url}/MilestoneTemplate', headers=headers)
templates = response.json()

# Create new template
template_data = {
    'name': 'My Template',
    'description': 'Template description',
    'type': 'Trip',
    'category': 'Logistics'
}
response = requests.post(f'{base_url}/MilestoneTemplate', 
                        json=template_data, headers=headers)
```

### C#

```csharp
using System.Net.Http;
using System.Text.Json;

var client = new HttpClient();
client.DefaultRequestHeaders.Authorization = 
    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "your-jwt-token");

var baseUrl = "https://api.tli.com/mobile-workflow/api";

// Get all templates
var response = await client.GetAsync($"{baseUrl}/MilestoneTemplate");
var templates = await response.Content.ReadFromJsonAsync<List<MilestoneTemplateDto>>();

// Create new template
var templateData = new {
    name = "My Template",
    description = "Template description",
    type = "Trip",
    category = "Logistics"
};
var createResponse = await client.PostAsJsonAsync($"{baseUrl}/MilestoneTemplate", templateData);
```

## Support

For API support and questions:
- **Documentation**: https://docs.tli.com/mobile-workflow
- **Support Email**: <EMAIL>
- **Developer Portal**: https://developer.tli.com
- **Status Page**: https://status.tli.com
