using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class AdministrativeAuditLogConfiguration : IEntityTypeConfiguration<AdministrativeAuditLog>
{
    public void Configure(EntityTypeBuilder<AdministrativeAuditLog> builder)
    {
        builder.ToTable("AdministrativeAuditLogs");

        builder.HasKey(aal => aal.Id);

        builder.Property(aal => aal.Id)
            .ValueGeneratedOnAdd();

        builder.Property(aal => aal.AdminUserId)
            .IsRequired();

        builder.Property(aal => aal.AdminUserName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(aal => aal.AdminRole)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(aal => aal.Action)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(aal => aal.TargetEntityId)
            .IsRequired(false);

        builder.Property(aal => aal.TargetEntityType)
            .HasMaxLength(100);

        builder.Property(aal => aal.Reason)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(aal => aal.AdditionalNotes)
            .HasMaxLength(4000);

        builder.Property(aal => aal.ActionTimestamp)
            .IsRequired();

        builder.Property(aal => aal.IpAddress)
            .HasMaxLength(45); // IPv6 max length

        builder.Property(aal => aal.UserAgent)
            .HasMaxLength(1000);

        builder.Property(aal => aal.Severity)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(aal => aal.IsSuccessful)
            .IsRequired();

        builder.Property(aal => aal.ErrorMessage)
            .HasMaxLength(2000);

        // Configure AuditMetadata as JSON
        builder.Property(aal => aal.AuditMetadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        builder.Property(aal => aal.CorrelationId)
            .IsRequired(false);

        builder.Property(aal => aal.SessionId)
            .HasMaxLength(200);

        // Indexes for performance and security monitoring
        builder.HasIndex(aal => aal.AdminUserId)
            .HasDatabaseName("IX_AdministrativeAuditLogs_AdminUserId");

        builder.HasIndex(aal => aal.Action)
            .HasDatabaseName("IX_AdministrativeAuditLogs_Action");

        builder.HasIndex(aal => aal.ActionTimestamp)
            .HasDatabaseName("IX_AdministrativeAuditLogs_ActionTimestamp");

        builder.HasIndex(aal => aal.Severity)
            .HasDatabaseName("IX_AdministrativeAuditLogs_Severity");

        builder.HasIndex(aal => aal.TargetEntityId)
            .HasDatabaseName("IX_AdministrativeAuditLogs_TargetEntityId");

        builder.HasIndex(aal => aal.CorrelationId)
            .HasDatabaseName("IX_AdministrativeAuditLogs_CorrelationId");

        builder.HasIndex(aal => new { aal.Action, aal.ActionTimestamp })
            .HasDatabaseName("IX_AdministrativeAuditLogs_Action_Timestamp");

        builder.HasIndex(aal => new { aal.AdminUserId, aal.ActionTimestamp })
            .HasDatabaseName("IX_AdministrativeAuditLogs_AdminUserId_Timestamp");

        builder.HasIndex(aal => new { aal.Severity, aal.ActionTimestamp })
            .HasDatabaseName("IX_AdministrativeAuditLogs_Severity_Timestamp");

        // Security monitoring index
        builder.HasIndex(aal => new { aal.Action, aal.IsSuccessful, aal.ActionTimestamp })
            .HasDatabaseName("IX_AdministrativeAuditLogs_Security_Monitor")
            .HasFilter("Action = 2"); // SecurityViolation enum value
    }
}
