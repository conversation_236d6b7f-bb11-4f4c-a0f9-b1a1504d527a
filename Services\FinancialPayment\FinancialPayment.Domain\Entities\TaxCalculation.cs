using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class TaxCalculation : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public Guid? UserId { get; private set; }
    public Money SubtotalAmount { get; private set; }
    public Money TotalTaxAmount { get; private set; }
    public Money TotalAmount { get; private set; }
    public string Jurisdiction { get; private set; }
    public DateTime CalculatedAt { get; private set; }
    public string? ProductCategory { get; private set; }
    public string? ServiceType { get; private set; }
    public TaxCalculationStatus Status { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<TaxCalculationLine> _taxLines = new();
    public IReadOnlyList<TaxCalculationLine> TaxLines => _taxLines.AsReadOnly();

    private TaxCalculation() { }

    public TaxCalculation(
        Guid orderId,
        Guid? userId,
        Money subtotalAmount,
        string jurisdiction,
        string? productCategory = null,
        string? serviceType = null)
    {
        if (subtotalAmount.Amount < 0)
            throw new ArgumentException("Subtotal amount cannot be negative", nameof(subtotalAmount));

        if (string.IsNullOrWhiteSpace(jurisdiction))
            throw new ArgumentException("Jurisdiction cannot be empty", nameof(jurisdiction));

        OrderId = orderId;
        UserId = userId;
        SubtotalAmount = subtotalAmount;
        TotalTaxAmount = Money.Zero(subtotalAmount.Currency);
        TotalAmount = subtotalAmount;
        Jurisdiction = jurisdiction;
        ProductCategory = productCategory;
        ServiceType = serviceType;
        CalculatedAt = DateTime.UtcNow;
        Status = TaxCalculationStatus.Pending;
    }

    public void AddTaxLine(TaxRule taxRule, Money taxAmount, string? description = null)
    {
        if (taxAmount.Currency != SubtotalAmount.Currency)
            throw new ArgumentException("Tax amount currency must match subtotal currency");

        var taxLine = new TaxCalculationLine(
            Id,
            taxRule.Id,
            taxRule.Name,
            taxRule.TaxType,
            taxRule.Rate,
            taxAmount,
            description);

        _taxLines.Add(taxLine);
        RecalculateTotals();
    }

    public void RemoveTaxLine(Guid taxLineId)
    {
        var taxLine = _taxLines.FirstOrDefault(tl => tl.Id == taxLineId);
        if (taxLine != null)
        {
            _taxLines.Remove(taxLine);
            RecalculateTotals();
        }
    }

    public void ClearTaxLines()
    {
        _taxLines.Clear();
        RecalculateTotals();
    }

    private void RecalculateTotals()
    {
        TotalTaxAmount = _taxLines.Aggregate(
            Money.Zero(SubtotalAmount.Currency),
            (sum, line) => sum + line.TaxAmount);

        TotalAmount = SubtotalAmount + TotalTaxAmount;
        SetUpdatedAt();
    }

    public void MarkAsCalculated(string? notes = null)
    {
        Status = TaxCalculationStatus.Calculated;
        Notes = notes;
        SetUpdatedAt();
    }

    public void MarkAsApplied(string? notes = null)
    {
        if (Status != TaxCalculationStatus.Calculated)
            throw new InvalidOperationException("Tax calculation must be calculated before it can be applied");

        Status = TaxCalculationStatus.Applied;
        Notes = notes;
        SetUpdatedAt();
    }

    public void MarkAsFailed(string errorMessage)
    {
        Status = TaxCalculationStatus.Failed;
        Notes = errorMessage;
        SetUpdatedAt();
    }

    public decimal GetEffectiveTaxRate()
    {
        if (SubtotalAmount.Amount == 0)
            return 0;

        return (TotalTaxAmount.Amount / SubtotalAmount.Amount) * 100;
    }

    public TaxCalculationSummary GetSummary()
    {
        return new TaxCalculationSummary
        {
            OrderId = OrderId,
            SubtotalAmount = SubtotalAmount,
            TotalTaxAmount = TotalTaxAmount,
            TotalAmount = TotalAmount,
            EffectiveTaxRate = GetEffectiveTaxRate(),
            TaxLineCount = _taxLines.Count,
            Jurisdiction = Jurisdiction,
            Status = Status,
            CalculatedAt = CalculatedAt
        };
    }
}

public class TaxCalculationLine : BaseEntity
{
    public Guid TaxCalculationId { get; private set; }
    public Guid TaxRuleId { get; private set; }
    public string TaxName { get; private set; }
    public string TaxType { get; private set; }
    public decimal TaxRate { get; private set; }
    public Money TaxAmount { get; private set; }
    public string? Description { get; private set; }

    private TaxCalculationLine() { }

    public TaxCalculationLine(
        Guid taxCalculationId,
        Guid taxRuleId,
        string taxName,
        string taxType,
        decimal taxRate,
        Money taxAmount,
        string? description = null)
    {
        if (string.IsNullOrWhiteSpace(taxName))
            throw new ArgumentException("Tax name cannot be empty", nameof(taxName));

        if (taxAmount.Amount < 0)
            throw new ArgumentException("Tax amount cannot be negative", nameof(taxAmount));

        TaxCalculationId = taxCalculationId;
        TaxRuleId = taxRuleId;
        TaxName = taxName;
        TaxType = taxType;
        TaxRate = taxRate;
        TaxAmount = taxAmount;
        Description = description;
    }
}

public enum TaxCalculationStatus
{
    Pending = 1,
    Calculated = 2,
    Applied = 3,
    Failed = 4
}

public class TaxCalculationSummary
{
    public Guid OrderId { get; set; }
    public Money SubtotalAmount { get; set; } = Money.Zero("INR");
    public Money TotalTaxAmount { get; set; } = Money.Zero("INR");
    public Money TotalAmount { get; set; } = Money.Zero("INR");
    public decimal EffectiveTaxRate { get; set; }
    public int TaxLineCount { get; set; }
    public string Jurisdiction { get; set; } = string.Empty;
    public TaxCalculationStatus Status { get; set; }
    public DateTime CalculatedAt { get; set; }
}


