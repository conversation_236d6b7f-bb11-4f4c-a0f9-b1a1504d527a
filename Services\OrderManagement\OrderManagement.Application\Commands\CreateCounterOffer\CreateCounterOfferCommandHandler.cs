using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.CreateCounterOffer;

public class CreateCounterOfferCommandHandler : IRequestHandler<CreateCounterOfferCommand, Guid>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateCounterOfferCommandHandler> _logger;

    public CreateCounterOfferCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreateCounterOfferCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateCounterOfferCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating counter offer for negotiation {NegotiationId} by user {UserId}",
            request.NegotiationId, request.RequestingUserId);

        try
        {
            // Get the RFQ with negotiations
            var rfq = await _rfqRepository.GetByIdWithNegotiationsAsync(request.NegotiationId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ not found for negotiation {NegotiationId}", request.NegotiationId);
                throw new ArgumentException($"RFQ not found for negotiation {request.NegotiationId}");
            }

            // Find the negotiation
            var negotiation = rfq.Negotiations.FirstOrDefault(n => n.Id == request.NegotiationId);
            if (negotiation == null)
            {
                _logger.LogWarning("Negotiation {NegotiationId} not found", request.NegotiationId);
                throw new ArgumentException($"Negotiation {request.NegotiationId} not found");
            }

            // Validate user has permission to create counter offer
            bool hasPermission = false;
            if (request.OfferType == OrderManagement.Domain.Entities.CounterOfferType.Shipper)
            {
                hasPermission = rfq.TransportCompanyId == request.RequestingUserId;
            }
            else if (request.OfferType == OrderManagement.Domain.Entities.CounterOfferType.Broker)
            {
                hasPermission = negotiation.BrokerId == request.RequestingUserId;
            }

            if (!hasPermission)
            {
                _logger.LogWarning("User {UserId} does not have permission to create counter offer for negotiation {NegotiationId}",
                    request.RequestingUserId, request.NegotiationId);
                throw new UnauthorizedAccessException("You do not have permission to create counter offer for this negotiation");
            }

            // Validate negotiation is active
            if (!negotiation.IsActive)
            {
                _logger.LogWarning("Cannot create counter offer for inactive negotiation {NegotiationId}",
                    request.NegotiationId);
                throw new InvalidOperationException("Cannot create counter offer for inactive negotiation");
            }

            // Convert DTO to domain value object
            var offeredPrice = new Money(request.OfferedPrice.Amount, request.OfferedPrice.Currency);

            // Create the counter offer
            var counterOffer = negotiation.CreateCounterOffer(
                request.BidId,
                request.OfferType,
                offeredPrice,
                request.RequestingUserId,
                request.OfferedTerms,
                request.OfferedPickupDate,
                request.OfferedDeliveryDate,
                request.AdditionalServices,
                request.CounterOfferReason,
                request.ExpiresAt);

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created counter offer {CounterOfferId} for negotiation {NegotiationId}",
                counterOffer.Id, request.NegotiationId);

            return counterOffer.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating counter offer for negotiation {NegotiationId}", request.NegotiationId);
            throw;
        }
    }
}
