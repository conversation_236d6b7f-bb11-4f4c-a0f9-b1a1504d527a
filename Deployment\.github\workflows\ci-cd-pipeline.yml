name: TLI Shipper Portal CI/CD Pipeline

on:
  push:
    branches: [ main, develop, release/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      version:
        description: 'Version to deploy'
        required: false
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_PREFIX: tli
  DOTNET_VERSION: '8.0'
  NODE_VERSION: '18'

jobs:
  # ===== BUILD AND TEST JOBS =====
  
  detect-changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.changes.outputs.services }}
      infrastructure: ${{ steps.changes.outputs.infrastructure }}
      documentation: ${{ steps.changes.outputs.documentation }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changes
        id: changes
        run: |
          # Detect which services have changed
          CHANGED_SERVICES=""
          
          if git diff --name-only HEAD~1 | grep -q "Services/OrderManagement/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES order-management"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/TripManagement/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES trip-management"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/FinancialPayment/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES financial-payment"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/NetworkFleetManagement/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES network-fleet-management"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/AnalyticsBIService/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES analytics-bi-service"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/UserManagement/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES user-management"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/CommunicationNotification/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES communication-notification"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/DataStorage/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES data-storage"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/MobileWorkflow/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES mobile-workflow"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Services/AuditCompliance/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES audit-compliance"
          fi
          
          if git diff --name-only HEAD~1 | grep -q "Gateway/"; then
            CHANGED_SERVICES="$CHANGED_SERVICES api-gateway"
          fi
          
          echo "services=$CHANGED_SERVICES" >> $GITHUB_OUTPUT
          
          # Detect infrastructure changes
          if git diff --name-only HEAD~1 | grep -q -E "(Deployment/|Infrastructure/|docker-compose)"; then
            echo "infrastructure=true" >> $GITHUB_OUTPUT
          else
            echo "infrastructure=false" >> $GITHUB_OUTPUT
          fi
          
          # Detect documentation changes
          if git diff --name-only HEAD~1 | grep -q "Documentation/"; then
            echo "documentation=true" >> $GITHUB_OUTPUT
          else
            echo "documentation=false" >> $GITHUB_OUTPUT
          fi

  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore

      - name: Build solution
        run: dotnet build --no-restore --configuration Release

      - name: Run code analysis
        run: |
          dotnet tool install --global dotnet-sonarscanner
          dotnet sonarscanner begin /k:"tli-shipper-portal" /o:"tli-org" /d:sonar.token="${{ secrets.SONAR_TOKEN }}" /d:sonar.host.url="https://sonarcloud.io"
          dotnet build --no-restore
          dotnet sonarscanner end /d:sonar.token="${{ secrets.SONAR_TOKEN }}"

      - name: Run security scan
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: 'security-scan-results.sarif'

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [
          'OrderManagement',
          'TripManagement', 
          'FinancialPayment',
          'NetworkFleetManagement',
          'AnalyticsBIService',
          'UserManagement',
          'CommunicationNotification',
          'DataStorage',
          'MobileWorkflow',
          'AuditCompliance'
        ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore Services/${{ matrix.service }}/

      - name: Run unit tests
        run: |
          dotnet test Services/${{ matrix.service }}/ \
            --configuration Release \
            --no-restore \
            --verbosity normal \
            --collect:"XPlat Code Coverage" \
            --results-directory ./coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/*/coverage.cobertura.xml
          flags: ${{ matrix.service }}
          name: ${{ matrix.service }}-coverage

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [detect-changes]
    if: needs.detect-changes.outputs.services != ''
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: tli_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

      rabbitmq:
        image: rabbitmq:3-management
        env:
          RABBITMQ_DEFAULT_USER: test
          RABBITMQ_DEFAULT_PASS: test
        ports:
          - 5672:5672
          - 15672:15672

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore

      - name: Run integration tests
        env:
          ConnectionStrings__DefaultConnection: "Host=localhost;Database=tli_test;Username=postgres;Password=test_password"
          ConnectionStrings__Redis: "localhost:6379"
          MessageBroker__ConnectionString: "amqp://test:test@localhost:5672/"
        run: |
          dotnet test Tests/Integration/ \
            --configuration Release \
            --verbosity normal \
            --collect:"XPlat Code Coverage" \
            --results-directory ./coverage

      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: ./coverage/

  # ===== BUILD AND PUSH DOCKER IMAGES =====
  
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [detect-changes, unit-tests]
    if: needs.detect-changes.outputs.services != '' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || startsWith(github.ref, 'refs/heads/release/'))
    strategy:
      matrix:
        service: [
          'api-gateway',
          'order-management',
          'trip-management',
          'financial-payment',
          'network-fleet-management',
          'analytics-bi-service',
          'user-management',
          'communication-notification',
          'data-storage',
          'mobile-workflow',
          'audit-compliance'
        ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_PREFIX }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Services/${{ matrix.service }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # ===== SECURITY SCANNING =====
  
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [build-images]
    if: always() && needs.build-images.result == 'success'
    strategy:
      matrix:
        service: [
          'api-gateway',
          'order-management',
          'trip-management',
          'financial-payment',
          'network-fleet-management',
          'analytics-bi-service',
          'user-management',
          'communication-notification',
          'data-storage',
          'mobile-workflow',
          'audit-compliance'
        ]
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME_PREFIX }}/${{ matrix.service }}:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-results-${{ matrix.service }}.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

  # ===== DEPLOYMENT JOBS =====
  
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [integration-tests, build-images, security-scan]
    if: github.ref == 'refs/heads/develop' && needs.build-images.result == 'success'
    environment:
      name: staging
      url: https://staging.tli.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name tli-staging-cluster

      - name: Deploy to staging
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|{{IMAGE_TAG}}|${{ github.sha }}|g" Deployment/k8s/staging/*.yaml
          
          # Apply database migrations
          kubectl apply -f Deployment/k8s/staging/migrations/
          kubectl wait --for=condition=complete job/database-migration --timeout=300s
          
          # Deploy services
          kubectl apply -f Deployment/k8s/staging/
          
          # Wait for rollout to complete
          kubectl rollout status deployment/api-gateway -n tli-staging
          kubectl rollout status deployment/order-management -n tli-staging
          kubectl rollout status deployment/trip-management -n tli-staging
          kubectl rollout status deployment/financial-payment -n tli-staging
          kubectl rollout status deployment/network-fleet-management -n tli-staging
          kubectl rollout status deployment/analytics-bi-service -n tli-staging

      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          sleep 60
          
          # Run smoke tests
          curl -f https://staging-api.tli.com/health || exit 1
          curl -f https://staging-api.tli.com/api/orders/health || exit 1
          curl -f https://staging-api.tli.com/api/trips/health || exit 1
          curl -f https://staging-api.tli.com/api/payments/health || exit 1

      - name: Notify deployment status
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-tests, build-images, security-scan]
    if: github.ref == 'refs/heads/main' && needs.build-images.result == 'success'
    environment:
      name: production
      url: https://api.tli.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name tli-production-cluster

      - name: Create deployment backup
        run: |
          # Backup current deployment
          kubectl get deployments -n tli-production -o yaml > deployment-backup-$(date +%Y%m%d-%H%M%S).yaml

      - name: Deploy to production
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|{{IMAGE_TAG}}|${{ github.sha }}|g" Deployment/k8s/production/*.yaml
          
          # Apply database migrations
          kubectl apply -f Deployment/k8s/production/migrations/
          kubectl wait --for=condition=complete job/database-migration --timeout=600s
          
          # Deploy services with rolling update
          kubectl apply -f Deployment/k8s/production/
          
          # Wait for rollout to complete
          kubectl rollout status deployment/api-gateway -n tli-production --timeout=600s
          kubectl rollout status deployment/order-management -n tli-production --timeout=600s
          kubectl rollout status deployment/trip-management -n tli-production --timeout=600s
          kubectl rollout status deployment/financial-payment -n tli-production --timeout=600s
          kubectl rollout status deployment/network-fleet-management -n tli-production --timeout=600s
          kubectl rollout status deployment/analytics-bi-service -n tli-production --timeout=600s

      - name: Run production health checks
        run: |
          # Wait for services to stabilize
          sleep 120
          
          # Comprehensive health checks
          curl -f https://api.tli.com/health || exit 1
          curl -f https://api.tli.com/api/orders/health || exit 1
          curl -f https://api.tli.com/api/trips/health || exit 1
          curl -f https://api.tli.com/api/payments/health || exit 1
          curl -f https://api.tli.com/api/network/health || exit 1
          curl -f https://api.tli.com/api/analytics/health || exit 1

      - name: Update deployment status
        run: |
          # Tag successful deployment
          git tag "production-$(date +%Y%m%d-%H%M%S)"
          git push origin --tags

      - name: Notify production deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#production-deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_PROD }}
          text: |
            🚀 Production deployment completed successfully!
            Version: ${{ github.sha }}
            Deployed by: ${{ github.actor }}
            Time: $(date)
        if: success()

      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment failed, initiating rollback..."
          kubectl rollout undo deployment/api-gateway -n tli-production
          kubectl rollout undo deployment/order-management -n tli-production
          kubectl rollout undo deployment/trip-management -n tli-production
          kubectl rollout undo deployment/financial-payment -n tli-production
          kubectl rollout undo deployment/network-fleet-management -n tli-production
          kubectl rollout undo deployment/analytics-bi-service -n tli-production
