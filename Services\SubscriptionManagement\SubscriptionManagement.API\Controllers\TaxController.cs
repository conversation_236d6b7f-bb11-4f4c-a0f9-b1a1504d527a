using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Queries.CalculateTaxForSubscription;
using SubscriptionManagement.Application.Queries.GetPlanTaxDetails;
using System.ComponentModel.DataAnnotations;

namespace SubscriptionManagement.API.Controllers
{
    [Route("api/tax")]
    [ApiController]
    public class TaxController : BaseController
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<TaxController> _logger;

        public TaxController(IMemoryCache cache, ILogger<TaxController> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Get tax details for a specific plan
        /// </summary>
        [HttpGet("plans/{planId}/details")]
        [ProducesResponseType(typeof(PlanTaxDetailsDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
        public async Task<IActionResult> GetPlanTaxDetails(
            Guid planId,
            [FromQuery, Required] string region,
            [FromQuery] Guid? customerId = null,
            [FromQuery] DateTime? calculationDate = null)
        {
            try
            {
                // Validate region
                if (string.IsNullOrWhiteSpace(region) || region.Length < 2 || region.Length > 10)
                {
                    return BadRequest("Invalid region code. Must be between 2 and 10 characters.");
                }

                // Create cache key
                var cacheKey = $"plan_tax_details_{planId}_{region}_{customerId}_{calculationDate?.ToString("yyyyMMdd")}";
                
                // Try to get from cache first
                if (_cache.TryGetValue(cacheKey, out PlanTaxDetailsDto? cachedResult))
                {
                    _logger.LogDebug("Returning cached tax details for plan {PlanId}", planId);
                    return Ok(cachedResult);
                }

                var query = new GetPlanTaxDetailsQuery
                {
                    PlanId = planId,
                    CustomerRegion = region.ToUpperInvariant(),
                    CustomerId = customerId,
                    CalculationDate = calculationDate
                };

                var result = await Mediator.Send(query);
                
                if (result == null)
                {
                    return NotFound($"Plan with ID {planId} not found or not active");
                }

                // Cache the result for 5 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                    SlidingExpiration = TimeSpan.FromMinutes(2),
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, result, cacheOptions);

                _logger.LogInformation("Retrieved tax details for plan {PlanId} in region {Region}", planId, region);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Calculate tax for a subscription
        /// </summary>
        [HttpPost("calculate")]
        [ProducesResponseType(typeof(TaxCalculationResponseDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
        public async Task<IActionResult> CalculateTax([FromBody] TaxCalculationRequestDto request)
        {
            try
            {
                // Validate request
                if (request.PlanId == Guid.Empty)
                {
                    return BadRequest("Plan ID is required");
                }

                if (string.IsNullOrWhiteSpace(request.CustomerRegion) || 
                    request.CustomerRegion.Length < 2 || 
                    request.CustomerRegion.Length > 10)
                {
                    return BadRequest("Invalid region code. Must be between 2 and 10 characters.");
                }

                // Create cache key
                var cacheKey = $"tax_calculation_{request.PlanId}_{request.CustomerRegion}_{request.CustomerId}_{request.CalculationDate?.ToString("yyyyMMdd")}";
                
                // Try to get from cache first
                if (_cache.TryGetValue(cacheKey, out TaxCalculationResponseDto? cachedResult))
                {
                    _logger.LogDebug("Returning cached tax calculation for plan {PlanId}", request.PlanId);
                    return Ok(cachedResult);
                }

                var query = new CalculateTaxForSubscriptionQuery
                {
                    PlanId = request.PlanId,
                    CustomerRegion = request.CustomerRegion.ToUpperInvariant(),
                    CustomerId = request.CustomerId,
                    CalculationDate = request.CalculationDate,
                    IncludeTaxBreakdown = request.IncludeTaxBreakdown
                };

                var result = await Mediator.Send(query);

                // Cache the result for 5 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                    SlidingExpiration = TimeSpan.FromMinutes(2),
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, result, cacheOptions);

                _logger.LogInformation("Calculated tax for plan {PlanId} in region {Region}. Total tax: {TotalTax} {Currency}",
                    request.PlanId, request.CustomerRegion, result.TotalTaxAmount, result.Currency);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get effective tax rate for a region
        /// </summary>
        [HttpGet("rates")]
        [ProducesResponseType(typeof(EffectiveTaxRateDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
        public async Task<IActionResult> GetEffectiveTaxRate(
            [FromQuery, Required] string region,
            [FromQuery] string? taxType = null,
            [FromQuery] DateTime? effectiveDate = null)
        {
            try
            {
                // Validate region
                if (string.IsNullOrWhiteSpace(region) || region.Length < 2 || region.Length > 10)
                {
                    return BadRequest("Invalid region code. Must be between 2 and 10 characters.");
                }

                // Create cache key
                var cacheKey = $"effective_tax_rate_{region}_{taxType}_{effectiveDate?.ToString("yyyyMMdd")}";
                
                // Try to get from cache first
                if (_cache.TryGetValue(cacheKey, out EffectiveTaxRateDto? cachedResult))
                {
                    _logger.LogDebug("Returning cached effective tax rate for region {Region}", region);
                    return Ok(cachedResult);
                }

                // This would need a specific query implementation
                var result = new EffectiveTaxRateDto
                {
                    Region = region.ToUpperInvariant(),
                    TotalRate = 0, // Would be calculated from actual tax configurations
                    TaxBreakdown = new List<TaxRateItemDto>(),
                    EffectiveDate = effectiveDate ?? DateTime.UtcNow,
                    IsTaxInclusive = false
                };

                // Cache the result for 10 minutes
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10),
                    SlidingExpiration = TimeSpan.FromMinutes(5),
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, result, cacheOptions);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Check if customer has tax exemptions
        /// </summary>
        [HttpGet("exemptions/check")]
        [Authorize]
        [ProducesResponseType(typeof(TaxExemptionCheckDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
        public async Task<IActionResult> CheckTaxExemption(
            [FromQuery, Required] string region,
            [FromQuery] string? taxType = null,
            [FromQuery] DateTime? checkDate = null)
        {
            try
            {
                var customerId = GetCurrentUserId();
                
                // Validate region
                if (string.IsNullOrWhiteSpace(region) || region.Length < 2 || region.Length > 10)
                {
                    return BadRequest("Invalid region code. Must be between 2 and 10 characters.");
                }

                // Create cache key
                var cacheKey = $"tax_exemption_check_{customerId}_{region}_{taxType}_{checkDate?.ToString("yyyyMMdd")}";
                
                // Try to get from cache first
                if (_cache.TryGetValue(cacheKey, out TaxExemptionCheckDto? cachedResult))
                {
                    _logger.LogDebug("Returning cached tax exemption check for customer {CustomerId}", customerId);
                    return Ok(cachedResult);
                }

                // This would need a specific query implementation
                var result = new TaxExemptionCheckDto
                {
                    CustomerId = customerId,
                    Region = region.ToUpperInvariant(),
                    HasExemptions = false,
                    ExemptTaxTypes = new List<string>(),
                    CheckedAt = checkDate ?? DateTime.UtcNow
                };

                // Cache the result for 2 minutes (shorter cache for user-specific data)
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(2),
                    SlidingExpiration = TimeSpan.FromMinutes(1),
                    Priority = CacheItemPriority.Normal
                };
                
                _cache.Set(cacheKey, result, cacheOptions);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get supported regions for tax calculation
        /// </summary>
        [HttpGet("regions")]
        [ProducesResponseType(typeof(List<TaxRegionDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
        public async Task<IActionResult> GetSupportedRegions()
        {
            try
            {
                const string cacheKey = "supported_tax_regions";
                
                // Try to get from cache first
                if (_cache.TryGetValue(cacheKey, out List<TaxRegionDto>? cachedResult))
                {
                    _logger.LogDebug("Returning cached supported tax regions");
                    return Ok(cachedResult);
                }

                // This would typically come from configuration or database
                var result = new List<TaxRegionDto>
                {
                    new() { Code = "IN", Name = "India", SupportedTaxTypes = new[] { "GST", "TDS", "CGST", "SGST", "IGST", "Cess" } },
                    new() { Code = "US", Name = "United States", SupportedTaxTypes = new[] { "SalesTax" } },
                    new() { Code = "GB", Name = "United Kingdom", SupportedTaxTypes = new[] { "VAT" } },
                    new() { Code = "CA", Name = "Canada", SupportedTaxTypes = new[] { "GST", "HST" } }
                };

                // Cache the result for 1 hour
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1),
                    Priority = CacheItemPriority.High
                };
                
                _cache.Set(cacheKey, result, cacheOptions);

                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        private IActionResult HandleException(Exception ex)
        {
            _logger.LogError(ex, "Error in TaxController");

            return ex switch
            {
                ArgumentException => BadRequest(ex.Message),
                InvalidOperationException => BadRequest(ex.Message),
                UnauthorizedAccessException => Unauthorized(),
                _ => StatusCode(500, "An error occurred while processing the request")
            };
        }
    }

    // Additional DTOs for public endpoints
    public class EffectiveTaxRateDto
    {
        public string Region { get; set; } = string.Empty;
        public decimal TotalRate { get; set; }
        public List<TaxRateItemDto> TaxBreakdown { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public bool IsTaxInclusive { get; set; }
    }

    public class TaxRateItemDto
    {
        public string TaxType { get; set; } = string.Empty;
        public string TaxName { get; set; } = string.Empty;
        public decimal Rate { get; set; }
    }

    public class TaxExemptionCheckDto
    {
        public Guid CustomerId { get; set; }
        public string Region { get; set; } = string.Empty;
        public bool HasExemptions { get; set; }
        public List<string> ExemptTaxTypes { get; set; } = new();
        public DateTime CheckedAt { get; set; }
    }

    public class TaxRegionDto
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string[] SupportedTaxTypes { get; set; } = Array.Empty<string>();
    }
}
