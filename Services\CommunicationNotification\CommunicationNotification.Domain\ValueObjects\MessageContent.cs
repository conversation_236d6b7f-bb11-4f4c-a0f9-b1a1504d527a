﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

public class MessageContent : ValueObject
{
    public string Subject { get; private set; }
    public string Body { get; private set; }
    public Language Language { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    private MessageContent()
    {
        Subject = string.Empty;
        Body = string.Empty;
        Metadata = new Dictionary<string, string>();
    }

    public MessageContent(string subject, string body, Language language, Dictionary<string, string>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(body))
            throw new ArgumentException("Message body cannot be empty", nameof(body));

        Subject = subject ?? string.Empty;
        Body = body;
        Language = language;
        Metadata = metadata ?? new Dictionary<string, string>();
    }

    public static MessageContent Create(string subject, string body, Language language, Dictionary<string, string>? metadata = null)
    {
        return new MessageContent(subject, body, language, metadata);
    }

    public static MessageContent Empty => new MessageContent("", " ", Language.English);

    public MessageContent WithSubject(string subject)
    {
        return new MessageContent(subject, Body, Language, Metadata);
    }

    public MessageContent WithBody(string body)
    {
        return new MessageContent(Subject, body, Language, Metadata);
    }

    public MessageContent WithLanguage(Language language)
    {
        return new MessageContent(Subject, Body, language, Metadata);
    }

    public MessageContent WithMetadata(Dictionary<string, string> metadata)
    {
        return new MessageContent(Subject, Body, Language, metadata);
    }

    public MessageContent AddMetadata(string key, string value)
    {
        var newMetadata = new Dictionary<string, string>(Metadata)
        {
            [key] = value
        };
        return new MessageContent(Subject, Body, Language, newMetadata);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Subject;
        yield return Body;
        yield return Language;

        foreach (var kvp in Metadata.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }

    public override string ToString()
    {
        return $"{Subject}: {Body} ({Language.ToDisplayName()})";
    }
}


