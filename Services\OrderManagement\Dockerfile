# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/OrderManagement/OrderManagement.API/OrderManagement.API.csproj", "Services/OrderManagement/OrderManagement.API/"]
COPY ["Services/OrderManagement/OrderManagement.Application/OrderManagement.Application.csproj", "Services/OrderManagement/OrderManagement.Application/"]
COPY ["Services/OrderManagement/OrderManagement.Domain/OrderManagement.Domain.csproj", "Services/OrderManagement/OrderManagement.Domain/"]
COPY ["Services/OrderManagement/OrderManagement.Infrastructure/OrderManagement.Infrastructure.csproj", "Services/OrderManagement/OrderManagement.Infrastructure/"]
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/OrderManagement/OrderManagement.API/OrderManagement.API.csproj"

# Copy all source code
COPY . .

# Build the application
WORKDIR "/src/Services/OrderManagement/OrderManagement.API"
RUN dotnet build "OrderManagement.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "OrderManagement.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

ENTRYPOINT ["dotnet", "OrderManagement.API.dll"]
