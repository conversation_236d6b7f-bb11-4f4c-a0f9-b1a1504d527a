using UserManagement.Domain.Entities;

namespace UserManagement.Application.Services;

/// <summary>
/// Service for advanced feedback analytics and insights
/// </summary>
public interface IFeedbackAnalyticsEngine
{
    /// <summary>
    /// Calculates trend data for feedback over time
    /// </summary>
    /// <param name="feedbacks">Feedback data</param>
    /// <param name="fromDate">Start date</param>
    /// <param name="toDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Trend analysis data</returns>
    Task<FeedbackTrendData> CalculateTrendsAsync(IEnumerable<Feedback> feedbacks, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Identifies top issues from feedback
    /// </summary>
    /// <param name="feedbacks">Feedback data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Top issues identified</returns>
    Task<IEnumerable<FeedbackIssue>> IdentifyTopIssuesAsync(IEnumerable<Feedback> feedbacks, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates improvement suggestions based on feedback
    /// </summary>
    /// <param name="feedbacks">Feedback data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Improvement suggestions</returns>
    Task<IEnumerable<ImprovementSuggestion>> GenerateImprovementSuggestionsAsync(IEnumerable<Feedback> feedbacks, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes trends based on query parameters
    /// </summary>
    /// <param name="feedbacks">Feedback data</param>
    /// <param name="query">Trend analysis query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Trend analysis result</returns>
    Task<FeedbackTrendAnalysis> AnalyzeTrendsAsync(IEnumerable<Feedback> feedbacks, FeedbackTrendQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates insights based on query
    /// </summary>
    /// <param name="query">Insights query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated insights</returns>
    Task<FeedbackInsights> GenerateInsightsAsync(FeedbackInsightsQuery query, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents feedback trend data
/// </summary>
public class FeedbackTrendData
{
    public Dictionary<DateTime, int> DailyFeedbackCount { get; set; } = new();
    public Dictionary<DateTime, double> DailyAverageRating { get; set; } = new();
    public Dictionary<string, int> CategoryTrends { get; set; } = new();
    public double OverallTrend { get; set; } // Positive/negative trend indicator
}

/// <summary>
/// Represents a feedback issue
/// </summary>
public class FeedbackIssue
{
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Frequency { get; set; }
    public double ImpactScore { get; set; }
    public List<string> RelatedFeedbacks { get; set; } = new();
}

/// <summary>
/// Represents an improvement suggestion
/// </summary>
public class ImprovementSuggestion
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int Priority { get; set; } // 1-5 scale
    public double PotentialImpact { get; set; }
    public List<string> SupportingEvidence { get; set; } = new();
}

/// <summary>
/// Query for feedback trend analysis
/// </summary>
public class FeedbackTrendQuery
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? Category { get; set; }
    public Guid? UserId { get; set; }
    public string? GroupBy { get; set; } // daily, weekly, monthly
}

/// <summary>
/// Result of feedback trend analysis
/// </summary>
public class FeedbackTrendAnalysis
{
    public FeedbackTrendData TrendData { get; set; } = new();
    public List<FeedbackIssue> TopIssues { get; set; } = new();
    public List<ImprovementSuggestion> Suggestions { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Query for feedback insights
/// </summary>
public class FeedbackInsightsQuery
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? Category { get; set; }
    public Guid? UserId { get; set; }
    public List<string> Metrics { get; set; } = new();
}

/// <summary>
/// Generated feedback insights
/// </summary>
public class FeedbackInsights
{
    public Dictionary<string, object> Metrics { get; set; } = new();
    public List<string> KeyFindings { get; set; } = new();
    public List<ImprovementSuggestion> Recommendations { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}
