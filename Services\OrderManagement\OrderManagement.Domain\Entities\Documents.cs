using OrderManagement.Domain.Primitives;
using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

public abstract class BaseDocument : Entity
{
    public string FileName { get; protected set; }
    public string FilePath { get; protected set; }
    public string ContentType { get; protected set; }
    public long FileSize { get; protected set; }
    public DocumentType DocumentType { get; protected set; }
    public string? Description { get; protected set; }
    public DateTime UploadedAt { get; protected set; }
    public Guid UploadedBy { get; protected set; }

    protected BaseDocument() { } // EF Constructor

    protected BaseDocument(
        string fileName,
        string filePath,
        string contentType,
        long fileSize,
        DocumentType documentType,
        Guid uploadedBy,
        string? description = null)
    {
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        ContentType = contentType ?? throw new ArgumentNullException(nameof(contentType));
        FileSize = fileSize;
        DocumentType = documentType;
        UploadedBy = uploadedBy;
        Description = description;
        UploadedAt = DateTime.UtcNow;
    }
}

public class RfqDocument : BaseDocument
{
    public Guid RfqId { get; private set; }

    // Navigation properties
    public RFQ Rfq { get; private set; } = null!;

    private RfqDocument() { } // EF Constructor

    public RfqDocument(
        Guid rfqId,
        string fileName,
        string filePath,
        string contentType,
        long fileSize,
        DocumentType documentType,
        Guid uploadedBy,
        string? description = null)
        : base(fileName, filePath, contentType, fileSize, documentType, uploadedBy, description)
    {
        RfqId = rfqId;
    }
}

public class BidDocument : BaseDocument
{
    public Guid BidId { get; private set; }

    // Navigation properties
    public RfqBid Bid { get; private set; } = null!;

    private BidDocument() { } // EF Constructor

    public BidDocument(
        Guid bidId,
        string fileName,
        string filePath,
        string contentType,
        long fileSize,
        DocumentType documentType,
        Guid uploadedBy,
        string? description = null)
        : base(fileName, filePath, contentType, fileSize, documentType, uploadedBy, description)
    {
        BidId = bidId;
    }
}

public class OrderDocument : BaseDocument
{
    public Guid OrderId { get; private set; }

    // Navigation properties
    public Order Order { get; private set; } = null!;

    private OrderDocument() { } // EF Constructor

    public OrderDocument(
        Guid orderId,
        string fileName,
        string filePath,
        string contentType,
        long fileSize,
        DocumentType documentType,
        Guid uploadedBy,
        string? description = null)
        : base(fileName, filePath, contentType, fileSize, documentType, uploadedBy, description)
    {
        OrderId = orderId;
    }
}

public class OrderStatusHistory : Entity
{
    public Guid OrderId { get; private set; }
    public OrderStatus Status { get; private set; }
    public string Notes { get; private set; }
    public DateTime ChangedAt { get; private set; }
    public Guid? ChangedBy { get; private set; }

    // Navigation properties
    public Order Order { get; private set; } = null!;

    private OrderStatusHistory() { } // EF Constructor

    public OrderStatusHistory(
        Guid orderId,
        OrderStatus status,
        string notes,
        DateTime changedAt,
        Guid? changedBy = null)
    {
        OrderId = orderId;
        Status = status;
        Notes = notes ?? throw new ArgumentNullException(nameof(notes));
        ChangedAt = changedAt;
        ChangedBy = changedBy;
    }
}


