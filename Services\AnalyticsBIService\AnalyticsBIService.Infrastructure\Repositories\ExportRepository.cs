using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for export operations
/// </summary>
public class ExportRepository : OptimizedRepositoryBase<ExportData, Guid>, IExportRepository
{
    public ExportRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics) : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get export data by user ID
    /// </summary>
    public async Task<IEnumerable<ExportData>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ExportData>()
            .Where(ed => ed.UserId == userId)
            .OrderByDescending(ed => ed.RequestedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get export data by status
    /// </summary>
    public async Task<IEnumerable<ExportData>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ExportData>()
            .Where(ed => ed.Status == status)
            .OrderByDescending(ed => ed.RequestedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get export data by date range
    /// </summary>
    public async Task<IEnumerable<ExportData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ExportData>()
            .Where(ed => ed.RequestedAt >= startDate && ed.RequestedAt <= endDate)
            .OrderByDescending(ed => ed.RequestedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get recent exports
    /// </summary>
    public async Task<IEnumerable<ExportData>> GetRecentExportsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ExportData>()
            .OrderByDescending(ed => ed.RequestedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Clean up old export files
    /// </summary>
    public async Task CleanupOldExportsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldExports = await Context.Set<ExportData>()
            .Where(ed => ed.RequestedAt < cutoffDate || (ed.ExpiresAt.HasValue && ed.ExpiresAt < DateTime.UtcNow))
            .ToListAsync(cancellationToken);

        Context.Set<ExportData>().RemoveRange(oldExports);
        await Context.SaveChangesAsync(cancellationToken);
    }
}
