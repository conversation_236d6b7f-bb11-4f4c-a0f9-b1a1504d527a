using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a span within a distributed trace
/// </summary>
public class Span
{
    private readonly List<SpanEvent> _events = new();
    private readonly List<SpanLog> _logs = new();

    public Span(string traceId, string spanId, string operationName, string serviceName,
        string? parentSpanId = null, Dictionary<string, string>? tags = null)
    {
        Id = Guid.NewGuid();
        TraceId = traceId ?? throw new ArgumentNullException(nameof(traceId));
        SpanId = spanId ?? throw new ArgumentNullException(nameof(spanId));
        OperationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
        ServiceName = serviceName ?? throw new ArgumentNullException(nameof(serviceName));
        ParentSpanId = parentSpanId;
        Tags = tags ?? new Dictionary<string, string>();
        StartTime = DateTime.UtcNow;
        IsActive = true;
    }

    // Required for EF Core
    private Span() { }

    public Guid Id { get; private set; }
    public string TraceId { get; private set; } = string.Empty;
    public string SpanId { get; private set; } = string.Empty;
    public string? ParentSpanId { get; private set; }
    public string OperationName { get; private set; } = string.Empty;
    public string ServiceName { get; private set; } = string.Empty;
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public TimeSpan? Duration => EndTime?.Subtract(StartTime);
    public bool IsActive { get; private set; }
    public bool HasError { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorType { get; private set; }
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public Trace Trace { get; private set; } = null!;
    public IReadOnlyList<SpanEvent> Events => _events.AsReadOnly();
    public IReadOnlyList<SpanLog> Logs => _logs.AsReadOnly();

    // Computed properties
    public bool IsRoot => string.IsNullOrEmpty(ParentSpanId);
    public int Depth => CalculateDepth();
    public string Component => Tags.GetValueOrDefault("component", "unknown");
    public string HttpMethod => Tags.GetValueOrDefault("http.method", string.Empty);
    public string HttpUrl => Tags.GetValueOrDefault("http.url", string.Empty);
    public string HttpStatusCode => Tags.GetValueOrDefault("http.status_code", string.Empty);
    public string DatabaseType => Tags.GetValueOrDefault("db.type", string.Empty);
    public string DatabaseStatement => Tags.GetValueOrDefault("db.statement", string.Empty);

    public void EndSpan(bool hasError = false, string? errorMessage = null, string? errorType = null)
    {
        if (!IsActive) throw new InvalidOperationException("Span is already ended");

        EndTime = DateTime.UtcNow;
        IsActive = false;
        HasError = hasError;
        ErrorMessage = errorMessage;
        ErrorType = errorType;

        // Add span ended event
        AddEvent(new SpanEvent(SpanId, "span.ended", 
            hasError ? $"Span ended with error: {errorMessage}" : "Span completed successfully",
            new Dictionary<string, object>
            {
                ["duration_ms"] = Duration?.TotalMilliseconds ?? 0,
                ["has_error"] = hasError,
                ["operation"] = OperationName,
                ["service"] = ServiceName
            }));
    }

    public void AddEvent(SpanEvent spanEvent)
    {
        if (spanEvent == null) throw new ArgumentNullException(nameof(spanEvent));
        _events.Add(spanEvent);
    }

    public void AddLog(SpanLog spanLog)
    {
        if (spanLog == null) throw new ArgumentNullException(nameof(spanLog));
        _logs.Add(spanLog);
    }

    public void AddTag(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Tag key cannot be null or empty", nameof(key));
        Tags[key] = value ?? string.Empty;
    }

    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Metadata key cannot be null or empty", nameof(key));
        Metadata[key] = value;
    }

    public void SetError(string errorMessage, string? errorType = null, Dictionary<string, object>? errorMetadata = null)
    {
        HasError = true;
        ErrorMessage = errorMessage;
        ErrorType = errorType;

        // Add error tags
        Tags["error"] = "true";
        Tags["error.message"] = errorMessage;
        if (!string.IsNullOrEmpty(errorType))
        {
            Tags["error.type"] = errorType;
        }

        // Add error metadata
        if (errorMetadata != null)
        {
            foreach (var kvp in errorMetadata)
            {
                Metadata[$"error.{kvp.Key}"] = kvp.Value;
            }
        }

        // Add error event
        AddEvent(new SpanEvent(SpanId, "error", errorMessage, errorMetadata ?? new Dictionary<string, object>()));
    }

    public void SetHttpInfo(string method, string url, int? statusCode = null, long? requestSize = null, long? responseSize = null)
    {
        AddTag("http.method", method);
        AddTag("http.url", url);
        
        if (statusCode.HasValue)
        {
            AddTag("http.status_code", statusCode.Value.ToString());
            
            // Mark as error for 4xx and 5xx status codes
            if (statusCode >= 400)
            {
                SetError($"HTTP {statusCode}", "http_error");
            }
        }

        if (requestSize.HasValue)
        {
            AddTag("http.request_size", requestSize.Value.ToString());
        }

        if (responseSize.HasValue)
        {
            AddTag("http.response_size", responseSize.Value.ToString());
        }
    }

    public void SetDatabaseInfo(string dbType, string? statement = null, string? instance = null, string? user = null)
    {
        AddTag("db.type", dbType);
        
        if (!string.IsNullOrEmpty(statement))
        {
            AddTag("db.statement", statement);
        }

        if (!string.IsNullOrEmpty(instance))
        {
            AddTag("db.instance", instance);
        }

        if (!string.IsNullOrEmpty(user))
        {
            AddTag("db.user", user);
        }
    }

    public void SetMessageQueueInfo(string destination, string? operation = null, string? messageId = null)
    {
        AddTag("message_bus.destination", destination);
        
        if (!string.IsNullOrEmpty(operation))
        {
            AddTag("message_bus.operation", operation);
        }

        if (!string.IsNullOrEmpty(messageId))
        {
            AddTag("message_bus.message_id", messageId);
        }
    }

    private int CalculateDepth()
    {
        // This would need to be calculated based on the trace hierarchy
        // For now, return 0 for root spans, 1 for others
        return IsRoot ? 0 : 1;
    }
}
