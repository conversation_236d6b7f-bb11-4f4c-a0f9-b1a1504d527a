using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.Queries.Vehicles;

public record GetVehiclesByCarrierWithDetailsQuery(
    Guid CarrierId,
    int PageNumber = 1,
    int PageSize = 10,
    VehicleStatus? Status = null,
    VehicleType? VehicleType = null,
    bool? HasExpiredDocuments = null,
    bool? RequiresMaintenance = null,
    bool? IsAssigned = null,
    string? SearchTerm = null) : IRequest<PagedResult<VehicleDashboardDto>>;
