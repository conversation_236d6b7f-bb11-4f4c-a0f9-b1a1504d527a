using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Services;

public class AuditService : IAuditService
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<AuditService> _logger;

    public AuditService(
        IAuditLogRepository auditLogRepository,
        ILogger<AuditService> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<Guid> LogEventAsync(CreateAuditLogDto auditLogDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var context = AuditContext.Create(
                auditLogDto.IpAddress,
                auditLogDto.UserAgent,
                auditLogDto.SessionId,
                auditLogDto.CorrelationId);

            var auditLog = new AuditLog(
                auditLogDto.EventType,
                auditLogDto.Severity,
                auditLogDto.EntityType,
                auditLogDto.Action,
                auditLogDto.Description,
                auditLogDto.UserId,
                auditLogDto.UserName,
                auditLogDto.UserRole,
                auditLogDto.EntityId,
                auditLogDto.OldValues,
                auditLogDto.NewValues,
                context,
                complianceFlags: auditLogDto.ComplianceFlags);

            var savedAuditLog = await _auditLogRepository.AddAsync(auditLog, cancellationToken);

            _logger.LogInformation("Audit event logged: {EventType} for entity: {EntityType}", 
                auditLogDto.EventType, auditLogDto.EntityType);

            return savedAuditLog.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging audit event: {EventType}", auditLogDto.EventType);
            throw;
        }
    }

    public async Task<AuditTrailResultDto> GetAuditTrailAsync(AuditTrailQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var auditLogs = await _auditLogRepository.GetAuditTrailAsync(
                query.UserId,
                query.EntityType,
                query.EntityId,
                query.EventType,
                query.MinSeverity,
                query.FromDate,
                query.ToDate,
                query.SearchTerm,
                query.PageNumber,
                query.PageSize,
                cancellationToken);

            var totalCount = await _auditLogRepository.GetAuditTrailCountAsync(
                query.UserId,
                query.EntityType,
                query.EntityId,
                query.EventType,
                query.MinSeverity,
                query.FromDate,
                query.ToDate,
                query.SearchTerm,
                cancellationToken);

            var auditLogDtos = auditLogs.Select(al => new AuditLogDto
            {
                Id = al.Id,
                EventType = al.EventType,
                Severity = al.Severity,
                UserId = al.UserId,
                UserName = al.UserName,
                UserRole = al.UserRole,
                EntityType = al.EntityType,
                EntityId = al.EntityId,
                Action = al.Action,
                Description = al.Description,
                OldValues = query.IncludeSensitiveData ? al.OldValues : "[REDACTED]",
                NewValues = query.IncludeSensitiveData ? al.NewValues : "[REDACTED]",
                IpAddress = al.Context.IpAddress,
                UserAgent = al.Context.UserAgent,
                SessionId = al.Context.SessionId,
                CorrelationId = al.Context.CorrelationId,
                EventTimestamp = al.EventTimestamp,
                CreatedAt = al.CreatedAt,
                ComplianceFlags = al.ComplianceFlags
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);

            return new AuditTrailResultDto
            {
                AuditLogs = auditLogDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasNextPage = query.PageNumber < totalPages,
                HasPreviousPage = query.PageNumber > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail");
            throw;
        }
    }

    public async Task<List<AuditLogDto>> GetSecurityEventsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var securityEvents = await _auditLogRepository.GetSecurityEventsAsync(fromDate, toDate, cancellationToken);

            return securityEvents.Select(se => new AuditLogDto
            {
                Id = se.Id,
                EventType = se.EventType,
                Severity = se.Severity,
                UserId = se.UserId,
                UserName = se.UserName,
                UserRole = se.UserRole,
                EntityType = se.EntityType,
                EntityId = se.EntityId,
                Action = se.Action,
                Description = se.Description,
                IpAddress = se.Context.IpAddress,
                UserAgent = se.Context.UserAgent,
                SessionId = se.Context.SessionId,
                CorrelationId = se.Context.CorrelationId,
                EventTimestamp = se.EventTimestamp,
                CreatedAt = se.CreatedAt,
                ComplianceFlags = se.ComplianceFlags
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events from {FromDate} to {ToDate}", fromDate, toDate);
            throw;
        }
    }

    public async Task<int> CleanupExpiredAuditLogsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var expiredLogs = await _auditLogRepository.GetExpiredAuditLogsAsync(cancellationToken);
            
            if (expiredLogs.Any())
            {
                await _auditLogRepository.DeleteRangeAsync(expiredLogs, cancellationToken);
                _logger.LogInformation("Cleaned up {Count} expired audit logs", expiredLogs.Count);
            }

            return expiredLogs.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired audit logs");
            throw;
        }
    }
}
