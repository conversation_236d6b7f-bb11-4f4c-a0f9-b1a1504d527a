using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetInvoices;

public class GetInvoicesQuery : IRequest<PagedResult<InvoiceSummaryDto>>
{
    public Guid? OrderId { get; set; }
    public InvoiceStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public DateTime? DueBefore { get; set; } // For overdue invoices
    public string? SearchTerm { get; set; } // Search in invoice number, title
    public bool? OverdueOnly { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
    public Guid? UserId { get; set; } // For authorization
}
