using FluentValidation;
using NetworkFleetManagement.Application.Commands.VehicleTypeMaster;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Validators;

/// <summary>
/// Validator for CreateVehicleTypeMasterCommand
/// </summary>
public class CreateVehicleTypeMasterCommandValidator : AbstractValidator<CreateVehicleTypeMasterCommand>
{
    public CreateVehicleTypeMasterCommandValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Vehicle type code is required")
            .MaximumLength(20).WithMessage("Vehicle type code cannot exceed 20 characters")
            .Matches("^[A-Z0-9_-]+$").WithMessage("Vehicle type code can only contain uppercase letters, numbers, underscores, and hyphens");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Vehicle type name is required")
            .MaximumLength(100).WithMessage("Vehicle type name cannot exceed 100 characters");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters");

        RuleFor(x => x.Category)
            .NotEmpty().WithMessage("Category is required")
            .MaximumLength(50).WithMessage("Category cannot exceed 50 characters");

        RuleFor(x => x.MinLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MinLoadCapacityKg.HasValue)
            .WithMessage("Minimum load capacity must be non-negative");

        RuleFor(x => x.MaxLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinLoadCapacityKg).When(x => x.MinLoadCapacityKg.HasValue && x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be greater than or equal to minimum load capacity");

        RuleFor(x => x.MinVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MinVolumeCapacityM3.HasValue)
            .WithMessage("Minimum volume capacity must be non-negative");

        RuleFor(x => x.MaxVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinVolumeCapacityM3).When(x => x.MinVolumeCapacityM3.HasValue && x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be greater than or equal to minimum volume capacity");

        RuleFor(x => x.SpecialRequirements)
            .MaximumLength(1000).WithMessage("Special requirements cannot exceed 1000 characters");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.IconUrl)
            .MaximumLength(500).WithMessage("Icon URL cannot exceed 500 characters")
            .Must(BeValidUrl).When(x => !string.IsNullOrWhiteSpace(x.IconUrl))
            .WithMessage("Icon URL must be a valid URL");
    }

    private static bool BeValidUrl(string? url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out _);
    }
}

/// <summary>
/// Validator for UpdateVehicleTypeMasterCommand
/// </summary>
public class UpdateVehicleTypeMasterCommandValidator : AbstractValidator<UpdateVehicleTypeMasterCommand>
{
    public UpdateVehicleTypeMasterCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Vehicle type master ID is required");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Vehicle type name is required")
            .MaximumLength(100).WithMessage("Vehicle type name cannot exceed 100 characters");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters");

        RuleFor(x => x.Category)
            .NotEmpty().WithMessage("Category is required")
            .MaximumLength(50).WithMessage("Category cannot exceed 50 characters");

        RuleFor(x => x.MinLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MinLoadCapacityKg.HasValue)
            .WithMessage("Minimum load capacity must be non-negative");

        RuleFor(x => x.MaxLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinLoadCapacityKg).When(x => x.MinLoadCapacityKg.HasValue && x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be greater than or equal to minimum load capacity");

        RuleFor(x => x.MinVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MinVolumeCapacityM3.HasValue)
            .WithMessage("Minimum volume capacity must be non-negative");

        RuleFor(x => x.MaxVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinVolumeCapacityM3).When(x => x.MinVolumeCapacityM3.HasValue && x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be greater than or equal to minimum volume capacity");

        RuleFor(x => x.SpecialRequirements)
            .MaximumLength(1000).WithMessage("Special requirements cannot exceed 1000 characters");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.IconUrl)
            .MaximumLength(500).WithMessage("Icon URL cannot exceed 500 characters")
            .Must(BeValidUrl).When(x => !string.IsNullOrWhiteSpace(x.IconUrl))
            .WithMessage("Icon URL must be a valid URL");
    }

    private static bool BeValidUrl(string? url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out _);
    }
}

/// <summary>
/// Validator for CreateVehicleTypeMasterDto
/// </summary>
public class CreateVehicleTypeMasterDtoValidator : AbstractValidator<CreateVehicleTypeMasterDto>
{
    public CreateVehicleTypeMasterDtoValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Vehicle type code is required")
            .MaximumLength(20).WithMessage("Vehicle type code cannot exceed 20 characters")
            .Matches("^[A-Z0-9_-]+$").WithMessage("Vehicle type code can only contain uppercase letters, numbers, underscores, and hyphens");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Vehicle type name is required")
            .MaximumLength(100).WithMessage("Vehicle type name cannot exceed 100 characters");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters");

        RuleFor(x => x.Category)
            .NotEmpty().WithMessage("Category is required")
            .MaximumLength(50).WithMessage("Category cannot exceed 50 characters");

        RuleFor(x => x.MinLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MinLoadCapacityKg.HasValue)
            .WithMessage("Minimum load capacity must be non-negative");

        RuleFor(x => x.MaxLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinLoadCapacityKg).When(x => x.MinLoadCapacityKg.HasValue && x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be greater than or equal to minimum load capacity");

        RuleFor(x => x.MinVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MinVolumeCapacityM3.HasValue)
            .WithMessage("Minimum volume capacity must be non-negative");

        RuleFor(x => x.MaxVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinVolumeCapacityM3).When(x => x.MinVolumeCapacityM3.HasValue && x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be greater than or equal to minimum volume capacity");

        RuleFor(x => x.SpecialRequirements)
            .MaximumLength(1000).WithMessage("Special requirements cannot exceed 1000 characters");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.IconUrl)
            .MaximumLength(500).WithMessage("Icon URL cannot exceed 500 characters")
            .Must(BeValidUrl).When(x => !string.IsNullOrWhiteSpace(x.IconUrl))
            .WithMessage("Icon URL must be a valid URL");
    }

    private static bool BeValidUrl(string? url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out _);
    }
}

/// <summary>
/// Validator for UpdateVehicleTypeMasterDto
/// </summary>
public class UpdateVehicleTypeMasterDtoValidator : AbstractValidator<UpdateVehicleTypeMasterDto>
{
    public UpdateVehicleTypeMasterDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Vehicle type name is required")
            .MaximumLength(100).WithMessage("Vehicle type name cannot exceed 100 characters");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("Description cannot exceed 500 characters");

        RuleFor(x => x.Category)
            .NotEmpty().WithMessage("Category is required")
            .MaximumLength(50).WithMessage("Category cannot exceed 50 characters");

        RuleFor(x => x.MinLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MinLoadCapacityKg.HasValue)
            .WithMessage("Minimum load capacity must be non-negative");

        RuleFor(x => x.MaxLoadCapacityKg)
            .GreaterThanOrEqualTo(0).When(x => x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinLoadCapacityKg).When(x => x.MinLoadCapacityKg.HasValue && x.MaxLoadCapacityKg.HasValue)
            .WithMessage("Maximum load capacity must be greater than or equal to minimum load capacity");

        RuleFor(x => x.MinVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MinVolumeCapacityM3.HasValue)
            .WithMessage("Minimum volume capacity must be non-negative");

        RuleFor(x => x.MaxVolumeCapacityM3)
            .GreaterThanOrEqualTo(0).When(x => x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be non-negative")
            .GreaterThanOrEqualTo(x => x.MinVolumeCapacityM3).When(x => x.MinVolumeCapacityM3.HasValue && x.MaxVolumeCapacityM3.HasValue)
            .WithMessage("Maximum volume capacity must be greater than or equal to minimum volume capacity");

        RuleFor(x => x.SpecialRequirements)
            .MaximumLength(1000).WithMessage("Special requirements cannot exceed 1000 characters");

        RuleFor(x => x.SortOrder)
            .GreaterThanOrEqualTo(0).WithMessage("Sort order must be non-negative");

        RuleFor(x => x.IconUrl)
            .MaximumLength(500).WithMessage("Icon URL cannot exceed 500 characters")
            .Must(BeValidUrl).When(x => !string.IsNullOrWhiteSpace(x.IconUrl))
            .WithMessage("Icon URL must be a valid URL");
    }

    private static bool BeValidUrl(string? url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out _);
    }
}
