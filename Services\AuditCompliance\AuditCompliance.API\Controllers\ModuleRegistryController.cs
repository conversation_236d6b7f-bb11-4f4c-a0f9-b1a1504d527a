using AuditCompliance.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AuditCompliance.API.Controllers
{
    /// <summary>
    /// Module registry controller for managing microservice modules and access control
    /// </summary>
    [ApiController]
    [Route("api/audit/modules")]
    [Authorize]
    public class ModuleRegistryController : ControllerBase
    {
        private readonly IModuleRegistryService _moduleRegistryService;
        private readonly ILogger<ModuleRegistryController> _logger;

        public ModuleRegistryController(
            IModuleRegistryService moduleRegistryService,
            ILogger<ModuleRegistryController> logger)
        {
            _moduleRegistryService = moduleRegistryService;
            _logger = logger;
        }

        /// <summary>
        /// Get all registered modules
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<List<ModuleInfoDto>>> GetAllModules()
        {
            try
            {
                var modules = await _moduleRegistryService.GetAllModulesAsync();
                return Ok(modules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all modules");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get module by name
        /// </summary>
        [HttpGet("{moduleName}")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<ModuleInfoDto>> GetModuleByName(string moduleName)
        {
            try
            {
                var module = await _moduleRegistryService.GetModuleByNameAsync(moduleName);
                if (module == null)
                {
                    return NotFound(new { Message = $"Module '{moduleName}' not found" });
                }

                return Ok(module);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module {ModuleName}", moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Register a new module
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> RegisterModule([FromBody] ModuleInfoDto moduleDto)
        {
            try
            {
                await _moduleRegistryService.RegisterModuleAsync(moduleDto);
                return CreatedAtAction(nameof(GetModuleByName), new { moduleName = moduleDto.ModuleName }, moduleDto);
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering module {ModuleName}", moduleDto.ModuleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing module
        /// </summary>
        [HttpPut("{moduleName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> UpdateModule(string moduleName, [FromBody] ModuleInfoDto moduleDto)
        {
            try
            {
                if (moduleName != moduleDto.ModuleName)
                {
                    return BadRequest(new { Message = "Module name in URL does not match module name in body" });
                }

                await _moduleRegistryService.UpdateModuleAsync(moduleDto);
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module {ModuleName}", moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get modules accessible by current user
        /// </summary>
        [HttpGet("accessible")]
        public async Task<ActionResult<List<ModuleInfoDto>>> GetAccessibleModules()
        {
            try
            {
                var userId = GetCurrentUserId();
                var modules = await _moduleRegistryService.GetAccessibleModulesAsync(userId);
                return Ok(modules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accessible modules for user {UserId}", GetCurrentUserId());
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Check if current user has access to a specific module
        /// </summary>
        [HttpGet("{moduleName}/access")]
        public async Task<ActionResult<ModuleAccessResponseDto>> CheckModuleAccess(string moduleName)
        {
            try
            {
                var userId = GetCurrentUserId();
                var hasAccess = await _moduleRegistryService.HasModuleAccessAsync(userId, moduleName);
                
                return Ok(new ModuleAccessResponseDto
                {
                    ModuleName = moduleName,
                    UserId = userId,
                    HasAccess = hasAccess,
                    CheckedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking module access for user {UserId} and module {ModuleName}", GetCurrentUserId(), moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Grant module access to a user
        /// </summary>
        [HttpPost("{moduleName}/access")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> GrantModuleAccess(string moduleName, [FromBody] GrantModuleAccessRequestDto request)
        {
            try
            {
                var grantedBy = GetCurrentUserId();
                await _moduleRegistryService.GrantModuleAccessAsync(
                    request.UserId,
                    moduleName,
                    request.AllowedActions,
                    grantedBy,
                    request.ExpiresAt,
                    request.Reason);

                return Ok(new { Message = "Module access granted successfully" });
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error granting module access for user {UserId} to module {ModuleName}", request.UserId, moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Revoke module access from a user
        /// </summary>
        [HttpDelete("{moduleName}/access/{userId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> RevokeModuleAccess(string moduleName, Guid userId, [FromBody] RevokeModuleAccessRequestDto? request = null)
        {
            try
            {
                var revokedBy = GetCurrentUserId();
                await _moduleRegistryService.RevokeModuleAccessAsync(userId, moduleName, revokedBy, request?.Reason);

                return Ok(new { Message = "Module access revoked successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking module access for user {UserId} from module {ModuleName}", userId, moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Update module health status
        /// </summary>
        [HttpPost("{moduleName}/health")]
        [Authorize(Roles = "Admin,System")]
        public async Task<ActionResult> UpdateModuleHealth(string moduleName, [FromBody] ModuleHealthUpdateRequestDto request)
        {
            try
            {
                await _moduleRegistryService.UpdateModuleHealthAsync(moduleName, request.IsHealthy, request.ErrorMessage);
                return Ok(new { Message = "Module health status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module health for {ModuleName}", moduleName);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get module filtering options for audit queries
        /// </summary>
        [HttpGet("filter-options")]
        [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
        public async Task<ActionResult<ModuleFilterOptionsDto>> GetModuleFilterOptions()
        {
            try
            {
                var userId = GetCurrentUserId();
                var accessibleModules = await _moduleRegistryService.GetAccessibleModulesAsync(userId);
                
                var filterOptions = new ModuleFilterOptionsDto
                {
                    AvailableModules = accessibleModules.Select(m => new ModuleFilterOptionDto
                    {
                        ModuleName = m.ModuleName,
                        DisplayName = m.DisplayName,
                        SupportedEntityTypes = m.SupportedEntityTypes,
                        SupportedActions = m.SupportedActions
                    }).ToList()
                };

                return Ok(filterOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving module filter options");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
        }
    }

    /// <summary>
    /// Module access response DTO
    /// </summary>
    public class ModuleAccessResponseDto
    {
        public string ModuleName { get; set; } = string.Empty;
        public Guid UserId { get; set; }
        public bool HasAccess { get; set; }
        public DateTime CheckedAt { get; set; }
    }

    /// <summary>
    /// Grant module access request DTO
    /// </summary>
    public class GrantModuleAccessRequestDto
    {
        public Guid UserId { get; set; }
        public List<string> AllowedActions { get; set; } = new();
        public DateTime? ExpiresAt { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Revoke module access request DTO
    /// </summary>
    public class RevokeModuleAccessRequestDto
    {
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Module health update request DTO
    /// </summary>
    public class ModuleHealthUpdateRequestDto
    {
        public bool IsHealthy { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Module filter options DTO
    /// </summary>
    public class ModuleFilterOptionsDto
    {
        public List<ModuleFilterOptionDto> AvailableModules { get; set; } = new();
    }

    /// <summary>
    /// Module filter option DTO
    /// </summary>
    public class ModuleFilterOptionDto
    {
        public string ModuleName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public List<string> SupportedEntityTypes { get; set; } = new();
        public List<string> SupportedActions { get; set; } = new();
    }
}
