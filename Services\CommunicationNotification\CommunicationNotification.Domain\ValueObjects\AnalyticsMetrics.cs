﻿using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Value object for message performance metrics
/// </summary>
public class MessagePerformanceMetrics : ValueObject
{
    public int TotalMessages { get; private set; }
    public int DeliveredMessages { get; private set; }
    public int ReadMessages { get; private set; }
    public int ClickedMessages { get; private set; }
    public int FailedMessages { get; private set; }
    public decimal DeliveryRate { get; private set; }
    public decimal ReadRate { get; private set; }
    public decimal ClickThroughRate { get; private set; }
    public decimal FailureRate { get; private set; }
    public TimeSpan AverageDeliveryTime { get; private set; }
    public TimeSpan AverageReadTime { get; private set; }
    public decimal TotalCost { get; private set; }
    public decimal CostPerMessage { get; private set; }
    public decimal EngagementScore { get; private set; }

    private MessagePerformanceMetrics() { }

    public MessagePerformanceMetrics(
        int totalMessages,
        int deliveredMessages,
        int readMessages,
        int clickedMessages,
        int failedMessages,
        TimeSpan averageDeliveryTime,
        TimeSpan averageReadTime,
        decimal totalCost,
        decimal engagementScore)
    {
        TotalMessages = totalMessages;
        DeliveredMessages = deliveredMessages;
        ReadMessages = readMessages;
        ClickedMessages = clickedMessages;
        FailedMessages = failedMessages;
        AverageDeliveryTime = averageDeliveryTime;
        AverageReadTime = averageReadTime;
        TotalCost = totalCost;
        EngagementScore = engagementScore;

        // Calculate rates
        DeliveryRate = totalMessages > 0 ? (decimal)deliveredMessages / totalMessages * 100 : 0;
        ReadRate = deliveredMessages > 0 ? (decimal)readMessages / deliveredMessages * 100 : 0;
        ClickThroughRate = readMessages > 0 ? (decimal)clickedMessages / readMessages * 100 : 0;
        FailureRate = totalMessages > 0 ? (decimal)failedMessages / totalMessages * 100 : 0;
        CostPerMessage = totalMessages > 0 ? totalCost / totalMessages : 0;
    }

    public static MessagePerformanceMetrics Empty()
    {
        return new MessagePerformanceMetrics(0, 0, 0, 0, 0, TimeSpan.Zero, TimeSpan.Zero, 0, 0);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalMessages;
        yield return DeliveredMessages;
        yield return ReadMessages;
        yield return ClickedMessages;
        yield return FailedMessages;
        yield return DeliveryRate;
        yield return ReadRate;
        yield return ClickThroughRate;
        yield return FailureRate;
        yield return AverageDeliveryTime;
        yield return AverageReadTime;
        yield return TotalCost;
        yield return CostPerMessage;
        yield return EngagementScore;
    }
}

/// <summary>
/// Value object for channel performance metrics
/// </summary>
public class ChannelPerformanceMetrics : ValueObject
{
    public NotificationChannel Channel { get; private set; }
    public MessagePerformanceMetrics Performance { get; private set; }
    public decimal MarketShare { get; private set; }
    public decimal CostEfficiency { get; private set; }
    public decimal UserPreference { get; private set; }
    public Dictionary<string, decimal> HourlyDistribution { get; private set; }

    private ChannelPerformanceMetrics()
    {
        HourlyDistribution = new Dictionary<string, decimal>();
        Performance = MessagePerformanceMetrics.Empty();
    }

    public ChannelPerformanceMetrics(
        NotificationChannel channel,
        MessagePerformanceMetrics performance,
        decimal marketShare,
        decimal costEfficiency,
        decimal userPreference,
        Dictionary<string, decimal>? hourlyDistribution = null)
    {
        Channel = channel;
        Performance = performance ?? throw new ArgumentNullException(nameof(performance));
        MarketShare = marketShare;
        CostEfficiency = costEfficiency;
        UserPreference = userPreference;
        HourlyDistribution = hourlyDistribution ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Channel;
        yield return Performance;
        yield return MarketShare;
        yield return CostEfficiency;
        yield return UserPreference;

        foreach (var kvp in HourlyDistribution.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Value object for user engagement metrics
/// </summary>
public class UserEngagementMetrics : ValueObject
{
    public Guid UserId { get; private set; }
    public int TotalMessagesReceived { get; private set; }
    public int MessagesRead { get; private set; }
    public int MessagesClicked { get; private set; }
    public decimal EngagementRate { get; private set; }
    public TimeSpan AverageResponseTime { get; private set; }
    public NotificationChannel PreferredChannel { get; private set; }
    public Dictionary<MessageType, int> MessageTypePreferences { get; private set; }
    public DateTime LastEngagement { get; private set; }
    public bool IsActiveUser { get; private set; }

    private UserEngagementMetrics()
    {
        MessageTypePreferences = new Dictionary<MessageType, int>();
    }

    public UserEngagementMetrics(
        Guid userId,
        int totalMessagesReceived,
        int messagesRead,
        int messagesClicked,
        TimeSpan averageResponseTime,
        NotificationChannel preferredChannel,
        Dictionary<MessageType, int>? messageTypePreferences = null,
        DateTime? lastEngagement = null)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        UserId = userId;
        TotalMessagesReceived = totalMessagesReceived;
        MessagesRead = messagesRead;
        MessagesClicked = messagesClicked;
        AverageResponseTime = averageResponseTime;
        PreferredChannel = preferredChannel;
        MessageTypePreferences = messageTypePreferences ?? new Dictionary<MessageType, int>();
        LastEngagement = lastEngagement ?? DateTime.MinValue;

        // Calculate engagement rate
        EngagementRate = totalMessagesReceived > 0 ?
            (decimal)(messagesRead + messagesClicked) / totalMessagesReceived * 100 : 0;

        // Determine if user is active (engaged in last 30 days)
        IsActiveUser = LastEngagement > DateTime.UtcNow.AddDays(-30);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return UserId;
        yield return TotalMessagesReceived;
        yield return MessagesRead;
        yield return MessagesClicked;
        yield return EngagementRate;
        yield return AverageResponseTime;
        yield return PreferredChannel;
        yield return LastEngagement;
        yield return IsActiveUser;

        foreach (var kvp in MessageTypePreferences.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Value object for time-based analytics metrics
/// </summary>
public class TimeBasedMetrics : ValueObject
{
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }
    public TimePeriodType PeriodType { get; private set; }
    public MessagePerformanceMetrics Metrics { get; private set; }
    public Dictionary<string, decimal> TrendData { get; private set; }

    private TimeBasedMetrics()
    {
        TrendData = new Dictionary<string, decimal>();
        Metrics = MessagePerformanceMetrics.Empty();
    }

    public TimeBasedMetrics(
        DateTime periodStart,
        DateTime periodEnd,
        TimePeriodType periodType,
        MessagePerformanceMetrics metrics,
        Dictionary<string, decimal>? trendData = null)
    {
        if (periodStart >= periodEnd)
            throw new ArgumentException("Period start must be before period end");

        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        PeriodType = periodType;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        TrendData = trendData ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return PeriodStart;
        yield return PeriodEnd;
        yield return PeriodType;
        yield return Metrics;

        foreach (var kvp in TrendData.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Time period types for analytics
/// </summary>
public enum TimePeriodType
{
    Hour = 1,
    Day = 2,
    Week = 3,
    Month = 4,
    Quarter = 5,
    Year = 6
}


