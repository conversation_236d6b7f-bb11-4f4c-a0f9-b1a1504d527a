using MediatR;

namespace DocumentManagement.Application.Commands.SetupDocumentExpiryAlerts;

public class SetupDocumentExpiryAlertsCommand : IRequest<SetupDocumentExpiryAlertsResponse>
{
    public Guid DriverId { get; set; }
    public DocumentExpiryAlertSettings Settings { get; set; } = new();
}

public class SetupDocumentExpiryAlertsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DocumentExpiryAlertConfiguration? Configuration { get; set; }
    public List<ScheduledAlertDto> ScheduledAlerts { get; set; } = new();
    public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;
}

public class DocumentExpiryAlertSettings
{
    // Notification preferences
    public bool EmailNotificationsEnabled { get; set; } = true;
    public bool SMSNotificationsEnabled { get; set; } = false;
    public bool PushNotificationsEnabled { get; set; } = true;
    public bool InAppNotificationsEnabled { get; set; } = true;
    
    // Alert timing
    public List<int> AlertDays { get; set; } = new() { 30, 15, 7, 3, 1 }; // Days before expiry
    public string AlertTime { get; set; } = "09:00"; // Time of day for alerts
    public string TimeZone { get; set; } = "UTC";
    
    // Alert recipients
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> SMSRecipients { get; set; } = new();
    
    // Alert content customization
    public Dictionary<string, string> CustomMessages { get; set; } = new();
    public string PreferredLanguage { get; set; } = "en";
    
    // Document type specific settings
    public Dictionary<string, DocumentTypeAlertSettings> DocumentTypeSettings { get; set; } = new();
    
    // Advanced settings
    public bool EnableEscalation { get; set; } = false;
    public int EscalationDays { get; set; } = 7; // Days after expiry to escalate
    public List<string> EscalationRecipients { get; set; } = new();
    public bool EnableAutoRenewalReminders { get; set; } = true;
    public bool EnableBulkAlerts { get; set; } = false; // Group multiple expiring documents
    
    // Frequency limits
    public int MaxAlertsPerDay { get; set; } = 3;
    public int MaxAlertsPerDocument { get; set; } = 10;
    public bool RespectQuietHours { get; set; } = true;
    public string QuietHoursStart { get; set; } = "22:00";
    public string QuietHoursEnd { get; set; } = "08:00";
}

public class DocumentTypeAlertSettings
{
    public bool IsEnabled { get; set; } = true;
    public List<int> CustomAlertDays { get; set; } = new();
    public string Priority { get; set; } = "Medium"; // Low, Medium, High, Critical
    public bool RequireAcknowledgment { get; set; } = false;
    public string? CustomMessage { get; set; }
    public List<string> SpecificRecipients { get; set; } = new();
}

public class DocumentExpiryAlertConfiguration
{
    public Guid ConfigurationId { get; set; }
    public Guid DriverId { get; set; }
    public DocumentExpiryAlertSettings Settings { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public Guid? UpdatedBy { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ScheduledAlertDto
{
    public Guid AlertId { get; set; }
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentName { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public DateTime ScheduledDate { get; set; }
    public int DaysBeforeExpiry { get; set; }
    public string AlertType { get; set; } = string.Empty; // Email, SMS, Push, InApp
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = "Scheduled"; // Scheduled, Sent, Failed, Cancelled
    public string? Message { get; set; }
    public List<string> Recipients { get; set; } = new();
}

// Command Handler
using MediatR;
using Microsoft.Extensions.Logging;
using DocumentManagement.Domain.Repositories;
using DocumentManagement.Domain.Entities;
using TLI.Shared.Infrastructure.Messaging;

namespace DocumentManagement.Application.Commands.SetupDocumentExpiryAlerts;

public class SetupDocumentExpiryAlertsCommandHandler : IRequestHandler<SetupDocumentExpiryAlertsCommand, SetupDocumentExpiryAlertsResponse>
{
    private readonly IDriverDocumentRepository _documentRepository;
    private readonly IDocumentExpiryAlertRepository _alertRepository;
    private readonly IDocumentExpiryConfigurationRepository _configurationRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<SetupDocumentExpiryAlertsCommandHandler> _logger;

    public SetupDocumentExpiryAlertsCommandHandler(
        IDriverDocumentRepository documentRepository,
        IDocumentExpiryAlertRepository alertRepository,
        IDocumentExpiryConfigurationRepository configurationRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<SetupDocumentExpiryAlertsCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _alertRepository = alertRepository;
        _configurationRepository = configurationRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<SetupDocumentExpiryAlertsResponse> Handle(SetupDocumentExpiryAlertsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Setting up document expiry alerts for driver {DriverId}", request.DriverId);

            // Validate settings
            var validationResult = ValidateAlertSettings(request.Settings);
            if (!validationResult.IsValid)
            {
                return new SetupDocumentExpiryAlertsResponse
                {
                    IsSuccess = false,
                    ErrorMessage = validationResult.ErrorMessage
                };
            }

            // Get or create configuration
            var configuration = await _configurationRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            
            if (configuration == null)
            {
                // Create new configuration
                configuration = new DocumentExpiryAlertConfigurationEntity(
                    request.DriverId,
                    request.Settings);

                await _configurationRepository.AddAsync(configuration, cancellationToken);
            }
            else
            {
                // Update existing configuration
                configuration.UpdateSettings(request.Settings);
                _configurationRepository.Update(configuration);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Get driver documents to schedule alerts
            var documents = await _documentRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);
            var documentsWithExpiry = documents.Where(d => d.ExpiryDate.HasValue && d.ExpiryDate > DateTime.UtcNow).ToList();

            // Schedule alerts for each document
            var scheduledAlerts = await ScheduleAlertsForDocuments(documentsWithExpiry, request.Settings, cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("document.expiry_alerts.configured", new
            {
                DriverId = request.DriverId,
                ConfigurationId = configuration.Id,
                AlertsScheduled = scheduledAlerts.Count,
                Settings = request.Settings,
                ConfiguredAt = DateTime.UtcNow
            }, cancellationToken);

            // Map to response DTO
            var responseConfiguration = new DocumentExpiryAlertConfiguration
            {
                ConfigurationId = configuration.Id,
                DriverId = configuration.DriverId,
                Settings = configuration.Settings,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                UpdatedAt = configuration.UpdatedAt,
                CreatedBy = configuration.CreatedBy,
                UpdatedBy = configuration.UpdatedBy,
                Metadata = configuration.Metadata
            };

            _logger.LogInformation("Document expiry alerts configured successfully for driver {DriverId}, {AlertCount} alerts scheduled", 
                request.DriverId, scheduledAlerts.Count);

            return new SetupDocumentExpiryAlertsResponse
            {
                IsSuccess = true,
                Configuration = responseConfiguration,
                ScheduledAlerts = scheduledAlerts,
                ConfiguredAt = configuration.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up document expiry alerts for driver {DriverId}", request.DriverId);
            return new SetupDocumentExpiryAlertsResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ConfiguredAt = DateTime.UtcNow
            };
        }
    }

    private ValidationResult ValidateAlertSettings(DocumentExpiryAlertSettings settings)
    {
        var errors = new List<string>();

        // Validate alert days
        if (!settings.AlertDays.Any() || settings.AlertDays.Any(d => d < 0))
        {
            errors.Add("Alert days must be positive numbers and at least one day must be specified");
        }

        // Validate time format
        if (!TimeSpan.TryParse(settings.AlertTime, out _))
        {
            errors.Add("Alert time must be in valid HH:mm format");
        }

        // Validate quiet hours
        if (settings.RespectQuietHours)
        {
            if (!TimeSpan.TryParse(settings.QuietHoursStart, out _) || !TimeSpan.TryParse(settings.QuietHoursEnd, out _))
            {
                errors.Add("Quiet hours must be in valid HH:mm format");
            }
        }

        // Validate notification settings
        if (!settings.EmailNotificationsEnabled && !settings.SMSNotificationsEnabled && 
            !settings.PushNotificationsEnabled && !settings.InAppNotificationsEnabled)
        {
            errors.Add("At least one notification method must be enabled");
        }

        // Validate email recipients if email notifications are enabled
        if (settings.EmailNotificationsEnabled && settings.EmailRecipients.Any())
        {
            foreach (var email in settings.EmailRecipients)
            {
                if (!IsValidEmail(email))
                {
                    errors.Add($"Invalid email address: {email}");
                }
            }
        }

        // Validate frequency limits
        if (settings.MaxAlertsPerDay < 1 || settings.MaxAlertsPerDay > 50)
        {
            errors.Add("Max alerts per day must be between 1 and 50");
        }

        if (settings.MaxAlertsPerDocument < 1 || settings.MaxAlertsPerDocument > 20)
        {
            errors.Add("Max alerts per document must be between 1 and 20");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            ErrorMessage = errors.Count > 0 ? string.Join("; ", errors) : null
        };
    }

    private async Task<List<ScheduledAlertDto>> ScheduleAlertsForDocuments(
        List<DriverDocument> documents, 
        DocumentExpiryAlertSettings settings, 
        CancellationToken cancellationToken)
    {
        var scheduledAlerts = new List<ScheduledAlertDto>();

        foreach (var document in documents)
        {
            if (!document.ExpiryDate.HasValue) continue;

            var documentTypeSettings = settings.DocumentTypeSettings.GetValueOrDefault(
                document.DocumentType.ToString(), 
                new DocumentTypeAlertSettings());

            if (!documentTypeSettings.IsEnabled) continue;

            var alertDays = documentTypeSettings.CustomAlertDays.Any() 
                ? documentTypeSettings.CustomAlertDays 
                : settings.AlertDays;

            foreach (var days in alertDays)
            {
                var alertDate = document.ExpiryDate.Value.AddDays(-days);
                
                // Only schedule future alerts
                if (alertDate <= DateTime.UtcNow) continue;

                // Schedule different types of alerts based on settings
                if (settings.EmailNotificationsEnabled)
                {
                    scheduledAlerts.Add(CreateScheduledAlert(document, alertDate, days, "Email", documentTypeSettings, settings));
                }

                if (settings.SMSNotificationsEnabled)
                {
                    scheduledAlerts.Add(CreateScheduledAlert(document, alertDate, days, "SMS", documentTypeSettings, settings));
                }

                if (settings.PushNotificationsEnabled)
                {
                    scheduledAlerts.Add(CreateScheduledAlert(document, alertDate, days, "Push", documentTypeSettings, settings));
                }

                if (settings.InAppNotificationsEnabled)
                {
                    scheduledAlerts.Add(CreateScheduledAlert(document, alertDate, days, "InApp", documentTypeSettings, settings));
                }
            }
        }

        // Save scheduled alerts to repository
        foreach (var alert in scheduledAlerts)
        {
            var alertEntity = new DocumentExpiryAlert(
                alert.DocumentId,
                alert.ScheduledDate,
                alert.AlertType,
                alert.Message ?? string.Empty,
                alert.Recipients);

            await _alertRepository.AddAsync(alertEntity, cancellationToken);
        }

        return scheduledAlerts;
    }

    private ScheduledAlertDto CreateScheduledAlert(
        DriverDocument document, 
        DateTime alertDate, 
        int daysBeforeExpiry, 
        string alertType,
        DocumentTypeAlertSettings documentTypeSettings,
        DocumentExpiryAlertSettings globalSettings)
    {
        var message = documentTypeSettings.CustomMessage ?? 
                     globalSettings.CustomMessages.GetValueOrDefault(document.DocumentType.ToString()) ??
                     $"Your {document.DocumentType} document '{document.DocumentName}' will expire in {daysBeforeExpiry} days.";

        var recipients = documentTypeSettings.SpecificRecipients.Any() 
            ? documentTypeSettings.SpecificRecipients 
            : GetRecipientsForAlertType(alertType, globalSettings);

        return new ScheduledAlertDto
        {
            AlertId = Guid.NewGuid(),
            DocumentId = document.Id,
            DocumentType = document.DocumentType.ToString(),
            DocumentName = document.DocumentName,
            ExpiryDate = document.ExpiryDate!.Value,
            ScheduledDate = alertDate,
            DaysBeforeExpiry = daysBeforeExpiry,
            AlertType = alertType,
            Priority = documentTypeSettings.Priority,
            Status = "Scheduled",
            Message = message,
            Recipients = recipients
        };
    }

    private List<string> GetRecipientsForAlertType(string alertType, DocumentExpiryAlertSettings settings)
    {
        return alertType switch
        {
            "Email" => settings.EmailRecipients,
            "SMS" => settings.SMSRecipients,
            _ => new List<string>()
        };
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
