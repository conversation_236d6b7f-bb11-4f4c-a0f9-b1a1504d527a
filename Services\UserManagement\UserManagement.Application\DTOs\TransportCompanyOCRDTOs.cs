﻿using UserManagement.Domain.Entities;
using Microsoft.AspNetCore.Http;

namespace UserManagement.Application.DTOs;

/// <summary>
/// Request for processing document with OCR
/// </summary>
public class ProcessDocumentWithOCRRequest
{
    public IFormFile File { get; set; } = null!;
    public DocumentType DocumentType { get; set; }
    public bool IsPreviewMode { get; set; } = true;
    public bool AutoValidate { get; set; } = true;
    public bool AutoCorrect { get; set; } = false;
    public decimal MinimumConfidenceThreshold { get; set; } = 0.7m;
    public Dictionary<string, object> ExistingData { get; set; } = new();
    public bool OverwriteExistingData { get; set; } = false;
    public List<string> RequiredFields { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
}

/// <summary>
/// DTO for OCR processing status
/// </summary>
public class OCRProcessingStatusDto
{
    public string PreviewToken { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Processing, Completed, Failed
    public DateTime ProcessedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public bool IsExpired { get; set; }
    public bool CanConfirm { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// DTO for supported document types
/// </summary>
public class SupportedDocumentTypeDto
{
    public DocumentType DocumentType { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> SupportedFormats { get; set; } = new();
    public long MaxFileSize { get; set; }
    public List<string> ExpectedFields { get; set; } = new();
    public List<string> ValidationRules { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// DTO for OCR analytics
/// </summary>
public class OCRAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    
    // Overall metrics
    public int TotalDocumentsProcessed { get; set; }
    public int SuccessfulExtractions { get; set; }
    public int FailedExtractions { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageConfidence { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
    
    // Document type breakdown
    public Dictionary<string, int> DocumentTypeBreakdown { get; set; } = new();
    public Dictionary<string, decimal> ConfidenceByDocumentType { get; set; } = new();
    public Dictionary<string, TimeSpan> ProcessingTimeByDocumentType { get; set; } = new();
    
    // Quality metrics
    public OCRQualityMetricsDto QualityMetrics { get; set; } = new();
    
    // Trends
    public List<OCRTrendDataDto> DailyTrends { get; set; } = new();
    public List<OCRTrendDataDto> HourlyTrends { get; set; } = new();
    
    // Error analysis
    public Dictionary<string, int> ErrorTypes { get; set; } = new();
    public List<string> CommonIssues { get; set; } = new();
    public List<string> ImprovementSuggestions { get; set; } = new();
}

/// <summary>
/// DTO for OCR quality metrics
/// </summary>
public class OCRQualityMetricsDto
{
    public decimal AverageImageQuality { get; set; }
    public decimal AverageTextClarity { get; set; }
    public decimal AverageDocumentCompleteness { get; set; }
    public int BlurryDocuments { get; set; }
    public int SkewedDocuments { get; set; }
    public int DocumentsWithGlare { get; set; }
    public int PartiallyVisibleDocuments { get; set; }
    public int LowQualityDocuments { get; set; }
    public int HighQualityDocuments { get; set; }
    public List<string> QualityImprovementSuggestions { get; set; } = new();
}

/// <summary>
/// DTO for OCR trend data
/// </summary>
public class OCRTrendDataDto
{
    public DateTime Date { get; set; }
    public int DocumentsProcessed { get; set; }
    public int SuccessfulExtractions { get; set; }
    public int FailedExtractions { get; set; }
    public decimal AverageConfidence { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
    public decimal SuccessRate { get; set; }
}

/// <summary>
/// DTO for OCR field validation
/// </summary>
public class OCRFieldValidationDto
{
    public string FieldName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string ExtractedValue { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public bool IsValid { get; set; }
    public bool IsRequired { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
    public string? SuggestedValue { get; set; }
    public string FieldType { get; set; } = string.Empty;
    public Dictionary<string, object> ValidationRules { get; set; } = new();
}

/// <summary>
/// DTO for document preview
/// </summary>
public class DocumentPreviewDto
{
    public string PreviewToken { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string PreviewImageUrl { get; set; } = string.Empty;
    public List<OCRFieldValidationDto> Fields { get; set; } = new();
    public List<DataConflictDto> DataConflicts { get; set; } = new();
    public List<AnnotatedRegionDto> AnnotatedRegions { get; set; } = new();
    public OCRExtractionSummaryDto ExtractionSummary { get; set; } = new();
    public DocumentValidationSummaryDto ValidationSummary { get; set; } = new();
    public List<OCRSuggestionDto> Suggestions { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// DTO for data conflicts
/// </summary>
public class DataConflictDto
{
    public string FieldName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string ExistingValue { get; set; } = string.Empty;
    public string ExtractedValue { get; set; } = string.Empty;
    public decimal ExtractedConfidence { get; set; }
    public string RecommendedAction { get; set; } = string.Empty;
    public string ConflictReason { get; set; } = string.Empty;
    public string ConflictSeverity { get; set; } = string.Empty; // Low, Medium, High, Critical
}

/// <summary>
/// DTO for annotated regions
/// </summary>
public class AnnotatedRegionDto
{
    public string FieldName { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public BoundingBoxDto BoundingBox { get; set; } = new();
    public string Color { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public bool IsHighlighted { get; set; }
    public string RegionType { get; set; } = string.Empty;
}

/// <summary>
/// DTO for bounding box
/// </summary>
public class BoundingBoxDto
{
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public decimal Confidence { get; set; }
}

/// <summary>
/// DTO for OCR extraction summary
/// </summary>
public class OCRExtractionSummaryDto
{
    public decimal OverallConfidence { get; set; }
    public int TotalFieldsExtracted { get; set; }
    public int HighConfidenceFields { get; set; }
    public int MediumConfidenceFields { get; set; }
    public int LowConfidenceFields { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public string OCRProvider { get; set; } = string.Empty;
    public DocumentQualityScoreDto QualityScore { get; set; } = new();
}

/// <summary>
/// DTO for document quality score
/// </summary>
public class DocumentQualityScoreDto
{
    public decimal ImageQuality { get; set; }
    public decimal TextClarity { get; set; }
    public decimal DocumentCompleteness { get; set; }
    public bool IsBlurry { get; set; }
    public bool IsSkewed { get; set; }
    public bool HasGlare { get; set; }
    public bool IsPartiallyVisible { get; set; }
    public List<string> QualityIssues { get; set; } = new();
    public List<string> ImprovementSuggestions { get; set; } = new();
}

/// <summary>
/// DTO for document validation summary
/// </summary>
public class DocumentValidationSummaryDto
{
    public bool IsValid { get; set; }
    public decimal ValidationScore { get; set; }
    public int TotalValidationChecks { get; set; }
    public int PassedChecks { get; set; }
    public int FailedChecks { get; set; }
    public int CriticalIssues { get; set; }
    public int Warnings { get; set; }
    public List<string> MissingRequiredFields { get; set; } = new();
    public List<string> InvalidFields { get; set; } = new();
    public AuthenticityResultDto? AuthenticityResult { get; set; }
}

/// <summary>
/// DTO for authenticity result
/// </summary>
public class AuthenticityResultDto
{
    public bool IsAuthentic { get; set; }
    public decimal AuthenticityScore { get; set; }
    public List<string> AuthenticityChecks { get; set; } = new();
    public List<string> SuspiciousElements { get; set; } = new();
    public string AuthenticityProvider { get; set; } = string.Empty;
}

/// <summary>
/// DTO for OCR suggestions
/// </summary>
public class OCRSuggestionDto
{
    public string SuggestionType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ActionRequired { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // Low, Medium, High, Critical
    public string Category { get; set; } = string.Empty; // Quality, Validation, Processing, etc.
    public Dictionary<string, object> SuggestionData { get; set; } = new();
}

/// <summary>
/// Request for confirming OCR processed document
/// </summary>
public class ConfirmOCRDocumentRequest
{
    public string PreviewToken { get; set; } = string.Empty;
    public Dictionary<string, object> ConfirmedData { get; set; } = new();
    public List<FieldCorrectionDto> UserCorrections { get; set; } = new();
    public bool OverrideValidationIssues { get; set; } = false;
    public string? UserNotes { get; set; }
}

/// <summary>
/// DTO for field corrections
/// </summary>
public class FieldCorrectionDto
{
    public string FieldName { get; set; } = string.Empty;
    public string OriginalValue { get; set; } = string.Empty;
    public string CorrectedValue { get; set; } = string.Empty;
    public string CorrectionReason { get; set; } = string.Empty;
    public decimal OriginalConfidence { get; set; }
}

/// <summary>
/// Response for OCR document confirmation
/// </summary>
public class ConfirmOCRDocumentResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? DocumentId { get; set; }
    public DateTime ConfirmedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public Dictionary<string, object> FinalData { get; set; } = new();
    public DocumentProcessingSummaryDto ProcessingSummary { get; set; } = new();
}

/// <summary>
/// DTO for document processing summary
/// </summary>
public class DocumentProcessingSummaryDto
{
    public int TotalFieldsProcessed { get; set; }
    public int AutoExtractedFields { get; set; }
    public int UserCorrectedFields { get; set; }
    public int ValidationIssuesResolved { get; set; }
    public decimal FinalConfidenceScore { get; set; }
    public TimeSpan TotalProcessingTime { get; set; }
    public List<string> ProcessingSteps { get; set; } = new();
}

