using MediatR;

namespace OrderManagement.Application.Commands.ResetRfqAward;

public class ResetRfqAwardCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid AdminUserId { get; set; }
    public string AdminUserName { get; set; } = string.Empty;
    public string AdminRole { get; set; } = string.Empty;
    public string ResetReason { get; set; } = string.Empty;
    public string? AdditionalNotes { get; set; }
    public bool ReopenForBidding { get; set; } = true;
    public DateTime? NewExpirationDate { get; set; }
    public bool NotifyStakeholders { get; set; } = true;
    public Dictionary<string, object>? AuditMetadata { get; set; }
}
