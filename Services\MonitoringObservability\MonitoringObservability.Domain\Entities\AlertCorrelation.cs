using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Events;
using Shared.Domain.Common;

namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents a correlation rule for grouping related alerts
/// </summary>
public class AlertCorrelationRule : AggregateRoot
{
    private readonly List<AlertCorrelationCondition> _conditions = new();
    private readonly List<AlertCorrelationGroup> _groups = new();

    public AlertCorrelationRule(string name, string description, CorrelationRuleType ruleType,
        TimeSpan correlationWindow, bool isEnabled = true)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        RuleType = ruleType;
        CorrelationWindow = correlationWindow;
        IsEnabled = isEnabled;
        CreatedAt = DateTime.UtcNow;
        LastModified = DateTime.UtcNow;
        Priority = 100;
        MaxGroupSize = 50; // Default max alerts per group
        Tags = new Dictionary<string, string>();
        Parameters = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AlertCorrelationRule() { }

    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public CorrelationRuleType RuleType { get; private set; }
    public TimeSpan CorrelationWindow { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; }
    public int MaxGroupSize { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public DateTime? LastTriggered { get; private set; }
    public int CorrelationCount { get; private set; }
    public Dictionary<string, string> Tags { get; private set; } = new();
    public Dictionary<string, object> Parameters { get; private set; } = new();

    // Navigation properties
    public IReadOnlyList<AlertCorrelationCondition> Conditions => _conditions.AsReadOnly();
    public IReadOnlyList<AlertCorrelationGroup> Groups => _groups.AsReadOnly();

    public void UpdateRule(string? name = null, string? description = null,
        TimeSpan? correlationWindow = null, bool? isEnabled = null, int? priority = null)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;
        
        if (!string.IsNullOrWhiteSpace(description))
            Description = description;
        
        if (correlationWindow.HasValue)
            CorrelationWindow = correlationWindow.Value;
        
        if (isEnabled.HasValue)
            IsEnabled = isEnabled.Value;
        
        if (priority.HasValue)
            Priority = priority.Value;

        LastModified = DateTime.UtcNow;
        
        AddDomainEvent(new AlertCorrelationRuleUpdatedEvent(Id, Name));
    }

    public void AddCondition(AlertCorrelationCondition condition)
    {
        if (condition == null) throw new ArgumentNullException(nameof(condition));
        
        _conditions.Add(condition);
        LastModified = DateTime.UtcNow;
    }

    public void RemoveCondition(Guid conditionId)
    {
        var condition = _conditions.FirstOrDefault(c => c.Id == conditionId);
        if (condition != null)
        {
            _conditions.Remove(condition);
            LastModified = DateTime.UtcNow;
        }
    }

    public AlertCorrelationGroup CreateGroup(string groupKey, List<Guid> alertIds)
    {
        var group = new AlertCorrelationGroup(Id, groupKey, alertIds, CorrelationWindow);
        _groups.Add(group);
        
        CorrelationCount++;
        LastTriggered = DateTime.UtcNow;
        
        AddDomainEvent(new AlertCorrelationGroupCreatedEvent(Id, group.Id, alertIds.Count));
        
        return group;
    }

    public void CloseExpiredGroups()
    {
        var expiredGroups = _groups.Where(g => g.IsExpired()).ToList();
        
        foreach (var group in expiredGroups)
        {
            group.Close();
            AddDomainEvent(new AlertCorrelationGroupClosedEvent(Id, group.Id, "Expired"));
        }
    }

    public bool CanCorrelateAlerts(List<Alert> alerts)
    {
        if (!IsEnabled || alerts.Count < 2)
            return false;

        // Check if alerts match correlation conditions
        return _conditions.All(condition => condition.EvaluateCondition(alerts));
    }

    public string GenerateGroupKey(List<Alert> alerts)
    {
        return RuleType switch
        {
            CorrelationRuleType.ServiceDependency => string.Join("-", alerts.Select(a => a.ServiceName).Distinct().OrderBy(s => s)),
            CorrelationRuleType.MetricCorrelation => string.Join("-", alerts.Select(a => a.MetricName).Distinct().OrderBy(m => m)),
            CorrelationRuleType.TimeWindow => $"time-{DateTime.UtcNow:yyyyMMddHHmm}",
            CorrelationRuleType.PatternMatching => GeneratePatternKey(alerts),
            _ => Guid.NewGuid().ToString("N")[..8]
        };
    }

    private string GeneratePatternKey(List<Alert> alerts)
    {
        // Generate a key based on alert patterns
        var severities = string.Join("", alerts.Select(a => a.Severity.ToString()[0]));
        var services = string.Join("-", alerts.Select(a => a.ServiceName).Distinct().Take(3));
        return $"pattern-{severities}-{services}";
    }
}

/// <summary>
/// Represents a condition for alert correlation
/// </summary>
public class AlertCorrelationCondition
{
    public AlertCorrelationCondition(string field, string conditionOperator, string value,
        AlertConditionOperator logicalOperator = AlertConditionOperator.And)
    {
        Id = Guid.NewGuid();
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = conditionOperator ?? throw new ArgumentNullException(nameof(conditionOperator));
        Value = value ?? throw new ArgumentNullException(nameof(value));
        LogicalOperator = logicalOperator;
        IsEnabled = true;
        CreatedAt = DateTime.UtcNow;
    }

    // Required for EF Core
    private AlertCorrelationCondition() { }

    public Guid Id { get; private set; }
    public string Field { get; private set; } = string.Empty; // e.g., "ServiceName", "Severity", "MetricName"
    public string Operator { get; private set; } = string.Empty; // e.g., "equals", "contains", "matches"
    public string Value { get; private set; } = string.Empty;
    public AlertConditionOperator LogicalOperator { get; private set; }
    public bool IsEnabled { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation properties
    public AlertCorrelationRule CorrelationRule { get; private set; } = null!;

    public bool EvaluateCondition(List<Alert> alerts)
    {
        if (!IsEnabled)
            return true;

        return Operator.ToLowerInvariant() switch
        {
            "equals" => alerts.Any(a => GetFieldValue(a, Field)?.ToString() == Value),
            "contains" => alerts.Any(a => GetFieldValue(a, Field)?.ToString()?.Contains(Value, StringComparison.OrdinalIgnoreCase) == true),
            "matches" => alerts.Any(a => System.Text.RegularExpressions.Regex.IsMatch(GetFieldValue(a, Field)?.ToString() ?? "", Value)),
            "same" => alerts.Select(a => GetFieldValue(a, Field)?.ToString()).Distinct().Count() == 1,
            "different" => alerts.Select(a => GetFieldValue(a, Field)?.ToString()).Distinct().Count() > 1,
            _ => false
        };
    }

    private object? GetFieldValue(Alert alert, string fieldName)
    {
        return fieldName.ToLowerInvariant() switch
        {
            "servicename" => alert.ServiceName,
            "severity" => alert.Severity.ToString(),
            "metricname" => alert.MetricName,
            "source" => alert.Source,
            "title" => alert.Title,
            _ => alert.Tags.GetValueOrDefault(fieldName)
        };
    }
}

/// <summary>
/// Represents a group of correlated alerts
/// </summary>
public class AlertCorrelationGroup
{
    private readonly List<Guid> _alertIds = new();

    public AlertCorrelationGroup(Guid correlationRuleId, string groupKey, List<Guid> alertIds, TimeSpan timeWindow)
    {
        Id = Guid.NewGuid();
        CorrelationRuleId = correlationRuleId;
        GroupKey = groupKey ?? throw new ArgumentNullException(nameof(groupKey));
        _alertIds.AddRange(alertIds ?? throw new ArgumentNullException(nameof(alertIds)));
        CreatedAt = DateTime.UtcNow;
        ExpiresAt = DateTime.UtcNow.Add(timeWindow);
        IsActive = true;
        Metadata = new Dictionary<string, object>();
    }

    // Required for EF Core
    private AlertCorrelationGroup() { }

    public Guid Id { get; private set; }
    public Guid CorrelationRuleId { get; private set; }
    public string GroupKey { get; private set; } = string.Empty;
    public DateTime CreatedAt { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    public bool IsActive { get; private set; }
    public string? ClosureReason { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public AlertCorrelationRule CorrelationRule { get; private set; } = null!;
    public IReadOnlyList<Guid> AlertIds => _alertIds.AsReadOnly();

    // Computed properties
    public int AlertCount => _alertIds.Count;
    public bool IsExpired() => DateTime.UtcNow > ExpiresAt;
    public TimeSpan? Duration => ClosedAt?.Subtract(CreatedAt);

    public void AddAlert(Guid alertId)
    {
        if (!_alertIds.Contains(alertId))
        {
            _alertIds.Add(alertId);
        }
    }

    public void RemoveAlert(Guid alertId)
    {
        _alertIds.Remove(alertId);
    }

    public void ExtendExpiration(TimeSpan extension)
    {
        if (IsActive)
        {
            ExpiresAt = ExpiresAt.Add(extension);
        }
    }

    public void Close(string? reason = null)
    {
        if (IsActive)
        {
            IsActive = false;
            ClosedAt = DateTime.UtcNow;
            ClosureReason = reason;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }
}
