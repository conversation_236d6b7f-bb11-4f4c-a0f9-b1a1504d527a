using SubscriptionManagement.Application.DTOs;
using MediatR;

namespace SubscriptionManagement.Application.Commands.TransportCompanyAutoRenewal;

/// <summary>
/// Command to update auto-renewal settings for Transport Company subscription
/// </summary>
public class UpdateAutoRenewalSettingsCommand : IRequest<UpdateAutoRenewalSettingsResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid SubscriptionId { get; set; }
    public bool AutoRenewEnabled { get; set; }
    public AutoRenewalPreferences Preferences { get; set; } = new();
    public string? ChangeReason { get; set; }
    public Guid UpdatedBy { get; set; }
}

/// <summary>
/// Auto-renewal preferences
/// </summary>
public class AutoRenewalPreferences
{
    public bool EnableEmailNotifications { get; set; } = true;
    public bool EnableSMSNotifications { get; set; } = false;
    public int ReminderDaysBefore { get; set; } = 7;
    public List<int> AdditionalReminderDays { get; set; } = new() { 30, 14, 3, 1 };
    public bool AutoUpdatePaymentMethod { get; set; } = false;
    public bool AllowGracePeriod { get; set; } = true;
    public int GracePeriodDays { get; set; } = 7;
    public bool RequireConfirmationForUpgrade { get; set; } = true;
    public decimal MaxAutoRenewalAmount { get; set; } = 0; // 0 = no limit
    public string PreferredPaymentMethodId { get; set; } = string.Empty;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// Result of updating auto-renewal settings
/// </summary>
public class UpdateAutoRenewalSettingsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public AutoRenewalStatus CurrentStatus { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public NextRenewalInfo? NextRenewal { get; set; }
}

/// <summary>
/// Current auto-renewal status
/// </summary>
public class AutoRenewalStatus
{
    public bool IsEnabled { get; set; }
    public DateTime? NextRenewalDate { get; set; }
    public decimal NextRenewalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public bool PaymentMethodValid { get; set; }
    public string? PaymentMethodLastFour { get; set; }
    public bool IsInGracePeriod { get; set; }
    public DateTime? GracePeriodEndDate { get; set; }
    public List<string> RenewalBlockers { get; set; } = new();
}

/// <summary>
/// Information about next renewal
/// </summary>
public class NextRenewalInfo
{
    public DateTime RenewalDate { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string PlanName { get; set; } = string.Empty;
    public int DaysUntilRenewal { get; set; }
    public bool CanCancel { get; set; } = true;
    public DateTime? CancellationDeadline { get; set; }
    public List<RenewalReminder> ScheduledReminders { get; set; } = new();
}

/// <summary>
/// Renewal reminder information
/// </summary>
public class RenewalReminder
{
    public DateTime ReminderDate { get; set; }
    public int DaysBefore { get; set; }
    public string ReminderType { get; set; } = string.Empty; // Email, SMS, Push
    public bool IsSent { get; set; }
    public DateTime? SentAt { get; set; }
}

/// <summary>
/// Command to get auto-renewal settings
/// </summary>
public class GetAutoRenewalSettingsQuery : IRequest<AutoRenewalSettingsDto>
{
    public Guid TransportCompanyId { get; set; }
    public Guid? SubscriptionId { get; set; }
    public bool IncludeHistory { get; set; } = false;
    public bool IncludeUpcomingReminders { get; set; } = true;
}

/// <summary>
/// Command to process auto-renewal
/// </summary>
public class ProcessAutoRenewalCommand : IRequest<ProcessAutoRenewalResult>
{
    public Guid SubscriptionId { get; set; }
    public bool ForceRenewal { get; set; } = false;
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object> ProcessingOptions { get; set; } = new();
}

/// <summary>
/// Result of processing auto-renewal
/// </summary>
public class ProcessAutoRenewalResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? PaymentId { get; set; }
    public DateTime? RenewalDate { get; set; }
    public DateTime? NextRenewalDate { get; set; }
    public decimal AmountCharged { get; set; }
    public string Currency { get; set; } = string.Empty;
    public RenewalProcessingDetails ProcessingDetails { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Renewal processing details
/// </summary>
public class RenewalProcessingDetails
{
    public DateTime ProcessingStarted { get; set; }
    public DateTime ProcessingCompleted { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string PaymentProvider { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public List<string> ProcessingSteps { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Command to schedule renewal reminders
/// </summary>
public class ScheduleRenewalRemindersCommand : IRequest<ScheduleRenewalRemindersResult>
{
    public Guid SubscriptionId { get; set; }
    public List<int> ReminderDays { get; set; } = new();
    public List<string> NotificationChannels { get; set; } = new();
    public bool RescheduleExisting { get; set; } = false;
    public Guid ScheduledBy { get; set; }
}

/// <summary>
/// Result of scheduling renewal reminders
/// </summary>
public class ScheduleRenewalRemindersResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int RemindersScheduled { get; set; }
    public int RemindersUpdated { get; set; }
    public List<ScheduledReminderDto> ScheduledReminders { get; set; } = new();
    public DateTime ScheduledAt { get; set; }
}

/// <summary>
/// DTO for scheduled reminder
/// </summary>
public class ScheduledReminderDto
{
    public Guid Id { get; set; }
    public DateTime ReminderDate { get; set; }
    public int DaysBefore { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public string Status { get; set; } = string.Empty; // Scheduled, Sent, Failed, Cancelled
    public DateTime? SentAt { get; set; }
    public string? FailureReason { get; set; }
}

/// <summary>
/// Command to cancel auto-renewal
/// </summary>
public class CancelAutoRenewalCommand : IRequest<CancelAutoRenewalResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid SubscriptionId { get; set; }
    public string CancellationReason { get; set; } = string.Empty;
    public bool CancelImmediately { get; set; } = false;
    public DateTime? CancellationDate { get; set; }
    public bool RefundProrated { get; set; } = false;
    public Guid CancelledBy { get; set; }
}

/// <summary>
/// Result of cancelling auto-renewal
/// </summary>
public class CancelAutoRenewalResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CancelledAt { get; set; }
    public DateTime? EffectiveDate { get; set; }
    public decimal? RefundAmount { get; set; }
    public string? RefundTransactionId { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public CancellationSummary Summary { get; set; } = new();
}

/// <summary>
/// Cancellation summary
/// </summary>
public class CancellationSummary
{
    public string SubscriptionPlan { get; set; } = string.Empty;
    public DateTime SubscriptionStartDate { get; set; }
    public DateTime SubscriptionEndDate { get; set; }
    public int TotalBillingCycles { get; set; }
    public decimal TotalAmountPaid { get; set; }
    public decimal RefundAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public bool WillContinueUntilPeriodEnd { get; set; }
    public DateTime? AccessEndDate { get; set; }
}

/// <summary>
/// Command to reactivate auto-renewal
/// </summary>
public class ReactivateAutoRenewalCommand : IRequest<ReactivateAutoRenewalResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid SubscriptionId { get; set; }
    public string? PaymentMethodId { get; set; }
    public AutoRenewalPreferences? NewPreferences { get; set; }
    public string ReactivationReason { get; set; } = string.Empty;
    public Guid ReactivatedBy { get; set; }
}

/// <summary>
/// Result of reactivating auto-renewal
/// </summary>
public class ReactivateAutoRenewalResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ReactivatedAt { get; set; }
    public DateTime? NextRenewalDate { get; set; }
    public bool RequiresImmediatePayment { get; set; }
    public decimal? ImmediatePaymentAmount { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public ReactivationSummary Summary { get; set; } = new();
}

/// <summary>
/// Reactivation summary
/// </summary>
public class ReactivationSummary
{
    public string SubscriptionPlan { get; set; } = string.Empty;
    public DateTime ReactivationDate { get; set; }
    public DateTime NextBillingDate { get; set; }
    public decimal NextBillingAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public bool IsInGracePeriod { get; set; }
    public DateTime? GracePeriodEndDate { get; set; }
    public List<string> BenefitsRestored { get; set; } = new();
}

/// <summary>
/// DTO for auto-renewal settings
/// </summary>
public class AutoRenewalSettingsDto
{
    public Guid SubscriptionId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public bool AutoRenewEnabled { get; set; }
    public AutoRenewalPreferences Preferences { get; set; } = new();
    public AutoRenewalStatus Status { get; set; } = new();
    public NextRenewalInfo? NextRenewal { get; set; }
    public List<AutoRenewalHistoryDto> History { get; set; } = new();
    public List<ScheduledReminderDto> UpcomingReminders { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public Guid LastUpdatedBy { get; set; }
    public string LastUpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// DTO for auto-renewal history
/// </summary>
public class AutoRenewalHistoryDto
{
    public Guid Id { get; set; }
    public DateTime Date { get; set; }
    public string Action { get; set; } = string.Empty; // Enabled, Disabled, Renewed, Failed, etc.
    public string? Reason { get; set; }
    public decimal? Amount { get; set; }
    public string? Currency { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? ProcessedBy { get; set; }
    public string? ProcessedByName { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Request for bulk auto-renewal operations
/// </summary>
public class BulkAutoRenewalOperationRequest
{
    public List<Guid> SubscriptionIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // enable, disable, process, cancel
    public Dictionary<string, object> OperationParameters { get; set; } = new();
    public Guid RequestedBy { get; set; }
}

/// <summary>
/// Result of bulk auto-renewal operations
/// </summary>
public class BulkAutoRenewalOperationResult
{
    public bool IsSuccess { get; set; }
    public int TotalProcessed { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public List<BulkOperationItemResult> ItemResults { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public TimeSpan TotalProcessingTime { get; set; }
}

/// <summary>
/// Individual item result in bulk operation
/// </summary>
public class BulkOperationItemResult
{
    public Guid SubscriptionId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> OperationData { get; set; } = new();
}
