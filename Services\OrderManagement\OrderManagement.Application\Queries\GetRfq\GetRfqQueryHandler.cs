using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetRfq;

public class GetRfqQueryHandler : IRequestHandler<GetRfqQuery, RfqDetailDto?>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRfqQueryHandler> _logger;

    public GetRfqQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetRfqQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<RfqDetailDto?> Handle(GetRfqQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting RFQ {RfqId} for user {UserId}", 
            request.RfqId, request.RequestingUserId);

        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
            return null;
        }

        // TODO: Add authorization logic here
        // Check if the requesting user has permission to view this RFQ
        // For now, we'll allow all authenticated users to view published RFQs

        var rfqDto = _mapper.Map<RfqDetailDto>(rfq);
        
        _logger.LogInformation("Successfully retrieved RFQ {RfqId}", request.RfqId);
        
        return rfqDto;
    }
}
