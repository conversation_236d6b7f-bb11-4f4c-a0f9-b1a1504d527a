using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence;

public class AlertConfiguration : IEntityTypeConfiguration<Alert>
{
    public void Configure(EntityTypeBuilder<Alert> builder)
    {
        builder.ToTable("alerts", "analytics");

        builder.HasKey(a => a.Id);

        builder.Property(a => a.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(a => a.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(a => a.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(a => a.Severity)
            .HasColumnName("severity")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(a => a.Message)
            .HasColumnName("message")
            .HasMaxLength(2000)
            .IsRequired();

        builder.Property(a => a.MetricId)
            .HasColumnName("metric_id");

        builder.Property(a => a.MetricName)
            .HasColumnName("metric_name")
            .HasMaxLength(200);

        builder.Property(a => a.TriggerValue)
            .HasColumnName("trigger_value")
            .HasPrecision(18, 6);

        builder.Property(a => a.ThresholdValue)
            .HasColumnName("threshold_value")
            .HasPrecision(18, 6);

        builder.Property(a => a.TriggeredAt)
            .HasColumnName("triggered_at")
            .IsRequired();

        builder.Property(a => a.ResolvedAt)
            .HasColumnName("resolved_at");

        builder.Property(a => a.ResolvedBy)
            .HasColumnName("resolved_by");

        builder.Property(a => a.Resolution)
            .HasColumnName("resolution")
            .HasMaxLength(2000);

        builder.Property(a => a.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(a => a.IsAcknowledged)
            .HasColumnName("is_acknowledged")
            .IsRequired();

        builder.Property(a => a.AcknowledgedBy)
            .HasColumnName("acknowledged_by");

        builder.Property(a => a.AcknowledgedAt)
            .HasColumnName("acknowledged_at");

        builder.Property(a => a.Context)
            .HasColumnName("context")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(a => a.NotifiedUsers)
            .HasColumnName("notified_users")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("jsonb");

        builder.Property(a => a.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(a => a.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(a => new { a.IsActive, a.Severity })
            .HasDatabaseName("IX_alerts_is_active_severity");

        builder.HasIndex(a => a.TriggeredAt)
            .HasDatabaseName("IX_alerts_triggered_at");

        builder.HasIndex(a => a.MetricId)
            .HasDatabaseName("IX_alerts_metric_id");

        builder.HasIndex(a => a.IsAcknowledged)
            .HasDatabaseName("IX_alerts_is_acknowledged");

        // GIN index for context
        builder.HasIndex(a => a.Context)
            .HasDatabaseName("IX_alerts_context_gin")
            .HasMethod("gin");
    }
}

public class AlertRuleConfiguration : IEntityTypeConfiguration<AlertRule>
{
    public void Configure(EntityTypeBuilder<AlertRule> builder)
    {
        builder.ToTable("alert_rules", "analytics");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(r => r.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(r => r.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(r => r.MetricName)
            .HasColumnName("metric_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(r => r.Category)
            .HasColumnName("category")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.Severity)
            .HasColumnName("severity")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(r => r.Condition)
            .HasColumnName("condition")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(r => r.ThresholdValue)
            .HasColumnName("threshold_value")
            .HasPrecision(18, 6)
            .IsRequired();

        builder.Property(r => r.EvaluationWindow)
            .HasColumnName("evaluation_window")
            .IsRequired();

        builder.Property(r => r.IsEnabled)
            .HasColumnName("is_enabled")
            .IsRequired();

        builder.Property(r => r.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(r => r.UserType)
            .HasColumnName("user_type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.NotificationTargets)
            .HasColumnName("notification_targets")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<Guid>())
            .HasColumnType("jsonb");

        builder.Property(r => r.Configuration)
            .HasColumnName("configuration")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(r => r.LastEvaluatedAt)
            .HasColumnName("last_evaluated_at");

        builder.Property(r => r.LastTriggeredAt)
            .HasColumnName("last_triggered_at");

        builder.Property(r => r.TriggerCount)
            .HasColumnName("trigger_count")
            .IsRequired();

        builder.Property(r => r.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(r => r.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(r => new { r.MetricName, r.IsEnabled })
            .HasDatabaseName("IX_alert_rules_metric_name_is_enabled");

        builder.HasIndex(r => new { r.CreatedBy, r.UserType })
            .HasDatabaseName("IX_alert_rules_created_by_user_type");

        builder.HasIndex(r => r.Category)
            .HasDatabaseName("IX_alert_rules_category");

        builder.HasIndex(r => r.LastEvaluatedAt)
            .HasDatabaseName("IX_alert_rules_last_evaluated_at");
    }
}
