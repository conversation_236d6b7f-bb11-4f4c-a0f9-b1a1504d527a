using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UserManagement.Domain.Entities;

namespace UserManagement.Domain.Repositories
{
    public interface IDocumentSubmissionRepository
    {
        Task<DocumentSubmission> GetByIdAsync(Guid id);
        Task<DocumentSubmission> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<DocumentSubmission>> GetByStatusAsync(DocumentStatus status);
        Task<IEnumerable<DocumentSubmission>> GetPendingReviewAsync();
        Task<IEnumerable<DocumentSubmission>> GetByReviewerAsync(Guid reviewerId);
        Task<IEnumerable<DocumentSubmission>> GetByUserTypeAsync(UserType userType);
        Task<bool> ExistsAsync(Guid userId);
        Task AddAsync(DocumentSubmission documentSubmission);
        Task UpdateAsync(DocumentSubmission documentSubmission);
        Task DeleteAsync(Guid id);
        Task<int> GetCountByStatusAsync(DocumentStatus status);
        Task<int> GetPendingDocumentsCountAsync();
        Task<IEnumerable<DocumentSubmission>> GetSubmissionsRequiringAttentionAsync();
        Task<IEnumerable<DocumentSubmission>> GetRecentSubmissionsAsync(int count = 10);

        // Export-specific method
        Task<List<DocumentSubmission>> GetDocumentSubmissionsAsync(Dictionary<string, object> filters);

        // Additional method for document retrieval
        Task<DocumentSubmission> GetDocumentByIdAsync(Guid documentId);
    }
}
