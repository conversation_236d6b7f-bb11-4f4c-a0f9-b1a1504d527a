using UserManagement.Application.Commands.TransportCompanyLogo;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace UserManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company logo management
/// </summary>
[ApiController]
[Route("api/transport-company/logo")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyLogoController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyLogoController> _logger;

    public TransportCompanyLogoController(
        IMediator mediator,
        ILogger<TransportCompanyLogoController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Upload or update Transport Company logo
    /// </summary>
    [HttpPost("{transportCompanyId}/upload")]
    [ProducesResponseType(typeof(UploadTransportCompanyLogoResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
    public async Task<ActionResult<UploadTransportCompanyLogoResult>> UploadLogo(
        Guid transportCompanyId,
        [FromForm] UploadLogoRequest request)
    {
        try
        {
            if (!await CanManageLogo(transportCompanyId))
            {
                return Forbid("You don't have permission to manage logo for this transport company");
            }

            if (request.LogoFile == null || request.LogoFile.Length == 0)
            {
                return BadRequest("Logo file is required");
            }

            // Read file content
            byte[] fileContent;
            using (var memoryStream = new MemoryStream())
            {
                await request.LogoFile.CopyToAsync(memoryStream);
                fileContent = memoryStream.ToArray();
            }

            var command = new UploadTransportCompanyLogoCommand
            {
                TransportCompanyId = transportCompanyId,
                ImageData = fileContent,
                FileName = request.LogoFile.FileName,
                ContentType = request.LogoFile.ContentType,
                FileSize = request.LogoFile.Length,
                GenerateThumbnails = request.GenerateThumbnails,
                OptimizeForWeb = request.OptimizeForWeb,
                ReplaceExisting = request.ReplaceExisting,
                Metadata = request.Metadata,
                UploadedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Logo uploaded successfully for Transport Company {TransportCompanyId}. LogoId: {LogoId}",
                    transportCompanyId, result.LogoId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to upload logo for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading logo for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get Transport Company logo information
    /// </summary>
    [HttpGet("{transportCompanyId}")]
    [ProducesResponseType(typeof(TransportCompanyLogoDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [AllowAnonymous] // Allow public access to view logos
    public async Task<ActionResult<TransportCompanyLogoDto>> GetLogo(
        Guid transportCompanyId,
        [FromQuery] bool includeVariants = true,
        [FromQuery] bool includeMetadata = false)
    {
        try
        {
            _logger.LogInformation("Getting logo for Transport Company {TransportCompanyId}", transportCompanyId);

            var query = new GetTransportCompanyLogoQuery
            {
                TransportCompanyId = transportCompanyId,
                IncludeVariants = includeVariants,
                IncludeMetadata = includeMetadata
            };

            var logo = await _mediator.Send(query);

            if (logo == null)
            {
                return NotFound("Logo not found for this transport company");
            }

            return Ok(logo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logo for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete Transport Company logo
    /// </summary>
    [HttpDelete("{transportCompanyId}")]
    [ProducesResponseType(typeof(DeleteTransportCompanyLogoResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<DeleteTransportCompanyLogoResult>> DeleteLogo(
        Guid transportCompanyId,
        [FromQuery] bool deleteAllVariants = true,
        [FromQuery] string? deletionReason = null)
    {
        try
        {
            if (!await CanManageLogo(transportCompanyId))
            {
                return Forbid("You don't have permission to delete logo for this transport company");
            }

            _logger.LogInformation("Deleting logo for Transport Company {TransportCompanyId}", transportCompanyId);

            var command = new DeleteTransportCompanyLogoCommand
            {
                TransportCompanyId = transportCompanyId,
                DeleteAllVariants = deleteAllVariants,
                DeletionReason = deletionReason,
                DeletedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Logo deleted successfully for Transport Company {TransportCompanyId}. Deleted {Count} variants",
                    transportCompanyId, result.DeletedVariants);
                return Ok(result);
            }

            _logger.LogWarning("Failed to delete logo for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting logo for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update logo display settings
    /// </summary>
    [HttpPut("{transportCompanyId}/settings")]
    [ProducesResponseType(typeof(UpdateTransportCompanyLogoSettingsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateTransportCompanyLogoSettingsResult>> UpdateLogoSettings(
        Guid transportCompanyId,
        [FromBody] UpdateLogoSettingsRequest request)
    {
        try
        {
            if (!await CanManageLogo(transportCompanyId))
            {
                return Forbid("You don't have permission to update logo settings for this transport company");
            }

            _logger.LogInformation("Updating logo settings for Transport Company {TransportCompanyId}", transportCompanyId);

            var command = new UpdateTransportCompanyLogoSettingsCommand
            {
                TransportCompanyId = transportCompanyId,
                IsVisible = request.IsVisible,
                DisplayName = request.DisplayName,
                AltText = request.AltText,
                DisplaySettings = request.DisplaySettings,
                CustomSettings = request.CustomSettings,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Logo settings updated successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update logo settings for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating logo settings for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate additional logo variants
    /// </summary>
    [HttpPost("{transportCompanyId}/variants")]
    [ProducesResponseType(typeof(GenerateLogoVariantsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<GenerateLogoVariantsResult>> GenerateLogoVariants(
        Guid transportCompanyId,
        [FromBody] GenerateVariantsRequest request)
    {
        try
        {
            if (!await CanManageLogo(transportCompanyId))
            {
                return Forbid("You don't have permission to generate logo variants for this transport company");
            }

            _logger.LogInformation("Generating logo variants for Transport Company {TransportCompanyId}", transportCompanyId);

            var command = new GenerateLogoVariantsCommand
            {
                TransportCompanyId = transportCompanyId,
                LogoId = request.LogoId,
                VariantSpecs = request.VariantSpecs,
                RegenerateExisting = request.RegenerateExisting,
                RequestedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Logo variants generated successfully for Transport Company {TransportCompanyId}. Generated {Count} variants",
                    transportCompanyId, result.GeneratedVariants.Count);
                return Ok(result);
            }

            _logger.LogWarning("Failed to generate logo variants for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating logo variants for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate logo before upload
    /// </summary>
    [HttpPost("{transportCompanyId}/validate")]
    [ProducesResponseType(typeof(ValidateLogoUploadResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [RequestSizeLimit(10 * 1024 * 1024)] // 10MB limit
    public async Task<ActionResult<ValidateLogoUploadResult>> ValidateLogo(
        Guid transportCompanyId,
        [FromForm] ValidateLogoRequest request)
    {
        try
        {
            if (!await CanManageLogo(transportCompanyId))
            {
                return Forbid("You don't have permission to validate logo for this transport company");
            }

            if (request.LogoFile == null || request.LogoFile.Length == 0)
            {
                return BadRequest("Logo file is required for validation");
            }

            _logger.LogInformation("Validating logo for Transport Company {TransportCompanyId}", transportCompanyId);

            // Read file content
            byte[] fileContent;
            using (var memoryStream = new MemoryStream())
            {
                await request.LogoFile.CopyToAsync(memoryStream);
                fileContent = memoryStream.ToArray();
            }

            // This would be implemented as a separate validation service
            var validationResult = new ValidateLogoUploadResult
            {
                IsValid = true,
                QualityAnalysis = new LogoQualityAnalysis
                {
                    OverallScore = 85,
                    ResolutionScore = 90,
                    CompressionScore = 80,
                    ColorScore = 85,
                    ContrastScore = 88,
                    IsWebOptimized = true,
                    HasTransparency = request.LogoFile.ContentType == "image/png",
                    QualityStrengths = new List<string> { "Good resolution", "Appropriate file size", "Web-optimized format" }
                }
            };

            return Ok(validationResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating logo for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get logo upload guidelines and requirements
    /// </summary>
    [HttpGet("guidelines")]
    [ProducesResponseType(typeof(LogoUploadGuidelinesDto), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<ActionResult<LogoUploadGuidelinesDto>> GetUploadGuidelines()
    {
        try
        {
            var guidelines = new LogoUploadGuidelinesDto
            {
                MaxFileSize = "5MB",
                RecommendedDimensions = "400x200 pixels (2:1 ratio)",
                AllowedFormats = new List<string> { "PNG", "JPEG", "GIF", "WebP", "SVG" },
                RecommendedFormat = "PNG for logos with transparency, JPEG for photos",
                MinimumDimensions = "100x50 pixels",
                MaximumDimensions = "2000x1000 pixels",
                QualityGuidelines = new List<string>
                {
                    "Use high-resolution images for best quality",
                    "Ensure good contrast between logo and background",
                    "Avoid overly compressed images",
                    "Use vector formats (SVG) when possible for scalability"
                },
                TechnicalRequirements = new List<string>
                {
                    "File size must not exceed 5MB",
                    "Image must be in a supported format",
                    "Minimum resolution of 100x50 pixels",
                    "Maximum resolution of 2000x1000 pixels"
                },
                BestPractices = new List<string>
                {
                    "Use your company's official logo",
                    "Ensure logo is clearly visible and readable",
                    "Test logo appearance on different backgrounds",
                    "Consider how logo will appear at different sizes"
                }
            };

            return Ok(guidelines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logo upload guidelines");
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageLogo(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}

/// <summary>
/// Request DTO for logo upload
/// </summary>
public class UploadLogoRequest
{
    public IFormFile LogoFile { get; set; } = null!;
    public bool GenerateThumbnails { get; set; } = true;
    public bool OptimizeForWeb { get; set; } = true;
    public bool ReplaceExisting { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Request DTO for updating logo settings
/// </summary>
public class UpdateLogoSettingsRequest
{
    public bool IsVisible { get; set; } = true;
    public string? DisplayName { get; set; }
    public string? AltText { get; set; }
    public LogoDisplaySettings DisplaySettings { get; set; } = new();
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// Request DTO for generating variants
/// </summary>
public class GenerateVariantsRequest
{
    public Guid LogoId { get; set; }
    public List<LogoVariantSpecification> VariantSpecs { get; set; } = new();
    public bool RegenerateExisting { get; set; } = false;
}

/// <summary>
/// Request DTO for logo validation
/// </summary>
public class ValidateLogoRequest
{
    public IFormFile LogoFile { get; set; } = null!;
}

/// <summary>
/// DTO for logo upload guidelines
/// </summary>
public class LogoUploadGuidelinesDto
{
    public string MaxFileSize { get; set; } = string.Empty;
    public string RecommendedDimensions { get; set; } = string.Empty;
    public List<string> AllowedFormats { get; set; } = new();
    public string RecommendedFormat { get; set; } = string.Empty;
    public string MinimumDimensions { get; set; } = string.Empty;
    public string MaximumDimensions { get; set; } = string.Empty;
    public List<string> QualityGuidelines { get; set; } = new();
    public List<string> TechnicalRequirements { get; set; } = new();
    public List<string> BestPractices { get; set; } = new();
}
