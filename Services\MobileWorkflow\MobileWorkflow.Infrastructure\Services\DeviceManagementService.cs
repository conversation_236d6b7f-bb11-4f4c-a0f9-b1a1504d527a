using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IDeviceManagementService
{
    Task<ManagedDeviceDto> EnrollDeviceAsync(EnrollDeviceRequest request, CancellationToken cancellationToken = default);
    Task<ManagedDeviceDto?> GetDeviceAsync(Guid deviceId, CancellationToken cancellationToken = default);
    Task<List<ManagedDeviceDto>> GetDevicesAsync(Guid? userId = null, string? platform = null, string? status = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateDeviceAsync(Guid deviceId, UpdateDeviceRequest request, CancellationToken cancellationToken = default);
    Task<bool> UnenrollDeviceAsync(Guid deviceId, CancellationToken cancellationToken = default);
    Task<DevicePolicyDto> CreatePolicyAsync(CreateDevicePolicyRequest request, CancellationToken cancellationToken = default);
    Task<DevicePolicyDto?> GetPolicyAsync(Guid policyId, CancellationToken cancellationToken = default);
    Task<List<DevicePolicyDto>> GetPoliciesAsync(string? platform = null, string? category = null, CancellationToken cancellationToken = default);
    Task<bool> UpdatePolicyAsync(Guid policyId, UpdateDevicePolicyRequest request, CancellationToken cancellationToken = default);
    Task<bool> AssignPolicyAsync(Guid deviceId, Guid policyId, CancellationToken cancellationToken = default);
    Task<bool> UnassignPolicyAsync(Guid deviceId, Guid policyId, CancellationToken cancellationToken = default);
    Task<DeviceCommandDto> SendCommandAsync(SendDeviceCommandRequest request, CancellationToken cancellationToken = default);
    Task<List<DeviceCommandDto>> GetCommandsAsync(Guid deviceId, string? status = null, CancellationToken cancellationToken = default);
    Task<bool> UpdateCommandStatusAsync(Guid commandId, UpdateCommandStatusRequest request, CancellationToken cancellationToken = default);
    Task<DeviceComplianceResultDto> CheckComplianceAsync(Guid deviceId, CancellationToken cancellationToken = default);
    Task<List<DeviceComplianceResultDto>> GetComplianceHistoryAsync(Guid deviceId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<DeviceInventoryDto> GetDeviceInventoryAsync(CancellationToken cancellationToken = default);
    Task<List<DeviceSecurityAlertDto>> GetSecurityAlertsAsync(string? severity = null, CancellationToken cancellationToken = default);
    Task ProcessExpiredCommandsAsync(CancellationToken cancellationToken = default);
    Task ProcessComplianceChecksAsync(CancellationToken cancellationToken = default);
}

public class DeviceManagementService : IDeviceManagementService
{
    private readonly IManagedDeviceRepository _deviceRepository;
    private readonly IDevicePolicyRepository _policyRepository;
    private readonly IDeviceCommandRepository _commandRepository;
    private readonly IDevicePolicyAssignmentRepository _assignmentRepository;
    private readonly IDeviceComplianceCheckRepository _complianceRepository;
    private readonly IDeviceCommandService _commandService;
    private readonly ILogger<DeviceManagementService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public DeviceManagementService(
        IManagedDeviceRepository deviceRepository,
        IDevicePolicyRepository policyRepository,
        IDeviceCommandRepository commandRepository,
        IDevicePolicyAssignmentRepository assignmentRepository,
        IDeviceComplianceCheckRepository complianceRepository,
        IDeviceCommandService commandService,
        ILogger<DeviceManagementService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _deviceRepository = deviceRepository;
        _policyRepository = policyRepository;
        _commandRepository = commandRepository;
        _assignmentRepository = assignmentRepository;
        _complianceRepository = complianceRepository;
        _commandService = commandService;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<ManagedDeviceDto> EnrollDeviceAsync(EnrollDeviceRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Enrolling device {DeviceId} for user {UserId}", request.DeviceId, request.UserId);

            // Check if device is already enrolled
            var existingDevice = await _deviceRepository.GetByDeviceIdAsync(request.DeviceId, cancellationToken);
            if (existingDevice != null)
            {
                if (existingDevice.UserId == request.UserId)
                {
                    // Update existing device
                    existingDevice.UpdateDeviceInfo(request.DeviceInfo, request.AppVersion);
                    await _deviceRepository.SaveChangesAsync(cancellationToken);
                    return MapToManagedDeviceDto(existingDevice);
                }
                else
                {
                    // Device moved to different user
                    existingDevice.Unenroll();
                }
            }

            var device = new ManagedDevice(
                request.UserId,
                request.DeviceId,
                request.DeviceName,
                request.Platform,
                request.PlatformVersion,
                request.AppVersion,
                request.DeviceInfo);

            if (request.SecurityInfo != null)
            {
                device.UpdateSecurityInfo(request.SecurityInfo);
            }

            _deviceRepository.Add(device);
            await _deviceRepository.SaveChangesAsync(cancellationToken);

            // Assign default policies
            await AssignDefaultPoliciesAsync(device, cancellationToken);

            // Trigger initial compliance check
            _ = Task.Run(async () => await CheckComplianceAsync(device.Id, CancellationToken.None));

            _logger.LogInformation("Device {DeviceId} enrolled successfully for user {UserId}", request.DeviceId, request.UserId);

            return MapToManagedDeviceDto(device);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enrolling device {DeviceId} for user {UserId}", request.DeviceId, request.UserId);
            throw;
        }
    }

    public async Task<ManagedDeviceDto?> GetDeviceAsync(Guid deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null)
            {
                return null;
            }

            return MapToManagedDeviceDto(device);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device {DeviceId}", deviceId);
            return null;
        }
    }

    public async Task<List<ManagedDeviceDto>> GetDevicesAsync(Guid? userId = null, string? platform = null, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var devices = await _deviceRepository.GetDevicesAsync(userId, platform, status, cancellationToken);
            return devices.Select(MapToManagedDeviceDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting devices");
            return new List<ManagedDeviceDto>();
        }
    }

    public async Task<bool> UpdateDeviceAsync(Guid deviceId, UpdateDeviceRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null)
            {
                return false;
            }

            if (request.DeviceInfo != null)
            {
                device.UpdateDeviceInfo(request.DeviceInfo, request.AppVersion ?? device.AppVersion);
            }

            if (request.SecurityInfo != null)
            {
                device.UpdateSecurityInfo(request.SecurityInfo);
            }

            if (request.Configuration != null)
            {
                device.UpdateConfiguration(request.Configuration);
            }

            if (!string.IsNullOrEmpty(request.DeviceName))
            {
                device.SetDeviceName(request.DeviceName);
            }

            await _deviceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Device {DeviceId} updated successfully", deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating device {DeviceId}", deviceId);
            return false;
        }
    }

    public async Task<bool> UnenrollDeviceAsync(Guid deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null)
            {
                return false;
            }

            device.Unenroll();
            await _deviceRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Device {DeviceId} unenrolled successfully", deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unenrolling device {DeviceId}", deviceId);
            return false;
        }
    }

    public async Task<DevicePolicyDto> CreatePolicyAsync(CreateDevicePolicyRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating device policy {Name} for platform {Platform}", request.Name, request.Platform);

            var policy = new DevicePolicy(
                request.Name,
                request.DisplayName,
                request.Description,
                request.Category,
                request.Platform,
                request.Version,
                request.Rules,
                request.CreatedBy);

            if (request.Configuration != null)
            {
                policy.UpdateConfiguration(request.Configuration, request.CreatedBy);
            }

            if (request.Enforcement != null)
            {
                policy.UpdateEnforcement(request.Enforcement, request.CreatedBy);
            }

            if (request.Conditions != null)
            {
                policy.UpdateConditions(request.Conditions, request.CreatedBy);
            }

            if (request.TargetGroups != null)
            {
                foreach (var group in request.TargetGroups)
                {
                    policy.AddTargetGroup(group);
                }
            }

            if (request.Priority.HasValue)
            {
                policy.SetPriority(request.Priority.Value, request.CreatedBy);
            }

            _policyRepository.Add(policy);
            await _policyRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Device policy {PolicyId} created successfully", policy.Id);

            return MapToDevicePolicyDto(policy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating device policy {Name}", request.Name);
            throw;
        }
    }

    public async Task<DevicePolicyDto?> GetPolicyAsync(Guid policyId, CancellationToken cancellationToken = default)
    {
        try
        {
            var policy = await _policyRepository.GetByIdAsync(policyId, cancellationToken);
            if (policy == null)
            {
                return null;
            }

            return MapToDevicePolicyDto(policy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device policy {PolicyId}", policyId);
            return null;
        }
    }

    public async Task<List<DevicePolicyDto>> GetPoliciesAsync(string? platform = null, string? category = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var policies = await _policyRepository.GetPoliciesAsync(platform, category, cancellationToken);
            return policies.Select(MapToDevicePolicyDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device policies");
            return new List<DevicePolicyDto>();
        }
    }

    public async Task<bool> UpdatePolicyAsync(Guid policyId, UpdateDevicePolicyRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var policy = await _policyRepository.GetByIdAsync(policyId, cancellationToken);
            if (policy == null)
            {
                return false;
            }

            if (request.Rules != null)
            {
                policy.UpdateRules(request.Rules, request.UpdatedBy);
            }

            if (request.Configuration != null)
            {
                policy.UpdateConfiguration(request.Configuration, request.UpdatedBy);
            }

            if (request.Enforcement != null)
            {
                policy.UpdateEnforcement(request.Enforcement, request.UpdatedBy);
            }

            if (request.Conditions != null)
            {
                policy.UpdateConditions(request.Conditions, request.UpdatedBy);
            }

            if (request.Priority.HasValue)
            {
                policy.SetPriority(request.Priority.Value, request.UpdatedBy);
            }

            await _policyRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Device policy {PolicyId} updated successfully", policyId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating device policy {PolicyId}", policyId);
            return false;
        }
    }

    public async Task<bool> AssignPolicyAsync(Guid deviceId, Guid policyId, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            var policy = await _policyRepository.GetByIdAsync(policyId, cancellationToken);

            if (device == null || policy == null)
            {
                return false;
            }

            // Check if policy is applicable to device platform
            if (!policy.AppliesToPlatform(device.Platform))
            {
                _logger.LogWarning("Policy {PolicyId} does not apply to platform {Platform}", policyId, device.Platform);
                return false;
            }

            device.AssignPolicy(policyId);

            var assignment = new DevicePolicyAssignment(deviceId, policyId, "System");
            _assignmentRepository.Add(assignment);

            await _deviceRepository.SaveChangesAsync(cancellationToken);
            await _assignmentRepository.SaveChangesAsync(cancellationToken);

            // Send policy update command to device
            await SendPolicyUpdateCommandAsync(deviceId, cancellationToken);

            _logger.LogInformation("Policy {PolicyId} assigned to device {DeviceId}", policyId, deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning policy {PolicyId} to device {DeviceId}", policyId, deviceId);
            return false;
        }
    }

    public async Task<bool> UnassignPolicyAsync(Guid deviceId, Guid policyId, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null)
            {
                return false;
            }

            device.UnassignPolicy(policyId);

            var assignment = await _assignmentRepository.GetByDeviceAndPolicyAsync(deviceId, policyId, cancellationToken);
            if (assignment != null)
            {
                _assignmentRepository.Remove(assignment);
            }

            await _deviceRepository.SaveChangesAsync(cancellationToken);
            await _assignmentRepository.SaveChangesAsync(cancellationToken);

            // Send policy update command to device
            await SendPolicyUpdateCommandAsync(deviceId, cancellationToken);

            _logger.LogInformation("Policy {PolicyId} unassigned from device {DeviceId}", policyId, deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning policy {PolicyId} from device {DeviceId}", policyId, deviceId);
            return false;
        }
    }

    public async Task<DeviceCommandDto> SendCommandAsync(SendDeviceCommandRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending command {CommandType} to device {DeviceId}", request.CommandType, request.DeviceId);

            var device = await _deviceRepository.GetByIdAsync(request.DeviceId, cancellationToken);
            if (device == null)
            {
                throw new InvalidOperationException("Device not found");
            }

            var command = new DeviceCommand(
                request.DeviceId,
                request.CommandType,
                request.CommandName,
                request.Parameters,
                request.CreatedBy,
                request.ExpiresIn);

            _commandRepository.Add(command);
            await _commandRepository.SaveChangesAsync(cancellationToken);

            // Send command to device
            var sendResult = await _commandService.SendCommandToDeviceAsync(command, cancellationToken);
            if (sendResult.IsSuccess)
            {
                command.MarkAsSent();
                await _commandRepository.SaveChangesAsync(cancellationToken);
            }
            else
            {
                command.MarkAsFailed(sendResult.ErrorMessage, sendResult.ErrorCode);
                await _commandRepository.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("Command {CommandId} sent to device {DeviceId} with result: {IsSuccess}",
                command.Id, request.DeviceId, sendResult.IsSuccess);

            return MapToDeviceCommandDto(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending command {CommandType} to device {DeviceId}", request.CommandType, request.DeviceId);
            throw;
        }
    }

    public async Task<List<DeviceCommandDto>> GetCommandsAsync(Guid deviceId, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var commands = await _commandRepository.GetByDeviceIdAsync(deviceId, status, cancellationToken);
            return commands.Select(MapToDeviceCommandDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting commands for device {DeviceId}", deviceId);
            return new List<DeviceCommandDto>();
        }
    }

    public async Task<bool> UpdateCommandStatusAsync(Guid commandId, UpdateCommandStatusRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var command = await _commandRepository.GetByIdAsync(commandId, cancellationToken);
            if (command == null)
            {
                return false;
            }

            switch (request.Status.ToLowerInvariant())
            {
                case "acknowledged":
                    command.MarkAsAcknowledged();
                    break;
                case "completed":
                    command.MarkAsCompleted(request.Result ?? new Dictionary<string, object>());
                    break;
                case "failed":
                    command.MarkAsFailed(request.ErrorMessage ?? "Unknown error", request.ErrorCode);
                    break;
                default:
                    return false;
            }

            await _commandRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Command {CommandId} status updated to {Status}", commandId, request.Status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating command {CommandId} status", commandId);
            return false;
        }
    }

    public async Task<DeviceComplianceResultDto> CheckComplianceAsync(Guid deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null)
            {
                throw new InvalidOperationException("Device not found");
            }

            var policies = await _policyRepository.GetByIdsAsync(device.AssignedPolicyIds, cancellationToken);
            var deviceState = GetDeviceStateForCompliance(device);

            var overallCompliant = true;
            var allViolations = new List<string>();
            var policyResults = new List<PolicyComplianceResult>();

            foreach (var policy in policies.Where(p => p.IsActive))
            {
                if (policy.EvaluateConditions(deviceState))
                {
                    var policyResult = policy.CheckCompliance(deviceState);
                    policyResults.Add(policyResult);

                    if (!policyResult.IsCompliant)
                    {
                        overallCompliant = false;
                        allViolations.AddRange(policyResult.Violations);
                    }
                }
            }

            // Update device compliance status
            var complianceStatus = new Dictionary<string, object>
            {
                ["isCompliant"] = overallCompliant,
                ["violations"] = allViolations,
                ["lastChecked"] = DateTime.UtcNow,
                ["policyResults"] = policyResults.Select(pr => new
                {
                    policyId = pr.PolicyId,
                    policyName = pr.PolicyName,
                    isCompliant = pr.IsCompliant,
                    violations = pr.Violations
                }).ToList()
            };

            device.UpdateComplianceStatus(complianceStatus, overallCompliant);

            // Create compliance check record
            var complianceCheck = new DeviceComplianceCheck(
                deviceId,
                overallCompliant,
                complianceStatus,
                allViolations,
                deviceState,
                "Manual",
                "System");

            _complianceRepository.Add(complianceCheck);

            await _deviceRepository.SaveChangesAsync(cancellationToken);
            await _complianceRepository.SaveChangesAsync(cancellationToken);

            // Handle non-compliance
            if (!overallCompliant)
            {
                await HandleNonComplianceAsync(device, allViolations, cancellationToken);
            }

            _logger.LogInformation("Compliance check completed for device {DeviceId}. Compliant: {IsCompliant}, Violations: {ViolationCount}",
                deviceId, overallCompliant, allViolations.Count);

            return new DeviceComplianceResultDto
            {
                DeviceId = deviceId,
                IsCompliant = overallCompliant,
                Violations = allViolations,
                PolicyResults = policyResults.Select(pr => new PolicyComplianceDto
                {
                    PolicyId = pr.PolicyId,
                    PolicyName = pr.PolicyName,
                    IsCompliant = pr.IsCompliant,
                    Violations = pr.Violations
                }).ToList(),
                CheckedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking compliance for device {DeviceId}", deviceId);
            throw;
        }
    }

    public async Task<List<DeviceComplianceResultDto>> GetComplianceHistoryAsync(Guid deviceId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var complianceChecks = await _complianceRepository.GetByDeviceIdAsync(deviceId, from, cancellationToken);

            return complianceChecks.Select(cc => new DeviceComplianceResultDto
            {
                DeviceId = cc.DeviceId,
                IsCompliant = cc.IsCompliant,
                Violations = cc.Violations,
                CheckedAt = cc.CheckedAt,
                CheckType = cc.CheckType,
                TriggeredBy = cc.TriggeredBy
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance history for device {DeviceId}", deviceId);
            return new List<DeviceComplianceResultDto>();
        }
    }

    public async Task<DeviceInventoryDto> GetDeviceInventoryAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var devices = await _deviceRepository.GetAllManagedAsync(cancellationToken);

            var inventory = new DeviceInventoryDto
            {
                TotalDevices = devices.Count,
                ActiveDevices = devices.Count(d => d.Status == "Active"),
                ComplianceRate = devices.Count > 0 ? (double)devices.Count(d => d.IsCompliant) / devices.Count * 100 : 0,
                PlatformBreakdown = devices.GroupBy(d => d.Platform).ToDictionary(g => g.Key, g => g.Count()),
                StatusBreakdown = devices.GroupBy(d => d.Status).ToDictionary(g => g.Key, g => g.Count()),
                OnlineDevices = devices.Count(d => d.IsOnline(TimeSpan.FromMinutes(15))),
                StaleDevices = devices.Count(d => d.IsStale(TimeSpan.FromDays(7))),
                RiskBreakdown = devices.GroupBy(d => d.GetRiskLevel()).ToDictionary(g => g.Key, g => g.Count()),
                RecentEnrollments = devices.Count(d => d.EnrolledAt > DateTime.UtcNow.AddDays(-7)),
                GeneratedAt = DateTime.UtcNow
            };

            return inventory;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device inventory");
            return new DeviceInventoryDto { GeneratedAt = DateTime.UtcNow };
        }
    }

    public async Task<List<DeviceSecurityAlertDto>> GetSecurityAlertsAsync(string? severity = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var devices = await _deviceRepository.GetAllManagedAsync(cancellationToken);
            var alerts = new List<DeviceSecurityAlertDto>();

            foreach (var device in devices)
            {
                // Check for security issues
                if (device.Status == "Compromised")
                {
                    alerts.Add(new DeviceSecurityAlertDto
                    {
                        DeviceId = device.Id,
                        DeviceName = device.DeviceName,
                        UserId = device.UserId,
                        AlertType = "Device Compromised",
                        Severity = "Critical",
                        Message = $"Device {device.DeviceName} has been marked as compromised",
                        DetectedAt = device.GetMetadata<DateTime>("compromised_at", DateTime.UtcNow),
                        Status = "Active"
                    });
                }

                if (device.GetSecurityInfo<bool>("jailbroken", false))
                {
                    alerts.Add(new DeviceSecurityAlertDto
                    {
                        DeviceId = device.Id,
                        DeviceName = device.DeviceName,
                        UserId = device.UserId,
                        AlertType = "Jailbroken Device",
                        Severity = "High",
                        Message = $"Device {device.DeviceName} is jailbroken/rooted",
                        DetectedAt = device.LastSeen,
                        Status = "Active"
                    });
                }

                if (!device.IsCompliant)
                {
                    alerts.Add(new DeviceSecurityAlertDto
                    {
                        DeviceId = device.Id,
                        DeviceName = device.DeviceName,
                        UserId = device.UserId,
                        AlertType = "Non-Compliant Device",
                        Severity = "Medium",
                        Message = $"Device {device.DeviceName} is not compliant with policies",
                        DetectedAt = device.LastSeen,
                        Status = "Active"
                    });
                }

                if (device.IsStale(TimeSpan.FromDays(30)))
                {
                    alerts.Add(new DeviceSecurityAlertDto
                    {
                        DeviceId = device.Id,
                        DeviceName = device.DeviceName,
                        UserId = device.UserId,
                        AlertType = "Stale Device",
                        Severity = "Low",
                        Message = $"Device {device.DeviceName} has not been seen for over 30 days",
                        DetectedAt = device.LastSeen,
                        Status = "Active"
                    });
                }
            }

            if (!string.IsNullOrEmpty(severity))
            {
                alerts = alerts.Where(a => a.Severity.Equals(severity, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return alerts.OrderByDescending(a => a.DetectedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting security alerts");
            return new List<DeviceSecurityAlertDto>();
        }
    }

    public async Task ProcessExpiredCommandsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var expiredCommands = await _commandRepository.GetExpiredAsync(cancellationToken);

            foreach (var command in expiredCommands)
            {
                command.MarkAsExpired();
                _logger.LogInformation("Command {CommandId} marked as expired", command.Id);
            }

            if (expiredCommands.Any())
            {
                await _commandRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Processed {Count} expired commands", expiredCommands.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing expired commands");
        }
    }

    public async Task ProcessComplianceChecksAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var devices = await _deviceRepository.GetActiveDevicesAsync(cancellationToken);
            var checkInterval = _configuration.GetValue<int>("DeviceManagement:ComplianceCheckIntervalHours", 24);

            foreach (var device in devices)
            {
                var lastCheck = await _complianceRepository.GetLatestByDeviceIdAsync(device.Id, cancellationToken);
                if (lastCheck == null || DateTime.UtcNow - lastCheck.CheckedAt > TimeSpan.FromHours(checkInterval))
                {
                    try
                    {
                        await CheckComplianceAsync(device.Id, cancellationToken);
                        _logger.LogDebug("Compliance check completed for device {DeviceId}", device.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error checking compliance for device {DeviceId}", device.Id);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing compliance checks");
        }
    }

    // Helper methods
    private async Task AssignDefaultPoliciesAsync(ManagedDevice device, CancellationToken cancellationToken)
    {
        var defaultPolicies = await _policyRepository.GetDefaultPoliciesAsync(device.Platform, cancellationToken);

        foreach (var policy in defaultPolicies)
        {
            if (policy.EvaluateConditions(GetDeviceStateForCompliance(device)))
            {
                device.AssignPolicy(policy.Id);

                var assignment = new DevicePolicyAssignment(device.Id, policy.Id, "System");
                _assignmentRepository.Add(assignment);
            }
        }

        if (defaultPolicies.Any())
        {
            await _assignmentRepository.SaveChangesAsync(cancellationToken);
        }
    }

    private async Task SendPolicyUpdateCommandAsync(Guid deviceId, CancellationToken cancellationToken)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(deviceId, cancellationToken);
            if (device == null) return;

            var policies = await _policyRepository.GetByIdsAsync(device.AssignedPolicyIds, cancellationToken);
            var policyData = policies.Where(p => p.IsActive).Select(p => new
            {
                id = p.Id,
                name = p.Name,
                rules = p.Rules,
                configuration = p.Configuration,
                enforcement = p.Enforcement
            }).ToList();

            var command = new DeviceCommand(
                deviceId,
                "UpdatePolicy",
                "Update Device Policies",
                new Dictionary<string, object> { ["policies"] = policyData },
                "System");

            _commandRepository.Add(command);
            await _commandRepository.SaveChangesAsync(cancellationToken);

            // Send command to device
            await _commandService.SendCommandToDeviceAsync(command, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending policy update command to device {DeviceId}", deviceId);
        }
    }

    private Dictionary<string, object> GetDeviceStateForCompliance(ManagedDevice device)
    {
        var deviceState = new Dictionary<string, object>(device.DeviceInfo);

        // Add security info
        foreach (var securityItem in device.SecurityInfo)
        {
            deviceState[securityItem.Key] = securityItem.Value;
        }

        // Add device metadata
        deviceState["platform"] = device.Platform;
        deviceState["platform_version"] = device.PlatformVersion;
        deviceState["app_version"] = device.AppVersion;
        deviceState["last_seen"] = device.LastSeen;
        deviceState["enrolled_at"] = device.EnrolledAt;

        return deviceState;
    }

    private async Task HandleNonComplianceAsync(ManagedDevice device, List<string> violations, CancellationToken cancellationToken)
    {
        try
        {
            var policies = await _policyRepository.GetByIdsAsync(device.AssignedPolicyIds, cancellationToken);
            var enforcementActions = new HashSet<string>();

            foreach (var policy in policies.Where(p => p.IsActive))
            {
                var actions = policy.GetEnforcementActions();
                foreach (var action in actions)
                {
                    enforcementActions.Add(action.Key);
                }
            }

            // Execute enforcement actions
            if (enforcementActions.Contains("blockAccess"))
            {
                device.Suspend("Non-compliance detected");
            }

            if (enforcementActions.Contains("quarantine"))
            {
                // Send quarantine command
                var quarantineCommand = new DeviceCommand(
                    device.Id,
                    "Quarantine",
                    "Quarantine Device",
                    new Dictionary<string, object> { ["reason"] = "Non-compliance", ["violations"] = violations },
                    "System");

                _commandRepository.Add(quarantineCommand);
            }

            if (enforcementActions.Contains("remoteWipe"))
            {
                // This would typically require additional approval
                _logger.LogWarning("Remote wipe enforcement triggered for device {DeviceId} but requires manual approval", device.Id);
            }

            await _deviceRepository.SaveChangesAsync(cancellationToken);
            await _commandRepository.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling non-compliance for device {DeviceId}", device.Id);
        }
    }

    // Mapping methods
    private ManagedDeviceDto MapToManagedDeviceDto(ManagedDevice device)
    {
        return new ManagedDeviceDto
        {
            Id = device.Id,
            UserId = device.UserId,
            DeviceId = device.DeviceId,
            DeviceName = device.DeviceName,
            Platform = device.Platform,
            PlatformVersion = device.PlatformVersion,
            AppVersion = device.AppVersion,
            Status = device.Status,
            IsManaged = device.IsManaged,
            IsCompliant = device.IsCompliant,
            LastSeen = device.LastSeen,
            EnrolledAt = device.EnrolledAt,
            LastPolicyUpdate = device.LastPolicyUpdate,
            DeviceInfo = device.DeviceInfo,
            SecurityInfo = device.SecurityInfo,
            ComplianceStatus = device.ComplianceStatus,
            AssignedPolicyIds = device.AssignedPolicyIds,
            Configuration = device.Configuration,
            HealthStatus = device.GetHealthStatus(),
            RiskLevel = device.GetRiskLevel(),
            IsOnline = device.IsOnline(TimeSpan.FromMinutes(15)),
            IsStale = device.IsStale(TimeSpan.FromDays(7))
        };
    }

    private DevicePolicyDto MapToDevicePolicyDto(DevicePolicy policy)
    {
        return new DevicePolicyDto
        {
            Id = policy.Id,
            Name = policy.Name,
            DisplayName = policy.DisplayName,
            Description = policy.Description,
            Category = policy.Category,
            Platform = policy.Platform,
            Version = policy.Version,
            IsActive = policy.IsActive,
            IsDefault = policy.IsDefault,
            Priority = policy.Priority,
            Rules = policy.Rules,
            Configuration = policy.Configuration,
            Enforcement = policy.Enforcement,
            Conditions = policy.Conditions,
            TargetGroups = policy.TargetGroups,
            CreatedAt = policy.CreatedAt,
            UpdatedAt = policy.UpdatedAt,
            CreatedBy = policy.CreatedBy,
            UpdatedBy = policy.UpdatedBy
        };
    }

    private DeviceCommandDto MapToDeviceCommandDto(DeviceCommand command)
    {
        return new DeviceCommandDto
        {
            Id = command.Id,
            DeviceId = command.DeviceId,
            CommandType = command.CommandType,
            CommandName = command.CommandName,
            Status = command.Status,
            Parameters = command.Parameters,
            Result = command.Result,
            CreatedAt = command.CreatedAt,
            SentAt = command.SentAt,
            AcknowledgedAt = command.AcknowledgedAt,
            CompletedAt = command.CompletedAt,
            ExpiresAt = command.ExpiresAt,
            ErrorMessage = command.ErrorMessage,
            ErrorCode = command.ErrorCode,
            RetryCount = command.RetryCount,
            MaxRetries = command.MaxRetries,
            CreatedBy = command.CreatedBy,
            ExecutionTime = command.GetExecutionTime()?.TotalMilliseconds,
            Age = command.GetAge().TotalMinutes,
            Priority = command.GetPriority(),
            RequiresUserInteraction = command.RequiresUserInteraction(),
            IsExpired = command.IsExpired(),
            CanRetry = command.CanRetry(),
            IsCompleted = command.IsCompleted()
        };
    }
}

// Supporting interfaces and classes
public interface IDeviceCommandService
{
    Task<DeviceCommandResult> SendCommandToDeviceAsync(DeviceCommand command, CancellationToken cancellationToken = default);
}

public class DeviceCommandResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, object> Response { get; set; } = new();
}
