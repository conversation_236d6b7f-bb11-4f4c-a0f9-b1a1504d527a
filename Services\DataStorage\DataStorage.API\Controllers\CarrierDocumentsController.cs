using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using DataStorage.Application.Commands;
using DataStorage.Application.Queries;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.API.Controllers;

[ApiController]
[Route("api/carrier/documents")]
[Authorize(Roles = "Carrier,Driver,Admin")]
public class CarrierDocumentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CarrierDocumentsController> _logger;

    public CarrierDocumentsController(IMediator mediator, ILogger<CarrierDocumentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Upload pickup confirmation photo
    /// </summary>
    [HttpPost("pickup-photos")]
    public async Task<ActionResult<DocumentDto>> UploadPickupConfirmationPhoto([FromForm] UploadPickupPhotoRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var command = new UploadPickupConfirmationPhotoCommand
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                OrderId = request.OrderId,
                Title = request.Title,
                Description = request.Description,
                PhotoStream = request.Photo.OpenReadStream(),
                FileName = request.Photo.FileName,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                CapturedAt = request.CapturedAt,
                LocationAddress = request.LocationAddress,
                AdditionalMetadata = request.AdditionalMetadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload pickup confirmation photo for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload pickup confirmation photo");
        }
    }

    /// <summary>
    /// Upload delivery photo
    /// </summary>
    [HttpPost("delivery-photos")]
    public async Task<ActionResult<DocumentDto>> UploadDeliveryPhoto([FromForm] UploadDeliveryPhotoRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var command = new UploadDeliveryPhotoCommand
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                OrderId = request.OrderId,
                Title = request.Title,
                Description = request.Description,
                PhotoStream = request.Photo.OpenReadStream(),
                FileName = request.Photo.FileName,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                CapturedAt = request.CapturedAt,
                LocationAddress = request.LocationAddress,
                RecipientName = request.RecipientName,
                RecipientContact = request.RecipientContact,
                AdditionalMetadata = request.AdditionalMetadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload delivery photo for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload delivery photo");
        }
    }

    /// <summary>
    /// Submit digital proof of delivery
    /// </summary>
    [HttpPost("proof-of-delivery")]
    public async Task<ActionResult<DocumentDto>> SubmitProofOfDelivery([FromForm] SubmitPODRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var supportingDocuments = new List<Stream>();
            var supportingDocumentNames = new List<string>();
            
            if (request.SupportingDocuments?.Any() == true)
            {
                foreach (var file in request.SupportingDocuments)
                {
                    supportingDocuments.Add(file.OpenReadStream());
                    supportingDocumentNames.Add(file.FileName);
                }
            }

            var command = new SubmitDigitalProofOfDeliveryCommand
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                OrderId = request.OrderId,
                Title = request.Title,
                Description = request.Description,
                RecipientName = request.RecipientName,
                RecipientContact = request.RecipientContact,
                RecipientIdType = request.RecipientIdType,
                RecipientIdNumber = request.RecipientIdNumber,
                DigitalSignature = request.DigitalSignature,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                DeliveredAt = request.DeliveredAt,
                LocationAddress = request.LocationAddress,
                SupportingDocuments = supportingDocuments,
                SupportingDocumentNames = supportingDocumentNames,
                DeliveryNotes = request.DeliveryNotes,
                DeliveryCondition = request.DeliveryCondition,
                AdditionalMetadata = request.AdditionalMetadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to submit proof of delivery for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to submit proof of delivery");
        }
    }

    /// <summary>
    /// Upload delivery receipt
    /// </summary>
    [HttpPost("delivery-receipts")]
    public async Task<ActionResult<DocumentDto>> UploadDeliveryReceipt([FromForm] UploadDeliveryReceiptRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var command = new UploadDeliveryReceiptCommand
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                OrderId = request.OrderId,
                Title = request.Title,
                Description = request.Description,
                ReceiptStream = request.Receipt.OpenReadStream(),
                FileName = request.Receipt.FileName,
                ContentType = request.Receipt.ContentType,
                ReceiptNumber = request.ReceiptNumber,
                ReceiptDate = request.ReceiptDate,
                Amount = request.Amount,
                Currency = request.Currency
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload delivery receipt for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to upload delivery receipt");
        }
    }

    /// <summary>
    /// Confirm unloading completion
    /// </summary>
    [HttpPost("unloading-confirmation")]
    public async Task<ActionResult<DocumentDto>> ConfirmUnloadingCompletion([FromForm] ConfirmUnloadingRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var unloadingPhotos = new List<Stream>();
            var unloadingPhotoNames = new List<string>();
            
            if (request.UnloadingPhotos?.Any() == true)
            {
                foreach (var file in request.UnloadingPhotos)
                {
                    unloadingPhotos.Add(file.OpenReadStream());
                    unloadingPhotoNames.Add(file.FileName);
                }
            }

            var command = new ConfirmUnloadingCompletionCommand
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                OrderId = request.OrderId,
                Title = request.Title,
                Description = request.Description,
                UnloadingStartedAt = request.UnloadingStartedAt,
                UnloadingCompletedAt = request.UnloadingCompletedAt,
                UnloadingLocation = request.UnloadingLocation,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                UnloadingPhotos = unloadingPhotos,
                UnloadingPhotoNames = unloadingPhotoNames,
                UnloadingNotes = request.UnloadingNotes,
                SupervisorName = request.SupervisorName,
                SupervisorContact = request.SupervisorContact,
                CargoCondition = request.CargoCondition,
                DamageNotes = request.DamageNotes,
                AdditionalMetadata = request.AdditionalMetadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to confirm unloading completion for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to confirm unloading completion");
        }
    }

    /// <summary>
    /// Get all documents for current carrier
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetCarrierDocuments([FromQuery] GetCarrierDocumentsRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var query = new GetCarrierDocumentsQuery
            {
                CarrierId = carrierId,
                DocumentType = request.DocumentType,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = carrierId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get documents for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve documents");
        }
    }

    /// <summary>
    /// Get documents for a specific trip
    /// </summary>
    [HttpGet("trips/{tripId}")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetTripDocuments(Guid tripId, [FromQuery] DocumentType? documentType = null)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var query = new GetCarrierTripDocumentsQuery
            {
                CarrierId = carrierId,
                TripId = tripId,
                DocumentType = documentType,
                RequestingUserId = carrierId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trip documents for carrier {CarrierId}, trip {TripId}", GetCurrentUserId(), tripId);
            return StatusCode(500, "Failed to retrieve trip documents");
        }
    }

    /// <summary>
    /// Get delivery photos
    /// </summary>
    [HttpGet("delivery-photos")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetDeliveryPhotos([FromQuery] GetDeliveryPhotosRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var query = new GetCarrierDeliveryPhotosQuery
            {
                CarrierId = carrierId,
                TripId = request.TripId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = carrierId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get delivery photos for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve delivery photos");
        }
    }

    /// <summary>
    /// Get POD history
    /// </summary>
    [HttpGet("pod-history")]
    public async Task<ActionResult<IEnumerable<DocumentDto>>> GetPODHistory([FromQuery] GetPODHistoryRequest request)
    {
        try
        {
            var carrierId = GetCurrentUserId();
            
            var query = new GetCarrierPODHistoryQuery
            {
                CarrierId = carrierId,
                DeliveryStatus = request.DeliveryStatus,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                RequestingUserId = carrierId
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get POD history for carrier {CarrierId}", GetCurrentUserId());
            return StatusCode(500, "Failed to retrieve POD history");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }
}

// Request DTOs for Carrier endpoints
public class UploadPickupPhotoRequest
{
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile Photo { get; set; } = null!;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime? CapturedAt { get; set; }
    public string? LocationAddress { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class UploadDeliveryPhotoRequest
{
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile Photo { get; set; } = null!;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime? CapturedAt { get; set; }
    public string? LocationAddress { get; set; }
    public string? RecipientName { get; set; }
    public string? RecipientContact { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class SubmitPODRequest
{
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string RecipientName { get; set; } = string.Empty;
    public string RecipientContact { get; set; } = string.Empty;
    public string? RecipientIdType { get; set; }
    public string? RecipientIdNumber { get; set; }
    public byte[]? DigitalSignature { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime DeliveredAt { get; set; }
    public string? LocationAddress { get; set; }
    public List<IFormFile>? SupportingDocuments { get; set; }
    public string? DeliveryNotes { get; set; }
    public DeliveryCondition DeliveryCondition { get; set; } = DeliveryCondition.Good;
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class UploadDeliveryReceiptRequest
{
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public IFormFile Receipt { get; set; } = null!;
    public string? ReceiptNumber { get; set; }
    public DateTime? ReceiptDate { get; set; }
    public decimal? Amount { get; set; }
    public string? Currency { get; set; } = "INR";
}

public class ConfirmUnloadingRequest
{
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime UnloadingStartedAt { get; set; }
    public DateTime UnloadingCompletedAt { get; set; }
    public string? UnloadingLocation { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public List<IFormFile>? UnloadingPhotos { get; set; }
    public string? UnloadingNotes { get; set; }
    public string? SupervisorName { get; set; }
    public string? SupervisorContact { get; set; }
    public CargoCondition CargoCondition { get; set; } = CargoCondition.Good;
    public string? DamageNotes { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class GetCarrierDocumentsRequest
{
    public DocumentType? DocumentType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetDeliveryPhotosRequest
{
    public Guid? TripId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class GetPODHistoryRequest
{
    public DeliveryCondition? DeliveryStatus { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
