using FluentValidation;

namespace UserManagement.Application.Admin.Queries.ExportUserSummary
{
    public class ExportUserSummaryQueryValidator : AbstractValidator<ExportUserSummaryQuery>
    {
        public ExportUserSummaryQueryValidator()
        {
            RuleFor(x => x.RequestedBy)
                .NotEmpty()
                .WithMessage("RequestedBy is required");

            RuleFor(x => x.RequestedByRole)
                .NotEmpty()
                .WithMessage("RequestedByRole is required");

            RuleFor(x => x.Format)
                .IsInEnum()
                .WithMessage("Invalid export format");

            RuleFor(x => x.FromDate)
                .LessThan(x => x.ToDate)
                .When(x => x.FromDate.HasValue && x.ToDate.HasValue)
                .WithMessage("FromDate must be before ToDate");

            RuleFor(x => x.MaxRecords)
                .GreaterThan(0)
                .LessThanOrEqualTo(100000)
                .When(x => x.MaxRecords.HasValue)
                .WithMessage("MaxRecords must be between 1 and 100,000");

            RuleFor(x => x.SelectedColumns)
                .Must(columns => columns == null || columns.Count <= 50)
                .WithMessage("Maximum 50 columns can be selected");
        }
    }
}
