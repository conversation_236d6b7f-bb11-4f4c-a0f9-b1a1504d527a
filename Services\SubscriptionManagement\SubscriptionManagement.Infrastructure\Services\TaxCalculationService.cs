using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class TaxCalculationService : ITaxCalculationService
    {
        private readonly IGlobalTaxConfigurationRepository _globalTaxConfigurationRepository;
        private readonly IPlanTaxConfigurationRepository _planTaxConfigurationRepository;
        private readonly ITaxExemptionRepository _taxExemptionRepository;
        private readonly ILogger<TaxCalculationService> _logger;

        public TaxCalculationService(
            IGlobalTaxConfigurationRepository globalTaxConfigurationRepository,
            IPlanTaxConfigurationRepository planTaxConfigurationRepository,
            ITaxExemptionRepository taxExemptionRepository,
            ILogger<TaxCalculationService> logger)
        {
            _globalTaxConfigurationRepository = globalTaxConfigurationRepository;
            _planTaxConfigurationRepository = planTaxConfigurationRepository;
            _taxExemptionRepository = taxExemptionRepository;
            _logger = logger;
        }

        public async Task<Money> CalculateTaxForPlanAsync(Plan plan, string customerRegion, Guid? customerId = null, DateTime? date = null)
        {
            _logger.LogDebug("Calculating tax for plan {PlanId} in region {Region}", plan.Id, customerRegion);

            var calculationDate = date ?? DateTime.UtcNow;

            // First check plan-specific tax configurations
            var planTaxConfigs = await _planTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(plan.Id, customerRegion, calculationDate);

            if (planTaxConfigs.Any())
            {
                var taxConfigurations = planTaxConfigs.Select(ptc => ptc.TaxConfiguration).ToList();
                return await CalculateCompoundTaxAsync(plan.Price, taxConfigurations, customerRegion, customerId, calculationDate);
            }

            // Fall back to global tax configurations
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            if (globalTaxConfigs.Any())
            {
                var taxConfigurations = globalTaxConfigs.Select(gtc => gtc.TaxConfiguration).ToList();
                return await CalculateCompoundTaxAsync(plan.Price, taxConfigurations, customerRegion, customerId, calculationDate);
            }

            return Money.Zero(plan.Price.Currency);
        }

        public async Task<Money> CalculateTaxAmountAsync(Money baseAmount, string customerRegion, TaxType? taxType = null, DateTime? date = null)
        {
            _logger.LogDebug("Calculating tax amount for {Amount} {Currency} in region {Region}", 
                baseAmount.Amount, baseAmount.Currency, customerRegion);

            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            if (taxType.HasValue)
            {
                globalTaxConfigs = globalTaxConfigs.Where(gtc => gtc.TaxConfiguration.TaxType == taxType.Value).ToList();
            }

            if (globalTaxConfigs.Any())
            {
                var taxConfigurations = globalTaxConfigs.Select(gtc => gtc.TaxConfiguration).ToList();
                return TaxDomainService.CalculateCompoundTax(baseAmount, taxConfigurations, customerRegion);
            }

            return Money.Zero(baseAmount.Currency);
        }

        public async Task<decimal> GetEffectiveTaxRateAsync(string customerRegion, TaxType taxType, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            var applicableConfig = globalTaxConfigs
                .FirstOrDefault(gtc => gtc.TaxConfiguration.TaxType == taxType);

            return applicableConfig?.TaxConfiguration.Rate ?? 0;
        }

        public async Task<bool> IsCustomerExemptAsync(Guid customerId, TaxType taxType, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            return await _taxExemptionRepository.IsUserExemptAsync(customerId, taxType, region, checkDate);
        }

        public async Task<List<TaxConfiguration>> GetApplicableTaxConfigurationsAsync(string customerRegion, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            return globalTaxConfigs.Select(gtc => gtc.TaxConfiguration).ToList();
        }

        public async Task<Money> CalculateCompoundTaxAsync(Money baseAmount, List<TaxType> taxTypes, string customerRegion, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            var applicableConfigs = globalTaxConfigs
                .Where(gtc => taxTypes.Contains(gtc.TaxConfiguration.TaxType))
                .Select(gtc => gtc.TaxConfiguration)
                .ToList();

            return TaxDomainService.CalculateCompoundTax(baseAmount, applicableConfigs, customerRegion);
        }

        public async Task<bool> IsTaxInclusivePricingAsync(string customerRegion, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            return globalTaxConfigs.Any(gtc => gtc.TaxConfiguration.IsIncluded);
        }

        public async Task<Money> CalculateBaseAmountFromTaxInclusivePriceAsync(Money taxInclusiveAmount, string customerRegion, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            var totalTaxRate = globalTaxConfigs
                .Where(gtc => gtc.TaxConfiguration.IsIncluded)
                .Sum(gtc => gtc.TaxConfiguration.Rate);

            return TaxDomainService.CalculateBaseAmountFromTaxInclusive(taxInclusiveAmount, totalTaxRate);
        }

        public async Task<TaxBreakdown> GetTaxBreakdownAsync(Money baseAmount, string customerRegion, Guid? customerId = null, DateTime? date = null)
        {
            var calculationDate = date ?? DateTime.UtcNow;
            var globalTaxConfigs = await _globalTaxConfigurationRepository
                .GetEffectiveConfigurationsAsync(customerRegion, calculationDate);

            var taxItems = new List<TaxBreakdownItem>();
            var totalTax = Money.Zero(baseAmount.Currency);
            var currentTaxableAmount = baseAmount;

            foreach (var config in globalTaxConfigs.OrderBy(gtc => gtc.Priority))
            {
                var taxConfig = config.TaxConfiguration;
                var isExempt = false;
                string? exemptionReason = null;

                // Check for exemptions
                if (customerId.HasValue)
                {
                    isExempt = await IsCustomerExemptAsync(customerId.Value, taxConfig.TaxType, customerRegion, calculationDate);
                    if (isExempt)
                    {
                        exemptionReason = "Customer has valid tax exemption";
                    }
                }

                var taxAmount = isExempt ? Money.Zero(baseAmount.Currency) : taxConfig.CalculateTax(currentTaxableAmount, customerRegion);

                taxItems.Add(new TaxBreakdownItem
                {
                    TaxType = taxConfig.TaxType,
                    TaxName = GetTaxDisplayName(taxConfig.TaxType),
                    Rate = taxConfig.Rate,
                    TaxableAmount = currentTaxableAmount,
                    TaxAmount = taxAmount,
                    IsExempt = isExempt,
                    ExemptionReason = exemptionReason
                });

                totalTax = totalTax.Add(taxAmount);

                // For compound taxation
                if (TaxDomainService.ShouldCompoundTax(taxConfig.TaxType))
                {
                    currentTaxableAmount = currentTaxableAmount.Add(taxAmount);
                }
            }

            var isTaxInclusive = globalTaxConfigs.Any(gtc => gtc.TaxConfiguration.IsIncluded);

            return new TaxBreakdown
            {
                BaseAmount = baseAmount,
                TaxItems = taxItems,
                TotalTaxAmount = totalTax,
                TotalAmount = baseAmount.Add(totalTax),
                IsTaxInclusive = isTaxInclusive,
                CustomerRegion = customerRegion,
                CalculatedAt = calculationDate
            };
        }

        public async Task<bool> ValidateTaxConfigurationAsync(TaxConfiguration taxConfiguration, string region)
        {
            try
            {
                TaxDomainService.ValidateTaxConfiguration(taxConfiguration, region);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<TaxDisplayInfo> GetTaxDisplayInfoAsync(Plan plan, string customerRegion, Guid? customerId = null, DateTime? date = null)
        {
            var taxBreakdown = await GetTaxBreakdownAsync(plan.Price, customerRegion, customerId, date);
            var hasExemptions = customerId.HasValue && taxBreakdown.TaxItems.Any(item => item.IsExempt);

            var taxLabels = taxBreakdown.TaxItems
                .Where(item => !item.IsExempt && item.TaxAmount.Amount > 0)
                .Select(item => $"{item.TaxName} ({item.Rate:F2}%)")
                .ToList();

            var taxDisplayText = taxLabels.Any() 
                ? $"Includes {string.Join(", ", taxLabels)}"
                : "No applicable taxes";

            return new TaxDisplayInfo
            {
                DisplayPrice = taxBreakdown.TotalAmount,
                BasePrice = plan.Price,
                TotalTaxAmount = taxBreakdown.TotalTaxAmount,
                IsTaxInclusive = taxBreakdown.IsTaxInclusive,
                TaxDisplayText = taxDisplayText,
                TaxLabels = taxLabels,
                HasExemptions = hasExemptions
            };
        }

        private async Task<Money> CalculateCompoundTaxAsync(Money baseAmount, List<TaxConfiguration> taxConfigurations, 
            string customerRegion, Guid? customerId, DateTime calculationDate)
        {
            var totalTax = Money.Zero(baseAmount.Currency);
            var currentTaxableAmount = baseAmount;

            foreach (var config in taxConfigurations.OrderBy(tc => TaxDomainService.GetTaxPriority(tc.TaxType)))
            {
                var isExempt = false;
                if (customerId.HasValue)
                {
                    isExempt = await IsCustomerExemptAsync(customerId.Value, config.TaxType, customerRegion, calculationDate);
                }

                if (!isExempt)
                {
                    var taxAmount = config.CalculateTax(currentTaxableAmount, customerRegion);
                    totalTax = totalTax.Add(taxAmount);

                    // For compound taxation
                    if (TaxDomainService.ShouldCompoundTax(config.TaxType))
                    {
                        currentTaxableAmount = currentTaxableAmount.Add(taxAmount);
                    }
                }
            }

            return totalTax;
        }

        private static string GetTaxDisplayName(TaxType taxType)
        {
            return taxType switch
            {
                TaxType.GST => "GST",
                TaxType.CGST => "CGST",
                TaxType.SGST => "SGST",
                TaxType.IGST => "IGST",
                TaxType.UTGST => "UTGST",
                TaxType.VAT => "VAT",
                TaxType.TDS => "TDS",
                TaxType.TCS => "TCS",
                TaxType.Cess => "Cess",
                TaxType.ServiceTax => "Service Tax",
                TaxType.SalesTax => "Sales Tax",
                TaxType.CustomDuty => "Custom Duty",
                TaxType.ExciseDuty => "Excise Duty",
                _ => taxType.ToString()
            };
        }
    }
}
