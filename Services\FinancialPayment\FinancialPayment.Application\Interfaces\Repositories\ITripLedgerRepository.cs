using FinancialPayment.Domain.Entities;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface ITripLedgerRepository
{
    Task<TripLedger?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<TripLedger?> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<TripLedger>> GetByOrderIdAsync(Guid orderId);
    Task<List<TripLedger>> GetByCarrierIdAsync(Guid carrierId);
    Task<List<TripLedger>> GetByTransportCompanyAsync(Guid transportCompanyId);
    Task<List<TripLedger>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<TripLedger> AddAsync(TripLedger tripLedger, CancellationToken cancellationToken = default);
    Task UpdateAsync(TripLedger tripLedger);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<List<TripLedger>> GetPendingLedgersAsync();
    Task<decimal> GetTotalAmountByTripAsync(Guid tripId);
    Task SaveChangesAsync();
    Task<List<TripLedgerEntry>> GetEntriesAsync(Guid tripLedgerId);
    Task<TripLedgerAnalyticsDto?> GetAnalyticsAsync(Guid tripLedgerId);
}
