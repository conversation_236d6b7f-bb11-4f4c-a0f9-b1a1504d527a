using CommunicationNotification.Infrastructure.Models;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Monitoring and alerting API controller
/// </summary>
[ApiController]
[Route("api/communication/monitoring")]
[Authorize(Roles = "Admin,SystemMonitor")]
public class MonitoringController : ControllerBase
{
    private readonly IMonitoringService _monitoringService;
    private readonly IAlertingService _alertingService;

    public MonitoringController(
        IMonitoringService monitoringService,
        IAlertingService alertingService)
    {
        _monitoringService = monitoringService;
        _alertingService = alertingService;
    }

    /// <summary>
    /// Get system health status
    /// </summary>
    [HttpGet("health")]
    public async Task<IActionResult> GetSystemHealth()
    {
        var health = await _monitoringService.GetSystemHealthAsync();
        return Ok(health);
    }

    /// <summary>
    /// Get monitoring dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<IActionResult> GetDashboard()
    {
        var dashboard = await _monitoringService.GetDashboardDataAsync();
        return Ok(dashboard);
    }

    /// <summary>
    /// Run health checks manually
    /// </summary>
    [HttpPost("health/check")]
    public async Task<IActionResult> RunHealthChecks()
    {
        var results = await _monitoringService.RunHealthChecksAsync();
        return Ok(results);
    }

    /// <summary>
    /// Get system metrics
    /// </summary>
    [HttpGet("metrics/system")]
    public async Task<IActionResult> GetSystemMetrics()
    {
        var metrics = await _monitoringService.GetSystemMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Get service metrics
    /// </summary>
    [HttpGet("metrics/service")]
    public async Task<IActionResult> GetServiceMetrics()
    {
        var metrics = await _monitoringService.GetServiceMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Record custom metric
    /// </summary>
    [HttpPost("metrics")]
    public async Task<IActionResult> RecordMetric([FromBody] RecordMetricRequest request)
    {
        _monitoringService.RecordMetric(request.MetricName, request.Value, request.Tags);
        return Ok(new { success = true, message = "Metric recorded successfully" });
    }

    /// <summary>
    /// Record monitoring event
    /// </summary>
    [HttpPost("events")]
    public async Task<IActionResult> RecordEvent([FromBody] RecordEventRequest request)
    {
        _monitoringService.RecordEvent(request.EventName, request.Description, request.Severity, request.Metadata);
        return Ok(new { success = true, message = "Event recorded successfully" });
    }

    /// <summary>
    /// Get active alerts
    /// </summary>
    [HttpGet("alerts")]
    public async Task<IActionResult> GetActiveAlerts()
    {
        var alerts = await _monitoringService.GetActiveAlertsAsync();
        return Ok(alerts);
    }

    /// <summary>
    /// Trigger manual alert
    /// </summary>
    [HttpPost("alerts")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TriggerAlert([FromBody] TriggerAlertRequest request)
    {
        var alert = new Alert
        {
            Type = request.Type,
            Title = request.Title,
            Message = request.Message,
            Severity = request.Severity,
            Source = "Manual",
            Metadata = request.Metadata ?? new Dictionary<string, object>()
        };

        await _monitoringService.TriggerAlertAsync(alert);
        return Ok(new { success = true, alertId = alert.Id, message = "Alert triggered successfully" });
    }

    /// <summary>
    /// Resolve alert
    /// </summary>
    [HttpPost("alerts/{alertId}/resolve")]
    public async Task<IActionResult> ResolveAlert(Guid alertId, [FromBody] ResolveAlertRequest request)
    {
        await _monitoringService.ResolveAlertAsync(alertId, request.ResolvedBy, request.Resolution);
        return Ok(new { success = true, message = "Alert resolved successfully" });
    }

    /// <summary>
    /// Get alert notification history
    /// </summary>
    [HttpGet("alerts/{alertId}/notifications")]
    public async Task<IActionResult> GetAlertNotifications(Guid alertId)
    {
        var notifications = await _alertingService.GetNotificationHistoryAsync(alertId);
        return Ok(notifications);
    }

    /// <summary>
    /// Get alerting statistics
    /// </summary>
    [HttpGet("alerting/statistics")]
    public async Task<IActionResult> GetAlertingStatistics()
    {
        var statistics = await _alertingService.GetAlertingStatisticsAsync();
        return Ok(statistics);
    }

    /// <summary>
    /// Test alert channel
    /// </summary>
    [HttpPost("alerting/test")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> TestAlertChannel([FromBody] TestAlertChannelRequest request)
    {
        try
        {
            await _alertingService.TestAlertChannelAsync(request.Channel, request.Recipient);
            return Ok(new { success = true, message = $"Test alert sent successfully to {request.Channel}" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"Failed to send test alert: {ex.Message}" });
        }
    }

    /// <summary>
    /// Suppress alerts for a specific type
    /// </summary>
    [HttpPost("alerts/suppress")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> SuppressAlerts([FromBody] SuppressAlertsRequest request)
    {
        await _alertingService.SuppressAlertsAsync(request.AlertType, TimeSpan.FromMinutes(request.DurationMinutes));
        return Ok(new { 
            success = true, 
            message = $"Alerts of type '{request.AlertType}' suppressed for {request.DurationMinutes} minutes" 
        });
    }

    /// <summary>
    /// Check if alert type is suppressed
    /// </summary>
    [HttpGet("alerts/{alertType}/suppressed")]
    public async Task<IActionResult> IsAlertSuppressed(string alertType)
    {
        var isSuppressed = await _alertingService.IsAlertSuppressedAsync(alertType);
        return Ok(new { alertType, isSuppressed });
    }

    /// <summary>
    /// Start monitoring service
    /// </summary>
    [HttpPost("start")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> StartMonitoring()
    {
        await _monitoringService.StartMonitoringAsync();
        return Ok(new { success = true, message = "Monitoring service started" });
    }

    /// <summary>
    /// Stop monitoring service
    /// </summary>
    [HttpPost("stop")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> StopMonitoring()
    {
        await _monitoringService.StopMonitoringAsync();
        return Ok(new { success = true, message = "Monitoring service stopped" });
    }

    /// <summary>
    /// Get monitoring service status
    /// </summary>
    [HttpGet("status")]
    public async Task<IActionResult> GetMonitoringStatus()
    {
        var health = await _monitoringService.GetSystemHealthAsync();
        var status = new
        {
            isRunning = true, // Would check actual service status
            lastHealthCheck = health.LastUpdated,
            overallHealth = health.OverallStatus,
            activeAlerts = health.ActiveAlerts,
            criticalAlerts = health.CriticalAlerts,
            uptime = health.Uptime
        };

        return Ok(status);
    }

    /// <summary>
    /// Get health check configuration
    /// </summary>
    [HttpGet("health/configuration")]
    public async Task<IActionResult> GetHealthCheckConfiguration()
    {
        // This would return the actual health check configuration
        var configuration = new
        {
            enabledChecks = new[] { "Database", "Cache", "External Services", "Message Queue", "Disk Space", "Memory" },
            checkInterval = "5 minutes",
            timeout = "30 seconds",
            failureThreshold = 3
        };

        return Ok(configuration);
    }

    /// <summary>
    /// Update health check configuration
    /// </summary>
    [HttpPut("health/configuration")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateHealthCheckConfiguration([FromBody] UpdateHealthCheckConfigRequest request)
    {
        // This would update the actual health check configuration
        return Ok(new { success = true, message = "Health check configuration updated successfully" });
    }

    /// <summary>
    /// Get monitoring rules
    /// </summary>
    [HttpGet("rules")]
    public async Task<IActionResult> GetMonitoringRules()
    {
        // This would return actual monitoring rules
        var rules = new[]
        {
            new MonitoringRule
            {
                Id = Guid.NewGuid(),
                Name = "High CPU Usage",
                Description = "Alert when CPU usage exceeds 80%",
                Enabled = true,
                MetricName = "system.cpu_usage",
                Condition = "value > 80",
                EvaluationWindow = TimeSpan.FromMinutes(5),
                AlertSeverity = AlertSeverity.High,
                AlertMessage = "CPU usage is critically high",
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            },
            new MonitoringRule
            {
                Id = Guid.NewGuid(),
                Name = "High Error Rate",
                Description = "Alert when error rate exceeds 5%",
                Enabled = true,
                MetricName = "service.error_rate",
                Condition = "value > 0.05",
                EvaluationWindow = TimeSpan.FromMinutes(10),
                AlertSeverity = AlertSeverity.Medium,
                AlertMessage = "Service error rate is elevated",
                CreatedAt = DateTime.UtcNow.AddDays(-20)
            }
        };

        return Ok(rules);
    }

    /// <summary>
    /// Create monitoring rule
    /// </summary>
    [HttpPost("rules")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> CreateMonitoringRule([FromBody] CreateMonitoringRuleRequest request)
    {
        var rule = new MonitoringRule
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            Description = request.Description,
            Enabled = request.Enabled,
            MetricName = request.MetricName,
            Condition = request.Condition,
            EvaluationWindow = TimeSpan.FromMinutes(request.EvaluationWindowMinutes),
            AlertSeverity = request.AlertSeverity,
            AlertMessage = request.AlertMessage,
            Tags = request.Tags ?? new Dictionary<string, object>(),
            CreatedAt = DateTime.UtcNow
        };

        // In real implementation, this would save to database
        return Ok(new { success = true, ruleId = rule.Id, message = "Monitoring rule created successfully" });
    }

    /// <summary>
    /// Update monitoring rule
    /// </summary>
    [HttpPut("rules/{ruleId}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateMonitoringRule(Guid ruleId, [FromBody] UpdateMonitoringRuleRequest request)
    {
        // In real implementation, this would update the rule in database
        return Ok(new { success = true, message = "Monitoring rule updated successfully" });
    }

    /// <summary>
    /// Delete monitoring rule
    /// </summary>
    [HttpDelete("rules/{ruleId}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> DeleteMonitoringRule(Guid ruleId)
    {
        // In real implementation, this would delete the rule from database
        return Ok(new { success = true, message = "Monitoring rule deleted successfully" });
    }
}

// Request/Response DTOs
public class RecordMetricRequest
{
    public string MetricName { get; set; } = string.Empty;
    public double Value { get; set; }
    public Dictionary<string, object>? Tags { get; set; }
}

public class RecordEventRequest
{
    public string EventName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public EventSeverity Severity { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class TriggerAlertRequest
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class ResolveAlertRequest
{
    public string ResolvedBy { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
}

public class TestAlertChannelRequest
{
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
}

public class SuppressAlertsRequest
{
    public string AlertType { get; set; } = string.Empty;
    public int DurationMinutes { get; set; }
}

public class UpdateHealthCheckConfigRequest
{
    public List<string> EnabledChecks { get; set; } = new();
    public int CheckIntervalMinutes { get; set; }
    public int TimeoutSeconds { get; set; }
    public int FailureThreshold { get; set; }
}

public class CreateMonitoringRuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
    public string MetricName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public int EvaluationWindowMinutes { get; set; } = 5;
    public AlertSeverity AlertSeverity { get; set; }
    public string AlertMessage { get; set; } = string.Empty;
    public Dictionary<string, object>? Tags { get; set; }
}

public class UpdateMonitoringRuleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? Enabled { get; set; }
    public string? Condition { get; set; }
    public int? EvaluationWindowMinutes { get; set; }
    public AlertSeverity? AlertSeverity { get; set; }
    public string? AlertMessage { get; set; }
}
