# PowerShell script to start infrastructure services
Write-Host "Starting TLI Microservices Infrastructure..." -ForegroundColor Green

# Check if Dock<PERSON> is running
try {
    docker version | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Start infrastructure services
Write-Host "Starting PostgreSQL, RabbitMQ, and Redis..." -ForegroundColor Yellow
docker-compose up -d postgres rabbitmq redis

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service health
Write-Host "Checking service health..." -ForegroundColor Yellow

# Check PostgreSQL
try {
    $pgResult = docker exec tli-postgres pg_isready -U postgres
    if ($pgResult -like "*accepting connections*") {
        Write-Host "✓ PostgreSQL is ready" -ForegroundColor Green
    } else {
        Write-Host "✗ PostgreSQL is not ready" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ PostgreSQL health check failed" -ForegroundColor Red
}

# Check RabbitMQ
try {
    $rmqResult = docker exec tli-rabbitmq rabbitmq-diagnostics ping
    if ($rmqResult -like "*Ping succeeded*") {
        Write-Host "✓ RabbitMQ is ready" -ForegroundColor Green
    } else {
        Write-Host "✗ RabbitMQ is not ready" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ RabbitMQ health check failed" -ForegroundColor Red
}

# Check Redis
try {
    $redisResult = docker exec tli-redis redis-cli ping
    if ($redisResult -eq "PONG") {
        Write-Host "✓ Redis is ready" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis is not ready" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Redis health check failed" -ForegroundColor Red
}

Write-Host "`nInfrastructure services are starting up!" -ForegroundColor Green
Write-Host "RabbitMQ Management: http://localhost:15672 (guest/guest)" -ForegroundColor Cyan
Write-Host "PostgreSQL: localhost:5432 (postgres/postgres)" -ForegroundColor Cyan
Write-Host "Redis: localhost:6379" -ForegroundColor Cyan
