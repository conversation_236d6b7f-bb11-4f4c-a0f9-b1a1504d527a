using Shared.Domain.Common;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class TaxCategory : AggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string Code { get; private set; }
        public bool IsActive { get; private set; }
        public List<TaxConfiguration> TaxConfigurations { get; private set; }

        private TaxCategory()
        {
            Name = string.Empty;
            Description = string.Empty;
            Code = string.Empty;
            TaxConfigurations = new List<TaxConfiguration>();
        }

        public TaxCategory(string name, string description, string code)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Tax category name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new SubscriptionDomainException("Tax category description cannot be empty");

            if (string.IsNullOrWhiteSpace(code))
                throw new SubscriptionDomainException("Tax category code cannot be empty");

            if (code.Length > 10)
                throw new SubscriptionDomainException("Tax category code cannot exceed 10 characters");

            Name = name;
            Description = description;
            Code = code.ToUpperInvariant();
            IsActive = true;
            TaxConfigurations = new List<TaxConfiguration>();

            AddDomainEvent(new TaxCategoryCreatedEvent(Id, name, code));
        }

        public void UpdateDetails(string name, string description)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Tax category name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new SubscriptionDomainException("Tax category description cannot be empty");

            var oldName = Name;
            Name = name;
            Description = description;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxCategoryUpdatedEvent(Id, oldName, name, description));
        }

        public void AddTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            // Check for overlapping configurations of the same type and region
            var overlapping = TaxConfigurations.Any(tc => 
                tc.TaxType == taxConfiguration.TaxType &&
                tc.ApplicableRegions.Any(r => taxConfiguration.ApplicableRegions.Contains(r)) &&
                tc.IsActiveOn(taxConfiguration.EffectiveDate));

            if (overlapping)
                throw new SubscriptionDomainException("Overlapping tax configuration already exists for this type and region");

            TaxConfigurations.Add(taxConfiguration);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxConfigurationAddedEvent(Id, taxConfiguration.TaxType, taxConfiguration.Rate));
        }

        public void RemoveTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            var removed = TaxConfigurations.Remove(taxConfiguration);
            if (removed)
            {
                UpdatedAt = DateTime.UtcNow;
                AddDomainEvent(new TaxConfigurationRemovedEvent(Id, taxConfiguration.TaxType));
            }
        }

        public void Activate()
        {
            if (IsActive)
                return;

            IsActive = true;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxCategoryActivatedEvent(Id, Name));
        }

        public void Deactivate()
        {
            if (!IsActive)
                return;

            IsActive = false;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new TaxCategoryDeactivatedEvent(Id, Name));
        }

        public List<TaxConfiguration> GetActiveTaxConfigurations(DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            return TaxConfigurations.Where(tc => tc.IsActiveOn(checkDate)).ToList();
        }

        public List<TaxConfiguration> GetTaxConfigurationsForRegion(string region, DateTime? date = null)
        {
            if (string.IsNullOrWhiteSpace(region))
                return new List<TaxConfiguration>();

            var checkDate = date ?? DateTime.UtcNow;
            return TaxConfigurations
                .Where(tc => tc.IsActiveOn(checkDate) && tc.IsApplicableToRegion(region))
                .ToList();
        }

        public Money CalculateTotalTax(Money baseAmount, string region, DateTime? date = null)
        {
            var applicableConfigurations = GetTaxConfigurationsForRegion(region, date);
            
            if (!applicableConfigurations.Any())
                return Money.Zero(baseAmount.Currency);

            var totalTax = Money.Zero(baseAmount.Currency);
            var currentBase = baseAmount;

            // Apply taxes sequentially (compound taxation)
            foreach (var config in applicableConfigurations.OrderBy(c => c.TaxType))
            {
                var tax = config.CalculateTax(currentBase, region);
                totalTax = totalTax.Add(tax);
                
                // For compound taxation, add tax to base for next calculation
                currentBase = currentBase.Add(tax);
            }

            return totalTax;
        }
    }
}
