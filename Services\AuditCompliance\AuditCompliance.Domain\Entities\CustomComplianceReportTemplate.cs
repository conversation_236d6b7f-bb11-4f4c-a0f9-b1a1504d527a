using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.Events;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Domain.Entities
{
    /// <summary>
    /// Custom compliance report template entity
    /// </summary>
    public class CustomComplianceReportTemplate : AggregateRoot
    {
        public string TemplateName { get; private set; }
        public string Description { get; private set; }
        public ComplianceStandard ComplianceStandard { get; private set; }
        public ReportTemplateType TemplateType { get; private set; }
        public string TemplateContent { get; private set; }
        public List<ReportSection> Sections { get; private set; }
        public List<ReportParameter> Parameters { get; private set; }
        public ReportSchedule? Schedule { get; private set; }
        public List<string> Tags { get; private set; }
        public bool IsPublic { get; private set; }
        public bool IsActive { get; private set; }
        public Guid CreatedBy { get; private set; }
        public string CreatedByName { get; private set; }
        public DateTime? LastUsed { get; private set; }
        public int UsageCount { get; private set; }
        public List<TemplatePermission> Permissions { get; private set; }
        public string Version { get; private set; }
        public Dictionary<string, object> Metadata { get; private set; }

        private CustomComplianceReportTemplate()
        {
            TemplateName = string.Empty;
            Description = string.Empty;
            TemplateContent = string.Empty;
            CreatedByName = string.Empty;
            Version = string.Empty;
            Sections = new List<ReportSection>();
            Parameters = new List<ReportParameter>();
            Tags = new List<string>();
            Permissions = new List<TemplatePermission>();
            Metadata = new Dictionary<string, object>();
        }

        public CustomComplianceReportTemplate(
            string templateName,
            string description,
            ComplianceStandard complianceStandard,
            ReportTemplateType templateType,
            string templateContent,
            List<ReportSection> sections,
            List<ReportParameter> parameters,
            Guid createdBy,
            string createdByName,
            List<string> tags,
            bool isPublic = false,
            ReportSchedule? schedule = null)
        {
            if (string.IsNullOrWhiteSpace(templateName))
                throw new ArgumentException("Template name cannot be empty", nameof(templateName));
            if (string.IsNullOrWhiteSpace(templateContent))
                throw new ArgumentException("Template content cannot be empty", nameof(templateContent));

            TemplateName = templateName;
            Description = description ?? string.Empty;
            ComplianceStandard = complianceStandard;
            TemplateType = templateType;
            TemplateContent = templateContent;
            Sections = sections ?? new List<ReportSection>();
            Parameters = parameters ?? new List<ReportParameter>();
            Schedule = schedule;
            Tags = tags ?? new List<string>();
            IsPublic = isPublic;
            IsActive = true;
            CreatedBy = createdBy;
            CreatedByName = createdByName;
            UsageCount = 0;
            Permissions = new List<TemplatePermission>();
            Version = "1.0";
            Metadata = new Dictionary<string, object>();

            // Add domain event
            AddDomainEvent(new CustomReportTemplateCreatedEvent(this));
        }

        public void UpdateTemplate(
            string templateName,
            string description,
            string templateContent,
            List<ReportSection> sections,
            List<ReportParameter> parameters,
            List<string> tags,
            ReportSchedule? schedule = null)
        {
            if (string.IsNullOrWhiteSpace(templateName))
                throw new ArgumentException("Template name cannot be empty", nameof(templateName));
            if (string.IsNullOrWhiteSpace(templateContent))
                throw new ArgumentException("Template content cannot be empty", nameof(templateContent));

            TemplateName = templateName;
            Description = description ?? string.Empty;
            TemplateContent = templateContent;
            Sections = sections ?? new List<ReportSection>();
            Parameters = parameters ?? new List<ReportParameter>();
            Tags = tags ?? new List<string>();
            Schedule = schedule;

            // Increment version
            var versionParts = Version.Split('.');
            if (versionParts.Length >= 2 && int.TryParse(versionParts[1], out var minorVersion))
            {
                Version = $"{versionParts[0]}.{minorVersion + 1}";
            }
            else
            {
                Version = "1.1";
            }

            // Add domain event
            AddDomainEvent(new CustomReportTemplateUpdatedEvent(this));
        }

        public void Activate()
        {
            if (!IsActive)
            {
                IsActive = true;
                AddDomainEvent(new CustomReportTemplateActivatedEvent(this));
            }
        }

        public void Deactivate()
        {
            if (IsActive)
            {
                IsActive = false;
                AddDomainEvent(new CustomReportTemplateDeactivatedEvent(this));
            }
        }

        public void MakePublic()
        {
            if (!IsPublic)
            {
                IsPublic = true;
                AddDomainEvent(new CustomReportTemplateMadePublicEvent(this));
            }
        }

        public void MakePrivate()
        {
            if (IsPublic)
            {
                IsPublic = false;
                AddDomainEvent(new CustomReportTemplateMadePrivateEvent(this));
            }
        }

        public void RecordUsage()
        {
            UsageCount++;
            LastUsed = DateTime.UtcNow;
            AddDomainEvent(new CustomReportTemplateUsedEvent(this));
        }

        public void GrantPermission(Guid userId, string userName, TemplatePermissionType permissionType, Guid grantedBy)
        {
            var existingPermission = Permissions.FirstOrDefault(p => p.UserId == userId);
            if (existingPermission != null)
            {
                existingPermission.UpdatePermission(permissionType, grantedBy);
            }
            else
            {
                var permission = new TemplatePermission(userId, userName, permissionType, grantedBy);
                Permissions.Add(permission);
            }

            AddDomainEvent(new TemplatePermissionGrantedEvent(this, userId, permissionType));
        }

        public void RevokePermission(Guid userId, Guid revokedBy)
        {
            var permission = Permissions.FirstOrDefault(p => p.UserId == userId);
            if (permission != null)
            {
                permission.Revoke(revokedBy);
                AddDomainEvent(new TemplatePermissionRevokedEvent(this, userId));
            }
        }

        public bool HasPermission(Guid userId, TemplatePermissionType requiredPermission)
        {
            if (CreatedBy == userId)
                return true; // Creator has all permissions

            if (IsPublic && requiredPermission == TemplatePermissionType.View)
                return true; // Public templates can be viewed by anyone

            var permission = Permissions.FirstOrDefault(p => p.UserId == userId && p.IsActive);
            return permission?.PermissionType >= requiredPermission;
        }

        public void SetMetadata(string key, object value)
        {
            Metadata[key] = value;
        }

        public T? GetMetadata<T>(string key)
        {
            if (Metadata.TryGetValue(key, out var value))
            {
                try
                {
                    return (T?)value;
                }
                catch
                {
                    return default(T);
                }
            }
            return default(T);
        }

        public void AddSection(ReportSection section)
        {
            if (section == null)
                throw new ArgumentNullException(nameof(section));

            Sections.Add(section);
        }

        public void RemoveSection(string sectionId)
        {
            Sections.RemoveAll(s => s.Id == sectionId);
        }

        public void AddParameter(ReportParameter parameter)
        {
            if (parameter == null)
                throw new ArgumentNullException(nameof(parameter));

            Parameters.Add(parameter);
        }

        public void RemoveParameter(string parameterId)
        {
            Parameters.RemoveAll(p => p.Id == parameterId);
        }

        public bool ValidateTemplate()
        {
            // Basic validation logic
            if (string.IsNullOrWhiteSpace(TemplateName) || string.IsNullOrWhiteSpace(TemplateContent))
                return false;

            // Validate sections
            if (Sections.Any(s => string.IsNullOrWhiteSpace(s.Name)))
                return false;

            // Validate parameters
            if (Parameters.Any(p => string.IsNullOrWhiteSpace(p.Name)))
                return false;

            return true;
        }
    }

    /// <summary>
    /// Report template type enumeration
    /// </summary>
    public enum ReportTemplateType
    {
        Standard = 0,
        Custom = 1,
        Scheduled = 2,
        Dashboard = 3,
        Export = 4
    }

    /// <summary>
    /// Template permission type enumeration
    /// </summary>
    public enum TemplatePermissionType
    {
        View = 0,
        Use = 1,
        Edit = 2,
        Manage = 3,
        Admin = 4
    }
}



