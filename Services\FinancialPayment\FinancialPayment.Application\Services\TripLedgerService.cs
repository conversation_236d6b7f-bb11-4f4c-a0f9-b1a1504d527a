using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Repositories;
using FinancialPayment.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace FinancialPayment.Application.Services;

/// <summary>
/// Service for managing Trip Ledger operations
/// </summary>
public interface ITripLedgerService
{
    Task<TripLedgerDto> GetTripLedgerAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<TripLedgerDto> GetTripLedgerByIdAsync(Guid ledgerId, CancellationToken cancellationToken = default);
    Task<List<TripLedgerSummaryDto>> GetTripLedgersByTransportCompanyAsync(
        Guid transportCompanyId,
        TripLedgerFilterDto filter,
        CancellationToken cancellationToken = default);
    Task<TripLedgerDto> CreateTripLedgerAsync(CreateTripLedgerDto request, CancellationToken cancellationToken = default);
    Task AddLedgerEntryAsync(Guid ledgerId, AddTripLedgerEntryDto request, CancellationToken cancellationToken = default);
    Task AddMilestoneAsync(Guid ledgerId, AddTripLedgerMilestoneDto request, CancellationToken cancellationToken = default);
    Task CompleteMilestoneAsync(Guid ledgerId, Guid milestoneId, CompleteMilestoneDto request, CancellationToken cancellationToken = default);
    Task AddAdjustmentAsync(Guid ledgerId, AddTripLedgerAdjustmentDto request, CancellationToken cancellationToken = default);
    Task<TripLedgerBalanceDto> GetLedgerBalanceAsync(Guid ledgerId, CancellationToken cancellationToken = default);
    Task<List<TripLedgerEntryDto>> GetLedgerEntriesAsync(
        Guid ledgerId,
        TripLedgerEntryFilterDto filter,
        CancellationToken cancellationToken = default);
    Task<byte[]> ExportLedgerAsync(Guid ledgerId, ExportFormat format, CancellationToken cancellationToken = default);
    Task<TripLedgerAnalyticsDto> GetLedgerAnalyticsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);
}

public class TripLedgerService : ITripLedgerService
{
    private readonly ITripLedgerRepository _tripLedgerRepository;
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<TripLedgerService> _logger;

    public TripLedgerService(
        ITripLedgerRepository tripLedgerRepository,
        IEscrowAccountRepository escrowAccountRepository,
        IMemoryCache cache,
        ILogger<TripLedgerService> logger)
    {
        _tripLedgerRepository = tripLedgerRepository;
        _escrowAccountRepository = escrowAccountRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<TripLedgerDto> GetTripLedgerAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting trip ledger for Trip {TripId}", tripId);

        var cacheKey = $"trip_ledger_{tripId}";
        if (_cache.TryGetValue(cacheKey, out TripLedgerDto? cachedLedger))
        {
            return cachedLedger!;
        }

        var ledger = await _tripLedgerRepository.GetByTripIdAsync(tripId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger not found for trip {tripId}");
        }

        var ledgerDto = MapToDto(ledger);
        _cache.Set(cacheKey, ledgerDto, TimeSpan.FromMinutes(15));

        return ledgerDto;
    }

    public async Task<TripLedgerDto> GetTripLedgerByIdAsync(Guid ledgerId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting trip ledger {LedgerId}", ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        return MapToDto(ledger);
    }

    public async Task<List<TripLedgerSummaryDto>> GetTripLedgersByTransportCompanyAsync(
        Guid transportCompanyId,
        TripLedgerFilterDto filter,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting trip ledgers for Transport Company {TransportCompanyId}", transportCompanyId);

        var ledgers = await _tripLedgerRepository.GetByTransportCompanyAsync(transportCompanyId);

        return ledgers.Select(MapToSummaryDto).ToList();
    }

    public async Task<TripLedgerDto> CreateTripLedgerAsync(CreateTripLedgerDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating trip ledger for Trip {TripId}", request.TripId);

        // Check if ledger already exists
        var existingLedger = await _tripLedgerRepository.GetByTripIdAsync(request.TripId, cancellationToken);
        if (existingLedger != null)
        {
            throw new InvalidOperationException($"Trip ledger already exists for trip {request.TripId}");
        }

        var totalOrderValue = new Money(request.TotalOrderValue.Amount, request.TotalOrderValue.Currency);

        var ledger = new TripLedger(
            request.TripId,
            request.OrderId,
            request.TransportCompanyId,
            request.TripNumber,
            totalOrderValue,
            request.TripStartDate,
            request.BrokerId,
            request.CarrierId,
            request.Notes);

        await _tripLedgerRepository.AddAsync(ledger, cancellationToken);
        await _tripLedgerRepository.SaveChangesAsync();

        _logger.LogInformation("Created trip ledger {LedgerId} for Trip {TripId}", ledger.Id, request.TripId);

        return MapToDto(ledger);
    }

    public async Task AddLedgerEntryAsync(Guid ledgerId, AddTripLedgerEntryDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Adding ledger entry to Trip Ledger {LedgerId}", ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        var amount = new Money(request.Amount.Amount, request.Amount.Currency);

        ledger.AddEntry(
            request.EntryType,
            amount,
            request.Description,
            request.RelatedEntityId,
            request.RelatedEntityType,
            request.Metadata);

        await _tripLedgerRepository.SaveChangesAsync();

        // Clear cache
        _cache.Remove($"trip_ledger_{ledger.TripId}");

        _logger.LogInformation("Added {EntryType} entry of {Amount} to Trip Ledger {LedgerId}",
            request.EntryType, amount, ledgerId);
    }

    public async Task AddMilestoneAsync(Guid ledgerId, AddTripLedgerMilestoneDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Adding milestone to Trip Ledger {LedgerId}", ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        var amount = new Money(request.Amount.Amount, request.Amount.Currency);

        ledger.AddMilestone(
            request.MilestoneName,
            amount,
            request.DueDate,
            request.Description,
            request.IsCompleted,
            request.CompletedDate);

        await _tripLedgerRepository.SaveChangesAsync();

        // Clear cache
        _cache.Remove($"trip_ledger_{ledger.TripId}");

        _logger.LogInformation("Added milestone '{MilestoneName}' to Trip Ledger {LedgerId}",
            request.MilestoneName, ledgerId);
    }

    public async Task CompleteMilestoneAsync(Guid ledgerId, Guid milestoneId, CompleteMilestoneDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Completing milestone {MilestoneId} in Trip Ledger {LedgerId}", milestoneId, ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        ledger.CompleteMilestone(milestoneId, request.CompletedDate, request.Notes);

        await _tripLedgerRepository.SaveChangesAsync();

        // Clear cache
        _cache.Remove($"trip_ledger_{ledger.TripId}");

        _logger.LogInformation("Completed milestone {MilestoneId} in Trip Ledger {LedgerId}", milestoneId, ledgerId);
    }

    public async Task AddAdjustmentAsync(Guid ledgerId, AddTripLedgerAdjustmentDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Adding adjustment to Trip Ledger {LedgerId}", ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        var amount = new Money(request.Amount.Amount, request.Amount.Currency);

        ledger.AddAdjustment(
            request.AdjustmentType,
            amount,
            request.Reason,
            request.AdjustedBy,
            request.ApprovalReference);

        await _tripLedgerRepository.SaveChangesAsync();

        // Clear cache
        _cache.Remove($"trip_ledger_{ledger.TripId}");

        _logger.LogInformation("Added {AdjustmentType} adjustment of {Amount} to Trip Ledger {LedgerId}",
            request.AdjustmentType, amount, ledgerId);
    }

    public async Task<TripLedgerBalanceDto> GetLedgerBalanceAsync(Guid ledgerId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting balance for Trip Ledger {LedgerId}", ledgerId);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        return new TripLedgerBalanceDto
        {
            LedgerId = ledger.Id,
            TripId = ledger.TripId,
            TotalOrderValue = MapMoneyDto(ledger.TotalOrderValue),
            TotalPaid = MapMoneyDto(ledger.TotalPaid),
            TotalPending = MapMoneyDto(ledger.TotalPending),
            TotalCommissions = MapMoneyDto(ledger.TotalCommissions),
            TotalTaxes = MapMoneyDto(ledger.TotalTaxes),
            NetAmount = MapMoneyDto(ledger.NetAmount),
            OutstandingBalance = MapMoneyDto(ledger.OutstandingBalance),
            LastUpdated = ledger.LastUpdated
        };
    }

    public async Task<List<TripLedgerEntryDto>> GetLedgerEntriesAsync(
        Guid ledgerId,
        TripLedgerEntryFilterDto filter,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting entries for Trip Ledger {LedgerId}", ledgerId);

        var entries = await _tripLedgerRepository.GetEntriesAsync(ledgerId);

        return entries.Select(MapEntryToDto).ToList();
    }

    public async Task<byte[]> ExportLedgerAsync(Guid ledgerId, ExportFormat format, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Exporting Trip Ledger {LedgerId} in {Format} format", ledgerId, format);

        var ledger = await _tripLedgerRepository.GetByIdAsync(ledgerId, cancellationToken);
        if (ledger == null)
        {
            throw new InvalidOperationException($"Trip ledger {ledgerId} not found");
        }

        // Implementation would depend on export format
        // For now, return empty byte array
        return Array.Empty<byte>();
    }

    public async Task<TripLedgerAnalyticsDto> GetLedgerAnalyticsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting ledger analytics for Transport Company {TransportCompanyId}", transportCompanyId);

        var analytics = await _tripLedgerRepository.GetAnalyticsAsync(transportCompanyId);

        return analytics;
    }

    private TripLedgerDto MapToDto(TripLedger ledger)
    {
        return new TripLedgerDto
        {
            Id = ledger.Id,
            TripId = ledger.TripId,
            OrderId = ledger.OrderId,
            TransportCompanyId = ledger.TransportCompanyId,
            BrokerId = ledger.BrokerId,
            CarrierId = ledger.CarrierId,
            TripNumber = ledger.TripNumber,
            LedgerNumber = ledger.LedgerNumber,
            TotalOrderValue = MapMoneyDto(ledger.TotalOrderValue),
            TotalPaid = MapMoneyDto(ledger.TotalPaid),
            TotalPending = MapMoneyDto(ledger.TotalPending),
            TotalCommissions = MapMoneyDto(ledger.TotalCommissions),
            TotalTaxes = MapMoneyDto(ledger.TotalTaxes),
            NetAmount = MapMoneyDto(ledger.NetAmount),
            OutstandingBalance = MapMoneyDto(ledger.OutstandingBalance),
            Status = ledger.Status,
            TripStartDate = ledger.TripStartDate,
            TripEndDate = ledger.TripEndDate,
            SettlementDate = ledger.SettlementDate,
            LastUpdated = ledger.LastUpdated,
            Entries = ledger.Entries.Select(MapEntryToDto).ToList(),
            Milestones = ledger.Milestones.Select(MapMilestoneToDto).ToList(),
            Adjustments = ledger.Adjustments.Select(MapAdjustmentToDto).ToList(),
            Notes = ledger.Notes
        };
    }

    private TripLedgerSummaryDto MapToSummaryDto(TripLedger ledger)
    {
        var summary = ledger.GetSummary();
        return new TripLedgerSummaryDto
        {
            LedgerId = summary.LedgerId,
            TripId = summary.TripId,
            OrderId = summary.OrderId,
            LedgerNumber = summary.LedgerNumber,
            TripNumber = summary.TripNumber,
            Status = summary.Status,
            TotalOrderValue = MapMoneyDto(summary.TotalOrderValue),
            TotalPaid = MapMoneyDto(summary.TotalPaid),
            OutstandingBalance = MapMoneyDto(summary.OutstandingBalance),
            CompletionPercentage = summary.CompletionPercentage,
            PaymentPercentage = summary.PaymentPercentage,
            IsOverdue = summary.IsOverdue,
            TripStartDate = summary.TripStartDate,
            TripEndDate = summary.TripEndDate,
            LastUpdated = summary.LastUpdated
        };
    }

    private TripLedgerEntryDto MapEntryToDto(TripLedgerEntry entry)
    {
        return new TripLedgerEntryDto
        {
            Id = entry.Id,
            EntryType = entry.EntryType,
            Amount = MapMoneyDto(entry.Amount),
            Description = entry.Description,
            EntryDate = entry.EntryDate,
            ReferenceNumber = entry.ReferenceNumber,
            RelatedEntityId = entry.RelatedEntityId,
            RelatedEntityType = entry.RelatedEntityType
        };
    }

    private TripLedgerMilestoneDto MapMilestoneToDto(TripLedgerMilestone milestone)
    {
        return new TripLedgerMilestoneDto
        {
            Id = milestone.Id,
            MilestoneName = milestone.MilestoneName,
            Amount = MapMoneyDto(milestone.Amount),
            DueDate = milestone.DueDate,
            Description = milestone.Description,
            IsCompleted = milestone.IsCompleted,
            CompletedDate = milestone.CompletedDate,
            CompletionNotes = milestone.CompletionNotes,
            SortOrder = milestone.SortOrder
        };
    }

    private TripLedgerAdjustmentDto MapAdjustmentToDto(TripLedgerAdjustment adjustment)
    {
        return new TripLedgerAdjustmentDto
        {
            Id = adjustment.Id,
            AdjustmentType = adjustment.AdjustmentType,
            Amount = MapMoneyDto(adjustment.Amount),
            Reason = adjustment.Reason,
            AdjustmentDate = adjustment.AdjustmentDate,
            Status = adjustment.Status,
            ApprovalReference = adjustment.ApprovalReference,
            RejectionReason = adjustment.RejectionReason
        };
    }

    private MoneyDto MapMoneyDto(Money money)
    {
        return new MoneyDto
        {
            Amount = money.Amount,
            Currency = money.Currency
        };
    }
}
