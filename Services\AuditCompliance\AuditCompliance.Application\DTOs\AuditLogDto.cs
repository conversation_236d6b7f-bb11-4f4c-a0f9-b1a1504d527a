using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs;

public class AuditLogDto
{
    public Guid Id { get; set; }
    public AuditEventType EventType { get; set; }
    public AuditSeverity Severity { get; set; }
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserRole { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public DateTime EventTimestamp { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<ComplianceStandard> ComplianceFlags { get; set; } = new();
}

public class CreateAuditLogDto
{
    public AuditEventType EventType { get; set; }
    public AuditSeverity Severity { get; set; }
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserRole { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public List<ComplianceStandard>? ComplianceFlags { get; set; }
}

public class AuditTrailQueryDto
{
    public Guid? UserId { get; set; }
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public AuditEventType? EventType { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public bool IncludeSensitiveData { get; set; } = false;
}

public class AuditTrailResultDto
{
    public List<AuditLogDto> AuditLogs { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}
