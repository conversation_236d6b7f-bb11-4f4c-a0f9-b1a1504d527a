using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Persistence.Configurations;

public class FeatureFlagUsageConfiguration : IEntityTypeConfiguration<FeatureFlagUsage>
{
    public void Configure(EntityTypeBuilder<FeatureFlagUsage> builder)
    {
        builder.ToTable("FeatureFlagUsage");

        builder.HasKey(u => u.Id);

        builder.Property(u => u.FeatureFlagId)
            .IsRequired();

        builder.Property(u => u.UserId)
            .IsRequired();

        builder.Property(u => u.Variant)
            .HasMaxLength(50);

        builder.Property(u => u.AccessedAt)
            .IsRequired();

        builder.Property(u => u.UserAgent)
            .HasMaxLength(500);

        builder.Property(u => u.IpAddress)
            .HasMaxLength(45); // IPv6 max length

        builder.Property(u => u.SessionId)
            .HasMaxLength(100);

        // Configure the Context property as JSON
        builder.Property(u => u.Context)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb"); // PostgreSQL JSONB type

        builder.Property(u => u.CreatedAt)
            .IsRequired();

        builder.Property(u => u.UpdatedAt);

        // Indexes
        builder.HasIndex(u => u.UserId);
        builder.HasIndex(u => u.AccessedAt);
        builder.HasIndex(u => new { u.FeatureFlagId, u.UserId });
        builder.HasIndex(u => new { u.FeatureFlagId, u.Variant });
        builder.HasIndex(u => u.SessionId);
    }
}
