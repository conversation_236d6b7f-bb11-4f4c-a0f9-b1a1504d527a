# Mobile App Integration Guide

## Overview

This guide provides comprehensive instructions for integrating with the TLI Mobile & Workflow Service across different platforms including native mobile apps, Progressive Web Apps (PWA), and responsive web applications.

## Platform Support

### Supported Platforms
- **Android**: Native Android apps (API level 26+)
- **iOS**: Native iOS apps (iOS 14.0+)
- **Progressive Web App (PWA)**: Modern browsers with service worker support
- **Responsive Web**: Desktop and tablet browsers

### Feature Matrix

| Feature | Android | iOS | PWA | Web |
|---------|---------|-----|-----|-----|
| Offline Mode | ✅ | ✅ | ✅ | ❌ |
| Push Notifications | ✅ | ✅ | ✅ | ❌ |
| Background Sync | ✅ | ✅ | ✅ | ❌ |
| Camera Access | ✅ | ✅ | ✅ | ✅ |
| Location Services | ✅ | ✅ | ✅ | ✅ |
| Biometric Auth | ✅ | ✅ | ✅* | ❌ |
| File Upload | ✅ | ✅ | ✅ | ✅ |
| Voice Recording | ✅ | ✅ | ✅ | ✅ |
| QR Code Scanner | ✅ | ✅ | ✅ | ✅ |

*PWA biometric auth requires HTTPS and compatible browser

## Getting Started

### 1. Register Your Mobile App

First, register your mobile app with the service:

```http
POST /api/mobileapps
Content-Type: application/json
Authorization: Bearer <admin-token>

{
  "name": "Your App Name",
  "version": "1.0.0",
  "platform": "Android",
  "packageId": "com.yourcompany.yourapp",
  "releaseDate": "2024-01-15T10:00:00Z",
  "minimumOSVersion": "8.0",
  "features": {
    "offline_mode": true,
    "gps_tracking": true,
    "camera_access": true,
    "push_notifications": true
  },
  "configuration": {
    "sync_interval": 300,
    "max_offline_storage": 100,
    "auto_sync": true
  },
  "supportsOffline": true,
  "downloadUrl": "https://your-app-store-url",
  "fileSize": 25000000,
  "checksum": "your-app-checksum"
}
```

### 2. Initialize Mobile Session

When your app starts, create a mobile session:

```http
POST /api/mobilesessions
Content-Type: application/json
Authorization: Bearer <user-token>

{
  "mobileAppId": "uuid-from-registration",
  "deviceId": "unique-device-identifier",
  "deviceInfo": "Samsung Galaxy S21, Android 11",
  "appVersion": "1.0.0",
  "platform": "Android",
  "osVersion": "11.0",
  "isOfflineCapable": true
}
```

## Android Integration

### Dependencies

Add to your `build.gradle`:

```gradle
dependencies {
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.0'
    implementation 'androidx.work:work-runtime:2.8.1'
    implementation 'androidx.room:room-runtime:2.4.3'
    kapt 'androidx.room:room-compiler:2.4.3'
}
```

### API Client Setup

```kotlin
class TLIApiClient {
    private val retrofit = Retrofit.Builder()
        .baseUrl("https://api.tli.com/")
        .addConverterFactory(GsonConverterFactory.create())
        .client(
            OkHttpClient.Builder()
                .addInterceptor { chain ->
                    val request = chain.request().newBuilder()
                        .addHeader("Authorization", "Bearer $authToken")
                        .build()
                    chain.proceed(request)
                }
                .build()
        )
        .build()

    val mobileService: MobileService = retrofit.create(MobileService::class.java)
}

interface MobileService {
    @POST("api/mobilesessions")
    suspend fun createSession(@Body session: CreateSessionRequest): SessionResponse

    @GET("api/drivermobile/dashboard")
    suspend fun getDashboard(): DriverDashboard

    @POST("api/drivermobile/trip-assignments/{tripId}/accept")
    suspend fun acceptTrip(@Path("tripId") tripId: String): TripAssignmentResult

    @POST("api/drivermobile/location")
    suspend fun updateLocation(@Body location: LocationUpdate): Response<Unit>

    @POST("api/drivermobile/trips/{tripId}/pod")
    suspend fun uploadPOD(@Path("tripId") tripId: String, @Body pod: PODData): PODUploadResult
}
```

### Offline Data Management

```kotlin
@Entity(tableName = "offline_actions")
data class OfflineAction(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val actionType: String,
    val data: String, // JSON data
    val timestamp: Long,
    val priority: Int,
    val isSynced: Boolean = false
)

@Dao
interface OfflineActionDao {
    @Query("SELECT * FROM offline_actions WHERE isSynced = 0 ORDER BY priority ASC, timestamp ASC")
    suspend fun getUnsyncedActions(): List<OfflineAction>

    @Insert
    suspend fun insertAction(action: OfflineAction)

    @Update
    suspend fun updateAction(action: OfflineAction)
}

class OfflineManager(
    private val dao: OfflineActionDao,
    private val apiClient: TLIApiClient
) {
    suspend fun recordOfflineAction(actionType: String, data: Any, priority: Int = 2) {
        val action = OfflineAction(
            actionType = actionType,
            data = Gson().toJson(data),
            timestamp = System.currentTimeMillis(),
            priority = priority
        )
        dao.insertAction(action)
    }

    suspend fun syncOfflineData() {
        val unsyncedActions = dao.getUnsyncedActions()
        for (action in unsyncedActions) {
            try {
                when (action.actionType) {
                    "LOCATION_UPDATE" -> {
                        val location = Gson().fromJson(action.data, LocationUpdate::class.java)
                        apiClient.mobileService.updateLocation(location)
                    }
                    "TRIP_ACCEPT" -> {
                        val tripData = Gson().fromJson(action.data, JsonObject::class.java)
                        val tripId = tripData.get("tripId").asString
                        apiClient.mobileService.acceptTrip(tripId)
                    }
                    // Handle other action types
                }
                
                // Mark as synced
                dao.updateAction(action.copy(isSynced = true))
            } catch (e: Exception) {
                // Handle sync error
                Log.e("OfflineManager", "Failed to sync action: ${action.id}", e)
            }
        }
    }
}
```

### Background Sync with WorkManager

```kotlin
class SyncWorker(context: Context, params: WorkerParameters) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        return try {
            val offlineManager = (applicationContext as TLIApplication).offlineManager
            offlineManager.syncOfflineData()
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

// Schedule periodic sync
val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(15, TimeUnit.MINUTES)
    .setConstraints(
        Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
    )
    .build()

WorkManager.getInstance(context).enqueueUniquePeriodicWork(
    "sync_offline_data",
    ExistingPeriodicWorkPolicy.KEEP,
    syncRequest
)
```

## iOS Integration

### Dependencies

Add to your `Package.swift`:

```swift
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.6.0"),
    .package(url: "https://github.com/realm/realm-swift.git", from: "10.0.0")
]
```

### API Client Setup

```swift
import Alamofire
import Foundation

class TLIApiClient {
    private let baseURL = "https://api.tli.com/"
    private var authToken: String?
    
    private var session: Session {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        
        let interceptor = AuthenticationInterceptor(authToken: authToken)
        return Session(configuration: configuration, interceptor: interceptor)
    }
    
    func createSession(_ request: CreateSessionRequest, completion: @escaping (Result<SessionResponse, Error>) -> Void) {
        session.request("\(baseURL)api/mobilesessions", 
                        method: .post, 
                        parameters: request, 
                        encoder: JSONParameterEncoder.default)
            .responseDecodable(of: SessionResponse.self) { response in
                completion(response.result)
            }
    }
    
    func getDashboard(completion: @escaping (Result<DriverDashboard, Error>) -> Void) {
        session.request("\(baseURL)api/drivermobile/dashboard")
            .responseDecodable(of: DriverDashboard.self) { response in
                completion(response.result)
            }
    }
    
    func acceptTrip(_ tripId: String, completion: @escaping (Result<TripAssignmentResult, Error>) -> Void) {
        session.request("\(baseURL)api/drivermobile/trip-assignments/\(tripId)/accept", method: .post)
            .responseDecodable(of: TripAssignmentResult.self) { response in
                completion(response.result)
            }
    }
}
```

### Offline Data with Realm

```swift
import RealmSwift

class OfflineAction: Object {
    @Persisted var id = UUID().uuidString
    @Persisted var actionType = ""
    @Persisted var data = ""
    @Persisted var timestamp = Date()
    @Persisted var priority = 2
    @Persisted var isSynced = false
    
    override static func primaryKey() -> String? {
        return "id"
    }
}

class OfflineManager {
    private let realm: Realm
    private let apiClient: TLIApiClient
    
    init(realm: Realm, apiClient: TLIApiClient) {
        self.realm = realm
        self.apiClient = apiClient
    }
    
    func recordOfflineAction(actionType: String, data: [String: Any], priority: Int = 2) {
        let action = OfflineAction()
        action.actionType = actionType
        action.data = try! JSONSerialization.data(with: data).base64EncodedString()
        action.priority = priority
        
        try! realm.write {
            realm.add(action)
        }
    }
    
    func syncOfflineData() {
        let unsyncedActions = realm.objects(OfflineAction.self)
            .filter("isSynced == false")
            .sorted(byKeyPath: "priority")
        
        for action in unsyncedActions {
            syncAction(action)
        }
    }
    
    private func syncAction(_ action: OfflineAction) {
        guard let data = Data(base64Encoded: action.data),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return
        }
        
        switch action.actionType {
        case "LOCATION_UPDATE":
            // Handle location update sync
            break
        case "TRIP_ACCEPT":
            if let tripId = json["tripId"] as? String {
                apiClient.acceptTrip(tripId) { [weak self] result in
                    if case .success = result {
                        self?.markAsSynced(action)
                    }
                }
            }
        default:
            break
        }
    }
    
    private func markAsSynced(_ action: OfflineAction) {
        try! realm.write {
            action.isSynced = true
        }
    }
}
```

## Progressive Web App (PWA) Integration

### Service Worker Setup

Create `service-worker.js`:

```javascript
const CACHE_NAME = 'tli-mobile-v1';
const API_BASE_URL = 'https://api.tli.com/api';

// Install event - cache essential resources
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME).then(cache => {
            return cache.addAll([
                '/',
                '/offline.html',
                '/css/app.css',
                '/js/app.js',
                '/icons/icon-192x192.png'
            ]);
        })
    );
});

// Fetch event - implement cache strategies
self.addEventListener('fetch', event => {
    const { request } = event;
    
    // API requests - network first, cache fallback
    if (request.url.startsWith(API_BASE_URL)) {
        event.respondWith(
            fetch(request)
                .then(response => {
                    // Cache successful GET requests
                    if (request.method === 'GET' && response.ok) {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME).then(cache => {
                            cache.put(request, responseClone);
                        });
                    }
                    return response;
                })
                .catch(() => {
                    // Return cached response if available
                    return caches.match(request);
                })
        );
    } else {
        // Static resources - cache first
        event.respondWith(
            caches.match(request).then(response => {
                return response || fetch(request);
            })
        );
    }
});

// Background sync
self.addEventListener('sync', event => {
    if (event.tag === 'offline-sync') {
        event.waitUntil(syncOfflineData());
    }
});

async function syncOfflineData() {
    const offlineActions = await getOfflineActions();
    
    for (const action of offlineActions) {
        try {
            await syncAction(action);
            await markActionAsSynced(action.id);
        } catch (error) {
            console.error('Failed to sync action:', action.id, error);
        }
    }
}
```

### PWA Manifest

Create `manifest.json`:

```json
{
  "name": "TLI Logistics Mobile",
  "short_name": "TLI Mobile",
  "description": "TLI Logistics mobile application for drivers and carriers",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#2196F3",
  "orientation": "portrait-primary",
  "scope": "/",
  "icons": [
    {
      "src": "/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ],
  "shortcuts": [
    {
      "name": "New Trip",
      "short_name": "Trip",
      "url": "/trips/new",
      "icons": [
        {
          "src": "/icons/trip-icon.png",
          "sizes": "96x96"
        }
      ]
    },
    {
      "name": "Upload POD",
      "short_name": "POD",
      "url": "/pod/upload",
      "icons": [
        {
          "src": "/icons/pod-icon.png",
          "sizes": "96x96"
        }
      ]
    }
  ]
}
```

### JavaScript API Client

```javascript
class TLIApiClient {
    constructor(baseUrl, authToken) {
        this.baseUrl = baseUrl;
        this.authToken = authToken;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.authToken}`,
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            // Store failed request for offline sync
            if (!navigator.onLine) {
                await this.storeOfflineAction(endpoint, options);
            }
            throw error;
        }
    }

    async storeOfflineAction(endpoint, options) {
        const action = {
            id: generateUUID(),
            endpoint,
            options,
            timestamp: Date.now(),
            priority: this.getPriority(endpoint)
        };

        const db = await openDB();
        await db.add('offlineActions', action);
        
        // Register for background sync
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            const registration = await navigator.serviceWorker.ready;
            await registration.sync.register('offline-sync');
        }
    }

    getPriority(endpoint) {
        if (endpoint.includes('emergency') || endpoint.includes('pod')) return 1;
        if (endpoint.includes('trip') || endpoint.includes('location')) return 2;
        return 3;
    }

    // Driver Mobile API methods
    async getDashboard() {
        return this.request('/api/drivermobile/dashboard');
    }

    async acceptTrip(tripId) {
        return this.request(`/api/drivermobile/trip-assignments/${tripId}/accept`, {
            method: 'POST'
        });
    }

    async updateLocation(locationData) {
        return this.request('/api/drivermobile/location', {
            method: 'POST',
            body: JSON.stringify(locationData)
        });
    }

    async uploadPOD(tripId, podData) {
        return this.request(`/api/drivermobile/trips/${tripId}/pod`, {
            method: 'POST',
            body: JSON.stringify(podData)
        });
    }
}
```

## Best Practices

### 1. Error Handling

```javascript
// Implement retry logic with exponential backoff
class RetryableApiClient extends TLIApiClient {
    async requestWithRetry(endpoint, options = {}, maxRetries = 3) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await this.request(endpoint, options);
            } catch (error) {
                lastError = error;
                
                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        throw lastError;
    }
}
```

### 2. Data Validation

```javascript
// Validate data before sending
function validateLocationData(location) {
    const required = ['latitude', 'longitude', 'timestamp'];
    const missing = required.filter(field => !(field in location));
    
    if (missing.length > 0) {
        throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }
    
    if (Math.abs(location.latitude) > 90 || Math.abs(location.longitude) > 180) {
        throw new Error('Invalid coordinates');
    }
    
    return true;
}
```

### 3. Security

```javascript
// Implement token refresh
class SecureApiClient extends TLIApiClient {
    async refreshToken() {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refreshToken })
        });
        
        const { accessToken } = await response.json();
        this.authToken = accessToken;
        localStorage.setItem('accessToken', accessToken);
    }

    async request(endpoint, options = {}) {
        try {
            return await super.request(endpoint, options);
        } catch (error) {
            if (error.message.includes('401')) {
                await this.refreshToken();
                return super.request(endpoint, options);
            }
            throw error;
        }
    }
}
```

## Testing

### Unit Testing Example

```javascript
// Jest test example
describe('TLIApiClient', () => {
    let apiClient;
    
    beforeEach(() => {
        apiClient = new TLIApiClient('https://api.test.com', 'test-token');
        global.fetch = jest.fn();
    });

    test('should accept trip successfully', async () => {
        const mockResponse = {
            isSuccess: true,
            tripId: 'test-trip-id',
            status: 'Accepted'
        };
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: async () => mockResponse
        });

        const result = await apiClient.acceptTrip('test-trip-id');
        
        expect(result).toEqual(mockResponse);
        expect(fetch).toHaveBeenCalledWith(
            'https://api.test.com/api/drivermobile/trip-assignments/test-trip-id/accept',
            expect.objectContaining({
                method: 'POST',
                headers: expect.objectContaining({
                    'Authorization': 'Bearer test-token'
                })
            })
        );
    });
});
```

## Troubleshooting

### Common Issues

1. **Authentication Errors (401)**
   - Verify JWT token is valid and not expired
   - Check token format: `Bearer <token>`
   - Ensure user has required permissions

2. **Network Connectivity**
   - Implement offline detection
   - Store failed requests for retry
   - Provide user feedback for network status

3. **Data Synchronization**
   - Check sync priority settings
   - Verify conflict resolution strategies
   - Monitor sync queue size

4. **Performance Issues**
   - Implement request batching
   - Use appropriate cache strategies
   - Optimize payload sizes

### Debug Mode

Enable debug logging:

```javascript
const apiClient = new TLIApiClient('https://api.tli.com', token, {
    debug: true,
    logLevel: 'verbose'
});
```

This will log all API requests, responses, and offline actions for troubleshooting.
