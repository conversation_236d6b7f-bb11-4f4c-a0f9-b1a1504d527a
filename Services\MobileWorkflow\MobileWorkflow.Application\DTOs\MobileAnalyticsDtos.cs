namespace MobileWorkflow.Application.DTOs;

public class AnalyticsEventDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> Context { get; set; } = new();
    public DateTime Timestamp { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string? ScreenName { get; set; }
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
    public string? Location { get; set; }
    public double? Duration { get; set; }
    public string? ReferrerUrl { get; set; }
    public Dictionary<string, object> CustomDimensions { get; set; } = new();
}

public class UserSessionDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string SessionToken { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? Location { get; set; }
    public string? UserAgent { get; set; }
    public bool IsActive { get; set; }
    public int EventCount { get; set; }
    public int ScreenViewCount { get; set; }
    public int InteractionCount { get; set; }
    public int ErrorCount { get; set; }
    public double Duration { get; set; }
    public double EngagementScore { get; set; }
    public Dictionary<string, object> SessionData { get; set; } = new();
}

public class PerformanceMetricDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string? ScreenName { get; set; }
    public string? OperationName { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
    public Dictionary<string, object> Context { get; set; } = new();
    public string PerformanceGrade { get; set; } = string.Empty;
}

public class CrashReportDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string CrashType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string? ScreenName { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsFatal { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public Dictionary<string, object> UserActions { get; set; } = new();
    public Dictionary<string, object> SystemInfo { get; set; } = new();
    public string SeverityLevel { get; set; } = string.Empty;
}

public class UserBehaviorPatternDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string PatternType { get; set; } = string.Empty;
    public string PatternName { get; set; } = string.Empty;
    public Dictionary<string, object> PatternData { get; set; } = new();
    public DateTime DetectedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    public int Frequency { get; set; }
    public double Confidence { get; set; }
    public string Platform { get; set; } = string.Empty;
    public bool IsHighConfidence { get; set; }
    public bool IsFrequentPattern { get; set; }
}

public class AnalyticsDashboardDto
{
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalEvents { get; set; }
    public int TotalSessions { get; set; }
    public int TotalCrashes { get; set; }
    public double AverageSessionDuration { get; set; }
    public double CrashRate { get; set; }
    public Dictionary<string, int> TopEvents { get; set; } = new();
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public Dictionary<DateTime, int> DailyActiveUsage { get; set; } = new();
}

public class UserBehaviorAnalysisDto
{
    public Guid UserId { get; set; }
    public DateTime AnalysisDate { get; set; }
    public int TotalSessions { get; set; }
    public double AverageSessionDuration { get; set; }
    public int TotalEvents { get; set; }
    public List<int> MostActiveHours { get; set; } = new();
    public Dictionary<string, int> TopScreens { get; set; } = new();
    public List<string> UserJourney { get; set; } = new();
    public double EngagementScore { get; set; }
    public Dictionary<string, object> RetentionIndicators { get; set; } = new();
}

public class AppPerformanceReportDto
{
    public string Platform { get; set; } = string.Empty;
    public string? AppVersion { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalSessions { get; set; }
    public int TotalCrashes { get; set; }
    public double CrashRate { get; set; }
    public double AverageSessionDuration { get; set; }
    public Dictionary<string, PerformanceMetricSummary> PerformanceMetrics { get; set; } = new();
    public Dictionary<string, int> TopCrashTypes { get; set; } = new();
    public Dictionary<DateTime, double> DailyMetrics { get; set; } = new();
}

public class PerformanceMetricSummary
{
    public double Average { get; set; }
    public double Min { get; set; }
    public double Max { get; set; }
    public int Count { get; set; }
}

public class RealTimeMetricDto
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

// Request DTOs
public class TrackEventRequest
{
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string? DeviceInfo { get; set; }
    public string? ScreenName { get; set; }
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
    public string? Location { get; set; }
    public double? Duration { get; set; }
    public Dictionary<string, object>? Properties { get; set; }
    public Dictionary<string, object>? Context { get; set; }
    public Dictionary<string, object>? CustomDimensions { get; set; }
}

public class TrackPerformanceRequest
{
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string? ScreenName { get; set; }
    public string? OperationName { get; set; }
    public Dictionary<string, object>? Tags { get; set; }
    public Dictionary<string, object>? Context { get; set; }
}

public class CrashReportRequest
{
    public Guid UserId { get; set; }
    public Guid? SessionId { get; set; }
    public string CrashType { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string? ScreenName { get; set; }
    public bool IsFatal { get; set; } = true;
    public Dictionary<string, object>? Context { get; set; }
    public Dictionary<string, object>? UserActions { get; set; }
    public Dictionary<string, object>? SystemInfo { get; set; }
}

public class StartSessionRequest
{
    public Guid UserId { get; set; }
    public string SessionToken { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string DeviceInfo { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? Location { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object>? SessionData { get; set; }
}

public class AnalyticsQueryRequest
{
    public Guid? UserId { get; set; }
    public string? Platform { get; set; }
    public string? AppVersion { get; set; }
    public string? EventType { get; set; }
    public string? MetricType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int? Limit { get; set; }
    public Dictionary<string, object>? Filters { get; set; }
}

public class AnalyticsExportRequest
{
    public Guid? UserId { get; set; }
    public string? Platform { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<string> DataTypes { get; set; } = new(); // Events, Performance, Crashes, Sessions
    public string Format { get; set; } = "json"; // json, csv, excel
    public bool IncludePersonalData { get; set; } = false;
}

public class AnalyticsAggregationDto
{
    public string Dimension { get; set; } = string.Empty;
    public string Metric { get; set; } = string.Empty;
    public Dictionary<string, double> Values { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Granularity { get; set; } = string.Empty; // hour, day, week, month
}
