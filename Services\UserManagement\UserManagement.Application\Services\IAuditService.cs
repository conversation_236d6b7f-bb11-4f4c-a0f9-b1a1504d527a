using UserManagement.Domain.Entities;
using UserManagement.Application.Models;

namespace UserManagement.Application.Services
{
    public interface IAuditService
    {
        Task LogUserApprovalAsync(Guid userId, string userName, Guid approvedBy, string approverName,
            string approverRole, string ipAddress, string userAgent, string? notes = null);

        Task LogUserRejectionAsync(Guid userId, string userName, Guid rejectedBy, string rejectorName,
            string rejectorRole, string reason, string ipAddress, string userAgent, string? notes = null);

        Task LogDocumentApprovalAsync(Guid submissionId, string documentType, Guid approvedBy,
            string approverName, string approverRole, string ipAddress, string userAgent, string? notes = null);

        Task LogDataExportAsync(string exportType, Guid requestedBy, string requestedByRole,
            string ipAddress, string userAgent, Dictionary<string, object>? additionalData = null);

        Task LogDocumentRejectionAsync(Guid submissionId, string documentType, Guid rejectedBy,
            string rejectorName, string rejectorRole, string reason, string ipAddress, string userAgent,
            bool requiresResubmission = true, string? notes = null);

        Task LogDocumentUploadAsync(Guid submissionId, string documentType, Guid userId,
            string userName, string fileName, string ipAddress, string userAgent);

        Task LogOcrProcessingAsync(Guid submissionId, string documentType, decimal confidenceScore,
            bool autoApproved, string systemUser = "System");

        Task LogBulkOperationAsync(string operationType, int itemCount, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            Dictionary<string, object>? additionalData = null);

        Task LogAccountActionAsync(Guid userId, string userName, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            string action, string actionResult, string reason, string notes);

        Task LogCustomActionAsync(AuditAction action, string entityType, Guid? entityId,
            Guid? userId, string userName, string userRole, string description,
            AuditSeverity severity = AuditSeverity.Medium, string? oldValues = null,
            string? newValues = null, string ipAddress = "", string userAgent = "",
            string? sessionId = null, string? correlationId = null,
            Dictionary<string, object>? additionalData = null);

        Task<IEnumerable<AuditLog>> GetAuditLogsAsync(AuditLogSearchCriteria criteria);
        Task<AuditLog?> GetAuditLogByIdAsync(Guid id);
        Task<int> GetAuditLogCountAsync(AuditLogSearchCriteria criteria);
        Task<IEnumerable<AuditLog>> GetRecentAuditLogsAsync(int count = 100);
        Task<IEnumerable<AuditLog>> GetCriticalEventsAsync(int pageNumber = 1, int pageSize = 50);
    }
}
