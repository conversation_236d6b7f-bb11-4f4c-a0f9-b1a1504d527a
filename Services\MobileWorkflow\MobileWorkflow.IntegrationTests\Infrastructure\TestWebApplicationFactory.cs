using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Infrastructure.Data;
using Testcontainers.PostgreSql;

namespace MobileWorkflow.IntegrationTests.Infrastructure;

public class TestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup>, IAsyncLifetime
    where TStartup : class
{
    private readonly PostgreSqlContainer _dbContainer;

    public TestWebApplicationFactory()
    {
        _dbContainer = new PostgreSqlBuilder()
            .WithImage("timescale/timescaledb:latest-pg15")
            .WithDatabase("mobileworkflow_test_db")
            .WithUsername("test_user")
            .WithPassword("test_password")
            .WithPortBinding(0, 5432)
            .Build();
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddJsonFile("appsettings.Test.json", optional: false);
        });

        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<MobileWorkflowDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database context
            services.AddDbContext<MobileWorkflowDbContext>(options =>
            {
                options.UseNpgsql(_dbContainer.GetConnectionString());
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Build service provider and ensure database is created
            var serviceProvider = services.BuildServiceProvider();
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<MobileWorkflowDbContext>();

            try
            {
                context.Database.EnsureCreated();
                SeedTestData(context);
            }
            catch (Exception ex)
            {
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<TestWebApplicationFactory<TStartup>>>();
                logger.LogError(ex, "An error occurred while creating the test database");
                throw;
            }
        });

        builder.UseEnvironment("Test");
    }

    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();
    }

    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
        await _dbContainer.DisposeAsync();
        await base.DisposeAsync();
    }

    private static void SeedTestData(MobileWorkflowDbContext context)
    {
        // Seed test mobile apps
        if (!context.MobileApps.Any())
        {
            var testMobileApps = new[]
            {
                new Domain.Entities.MobileApp(
                    "TLI Driver App Test",
                    "1.0.0",
                    "Android",
                    "com.tli.driver.test.android",
                    DateTime.UtcNow,
                    "8.0",
                    new Dictionary<string, object> { { "offline_mode", true }, { "gps_tracking", true } },
                    new Dictionary<string, object> { { "sync_interval", 300 } },
                    true,
                    "https://test.com/android-app",
                    25000000L,
                    "test-checksum-android"),

                new Domain.Entities.MobileApp(
                    "TLI Driver App Test",
                    "1.0.0",
                    "iOS",
                    "com.tli.driver.test.ios",
                    DateTime.UtcNow,
                    "14.0",
                    new Dictionary<string, object> { { "offline_mode", true }, { "biometric_auth", true } },
                    new Dictionary<string, object> { { "sync_interval", 300 } },
                    true,
                    "https://test.com/ios-app",
                    30000000L,
                    "test-checksum-ios")
            };

            context.MobileApps.AddRange(testMobileApps);
        }

        // Seed test workflows
        if (!context.Workflows.Any())
        {
            var testWorkflows = new[]
            {
                new Domain.Entities.Workflow(
                    "Test Driver Onboarding",
                    "Test workflow for driver onboarding",
                    "UserOnboarding",
                    "1.0",
                    new Dictionary<string, object>
                    {
                        { "steps", new List<object>
                            {
                                new Dictionary<string, object>
                                {
                                    { "id", "collect_docs" },
                                    { "name", "Collect Documents" },
                                    { "type", "Manual" },
                                    { "parameters", new Dictionary<string, object> { { "required_docs", new[] { "license", "aadhar" } } } }
                                },
                                new Dictionary<string, object>
                                {
                                    { "id", "verify_identity" },
                                    { "name", "Verify Identity" },
                                    { "type", "Automated" },
                                    { "parameters", new Dictionary<string, object> { { "verification_service", "test_api" } } }
                                }
                            }
                        }
                    },
                    new Dictionary<string, object> { { "timeout", "24:00:00" } },
                    "Manual",
                    new Dictionary<string, object>(),
                    "test-admin"),

                new Domain.Entities.Workflow(
                    "Test Trip Completion",
                    "Test workflow for trip completion",
                    "TripManagement",
                    "1.0",
                    new Dictionary<string, object>
                    {
                        { "steps", new List<object>
                            {
                                new Dictionary<string, object>
                                {
                                    { "id", "verify_pod" },
                                    { "name", "Verify POD" },
                                    { "type", "Automated" },
                                    { "parameters", new Dictionary<string, object> { { "validation_rules", new[] { "signature_present" } } } }
                                },
                                new Dictionary<string, object>
                                {
                                    { "id", "process_payment" },
                                    { "name", "Process Payment" },
                                    { "type", "Automated" },
                                    { "parameters", new Dictionary<string, object> { { "payment_type", "trip_completion" } } }
                                }
                            }
                        }
                    },
                    new Dictionary<string, object>(),
                    "Event",
                    new Dictionary<string, object> { { "trigger_event", "pod_uploaded" } },
                    "system")
            };

            context.Workflows.AddRange(testWorkflows);
        }

        // Seed milestone test data
        SeedMilestoneTestData(context);

        context.SaveChanges();
    }

    private static void SeedMilestoneTestData(MobileWorkflowDbContext context)
    {
        // Seed test milestone templates if they don't exist
        if (!context.MilestoneTemplates.Any())
        {
            var testTemplate = new Domain.Entities.MilestoneTemplate(
                "Test Integration Template",
                "Template for integration testing",
                "Test",
                "Integration",
                "test-admin");

            var step1 = testTemplate.AddStep("Test Step 1", "First test step", 1, true);
            var step2 = testTemplate.AddStep("Test Step 2", "Second test step", 2, false);

            step1.AddPayoutRule(70.0m, "status=step1_complete", "Step 1 payment");
            step2.AddPayoutRule(30.0m, "status=step2_complete", "Step 2 payment");

            context.MilestoneTemplates.Add(testTemplate);

            // Add role mapping for testing
            var roleMapping = new Domain.Entities.RoleTemplateMappings(
                "TestRole", testTemplate.Id, "test-admin", true, 100);
            context.RoleTemplateMappings.Add(roleMapping);
        }
    }

    public MobileWorkflowDbContext CreateDbContext()
    {
        var scope = Services.CreateScope();
        return scope.ServiceProvider.GetRequiredService<MobileWorkflowDbContext>();
    }

    public async Task<T> ExecuteDbContextAsync<T>(Func<MobileWorkflowDbContext, Task<T>> action)
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MobileWorkflowDbContext>();
        return await action(context);
    }

    public async Task ExecuteDbContextAsync(Func<MobileWorkflowDbContext, Task> action)
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MobileWorkflowDbContext>();
        await action(context);
    }
}
