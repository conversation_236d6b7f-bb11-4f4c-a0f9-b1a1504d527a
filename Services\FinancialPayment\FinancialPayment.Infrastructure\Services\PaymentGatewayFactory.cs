using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class PaymentGatewayFactory : IPaymentGatewayFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PaymentGatewayFactory> _logger;
    private readonly Dictionary<string, Type> _gateways;

    public PaymentGatewayFactory(IServiceProvider serviceProvider, ILogger<PaymentGatewayFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _gateways = new Dictionary<string, Type>(StringComparer.OrdinalIgnoreCase)
        {
            { "razorpay", typeof(RazorpayGateway) },
            { "stripe", typeof(StripeGateway) },
            { "paypal", typeof(PayPalGateway) },
            { "square", typeof(SquareGateway) }
        };
    }

    public IPaymentGateway GetGateway(string gatewayName)
    {
        if (string.IsNullOrWhiteSpace(gatewayName))
        {
            _logger.LogWarning("Gateway name is null or empty, returning default gateway");
            return GetDefaultGateway();
        }

        if (!_gateways.TryGetValue(gatewayName, out var gatewayType))
        {
            _logger.LogWarning("Gateway {GatewayName} not found, returning default gateway", gatewayName);
            return GetDefaultGateway();
        }

        try
        {
            var gateway = _serviceProvider.GetService(gatewayType) as IPaymentGateway;
            if (gateway == null)
            {
                _logger.LogError("Failed to resolve gateway {GatewayName}, returning default gateway", gatewayName);
                return GetDefaultGateway();
            }

            return gateway;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating gateway {GatewayName}, returning default gateway", gatewayName);
            return GetDefaultGateway();
        }
    }

    public IPaymentGateway GetDefaultGateway()
    {
        // Razorpay is the default gateway
        return _serviceProvider.GetRequiredService<RazorpayGateway>();
    }

    public IEnumerable<string> GetAvailableGateways()
    {
        return _gateways.Keys;
    }
}
