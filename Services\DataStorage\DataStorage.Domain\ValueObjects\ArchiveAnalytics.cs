using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ArchiveCandidate
{
    public Guid DocumentId { get; private set; }
    public string FileName { get; private set; }
    public long SizeBytes { get; private set; }
    public DateTime LastAccessedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public DocumentCategory Category { get; private set; }
    public ArchiveCandidateReason Reason { get; private set; }
    public double ArchiveScore { get; private set; }
    public StorageTierType RecommendedTier { get; private set; }
    public long EstimatedSavings { get; private set; }

    public ArchiveCandidate(
        Guid documentId,
        string fileName,
        long sizeBytes,
        DateTime lastAccessedAt,
        DateTime createdAt,
        DocumentType documentType,
        DocumentCategory category,
        ArchiveCandidateReason reason,
        double archiveScore,
        StorageTierType recommendedTier,
        long estimatedSavings)
    {
        DocumentId = documentId;
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        SizeBytes = sizeBytes;
        LastAccessedAt = lastAccessedAt;
        CreatedAt = createdAt;
        DocumentType = documentType;
        Category = category;
        Reason = reason;
        ArchiveScore = Math.Max(0, Math.Min(100, archiveScore));
        RecommendedTier = recommendedTier;
        EstimatedSavings = estimatedSavings;
    }
}

public class BatchArchiveResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid PolicyId { get; private set; }
    public int TotalCandidates { get; private set; }
    public int SuccessfulArchives { get; private set; }
    public int FailedArchives { get; private set; }
    public List<ArchiveResult> Results { get; private set; }
    public TimeSpan TotalProcessingTime { get; private set; }
    public long TotalSizeArchived { get; private set; }
    public long TotalSpaceSaved { get; private set; }

    private BatchArchiveResult()
    {
        Results = new List<ArchiveResult>();
    }

    public static BatchArchiveResult Success(
        Guid policyId,
        List<ArchiveResult> results,
        TimeSpan totalProcessingTime)
    {
        var successfulResults = results.Where(r => r.IsSuccessful).ToList();
        var failedResults = results.Where(r => !r.IsSuccessful).ToList();

        return new BatchArchiveResult
        {
            IsSuccessful = true,
            PolicyId = policyId,
            TotalCandidates = results.Count,
            SuccessfulArchives = successfulResults.Count,
            FailedArchives = failedResults.Count,
            Results = results,
            TotalProcessingTime = totalProcessingTime,
            TotalSizeArchived = successfulResults.Sum(r => r.OriginalSizeBytes),
            TotalSpaceSaved = successfulResults.Sum(r => r.OriginalSizeBytes - r.CompressedSizeBytes)
        };
    }

    public static BatchArchiveResult Failure(Guid policyId, string errorMessage)
    {
        return new BatchArchiveResult
        {
            IsSuccessful = false,
            PolicyId = policyId,
            ErrorMessage = errorMessage,
            Results = new List<ArchiveResult>()
        };
    }
}

public class ArchiveSchedule
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public Guid PolicyId { get; private set; }
    public ScheduleType Type { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime NextRunTime { get; private set; }
    public DateTime? LastRunTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public ScheduleStatistics Statistics { get; private set; }

    public ArchiveSchedule(
        Guid id,
        string name,
        Guid policyId,
        ScheduleType type,
        string cronExpression,
        DateTime nextRunTime,
        bool isEnabled,
        Guid createdBy,
        DateTime createdAt,
        DateTime? lastRunTime = null,
        ScheduleStatistics? statistics = null)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        PolicyId = policyId;
        Type = type;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        NextRunTime = nextRunTime;
        LastRunTime = lastRunTime;
        IsEnabled = isEnabled;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        Statistics = statistics ?? new ScheduleStatistics();
    }
}

public class ArchiveScheduleRequest
{
    public string Name { get; private set; }
    public Guid PolicyId { get; private set; }
    public ScheduleType Type { get; private set; }
    public string CronExpression { get; private set; }
    public DateTime StartTime { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }

    public ArchiveScheduleRequest(
        string name,
        Guid policyId,
        ScheduleType type,
        string cronExpression,
        DateTime startTime,
        Guid createdBy,
        bool isEnabled = true)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        PolicyId = policyId;
        Type = type;
        CronExpression = cronExpression ?? throw new ArgumentNullException(nameof(cronExpression));
        StartTime = startTime;
        CreatedBy = createdBy;
        IsEnabled = isEnabled;
    }
}

public class ScheduleStatistics
{
    public int TotalRuns { get; private set; }
    public int SuccessfulRuns { get; private set; }
    public int FailedRuns { get; private set; }
    public TimeSpan AverageRunTime { get; private set; }
    public long TotalDocumentsArchived { get; private set; }
    public long TotalSpaceSaved { get; private set; }

    public ScheduleStatistics(
        int totalRuns = 0,
        int successfulRuns = 0,
        int failedRuns = 0,
        TimeSpan averageRunTime = default,
        long totalDocumentsArchived = 0,
        long totalSpaceSaved = 0)
    {
        TotalRuns = totalRuns;
        SuccessfulRuns = successfulRuns;
        FailedRuns = failedRuns;
        AverageRunTime = averageRunTime;
        TotalDocumentsArchived = totalDocumentsArchived;
        TotalSpaceSaved = totalSpaceSaved;
    }
}

public class StorageTier
{
    public StorageTierType Type { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public decimal CostPerGBPerMonth { get; private set; }
    public TimeSpan RetrievalTime { get; private set; }
    public decimal RetrievalCostPerGB { get; private set; }
    public bool SupportsCompression { get; private set; }
    public bool SupportsEncryption { get; private set; }
    public long MaxFileSizeBytes { get; private set; }
    public TierAvailability Availability { get; private set; }

    public StorageTier(
        StorageTierType type,
        string name,
        string description,
        decimal costPerGBPerMonth,
        TimeSpan retrievalTime,
        decimal retrievalCostPerGB,
        bool supportsCompression,
        bool supportsEncryption,
        long maxFileSizeBytes,
        TierAvailability availability)
    {
        Type = type;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        CostPerGBPerMonth = costPerGBPerMonth;
        RetrievalTime = retrievalTime;
        RetrievalCostPerGB = retrievalCostPerGB;
        SupportsCompression = supportsCompression;
        SupportsEncryption = supportsEncryption;
        MaxFileSizeBytes = maxFileSizeBytes;
        Availability = availability;
    }
}

public class TierMigrationResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid DocumentId { get; private set; }
    public StorageTierType SourceTier { get; private set; }
    public StorageTierType TargetTier { get; private set; }
    public DateTime MigratedAt { get; private set; }
    public TimeSpan MigrationTime { get; private set; }
    public long SizeBytes { get; private set; }
    public decimal CostSavings { get; private set; }

    private TierMigrationResult()
    {
    }

    public static TierMigrationResult Success(
        Guid documentId,
        StorageTierType sourceTier,
        StorageTierType targetTier,
        TimeSpan migrationTime,
        long sizeBytes,
        decimal costSavings)
    {
        return new TierMigrationResult
        {
            IsSuccessful = true,
            DocumentId = documentId,
            SourceTier = sourceTier,
            TargetTier = targetTier,
            MigratedAt = DateTime.UtcNow,
            MigrationTime = migrationTime,
            SizeBytes = sizeBytes,
            CostSavings = costSavings
        };
    }

    public static TierMigrationResult Failure(Guid documentId, string errorMessage)
    {
        return new TierMigrationResult
        {
            IsSuccessful = false,
            DocumentId = documentId,
            ErrorMessage = errorMessage,
            MigratedAt = DateTime.UtcNow
        };
    }
}

public class TierMigrationJob
{
    public Guid JobId { get; private set; }
    public Guid DocumentId { get; private set; }
    public StorageTierType SourceTier { get; private set; }
    public StorageTierType TargetTier { get; private set; }
    public TierMigrationStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public double ProgressPercentage { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid RequestedBy { get; private set; }

    public TierMigrationJob(
        Guid jobId,
        Guid documentId,
        StorageTierType sourceTier,
        StorageTierType targetTier,
        TierMigrationStatus status,
        DateTime createdAt,
        Guid requestedBy,
        DateTime? startedAt = null,
        DateTime? completedAt = null,
        double progressPercentage = 0,
        string? errorMessage = null)
    {
        JobId = jobId;
        DocumentId = documentId;
        SourceTier = sourceTier;
        TargetTier = targetTier;
        Status = status;
        CreatedAt = createdAt;
        StartedAt = startedAt;
        CompletedAt = completedAt;
        ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
        ErrorMessage = errorMessage;
        RequestedBy = requestedBy;
    }
}
