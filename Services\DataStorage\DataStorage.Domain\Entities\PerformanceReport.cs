using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public enum ReportType
    {
        Daily,
        Weekly,
        Monthly,
        Quarterly,
        Custom
    }

    public enum ReportStatus
    {
        Pending,
        Generating,
        Completed,
        Failed
    }

    public class PerformanceReport : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public ReportType Type { get; private set; }
        public ReportStatus Status { get; private set; }
        public DateTime StartDate { get; private set; }
        public DateTime EndDate { get; private set; }
        public string ReportData { get; private set; } // JSON data
        public string FilePath { get; private set; }
        public long FileSizeBytes { get; private set; }
        public Guid GeneratedBy { get; private set; }
        public DateTime? CompletedAt { get; private set; }
        public string ErrorMessage { get; private set; }
        public Dictionary<string, object> Metrics { get; private set; }
        public List<string> IncludedSections { get; private set; }

        private PerformanceReport()
        {
            Metrics = new Dictionary<string, object>();
            IncludedSections = new List<string>();
        }

        public PerformanceReport(
            string name,
            string description,
            ReportType type,
            DateTime startDate,
            DateTime endDate,
            Guid generatedBy,
            List<string> includedSections = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            Type = type;
            StartDate = startDate;
            EndDate = endDate;
            GeneratedBy = generatedBy;
            Status = ReportStatus.Pending;
            Metrics = new Dictionary<string, object>();
            IncludedSections = includedSections ?? new List<string>();
            CreatedAt = DateTime.UtcNow;
        }

        public void StartGeneration()
        {
            Status = ReportStatus.Generating;
            UpdatedAt = DateTime.UtcNow;
        }

        public void CompleteGeneration(string reportData, string filePath, long fileSizeBytes)
        {
            Status = ReportStatus.Completed;
            ReportData = reportData;
            FilePath = filePath;
            FileSizeBytes = fileSizeBytes;
            CompletedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void FailGeneration(string errorMessage)
        {
            Status = ReportStatus.Failed;
            ErrorMessage = errorMessage;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddMetric(string key, object value)
        {
            Metrics[key] = value;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddSection(string section)
        {
            if (!string.IsNullOrWhiteSpace(section) && !IncludedSections.Contains(section))
            {
                IncludedSections.Add(section);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public bool IsExpired(int retentionDays = 30)
        {
            return CreatedAt.AddDays(retentionDays) < DateTime.UtcNow;
        }
    }
}
