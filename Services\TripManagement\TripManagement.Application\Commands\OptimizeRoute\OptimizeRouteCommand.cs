using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.OptimizeRoute;

public record OptimizeRouteCommand : IRequest<RouteOptimizationResult>
{
    public Guid TripId { get; init; }
    public List<LocationDto> Waypoints { get; init; } = new();
    public OptimizationCriteria Criteria { get; init; } = OptimizationCriteria.Distance;
    public bool AvoidTolls { get; init; }
    public bool AvoidHighways { get; init; }
    public VehicleConstraints? VehicleConstraints { get; init; }
}

public record RouteOptimizationResult
{
    public RouteDto OptimizedRoute { get; init; } = null!;
    public List<LocationDto> OptimizedWaypoints { get; init; } = new();
    public double TotalDistanceKm { get; init; }
    public TimeSpan EstimatedDuration { get; init; }
    public decimal EstimatedFuelCost { get; init; }
    public decimal EstimatedTollCost { get; init; }
    public string OptimizationNotes { get; init; } = string.Empty;
}

public record VehicleConstraints
{
    public double MaxWeightKg { get; init; }
    public double MaxHeightM { get; init; }
    public double MaxWidthM { get; init; }
    public double MaxLengthM { get; init; }
    public bool IsHazardous { get; init; }
    public List<string> RestrictedAreas { get; init; } = new();
}

public enum OptimizationCriteria
{
    Distance = 0,
    Time = 1,
    Fuel = 2,
    Cost = 3,
    Balanced = 4
}
