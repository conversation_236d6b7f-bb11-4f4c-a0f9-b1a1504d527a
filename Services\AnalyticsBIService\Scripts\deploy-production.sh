#!/bin/bash

# AnalyticsBIService Production Deployment Script
# This script deploys the AnalyticsBIService to production environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.prod.yml"
ENV_FILE="$PROJECT_DIR/.env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file not found at $ENV_FILE"
        log_info "Please copy .env.template to .env and configure it with your production values."
        exit 1
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose file not found at $COMPOSE_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

validate_environment() {
    log_info "Validating environment configuration..."
    
    # Source the environment file
    source "$ENV_FILE"
    
    # Check required environment variables
    required_vars=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "JWT_SECRET_KEY"
        "GRAFANA_ADMIN_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Check JWT secret key length (should be at least 32 characters)
    if [ ${#JWT_SECRET_KEY} -lt 32 ]; then
        log_error "JWT_SECRET_KEY must be at least 32 characters long"
        exit 1
    fi
    
    log_success "Environment validation passed"
}

backup_existing_data() {
    log_info "Creating backup of existing data..."
    
    # Create backup directory with timestamp
    BACKUP_DIR="$PROJECT_DIR/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if container exists
    if docker ps -a --format "table {{.Names}}" | grep -q "tli-analytics-timescaledb-prod"; then
        log_info "Backing up database..."
        docker exec tli-analytics-timescaledb-prod pg_dump -U timescale TLI_AnalyticsBI > "$BACKUP_DIR/database_backup.sql" || log_warning "Database backup failed"
    fi
    
    # Backup volumes
    if docker volume ls --format "table {{.Name}}" | grep -q "timescaledb_data"; then
        log_info "Backing up database volume..."
        docker run --rm -v timescaledb_data:/data -v "$BACKUP_DIR":/backup alpine tar czf /backup/timescaledb_data.tar.gz -C /data . || log_warning "Volume backup failed"
    fi
    
    log_success "Backup completed in $BACKUP_DIR"
}

build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_DIR"
    
    # Build the application image
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" build --no-cache
    else
        docker compose -f "$COMPOSE_FILE" build --no-cache
    fi
    
    log_success "Docker images built successfully"
}

deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing services
    log_info "Stopping existing services..."
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" down || true
    else
        docker compose -f "$COMPOSE_FILE" down || true
    fi
    
    # Start infrastructure services first
    log_info "Starting infrastructure services..."
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" up -d timescaledb redis
    else
        docker compose -f "$COMPOSE_FILE" up -d timescaledb redis
    fi
    
    # Wait for infrastructure services to be ready
    log_info "Waiting for infrastructure services to be ready..."
    sleep 30
    
    # Start application services
    log_info "Starting application services..."
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        docker compose -f "$COMPOSE_FILE" up -d
    fi
    
    log_success "Services deployed successfully"
}

verify_deployment() {
    log_info "Verifying deployment..."
    
    # Wait for services to start
    sleep 60
    
    # Check service health
    services=("tli-analytics-api-prod" "tli-analytics-timescaledb-prod" "tli-analytics-redis-prod")
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$service" | grep -q "Up"; then
            log_success "$service is running"
        else
            log_error "$service is not running"
            docker logs "$service" --tail 20
            exit 1
        fi
    done
    
    # Test API health endpoint
    log_info "Testing API health endpoint..."
    API_PORT=${API_PORT:-5014}
    
    for i in {1..10}; do
        if curl -f "http://localhost:$API_PORT/health" &> /dev/null; then
            log_success "API health check passed"
            break
        else
            if [ $i -eq 10 ]; then
                log_error "API health check failed after 10 attempts"
                exit 1
            fi
            log_info "Waiting for API to be ready... (attempt $i/10)"
            sleep 10
        fi
    done
    
    log_success "Deployment verification completed"
}

show_deployment_info() {
    log_info "Deployment Information:"
    echo "=========================="
    echo "API URL: http://localhost:${API_PORT:-5014}"
    echo "Grafana URL: http://localhost:${GRAFANA_PORT:-3000}"
    echo "Prometheus URL: http://localhost:${PROMETHEUS_PORT:-9090}"
    echo "=========================="
    echo ""
    log_info "To view logs: docker logs tli-analytics-api-prod"
    log_info "To stop services: docker-compose -f docker-compose.prod.yml down"
    log_info "To view service status: docker-compose -f docker-compose.prod.yml ps"
}

# Main execution
main() {
    log_info "Starting AnalyticsBIService production deployment..."
    
    check_prerequisites
    validate_environment
    
    # Ask for confirmation
    read -p "Are you sure you want to deploy to production? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled"
        exit 0
    fi
    
    backup_existing_data
    build_images
    deploy_services
    verify_deployment
    show_deployment_info
    
    log_success "AnalyticsBIService production deployment completed successfully!"
}

# Run main function
main "$@"
