using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class MilestoneTemplateRepository : IMilestoneTemplateRepository
{
    private readonly OrderManagementDbContext _context;

    public MilestoneTemplateRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<MilestoneTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Include(mt => mt.RfqAssignments)
            .FirstOrDefaultAsync(mt => mt.Id == id, cancellationToken);
    }

    public async Task<List<MilestoneTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Where(mt => mt.IsActive)
            .OrderBy(mt => mt.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<MilestoneTemplate>> GetByTypeAsync(MilestoneTemplateType templateType, bool activeOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.MilestoneTemplates
            .Include(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Where(mt => mt.TemplateType == templateType);

        if (activeOnly)
        {
            query = query.Where(mt => mt.IsActive);
        }

        return await query
            .OrderBy(mt => mt.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestoneTemplate?> GetDefaultTemplateAsync(MilestoneTemplateType templateType, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(mt => mt.Steps.OrderBy(s => s.StepOrder))
            .Where(mt => mt.TemplateType == templateType && mt.IsDefault && mt.IsActive)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<(List<MilestoneTemplate> Templates, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        MilestoneTemplateType? templateType = null,
        bool? isActive = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.MilestoneTemplates
            .Include(mt => mt.Steps)
            .AsQueryable();

        if (templateType.HasValue)
        {
            query = query.Where(mt => mt.TemplateType == templateType.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(mt => mt.IsActive == isActive.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            query = query.Where(mt => 
                mt.Name.ToLower().Contains(searchTermLower) ||
                mt.Description.ToLower().Contains(searchTermLower) ||
                (mt.Tags != null && mt.Tags.ToLower().Contains(searchTermLower)));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var templates = await query
            .OrderBy(mt => mt.Name)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (templates, totalCount);
    }

    public async Task AddAsync(MilestoneTemplate template, CancellationToken cancellationToken = default)
    {
        await _context.MilestoneTemplates.AddAsync(template, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(MilestoneTemplate template)
    {
        _context.MilestoneTemplates.Update(template);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var template = await GetByIdAsync(id, cancellationToken);
        if (template != null)
        {
            // Check if template can be deleted
            if (!template.CanBeDeleted())
            {
                throw new InvalidOperationException("Cannot delete milestone template that is actively assigned to RFQs");
            }

            _context.MilestoneTemplates.Remove(template);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
