using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Services
{
    /// <summary>
    /// Export validation service implementation
    /// </summary>
    public class ExportValidationService : IExportValidationService
    {
        private readonly ILogger<ExportValidationService> _logger;
        private readonly IModuleRegistryService _moduleRegistryService;

        public ExportValidationService(
            ILogger<ExportValidationService> logger,
            IModuleRegistryService moduleRegistryService)
        {
            _logger = logger;
            _moduleRegistryService = moduleRegistryService;
        }

        public async Task<List<ExportValidationDto>> ValidateExportRequestAsync(EnhancedAuditExportRequestDto request)
        {
            var validationResults = new List<ExportValidationDto>();

            try
            {
                // Validate basic request parameters
                await ValidateBasicParametersAsync(request, validationResults);

                // Validate date ranges
                await ValidateDateRangesAsync(request, validationResults);

                // Validate module access
                await ValidateModuleAccessAsync(request, validationResults);

                // Validate export format and columns
                await ValidateFormatAndColumnsAsync(request, validationResults);

                // Validate record limits
                await ValidateRecordLimitsAsync(request, validationResults);

                // Validate permissions
                await ValidatePermissionsAsync(request, validationResults);

                _logger.LogInformation("Export request validation completed with {ErrorCount} errors and {WarningCount} warnings",
                    validationResults.Count(v => v.Type == "Error"),
                    validationResults.Count(v => v.Type == "Warning"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during export request validation");
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Validation process failed",
                    Code = "VALIDATION_ERROR",
                    Field = null
                });
            }

            return validationResults;
        }

        public async Task<List<ExportValidationDto>> ValidateBulkExportRequestAsync(BulkAuditExportRequestDto request)
        {
            var validationResults = new List<ExportValidationDto>();

            try
            {
                // Validate bulk request limits
                if (request.ExportRequests.Count > 10)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Error",
                        Message = "Bulk export cannot contain more than 10 individual exports",
                        Code = "BULK_LIMIT_EXCEEDED",
                        Field = "ExportRequests"
                    });
                }

                // Validate each individual export request
                for (int i = 0; i < request.ExportRequests.Count; i++)
                {
                    var individualValidation = await ValidateExportRequestAsync(request.ExportRequests[i]);
                    foreach (var validation in individualValidation)
                    {
                        validation.Field = $"ExportRequests[{i}].{validation.Field}";
                        validationResults.Add(validation);
                    }
                }

                // Validate global settings
                if (request.ProcessInParallel && request.ExportRequests.Count > 5)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Warning",
                        Message = "Parallel processing with more than 5 exports may impact system performance",
                        Code = "PERFORMANCE_WARNING",
                        Field = "ProcessInParallel"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bulk export request validation");
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Bulk validation process failed",
                    Code = "BULK_VALIDATION_ERROR"
                });
            }

            return validationResults;
        }

        public async Task<bool> HasExportPermissionAsync(Guid userId, string userRole, EnhancedAuditExportRequestDto request)
        {
            try
            {
                // Check basic export permissions
                var allowedRoles = new[] { "Admin", "Auditor", "ComplianceOfficer" };
                if (!allowedRoles.Contains(userRole))
                {
                    return false;
                }

                // Check sensitive data access
                if (request.IncludeSensitiveData && userRole != "Admin")
                {
                    return false;
                }

                // Check module access
                foreach (var moduleName in request.ModuleNames)
                {
                    var hasAccess = await _moduleRegistryService.HasModuleAccessAsync(userId, moduleName);
                    if (!hasAccess)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking export permissions for user {UserId}", userId);
                return false;
            }
        }

        public async Task<List<ExportValidationDto>> ValidateFormatConfigurationAsync(ExportFormat format, List<string> selectedColumns)
        {
            var validationResults = new List<ExportValidationDto>();

            try
            {
                // Validate format-specific constraints
                switch (format)
                {
                    case ExportFormat.CSV:
                        await ValidateCsvFormatAsync(selectedColumns, validationResults);
                        break;
                    case ExportFormat.Excel:
                        await ValidateExcelFormatAsync(selectedColumns, validationResults);
                        break;
                    case ExportFormat.PDF:
                        await ValidatePdfFormatAsync(selectedColumns, validationResults);
                        break;
                    case ExportFormat.JSON:
                        await ValidateJsonFormatAsync(selectedColumns, validationResults);
                        break;
                    case ExportFormat.XML:
                        await ValidateXmlFormatAsync(selectedColumns, validationResults);
                        break;
                }

                // Validate column selection
                if (selectedColumns.Any() && selectedColumns.Count > 50)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Warning",
                        Message = "Large number of columns may impact export performance",
                        Code = "COLUMN_COUNT_WARNING",
                        Field = "SelectedColumns"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating format configuration");
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Format validation failed",
                    Code = "FORMAT_VALIDATION_ERROR"
                });
            }

            return validationResults;
        }

        private async Task ValidateBasicParametersAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            if (string.IsNullOrEmpty(request.RequestedByRole))
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Requested by role is required",
                    Code = "MISSING_ROLE",
                    Field = "RequestedByRole"
                });
            }

            if (request.RequestedBy == Guid.Empty)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Requested by user ID is required",
                    Code = "MISSING_USER_ID",
                    Field = "RequestedBy"
                });
            }
        }

        private async Task ValidateDateRangesAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            if (request.FromDate.HasValue && request.ToDate.HasValue)
            {
                if (request.FromDate.Value > request.ToDate.Value)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Error",
                        Message = "From date cannot be later than to date",
                        Code = "INVALID_DATE_RANGE",
                        Field = "FromDate"
                    });
                }

                var dateRange = request.ToDate.Value - request.FromDate.Value;
                if (dateRange.TotalDays > 365)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Warning",
                        Message = "Date range exceeds one year, export may be very large",
                        Code = "LARGE_DATE_RANGE",
                        Field = "DateRange"
                    });
                }
            }
        }

        private async Task ValidateModuleAccessAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            foreach (var moduleName in request.ModuleNames)
            {
                var module = await _moduleRegistryService.GetModuleByNameAsync(moduleName);
                if (module == null)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Error",
                        Message = $"Module '{moduleName}' not found",
                        Code = "MODULE_NOT_FOUND",
                        Field = "ModuleNames"
                    });
                }
                else if (!module.IsActive)
                {
                    validationResults.Add(new ExportValidationDto
                    {
                        Type = "Warning",
                        Message = $"Module '{moduleName}' is not active",
                        Code = "MODULE_INACTIVE",
                        Field = "ModuleNames"
                    });
                }
            }
        }

        private async Task ValidateFormatAndColumnsAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            var formatValidation = await ValidateFormatConfigurationAsync(request.Format, request.SelectedColumns);
            validationResults.AddRange(formatValidation);
        }

        private async Task ValidateRecordLimitsAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            if (request.MaxRecords.HasValue && request.MaxRecords.Value > 1000000)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Warning",
                    Message = "Maximum records limit is very high, consider using background processing",
                    Code = "HIGH_RECORD_LIMIT",
                    Field = "MaxRecords"
                });
            }

            if (request.PageSize > 50000)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Warning",
                    Message = "Page size is very large, may impact memory usage",
                    Code = "LARGE_PAGE_SIZE",
                    Field = "PageSize"
                });
            }
        }

        private async Task ValidatePermissionsAsync(EnhancedAuditExportRequestDto request, List<ExportValidationDto> validationResults)
        {
            var hasPermission = await HasExportPermissionAsync(request.RequestedBy, request.RequestedByRole, request);
            if (!hasPermission)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Insufficient permissions for this export request",
                    Code = "INSUFFICIENT_PERMISSIONS",
                    Field = "RequestedBy"
                });
            }
        }

        private async Task ValidateCsvFormatAsync(List<string> selectedColumns, List<ExportValidationDto> validationResults)
        {
            // CSV-specific validations
            await Task.CompletedTask;
        }

        private async Task ValidateExcelFormatAsync(List<string> selectedColumns, List<ExportValidationDto> validationResults)
        {
            if (selectedColumns.Count > 256)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Error",
                    Message = "Excel format supports maximum 256 columns",
                    Code = "EXCEL_COLUMN_LIMIT",
                    Field = "SelectedColumns"
                });
            }
        }

        private async Task ValidatePdfFormatAsync(List<string> selectedColumns, List<ExportValidationDto> validationResults)
        {
            if (selectedColumns.Count > 20)
            {
                validationResults.Add(new ExportValidationDto
                {
                    Type = "Warning",
                    Message = "PDF format with many columns may not display well",
                    Code = "PDF_COLUMN_WARNING",
                    Field = "SelectedColumns"
                });
            }
        }

        private async Task ValidateJsonFormatAsync(List<string> selectedColumns, List<ExportValidationDto> validationResults)
        {
            // JSON-specific validations
            await Task.CompletedTask;
        }

        private async Task ValidateXmlFormatAsync(List<string> selectedColumns, List<ExportValidationDto> validationResults)
        {
            // XML-specific validations
            await Task.CompletedTask;
        }
    }
}
