﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;

namespace DataStorage.Domain.ValueObjects;

public class FileMetadata : ValueObject
{
    public string FileName { get; private set; }
    public string ContentType { get; private set; }
    public long FileSizeBytes { get; private set; }
    public string FileExtension { get; private set; }
    public string? Checksum { get; private set; }
    public Dictionary<string, string> CustomProperties { get; private set; }

    private FileMetadata()
    {
        FileName = string.Empty;
        ContentType = string.Empty;
        FileExtension = string.Empty;
        CustomProperties = new Dictionary<string, string>();
    }

    public FileMetadata(
        string fileName,
        string contentType,
        long fileSizeBytes,
        string? checksum = null,
        Dictionary<string, string>? customProperties = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(contentType))
            throw new ArgumentException("Content type cannot be empty", nameof(contentType));

        if (fileSizeBytes < 0)
            throw new ArgumentException("File size cannot be negative", nameof(fileSizeBytes));

        FileName = fileName;
        ContentType = contentType;
        FileSizeBytes = fileSizeBytes;
        FileExtension = Path.GetExtension(fileName).ToLowerInvariant();
        Checksum = checksum;
        CustomProperties = customProperties ?? new Dictionary<string, string>();
    }

    public static FileMetadata Create(string fileName, string contentType, long fileSizeBytes, string? checksum = null)
    {
        return new FileMetadata(fileName, contentType, fileSizeBytes, checksum);
    }

    public FileMetadata WithChecksum(string checksum)
    {
        return new FileMetadata(FileName, ContentType, FileSizeBytes, checksum, CustomProperties);
    }

    public FileMetadata WithCustomProperty(string key, string value)
    {
        var newProperties = new Dictionary<string, string>(CustomProperties)
        {
            [key] = value
        };
        return new FileMetadata(FileName, ContentType, FileSizeBytes, Checksum, newProperties);
    }

    public bool IsImage()
    {
        return ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsPdf()
    {
        return ContentType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase);
    }

    public bool IsDocument()
    {
        var documentTypes = new[]
        {
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/plain",
            "text/csv"
        };

        return documentTypes.Contains(ContentType, StringComparer.OrdinalIgnoreCase);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FileName;
        yield return ContentType;
        yield return FileSizeBytes;
        yield return FileExtension;
        yield return Checksum ?? string.Empty;

        foreach (var kvp in CustomProperties.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

