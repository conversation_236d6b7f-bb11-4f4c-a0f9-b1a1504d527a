using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class EscrowAccountRepository : IEscrowAccountRepository
{
    private readonly FinancialPaymentDbContext _context;

    public EscrowAccountRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<EscrowAccount?> GetByIdAsync(Guid id)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .FirstOrDefaultAsync(e => e.Id == id);
    }

    public async Task<EscrowAccount?> GetByOrderIdAsync(Guid orderId)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .FirstOrDefaultAsync(e => e.OrderId == orderId);
    }

    public async Task<EscrowAccount?> GetByEscrowAccountNumberAsync(string escrowAccountNumber)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .FirstOrDefaultAsync(e => e.EscrowAccountNumber == escrowAccountNumber);
    }

    public async Task<List<EscrowAccount>> GetByTransportCompanyIdAsync(Guid transportCompanyId)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.TransportCompanyId == transportCompanyId)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<EscrowAccount>> GetByBrokerIdAsync(Guid brokerId)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.BrokerId == brokerId)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<EscrowAccount>> GetByCarrierIdAsync(Guid carrierId)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.CarrierId == carrierId)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<EscrowAccount>> GetByStatusAsync(EscrowStatus status)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.Status == status)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<EscrowAccount>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.CreatedAt >= from && e.CreatedAt <= to)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();
    }

    public async Task<EscrowAccount> AddAsync(EscrowAccount escrowAccount)
    {
        _context.EscrowAccounts.Add(escrowAccount);
        return escrowAccount;
    }

    public async Task UpdateAsync(EscrowAccount escrowAccount)
    {
        _context.EscrowAccounts.Update(escrowAccount);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var escrowAccount = await _context.EscrowAccounts.FindAsync(id);
        if (escrowAccount != null)
        {
            _context.EscrowAccounts.Remove(escrowAccount);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.EscrowAccounts.AnyAsync(e => e.Id == id);
    }

    public async Task<decimal> GetTotalEscrowAmountAsync(Guid participantId)
    {
        var totalAsTransportCompany = await _context.EscrowAccounts
            .Where(e => e.TransportCompanyId == participantId && 
                       (e.Status == EscrowStatus.Funded || e.Status == EscrowStatus.PartiallyReleased))
            .SumAsync(e => e.AvailableAmount.Amount + e.ReservedAmount.Amount);

        var totalAsBroker = await _context.EscrowAccounts
            .Where(e => e.BrokerId == participantId && 
                       (e.Status == EscrowStatus.Funded || e.Status == EscrowStatus.PartiallyReleased))
            .SumAsync(e => e.AvailableAmount.Amount + e.ReservedAmount.Amount);

        var totalAsCarrier = await _context.EscrowAccounts
            .Where(e => e.CarrierId == participantId && 
                       (e.Status == EscrowStatus.Funded || e.Status == EscrowStatus.PartiallyReleased))
            .SumAsync(e => e.AvailableAmount.Amount + e.ReservedAmount.Amount);

        return totalAsTransportCompany + totalAsBroker + totalAsCarrier;
    }

    public async Task<List<EscrowAccount>> GetOverdueEscrowAccountsAsync()
    {
        var overdueDate = DateTime.UtcNow.AddDays(-30); // Consider accounts overdue after 30 days

        return await _context.EscrowAccounts
            .Include(e => e.Transactions)
            .Include(e => e.Milestones)
            .Where(e => e.Status == EscrowStatus.Funded && e.FundedAt < overdueDate)
            .OrderBy(e => e.FundedAt)
            .ToListAsync();
    }
}
