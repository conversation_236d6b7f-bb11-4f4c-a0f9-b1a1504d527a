using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetBrokerRatings;

public class GetBrokerRatingsQueryHandler : IRequestHandler<GetBrokerRatingsQuery, List<BrokerRatingDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly INetworkFleetService _networkFleetService;
    private readonly ILogger<GetBrokerRatingsQueryHandler> _logger;

    public GetBrokerRatingsQueryHandler(
        IRfqRepository rfqRepository,
        INetworkFleetService networkFleetService,
        ILogger<GetBrokerRatingsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _networkFleetService = networkFleetService;
        _logger = logger;
    }

    public async Task<List<BrokerRatingDto>> Handle(GetBrokerRatingsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting broker ratings for RFQ {RfqId} by user {UserId}",
            request.RfqId, request.RequestingUserId);

        try
        {
            // Get the RFQ to validate access and get preferred partners
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new ArgumentException($"RFQ {request.RfqId} not found");
            }

            // Validate user has permission to view this RFQ
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view RFQ {RfqId}",
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to view this RFQ");
            }

            // Get broker ratings from NetworkFleetService
            var brokerRatings = await _networkFleetService.GetBrokerRatingsAsync(
                request.ServiceAreas,
                request.Specializations,
                request.MinRating,
                request.MaxResults,
                cancellationToken);

            // If only preferred partners requested, filter the results
            if (request.OnlyPreferredPartners)
            {
                var preferredPartnerIds = rfq.GetActivePreferredPartners()
                    .Where(pp => pp.PartnerType == OrderManagement.Domain.Entities.PartnerType.Broker)
                    .Select(pp => pp.PartnerId)
                    .ToHashSet();

                brokerRatings = brokerRatings
                    .Where(br => preferredPartnerIds.Contains(br.BrokerId))
                    .ToList();
            }

            // Mark preferred partners
            var preferredBrokerIds = rfq.GetActivePreferredPartners()
                .Where(pp => pp.PartnerType == OrderManagement.Domain.Entities.PartnerType.Broker)
                .Select(pp => pp.PartnerId)
                .ToHashSet();

            foreach (var rating in brokerRatings)
            {
                rating.IsPreferredPartner = preferredBrokerIds.Contains(rating.BrokerId);
            }

            // Apply sorting
            brokerRatings = ApplySorting(brokerRatings, request.SortBy, request.SortDescending);

            _logger.LogInformation("Successfully retrieved {Count} broker ratings for RFQ {RfqId}",
                brokerRatings.Count, request.RfqId);

            return brokerRatings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting broker ratings for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }

    private List<BrokerRatingDto> ApplySorting(List<BrokerRatingDto> ratings, string? sortBy, bool descending)
    {
        return sortBy?.ToLower() switch
        {
            "overallrating" => descending
                ? ratings.OrderByDescending(r => r.OverallRating).ToList()
                : ratings.OrderBy(r => r.OverallRating).ToList(),
            "matchsuccessrate" => descending
                ? ratings.OrderByDescending(r => r.MatchSuccessRate).ToList()
                : ratings.OrderBy(r => r.MatchSuccessRate).ToList(),
            "totalrfqs" => descending
                ? ratings.OrderByDescending(r => r.TotalRfqs).ToList()
                : ratings.OrderBy(r => r.TotalRfqs).ToList(),
            "lastrfqdate" => descending
                ? ratings.OrderByDescending(r => r.LastRfqDate).ToList()
                : ratings.OrderBy(r => r.LastRfqDate).ToList(),
            "responsetimerating" => descending
                ? ratings.OrderByDescending(r => r.ResponseTimeRating).ToList()
                : ratings.OrderBy(r => r.ResponseTimeRating).ToList(),
            "quoteaccuracyrating" => descending
                ? ratings.OrderByDescending(r => r.QuoteAccuracyRating).ToList()
                : ratings.OrderBy(r => r.QuoteAccuracyRating).ToList(),
            _ => descending
                ? ratings.OrderByDescending(r => r.OverallRating).ToList()
                : ratings.OrderBy(r => r.OverallRating).ToList()
        };
    }
}
