using Shared.Domain.Common;

namespace UserManagement.Domain.Entities;

/// <summary>
/// Represents user feedback in the system
/// </summary>
public class Feedback : BaseEntity
{
    public Guid OrderId { get; private set; }
    public Guid ReviewerId { get; private set; }
    public string ReviewerRole { get; private set; } = string.Empty; // "Shipper", "Broker", "Admin"
    public Guid RevieweeId { get; private set; }
    public string RevieweeRole { get; private set; } = string.Empty; // "Driver", "Carrier", "TransportCompany"

    // Overall rating (1-5 stars)
    public decimal OverallRating { get; private set; }

    // Detailed ratings
    public decimal ServiceQualityRating { get; private set; }
    public decimal CommunicationRating { get; private set; }
    public decimal TimelinessRating { get; private set; }
    public decimal ProfessionalismRating { get; private set; }

    // Feedback content
    public string Comments { get; private set; } = string.Empty;
    public List<string> Categories { get; private set; } = new();
    public List<string> Tags { get; private set; } = new();

    // Feedback metadata
    public string FeedbackType { get; private set; } = string.Empty; // "Order", "Service", "General"
    public bool IsAnonymous { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime SubmittedAt { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public Guid? VerifiedBy { get; private set; }

    // Additional data
    public Dictionary<string, object> Metadata { get; private set; } = new();

    private Feedback() { }

    public Feedback(
        Guid orderId,
        Guid reviewerId,
        string reviewerRole,
        Guid revieweeId,
        string revieweeRole,
        decimal overallRating,
        string comments,
        string feedbackType = "Order",
        bool isAnonymous = false)
    {
        if (overallRating < 1 || overallRating > 5)
            throw new ArgumentException("Overall rating must be between 1 and 5", nameof(overallRating));

        OrderId = orderId;
        ReviewerId = reviewerId;
        ReviewerRole = reviewerRole;
        RevieweeId = revieweeId;
        RevieweeRole = revieweeRole;
        OverallRating = overallRating;
        Comments = comments;
        FeedbackType = feedbackType;
        IsAnonymous = isAnonymous;
        SubmittedAt = DateTime.UtcNow;
        IsVerified = false;

        // Initialize with default ratings
        ServiceQualityRating = overallRating;
        CommunicationRating = overallRating;
        TimelinessRating = overallRating;
        ProfessionalismRating = overallRating;
    }

    public void UpdateDetailedRatings(
        decimal serviceQuality,
        decimal communication,
        decimal timeliness,
        decimal professionalism)
    {
        ValidateRating(serviceQuality, nameof(serviceQuality));
        ValidateRating(communication, nameof(communication));
        ValidateRating(timeliness, nameof(timeliness));
        ValidateRating(professionalism, nameof(professionalism));

        ServiceQualityRating = serviceQuality;
        CommunicationRating = communication;
        TimelinessRating = timeliness;
        ProfessionalismRating = professionalism;

        // Recalculate overall rating as average
        OverallRating = Math.Round((serviceQuality + communication + timeliness + professionalism) / 4, 1);
    }

    public void AddCategories(List<string> categories)
    {
        Categories.AddRange(categories.Where(c => !string.IsNullOrWhiteSpace(c)));
    }

    public void AddTags(List<string> tags)
    {
        Tags.AddRange(tags.Where(t => !string.IsNullOrWhiteSpace(t)));
    }

    public void Verify(Guid verifiedBy)
    {
        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsHighRating() => OverallRating >= 4.0m;
    public bool IsLowRating() => OverallRating <= 2.0m;
    public bool HasConcerns() => IsLowRating();

    private static void ValidateRating(decimal rating, string paramName)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException($"Rating must be between 1 and 5", paramName);
    }
}
