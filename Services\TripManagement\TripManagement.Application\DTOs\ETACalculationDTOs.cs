using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs;

public class ETACalculationRequest
{
    public Guid TripId { get; set; }
    public Location CurrentLocation { get; set; } = null!;
    public Location DestinationLocation { get; set; } = null!;
    public List<TripStop> RemainingStops { get; set; } = new();
    public VehicleType? VehicleType { get; set; }
    public Guid? DriverId { get; set; }
    public ETACalculationMethod CalculationMethod { get; set; } = ETACalculationMethod.Simple;
    public bool ConsiderTraffic { get; set; } = false;
    public bool ConsiderWeather { get; set; } = false;
    public bool ConsiderDriverBehavior { get; set; } = false;
    public double? CurrentSpeed { get; set; }
    public double? AverageSpeed { get; set; }
    public Dictionary<string, object>? AdditionalParameters { get; set; }
}

public class ETACalculationResult
{
    public Guid TripId { get; set; }
    public Guid RequestId { get; set; }
    public ETACalculationMethod CalculationMethod { get; set; }
    public DateTime BaseETA { get; set; }
    public DateTime FinalETA { get; set; }
    public double ConfidenceScore { get; set; }
    public DateTime CalculatedAt { get; set; }
    public List<string> InfluencingFactors { get; set; } = new();
    public Dictionary<string, object>? CalculationMetadata { get; set; }
    public TimeSpan EstimatedTravelTime => FinalETA - DateTime.UtcNow;
    public double EstimatedDistanceKm { get; set; }
}

public class MilestoneETA
{
    public Guid StopId { get; set; }
    public TripStopType StopType { get; set; }
    public Location Location { get; set; } = null!;
    public DateTime EstimatedArrival { get; set; }
    public double ConfidenceScore { get; set; }
    public double DistanceFromCurrentKm { get; set; }
    public TimeSpan EstimatedTravelTime { get; set; }
    public bool IsDelayed { get; set; }
    public TimeSpan? DelayDuration { get; set; }
}

public class ETAAccuracyMetrics
{
    public Guid TripId { get; set; }
    public int TotalPredictions { get; set; }
    public double AverageAccuracyPercentage { get; set; }
    public double MedianErrorMinutes { get; set; }
    public double StandardDeviationMinutes { get; set; }
    public DateTime CalculatedAt { get; set; }
    public List<ETAAccuracyDataPoint> DataPoints { get; set; } = new();
}

public class ETAAccuracyDataPoint
{
    public DateTime PredictedAt { get; set; }
    public DateTime PredictedETA { get; set; }
    public DateTime ActualArrival { get; set; }
    public double ErrorMinutes { get; set; }
    public double AccuracyPercentage { get; set; }
    public ETACalculationMethod Method { get; set; }
}

public class TrafficConditions
{
    public string CongestionLevel { get; set; } = string.Empty; // Low, Medium, High, Severe
    public double AverageSpeed { get; set; }
    public double DelayFactor { get; set; }
    public List<TrafficIncident> Incidents { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class TrafficIncident
{
    public string Type { get; set; } = string.Empty; // Accident, Construction, Event
    public Location Location { get; set; } = null!;
    public string Description { get; set; } = string.Empty;
    public double ImpactRadius { get; set; }
    public TimeSpan EstimatedDelay { get; set; }
}

public class WeatherConditions
{
    public string Condition { get; set; } = string.Empty; // Clear, Rain, Snow, Fog
    public double Temperature { get; set; }
    public double Visibility { get; set; }
    public double WindSpeed { get; set; }
    public double Precipitation { get; set; }
    public double ImpactFactor { get; set; } // Multiplier for travel time
    public DateTime LastUpdated { get; set; }
}

public class DriverBehaviorProfile
{
    public Guid DriverId { get; set; }
    public double AverageSpeed { get; set; }
    public double SpeedVariance { get; set; }
    public double RestFrequency { get; set; } // Stops per hour
    public double AverageRestDuration { get; set; } // Minutes
    public double OnTimePerformance { get; set; } // Percentage
    public double EfficiencyScore { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class RouteOptimizationResult
{
    public List<Location> OptimizedWaypoints { get; set; } = new();
    public double TotalDistanceKm { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public double FuelSavings { get; set; }
    public double TimeSavings { get; set; }
    public string OptimizationMethod { get; set; } = string.Empty;
    public DateTime CalculatedAt { get; set; }
}

// Service interfaces
public interface ITrafficService
{
    Task<TrafficConditions> GetTrafficConditionsAsync(Location from, Location to, CancellationToken cancellationToken = default);
    Task<List<TrafficIncident>> GetTrafficIncidentsAsync(Location center, double radiusKm, CancellationToken cancellationToken = default);
}

public interface IWeatherService
{
    Task<WeatherConditions> GetWeatherConditionsAsync(Location location, CancellationToken cancellationToken = default);
    Task<List<WeatherConditions>> GetRouteWeatherAsync(List<Location> route, CancellationToken cancellationToken = default);
}

public interface IDriverBehaviorService
{
    Task<DriverBehaviorProfile> GetDriverBehaviorProfileAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task UpdateDriverBehaviorAsync(Guid driverId, TripPerformanceData performanceData, CancellationToken cancellationToken = default);
}

public interface IRouteOptimizationService
{
    Task<RouteOptimizationResult> OptimizeRouteAsync(Location start, Location end, List<Location> waypoints, CancellationToken cancellationToken = default);
    Task<Route> GetOptimalRouteAsync(Location start, Location end, VehicleType? vehicleType = null, CancellationToken cancellationToken = default);
}

public class TripPerformanceData
{
    public Guid TripId { get; set; }
    public Guid DriverId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public double AverageSpeed { get; set; }
    public int RestStops { get; set; }
    public TimeSpan TotalRestTime { get; set; }
    public bool OnTime { get; set; }
    public TimeSpan? DelayDuration { get; set; }
}

// Milestone monitoring DTOs
public class MilestoneAlert
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public Guid? StopId { get; set; }
    public TripStopType? StopType { get; set; }
    public MilestoneAlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ScheduledTime { get; set; }
    public DateTime? EstimatedTime { get; set; }
    public double DelayMinutes { get; set; }
    public double DistanceToStopKm { get; set; }
    public DateTime DetectedAt { get; set; }
}

public class MilestoneStatus
{
    public Guid TripId { get; set; }
    public Guid StopId { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsDelayed { get; set; }
    public TimeSpan DelayDuration { get; set; }
    public double DistanceToStopKm { get; set; }
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? EstimatedArrival { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class MilestoneThresholds
{
    public int WarningThresholdMinutes { get; set; } = 15;
    public int CriticalThresholdMinutes { get; set; } = 30;
    public int OnTimeThresholdMinutes { get; set; } = 5;
}

public class MilestonePerformanceMetrics
{
    public Guid TripId { get; set; }
    public Guid StopId { get; set; }
    public TripStopType StopType { get; set; }
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? ActualArrival { get; set; }
    public bool? IsOnTime { get; set; }
    public TimeSpan? DelayDuration { get; set; }
}

public class MilestoneMonitoringOptions
{
    public const string SectionName = "MilestoneMonitoring";

    public int WarningThresholdMinutes { get; set; } = 15;
    public int CriticalThresholdMinutes { get; set; } = 30;
    public int OnTimeThresholdMinutes { get; set; } = 5;
    public int MonitoringIntervalMinutes { get; set; } = 5;
    public bool EnableNotifications { get; set; } = true;
    public bool EnableIntegrationEvents { get; set; } = true;
}

public enum MilestoneAlertSeverity
{
    Info = 0,
    Warning = 1,
    Critical = 2
}

// Real-time tracking DTOs
public class TripLocationDto
{
    public Guid TripId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public string Source { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsSignificantUpdate { get; set; }
    public double? DistanceFromPreviousKm { get; set; }
    public string GeofenceStatus { get; set; } = string.Empty;
    public string? GeofenceZoneId { get; set; }
}

public class UpdateETARequest
{
    public Location? CurrentLocation { get; set; }
    public double? CurrentSpeed { get; set; }
    public bool ConsiderTraffic { get; set; } = true;
    public bool ConsiderWeather { get; set; } = true;
    public bool ConsiderDriverBehavior { get; set; } = true;
}

// Geofencing DTOs
public class GeofenceZone
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public GeofenceZoneType ZoneType { get; set; }
    public Location CenterLocation { get; set; } = null!;
    public double RadiusMeters { get; set; }
    public bool IsActive { get; set; }
    public Guid? TripId { get; set; }
    public Guid? StopId { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }

    public void UpdateDetails(string name, string? description, Location centerLocation, double radiusMeters, bool isActive)
    {
        Name = name;
        Description = description;
        CenterLocation = centerLocation;
        RadiusMeters = radiusMeters;
        IsActive = isActive;
    }
}

public class GeofenceEvent
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public GeofenceEventType EventType { get; set; }
    public Location Location { get; set; } = null!;
    public GeofenceStatus PreviousStatus { get; set; }
    public GeofenceStatus NewStatus { get; set; }
    public DateTime Timestamp { get; set; }
    public double DistanceFromCenter { get; set; }
}

public class GeofenceCheckResult
{
    public Guid TripId { get; set; }
    public Location CurrentLocation { get; set; } = null!;
    public DateTime CheckedAt { get; set; }
    public bool IsInsideAnyZone { get; set; }
    public bool IsAtCriticalLocation { get; set; }
    public List<GeofenceZoneStatus> GeofenceStatuses { get; set; } = new();
}

public class GeofenceZoneStatus
{
    public Guid ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public GeofenceStatus Status { get; set; }
    public double DistanceFromCenterMeters { get; set; }
    public DateTime CheckedAt { get; set; }
}

public class CreateGeofenceZoneRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public GeofenceZoneType ZoneType { get; set; }
    public Location CenterLocation { get; set; } = null!;
    public double RadiusMeters { get; set; }
    public Guid? TripId { get; set; }
    public Guid? StopId { get; set; }
    public Guid? CreatedBy { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class UpdateGeofenceZoneRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public Location? CenterLocation { get; set; }
    public double? RadiusMeters { get; set; }
    public bool? IsActive { get; set; }
}

public class GeofencingOptions
{
    public const string SectionName = "Geofencing";

    public double DefaultStopRadiusMeters { get; set; } = 100.0;
    public double NearbyThresholdMeters { get; set; } = 50.0;
    public bool EnableNotifications { get; set; } = true;
    public bool EnableIntegrationEvents { get; set; } = true;
    public int MaxZonesPerTrip { get; set; } = 20;
}

public enum GeofenceZoneType
{
    Pickup = 0,
    Delivery = 1,
    Checkpoint = 2,
    RestArea = 3,
    Restricted = 4,
    Custom = 5
}

public enum GeofenceEventType
{
    None = 0,
    Approaching = 1,
    Entered = 2,
    Exited = 3,
    LeftArea = 4
}
