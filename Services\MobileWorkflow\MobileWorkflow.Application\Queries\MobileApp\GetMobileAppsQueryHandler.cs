using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Queries.MobileApp;

public class GetMobileAppsQueryHandler : IRequestHandler<GetMobileAppsQuery, IEnumerable<MobileAppDto>>
{
    private readonly IMobileAppRepository _mobileAppRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMobileAppsQueryHandler> _logger;

    public GetMobileAppsQueryHandler(
        IMobileAppRepository mobileAppRepository,
        IMapper mapper,
        ILogger<GetMobileAppsQueryHandler> logger)
    {
        _mobileAppRepository = mobileAppRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<MobileAppDto>> Handle(GetMobileAppsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting mobile apps with filters - Platform: {Platform}, IsActive: {IsActive}", 
            request.Platform, request.IsActive);

        IEnumerable<Domain.Entities.MobileApp> apps;

        if (!string.IsNullOrEmpty(request.Platform))
        {
            apps = await _mobileAppRepository.GetByPlatformAsync(request.Platform, cancellationToken);
        }
        else if (request.IsActive == true)
        {
            apps = await _mobileAppRepository.GetActiveAppsAsync(cancellationToken);
        }
        else
        {
            apps = await _mobileAppRepository.GetAllAsync(cancellationToken);
        }

        // Apply additional filters
        if (request.IsActive.HasValue && string.IsNullOrEmpty(request.Platform))
        {
            apps = apps.Where(a => a.IsActive == request.IsActive.Value);
        }

        if (request.SupportsOffline.HasValue)
        {
            apps = apps.Where(a => a.SupportsOffline == request.SupportsOffline.Value);
        }

        var result = _mapper.Map<IEnumerable<MobileAppDto>>(apps);
        
        _logger.LogInformation("Retrieved {Count} mobile apps", result.Count());
        
        return result;
    }
}

public class GetMobileAppByIdQueryHandler : IRequestHandler<GetMobileAppByIdQuery, MobileAppDto?>
{
    private readonly IMobileAppRepository _mobileAppRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMobileAppByIdQueryHandler> _logger;

    public GetMobileAppByIdQueryHandler(
        IMobileAppRepository mobileAppRepository,
        IMapper mapper,
        ILogger<GetMobileAppByIdQueryHandler> logger)
    {
        _mobileAppRepository = mobileAppRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MobileAppDto?> Handle(GetMobileAppByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting mobile app by ID: {Id}", request.Id);

        var app = await _mobileAppRepository.GetByIdAsync(request.Id, cancellationToken);
        
        if (app == null)
        {
            _logger.LogWarning("Mobile app not found with ID: {Id}", request.Id);
            return null;
        }

        return _mapper.Map<MobileAppDto>(app);
    }
}

public class GetMobileAppByPackageIdQueryHandler : IRequestHandler<GetMobileAppByPackageIdQuery, MobileAppDto?>
{
    private readonly IMobileAppRepository _mobileAppRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMobileAppByPackageIdQueryHandler> _logger;

    public GetMobileAppByPackageIdQueryHandler(
        IMobileAppRepository mobileAppRepository,
        IMapper mapper,
        ILogger<GetMobileAppByPackageIdQueryHandler> logger)
    {
        _mobileAppRepository = mobileAppRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MobileAppDto?> Handle(GetMobileAppByPackageIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting mobile app by package ID: {PackageId}", request.PackageId);

        var app = await _mobileAppRepository.GetByPackageIdAsync(request.PackageId, cancellationToken);
        
        if (app == null)
        {
            _logger.LogWarning("Mobile app not found with package ID: {PackageId}", request.PackageId);
            return null;
        }

        return _mapper.Map<MobileAppDto>(app);
    }
}
