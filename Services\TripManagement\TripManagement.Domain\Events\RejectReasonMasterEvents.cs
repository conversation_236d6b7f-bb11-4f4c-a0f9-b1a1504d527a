using Shared.Domain.Common;

namespace TripManagement.Domain.Events;

/// <summary>
/// Domain event raised when a reject reason master is created
/// </summary>
public class RejectReasonMasterCreatedEvent : DomainEvent
{
    public Guid RejectReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }

    public RejectReasonMasterCreatedEvent(Guid rejectReasonMasterId, string code, string name, string category)
    {
        RejectReasonMasterId = rejectReasonMasterId;
        Code = code;
        Name = name;
        Category = category;
    }
}

/// <summary>
/// Domain event raised when a reject reason master is updated
/// </summary>
public class RejectReasonMasterUpdatedEvent : DomainEvent
{
    public Guid RejectReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }
    public string OldName { get; }
    public string OldCategory { get; }

    public RejectReasonMasterUpdatedEvent(Guid rejectReasonMasterId, string code, string name, string category, string oldName, string oldCategory)
    {
        RejectReasonMasterId = rejectReasonMasterId;
        Code = code;
        Name = name;
        Category = category;
        OldName = oldName;
        OldCategory = oldCategory;
    }
}

/// <summary>
/// Domain event raised when a reject reason master is activated
/// </summary>
public class RejectReasonMasterActivatedEvent : DomainEvent
{
    public Guid RejectReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public RejectReasonMasterActivatedEvent(Guid rejectReasonMasterId, string code, string name)
    {
        RejectReasonMasterId = rejectReasonMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a reject reason master is deactivated
/// </summary>
public class RejectReasonMasterDeactivatedEvent : DomainEvent
{
    public Guid RejectReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public RejectReasonMasterDeactivatedEvent(Guid rejectReasonMasterId, string code, string name)
    {
        RejectReasonMasterId = rejectReasonMasterId;
        Code = code;
        Name = name;
    }
}


