using FluentValidation;

namespace SubscriptionManagement.Application.Commands.RejectPaymentProof
{
    public class RejectPaymentProofCommandValidator : AbstractValidator<RejectPaymentProofCommand>
    {
        public RejectPaymentProofCommandValidator()
        {
            RuleFor(x => x.PaymentProofId)
                .NotEmpty()
                .WithMessage("Payment proof ID is required");

            RuleFor(x => x.RejectedByUserId)
                .NotEmpty()
                .WithMessage("Rejected by user ID is required");

            RuleFor(x => x.RejectionReason)
                .NotEmpty()
                .WithMessage("Rejection reason is required")
                .MaximumLength(2000)
                .WithMessage("Rejection reason cannot exceed 2000 characters");

            RuleFor(x => x.VerificationNotes)
                .MaximumLength(2000)
                .WithMessage("Verification notes cannot exceed 2000 characters");
        }
    }
}
