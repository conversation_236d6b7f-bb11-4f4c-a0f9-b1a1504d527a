using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Services;

public interface ICrossPlatformUIService
{
    Task<UIComponentDto> CreateComponentAsync(CreateUIComponentRequest request, CancellationToken cancellationToken = default);
    Task<UIComponentDto> UpdateComponentAsync(Guid componentId, UpdateUIComponentRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteComponentAsync(Guid componentId, CancellationToken cancellationToken = default);
    Task<UIComponentDto?> GetComponentAsync(Guid componentId, CancellationToken cancellationToken = default);
    Task<List<UIComponentDto>> GetComponentsAsync(UIComponentFilterRequest? filter = null, CancellationToken cancellationToken = default);
    Task<ThemeDto> CreateThemeAsync(CreateThemeRequest request, CancellationToken cancellationToken = default);
    Task<ThemeDto> UpdateThemeAsync(Guid themeId, UpdateThemeRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteThemeAsync(Guid themeId, CancellationToken cancellationToken = default);
    Task<ThemeDto?> GetThemeAsync(Guid themeId, CancellationToken cancellationToken = default);
    Task<List<ThemeDto>> GetThemesAsync(ThemeFilterRequest? filter = null, CancellationToken cancellationToken = default);
    Task<UIRenderResult> RenderComponentAsync(Guid componentId, UIRenderContext context, CancellationToken cancellationToken = default);
    Task<UILayoutDto> CreateLayoutAsync(CreateUILayoutRequest request, CancellationToken cancellationToken = default);
    Task<List<UILayoutDto>> GetLayoutsAsync(UILayoutFilterRequest? filter = null, CancellationToken cancellationToken = default);
    Task<ComponentLibraryDto> GetComponentLibraryAsync(string platform, CancellationToken cancellationToken = default);
    Task<bool> ValidateComponentAsync(Guid componentId, string platform, CancellationToken cancellationToken = default);
}

public class CreateUIComponentRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string ComponentType { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public Dictionary<string, object> DefaultValues { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> PlatformSpecific { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public List<string> Dependencies { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsTemplate { get; set; }
    public bool IsPublic { get; set; }
}

public class UpdateUIComponentRequest
{
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public string? Category { get; set; }
    public Dictionary<string, object>? Properties { get; set; }
    public Dictionary<string, object>? DefaultValues { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public List<string>? SupportedPlatforms { get; set; }
    public bool? IsActive { get; set; }
}

public class UIComponentFilterRequest
{
    public string? Category { get; set; }
    public string? ComponentType { get; set; }
    public string? Platform { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
    public List<string>? Tags { get; set; }
    public bool IncludeTemplates { get; set; } = false;
    public bool IncludeInactive { get; set; } = false;
}

public class CreateThemeRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Colors { get; set; } = new();
    public Dictionary<string, object> ColorPalette { get; set; } = new();
    public Dictionary<string, object> Typography { get; set; } = new();
    public Dictionary<string, object> Spacing { get; set; } = new();
    public Dictionary<string, object> ComponentStyles { get; set; } = new();
    public Dictionary<string, object> PlatformSpecific { get; set; } = new();
    public Dictionary<string, object> DarkModeOverrides { get; set; } = new();
    public Dictionary<string, object> CustomProperties { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsDefault { get; set; } = false;
    public bool IsPublic { get; set; } = false;
}

public class UpdateThemeRequest
{
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object>? Colors { get; set; }
    public Dictionary<string, object>? Typography { get; set; }
    public Dictionary<string, object>? Spacing { get; set; }
    public Dictionary<string, object>? CustomProperties { get; set; }
    public List<string>? SupportedPlatforms { get; set; }
    public bool? IsDefault { get; set; }
    public bool? IsActive { get; set; }
}

public class ThemeFilterRequest
{
    public bool? IsDefault { get; set; }
    public bool? IsActive { get; set; }
    public string? Platform { get; set; }
    public string? SearchTerm { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

public class UIRenderContext
{
    public string Platform { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public Guid? ThemeId { get; set; }
    public Dictionary<string, object> UserContext { get; set; } = new();
    public Dictionary<string, object> SessionData { get; set; } = new();
}

public class UIRenderResult
{
    public string RenderedContent { get; set; } = string.Empty;
    public Dictionary<string, object> Styles { get; set; } = new();
    public Dictionary<string, object> Scripts { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateUILayoutRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string LayoutType { get; set; } = string.Empty;
    public List<Guid> ComponentIds { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
}

public class UILayoutFilterRequest
{
    public string? LayoutType { get; set; }
    public string? Platform { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

public class UILayoutDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string LayoutType { get; set; } = string.Empty;
    public List<UIComponentDto> Components { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> SupportedPlatforms { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
