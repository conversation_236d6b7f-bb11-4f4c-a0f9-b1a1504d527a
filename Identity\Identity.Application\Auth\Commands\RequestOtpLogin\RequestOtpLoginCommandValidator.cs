using FluentValidation;

namespace Identity.Application.Auth.Commands.RequestOtpLogin
{
    public class RequestOtpLoginCommandValidator : AbstractValidator<RequestOtpLoginCommand>
    {
        public RequestOtpLoginCommandValidator()
        {
            RuleFor(x => x.Username)
                .NotEmpty()
                .WithMessage("Username is required")
                .MaximumLength(320)
                .WithMessage("Username cannot exceed 320 characters");

            RuleFor(x => x.DeliveryMethod)
                .NotEmpty()
                .WithMessage("Delivery method is required")
                .Must(x => x.ToUpper() == "SMS" || x.ToUpper() == "EMAIL")
                .WithMessage("Delivery method must be either 'SMS' or 'Email'");

            RuleFor(x => x.IpAddress)
                .MaximumLength(50)
                .WithMessage("IP address cannot exceed 50 characters");

            RuleFor(x => x.UserAgent)
                .MaximumLength(1024)
                .WithMessage("User agent cannot exceed 1024 characters");
        }
    }
}
