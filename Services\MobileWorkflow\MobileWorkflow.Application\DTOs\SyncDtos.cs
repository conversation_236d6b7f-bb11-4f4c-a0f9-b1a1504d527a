namespace MobileWorkflow.Application.DTOs;

public class SyncOptions
{
    public string OperationType { get; set; } = "Incremental"; // Full, Incremental, Priority, Conflict
    public string? NetworkType { get; set; }
    public string? ClientVersion { get; set; }
    public bool EnableCompression { get; set; } = true;
    public bool PrioritizeHighPriorityItems { get; set; } = true;
    public int? MaxBandwidthKbps { get; set; }
    public int? MaxConcurrentItems { get; set; } = 5;
    public TimeSpan? Timeout { get; set; }
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

public class SyncOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Guid? SyncOperationId { get; set; }
    public string? Status { get; set; }
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public int FailedItems { get; set; }
    public int ConflictItems { get; set; }
    public double ProgressPercentage { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
    public TimeSpan? EstimatedDuration { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? ErrorMessage { get; set; }
    public List<SyncItemDto> SyncItems { get; set; } = new();
}

public class SyncItemDto
{
    public Guid Id { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int Priority { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public long DataSizeBytes { get; set; }
    public int Version { get; set; }
    public string? ConflictType { get; set; }
    public int RetryCount { get; set; }
    public DateTime? NextRetryTime { get; set; }
}

public class ConflictResolutionDto
{
    public Guid Id { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string ConflictType { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public Dictionary<string, object> LocalData { get; set; } = new();
    public Dictionary<string, object> RemoteData { get; set; } = new();
    public Dictionary<string, object> ConflictDetails { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public DateTime ConflictDetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public bool CanAutoResolve { get; set; }
    public string SuggestedStrategy { get; set; } = string.Empty;
    public List<string> ConflictingFields { get; set; } = new();
}

public class ConflictResolutionRequest
{
    public Guid ConflictId { get; set; }
    public string ResolutionType { get; set; } = string.Empty; // LocalWins, RemoteWins, Merge, Manual
    public string ResolutionStrategy { get; set; } = string.Empty; // LocalWins, RemoteWins, Merge, Manual
    public Dictionary<string, object>? ResolutionData { get; set; }
    public Guid ResolvedBy { get; set; }
    public string? Notes { get; set; }
    public string? Comments { get; set; }
    public bool ApplyToSimilar { get; set; } = false;
    public Dictionary<string, object>? CustomData { get; set; }
}

public class SyncStatistics
{
    public int TotalOperations { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public double AverageOperationDuration { get; set; }
    public long TotalDataSynced { get; set; }
    public double AverageBandwidth { get; set; }
    public int ConflictsResolved { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;
}

public class BandwidthOptimizationOptions
{
    public bool EnableCompression { get; set; } = true;
    public int? MaxBandwidthKbps { get; set; }
    public bool PrioritizeHighPriorityItems { get; set; } = true;
    public bool EnableDeltaSync { get; set; } = true;
    public int? MaxConcurrentTransfers { get; set; } = 3;
    public bool AdaptiveQuality { get; set; } = true;
}

public class OfflineSyncQueueDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public int Priority { get; set; }
    public DateTime CreatedOffline { get; set; }
    public bool IsSynced { get; set; }
    public int SyncAttempts { get; set; }
    public string? SyncError { get; set; }
    public long DataSizeBytes { get; set; }
    public string? DataHash { get; set; }
}

public class SyncProgressDto
{
    public Guid SyncOperationId { get; set; }
    public string Status { get; set; } = string.Empty;
    public double ProgressPercentage { get; set; }
    public int ProcessedItems { get; set; }
    public int TotalItems { get; set; }
    public int FailedItems { get; set; }
    public int ConflictItems { get; set; }
    public long TransferredBytes { get; set; }
    public long TotalBytes { get; set; }
    public double? CurrentBandwidthKbps { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
    public DateTime StartTime { get; set; }
    public string? CurrentItem { get; set; }
    public List<string> RecentErrors { get; set; } = new();
}

public class DataVersionInfo
{
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public int Version { get; set; }
    public DateTime LastModified { get; set; }
    public string? DataHash { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SyncConflictDto
{
    public Guid Id { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string ConflictType { get; set; } = string.Empty;
    public DataVersionInfo LocalVersion { get; set; } = new();
    public DataVersionInfo RemoteVersion { get; set; } = new();
    public List<FieldConflictDto> FieldConflicts { get; set; } = new();
    public DateTime DetectedAt { get; set; }
    public bool RequiresManualResolution { get; set; }
    public string? SuggestedResolution { get; set; }
}

public class FieldConflictDto
{
    public string FieldName { get; set; } = string.Empty;
    public object? LocalValue { get; set; }
    public object? RemoteValue { get; set; }
    public string ConflictReason { get; set; } = string.Empty;
    public string? SuggestedValue { get; set; }
}

public class SyncPolicyDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Rules { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class NetworkOptimizationDto
{
    public string NetworkType { get; set; } = string.Empty;
    public double BandwidthKbps { get; set; }
    public int Latency { get; set; }
    public bool IsMetered { get; set; }
    public string Quality { get; set; } = string.Empty; // Excellent, Good, Fair, Poor
    public Dictionary<string, object> OptimizationSettings { get; set; } = new();
}

public class SyncHealthDto
{
    public Guid UserId { get; set; }
    public DateTime LastSuccessfulSync { get; set; }
    public int PendingItems { get; set; }
    public int FailedItems { get; set; }
    public int ConflictItems { get; set; }
    public List<string> RecentErrors { get; set; } = new();
    public double SyncSuccessRate { get; set; }
    public TimeSpan AverageSyncDuration { get; set; }
    public long TotalDataPending { get; set; }
    public string OverallHealth { get; set; } = string.Empty; // Healthy, Warning, Critical
}

public class BatchSyncRequest
{
    public List<Guid> UserIds { get; set; } = new();
    public SyncOptions Options { get; set; } = new();
    public bool ProcessInParallel { get; set; } = true;
    public int MaxConcurrentOperations { get; set; } = 10;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class BatchSyncResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int TotalUsers { get; set; }
    public int SuccessfulSyncs { get; set; }
    public int FailedSyncs { get; set; }
    public List<SyncOperationResult> Results { get; set; } = new();
    public TimeSpan TotalDuration { get; set; }
}

// Alias for backward compatibility - maps to SyncItemDto
public class SyncItem : SyncItemDto
{
}

public class OfflineData
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid MobileSessionId { get; set; }
    public Guid? MobileAppId { get; set; }
    public string DataType { get; set; } = string.Empty; // TripUpdate, PODUpload, DocumentUpload, etc.
    public string Action { get; set; } = string.Empty; // Create, Update, Delete
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime CreatedOffline { get; set; }
    public DateTime? SyncedAt { get; set; }
    public bool IsSynced { get; set; }
    public string? SyncError { get; set; }
    public int SyncAttempts { get; set; }
    public int Priority { get; set; } // 1 = High (Emergency), 2 = Medium, 3 = Low
    public string? ConflictResolution { get; set; }
    public string Status { get; set; } = string.Empty; // Pending, Synced, Conflict, Error
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModified { get; set; }
    public DateTime? LastSyncAttempt { get; set; }
    public DateTime? LastSuccessfulSync { get; set; }
    public string? LastError { get; set; }
    public long DataSizeBytes { get; set; }
    public int Version { get; set; }
    public string? ConflictType { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
}
