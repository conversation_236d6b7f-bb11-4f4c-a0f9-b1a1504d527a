using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class TestAutoAssignmentQueryHandler : IRequestHandler<TestAutoAssignmentQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<TestAutoAssignmentQueryHandler> _logger;

    public TestAutoAssignmentQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<TestAutoAssignmentQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(TestAutoAssignmentQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var eligiblePartners = await _preferredPartnerRepository.GetAutoAssignEligiblePartnersAsync(
                request.UserId,
                request.PartnerType,
                request.Route,
                request.LoadType,
                cancellationToken);

            // Filter by minimum rating if specified
            if (request.MinRating.HasValue)
            {
                eligiblePartners = eligiblePartners.Where(p => 
                    p.PerformanceMetrics.OverallRating >= request.MinRating.Value);
            }

            // Apply auto-assignment criteria and sort by priority
            var sortedPartners = eligiblePartners
                .Where(p => p.CanAutoAssign)
                .Where(p => !request.MinRating.HasValue || p.MeetsAutoAssignCriteria(request.MinRating.Value))
                .OrderBy(p => p.PreferenceLevel)
                .ThenBy(p => p.Priority)
                .ThenByDescending(p => p.PerformanceMetrics.OverallRating)
                .Take(request.MaxResults);

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(sortedPartners);

            _logger.LogInformation("Auto-assignment test returned {Count} eligible partners for user {UserId}", 
                result.Count, request.UserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing auto-assignment for user {UserId}", request.UserId);
            throw;
        }
    }
}
