-- Migration: Add Milestone Configuration Tables
-- Description: Creates tables for milestone templates, steps, payout rules, and role mappings
-- Date: 2024-06-24

BEGIN;

-- Milestone Templates table
CREATE TABLE milestone_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    description VARCHAR(1000) NOT NULL,
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false,
    created_by VARCHAR(200) NOT NULL,
    updated_by VARCHAR(200),
    updated_at TIMESTAMP,
    usage_count INTEGER NOT NULL DEFAULT 0,
    configuration JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Milestone Steps table
CREATE TABLE milestone_steps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    milestone_template_id UUID NOT NULL REFERENCES milestone_templates(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description VARCHAR(1000) NOT NULL,
    sequence_number INTEGER NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT true,
    is_active BOOLEAN NOT NULL DEFAULT true,
    trigger_condition VARCHAR(500),
    configuration JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Milestone Payout Rules table
CREATE TABLE milestone_payout_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    milestone_step_id UUID NOT NULL REFERENCES milestone_steps(id) ON DELETE CASCADE,
    payout_percentage DECIMAL(5,2) NOT NULL CHECK (payout_percentage >= 0 AND payout_percentage <= 100),
    trigger_condition VARCHAR(500),
    is_active BOOLEAN NOT NULL DEFAULT true,
    description VARCHAR(1000),
    configuration JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Role Template Mappings table
CREATE TABLE role_template_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_name VARCHAR(100) NOT NULL,
    milestone_template_id UUID NOT NULL REFERENCES milestone_templates(id) ON DELETE CASCADE,
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    conditions VARCHAR(2000),
    created_by VARCHAR(200) NOT NULL,
    updated_by VARCHAR(200),
    updated_at TIMESTAMP,
    configuration JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_milestone_templates_name ON milestone_templates(name);
CREATE INDEX idx_milestone_templates_type ON milestone_templates(type);
CREATE INDEX idx_milestone_templates_category ON milestone_templates(category);
CREATE INDEX idx_milestone_templates_is_active ON milestone_templates(is_active);
CREATE INDEX idx_milestone_templates_is_default ON milestone_templates(is_default);
CREATE INDEX idx_milestone_templates_created_by ON milestone_templates(created_by);

CREATE INDEX idx_milestone_steps_template_id ON milestone_steps(milestone_template_id);
CREATE INDEX idx_milestone_steps_template_sequence ON milestone_steps(milestone_template_id, sequence_number);
CREATE INDEX idx_milestone_steps_is_active ON milestone_steps(is_active);
CREATE INDEX idx_milestone_steps_is_required ON milestone_steps(is_required);

CREATE INDEX idx_milestone_payout_rules_step_id ON milestone_payout_rules(milestone_step_id);
CREATE INDEX idx_milestone_payout_rules_is_active ON milestone_payout_rules(is_active);

CREATE INDEX idx_role_template_mappings_role_name ON role_template_mappings(role_name);
CREATE INDEX idx_role_template_mappings_template_id ON role_template_mappings(milestone_template_id);
CREATE INDEX idx_role_template_mappings_role_template ON role_template_mappings(role_name, milestone_template_id);
CREATE INDEX idx_role_template_mappings_is_default ON role_template_mappings(is_default);
CREATE INDEX idx_role_template_mappings_is_active ON role_template_mappings(is_active);
CREATE INDEX idx_role_template_mappings_priority ON role_template_mappings(priority);
CREATE INDEX idx_role_template_mappings_created_by ON role_template_mappings(created_by);

-- Create triggers for updating updated_at timestamps
CREATE TRIGGER update_milestone_templates_updated_at 
    BEFORE UPDATE ON milestone_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_template_mappings_updated_at 
    BEFORE UPDATE ON role_template_mappings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add unique constraint to ensure only one sequence number per template
ALTER TABLE milestone_steps ADD CONSTRAINT uk_milestone_steps_template_sequence 
    UNIQUE (milestone_template_id, sequence_number);

-- Add unique constraint to ensure only one role-template mapping per role and template
ALTER TABLE role_template_mappings ADD CONSTRAINT uk_role_template_mappings_role_template 
    UNIQUE (role_name, milestone_template_id);

-- Insert sample milestone templates for testing
INSERT INTO milestone_templates (name, description, type, category, created_by, configuration) VALUES
('Standard Trip Milestones', 'Standard milestone template for trip completion', 'Trip', 'Logistics', 'system', 
 '{"auto_advance": true, "require_photos": true, "allow_skip_optional": false}'),
('Express Delivery Milestones', 'Fast-track milestone template for express deliveries', 'Trip', 'Delivery', 'system',
 '{"auto_advance": true, "require_photos": false, "allow_skip_optional": true}'),
('Order Processing Milestones', 'Standard milestone template for order processing', 'Order', 'Logistics', 'system',
 '{"auto_advance": false, "require_approval": true, "escalation_enabled": true}');

-- Insert sample milestone steps
INSERT INTO milestone_steps (milestone_template_id, name, description, sequence_number, is_required) VALUES
((SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), 'Trip Started', 'Driver has started the trip', 1, true),
((SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), 'Pickup Completed', 'Goods have been picked up from origin', 2, true),
((SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), 'In Transit', 'Trip is in progress to destination', 3, true),
((SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), 'Delivery Completed', 'Goods have been delivered to destination', 4, true),
((SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), 'POD Uploaded', 'Proof of delivery has been uploaded', 5, true);

-- Insert sample payout rules
INSERT INTO milestone_payout_rules (milestone_step_id, payout_percentage, description) VALUES
((SELECT id FROM milestone_steps WHERE name = 'Trip Started'), 10.00, 'Initial payment for starting the trip'),
((SELECT id FROM milestone_steps WHERE name = 'Pickup Completed'), 20.00, 'Payment for successful pickup'),
((SELECT id FROM milestone_steps WHERE name = 'In Transit'), 20.00, 'Payment for transit milestone'),
((SELECT id FROM milestone_steps WHERE name = 'Delivery Completed'), 40.00, 'Payment for successful delivery'),
((SELECT id FROM milestone_steps WHERE name = 'POD Uploaded'), 10.00, 'Final payment for POD submission');

-- Insert sample role mappings
INSERT INTO role_template_mappings (role_name, milestone_template_id, is_default, priority, created_by) VALUES
('Driver', (SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), true, 100, 'system'),
('Carrier', (SELECT id FROM milestone_templates WHERE name = 'Standard Trip Milestones'), false, 90, 'system'),
('Admin', (SELECT id FROM milestone_templates WHERE name = 'Order Processing Milestones'), true, 100, 'system');

COMMIT;
