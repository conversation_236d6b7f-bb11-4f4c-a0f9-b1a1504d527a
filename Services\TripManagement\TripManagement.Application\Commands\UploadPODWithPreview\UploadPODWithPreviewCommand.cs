using MediatR;

namespace TripManagement.Application.Commands.UploadPODWithPreview;

public class UploadPODWithPreviewCommand : IRequest<UploadPODWithPreviewResponse>
{
    public Guid TripId { get; set; }
    public Guid DriverId { get; set; }
    public PODUploadData PODData { get; set; } = new();
    public bool IsPreviewMode { get; set; } = true; // True for preview, false for final submission
    public bool RequireConfirmation { get; set; } = true;
    public string? ConfirmationToken { get; set; } // Required for final submission
}

public class UploadPODWithPreviewResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public PODUploadResult? Result { get; set; }
    public PODPreviewData? PreviewData { get; set; }
    public string? ConfirmationToken { get; set; } // For confirming the upload
    public List<ValidationIssue> ValidationIssues { get; set; } = new();
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}

public class PODUploadData
{
    public List<PODDocument> Documents { get; set; } = new();
    public PODSignature? Signature { get; set; }
    public string? RecipientName { get; set; }
    public string? RecipientTitle { get; set; }
    public string? RecipientPhone { get; set; }
    public string? RecipientEmail { get; set; }
    public DateTime DeliveryDateTime { get; set; } = DateTime.UtcNow;
    public double? DeliveryLatitude { get; set; }
    public double? DeliveryLongitude { get; set; }
    public string? DeliveryAddress { get; set; }
    public string? DeliveryNotes { get; set; }
    public List<string> DeliveryConditions { get; set; } = new(); // Good, Damaged, Partial, etc.
    public Dictionary<string, object> CustomFields { get; set; } = new();
    public bool RequireRecipientVerification { get; set; } = true;
    public string? VerificationMethod { get; set; } // OTP, ID, Signature
}

public class PODDocument
{
    public string DocumentType { get; set; } = string.Empty; // Photo, Invoice, Receipt, Damage Report
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] Content { get; set; } = Array.Empty<byte>();
    public long FileSize { get; set; }
    public string? Description { get; set; }
    public bool IsRequired { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PODSignature
{
    public byte[] SignatureData { get; set; } = Array.Empty<byte>();
    public string SignatureFormat { get; set; } = "SVG"; // SVG, PNG, etc.
    public DateTime SignedAt { get; set; } = DateTime.UtcNow;
    public string? SignerName { get; set; }
    public string? SignerTitle { get; set; }
    public string? SignerID { get; set; }
    public bool IsDigitalSignature { get; set; } = false;
}

public class PODUploadResult
{
    public Guid PODId { get; set; }
    public string PODNumber { get; set; } = string.Empty;
    public PODStatus Status { get; set; }
    public DateTime UploadedAt { get; set; }
    public List<string> UploadedDocuments { get; set; } = new();
    public string? TemporaryStorageLocation { get; set; }
    public string? PermanentStorageLocation { get; set; }
    public PODProcessingInfo ProcessingInfo { get; set; } = new();
}

public class PODPreviewData
{
    public List<DocumentPreview> DocumentPreviews { get; set; } = new();
    public SignaturePreview? SignaturePreview { get; set; }
    public DeliveryInfoPreview DeliveryInfo { get; set; } = new();
    public List<string> RequiredActions { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool IsReadyForSubmission { get; set; }
    public DateTime PreviewGeneratedAt { get; set; } = DateTime.UtcNow;
}

public class DocumentPreview
{
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string PreviewUrl { get; set; } = string.Empty;
    public string ThumbnailUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new();
}

public class SignaturePreview
{
    public string PreviewUrl { get; set; } = string.Empty;
    public string SignerName { get; set; } = string.Empty;
    public DateTime SignedAt { get; set; }
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new();
}

public class DeliveryInfoPreview
{
    public string RecipientName { get; set; } = string.Empty;
    public string RecipientTitle { get; set; } = string.Empty;
    public DateTime DeliveryDateTime { get; set; }
    public string DeliveryAddress { get; set; } = string.Empty;
    public List<string> DeliveryConditions { get; set; } = new();
    public string? DeliveryNotes { get; set; }
    public bool LocationVerified { get; set; }
    public bool RecipientVerified { get; set; }
}

public class PODProcessingInfo
{
    public string ProcessingStatus { get; set; } = string.Empty; // Uploading, Processing, Completed, Failed
    public double ProcessingProgress { get; set; }
    public List<ProcessingStep> ProcessingSteps { get; set; } = new();
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ProcessingStep
{
    public string StepName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, InProgress, Completed, Failed
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ValidationIssue
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Error, Warning, Info
    public string? Field { get; set; }
    public bool IsBlocking { get; set; }
    public string? SuggestedAction { get; set; }
}

public enum PODStatus
{
    Draft = 1,
    Preview = 2,
    Submitted = 3,
    Processing = 4,
    Verified = 5,
    Approved = 6,
    Rejected = 7,
    Archived = 8
}
