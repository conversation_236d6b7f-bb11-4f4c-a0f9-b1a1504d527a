using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;

namespace FinancialPayment.Domain.Entities;

public class Commission : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public Guid CarrierId { get; private set; }
    public Money OrderAmount { get; private set; }
    public CommissionStructure CommissionStructure { get; private set; }
    public Money CalculatedAmount { get; private set; }
    public CommissionStatus Status { get; private set; }

    public DateTime? ProcessedAt { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<CommissionAdjustment> _adjustments = new();
    public IReadOnlyList<CommissionAdjustment> Adjustments => _adjustments.AsReadOnly();

    protected Commission() { }

    public Commission(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure,
        string? notes = null)
    {
        if (orderAmount == null || orderAmount.Amount <= 0)
            throw new ArgumentException("Order amount must be greater than zero");

        if (commissionStructure == null)
            throw new ArgumentNullException(nameof(commissionStructure));

        OrderId = orderId;
        BrokerId = brokerId;
        TransportCompanyId = transportCompanyId;
        CarrierId = carrierId;
        OrderAmount = orderAmount;
        CommissionStructure = commissionStructure;
        CalculatedAmount = CalculateCommission(orderAmount, commissionStructure);
        Status = CommissionStatus.Calculated;
        Notes = notes;

        AddDomainEvent(new CommissionCalculatedEvent(Id, OrderId, BrokerId, CalculatedAmount));
    }

    public void AddAdjustment(Money adjustmentAmount, CommissionAdjustmentType type, string reason)
    {
        if (Status == CommissionStatus.Paid)
            throw new InvalidOperationException("Cannot adjust paid commissions");

        var adjustment = new CommissionAdjustment(Id, adjustmentAmount, type, reason);
        _adjustments.Add(adjustment);

        // Recalculate total amount based on original calculated amount plus adjustments
        var totalAdjustments = _adjustments.Sum(a =>
            a.Type == CommissionAdjustmentType.Increase ? a.Amount.Amount : -a.Amount.Amount);

        // Start with the original calculated commission amount (without previous adjustments)
        var originalCalculatedAmount = CalculateCommission(OrderAmount, CommissionStructure);
        var newAmount = originalCalculatedAmount.Amount + totalAdjustments;

        if (newAmount < 0)
        {
            CalculatedAmount = Money.Zero(OrderAmount.Currency);
        }
        else
        {
            CalculatedAmount = new Money(newAmount, OrderAmount.Currency);
        }

        AddDomainEvent(new CommissionAdjustedEvent(Id, OrderId, adjustment.Id, adjustmentAmount, type));
    }

    public void Approve(string approvedBy)
    {
        if (Status != CommissionStatus.Calculated)
            throw new InvalidOperationException("Only calculated commissions can be approved");

        Status = CommissionStatus.Approved;
        Notes = $"Approved by: {approvedBy}";
        AddDomainEvent(new CommissionApprovedEvent(Id, OrderId, BrokerId, CalculatedAmount));
    }

    public void Pay(string paymentGatewayTransactionId)
    {
        if (Status != CommissionStatus.Approved)
            throw new InvalidOperationException("Only approved commissions can be paid");

        Status = CommissionStatus.Paid;
        ProcessedAt = DateTime.UtcNow;
        Notes = $"Paid via transaction: {paymentGatewayTransactionId}";
        AddDomainEvent(new CommissionPaidEvent(Id, OrderId, BrokerId, CalculatedAmount, paymentGatewayTransactionId));
    }

    public void Dispute(string reason)
    {
        if (Status == CommissionStatus.Paid)
            throw new InvalidOperationException("Cannot dispute paid commissions");

        Status = CommissionStatus.Disputed;
        Notes = $"Disputed: {reason}";
        AddDomainEvent(new CommissionDisputedEvent(Id, OrderId, BrokerId, reason));
    }

    public void ResolveDispute(Money resolvedAmount, string resolution)
    {
        if (Status != CommissionStatus.Disputed)
            throw new InvalidOperationException("Only disputed commissions can be resolved");

        CalculatedAmount = resolvedAmount;
        Status = CommissionStatus.Approved;
        Notes = $"Dispute resolved: {resolution}";
        AddDomainEvent(new CommissionDisputeResolvedEvent(Id, OrderId, BrokerId, resolvedAmount, resolution));
    }

    private static Money CalculateCommission(Money orderAmount, CommissionStructure structure)
    {
        decimal commissionAmount = structure.Type switch
        {
            CommissionType.Percentage => orderAmount.Amount * (structure.Rate / 100),
            CommissionType.FixedAmount => structure.Rate,
            CommissionType.Tiered => CalculateTieredCommission(orderAmount.Amount, structure),
            _ => throw new ArgumentException("Invalid commission type")
        };

        // Apply minimum and maximum limits
        if (structure.MinimumAmount.HasValue && commissionAmount < structure.MinimumAmount.Value)
            commissionAmount = structure.MinimumAmount.Value;

        if (structure.MaximumAmount.HasValue && commissionAmount > structure.MaximumAmount.Value)
            commissionAmount = structure.MaximumAmount.Value;

        return new Money(commissionAmount, orderAmount.Currency);
    }

    private static decimal CalculateTieredCommission(decimal orderAmount, CommissionStructure structure)
    {
        // This is a simplified tiered calculation
        // In a real implementation, you would have tier definitions
        if (orderAmount <= 10000) return orderAmount * 0.05m; // 5% for orders <= 10k
        if (orderAmount <= 50000) return 500 + (orderAmount - 10000) * 0.03m; // 3% for 10k-50k
        return 1700 + (orderAmount - 50000) * 0.02m; // 2% for >50k
    }
}
