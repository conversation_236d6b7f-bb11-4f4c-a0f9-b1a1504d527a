using AnalyticsBIService.Domain.Entities;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for report execution operations
/// </summary>
public interface IReportExecutionRepository : IRepositoryBase<ReportExecution, Guid>
{
    /// <summary>
    /// Get report executions by report ID
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetByReportIdAsync(Guid reportId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report executions by status
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report executions by user ID
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recent report executions
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetRecentExecutionsAsync(int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get failed report executions
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetFailedExecutionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get scheduled reports
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetScheduledReportsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Add scheduled report
    /// </summary>
    Task<ReportExecution> AddScheduledReportAsync(ReportExecution scheduledReport, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get execution history
    /// </summary>
    Task<IEnumerable<ReportExecution>> GetExecutionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
}
