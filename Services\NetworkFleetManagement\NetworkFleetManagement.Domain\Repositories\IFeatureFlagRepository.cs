using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IFeatureFlagRepository
{
    Task<FeatureFlag?> GetByKeyAsync(string key);
    Task<FeatureFlag?> GetByIdAsync(Guid id);
    Task<List<FeatureFlag>> GetAllAsync();
    Task<List<FeatureFlag>> GetActiveAsync();
    Task<List<FeatureFlag>> GetByEnvironmentAsync(string environment);
    Task<List<FeatureFlag>> GetByTagsAsync(string tags);
    
    Task<FeatureFlag> CreateAsync(FeatureFlag featureFlag);
    Task<FeatureFlag> UpdateAsync(FeatureFlag featureFlag);
    Task DeleteAsync(Guid id);
    
    // Usage analytics
    Task<Dictionary<string, int>> GetUsageStatsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, object>> GetAnalyticsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    
    // A/B Testing
    Task<Dictionary<string, object>> GetABTestResultsAsync(string key, DateTime? from = null, DateTime? to = null);
    Task RecordConversionAsync(string key, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null);
}
