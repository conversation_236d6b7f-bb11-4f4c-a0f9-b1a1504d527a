using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace NetworkFleetManagement.Infrastructure.MessageBus;

public enum MessageBusProvider
{
    RabbitMQ,
    AzureServiceBus,
    InMemory
}

public interface IMessageBusFactory
{
    IMessageBusPublisher CreatePublisher(MessageBusProvider provider);
}

public class MessageBusFactory : IMessageBusFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<MessageBusFactory> _logger;

    public MessageBusFactory(IServiceProvider serviceProvider, IConfiguration configuration, ILogger<MessageBusFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    public IMessageBusPublisher CreatePublisher(MessageBusProvider provider)
    {
        return provider switch
        {
            MessageBusProvider.RabbitMQ => CreateRabbitMQPublisher(),
            MessageBusProvider.AzureServiceBus => CreateAzureServiceBusPublisher(),
            MessageBusProvider.InMemory => CreateInMemoryPublisher(),
            _ => throw new ArgumentException($"Unsupported message bus provider: {provider}")
        };
    }

    private IMessageBusPublisher CreateRabbitMQPublisher()
    {
        try
        {
            var settings = _serviceProvider.GetRequiredService<IOptions<RabbitMQSettings>>();
            var logger = _serviceProvider.GetRequiredService<ILogger<RabbitMQPublisher>>();
            return new RabbitMQPublisher(settings, logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create RabbitMQ publisher");
            throw;
        }
    }

    private IMessageBusPublisher CreateAzureServiceBusPublisher()
    {
        try
        {
            var settings = _serviceProvider.GetRequiredService<IOptions<AzureServiceBusSettings>>();
            var logger = _serviceProvider.GetRequiredService<ILogger<AzureServiceBusPublisher>>();
            return new AzureServiceBusPublisher(settings, logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Azure Service Bus publisher");
            throw;
        }
    }

    private IMessageBusPublisher CreateInMemoryPublisher()
    {
        var logger = _serviceProvider.GetRequiredService<ILogger<InMemoryMessageBusPublisher>>();
        return new InMemoryMessageBusPublisher(logger);
    }
}

public class InMemoryMessageBusPublisher : IMessageBusPublisher
{
    private readonly ILogger<InMemoryMessageBusPublisher> _logger;
    private readonly List<PublishedMessage> _publishedMessages = new();

    public InMemoryMessageBusPublisher(ILogger<InMemoryMessageBusPublisher> logger)
    {
        _logger = logger;
    }

    public async Task PublishAsync<T>(T message, string routingKey, CancellationToken cancellationToken = default) where T : class
    {
        var publishedMessage = new PublishedMessage
        {
            Id = Guid.NewGuid(),
            Message = message,
            RoutingKey = routingKey,
            Timestamp = DateTime.UtcNow,
            MessageType = typeof(T).Name
        };

        _publishedMessages.Add(publishedMessage);
        
        _logger.LogInformation("In-memory message published: {MessageType} with routing key {RoutingKey}", 
            typeof(T).Name, routingKey);

        await Task.CompletedTask;
    }

    public async Task PublishAsync(string message, string routingKey, CancellationToken cancellationToken = default)
    {
        var publishedMessage = new PublishedMessage
        {
            Id = Guid.NewGuid(),
            Message = message,
            RoutingKey = routingKey,
            Timestamp = DateTime.UtcNow,
            MessageType = "String"
        };

        _publishedMessages.Add(publishedMessage);
        
        _logger.LogInformation("In-memory string message published with routing key {RoutingKey}", routingKey);

        await Task.CompletedTask;
    }

    public async Task PublishIntegrationEventAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class
    {
        var eventType = typeof(T).Name;
        var routingKey = $"network-fleet.{eventType.ToLowerInvariant()}";

        var eventWrapper = new
        {
            EventId = Guid.NewGuid(),
            EventType = eventType,
            Source = "NetworkFleetManagement",
            Timestamp = DateTime.UtcNow,
            Version = "1.0",
            Data = integrationEvent
        };

        await PublishAsync(eventWrapper, routingKey, cancellationToken);
    }

    public IReadOnlyList<PublishedMessage> GetPublishedMessages() => _publishedMessages.AsReadOnly();

    public void ClearMessages() => _publishedMessages.Clear();
}

public class PublishedMessage
{
    public Guid Id { get; set; }
    public object Message { get; set; } = null!;
    public string RoutingKey { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string MessageType { get; set; } = string.Empty;
}

public static class MessageBusExtensions
{
    public static IServiceCollection AddMessageBus(this IServiceCollection services, IConfiguration configuration)
    {
        var provider = configuration.GetValue<MessageBusProvider>("MessageBus:Provider");
        
        services.Configure<RabbitMQSettings>(configuration.GetSection(RabbitMQSettings.SectionName));
        services.Configure<AzureServiceBusSettings>(configuration.GetSection(AzureServiceBusSettings.SectionName));
        
        services.AddSingleton<IMessageBusFactory, MessageBusFactory>();
        
        services.AddSingleton<IMessageBusPublisher>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IMessageBusFactory>();
            return factory.CreatePublisher(provider);
        });

        return services;
    }
}
