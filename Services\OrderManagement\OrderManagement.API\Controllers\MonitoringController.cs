using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Services;

namespace OrderManagement.API.Controllers;

[Authorize]
[Route("api/v1/monitoring")]
public class MonitoringController : BaseController
{
    private readonly IOrderMetricsService _metricsService;

    public MonitoringController(IOrderMetricsService metricsService)
    {
        _metricsService = metricsService;
    }

    /// <summary>
    /// Get order metrics for a date range
    /// </summary>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(OrderMetrics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOrderMetrics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30); // Default to last 30 days
            var to = toDate ?? DateTime.UtcNow;

            if (from > to)
            {
                return BadRequest(new { message = "From date cannot be greater than to date" });
            }

            if ((to - from).TotalDays > 365)
            {
                return BadRequest(new { message = "Date range cannot exceed 365 days" });
            }

            var metrics = await _metricsService.GetOrderMetricsAsync(from, to);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get current system status and key performance indicators
    /// </summary>
    [HttpGet("status")]
    [ProducesResponseType(typeof(SystemStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetSystemStatus()
    {
        try
        {
            // Get metrics for the last 24 hours
            var yesterday = DateTime.UtcNow.AddDays(-1);
            var now = DateTime.UtcNow;
            
            var metrics = await _metricsService.GetOrderMetricsAsync(yesterday, now);

            var status = new SystemStatus
            {
                Timestamp = DateTime.UtcNow,
                IsHealthy = true, // This would be determined by various health checks
                OrdersLast24Hours = metrics.TotalOrders,
                InvoicesLast24Hours = metrics.TotalInvoices,
                PendingOrders = metrics.OrdersByStatus.GetValueOrDefault("Created", 0) + 
                               metrics.OrdersByStatus.GetValueOrDefault("Confirmed", 0),
                ActiveOrders = metrics.OrdersByStatus.GetValueOrDefault("InProgress", 0),
                CompletedOrdersLast24Hours = metrics.OrdersByStatus.GetValueOrDefault("Completed", 0),
                OverdueInvoices = metrics.OverdueInvoices,
                AverageOrderProcessingTime = metrics.AverageTimeToComplete,
                AveragePaymentTime = metrics.AverageTimeToPay,
                SystemLoad = "Normal", // This would be calculated based on actual system metrics
                LastUpdated = DateTime.UtcNow
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get performance metrics for dashboards
    /// </summary>
    [HttpGet("performance")]
    [ProducesResponseType(typeof(PerformanceMetrics), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetPerformanceMetrics(
        [FromQuery] int days = 7)
    {
        try
        {
            if (days < 1 || days > 90)
            {
                return BadRequest(new { message = "Days must be between 1 and 90" });
            }

            var fromDate = DateTime.UtcNow.AddDays(-days);
            var toDate = DateTime.UtcNow;

            var metrics = await _metricsService.GetOrderMetricsAsync(fromDate, toDate);

            var performance = new PerformanceMetrics
            {
                Period = $"Last {days} days",
                FromDate = fromDate,
                ToDate = toDate,
                TotalOrders = metrics.TotalOrders,
                CompletionRate = metrics.TotalOrders > 0 
                    ? (double)metrics.OrdersByStatus.GetValueOrDefault("Completed", 0) / metrics.TotalOrders * 100 
                    : 0,
                CancellationRate = metrics.TotalOrders > 0 
                    ? (double)metrics.OrdersByStatus.GetValueOrDefault("Cancelled", 0) / metrics.TotalOrders * 100 
                    : 0,
                AverageOrderValue = metrics.AverageOrderValue,
                TotalRevenue = metrics.TotalOrderValue,
                InvoicePaymentRate = metrics.TotalInvoices > 0 
                    ? (double)metrics.PaidInvoices / metrics.TotalInvoices * 100 
                    : 0,
                AverageTimeToComplete = metrics.AverageTimeToComplete,
                AverageTimeToPay = metrics.AverageTimeToPay,
                UrgentOrderPercentage = metrics.TotalOrders > 0 
                    ? (double)metrics.UrgentOrders / metrics.TotalOrders * 100 
                    : 0
            };

            return Ok(performance);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }
}

public class SystemStatus
{
    public DateTime Timestamp { get; set; }
    public bool IsHealthy { get; set; }
    public int OrdersLast24Hours { get; set; }
    public int InvoicesLast24Hours { get; set; }
    public int PendingOrders { get; set; }
    public int ActiveOrders { get; set; }
    public int CompletedOrdersLast24Hours { get; set; }
    public int OverdueInvoices { get; set; }
    public double AverageOrderProcessingTime { get; set; }
    public double AveragePaymentTime { get; set; }
    public string SystemLoad { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

public class PerformanceMetrics
{
    public string Period { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalOrders { get; set; }
    public double CompletionRate { get; set; }
    public double CancellationRate { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal TotalRevenue { get; set; }
    public double InvoicePaymentRate { get; set; }
    public double AverageTimeToComplete { get; set; }
    public double AverageTimeToPay { get; set; }
    public double UrgentOrderPercentage { get; set; }
}
