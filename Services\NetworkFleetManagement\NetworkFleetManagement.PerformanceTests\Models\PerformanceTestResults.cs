using NetworkFleetManagement.Infrastructure.Monitoring;
using NetworkFleetManagement.PerformanceTests.Configuration;

namespace NetworkFleetManagement.PerformanceTests.Models;

public class PerformanceTestResults
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public PerformanceTestConfiguration Configuration { get; set; } = new();
    public List<PerformanceTestCategory> TestCategories { get; set; } = new();
    public PerformanceReport? PerformanceReport { get; set; }
    public bool PassedThresholds { get; set; }
    public List<string> ThresholdViolations { get; set; } = new();
    public bool HasCriticalFailure { get; set; }
    public string? CriticalFailureMessage { get; set; }

    public int TotalTests => TestCategories.Sum(c => c.Results.Count);
    public int PassedTests => TestCategories.Sum(c => c.Results.Count(r => r.Success));
    public int FailedTests => TotalTests - PassedTests;
    public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests * 100 : 0;

    public PerformanceTestSummary GetSummary()
    {
        return new PerformanceTestSummary
        {
            TotalDuration = TotalDuration,
            TotalTests = TotalTests,
            PassedTests = PassedTests,
            FailedTests = FailedTests,
            SuccessRate = SuccessRate,
            PassedThresholds = PassedThresholds,
            HasCriticalFailure = HasCriticalFailure,
            CategorySummaries = TestCategories.Select(c => new CategorySummary
            {
                Name = c.Name,
                Duration = c.Duration,
                TestCount = c.Results.Count,
                PassedCount = c.Results.Count(r => r.Success),
                Success = c.Success
            }).ToList()
        };
    }
}

public class PerformanceTestCategory
{
    public string Name { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public bool HasCriticalFailure { get; set; }
    public string? CriticalFailureMessage { get; set; }
    public List<PerformanceTestResult> Results { get; set; } = new();
}

public class PerformanceTestResult
{
    public string TestName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public TimeSpan Duration { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
}

public class PerformanceTestSummary
{
    public TimeSpan TotalDuration { get; set; }
    public int TotalTests { get; set; }
    public int PassedTests { get; set; }
    public int FailedTests { get; set; }
    public double SuccessRate { get; set; }
    public bool PassedThresholds { get; set; }
    public bool HasCriticalFailure { get; set; }
    public List<CategorySummary> CategorySummaries { get; set; } = new();
}

public class CategorySummary
{
    public string Name { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public int TestCount { get; set; }
    public int PassedCount { get; set; }
    public bool Success { get; set; }
    public double SuccessRate => TestCount > 0 ? (double)PassedCount / TestCount * 100 : 0;
}

public static class PerformanceTestResultsExtensions
{
    public static string GenerateReport(this PerformanceTestResults results)
    {
        var report = new System.Text.StringBuilder();
        
        report.AppendLine("=== PERFORMANCE TEST RESULTS ===");
        report.AppendLine($"Execution Time: {results.StartTime:yyyy-MM-dd HH:mm:ss} - {results.EndTime:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"Total Duration: {results.TotalDuration:hh\\:mm\\:ss}");
        report.AppendLine($"Tests: {results.PassedTests}/{results.TotalTests} passed ({results.SuccessRate:F1}%)");
        report.AppendLine($"Thresholds: {(results.PassedThresholds ? "PASSED" : "FAILED")}");
        
        if (results.HasCriticalFailure)
        {
            report.AppendLine($"CRITICAL FAILURE: {results.CriticalFailureMessage}");
        }
        
        report.AppendLine();
        
        foreach (var category in results.TestCategories)
        {
            report.AppendLine($"--- {category.Name} ---");
            report.AppendLine($"Duration: {category.Duration:hh\\:mm\\:ss}");
            report.AppendLine($"Status: {(category.Success ? "PASSED" : "FAILED")}");
            
            if (category.HasCriticalFailure)
            {
                report.AppendLine($"Critical Failure: {category.CriticalFailureMessage}");
            }
            
            foreach (var result in category.Results)
            {
                report.AppendLine($"  {result.TestName}: {(result.Success ? "PASS" : "FAIL")} ({result.Duration.TotalMilliseconds:F0}ms)");
                
                if (!result.Success && !string.IsNullOrEmpty(result.ErrorMessage))
                {
                    report.AppendLine($"    Error: {result.ErrorMessage}");
                }
                
                foreach (var metric in result.Metrics)
                {
                    report.AppendLine($"    {metric.Key}: {metric.Value}");
                }
            }
            
            report.AppendLine();
        }
        
        if (results.ThresholdViolations.Any())
        {
            report.AppendLine("--- THRESHOLD VIOLATIONS ---");
            foreach (var violation in results.ThresholdViolations)
            {
                report.AppendLine($"  {violation}");
            }
            report.AppendLine();
        }
        
        if (results.PerformanceReport != null)
        {
            report.AppendLine("--- PERFORMANCE METRICS ---");
            report.AppendLine($"Total Queries: {results.PerformanceReport.TotalQueries}");
            report.AppendLine($"Average Query Duration: {results.PerformanceReport.AverageQueryDuration:F2}ms");
            report.AppendLine($"Total API Requests: {results.PerformanceReport.TotalApiRequests}");
            report.AppendLine($"Average API Duration: {results.PerformanceReport.AverageApiDuration:F2}ms");
            report.AppendLine($"Cache Hit Rate: {results.PerformanceReport.CacheHitRate:P2}");
            
            if (results.PerformanceReport.SlowQueries.Any())
            {
                report.AppendLine("Slow Queries:");
                foreach (var slowQuery in results.PerformanceReport.SlowQueries)
                {
                    report.AppendLine($"  {slowQuery.Name}: {slowQuery.AverageDuration.TotalMilliseconds:F0}ms ({slowQuery.Count} times)");
                }
            }
        }
        
        return report.ToString();
    }
    
    public static void SaveToFile(this PerformanceTestResults results, string filePath)
    {
        var report = results.GenerateReport();
        File.WriteAllText(filePath, report);
    }
    
    public static async Task SaveToFileAsync(this PerformanceTestResults results, string filePath)
    {
        var report = results.GenerateReport();
        await File.WriteAllTextAsync(filePath, report);
    }
}
