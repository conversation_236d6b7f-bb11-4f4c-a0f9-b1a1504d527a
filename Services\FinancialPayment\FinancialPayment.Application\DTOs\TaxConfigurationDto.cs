using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.DTOs;

public class TaxConfigurationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public TaxConfigurationStatus Status { get; set; }
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // Tax settings
    public bool EnableGstCalculation { get; set; }
    public bool EnableTdsCalculation { get; set; }
    public bool EnableReverseCharge { get; set; }
    public bool RequireHsnCode { get; set; }
    public decimal DefaultGstRate { get; set; }
    public decimal DefaultTdsRate { get; set; }
    public string DefaultCurrency { get; set; } = string.Empty;

    public List<TaxConfigurationHistoryDto> History { get; set; } = new();
    public List<TaxConfigurationRuleDto> Rules { get; set; } = new();
}

public class TaxJurisdictionDto
{
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public LocationType Type { get; set; }
}

public class TaxConfigurationHistoryDto
{
    public Guid Id { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime ModifiedAt { get; set; }
}

public class TaxConfigurationRuleDto
{
    public Guid Id { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class CreateTaxConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateTaxConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
    public int Priority { get; set; }
    public string ModifiedBy { get; set; } = string.Empty;
}

public class UpdateTaxSettingsDto
{
    public bool EnableGstCalculation { get; set; }
    public bool EnableTdsCalculation { get; set; }
    public bool EnableReverseCharge { get; set; }
    public bool RequireHsnCode { get; set; }
    public decimal DefaultGstRate { get; set; }
    public decimal DefaultTdsRate { get; set; }
    public string DefaultCurrency { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
}

public class GstConfigurationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public GstRate GstRate { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public TaxConfigurationStatus Status { get; set; }
    public string? HsnCode { get; set; }
    public MoneyDto MinimumAmount { get; set; } = new();
    public MoneyDto MaximumAmount { get; set; } = new();
    public bool IsReverseChargeApplicable { get; set; }
    public string? ReverseChargeConditions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public List<GstConfigurationHistoryDto> History { get; set; } = new();
}

public class TaxRateDto
{
    public decimal Rate { get; set; }
    public TaxCalculationMethod CalculationMethod { get; set; }
    public DateTime EffectiveFrom { get; set; }
    public DateTime? EffectiveTo { get; set; }
}

public class GstConfigurationHistoryDto
{
    public Guid Id { get; set; }
    public Guid GstConfigurationId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime ModifiedAt { get; set; }
}

public class CreateGstConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public GstRate GstRate { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public CreateMoneyDto MinimumAmount { get; set; } = new();
    public CreateMoneyDto MaximumAmount { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public string? HsnCode { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public string? ReverseChargeConditions { get; set; }
}

public class UpdateGstConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public GstRate GstRate { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public CreateMoneyDto MinimumAmount { get; set; } = new();
    public CreateMoneyDto MaximumAmount { get; set; } = new();
    public string ModifiedBy { get; set; } = string.Empty;
    public string? HsnCode { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public string? ReverseChargeConditions { get; set; }
}

public class TdsConfigurationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TdsSection Section { get; set; }
    public EntityType EntityType { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public TdsThresholdDto Threshold { get; set; } = new();
    public TaxConfigurationStatus Status { get; set; }
    public bool RequiresPan { get; set; }
    public decimal HigherRateWithoutPan { get; set; }
    public string? SpecialConditions { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? ModifiedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public List<TdsConfigurationHistoryDto> History { get; set; } = new();
}

public class TdsThresholdDto
{
    public MoneyDto ThresholdAmount { get; set; } = new();
    public MoneyDto AnnualThresholdAmount { get; set; } = new();
    public EntityType EntityType { get; set; }
    public bool HasPan { get; set; }
}

public class TdsConfigurationHistoryDto
{
    public Guid Id { get; set; }
    public Guid TdsConfigurationId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime ModifiedAt { get; set; }
}

public class CreateTdsConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TdsSection Section { get; set; }
    public EntityType EntityType { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public TdsThresholdDto Threshold { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public bool RequiresPan { get; set; } = true;
    public decimal HigherRateWithoutPan { get; set; }
    public string? SpecialConditions { get; set; }
}

public class UpdateTdsConfigurationDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TdsSection Section { get; set; }
    public EntityType EntityType { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public TdsThresholdDto Threshold { get; set; } = new();
    public string ModifiedBy { get; set; } = string.Empty;
    public bool RequiresPan { get; set; } = true;
    public decimal HigherRateWithoutPan { get; set; }
    public string? SpecialConditions { get; set; }
}
