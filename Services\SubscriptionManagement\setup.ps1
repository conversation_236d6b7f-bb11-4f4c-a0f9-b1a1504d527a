# Subscription Management Service Setup Script
# This script automates the setup process for the Subscription Management Service

param(
    [string]$DatabaseHost = "localhost",
    [string]$DatabasePort = "5432",
    [string]$DatabaseUser = "timescale",
    [string]$DatabasePassword = "timescale",
    [string]$DatabaseName = "TLI_SubscriptionManagement",
    [switch]$SkipDatabase,
    [switch]$RunService
)

Write-Host "🚀 Setting up Subscription Management Service..." -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "dotnet")) {
    Write-Error "❌ .NET 8 SDK is required but not found. Please install .NET 8 SDK."
    exit 1
}

if (-not (Test-Command "psql") -and -not $SkipDatabase) {
    Write-Warning "⚠️ PostgreSQL client (psql) not found. Database setup will be skipped."
    $SkipDatabase = $true
}

Write-Host "✅ Prerequisites check completed." -ForegroundColor Green

# Build the solution
Write-Host "🔨 Building the solution..." -ForegroundColor Yellow
$buildResult = dotnet build SubscriptionManagement.API/SubscriptionManagement.API.csproj
if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Build failed. Please check the build errors above."
    exit 1
}
Write-Host "✅ Build completed successfully." -ForegroundColor Green

# Database setup
if (-not $SkipDatabase) {
    Write-Host "🗄️ Setting up database..." -ForegroundColor Yellow
    
    $connectionString = "Host=$DatabaseHost;Port=$DatabasePort;Database=postgres;User Id=$DatabaseUser;Password=$DatabasePassword"
    
    try {
        # Test database connection
        $env:PGPASSWORD = $DatabasePassword
        $testConnection = psql -h $DatabaseHost -p $DatabasePort -U $DatabaseUser -d postgres -c "SELECT 1;" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database connection successful." -ForegroundColor Green
            
            # Create database if it doesn't exist
            Write-Host "📊 Creating database if it doesn't exist..." -ForegroundColor Yellow
            $createDbResult = psql -h $DatabaseHost -p $DatabasePort -U $DatabaseUser -d postgres -c "CREATE DATABASE `"$DatabaseName`";" 2>&1
            
            # Run database setup script
            Write-Host "📋 Running database setup script..." -ForegroundColor Yellow
            $setupResult = psql -h $DatabaseHost -p $DatabasePort -U $DatabaseUser -d $DatabaseName -f "database-setup.sql"
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Database setup completed successfully." -ForegroundColor Green
            } else {
                Write-Warning "⚠️ Database setup encountered some issues, but continuing..."
            }
        } else {
            Write-Warning "⚠️ Could not connect to database. Please set up the database manually."
        }
    }
    catch {
        Write-Warning "⚠️ Database setup failed: $($_.Exception.Message)"
        Write-Host "📝 Please run the database setup manually using the database-setup.sql script." -ForegroundColor Yellow
    }
    finally {
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "⏭️ Skipping database setup as requested." -ForegroundColor Yellow
}

# Update configuration
Write-Host "⚙️ Configuration setup..." -ForegroundColor Yellow

$appsettingsPath = "SubscriptionManagement.API/appsettings.json"
if (Test-Path $appsettingsPath) {
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    
    # Update connection string
    $connectionString = "Host=$DatabaseHost;Port=$DatabasePort;Database=$DatabaseName;User Id=$DatabaseUser;Password=$DatabasePassword"
    $appsettings.ConnectionStrings.DefaultConnection = $connectionString
    
    # Save updated configuration
    $appsettings | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath
    Write-Host "✅ Configuration updated successfully." -ForegroundColor Green
} else {
    Write-Warning "⚠️ appsettings.json not found. Please configure manually."
}

# Run tests
Write-Host "🧪 Running tests..." -ForegroundColor Yellow
$testResult = dotnet test SubscriptionManagement.Tests/SubscriptionManagement.Tests.csproj --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All tests passed." -ForegroundColor Green
} else {
    Write-Warning "⚠️ Some tests failed. Please review the test results."
}

# Display setup summary
Write-Host ""
Write-Host "🎉 Setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "  • Service Port: http://localhost:5003" -ForegroundColor White
Write-Host "  • Swagger UI: http://localhost:5003" -ForegroundColor White
Write-Host "  • Database: $DatabaseHost:$DatabasePort/$DatabaseName" -ForegroundColor White
Write-Host "  • API Gateway Routes:" -ForegroundColor White
Write-Host "    - Subscriptions: http://localhost:5000/api/subscriptions/*" -ForegroundColor Gray
Write-Host "    - Plans: http://localhost:5000/api/plans/*" -ForegroundColor Gray
Write-Host ""

# Configuration reminders
Write-Host "⚠️ Important Configuration Reminders:" -ForegroundColor Yellow
Write-Host "  1. Update RazorPay credentials in appsettings.json" -ForegroundColor White
Write-Host "  2. Ensure JWT settings match your Identity Service" -ForegroundColor White
Write-Host "  3. Configure RabbitMQ connection if using messaging" -ForegroundColor White
Write-Host "  4. Start Identity Service (port 5001) for authentication" -ForegroundColor White
Write-Host "  5. Start API Gateway (port 5000) for routing" -ForegroundColor White
Write-Host ""

# Run the service if requested
if ($RunService) {
    Write-Host "🚀 Starting the Subscription Management Service..." -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the service." -ForegroundColor Yellow
    Write-Host ""
    
    Set-Location "SubscriptionManagement.API"
    dotnet run
} else {
    Write-Host "🚀 To start the service, run:" -ForegroundColor Green
    Write-Host "  cd SubscriptionManagement.API" -ForegroundColor White
    Write-Host "  dotnet run" -ForegroundColor White
    Write-Host ""
    Write-Host "Or run this script with -RunService flag to start automatically." -ForegroundColor Gray
}

Write-Host "📚 For detailed setup instructions, see SETUP.md" -ForegroundColor Cyan
