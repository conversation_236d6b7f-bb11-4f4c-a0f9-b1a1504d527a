using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using MediatR;

namespace AuditCompliance.Application.Queries
{
    /// <summary>
    /// Get custom report template by ID query
    /// </summary>
    public class GetCustomReportTemplateByIdQuery : IRequest<CustomComplianceReportTemplateDto?>
    {
        public Guid TemplateId { get; set; }
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Search custom report templates query
    /// </summary>
    public class SearchCustomReportTemplatesQuery : IRequest<TemplateSearchResultDto>
    {
        public string? SearchTerm { get; set; }
        public ComplianceStandard? ComplianceStandard { get; set; }
        public ReportTemplateType? TemplateType { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool? IsPublic { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Get retention policy by ID query
    /// </summary>
    public class GetRetentionPolicyByIdQuery : IRequest<RetentionPolicyManagementDto?>
    {
        public Guid PolicyId { get; set; }
    }

    /// <summary>
    /// Search retention policies query
    /// </summary>
    public class SearchRetentionPoliciesQuery : IRequest<PolicySearchResultDto>
    {
        public string? SearchTerm { get; set; }
        public PolicyApprovalStatus? ApprovalStatus { get; set; }
        public bool? IsActive { get; set; }
        public PolicyPriority? Priority { get; set; }
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// Get module registry by ID query
    /// </summary>
    public class GetModuleRegistryByIdQuery : IRequest<ModuleInfoDto?>
    {
        public Guid ModuleId { get; set; }
    }

    /// <summary>
    /// Get module registry by name query
    /// </summary>
    public class GetModuleRegistryByNameQuery : IRequest<ModuleInfoDto?>
    {
        public string ModuleName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Get all module registries query
    /// </summary>
    public class GetAllModuleRegistriesQuery : IRequest<List<ModuleInfoDto>>
    {
        public bool ActiveOnly { get; set; } = false;
    }

    /// <summary>
    /// Get accessible modules for user query
    /// </summary>
    public class GetAccessibleModulesQuery : IRequest<List<ModuleInfoDto>>
    {
        public Guid UserId { get; set; }
    }

    /// <summary>
    /// Get export job status query
    /// </summary>
    public class GetExportJobStatusQuery : IRequest<ExportJobStatusDto?>
    {
        public string JobId { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Get export history query
    /// </summary>
    public class GetExportHistoryQuery : IRequest<List<EnhancedAuditExportResponseDto>>
    {
        public Guid? RequestedBy { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public ExportStatus? Status { get; set; }
        public ExportFormat? Format { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Get supported export formats query
    /// </summary>
    public class GetSupportedExportFormatsQuery : IRequest<List<ExportFormatConfigDto>>
    {
        public bool IncludeAdvancedFormats { get; set; } = true;
        public string? UserRole { get; set; }
    }

    /// <summary>
    /// Get retention status dashboard query
    /// </summary>
    public class GetRetentionStatusDashboardQuery : IRequest<RetentionStatusDashboardDto>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IncludeTrends { get; set; } = true;
        public bool IncludeAlerts { get; set; } = true;
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Get template usage statistics query
    /// </summary>
    public class GetTemplateUsageStatisticsQuery : IRequest<List<TemplateUsageStatisticsDto>>
    {
        public Guid? TemplateId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int TopCount { get; set; } = 10;
    }

    /// <summary>
    /// Get template designer configuration query
    /// </summary>
    public class GetTemplateDesignerConfigQuery : IRequest<TemplateDesignerConfigDto>
    {
        public string? UserRole { get; set; }
    }

    /// <summary>
    /// Get policy impact analysis query
    /// </summary>
    public class GetPolicyImpactAnalysisQuery : IRequest<PolicyImpactAnalysisDto?>
    {
        public Guid PolicyId { get; set; }
        public bool IncludeDetailedBreakdown { get; set; } = true;
    }

    /// <summary>
    /// Get module health status query
    /// </summary>
    public class GetModuleHealthStatusQuery : IRequest<List<ModuleHealthStatusDto>>
    {
        public bool UnhealthyOnly { get; set; } = false;
    }

    /// <summary>
    /// Get audit trail with enhanced filtering query
    /// </summary>
    public class GetEnhancedAuditTrailQuery : IRequest<AuditTrailResultDto>
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public List<string> ModuleNames { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> SeverityLevels { get; set; } = new();
        public List<string> IpAddresses { get; set; } = new();
        public List<string> ComplianceFlags { get; set; } = new();
        public bool IncludeSystemActions { get; set; } = true;
        public bool IncludeSensitiveData { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public Dictionary<string, object> CustomFilters { get; set; } = new();
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
    }

    /// <summary>
    /// Get scheduled exports query
    /// </summary>
    public class GetScheduledExportsQuery : IRequest<List<ScheduledExportDto>>
    {
        public Guid? RequestedBy { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Get retention alerts query
    /// </summary>
    public class GetRetentionAlertsQuery : IRequest<List<RetentionAlertDto>>
    {
        public RetentionAlertType? AlertType { get; set; }
        public RetentionAlertSeverity? Severity { get; set; }
        public bool? IsAcknowledged { get; set; }
        public Guid? PolicyId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Module health status DTO
    /// </summary>
    public class ModuleHealthStatusDto
    {
        public string ModuleName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public DateTime? LastHealthCheck { get; set; }
        public string? LastHealthCheckError { get; set; }
        public bool IsActive { get; set; }
        public string Version { get; set; } = string.Empty;
        public string? HealthCheckEndpoint { get; set; }
    }
}
