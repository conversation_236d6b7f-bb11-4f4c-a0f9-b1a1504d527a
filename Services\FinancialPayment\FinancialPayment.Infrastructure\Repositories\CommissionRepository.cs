using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class CommissionRepository : ICommissionRepository
{
    private readonly FinancialPaymentDbContext _context;

    public CommissionRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<Commission?> GetByIdAsync(Guid id)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Commission?> GetByOrderIdAsync(Guid orderId)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .FirstOrDefaultAsync(c => c.OrderId == orderId);
    }

    public async Task<List<Commission>> GetByBrokerIdAsync(Guid brokerId)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.BrokerId == brokerId)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Commission>> GetByTransportCompanyIdAsync(Guid transportCompanyId)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.TransportCompanyId == transportCompanyId)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Commission>> GetByCarrierIdAsync(Guid carrierId)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.CarrierId == carrierId)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Commission>> GetByStatusAsync(CommissionStatus status)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.Status == status)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Commission>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.CreatedAt >= from && c.CreatedAt <= to)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<Commission> AddAsync(Commission commission)
    {
        _context.Commissions.Add(commission);
        return commission;
    }

    public async Task UpdateAsync(Commission commission)
    {
        _context.Commissions.Update(commission);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var commission = await _context.Commissions.FindAsync(id);
        if (commission != null)
        {
            _context.Commissions.Remove(commission);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.Commissions.AnyAsync(c => c.Id == id);
    }

    public async Task<decimal> GetTotalCommissionAmountAsync(Guid brokerId, DateTime? from = null, DateTime? to = null)
    {
        var query = _context.Commissions
            .Where(c => c.BrokerId == brokerId && c.Status == CommissionStatus.Paid);

        if (from.HasValue)
            query = query.Where(c => c.ProcessedAt >= from.Value);

        if (to.HasValue)
            query = query.Where(c => c.ProcessedAt <= to.Value);

        return await query.SumAsync(c => c.CalculatedAmount.Amount);
    }

    public async Task<List<Commission>> GetPendingCommissionsAsync()
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.Status == CommissionStatus.Calculated || c.Status == CommissionStatus.Approved)
            .OrderBy(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<Commission>> GetDisputedCommissionsAsync()
    {
        return await _context.Commissions
            .Include(c => c.Adjustments)
            .Where(c => c.Status == CommissionStatus.Disputed)
            .OrderBy(c => c.CreatedAt)
            .ToListAsync();
    }
}
