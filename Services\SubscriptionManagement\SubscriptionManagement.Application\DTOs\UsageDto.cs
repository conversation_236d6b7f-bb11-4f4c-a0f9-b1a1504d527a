using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.DTOs;

public class UsageDto
{
    public Guid Id { get; set; }
    public Guid SubscriptionId { get; set; }
    public Guid UserId { get; set; }
    public FeatureType FeatureType { get; set; }
    public int UsageCount { get; set; }
    public DateTime UsageDate { get; set; }
    public string? Metadata { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class UsageSummaryDto
{
    public FeatureType FeatureType { get; set; }
    public int CurrentUsage { get; set; }
    public int? Limit { get; set; }
    public bool IsUnlimited { get; set; }
    public double PercentageUsed { get; set; }
    public int RemainingUsage { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class UsageAnalyticsDto
{
    public Dictionary<string, int> UsageByFeature { get; set; } = new();
    public Dictionary<string, int> UsageByDay { get; set; } = new();
    public Dictionary<string, int> UsageByWeek { get; set; } = new();
    public Dictionary<string, int> UsageByMonth { get; set; } = new();
    public int TotalUsage { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public List<FeatureUsageTrendDto> Trends { get; set; } = new();
}

public class FeatureUsageTrendDto
{
    public FeatureType FeatureType { get; set; }
    public List<DailyUsageDto> DailyUsage { get; set; } = new();
    public double AverageDaily { get; set; }
    public double TrendDirection { get; set; } // Positive = increasing, Negative = decreasing
}

public class DailyUsageDto
{
    public DateTime Date { get; set; }
    public int Usage { get; set; }
}
