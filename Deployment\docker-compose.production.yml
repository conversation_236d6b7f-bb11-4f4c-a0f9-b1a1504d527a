version: '3.8'

# TLI Shipper Portal - Production Docker Compose Configuration
# This configuration sets up all microservices for production deployment
# with proper networking, security, monitoring, and scalability

networks:
  tli-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  rabbitmq-data:
    driver: local
  elasticsearch-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

services:
  # ===== INFRASTRUCTURE SERVICES =====
  
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tli-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: tli_production
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_MULTIPLE_DATABASES: "OrderManagement,TripManagement,FinancialPayment,NetworkFleetManagement,UserManagement,AnalyticsBIService,CommunicationNotification,DataStorage,MobileWorkflow,AuditCompliance"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-databases.sh:/docker-entrypoint-initdb.d/init-databases.sh
    ports:
      - "5432:5432"
    networks:
      - tli-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d tli_production"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tli-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - tli-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: tli-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_DEFAULT_VHOST: tli-production
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
      - ./config/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./config/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - tli-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: tli-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - tli-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== MONITORING SERVICES =====
  
  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: tli-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - tli-network

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: tli-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - tli-network
    depends_on:
      - prometheus

  # ===== MICROSERVICES =====
  
  # API Gateway
  api-gateway:
    image: tli/api-gateway:${VERSION:-latest}
    container_name: tli-api-gateway
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - Services__OrderManagement__BaseUrl=http://order-management:80
      - Services__TripManagement__BaseUrl=http://trip-management:80
      - Services__FinancialPayment__BaseUrl=http://financial-payment:80
      - Services__NetworkFleetManagement__BaseUrl=http://network-fleet:80
      - Services__AnalyticsBIService__BaseUrl=http://analytics-bi:80
      - Services__UserManagement__BaseUrl=http://user-management:80
      - Services__CommunicationNotification__BaseUrl=http://communication:80
      - Services__DataStorage__BaseUrl=http://data-storage:80
      - Services__MobileWorkflow__BaseUrl=http://mobile-workflow:80
      - Services__AuditCompliance__BaseUrl=http://audit-compliance:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - RateLimiting__Enabled=true
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    ports:
      - "80:80"
      - "443:443"
    networks:
      - tli-network
    depends_on:
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Order Management Service
  order-management:
    image: tli/order-management:${VERSION:-latest}
    container_name: tli-order-management
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=OrderManagement;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__TripManagement__BaseUrl=http://trip-management:80
      - Services__FinancialPayment__BaseUrl=http://financial-payment:80
      - Services__NetworkFleetManagement__BaseUrl=http://network-fleet:80
      - Services__CommunicationNotification__BaseUrl=http://communication:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Trip Management Service
  trip-management:
    image: tli/trip-management:${VERSION:-latest}
    container_name: tli-trip-management
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=TripManagement;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__OrderManagement__BaseUrl=http://order-management:80
      - Services__NetworkFleetManagement__BaseUrl=http://network-fleet:80
      - Services__CommunicationNotification__BaseUrl=http://communication:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Financial & Payment Service
  financial-payment:
    image: tli/financial-payment:${VERSION:-latest}
    container_name: tli-financial-payment
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=FinancialPayment;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__OrderManagement__BaseUrl=http://order-management:80
      - Services__TripManagement__BaseUrl=http://trip-management:80
      - PaymentGateway__ApiKey=${PAYMENT_GATEWAY_API_KEY}
      - PaymentGateway__BaseUrl=${PAYMENT_GATEWAY_BASE_URL}
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Network & Fleet Management Service
  network-fleet:
    image: tli/network-fleet-management:${VERSION:-latest}
    container_name: tli-network-fleet
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=NetworkFleetManagement;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__OrderManagement__BaseUrl=http://order-management:80
      - Services__TripManagement__BaseUrl=http://trip-management:80
      - Services__AnalyticsBIService__BaseUrl=http://analytics-bi:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Analytics & BI Service
  analytics-bi:
    image: tli/analytics-bi-service:${VERSION:-latest}
    container_name: tli-analytics-bi
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=AnalyticsBIService;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__DataWarehouse=Host=postgres;Database=TLI_DataWarehouse;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__OrderManagement__BaseUrl=http://order-management:80
      - Services__TripManagement__BaseUrl=http://trip-management:80
      - Services__FinancialPayment__BaseUrl=http://financial-payment:80
      - Services__NetworkFleetManagement__BaseUrl=http://network-fleet:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # User Management Service
  user-management:
    image: tli/user-management:${VERSION:-latest}
    container_name: tli-user-management
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=UserManagement;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - Services__AuditCompliance__BaseUrl=http://audit-compliance:80
      - Services__CommunicationNotification__BaseUrl=http://communication:80
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Communication & Notification Service
  communication:
    image: tli/communication-notification:${VERSION:-latest}
    container_name: tli-communication
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=CommunicationNotification;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD}
      - MessageBroker__ConnectionString=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672/tli-production
      - EmailService__SmtpHost=${SMTP_HOST}
      - EmailService__SmtpPort=${SMTP_PORT}
      - EmailService__Username=${SMTP_USERNAME}
      - EmailService__Password=${SMTP_PASSWORD}
      - SmsService__ApiKey=${SMS_API_KEY}
      - SmsService__BaseUrl=${SMS_BASE_URL}
      - Authentication__JwtBearer__Authority=${JWT_AUTHORITY}
      - Authentication__JwtBearer__Audience=${JWT_AUDIENCE}
      - Monitoring__ApplicationInsights__InstrumentationKey=${APPINSIGHTS_KEY}
    networks:
      - tli-network
    depends_on:
      - postgres
      - redis
      - rabbitmq
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
