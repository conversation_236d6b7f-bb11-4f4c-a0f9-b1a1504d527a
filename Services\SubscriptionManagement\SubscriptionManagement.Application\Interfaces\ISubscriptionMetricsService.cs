using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface ISubscriptionMetricsService
    {
        Task<Dictionary<string, object>> GetSubscriptionMetricsAsync(Guid subscriptionId);
        Task<Dictionary<string, object>> GetUserSubscriptionMetricsAsync(Guid userId);
        Task<Dictionary<string, object>> GetPlanMetricsAsync(Guid planId);
        Task<Dictionary<string, object>> GetOverallMetricsAsync();
        Task RecordSubscriptionEventAsync(string eventType, Guid subscriptionId, Dictionary<string, object>? metadata = null);
        Task RecordUsageMetricAsync(Guid subscriptionId, string metricName, double value, DateTime? timestamp = null);
        Task<Dictionary<string, double>> GetUsageMetricsAsync(Guid subscriptionId, DateTime fromDate, DateTime toDate);
        Task<double> GetCurrentUsageAsync(Guid subscriptionId, string metricName);
        Task<bool> IsUsageLimitExceededAsync(Guid subscriptionId, string metricName);
        Task<Dictionary<string, object>> GetChurnAnalyticsAsync(DateTime fromDate, DateTime toDate);
        Task<Dictionary<string, object>> GetRevenueMetricsAsync(DateTime fromDate, DateTime toDate);
        Task<List<Dictionary<string, object>>> GetSubscriptionTrendsAsync(int days = 30);
    }
}
