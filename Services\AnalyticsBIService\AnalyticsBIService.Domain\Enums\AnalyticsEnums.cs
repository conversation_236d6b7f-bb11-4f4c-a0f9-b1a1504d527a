namespace AnalyticsBIService.Domain.Enums;

/// <summary>
/// User types for role-based analytics
/// </summary>
public enum UserType
{
    Admin = 1,
    TransportCompany = 2,
    Broker = 3,
    Carrier = 4,
    Driver = 5,
    Shipper = 6
}

/// <summary>
/// Types of analytics metrics
/// </summary>
public enum MetricType
{
    Counter = 1,
    Gauge = 2,
    Histogram = 3,
    Summary = 4,
    Rate = 5,
    Percentage = 6,
    Timer = 7,
    ErrorRate = 8,
    OrderCount = 9,
    ResponseTime = 10,
    Revenue = 11,
    TripCount = 12,
    Distance = 13,
    UserActivity = 14,
    EventCount = 15
}

/// <summary>
/// Time periods for analytics aggregation
/// </summary>
public enum TimePeriod
{
    RealTime = 1,
    Hourly = 2,
    Daily = 3,
    Weekly = 4,
    Monthly = 5,
    Quarterly = 6,
    Yearly = 7
}

/// <summary>
/// Dashboard types for different user roles
/// </summary>
public enum DashboardType
{
    AdminPlatformPerformance = 1,
    AdminBusinessIntelligence = 2,
    TransportCompanyPerformance = 3,
    TransportCompanyBusinessIntelligence = 4,
    BrokerOperational = 5,
    BrokerBusinessGrowth = 6,
    CarrierPerformance = 7,
    CarrierGrowthOpportunities = 8,
    ShipperSLAPerformance = 9,
    ShipperBusinessReporting = 10
}

/// <summary>
/// Report types for business intelligence
/// </summary>
public enum ReportType
{
    PlatformUsage = 1,
    RegulatoryCompliance = 2,
    TaxReporting = 3,
    AuditTrail = 4,
    PerformanceAnalysis = 5,
    RevenueAnalysis = 6,
    CustomerAnalysis = 7,
    OperationalEfficiency = 8,
    MarketTrends = 9,
    CustomReport = 10,
    DashboardMetrics = 11,
    TripDelayReport = 12,
    PerformanceInsights = 13,
    RiskMonitoring = 14,
    CarrierPoolHealth = 15,
    Comprehensive = 16
}

/// <summary>
/// KPI categories for performance tracking
/// </summary>
public enum KPICategory
{
    Financial = 1,
    Operational = 2,
    Customer = 3,
    Growth = 4,
    Quality = 5,
    Efficiency = 6,
    Compliance = 7,
    Market = 8,
    Performance = 9 // Missing value referenced in GetCarrierAnalyticsQueryHandler
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Info = 1,
    Low = 2,
    Warning = 3,
    Medium = 4, // Missing value referenced in NotificationService
    High = 5,
    Critical = 6,
    Emergency = 7
}

/// <summary>
/// Data aggregation methods
/// </summary>
public enum AggregationMethod
{
    Sum = 1,
    Average = 2,
    Count = 3,
    Min = 4,
    Max = 5,
    Median = 6,
    Percentile = 7,
    StandardDeviation = 8
}

/// <summary>
/// Export formats for reports
/// </summary>
public enum ExportFormat
{
    PDF = 1,
    Excel = 2,
    CSV = 3,
    JSON = 4,
    XML = 5,
    PowerPoint = 6
}

/// <summary>
/// Analytics event types
/// </summary>
public enum AnalyticsEventType
{
    UserActivity = 1,
    BusinessTransaction = 2,
    SystemPerformance = 3,
    FinancialMetric = 4,
    OperationalMetric = 5,
    QualityMetric = 6,
    ComplianceEvent = 7,
    MarketEvent = 8
}

/// <summary>
/// Trend directions for analytics
/// </summary>
public enum TrendDirection
{
    Increasing = 1,
    Decreasing = 2,
    Stable = 3,
    Volatile = 4
}

/// <summary>
/// Performance status indicators
/// </summary>
public enum PerformanceStatus
{
    Excellent = 1,
    Good = 2,
    Average = 3,
    BelowAverage = 4,
    Poor = 5
}

/// <summary>
/// Data source types for analytics
/// </summary>
public enum DataSourceType
{
    OrderManagement = 1,
    TripManagement = 2,
    UserManagement = 3,
    SubscriptionManagement = 4,
    FinancialPayment = 5,
    NetworkFleetManagement = 6,
    CommunicationNotification = 7,
    External = 8,
    Application = 9
}

/// <summary>
/// Metric categories for analytics
/// </summary>
public enum MetricCategory
{
    Financial = 1,
    Operational = 2,
    Customer = 3,
    Growth = 4,
    Quality = 5,
    Efficiency = 6,
    Compliance = 7,
    Market = 8,
    Performance = 9,
    Usage = 10,
    Orders = 11,
    Trips = 12,
    Users = 13,
    System = 14
}

/// <summary>
/// Widget types for dashboard widgets
/// </summary>
public enum WidgetType
{
    Chart = 1,
    Table = 2,
    KPI = 3,
    Gauge = 4,
    Map = 5,
    Text = 6,
    Image = 7,
    List = 8,
    Calendar = 9,
    Progress = 10
}

/// <summary>
/// Report section types
/// </summary>
public enum ReportSectionType
{
    Summary = 1,
    Chart = 2,
    Table = 3,
    Text = 4,
    Image = 5,
    KPI = 6,
    List = 7,
    Analysis = 8,
    Recommendation = 9,
    Appendix = 10
}

/// <summary>
/// Export status for report exports
/// </summary>
public enum ExportStatus
{
    Pending = 1,
    InProgress = 2,
    Processing = 3,
    Completed = 4,
    Failed = 5,
    Expired = 6,
    NotFound = 7,
    Cancelled = 8
}

/// <summary>
/// Report execution status enumeration
/// </summary>
public enum ReportExecutionStatus
{
    Pending = 1,
    Running = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

/// <summary>
/// Report status enumeration
/// </summary>
public enum ReportStatus
{
    Draft = 1,
    Published = 2,
    Archived = 3,
    Deleted = 4,
    Completed = 5,
    InProgress = 6,
    Pending = 7,
    Generated = 8,
    Failed = 9,
    Scheduled = 10
}

/// <summary>
/// User role enumeration for detailed role-based access
/// </summary>
public enum UserRole
{
    SuperAdmin = 1,
    Admin = 2,
    Manager = 3,
    Operator = 4,
    Viewer = 5,
    TransportManager = 6,
    BrokerManager = 7,
    CarrierManager = 8,
    DriverManager = 9,
    ShipperManager = 10,

    // Missing basic role values referenced in RealTimeAnalyticsService
    TransportCompany = 11,
    Broker = 12,
    Carrier = 13,
    Shipper = 14,
    Driver = 15
}

/// <summary>
/// Comparison operator enumeration for threshold monitoring
/// </summary>
public enum ComparisonOperator
{
    Equal = 1,
    NotEqual = 2,
    GreaterThan = 3,
    GreaterThanOrEqual = 4,
    LessThan = 5,
    LessThanOrEqual = 6,
    Contains = 7,
    StartsWith = 8,
    EndsWith = 9
}

/// <summary>
/// Aggregation types for data aggregation
/// </summary>
public enum AggregationType
{
    Sum = 1,
    Average = 2,
    Count = 3,
    Min = 4,
    Max = 5,
    Median = 6,
    Percentile = 7,
    StandardDeviation = 8,
    First = 9,
    Last = 10
}

/// <summary>
/// Time grouping for analytics aggregation
/// </summary>
public enum TimeGrouping
{
    Minute = 1,
    Hour = 2,
    Day = 3,
    Week = 4,
    Month = 5,
    Quarter = 6,
    Year = 7
}

/// <summary>
/// Entity types for analytics
/// </summary>
public enum EntityType
{
    User = 1,
    TransportCompany = 2,
    Broker = 3,
    Carrier = 4,
    Driver = 5,
    Shipper = 6,
    Vehicle = 7,
    Order = 8,
    Trip = 9,
    Route = 10
}

/// <summary>
/// Trip grouping criteria
/// </summary>
public enum TripGrouping
{
    ByCarrier = 1,
    ByDriver = 2,
    ByVehicle = 3,
    ByRoute = 4,
    ByDate = 5,
    ByStatus = 6,
    ByDistance = 7,
    ByDuration = 8
}

/// <summary>
/// Order grouping criteria
/// </summary>
public enum OrderGrouping
{
    ByShipper = 1,
    ByCarrier = 2,
    ByRoute = 3,
    ByDate = 4,
    ByStatus = 5,
    ByValue = 6,
    ByWeight = 7,
    ByCategory = 8
}

/// <summary>
/// Trend types for trend analysis
/// </summary>
public enum TrendType
{
    Linear = 1,
    Exponential = 2,
    Polynomial = 3,
    MovingAverage = 4,
    Seasonal = 5,
    Cyclical = 6
}

/// <summary>
/// Financial report types
/// </summary>
public enum FinancialReportType
{
    Revenue = 1,
    Expenses = 2,
    Profit = 3,
    CashFlow = 4,
    Balance = 5,
    Commission = 6,
    Tax = 7,
    Billing = 8
}

/// <summary>
/// Comparison types for comparative analysis
/// </summary>
public enum ComparisonType
{
    PeriodOverPeriod = 1,
    YearOverYear = 2,
    MonthOverMonth = 3,
    WeekOverWeek = 4,
    Benchmark = 5,
    PeerComparison = 6
}

/// <summary>
/// Threshold types for monitoring
/// </summary>
public enum ThresholdType
{
    Performance = 1,
    Business = 2,
    System = 3,
    Financial = 4,
    Quality = 5,
    Compliance = 6
}

/// <summary>
/// Alert types for notifications
/// </summary>
public enum AlertType
{
    Performance = 1,
    Business = 2,
    System = 3,
    Financial = 4,
    Quality = 5,
    Compliance = 6,
    Security = 7,
    Operational = 8,
    RiskAlert = 9,
    QualityAlert = 10
}

/// <summary>
/// Alert status enumeration
/// </summary>
public enum AlertStatus
{
    Active = 1,
    Acknowledged = 2,
    InProgress = 3,
    Resolved = 4,
    Closed = 5,
    Suppressed = 6,
    Pending = 7,
    Sent = 8,
    Failed = 9,
    Expired = 10
}


