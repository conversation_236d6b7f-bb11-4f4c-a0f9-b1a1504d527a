using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Razorpay.Api;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;
using System.Security.Cryptography;
using System.Text;

namespace FinancialPayment.Infrastructure.Services;

public class RazorpayGateway : IPaymentGateway
{
    private readonly RazorpayClient _razorpayClient;
    private readonly RazorPaySettings _settings;
    private readonly ILogger<RazorpayGateway> _logger;

    public string GatewayName => "Razorpay";

    public RazorpayGateway(IOptions<RazorPaySettings> settings, ILogger<RazorpayGateway> logger)
    {
        _settings = settings.Value;
        _razorpayClient = new RazorpayClient(_settings.KeyId, _settings.KeySecret);
        _logger = logger;
    }

    public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
    {
        _logger.LogInformation("Processing payment via Razorpay for user {UserId} with amount {Amount}",
            request.UserId, request.Amount);

        try
        {
            var orderOptions = new Dictionary<string, object>
            {
                {"amount", (int)(request.Amount.Amount * 100)}, // Amount in paise
                {"currency", request.Amount.Currency},
                {"receipt", $"user_{request.UserId}_{DateTime.UtcNow:yyyyMMddHHmmss}"},
                {"payment_capture", 1},
                {"notes", request.Metadata}
            };

            var order = _razorpayClient.Order.Create(orderOptions);
            string orderId = order["id"]?.ToString() ?? $"order_{Guid.NewGuid():N}";

            // For demo purposes, simulate successful payment
            await Task.Delay(1000);

            _logger.LogInformation("Payment processed successfully via Razorpay with order ID {OrderId}", orderId);

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = orderId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment completed successfully via Razorpay"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment via Razorpay for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<RefundResult> ProcessRefundAsync(RefundRequest request)
    {
        _logger.LogInformation("Processing refund via Razorpay for transaction {TransactionId}", request.TransactionId);

        try
        {
            var refundOptions = new Dictionary<string, object>
            {
                {"amount", (int)(request.Amount.Amount * 100)}, // Amount in paise
                {"speed", "normal"},
                {"notes", request.Metadata}
            };

            // For demo purposes, simulate successful refund
            await Task.Delay(500);
            var refundId = $"rfnd_{Guid.NewGuid():N}";

            _logger.LogInformation("Refund processed successfully via Razorpay with refund ID {RefundId}", refundId);

            return new RefundResult
            {
                IsSuccess = true,
                RefundId = refundId,
                RefundAmount = request.Amount,
                ProcessedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund via Razorpay for transaction {TransactionId}", request.TransactionId);

            return new RefundResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<PaymentStatus> GetPaymentStatusAsync(string transactionId)
    {
        try
        {
            // For demo purposes, simulate status check
            await Task.Delay(100);
            return PaymentStatus.Completed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment status for transaction {TransactionId}", transactionId);
            return PaymentStatus.Failed;
        }
    }

    public async Task<bool> ValidateWebhookAsync(string payload, string signature)
    {
        try
        {
            var expectedSignature = ComputeHmacSha256(_settings.WebhookSecret, payload);
            var isValid = string.Equals(signature, expectedSignature, StringComparison.OrdinalIgnoreCase);
            
            await Task.CompletedTask;
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Razorpay webhook");
            return false;
        }
    }

    public async Task<PaymentResult> CreatePaymentIntentAsync(PaymentIntentRequest request)
    {
        _logger.LogInformation("Creating payment intent via Razorpay for user {UserId}", request.UserId);

        try
        {
            var orderOptions = new Dictionary<string, object>
            {
                {"amount", (int)(request.Amount.Amount * 100)}, // Amount in paise
                {"currency", request.Amount.Currency},
                {"receipt", $"intent_{request.UserId}_{DateTime.UtcNow:yyyyMMddHHmmss}"},
                {"payment_capture", request.AutoCapture ? 1 : 0},
                {"notes", request.Metadata}
            };

            var order = _razorpayClient.Order.Create(orderOptions);
            string orderId = order["id"]?.ToString() ?? $"order_{Guid.NewGuid():N}";

            await Task.CompletedTask;

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = orderId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment intent created successfully via Razorpay"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent via Razorpay for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    private static string ComputeHmacSha256(string secret, string payload)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
        return Convert.ToHexString(hash).ToLowerInvariant();
    }
}
