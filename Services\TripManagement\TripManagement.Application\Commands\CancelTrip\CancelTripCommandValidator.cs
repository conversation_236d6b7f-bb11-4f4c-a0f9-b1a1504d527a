using FluentValidation;

namespace TripManagement.Application.Commands.CancelTrip;

public class CancelTripCommandValidator : AbstractValidator<CancelTripCommand>
{
    public CancelTripCommandValidator()
    {
        RuleFor(x => x.TripId)
            .NotEmpty()
            .WithMessage("Trip ID is required");

        RuleFor(x => x.Reason)
            .NotEmpty()
            .WithMessage("Cancellation reason is required")
            .MaximumLength(500)
            .WithMessage("Cancellation reason cannot exceed 500 characters");

        RuleFor(x => x.CancelledBy)
            .NotEmpty()
            .WithMessage("Cancelled by user ID is required");

        RuleFor(x => x.AdditionalNotes)
            .MaximumLength(1000)
            .WithMessage("Additional notes cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.AdditionalNotes));
    }
}
