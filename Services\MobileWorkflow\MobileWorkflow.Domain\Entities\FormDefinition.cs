using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class FormDefinition : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // Registration, Survey, Order, Inspection, etc.
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsTemplate { get; private set; }
    public bool IsMultiStep { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Styling { get; private set; }
    public Dictionary<string, object> ValidationRules { get; private set; }
    public Dictionary<string, object> ConditionalLogic { get; private set; }
    public Dictionary<string, object> DataBinding { get; private set; }
    public List<string> RequiredRoles { get; private set; }
    public List<string> Tags { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<FormField> Fields { get; private set; } = new List<FormField>();
    public virtual ICollection<FormStep> Steps { get; private set; } = new List<FormStep>();
    public virtual ICollection<FormSubmission> Submissions { get; private set; } = new List<FormSubmission>();

    private FormDefinition() { } // EF Core

    public FormDefinition(
        string name,
        string displayName,
        string description,
        string category,
        string version,
        string createdBy,
        bool isTemplate = false)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));
        IsTemplate = isTemplate;

        IsActive = true;
        IsMultiStep = false;
        CreatedAt = DateTime.UtcNow;
        Configuration = new Dictionary<string, object>();
        Styling = new Dictionary<string, object>();
        ValidationRules = new Dictionary<string, object>();
        ConditionalLogic = new Dictionary<string, object>();
        DataBinding = new Dictionary<string, object>();
        RequiredRoles = new List<string>();
        Tags = new List<string>();
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();

        AddDomainEvent(new FormDefinitionCreatedEvent(Id, Name, Category, IsTemplate, CreatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateStyling(Dictionary<string, object> styling, string updatedBy)
    {
        Styling = styling ?? throw new ArgumentNullException(nameof(styling));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionStylingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateValidationRules(Dictionary<string, object> validationRules, string updatedBy)
    {
        ValidationRules = validationRules ?? throw new ArgumentNullException(nameof(validationRules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionValidationRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConditionalLogic(Dictionary<string, object> conditionalLogic, string updatedBy)
    {
        ConditionalLogic = conditionalLogic ?? throw new ArgumentNullException(nameof(conditionalLogic));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionConditionalLogicUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateDataBinding(Dictionary<string, object> dataBinding, string updatedBy)
    {
        DataBinding = dataBinding ?? throw new ArgumentNullException(nameof(dataBinding));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionDataBindingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetAsMultiStep()
    {
        IsMultiStep = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionSetAsMultiStepEvent(Id, Name));
    }

    public void SetAsSingleStep()
    {
        IsMultiStep = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionSetAsSingleStepEvent(Id, Name));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionDeactivatedEvent(Id, Name));
    }

    public void AddTag(string tag)
    {
        if (!Tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
        {
            Tags.Add(tag);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new FormDefinitionTagAddedEvent(Id, Name, tag));
        }
    }

    public void RemoveTag(string tag)
    {
        if (Tags.RemoveAll(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new FormDefinitionTagRemovedEvent(Id, Name, tag));
        }
    }

    public void AddRequiredRole(string role)
    {
        if (!RequiredRoles.Contains(role, StringComparer.OrdinalIgnoreCase))
        {
            RequiredRoles.Add(role);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new FormDefinitionRequiredRoleAddedEvent(Id, Name, role));
        }
    }

    public void RemoveRequiredRole(string role)
    {
        if (RequiredRoles.RemoveAll(r => r.Equals(role, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new FormDefinitionRequiredRoleRemovedEvent(Id, Name, role));
        }
    }

    public void UpdateVersion(string newVersion, string updatedBy)
    {
        var oldVersion = Version;
        Version = newVersion ?? throw new ArgumentNullException(nameof(newVersion));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FormDefinitionVersionUpdatedEvent(Id, Name, oldVersion, newVersion, UpdatedBy));
    }

    public bool CanBeAccessedBy(List<string> userRoles)
    {
        if (RequiredRoles.Count == 0)
        {
            return true;
        }

        return RequiredRoles.Any(role => userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
    }

    public bool HasTag(string tag)
    {
        return Tags.Contains(tag, StringComparer.OrdinalIgnoreCase);
    }

    public List<FormField> GetVisibleFields(Dictionary<string, object> context)
    {
        var visibleFields = new List<FormField>();

        foreach (var field in Fields.Where(f => f.IsActive).OrderBy(f => f.Order))
        {
            if (field.EvaluateVisibilityConditions(context))
            {
                visibleFields.Add(field);
            }
        }

        return visibleFields;
    }

    public List<FormStep> GetActiveSteps()
    {
        return Steps.Where(s => s.IsActive).OrderBy(s => s.Order).ToList();
    }

    public FormStep? GetCurrentStep(Dictionary<string, object> context)
    {
        if (!IsMultiStep)
        {
            return Steps.FirstOrDefault();
        }

        // Determine current step based on context
        if (context.TryGetValue("current_step", out var currentStepObj) && currentStepObj is int currentStepIndex)
        {
            return Steps.Where(s => s.IsActive).OrderBy(s => s.Order).Skip(currentStepIndex).FirstOrDefault();
        }

        return Steps.Where(s => s.IsActive).OrderBy(s => s.Order).FirstOrDefault();
    }

    public Dictionary<string, object> ValidateSubmission(Dictionary<string, object> submissionData)
    {
        var validationResults = new Dictionary<string, object>
        {
            ["isValid"] = true,
            ["errors"] = new List<string>(),
            ["fieldErrors"] = new Dictionary<string, List<string>>()
        };

        var errors = (List<string>)validationResults["errors"];
        var fieldErrors = (Dictionary<string, List<string>>)validationResults["fieldErrors"];

        // Validate each field
        foreach (var field in Fields.Where(f => f.IsActive))
        {
            var fieldValidationErrors = field.ValidateValue(submissionData);
            if (fieldValidationErrors.Count > 0)
            {
                fieldErrors[field.Name] = fieldValidationErrors;
                errors.AddRange(fieldValidationErrors.Select(e => $"{field.DisplayName}: {e}"));
            }
        }

        // Apply form-level validation rules
        var formValidationErrors = ApplyFormValidationRules(submissionData);
        errors.AddRange(formValidationErrors);

        validationResults["isValid"] = errors.Count == 0;
        return validationResults;
    }

    private List<string> ApplyFormValidationRules(Dictionary<string, object> submissionData)
    {
        var errors = new List<string>();

        // Apply custom validation rules defined in ValidationRules
        foreach (var rule in ValidationRules)
        {
            if (rule.Value is Dictionary<string, object> ruleDefinition)
            {
                var ruleErrors = EvaluateValidationRule(rule.Key, ruleDefinition, submissionData);
                errors.AddRange(ruleErrors);
            }
        }

        return errors;
    }

    private List<string> EvaluateValidationRule(string ruleName, Dictionary<string, object> ruleDefinition, Dictionary<string, object> submissionData)
    {
        var errors = new List<string>();

        // Simple rule evaluation - can be enhanced with expression engine
        if (ruleDefinition.TryGetValue("type", out var ruleType) && ruleType is string ruleTypeStr)
        {
            switch (ruleTypeStr.ToLowerInvariant())
            {
                case "required_if":
                    errors.AddRange(EvaluateRequiredIfRule(ruleDefinition, submissionData));
                    break;
                case "unique":
                    errors.AddRange(EvaluateUniqueRule(ruleDefinition, submissionData));
                    break;
                case "custom":
                    errors.AddRange(EvaluateCustomRule(ruleDefinition, submissionData));
                    break;
            }
        }

        return errors;
    }

    private List<string> EvaluateRequiredIfRule(Dictionary<string, object> ruleDefinition, Dictionary<string, object> submissionData)
    {
        // Implementation for required_if validation
        return new List<string>();
    }

    private List<string> EvaluateUniqueRule(Dictionary<string, object> ruleDefinition, Dictionary<string, object> submissionData)
    {
        // Implementation for unique validation
        return new List<string>();
    }

    private List<string> EvaluateCustomRule(Dictionary<string, object> ruleDefinition, Dictionary<string, object> submissionData)
    {
        // Implementation for custom validation
        return new List<string>();
    }

    private void SetDefaultConfiguration()
    {
        Configuration["submit_button_text"] = "Submit";
        Configuration["cancel_button_text"] = "Cancel";
        Configuration["show_progress"] = true;
        Configuration["allow_draft"] = true;
        Configuration["auto_save"] = false;
        Configuration["validation_mode"] = "on_submit"; // on_submit, on_blur, real_time

        Styling["theme"] = "default";
        Styling["layout"] = "vertical";
        Styling["field_spacing"] = "medium";
        Styling["button_style"] = "primary";
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


