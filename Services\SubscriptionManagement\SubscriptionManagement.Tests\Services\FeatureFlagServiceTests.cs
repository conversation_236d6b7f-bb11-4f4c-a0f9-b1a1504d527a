using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Services;
using Xunit;

namespace SubscriptionManagement.Tests.Services
{
    public class FeatureFlagServiceTests
    {
        private readonly Mock<IFeatureFlagRepository> _repositoryMock;
        private readonly Mock<ISubscriptionCacheService> _cacheServiceMock;
        private readonly IMemoryCache _memoryCache;
        private readonly Mock<ILogger<FeatureFlagService>> _loggerMock;
        private readonly FeatureFlagService _service;

        public FeatureFlagServiceTests()
        {
            _repositoryMock = new Mock<IFeatureFlagRepository>();
            _cacheServiceMock = new Mock<ISubscriptionCacheService>();
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _loggerMock = new Mock<ILogger<FeatureFlagService>>();

            _service = new FeatureFlagService(
                _repositoryMock.Object,
                _memoryCache,
                _cacheServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task IsEnabledAsync_EnabledFlag_ShouldReturnTrue()
        {
            // Arrange
            var flagKey = "test_feature";
            var userId = Guid.NewGuid();
            var featureFlag = new FeatureFlag(flagKey, "Test Feature", "Test", true, new Dictionary<string, object>());

            _cacheServiceMock.Setup(x => x.GetFeatureFlagAsync(flagKey))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.IsEnabledAsync(flagKey, userId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsEnabledAsync_DisabledFlag_ShouldReturnFalse()
        {
            // Arrange
            var flagKey = "test_feature";
            var userId = Guid.NewGuid();
            var featureFlag = new FeatureFlag(flagKey, "Test Feature", "Test", false, new Dictionary<string, object>());

            _cacheServiceMock.Setup(x => x.GetFeatureFlagAsync(flagKey))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.IsEnabledAsync(flagKey, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsEnabledAsync_FlagNotFound_ShouldReturnFalse()
        {
            // Arrange
            var flagKey = "nonexistent_feature";
            var userId = Guid.NewGuid();

            _cacheServiceMock.Setup(x => x.GetFeatureFlagAsync(flagKey))
                .ReturnsAsync((FeatureFlag?)null);

            _repositoryMock.Setup(x => x.GetByKeyAsync(flagKey))
                .ReturnsAsync((FeatureFlag?)null);

            // Act
            var result = await _service.IsEnabledAsync(flagKey, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetVariantAsync_ABTestFlag_ShouldReturnVariant()
        {
            // Arrange
            var flagKey = "ab_test_feature";
            var userId = Guid.NewGuid();
            var metadata = new Dictionary<string, object>
            {
                ["variants"] = new[] { "control", "treatment" },
                ["rollout_percentage"] = 100
            };
            var featureFlag = new FeatureFlag(flagKey, "AB Test Feature", "Test", true, metadata);
            featureFlag.SetType(FeatureFlagType.ABTest);

            _cacheServiceMock.Setup(x => x.GetFeatureFlagAsync(flagKey))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.GetVariantAsync(flagKey, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Contains(result, new[] { "control", "treatment" });
        }

        [Fact]
        public async Task UpdateRolloutPercentageAsync_ValidFlag_ShouldUpdateSuccessfully()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var newPercentage = 75;
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", true, new Dictionary<string, object>());

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.UpdateRolloutPercentageAsync(flagId, newPercentage);

            // Assert
            Assert.True(result);
            _repositoryMock.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
            _cacheServiceMock.Verify(x => x.RemoveFeatureFlagAsync(featureFlag.Key), Times.Once);
        }

        [Fact]
        public async Task IsUserInTargetAudienceAsync_UserInAudience_ShouldReturnTrue()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var userId = Guid.NewGuid();
            var context = new Dictionary<string, object>
            {
                ["user_type"] = "premium",
                ["region"] = "US"
            };

            var metadata = new Dictionary<string, object>
            {
                ["target_user_types"] = new[] { "premium", "enterprise" },
                ["rollout_percentage"] = 100
            };
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", true, metadata);

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.IsUserInTargetAudienceAsync(flagId, userId, context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetRolloutStatusAsync_ValidFlag_ShouldReturnStatus()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", true, new Dictionary<string, object>());
            featureFlag.UpdateRolloutPercentage(50);

            var usageHistory = new List<FeatureFlagUsage>
            {
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "enabled", DateTime.UtcNow, new Dictionary<string, object>()),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "enabled", DateTime.UtcNow.AddHours(-1), new Dictionary<string, object>())
            };

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            _repositoryMock.Setup(x => x.GetUsageHistoryAsync(flagId, It.IsAny<DateTime>()))
                .ReturnsAsync(usageHistory);

            // Act
            var result = await _service.GetRolloutStatusAsync(flagId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(flagId, result["featureFlagId"]);
            Assert.Equal(50, result["rolloutPercentage"]);
            Assert.Equal(2, result["totalUsersInPeriod"]);
        }

        [Fact]
        public async Task CanIncreaseRolloutAsync_LowErrorRate_ShouldReturnTrue()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var targetPercentage = 75;
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", true, new Dictionary<string, object>());
            featureFlag.UpdateRolloutPercentage(50);

            var usageHistory = new List<FeatureFlagUsage>
            {
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "enabled", DateTime.UtcNow, new Dictionary<string, object>()),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "enabled", DateTime.UtcNow.AddMinutes(-30), new Dictionary<string, object>())
            };

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            _repositoryMock.Setup(x => x.GetUsageHistoryAsync(flagId, It.IsAny<DateTime>()))
                .ReturnsAsync(usageHistory);

            // Act
            var result = await _service.CanIncreaseRolloutAsync(flagId, targetPercentage);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetABTestPerformanceAsync_ABTestFlag_ShouldReturnPerformanceData()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var featureFlag = new FeatureFlag("ab_test_feature", "AB Test Feature", "Test", true, new Dictionary<string, object>());
            featureFlag.SetType(FeatureFlagType.ABTest);

            var usageHistory = new List<FeatureFlagUsage>
            {
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "control", DateTime.UtcNow, new Dictionary<string, object> { ["conversion"] = true }),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "treatment", DateTime.UtcNow, new Dictionary<string, object> { ["conversion"] = true }),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "control", DateTime.UtcNow, new Dictionary<string, object>())
            };

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            _repositoryMock.Setup(x => x.GetUsageHistoryAsync(flagId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(usageHistory);

            // Act
            var result = await _service.GetABTestPerformanceAsync(flagId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(flagId, result["featureFlagId"]);
            Assert.Contains("variants", result.Keys);
            Assert.Equal(2, result["totalUsers"]);
        }

        [Fact]
        public async Task GetWinningVariantAsync_ABTestWithData_ShouldReturnBestVariant()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var featureFlag = new FeatureFlag("ab_test_feature", "AB Test Feature", "Test", true, new Dictionary<string, object>());
            featureFlag.SetType(FeatureFlagType.ABTest);

            // Setup usage history where "treatment" has better conversion rate
            var usageHistory = new List<FeatureFlagUsage>
            {
                // Control: 1 conversion out of 2 users (50%)
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "control", DateTime.UtcNow, new Dictionary<string, object> { ["conversion"] = true }),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "control", DateTime.UtcNow, new Dictionary<string, object>()),
                
                // Treatment: 2 conversions out of 2 users (100%)
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "treatment", DateTime.UtcNow, new Dictionary<string, object> { ["conversion"] = true }),
                new FeatureFlagUsage(flagId, Guid.NewGuid(), "treatment", DateTime.UtcNow, new Dictionary<string, object> { ["conversion"] = true })
            };

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            _repositoryMock.Setup(x => x.GetUsageHistoryAsync(flagId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(usageHistory);

            // Act
            var result = await _service.GetWinningVariantAsync(flagId);

            // Assert
            Assert.NotNull(result);
            // The winning variant should be "treatment" due to higher conversion rate
        }

        [Fact]
        public async Task EnableAsync_ValidFlag_ShouldEnableSuccessfully()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", false, new Dictionary<string, object>());

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.EnableAsync(flagId);

            // Assert
            Assert.True(result);
            _repositoryMock.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
        }

        [Fact]
        public async Task DisableAsync_ValidFlag_ShouldDisableSuccessfully()
        {
            // Arrange
            var flagId = Guid.NewGuid();
            var featureFlag = new FeatureFlag("test_feature", "Test Feature", "Test", true, new Dictionary<string, object>());

            _repositoryMock.Setup(x => x.GetByIdAsync(flagId))
                .ReturnsAsync(featureFlag);

            // Act
            var result = await _service.DisableAsync(flagId);

            // Assert
            Assert.True(result);
            _repositoryMock.Verify(x => x.UpdateAsync(It.IsAny<FeatureFlag>()), Times.Once);
        }

        [Fact]
        public async Task RecordUsageAsync_ValidUsage_ShouldRecordSuccessfully()
        {
            // Arrange
            var flagKey = "test_feature";
            var userId = Guid.NewGuid();
            var variant = "enabled";
            var context = new Dictionary<string, object> { ["source"] = "api" };

            var featureFlag = new FeatureFlag(flagKey, "Test Feature", "Test", true, new Dictionary<string, object>());

            _cacheServiceMock.Setup(x => x.GetFeatureFlagAsync(flagKey))
                .ReturnsAsync(featureFlag);

            // Act
            await _service.RecordUsageAsync(flagKey, userId, variant, context);

            // Assert
            _repositoryMock.Verify(x => x.RecordUsageAsync(It.Is<FeatureFlagUsage>(usage =>
                usage.FeatureFlagId == featureFlag.Id &&
                usage.UserId == userId &&
                usage.Variant == variant
            )), Times.Once);
        }
    }
}
