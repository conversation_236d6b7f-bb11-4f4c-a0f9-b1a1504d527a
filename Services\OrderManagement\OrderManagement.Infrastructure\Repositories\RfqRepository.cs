using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqRepository : IRfqRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<RFQ?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
                .ThenInclude(b => b.Documents)
            .Include(r => r.Documents)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }

    public async Task<RFQ?> GetByRfqNumberAsync(string rfqNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
                .ThenInclude(b => b.Documents)
            .Include(r => r.Documents)
            .FirstOrDefaultAsync(r => r.RfqNumber == rfqNumber, cancellationToken);
    }

    public async Task<IEnumerable<RFQ>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.TransportCompanyId == transportCompanyId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RFQ>> GetPublishedRfqsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.Status == RfqStatus.Published)
            .Where(r => !r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RFQ>> GetRfqsByStatusAsync(RfqStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.Status == status)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RFQ>> GetExpiringRfqsAsync(DateTime beforeDate, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Where(r => r.Status == RfqStatus.Published)
            .Where(r => r.ExpiresAt.HasValue && r.ExpiresAt <= beforeDate)
            .OrderBy(r => r.ExpiresAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<RFQ> Rfqs, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? transportCompanyId = null,
        RfqStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs.AsQueryable();

        if (transportCompanyId.HasValue)
        {
            query = query.Where(r => r.TransportCompanyId == transportCompanyId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(r => r.Status == status.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var rfqs = await query
            .Include(r => r.Bids)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (rfqs, totalCount);
    }

    public async Task AddAsync(RFQ rfq, CancellationToken cancellationToken = default)
    {
        await _context.Rfqs.AddAsync(rfq, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<RFQ>> GetByTransportCompanyAsync(
        Guid transportCompanyId,
        int page,
        int pageSize,
        RfqStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Where(r => r.TransportCompanyId == transportCompanyId);

        if (status.HasValue)
        {
            query = query.Where(r => r.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt <= toDate.Value);
        }

        return await query
            .Include(r => r.Bids)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCountByTransportCompanyAsync(
        Guid transportCompanyId,
        RfqStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Where(r => r.TransportCompanyId == transportCompanyId);

        if (status.HasValue)
        {
            query = query.Where(r => r.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt <= toDate.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<RFQ>> GetPublishedRfqsAsync(
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        LoadType? loadType = null,
        decimal? maxBudget = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Where(r => r.Status == RfqStatus.Published);

        if (excludeExpired)
        {
            query = query.Where(r => !r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.RouteDetails.PreferredPickupDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.RouteDetails.PreferredDeliveryDate <= toDate.Value);
        }

        if (!string.IsNullOrEmpty(pickupCity))
        {
            query = query.Where(r => r.RouteDetails.PickupAddress.City.Contains(pickupCity));
        }

        if (!string.IsNullOrEmpty(deliveryCity))
        {
            query = query.Where(r => r.RouteDetails.DeliveryAddress.City.Contains(deliveryCity));
        }

        if (loadType.HasValue)
        {
            query = query.Where(r => r.LoadDetails.LoadType == loadType.Value);
        }

        if (maxBudget.HasValue)
        {
            query = query.Where(r => r.BudgetRange == null || r.BudgetRange.Amount <= maxBudget.Value);
        }

        return await query
            .Include(r => r.Bids)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetPublishedRfqsCountAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        LoadType? loadType = null,
        decimal? maxBudget = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Where(r => r.Status == RfqStatus.Published);

        if (excludeExpired)
        {
            query = query.Where(r => !r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.RouteDetails.PreferredPickupDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.RouteDetails.PreferredDeliveryDate <= toDate.Value);
        }

        if (!string.IsNullOrEmpty(pickupCity))
        {
            query = query.Where(r => r.RouteDetails.PickupAddress.City.Contains(pickupCity));
        }

        if (!string.IsNullOrEmpty(deliveryCity))
        {
            query = query.Where(r => r.RouteDetails.DeliveryAddress.City.Contains(deliveryCity));
        }

        if (loadType.HasValue)
        {
            query = query.Where(r => r.LoadDetails.LoadType == loadType.Value);
        }

        if (maxBudget.HasValue)
        {
            query = query.Where(r => r.BudgetRange == null || r.BudgetRange.Amount <= maxBudget.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task UpdateAsync(RFQ rfq, CancellationToken cancellationToken = default)
    {
        _context.Rfqs.Update(rfq);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RFQ rfq)
    {
        _context.Rfqs.Update(rfq);
    }

    public async Task<List<RFQ>> GetExpiredActiveRfqsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;

        return await _context.Rfqs
            .Where(r => r.Status == RfqStatus.Published || r.Status == RfqStatus.Draft)
            .Where(r => (r.Timeframe != null && r.Timeframe.ExpiresAt <= now) ||
                       (r.Timeframe == null && r.ExpiresAt.HasValue && r.ExpiresAt <= now))
            .OrderBy(r => r.ExpiresAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RFQ>> GetRfqsApproachingExpirationAsync(int[] hoursBeforeExpiration, CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var rfqs = new List<RFQ>();

        foreach (var hours in hoursBeforeExpiration)
        {
            var notificationTime = now.AddHours(hours);

            var rfqsForThisHour = await _context.Rfqs
                .Where(r => r.Status == RfqStatus.Published)
                .Where(r => (r.Timeframe != null && r.Timeframe.ExpiresAt > now && r.Timeframe.ExpiresAt <= notificationTime) ||
                           (r.Timeframe == null && r.ExpiresAt.HasValue && r.ExpiresAt > now && r.ExpiresAt <= notificationTime))
                .ToListAsync(cancellationToken);

            rfqs.AddRange(rfqsForThisHour);
        }

        return rfqs.Distinct().OrderBy(r => r.ExpiresAt).ToList();
    }

    public async Task<List<RFQ>> GetRfqsExpiringSoonAsync(int hoursBeforeExpiration, CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        var notificationTime = now.AddHours(hoursBeforeExpiration);

        return await _context.Rfqs
            .Where(r => r.Status == RfqStatus.Published)
            .Where(r => (r.Timeframe != null && r.Timeframe.ExpiresAt > now && r.Timeframe.ExpiresAt <= notificationTime) ||
                       (r.Timeframe == null && r.ExpiresAt.HasValue && r.ExpiresAt > now && r.ExpiresAt <= notificationTime))
            .OrderBy(r => r.ExpiresAt)
            .ToListAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var rfq = await _context.Rfqs.FindAsync(new object[] { id }, cancellationToken);
        if (rfq != null)
        {
            _context.Rfqs.Remove(rfq);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<(List<RFQ> Rfqs, int TotalCount)> GetFilteredRfqsAsync(
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<VehicleType>? vehicleTypes = null,
        List<LoadType>? loadTypes = null,
        List<RfqStatus>? statuses = null,
        Guid? transportCompanyId = null,
        Guid? brokerId = null,
        Guid? carrierId = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        string? pickupState = null,
        string? deliveryState = null,
        decimal? minBudget = null,
        decimal? maxBudget = null,
        bool? isUrgent = null,
        bool? hasDocuments = null,
        bool? hasBids = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Include(r => r.Bids)
            .Include(r => r.Documents)
            .AsQueryable();

        // Apply filters
        if (excludeExpired)
        {
            query = query.Where(r => !r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt <= toDate.Value);
        }

        if (vehicleTypes?.Any() == true)
        {
            query = query.Where(r => vehicleTypes.Contains(r.LoadDetails.RequiredVehicleType));
        }

        if (loadTypes?.Any() == true)
        {
            query = query.Where(r => loadTypes.Contains(r.LoadDetails.LoadType));
        }

        if (statuses?.Any() == true)
        {
            query = query.Where(r => statuses.Contains(r.Status));
        }

        if (transportCompanyId.HasValue)
        {
            query = query.Where(r => r.TransportCompanyId == transportCompanyId.Value);
        }

        if (!string.IsNullOrEmpty(pickupCity))
        {
            query = query.Where(r => r.RouteDetails.PickupAddress.City.Contains(pickupCity));
        }

        if (!string.IsNullOrEmpty(deliveryCity))
        {
            query = query.Where(r => r.RouteDetails.DeliveryAddress.City.Contains(deliveryCity));
        }

        if (!string.IsNullOrEmpty(pickupState))
        {
            query = query.Where(r => r.RouteDetails.PickupAddress.State.Contains(pickupState));
        }

        if (!string.IsNullOrEmpty(deliveryState))
        {
            query = query.Where(r => r.RouteDetails.DeliveryAddress.State.Contains(deliveryState));
        }

        if (minBudget.HasValue)
        {
            query = query.Where(r => r.BudgetRange != null && r.BudgetRange.Amount >= minBudget.Value);
        }

        if (maxBudget.HasValue)
        {
            query = query.Where(r => r.BudgetRange != null && r.BudgetRange.Amount <= maxBudget.Value);
        }

        if (isUrgent.HasValue)
        {
            query = query.Where(r => r.IsUrgent == isUrgent.Value);
        }

        if (hasDocuments.HasValue)
        {
            if (hasDocuments.Value)
            {
                query = query.Where(r => r.Documents.Any());
            }
            else
            {
                query = query.Where(r => !r.Documents.Any());
            }
        }

        if (hasBids.HasValue)
        {
            if (hasBids.Value)
            {
                query = query.Where(r => r.Bids.Any());
            }
            else
            {
                query = query.Where(r => !r.Bids.Any());
            }
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var rfqs = await query
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (rfqs, totalCount);
    }

    public async Task<(List<RFQ> Rfqs, int TotalCount)> SearchRfqsAsync(
        string searchTerm,
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<RfqStatus>? statuses = null,
        Guid? transportCompanyId = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Rfqs
            .Include(r => r.Bids)
            .Include(r => r.Documents)
            .AsQueryable();

        // Apply basic filters
        if (excludeExpired)
        {
            query = query.Where(r => !r.ExpiresAt.HasValue || r.ExpiresAt > DateTime.UtcNow);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(r => r.CreatedAt <= toDate.Value);
        }

        if (statuses?.Any() == true)
        {
            query = query.Where(r => statuses.Contains(r.Status));
        }

        if (transportCompanyId.HasValue)
        {
            query = query.Where(r => r.TransportCompanyId == transportCompanyId.Value);
        }

        // Apply search term across multiple fields
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            query = query.Where(r =>
                r.Title.ToLower().Contains(searchTermLower) ||
                r.Description.ToLower().Contains(searchTermLower) ||
                r.RfqNumber.ToLower().Contains(searchTermLower) ||
                r.LoadDetails.Description.ToLower().Contains(searchTermLower) ||
                r.RouteDetails.PickupAddress.City.ToLower().Contains(searchTermLower) ||
                r.RouteDetails.DeliveryAddress.City.ToLower().Contains(searchTermLower) ||
                r.RouteDetails.PickupAddress.State.ToLower().Contains(searchTermLower) ||
                r.RouteDetails.DeliveryAddress.State.ToLower().Contains(searchTermLower) ||
                (r.SpecialInstructions != null && r.SpecialInstructions.ToLower().Contains(searchTermLower)) ||
                (r.ContactPerson != null && r.ContactPerson.ToLower().Contains(searchTermLower)));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var rfqs = await query
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (rfqs, totalCount);
    }

    public async Task<List<RFQ>> GetRfqsWithoutBidsAsync(DateTime publishedBefore, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.Status == RfqStatus.Published)
            .Where(r => r.CreatedAt <= publishedBefore)
            .Where(r => !r.Bids.Any())
            .OrderBy(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RFQ>> GetRfqsWithLowBidCountAsync(int minimumBids, DateTime publishedBefore, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.Status == RfqStatus.Published)
            .Where(r => r.CreatedAt <= publishedBefore)
            .Where(r => r.Bids.Count < minimumBids)
            .OrderBy(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RFQ>> GetRfqsByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Where(r => r.TransportCompanyId == transportCompanyId)
            .ToListAsync(cancellationToken);
    }

    public async Task<RFQ?> GetByIdWithBidsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
                .ThenInclude(b => b.Documents)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }

    public async Task<RFQ?> GetByIdWithNegotiationsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Rfqs
            .Include(r => r.Bids)
            .Include(r => r.Negotiations)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }

    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await _context.SaveChangesAsync(cancellationToken);
    }
}
