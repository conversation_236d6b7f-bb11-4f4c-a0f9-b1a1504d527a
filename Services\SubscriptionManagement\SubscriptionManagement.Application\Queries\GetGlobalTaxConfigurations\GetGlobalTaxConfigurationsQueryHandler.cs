using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetGlobalTaxConfigurations
{
    public class GetGlobalTaxConfigurationsQueryHandler : IRequestHandler<GetGlobalTaxConfigurationsQuery, List<GlobalTaxConfigurationDto>>
    {
        private readonly IGlobalTaxConfigurationRepository _globalTaxConfigurationRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<GetGlobalTaxConfigurationsQueryHandler> _logger;

        public GetGlobalTaxConfigurationsQueryHandler(
            IGlobalTaxConfigurationRepository globalTaxConfigurationRepository,
            IMapper mapper,
            ILogger<GetGlobalTaxConfigurationsQueryHandler> logger)
        {
            _globalTaxConfigurationRepository = globalTaxConfigurationRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<GlobalTaxConfigurationDto>> Handle(GetGlobalTaxConfigurationsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting global tax configurations. ActiveOnly: {ActiveOnly}, Region: {Region}, TaxType: {TaxType}",
                request.ActiveOnly, request.Region, request.TaxType);

            try
            {
                var configurations = request.ActiveOnly 
                    ? await _globalTaxConfigurationRepository.GetActiveAsync()
                    : await _globalTaxConfigurationRepository.GetAllAsync();

                // Filter by region if specified
                if (!string.IsNullOrEmpty(request.Region))
                {
                    var regionConfigurations = await _globalTaxConfigurationRepository.GetByRegionAsync(request.Region);
                    configurations = configurations.Intersect(regionConfigurations).ToList();
                }

                // Filter by tax type if specified
                if (request.TaxType.HasValue)
                {
                    var typeConfigurations = await _globalTaxConfigurationRepository.GetByTaxTypeAsync(request.TaxType.Value);
                    configurations = configurations.Intersect(typeConfigurations).ToList();
                }

                // Filter by effective date if specified
                if (request.EffectiveDate.HasValue)
                {
                    configurations = configurations.Where(c => c.IsEffectiveOn(request.EffectiveDate.Value)).ToList();
                }

                // Apply pagination
                var pagedConfigurations = configurations
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                var result = _mapper.Map<List<GlobalTaxConfigurationDto>>(pagedConfigurations);

                _logger.LogInformation("Successfully retrieved {Count} global tax configurations", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting global tax configurations");
                throw;
            }
        }
    }
}
