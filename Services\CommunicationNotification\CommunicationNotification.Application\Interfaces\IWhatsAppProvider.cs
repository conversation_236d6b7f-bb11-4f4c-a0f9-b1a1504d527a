using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for WhatsApp Business API provider
/// </summary>
public interface IWhatsAppProvider : INotificationProvider
{
    /// <summary>
    /// Send WhatsApp text message
    /// </summary>
    /// <param name="phoneNumber">Recipient phone number (with country code)</param>
    /// <param name="message">Message text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>WhatsApp send result</returns>
    Task<WhatsAppResult> SendTextMessageAsync(
        string phoneNumber,
        string message,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send WhatsApp template message
    /// </summary>
    /// <param name="templateRequest">Template message request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>WhatsApp send result</returns>
    Task<WhatsAppResult> SendTemplateMessageAsync(
        WhatsAppTemplateRequest templateRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send WhatsApp media message (image, document, etc.)
    /// </summary>
    /// <param name="mediaRequest">Media message request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>WhatsApp send result</returns>
    Task<WhatsAppResult> SendMediaMessageAsync(
        WhatsAppMediaRequest mediaRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send WhatsApp location message
    /// </summary>
    /// <param name="locationRequest">Location message request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>WhatsApp send result</returns>
    Task<WhatsAppResult> SendLocationMessageAsync(
        WhatsAppLocationRequest locationRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get WhatsApp message status
    /// </summary>
    /// <param name="messageId">WhatsApp message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message status</returns>
    Task<WhatsAppMessageStatus> GetMessageStatusAsync(
        string messageId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Verify WhatsApp phone number
    /// </summary>
    /// <param name="phoneNumber">Phone number to verify</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if number is valid for WhatsApp</returns>
    Task<bool> VerifyPhoneNumberAsync(
        string phoneNumber,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available message templates
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of available templates</returns>
    Task<List<WhatsAppTemplate>> GetTemplatesAsync(
        CancellationToken cancellationToken = default);
}

/// <summary>
/// WhatsApp template message request
/// </summary>
public class WhatsAppTemplateRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
    public string LanguageCode { get; set; } = "en";
    public List<WhatsAppTemplateParameter>? Parameters { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// WhatsApp media message request
/// </summary>
public class WhatsAppMediaRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public WhatsAppMediaType MediaType { get; set; }
    public string MediaUrl { get; set; } = string.Empty;
    public string? Caption { get; set; }
    public string? FileName { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// WhatsApp location message request
/// </summary>
public class WhatsAppLocationRequest
{
    public string PhoneNumber { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Name { get; set; }
    public string? Address { get; set; }
    public Dictionary<string, string>? Metadata { get; set; }
}

/// <summary>
/// WhatsApp template parameter
/// </summary>
public class WhatsAppTemplateParameter
{
    public string Type { get; set; } = "text";
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// WhatsApp template
/// </summary>
public class WhatsAppTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<WhatsAppTemplateComponent> Components { get; set; } = new();
}

/// <summary>
/// WhatsApp template component
/// </summary>
public class WhatsAppTemplateComponent
{
    public string Type { get; set; } = string.Empty;
    public string? Text { get; set; }
    public List<WhatsAppTemplateParameter>? Parameters { get; set; }
}

/// <summary>
/// WhatsApp send result
/// </summary>
public class WhatsAppResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static WhatsAppResult Success(string messageId)
    {
        return new WhatsAppResult
        {
            IsSuccess = true,
            MessageId = messageId
        };
    }

    public static WhatsAppResult Failure(string errorMessage, string? errorCode = null)
    {
        return new WhatsAppResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// WhatsApp message status
/// </summary>
public class WhatsAppMessageStatus
{
    public string MessageId { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public WhatsAppDeliveryStatus Status { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// WhatsApp media types
/// </summary>
public enum WhatsAppMediaType
{
    Image = 1,
    Document = 2,
    Audio = 3,
    Video = 4,
    Sticker = 5
}

/// <summary>
/// WhatsApp delivery status
/// </summary>
public enum WhatsAppDeliveryStatus
{
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Read = 4,
    Failed = 5,
    Rejected = 6
}
