
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class MobileApp : AggregateRoot
{
    public string Name { get; private set; }
    public string Version { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Web
    public string PackageId { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime ReleaseDate { get; private set; }
    public string MinimumOSVersion { get; private set; }
    public Dictionary<string, object> Features { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public bool SupportsOffline { get; private set; }
    public string DownloadUrl { get; private set; }
    public long FileSize { get; private set; }
    public string Checksum { get; private set; }

    // Navigation properties
    public virtual ICollection<MobileSession> Sessions { get; private set; } = new List<MobileSession>();
    public virtual ICollection<OfflineData> OfflineData { get; private set; } = new List<OfflineData>();

    private MobileApp() { } // EF Core

    public MobileApp(
        string name,
        string version,
        string platform,
        string packageId,
        DateTime releaseDate,
        string minimumOSVersion,
        Dictionary<string, object> features,
        Dictionary<string, object> configuration,
        bool supportsOffline,
        string downloadUrl,
        long fileSize,
        string checksum)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        PackageId = packageId ?? throw new ArgumentNullException(nameof(packageId));
        ReleaseDate = releaseDate;
        MinimumOSVersion = minimumOSVersion ?? throw new ArgumentNullException(nameof(minimumOSVersion));
        Features = features ?? new Dictionary<string, object>();
        Configuration = configuration ?? new Dictionary<string, object>();
        SupportsOffline = supportsOffline;
        DownloadUrl = downloadUrl ?? throw new ArgumentNullException(nameof(downloadUrl));
        FileSize = fileSize;
        Checksum = checksum ?? throw new ArgumentNullException(nameof(checksum));
        IsActive = true;

        AddDomainEvent(new MobileAppCreatedEvent(Id, Name, Version, Platform));
    }

    public void UpdateVersion(string version, string downloadUrl, long fileSize, string checksum, Dictionary<string, object>? features = null)
    {
        Version = version ?? throw new ArgumentNullException(nameof(version));
        DownloadUrl = downloadUrl ?? throw new ArgumentNullException(nameof(downloadUrl));
        FileSize = fileSize;
        Checksum = checksum ?? throw new ArgumentNullException(nameof(checksum));

        if (features != null)
        {
            Features = features;
        }

        AddDomainEvent(new MobileAppVersionUpdatedEvent(Id, Version, Platform));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        AddDomainEvent(new MobileAppConfigurationUpdatedEvent(Id, Platform));
    }

    public void Activate()
    {
        IsActive = true;
        AddDomainEvent(new MobileAppActivatedEvent(Id, Name, Platform));
    }

    public void Deactivate()
    {
        IsActive = false;
        AddDomainEvent(new MobileAppDeactivatedEvent(Id, Name, Platform));
    }
}



