using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class FormSubmission : AggregateRoot
{
    public Guid FormDefinitionId { get; private set; }
    public Guid SubmittedBy { get; private set; }
    public string Status { get; private set; } // Draft, Submitted, Approved, Rejected, Processing
    public Dictionary<string, object> Data { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? SubmittedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Guid? ReviewedBy { get; private set; }
    public DateTime? ReviewedAt { get; private set; }
    public string? ReviewNotes { get; private set; }
    public int CurrentStep { get; private set; }
    public int TotalSteps { get; private set; }
    public Dictionary<string, object> ValidationResults { get; private set; }
    public Dictionary<string, object> ProcessingResults { get; private set; }

    // Navigation properties
    public virtual FormDefinition FormDefinition { get; private set; } = null!;

    private FormSubmission() { } // EF Core

    public FormSubmission(
        Guid formDefinitionId,
        Guid submittedBy,
        Dictionary<string, object> data,
        int totalSteps = 1)
    {
        FormDefinitionId = formDefinitionId;
        SubmittedBy = submittedBy;
        Data = data ?? throw new ArgumentNullException(nameof(data));
        TotalSteps = totalSteps;

        Status = "Draft";
        CurrentStep = 1;
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();
        ValidationResults = new Dictionary<string, object>();
        ProcessingResults = new Dictionary<string, object>();

        AddDomainEvent(new FormSubmissionCreatedEvent(Id, FormDefinitionId, SubmittedBy, Status));
    }

    public void UpdateData(Dictionary<string, object> newData)
    {
        Data = newData ?? throw new ArgumentNullException(nameof(newData));

        AddDomainEvent(new FormSubmissionDataUpdatedEvent(Id, FormDefinitionId, SubmittedBy));
    }

    public void MergeData(Dictionary<string, object> additionalData)
    {
        foreach (var kvp in additionalData)
        {
            Data[kvp.Key] = kvp.Value;
        }

        AddDomainEvent(new FormSubmissionDataMergedEvent(Id, FormDefinitionId, SubmittedBy));
    }

    public void Submit()
    {
        if (Status != "Draft")
            throw new InvalidOperationException($"Cannot submit form with status: {Status}");

        Status = "Submitted";
        SubmittedAt = DateTime.UtcNow;

        AddDomainEvent(new FormSubmissionSubmittedEvent(Id, FormDefinitionId, SubmittedBy, SubmittedAt));
    }

    public void Approve(Guid reviewedBy, string? notes = null)
    {
        if (Status != "Submitted")
            throw new InvalidOperationException($"Cannot approve form with status: {Status}");

        Status = "Approved";
        ReviewedBy = reviewedBy;
        ReviewedAt = DateTime.UtcNow;
        ReviewNotes = notes;
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new FormSubmissionApprovedEvent(Id, FormDefinitionId, SubmittedBy, ReviewedBy, ReviewedAt));
    }

    public void Reject(Guid reviewedBy, string? notes = null)
    {
        if (Status != "Submitted")
            throw new InvalidOperationException($"Cannot reject form with status: {Status}");

        Status = "Rejected";
        ReviewedBy = reviewedBy;
        ReviewedAt = DateTime.UtcNow;
        ReviewNotes = notes;

        AddDomainEvent(new FormSubmissionRejectedEvent(Id, FormDefinitionId, SubmittedBy, ReviewedBy, ReviewedAt));
    }

    public void StartProcessing()
    {
        if (Status != "Submitted" && Status != "Approved")
            throw new InvalidOperationException($"Cannot start processing form with status: {Status}");

        Status = "Processing";

        AddDomainEvent(new FormSubmissionProcessingStartedEvent(Id, FormDefinitionId, SubmittedBy));
    }

    public void CompleteProcessing(Dictionary<string, object> results)
    {
        if (Status != "Processing")
            throw new InvalidOperationException($"Cannot complete processing form with status: {Status}");

        Status = "Approved"; // or "Completed" depending on business logic
        ProcessingResults = results ?? throw new ArgumentNullException(nameof(results));
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new FormSubmissionProcessingCompletedEvent(Id, FormDefinitionId, SubmittedBy, CompletedAt));
    }

    public void MoveToNextStep()
    {
        if (CurrentStep < TotalSteps)
        {
            CurrentStep++;

            AddDomainEvent(new FormSubmissionStepAdvancedEvent(Id, FormDefinitionId, SubmittedBy, CurrentStep, TotalSteps));
        }
    }

    public void MoveToPreviousStep()
    {
        if (CurrentStep > 1)
        {
            CurrentStep--;

            AddDomainEvent(new FormSubmissionStepReversedEvent(Id, FormDefinitionId, SubmittedBy, CurrentStep, TotalSteps));
        }
    }

    public void MoveToStep(int stepNumber)
    {
        if (stepNumber < 1 || stepNumber > TotalSteps)
            throw new ArgumentOutOfRangeException(nameof(stepNumber), "Step number must be between 1 and total steps");

        var oldStep = CurrentStep;
        CurrentStep = stepNumber;

        AddDomainEvent(new FormSubmissionStepChangedEvent(Id, FormDefinitionId, SubmittedBy, oldStep, CurrentStep));
    }

    public void SetValidationResults(Dictionary<string, object> validationResults)
    {
        ValidationResults = validationResults ?? throw new ArgumentNullException(nameof(validationResults));

        AddDomainEvent(new FormSubmissionValidationResultsSetEvent(Id, FormDefinitionId, SubmittedBy));
    }

    public void AddValidationError(string field, string error)
    {
        if (!ValidationResults.ContainsKey("fieldErrors"))
        {
            ValidationResults["fieldErrors"] = new Dictionary<string, List<string>>();
        }

        var fieldErrors = (Dictionary<string, List<string>>)ValidationResults["fieldErrors"];
        if (!fieldErrors.ContainsKey(field))
        {
            fieldErrors[field] = new List<string>();
        }

        fieldErrors[field].Add(error);
        ValidationResults["isValid"] = false;
    }

    public void ClearValidationErrors()
    {
        ValidationResults.Clear();
        ValidationResults["isValid"] = true;
        ValidationResults["errors"] = new List<string>();
        ValidationResults["fieldErrors"] = new Dictionary<string, List<string>>();
    }

    public bool IsValid()
    {
        return ValidationResults.TryGetValue("isValid", out var isValid) && isValid is bool valid && valid;
    }

    public bool IsComplete()
    {
        return CurrentStep >= TotalSteps && (Status == "Submitted" || Status == "Approved" || Status == "Processing");
    }

    public bool IsDraft()
    {
        return Status == "Draft";
    }

    public bool IsSubmitted()
    {
        return Status == "Submitted";
    }

    public bool IsApproved()
    {
        return Status == "Approved";
    }

    public bool IsRejected()
    {
        return Status == "Rejected";
    }

    public bool IsProcessing()
    {
        return Status == "Processing";
    }

    public double GetProgressPercentage()
    {
        return TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;
    }

    public TimeSpan GetSubmissionDuration()
    {
        var endTime = CompletedAt ?? DateTime.UtcNow;
        return endTime - CreatedAt;
    }

    public TimeSpan? GetReviewDuration()
    {
        if (SubmittedAt.HasValue && ReviewedAt.HasValue)
        {
            return ReviewedAt.Value - SubmittedAt.Value;
        }
        return null;
    }

    public T? GetDataValue<T>(string key, T? defaultValue = default)
    {
        if (Data.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public void SetDataValue(string key, object value)
    {
        Data[key] = value;
    }

    public void RemoveDataValue(string key)
    {
        Data.Remove(key);
    }

    public bool HasDataValue(string key)
    {
        return Data.ContainsKey(key);
    }

    public List<string> GetValidationErrors()
    {
        if (ValidationResults.TryGetValue("errors", out var errors) && errors is List<string> errorList)
        {
            return errorList;
        }
        return new List<string>();
    }

    public Dictionary<string, List<string>> GetFieldErrors()
    {
        if (ValidationResults.TryGetValue("fieldErrors", out var fieldErrors) &&
            fieldErrors is Dictionary<string, List<string>> fieldErrorDict)
        {
            return fieldErrorDict;
        }
        return new Dictionary<string, List<string>>();
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public void AddProcessingResult(string key, object value)
    {
        ProcessingResults[key] = value;
    }

    public T? GetProcessingResult<T>(string key, T? defaultValue = default)
    {
        if (ProcessingResults.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public Dictionary<string, object> GetStepData(int stepNumber)
    {
        var stepData = new Dictionary<string, object>();
        var stepPrefix = $"step_{stepNumber}_";

        foreach (var kvp in Data)
        {
            if (kvp.Key.StartsWith(stepPrefix))
            {
                stepData[kvp.Key.Substring(stepPrefix.Length)] = kvp.Value;
            }
        }

        return stepData;
    }

    public void SetStepData(int stepNumber, Dictionary<string, object> stepData)
    {
        var stepPrefix = $"step_{stepNumber}_";

        // Remove existing step data
        var keysToRemove = Data.Keys.Where(k => k.StartsWith(stepPrefix)).ToList();
        foreach (var key in keysToRemove)
        {
            Data.Remove(key);
        }

        // Add new step data
        foreach (var kvp in stepData)
        {
            Data[$"{stepPrefix}{kvp.Key}"] = kvp.Value;
        }
    }
}


