using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Commands.CreateRfq;
using OrderManagement.Domain.Enums;
using OrderManagement.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace OrderManagement.IntegrationTests.Controllers;

public class RfqControllerTests : IntegrationTestBase
{
    public RfqControllerTests(TestWebApplicationFactory<Program> factory) : base(factory)
    {
    }

    [Fact]
    public async Task CreateRfq_WithValidData_ShouldReturnCreatedResult()
    {
        // Arrange
        await SeedDatabaseAsync();

        var command = new CreateRfqCommand
        {
            Title = "Test RFQ",
            Description = "Test RFQ Description",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Test Load",
                LoadType = LoadType.General,
                Weight = new CreateWeightDto { Value = 1000, Unit = WeightUnit.Kg },
                Quantity = 1,
                RequiredVehicleType = VehicleType.Truck
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "123 Pickup St",
                    City = "Pickup City",
                    State = "PC",
                    PostalCode = "12345",
                    Country = "USA"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "456 Delivery Ave",
                    City = "Delivery City",
                    State = "DC",
                    PostalCode = "67890",
                    Country = "USA"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(1),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(3)
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Truck,
                RequiresInsurance = true,
                RequiresTracking = true
            }
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/rfq", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);

        var content = await response.Content.ReadAsStringAsync();
        var rfqId = JsonSerializer.Deserialize<Guid>(content);
        rfqId.Should().NotBeEmpty();

        // Verify in database
        using var context = await GetDbContextAsync();
        var rfq = await context.Rfqs.FirstOrDefaultAsync(r => r.Id == rfqId);
        rfq.Should().NotBeNull();
        rfq!.Title.Should().Be("Test RFQ");
        rfq.Status.Should().Be(RfqStatus.Draft);
    }

    [Fact]
    public async Task CreateRfq_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateRfqCommand
        {
            Title = "", // Invalid: empty title
            Description = "Test Description"
            // Missing required fields
        };

        // Act
        var response = await Client.PostAsJsonAsync("/api/v1/rfq", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetRfq_WithValidId_ShouldReturnRfq()
    {
        // Arrange
        await SeedDatabaseAsync();

        // First create an RFQ
        var createCommand = new CreateRfqCommand
        {
            Title = "Test RFQ for Get",
            Description = "Test Description",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Test Load",
                LoadType = LoadType.General,
                Weight = new CreateWeightDto { Value = 500, Unit = WeightUnit.Kg },
                Quantity = 1,
                RequiredVehicleType = VehicleType.Van
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "123 Test St",
                    City = "Test City",
                    State = "TC",
                    PostalCode = "12345",
                    Country = "USA"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "456 Test Ave",
                    City = "Test City 2",
                    State = "TC2",
                    PostalCode = "67890",
                    Country = "USA"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(1),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(2)
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Van,
                RequiresInsurance = true
            }
        };

        var createResponse = await Client.PostAsJsonAsync("/api/v1/rfq", createCommand);
        var rfqId = JsonSerializer.Deserialize<Guid>(await createResponse.Content.ReadAsStringAsync());

        // Act
        var response = await Client.GetAsync($"/api/v1/rfq/{rfqId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Test RFQ for Get");
        content.Should().Contain("Test Description");
    }

    [Fact]
    public async Task GetRfq_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await Client.GetAsync($"/api/v1/rfq/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task PublishRfq_WithValidId_ShouldReturnOk()
    {
        // Arrange
        await SeedDatabaseAsync();

        // First create an RFQ
        var createCommand = new CreateRfqCommand
        {
            Title = "RFQ to Publish",
            Description = "Test Description",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Test Load",
                LoadType = LoadType.General,
                Weight = new CreateWeightDto { Value = 750, Unit = WeightUnit.Kg },
                Quantity = 1,
                RequiredVehicleType = VehicleType.Truck
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "123 Publish St",
                    City = "Publish City",
                    State = "PC",
                    PostalCode = "12345",
                    Country = "USA"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "456 Publish Ave",
                    City = "Publish City 2",
                    State = "PC2",
                    PostalCode = "67890",
                    Country = "USA"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(1),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(3)
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Truck,
                RequiresInsurance = true,
                RequiresTracking = true
            }
        };

        var createResponse = await Client.PostAsJsonAsync("/api/v1/rfq", createCommand);
        var rfqId = JsonSerializer.Deserialize<Guid>(await createResponse.Content.ReadAsStringAsync());

        // Act
        var response = await Client.PostAsync($"/api/v1/rfq/{rfqId}/publish", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify in database
        using var context = await GetDbContextAsync();
        var rfq = await context.Rfqs.FirstOrDefaultAsync(r => r.Id == rfqId);
        rfq.Should().NotBeNull();
        rfq!.Status.Should().Be(RfqStatus.Published);
    }

    [Fact]
    public async Task PublishRfq_WithInvalidId_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await Client.PostAsync($"/api/v1/rfq/{invalidId}/publish", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
