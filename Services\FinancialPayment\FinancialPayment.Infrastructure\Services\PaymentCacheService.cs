using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class PaymentCacheService : IPaymentCacheService
{
    private readonly IDistributedCacheService _cacheService;
    private readonly ICacheKeyGenerator _keyGenerator;
    private readonly ILogger<PaymentCacheService> _logger;
    private readonly CacheConfiguration _configuration;

    public PaymentCacheService(
        IDistributedCacheService cacheService,
        ICacheKeyGenerator keyGenerator,
        ILogger<PaymentCacheService> logger,
        IOptions<CacheConfiguration> configuration)
    {
        _cacheService = cacheService;
        _keyGenerator = keyGenerator;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<T?> GetPaymentAsync<T>(Guid paymentId) where T : class
    {
        try
        {
            var key = _keyGenerator.GeneratePaymentKey(paymentId);
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment {PaymentId} from cache", paymentId);
            return null;
        }
    }

    public async Task SetPaymentAsync<T>(Guid paymentId, T payment, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GeneratePaymentKey(paymentId);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.PaymentCacheExpiryMinutes);
            await _cacheService.SetAsync(key, payment, effectiveExpiry);
            
            _logger.LogDebug("Payment {PaymentId} cached for {Expiry}", paymentId, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching payment {PaymentId}", paymentId);
        }
    }

    public async Task RemovePaymentAsync(Guid paymentId)
    {
        try
        {
            var key = _keyGenerator.GeneratePaymentKey(paymentId);
            await _cacheService.RemoveAsync(key);
            
            _logger.LogDebug("Payment {PaymentId} removed from cache", paymentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing payment {PaymentId} from cache", paymentId);
        }
    }

    public async Task<T?> GetSubscriptionAsync<T>(Guid subscriptionId) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateSubscriptionKey(subscriptionId);
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription {SubscriptionId} from cache", subscriptionId);
            return null;
        }
    }

    public async Task SetSubscriptionAsync<T>(Guid subscriptionId, T subscription, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateSubscriptionKey(subscriptionId);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.SubscriptionCacheExpiryMinutes);
            await _cacheService.SetAsync(key, subscription, effectiveExpiry);
            
            _logger.LogDebug("Subscription {SubscriptionId} cached for {Expiry}", subscriptionId, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching subscription {SubscriptionId}", subscriptionId);
        }
    }

    public async Task RemoveSubscriptionAsync(Guid subscriptionId)
    {
        try
        {
            var key = _keyGenerator.GenerateSubscriptionKey(subscriptionId);
            await _cacheService.RemoveAsync(key);
            
            _logger.LogDebug("Subscription {SubscriptionId} removed from cache", subscriptionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing subscription {SubscriptionId} from cache", subscriptionId);
        }
    }

    public async Task<T?> GetPlanAsync<T>(Guid planId) where T : class
    {
        try
        {
            var key = _keyGenerator.GeneratePlanKey(planId);
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plan {PlanId} from cache", planId);
            return null;
        }
    }

    public async Task SetPlanAsync<T>(Guid planId, T plan, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GeneratePlanKey(planId);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.PlanCacheExpiryMinutes);
            await _cacheService.SetAsync(key, plan, effectiveExpiry);
            
            _logger.LogDebug("Plan {PlanId} cached for {Expiry}", planId, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching plan {PlanId}", planId);
        }
    }

    public async Task RemovePlanAsync(Guid planId)
    {
        try
        {
            var key = _keyGenerator.GeneratePlanKey(planId);
            await _cacheService.RemoveAsync(key);
            
            _logger.LogDebug("Plan {PlanId} removed from cache", planId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing plan {PlanId} from cache", planId);
        }
    }

    public async Task InvalidatePlansAsync()
    {
        try
        {
            await _cacheService.RemoveByPatternAsync("plan:*");
            _logger.LogInformation("All plans invalidated from cache");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating plans from cache");
        }
    }

    public async Task<T?> GetUserPaymentMethodsAsync<T>(Guid userId) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateUserPaymentMethodsKey(userId);
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user payment methods {UserId} from cache", userId);
            return null;
        }
    }

    public async Task SetUserPaymentMethodsAsync<T>(Guid userId, T paymentMethods, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateUserPaymentMethodsKey(userId);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.DefaultExpiryMinutes);
            await _cacheService.SetAsync(key, paymentMethods, effectiveExpiry);
            
            _logger.LogDebug("User payment methods {UserId} cached for {Expiry}", userId, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching user payment methods {UserId}", userId);
        }
    }

    public async Task RemoveUserPaymentMethodsAsync(Guid userId)
    {
        try
        {
            var key = _keyGenerator.GenerateUserPaymentMethodsKey(userId);
            await _cacheService.RemoveAsync(key);
            
            _logger.LogDebug("User payment methods {UserId} removed from cache", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing user payment methods {UserId} from cache", userId);
        }
    }

    public async Task<T?> GetTaxRulesAsync<T>(string jurisdiction) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateTaxRulesKey(jurisdiction);
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax rules {Jurisdiction} from cache", jurisdiction);
            return null;
        }
    }

    public async Task SetTaxRulesAsync<T>(string jurisdiction, T taxRules, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateTaxRulesKey(jurisdiction);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.TaxRulesCacheExpiryMinutes);
            await _cacheService.SetAsync(key, taxRules, effectiveExpiry);
            
            _logger.LogDebug("Tax rules {Jurisdiction} cached for {Expiry}", jurisdiction, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching tax rules {Jurisdiction}", jurisdiction);
        }
    }

    public async Task RemoveTaxRulesAsync(string jurisdiction)
    {
        try
        {
            var key = _keyGenerator.GenerateTaxRulesKey(jurisdiction);
            await _cacheService.RemoveAsync(key);
            
            _logger.LogDebug("Tax rules {Jurisdiction} removed from cache", jurisdiction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing tax rules {Jurisdiction} from cache", jurisdiction);
        }
    }

    public async Task<T?> GetFraudRulesAsync<T>() where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateFraudRulesKey();
            return await _cacheService.GetAsync<T>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting fraud rules from cache");
            return null;
        }
    }

    public async Task SetFraudRulesAsync<T>(T fraudRules, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var key = _keyGenerator.GenerateFraudRulesKey();
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.FraudRulesCacheExpiryMinutes);
            await _cacheService.SetAsync(key, fraudRules, effectiveExpiry);
            
            _logger.LogDebug("Fraud rules cached for {Expiry}", effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching fraud rules");
        }
    }

    public async Task InvalidateFraudRulesAsync()
    {
        try
        {
            var key = _keyGenerator.GenerateFraudRulesKey();
            await _cacheService.RemoveAsync(key);
            
            _logger.LogInformation("Fraud rules invalidated from cache");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating fraud rules from cache");
        }
    }

    public async Task<T?> GetAnalyticsDataAsync<T>(string key) where T : class
    {
        try
        {
            var cacheKey = _keyGenerator.GenerateAnalyticsKey("data", key);
            return await _cacheService.GetAsync<T>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics data {Key} from cache", key);
            return null;
        }
    }

    public async Task SetAnalyticsDataAsync<T>(string key, T data, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var cacheKey = _keyGenerator.GenerateAnalyticsKey("data", key);
            var effectiveExpiry = expiry ?? TimeSpan.FromMinutes(_configuration.AnalyticsCacheExpiryMinutes);
            await _cacheService.SetAsync(cacheKey, data, effectiveExpiry);
            
            _logger.LogDebug("Analytics data {Key} cached for {Expiry}", key, effectiveExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching analytics data {Key}", key);
        }
    }

    public async Task RemoveAnalyticsDataAsync(string key)
    {
        try
        {
            var cacheKey = _keyGenerator.GenerateAnalyticsKey("data", key);
            await _cacheService.RemoveAsync(cacheKey);
            
            _logger.LogDebug("Analytics data {Key} removed from cache", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing analytics data {Key} from cache", key);
        }
    }
}
