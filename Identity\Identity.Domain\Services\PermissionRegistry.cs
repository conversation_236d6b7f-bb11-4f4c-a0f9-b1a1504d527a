using System;
using System.Collections.Generic;
using System.Linq;
using Identity.Domain.Entities;

namespace Identity.Domain.Services
{
    public static class PermissionRegistry
    {
        private static readonly Dictionary<string, Permission> _systemPermissions = new();

        static PermissionRegistry()
        {
            InitializeSystemPermissions();
        }

        private static void InitializeSystemPermissions()
        {
            // User Management Permissions
            RegisterPermission("user.read", "View Users", "UserManagement", "Users", PermissionType.Read, "user", "read", true, 1);
            RegisterPermission("user.write", "Create/Edit Users", "UserManagement", "Users", PermissionType.Write, "user", "write", true, 2);
            RegisterPermission("user.delete", "Delete Users", "UserManagement", "Users", PermissionType.Delete, "user", "delete", true, 3);
            RegisterPermission("user.approve", "Approve Users", "UserManagement", "Users", PermissionType.Approve, "user", "approve", true, 4);
            RegisterPermission("user.manage", "Manage Users", "UserManagement", "Users", PermissionType.Manage, "user", "manage", true, 5);

            // Role Management Permissions
            RegisterPermission("role.read", "View Roles", "RoleManagement", "Roles", PermissionType.Read, "role", "read", true, 1);
            RegisterPermission("role.write", "Create/Edit Roles", "RoleManagement", "Roles", PermissionType.Write, "role", "write", true, 2);
            RegisterPermission("role.delete", "Delete Roles", "RoleManagement", "Roles", PermissionType.Delete, "role", "delete", true, 3);
            RegisterPermission("role.assign", "Assign Roles", "RoleManagement", "Roles", PermissionType.Manage, "role", "assign", true, 4);

            // Permission Management Permissions
            RegisterPermission("permission.read", "View Permissions", "PermissionManagement", "Permissions", PermissionType.Read, "permission", "read", true, 1);
            RegisterPermission("permission.write", "Create/Edit Permissions", "PermissionManagement", "Permissions", PermissionType.Write, "permission", "write", true, 2);
            RegisterPermission("permission.assign", "Assign Permissions", "PermissionManagement", "Permissions", PermissionType.Manage, "permission", "assign", true, 3);

            // Sub-Admin Management Permissions
            RegisterPermission("subadmin.read", "View Sub-Admins", "SubAdminManagement", "SubAdmins", PermissionType.Read, "subadmin", "read", true, 1);
            RegisterPermission("subadmin.write", "Create/Edit Sub-Admins", "SubAdminManagement", "SubAdmins", PermissionType.Write, "subadmin", "write", true, 2);
            RegisterPermission("subadmin.delete", "Delete Sub-Admins", "SubAdminManagement", "SubAdmins", PermissionType.Delete, "subadmin", "delete", true, 3);
            RegisterPermission("subadmin.activate", "Activate/Deactivate Sub-Admins", "SubAdminManagement", "SubAdmins", PermissionType.Manage, "subadmin", "activate", true, 4);

            // KYC Management Permissions
            RegisterPermission("kyc.read", "View KYC Documents", "KYCManagement", "KYC", PermissionType.Read, "kyc", "read", true, 1);
            RegisterPermission("kyc.review", "Review KYC Documents", "KYCManagement", "KYC", PermissionType.Write, "kyc", "review", true, 2);
            RegisterPermission("kyc.approve", "Approve KYC Documents", "KYCManagement", "KYC", PermissionType.Approve, "kyc", "approve", true, 3);
            RegisterPermission("kyc.reject", "Reject KYC Documents", "KYCManagement", "KYC", PermissionType.Write, "kyc", "reject", true, 4);

            // Subscription Management Permissions
            RegisterPermission("subscription.read", "View Subscriptions", "SubscriptionManagement", "Subscriptions", PermissionType.Read, "subscription", "read", true, 1);
            RegisterPermission("subscription.write", "Manage Subscriptions", "SubscriptionManagement", "Subscriptions", PermissionType.Write, "subscription", "write", true, 2);
            RegisterPermission("subscription.billing", "Manage Billing", "SubscriptionManagement", "Billing", PermissionType.Manage, "subscription", "billing", true, 3);

            // Order Management Permissions
            RegisterPermission("order.read", "View Orders", "OrderManagement", "Orders", PermissionType.Read, "order", "read", true, 1);
            RegisterPermission("order.write", "Manage Orders", "OrderManagement", "Orders", PermissionType.Write, "order", "write", true, 2);
            RegisterPermission("order.approve", "Approve Orders", "OrderManagement", "Orders", PermissionType.Approve, "order", "approve", true, 3);

            // Trip Management Permissions
            RegisterPermission("trip.read", "View Trips", "TripManagement", "Trips", PermissionType.Read, "trip", "read", true, 1);
            RegisterPermission("trip.write", "Manage Trips", "TripManagement", "Trips", PermissionType.Write, "trip", "write", true, 2);
            RegisterPermission("trip.assign", "Assign Trips", "TripManagement", "Trips", PermissionType.Manage, "trip", "assign", true, 3);

            // Financial Management Permissions
            RegisterPermission("finance.read", "View Financial Data", "FinancialManagement", "Finance", PermissionType.Read, "finance", "read", true, 1);
            RegisterPermission("finance.write", "Manage Financial Data", "FinancialManagement", "Finance", PermissionType.Write, "finance", "write", true, 2);
            RegisterPermission("finance.approve", "Approve Financial Transactions", "FinancialManagement", "Finance", PermissionType.Approve, "finance", "approve", true, 3);

            // Analytics Permissions
            RegisterPermission("analytics.read", "View Analytics", "Analytics", "Reports", PermissionType.Read, "analytics", "read", true, 1);
            RegisterPermission("analytics.export", "Export Analytics", "Analytics", "Reports", PermissionType.Write, "analytics", "export", true, 2);

            // System Administration Permissions
            RegisterPermission("system.admin", "System Administration", "SystemAdmin", "System", PermissionType.Admin, "system", "admin", true, 10);
            RegisterPermission("system.config", "System Configuration", "SystemAdmin", "System", PermissionType.Manage, "system", "config", true, 9);
            RegisterPermission("system.audit", "View Audit Logs", "SystemAdmin", "Audit", PermissionType.Read, "system", "audit", true, 8);

            // Emergency Access Permissions
            RegisterPermission("emergency.access", "Emergency Access", "Emergency", "System", PermissionType.Admin, "emergency", "access", true, 100);
        }

        private static void RegisterPermission(
            string name, 
            string description, 
            string module, 
            string category, 
            PermissionType type,
            string resource, 
            string action, 
            bool isSystem, 
            int priority)
        {
            var permission = new Permission(name, description, module, category, type, resource, action, isSystem, priority);
            _systemPermissions[name] = permission;
        }

        public static Permission GetPermission(string name)
        {
            return _systemPermissions.TryGetValue(name, out var permission) ? permission : null;
        }

        public static IEnumerable<Permission> GetAllPermissions()
        {
            return _systemPermissions.Values.OrderBy(p => p.Module).ThenBy(p => p.Priority);
        }

        public static IEnumerable<Permission> GetPermissionsByModule(string module)
        {
            return _systemPermissions.Values
                .Where(p => p.Module.Equals(module, StringComparison.OrdinalIgnoreCase))
                .OrderBy(p => p.Priority);
        }

        public static IEnumerable<Permission> GetPermissionsByCategory(string category)
        {
            return _systemPermissions.Values
                .Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                .OrderBy(p => p.Module).ThenBy(p => p.Priority);
        }

        public static IEnumerable<Permission> GetPermissionsByType(PermissionType type)
        {
            return _systemPermissions.Values
                .Where(p => p.Type == type)
                .OrderBy(p => p.Module).ThenBy(p => p.Priority);
        }

        public static IEnumerable<string> GetAllModules()
        {
            return _systemPermissions.Values
                .Select(p => p.Module)
                .Distinct()
                .OrderBy(m => m);
        }

        public static IEnumerable<string> GetAllCategories()
        {
            return _systemPermissions.Values
                .Select(p => p.Category)
                .Distinct()
                .OrderBy(c => c);
        }

        public static bool PermissionExists(string name)
        {
            return _systemPermissions.ContainsKey(name);
        }

        public static bool HasPermission(IEnumerable<Permission> userPermissions, string module, string resource, string action)
        {
            return userPermissions.Any(p => p.Matches(module, resource, action));
        }

        public static Dictionary<string, List<Permission>> GetPermissionsByModuleGrouped()
        {
            return _systemPermissions.Values
                .GroupBy(p => p.Module)
                .ToDictionary(g => g.Key, g => g.OrderBy(p => p.Priority).ToList());
        }
    }
}
