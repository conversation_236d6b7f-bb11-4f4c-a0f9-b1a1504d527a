using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Services;

public class FinancialDataAggregator : IFinancialDataAggregator
{
    private readonly FinancialPaymentDbContext _context;
    private readonly ILogger<FinancialDataAggregator> _logger;

    public FinancialDataAggregator(FinancialPaymentDbContext context, ILogger<FinancialDataAggregator> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<FinancialMetrics> AggregateFinancialMetricsAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        _logger.LogDebug("Aggregating financial metrics from {FromDate} to {ToDate}", fromDate, toDate);

        try
        {
            // Get escrow transactions as primary revenue source
            var escrowQuery = _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate);

            // Apply filters
            if (filter != null)
            {
                if (filter.MinAmount.HasValue)
                    escrowQuery = escrowQuery.Where(et => et.Amount.Amount >= filter.MinAmount.Value);
                
                if (filter.MaxAmount.HasValue)
                    escrowQuery = escrowQuery.Where(et => et.Amount.Amount <= filter.MaxAmount.Value);
                
                if (filter.Currencies.Any())
                    escrowQuery = escrowQuery.Where(et => filter.Currencies.Contains(et.Amount.Currency));
            }

            var escrowTransactions = await escrowQuery.ToListAsync();

            // Get commissions
            var commissionQuery = _context.Commissions
                .Where(c => c.CreatedAt >= fromDate && c.CreatedAt <= toDate);
            var commissions = await commissionQuery.ToListAsync();

            // Get tax calculations
            var taxQuery = _context.TaxCalculations
                .Where(tc => tc.CalculatedAt >= fromDate && tc.CalculatedAt <= toDate);
            var taxes = await taxQuery.ToListAsync();

            // Calculate metrics
            var totalRevenue = escrowTransactions
                .Where(et => et.Status == EscrowTransactionStatus.Completed)
                .Aggregate(Money.Zero("INR"), (sum, et) => sum + et.Amount);

            var totalCommissions = commissions
                .Where(c => c.Status == CommissionStatus.Paid)
                .Aggregate(Money.Zero("INR"), (sum, c) => sum + c.Amount);

            var totalTaxes = taxes
                .Aggregate(Money.Zero("INR"), (sum, tc) => sum + tc.TotalTaxAmount);

            // Calculate expenses (simplified)
            var paymentProcessingFees = new Money(totalRevenue.Amount * 0.029m, "INR"); // 2.9% processing fee
            var operationalExpenses = new Money(totalRevenue.Amount * 0.05m, "INR"); // 5% operational costs
            var totalExpenses = paymentProcessingFees + operationalExpenses;

            var grossProfit = totalRevenue - paymentProcessingFees;
            var netIncome = totalRevenue - totalExpenses;

            var totalTransactionCount = escrowTransactions.Count;
            var averageTransactionValue = totalTransactionCount > 0 
                ? new Money(totalRevenue.Amount / totalTransactionCount, "INR")
                : Money.Zero("INR");

            // Calculate refunds and chargebacks (simplified)
            var refundedTransactions = escrowTransactions.Where(et => et.Status == EscrowTransactionStatus.Refunded);
            var totalRefunds = refundedTransactions.Aggregate(Money.Zero("INR"), (sum, et) => sum + et.Amount);
            var refundRate = totalTransactionCount > 0 ? (decimal)refundedTransactions.Count() / totalTransactionCount * 100 : 0;

            // For demo purposes, assume 0.1% chargeback rate
            var totalChargebacks = new Money(totalRevenue.Amount * 0.001m, "INR");
            var chargebackRate = 0.1m;

            return new FinancialMetrics
            {
                TotalRevenue = totalRevenue,
                TotalExpenses = totalExpenses,
                NetIncome = netIncome,
                GrossProfit = grossProfit,
                GrossProfitMargin = totalRevenue.Amount > 0 ? (grossProfit.Amount / totalRevenue.Amount) * 100 : 0,
                NetProfitMargin = totalRevenue.Amount > 0 ? (netIncome.Amount / totalRevenue.Amount) * 100 : 0,
                TotalTransactionVolume = totalRevenue,
                TotalTransactionCount = totalTransactionCount,
                AverageTransactionValue = averageTransactionValue,
                TotalCommissions = totalCommissions,
                TotalTaxes = totalTaxes,
                TotalRefunds = totalRefunds,
                TotalChargebacks = totalChargebacks,
                RefundRate = refundRate,
                ChargebackRate = chargebackRate
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating financial metrics");
            throw;
        }
    }

    public async Task<RevenueBreakdown> AggregateRevenueDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate &&
                           et.Status == EscrowTransactionStatus.Completed)
                .ToListAsync();

            // For demo purposes, create mock payment method breakdown
            var byPaymentMethod = new Dictionary<string, Money>
            {
                { "Credit Card", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.45m, "INR") },
                { "Debit Card", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.25m, "INR") },
                { "Net Banking", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.15m, "INR") },
                { "UPI", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.10m, "INR") },
                { "Wallet", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.05m, "INR") }
            };

            var byGateway = new Dictionary<string, Money>
            {
                { "Razorpay", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.60m, "INR") },
                { "Stripe", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.25m, "INR") },
                { "PayPal", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.10m, "INR") },
                { "Square", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.05m, "INR") }
            };

            var byRegion = new Dictionary<string, Money>
            {
                { "North India", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.35m, "INR") },
                { "South India", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.30m, "INR") },
                { "West India", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.25m, "INR") },
                { "East India", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.10m, "INR") }
            };

            // Daily breakdown
            var dailyBreakdown = escrowTransactions
                .GroupBy(et => et.CreatedAt.Date)
                .Select(g => new DailyRevenue
                {
                    Date = g.Key,
                    Revenue = g.Aggregate(Money.Zero("INR"), (sum, et) => sum + et.Amount),
                    TransactionCount = g.Count(),
                    Commissions = new Money(g.Sum(et => et.Amount.Amount) * 0.02m, "INR"), // 2% commission
                    Taxes = new Money(g.Sum(et => et.Amount.Amount) * 0.18m, "INR") // 18% tax
                })
                .OrderBy(dr => dr.Date)
                .ToList();

            // Monthly breakdown
            var monthlyBreakdown = escrowTransactions
                .GroupBy(et => new { et.CreatedAt.Year, et.CreatedAt.Month })
                .Select(g => new MonthlyRevenue
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    Revenue = g.Aggregate(Money.Zero("INR"), (sum, et) => sum + et.Amount),
                    TransactionCount = g.Count(),
                    Commissions = new Money(g.Sum(et => et.Amount.Amount) * 0.02m, "INR"),
                    Taxes = new Money(g.Sum(et => et.Amount.Amount) * 0.18m, "INR"),
                    GrowthRate = 0 // Would need historical data to calculate
                })
                .OrderBy(mr => mr.Year).ThenBy(mr => mr.Month)
                .ToList();

            return new RevenueBreakdown
            {
                ByPaymentMethod = byPaymentMethod,
                ByGateway = byGateway,
                ByRegion = byRegion,
                ByCustomerSegment = new Dictionary<string, Money>
                {
                    { "Enterprise", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.40m, "INR") },
                    { "SMB", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.35m, "INR") },
                    { "Individual", new Money(escrowTransactions.Sum(et => et.Amount.Amount) * 0.25m, "INR") }
                },
                DailyBreakdown = dailyBreakdown,
                MonthlyBreakdown = monthlyBreakdown
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating revenue data");
            throw;
        }
    }

    public async Task<ExpenseBreakdown> AggregateExpenseDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate &&
                           et.Status == EscrowTransactionStatus.Completed)
                .ToListAsync();

            var totalRevenue = escrowTransactions.Sum(et => et.Amount.Amount);

            // Calculate various expense categories
            var paymentProcessingFees = new Money(totalRevenue * 0.029m, "INR"); // 2.9%
            var gatewayFees = new Money(totalRevenue * 0.015m, "INR"); // 1.5%
            var operationalExpenses = new Money(totalRevenue * 0.03m, "INR"); // 3%
            var technologyExpenses = new Money(totalRevenue * 0.01m, "INR"); // 1%
            var complianceExpenses = new Money(totalRevenue * 0.005m, "INR"); // 0.5%
            var marketingExpenses = new Money(totalRevenue * 0.02m, "INR"); // 2%

            return new ExpenseBreakdown
            {
                PaymentProcessingFees = paymentProcessingFees,
                GatewayFees = gatewayFees,
                OperationalExpenses = operationalExpenses,
                TechnologyExpenses = technologyExpenses,
                ComplianceExpenses = complianceExpenses,
                MarketingExpenses = marketingExpenses,
                CustomExpenseCategories = new Dictionary<string, Money>
                {
                    { "Customer Support", new Money(totalRevenue * 0.01m, "INR") },
                    { "Legal & Professional", new Money(totalRevenue * 0.005m, "INR") },
                    { "Infrastructure", new Money(totalRevenue * 0.008m, "INR") }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating expense data");
            throw;
        }
    }

    public async Task<List<CashFlowItem>> AggregateCashFlowDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null)
    {
        try
        {
            var cashFlowItems = new List<CashFlowItem>();

            // Get revenue from escrow transactions
            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate &&
                           et.Status == EscrowTransactionStatus.Completed)
                .ToListAsync();

            foreach (var transaction in escrowTransactions)
            {
                cashFlowItems.Add(new CashFlowItem
                {
                    Description = $"Revenue from transaction {transaction.TransactionId}",
                    Amount = transaction.Amount,
                    Category = "Operating",
                    Date = transaction.CreatedAt
                });
            }

            // Add expense items (simplified)
            var totalRevenue = escrowTransactions.Sum(et => et.Amount.Amount);
            
            cashFlowItems.Add(new CashFlowItem
            {
                Description = "Payment Processing Fees",
                Amount = new Money(-totalRevenue * 0.029m, "INR"),
                Category = "Operating",
                Date = toDate
            });

            cashFlowItems.Add(new CashFlowItem
            {
                Description = "Operational Expenses",
                Amount = new Money(-totalRevenue * 0.03m, "INR"),
                Category = "Operating",
                Date = toDate
            });

            return cashFlowItems.OrderBy(cf => cf.Date).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating cash flow data");
            throw;
        }
    }

    public async Task<List<FinancialTrend>> AggregateTrendDataAsync(DateTime fromDate, DateTime toDate, string metricType, FinancialReportFilter? filter = null)
    {
        try
        {
            var escrowTransactions = await _context.EscrowTransactions
                .Where(et => et.CreatedAt >= fromDate && et.CreatedAt <= toDate)
                .ToListAsync();

            var trends = new List<FinancialTrend>();

            switch (metricType.ToLowerInvariant())
            {
                case "revenue":
                    trends = escrowTransactions
                        .Where(et => et.Status == EscrowTransactionStatus.Completed)
                        .GroupBy(et => et.CreatedAt.Date)
                        .Select(g => new FinancialTrend
                        {
                            Date = g.Key,
                            MetricType = "revenue",
                            Value = g.Sum(et => et.Amount.Amount),
                            Label = g.Key.ToString("MMM dd")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                case "transaction_count":
                    trends = escrowTransactions
                        .GroupBy(et => et.CreatedAt.Date)
                        .Select(g => new FinancialTrend
                        {
                            Date = g.Key,
                            MetricType = "transaction_count",
                            Value = g.Count(),
                            Label = g.Key.ToString("MMM dd")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                case "average_transaction_value":
                    trends = escrowTransactions
                        .GroupBy(et => et.CreatedAt.Date)
                        .Select(g => new FinancialTrend
                        {
                            Date = g.Key,
                            MetricType = "average_transaction_value",
                            Value = g.Count() > 0 ? g.Average(et => et.Amount.Amount) : 0,
                            Label = g.Key.ToString("MMM dd")
                        })
                        .OrderBy(t => t.Date)
                        .ToList();
                    break;

                default:
                    _logger.LogWarning("Unknown metric type: {MetricType}", metricType);
                    break;
            }

            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error aggregating trend data for metric {MetricType}", metricType);
            throw;
        }
    }
}
