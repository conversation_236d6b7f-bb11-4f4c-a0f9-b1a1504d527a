
using Shared.Domain.Common;


namespace TripManagement.Domain.Entities;

public class DriverLocationPreferencesEntity : AggregateRoot
{
    public Guid DriverId { get; private set; }
    public bool LocationSharingEnabled { get; private set; }
    public bool ManualToggleAllowed { get; private set; }
    public int LocationUpdateIntervalSeconds { get; private set; }
    public bool ShowGpsTimestamp { get; private set; }
    public bool HighAccuracyMode { get; private set; }
    public bool BackgroundLocationEnabled { get; private set; }
    public bool GeofenceAlertsEnabled { get; private set; }
    public double LocationAccuracyThresholdMeters { get; private set; }
    public DateTime LastLocationUpdate { get; private set; }
    public string? LastKnownLocation { get; private set; }
    public double? LastLatitude { get; private set; }
    public double? LastLongitude { get; private set; }
    public double? LastAccuracy { get; private set; }
    public DateTime? LastGpsTimestamp { get; private set; }
    public Dictionary<string, object> AdditionalSettings { get; private set; } = new();

    // Parameterless constructor for EF Core
    private DriverLocationPreferencesEntity() { }

    public DriverLocationPreferencesEntity(
        Guid driverId,
        bool locationSharingEnabled = true,
        bool manualToggleAllowed = true,
        int locationUpdateIntervalSeconds = 30,
        bool showGpsTimestamp = true,
        bool highAccuracyMode = false,
        bool backgroundLocationEnabled = true,
        bool geofenceAlertsEnabled = true,
        double locationAccuracyThresholdMeters = 10.0,
        Dictionary<string, object>? additionalSettings = null)
    {
        if (driverId == Guid.Empty)
            throw new ArgumentException("Driver ID cannot be empty", nameof(driverId));

        if (locationUpdateIntervalSeconds < 5 || locationUpdateIntervalSeconds > 300)
            throw new ArgumentException("Location update interval must be between 5 and 300 seconds", nameof(locationUpdateIntervalSeconds));

        if (locationAccuracyThresholdMeters < 1 || locationAccuracyThresholdMeters > 100)
            throw new ArgumentException("Location accuracy threshold must be between 1 and 100 meters", nameof(locationAccuracyThresholdMeters));

        DriverId = driverId;
        LocationSharingEnabled = locationSharingEnabled;
        ManualToggleAllowed = manualToggleAllowed;
        LocationUpdateIntervalSeconds = locationUpdateIntervalSeconds;
        ShowGpsTimestamp = showGpsTimestamp;
        HighAccuracyMode = highAccuracyMode;
        BackgroundLocationEnabled = backgroundLocationEnabled;
        GeofenceAlertsEnabled = geofenceAlertsEnabled;
        LocationAccuracyThresholdMeters = locationAccuracyThresholdMeters;
        LastLocationUpdate = DateTime.UtcNow;
        AdditionalSettings = additionalSettings ?? new Dictionary<string, object>();
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DriverLocationPreferencesCreatedEvent(DriverId, LocationSharingEnabled));
    }

    public void UpdatePreferences(
        bool locationSharingEnabled,
        bool manualToggleAllowed,
        int locationUpdateIntervalSeconds,
        bool showGpsTimestamp,
        bool highAccuracyMode,
        bool backgroundLocationEnabled,
        bool geofenceAlertsEnabled,
        double locationAccuracyThresholdMeters,
        Dictionary<string, object>? additionalSettings = null)
    {
        if (locationUpdateIntervalSeconds < 5 || locationUpdateIntervalSeconds > 300)
            throw new ArgumentException("Location update interval must be between 5 and 300 seconds", nameof(locationUpdateIntervalSeconds));

        if (locationAccuracyThresholdMeters < 1 || locationAccuracyThresholdMeters > 100)
            throw new ArgumentException("Location accuracy threshold must be between 1 and 100 meters", nameof(locationAccuracyThresholdMeters));

        var oldLocationSharingEnabled = LocationSharingEnabled;

        LocationSharingEnabled = locationSharingEnabled;
        ManualToggleAllowed = manualToggleAllowed;
        LocationUpdateIntervalSeconds = locationUpdateIntervalSeconds;
        ShowGpsTimestamp = showGpsTimestamp;
        HighAccuracyMode = highAccuracyMode;
        BackgroundLocationEnabled = backgroundLocationEnabled;
        GeofenceAlertsEnabled = geofenceAlertsEnabled;
        LocationAccuracyThresholdMeters = locationAccuracyThresholdMeters;
        UpdatedAt = DateTime.UtcNow;

        if (additionalSettings != null)
        {
            AdditionalSettings = additionalSettings;
        }

        // Raise event if location sharing status changed
        if (oldLocationSharingEnabled != locationSharingEnabled)
        {
            AddDomainEvent(new DriverLocationSharingToggedEvent(DriverId, locationSharingEnabled, UpdatedAt ?? DateTime.UtcNow));
        }

        AddDomainEvent(new DriverLocationPreferencesUpdatedEvent(DriverId, UpdatedAt ?? DateTime.UtcNow));
    }

    public void UpdateLastLocation(double latitude, double longitude, double? accuracy = null, string? locationDescription = null)
    {
        if (latitude < -90 || latitude > 90)
            throw new ArgumentException("Latitude must be between -90 and 90 degrees", nameof(latitude));

        if (longitude < -180 || longitude > 180)
            throw new ArgumentException("Longitude must be between -180 and 180 degrees", nameof(longitude));

        LastLatitude = latitude;
        LastLongitude = longitude;
        LastAccuracy = accuracy;
        LastKnownLocation = locationDescription;
        LastLocationUpdate = DateTime.UtcNow;
        LastGpsTimestamp = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DriverLocationUpdatedEvent(DriverId, latitude, longitude, LastGpsTimestamp ?? DateTime.UtcNow));
    }

    public void ToggleLocationSharing()
    {
        if (!ManualToggleAllowed)
            throw new InvalidOperationException("Manual location sharing toggle is not allowed for this driver");

        LocationSharingEnabled = !LocationSharingEnabled;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DriverLocationSharingToggedEvent(DriverId, LocationSharingEnabled, UpdatedAt ?? DateTime.UtcNow));
    }

    public void EnableLocationSharing()
    {
        if (!LocationSharingEnabled)
        {
            LocationSharingEnabled = true;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new DriverLocationSharingToggedEvent(DriverId, true, UpdatedAt ?? DateTime.UtcNow));
        }
    }

    public void DisableLocationSharing()
    {
        if (ManualToggleAllowed && LocationSharingEnabled)
        {
            LocationSharingEnabled = false;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new DriverLocationSharingToggedEvent(DriverId, false, UpdatedAt ?? DateTime.UtcNow));
        }
        else if (!ManualToggleAllowed)
        {
            throw new InvalidOperationException("Manual location sharing toggle is not allowed for this driver");
        }
    }

    public bool IsLocationDataStale(int staleThresholdMinutes = 5)
    {
        return LastLocationUpdate < DateTime.UtcNow.AddMinutes(-staleThresholdMinutes);
    }

    public bool IsLocationAccurate()
    {
        return LastAccuracy.HasValue && LastAccuracy.Value <= LocationAccuracyThresholdMeters;
    }

    public TimeSpan GetTimeSinceLastUpdate()
    {
        return DateTime.UtcNow - LastLocationUpdate;
    }
}

// Domain Events
public record DriverLocationPreferencesCreatedEvent(Guid DriverId, bool LocationSharingEnabled) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DriverLocationPreferencesUpdatedEvent(Guid DriverId, DateTime UpdatedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DriverLocationSharingToggedEvent(Guid DriverId, bool LocationSharingEnabled, DateTime ToggledAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DriverLocationUpdatedEvent(Guid DriverId, double Latitude, double Longitude, DateTime Timestamp) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}



