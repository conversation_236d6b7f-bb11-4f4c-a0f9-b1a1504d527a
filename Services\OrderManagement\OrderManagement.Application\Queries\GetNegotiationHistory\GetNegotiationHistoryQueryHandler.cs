using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetNegotiationHistory;

public class GetNegotiationHistoryQueryHandler : IRequestHandler<GetNegotiationHistoryQuery, List<NegotiationDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetNegotiationHistoryQueryHandler> _logger;

    public GetNegotiationHistoryQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetNegotiationHistoryQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<NegotiationDto>> Handle(GetNegotiationHistoryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting negotiation history for RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        try
        {
            // Get the RFQ with negotiations
            var rfq = await _rfqRepository.GetByIdWithNegotiationsAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return new List<NegotiationDto>();
            }

            // Validate user has permission to view negotiations
            if (rfq.TransportCompanyId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view negotiations for RFQ {RfqId}", 
                    request.RequestingUserId, request.RfqId);
                throw new UnauthorizedAccessException("You do not have permission to view negotiations for this RFQ");
            }

            // Filter negotiations based on criteria
            var negotiations = rfq.Negotiations.AsEnumerable();

            if (request.BrokerId.HasValue)
            {
                negotiations = negotiations.Where(n => n.BrokerId == request.BrokerId.Value);
            }

            if (!request.IncludeCompleted)
            {
                negotiations = negotiations.Where(n => !n.IsCompleted);
            }

            if (!request.IncludeCancelled)
            {
                negotiations = negotiations.Where(n => !n.IsCancelled);
            }

            if (request.FromDate.HasValue)
            {
                negotiations = negotiations.Where(n => n.StartedAt >= request.FromDate.Value);
            }

            if (request.ToDate.HasValue)
            {
                negotiations = negotiations.Where(n => n.StartedAt <= request.ToDate.Value);
            }

            // Apply sorting
            negotiations = ApplySorting(negotiations, request.SortBy, request.SortDescending);

            // Map to DTOs
            var negotiationDtos = negotiations.Select(MapToNegotiationDto).ToList();

            _logger.LogInformation("Successfully retrieved {Count} negotiations for RFQ {RfqId}", 
                negotiationDtos.Count, request.RfqId);

            return negotiationDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting negotiation history for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }

    private IEnumerable<Domain.Entities.Negotiation> ApplySorting(
        IEnumerable<Domain.Entities.Negotiation> negotiations, 
        string? sortBy, 
        bool descending)
    {
        return sortBy?.ToLower() switch
        {
            "startedat" => descending 
                ? negotiations.OrderByDescending(n => n.StartedAt)
                : negotiations.OrderBy(n => n.StartedAt),
            "completedat" => descending 
                ? negotiations.OrderByDescending(n => n.CompletedAt ?? DateTime.MaxValue)
                : negotiations.OrderBy(n => n.CompletedAt ?? DateTime.MaxValue),
            "totalrounds" => descending 
                ? negotiations.OrderByDescending(n => n.TotalRounds)
                : negotiations.OrderBy(n => n.TotalRounds),
            _ => descending 
                ? negotiations.OrderByDescending(n => n.StartedAt)
                : negotiations.OrderBy(n => n.StartedAt)
        };
    }

    private NegotiationDto MapToNegotiationDto(Domain.Entities.Negotiation negotiation)
    {
        return new NegotiationDto
        {
            Id = negotiation.Id,
            RfqId = negotiation.RfqId,
            BrokerId = negotiation.BrokerId,
            BrokerName = "Broker Name", // Would need to fetch from external service
            OriginalBidId = negotiation.OriginalBidId,
            NegotiationStatus = negotiation.Status.ToString(),
            StartedAt = negotiation.StartedAt,
            CompletedAt = negotiation.CompletedAt,
            TotalRounds = negotiation.TotalRounds,
            OriginalPrice = _mapper.Map<MoneyDto>(negotiation.OriginalPrice),
            FinalPrice = negotiation.FinalPrice != null ? _mapper.Map<MoneyDto>(negotiation.FinalPrice) : null,
            FinalTerms = negotiation.FinalTerms,
            IsActive = negotiation.IsActive,
            Notes = negotiation.Notes,
            CounterOffers = negotiation.CounterOffers.Select(MapToCounterOfferDto).ToList()
        };
    }

    private CounterOfferDto MapToCounterOfferDto(Domain.Entities.CounterOffer counterOffer)
    {
        return new CounterOfferDto
        {
            Id = counterOffer.Id,
            NegotiationId = counterOffer.NegotiationId,
            BidId = counterOffer.BidId,
            OfferType = counterOffer.OfferType.ToString(),
            OfferedPrice = _mapper.Map<MoneyDto>(counterOffer.OfferedPrice),
            OfferedTerms = counterOffer.OfferedTerms,
            OfferedPickupDate = counterOffer.OfferedPickupDate,
            OfferedDeliveryDate = counterOffer.OfferedDeliveryDate,
            AdditionalServices = counterOffer.AdditionalServices,
            CounterOfferReason = counterOffer.CounterOfferReason,
            CreatedAt = counterOffer.CreatedAt,
            CreatedBy = counterOffer.CreatedBy,
            CreatedByName = "User Name", // Would need to fetch from external service
            Status = counterOffer.Status.ToString(),
            RespondedAt = counterOffer.RespondedAt,
            ResponseNotes = counterOffer.ResponseNotes,
            IsAccepted = counterOffer.IsAccepted,
            IsRejected = counterOffer.IsRejected,
            IsExpired = counterOffer.IsExpired,
            ExpiresAt = counterOffer.ExpiresAt
        };
    }
}
