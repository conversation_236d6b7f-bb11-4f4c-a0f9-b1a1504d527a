using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class RfqRoutingHistoryRepository : IRfqRoutingHistoryRepository
{
    private readonly OrderManagementDbContext _context;

    public RfqRoutingHistoryRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<List<RfqRoutingHistory>> GetByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default)
    {
        return await _context.RfqRoutingHistories
            .Where(rh => rh.RfqId == rfqId)
            .OrderBy(rh => rh.SequenceNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<RfqRoutingHistory?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RfqRoutingHistories
            .FirstOrDefaultAsync(rh => rh.Id == id, cancellationToken);
    }

    public async Task<List<RfqRoutingHistory>> GetPendingRoutingsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RfqRoutingHistories
            .Where(rh => rh.Status == RfqRoutingStatus.Pending)
            .OrderBy(rh => rh.RoutedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RfqRoutingHistory>> GetExpiredRoutingsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _context.RfqRoutingHistories
            .Where(rh => rh.Status == RfqRoutingStatus.Pending &&
                        rh.ResponseDeadline.HasValue &&
                        rh.ResponseDeadline.Value < now)
            .OrderBy(rh => rh.ResponseDeadline)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<RfqRoutingHistory>> GetByStatusAsync(RfqRoutingStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.RfqRoutingHistories
            .Where(rh => rh.Status == status)
            .OrderBy(rh => rh.RoutedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(RfqRoutingHistory routingHistory, CancellationToken cancellationToken = default)
    {
        await _context.RfqRoutingHistories.AddAsync(routingHistory, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(RfqRoutingHistory routingHistory)
    {
        _context.RfqRoutingHistories.Update(routingHistory);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var routingHistory = await GetByIdAsync(id, cancellationToken);
        if (routingHistory != null)
        {
            _context.RfqRoutingHistories.Remove(routingHistory);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
