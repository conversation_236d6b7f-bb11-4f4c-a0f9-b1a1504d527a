using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class CreatePreferredPartnerCommandHandler : IRequestHandler<CreatePreferredPartnerCommand, Guid>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<CreatePreferredPartnerCommandHandler> _logger;

    public CreatePreferredPartnerCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<CreatePreferredPartnerCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreatePreferredPartnerCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating preferred partner relationship for user {UserId} with partner {PartnerId}",
            request.UserId, request.PartnerId);

        // Validate that the relationship doesn't already exist
        var existingRelationship = await _preferredPartnerRepository.GetByUserAndPartnerAsync(
            request.UserId, request.PartnerId, cancellationToken);

        if (existingRelationship != null)
        {
            if (existingRelationship.IsActive)
            {
                throw new InvalidOperationException(
                    $"An active preferred partner relationship already exists between user {request.UserId} and partner {request.PartnerId}");
            }
            
            // Reactivate existing inactive relationship
            existingRelationship.Activate($"Reactivated with updated preferences: {request.Notes}");
            existingRelationship.UpdatePreferenceLevel(request.PreferenceLevel, request.Priority);
            existingRelationship.UpdateRates(request.PreferredCommissionRate, request.PreferredServiceRate);
            
            if (request.AutoAssignEnabled)
            {
                existingRelationship.UpdateAutoAssignSettings(
                    request.AutoAssignEnabled,
                    request.AutoAssignThreshold,
                    request.PreferredRoutes,
                    request.PreferredLoadTypes,
                    request.ExcludedRoutes,
                    request.ExcludedLoadTypes);
            }
            
            existingRelationship.UpdateNotificationPreferences(
                request.NotifyOnNewOpportunities,
                request.NotifyOnPerformanceChanges,
                request.NotifyOnContractExpiry);

            if (!string.IsNullOrEmpty(request.AgreementNumber))
            {
                existingRelationship.UpdateAgreementDetails(
                    request.AgreementNumber,
                    request.AgreementStartDate,
                    request.AgreementEndDate,
                    request.PaymentTermsDays,
                    request.SpecialTerms);
            }

            if (request.IsExclusive)
            {
                existingRelationship.SetExclusivity(request.IsExclusive, request.ExclusivityExpiresAt);
            }

            await _preferredPartnerRepository.UpdateAsync(existingRelationship, cancellationToken);
            
            _logger.LogInformation("Reactivated existing preferred partner relationship {RelationshipId}",
                existingRelationship.Id);
            
            return existingRelationship.Id;
        }

        // Create new preferred partner relationship
        var preferredPartner = new PreferredPartner(
            request.UserId,
            request.PartnerId,
            request.PartnerType,
            request.PreferenceLevel,
            request.Priority,
            request.PreferredCommissionRate,
            request.PreferredServiceRate,
            request.AutoAssignEnabled,
            request.Notes);

        // Set auto-assignment settings if enabled
        if (request.AutoAssignEnabled)
        {
            preferredPartner.UpdateAutoAssignSettings(
                request.AutoAssignEnabled,
                request.AutoAssignThreshold,
                request.PreferredRoutes,
                request.PreferredLoadTypes,
                request.ExcludedRoutes,
                request.ExcludedLoadTypes);
        }

        // Set notification preferences
        preferredPartner.UpdateNotificationPreferences(
            request.NotifyOnNewOpportunities,
            request.NotifyOnPerformanceChanges,
            request.NotifyOnContractExpiry);

        // Set agreement details if provided
        if (!string.IsNullOrEmpty(request.AgreementNumber))
        {
            preferredPartner.UpdateAgreementDetails(
                request.AgreementNumber,
                request.AgreementStartDate,
                request.AgreementEndDate,
                request.PaymentTermsDays,
                request.SpecialTerms);
        }

        // Set exclusivity if requested
        if (request.IsExclusive)
        {
            preferredPartner.SetExclusivity(request.IsExclusive, request.ExclusivityExpiresAt);
        }

        // Adjust priorities of existing partners in the same preference level
        await AdjustExistingPartnerPriorities(request.UserId, request.PreferenceLevel, request.Priority, cancellationToken);

        await _preferredPartnerRepository.AddAsync(preferredPartner, cancellationToken);

        _logger.LogInformation("Successfully created preferred partner relationship {RelationshipId} for user {UserId} with partner {PartnerId}",
            preferredPartner.Id, request.UserId, request.PartnerId);

        return preferredPartner.Id;
    }

    private async Task AdjustExistingPartnerPriorities(
        Guid userId, 
        PreferenceLevel preferenceLevel, 
        int newPriority, 
        CancellationToken cancellationToken)
    {
        // Get existing partners in the same preference level
        var existingPartners = await _preferredPartnerRepository.GetByUserAndPreferenceLevelAsync(
            userId, preferenceLevel, cancellationToken);

        // Adjust priorities of partners that have the same or higher priority
        foreach (var partner in existingPartners.Where(p => p.Priority >= newPriority))
        {
            partner.UpdatePreferenceLevel(preferenceLevel, partner.Priority + 1);
            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);
        }
    }
}
