using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Vehicles;

public class GetVehiclesByCarrierQueryHandler : IRequestHandler<GetVehiclesByCarrierQuery, IEnumerable<VehicleSummaryDto>>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;

    public GetVehiclesByCarrierQueryHandler(IVehicleRepository vehicleRepository, IMapper mapper)
    {
        _vehicleRepository = vehicleRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<VehicleSummaryDto>> Handle(GetVehiclesByCarrierQuery request, CancellationToken cancellationToken)
    {
        var vehicles = await _vehicleRepository.GetByCarrierIdAsync(request.CarrierId, cancellationToken);
        return _mapper.Map<IEnumerable<VehicleSummaryDto>>(vehicles);
    }
}
