using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Analytics entity for tracking message performance and metrics
/// </summary>
public class MessageAnalytics : BaseEntity
{
    public Guid MessageId { get; private set; }
    public Guid UserId { get; private set; }
    public MessageType MessageType { get; private set; }
    public NotificationChannel Channel { get; private set; }
    public MessageStatus Status { get; private set; }
    public DateTime SentAt { get; private set; }
    public DateTime? DeliveredAt { get; private set; }
    public DateTime? ReadAt { get; private set; }
    public DateTime? ClickedAt { get; private set; }
    public TimeSpan? DeliveryTime { get; private set; }
    public TimeSpan? ReadTime { get; private set; }
    public TimeSpan? ResponseTime { get; private set; }
    public string? FailureReason { get; private set; }
    public int RetryCount { get; private set; }
    public decimal Cost { get; private set; }
    public string Currency { get; private set; }
    public string? CampaignId { get; private set; }
    public string? ABTestVariant { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private MessageAnalytics()
    {
        Tags = new Dictionary<string, string>();
        Metadata = new Dictionary<string, object>();
        Currency = "INR";
    }

    public MessageAnalytics(
        Guid messageId,
        Guid userId,
        MessageType messageType,
        NotificationChannel channel,
        DateTime sentAt,
        decimal cost = 0,
        string currency = "INR",
        string? campaignId = null,
        string? abTestVariant = null,
        Dictionary<string, string>? tags = null,
        Dictionary<string, object>? metadata = null)
    {
        if (messageId == Guid.Empty)
            throw new ArgumentException("Message ID cannot be empty", nameof(messageId));
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        MessageId = messageId;
        UserId = userId;
        MessageType = messageType;
        Channel = channel;
        Status = MessageStatus.Sent;
        SentAt = sentAt;
        Cost = cost;
        Currency = currency;
        CampaignId = campaignId;
        ABTestVariant = abTestVariant;
        Tags = tags ?? new Dictionary<string, string>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public static MessageAnalytics Create(
        Guid messageId,
        Guid userId,
        MessageType messageType,
        NotificationChannel channel,
        DateTime sentAt,
        decimal cost = 0,
        string currency = "INR",
        string? campaignId = null,
        string? abTestVariant = null,
        Dictionary<string, string>? tags = null,
        Dictionary<string, object>? metadata = null)
    {
        return new MessageAnalytics(messageId, userId, messageType, channel, sentAt,
            cost, currency, campaignId, abTestVariant, tags, metadata);
    }

    public void MarkAsDelivered(DateTime deliveredAt)
    {
        if (Status != MessageStatus.Sent)
            throw new InvalidOperationException($"Cannot mark as delivered when status is {Status}");

        Status = MessageStatus.Delivered;
        DeliveredAt = deliveredAt;
        DeliveryTime = deliveredAt - SentAt;
    }

    public void MarkAsRead(DateTime readAt)
    {
        if (Status != MessageStatus.Delivered && Status != MessageStatus.Sent)
            throw new InvalidOperationException($"Cannot mark as read when status is {Status}");

        Status = MessageStatus.Read;
        ReadAt = readAt;
        ReadTime = readAt - (DeliveredAt ?? SentAt);
    }

    public void MarkAsClicked(DateTime clickedAt)
    {
        ClickedAt = clickedAt;
        ResponseTime = clickedAt - SentAt;
    }

    public void MarkAsFailed(string failureReason, int retryCount = 0)
    {
        Status = MessageStatus.Failed;
        FailureReason = failureReason;
        RetryCount = retryCount;
    }

    public void AddTag(string key, string value)
    {
        Tags[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsDelivered => Status == MessageStatus.Delivered || Status == MessageStatus.Read;
    public bool IsRead => Status == MessageStatus.Read;
    public bool IsClicked => ClickedAt.HasValue;
    public bool IsFailed => Status == MessageStatus.Failed;
    public bool IsSuccessful => IsDelivered && !IsFailed;

    public decimal GetEngagementScore()
    {
        decimal score = 0;

        if (IsDelivered) score += 1;
        if (IsRead) score += 2;
        if (IsClicked) score += 3;

        return score;
    }
}


