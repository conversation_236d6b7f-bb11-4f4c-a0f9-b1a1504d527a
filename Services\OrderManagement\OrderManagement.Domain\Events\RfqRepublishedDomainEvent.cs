using Shared.Domain.Common;

namespace OrderManagement.Domain.Events;

public class RfqRepublishedDomainEvent : DomainEvent
{
    public Guid RfqId { get; }
    public string Title { get; }
    public Guid RepublishedBy { get; }

    public RfqRepublishedDomainEvent(Guid rfqId, string title, Guid republishedBy)
    {
        RfqId = rfqId;
        Title = title;
        RepublishedBy = republishedBy;
    }
}
