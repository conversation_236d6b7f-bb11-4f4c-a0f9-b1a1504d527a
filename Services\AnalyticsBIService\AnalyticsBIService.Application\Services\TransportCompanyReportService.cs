using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using DomainReportTemplate = AnalyticsBIService.Domain.Entities.ReportTemplate;
using DomainReportExecution = AnalyticsBIService.Domain.Entities.ReportExecution;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for Transport Company report generation and export
/// </summary>
public interface ITransportCompanyReportService
{
    Task<List<ReportTemplateDto>> GetAvailableReportTemplatesAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);

    Task<ReportGenerationResultDto> GenerateReportAsync(
        GenerateReportRequestDto request,
        CancellationToken cancellationToken = default);

    Task<ExportResultDto> ExportReportAsync(
        ExportReportRequestDto request,
        CancellationToken cancellationToken = default);

    Task<List<AnalyticsBIService.Application.DTOs.ScheduledReportDto>> GetScheduledReportsAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);

    Task<AnalyticsBIService.Application.DTOs.ScheduledReportDto> CreateScheduledReportAsync(
        CreateScheduledReportRequestDto request,
        CancellationToken cancellationToken = default);

    Task<ReportExecutionStatusDto> GetReportExecutionStatusAsync(
        Guid executionId,
        CancellationToken cancellationToken = default);

    Task<List<AnalyticsBIService.Application.DTOs.ReportExecutionHistoryDto>> GetReportExecutionHistoryAsync(
        Guid transportCompanyId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<CustomReportTemplateDto> CreateCustomReportTemplateAsync(
        CreateCustomReportTemplateRequestDto request,
        CancellationToken cancellationToken = default);

    Task<ReportPreviewDto> PreviewReportAsync(
        PreviewReportRequestDto request,
        CancellationToken cancellationToken = default);
}

public class TransportCompanyReportService : ITransportCompanyReportService
{
    private readonly AnalyticsBIService.Domain.Repositories.IReportTemplateRepository _reportTemplateRepository;
    private readonly IReportExecutionRepository _reportExecutionRepository;
    private readonly IDataAggregationService _dataAggregationService;
    private readonly IReportGenerationService _reportGenerationService;
    private readonly IFileStorageService _fileStorageService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<TransportCompanyReportService> _logger;

    public TransportCompanyReportService(
        AnalyticsBIService.Domain.Repositories.IReportTemplateRepository reportTemplateRepository,
        IReportExecutionRepository reportExecutionRepository,
        IDataAggregationService dataAggregationService,
        IReportGenerationService reportGenerationService,
        IFileStorageService fileStorageService,
        IMemoryCache cache,
        ILogger<TransportCompanyReportService> logger)
    {
        _reportTemplateRepository = reportTemplateRepository;
        _reportExecutionRepository = reportExecutionRepository;
        _dataAggregationService = dataAggregationService;
        _reportGenerationService = reportGenerationService;
        _fileStorageService = fileStorageService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<List<ReportTemplateDto>> GetAvailableReportTemplatesAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting available report templates for Transport Company {TransportCompanyId}", transportCompanyId);

        var cacheKey = $"report_templates_{transportCompanyId}";
        if (_cache.TryGetValue(cacheKey, out List<ReportTemplateDto>? cachedTemplates))
        {
            return cachedTemplates!;
        }

        var templates = new List<ReportTemplateDto>();

        // Add built-in templates
        templates.AddRange(GetBuiltInTransportCompanyTemplates());

        // Add custom templates for this transport company
        var customTemplates = await _reportTemplateRepository.GetByTransportCompanyAsync(transportCompanyId, cancellationToken);
        templates.AddRange(customTemplates.Select(MapToDto));

        // Cache for 1 hour
        _cache.Set(cacheKey, templates, TimeSpan.FromHours(1));

        return templates;
    }

    public async Task<ReportGenerationResultDto> GenerateReportAsync(
        GenerateReportRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating report {TemplateId} for Transport Company {TransportCompanyId}",
            request.TemplateId, request.TransportCompanyId);

        try
        {
            // Create execution record
            var execution = AnalyticsBIService.Domain.Entities.ReportExecution.Create(
                reportId: Guid.NewGuid(), // Temporary report ID
                templateId: request.TemplateId,
                userId: request.RequestedBy,
                parameters: request.Parameters != null ? System.Text.Json.JsonSerializer.Serialize(request.Parameters) : "{}",
                outputFormat: AnalyticsBIService.Domain.Enums.ExportFormat.JSON,
                isManualExecution: true,
                reportName: $"Transport Company Report - {DateTime.UtcNow:yyyy-MM-dd}"
            );

            await _reportExecutionRepository.AddAsync(execution, cancellationToken);

            // Get template
            var template = await GetReportTemplateAsync(request.TemplateId, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Report template {request.TemplateId} not found");
            }

            // Collect data based on template configuration
            var templateDto = MapToDto(template);
            var reportData = await CollectReportDataAsync(templateDto, request, cancellationToken);

            // Extract TotalRecords before casting
            var totalRecords = ((dynamic)reportData).TotalRecords;

            // Convert anonymous object to Dictionary for the report generation service
            var reportDataDict = new Dictionary<string, object>
            {
                ["TotalRecords"] = totalRecords,
                ["Data"] = ((dynamic)reportData).Data
            };

            // Generate report content
            var reportContent = await _reportGenerationService.GenerateReportContentAsync(
                templateDto, reportDataDict, cancellationToken);

            // Generate file for storage
            var fileName = GenerateFileName(execution, ExportFormat.JSON);
            var fileContent = System.Text.Encoding.UTF8.GetBytes(reportContent);
            var filePath = await _fileStorageService.StoreFileAsync(
                fileContent, fileName, "application/json", cancellationToken);

            // Update execution record - use the MarkCompleted method instead of direct assignment
            execution.MarkCompleted(filePath, fileContent.Length, totalRecords);

            await _reportExecutionRepository.UpdateAsync(execution, cancellationToken);

            return new ReportGenerationResultDto
            {
                ExecutionId = execution.Id,
                Status = (AnalyticsBIService.Application.DTOs.ReportExecutionStatus)Enum.Parse(typeof(AnalyticsBIService.Domain.Enums.ReportExecutionStatus), execution.Status),
                ReportContent = reportContent,
                RecordCount = execution.RecordCount ?? 0,
                GeneratedAt = execution.EndTime.Value,
                DownloadUrl = GenerateDownloadUrl(execution.Id)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report {TemplateId} for Transport Company {TransportCompanyId}",
                request.TemplateId, request.TransportCompanyId);
            throw;
        }
    }

    public async Task<ExportResultDto> ExportReportAsync(
        ExportReportRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Exporting report {ExecutionId} in {Format} format",
            request.ExecutionId, request.Format);

        try
        {
            // Get execution record
            var execution = await _reportExecutionRepository.GetByIdAsync(request.ExecutionId, cancellationToken);
            if (execution == null)
            {
                throw new InvalidOperationException($"Report execution {request.ExecutionId} not found");
            }

            // Generate file based on format
            var fileName = GenerateFileName(execution, request.Format);
            var fileContent = await GenerateExportFileAsync(execution, request.Format, cancellationToken);

            // Store file
            var filePath = await _fileStorageService.StoreFileAsync(
                fileContent, fileName, "application/octet-stream", cancellationToken);

            // Create export record
            var exportResult = new ExportResultDto
            {
                ExportId = Guid.NewGuid(),
                ExecutionId = request.ExecutionId,
                Format = request.Format.ToString(),
                FileName = fileName,
                FilePath = filePath,
                FileSize = fileContent.Length,
                ExportedAt = DateTime.UtcNow,
                DownloadUrl = GenerateDownloadUrl(filePath),
                ExpiresAt = DateTime.UtcNow.AddDays(7) // Files expire after 7 days
            };

            return exportResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report {ExecutionId}", request.ExecutionId);
            throw;
        }
    }

    public async Task<List<AnalyticsBIService.Application.DTOs.ScheduledReportDto>> GetScheduledReportsAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting scheduled reports for Transport Company {TransportCompanyId}", transportCompanyId);

        var scheduledReports = await _reportExecutionRepository.GetScheduledReportsAsync(cancellationToken);
        // Filter by transport company ID if needed (assuming UserId represents the transport company)
        var filteredReports = scheduledReports.Where(r => r.UserId == transportCompanyId);
        return filteredReports.Select(MapScheduledReportToDto).ToList();
    }

    public async Task<AnalyticsBIService.Application.DTOs.ScheduledReportDto> CreateScheduledReportAsync(
        CreateScheduledReportRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating scheduled report for Transport Company {TransportCompanyId}", request.TransportCompanyId);

        // Create a ReportExecution entity for the scheduled report
        var reportExecution = AnalyticsBIService.Domain.Entities.ReportExecution.Create(
            reportId: Guid.NewGuid(), // Generate a new report ID for this scheduled execution
            templateId: request.TemplateId,
            userId: request.TransportCompanyId, // Use TransportCompanyId as UserId
            parameters: System.Text.Json.JsonSerializer.Serialize(request.Parameters),
            outputFormat: request.ExportFormats.Any() ? request.ExportFormats.First() : ExportFormat.PDF,
            isManualExecution: false,
            reportName: request.Name
        );

        await _reportExecutionRepository.AddScheduledReportAsync(reportExecution, cancellationToken);

        return MapScheduledReportToDto(reportExecution);
    }

    public async Task<ReportExecutionStatusDto> GetReportExecutionStatusAsync(
        Guid executionId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting execution status for report {ExecutionId}", executionId);

        var execution = await _reportExecutionRepository.GetByIdAsync(executionId, cancellationToken);
        if (execution == null)
        {
            throw new InvalidOperationException($"Report execution {executionId} not found");
        }

        return new ReportExecutionStatusDto
        {
            ExecutionId = execution.Id,
            Status = ParseExecutionStatus(execution.Status),
            StartedAt = execution.StartTime,
            CompletedAt = execution.EndTime,
            RecordCount = execution.RecordCount,
            ErrorMessage = execution.ErrorMessage,
            Progress = (int)CalculateProgress(execution),
            EstimatedTimeRemaining = CalculateEstimatedTimeRemaining(execution)
        };
    }

    public async Task<List<AnalyticsBIService.Application.DTOs.ReportExecutionHistoryDto>> GetReportExecutionHistoryAsync(
        Guid transportCompanyId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting report execution history for Transport Company {TransportCompanyId}", transportCompanyId);

        var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var to = toDate ?? DateTime.UtcNow;

        // Get all executions for the transport company (using a placeholder reportId)
        // Note: This method signature doesn't match the expected usage - may need repository interface update
        var allExecutions = await _reportExecutionRepository.GetExecutionHistoryAsync(
            transportCompanyId, cancellationToken);

        // Filter by date range and user
        var filteredExecutions = allExecutions
            .Where(e => e.UserId == transportCompanyId &&
                       e.StartTime >= from &&
                       e.StartTime <= to)
            .OrderByDescending(e => e.StartTime);

        return filteredExecutions.Select(MapExecutionHistoryToDto).ToList();
    }

    public async Task<CustomReportTemplateDto> CreateCustomReportTemplateAsync(
        CreateCustomReportTemplateRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating custom report template for Transport Company {TransportCompanyId}", request.TransportCompanyId);

        // Serialize configuration and parameters for domain entity
        var templateContent = System.Text.Json.JsonSerializer.Serialize(request.Configuration);
        var parametersJson = System.Text.Json.JsonSerializer.Serialize(new Dictionary<string, object>());

        // Create domain entity
        var template = Domain.Entities.ReportTemplate.Create(
            request.Name,
            request.Description,
            request.Category,
            templateContent,
            parametersJson,
            UserType.TransportCompany,
            request.CreatedBy);

        await _reportTemplateRepository.AddCustomTemplateAsync(template, cancellationToken);

        // Clear cache
        _cache.Remove($"report_templates_{request.TransportCompanyId}");

        // Convert domain entity back to DTO
        return new CustomReportTemplateDto
        {
            TemplateId = template.Id,
            Name = template.Name,
            Description = template.Description,
            Category = template.Category,
            Configuration = request.Configuration,
            CreatedBy = template.CreatedBy,
            CreatedAt = template.CreatedDate,
            IsPublic = false
        };
    }

    public async Task<ReportPreviewDto> PreviewReportAsync(
        PreviewReportRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating report preview for template {TemplateId}", request.TemplateId);

        try
        {
            // Get template
            var template = await GetReportTemplateAsync(request.TemplateId, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Report template {request.TemplateId} not found");
            }

            // Collect limited sample data
            var templateDto = MapToDto(template);
            var sampleData = await CollectSampleReportDataAsync(templateDto, request, cancellationToken);

            // Extract TotalRecords from the anonymous object
            var totalRecords = ((dynamic)sampleData).TotalRecords;

            // Generate preview content
            var previewContent = await _reportGenerationService.GeneratePreviewContentAsync(
                templateDto, sampleData, request.Parameters, cancellationToken);

            return new ReportPreviewDto
            {
                TemplateId = request.TemplateId,
                PreviewContent = previewContent,
                SampleRecordCount = totalRecords,
                GeneratedAt = DateTime.UtcNow,
                EstimatedFullReportSize = EstimateFullReportSize((ReportDataCollection)sampleData, template)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report preview for template {TemplateId}", request.TemplateId);
            throw;
        }
    }

    private List<ReportTemplateDto> GetBuiltInTransportCompanyTemplates()
    {
        return new List<ReportTemplateDto>
        {
            new ReportTemplateDto
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                Name = "Trip Performance Report",
                Description = "Comprehensive analysis of trip performance including on-time delivery, delays, and efficiency metrics",
                Category = "Operations",
                DataSources = new List<string> { "TripManagement", "OrderManagement" },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["DateRange"] = "Last30Days",
                    ["IncludeDelayAnalysis"] = true,
                    ["GroupBy"] = "Month"
                },
                SupportedFormats = new List<ExportFormat> { ExportFormat.Excel, ExportFormat.PDF, ExportFormat.CSV },
                IsBuiltIn = true,
                EstimatedExecutionTime = TimeSpan.FromMinutes(2)
            },
            new ReportTemplateDto
            {
                Id = Guid.Parse("*************-2222-2222-************"),
                Name = "Financial Summary Report",
                Description = "Financial overview including payments, outstanding balances, commissions, and revenue trends",
                Category = "Financial",
                DataSources = new List<string> { "FinancialPayment", "OrderManagement" },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["DateRange"] = "LastQuarter",
                    ["IncludeTaxBreakdown"] = true,
                    ["Currency"] = "INR"
                },
                SupportedFormats = new List<ExportFormat> { ExportFormat.Excel, ExportFormat.PDF },
                IsBuiltIn = true,
                EstimatedExecutionTime = TimeSpan.FromMinutes(3)
            },
            new ReportTemplateDto
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                Name = "Customer Feedback Analysis",
                Description = "Analysis of customer ratings, feedback trends, and service quality metrics",
                Category = "Quality",
                DataSources = new List<string> { "OrderManagement", "TripManagement" },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["DateRange"] = "Last6Months",
                    ["MinimumRating"] = 1,
                    ["IncludeSentimentAnalysis"] = true
                },
                SupportedFormats = new List<ExportFormat> { ExportFormat.Excel, ExportFormat.PDF, ExportFormat.CSV },
                IsBuiltIn = true,
                EstimatedExecutionTime = TimeSpan.FromMinutes(4)
            },
            new ReportTemplateDto
            {
                Id = Guid.Parse("*************-4444-4444-************"),
                Name = "Vehicle & Driver Utilization",
                Description = "Analysis of vehicle and driver utilization rates, efficiency, and performance metrics",
                Category = "Fleet",
                DataSources = new List<string> { "NetworkFleetManagement", "TripManagement" },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["DateRange"] = "LastMonth",
                    ["IncludeMaintenanceData"] = true,
                    ["GroupBy"] = "Vehicle"
                },
                SupportedFormats = new List<ExportFormat> { ExportFormat.Excel, ExportFormat.PDF },
                IsBuiltIn = true,
                EstimatedExecutionTime = TimeSpan.FromMinutes(3)
            },
            new ReportTemplateDto
            {
                Id = Guid.Parse("*************-5555-5555-************"),
                Name = "Compliance & Documentation Status",
                Description = "Status of compliance requirements, document expiry tracking, and regulatory adherence",
                Category = "Compliance",
                DataSources = new List<string> { "UserManagement", "AuditCompliance" },
                DefaultParameters = new Dictionary<string, object>
                {
                    ["IncludeExpiringDocuments"] = true,
                    ["ExpiryThresholdDays"] = 30,
                    ["DocumentTypes"] = new List<string> { "All" }
                },
                SupportedFormats = new List<ExportFormat> { ExportFormat.Excel, ExportFormat.PDF, ExportFormat.CSV },
                IsBuiltIn = true,
                EstimatedExecutionTime = TimeSpan.FromMinutes(2)
            }
        };
    }

    private async Task<DomainReportTemplate?> GetReportTemplateAsync(Guid templateId, CancellationToken cancellationToken)
    {
        // Check built-in templates first
        var builtInTemplate = GetBuiltInTransportCompanyTemplates().FirstOrDefault(t => t.Id == templateId);
        if (builtInTemplate != null)
        {
            return MapDtoToTemplate(builtInTemplate);
        }

        // Check custom templates
        return await _reportTemplateRepository.GetByIdAsync(templateId, cancellationToken);
    }

    private string GenerateFileName(DomainReportExecution execution, ExportFormat format)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var extension = format.ToString().ToLower();
        return $"transport_company_report_{execution.Id:N}_{timestamp}.{extension}";
    }

    private async Task<byte[]> GenerateExportFileAsync(
        DomainReportExecution execution,
        ExportFormat format,
        CancellationToken cancellationToken)
    {
        return format switch
        {
            ExportFormat.Excel => await GenerateExcelFileAsync(execution, cancellationToken),
            ExportFormat.PDF => await GeneratePdfFileAsync(execution, cancellationToken),
            ExportFormat.CSV => await GenerateCsvFileAsync(execution, cancellationToken),
            ExportFormat.JSON => await GenerateJsonFileAsync(execution, cancellationToken),
            _ => throw new ArgumentException($"Unsupported export format: {format}")
        };
    }

    private async Task<byte[]> GenerateExcelFileAsync(DomainReportExecution execution, CancellationToken cancellationToken)
    {
        // Implementation would use a library like EPPlus to generate Excel files
        return await Task.FromResult(Array.Empty<byte>());
    }

    private async Task<byte[]> GeneratePdfFileAsync(DomainReportExecution execution, CancellationToken cancellationToken)
    {
        // Implementation would use a library like iTextSharp to generate PDF files
        return await Task.FromResult(Array.Empty<byte>());
    }

    private async Task<byte[]> GenerateCsvFileAsync(DomainReportExecution execution, CancellationToken cancellationToken)
    {
        // Implementation would generate CSV content
        return await Task.FromResult(Array.Empty<byte>());
    }

    private async Task<byte[]> GenerateJsonFileAsync(DomainReportExecution execution, CancellationToken cancellationToken)
    {
        // Implementation would serialize report data to JSON
        return await Task.FromResult(Array.Empty<byte>());
    }

    private string GenerateDownloadUrl(Guid executionId)
    {
        return $"/api/reports/download/{executionId}";
    }

    private string GenerateDownloadUrl(string filePath)
    {
        return $"/api/files/download?path={Uri.EscapeDataString(filePath)}";
    }

    private DateTime CalculateNextExecutionTime(TransportCompanyReportScheduleDto schedule)
    {
        var now = DateTime.UtcNow;
        var executionTime = schedule.ExecutionTime;
        return schedule.Frequency switch
        {
            AnalyticsBIService.Application.DTOs.ScheduleFrequency.Daily => now.Date.AddDays(1).Add(executionTime),
            AnalyticsBIService.Application.DTOs.ScheduleFrequency.Weekly => now.Date.AddDays(7 - (int)now.DayOfWeek + (int)schedule.DayOfWeek).Add(executionTime),
            AnalyticsBIService.Application.DTOs.ScheduleFrequency.Monthly => new DateTime(now.Year, now.Month, schedule.DayOfMonth).AddMonths(1).Add(executionTime),
            _ => now.AddHours(1)
        };
    }

    private long EstimateFullReportSize(ReportDataCollection sampleData, DomainReportTemplate template)
    {
        // Estimate based on sample data size
        var sampleSize = sampleData.TotalRecords;
        var estimatedFullSize = sampleSize * 100; // Assume full report is 100x larger

        return estimatedFullSize * 1024; // Convert to bytes (rough estimate)
    }

    private AnalyticsBIService.Application.DTOs.ReportExecutionStatus ParseExecutionStatus(string status)
    {
        return status?.ToLowerInvariant() switch
        {
            "started" or "processing" => AnalyticsBIService.Application.DTOs.ReportExecutionStatus.InProgress,
            "completed" => AnalyticsBIService.Application.DTOs.ReportExecutionStatus.Completed,
            "failed" => AnalyticsBIService.Application.DTOs.ReportExecutionStatus.Failed,
            "cancelled" => AnalyticsBIService.Application.DTOs.ReportExecutionStatus.Cancelled,
            _ => AnalyticsBIService.Application.DTOs.ReportExecutionStatus.Pending
        };
    }

    private AnalyticsBIService.Application.DTOs.ReportExecutionHistoryDto MapExecutionHistoryToDto(AnalyticsBIService.Domain.Entities.ReportExecution execution)
    {
        return new AnalyticsBIService.Application.DTOs.ReportExecutionHistoryDto
        {
            Id = execution.Id,
            ReportId = execution.ReportId,
            ReportName = execution.ReportName,
            ExecutedAt = execution.ExecutedAt,
            ExecutedBy = execution.ExecutedBy,
            Status = ParseExecutionStatus(execution.Status.ToString()),
            Duration = execution.Duration,
            RecordCount = execution.RecordCount,
            FilePath = execution.FilePath,
            FileSize = execution.FileSize,
            Parameters = execution.Parameters
        };
    }

    private async Task<object> CollectReportDataAsync(
        ReportTemplateDto template,
        GenerateReportRequestDto request,
        CancellationToken cancellationToken)
    {
        // Placeholder implementation for collecting report data
        await Task.CompletedTask; // Placeholder for async operation

        return new
        {
            TotalRecords = 1000, // Full record count
            Data = new List<object>
            {
                new { Id = 1, Name = "Report Data 1", Value = 100 },
                new { Id = 2, Name = "Report Data 2", Value = 200 },
                new { Id = 3, Name = "Report Data 3", Value = 300 }
            }
        };
    }

    private async Task<object> CollectSampleReportDataAsync(
        ReportTemplateDto template,
        PreviewReportRequestDto request,
        CancellationToken cancellationToken)
    {
        // Collect limited sample data for preview
        // This is a simplified implementation - in practice, you would collect actual data
        // based on the template's data sources and parameters

        await Task.CompletedTask; // Placeholder for async operation

        return new
        {
            TotalRecords = 100, // Sample record count
            SampleData = new List<object>
            {
                new { Id = 1, Name = "Sample Data 1", Value = 100 },
                new { Id = 2, Name = "Sample Data 2", Value = 200 },
                new { Id = 3, Name = "Sample Data 3", Value = 300 }
            },
            DataSources = template.DataSources,
            Parameters = request.Parameters
        };
    }

    private ReportTemplateDto MapToDto(AnalyticsBIService.Domain.Entities.ReportTemplate template)
    {
        return new ReportTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            DataSources = new List<string>(), // Placeholder
            Parameters = new List<ReportParameterDto>() // Placeholder
        };
    }

    private AnalyticsBIService.Application.DTOs.ScheduledReportDto MapScheduledReportToDto(AnalyticsBIService.Domain.Entities.ReportExecution execution)
    {
        return new AnalyticsBIService.Application.DTOs.ScheduledReportDto
        {
            Id = execution.Id,
            ReportId = execution.ReportId,
            Status = execution.Status,
            StartTime = execution.StartTime,
            EndTime = execution.EndTime
        };
    }

    private AnalyticsBIService.Domain.Entities.ReportTemplate MapDtoToTemplate(ReportTemplateDto dto)
    {
        // This would typically use a proper mapping library like AutoMapper
        // For now, return a placeholder
        return AnalyticsBIService.Domain.Entities.ReportTemplate.Create(
            dto.Name,
            dto.Description,
            dto.Category,
            string.Empty, // templateContent
            string.Empty, // parameters
            UserType.TransportCompany,
            Guid.NewGuid()); // createdBy
    }

    private decimal CalculateProgress(AnalyticsBIService.Domain.Entities.ReportExecution execution)
    {
        if (execution.IsCompleted) return 100m;
        if (execution.IsFailed) return 0m;
        return 50m; // Placeholder for in-progress
    }

    private TimeSpan CalculateEstimatedTimeRemaining(AnalyticsBIService.Domain.Entities.ReportExecution execution)
    {
        return TimeSpan.FromMinutes(5); // Placeholder
    }

    private ReportTemplateDto MapCustomTemplateToDto(AnalyticsBIService.Domain.Entities.ReportTemplate template)
    {
        return MapToDto(template);
    }
}
