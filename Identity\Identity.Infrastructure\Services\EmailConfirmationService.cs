using System;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Identity.Infrastructure.Services
{
    public class EmailConfirmationService : IEmailConfirmationService
    {
        private readonly IUserRepository _userRepository;
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailConfirmationService> _logger;

        public EmailConfirmationService(
            IUserRepository userRepository,
            IConfiguration configuration,
            ILogger<EmailConfirmationService> logger)
        {
            _userRepository = userRepository;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string> GenerateEmailConfirmationTokenAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            // Generate a random token
            var tokenBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(tokenBytes);
            }
            var token = Convert.ToBase64String(tokenBytes);

            // In a real implementation, you would store this token securely
            // For demo purposes, we're just returning it
            return token;
        }

        public async Task<bool> ValidateEmailConfirmationTokenAsync(Guid userId, string token)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            // In a real implementation, you would validate against a stored token
            // For demo purposes, we're just returning true
            return true;
        }

        public async Task SendConfirmationEmailAsync(Guid userId, string email, string token)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            var confirmationLink = $"{_configuration["AppUrl"]}/confirm-email?userId={userId}&token={Uri.EscapeDataString(token)}";

            // In a real implementation, you would send an email with the confirmation link
            // For demo purposes, we're just logging it
            _logger.LogInformation("Email confirmation link for user {UserId}: {ConfirmationLink}", userId, confirmationLink);
        }
    }
}
