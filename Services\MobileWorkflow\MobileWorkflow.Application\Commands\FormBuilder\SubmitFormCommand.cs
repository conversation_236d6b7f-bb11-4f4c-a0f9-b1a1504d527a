using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class SubmitFormCommand : IRequest<FormSubmissionDto>
    {
        public Guid FormDefinitionId { get; set; }
        public Guid UserId { get; set; }
        public Dictionary<string, object> FormData { get; set; } = new();
        public Dictionary<string, object>? Metadata { get; set; }
        public string? Platform { get; set; }
        public string? DeviceId { get; set; }
        public bool ValidateBeforeSubmit { get; set; } = true;
    }
}
