using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Commands.Milestone;

public class UpdateMilestoneTemplateCommandHandler : IRequestHandler<UpdateMilestoneTemplateCommand, MilestoneTemplateDto>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateMilestoneTemplateCommandHandler> _logger;

    public UpdateMilestoneTemplateCommandHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<UpdateMilestoneTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto> Handle(UpdateMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating milestone template: {Id} by {UpdatedBy}", request.Id, request.UpdatedBy);

            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {request.Id} not found");
            }

            // Check if name is being changed and if new name already exists
            if (template.Name != request.Name)
            {
                var existingTemplate = await _templateRepository.GetByNameAsync(request.Name, cancellationToken);
                if (existingTemplate != null && existingTemplate.Id != request.Id)
                {
                    throw new InvalidOperationException($"Milestone template with name '{request.Name}' already exists");
                }
            }

            // Update template details
            template.UpdateDetails(request.Name, request.Description, request.UpdatedBy);

            // Update configuration
            if (request.Configuration.Any())
            {
                template.UpdateConfiguration(request.Configuration, request.UpdatedBy);
            }

            // Update metadata
            foreach (var kvp in request.Metadata)
            {
                template.AddMetadata(kvp.Key, kvp.Value);
            }

            // Save changes
            var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template updated successfully: {Id}", updatedTemplate.Id);

            // Map to DTO and return
            var templateDto = _mapper.Map<MilestoneTemplateDto>(updatedTemplate);
            templateDto.TotalPayoutPercentage = updatedTemplate.Steps
                .SelectMany(s => s.PayoutRules)
                .Sum(r => r.PayoutPercentage);
            templateDto.IsValid = !updatedTemplate.GetValidationErrors().Any();
            templateDto.ValidationErrors = updatedTemplate.GetValidationErrors();

            return templateDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone template: {Id}", request.Id);
            throw;
        }
    }
}

public class DeleteMilestoneTemplateCommandHandler : IRequestHandler<DeleteMilestoneTemplateCommand, bool>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly ILogger<DeleteMilestoneTemplateCommandHandler> _logger;

    public DeleteMilestoneTemplateCommandHandler(
        IMilestoneTemplateRepository templateRepository,
        ILogger<DeleteMilestoneTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deleting milestone template: {Id} by {DeletedBy}", request.Id, request.DeletedBy);

            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {request.Id} not found");
            }

            // Check if template can be deleted
            if (!template.CanBeDeleted())
            {
                throw new InvalidOperationException("Template cannot be deleted because it is in use or is a default template");
            }

            await _templateRepository.DeleteAsync(request.Id, cancellationToken);

            _logger.LogInformation("Milestone template deleted successfully: {Id}", request.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting milestone template: {Id}", request.Id);
            throw;
        }
    }
}

public class ActivateMilestoneTemplateCommandHandler : IRequestHandler<ActivateMilestoneTemplateCommand, MilestoneTemplateDto>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<ActivateMilestoneTemplateCommandHandler> _logger;

    public ActivateMilestoneTemplateCommandHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<ActivateMilestoneTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto> Handle(ActivateMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Activating milestone template: {Id} by {UpdatedBy}", request.Id, request.UpdatedBy);

            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {request.Id} not found");
            }

            template.Activate();
            var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template activated successfully: {Id}", updatedTemplate.Id);

            return _mapper.Map<MilestoneTemplateDto>(updatedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating milestone template: {Id}", request.Id);
            throw;
        }
    }
}

public class DeactivateMilestoneTemplateCommandHandler : IRequestHandler<DeactivateMilestoneTemplateCommand, MilestoneTemplateDto>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<DeactivateMilestoneTemplateCommandHandler> _logger;

    public DeactivateMilestoneTemplateCommandHandler(
        IMilestoneTemplateRepository templateRepository,
        IMapper mapper,
        ILogger<DeactivateMilestoneTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto> Handle(DeactivateMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deactivating milestone template: {Id} by {UpdatedBy}", request.Id, request.UpdatedBy);

            var template = await _templateRepository.GetByIdAsync(request.Id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {request.Id} not found");
            }

            template.Deactivate();
            var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template deactivated successfully: {Id}", updatedTemplate.Id);

            return _mapper.Map<MilestoneTemplateDto>(updatedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating milestone template: {Id}", request.Id);
            throw;
        }
    }
}
