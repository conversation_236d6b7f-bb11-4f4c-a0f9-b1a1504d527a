using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// In-memory localization repository implementation
/// </summary>
public class InMemoryLocalizationRepository : ILocalizationRepository
{
    private readonly Dictionary<string, Dictionary<Language, string>> _localizedTexts;
    private readonly Dictionary<string, Dictionary<Language, LocalizedTemplate>> _templates;
    private readonly ILogger<InMemoryLocalizationRepository> _logger;

    public InMemoryLocalizationRepository(ILogger<InMemoryLocalizationRepository> logger)
    {
        _logger = logger;
        _localizedTexts = new Dictionary<string, Dictionary<Language, string>>();
        _templates = new Dictionary<string, Dictionary<Language, LocalizedTemplate>>();
        
        // Initialize with default data
        InitializeDefaultData();
    }

    public async Task<string?> GetTextAsync(string key, Language language, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_localizedTexts.TryGetValue(key, out var languageTexts) &&
                languageTexts.TryGetValue(language, out var text))
            {
                return await Task.FromResult(text);
            }

            return await Task.FromResult<string?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get localized text for key '{Key}' and language '{Language}'", key, language);
            return null;
        }
    }

    public async Task<List<LocalizedTemplate>> GetTemplatesAsync(string category, Language language, CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = new List<LocalizedTemplate>();
            
            foreach (var templateEntry in _templates)
            {
                if (templateEntry.Value.TryGetValue(language, out var template) &&
                    template.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                {
                    templates.Add(template);
                }
            }

            return await Task.FromResult(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates for category '{Category}' and language '{Language}'", category, language);
            return new List<LocalizedTemplate>();
        }
    }

    public async Task<LocalizedTemplate?> GetTemplateAsync(string key, Language language, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_templates.TryGetValue(key, out var languageTemplates) &&
                languageTemplates.TryGetValue(language, out var template))
            {
                return await Task.FromResult(template);
            }

            return await Task.FromResult<LocalizedTemplate?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get template '{Key}' for language '{Language}'", key, language);
            return null;
        }
    }

    public async Task SetTextAsync(string key, Language language, string text, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_localizedTexts.ContainsKey(key))
            {
                _localizedTexts[key] = new Dictionary<Language, string>();
            }

            _localizedTexts[key][language] = text;
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set localized text for key '{Key}' and language '{Language}'", key, language);
            throw;
        }
    }

    public async Task SetTemplateAsync(LocalizedTemplate template, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_templates.ContainsKey(template.Key))
            {
                _templates[template.Key] = new Dictionary<Language, LocalizedTemplate>();
            }

            _templates[template.Key][template.Language] = template;
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set template '{Key}' for language '{Language}'", template.Key, template.Language);
            throw;
        }
    }

    public async Task<List<string>> GetKeysAsync(Language language, CancellationToken cancellationToken = default)
    {
        try
        {
            var keys = new List<string>();
            
            foreach (var entry in _localizedTexts)
            {
                if (entry.Value.ContainsKey(language))
                {
                    keys.Add(entry.Key);
                }
            }

            return await Task.FromResult(keys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get keys for language '{Language}'", language);
            return new List<string>();
        }
    }

    public async Task<LocalizationStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new LocalizationStatistics
            {
                TotalKeys = _localizedTexts.Count
            };

            foreach (var language in Enum.GetValues<Language>())
            {
                var translatedCount = _localizedTexts.Values.Count(v => v.ContainsKey(language));
                stats.TranslationCounts[language] = translatedCount;
                stats.CompletionPercentages[language] = stats.TotalKeys > 0 ? (double)translatedCount / stats.TotalKeys * 100 : 0;
            }

            return await Task.FromResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get localization statistics");
            return new LocalizationStatistics();
        }
    }

    private void InitializeDefaultData()
    {
        // Initialize common localized texts
        InitializeCommonTexts();
        
        // Initialize notification templates
        InitializeNotificationTemplates();
        
        // Initialize trip-related templates
        InitializeTripTemplates();
        
        // Initialize order-related templates
        InitializeOrderTemplates();
    }

    private void InitializeCommonTexts()
    {
        var commonTexts = new Dictionary<string, Dictionary<Language, string>>
        {
            ["welcome"] = new()
            {
                [Language.English] = "Welcome",
                [Language.Hindi] = "स्वागत है",
                [Language.Kannada] = "ಸ್ವಾಗತ"
            },
            ["thank_you"] = new()
            {
                [Language.English] = "Thank you",
                [Language.Hindi] = "धन्यवाद",
                [Language.Kannada] = "ಧನ್ಯವಾದಗಳು"
            },
            ["notification"] = new()
            {
                [Language.English] = "Notification",
                [Language.Hindi] = "सूचना",
                [Language.Kannada] = "ಅಧಿಸೂಚನೆ"
            },
            ["trip_update"] = new()
            {
                [Language.English] = "Trip Update",
                [Language.Hindi] = "यात्रा अपडेट",
                [Language.Kannada] = "ಪ್ರಯಾಣ ಅಪ್ಡೇಟ್"
            },
            ["order_confirmation"] = new()
            {
                [Language.English] = "Order Confirmation",
                [Language.Hindi] = "ऑर्डर पुष्टि",
                [Language.Kannada] = "ಆರ್ಡರ್ ದೃಢೀಕರಣ"
            }
        };

        foreach (var entry in commonTexts)
        {
            _localizedTexts[entry.Key] = entry.Value;
        }
    }

    private void InitializeNotificationTemplates()
    {
        // Welcome notification template
        var welcomeTemplates = new Dictionary<Language, LocalizedTemplate>
        {
            [Language.English] = new()
            {
                Key = "welcome_notification",
                Name = "Welcome Notification",
                Description = "Welcome message for new users",
                Category = "notification",
                Language = Language.English,
                Subject = "Welcome to TLI",
                Content = "Welcome {{userName}} to Transport Logistics India! Your account has been successfully created.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Hindi] = new()
            {
                Key = "welcome_notification",
                Name = "स्वागत सूचना",
                Description = "नए उपयोगकर्ताओं के लिए स्वागत संदेश",
                Category = "notification",
                Language = Language.Hindi,
                Subject = "TLI में आपका स्वागत है",
                Content = "{{userName}} ट्रांसपोर्ट लॉजिस्टिक्स इंडिया में आपका स्वागत है! आपका खाता सफलतापूर्वक बनाया गया है।",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Kannada] = new()
            {
                Key = "welcome_notification",
                Name = "ಸ್ವಾಗತ ಅಧಿಸೂಚನೆ",
                Description = "ಹೊಸ ಬಳಕೆದಾರರಿಗೆ ಸ್ವಾಗತ ಸಂದೇಶ",
                Category = "notification",
                Language = Language.Kannada,
                Subject = "TLI ಗೆ ಸ್ವಾಗತ",
                Content = "{{userName}} ಟ್ರಾನ್ಸ್‌ಪೋರ್ಟ್ ಲಾಜಿಸ್ಟಿಕ್ಸ್ ಇಂಡಿಯಾಗೆ ಸ್ವಾಗತ! ನಿಮ್ಮ ಖಾತೆಯನ್ನು ಯಶಸ್ವಿಯಾಗಿ ರಚಿಸಲಾಗಿದೆ.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        _templates["welcome_notification"] = welcomeTemplates;
    }

    private void InitializeTripTemplates()
    {
        // Trip started template
        var tripStartedTemplates = new Dictionary<Language, LocalizedTemplate>
        {
            [Language.English] = new()
            {
                Key = "trip_started",
                Name = "Trip Started",
                Description = "Notification when trip starts",
                Category = "trip",
                Language = Language.English,
                Subject = "Your trip has started",
                Content = "Your trip from {{origin}} to {{destination}} has started. Driver: {{driverName}}, Vehicle: {{vehicleNumber}}. Estimated arrival: {{estimatedArrival}}.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Hindi] = new()
            {
                Key = "trip_started",
                Name = "यात्रा शुरू",
                Description = "यात्रा शुरू होने पर सूचना",
                Category = "trip",
                Language = Language.Hindi,
                Subject = "आपकी यात्रा शुरू हो गई है",
                Content = "{{origin}} से {{destination}} तक आपकी यात्रा शुरू हो गई है। ड्राइवर: {{driverName}}, वाहन: {{vehicleNumber}}। अनुमानित पहुंच: {{estimatedArrival}}।",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Kannada] = new()
            {
                Key = "trip_started",
                Name = "ಪ್ರಯಾಣ ಪ್ರಾರಂಭ",
                Description = "ಪ್ರಯಾಣ ಪ್ರಾರಂಭವಾದಾಗ ಅಧಿಸೂಚನೆ",
                Category = "trip",
                Language = Language.Kannada,
                Subject = "ನಿಮ್ಮ ಪ್ರಯಾಣ ಪ್ರಾರಂಭವಾಗಿದೆ",
                Content = "{{origin}} ನಿಂದ {{destination}} ಗೆ ನಿಮ್ಮ ಪ್ರಯಾಣ ಪ್ರಾರಂಭವಾಗಿದೆ. ಚಾಲಕ: {{driverName}}, ವಾಹನ: {{vehicleNumber}}. ಅಂದಾಜು ಆಗಮನ: {{estimatedArrival}}.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        _templates["trip_started"] = tripStartedTemplates;

        // Trip completed template
        var tripCompletedTemplates = new Dictionary<Language, LocalizedTemplate>
        {
            [Language.English] = new()
            {
                Key = "trip_completed",
                Name = "Trip Completed",
                Description = "Notification when trip is completed",
                Category = "trip",
                Language = Language.English,
                Subject = "Trip completed successfully",
                Content = "Your trip from {{origin}} to {{destination}} has been completed successfully. Total distance: {{distance}} km, Duration: {{duration}}. Thank you for using TLI!",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Hindi] = new()
            {
                Key = "trip_completed",
                Name = "यात्रा पूर्ण",
                Description = "यात्रा पूर्ण होने पर सूचना",
                Category = "trip",
                Language = Language.Hindi,
                Subject = "यात्रा सफलतापूर्वक पूर्ण",
                Content = "{{origin}} से {{destination}} तक आपकी यात्रा सफलतापूर्वक पूर्ण हो गई है। कुल दूरी: {{distance}} किमी, अवधि: {{duration}}। TLI का उपयोग करने के लिए धन्यवाद!",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Kannada] = new()
            {
                Key = "trip_completed",
                Name = "ಪ್ರಯಾಣ ಪೂರ್ಣ",
                Description = "ಪ್ರಯಾಣ ಪೂರ್ಣಗೊಂಡಾಗ ಅಧಿಸೂಚನೆ",
                Category = "trip",
                Language = Language.Kannada,
                Subject = "ಪ್ರಯಾಣ ಯಶಸ್ವಿಯಾಗಿ ಪೂರ್ಣಗೊಂಡಿದೆ",
                Content = "{{origin}} ನಿಂದ {{destination}} ಗೆ ನಿಮ್ಮ ಪ್ರಯಾಣ ಯಶಸ್ವಿಯಾಗಿ ಪೂರ್ಣಗೊಂಡಿದೆ. ಒಟ್ಟು ದೂರ: {{distance}} ಕಿಮೀ, ಅವಧಿ: {{duration}}. TLI ಬಳಸಿದ್ದಕ್ಕಾಗಿ ಧನ್ಯವಾದಗಳು!",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        _templates["trip_completed"] = tripCompletedTemplates;
    }

    private void InitializeOrderTemplates()
    {
        // Order confirmation template
        var orderConfirmationTemplates = new Dictionary<Language, LocalizedTemplate>
        {
            [Language.English] = new()
            {
                Key = "order_confirmation",
                Name = "Order Confirmation",
                Description = "Order confirmation notification",
                Category = "order",
                Language = Language.English,
                Subject = "Order Confirmed - {{orderNumber}}",
                Content = "Your order {{orderNumber}} has been confirmed. Pickup: {{pickupLocation}} on {{pickupDate}}. Delivery: {{deliveryLocation}} on {{deliveryDate}}. Total amount: ₹{{amount}}.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Hindi] = new()
            {
                Key = "order_confirmation",
                Name = "ऑर्डर पुष्टि",
                Description = "ऑर्डर पुष्टि सूचना",
                Category = "order",
                Language = Language.Hindi,
                Subject = "ऑर्डर पुष्ट - {{orderNumber}}",
                Content = "आपका ऑर्डर {{orderNumber}} पुष्ट हो गया है। पिकअप: {{pickupLocation}} {{pickupDate}} को। डिलीवरी: {{deliveryLocation}} {{deliveryDate}} को। कुल राशि: ₹{{amount}}।",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            [Language.Kannada] = new()
            {
                Key = "order_confirmation",
                Name = "ಆರ್ಡರ್ ದೃಢೀಕರಣ",
                Description = "ಆರ್ಡರ್ ದೃಢೀಕರಣ ಅಧಿಸೂಚನೆ",
                Category = "order",
                Language = Language.Kannada,
                Subject = "ಆರ್ಡರ್ ದೃಢೀಕರಿಸಲಾಗಿದೆ - {{orderNumber}}",
                Content = "ನಿಮ್ಮ ಆರ್ಡರ್ {{orderNumber}} ದೃಢೀಕರಿಸಲಾಗಿದೆ. ಪಿಕಪ್: {{pickupLocation}} {{pickupDate}} ರಂದು. ಡೆಲಿವರಿ: {{deliveryLocation}} {{deliveryDate}} ರಂದು. ಒಟ್ಟು ಮೊತ್ತ: ₹{{amount}}.",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        _templates["order_confirmation"] = orderConfirmationTemplates;
    }
}
