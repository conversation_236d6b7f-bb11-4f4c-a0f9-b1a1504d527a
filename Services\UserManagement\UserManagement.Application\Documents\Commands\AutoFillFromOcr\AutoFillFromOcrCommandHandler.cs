using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.AutoFillFromOcr;

public class AutoFillFromOcrCommandHandler : IRequestHandler<AutoFillFromOcrCommand, AutoFillFromOcrResponse>
{
    private readonly IOcrService _ocrService;
    private readonly ILogger<AutoFillFromOcrCommandHandler> _logger;

    public AutoFillFromOcrCommandHandler(
        IOcrService ocrService,
        ILogger<AutoFillFromOcrCommandHandler> logger)
    {
        _ocrService = ocrService;
        _logger = logger;
    }

    public async Task<AutoFillFromOcrResponse> Handle(AutoFillFromOcrCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Auto-filling form data from OCR for file: {FilePath}, FormType: {FormType}", 
            request.FilePath, request.FormType);

        try
        {
            // Extract data using OCR
            var ocrResult = await _ocrService.ExtractDataAsync(request.FilePath, request.DocumentType, cancellationToken);
            
            if (!ocrResult.Success)
            {
                return new AutoFillFromOcrResponse
                {
                    Success = false,
                    Message = $"OCR extraction failed: {ocrResult.ErrorMessage}",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            // Process auto-fill based on form type
            var autoFillResult = ProcessAutoFill(
                ocrResult.ExtractedData, 
                request.FormType, 
                request.ExistingFormData, 
                request.OverwriteExistingData,
                request.MinimumConfidenceThreshold,
                ocrResult.ConfidenceScore);

            var response = new AutoFillFromOcrResponse
            {
                Success = true,
                Message = "Auto-fill completed successfully",
                AutoFilledData = autoFillResult.AutoFilledData,
                FieldResults = autoFillResult.FieldResults,
                OverallConfidence = ocrResult.ConfidenceScore,
                Warnings = autoFillResult.Warnings,
                RequiresManualReview = autoFillResult.RequiresManualReview,
                ProcessedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Auto-fill completed. Fields processed: {FieldCount}, Confidence: {Confidence}%", 
                autoFillResult.FieldResults.Count, ocrResult.ConfidenceScore * 100);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error auto-filling form data from OCR for file: {FilePath}", request.FilePath);
            return new AutoFillFromOcrResponse
            {
                Success = false,
                Message = $"Error processing auto-fill: {ex.Message}",
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    private AutoFillProcessResult ProcessAutoFill(
        Dictionary<string, object> extractedData,
        string formType,
        Dictionary<string, object> existingFormData,
        bool overwriteExistingData,
        decimal minimumConfidenceThreshold,
        decimal overallConfidence)
    {
        var result = new AutoFillProcessResult
        {
            AutoFilledData = new Dictionary<string, object>(existingFormData),
            FieldResults = new Dictionary<string, FieldAutoFillResult>(),
            Warnings = new List<string>(),
            RequiresManualReview = new List<string>()
        };

        var fieldMappings = GetFieldMappingsForFormType(formType);

        foreach (var mapping in fieldMappings)
        {
            var fieldResult = ProcessField(
                mapping.Key, 
                mapping.Value, 
                extractedData, 
                existingFormData, 
                overwriteExistingData,
                minimumConfidenceThreshold,
                overallConfidence);

            result.FieldResults[mapping.Key] = fieldResult;

            if (fieldResult.WasAutoFilled)
            {
                result.AutoFilledData[mapping.Key] = fieldResult.FinalValue ?? "";
            }

            if (fieldResult.Status == "RequiresReview")
            {
                result.RequiresManualReview.Add($"{fieldResult.FieldName}: {fieldResult.Reason}");
            }

            if (!string.IsNullOrEmpty(fieldResult.Reason) && fieldResult.Status == "Skipped")
            {
                result.Warnings.Add($"{fieldResult.FieldName}: {fieldResult.Reason}");
            }
        }

        return result;
    }

    private FieldAutoFillResult ProcessField(
        string formFieldName,
        string ocrFieldName,
        Dictionary<string, object> extractedData,
        Dictionary<string, object> existingFormData,
        bool overwriteExistingData,
        decimal minimumConfidenceThreshold,
        decimal overallConfidence)
    {
        var result = new FieldAutoFillResult
        {
            FieldName = formFieldName,
            Confidence = overallConfidence
        };

        // Get existing value
        var existingValue = existingFormData.ContainsKey(formFieldName) 
            ? existingFormData[formFieldName]?.ToString() 
            : null;
        result.OriginalValue = existingValue;

        // Get extracted value
        var extractedValue = extractedData.ContainsKey(ocrFieldName) 
            ? extractedData[ocrFieldName]?.ToString() 
            : null;
        result.ExtractedValue = extractedValue;

        // Determine if we should auto-fill
        if (string.IsNullOrEmpty(extractedValue))
        {
            result.Status = "Skipped";
            result.Reason = "No data extracted from document";
            result.FinalValue = existingValue;
            return result;
        }

        if (overallConfidence < minimumConfidenceThreshold)
        {
            result.Status = "RequiresReview";
            result.Reason = $"Confidence ({overallConfidence:P0}) below threshold ({minimumConfidenceThreshold:P0})";
            result.FinalValue = existingValue;
            return result;
        }

        if (!string.IsNullOrEmpty(existingValue) && !overwriteExistingData)
        {
            result.Status = "Skipped";
            result.Reason = "Field already has data and overwrite is disabled";
            result.FinalValue = existingValue;
            return result;
        }

        // Auto-fill the field
        result.WasAutoFilled = true;
        result.WasOverwritten = !string.IsNullOrEmpty(existingValue);
        result.FinalValue = extractedValue;
        result.Status = "AutoFilled";

        return result;
    }

    private Dictionary<string, string> GetFieldMappingsForFormType(string formType)
    {
        return formType.ToLower() switch
        {
            "carrierregistration" => new Dictionary<string, string>
            {
                ["CompanyName"] = "business_name",
                ["GstNumber"] = "gst_number",
                ["PanNumber"] = "pan_number",
                ["RegisteredAddress"] = "address",
                ["ContactPersonName"] = "name",
                ["PhoneNumber"] = "phone",
                ["EmailAddress"] = "email"
            },
            "driverregistration" => new Dictionary<string, string>
            {
                ["FullName"] = "name",
                ["LicenseNumber"] = "license_number",
                ["AadharNumber"] = "aadhar_number",
                ["PanNumber"] = "pan_number",
                ["Address"] = "address",
                ["DateOfBirth"] = "date_of_birth",
                ["PhoneNumber"] = "phone",
                ["LicenseIssueDate"] = "issue_date",
                ["LicenseExpiryDate"] = "expiry_date"
            },
            "vehicleregistration" => new Dictionary<string, string>
            {
                ["VehicleNumber"] = "vehicle_number",
                ["OwnerName"] = "owner_name",
                ["VehicleModel"] = "vehicle_model",
                ["EngineNumber"] = "engine_number",
                ["ChassisNumber"] = "chassis_number",
                ["RegistrationDate"] = "registration_date",
                ["FuelType"] = "fuel_type",
                ["VehicleClass"] = "vehicle_class"
            },
            "vehicleinsurance" => new Dictionary<string, string>
            {
                ["PolicyNumber"] = "policy_number",
                ["InsuredName"] = "insured_name",
                ["VehicleNumber"] = "vehicle_number",
                ["PolicyStartDate"] = "policy_start_date",
                ["PolicyEndDate"] = "policy_end_date",
                ["InsuranceCompany"] = "insurance_company",
                ["PremiumAmount"] = "premium_amount"
            },
            _ => new Dictionary<string, string>()
        };
    }
}

public class AutoFillProcessResult
{
    public Dictionary<string, object> AutoFilledData { get; set; } = new();
    public Dictionary<string, FieldAutoFillResult> FieldResults { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> RequiresManualReview { get; set; } = new();
}
