using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class GstConfigurationController : ControllerBase
{
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly ILogger<GstConfigurationController> _logger;

    public GstConfigurationController(
        ITaxConfigurationService taxConfigurationService,
        ILogger<GstConfigurationController> logger)
    {
        _taxConfigurationService = taxConfigurationService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new GST configuration
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Guid>> CreateGstConfiguration([FromBody] CreateGstConfigurationDto request)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var taxRate = new TaxRate(
                request.TaxRate.Rate,
                request.TaxRate.CalculationMethod,
                request.TaxRate.EffectiveFrom,
                request.TaxRate.EffectiveTo);

            var minimumAmount = new Money(request.MinimumAmount.Amount, request.MinimumAmount.Currency);
            var maximumAmount = new Money(request.MaximumAmount.Amount, request.MaximumAmount.Currency);

            var gstConfiguration = await _taxConfigurationService.CreateGstConfigurationAsync(
                request.Name,
                request.Description,
                request.ServiceCategory,
                jurisdiction,
                request.GstRate,
                taxRate,
                minimumAmount,
                maximumAmount,
                request.CreatedBy,
                request.HsnCode,
                request.IsReverseChargeApplicable,
                request.ReverseChargeConditions);

            return CreatedAtAction(nameof(GetGstConfiguration), new { id = gstConfiguration.Id }, gstConfiguration.Id);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating GST configuration");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating GST configuration");
            return StatusCode(500, "An error occurred while creating GST configuration");
        }
    }

    /// <summary>
    /// Get GST configuration by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<GstConfigurationDto>> GetGstConfiguration(Guid id)
    {
        try
        {
            var gstConfiguration = await _taxConfigurationService.GetGstConfigurationAsync(id);
            if (gstConfiguration == null)
                return NotFound($"GST configuration {id} not found");

            var dto = MapToDto(gstConfiguration);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving GST configuration {Id}", id);
            return StatusCode(500, "An error occurred while retrieving GST configuration");
        }
    }

    /// <summary>
    /// Get all active GST configurations
    /// </summary>
    [HttpGet("active")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<List<GstConfigurationDto>>> GetActiveGstConfigurations()
    {
        try
        {
            var configurations = await _taxConfigurationService.GetActiveGstConfigurationsAsync();
            var dtos = configurations.Select(MapToDto).ToList();
            return Ok(dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active GST configurations");
            return StatusCode(500, "An error occurred while retrieving GST configurations");
        }
    }

    /// <summary>
    /// Get applicable GST configuration for service category and jurisdiction
    /// </summary>
    [HttpGet("applicable")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<GstConfigurationDto>> GetApplicableGstConfiguration(
        [FromQuery] ServiceCategory serviceCategory,
        [FromQuery] string country,
        [FromQuery] string? state = null,
        [FromQuery] string? city = null,
        [FromQuery] DateTime? date = null)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(country, state ?? "", city ?? "");
            var effectiveDate = date ?? DateTime.UtcNow;

            var configuration = await _taxConfigurationService.GetApplicableGstConfigurationAsync(
                serviceCategory, jurisdiction, effectiveDate);

            if (configuration == null)
                return NotFound("No applicable GST configuration found");

            var dto = MapToDto(configuration);
            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving applicable GST configuration");
            return StatusCode(500, "An error occurred while retrieving applicable GST configuration");
        }
    }

    /// <summary>
    /// Calculate GST for given amount and parameters
    /// </summary>
    [HttpPost("calculate")]
    [Authorize(Roles = "Admin,Finance,Broker,TransportCompany")]
    public async Task<ActionResult<MoneyDto>> CalculateGst([FromBody] CalculateGstRequest request)
    {
        try
        {
            var baseAmount = new Money(request.BaseAmount.Amount, request.BaseAmount.Currency);
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var gstAmount = await _taxConfigurationService.CalculateGstAsync(
                baseAmount, request.ServiceCategory, jurisdiction);

            var dto = new MoneyDto
            {
                Amount = gstAmount.Amount,
                Currency = gstAmount.Currency
            };

            return Ok(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating GST");
            return StatusCode(500, "An error occurred while calculating GST");
        }
    }

    /// <summary>
    /// Update GST configuration
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<GstConfigurationDto>> UpdateGstConfiguration(Guid id, [FromBody] UpdateGstConfigurationDto request)
    {
        try
        {
            var jurisdiction = new TaxJurisdiction(
                request.Jurisdiction.Country,
                request.Jurisdiction.State,
                request.Jurisdiction.City,
                request.Jurisdiction.Type);

            var taxRate = new TaxRate(
                request.TaxRate.Rate,
                request.TaxRate.CalculationMethod,
                request.TaxRate.EffectiveFrom,
                request.TaxRate.EffectiveTo);

            var minimumAmount = new Money(request.MinimumAmount.Amount, request.MinimumAmount.Currency);
            var maximumAmount = new Money(request.MaximumAmount.Amount, request.MaximumAmount.Currency);

            var updatedConfiguration = await _taxConfigurationService.UpdateGstConfigurationAsync(
                id,
                request.Name,
                request.Description,
                request.ServiceCategory,
                jurisdiction,
                request.GstRate,
                taxRate,
                minimumAmount,
                maximumAmount,
                request.ModifiedBy,
                request.HsnCode,
                request.IsReverseChargeApplicable,
                request.ReverseChargeConditions);

            var dto = MapToDto(updatedConfiguration);
            return Ok(dto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating GST configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating GST configuration {Id}", id);
            return StatusCode(500, "An error occurred while updating GST configuration");
        }
    }

    /// <summary>
    /// Activate GST configuration
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateGstConfiguration(Guid id, [FromBody] string activatedBy)
    {
        try
        {
            await _taxConfigurationService.ActivateGstConfigurationAsync(id, activatedBy);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while activating GST configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating GST configuration {Id}", id);
            return StatusCode(500, "An error occurred while activating GST configuration");
        }
    }

    /// <summary>
    /// Deactivate GST configuration
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateGstConfiguration(Guid id, [FromBody] DeactivateGstRequest request)
    {
        try
        {
            await _taxConfigurationService.DeactivateGstConfigurationAsync(id, request.DeactivatedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while deactivating GST configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating GST configuration {Id}", id);
            return StatusCode(500, "An error occurred while deactivating GST configuration");
        }
    }

    /// <summary>
    /// Archive GST configuration
    /// </summary>
    [HttpPost("{id}/archive")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ArchiveGstConfiguration(Guid id, [FromBody] ArchiveGstRequest request)
    {
        try
        {
            await _taxConfigurationService.ArchiveGstConfigurationAsync(id, request.ArchivedBy, request.Reason);
            return Ok();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while archiving GST configuration {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving GST configuration {Id}", id);
            return StatusCode(500, "An error occurred while archiving GST configuration");
        }
    }

    private static GstConfigurationDto MapToDto(Domain.Entities.GstConfiguration gstConfiguration)
    {
        return new GstConfigurationDto
        {
            Id = gstConfiguration.Id,
            Name = gstConfiguration.Name,
            Description = gstConfiguration.Description,
            ServiceCategory = gstConfiguration.ServiceCategory,
            Jurisdiction = new TaxJurisdictionDto
            {
                Country = gstConfiguration.Jurisdiction.Country,
                State = gstConfiguration.Jurisdiction.State,
                City = gstConfiguration.Jurisdiction.City,
                Type = gstConfiguration.Jurisdiction.Type
            },
            GstRate = gstConfiguration.GstRate,
            TaxRate = new TaxRateDto
            {
                Rate = gstConfiguration.TaxRate.Rate,
                CalculationMethod = gstConfiguration.TaxRate.CalculationMethod,
                EffectiveFrom = gstConfiguration.TaxRate.EffectiveFrom,
                EffectiveTo = gstConfiguration.TaxRate.EffectiveTo
            },
            Status = gstConfiguration.Status,
            HsnCode = gstConfiguration.HsnCode,
            MinimumAmount = new MoneyDto
            {
                Amount = gstConfiguration.MinimumAmount.Amount,
                Currency = gstConfiguration.MinimumAmount.Currency
            },
            MaximumAmount = new MoneyDto
            {
                Amount = gstConfiguration.MaximumAmount.Amount,
                Currency = gstConfiguration.MaximumAmount.Currency
            },
            IsReverseChargeApplicable = gstConfiguration.IsReverseChargeApplicable,
            ReverseChargeConditions = gstConfiguration.ReverseChargeConditions,
            CreatedBy = gstConfiguration.CreatedBy,
            ModifiedBy = gstConfiguration.ModifiedBy,
            ModifiedAt = gstConfiguration.ModifiedAt,
            CreatedAt = gstConfiguration.CreatedAt,
            UpdatedAt = gstConfiguration.UpdatedAt,
            History = gstConfiguration.History.Select(h => new GstConfigurationHistoryDto
            {
                Id = h.Id,
                GstConfigurationId = h.GstConfigurationId,
                Action = h.Action,
                Details = h.Details,
                ModifiedBy = h.ModifiedBy,
                ModifiedAt = h.ModifiedAt
            }).ToList()
        };
    }
}

public class CalculateGstRequest
{
    public CreateMoneyDto BaseAmount { get; set; } = new();
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
}

public class DeactivateGstRequest
{
    public string DeactivatedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

public class ArchiveGstRequest
{
    public string ArchivedBy { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}
