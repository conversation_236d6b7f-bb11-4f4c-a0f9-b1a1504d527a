using MediatR;

namespace TripManagement.Application.Commands.SendEmergencyAlert;

public class SendEmergencyAlertCommand : IRequest<SendEmergencyAlertResponse>
{
    public Guid DriverId { get; set; }
    public Guid? TripId { get; set; }
    public string AlertType { get; set; } = string.Empty; // SOS, Accident, Breakdown, Medical, Security
    public string? Message { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? DeviceId { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string Severity { get; set; } = "High"; // Low, Medium, High, Critical
}

public class SendEmergencyAlertResponse
{
    public bool IsSuccess { get; set; }
    public string AlertId { get; set; } = string.Empty;
    public DateTime AlertTime { get; set; }
    public List<string> NotifiedContacts { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string Status { get; set; } = string.Empty; // Sent, Failed, Pending
    public int EstimatedResponseTimeMinutes { get; set; }
}
