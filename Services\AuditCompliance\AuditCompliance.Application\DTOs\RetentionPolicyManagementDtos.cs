using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Application.DTOs
{
    /// <summary>
    /// Retention policy management DTO
    /// </summary>
    public class RetentionPolicyManagementDto
    {
        public Guid Id { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public PolicyPriority Priority { get; set; }
        public bool IsActive { get; set; }
        public bool RequiresApproval { get; set; }
        public PolicyApprovalStatus ApprovalStatus { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? LastExecuted { get; set; }
        public int ExecutionCount { get; set; }
        public int RecordsAffected { get; set; }
        public List<PolicyExecutionDto> ExecutionHistory { get; set; } = new();
        public PolicyImpactAnalysisDto? ImpactAnalysis { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public PolicyExecutionSummaryDto ExecutionSummary { get; set; } = new();
    }

    /// <summary>
    /// Retention policy DTO
    /// </summary>
    public class RetentionPolicyDto
    {
        public TimeSpan RetentionPeriod { get; set; }
        public bool AutoDelete { get; set; }
        public bool RequireApproval { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
    }

    /// <summary>
    /// Policy execution DTO
    /// </summary>
    public class PolicyExecutionDto
    {
        public DateTime ExecutedAt { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsDeleted { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Policy impact analysis DTO
    /// </summary>
    public class PolicyImpactAnalysisDto
    {
        public int EstimatedRecordsAffected { get; set; }
        public Dictionary<string, int> RecordsByEntityType { get; set; } = new();
        public Dictionary<string, int> RecordsByModule { get; set; } = new();
        public TimeSpan EstimatedExecutionTime { get; set; }
        public DateTime AnalyzedAt { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Policy execution summary DTO
    /// </summary>
    public class PolicyExecutionSummaryDto
    {
        public int TotalExecutions { get; set; }
        public int TotalRecordsAffected { get; set; }
        public DateTime? LastExecuted { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public double AverageExecutionTimeSeconds { get; set; }
        public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;
    }

    /// <summary>
    /// Create retention policy request DTO
    /// </summary>
    public class CreateRetentionPolicyRequestDto
    {
        public string PolicyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public PolicyPriority Priority { get; set; } = PolicyPriority.Medium;
        public bool RequiresApproval { get; set; } = true;
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// Update retention policy request DTO
    /// </summary>
    public class UpdateRetentionPolicyRequestDto
    {
        public string PolicyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public PolicyPriority Priority { get; set; } = PolicyPriority.Medium;
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// Policy approval request DTO
    /// </summary>
    public class PolicyApprovalRequestDto
    {
        public bool Approve { get; set; }
        public string? Notes { get; set; }
        public string? RejectionReason { get; set; }
    }

    /// <summary>
    /// Policy search request DTO
    /// </summary>
    public class PolicySearchRequestDto
    {
        public string? SearchTerm { get; set; }
        public PolicyApprovalStatus? ApprovalStatus { get; set; }
        public bool? IsActive { get; set; }
        public PolicyPriority? Priority { get; set; }
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// Policy search result DTO
    /// </summary>
    public class PolicySearchResultDto
    {
        public List<RetentionPolicyManagementDto> Policies { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
        public Dictionary<PolicyApprovalStatus, int> StatusCounts { get; set; } = new();
        public Dictionary<PolicyPriority, int> PriorityCounts { get; set; } = new();
        public Dictionary<string, int> TagCounts { get; set; } = new();
    }

    /// <summary>
    /// Retention status dashboard DTO
    /// </summary>
    public class RetentionStatusDashboardDto
    {
        public RetentionOverviewDto Overview { get; set; } = new();
        public List<PolicyStatusSummaryDto> PolicyStatuses { get; set; } = new();
        public List<RecentExecutionDto> RecentExecutions { get; set; } = new();
        public List<UpcomingExecutionDto> UpcomingExecutions { get; set; } = new();
        public RetentionTrendsDto Trends { get; set; } = new();
        public List<RetentionAlertDto> Alerts { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    /// <summary>
    /// Retention overview DTO
    /// </summary>
    public class RetentionOverviewDto
    {
        public int TotalPolicies { get; set; }
        public int ActivePolicies { get; set; }
        public int PendingApproval { get; set; }
        public int TotalRecordsManaged { get; set; }
        public int RecordsDeletedToday { get; set; }
        public int RecordsDeletedThisMonth { get; set; }
        public double AverageRetentionPeriodDays { get; set; }
        public DateTime LastPolicyExecution { get; set; }
        public int FailedExecutionsToday { get; set; }
    }

    /// <summary>
    /// Policy status summary DTO
    /// </summary>
    public class PolicyStatusSummaryDto
    {
        public Guid PolicyId { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public PolicyApprovalStatus Status { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastExecuted { get; set; }
        public int RecordsAffected { get; set; }
        public bool HasErrors { get; set; }
        public string? LastError { get; set; }
        public DateTime? NextExecution { get; set; }
    }

    /// <summary>
    /// Recent execution DTO
    /// </summary>
    public class RecentExecutionDto
    {
        public Guid PolicyId { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public DateTime ExecutedAt { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsDeleted { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Upcoming execution DTO
    /// </summary>
    public class UpcomingExecutionDto
    {
        public Guid PolicyId { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public DateTime ScheduledAt { get; set; }
        public int EstimatedRecordsToProcess { get; set; }
        public TimeSpan EstimatedExecutionTime { get; set; }
        public PolicyPriority Priority { get; set; }
    }

    /// <summary>
    /// Retention trends DTO
    /// </summary>
    public class RetentionTrendsDto
    {
        public Dictionary<string, int> RecordsDeletedByDay { get; set; } = new();
        public Dictionary<string, int> RecordsDeletedByMonth { get; set; } = new();
        public Dictionary<string, int> ExecutionsByStatus { get; set; } = new();
        public Dictionary<string, double> AverageExecutionTimeByPolicy { get; set; } = new();
        public Dictionary<string, int> RecordsByEntityType { get; set; } = new();
        public Dictionary<string, int> RecordsByModule { get; set; } = new();
    }

    /// <summary>
    /// Retention alert DTO
    /// </summary>
    public class RetentionAlertDto
    {
        public string AlertId { get; set; } = string.Empty;
        public RetentionAlertType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public RetentionAlertSeverity Severity { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsAcknowledged { get; set; }
        public Guid? PolicyId { get; set; }
        public string? PolicyName { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Policy validation result DTO
    /// </summary>
    public class PolicyValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<PolicyValidationErrorDto> Errors { get; set; } = new();
        public List<PolicyValidationWarningDto> Warnings { get; set; } = new();
        public PolicyImpactAnalysisDto? ImpactAnalysis { get; set; }
        public Dictionary<string, object> ValidationMetadata { get; set; } = new();
    }

    /// <summary>
    /// Policy validation error DTO
    /// </summary>
    public class PolicyValidationErrorDto
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Field { get; set; }
        public string? Suggestion { get; set; }
    }

    /// <summary>
    /// Policy validation warning DTO
    /// </summary>
    public class PolicyValidationWarningDto
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Field { get; set; }
        public string? Recommendation { get; set; }
    }

    /// <summary>
    /// Retention alert type enumeration
    /// </summary>
    public enum RetentionAlertType
    {
        PolicyExecutionFailed = 0,
        PolicyApprovalRequired = 1,
        HighVolumeExecution = 2,
        PolicyConflict = 3,
        ComplianceViolation = 4,
        SystemError = 5
    }

    /// <summary>
    /// Retention alert severity enumeration
    /// </summary>
    public enum RetentionAlertSeverity
    {
        Info = 0,
        Warning = 1,
        Error = 2,
        Critical = 3
    }
}
