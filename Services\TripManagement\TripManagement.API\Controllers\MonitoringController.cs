using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using TripManagement.Application.Interfaces;
using System.Diagnostics;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MonitoringController : ControllerBase
{
    private readonly HealthCheckService _healthCheckService;
    private readonly IMetricsCollectionService _metricsService;
    private readonly IAlertingService _alertingService;
    private readonly ILogger<MonitoringController> _logger;

    public MonitoringController(
        HealthCheckService healthCheckService,
        IMetricsCollectionService metricsService,
        IAlertingService alertingService,
        ILogger<MonitoringController> logger)
    {
        _healthCheckService = healthCheckService;
        _metricsService = metricsService;
        _alertingService = alertingService;
        _logger = logger;
    }

    /// <summary>
    /// Get service health status
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult> GetHealth()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync();

            var response = new
            {
                Status = healthReport.Status.ToString(),
                TotalDuration = healthReport.TotalDuration.TotalMilliseconds,
                Entries = healthReport.Entries.Select(e => new
                {
                    Name = e.Key,
                    Status = e.Value.Status.ToString(),
                    Duration = e.Value.Duration.TotalMilliseconds,
                    Description = e.Value.Description,
                    Data = e.Value.Data,
                    Exception = e.Value.Exception?.Message
                })
            };

            var statusCode = healthReport.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200,
                HealthStatus.Unhealthy => 503,
                _ => 500
            };

            return StatusCode(statusCode, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking health status");
            return StatusCode(500, new { Status = "Unhealthy", Error = ex.Message });
        }
    }

    /// <summary>
    /// Get service metrics
    /// </summary>
    [HttpGet("metrics")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult> GetMetrics()
    {
        try
        {
            var metrics = await _metricsService.CollectMetricsAsync();
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting metrics");
            return StatusCode(500, "An error occurred while collecting metrics");
        }
    }

    /// <summary>
    /// Get service readiness status
    /// </summary>
    [HttpGet("ready")]
    public async Task<ActionResult> GetReadiness()
    {
        try
        {
            // Check if service is ready to handle requests
            var healthReport = await _healthCheckService.CheckHealthAsync();

            var isReady = healthReport.Status == HealthStatus.Healthy ||
                         healthReport.Status == HealthStatus.Degraded;

            var response = new
            {
                Ready = isReady,
                Status = healthReport.Status.ToString(),
                Timestamp = DateTime.UtcNow
            };

            return isReady ? Ok(response) : StatusCode(503, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking readiness status");
            return StatusCode(503, new { Ready = false, Error = ex.Message });
        }
    }

    /// <summary>
    /// Get service liveness status
    /// </summary>
    [HttpGet("live")]
    public ActionResult GetLiveness()
    {
        try
        {
            // Simple liveness check - service is running
            return Ok(new
            {
                Alive = true,
                Timestamp = DateTime.UtcNow,
                Version = GetType().Assembly.GetName().Version?.ToString()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking liveness status");
            return StatusCode(500, new { Alive = false, Error = ex.Message });
        }
    }

    /// <summary>
    /// Trigger manual metrics collection and alerting
    /// </summary>
    [HttpPost("alerts/check")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> TriggerAlertCheck()
    {
        try
        {
            var metrics = await _metricsService.CollectMetricsAsync();
            await _alertingService.MonitorMetricsAndAlertAsync(metrics);

            return Ok(new
            {
                Message = "Alert check completed successfully",
                MetricsCollectedAt = metrics.CollectedAt,
                CheckedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual alert check");
            return StatusCode(500, "An error occurred during alert check");
        }
    }

    /// <summary>
    /// Send test alert
    /// </summary>
    [HttpPost("alerts/test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> SendTestAlert([FromBody] TestAlertRequest request)
    {
        try
        {
            switch (request.Level.ToLower())
            {
                case "critical":
                    await _alertingService.SendCriticalAlertAsync(
                        request.Title ?? "Test Critical Alert",
                        request.Message ?? "This is a test critical alert from Trip Management Service",
                        new Dictionary<string, object> { ["test"] = true });
                    break;
                case "warning":
                    await _alertingService.SendWarningAlertAsync(
                        request.Title ?? "Test Warning Alert",
                        request.Message ?? "This is a test warning alert from Trip Management Service",
                        new Dictionary<string, object> { ["test"] = true });
                    break;
                case "info":
                default:
                    await _alertingService.SendInfoAlertAsync(
                        request.Title ?? "Test Info Alert",
                        request.Message ?? "This is a test info alert from Trip Management Service",
                        new Dictionary<string, object> { ["test"] = true });
                    break;
            }

            return Ok(new { Message = $"Test {request.Level} alert sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test alert");
            return StatusCode(500, "An error occurred while sending test alert");
        }
    }

    /// <summary>
    /// Get service information
    /// </summary>
    [HttpGet("info")]
    public ActionResult GetServiceInfo()
    {
        try
        {
            var assembly = GetType().Assembly;
            var version = assembly.GetName().Version;
            var buildDate = System.IO.File.GetLastWriteTime(assembly.Location);

            return Ok(new
            {
                ServiceName = "Trip Management Service",
                Version = version?.ToString(),
                BuildDate = buildDate,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                MachineName = Environment.MachineName,
                ProcessId = Environment.ProcessId,
                StartTime = Process.GetCurrentProcess().StartTime,
                WorkingSet = Environment.WorkingSet,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service info");
            return StatusCode(500, "An error occurred while retrieving service information");
        }
    }
}

// Request DTOs
public record TestAlertRequest
{
    public string Level { get; init; } = "info";
    public string? Title { get; init; }
    public string? Message { get; init; }
}
