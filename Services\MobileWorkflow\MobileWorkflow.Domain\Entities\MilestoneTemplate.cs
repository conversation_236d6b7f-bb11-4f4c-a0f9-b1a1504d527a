
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class MilestoneTemplate : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Type { get; private set; } // Trip, Order, Project, etc.
    public string Category { get; private set; } // Logistics, Delivery, Pickup, etc.
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public int UsageCount { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<MilestoneStep> Steps { get; private set; } = new List<MilestoneStep>();
    public virtual ICollection<RoleTemplateMappings> RoleMappings { get; private set; } = new List<RoleTemplateMappings>();

    private MilestoneTemplate() { } // EF Core

    public MilestoneTemplate(
        string name,
        string description,
        string type,
        string category,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsDefault = false;
        UsageCount = 0;
        Configuration = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new MilestoneTemplateCreatedEvent(Id, Name, Type, Category, CreatedBy));
    }

    public void UpdateDetails(string name, string description, string updatedBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        UpdatedBy = updatedBy ?? throw new ArgumentNullException(nameof(updatedBy));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateDeactivatedEvent(Id, Name));
    }

    public void SetAsDefault()
    {
        IsDefault = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateSetAsDefaultEvent(Id, Name, Type, Category));
    }

    public void RemoveAsDefault()
    {
        IsDefault = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateRemovedAsDefaultEvent(Id, Name, Type, Category));
    }

    public MilestoneStep AddStep(string name, string description, int sequenceNumber, bool isRequired = true)
    {
        if (Steps.Any(s => s.SequenceNumber == sequenceNumber))
            throw new InvalidOperationException($"Step with sequence number {sequenceNumber} already exists");

        var step = new MilestoneStep(Id, name, description, sequenceNumber, isRequired);
        Steps.Add(step);
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateStepAddedEvent(Id, Name, name, sequenceNumber));

        return step;
    }

    public void RemoveStep(Guid stepId)
    {
        var step = Steps.FirstOrDefault(s => s.Id == stepId);
        if (step == null)
            throw new InvalidOperationException($"Step with ID {stepId} not found");

        Steps.Remove(step);
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateStepRemovedEvent(Id, Name, step.Name));
    }

    public void ReorderSteps(Dictionary<Guid, int> stepSequenceMap)
    {
        foreach (var kvp in stepSequenceMap)
        {
            var step = Steps.FirstOrDefault(s => s.Id == kvp.Key);
            if (step != null)
            {
                step.UpdateSequenceNumber(kvp.Value);
            }
        }

        UpdatedAt = DateTime.UtcNow;
        AddDomainEvent(new MilestoneTemplateStepsReorderedEvent(Id, Name));
    }

    public void IncrementUsage()
    {
        UsageCount++;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateUsageIncrementedEvent(Id, Name, UsageCount));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneTemplateConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool ValidatePayoutPercentages()
    {
        var totalPercentage = Steps
            .SelectMany(s => s.PayoutRules)
            .Sum(r => r.PayoutPercentage);

        return Math.Abs(totalPercentage - 100.0m) < 0.01m; // Allow for small floating point differences
    }

    public List<string> GetValidationErrors()
    {
        var errors = new List<string>();

        if (Steps.Count == 0)
        {
            errors.Add("Template must have at least one milestone step");
        }

        if (!ValidatePayoutPercentages())
        {
            errors.Add("Total payout percentages must equal 100%");
        }

        var duplicateSequences = Steps
            .GroupBy(s => s.SequenceNumber)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        if (duplicateSequences.Any())
        {
            errors.Add($"Duplicate sequence numbers found: {string.Join(", ", duplicateSequences)}");
        }

        return errors;
    }

    public bool CanBeDeleted()
    {
        return UsageCount == 0 && !IsDefault;
    }
}



