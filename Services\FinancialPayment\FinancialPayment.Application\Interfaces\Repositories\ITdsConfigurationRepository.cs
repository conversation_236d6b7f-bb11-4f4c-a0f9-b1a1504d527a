using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface ITdsConfigurationRepository
{
    Task<TdsConfiguration?> GetByIdAsync(Guid id);
    Task<List<TdsConfiguration>> GetAllAsync();
    Task<List<TdsConfiguration>> GetActiveAsync();
    Task<List<TdsConfiguration>> GetByStatusAsync(TaxConfigurationStatus status);
    Task<List<TdsConfiguration>> GetBySectionAsync(TdsSection section);
    Task<List<TdsConfiguration>> GetByEntityTypeAsync(EntityType entityType);
    Task<List<TdsConfiguration>> GetBySectionAndEntityTypeAsync(TdsSection section, EntityType entityType);
    Task<List<TdsConfiguration>> GetEffectiveOnDateAsync(DateTime date);
    Task<TdsConfiguration?> GetApplicableConfigurationAsync(TdsSection section, EntityType entityType, DateTime date, bool hasPan = true);
    Task<List<TdsConfiguration>> GetByTaxRateRangeAsync(decimal minRate, decimal maxRate);
    Task<List<TdsConfiguration>> GetRequiringPanAsync();
    Task<List<TdsConfiguration>> GetWithHigherRateWithoutPanAsync();
    Task<List<TdsConfiguration>> GetByThresholdAmountAsync(Money thresholdAmount);
    Task<List<TdsConfiguration>> GetByCreatedByAsync(string createdBy);
    Task<List<TdsConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<TdsConfiguration> AddAsync(TdsConfiguration tdsConfiguration);
    Task UpdateAsync(TdsConfiguration tdsConfiguration);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null);
    Task<bool> ExistsBySectionAndEntityTypeAsync(TdsSection section, EntityType entityType, Guid? excludeId = null);
    Task<int> GetCountByStatusAsync(TaxConfigurationStatus status);
    Task<List<TdsConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50);
    Task<List<TdsConfigurationHistory>> GetHistoryAsync(Guid tdsConfigurationId);
}
