using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Queries.Content;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ContentManagement.API.Controllers;

/// <summary>
/// Controller for content management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ContentController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<ContentController> _logger;

    public ContentController(IMediator mediator, ILogger<ContentController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get content by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContentDto>> GetById(
        Guid id,
        [FromQuery] bool includeVersions = false,
        [FromQuery] bool includeAttachments = false)
    {
        var query = new GetContentByIdQuery
        {
            Id = id,
            IncludeVersions = includeVersions,
            IncludeAttachments = includeAttachments,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Get content by slug
    /// </summary>
    [HttpGet("slug/{slug}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContentDto>> GetBySlug(
        string slug,
        [FromQuery] bool includeVersions = false,
        [FromQuery] bool includeAttachments = false)
    {
        var query = new GetContentBySlugQuery
        {
            Slug = slug,
            IncludeVersions = includeVersions,
            IncludeAttachments = includeAttachments,
            UserRoles = GetUserRoles(),
            IsAuthenticated = User.Identity?.IsAuthenticated ?? false
        };

        var result = await _mediator.Send(query);
        
        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Search content with filters
    /// </summary>
    [HttpGet("search")]
    [AllowAnonymous]
    public async Task<ActionResult<ContentSearchResultDto>> Search([FromQuery] SearchContentQuery query)
    {
        query.UserRoles = GetUserRoles();
        query.IsAuthenticated = User.Identity?.IsAuthenticated ?? false;

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Create new content
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentDto>> Create([FromBody] CreateContentCommand command)
    {
        command.CreatedBy = GetUserId();
        command.CreatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Update existing content
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentDto>> Update(Guid id, [FromBody] UpdateContentCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Update content visibility
    /// </summary>
    [HttpPut("{id:guid}/visibility")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentDto>> UpdateVisibility(Guid id, [FromBody] UpdateContentVisibilityCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Update content sort order
    /// </summary>
    [HttpPut("{id:guid}/sort-order")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentDto>> UpdateSortOrder(Guid id, [FromBody] UpdateContentSortOrderCommand command)
    {
        command.Id = id;
        command.UpdatedBy = GetUserId();
        command.UpdatedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Submit content for review
    /// </summary>
    [HttpPost("{id:guid}/submit-for-review")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentDto>> SubmitForReview(Guid id)
    {
        var command = new SubmitContentForReviewCommand
        {
            Id = id,
            SubmittedBy = GetUserId(),
            SubmittedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Approve content
    /// </summary>
    [HttpPost("{id:guid}/approve")]
    [Authorize(Roles = "Admin,ContentApprover")]
    public async Task<ActionResult<ContentDto>> Approve(Guid id)
    {
        var command = new ApproveContentCommand
        {
            Id = id,
            ApprovedBy = GetUserId(),
            ApprovedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Reject content
    /// </summary>
    [HttpPost("{id:guid}/reject")]
    [Authorize(Roles = "Admin,ContentApprover")]
    public async Task<ActionResult<ContentDto>> Reject(Guid id, [FromBody] RejectContentCommand command)
    {
        command.Id = id;
        command.RejectedBy = GetUserId();
        command.RejectedByName = GetUserName();

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Publish content
    /// </summary>
    [HttpPost("{id:guid}/publish")]
    [Authorize(Roles = "Admin,ContentApprover")]
    public async Task<ActionResult<ContentDto>> Publish(Guid id)
    {
        var command = new PublishContentCommand
        {
            Id = id,
            PublishedBy = GetUserId(),
            PublishedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Unpublish content
    /// </summary>
    [HttpPost("{id:guid}/unpublish")]
    [Authorize(Roles = "Admin,ContentApprover")]
    public async Task<ActionResult<ContentDto>> Unpublish(Guid id)
    {
        var command = new UnpublishContentCommand
        {
            Id = id,
            UnpublishedBy = GetUserId(),
            UnpublishedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Archive content
    /// </summary>
    [HttpPost("{id:guid}/archive")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ContentDto>> Archive(Guid id)
    {
        var command = new ArchiveContentCommand
        {
            Id = id,
            ArchivedBy = GetUserId(),
            ArchivedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Delete content
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> Delete(Guid id)
    {
        var command = new DeleteContentCommand
        {
            Id = id,
            DeletedBy = GetUserId(),
            DeletedByName = GetUserName()
        };

        var result = await _mediator.Send(command);
        
        if (result)
            return NoContent();
        
        return NotFound();
    }

    /// <summary>
    /// Get content versions
    /// </summary>
    [HttpGet("{id:guid}/versions")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<List<ContentVersionDto>>> GetVersions(Guid id, [FromQuery] bool publishedOnly = false)
    {
        var query = new GetContentVersionsQuery
        {
            ContentId = id,
            PublishedOnly = publishedOnly
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get content pending approval
    /// </summary>
    [HttpGet("pending-approval")]
    [Authorize(Roles = "Admin,ContentApprover")]
    public async Task<ActionResult<ContentSearchResultDto>> GetPendingApproval([FromQuery] GetContentPendingApprovalQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get content statistics
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,ContentManager")]
    public async Task<ActionResult<ContentStatisticsDto>> GetStatistics([FromQuery] GetContentStatisticsQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }

    private List<string> GetUserRoles()
    {
        return User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
    }
}
