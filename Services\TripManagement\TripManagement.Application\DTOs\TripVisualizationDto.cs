using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs;

// ===== ENHANCED TRIP STATUS VISUALIZATION DTOs =====

public class TripStatusVisualizationDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public TripStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? EstimatedCompletionDate { get; set; }
    public DateTime? ActualCompletionDate { get; set; }
    public decimal OverallProgress { get; set; }
    public TimelineVisualizationDto Timeline { get; set; } = new();
    public List<MilestoneVisualizationDto> Milestones { get; set; } = new();
    public List<TripLegVisualizationDto> Legs { get; set; } = new();
    public List<NotificationDto> ActiveNotifications { get; set; } = new();
    public List<DelayAlertDto> DelayAlerts { get; set; } = new();
    public TripMetricsDto Metrics { get; set; } = new();
    public LocationTrackingDto CurrentLocation { get; set; } = new();
    public List<ExceptionVisualizationDto> Exceptions { get; set; } = new();
    public bool HasCriticalIssues { get; set; }
    public string? NextAction { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class TimelineVisualizationDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalDays { get; set; }
    public int ElapsedDays { get; set; }
    public int RemainingDays { get; set; }
    public decimal TimelineProgress { get; set; }
    public List<TimelineEventDto> Events { get; set; } = new();
    public List<TimelineMilestoneDto> Milestones { get; set; } = new();
    public bool IsOnSchedule { get; set; }
    public TimeSpan? DelayDuration { get; set; }
    public string TimelineStatus { get; set; } = string.Empty; // OnTime, Delayed, Critical
}

public class TimelineEventDto
{
    public Guid EventId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime EventDate { get; set; }
    public string Status { get; set; } = string.Empty; // Completed, InProgress, Pending, Overdue
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TimelineMilestoneDto
{
    public Guid MilestoneId { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime ScheduledDate { get; set; }
    public DateTime? ActualDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal Progress { get; set; }
    public bool IsCritical { get; set; }
    public bool IsOverdue { get; set; }
    public TimeSpan? Delay { get; set; }
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
}

public class MilestoneVisualizationDto
{
    public Guid MilestoneId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Pickup, Delivery, Checkpoint, Custom
    public DateTime ScheduledDateTime { get; set; }
    public DateTime? ActualDateTime { get; set; }
    public string Status { get; set; } = string.Empty; // Pending, InProgress, Completed, Delayed, Failed
    public decimal Progress { get; set; }
    public LocationDto Location { get; set; } = new();
    public List<RequirementDto> Requirements { get; set; } = new();
    public List<DocumentDto> Documents { get; set; } = new();
    public NotificationSettingsDto NotificationSettings { get; set; } = new();
    public bool RequiresApproval { get; set; }
    public string? ApprovalStatus { get; set; }
    public bool IsOverdue { get; set; }
    public TimeSpan? DelayDuration { get; set; }
    public string? DelayReason { get; set; }
    public List<string> AvailableActions { get; set; } = new();
    public VisualizationStyleDto Style { get; set; } = new();
}

public class TripLegVisualizationDto
{
    public Guid LegId { get; set; }
    public int SequenceNumber { get; set; }
    public string LegName { get; set; } = string.Empty;
    public LocationDto OriginLocation { get; set; } = new();
    public LocationDto DestinationLocation { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public decimal Progress { get; set; }
    public DateTime ScheduledStartTime { get; set; }
    public DateTime ScheduledEndTime { get; set; }
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public decimal EstimatedDistance { get; set; }
    public decimal ActualDistance { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public TimeSpan? ActualDuration { get; set; }
    public VehicleInfoDto? AssignedVehicle { get; set; }
    public DriverInfoDto? AssignedDriver { get; set; }
    public List<MilestoneVisualizationDto> LegMilestones { get; set; } = new();
    public RouteVisualizationDto Route { get; set; } = new();
    public bool HasIssues { get; set; }
    public List<string> Issues { get; set; } = new();
}

public class NotificationDto
{
    public Guid NotificationId { get; set; }
    public string Type { get; set; } = string.Empty; // Info, Warning, Error, Success
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public bool IsRead { get; set; }
    public bool RequiresAction { get; set; }
    public string? ActionUrl { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<string> Recipients { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
}

public class DelayAlertDto
{
    public Guid AlertId { get; set; }
    public string AlertType { get; set; } = string.Empty; // MinorDelay, MajorDelay, CriticalDelay
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TimeSpan DelayDuration { get; set; }
    public DateTime DetectedAt { get; set; }
    public string Severity { get; set; } = string.Empty; // Low, Medium, High, Critical
    public string? Reason { get; set; }
    public List<string> ImpactedMilestones { get; set; } = new();
    public List<string> SuggestedActions { get; set; } = new();
    public bool IsEscalated { get; set; }
    public DateTime? EscalatedAt { get; set; }
    public List<string> EscalatedTo { get; set; } = new();
    public string Status { get; set; } = string.Empty; // Active, Acknowledged, Resolved
    public DateTime? ResolvedAt { get; set; }
}

public class TripMetricsDto
{
    public decimal OnTimePerformance { get; set; }
    public decimal DistanceEfficiency { get; set; }
    public decimal TimeEfficiency { get; set; }
    public decimal CostEfficiency { get; set; }
    public int TotalMilestones { get; set; }
    public int CompletedMilestones { get; set; }
    public int OverdueMilestones { get; set; }
    public int TotalExceptions { get; set; }
    public int ResolvedExceptions { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty; // A, B, C, D, F
    public List<MetricTrendDto> Trends { get; set; } = new();
}

public class LocationTrackingDto
{
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public decimal Speed { get; set; }
    public string Direction { get; set; } = string.Empty;
    public decimal Accuracy { get; set; }
    public bool IsMoving { get; set; }
    public TimeSpan? EstimatedTimeToNextMilestone { get; set; }
    public decimal DistanceToNextMilestone { get; set; }
    public string? NextMilestoneName { get; set; }
    public List<GeofenceDto> NearbyGeofences { get; set; } = new();
}

public class ExceptionVisualizationDto
{
    public Guid ExceptionId { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime OccurredAt { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Open, InProgress, Resolved
    public string? Resolution { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public Guid? ResolvedBy { get; set; }
    public List<string> ImpactedComponents { get; set; } = new();
    public TimeSpan? EstimatedResolutionTime { get; set; }
    public List<string> SuggestedActions { get; set; } = new();
    public bool RequiresEscalation { get; set; }
    public VisualizationStyleDto Style { get; set; } = new();
}

public class RequirementDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletedBy { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class DocumentDto
{
    public Guid DocumentId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsUploaded { get; set; }
    public DateTime? UploadedAt { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class NotificationSettingsDto
{
    public bool EnableEmailNotifications { get; set; }
    public bool EnableSmsNotifications { get; set; }
    public bool EnablePushNotifications { get; set; }
    public List<string> NotificationTriggers { get; set; } = new();
    public int NotificationLeadTimeMinutes { get; set; }
    public List<string> Recipients { get; set; } = new();
}

public class VisualizationStyleDto
{
    public string Color { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string BorderColor { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public Dictionary<string, object> CustomStyles { get; set; } = new();
}

public class LocationDto
{
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
}

public class VehicleInfoDto
{
    public Guid VehicleId { get; set; }
    public string VehicleNumber { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
}

public class DriverInfoDto
{
    public Guid DriverId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
}

public class RouteVisualizationDto
{
    public List<LocationDto> RoutePoints { get; set; } = new();
    public decimal TotalDistance { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public List<string> Waypoints { get; set; } = new();
    public string RouteType { get; set; } = string.Empty; // Fastest, Shortest, Scenic
}

public class GeofenceDto
{
    public Guid GeofenceId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal CenterLatitude { get; set; }
    public decimal CenterLongitude { get; set; }
    public decimal Radius { get; set; }
    public bool IsActive { get; set; }
}

public class MetricTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public List<TrendDataPoint> DataPoints { get; set; } = new();
    public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
    public decimal TrendPercentage { get; set; }
}

public class TrendDataPoint
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public string? Label { get; set; }
}
