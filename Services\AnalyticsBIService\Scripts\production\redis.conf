# Redis configuration for Analytics & BI Service Production
# Optimized for caching and session management

################################## NETWORK #####################################

# Accept connections on all interfaces
bind 0.0.0.0

# Disable protected mode since we're using password authentication
protected-mode no

# Port
port 6379

# TCP listen() backlog
tcp-backlog 511

# TCP keepalive
tcp-keepalive 300

################################# TLS/SSL ######################################

# TLS/SSL configuration (uncomment if using SSL)
# port 0
# tls-port 6380
# tls-cert-file /etc/redis/tls/redis.crt
# tls-key-file /etc/redis/tls/redis.key
# tls-ca-cert-file /etc/redis/tls/ca.crt

################################# GENERAL #####################################

# Run as a daemon
daemonize no

# Process ID file
pidfile /var/run/redis_6379.pid

# Log level
loglevel notice

# Log file
logfile ""

# Number of databases
databases 16

# Show Redis logo
always-show-logo no

# Set server verbosity to 'debug'
# it can be one of: debug, verbose, notice, warning
set-proc-title yes
proc-title-template "{title} {listen-addr} {server-mode}"

################################ SNAPSHOTTING  ################################

# Save the DB to disk
save 900 1
save 300 10
save 60 10000

# Compress string objects using LZF when dump .rdb databases
rdbcompression yes

# Checksum the RDB file
rdbchecksum yes

# RDB filename
dbfilename dump.rdb

# Working directory
dir /data

################################# REPLICATION #################################

# Master-Replica replication settings
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5
# repl-ping-replica-period 10
# repl-timeout 60
# repl-disable-tcp-nodelay no
# repl-backlog-size 1mb
# repl-backlog-ttl 3600

################################## SECURITY ###################################

# Require password authentication
# requirepass will be set via environment variable

# Rename dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"
rename-command DEBUG ""
rename-command EVAL ""
rename-command SHUTDOWN "SHUTDOWN_a8f7e2d1c9b4f6e3"

################################### CLIENTS ####################################

# Max number of connected clients
maxclients 10000

############################## MEMORY MANAGEMENT #############################

# Memory limit
maxmemory 512mb

# Memory policy when limit is reached
maxmemory-policy allkeys-lru

# Memory sampling for LRU
maxmemory-samples 5

# Replica ignore maxmemory
replica-ignore-maxmemory yes

############################# LAZY FREEING ####################################

# Lazy freeing of memory
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Delete in background
lazyfree-lazy-user-del no

############################ KERNEL OOM CONTROL ##############################

# OOM score adjustment
oom-score-adj no

# OOM score adjustment values
oom-score-adj-values 0 200 800

############################ KERNEL TRANSPARENT HUGEPAGE CONTROL #############

# Disable transparent huge pages
disable-thp yes

############################## APPEND ONLY FILE ###############################

# Enable AOF persistence
appendonly yes

# AOF filename
appendfilename "appendonly.aof"

# AOF sync policy
appendfsync everysec

# Don't fsync during rewrites
no-appendfsync-on-rewrite no

# AOF rewrite percentage
auto-aof-rewrite-percentage 100

# AOF rewrite min size
auto-aof-rewrite-min-size 64mb

# Load truncated AOF
aof-load-truncated yes

# Use RDB-AOF hybrid persistence
aof-use-rdb-preamble yes

################################ LUA SCRIPTING  ###############################

# Lua script timeout
lua-time-limit 5000

################################ REDIS CLUSTER  ###############################

# Cluster configuration (disabled for single instance)
# cluster-enabled no

################################## SLOW LOG ###################################

# Slow log settings
slowlog-log-slower-than 10000
slowlog-max-len 128

################################ LATENCY MONITOR ##############################

# Latency monitoring
latency-monitor-threshold 100

############################# EVENT NOTIFICATION ##############################

# Keyspace notifications
notify-keyspace-events ""

############################### GOPHER SERVER ################################

# Gopher protocol support (disabled)
gopher-enabled no

############################### ADVANCED CONFIG #############################

# Hash settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List settings
list-max-ziplist-size -2
list-compress-depth 0

# Set settings
set-max-intset-entries 512

# Sorted set settings
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog settings
hll-sparse-max-bytes 3000

# Streams settings
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF rewrite incremental fsync
aof-rewrite-incremental-fsync yes

# RDB save incremental fsync
rdb-save-incremental-fsync yes

# LFU settings
lfu-log-factor 10
lfu-decay-time 1

########################### ACTIVE DEFRAGMENTATION #######################

# Active defragmentation
activedefrag no

# Active defrag ignore bytes
active-defrag-ignore-bytes 100mb

# Active defrag threshold lower
active-defrag-threshold-lower 10

# Active defrag threshold upper
active-defrag-threshold-upper 100

# Active defrag cycle min
active-defrag-cycle-min 1

# Active defrag cycle max
active-defrag-cycle-max 25

# Active defrag max scan fields
active-defrag-max-scan-fields 1000

# Jemalloc background thread
jemalloc-bg-thread yes

# EOF
