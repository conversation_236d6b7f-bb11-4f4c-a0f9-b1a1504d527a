# Subscription Management Service

A comprehensive subscription management microservice built with .NET 8, featuring clean architecture principles, domain-driven design, and RazorPay integration for the TLI Logistics platform.

## 🏗️ Architecture Overview

This service follows Clean Architecture principles with the following layers:

### Domain Layer (`SubscriptionManagement.Domain`)

- **Entities**: Core business entities (Subscription, Plan, Payment, etc.)
- **Value Objects**: Money, BillingCycle, PlanLimits
- **Domain Events**: Subscription lifecycle events
- **Enums**: SubscriptionStatus, PlanType, UserType, etc.

### Application Layer (`SubscriptionManagement.Application`)

- **Commands**: CQRS commands for write operations
- **Queries**: CQRS queries for read operations
- **DTOs**: Data transfer objects
- **Interfaces**: Repository and service contracts

### Infrastructure Layer (`SubscriptionManagement.Infrastructure`)

- **Database**: Entity Framework Core with PostgreSQL
- **Repositories**: Data access implementations
- **External Services**: RazorPay payment integration
- **Configurations**: Entity configurations

### API Layer (`SubscriptionManagement.API`)

- **Controllers**: REST API endpoints
- **Authentication**: JWT token validation
- **Swagger**: API documentation

## 🚀 Features

### Subscription Management

- Create, activate, cancel, suspend subscriptions
- Plan upgrades and downgrades with proration
- Automatic renewal handling
- Trial period support
- Subscription lifecycle tracking
- **Grace period extensions** for payment issues
- **Manual subscription extensions** for customer support

### Plan Management

- Tiered plans (Basic, Pro, Enterprise)
- User type specific plans (Transport Companies, Brokers, Carriers)
- Feature-based access control
- Custom billing cycles
- Setup fees and trial periods

### Payment Processing

- RazorPay integration
- Automatic payment processing
- Payment retry logic
- Refund handling
- Payment method management

### Feature Access Control

- Real-time feature gating
- Usage limit enforcement
- Feature rollouts
- Plan-based feature access

## 📊 Supported Plan Types

### Transport Companies

- **Basic Plan**: Limited RFQ creation (10/month), standard broker network access, email support
- **Pro Plan**: Unlimited RFQ creation, advanced analytics, priority support, integrations
- **Enterprise Plan**: Custom branding, dedicated support, API access, white-label options

### Brokers

- **Basic Plan**: Limited RFQ processing (20/month), up to 50 carriers, basic features
- **Pro Plan**: Unlimited RFQ processing, up to 200 carriers, advanced margin analysis
- **Enterprise Plan**: Unlimited access, custom pricing algorithms, white-label platform

### Carriers

- **Basic Plan**: Up to 2 vehicles, limited RFQs (15/month), basic tracking
- **Pro Plan**: Up to 10 vehicles, unlimited RFQs, route optimization, analytics
- **Enterprise Plan**: Unlimited fleet, API access, dedicated support, volume pricing

## 🛠️ Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL**: Primary database
- **RazorPay**: Payment gateway integration
- **MediatR**: CQRS implementation
- **AutoMapper**: Object mapping
- **FluentValidation**: Input validation
- **Swagger**: API documentation
- **xUnit**: Unit testing framework

## 🔧 Configuration

### Database Connection

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale"
  }
}
```

### RazorPay Configuration

```json
{
  "RazorPay": {
    "KeyId": "your_razorpay_key_id",
    "KeySecret": "your_razorpay_key_secret",
    "WebhookSecret": "your_webhook_secret"
  }
}
```

### JWT Configuration

```json
{
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services",
    "ExpiryMinutes": 60
  }
}
```

## 🚀 Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL database
- RazorPay account (for payment processing)

### Running the Service

1. **Update Configuration**

   ```bash
   # Update appsettings.json with your database and RazorPay credentials
   ```

2. **Run Database Migrations**

   ```bash
   cd SubscriptionManagement.API
   dotnet ef database update
   ```

3. **Start the Service**

   ```bash
   dotnet run
   ```

4. **Access Swagger UI**
   ```
   https://localhost:7002
   ```

## 📡 API Endpoints

### Subscriptions

- `POST /api/subscriptions` - Create subscription
- `GET /api/subscriptions/{id}` - Get subscription by ID
- `GET /api/subscriptions/my-subscription` - Get current user's subscription
- `POST /api/subscriptions/{id}/cancel` - Cancel subscription
- `POST /api/subscriptions/{id}/upgrade` - Upgrade subscription
- `POST /api/subscriptions/{id}/downgrade` - Downgrade subscription
- `POST /api/subscriptions/{id}/extend` - Extend subscription (Admin only)
- `GET /api/subscriptions/{id}/usage` - Get subscription usage

### Plans

- `GET /api/plans` - Get public plans
- `GET /api/plans/{id}` - Get plan by ID
- `GET /api/plans/by-user-type/{userType}` - Get plans by user type
- `POST /api/plans` - Create plan (Admin only)
- `PUT /api/plans/{id}` - Update plan (Admin only)
- `POST /api/plans/{id}/features` - Add plan feature (Admin only)

## 🧪 Testing

### Run Unit Tests

```bash
cd SubscriptionManagement.Tests
dotnet test
```

### Test Coverage

The test suite covers:

- Domain entity business logic
- Value object validation
- Command handlers
- Repository implementations

## 🔄 Integration with Other Services

### Identity Service

- JWT token validation
- User authentication and authorization

### User Management Service

- User profile information
- KYC status validation

### Feature Flag Service

- Real-time feature access control
- A/B testing capabilities

### Usage Tracking Service

- Real-time usage monitoring
- Limit enforcement

## 📈 Monitoring and Observability

### Health Checks

- Database connectivity
- External service availability
- Application health status

### Logging

- Structured logging with Serilog
- Request/response logging
- Error tracking and alerting

### Metrics

- Subscription metrics
- Payment success rates
- Revenue tracking
- Extension metrics and monitoring

## 📋 Extension Functionality

### Grace Period Extensions

- Temporary access during payment resolution
- Configurable grace period duration
- Automatic expiration handling
- Customer communication integration

### Manual Extensions

- Admin-controlled subscription extensions
- Billing cycle adjustments
- Customer support scenarios
- Comprehensive audit trail

### Documentation

- [Extension API Documentation](docs/subscription-extension-api.md)
- [Admin Usage Guidelines](docs/admin-extension-guidelines.md)
- [Monitoring & Metrics](docs/extension-monitoring.md)

## 🔒 Security

### Authentication

- JWT bearer token authentication
- Role-based authorization (Admin, User)

### Data Protection

- Sensitive data encryption
- PCI compliance for payment data
- Secure API endpoints

## 🚀 Deployment

### Docker Support

```dockerfile
# Dockerfile included for containerization
FROM mcr.microsoft.com/dotnet/aspnet:8.0
# ... deployment configuration
```

### Environment Variables

- Database connection strings
- RazorPay credentials
- JWT secrets
- External service URLs

## 📝 Contributing

1. Follow clean architecture principles
2. Write comprehensive unit tests
3. Use domain-driven design patterns
4. Implement proper error handling
5. Add appropriate logging

## 📄 License

This project is part of the TLI Logistics microservices platform.
