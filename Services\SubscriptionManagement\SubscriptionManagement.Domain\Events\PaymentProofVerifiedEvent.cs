using MediatR;
using Shared.Domain.Common;

namespace SubscriptionManagement.Domain.Events;

public class PaymentProofVerifiedEvent : IDomainEvent, INotification
{
    public Guid Id { get; }
    public DateTime OccurredOn { get; }
    public Guid PaymentProofId { get; }
    public Guid SubscriptionId { get; }
    public Guid UserId { get; }
    public Guid VerifiedByUserId { get; }
    public decimal Amount { get; }
    public string Currency { get; }
    public DateTime VerifiedAt { get; }

    public PaymentProofVerifiedEvent(
        Guid paymentProofId,
        Guid subscriptionId,
        Guid userId,
        Guid verifiedByUserId,
        decimal amount,
        string currency,
        DateTime verifiedAt)
    {
        Id = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
        PaymentProofId = paymentProofId;
        SubscriptionId = subscriptionId;
        UserId = userId;
        VerifiedByUserId = verifiedByUserId;
        Amount = amount;
        Currency = currency;
        VerifiedAt = verifiedAt;
    }
}
