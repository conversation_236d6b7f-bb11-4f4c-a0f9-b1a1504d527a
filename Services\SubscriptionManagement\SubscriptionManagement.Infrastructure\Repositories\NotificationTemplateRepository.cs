using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class NotificationTemplateRepository : INotificationTemplateRepository
    {
        private readonly SubscriptionDbContext _context;

        public NotificationTemplateRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<NotificationTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
        }

        public async Task<NotificationTemplate?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .FirstOrDefaultAsync(t => t.Name == name, cancellationToken);
        }

        public async Task<List<NotificationTemplate>> GetByTypeAsync(NotificationType type, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .Where(t => t.Type == type)
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationTemplate>> GetByChannelAsync(string channel, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .Where(t => t.Channel == channel)
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationTemplate>> GetByLanguageAsync(string language, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .Where(t => t.Language == language)
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .Where(t => t.IsActive)
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<NotificationTemplate?> GetTemplateAsync(
            NotificationType type, 
            string channel, 
            string language = "en", 
            CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .Where(t => t.Type == type && t.Channel == channel && t.Language == language && t.IsActive)
                .FirstOrDefaultAsync(cancellationToken);
        }

        public async Task<List<NotificationTemplate>> GetTemplatesAsync(
            NotificationType? type = null,
            string? channel = null,
            string? language = null,
            bool? isActive = null,
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationTemplates.AsQueryable();

            if (type.HasValue)
                query = query.Where(t => t.Type == type.Value);

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(t => t.Channel == channel);

            if (!string.IsNullOrEmpty(language))
                query = query.Where(t => t.Language == language);

            if (isActive.HasValue)
                query = query.Where(t => t.IsActive == isActive.Value);

            return await query
                .OrderBy(t => t.Type)
                .ThenBy(t => t.Channel)
                .ThenBy(t => t.Language)
                .ThenBy(t => t.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<(List<NotificationTemplate> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            NotificationType? type = null,
            string? channel = null,
            string? language = null,
            bool? isActive = null,
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationTemplates.AsQueryable();

            if (type.HasValue)
                query = query.Where(t => t.Type == type.Value);

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(t => t.Channel == channel);

            if (!string.IsNullOrEmpty(language))
                query = query.Where(t => t.Language == language);

            if (isActive.HasValue)
                query = query.Where(t => t.IsActive == isActive.Value);

            var totalCount = await query.CountAsync(cancellationToken);

            var items = await query
                .OrderBy(t => t.Type)
                .ThenBy(t => t.Channel)
                .ThenBy(t => t.Language)
                .ThenBy(t => t.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (items, totalCount);
        }

        public async Task<NotificationTemplate> AddAsync(NotificationTemplate template, CancellationToken cancellationToken = default)
        {
            _context.NotificationTemplates.Add(template);
            await _context.SaveChangesAsync(cancellationToken);
            return template;
        }

        public async Task UpdateAsync(NotificationTemplate template, CancellationToken cancellationToken = default)
        {
            _context.NotificationTemplates.Update(template);
            await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var template = await _context.NotificationTemplates.FindAsync(new object[] { id }, cancellationToken);
            if (template != null)
            {
                _context.NotificationTemplates.Remove(template);
                await _context.SaveChangesAsync(cancellationToken);
            }
        }

        public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .AnyAsync(t => t.Id == id, cancellationToken);
        }

        public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .AnyAsync(t => t.Name == name, cancellationToken);
        }

        public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates.CountAsync(cancellationToken);
        }

        public async Task<int> GetCountByTypeAsync(NotificationType type, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationTemplates
                .CountAsync(t => t.Type == type, cancellationToken);
        }
    }
}
