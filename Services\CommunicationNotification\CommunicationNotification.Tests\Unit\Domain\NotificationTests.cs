using Shared.Domain.Common;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Tests.Infrastructure;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Unit.Domain;

/// <summary>
/// Unit tests for Notification domain entity
/// </summary>
public class NotificationTests : UnitTestBase
{
    public NotificationTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public void Notification_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var content = new MessageContent
        {
            Subject = "Test Subject",
            Body = "Test notification body",
            Language = Language.English
        };

        // Act
        var notification = new Notification
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            MessageType = MessageType.SystemNotification,
            Priority = NotificationPriority.Normal,
            Channel = NotificationChannel.Push,
            Status = NotificationStatus.Pending,
            ScheduledAt = DateTime.UtcNow,
            Content = content,
            Metadata = new Dictionary<string, string>
            {
                ["source"] = "unit-test"
            },
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Assert
        notification.Id.Should().NotBeEmpty();
        notification.UserId.Should().Be(userId);
        notification.MessageType.Should().Be(MessageType.SystemNotification);
        notification.Priority.Should().Be(NotificationPriority.Normal);
        notification.Channel.Should().Be(NotificationChannel.Push);
        notification.Status.Should().Be(NotificationStatus.Pending);
        notification.Content.Should().NotBeNull();
        notification.Content.Subject.Should().Be("Test Subject");
        notification.Content.Body.Should().Be("Test notification body");
        notification.Content.Language.Should().Be(Language.English);
        notification.Metadata.Should().ContainKey("source");
        notification.Metadata["source"].Should().Be("unit-test");
    }

    [Fact]
    public void Notification_MarkAsSent_ShouldUpdateStatusAndTimestamp()
    {
        // Arrange
        var notification = CreateTestNotification();
        var sentAt = DateTime.UtcNow;

        // Act
        notification.Status = NotificationStatus.Sent;
        notification.SentAt = sentAt;
        notification.UpdatedAt = sentAt;

        // Assert
        notification.Status.Should().Be(NotificationStatus.Sent);
        notification.SentAt.Should().Be(sentAt);
        notification.UpdatedAt.Should().Be(sentAt);
    }

    [Fact]
    public void Notification_MarkAsDelivered_ShouldUpdateStatusAndTimestamp()
    {
        // Arrange
        var notification = CreateTestNotification();
        var deliveredAt = DateTime.UtcNow;

        // Act
        notification.Status = NotificationStatus.Delivered;
        notification.DeliveredAt = deliveredAt;
        notification.UpdatedAt = deliveredAt;

        // Assert
        notification.Status.Should().Be(NotificationStatus.Delivered);
        notification.DeliveredAt.Should().Be(deliveredAt);
        notification.UpdatedAt.Should().Be(deliveredAt);
    }

    [Fact]
    public void Notification_MarkAsRead_ShouldUpdateTimestamp()
    {
        // Arrange
        var notification = CreateTestNotification();
        notification.Status = NotificationStatus.Delivered;
        var readAt = DateTime.UtcNow;

        // Act
        notification.ReadAt = readAt;
        notification.UpdatedAt = readAt;

        // Assert
        notification.ReadAt.Should().Be(readAt);
        notification.UpdatedAt.Should().Be(readAt);
    }

    [Fact]
    public void Notification_MarkAsFailed_ShouldUpdateStatusAndReason()
    {
        // Arrange
        var notification = CreateTestNotification();
        var failureReason = "Provider timeout";

        // Act
        notification.Status = NotificationStatus.Failed;
        notification.FailureReason = failureReason;
        notification.RetryCount++;
        notification.UpdatedAt = DateTime.UtcNow;

        // Assert
        notification.Status.Should().Be(NotificationStatus.Failed);
        notification.FailureReason.Should().Be(failureReason);
        notification.RetryCount.Should().Be(1);
    }

    [Fact]
    public void Notification_IncrementRetryCount_ShouldUpdateCounter()
    {
        // Arrange
        var notification = CreateTestNotification();
        var initialRetryCount = notification.RetryCount;

        // Act
        notification.RetryCount++;

        // Assert
        notification.RetryCount.Should().Be(initialRetryCount + 1);
    }

    [Fact]
    public void Notification_SetExternalId_ShouldUpdateProperty()
    {
        // Arrange
        var notification = CreateTestNotification();
        var externalId = "provider_msg_12345";

        // Act
        notification.ExternalId = externalId;

        // Assert
        notification.ExternalId.Should().Be(externalId);
    }

    [Fact]
    public void Notification_UpdateMetadata_ShouldMergeValues()
    {
        // Arrange
        var notification = CreateTestNotification();
        notification.Metadata = new Dictionary<string, string>
        {
            ["initial"] = "value"
        };

        // Act
        notification.Metadata["new_key"] = "new_value";
        notification.Metadata["initial"] = "updated_value";

        // Assert
        notification.Metadata.Should().HaveCount(2);
        notification.Metadata["initial"].Should().Be("updated_value");
        notification.Metadata["new_key"].Should().Be("new_value");
    }

    [Theory]
    [InlineData(NotificationPriority.Critical)]
    [InlineData(NotificationPriority.High)]
    [InlineData(NotificationPriority.Normal)]
    [InlineData(NotificationPriority.Low)]
    public void Notification_SetPriority_ShouldAcceptValidValues(NotificationPriority priority)
    {
        // Arrange
        var notification = CreateTestNotification();

        // Act
        notification.Priority = priority;

        // Assert
        notification.Priority.Should().Be(priority);
    }

    [Theory]
    [InlineData(NotificationChannel.Sms)]
    [InlineData(NotificationChannel.Email)]
    [InlineData(NotificationChannel.Push)]
    [InlineData(NotificationChannel.WhatsApp)]
    [InlineData(NotificationChannel.Voice)]
    public void Notification_SetChannel_ShouldAcceptValidValues(NotificationChannel channel)
    {
        // Arrange
        var notification = CreateTestNotification();

        // Act
        notification.Channel = channel;

        // Assert
        notification.Channel.Should().Be(channel);
    }

    [Theory]
    [InlineData(MessageType.SystemNotification)]
    [InlineData(MessageType.TripConfirmation)]
    [InlineData(MessageType.TripUpdate)]
    [InlineData(MessageType.Emergency)]
    [InlineData(MessageType.Marketing)]
    [InlineData(MessageType.AccountVerification)]
    public void Notification_SetMessageType_ShouldAcceptValidValues(MessageType messageType)
    {
        // Arrange
        var notification = CreateTestNotification();

        // Act
        notification.MessageType = messageType;

        // Assert
        notification.MessageType.Should().Be(messageType);
    }

    [Fact]
    public void Notification_SetRelatedEntity_ShouldUpdateProperties()
    {
        // Arrange
        var notification = CreateTestNotification();
        var relatedEntityId = Guid.NewGuid();
        var relatedEntityType = "Trip";

        // Act
        notification.RelatedEntityId = relatedEntityId;
        notification.RelatedEntityType = relatedEntityType;

        // Assert
        notification.RelatedEntityId.Should().Be(relatedEntityId);
        notification.RelatedEntityType.Should().Be(relatedEntityType);
    }

    [Fact]
    public void Notification_IsScheduled_ShouldReturnCorrectValue()
    {
        // Arrange
        var futureNotification = CreateTestNotification();
        futureNotification.ScheduledAt = DateTime.UtcNow.AddMinutes(30);

        var pastNotification = CreateTestNotification();
        pastNotification.ScheduledAt = DateTime.UtcNow.AddMinutes(-30);

        // Act & Assert
        (futureNotification.ScheduledAt > DateTime.UtcNow).Should().BeTrue();
        (pastNotification.ScheduledAt <= DateTime.UtcNow).Should().BeTrue();
    }

    [Fact]
    public void Notification_IsDelivered_ShouldReturnCorrectValue()
    {
        // Arrange
        var deliveredNotification = CreateTestNotification();
        deliveredNotification.Status = NotificationStatus.Delivered;
        deliveredNotification.DeliveredAt = DateTime.UtcNow;

        var pendingNotification = CreateTestNotification();
        pendingNotification.Status = NotificationStatus.Pending;

        // Act & Assert
        deliveredNotification.Status.Should().Be(NotificationStatus.Delivered);
        deliveredNotification.DeliveredAt.Should().NotBeNull();
        
        pendingNotification.Status.Should().Be(NotificationStatus.Pending);
        pendingNotification.DeliveredAt.Should().BeNull();
    }

    [Fact]
    public void Notification_IsRead_ShouldReturnCorrectValue()
    {
        // Arrange
        var readNotification = CreateTestNotification();
        readNotification.ReadAt = DateTime.UtcNow;

        var unreadNotification = CreateTestNotification();

        // Act & Assert
        readNotification.ReadAt.Should().NotBeNull();
        unreadNotification.ReadAt.Should().BeNull();
    }

    [Fact]
    public void Notification_HasFailed_ShouldReturnCorrectValue()
    {
        // Arrange
        var failedNotification = CreateTestNotification();
        failedNotification.Status = NotificationStatus.Failed;
        failedNotification.FailureReason = "Provider error";

        var successfulNotification = CreateTestNotification();
        successfulNotification.Status = NotificationStatus.Delivered;

        // Act & Assert
        failedNotification.Status.Should().Be(NotificationStatus.Failed);
        failedNotification.FailureReason.Should().NotBeNullOrEmpty();
        
        successfulNotification.Status.Should().Be(NotificationStatus.Delivered);
        successfulNotification.FailureReason.Should().BeNull();
    }

    [Fact]
    public void Notification_CanRetry_ShouldReturnCorrectValue()
    {
        // Arrange
        var retryableNotification = CreateTestNotification();
        retryableNotification.Status = NotificationStatus.Failed;
        retryableNotification.RetryCount = 2;

        var maxRetriedNotification = CreateTestNotification();
        maxRetriedNotification.Status = NotificationStatus.Failed;
        maxRetriedNotification.RetryCount = 5; // Assuming max retry is 3

        var deliveredNotification = CreateTestNotification();
        deliveredNotification.Status = NotificationStatus.Delivered;

        // Act & Assert
        const int maxRetryCount = 3;
        
        (retryableNotification.Status == NotificationStatus.Failed && 
         retryableNotification.RetryCount < maxRetryCount).Should().BeTrue();
        
        (maxRetriedNotification.Status == NotificationStatus.Failed && 
         maxRetriedNotification.RetryCount >= maxRetryCount).Should().BeTrue();
        
        deliveredNotification.Status.Should().NotBe(NotificationStatus.Failed);
    }

    [Fact]
    public void Notification_GetDeliveryDuration_ShouldCalculateCorrectly()
    {
        // Arrange
        var notification = CreateTestNotification();
        var sentAt = DateTime.UtcNow;
        var deliveredAt = sentAt.AddSeconds(30);

        notification.SentAt = sentAt;
        notification.DeliveredAt = deliveredAt;

        // Act
        var duration = notification.DeliveredAt - notification.SentAt;

        // Assert
        duration.Should().NotBeNull();
        duration.Value.TotalSeconds.Should().Be(30);
    }

    [Fact]
    public void Notification_GetReadDuration_ShouldCalculateCorrectly()
    {
        // Arrange
        var notification = CreateTestNotification();
        var deliveredAt = DateTime.UtcNow;
        var readAt = deliveredAt.AddMinutes(5);

        notification.DeliveredAt = deliveredAt;
        notification.ReadAt = readAt;

        // Act
        var duration = notification.ReadAt - notification.DeliveredAt;

        // Assert
        duration.Should().NotBeNull();
        duration.Value.TotalMinutes.Should().Be(5);
    }

    private Notification CreateTestNotification()
    {
        return new Notification
        {
            Id = Guid.NewGuid(),
            UserId = Guid.NewGuid(),
            MessageType = MessageType.SystemNotification,
            Priority = NotificationPriority.Normal,
            Channel = NotificationChannel.Push,
            Status = NotificationStatus.Pending,
            ScheduledAt = DateTime.UtcNow,
            Content = new MessageContent
            {
                Subject = "Test Subject",
                Body = "Test notification body",
                Language = Language.English
            },
            Metadata = new Dictionary<string, string>(),
            RetryCount = 0,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }
}

