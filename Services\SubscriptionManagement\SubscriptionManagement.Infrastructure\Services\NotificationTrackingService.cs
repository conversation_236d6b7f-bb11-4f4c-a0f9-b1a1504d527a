using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class NotificationTrackingService : INotificationTrackingService
    {
        private readonly INotificationHistoryRepository _notificationHistoryRepository;
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly ILogger<NotificationTrackingService> _logger;

        public NotificationTrackingService(
            INotificationHistoryRepository notificationHistoryRepository,
            ISubscriptionRepository subscriptionRepository,
            ILogger<NotificationTrackingService> logger)
        {
            _notificationHistoryRepository = notificationHistoryRepository;
            _subscriptionRepository = subscriptionRepository;
            _logger = logger;
        }

        public async Task<NotificationHistory> TrackNotificationAsync(
            Guid? subscriptionId,
            Guid? userId,
            NotificationType type,
            string channel,
            string subject,
            string body,
            Guid triggeredByUserId,
            DateTime? scheduledAt = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Tracking notification. Type: {Type}, Channel: {Channel}, SubscriptionId: {SubscriptionId}, UserId: {UserId}", 
                type, channel, subscriptionId, userId);

            var notification = NotificationHistory.Create(
                subscriptionId,
                userId,
                type,
                channel,
                subject,
                body,
                triggeredByUserId,
                scheduledAt,
                metadata);

            await _notificationHistoryRepository.AddAsync(notification, cancellationToken);

            _logger.LogInformation("Notification tracked successfully. NotificationId: {NotificationId}", notification.Id);
            return notification;
        }

        public async Task UpdateNotificationSentAsync(
            Guid notificationId,
            string? externalNotificationId = null,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for sent update. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsSent(externalNotificationId);
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification marked as sent. NotificationId: {NotificationId}, ExternalId: {ExternalId}", 
                notificationId, externalNotificationId);
        }

        public async Task UpdateNotificationDeliveredAsync(
            Guid notificationId,
            Dictionary<string, string>? deliveryMetadata = null,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for delivered update. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsDelivered(deliveryMetadata);
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification marked as delivered. NotificationId: {NotificationId}", notificationId);
        }

        public async Task UpdateNotificationReadAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for read update. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsRead();
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification marked as read. NotificationId: {NotificationId}", notificationId);
        }

        public async Task UpdateNotificationClickedAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for clicked update. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsClicked();
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification marked as clicked. NotificationId: {NotificationId}", notificationId);
        }

        public async Task UpdateNotificationFailedAsync(
            Guid notificationId,
            string errorMessage,
            string? errorCode = null,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for failed update. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsFailed(errorMessage, errorCode);
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogWarning("Notification marked as failed. NotificationId: {NotificationId}, Error: {ErrorMessage}", 
                notificationId, errorMessage);
        }

        public async Task CancelNotificationAsync(
            Guid notificationId,
            CancellationToken cancellationToken = default)
        {
            var notification = await _notificationHistoryRepository.GetByIdAsync(notificationId, cancellationToken);
            if (notification == null)
            {
                _logger.LogWarning("Notification not found for cancellation. NotificationId: {NotificationId}", notificationId);
                return;
            }

            notification.MarkAsCancelled();
            await _notificationHistoryRepository.UpdateAsync(notification, cancellationToken);

            _logger.LogInformation("Notification cancelled. NotificationId: {NotificationId}", notificationId);
        }

        public async Task<NotificationAnalytics> GetNotificationAnalyticsAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? channel = null,
            NotificationType? type = null,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Getting notification analytics. FromDate: {FromDate}, ToDate: {ToDate}, Channel: {Channel}, Type: {Type}", 
                fromDate, toDate, channel, type);

            var analytics = new NotificationAnalytics
            {
                FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
                ToDate = toDate ?? DateTime.UtcNow,
                Channel = channel,
                Type = type
            };

            // Get counts by status
            analytics.NotificationsByStatus = await _notificationHistoryRepository.GetNotificationCountsByStatusAsync(
                fromDate, toDate, cancellationToken);

            // Get counts by channel
            analytics.NotificationsByChannel = await _notificationHistoryRepository.GetNotificationCountsByChannelAsync(
                fromDate, toDate, cancellationToken);

            // Get counts by type
            analytics.NotificationsByType = await _notificationHistoryRepository.GetNotificationCountsByTypeAsync(
                fromDate, toDate, cancellationToken);

            // Calculate total notifications
            analytics.TotalNotifications = analytics.NotificationsByStatus.Values.Sum();

            // Get delivery rate
            analytics.DeliveryRate = await _notificationHistoryRepository.GetDeliveryRateAsync(
                channel, fromDate, toDate, cancellationToken);

            // Get read rate
            analytics.ReadRate = await _notificationHistoryRepository.GetReadRateAsync(
                channel, fromDate, toDate, cancellationToken);

            // Get click rate
            analytics.ClickRate = await _notificationHistoryRepository.GetClickRateAsync(
                channel, fromDate, toDate, cancellationToken);

            // Get average delivery time
            analytics.AverageDeliveryTime = await _notificationHistoryRepository.GetAverageDeliveryTimeAsync(
                channel, fromDate, toDate, cancellationToken);

            return analytics;
        }

        public async Task<ConversionAnalytics> GetConversionAnalyticsAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            NotificationType? type = null,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Getting conversion analytics. FromDate: {FromDate}, ToDate: {ToDate}, Type: {Type}", 
                fromDate, toDate, type);

            var analytics = new ConversionAnalytics
            {
                FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
                ToDate = toDate ?? DateTime.UtcNow,
                Type = type
            };

            // TODO: Implement conversion tracking logic
            // This would involve:
            // 1. Getting all reminder notifications in the date range
            // 2. Finding subscriptions that were renewed after receiving reminders
            // 3. Calculating conversion rates by channel
            // 4. Calculating average time to conversion

            // For now, return empty analytics
            analytics.TotalReminders = 0;
            analytics.SubscriptionsRenewed = 0;
            analytics.ConversionRate = 0;
            analytics.AverageTimeToConversion = TimeSpan.Zero;

            return analytics;
        }

        public async Task<List<NotificationHistory>> GetNotificationsForRetryAsync(
            CancellationToken cancellationToken = default)
        {
            return await _notificationHistoryRepository.GetFailedNotificationsForRetryAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetScheduledNotificationsReadyToSendAsync(
            CancellationToken cancellationToken = default)
        {
            return await _notificationHistoryRepository.GetScheduledNotificationsAsync(DateTime.UtcNow, cancellationToken);
        }
    }
}
