using MediatR;

namespace OrderManagement.Application.Commands.AddPreferredPartnersToRfq;

public class AddPreferredPartnersToRfqCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public List<PreferredPartnerRequest> PreferredPartners { get; set; } = new();
    public Guid RequestingUserId { get; set; }
}

public class PreferredPartnerRequest
{
    public Guid PartnerId { get; set; }
    public OrderManagement.Domain.Entities.PartnerType PartnerType { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public decimal PreferenceRating { get; set; }
    public string? Notes { get; set; }
    public int Priority { get; set; } = 1;
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> VehicleTypes { get; set; } = new();
}
