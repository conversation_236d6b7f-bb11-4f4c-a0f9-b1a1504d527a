# 💳 Subscription Management Service API Documentation

## 📋 Overview

The Subscription Management Service handles subscription plans, billing, feature flags, and payment processing. This service is **98% complete** and **production-ready** with comprehensive tax configuration, payment proof verification, and advanced billing features.

**Base URL**: `/api/v1/subscriptions` (via API Gateway)
**Direct URL**: `http://localhost:5003`
**Authentication**: Required for all endpoints
**Authorization**: Role-based access control
**Caching**: Redis-based performance optimization

## 🚀 Current Implementation Status

### ✅ Fully Implemented Features

- **Complete CQRS Implementation**: 25+ command handlers, 15+ query handlers
- **Tax Configuration**: Comprehensive GST/TDS calculation with region-based rules
- **Payment Proof Verification**: Manual payment verification workflow with admin approval
- **Grace Period Management**: Automatic grace period extension with business rules
- **Redis Caching**: Performance-optimized subscription data caching with decorators
- **Background Processing**: Automated billing cycles, notifications, and alerts
- **Feature Flags**: Advanced feature flag system with gradual rollout and A/B testing
- **Usage Analytics**: Comprehensive subscription and feature usage metrics

### 🔄 Minor Enhancements Remaining

- **Admin Panel UI**: Minor UI enhancements for better user experience
- **Advanced Reporting**: Additional analytics and reporting features

## 🔐 Authentication & Permissions

### Required Permissions

| Operation              | Permission                      | Roles                   |
| ---------------------- | ------------------------------- | ----------------------- |
| View own subscription  | `subscriptions.read.own`        | All authenticated users |
| View all subscriptions | `subscriptions.read.all`        | Admin                   |
| Create subscription    | `subscriptions.create`          | Admin, Self-service     |
| Update subscription    | `subscriptions.update`          | Admin, Account owner    |
| Cancel subscription    | `subscriptions.cancel`          | Admin, Account owner    |
| Manage plans           | `subscriptions.plans.manage`    | Admin                   |
| Process billing        | `subscriptions.billing.process` | Admin, System           |
| View feature flags     | `subscriptions.features.read`   | All authenticated users |
| Manage feature flags   | `subscriptions.features.manage` | Admin                   |

## 📊 Core Endpoints

### 1. Subscription Plans

#### Get Available Plans

```http
GET /api/v1/subscriptions/plans
Authorization: Bearer <token>
```

**Query Parameters:**

- `includeFeatures` (boolean): Include feature details
- `currency` (string): Filter by currency
- `userType` (string): Filter by user type

**Response:**

```json
{
  "data": [
    {
      "id": "plan-uuid",
      "name": "Premium",
      "displayName": "Premium Plan",
      "description": "Advanced features for growing businesses",
      "userType": "Broker|Transporter|Shipper",
      "tier": "Basic|Premium|Enterprise",
      "pricing": {
        "monthly": {
          "amount": 99.0,
          "currency": "USD",
          "discountPercentage": 0
        },
        "quarterly": {
          "amount": 267.3,
          "currency": "USD",
          "discountPercentage": 10
        },
        "annual": {
          "amount": 950.4,
          "currency": "USD",
          "discountPercentage": 20
        }
      },
      "features": [
        {
          "featureKey": "max_rfqs_per_month",
          "displayName": "RFQs per Month",
          "value": "100",
          "description": "Maximum RFQs you can create per month"
        },
        {
          "featureKey": "real_time_tracking",
          "displayName": "Real-time Tracking",
          "value": "true",
          "description": "Live GPS tracking for all shipments"
        },
        {
          "featureKey": "api_access",
          "displayName": "API Access",
          "value": "true",
          "description": "Full REST API access"
        }
      ],
      "limits": {
        "maxUsers": 10,
        "maxVehicles": 50,
        "maxOrders": 1000,
        "storageGB": 100
      },
      "isActive": true,
      "isPopular": true,
      "trialDays": 14,
      "setupFee": 0.0,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### Get Plan Details

```http
GET /api/v1/subscriptions/plans/{planId}
Authorization: Bearer <token>
```

#### Create Plan (Admin Only)

```http
POST /api/v1/subscriptions/plans
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Enterprise",
  "displayName": "Enterprise Plan",
  "description": "Full-featured plan for large organizations",
  "userType": "Broker",
  "tier": "Enterprise",
  "pricing": {
    "monthly": {
      "amount": 299.00,
      "currency": "USD"
    },
    "annual": {
      "amount": 2990.00,
      "currency": "USD",
      "discountPercentage": 16.7
    }
  },
  "features": [
    {
      "featureKey": "max_rfqs_per_month",
      "value": "unlimited"
    },
    {
      "featureKey": "dedicated_support",
      "value": "true"
    }
  ],
  "limits": {
    "maxUsers": 100,
    "maxVehicles": 500,
    "maxOrders": 10000,
    "storageGB": 1000
  },
  "trialDays": 30,
  "isActive": true
}
```

### 2. User Subscriptions

#### Get Current Subscription

```http
GET /api/v1/subscriptions/current
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "subscription-uuid",
  "userId": "user-uuid",
  "planId": "plan-uuid",
  "planName": "Premium",
  "status": "Active|Cancelled|Suspended|PastDue|Trialing",
  "billingCycle": "Monthly|Quarterly|Annual",
  "currentPeriod": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-02-01T00:00:00Z"
  },
  "nextBillingDate": "2024-02-01T00:00:00Z",
  "amount": 99.0,
  "currency": "USD",
  "paymentMethod": {
    "id": "pm-uuid",
    "type": "Card|BankTransfer|UPI|Wallet",
    "last4": "4242",
    "brand": "Visa",
    "expiryMonth": 12,
    "expiryYear": 2025
  },
  "trialInfo": {
    "isInTrial": false,
    "trialStartDate": "2023-12-01T00:00:00Z",
    "trialEndDate": "2023-12-15T00:00:00Z"
  },
  "gracePeriod": {
    "isInGracePeriod": false,
    "gracePeriodEndDate": null
  },
  "usage": {
    "currentPeriodUsage": {
      "rfqsCreated": 25,
      "ordersProcessed": 150,
      "apiCalls": 5000,
      "storageUsedGB": 45.2
    },
    "limits": {
      "maxRFQs": 100,
      "maxOrders": 1000,
      "maxApiCalls": 10000,
      "maxStorageGB": 100
    }
  },
  "features": [
    {
      "featureKey": "real_time_tracking",
      "isEnabled": true,
      "value": "true"
    },
    {
      "featureKey": "api_access",
      "isEnabled": true,
      "value": "true"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-15T00:00:00Z"
}
```

#### Create Subscription

```http
POST /api/v1/subscriptions
Authorization: Bearer <token>
Content-Type: application/json

{
  "planId": "plan-uuid",
  "billingCycle": "Monthly",
  "paymentMethodId": "pm-uuid",
  "startDate": "2024-02-01T00:00:00Z",
  "couponCode": "WELCOME20",
  "autoRenew": true,
  "billingAddress": {
    "street": "123 Business St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  }
}
```

#### Upgrade Subscription

```http
PUT /api/v1/subscriptions/{subscriptionId}/upgrade
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPlanId": "enterprise-plan-uuid",
  "billingCycle": "Annual",
  "effectiveDate": "immediate|next_billing_cycle",
  "prorationBehavior": "create_prorations|none",
  "paymentMethodId": "pm-uuid"
}
```

#### Downgrade Subscription

```http
PUT /api/v1/subscriptions/{subscriptionId}/downgrade
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPlanId": "basic-plan-uuid",
  "effectiveDate": "immediate|next_billing_cycle",
  "reason": "Cost optimization",
  "confirmDataLoss": true
}
```

#### Pause Subscription

```http
PUT /api/v1/subscriptions/{subscriptionId}/pause
Authorization: Bearer <token>
Content-Type: application/json

{
  "pauseDate": "2024-03-01T00:00:00Z",
  "resumeDate": "2024-04-01T00:00:00Z",
  "reason": "Temporary business closure",
  "retainData": true
}
```

#### Resume Subscription

```http
PUT /api/v1/subscriptions/{subscriptionId}/resume
Authorization: Bearer <token>
Content-Type: application/json

{
  "resumeDate": "immediate|specific_date",
  "specificDate": "2024-02-15T00:00:00Z",
  "paymentMethodId": "pm-uuid"
}
```

#### Cancel Subscription

```http
PUT /api/v1/subscriptions/{subscriptionId}/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "cancellationDate": "immediate|end_of_period",
  "reason": "No longer needed",
  "feedback": "Found a better alternative",
  "retainData": true,
  "refundRequested": false
}
```

### 3. Payment Methods

#### Get Payment Methods

```http
GET /api/v1/subscriptions/payment-methods
Authorization: Bearer <token>
```

**Response:**

```json
{
  "data": [
    {
      "id": "pm-uuid",
      "type": "Card",
      "isDefault": true,
      "card": {
        "brand": "Visa",
        "last4": "4242",
        "expiryMonth": 12,
        "expiryYear": 2025,
        "country": "US"
      },
      "billingAddress": {
        "street": "123 Business St",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "country": "USA"
      },
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### Add Payment Method

```http
POST /api/v1/subscriptions/payment-methods
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "Card|BankTransfer|UPI|Wallet",
  "cardDetails": {
    "number": "****************",
    "expiryMonth": 12,
    "expiryYear": 2025,
    "cvc": "123",
    "holderName": "John Doe"
  },
  "billingAddress": {
    "street": "123 Business St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  },
  "setAsDefault": true
}
```

#### Update Payment Method

```http
PUT /api/v1/subscriptions/payment-methods/{paymentMethodId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "expiryMonth": 6,
  "expiryYear": 2026,
  "billingAddress": {
    "street": "456 Updated St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10002",
    "country": "USA"
  }
}
```

#### Delete Payment Method

```http
DELETE /api/v1/subscriptions/payment-methods/{paymentMethodId}
Authorization: Bearer <token>
```

### 4. Billing & Invoices

#### Get Billing History

```http
GET /api/v1/subscriptions/billing/history
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Filter from date
- `toDate`: Filter to date
- `status`: Filter by payment status
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "invoice-uuid",
      "invoiceNumber": "INV-2024-001234",
      "subscriptionId": "subscription-uuid",
      "amount": 99.0,
      "currency": "USD",
      "status": "Paid|Pending|Failed|Refunded",
      "billingPeriod": {
        "startDate": "2024-01-01T00:00:00Z",
        "endDate": "2024-02-01T00:00:00Z"
      },
      "dueDate": "2024-01-01T00:00:00Z",
      "paidDate": "2024-01-01T10:30:00Z",
      "paymentMethod": "Card ending in 4242",
      "lineItems": [
        {
          "description": "Premium Plan - Monthly",
          "quantity": 1,
          "unitPrice": 99.0,
          "amount": 99.0
        }
      ],
      "taxes": [
        {
          "name": "GST",
          "rate": 18.0,
          "amount": 17.82
        }
      ],
      "totalAmount": 116.82,
      "downloadUrl": "https://storage.tli.com/invoices/invoice-uuid.pdf",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 12,
    "totalPages": 1
  }
}
```

#### Get Invoice Details

```http
GET /api/v1/subscriptions/billing/invoices/{invoiceId}
Authorization: Bearer <token>
```

#### Download Invoice

```http
GET /api/v1/subscriptions/billing/invoices/{invoiceId}/download
Authorization: Bearer <token>
```

#### Process Manual Payment

```http
POST /api/v1/subscriptions/billing/manual-payment
Authorization: Bearer <token>
Content-Type: application/json

{
  "subscriptionId": "subscription-uuid",
  "amount": 99.00,
  "currency": "USD",
  "paymentMethod": "BankTransfer",
  "transactionReference": "TXN123456789",
  "paymentDate": "2024-01-01T00:00:00Z",
  "notes": "Payment made via bank transfer",
  "attachments": [
    {
      "fileName": "payment_receipt.pdf",
      "fileUrl": "https://storage.tli.com/receipts/receipt-uuid.pdf"
    }
  ]
}
```

### 5. Feature Flags

#### Get User Features

```http
GET /api/v1/subscriptions/features
Authorization: Bearer <token>
```

**Response:**

```json
{
  "userId": "user-uuid",
  "subscriptionTier": "Premium",
  "features": [
    {
      "featureKey": "real_time_tracking",
      "displayName": "Real-time Tracking",
      "isEnabled": true,
      "value": "true",
      "description": "Live GPS tracking for all shipments"
    },
    {
      "featureKey": "max_rfqs_per_month",
      "displayName": "RFQs per Month",
      "isEnabled": true,
      "value": "100",
      "description": "Maximum RFQs you can create per month"
    },
    {
      "featureKey": "api_access",
      "displayName": "API Access",
      "isEnabled": true,
      "value": "true",
      "description": "Full REST API access"
    },
    {
      "featureKey": "advanced_analytics",
      "displayName": "Advanced Analytics",
      "isEnabled": false,
      "value": "false",
      "description": "Detailed analytics and reporting",
      "upgradeRequired": "Enterprise"
    }
  ],
  "usage": {
    "rfqsUsed": 25,
    "rfqsLimit": 100,
    "apiCallsUsed": 5000,
    "apiCallsLimit": 10000
  }
}
```

#### Check Feature Access

```http
GET /api/v1/subscriptions/features/{featureKey}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "featureKey": "real_time_tracking",
  "isEnabled": true,
  "value": "true",
  "hasAccess": true,
  "usageInfo": {
    "used": 0,
    "limit": null,
    "resetDate": null
  }
}
```

### 6. Usage Analytics

#### Get Usage Statistics

```http
GET /api/v1/subscriptions/usage/statistics
Authorization: Bearer <token>
```

**Query Parameters:**

- `period`: `current|previous|custom`
- `fromDate`: Start date for custom period
- `toDate`: End date for custom period

**Response:**

```json
{
  "subscriptionId": "subscription-uuid",
  "period": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-02-01T00:00:00Z",
    "type": "current"
  },
  "usage": {
    "rfqs": {
      "used": 25,
      "limit": 100,
      "percentage": 25.0,
      "trend": "+15% from last period"
    },
    "orders": {
      "used": 150,
      "limit": 1000,
      "percentage": 15.0,
      "trend": "+8% from last period"
    },
    "apiCalls": {
      "used": 5000,
      "limit": 10000,
      "percentage": 50.0,
      "trend": "+25% from last period"
    },
    "storage": {
      "used": 45.2,
      "limit": 100,
      "unit": "GB",
      "percentage": 45.2,
      "trend": "+5% from last period"
    }
  },
  "projectedUsage": {
    "rfqs": 85,
    "orders": 450,
    "apiCalls": 8500,
    "storage": 52.3
  },
  "alerts": [
    {
      "type": "Warning",
      "feature": "apiCalls",
      "message": "API usage is 50% of monthly limit",
      "threshold": 50
    }
  ]
}
```

#### Get Feature Usage History

```http
GET /api/v1/subscriptions/usage/history/{featureKey}
Authorization: Bearer <token>
```

**Query Parameters:**

- `period`: `7d|30d|90d|1y`
- `granularity`: `daily|weekly|monthly`

### 7. Subscription Management (Admin)

#### Get All Subscriptions (Admin)

```http
GET /api/v1/subscriptions/admin/all
Authorization: Bearer <token>
```

**Query Parameters:**

- `status`: Filter by subscription status
- `planId`: Filter by plan
- `userType`: Filter by user type
- `billingCycle`: Filter by billing cycle
- `searchTerm`: Search in user details
- `page`: Page number
- `pageSize`: Items per page

#### Extend Grace Period (Admin)

```http
PUT /api/v1/subscriptions/{subscriptionId}/extend-grace-period
Authorization: Bearer <token>
Content-Type: application/json

{
  "extensionDays": 7,
  "reason": "Customer requested extension due to payment processing delay",
  "notifyCustomer": true,
  "adminComments": "One-time extension approved"
}
```

#### Process Subscription Renewal

```http
POST /api/v1/subscriptions/{subscriptionId}/renew
Authorization: Bearer <token>
Content-Type: application/json

{
  "renewalDate": "2024-02-01T00:00:00Z",
  "paymentMethodId": "pm-uuid",
  "applyDiscounts": true,
  "sendInvoice": true
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/subscriptions/hub`

**Events:**

- `SubscriptionCreated` - New subscription created
- `SubscriptionUpgraded` - Subscription upgraded
- `SubscriptionDowngraded` - Subscription downgraded
- `SubscriptionCancelled` - Subscription cancelled
- `PaymentSucceeded` - Payment processed successfully
- `PaymentFailed` - Payment processing failed
- `UsageLimitReached` - Feature usage limit reached
- `TrialExpiring` - Trial period expiring soon
- `GracePeriodStarted` - Grace period started
- `FeatureToggled` - Feature flag changed

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/subscriptions/hub')
  .build()

connection.on('PaymentFailed', (data) => {
  console.log('Payment failed:', data)
  // Show payment retry UI
})

connection.on('UsageLimitReached', (data) => {
  console.log('Usage limit reached:', data)
  // Show upgrade prompt
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                       | Description                               |
| ---- | ----------------------------- | ----------------------------------------- |
| 400  | `INVALID_PLAN_ID`             | Invalid subscription plan ID              |
| 400  | `INVALID_BILLING_CYCLE`       | Invalid billing cycle specified           |
| 400  | `PAYMENT_METHOD_REQUIRED`     | Payment method is required                |
| 400  | `SUBSCRIPTION_NOT_ACTIVE`     | Subscription is not active                |
| 401  | `UNAUTHORIZED`                | Authentication required                   |
| 403  | `INSUFFICIENT_PERMISSIONS`    | Insufficient permissions                  |
| 404  | `SUBSCRIPTION_NOT_FOUND`      | Subscription not found                    |
| 404  | `PLAN_NOT_FOUND`              | Subscription plan not found               |
| 409  | `SUBSCRIPTION_ALREADY_EXISTS` | User already has an active subscription   |
| 409  | `CANNOT_DOWNGRADE`            | Cannot downgrade due to usage constraints |
| 422  | `PAYMENT_FAILED`              | Payment processing failed                 |
| 422  | `VALIDATION_FAILED`           | Input validation failed                   |
| 429  | `RATE_LIMIT_EXCEEDED`         | Too many requests                         |

### Error Response Format

```json
{
  "error": {
    "code": "PAYMENT_FAILED",
    "message": "Payment processing failed",
    "details": "Insufficient funds in the account",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid",
    "retryable": true,
    "suggestedAction": "Update payment method or try again later"
  }
}
```
