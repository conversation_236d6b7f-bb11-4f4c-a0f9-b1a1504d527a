using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.FormBuilder
{
    public class SubmitFormCommandHandler : IRequestHandler<SubmitFormCommand, FormSubmissionDto>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<SubmitFormCommandHandler> _logger;

        public SubmitFormCommandHandler(
            IFormBuilderService formBuilderService,
            ILogger<SubmitFormCommandHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<FormSubmissionDto> Handle(SubmitFormCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Submitting form {FormId} for user {UserId}",
                    request.FormDefinitionId, request.UserId);

                // Validate form data if requested
                if (request.ValidateBeforeSubmit)
                {
                    var validationResult = await _formBuilderService.ValidateFormAsync(
                        request.FormDefinitionId, request.FormData, cancellationToken);

                    if (!validationResult.IsValid)
                    {
                        throw new InvalidOperationException($"Form validation failed: {string.Join(", ", validationResult.Errors)}");
                    }
                }

                var submitRequest = new MobileWorkflow.Application.Services.CreateFormSubmissionRequest
                {
                    FormDefinitionId = request.FormDefinitionId,
                    UserId = request.UserId.ToString(),
                    FormData = request.FormData,
                    Metadata = request.Metadata ?? new Dictionary<string, object>(),
                    Platform = request.Platform,
                    DeviceId = request.DeviceId
                };

                var result = await _formBuilderService.CreateSubmissionAsync(submitRequest, cancellationToken);

                // Submit the form
                await _formBuilderService.SubmitFormAsync(result.Id, cancellationToken);

                _logger.LogInformation("Successfully submitted form {FormId} with submission {SubmissionId}",
                    request.FormDefinitionId, result.Id);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting form {FormId} for user {UserId}",
                    request.FormDefinitionId, request.UserId);
                throw;
            }
        }
    }
}
