namespace SubscriptionManagement.Application.Interfaces
{
    public interface IRetryPolicyService
    {
        /// <summary>
        /// Execute an operation with retry logic
        /// </summary>
        Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan? baseDelay = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Execute an operation with retry logic (void return)
        /// </summary>
        Task ExecuteWithRetryAsync(
            Func<Task> operation,
            int maxRetries = 3,
            TimeSpan? baseDelay = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Execute an operation with circuit breaker pattern
        /// </summary>
        Task<T> ExecuteWithCircuitBreakerAsync<T>(
            Func<Task<T>> operation,
            string circuitName,
            int failureThreshold = 5,
            TimeSpan circuitOpenTime = default,
            CancellationToken cancellationToken = default);
    }
}
