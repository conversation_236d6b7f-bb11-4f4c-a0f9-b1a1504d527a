using Xunit;
using FluentAssertions;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;
using AnalyticsBIService.Tests.Infrastructure;

namespace AnalyticsBIService.Tests.Unit.Domain;

/// <summary>
/// Unit tests for Report domain entity
/// </summary>
public class ReportTests
{
    [Fact]
    public void Report_Constructor_Should_Create_Valid_Report()
    {
        // Arrange
        var name = "Test Report";
        var description = "Test Description";
        var generatedBy = Guid.NewGuid();
        var reportType = ReportType.PerformanceAnalysis;
        var userType = UserType.Admin;
        var period = TestDataFactory.CreateTestTimePeriod();

        // Act
        var report = new Report(name, description, reportType, generatedBy, userType, period);

        // Assert
        report.Should().NotBeNull();
        report.Id.Should().NotBeEmpty();
        report.Name.Should().Be(name);
        report.Description.Should().Be(description);
        report.GeneratedBy.Should().Be(generatedBy);
        report.Type.Should().Be(reportType);
        report.UserType.Should().Be(userType);
        report.Period.Should().Be(period);
        report.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        report.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        report.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Report_Constructor_Should_Throw_When_Name_Is_Null()
    {
        // Arrange
        var period = TestDataFactory.CreateTestTimePeriod();

        // Act & Assert
        var action = () => new Report(null!, "Description", ReportType.PerformanceAnalysis,
            Guid.NewGuid(), UserType.Admin, period);

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("name");
    }

    [Fact]
    public void Report_Constructor_Should_Throw_When_Description_Is_Null()
    {
        // Arrange
        var period = TestDataFactory.CreateTestTimePeriod();

        // Act & Assert
        var action = () => new Report("Test Report", null!, ReportType.PerformanceAnalysis,
            Guid.NewGuid(), UserType.Admin, period);

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("description");
    }

    [Fact]
    public void Report_Constructor_Should_Throw_When_Period_Is_Null()
    {
        // Act & Assert
        var action = () => new Report("Test Report", "Description", ReportType.PerformanceAnalysis,
            Guid.NewGuid(), UserType.Admin, null!);

        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("period");
    }

    [Theory]
    [InlineData(ReportType.PerformanceAnalysis)]
    [InlineData(ReportType.RevenueAnalysis)]
    [InlineData(ReportType.OperationalEfficiency)]
    [InlineData(ReportType.CustomReport)]
    public void Report_Should_Accept_Valid_ReportTypes(ReportType reportType)
    {
        // Arrange
        var period = TestDataFactory.CreateTestTimePeriod();

        // Act
        var report = new Report("Test", "Description", reportType, Guid.NewGuid(), UserType.Admin, period);

        // Assert
        report.Type.Should().Be(reportType);
    }

    [Theory]
    [InlineData(UserType.Admin)]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Broker)]
    [InlineData(UserType.Carrier)]
    public void Report_Should_Accept_Valid_UserTypes(UserType userType)
    {
        // Arrange
        var period = TestDataFactory.CreateTestTimePeriod();

        // Act
        var report = new Report("Test", "Description", ReportType.PerformanceAnalysis,
            Guid.NewGuid(), userType, period);

        // Assert
        report.UserType.Should().Be(userType);
    }

    [Fact]
    public void Report_UpdateName_Should_Update_Name_And_Timestamp()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();
        var originalUpdatedAt = report.UpdatedAt;
        var newName = "Updated Report Name";

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        report.UpdateName(newName);

        // Assert
        report.Name.Should().Be(newName);
        report.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Report_UpdateName_Should_Throw_When_Name_Is_Null()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();

        // Act & Assert
        var action = () => report.UpdateName(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Report_UpdateDescription_Should_Update_Description_And_Timestamp()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();
        var originalUpdatedAt = report.UpdatedAt;
        var newDescription = "Updated report description";

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        report.UpdateDescription(newDescription);

        // Assert
        report.Description.Should().Be(newDescription);
        report.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Report_UpdateDescription_Should_Throw_When_Description_Is_Null()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();

        // Act & Assert
        var action = () => report.UpdateDescription(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Report_UpdateParameters_Should_Update_Parameters_And_Timestamp()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();
        var originalUpdatedAt = report.UpdatedAt;
        var newParameters = new Dictionary<string, object>
        {
            { "filter", "active" },
            { "limit", 100 }
        };

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        report.UpdateParameters(newParameters);

        // Assert
        report.Parameters.Should().BeEquivalentTo(newParameters);
        report.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Report_RecordAccess_Should_Update_Access_Properties()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();
        var originalAccessCount = report.AccessCount;

        // Act
        report.RecordAccess();

        // Assert
        report.LastAccessedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        report.AccessCount.Should().Be(originalAccessCount + 1);
    }

    [Fact]
    public void Report_SetSchedule_Should_Update_Schedule_Properties_And_Timestamp()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport();
        var originalUpdatedAt = report.UpdatedAt;
        var cronExpression = "0 0 8 * * ?"; // Daily at 8 AM

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        report.SetSchedule(cronExpression);

        // Assert
        report.IsScheduled.Should().BeTrue();
        report.ScheduleCron.Should().Be(cronExpression);
        report.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Report_RemoveSchedule_Should_Clear_Schedule_Properties_And_Update_Timestamp()
    {
        // Arrange
        var report = TestDataFactory.CreateTestReport(isScheduled: true, scheduleCron: "0 0 8 * * ?");
        var originalUpdatedAt = report.UpdatedAt;

        // Wait a small amount to ensure timestamp difference
        Thread.Sleep(10);

        // Act
        report.RemoveSchedule();

        // Assert
        report.IsScheduled.Should().BeFalse();
        report.ScheduleCron.Should().BeNull();
        report.UpdatedAt.Should().BeAfter(originalUpdatedAt.Value);
    }

    [Fact]
    public void Report_Should_Have_Unique_Ids()
    {
        // Arrange & Act
        var report1 = TestDataFactory.CreateTestReport();
        var report2 = TestDataFactory.CreateTestReport();

        // Assert
        report1.Id.Should().NotBe(report2.Id);
    }

    [Fact]
    public void Report_Should_Support_Different_Time_Periods()
    {
        // Arrange
        var dailyPeriod = TestDataFactory.CreateTestTimePeriod(TimePeriod.Daily);
        var weeklyPeriod = TestDataFactory.CreateTestTimePeriod(TimePeriod.Weekly);
        var monthlyPeriod = TestDataFactory.CreateTestTimePeriod(TimePeriod.Monthly);

        // Act
        var dailyReport = TestDataFactory.CreateTestReport(period: dailyPeriod);
        var weeklyReport = TestDataFactory.CreateTestReport(period: weeklyPeriod);
        var monthlyReport = TestDataFactory.CreateTestReport(period: monthlyPeriod);

        // Assert
        dailyReport.Period.Period.Should().Be(TimePeriod.Daily);
        weeklyReport.Period.Period.Should().Be(TimePeriod.Weekly);
        monthlyReport.Period.Period.Should().Be(TimePeriod.Monthly);
    }

    [Fact]
    public void Report_Factory_Methods_Should_Create_Specific_Report_Types()
    {
        // Arrange
        var userId = Guid.NewGuid();

        // Act
        var performanceReport = TestDataFactory.CreatePerformanceAnalysisReport(userId);

        // Assert
        performanceReport.Should().NotBeNull();
        performanceReport.Type.Should().Be(ReportType.PerformanceAnalysis);
        performanceReport.GeneratedBy.Should().Be(userId);
        performanceReport.UserType.Should().Be(UserType.Admin);
        performanceReport.Name.Should().Contain("Performance Analysis");
    }

    [Fact]
    public void Report_Should_Handle_Optional_Constructor_Parameters()
    {
        // Arrange
        var name = "Test Report";
        var description = "Test Description";
        var reportType = ReportType.CustomReport;
        var generatedBy = Guid.NewGuid();
        var userType = UserType.Broker;
        var period = TestDataFactory.CreateTestTimePeriod();
        var parameters = new Dictionary<string, object> { { "key", "value" } };
        var configuration = new Dictionary<string, object> { { "config", "setting" } };
        var expiresAt = DateTime.UtcNow.AddDays(7);

        // Act
        var report = new Report(name, description, reportType, generatedBy, userType, period,
            parameters, configuration, expiresAt, true, "0 0 9 * * ?");

        // Assert
        report.Parameters.Should().BeEquivalentTo(parameters);
        report.Configuration.Should().BeEquivalentTo(configuration);
        report.ExpiresAt.Should().Be(expiresAt);
        report.IsScheduled.Should().BeTrue();
        report.ScheduleCron.Should().Be("0 0 9 * * ?");
    }
}
