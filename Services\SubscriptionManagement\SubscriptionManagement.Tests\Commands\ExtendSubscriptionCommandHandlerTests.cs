using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.ExtendSubscription;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Commands
{
    public class ExtendSubscriptionCommandHandlerTests
    {
        private readonly Mock<ISubscriptionRepository> _subscriptionRepositoryMock;
        private readonly Mock<IMessageBroker> _messageBrokerMock;
        private readonly Mock<ILogger<ExtendSubscriptionCommandHandler>> _loggerMock;
        private readonly ExtendSubscriptionCommandHandler _handler;

        public ExtendSubscriptionCommandHandlerTests()
        {
            _subscriptionRepositoryMock = new Mock<ISubscriptionRepository>();
            _messageBrokerMock = new Mock<IMessageBroker>();
            _loggerMock = new Mock<ILogger<ExtendSubscriptionCommandHandler>>();

            _handler = new ExtendSubscriptionCommandHandler(
                _subscriptionRepositoryMock.Object,
                _messageBrokerMock.Object,
                _loggerMock.Object);
        }

        private Plan CreateTestPlan()
        {
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            return new Plan(
                "Test Plan",
                "Test Description",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits);
        }

        [Fact]
        public async Task Handle_ValidExtension_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = Guid.Empty, // Will be set by handler
                ExtensionDays = 30,
                Reason = "Customer requested extension",
                ApplyAsGracePeriod = false,
                ExtendedByUserId = adminUserId
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.Equal(userId, command.UserId); // Should be set by handler
            _subscriptionRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Subscription>()), Times.Once);
            _messageBrokerMock.Verify(x => x.PublishAsync("subscription.extended", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_GracePeriodExtension_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = Guid.Empty,
                ExtensionDays = 15,
                Reason = "Grace period for payment issues",
                ApplyAsGracePeriod = true,
                ExtendedByUserId = adminUserId
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result);
            _subscriptionRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Subscription>()), Times.Once);
            _messageBrokerMock.Verify(x => x.PublishAsync("subscription.extended", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_SubscriptionNotFound_ShouldThrowException()
        {
            // Arrange
            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                ExtensionDays = 30,
                ExtendedByUserId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync((Subscription?)null);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_CancelledSubscription_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            subscription.Cancel("User cancelled");

            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = Guid.Empty,
                ExtensionDays = 30,
                ExtendedByUserId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_InvalidExtensionDays_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = Guid.Empty,
                ExtensionDays = 0, // Invalid
                ExtendedByUserId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ExcessiveExtensionDays_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            var command = new ExtendSubscriptionCommand
            {
                SubscriptionId = subscriptionId,
                UserId = Guid.Empty,
                ExtensionDays = 100, // Exceeds 90 day limit
                ExtendedByUserId = Guid.NewGuid()
            };

            _subscriptionRepositoryMock.Setup(x => x.GetByIdAsync(subscriptionId))
                .ReturnsAsync(subscription);

            // Act & Assert
            await Assert.ThrowsAsync<SubscriptionDomainException>(() => 
                _handler.Handle(command, CancellationToken.None));
        }
    }
}
