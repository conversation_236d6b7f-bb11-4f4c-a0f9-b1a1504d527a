using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;
using DataStorage.Domain.ValueObjects;

namespace DataStorage.Infrastructure.Data;

public static class DataStorageDbContextSeed
{
    public static async Task SeedAsync(DataStorageDbContext context, ILogger logger)
    {
        try
        {
            if (!await context.Documents.AnyAsync())
            {
                logger.LogInformation("Seeding Data Storage database...");

                // Sample user IDs (these would come from Identity service in real scenario)
                var adminUserId = Guid.Parse("11111111-1111-1111-1111-111111111111");
                var shipperUserId = Guid.Parse("*************-2222-2222-************");
                var carrierUserId = Guid.Parse("*************-3333-3333-************");
                var brokerUserId = Guid.Parse("*************-4444-4444-************");

                // Sample company IDs
                var shipperCompanyId = Guid.Parse("*************-5555-5555-************");
                var carrierCompanyId = Guid.Parse("*************-6666-6666-************");
                var brokerCompanyId = Guid.Parse("*************-7777-7777-************");

                var documents = new List<Document>
                {
                    // Shipper Documents
                    Document.Create(
                        fileName: "sample_eway_bill_001.pdf",
                        originalFileName: "E-Way Bill - Order #12345.pdf",
                        contentType: "application/pdf",
                        fileSize: 245760,
                        filePath: "/documents/shipper/eway_bills/sample_eway_bill_001.pdf",
                        storageProvider: "Local",
                        documentType: DocumentType.EWayBill,
                        category: DocumentCategory.Compliance,
                        uploadedBy: shipperUserId,
                        uploadedByName: "John Shipper",
                        userRole: "Shipper",
                        companyId: shipperCompanyId,
                        companyName: "ABC Logistics Ltd",
                        relatedEntityId: Guid.NewGuid(), // Order ID
                        relatedEntityType: "Order",
                        description: "E-Way Bill for shipment from Mumbai to Delhi",
                        tags: new Dictionary<string, string>
                        {
                            { "route", "mumbai-delhi" },
                            { "vehicle_type", "truck" },
                            { "priority", "high" }
                        }
                    ),

                    Document.Create(
                        fileName: "sample_invoice_001.pdf",
                        originalFileName: "Tax Invoice - INV-2024-001.pdf",
                        contentType: "application/pdf",
                        fileSize: 189440,
                        filePath: "/documents/shipper/invoices/sample_invoice_001.pdf",
                        storageProvider: "Local",
                        documentType: DocumentType.TaxInvoice,
                        category: DocumentCategory.Financial,
                        uploadedBy: shipperUserId,
                        uploadedByName: "John Shipper",
                        userRole: "Shipper",
                        companyId: shipperCompanyId,
                        companyName: "ABC Logistics Ltd",
                        relatedEntityId: Guid.NewGuid(), // Order ID
                        relatedEntityType: "Order",
                        description: "Tax invoice for goods transportation",
                        tags: new Dictionary<string, string>
                        {
                            { "invoice_number", "INV-2024-001" },
                            { "amount", "25000" },
                            { "gst_rate", "18%" }
                        }
                    ),

                    Document.Create(
                        fileName: "sample_transport_contract_001.pdf",
                        originalFileName: "Transport Agreement - Contract #TC001.pdf",
                        contentType: "application/pdf",
                        fileSize: 512000,
                        filePath: "/documents/shipper/contracts/sample_transport_contract_001.pdf",
                        storageProvider: "Local",
                        documentType: DocumentType.TransportContract,
                        category: DocumentCategory.Legal,
                        uploadedBy: shipperUserId,
                        uploadedByName: "John Shipper",
                        userRole: "Shipper",
                        companyId: shipperCompanyId,
                        companyName: "ABC Logistics Ltd",
                        description: "Master transport agreement with carrier",
                        tags: new Dictionary<string, string>
                        {
                            { "contract_type", "master_agreement" },
                            { "validity", "1_year" },
                            { "auto_renewal", "true" }
                        }
                    ),

                    // Carrier Documents
                    Document.Create(
                        fileName: "sample_pickup_photo_001.jpg",
                        originalFileName: "Pickup Photo - Warehouse A.jpg",
                        contentType: "image/jpeg",
                        fileSize: 1024000,
                        filePath: "/documents/carrier/pickup_photos/sample_pickup_photo_001.jpg",
                        storageProvider: "Local",
                        documentType: DocumentType.PickupPhoto,
                        category: DocumentCategory.Operational,
                        uploadedBy: carrierUserId,
                        uploadedByName: "Mike Carrier",
                        userRole: "Carrier",
                        companyId: carrierCompanyId,
                        companyName: "XYZ Transport Services",
                        relatedEntityId: Guid.NewGuid(), // Trip ID
                        relatedEntityType: "Trip",
                        description: "Photo taken during goods pickup at warehouse",
                        tags: new Dictionary<string, string>
                        {
                            { "location", "warehouse_a" },
                            { "timestamp", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") },
                            { "driver_id", "DRV001" }
                        }
                    ),

                    Document.Create(
                        fileName: "sample_delivery_photo_001.jpg",
                        originalFileName: "Delivery Photo - Customer Location.jpg",
                        contentType: "image/jpeg",
                        fileSize: 896000,
                        filePath: "/documents/carrier/delivery_photos/sample_delivery_photo_001.jpg",
                        storageProvider: "Local",
                        documentType: DocumentType.DeliveryPhoto,
                        category: DocumentCategory.Operational,
                        uploadedBy: carrierUserId,
                        uploadedByName: "Mike Carrier",
                        userRole: "Carrier",
                        companyId: carrierCompanyId,
                        companyName: "XYZ Transport Services",
                        relatedEntityId: Guid.NewGuid(), // Trip ID
                        relatedEntityType: "Trip",
                        description: "Photo taken after successful delivery",
                        tags: new Dictionary<string, string>
                        {
                            { "delivery_status", "completed" },
                            { "customer_present", "true" },
                            { "condition", "good" }
                        }
                    ),

                    Document.Create(
                        fileName: "sample_pod_001.pdf",
                        originalFileName: "Proof of Delivery - POD-001.pdf",
                        contentType: "application/pdf",
                        fileSize: 156000,
                        filePath: "/documents/carrier/pod/sample_pod_001.pdf",
                        storageProvider: "Local",
                        documentType: DocumentType.ProofOfDelivery,
                        category: DocumentCategory.Operational,
                        uploadedBy: carrierUserId,
                        uploadedByName: "Mike Carrier",
                        userRole: "Carrier",
                        companyId: carrierCompanyId,
                        companyName: "XYZ Transport Services",
                        relatedEntityId: Guid.NewGuid(), // Trip ID
                        relatedEntityType: "Trip",
                        description: "Digital proof of delivery with customer signature",
                        tags: new Dictionary<string, string>
                        {
                            { "signature_captured", "true" },
                            { "delivery_time", "14:30" },
                            { "recipient_name", "John Customer" }
                        }
                    ),

                    // System Documents
                    Document.Create(
                        fileName: "system_backup_001.zip",
                        originalFileName: "Daily Backup - 2024-12-18.zip",
                        contentType: "application/zip",
                        fileSize: 52428800, // 50MB
                        filePath: "/documents/system/backups/system_backup_001.zip",
                        storageProvider: "Local",
                        documentType: DocumentType.SystemBackup,
                        category: DocumentCategory.System,
                        uploadedBy: adminUserId,
                        uploadedByName: "System Admin",
                        userRole: "Admin",
                        description: "Daily system backup file",
                        tags: new Dictionary<string, string>
                        {
                            { "backup_type", "daily" },
                            { "compression", "high" },
                            { "retention_days", "30" }
                        }
                    ),

                    Document.Create(
                        fileName: "audit_report_001.pdf",
                        originalFileName: "Monthly Audit Report - December 2024.pdf",
                        contentType: "application/pdf",
                        fileSize: 2048000,
                        filePath: "/documents/system/reports/audit_report_001.pdf",
                        storageProvider: "Local",
                        documentType: DocumentType.AuditReport,
                        category: DocumentCategory.Compliance,
                        uploadedBy: adminUserId,
                        uploadedByName: "System Admin",
                        userRole: "Admin",
                        description: "Monthly compliance and audit report",
                        tags: new Dictionary<string, string>
                        {
                            { "report_period", "2024-12" },
                            { "compliance_score", "98%" },
                            { "issues_found", "2" }
                        }
                    )
                };

                await context.Documents.AddRangeAsync(documents);

                // Add sample document permissions
                var permissions = new List<DocumentPermission>();

                foreach (var doc in documents)
                {
                    // Owner has full permissions
                    permissions.Add(DocumentPermission.Create(
                        documentId: doc.Id,
                        userId: doc.UploadedBy,
                        permissionType: PermissionType.User,
                        canRead: true,
                        canWrite: true,
                        canDelete: true,
                        canShare: true,
                        grantedBy: doc.UploadedBy
                    ));

                    // Admin has full permissions on all documents
                    if (doc.UploadedBy != adminUserId)
                    {
                        permissions.Add(DocumentPermission.Create(
                            documentId: doc.Id,
                            userId: adminUserId,
                            permissionType: PermissionType.User,
                            canRead: true,
                            canWrite: true,
                            canDelete: true,
                            canShare: true,
                            grantedBy: adminUserId
                        ));
                    }

                    // Company members have read access to company documents
                    if (doc.CompanyId.HasValue)
                    {
                        permissions.Add(DocumentPermission.Create(
                            documentId: doc.Id,
                            companyId: doc.CompanyId.Value,
                            permissionType: PermissionType.Company,
                            canRead: true,
                            canWrite: false,
                            canDelete: false,
                            canShare: false,
                            grantedBy: doc.UploadedBy
                        ));
                    }
                }

                await context.DocumentPermissions.AddRangeAsync(permissions);

                await context.SaveChangesAsync();

                logger.LogInformation("Data Storage database seeded successfully with {DocumentCount} documents and {PermissionCount} permissions", 
                    documents.Count, permissions.Count);
            }
            else
            {
                logger.LogInformation("Data Storage database already contains data, skipping seed");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the Data Storage database");
            throw;
        }
    }
}
