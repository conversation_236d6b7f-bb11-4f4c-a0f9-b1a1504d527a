using MediatR;
using Shared.Domain.Common;

namespace SubscriptionManagement.Domain.Events;

public class PaymentProofRejectedEvent : IDomainEvent, INotification
{
    public Guid Id { get; }
    public DateTime OccurredOn { get; }
    public Guid PaymentProofId { get; }
    public Guid SubscriptionId { get; }
    public Guid UserId { get; }
    public Guid RejectedByUserId { get; }
    public string RejectionReason { get; }
    public DateTime RejectedAt { get; }

    public PaymentProofRejectedEvent(
        Guid paymentProofId,
        Guid subscriptionId,
        Guid userId,
        Guid rejectedByUserId,
        string rejectionReason,
        DateTime rejectedAt)
    {
        Id = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
        PaymentProofId = paymentProofId;
        SubscriptionId = subscriptionId;
        UserId = userId;
        RejectedByUserId = rejectedByUserId;
        RejectionReason = rejectionReason;
        RejectedAt = rejectedAt;
    }
}
