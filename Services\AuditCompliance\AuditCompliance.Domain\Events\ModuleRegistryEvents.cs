using Shared.Domain.Common;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Domain.Events
{
    /// <summary>
    /// Event raised when a new module is registered
    /// </summary>
    public class ModuleRegisteredEvent : DomainEvent
    {
        public ModuleRegistry Module { get; }

        public ModuleRegisteredEvent(ModuleRegistry module)
        {
            Module = module;
        }
    }

    /// <summary>
    /// Event raised when a module is updated
    /// </summary>
    public class ModuleUpdatedEvent : DomainEvent
    {
        public ModuleRegistry Module { get; }

        public ModuleUpdatedEvent(ModuleRegistry module)
        {
            Module = module;
        }
    }

    /// <summary>
    /// Event raised when a module is activated
    /// </summary>
    public class ModuleActivatedEvent : DomainEvent
    {
        public ModuleRegistry Module { get; }

        public ModuleActivatedEvent(ModuleRegistry module)
        {
            Module = module;
        }
    }

    /// <summary>
    /// Event raised when a module is deactivated
    /// </summary>
    public class ModuleDeactivatedEvent : DomainEvent
    {
        public ModuleRegistry Module { get; }

        public ModuleDeactivatedEvent(ModuleRegistry module)
        {
            Module = module;
        }
    }

    /// <summary>
    /// Event raised when module health status changes
    /// </summary>
    public class ModuleHealthStatusChangedEvent : DomainEvent
    {
        public ModuleRegistry Module { get; }
        public bool PreviousHealthStatus { get; }
        public bool CurrentHealthStatus { get; }

        public ModuleHealthStatusChangedEvent(ModuleRegistry module, bool previousHealthStatus, bool currentHealthStatus)
        {
            Module = module;
            PreviousHealthStatus = previousHealthStatus;
            CurrentHealthStatus = currentHealthStatus;
        }
    }

    /// <summary>
    /// Event raised when module access is granted to a user
    /// </summary>
    public class ModuleAccessGrantedEvent : DomainEvent
    {
        public ModuleAccessControl AccessControl { get; }

        public ModuleAccessGrantedEvent(ModuleAccessControl accessControl)
        {
            AccessControl = accessControl;
        }
    }

    /// <summary>
    /// Event raised when module access is revoked from a user
    /// </summary>
    public class ModuleAccessRevokedEvent : DomainEvent
    {
        public ModuleAccessControl AccessControl { get; }
        public Guid RevokedBy { get; }
        public string? Reason { get; }

        public ModuleAccessRevokedEvent(ModuleAccessControl accessControl, Guid revokedBy, string? reason)
        {
            AccessControl = accessControl;
            RevokedBy = revokedBy;
            Reason = reason;
        }
    }

    /// <summary>
    /// Event raised when module access is extended
    /// </summary>
    public class ModuleAccessExtendedEvent : DomainEvent
    {
        public ModuleAccessControl AccessControl { get; }
        public DateTime NewExpiryDate { get; }

        public ModuleAccessExtendedEvent(ModuleAccessControl accessControl, DateTime newExpiryDate)
        {
            AccessControl = accessControl;
            NewExpiryDate = newExpiryDate;
        }
    }

    /// <summary>
    /// Event raised when action access is granted for a module
    /// </summary>
    public class ModuleActionAccessGrantedEvent : DomainEvent
    {
        public ModuleAccessControl AccessControl { get; }
        public string Action { get; }

        public ModuleActionAccessGrantedEvent(ModuleAccessControl accessControl, string action)
        {
            AccessControl = accessControl;
            Action = action;
        }
    }

    /// <summary>
    /// Event raised when action access is denied for a module
    /// </summary>
    public class ModuleActionAccessDeniedEvent : DomainEvent
    {
        public ModuleAccessControl AccessControl { get; }
        public string Action { get; }

        public ModuleActionAccessDeniedEvent(ModuleAccessControl accessControl, string action)
        {
            AccessControl = accessControl;
            Action = action;
        }
    }

    // Custom Report Template Events

    /// <summary>
    /// Event raised when a custom report template is created
    /// </summary>
    public class CustomReportTemplateCreatedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateCreatedEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is updated
    /// </summary>
    public class CustomReportTemplateUpdatedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateUpdatedEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is activated
    /// </summary>
    public class CustomReportTemplateActivatedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateActivatedEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is deactivated
    /// </summary>
    public class CustomReportTemplateDeactivatedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateDeactivatedEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is made public
    /// </summary>
    public class CustomReportTemplateMadePublicEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateMadePublicEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is made private
    /// </summary>
    public class CustomReportTemplateMadePrivateEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateMadePrivateEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when a custom report template is used
    /// </summary>
    public class CustomReportTemplateUsedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }

        public CustomReportTemplateUsedEvent(CustomComplianceReportTemplate template)
        {
            Template = template;
        }
    }

    /// <summary>
    /// Event raised when template permission is granted
    /// </summary>
    public class TemplatePermissionGrantedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }
        public Guid UserId { get; }
        public TemplatePermissionType PermissionType { get; }

        public TemplatePermissionGrantedEvent(CustomComplianceReportTemplate template, Guid userId, TemplatePermissionType permissionType)
        {
            Template = template;
            UserId = userId;
            PermissionType = permissionType;
        }
    }

    /// <summary>
    /// Event raised when template permission is revoked
    /// </summary>
    public class TemplatePermissionRevokedEvent : DomainEvent
    {
        public CustomComplianceReportTemplate Template { get; }
        public Guid UserId { get; }

        public TemplatePermissionRevokedEvent(CustomComplianceReportTemplate template, Guid userId)
        {
            Template = template;
            UserId = userId;
        }
    }
}



