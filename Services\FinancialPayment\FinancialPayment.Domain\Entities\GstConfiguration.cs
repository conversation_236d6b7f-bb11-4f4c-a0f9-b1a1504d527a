using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Domain entity representing GST configuration for different service types and locations
/// </summary>
public class GstConfiguration : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ServiceCategory ServiceCategory { get; private set; }
    public TaxJurisdiction Jurisdiction { get; private set; }
    public GstRate GstRate { get; private set; }
    public TaxRate TaxRate { get; private set; }
    public TaxConfigurationStatus Status { get; private set; }
    public string? HsnCode { get; private set; }
    public Money MinimumAmount { get; private set; }
    public Money MaximumAmount { get; private set; }
    public bool IsReverseChargeApplicable { get; private set; }
    public string? ReverseChargeConditions { get; private set; }
    public string CreatedBy { get; private set; }
    public string? ModifiedBy { get; private set; }
    public DateTime? ModifiedAt { get; private set; }

    // Navigation properties
    private readonly List<GstConfigurationHistory> _history = new();
    public IReadOnlyList<GstConfigurationHistory> History => _history.AsReadOnly();

    private GstConfiguration() { }

    public GstConfiguration(
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string createdBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("GST configuration name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("GST configuration description cannot be empty", nameof(description));

        if (string.IsNullOrWhiteSpace(createdBy))
            throw new ArgumentException("Created by cannot be empty", nameof(createdBy));

        if (minimumAmount.Currency != maximumAmount.Currency)
            throw new ArgumentException("Minimum and maximum amounts must have the same currency");

        if (minimumAmount.Amount > maximumAmount.Amount)
            throw new ArgumentException("Minimum amount cannot be greater than maximum amount");

        ValidateGstRateConsistency(gstRate, taxRate);

        Name = name.Trim();
        Description = description.Trim();
        ServiceCategory = serviceCategory;
        Jurisdiction = jurisdiction;
        GstRate = gstRate;
        TaxRate = taxRate;
        MinimumAmount = minimumAmount;
        MaximumAmount = maximumAmount;
        HsnCode = hsnCode?.Trim();
        IsReverseChargeApplicable = isReverseChargeApplicable;
        ReverseChargeConditions = reverseChargeConditions?.Trim();
        Status = TaxConfigurationStatus.Draft;
        CreatedBy = createdBy.Trim();

        AddHistoryEntry("Created", $"GST configuration created by {createdBy}");
    }

    public void UpdateConfiguration(
        string name,
        string description,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        GstRate gstRate,
        TaxRate taxRate,
        Money minimumAmount,
        Money maximumAmount,
        string modifiedBy,
        string? hsnCode = null,
        bool isReverseChargeApplicable = false,
        string? reverseChargeConditions = null)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot update archived GST configuration");

        if (string.IsNullOrWhiteSpace(modifiedBy))
            throw new ArgumentException("Modified by cannot be empty", nameof(modifiedBy));

        ValidateGstRateConsistency(gstRate, taxRate);

        var changes = new List<string>();

        if (Name != name.Trim())
        {
            changes.Add($"Name: {Name} → {name.Trim()}");
            Name = name.Trim();
        }

        if (Description != description.Trim())
        {
            changes.Add($"Description updated");
            Description = description.Trim();
        }

        if (ServiceCategory != serviceCategory)
        {
            changes.Add($"Service Category: {ServiceCategory} → {serviceCategory}");
            ServiceCategory = serviceCategory;
        }

        if (!Jurisdiction.Equals(jurisdiction))
        {
            changes.Add($"Jurisdiction: {Jurisdiction.GetFullJurisdiction()} → {jurisdiction.GetFullJurisdiction()}");
            Jurisdiction = jurisdiction;
        }

        if (GstRate != gstRate)
        {
            changes.Add($"GST Rate: {GstRate}% → {gstRate}%");
            GstRate = gstRate;
        }

        if (!TaxRate.Equals(taxRate))
        {
            changes.Add($"Tax Rate updated");
            TaxRate = taxRate;
        }

        if (!MinimumAmount.Equals(minimumAmount))
        {
            changes.Add($"Minimum Amount: {MinimumAmount} → {minimumAmount}");
            MinimumAmount = minimumAmount;
        }

        if (!MaximumAmount.Equals(maximumAmount))
        {
            changes.Add($"Maximum Amount: {MaximumAmount} → {maximumAmount}");
            MaximumAmount = maximumAmount;
        }

        if (HsnCode != hsnCode?.Trim())
        {
            changes.Add($"HSN Code: {HsnCode ?? "None"} → {hsnCode?.Trim() ?? "None"}");
            HsnCode = hsnCode?.Trim();
        }

        if (IsReverseChargeApplicable != isReverseChargeApplicable)
        {
            changes.Add($"Reverse Charge: {IsReverseChargeApplicable} → {isReverseChargeApplicable}");
            IsReverseChargeApplicable = isReverseChargeApplicable;
        }

        if (ReverseChargeConditions != reverseChargeConditions?.Trim())
        {
            changes.Add("Reverse Charge Conditions updated");
            ReverseChargeConditions = reverseChargeConditions?.Trim();
        }

        if (changes.Any())
        {
            ModifiedBy = modifiedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Updated", $"Configuration updated by {modifiedBy}: {string.Join(", ", changes)}");
        }
    }

    public void Activate(string activatedBy)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot activate archived GST configuration");

        if (string.IsNullOrWhiteSpace(activatedBy))
            throw new ArgumentException("Activated by cannot be empty", nameof(activatedBy));

        if (Status != TaxConfigurationStatus.Active)
        {
            Status = TaxConfigurationStatus.Active;
            ModifiedBy = activatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Activated", $"Configuration activated by {activatedBy}");
        }
    }

    public void Deactivate(string deactivatedBy, string reason)
    {
        if (Status == TaxConfigurationStatus.Archived)
            throw new InvalidOperationException("Cannot deactivate archived GST configuration");

        if (string.IsNullOrWhiteSpace(deactivatedBy))
            throw new ArgumentException("Deactivated by cannot be empty", nameof(deactivatedBy));

        if (Status != TaxConfigurationStatus.Inactive)
        {
            Status = TaxConfigurationStatus.Inactive;
            ModifiedBy = deactivatedBy.Trim();
            ModifiedAt = DateTime.UtcNow;
            SetUpdatedAt();

            AddHistoryEntry("Deactivated", $"Configuration deactivated by {deactivatedBy}. Reason: {reason}");
        }
    }

    public void Archive(string archivedBy, string reason)
    {
        if (string.IsNullOrWhiteSpace(archivedBy))
            throw new ArgumentException("Archived by cannot be empty", nameof(archivedBy));

        Status = TaxConfigurationStatus.Archived;
        ModifiedBy = archivedBy.Trim();
        ModifiedAt = DateTime.UtcNow;
        SetUpdatedAt();

        AddHistoryEntry("Archived", $"Configuration archived by {archivedBy}. Reason: {reason}");
    }

    public Money CalculateGst(Money baseAmount)
    {
        if (Status != TaxConfigurationStatus.Active)
            throw new InvalidOperationException("Cannot calculate GST using inactive configuration");

        if (!TaxRate.IsEffectiveOn(DateTime.UtcNow))
            throw new InvalidOperationException("Tax rate is not effective for current date");

        if (baseAmount.Amount < MinimumAmount.Amount || baseAmount.Amount > MaximumAmount.Amount)
            return Money.Zero(baseAmount.Currency);

        var gstAmount = baseAmount.Amount * (TaxRate.Rate / 100);
        return new Money(gstAmount, baseAmount.Currency);
    }

    public bool IsApplicableFor(ServiceCategory category, TaxJurisdiction jurisdiction, DateTime date)
    {
        return Status == TaxConfigurationStatus.Active &&
               ServiceCategory == category &&
               Jurisdiction.Equals(jurisdiction) &&
               TaxRate.IsEffectiveOn(date);
    }

    private void AddHistoryEntry(string action, string details)
    {
        var historyEntry = new GstConfigurationHistory(Id, action, details, ModifiedBy ?? CreatedBy);
        _history.Add(historyEntry);
    }

    private static void ValidateGstRateConsistency(GstRate gstRate, TaxRate taxRate)
    {
        var expectedRate = (decimal)gstRate;
        if (Math.Abs(taxRate.Rate - expectedRate) > 0.01m)
        {
            throw new ArgumentException($"Tax rate {taxRate.Rate}% does not match GST rate {gstRate} ({expectedRate}%)");
        }
    }
}

/// <summary>
/// Entity representing GST configuration history for audit purposes
/// </summary>
public class GstConfigurationHistory : BaseEntity
{
    public Guid GstConfigurationId { get; private set; }
    public string Action { get; private set; }
    public string Details { get; private set; }
    public string ModifiedBy { get; private set; }
    public DateTime ModifiedAt { get; private set; }

    private GstConfigurationHistory() { }

    public GstConfigurationHistory(Guid gstConfigurationId, string action, string details, string modifiedBy)
    {
        GstConfigurationId = gstConfigurationId;
        Action = action;
        Details = details;
        ModifiedBy = modifiedBy;
        ModifiedAt = DateTime.UtcNow;
    }
}


