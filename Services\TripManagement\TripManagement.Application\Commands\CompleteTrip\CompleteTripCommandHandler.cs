using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;


namespace TripManagement.Application.Commands.CompleteTrip;

public class CompleteTripCommandHandler : IRequestHandler<CompleteTripCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CompleteTripCommandHandler> _logger;

    public CompleteTripCommandHandler(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<CompleteTripCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(CompleteTripCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Completing trip {TripId} by driver {DriverId}",
            request.TripId, request.DriverId);

        // Get trip
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
        {
            _logger.LogWarning("Trip {TripId} not found", request.TripId);
            throw new ArgumentException($"Trip {request.TripId} not found");
        }

        // Validate trip can be completed
        if (trip.Status != TripStatus.InProgress)
        {
            _logger.LogWarning("Trip {TripId} cannot be completed. Current status: {Status}",
                request.TripId, trip.Status);
            throw new InvalidOperationException($"Trip cannot be completed. Current status: {trip.Status}");
        }

        // Validate driver is assigned to this trip
        if (trip.DriverId != request.DriverId)
        {
            _logger.LogWarning("Driver {DriverId} is not assigned to trip {TripId}",
                request.DriverId, request.TripId);
            throw new InvalidOperationException("Driver is not assigned to this trip");
        }

        // Get driver and vehicle to update status
        var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
        if (driver == null)
        {
            _logger.LogWarning("Driver {DriverId} not found", request.DriverId);
            throw new ArgumentException($"Driver {request.DriverId} not found");
        }

        var vehicle = trip.VehicleId.HasValue
            ? await _vehicleRepository.GetByIdAsync(trip.VehicleId.Value, cancellationToken)
            : null;

        // Complete the trip
        trip.Complete();

        // Update driver status to available
        driver.UpdateStatus(DriverStatus.Available);

        // Update vehicle status to available if assigned
        if (vehicle != null)
        {
            vehicle.UpdateStatus(VehicleStatus.Available);
        }

        // Save changes
        _tripRepository.Update(trip);
        _driverRepository.Update(driver);
        if (vehicle != null)
        {
            _vehicleRepository.Update(vehicle);
        }
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("trip.completed", new
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            OrderId = trip.OrderId,
            CarrierId = trip.CarrierId,
            DriverId = request.DriverId,
            VehicleId = trip.VehicleId,
            ActualStartTime = trip.StartedAt,
            ActualEndTime = request.ActualEndTime,
            EstimatedDistanceKm = trip.EstimatedDistanceKm,
            ActualDistanceKm = request.ActualDistanceKm,
            Status = trip.Status.ToString(),
            CompletionNotes = request.CompletionNotes,
            ProofOfDeliveryUploaded = request.ProofOfDeliveryUploaded,
            CompletedAt = DateTime.UtcNow
        });

        _logger.LogInformation("Successfully completed trip {TripId} by driver {DriverId}",
            request.TripId, request.DriverId);

        return true;
    }
}
