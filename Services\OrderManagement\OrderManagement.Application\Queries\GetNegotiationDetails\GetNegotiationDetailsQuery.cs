using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetNegotiationDetails;

public class GetNegotiationDetailsQuery : IRequest<RfqNegotiationDetailsDto?>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool IncludeBrokerComments { get; set; } = true;
    public bool IncludeInternalComments { get; set; } = false;
    public bool IncludeReverseAuctionDetails { get; set; } = true;
}
