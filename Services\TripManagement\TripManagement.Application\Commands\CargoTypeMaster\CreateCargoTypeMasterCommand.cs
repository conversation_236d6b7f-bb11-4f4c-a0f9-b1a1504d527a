using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.CargoTypeMaster;

/// <summary>
/// Command to create a new cargo type master
/// </summary>
public class CreateCargoTypeMasterCommand : IRequest<CargoTypeMasterDto>
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool RequiresSpecialHandling { get; set; }
    public bool IsHazardous { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public decimal? MinTemperatureCelsius { get; set; }
    public decimal? MaxTemperatureCelsius { get; set; }
    public string? HandlingInstructions { get; set; }
    public string? SafetyRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}
