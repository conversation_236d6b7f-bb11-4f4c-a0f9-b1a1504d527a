using FluentAssertions;
using FluentValidation.TestHelper;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Validators;
using Xunit;

namespace MobileWorkflow.Tests.Validators;

public class CreateMilestoneTemplateCommandValidatorTests
{
    private readonly CreateMilestoneTemplateCommandValidator _validator;

    public CreateMilestoneTemplateCommandValidatorTests()
    {
        _validator = new CreateMilestoneTemplateCommandValidator();
    }

    [Fact]
    public void Validate_WithValidCommand_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Template Name",
            Description = "Valid description for the template",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    IsRequired = true,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 100.0m }
                    }
                }
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Validate_WithInvalidName_ShouldHaveValidationError(string invalidName)
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = invalidName,
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void Validate_WithNameTooLong_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = new string('A', 201), // 201 characters, exceeds 200 limit
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
              .WithErrorMessage("Template name cannot exceed 200 characters");
    }

    [Fact]
    public void Validate_WithInvalidCharactersInName_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Invalid@Name#With$Special%Characters",
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Name)
              .WithErrorMessage("Template name contains invalid characters");
    }

    [Theory]
    [InlineData("InvalidType")]
    [InlineData("TRIP")]
    [InlineData("order")]
    public void Validate_WithInvalidType_ShouldHaveValidationError(string invalidType)
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Name",
            Description = "Valid description",
            Type = invalidType,
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Type)
              .WithErrorMessage("Invalid template type");
    }

    [Theory]
    [InlineData("Trip")]
    [InlineData("Order")]
    [InlineData("Project")]
    [InlineData("Delivery")]
    [InlineData("Pickup")]
    [InlineData("Custom")]
    public void Validate_WithValidType_ShouldNotHaveValidationError(string validType)
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Name",
            Description = "Valid description",
            Type = validType,
            Category = "Logistics",
            CreatedBy = "<EMAIL>"
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Type);
    }

    [Fact]
    public void Validate_WithDuplicateSequenceNumbers_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Name",
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m }
                    }
                },
                new CreateMilestoneStepRequest
                {
                    Name = "Step 2",
                    Description = "Second step",
                    SequenceNumber = 1, // Duplicate sequence number
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m }
                    }
                }
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Steps)
              .WithErrorMessage("Step sequence numbers must be unique and sequential starting from 1");
    }

    [Fact]
    public void Validate_WithInvalidPayoutPercentages_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Name",
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 60.0m }
                    }
                },
                new CreateMilestoneStepRequest
                {
                    Name = "Step 2",
                    Description = "Second step",
                    SequenceNumber = 2,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m } // Total = 110%
                    }
                }
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Steps)
              .WithErrorMessage("Total payout percentages must equal 100%");
    }

    [Fact]
    public void Validate_WithNonConsecutiveSequenceNumbers_ShouldHaveValidationError()
    {
        // Arrange
        var command = new CreateMilestoneTemplateCommand
        {
            Name = "Valid Name",
            Description = "Valid description",
            Type = "Trip",
            Category = "Logistics",
            CreatedBy = "<EMAIL>",
            Steps = new List<CreateMilestoneStepRequest>
            {
                new CreateMilestoneStepRequest
                {
                    Name = "Step 1",
                    Description = "First step",
                    SequenceNumber = 1,
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m }
                    }
                },
                new CreateMilestoneStepRequest
                {
                    Name = "Step 3",
                    Description = "Third step",
                    SequenceNumber = 3, // Skips sequence number 2
                    PayoutRules = new List<CreateMilestonePayoutRuleRequest>
                    {
                        new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m }
                    }
                }
            }
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Steps)
              .WithErrorMessage("Step sequence numbers must be unique and sequential starting from 1");
    }
}

public class CreateMilestoneStepRequestValidatorTests
{
    private readonly CreateMilestoneStepRequestValidator _validator;

    public CreateMilestoneStepRequestValidatorTests()
    {
        _validator = new CreateMilestoneStepRequestValidator();
    }

    [Fact]
    public void Validate_WithValidRequest_ShouldNotHaveValidationErrors()
    {
        // Arrange
        var request = new CreateMilestoneStepRequest
        {
            Name = "Valid Step Name",
            Description = "Valid step description",
            SequenceNumber = 1,
            IsRequired = true,
            TriggerCondition = "status=completed",
            PayoutRules = new List<CreateMilestonePayoutRuleRequest>
            {
                new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m }
            }
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void Validate_WithInvalidSequenceNumber_ShouldHaveValidationError(int invalidSequence)
    {
        // Arrange
        var request = new CreateMilestoneStepRequest
        {
            Name = "Valid Step Name",
            Description = "Valid step description",
            SequenceNumber = invalidSequence,
            PayoutRules = new List<CreateMilestonePayoutRuleRequest>()
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.SequenceNumber)
              .WithErrorMessage("Sequence number must be greater than 0");
    }

    [Fact]
    public void Validate_WithInvalidTriggerCondition_ShouldHaveValidationError()
    {
        // Arrange
        var request = new CreateMilestoneStepRequest
        {
            Name = "Valid Step Name",
            Description = "Valid step description",
            SequenceNumber = 1,
            TriggerCondition = "invalid_condition_format",
            PayoutRules = new List<CreateMilestonePayoutRuleRequest>()
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.TriggerCondition)
              .WithErrorMessage("Invalid trigger condition format");
    }

    [Fact]
    public void Validate_WithExcessivePayoutPercentage_ShouldHaveValidationError()
    {
        // Arrange
        var request = new CreateMilestoneStepRequest
        {
            Name = "Valid Step Name",
            Description = "Valid step description",
            SequenceNumber = 1,
            PayoutRules = new List<CreateMilestonePayoutRuleRequest>
            {
                new CreateMilestonePayoutRuleRequest { PayoutPercentage = 60.0m },
                new CreateMilestonePayoutRuleRequest { PayoutPercentage = 50.0m } // Total = 110%
            }
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PayoutRules)
              .WithErrorMessage("Total payout percentage for step cannot exceed 100%");
    }
}

public class CreateMilestonePayoutRuleRequestValidatorTests
{
    private readonly CreateMilestonePayoutRuleRequestValidator _validator;

    public CreateMilestonePayoutRuleRequestValidatorTests()
    {
        _validator = new CreateMilestonePayoutRuleRequestValidator();
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(101)]
    public void Validate_WithInvalidPayoutPercentage_ShouldHaveValidationError(decimal invalidPercentage)
    {
        // Arrange
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = invalidPercentage,
            Description = "Valid description"
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PayoutPercentage);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(50.5)]
    [InlineData(100)]
    public void Validate_WithValidPayoutPercentage_ShouldNotHaveValidationError(decimal validPercentage)
    {
        // Arrange
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = validPercentage,
            Description = "Valid description"
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PayoutPercentage);
    }

    [Fact]
    public void Validate_WithInvalidTriggerCondition_ShouldHaveValidationError()
    {
        // Arrange
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 50.0m,
            TriggerCondition = "invalid_format",
            Description = "Valid description"
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.TriggerCondition)
              .WithErrorMessage("Invalid trigger condition format");
    }

    [Theory]
    [InlineData("status=completed")]
    [InlineData("quality_check=passed")]
    [InlineData("time_elapsed=30")]
    public void Validate_WithValidTriggerCondition_ShouldNotHaveValidationError(string validCondition)
    {
        // Arrange
        var request = new CreateMilestonePayoutRuleRequest
        {
            PayoutPercentage = 50.0m,
            TriggerCondition = validCondition,
            Description = "Valid description"
        };

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.TriggerCondition);
    }
}
