using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IBrokerCarrierNetworkRepository
{
    Task<BrokerCarrierNetwork?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<BrokerCarrierNetwork?> GetByBrokerAndCarrierAsync(Guid brokerId, Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetByStatusAsync(NetworkRelationshipStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetActiveNetworksAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetExclusiveNetworksAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetNetworksByPriorityAsync(Guid brokerId, int maxPriority = 5, CancellationToken cancellationToken = default);
    Task<IEnumerable<BrokerCarrierNetwork>> GetExpiredContractsAsync(CancellationToken cancellationToken = default);
    Task<(IEnumerable<BrokerCarrierNetwork> Networks, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? brokerId = null,
        Guid? carrierId = null,
        NetworkRelationshipStatus? status = null,
        CancellationToken cancellationToken = default);
    
    void Add(BrokerCarrierNetwork network);
    void Update(BrokerCarrierNetwork network);
    void Remove(BrokerCarrierNetwork network);
}
