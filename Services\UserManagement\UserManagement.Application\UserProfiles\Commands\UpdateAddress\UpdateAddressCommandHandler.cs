using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.UserProfiles.Commands.UpdateAddress
{
    public class UpdateAddressCommandHandler : IRequestHandler<UpdateAddressCommand, bool>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<UpdateAddressCommandHandler> _logger;

        public UpdateAddressCommandHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<UpdateAddressCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdateAddressCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating address for profile {ProfileId}", request.Id);

                var userProfile = await _userProfileRepository.GetByIdAsync(request.Id);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                // Update address using domain method
                userProfile.UpdateAddress(
                    request.Address, 
                    request.City, 
                    request.State, 
                    request.PostalCode, 
                    request.Country);

                // Save changes
                await _userProfileRepository.UpdateAsync(userProfile);

                _logger.LogInformation("Successfully updated address for profile {ProfileId}", request.Id);

                return true;
            }
            catch (UserManagementDomainException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating address for profile {ProfileId}", request.Id);
                throw;
            }
        }
    }
}
