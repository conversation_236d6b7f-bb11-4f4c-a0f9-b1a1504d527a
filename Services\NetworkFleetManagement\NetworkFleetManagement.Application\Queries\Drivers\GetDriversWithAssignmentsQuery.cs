using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.Queries.Drivers;

public record GetDriversWithAssignmentsQuery(
    Guid CarrierId,
    int PageNumber = 1,
    int PageSize = 10,
    DriverStatus? Status = null,
    OnboardingStatus? OnboardingStatus = null,
    bool? IsAssigned = null,
    bool? HasExpiredLicense = null,
    bool? IsActive = null, // Active in last 24 hours
    string? SearchTerm = null) : IRequest<PagedResult<DriverDashboardDto>>;
