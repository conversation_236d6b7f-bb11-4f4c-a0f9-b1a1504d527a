using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.AddTimelineEvent;
using OrderManagement.Application.Queries.GetOrderTimeline;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/timeline")]
[ApiVersion("1.0")]
[Authorize]
public class TimelineController : BaseController
{
    /// <summary>
    /// Get order timeline with filtering and pagination
    /// </summary>
    [HttpGet("order/{orderId:guid}")]
    [ProducesResponseType(typeof(PagedResult<OrderTimelineDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetOrderTimeline(
        Guid orderId,
        [FromQuery] OrderTimelineEventType? eventType = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] Guid? actorId = null,
        [FromQuery] string? actorRole = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "EventTimestamp",
        [FromQuery] string? sortDirection = "desc",
        [FromQuery] bool includeMetadata = false)
    {
        try
        {
            var query = new GetOrderTimelineQuery
            {
                OrderId = orderId,
                EventType = eventType,
                FromDate = fromDate,
                ToDate = toDate,
                ActorId = actorId,
                ActorRole = actorRole,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection,
                IncludeMetadata = includeMetadata
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while retrieving timeline for order {OrderId}", orderId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving timeline for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while retrieving the order timeline");
        }
    }

    /// <summary>
    /// Add a timeline event to an order
    /// </summary>
    [HttpPost("order/{orderId:guid}/events")]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AddTimelineEvent(
        Guid orderId,
        [FromBody] AddTimelineEventRequest request)
    {
        try
        {
            var command = new AddTimelineEventCommand
            {
                OrderId = orderId,
                EventType = request.EventType,
                EventDescription = request.EventDescription,
                ActorId = GetCurrentUserId(),
                ActorName = GetCurrentUserName(),
                ActorRole = GetCurrentUserRole(),
                AdditionalData = request.AdditionalData,
                PreviousStatus = request.PreviousStatus,
                NewStatus = request.NewStatus,
                Notes = request.Notes,
                IpAddress = GetClientIpAddress(),
                UserAgent = GetUserAgent(),
                CorrelationId = request.CorrelationId,
                SessionId = GetSessionId(),
                Metadata = request.Metadata
            };

            var eventId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetOrderTimeline), new { orderId }, eventId);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while adding timeline event for order {OrderId}", orderId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error adding timeline event for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while adding the timeline event");
        }
    }

    /// <summary>
    /// Get aggregated timeline including trip events
    /// </summary>
    [HttpGet("order/{orderId:guid}/aggregate")]
    [ProducesResponseType(typeof(OrderTimelineAggregateDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetAggregatedTimeline(
        Guid orderId,
        [FromQuery] bool includeTripEvents = true,
        [FromQuery] bool includeStatistics = true)
    {
        try
        {
            // This would be implemented as a separate query handler
            // For now, return a placeholder response
            var result = new OrderTimelineAggregateDto
            {
                OrderId = orderId,
                OrderNumber = "ORD-PLACEHOLDER",
                CurrentStatus = OrderStatus.Created,
                CreatedAt = DateTime.UtcNow,
                Timeline = new List<OrderTimelineDto>(),
                TripEvents = new List<TripTimelineDto>(),
                Statistics = new TimelineStatistics()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving aggregated timeline for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while retrieving the aggregated timeline");
        }
    }

    private string? GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString();
    }

    private string? GetUserAgent()
    {
        return HttpContext.Request.Headers["User-Agent"].FirstOrDefault();
    }

    private string? GetSessionId()
    {
        return HttpContext.Session?.Id;
    }
}

public class AddTimelineEventRequest
{
    public OrderTimelineEventType EventType { get; set; }
    public string EventDescription { get; set; } = string.Empty;
    public string? AdditionalData { get; set; }
    public OrderStatus? PreviousStatus { get; set; }
    public OrderStatus? NewStatus { get; set; }
    public string? Notes { get; set; }
    public Guid? CorrelationId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
