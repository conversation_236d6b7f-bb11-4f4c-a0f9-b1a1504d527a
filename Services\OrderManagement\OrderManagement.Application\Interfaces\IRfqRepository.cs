using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Interfaces;

public interface IRfqRepository
{
    Task<RFQ?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<RFQ?> GetByRfqNumberAsync(string rfqNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<RFQ>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RFQ>> GetPublishedRfqsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RFQ>> GetRfqsByStatusAsync(RfqStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<RFQ>> GetExpiringRfqsAsync(DateTime beforeDate, CancellationToken cancellationToken = default);
    Task<(IEnumerable<RFQ> Rfqs, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? transportCompanyId = null,
        RfqStatus? status = null,
        CancellationToken cancellationToken = default);

    // New methods for query handlers
    Task<List<RFQ>> GetByTransportCompanyAsync(
        Guid transportCompanyId,
        int page,
        int pageSize,
        RfqStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<List<RFQ>> GetRfqsByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);

    Task<int> GetCountByTransportCompanyAsync(
        Guid transportCompanyId,
        RfqStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<List<RFQ>> GetPublishedRfqsAsync(
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        LoadType? loadType = null,
        decimal? maxBudget = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default);

    Task<int> GetPublishedRfqsCountAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        LoadType? loadType = null,
        decimal? maxBudget = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default);

    // Timeframe-related methods
    Task<List<RFQ>> GetExpiredActiveRfqsAsync(CancellationToken cancellationToken = default);
    Task<List<RFQ>> GetRfqsApproachingExpirationAsync(int[] hoursBeforeExpiration, CancellationToken cancellationToken = default);
    Task<List<RFQ>> GetRfqsExpiringSoonAsync(int hoursBeforeExpiration, CancellationToken cancellationToken = default);

    // Advanced filtering methods
    Task<(List<RFQ> Rfqs, int TotalCount)> GetFilteredRfqsAsync(
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<VehicleType>? vehicleTypes = null,
        List<LoadType>? loadTypes = null,
        List<RfqStatus>? statuses = null,
        Guid? transportCompanyId = null,
        Guid? brokerId = null,
        Guid? carrierId = null,
        string? pickupCity = null,
        string? deliveryCity = null,
        string? pickupState = null,
        string? deliveryState = null,
        decimal? minBudget = null,
        decimal? maxBudget = null,
        bool? isUrgent = null,
        bool? hasDocuments = null,
        bool? hasBids = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default);

    Task<(List<RFQ> Rfqs, int TotalCount)> SearchRfqsAsync(
        string searchTerm,
        int page,
        int pageSize,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<RfqStatus>? statuses = null,
        Guid? transportCompanyId = null,
        bool excludeExpired = true,
        CancellationToken cancellationToken = default);

    // Monitoring methods
    Task<List<RFQ>> GetRfqsWithoutBidsAsync(DateTime publishedBefore, CancellationToken cancellationToken = default);
    Task<List<RFQ>> GetRfqsWithLowBidCountAsync(int minimumBids, DateTime publishedBefore, CancellationToken cancellationToken = default);

    // New methods for Shipper Portal features
    Task<RFQ?> GetByIdWithBidsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<RFQ?> GetByIdWithNegotiationsAsync(Guid negotiationId, CancellationToken cancellationToken = default);
    Task SaveChangesAsync(CancellationToken cancellationToken = default);

    Task AddAsync(RFQ rfq, CancellationToken cancellationToken = default);
    void Update(RFQ rfq);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IRfqBidRepository
{
    Task<RfqBid?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<RfqBid>> GetByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RfqBid>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default);
    Task<RfqBid?> GetByBidNumberAsync(string bidNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<RfqBid>> GetBidsByStatusAsync(BidStatus status, CancellationToken cancellationToken = default);
    Task<(IEnumerable<RfqBid> Bids, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? rfqId = null,
        Guid? brokerId = null,
        BidStatus? status = null,
        CancellationToken cancellationToken = default);

    // New methods for query handlers
    Task<List<RfqBid>> GetByBrokerAsync(
        Guid brokerId,
        int page,
        int pageSize,
        BidStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task<int> GetCountByBrokerAsync(
        Guid brokerId,
        BidStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task AddAsync(RfqBid bid, CancellationToken cancellationToken = default);
    void Update(RfqBid bid);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Analytics methods for GetBrokerQuoteAnalyticsQueryHandler
    Task<List<RfqBid>> GetBrokerBidsWithFiltersAsync(
        Guid brokerId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<Guid>? carrierIds = null,
        List<LoadType>? loadTypes = null,
        List<string>? routes = null,
        CancellationToken cancellationToken = default);
}

public interface IRfqTimelineRepository
{
    Task<List<RfqTimeline>> GetByRfqIdAsync(
        Guid rfqId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        List<RfqTimelineEventType>? eventTypes = null,
        CancellationToken cancellationToken = default);

    Task<RfqTimeline?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task AddAsync(RfqTimeline timelineEvent, CancellationToken cancellationToken = default);

    Task AddRangeAsync(List<RfqTimeline> timelineEvents, CancellationToken cancellationToken = default);

    void Update(RfqTimeline timelineEvent);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IRfqRoutingHistoryRepository
{
    Task<List<RfqRoutingHistory>> GetByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default);

    Task<RfqRoutingHistory?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<RfqRoutingHistory>> GetPendingRoutingsAsync(CancellationToken cancellationToken = default);

    Task<List<RfqRoutingHistory>> GetExpiredRoutingsAsync(CancellationToken cancellationToken = default);

    Task<List<RfqRoutingHistory>> GetByStatusAsync(RfqRoutingStatus status, CancellationToken cancellationToken = default);

    Task AddAsync(RfqRoutingHistory routingHistory, CancellationToken cancellationToken = default);

    void Update(RfqRoutingHistory routingHistory);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IRfqTagRepository
{
    Task<List<RfqTag>> GetByRfqIdAsync(Guid rfqId, bool activeOnly = true, CancellationToken cancellationToken = default);

    Task<RfqTag?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<RfqTag>> GetByTagTypeAsync(RfqTagType tagType, bool activeOnly = true, CancellationToken cancellationToken = default);

    Task<(List<RFQ> Rfqs, int TotalCount)> GetRfqsByTagsAsync(
        List<RfqTagType> tagTypes,
        int page,
        int pageSize,
        bool activeTagsOnly = true,
        CancellationToken cancellationToken = default);

    Task AddAsync(RfqTag tag, CancellationToken cancellationToken = default);

    Task AddRangeAsync(List<RfqTag> tags, CancellationToken cancellationToken = default);

    void Update(RfqTag tag);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IOrderRepository
{
    Task<Order?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Order?> GetByIdWithTimelineAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Order?> GetByOrderNumberAsync(string orderNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status, CancellationToken cancellationToken = default);
    Task<(IEnumerable<Order> Orders, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? transportCompanyId = null,
        Guid? brokerId = null,
        Guid? carrierId = null,
        OrderStatus? status = null,
        CancellationToken cancellationToken = default);
    Task AddAsync(Order order, CancellationToken cancellationToken = default);
    Task UpdateAsync(Order order, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<Order>> GetOrdersByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<Order>> GetOrdersByTransportCompanyAsync(Guid transportCompanyId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken = default);
    Task<List<Order>> GetAllAsync(CancellationToken cancellationToken = default);
}

public interface IInvoiceRepository
{
    Task<Invoice?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<Invoice>> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync(CancellationToken cancellationToken = default);
    Task<(IEnumerable<Invoice> Invoices, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        InvoiceStatus? status = null,
        CancellationToken cancellationToken = default);
    Task AddAsync(Invoice invoice, CancellationToken cancellationToken = default);
    Task UpdateAsync(Invoice invoice, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IRatingAggregationRepository
{
    Task<decimal> GetAverageRatingAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<int> GetTotalRatingsCountAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<Dictionary<int, int>> GetRatingDistributionAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<decimal> GetRecentAverageRatingAsync(Guid transportCompanyId, DateTime fromDate, CancellationToken cancellationToken = default);
    Task<List<decimal>> GetMonthlyAverageRatingsAsync(Guid transportCompanyId, int months, CancellationToken cancellationToken = default);
    Task UpdateRatingAggregationAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
}

public interface ITripFeedbackRepository
{
    Task<List<TripFeedback>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<TripFeedback>> GetRecentFeedbackAsync(Guid transportCompanyId, DateTime fromDate, CancellationToken cancellationToken = default);
    Task<decimal> GetAverageRatingAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<int> GetFeedbackCountAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<TripFeedback>> GetFeedbackByRatingAsync(Guid transportCompanyId, int rating, CancellationToken cancellationToken = default);
    Task<TimeSpan> GetAverageResponseTimeAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<TripFeedback>> GetFeedbackByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default);
    Task<List<TripFeedback>> GetFeedbackByTransportCompanyAsync(Guid transportCompanyId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken = default);
}

// Supporting class for TripFeedback
public class TripFeedback
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid ReviewerId { get; set; }
    public int Rating { get; set; }
    public string Comments { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ResponseTime { get; set; }

    // Additional properties needed by TransportCompanyRatingService
    public DateTime SubmittedAt { get; set; }
    public decimal OverallRating { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal TimelinessRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal ProfessionalismRating { get; set; }
    public decimal VehicleConditionRating { get; set; }
    public decimal CargoHandlingRating { get; set; }
}

public interface IMilestoneTemplateRepository
{
    Task<MilestoneTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<MilestoneTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default);

    Task<List<MilestoneTemplate>> GetByTypeAsync(MilestoneTemplateType templateType, bool activeOnly = true, CancellationToken cancellationToken = default);

    Task<MilestoneTemplate?> GetDefaultTemplateAsync(MilestoneTemplateType templateType, CancellationToken cancellationToken = default);

    Task<(List<MilestoneTemplate> Templates, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        MilestoneTemplateType? templateType = null,
        bool? isActive = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    Task AddAsync(MilestoneTemplate template, CancellationToken cancellationToken = default);

    void Update(MilestoneTemplate template);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IRfqMilestoneAssignmentRepository
{
    Task<RfqMilestoneAssignment?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<RfqMilestoneAssignment?> GetActiveByRfqIdAsync(Guid rfqId, CancellationToken cancellationToken = default);

    Task<List<RfqMilestoneAssignment>> GetByRfqIdAsync(Guid rfqId, bool activeOnly = true, CancellationToken cancellationToken = default);

    Task<List<RfqMilestoneAssignment>> GetByTemplateIdAsync(Guid templateId, bool activeOnly = true, CancellationToken cancellationToken = default);

    Task AddAsync(RfqMilestoneAssignment assignment, CancellationToken cancellationToken = default);

    void Update(RfqMilestoneAssignment assignment);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IBrokerCommentRepository
{
    Task<BrokerComment?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<BrokerComment>> GetByRfqIdAsync(Guid rfqId, bool visibleOnly = true, bool includeInternal = true, CancellationToken cancellationToken = default);

    Task<List<BrokerComment>> GetByBrokerIdAsync(Guid brokerId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<List<BrokerComment>> GetByCommentTypeAsync(BrokerCommentType commentType, bool visibleOnly = true, CancellationToken cancellationToken = default);

    Task<List<BrokerComment>> GetRepliesAsync(Guid parentCommentId, CancellationToken cancellationToken = default);

    Task AddAsync(BrokerComment comment, CancellationToken cancellationToken = default);

    void Update(BrokerComment comment);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IAdministrativeAuditRepository
{
    Task<AdministrativeAuditLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    Task<List<AdministrativeAuditLog>> GetByAdminUserIdAsync(Guid adminUserId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<List<AdministrativeAuditLog>> GetByActionAsync(AdministrativeAction action, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<List<AdministrativeAuditLog>> GetBySeverityAsync(AuditSeverity severity, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<List<AdministrativeAuditLog>> GetSecurityViolationsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    Task<(List<AdministrativeAuditLog> Logs, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? adminUserId = null,
        AdministrativeAction? action = null,
        AuditSeverity? severity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    Task AddAsync(AdministrativeAuditLog auditLog, CancellationToken cancellationToken = default);

    void Update(AdministrativeAuditLog auditLog);

    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}

public interface IAuthorizationService
{
    Task<bool> HasAdministrativeOverridePermissionAsync(Guid userId, string action, CancellationToken cancellationToken = default);

    Task<bool> HasRoleAsync(Guid userId, string role, CancellationToken cancellationToken = default);

    Task<List<string>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default);

    Task<bool> CanAccessRfqAsync(Guid userId, Guid rfqId, CancellationToken cancellationToken = default);
}
