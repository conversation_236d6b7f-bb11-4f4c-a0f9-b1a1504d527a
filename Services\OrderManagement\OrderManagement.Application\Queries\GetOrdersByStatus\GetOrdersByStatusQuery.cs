using MediatR;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetOrdersByStatus;

public class GetOrdersByStatusQuery : IRequest<PagedResult<OrderSummaryDto>>
{
    public OrderStatus Status { get; set; }
    public Guid? UserId { get; set; } // For filtering by user
    public string? UserRole { get; set; } // "TransportCompany", "Broker", "Carrier"
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
}
