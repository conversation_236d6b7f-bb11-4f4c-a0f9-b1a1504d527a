using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs;

public record TripDto
{
    public Guid Id { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public Guid CarrierId { get; init; }
    public Guid? DriverId { get; init; }
    public Guid? VehicleId { get; init; }
    public TripStatus Status { get; init; }
    public RouteDto Route { get; init; } = null!;
    public DateTime CreatedAt { get; init; }
    public DateTime? AssignedAt { get; init; }
    public DateTime? StartedAt { get; init; }
    public DateTime? CompletedAt { get; init; }
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public string? SpecialInstructions { get; init; }
    public bool IsUrgent { get; init; }
    public decimal? EstimatedDistanceKm { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public string? Notes { get; init; }
    public DriverDto? Driver { get; init; }
    public VehicleDto? Vehicle { get; init; }
    public List<TripStopDto> Stops { get; init; } = new();
    public LocationDto? CurrentLocation { get; init; }
    public bool IsDelayed { get; init; }
    public TimeSpan? ActualDuration { get; init; }
}

public record RouteDto
{
    public LocationDto StartLocation { get; init; } = null!;
    public LocationDto EndLocation { get; init; } = null!;
    public List<LocationDto> Waypoints { get; init; } = new();
    public double? EstimatedDistanceKm { get; init; }
    public TimeSpan? EstimatedDuration { get; init; }
    public string? RouteInstructions { get; init; }
}

public record LocationDto
{
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
    public double? Accuracy { get; init; }
    public DateTime Timestamp { get; init; }
    public string? Address { get; init; }
    public string? City { get; init; }
    public string? State { get; init; }
    public string? Country { get; init; }
    public string? PostalCode { get; init; }
}

public record TripStopDto
{
    public Guid Id { get; init; }
    public TripStopType StopType { get; init; }
    public int SequenceNumber { get; init; }
    public LocationDto Location { get; init; } = null!;
    public string? ContactName { get; init; }
    public string? ContactPhone { get; init; }
    public DateTime? ScheduledArrival { get; init; }
    public DateTime? ScheduledDeparture { get; init; }
    public DateTime? ActualArrival { get; init; }
    public DateTime? ActualDeparture { get; init; }
    public TripStopStatus Status { get; init; }
    public string? Instructions { get; init; }
    public string? Notes { get; init; }
    public bool IsDelayed { get; init; }
    public List<ProofOfDeliveryDto> ProofOfDeliveries { get; init; } = new();
}

public record DriverDto
{
    public Guid Id { get; init; }
    public Guid UserId { get; init; }
    public string FirstName { get; init; } = string.Empty;
    public string LastName { get; init; } = string.Empty;
    public string FullName { get; init; } = string.Empty;
    public string PhoneNumber { get; init; } = string.Empty;
    public string Email { get; init; } = string.Empty;
    public string LicenseNumber { get; init; } = string.Empty;
    public DateTime LicenseExpiryDate { get; init; }
    public DriverStatus Status { get; init; }
    public LocationDto? CurrentLocation { get; init; }
    public string? ProfilePhotoUrl { get; init; }
    public decimal Rating { get; init; }
    public int TotalTrips { get; init; }
    public int CompletedTrips { get; init; }
    public bool IsLicenseValid { get; init; }
    public bool IsAvailable { get; init; }
}

public record VehicleDto
{
    public Guid Id { get; init; }
    public Guid CarrierId { get; init; }
    public string RegistrationNumber { get; init; } = string.Empty;
    public VehicleType VehicleType { get; init; }
    public string Make { get; init; } = string.Empty;
    public string Model { get; init; } = string.Empty;
    public int Year { get; init; }
    public string Color { get; init; } = string.Empty;
    public string DisplayName { get; init; } = string.Empty;
    public decimal LoadCapacityKg { get; init; }
    public decimal VolumeCapacityM3 { get; init; }
    public VehicleStatus Status { get; init; }
    public string? InsuranceNumber { get; init; }
    public DateTime? InsuranceExpiryDate { get; init; }
    public string? FitnessNumber { get; init; }
    public DateTime? FitnessExpiryDate { get; init; }
    public bool IsInsuranceValid { get; init; }
    public bool IsFitnessValid { get; init; }
    public bool IsAvailable { get; init; }
}

public record ProofOfDeliveryDto
{
    public Guid Id { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public string? RecipientSignature { get; init; }
    public string? PhotoUrl { get; init; }
    public DateTime DeliveredAt { get; init; }
    public string? Notes { get; init; }
    public bool IsDigitalSignature { get; init; }
    public string? DeliveredBy { get; init; }
}

public record TripLocationUpdateDto
{
    public Guid Id { get; init; }
    public LocationDto Location { get; init; } = null!;
    public DateTime Timestamp { get; init; }
    public LocationUpdateSource Source { get; init; }
    public double? Speed { get; init; }
    public double? Heading { get; init; }
}

public record TripExceptionDto
{
    public Guid Id { get; init; }
    public ExceptionType ExceptionType { get; init; }
    public string Description { get; init; } = string.Empty;
    public LocationDto? Location { get; init; }
    public DateTime ReportedAt { get; init; }
    public bool IsResolved { get; init; }
    public DateTime? ResolvedAt { get; init; }
    public string? Resolution { get; init; }
    public string? ResolvedBy { get; init; }
}

public record TripDashboardDto
{
    public Guid Id { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public Guid CarrierId { get; init; }
    public Guid? DriverId { get; init; }
    public string? DriverName { get; init; }
    public Guid? VehicleId { get; init; }
    public string? VehicleRegistration { get; init; }
    public string? VehicleDisplayName { get; init; }
    public TripStatus Status { get; init; }
    public string StatusDisplayName { get; init; } = string.Empty;
    public string StatusColor { get; init; } = string.Empty; // Green, Orange, Red, Blue

    // Trip Timeline
    public DateTime CreatedAt { get; init; }
    public DateTime? AssignedAt { get; init; }
    public DateTime? StartedAt { get; init; }
    public DateTime? CompletedAt { get; init; }
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }

    // Performance Metrics
    public bool IsDelayed { get; init; }
    public bool IsOnTime { get; init; }
    public TimeSpan? DelayDuration { get; init; }
    public decimal? EstimatedDistanceKm { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public TimeSpan? EstimatedDuration { get; init; }
    public TimeSpan? ActualDuration { get; init; }

    // Route Information
    public RouteDto Route { get; init; } = null!;
    public LocationDto? CurrentLocation { get; init; }
    public decimal? ProgressPercentage { get; init; }

    // Milestone Tracking
    public List<TripMilestoneDto> Milestones { get; init; } = new();
    public int CompletedMilestones { get; init; }
    public int TotalMilestones { get; init; }
    public decimal MilestoneCompletionRate { get; init; }

    // Stops Information
    public List<TripStopSummaryDto> Stops { get; init; } = new();
    public int CompletedStops { get; init; }
    public int TotalStops { get; init; }

    // POD Information
    public bool HasProofOfDelivery { get; init; }
    public int ProofOfDeliveryCount { get; init; }
    public List<ProofOfDeliverySummaryDto> ProofOfDeliveries { get; init; } = new();

    // Exception Information
    public bool HasExceptions { get; init; }
    public int ActiveExceptions { get; init; }
    public int ResolvedExceptions { get; init; }
    public List<TripExceptionSummaryDto> RecentExceptions { get; init; } = new();

    // Additional Information
    public bool IsUrgent { get; init; }
    public string? SpecialInstructions { get; init; }
    public string? Notes { get; init; }
    public decimal? EstimatedRevenue { get; init; }
    public decimal? ActualRevenue { get; init; }

    // Multi-Leg Information
    public bool IsMultiLegTrip { get; init; }
    public List<TripLegDto> Legs { get; init; } = new();
    public int TotalLegs { get; init; }
    public int CompletedLegs { get; init; }
    public TripLegDto? CurrentLeg { get; init; }
    public TripLegDto? NextLeg { get; init; }
    public decimal OverallCompletionPercentage { get; init; }
    public DateTime? EstimatedCompletionTime { get; init; }
    public decimal? TotalEstimatedDistance { get; init; }
    public decimal? TotalActualDistance { get; init; }
}

public record TripMilestoneDto
{
    public Guid Id { get; init; }
    public string Name { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public int SequenceNumber { get; init; }
    public bool IsCompleted { get; init; }
    public DateTime? CompletedAt { get; init; }
    public DateTime? ScheduledAt { get; init; }
    public string Status { get; init; } = string.Empty; // Pending, InProgress, Completed, Skipped
    public string StatusColor { get; init; } = string.Empty;
    public decimal? PayoutPercentage { get; init; }
    public LocationDto? Location { get; init; }
}

public record TripStopSummaryDto
{
    public Guid Id { get; init; }
    public TripStopType StopType { get; init; }
    public int SequenceNumber { get; init; }
    public string LocationName { get; init; } = string.Empty;
    public TripStopStatus Status { get; init; }
    public string StatusDisplayName { get; init; } = string.Empty;
    public string StatusColor { get; init; } = string.Empty;
    public DateTime? ScheduledArrival { get; init; }
    public DateTime? ActualArrival { get; init; }
    public bool IsDelayed { get; init; }
    public TimeSpan? DelayDuration { get; init; }
    public bool HasProofOfDelivery { get; init; }
    public int ProofOfDeliveryCount { get; init; }
}

public record ProofOfDeliverySummaryDto
{
    public Guid Id { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public DateTime DeliveredAt { get; init; }
    public bool HasSignature { get; init; }
    public bool HasPhoto { get; init; }
    public bool IsDigitalSignature { get; init; }
    public string? DeliveredBy { get; init; }
    public Guid StopId { get; init; }
    public string StopLocationName { get; init; } = string.Empty;
}

public record TripExceptionSummaryDto
{
    public Guid Id { get; init; }
    public ExceptionType ExceptionType { get; init; }
    public string ExceptionTypeName { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public DateTime ReportedAt { get; init; }
    public bool IsResolved { get; init; }
    public string Status { get; init; } = string.Empty; // Active, Resolved
    public string StatusColor { get; init; } = string.Empty;
    public string Priority { get; init; } = string.Empty; // High, Medium, Low
}

// ===== MULTI-LEG TRIP DTOs =====

public record TripLegDto
{
    public Guid Id { get; init; }
    public Guid TripId { get; init; }
    public int LegNumber { get; init; }
    public string LegName { get; init; } = string.Empty;
    public string? Description { get; init; }
    public Guid? DriverId { get; init; }
    public string? DriverName { get; init; }
    public Guid? VehicleId { get; init; }
    public string? VehicleRegistration { get; init; }
    public string? VehicleDisplayName { get; init; }
    public TripLegStatus Status { get; init; }
    public string StatusDisplayName { get; init; } = string.Empty;
    public string StatusColor { get; init; } = string.Empty;
    public RouteDto Route { get; init; } = null!;
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public DateTime? ActualStartTime { get; init; }
    public DateTime? ActualEndTime { get; init; }
    public decimal? EstimatedDistanceKm { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public string? SpecialInstructions { get; init; }
    public bool IsRequired { get; init; }
    public int Priority { get; init; }
    public string? Notes { get; init; }
    public bool IsDelayed { get; init; }
    public TimeSpan? DelayDuration { get; init; }
    public decimal CompletionPercentage { get; init; }
    public TimeSpan EstimatedDuration { get; init; }
    public TimeSpan? ActualDuration { get; init; }
    public LocationDto? CurrentLocation { get; init; }
    public List<TripLegStopDto> Stops { get; init; } = new();
    public List<TripLegExceptionDto> Exceptions { get; init; } = new();
    public bool CanStart { get; init; }
    public bool CanComplete { get; init; }
    public bool IsActive { get; init; }
}

public record TripLegStopDto
{
    public Guid Id { get; init; }
    public Guid TripLegId { get; init; }
    public TripStopType StopType { get; init; }
    public int SequenceNumber { get; init; }
    public LocationDto Location { get; init; } = null!;
    public string? ContactName { get; init; }
    public string? ContactPhone { get; init; }
    public DateTime? ScheduledArrival { get; init; }
    public DateTime? ScheduledDeparture { get; init; }
    public DateTime? ActualArrival { get; init; }
    public DateTime? ActualDeparture { get; init; }
    public TripStopStatus Status { get; init; }
    public string StatusDisplayName { get; init; } = string.Empty;
    public string StatusColor { get; init; } = string.Empty;
    public string? Instructions { get; init; }
    public string? Notes { get; init; }
    public bool IsRequired { get; init; }
    public bool IsDelayed { get; init; }
    public TimeSpan? DelayDuration { get; init; }
    public List<ProofOfDeliveryDto> ProofOfDeliveries { get; init; } = new();
}

public record TripLegExceptionDto
{
    public Guid Id { get; init; }
    public Guid TripLegId { get; init; }
    public ExceptionType Type { get; init; }
    public ExceptionSeverity Severity { get; init; }
    public string Title { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public DateTime OccurredAt { get; init; }
    public LocationDto? Location { get; init; }
    public Guid? ReportedBy { get; init; }
    public string? ReportedByName { get; init; }
    public bool IsResolved { get; init; }
    public DateTime? ResolvedAt { get; init; }
    public string? Resolution { get; init; }
    public Guid? ResolvedBy { get; init; }
    public string? ResolvedByName { get; init; }
    public string Status { get; init; } = string.Empty; // Active, Resolved
    public string StatusColor { get; init; } = string.Empty;
    public string SeverityDisplayName { get; init; } = string.Empty;
    public string SeverityColor { get; init; } = string.Empty;
}

public record TripPlanDto
{
    public Guid TripId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public DateTime GeneratedAt { get; init; }
    public Guid GeneratedBy { get; init; }
    public string GeneratedByName { get; init; } = string.Empty;
    public bool IsMultiLegTrip { get; init; }
    public int TotalLegs { get; init; }
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public TimeSpan TotalEstimatedDuration { get; init; }
    public decimal? TotalEstimatedDistance { get; init; }
    public List<TripLegPlanDto> LegPlans { get; init; } = new();
    public List<ResourceAssignmentDto> ResourceAssignments { get; init; } = new();
    public string? SpecialInstructions { get; init; }
    public string? Notes { get; init; }
    public bool IsPrintable { get; init; } = true;
}

public record TripLegPlanDto
{
    public int LegNumber { get; init; }
    public string LegName { get; init; } = string.Empty;
    public string? Description { get; init; }
    public RouteDto Route { get; init; } = null!;
    public DateTime EstimatedStartTime { get; init; }
    public DateTime EstimatedEndTime { get; init; }
    public TimeSpan EstimatedDuration { get; init; }
    public decimal? EstimatedDistanceKm { get; init; }
    public Guid? AssignedDriverId { get; init; }
    public string? AssignedDriverName { get; init; }
    public Guid? AssignedVehicleId { get; init; }
    public string? AssignedVehicleRegistration { get; init; }
    public bool IsRequired { get; init; }
    public int Priority { get; init; }
    public string? SpecialInstructions { get; init; }
    public List<TripLegStopPlanDto> Stops { get; init; } = new();
}

public record TripLegStopPlanDto
{
    public int SequenceNumber { get; init; }
    public TripStopType StopType { get; init; }
    public string StopTypeName { get; init; } = string.Empty;
    public LocationDto Location { get; init; } = null!;
    public string LocationName { get; init; } = string.Empty;
    public string? ContactName { get; init; }
    public string? ContactPhone { get; init; }
    public DateTime? ScheduledArrival { get; init; }
    public DateTime? ScheduledDeparture { get; init; }
    public string? Instructions { get; init; }
    public bool IsRequired { get; init; }
}

public record ResourceAssignmentDto
{
    public int LegNumber { get; init; }
    public string LegName { get; init; } = string.Empty;
    public Guid? DriverId { get; init; }
    public string? DriverName { get; init; }
    public string? DriverPhone { get; init; }
    public Guid? VehicleId { get; init; }
    public string? VehicleRegistration { get; init; }
    public string? VehicleDisplayName { get; init; }
    public VehicleType? VehicleType { get; init; }
    public bool IsAssigned { get; init; }
    public DateTime? AssignedAt { get; init; }
}

// Enums for TripLeg
public enum TripLegStatus
{
    Planned = 1,
    Assigned = 2,
    InProgress = 3,
    Completed = 4,
    Cancelled = 5,
    Skipped = 6,
    Exception = 7
}
