using Microsoft.EntityFrameworkCore;
using UserManagement.Domain.Entities;

namespace UserManagement.Infrastructure.Data
{
    public class UserManagementDbContext : DbContext
    {
        public UserManagementDbContext(DbContextOptions<UserManagementDbContext> options) : base(options)
        {
        }

        public DbSet<UserProfile> UserProfiles { get; set; }
        public DbSet<DocumentSubmission> DocumentSubmissions { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure UserProfile
            modelBuilder.Entity<UserProfile>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UserId).IsRequired();
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FirstName).HasMaxLength(100);
                entity.Property(e => e.LastName).HasMaxLength(100);
                entity.Property(e => e.PhoneNumber).HasMaxLength(20);
                entity.Property(e => e.CompanyName).HasMaxLength(255);
                entity.Property(e => e.GstNumber).HasMaxLength(15);
                entity.Property(e => e.PanNumber).HasMaxLength(10);
                entity.Property(e => e.AadharNumber).HasMaxLength(12);
                entity.Property(e => e.LicenseNumber).HasMaxLength(20);
                entity.Property(e => e.Address).HasMaxLength(500);
                entity.Property(e => e.City).HasMaxLength(100);
                entity.Property(e => e.State).HasMaxLength(100);
                entity.Property(e => e.Country).HasMaxLength(100);
                entity.Property(e => e.PostalCode).HasMaxLength(10);
                entity.Property(e => e.RejectionReason).HasMaxLength(1000);

                entity.HasIndex(e => e.UserId).IsUnique();
                entity.HasIndex(e => e.Email);
                entity.HasIndex(e => e.UserType);
                entity.HasIndex(e => e.Status);
            });

            // Configure DocumentSubmission
            modelBuilder.Entity<DocumentSubmission>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UserId).IsRequired();
                entity.Property(e => e.ReviewNotes).HasMaxLength(1000);

                entity.HasIndex(e => e.UserId).IsUnique();
                entity.HasIndex(e => e.OverallStatus);
                entity.HasIndex(e => e.UserType);

                // Configure owned collection of Documents
                entity.OwnsMany(e => e.Documents, doc =>
                {
                    doc.WithOwner().HasForeignKey("DocumentSubmissionId");
                    doc.Property(d => d.FileName).HasMaxLength(255);
                    doc.Property(d => d.FilePath).HasMaxLength(500);
                    doc.Property(d => d.FileSize).HasMaxLength(50);
                    doc.Property(d => d.MimeType).HasMaxLength(100);
                    doc.Property(d => d.ExtractedData).HasMaxLength(2000);
                    doc.Property(d => d.ReviewNotes).HasMaxLength(1000);
                    doc.Property(d => d.RejectionReason).HasMaxLength(1000);

                    doc.HasIndex(d => d.DocumentType);
                    doc.HasIndex(d => d.Status);
                });
            });

            // Configure enums as strings
            modelBuilder.Entity<UserProfile>()
                .Property(e => e.UserType)
                .HasConversion<string>();

            modelBuilder.Entity<UserProfile>()
                .Property(e => e.Status)
                .HasConversion<string>();

            modelBuilder.Entity<DocumentSubmission>()
                .Property(e => e.UserType)
                .HasConversion<string>();

            modelBuilder.Entity<DocumentSubmission>()
                .Property(e => e.OverallStatus)
                .HasConversion<string>();
        }
    }
}
