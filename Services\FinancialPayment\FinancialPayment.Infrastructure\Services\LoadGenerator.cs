using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FinancialPayment.Infrastructure.Services;

public class LoadGenerator : ILoadGenerator
{
    private readonly ILogger<LoadGenerator> _logger;
    private readonly PerformanceTestConfiguration _configuration;
    private readonly ConcurrentDictionary<Guid, TestExecution> _activeExecutions;
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _cancellationTokens;

    public LoadGenerator(
        ILogger<LoadGenerator> logger,
        IOptions<PerformanceTestConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _activeExecutions = new ConcurrentDictionary<Guid, TestExecution>();
        _cancellationTokens = new ConcurrentDictionary<Guid, CancellationTokenSource>();
    }

    public async Task<LoadTestExecution> StartLoadTestAsync(LoadTestConfiguration configuration)
    {
        var execution = new LoadTestExecution
        {
            Id = Guid.NewGuid(),
            TestType = "Load Test",
            StartTime = DateTime.UtcNow,
            Status = TestExecutionStatus.Starting,
            Configuration = configuration
        };

        _activeExecutions.TryAdd(execution.Id, execution);
        var cancellationTokenSource = new CancellationTokenSource();
        _cancellationTokens.TryAdd(execution.Id, cancellationTokenSource);

        _logger.LogInformation("Starting load test execution {ExecutionId} with {Users} users", 
            execution.Id, configuration.ConcurrentUsers);

        // Start the load test in background
        _ = Task.Run(async () => await ExecuteLoadTestAsync(execution, cancellationTokenSource.Token));

        return execution;
    }

    public async Task<StressTestExecution> StartStressTestAsync(StressTestConfiguration configuration)
    {
        var execution = new StressTestExecution
        {
            Id = Guid.NewGuid(),
            TestType = "Stress Test",
            StartTime = DateTime.UtcNow,
            Status = TestExecutionStatus.Starting,
            Configuration = configuration
        };

        _activeExecutions.TryAdd(execution.Id, execution);
        var cancellationTokenSource = new CancellationTokenSource();
        _cancellationTokens.TryAdd(execution.Id, cancellationTokenSource);

        _logger.LogInformation("Starting stress test execution {ExecutionId} ramping to {MaxUsers} users", 
            execution.Id, configuration.MaxConcurrentUsers);

        _ = Task.Run(async () => await ExecuteStressTestAsync(execution, cancellationTokenSource.Token));

        return execution;
    }

    public async Task<SpikeTestExecution> StartSpikeTestAsync(SpikeTestConfiguration configuration)
    {
        var execution = new SpikeTestExecution
        {
            Id = Guid.NewGuid(),
            TestType = "Spike Test",
            StartTime = DateTime.UtcNow,
            Status = TestExecutionStatus.Starting,
            Configuration = configuration
        };

        _activeExecutions.TryAdd(execution.Id, execution);
        var cancellationTokenSource = new CancellationTokenSource();
        _cancellationTokens.TryAdd(execution.Id, cancellationTokenSource);

        _logger.LogInformation("Starting spike test execution {ExecutionId} with {SpikeUsers} spike users", 
            execution.Id, configuration.SpikeUsers);

        _ = Task.Run(async () => await ExecuteSpikeTestAsync(execution, cancellationTokenSource.Token));

        return execution;
    }

    public async Task<EnduranceTestExecution> StartEnduranceTestAsync(EnduranceTestConfiguration configuration)
    {
        var execution = new EnduranceTestExecution
        {
            Id = Guid.NewGuid(),
            TestType = "Endurance Test",
            StartTime = DateTime.UtcNow,
            Status = TestExecutionStatus.Starting,
            Configuration = configuration
        };

        _activeExecutions.TryAdd(execution.Id, execution);
        var cancellationTokenSource = new CancellationTokenSource();
        _cancellationTokens.TryAdd(execution.Id, cancellationTokenSource);

        _logger.LogInformation("Starting endurance test execution {ExecutionId} for {Duration}", 
            execution.Id, configuration.EnduranceDuration);

        _ = Task.Run(async () => await ExecuteEnduranceTestAsync(execution, cancellationTokenSource.Token));

        return execution;
    }

    public async Task StopTestAsync(Guid executionId)
    {
        try
        {
            if (_cancellationTokens.TryGetValue(executionId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                _logger.LogInformation("Stopping test execution {ExecutionId}", executionId);
            }

            if (_activeExecutions.TryGetValue(executionId, out var execution))
            {
                execution.Status = TestExecutionStatus.Stopping;
                execution.EndTime = DateTime.UtcNow;
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping test execution {ExecutionId}", executionId);
        }
    }

    public async Task<TestExecutionStatus> GetExecutionStatusAsync(Guid executionId)
    {
        try
        {
            if (_activeExecutions.TryGetValue(executionId, out var execution))
            {
                return execution.Status;
            }
            return TestExecutionStatus.NotStarted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution status for {ExecutionId}", executionId);
            return TestExecutionStatus.Failed;
        }
    }

    private async Task ExecuteLoadTestAsync(LoadTestExecution execution, CancellationToken cancellationToken)
    {
        try
        {
            execution.Status = TestExecutionStatus.Running;
            var configuration = execution.Configuration;
            var tasks = new List<Task>();

            // Ramp up users gradually
            var usersPerStep = Math.Max(1, configuration.ConcurrentUsers / 10);
            var rampUpStepDuration = configuration.RampUpTime.TotalMilliseconds / 10;

            for (int step = 0; step < 10 && !cancellationToken.IsCancellationRequested; step++)
            {
                var usersInThisStep = Math.Min(usersPerStep, configuration.ConcurrentUsers - (step * usersPerStep));
                
                for (int i = 0; i < usersInThisStep; i++)
                {
                    var userId = (step * usersPerStep) + i + 1;
                    tasks.Add(SimulateUserAsync(execution.Id, userId, configuration.Duration, cancellationToken));
                }

                execution.CurrentUsers = (step + 1) * usersPerStep;
                
                if (step < 9) // Don't delay after the last step
                {
                    await Task.Delay(TimeSpan.FromMilliseconds(rampUpStepDuration), cancellationToken);
                }
            }

            // Wait for all user simulations to complete
            await Task.WhenAll(tasks);

            execution.Status = TestExecutionStatus.Completed;
            execution.EndTime = DateTime.UtcNow;

            _logger.LogInformation("Load test execution {ExecutionId} completed", execution.Id);
        }
        catch (OperationCanceledException)
        {
            execution.Status = TestExecutionStatus.Cancelled;
            execution.EndTime = DateTime.UtcNow;
            _logger.LogInformation("Load test execution {ExecutionId} cancelled", execution.Id);
        }
        catch (Exception ex)
        {
            execution.Status = TestExecutionStatus.Failed;
            execution.EndTime = DateTime.UtcNow;
            execution.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Load test execution {ExecutionId} failed", execution.Id);
        }
        finally
        {
            _cancellationTokens.TryRemove(execution.Id, out _);
        }
    }

    private async Task ExecuteStressTestAsync(StressTestExecution execution, CancellationToken cancellationToken)
    {
        try
        {
            execution.Status = TestExecutionStatus.Running;
            var configuration = execution.Configuration;
            var tasks = new List<Task>();

            // Start with base load
            for (int i = 0; i < configuration.ConcurrentUsers; i++)
            {
                tasks.Add(SimulateUserAsync(execution.Id, i + 1, configuration.StressDuration, cancellationToken));
            }

            execution.CurrentUsers = configuration.ConcurrentUsers;

            // Gradually increase load
            var currentUsers = configuration.ConcurrentUsers;
            var incrementInterval = configuration.IncrementInterval;

            while (currentUsers < configuration.MaxConcurrentUsers && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(incrementInterval, cancellationToken);

                var newUsers = Math.Min(configuration.UserIncrementStep, 
                    configuration.MaxConcurrentUsers - currentUsers);

                for (int i = 0; i < newUsers; i++)
                {
                    tasks.Add(SimulateUserAsync(execution.Id, currentUsers + i + 1, 
                        configuration.StressDuration, cancellationToken));
                }

                currentUsers += newUsers;
                execution.CurrentUsers = currentUsers;

                _logger.LogDebug("Stress test {ExecutionId} ramped up to {CurrentUsers} users", 
                    execution.Id, currentUsers);
            }

            await Task.WhenAll(tasks);

            execution.Status = TestExecutionStatus.Completed;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (OperationCanceledException)
        {
            execution.Status = TestExecutionStatus.Cancelled;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            execution.Status = TestExecutionStatus.Failed;
            execution.EndTime = DateTime.UtcNow;
            execution.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Stress test execution {ExecutionId} failed", execution.Id);
        }
        finally
        {
            _cancellationTokens.TryRemove(execution.Id, out _);
        }
    }

    private async Task ExecuteSpikeTestAsync(SpikeTestExecution execution, CancellationToken cancellationToken)
    {
        try
        {
            execution.Status = TestExecutionStatus.Running;
            var configuration = execution.Configuration;
            var tasks = new List<Task>();

            // Start with base load
            for (int i = 0; i < configuration.ConcurrentUsers; i++)
            {
                tasks.Add(SimulateUserAsync(execution.Id, i + 1, configuration.Duration, cancellationToken));
            }

            execution.CurrentUsers = configuration.ConcurrentUsers;

            // Execute spikes
            for (int spike = 0; spike < configuration.NumberOfSpikes && !cancellationToken.IsCancellationRequested; spike++)
            {
                if (spike > 0)
                {
                    await Task.Delay(configuration.TimeBetweenSpikes, cancellationToken);
                }

                // Add spike users
                var spikeUserTasks = new List<Task>();
                for (int i = 0; i < configuration.SpikeUsers; i++)
                {
                    var userId = configuration.ConcurrentUsers + (spike * configuration.SpikeUsers) + i + 1;
                    spikeUserTasks.Add(SimulateUserAsync(execution.Id, userId, 
                        configuration.SpikeDuration, cancellationToken));
                }

                execution.CurrentUsers = configuration.ConcurrentUsers + configuration.SpikeUsers;

                _logger.LogDebug("Spike test {ExecutionId} executing spike {SpikeNumber} with {SpikeUsers} users", 
                    execution.Id, spike + 1, configuration.SpikeUsers);

                await Task.WhenAll(spikeUserTasks);
                execution.CurrentUsers = configuration.ConcurrentUsers;
            }

            await Task.WhenAll(tasks);

            execution.Status = TestExecutionStatus.Completed;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (OperationCanceledException)
        {
            execution.Status = TestExecutionStatus.Cancelled;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            execution.Status = TestExecutionStatus.Failed;
            execution.EndTime = DateTime.UtcNow;
            execution.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Spike test execution {ExecutionId} failed", execution.Id);
        }
        finally
        {
            _cancellationTokens.TryRemove(execution.Id, out _);
        }
    }

    private async Task ExecuteEnduranceTestAsync(EnduranceTestExecution execution, CancellationToken cancellationToken)
    {
        try
        {
            execution.Status = TestExecutionStatus.Running;
            var configuration = execution.Configuration;
            var tasks = new List<Task>();

            // Start users for the entire duration
            for (int i = 0; i < configuration.ConcurrentUsers; i++)
            {
                tasks.Add(SimulateUserAsync(execution.Id, i + 1, configuration.EnduranceDuration, cancellationToken));
            }

            execution.CurrentUsers = configuration.ConcurrentUsers;

            await Task.WhenAll(tasks);

            execution.Status = TestExecutionStatus.Completed;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (OperationCanceledException)
        {
            execution.Status = TestExecutionStatus.Cancelled;
            execution.EndTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            execution.Status = TestExecutionStatus.Failed;
            execution.EndTime = DateTime.UtcNow;
            execution.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Endurance test execution {ExecutionId} failed", execution.Id);
        }
        finally
        {
            _cancellationTokens.TryRemove(execution.Id, out _);
        }
    }

    private async Task SimulateUserAsync(Guid executionId, int userId, TimeSpan duration, CancellationToken cancellationToken)
    {
        var endTime = DateTime.UtcNow.Add(duration);
        var random = new Random(userId); // Seed with userId for reproducible behavior

        try
        {
            while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
            {
                // Simulate API calls
                await SimulateApiCallAsync(executionId, userId, "payment", cancellationToken);
                
                // Random delay between requests (1-5 seconds)
                var delay = TimeSpan.FromMilliseconds(random.Next(1000, 5000));
                await Task.Delay(delay, cancellationToken);

                // Occasionally simulate subscription operations
                if (random.NextDouble() < 0.3) // 30% chance
                {
                    await SimulateApiCallAsync(executionId, userId, "subscription", cancellationToken);
                    await Task.Delay(TimeSpan.FromMilliseconds(random.Next(500, 2000)), cancellationToken);
                }

                // Occasionally simulate analytics queries
                if (random.NextDouble() < 0.1) // 10% chance
                {
                    await SimulateApiCallAsync(executionId, userId, "analytics", cancellationToken);
                    await Task.Delay(TimeSpan.FromMilliseconds(random.Next(200, 1000)), cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when test is cancelled
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "User simulation {UserId} in execution {ExecutionId} encountered an error", 
                userId, executionId);
        }
    }

    private async Task SimulateApiCallAsync(Guid executionId, int userId, string apiType, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Simulate different response times based on API type
            var responseTime = apiType switch
            {
                "payment" => TimeSpan.FromMilliseconds(Random.Shared.Next(100, 800)),
                "subscription" => TimeSpan.FromMilliseconds(Random.Shared.Next(50, 400)),
                "analytics" => TimeSpan.FromMilliseconds(Random.Shared.Next(200, 1500)),
                _ => TimeSpan.FromMilliseconds(Random.Shared.Next(100, 500))
            };

            await Task.Delay(responseTime, cancellationToken);

            // Simulate occasional failures (5% failure rate)
            if (Random.Shared.NextDouble() < 0.05)
            {
                throw new InvalidOperationException($"Simulated {apiType} API failure");
            }

            stopwatch.Stop();

            // Update execution metrics (in a real implementation, this would be more sophisticated)
            if (_activeExecutions.TryGetValue(executionId, out var execution))
            {
                execution.CurrentMetrics.TotalRequests++;
                execution.CurrentMetrics.SuccessfulRequests++;
                
                // Update average response time (simplified calculation)
                var currentAvg = execution.CurrentMetrics.AverageResponseTime.TotalMilliseconds;
                var newAvg = (currentAvg * (execution.CurrentMetrics.SuccessfulRequests - 1) + stopwatch.Elapsed.TotalMilliseconds) 
                           / execution.CurrentMetrics.SuccessfulRequests;
                execution.CurrentMetrics.AverageResponseTime = TimeSpan.FromMilliseconds(newAvg);
            }
        }
        catch (OperationCanceledException)
        {
            throw; // Re-throw cancellation
        }
        catch (Exception)
        {
            stopwatch.Stop();

            // Update failure metrics
            if (_activeExecutions.TryGetValue(executionId, out var execution))
            {
                execution.CurrentMetrics.TotalRequests++;
                execution.CurrentMetrics.FailedRequests++;
            }
        }
    }
}
