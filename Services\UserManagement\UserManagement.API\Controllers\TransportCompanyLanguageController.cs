using UserManagement.Application.Commands.TransportCompanyLanguage;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace UserManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company language preference management
/// </summary>
[ApiController]
[Route("api/transport-company/language")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyLanguageController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyLanguageController> _logger;

    public TransportCompanyLanguageController(
        IMediator mediator,
        ILogger<TransportCompanyLanguageController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get language preferences for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}")]
    [ProducesResponseType(typeof(TransportCompanyLanguagePreferencesDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<TransportCompanyLanguagePreferencesDto>> GetLanguagePreferences(
        Guid transportCompanyId,
        [FromQuery] bool includeAvailableLanguages = true,
        [FromQuery] bool includeUsageStatistics = false,
        [FromQuery] bool includeTranslationStatus = false)
    {
        try
        {
            if (!await CanManageLanguagePreferences(transportCompanyId))
            {
                return Forbid("You don't have permission to view language preferences for this transport company");
            }

            _logger.LogInformation("Getting language preferences for Transport Company {TransportCompanyId}", transportCompanyId);

            var query = new GetTransportCompanyLanguagePreferencesQuery
            {
                TransportCompanyId = transportCompanyId,
                IncludeAvailableLanguages = includeAvailableLanguages,
                IncludeUsageStatistics = includeUsageStatistics,
                IncludeTranslationStatus = includeTranslationStatus
            };

            var preferences = await _mediator.Send(query);

            if (preferences == null)
            {
                return NotFound("Language preferences not found for this transport company");
            }

            return Ok(preferences);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting language preferences for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update language preferences for a transport company
    /// </summary>
    [HttpPut("{transportCompanyId}")]
    [ProducesResponseType(typeof(UpdateTransportCompanyLanguagePreferencesResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateTransportCompanyLanguagePreferencesResult>> UpdateLanguagePreferences(
        Guid transportCompanyId,
        [FromBody] UpdateLanguagePreferencesRequest request)
    {
        try
        {
            if (!await CanManageLanguagePreferences(transportCompanyId))
            {
                return Forbid("You don't have permission to update language preferences for this transport company");
            }

            _logger.LogInformation("Updating language preferences for Transport Company {TransportCompanyId}. Primary language: {PrimaryLanguage}",
                transportCompanyId, request.Preferences.PrimaryLanguageCode);

            var command = new UpdateTransportCompanyLanguagePreferencesCommand
            {
                TransportCompanyId = transportCompanyId,
                UserId = GetCurrentUserId(),
                Preferences = request.Preferences,
                ApplyToAllUsers = request.ApplyToAllUsers,
                ChangeReason = request.ChangeReason,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Language preferences updated successfully for Transport Company {TransportCompanyId}. Primary language: {PrimaryLanguage}",
                    transportCompanyId, request.Preferences.PrimaryLanguageCode);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update language preferences for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating language preferences for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Detect user language based on browser and location data
    /// </summary>
    [HttpPost("{transportCompanyId}/detect-language")]
    [ProducesResponseType(typeof(DetectUserLanguageResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [AllowAnonymous] // Allow anonymous access for language detection
    public async Task<ActionResult<DetectUserLanguageResult>> DetectUserLanguage(
        Guid transportCompanyId,
        [FromBody] DetectLanguageRequest request)
    {
        try
        {
            _logger.LogInformation("Detecting language for user in Transport Company {TransportCompanyId}", transportCompanyId);

            var command = new DetectUserLanguageCommand
            {
                UserId = request.UserId ?? GetCurrentUserId(),
                TransportCompanyId = transportCompanyId,
                IPAddress = GetClientIPAddress(),
                UserAgent = Request.Headers["User-Agent"].ToString(),
                AcceptLanguageHeader = Request.Headers["Accept-Language"].ToString(),
                TimeZone = request.TimeZone,
                BrowserInfo = request.BrowserInfo
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Language detected successfully for Transport Company {TransportCompanyId}. Detected: {Language}",
                    transportCompanyId, result.DetectedLanguage.LanguageCode);
                return Ok(result);
            }

            _logger.LogWarning("Failed to detect language for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting language for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Apply language to a specific user
    /// </summary>
    [HttpPost("{transportCompanyId}/apply-language")]
    [ProducesResponseType(typeof(ApplyLanguageToUserResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ApplyLanguageToUserResult>> ApplyLanguageToUser(
        Guid transportCompanyId,
        [FromBody] ApplyLanguageRequest request)
    {
        try
        {
            if (!await CanManageLanguagePreferences(transportCompanyId))
            {
                return Forbid("You don't have permission to apply language settings for this transport company");
            }

            _logger.LogInformation("Applying language {LanguageCode} to user {UserId} in Transport Company {TransportCompanyId}",
                request.LanguageCode, request.UserId, transportCompanyId);

            var command = new ApplyLanguageToUserCommand
            {
                UserId = request.UserId,
                TransportCompanyId = transportCompanyId,
                LanguageCode = request.LanguageCode,
                CountryCode = request.CountryCode,
                PersistChoice = request.PersistChoice,
                NotifyUser = request.NotifyUser,
                ChangeReason = request.ChangeReason,
                AppliedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Language applied successfully to user {UserId} in Transport Company {TransportCompanyId}. Language: {LanguageCode}",
                    request.UserId, transportCompanyId, request.LanguageCode);
                return Ok(result);
            }

            _logger.LogWarning("Failed to apply language to user {UserId} in Transport Company {TransportCompanyId}: {Error}",
                request.UserId, transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying language to user {UserId} in Transport Company {TransportCompanyId}", 
                request.UserId, transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Bulk update user languages
    /// </summary>
    [HttpPost("{transportCompanyId}/bulk-update")]
    [ProducesResponseType(typeof(BulkUpdateUserLanguagesResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult<BulkUpdateUserLanguagesResult>> BulkUpdateUserLanguages(
        Guid transportCompanyId,
        [FromBody] BulkUpdateLanguagesRequest request)
    {
        try
        {
            if (!await CanManageLanguagePreferences(transportCompanyId))
            {
                return Forbid("You don't have permission to bulk update languages for this transport company");
            }

            _logger.LogInformation("Bulk updating languages for {UserCount} users in Transport Company {TransportCompanyId}",
                request.UserIds.Count, transportCompanyId);

            var command = new BulkUpdateUserLanguagesCommand
            {
                TransportCompanyId = transportCompanyId,
                UserIds = request.UserIds,
                LanguageCode = request.LanguageCode,
                CountryCode = request.CountryCode,
                NotifyUsers = request.NotifyUsers,
                ChangeReason = request.ChangeReason,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Bulk language update completed for Transport Company {TransportCompanyId}. Success: {Success}, Failed: {Failed}",
                    transportCompanyId, result.SuccessfulUpdates, result.FailedUpdates);
                return Ok(result);
            }

            _logger.LogWarning("Bulk language update failed for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating languages for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available languages
    /// </summary>
    [HttpGet("available-languages")]
    [ProducesResponseType(typeof(List<AvailableLanguageDto>), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<ActionResult<List<AvailableLanguageDto>>> GetAvailableLanguages(
        [FromQuery] Guid? transportCompanyId = null,
        [FromQuery] bool includeTranslationStatus = false,
        [FromQuery] bool includeUsageStatistics = false,
        [FromQuery] bool onlyEnabled = true)
    {
        try
        {
            _logger.LogInformation("Getting available languages. TransportCompanyId: {TransportCompanyId}", transportCompanyId);

            var query = new GetAvailableLanguagesQuery
            {
                TransportCompanyId = transportCompanyId,
                IncludeTranslationStatus = includeTranslationStatus,
                IncludeUsageStatistics = includeUsageStatistics,
                OnlyEnabled = onlyEnabled
            };

            var languages = await _mediator.Send(query);
            return Ok(languages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available languages");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get language guidelines and best practices
    /// </summary>
    [HttpGet("guidelines")]
    [ProducesResponseType(typeof(LanguageGuidelinesDto), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<ActionResult<LanguageGuidelinesDto>> GetLanguageGuidelines()
    {
        try
        {
            var guidelines = new LanguageGuidelinesDto
            {
                Overview = "Language preferences allow you to customize the platform experience for your transport company and users.",
                SupportedFeatures = new List<string>
                {
                    "Multi-language user interface",
                    "Automatic language detection",
                    "Real-time translation",
                    "Localized notifications and reports",
                    "Regional formatting (dates, numbers, currency)",
                    "Right-to-left language support"
                },
                BestPractices = new List<string>
                {
                    "Set the primary language based on your main business location",
                    "Enable secondary languages for diverse user bases",
                    "Use location-based detection for better user experience",
                    "Test translations before enabling auto-translation",
                    "Consider regional formatting preferences",
                    "Provide language switching options for users"
                },
                TechnicalConsiderations = new List<string>
                {
                    "Language changes may require page refresh",
                    "Translation quality varies by language pair",
                    "Some features may have limited translation coverage",
                    "Regional formatting affects data display",
                    "Right-to-left languages require layout adjustments"
                },
                SupportedLanguages = new List<string>
                {
                    "English (en-US, en-GB, en-AU)",
                    "Hindi (hi-IN)",
                    "Spanish (es-ES, es-MX)",
                    "French (fr-FR, fr-CA)",
                    "German (de-DE)",
                    "Portuguese (pt-BR, pt-PT)",
                    "Chinese (zh-CN, zh-TW)",
                    "Japanese (ja-JP)",
                    "Korean (ko-KR)",
                    "Arabic (ar-SA)"
                }
            };

            return Ok(guidelines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting language guidelines");
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageLanguagePreferences(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }

    private string GetClientIPAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

/// <summary>
/// Request DTO for updating language preferences
/// </summary>
public class UpdateLanguagePreferencesRequest
{
    public TransportCompanyLanguagePreferences Preferences { get; set; } = new();
    public bool ApplyToAllUsers { get; set; } = false;
    public string? ChangeReason { get; set; }
}

/// <summary>
/// Request DTO for language detection
/// </summary>
public class DetectLanguageRequest
{
    public Guid? UserId { get; set; }
    public string? TimeZone { get; set; }
    public Dictionary<string, object> BrowserInfo { get; set; } = new();
}

/// <summary>
/// Request DTO for applying language
/// </summary>
public class ApplyLanguageRequest
{
    public Guid UserId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public bool PersistChoice { get; set; } = true;
    public bool NotifyUser { get; set; } = false;
    public string? ChangeReason { get; set; }
}

/// <summary>
/// Request DTO for bulk language update
/// </summary>
public class BulkUpdateLanguagesRequest
{
    public List<Guid> UserIds { get; set; } = new();
    public string LanguageCode { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public bool NotifyUsers { get; set; } = false;
    public string? ChangeReason { get; set; }
}

/// <summary>
/// DTO for language guidelines
/// </summary>
public class LanguageGuidelinesDto
{
    public string Overview { get; set; } = string.Empty;
    public List<string> SupportedFeatures { get; set; } = new();
    public List<string> BestPractices { get; set; } = new();
    public List<string> TechnicalConsiderations { get; set; } = new();
    public List<string> SupportedLanguages { get; set; } = new();
}
