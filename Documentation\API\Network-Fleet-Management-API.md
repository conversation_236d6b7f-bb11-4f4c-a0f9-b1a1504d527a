# 🚛 Network & Fleet Management Service API Documentation

## 📋 Overview

The Network & Fleet Management Service handles carrier networks, vehicle fleet management, driver management, and broker-carrier relationships. This service is 95% complete and production-ready.

**Base URL**: `/api/v1/fleet` (via API Gateway)  
**Direct URL**: `http://localhost:5006`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation        | Permission               | Roles                   |
| ---------------- | ------------------------ | ----------------------- |
| View carriers    | `fleet.carriers.read`    | All authenticated users |
| Manage carriers  | `fleet.carriers.manage`  | Admin, Broker           |
| View vehicles    | `fleet.vehicles.read`    | Carrier, Admin          |
| Manage vehicles  | `fleet.vehicles.manage`  | Carrier, Admin          |
| View drivers     | `fleet.drivers.read`     | Carrier, Admin          |
| Manage drivers   | `fleet.drivers.manage`   | Carrier, Admin          |
| Manage networks  | `fleet.networks.manage`  | Admin, Broker           |
| View performance | `fleet.performance.read` | Ad<PERSON>, Broker, Carrier  |
| Admin operations | `fleet.admin`            | Admin only              |

## 📊 Core Endpoints

### 1. Carrier Management

#### Get Carriers

```http
GET /api/v1/fleet/carriers
Authorization: Bearer <token>
```

**Query Parameters:**

- `pageNumber` (default: 1): Page number
- `pageSize` (default: 10): Items per page
- `status`: Filter by carrier status
- `onboardingStatus`: Filter by onboarding status
- `searchTerm`: Search in company name, contact person

**Response:**

```json
{
  "data": [
    {
      "id": "carrier-uuid",
      "userId": "user-uuid",
      "companyName": "ABC Transport Ltd",
      "contactPersonName": "John Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+91-**********",
      "businessLicenseNumber": "BL123456789",
      "taxIdentificationNumber": "TIN987654321",
      "status": "Active|Suspended|Inactive|UnderReview",
      "onboardingStatus": "Pending|DocumentsSubmitted|UnderReview|Approved|Rejected",
      "performanceMetrics": {
        "totalTrips": 150,
        "completedTrips": 145,
        "onTimeDeliveryRate": 96.7,
        "averageRating": 4.5,
        "totalRevenue": 250000.0,
        "lastTripDate": "2024-01-15T10:30:00Z"
      },
      "businessAddress": {
        "street": "123 Industrial Area",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postalCode": "400001",
        "country": "India",
        "coordinates": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      },
      "verifiedAt": "2024-01-01T00:00:00Z",
      "isActive": true,
      "createdAt": "2023-12-01T00:00:00Z",
      "updatedAt": "2024-01-15T00:00:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 10,
    "totalItems": 25,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

#### Get Carrier Details

```http
GET /api/v1/fleet/carriers/{carrierId}
Authorization: Bearer <token>
```

#### Create Carrier

```http
POST /api/v1/fleet/carriers
Authorization: Bearer <token>
Content-Type: application/json

{
  "userId": "user-uuid",
  "companyName": "ABC Transport Ltd",
  "contactPersonName": "John Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+91-**********",
  "businessLicenseNumber": "BL123456789",
  "taxIdentificationNumber": "TIN987654321",
  "businessAddress": {
    "street": "123 Industrial Area",
    "city": "Mumbai",
    "state": "Maharashtra",
    "postalCode": "400001",
    "country": "India",
    "coordinates": {
      "latitude": 19.0760,
      "longitude": 72.8777
    }
  },
  "notes": "Specialized in electronics transportation"
}
```

#### Update Carrier Status

```http
PUT /api/v1/fleet/carriers/{carrierId}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "Active",
  "reason": "KYC verification completed",
  "notes": "All documents verified and approved"
}
```

### 2. Vehicle Management

#### Get Vehicles

```http
GET /api/v1/fleet/vehicles
Authorization: Bearer <token>
```

**Query Parameters:**

- `carrierId`: Filter by carrier
- `status`: Filter by vehicle status
- `vehicleType`: Filter by vehicle type
- `isAvailable`: Filter available vehicles
- `pageNumber`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "vehicle-uuid",
      "carrierId": "carrier-uuid",
      "vehicleNumber": "MH-01-AB-1234",
      "vehicleType": "Truck|Trailer|Container|Van|Bike",
      "make": "Tata",
      "model": "LPT 1618",
      "year": 2022,
      "status": "Available|InUse|Maintenance|OutOfService",
      "specifications": {
        "maxWeight": 16000,
        "maxVolume": 45.0,
        "maxLength": 20.0,
        "maxWidth": 2.5,
        "maxHeight": 3.5,
        "fuelType": "Diesel",
        "engineCapacity": 5900,
        "mileage": 8.5,
        "features": ["GPS Tracking", "Temperature Control", "Security System"]
      },
      "currentLocation": {
        "latitude": 19.076,
        "longitude": 72.8777,
        "address": "Mumbai, Maharashtra",
        "timestamp": "2024-01-15T14:30:00Z"
      },
      "assignedDriverId": "driver-uuid",
      "isAvailable": true,
      "lastMaintenanceDate": "2024-01-01T00:00:00Z",
      "nextMaintenanceDue": "2024-04-01T00:00:00Z",
      "totalEarnings": 125000.0,
      "totalTrips": 75,
      "createdAt": "2023-12-01T00:00:00Z"
    }
  ]
}
```

#### Register Vehicle

```http
POST /api/v1/fleet/vehicles
Authorization: Bearer <token>
Content-Type: application/json

{
  "carrierId": "carrier-uuid",
  "vehicleNumber": "MH-01-AB-1234",
  "vehicleType": "Truck",
  "make": "Tata",
  "model": "LPT 1618",
  "year": 2022,
  "specifications": {
    "maxWeight": 16000,
    "maxVolume": 45.0,
    "maxLength": 20.0,
    "maxWidth": 2.5,
    "maxHeight": 3.5,
    "fuelType": "Diesel",
    "engineCapacity": 5900,
    "mileage": 8.5,
    "features": ["GPS Tracking", "Temperature Control"]
  },
  "registrationDetails": {
    "registrationNumber": "MH01AB1234",
    "registrationDate": "2022-01-15T00:00:00Z",
    "expiryDate": "2037-01-15T00:00:00Z",
    "state": "Maharashtra"
  },
  "insuranceDetails": {
    "policyNumber": "INS123456789",
    "provider": "ICICI Lombard",
    "expiryDate": "2025-01-15T00:00:00Z",
    "coverageAmount": 1000000.0
  }
}
```

#### Update Vehicle Location

```http
PUT /api/v1/fleet/vehicles/{vehicleId}/location
Authorization: Bearer <token>
Content-Type: application/json

{
  "latitude": 19.0760,
  "longitude": 72.8777,
  "accuracy": 5.0,
  "speed": 65.5,
  "heading": 45.0,
  "timestamp": "2024-01-15T14:30:00Z",
  "address": "Highway NH-4, Near Mumbai"
}
```

#### Schedule Maintenance

```http
POST /api/v1/fleet/vehicles/{vehicleId}/maintenance
Authorization: Bearer <token>
Content-Type: application/json

{
  "maintenanceType": "Scheduled|Breakdown|Inspection",
  "description": "Regular service and oil change",
  "scheduledDate": "2024-02-01T09:00:00Z",
  "estimatedCost": 5000.0,
  "serviceProvider": "Tata Motors Service Center",
  "notes": "Include brake inspection"
}
```

### 3. Driver Management

#### Get Drivers

```http
GET /api/v1/fleet/drivers
Authorization: Bearer <token>
```

**Query Parameters:**

- `carrierId`: Filter by carrier
- `status`: Filter by driver status
- `isAvailable`: Filter available drivers
- `licenseType`: Filter by license type
- `pageNumber`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "driver-uuid",
      "carrierId": "carrier-uuid",
      "employeeId": "EMP001",
      "personalDetails": {
        "firstName": "Rajesh",
        "lastName": "Kumar",
        "dateOfBirth": "1985-05-15T00:00:00Z",
        "phoneNumber": "+91-**********",
        "alternatePhoneNumber": "+91-**********",
        "email": "<EMAIL>",
        "bloodGroup": "B+",
        "profilePhotoUrl": "https://storage.tli.com/drivers/photo-uuid.jpg"
      },
      "licenseDetails": {
        "licenseNumber": "MH1234567890",
        "licenseType": "Heavy Vehicle",
        "issueDate": "2020-01-15T00:00:00Z",
        "expiryDate": "2030-01-15T00:00:00Z",
        "issuingAuthority": "RTO Mumbai",
        "endorsements": ["Hazardous Materials", "Passenger Transport"]
      },
      "status": "Available|OnTrip|OffDuty|OnLeave|Suspended",
      "onboardingStatus": "Pending|DocumentsSubmitted|UnderReview|Approved|Rejected",
      "performanceMetrics": {
        "totalTrips": 120,
        "completedTrips": 118,
        "onTimeDeliveries": 115,
        "averageRating": 4.6,
        "safetyScore": 95.5,
        "fuelEfficiency": 8.2
      },
      "currentLocation": {
        "latitude": 19.076,
        "longitude": 72.8777,
        "timestamp": "2024-01-15T14:30:00Z"
      },
      "assignedVehicleId": "vehicle-uuid",
      "isAvailable": true,
      "createdAt": "2023-12-01T00:00:00Z"
    }
  ]
}
```

#### Register Driver

```http
POST /api/v1/fleet/drivers
Authorization: Bearer <token>
Content-Type: application/json

{
  "carrierId": "carrier-uuid",
  "employeeId": "EMP001",
  "personalDetails": {
    "firstName": "Rajesh",
    "lastName": "Kumar",
    "dateOfBirth": "1985-05-15T00:00:00Z",
    "phoneNumber": "+91-**********",
    "email": "<EMAIL>",
    "bloodGroup": "B+",
    "address": {
      "street": "456 Driver Colony",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postalCode": "400002",
      "country": "India"
    },
    "emergencyContact": {
      "name": "Sunita Kumar",
      "relationship": "Wife",
      "phoneNumber": "+91-**********"
    }
  },
  "licenseDetails": {
    "licenseNumber": "MH1234567890",
    "licenseType": "Heavy Vehicle",
    "issueDate": "2020-01-15T00:00:00Z",
    "expiryDate": "2030-01-15T00:00:00Z",
    "issuingAuthority": "RTO Mumbai"
  },
  "identificationDetails": {
    "aadhaarNumber": "1234-5678-9012",
    "panNumber": "**********"
  }
}
```

#### Assign Driver to Vehicle

```http
POST /api/v1/fleet/drivers/{driverId}/assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "vehicleId": "vehicle-uuid",
  "assignmentDate": "2024-01-15T00:00:00Z",
  "notes": "Primary driver assignment for Route A"
}
```

### 4. Network Management

#### Get Network Relationships

```http
GET /api/v1/fleet/networks
Authorization: Bearer <token>
```

**Query Parameters:**

- `brokerId`: Filter by broker
- `carrierId`: Filter by carrier
- `status`: Filter by relationship status
- `pageNumber`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "network-uuid",
      "brokerId": "broker-uuid",
      "carrierId": "carrier-uuid",
      "status": "Pending|Active|Suspended|Terminated",
      "establishedAt": "2024-01-01T00:00:00Z",
      "activatedAt": "2024-01-02T00:00:00Z",
      "performanceMetrics": {
        "totalOrders": 50,
        "completedOrders": 48,
        "onTimeDeliveryRate": 96.0,
        "averageRating": 4.5,
        "totalRevenue": 125000.0
      },
      "preferredRate": 15.5,
      "priority": 1,
      "notes": "Preferred carrier for electronics shipments"
    }
  ]
}
```

#### Create Network Relationship

```http
POST /api/v1/fleet/networks
Authorization: Bearer <token>
Content-Type: application/json

{
  "brokerId": "broker-uuid",
  "carrierId": "carrier-uuid",
  "preferredRate": 15.50,
  "priority": 1,
  "contractTerms": "Standard terms and conditions",
  "notes": "Preferred carrier for electronics shipments"
}
```

#### Activate Network

```http
PUT /api/v1/fleet/networks/{networkId}/activate
Authorization: Bearer <token>
Content-Type: application/json

{
  "activationDate": "2024-01-15T00:00:00Z",
  "notes": "All verification completed, activating network"
}
```

#### Suspend Network

```http
PUT /api/v1/fleet/networks/{networkId}/suspend
Authorization: Bearer <token>
Content-Type: application/json

{
  "suspensionReason": "Performance issues",
  "suspensionDate": "2024-01-15T00:00:00Z",
  "notes": "Temporary suspension pending investigation"
}
```

### 5. Document Management

#### Get Documents

```http
GET /api/v1/fleet/documents
Authorization: Bearer <token>
```

**Query Parameters:**

- `entityType`: `Carrier|Vehicle|Driver`
- `entityId`: ID of the entity
- `documentType`: Filter by document type
- `status`: Filter by verification status
- `expiringWithinDays`: Filter documents expiring within X days

**Response:**

```json
{
  "data": [
    {
      "id": "document-uuid",
      "entityType": "Vehicle",
      "entityId": "vehicle-uuid",
      "documentType": "Registration|Insurance|PUC|Permit|FitnessTest",
      "fileName": "vehicle_registration.pdf",
      "fileUrl": "https://storage.tli.com/documents/doc-uuid.pdf",
      "fileSize": 1024000,
      "uploadedAt": "2024-01-01T00:00:00Z",
      "status": "Pending|Approved|Rejected|Expired",
      "expiryDate": "2025-01-15T00:00:00Z",
      "verifiedAt": "2024-01-02T00:00:00Z",
      "verifiedBy": "admin-uuid",
      "rejectionReason": null,
      "notes": "Valid registration certificate"
    }
  ]
}
```

#### Upload Document

```http
POST /api/v1/fleet/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "entityType": "Vehicle",
  "entityId": "vehicle-uuid",
  "documentType": "Registration",
  "file": <binary-file-data>,
  "expiryDate": "2025-01-15T00:00:00Z",
  "description": "Vehicle registration certificate"
}
```

#### Verify Document

```http
PUT /api/v1/fleet/documents/{documentId}/verify
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "Approved",
  "verificationNotes": "Document verified and approved",
  "verifiedBy": "admin-uuid"
}
```

### 6. Performance Analytics

#### Get Carrier Performance

```http
GET /api/v1/fleet/analytics/carriers/{carrierId}/performance
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Start date for analytics
- `toDate`: End date for analytics
- `granularity`: `daily|weekly|monthly`

**Response:**

```json
{
  "carrierId": "carrier-uuid",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T00:00:00Z"
  },
  "metrics": {
    "totalTrips": 45,
    "completedTrips": 43,
    "cancelledTrips": 2,
    "onTimeDeliveryRate": 95.6,
    "averageRating": 4.5,
    "totalRevenue": 87500.0,
    "averageRevenuePerTrip": 1944.4,
    "fuelEfficiency": 8.2,
    "maintenanceCost": 12000.0,
    "profitMargin": 25.5
  },
  "trends": [
    {
      "date": "2024-01-01",
      "trips": 2,
      "revenue": 3500.0,
      "onTimeRate": 100.0
    }
  ],
  "topRoutes": [
    {
      "route": "Mumbai to Delhi",
      "trips": 8,
      "revenue": 24000.0,
      "averageRating": 4.7
    }
  ]
}
```

#### Get Fleet Utilization

```http
GET /api/v1/fleet/analytics/fleet/utilization
Authorization: Bearer <token>
```

**Response:**

```json
{
  "totalVehicles": 25,
  "activeVehicles": 20,
  "utilizationRate": 80.0,
  "vehiclesByStatus": {
    "Available": 5,
    "InUse": 15,
    "Maintenance": 3,
    "OutOfService": 2
  },
  "utilizationTrends": [
    {
      "date": "2024-01-01",
      "utilizationRate": 75.0,
      "activeVehicles": 18
    }
  ],
  "topPerformingVehicles": [
    {
      "vehicleId": "vehicle-uuid",
      "vehicleNumber": "MH-01-AB-1234",
      "trips": 12,
      "revenue": 18000.0,
      "utilizationRate": 95.0
    }
  ]
}
```

### 7. Admin Operations

#### Get Admin Dashboard

```http
GET /api/v1/fleet/admin/dashboard
Authorization: Bearer <token>
```

**Response:**

```json
{
  "overview": {
    "totalCarriers": 150,
    "activeCarriers": 120,
    "totalVehicles": 500,
    "activeVehicles": 400,
    "totalDrivers": 600,
    "activeDrivers": 480
  },
  "onboardingStats": {
    "pendingCarriers": 15,
    "pendingDrivers": 25,
    "pendingDocuments": 45,
    "averageOnboardingTime": 7.5
  },
  "performanceMetrics": {
    "averageCarrierRating": 4.3,
    "onTimeDeliveryRate": 94.5,
    "fleetUtilizationRate": 78.5,
    "documentComplianceRate": 92.0
  },
  "alerts": [
    {
      "type": "DocumentExpiry",
      "count": 12,
      "message": "12 documents expiring within 30 days"
    },
    {
      "type": "MaintenanceDue",
      "count": 8,
      "message": "8 vehicles due for maintenance"
    }
  ]
}
```

#### Get Compliance Report

```http
GET /api/v1/fleet/admin/compliance
Authorization: Bearer <token>
```

**Response:**

```json
{
  "documentCompliance": {
    "totalDocuments": 1500,
    "validDocuments": 1380,
    "expiredDocuments": 45,
    "expiringDocuments": 75,
    "complianceRate": 92.0
  },
  "carrierCompliance": [
    {
      "carrierId": "carrier-uuid",
      "companyName": "ABC Transport",
      "complianceScore": 95.5,
      "expiredDocuments": 0,
      "expiringDocuments": 2,
      "lastAuditDate": "2024-01-01T00:00:00Z"
    }
  ],
  "expiringDocuments": [
    {
      "documentId": "doc-uuid",
      "entityType": "Vehicle",
      "entityId": "vehicle-uuid",
      "documentType": "Insurance",
      "expiryDate": "2024-02-15T00:00:00Z",
      "daysToExpiry": 15
    }
  ]
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/fleet/hub`

**Events:**

- `VehicleLocationUpdated` - Real-time vehicle location
- `DriverStatusChanged` - Driver status updates
- `MaintenanceScheduled` - Maintenance scheduling
- `DocumentExpiring` - Document expiry alerts
- `NetworkStatusChanged` - Network relationship changes
- `PerformanceUpdated` - Performance metrics updates

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/fleet/hub')
  .build()

connection.on('VehicleLocationUpdated', (data) => {
  console.log('Vehicle location updated:', data)
  // Update map with new location
})

connection.on('DocumentExpiring', (data) => {
  console.log('Document expiring:', data)
  // Show expiry notification
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                    | Description                          |
| ---- | -------------------------- | ------------------------------------ |
| 400  | `INVALID_VEHICLE_DATA`     | Invalid vehicle information          |
| 400  | `DRIVER_ALREADY_ASSIGNED`  | Driver already assigned to vehicle   |
| 400  | `VEHICLE_NOT_AVAILABLE`    | Vehicle not available for assignment |
| 401  | `UNAUTHORIZED`             | Authentication required              |
| 403  | `INSUFFICIENT_PERMISSIONS` | Insufficient permissions             |
| 404  | `CARRIER_NOT_FOUND`        | Carrier not found                    |
| 404  | `VEHICLE_NOT_FOUND`        | Vehicle not found                    |
| 404  | `DRIVER_NOT_FOUND`         | Driver not found                     |
| 409  | `NETWORK_ALREADY_EXISTS`   | Network relationship already exists  |
| 409  | `VEHICLE_NUMBER_EXISTS`    | Vehicle number already registered    |
| 422  | `VALIDATION_FAILED`        | Input validation failed              |
| 429  | `RATE_LIMIT_EXCEEDED`      | Too many requests                    |

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Input validation failed",
    "details": [
      {
        "field": "vehicleNumber",
        "message": "Vehicle number is required"
      }
    ],
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
