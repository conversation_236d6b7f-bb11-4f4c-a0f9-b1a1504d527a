# Network & Fleet Management Service Setup and Run Script

Write-Host "🚀 Setting up Network & Fleet Management Service..." -ForegroundColor Green

# Check if .NET 8 is installed
Write-Host "Checking .NET 8 installation..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version
if ($dotnetVersion -notlike "8.*") {
    Write-Host "❌ .NET 8 is required. Please install .NET 8 SDK." -ForegroundColor Red
    exit 1
}
Write-Host "✅ .NET 8 is installed: $dotnetVersion" -ForegroundColor Green

# Check if Docker is running
Write-Host "Checking Docker..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker." -ForegroundColor Red
    exit 1
}

# Create network if it doesn't exist
Write-Host "Creating TLI network..." -ForegroundColor Yellow
docker network create tli-network 2>$null
Write-Host "✅ TLI network ready" -ForegroundColor Green

# Start PostgreSQL container
Write-Host "Starting PostgreSQL container..." -ForegroundColor Yellow
docker run -d --name networkfleet-postgres `
    --network tli-network `
    -e POSTGRES_DB=TLI_NetworkFleetManagement `
    -e POSTGRES_USER=timescale `
    -e POSTGRES_PASSWORD=timescale `
    -p 5434:5432 `
    -v networkfleet_postgres_data:/var/lib/postgresql/data `
    timescale/timescaledb:latest-pg15

# Wait for PostgreSQL to be ready
Write-Host "Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if PostgreSQL is ready
$maxAttempts = 30
$attempt = 0
do {
    $attempt++
    try {
        $env:PGPASSWORD = "timescale"
        psql -h localhost -p 5434 -U timescale -d TLI_NetworkFleetManagement -c "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL is ready" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($attempt -eq $maxAttempts) {
        Write-Host "❌ PostgreSQL failed to start within timeout" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Waiting for PostgreSQL... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
    Start-Sleep -Seconds 2
} while ($true)

# Run database setup script
Write-Host "Setting up database schema..." -ForegroundColor Yellow
$env:PGPASSWORD = "timescale"
psql -h localhost -p 5434 -U timescale -d TLI_NetworkFleetManagement -f database-setup.sql
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Database schema created" -ForegroundColor Green
} else {
    Write-Host "⚠️ Database schema setup completed with warnings" -ForegroundColor Yellow
}

# Start RabbitMQ container
Write-Host "Starting RabbitMQ container..." -ForegroundColor Yellow
docker run -d --name networkfleet-rabbitmq `
    --network tli-network `
    -e RABBITMQ_DEFAULT_USER=admin `
    -e RABBITMQ_DEFAULT_PASS=admin123 `
    -p 5674:5672 `
    -p 15674:15672 `
    -v networkfleet_rabbitmq_data:/var/lib/rabbitmq `
    rabbitmq:3-management

# Start Redis container
Write-Host "Starting Redis container..." -ForegroundColor Yellow
docker run -d --name networkfleet-redis `
    --network tli-network `
    -p 6381:6379 `
    -v networkfleet_redis_data:/data `
    redis:7-alpine redis-server --appendonly yes

Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Restore NuGet packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NuGet packages restored" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to restore NuGet packages" -ForegroundColor Red
    exit 1
}

# Build the solution
Write-Host "Building the solution..." -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Solution built successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Run database migrations
Write-Host "Running database migrations..." -ForegroundColor Yellow
Set-Location "NetworkFleetManagement.API"
dotnet ef database update --no-build
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Database migrations completed" -ForegroundColor Green
} else {
    Write-Host "⚠️ Database migrations completed with warnings" -ForegroundColor Yellow
}
Set-Location ".."

# Run tests
Write-Host "Running unit tests..." -ForegroundColor Yellow
dotnet test NetworkFleetManagement.Tests --no-build --verbosity minimal
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Unit tests passed" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some unit tests failed" -ForegroundColor Yellow
}

# Start the API service
Write-Host "Starting Network & Fleet Management API..." -ForegroundColor Yellow
Write-Host "🌐 API will be available at: http://localhost:5005" -ForegroundColor Cyan
Write-Host "📚 Swagger UI will be available at: http://localhost:5005" -ForegroundColor Cyan
Write-Host "🐰 RabbitMQ Management UI: http://localhost:15674 (admin/admin123)" -ForegroundColor Cyan
Write-Host "🗄️ PostgreSQL: localhost:5434 (timescale/timescale)" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White

Set-Location "NetworkFleetManagement.API"
$env:ASPNETCORE_ENVIRONMENT = "Development"
$env:ASPNETCORE_URLS = "http://localhost:5005"
dotnet run --no-build
