using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetOrderTimeline;

public class GetOrderTimelineQueryHandler : IRequestHandler<GetOrderTimelineQuery, PagedResult<OrderTimelineDto>>
{
    private readonly IOrderRepository _orderRepository;
    private readonly ILogger<GetOrderTimelineQueryHandler> _logger;

    public GetOrderTimelineQueryHandler(
        IOrderRepository orderRepository,
        ILogger<GetOrderTimelineQueryHandler> logger)
    {
        _orderRepository = orderRepository;
        _logger = logger;
    }

    public async Task<PagedResult<OrderTimelineDto>> Handle(GetOrderTimelineQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Retrieving timeline for order {OrderId}", request.OrderId);

            // Get the order with timeline
            var order = await _orderRepository.GetByIdWithTimelineAsync(request.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found", request.OrderId);
                throw new OrderManagementException("Order not found");
            }

            // Apply filters
            var timeline = order.Timeline.AsQueryable();

            if (request.EventType.HasValue)
                timeline = timeline.Where(t => t.EventType == request.EventType.Value);

            if (request.FromDate.HasValue)
                timeline = timeline.Where(t => t.EventTimestamp >= request.FromDate.Value);

            if (request.ToDate.HasValue)
                timeline = timeline.Where(t => t.EventTimestamp <= request.ToDate.Value);

            if (request.ActorId.HasValue)
                timeline = timeline.Where(t => t.ActorId == request.ActorId.Value);

            if (!string.IsNullOrEmpty(request.ActorRole))
                timeline = timeline.Where(t => t.ActorRole == request.ActorRole);

            // Apply sorting
            timeline = request.SortDirection?.ToLower() == "asc"
                ? timeline.OrderBy(t => t.EventTimestamp)
                : timeline.OrderByDescending(t => t.EventTimestamp);

            // Get total count
            var totalCount = timeline.Count();

            // Apply pagination
            var pagedTimeline = timeline
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            // Map to DTOs
            var timelineDtos = pagedTimeline.Select(t => new OrderTimelineDto
            {
                Id = t.Id,
                OrderId = t.OrderId,
                EventType = t.EventType,
                EventDescription = t.EventDescription,
                EventTimestamp = t.EventTimestamp,
                ActorId = t.ActorId,
                ActorName = t.ActorName,
                ActorRole = t.ActorRole,
                AdditionalData = t.AdditionalData,
                PreviousStatus = t.PreviousStatus,
                NewStatus = t.NewStatus,
                Notes = t.Notes,
                IpAddress = request.IncludeMetadata ? t.IpAddress : null,
                UserAgent = request.IncludeMetadata ? t.UserAgent : null,
                CorrelationId = t.CorrelationId,
                SessionId = request.IncludeMetadata ? t.SessionId : null,
                Metadata = request.IncludeMetadata ? t.Metadata : null
            }).ToList();

            _logger.LogInformation("Retrieved {Count} timeline events for order {OrderId}",
                timelineDtos.Count, request.OrderId);

            return new PagedResult<OrderTimelineDto>
            {
                Items = timelineDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
                // TotalPages is calculated automatically
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving timeline for order {OrderId}", request.OrderId);
            throw;
        }
    }
}
