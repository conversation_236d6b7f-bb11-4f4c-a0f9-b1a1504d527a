using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Application.Commands.UpdateTripLocation;

public class UpdateTripLocationCommandHandler : IRequestHandler<UpdateTripLocationCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateTripLocationCommandHandler> _logger;

    public UpdateTripLocationCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<UpdateTripLocationCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateTripLocationCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating location for trip {TripId}", request.TripId);

        // Get trip
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
        {
            _logger.LogWarning("Trip {TripId} not found", request.TripId);
            throw new ArgumentException($"Trip {request.TripId} not found");
        }

        // Create location
        var location = new Location(
            request.Location.Latitude,
            request.Location.Longitude,
            request.Location.Timestamp,
            request.Location.Altitude,
            request.Location.Accuracy,
            request.Location.Address,
            request.Location.City,
            request.Location.State,
            request.Location.Country,
            request.Location.PostalCode);

        // Update trip location
        trip.UpdateLocation(location, request.Source);

        // Save changes
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("trip.location_updated", new
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            Location = new
            {
                Latitude = location.Latitude,
                Longitude = location.Longitude,
                Altitude = location.Altitude,
                Accuracy = location.Accuracy,
                Timestamp = location.Timestamp,
                Address = location.Address,
                City = location.City,
                State = location.State,
                Country = location.Country,
                PostalCode = location.PostalCode
            },
            Source = request.Source.ToString(),
            Speed = request.Speed,
            Heading = request.Heading,
            UpdatedAt = DateTime.UtcNow
        }, cancellationToken);

        _logger.LogInformation("Successfully updated location for trip {TripId}", request.TripId);

        return true;
    }
}
