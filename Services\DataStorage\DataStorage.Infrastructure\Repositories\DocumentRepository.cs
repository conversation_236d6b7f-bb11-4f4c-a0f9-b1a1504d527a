using Microsoft.EntityFrameworkCore;
using DataStorage.Domain.Entities;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.Enums;
using DataStorage.Infrastructure.Data;

namespace DataStorage.Infrastructure.Repositories;

public class DocumentRepository : IDocumentRepository
{
    private readonly DataStorageDbContext _context;

    public DocumentRepository(DataStorageDbContext context)
    {
        _context = context;
    }

    public async Task<Document?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
    }

    public async Task<Document?> GetByIdWithVersionsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Include(d => d.Versions)
            .Include(d => d.AccessGrants)
            .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Where(d => d.OwnerId == ownerId)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetByOwnerAndTypeAsync(Guid ownerId, DocumentType documentType, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Where(d => d.OwnerId == ownerId && d.DocumentType == documentType)
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Document document, CancellationToken cancellationToken = default)
    {
        await _context.Documents.AddAsync(document, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Document document, CancellationToken cancellationToken = default)
    {
        _context.Documents.Update(document);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var document = await GetByIdAsync(id, cancellationToken);
        if (document != null)
        {
            _context.Documents.Remove(document);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Document>> SearchAsync(
        string? searchTerm = null,
        DocumentType? documentType = null,
        DocumentCategory? category = null,
        DocumentStatus? status = null,
        Guid? ownerId = null,
        string? ownerType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Documents.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(d => d.Title.Contains(searchTerm) || 
                                   (d.Description != null && d.Description.Contains(searchTerm)));
        }

        if (documentType.HasValue)
            query = query.Where(d => d.DocumentType == documentType.Value);

        if (category.HasValue)
            query = query.Where(d => d.Category == category.Value);

        if (status.HasValue)
            query = query.Where(d => d.Status == status.Value);

        if (ownerId.HasValue)
            query = query.Where(d => d.OwnerId == ownerId.Value);

        if (!string.IsNullOrWhiteSpace(ownerType))
            query = query.Where(d => d.OwnerType == ownerType);

        if (createdAfter.HasValue)
            query = query.Where(d => d.CreatedAt >= createdAfter.Value);

        if (createdBefore.HasValue)
            query = query.Where(d => d.CreatedAt <= createdBefore.Value);

        return await query
            .OrderByDescending(d => d.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> CountAsync(
        string? searchTerm = null,
        DocumentType? documentType = null,
        DocumentCategory? category = null,
        DocumentStatus? status = null,
        Guid? ownerId = null,
        string? ownerType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Documents.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(d => d.Title.Contains(searchTerm) || 
                                   (d.Description != null && d.Description.Contains(searchTerm)));
        }

        if (documentType.HasValue)
            query = query.Where(d => d.DocumentType == documentType.Value);

        if (category.HasValue)
            query = query.Where(d => d.Category == category.Value);

        if (status.HasValue)
            query = query.Where(d => d.Status == status.Value);

        if (ownerId.HasValue)
            query = query.Where(d => d.OwnerId == ownerId.Value);

        if (!string.IsNullOrWhiteSpace(ownerType))
            query = query.Where(d => d.OwnerType == ownerType);

        if (createdAfter.HasValue)
            query = query.Where(d => d.CreatedAt >= createdAfter.Value);

        if (createdBefore.HasValue)
            query = query.Where(d => d.CreatedAt <= createdBefore.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetAccessibleDocumentsAsync(
        Guid userId,
        AccessLevel minimumAccessLevel = AccessLevel.Private,
        CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Where(d => d.OwnerId == userId || 
                       d.AccessGrants.Any(a => a.UserId == userId && 
                                             a.AccessLevel >= minimumAccessLevel && 
                                             (a.ExpiresAt == null || a.ExpiresAt > DateTime.UtcNow)))
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DocumentVersion>> GetDocumentVersionsAsync(Guid documentId, CancellationToken cancellationToken = default)
    {
        return await _context.DocumentVersions
            .Where(dv => dv.DocumentId == documentId)
            .OrderByDescending(dv => dv.VersionNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<DocumentVersion?> GetDocumentVersionAsync(Guid documentId, int versionNumber, CancellationToken cancellationToken = default)
    {
        return await _context.DocumentVersions
            .FirstOrDefaultAsync(dv => dv.DocumentId == documentId && dv.VersionNumber == versionNumber, cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetExpiredDocumentsAsync(DateTime beforeDate, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Where(d => d.ExpiresAt.HasValue && d.ExpiresAt.Value <= beforeDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Document>> GetDocumentsForArchivalAsync(DateTime beforeDate, CancellationToken cancellationToken = default)
    {
        return await _context.Documents
            .Where(d => d.CreatedAt <= beforeDate && d.Status != DocumentStatus.Archived && d.Status != DocumentStatus.Deleted)
            .ToListAsync(cancellationToken);
    }

    public async Task BulkUpdateStatusAsync(IEnumerable<Guid> documentIds, DocumentStatus newStatus, CancellationToken cancellationToken = default)
    {
        await _context.Documents
            .Where(d => documentIds.Contains(d.Id))
            .ExecuteUpdateAsync(d => d.SetProperty(x => x.Status, newStatus), cancellationToken);
    }

    public async Task<Dictionary<DocumentType, int>> GetDocumentCountByTypeAsync(Guid? ownerId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Documents.AsQueryable();
        
        if (ownerId.HasValue)
            query = query.Where(d => d.OwnerId == ownerId.Value);

        return await query
            .GroupBy(d => d.DocumentType)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<DocumentStatus, int>> GetDocumentCountByStatusAsync(Guid? ownerId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Documents.AsQueryable();
        
        if (ownerId.HasValue)
            query = query.Where(d => d.OwnerId == ownerId.Value);

        return await query
            .GroupBy(d => d.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<long> GetTotalStorageSizeAsync(Guid? ownerId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Documents.AsQueryable();
        
        if (ownerId.HasValue)
            query = query.Where(d => d.OwnerId == ownerId.Value);

        return await query.SumAsync(d => d.FileMetadata.FileSizeBytes, cancellationToken);
    }
}
