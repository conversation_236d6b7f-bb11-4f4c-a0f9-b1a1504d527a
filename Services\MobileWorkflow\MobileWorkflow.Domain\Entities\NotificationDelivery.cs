using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class NotificationDelivery : AggregateRoot
{
    public Guid CampaignId { get; private set; }
    public Guid UserId { get; private set; }
    public string DeviceToken { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Web
    public string Status { get; private set; } // Pending, Sent, Delivered, Failed, Opened, Clicked
    public Dictionary<string, object> Content { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? SentAt { get; private set; }
    public DateTime? DeliveredAt { get; private set; }
    public DateTime? OpenedAt { get; private set; }
    public DateTime? ClickedAt { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorCode { get; private set; }
    public int RetryCount { get; private set; }
    public DateTime? NextRetryAt { get; private set; }
    public string? ExternalId { get; private set; } // Provider-specific ID (FCM, APNS, etc.)
    public Dictionary<string, object> ProviderResponse { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual NotificationCampaign Campaign { get; private set; } = null!;

    private NotificationDelivery() { } // EF Core

    public NotificationDelivery(
        Guid campaignId,
        Guid userId,
        string deviceToken,
        string platform,
        Dictionary<string, object> content)
    {
        CampaignId = campaignId;
        UserId = userId;
        DeviceToken = deviceToken ?? throw new ArgumentNullException(nameof(deviceToken));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Content = content ?? throw new ArgumentNullException(nameof(content));

        Status = "Pending";
        CreatedAt = DateTime.UtcNow;
        RetryCount = 0;
        ProviderResponse = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new NotificationDeliveryCreatedEvent(Id, CampaignId, UserId, Platform));
    }

    public void MarkAsSent(string? externalId = null)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot mark as sent with status: {Status}");

        Status = "Sent";
        SentAt = DateTime.UtcNow;
        ExternalId = externalId;

        AddDomainEvent(new NotificationDeliverySentEvent(Id, CampaignId, UserId, Platform, ExternalId));
    }

    public void MarkAsDelivered()
    {
        if (Status != "Sent")
            throw new InvalidOperationException($"Cannot mark as delivered with status: {Status}");

        Status = "Delivered";
        DeliveredAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationDeliveryDeliveredEvent(Id, CampaignId, UserId, Platform));
    }

    public void MarkAsFailed(string errorMessage, string? errorCode = null)
    {
        Status = "Failed";
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;

        AddDomainEvent(new NotificationDeliveryFailedEvent(Id, CampaignId, UserId, Platform, ErrorMessage, ErrorCode));
    }

    public void MarkAsOpened()
    {
        if (Status != "Delivered")
            throw new InvalidOperationException($"Cannot mark as opened with status: {Status}");

        Status = "Opened";
        OpenedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationDeliveryOpenedEvent(Id, CampaignId, UserId, Platform));
    }

    public void MarkAsClicked()
    {
        if (Status != "Delivered" && Status != "Opened")
            throw new InvalidOperationException($"Cannot mark as clicked with status: {Status}");

        Status = "Clicked";
        ClickedAt = DateTime.UtcNow;

        // Also mark as opened if not already
        if (OpenedAt == null)
        {
            OpenedAt = DateTime.UtcNow;
        }

        AddDomainEvent(new NotificationDeliveryClickedEvent(Id, CampaignId, UserId, Platform));
    }

    public void ScheduleRetry(TimeSpan delay)
    {
        if (Status != "Failed")
            throw new InvalidOperationException($"Cannot schedule retry with status: {Status}");

        RetryCount++;
        NextRetryAt = DateTime.UtcNow.Add(delay);
        Status = "Pending";
        ErrorMessage = null;
        ErrorCode = null;

        AddDomainEvent(new NotificationDeliveryRetryScheduledEvent(Id, CampaignId, UserId, Platform, RetryCount, NextRetryAt));
    }

    public void SetProviderResponse(Dictionary<string, object> response)
    {
        ProviderResponse = response ?? throw new ArgumentNullException(nameof(response));
    }

    public bool CanRetry(int maxRetries)
    {
        return Status == "Failed" && RetryCount < maxRetries &&
               (NextRetryAt == null || DateTime.UtcNow >= NextRetryAt);
    }

    public bool IsSuccessful()
    {
        return Status == "Delivered" || Status == "Opened" || Status == "Clicked";
    }

    public bool IsFailed()
    {
        return Status == "Failed";
    }

    public bool IsPending()
    {
        return Status == "Pending";
    }

    public bool IsEngaged()
    {
        return Status == "Opened" || Status == "Clicked";
    }

    public TimeSpan? GetDeliveryTime()
    {
        if (SentAt.HasValue && DeliveredAt.HasValue)
        {
            return DeliveredAt.Value - SentAt.Value;
        }
        return null;
    }

    public TimeSpan? GetTimeToOpen()
    {
        if (DeliveredAt.HasValue && OpenedAt.HasValue)
        {
            return OpenedAt.Value - DeliveredAt.Value;
        }
        return null;
    }

    public TimeSpan? GetTimeToClick()
    {
        if (DeliveredAt.HasValue && ClickedAt.HasValue)
        {
            return ClickedAt.Value - DeliveredAt.Value;
        }
        return null;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetContent<T>(string key, T? defaultValue = default)
    {
        if (Content.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetProviderResponse<T>(string key, T? defaultValue = default)
    {
        if (ProviderResponse.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public Dictionary<string, object> GetDeliveryMetrics()
    {
        var metrics = new Dictionary<string, object>
        {
            ["status"] = Status,
            ["platform"] = Platform,
            ["retryCount"] = RetryCount,
            ["createdAt"] = CreatedAt
        };

        if (SentAt.HasValue)
            metrics["sentAt"] = SentAt.Value;

        if (DeliveredAt.HasValue)
            metrics["deliveredAt"] = DeliveredAt.Value;

        if (OpenedAt.HasValue)
            metrics["openedAt"] = OpenedAt.Value;

        if (ClickedAt.HasValue)
            metrics["clickedAt"] = ClickedAt.Value;

        var deliveryTime = GetDeliveryTime();
        if (deliveryTime.HasValue)
            metrics["deliveryTimeMs"] = deliveryTime.Value.TotalMilliseconds;

        var timeToOpen = GetTimeToOpen();
        if (timeToOpen.HasValue)
            metrics["timeToOpenMs"] = timeToOpen.Value.TotalMilliseconds;

        var timeToClick = GetTimeToClick();
        if (timeToClick.HasValue)
            metrics["timeToClickMs"] = timeToClick.Value.TotalMilliseconds;

        if (!string.IsNullOrEmpty(ErrorMessage))
        {
            metrics["errorMessage"] = ErrorMessage;
            metrics["errorCode"] = ErrorCode;
        }

        return metrics;
    }
}

public class DeviceToken : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string Token { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Web
    public string AppVersion { get; private set; }
    public string DeviceInfo { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? LastUsedAt { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private DeviceToken() { } // EF Core

    public DeviceToken(
        Guid userId,
        string token,
        string platform,
        string appVersion,
        string deviceInfo)
    {
        UserId = userId;
        Token = token ?? throw new ArgumentNullException(nameof(token));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new DeviceTokenRegisteredEvent(Id, UserId, Platform, token.Substring(0, Math.Min(10, token.Length)) + "..."));
    }

    public void UpdateToken(string newToken, string appVersion, string deviceInfo)
    {
        Token = newToken ?? throw new ArgumentNullException(nameof(newToken));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceTokenUpdatedEvent(Id, UserId, Platform));
    }

    public void MarkAsUsed()
    {
        LastUsedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceTokenDeactivatedEvent(Id, UserId, Platform));
    }

    public void Reactivate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DeviceTokenReactivatedEvent(Id, UserId, Platform));
    }

    public bool IsStale(TimeSpan maxAge)
    {
        var lastActivity = LastUsedAt ?? UpdatedAt ?? CreatedAt;
        return DateTime.UtcNow - lastActivity > maxAge;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


