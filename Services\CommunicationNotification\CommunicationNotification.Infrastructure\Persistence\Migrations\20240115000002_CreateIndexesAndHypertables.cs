using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CommunicationNotification.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class CreateIndexesAndHypertables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create indexes for notifications
            migrationBuilder.CreateIndex(
                name: "IX_Notifications_UserId",
                schema: "communication",
                table: "notifications",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_Status",
                schema: "communication",
                table: "notifications",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_ScheduledAt",
                schema: "communication",
                table: "notifications",
                column: "scheduled_at");

            migrationBuilder.CreateIndex(
                name: "IX_Notifications_UserId_Status",
                schema: "communication",
                table: "notifications",
                columns: new[] { "user_id", "status" });

            // Create indexes for messages
            migrationBuilder.CreateIndex(
                name: "IX_Messages_SenderId",
                schema: "communication",
                table: "messages",
                column: "sender_id");

            migrationBuilder.CreateIndex(
                name: "IX_Messages_ReceiverId",
                schema: "communication",
                table: "messages",
                column: "receiver_id");

            migrationBuilder.CreateIndex(
                name: "IX_Messages_ConversationThreadId",
                schema: "communication",
                table: "messages",
                column: "conversation_thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_Messages_SentAt",
                schema: "communication",
                table: "messages",
                column: "sent_at");

            migrationBuilder.CreateIndex(
                name: "IX_Messages_SenderId_ReceiverId_SentAt",
                schema: "communication",
                table: "messages",
                columns: new[] { "sender_id", "receiver_id", "sent_at" });

            // Create indexes for user_preferences
            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_UserId",
                schema: "communication",
                table: "user_preferences",
                column: "user_id",
                unique: true);

            // Create indexes for conversation_threads
            migrationBuilder.CreateIndex(
                name: "IX_ConversationThreads_CreatedBy",
                schema: "communication",
                table: "conversation_threads",
                column: "created_by");

            migrationBuilder.CreateIndex(
                name: "IX_ConversationThreads_RelatedEntityId",
                schema: "communication",
                table: "conversation_threads",
                column: "related_entity_id");

            migrationBuilder.CreateIndex(
                name: "IX_ConversationThreads_IsActive",
                schema: "communication",
                table: "conversation_threads",
                column: "is_active");

            // Create indexes for conversation_participants
            migrationBuilder.CreateIndex(
                name: "IX_ConversationParticipants_UserId",
                schema: "communication",
                table: "conversation_participants",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_ConversationParticipants_ConversationThreadId",
                schema: "communication",
                table: "conversation_participants",
                column: "conversation_thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_ConversationParticipants_ConversationThreadId_UserId",
                schema: "communication",
                table: "conversation_participants",
                columns: new[] { "conversation_thread_id", "user_id" },
                unique: true);

            // Create indexes for templates
            migrationBuilder.CreateIndex(
                name: "IX_Templates_Name",
                schema: "communication",
                table: "templates",
                column: "name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Templates_MessageType_Channel_Language",
                schema: "communication",
                table: "templates",
                columns: new[] { "message_type", "channel", "language" });

            // Create indexes for template_versions
            migrationBuilder.CreateIndex(
                name: "IX_TemplateVersions_TemplateId_Version",
                schema: "communication",
                table: "template_versions",
                columns: new[] { "template_id", "version" },
                unique: true);

            // Create indexes for delivery_attempts
            migrationBuilder.CreateIndex(
                name: "IX_DeliveryAttempts_NotificationId",
                schema: "communication",
                table: "delivery_attempts",
                column: "notification_id");

            migrationBuilder.CreateIndex(
                name: "IX_DeliveryAttempts_AttemptedAt",
                schema: "communication",
                table: "delivery_attempts",
                column: "attempted_at");

            // Create indexes for message_archives
            migrationBuilder.CreateIndex(
                name: "IX_MessageArchives_OriginalMessageId",
                schema: "communication",
                table: "message_archives",
                column: "original_message_id");

            migrationBuilder.CreateIndex(
                name: "IX_MessageArchives_UserId",
                schema: "communication",
                table: "message_archives",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_MessageArchives_ArchivedAt",
                schema: "communication",
                table: "message_archives",
                column: "archived_at");

            // Create indexes for audit_logs
            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId",
                schema: "communication",
                table: "audit_logs",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Timestamp",
                schema: "communication",
                table: "audit_logs",
                column: "timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityType",
                schema: "communication",
                table: "audit_logs",
                column: "entity_type");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityId",
                schema: "communication",
                table: "audit_logs",
                column: "entity_id");

            // Create indexes for compliance_records
            migrationBuilder.CreateIndex(
                name: "IX_ComplianceRecords_UserId",
                schema: "communication",
                table: "compliance_records",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_ComplianceRecords_ComplianceType",
                schema: "communication",
                table: "compliance_records",
                column: "compliance_type");

            migrationBuilder.CreateIndex(
                name: "IX_ComplianceRecords_Timestamp",
                schema: "communication",
                table: "compliance_records",
                column: "timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_ComplianceRecords_ExpiresAt",
                schema: "communication",
                table: "compliance_records",
                column: "expires_at");

            migrationBuilder.CreateIndex(
                name: "IX_ComplianceRecords_UserId_ComplianceType_IsActive",
                schema: "communication",
                table: "compliance_records",
                columns: new[] { "user_id", "compliance_type", "is_active" });

            // Create TimescaleDB hypertables for time-series data
            migrationBuilder.Sql(@"
                -- Create hypertable for notifications partitioned by scheduled_at
                SELECT create_hypertable('communication.notifications', 'scheduled_at', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
                
                -- Create hypertable for messages partitioned by sent_at
                SELECT create_hypertable('communication.messages', 'sent_at', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
                
                -- Create hypertable for delivery_attempts partitioned by attempted_at
                SELECT create_hypertable('communication.delivery_attempts', 'attempted_at', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
                
                -- Create hypertable for audit_logs partitioned by timestamp
                SELECT create_hypertable('communication.audit_logs', 'timestamp', 
                    chunk_time_interval => INTERVAL '1 day',
                    create_default_indexes => FALSE);
            ");

            // Create data retention policies for TimescaleDB
            migrationBuilder.Sql(@"
                -- Add data retention policy for notifications (keep for 2 years)
                SELECT add_retention_policy('communication.notifications', INTERVAL '2 years');
                
                -- Add data retention policy for messages (keep for 7 years for compliance)
                SELECT add_retention_policy('communication.messages', INTERVAL '7 years');
                
                -- Add data retention policy for delivery_attempts (keep for 1 year)
                SELECT add_retention_policy('communication.delivery_attempts', INTERVAL '1 year');
                
                -- Add data retention policy for audit_logs (keep for 7 years for compliance)
                SELECT add_retention_policy('communication.audit_logs', INTERVAL '7 years');
            ");

            // Create compression policies for better storage efficiency
            migrationBuilder.Sql(@"
                -- Add compression policy for notifications (compress after 7 days)
                SELECT add_compression_policy('communication.notifications', INTERVAL '7 days');
                
                -- Add compression policy for messages (compress after 30 days)
                SELECT add_compression_policy('communication.messages', INTERVAL '30 days');
                
                -- Add compression policy for delivery_attempts (compress after 7 days)
                SELECT add_compression_policy('communication.delivery_attempts', INTERVAL '7 days');
                
                -- Add compression policy for audit_logs (compress after 30 days)
                SELECT add_compression_policy('communication.audit_logs', INTERVAL '30 days');
            ");

            // Create continuous aggregates for analytics
            migrationBuilder.Sql(@"
                -- Create continuous aggregate for daily notification stats
                CREATE MATERIALIZED VIEW communication.daily_notification_stats
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 day', scheduled_at) AS day,
                    user_id,
                    channel,
                    status,
                    COUNT(*) as notification_count,
                    COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_count,
                    COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_count,
                    AVG(EXTRACT(EPOCH FROM (delivered_at - scheduled_at))) as avg_delivery_time_seconds
                FROM communication.notifications
                WHERE scheduled_at >= NOW() - INTERVAL '1 year'
                GROUP BY day, user_id, channel, status
                WITH NO DATA;
                
                -- Create continuous aggregate for daily message stats
                CREATE MATERIALIZED VIEW communication.daily_message_stats
                WITH (timescaledb.continuous) AS
                SELECT 
                    time_bucket('1 day', sent_at) AS day,
                    sender_id,
                    receiver_id,
                    message_type,
                    COUNT(*) as message_count,
                    COUNT(CASE WHEN read_at IS NOT NULL THEN 1 END) as read_count,
                    AVG(EXTRACT(EPOCH FROM (read_at - sent_at))) as avg_read_time_seconds
                FROM communication.messages
                WHERE sent_at >= NOW() - INTERVAL '1 year'
                GROUP BY day, sender_id, receiver_id, message_type
                WITH NO DATA;
            ");

            // Add refresh policies for continuous aggregates
            migrationBuilder.Sql(@"
                -- Add refresh policy for daily notification stats (refresh every hour)
                SELECT add_continuous_aggregate_policy('communication.daily_notification_stats',
                    start_offset => INTERVAL '1 month',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
                
                -- Add refresh policy for daily message stats (refresh every hour)
                SELECT add_continuous_aggregate_policy('communication.daily_message_stats',
                    start_offset => INTERVAL '1 month',
                    end_offset => INTERVAL '1 hour',
                    schedule_interval => INTERVAL '1 hour');
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop continuous aggregates
            migrationBuilder.Sql("DROP MATERIALIZED VIEW IF EXISTS communication.daily_notification_stats;");
            migrationBuilder.Sql("DROP MATERIALIZED VIEW IF EXISTS communication.daily_message_stats;");

            // Drop all indexes
            migrationBuilder.DropIndex(name: "IX_ComplianceRecords_UserId_ComplianceType_IsActive", schema: "communication", table: "compliance_records");
            migrationBuilder.DropIndex(name: "IX_ComplianceRecords_ExpiresAt", schema: "communication", table: "compliance_records");
            migrationBuilder.DropIndex(name: "IX_ComplianceRecords_Timestamp", schema: "communication", table: "compliance_records");
            migrationBuilder.DropIndex(name: "IX_ComplianceRecords_ComplianceType", schema: "communication", table: "compliance_records");
            migrationBuilder.DropIndex(name: "IX_ComplianceRecords_UserId", schema: "communication", table: "compliance_records");
            
            migrationBuilder.DropIndex(name: "IX_AuditLogs_EntityId", schema: "communication", table: "audit_logs");
            migrationBuilder.DropIndex(name: "IX_AuditLogs_EntityType", schema: "communication", table: "audit_logs");
            migrationBuilder.DropIndex(name: "IX_AuditLogs_Timestamp", schema: "communication", table: "audit_logs");
            migrationBuilder.DropIndex(name: "IX_AuditLogs_UserId", schema: "communication", table: "audit_logs");
            
            migrationBuilder.DropIndex(name: "IX_MessageArchives_ArchivedAt", schema: "communication", table: "message_archives");
            migrationBuilder.DropIndex(name: "IX_MessageArchives_UserId", schema: "communication", table: "message_archives");
            migrationBuilder.DropIndex(name: "IX_MessageArchives_OriginalMessageId", schema: "communication", table: "message_archives");
            
            migrationBuilder.DropIndex(name: "IX_DeliveryAttempts_AttemptedAt", schema: "communication", table: "delivery_attempts");
            migrationBuilder.DropIndex(name: "IX_DeliveryAttempts_NotificationId", schema: "communication", table: "delivery_attempts");
            
            migrationBuilder.DropIndex(name: "IX_TemplateVersions_TemplateId_Version", schema: "communication", table: "template_versions");
            
            migrationBuilder.DropIndex(name: "IX_Templates_MessageType_Channel_Language", schema: "communication", table: "templates");
            migrationBuilder.DropIndex(name: "IX_Templates_Name", schema: "communication", table: "templates");
            
            migrationBuilder.DropIndex(name: "IX_ConversationParticipants_ConversationThreadId_UserId", schema: "communication", table: "conversation_participants");
            migrationBuilder.DropIndex(name: "IX_ConversationParticipants_ConversationThreadId", schema: "communication", table: "conversation_participants");
            migrationBuilder.DropIndex(name: "IX_ConversationParticipants_UserId", schema: "communication", table: "conversation_participants");
            
            migrationBuilder.DropIndex(name: "IX_ConversationThreads_IsActive", schema: "communication", table: "conversation_threads");
            migrationBuilder.DropIndex(name: "IX_ConversationThreads_RelatedEntityId", schema: "communication", table: "conversation_threads");
            migrationBuilder.DropIndex(name: "IX_ConversationThreads_CreatedBy", schema: "communication", table: "conversation_threads");
            
            migrationBuilder.DropIndex(name: "IX_UserPreferences_UserId", schema: "communication", table: "user_preferences");
            
            migrationBuilder.DropIndex(name: "IX_Messages_SenderId_ReceiverId_SentAt", schema: "communication", table: "messages");
            migrationBuilder.DropIndex(name: "IX_Messages_SentAt", schema: "communication", table: "messages");
            migrationBuilder.DropIndex(name: "IX_Messages_ConversationThreadId", schema: "communication", table: "messages");
            migrationBuilder.DropIndex(name: "IX_Messages_ReceiverId", schema: "communication", table: "messages");
            migrationBuilder.DropIndex(name: "IX_Messages_SenderId", schema: "communication", table: "messages");
            
            migrationBuilder.DropIndex(name: "IX_Notifications_UserId_Status", schema: "communication", table: "notifications");
            migrationBuilder.DropIndex(name: "IX_Notifications_ScheduledAt", schema: "communication", table: "notifications");
            migrationBuilder.DropIndex(name: "IX_Notifications_Status", schema: "communication", table: "notifications");
            migrationBuilder.DropIndex(name: "IX_Notifications_UserId", schema: "communication", table: "notifications");
        }
    }
}
