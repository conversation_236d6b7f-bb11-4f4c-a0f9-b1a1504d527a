using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System.Diagnostics;
using System.Collections.Concurrent;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Infrastructure.Services;

public interface IMonitoringService
{
    Task<HealthCheckResultDto> CheckHealthAsync(CancellationToken cancellationToken = default);
    Task<List<ServiceStatusDto>> GetServiceStatusesAsync(CancellationToken cancellationToken = default);
    Task<MetricsCollectionDto> CollectMetricsAsync(CancellationToken cancellationToken = default);
    Task<AlertDto> CreateAlertAsync(CreateAlertRequest request, CancellationToken cancellationToken = default);
    Task<List<AlertDto>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
    Task<bool> ResolveAlertAsync(Guid alertId, string resolvedBy, CancellationToken cancellationToken = default);
    Task<DashboardDto> GetDashboardDataAsync(string dashboardType, CancellationToken cancellationToken = default);
    Task<List<LogEntryDto>> GetLogsAsync(GetLogsRequest request, CancellationToken cancellationToken = default);
    Task<TraceDto> StartTraceAsync(string operationName, Dictionary<string, object>? properties = null);
    Task CompleteTraceAsync(string traceId, bool isSuccess = true, string? errorMessage = null);
    Task<List<TraceDto>> GetTracesAsync(string? operationName = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
}

public class MonitoringService : IMonitoringService
{
    private readonly ICachingService _cachingService;
    private readonly IPerformanceMonitoringService _performanceService;
    private readonly ILogger<MonitoringService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, TraceDto> _activeTraces;
    private readonly ConcurrentDictionary<Guid, AlertDto> _activeAlerts;
    private readonly ConcurrentQueue<LogEntryDto> _logBuffer;

    public MonitoringService(
        ICachingService cachingService,
        IPerformanceMonitoringService performanceService,
        ILogger<MonitoringService> logger,
        IConfiguration configuration)
    {
        _cachingService = cachingService;
        _performanceService = performanceService;
        _logger = logger;
        _configuration = configuration;
        _activeTraces = new ConcurrentDictionary<string, TraceDto>();
        _activeAlerts = new ConcurrentDictionary<Guid, AlertDto>();
        _logBuffer = new ConcurrentQueue<LogEntryDto>();
    }

    public async Task<HealthCheckResultDto> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var healthCheck = new HealthCheckResultDto
            {
                CheckedAt = DateTime.UtcNow,
                OverallStatus = "Healthy"
            };

            var checks = new List<Task<HealthCheckItemDto>>
            {
                CheckDatabaseHealthAsync(cancellationToken),
                CheckCacheHealthAsync(cancellationToken),
                CheckExternalServicesHealthAsync(cancellationToken),
                CheckSystemResourcesHealthAsync(cancellationToken)
            };

            var results = await Task.WhenAll(checks);
            healthCheck.Checks = results.ToList();

            // Determine overall status
            if (results.Any(r => r.Status == "Critical"))
            {
                healthCheck.OverallStatus = "Critical";
            }
            else if (results.Any(r => r.Status == "Warning"))
            {
                healthCheck.OverallStatus = "Warning";
            }

            // Cache health check results
            await _cachingService.SetAsync("health_check", healthCheck, TimeSpan.FromMinutes(1));

            return healthCheck;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing health check");
            return new HealthCheckResultDto
            {
                CheckedAt = DateTime.UtcNow,
                OverallStatus = "Critical",
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<ServiceStatusDto>> GetServiceStatusesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var services = new List<ServiceStatusDto>
            {
                await CheckServiceStatusAsync("MobileWorkflow.API", "http://localhost:5000/health"),
                await CheckServiceStatusAsync("UserManagement.API", "http://localhost:5001/health"),
                await CheckServiceStatusAsync("OrderManagement.API", "http://localhost:5002/health"),
                await CheckServiceStatusAsync("TripManagement.API", "http://localhost:5003/health"),
                await CheckServiceStatusAsync("Redis", "redis://localhost:6379"),
                await CheckServiceStatusAsync("Database", "Server=localhost;Database=MobileWorkflow;")
            };

            return services;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service statuses");
            return new List<ServiceStatusDto>();
        }
    }

    public async Task<MetricsCollectionDto> CollectMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = new MetricsCollectionDto
            {
                CollectedAt = DateTime.UtcNow
            };

            // System metrics
            var process = Process.GetCurrentProcess();
            metrics.SystemMetrics = new Dictionary<string, double>
            {
                ["cpu_usage_percent"] = GetCpuUsage(),
                ["memory_usage_mb"] = process.WorkingSet64 / (1024 * 1024),
                ["thread_count"] = process.Threads.Count,
                ["handle_count"] = process.HandleCount,
                ["gc_gen0_collections"] = GC.CollectionCount(0),
                ["gc_gen1_collections"] = GC.CollectionCount(1),
                ["gc_gen2_collections"] = GC.CollectionCount(2)
            };

            // Application metrics
            metrics.ApplicationMetrics = new Dictionary<string, double>
            {
                ["active_traces"] = _activeTraces.Count,
                ["active_alerts"] = _activeAlerts.Count,
                ["log_buffer_size"] = _logBuffer.Count,
                ["cache_hit_rate"] = await GetCacheHitRateAsync()
            };

            // Business metrics (simulated)
            metrics.BusinessMetrics = new Dictionary<string, double>
            {
                ["active_users"] = Random.Shared.Next(100, 1000),
                ["workflows_executed"] = Random.Shared.Next(50, 500),
                ["forms_submitted"] = Random.Shared.Next(20, 200),
                ["notifications_sent"] = Random.Shared.Next(100, 1000)
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting metrics");
            return new MetricsCollectionDto { CollectedAt = DateTime.UtcNow };
        }
    }

    public async Task<AlertDto> CreateAlertAsync(CreateAlertRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = new AlertDto
            {
                Id = Guid.NewGuid(),
                Title = request.Title,
                Description = request.Description,
                Severity = request.Severity,
                Source = request.Source,
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Properties = request.Properties ?? new Dictionary<string, object>()
            };

            _activeAlerts.TryAdd(alert.Id, alert);

            // Cache alert for quick access
            await _cachingService.SetAsync($"alert:{alert.Id}", alert, TimeSpan.FromHours(24));

            _logger.LogWarning("Alert created: {Title} ({Severity}) - {Description}", 
                alert.Title, alert.Severity, alert.Description);

            return alert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert");
            throw;
        }
    }

    public async Task<List<AlertDto>> GetActiveAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return _activeAlerts.Values.Where(a => a.IsActive).OrderByDescending(a => a.CreatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return new List<AlertDto>();
        }
    }

    public async Task<bool> ResolveAlertAsync(Guid alertId, string resolvedBy, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.IsActive = false;
                alert.ResolvedAt = DateTime.UtcNow;
                alert.ResolvedBy = resolvedBy;

                await _cachingService.SetAsync($"alert:{alertId}", alert, TimeSpan.FromHours(24));

                _logger.LogInformation("Alert resolved: {AlertId} by {ResolvedBy}", alertId, resolvedBy);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert {AlertId}", alertId);
            return false;
        }
    }

    public async Task<DashboardDto> GetDashboardDataAsync(string dashboardType, CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = new DashboardDto
            {
                DashboardType = dashboardType,
                GeneratedAt = DateTime.UtcNow
            };

            switch (dashboardType.ToLowerInvariant())
            {
                case "overview":
                    dashboard = await GetOverviewDashboardAsync(cancellationToken);
                    break;
                case "performance":
                    dashboard = await GetPerformanceDashboardAsync(cancellationToken);
                    break;
                case "security":
                    dashboard = await GetSecurityDashboardAsync(cancellationToken);
                    break;
                case "business":
                    dashboard = await GetBusinessDashboardAsync(cancellationToken);
                    break;
                default:
                    dashboard.Widgets = new List<DashboardWidgetDto>();
                    break;
            }

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard data for type: {DashboardType}", dashboardType);
            return new DashboardDto
            {
                DashboardType = dashboardType,
                GeneratedAt = DateTime.UtcNow,
                Widgets = new List<DashboardWidgetDto>()
            };
        }
    }

    public async Task<List<LogEntryDto>> GetLogsAsync(GetLogsRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var logs = new List<LogEntryDto>();
            
            // Get logs from buffer
            var bufferLogs = _logBuffer.ToArray()
                .Where(l => l.Timestamp >= (request.FromDate ?? DateTime.UtcNow.AddHours(-24)))
                .Where(l => string.IsNullOrEmpty(request.Level) || l.Level.Equals(request.Level, StringComparison.OrdinalIgnoreCase))
                .Where(l => string.IsNullOrEmpty(request.Source) || l.Source.Contains(request.Source, StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(l => l.Timestamp)
                .Take(request.PageSize ?? 100);

            logs.AddRange(bufferLogs);

            // In a real implementation, you would also query persistent log storage
            // For now, we'll simulate some additional logs
            if (logs.Count < (request.PageSize ?? 100))
            {
                var simulatedLogs = GenerateSimulatedLogs(request);
                logs.AddRange(simulatedLogs);
            }

            return logs.Take(request.PageSize ?? 100).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting logs");
            return new List<LogEntryDto>();
        }
    }

    public async Task<TraceDto> StartTraceAsync(string operationName, Dictionary<string, object>? properties = null)
    {
        try
        {
            var trace = new TraceDto
            {
                TraceId = Guid.NewGuid().ToString(),
                OperationName = operationName,
                StartTime = DateTime.UtcNow,
                Properties = properties ?? new Dictionary<string, object>(),
                Spans = new List<TraceSpanDto>()
            };

            _activeTraces.TryAdd(trace.TraceId, trace);

            _logger.LogDebug("Started trace: {OperationName} ({TraceId})", operationName, trace.TraceId);

            return trace;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting trace for operation: {OperationName}", operationName);
            throw;
        }
    }

    public async Task CompleteTraceAsync(string traceId, bool isSuccess = true, string? errorMessage = null)
    {
        try
        {
            if (_activeTraces.TryRemove(traceId, out var trace))
            {
                trace.EndTime = DateTime.UtcNow;
                trace.Duration = trace.EndTime.Value - trace.StartTime;
                trace.IsSuccess = isSuccess;
                trace.ErrorMessage = errorMessage;

                // Cache completed trace
                await _cachingService.SetAsync($"trace:{traceId}", trace, TimeSpan.FromHours(24));

                _logger.LogDebug("Completed trace: {OperationName} ({TraceId}) - Duration: {Duration}ms, Success: {IsSuccess}",
                    trace.OperationName, traceId, trace.Duration?.TotalMilliseconds, isSuccess);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing trace: {TraceId}", traceId);
        }
    }

    public async Task<List<TraceDto>> GetTracesAsync(string? operationName = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var traces = new List<TraceDto>();
            var from = fromDate ?? DateTime.UtcNow.AddHours(-24);

            // Get active traces
            var activeTraces = _activeTraces.Values
                .Where(t => t.StartTime >= from)
                .Where(t => string.IsNullOrEmpty(operationName) || t.OperationName.Contains(operationName, StringComparison.OrdinalIgnoreCase));

            traces.AddRange(activeTraces);

            // In a real implementation, you would also query completed traces from storage
            // For now, we'll return just the active traces

            return traces.OrderByDescending(t => t.StartTime).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting traces");
            return new List<TraceDto>();
        }
    }

    // Helper methods
    private async Task<HealthCheckItemDto> CheckDatabaseHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Simulate database health check
            await Task.Delay(50, cancellationToken);
            
            return new HealthCheckItemDto
            {
                Name = "Database",
                Status = "Healthy",
                ResponseTime = TimeSpan.FromMilliseconds(45),
                Details = new Dictionary<string, object>
                {
                    ["connection_pool_size"] = 10,
                    ["active_connections"] = 3,
                    ["last_backup"] = DateTime.UtcNow.AddHours(-6)
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckItemDto
            {
                Name = "Database",
                Status = "Critical",
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<HealthCheckItemDto> CheckCacheHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var testKey = "health_check_test";
            await _cachingService.SetAsync(testKey, "test_value", TimeSpan.FromMinutes(1));
            var value = await _cachingService.GetAsync<string>(testKey);
            await _cachingService.RemoveAsync(testKey);

            return new HealthCheckItemDto
            {
                Name = "Cache",
                Status = value == "test_value" ? "Healthy" : "Warning",
                ResponseTime = TimeSpan.FromMilliseconds(25),
                Details = new Dictionary<string, object>
                {
                    ["hit_rate"] = await GetCacheHitRateAsync(),
                    ["memory_usage"] = "45%"
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckItemDto
            {
                Name = "Cache",
                Status = "Critical",
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<HealthCheckItemDto> CheckExternalServicesHealthAsync(CancellationToken cancellationToken)
    {
        // Simulate external service health check
        await Task.Delay(100, cancellationToken);
        
        return new HealthCheckItemDto
        {
            Name = "External Services",
            Status = "Healthy",
            ResponseTime = TimeSpan.FromMilliseconds(95),
            Details = new Dictionary<string, object>
            {
                ["notification_service"] = "Healthy",
                ["payment_gateway"] = "Healthy",
                ["mapping_service"] = "Warning"
            }
        };
    }

    private async Task<HealthCheckItemDto> CheckSystemResourcesHealthAsync(CancellationToken cancellationToken)
    {
        await Task.Delay(25, cancellationToken);
        
        var process = Process.GetCurrentProcess();
        var memoryMB = process.WorkingSet64 / (1024 * 1024);
        
        return new HealthCheckItemDto
        {
            Name = "System Resources",
            Status = memoryMB > 500 ? "Warning" : "Healthy",
            ResponseTime = TimeSpan.FromMilliseconds(20),
            Details = new Dictionary<string, object>
            {
                ["memory_mb"] = memoryMB,
                ["cpu_percent"] = GetCpuUsage(),
                ["disk_space_gb"] = 150.5
            }
        };
    }

    private async Task<ServiceStatusDto> CheckServiceStatusAsync(string serviceName, string endpoint)
    {
        try
        {
            // Simulate service health check
            await Task.Delay(Random.Shared.Next(50, 200));
            
            return new ServiceStatusDto
            {
                ServiceName = serviceName,
                Status = "Healthy",
                Endpoint = endpoint,
                LastChecked = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(Random.Shared.Next(50, 200)),
                Version = "1.0.0"
            };
        }
        catch (Exception ex)
        {
            return new ServiceStatusDto
            {
                ServiceName = serviceName,
                Status = "Critical",
                Endpoint = endpoint,
                LastChecked = DateTime.UtcNow,
                ErrorMessage = ex.Message
            };
        }
    }

    private double GetCpuUsage()
    {
        // Simplified CPU usage calculation
        return Random.Shared.NextDouble() * 50 + 10; // 10-60%
    }

    private async Task<double> GetCacheHitRateAsync()
    {
        // Simplified cache hit rate calculation
        return await Task.FromResult(Random.Shared.NextDouble() * 20 + 80); // 80-100%
    }

    private async Task<DashboardDto> GetOverviewDashboardAsync(CancellationToken cancellationToken)
    {
        var healthCheck = await CheckHealthAsync(cancellationToken);
        var metrics = await CollectMetricsAsync(cancellationToken);
        
        return new DashboardDto
        {
            DashboardType = "overview",
            GeneratedAt = DateTime.UtcNow,
            Widgets = new List<DashboardWidgetDto>
            {
                new() { Type = "health", Title = "System Health", Data = healthCheck },
                new() { Type = "metrics", Title = "Key Metrics", Data = metrics.SystemMetrics },
                new() { Type = "alerts", Title = "Active Alerts", Data = _activeAlerts.Values.Count(a => a.IsActive) }
            }
        };
    }

    private async Task<DashboardDto> GetPerformanceDashboardAsync(CancellationToken cancellationToken)
    {
        var systemHealth = await _performanceService.GetSystemHealthAsync(cancellationToken);
        
        return new DashboardDto
        {
            DashboardType = "performance",
            GeneratedAt = DateTime.UtcNow,
            Widgets = new List<DashboardWidgetDto>
            {
                new() { Type = "performance", Title = "System Performance", Data = systemHealth },
                new() { Type = "response_times", Title = "Response Times", Data = new { avg = 150.5, p95 = 250.2, p99 = 450.1 } },
                new() { Type = "throughput", Title = "Throughput", Data = new { rps = 45.2, peak = 89.5 } }
            }
        };
    }

    private async Task<DashboardDto> GetSecurityDashboardAsync(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new DashboardDto
        {
            DashboardType = "security",
            GeneratedAt = DateTime.UtcNow,
            Widgets = new List<DashboardWidgetDto>
            {
                new() { Type = "auth_attempts", Title = "Authentication Attempts", Data = new { successful = 1250, failed = 23 } },
                new() { Type = "security_alerts", Title = "Security Alerts", Data = new { critical = 0, warning = 2 } },
                new() { Type = "access_patterns", Title = "Access Patterns", Data = new { normal = 98.5, suspicious = 1.5 } }
            }
        });
    }

    private async Task<DashboardDto> GetBusinessDashboardAsync(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new DashboardDto
        {
            DashboardType = "business",
            GeneratedAt = DateTime.UtcNow,
            Widgets = new List<DashboardWidgetDto>
            {
                new() { Type = "active_users", Title = "Active Users", Data = Random.Shared.Next(500, 1500) },
                new() { Type = "workflows", Title = "Workflows Executed", Data = Random.Shared.Next(100, 500) },
                new() { Type = "revenue", Title = "Revenue", Data = new { daily = 15420.50, monthly = 456789.25 } }
            }
        });
    }

    private List<LogEntryDto> GenerateSimulatedLogs(GetLogsRequest request)
    {
        var logs = new List<LogEntryDto>();
        var levels = new[] { "Info", "Warning", "Error", "Debug" };
        var sources = new[] { "API", "Service", "Database", "Cache", "External" };

        for (int i = 0; i < 50; i++)
        {
            logs.Add(new LogEntryDto
            {
                Id = Guid.NewGuid(),
                Timestamp = DateTime.UtcNow.AddMinutes(-Random.Shared.Next(0, 1440)),
                Level = levels[Random.Shared.Next(levels.Length)],
                Source = sources[Random.Shared.Next(sources.Length)],
                Message = $"Simulated log message {i + 1}",
                Properties = new Dictionary<string, object>
                {
                    ["user_id"] = Guid.NewGuid(),
                    ["operation"] = "test_operation",
                    ["duration_ms"] = Random.Shared.Next(10, 1000)
                }
            });
        }

        return logs;
    }
}
