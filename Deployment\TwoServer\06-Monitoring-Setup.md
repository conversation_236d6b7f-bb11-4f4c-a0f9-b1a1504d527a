# Monitoring Setup Guide

## Overview

This guide provides comprehensive monitoring and observability setup for the TLI microservices deployment, including metrics collection, alerting, dashboards, and log aggregation.

## Monitoring Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Monitoring Stack                            │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 Grafana Dashboards                      │   │
│  │  • Infrastructure Metrics                               │   │
│  │  • Application Metrics                                  │   │
│  │  • Business Metrics                                     │   │
│  │  • Alert Management                                     │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Prometheus (Metrics)                       │   │
│  │  • Time Series Database                                 │   │
│  │  • Metrics Collection                                   │   │
│  │  • Alert Rules                                          │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │               Log Aggregation                           │   │
│  │  • Centralized Logging                                  │   │
│  │  • Log Analysis                                         │   │
│  │  • Error Tracking                                       │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Distributed Tracing                        │   │
│  │  • Request Tracing                                      │   │
│  │  • Performance Analysis                                 │   │
│  │  • Dependency Mapping                                   │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Step 1: Enhanced Prometheus Configuration

### 1.1 Advanced Prometheus Setup

#### Update Prometheus Configuration

```bash
# Create enhanced Prometheus configuration
cat > /opt/tli/config/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tli-production'
    environment: 'production'

rule_files:
  - "tli_rules.yml"
  - "infrastructure_rules.yml"
  - "application_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Infrastructure monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    scrape_interval: 30s

  # Node monitoring
  - job_name: 'node-exporter-server1'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 30s

  - job_name: 'node-exporter-server2'
    static_configs:
      - targets: ['SERVER2_IP:9100']
    scrape_interval: 30s

  # TLI microservices
  - job_name: 'tli-services'
    static_configs:
      - targets:
        - 'SERVER2_IP:5001'  # Identity Service
        - 'SERVER2_IP:5002'  # User Management
        - 'SERVER2_IP:5003'  # Subscription Management
        - 'SERVER2_IP:5004'  # Order Management
        - 'SERVER2_IP:5005'  # Trip Management
        - 'SERVER2_IP:5006'  # Network & Fleet
        - 'SERVER2_IP:5007'  # Financial & Payment
        - 'SERVER2_IP:5008'  # Communication
        - 'SERVER2_IP:5009'  # Analytics & BI
        - 'SERVER2_IP:5010'  # Data & Storage
        - 'SERVER2_IP:5011'  # Audit & Compliance
        - 'SERVER2_IP:5012'  # Monitoring
        - 'SERVER2_IP:5013'  # Mobile Workflow
    scrape_interval: 30s
    metrics_path: '/metrics'
    scrape_timeout: 10s

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['SERVER2_IP:5000']
    scrape_interval: 15s
    metrics_path: '/metrics'

  # Docker monitoring
  - job_name: 'docker-server1'
    static_configs:
      - targets: ['localhost:9323']
    scrape_interval: 30s

  - job_name: 'docker-server2'
    static_configs:
      - targets: ['SERVER2_IP:9323']
    scrape_interval: 30s

  # Nginx monitoring (if configured)
  - job_name: 'nginx'
    static_configs:
      - targets: ['SERVER2_IP:9113']
    scrape_interval: 30s
EOF
```

### 1.2 Advanced Alert Rules

#### Infrastructure Alert Rules

```bash
# Create infrastructure-specific alert rules
cat > /opt/tli/config/prometheus/infrastructure_rules.yml << 'EOF'
groups:
  - name: infrastructure_alerts
    rules:
      # PostgreSQL Alerts
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL has been down for more than 1 minute."

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL high connection usage"
          description: "PostgreSQL connection usage is above 80%"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "PostgreSQL query performance is degraded"

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is above 90%"

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high connection count"
          description: "Redis has more than 100 connected clients"

      # RabbitMQ Alerts
      - alert: RabbitMQDown
        expr: up{job="rabbitmq"} == 0
        for: 1m
        labels:
          severity: critical
          service: rabbitmq
        annotations:
          summary: "RabbitMQ is down"
          description: "RabbitMQ has been down for more than 1 minute."

      - alert: RabbitMQHighQueueSize
        expr: rabbitmq_queue_messages > 1000
        for: 5m
        labels:
          severity: warning
          service: rabbitmq
        annotations:
          summary: "RabbitMQ high queue size"
          description: "RabbitMQ queue has more than 1000 messages"

      # System Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"

      - alert: LowDiskSpace
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is above 85% for more than 5 minutes"
EOF
```

#### Application Alert Rules

```bash
# Create application-specific alert rules
cat > /opt/tli/config/prometheus/application_rules.yml << 'EOF'
groups:
  - name: application_alerts
    rules:
      # Service Health Alerts
      - alert: TLIServiceDown
        expr: up{job="tli-services"} == 0
        for: 2m
        labels:
          severity: critical
          service: "{{ $labels.instance }}"
        annotations:
          summary: "TLI Service {{ $labels.instance }} is down"
          description: "TLI Service {{ $labels.instance }} has been down for more than 2 minutes."

      - alert: APIGatewayDown
        expr: up{job="api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
          service: api-gateway
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway has been down for more than 1 minute."

      # Performance Alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 2 seconds"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for more than 5 minutes"

      # Business Logic Alerts
      - alert: HighFailedLogins
        expr: rate(failed_login_attempts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: identity
        annotations:
          summary: "High failed login attempts"
          description: "Failed login attempts rate is above 10 per minute"

      - alert: DatabaseConnectionPoolExhaustion
        expr: database_connections_active / database_connections_max * 100 > 90
        for: 2m
        labels:
          severity: critical
          service: "{{ $labels.service }}"
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Database connection pool usage is above 90%"
EOF
```

## Step 2: Node Exporter Setup

### 2.1 Install Node Exporter on Both Servers

#### Server 1 (Infrastructure) Node Exporter

```bash
# Download and install Node Exporter
cd /tmp
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz
sudo cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
sudo chown tli:tli /usr/local/bin/node_exporter

# Create systemd service
sudo tee /etc/systemd/system/node_exporter.service << 'EOF'
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=tli
Group=tli
Type=simple
ExecStart=/usr/local/bin/node_exporter \
  --web.listen-address=:9100 \
  --collector.systemd \
  --collector.processes \
  --collector.filesystem.ignored-mount-points="^/(dev|proc|sys|var/lib/docker/.+)($|/)" \
  --collector.filesystem.ignored-fs-types="^(autofs|binfmt_misc|bpf|cgroup2?|configfs|debugfs|devpts|devtmpfs|fusectl|hugetlbfs|iso9660|mqueue|nsfs|overlay|proc|procfs|pstore|rpc_pipefs|securityfs|selinuxfs|squashfs|sysfs|tracefs)$"

[Install]
WantedBy=multi-user.target
EOF

# Start and enable Node Exporter
sudo systemctl daemon-reload
sudo systemctl enable node_exporter
sudo systemctl start node_exporter

# Verify installation
curl http://localhost:9100/metrics | head -20
```

#### Server 2 (Applications) Node Exporter

```bash
# Same installation process as Server 1
# (Repeat the above commands on Server 2)

# Allow Node Exporter access from Server 1
sudo ufw allow from ********** to any port 9100 comment 'Node Exporter from Server 1'
```

## Step 3: Enhanced Grafana Configuration

### 3.1 Grafana Dashboard Setup

#### Create TLI Infrastructure Dashboard

```bash
# Create Grafana dashboard configuration
mkdir -p /opt/tli/config/grafana/dashboards

cat > /opt/tli/config/grafana/dashboards/tli-infrastructure.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "TLI Infrastructure Monitoring",
    "tags": ["tli", "infrastructure"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "System Overview",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=~\"postgres|redis|rabbitmq\"}",
            "legendFormat": "{{job}} Status"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "CPU Usage",
        "type": "timeseries",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "{{instance}} CPU Usage"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "max": 100,
            "min": 0
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Memory Usage",
        "type": "timeseries",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "{{instance}} Memory Usage"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "max": 100,
            "min": 0
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "PostgreSQL Connections",
        "type": "timeseries",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "{{datname}} Connections"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOF
```

#### Create TLI Applications Dashboard

```bash
cat > /opt/tli/config/grafana/dashboards/tli-applications.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "TLI Applications Monitoring",
    "tags": ["tli", "applications"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Service Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"tli-services\"}",
            "legendFormat": "{{instance}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{service}} - {{method}} {{status}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 3,
        "title": "Response Time (95th percentile)",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "{{service}} Response Time"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      },
      {
        "id": 4,
        "title": "Error Rate",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "{{service}} Error Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOF
```

### 3.2 Grafana Provisioning

#### Configure Dashboard Provisioning

```bash
# Create dashboard provisioning configuration
cat > /opt/tli/config/grafana/provisioning/dashboards/dashboards.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'TLI Dashboards'
    orgId: 1
    folder: 'TLI'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

# Create datasource provisioning
cat > /opt/tli/config/grafana/provisioning/datasources/datasources.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "30s"
      queryTimeout: "60s"
      httpMethod: "POST"

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
EOF
```

## Next Steps

1. **Deploy monitoring stack** using the provided configurations
2. **Configure dashboards** in Grafana for your specific needs
3. **Set up alerting** with appropriate notification channels
4. **Test monitoring** with the health check scripts
5. **Train team** on using monitoring tools and responding to alerts

## Monitoring Access

### Service URLs

- **Grafana Dashboard**: http://SERVER1_IP:3000 (admin/TLI_Grafana_2024!@#)
- **Prometheus Metrics**: http://SERVER1_IP:9090
- **Alertmanager**: http://SERVER1_IP:9093
- **Loki Logs**: http://SERVER1_IP:3100

### Key Management Commands

```bash
# Start monitoring stack
/opt/tli/manage-monitoring.sh start

# Check monitoring health
/opt/tli/monitoring-health-check.sh

# Reload Prometheus config
/opt/tli/manage-monitoring.sh reload

# View monitoring logs
/opt/tli/manage-monitoring.sh logs [service]
```

## Troubleshooting

### Common Issues

- **Prometheus targets down**: Check network connectivity and firewall rules
- **Grafana dashboard not loading**: Verify datasource configuration
- **Missing metrics**: Check service instrumentation and scrape configuration
- **Alerts not firing**: Verify alert rules and Alertmanager configuration
