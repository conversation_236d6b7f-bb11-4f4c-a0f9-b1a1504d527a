using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence;

public class DashboardWidgetConfiguration : IEntityTypeConfiguration<DashboardWidget>
{
    public void Configure(EntityTypeBuilder<DashboardWidget> builder)
    {
        builder.ToTable("dashboard_widgets", "analytics");

        builder.<PERSON><PERSON><PERSON>(w => w.Id);

        builder.Property(w => w.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(w => w.Title)
            .HasColumnName("title")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(w => w.WidgetType)
            .HasColumnName("widget_type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(w => w.Position)
            .HasColumnName("position")
            .IsRequired();

        builder.Property(w => w.Width)
            .HasColumnName("width")
            .IsRequired();

        builder.Property(w => w.Height)
            .HasColumnName("height")
            .IsRequired();

        builder.Property(w => w.Configuration)
            .HasColumnName("configuration")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(w => w.IsVisible)
            .HasColumnName("is_visible")
            .IsRequired();

        builder.Property(w => w.DataSource)
            .HasColumnName("data_source")
            .HasMaxLength(200);

        builder.Property(w => w.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(w => w.UpdatedAt)
            .HasColumnName("updated_at");

        // Foreign key to Dashboard
        builder.Property<Guid>("DashboardId")
            .HasColumnName("dashboard_id")
            .IsRequired();

        // Indexes
        builder.HasIndex("DashboardId")
            .HasDatabaseName("IX_dashboard_widgets_dashboard_id");

        builder.HasIndex(w => w.Position)
            .HasDatabaseName("IX_dashboard_widgets_position");
    }
}

public class ReportConfiguration : IEntityTypeConfiguration<Report>
{
    public void Configure(EntityTypeBuilder<Report> builder)
    {
        builder.ToTable("reports", "analytics");

        builder.HasKey(r => r.Id);

        builder.Property(r => r.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(r => r.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(r => r.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(r => r.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.GeneratedBy)
            .HasColumnName("generated_by")
            .IsRequired();

        builder.Property(r => r.UserType)
            .HasColumnName("user_type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.Parameters)
            .HasColumnName("parameters")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(r => r.Configuration)
            .HasColumnName("configuration")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(r => r.GeneratedAt)
            .HasColumnName("generated_at")
            .IsRequired();

        builder.Property(r => r.ExpiresAt)
            .HasColumnName("expires_at");

        builder.Property(r => r.IsScheduled)
            .HasColumnName("is_scheduled")
            .IsRequired();

        builder.Property(r => r.ScheduleCron)
            .HasColumnName("schedule_cron")
            .HasMaxLength(100);

        builder.Property(r => r.AccessCount)
            .HasColumnName("access_count")
            .IsRequired();

        builder.Property(r => r.LastAccessedAt)
            .HasColumnName("last_accessed_at");

        builder.Property(r => r.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(r => r.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasMany(r => r.Sections)
            .WithOne()
            .HasForeignKey("ReportId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.Exports)
            .WithOne()
            .HasForeignKey("ReportId")
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(r => new { r.GeneratedBy, r.Type })
            .HasDatabaseName("IX_reports_generated_by_type");

        builder.HasIndex(r => r.GeneratedAt)
            .HasDatabaseName("IX_reports_generated_at");

        builder.HasIndex(r => r.IsScheduled)
            .HasDatabaseName("IX_reports_is_scheduled");
    }
}

public class ReportSectionConfiguration : IEntityTypeConfiguration<ReportSection>
{
    public void Configure(EntityTypeBuilder<ReportSection> builder)
    {
        builder.ToTable("report_sections", "analytics");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(s => s.Title)
            .HasColumnName("title")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(s => s.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(s => s.Order)
            .HasColumnName("order")
            .IsRequired();

        builder.Property(s => s.SectionType)
            .HasColumnName("section_type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(s => s.Data)
            .HasColumnName("data")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(s => s.Configuration)
            .HasColumnName("configuration")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("jsonb");

        builder.Property(s => s.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(s => s.UpdatedAt)
            .HasColumnName("updated_at");

        // Foreign key to Report
        builder.Property<Guid>("ReportId")
            .HasColumnName("report_id")
            .IsRequired();

        // Indexes
        builder.HasIndex("ReportId")
            .HasDatabaseName("IX_report_sections_report_id");

        builder.HasIndex(s => s.Order)
            .HasDatabaseName("IX_report_sections_order");
    }
}

public class ReportExportConfiguration : IEntityTypeConfiguration<ReportExport>
{
    public void Configure(EntityTypeBuilder<ReportExport> builder)
    {
        builder.ToTable("report_exports", "analytics");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(e => e.ReportId)
            .HasColumnName("report_id")
            .IsRequired();

        builder.Property(e => e.Format)
            .HasColumnName("format")
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.ExportedBy)
            .HasColumnName("exported_by")
            .IsRequired();

        builder.Property(e => e.ExportedAt)
            .HasColumnName("exported_at")
            .IsRequired();

        builder.Property(e => e.FileName)
            .HasColumnName("file_name")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.FileSizeBytes)
            .HasColumnName("file_size_bytes")
            .IsRequired();

        builder.Property(e => e.Data)
            .HasColumnName("data")
            .IsRequired();

        builder.Property(e => e.ContentType)
            .HasColumnName("content_type")
            .HasMaxLength(100);

        builder.Property(e => e.ExpiresAt)
            .HasColumnName("expires_at");

        builder.Property(e => e.DownloadCount)
            .HasColumnName("download_count")
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at");

        // Indexes
        builder.HasIndex(e => e.ReportId)
            .HasDatabaseName("IX_report_exports_report_id");

        builder.HasIndex(e => e.ExportedAt)
            .HasDatabaseName("IX_report_exports_exported_at");
    }
}
