using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Services;

public interface IFeatureFlagService
{
    // Feature flag evaluation
    Task<bool> IsFeatureEnabledAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null);
    Task<T?> GetFeatureValueAsync<T>(string featureKey, Guid userId, T defaultValue, Dictionary<string, object>? context = null);
    Task<string?> GetFeatureVariantAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null);
    
    // A/B Testing
    Task<string?> GetABTestVariantAsync(string testKey, Guid userId, Dictionary<string, object>? context = null);
    Task RecordConversionAsync(string featureKey, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null);
    
    // Feature flag management
    Task<FeatureFlag> CreateFeatureFlagAsync(string name, string description, string key, FeatureFlagType type, string? defaultValue = null);
    Task<FeatureFlag> UpdateFeatureFlagAsync(Guid id, string? name = null, string? description = null, FeatureFlagStatus? status = null);
    Task ActivateFeatureFlagAsync(Guid id, DateTime? startDate = null, DateTime? endDate = null);
    Task DeactivateFeatureFlagAsync(Guid id);
    Task ArchiveFeatureFlagAsync(Guid id);
    
    // Gradual rollout
    Task UpdateRolloutPercentageAsync(Guid id, int percentage);
    Task<List<Guid>> GetUsersInRolloutAsync(Guid featureFlagId, int sampleSize = 100);
    
    // Rules management
    Task AddRuleAsync(Guid featureFlagId, string name, string description, FeatureFlagRuleType type, string condition, int priority = 0);
    Task UpdateRuleAsync(Guid ruleId, string? condition = null, bool? isActive = null, int? priority = null);
    Task RemoveRuleAsync(Guid featureFlagId, Guid ruleId);
    
    // Analytics and reporting
    Task<Dictionary<string, object>> GetFeatureFlagAnalyticsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, object>> GetABTestResultsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<List<FeatureFlag>> GetFeatureFlagsForUserAsync(Guid userId, Dictionary<string, object>? context = null);
    
    // Bulk operations
    Task<List<FeatureFlag>> GetAllFeatureFlagsAsync();
    Task<List<FeatureFlag>> GetActiveFeatureFlagsAsync();
    Task BulkUpdateStatusAsync(List<Guid> featureFlagIds, FeatureFlagStatus status);
}
