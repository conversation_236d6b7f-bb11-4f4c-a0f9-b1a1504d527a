using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Infrastructure.Persistence;
using SubscriptionManagement.Infrastructure.Repositories;
using SubscriptionManagement.Infrastructure.Services;

namespace SubscriptionManagement.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Database
            services.AddDbContext<SubscriptionDbContext>(options =>
                options.UseNpgsql(
                    configuration.GetConnectionString("DefaultConnection"),
                    b => b.MigrationsAssembly(typeof(SubscriptionDbContext).Assembly.FullName)));

            // Cache Service
            services.AddScoped<ISubscriptionCacheService, SubscriptionCacheService>();

            // Base Repositories
            services.AddScoped<SubscriptionRepository>();
            services.AddScoped<PlanRepository>();
            services.AddScoped<IPaymentRepository, PaymentRepository>();
            services.AddScoped<IFeatureFlagRepository, FeatureFlagRepository>();
            services.AddScoped<IUsageRecordRepository, UsageRecordRepository>();
            services.AddScoped<ISubscriptionPaymentProofRepository, SubscriptionPaymentProofRepository>();
            services.AddScoped<INotificationTemplateRepository, NotificationTemplateRepository>();
            services.AddScoped<INotificationHistoryRepository, NotificationHistoryRepository>();

            // Tax-related repositories
            services.AddScoped<ITaxCategoryRepository, TaxCategoryRepository>();
            services.AddScoped<ITaxExemptionRepository, TaxExemptionRepository>();
            services.AddScoped<IGlobalTaxConfigurationRepository, GlobalTaxConfigurationRepository>();
            services.AddScoped<IPlanTaxConfigurationRepository, PlanTaxConfigurationRepository>();

            // Cached Repositories (decorators)
            services.AddScoped<ISubscriptionRepository>(provider =>
            {
                var baseRepo = provider.GetRequiredService<SubscriptionRepository>();
                var cacheService = provider.GetRequiredService<ISubscriptionCacheService>();
                var logger = provider.GetRequiredService<ILogger<CachedSubscriptionRepository>>();
                return new CachedSubscriptionRepository(baseRepo, cacheService, logger);
            });

            services.AddScoped<IPlanRepository>(provider =>
            {
                var baseRepo = provider.GetRequiredService<PlanRepository>();
                var cacheService = provider.GetRequiredService<ISubscriptionCacheService>();
                var logger = provider.GetRequiredService<ILogger<CachedPlanRepository>>();
                return new CachedPlanRepository(baseRepo, cacheService, logger);
            });

            // Services
            services.AddScoped<IPaymentService, RazorPayPaymentService>();
            services.AddScoped<IFeatureFlagService, FeatureFlagService>();
            services.AddScoped<IUsageTrackingService, UsageTrackingService>();
            services.AddSingleton<ISubscriptionMetricsService, SubscriptionMetricsService>();
            services.AddScoped<IPaymentProofFileService, PaymentProofFileService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<INotificationTemplateService, NotificationTemplateService>();
            services.AddScoped<INotificationTrackingService, NotificationTrackingService>();
            services.AddScoped<IRetryPolicyService, RetryPolicyService>();
            services.AddSingleton<IMetricsService, MetricsService>();

            // Tax-related services
            services.AddScoped<ITaxCalculationService, TaxCalculationService>();

            // Communication Service
            services.AddHttpClient<ICommunicationService, CommunicationService>();

            // Security
            services.AddSubscriptionSecurity(configuration);

            // Monitoring
            services.AddSubscriptionHealthChecks();

            // Add memory cache for feature flags
            services.AddMemoryCache();

            // Health Checks
            services.AddHealthChecks()
                .AddCheck<SubscriptionManagement.Infrastructure.HealthChecks.SubscriptionHealthCheck>("subscription_service");

            return services;
        }
    }
}
