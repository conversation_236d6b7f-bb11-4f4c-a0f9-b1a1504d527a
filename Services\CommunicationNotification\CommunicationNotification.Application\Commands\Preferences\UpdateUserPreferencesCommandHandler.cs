using CommunicationNotification.Application.Common;
using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Application.Commands.Preferences;

/// <summary>
/// Handler for updating user communication preferences
/// </summary>
public class UpdateUserPreferencesCommandHandler : ICommandHandler<UpdateUserPreferencesCommand, CommandResult<UserPreferencesResult>>
{
    private readonly IUserPreferencesService _userPreferencesService;
    private readonly ICommunicationAuditService _auditService;
    private readonly ILogger<UpdateUserPreferencesCommandHandler> _logger;

    public UpdateUserPreferencesCommandHandler(
        IUserPreferencesService userPreferencesService,
        ICommunicationAuditService auditService,
        ILogger<UpdateUserPreferencesCommandHandler> logger)
    {
        _userPreferencesService = userPreferencesService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<CommandResult<UserPreferencesResult>> Handle(
        UpdateUserPreferencesCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing update user preferences command {CommandId} for user {UserId}",
                request.CommandId, request.UserId);

            // Validate command
            var validationResult = await ValidateCommandAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return CommandResult<UserPreferencesResult>.ValidationFailure(
                    validationResult.ValidationErrors, request.CommandId);
            }

            // Get current preferences
            var currentPreferences = await _userPreferencesService.GetUserPreferencesAsync(request.UserId, cancellationToken);

            // Update preferences
            var updateRequest = new UserPreferencesUpdateRequest
            {
                UserId = request.UserId,
                ChannelPreferences = request.ChannelPreferences,
                LanguagePreferences = request.LanguagePreferences,
                DeliveryPreferences = request.DeliveryPreferences,
                PrivacyPreferences = request.PrivacyPreferences,
                CustomPreferences = request.CustomPreferences
            };

            var updateResult = await _userPreferencesService.UpdateUserPreferencesAsync(updateRequest, cancellationToken);

            // Log audit event
            await LogPreferencesUpdateAuditEventAsync(request, updateResult, currentPreferences, cancellationToken);

            // Create result
            var result = new UserPreferencesResult
            {
                UserId = request.UserId,
                IsSuccess = updateResult.IsSuccess,
                ErrorMessage = updateResult.ErrorMessage,
                UpdatedAt = DateTime.UtcNow,
                UpdatedFields = updateResult.UpdatedFields
            };

            _logger.LogInformation("Update user preferences command {CommandId} completed for user {UserId}. Success: {Success}",
                request.CommandId, request.UserId, result.IsSuccess);

            return CommandResult<UserPreferencesResult>.Success(result, request.CommandId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process update user preferences command {CommandId}", request.CommandId);
            return CommandResult<UserPreferencesResult>.Failure(ex.Message, request.CommandId);
        }
    }

    private async Task<QueryValidationResult> ValidateCommandAsync(
        UpdateUserPreferencesCommand command,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (command.UserId == Guid.Empty)
            errors.Add("UserId is required");

        // Validate channel preferences
        if (command.ChannelPreferences.MessageTypeChannels.Any(kvp => !kvp.Value.Any()))
            errors.Add("Each message type must have at least one channel configured");

        // Validate language preferences
        if (command.LanguagePreferences.SecondaryLanguages.Contains(command.LanguagePreferences.PrimaryLanguage))
            errors.Add("Primary language cannot be included in secondary languages");

        // Validate delivery preferences
        if (command.DeliveryPreferences.MaxRetryAttempts < 0 || command.DeliveryPreferences.MaxRetryAttempts > 10)
            errors.Add("MaxRetryAttempts must be between 0 and 10");

        if (command.DeliveryPreferences.DeliveryTimeout < TimeSpan.FromMinutes(1) || 
            command.DeliveryPreferences.DeliveryTimeout > TimeSpan.FromHours(24))
            errors.Add("DeliveryTimeout must be between 1 minute and 24 hours");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }

    private async Task LogPreferencesUpdateAuditEventAsync(
        UpdateUserPreferencesCommand command,
        UserPreferencesUpdateResult updateResult,
        UserPreferences? currentPreferences,
        CancellationToken cancellationToken)
    {
        try
        {
            var auditEvent = new CommunicationAuditEvent
            {
                EventType = AuditEventType.UserPreferencesUpdated,
                UserId = command.UserId,
                EventData = new Dictionary<string, object>
                {
                    ["updatedFields"] = updateResult.UpdatedFields,
                    ["isSuccess"] = updateResult.IsSuccess,
                    ["commandId"] = command.CommandId,
                    ["hasPrivacyChanges"] = updateResult.UpdatedFields.Any(f => f.StartsWith("Privacy")),
                    ["hasChannelChanges"] = updateResult.UpdatedFields.Any(f => f.StartsWith("Channel")),
                    ["hasLanguageChanges"] = updateResult.UpdatedFields.Any(f => f.StartsWith("Language"))
                },
                Severity = AuditSeverity.Medium,
                ComplianceFlags = DetermineComplianceFlags(command, currentPreferences),
                CorrelationId = command.CorrelationId
            };

            await _auditService.LogCommunicationEventAsync(auditEvent, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log preferences update audit event for command {CommandId}", command.CommandId);
        }
    }

    private ComplianceFlag DetermineComplianceFlags(UpdateUserPreferencesCommand command, UserPreferences? currentPreferences)
    {
        var flags = ComplianceFlag.None;

        // Check for GDPR-related changes
        if (HasPrivacyPreferenceChanges(command, currentPreferences))
        {
            flags |= ComplianceFlag.GDPR;
        }

        // Check for consent changes
        if (HasConsentChanges(command, currentPreferences))
        {
            flags |= ComplianceFlag.ConsentRequired;
        }

        return flags;
    }

    private bool HasPrivacyPreferenceChanges(UpdateUserPreferencesCommand command, UserPreferences? currentPreferences)
    {
        if (currentPreferences == null) return true;

        return command.PrivacyPreferences.AllowDataCollection != currentPreferences.PrivacyPreferences.AllowDataCollection ||
               command.PrivacyPreferences.AllowThirdPartySharing != currentPreferences.PrivacyPreferences.AllowThirdPartySharing ||
               command.PrivacyPreferences.DataRetentionPreference != currentPreferences.PrivacyPreferences.DataRetentionPreference;
    }

    private bool HasConsentChanges(UpdateUserPreferencesCommand command, UserPreferences? currentPreferences)
    {
        if (currentPreferences == null) return true;

        return command.PrivacyPreferences.ConsentGiven.Any() || command.PrivacyPreferences.ConsentWithdrawn.Any();
    }
}

/// <summary>
/// Handler for updating channel preferences
/// </summary>
public class UpdateChannelPreferencesCommandHandler : ICommandHandler<UpdateChannelPreferencesCommand, CommandResult<ChannelPreferencesResult>>
{
    private readonly IUserPreferencesService _userPreferencesService;
    private readonly ICommunicationAuditService _auditService;
    private readonly ILogger<UpdateChannelPreferencesCommandHandler> _logger;

    public UpdateChannelPreferencesCommandHandler(
        IUserPreferencesService userPreferencesService,
        ICommunicationAuditService auditService,
        ILogger<UpdateChannelPreferencesCommandHandler> logger)
    {
        _userPreferencesService = userPreferencesService;
        _auditService = auditService;
        _logger = logger;
    }

    public async Task<CommandResult<ChannelPreferencesResult>> Handle(
        UpdateChannelPreferencesCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing update channel preferences command {CommandId} for user {UserId}, message type {MessageType}",
                request.CommandId, request.UserId, request.MessageType);

            // Validate command
            var validationResult = await ValidateCommandAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return CommandResult<ChannelPreferencesResult>.ValidationFailure(
                    validationResult.ValidationErrors, request.CommandId);
            }

            // Update channel preferences
            var updateRequest = new ChannelPreferencesUpdateRequest
            {
                UserId = request.UserId,
                MessageType = request.MessageType,
                PreferredChannels = request.PreferredChannels,
                DisabledChannels = request.DisabledChannels,
                ChannelSettings = request.ChannelSettings
            };

            var updateResult = await _userPreferencesService.UpdateChannelPreferencesAsync(updateRequest, cancellationToken);

            // Log audit event
            await LogChannelPreferencesAuditEventAsync(request, updateResult, cancellationToken);

            // Create result
            var result = new ChannelPreferencesResult
            {
                UserId = request.UserId,
                MessageType = request.MessageType,
                IsSuccess = updateResult.IsSuccess,
                ErrorMessage = updateResult.ErrorMessage,
                UpdatedAt = DateTime.UtcNow,
                UpdatedChannels = updateResult.UpdatedChannels
            };

            _logger.LogInformation("Update channel preferences command {CommandId} completed for user {UserId}. Success: {Success}",
                request.CommandId, request.UserId, result.IsSuccess);

            return CommandResult<ChannelPreferencesResult>.Success(result, request.CommandId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process update channel preferences command {CommandId}", request.CommandId);
            return CommandResult<ChannelPreferencesResult>.Failure(ex.Message, request.CommandId);
        }
    }

    private async Task<QueryValidationResult> ValidateCommandAsync(
        UpdateChannelPreferencesCommand command,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (command.UserId == Guid.Empty)
            errors.Add("UserId is required");

        if (!command.PreferredChannels.Any() && !command.DisabledChannels.Any())
            errors.Add("At least one preferred or disabled channel must be specified");

        // Check for conflicts between preferred and disabled channels
        var conflictingChannels = command.PreferredChannels.Intersect(command.DisabledChannels).ToList();
        if (conflictingChannels.Any())
            errors.Add($"Channels cannot be both preferred and disabled: {string.Join(", ", conflictingChannels)}");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }

    private async Task LogChannelPreferencesAuditEventAsync(
        UpdateChannelPreferencesCommand command,
        ChannelPreferencesUpdateResult updateResult,
        CancellationToken cancellationToken)
    {
        try
        {
            var auditEvent = new CommunicationAuditEvent
            {
                EventType = AuditEventType.ChannelPreferencesUpdated,
                UserId = command.UserId,
                EventData = new Dictionary<string, object>
                {
                    ["messageType"] = command.MessageType.ToString(),
                    ["preferredChannels"] = command.PreferredChannels.Select(c => c.ToString()).ToList(),
                    ["disabledChannels"] = command.DisabledChannels.Select(c => c.ToString()).ToList(),
                    ["updatedChannels"] = updateResult.UpdatedChannels.Select(c => c.ToString()).ToList(),
                    ["isSuccess"] = updateResult.IsSuccess,
                    ["commandId"] = command.CommandId
                },
                Severity = AuditSeverity.Low,
                CorrelationId = command.CorrelationId
            };

            await _auditService.LogCommunicationEventAsync(auditEvent, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log channel preferences audit event for command {CommandId}", command.CommandId);
        }
    }
}

// Supporting interfaces and classes that would be implemented in the Infrastructure layer
public interface IUserPreferencesService
{
    Task<UserPreferences?> GetUserPreferencesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<UserPreferencesUpdateResult> UpdateUserPreferencesAsync(UserPreferencesUpdateRequest request, CancellationToken cancellationToken = default);
    Task<ChannelPreferencesUpdateResult> UpdateChannelPreferencesAsync(ChannelPreferencesUpdateRequest request, CancellationToken cancellationToken = default);
}

public class UserPreferencesUpdateRequest
{
    public Guid UserId { get; set; }
    public NotificationChannelPreferences ChannelPreferences { get; set; } = new();
    public LanguagePreferences LanguagePreferences { get; set; } = new();
    public DeliveryPreferences DeliveryPreferences { get; set; } = new();
    public PrivacyPreferences PrivacyPreferences { get; set; } = new();
    public Dictionary<string, object> CustomPreferences { get; set; } = new();
}

public class ChannelPreferencesUpdateRequest
{
    public Guid UserId { get; set; }
    public MessageType MessageType { get; set; }
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public List<NotificationChannel> DisabledChannels { get; set; } = new();
    public Dictionary<NotificationChannel, ChannelSettings> ChannelSettings { get; set; } = new();
}

public class UserPreferencesUpdateResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> UpdatedFields { get; set; } = new();
}

public class ChannelPreferencesUpdateResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<NotificationChannel> UpdatedChannels { get; set; } = new();
}

public class UserPreferences
{
    public Guid UserId { get; set; }
    public NotificationChannelPreferences ChannelPreferences { get; set; } = new();
    public LanguagePreferences LanguagePreferences { get; set; } = new();
    public DeliveryPreferences DeliveryPreferences { get; set; } = new();
    public PrivacyPreferences PrivacyPreferences { get; set; } = new();
    public Dictionary<string, object> CustomPreferences { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

// Additional audit event types
public enum AuditEventType
{
    MessageSent,
    MessageDelivered,
    MessageRead,
    MessageFailed,
    UserLogin,
    UserLogout,
    ConversationCreated,
    ConversationJoined,
    ConversationLeft,
    EmergencyAlert,
    DataExport,
    DataDeletion,
    ConsentGiven,
    ConsentWithdrawn,
    MessageArchived,
    PolicyApplied,
    UserPreferencesUpdated,
    ChannelPreferencesUpdated
}
