using MediatR;

namespace OrderManagement.Application.Commands.RouteRfqToBroker;

public class RouteRfqToBrokerCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public Guid RoutedBy { get; set; }
    public string? RoutedByName { get; set; }
    public string? RoutingReason { get; set; }
    public string? RoutingNotes { get; set; }
    public DateTime? ResponseDeadline { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
