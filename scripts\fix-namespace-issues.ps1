# PowerShell script to fix namespace and class issues
Write-Host "Fixing namespace and class issues..." -ForegroundColor Green

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot

Write-Host "`n=== Step 1: Fix SharedKernel References ===" -ForegroundColor Cyan

# Find all files with SharedKernel references
$filesToFix = Get-ChildItem -Path "$solutionRoot\Services" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*bin*" -and $_.FullName -notlike "*obj*" 
}

foreach ($file in $filesToFix) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content -and $content -match "using SharedKernel") {
        Write-Host "  Fixing SharedKernel reference in $($file.Name)" -ForegroundColor White
        $content = $content -replace "using SharedKernel;", "using Shared.Domain.Common;"
        Set-Content $file.FullName $content -NoNewline
    }
}

Write-Host "`n=== Step 2: Fix Common Namespace References ===" -ForegroundColor Cyan

foreach ($file in $filesToFix) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content -and $content -match "\.Domain\.Common") {
        Write-Host "  Fixing Common namespace in $($file.Name)" -ForegroundColor White
        # Fix namespace references like FinancialPayment.Domain.Common
        $content = $content -replace "using [^.]+\.Domain\.Common;", "using Shared.Domain.Common;"
        Set-Content $file.FullName $content -NoNewline
    }
}

Write-Host "`n=== Step 3: Fix Missing Base Class References ===" -ForegroundColor Cyan

# Add missing using statements for Entity and ValueObject
foreach ($file in $filesToFix) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content) {
        $modified = $false
        
        # Check if file uses Entity but doesn't have the using statement
        if ($content -match ": Entity" -and $content -notmatch "using Shared\.Domain\.Common;") {
            if ($content -match "^using ") {
                $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.Common;"
            } else {
                $content = "using Shared.Domain.Common;`n$content"
            }
            $modified = $true
        }
        
        # Check if file uses ValueObject but doesn't have the using statement
        if ($content -match ": ValueObject" -and $content -notmatch "using Shared\.Domain\.ValueObjects;") {
            if ($content -match "^using ") {
                $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.ValueObjects;"
            } else {
                $content = "using Shared.Domain.ValueObjects;`n$content"
            }
            $modified = $true
        }
        
        if ($modified) {
            Set-Content $file.FullName $content -NoNewline
            Write-Host "  Added base class using statements to $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "`n=== Step 4: Fix Missing Type References ===" -ForegroundColor Cyan

# Fix specific missing types
$typeReplacements = @{
    "TaxCalculationResult" = "decimal"  # Simplify for now
    "CustomerDetails" = "string"       # Simplify for now
    "BillingDetails" = "string"        # Simplify for now
}

foreach ($file in $filesToFix) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content) {
        $modified = $false
        
        foreach ($type in $typeReplacements.Keys) {
            if ($content -match "\b$type\b") {
                $content = $content -replace "\b$type\b", $typeReplacements[$type]
                $modified = $true
            }
        }
        
        if ($modified) {
            Set-Content $file.FullName $content -NoNewline
            Write-Host "  Fixed missing type references in $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "`n=== Step 5: Remove Duplicate Classes ===" -ForegroundColor Cyan

# Fix duplicate EscrowTransaction in MilestoneEntities.cs
$milestoneFile = "$solutionRoot\Services\FinancialPayment\FinancialPayment.Domain\Entities\MilestoneEntities.cs"
if (Test-Path $milestoneFile) {
    Write-Host "  Fixing duplicate EscrowTransaction class..." -ForegroundColor White
    $content = Get-Content $milestoneFile -Raw
    
    # Find and remove duplicate class definition
    $lines = $content -split "`n"
    $newLines = @()
    $inDuplicateClass = $false
    $braceCount = 0
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        
        # Check if this is the start of a duplicate EscrowTransaction class
        if ($line -match "public class EscrowTransaction" -and $newLines -join "`n" -match "public class EscrowTransaction") {
            $inDuplicateClass = $true
            $braceCount = 0
            continue
        }
        
        if ($inDuplicateClass) {
            # Count braces to know when the class ends
            $braceCount += ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
            $braceCount -= ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
            
            if ($braceCount -le 0) {
                $inDuplicateClass = $false
            }
            continue
        }
        
        $newLines += $line
    }
    
    Set-Content $milestoneFile ($newLines -join "`n") -NoNewline
    Write-Host "    Removed duplicate EscrowTransaction class" -ForegroundColor Green
}

# Fix duplicate DashboardWidget in PaymentAnalytics.cs
$analyticsFile = "$solutionRoot\Services\FinancialPayment\FinancialPayment.Domain\Entities\PaymentAnalytics.cs"
if (Test-Path $analyticsFile) {
    Write-Host "  Fixing duplicate DashboardWidget class..." -ForegroundColor White
    $content = Get-Content $analyticsFile -Raw
    
    # Find and remove duplicate class definition
    $lines = $content -split "`n"
    $newLines = @()
    $inDuplicateClass = $false
    $braceCount = 0
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        
        # Check if this is the start of a duplicate DashboardWidget class
        if ($line -match "public class DashboardWidget" -and $newLines -join "`n" -match "public class DashboardWidget") {
            $inDuplicateClass = $true
            $braceCount = 0
            continue
        }
        
        if ($inDuplicateClass) {
            # Count braces to know when the class ends
            $braceCount += ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
            $braceCount -= ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
            
            if ($braceCount -le 0) {
                $inDuplicateClass = $false
            }
            continue
        }
        
        $newLines += $line
    }
    
    Set-Content $analyticsFile ($newLines -join "`n") -NoNewline
    Write-Host "    Removed duplicate DashboardWidget class" -ForegroundColor Green
}

Write-Host "`nNamespace and class issue fixes completed!" -ForegroundColor Green
Write-Host "Run 'dotnet build' to verify fixes..." -ForegroundColor Cyan
