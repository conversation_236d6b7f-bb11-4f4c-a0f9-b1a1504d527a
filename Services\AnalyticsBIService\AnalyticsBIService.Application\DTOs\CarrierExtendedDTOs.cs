using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Carrier financial performance data transfer object
/// </summary>
public class CarrierFinancialPerformanceDto
{
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal AverageRevenuePerTrip { get; set; }
    public decimal AverageRevenuePerMile { get; set; }
    public decimal FuelCosts { get; set; }
    public decimal MaintenanceCosts { get; set; }
    public decimal DriverCosts { get; set; }
    public decimal InsuranceCosts { get; set; }
    public List<MonthlyFinancialDto> MonthlyBreakdown { get; set; } = new();
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// Monthly financial data transfer object
/// </summary>
public class MonthlyFinancialDto
{
    public int Year { get; set; }
    public int Month { get; set; }
    public decimal Revenue { get; set; }
    public decimal Expenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
}

/// <summary>
/// Carrier customer satisfaction data transfer object
/// </summary>
public class CarrierCustomerSatisfactionDto
{
    public string CarrierId { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public decimal OnTimeDeliveryRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal ServiceQualityRating { get; set; }
    public decimal PricingRating { get; set; }
    public int TotalReviews { get; set; }
    public List<CustomerFeedbackDto> RecentFeedback { get; set; } = new();
    public List<RatingTrendDto> RatingTrends { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Customer feedback data transfer object
/// </summary>
public class CustomerFeedbackDto
{
    public string FeedbackId { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public string Comments { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string TripId { get; set; } = string.Empty;
}

/// <summary>
/// Rating trend data transfer object
/// </summary>
public class RatingTrendDto
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public int ReviewCount { get; set; }
}

/// <summary>
/// Carrier competitive benchmarking data transfer object
/// </summary>
public class CarrierCompetitiveBenchmarkingDto
{
    public string CarrierId { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal MarketRank { get; set; }
    public decimal MarketShare { get; set; }
    public List<BenchmarkMetricDto> PerformanceMetrics { get; set; } = new();
    public List<CompetitorComparisonDto> CompetitorComparisons { get; set; } = new();
    public List<MarketTrendDto> MarketTrends { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

// BenchmarkMetricDto is already defined in AdminAnalyticsDto.cs - using that instead

// CompetitorComparisonDto is already defined in AdminAnalyticsDto.cs - using that instead

// MarketTrendDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Carrier route performance data transfer object
/// </summary>
public class CarrierRoutePerformanceDto
{
    public string CarrierId { get; set; } = string.Empty;
    public string RouteId { get; set; } = string.Empty;
    public string RouteName { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal AverageTransitTime { get; set; }
    public decimal OnTimePerformance { get; set; }
    public decimal AverageRevenue { get; set; }
    public decimal AverageCost { get; set; }
    public decimal ProfitMargin { get; set; }
    public int TotalTrips { get; set; }
    public List<RoutePerformanceTrendDto> PerformanceTrends { get; set; } = new();

    // Date range properties for analytics
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Route performance trend data transfer object
/// </summary>
public class RoutePerformanceTrendDto
{
    public DateTime Date { get; set; }
    public decimal OnTimePerformance { get; set; }
    public decimal AverageTransitTime { get; set; }
    public decimal Revenue { get; set; }
    public int TripCount { get; set; }
}

/// <summary>
/// Carrier technology adoption data transfer object
/// </summary>
public class CarrierTechnologyAdoptionDto
{
    public string CarrierId { get; set; } = string.Empty;
    public List<TechnologyUsageDto> TechnologyUsage { get; set; } = new();
    public decimal DigitalMaturityScore { get; set; }
    public List<string> AdoptedTechnologies { get; set; } = new();
    public List<string> RecommendedTechnologies { get; set; } = new();
    public List<TechnologyBenefitDto> TechnologyBenefits { get; set; } = new();
    public DateTime LastAssessed { get; set; }
}

/// <summary>
/// Technology usage data transfer object
/// </summary>
public class TechnologyUsageDto
{
    public string TechnologyName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsAdopted { get; set; }
    public DateTime? AdoptionDate { get; set; }
    public decimal UsageLevel { get; set; } // 0-100%
    public decimal ROI { get; set; }
    public List<string> Benefits { get; set; } = new();
}

/// <summary>
/// Technology benefit data transfer object
/// </summary>
public class TechnologyBenefitDto
{
    public string TechnologyName { get; set; } = string.Empty;
    public string BenefitType { get; set; } = string.Empty;
    public decimal QuantifiedBenefit { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Carrier safety analytics data transfer object
/// </summary>
public class CarrierSafetyAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public decimal SafetyScore { get; set; }
    public int AccidentCount { get; set; }
    public decimal AccidentRate { get; set; }
    public int ViolationCount { get; set; }
    public decimal ViolationRate { get; set; }
    public List<SafetyIncidentDto> RecentIncidents { get; set; } = new();
    public List<SafetyMetricTrendDto> SafetyTrends { get; set; } = new();
    public List<string> SafetyRecommendations { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Safety incident data transfer object
/// </summary>
public class SafetyIncidentDto
{
    public string IncidentId { get; set; } = string.Empty;
    public string IncidentType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime IncidentDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public string DriverId { get; set; } = string.Empty;
    public string VehicleId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Safety metric trend data transfer object
/// </summary>
public class SafetyMetricTrendDto
{
    public DateTime Date { get; set; }
    public decimal SafetyScore { get; set; }
    public int IncidentCount { get; set; }
    public decimal AccidentRate { get; set; }
}

/// <summary>
/// Carrier vehicle utilization data transfer object
/// </summary>
public class CarrierVehicleUtilizationDto
{
    public string CarrierId { get; set; } = string.Empty;
    public int TotalVehicles { get; set; }
    public int ActiveVehicles { get; set; }
    public decimal UtilizationRate { get; set; }
    public decimal AverageIdleTime { get; set; }
    public decimal AverageMileage { get; set; }
    public List<VehicleUtilizationDto> VehicleDetails { get; set; } = new();
    public List<UtilizationTrendDto> UtilizationTrends { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Vehicle utilization data transfer object
/// </summary>
public class VehicleUtilizationDto
{
    public string VehicleId { get; set; } = string.Empty;
    public string VehicleType { get; set; } = string.Empty;
    public decimal UtilizationRate { get; set; }
    public decimal IdleTime { get; set; }
    public decimal Mileage { get; set; }
    public string Status { get; set; } = string.Empty;

    // Additional properties required by GetCarrierAnalyticsQueryHandler
    public decimal ActiveTime { get; set; }
    public decimal MaintenanceTime { get; set; }
    public List<UtilizationTrendDto> UtilizationTrends { get; set; } = new();
    public List<EfficiencyOpportunityDto> OptimizationOpportunities { get; set; } = new();
}

/// <summary>
/// Utilization trend data transfer object
/// </summary>
public class UtilizationTrendDto
{
    public DateTime Date { get; set; }
    public decimal UtilizationRate { get; set; }
    public decimal IdleTime { get; set; }
    public int ActiveVehicles { get; set; }

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public decimal VehicleUtilization { get; set; }
    public decimal DriverUtilization { get; set; }
    public decimal WarehouseUtilization { get; set; }
}

/// <summary>
/// Carrier operational insights data transfer object
/// </summary>
public class CarrierOperationalInsightsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public List<OperationalMetricDto> KeyMetrics { get; set; } = new();
    public List<EfficiencyOpportunityDto> EfficiencyOpportunities { get; set; } = new();
    public List<CostOptimizationDto> CostOptimizations { get; set; } = new();
    public List<PerformanceInsightDto> PerformanceInsights { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Operational metric data transfer object
/// </summary>
public class OperationalMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Efficiency opportunity data transfer object
/// </summary>
public class EfficiencyOpportunityDto
{
    public string OpportunityId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
}

/// <summary>
/// Cost optimization data transfer object
/// </summary>
public class CostOptimizationDto
{
    public string OptimizationId { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal CurrentCost { get; set; }
    public decimal OptimizedCost { get; set; }
    public decimal PotentialSavings { get; set; }
    public string Recommendation { get; set; } = string.Empty;
}

// PerformanceInsightDto is already defined in DriverAnalyticsDTOs.cs - using that instead

/// <summary>
/// Carrier market opportunities data transfer object
/// </summary>
public class CarrierMarketOpportunitiesDto
{
    public string CarrierId { get; set; } = string.Empty;
    public List<MarketOpportunityDto> Opportunities { get; set; } = new();
    public List<MarketSegmentDto> GrowthSegments { get; set; } = new();
    public List<GeographicOpportunityDto> GeographicOpportunities { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

// MarketOpportunityDto is already defined in AdminAnalyticsDto.cs - using that instead

// MarketSegmentDto is already defined in BrokerAnalyticsDTOs.cs - using that instead

/// <summary>
/// Geographic opportunity data transfer object
/// </summary>
public class GeographicOpportunityDto
{
    public string Region { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public decimal DemandLevel { get; set; }
    public decimal CompetitionLevel { get; set; }
    public decimal PotentialRevenue { get; set; }
    public List<string> KeyIndustries { get; set; } = new();
}

/// <summary>
/// Carrier partnership analytics data transfer object
/// </summary>
public class CarrierPartnershipAnalyticsDto
{
    public string CarrierId { get; set; } = string.Empty;
    public List<PartnershipPerformanceDto> Partnerships { get; set; } = new();
    public List<PartnershipOpportunityDto> Opportunities { get; set; } = new();
    public decimal PartnershipRevenue { get; set; }
    public decimal PartnershipGrowthRate { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Partnership performance data transfer object
/// </summary>
public class PartnershipPerformanceDto
{
    public string PartnerId { get; set; } = string.Empty;
    public string PartnerName { get; set; } = string.Empty;
    public string PartnershipType { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal SatisfactionScore { get; set; }
    public int CollaborationCount { get; set; }
    public DateTime PartnershipStart { get; set; }
}

// PartnershipOpportunityDto is already defined in AdminAnalyticsDto.cs - using that instead
