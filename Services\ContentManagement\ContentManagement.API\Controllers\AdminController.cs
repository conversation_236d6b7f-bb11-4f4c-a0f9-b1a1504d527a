using ContentManagement.Application.DTOs;
using ContentManagement.Application.Queries.Content;
using ContentManagement.Application.Queries.Faq;
using ContentManagement.Application.Queries.Policy;
using ContentManagement.Infrastructure.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ContentManagement.API.Controllers;

/// <summary>
/// Admin controller for content management dashboard and administrative functions
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,ContentManager")]
public class AdminController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IContentAnalyticsService _analyticsService;
    private readonly IContentPreviewService _previewService;
    private readonly ILogger<AdminController> _logger;

    public AdminController(
        IMediator mediator,
        IContentAnalyticsService analyticsService,
        IContentPreviewService previewService,
        ILogger<AdminController> logger)
    {
        _mediator = mediator;
        _analyticsService = analyticsService;
        _previewService = previewService;
        _logger = logger;
    }

    /// <summary>
    /// Get admin dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<AdminDashboardDto>> GetDashboard()
    {
        try
        {
            // Get content statistics
            var contentStats = await _mediator.Send(new GetContentStatisticsQuery());
            
            // Get FAQ analytics
            var faqAnalytics = await _mediator.Send(new GetFaqAnalyticsQuery());
            
            // Get policy analytics
            var policyAnalytics = await _mediator.Send(new GetPolicyAnalyticsQuery());
            
            // Get content analytics
            var contentAnalytics = await _analyticsService.GetContentAnalyticsAsync();
            
            // Get top performing content
            var topContent = await _analyticsService.GetTopPerformingContentAsync(5);
            
            // Get content trends
            var trends = await _analyticsService.GetContentTrendsAsync(30);
            
            // Get user engagement
            var engagement = await _analyticsService.GetUserEngagementAsync();

            var dashboard = new AdminDashboardDto
            {
                ContentStatistics = contentStats,
                FaqAnalytics = faqAnalytics,
                PolicyAnalytics = policyAnalytics,
                ContentAnalytics = contentAnalytics,
                TopPerformingContent = topContent,
                ContentTrends = trends,
                UserEngagement = engagement,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get admin dashboard data");
            return StatusCode(500, "Failed to load dashboard data");
        }
    }

    /// <summary>
    /// Get content management overview
    /// </summary>
    [HttpGet("content-overview")]
    public async Task<ActionResult<ContentOverviewDto>> GetContentOverview(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var contentStats = await _mediator.Send(new GetContentStatisticsQuery
            {
                FromDate = fromDate,
                ToDate = toDate
            });

            var pendingApproval = await _mediator.Send(new GetContentPendingApprovalQuery());
            
            var recentContent = await _mediator.Send(new SearchContentQuery
            {
                SortBy = "CreatedAt",
                SortDescending = true,
                PageSize = 10,
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            });

            var overview = new ContentOverviewDto
            {
                Statistics = contentStats,
                PendingApproval = pendingApproval,
                RecentContent = recentContent.Items.Take(10).ToList(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(overview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content overview");
            return StatusCode(500, "Failed to load content overview");
        }
    }

    /// <summary>
    /// Get content analytics
    /// </summary>
    [HttpGet("analytics")]
    public async Task<ActionResult<ContentAnalyticsDto>> GetAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var analytics = await _analyticsService.GetContentAnalyticsAsync(fromDate, toDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content analytics");
            return StatusCode(500, "Failed to load analytics data");
        }
    }

    /// <summary>
    /// Get content performance metrics
    /// </summary>
    [HttpGet("performance")]
    public async Task<ActionResult<List<ContentPerformanceDto>>> GetPerformanceMetrics(
        [FromQuery] int limit = 20)
    {
        try
        {
            var performance = await _analyticsService.GetTopPerformingContentAsync(limit);
            return Ok(performance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance metrics");
            return StatusCode(500, "Failed to load performance data");
        }
    }

    /// <summary>
    /// Get content trends
    /// </summary>
    [HttpGet("trends")]
    public async Task<ActionResult<List<ContentTrendDto>>> GetTrends(
        [FromQuery] int days = 30)
    {
        try
        {
            var trends = await _analyticsService.GetContentTrendsAsync(days);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content trends");
            return StatusCode(500, "Failed to load trends data");
        }
    }

    /// <summary>
    /// Get user engagement metrics
    /// </summary>
    [HttpGet("engagement")]
    public async Task<ActionResult<UserEngagementDto>> GetEngagement(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var engagement = await _analyticsService.GetUserEngagementAsync(fromDate, toDate);
            return Ok(engagement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user engagement metrics");
            return StatusCode(500, "Failed to load engagement data");
        }
    }

    /// <summary>
    /// Preview content
    /// </summary>
    [HttpGet("preview/{contentId:guid}")]
    public async Task<ActionResult<ContentPreviewDto>> PreviewContent(Guid contentId)
    {
        try
        {
            var content = await _mediator.Send(new GetContentByIdQuery
            {
                Id = contentId,
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            });

            if (content == null)
                return NotFound();

            // Convert DTO back to domain entity for preview service
            // In a real implementation, you'd get the domain entity directly
            var domainContent = new Domain.Entities.Content(
                content.Title,
                content.Slug,
                content.Type,
                content.ContentBody,
                content.CreatedBy,
                content.CreatedByName,
                content.Description,
                tags: content.Tags);

            var preview = await _previewService.GeneratePreviewAsync(domainContent);
            return Ok(preview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate content preview for {ContentId}", contentId);
            return StatusCode(500, "Failed to generate preview");
        }
    }

    /// <summary>
    /// Get content workflow status
    /// </summary>
    [HttpGet("workflow-status")]
    public async Task<ActionResult<WorkflowStatusDto>> GetWorkflowStatus()
    {
        try
        {
            var pendingReview = await _mediator.Send(new GetContentPendingApprovalQuery());
            
            var draftContent = await _mediator.Send(new SearchContentQuery
            {
                Status = Domain.Enums.ContentStatus.Draft,
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            });

            var rejectedContent = await _mediator.Send(new SearchContentQuery
            {
                Status = Domain.Enums.ContentStatus.Rejected,
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            });

            var workflowStatus = new WorkflowStatusDto
            {
                PendingReview = pendingReview.TotalCount,
                DraftContent = draftContent.TotalCount,
                RejectedContent = rejectedContent.TotalCount,
                RecentActivity = pendingReview.Items.Take(5).ToList(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(workflowStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow status");
            return StatusCode(500, "Failed to load workflow status");
        }
    }

    /// <summary>
    /// Export content data
    /// </summary>
    [HttpGet("export")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ExportContent(
        [FromQuery] string format = "json",
        [FromQuery] Domain.Enums.ContentType? type = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var searchQuery = new SearchContentQuery
            {
                Type = type,
                CreatedFrom = fromDate,
                CreatedTo = toDate,
                PageSize = 1000, // Large page size for export
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            };

            var content = await _mediator.Send(searchQuery);

            var exportData = new
            {
                ExportedAt = DateTime.UtcNow,
                ExportedBy = GetUserName(),
                Filters = new { Type = type, FromDate = fromDate, ToDate = toDate },
                TotalCount = content.TotalCount,
                Content = content.Items
            };

            return format.ToLowerInvariant() switch
            {
                "csv" => GenerateCsvResponse(content.Items),
                "xml" => GenerateXmlResponse(exportData),
                _ => Ok(exportData) // Default to JSON
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export content data");
            return StatusCode(500, "Failed to export data");
        }
    }

    private List<string> GetUserRoles()
    {
        return User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }

    private ActionResult GenerateCsvResponse(List<ContentSummaryDto> content)
    {
        var csv = "Id,Title,Type,Status,CreatedBy,CreatedAt,UpdatedAt\n";
        csv += string.Join("\n", content.Select(c => 
            $"{c.Id},{c.Title},{c.Type},{c.Status},{c.CreatedByName},{c.CreatedAt},{c.UpdatedAt}"));

        return File(System.Text.Encoding.UTF8.GetBytes(csv), "text/csv", "content-export.csv");
    }

    private ActionResult GenerateXmlResponse(object data)
    {
        var xml = System.Text.Json.JsonSerializer.Serialize(data);
        return File(System.Text.Encoding.UTF8.GetBytes(xml), "application/xml", "content-export.xml");
    }
}

/// <summary>
/// Admin dashboard DTO
/// </summary>
public class AdminDashboardDto
{
    public ContentStatisticsDto? ContentStatistics { get; set; }
    public FaqAnalyticsDto? FaqAnalytics { get; set; }
    public PolicyAnalyticsDto? PolicyAnalytics { get; set; }
    public ContentAnalyticsDto? ContentAnalytics { get; set; }
    public List<ContentPerformanceDto> TopPerformingContent { get; set; } = new();
    public List<ContentTrendDto> ContentTrends { get; set; } = new();
    public UserEngagementDto? UserEngagement { get; set; }
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Content overview DTO
/// </summary>
public class ContentOverviewDto
{
    public ContentStatisticsDto? Statistics { get; set; }
    public ContentSearchResultDto? PendingApproval { get; set; }
    public List<ContentSummaryDto> RecentContent { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Workflow status DTO
/// </summary>
public class WorkflowStatusDto
{
    public int PendingReview { get; set; }
    public int DraftContent { get; set; }
    public int RejectedContent { get; set; }
    public List<ContentSummaryDto> RecentActivity { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
