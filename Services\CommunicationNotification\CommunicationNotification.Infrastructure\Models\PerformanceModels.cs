using System.Collections.Concurrent;
using System.Diagnostics;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// Performance operation tracker
/// </summary>
public class PerformanceOperation : IDisposable
{
    private readonly string _operationName;
    private readonly Dictionary<string, object>? _tags;
    private readonly Stopwatch _stopwatch;
    private readonly Services.PerformanceMonitoringService _monitoringService;
    private bool _disposed;

    public PerformanceOperation(string operationName, Dictionary<string, object>? tags, Services.PerformanceMonitoringService monitoringService)
    {
        _operationName = operationName;
        _tags = tags;
        _monitoringService = monitoringService;
        _stopwatch = Stopwatch.StartNew();
    }

    public void Dispose()
    {
        if (_disposed) return;

        _stopwatch.Stop();
        _monitoringService.RecordOperationCompletion(_operationName, _stopwatch.Elapsed, _tags, true);
        _disposed = true;
    }

    public void MarkAsError()
    {
        if (_disposed) return;

        _stopwatch.Stop();
        _monitoringService.RecordOperationCompletion(_operationName, _stopwatch.Elapsed, _tags, false);
        _disposed = true;
    }
}

/// <summary>
/// Performance metric container
/// </summary>
public class PerformanceMetric
{
    private readonly ConcurrentQueue<MetricValue> _values;
    private readonly object _lock = new();

    public string Name { get; }
    public DateTime CreatedAt { get; }
    public DateTime LastUpdated { get; private set; }
    public long Count { get; private set; }
    public double Sum { get; private set; }
    public double Average => Count > 0 ? Sum / Count : 0;
    public double Min { get; private set; } = double.MaxValue;
    public double Max { get; private set; } = double.MinValue;

    public PerformanceMetric(string name)
    {
        Name = name;
        CreatedAt = DateTime.UtcNow;
        LastUpdated = DateTime.UtcNow;
        _values = new ConcurrentQueue<MetricValue>();
    }

    public void RecordValue(double value, Dictionary<string, object>? tags = null)
    {
        lock (_lock)
        {
            Count++;
            Sum += value;
            Min = Math.Min(Min, value);
            Max = Math.Max(Max, value);
            LastUpdated = DateTime.UtcNow;
        }

        _values.Enqueue(new MetricValue
        {
            Value = value,
            Tags = tags ?? new Dictionary<string, object>(),
            Timestamp = DateTime.UtcNow
        });

        // Keep only recent values to prevent memory issues
        while (_values.Count > 1000)
        {
            _values.TryDequeue(out _);
        }
    }

    public List<MetricValue> GetRecentValues(int count = 100)
    {
        return _values.TakeLast(count).ToList();
    }

    public double GetPercentile(double percentile)
    {
        var values = _values.Select(v => v.Value).OrderBy(v => v).ToList();
        if (!values.Any()) return 0;

        var index = (int)Math.Ceiling(percentile / 100.0 * values.Count) - 1;
        return values[Math.Max(0, Math.Min(index, values.Count - 1))];
    }
}

/// <summary>
/// Individual metric value with timestamp and tags
/// </summary>
public class MetricValue
{
    public double Value { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Performance event for tracking operations
/// </summary>
public class PerformanceEvent
{
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Performance report containing comprehensive metrics
/// </summary>
public class PerformanceReport
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalEvents { get; set; }
    public List<PerformanceMetric> Metrics { get; set; } = new();
    public List<TopOperation> TopOperations { get; set; } = new();
    public Dictionary<string, double> PerformanceTrends { get; set; } = new();
    public CacheStatistics CacheStatistics { get; set; } = new();
    public DatabaseStatistics DatabaseStatistics { get; set; } = new();
    public ApiStatistics ApiStatistics { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Top performing operations
/// </summary>
public class TopOperation
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
    public double AverageDuration { get; set; }
    public double TotalDuration { get; set; }
}

/// <summary>
/// Cache performance statistics
/// </summary>
public class CacheStatistics
{
    public int TotalRequests { get; set; }
    public double HitRate { get; set; }
    public double MissRate { get; set; }
    public double AverageResponseTime { get; set; }
}

/// <summary>
/// Database performance statistics
/// </summary>
public class DatabaseStatistics
{
    public int TotalQueries { get; set; }
    public double AverageQueryTime { get; set; }
    public int SlowQueries { get; set; }
    public double ErrorRate { get; set; }
}

/// <summary>
/// API performance statistics
/// </summary>
public class ApiStatistics
{
    public int TotalCalls { get; set; }
    public double AverageResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public double ThroughputPerMinute { get; set; }
}

/// <summary>
/// Slow operation details
/// </summary>
public class SlowOperation
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
    public double AverageDuration { get; set; }
}

/// <summary>
/// Performance alert
/// </summary>
public class PerformanceAlert
{
    public string Type { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public double Value { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Performance monitoring configuration
/// </summary>
public class PerformanceConfiguration
{
    public bool EnableMonitoring { get; set; } = true;
    public bool EnableAlerting { get; set; } = true;
    public int MaxEventHistory { get; set; } = 10000;
    public int AlertLookbackMinutes { get; set; } = 15;
    public double ErrorRateThreshold { get; set; } = 0.05; // 5%
    public double CacheHitRateThreshold { get; set; } = 0.8; // 80%
    public double SlowOperationThresholdMs { get; set; } = 1000; // 1 second
    public double SlowQueryThresholdMs { get; set; } = 500; // 500ms
    public bool EnableDetailedMetrics { get; set; } = true;
    public List<string> ExcludedOperations { get; set; } = new();
    public Dictionary<string, double> CustomThresholds { get; set; } = new();
}

/// <summary>
/// Database query optimization recommendations
/// </summary>
public class QueryOptimizationRecommendation
{
    public string QueryType { get; set; } = string.Empty;
    public string Table { get; set; } = string.Empty;
    public double AverageExecutionTime { get; set; }
    public int ExecutionCount { get; set; }
    public string Recommendation { get; set; } = string.Empty;
    public string RecommendationType { get; set; } = string.Empty;
    public double PotentialImprovement { get; set; }
}

/// <summary>
/// Cache optimization recommendations
/// </summary>
public class CacheOptimizationRecommendation
{
    public string CacheKey { get; set; } = string.Empty;
    public double HitRate { get; set; }
    public int AccessCount { get; set; }
    public TimeSpan CurrentTtl { get; set; }
    public string Recommendation { get; set; } = string.Empty;
    public TimeSpan RecommendedTtl { get; set; }
    public double PotentialImprovement { get; set; }
}

/// <summary>
/// Performance optimization suggestions
/// </summary>
public class PerformanceOptimizationSuggestion
{
    public string Category { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public string Effort { get; set; } = string.Empty;
    public double Priority { get; set; }
    public List<string> ActionItems { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// System resource metrics
/// </summary>
public class SystemResourceMetrics
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageBytes { get; set; }
    public double MemoryUsagePercent { get; set; }
    public long DiskUsageBytes { get; set; }
    public double DiskUsagePercent { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public double NetworkBytesReceived { get; set; }
    public double NetworkBytesSent { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Application performance metrics
/// </summary>
public class ApplicationPerformanceMetrics
{
    public int ActiveConnections { get; set; }
    public double RequestsPerSecond { get; set; }
    public double AverageResponseTime { get; set; }
    public double ErrorRate { get; set; }
    public int QueuedMessages { get; set; }
    public int ProcessedMessages { get; set; }
    public double CacheHitRate { get; set; }
    public double DatabaseConnectionPoolUsage { get; set; }
    public DateTime Timestamp { get; set; }
}
