using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Data;

namespace MobileWorkflow.Infrastructure.Repositories;

public class WorkflowRepository : IWorkflowRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<WorkflowRepository> _logger;

    public WorkflowRepository(MobileWorkflowDbContext context, ILogger<WorkflowRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Workflow?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .Include(w => w.Executions)
            .Include(w => w.Tasks)
            .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
    }

    public async Task<Workflow?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .FirstOrDefaultAsync(w => w.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<Workflow>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .Where(w => w.Category == category)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Workflow>> GetActiveWorkflowsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .Where(w => w.IsActive)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Workflow>> GetByTriggerTypeAsync(string triggerType, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .Where(w => w.TriggerType == triggerType)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Workflow>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Workflows.ToListAsync(cancellationToken);
    }

    public async Task<Workflow> AddAsync(Workflow workflow, CancellationToken cancellationToken = default)
    {
        _context.Workflows.Add(workflow);
        await _context.SaveChangesAsync(cancellationToken);
        return workflow;
    }

    public async Task<Workflow> UpdateAsync(Workflow workflow, CancellationToken cancellationToken = default)
    {
        _context.Workflows.Update(workflow);
        await _context.SaveChangesAsync(cancellationToken);
        return workflow;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var workflow = await GetByIdAsync(id, cancellationToken);
        if (workflow != null)
        {
            _context.Workflows.Remove(workflow);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows.AnyAsync(w => w.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows.AnyAsync(w => w.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<Workflow>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows
            .Where(w => w.Name.Contains(searchTerm) || w.Description.Contains(searchTerm))
            .ToListAsync(cancellationToken);
    }
}

public class WorkflowExecutionRepository : IWorkflowExecutionRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<WorkflowExecutionRepository> _logger;

    public WorkflowExecutionRepository(MobileWorkflowDbContext context, ILogger<WorkflowExecutionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<WorkflowExecution?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .Include(e => e.Tasks)
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByWorkflowIdAsync(Guid workflowId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Tasks)
            .Where(e => e.WorkflowId == workflowId)
            .OrderByDescending(e => e.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .Include(e => e.Tasks)
            .Where(e => e.TriggeredBy == userId)
            .OrderByDescending(e => e.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .Where(e => e.Status == status)
            .OrderByDescending(e => e.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .Include(e => e.Tasks)
            .Where(e => e.Status == "Running")
            .OrderBy(e => e.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetRecentExecutionsAsync(int count, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .OrderByDescending(e => e.StartTime)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<WorkflowExecution> AddAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        _context.WorkflowExecutions.Add(execution);
        await _context.SaveChangesAsync(cancellationToken);
        return execution;
    }

    public async Task<WorkflowExecution> UpdateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        _context.WorkflowExecutions.Update(execution);
        await _context.SaveChangesAsync(cancellationToken);
        return execution;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var execution = await GetByIdAsync(id, cancellationToken);
        if (execution != null)
        {
            _context.WorkflowExecutions.Remove(execution);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions.AnyAsync(e => e.Id == id, cancellationToken);
    }

    public async Task<int> GetExecutionCountByWorkflowIdAsync(Guid workflowId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .CountAsync(e => e.WorkflowId == workflowId, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowExecution>> GetExecutionsInDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowExecutions
            .Include(e => e.Workflow)
            .Where(e => e.StartTime >= startDate && e.StartTime <= endDate)
            .OrderByDescending(e => e.StartTime)
            .ToListAsync(cancellationToken);
    }
}

public class WorkflowTaskRepository : IWorkflowTaskRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<WorkflowTaskRepository> _logger;

    public WorkflowTaskRepository(MobileWorkflowDbContext context, ILogger<WorkflowTaskRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<WorkflowTask?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByWorkflowExecutionIdAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Where(t => t.WorkflowExecutionId == executionId)
            .OrderBy(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByAssignedToAsync(Guid assignedTo, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.AssignedTo == assignedTo)
            .OrderBy(t => t.Priority)
            .ThenBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByAssignedToRoleAsync(string role, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.AssignedToRole == role)
            .OrderBy(t => t.Priority)
            .ThenBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.Status == status)
            .OrderBy(t => t.Priority)
            .ThenBy(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByTypeAsync(string type, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.Type == type)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetByPriorityAsync(int priority, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.Priority == priority)
            .OrderBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetOverdueTasksAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.DueDate < now && !t.IsCompleted())
            .OrderBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetPendingTasksAsync(CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.Status == "Pending")
            .OrderBy(t => t.Priority)
            .ThenBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetTasksDueSoonAsync(TimeSpan timeWindow, CancellationToken cancellationToken = default)
    {
        var dueThreshold = DateTime.UtcNow.Add(timeWindow);
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.DueDate <= dueThreshold && !t.IsCompleted())
            .OrderBy(t => t.DueDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<WorkflowTask> AddAsync(WorkflowTask task, CancellationToken cancellationToken = default)
    {
        _context.WorkflowTasks.Add(task);
        await _context.SaveChangesAsync(cancellationToken);
        return task;
    }

    public async Task<WorkflowTask> UpdateAsync(WorkflowTask task, CancellationToken cancellationToken = default)
    {
        _context.WorkflowTasks.Update(task);
        await _context.SaveChangesAsync(cancellationToken);
        return task;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var task = await GetByIdAsync(id, cancellationToken);
        if (task != null)
        {
            _context.WorkflowTasks.Remove(task);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks.AnyAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<int> GetTaskCountByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .CountAsync(t => t.Status == status, cancellationToken);
    }

    public async Task<IEnumerable<WorkflowTask>> GetTasksInDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _context.WorkflowTasks
            .Include(t => t.Workflow)
            .Include(t => t.WorkflowExecution)
            .Where(t => t.CreatedAt >= startDate && t.CreatedAt <= endDate)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
