using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

public class TripLeg : BaseEntity
{
    public Guid TripId { get; private set; }
    public int LegNumber { get; private set; }
    public string LegName { get; private set; } = string.Empty;
    public string? Description { get; private set; }
    public Guid? DriverId { get; private set; }
    public Guid? VehicleId { get; private set; }
    public TripLegStatus Status { get; private set; }
    public Route Route { get; private set; } = null!;
    public DateTime EstimatedStartTime { get; private set; }
    public DateTime EstimatedEndTime { get; private set; }
    public DateTime? ActualStartTime { get; private set; }
    public DateTime? ActualEndTime { get; private set; }
    public decimal? EstimatedDistanceKm { get; private set; }
    public decimal? ActualDistanceKm { get; private set; }
    public string? SpecialInstructions { get; private set; }
    public bool IsRequired { get; private set; }
    public int Priority { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public Trip Trip { get; private set; } = null!;
    public Driver? Driver { get; private set; }
    public Vehicle? Vehicle { get; private set; }

    private readonly List<TripLegStop> _stops = new();
    public IReadOnlyCollection<TripLegStop> Stops => _stops.AsReadOnly();

    private readonly List<TripLegLocationUpdate> _locationUpdates = new();
    public IReadOnlyCollection<TripLegLocationUpdate> LocationUpdates => _locationUpdates.AsReadOnly();

    private readonly List<TripLegException> _exceptions = new();
    public IReadOnlyCollection<TripLegException> Exceptions => _exceptions.AsReadOnly();

    private readonly List<TripLegDocument> _documents = new();
    public IReadOnlyCollection<TripLegDocument> Documents => _documents.AsReadOnly();

    private TripLeg() { } // EF Constructor

    public TripLeg(
        Guid tripId,
        int legNumber,
        string legName,
        Route route,
        DateTime estimatedStartTime,
        DateTime estimatedEndTime,
        string? description = null,
        string? specialInstructions = null,
        bool isRequired = true,
        int priority = 1)
    {
        if (string.IsNullOrWhiteSpace(legName))
            throw new ArgumentException("Leg name cannot be empty", nameof(legName));

        if (estimatedStartTime >= estimatedEndTime)
            throw new ArgumentException("Estimated start time must be before end time");

        if (legNumber < 1)
            throw new ArgumentException("Leg number must be greater than 0", nameof(legNumber));

        TripId = tripId;
        LegNumber = legNumber;
        LegName = legName;
        Description = description;
        Route = route ?? throw new ArgumentNullException(nameof(route));
        EstimatedStartTime = estimatedStartTime;
        EstimatedEndTime = estimatedEndTime;
        SpecialInstructions = specialInstructions;
        IsRequired = isRequired;
        Priority = priority;
        Status = TripLegStatus.Planned;
        EstimatedDistanceKm = (decimal?)route.EstimatedDistanceKm;
    }

    public void AssignResources(Guid? driverId, Guid? vehicleId)
    {
        if (Status != TripLegStatus.Planned && Status != TripLegStatus.Assigned)
            throw new InvalidOperationException($"Cannot assign resources to leg with status {Status}");

        DriverId = driverId;
        VehicleId = vehicleId;

        if (driverId.HasValue && vehicleId.HasValue)
        {
            Status = TripLegStatus.Assigned;
        }
        else if (Status == TripLegStatus.Assigned)
        {
            Status = TripLegStatus.Planned;
        }
    }

    public void Start()
    {
        if (Status != TripLegStatus.Assigned)
            throw new InvalidOperationException("Can only start assigned legs");

        if (!DriverId.HasValue || !VehicleId.HasValue)
            throw new InvalidOperationException("Cannot start leg without driver and vehicle assignment");

        Status = TripLegStatus.InProgress;
        ActualStartTime = DateTime.UtcNow;
    }

    public void Complete()
    {
        if (Status != TripLegStatus.InProgress)
            throw new InvalidOperationException("Can only complete in-progress legs");

        // Check if all required stops are completed
        var incompleteRequiredStops = _stops.Where(s =>
            s.IsRequired &&
            s.Status != TripStopStatus.Completed &&
            s.Status != TripStopStatus.Skipped).ToList();

        if (incompleteRequiredStops.Any())
            throw new InvalidOperationException("Cannot complete leg with incomplete required stops");

        Status = TripLegStatus.Completed;
        ActualEndTime = DateTime.UtcNow;
    }

    public void Cancel(string reason)
    {
        if (Status == TripLegStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed leg");

        Status = TripLegStatus.Cancelled;
        Notes = $"Cancelled: {reason}";
    }

    public void Skip(string reason)
    {
        if (!IsRequired)
        {
            Status = TripLegStatus.Skipped;
            Notes = $"Skipped: {reason}";
        }
        else
        {
            throw new InvalidOperationException("Cannot skip required leg");
        }
    }

    public void UpdateEstimatedTimes(DateTime newStartTime, DateTime newEndTime)
    {
        if (newStartTime >= newEndTime)
            throw new ArgumentException("Start time must be before end time");

        if (Status == TripLegStatus.Completed)
            throw new InvalidOperationException("Cannot update times for completed leg");

        EstimatedStartTime = newStartTime;
        EstimatedEndTime = newEndTime;
    }

    public void UpdateRoute(Route newRoute)
    {
        if (Status == TripLegStatus.Completed)
            throw new InvalidOperationException("Cannot update route for completed leg");

        Route = newRoute ?? throw new ArgumentNullException(nameof(newRoute));
        EstimatedDistanceKm = (decimal?)newRoute.EstimatedDistanceKm;
    }

    public void AddStop(TripLegStop stop)
    {
        if (stop == null)
            throw new ArgumentNullException(nameof(stop));

        _stops.Add(stop);
    }

    public void RemoveStop(Guid stopId)
    {
        var stop = _stops.FirstOrDefault(s => s.Id == stopId);
        if (stop != null)
        {
            _stops.Remove(stop);
        }
    }

    public void AddLocationUpdate(TripLegLocationUpdate locationUpdate)
    {
        if (locationUpdate == null)
            throw new ArgumentNullException(nameof(locationUpdate));

        _locationUpdates.Add(locationUpdate);
    }

    public void ReportException(TripLegException exception)
    {
        if (exception == null)
            throw new ArgumentNullException(nameof(exception));

        _exceptions.Add(exception);

        if (exception.Severity == ExceptionSeverity.Critical)
        {
            Status = TripLegStatus.Exception;
        }
    }

    public void ResolveException(Guid exceptionId, string resolution)
    {
        var exception = _exceptions.FirstOrDefault(e => e.Id == exceptionId);
        if (exception == null)
            throw new ArgumentException("Exception not found", nameof(exceptionId));

        exception.Resolve(resolution);

        // If all exceptions are resolved, return to previous status
        if (!_exceptions.Any(e => !e.IsResolved) && Status == TripLegStatus.Exception)
        {
            Status = TripLegStatus.InProgress;
        }
    }

    public void AddDocument(TripLegDocument document)
    {
        if (document == null)
            throw new ArgumentNullException(nameof(document));

        _documents.Add(document);
    }

    public void UpdateActualDistance(decimal distanceKm)
    {
        if (distanceKm < 0)
            throw new ArgumentException("Distance cannot be negative", nameof(distanceKm));

        ActualDistanceKm = distanceKm;
    }

    public void UpdateNotes(string? notes)
    {
        Notes = notes;
    }

    public void UpdatePriority(int newPriority)
    {
        if (newPriority < 1)
            throw new ArgumentException("Priority must be greater than 0", nameof(newPriority));

        Priority = newPriority;
    }

    public Location? GetCurrentLocation()
    {
        return _locationUpdates.OrderByDescending(l => l.Timestamp).FirstOrDefault()?.Location;
    }

    public TimeSpan? GetActualDuration()
    {
        if (ActualStartTime.HasValue && ActualEndTime.HasValue)
            return ActualEndTime.Value - ActualStartTime.Value;

        if (ActualStartTime.HasValue)
            return DateTime.UtcNow - ActualStartTime.Value;

        return null;
    }

    public TimeSpan GetEstimatedDuration()
    {
        return EstimatedEndTime - EstimatedStartTime;
    }

    public bool IsDelayed()
    {
        if (Status == TripLegStatus.Completed && ActualEndTime.HasValue)
            return ActualEndTime.Value > EstimatedEndTime;

        return DateTime.UtcNow > EstimatedEndTime && Status != TripLegStatus.Completed;
    }

    public bool IsActive()
    {
        return Status == TripLegStatus.InProgress || Status == TripLegStatus.Exception;
    }

    public bool CanStart()
    {
        return Status == TripLegStatus.Assigned && DriverId.HasValue && VehicleId.HasValue;
    }

    public bool CanComplete()
    {
        return Status == TripLegStatus.InProgress &&
               !_stops.Where(s => s.IsRequired).Any(s =>
                   s.Status != TripStopStatus.Completed &&
                   s.Status != TripStopStatus.Skipped);
    }

    public decimal GetCompletionPercentage()
    {
        if (Status == TripLegStatus.Completed)
            return 100m;

        if (Status == TripLegStatus.Planned || Status == TripLegStatus.Assigned)
            return 0m;

        var totalStops = _stops.Count;
        if (totalStops == 0)
        {
            // Base completion on time if no stops
            if (ActualStartTime.HasValue)
            {
                var elapsed = DateTime.UtcNow - ActualStartTime.Value;
                var estimated = GetEstimatedDuration();
                return Math.Min(100m, (decimal)(elapsed.TotalMinutes / estimated.TotalMinutes * 100));
            }
            return 0m;
        }

        var completedStops = _stops.Count(s => s.Status == TripStopStatus.Completed);
        return (decimal)completedStops / totalStops * 100m;
    }
}

public enum TripLegStatus
{
    Planned = 1,
    Assigned = 2,
    InProgress = 3,
    Completed = 4,
    Cancelled = 5,
    Skipped = 6,
    Exception = 7
}


