using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetRfqMilestoneDetails;

public class GetRfqMilestoneDetailsQuery : IRequest<RfqMilestoneDetailsDto?>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool IncludeProgress { get; set; } = true;
    public bool IncludePayoutCalculations { get; set; } = true;
}
