using AutoMapper;
using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.ValueObjects;

namespace ContentManagement.Application.Mappings;

/// <summary>
/// AutoMapper profile for content management mappings
/// </summary>
public class ContentMappingProfile : Profile
{
    public ContentMappingProfile()
    {
        // Content mappings
        CreateMap<Content, ContentDto>()
            .ForMember(dest => dest.Visibility, opt => opt.MapFrom(src => src.Visibility))
            .ForMember(dest => dest.PublishSchedule, opt => opt.MapFrom(src => src.PublishSchedule))
            .ForMember(dest => dest.Versions, opt => opt.MapFrom(src => src.Versions))
            .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments));

        CreateMap<Content, ContentSummaryDto>();

        // Content visibility mappings
        CreateMap<ContentVisibility, ContentVisibilityDto>()
            .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.Level))
            .ForMember(dest => dest.AllowedRoles, opt => opt.MapFrom(src => src.AllowedRoles))
            .ForMember(dest => dest.VisibleFrom, opt => opt.MapFrom(src => src.VisibleFrom))
            .ForMember(dest => dest.VisibleUntil, opt => opt.MapFrom(src => src.VisibleUntil));

        CreateMap<ContentVisibilityDto, ContentVisibility>()
            .ConvertUsing(src => MapContentVisibility(src));

        // Publish schedule mappings
        CreateMap<PublishSchedule, PublishScheduleDto>()
            .ForMember(dest => dest.PublishAt, opt => opt.MapFrom(src => src.PublishAt))
            .ForMember(dest => dest.UnpublishAt, opt => opt.MapFrom(src => src.UnpublishAt))
            .ForMember(dest => dest.IsRecurring, opt => opt.MapFrom(src => src.IsRecurring))
            .ForMember(dest => dest.Pattern, opt => opt.MapFrom(src => src.Pattern))
            .ForMember(dest => dest.RecurrenceInterval, opt => opt.MapFrom(src => src.RecurrenceInterval))
            .ForMember(dest => dest.RecurrenceEndDate, opt => opt.MapFrom(src => src.RecurrenceEndDate));

        CreateMap<PublishScheduleDto, PublishSchedule>()
            .ConvertUsing(src => MapPublishSchedule(src));

        // Content version mappings
        CreateMap<ContentVersion, ContentVersionDto>();

        // Content attachment mappings
        CreateMap<ContentAttachment, ContentAttachmentDto>();

        // FAQ mappings
        CreateMap<FaqItem, FaqItemDto>()
            .IncludeBase<Content, ContentDto>()
            .ForMember(dest => dest.Question, opt => opt.MapFrom(src => src.Question))
            .ForMember(dest => dest.Answer, opt => opt.MapFrom(src => src.Answer))
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category))
            .ForMember(dest => dest.RoleVisibility, opt => opt.MapFrom(src => src.RoleVisibility))
            .ForMember(dest => dest.IsHighlighted, opt => opt.MapFrom(src => src.IsHighlighted))
            .ForMember(dest => dest.ViewCount, opt => opt.MapFrom(src => src.ViewCount))
            .ForMember(dest => dest.HelpfulnessRating, opt => opt.MapFrom(src => src.HelpfulnessRating))
            .ForMember(dest => dest.TotalRatings, opt => opt.MapFrom(src => src.TotalRatings));

        CreateMap<FaqItem, FaqSummaryDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Question, opt => opt.MapFrom(src => src.Question))
            .ForMember(dest => dest.Answer, opt => opt.MapFrom(src => src.Answer))
            .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.Category))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.IsHighlighted, opt => opt.MapFrom(src => src.IsHighlighted))
            .ForMember(dest => dest.ViewCount, opt => opt.MapFrom(src => src.ViewCount))
            .ForMember(dest => dest.HelpfulnessRating, opt => opt.MapFrom(src => src.HelpfulnessRating))
            .ForMember(dest => dest.SortOrder, opt => opt.MapFrom(src => src.SortOrder))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.Tags));

        // Policy mappings
        CreateMap<PolicyDocument, PolicyDocumentDto>()
            .IncludeBase<Content, ContentDto>()
            .ForMember(dest => dest.PolicyType, opt => opt.MapFrom(src => src.PolicyType))
            .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.Version))
            .ForMember(dest => dest.EffectiveDate, opt => opt.MapFrom(src => src.EffectiveDate))
            .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
            .ForMember(dest => dest.RequiresAcceptance, opt => opt.MapFrom(src => src.RequiresAcceptance))
            .ForMember(dest => dest.ApplicableRoles, opt => opt.MapFrom(src => src.ApplicableRoles))
            .ForMember(dest => dest.LegalReferences, opt => opt.MapFrom(src => src.LegalReferences))
            .ForMember(dest => dest.IsCurrentVersion, opt => opt.MapFrom(src => src.IsCurrentVersion))
            .ForMember(dest => dest.Acceptances, opt => opt.MapFrom(src => src.Acceptances));

        CreateMap<PolicyDocument, PolicySummaryDto>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
            .ForMember(dest => dest.PolicyType, opt => opt.MapFrom(src => src.PolicyType))
            .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.Version))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.EffectiveDate, opt => opt.MapFrom(src => src.EffectiveDate))
            .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
            .ForMember(dest => dest.RequiresAcceptance, opt => opt.MapFrom(src => src.RequiresAcceptance))
            .ForMember(dest => dest.IsCurrentVersion, opt => opt.MapFrom(src => src.IsCurrentVersion))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.AcceptanceCount, opt => opt.MapFrom(src => src.Acceptances.Count));

        // Policy acceptance mappings
        CreateMap<PolicyAcceptance, PolicyAcceptanceDto>();
    }

    private static ContentVisibility MapContentVisibility(ContentVisibilityDto dto)
    {
        return dto.Level switch
        {
            Domain.Enums.VisibilityLevel.Public => ContentVisibility.CreatePublic(dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.Authenticated => ContentVisibility.CreateAuthenticated(dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.RoleSpecific => ContentVisibility.CreateRoleSpecific(dto.AllowedRoles, dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.Private => ContentVisibility.CreatePrivate(dto.VisibleFrom, dto.VisibleUntil),
            _ => ContentVisibility.CreatePublic()
        };
    }

    private static PublishSchedule? MapPublishSchedule(PublishScheduleDto dto)
    {
        if (dto.PublishAt == null && dto.UnpublishAt == null && !dto.IsRecurring)
        {
            return null;
        }

        if (dto.IsRecurring && dto.PublishAt != null)
        {
            return PublishSchedule.CreateRecurring(
                dto.PublishAt.Value,
                dto.Pattern,
                dto.RecurrenceInterval ?? 1,
                dto.RecurrenceEndDate,
                dto.UnpublishAt);
        }

        if (dto.PublishAt != null)
        {
            return PublishSchedule.CreateScheduled(dto.PublishAt.Value, dto.UnpublishAt);
        }

        return null;
    }
}
