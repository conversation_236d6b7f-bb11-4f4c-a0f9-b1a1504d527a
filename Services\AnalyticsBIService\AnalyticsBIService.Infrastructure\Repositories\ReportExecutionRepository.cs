using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for report execution operations
/// </summary>
public class ReportExecutionRepository : OptimizedRepositoryBase<ReportExecution, Guid>, IReportExecutionRepository
{
    public ReportExecutionRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get report executions by report ID
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetByReportIdAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.ReportId == reportId)
            .OrderByDescending(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get report executions by status
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.Status == status)
            .OrderByDescending(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get report executions by user ID
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.UserId == userId)
            .OrderByDescending(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get recent report executions
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetRecentExecutionsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .OrderByDescending(re => re.StartTime)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get failed report executions
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetFailedExecutionsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.Status == "Failed")
            .OrderByDescending(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get scheduled reports
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetScheduledReportsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.Status == "Scheduled")
            .OrderBy(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Add scheduled report
    /// </summary>
    public async Task<ReportExecution> AddScheduledReportAsync(ReportExecution scheduledReport, CancellationToken cancellationToken = default)
    {
        await Context.Set<ReportExecution>().AddAsync(scheduledReport, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
        return scheduledReport;
    }

    /// <summary>
    /// Get execution history
    /// </summary>
    public async Task<IEnumerable<ReportExecution>> GetExecutionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExecution>()
            .Where(re => re.ReportId == reportId)
            .OrderByDescending(re => re.StartTime)
            .ToListAsync(cancellationToken);
    }
}
