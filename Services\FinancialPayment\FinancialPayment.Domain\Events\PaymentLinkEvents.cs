using Shared.Domain.Common;
using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

/// <summary>
/// Event raised when a payment link is created
/// </summary>
public class PaymentLinkCreatedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public Guid TransportCompanyId { get; }
    public Guid ShipperId { get; }
    public Money Amount { get; }
    public string LinkToken { get; }
    public DateTime ExpiresAt { get; }
    public PaymentLinkType LinkType { get; }

    public PaymentLinkCreatedEvent(
        Guid paymentLinkId,
        Guid transportCompanyId,
        Guid shipperId,
        Money amount,
        string linkToken,
        DateTime expiresAt,
        PaymentLinkType linkType)
    {
        PaymentLinkId = paymentLinkId;
        TransportCompanyId = transportCompanyId;
        ShipperId = shipperId;
        Amount = amount;
        LinkToken = linkToken;
        ExpiresAt = expiresAt;
        LinkType = linkType;
    }
}

/// <summary>
/// Event raised when a payment link is accessed
/// </summary>
public class PaymentLinkAccessedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string IpAddress { get; }
    public int TotalAccessCount { get; }

    public PaymentLinkAccessedEvent(Guid paymentLinkId, string ipAddress, int totalAccessCount)
    {
        PaymentLinkId = paymentLinkId;
        IpAddress = ipAddress;
        TotalAccessCount = totalAccessCount;
    }
}

/// <summary>
/// Event raised when a payment is attempted through a payment link
/// </summary>
public class PaymentLinkPaymentAttemptedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string PaymentMethodId { get; }
    public string? GatewayTransactionId { get; }

    public PaymentLinkPaymentAttemptedEvent(
        Guid paymentLinkId,
        string paymentMethodId,
        string? gatewayTransactionId)
    {
        PaymentLinkId = paymentLinkId;
        PaymentMethodId = paymentMethodId;
        GatewayTransactionId = gatewayTransactionId;
    }
}

/// <summary>
/// Event raised when a payment link is successfully paid
/// </summary>
public class PaymentLinkPaidEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public Guid TransportCompanyId { get; }
    public Guid ShipperId { get; }
    public Money Amount { get; }
    public string PaymentTransactionId { get; }
    public DateTime PaidAt { get; }

    public PaymentLinkPaidEvent(
        Guid paymentLinkId,
        Guid transportCompanyId,
        Guid shipperId,
        Money amount,
        string paymentTransactionId,
        DateTime paidAt)
    {
        PaymentLinkId = paymentLinkId;
        TransportCompanyId = transportCompanyId;
        ShipperId = shipperId;
        Amount = amount;
        PaymentTransactionId = paymentTransactionId;
        PaidAt = paidAt;
    }
}

/// <summary>
/// Event raised when a payment link is cancelled
/// </summary>
public class PaymentLinkCancelledEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string Reason { get; }

    public PaymentLinkCancelledEvent(Guid paymentLinkId, string reason)
    {
        PaymentLinkId = paymentLinkId;
        Reason = reason;
    }
}

/// <summary>
/// Event raised when a payment link is suspended
/// </summary>
public class PaymentLinkSuspendedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string Reason { get; }

    public PaymentLinkSuspendedEvent(Guid paymentLinkId, string reason)
    {
        PaymentLinkId = paymentLinkId;
        Reason = reason;
    }
}

/// <summary>
/// Event raised when a payment link is extended
/// </summary>
public class PaymentLinkExtendedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public DateTime OldExpiryDate { get; }
    public DateTime NewExpiryDate { get; }
    public string Reason { get; }

    public PaymentLinkExtendedEvent(
        Guid paymentLinkId,
        DateTime oldExpiryDate,
        DateTime newExpiryDate,
        string reason)
    {
        PaymentLinkId = paymentLinkId;
        OldExpiryDate = oldExpiryDate;
        NewExpiryDate = newExpiryDate;
        Reason = reason;
    }
}

/// <summary>
/// Event raised when suspicious activity is detected on a payment link
/// </summary>
public class PaymentLinkSuspiciousActivityEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string IpAddress { get; }
    public int AccessCount { get; }
    public string ActivityType { get; }

    public PaymentLinkSuspiciousActivityEvent(
        Guid paymentLinkId,
        string ipAddress,
        int accessCount,
        string activityType = "Excessive Access Attempts")
    {
        PaymentLinkId = paymentLinkId;
        IpAddress = ipAddress;
        AccessCount = accessCount;
        ActivityType = activityType;
    }
}

/// <summary>
/// Event raised when a payment link expires
/// </summary>
public class PaymentLinkExpiredEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public Guid TransportCompanyId { get; }
    public Guid ShipperId { get; }
    public Money Amount { get; }
    public DateTime ExpiredAt { get; }

    public PaymentLinkExpiredEvent(
        Guid paymentLinkId,
        Guid transportCompanyId,
        Guid shipperId,
        Money amount,
        DateTime expiredAt)
    {
        PaymentLinkId = paymentLinkId;
        TransportCompanyId = transportCompanyId;
        ShipperId = shipperId;
        Amount = amount;
        ExpiredAt = expiredAt;
    }
}

/// <summary>
/// Event raised when a payment link reminder is sent
/// </summary>
public class PaymentLinkReminderSentEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string ReminderType { get; }
    public List<string> Recipients { get; }
    public int ReminderCount { get; }

    public PaymentLinkReminderSentEvent(
        Guid paymentLinkId,
        string reminderType,
        List<string> recipients,
        int reminderCount)
    {
        PaymentLinkId = paymentLinkId;
        ReminderType = reminderType;
        Recipients = recipients;
        ReminderCount = reminderCount;
    }
}

/// <summary>
/// Event raised when a payment link is shared
/// </summary>
public class PaymentLinkSharedEvent : DomainEvent
{
    public Guid PaymentLinkId { get; }
    public string SharedVia { get; } // Email, SMS, WhatsApp, etc.
    public List<string> Recipients { get; }
    public Guid SharedByUserId { get; }

    public PaymentLinkSharedEvent(
        Guid paymentLinkId,
        string sharedVia,
        List<string> recipients,
        Guid sharedByUserId)
    {
        PaymentLinkId = paymentLinkId;
        SharedVia = sharedVia;
        Recipients = recipients;
        SharedByUserId = sharedByUserId;
    }
}


