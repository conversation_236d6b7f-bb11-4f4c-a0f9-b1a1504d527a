using Identity.Domain.Entities;
using Identity.Domain.Common.Events;
using Microsoft.EntityFrameworkCore;

namespace Identity.Infrastructure.Persistence
{
    public class IdentityDbContext : DbContext
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<OtpToken> OtpTokens { get; set; }
        public DbSet<SubAdmin> SubAdmins { get; set; }
        public DbSet<SubAdminSession> SubAdminSessions { get; set; }

        public IdentityDbContext(DbContextOptions<IdentityDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("Users");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).HasMaxLength(50).IsRequired();
                entity.HasIndex(e => e.Username).IsUnique();
                entity.OwnsOne(e => e.Email, email =>
                {
                    email.Property(e => e.Value)
                        .HasColumnName("Email")
                        .HasMaxLength(320)
                        .IsRequired();
                    email.WithOwner();
                    email.HasIndex(e => e.Value).IsUnique();
                });
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.SecurityStamp).IsRequired();
                entity.Property(e => e.PhoneNumber).HasMaxLength(20).IsRequired(false);
                entity.Property(e => e.Status).HasConversion<string>().HasMaxLength(20);

                // Configure User-UserRole relationship
                entity.HasMany(u => u.Roles)
                    .WithOne()
                    .HasForeignKey(ur => ur.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Role configuration
            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("Roles");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(50).IsRequired();
                entity.HasIndex(e => e.NormalizedName).IsUnique();
                entity.Property(e => e.Description).HasMaxLength(200);
            });

            // Permission configuration
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.ToTable("Permissions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.HasIndex(e => e.NormalizedName).IsUnique();
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.Property(e => e.Category).HasMaxLength(50);
            });

            // UserRole configuration
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.ToTable("UserRoles");
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique();

                // Configure explicit relationships to avoid shadow properties
                entity.HasOne<Role>()
                    .WithMany()
                    .HasForeignKey(ur => ur.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // RolePermission configuration
            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.ToTable("RolePermissions");
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.RoleId, e.PermissionId }).IsUnique();
            });

            // RefreshToken configuration
            modelBuilder.Entity<RefreshToken>(entity =>
            {
                entity.ToTable("RefreshTokens");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Token).HasMaxLength(128).IsRequired();
                entity.HasIndex(e => e.Token).IsUnique();
                entity.Property(e => e.CreatedByIp).HasMaxLength(50);
                entity.Property(e => e.RevokedByIp).HasMaxLength(50);
                entity.Property(e => e.ReasonRevoked).HasMaxLength(200);
                entity.Property(e => e.ReplacedByToken).HasMaxLength(128);
            });

            // UserSession configuration
            modelBuilder.Entity<UserSession>(entity =>
            {
                entity.ToTable("UserSessions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Token).HasMaxLength(1024).IsRequired();
                entity.Property(e => e.DeviceInfo).HasMaxLength(1024);
                entity.Property(e => e.IpAddress).HasMaxLength(50);
            });

            // OtpToken configuration
            modelBuilder.Entity<OtpToken>(entity =>
            {
                entity.ToTable("OtpTokens");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Token).HasMaxLength(10).IsRequired();
                entity.Property(e => e.Purpose).HasConversion<string>().HasMaxLength(50);
                entity.Property(e => e.DeliveryMethod).HasMaxLength(20).IsRequired();
                entity.Property(e => e.DeliveryAddress).HasMaxLength(320).IsRequired();
                entity.Property(e => e.IpAddress).HasMaxLength(50);
                entity.Property(e => e.UserAgent).HasMaxLength(1024);
                entity.HasIndex(e => new { e.UserId, e.Purpose, e.CreatedAt });
                entity.HasIndex(e => e.ExpiresAt);

                // Foreign key relationship
                entity.HasOne(e => e.User)
                    .WithMany(u => u.OtpTokens)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SubAdmin configuration
            modelBuilder.Entity<SubAdmin>(entity =>
            {
                entity.ToTable("SubAdmins");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Status).HasConversion<string>().HasMaxLength(20);
                entity.Property(e => e.LastLoginIp).HasMaxLength(50);
                entity.Property(e => e.TwoFactorSecret).HasMaxLength(256);
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.HasIndex(e => e.UserId).IsUnique();
                entity.HasIndex(e => e.Department);
                entity.HasIndex(e => e.Status);

                // Configure Email value object
                entity.OwnsOne(e => e.Email, email =>
                {
                    email.Property(e => e.Value)
                        .HasColumnName("Email")
                        .HasMaxLength(320)
                        .IsRequired();
                    email.WithOwner();
                    email.HasIndex(e => e.Value).IsUnique();
                });

                // Configure AllowedIpAddresses as JSON
                entity.Property(e => e.AllowedIpAddresses)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                    .HasMaxLength(1000)
                    .Metadata.SetValueComparer(new Microsoft.EntityFrameworkCore.ChangeTracking.ValueComparer<List<string>>(
                        (c1, c2) => c1.SequenceEqual(c2),
                        c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        c => c.ToList()));

                // Configure relationships - SubAdmin roles use the same UserRole table but with SubAdmin's UserId
                entity.HasMany(e => e.Roles)
                    .WithOne()
                    .HasForeignKey(ur => ur.UserId)
                    .HasPrincipalKey(sa => sa.UserId)  // Use SubAdmin's UserId as the principal key
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.Sessions)
                    .WithOne()
                    .HasForeignKey(s => s.SubAdminId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SubAdminSession configuration
            modelBuilder.Entity<SubAdminSession>(entity =>
            {
                entity.ToTable("SubAdminSessions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SessionToken).HasMaxLength(1024).IsRequired();
                entity.Property(e => e.IpAddress).HasMaxLength(50).IsRequired();
                entity.Property(e => e.UserAgent).HasMaxLength(1024);
                entity.Property(e => e.Location).HasMaxLength(200);
                entity.Property(e => e.Status).HasConversion<string>().HasMaxLength(20);
                entity.Property(e => e.EmergencyReason).HasMaxLength(500);
                entity.HasIndex(e => e.SessionToken).IsUnique();
                entity.HasIndex(e => new { e.SubAdminId, e.Status });
                entity.HasIndex(e => e.ExpiresAt);
            });

            // Ignore domain events as they are not persisted to the database
            modelBuilder.Ignore<DomainEvent>();
        }
    }
}
