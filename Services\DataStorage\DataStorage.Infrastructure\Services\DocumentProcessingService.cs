using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using System.Security.Cryptography;
using System.Text;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;

namespace DataStorage.Infrastructure.Services;

public class DocumentProcessingService : IDocumentProcessingService
{
    private readonly ILogger<DocumentProcessingService> _logger;
    private readonly IFileStorageService _fileStorageService;

    public DocumentProcessingService(
        ILogger<DocumentProcessingService> logger,
        IFileStorageService fileStorageService)
    {
        _logger = logger;
        _fileStorageService = fileStorageService;
    }

    public async Task<string> ExtractTextAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Extracting text from document at {Location}", location.GetFullPath());

            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            
            return fileMetadata.ContentType.ToLowerInvariant() switch
            {
                "text/plain" => await ExtractTextFromPlainTextAsync(location, cancellationToken),
                "application/pdf" => await ExtractTextFromPdfAsync(location, cancellationToken),
                _ when fileMetadata.ContentType.StartsWith("image/") => await PerformOCRAsync(location, null, cancellationToken),
                _ => throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType)
            };
        }
        catch (Exception ex) when (!(ex is UnsupportedDocumentTypeException))
        {
            _logger.LogError(ex, "Failed to extract text from document at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("extract text", location.FilePath, ex);
        }
    }

    public async Task<byte[]> GenerateThumbnailAsync(
        StorageLocation location, 
        int width = 200, 
        int height = 200, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating thumbnail for document at {Location}", location.GetFullPath());

            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            
            if (!fileMetadata.IsImage())
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
            using var image = await Image.LoadAsync(fileStream, cancellationToken);
            
            image.Mutate(x => x.Resize(new ResizeOptions
            {
                Size = new Size(width, height),
                Mode = ResizeMode.Max
            }));

            using var outputStream = new MemoryStream();
            await image.SaveAsJpegAsync(outputStream, new JpegEncoder { Quality = 85 }, cancellationToken);
            
            _logger.LogDebug("Thumbnail generated successfully for {Location}", location.GetFullPath());
            
            return outputStream.ToArray();
        }
        catch (Exception ex) when (!(ex is UnsupportedDocumentTypeException))
        {
            _logger.LogError(ex, "Failed to generate thumbnail for document at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("generate thumbnail", location.FilePath, ex);
        }
    }

    public async Task<FileMetadata> AnalyzeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Analyzing document at {Location}", location.GetFullPath());

            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var customProperties = new Dictionary<string, string>();

            // Add basic analysis
            customProperties["analyzed_at"] = DateTime.UtcNow.ToString("O");
            customProperties["is_image"] = fileMetadata.IsImage().ToString();
            customProperties["is_pdf"] = fileMetadata.IsPdf().ToString();
            customProperties["is_document"] = fileMetadata.IsDocument().ToString();

            // For images, add dimension information
            if (fileMetadata.IsImage())
            {
                try
                {
                    using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
                    using var image = await Image.LoadAsync(fileStream, cancellationToken);
                    
                    customProperties["image_width"] = image.Width.ToString();
                    customProperties["image_height"] = image.Height.ToString();
                    customProperties["image_format"] = image.Metadata.DecodedImageFormat?.Name ?? "Unknown";
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze image dimensions for {Location}", location.GetFullPath());
                }
            }

            return fileMetadata.WithCustomProperty("analysis_complete", "true");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze document at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("analyze", location.FilePath, ex);
        }
    }

    public async Task<StorageLocation> ConvertToPdfAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        // PDF conversion would require additional libraries like iText7 or similar
        // For now, throw not implemented
        throw new NotImplementedException("PDF conversion is not yet implemented");
    }

    public async Task<StorageLocation> ConvertToImageAsync(StorageLocation location, string format = "png", CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Converting document to image format {Format} at {Location}", format, location.GetFullPath());

            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            
            if (!fileMetadata.IsImage())
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
            using var image = await Image.LoadAsync(fileStream, cancellationToken);
            
            var outputFileName = Path.ChangeExtension(fileMetadata.FileName, format);
            var outputContentType = $"image/{format}";
            
            using var outputStream = new MemoryStream();
            
            switch (format.ToLowerInvariant())
            {
                case "png":
                    await image.SaveAsPngAsync(outputStream, cancellationToken);
                    break;
                case "jpg":
                case "jpeg":
                    await image.SaveAsJpegAsync(outputStream, cancellationToken);
                    break;
                default:
                    throw new ArgumentException($"Unsupported output format: {format}");
            }

            outputStream.Position = 0;
            return await _fileStorageService.UploadFileAsync(
                outputStream, outputFileName, outputContentType, cancellationToken: cancellationToken);
        }
        catch (Exception ex) when (!(ex is UnsupportedDocumentTypeException || ex is ArgumentException))
        {
            _logger.LogError(ex, "Failed to convert document to image at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("convert to image", location.FilePath, ex);
        }
    }

    public async Task<bool> ValidateDocumentAsync(StorageLocation location, DocumentType expectedType, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            
            return expectedType switch
            {
                DocumentType.PickupConfirmationPhoto or DocumentType.DeliveryPhoto => fileMetadata.IsImage(),
                DocumentType.DigitalInvoice or DocumentType.EWayBill => fileMetadata.IsPdf() || fileMetadata.IsDocument(),
                DocumentType.ProofOfDelivery => fileMetadata.IsPdf() || fileMetadata.IsImage(),
                _ => true // Default to valid for other types
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate document at {Location}", location.GetFullPath());
            return false;
        }
    }

    public async Task<string> CalculateChecksumAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
            using var sha256 = SHA256.Create();
            
            var hashBytes = await sha256.ComputeHashAsync(fileStream, cancellationToken);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to calculate checksum for document at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("calculate checksum", location.FilePath, ex);
        }
    }

    public async Task<string> PerformOCRAsync(StorageLocation location, string? language = null, CancellationToken cancellationToken = default)
    {
        // OCR would require additional libraries like Tesseract.NET
        // For now, return a placeholder
        _logger.LogWarning("OCR requested for {Location} but OCR is not yet implemented", location.GetFullPath());
        return "OCR functionality not yet implemented";
    }

    public async Task<Dictionary<string, object>> ExtractMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var metadata = new Dictionary<string, object>();
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            
            metadata["file_name"] = fileMetadata.FileName;
            metadata["content_type"] = fileMetadata.ContentType;
            metadata["file_size"] = fileMetadata.FileSizeBytes;
            metadata["file_extension"] = fileMetadata.FileExtension;
            metadata["checksum"] = await CalculateChecksumAsync(location, cancellationToken);
            metadata["extracted_at"] = DateTime.UtcNow;

            // Add type-specific metadata
            if (fileMetadata.IsImage())
            {
                try
                {
                    using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
                    using var image = await Image.LoadAsync(fileStream, cancellationToken);
                    
                    metadata["image_width"] = image.Width;
                    metadata["image_height"] = image.Height;
                    metadata["image_format"] = image.Metadata.DecodedImageFormat?.Name ?? "Unknown";
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to extract image metadata for {Location}", location.GetFullPath());
                }
            }

            return metadata;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract metadata from document at {Location}", location.GetFullPath());
            throw new DocumentProcessingException("extract metadata", location.FilePath, ex);
        }
    }

    private async Task<string> ExtractTextFromPlainTextAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        using var fileStream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
        using var reader = new StreamReader(fileStream, Encoding.UTF8);
        return await reader.ReadToEndAsync(cancellationToken);
    }

    private async Task<string> ExtractTextFromPdfAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        // PDF text extraction would require iText7 or similar library
        // For now, return a placeholder
        _logger.LogWarning("PDF text extraction requested for {Location} but is not yet implemented", location.GetFullPath());
        return "PDF text extraction not yet implemented";
    }
}
