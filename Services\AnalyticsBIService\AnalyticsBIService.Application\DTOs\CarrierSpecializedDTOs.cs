using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Carrier broker feedback data transfer object
    /// </summary>
    public class CarrierBrokerFeedbackDto
    {
        public Guid BrokerId { get; set; }
        public string BrokerName { get; set; } = string.Empty;
        public decimal OverallRating { get; set; }
        public decimal PaymentRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal LoadQualityRating { get; set; }
        public string Comments { get; set; } = string.Empty;
        public DateTime FeedbackDate { get; set; }
        public List<FeedbackCategoryDto> CategoryRatings { get; set; } = new();
    }

    /// <summary>
    /// Feedback category data transfer object
    /// </summary>
    public class FeedbackCategoryDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Rating { get; set; }
        public string Comments { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carrier service quality monitoring data transfer object
    /// </summary>
    public class CarrierServiceQualityMonitoringDto
    {
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal DamageRate { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public decimal CommunicationScore { get; set; }
        public List<QualityMetricTrendDto> QualityTrends { get; set; } = new();
        public List<ServiceIssueDto> RecentIssues { get; set; } = new();
        public List<QualityImprovementDto> ImprovementAreas { get; set; } = new();
    }

    /// <summary>
    /// Quality metric trend data transfer object
    /// </summary>
    public class QualityMetricTrendDto
    {
        public DateTime Period { get; set; }
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Quality improvement data transfer object
    /// </summary>
    public class QualityImprovementDto
    {
        public string Area { get; set; } = string.Empty;
        public string ImprovementArea { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal CurrentScore { get; set; }
        public decimal TargetScore { get; set; }
        public decimal PotentialImpact { get; set; }
        public string Priority { get; set; } = string.Empty;
        public List<string> ActionItems { get; set; } = new();
        public string ActionPlan { get; set; } = string.Empty;
        public string Timeline { get; set; } = string.Empty;
        public DateTime TargetDate { get; set; }
    }

    /// <summary>
    /// Carrier route delivery performance data transfer object
    /// </summary>
    public class CarrierRouteDeliveryPerformanceDto
    {
        public string RouteId { get; set; } = string.Empty;
        public string Origin { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public int TotalDeliveries { get; set; }
        public int OnTimeDeliveries { get; set; }
        public decimal OnTimeRate { get; set; }
        public decimal AverageDeliveryTime { get; set; }
        public List<DeliveryPerformanceTrendDto> PerformanceTrends { get; set; } = new();
    }

    /// <summary>
    /// Delivery performance trend data transfer object
    /// </summary>
    public class DeliveryPerformanceTrendDto
    {
        public DateTime Period { get; set; }
        public decimal OnTimeRate { get; set; }
        public decimal AverageDeliveryTime { get; set; }
        public int DeliveryCount { get; set; }
    }

    /// <summary>
    /// Carrier delivery issue data transfer object
    /// </summary>
    public class CarrierDeliveryIssueDto
    {
        public Guid IssueId { get; set; }
        public string IssueType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
        public decimal ImpactScore { get; set; }
        public string RouteId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carrier customer feedback data transfer object
    /// </summary>
    public class CarrierCustomerFeedbackDto
    {
        public Guid CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal OverallRating { get; set; }
        public decimal ServiceRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal TimelinessRating { get; set; }
        public string Comments { get; set; } = string.Empty;
        public DateTime FeedbackDate { get; set; }
        public string LoadId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Vehicle utilization trend data transfer object
    /// </summary>
    public class VehicleUtilizationTrendDto
    {
        public DateTime Period { get; set; }
        public decimal UtilizationRate { get; set; }
        public int TotalVehicles { get; set; }
        public int ActiveVehicles { get; set; }
        public decimal RevenuePerVehicle { get; set; }
        public decimal MileagePerVehicle { get; set; }
    }

    /// <summary>
    /// Fuel consumption trend data transfer object
    /// </summary>
    public class FuelConsumptionTrendDto
    {
        public DateTime Period { get; set; }
        public decimal FuelConsumption { get; set; }
        public decimal FuelCost { get; set; }
        public decimal FuelEfficiency { get; set; }
        public decimal CostPerMile { get; set; }
        public string VehicleType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Route efficiency trend data transfer object
    /// </summary>
    public class RouteEfficiencyTrendDto
    {
        public DateTime Period { get; set; }
        public string RouteId { get; set; } = string.Empty;
        public decimal EfficiencyScore { get; set; }
        public decimal AverageTime { get; set; }
        public decimal FuelConsumption { get; set; }
        public decimal CostPerMile { get; set; }
        public int TripCount { get; set; }
    }

    /// <summary>
    /// Carrier top route data transfer object
    /// </summary>
    public class CarrierTopRouteDto
    {
        public string RouteId { get; set; } = string.Empty;
        public string Origin { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public int TripCount { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageRevenue { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal OnTimeRate { get; set; }
    }

    /// <summary>
    /// Carrier income trend data transfer object
    /// </summary>
    public class CarrierIncomeTrendDto
    {
        public DateTime Period { get; set; }
        public decimal GrossIncome { get; set; }
        public decimal NetIncome { get; set; }
        public decimal OperatingCosts { get; set; }
        public decimal ProfitMargin { get; set; }
        public int LoadCount { get; set; }
        public decimal IncomePerLoad { get; set; }
    }

    /// <summary>
    /// Performance trend data data transfer object
    /// </summary>
    public class PerformanceTrendDataDto
    {
        public DateTime Period { get; set; }
        public DateTime Date { get; set; }
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal Target { get; set; }
        public decimal Variance { get; set; }
        public string Trend { get; set; } = string.Empty;

        // Additional properties for analytics
        public decimal PerformanceScore { get; set; }
    }

    /// <summary>
    /// Earnings trend data data transfer object
    /// </summary>
    public class EarningsTrendDataDto
    {
        public DateTime Period { get; set; }
        public DateTime Date { get; set; }
        public decimal GrossEarnings { get; set; }
        public decimal NetEarnings { get; set; }
        public decimal Expenses { get; set; }
        public decimal ProfitMargin { get; set; }
        public int LoadCount { get; set; }

        // Additional properties for analytics
        public decimal DailyEarnings { get; set; }
    }

    /// <summary>
    /// Order volume trend data transfer object
    /// </summary>
    public class OrderVolumeTrendDto
    {
        public DateTime Period { get; set; }
        public DateTime Date { get; set; }
        public int TotalOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int CancelledOrders { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageOrderValue { get; set; }

        // Additional properties for analytics
        public int OrderVolume { get; set; }
    }

    /// <summary>
    /// Customer satisfaction trend data transfer object
    /// </summary>
    public class CustomerSatisfactionTrendDto
    {
        public DateTime Period { get; set; }
        public DateTime Date { get; set; }
        public decimal SatisfactionScore { get; set; }
        public int ResponseCount { get; set; }
        public decimal ServiceRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal TimelinessRating { get; set; }
    }

    /// <summary>
    /// Order trend data transfer object
    /// </summary>
    public class OrderTrendDto
    {
        public DateTime Date { get; set; }
        public DateTime Period { get; set; }
        public int OrderCount { get; set; }
        public int CompletedCount { get; set; }
        public int TotalOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int PendingOrders { get; set; }
        public int CancelledOrders { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal GrowthRate { get; set; }
    }

    /// <summary>
    /// On-time delivery trend data transfer object
    /// </summary>
    public class OnTimeDeliveryTrendDto
    {
        public DateTime Period { get; set; }
        public DateTime Date { get; set; }
        public int TotalDeliveries { get; set; }
        public int OnTimeDeliveries { get; set; }
        public int LateDeliveries { get; set; }
        public decimal OnTimeRate { get; set; }
        public decimal AverageDelayTime { get; set; }
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carrier delivery trend data transfer object
    /// </summary>
    public class CarrierDeliveryTrendDto
    {
        public DateTime Date { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal EarlyDeliveryRate { get; set; }
        public decimal LateDeliveryRate { get; set; }
        public int TotalDeliveries { get; set; }
        public TimeSpan AverageDeliveryTime { get; set; }
        public decimal DeliveryPerformanceScore { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public List<DeliveryIssueDto> DeliveryIssues { get; set; } = new();
    }

    /// <summary>
    /// Delivery issue data transfer object
    /// </summary>
    public class DeliveryIssueDto
    {
        public string IssueType { get; set; } = string.Empty;
        public int Count { get; set; }
        public int Frequency { get; set; }
        public decimal ImpactScore { get; set; }
        public decimal Impact { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carrier improvement area data transfer object
    /// </summary>
    public class CarrierImprovementAreaDto
    {
        public string ImprovementArea { get; set; } = string.Empty;
        public decimal CurrentScore { get; set; }
        public decimal TargetScore { get; set; }
        public decimal PotentialImpact { get; set; }
        public string Priority { get; set; } = string.Empty;
        public List<string> ActionItems { get; set; } = new();
        public string Timeline { get; set; } = string.Empty;
        public decimal ImplementationCost { get; set; }
        public decimal ExpectedROI { get; set; }
        public List<string> RequiredResources { get; set; } = new();
        public List<ImprovementMilestoneDto> Milestones { get; set; } = new();
    }

    /// <summary>
    /// Improvement milestone data transfer object
    /// </summary>
    public class ImprovementMilestoneDto
    {
        public string MilestoneName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime TargetDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal CompletionPercentage { get; set; }
    }

    /// <summary>
    /// Carrier service quality data transfer object
    /// </summary>
    public class CarrierServiceQualityDto
    {
        public Guid CarrierId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Period { get; set; } = string.Empty;

        // Overall Quality Metrics
        public decimal OverallQualityScore { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal DamageRate { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public decimal CommunicationScore { get; set; }
        public decimal DocumentationAccuracy { get; set; }

        // Service Quality Trends
        public List<ServiceQualityTrendDto> QualityTrends { get; set; } = new();

        // Quality Metrics by Category
        public List<QualityMetricCategoryDto> QualityCategories { get; set; } = new();

        // Recent Service Issues
        public List<ServiceIssueDto> RecentIssues { get; set; } = new();

        // Quality Improvement Areas
        public List<QualityImprovementDto> ImprovementAreas { get; set; } = new();

        // Customer Feedback Summary
        public CustomerFeedbackSummaryDto CustomerFeedback { get; set; } = new();

        // Broker Feedback Summary
        public BrokerFeedbackSummaryDto BrokerFeedback { get; set; } = new();

        // Performance Benchmarking
        public ServiceQualityBenchmarkDto Benchmarking { get; set; } = new();
    }

    /// <summary>
    /// Service quality trend data transfer object
    /// </summary>
    public class ServiceQualityTrendDto
    {
        public DateTime Date { get; set; }
        public decimal QualityScore { get; set; }
        public decimal OnTimeRate { get; set; }
        public decimal CustomerSatisfaction { get; set; }
        public decimal CommunicationScore { get; set; }
        public int IssueCount { get; set; }
    }

    /// <summary>
    /// Quality metric category data transfer object
    /// </summary>
    public class QualityMetricCategoryDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public decimal Weight { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<QualitySubMetricDto> SubMetrics { get; set; } = new();
    }

    /// <summary>
    /// Quality sub-metric data transfer object
    /// </summary>
    public class QualitySubMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal Target { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Trend { get; set; } = string.Empty;
    }

    /// <summary>
    /// Customer feedback summary data transfer object
    /// </summary>
    public class CustomerFeedbackSummaryDto
    {
        public int TotalFeedbackCount { get; set; }
        public decimal AverageRating { get; set; }
        public decimal PositiveFeedbackPercentage { get; set; }
        public decimal NegativeFeedbackPercentage { get; set; }
        public List<FeedbackThemeDto> CommonThemes { get; set; } = new();
        public List<CustomerComplaintDto> RecentComplaints { get; set; } = new();
    }

    /// <summary>
    /// Customer complaint data transfer object
    /// </summary>
    public class CustomerComplaintDto
    {
        public Guid ComplaintId { get; set; }
        public string ComplaintType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime ReportedDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
    }

    /// <summary>
    /// Broker feedback summary data transfer object
    /// </summary>
    public class BrokerFeedbackSummaryDto
    {
        public int TotalFeedbackCount { get; set; }
        public decimal AverageRating { get; set; }
        public decimal PaymentRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal LoadQualityRating { get; set; }
        public List<BrokerFeedbackDto> RecentFeedback { get; set; } = new();
        public List<FeedbackThemeDto> CommonThemes { get; set; } = new();
    }

    /// <summary>
    /// Broker feedback data transfer object
    /// </summary>
    public class BrokerFeedbackDto
    {
        public Guid FeedbackId { get; set; }
        public Guid BrokerId { get; set; }
        public string BrokerName { get; set; } = string.Empty;
        public decimal OverallRating { get; set; }
        public decimal PaymentRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal LoadQualityRating { get; set; }
        public string Comments { get; set; } = string.Empty;
        public DateTime FeedbackDate { get; set; }
        public string FeedbackType { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
    }

    /// <summary>
    /// Service quality benchmark data transfer object
    /// </summary>
    public class ServiceQualityBenchmarkDto
    {
        public decimal IndustryAverageQuality { get; set; }
        public decimal TopPerformerQuality { get; set; }
        public decimal CarrierRanking { get; set; }
        public string PerformanceTier { get; set; } = string.Empty;
        public List<BenchmarkMetricDto> BenchmarkMetrics { get; set; } = new();
    }

    /// <summary>
    /// Benchmark metric data transfer object
    /// </summary>

}
