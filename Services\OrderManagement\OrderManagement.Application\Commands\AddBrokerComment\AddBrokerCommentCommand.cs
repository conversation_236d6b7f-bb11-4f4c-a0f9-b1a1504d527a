using MediatR;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AddBrokerComment;

public class AddBrokerCommentCommand : IRequest<Guid>
{
    public Guid RfqId { get; set; }
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public BrokerCommentType CommentType { get; set; } = BrokerCommentType.General;
    public bool IsInternal { get; set; } = true;
    public bool IsVisible { get; set; } = true;
    public Guid? ParentCommentId { get; set; }
    public string? Tags { get; set; }
    public int Priority { get; set; } = 0;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
