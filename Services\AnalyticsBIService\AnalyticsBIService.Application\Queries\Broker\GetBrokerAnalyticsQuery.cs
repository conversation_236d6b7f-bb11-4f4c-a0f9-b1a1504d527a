using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Broker;

/// <summary>
/// Query to get broker operational dashboard
/// </summary>
public record GetBrokerDashboardQuery(
    Guid BrokerId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<BrokerDashboardDto>;

/// <summary>
/// Query to get broker operational analytics
/// </summary>
public record GetBrokerOperationalAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeRFQAnalysis = true,
    bool IncludeQuoteAnalysis = true,
    bool IncludeCarrierNetworkAnalysis = true
) : IRequest<BrokerOperationalAnalyticsDto>;

/// <summary>
/// Query to get RFQ conversion analytics for broker
/// </summary>
public record GetBrokerRFQConversionAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    Guid? TransportCompanyId = null,
    string? RouteType = null
) : IRequest<BrokerRFQConversionAnalyticsDto>;

/// <summary>
/// Query to get quote success analytics for broker
/// </summary>
public record GetBrokerQuoteSuccessAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? QuoteType = null,
    string? ServiceLevel = null
) : IRequest<BrokerQuoteSuccessAnalyticsDto>;

/// <summary>
/// Query to get carrier network utilization for broker
/// </summary>
public record GetCarrierNetworkUtilizationQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    List<Guid>? CarrierIds = null,
    string? CarrierType = null
) : IRequest<CarrierNetworkUtilizationDto>;

/// <summary>
/// Query to get broker margin analysis
/// </summary>
public record GetBrokerMarginAnalysisQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeProfitabilityMetrics = true,
    bool IncludeCommissionTracking = true
) : IRequest<BrokerMarginAnalysisDto>;

/// <summary>
/// Query to get broker business growth analytics
/// </summary>
public record GetBrokerBusinessGrowthAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeMarketOpportunities = true,
    bool IncludeExpansionPotential = true
) : IRequest<BrokerBusinessGrowthAnalyticsDto>;

/// <summary>
/// Query to get broker performance tracking
/// </summary>
public record GetBrokerPerformanceTrackingQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeTripCompletionRates = true,
    bool IncludeDeliveryPerformance = true
) : IRequest<BrokerPerformanceTrackingDto>;

/// <summary>
/// Query to get carrier network growth analytics for broker
/// </summary>
public record GetCarrierNetworkGrowthAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludePerformanceTrends = true,
    bool IncludeQualityMetrics = true
) : IRequest<CarrierNetworkGrowthAnalyticsDto>;

/// <summary>
/// Query to get broker efficiency metrics
/// </summary>
public record GetBrokerEfficiencyMetricsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeProcessEfficiency = true,
    bool IncludeResourceUtilization = true
) : IRequest<BrokerEfficiencyMetricsDto>;

/// <summary>
/// Query to get broker customer satisfaction analytics
/// </summary>
public record GetBrokerCustomerSatisfactionQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeFeedbackAnalysis = true,
    bool IncludeServiceQuality = true
) : IRequest<BrokerCustomerSatisfactionDto>;

/// <summary>
/// Query to get broker competitive analysis
/// </summary>
public record GetBrokerCompetitiveAnalysisQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null,
    bool IncludeMarketShare = true
) : IRequest<BrokerCompetitiveAnalysisDto>;

/// <summary>
/// Query to get broker route optimization analytics
/// </summary>
public record GetBrokerRouteOptimizationQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    string? OriginCity = null,
    string? DestinationCity = null,
    bool IncludeEfficiencyMetrics = true
) : IRequest<BrokerRouteOptimizationDto>;

/// <summary>
/// Query to get broker capacity management analytics
/// </summary>
public record GetBrokerCapacityManagementQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeUtilizationMetrics = true,
    bool IncludeDemandForecasting = true
) : IRequest<BrokerCapacityManagementDto>;

/// <summary>
/// Query to get broker financial performance
/// </summary>
public record GetBrokerFinancialPerformanceQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeRevenueBreakdown = true,
    bool IncludeCommissionAnalysis = true,
    bool IncludeProfitabilityAnalysis = true
) : IRequest<BrokerFinancialPerformanceDto>;

/// <summary>
/// Query to get broker service level analytics
/// </summary>
public record GetBrokerServiceLevelAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? ServiceType = null,
    bool IncludeSLACompliance = true
) : IRequest<BrokerServiceLevelAnalyticsDto>;

/// <summary>
/// Query to get broker risk management analytics
/// </summary>
public record GetBrokerRiskManagementQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeCarrierRisk = true,
    bool IncludeOperationalRisk = true,
    bool IncludeFinancialRisk = true
) : IRequest<BrokerRiskManagementDto>;

/// <summary>
/// Query to get broker technology utilization analytics
/// </summary>
public record GetBrokerTechnologyUtilizationQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeSystemUsage = true,
    bool IncludeAutomationMetrics = true
) : IRequest<BrokerTechnologyUtilizationDto>;

/// <summary>
/// Query to get broker market intelligence
/// </summary>
public record GetBrokerMarketIntelligenceQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null,
    bool IncludeTrendAnalysis = true
) : IRequest<BrokerMarketIntelligenceDto>;

/// <summary>
/// Query to get broker compliance analytics
/// </summary>
public record GetBrokerComplianceAnalyticsQuery(
    Guid BrokerId,
    DateTime FromDate,
    DateTime ToDate,
    string? ComplianceType = null,
    string? Region = null,
    bool IncludeAuditTrail = true
) : IRequest<BrokerComplianceAnalyticsDto>;
