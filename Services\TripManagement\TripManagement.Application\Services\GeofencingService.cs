using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;
using TripManagement.Domain.Enums;
using TripManagement.Application.Interfaces;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Services;

public interface IGeofencingService
{
    Task<GeofenceCheckResult> CheckGeofenceStatusAsync(Guid tripId, Location currentLocation, CancellationToken cancellationToken = default);
    Task<List<GeofenceZone>> GetActiveGeofenceZonesAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<GeofenceZone> CreateGeofenceZoneAsync(CreateGeofenceZoneRequest request, CancellationToken cancellationToken = default);
    Task UpdateGeofenceZoneAsync(Guid zoneId, UpdateGeofenceZoneRequest request, CancellationToken cancellationToken = default);
    Task DeleteGeofenceZoneAsync(Guid zoneId, CancellationToken cancellationToken = default);
    Task<List<GeofenceEvent>> GetGeofenceEventsAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task ProcessLocationUpdateAsync(Guid tripId, Location newLocation, Location? previousLocation, CancellationToken cancellationToken = default);
}

public class GeofencingService : IGeofencingService
{
    private readonly ILogger<GeofencingService> _logger;
    private readonly ITripRepository _tripRepository;
    private readonly IGeofenceRepository _geofenceRepository;
    private readonly INotificationService _notificationService;
    private readonly IMessageBroker _messageBroker;
    private readonly GeofencingOptions _options;

    public GeofencingService(
        ILogger<GeofencingService> logger,
        ITripRepository tripRepository,
        IGeofenceRepository geofenceRepository,
        INotificationService notificationService,
        IMessageBroker messageBroker,
        IOptions<GeofencingOptions> options)
    {
        _logger = logger;
        _tripRepository = tripRepository;
        _geofenceRepository = geofenceRepository;
        _notificationService = notificationService;
        _messageBroker = messageBroker;
        _options = options.Value;
    }

    public async Task<GeofenceCheckResult> CheckGeofenceStatusAsync(Guid tripId, Location currentLocation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking geofence status for trip {TripId} at location {Location}", 
                tripId, currentLocation);

            var activeZones = await GetActiveGeofenceZonesAsync(tripId, cancellationToken);
            var result = new GeofenceCheckResult
            {
                TripId = tripId,
                CurrentLocation = currentLocation,
                CheckedAt = DateTime.UtcNow,
                GeofenceStatuses = new List<GeofenceZoneStatus>()
            };

            foreach (var zone in activeZones)
            {
                var status = CheckLocationInZone(currentLocation, zone);
                result.GeofenceStatuses.Add(status);

                if (status.Status != GeofenceStatus.Outside)
                {
                    result.IsInsideAnyZone = true;
                    if (zone.ZoneType == GeofenceZoneType.Pickup || zone.ZoneType == GeofenceZoneType.Delivery)
                    {
                        result.IsAtCriticalLocation = true;
                    }
                }
            }

            _logger.LogInformation("Geofence check completed for trip {TripId}: Inside {ZoneCount} zones", 
                tripId, result.GeofenceStatuses.Count(s => s.Status != GeofenceStatus.Outside));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking geofence status for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<List<GeofenceZone>> GetActiveGeofenceZonesAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        try
        {
            var trip = await _tripRepository.GetByIdWithStopsAsync(tripId, cancellationToken);
            if (trip == null)
                return new List<GeofenceZone>();

            var zones = new List<GeofenceZone>();

            // Add zones for trip stops
            foreach (var stop in trip.Stops.Where(s => s.Status == TripStopStatus.Pending))
            {
                var zoneType = stop.StopType == TripStopType.Pickup 
                    ? GeofenceZoneType.Pickup 
                    : GeofenceZoneType.Delivery;

                zones.Add(new GeofenceZone
                {
                    Id = Guid.NewGuid(),
                    Name = $"{stop.StopType} - {stop.Address}",
                    ZoneType = zoneType,
                    CenterLocation = stop.Location,
                    RadiusMeters = _options.DefaultStopRadiusMeters,
                    IsActive = true,
                    TripId = tripId,
                    StopId = stop.Id,
                    CreatedAt = DateTime.UtcNow
                });
            }

            // Get custom geofence zones from repository
            var customZones = await _geofenceRepository.GetByTripIdAsync(tripId, cancellationToken);
            zones.AddRange(customZones.Where(z => z.IsActive));

            return zones;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active geofence zones for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<GeofenceZone> CreateGeofenceZoneAsync(CreateGeofenceZoneRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating geofence zone {Name} for trip {TripId}", 
                request.Name, request.TripId);

            var zone = new GeofenceZone
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                ZoneType = request.ZoneType,
                CenterLocation = request.CenterLocation,
                RadiusMeters = request.RadiusMeters,
                IsActive = true,
                TripId = request.TripId,
                StopId = request.StopId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                Metadata = request.Metadata
            };

            await _geofenceRepository.AddAsync(zone, cancellationToken);

            _logger.LogInformation("Geofence zone {ZoneId} created successfully", zone.Id);
            return zone;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating geofence zone for trip {TripId}", request.TripId);
            throw;
        }
    }

    public async Task UpdateGeofenceZoneAsync(Guid zoneId, UpdateGeofenceZoneRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating geofence zone {ZoneId}", zoneId);

            var zone = await _geofenceRepository.GetByIdAsync(zoneId, cancellationToken);
            if (zone == null)
                throw new ArgumentException($"Geofence zone {zoneId} not found");

            zone.UpdateDetails(
                request.Name ?? zone.Name,
                request.Description ?? zone.Description,
                request.CenterLocation ?? zone.CenterLocation,
                request.RadiusMeters ?? zone.RadiusMeters,
                request.IsActive ?? zone.IsActive);

            await _geofenceRepository.UpdateAsync(zone, cancellationToken);

            _logger.LogInformation("Geofence zone {ZoneId} updated successfully", zoneId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating geofence zone {ZoneId}", zoneId);
            throw;
        }
    }

    public async Task DeleteGeofenceZoneAsync(Guid zoneId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting geofence zone {ZoneId}", zoneId);

            var zone = await _geofenceRepository.GetByIdAsync(zoneId, cancellationToken);
            if (zone == null)
                throw new ArgumentException($"Geofence zone {zoneId} not found");

            await _geofenceRepository.DeleteAsync(zone, cancellationToken);

            _logger.LogInformation("Geofence zone {ZoneId} deleted successfully", zoneId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting geofence zone {ZoneId}", zoneId);
            throw;
        }
    }

    public async Task<List<GeofenceEvent>> GetGeofenceEventsAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _geofenceRepository.GetEventsByTripIdAsync(tripId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geofence events for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task ProcessLocationUpdateAsync(Guid tripId, Location newLocation, Location? previousLocation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Processing location update for trip {TripId}", tripId);

            var activeZones = await GetActiveGeofenceZonesAsync(tripId, cancellationToken);
            var events = new List<GeofenceEvent>();

            foreach (var zone in activeZones)
            {
                var currentStatus = CheckLocationInZone(newLocation, zone);
                var previousStatus = previousLocation != null 
                    ? CheckLocationInZone(previousLocation, zone) 
                    : new GeofenceZoneStatus { Status = GeofenceStatus.Unknown };

                // Detect status changes
                if (currentStatus.Status != previousStatus.Status)
                {
                    var eventType = DetermineEventType(previousStatus.Status, currentStatus.Status);
                    if (eventType != GeofenceEventType.None)
                    {
                        var geofenceEvent = new GeofenceEvent
                        {
                            Id = Guid.NewGuid(),
                            TripId = tripId,
                            ZoneId = zone.Id,
                            ZoneName = zone.Name,
                            EventType = eventType,
                            Location = newLocation,
                            PreviousStatus = previousStatus.Status,
                            NewStatus = currentStatus.Status,
                            Timestamp = DateTime.UtcNow,
                            DistanceFromCenter = currentStatus.DistanceFromCenterMeters
                        };

                        events.Add(geofenceEvent);
                        await _geofenceRepository.AddEventAsync(geofenceEvent, cancellationToken);

                        // Send notifications for critical events
                        if (ShouldNotify(eventType, zone.ZoneType))
                        {
                            await SendGeofenceNotificationAsync(geofenceEvent, zone, cancellationToken);
                        }
                    }
                }
            }

            if (events.Any())
            {
                _logger.LogInformation("Processed {EventCount} geofence events for trip {TripId}", 
                    events.Count, tripId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing location update for trip {TripId}", tripId);
            // Don't throw - this shouldn't break location updates
        }
    }

    private GeofenceZoneStatus CheckLocationInZone(Location location, GeofenceZone zone)
    {
        var distanceMeters = location.CalculateDistanceKm(zone.CenterLocation) * 1000;
        var status = GeofenceStatus.Outside;

        if (distanceMeters <= zone.RadiusMeters)
        {
            status = GeofenceStatus.Inside;
        }
        else if (distanceMeters <= zone.RadiusMeters + _options.NearbyThresholdMeters)
        {
            status = GeofenceStatus.Nearby;
        }

        return new GeofenceZoneStatus
        {
            ZoneId = zone.Id,
            ZoneName = zone.Name,
            Status = status,
            DistanceFromCenterMeters = distanceMeters,
            CheckedAt = DateTime.UtcNow
        };
    }

    private GeofenceEventType DetermineEventType(GeofenceStatus previousStatus, GeofenceStatus currentStatus)
    {
        return (previousStatus, currentStatus) switch
        {
            (GeofenceStatus.Outside, GeofenceStatus.Nearby) => GeofenceEventType.Approaching,
            (GeofenceStatus.Outside, GeofenceStatus.Inside) => GeofenceEventType.Entered,
            (GeofenceStatus.Nearby, GeofenceStatus.Inside) => GeofenceEventType.Entered,
            (GeofenceStatus.Inside, GeofenceStatus.Nearby) => GeofenceEventType.Exited,
            (GeofenceStatus.Inside, GeofenceStatus.Outside) => GeofenceEventType.Exited,
            (GeofenceStatus.Nearby, GeofenceStatus.Outside) => GeofenceEventType.LeftArea,
            _ => GeofenceEventType.None
        };
    }

    private bool ShouldNotify(GeofenceEventType eventType, GeofenceZoneType zoneType)
    {
        return eventType switch
        {
            GeofenceEventType.Entered when zoneType is GeofenceZoneType.Pickup or GeofenceZoneType.Delivery => true,
            GeofenceEventType.Exited when zoneType is GeofenceZoneType.Pickup or GeofenceZoneType.Delivery => true,
            GeofenceEventType.Approaching when zoneType is GeofenceZoneType.Pickup or GeofenceZoneType.Delivery => true,
            _ => false
        };
    }

    private async Task SendGeofenceNotificationAsync(GeofenceEvent geofenceEvent, GeofenceZone zone, CancellationToken cancellationToken)
    {
        try
        {
            var message = geofenceEvent.EventType switch
            {
                GeofenceEventType.Approaching => $"Vehicle approaching {zone.Name}",
                GeofenceEventType.Entered => $"Vehicle entered {zone.Name}",
                GeofenceEventType.Exited => $"Vehicle exited {zone.Name}",
                _ => $"Geofence event: {geofenceEvent.EventType} at {zone.Name}"
            };

            // Send notification
            await _notificationService.SendGeofenceNotificationAsync(geofenceEvent.TripId, message, cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("geofence.event", new
            {
                geofenceEvent.TripId,
                geofenceEvent.ZoneId,
                geofenceEvent.ZoneName,
                EventType = geofenceEvent.EventType.ToString(),
                geofenceEvent.Location,
                geofenceEvent.Timestamp,
                Message = message
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending geofence notification for event {EventId}", geofenceEvent.Id);
        }
    }
}
