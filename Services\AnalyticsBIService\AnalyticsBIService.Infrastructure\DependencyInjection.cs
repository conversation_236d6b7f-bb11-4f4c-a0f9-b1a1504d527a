using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Infrastructure.Persistence;
using AnalyticsBIService.Infrastructure.Repositories;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Infrastructure.Services;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Interfaces;
using Shared.Messaging;
using StackExchange.Redis;

namespace AnalyticsBIService.Infrastructure;

/// <summary>
/// Infrastructure layer dependency injection configuration
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add infrastructure services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database Configuration
        services.AddDbContext<AnalyticsBIDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);

                // TimescaleDB will be enabled via SQL scripts
            });

            // Enable sensitive data logging in development
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            // Enable detailed errors in development
            if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // Repository Pattern
        services.AddScoped<AnalyticsBIService.Domain.Repositories.IUnitOfWork, UnitOfWork>();
        services.AddScoped<IAnalyticsEventRepository, AnalyticsEventRepository>();
        services.AddScoped<IMetricRepository, MetricRepository>();
        services.AddScoped<IDashboardRepository, DashboardRepository>();
        services.AddScoped<IReportRepository, ReportRepository>();
        services.AddScoped<IAlertRepository, AlertRepository>();
        services.AddScoped<IThresholdRepository, ThresholdRepository>();
        services.AddScoped<AnalyticsBIService.Domain.Repositories.IReportTemplateRepository, ReportTemplateRepository>();
        services.AddScoped<IReportExecutionRepository, ReportExecutionRepository>();
        services.AddScoped<IDashboardWidgetRepository, DashboardWidgetRepository>();
        services.AddScoped<IReportSectionRepository, ReportSectionRepository>();
        services.AddScoped<IReportExportRepository, ReportExportRepository>();
        services.AddScoped<IAlertRuleRepository, AlertRuleRepository>();
        services.AddScoped<AnalyticsBIService.Domain.Repositories.IOrderDataRepository, OrderDataRepository>();
        services.AddScoped<AnalyticsBIService.Domain.Repositories.ITripDataRepository, TripDataRepository>();
        services.AddScoped<AnalyticsBIService.Domain.Repositories.IExportRepository, ExportRepository>();
        services.AddScoped<IDataPipelineRepository, DataPipelineRepository>();

        // Redis Cache Configuration
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = configuration.GetConnectionString("Redis");
            options.InstanceName = "AnalyticsBIService";
        });

        // Redis Connection for advanced operations
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis");
            return ConnectionMultiplexer.Connect(connectionString);
        });

        // Infrastructure Services
        services.AddScoped<AnalyticsBIService.Infrastructure.Services.ICacheService, CacheService>();
        services.AddScoped<AnalyticsBIService.Application.Interfaces.IDataAggregationService, DataAggregationService>();
        services.AddScoped<AnalyticsBIService.Application.Interfaces.IReportGenerationService, ReportGenerationService>();
        services.AddScoped<AnalyticsBIService.Application.Interfaces.INotificationService, NotificationService>();
        services.AddScoped<AnalyticsBIService.Application.Interfaces.IFileStorageService, FileStorageService>();

        // Shared Infrastructure Services
        services.AddScoped<Shared.Infrastructure.Caching.ICachingService, Shared.Infrastructure.Caching.CachingService>();
        services.AddScoped<Shared.Infrastructure.Monitoring.IPerformanceMetrics, Shared.Infrastructure.Monitoring.PerformanceMetrics>();

        // Background Services
        services.AddHostedService<AnalyticsBIService.Infrastructure.BackgroundServices.MetricsProcessingService>();
        services.AddHostedService<AnalyticsBIService.Infrastructure.BackgroundServices.AlertProcessingService>();
        services.AddHostedService<AnalyticsBIService.Infrastructure.BackgroundServices.ReportSchedulingService>();
        services.AddHostedService<AnalyticsBIService.Infrastructure.BackgroundServices.DataRetentionService>();

        // Health Checks
        var healthChecks = services.AddHealthChecks();
        healthChecks.AddDbContextCheck<AnalyticsBIDbContext>();
        if (!string.IsNullOrEmpty(configuration.GetConnectionString("Redis")))
        {
            healthChecks.AddRedis(configuration.GetConnectionString("Redis"));
        }

        return services;
    }

    /// <summary>
    /// Add TimescaleDB specific configurations
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddTimescaleDB(this IServiceCollection services, IConfiguration configuration)
    {
        // TimescaleDB specific configurations
        services.Configure<TimescaleDBOptions>(configuration.GetSection("TimescaleDB"));

        return services;
    }
}

/// <summary>
/// TimescaleDB configuration options
/// </summary>
public class TimescaleDBOptions
{
    /// <summary>
    /// Enable automatic hypertable creation
    /// </summary>
    public bool EnableAutoHypertables { get; set; } = true;

    /// <summary>
    /// Default chunk time interval for hypertables
    /// </summary>
    public TimeSpan DefaultChunkTimeInterval { get; set; } = TimeSpan.FromDays(7);

    /// <summary>
    /// Enable compression for hypertables
    /// </summary>
    public bool EnableCompression { get; set; } = true;

    /// <summary>
    /// Compression after interval
    /// </summary>
    public TimeSpan CompressionAfter { get; set; } = TimeSpan.FromDays(30);

    /// <summary>
    /// Data retention period
    /// </summary>
    public TimeSpan DataRetentionPeriod { get; set; } = TimeSpan.FromDays(365);
}
