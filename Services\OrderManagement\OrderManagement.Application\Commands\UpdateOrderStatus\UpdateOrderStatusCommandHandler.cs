using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.UpdateOrderStatus;

public class UpdateOrderStatusCommandHandler : IRequestHandler<UpdateOrderStatusCommand, bool>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateOrderStatusCommandHandler> _logger;

    public UpdateOrderStatusCommandHandler(
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<UpdateOrderStatusCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateOrderStatusCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating order {OrderId} status to {NewStatus} by user {UserId}",
            request.OrderId, request.NewStatus, request.UserId);

        var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);

        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found", request.OrderId);
            throw new OrderManagementException("Order not found");
        }

        // Authorization check - only involved parties can update status
        if (order.TransportCompanyId != request.UserId &&
            order.BrokerId != request.UserId &&
            order.CarrierId != request.UserId)
        {
            _logger.LogWarning("User {UserId} is not authorized to update order {OrderId} status",
                request.UserId, request.OrderId);
            throw new OrderManagementException("Not authorized to update this order status");
        }

        // Validate status transition
        if (!IsValidStatusTransition(order.Status, request.NewStatus))
        {
            _logger.LogWarning("Invalid status transition from {CurrentStatus} to {NewStatus} for order {OrderId}",
                order.Status, request.NewStatus, request.OrderId);
            throw new OrderManagementException($"Invalid status transition from {order.Status} to {request.NewStatus}");
        }

        try
        {
            var previousStatus = order.Status;

            // Apply status change based on the new status
            switch (request.NewStatus)
            {
                case OrderStatus.InProgress:
                    if (order.Status == OrderStatus.Confirmed)
                    {
                        order.StartProgress();
                    }
                    else
                    {
                        throw new OrderManagementException("Can only start progress on confirmed orders");
                    }
                    break;

                case OrderStatus.Completed:
                    if (order.Status == OrderStatus.InProgress)
                    {
                        order.Complete();
                    }
                    else
                    {
                        throw new OrderManagementException("Can only complete orders that are in progress");
                    }
                    break;

                case OrderStatus.Cancelled:
                    if (!string.IsNullOrWhiteSpace(request.Notes))
                    {
                        order.Cancel(request.Notes);
                    }
                    else
                    {
                        throw new OrderManagementException("Cancellation reason is required");
                    }
                    break;

                case OrderStatus.OnHold:
                    // For now, we'll handle OnHold as a simple status update
                    // In a more complex system, this might require additional business logic
                    break;

                default:
                    throw new OrderManagementException($"Status update to {request.NewStatus} is not supported through this endpoint");
            }

            // Add timeline event with actor information
            order.AddTimelineEvent(
                Domain.Enums.OrderTimelineEventType.StatusChanged,
                $"Order status updated from {previousStatus} to {request.NewStatus}",
                actorId: request.UserId,
                actorRole: "User", // This could be enhanced to get actual role
                previousStatus: previousStatus,
                newStatus: request.NewStatus,
                notes: request.Notes,
                additionalData: $"Updated via API by user {request.UserId}");

            // Update repository
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("order.status.updated", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                PreviousStatus = previousStatus.ToString(),
                NewStatus = request.NewStatus.ToString(),
                UpdatedBy = request.UserId,
                UpdatedAt = DateTime.UtcNow,
                Notes = request.Notes
            }, cancellationToken);

            _logger.LogInformation("Successfully updated order {OrderId} status from {PreviousStatus} to {NewStatus}",
                request.OrderId, previousStatus, request.NewStatus);

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to update order {OrderId} status: {Message}",
                request.OrderId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating order {OrderId} status", request.OrderId);
            throw;
        }
    }

    private static bool IsValidStatusTransition(OrderStatus currentStatus, OrderStatus newStatus)
    {
        return currentStatus switch
        {
            OrderStatus.Created => newStatus is OrderStatus.Confirmed or OrderStatus.Cancelled,
            OrderStatus.Confirmed => newStatus is OrderStatus.InProgress or OrderStatus.Cancelled or OrderStatus.OnHold,
            OrderStatus.InProgress => newStatus is OrderStatus.Completed or OrderStatus.Cancelled or OrderStatus.OnHold,
            OrderStatus.OnHold => newStatus is OrderStatus.InProgress or OrderStatus.Cancelled,
            OrderStatus.Completed => false, // Completed orders cannot change status
            OrderStatus.Cancelled => false, // Cancelled orders cannot change status
            _ => false
        };
    }
}
