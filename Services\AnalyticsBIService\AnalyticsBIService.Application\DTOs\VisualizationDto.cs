using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Visualization request data transfer object
/// </summary>
public class VisualizationRequest
{
    public string ChartType { get; set; } = string.Empty; // Bar, Line, Pie, Scatter, etc.
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateRangeDto? DateRange { get; set; }
    public List<FilterDto> Filters { get; set; } = new();
    public Guid RequestedBy { get; set; }

    // Properties required by DataVisualizationService
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Customizations { get; set; } = new();
}

/// <summary>
/// Dashboard request data transfer object
/// </summary>
public class DashboardRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Layout { get; set; } = string.Empty; // Grid, Flexible, Fixed
    public List<VisualizationRequest> Visualizations { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public Guid RequestedBy { get; set; }

    // Properties required by DataVisualizationService
    public string Title { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public Dictionary<string, object> GlobalFilters { get; set; } = new();
    public int RefreshInterval { get; set; } = 300; // seconds
    public bool IsPublic { get; set; } = false;
    public List<Dictionary<string, object>> Interactions { get; set; } = new();
}

/// <summary>
/// Visualization template data transfer object
/// </summary>
public class VisualizationTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // Financial, Operational, Performance, etc.
    public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
    public List<string> RequiredMetrics { get; set; } = new();
    public List<string> OptionalMetrics { get; set; } = new();
    public bool IsPublic { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }

    // Properties required by DataVisualizationService
    public string[] ChartTypes { get; set; } = Array.Empty<string>();
    public string[] DataRequirements { get; set; } = Array.Empty<string>();
    public string UseCase { get; set; } = string.Empty;
    public string CreatedByName { get; set; } = string.Empty;
    public int UsageCount { get; set; }
}

/// <summary>
/// Filter data transfer object
/// </summary>
public class FilterDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty; // Equals, Contains, GreaterThan, etc.
    public object Value { get; set; } = new();
    public string LogicalOperator { get; set; } = "AND"; // AND, OR
}

/// <summary>
/// Export format data transfer object
/// </summary>
public class ExportFormatDto
{
    public ExportFormat Format { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public string FileExtension { get; set; } = string.Empty;
    public string Extension { get; set; } = string.Empty;
    public int MaxRecords { get; set; }
    public Dictionary<string, object> Options { get; set; } = new();
}
