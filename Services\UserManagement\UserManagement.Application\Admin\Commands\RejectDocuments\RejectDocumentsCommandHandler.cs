using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using Shared.Messaging;

namespace UserManagement.Application.Admin.Commands.RejectDocuments
{
    public class RejectDocumentsCommandHandler : IRe<PERSON>Handler<RejectDocumentsCommand, RejectDocumentsResponse>
    {
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<RejectDocumentsCommandHandler> _logger;

        public RejectDocumentsCommandHandler(
            IDocumentSubmissionRepository documentSubmissionRepository,
            IMessageBroker messageBroker,
            ILogger<RejectDocumentsCommandHandler> logger)
        {
            _documentSubmissionRepository = documentSubmissionRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<RejectDocumentsResponse> Handle(RejectDocumentsCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing bulk document rejection for SubmissionId: {SubmissionId} by {RejectedBy}", 
                request.SubmissionId, request.RejectedBy);

            try
            {
                // Get the document submission
                var submission = await _documentSubmissionRepository.GetByIdAsync(request.SubmissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Document submission not found for SubmissionId: {SubmissionId}", request.SubmissionId);
                    return new RejectDocumentsResponse
                    {
                        Success = false,
                        Message = "Document submission not found"
                    };
                }

                var results = new List<DocumentRejectionResult>();

                // Process each document rejection
                foreach (var docRequest in request.Documents)
                {
                    try
                    {
                        if (docRequest.RequireResubmission)
                        {
                            submission.RequestDocumentResubmission(docRequest.DocumentType, docRequest.RejectionReason, docRequest.ReviewNotes);
                        }
                        else
                        {
                            submission.RejectDocument(docRequest.DocumentType, docRequest.RejectionReason, docRequest.ReviewNotes);
                        }

                        results.Add(new DocumentRejectionResult
                        {
                            DocumentType = docRequest.DocumentType,
                            Success = true,
                            Message = docRequest.RequireResubmission ? "Document marked for resubmission" : "Document rejected"
                        });

                        _logger.LogInformation("Processed document {DocumentType} for submission {SubmissionId}: {Action}", 
                            docRequest.DocumentType, request.SubmissionId, 
                            docRequest.RequireResubmission ? "Resubmission Required" : "Rejected");
                    }
                    catch (UserManagementDomainException ex)
                    {
                        results.Add(new DocumentRejectionResult
                        {
                            DocumentType = docRequest.DocumentType,
                            Success = false,
                            Message = ex.Message
                        });

                        _logger.LogWarning("Failed to process document {DocumentType}: {Message}", 
                            docRequest.DocumentType, ex.Message);
                    }
                }

                // Update the submission
                await _documentSubmissionRepository.UpdateAsync(submission);

                // Publish integration event
                await _messageBroker.PublishAsync("documents.bulk.rejected", new
                {
                    SubmissionId = request.SubmissionId,
                    UserId = submission.UserId,
                    UserType = submission.UserType.ToString(),
                    RejectedBy = request.RejectedBy,
                    RejectedDocuments = results.Where(r => r.Success).Select(r => new { 
                        DocumentType = r.DocumentType.ToString(), 
                        Action = r.Message 
                    }).ToList(),
                    ProcessedAt = DateTime.UtcNow,
                    Notes = request.Notes
                });

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                _logger.LogInformation("Bulk document rejection completed: {SuccessCount}/{TotalCount} documents processed", 
                    successCount, totalCount);

                return new RejectDocumentsResponse
                {
                    Success = successCount > 0,
                    Message = $"Processed {totalCount} documents. {successCount} processed successfully.",
                    Results = results,
                    ProcessedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing bulk document rejection for submission {SubmissionId}", request.SubmissionId);
                return new RejectDocumentsResponse
                {
                    Success = false,
                    Message = "An error occurred while processing document rejections"
                };
            }
        }
    }
}
