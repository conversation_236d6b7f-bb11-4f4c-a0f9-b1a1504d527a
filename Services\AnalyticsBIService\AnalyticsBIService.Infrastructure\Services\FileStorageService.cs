using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// File storage service implementation
/// </summary>
public class FileStorageService : IFileStorageService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<FileStorageService> _logger;
    private readonly string _basePath;

    public FileStorageService(
        IConfiguration configuration,
        ILogger<FileStorageService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _basePath = _configuration.GetValue<string>("FileStorage:BasePath") ?? Path.Combine(Directory.GetCurrentDirectory(), "Storage");

        // Ensure base directory exists
        Directory.CreateDirectory(_basePath);
    }

    /// <summary>
    /// Save file to storage
    /// </summary>
    public async Task<string> SaveFileAsync(
        string fileName,
        byte[] content,
        string contentType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = Path.Combine(_basePath, fileName);
            var directory = Path.GetDirectoryName(filePath);

            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllBytesAsync(filePath, content, cancellationToken);

            _logger.LogInformation("File saved successfully: {FileName} ({Size} bytes)", fileName, content.Length);

            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving file: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// Save file from stream
    /// </summary>
    public async Task<string> SaveFileAsync(
        string fileName,
        Stream content,
        string contentType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = Path.Combine(_basePath, fileName);
            var directory = Path.GetDirectoryName(filePath);

            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            await content.CopyToAsync(fileStream, cancellationToken);

            _logger.LogInformation("File saved successfully from stream: {FileName}", fileName);

            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving file from stream: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// Get file content as bytes
    /// </summary>
    public async Task<byte[]> GetFileAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var content = await File.ReadAllBytesAsync(filePath, cancellationToken);

            _logger.LogDebug("File retrieved successfully: {FilePath} ({Size} bytes)", filePath, content.Length);

            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving file: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Get file content as stream
    /// </summary>
    public async Task<Stream> GetFileStreamAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

            _logger.LogDebug("File stream opened successfully: {FilePath}", filePath);

            return stream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opening file stream: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Delete file from storage
    /// </summary>
    public async Task<bool> DeleteFileAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("File not found for deletion: {FilePath}", filePath);
                return false;
            }

            File.Delete(filePath);

            _logger.LogInformation("File deleted successfully: {FilePath}", filePath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Check if file exists
    /// </summary>
    public async Task<bool> FileExistsAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return File.Exists(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking file existence: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Get file information
    /// </summary>
    public async Task<FileInfo?> GetFileInfoAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            return new FileInfo(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file info: {FilePath}", filePath);
            return null;
        }
    }

    /// <summary>
    /// List files in directory
    /// </summary>
    public async Task<List<string>> ListFilesAsync(
        string directoryPath,
        string searchPattern = "*",
        CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, directoryPath);

            if (!Directory.Exists(fullPath))
                return new List<string>();

            var files = Directory.GetFiles(fullPath, searchPattern, SearchOption.TopDirectoryOnly);

            return files.Select(f => Path.GetRelativePath(_basePath, f)).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing files in directory: {DirectoryPath}", directoryPath);
            return new List<string>();
        }
    }

    /// <summary>
    /// Create directory
    /// </summary>
    public async Task<bool> CreateDirectoryAsync(
        string directoryPath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, directoryPath);

            if (Directory.Exists(fullPath))
                return true;

            Directory.CreateDirectory(fullPath);

            _logger.LogInformation("Directory created successfully: {DirectoryPath}", directoryPath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating directory: {DirectoryPath}", directoryPath);
            return false;
        }
    }

    /// <summary>
    /// Delete directory
    /// </summary>
    public async Task<bool> DeleteDirectoryAsync(
        string directoryPath,
        bool recursive = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, directoryPath);

            if (!Directory.Exists(fullPath))
            {
                _logger.LogWarning("Directory not found for deletion: {DirectoryPath}", directoryPath);
                return false;
            }

            Directory.Delete(fullPath, recursive);

            _logger.LogInformation("Directory deleted successfully: {DirectoryPath}", directoryPath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting directory: {DirectoryPath}", directoryPath);
            return false;
        }
    }

    /// <summary>
    /// Get file size
    /// </summary>
    public async Task<long> GetFileSizeAsync(
        string filePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
                return 0;

            var fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file size: {FilePath}", filePath);
            return 0;
        }
    }

    /// <summary>
    /// Copy file
    /// </summary>
    public async Task<bool> CopyFileAsync(
        string sourceFilePath,
        string destinationFilePath,
        bool overwrite = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
            {
                _logger.LogWarning("Source file not found for copy: {SourceFilePath}", sourceFilePath);
                return false;
            }

            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                Directory.CreateDirectory(destinationDirectory);
            }

            File.Copy(sourceFilePath, destinationFilePath, overwrite);

            _logger.LogInformation("File copied successfully from {SourceFilePath} to {DestinationFilePath}",
                sourceFilePath, destinationFilePath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying file from {SourceFilePath} to {DestinationFilePath}",
                sourceFilePath, destinationFilePath);
            return false;
        }
    }

    /// <summary>
    /// Move file
    /// </summary>
    public async Task<bool> MoveFileAsync(
        string sourceFilePath,
        string destinationFilePath,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
            {
                _logger.LogWarning("Source file not found for move: {SourceFilePath}", sourceFilePath);
                return false;
            }

            var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                Directory.CreateDirectory(destinationDirectory);
            }

            File.Move(sourceFilePath, destinationFilePath);

            _logger.LogInformation("File moved successfully from {SourceFilePath} to {DestinationFilePath}",
                sourceFilePath, destinationFilePath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving file from {SourceFilePath} to {DestinationFilePath}",
                sourceFilePath, destinationFilePath);
            return false;
        }
    }

    /// <summary>
    /// Store file and return file path
    /// </summary>
    public async Task<string> StoreFileAsync(
        byte[] content,
        string fileName,
        string contentType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var filePath = Path.Combine(_basePath, fileName);
            var directory = Path.GetDirectoryName(filePath);

            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllBytesAsync(filePath, content, cancellationToken);

            _logger.LogInformation("File stored successfully: {FileName} ({Size} bytes)", fileName, content.Length);

            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing file: {FileName}", fileName);
            throw;
        }
    }
}
