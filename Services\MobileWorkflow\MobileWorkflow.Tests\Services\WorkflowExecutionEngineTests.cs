using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Services;
using Xunit;

namespace MobileWorkflow.Tests.Services;

public class WorkflowExecutionEngineTests
{
    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
    private readonly Mock<IWorkflowExecutionRepository> _mockExecutionRepository;
    private readonly Mock<IWorkflowTaskRepository> _mockTaskRepository;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILogger<WorkflowExecutionEngine>> _mockLogger;
    private readonly WorkflowExecutionEngine _engine;

    public WorkflowExecutionEngineTests()
    {
        _mockWorkflowRepository = new Mock<IWorkflowRepository>();
        _mockExecutionRepository = new Mock<IWorkflowExecutionRepository>();
        _mockTaskRepository = new Mock<IWorkflowTaskRepository>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLogger = new Mock<ILogger<WorkflowExecutionEngine>>();
        
        _engine = new WorkflowExecutionEngine(
            _mockWorkflowRepository.Object,
            _mockExecutionRepository.Object,
            _mockTaskRepository.Object,
            _mockServiceProvider.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteWorkflowAsync_WithValidWorkflow_ShouldReturnSuccess()
    {
        // Arrange
        var workflowId = Guid.NewGuid();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object> { { "userId", triggeredBy } };
        var workflow = CreateTestWorkflow(workflowId);

        _mockWorkflowRepository
            .Setup(x => x.GetByIdAsync(workflowId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(workflow);

        _mockExecutionRepository
            .Setup(x => x.AddAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution exec, CancellationToken _) => exec);

        _mockWorkflowRepository
            .Setup(x => x.UpdateAsync(It.IsAny<Workflow>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Workflow w, CancellationToken _) => w);

        // Act
        var result = await _engine.ExecuteWorkflowAsync(workflowId, triggeredBy, inputData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("Workflow execution started successfully");
        
        _mockExecutionRepository.Verify(
            x => x.AddAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteWorkflowAsync_WithNonExistentWorkflow_ShouldReturnFailure()
    {
        // Arrange
        var workflowId = Guid.NewGuid();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object>();

        _mockWorkflowRepository
            .Setup(x => x.GetByIdAsync(workflowId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Workflow?)null);

        // Act
        var result = await _engine.ExecuteWorkflowAsync(workflowId, triggeredBy, inputData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Workflow not found");
    }

    [Fact]
    public async Task ExecuteWorkflowAsync_WithInactiveWorkflow_ShouldReturnFailure()
    {
        // Arrange
        var workflowId = Guid.NewGuid();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object>();
        var workflow = CreateTestWorkflow(workflowId);
        workflow.Deactivate();

        _mockWorkflowRepository
            .Setup(x => x.GetByIdAsync(workflowId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(workflow);

        // Act
        var result = await _engine.ExecuteWorkflowAsync(workflowId, triggeredBy, inputData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Workflow cannot be executed");
    }

    [Fact]
    public async Task ContinueExecutionAsync_WithValidExecution_ShouldReturnSuccess()
    {
        // Arrange
        var executionId = Guid.NewGuid();
        var execution = CreateTestExecution();

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(execution);

        _mockWorkflowRepository
            .Setup(x => x.GetByIdAsync(execution.WorkflowId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateTestWorkflow(execution.WorkflowId));

        _mockExecutionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution exec, CancellationToken _) => exec);

        // Act
        var result = await _engine.ContinueExecutionAsync(executionId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("Workflow execution continued successfully");
    }

    [Fact]
    public async Task ContinueExecutionAsync_WithNonExistentExecution_ShouldReturnFailure()
    {
        // Arrange
        var executionId = Guid.NewGuid();

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution?)null);

        // Act
        var result = await _engine.ContinueExecutionAsync(executionId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Execution not found");
    }

    [Fact]
    public async Task ContinueExecutionAsync_WithCompletedExecution_ShouldReturnFailure()
    {
        // Arrange
        var executionId = Guid.NewGuid();
        var execution = CreateTestExecution();
        execution.Complete();

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(execution);

        // Act
        var result = await _engine.ContinueExecutionAsync(executionId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("Cannot continue execution with status: Completed");
    }

    [Fact]
    public async Task PauseExecutionAsync_WithValidExecution_ShouldReturnTrue()
    {
        // Arrange
        var executionId = Guid.NewGuid();
        var execution = CreateTestExecution();

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(execution);

        _mockExecutionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution exec, CancellationToken _) => exec);

        // Act
        var result = await _engine.PauseExecutionAsync(executionId);

        // Assert
        result.Should().BeTrue();
        execution.Context.Should().ContainKey("paused_at");
    }

    [Fact]
    public async Task CancelExecutionAsync_WithValidExecution_ShouldReturnTrue()
    {
        // Arrange
        var executionId = Guid.NewGuid();
        var execution = CreateTestExecution();
        var reason = "User requested cancellation";
        var tasks = new List<WorkflowTask>
        {
            CreateTestTask(executionId),
            CreateTestTask(executionId)
        };

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(execution);

        _mockTaskRepository
            .Setup(x => x.GetByWorkflowExecutionIdAsync(executionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tasks);

        _mockExecutionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution exec, CancellationToken _) => exec);

        _mockTaskRepository
            .Setup(x => x.UpdateAsync(It.IsAny<WorkflowTask>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowTask task, CancellationToken _) => task);

        // Act
        var result = await _engine.CancelExecutionAsync(executionId, reason);

        // Assert
        result.Should().BeTrue();
        execution.Status.Should().Be("Cancelled");
        execution.Context.Should().ContainKey("cancellation_reason");
        execution.Context["cancellation_reason"].Should().Be(reason);
    }

    [Fact]
    public async Task GetRunningExecutionsAsync_ShouldReturnRunningExecutions()
    {
        // Arrange
        var runningExecutions = new List<WorkflowExecution>
        {
            CreateTestExecution(),
            CreateTestExecution()
        };

        _mockExecutionRepository
            .Setup(x => x.GetRunningExecutionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(runningExecutions);

        // Act
        var result = await _engine.GetRunningExecutionsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().OnlyContain(e => e.Status == "Running");
    }

    [Fact]
    public async Task ProcessPendingExecutionsAsync_ShouldProcessAllRunningExecutions()
    {
        // Arrange
        var runningExecutions = new List<WorkflowExecution>
        {
            CreateTestExecution(),
            CreateTestExecution()
        };

        _mockExecutionRepository
            .Setup(x => x.GetRunningExecutionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(runningExecutions);

        _mockExecutionRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Guid id, CancellationToken _) => 
                runningExecutions.FirstOrDefault(e => e.Id == id));

        _mockWorkflowRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Guid id, CancellationToken _) => CreateTestWorkflow(id));

        _mockExecutionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<WorkflowExecution>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((WorkflowExecution exec, CancellationToken _) => exec);

        // Act
        await _engine.ProcessPendingExecutionsAsync();

        // Assert
        _mockExecutionRepository.Verify(
            x => x.GetRunningExecutionsAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private static Workflow CreateTestWorkflow(Guid? id = null)
    {
        var workflowId = id ?? Guid.NewGuid();
        var definition = new Dictionary<string, object>
        {
            { "steps", new List<object>
                {
                    new Dictionary<string, object>
                    {
                        { "id", "step1" },
                        { "name", "Test Step" },
                        { "type", "Manual" },
                        { "parameters", new Dictionary<string, object>() }
                    }
                }
            }
        };

        var workflow = new Workflow(
            "Test Workflow",
            "Test Description",
            "Testing",
            "1.0",
            definition,
            new Dictionary<string, object>(),
            "Manual",
            new Dictionary<string, object>(),
            "test-user");

        // Use reflection to set the ID for testing
        var idProperty = typeof(Workflow).GetProperty("Id");
        idProperty?.SetValue(workflow, workflowId);

        return workflow;
    }

    private static WorkflowExecution CreateTestExecution()
    {
        return new WorkflowExecution(
            Guid.NewGuid(),
            Guid.NewGuid(),
            new Dictionary<string, object> { { "test", "data" } },
            "Test");
    }

    private static WorkflowTask CreateTestTask(Guid executionId)
    {
        return new WorkflowTask(
            executionId,
            "Test Task",
            "Manual",
            new Dictionary<string, object>(),
            Guid.NewGuid());
    }
}
