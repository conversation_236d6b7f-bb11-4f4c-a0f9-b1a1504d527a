using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;

namespace FinancialPayment.Application.Queries.GetCommission;

public class GetCommissionQueryHandler : IRequestHandler<GetCommissionQuery, CommissionDto?>
{
    private readonly ICommissionRepository _commissionRepository;
    private readonly ILogger<GetCommissionQueryHandler> _logger;

    public GetCommissionQueryHandler(
        ICommissionRepository commissionRepository,
        ILogger<GetCommissionQueryHandler> logger)
    {
        _commissionRepository = commissionRepository;
        _logger = logger;
    }

    public async Task<CommissionDto?> Handle(GetCommissionQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting commission {CommissionId}", request.CommissionId);

            var commission = await _commissionRepository.GetByIdAsync(request.CommissionId);
            if (commission == null)
            {
                _logger.LogWarning("Commission {CommissionId} not found", request.CommissionId);
                return null;
            }

            return new CommissionDto
            {
                Id = commission.Id,
                OrderId = commission.OrderId,
                BrokerId = commission.BrokerId,
                TransportCompanyId = commission.TransportCompanyId,
                CarrierId = commission.CarrierId,
                OrderAmount = commission.OrderAmount.Amount,
                OrderCurrency = commission.OrderAmount.Currency,
                CommissionType = commission.CommissionStructure.Type.ToString(),
                CommissionRate = commission.CommissionStructure.Rate,
                CommissionMinimumAmount = commission.CommissionStructure.MinimumAmount,
                CommissionMaximumAmount = commission.CommissionStructure.MaximumAmount,
                CommissionDescription = commission.CommissionStructure.Description,
                CalculatedAmount = commission.CalculatedAmount.Amount,
                CalculatedCurrency = commission.CalculatedAmount.Currency,
                Status = commission.Status.ToString(),
                ProcessedAt = commission.ProcessedAt,
                Notes = commission.Notes,
                CreatedAt = commission.CreatedAt,
                UpdatedAt = commission.UpdatedAt,
                Adjustments = commission.Adjustments.Select(a => new CommissionAdjustmentDto
                {
                    Id = a.Id,
                    CommissionId = a.CommissionId,
                    Amount = a.Amount.Amount,
                    Currency = a.Amount.Currency,
                    Type = a.Type.ToString(),
                    Reason = a.Reason,
                    CreatedBy = a.CreatedBy,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting commission {CommissionId}", request.CommissionId);
            throw;
        }
    }
}
