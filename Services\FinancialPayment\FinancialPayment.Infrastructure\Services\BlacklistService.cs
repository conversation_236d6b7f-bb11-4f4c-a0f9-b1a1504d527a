using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using FinancialPayment.Application.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class BlacklistService : IBlacklistService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<BlacklistService> _logger;
    private readonly HashSet<string> _blacklistedUsers;
    private readonly HashSet<string> _blacklistedEmails;
    private readonly HashSet<string> _blacklistedIpAddresses;
    private readonly HashSet<string> _blacklistedDevices;

    public BlacklistService(IMemoryCache cache, ILogger<BlacklistService> logger)
    {
        _cache = cache;
        _logger = logger;
        
        // Initialize with some demo blacklisted items
        _blacklistedUsers = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        _blacklistedEmails = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        };
        _blacklistedIpAddresses = new HashSet<string>
        {
            "*************", // Demo blacklisted IP
            "*********",
            "***********"
        };
        _blacklistedDevices = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    public async Task<bool> IsUserBlacklistedAsync(Guid userId)
    {
        try
        {
            var cacheKey = $"blacklist_user_{userId}";
            if (_cache.TryGetValue(cacheKey, out bool cachedResult))
            {
                return cachedResult;
            }

            var isBlacklisted = _blacklistedUsers.Contains(userId.ToString());
            
            // Cache the result for 5 minutes
            _cache.Set(cacheKey, isBlacklisted, TimeSpan.FromMinutes(5));

            if (isBlacklisted)
            {
                _logger.LogWarning("User {UserId} is blacklisted", userId);
            }

            await Task.CompletedTask;
            return isBlacklisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user blacklist for {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> IsEmailBlacklistedAsync(string email)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var cacheKey = $"blacklist_email_{email.ToLowerInvariant()}";
            if (_cache.TryGetValue(cacheKey, out bool cachedResult))
            {
                return cachedResult;
            }

            var isBlacklisted = _blacklistedEmails.Contains(email) || 
                               IsEmailDomainBlacklisted(email);
            
            // Cache the result for 10 minutes
            _cache.Set(cacheKey, isBlacklisted, TimeSpan.FromMinutes(10));

            if (isBlacklisted)
            {
                _logger.LogWarning("Email {Email} is blacklisted", email);
            }

            await Task.CompletedTask;
            return isBlacklisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email blacklist for {Email}", email);
            return false;
        }
    }

    public async Task<bool> IsIpAddressBlacklistedAsync(string ipAddress)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return false;

            var cacheKey = $"blacklist_ip_{ipAddress}";
            if (_cache.TryGetValue(cacheKey, out bool cachedResult))
            {
                return cachedResult;
            }

            var isBlacklisted = _blacklistedIpAddresses.Contains(ipAddress) ||
                               IsIpRangeBlacklisted(ipAddress);
            
            // Cache the result for 15 minutes
            _cache.Set(cacheKey, isBlacklisted, TimeSpan.FromMinutes(15));

            if (isBlacklisted)
            {
                _logger.LogWarning("IP address {IpAddress} is blacklisted", ipAddress);
            }

            await Task.CompletedTask;
            return isBlacklisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking IP blacklist for {IpAddress}", ipAddress);
            return false;
        }
    }

    public async Task<bool> IsDeviceBlacklistedAsync(string deviceFingerprint)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(deviceFingerprint))
                return false;

            var cacheKey = $"blacklist_device_{deviceFingerprint}";
            if (_cache.TryGetValue(cacheKey, out bool cachedResult))
            {
                return cachedResult;
            }

            var isBlacklisted = _blacklistedDevices.Contains(deviceFingerprint);
            
            // Cache the result for 30 minutes
            _cache.Set(cacheKey, isBlacklisted, TimeSpan.FromMinutes(30));

            if (isBlacklisted)
            {
                _logger.LogWarning("Device {DeviceFingerprint} is blacklisted", deviceFingerprint);
            }

            await Task.CompletedTask;
            return isBlacklisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking device blacklist for {DeviceFingerprint}", deviceFingerprint);
            return false;
        }
    }

    public async Task AddToBlacklistAsync(BlacklistEntry entry)
    {
        try
        {
            switch (entry.Type)
            {
                case BlacklistType.UserId:
                    _blacklistedUsers.Add(entry.Value);
                    _cache.Remove($"blacklist_user_{entry.Value}");
                    break;

                case BlacklistType.Email:
                    _blacklistedEmails.Add(entry.Value);
                    _cache.Remove($"blacklist_email_{entry.Value.ToLowerInvariant()}");
                    break;

                case BlacklistType.IpAddress:
                    _blacklistedIpAddresses.Add(entry.Value);
                    _cache.Remove($"blacklist_ip_{entry.Value}");
                    break;

                case BlacklistType.DeviceFingerprint:
                    _blacklistedDevices.Add(entry.Value);
                    _cache.Remove($"blacklist_device_{entry.Value}");
                    break;
            }

            _logger.LogInformation("Added {Type} {Value} to blacklist. Reason: {Reason}",
                entry.Type, entry.Value, entry.Reason);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding {Type} {Value} to blacklist", entry.Type, entry.Value);
            throw;
        }
    }

    public async Task RemoveFromBlacklistAsync(Guid entryId)
    {
        try
        {
            // In a real implementation, you would look up the entry by ID and remove it
            // For this demo, we'll just log the action
            _logger.LogInformation("Removed blacklist entry {EntryId}", entryId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing blacklist entry {EntryId}", entryId);
            throw;
        }
    }

    private bool IsEmailDomainBlacklisted(string email)
    {
        try
        {
            var domain = email.Split('@').LastOrDefault()?.ToLowerInvariant();
            if (string.IsNullOrEmpty(domain))
                return false;

            // List of known suspicious domains
            var blacklistedDomains = new[]
            {
                "tempmail.com",
                "10minutemail.com",
                "guerrillamail.com",
                "mailinator.com",
                "throwaway.email",
                "fake.com",
                "scam.com"
            };

            return blacklistedDomains.Contains(domain);
        }
        catch
        {
            return false;
        }
    }

    private bool IsIpRangeBlacklisted(string ipAddress)
    {
        try
        {
            // Check for known malicious IP ranges
            var blacklistedRanges = new[]
            {
                "192.168.1.", // Demo range
                "10.0.0.",    // Demo range
                "172.16.0."   // Demo range
            };

            return blacklistedRanges.Any(range => ipAddress.StartsWith(range));
        }
        catch
        {
            return false;
        }
    }

    // Additional helper methods for managing blacklists
    public async Task<List<BlacklistEntry>> GetBlacklistEntriesAsync(BlacklistType? type = null)
    {
        try
        {
            var entries = new List<BlacklistEntry>();

            if (type == null || type == BlacklistType.Email)
            {
                entries.AddRange(_blacklistedEmails.Select(email => new BlacklistEntry
                {
                    Id = Guid.NewGuid(),
                    Type = BlacklistType.Email,
                    Value = email,
                    Reason = "Blacklisted email",
                    AddedAt = DateTime.UtcNow.AddDays(-1), // Demo data
                    IsActive = true
                }));
            }

            if (type == null || type == BlacklistType.IpAddress)
            {
                entries.AddRange(_blacklistedIpAddresses.Select(ip => new BlacklistEntry
                {
                    Id = Guid.NewGuid(),
                    Type = BlacklistType.IpAddress,
                    Value = ip,
                    Reason = "Suspicious IP address",
                    AddedAt = DateTime.UtcNow.AddDays(-2), // Demo data
                    IsActive = true
                }));
            }

            await Task.CompletedTask;
            return entries;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting blacklist entries");
            return new List<BlacklistEntry>();
        }
    }

    public async Task<bool> IsPhoneNumberBlacklistedAsync(string phoneNumber)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Normalize phone number
            var normalizedPhone = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // Check against known blacklisted patterns
            var blacklistedPatterns = new[]
            {
                "+1234567890", // Demo blacklisted number
                "+9999999999"  // Demo blacklisted number
            };

            var isBlacklisted = blacklistedPatterns.Contains(normalizedPhone);

            if (isBlacklisted)
            {
                _logger.LogWarning("Phone number {PhoneNumber} is blacklisted", phoneNumber);
            }

            await Task.CompletedTask;
            return isBlacklisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking phone blacklist for {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task ClearCacheAsync()
    {
        try
        {
            // In a real implementation, you might want to clear specific cache entries
            // For now, we'll just log the action
            _logger.LogInformation("Blacklist cache cleared");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing blacklist cache");
            throw;
        }
    }
}
