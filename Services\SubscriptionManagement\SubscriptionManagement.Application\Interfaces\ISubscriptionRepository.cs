using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface ISubscriptionRepository
    {
        Task<Subscription?> GetByIdAsync(Guid id);
        Task<Subscription?> GetByUserIdAsync(Guid userId);
        Task<List<Subscription>> GetByUserIdAndStatusAsync(Guid userId, SubscriptionStatus status);
        Task<List<Subscription>> GetActiveSubscriptionsAsync();
        Task<List<Subscription>> GetExpiredSubscriptionsAsync();
        Task<List<Subscription>> GetSubscriptionsDueForRenewalAsync();
        Task<List<Subscription>> GetSubscriptionsDueForBillingAsync(DateTime billingDate);
        Task<List<Subscription>> GetSubscriptionsByPlanIdAsync(Guid planId);
        Task<List<Subscription>> GetSubscriptionsAsync(int page, int pageSize);
        Task<int> GetSubscriptionsCountAsync();
        Task<int> GetActiveSubscriptionsCountAsync();
        Task<int> GetSubscriptionsCountByStatusAsync(SubscriptionStatus status);
        Task<decimal> GetTotalRevenueAsync();
        Task<decimal> GetMonthlyRecurringRevenueAsync();
        Task AddAsync(Subscription subscription);
        Task UpdateAsync(Subscription subscription);
        Task DeleteAsync(Subscription subscription);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> HasActiveSubscriptionAsync(Guid userId);
    }
}
