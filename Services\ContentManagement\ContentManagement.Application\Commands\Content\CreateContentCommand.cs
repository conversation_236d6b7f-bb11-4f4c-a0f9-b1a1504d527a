using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Commands.Content;

/// <summary>
/// Command to create new content
/// </summary>
public class CreateContentCommand : IRequest<ContentDto>
{
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public ContentType Type { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ContentVisibilityDto? Visibility { get; set; }
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<string>? Tags { get; set; }
    public int SortOrder { get; set; } = 0;
    public PublishScheduleDto? PublishSchedule { get; set; }
    
    // User context
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update existing content
/// </summary>
public class UpdateContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<string>? Tags { get; set; }
    public string? ChangeDescription { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update content visibility
/// </summary>
public class UpdateContentVisibilityCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    public ContentVisibilityDto Visibility { get; set; } = null!;
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update content sort order
/// </summary>
public class UpdateContentSortOrderCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    public int SortOrder { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to submit content for review
/// </summary>
public class SubmitContentForReviewCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid SubmittedBy { get; set; }
    public string SubmittedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to approve content
/// </summary>
public class ApproveContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid ApprovedBy { get; set; }
    public string ApprovedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to reject content
/// </summary>
public class RejectContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    public string Reason { get; set; } = string.Empty;
    
    // User context
    public Guid RejectedBy { get; set; }
    public string RejectedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to publish content
/// </summary>
public class PublishContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid PublishedBy { get; set; }
    public string PublishedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to unpublish content
/// </summary>
public class UnpublishContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid UnpublishedBy { get; set; }
    public string UnpublishedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to archive content
/// </summary>
public class ArchiveContentCommand : IRequest<ContentDto>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid ArchivedBy { get; set; }
    public string ArchivedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to delete content
/// </summary>
public class DeleteContentCommand : IRequest<bool>
{
    public Guid Id { get; set; }
    
    // User context
    public Guid DeletedBy { get; set; }
    public string DeletedByName { get; set; } = string.Empty;
}
