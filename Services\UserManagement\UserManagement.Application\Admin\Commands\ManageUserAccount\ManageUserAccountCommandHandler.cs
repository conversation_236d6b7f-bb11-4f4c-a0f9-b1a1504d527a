using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Interfaces;
using UserManagement.Application.Services;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Admin.Commands.ManageUserAccount
{
    public class ManageUserAccountCommandHandler : IRequestHandler<ManageUserAccountCommand, ManageUserAccountResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly UserManagement.Application.Interfaces.INotificationService _notificationService;
        private readonly IAuditService _auditService;
        private readonly ILogger<ManageUserAccountCommandHandler> _logger;

        public ManageUserAccountCommandHandler(
            IUserProfileRepository userProfileRepository,
            UserManagement.Application.Interfaces.INotificationService notificationService,
            IAuditService auditService,
            ILogger<ManageUserAccountCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _notificationService = notificationService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<ManageUserAccountResponse> Handle(ManageUserAccountCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Managing user account {UserId} with action {Action}",
                    request.UserId, request.Action);

                var userProfile = await _userProfileRepository.GetByUserIdAsync(request.UserId);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                var previousState = GetCurrentUserState(userProfile);
                var actionResult = await PerformAccountAction(userProfile, request);
                var newState = GetCurrentUserState(userProfile);

                // Update the user profile
                await _userProfileRepository.UpdateAsync(userProfile);

                // Log the action
                await LogAccountAction(request, userProfile, actionResult);

                // Send notifications
                await SendActionNotification(userProfile, request, actionResult);

                var response = new ManageUserAccountResponse
                {
                    Success = true,
                    Message = GetSuccessMessage(request.Action),
                    ActionPerformed = request.Action,
                    ActionPerformedAt = DateTime.UtcNow,
                    NewStatus = CreateUserAccountStatus(userProfile),
                    Result = new AccountActionResult
                    {
                        ActionType = request.Action.ToString(),
                        WasSuccessful = true,
                        Details = actionResult,
                        Timestamp = DateTime.UtcNow,
                        PerformedBy = request.ActionBy.ToString(),
                        PreviousState = previousState,
                        NewState = newState,
                        NotificationSent = true,
                        NotificationMethod = "Email"
                    }
                };

                _logger.LogInformation("Successfully performed action {Action} for user {UserId}",
                    request.Action, request.UserId);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error managing user account {UserId} with action {Action}",
                    request.UserId, request.Action);

                return new ManageUserAccountResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ActionPerformed = request.Action,
                    ActionPerformedAt = DateTime.UtcNow
                };
            }
        }

        private async Task<string> PerformAccountAction(UserProfile userProfile, ManageUserAccountCommand request)
        {
            switch (request.Action)
            {
                case UserAccountAction.Activate:
                    userProfile.Activate();
                    return "User account activated successfully";

                case UserAccountAction.Deactivate:
                    userProfile.Deactivate(request.Reason);
                    return "User account deactivated successfully";

                case UserAccountAction.Suspend:
                    userProfile.Suspend(request.Reason);
                    return "User account suspended successfully";

                case UserAccountAction.Unsuspend:
                    userProfile.Unsuspend();
                    return "User account unsuspended successfully";

                case UserAccountAction.ResetPassword:
                    await ResetUserPassword(userProfile, request);
                    return "Password reset successfully";

                case UserAccountAction.UpdatePlanInfo:
                    UpdatePlanInformation(userProfile, request.PlanInfo);
                    return "Plan information updated successfully";

                case UserAccountAction.EnableAutoRenewal:
                    userProfile.EnableAutoRenewal();
                    return "Auto-renewal enabled successfully";

                case UserAccountAction.DisableAutoRenewal:
                    userProfile.DisableAutoRenewal();
                    return "Auto-renewal disabled successfully";

                case UserAccountAction.ExtendSubscription:
                    ExtendSubscription(userProfile, request);
                    return "Subscription extended successfully";

                case UserAccountAction.CancelSubscription:
                    CancelSubscription(userProfile, request.Reason);
                    return "Subscription cancelled successfully";

                case UserAccountAction.SendKycReminder:
                    await SendKycReminder(userProfile);
                    return "KYC reminder sent successfully";

                case UserAccountAction.ResendKycUploadPrompt:
                    await ResendKycUploadPrompt(userProfile);
                    return "KYC upload prompt resent successfully";

                case UserAccountAction.ForceKycResubmission:
                    ForceKycResubmission(userProfile, request.Reason);
                    return "KYC resubmission forced successfully";

                default:
                    throw new UserManagementDomainException($"Unsupported account action: {request.Action}");
            }
        }

        private async Task ResetUserPassword(UserProfile userProfile, ManageUserAccountCommand request)
        {
            // This would integrate with the Identity Service to reset the password
            userProfile.RecordPasswordReset(request.ActionBy, request.RequirePasswordChangeOnNextLogin);

            // In a real implementation, this would call the Identity Service
            // await _identityService.ResetPasswordAsync(userProfile.UserId, request.NewPassword);
        }

        private void UpdatePlanInformation(UserProfile userProfile, PlanInformation planInfo)
        {
            if (planInfo == null) return;

            userProfile.UpdateSubscriptionPlan(
                planInfo.PlanName,
                planInfo.StartDate,
                planInfo.ExpiryDate,
                planInfo.MonthlyFee,
                planInfo.FeatureLimits);
        }

        private void ExtendSubscription(UserProfile userProfile, ManageUserAccountCommand request)
        {
            if (request.SubscriptionExpiryDate.HasValue)
            {
                userProfile.ExtendSubscription(request.SubscriptionExpiryDate.Value);
            }
        }

        private void CancelSubscription(UserProfile userProfile, string reason)
        {
            userProfile.CancelSubscription(reason);
        }

        private async Task SendKycReminder(UserProfile userProfile)
        {
            await _notificationService.SendKycReminderAsync(
                userProfile.UserId,
                userProfile.UserType.ToString(),
                userProfile.GetDisplayName(),
                userProfile.Email);

            userProfile.RecordKycReminderSent();
        }

        private async Task ResendKycUploadPrompt(UserProfile userProfile)
        {
            await _notificationService.SendKycUploadPromptAsync(
                userProfile.UserId,
                userProfile.UserType.ToString(),
                userProfile.GetDisplayName(),
                userProfile.Email);

            userProfile.RecordKycUploadPromptSent();
        }

        private void ForceKycResubmission(UserProfile userProfile, string reason)
        {
            userProfile.ForceKycResubmission(reason);
        }

        private string GetCurrentUserState(UserProfile userProfile)
        {
            return $"Status: {userProfile.Status}, " +
                   $"KYC: {userProfile.KycStatus}, " +
                   $"Subscription: {userProfile.SubscriptionPlan ?? "None"}, " +
                   $"AutoRenewal: {userProfile.AutoRenewalEnabled}";
        }

        private UserAccountStatus CreateUserAccountStatus(UserProfile userProfile)
        {
            return new UserAccountStatus
            {
                IsActive = userProfile.Status == ProfileStatus.Approved,
                IsSuspended = userProfile.Status == ProfileStatus.Rejected, // Simplified mapping
                Status = userProfile.Status.ToString(),
                LastPasswordChange = userProfile.LastPasswordChangeAt,
                RequirePasswordChange = userProfile.RequirePasswordChange,
                Subscription = new SubscriptionStatus
                {
                    PlanName = userProfile.SubscriptionPlan,
                    StartDate = userProfile.SubscriptionStartDate,
                    ExpiryDate = userProfile.SubscriptionExpiryDate,
                    IsActive = userProfile.SubscriptionExpiryDate > DateTime.UtcNow,
                    AutoRenewalEnabled = userProfile.AutoRenewalEnabled ?? false,
                    MonthlyFee = userProfile.SubscriptionMonthlyFee,
                    FeatureLimits = userProfile.SubscriptionFeatureLimits,
                    DaysUntilExpiry = userProfile.SubscriptionExpiryDate?.Subtract(DateTime.UtcNow).Days ?? 0,
                    IsExpiringSoon = userProfile.SubscriptionExpiryDate?.Subtract(DateTime.UtcNow).Days <= 30
                },
                KycStatus = new KycStatus
                {
                    Status = userProfile.KycStatus?.ToString() ?? "NotStarted",
                    SubmittedAt = userProfile.KycSubmittedAt,
                    ApprovedAt = userProfile.KycApprovedAt,
                    LastReminderSent = userProfile.LastKycReminderSent,
                    ReminderCount = userProfile.KycReminderCount ?? 0,
                    RequiresResubmission = userProfile.KycRequiresResubmission ?? false
                }
            };
        }

        private async Task LogAccountAction(ManageUserAccountCommand request, UserProfile userProfile, string actionResult)
        {
            await _auditService.LogAccountActionAsync(
                request.UserId,
                userProfile.GetDisplayName(),
                request.ActionBy,
                "Admin User", // In real implementation, get from current user context
                "Admin",
                "127.0.0.1", // In real implementation, get from HTTP context
                "UserAgent", // In real implementation, get from HTTP context
                request.Action.ToString(),
                actionResult,
                request.Reason,
                request.Notes);
        }

        private async Task SendActionNotification(UserProfile userProfile, ManageUserAccountCommand request, string actionResult)
        {
            var notificationType = GetNotificationType(request.Action);

            await _notificationService.SendAccountActionNotificationAsync(
                userProfile.UserId,
                userProfile.UserType.ToString(),
                userProfile.GetDisplayName(),
                userProfile.Email,
                notificationType,
                actionResult,
                request.Reason,
                DateTime.UtcNow);
        }

        private string GetNotificationType(UserAccountAction action)
        {
            return action switch
            {
                UserAccountAction.Activate => "AccountActivated",
                UserAccountAction.Deactivate => "AccountDeactivated",
                UserAccountAction.Suspend => "AccountSuspended",
                UserAccountAction.Unsuspend => "AccountUnsuspended",
                UserAccountAction.ResetPassword => "PasswordReset",
                UserAccountAction.UpdatePlanInfo => "PlanUpdated",
                UserAccountAction.EnableAutoRenewal => "AutoRenewalEnabled",
                UserAccountAction.DisableAutoRenewal => "AutoRenewalDisabled",
                UserAccountAction.ExtendSubscription => "SubscriptionExtended",
                UserAccountAction.CancelSubscription => "SubscriptionCancelled",
                UserAccountAction.SendKycReminder => "KycReminder",
                UserAccountAction.ResendKycUploadPrompt => "KycUploadPrompt",
                UserAccountAction.ForceKycResubmission => "KycResubmissionRequired",
                _ => "AccountActionPerformed"
            };
        }

        private string GetSuccessMessage(UserAccountAction action)
        {
            return action switch
            {
                UserAccountAction.Activate => "User account has been activated successfully",
                UserAccountAction.Deactivate => "User account has been deactivated successfully",
                UserAccountAction.Suspend => "User account has been suspended successfully",
                UserAccountAction.Unsuspend => "User account has been unsuspended successfully",
                UserAccountAction.ResetPassword => "User password has been reset successfully",
                UserAccountAction.UpdatePlanInfo => "User plan information has been updated successfully",
                UserAccountAction.EnableAutoRenewal => "Auto-renewal has been enabled for the user",
                UserAccountAction.DisableAutoRenewal => "Auto-renewal has been disabled for the user",
                UserAccountAction.ExtendSubscription => "User subscription has been extended successfully",
                UserAccountAction.CancelSubscription => "User subscription has been cancelled successfully",
                UserAccountAction.SendKycReminder => "KYC reminder has been sent to the user",
                UserAccountAction.ResendKycUploadPrompt => "KYC upload prompt has been resent to the user",
                UserAccountAction.ForceKycResubmission => "KYC resubmission has been forced for the user",
                _ => "Account action has been performed successfully"
            };
        }
    }
}
