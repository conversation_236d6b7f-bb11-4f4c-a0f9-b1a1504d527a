using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace AuditCompliance.Infrastructure.Services
{
    /// <summary>
    /// File storage service implementation with compression and security features
    /// </summary>
    public class FileStorageService : IFileStorageService
    {
        private readonly ILogger<FileStorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _storageBasePath;
        private readonly string _secureStoragePath;
        private readonly Timer _cleanupTimer;

        public FileStorageService(ILogger<FileStorageService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _storageBasePath = _configuration.GetValue<string>("FileStorage:BasePath") ?? Path.Combine(Path.GetTempPath(), "AuditExports");
            _secureStoragePath = _configuration.GetValue<string>("FileStorage:SecurePath") ?? Path.Combine(_storageBasePath, "Secure");
            
            // Ensure directories exist
            Directory.CreateDirectory(_storageBasePath);
            Directory.CreateDirectory(_secureStoragePath);
            
            // Cleanup timer runs every 6 hours
            _cleanupTimer = new Timer(CleanupExpiredFiles, null, TimeSpan.FromHours(6), TimeSpan.FromHours(6));
        }

        public async Task<string> SaveExportFileAsync(string fileName, string content, string contentType)
        {
            try
            {
                var fileBytes = Encoding.UTF8.GetBytes(content);
                return await SaveExportFileAsync(fileName, fileBytes, contentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving export file {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> SaveExportFileAsync(string fileName, byte[] content, string contentType)
        {
            try
            {
                var fileId = Guid.NewGuid().ToString("N")[..12];
                var sanitizedFileName = SanitizeFileName(fileName);
                var fullFileName = $"{fileId}_{sanitizedFileName}";
                var filePath = Path.Combine(_storageBasePath, fullFileName);

                // Compress if file is large
                var finalContent = content;
                var isCompressed = false;
                
                if (content.Length > 1024 * 1024) // 1MB threshold
                {
                    finalContent = await CompressContentAsync(content);
                    isCompressed = true;
                    fullFileName = Path.ChangeExtension(fullFileName, ".gz");
                    filePath = Path.ChangeExtension(filePath, ".gz");
                }

                await File.WriteAllBytesAsync(filePath, finalContent);

                // Create metadata file
                var metadata = new FileMetadataDto
                {
                    FileName = sanitizedFileName,
                    ContentType = contentType,
                    FileSizeBytes = finalContent.Length,
                    CreatedAt = DateTime.UtcNow,
                    CustomMetadata = new Dictionary<string, string>
                    {
                        ["FileId"] = fileId,
                        ["IsCompressed"] = isCompressed.ToString(),
                        ["OriginalSize"] = content.Length.ToString(),
                        ["ExpiresAt"] = DateTime.UtcNow.AddDays(7).ToString("O")
                    }
                };

                var metadataPath = Path.ChangeExtension(filePath, ".meta");
                var metadataJson = System.Text.Json.JsonSerializer.Serialize(metadata);
                await File.WriteAllTextAsync(metadataPath, metadataJson);

                var fileUrl = GenerateFileUrl(fullFileName);
                
                _logger.LogInformation("Export file saved: {FileName} -> {FileUrl} (Size: {Size} bytes, Compressed: {Compressed})", 
                    sanitizedFileName, fileUrl, finalContent.Length, isCompressed);

                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving export file {FileName}", fileName);
                throw;
            }
        }

        public async Task<byte[]> GetFileContentAsync(string fileUrl)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_storageBasePath, fileName);

                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {fileName}");
                }

                // Check if file has expired
                var metadataPath = Path.ChangeExtension(filePath, ".meta");
                if (File.Exists(metadataPath))
                {
                    var metadataJson = await File.ReadAllTextAsync(metadataPath);
                    var metadata = System.Text.Json.JsonSerializer.Deserialize<FileMetadataDto>(metadataJson);
                    
                    if (metadata?.CustomMetadata.TryGetValue("ExpiresAt", out var expiresAtStr) == true &&
                        DateTime.TryParse(expiresAtStr, out var expiresAt) &&
                        expiresAt < DateTime.UtcNow)
                    {
                        throw new InvalidOperationException($"File has expired: {fileName}");
                    }
                }

                var content = await File.ReadAllBytesAsync(filePath);

                // Decompress if needed
                if (fileName.EndsWith(".gz"))
                {
                    content = await DecompressContentAsync(content);
                }

                return content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file content from {FileUrl}", fileUrl);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string fileUrl)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_storageBasePath, fileName);
                var metadataPath = Path.ChangeExtension(filePath, ".meta");

                var deleted = false;

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    deleted = true;
                }

                if (File.Exists(metadataPath))
                {
                    File.Delete(metadataPath);
                }

                if (deleted)
                {
                    _logger.LogInformation("File deleted: {FileName}", fileName);
                }

                return deleted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {FileUrl}", fileUrl);
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string fileUrl)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_storageBasePath, fileName);
                return File.Exists(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking file existence {FileUrl}", fileUrl);
                return false;
            }
        }

        public async Task<FileMetadataDto> GetFileMetadataAsync(string fileUrl)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var filePath = Path.Combine(_storageBasePath, fileName);
                var metadataPath = Path.ChangeExtension(filePath, ".meta");

                if (!File.Exists(metadataPath))
                {
                    // Generate basic metadata if metadata file doesn't exist
                    var fileInfo = new FileInfo(filePath);
                    return new FileMetadataDto
                    {
                        FileName = fileName,
                        ContentType = "application/octet-stream",
                        FileSizeBytes = fileInfo.Length,
                        CreatedAt = fileInfo.CreationTimeUtc,
                        LastModified = fileInfo.LastWriteTimeUtc
                    };
                }

                var metadataJson = await File.ReadAllTextAsync(metadataPath);
                var metadata = System.Text.Json.JsonSerializer.Deserialize<FileMetadataDto>(metadataJson);
                
                return metadata ?? throw new InvalidOperationException("Failed to deserialize file metadata");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file metadata {FileUrl}", fileUrl);
                throw;
            }
        }

        public async Task<string> GenerateSecureDownloadUrlAsync(string fileUrl, TimeSpan expiration)
        {
            try
            {
                var fileName = ExtractFileNameFromUrl(fileUrl);
                var token = GenerateSecureToken(fileName, expiration);
                var secureUrl = $"{fileUrl}?token={token}&expires={DateTimeOffset.UtcNow.Add(expiration).ToUnixTimeSeconds()}";
                
                _logger.LogDebug("Generated secure download URL for {FileName} (expires in {Expiration})", fileName, expiration);
                
                return secureUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating secure download URL for {FileUrl}", fileUrl);
                throw;
            }
        }

        public async Task<string> SaveSecureFileAsync(string fileName, byte[] content, string contentType, string encryptionKey)
        {
            try
            {
                // Encrypt content
                var encryptedContent = await EncryptContentAsync(content, encryptionKey);
                
                var fileId = Guid.NewGuid().ToString("N")[..12];
                var sanitizedFileName = SanitizeFileName(fileName);
                var fullFileName = $"{fileId}_{sanitizedFileName}.enc";
                var filePath = Path.Combine(_secureStoragePath, fullFileName);

                await File.WriteAllBytesAsync(filePath, encryptedContent);

                // Create secure metadata
                var metadata = new FileMetadataDto
                {
                    FileName = sanitizedFileName,
                    ContentType = contentType,
                    FileSizeBytes = encryptedContent.Length,
                    CreatedAt = DateTime.UtcNow,
                    CustomMetadata = new Dictionary<string, string>
                    {
                        ["FileId"] = fileId,
                        ["IsEncrypted"] = "true",
                        ["OriginalSize"] = content.Length.ToString(),
                        ["ExpiresAt"] = DateTime.UtcNow.AddDays(30).ToString("O") // Secure files last longer
                    }
                };

                var metadataPath = Path.ChangeExtension(filePath, ".meta");
                var metadataJson = System.Text.Json.JsonSerializer.Serialize(metadata);
                await File.WriteAllTextAsync(metadataPath, metadataJson);

                var fileUrl = GenerateSecureFileUrl(fullFileName);
                
                _logger.LogInformation("Secure file saved: {FileName} -> {FileUrl} (Size: {Size} bytes)", 
                    sanitizedFileName, fileUrl, encryptedContent.Length);

                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving secure file {FileName}", fileName);
                throw;
            }
        }

        private async Task<byte[]> CompressContentAsync(byte[] content)
        {
            using var output = new MemoryStream();
            using (var gzip = new GZipStream(output, CompressionLevel.Optimal))
            {
                await gzip.WriteAsync(content);
            }
            return output.ToArray();
        }

        private async Task<byte[]> DecompressContentAsync(byte[] compressedContent)
        {
            using var input = new MemoryStream(compressedContent);
            using var gzip = new GZipStream(input, CompressionMode.Decompress);
            using var output = new MemoryStream();
            await gzip.CopyToAsync(output);
            return output.ToArray();
        }

        private async Task<byte[]> EncryptContentAsync(byte[] content, string encryptionKey)
        {
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(encryptionKey.PadRight(32)[..32]); // Ensure 32 bytes
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            using var output = new MemoryStream();
            
            // Write IV first
            await output.WriteAsync(aes.IV);
            
            using (var cryptoStream = new CryptoStream(output, encryptor, CryptoStreamMode.Write))
            {
                await cryptoStream.WriteAsync(content);
            }
            
            return output.ToArray();
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return string.IsNullOrWhiteSpace(sanitized) ? "export_file" : sanitized;
        }

        private string GenerateFileUrl(string fileName)
        {
            var baseUrl = _configuration.GetValue<string>("FileStorage:BaseUrl") ?? "https://localhost/files";
            return $"{baseUrl.TrimEnd('/')}/{fileName}";
        }

        private string GenerateSecureFileUrl(string fileName)
        {
            var baseUrl = _configuration.GetValue<string>("FileStorage:SecureBaseUrl") ?? "https://localhost/secure-files";
            return $"{baseUrl.TrimEnd('/')}/{fileName}";
        }

        private string ExtractFileNameFromUrl(string fileUrl)
        {
            return Path.GetFileName(new Uri(fileUrl).LocalPath);
        }

        private string GenerateSecureToken(string fileName, TimeSpan expiration)
        {
            var secretKey = _configuration.GetValue<string>("FileStorage:SecretKey") ?? "default-secret-key";
            var payload = $"{fileName}:{DateTimeOffset.UtcNow.Add(expiration).ToUnixTimeSeconds()}";
            
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
            return Convert.ToBase64String(hash);
        }

        private void CleanupExpiredFiles(object? state)
        {
            try
            {
                var now = DateTime.UtcNow;
                var deletedCount = 0;

                // Cleanup regular files
                deletedCount += CleanupDirectory(_storageBasePath, now);
                
                // Cleanup secure files
                deletedCount += CleanupDirectory(_secureStoragePath, now);

                if (deletedCount > 0)
                {
                    _logger.LogInformation("Cleaned up {Count} expired files", deletedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file cleanup");
            }
        }

        private int CleanupDirectory(string directoryPath, DateTime now)
        {
            var deletedCount = 0;
            
            foreach (var metadataFile in Directory.GetFiles(directoryPath, "*.meta"))
            {
                try
                {
                    var metadataJson = File.ReadAllText(metadataFile);
                    var metadata = System.Text.Json.JsonSerializer.Deserialize<FileMetadataDto>(metadataJson);
                    
                    if (metadata?.CustomMetadata.TryGetValue("ExpiresAt", out var expiresAtStr) == true &&
                        DateTime.TryParse(expiresAtStr, out var expiresAt) &&
                        expiresAt < now)
                    {
                        var dataFile = Path.ChangeExtension(metadataFile, null);
                        
                        if (File.Exists(dataFile))
                        {
                            File.Delete(dataFile);
                        }
                        
                        File.Delete(metadataFile);
                        deletedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing metadata file {MetadataFile}", metadataFile);
                }
            }
            
            return deletedCount;
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }
}
