using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for monitoring thresholds and generating alerts
/// </summary>
public interface IThresholdMonitoringService
{
    Task<List<ThresholdViolation>> CheckThresholdsAsync(AnalyticsEvent analyticsEvent);
    Task<List<Threshold>> GetThresholdsAsync(Guid userId, UserRole userRole);
    Task CreateThresholdAsync(Threshold threshold);
    Task UpdateThresholdAsync(Threshold threshold);
    Task DeleteThresholdAsync(Guid thresholdId);
    Task<bool> IsThresholdViolatedAsync(string metricName, decimal currentValue, Guid userId);
}

public class ThresholdMonitoringService : IThresholdMonitoringService
{
    private readonly IThresholdRepository _thresholdRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly ILogger<ThresholdMonitoringService> _logger;

    public ThresholdMonitoringService(
        IThresholdRepository thresholdRepository,
        IAlertRepository alertRepository,
        IMetricRepository metricRepository,
        ILogger<ThresholdMonitoringService> logger)
    {
        _thresholdRepository = thresholdRepository;
        _alertRepository = alertRepository;
        _metricRepository = metricRepository;
        _logger = logger;
    }

    public async Task<List<ThresholdViolation>> CheckThresholdsAsync(AnalyticsEvent analyticsEvent)
    {
        try
        {
            var violations = new List<ThresholdViolation>();

            // Get relevant thresholds for the user and metric
            var thresholds = await _thresholdRepository.GetByUserAndMetricAsync(
                analyticsEvent.UserId ?? Guid.Empty,
                analyticsEvent.EventName);

            var metricValue = ExtractMetricValueFromEvent(analyticsEvent);

            foreach (var threshold in thresholds)
            {
                var violation = CheckThresholdViolation(threshold, metricValue, analyticsEvent);
                if (violation != null)
                {
                    violations.Add(violation);
                }
            }

            return violations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking thresholds for event {EventName}", analyticsEvent.EventName);
            throw;
        }
    }

    public async Task<List<Threshold>> GetThresholdsAsync(Guid userId, UserRole userRole)
    {
        try
        {
            return await _thresholdRepository.GetByUserAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting thresholds for user {UserId}", userId);
            throw;
        }
    }

    public async Task CreateThresholdAsync(Threshold threshold)
    {
        try
        {
            threshold.Id = Guid.NewGuid();
            threshold.CreatedAt = DateTime.UtcNow;
            threshold.UpdatedAt = DateTime.UtcNow;

            await _thresholdRepository.AddAsync(threshold);

            _logger.LogInformation("Created threshold {ThresholdId} for metric {MetricName}",
                threshold.Id, threshold.MetricName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating threshold for metric {MetricName}", threshold.MetricName);
            throw;
        }
    }

    public async Task UpdateThresholdAsync(Threshold threshold)
    {
        try
        {
            var existingThreshold = await _thresholdRepository.GetByIdAsync(threshold.Id);
            if (existingThreshold == null)
            {
                throw new InvalidOperationException($"Threshold {threshold.Id} not found");
            }

            // Update threshold using domain methods
            existingThreshold.UpdateThresholds(threshold.WarningValue, threshold.CriticalValue);

            // Enable or disable based on IsActive status
            if (threshold.IsActive)
                existingThreshold.Enable();
            else
                existingThreshold.Disable();

            await _thresholdRepository.UpdateAsync(existingThreshold);

            _logger.LogInformation("Updated threshold {ThresholdId}", threshold.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating threshold {ThresholdId}", threshold.Id);
            throw;
        }
    }

    public async Task DeleteThresholdAsync(Guid thresholdId)
    {
        try
        {
            var threshold = await _thresholdRepository.GetByIdAsync(thresholdId);
            if (threshold == null)
            {
                throw new InvalidOperationException($"Threshold {thresholdId} not found");
            }

            await _thresholdRepository.DeleteAsync(thresholdId);

            _logger.LogInformation("Deleted threshold {ThresholdId}", thresholdId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting threshold {ThresholdId}", thresholdId);
            throw;
        }
    }

    public async Task<bool> IsThresholdViolatedAsync(string metricName, decimal currentValue, Guid userId)
    {
        try
        {
            var thresholds = await _thresholdRepository.GetPagedAsync(1, 1000,
                t => t.UserId == userId && t.MetricName == metricName && t.IsActive);

            return thresholds.Items.Any(t => IsValueViolatingThreshold(currentValue, t));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking threshold violation for metric {MetricName}", metricName);
            throw;
        }
    }

    private ThresholdViolation? CheckThresholdViolation(Threshold threshold, decimal metricValue, AnalyticsEvent analyticsEvent)
    {
        if (!IsValueViolatingThreshold(metricValue, threshold))
        {
            return null;
        }

        var severity = DetermineSeverity(metricValue, threshold);
        var userRole = DetermineUserRole(analyticsEvent.UserId ?? Guid.Empty); // This would be implemented

        return new ThresholdViolation
        {
            UserId = threshold.UserId,
            UserRole = userRole,
            AlertType = threshold.ThresholdType == ThresholdType.Performance ? AlertType.Performance : AlertType.System,
            Severity = severity,
            Title = GenerateAlertTitle(threshold, metricValue),
            Message = GenerateAlertMessage(threshold, metricValue),
            MetricName = threshold.MetricName,
            CurrentValue = metricValue,
            ThresholdValue = severity == AlertSeverity.Critical ? threshold.CriticalValue : threshold.WarningValue
        };
    }

    private bool IsValueViolatingThreshold(decimal value, Threshold threshold)
    {
        return threshold.ComparisonOperator switch
        {
            ComparisonOperator.GreaterThan => value > threshold.WarningValue || value > threshold.CriticalValue,
            ComparisonOperator.LessThan => value < threshold.WarningValue || value < threshold.CriticalValue,
            ComparisonOperator.GreaterThanOrEqual => value >= threshold.WarningValue || value >= threshold.CriticalValue,
            ComparisonOperator.LessThanOrEqual => value <= threshold.WarningValue || value <= threshold.CriticalValue,
            ComparisonOperator.Equal => Math.Abs(value - threshold.WarningValue) < 0.01m || Math.Abs(value - threshold.CriticalValue) < 0.01m,
            ComparisonOperator.NotEqual => Math.Abs(value - threshold.WarningValue) >= 0.01m && Math.Abs(value - threshold.CriticalValue) >= 0.01m,
            _ => false
        };
    }

    private AlertSeverity DetermineSeverity(decimal value, Threshold threshold)
    {
        var criticalViolated = threshold.ComparisonOperator switch
        {
            ComparisonOperator.GreaterThan => value > threshold.CriticalValue,
            ComparisonOperator.LessThan => value < threshold.CriticalValue,
            ComparisonOperator.GreaterThanOrEqual => value >= threshold.CriticalValue,
            ComparisonOperator.LessThanOrEqual => value <= threshold.CriticalValue,
            ComparisonOperator.Equal => Math.Abs(value - threshold.CriticalValue) < 0.01m,
            ComparisonOperator.NotEqual => Math.Abs(value - threshold.CriticalValue) >= 0.01m,
            _ => false
        };

        return criticalViolated ? AlertSeverity.Critical : AlertSeverity.Warning;
    }

    private string GenerateAlertTitle(Threshold threshold, decimal currentValue)
    {
        var severity = DetermineSeverity(currentValue, threshold);
        return $"{severity} Alert: {threshold.MetricName} Threshold Exceeded";
    }

    private string GenerateAlertMessage(Threshold threshold, decimal currentValue)
    {
        var severity = DetermineSeverity(currentValue, threshold);
        var thresholdValue = severity == AlertSeverity.Critical ? threshold.CriticalValue : threshold.WarningValue;

        return $"Metric '{threshold.MetricName}' has reached {currentValue}, which {GetComparisonText(threshold.ComparisonOperator)} the {severity.ToString().ToLower()} threshold of {thresholdValue}.";
    }

    private string GetComparisonText(ComparisonOperator comparisonOperator)
    {
        return comparisonOperator switch
        {
            ComparisonOperator.GreaterThan => "exceeds",
            ComparisonOperator.LessThan => "is below",
            ComparisonOperator.GreaterThanOrEqual => "meets or exceeds",
            ComparisonOperator.LessThanOrEqual => "is at or below",
            ComparisonOperator.Equal => "equals",
            ComparisonOperator.NotEqual => "does not equal",
            _ => "violates"
        };
    }

    private decimal ExtractMetricValueFromEvent(AnalyticsEvent analyticsEvent)
    {
        // Extract numeric value from event data
        if (analyticsEvent.Properties.ContainsKey("value"))
        {
            if (decimal.TryParse(analyticsEvent.Properties["value"]?.ToString(), out var value))
                return value;
        }

        // Try to extract from other common fields
        if (analyticsEvent.Properties.ContainsKey("amount"))
        {
            if (decimal.TryParse(analyticsEvent.Properties["amount"]?.ToString(), out var amount))
                return amount;
        }

        if (analyticsEvent.Properties.ContainsKey("count"))
        {
            if (decimal.TryParse(analyticsEvent.Properties["count"]?.ToString(), out var count))
                return count;
        }

        // Default to 1 for counting events
        return 1;
    }

    private UserRole DetermineUserRole(Guid userId)
    {
        // This would typically involve a database lookup or cache lookup
        // For now, returning a default value
        return UserRole.TransportCompany;
    }

    // Default thresholds for different user roles
    public async Task CreateDefaultThresholdsAsync(Guid userId, UserRole userRole)
    {
        try
        {
            var defaultThresholds = GetDefaultThresholds(userId, userRole);

            foreach (var threshold in defaultThresholds)
            {
                await CreateThresholdAsync(threshold);
            }

            _logger.LogInformation("Created {Count} default thresholds for user {UserId} with role {UserRole}",
                defaultThresholds.Count, userId, userRole);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating default thresholds for user {UserId}", userId);
            throw;
        }
    }

    private List<Threshold> GetDefaultThresholds(Guid userId, UserRole userRole)
    {
        var thresholds = new List<Threshold>();

        switch (userRole)
        {
            case UserRole.Admin:
                thresholds.AddRange(GetAdminDefaultThresholds(userId));
                break;
            case UserRole.TransportCompany:
                thresholds.AddRange(GetTransportCompanyDefaultThresholds(userId));
                break;
            case UserRole.Broker:
                thresholds.AddRange(GetBrokerDefaultThresholds(userId));
                break;
            case UserRole.Carrier:
                thresholds.AddRange(GetCarrierDefaultThresholds(userId));
                break;
            case UserRole.Shipper:
                thresholds.AddRange(GetShipperDefaultThresholds(userId));
                break;
        }

        return thresholds;
    }

    private List<Threshold> GetAdminDefaultThresholds(Guid userId)
    {
        return new List<Threshold>
        {
            new("SystemPerformance", "SystemPerformance", 90, 80, ComparisonOperator.LessThan, "Admin", UserRole.Admin, userId, ThresholdType.Performance),
            new("ActiveUsers", "ActiveUsers", 1000, 500, ComparisonOperator.LessThan, "Admin", UserRole.Admin, userId, ThresholdType.Business),
            new("ErrorRate", "ErrorRate", 5, 10, ComparisonOperator.GreaterThan, "Admin", UserRole.Admin, userId, ThresholdType.System)
        };
    }

    private List<Threshold> GetTransportCompanyDefaultThresholds(Guid userId)
    {
        return new List<Threshold>
        {
            new("RFQConversionRate", "RFQConversionRate", 60, 40, ComparisonOperator.LessThan, "TransportCompany", UserRole.TransportCompany, userId, ThresholdType.Business),
            new("AverageResponseTime", "AverageResponseTime", 24, 48, ComparisonOperator.GreaterThan, "TransportCompany", UserRole.TransportCompany, userId, ThresholdType.Performance),
            new("DeliveryPerformance", "DeliveryPerformance", 85, 70, ComparisonOperator.LessThan, "TransportCompany", UserRole.TransportCompany, userId, ThresholdType.Performance)
        };
    }

    private List<Threshold> GetBrokerDefaultThresholds(Guid userId)
    {
        return new List<Threshold>
        {
            new("QuoteSuccessRate", "QuoteSuccessRate", 65, 50, ComparisonOperator.LessThan, "Broker", UserRole.Broker, userId, ThresholdType.Business),
            new("CarrierUtilization", "CarrierUtilization", 70, 50, ComparisonOperator.LessThan, "Broker", UserRole.Broker, userId, ThresholdType.Performance),
            new("DailyMargin", "DailyMargin", 5000, 2000, ComparisonOperator.LessThan, "Broker", UserRole.Broker, userId, ThresholdType.Financial)
        };
    }

    private List<Threshold> GetCarrierDefaultThresholds(Guid userId)
    {
        return new List<Threshold>
        {
            new("OnTimeDeliveryRate", "OnTimeDeliveryRate", 90, 80, ComparisonOperator.LessThan, "Carrier", UserRole.Carrier, userId, ThresholdType.Performance),
            new("VehicleUtilization", "VehicleUtilization", 70, 50, ComparisonOperator.LessThan, "Carrier", UserRole.Carrier, userId, ThresholdType.Performance),
            new("FuelEfficiency", "FuelEfficiency", 6, 5, ComparisonOperator.LessThan, "Carrier", UserRole.Carrier, userId, ThresholdType.Performance)
        };
    }

    private List<Threshold> GetShipperDefaultThresholds(Guid userId)
    {
        return new List<Threshold>
        {
            new("SLACompliance", "SLACompliance", 90, 80, ComparisonOperator.LessThan, "Shipper", UserRole.Shipper, userId, ThresholdType.Performance),
            new("CostPerShipment", "CostPerShipment", 3000, 4000, ComparisonOperator.GreaterThan, "Shipper", UserRole.Shipper, userId, ThresholdType.Financial),
            new("CustomerSatisfaction", "CustomerSatisfaction", 4.0m, 3.5m, ComparisonOperator.LessThan, "Shipper", UserRole.Shipper, userId, ThresholdType.Business)
        };
    }
}
