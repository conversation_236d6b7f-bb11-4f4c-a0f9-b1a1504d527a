using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

public class CommissionAdjustment : BaseEntity
{
    public Guid CommissionId { get; private set; }
    public Money Amount { get; private set; }
    public CommissionAdjustmentType Type { get; private set; }
    public string Reason { get; private set; }

    public string? CreatedBy { get; private set; }

    // Navigation properties
    public Commission Commission { get; private set; } = null!;

    private CommissionAdjustment() { }

    public CommissionAdjustment(
        Guid commissionId,
        Money amount,
        CommissionAdjustmentType type,
        string reason,
        string? createdBy = null)
    {
        if (amount == null || amount.Amount <= 0)
            throw new ArgumentException("Amount must be greater than zero");

        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Reason cannot be empty");

        CommissionId = commissionId;
        Amount = amount;
        Type = type;
        Reason = reason;
        CreatedBy = createdBy;
    }
}
