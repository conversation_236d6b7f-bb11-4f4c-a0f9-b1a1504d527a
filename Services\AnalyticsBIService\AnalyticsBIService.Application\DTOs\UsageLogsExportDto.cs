using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Request DTO for exporting per-user usage logs
    /// </summary>
    public class UsageLogsExportRequestDto
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> DataSources { get; set; } = new(); // Orders, RFQs, Trips, Analytics
        public List<string> ActivityTypes { get; set; } = new(); // Login, Create, Update, etc.
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public UserType? UserType { get; set; }
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public bool IncludeMetadata { get; set; } = true;
        public bool GroupByUser { get; set; } = true;
        public bool GroupByDataSource { get; set; } = false;
        public int? MaxRecords { get; set; }
        public string? FilterExpression { get; set; }

        // Properties required by DataExportService
        public Guid UserId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<UserType> UserTypes { get; set; } = new();
        public bool IncludeDetails { get; set; } = true;

        // Audit fields (set by controller)
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response DTO for usage logs export
    /// </summary>
    public class UsageLogsExportResponseDto
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public int UserCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; }
        public List<DataSourceSummary> DataSourceSummary { get; set; } = new();
        public Dictionary<string, int> ActivityTypeCounts { get; set; } = new();
    }

    /// <summary>
    /// Summary of data from each source
    /// </summary>
    public class DataSourceSummary
    {
        public string DataSource { get; set; }
        public int RecordCount { get; set; }
        public int UniqueUsers { get; set; }
        public DateTime? EarliestRecord { get; set; }
        public DateTime? LatestRecord { get; set; }
        public List<string> AvailableColumns { get; set; } = new();
    }

    /// <summary>
    /// Individual usage log entry
    /// </summary>
    public class UsageLogEntryDto
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; }
        public string DataSource { get; set; }
        public string EntityId { get; set; }
        public string EntityType { get; set; }
        public string Action { get; set; }
        public DateTime Timestamp { get; set; }
        public string SessionId { get; set; }
        public string IpAddress { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Cross-service usage aggregation
    /// </summary>
    public class CrossServiceUsageDto
    {
        public Guid UserId { get; set; }
        public string UserType { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalActivities { get; set; }
        public int OrderActivities { get; set; }
        public int RfqActivities { get; set; }
        public int TripActivities { get; set; }
        public int AnalyticsEvents { get; set; }
        public DateTime? FirstActivity { get; set; }
        public DateTime? LastActivity { get; set; }
        public List<string> ActiveDataSources { get; set; } = new();
        public Dictionary<string, int> ActivityBreakdown { get; set; } = new();
    }
}
