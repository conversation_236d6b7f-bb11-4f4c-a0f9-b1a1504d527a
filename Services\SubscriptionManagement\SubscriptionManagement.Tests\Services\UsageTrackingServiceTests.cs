using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Services;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Services
{
    public class UsageTrackingServiceTests
    {
        private readonly Mock<IUsageRecordRepository> _usageRecordRepositoryMock;
        private readonly Mock<ISubscriptionRepository> _subscriptionRepositoryMock;
        private readonly Mock<IPlanRepository> _planRepositoryMock;
        private readonly Mock<ISubscriptionCacheService> _cacheServiceMock;
        private readonly Mock<IMessageBroker> _messageBrokerMock;
        private readonly Mock<ILogger<UsageTrackingService>> _loggerMock;
        private readonly UsageTrackingService _service;

        public UsageTrackingServiceTests()
        {
            _usageRecordRepositoryMock = new Mock<IUsageRecordRepository>();
            _subscriptionRepositoryMock = new Mock<ISubscriptionRepository>();
            _planRepositoryMock = new Mock<IPlanRepository>();
            _cacheServiceMock = new Mock<ISubscriptionCacheService>();
            _messageBrokerMock = new Mock<IMessageBroker>();
            _loggerMock = new Mock<ILogger<UsageTrackingService>>();

            _service = new UsageTrackingService(
                _usageRecordRepositoryMock.Object,
                _subscriptionRepositoryMock.Object,
                _planRepositoryMock.Object,
                _cacheServiceMock.Object,
                _messageBrokerMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task RecordUsageAsync_ValidUsage_ShouldRecordSuccessfully()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var count = 5;
            var metadata = "test metadata";

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(subscription);

            _usageRecordRepositoryMock.Setup(x => x.AddAsync(It.IsAny<UsageRecord>()))
                .ReturnsAsync((UsageRecord ur) => ur);

            // Act
            await _service.RecordUsageAsync(userId, featureType, count, metadata);

            // Assert
            _usageRecordRepositoryMock.Verify(x => x.AddAsync(It.Is<UsageRecord>(ur => 
                ur.UserId == userId && 
                ur.FeatureType == featureType && 
                ur.UsageCount == count)), Times.Once);

            _cacheServiceMock.Verify(x => x.InvalidateUserUsageCacheAsync(userId), Times.Once);
        }

        [Fact]
        public async Task RecordUsageAsync_NoSubscription_ShouldLogWarningAndReturn()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync((Subscription?)null);

            // Act
            await _service.RecordUsageAsync(userId, featureType);

            // Assert
            _usageRecordRepositoryMock.Verify(x => x.AddAsync(It.IsAny<UsageRecord>()), Times.Never);
        }

        [Fact]
        public async Task CanUseFeatureAsync_WithinLimits_ShouldReturnTrue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var requestedCount = 5;

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            plan.AddFeature(new PlanFeature(featureType, FeatureAccessType.Limit, 100));
            
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(subscription.PlanId))
                .ReturnsAsync(plan);

            var currentUsage = new Dictionary<FeatureType, int> { { featureType, 50 } };
            _usageRecordRepositoryMock.Setup(x => x.GetUsageSummaryAsync(userId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(currentUsage);

            // Act
            var result = await _service.CanUseFeatureAsync(userId, featureType, requestedCount);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CanUseFeatureAsync_ExceedsLimits_ShouldReturnFalse()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var requestedCount = 60;

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            plan.AddFeature(new PlanFeature(featureType, FeatureAccessType.Limit, 100));
            
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(subscription.PlanId))
                .ReturnsAsync(plan);

            var currentUsage = new Dictionary<FeatureType, int> { { featureType, 50 } };
            _usageRecordRepositoryMock.Setup(x => x.GetUsageSummaryAsync(userId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(currentUsage);

            // Act
            var result = await _service.CanUseFeatureAsync(userId, featureType, requestedCount);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CanUseFeatureAsync_UnlimitedFeature_ShouldReturnTrue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var requestedCount = 1000;

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Premium, UserType.Individual);
            plan.AddFeature(new PlanFeature(featureType, FeatureAccessType.Unlimited, null));
            
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(subscription.PlanId))
                .ReturnsAsync(plan);

            // Act
            var result = await _service.CanUseFeatureAsync(userId, featureType, requestedCount);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetRemainingUsageAsync_WithLimits_ShouldReturnCorrectAmount()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var limit = 100;
            var currentUsage = 30;

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            plan.AddFeature(new PlanFeature(featureType, FeatureAccessType.Limit, limit));
            
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);

            _subscriptionRepositoryMock.Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(subscription);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(subscription.PlanId))
                .ReturnsAsync(plan);

            var usageDict = new Dictionary<FeatureType, int> { { featureType, currentUsage } };
            _usageRecordRepositoryMock.Setup(x => x.GetUsageSummaryAsync(userId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(usageDict);

            // Act
            var result = await _service.GetRemainingUsageAsync(userId, featureType);

            // Assert
            Assert.Equal(limit - currentUsage, result);
        }

        [Fact]
        public async Task GetCurrentPeriodUsageAsync_WithCachedData_ShouldReturnCachedData()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var cachedUsage = new Dictionary<FeatureType, int> { { FeatureType.ApiCalls, 50 } };

            _cacheServiceMock.Setup(x => x.GetUserUsageAsync(userId, It.IsAny<string>()))
                .ReturnsAsync(cachedUsage);

            // Act
            var result = await _service.GetCurrentPeriodUsageAsync(userId);

            // Assert
            Assert.Equal(cachedUsage, result);
            _usageRecordRepositoryMock.Verify(x => x.GetUsageSummaryAsync(It.IsAny<Guid>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()), Times.Never);
        }

        [Fact]
        public async Task GetCurrentPeriodUsageAsync_NoCachedData_ShouldLoadFromDatabaseAndCache()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var dbUsage = new Dictionary<FeatureType, int> { { FeatureType.ApiCalls, 75 } };

            _cacheServiceMock.Setup(x => x.GetUserUsageAsync(userId, It.IsAny<string>()))
                .ReturnsAsync((Dictionary<FeatureType, int>?)null);

            _usageRecordRepositoryMock.Setup(x => x.GetUsageSummaryAsync(userId, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(dbUsage);

            // Act
            var result = await _service.GetCurrentPeriodUsageAsync(userId);

            // Assert
            Assert.Equal(dbUsage, result);
            _cacheServiceMock.Verify(x => x.SetUserUsageAsync(userId, It.IsAny<string>(), dbUsage, It.IsAny<TimeSpan>()), Times.Once);
        }

        [Fact]
        public async Task GetUsersNearLimitAsync_ShouldReturnUsersAboveThreshold()
        {
            // Arrange
            var featureType = FeatureType.ApiCalls;
            var threshold = 0.8;
            var userId1 = Guid.NewGuid();
            var userId2 = Guid.NewGuid();
            var userId3 = Guid.NewGuid();

            var plan = new Plan("Test Plan", "Test plan", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            plan.AddFeature(new PlanFeature(featureType, FeatureAccessType.Limit, 100));

            var subscriptions = new List<Subscription>
            {
                new Subscription(userId1, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations),
                new Subscription(userId2, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations),
                new Subscription(userId3, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations)
            };

            _subscriptionRepositoryMock.Setup(x => x.GetActiveSubscriptionsAsync())
                .ReturnsAsync(subscriptions);

            _planRepositoryMock.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(plan);

            // Setup usage: user1 = 85% (above threshold), user2 = 70% (below), user3 = 95% (above but over limit)
            _usageRecordRepositoryMock.SetupSequence(x => x.GetUsageSummaryAsync(It.IsAny<Guid>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(new Dictionary<FeatureType, int> { { featureType, 85 } }) // user1
                .ReturnsAsync(new Dictionary<FeatureType, int> { { featureType, 70 } }) // user2
                .ReturnsAsync(new Dictionary<FeatureType, int> { { featureType, 105 } }); // user3 (over limit)

            // Act
            var result = await _service.GetUsersNearLimitAsync(featureType, threshold);

            // Assert
            Assert.Single(result);
            Assert.Contains(userId1, result);
        }

        [Fact]
        public async Task SendUsageAlertAsync_ShouldPublishAlertMessage()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var featureType = FeatureType.ApiCalls;
            var currentUsage = 85;
            var limit = 100;

            // Act
            await _service.SendUsageAlertAsync(userId, featureType, currentUsage, limit);

            // Assert
            _messageBrokerMock.Verify(x => x.PublishAsync("usage.alert", It.Is<object>(obj => 
                obj.GetType().GetProperty("UserId")!.GetValue(obj)!.Equals(userId) &&
                obj.GetType().GetProperty("FeatureType")!.GetValue(obj)!.Equals(featureType.ToString()) &&
                obj.GetType().GetProperty("CurrentUsage")!.GetValue(obj)!.Equals(currentUsage) &&
                obj.GetType().GetProperty("Limit")!.GetValue(obj)!.Equals(limit)
            )), Times.Once);
        }
    }
}
