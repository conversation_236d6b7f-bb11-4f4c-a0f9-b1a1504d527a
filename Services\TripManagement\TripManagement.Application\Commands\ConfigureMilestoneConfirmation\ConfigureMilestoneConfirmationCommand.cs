using MediatR;

namespace TripManagement.Application.Commands.ConfigureMilestoneConfirmation;

public class ConfigureMilestoneConfirmationCommand : IRequest<ConfigureMilestoneConfirmationResponse>
{
    public Guid TripId { get; set; }
    public Guid? MilestoneId { get; set; } // If null, applies to all milestones in trip
    public MilestoneConfirmationSettings Settings { get; set; } = new();
}

public class ConfigureMilestoneConfirmationResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public MilestoneConfirmationConfiguration? Configuration { get; set; }
    public DateTime ConfiguredAt { get; set; }
}

public class MilestoneConfirmationSettings
{
    public bool RequireConfirmationPopup { get; set; } = true;
    public bool RequireDoubleConfirmation { get; set; } = false;
    public bool RequireReasonForCompletion { get; set; } = false;
    public bool RequirePhotoEvidence { get; set; } = false;
    public bool RequireLocationVerification { get; set; } = true;
    public bool RequireSignature { get; set; } = false;
    public bool AllowSkipWithReason { get; set; } = false;
    public bool EnableSequenceValidation { get; set; } = true;
    public bool AllowOutOfSequenceCompletion { get; set; } = false;
    public bool RequireManagerApproval { get; set; } = false;
    public int ConfirmationTimeoutSeconds { get; set; } = 30;
    public string ConfirmationMessage { get; set; } = "Are you sure you want to complete this milestone?";
    public string WarningMessage { get; set; } = "Please ensure all requirements are met before confirming.";
    public List<string> RequiredFields { get; set; } = new();
    public Dictionary<string, object> CustomValidationRules { get; set; } = new();
    public Dictionary<string, string> LocalizedMessages { get; set; } = new();
}

public class MilestoneConfirmationConfiguration
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid? MilestoneId { get; set; }
    public MilestoneConfirmationSettings Settings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid? UpdatedBy { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
