using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SubscriptionManagement.Tests.Performance.Analysis
{
    public interface IPerformanceAnalyzer
    {
        Task<PerformanceReport> AnalyzeBenchmarkResults(string benchmarkResultsPath);
        Task<PerformanceReport> AnalyzeLoadTestResults(string loadTestResultsPath);
        Task<PerformanceReport> GenerateComparisonReport(List<string> resultPaths);
        Task<List<PerformanceRecommendation>> GenerateRecommendations(PerformanceReport report);
    }

    public class PerformanceAnalyzer : IPerformanceAnalyzer
    {
        private readonly ILogger<PerformanceAnalyzer> _logger;

        public PerformanceAnalyzer(ILogger<PerformanceAnalyzer> logger)
        {
            _logger = logger;
        }

        public async Task<PerformanceReport> AnalyzeBenchmarkResults(string benchmarkResultsPath)
        {
            _logger.LogInformation("Analyzing benchmark results from {Path}", benchmarkResultsPath);

            var report = new PerformanceReport
            {
                TestType = "Benchmark",
                GeneratedAt = DateTime.UtcNow,
                ResultsPath = benchmarkResultsPath
            };

            try
            {
                if (!File.Exists(benchmarkResultsPath))
                {
                    _logger.LogWarning("Benchmark results file not found: {Path}", benchmarkResultsPath);
                    return report;
                }

                var jsonContent = await File.ReadAllTextAsync(benchmarkResultsPath);
                var benchmarkData = JsonSerializer.Deserialize<BenchmarkResults>(jsonContent);

                if (benchmarkData?.Benchmarks != null)
                {
                    foreach (var benchmark in benchmarkData.Benchmarks)
                    {
                        var metric = new PerformanceMetric
                        {
                            Name = benchmark.DisplayInfo,
                            Value = benchmark.Statistics.Mean,
                            Unit = benchmark.Statistics.Unit,
                            Category = ExtractCategory(benchmark.DisplayInfo),
                            Threshold = GetThreshold(benchmark.DisplayInfo),
                            Status = DetermineStatus(benchmark.Statistics.Mean, GetThreshold(benchmark.DisplayInfo))
                        };

                        report.Metrics.Add(metric);
                    }
                }

                report.Summary = GenerateBenchmarkSummary(report.Metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing benchmark results");
                report.Errors.Add($"Analysis error: {ex.Message}");
            }

            return report;
        }

        public async Task<PerformanceReport> AnalyzeLoadTestResults(string loadTestResultsPath)
        {
            _logger.LogInformation("Analyzing load test results from {Path}", loadTestResultsPath);

            var report = new PerformanceReport
            {
                TestType = "LoadTest",
                GeneratedAt = DateTime.UtcNow,
                ResultsPath = loadTestResultsPath
            };

            try
            {
                if (!File.Exists(loadTestResultsPath))
                {
                    _logger.LogWarning("Load test results file not found: {Path}", loadTestResultsPath);
                    return report;
                }

                var jsonContent = await File.ReadAllTextAsync(loadTestResultsPath);
                var loadTestData = JsonSerializer.Deserialize<LoadTestResults>(jsonContent);

                if (loadTestData?.ScenarioStats != null)
                {
                    foreach (var scenario in loadTestData.ScenarioStats)
                    {
                        // Response time metrics
                        report.Metrics.Add(new PerformanceMetric
                        {
                            Name = $"{scenario.ScenarioName}_ResponseTime_Mean",
                            Value = scenario.Ok.Response.Mean,
                            Unit = "ms",
                            Category = "ResponseTime",
                            Threshold = 1000, // 1 second threshold
                            Status = DetermineStatus(scenario.Ok.Response.Mean, 1000)
                        });

                        // Throughput metrics
                        report.Metrics.Add(new PerformanceMetric
                        {
                            Name = $"{scenario.ScenarioName}_Throughput",
                            Value = scenario.Ok.Request.Count / (double)loadTestData.TestSuite.Duration.TotalSeconds,
                            Unit = "req/sec",
                            Category = "Throughput",
                            Threshold = 10, // 10 req/sec minimum
                            Status = DetermineStatus(scenario.Ok.Request.Count / (double)loadTestData.TestSuite.Duration.TotalSeconds, 10)
                        });

                        // Error rate metrics
                        var totalRequests = scenario.Ok.Request.Count + scenario.Fail.Request.Count;
                        var errorRate = totalRequests > 0 ? (scenario.Fail.Request.Count / (double)totalRequests) * 100 : 0;
                        
                        report.Metrics.Add(new PerformanceMetric
                        {
                            Name = $"{scenario.ScenarioName}_ErrorRate",
                            Value = errorRate,
                            Unit = "%",
                            Category = "ErrorRate",
                            Threshold = 5, // 5% error rate threshold
                            Status = DetermineStatus(errorRate, 5, true) // true = lower is better
                        });
                    }
                }

                report.Summary = GenerateLoadTestSummary(report.Metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing load test results");
                report.Errors.Add($"Analysis error: {ex.Message}");
            }

            return report;
        }

        public async Task<PerformanceReport> GenerateComparisonReport(List<string> resultPaths)
        {
            _logger.LogInformation("Generating comparison report for {Count} result sets", resultPaths.Count);

            var comparisonReport = new PerformanceReport
            {
                TestType = "Comparison",
                GeneratedAt = DateTime.UtcNow,
                ResultsPath = string.Join(";", resultPaths)
            };

            var reports = new List<PerformanceReport>();

            foreach (var path in resultPaths)
            {
                if (path.Contains("benchmark", StringComparison.OrdinalIgnoreCase))
                {
                    reports.Add(await AnalyzeBenchmarkResults(path));
                }
                else if (path.Contains("loadtest", StringComparison.OrdinalIgnoreCase))
                {
                    reports.Add(await AnalyzeLoadTestResults(path));
                }
            }

            // Compare metrics across reports
            var metricGroups = reports
                .SelectMany(r => r.Metrics)
                .GroupBy(m => m.Name)
                .Where(g => g.Count() > 1);

            foreach (var group in metricGroups)
            {
                var metrics = group.OrderBy(m => m.Value).ToList();
                var best = metrics.First();
                var worst = metrics.Last();
                var improvement = worst.Value > 0 ? ((worst.Value - best.Value) / worst.Value) * 100 : 0;

                comparisonReport.Metrics.Add(new PerformanceMetric
                {
                    Name = $"{group.Key}_Improvement",
                    Value = improvement,
                    Unit = "%",
                    Category = "Comparison",
                    Status = improvement > 10 ? PerformanceStatus.Good : 
                            improvement > 0 ? PerformanceStatus.Warning : PerformanceStatus.Critical
                });
            }

            comparisonReport.Summary = GenerateComparisonSummary(comparisonReport.Metrics);

            return comparisonReport;
        }

        public async Task<List<PerformanceRecommendation>> GenerateRecommendations(PerformanceReport report)
        {
            var recommendations = new List<PerformanceRecommendation>();

            foreach (var metric in report.Metrics.Where(m => m.Status != PerformanceStatus.Good))
            {
                var recommendation = GenerateRecommendationForMetric(metric);
                if (recommendation != null)
                {
                    recommendations.Add(recommendation);
                }
            }

            // Add general recommendations based on patterns
            if (report.Metrics.Any(m => m.Category == "ResponseTime" && m.Status == PerformanceStatus.Critical))
            {
                recommendations.Add(new PerformanceRecommendation
                {
                    Category = "Database",
                    Priority = RecommendationPriority.High,
                    Title = "Optimize Database Queries",
                    Description = "High response times detected. Consider adding database indexes, optimizing queries, or implementing connection pooling.",
                    EstimatedImpact = "20-50% response time improvement"
                });
            }

            if (report.Metrics.Any(m => m.Category == "Cache" && m.Status != PerformanceStatus.Good))
            {
                recommendations.Add(new PerformanceRecommendation
                {
                    Category = "Caching",
                    Priority = RecommendationPriority.Medium,
                    Title = "Improve Cache Strategy",
                    Description = "Cache performance issues detected. Consider adjusting cache expiration times, implementing cache warming, or using distributed caching.",
                    EstimatedImpact = "10-30% response time improvement"
                });
            }

            return recommendations;
        }

        private string ExtractCategory(string benchmarkName)
        {
            if (benchmarkName.Contains("Cache", StringComparison.OrdinalIgnoreCase))
                return "Cache";
            if (benchmarkName.Contains("Database", StringComparison.OrdinalIgnoreCase))
                return "Database";
            if (benchmarkName.Contains("Metrics", StringComparison.OrdinalIgnoreCase))
                return "Metrics";
            if (benchmarkName.Contains("Subscription", StringComparison.OrdinalIgnoreCase))
                return "Subscription";
            
            return "General";
        }

        private double GetThreshold(string benchmarkName)
        {
            // Define performance thresholds based on operation type
            if (benchmarkName.Contains("Cache", StringComparison.OrdinalIgnoreCase))
                return 10; // 10ms for cache operations
            if (benchmarkName.Contains("Database", StringComparison.OrdinalIgnoreCase))
                return 100; // 100ms for database operations
            if (benchmarkName.Contains("Create", StringComparison.OrdinalIgnoreCase))
                return 500; // 500ms for creation operations
            
            return 1000; // 1 second default threshold
        }

        private PerformanceStatus DetermineStatus(double value, double threshold, bool lowerIsBetter = false)
        {
            if (lowerIsBetter)
            {
                if (value <= threshold * 0.5) return PerformanceStatus.Good;
                if (value <= threshold) return PerformanceStatus.Warning;
                return PerformanceStatus.Critical;
            }
            else
            {
                if (value >= threshold * 2) return PerformanceStatus.Good;
                if (value >= threshold) return PerformanceStatus.Warning;
                return PerformanceStatus.Critical;
            }
        }

        private string GenerateBenchmarkSummary(List<PerformanceMetric> metrics)
        {
            var good = metrics.Count(m => m.Status == PerformanceStatus.Good);
            var warning = metrics.Count(m => m.Status == PerformanceStatus.Warning);
            var critical = metrics.Count(m => m.Status == PerformanceStatus.Critical);

            return $"Benchmark Results: {good} good, {warning} warning, {critical} critical metrics";
        }

        private string GenerateLoadTestSummary(List<PerformanceMetric> metrics)
        {
            var responseTimeMetrics = metrics.Where(m => m.Category == "ResponseTime").ToList();
            var avgResponseTime = responseTimeMetrics.Any() ? responseTimeMetrics.Average(m => m.Value) : 0;
            
            var throughputMetrics = metrics.Where(m => m.Category == "Throughput").ToList();
            var avgThroughput = throughputMetrics.Any() ? throughputMetrics.Average(m => m.Value) : 0;

            return $"Load Test Results: Avg Response Time: {avgResponseTime:F2}ms, Avg Throughput: {avgThroughput:F2} req/sec";
        }

        private string GenerateComparisonSummary(List<PerformanceMetric> metrics)
        {
            var improvements = metrics.Where(m => m.Value > 0).ToList();
            var avgImprovement = improvements.Any() ? improvements.Average(m => m.Value) : 0;

            return $"Comparison Results: Average improvement: {avgImprovement:F2}%";
        }

        private PerformanceRecommendation? GenerateRecommendationForMetric(PerformanceMetric metric)
        {
            return metric.Category switch
            {
                "ResponseTime" when metric.Status == PerformanceStatus.Critical => new PerformanceRecommendation
                {
                    Category = "Performance",
                    Priority = RecommendationPriority.High,
                    Title = $"Optimize {metric.Name}",
                    Description = $"Response time of {metric.Value:F2}{metric.Unit} exceeds threshold of {metric.Threshold}{metric.Unit}",
                    EstimatedImpact = "Significant user experience improvement"
                },
                "ErrorRate" when metric.Status != PerformanceStatus.Good => new PerformanceRecommendation
                {
                    Category = "Reliability",
                    Priority = RecommendationPriority.High,
                    Title = $"Reduce Error Rate for {metric.Name}",
                    Description = $"Error rate of {metric.Value:F2}% is above acceptable threshold",
                    EstimatedImpact = "Improved system reliability"
                },
                _ => null
            };
        }
    }

    // Data models for analysis
    public class PerformanceReport
    {
        public string TestType { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public string ResultsPath { get; set; } = string.Empty;
        public List<PerformanceMetric> Metrics { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
    }

    public class PerformanceMetric
    {
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public double Threshold { get; set; }
        public PerformanceStatus Status { get; set; }
    }

    public class PerformanceRecommendation
    {
        public string Category { get; set; } = string.Empty;
        public RecommendationPriority Priority { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string EstimatedImpact { get; set; } = string.Empty;
    }

    public enum PerformanceStatus
    {
        Good,
        Warning,
        Critical
    }

    public enum RecommendationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    // External data models (simplified)
    public class BenchmarkResults
    {
        public List<BenchmarkResult> Benchmarks { get; set; } = new();
    }

    public class BenchmarkResult
    {
        public string DisplayInfo { get; set; } = string.Empty;
        public BenchmarkStatistics Statistics { get; set; } = new();
    }

    public class BenchmarkStatistics
    {
        public double Mean { get; set; }
        public string Unit { get; set; } = string.Empty;
    }

    public class LoadTestResults
    {
        public TestSuite TestSuite { get; set; } = new();
        public List<ScenarioStats> ScenarioStats { get; set; } = new();
    }

    public class TestSuite
    {
        public TimeSpan Duration { get; set; }
    }

    public class ScenarioStats
    {
        public string ScenarioName { get; set; } = string.Empty;
        public RequestStats Ok { get; set; } = new();
        public RequestStats Fail { get; set; } = new();
    }

    public class RequestStats
    {
        public RequestCount Request { get; set; } = new();
        public ResponseStats Response { get; set; } = new();
    }

    public class RequestCount
    {
        public int Count { get; set; }
    }

    public class ResponseStats
    {
        public double Mean { get; set; }
    }
}
