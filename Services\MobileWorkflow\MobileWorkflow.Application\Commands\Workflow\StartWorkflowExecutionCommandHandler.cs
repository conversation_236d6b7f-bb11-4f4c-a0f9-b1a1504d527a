using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Commands.Workflow;

public class StartWorkflowExecutionCommandHandler : IRequestHandler<StartWorkflowExecutionCommand, WorkflowExecutionDto>
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowExecutionRepository _executionRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<StartWorkflowExecutionCommandHandler> _logger;

    public StartWorkflowExecutionCommandHandler(
        IWorkflowRepository workflowRepository,
        IWorkflowExecutionRepository executionRepository,
        IMapper mapper,
        ILogger<StartWorkflowExecutionCommandHandler> logger)
    {
        _workflowRepository = workflowRepository;
        _executionRepository = executionRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<WorkflowExecutionDto> Handle(StartWorkflowExecutionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting workflow execution for workflow: {WorkflowId} by user: {TriggeredBy}", 
            request.WorkflowId, request.TriggeredBy);

        // Get workflow
        var workflow = await _workflowRepository.GetByIdAsync(request.WorkflowId, cancellationToken);
        if (workflow == null)
        {
            throw new InvalidOperationException($"Workflow with ID {request.WorkflowId} not found");
        }

        if (!workflow.CanExecute())
        {
            throw new InvalidOperationException($"Workflow {workflow.Name} cannot be executed. It may be inactive or have invalid definition.");
        }

        // Start execution
        var execution = workflow.StartExecution(request.TriggeredBy, request.InputData, request.TriggerSource);

        // Save execution
        var savedExecution = await _executionRepository.AddAsync(execution, cancellationToken);

        // Update workflow
        await _workflowRepository.UpdateAsync(workflow, cancellationToken);

        _logger.LogInformation("Workflow execution started successfully with ID: {ExecutionId}", savedExecution.Id);

        return _mapper.Map<WorkflowExecutionDto>(savedExecution);
    }
}
