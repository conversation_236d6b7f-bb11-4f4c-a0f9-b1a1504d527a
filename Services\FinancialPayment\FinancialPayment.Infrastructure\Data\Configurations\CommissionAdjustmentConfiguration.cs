using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class CommissionAdjustmentConfiguration : IEntityTypeConfiguration<CommissionAdjustment>
{
    public void Configure(EntityTypeBuilder<CommissionAdjustment> builder)
    {
        builder.ToTable("commission_adjustments");

        builder.<PERSON><PERSON>ey(a => a.Id);

        builder.Property(a => a.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(a => a.CommissionId)
            .HasColumnName("commission_id")
            .IsRequired();

        // Money value object
        builder.OwnsOne(a => a.Amount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(a => a.Type)
            .HasColumnName("type")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(a => a.Reason)
            .HasColumnName("reason")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(a => a.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(a => a.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(a => a.CommissionId)
            .HasDatabaseName("ix_commission_adjustments_commission_id");

        builder.HasIndex(a => a.Type)
            .HasDatabaseName("ix_commission_adjustments_type");

        builder.HasIndex(a => a.CreatedAt)
            .HasDatabaseName("ix_commission_adjustments_created_at");
    }
}
