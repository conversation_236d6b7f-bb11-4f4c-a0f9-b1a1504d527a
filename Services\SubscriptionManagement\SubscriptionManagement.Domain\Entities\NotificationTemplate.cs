using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.Entities
{
    /// <summary>
    /// Represents a notification template for subscription-related communications
    /// </summary>
    public class NotificationTemplate : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public NotificationType Type { get; private set; }
        public string Channel { get; private set; } // Email, SMS, InApp, WhatsApp
        public string Language { get; private set; } = "en";
        public string Subject { get; private set; }
        public string Body { get; private set; }
        public bool IsActive { get; private set; }
        public Dictionary<string, string> Variables { get; private set; }
        public Dictionary<string, string> Metadata { get; private set; }

        private NotificationTemplate()
        {
            Name = string.Empty;
            Description = string.Empty;
            Channel = string.Empty;
            Subject = string.Empty;
            Body = string.Empty;
            Variables = new Dictionary<string, string>();
            Metadata = new Dictionary<string, string>();
        }

        public NotificationTemplate(
            string name,
            string description,
            NotificationType type,
            string channel,
            string subject,
            string body,
            string language = "en",
            Dictionary<string, string>? variables = null,
            Dictionary<string, string>? metadata = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Template name cannot be empty");

            if (string.IsNullOrWhiteSpace(channel))
                throw new SubscriptionDomainException("Template channel cannot be empty");

            if (string.IsNullOrWhiteSpace(body))
                throw new SubscriptionDomainException("Template body cannot be empty");

            Name = name;
            Description = description ?? string.Empty;
            Type = type;
            Channel = channel;
            Language = language;
            Subject = subject ?? string.Empty;
            Body = body;
            IsActive = true;
            Variables = variables ?? new Dictionary<string, string>();
            Metadata = metadata ?? new Dictionary<string, string>();
        }

        public static NotificationTemplate Create(
            string name,
            string description,
            NotificationType type,
            string channel,
            string subject,
            string body,
            string language = "en",
            Dictionary<string, string>? variables = null,
            Dictionary<string, string>? metadata = null)
        {
            return new NotificationTemplate(name, description, type, channel, subject, body, language, variables, metadata);
        }

        public void UpdateContent(string subject, string body)
        {
            if (string.IsNullOrWhiteSpace(body))
                throw new SubscriptionDomainException("Template body cannot be empty");

            Subject = subject ?? string.Empty;
            Body = body;
            SetUpdatedAt();
        }

        public void UpdateDescription(string description)
        {
            Description = description ?? string.Empty;
            SetUpdatedAt();
        }

        public void Activate()
        {
            IsActive = true;
            SetUpdatedAt();
        }

        public void Deactivate()
        {
            IsActive = false;
            SetUpdatedAt();
        }

        public void AddVariable(string name, string description)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Variable name cannot be empty");

            Variables[name] = description ?? string.Empty;
            SetUpdatedAt();
        }

        public void RemoveVariable(string name)
        {
            if (Variables.ContainsKey(name))
            {
                Variables.Remove(name);
                SetUpdatedAt();
            }
        }

        public void AddMetadata(string key, string value)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new SubscriptionDomainException("Metadata key cannot be empty");

            Metadata[key] = value ?? string.Empty;
            SetUpdatedAt();
        }

        public void RemoveMetadata(string key)
        {
            if (Metadata.ContainsKey(key))
            {
                Metadata.Remove(key);
                SetUpdatedAt();
            }
        }

        public string RenderTemplate(Dictionary<string, string> values)
        {
            var renderedBody = Body;
            var renderedSubject = Subject;

            foreach (var variable in Variables.Keys)
            {
                var placeholder = $"{{{variable}}}";
                var value = values.ContainsKey(variable) ? values[variable] : $"[{variable}]";
                
                renderedBody = renderedBody.Replace(placeholder, value);
                renderedSubject = renderedSubject.Replace(placeholder, value);
            }

            return $"Subject: {renderedSubject}\n\nBody: {renderedBody}";
        }

        public List<string> GetMissingVariables(Dictionary<string, string> values)
        {
            return Variables.Keys.Where(variable => !values.ContainsKey(variable)).ToList();
        }

        public bool IsValidForChannel(string channel)
        {
            return Channel.Equals(channel, StringComparison.OrdinalIgnoreCase);
        }

        public bool IsValidForType(NotificationType type)
        {
            return Type == type;
        }

        public bool IsValidForLanguage(string language)
        {
            return Language.Equals(language, StringComparison.OrdinalIgnoreCase);
        }
    }
}
