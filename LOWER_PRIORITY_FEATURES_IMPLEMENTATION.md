# Lower Priority Features - Implementation Details

## Overview

Implementation of advanced features that provide additional value but are not critical for core broker operations. These features enhance user experience and provide competitive advantages.

## 1. OCR-based Preview & Validation

### Overview

Implement OCR processing capability in DataStorage service with document validation pipeline for automated document processing and preview generation.

### Implementation Strategy

#### 1.1 OCR Processing Service

**Enhanced DataStorage Service with OCR**:

```csharp
// Services/DataStorage/DataStorage.Domain/Entities/OcrProcessingJob.cs
public class OcrProcessingJob : Entity
{
    public Guid DocumentId { get; private set; }
    public string FilePath { get; private set; }
    public string FileName { get; private set; }
    public string MimeType { get; private set; }
    public OcrJobStatus Status { get; private set; }
    public OcrProvider Provider { get; private set; }
    public OcrProcessingResult? Result { get; private set; }
    public DateTime SubmittedAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? ErrorMessage { get; private set; }
    public int RetryCount { get; private set; }
    public OcrJobConfiguration Configuration { get; private set; }

    public OcrProcessingJob(
        Guid documentId,
        string filePath,
        string fileName,
        string mimeType,
        OcrProvider provider,
        OcrJobConfiguration configuration)
    {
        DocumentId = documentId;
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        MimeType = mimeType ?? throw new ArgumentNullException(nameof(mimeType));
        Provider = provider;
        Configuration = configuration;
        Status = OcrJobStatus.Pending;
        SubmittedAt = DateTime.UtcNow;
        RetryCount = 0;
        CreatedAt = DateTime.UtcNow;
    }

    public void StartProcessing()
    {
        if (Status != OcrJobStatus.Pending && Status != OcrJobStatus.Retrying)
            throw new InvalidOperationException("Can only start processing for pending or retrying jobs");

        Status = OcrJobStatus.Processing;
        StartedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void CompleteProcessing(OcrProcessingResult result)
    {
        if (Status != OcrJobStatus.Processing)
            throw new InvalidOperationException("Can only complete processing jobs");

        Status = OcrJobStatus.Completed;
        Result = result;
        CompletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void FailProcessing(string errorMessage)
    {
        Status = OcrJobStatus.Failed;
        ErrorMessage = errorMessage;
        CompletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void ScheduleRetry()
    {
        if (RetryCount >= Configuration.MaxRetryAttempts)
        {
            Status = OcrJobStatus.Failed;
            ErrorMessage = "Maximum retry attempts exceeded";
        }
        else
        {
            Status = OcrJobStatus.Retrying;
            RetryCount++;
        }
        UpdatedAt = DateTime.UtcNow;
    }
}

public enum OcrJobStatus
{
    Pending,
    Processing,
    Completed,
    Failed,
    Retrying,
    Cancelled
}

public enum OcrProvider
{
    AzureComputerVision,
    GoogleVisionApi,
    AmazonTextract,
    TesseractOcr
}

public class OcrJobConfiguration : ValueObject
{
    public int MaxRetryAttempts { get; private set; }
    public TimeSpan RetryDelay { get; private set; }
    public bool ExtractTables { get; private set; }
    public bool ExtractHandwriting { get; private set; }
    public List<string> LanguageCodes { get; private set; }
    public Dictionary<string, object> ProviderSpecificSettings { get; private set; }

    public OcrJobConfiguration(
        int maxRetryAttempts = 3,
        TimeSpan? retryDelay = null,
        bool extractTables = false,
        bool extractHandwriting = false,
        List<string>? languageCodes = null,
        Dictionary<string, object>? providerSpecificSettings = null)
    {
        MaxRetryAttempts = Math.Max(1, maxRetryAttempts);
        RetryDelay = retryDelay ?? TimeSpan.FromMinutes(5);
        ExtractTables = extractTables;
        ExtractHandwriting = extractHandwriting;
        LanguageCodes = languageCodes ?? new List<string> { "en" };
        ProviderSpecificSettings = providerSpecificSettings ?? new Dictionary<string, object>();
    }
}

public class OcrProcessingResult : ValueObject
{
    public string ExtractedText { get; private set; }
    public decimal ConfidenceScore { get; private set; }
    public List<OcrTextRegion> TextRegions { get; private set; }
    public List<OcrTable> Tables { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public TimeSpan ProcessingTime { get; private set; }

    public OcrProcessingResult(
        string extractedText,
        decimal confidenceScore,
        List<OcrTextRegion> textRegions,
        List<OcrTable> tables,
        Dictionary<string, object> metadata,
        TimeSpan processingTime)
    {
        ExtractedText = extractedText ?? string.Empty;
        ConfidenceScore = Math.Max(0, Math.Min(1, confidenceScore));
        TextRegions = textRegions ?? new List<OcrTextRegion>();
        Tables = tables ?? new List<OcrTable>();
        Metadata = metadata ?? new Dictionary<string, object>();
        ProcessingTime = processingTime;
    }
}

public class OcrTextRegion : ValueObject
{
    public string Text { get; private set; }
    public decimal Confidence { get; private set; }
    public BoundingBox BoundingBox { get; private set; }
    public string? Language { get; private set; }

    public OcrTextRegion(string text, decimal confidence, BoundingBox boundingBox, string? language = null)
    {
        Text = text ?? string.Empty;
        Confidence = confidence;
        BoundingBox = boundingBox;
        Language = language;
    }
}

public class BoundingBox : ValueObject
{
    public int X { get; private set; }
    public int Y { get; private set; }
    public int Width { get; private set; }
    public int Height { get; private set; }

    public BoundingBox(int x, int y, int width, int height)
    {
        X = x;
        Y = y;
        Width = Math.Max(0, width);
        Height = Math.Max(0, height);
    }
}
```

#### 1.2 OCR Service Implementation

**Azure Computer Vision OCR Service**:

```csharp
// Services/DataStorage/DataStorage.Infrastructure/Services/AzureOcrService.cs
public interface IOcrService
{
    Task<OcrProcessingResult> ProcessDocumentAsync(string filePath, OcrJobConfiguration configuration, CancellationToken cancellationToken = default);
    Task<DocumentPreview> GeneratePreviewAsync(string filePath, OcrProcessingResult ocrResult, CancellationToken cancellationToken = default);
    Task<bool> ValidateDocumentAsync(string filePath, string documentType, CancellationToken cancellationToken = default);
}

public class AzureOcrService : IOcrService
{
    private readonly ComputerVisionClient _client;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AzureOcrService> _logger;

    public AzureOcrService(ComputerVisionClient client, IConfiguration configuration, ILogger<AzureOcrService> logger)
    {
        _client = client;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<OcrProcessingResult> ProcessDocumentAsync(
        string filePath,
        OcrJobConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting OCR processing for file: {FilePath}", filePath);

            using var fileStream = File.OpenRead(filePath);

            // Submit OCR operation
            var operation = await _client.ReadInStreamAsync(fileStream, language: configuration.LanguageCodes.FirstOrDefault(), cancellationToken: cancellationToken);

            // Wait for completion
            var operationId = ExtractOperationId(operation.OperationLocation);
            ReadOperationResult result;

            do
            {
                await Task.Delay(1000, cancellationToken);
                result = await _client.GetReadResultAsync(Guid.Parse(operationId), cancellationToken);
            }
            while (result.Status == OperationStatusCodes.Running || result.Status == OperationStatusCodes.NotStarted);

            stopwatch.Stop();

            if (result.Status == OperationStatusCodes.Failed)
            {
                throw new OcrProcessingException($"OCR processing failed: {result.CreatedDateTime}");
            }

            // Process results
            var extractedText = new StringBuilder();
            var textRegions = new List<OcrTextRegion>();
            var tables = new List<OcrTable>();

            foreach (var page in result.AnalyzeResult.ReadResults)
            {
                foreach (var line in page.Lines)
                {
                    extractedText.AppendLine(line.Text);

                    var boundingBox = new BoundingBox(
                        (int)line.BoundingBox[0],
                        (int)line.BoundingBox[1],
                        (int)(line.BoundingBox[4] - line.BoundingBox[0]),
                        (int)(line.BoundingBox[5] - line.BoundingBox[1]));

                    textRegions.Add(new OcrTextRegion(line.Text, 0.95m, boundingBox));
                }
            }

            // Calculate overall confidence score
            var confidenceScore = textRegions.Any() ? textRegions.Average(r => r.Confidence) : 0;

            var metadata = new Dictionary<string, object>
            {
                ["provider"] = "Azure Computer Vision",
                ["pages_processed"] = result.AnalyzeResult.ReadResults.Count,
                ["operation_id"] = operationId,
                ["model_version"] = result.AnalyzeResult.Version
            };

            var ocrResult = new OcrProcessingResult(
                extractedText.ToString(),
                confidenceScore,
                textRegions,
                tables,
                metadata,
                stopwatch.Elapsed);

            _logger.LogInformation("OCR processing completed for file: {FilePath} in {ProcessingTime}ms",
                filePath, stopwatch.ElapsedMilliseconds);

            return ocrResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OCR for file: {FilePath}", filePath);
            throw new OcrProcessingException($"OCR processing failed: {ex.Message}", ex);
        }
    }

    public async Task<DocumentPreview> GeneratePreviewAsync(
        string filePath,
        OcrProcessingResult ocrResult,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Generate thumbnail
            var thumbnailPath = await GenerateThumbnailAsync(filePath, cancellationToken);

            // Extract key information based on document type
            var keyValuePairs = ExtractKeyValuePairs(ocrResult.ExtractedText);

            // Generate preview summary
            var summary = GenerateDocumentSummary(ocrResult.ExtractedText, keyValuePairs);

            var preview = new DocumentPreview(
                thumbnailPath,
                summary,
                keyValuePairs,
                ocrResult.ConfidenceScore,
                ocrResult.ExtractedText.Length,
                ocrResult.TextRegions.Count);

            return preview;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating preview for file: {FilePath}", filePath);
            throw;
        }
    }

    private async Task<string> GenerateThumbnailAsync(string filePath, CancellationToken cancellationToken)
    {
        var thumbnailDirectory = Path.Combine(Path.GetDirectoryName(filePath)!, "thumbnails");
        Directory.CreateDirectory(thumbnailDirectory);

        var thumbnailFileName = $"{Path.GetFileNameWithoutExtension(filePath)}_thumb.jpg";
        var thumbnailPath = Path.Combine(thumbnailDirectory, thumbnailFileName);

        using var fileStream = File.OpenRead(filePath);
        var thumbnail = await _client.GenerateThumbnailInStreamAsync(200, 200, fileStream, smartCropping: true, cancellationToken: cancellationToken);

        using var thumbnailFileStream = File.Create(thumbnailPath);
        await thumbnail.CopyToAsync(thumbnailFileStream, cancellationToken);

        return thumbnailPath;
    }

    private Dictionary<string, string> ExtractKeyValuePairs(string text)
    {
        var keyValuePairs = new Dictionary<string, string>();

        // Common patterns for Indian documents
        var patterns = new Dictionary<string, string>
        {
            ["PAN"] = @"[A-Z]{5}[0-9]{4}[A-Z]{1}",
            ["GST"] = @"[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[Z0-9]{1}[0-9]{1}",
            ["Aadhar"] = @"[0-9]{4}\s?[0-9]{4}\s?[0-9]{4}",
            ["Phone"] = @"[+]?[0-9]{10,12}",
            ["Email"] = @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        };

        foreach (var pattern in patterns)
        {
            var matches = Regex.Matches(text, pattern.Value, RegexOptions.IgnoreCase);
            if (matches.Count > 0)
            {
                keyValuePairs[pattern.Key] = matches[0].Value;
            }
        }

        return keyValuePairs;
    }

    private string GenerateDocumentSummary(string text, Dictionary<string, string> keyValuePairs)
    {
        var summary = new StringBuilder();

        if (keyValuePairs.ContainsKey("PAN"))
            summary.AppendLine($"PAN Number: {keyValuePairs["PAN"]}");

        if (keyValuePairs.ContainsKey("GST"))
            summary.AppendLine($"GST Number: {keyValuePairs["GST"]}");

        if (keyValuePairs.ContainsKey("Aadhar"))
            summary.AppendLine($"Aadhar Number: {keyValuePairs["Aadhar"]}");

        // Extract name (simple heuristic)
        var lines = text.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var namePattern = @"^[A-Z][a-z]+ [A-Z][a-z]+";
        var nameLine = lines.FirstOrDefault(line => Regex.IsMatch(line.Trim(), namePattern));
        if (!string.IsNullOrEmpty(nameLine))
        {
            summary.AppendLine($"Name: {nameLine.Trim()}");
        }

        return summary.ToString();
    }

    private string ExtractOperationId(string operationLocation)
    {
        return operationLocation.Split('/').Last();
    }
}

public class DocumentPreview : ValueObject
{
    public string ThumbnailPath { get; private set; }
    public string Summary { get; private set; }
    public Dictionary<string, string> KeyValuePairs { get; private set; }
    public decimal ConfidenceScore { get; private set; }
    public int TextLength { get; private set; }
    public int RegionCount { get; private set; }
    public DateTime GeneratedAt { get; private set; }

    public DocumentPreview(
        string thumbnailPath,
        string summary,
        Dictionary<string, string> keyValuePairs,
        decimal confidenceScore,
        int textLength,
        int regionCount)
    {
        ThumbnailPath = thumbnailPath ?? string.Empty;
        Summary = summary ?? string.Empty;
        KeyValuePairs = keyValuePairs ?? new Dictionary<string, string>();
        ConfidenceScore = confidenceScore;
        TextLength = textLength;
        RegionCount = regionCount;
        GeneratedAt = DateTime.UtcNow;
    }
}

public class OcrProcessingException : Exception
{
    public OcrProcessingException(string message) : base(message) { }
    public OcrProcessingException(string message, Exception innerException) : base(message, innerException) { }
}
```

## 2. Drag-and-drop Reorder Stops

### Overview

Implement UI components for reordering trip stops with route recalculation in TripManagement service.

### Implementation Strategy

#### 2.1 Enhanced Trip Stop Management

**Updated TripStop Entity with Reordering**:

```csharp
// Services/TripManagement/TripManagement.Domain/Entities/TripStop.cs
public class TripStop : Entity
{
    // Existing properties...
    public int SequenceOrder { get; private set; }
    public bool IsReorderable { get; private set; }
    public TripStopDependencies? Dependencies { get; private set; }
    public RouteCalculationData? RouteData { get; private set; }

    public void UpdateSequenceOrder(int newOrder)
    {
        if (newOrder < 1)
            throw new ArgumentException("Sequence order must be greater than 0");

        if (!IsReorderable)
            throw new TripManagementDomainException("This stop cannot be reordered due to dependencies");

        SequenceOrder = newOrder;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetReorderability(bool isReorderable, TripStopDependencies? dependencies = null)
    {
        IsReorderable = isReorderable;
        Dependencies = dependencies;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateRouteData(RouteCalculationData routeData)
    {
        RouteData = routeData;
        UpdatedAt = DateTime.UtcNow;
    }
}

public class TripStopDependencies : ValueObject
{
    public List<Guid> DependsOnStops { get; private set; }
    public List<Guid> RequiredBeforeStops { get; private set; }
    public string? RestrictionReason { get; private set; }
    public bool HasTimeConstraints { get; private set; }

    public TripStopDependencies(
        List<Guid>? dependsOnStops = null,
        List<Guid>? requiredBeforeStops = null,
        string? restrictionReason = null,
        bool hasTimeConstraints = false)
    {
        DependsOnStops = dependsOnStops ?? new List<Guid>();
        RequiredBeforeStops = requiredBeforeStops ?? new List<Guid>();
        RestrictionReason = restrictionReason;
        HasTimeConstraints = hasTimeConstraints;
    }
}

public class RouteCalculationData : ValueObject
{
    public decimal DistanceFromPrevious { get; private set; }
    public TimeSpan EstimatedTravelTime { get; private set; }
    public decimal EstimatedFuelCost { get; private set; }
    public List<RouteWaypoint> Waypoints { get; private set; }
    public DateTime CalculatedAt { get; private set; }

    public RouteCalculationData(
        decimal distanceFromPrevious,
        TimeSpan estimatedTravelTime,
        decimal estimatedFuelCost,
        List<RouteWaypoint> waypoints)
    {
        DistanceFromPrevious = distanceFromPrevious;
        EstimatedTravelTime = estimatedTravelTime;
        EstimatedFuelCost = estimatedFuelCost;
        Waypoints = waypoints ?? new List<RouteWaypoint>();
        CalculatedAt = DateTime.UtcNow;
    }
}
```

#### 2.2 Trip Stop Reordering Command

**Reorder Trip Stops Command**:

```csharp
// Services/TripManagement/TripManagement.Application/Commands/ReorderTripStops/ReorderTripStopsCommand.cs
public class ReorderTripStopsCommand : IRequest<ReorderTripStopsResponse>
{
    public Guid TripId { get; set; }
    public List<TripStopReorderItem> NewStopOrder { get; set; } = new();
    public bool RecalculateRoute { get; set; } = true;
    public bool ValidateDependencies { get; set; } = true;
}

public class TripStopReorderItem
{
    public Guid StopId { get; set; }
    public int NewSequenceOrder { get; set; }
}

public class ReorderTripStopsResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<TripStopDto> ReorderedStops { get; set; } = new();
    public RouteRecalculationResult? RouteRecalculation { get; set; }
    public List<string> Warnings { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
}

// Handler implementation
public class ReorderTripStopsCommandHandler : IRequestHandler<ReorderTripStopsCommand, ReorderTripStopsResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IRouteCalculationService _routeService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ReorderTripStopsCommandHandler> _logger;

    public async Task<ReorderTripStopsResponse> Handle(ReorderTripStopsCommand request, CancellationToken cancellationToken)
    {
        var response = new ReorderTripStopsResponse();

        // Get trip
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
        {
            response.Success = false;
            response.Message = $"Trip {request.TripId} not found";
            return response;
        }

        // Validate trip status
        if (trip.Status == TripStatus.InProgress || trip.Status == TripStatus.Completed)
        {
            response.Success = false;
            response.Message = "Cannot reorder stops for trips that are in progress or completed";
            return response;
        }

        // Validate dependencies if requested
        if (request.ValidateDependencies)
        {
            var validationErrors = ValidateStopDependencies(trip, request.NewStopOrder);
            if (validationErrors.Any())
            {
                response.Success = false;
                response.Message = "Stop reordering violates dependencies";
                response.ValidationErrors = validationErrors;
                return response;
            }
        }

        try
        {
            // Reorder stops
            foreach (var reorderItem in request.NewStopOrder)
            {
                var stop = trip.Stops.FirstOrDefault(s => s.Id == reorderItem.StopId);
                if (stop != null)
                {
                    stop.UpdateSequenceOrder(reorderItem.NewSequenceOrder);
                }
            }

            // Recalculate route if requested
            RouteRecalculationResult? routeRecalculation = null;
            if (request.RecalculateRoute)
            {
                routeRecalculation = await RecalculateRouteAsync(trip, cancellationToken);
                response.RouteRecalculation = routeRecalculation;

                if (routeRecalculation.HasWarnings)
                {
                    response.Warnings.AddRange(routeRecalculation.Warnings);
                }
            }

            // Save changes
            await _tripRepository.UpdateAsync(trip, cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("trip.stops.reordered", new
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                StopCount = trip.Stops.Count,
                ReorderedStops = request.NewStopOrder,
                RouteRecalculated = request.RecalculateRoute,
                NewTotalDistance = routeRecalculation?.TotalDistance,
                NewEstimatedDuration = routeRecalculation?.TotalDuration,
                ReorderedAt = DateTime.UtcNow
            }, cancellationToken);

            response.Success = true;
            response.Message = "Trip stops reordered successfully";
            response.ReorderedStops = trip.Stops.OrderBy(s => s.SequenceOrder).Select(MapToDto).ToList();

            _logger.LogInformation("Successfully reordered {StopCount} stops for trip {TripId}",
                request.NewStopOrder.Count, request.TripId);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering stops for trip {TripId}", request.TripId);
            response.Success = false;
            response.Message = $"Error reordering stops: {ex.Message}";
            return response;
        }
    }

    private List<string> ValidateStopDependencies(Trip trip, List<TripStopReorderItem> newOrder)
    {
        var errors = new List<string>();

        foreach (var stop in trip.Stops)
        {
            if (stop.Dependencies != null)
            {
                var newStopOrder = newOrder.FirstOrDefault(o => o.StopId == stop.Id);
                if (newStopOrder != null)
                {
                    // Check if dependent stops come before this stop
                    foreach (var dependentStopId in stop.Dependencies.DependsOnStops)
                    {
                        var dependentStopOrder = newOrder.FirstOrDefault(o => o.StopId == dependentStopId);
                        if (dependentStopOrder != null && dependentStopOrder.NewSequenceOrder >= newStopOrder.NewSequenceOrder)
                        {
                            errors.Add($"Stop {stop.Id} depends on stop {dependentStopId} which must come before it");
                        }
                    }

                    // Check if required-before stops come after this stop
                    foreach (var requiredBeforeStopId in stop.Dependencies.RequiredBeforeStops)
                    {
                        var requiredBeforeStopOrder = newOrder.FirstOrDefault(o => o.StopId == requiredBeforeStopId);
                        if (requiredBeforeStopOrder != null && requiredBeforeStopOrder.NewSequenceOrder <= newStopOrder.NewSequenceOrder)
                        {
                            errors.Add($"Stop {stop.Id} must come before stop {requiredBeforeStopId}");
                        }
                    }
                }
            }
        }

        return errors;
    }

    private async Task<RouteRecalculationResult> RecalculateRouteAsync(Trip trip, CancellationToken cancellationToken)
    {
        var orderedStops = trip.Stops.OrderBy(s => s.SequenceOrder).ToList();
        var waypoints = orderedStops.Select(s => new RouteWaypoint(s.Location.Latitude, s.Location.Longitude, s.Address)).ToList();

        var routeCalculation = await _routeService.CalculateOptimalRouteAsync(waypoints, cancellationToken);

        // Update route data for each stop
        for (int i = 0; i < orderedStops.Count; i++)
        {
            var stop = orderedStops[i];
            var routeSegment = routeCalculation.RouteSegments.ElementAtOrDefault(i);

            if (routeSegment != null)
            {
                var routeData = new RouteCalculationData(
                    routeSegment.Distance,
                    routeSegment.Duration,
                    routeSegment.EstimatedFuelCost,
                    routeSegment.Waypoints);

                stop.UpdateRouteData(routeData);
            }
        }

        return new RouteRecalculationResult(
            routeCalculation.TotalDistance,
            routeCalculation.TotalDuration,
            routeCalculation.TotalFuelCost,
            routeCalculation.Warnings);
    }
}

public class RouteRecalculationResult
{
    public decimal TotalDistance { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public decimal TotalFuelCost { get; set; }
    public List<string> Warnings { get; set; } = new();
    public bool HasWarnings => Warnings.Any();

    public RouteRecalculationResult(decimal totalDistance, TimeSpan totalDuration, decimal totalFuelCost, List<string> warnings)
    {
        TotalDistance = totalDistance;
        TotalDuration = totalDuration;
        TotalFuelCost = totalFuelCost;
        Warnings = warnings ?? new List<string>();
    }
}
```

## 3. Export to Excel/PDF Functionality

### Overview

Implement comprehensive report export capabilities in AnalyticsBIService with multiple formats for broker analytics and reporting.

### Implementation Strategy

#### 3.1 Enhanced Export Service

**Comprehensive Export Service**:

```csharp
// Services/AnalyticsBIService/AnalyticsBIService.Application/Services/ReportExportService.cs
public interface IReportExportService
{
    Task<ExportResult> ExportToExcelAsync(ExportRequest request, CancellationToken cancellationToken = default);
    Task<ExportResult> ExportToPdfAsync(ExportRequest request, CancellationToken cancellationToken = default);
    Task<ExportResult> ExportToCsvAsync(ExportRequest request, CancellationToken cancellationToken = default);
    Task<List<ExportTemplate>> GetAvailableTemplatesAsync(string reportType, CancellationToken cancellationToken = default);
}

public class ReportExportService : IReportExportService
{
    private readonly IAnalyticsDataService _analyticsService;
    private readonly IFileStorageService _fileStorage;
    private readonly ITemplateService _templateService;
    private readonly ILogger<ReportExportService> _logger;

    public async Task<ExportResult> ExportToExcelAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting Excel export for report type: {ReportType}", request.ReportType);

            // Get data
            var data = await GetReportDataAsync(request, cancellationToken);

            // Create Excel workbook
            using var workbook = new XLWorkbook();

            // Add summary sheet
            var summarySheet = workbook.Worksheets.Add("Summary");
            await PopulateSummarySheetAsync(summarySheet, data, request);

            // Add detailed data sheets
            foreach (var dataSet in data.DataSets)
            {
                var dataSheet = workbook.Worksheets.Add(dataSet.Name);
                await PopulateDataSheetAsync(dataSheet, dataSet, request);
            }

            // Add charts if requested
            if (request.IncludeCharts)
            {
                var chartSheet = workbook.Worksheets.Add("Charts");
                await AddChartsToSheetAsync(chartSheet, data, request);
            }

            // Save to file
            var fileName = GenerateFileName(request, "xlsx");
            var filePath = Path.Combine(Path.GetTempPath(), fileName);
            workbook.SaveAs(filePath);

            // Upload to storage
            var storageUrl = await _fileStorage.UploadFileAsync(filePath, $"exports/{fileName}", cancellationToken);

            // Clean up temp file
            File.Delete(filePath);

            var result = new ExportResult
            {
                Success = true,
                FileName = fileName,
                FileUrl = storageUrl,
                FileSize = new FileInfo(filePath).Length,
                ExportFormat = ExportFormat.Excel,
                GeneratedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(7)
            };

            _logger.LogInformation("Excel export completed successfully: {FileName}", fileName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to Excel for request: {@Request}", request);
            return new ExportResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<ExportResult> ExportToPdfAsync(ExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting PDF export for report type: {ReportType}", request.ReportType);

            // Get data
            var data = await GetReportDataAsync(request, cancellationToken);

            // Get template
            var template = await _templateService.GetTemplateAsync(request.ReportType, ExportFormat.Pdf, cancellationToken);

            // Generate PDF
            var pdfBytes = await GeneratePdfFromTemplateAsync(template, data, request, cancellationToken);

            // Save to file
            var fileName = GenerateFileName(request, "pdf");
            var filePath = Path.Combine(Path.GetTempPath(), fileName);
            await File.WriteAllBytesAsync(filePath, pdfBytes, cancellationToken);

            // Upload to storage
            var storageUrl = await _fileStorage.UploadFileAsync(filePath, $"exports/{fileName}", cancellationToken);

            // Clean up temp file
            File.Delete(filePath);

            var result = new ExportResult
            {
                Success = true,
                FileName = fileName,
                FileUrl = storageUrl,
                FileSize = pdfBytes.Length,
                ExportFormat = ExportFormat.Pdf,
                GeneratedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(7)
            };

            _logger.LogInformation("PDF export completed successfully: {FileName}", fileName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to PDF for request: {@Request}", request);
            return new ExportResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<ReportData> GetReportDataAsync(ExportRequest request, CancellationToken cancellationToken)
    {
        return request.ReportType switch
        {
            "BrokerFunnelAnalytics" => await _analyticsService.GetBrokerFunnelDataAsync(request, cancellationToken),
            "BrokerPerformance" => await _analyticsService.GetBrokerPerformanceDataAsync(request, cancellationToken),
            "QuoteAnalytics" => await _analyticsService.GetQuoteAnalyticsDataAsync(request, cancellationToken),
            "OrderAnalytics" => await _analyticsService.GetOrderAnalyticsDataAsync(request, cancellationToken),
            _ => throw new NotSupportedException($"Report type {request.ReportType} is not supported")
        };
    }

    private async Task PopulateSummarySheetAsync(IXLWorksheet sheet, ReportData data, ExportRequest request)
    {
        // Add header
        sheet.Cell(1, 1).Value = $"{request.ReportType} Summary";
        sheet.Cell(1, 1).Style.Font.Bold = true;
        sheet.Cell(1, 1).Style.Font.FontSize = 16;

        // Add metadata
        var row = 3;
        sheet.Cell(row, 1).Value = "Generated At:";
        sheet.Cell(row, 2).Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        row++;

        sheet.Cell(row, 1).Value = "Date Range:";
        sheet.Cell(row, 2).Value = $"{request.FromDate:yyyy-MM-dd} to {request.ToDate:yyyy-MM-dd}";
        row++;

        sheet.Cell(row, 1).Value = "User ID:";
        sheet.Cell(row, 2).Value = request.UserId.ToString();
        row += 2;

        // Add summary metrics
        if (data.SummaryMetrics.Any())
        {
            sheet.Cell(row, 1).Value = "Key Metrics";
            sheet.Cell(row, 1).Style.Font.Bold = true;
            row++;

            foreach (var metric in data.SummaryMetrics)
            {
                sheet.Cell(row, 1).Value = metric.Key;
                sheet.Cell(row, 2).Value = metric.Value;
                row++;
            }
        }

        // Auto-fit columns
        sheet.ColumnsUsed().AdjustToContents();
    }

    private async Task PopulateDataSheetAsync(IXLWorksheet sheet, ReportDataSet dataSet, ExportRequest request)
    {
        if (!dataSet.Data.Any()) return;

        // Add headers
        var headers = dataSet.Data.First().Keys.ToList();
        for (int i = 0; i < headers.Count; i++)
        {
            sheet.Cell(1, i + 1).Value = headers[i];
            sheet.Cell(1, i + 1).Style.Font.Bold = true;
        }

        // Add data
        var row = 2;
        foreach (var dataRow in dataSet.Data)
        {
            for (int i = 0; i < headers.Count; i++)
            {
                var value = dataRow[headers[i]];
                sheet.Cell(row, i + 1).Value = value?.ToString() ?? "";
            }
            row++;
        }

        // Auto-fit columns
        sheet.ColumnsUsed().AdjustToContents();

        // Add table formatting
        var dataRange = sheet.Range(1, 1, row - 1, headers.Count);
        var table = dataRange.CreateTable();
        table.Theme = XLTableTheme.TableStyleMedium2;
    }

    private string GenerateFileName(ExportRequest request, string extension)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var sanitizedReportType = request.ReportType.Replace(" ", "_");
        return $"{sanitizedReportType}_{request.UserId}_{timestamp}.{extension}";
    }
}

public class ExportRequest
{
    public string ReportType { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public ExportFormat Format { get; set; }
    public bool IncludeCharts { get; set; } = true;
    public bool IncludeSummary { get; set; } = true;
    public List<string> SelectedColumns { get; set; } = new();
    public Dictionary<string, object> FilterCriteria { get; set; } = new();
    public string? TemplateId { get; set; }
}

public class ExportResult
{
    public bool Success { get; set; }
    public string? FileName { get; set; }
    public string? FileUrl { get; set; }
    public long FileSize { get; set; }
    public ExportFormat ExportFormat { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }
}

public enum ExportFormat
{
    Excel,
    Pdf,
    Csv,
    Json
}
```

## 4. Language Preference Selection UI

### Overview

Implement multi-language support with user preference management in UserManagement service.

### Implementation Strategy

#### 4.1 Language Preference Management

**Enhanced User Language Preferences**:

```csharp
// Services/UserManagement/UserManagement.Domain/ValueObjects/LanguagePreferences.cs
public class LanguagePreferences : ValueObject
{
    public string PrimaryLanguage { get; private set; }
    public string? SecondaryLanguage { get; private set; }
    public string DateFormat { get; private set; }
    public string TimeFormat { get; private set; }
    public string NumberFormat { get; private set; }
    public string CurrencyFormat { get; private set; }
    public bool UseRtlLayout { get; private set; }
    public Dictionary<string, string> ContextSpecificLanguages { get; private set; }

    public LanguagePreferences(
        string primaryLanguage,
        string? secondaryLanguage = null,
        string? dateFormat = null,
        string? timeFormat = null,
        string? numberFormat = null,
        string? currencyFormat = null,
        bool useRtlLayout = false,
        Dictionary<string, string>? contextSpecificLanguages = null)
    {
        PrimaryLanguage = ValidateLanguageCode(primaryLanguage);
        SecondaryLanguage = secondaryLanguage != null ? ValidateLanguageCode(secondaryLanguage) : null;
        DateFormat = dateFormat ?? GetDefaultDateFormat(primaryLanguage);
        TimeFormat = timeFormat ?? GetDefaultTimeFormat(primaryLanguage);
        NumberFormat = numberFormat ?? GetDefaultNumberFormat(primaryLanguage);
        CurrencyFormat = currencyFormat ?? GetDefaultCurrencyFormat(primaryLanguage);
        UseRtlLayout = useRtlLayout;
        ContextSpecificLanguages = contextSpecificLanguages ?? new Dictionary<string, string>();
    }

    private string ValidateLanguageCode(string languageCode)
    {
        var supportedLanguages = new[] { "en", "hi", "mr", "gu", "ta", "te", "kn", "ml", "bn", "pa" };

        if (!supportedLanguages.Contains(languageCode.ToLower()))
            throw new ArgumentException($"Language code '{languageCode}' is not supported");

        return languageCode.ToLower();
    }

    private string GetDefaultDateFormat(string languageCode)
    {
        return languageCode switch
        {
            "en" => "MM/dd/yyyy",
            "hi" => "dd/MM/yyyy",
            _ => "dd/MM/yyyy"
        };
    }

    private string GetDefaultTimeFormat(string languageCode)
    {
        return languageCode switch
        {
            "en" => "hh:mm tt",
            _ => "HH:mm"
        };
    }

    private string GetDefaultNumberFormat(string languageCode)
    {
        return languageCode switch
        {
            "en" => "en-US",
            "hi" => "hi-IN",
            _ => "en-IN"
        };
    }

    private string GetDefaultCurrencyFormat(string languageCode)
    {
        return "₹#,##0.00"; // Indian Rupee format for all languages
    }
}
```

This comprehensive implementation covers all lower priority features with detailed technical specifications, providing OCR processing, trip stop reordering, export functionality, and language preference management.
