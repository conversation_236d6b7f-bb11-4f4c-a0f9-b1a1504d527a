using System;
using System.Threading.Tasks;
using Identity.Application.Users.Queries.GetUserById;
using Identity.Application.Users.Queries.CheckUserPermission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Identity.API.Controllers
{
    public class UsersController : BaseController
    {
        [HttpGet("{userId}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetById(Guid userId)
        {
            var query = new GetUserByIdQuery { UserId = userId };
            var user = await Mediator.Send(query);

            if (user == null)
            {
                return NotFound();
            }

            return Ok(user);
        }

        [HttpGet("{userId}/permissions/{permission}")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CheckPermission(Guid userId, string permission)
        {
            var query = new CheckUserPermissionQuery
            {
                UserId = userId,
                Permission = permission
            };

            var hasPermission = await Mediator.Send(query);
            return Ok(new { hasPermission });
        }
    }
}
