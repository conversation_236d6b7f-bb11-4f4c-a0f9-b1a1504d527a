using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing a dispute related to an order
/// </summary>
public class OrderDispute : BaseEntity
{
    public Guid OrderId { get; private set; }
    public string DisputeNumber { get; private set; } = string.Empty;
    public DisputeType DisputeType { get; private set; }
    public DisputeStatus Status { get; private set; }
    public DisputePriority Priority { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public decimal? DisputedAmount { get; private set; }
    public string? DisputedCurrency { get; private set; }

    // Parties involved
    public Guid RaisedBy { get; private set; }
    public string RaisedByType { get; private set; } = string.Empty; // TransportCompany, Broker, Carrier
    public Guid? RespondentId { get; private set; }
    public string? RespondentType { get; private set; }

    // Timestamps
    public DateTime RaisedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public DateTime? ClosedAt { get; private set; }
    public DateTime? EscalatedAt { get; private set; }

    // Resolution details
    public string? ResolutionSummary { get; private set; }
    public decimal? SettlementAmount { get; private set; }
    public string? SettlementCurrency { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? ResolverType { get; private set; } // Admin, Mediator, System

    // Additional properties
    public string? Category { get; private set; }
    public List<string> Tags { get; private set; } = new();
    public Dictionary<string, object>? Metadata { get; private set; }
    public bool RequiresPaymentHold { get; private set; }
    public bool IsEscalated { get; private set; }
    public int EscalationLevel { get; private set; }

    // Navigation properties
    public Order Order { get; private set; } = null!;
    private readonly List<DisputeComment> _comments = new();
    public IReadOnlyCollection<DisputeComment> Comments => _comments.AsReadOnly();

    private readonly List<DisputeDocument> _documents = new();
    public IReadOnlyCollection<DisputeDocument> Documents => _documents.AsReadOnly();

    private readonly List<DisputeStatusHistory> _statusHistory = new();
    public IReadOnlyCollection<DisputeStatusHistory> StatusHistory => _statusHistory.AsReadOnly();

    private OrderDispute() { } // EF Constructor

    public OrderDispute(
        Guid orderId,
        DisputeType disputeType,
        string title,
        string description,
        Guid raisedBy,
        string raisedByType,
        decimal? disputedAmount = null,
        string? disputedCurrency = null,
        DisputePriority priority = DisputePriority.Medium,
        string? category = null,
        List<string>? tags = null,
        bool requiresPaymentHold = false)
    {
        OrderId = orderId;
        DisputeNumber = GenerateDisputeNumber();
        DisputeType = disputeType;
        Status = DisputeStatus.Open;
        Priority = priority;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        DisputedAmount = disputedAmount;
        DisputedCurrency = disputedCurrency;
        RaisedBy = raisedBy;
        RaisedByType = raisedByType ?? throw new ArgumentNullException(nameof(raisedByType));
        RaisedAt = DateTime.UtcNow;
        Category = category;
        Tags = tags ?? new List<string>();
        RequiresPaymentHold = requiresPaymentHold;
        IsEscalated = false;
        EscalationLevel = 0;

        // Add initial status history
        AddStatusHistory(DisputeStatus.Open, "Dispute created");
    }

    public void Acknowledge(Guid acknowledgedBy, string acknowledgerType)
    {
        if (Status != DisputeStatus.Open)
            throw new InvalidOperationException("Only open disputes can be acknowledged");

        Status = DisputeStatus.InProgress;
        AcknowledgedAt = DateTime.UtcNow;
        RespondentId = acknowledgedBy;
        RespondentType = acknowledgerType;

        AddStatusHistory(DisputeStatus.InProgress, $"Dispute acknowledged by {acknowledgerType}");
    }

    public void Resolve(string resolutionSummary, Guid resolvedBy, string resolverType, decimal? settlementAmount = null, string? settlementCurrency = null)
    {
        if (Status == DisputeStatus.Resolved || Status == DisputeStatus.Closed)
            throw new InvalidOperationException("Dispute is already resolved or closed");

        Status = DisputeStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolutionSummary = resolutionSummary ?? throw new ArgumentNullException(nameof(resolutionSummary));
        ResolvedBy = resolvedBy;
        ResolverType = resolverType;
        SettlementAmount = settlementAmount;
        SettlementCurrency = settlementCurrency;

        AddStatusHistory(DisputeStatus.Resolved, $"Dispute resolved by {resolverType}: {resolutionSummary}");
    }

    public void Close(Guid closedBy, string closerType, string? closureReason = null)
    {
        if (Status != DisputeStatus.Resolved)
            throw new InvalidOperationException("Only resolved disputes can be closed");

        Status = DisputeStatus.Closed;
        ClosedAt = DateTime.UtcNow;

        AddStatusHistory(DisputeStatus.Closed, closureReason ?? "Dispute closed");
    }

    public void Escalate(int newLevel, string escalationReason)
    {
        IsEscalated = true;
        EscalationLevel = newLevel;
        EscalatedAt = DateTime.UtcNow;
        Priority = Priority == DisputePriority.Low ? DisputePriority.Medium : DisputePriority.High;

        AddStatusHistory(Status, $"Dispute escalated to level {newLevel}: {escalationReason}");
    }

    public void UpdatePriority(DisputePriority newPriority, string reason)
    {
        var oldPriority = Priority;
        Priority = newPriority;

        AddStatusHistory(Status, $"Priority changed from {oldPriority} to {newPriority}: {reason}");
    }

    public void AddComment(string content, Guid authorId, string authorType, bool isInternal = false)
    {
        var comment = new DisputeComment(Id, content, authorId, authorType, isInternal);
        _comments.Add(comment);
    }

    public void AddDocument(string fileName, string filePath, string documentType, Guid uploadedBy, string uploaderType)
    {
        var document = new DisputeDocument(Id, fileName, filePath, documentType, uploadedBy, uploaderType);
        _documents.Add(document);
    }

    public void AddTag(string tag)
    {
        if (!Tags.Contains(tag))
        {
            Tags.Add(tag);
        }
    }

    public void RemoveTag(string tag)
    {
        Tags.Remove(tag);
    }

    public void UpdateMetadata(Dictionary<string, object> metadata)
    {
        Metadata = metadata;
    }

    public bool IsOverdue(int businessDaysThreshold = 5)
    {
        if (Status == DisputeStatus.Resolved || Status == DisputeStatus.Closed)
            return false;

        var businessDaysElapsed = CalculateBusinessDays(RaisedAt, DateTime.UtcNow);
        return businessDaysElapsed > businessDaysThreshold;
    }

    public TimeSpan GetResolutionTime()
    {
        if (ResolvedAt.HasValue)
            return ResolvedAt.Value - RaisedAt;

        return DateTime.UtcNow - RaisedAt;
    }

    private void AddStatusHistory(DisputeStatus status, string notes)
    {
        _statusHistory.Add(new DisputeStatusHistory(Id, status, notes, DateTime.UtcNow));
    }

    private static string GenerateDisputeNumber()
    {
        return $"DSP-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }

    private static int CalculateBusinessDays(DateTime startDate, DateTime endDate)
    {
        var businessDays = 0;
        var currentDate = startDate.Date;

        while (currentDate <= endDate.Date)
        {
            if (currentDate.DayOfWeek != DayOfWeek.Saturday && currentDate.DayOfWeek != DayOfWeek.Sunday)
                businessDays++;

            currentDate = currentDate.AddDays(1);
        }

        return businessDays;
    }
}

/// <summary>
/// Entity representing a comment on a dispute
/// </summary>
public class DisputeComment : BaseEntity
{
    public Guid DisputeId { get; private set; }
    public string Content { get; private set; } = string.Empty;
    public Guid AuthorId { get; private set; }
    public string AuthorType { get; private set; } = string.Empty;
    public bool IsInternal { get; private set; }
    public DateTime CreatedAt { get; private set; }

    // Navigation property
    public OrderDispute Dispute { get; private set; } = null!;

    private DisputeComment() { } // EF Constructor

    public DisputeComment(Guid disputeId, string content, Guid authorId, string authorType, bool isInternal = false)
    {
        DisputeId = disputeId;
        Content = content ?? throw new ArgumentNullException(nameof(content));
        AuthorId = authorId;
        AuthorType = authorType ?? throw new ArgumentNullException(nameof(authorType));
        IsInternal = isInternal;
        CreatedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Entity representing a document attached to a dispute
/// </summary>
public class DisputeDocument : BaseEntity
{
    public Guid DisputeId { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string FilePath { get; private set; } = string.Empty;
    public string DocumentType { get; private set; } = string.Empty;
    public long FileSize { get; private set; }
    public string? ContentType { get; private set; }
    public Guid UploadedBy { get; private set; }
    public string UploaderType { get; private set; } = string.Empty;
    public DateTime UploadedAt { get; private set; }

    // Navigation property
    public OrderDispute Dispute { get; private set; } = null!;

    private DisputeDocument() { } // EF Constructor

    public DisputeDocument(Guid disputeId, string fileName, string filePath, string documentType, Guid uploadedBy, string uploaderType)
    {
        DisputeId = disputeId;
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        DocumentType = documentType ?? throw new ArgumentNullException(nameof(documentType));
        UploadedBy = uploadedBy;
        UploaderType = uploaderType ?? throw new ArgumentNullException(nameof(uploaderType));
        UploadedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Entity representing the status history of a dispute
/// </summary>
public class DisputeStatusHistory : BaseEntity
{
    public Guid DisputeId { get; private set; }
    public DisputeStatus Status { get; private set; }
    public string Notes { get; private set; } = string.Empty;
    public DateTime ChangedAt { get; private set; }

    // Navigation property
    public OrderDispute Dispute { get; private set; } = null!;

    private DisputeStatusHistory() { } // EF Constructor

    public DisputeStatusHistory(Guid disputeId, DisputeStatus status, string notes, DateTime changedAt)
    {
        DisputeId = disputeId;
        Status = status;
        Notes = notes ?? string.Empty;
        ChangedAt = changedAt;
    }
}
