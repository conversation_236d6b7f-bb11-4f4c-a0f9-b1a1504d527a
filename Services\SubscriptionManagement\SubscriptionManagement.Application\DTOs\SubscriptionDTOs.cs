using SubscriptionManagement.Domain.Enums;
using Microsoft.AspNetCore.Http;

namespace SubscriptionManagement.Application.DTOs
{
    public class SubscriptionDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid PlanId { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public SubscriptionStatus Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime NextBillingDate { get; set; }
        public DateTime? TrialEndDate { get; set; }
        public DateTime? CancelledAt { get; set; }
        public string? CancellationReason { get; set; }
        public bool AutoRenew { get; set; }
        public decimal CurrentPrice { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public bool IsInTrial { get; set; }
        public bool IsExpired { get; set; }
        public int DaysUntilRenewal { get; set; }
        public decimal TotalPaid { get; set; }

        // Grace Period Extension Properties
        public DateTime? GracePeriodEndDate { get; set; }
        public DateTime? LastExtendedAt { get; set; }
        public string? ExtensionReason { get; set; }
        public Guid? ExtendedByUserId { get; set; }
        public bool IsInGracePeriod { get; set; }
        public int DaysRemainingInGracePeriod { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateSubscriptionDto
    {
        public Guid UserId { get; set; }
        public Guid PlanId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? TrialEndDate { get; set; }
        public bool AutoRenew { get; set; } = true;
        public ProrationMode ProrationMode { get; set; } = ProrationMode.CreateProrations;
    }

    public class UpdateSubscriptionDto
    {
        public bool? AutoRenew { get; set; }
        public ProrationMode? ProrationMode { get; set; }
    }

    public class CancelSubscriptionDto
    {
        public string? CancellationReason { get; set; }
        public bool ImmediateCancellation { get; set; } = false;
        public bool RefundProration { get; set; } = false;
    }

    public class ChangePlanDto
    {
        public Guid NewPlanId { get; set; }
        public DateTime? EffectiveDate { get; set; }
    }

    public class SubscriptionSummaryDto
    {
        public Guid Id { get; set; }
        public string PlanName { get; set; } = string.Empty;
        public PlanType PlanType { get; set; }
        public SubscriptionStatus Status { get; set; }
        public decimal MonthlyPrice { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime NextBillingDate { get; set; }
        public bool IsInTrial { get; set; }
        public int DaysUntilRenewal { get; set; }
    }

    public class ExtendSubscriptionDto
    {
        public int ExtensionDays { get; set; }
        public string? Reason { get; set; }
        public bool ApplyAsGracePeriod { get; set; } = false;
    }

    // Payment Proof DTOs
    public class SubscriptionPaymentProofDto
    {
        public Guid Id { get; set; }
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public string ProofImageUrl { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public PaymentProofStatus Status { get; set; }
        public string? PaymentMethod { get; set; }
        public string? TransactionReference { get; set; }
        public Guid? VerifiedByUserId { get; set; }
        public DateTime? VerifiedAt { get; set; }
        public string? VerificationNotes { get; set; }
        public string? RejectionReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class UploadPaymentProofRequest
    {
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "INR";
        public DateTime PaymentDate { get; set; }
        public IFormFile ProofImage { get; set; } = null!;
        public string? Notes { get; set; }
        public string? PaymentMethod { get; set; }
        public string? TransactionReference { get; set; }
    }

    public class UploadPaymentProofResponse
    {
        public Guid PaymentProofId { get; set; }
        public string ProofImageUrl { get; set; } = string.Empty;
        public PaymentProofStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class VerifyPaymentProofRequest
    {
        public string? VerificationNotes { get; set; }
    }

    public class RejectPaymentProofRequest
    {
        public string RejectionReason { get; set; } = string.Empty;
        public string? VerificationNotes { get; set; }
    }

    public class RequestAdditionalInfoRequest
    {
        public string RequestReason { get; set; } = string.Empty;
    }

    public class PaymentProofListDto
    {
        public List<SubscriptionPaymentProofDto> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class PaymentProofFilterDto
    {
        public PaymentProofStatus? Status { get; set; }
        public Guid? UserId { get; set; }
        public Guid? SubscriptionId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
