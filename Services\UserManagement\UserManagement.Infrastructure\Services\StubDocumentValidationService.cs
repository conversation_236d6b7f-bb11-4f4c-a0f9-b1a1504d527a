using Microsoft.AspNetCore.Http;
using UserManagement.Application.Interfaces;

namespace UserManagement.Infrastructure.Services
{
    public class StubDocumentValidationService : IDocumentValidationService
    {
        public Task<ValidationResult> ValidateDocumentAsync(IFormFile document)
        {
            // Stub implementation - return valid result
            return Task.FromResult(new ValidationResult
            {
                IsValid = true,
                Errors = new List<string>()
            });
        }

        public Task<ValidationResult> ValidateDocumentTypeAsync(IFormFile document, string expectedType)
        {
            // Stub implementation - return valid result
            return Task.FromResult(new ValidationResult
            {
                IsValid = true,
                Errors = new List<string>()
            });
        }

        public Task<DocumentValidationResult> ValidateDocumentDataAsync(string documentType, Dictionary<string, string> extractedFields, ValidationConfiguration validationConfig, CancellationToken cancellationToken = default)
        {
            // Stub implementation - return valid result
            return Task.FromResult(new DocumentValidationResult
            {
                IsValid = true,
                Errors = new List<string>(),
                Warnings = new List<string>(),
                ValidatedFields = new Dictionary<string, object>(),
                ConfidenceScore = 0.95m
            });
        }
    }
}
