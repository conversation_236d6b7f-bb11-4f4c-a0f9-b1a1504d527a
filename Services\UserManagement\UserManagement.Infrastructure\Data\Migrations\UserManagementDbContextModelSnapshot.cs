﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UserManagement.Infrastructure.Data;

#nullable disable

namespace UserManagement.Infrastructure.Data.Migrations
{
    [DbContext(typeof(UserManagementDbContext))]
    partial class UserManagementDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UserManagement.Domain.Entities.DocumentSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OverallStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReviewNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ReviewStartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ReviewedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OverallStatus");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserType");

                    b.ToTable("DocumentSubmissions");
                });

            modelBuilder.Entity("UserManagement.Domain.Entities.UserProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AadharNumber")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ApprovedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("GstNumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LicenseNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PanNumber")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("RejectionReason")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("Status");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserType");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("UserManagement.Domain.Entities.DocumentSubmission", b =>
                {
                    b.OwnsMany("UserManagement.Domain.Entities.Document", "Documents", b1 =>
                        {
                            b1.Property<Guid>("DocumentSubmissionId")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid");

                            b1.Property<decimal>("ConfidenceScore")
                                .HasColumnType("numeric");

                            b1.Property<DateTime>("CreatedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("DocumentType")
                                .HasColumnType("integer");

                            b1.Property<string>("ExtractedData")
                                .HasMaxLength(2000)
                                .HasColumnType("character varying(2000)");

                            b1.Property<string>("FileName")
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)");

                            b1.Property<string>("FilePath")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("FileSize")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.Property<string>("MimeType")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("RejectionReason")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)");

                            b1.Property<string>("ReviewNotes")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)");

                            b1.Property<DateTime?>("ReviewedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("Status")
                                .HasColumnType("integer");

                            b1.Property<DateTime?>("UpdatedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<DateTime?>("UploadedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("VerificationMethod")
                                .HasColumnType("integer");

                            b1.HasKey("DocumentSubmissionId", "Id");

                            b1.HasIndex("DocumentType");

                            b1.HasIndex("Status");

                            b1.ToTable("Documents");

                            b1.WithOwner()
                                .HasForeignKey("DocumentSubmissionId");
                        });

                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
