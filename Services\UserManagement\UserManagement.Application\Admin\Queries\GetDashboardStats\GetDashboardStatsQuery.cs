using MediatR;

namespace UserManagement.Application.Admin.Queries.GetDashboardStats
{
    public class GetDashboardStatsQuery : IRequest<DashboardStatsResponse>
    {
    }

    public class DashboardStatsResponse
    {
        public int TotalUsers { get; set; }
        public int PendingApprovals { get; set; }
        public int PendingDocuments { get; set; }
        public int ApprovedToday { get; set; }
        public int RejectedToday { get; set; }
        public UserTypeBreakdown UserTypeBreakdown { get; set; } = new();
        public StatusBreakdown StatusBreakdown { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    public class UserTypeBreakdown
    {
        public int TransportCompanies { get; set; }
        public int Brokers { get; set; }
        public int Carriers { get; set; }
        public int Drivers { get; set; }
        public int Shippers { get; set; }
    }

    public class StatusBreakdown
    {
        public int Incomplete { get; set; }
        public int Complete { get; set; }
        public int UnderReview { get; set; }
        public int Approved { get; set; }
        public int Rejected { get; set; }
    }
}
