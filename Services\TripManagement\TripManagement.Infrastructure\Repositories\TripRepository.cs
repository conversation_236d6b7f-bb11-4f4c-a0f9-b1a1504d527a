using Microsoft.EntityFrameworkCore;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.Repositories;

public class TripRepository : ITripRepository
{
    private readonly TripManagementDbContext _context;

    public TripRepository(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<Trip?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
                .ThenInclude(s => s.ProofOfDeliveries)
            .Include(t => t.LocationUpdates)
            .Include(t => t.Exceptions)
            .Include(t => t.Documents)
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Trip?> GetByOrderIdAsync(Guid orderId, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
                .ThenInclude(s => s.ProofOfDeliveries)
            .Include(t => t.LocationUpdates)
            .Include(t => t.Exceptions)
            .Include(t => t.Documents)
            .FirstOrDefaultAsync(t => t.OrderId == orderId, cancellationToken);
    }

    public async Task<Trip?> GetByTripNumberAsync(string tripNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
                .ThenInclude(s => s.ProofOfDeliveries)
            .Include(t => t.LocationUpdates)
            .Include(t => t.Exceptions)
            .Include(t => t.Documents)
            .FirstOrDefaultAsync(t => t.TripNumber == tripNumber, cancellationToken);
    }

    public async Task<List<Trip>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Where(t => t.CarrierId == carrierId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Trip>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Where(t => t.DriverId == driverId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Trip>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Where(t => t.VehicleId == vehicleId)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Trip>> GetActiveTripsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Where(t => t.Status == TripStatus.Assigned ||
                       t.Status == TripStatus.InProgress ||
                       t.Status == TripStatus.Exception)
            .OrderBy(t => t.EstimatedStartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Trip>> GetTripsByStatusAsync(TripStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Where(t => t.Status == status)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<TripStop?> GetTripStopByIdAsync(Guid tripStopId, CancellationToken cancellationToken = default)
    {
        return await _context.TripStops
            .Include(ts => ts.ProofOfDeliveries)
            .Include(ts => ts.Trip)
            .FirstOrDefaultAsync(ts => ts.Id == tripStopId, cancellationToken);
    }

    public async Task AddAsync(Trip trip, CancellationToken cancellationToken = default)
    {
        await _context.Trips.AddAsync(trip, cancellationToken);
    }

    public void Update(Trip trip)
    {
        _context.Trips.Update(trip);
    }

    public void Delete(Trip trip)
    {
        _context.Trips.Remove(trip);
    }
}
