using System.ComponentModel.DataAnnotations;
using Identity.Domain.Entities;

namespace Identity.API.Models.Requests
{
    public class RegisterRequest
    {
        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string Username { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; }

        public string PhoneNumber { get; set; }
        public string CountryCode { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }

        [Required]
        public UserType UserType { get; set; } = UserType.Shipper;
    }

    public class LoginRequest
    {
        [Required]
        public string Username { get; set; }

        [Required]
        public string Password { get; set; }

        public string DeviceInfo { get; set; }
    }

    public class RefreshTokenRequest
    {
        [Required]
        public string RefreshToken { get; set; }
    }

    public class LogoutRequest
    {
        [Required]
        public string RefreshToken { get; set; }
    }

    public class ValidateTokenRequest
    {
        [Required]
        public string Token { get; set; }
    }

    public class RequestOtpLoginRequest
    {
        [Required]
        public string Username { get; set; }

        [Required]
        public string DeliveryMethod { get; set; } // "SMS" or "Email"
    }

    public class VerifyOtpLoginRequest
    {
        [Required]
        public string Username { get; set; }

        [Required]
        [StringLength(10, MinimumLength = 4)]
        public string OtpToken { get; set; }

        public string DeviceInfo { get; set; }
    }
}
