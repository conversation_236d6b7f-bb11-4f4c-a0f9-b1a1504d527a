using MediatR;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;

namespace AnalyticsBIService.Application.Commands.GenerateComprehensiveReport;

public class GenerateComprehensiveReportCommandHandler : IRequestHandler<GenerateComprehensiveReportCommand, ComprehensiveReportDto>
{
    private readonly IDataAggregationService _dataAggregationService;
    private readonly IVisualizationService _visualizationService;
    private readonly IInsightGenerationService _insightGenerationService;
    private readonly IReportTemplateService _templateService;
    private readonly ICrossServiceDataCollector _crossServiceCollector;
    private readonly ILogger<GenerateComprehensiveReportCommandHandler> _logger;

    public GenerateComprehensiveReportCommandHandler(
        IDataAggregationService dataAggregationService,
        IVisualizationService visualizationService,
        IInsightGenerationService insightGenerationService,
        IReportTemplateService templateService,
        ICrossServiceDataCollector crossServiceCollector,
        ILogger<GenerateComprehensiveReportCommandHandler> logger)
    {
        _dataAggregationService = dataAggregationService;
        _visualizationService = visualizationService;
        _insightGenerationService = insightGenerationService;
        _templateService = templateService;
        _crossServiceCollector = crossServiceCollector;
        _logger = logger;
    }

    public async Task<ComprehensiveReportDto> Handle(GenerateComprehensiveReportCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating comprehensive report of type {ReportType} for user {UserId}", 
            request.ReportType, request.RequestedBy);

        var startTime = DateTime.UtcNow;

        try
        {
            // Initialize report
            var report = new ComprehensiveReportDto
            {
                ReportId = Guid.NewGuid(),
                ReportName = GenerateReportName(request.ReportType, request.DateRange),
                ReportType = request.ReportType,
                GeneratedAt = DateTime.UtcNow,
                GeneratedBy = request.RequestedBy,
                GeneratedByName = await GetUserNameAsync(request.RequestedBy, cancellationToken),
                DateRange = request.DateRange,
                DataSources = request.DataSources
            };

            // Apply template if specified
            if (!string.IsNullOrEmpty(request.TemplateId))
            {
                await ApplyReportTemplate(report, request.TemplateId, cancellationToken);
            }

            // Collect data from multiple sources
            var aggregatedData = await CollectAndAggregateData(request, cancellationToken);

            // Generate report sections
            report.Sections = await GenerateReportSections(aggregatedData, request, cancellationToken);

            // Generate visualizations
            if (request.IncludeVisualizations)
            {
                report.Visualizations = await GenerateVisualizations(aggregatedData, request, cancellationToken);
            }

            // Generate insights
            if (request.IncludeInsights)
            {
                report.Insights = await GenerateInsights(aggregatedData, request, cancellationToken);
            }

            // Generate summary
            report.Summary = GenerateReportSummary(report, startTime);

            // Set metadata
            report.Metadata = GenerateReportMetadata(request, report);

            // Set export options
            report.ExportOptions = GenerateExportOptions(request);

            // Save as template if requested
            if (request.SaveAsTemplate && !string.IsNullOrEmpty(request.TemplateName))
            {
                await SaveAsTemplate(report, request.TemplateName, request.RequestedBy, cancellationToken);
            }

            _logger.LogInformation("Successfully generated comprehensive report {ReportId} in {Duration}ms", 
                report.ReportId, (DateTime.UtcNow - startTime).TotalMilliseconds);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating comprehensive report of type {ReportType}", request.ReportType);
            throw;
        }
    }

    private async Task<Dictionary<string, object>> CollectAndAggregateData(
        GenerateComprehensiveReportCommand request, 
        CancellationToken cancellationToken)
    {
        var aggregatedData = new Dictionary<string, object>();

        foreach (var dataSource in request.DataSources)
        {
            try
            {
                var sourceData = await _crossServiceCollector.CollectDataAsync(
                    dataSource, 
                    request.DateRange, 
                    request.Metrics, 
                    request.Parameters, 
                    cancellationToken);

                aggregatedData[dataSource] = sourceData;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to collect data from source {DataSource}", dataSource);
                aggregatedData[dataSource] = new { Error = ex.Message };
            }
        }

        return aggregatedData;
    }

    private async Task<List<ReportSectionDto>> GenerateReportSections(
        Dictionary<string, object> aggregatedData,
        GenerateComprehensiveReportCommand request,
        CancellationToken cancellationToken)
    {
        var sections = new List<ReportSectionDto>();

        // Executive Summary Section
        sections.Add(await GenerateExecutiveSummarySection(aggregatedData, cancellationToken));

        // Data Source Sections
        foreach (var dataSource in request.DataSources)
        {
            if (aggregatedData.ContainsKey(dataSource))
            {
                var section = await GenerateDataSourceSection(dataSource, aggregatedData[dataSource], cancellationToken);
                sections.Add(section);
            }
        }

        // Performance Metrics Section
        if (request.Metrics.Any())
        {
            sections.Add(await GeneratePerformanceMetricsSection(aggregatedData, request.Metrics, cancellationToken));
        }

        // Trend Analysis Section
        sections.Add(await GenerateTrendAnalysisSection(aggregatedData, request.DateRange, cancellationToken));

        // Comparative Analysis Section
        if (request.IncludeBenchmarking)
        {
            sections.Add(await GenerateComparativeAnalysisSection(aggregatedData, cancellationToken));
        }

        return sections.OrderBy(s => s.Order).ToList();
    }

    private async Task<List<ReportVisualizationDto>> GenerateVisualizations(
        Dictionary<string, object> aggregatedData,
        GenerateComprehensiveReportCommand request,
        CancellationToken cancellationToken)
    {
        var visualizations = new List<ReportVisualizationDto>();

        // Generate standard visualizations based on data
        foreach (var dataSource in request.DataSources)
        {
            if (aggregatedData.ContainsKey(dataSource))
            {
                var sourceVisualizations = await _visualizationService.GenerateVisualizationsAsync(
                    dataSource, 
                    aggregatedData[dataSource], 
                    request.Metrics, 
                    cancellationToken);

                visualizations.AddRange(sourceVisualizations);
            }
        }

        // Generate cross-service correlation visualizations
        if (request.DataSources.Count > 1)
        {
            var correlationVisualizations = await _visualizationService.GenerateCorrelationVisualizationsAsync(
                aggregatedData, 
                cancellationToken);

            visualizations.AddRange(correlationVisualizations);
        }

        return visualizations;
    }

    private async Task<List<ReportInsightDto>> GenerateInsights(
        Dictionary<string, object> aggregatedData,
        GenerateComprehensiveReportCommand request,
        CancellationToken cancellationToken)
    {
        var insights = new List<ReportInsightDto>();

        // Generate data-driven insights
        insights.AddRange(await _insightGenerationService.GenerateDataInsightsAsync(aggregatedData, cancellationToken));

        // Generate trend insights
        insights.AddRange(await _insightGenerationService.GenerateTrendInsightsAsync(aggregatedData, request.DateRange, cancellationToken));

        // Generate performance insights
        if (request.Metrics.Any())
        {
            insights.AddRange(await _insightGenerationService.GeneratePerformanceInsightsAsync(aggregatedData, request.Metrics, cancellationToken));
        }

        // Generate predictive insights
        if (request.IncludePredictiveAnalytics)
        {
            insights.AddRange(await _insightGenerationService.GeneratePredictiveInsightsAsync(aggregatedData, cancellationToken));
        }

        // Generate benchmarking insights
        if (request.IncludeBenchmarking)
        {
            insights.AddRange(await _insightGenerationService.GenerateBenchmarkingInsightsAsync(aggregatedData, cancellationToken));
        }

        return insights.OrderByDescending(i => i.Importance).ThenByDescending(i => i.Confidence).ToList();
    }

    private ReportSummaryDto GenerateReportSummary(ComprehensiveReportDto report, DateTime startTime)
    {
        return new ReportSummaryDto
        {
            TotalDataPoints = CalculateTotalDataPoints(report.Sections),
            TotalSections = report.Sections.Count,
            TotalVisualizations = report.Visualizations.Count,
            TotalInsights = report.Insights.Count,
            GenerationTime = DateTime.UtcNow - startTime,
            DataQuality = AssessDataQuality(report.Sections),
            CompletionPercentage = CalculateCompletionPercentage(report.Sections),
            KeyFindings = ExtractKeyFindings(report.Insights),
            Recommendations = ExtractRecommendations(report.Insights)
        };
    }

    private ReportMetadataDto GenerateReportMetadata(GenerateComprehensiveReportCommand request, ComprehensiveReportDto report)
    {
        return new ReportMetadataDto
        {
            Version = "1.0",
            Tags = GenerateReportTags(request.ReportType, request.DataSources),
            Parameters = request.Parameters.ToDictionary(p => p.Key, p => p.Value.ToString() ?? string.Empty),
            TemplateId = request.TemplateId ?? string.Empty,
            IsScheduled = false,
            Recipients = request.Recipients,
            AccessLevel = "Private"
        };
    }

    private ExportOptionsDto GenerateExportOptions(GenerateComprehensiveReportCommand request)
    {
        return new ExportOptionsDto
        {
            AvailableFormats = new List<string> { "PDF", "Excel", "CSV", "JSON", "HTML" },
            IncludeVisualizations = request.IncludeVisualizations,
            IncludeRawData = false,
            IncludeInsights = request.IncludeInsights,
            DefaultFormat = request.OutputFormat
        };
    }

    // Helper methods
    private string GenerateReportName(string reportType, DateRangeDto dateRange)
    {
        return $"{reportType} Report - {dateRange.StartDate:yyyy-MM-dd} to {dateRange.EndDate:yyyy-MM-dd}";
    }

    private async Task<string> GetUserNameAsync(Guid userId, CancellationToken cancellationToken)
    {
        // This would typically call an external user service
        return "User Name"; // Placeholder
    }

    private async Task ApplyReportTemplate(ComprehensiveReportDto report, string templateId, CancellationToken cancellationToken)
    {
        var template = await _templateService.GetTemplateAsync(templateId, cancellationToken);
        if (template != null)
        {
            report.ReportName = template.Name;
            // Apply other template settings
        }
    }

    private async Task SaveAsTemplate(ComprehensiveReportDto report, string templateName, Guid createdBy, CancellationToken cancellationToken)
    {
        await _templateService.SaveReportAsTemplateAsync(report, templateName, createdBy, cancellationToken);
    }

    // Additional helper methods for section generation
    private async Task<ReportSectionDto> GenerateExecutiveSummarySection(Dictionary<string, object> aggregatedData, CancellationToken cancellationToken)
    {
        return new ReportSectionDto
        {
            SectionId = "executive-summary",
            Title = "Executive Summary",
            Description = "High-level overview of key metrics and findings",
            Order = 1,
            SectionType = "Summary",
            Data = new Dictionary<string, object> { { "summary", "Executive summary content" } },
            Metrics = new ReportSectionMetricsDto
            {
                RecordCount = 1,
                LastUpdated = DateTime.UtcNow,
                DataSource = "Aggregated",
                DataAccuracy = 100
            }
        };
    }

    private async Task<ReportSectionDto> GenerateDataSourceSection(string dataSource, object data, CancellationToken cancellationToken)
    {
        return new ReportSectionDto
        {
            SectionId = $"data-source-{dataSource.ToLower()}",
            Title = $"{dataSource} Analysis",
            Description = $"Detailed analysis of {dataSource} data",
            Order = 2,
            SectionType = "Data",
            Data = new Dictionary<string, object> { { dataSource, data } },
            Metrics = new ReportSectionMetricsDto
            {
                RecordCount = GetDataRecordCount(data),
                LastUpdated = DateTime.UtcNow,
                DataSource = dataSource,
                DataAccuracy = 95
            }
        };
    }

    private async Task<ReportSectionDto> GeneratePerformanceMetricsSection(Dictionary<string, object> aggregatedData, List<string> metrics, CancellationToken cancellationToken)
    {
        return new ReportSectionDto
        {
            SectionId = "performance-metrics",
            Title = "Performance Metrics",
            Description = "Key performance indicators and metrics analysis",
            Order = 3,
            SectionType = "Metrics",
            Data = new Dictionary<string, object> { { "metrics", metrics } },
            Metrics = new ReportSectionMetricsDto
            {
                RecordCount = metrics.Count,
                LastUpdated = DateTime.UtcNow,
                DataSource = "Multiple",
                DataAccuracy = 98
            }
        };
    }

    private async Task<ReportSectionDto> GenerateTrendAnalysisSection(Dictionary<string, object> aggregatedData, DateRangeDto dateRange, CancellationToken cancellationToken)
    {
        return new ReportSectionDto
        {
            SectionId = "trend-analysis",
            Title = "Trend Analysis",
            Description = "Analysis of trends and patterns over the specified period",
            Order = 4,
            SectionType = "Trends",
            Data = new Dictionary<string, object> { { "dateRange", dateRange } },
            Metrics = new ReportSectionMetricsDto
            {
                RecordCount = dateRange.TotalDays,
                LastUpdated = DateTime.UtcNow,
                DataSource = "Time Series",
                DataAccuracy = 92
            }
        };
    }

    private async Task<ReportSectionDto> GenerateComparativeAnalysisSection(Dictionary<string, object> aggregatedData, CancellationToken cancellationToken)
    {
        return new ReportSectionDto
        {
            SectionId = "comparative-analysis",
            Title = "Comparative Analysis",
            Description = "Benchmarking and comparative analysis against industry standards",
            Order = 5,
            SectionType = "Comparison",
            Data = new Dictionary<string, object> { { "benchmarks", "Industry benchmarks" } },
            Metrics = new ReportSectionMetricsDto
            {
                RecordCount = 10,
                LastUpdated = DateTime.UtcNow,
                DataSource = "Benchmarks",
                DataAccuracy = 90
            }
        };
    }

    // Utility methods
    private int CalculateTotalDataPoints(List<ReportSectionDto> sections)
    {
        return sections.Sum(s => s.Metrics.RecordCount);
    }

    private string AssessDataQuality(List<ReportSectionDto> sections)
    {
        var averageAccuracy = sections.Average(s => s.Metrics.DataAccuracy);
        return averageAccuracy >= 95 ? "High" : averageAccuracy >= 85 ? "Medium" : "Low";
    }

    private decimal CalculateCompletionPercentage(List<ReportSectionDto> sections)
    {
        var completedSections = sections.Count(s => s.Metrics.RecordCount > 0);
        return sections.Count > 0 ? (decimal)completedSections / sections.Count * 100 : 0;
    }

    private List<string> ExtractKeyFindings(List<ReportInsightDto> insights)
    {
        return insights.Where(i => i.Importance == "High")
                      .Take(5)
                      .Select(i => i.Title)
                      .ToList();
    }

    private List<string> ExtractRecommendations(List<ReportInsightDto> insights)
    {
        return insights.SelectMany(i => i.RecommendedActions)
                      .Distinct()
                      .Take(10)
                      .ToList();
    }

    private List<string> GenerateReportTags(string reportType, List<string> dataSources)
    {
        var tags = new List<string> { reportType };
        tags.AddRange(dataSources);
        tags.Add("comprehensive");
        return tags;
    }

    private int GetDataRecordCount(object data)
    {
        // This would analyze the data structure to count records
        return 100; // Placeholder
    }
}
