using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class CdnDeploymentResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? CdnUrl { get; private set; }
    public string? EndpointId { get; private set; }
    public CdnProvider Provider { get; private set; }
    public TimeSpan DeploymentTime { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    private CdnDeploymentResult()
    {
        Metadata = new Dictionary<string, string>();
    }

    public static CdnDeploymentResult Success(
        string cdnUrl,
        string endpointId,
        CdnProvider provider,
        TimeSpan deploymentTime,
        Dictionary<string, string>? metadata = null)
    {
        return new CdnDeploymentResult
        {
            IsSuccessful = true,
            CdnUrl = cdnUrl,
            EndpointId = endpointId,
            Provider = provider,
            DeploymentTime = deploymentTime,
            Metadata = metadata ?? new Dictionary<string, string>()
        };
    }

    public static CdnDeploymentResult Failure(string errorMessage)
    {
        return new CdnDeploymentResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            Provider = CdnProvider.None,
            DeploymentTime = TimeSpan.Zero,
            Metadata = new Dictionary<string, string>()
        };
    }
}

public class CdnDeploymentOptions
{
    public CdnProvider Provider { get; private set; }
    public string? CustomDomain { get; private set; }
    public CachePolicy CachePolicy { get; private set; }
    public CompressionOptions CompressionOptions { get; private set; }
    public SecurityOptions SecurityOptions { get; private set; }
    public List<string> AllowedOrigins { get; private set; }
    public Dictionary<string, string> CustomHeaders { get; private set; }

    public CdnDeploymentOptions(
        CdnProvider provider,
        string? customDomain = null,
        CachePolicy? cachePolicy = null,
        CompressionOptions? compressionOptions = null,
        SecurityOptions? securityOptions = null,
        List<string>? allowedOrigins = null,
        Dictionary<string, string>? customHeaders = null)
    {
        Provider = provider;
        CustomDomain = customDomain;
        CachePolicy = cachePolicy ?? CachePolicy.Default;
        CompressionOptions = compressionOptions ?? CompressionOptions.Default;
        SecurityOptions = securityOptions ?? SecurityOptions.Default;
        AllowedOrigins = allowedOrigins ?? new List<string>();
        CustomHeaders = customHeaders ?? new Dictionary<string, string>();
    }
}

public class CdnEndpoint
{
    public string Id { get; private set; }
    public string Name { get; private set; }
    public string Url { get; private set; }
    public CdnProvider Provider { get; private set; }
    public CdnEndpointStatus Status { get; private set; }
    public string? CustomDomain { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public CdnEndpointConfiguration Configuration { get; private set; }

    public CdnEndpoint(
        string id,
        string name,
        string url,
        CdnProvider provider,
        CdnEndpointStatus status,
        CdnEndpointConfiguration configuration,
        string? customDomain = null,
        DateTime createdAt = default,
        DateTime lastModified = default)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Url = url ?? throw new ArgumentNullException(nameof(url));
        Provider = provider;
        Status = status;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CustomDomain = customDomain;
        CreatedAt = createdAt == default ? DateTime.UtcNow : createdAt;
        LastModified = lastModified == default ? DateTime.UtcNow : lastModified;
    }
}

public class CdnEndpointConfiguration
{
    public string OriginUrl { get; private set; }
    public CachePolicy CachePolicy { get; private set; }
    public CompressionOptions CompressionOptions { get; private set; }
    public SecurityOptions SecurityOptions { get; private set; }
    public List<string> AllowedOrigins { get; private set; }
    public Dictionary<string, string> CustomHeaders { get; private set; }
    public bool EnableLogging { get; private set; }

    public CdnEndpointConfiguration(
        string originUrl,
        CachePolicy? cachePolicy = null,
        CompressionOptions? compressionOptions = null,
        SecurityOptions? securityOptions = null,
        List<string>? allowedOrigins = null,
        Dictionary<string, string>? customHeaders = null,
        bool enableLogging = true)
    {
        OriginUrl = originUrl ?? throw new ArgumentNullException(nameof(originUrl));
        CachePolicy = cachePolicy ?? CachePolicy.Default;
        CompressionOptions = compressionOptions ?? CompressionOptions.Default;
        SecurityOptions = securityOptions ?? SecurityOptions.Default;
        AllowedOrigins = allowedOrigins ?? new List<string>();
        CustomHeaders = customHeaders ?? new Dictionary<string, string>();
        EnableLogging = enableLogging;
    }
}

public class CachePolicy
{
    public TimeSpan DefaultTtl { get; private set; }
    public TimeSpan MaxTtl { get; private set; }
    public List<CacheRule> Rules { get; private set; }
    public bool EnableQueryStringCaching { get; private set; }
    public List<string> IgnoredQueryParameters { get; private set; }

    public CachePolicy(
        TimeSpan defaultTtl,
        TimeSpan maxTtl,
        List<CacheRule>? rules = null,
        bool enableQueryStringCaching = false,
        List<string>? ignoredQueryParameters = null)
    {
        DefaultTtl = defaultTtl;
        MaxTtl = maxTtl;
        Rules = rules ?? new List<CacheRule>();
        EnableQueryStringCaching = enableQueryStringCaching;
        IgnoredQueryParameters = ignoredQueryParameters ?? new List<string>();
    }

    public static CachePolicy Default => new(
        TimeSpan.FromHours(24),
        TimeSpan.FromDays(365),
        new List<CacheRule>
        {
            new("*.css", TimeSpan.FromDays(30)),
            new("*.js", TimeSpan.FromDays(30)),
            new("*.png", TimeSpan.FromDays(7)),
            new("*.jpg", TimeSpan.FromDays(7)),
            new("*.jpeg", TimeSpan.FromDays(7)),
            new("*.gif", TimeSpan.FromDays(7)),
            new("*.pdf", TimeSpan.FromDays(1))
        });
}

public class CacheRule
{
    public string PathPattern { get; private set; }
    public TimeSpan Ttl { get; private set; }
    public CacheBehavior Behavior { get; private set; }

    public CacheRule(string pathPattern, TimeSpan ttl, CacheBehavior behavior = CacheBehavior.Cache)
    {
        PathPattern = pathPattern ?? throw new ArgumentNullException(nameof(pathPattern));
        Ttl = ttl;
        Behavior = behavior;
    }
}

public class CompressionOptions
{
    public bool EnableGzip { get; private set; }
    public bool EnableBrotli { get; private set; }
    public List<string> CompressibleMimeTypes { get; private set; }
    public int MinFileSizeBytes { get; private set; }

    public CompressionOptions(
        bool enableGzip = true,
        bool enableBrotli = true,
        List<string>? compressibleMimeTypes = null,
        int minFileSizeBytes = 1024)
    {
        EnableGzip = enableGzip;
        EnableBrotli = enableBrotli;
        CompressibleMimeTypes = compressibleMimeTypes ?? GetDefaultCompressibleMimeTypes();
        MinFileSizeBytes = minFileSizeBytes;
    }

    public static CompressionOptions Default => new();

    private static List<string> GetDefaultCompressibleMimeTypes()
    {
        return new List<string>
        {
            "text/html",
            "text/css",
            "text/javascript",
            "application/javascript",
            "application/json",
            "application/xml",
            "text/xml",
            "text/plain",
            "image/svg+xml"
        };
    }
}

public class SecurityOptions
{
    public bool EnableHttps { get; private set; }
    public bool RedirectHttpToHttps { get; private set; }
    public bool EnableHsts { get; private set; }
    public TimeSpan HstsMaxAge { get; private set; }
    public List<string> AllowedOrigins { get; private set; }
    public Dictionary<string, string> SecurityHeaders { get; private set; }

    public SecurityOptions(
        bool enableHttps = true,
        bool redirectHttpToHttps = true,
        bool enableHsts = true,
        TimeSpan hstsMaxAge = default,
        List<string>? allowedOrigins = null,
        Dictionary<string, string>? securityHeaders = null)
    {
        EnableHttps = enableHttps;
        RedirectHttpToHttps = redirectHttpToHttps;
        EnableHsts = enableHsts;
        HstsMaxAge = hstsMaxAge == default ? TimeSpan.FromDays(365) : hstsMaxAge;
        AllowedOrigins = allowedOrigins ?? new List<string>();
        SecurityHeaders = securityHeaders ?? GetDefaultSecurityHeaders();
    }

    public static SecurityOptions Default => new();

    private static Dictionary<string, string> GetDefaultSecurityHeaders()
    {
        return new Dictionary<string, string>
        {
            ["X-Content-Type-Options"] = "nosniff",
            ["X-Frame-Options"] = "DENY",
            ["X-XSS-Protection"] = "1; mode=block",
            ["Referrer-Policy"] = "strict-origin-when-cross-origin"
        };
    }
}
