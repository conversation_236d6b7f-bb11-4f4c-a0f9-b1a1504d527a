{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TLI_NetworkFleetManagement;User Id=timescale;Password=timescale", "RabbitMQ": "localhost", "Redis": "localhost:6379"}, "Authentication": {"Authority": "http://localhost:5001", "Audience": "networkfleet-api"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/networkfleet-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}, "AllowedHosts": "*"}