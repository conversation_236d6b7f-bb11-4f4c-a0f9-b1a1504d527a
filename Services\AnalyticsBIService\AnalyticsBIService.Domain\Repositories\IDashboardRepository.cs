using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for Dashboard entity
/// </summary>
public interface IDashboardRepository : IRepositoryBase<Dashboard, Guid>
{
    /// <summary>
    /// Get dashboards by user ID
    /// </summary>
    Task<PagedResult<Dashboard>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboards by type
    /// </summary>
    Task<PagedResult<Dashboard>> GetByTypeAsync(
        DashboardType type,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboard by name
    /// </summary>
    Task<Dashboard?> GetByNameAsync(
        string name,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get public dashboards
    /// </summary>
    Task<PagedResult<Dashboard>> GetPublicDashboardsAsync(
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get shared dashboards for user
    /// </summary>
    Task<PagedResult<Dashboard>> GetSharedDashboardsAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboard with widgets
    /// </summary>
    Task<Dashboard?> GetWithWidgetsAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clone dashboard
    /// </summary>
    Task<Dashboard> CloneDashboardAsync(
        Guid sourceDashboardId,
        string newName,
        Guid newUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update dashboard layout
    /// </summary>
    Task UpdateLayoutAsync(
        Guid dashboardId,
        string layout,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get favorite dashboards
    /// </summary>
    Task<List<Dashboard>> GetFavoriteDashboardsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Set dashboard as favorite
    /// </summary>
    Task SetFavoriteAsync(
        Guid dashboardId,
        Guid userId,
        bool isFavorite,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for DashboardWidget entity
/// </summary>
public interface IDashboardWidgetRepository : IRepositoryBase<DashboardWidget, Guid>
{
    /// <summary>
    /// Get widgets by dashboard ID
    /// </summary>
    Task<IEnumerable<DashboardWidget>> GetByDashboardIdAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get widget by position
    /// </summary>
    Task<DashboardWidget?> GetByPositionAsync(
        Guid dashboardId,
        int position,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update widget position
    /// </summary>
    Task UpdatePositionAsync(
        Guid widgetId,
        int newRow,
        int newColumn,
        int newWidth,
        int newHeight,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update widget configuration
    /// </summary>
    Task UpdateConfigurationAsync(
        Guid widgetId,
        string configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update widget positions
    /// </summary>
    Task BulkUpdatePositionsAsync(
        List<(Guid WidgetId, int Row, int Column, int Width, int Height)> positions,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete widgets by dashboard ID
    /// </summary>
    Task DeleteByDashboardIdAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get widgets by user ID
    /// </summary>
    Task<IEnumerable<DashboardWidget>> GetByUserIdAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active widgets
    /// </summary>
    Task<IEnumerable<DashboardWidget>> GetActiveWidgetsAsync(
        CancellationToken cancellationToken = default);
}
