using MediatR;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.Commands.CreateCustomReportTemplate;

public class CreateCustomReportTemplateCommand : IRequest<CustomReportTemplateDto>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsPublic { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public ReportConfigurationDto Configuration { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public Guid CreatedBy { get; set; }
    public bool ValidateConfiguration { get; set; } = true;
    public bool SaveAsDraft { get; set; } = false;
    public string? BasedOnTemplateId { get; set; }
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class GenerateReportFromTemplateCommand : IRequest<ComprehensiveReportDto>
{
    public Guid TemplateId { get; set; }
    public Dictionary<string, object> ParameterValues { get; set; } = new();
    public string OutputFormat { get; set; } = "PDF";
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public Guid RequestedBy { get; set; }
    public bool SaveResults { get; set; } = true;
    public string? ResultsName { get; set; }
    public List<string> Recipients { get; set; } = new();
    public bool ScheduleReport { get; set; } = false;
    public string? ScheduleExpression { get; set; }
}
