# 📊 Data Models & DTOs Reference

## 📋 Overview

This document provides comprehensive data models and DTOs (Data Transfer Objects) used across the TLI platform. These models are essential for frontend development to ensure proper data handling and type safety.

## 👥 User Management Models

### User Profile

```typescript
interface UserProfile {
  id: string;
  userId: string;
  userType: 'Broker' | 'Transporter' | 'Shipper';
  status: 'Pending' | 'Approved' | 'Rejected' | 'Suspended';
  personalDetails: PersonalDetails;
  companyDetails: CompanyDetails;
  addressDetails: AddressDetails;
  kycStatus: KYCStatus;
  metadata: UserMetadata;
}

interface PersonalDetails {
  firstName: string;
  lastName: string;
  dateOfBirth: string; // ISO date string
  phoneNumber: string;
  alternatePhoneNumber?: string;
  email: string;
  alternateEmail?: string;
  profilePictureUrl?: string;
}

interface CompanyDetails {
  companyName: string;
  companyType: 'Private' | 'Public' | 'Partnership' | 'Sole Proprietorship';
  registrationNumber: string;
  taxIdentificationNumber?: string;
  incorporationDate?: string; // ISO date string
  website?: string;
  description?: string;
  employeeCount: '1-10' | '11-50' | '51-200' | '201-500' | '500+';
  annualRevenue: '< 1M' | '1M-10M' | '10M-50M' | '50M+';
  businessLicense?: string;
  companyLogoUrl?: string;
}

interface AddressDetails {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  coordinates?: Coordinates;
}

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface KYCStatus {
  overallStatus: 'Pending' | 'InReview' | 'Approved' | 'Rejected';
  documentsSubmitted: boolean;
  documentsApproved: boolean;
  profileCompleted: boolean;
  lastReviewDate?: string; // ISO date string
  reviewComments?: string;
}

interface UserMetadata {
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  lastLoginAt?: string; // ISO date string
  profileCompletionPercentage: number;
  isActive: boolean;
  tags: string[];
}
```

### Document Models

```typescript
interface Document {
  id: string;
  documentType: DocumentType;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string; // ISO date string
  status: 'Pending' | 'Approved' | 'Rejected';
  reviewComments?: string;
  reviewedAt?: string; // ISO date string
  reviewedBy?: string;
  expiryDate?: string; // ISO date string
  ocrData?: OCRData;
}

type DocumentType = 
  | 'PAN' 
  | 'Aadhaar' 
  | 'DrivingLicense' 
  | 'CompanyRegistration' 
  | 'TaxCertificate' 
  | 'BankStatement';

interface OCRData {
  extractedText: string;
  confidence: number;
  fields: Record<string, any>;
}
```

## 📦 Order Management Models

### RFQ (Request for Quote)

```typescript
interface RFQ {
  id: string;
  rfqNumber: string;
  title: string;
  description: string;
  status: RFQStatus;
  createdBy: string;
  createdAt: string; // ISO date string
  publishedAt?: string; // ISO date string
  biddingDeadline: string; // ISO date string
  cargoDetails: CargoDetails;
  routeDetails: RouteDetails;
  timeRequirements: TimeRequirements;
  vehicleRequirements: VehicleRequirements;
  budgetExpectations: BudgetExpectations;
  milestoneTemplate: string;
  visibility: 'Public' | 'Private' | 'Network';
  bidCount: number;
  viewCount: number;
  shortlistedCarriers: string[];
  attachments: Attachment[];
  timeline: TimelineEvent[];
}

type RFQStatus = 'Draft' | 'Published' | 'Closed' | 'Awarded' | 'Cancelled';

interface CargoDetails {
  cargoType: CargoType;
  weight: number;
  weightUnit: 'KG' | 'LB' | 'TON';
  volume: number;
  volumeUnit: 'CBM' | 'CFT';
  quantity: number;
  quantityUnit: 'Pieces' | 'Boxes' | 'Pallets' | 'Containers';
  value: number;
  currency: string;
  packagingType: 'Boxes' | 'Pallets' | 'Loose' | 'Containers';
  specialInstructions?: string;
  hazardousDetails?: HazardousDetails;
}

type CargoType = 
  | 'Electronics' 
  | 'Perishable' 
  | 'Hazardous' 
  | 'General' 
  | 'Automotive' 
  | 'Textile';

interface HazardousDetails {
  hazardClass: string;
  unNumber: string;
  packingGroup: string;
  specialHandling: string[];
}

interface RouteDetails {
  pickupLocation: LocationDetails;
  deliveryLocation: LocationDetails;
  intermediateStops: IntermediateStop[];
  totalDistance: number;
  estimatedDuration: number; // minutes
}

interface LocationDetails {
  address: string;
  coordinates: Coordinates;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  preferredTimeSlot: TimeSlot;
  specialInstructions?: string;
}

interface TimeSlot {
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
}

interface IntermediateStop {
  address: string;
  stopType: 'Pickup' | 'Delivery' | 'Rest';
  estimatedDuration: number; // minutes
  specialInstructions?: string;
}

interface TimeRequirements {
  pickupDate: string; // ISO date string
  deliveryDate: string; // ISO date string
  isFlexible: boolean;
  urgencyLevel: 'Low' | 'Medium' | 'High' | 'Critical';
}

interface VehicleRequirements {
  vehicleType: VehicleType;
  vehicleSize: 'Small' | 'Medium' | 'Large' | 'XLarge';
  specialFeatures: string[];
  driverRequirements: string[];
}

type VehicleType = 'Truck' | 'Trailer' | 'Container' | 'Van' | 'Bike';

interface BudgetExpectations {
  minBudget: number;
  maxBudget: number;
  currency: string;
  paymentTerms: 'Advance' | 'COD' | 'Credit30' | 'Credit60';
  includesTax: boolean;
}
```

### Bid Models

```typescript
interface Bid {
  id: string;
  bidNumber: string;
  rfqId: string;
  bidderId: string;
  status: BidStatus;
  quotedPrice: number;
  currency: string;
  validityPeriod: number; // days
  submittedAt: string; // ISO date string
  validUntil: string; // ISO date string
  proposedPickupDate: string; // ISO date string
  proposedDeliveryDate: string; // ISO date string
  vehicleDetails: BidVehicleDetails;
  driverDetails: BidDriverDetails;
  serviceInclusions: string[];
  terms: string;
  specialOffers?: string;
  comments?: string;
  attachments: Attachment[];
  ranking?: number;
  competitiveScore?: number;
}

type BidStatus = 
  | 'Submitted' 
  | 'UnderReview' 
  | 'Shortlisted' 
  | 'Accepted' 
  | 'Rejected' 
  | 'Withdrawn';

interface BidVehicleDetails {
  vehicleId: string;
  vehicleNumber: string;
  vehicleType: VehicleType;
  capacity: string;
  features: string[];
}

interface BidDriverDetails {
  driverId: string;
  driverName: string;
  driverPhone: string;
  experience: string;
  rating: number;
}
```

### Order Models

```typescript
interface Order {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  rfqId: string;
  acceptedBidId: string;
  shipperId: string;
  transporterId: string;
  finalPrice: number;
  currency: string;
  paymentTerms: string;
  specialInstructions?: string;
  milestones: Milestone[];
  createdAt: string; // ISO date string
  estimatedCompletionDate: string; // ISO date string
  actualCompletionDate?: string; // ISO date string
  timeline: TimelineEvent[];
}

type OrderStatus = 
  | 'Created' 
  | 'Confirmed' 
  | 'InProgress' 
  | 'Completed' 
  | 'Cancelled';

interface Milestone {
  id: string;
  name: string;
  description: string;
  status: MilestoneStatus;
  payoutPercentage: number;
  requiredDocuments: string[];
  completedAt?: string; // ISO date string
  documents: Document[];
  location?: Coordinates;
}

type MilestoneStatus = 'Pending' | 'InProgress' | 'Completed' | 'Failed';
```

## 🚛 Trip Management Models

### Trip Models

```typescript
interface Trip {
  id: string;
  tripNumber: string;
  status: TripStatus;
  orderId: string;
  orderNumber: string;
  vehicleDetails: TripVehicleDetails;
  driverDetails: TripDriverDetails;
  routeDetails: TripRouteDetails;
  timeline: TimelineEvent[];
  milestones: Milestone[];
  currentStatus: TripCurrentStatus;
  createdAt: string; // ISO date string
  estimatedStartTime: string; // ISO date string
  estimatedCompletionTime: string; // ISO date string
  actualStartTime?: string; // ISO date string
  actualCompletionTime?: string; // ISO date string
}

type TripStatus = 
  | 'Created' 
  | 'Assigned' 
  | 'Started' 
  | 'InProgress' 
  | 'Completed' 
  | 'Cancelled';

interface TripVehicleDetails {
  id: string;
  vehicleNumber: string;
  vehicleType: VehicleType;
  capacity: string;
  currentLocation: LocationUpdate;
}

interface TripDriverDetails {
  id: string;
  name: string;
  phone: string;
  licenseNumber: string;
  rating: number;
}

interface TripRouteDetails {
  stops: TripStop[];
  totalDistance: number;
  completedDistance: number;
  remainingDistance: number;
  estimatedTimeToCompletion: number; // minutes
}

interface TripStop {
  id: string;
  type: 'Pickup' | 'Delivery' | 'Rest';
  location: LocationDetails;
  plannedArrival: string; // ISO date string
  plannedDeparture: string; // ISO date string
  actualArrival?: string; // ISO date string
  actualDeparture?: string; // ISO date string
  status: StopStatus;
  estimatedDuration: number; // minutes
  actualDuration?: number; // minutes
  specialInstructions?: string;
  requiredDocuments: string[];
  cargoDetails?: CargoDetails;
}

type StopStatus = 
  | 'Pending' 
  | 'Approaching' 
  | 'Arrived' 
  | 'InProgress' 
  | 'Completed' 
  | 'Failed';

interface TripCurrentStatus {
  currentLocation: LocationUpdate;
  nextStop?: {
    id: string;
    type: string;
    eta: string; // ISO date string
    distance: number;
  };
  speed: number;
  isMoving: boolean;
  lastUpdate: string; // ISO date string
}
```

### Location & Tracking Models

```typescript
interface LocationUpdate {
  latitude: number;
  longitude: number;
  accuracy: number;
  speed: number;
  heading: number;
  altitude?: number;
  timestamp: string; // ISO date string
  address?: string;
}

interface TrackingData {
  tripId: string;
  currentLocation: LocationUpdate;
  routeProgress: RouteProgress;
  eta: ETAData;
  geofenceStatus: GeofenceStatus;
  alerts: TrackingAlert[];
}

interface RouteProgress {
  totalDistance: number;
  completedDistance: number;
  remainingDistance: number;
  progressPercentage: number;
}

interface ETAData {
  nextStop: string; // ISO date string
  finalDestination: string; // ISO date string
}

interface GeofenceStatus {
  isInGeofence: boolean;
  nearestGeofence: string;
  distanceToGeofence: number;
}

interface TrackingAlert {
  type: AlertType;
  message: string;
  severity: 'Info' | 'Warning' | 'Error' | 'Critical';
  timestamp: string; // ISO date string
}

type AlertType = 
  | 'SpeedLimit' 
  | 'RouteDeviation' 
  | 'GeofenceEntry' 
  | 'GeofenceExit' 
  | 'LongStop' 
  | 'LowFuel';
```

### POD (Proof of Delivery) Models

```typescript
interface POD {
  id: string;
  tripId: string;
  stopId: string;
  status: PODStatus;
  submittedAt: string; // ISO date string
  submittedBy: string;
  deliveryDetails: DeliveryDetails;
  documents: PODDocument[];
  approvedAt?: string; // ISO date string
  approvedBy?: string;
  rejectedAt?: string; // ISO date string
  rejectedBy?: string;
  rejectionReason?: string;
}

type PODStatus = 'Submitted' | 'UnderReview' | 'Approved' | 'Rejected';

interface DeliveryDetails {
  deliveryTime: string; // ISO date string
  recipientName: string;
  recipientDesignation: string;
  recipientPhone: string;
  deliveryCondition: 'Good' | 'Damaged' | 'Partial';
  deliveredQuantity: number;
  damageDetails?: string;
  customerComments?: string;
  driverComments?: string;
}

interface PODDocument {
  id: string;
  type: 'Signature' | 'Photo' | 'Document';
  url: string;
  description?: string;
  timestamp: string; // ISO date string
  gpsLocation?: Coordinates;
}
```

## 🚗 Fleet Management Models

### Vehicle Models

```typescript
interface Vehicle {
  id: string;
  vehicleNumber: string;
  vehicleType: VehicleType;
  make: string;
  model: string;
  year: number;
  capacity: VehicleCapacity;
  specifications: VehicleSpecifications;
  status: VehicleStatus;
  currentLocation?: LocationUpdate;
  assignedDriver?: string;
  documents: VehicleDocument[];
  maintenance: MaintenanceRecord[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

type VehicleStatus = 
  | 'Available' 
  | 'InUse' 
  | 'Maintenance' 
  | 'OutOfService' 
  | 'Retired';

interface VehicleCapacity {
  maxWeight: number;
  maxVolume: number;
  maxLength: number;
  maxWidth: number;
  maxHeight: number;
  units: {
    weight: string;
    volume: string;
    dimensions: string;
  };
}

interface VehicleSpecifications {
  fuelType: 'Petrol' | 'Diesel' | 'Electric' | 'Hybrid' | 'CNG';
  engineCapacity: number;
  mileage: number;
  features: string[];
  insuranceDetails: InsuranceDetails;
  registrationDetails: RegistrationDetails;
}

interface InsuranceDetails {
  policyNumber: string;
  provider: string;
  expiryDate: string; // ISO date string
  coverageAmount: number;
}

interface RegistrationDetails {
  registrationNumber: string;
  registrationDate: string; // ISO date string
  expiryDate: string; // ISO date string
  state: string;
}

interface VehicleDocument {
  id: string;
  documentType: VehicleDocumentType;
  fileName: string;
  fileUrl: string;
  expiryDate?: string; // ISO date string
  status: 'Valid' | 'Expired' | 'Expiring';
}

type VehicleDocumentType = 
  | 'Registration' 
  | 'Insurance' 
  | 'PUC' 
  | 'Permit' 
  | 'FitnessTest';

interface MaintenanceRecord {
  id: string;
  maintenanceType: 'Scheduled' | 'Breakdown' | 'Inspection';
  description: string;
  cost: number;
  performedAt: string; // ISO date string
  performedBy: string;
  nextDueDate?: string; // ISO date string
  documents: Document[];
}
```

### Driver Models

```typescript
interface Driver {
  id: string;
  employeeId: string;
  personalDetails: DriverPersonalDetails;
  licenseDetails: LicenseDetails;
  contactDetails: ContactDetails;
  status: DriverStatus;
  rating: DriverRating;
  assignedVehicle?: string;
  currentLocation?: LocationUpdate;
  documents: DriverDocument[];
  performanceMetrics: PerformanceMetrics;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

type DriverStatus = 
  | 'Available' 
  | 'OnTrip' 
  | 'OffDuty' 
  | 'OnLeave' 
  | 'Suspended' 
  | 'Terminated';

interface DriverPersonalDetails {
  firstName: string;
  lastName: string;
  dateOfBirth: string; // ISO date string
  bloodGroup: string;
  emergencyContact: EmergencyContact;
  address: AddressDetails;
  profilePhotoUrl?: string;
}

interface LicenseDetails {
  licenseNumber: string;
  licenseType: string;
  issueDate: string; // ISO date string
  expiryDate: string; // ISO date string
  issuingAuthority: string;
  endorsements: string[];
}

interface ContactDetails {
  primaryPhone: string;
  alternatePhone?: string;
  email?: string;
  whatsappNumber?: string;
}

interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
}

interface DriverRating {
  overallRating: number;
  totalTrips: number;
  completedTrips: number;
  onTimeDeliveries: number;
  customerRatings: CustomerRating[];
}

interface CustomerRating {
  tripId: string;
  rating: number;
  feedback: string;
  ratedAt: string; // ISO date string
}

interface DriverDocument {
  id: string;
  documentType: DriverDocumentType;
  fileName: string;
  fileUrl: string;
  expiryDate?: string; // ISO date string
  status: 'Valid' | 'Expired' | 'Expiring';
}

type DriverDocumentType = 
  | 'License' 
  | 'Aadhaar' 
  | 'PAN' 
  | 'MedicalCertificate' 
  | 'PoliceVerification';

interface PerformanceMetrics {
  totalTrips: number;
  completedTrips: number;
  cancelledTrips: number;
  averageRating: number;
  onTimePercentage: number;
  fuelEfficiency: number;
  safetyScore: number;
  lastTripDate?: string; // ISO date string
}
```

## 🔄 Common Models

### Timeline & Events

```typescript
interface TimelineEvent {
  id: string;
  event: string;
  timestamp: string; // ISO date string
  description: string;
  performedBy?: string;
  metadata?: Record<string, any>;
}

interface Attachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string; // ISO date string
  uploadedBy: string;
}
```

### Pagination & Filtering

```typescript
interface PagedResult<T> {
  data: T[];
  pagination: PaginationInfo;
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface FilterOptions {
  searchTerm?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}
```

### API Response Models

```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

interface ApiError {
  error: {
    code: string;
    message: string;
    details?: string;
    timestamp: string;
    traceId: string;
  };
}
```
