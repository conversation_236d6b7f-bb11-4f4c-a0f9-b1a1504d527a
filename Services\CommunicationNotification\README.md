# Communication & Notification Service

A comprehensive communication and notification service supporting multiple channels including WhatsApp Business API, SMS, Email, Push Notifications, and Voice calls.

## 🚀 Features

### ✅ Implemented

- **WhatsApp Business API Integration**

  - Text message sending
  - Template message support
  - Media message support (images, documents, audio, video)
  - Location message support
  - Delivery status tracking
  - Webhook support for real-time status updates
  - Phone number verification
  - Template management

- **SMS Provider (Twilio)**

  - Text message sending with delivery receipts
  - Bulk SMS messaging
  - Delivery status tracking
  - Cost tracking and reporting
  - Phone number validation

- **Email Provider (SendGrid)**

  - HTML and text email sending
  - Template-based emails
  - Bulk email campaigns
  - Attachment support
  - Click and open tracking
  - CC/BCC support

- **Push Notification Provider (Firebase)**

  - Single device notifications
  - Multicast notifications
  - Topic-based messaging
  - Android and iOS support
  - Rich notifications with images
  - Topic subscription management

- **Voice Provider (Twilio)**

  - Voice call initiation
  - Text-to-speech messages
  - Multi-language voice support
  - Call status tracking
  - Cost estimation

- **Core Infrastructure**

  - Unified notification provider interface
  - Multi-channel notification support with failover
  - Provider factory pattern
  - Rate limiting and throttling
  - Health monitoring and metrics
  - Comprehensive error handling and logging
  - Notification orchestration service

- **Real-time Chat System (SignalR) ✅**

  - Multi-party chat conversations
  - Direct and group messaging
  - Real-time message delivery
  - Typing indicators and read receipts
  - Offline message handling with push notifications
  - Conversation threading and management
  - Message search functionality
  - Connection tracking and presence
  - Redis backplane support for scaling

- **Multi-Language Support System ✅**

  - Comprehensive localization framework
  - Support for English, Hindi, and Kannada
  - Dynamic message template system
  - Real-time content translation
  - Automatic language detection
  - Cultural adaptation and formatting
  - Template management with multi-language support
  - Translation quality assessment

- **Driver Mobile Communication Features ✅**

  - Offline capability with message queuing
  - Voice instructions in multiple languages
  - Trip status updates and assignments
  - Emergency alert system with escalation
  - Navigation instructions with voice guidance
  - Real-time driver status tracking
  - Automatic reconnection handling
  - Multi-channel communication failover

- **Communication Audit and Compliance ✅**

  - Comprehensive audit trail logging
  - Message archiving with encryption
  - Automated compliance reporting
  - Data retention policy management
  - Dispute resolution data compilation
  - GDPR and regulatory compliance
  - Security audit and monitoring
  - Legal hold and e-discovery support

- **Application Layer - Commands and Queries ✅**

  - CQRS pattern implementation with MediatR
  - Comprehensive command handlers for all operations
  - Advanced query system with pagination and filtering
  - User preference management commands
  - Conversation management commands
  - Message history and delivery status queries
  - Audit trail integration for all operations
  - Validation and error handling framework

- **Infrastructure Layer - External Services ✅**
  - Twilio SMS and Voice provider integration
  - SendGrid email provider with template support
  - Firebase Cloud Messaging for push notifications
  - WhatsApp Business API integration
  - External service factory with provider abstraction
  - Health monitoring and alerting system
  - Comprehensive configuration management
  - Provider failover and circuit breaker patterns
  - Rate limiting and performance monitoring

### 🔄 In Progress

- Template Management System

## 📋 Architecture

### Domain Layer (`CommunicationNotification.Domain`)

- **Entities**: Notification, Message, UserPreference
- **Value Objects**: MessageContent, ContactInfo, NotificationSettings
- **Enums**: NotificationChannel, MessageType, Priority, Language

### Application Layer (`CommunicationNotification.Application`)

- **Interfaces**: Service contracts for all providers and services
- **CQRS**: Commands and queries for communication operations (planned)
- **DTOs**: Data transfer objects for API communication

### Infrastructure Layer (`CommunicationNotification.Infrastructure`)

- **Providers**: WhatsApp Business API implementation
- **Webhooks**: WhatsApp webhook handling service
- **Configuration**: Provider-specific configurations
- **Models**: API response models and DTOs

## 🔧 WhatsApp Business API Setup

### Prerequisites

1. Facebook Business Account
2. WhatsApp Business Account
3. WhatsApp Business API access
4. Verified phone number

### Configuration

1. **Add WhatsApp configuration to your `appsettings.json`:**

```json
{
  "WhatsApp": {
    "BaseUrl": "https://graph.facebook.com/v18.0",
    "AccessToken": "YOUR_WHATSAPP_ACCESS_TOKEN",
    "PhoneNumberId": "YOUR_PHONE_NUMBER_ID",
    "BusinessAccountId": "YOUR_BUSINESS_ACCOUNT_ID",
    "WebhookVerifyToken": "YOUR_WEBHOOK_VERIFY_TOKEN",
    "WebhookUrl": "https://your-domain.com/api/webhooks/whatsapp",
    "MaxRetryAttempts": 3,
    "TimeoutSeconds": 30,
    "EnableWebhook": true,
    "DefaultLanguageCode": "en"
  }
}
```

2. **Register services in your `Program.cs`:**

```csharp
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);
```

### Usage Examples

#### Multi-Channel Notification Service

```csharp
public class NotificationController : ControllerBase
{
    private readonly INotificationService _notificationService;

    public NotificationController(INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    [HttpPost("send-notification")]
    public async Task<IActionResult> SendNotification([FromBody] NotificationRequest request)
    {
        var result = await _notificationService.SendNotificationAsync(request);
        return result.IsSuccess ? Ok(result) : BadRequest(result);
    }

    [HttpPost("send-bulk")]
    public async Task<IActionResult> SendBulkNotification([FromBody] BulkNotificationRequest request)
    {
        var result = await _notificationService.SendBulkNotificationAsync(request);
        return Ok(result);
    }
}
```

#### SMS Messages (Twilio)

```csharp
private readonly ISmsProvider _smsProvider;

// Send single SMS
var result = await _smsProvider.SendSmsAsync("+**********", "Your OTP is 123456");

// Send bulk SMS
var recipients = new List<string> { "+**********", "+**********" };
var bulkResult = await _smsProvider.SendBulkSmsAsync(recipients, "Important announcement");
```

#### Email Messages (SendGrid)

```csharp
private readonly IEmailProvider _emailProvider;

// Send simple email
var emailRequest = new EmailRequest
{
    ToEmail = "<EMAIL>",
    Subject = "Welcome!",
    HtmlContent = "<h1>Welcome to our service!</h1>",
    TextContent = "Welcome to our service!"
};
var result = await _emailProvider.SendEmailAsync(emailRequest);
```

#### Push Notifications (Firebase)

```csharp
private readonly IPushNotificationProvider _pushProvider;

// Send to single device
var pushRequest = new PushNotificationRequest
{
    DeviceToken = "device-token-here",
    Title = "Trip Update",
    Body = "Your trip has started",
    Data = new Dictionary<string, string> { ["tripId"] = "123" }
};
var result = await _pushProvider.SendPushNotificationAsync(pushRequest);
```

#### Real-time Chat (SignalR)

```csharp
// Client-side JavaScript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/chatHub")
    .build();

// Connect to chat hub
await connection.start();

// Send message
await connection.invoke("SendMessage", conversationId, "Hello everyone!", "Text");

// Receive messages
connection.on("ReceiveMessage", (messageId, senderId, message, messageType, timestamp) => {
    console.log(`New message from ${senderId}: ${message}`);
});

// Join conversation
await connection.invoke("JoinConversation", conversationId);

// Typing indicators
await connection.invoke("StartTyping", conversationId);
await connection.invoke("StopTyping", conversationId);
```

```csharp
// Server-side C# API
private readonly IChatService _chatService;

// Create conversation
var conversation = await _chatService.CreateConversationAsync(new CreateConversationRequest
{
    Title = "Trip Discussion",
    Type = ConversationType.Trip,
    ParticipantIds = new List<Guid> { driverId, customerId },
    CreatedBy = userId,
    RelatedEntityId = tripId,
    RelatedEntityType = "Trip"
});

// Send message
var result = await _chatService.SendMessageAsync(new ChatMessageRequest
{
    SenderId = userId,
    ConversationId = conversationId,
    Content = MessageContent.Create("", "Driver is on the way!", Language.English),
    MessageType = MessageType.TripUpdate
});
```

#### Multi-Language Support

```csharp
// Server-side C# - Localization Service
private readonly ILocalizationService _localizationService;
private readonly ITemplateService _templateService;

// Get localized text
var welcomeText = await _localizationService.GetLocalizedTextAsync(
    "welcome",
    Language.Hindi,
    new Dictionary<string, object> { ["userName"] = "राहुल" });

// Render localized template
var tripNotification = await _templateService.RenderNotificationTemplateAsync(
    "trip_started",
    Language.Kannada,
    new Dictionary<string, object>
    {
        ["origin"] = "ಬೆಂಗಳೂರು",
        ["destination"] = "ಮೈಸೂರು",
        ["driverName"] = "ರಾಜ್",
        ["vehicleNumber"] = "KA-01-AB-1234"
    });

// Translate content
var translatedContent = await _translationService.TranslateTextAsync(
    "Your trip has started",
    Language.English,
    Language.Hindi);

// Multi-language notification
var userLanguages = new Dictionary<Guid, Language>
{
    [userId1] = Language.English,
    [userId2] = Language.Hindi,
    [userId3] = Language.Kannada
};

var results = await _localizedNotificationService.SendLocalizedBulkNotificationAsync(
    userLanguages,
    "trip_started",
    tripParameters);
```

```javascript
// Client-side JavaScript - Language Detection
// Automatic language detection from headers
fetch('/api/localization/text/welcome', {
  headers: {
    'X-Language': 'hi',
    'Accept-Language': 'hi-IN,hi;q=0.9,en;q=0.8',
  },
})

// Manual language selection
const response = await fetch('/api/localization/text/trip_update?lang=kn')
const data = await response.json()
console.log(data.Text) // ಪ್ರಯಾಣ ಅಪ್ಡೇಟ್

// Template rendering with parameters
const templateResponse = await fetch(
  '/api/localization/templates/trip_started/render',
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      Language: 'Hindi',
      Parameters: {
        origin: 'दिल्ली',
        destination: 'मुंबई',
        driverName: 'अमित',
      },
    }),
  }
)
```

#### Driver Mobile Communication

```csharp
// Server-side C# - Driver Communication Service
private readonly IDriverCommunicationService _driverCommunicationService;
private readonly IVoiceInstructionService _voiceInstructionService;
private readonly IDriverOfflineService _driverOfflineService;

// Send trip assignment to driver
var assignment = new DriverTripAssignment
{
    DriverId = driverId,
    TripId = tripId,
    PickupLocation = "Bangalore Airport",
    DropoffLocation = "MG Road",
    PickupTime = DateTime.Now.AddMinutes(30),
    CustomerName = "John Doe",
    EstimatedDistance = 25.5,
    EstimatedFare = 450.00m,
    PreferredLanguage = Language.Kannada,
    EnableVoiceInstructions = true,
    IsUrgent = false
};

var result = await _driverCommunicationService.SendTripAssignmentAsync(assignment);

// Send voice navigation instruction
var navigation = new DriverNavigationInstruction
{
    DriverId = driverId,
    TripId = tripId,
    Instruction = "Turn right at the next intersection",
    Distance = 0.5,
    Direction = "Right",
    StreetName = "Brigade Road",
    PreferredLanguage = Language.Kannada,
    EnableVoice = true,
    CurrentLocation = new GeoLocation { Latitude = 12.9716, Longitude = 77.5946 }
};

await _driverCommunicationService.SendNavigationInstructionAsync(navigation);

// Send emergency alert
var emergency = new DriverEmergencyAlert
{
    DriverId = driverId,
    TripId = tripId,
    AlertType = EmergencyAlertType.Accident,
    Severity = EmergencySeverity.Critical,
    Message = "Vehicle accident reported on Outer Ring Road",
    Location = "Outer Ring Road, Marathahalli",
    RequiresResponse = true,
    PreferredLanguage = Language.English
};

await _driverCommunicationService.SendEmergencyAlertAsync(emergency);

// Handle offline driver reconnection
var reconnectionResult = await _driverOfflineService.HandleDriverReconnectionAsync(driverId);
if (reconnectionResult.IsSuccess)
{
    // Process pending messages and sync offline data
    var pendingMessages = await _driverOfflineService.GetPendingMessagesAsync(driverId);
    Console.WriteLine($"Delivered {pendingMessages.Count} pending messages");
}
```

```javascript
// Mobile App JavaScript - Driver Communication
// Offline capability with automatic sync
class DriverCommunicationManager {
  constructor() {
    this.offlineQueue = []
    this.isOnline = navigator.onLine
    this.setupEventListeners()
  }

  // Handle offline actions
  async handleOfflineAction(action) {
    if (this.isOnline) {
      return await this.sendAction(action)
    } else {
      this.offlineQueue.push(action)
      this.storeOfflineData()
      return { success: true, queued: true }
    }
  }

  // Sync when coming back online
  async syncOfflineData() {
    if (this.offlineQueue.length === 0) return

    const response = await fetch('/api/driver-communication/offline/sync', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ Actions: this.offlineQueue }),
    })

    if (response.ok) {
      this.offlineQueue = []
      this.clearOfflineData()
    }
  }

  // Update trip status (works offline)
  async updateTripStatus(tripId, status, location) {
    const action = {
      ActionType: 'TripStatusUpdate',
      Data: JSON.stringify({
        tripId: tripId,
        status: status,
        location: location,
        timestamp: new Date().toISOString(),
      }),
      Timestamp: new Date().toISOString(),
    }

    return await this.handleOfflineAction(action)
  }

  // Send emergency alert (priority sync)
  async sendEmergencyAlert(alertType, message, location) {
    const alert = {
      AlertType: alertType,
      Severity: 'Critical',
      Message: message,
      Location: location,
      RequiresResponse: true,
    }

    // Emergency alerts bypass offline queue
    return await fetch('/api/driver-communication/emergency-alert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alert),
    })
  }

  setupEventListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true
      this.syncOfflineData()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }
}

// Voice instruction handling
class VoiceInstructionHandler {
  constructor() {
    this.speechSynthesis = window.speechSynthesis
    this.currentLanguage = 'en-US'
  }

  async playVoiceInstruction(instruction, language = 'en-US') {
    if (!this.speechSynthesis) return

    const utterance = new SpeechSynthesisUtterance(instruction)
    utterance.lang = language
    utterance.rate = 0.9
    utterance.volume = 0.8

    this.speechSynthesis.speak(utterance)
  }

  setLanguage(language) {
    this.currentLanguage =
      language === 'Hindi'
        ? 'hi-IN'
        : language === 'Kannada'
        ? 'kn-IN'
        : 'en-US'
  }
}
```

#### Communication Audit and Compliance

```csharp
// Server-side C# - Audit and Compliance Service
private readonly ICommunicationAuditService _auditService;
private readonly IComplianceRepository _complianceRepository;

// Log communication audit event
var auditEvent = new CommunicationAuditEvent
{
    EventType = AuditEventType.MessageSent,
    UserId = userId,
    MessageId = messageId,
    Channel = NotificationChannel.WhatsApp,
    EventData = new Dictionary<string, object>
    {
        ["recipientPhone"] = "+91XXXXXXXXXX",
        ["messageLength"] = 150,
        ["deliveryStatus"] = "sent"
    },
    Severity = AuditSeverity.Medium,
    ComplianceFlags = ComplianceFlag.GDPR,
    IpAddress = "*************",
    SessionId = sessionId
};

var auditResult = await _auditService.LogCommunicationEventAsync(auditEvent);

// Generate compliance report
var reportRequest = new ComplianceReportRequest
{
    ReportType = ComplianceReportType.Comprehensive,
    Period = DateTime.UtcNow.AddMonths(-1),
    RequestedBy = adminUserId,
    IncludeFlags = new List<ComplianceFlag> { ComplianceFlag.GDPR, ComplianceFlag.DataLocalization },
    IncludeSensitiveData = false
};

var complianceReport = await _auditService.GenerateComplianceReportAsync(reportRequest);

// Archive message for legal hold
var archive = await _auditService.ArchiveMessageAsync(
    messageId,
    ArchiveReason.LegalHold,
    TimeSpan.FromDays(2555), // 7 years
    cancellationToken);

// Get dispute resolution data
var disputeContext = new DisputeContext
{
    DisputeId = Guid.NewGuid(),
    DisputeType = DisputeType.MessageDelivery,
    Description = "Customer claims message was not delivered",
    Priority = DisputePriority.High,
    RequestedBy = complianceOfficerId
};

var disputeData = await _auditService.GetDisputeResolutionDataAsync(messageId, disputeContext);

// Apply data retention policy
var retentionPolicy = new DataRetentionPolicy
{
    Name = "GDPR Personal Data Cleanup",
    Action = RetentionAction.Anonymize,
    RetentionPeriod = TimeSpan.FromDays(1095), // 3 years
    ApplicableFlags = new List<ComplianceFlag> { ComplianceFlag.GDPR },
    IsActive = true
};

var retentionResult = await _auditService.ApplyDataRetentionPolicyAsync(retentionPolicy);

// Get audit trail for investigation
var auditQuery = new AuditTrailQuery
{
    UserId = suspiciousUserId,
    FromDate = DateTime.UtcNow.AddDays(-30),
    ToDate = DateTime.UtcNow,
    MinSeverity = AuditSeverity.Medium,
    IncludeSensitiveData = true,
    RequestedBy = investigatorId
};

var auditTrail = await _auditService.GetAuditTrailAsync(auditQuery);
```

```javascript
// Client-side JavaScript - Compliance Dashboard
class ComplianceDashboard {
  async loadDashboard() {
    const response = await fetch('/api/compliance/dashboard')
    const dashboard = await response.json()

    this.updateMetrics(dashboard)
    this.loadRecentReports(dashboard.recentReports)
    this.showComplianceStatus(dashboard.complianceStatus)
  }

  async generateComplianceReport(reportType, period) {
    const response = await fetch('/api/compliance/reports/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        reportType: reportType,
        period: period,
        includeSensitiveData: false,
      }),
    })

    const report = await response.json()
    this.displayReport(report)
  }

  async searchAuditTrail(criteria) {
    const response = await fetch('/api/compliance/audit-trail', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: criteria.userId,
        fromDate: criteria.fromDate,
        toDate: criteria.toDate,
        eventType: criteria.eventType,
        pageSize: 100,
        pageNumber: 1,
      }),
    })

    const auditData = await response.json()
    this.displayAuditTrail(auditData.results)
  }

  async archiveMessage(messageId, reason, retentionDays) {
    const response = await fetch('/api/compliance/archive-message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messageId: messageId,
        reason: reason,
        retentionPeriod: `${retentionDays}.00:00:00`,
      }),
    })

    if (response.ok) {
      const archive = await response.json()
      console.log(`Message archived with ID: ${archive.id}`)
    }
  }
}
```

#### CQRS Commands and Queries

```csharp
// Server-side C# - CQRS Implementation with MediatR
private readonly IMediator _mediator;

// Send notification command
var sendCommand = new SendNotificationCommand
{
    UserId = userId,
    Content = "Your trip has been confirmed for tomorrow at 9:00 AM",
    MessageType = MessageType.TripConfirmation,
    Priority = Priority.High,
    PreferredChannel = NotificationChannel.WhatsApp,
    PreferredLanguage = Language.Kannada,
    RequireDeliveryConfirmation = true,
    Tags = new List<string> { "trip", "confirmation" },
    TemplateId = "trip_confirmation_template",
    TemplateParameters = new Dictionary<string, object>
    {
        ["customerName"] = "Rajesh Kumar",
        ["tripDate"] = "2024-01-15",
        ["tripTime"] = "09:00",
        ["pickupLocation"] = "Bangalore Airport",
        ["dropoffLocation"] = "MG Road"
    }
};

var sendResult = await _mediator.Send(sendCommand);

// Send bulk notifications command
var bulkCommand = new SendBulkNotificationCommand
{
    UserIds = driverIds,
    Content = "New trip opportunities available in your area",
    MessageType = MessageType.TripOpportunity,
    Priority = Priority.Normal,
    PreferredChannel = NotificationChannel.Push,
    BatchSize = 50,
    BatchDelay = TimeSpan.FromSeconds(2)
};

var bulkResult = await _mediator.Send(bulkCommand);

// Update user preferences command
var preferencesCommand = new UpdateUserPreferencesCommand
{
    UserId = userId,
    ChannelPreferences = new NotificationChannelPreferences
    {
        PrimaryChannel = NotificationChannel.WhatsApp,
        SecondaryChannels = new List<NotificationChannel> { NotificationChannel.Sms, NotificationChannel.Push },
        MessageTypeChannels = new Dictionary<MessageType, List<NotificationChannel>>
        {
            [MessageType.TripConfirmation] = new List<NotificationChannel> { NotificationChannel.WhatsApp, NotificationChannel.Sms },
            [MessageType.EmergencyAlert] = new List<NotificationChannel> { NotificationChannel.Voice, NotificationChannel.Sms }
        }
    },
    LanguagePreferences = new LanguagePreferences
    {
        PrimaryLanguage = Language.Kannada,
        SecondaryLanguages = new List<Language> { Language.Hindi, Language.English },
        EnableAutoTranslation = true
    },
    DeliveryPreferences = new DeliveryPreferences
    {
        RequireDeliveryConfirmation = true,
        RequireReadReceipts = false,
        MaxRetryAttempts = 3,
        EnableFailoverChannels = true
    }
};

var preferencesResult = await _mediator.Send(preferencesCommand);

// Get message history query
var historyQuery = new GetMessageHistoryQuery
{
    UserId = userId,
    MessageType = MessageType.TripConfirmation,
    FromDate = DateTime.UtcNow.AddDays(-30),
    ToDate = DateTime.UtcNow,
    Parameters = new QueryParameters
    {
        PageSize = 20,
        PageNumber = 1,
        Sorting = new SortingParameters
        {
            SortBy = "SentAt",
            SortDirection = SortDirection.Descending
        },
        Filtering = new FilteringParameters
        {
            SearchTerm = "trip confirmation"
        }
    }
};

var historyResult = await _mediator.Send(historyQuery);

// Get conversation history query
var conversationQuery = new GetConversationHistoryQuery
{
    ConversationId = conversationId,
    UserId = userId,
    IncludeSystemMessages = true,
    Parameters = new QueryParameters
    {
        PageSize = 50,
        PageNumber = 1
    }
};

var conversationResult = await _mediator.Send(conversationQuery);

// Create conversation command
var createConversationCommand = new CreateConversationCommand
{
    Title = "Trip Support - Booking #12345",
    Type = ConversationType.Support,
    ParticipantIds = new List<Guid> { customerId, supportAgentId },
    CreatedBy = supportAgentId,
    Description = "Customer support for trip booking issues",
    Settings = new Dictionary<string, object>
    {
        ["autoClose"] = false,
        ["priority"] = "high",
        ["department"] = "customer-support"
    }
};

var conversationCreateResult = await _mediator.Send(createConversationCommand);
```

```javascript
// Client-side JavaScript - CQRS API Integration
class CommunicationService {
  async sendNotification(notification) {
    const response = await fetch('/api/communication/notifications/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: notification.userId,
        content: notification.content,
        messageType: notification.messageType,
        priority: notification.priority,
        preferredChannel: notification.preferredChannel,
        preferredLanguage: notification.preferredLanguage,
        templateId: notification.templateId,
        templateParameters: notification.templateParameters,
        requireDeliveryConfirmation: true,
      }),
    })

    const result = await response.json()
    if (result.success) {
      console.log(`Notification sent: ${result.data.notificationId}`)
      return result.data
    } else {
      console.error('Failed to send notification:', result.error)
      throw new Error(result.error)
    }
  }

  async sendBulkNotification(bulkNotification) {
    const response = await fetch('/api/communication/notifications/send-bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userIds: bulkNotification.userIds,
        content: bulkNotification.content,
        messageType: bulkNotification.messageType,
        batchSize: 50,
        batchDelaySeconds: 2,
      }),
    })

    const result = await response.json()
    return result.data
  }

  async getMessageHistory(filters = {}) {
    const params = new URLSearchParams({
      pageSize: filters.pageSize || 20,
      pageNumber: filters.pageNumber || 1,
      sortBy: filters.sortBy || 'SentAt',
      sortDirection: filters.sortDirection || 'Descending',
      ...filters,
    })

    const response = await fetch(
      `/api/communication/messages/history?${params}`
    )
    const result = await response.json()

    if (result.success) {
      return {
        messages: result.data,
        totalCount: result.totalCount,
        pageNumber: result.pageNumber,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
      }
    }

    throw new Error(result.error)
  }

  async updateUserPreferences(preferences) {
    const response = await fetch('/api/communication/preferences', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        channelPreferences: preferences.channels,
        languagePreferences: preferences.language,
        deliveryPreferences: preferences.delivery,
        privacyPreferences: preferences.privacy,
      }),
    })

    const result = await response.json()
    if (result.success) {
      console.log('Preferences updated successfully')
      return result.data
    } else {
      console.error('Failed to update preferences:', result.error)
      throw new Error(result.error)
    }
  }

  async getConversationHistory(conversationId, options = {}) {
    const params = new URLSearchParams({
      pageSize: options.pageSize || 50,
      pageNumber: options.pageNumber || 1,
      includeSystemMessages: options.includeSystemMessages !== false,
      ...options,
    })

    const response = await fetch(
      `/api/communication/conversations/${conversationId}/messages?${params}`
    )
    const result = await response.json()

    return result.success ? result.data : []
  }

  async getMessageDeliveryStatus(messageId) {
    const response = await fetch(
      `/api/communication/messages/${messageId}/delivery-status`
    )
    const result = await response.json()

    if (result.success) {
      return result.data
    }

    throw new Error(result.error)
  }
}

// Usage example
const communicationService = new CommunicationService()

// Send a trip confirmation notification
await communicationService.sendNotification({
  userId: 'user-123',
  content: 'Your trip has been confirmed',
  messageType: 'TripConfirmation',
  priority: 'High',
  preferredChannel: 'WhatsApp',
  preferredLanguage: 'Kannada',
  templateId: 'trip_confirmation',
  templateParameters: {
    customerName: 'Rajesh Kumar',
    tripDate: '2024-01-15',
    pickupLocation: 'Bangalore Airport',
  },
})

// Get message history with pagination
const messageHistory = await communicationService.getMessageHistory({
  messageType: 'TripConfirmation',
  fromDate: '2024-01-01',
  pageSize: 20,
  pageNumber: 1,
})

// Update user communication preferences
await communicationService.updateUserPreferences({
  channels: {
    primaryChannel: 'WhatsApp',
    secondaryChannels: ['Sms', 'Push'],
  },
  language: {
    primaryLanguage: 'Kannada',
    enableAutoTranslation: true,
  },
  delivery: {
    requireDeliveryConfirmation: true,
    maxRetryAttempts: 3,
  },
})
```

#### External Service Providers

```csharp
// Server-side C# - External Service Integration
private readonly IExternalServiceFactory _serviceFactory;

// SMS Provider (Twilio)
var smsProvider = _serviceFactory.GetSmsProvider();
var smsResult = await smsProvider.SendSmsAsync(new SmsRequest
{
    PhoneNumber = "+919876543210",
    Message = "Your OTP is 123456. Valid for 5 minutes.",
    MediaUrls = new List<string>() // Optional for MMS
}, cancellationToken);

// Email Provider (SendGrid)
var emailProvider = _serviceFactory.GetEmailProvider();
var emailResult = await emailProvider.SendEmailAsync(new EmailRequest
{
    ToEmail = "<EMAIL>",
    ToName = "Rajesh Kumar",
    Subject = "Trip Confirmation - Booking #12345",
    HtmlContent = "<h1>Your trip has been confirmed!</h1><p>Details: Pickup at 9:00 AM from Bangalore Airport</p>",
    PlainTextContent = "Your trip has been confirmed! Details: Pickup at 9:00 AM from Bangalore Airport",
    CcEmails = new List<string> { "<EMAIL>" },
    Attachments = new List<EmailAttachment>
    {
        new EmailAttachment
        {
            FileName = "ticket.pdf",
            Content = Convert.ToBase64String(pdfBytes),
            ContentType = "application/pdf"
        }
    }
}, cancellationToken);

// Template Email (SendGrid)
var templateResult = await emailProvider.SendTemplateEmailAsync(new TemplateEmailRequest
{
    ToEmail = "<EMAIL>",
    TemplateId = "d-**********abcdef",
    TemplateData = new Dictionary<string, object>
    {
        ["customer_name"] = "Rajesh Kumar",
        ["trip_date"] = "2024-01-15",
        ["pickup_time"] = "09:00",
        ["pickup_location"] = "Bangalore Airport",
        ["driver_name"] = "Suresh",
        ["vehicle_number"] = "KA01AB1234"
    }
}, cancellationToken);

// Push Notifications (Firebase)
var pushProvider = _serviceFactory.GetPushProvider();
var pushResult = await pushProvider.SendPushNotificationAsync(new PushNotificationRequest
{
    DeviceToken = "device_token_here",
    Title = "Trip Update",
    Body = "Your driver is 5 minutes away",
    Platform = DevicePlatform.Android,
    Priority = NotificationPriority.High,
    Data = new Dictionary<string, string>
    {
        ["trip_id"] = "12345",
        ["driver_location"] = "12.9716,77.5946",
        ["eta"] = "5"
    },
    Icon = "trip_icon",
    Sound = "notification_sound",
    ChannelId = "trip_updates"
}, cancellationToken);

// Bulk Push Notifications
var bulkPushResult = await pushProvider.SendBulkPushNotificationAsync(new BulkPushNotificationRequest
{
    DeviceTokens = driverDeviceTokens,
    Title = "New Trip Opportunities",
    Body = "Multiple trips available in your area",
    Data = new Dictionary<string, string>
    {
        ["notification_type"] = "bulk_opportunity",
        ["area"] = "bangalore_central"
    }
}, cancellationToken);

// Voice Calls (Twilio)
var voiceProvider = _serviceFactory.GetVoiceProvider();
var voiceResult = await voiceProvider.MakeCallAsync(new VoiceCallRequest
{
    PhoneNumber = "+919876543210",
    Message = "Hello, this is an important message from TLI. Your trip booking has been confirmed. Please be ready at the pickup location by 9:00 AM.",
    Language = Language.Kannada,
    RecordCall = true,
    RepeatCount = 2,
    PauseDuration = TimeSpan.FromSeconds(2)
}, cancellationToken);

// WhatsApp Messages (Business API)
var whatsAppProvider = _serviceFactory.GetWhatsAppProvider();

// Text Message
var whatsAppResult = await whatsAppProvider.SendTextMessageAsync(
    "+919876543210",
    "Your trip has been confirmed! Driver: Suresh, Vehicle: KA01AB1234. Pickup time: 9:00 AM",
    cancellationToken);

// Template Message
var templateWhatsAppResult = await whatsAppProvider.SendTemplateMessageAsync(new WhatsAppTemplateRequest
{
    PhoneNumber = "+919876543210",
    TemplateName = "trip_confirmation",
    Language = Language.Kannada,
    HeaderParameters = new List<string> { "Trip Confirmation" },
    BodyParameters = new List<string>
    {
        "Rajesh Kumar",
        "January 15, 2024",
        "9:00 AM",
        "Bangalore Airport",
        "Suresh",
        "KA01AB1234"
    },
    ButtonParameters = new List<string> { "CONFIRM_TRIP" }
}, cancellationToken);

// Media Message
var mediaResult = await whatsAppProvider.SendMediaMessageAsync(new WhatsAppMediaRequest
{
    PhoneNumber = "+919876543210",
    MediaType = "image",
    MediaUrl = "https://example.com/trip-route-map.jpg",
    Caption = "Your trip route map"
}, cancellationToken);

// Health Monitoring
var healthStatuses = await _serviceFactory.CheckAllProvidersHealthAsync(cancellationToken);
foreach (var status in healthStatuses)
{
    if (!status.IsHealthy)
    {
        _logger.LogWarning("Provider {Provider} for {Channel} is unhealthy: {Error}",
            status.ProviderType, status.Channel, status.ErrorMessage);
    }
}

// Provider Configuration
var smsConfig = _serviceFactory.GetProviderConfiguration(NotificationChannel.Sms);
var emailConfig = _serviceFactory.GetProviderConfiguration(NotificationChannel.Email);
```

```javascript
// Client-side JavaScript - External Services API Integration
class ExternalServicesClient {
  async testSmsProvider(phoneNumber, message) {
    const response = await fetch('/api/external-services/test/sms', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        phoneNumber: phoneNumber,
        message: message || 'Test SMS from TLI',
      }),
    })

    const result = await response.json()
    if (result.isSuccess) {
      console.log(`SMS sent successfully: ${result.messageId}`)
      return result
    } else {
      console.error('SMS test failed:', result.errorMessage)
      throw new Error(result.errorMessage)
    }
  }

  async testEmailProvider(toEmail, subject, message) {
    const response = await fetch('/api/external-services/test/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        toEmail: toEmail,
        subject: subject || 'Test Email from TLI',
        message:
          message || 'This is a test email from TLI Communication Service',
      }),
    })

    const result = await response.json()
    return result.isSuccess ? result : Promise.reject(result.error)
  }

  async testPushNotification(
    deviceToken,
    title,
    message,
    platform = 'Android'
  ) {
    const response = await fetch('/api/external-services/test/push', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        deviceToken: deviceToken,
        title: title || 'Test Notification',
        message: message || 'Test push notification from TLI',
        platform: platform,
      }),
    })

    const result = await response.json()
    return result.isSuccess ? result : Promise.reject(result.error)
  }

  async getProvidersHealth() {
    const response = await fetch('/api/external-services/health')
    const result = await response.json()

    return {
      summary: result.summary,
      providers: result.providers,
      healthyCount: result.summary.healthyProviders,
      totalCount: result.summary.totalProviders,
      healthPercentage: result.summary.overallHealthPercentage,
    }
  }

  async validatePhoneNumber(phoneNumber) {
    const response = await fetch('/api/external-services/validate/phone', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phoneNumber: phoneNumber }),
    })

    const result = await response.json()
    return result.isValid
  }

  async validateEmailAddress(emailAddress) {
    const response = await fetch('/api/external-services/validate/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ emailAddress: emailAddress }),
    })

    const result = await response.json()
    return result.isValid
  }

  async getProviderMetrics() {
    const response = await fetch('/api/external-services/metrics')
    const metrics = await response.json()

    return {
      totalProviders: metrics.totalProviders,
      healthyProviders: metrics.healthyProviders,
      averageResponseTime: metrics.averageResponseTime,
      providerBreakdown: metrics.providerBreakdown,
      lastUpdated: new Date(metrics.lastUpdated),
    }
  }
}

// Usage examples
const externalServices = new ExternalServicesClient()

// Test SMS functionality
await externalServices.testSmsProvider('+919876543210', 'Test message from TLI')

// Check provider health
const health = await externalServices.getProvidersHealth()
console.log(
  `Provider health: ${health.healthPercentage.toFixed(1)}% (${
    health.healthyCount
  }/${health.totalCount})`
)

// Validate contact information
const isValidPhone = await externalServices.validatePhoneNumber('+919876543210')
const isValidEmail = await externalServices.validateEmailAddress(
  '<EMAIL>'
)

// Monitor provider metrics
const metrics = await externalServices.getProviderMetrics()
console.log('Provider metrics:', metrics)
```

#### WhatsApp Messages

```csharp
private readonly IWhatsAppProvider _whatsAppProvider;

// Send text message
var result = await _whatsAppProvider.SendTextMessageAsync("+**********", "Hello World!");
```

#### Send Template Message

```csharp
var templateRequest = new WhatsAppTemplateRequest
{
    PhoneNumber = "+**********",
    TemplateName = "trip_update",
    LanguageCode = "en",
    Parameters = new List<WhatsAppTemplateParameter>
    {
        new() { Type = "text", Value = "John Doe" },
        new() { Type = "text", Value = "Mumbai to Delhi" }
    }
};

var result = await _whatsAppProvider.SendTemplateMessageAsync(templateRequest);
```

#### Send Media Message

```csharp
var mediaRequest = new WhatsAppMediaRequest
{
    PhoneNumber = "+**********",
    MediaType = WhatsAppMediaType.Image,
    MediaUrl = "https://example.com/image.jpg",
    Caption = "Trip route map"
};

var result = await _whatsAppProvider.SendMediaMessageAsync(mediaRequest);
```

### Webhook Setup

1. **Configure webhook URL in Facebook Developer Console**
2. **Set webhook verify token in configuration**
3. **Handle webhook events in your controller:**

```csharp
[HttpGet("webhooks/whatsapp")]
public IActionResult VerifyWebhook(
    [FromQuery(Name = "hub.mode")] string mode,
    [FromQuery(Name = "hub.token")] string token,
    [FromQuery(Name = "hub.challenge")] string challenge)
{
    var result = _webhookService.VerifyWebhook(mode, token, challenge);
    return result != null ? Ok(result) : BadRequest();
}

[HttpPost("webhooks/whatsapp")]
public async Task<IActionResult> HandleWebhook([FromBody] string payload)
{
    var result = await _webhookService.ProcessWebhookAsync(payload);
    return result ? Ok() : BadRequest();
}
```

## 📊 Supported Message Types

### WhatsApp Business API ✅

- ✅ Text messages
- ✅ Template messages (for business communications)
- ✅ Media messages (image, document, audio, video)
- ✅ Location messages
- ✅ Delivery status tracking
- ✅ Read receipts

### SMS (Twilio) ✅

- ✅ Text messages with delivery receipts
- ✅ Bulk SMS messaging
- ✅ Delivery status tracking
- ✅ Cost tracking and reporting
- ✅ Phone number validation

### Email (SendGrid) ✅

- ✅ HTML and text emails
- ✅ Template-based emails
- ✅ Bulk email campaigns
- ✅ Attachment support
- ✅ Click and open tracking
- ✅ CC/BCC support

### Push Notifications (Firebase) ✅

- ✅ Single device notifications
- ✅ Multicast notifications
- ✅ Topic-based messaging
- ✅ Android and iOS support
- ✅ Rich notifications with images
- ✅ Topic subscription management

### Voice Calls (Twilio) ✅

- ✅ Voice call initiation
- ✅ Text-to-speech messages
- ✅ Multi-language voice support
- ✅ Call status tracking
- ✅ Cost estimation

### Real-time Chat (SignalR) ✅

- ✅ Multi-party conversations
- ✅ Direct and group messaging
- ✅ Real-time message delivery
- ✅ Typing indicators
- ✅ Read receipts
- ✅ Offline message handling
- ✅ Message search
- ✅ Connection tracking
- ✅ Redis backplane support

### Multi-Language Support ✅

- ✅ English, Hindi, and Kannada support
- ✅ Dynamic template localization
- ✅ Real-time content translation
- ✅ Automatic language detection
- ✅ Cultural formatting (dates, numbers, currency)
- ✅ Template parameter validation
- ✅ Translation quality assessment
- ✅ Fallback language support
- ✅ Cache-optimized performance

### Driver Mobile Communication ✅

- ✅ Offline message queuing and sync
- ✅ Voice instructions in multiple languages
- ✅ Trip assignment notifications
- ✅ Real-time navigation instructions
- ✅ Emergency alert system with escalation
- ✅ Trip status updates and tracking
- ✅ Automatic reconnection handling
- ✅ Multi-channel failover for poor connectivity
- ✅ Driver preference management

### Communication Audit and Compliance ✅

- ✅ Comprehensive audit trail logging
- ✅ Message archiving with encryption
- ✅ Automated compliance reporting (Daily/Weekly/Monthly)
- ✅ Data retention policy management
- ✅ Dispute resolution data compilation
- ✅ GDPR and regulatory compliance
- ✅ Security audit and monitoring
- ✅ Legal hold and e-discovery support
- ✅ Background compliance monitoring

### Future Channels 🔄

- 🌐 In-App Notifications
- 📱 Apple Push Notifications (APNS)
- 📧 AWS SES Email Provider

## 🌍 Multi-Language Support

The service is designed to support multiple languages:

- **English** (en)
- **Hindi** (hi)
- **Kannada** (kn)

Language-specific features:

- Localized message templates
- Content translation
- Voice instructions in local languages
- Cultural adaptation for regional markets

## 🔒 Security & Compliance

- **Webhook Verification**: Secure webhook token verification
- **Rate Limiting**: Configurable rate limits for API calls
- **Audit Trails**: Comprehensive logging and audit capabilities
- **Data Privacy**: GDPR-compliant data handling
- **Message Encryption**: End-to-end encryption support

## 📈 Monitoring & Analytics

- **Delivery Tracking**: Real-time message delivery status
- **Performance Metrics**: Response times and success rates
- **Error Handling**: Comprehensive error logging and retry mechanisms
- **Usage Analytics**: Message volume and channel performance

## 🚧 Roadmap

### Phase 1: Core Channels ✅ COMPLETE

- [x] WhatsApp Business API
- [x] SMS Provider Integration (Twilio)
- [x] Email Provider Integration (SendGrid)
- [x] Push Notification Provider (Firebase)
- [x] Voice Provider Integration (Twilio)
- [x] Multi-channel orchestration with failover
- [x] Rate limiting and health monitoring

### Phase 2: Advanced Features ✅ COMPLETE

- [x] Real-time Chat System (SignalR)
- [x] Multi-party conversations with threading
- [x] Offline message handling
- [x] Connection tracking and presence
- [x] Multi-language Support System
- [x] Comprehensive localization framework
- [x] Dynamic template management
- [x] Real-time content translation
- [ ] Advanced Analytics Dashboard
- [ ] A/B Testing for Messages

### Phase 3: Mobile & Driver Features ✅ COMPLETE

- [x] Driver Mobile App Communication
- [x] Offline Message Handling with automatic sync
- [x] Voice Instructions in multiple languages
- [x] Emergency Alert System with escalation
- [x] Real-time navigation instructions
- [x] Trip assignment and status updates
- [x] Driver preference management
- [x] Multi-channel failover for poor connectivity

### Phase 4: Compliance & Governance ✅ COMPLETE

- [x] Audit Trail System with comprehensive logging
- [x] Message Archiving with encryption and retention
- [x] Compliance Reporting (automated and on-demand)
- [x] Data Retention Policies with automated enforcement
- [x] Dispute Resolution data compilation
- [x] GDPR and regulatory compliance framework
- [x] Security audit and monitoring
- [x] Background compliance monitoring service

### Phase 5: AI & Automation

- [ ] Smart Message Routing
- [ ] Automated Response System
- [ ] Sentiment Analysis
- [ ] Predictive Delivery Optimization

## 🤝 Contributing

1. Follow the established architecture patterns
2. Implement comprehensive error handling
3. Add unit and integration tests
4. Update documentation for new features
5. Follow logging and monitoring best practices

## 📞 Support

For technical support and questions:

- Check the logs for detailed error information
- Verify configuration settings
- Test webhook connectivity
- Review rate limiting settings
