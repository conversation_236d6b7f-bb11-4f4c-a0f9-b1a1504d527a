using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.ValueObjects;
using Shared.Domain.Common;
using Shared.Infrastructure.Repositories;

namespace AnalyticsBIService.Domain.Repositories;

/// <summary>
/// Repository interface for Metric entity
/// </summary>
public interface IMetricRepository : IRepositoryBase<Metric, Guid>
{
    /// <summary>
    /// Get metrics by user ID
    /// </summary>
    Task<PagedResult<Metric>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by name
    /// </summary>
    Task<Metric?> GetByNameAsync(
        string name,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by category
    /// </summary>
    Task<PagedResult<Metric>> GetByCategoryAsync(
        string category,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by type
    /// </summary>
    Task<PagedResult<Metric>> GetByTypeAsync(
        MetricType type,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metric time series data
    /// </summary>
    Task<List<Metric>> GetTimeSeriesAsync(
        string metricName,
        DateTime startDate,
        DateTime endDate,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get KPI performance summary
    /// </summary>
    Task<List<Metric>> GetKPIPerformanceAsync(
        List<string> metricNames,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get real-time metrics
    /// </summary>
    Task<Dictionary<string, Metric>> GetRealTimeMetricsAsync(
        List<string> metricNames,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metric trends
    /// </summary>
    Task<Dictionary<string, List<Metric>>> GetMetricTrendsAsync(
        List<string> metricNames,
        DateTime startDate,
        DateTime endDate,
        TimePeriod aggregationPeriod,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Compare metrics
    /// </summary>
    Task<List<Metric>> CompareMetricsAsync(
        List<string> metricNames,
        DateTime startDate,
        DateTime endDate,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get underperforming KPIs
    /// </summary>
    Task<List<Metric>> GetUnderperformingKPIsAsync(
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by date range
    /// </summary>
    Task<PagedResult<Metric>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update metric value
    /// </summary>
    Task UpdateMetricValueAsync(
        Guid metricId,
        MetricValue newValue,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Set metric target
    /// </summary>
    Task SetMetricTargetAsync(
        Guid metricId,
        KPITarget target,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old metrics
    /// </summary>
    Task DeleteOldMetricsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by date range
    /// </summary>
    Task<List<Metric>> GetMetricsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by category and date range
    /// </summary>
    Task<List<Metric>> GetMetricsByCategoryAsync(
        MetricCategory category,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get metrics by type and date range
    /// </summary>
    Task<List<Metric>> GetMetricsByTypeAsync(
        MetricType metricType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update metrics
    /// </summary>
    Task BulkUpdateAsync(
        IEnumerable<Metric> metrics,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all metrics with pagination
    /// </summary>
    Task<PagedResult<Metric>> GetAllAsync(
        int page,
        int pageSize,
        CancellationToken cancellationToken = default);
}
