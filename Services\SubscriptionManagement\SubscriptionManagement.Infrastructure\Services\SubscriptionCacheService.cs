using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using Shared.Infrastructure.Caching;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Services
{
    public interface ISubscriptionCacheService
    {
        // Plan caching
        Task<Plan?> GetPlanAsync(Guid planId);
        Task SetPlanAsync(Plan plan, TimeSpan? expiration = null);
        Task RemovePlanAsync(Guid planId);
        Task InvalidatePlanCacheAsync();

        // Subscription caching
        Task<Subscription?> GetSubscriptionAsync(Guid subscriptionId);
        Task<Subscription?> GetUserSubscriptionAsync(Guid userId);
        Task SetSubscriptionAsync(Subscription subscription, TimeSpan? expiration = null);
        Task RemoveSubscriptionAsync(Guid subscriptionId);
        Task RemoveUserSubscriptionAsync(Guid userId);
        Task InvalidateSubscriptionCacheAsync();

        // Feature flag caching
        Task<FeatureFlag?> GetFeatureFlagAsync(string key);
        Task SetFeatureFlagAsync(FeatureFlag featureFlag, TimeSpan? expiration = null);
        Task RemoveFeatureFlagAsync(string key);
        Task InvalidateFeatureFlagCacheAsync();

        // User subscription status caching
        Task<SubscriptionStatus?> GetUserSubscriptionStatusAsync(Guid userId);
        Task SetUserSubscriptionStatusAsync(Guid userId, SubscriptionStatus status, TimeSpan? expiration = null);
        Task RemoveUserSubscriptionStatusAsync(Guid userId);

        // Usage tracking caching
        Task<Dictionary<FeatureType, int>?> GetUserUsageAsync(Guid userId, string period);
        Task SetUserUsageAsync(Guid userId, string period, Dictionary<FeatureType, int> usage, TimeSpan? expiration = null);
        Task RemoveUserUsageAsync(Guid userId, string period);
        Task InvalidateUserUsageCacheAsync(Guid userId);

        // Bulk operations
        Task InvalidateAllCacheAsync();
        Task WarmupCacheAsync();
    }

    public class SubscriptionCacheService : ISubscriptionCacheService
    {
        private readonly ICacheService _cacheService;
        private readonly ILogger<SubscriptionCacheService> _logger;

        // Cache key prefixes
        private const string PLAN_PREFIX = "subscription:plan:";
        private const string SUBSCRIPTION_PREFIX = "subscription:sub:";
        private const string USER_SUBSCRIPTION_PREFIX = "subscription:user:";
        private const string FEATURE_FLAG_PREFIX = "subscription:flag:";
        private const string USER_STATUS_PREFIX = "subscription:status:";
        private const string USER_USAGE_PREFIX = "subscription:usage:";

        // Default cache expiration times
        private static readonly TimeSpan DefaultPlanExpiration = TimeSpan.FromHours(1);
        private static readonly TimeSpan DefaultSubscriptionExpiration = TimeSpan.FromMinutes(30);
        private static readonly TimeSpan DefaultFeatureFlagExpiration = TimeSpan.FromMinutes(5);
        private static readonly TimeSpan DefaultStatusExpiration = TimeSpan.FromMinutes(15);
        private static readonly TimeSpan DefaultUsageExpiration = TimeSpan.FromMinutes(10);

        public SubscriptionCacheService(ICacheService cacheService, ILogger<SubscriptionCacheService> logger)
        {
            _cacheService = cacheService;
            _logger = logger;
        }

        // Plan caching methods
        public async Task<Plan?> GetPlanAsync(Guid planId)
        {
            try
            {
                var key = $"{PLAN_PREFIX}{planId}";
                return await _cacheService.GetAsync<Plan>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting plan {PlanId} from cache", planId);
                return null;
            }
        }

        public async Task SetPlanAsync(Plan plan, TimeSpan? expiration = null)
        {
            try
            {
                var key = $"{PLAN_PREFIX}{plan.Id}";
                await _cacheService.SetAsync(key, plan, expiration ?? DefaultPlanExpiration);
                _logger.LogDebug("Cached plan {PlanId}", plan.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching plan {PlanId}", plan.Id);
            }
        }

        public async Task RemovePlanAsync(Guid planId)
        {
            try
            {
                var key = $"{PLAN_PREFIX}{planId}";
                await _cacheService.RemoveAsync(key);
                _logger.LogDebug("Removed plan {PlanId} from cache", planId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing plan {PlanId} from cache", planId);
            }
        }

        public async Task InvalidatePlanCacheAsync()
        {
            try
            {
                await _cacheService.RemoveByPatternAsync($"{PLAN_PREFIX}*");
                _logger.LogInformation("Invalidated all plan cache");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating plan cache");
            }
        }

        // Subscription caching methods
        public async Task<Subscription?> GetSubscriptionAsync(Guid subscriptionId)
        {
            try
            {
                var key = $"{SUBSCRIPTION_PREFIX}{subscriptionId}";
                return await _cacheService.GetAsync<Subscription>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription {SubscriptionId} from cache", subscriptionId);
                return null;
            }
        }

        public async Task<Subscription?> GetUserSubscriptionAsync(Guid userId)
        {
            try
            {
                var key = $"{USER_SUBSCRIPTION_PREFIX}{userId}";
                return await _cacheService.GetAsync<Subscription>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user subscription {UserId} from cache", userId);
                return null;
            }
        }

        public async Task SetSubscriptionAsync(Subscription subscription, TimeSpan? expiration = null)
        {
            try
            {
                var subscriptionKey = $"{SUBSCRIPTION_PREFIX}{subscription.Id}";
                var userKey = $"{USER_SUBSCRIPTION_PREFIX}{subscription.UserId}";
                
                var exp = expiration ?? DefaultSubscriptionExpiration;
                
                await _cacheService.SetAsync(subscriptionKey, subscription, exp);
                await _cacheService.SetAsync(userKey, subscription, exp);
                
                _logger.LogDebug("Cached subscription {SubscriptionId} for user {UserId}", subscription.Id, subscription.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching subscription {SubscriptionId}", subscription.Id);
            }
        }

        public async Task RemoveSubscriptionAsync(Guid subscriptionId)
        {
            try
            {
                var key = $"{SUBSCRIPTION_PREFIX}{subscriptionId}";
                await _cacheService.RemoveAsync(key);
                _logger.LogDebug("Removed subscription {SubscriptionId} from cache", subscriptionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing subscription {SubscriptionId} from cache", subscriptionId);
            }
        }

        public async Task RemoveUserSubscriptionAsync(Guid userId)
        {
            try
            {
                var key = $"{USER_SUBSCRIPTION_PREFIX}{userId}";
                await _cacheService.RemoveAsync(key);
                _logger.LogDebug("Removed user subscription {UserId} from cache", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing user subscription {UserId} from cache", userId);
            }
        }

        public async Task InvalidateSubscriptionCacheAsync()
        {
            try
            {
                await _cacheService.RemoveByPatternAsync($"{SUBSCRIPTION_PREFIX}*");
                await _cacheService.RemoveByPatternAsync($"{USER_SUBSCRIPTION_PREFIX}*");
                _logger.LogInformation("Invalidated all subscription cache");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating subscription cache");
            }
        }

        // Feature flag caching methods
        public async Task<FeatureFlag?> GetFeatureFlagAsync(string key)
        {
            try
            {
                var cacheKey = $"{FEATURE_FLAG_PREFIX}{key}";
                return await _cacheService.GetAsync<FeatureFlag>(cacheKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feature flag {Key} from cache", key);
                return null;
            }
        }

        public async Task SetFeatureFlagAsync(FeatureFlag featureFlag, TimeSpan? expiration = null)
        {
            try
            {
                var key = $"{FEATURE_FLAG_PREFIX}{featureFlag.Key}";
                await _cacheService.SetAsync(key, featureFlag, expiration ?? DefaultFeatureFlagExpiration);
                _logger.LogDebug("Cached feature flag {Key}", featureFlag.Key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching feature flag {Key}", featureFlag.Key);
            }
        }

        public async Task RemoveFeatureFlagAsync(string key)
        {
            try
            {
                var cacheKey = $"{FEATURE_FLAG_PREFIX}{key}";
                await _cacheService.RemoveAsync(cacheKey);
                _logger.LogDebug("Removed feature flag {Key} from cache", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing feature flag {Key} from cache", key);
            }
        }

        public async Task InvalidateFeatureFlagCacheAsync()
        {
            try
            {
                await _cacheService.RemoveByPatternAsync($"{FEATURE_FLAG_PREFIX}*");
                _logger.LogInformation("Invalidated all feature flag cache");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating feature flag cache");
            }
        }

        // User subscription status caching methods
        public async Task<SubscriptionStatus?> GetUserSubscriptionStatusAsync(Guid userId)
        {
            try
            {
                var key = $"{USER_STATUS_PREFIX}{userId}";
                var statusString = await _cacheService.GetAsync<string>(key);
                
                if (statusString != null && Enum.TryParse<SubscriptionStatus>(statusString, out var status))
                {
                    return status;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user subscription status {UserId} from cache", userId);
                return null;
            }
        }

        public async Task SetUserSubscriptionStatusAsync(Guid userId, SubscriptionStatus status, TimeSpan? expiration = null)
        {
            try
            {
                var key = $"{USER_STATUS_PREFIX}{userId}";
                await _cacheService.SetAsync(key, status.ToString(), expiration ?? DefaultStatusExpiration);
                _logger.LogDebug("Cached user subscription status {UserId}: {Status}", userId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching user subscription status {UserId}", userId);
            }
        }

        public async Task RemoveUserSubscriptionStatusAsync(Guid userId)
        {
            try
            {
                var key = $"{USER_STATUS_PREFIX}{userId}";
                await _cacheService.RemoveAsync(key);
                _logger.LogDebug("Removed user subscription status {UserId} from cache", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing user subscription status {UserId} from cache", userId);
            }
        }

        // Usage tracking caching methods
        public async Task<Dictionary<FeatureType, int>?> GetUserUsageAsync(Guid userId, string period)
        {
            try
            {
                var key = $"{USER_USAGE_PREFIX}{userId}:{period}";
                return await _cacheService.GetAsync<Dictionary<FeatureType, int>>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user usage {UserId}:{Period} from cache", userId, period);
                return null;
            }
        }

        public async Task SetUserUsageAsync(Guid userId, string period, Dictionary<FeatureType, int> usage, TimeSpan? expiration = null)
        {
            try
            {
                var key = $"{USER_USAGE_PREFIX}{userId}:{period}";
                await _cacheService.SetAsync(key, usage, expiration ?? DefaultUsageExpiration);
                _logger.LogDebug("Cached user usage {UserId}:{Period}", userId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching user usage {UserId}:{Period}", userId, period);
            }
        }

        public async Task RemoveUserUsageAsync(Guid userId, string period)
        {
            try
            {
                var key = $"{USER_USAGE_PREFIX}{userId}:{period}";
                await _cacheService.RemoveAsync(key);
                _logger.LogDebug("Removed user usage {UserId}:{Period} from cache", userId, period);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing user usage {UserId}:{Period} from cache", userId, period);
            }
        }

        public async Task InvalidateUserUsageCacheAsync(Guid userId)
        {
            try
            {
                await _cacheService.RemoveByPatternAsync($"{USER_USAGE_PREFIX}{userId}:*");
                _logger.LogDebug("Invalidated user usage cache for {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating user usage cache for {UserId}", userId);
            }
        }

        // Bulk operations
        public async Task InvalidateAllCacheAsync()
        {
            try
            {
                await Task.WhenAll(
                    InvalidatePlanCacheAsync(),
                    InvalidateSubscriptionCacheAsync(),
                    InvalidateFeatureFlagCacheAsync(),
                    _cacheService.RemoveByPatternAsync($"{USER_STATUS_PREFIX}*"),
                    _cacheService.RemoveByPatternAsync($"{USER_USAGE_PREFIX}*")
                );
                
                _logger.LogInformation("Invalidated all subscription management cache");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating all cache");
            }
        }

        public async Task WarmupCacheAsync()
        {
            try
            {
                _logger.LogInformation("Starting cache warmup for subscription management");
                // Cache warmup logic would go here
                // This could include pre-loading frequently accessed plans, active subscriptions, etc.
                _logger.LogInformation("Cache warmup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache warmup");
            }
        }
    }
}
