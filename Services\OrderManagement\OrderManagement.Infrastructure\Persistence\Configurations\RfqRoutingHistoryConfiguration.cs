using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqRoutingHistoryConfiguration : IEntityTypeConfiguration<RfqRoutingHistory>
{
    public void Configure(EntityTypeBuilder<RfqRoutingHistory> builder)
    {
        builder.ToTable("RfqRoutingHistories");

        builder.HasKey(rh => rh.Id);

        builder.Property(rh => rh.Id)
            .ValueGeneratedOnAdd();

        builder.Property(rh => rh.RfqId)
            .IsRequired();

        builder.Property(rh => rh.Action)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rh => rh.FromEntityId)
            .IsRequired(false);

        builder.Property(rh => rh.FromEntityName)
            .HasMaxLength(200);

        builder.Property(rh => rh.FromEntityType)
            .IsRequired(false)
            .HasConversion<int>();

        builder.Property(rh => rh.ToEntityId)
            .IsRequired(false);

        builder.Property(rh => rh.ToEntityName)
            .HasMaxLength(200);

        builder.Property(rh => rh.ToEntityType)
            .IsRequired(false)
            .HasConversion<int>();

        builder.Property(rh => rh.RoutedAt)
            .IsRequired();

        builder.Property(rh => rh.RoutedBy)
            .IsRequired(false);

        builder.Property(rh => rh.RoutedByName)
            .HasMaxLength(200);

        builder.Property(rh => rh.RoutingReason)
            .HasMaxLength(500);

        builder.Property(rh => rh.RoutingNotes)
            .HasMaxLength(2000);

        builder.Property(rh => rh.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rh => rh.ResponseDeadline)
            .IsRequired(false);

        builder.Property(rh => rh.RespondedAt)
            .IsRequired(false);

        builder.Property(rh => rh.ResponseNotes)
            .HasMaxLength(2000);

        builder.Property(rh => rh.IsSuccessful)
            .IsRequired();

        builder.Property(rh => rh.FailureReason)
            .HasMaxLength(1000);

        builder.Property(rh => rh.SequenceNumber)
            .IsRequired();

        // Relationships
        builder.HasOne(rh => rh.RFQ)
            .WithMany(r => r.RoutingHistory)
            .HasForeignKey(rh => rh.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rh => rh.RfqId)
            .HasDatabaseName("IX_RfqRoutingHistories_RfqId");

        builder.HasIndex(rh => rh.RoutedAt)
            .HasDatabaseName("IX_RfqRoutingHistories_RoutedAt");

        builder.HasIndex(rh => rh.Action)
            .HasDatabaseName("IX_RfqRoutingHistories_Action");

        builder.HasIndex(rh => rh.Status)
            .HasDatabaseName("IX_RfqRoutingHistories_Status");

        builder.HasIndex(rh => rh.ToEntityId)
            .HasDatabaseName("IX_RfqRoutingHistories_ToEntityId");

        builder.HasIndex(rh => rh.FromEntityId)
            .HasDatabaseName("IX_RfqRoutingHistories_FromEntityId");

        builder.HasIndex(rh => new { rh.RfqId, rh.SequenceNumber })
            .HasDatabaseName("IX_RfqRoutingHistories_RfqId_SequenceNumber")
            .IsUnique();
    }
}
