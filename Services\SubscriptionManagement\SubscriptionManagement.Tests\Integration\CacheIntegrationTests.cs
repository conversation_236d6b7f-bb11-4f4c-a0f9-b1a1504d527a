using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Services;
using Shared.Infrastructure.Caching;
using StackExchange.Redis;
using Xunit;

namespace SubscriptionManagement.Tests.Integration
{
    [Collection("Redis")]
    public class CacheIntegrationTests : IAsyncLifetime
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ISubscriptionCacheService _cacheService;
        private readonly IDatabase _redisDatabase;

        public CacheIntegrationTests()
        {
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            
            // Add Redis (using test Redis instance)
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = "localhost:6379";
                options.InstanceName = "SubscriptionManagement_Test";
            });

            // Add cache services
            services.AddScoped<ICacheService, RedisCacheService>();
            services.AddScoped<ISubscriptionCacheService, SubscriptionCacheService>();

            _serviceProvider = services.BuildServiceProvider();
            _cacheService = _serviceProvider.GetRequiredService<ISubscriptionCacheService>();

            // Get Redis database for cleanup
            var redis = ConnectionMultiplexer.Connect("localhost:6379");
            _redisDatabase = redis.GetDatabase();
        }

        public async Task InitializeAsync()
        {
            // Clean up any existing test data
            await CleanupTestData();
        }

        public async Task DisposeAsync()
        {
            await CleanupTestData();
            _serviceProvider.Dispose();
        }

        [Fact]
        public async Task PlanCaching_SetAndGet_ShouldWorkCorrectly()
        {
            // Arrange
            var plan = new Plan(
                "Test Plan",
                "Test plan for caching",
                Money.Create(99.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Premium,
                UserType.Individual
            );

            // Act
            await _cacheService.SetPlanAsync(plan, TimeSpan.FromMinutes(5));
            var cachedPlan = await _cacheService.GetPlanAsync(plan.Id);

            // Assert
            Assert.NotNull(cachedPlan);
            Assert.Equal(plan.Id, cachedPlan.Id);
            Assert.Equal(plan.Name, cachedPlan.Name);
            Assert.Equal(plan.Description, cachedPlan.Description);
            Assert.Equal(plan.Price.Amount, cachedPlan.Price.Amount);
            Assert.Equal(plan.Price.Currency, cachedPlan.Price.Currency);
        }

        [Fact]
        public async Task PlanCaching_Remove_ShouldDeleteFromCache()
        {
            // Arrange
            var plan = new Plan(
                "Test Plan",
                "Test plan for removal",
                Money.Create(49.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Basic,
                UserType.Individual
            );

            await _cacheService.SetPlanAsync(plan);

            // Act
            await _cacheService.RemovePlanAsync(plan.Id);
            var cachedPlan = await _cacheService.GetPlanAsync(plan.Id);

            // Assert
            Assert.Null(cachedPlan);
        }

        [Fact]
        public async Task SubscriptionCaching_SetAndGet_ShouldWorkCorrectly()
        {
            // Arrange
            var plan = new Plan(
                "Test Plan",
                "Test plan",
                Money.Create(29.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Basic,
                UserType.Individual
            );

            var userId = Guid.NewGuid();
            var subscription = new Subscription(
                userId,
                plan,
                DateTime.UtcNow,
                null,
                true,
                ProrationMode.CreateProrations
            );

            // Act
            await _cacheService.SetSubscriptionAsync(subscription, TimeSpan.FromMinutes(10));
            var cachedSubscription = await _cacheService.GetSubscriptionAsync(subscription.Id);
            var userSubscription = await _cacheService.GetUserSubscriptionAsync(userId);

            // Assert
            Assert.NotNull(cachedSubscription);
            Assert.NotNull(userSubscription);
            Assert.Equal(subscription.Id, cachedSubscription.Id);
            Assert.Equal(subscription.UserId, cachedSubscription.UserId);
            Assert.Equal(subscription.Id, userSubscription.Id);
        }

        [Fact]
        public async Task FeatureFlagCaching_SetAndGet_ShouldWorkCorrectly()
        {
            // Arrange
            var featureFlag = new FeatureFlag(
                "test_feature",
                "Test Feature",
                "Test feature for caching",
                true,
                new Dictionary<string, object>
                {
                    ["rollout_percentage"] = 50,
                    ["target_users"] = new[] { "premium", "enterprise" }
                }
            );

            // Act
            await _cacheService.SetFeatureFlagAsync(featureFlag, TimeSpan.FromMinutes(2));
            var cachedFlag = await _cacheService.GetFeatureFlagAsync(featureFlag.Key);

            // Assert
            Assert.NotNull(cachedFlag);
            Assert.Equal(featureFlag.Key, cachedFlag.Key);
            Assert.Equal(featureFlag.Name, cachedFlag.Name);
            Assert.Equal(featureFlag.IsEnabled, cachedFlag.IsEnabled);
        }

        [Fact]
        public async Task UserSubscriptionStatusCaching_SetAndGet_ShouldWorkCorrectly()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var status = SubscriptionStatus.Active;

            // Act
            await _cacheService.SetUserSubscriptionStatusAsync(userId, status, TimeSpan.FromMinutes(15));
            var cachedStatus = await _cacheService.GetUserSubscriptionStatusAsync(userId);

            // Assert
            Assert.NotNull(cachedStatus);
            Assert.Equal(status, cachedStatus.Value);
        }

        [Fact]
        public async Task UserUsageCaching_SetAndGet_ShouldWorkCorrectly()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var period = "2024-01";
            var usage = new Dictionary<FeatureType, int>
            {
                { FeatureType.ApiCalls, 150 },
                { FeatureType.DataStorage, 500 },
                { FeatureType.Users, 10 }
            };

            // Act
            await _cacheService.SetUserUsageAsync(userId, period, usage, TimeSpan.FromMinutes(10));
            var cachedUsage = await _cacheService.GetUserUsageAsync(userId, period);

            // Assert
            Assert.NotNull(cachedUsage);
            Assert.Equal(usage.Count, cachedUsage.Count);
            Assert.Equal(usage[FeatureType.ApiCalls], cachedUsage[FeatureType.ApiCalls]);
            Assert.Equal(usage[FeatureType.DataStorage], cachedUsage[FeatureType.DataStorage]);
            Assert.Equal(usage[FeatureType.Users], cachedUsage[FeatureType.Users]);
        }

        [Fact]
        public async Task CacheExpiration_ShouldExpireAfterTimeout()
        {
            // Arrange
            var plan = new Plan(
                "Expiring Plan",
                "Plan that will expire",
                Money.Create(19.99m, "USD"),
                BillingCycle.Monthly,
                PlanType.Basic,
                UserType.Individual
            );

            // Act
            await _cacheService.SetPlanAsync(plan, TimeSpan.FromSeconds(2));
            
            // Verify it's cached
            var cachedPlan = await _cacheService.GetPlanAsync(plan.Id);
            Assert.NotNull(cachedPlan);

            // Wait for expiration
            await Task.Delay(TimeSpan.FromSeconds(3));

            // Verify it's expired
            var expiredPlan = await _cacheService.GetPlanAsync(plan.Id);
            Assert.Null(expiredPlan);
        }

        [Fact]
        public async Task InvalidateAllCache_ShouldClearAllCachedData()
        {
            // Arrange
            var plan = new Plan("Test Plan", "Test", Money.Create(10, "USD"), BillingCycle.Monthly, PlanType.Basic, UserType.Individual);
            var userId = Guid.NewGuid();
            var subscription = new Subscription(userId, plan, DateTime.UtcNow, null, true, ProrationMode.CreateProrations);
            var featureFlag = new FeatureFlag("test_flag", "Test Flag", "Test", true, new Dictionary<string, object>());

            await _cacheService.SetPlanAsync(plan);
            await _cacheService.SetSubscriptionAsync(subscription);
            await _cacheService.SetFeatureFlagAsync(featureFlag);
            await _cacheService.SetUserSubscriptionStatusAsync(userId, SubscriptionStatus.Active);

            // Act
            await _cacheService.InvalidateAllCacheAsync();

            // Assert
            Assert.Null(await _cacheService.GetPlanAsync(plan.Id));
            Assert.Null(await _cacheService.GetSubscriptionAsync(subscription.Id));
            Assert.Null(await _cacheService.GetFeatureFlagAsync(featureFlag.Key));
            Assert.Null(await _cacheService.GetUserSubscriptionStatusAsync(userId));
        }

        [Fact]
        public async Task ConcurrentCacheOperations_ShouldHandleCorrectly()
        {
            // Arrange
            var tasks = new List<Task>();
            var planIds = new List<Guid>();

            // Act - Perform concurrent cache operations
            for (int i = 0; i < 10; i++)
            {
                var planId = Guid.NewGuid();
                planIds.Add(planId);

                var plan = new Plan(
                    $"Concurrent Plan {i}",
                    $"Plan {i}",
                    Money.Create(i * 10, "USD"),
                    BillingCycle.Monthly,
                    PlanType.Basic,
                    UserType.Individual
                );

                tasks.Add(_cacheService.SetPlanAsync(plan));
            }

            await Task.WhenAll(tasks);

            // Verify all plans are cached
            var retrievalTasks = planIds.Select(id => _cacheService.GetPlanAsync(id)).ToArray();
            var results = await Task.WhenAll(retrievalTasks);

            // Assert
            Assert.All(results, plan => Assert.NotNull(plan));
            Assert.Equal(10, results.Length);
        }

        private async Task CleanupTestData()
        {
            try
            {
                // Clean up test cache data
                var server = _redisDatabase.Multiplexer.GetServer("localhost:6379");
                var keys = server.Keys(pattern: "SubscriptionManagement_Test:subscription:*").ToArray();
                
                if (keys.Any())
                {
                    await _redisDatabase.KeyDeleteAsync(keys);
                }
            }
            catch (Exception)
            {
                // Ignore cleanup errors in tests
            }
        }
    }

    [CollectionDefinition("Redis")]
    public class RedisCollection : ICollectionFixture<RedisFixture>
    {
    }

    public class RedisFixture : IAsyncLifetime
    {
        public async Task InitializeAsync()
        {
            // Ensure Redis is available for tests
            try
            {
                var redis = await ConnectionMultiplexer.ConnectAsync("localhost:6379");
                await redis.GetDatabase().PingAsync();
                redis.Dispose();
            }
            catch (Exception)
            {
                throw new InvalidOperationException("Redis is not available for integration tests. Please ensure Redis is running on localhost:6379");
            }
        }

        public Task DisposeAsync()
        {
            return Task.CompletedTask;
        }
    }
}
