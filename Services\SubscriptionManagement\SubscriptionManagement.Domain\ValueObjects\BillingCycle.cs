using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.ValueObjects
{
    public class BillingCycle : IEquatable<BillingCycle>
    {
        public BillingInterval Interval { get; private set; }
        public int IntervalCount { get; private set; }
        public int? CustomDays { get; private set; }

        private BillingCycle() { }

        public BillingCycle(BillingInterval interval, int intervalCount = 1, int? customDays = null)
        {
            if (intervalCount <= 0)
                throw new SubscriptionDomainException("Interval count must be greater than 0");

            if (interval == BillingInterval.Custom && (!customDays.HasValue || customDays.Value <= 0))
                throw new SubscriptionDomainException("Custom days must be specified and greater than 0 for custom billing interval");

            if (interval != BillingInterval.Custom && customDays.HasValue)
                throw new SubscriptionDomainException("Custom days should only be specified for custom billing interval");

            Interval = interval;
            IntervalCount = intervalCount;
            CustomDays = customDays;
        }

        public static BillingCycle Weekly(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.Weekly, intervalCount);
        }

        public static BillingCycle BiWeekly(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.BiWeekly, intervalCount);
        }

        public static BillingCycle Monthly(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.Monthly, intervalCount);
        }

        public static BillingCycle Quarterly(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.Quarterly, intervalCount);
        }

        public static BillingCycle SemiAnnually(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.SemiAnnually, intervalCount);
        }

        public static BillingCycle Annually(int intervalCount = 1)
        {
            return new BillingCycle(BillingInterval.Annually, intervalCount);
        }

        public static BillingCycle Custom(int days)
        {
            return new BillingCycle(BillingInterval.Custom, 1, days);
        }

        public DateTime CalculateNextBillingDate(DateTime currentDate)
        {
            return Interval switch
            {
                BillingInterval.Weekly => currentDate.AddDays(7 * IntervalCount),
                BillingInterval.BiWeekly => currentDate.AddDays(14 * IntervalCount),
                BillingInterval.Monthly => currentDate.AddMonths(IntervalCount),
                BillingInterval.Quarterly => currentDate.AddMonths(3 * IntervalCount),
                BillingInterval.SemiAnnually => currentDate.AddMonths(6 * IntervalCount),
                BillingInterval.Annually => currentDate.AddYears(IntervalCount),
                BillingInterval.Custom => currentDate.AddDays(CustomDays!.Value),
                _ => throw new SubscriptionDomainException($"Unsupported billing interval: {Interval}")
            };
        }

        public int GetDaysInCycle()
        {
            return Interval switch
            {
                BillingInterval.Weekly => 7 * IntervalCount,
                BillingInterval.BiWeekly => 14 * IntervalCount,
                BillingInterval.Monthly => 30 * IntervalCount, // Approximate
                BillingInterval.Quarterly => 90 * IntervalCount, // Approximate
                BillingInterval.SemiAnnually => 180 * IntervalCount, // Approximate
                BillingInterval.Annually => 365 * IntervalCount, // Approximate
                BillingInterval.Custom => CustomDays!.Value,
                _ => throw new SubscriptionDomainException($"Unsupported billing interval: {Interval}")
            };
        }

        public string GetDisplayName()
        {
            return Interval switch
            {
                BillingInterval.Weekly when IntervalCount == 1 => "Weekly",
                BillingInterval.Weekly => $"Every {IntervalCount} weeks",
                BillingInterval.BiWeekly when IntervalCount == 1 => "Bi-weekly",
                BillingInterval.BiWeekly => $"Every {IntervalCount * 2} weeks",
                BillingInterval.Monthly when IntervalCount == 1 => "Monthly",
                BillingInterval.Monthly => $"Every {IntervalCount} months",
                BillingInterval.Quarterly when IntervalCount == 1 => "Quarterly",
                BillingInterval.Quarterly => $"Every {IntervalCount} quarters",
                BillingInterval.SemiAnnually when IntervalCount == 1 => "Semi-annually",
                BillingInterval.SemiAnnually => $"Every {IntervalCount * 6} months",
                BillingInterval.Annually when IntervalCount == 1 => "Annually",
                BillingInterval.Annually => $"Every {IntervalCount} years",
                BillingInterval.Custom => $"Every {CustomDays} days",
                _ => throw new SubscriptionDomainException($"Unsupported billing interval: {Interval}")
            };
        }

        public bool Equals(BillingCycle? other)
        {
            if (other is null) return false;
            return Interval == other.Interval && 
                   IntervalCount == other.IntervalCount && 
                   CustomDays == other.CustomDays;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as BillingCycle);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Interval, IntervalCount, CustomDays);
        }

        public static bool operator ==(BillingCycle? left, BillingCycle? right)
        {
            return EqualityComparer<BillingCycle>.Default.Equals(left, right);
        }

        public static bool operator !=(BillingCycle? left, BillingCycle? right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return GetDisplayName();
        }
    }
}
