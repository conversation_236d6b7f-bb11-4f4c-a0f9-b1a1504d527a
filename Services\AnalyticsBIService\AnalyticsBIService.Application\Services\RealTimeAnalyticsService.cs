using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Domain.Services;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for real-time analytics processing and monitoring
/// </summary>
public interface IRealTimeAnalyticsService
{
    Task ProcessRealTimeEventAsync(AnalyticsEvent analyticsEvent);
    Task<List<RealTimeKPIDto>> GetRealTimeKPIsAsync(Guid userId, UserRole userRole);
    Task<List<AlertDto>> GetActiveAlertsAsync(Guid userId, UserRole userRole);
    Task StartRealTimeMonitoringAsync();
    Task StopRealTimeMonitoringAsync();
    Task<RealTimeMetricsDto> GetRealTimeMetricsAsync(Guid userId, UserRole userRole);
}

public class RealTimeAnalyticsService : IRealTimeAnalyticsService
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IOrderDataRepository _orderDataRepository;
    private readonly ITripDataRepository _tripDataRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly INotificationService _notificationService;
    private readonly IThresholdMonitoringService _thresholdMonitoringService;
    private readonly ILogger<RealTimeAnalyticsService> _logger;

    // In-memory cache for real-time metrics
    private readonly ConcurrentDictionary<string, RealTimeMetricValue> _realTimeMetrics;
    private readonly ConcurrentDictionary<string, List<AlertDto>> _activeAlerts;

    private Timer? _monitoringTimer;
    private bool _isMonitoring;

    public RealTimeAnalyticsService(
        IAnalyticsEventRepository analyticsEventRepository,
        IMetricRepository metricRepository,
        IOrderDataRepository orderDataRepository,
        ITripDataRepository tripDataRepository,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        IThresholdMonitoringService thresholdMonitoringService,
        ILogger<RealTimeAnalyticsService> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _metricRepository = metricRepository;
        _orderDataRepository = orderDataRepository;
        _tripDataRepository = tripDataRepository;
        _alertRepository = alertRepository;
        _notificationService = notificationService;
        _thresholdMonitoringService = thresholdMonitoringService;
        _logger = logger;
        _realTimeMetrics = new ConcurrentDictionary<string, RealTimeMetricValue>();
        _activeAlerts = new ConcurrentDictionary<string, List<AlertDto>>();
    }

    public async Task ProcessRealTimeEventAsync(AnalyticsEvent analyticsEvent)
    {
        try
        {
            _logger.LogDebug("Processing real-time event: {EventName} for user {UserId}",
                analyticsEvent.EventName, analyticsEvent.UserId);

            // Update real-time metrics
            await UpdateRealTimeMetricsAsync(analyticsEvent);

            // Check thresholds and generate alerts
            await CheckThresholdsAsync(analyticsEvent);

            // Store event in database
            await _analyticsEventRepository.AddAsync(analyticsEvent);

            _logger.LogDebug("Successfully processed real-time event: {EventName}", analyticsEvent.EventName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing real-time event: {EventName}", analyticsEvent.EventName);
            throw;
        }
    }

    public async Task<List<RealTimeKPIDto>> GetRealTimeKPIsAsync(Guid userId, UserRole userRole)
    {
        try
        {
            var kpis = new List<RealTimeKPIDto>();

            switch (userRole)
            {
                case UserRole.Admin:
                    kpis = await GetAdminRealTimeKPIsAsync();
                    break;
                case UserRole.TransportCompany:
                    kpis = await GetTransportCompanyRealTimeKPIsAsync(userId);
                    break;
                case UserRole.Broker:
                    kpis = await GetBrokerRealTimeKPIsAsync(userId);
                    break;
                case UserRole.Carrier:
                    kpis = await GetCarrierRealTimeKPIsAsync(userId);
                    break;
                case UserRole.Shipper:
                    kpis = await GetShipperRealTimeKPIsAsync(userId);
                    break;
            }

            return kpis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time KPIs for user {UserId} with role {UserRole}", userId, userRole);
            throw;
        }
    }

    public async Task<List<AlertDto>> GetActiveAlertsAsync(Guid userId, UserRole userRole)
    {
        try
        {
            var userKey = $"{userId}_{userRole}";

            if (_activeAlerts.TryGetValue(userKey, out var alerts))
            {
                return alerts.Where(a => a.IsActive && !a.IsAcknowledged).ToList();
            }

            // If no cached alerts, get from database
            var dbAlerts = await _alertRepository.GetUnacknowledgedAlertsAsync(userId, 1, 50);

            var alertDtos = dbAlerts.Items.Select(a => new AlertDto
            {
                Id = a.Id,
                UserId = Guid.Empty, // Alert entity doesn't have UserId, using default
                AlertType = "Threshold", // Alert entity doesn't have AlertType, using default
                Severity = a.Severity.ToString(),
                Title = a.Name, // Using Name as Title
                Message = a.Message,
                MetricName = a.MetricName,
                CurrentValue = a.TriggerValue ?? 0, // Using TriggerValue as CurrentValue
                ThresholdValue = a.ThresholdValue,
                IsActive = a.IsActive,
                IsAcknowledged = a.IsAcknowledged,
                CreatedAt = a.CreatedAt,
                AcknowledgedAt = a.AcknowledgedAt
            }).ToList();

            _activeAlerts.TryAdd(userKey, alertDtos);
            return alertDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts for user {UserId}", userId);
            throw;
        }
    }

    public async Task<RealTimeMetricsDto> GetRealTimeMetricsAsync(Guid userId, UserRole userRole)
    {
        try
        {
            var metrics = new RealTimeMetricsDto
            {
                UserId = userId,
                UserRole = userRole.ToString(),
                Timestamp = DateTime.UtcNow,
                Metrics = new List<RealTimeMetricDto>()
            };

            var userKey = $"{userId}_{userRole}";
            var relevantMetrics = _realTimeMetrics
                .Where(kvp => kvp.Key.StartsWith(userKey))
                .ToList();

            foreach (var metric in relevantMetrics)
            {
                metrics.Metrics.Add(new RealTimeMetricDto
                {
                    MetricName = metric.Value.MetricName,
                    CurrentValue = metric.Value.CurrentValue,
                    PreviousValue = metric.Value.PreviousValue,
                    ChangePercentage = metric.Value.ChangePercentage,
                    Trend = metric.Value.Trend,
                    Unit = metric.Value.Unit,
                    LastUpdated = metric.Value.LastUpdated,
                    Status = metric.Value.Status
                });
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics for user {UserId}", userId);
            throw;
        }
    }

    public async Task StartRealTimeMonitoringAsync()
    {
        if (_isMonitoring)
        {
            _logger.LogWarning("Real-time monitoring is already running");
            return;
        }

        _logger.LogInformation("Starting real-time analytics monitoring");

        _isMonitoring = true;
        _monitoringTimer = new Timer(async _ => await MonitoringTickAsync(), null, TimeSpan.Zero, TimeSpan.FromSeconds(30));

        await Task.CompletedTask;
    }

    public async Task StopRealTimeMonitoringAsync()
    {
        if (!_isMonitoring)
        {
            _logger.LogWarning("Real-time monitoring is not running");
            return;
        }

        _logger.LogInformation("Stopping real-time analytics monitoring");

        _isMonitoring = false;
        _monitoringTimer?.Dispose();
        _monitoringTimer = null;

        await Task.CompletedTask;
    }

    private async Task UpdateRealTimeMetricsAsync(AnalyticsEvent analyticsEvent)
    {
        var userKey = $"{analyticsEvent.UserId}_{GetUserRoleFromEvent(analyticsEvent)}";
        var metricKey = $"{userKey}_{analyticsEvent.EventName}";

        var currentValue = ExtractMetricValueFromEvent(analyticsEvent);

        _realTimeMetrics.AddOrUpdate(metricKey,
            new RealTimeMetricValue
            {
                MetricName = analyticsEvent.EventName,
                CurrentValue = currentValue,
                PreviousValue = 0,
                ChangePercentage = 0,
                Trend = "Stable",
                Unit = GetMetricUnit(analyticsEvent.EventName),
                LastUpdated = DateTime.UtcNow,
                Status = "Normal"
            },
            (key, existing) =>
            {
                var changePercentage = existing.CurrentValue != 0
                    ? ((currentValue - existing.CurrentValue) / existing.CurrentValue) * 100
                    : 0;

                return new RealTimeMetricValue
                {
                    MetricName = analyticsEvent.EventName,
                    CurrentValue = currentValue,
                    PreviousValue = existing.CurrentValue,
                    ChangePercentage = changePercentage,
                    Trend = changePercentage > 5 ? "Increasing" : changePercentage < -5 ? "Decreasing" : "Stable",
                    Unit = existing.Unit,
                    LastUpdated = DateTime.UtcNow,
                    Status = DetermineMetricStatus(analyticsEvent.EventName, currentValue)
                };
            });
    }

    private async Task CheckThresholdsAsync(AnalyticsEvent analyticsEvent)
    {
        var thresholdViolations = await _thresholdMonitoringService.CheckThresholdsAsync(analyticsEvent);

        foreach (var violation in thresholdViolations)
        {
            await GenerateAlertAsync(violation);
        }
    }

    private async Task GenerateAlertAsync(ThresholdViolation violation)
    {
        var alert = new Alert(
            name: violation.Title,
            description: violation.Message,
            severity: violation.Severity,
            message: violation.Message,
            metricName: violation.MetricName,
            triggerValue: violation.CurrentValue,
            thresholdValue: violation.ThresholdValue
        );

        await _alertRepository.AddAsync(alert);

        // Add to active alerts cache
        var userKey = $"{violation.UserId}_{violation.UserRole}";
        var alertDto = new AlertDto
        {
            Id = alert.Id,
            UserId = violation.UserId, // Use violation.UserId since Alert doesn't have UserId
            AlertType = violation.AlertType.ToString(),
            Severity = alert.Severity.ToString(),
            Title = alert.Name, // Use alert.Name instead of Title
            Message = alert.Message,
            MetricName = alert.MetricName,
            CurrentValue = alert.TriggerValue ?? 0, // Use TriggerValue instead of CurrentValue
            ThresholdValue = alert.ThresholdValue ?? 0,
            IsActive = alert.IsActive,
            IsAcknowledged = alert.IsAcknowledged,
            CreatedAt = alert.TriggeredAt // Use TriggeredAt instead of CreatedAt
        };

        _activeAlerts.AddOrUpdate(userKey,
            new List<AlertDto> { alertDto },
            (key, existing) =>
            {
                existing.Add(alertDto);
                return existing.OrderByDescending(a => a.CreatedAt).Take(50).ToList();
            });

        // Send notification
        await _notificationService.SendAlertNotificationAsync(alertDto);
    }

    private async Task MonitoringTickAsync()
    {
        try
        {
            _logger.LogDebug("Running real-time monitoring tick");

            // Update aggregated metrics
            await UpdateAggregatedMetricsAsync();

            // Clean up old alerts
            await CleanupOldAlertsAsync();

            // Update cache
            await RefreshMetricsCacheAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during monitoring tick");
        }
    }

    private async Task UpdateAggregatedMetricsAsync()
    {
        // Update platform-wide metrics for admin users
        var platformMetrics = await CalculatePlatformMetricsAsync();

        foreach (var metric in platformMetrics)
        {
            var metricKey = $"platform_{metric.Key}";
            _realTimeMetrics.AddOrUpdate(metricKey, metric.Value, (key, existing) => metric.Value);
        }
    }

    private async Task CleanupOldAlertsAsync()
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-24);

        var oldAlerts = await _alertRepository.GetByDateRangeAsync(DateTime.MinValue, cutoffTime, 1, int.MaxValue);

        if (oldAlerts.Items.Any())
        {
            foreach (var alert in oldAlerts.Items)
            {
                await _alertRepository.DeleteAsync(alert.Id);
            }

            _logger.LogInformation("Cleaned up {Count} old alerts", oldAlerts.Items.Count());
        }
    }

    private async Task RefreshMetricsCacheAsync()
    {
        // Remove metrics older than 1 hour
        var cutoffTime = DateTime.UtcNow.AddHours(-1);
        var keysToRemove = _realTimeMetrics
            .Where(kvp => kvp.Value.LastUpdated < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in keysToRemove)
        {
            _realTimeMetrics.TryRemove(key, out _);
        }
    }

    // Helper methods for role-specific KPIs
    private async Task<List<RealTimeKPIDto>> GetAdminRealTimeKPIsAsync()
    {
        return new List<RealTimeKPIDto>
        {
            new() { KPIName = "Active Users", CurrentValue = GetMetricValue("platform_active_users", 1250), Unit = "count", Status = "Normal", Trend = "Increasing" },
            new() { KPIName = "Platform Revenue", CurrentValue = GetMetricValue("platform_revenue", 125000), Unit = "USD", Status = "Normal", Trend = "Increasing" },
            new() { KPIName = "System Performance", CurrentValue = GetMetricValue("platform_performance", 98.5m), Unit = "%", Status = "Good", Trend = "Stable" },
            new() { KPIName = "Active Transactions", CurrentValue = GetMetricValue("platform_transactions", 450), Unit = "count", Status = "Normal", Trend = "Increasing" }
        };
    }

    private async Task<List<RealTimeKPIDto>> GetTransportCompanyRealTimeKPIsAsync(Guid userId)
    {
        return new List<RealTimeKPIDto>
        {
            new() { KPIName = "Active RFQs", CurrentValue = GetMetricValue($"{userId}_active_rfqs", 25), Unit = "count", Status = "Normal", Trend = "Stable" },
            new() { KPIName = "Conversion Rate", CurrentValue = GetMetricValue($"{userId}_conversion_rate", 68.5m), Unit = "%", Status = "Good", Trend = "Increasing" },
            new() { KPIName = "Average Response Time", CurrentValue = GetMetricValue($"{userId}_response_time", 18.5m), Unit = "hours", Status = "Normal", Trend = "Decreasing" },
            new() { KPIName = "Daily Revenue", CurrentValue = GetMetricValue($"{userId}_daily_revenue", 15000), Unit = "USD", Status = "Normal", Trend = "Increasing" }
        };
    }

    private async Task<List<RealTimeKPIDto>> GetBrokerRealTimeKPIsAsync(Guid userId)
    {
        return new List<RealTimeKPIDto>
        {
            new() { KPIName = "Active Quotes", CurrentValue = GetMetricValue($"{userId}_active_quotes", 35), Unit = "count", Status = "Normal", Trend = "Stable" },
            new() { KPIName = "Quote Success Rate", CurrentValue = GetMetricValue($"{userId}_quote_success", 72.3m), Unit = "%", Status = "Good", Trend = "Increasing" },
            new() { KPIName = "Carrier Utilization", CurrentValue = GetMetricValue($"{userId}_carrier_utilization", 85.2m), Unit = "%", Status = "Good", Trend = "Stable" },
            new() { KPIName = "Daily Margin", CurrentValue = GetMetricValue($"{userId}_daily_margin", 8500), Unit = "USD", Status = "Normal", Trend = "Increasing" }
        };
    }

    private async Task<List<RealTimeKPIDto>> GetCarrierRealTimeKPIsAsync(Guid userId)
    {
        return new List<RealTimeKPIDto>
        {
            new() { KPIName = "Active Trips", CurrentValue = GetMetricValue($"{userId}_active_trips", 12), Unit = "count", Status = "Normal", Trend = "Stable" },
            new() { KPIName = "On-Time Delivery", CurrentValue = GetMetricValue($"{userId}_ontime_delivery", 94.2m), Unit = "%", Status = "Excellent", Trend = "Stable" },
            new() { KPIName = "Vehicle Utilization", CurrentValue = GetMetricValue($"{userId}_vehicle_utilization", 78.5m), Unit = "%", Status = "Good", Trend = "Increasing" },
            new() { KPIName = "Daily Earnings", CurrentValue = GetMetricValue($"{userId}_daily_earnings", 3200), Unit = "USD", Status = "Normal", Trend = "Increasing" }
        };
    }

    private async Task<List<RealTimeKPIDto>> GetShipperRealTimeKPIsAsync(Guid userId)
    {
        return new List<RealTimeKPIDto>
        {
            new() { KPIName = "Active Shipments", CurrentValue = GetMetricValue($"{userId}_active_shipments", 18), Unit = "count", Status = "Normal", Trend = "Stable" },
            new() { KPIName = "SLA Compliance", CurrentValue = GetMetricValue($"{userId}_sla_compliance", 92.8m), Unit = "%", Status = "Good", Trend = "Stable" },
            new() { KPIName = "Cost per Shipment", CurrentValue = GetMetricValue($"{userId}_cost_per_shipment", 2450), Unit = "USD", Status = "Normal", Trend = "Decreasing" },
            new() { KPIName = "Customer Satisfaction", CurrentValue = GetMetricValue($"{userId}_customer_satisfaction", 4.3m), Unit = "rating", Status = "Good", Trend = "Increasing" }
        };
    }

    // Helper methods
    private decimal GetMetricValue(string metricKey, decimal defaultValue)
    {
        return _realTimeMetrics.TryGetValue(metricKey, out var metric) ? metric.CurrentValue : defaultValue;
    }

    private UserRole GetUserRoleFromEvent(AnalyticsEvent analyticsEvent)
    {
        // This would be determined based on event context or user lookup
        return UserRole.TransportCompany; // Placeholder
    }

    private decimal ExtractMetricValueFromEvent(AnalyticsEvent analyticsEvent)
    {
        // Extract numeric value from event data
        if (analyticsEvent.Properties.ContainsKey("value"))
        {
            if (decimal.TryParse(analyticsEvent.Properties["value"]?.ToString(), out var value))
                return value;
        }
        return 1; // Default increment
    }

    private string GetMetricUnit(string eventName)
    {
        return eventName.ToLower() switch
        {
            var name when name.Contains("rate") || name.Contains("percentage") => "%",
            var name when name.Contains("time") => "hours",
            var name when name.Contains("cost") || name.Contains("revenue") || name.Contains("margin") => "USD",
            var name when name.Contains("count") || name.Contains("number") => "count",
            _ => "units"
        };
    }

    private string DetermineMetricStatus(string metricName, decimal value)
    {
        // Simple status determination logic
        return metricName.ToLower() switch
        {
            var name when name.Contains("rate") && value >= 90 => "Excellent",
            var name when name.Contains("rate") && value >= 80 => "Good",
            var name when name.Contains("rate") && value >= 70 => "Normal",
            var name when name.Contains("rate") && value < 70 => "Poor",
            _ => "Normal"
        };
    }

    private async Task<Dictionary<string, RealTimeMetricValue>> CalculatePlatformMetricsAsync()
    {
        // Calculate platform-wide metrics
        var metrics = new Dictionary<string, RealTimeMetricValue>();

        // This would calculate actual platform metrics from database
        metrics["active_users"] = new RealTimeMetricValue
        {
            MetricName = "Active Users",
            CurrentValue = 1250,
            Unit = "count",
            LastUpdated = DateTime.UtcNow,
            Status = "Normal"
        };

        return metrics;
    }
}

// Supporting classes
public class RealTimeMetricValue
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class ThresholdViolation
{
    public Guid UserId { get; set; }
    public UserRole UserRole { get; set; }
    public Domain.Enums.AlertType AlertType { get; set; }
    public AlertSeverity Severity { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal ThresholdValue { get; set; }
}
