﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.ValueObjects;

public class CommissionStructure : ValueObject
{
    public CommissionType Type { get; private set; }
    public decimal Rate { get; private set; }
    public decimal? MinimumAmount { get; private set; }
    public decimal? MaximumAmount { get; private set; }
    public string? Description { get; private set; }

    private CommissionStructure() { }

    public CommissionStructure(
        CommissionType type,
        decimal rate,
        decimal? minimumAmount = null,
        decimal? maximumAmount = null,
        string? description = null)
    {
        if (rate < 0)
            throw new ArgumentException("Rate cannot be negative");

        if (minimumAmount.HasValue && minimumAmount.Value < 0)
            throw new ArgumentException("Minimum amount cannot be negative");

        if (maximumAmount.HasValue && maximumAmount.Value < 0)
            throw new ArgumentException("Maximum amount cannot be negative");

        if (minimumAmount.HasValue && maximumAmount.HasValue && minimumAmount.Value > maximumAmount.Value)
            throw new ArgumentException("Minimum amount cannot be greater than maximum amount");

        Type = type;
        Rate = rate;
        MinimumAmount = minimumAmount;
        MaximumAmount = maximumAmount;
        Description = description;
    }

    public static CommissionStructure Percentage(decimal percentage, decimal? min = null, decimal? max = null, string? description = null)
    {
        if (percentage < 0 || percentage > 100)
            throw new ArgumentException("Percentage must be between 0 and 100");

        return new CommissionStructure(CommissionType.Percentage, percentage, min, max, description);
    }

    public static CommissionStructure FixedAmount(decimal amount, string? description = null)
    {
        if (amount <= 0)
            throw new ArgumentException("Fixed amount must be greater than zero");

        return new CommissionStructure(CommissionType.FixedAmount, amount, null, null, description);
    }

    public static CommissionStructure Tiered(decimal baseRate, decimal? min = null, decimal? max = null, string? description = null)
    {
        if (baseRate < 0)
            throw new ArgumentException("Base rate cannot be negative");

        return new CommissionStructure(CommissionType.Tiered, baseRate, min, max, description);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Type;
        yield return Rate;
        yield return MinimumAmount ?? 0;
        yield return MaximumAmount ?? 0;
        yield return Description ?? string.Empty;
    }
}


