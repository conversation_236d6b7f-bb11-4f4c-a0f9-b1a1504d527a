using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Carriers;

public class UpdateCarrierCommandHandler : IRequestHandler<UpdateCarrierCommand, CarrierDto>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public UpdateCarrierCommandHandler(
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<CarrierDto> Handle(UpdateCarrierCommand request, CancellationToken cancellationToken)
    {
        var carrier = await _carrierRepository.GetByIdAsync(request.CarrierId, cancellationToken);
        if (carrier == null)
        {
            throw new InvalidOperationException($"Carrier with ID {request.CarrierId} not found");
        }

        var dto = request.CarrierDto;

        // Update business address if provided
        Location? businessAddress = null;
        if (dto.BusinessAddress != null)
        {
            businessAddress = new Location(
                dto.BusinessAddress.Latitude,
                dto.BusinessAddress.Longitude,
                dto.BusinessAddress.Address,
                dto.BusinessAddress.City,
                dto.BusinessAddress.State,
                dto.BusinessAddress.Country,
                dto.BusinessAddress.PostalCode);
        }

        // Update carrier properties using reflection or manual assignment
        // For simplicity, we'll use manual assignment here
        var updatedCarrier = new Domain.Entities.Carrier(
            carrier.UserId,
            dto.CompanyName,
            dto.ContactPersonName,
            dto.Email,
            dto.PhoneNumber,
            dto.BusinessLicenseNumber,
            dto.TaxIdentificationNumber,
            businessAddress,
            dto.Notes);

        // Copy the ID and other properties
        typeof(Domain.Entities.Carrier).GetProperty("Id")?.SetValue(updatedCarrier, carrier.Id);
        
        _carrierRepository.Update(updatedCarrier);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("carrier.updated", new
        {
            CarrierId = updatedCarrier.Id,
            UserId = updatedCarrier.UserId,
            CompanyName = updatedCarrier.CompanyName,
            ContactPersonName = updatedCarrier.ContactPersonName,
            Email = updatedCarrier.Email,
            PhoneNumber = updatedCarrier.PhoneNumber,
            UpdatedAt = updatedCarrier.UpdatedAt
        }, cancellationToken);

        var result = _mapper.Map<CarrierDto>(updatedCarrier);
        return result;
    }
}
