using CommunicationNotification.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// User preferences response model
/// </summary>
public class UserPreferencesResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Preferred language
    /// </summary>
    public string PreferredLanguage { get; set; } = string.Empty;

    /// <summary>
    /// User's time zone
    /// </summary>
    public string TimeZone { get; set; } = string.Empty;

    /// <summary>
    /// Quiet hours start time
    /// </summary>
    public TimeOnly? QuietHoursStart { get; set; }

    /// <summary>
    /// Quiet hours end time
    /// </summary>
    public TimeOnly? QuietHoursEnd { get; set; }

    /// <summary>
    /// Whether quiet hours are enabled
    /// </summary>
    public bool EnableQuietHours { get; set; }

    /// <summary>
    /// Channel-specific preferences
    /// </summary>
    public Dictionary<string, ChannelPreferenceResponse> ChannelPreferences { get; set; } = new();

    /// <summary>
    /// Message type preferences
    /// </summary>
    public Dictionary<string, MessageTypePreferenceResponse> MessageTypePreferences { get; set; } = new();

    /// <summary>
    /// Global opt-out status
    /// </summary>
    public bool GlobalOptOut { get; set; }

    /// <summary>
    /// Marketing opt-out status
    /// </summary>
    public bool MarketingOptOut { get; set; }

    /// <summary>
    /// Last updated timestamp
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Channel preference response
/// </summary>
public class ChannelPreferenceResponse
{
    /// <summary>
    /// Whether the channel is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Channel priority (1-10, higher is more preferred)
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// Contact information for this channel
    /// </summary>
    public string ContactInfo { get; set; } = string.Empty;

    /// <summary>
    /// Channel-specific settings
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Message type preference response
/// </summary>
public class MessageTypePreferenceResponse
{
    /// <summary>
    /// Whether this message type is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Preferred channels for this message type
    /// </summary>
    public List<string> PreferredChannels { get; set; } = new();

    /// <summary>
    /// Whether confirmation is required for this message type
    /// </summary>
    public bool RequireConfirmation { get; set; }

    /// <summary>
    /// Message type specific settings
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Update user preferences request
/// </summary>
public class UpdateUserPreferencesRequest
{
    /// <summary>
    /// Preferred language
    /// </summary>
    [Required]
    public string PreferredLanguage { get; set; } = "English";

    /// <summary>
    /// User's time zone
    /// </summary>
    [Required]
    [StringLength(50)]
    public string TimeZone { get; set; } = "Asia/Kolkata";

    /// <summary>
    /// Quiet hours start time
    /// </summary>
    public TimeOnly? QuietHoursStart { get; set; }

    /// <summary>
    /// Quiet hours end time
    /// </summary>
    public TimeOnly? QuietHoursEnd { get; set; }

    /// <summary>
    /// Whether quiet hours are enabled
    /// </summary>
    public bool EnableQuietHours { get; set; } = false;

    /// <summary>
    /// Channel-specific preferences
    /// </summary>
    public Dictionary<string, UpdateChannelPreferenceRequest>? ChannelPreferences { get; set; }

    /// <summary>
    /// Message type preferences
    /// </summary>
    public Dictionary<string, UpdateMessageTypePreferenceRequest>? MessageTypePreferences { get; set; }

    /// <summary>
    /// Global opt-out status
    /// </summary>
    public bool GlobalOptOut { get; set; } = false;

    /// <summary>
    /// Marketing opt-out status
    /// </summary>
    public bool MarketingOptOut { get; set; } = false;
}

/// <summary>
/// Update channel preference request
/// </summary>
public class UpdateChannelPreferenceRequest
{
    /// <summary>
    /// Whether the channel is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Channel priority (1-10, higher is more preferred)
    /// </summary>
    [Range(1, 10)]
    public int Priority { get; set; } = 5;

    /// <summary>
    /// Contact information for this channel
    /// </summary>
    [StringLength(200)]
    public string? ContactInfo { get; set; }

    /// <summary>
    /// Channel-specific settings
    /// </summary>
    public Dictionary<string, object>? Settings { get; set; }
}

/// <summary>
/// Update message type preference request
/// </summary>
public class UpdateMessageTypePreferenceRequest
{
    /// <summary>
    /// Whether this message type is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Preferred channels for this message type
    /// </summary>
    public List<string>? PreferredChannels { get; set; }

    /// <summary>
    /// Whether confirmation is required for this message type
    /// </summary>
    public bool RequireConfirmation { get; set; } = false;

    /// <summary>
    /// Message type specific settings
    /// </summary>
    public Dictionary<string, object>? Settings { get; set; }
}

/// <summary>
/// Channel preference update response
/// </summary>
public class ChannelPreferenceUpdateResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Channel that was updated
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Whether the channel is enabled
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// Channel priority
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// Contact information
    /// </summary>
    public string? ContactInfo { get; set; }

    /// <summary>
    /// Update timestamp
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }
}

/// <summary>
/// Opt-out response
/// </summary>
public class OptOutResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Type of opt-out
    /// </summary>
    public string OptOutType { get; set; } = string.Empty;

    /// <summary>
    /// Opt-out timestamp
    /// </summary>
    public DateTime OptedOutAt { get; set; }

    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }
}

/// <summary>
/// Opt-in response
/// </summary>
public class OptInResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Type of opt-in
    /// </summary>
    public string OptInType { get; set; } = string.Empty;

    /// <summary>
    /// Opt-in timestamp
    /// </summary>
    public DateTime OptedInAt { get; set; }

    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }
}

/// <summary>
/// Opt-out status response
/// </summary>
public class OptOutStatusResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Global opt-out status
    /// </summary>
    public bool GlobalOptOut { get; set; }

    /// <summary>
    /// Marketing opt-out status
    /// </summary>
    public bool MarketingOptOut { get; set; }

    /// <summary>
    /// Channel-specific opt-out status
    /// </summary>
    public Dictionary<string, bool> ChannelOptOuts { get; set; } = new();

    /// <summary>
    /// Message type opt-out status
    /// </summary>
    public Dictionary<string, bool> MessageTypeOptOuts { get; set; } = new();

    /// <summary>
    /// Last opt-out date
    /// </summary>
    public DateTime? LastOptOutDate { get; set; }

    /// <summary>
    /// Last opt-in date
    /// </summary>
    public DateTime? LastOptInDate { get; set; }
}
