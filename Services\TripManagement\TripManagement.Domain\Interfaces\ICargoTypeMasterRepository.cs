using TripManagement.Domain.Entities;
using Shared.Domain.Common;

namespace TripManagement.Domain.Interfaces;

/// <summary>
/// Repository interface for CargoTypeMaster entity
/// </summary>
public interface ICargoTypeMasterRepository
{
    // Basic CRUD operations
    Task<CargoTypeMaster?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<CargoTypeMaster?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<IEnumerable<CargoTypeMaster>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<CargoTypeMaster>> GetActiveAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<CargoTypeMaster>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    
    // Pagination and filtering
    Task<(IEnumerable<CargoTypeMaster> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string? category = null, 
        bool? isActive = null,
        bool? requiresSpecialHandling = null,
        bool? isHazardous = null,
        bool? requiresTemperatureControl = null,
        CancellationToken cancellationToken = default);
    
    // Special handling queries
    Task<IEnumerable<CargoTypeMaster>> GetBySpecialHandlingAsync(
        bool requiresSpecialHandling,
        bool? isHazardous = null,
        bool? requiresTemperatureControl = null,
        CancellationToken cancellationToken = default);
    
    // Temperature-based queries
    Task<IEnumerable<CargoTypeMaster>> GetByTemperatureRangeAsync(
        decimal? minTemperature = null,
        decimal? maxTemperature = null,
        CancellationToken cancellationToken = default);
    
    // Sorting and ordering
    Task<IEnumerable<CargoTypeMaster>> GetOrderedBySortOrderAsync(CancellationToken cancellationToken = default);
    Task<int> GetMaxSortOrderAsync(CancellationToken cancellationToken = default);
    
    // Existence checks
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<bool> ExistsByCodeAsync(string code, Guid excludeId, CancellationToken cancellationToken = default);
    
    // Categories
    Task<IEnumerable<string>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetCategoryCountsAsync(CancellationToken cancellationToken = default);
    
    // Modification operations
    void Add(CargoTypeMaster cargoTypeMaster);
    void Update(CargoTypeMaster cargoTypeMaster);
    void Delete(CargoTypeMaster cargoTypeMaster);
    
    // Bulk operations
    Task<IEnumerable<CargoTypeMaster>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default);
    Task<IEnumerable<CargoTypeMaster>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default);
    
    // Analytics
    Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default);
    Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetUsageStatisticsAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetSpecialHandlingStatisticsAsync(CancellationToken cancellationToken = default);
}

