using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Queries.GetTripDetails;

public class GetTripDetailsQueryHandler : IRequestHandler<GetTripDetailsQuery, TripDto?>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTripDetailsQueryHandler> _logger;

    public GetTripDetailsQueryHandler(
        ITripRepository tripRepository,
        IMapper mapper,
        ILogger<GetTripDetailsQueryHandler> logger)
    {
        _tripRepository = tripRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<TripDto?> Handle(GetTripDetailsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting trip details for trip {TripId}", request.TripId);

        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        
        if (trip == null)
        {
            _logger.LogWarning("Trip {TripId} not found", request.TripId);
            return null;
        }

        var tripDto = _mapper.Map<TripDto>(trip);
        
        _logger.LogInformation("Successfully retrieved trip details for trip {TripId}", request.TripId);
        
        return tripDto;
    }
}
