using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class TaxRule : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Jurisdiction { get; private set; } // Country, State, City
    public string TaxType { get; private set; } // GST, VAT, Sales Tax, etc.
    public decimal Rate { get; private set; } // Tax rate as percentage
    public Money MinimumAmount { get; private set; }
    public Money MaximumAmount { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }
    public string? ProductCategory { get; private set; }
    public string? ServiceType { get; private set; }
    public TaxCalculationType CalculationType { get; private set; }
    public int Priority { get; private set; } // For rule precedence

    // Navigation properties
    private readonly List<TaxRuleCondition> _conditions = new();
    public IReadOnlyList<TaxRuleCondition> Conditions => _conditions.AsReadOnly();

    private TaxRule() { }

    public TaxRule(
        string name,
        string description,
        string jurisdiction,
        string taxType,
        decimal rate,
        Money minimumAmount,
        Money maximumAmount,
        DateTime effectiveFrom,
        TaxCalculationType calculationType,
        int priority = 0)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Tax rule name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(jurisdiction))
            throw new ArgumentException("Jurisdiction cannot be empty", nameof(jurisdiction));

        if (rate < 0 || rate > 100)
            throw new ArgumentException("Tax rate must be between 0 and 100", nameof(rate));

        if (minimumAmount.Currency != maximumAmount.Currency)
            throw new ArgumentException("Minimum and maximum amounts must have the same currency");

        Name = name;
        Description = description;
        Jurisdiction = jurisdiction;
        TaxType = taxType;
        Rate = rate;
        MinimumAmount = minimumAmount;
        MaximumAmount = maximumAmount;
        EffectiveFrom = effectiveFrom;
        CalculationType = calculationType;
        Priority = priority;
        IsActive = true;
    }

    public void AddCondition(string field, string @operator, string value, string? description = null)
    {
        var condition = new TaxRuleCondition(Id, field, @operator, value, description);
        _conditions.Add(condition);
    }

    public void RemoveCondition(Guid conditionId)
    {
        var condition = _conditions.FirstOrDefault(c => c.Id == conditionId);
        if (condition != null)
        {
            _conditions.Remove(condition);
        }
    }

    public void UpdateRate(decimal newRate)
    {
        if (newRate < 0 || newRate > 100)
            throw new ArgumentException("Tax rate must be between 0 and 100", nameof(newRate));

        Rate = newRate;
        SetUpdatedAt();
    }

    public void SetEffectivePeriod(DateTime effectiveFrom, DateTime? effectiveTo = null)
    {
        if (effectiveTo.HasValue && effectiveTo <= effectiveFrom)
            throw new ArgumentException("Effective to date must be after effective from date");

        EffectiveFrom = effectiveFrom;
        EffectiveTo = effectiveTo;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }

    public bool IsApplicable(DateTime date, string? productCategory = null, string? serviceType = null)
    {
        if (!IsActive)
            return false;

        if (date < EffectiveFrom)
            return false;

        if (EffectiveTo.HasValue && date > EffectiveTo.Value)
            return false;

        if (!string.IsNullOrEmpty(ProductCategory) && ProductCategory != productCategory)
            return false;

        if (!string.IsNullOrEmpty(ServiceType) && ServiceType != serviceType)
            return false;

        return true;
    }

    public bool EvaluateConditions(Dictionary<string, object> context)
    {
        if (!_conditions.Any())
            return true;

        return _conditions.All(condition => condition.Evaluate(context));
    }
}

public class TaxRuleCondition : BaseEntity
{
    public Guid TaxRuleId { get; private set; }
    public string Field { get; private set; }
    public string Operator { get; private set; } // equals, greater_than, less_than, contains, etc.
    public string Value { get; private set; }
    public string? Description { get; private set; }

    private TaxRuleCondition() { }

    public TaxRuleCondition(Guid taxRuleId, string field, string @operator, string value, string? description = null)
    {
        if (string.IsNullOrWhiteSpace(field))
            throw new ArgumentException("Field cannot be empty", nameof(field));

        if (string.IsNullOrWhiteSpace(@operator))
            throw new ArgumentException("Operator cannot be empty", nameof(@operator));

        TaxRuleId = taxRuleId;
        Field = field;
        Operator = @operator;
        Value = value;
        Description = description;
    }

    public bool Evaluate(Dictionary<string, object> context)
    {
        if (!context.TryGetValue(Field, out var contextValue))
            return false;

        return Operator.ToLowerInvariant() switch
        {
            "equals" => contextValue?.ToString() == Value,
            "not_equals" => contextValue?.ToString() != Value,
            "greater_than" => decimal.TryParse(contextValue?.ToString(), out var gtVal) &&
                             decimal.TryParse(Value, out var gtTarget) && gtVal > gtTarget,
            "less_than" => decimal.TryParse(contextValue?.ToString(), out var ltVal) &&
                          decimal.TryParse(Value, out var ltTarget) && ltVal < ltTarget,
            "greater_equal" => decimal.TryParse(contextValue?.ToString(), out var geVal) &&
                              decimal.TryParse(Value, out var geTarget) && geVal >= geTarget,
            "less_equal" => decimal.TryParse(contextValue?.ToString(), out var leVal) &&
                           decimal.TryParse(Value, out var leTarget) && leVal <= leTarget,
            "contains" => contextValue?.ToString()?.Contains(Value, StringComparison.OrdinalIgnoreCase) == true,
            "starts_with" => contextValue?.ToString()?.StartsWith(Value, StringComparison.OrdinalIgnoreCase) == true,
            "ends_with" => contextValue?.ToString()?.EndsWith(Value, StringComparison.OrdinalIgnoreCase) == true,
            _ => false
        };
    }
}

public enum TaxCalculationType
{
    Percentage = 1,
    FixedAmount = 2,
    Tiered = 3,
    Compound = 4
}


