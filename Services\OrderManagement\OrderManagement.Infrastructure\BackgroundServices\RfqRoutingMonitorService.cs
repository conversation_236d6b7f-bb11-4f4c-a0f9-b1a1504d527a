using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Infrastructure.BackgroundServices;

public class RfqRoutingMonitorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RfqRoutingMonitorService> _logger;
    private readonly RfqRoutingMonitorOptions _options;

    public RfqRoutingMonitorService(
        IServiceProvider serviceProvider,
        ILogger<RfqRoutingMonitorService> logger,
        IOptions<RfqRoutingMonitorOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("RFQ Routing Monitor Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorRoutingFailures(stoppingToken);
                await MonitorNoResponseRfqs(stoppingToken);
                await MonitorLowBidCountRfqs(stoppingToken);
                await MonitorExpiredRoutings(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during RFQ routing monitoring");
            }

            await Task.Delay(TimeSpan.FromMinutes(_options.MonitoringIntervalMinutes), stoppingToken);
        }

        _logger.LogInformation("RFQ Routing Monitor Service stopped");
    }

    private async Task MonitorRoutingFailures(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var routingHistoryRepository = scope.ServiceProvider.GetRequiredService<IRfqRoutingHistoryRepository>();
        var rfqRepository = scope.ServiceProvider.GetRequiredService<IRfqRepository>();
        var tagRepository = scope.ServiceProvider.GetRequiredService<IRfqTagRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // Get failed routings that haven't been tagged yet
            var failedRoutings = await routingHistoryRepository.GetByStatusAsync(RfqRoutingStatus.Failed, cancellationToken);

            foreach (var routing in failedRoutings)
            {
                var rfq = await rfqRepository.GetByIdAsync(routing.RfqId, cancellationToken);
                if (rfq == null) continue;

                // Check if already tagged for routing failure
                if (rfq.HasActiveTag(RfqTagType.RoutingFailure)) continue;

                // Create routing failure tag
                var metadata = new Dictionary<string, object>
                {
                    ["routingHistoryId"] = routing.Id,
                    ["failureReason"] = routing.FailureReason ?? "Unknown",
                    ["routedToEntityId"] = routing.ToEntityId?.ToString() ?? "",
                    ["routedToEntityName"] = routing.ToEntityName ?? "",
                    ["routedToEntityType"] = routing.ToEntityType?.ToString() ?? "",
                    ["routedAt"] = routing.RoutedAt,
                    ["sequenceNumber"] = routing.SequenceNumber
                };

                var tag = RfqTag.CreateRoutingFailureTag(
                    routing.RfqId,
                    routing.FailureReason ?? "Routing failed",
                    metadata);

                rfq.AddTag(tag);
                rfqRepository.Update(rfq);

                _logger.LogInformation("Applied routing failure tag to RFQ {RfqId}", routing.RfqId);
            }

            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring routing failures");
        }
    }

    private async Task MonitorNoResponseRfqs(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var rfqRepository = scope.ServiceProvider.GetRequiredService<IRfqRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // Get published RFQs that haven't received bids in the specified time
            var cutoffTime = DateTime.UtcNow.AddHours(-_options.NoResponseThresholdHours);
            var rfqsWithoutResponse = await rfqRepository.GetRfqsWithoutBidsAsync(cutoffTime, cancellationToken);

            foreach (var rfq in rfqsWithoutResponse)
            {
                // Check if already tagged for no response
                if (rfq.HasActiveTag(RfqTagType.NoResponse)) continue;

                var hoursWithoutResponse = (int)(DateTime.UtcNow - rfq.CreatedAt).TotalHours;
                var metadata = new Dictionary<string, object>
                {
                    ["hoursWithoutResponse"] = hoursWithoutResponse,
                    ["publishedAt"] = rfq.CreatedAt,
                    ["currentBidCount"] = rfq.Bids.Count
                };

                var tag = RfqTag.CreateNoResponseTag(
                    rfq.Id,
                    hoursWithoutResponse,
                    metadata);

                rfq.AddTag(tag);
                rfqRepository.Update(rfq);

                _logger.LogInformation("Applied no response tag to RFQ {RfqId} after {Hours} hours", rfq.Id, hoursWithoutResponse);
            }

            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring no response RFQs");
        }
    }

    private async Task MonitorLowBidCountRfqs(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var rfqRepository = scope.ServiceProvider.GetRequiredService<IRfqRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // Get RFQs with low bid counts
            var cutoffTime = DateTime.UtcNow.AddHours(-_options.LowBidCountCheckHours);
            var rfqsWithLowBids = await rfqRepository.GetRfqsWithLowBidCountAsync(
                _options.MinimumExpectedBids, cutoffTime, cancellationToken);

            foreach (var rfq in rfqsWithLowBids)
            {
                // Check if already tagged for low bid count
                if (rfq.HasActiveTag(RfqTagType.LowBidCount)) continue;

                var metadata = new Dictionary<string, object>
                {
                    ["currentBidCount"] = rfq.Bids.Count,
                    ["expectedMinimum"] = _options.MinimumExpectedBids,
                    ["publishedAt"] = rfq.CreatedAt,
                    ["hoursPublished"] = (int)(DateTime.UtcNow - rfq.CreatedAt).TotalHours
                };

                var tag = RfqTag.CreateLowBidCountTag(
                    rfq.Id,
                    rfq.Bids.Count,
                    _options.MinimumExpectedBids,
                    metadata);

                rfq.AddTag(tag);
                rfqRepository.Update(rfq);

                _logger.LogInformation("Applied low bid count tag to RFQ {RfqId} with {BidCount} bids", 
                    rfq.Id, rfq.Bids.Count);
            }

            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring low bid count RFQs");
        }
    }

    private async Task MonitorExpiredRoutings(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var routingHistoryRepository = scope.ServiceProvider.GetRequiredService<IRfqRoutingHistoryRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // Get expired routings
            var expiredRoutings = await routingHistoryRepository.GetExpiredRoutingsAsync(cancellationToken);

            foreach (var routing in expiredRoutings)
            {
                routing.MarkAsExpired();
                routingHistoryRepository.Update(routing);

                _logger.LogInformation("Marked routing {RoutingId} as expired for RFQ {RfqId}", 
                    routing.Id, routing.RfqId);
            }

            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring expired routings");
        }
    }
}

public class RfqRoutingMonitorOptions
{
    public const string SectionName = "RfqRoutingMonitor";

    public int MonitoringIntervalMinutes { get; set; } = 30;
    public int NoResponseThresholdHours { get; set; } = 24;
    public int LowBidCountCheckHours { get; set; } = 48;
    public int MinimumExpectedBids { get; set; } = 3;
}
