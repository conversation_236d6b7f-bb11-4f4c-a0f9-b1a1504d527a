namespace MonitoringObservability.Domain.Entities;

/// <summary>
/// Represents an event within a distributed trace
/// </summary>
public class TraceEvent
{
    public TraceEvent(string traceId, string eventName, string message, 
        Dictionary<string, object>? metadata = null)
    {
        Id = Guid.NewGuid();
        TraceId = traceId ?? throw new ArgumentNullException(nameof(traceId));
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        Message = message ?? throw new ArgumentNullException(nameof(message));
        Metadata = metadata ?? new Dictionary<string, object>();
        Timestamp = DateTime.UtcNow;
    }

    // Required for EF Core
    private TraceEvent() { }

    public Guid Id { get; private set; }
    public string TraceId { get; private set; } = string.Empty;
    public string EventName { get; private set; } = string.Empty;
    public string Message { get; private set; } = string.Empty;
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public Trace Trace { get; private set; } = null!;
}

/// <summary>
/// Represents an event within a span
/// </summary>
public class SpanEvent
{
    public SpanEvent(string spanId, string eventName, string message, 
        Dictionary<string, object>? metadata = null)
    {
        Id = Guid.NewGuid();
        SpanId = spanId ?? throw new ArgumentNullException(nameof(spanId));
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        Message = message ?? throw new ArgumentNullException(nameof(message));
        Metadata = metadata ?? new Dictionary<string, object>();
        Timestamp = DateTime.UtcNow;
    }

    // Required for EF Core
    private SpanEvent() { }

    public Guid Id { get; private set; }
    public string SpanId { get; private set; } = string.Empty;
    public string EventName { get; private set; } = string.Empty;
    public string Message { get; private set; } = string.Empty;
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public Span Span { get; private set; } = null!;
}

/// <summary>
/// Represents a log entry within a span
/// </summary>
public class SpanLog
{
    public SpanLog(string spanId, string level, string message, 
        Dictionary<string, object>? fields = null)
    {
        Id = Guid.NewGuid();
        SpanId = spanId ?? throw new ArgumentNullException(nameof(spanId));
        Level = level ?? throw new ArgumentNullException(nameof(level));
        Message = message ?? throw new ArgumentNullException(nameof(message));
        Fields = fields ?? new Dictionary<string, object>();
        Timestamp = DateTime.UtcNow;
    }

    // Required for EF Core
    private SpanLog() { }

    public Guid Id { get; private set; }
    public string SpanId { get; private set; } = string.Empty;
    public string Level { get; private set; } = string.Empty;
    public string Message { get; private set; } = string.Empty;
    public DateTime Timestamp { get; private set; }
    public Dictionary<string, object> Fields { get; private set; } = new();

    // Navigation properties
    public Span Span { get; private set; } = null!;
}

/// <summary>
/// Represents service dependency information extracted from traces
/// </summary>
public class ServiceDependency
{
    public ServiceDependency(string fromService, string toService, string operationType,
        int callCount = 1, TimeSpan? averageLatency = null)
    {
        Id = Guid.NewGuid();
        FromService = fromService ?? throw new ArgumentNullException(nameof(fromService));
        ToService = toService ?? throw new ArgumentNullException(nameof(toService));
        OperationType = operationType ?? throw new ArgumentNullException(nameof(operationType));
        CallCount = callCount;
        AverageLatency = averageLatency;
        FirstSeen = DateTime.UtcNow;
        LastSeen = DateTime.UtcNow;
    }

    // Required for EF Core
    private ServiceDependency() { }

    public Guid Id { get; private set; }
    public string FromService { get; private set; } = string.Empty;
    public string ToService { get; private set; } = string.Empty;
    public string OperationType { get; private set; } = string.Empty;
    public int CallCount { get; private set; }
    public TimeSpan? AverageLatency { get; private set; }
    public DateTime FirstSeen { get; private set; }
    public DateTime LastSeen { get; private set; }
    public double ErrorRate { get; private set; }
    public int ErrorCount { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    public void UpdateStatistics(TimeSpan latency, bool hasError)
    {
        CallCount++;
        LastSeen = DateTime.UtcNow;

        // Update average latency
        if (AverageLatency.HasValue)
        {
            var totalMs = (AverageLatency.Value.TotalMilliseconds * (CallCount - 1)) + latency.TotalMilliseconds;
            AverageLatency = TimeSpan.FromMilliseconds(totalMs / CallCount);
        }
        else
        {
            AverageLatency = latency;
        }

        // Update error statistics
        if (hasError)
        {
            ErrorCount++;
        }
        ErrorRate = (double)ErrorCount / CallCount;
    }
}
