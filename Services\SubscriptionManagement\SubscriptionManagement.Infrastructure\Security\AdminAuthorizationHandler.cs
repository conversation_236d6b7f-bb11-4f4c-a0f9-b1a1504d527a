using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace SubscriptionManagement.Infrastructure.Security
{
    public class AdminAuthorizationHandler : AuthorizationHandler<AdminRequirement>
    {
        private readonly ILogger<AdminAuthorizationHandler> _logger;

        public AdminAuthorizationHandler(ILogger<AdminAuthorizationHandler> logger)
        {
            _logger = logger;
        }

        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            AdminRequirement requirement)
        {
            var user = context.User;
            
            if (user?.Identity?.IsAuthenticated != true)
            {
                _logger.LogWarning("User is not authenticated for admin requirement");
                context.Fail();
                return Task.CompletedTask;
            }

            // Check for admin role
            if (user.IsInRole("Admin") || user.IsInRole("SuperAdmin"))
            {
                _logger.LogInformation("User {UserId} authorized as admin", GetUserId(user));
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // Check for admin claim
            if (user.HasClaim("role", "Admin") || user.HasClaim("role", "SuperAdmin"))
            {
                _logger.LogInformation("User {UserId} authorized as admin via claims", GetUserId(user));
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // Check for specific admin permissions
            if (user.HasClaim("permission", "admin.notifications") || 
                user.HasClaim("permission", "admin.payments") ||
                user.HasClaim("permission", "admin.subscriptions"))
            {
                _logger.LogInformation("User {UserId} authorized via admin permissions", GetUserId(user));
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            _logger.LogWarning("User {UserId} does not have admin privileges", GetUserId(user));
            context.Fail();
            return Task.CompletedTask;
        }

        private string GetUserId(ClaimsPrincipal user)
        {
            return user.FindFirst("sub")?.Value ?? 
                   user.FindFirst("userId")?.Value ?? 
                   user.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
                   "unknown";
        }
    }

    public class AdminRequirement : IAuthorizationRequirement
    {
        public string RequiredRole { get; } = "Admin";
    }
}
