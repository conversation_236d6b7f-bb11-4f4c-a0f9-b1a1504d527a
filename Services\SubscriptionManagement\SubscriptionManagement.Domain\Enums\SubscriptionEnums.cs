namespace SubscriptionManagement.Domain.Enums
{
    public enum SubscriptionStatus
    {
        Pending = 0,
        Active = 1,
        Suspended = 2,
        Cancelled = 3,
        Expired = 4,
        PendingCancellation = 5,
        PendingUpgrade = 6,
        PendingDowngrade = 7
    }

    public enum PlanType
    {
        Basic = 0,
        Pro = 1,
        Enterprise = 2
    }

    public enum UserType
    {
        TransportCompany = 0,
        Broker = 1,
        Carrier = 2
    }

    public enum BillingInterval
    {
        Weekly = 0,
        BiWeekly = 1,
        Monthly = 2,
        Quarterly = 3,
        SemiAnnually = 4,
        Annually = 5,
        Custom = 6
    }

    public enum PaymentStatus
    {
        Pending = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4,
        Refunded = 5,
        PartiallyRefunded = 6
    }

    public enum FeatureType
    {
        RfqLimit = 0,
        VehicleLimit = 1,
        CarrierLimit = 2,
        Analytics = 3,
        PrioritySupport = 4,
        ApiAccess = 5,
        CustomBranding = 6,
        WhiteLabel = 7,
        RouteOptimization = 8,
        MarginAnalysis = 9,
        CustomPricing = 10,
        DedicatedSupport = 11,
        Integrations = 12,
        VolumeDiscounts = 13
    }

    public enum FeatureAccessType
    {
        Boolean = 0,    // Feature is enabled/disabled
        Limit = 1,      // Feature has a usage limit
        Unlimited = 2   // Feature has unlimited usage
    }

    public enum InvoiceStatus
    {
        Draft = 0,
        Pending = 1,
        Sent = 2,
        Paid = 3,
        Overdue = 4,
        Cancelled = 5,
        Refunded = 6
    }

    public enum ProrationMode
    {
        None = 0,
        CreateProrations = 1,
        AlwaysInvoice = 2
    }

    public enum DiscountType
    {
        Percentage = 0,
        FixedAmount = 1
    }

    public enum CouponStatus
    {
        Active = 0,
        Inactive = 1,
        Expired = 2,
        UsageLimitReached = 3
    }

    public enum PaymentProofStatus
    {
        Pending = 0,
        UnderReview = 1,
        Verified = 2,
        Rejected = 3,
        RequiresAdditionalInfo = 4
    }

    public enum NotificationType
    {
        SubscriptionReminder = 0,
        PaymentThankYou = 1,
        ExpiryWarning = 2,
        PaymentFailed = 3,
        SubscriptionActivated = 4,
        SubscriptionCancelled = 5,
        SubscriptionUpgraded = 6,
        SubscriptionDowngraded = 7,
        PaymentProofReceived = 8,
        PaymentProofVerified = 9,
        PaymentProofRejected = 10,
        Custom = 99
    }
}
