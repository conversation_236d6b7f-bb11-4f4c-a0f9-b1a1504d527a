using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using Shared.Messaging.Events;

namespace Identity.Application.Auth.Commands.ChangePassword
{
    public class ChangePasswordCommandHandler : IRequestHandler<ChangePasswordCommand, Unit>
    {
        private readonly IUserRepository _userRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ChangePasswordCommandHandler> _logger;

        public ChangePasswordCommandHandler(
            IUserRepository userRepository,
            IPasswordHasher passwordHasher,
            IMessageBroker messageBroker,
            ILogger<ChangePasswordCommandHandler> logger)
        {
            _userRepository = userRepository;
            _passwordHasher = passwordHasher;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Unit> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            // Verify current password
            if (!_passwordHasher.VerifyPassword(user.PasswordHash, request.CurrentPassword))
            {
                throw new ApplicationException("Current password is incorrect");
            }

            // Hash the new password
            var newPasswordHash = _passwordHasher.HashPassword(request.NewPassword);

            // Update the password
            user.SetPasswordHash(newPasswordHash);
            await _userRepository.UpdateAsync(user);

            // Publish password changed event
            await _messageBroker.PublishAsync("user.password.changed", new UserPasswordChangedEvent
            {
                UserId = user.Id,
                ChangedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Password changed for user {UserId}", user.Id);

            return Unit.Value;
        }
    }
}
