using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetMyRfqs;

public class GetMyRfqsQuery : IRequest<PagedResult<RfqSummaryDto>>
{
    public Guid TransportCompanyId { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
