namespace MonitoringObservability.Domain.Exceptions;

public abstract class MonitoringDomainException : Exception
{
    protected MonitoringDomainException(string message) : base(message) { }
    protected MonitoringDomainException(string message, Exception innerException) : base(message, innerException) { }
}

// Alert exceptions
public class AlertNotFoundException : MonitoringDomainException
{
    public AlertNotFoundException(Guid alertId) 
        : base($"Alert with ID '{alertId}' was not found.")
    {
    }
}

public class AlertInvalidStateException : MonitoringDomainException
{
    public AlertInvalidStateException(Guid alertId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on alert '{alertId}' in state '{currentState}'.")
    {
    }
}

public class AlertAlreadyAcknowledgedException : MonitoringDomainException
{
    public AlertAlreadyAcknowledgedException(Guid alertId)
        : base($"Alert '{alertId}' has already been acknowledged.")
    {
    }
}

public class AlertAlreadyResolvedException : MonitoringDomainException
{
    public AlertAlreadyResolvedException(Guid alertId)
        : base($"Alert '{alertId}' has already been resolved.")
    {
    }
}

// Metric exceptions
public class MetricNotFoundException : MonitoringDomainException
{
    public MetricNotFoundException(string metricName, string serviceName)
        : base($"Metric '{metricName}' for service '{serviceName}' was not found.")
    {
    }

    public MetricNotFoundException(Guid metricId)
        : base($"Metric with ID '{metricId}' was not found.")
    {
    }
}

public class MetricAlreadyExistsException : MonitoringDomainException
{
    public MetricAlreadyExistsException(string metricName, string serviceName)
        : base($"Metric '{metricName}' already exists for service '{serviceName}'.")
    {
    }
}

public class InvalidMetricValueException : MonitoringDomainException
{
    public InvalidMetricValueException(string metricName, double value, string reason)
        : base($"Invalid value '{value}' for metric '{metricName}': {reason}")
    {
    }
}

public class MetricThresholdException : MonitoringDomainException
{
    public MetricThresholdException(string metricName, string reason)
        : base($"Invalid threshold configuration for metric '{metricName}': {reason}")
    {
    }
}

// Health check exceptions
public class HealthCheckNotFoundException : MonitoringDomainException
{
    public HealthCheckNotFoundException(Guid healthCheckId)
        : base($"Health check with ID '{healthCheckId}' was not found.")
    {
    }

    public HealthCheckNotFoundException(string name, string serviceName)
        : base($"Health check '{name}' for service '{serviceName}' was not found.")
    {
    }
}

public class HealthCheckExecutionException : MonitoringDomainException
{
    public HealthCheckExecutionException(string healthCheckName, string reason)
        : base($"Failed to execute health check '{healthCheckName}': {reason}")
    {
    }

    public HealthCheckExecutionException(string healthCheckName, Exception innerException)
        : base($"Failed to execute health check '{healthCheckName}'", innerException)
    {
    }
}

public class HealthCheckTimeoutException : MonitoringDomainException
{
    public HealthCheckTimeoutException(string healthCheckName, TimeSpan timeout)
        : base($"Health check '{healthCheckName}' timed out after {timeout.TotalSeconds} seconds.")
    {
    }
}

// Incident exceptions
public class IncidentNotFoundException : MonitoringDomainException
{
    public IncidentNotFoundException(Guid incidentId)
        : base($"Incident with ID '{incidentId}' was not found.")
    {
    }
}

public class IncidentInvalidStateException : MonitoringDomainException
{
    public IncidentInvalidStateException(Guid incidentId, string currentState, string attemptedAction)
        : base($"Cannot perform action '{attemptedAction}' on incident '{incidentId}' in state '{currentState}'.")
    {
    }
}

public class IncidentAlreadyResolvedException : MonitoringDomainException
{
    public IncidentAlreadyResolvedException(Guid incidentId)
        : base($"Incident '{incidentId}' has already been resolved.")
    {
    }
}

public class IncidentAlreadyClosedException : MonitoringDomainException
{
    public IncidentAlreadyClosedException(Guid incidentId)
        : base($"Incident '{incidentId}' has already been closed.")
    {
    }
}

// Notification exceptions
public class NotificationDeliveryException : MonitoringDomainException
{
    public NotificationDeliveryException(string channel, string recipient, string reason)
        : base($"Failed to deliver notification via '{channel}' to '{recipient}': {reason}")
    {
    }

    public NotificationDeliveryException(string channel, string recipient, Exception innerException)
        : base($"Failed to deliver notification via '{channel}' to '{recipient}'", innerException)
    {
    }
}

public class InvalidNotificationChannelException : MonitoringDomainException
{
    public InvalidNotificationChannelException(string channel)
        : base($"Invalid or unsupported notification channel: '{channel}'")
    {
    }
}

public class NotificationTemplateException : MonitoringDomainException
{
    public NotificationTemplateException(string templateName, string reason)
        : base($"Error processing notification template '{templateName}': {reason}")
    {
    }
}

// Service exceptions
public class ServiceNotFoundException : MonitoringDomainException
{
    public ServiceNotFoundException(string serviceName)
        : base($"Service '{serviceName}' was not found.")
    {
    }
}

public class ServiceUnavailableException : MonitoringDomainException
{
    public ServiceUnavailableException(string serviceName, string reason)
        : base($"Service '{serviceName}' is unavailable: {reason}")
    {
    }
}

// Configuration exceptions
public class InvalidConfigurationException : MonitoringDomainException
{
    public InvalidConfigurationException(string configurationName, string reason)
        : base($"Invalid configuration for '{configurationName}': {reason}")
    {
    }
}

public class MissingConfigurationException : MonitoringDomainException
{
    public MissingConfigurationException(string configurationName)
        : base($"Required configuration '{configurationName}' is missing.")
    {
    }
}

// Data exceptions
public class DataRetentionException : MonitoringDomainException
{
    public DataRetentionException(string dataType, string reason)
        : base($"Data retention error for '{dataType}': {reason}")
    {
    }
}

public class DataAggregationException : MonitoringDomainException
{
    public DataAggregationException(string operation, string reason)
        : base($"Data aggregation error during '{operation}': {reason}")
    {
    }
}

// Threshold and alerting exceptions
public class ThresholdEvaluationException : MonitoringDomainException
{
    public ThresholdEvaluationException(string metricName, string reason)
        : base($"Failed to evaluate threshold for metric '{metricName}': {reason}")
    {
    }
}

public class AlertEscalationException : MonitoringDomainException
{
    public AlertEscalationException(Guid alertId, string reason)
        : base($"Failed to escalate alert '{alertId}': {reason}")
    {
    }
}

// Integration exceptions
public class ExternalServiceException : MonitoringDomainException
{
    public ExternalServiceException(string serviceName, string operation, string reason)
        : base($"External service '{serviceName}' failed during '{operation}': {reason}")
    {
    }

    public ExternalServiceException(string serviceName, string operation, Exception innerException)
        : base($"External service '{serviceName}' failed during '{operation}'", innerException)
    {
    }
}

public class MonitoringIntegrationException : MonitoringDomainException
{
    public MonitoringIntegrationException(string integration, string reason)
        : base($"Monitoring integration '{integration}' failed: {reason}")
    {
    }
}
