using Xunit;
using FluentAssertions;

namespace AnalyticsBIService.Tests.UnitTests;

public class BasicTests
{
    [Fact]
    public void BasicTest_ShouldPass()
    {
        // Arrange
        var expected = 42;
        
        // Act
        var actual = 42;
        
        // Assert
        actual.Should().Be(expected);
    }

    [Theory]
    [InlineData(1, 2, 3)]
    [InlineData(5, 5, 10)]
    [InlineData(-1, 1, 0)]
    public void Add_ShouldReturnCorrectSum(int a, int b, int expected)
    {
        // Act
        var result = a + b;
        
        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void String_ShouldNotBeNullOrEmpty()
    {
        // Arrange
        var testString = "Hello World";
        
        // Assert
        testString.Should().NotBeNullOrEmpty();
        testString.Should().Contain("World");
    }
}
