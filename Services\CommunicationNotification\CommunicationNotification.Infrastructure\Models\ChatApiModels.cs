using CommunicationNotification.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// Create chat conversation request
/// </summary>
public class CreateChatConversationRequest
{
    /// <summary>
    /// Conversation title
    /// </summary>
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Conversation description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Type of conversation
    /// </summary>
    [Required]
    public string ConversationType { get; set; } = "General";

    /// <summary>
    /// Initial participant user IDs
    /// </summary>
    [Required]
    [MinLength(1)]
    public List<Guid> ParticipantIds { get; set; } = new();

    /// <summary>
    /// Associated trip ID (optional)
    /// </summary>
    public Guid? TripId { get; set; }

    /// <summary>
    /// Associated order ID (optional)
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// Whether the conversation is private
    /// </summary>
    public bool IsPrivate { get; set; } = false;

    /// <summary>
    /// Whether file sharing is allowed
    /// </summary>
    public bool AllowFileSharing { get; set; } = true;

    /// <summary>
    /// Maximum number of participants
    /// </summary>
    [Range(2, 100)]
    public int MaxParticipants { get; set; } = 10;

    /// <summary>
    /// Conversation tags
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Chat conversation response
/// </summary>
public class ChatConversationResponse
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Conversation description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Type of conversation
    /// </summary>
    public string ConversationType { get; set; } = string.Empty;

    /// <summary>
    /// Number of participants
    /// </summary>
    public int ParticipantCount { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Created timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Whether the conversation is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    public DateTime? LastActivity { get; set; }

    /// <summary>
    /// Associated trip ID
    /// </summary>
    public Guid? TripId { get; set; }

    /// <summary>
    /// Associated order ID
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// Conversation tags
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Detailed chat conversation response
/// </summary>
public class ChatConversationDetailResponse : ChatConversationResponse
{
    /// <summary>
    /// Conversation participants
    /// </summary>
    public List<ChatParticipantResponse> Participants { get; set; } = new();

    /// <summary>
    /// Recent messages
    /// </summary>
    public List<ChatMessageResponse> RecentMessages { get; set; } = new();
}

/// <summary>
/// Chat participant response
/// </summary>
public class ChatParticipantResponse
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Participant role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Joined timestamp
    /// </summary>
    public DateTime JoinedAt { get; set; }

    /// <summary>
    /// Whether the user is currently online
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// Last seen timestamp
    /// </summary>
    public DateTime? LastSeen { get; set; }

    /// <summary>
    /// Whether the participant can send messages
    /// </summary>
    public bool CanSendMessages { get; set; }

    /// <summary>
    /// Whether the participant can manage other participants
    /// </summary>
    public bool CanManageParticipants { get; set; }
}

/// <summary>
/// Send chat message request
/// </summary>
public class SendChatMessageRequest
{
    /// <summary>
    /// Message content
    /// </summary>
    [Required]
    [StringLength(4000, MinimumLength = 1)]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    [Required]
    public string MessageType { get; set; } = "Text";

    /// <summary>
    /// Reply to message ID (optional)
    /// </summary>
    public Guid? ReplyToMessageId { get; set; }

    /// <summary>
    /// Message attachments
    /// </summary>
    public List<ChatAttachmentRequest>? Attachments { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Chat attachment request
/// </summary>
public class ChatAttachmentRequest
{
    /// <summary>
    /// File name
    /// </summary>
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    [Range(1, 10485760)] // Max 10MB
    public long FileSize { get; set; }

    /// <summary>
    /// Content type
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// Base64 encoded file data
    /// </summary>
    [Required]
    public string FileData { get; set; } = string.Empty;
}

/// <summary>
/// Chat message response
/// </summary>
public class ChatMessageResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// Sender user ID
    /// </summary>
    public Guid SenderId { get; set; }

    /// <summary>
    /// Sent timestamp
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Whether the message has been edited
    /// </summary>
    public bool IsEdited { get; set; }

    /// <summary>
    /// Edited timestamp
    /// </summary>
    public DateTime? EditedAt { get; set; }

    /// <summary>
    /// Reply to message ID
    /// </summary>
    public Guid? ReplyToMessageId { get; set; }

    /// <summary>
    /// Message attachments
    /// </summary>
    public List<ChatAttachmentResponse> Attachments { get; set; } = new();
}

/// <summary>
/// Chat attachment response
/// </summary>
public class ChatAttachmentResponse
{
    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// Content type
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// Download URL
    /// </summary>
    public string DownloadUrl { get; set; } = string.Empty;
}

/// <summary>
/// Chat messages response
/// </summary>
public class ChatMessagesResponse
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Messages
    /// </summary>
    public List<ChatMessageResponse> Messages { get; set; } = new();

    /// <summary>
    /// Total count of messages
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Whether there are more messages
    /// </summary>
    public bool HasMoreMessages { get; set; }
}

/// <summary>
/// Chat action response
/// </summary>
public class ChatActionResponse
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// Action performed
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// Whether the action was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Action timestamp
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Command ID for tracking
    /// </summary>
    public Guid CommandId { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}
