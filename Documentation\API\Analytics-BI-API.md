# 📊 Analytics & BI Service API Documentation

## 📋 Overview

The Analytics & BI Service provides comprehensive business intelligence, real-time dashboards, performance analytics, and predictive insights across all platform operations. This service is 60% complete with core analytics features production-ready.

**Base URL**: `/api/v1/analytics` (via API Gateway)  
**Direct URL**: `http://localhost:5009`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation                  | Permission                    | Roles                   |
| -------------------------- | ----------------------------- | ----------------------- |
| View admin analytics       | `analytics.admin.read`        | Admin                   |
| View own analytics         | `analytics.read.own`          | All authenticated users |
| Export reports             | `analytics.export`            | Admin, Manager roles    |
| Create custom reports      | `analytics.reports.create`    | Admin, Manager roles    |
| Manage dashboards          | `analytics.dashboards.manage` | Admin                   |
| View real-time KPIs        | `analytics.kpis.read`         | Admin, Manager roles    |
| Access predictive insights | `analytics.insights.read`     | Premium subscribers     |

## 📊 Core Endpoints

### 1. Admin Analytics

#### Get Admin Dashboard

```http
GET /api/v1/analytics/admin/dashboard
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Start date for analytics period
- `toDate`: End date for analytics period
- `period`: Time granularity (`Daily|Weekly|Monthly|Quarterly|Yearly`)
- `includeForecasts`: Include predictive forecasts (default: false)

**Response:**

```json
{
  "generatedAt": "2024-01-15T10:30:00Z",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z",
    "granularity": "Daily"
  },
  "platformOverview": {
    "totalUsers": 15000,
    "activeUsers": 12500,
    "totalOrders": 45000,
    "completedTrips": 42000,
    "totalRevenue": {
      "amount": 2500000.0,
      "currency": "INR"
    },
    "revenueGrowthRate": 12.8,
    "userGrowthRate": 18.5,
    "orderGrowthRate": 22.3
  },
  "userMetrics": {
    "totalRegistrations": 15000,
    "activeUsers": 12500,
    "userRetentionRate": 83.3,
    "averageSessionDuration": 1800,
    "usersByType": {
      "Broker": 3500,
      "Transporter": 4200,
      "Shipper": 6800,
      "Driver": 500
    },
    "userGrowthTrends": [
      {
        "date": "2024-01-01",
        "newUsers": 150,
        "activeUsers": 11800,
        "retentionRate": 82.1
      }
    ]
  },
  "operationalMetrics": {
    "totalOrders": 45000,
    "completedOrders": 42000,
    "orderCompletionRate": 93.3,
    "averageOrderValue": {
      "amount": 25000.0,
      "currency": "INR"
    },
    "onTimeDeliveryRate": 94.5,
    "customerSatisfactionScore": 4.6,
    "operationalEfficiency": 88.7
  },
  "financialMetrics": {
    "totalRevenue": {
      "amount": 2500000.0,
      "currency": "INR"
    },
    "grossMargin": {
      "amount": 625000.0,
      "currency": "INR"
    },
    "netMargin": {
      "amount": 375000.0,
      "currency": "INR"
    },
    "revenueByUserType": [
      {
        "userType": "Transporter",
        "revenue": {
          "amount": 1200000.0,
          "currency": "INR"
        },
        "percentage": 48.0
      }
    ],
    "revenueTrends": [
      {
        "date": "2024-01-01",
        "revenue": 80000.0,
        "cumulativeRevenue": 80000.0,
        "growthRate": 15.2
      }
    ]
  },
  "geographicMetrics": {
    "topStates": [
      {
        "state": "Maharashtra",
        "orders": 12000,
        "revenue": {
          "amount": 600000.0,
          "currency": "INR"
        },
        "percentage": 24.0
      }
    ],
    "topRoutes": [
      {
        "route": "Mumbai to Delhi",
        "orders": 2500,
        "revenue": {
          "amount": 125000.0,
          "currency": "INR"
        },
        "averageDistance": 1400.5
      }
    ]
  },
  "alerts": [
    {
      "type": "Performance",
      "severity": "Warning",
      "message": "Order completion rate dropped below 95%",
      "value": 93.3,
      "threshold": 95.0,
      "timestamp": "2024-01-15T09:00:00Z"
    }
  ]
}
```

#### Get Real-time KPIs

```http
GET /api/v1/analytics/admin/kpis/realtime
Authorization: Bearer <token>
```

**Response:**

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "kpis": [
    {
      "name": "Active Users",
      "value": 1250,
      "unit": "count",
      "trend": "up",
      "changePercentage": 5.2,
      "status": "Good",
      "target": 1200,
      "targetAchievement": 104.2
    },
    {
      "name": "Order Completion Rate",
      "value": 93.3,
      "unit": "percentage",
      "trend": "down",
      "changePercentage": -1.7,
      "status": "Warning",
      "target": 95.0,
      "targetAchievement": 98.2
    },
    {
      "name": "Revenue Today",
      "value": 125000.0,
      "unit": "currency",
      "currency": "INR",
      "trend": "up",
      "changePercentage": 8.5,
      "status": "Excellent",
      "target": 120000.0,
      "targetAchievement": 104.2
    },
    {
      "name": "Customer Satisfaction",
      "value": 4.6,
      "unit": "rating",
      "trend": "stable",
      "changePercentage": 0.2,
      "status": "Good",
      "target": 4.5,
      "targetAchievement": 102.2
    }
  ],
  "systemHealth": {
    "overallStatus": "Healthy",
    "apiResponseTime": 145,
    "databaseResponseTime": 25,
    "activeConnections": 850,
    "errorRate": 0.02
  }
}
```

#### Get Revenue Analytics

```http
GET /api/v1/analytics/admin/revenue
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Start date
- `toDate`: End date
- `granularity`: `Daily|Weekly|Monthly`
- `includeForecasts`: Include revenue forecasts
- `segmentBy`: Segment by (`UserType|Geography|Service`)

### 2. Transport Company Analytics

#### Get Transport Company Dashboard

```http
GET /api/v1/analytics/transport-company/{companyId}/dashboard
Authorization: Bearer <token>
```

**Response:**

```json
{
  "companyId": "company-uuid",
  "generatedAt": "2024-01-15T10:30:00Z",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "businessOverview": {
    "totalOrders": 1200,
    "completedOrders": 1140,
    "totalRevenue": {
      "amount": 300000.0,
      "currency": "INR"
    },
    "averageOrderValue": {
      "amount": 25000.0,
      "currency": "INR"
    },
    "profitMargin": 18.5,
    "customerSatisfactionScore": 4.7
  },
  "operationalMetrics": {
    "orderCompletionRate": 95.0,
    "onTimeDeliveryRate": 96.2,
    "averageDeliveryTime": 2.3,
    "fleetUtilizationRate": 87.5,
    "driverEfficiencyScore": 92.1,
    "fuelEfficiency": 8.2
  },
  "rfqPerformance": {
    "totalRFQsReceived": 2500,
    "quotesSubmitted": 1800,
    "quoteResponseRate": 72.0,
    "quotesAccepted": 1200,
    "quoteSuccessRate": 66.7,
    "averageResponseTime": 4.2,
    "competitiveWinRate": 68.5
  },
  "customerAnalytics": {
    "totalCustomers": 450,
    "activeCustomers": 380,
    "customerRetentionRate": 84.4,
    "repeatCustomerRate": 76.8,
    "customerLifetimeValue": {
      "amount": 125000.0,
      "currency": "INR"
    },
    "topCustomers": [
      {
        "customerId": "customer-uuid",
        "customerName": "ABC Electronics",
        "orders": 45,
        "revenue": {
          "amount": 112500.0,
          "currency": "INR"
        }
      }
    ]
  },
  "routeAnalytics": {
    "topRoutes": [
      {
        "route": "Mumbai to Delhi",
        "orders": 150,
        "revenue": {
          "amount": 375000.0,
          "currency": "INR"
        },
        "averageMargin": 22.5,
        "onTimeRate": 97.3
      }
    ],
    "routeOptimizationSavings": {
      "amount": 25000.0,
      "currency": "INR"
    },
    "fuelSavings": {
      "amount": 15000.0,
      "currency": "INR"
    }
  },
  "competitiveAnalysis": {
    "marketPosition": "Top 3",
    "marketShare": 12.5,
    "competitiveAdvantages": [
      "On-time delivery",
      "Competitive pricing",
      "Technology integration"
    ],
    "improvementAreas": ["Response time", "Fleet expansion"]
  },
  "predictiveInsights": {
    "demandForecast": [
      {
        "period": "2024-02",
        "predictedOrders": 1350,
        "confidence": 87.5
      }
    ],
    "revenueProjection": {
      "amount": 340000.0,
      "currency": "INR",
      "confidence": 82.3
    },
    "recommendations": [
      "Expand fleet capacity for Q2 demand surge",
      "Focus on Mumbai-Bangalore route optimization",
      "Improve quote response time to increase win rate"
    ]
  }
}
```

#### Get RFQ Conversion Analytics

```http
GET /api/v1/analytics/transport-company/{companyId}/rfq-conversion
Authorization: Bearer <token>
```

#### Get Delivery Performance

```http
GET /api/v1/analytics/transport-company/{companyId}/delivery-performance
Authorization: Bearer <token>
```

### 3. Broker Analytics

#### Get Broker Dashboard

```http
GET /api/v1/analytics/broker/{brokerId}/dashboard
Authorization: Bearer <token>
```

**Response:**

```json
{
  "brokerId": "broker-uuid",
  "generatedAt": "2024-01-15T10:30:00Z",
  "businessOverview": {
    "totalRFQsCreated": 850,
    "totalQuotesReceived": 2400,
    "totalOrdersPlaced": 580,
    "totalRevenue": {
      "amount": 145000.0,
      "currency": "INR"
    },
    "averageCommission": 15.5,
    "customerSatisfactionScore": 4.5
  },
  "operationalMetrics": {
    "rfqConversionRate": 68.2,
    "quoteResponseRate": 85.7,
    "averageQuotesPerRFQ": 2.8,
    "carrierNetworkUtilization": 78.5,
    "operationalEfficiencyScore": 88.7,
    "averageOrderProcessingTime": 3.2
  },
  "carrierNetworkAnalytics": {
    "totalCarriers": 125,
    "activeCarriers": 98,
    "carrierUtilizationRate": 78.4,
    "averageCarrierRating": 4.3,
    "topCarriers": [
      {
        "carrierId": "carrier-uuid",
        "carrierName": "Express Logistics",
        "orders": 45,
        "successRate": 97.8,
        "averageRating": 4.8
      }
    ],
    "carrierPerformanceMetrics": {
      "onTimeDeliveryRate": 94.2,
      "averageResponseTime": 2.1,
      "reliabilityScore": 92.5
    }
  },
  "quoteAnalytics": {
    "totalQuotesGenerated": 850,
    "acceptedQuotes": 582,
    "overallSuccessRate": 68.5,
    "averageQuoteValue": {
      "amount": 24000.0,
      "currency": "INR"
    },
    "averageMargin": 15.5,
    "quoteTypeAnalysis": [
      {
        "quoteType": "Express",
        "totalQuotes": 250,
        "acceptedQuotes": 185,
        "successRate": 74.0,
        "averageValue": {
          "amount": 28000.0,
          "currency": "INR"
        }
      }
    ],
    "competitivePricing": {
      "averageMarketPrice": {
        "amount": 25500.0,
        "currency": "INR"
      },
      "priceCompetitiveness": 94.1,
      "winRateByPriceRange": [
        {
          "priceRange": "Below Market",
          "winRate": 85.2
        },
        {
          "priceRange": "At Market",
          "winRate": 68.7
        },
        {
          "priceRange": "Above Market",
          "winRate": 42.3
        }
      ]
    }
  },
  "customerAnalytics": {
    "totalCustomers": 180,
    "activeCustomers": 145,
    "customerRetentionRate": 80.6,
    "repeatCustomerRate": 72.4,
    "customerLifetimeValue": {
      "amount": 85000.0,
      "currency": "INR"
    }
  },
  "marketIntelligence": {
    "marketTrends": [
      {
        "trend": "Increasing demand for express delivery",
        "impact": "High",
        "recommendation": "Expand express service offerings"
      }
    ],
    "competitorAnalysis": {
      "marketPosition": "Top 5",
      "competitiveAdvantages": [
        "Strong carrier network",
        "Technology platform",
        "Customer service"
      ]
    }
  }
}
```

#### Get Quote Analytics

```http
GET /api/v1/analytics/broker/{brokerId}/quotes
Authorization: Bearer <token>
```

### 4. Carrier Analytics

#### Get Carrier Dashboard

```http
GET /api/v1/analytics/carrier/{carrierId}/dashboard
Authorization: Bearer <token>
```

**Response:**

```json
{
  "carrierId": "carrier-uuid",
  "generatedAt": "2024-01-15T10:30:00Z",
  "businessOverview": {
    "totalTrips": 320,
    "completedTrips": 305,
    "totalRevenue": {
      "amount": 800000.0,
      "currency": "INR"
    },
    "averageRevenuePerTrip": {
      "amount": 2500.0,
      "currency": "INR"
    },
    "profitMargin": 22.5,
    "customerRating": 4.6
  },
  "operationalMetrics": {
    "tripCompletionRate": 95.3,
    "onTimeDeliveryRate": 96.8,
    "averageDeliveryTime": 1.8,
    "vehicleUtilizationRate": 89.2,
    "fuelEfficiency": 8.5,
    "maintenanceCostPerKm": 2.5
  },
  "performanceMetrics": {
    "customerSatisfactionScore": 4.6,
    "repeatBusinessRate": 78.5,
    "safetyScore": 94.2,
    "complianceScore": 97.1,
    "reliabilityIndex": 92.8
  },
  "fleetAnalytics": {
    "totalVehicles": 25,
    "activeVehicles": 22,
    "vehicleUtilization": [
      {
        "vehicleId": "vehicle-uuid",
        "vehicleNumber": "MH-01-AB-1234",
        "utilizationRate": 92.5,
        "revenue": {
          "amount": 45000.0,
          "currency": "INR"
        },
        "trips": 18
      }
    ],
    "maintenanceMetrics": {
      "scheduledMaintenanceCompliance": 98.5,
      "unplannedBreakdowns": 2,
      "averageMaintenanceCost": {
        "amount": 15000.0,
        "currency": "INR"
      }
    }
  },
  "routePerformance": {
    "topRoutes": [
      {
        "route": "Mumbai to Pune",
        "trips": 45,
        "revenue": {
          "amount": 112500.0,
          "currency": "INR"
        },
        "averageMargin": 25.2,
        "onTimeRate": 98.9
      }
    ],
    "routeOptimization": {
      "fuelSavings": {
        "amount": 12000.0,
        "currency": "INR"
      },
      "timeSavings": 120,
      "distanceOptimization": 8.5
    }
  },
  "financialAnalytics": {
    "revenueBreakdown": {
      "transportRevenue": {
        "amount": 720000.0,
        "currency": "INR"
      },
      "additionalServices": {
        "amount": 80000.0,
        "currency": "INR"
      }
    },
    "costBreakdown": {
      "fuelCosts": {
        "amount": 320000.0,
        "currency": "INR"
      },
      "maintenanceCosts": {
        "amount": 80000.0,
        "currency": "INR"
      },
      "driverCosts": {
        "amount": 180000.0,
        "currency": "INR"
      },
      "otherCosts": {
        "amount": 40000.0,
        "currency": "INR"
      }
    },
    "profitabilityAnalysis": {
      "grossProfit": {
        "amount": 180000.0,
        "currency": "INR"
      },
      "netProfit": {
        "amount": 120000.0,
        "currency": "INR"
      },
      "profitMargin": 15.0
    }
  }
}
```

### 5. Custom Reports & Exports

#### Create Custom Report

```http
POST /api/v1/analytics/reports/custom
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Monthly Performance Report",
  "description": "Comprehensive monthly performance analysis",
  "reportType": "Performance",
  "dataSource": "Orders",
  "filters": {
    "dateRange": {
      "fromDate": "2024-01-01T00:00:00Z",
      "toDate": "2024-01-31T23:59:59Z"
    },
    "userTypes": ["Transporter", "Broker"],
    "regions": ["Maharashtra", "Delhi"],
    "orderStatuses": ["Completed", "InProgress"]
  },
  "metrics": [
    "totalOrders",
    "completedOrders",
    "revenue",
    "onTimeDeliveryRate",
    "customerSatisfaction"
  ],
  "groupBy": ["userType", "region", "month"],
  "sortBy": "revenue",
  "sortDirection": "desc",
  "includeCharts": true,
  "chartTypes": ["line", "bar", "pie"],
  "exportFormats": ["PDF", "Excel", "CSV"],
  "isScheduled": true,
  "scheduleFrequency": "Monthly",
  "recipients": ["<EMAIL>", "<EMAIL>"]
}
```

#### Get Predictive Insights

```http
GET /api/v1/analytics/predictive/insights
Authorization: Bearer <token>
```

**Query Parameters:**

- `entityType`: Entity type for insights
- `entityId`: Specific entity ID
- `insightType`: Type of insight (`Demand|Revenue|Risk|Opportunity`)
- `timeframe`: Prediction timeframe

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/analytics/hub`

**Events:**

- `MetricUpdated` - Real-time metric updates
- `AlertTriggered` - Performance alerts
- `ReportGenerated` - Report completion notifications
- `DashboardRefreshed` - Dashboard data updates
- `ThresholdBreached` - KPI threshold violations
- `AnomalyDetected` - Anomaly detection alerts

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/analytics/hub')
  .build()

// Subscribe to real-time metrics
connection.on('MetricUpdated', (data) => {
  console.log('Metric updated:', data)
  // Update dashboard widgets
})

// Listen for alerts
connection.on('AlertTriggered', (alert) => {
  console.log('Alert triggered:', alert)
  // Show alert notification
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                     | Description                            |
| ---- | --------------------------- | -------------------------------------- |
| 400  | `INVALID_DATE_RANGE`        | Invalid date range specified           |
| 400  | `INVALID_METRIC_TYPE`       | Invalid metric type requested          |
| 400  | `UNSUPPORTED_EXPORT_FORMAT` | Export format not supported            |
| 401  | `UNAUTHORIZED`              | Authentication required                |
| 403  | `INSUFFICIENT_PERMISSIONS`  | Insufficient permissions for analytics |
| 404  | `REPORT_NOT_FOUND`          | Report not found                       |
| 404  | `DASHBOARD_NOT_FOUND`       | Dashboard not found                    |
| 409  | `REPORT_ALREADY_PROCESSING` | Report is already being processed      |
| 422  | `INVALID_FILTER_CRITERIA`   | Invalid filter criteria                |
| 422  | `VALIDATION_FAILED`         | Input validation failed                |
| 429  | `RATE_LIMIT_EXCEEDED`       | Too many requests                      |

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_DATE_RANGE",
    "message": "Invalid date range specified",
    "details": "End date must be after start date",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
