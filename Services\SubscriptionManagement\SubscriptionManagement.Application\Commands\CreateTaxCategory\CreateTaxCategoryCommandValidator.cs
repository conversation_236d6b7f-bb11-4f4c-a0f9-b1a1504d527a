using FluentValidation;

namespace SubscriptionManagement.Application.Commands.CreateTaxCategory
{
    public class CreateTaxCategoryCommandValidator : AbstractValidator<CreateTaxCategoryCommand>
    {
        public CreateTaxCategoryCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Tax category name is required")
                .MaximumLength(100)
                .WithMessage("Tax category name cannot exceed 100 characters");

            RuleFor(x => x.Description)
                .NotEmpty()
                .WithMessage("Tax category description is required")
                .MaximumLength(500)
                .WithMessage("Tax category description cannot exceed 500 characters");

            RuleFor(x => x.Code)
                .NotEmpty()
                .WithMessage("Tax category code is required")
                .Length(2, 10)
                .WithMessage("Tax category code must be between 2 and 10 characters")
                .Matches("^[A-Z0-9_]+$")
                .WithMessage("Tax category code can only contain uppercase letters, numbers, and underscores");

            RuleFor(x => x.CreatedByUserId)
                .NotEmpty()
                .WithMessage("Created by user ID is required");
        }
    }
}
