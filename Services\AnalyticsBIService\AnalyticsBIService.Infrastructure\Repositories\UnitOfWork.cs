using Microsoft.EntityFrameworkCore.Storage;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Interfaces;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using System.Data;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Wrapper for Entity Framework transaction to implement domain IDbTransaction interface
/// </summary>
public class DbTransactionWrapper : AnalyticsBIService.Domain.Repositories.IDbTransaction
{
    private readonly IDbContextTransaction _transaction;

    public DbTransactionWrapper(IDbContextTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.CommitAsync(cancellationToken);
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.RollbackAsync(cancellationToken);
    }

    public void Dispose()
    {
        _transaction?.Dispose();
    }
}

public class UnitOfWork : AnalyticsBIService.Domain.Repositories.IUnitOfWork, Shared.Infrastructure.Interfaces.IUnitOfWork
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICachingService _cachingService;
    private readonly IPerformanceMetrics _performanceMetrics;
    private IDbContextTransaction? _transaction;
    private bool _disposed = false;

    // Repository instances
    private IAnalyticsEventRepository? _analyticsEventRepository;
    private IMetricRepository? _metricRepository;
    private IDashboardRepository? _dashboardRepository;
    private IDashboardWidgetRepository? _dashboardWidgetRepository;
    private IReportRepository? _reportRepository;
    private IReportSectionRepository? _reportSectionRepository;
    private IReportExportRepository? _reportExportRepository;
    private IAlertRepository? _alertRepository;
    private IAlertRuleRepository? _alertRuleRepository;

    public UnitOfWork(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _cachingService = cachingService ?? throw new ArgumentNullException(nameof(cachingService));
        _performanceMetrics = performanceMetrics ?? throw new ArgumentNullException(nameof(performanceMetrics));
    }

    public IAnalyticsEventRepository AnalyticsEvents
    {
        get
        {
            _analyticsEventRepository ??= new AnalyticsEventRepository(_context, _cachingService, _performanceMetrics);
            return _analyticsEventRepository;
        }
    }

    public IMetricRepository Metrics
    {
        get
        {
            _metricRepository ??= new MetricRepository(_context, _cachingService, _performanceMetrics);
            return _metricRepository;
        }
    }

    public IDashboardRepository Dashboards
    {
        get
        {
            _dashboardRepository ??= new DashboardRepository(_context, _cachingService, _performanceMetrics);
            return _dashboardRepository;
        }
    }

    public IReportRepository Reports
    {
        get
        {
            _reportRepository ??= new ReportRepository(_context, _cachingService, _performanceMetrics);
            return _reportRepository;
        }
    }

    public IAlertRepository Alerts
    {
        get
        {
            _alertRepository ??= new AlertRepository(_context, _cachingService, _performanceMetrics);
            return _alertRepository;
        }
    }

    public IDashboardWidgetRepository DashboardWidgets
    {
        get
        {
            _dashboardWidgetRepository ??= new DashboardWidgetRepository(_context, _cachingService, _performanceMetrics);
            return _dashboardWidgetRepository;
        }
    }

    public IReportSectionRepository ReportSections
    {
        get
        {
            _reportSectionRepository ??= new ReportSectionRepository(_context, _cachingService, _performanceMetrics);
            return _reportSectionRepository;
        }
    }

    public IReportExportRepository ReportExports
    {
        get
        {
            _reportExportRepository ??= new ReportExportRepository(_context, _cachingService, _performanceMetrics);
            return _reportExportRepository;
        }
    }

    public IAlertRuleRepository AlertRules
    {
        get
        {
            _alertRuleRepository ??= new AlertRuleRepository(_context, _cachingService, _performanceMetrics);
            return _alertRuleRepository;
        }
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<AnalyticsBIService.Domain.Repositories.IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            throw new InvalidOperationException("A transaction is already in progress.");
        }

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new DbTransactionWrapper(_transaction);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction in progress.");
        }

        try
        {
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction in progress.");
        }

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            // If already in a transaction, just execute the operation
            return await operation();
        }

        await BeginTransactionAsync(cancellationToken);
        try
        {
            var result = await operation();
            await CommitTransactionAsync(cancellationToken);
            return result;
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task ExecuteInTransactionAsync(Func<Task> operation, CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            // If already in a transaction, just execute the operation
            await operation();
            return;
        }

        await BeginTransactionAsync(cancellationToken);
        try
        {
            await operation();
            await CommitTransactionAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
        _disposed = true;
    }

    // Shared IUnitOfWork interface implementation
    async Task<int> Shared.Infrastructure.Interfaces.IUnitOfWork.SaveChangesAsync(CancellationToken cancellationToken)
    {
        return await SaveChangesAsync(cancellationToken);
    }

    async Task Shared.Infrastructure.Interfaces.IUnitOfWork.BeginTransactionAsync(CancellationToken cancellationToken)
    {
        await BeginTransactionAsync(cancellationToken);
    }

    async Task Shared.Infrastructure.Interfaces.IUnitOfWork.CommitTransactionAsync(CancellationToken cancellationToken)
    {
        await CommitTransactionAsync(cancellationToken);
    }

    async Task Shared.Infrastructure.Interfaces.IUnitOfWork.RollbackTransactionAsync(CancellationToken cancellationToken)
    {
        await RollbackTransactionAsync(cancellationToken);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    public async ValueTask DisposeAsync()
    {
        if (_transaction != null)
        {
            await _transaction.DisposeAsync();
        }

        await _context.DisposeAsync();
        Dispose(false);
        GC.SuppressFinalize(this);
    }
}
