using AuditCompliance.API;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;
using FluentAssertions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace AuditCompliance.Tests.Integration
{
    /// <summary>
    /// Integration tests for custom report template functionality
    /// </summary>
    public class CustomReportTemplateIntegrationTests : IClassFixture<WebApplicationFactory<Program>>, IDisposable
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly IServiceScope _scope;
        private readonly AuditComplianceDbContext _context;

        public CustomReportTemplateIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AuditComplianceDbContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }

                    // Add in-memory database for testing
                    services.AddDbContext<AuditComplianceDbContext>(options =>
                    {
                        options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
                    });

                    // Reduce logging noise in tests
                    services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Warning));
                });
            });

            _client = _factory.CreateClient();
            _scope = _factory.Services.CreateScope();
            _context = _scope.ServiceProvider.GetRequiredService<AuditComplianceDbContext>();
            
            // Ensure database is created
            _context.Database.EnsureCreated();
        }

        [Fact]
        public async Task CreateTemplate_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var request = CreateValidTemplateRequest();

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/report-templates", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.TemplateName.Should().Be(request.TemplateName);
            result.Description.Should().Be(request.Description);
            result.ComplianceStandard.Should().Be(request.ComplianceStandard);
            result.TemplateType.Should().Be(request.TemplateType);
            result.IsActive.Should().BeTrue();
            result.Version.Should().Be("1.0");
        }

        [Fact]
        public async Task CreateTemplate_WithDuplicateName_ShouldReturnConflict()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create first template
            await _client.PostAsJsonAsync("/api/audit/report-templates", request);

            // Act - Try to create template with same name
            var response = await _client.PostAsJsonAsync("/api/audit/report-templates", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Conflict);
        }

        [Fact]
        public async Task GetTemplateById_WithValidId_ShouldReturnTemplate()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            // Act
            var response = await _client.GetAsync($"/api/audit/report-templates/{createdTemplate!.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Id.Should().Be(createdTemplate.Id);
            result.TemplateName.Should().Be(request.TemplateName);
        }

        [Fact]
        public async Task GetTemplateById_WithInvalidId_ShouldReturnNotFound()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/audit/report-templates/{invalidId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task SearchTemplates_WithValidCriteria_ShouldReturnResults()
        {
            // Arrange
            await SeedTestTemplatesAsync();
            
            var searchRequest = new TemplateSearchRequestDto
            {
                SearchTerm = "Test",
                ComplianceStandard = ComplianceStandard.SOX,
                PageNumber = 1,
                PageSize = 10
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/report-templates/search", searchRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<TemplateSearchResultDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Templates.Should().NotBeEmpty();
            result.TotalCount.Should().BeGreaterThan(0);
            result.Templates.Should().AllSatisfy(t => t.TemplateName.Should().Contain("Test"));
        }

        [Fact]
        public async Task UpdateTemplate_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var createRequest = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", createRequest);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            var updateRequest = new UpdateCustomReportTemplateRequestDto
            {
                TemplateName = "Updated Template Name",
                Description = "Updated description",
                TemplateContent = "Updated template content",
                Sections = new List<ReportSectionDto>(),
                Parameters = new List<ReportParameterDto>(),
                Tags = new List<string> { "updated", "test" },
                Metadata = new Dictionary<string, object>()
            };

            // Act
            var response = await _client.PutAsJsonAsync($"/api/audit/report-templates/{createdTemplate!.Id}", updateRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.TemplateName.Should().Be(updateRequest.TemplateName);
            result.Description.Should().Be(updateRequest.Description);
            result.Version.Should().Be("1.1"); // Version should increment
        }

        [Fact]
        public async Task DeleteTemplate_WithValidId_ShouldReturnSuccess()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            // Act
            var response = await _client.DeleteAsync($"/api/audit/report-templates/{createdTemplate!.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NoContent);

            // Verify template is deleted
            var getResponse = await _client.GetAsync($"/api/audit/report-templates/{createdTemplate.Id}");
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task ToggleActiveStatus_WithValidId_ShouldReturnSuccess()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            var toggleRequest = new { IsActive = false };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/audit/report-templates/{createdTemplate!.Id}/toggle-active", toggleRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(content);
            result.GetProperty("success").GetBoolean().Should().BeTrue();
        }

        [Fact]
        public async Task ToggleVisibility_WithValidId_ShouldReturnSuccess()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            var toggleRequest = new { IsPublic = true };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/audit/report-templates/{createdTemplate!.Id}/toggle-visibility", toggleRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(content);
            result.GetProperty("success").GetBoolean().Should().BeTrue();
        }

        [Fact]
        public async Task ValidateTemplate_WithValidTemplate_ShouldReturnNoErrors()
        {
            // Arrange
            var validateRequest = new
            {
                TemplateContent = "Valid template content",
                Sections = new List<ReportSectionDto>(),
                Parameters = new List<ReportParameterDto>(),
                PerformDeepValidation = true
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/audit/report-templates/validate", validateRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<TemplateValidationResultDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Fact]
        public async Task CloneTemplate_WithValidId_ShouldReturnClonedTemplate()
        {
            // Arrange
            var request = CreateValidTemplateRequest();
            
            // Create template first
            var createResponse = await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            var createContent = await createResponse.Content.ReadAsStringAsync();
            var createdTemplate = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(createContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            var cloneRequest = new
            {
                NewTemplateName = "Cloned Template",
                NewDescription = "Cloned from original template",
                MakePrivate = true
            };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/audit/report-templates/{createdTemplate!.Id}/clone", cloneRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<CustomComplianceReportTemplateDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            result.Should().NotBeNull();
            result!.Id.Should().NotBe(createdTemplate.Id);
            result.TemplateName.Should().Be("Cloned Template");
            result.IsPublic.Should().BeFalse();
        }

        private async Task SeedTestTemplatesAsync()
        {
            for (int i = 0; i < 5; i++)
            {
                var request = CreateValidTemplateRequest();
                request.TemplateName = $"Test Template {i + 1}";
                request.Description = $"Test template description {i + 1}";
                
                await _client.PostAsJsonAsync("/api/audit/report-templates", request);
            }
        }

        private static CreateCustomReportTemplateRequestDto CreateValidTemplateRequest()
        {
            return new CreateCustomReportTemplateRequestDto
            {
                TemplateName = "Test Template",
                Description = "Test template description",
                ComplianceStandard = ComplianceStandard.SOX,
                TemplateType = ReportTemplateType.Custom,
                TemplateContent = "Template content here",
                Sections = new List<ReportSectionDto>
                {
                    new()
                    {
                        Id = "section1",
                        Name = "Summary Section",
                        Description = "Summary of audit data",
                        Order = 1,
                        IsRequired = true,
                        Template = "Summary template content",
                        DataSources = new List<string> { "AuditLogs" },
                        Type = Domain.ValueObjects.SectionType.Summary,
                        Configuration = new Dictionary<string, object> { { "showCharts", true } }
                    }
                },
                Parameters = new List<ReportParameterDto>
                {
                    new()
                    {
                        Id = "param1",
                        Name = "startDate",
                        DisplayName = "Start Date",
                        Description = "Report start date",
                        Type = Domain.ValueObjects.ParameterType.Date,
                        IsRequired = true,
                        DefaultValue = DateTime.UtcNow.AddDays(-30),
                        Options = new List<ParameterOptionDto>(),
                        Validation = new Dictionary<string, object> { { "minDate", DateTime.UtcNow.AddYears(-1) } }
                    }
                },
                Tags = new List<string> { "test", "compliance" },
                IsPublic = false,
                Metadata = new Dictionary<string, object> { { "version", "1.0" } }
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
            _scope?.Dispose();
            _client?.Dispose();
        }
    }
}
