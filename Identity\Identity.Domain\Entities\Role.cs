﻿using System;
using System.Collections.Generic;
using System.Linq;
using Identity.Domain.Common;
using Identity.Domain.Common.Events;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public enum RoleType
    {
        System,
        Template,
        Custom,
        Inherited
    }

    public class RoleCreatedEvent : DomainEvent
    {
        public Guid RoleId { get; }
        public string RoleName { get; }
        public Guid CreatedBy { get; }

        public RoleCreatedEvent(Guid roleId, string roleName, Guid createdBy)
        {
            RoleId = roleId;
            RoleName = roleName;
            CreatedBy = createdBy;
        }
    }

    public class Role : Entity
    {
        public string Name { get; private set; }
        public string NormalizedName { get; private set; }
        public string Description { get; private set; }
        public bool IsSystem { get; private set; }
        public RoleType Type { get; private set; }
        public Guid? ParentRoleId { get; private set; }
        public bool IsTemplate { get; private set; }
        public string Department { get; private set; }
        public int Priority { get; private set; }
        public bool IsActive { get; private set; }
        public Guid CreatedBy { get; private set; }

        private readonly List<RolePermission> _permissions = new();
        public IReadOnlyCollection<RolePermission> Permissions => _permissions.AsReadOnly();

        private readonly List<Role> _childRoles = new();
        public IReadOnlyCollection<Role> ChildRoles => _childRoles.AsReadOnly();

        private Role() { }

        public Role(
            string name,
            string description,
            Guid createdBy,
            bool isSystem = false,
            RoleType type = RoleType.Custom,
            Guid? parentRoleId = null,
            bool isTemplate = false,
            string department = null,
            int priority = 0)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Role name cannot be empty");

            Name = name;
            NormalizedName = name.ToUpperInvariant();
            Description = description ?? string.Empty;
            IsSystem = isSystem;
            Type = type;
            ParentRoleId = parentRoleId;
            IsTemplate = isTemplate;
            Department = department ?? string.Empty;
            Priority = priority;
            IsActive = true;
            CreatedBy = createdBy;
            CreatedAt = DateTime.UtcNow;

            // AddDomainEvent(new RoleCreatedEvent(Id, name, createdBy));
        }

        public void AddPermission(Permission permission)
        {
            if (permission == null)
                throw new DomainException("Permission cannot be null");

            if (IsSystem && Type == RoleType.System)
                throw new DomainException("System roles cannot be modified");

            if (_permissions.Any(p => p.PermissionId == permission.Id))
                return;

            _permissions.Add(new RolePermission(this.Id, permission.Id));
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemovePermission(Permission permission)
        {
            if (permission == null)
                throw new DomainException("Permission cannot be null");

            if (IsSystem && Type == RoleType.System)
                throw new DomainException("System roles cannot be modified");

            var rolePermission = _permissions.FirstOrDefault(p => p.PermissionId == permission.Id);
            if (rolePermission != null)
            {
                _permissions.Remove(rolePermission);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void AddPermissions(IEnumerable<Permission> permissions)
        {
            if (permissions == null)
                throw new DomainException("Permissions cannot be null");

            foreach (var permission in permissions)
            {
                AddPermission(permission);
            }
        }

        public void ClearPermissions()
        {
            if (IsSystem && Type == RoleType.System)
                throw new DomainException("System roles cannot be modified");

            _permissions.Clear();
            UpdatedAt = DateTime.UtcNow;
        }

        public void Update(string name, string description, string department = null, int priority = 0)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Role name cannot be empty");

            if (IsSystem && Type == RoleType.System)
                throw new DomainException("System roles cannot be updated");

            Name = name;
            NormalizedName = name.ToUpperInvariant();
            Description = description ?? string.Empty;
            Department = department ?? Department;
            Priority = priority;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            if (IsSystem && Type == RoleType.System)
                throw new DomainException("System roles cannot be deactivated");

            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetAsTemplate()
        {
            IsTemplate = true;
            Type = RoleType.Template;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveFromTemplate()
        {
            IsTemplate = false;
            if (Type == RoleType.Template)
                Type = RoleType.Custom;
            UpdatedAt = DateTime.UtcNow;
        }

        public Role CreateChildRole(string name, string description, Guid createdBy)
        {
            var childRole = new Role(
                name,
                description,
                createdBy,
                false,
                RoleType.Inherited,
                this.Id,
                false,
                this.Department,
                this.Priority);

            // Inherit permissions from parent
            foreach (var permission in _permissions)
            {
                childRole._permissions.Add(new RolePermission(childRole.Id, permission.PermissionId));
            }

            _childRoles.Add(childRole);
            UpdatedAt = DateTime.UtcNow;

            return childRole;
        }

        public bool HasPermission(Guid permissionId)
        {
            return _permissions.Any(p => p.PermissionId == permissionId);
        }

        public bool HasPermission(string permissionName)
        {
            // This would need to be resolved with the permission repository
            // For now, just check by normalized name pattern
            return _permissions.Any();
        }

        public List<Guid> GetAllPermissionIds()
        {
            var permissionIds = _permissions.Select(p => p.PermissionId).ToList();

            // If this role inherits from a parent, we'd need to get parent permissions too
            // This would be handled in the application layer with proper repository access

            return permissionIds;
        }

        public void InheritFromParent(Role parentRole)
        {
            if (parentRole == null)
                throw new DomainException("Parent role cannot be null");

            if (Type != RoleType.Inherited)
                throw new DomainException("Only inherited roles can inherit from parent");

            ParentRoleId = parentRole.Id;

            // Clear existing permissions and inherit from parent
            _permissions.Clear();
            foreach (var permission in parentRole._permissions)
            {
                _permissions.Add(new RolePermission(this.Id, permission.PermissionId));
            }

            UpdatedAt = DateTime.UtcNow;
        }
    }
}

