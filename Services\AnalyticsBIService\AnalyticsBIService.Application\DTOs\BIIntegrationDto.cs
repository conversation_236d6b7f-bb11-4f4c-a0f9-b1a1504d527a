namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// BI integration data transfer object
/// </summary>
public class BIIntegrationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IntegrationType { get; set; } = string.Empty; // PowerBI, Tableau, Looker, Custom
    public string Status { get; set; } = string.Empty; // Active, Inactive, Error, Syncing
    public DateTime CreatedAt { get; set; }
    public DateTime? LastSync { get; set; }
    public DateTime? NextSync { get; set; }
    public string SyncSchedule { get; set; } = string.Empty;
    public Dictionary<string, object> ConnectionSettings { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public List<string> Dashboards { get; set; } = new();
    public BIIntegrationMetricsDto Metrics { get; set; } = new();

    // Properties required by DataExportService
    public bool IsActive { get; set; } = true;
    public DateTime? LastSyncTime { get; set; }
}

/// <summary>
/// BI integration request data transfer object
/// </summary>
public class BIIntegrationRequestDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IntegrationType { get; set; } = string.Empty;
    public string SyncSchedule { get; set; } = string.Empty;
    public Dictionary<string, object> ConnectionSettings { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public List<string> Dashboards { get; set; } = new();
    public Guid RequestedBy { get; set; }
}

/// <summary>
/// Integration status data transfer object
/// </summary>
public class IntegrationStatusDto
{
    public Guid IntegrationId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public bool IsHealthy { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> StatusDetails { get; set; } = new();

    // Properties required by DataExportService
    public Guid UserId { get; set; }
    public int TotalIntegrations { get; set; }
    public int ActiveIntegrations { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public string IntegrationHealth { get; set; } = string.Empty;
    public List<string> SupportedPlatforms { get; set; } = new();
    public List<BIIntegrationDto> Integrations { get; set; } = new();
}

/// <summary>
/// BI integration metrics data transfer object
/// </summary>
public class BIIntegrationMetricsDto
{
    public long TotalSyncs { get; set; }
    public long SuccessfulSyncs { get; set; }
    public long FailedSyncs { get; set; }
    public decimal SuccessRate { get; set; }
    public TimeSpan AverageSyncTime { get; set; }
    public long TotalDataPointsSynced { get; set; }
    public DateTime? LastSuccessfulSync { get; set; }
    public DateTime? LastFailedSync { get; set; }
    public string? LastError { get; set; }
}
