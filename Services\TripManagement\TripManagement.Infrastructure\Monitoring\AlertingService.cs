using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TripManagement.Application.Interfaces;

namespace TripManagement.Infrastructure.Monitoring;

public class AlertingService : IAlertingService
{
    private readonly INotificationService _notificationService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AlertingService> _logger;
    private readonly AlertingConfiguration _config;

    public AlertingService(
        INotificationService notificationService,
        IMessageBroker messageBroker,
        ILogger<AlertingService> logger,
        IOptions<AlertingConfiguration> config)
    {
        _notificationService = notificationService;
        _messageBroker = messageBroker;
        _logger = logger;
        _config = config.Value;
    }

    public async Task SendCriticalAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogCritical("CRITICAL ALERT: {Title} - {Message}", title, message);

            var alert = new Alert
            {
                Id = Guid.NewGuid(),
                Level = AlertLevel.Critical,
                Title = title,
                Message = message,
                Metadata = metadata ?? new Dictionary<string, object>(),
                CreatedAt = DateTime.UtcNow,
                Source = "TripManagement.Service"
            };

            // Send to all critical alert recipients
            foreach (var recipient in _config.CriticalAlertRecipients)
            {
                await _notificationService.SendNotificationAsync(
                    recipient, 
                    $"🚨 CRITICAL: {title}", 
                    message, 
                    cancellationToken);
            }

            // Publish to message broker for external alerting systems
            await _messageBroker.PublishAsync("alerts.critical", alert, cancellationToken);

            _logger.LogInformation("Critical alert sent successfully: {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send critical alert: {Title}", title);
            throw;
        }
    }

    public async Task SendWarningAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("WARNING ALERT: {Title} - {Message}", title, message);

            var alert = new Alert
            {
                Id = Guid.NewGuid(),
                Level = AlertLevel.Warning,
                Title = title,
                Message = message,
                Metadata = metadata ?? new Dictionary<string, object>(),
                CreatedAt = DateTime.UtcNow,
                Source = "TripManagement.Service"
            };

            // Send to warning alert recipients
            foreach (var recipient in _config.WarningAlertRecipients)
            {
                await _notificationService.SendNotificationAsync(
                    recipient, 
                    $"⚠️ WARNING: {title}", 
                    message, 
                    cancellationToken);
            }

            // Publish to message broker
            await _messageBroker.PublishAsync("alerts.warning", alert, cancellationToken);

            _logger.LogInformation("Warning alert sent successfully: {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send warning alert: {Title}", title);
            throw;
        }
    }

    public async Task SendInfoAlertAsync(string title, string message, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("INFO ALERT: {Title} - {Message}", title, message);

            var alert = new Alert
            {
                Id = Guid.NewGuid(),
                Level = AlertLevel.Info,
                Title = title,
                Message = message,
                Metadata = metadata ?? new Dictionary<string, object>(),
                CreatedAt = DateTime.UtcNow,
                Source = "TripManagement.Service"
            };

            // Publish to message broker only (no direct notifications for info alerts)
            await _messageBroker.PublishAsync("alerts.info", alert, cancellationToken);

            _logger.LogDebug("Info alert sent successfully: {AlertId}", alert.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send info alert: {Title}", title);
            throw;
        }
    }

    public async Task MonitorMetricsAndAlertAsync(TripManagementMetrics metrics, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Monitoring metrics for alerting conditions");

            // Check for critical conditions
            if (metrics.OverdueTrips > _config.CriticalOverdueTripsThreshold)
            {
                await SendCriticalAlertAsync(
                    "High Number of Overdue Trips",
                    $"There are currently {metrics.OverdueTrips} overdue trips, which exceeds the critical threshold of {_config.CriticalOverdueTripsThreshold}.",
                    new Dictionary<string, object>
                    {
                        ["overdueTrips"] = metrics.OverdueTrips,
                        ["threshold"] = _config.CriticalOverdueTripsThreshold,
                        ["activeTrips"] = metrics.ActiveTrips
                    },
                    cancellationToken);
            }

            if (metrics.TripsInException > _config.CriticalExceptionTripsThreshold)
            {
                await SendCriticalAlertAsync(
                    "High Number of Exception Trips",
                    $"There are currently {metrics.TripsInException} trips in exception status, which exceeds the critical threshold of {_config.CriticalExceptionTripsThreshold}.",
                    new Dictionary<string, object>
                    {
                        ["exceptionTrips"] = metrics.TripsInException,
                        ["threshold"] = _config.CriticalExceptionTripsThreshold
                    },
                    cancellationToken);
            }

            if (metrics.AvailableDrivers < _config.MinimumAvailableDrivers)
            {
                await SendWarningAlertAsync(
                    "Low Driver Availability",
                    $"Only {metrics.AvailableDrivers} drivers are currently available, which is below the minimum threshold of {_config.MinimumAvailableDrivers}.",
                    new Dictionary<string, object>
                    {
                        ["availableDrivers"] = metrics.AvailableDrivers,
                        ["minimumThreshold"] = _config.MinimumAvailableDrivers
                    },
                    cancellationToken);
            }

            if (metrics.AvailableVehicles < _config.MinimumAvailableVehicles)
            {
                await SendWarningAlertAsync(
                    "Low Vehicle Availability",
                    $"Only {metrics.AvailableVehicles} vehicles are currently available, which is below the minimum threshold of {_config.MinimumAvailableVehicles}.",
                    new Dictionary<string, object>
                    {
                        ["availableVehicles"] = metrics.AvailableVehicles,
                        ["minimumThreshold"] = _config.MinimumAvailableVehicles
                    },
                    cancellationToken);
            }

            // Check for warning conditions
            if (metrics.OnTimeDeliveryRate < _config.MinimumOnTimeDeliveryRate)
            {
                await SendWarningAlertAsync(
                    "Low On-Time Delivery Rate",
                    $"Current on-time delivery rate is {metrics.OnTimeDeliveryRate:F1}%, which is below the minimum threshold of {_config.MinimumOnTimeDeliveryRate}%.",
                    new Dictionary<string, object>
                    {
                        ["onTimeDeliveryRate"] = metrics.OnTimeDeliveryRate,
                        ["minimumThreshold"] = _config.MinimumOnTimeDeliveryRate
                    },
                    cancellationToken);
            }

            _logger.LogDebug("Metrics monitoring completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring metrics for alerting");
            throw;
        }
    }
}

public record Alert
{
    public Guid Id { get; init; }
    public AlertLevel Level { get; init; }
    public string Title { get; init; } = string.Empty;
    public string Message { get; init; } = string.Empty;
    public Dictionary<string, object> Metadata { get; init; } = new();
    public DateTime CreatedAt { get; init; }
    public string Source { get; init; } = string.Empty;
}

public enum AlertLevel
{
    Info = 0,
    Warning = 1,
    Critical = 2
}

public class AlertingConfiguration
{
    public List<Guid> CriticalAlertRecipients { get; set; } = new();
    public List<Guid> WarningAlertRecipients { get; set; } = new();
    public int CriticalOverdueTripsThreshold { get; set; } = 20;
    public int CriticalExceptionTripsThreshold { get; set; } = 15;
    public int MinimumAvailableDrivers { get; set; } = 5;
    public int MinimumAvailableVehicles { get; set; } = 5;
    public double MinimumOnTimeDeliveryRate { get; set; } = 80.0;
}
