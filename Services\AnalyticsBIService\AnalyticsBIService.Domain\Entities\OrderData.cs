using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Order data entity for analytics
/// </summary>
public class OrderData : BaseEntity
{
    public string OrderNumber { get; private set; }
    public Guid ShipperId { get; private set; }
    public Guid? CarrierId { get; private set; }
    public Guid? BrokerId { get; private set; }
    public string Status { get; private set; }
    public DateTime OrderDate { get; private set; }
    public DateTime? PickupDate { get; private set; }
    public DateTime? DeliveryDate { get; private set; }
    public string OriginCity { get; private set; }
    public string DestinationCity { get; private set; }
    public decimal Distance { get; private set; }
    public decimal Weight { get; private set; }
    public decimal Volume { get; private set; }
    public decimal OrderValue { get; private set; }
    public decimal ShippingCost { get; private set; }
    public string VehicleType { get; private set; }
    public bool IsOnTime { get; private set; }
    public int DelayHours { get; private set; }
    public decimal CustomerRating { get; private set; }

    // Private constructor for EF Core
    private OrderData() 
    {
        OrderNumber = string.Empty;
        Status = string.Empty;
        OriginCity = string.Empty;
        DestinationCity = string.Empty;
        VehicleType = string.Empty;
    }

    /// <summary>
    /// Create order data for analytics
    /// </summary>
    public static OrderData Create(
        string orderNumber,
        Guid shipperId,
        Guid? carrierId,
        Guid? brokerId,
        string status,
        DateTime orderDate,
        string originCity,
        string destinationCity,
        decimal distance,
        decimal weight,
        decimal volume,
        decimal orderValue,
        decimal shippingCost,
        string vehicleType)
    {
        return new OrderData
        {
            OrderNumber = orderNumber,
            ShipperId = shipperId,
            CarrierId = carrierId,
            BrokerId = brokerId,
            Status = status,
            OrderDate = orderDate,
            OriginCity = originCity,
            DestinationCity = destinationCity,
            Distance = distance,
            Weight = weight,
            Volume = volume,
            OrderValue = orderValue,
            ShippingCost = shippingCost,
            VehicleType = vehicleType,
            IsOnTime = true,
            DelayHours = 0,
            CustomerRating = 0
        };
    }

    /// <summary>
    /// Update pickup information
    /// </summary>
    public void UpdatePickup(DateTime pickupDate)
    {
        PickupDate = pickupDate;
    }

    /// <summary>
    /// Update delivery information
    /// </summary>
    public void UpdateDelivery(DateTime deliveryDate, bool isOnTime, int delayHours, decimal customerRating)
    {
        DeliveryDate = deliveryDate;
        IsOnTime = isOnTime;
        DelayHours = delayHours;
        CustomerRating = customerRating;
    }

    /// <summary>
    /// Update order status
    /// </summary>
    public void UpdateStatus(string status)
    {
        Status = status;
    }

    /// <summary>
    /// Calculate cost per kilometer
    /// </summary>
    public decimal GetCostPerKm()
    {
        return Distance > 0 ? ShippingCost / Distance : 0;
    }

    /// <summary>
    /// Calculate cost per kilogram
    /// </summary>
    public decimal GetCostPerKg()
    {
        return Weight > 0 ? ShippingCost / Weight : 0;
    }

    /// <summary>
    /// Get delivery performance
    /// </summary>
    public string GetDeliveryPerformance()
    {
        if (!DeliveryDate.HasValue) return "Pending";
        if (IsOnTime) return "On Time";
        if (DelayHours <= 24) return "Minor Delay";
        if (DelayHours <= 72) return "Major Delay";
        return "Critical Delay";
    }
}
