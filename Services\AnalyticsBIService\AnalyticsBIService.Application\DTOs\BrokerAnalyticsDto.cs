namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Broker dashboard data transfer object
/// </summary>
public class BrokerDashboardDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Operational Analytics
    public BrokerOperationalAnalyticsDto OperationalAnalytics { get; set; } = new();

    // RFQ Analytics
    public BrokerRFQConversionAnalyticsDto RFQAnalytics { get; set; } = new();

    // Margin Analysis
    public BrokerMarginAnalysisDto MarginAnalysis { get; set; } = new();

    // Business Growth
    public BrokerBusinessGrowthAnalyticsDto BusinessGrowth { get; set; } = new();

    // Performance Tracking
    public BrokerPerformanceTrackingDto PerformanceTracking { get; set; } = new();

    // Carrier Network
    public CarrierNetworkUtilizationDto CarrierNetwork { get; set; } = new();

    // Financial Performance
    public BrokerFinancialPerformanceDto FinancialPerformance { get; set; } = new();

    // Key Metrics Summary
    public List<KPIPerformanceDto> KeyMetrics { get; set; } = new();

    // Recent Alerts
    public List<AlertDto> RecentAlerts { get; set; } = new();
}

// BrokerOperationalAnalyticsDto is already defined in BrokerAnalyticsDTOs.cs - using that instead

/// <summary>
/// Operational trend data
/// </summary>
public class OperationalTrendDto
{
    public DateTime Date { get; set; }
    public decimal RFQConversionRate { get; set; }
    public decimal QuoteSuccessRate { get; set; }
    public decimal CarrierUtilization { get; set; }
    public decimal OperationalEfficiency { get; set; }
}

/// <summary>
/// Top carrier performance data
/// </summary>
public class TopCarrierDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public long TripsCompleted { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal RevenueGenerated { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Top route performance data
/// </summary>
public class TopRouteDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public long TripCount { get; set; }
    public decimal AverageRevenue { get; set; }
    public decimal ProfitMargin { get; set; }
    public string PopularityRank { get; set; } = string.Empty;
}

/// <summary>
/// Broker RFQ conversion analytics data transfer object
/// </summary>
public class BrokerRFQConversionAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall RFQ Metrics
    public long TotalRFQsReceived { get; set; }
    public long RFQsProcessed { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal OverallConversionRate { get; set; }
    public decimal AverageProcessingTime { get; set; }

    // Conversion Funnel
    public BrokerRFQConversionFunnelDto ConversionFunnel { get; set; } = new();

    // Transport Company Analysis
    public List<TransportCompanyRFQPerformanceDto> TransportCompanyPerformance { get; set; } = new();

    // Route Analysis
    public List<RouteRFQPerformanceDto> RoutePerformance { get; set; } = new();

    // Time-based Analysis
    public List<RFQConversionTrendDto> ConversionTrends { get; set; } = new();

    // Performance Factors
    public List<RFQPerformanceFactorDto> PerformanceFactors { get; set; } = new();

    // Optimization Recommendations
    public List<RFQOptimizationRecommendationDto> Recommendations { get; set; } = new();
}

/// <summary>
/// Broker RFQ conversion funnel
/// </summary>
public class BrokerRFQConversionFunnelDto
{
    public long RFQsReceived { get; set; }
    public long RFQsReviewed { get; set; }
    public long CarriersContacted { get; set; }
    public long QuotesReceived { get; set; }
    public long QuotesSubmitted { get; set; }
    public long QuotesAccepted { get; set; }
    public long TripsCompleted { get; set; }

    // Conversion Rates
    public decimal ReviewRate { get; set; }
    public decimal CarrierContactRate { get; set; }
    public decimal QuoteReceiptRate { get; set; }
    public decimal QuoteSubmissionRate { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal CompletionRate { get; set; }
}

/// <summary>
/// Transport company RFQ performance for broker
/// </summary>
public class TransportCompanyRFQPerformanceDto
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public long RFQsReceived { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageProcessingTime { get; set; }
    public decimal QualityScore { get; set; }
    public string RelationshipRating { get; set; } = string.Empty;
}

/// <summary>
/// RFQ performance factor analysis
/// </summary>
public class RFQPerformanceFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public string FactorType { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string ImpactDirection { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Broker quote success analytics data transfer object
/// </summary>
public class BrokerQuoteSuccessAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Quote Metrics
    public long TotalQuotesGenerated { get; set; }
    public long AcceptedQuotes { get; set; }
    public decimal OverallSuccessRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageMargin { get; set; }

    // Quote Analysis
    public List<QuoteTypeSuccessDto> QuoteTypeAnalysis { get; set; } = new();
    public List<ServiceLevelSuccessDto> ServiceLevelAnalysis { get; set; } = new();
    public List<QuoteSuccessTrendDto> SuccessTrends { get; set; } = new();

    // Competitive Analysis
    public QuoteCompetitiveAnalysisDto CompetitiveAnalysis { get; set; } = new();

    // Success Factors
    public List<QuoteSuccessFactorDto> SuccessFactors { get; set; } = new();

    // Improvement Opportunities
    public List<QuoteImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// Quote type success analysis
/// </summary>
public class QuoteTypeSuccessDto
{
    public string QuoteType { get; set; } = string.Empty;
    public long TotalQuotes { get; set; }
    public long AcceptedQuotes { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageValue { get; set; }
    public decimal AverageMargin { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Service level success analysis
/// </summary>
public class ServiceLevelSuccessDto
{
    public string ServiceLevel { get; set; } = string.Empty;
    public long TotalQuotes { get; set; }
    public long AcceptedQuotes { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal PremiumPercentage { get; set; }
    public decimal CustomerSatisfaction { get; set; }
}

/// <summary>
/// Quote success trend over time
/// </summary>
public class QuoteSuccessTrendDto
{
    public DateTime Date { get; set; }
    public long TotalQuotes { get; set; }
    public long QuotesSubmitted { get; set; } // Alias for TotalQuotes
    public long AcceptedQuotes { get; set; }
    public long QuotesAccepted { get; set; } // Alias for AcceptedQuotes
    public decimal SuccessRate { get; set; }
    public decimal AverageValue { get; set; }
    public decimal AveragePrice { get; set; } // Alias for AverageValue
    public decimal AverageMargin { get; set; }
}

/// <summary>
/// Quote competitive analysis
/// </summary>
public class QuoteCompetitiveAnalysisDto
{
    public decimal MarketSharePercentage { get; set; }
    public decimal AverageMarketPrice { get; set; }
    public decimal PriceCompetitiveness { get; set; }
    public decimal WinRateVsCompetitors { get; set; }
    public List<CompetitorQuoteAnalysisDto> CompetitorAnalysis { get; set; } = new();
}

/// <summary>
/// Competitor quote analysis
/// </summary>
public class CompetitorQuoteAnalysisDto
{
    public string CompetitorName { get; set; } = string.Empty;
    public decimal AveragePrice { get; set; }
    public decimal WinRate { get; set; }
    public decimal MarketShare { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

/// <summary>
/// Quote success factor analysis
/// </summary>
public class QuoteSuccessFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public string Factor { get; set; } = string.Empty; // Alias for FactorName
    public decimal CorrelationScore { get; set; }
    public decimal Impact { get; set; } // Impact score
    public string ImpactLevel { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty; // Single recommendation
    public List<string> OptimizationTips { get; set; } = new();
}

/// <summary>
/// Quote improvement opportunity
/// </summary>
public class QuoteImprovementOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public decimal EstimatedRevenue { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
    public decimal ROI { get; set; }
}

/// <summary>
/// Carrier network utilization data transfer object
/// </summary>
public class CarrierNetworkUtilizationDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Network Metrics
    public long TotalCarriers { get; set; }
    public long ActiveCarriers { get; set; }
    public decimal NetworkUtilizationRate { get; set; }
    public decimal AverageCarrierUtilization { get; set; }
    public decimal CarrierSatisfactionScore { get; set; }

    // Carrier Performance
    public List<CarrierUtilizationDto> CarrierPerformance { get; set; } = new();

    // Network Analysis
    public CarrierNetworkAnalysisDto NetworkAnalysis { get; set; } = new();

    // Utilization Trends
    public List<CarrierUtilizationTrendDto> UtilizationTrends { get; set; } = new();

    // Capacity Management
    public CarrierCapacityManagementDto CapacityManagement { get; set; } = new();

    // Network Optimization
    public List<NetworkOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
}

/// <summary>
/// Individual carrier utilization data
/// </summary>
public class CarrierUtilizationDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string CarrierType { get; set; } = string.Empty;
    public long TotalCapacity { get; set; }
    public long UtilizedCapacity { get; set; }
    public decimal UtilizationRate { get; set; }
    public long TripsCompleted { get; set; }
    public decimal RevenueGenerated { get; set; }
    public decimal PerformanceScore { get; set; }
    public string UtilizationRating { get; set; } = string.Empty;
}

/// <summary>
/// Carrier network analysis
/// </summary>
public class CarrierNetworkAnalysisDto
{
    public decimal NetworkDensity { get; set; }
    public decimal NetworkReach { get; set; }
    public decimal NetworkReliability { get; set; }
    public decimal NetworkEfficiency { get; set; }
    public List<NetworkGapDto> NetworkGaps { get; set; } = new();
    public List<NetworkStrengthDto> NetworkStrengths { get; set; } = new();
}

/// <summary>
/// Network gap identification
/// </summary>
public class NetworkGapDto
{
    public string GapType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string GeographicArea { get; set; } = string.Empty;
    public string ServiceType { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Network strength identification
/// </summary>
public class NetworkStrengthDto
{
    public string StrengthType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string GeographicArea { get; set; } = string.Empty;
    public decimal StrengthScore { get; set; }
    public List<string> LeverageOpportunities { get; set; } = new();
}

/// <summary>
/// Carrier utilization trend over time
/// </summary>
public class CarrierUtilizationTrendDto
{
    public DateTime Date { get; set; }
    public long ActiveCarriers { get; set; }
    public decimal NetworkUtilizationRate { get; set; }
    public decimal AverageCarrierUtilization { get; set; }
    public long TotalTrips { get; set; }
    public decimal TotalRevenue { get; set; }
}

/// <summary>
/// Carrier capacity management data
/// </summary>
public class CarrierCapacityManagementDto
{
    public long TotalNetworkCapacity { get; set; }
    public long UtilizedCapacity { get; set; }
    public long AvailableCapacity { get; set; }
    public decimal CapacityUtilizationRate { get; set; }
    public List<CapacityByRegionDto> RegionalCapacity { get; set; } = new();
    public List<CapacityByServiceTypeDto> ServiceTypeCapacity { get; set; } = new();
    public CapacityForecastDto CapacityForecast { get; set; } = new();
}

/// <summary>
/// Capacity by region
/// </summary>
public class CapacityByRegionDto
{
    public string Region { get; set; } = string.Empty;
    public long TotalCapacity { get; set; }
    public long UtilizedCapacity { get; set; }
    public decimal UtilizationRate { get; set; }
    public string CapacityStatus { get; set; } = string.Empty;
}

/// <summary>
/// Capacity by service type
/// </summary>
public class CapacityByServiceTypeDto
{
    public string ServiceType { get; set; } = string.Empty;
    public long TotalCapacity { get; set; }
    public long UtilizedCapacity { get; set; }
    public decimal UtilizationRate { get; set; }
    public decimal DemandGrowthRate { get; set; }
}

/// <summary>
/// Capacity forecast data
/// </summary>
public class CapacityForecastDto
{
    public List<CapacityForecastDataPointDto> ForecastData { get; set; } = new();
    public decimal ExpectedGrowthRate { get; set; }
    public List<CapacityRiskDto> CapacityRisks { get; set; } = new();
    public List<CapacityOpportunityDto> CapacityOpportunities { get; set; } = new();
}

/// <summary>
/// Capacity forecast data point
/// </summary>
public class CapacityForecastDataPointDto
{
    public DateTime Date { get; set; }
    public long ForecastedDemand { get; set; }
    public long AvailableCapacity { get; set; }
    public decimal UtilizationRate { get; set; }
    public string CapacityStatus { get; set; } = string.Empty;
}

/// <summary>
/// Capacity risk identification
/// </summary>
public class CapacityRiskDto
{
    public string RiskType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Probability { get; set; }
    public decimal Impact { get; set; }
    public string Severity { get; set; } = string.Empty;
    public List<string> MitigationStrategies { get; set; } = new();
}

/// <summary>
/// Capacity opportunity identification
/// </summary>
public class CapacityOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialRevenue { get; set; }
    public decimal InvestmentRequired { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// Network optimization opportunity
/// </summary>
public class NetworkOptimizationOpportunityDto
{
    public string OptimizationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public decimal EstimatedCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
}

/// <summary>
/// Broker service level analytics data transfer object
/// </summary>
public class BrokerServiceLevelAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // SLA Performance Metrics
    public decimal OverallSLACompliance { get; set; }
    public decimal ResponseTimeSLA { get; set; }
    public decimal ResolutionTimeSLA { get; set; }
    public decimal AvailabilitySLA { get; set; }

    // Service Quality Metrics
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ServiceQualityIndex { get; set; }
    public int TotalServiceRequests { get; set; }
    public int ResolvedServiceRequests { get; set; }
    public int EscalatedServiceRequests { get; set; }

    // Response Time Analytics
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan MedianResponseTime { get; set; }
    public TimeSpan MaxResponseTime { get; set; }
    public decimal ResponseTimeVariance { get; set; }

    // Service Level Breakdown
    public List<ServiceLevelBreakdownDto> ServiceLevelBreakdown { get; set; } = new();
    public List<SLAComplianceDto> SLACompliance { get; set; } = new();
    public List<ServicePerformanceTrendDto> PerformanceTrends { get; set; } = new();
}

/// <summary>
/// Service level breakdown data transfer object
/// </summary>
public class ServiceLevelBreakdownDto
{
    public string ServiceType { get; set; } = string.Empty;
    public decimal CompliancePercentage { get; set; }
    public int TotalRequests { get; set; }
    public int CompliantRequests { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
}

/// <summary>
/// SLA compliance data transfer object
/// </summary>
public class SLAComplianceDto
{
    public string SLAType { get; set; } = string.Empty;
    public decimal TargetPercentage { get; set; }
    public decimal ActualPercentage { get; set; }
    public decimal Variance { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Service performance trend data transfer object
/// </summary>
public class ServicePerformanceTrendDto
{
    public DateTime Date { get; set; }
    public decimal SLACompliance { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public int ServiceRequests { get; set; }
    public decimal CustomerSatisfaction { get; set; }
}

/// <summary>
/// Broker risk management analytics data transfer object
/// </summary>
public class BrokerRiskManagementDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Risk Assessment
    public decimal OverallRiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public decimal RiskTolerance { get; set; }

    // Operational Risk
    public OperationalRiskDto OperationalRisk { get; set; } = new();

    // Financial Risk
    public FinancialRiskDto FinancialRisk { get; set; } = new();

    // Market Risk
    public MarketRiskDto MarketRisk { get; set; } = new();

    // Compliance Risk
    public ComplianceRiskDto ComplianceRisk { get; set; } = new();

    // Risk Mitigation
    public List<RiskMitigationDto> RiskMitigations { get; set; } = new();
    public List<RiskTrendDto> RiskTrends { get; set; } = new();
    public List<RiskAlertDto> RiskAlerts { get; set; } = new();
}

/// <summary>
/// Operational risk data transfer object
/// </summary>
public class OperationalRiskDto
{
    public decimal RiskScore { get; set; }
    public decimal CarrierDefaultRate { get; set; }
    public decimal ServiceDisruptionRate { get; set; }
    public decimal CapacityShortageRisk { get; set; }
    public decimal QualityRisk { get; set; }
    public int HighRiskCarriers { get; set; }
    public int CriticalRoutes { get; set; }
}

/// <summary>
/// Financial risk data transfer object
/// </summary>
public class FinancialRiskDto
{
    public decimal RiskScore { get; set; }
    public decimal CreditRisk { get; set; }
    public decimal LiquidityRisk { get; set; }
    public decimal MarginRisk { get; set; }
    public decimal ConcentrationRisk { get; set; }
    public decimal ExposureAmount { get; set; }
    public decimal RiskAdjustedReturn { get; set; }
}

/// <summary>
/// Market risk data transfer object
/// </summary>
public class MarketRiskDto
{
    public decimal RiskScore { get; set; }
    public decimal PriceVolatilityRisk { get; set; }
    public decimal DemandFluctuationRisk { get; set; }
    public decimal CompetitionRisk { get; set; }
    public decimal RegulatoryRisk { get; set; }
    public decimal EconomicRisk { get; set; }
}

/// <summary>
/// Compliance risk data transfer object
/// </summary>
public class ComplianceRiskDto
{
    public decimal RiskScore { get; set; }
    public decimal RegulatoryComplianceRisk { get; set; }
    public decimal DataPrivacyRisk { get; set; }
    public decimal ContractualRisk { get; set; }
    public decimal AuditRisk { get; set; }
    public int ComplianceViolations { get; set; }
    public int PendingAudits { get; set; }
}

/// <summary>
/// Risk mitigation data transfer object
/// </summary>
public class RiskMitigationDto
{
    public string RiskType { get; set; } = string.Empty;
    public string MitigationStrategy { get; set; } = string.Empty;
    public decimal EffectivenessScore { get; set; }
    public DateTime ImplementationDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal CostOfMitigation { get; set; }
}

/// <summary>
/// Risk trend data transfer object
/// </summary>
public class RiskTrendDto
{
    public DateTime Date { get; set; }
    public decimal OverallRiskScore { get; set; }
    public decimal OperationalRisk { get; set; }
    public decimal FinancialRisk { get; set; }
    public decimal MarketRisk { get; set; }
    public decimal ComplianceRisk { get; set; }
}



/// <summary>
/// Broker technology utilization analytics data transfer object
/// </summary>
public class BrokerTechnologyUtilizationDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // System Usage Metrics
    public decimal SystemUtilizationRate { get; set; }
    public decimal PlatformAdoptionRate { get; set; }
    public decimal FeatureUsageRate { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalUsers { get; set; }

    // Automation Metrics
    public decimal AutomationRate { get; set; }
    public decimal ProcessAutomationEfficiency { get; set; }
    public int AutomatedTransactions { get; set; }
    public int ManualTransactions { get; set; }
    public decimal AutomationCostSavings { get; set; }

    // Technology Performance
    public decimal SystemPerformanceScore { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal SystemAvailability { get; set; }
    public decimal ErrorRate { get; set; }

    // Digital Transformation
    public decimal DigitalMaturityScore { get; set; }
    public decimal TechnologyROI { get; set; }
    public decimal InnovationIndex { get; set; }

    // Usage Analytics
    public List<FeatureUsageDto> FeatureUsage { get; set; } = new();
    public List<SystemUsageTrendDto> UsageTrends { get; set; } = new();
    public List<TechnologyMetricDto> TechnologyMetrics { get; set; } = new();
}



/// <summary>
/// System usage trend data transfer object
/// </summary>
public class SystemUsageTrendDto
{
    public DateTime Date { get; set; }
    public decimal UtilizationRate { get; set; }
    public int ActiveUsers { get; set; }
    public int Transactions { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
}

/// <summary>
/// Technology metric data transfer object
/// </summary>
public class TechnologyMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal Target { get; set; }
    public decimal Variance { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Broker market intelligence analytics data transfer object
/// </summary>
public class BrokerMarketIntelligenceDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Market Overview
    public decimal MarketShare { get; set; }
    public decimal MarketGrowthRate { get; set; }
    public decimal CompetitivePosition { get; set; }
    public decimal MarketPenetration { get; set; }

    // Competitive Analysis
    public List<CompetitorAnalysisDto> Competitors { get; set; } = new();
    public decimal CompetitiveAdvantageScore { get; set; }
    public string MarketLeadershipPosition { get; set; } = string.Empty;

    // Market Trends
    public List<MarketTrendDto> MarketTrends { get; set; } = new();
    public List<OpportunityDto> MarketOpportunities { get; set; } = new();
    public List<ThreatDto> MarketThreats { get; set; } = new();

    // Geographic Analysis
    public List<GeographicMarketDto> GeographicMarkets { get; set; } = new();
    public decimal GeographicDiversification { get; set; }

    // Customer Intelligence
    public CustomerIntelligenceDto CustomerIntelligence { get; set; } = new();

    // Pricing Intelligence
    public PricingIntelligenceDto PricingIntelligence { get; set; } = new();
}

/// <summary>
/// Competitor analysis data transfer object
/// </summary>
public class CompetitorAnalysisDto
{
    public string CompetitorName { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal CompetitiveStrength { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
    public decimal ThreatLevel { get; set; }
}



/// <summary>
/// Market opportunity data transfer object
/// </summary>
public class OpportunityDto
{
    public string OpportunityName { get; set; } = string.Empty;
    public decimal PotentialValue { get; set; }
    public decimal Probability { get; set; }
    public string TimeFrame { get; set; } = string.Empty;
    public decimal InvestmentRequired { get; set; }
    public decimal ROI { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Market threat data transfer object
/// </summary>
public class ThreatDto
{
    public string ThreatName { get; set; } = string.Empty;
    public decimal Severity { get; set; }
    public decimal Probability { get; set; }
    public decimal PotentialImpact { get; set; }
    public string TimeFrame { get; set; } = string.Empty;
    public string MitigationStrategy { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Geographic market data transfer object
/// </summary>
public class GeographicMarketDto
{
    public string Region { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal Revenue { get; set; }
    public decimal Profitability { get; set; }
    public decimal CompetitionLevel { get; set; }
    public decimal MarketPotential { get; set; }
}

/// <summary>
/// Customer intelligence data transfer object
/// </summary>
public class CustomerIntelligenceDto
{
    public int TotalCustomers { get; set; }
    public int NewCustomers { get; set; }
    public decimal CustomerRetentionRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal CustomerAcquisitionCost { get; set; }
    public List<CustomerSegmentDto> CustomerSegments { get; set; } = new();
}

/// <summary>
/// Customer segment data transfer object
/// </summary>
public class CustomerSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int CustomerCount { get; set; }
    public decimal Revenue { get; set; }
    public decimal Profitability { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal SatisfactionScore { get; set; }
}

/// <summary>
/// Pricing intelligence data transfer object
/// </summary>
public class PricingIntelligenceDto
{
    public decimal AverageMarketPrice { get; set; }
    public decimal PricePremium { get; set; }
    public decimal PriceElasticity { get; set; }
    public decimal CompetitivePricePosition { get; set; }
    public List<PricingTrendDto> PricingTrends { get; set; } = new();
    public List<PricingBenchmarkDto> PricingBenchmarks { get; set; } = new();
}

/// <summary>
/// Pricing trend data transfer object
/// </summary>
public class PricingTrendDto
{
    public DateTime Date { get; set; }
    public decimal AveragePrice { get; set; }
    public decimal PriceChange { get; set; }
    public decimal MarketPrice { get; set; }
    public decimal MarketAveragePrice { get; set; } // Alias for MarketPrice
    public decimal CompetitorPrice { get; set; }
    public decimal PriceCompetitiveness { get; set; } // Competitiveness score
    public int QuoteCount { get; set; } // Number of quotes in this period

    // Additional properties required by query handlers
    public decimal MedianPrice { get; set; }
    public decimal PriceVariability { get; set; }
    public int TransactionCount { get; set; }
}

/// <summary>
/// Pricing benchmark data transfer object
/// </summary>
public class PricingBenchmarkDto
{
    public string ServiceType { get; set; } = string.Empty;
    public decimal OurPrice { get; set; }
    public decimal MarketPrice { get; set; }
    public decimal CompetitorPrice { get; set; }
    public decimal PriceDifference { get; set; }
    public string PricePosition { get; set; } = string.Empty;
}

/// <summary>
/// Broker compliance analytics data transfer object
/// </summary>
public class BrokerComplianceAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Compliance
    public decimal OverallComplianceScore { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty;
    public decimal ComplianceImprovement { get; set; }

    // Regulatory Compliance
    public RegulatoryComplianceDto RegulatoryCompliance { get; set; } = new();

    // Data Privacy Compliance
    public DataPrivacyComplianceDto DataPrivacyCompliance { get; set; } = new();

    // Financial Compliance
    public FinancialComplianceDto FinancialCompliance { get; set; } = new();

    // Operational Compliance
    public OperationalComplianceDto OperationalCompliance { get; set; } = new();

    // Compliance Violations
    public List<ComplianceViolationDto> Violations { get; set; } = new();

    // Audit Results
    public List<AuditResultDto> AuditResults { get; set; } = new();

    // Compliance Trends
    public List<ComplianceTrendDto> ComplianceTrends { get; set; } = new();

    // Remediation Actions
    public List<RemediationActionDto> RemediationActions { get; set; } = new();
}

/// <summary>
/// Regulatory compliance data transfer object
/// </summary>
public class RegulatoryComplianceDto
{
    public decimal ComplianceScore { get; set; }
    public int TotalRequirements { get; set; }
    public int CompliantRequirements { get; set; }
    public int NonCompliantRequirements { get; set; }
    public int PendingRequirements { get; set; }
    public DateTime LastAssessment { get; set; }
    public List<RegulatoryRequirementDto> Requirements { get; set; } = new();
}

/// <summary>
/// Regulatory requirement data transfer object
/// </summary>
public class RegulatoryRequirementDto
{
    public string RequirementId { get; set; } = string.Empty;
    public string RequirementName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal ComplianceLevel { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime LastReviewed { get; set; }
    public string ResponsibleParty { get; set; } = string.Empty;
}

/// <summary>
/// Data privacy compliance data transfer object
/// </summary>
public class DataPrivacyComplianceDto
{
    public decimal ComplianceScore { get; set; }
    public bool GDPRCompliant { get; set; }
    public bool CCPACompliant { get; set; }
    public bool HIPAACompliant { get; set; }
    public int DataBreaches { get; set; }
    public int DataSubjectRequests { get; set; }
    public int ProcessedRequests { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public List<DataPrivacyMetricDto> PrivacyMetrics { get; set; } = new();
}

/// <summary>
/// Data privacy metric data transfer object
/// </summary>
public class DataPrivacyMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal Target { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Financial compliance data transfer object
/// </summary>
public class FinancialComplianceDto
{
    public decimal ComplianceScore { get; set; }
    public bool SOXCompliant { get; set; }
    public bool AMLCompliant { get; set; }
    public bool KYCCompliant { get; set; }
    public int FinancialViolations { get; set; }
    public decimal ComplianceCost { get; set; }
    public List<FinancialComplianceMetricDto> FinancialMetrics { get; set; } = new();
}

/// <summary>
/// Financial compliance metric data transfer object
/// </summary>
public class FinancialComplianceMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal Threshold { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
}

/// <summary>
/// Operational compliance data transfer object
/// </summary>
public class OperationalComplianceDto
{
    public decimal ComplianceScore { get; set; }
    public bool ISO9001Compliant { get; set; }
    public bool ISO27001Compliant { get; set; }
    public int ProcessViolations { get; set; }
    public int QualityIssues { get; set; }
    public decimal ProcessEfficiency { get; set; }
    public List<OperationalComplianceMetricDto> OperationalMetrics { get; set; } = new();
}

/// <summary>
/// Operational compliance metric data transfer object
/// </summary>
public class OperationalComplianceMetricDto
{
    public string ProcessName { get; set; } = string.Empty;
    public decimal ComplianceLevel { get; set; }
    public int Violations { get; set; }
    public DateTime LastAudit { get; set; }
    public string AuditResult { get; set; } = string.Empty;
    public string ResponsibleTeam { get; set; } = string.Empty;
}



/// <summary>
/// Audit result data transfer object
/// </summary>
public class AuditResultDto
{
    public Guid AuditId { get; set; }
    public string AuditType { get; set; } = string.Empty;
    public string AuditScope { get; set; } = string.Empty;
    public DateTime AuditDate { get; set; }
    public string AuditorName { get; set; } = string.Empty;
    public decimal OverallScore { get; set; }
    public string Result { get; set; } = string.Empty;
    public int FindingsCount { get; set; }
    public int CriticalFindings { get; set; }
    public int MajorFindings { get; set; }
    public int MinorFindings { get; set; }
    public List<AuditFindingDto> Findings { get; set; } = new();
}

/// <summary>
/// Audit finding data transfer object
/// </summary>
public class AuditFindingDto
{
    public Guid FindingId { get; set; }
    public string FindingType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string ResponsibleParty { get; set; } = string.Empty;
}

/// <summary>
/// Compliance trend data transfer object
/// </summary>
public class ComplianceTrendDto
{
    public DateTime Date { get; set; }
    public decimal OverallComplianceScore { get; set; }
    public decimal RegulatoryCompliance { get; set; }
    public decimal DataPrivacyCompliance { get; set; }
    public decimal FinancialCompliance { get; set; }
    public decimal OperationalCompliance { get; set; }
    public int ViolationCount { get; set; }
}

/// <summary>
/// Remediation action data transfer object
/// </summary>
public class RemediationActionDto
{
    public Guid ActionId { get; set; }
    public string ActionType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string ResponsibleParty { get; set; } = string.Empty;
    public decimal EstimatedCost { get; set; }
    public decimal ActualCost { get; set; }
    public decimal EffectivenessScore { get; set; }
}
