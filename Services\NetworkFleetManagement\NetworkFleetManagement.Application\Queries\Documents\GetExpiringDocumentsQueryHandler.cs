using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Documents;

public class GetExpiringDocumentsQueryHandler : IRequestHandler<GetExpiringDocumentsQuery, PagedResult<ExpiringDocumentDto>>
{
    private readonly IDocumentRepository _documentRepository;

    public GetExpiringDocumentsQueryHandler(IDocumentRepository documentRepository)
    {
        _documentRepository = documentRepository;
    }

    public async Task<PagedResult<ExpiringDocumentDto>> Handle(GetExpiringDocumentsQuery request, CancellationToken cancellationToken)
    {
        var (documents, totalCount) = await _documentRepository.GetExpiringDocumentsPagedAsync(
            request.CarrierId,
            request.PageNumber,
            request.PageSize,
            request.ExpiryThresholdDays,
            request.EntityType,
            request.DocumentType,
            request.IsExpired,
            cancellationToken);

        var documentDtos = documents.Select(doc => new ExpiringDocumentDto
        {
            DocumentId = doc.DocumentId,
            EntityType = doc.EntityType,
            EntityId = doc.EntityId,
            EntityName = doc.EntityName,
            DocumentType = doc.DocumentType.ToString(),
            DocumentName = doc.DocumentName,
            ExpiryDate = doc.ExpiryDate,
            DaysUntilExpiry = doc.DaysUntilExpiry,
            IsExpired = doc.IsExpired,
            IsVerified = doc.IsVerified,
            RejectionReason = doc.RejectionReason,
            Status = doc.IsExpired ? "Expired" : doc.DaysUntilExpiry <= 30 ? "Expiring" : "Valid",
            StatusColor = doc.IsExpired ? "Red" : doc.DaysUntilExpiry <= 30 ? "Orange" : "Green",
            Priority = doc.IsExpired ? "High" : doc.DaysUntilExpiry <= 30 ? "Medium" : "Low"
        }).ToList();

        return new PagedResult<ExpiringDocumentDto>
        {
            Items = documentDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
        };
    }
}
