using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Tests.Infrastructure;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Unit.Domain;

/// <summary>
/// Unit tests for domain value objects
/// </summary>
public class ValueObjectTests : UnitTestBase
{
    public ValueObjectTests(ITestOutputHelper output) : base(output)
    {
    }

    #region MessageContent Tests

    [Fact]
    public void MessageContent_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange & Act
        var content = new MessageContent
        {
            Subject = "Test Subject",
            Body = "Test message body",
            Language = Language.English
        };

        // Assert
        content.Subject.Should().Be("Test Subject");
        content.Body.Should().Be("Test message body");
        content.Language.Should().Be(Language.English);
    }

    [Fact]
    public void MessageContent_WithNullSubject_ShouldAllowNull()
    {
        // Arrange & Act
        var content = new MessageContent
        {
            Subject = null,
            Body = "Test message body",
            Language = Language.English
        };

        // Assert
        content.Subject.Should().BeNull();
        content.Body.Should().Be("Test message body");
    }

    [Theory]
    [InlineData(Language.English)]
    [InlineData(Language.Hindi)]
    [InlineData(Language.Kannada)]
    public void MessageContent_WithDifferentLanguages_ShouldSetCorrectly(Language language)
    {
        // Arrange & Act
        var content = new MessageContent
        {
            Subject = "Test Subject",
            Body = "Test message body",
            Language = language
        };

        // Assert
        content.Language.Should().Be(language);
    }

    [Fact]
    public void MessageContent_Equality_ShouldWorkCorrectly()
    {
        // Arrange
        var content1 = new MessageContent
        {
            Subject = "Test Subject",
            Body = "Test message body",
            Language = Language.English
        };

        var content2 = new MessageContent
        {
            Subject = "Test Subject",
            Body = "Test message body",
            Language = Language.English
        };

        var content3 = new MessageContent
        {
            Subject = "Different Subject",
            Body = "Test message body",
            Language = Language.English
        };

        // Act & Assert
        content1.Should().BeEquivalentTo(content2);
        content1.Should().NotBeEquivalentTo(content3);
    }

    #endregion

    #region ContactInfo Tests

    [Fact]
    public void ContactInfo_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange & Act
        var contactInfo = new ContactInfo
        {
            PhoneNumber = "+919876543210",
            Email = "<EMAIL>",
            WhatsAppNumber = "+919876543210",
            PushDeviceToken = "device_token_123"
        };

        // Assert
        contactInfo.PhoneNumber.Should().Be("+919876543210");
        contactInfo.Email.Should().Be("<EMAIL>");
        contactInfo.WhatsAppNumber.Should().Be("+919876543210");
        contactInfo.PushDeviceToken.Should().Be("device_token_123");
    }

    [Fact]
    public void ContactInfo_WithNullValues_ShouldAllowNulls()
    {
        // Arrange & Act
        var contactInfo = new ContactInfo
        {
            PhoneNumber = null,
            Email = "<EMAIL>",
            WhatsAppNumber = null,
            PushDeviceToken = null
        };

        // Assert
        contactInfo.PhoneNumber.Should().BeNull();
        contactInfo.Email.Should().Be("<EMAIL>");
        contactInfo.WhatsAppNumber.Should().BeNull();
        contactInfo.PushDeviceToken.Should().BeNull();
    }

    [Theory]
    [InlineData("+919876543210", true)]
    [InlineData("+1234567890", true)]
    [InlineData("9876543210", false)]
    [InlineData("+91", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void ContactInfo_IsValidPhoneNumber_ShouldValidateCorrectly(string phoneNumber, bool expectedValid)
    {
        // Arrange
        var contactInfo = new ContactInfo { PhoneNumber = phoneNumber };

        // Act
        var isValid = IsValidPhoneNumber(contactInfo.PhoneNumber);

        // Assert
        isValid.Should().Be(expectedValid);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("@domain.com", false)]
    [InlineData("user@", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void ContactInfo_IsValidEmail_ShouldValidateCorrectly(string email, bool expectedValid)
    {
        // Arrange
        var contactInfo = new ContactInfo { Email = email };

        // Act
        var isValid = IsValidEmail(contactInfo.Email);

        // Assert
        isValid.Should().Be(expectedValid);
    }

    #endregion

    #region NotificationSettings Tests

    [Fact]
    public void NotificationSettings_Creation_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var settings = new NotificationSettings();

        // Assert
        settings.EnableSms.Should().BeTrue();
        settings.EnableEmail.Should().BeTrue();
        settings.EnablePush.Should().BeTrue();
        settings.EnableWhatsApp.Should().BeTrue();
        settings.EnableVoice.Should().BeFalse();
        settings.PreferredChannels.Should().NotBeNull();
        settings.PreferredChannels.Should().BeEmpty();
    }

    [Fact]
    public void NotificationSettings_SetChannelPreferences_ShouldUpdateCorrectly()
    {
        // Arrange
        var settings = new NotificationSettings();

        // Act
        settings.EnableSms = false;
        settings.EnableEmail = true;
        settings.EnablePush = true;
        settings.EnableWhatsApp = false;
        settings.EnableVoice = true;

        // Assert
        settings.EnableSms.Should().BeFalse();
        settings.EnableEmail.Should().BeTrue();
        settings.EnablePush.Should().BeTrue();
        settings.EnableWhatsApp.Should().BeFalse();
        settings.EnableVoice.Should().BeTrue();
    }

    [Fact]
    public void NotificationSettings_SetQuietHours_ShouldUpdateCorrectly()
    {
        // Arrange
        var settings = new NotificationSettings();
        var quietStart = TimeSpan.FromHours(22); // 10 PM
        var quietEnd = TimeSpan.FromHours(7);    // 7 AM

        // Act
        settings.QuietHoursStart = quietStart;
        settings.QuietHoursEnd = quietEnd;

        // Assert
        settings.QuietHoursStart.Should().Be(quietStart);
        settings.QuietHoursEnd.Should().Be(quietEnd);
    }

    [Fact]
    public void NotificationSettings_SetPreferredChannels_ShouldUpdateCorrectly()
    {
        // Arrange
        var settings = new NotificationSettings();
        var preferredChannels = new List<NotificationChannel>
        {
            NotificationChannel.WhatsApp,
            NotificationChannel.Push,
            NotificationChannel.Email
        };

        // Act
        settings.PreferredChannels = preferredChannels;

        // Assert
        settings.PreferredChannels.Should().HaveCount(3);
        settings.PreferredChannels.Should().Contain(NotificationChannel.WhatsApp);
        settings.PreferredChannels.Should().Contain(NotificationChannel.Push);
        settings.PreferredChannels.Should().Contain(NotificationChannel.Email);
    }

    [Fact]
    public void NotificationSettings_IsChannelEnabled_ShouldReturnCorrectValue()
    {
        // Arrange
        var settings = new NotificationSettings
        {
            EnableSms = true,
            EnableEmail = false,
            EnablePush = true,
            EnableWhatsApp = false,
            EnableVoice = true
        };

        // Act & Assert
        IsChannelEnabled(settings, NotificationChannel.Sms).Should().BeTrue();
        IsChannelEnabled(settings, NotificationChannel.Email).Should().BeFalse();
        IsChannelEnabled(settings, NotificationChannel.Push).Should().BeTrue();
        IsChannelEnabled(settings, NotificationChannel.WhatsApp).Should().BeFalse();
        IsChannelEnabled(settings, NotificationChannel.Voice).Should().BeTrue();
    }

    [Theory]
    [InlineData(22, 0, 7, 0, 23, 30, true)]   // 11:30 PM (in quiet hours)
    [InlineData(22, 0, 7, 0, 2, 0, true)]     // 2:00 AM (in quiet hours)
    [InlineData(22, 0, 7, 0, 8, 0, false)]    // 8:00 AM (not in quiet hours)
    [InlineData(22, 0, 7, 0, 15, 0, false)]   // 3:00 PM (not in quiet hours)
    [InlineData(9, 0, 17, 0, 12, 0, true)]    // 12:00 PM (in quiet hours 9-17)
    [InlineData(9, 0, 17, 0, 8, 0, false)]    // 8:00 AM (not in quiet hours 9-17)
    public void NotificationSettings_IsInQuietHours_ShouldCalculateCorrectly(
        int quietStartHour, int quietStartMinute,
        int quietEndHour, int quietEndMinute,
        int checkHour, int checkMinute,
        bool expectedInQuietHours)
    {
        // Arrange
        var settings = new NotificationSettings
        {
            QuietHoursStart = new TimeSpan(quietStartHour, quietStartMinute, 0),
            QuietHoursEnd = new TimeSpan(quietEndHour, quietEndMinute, 0)
        };

        var checkTime = new TimeSpan(checkHour, checkMinute, 0);

        // Act
        var isInQuietHours = IsInQuietHours(settings, checkTime);

        // Assert
        isInQuietHours.Should().Be(expectedInQuietHours);
    }

    #endregion

    #region Helper Methods

    private bool IsValidPhoneNumber(string? phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        return phoneNumber.StartsWith("+") && phoneNumber.Length >= 10;
    }

    private bool IsValidEmail(string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        return email.Contains("@") && email.Contains(".");
    }

    private bool IsChannelEnabled(NotificationSettings settings, NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Sms => settings.EnableSms,
            NotificationChannel.Email => settings.EnableEmail,
            NotificationChannel.Push => settings.EnablePush,
            NotificationChannel.WhatsApp => settings.EnableWhatsApp,
            NotificationChannel.Voice => settings.EnableVoice,
            _ => false
        };
    }

    private bool IsInQuietHours(NotificationSettings settings, TimeSpan checkTime)
    {
        if (!settings.QuietHoursStart.HasValue || !settings.QuietHoursEnd.HasValue)
            return false;

        var start = settings.QuietHoursStart.Value;
        var end = settings.QuietHoursEnd.Value;

        if (start <= end)
        {
            // Same day quiet hours (e.g., 9 AM to 5 PM)
            return checkTime >= start && checkTime <= end;
        }
        else
        {
            // Overnight quiet hours (e.g., 10 PM to 7 AM)
            return checkTime >= start || checkTime <= end;
        }
    }

    #endregion
}
