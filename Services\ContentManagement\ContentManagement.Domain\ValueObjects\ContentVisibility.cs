﻿using ContentManagement.Domain.Enums;
using Shared.Domain.ValueObjects;

namespace ContentManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing content visibility settings
/// </summary>
public class ContentVisibility : ValueObject
{
    public VisibilityLevel Level { get; private set; }
    public List<string> AllowedRoles { get; private set; }
    public DateTime? VisibleFrom { get; private set; }
    public DateTime? VisibleUntil { get; private set; }

    private ContentVisibility()
    {
        AllowedRoles = new List<string>();
    }

    private ContentVisibility(
        VisibilityLevel level,
        List<string>? allowedRoles = null,
        DateTime? visibleFrom = null,
        DateTime? visibleUntil = null)
    {
        Level = level;
        AllowedRoles = allowedRoles ?? new List<string>();
        VisibleFrom = visibleFrom;
        VisibleUntil = visibleUntil;

        // Validation
        if (level == VisibilityLevel.RoleSpecific && !AllowedRoles.Any())
            throw new ArgumentException("Role-specific visibility requires at least one allowed role");

        if (visibleFrom.HasValue && visibleUntil.HasValue && visibleFrom.Value >= visibleUntil.Value)
            throw new ArgumentException("Visible from date must be before visible until date");
    }

    public static ContentVisibility CreatePublic(DateTime? visibleFrom = null, DateTime? visibleUntil = null)
    {
        return new ContentVisibility(VisibilityLevel.Public, null, visibleFrom, visibleUntil);
    }

    public static ContentVisibility CreateAuthenticated(DateTime? visibleFrom = null, DateTime? visibleUntil = null)
    {
        return new ContentVisibility(VisibilityLevel.Authenticated, null, visibleFrom, visibleUntil);
    }

    public static ContentVisibility CreateRoleSpecific(
        List<string> allowedRoles,
        DateTime? visibleFrom = null,
        DateTime? visibleUntil = null)
    {
        if (allowedRoles == null || !allowedRoles.Any())
            throw new ArgumentException("Role-specific visibility requires at least one allowed role");

        return new ContentVisibility(VisibilityLevel.RoleSpecific, allowedRoles, visibleFrom, visibleUntil);
    }

    public static ContentVisibility CreatePrivate(DateTime? visibleFrom = null, DateTime? visibleUntil = null)
    {
        return new ContentVisibility(VisibilityLevel.Private, null, visibleFrom, visibleUntil);
    }

    public bool IsVisibleTo(List<string> userRoles, bool isAuthenticated = true)
    {
        // Check time-based visibility
        var now = DateTime.UtcNow;
        if (VisibleFrom.HasValue && now < VisibleFrom.Value)
            return false;
        if (VisibleUntil.HasValue && now > VisibleUntil.Value)
            return false;

        // Check level-based visibility
        return Level switch
        {
            VisibilityLevel.Public => true,
            VisibilityLevel.Authenticated => isAuthenticated,
            VisibilityLevel.RoleSpecific => isAuthenticated && userRoles.Any(role => AllowedRoles.Contains(role)),
            VisibilityLevel.Private => false,
            _ => false
        };
    }

    public bool IsCurrentlyVisible()
    {
        var now = DateTime.UtcNow;
        if (VisibleFrom.HasValue && now < VisibleFrom.Value)
            return false;
        if (VisibleUntil.HasValue && now > VisibleUntil.Value)
            return false;
        return true;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Level;
        yield return string.Join(",", AllowedRoles.OrderBy(r => r));
        yield return VisibleFrom ?? DateTime.MinValue;
        yield return VisibleUntil ?? DateTime.MaxValue;
    }
}




