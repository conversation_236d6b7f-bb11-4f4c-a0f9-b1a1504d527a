using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class GetFormDefinitionQueryHandler : IRequestHandler<GetFormDefinitionQuery, FormDefinitionDto?>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<GetFormDefinitionQueryHandler> _logger;

        public GetFormDefinitionQueryHandler(
            IFormBuilderService formBuilderService,
            ILogger<GetFormDefinitionQueryHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<FormDefinitionDto?> Handle(GetFormDefinitionQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting form definition {FormId}", request.FormDefinitionId);

                var result = await _formBuilderService.GetFormDefinitionAsync(
                    request.FormDefinitionId,
                    cancellationToken);

                if (result == null)
                {
                    _logger.LogWarning("Form definition {FormId} not found", request.FormDefinitionId);
                    return null;
                }

                _logger.LogInformation("Successfully retrieved form definition {FormId}", request.FormDefinitionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting form definition {FormId}", request.FormDefinitionId);
                throw;
            }
        }
    }
}
