using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Repositories;
using TripManagement.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace TripManagement.Application.Services;

/// <summary>
/// Service for monitoring and managing delay alerts for Transport Company Portal
/// </summary>
public interface IDelayAlertMonitoringService
{
    Task<DelayAlert?> CheckForDelaysAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> ProcessActiveAlertsAsync(CancellationToken cancellationToken = default);
    Task<DelayAlert?> CreateDelayAlertAsync(
        Guid tripId,
        DelayAlertType alertType,
        TimeSpan delayDuration,
        DateTime scheduledTime,
        DelayReason delayReason,
        CancellationToken cancellationToken = default);
    Task<bool> UpdateDelayAlertAsync(
        Guid delayAlertId,
        TimeSpan newDelayDuration,
        DateTime? newEstimatedTime = null,
        CancellationToken cancellationToken = default);
    Task<bool> EscalateDelayAlertAsync(Guid delayAlertId, string reason, CancellationToken cancellationToken = default);
    Task<bool> AcknowledgeDelayAlertAsync(
        Guid delayAlertId,
        Guid acknowledgedByUserId,
        string? notes = null,
        CancellationToken cancellationToken = default);
    Task<bool> ResolveDelayAlertAsync(
        Guid delayAlertId,
        string resolutionNotes,
        DateTime? actualTime = null,
        CancellationToken cancellationToken = default);
    Task<DelayAlertAnalytics> GetDelayAnalyticsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);
}

public class DelayAlertMonitoringService : IDelayAlertMonitoringService
{
    private readonly ITripRepository _tripRepository;
    private readonly IDelayAlertRepository _delayAlertRepository;
    private readonly IDelayAlertNotificationService _notificationService;
    private readonly IETACalculationService _etaCalculationService;
    private readonly ITransportCompanyConfigurationService _configurationService;
    private readonly ILogger<DelayAlertMonitoringService> _logger;

    public DelayAlertMonitoringService(
        ITripRepository tripRepository,
        IDelayAlertRepository delayAlertRepository,
        IDelayAlertNotificationService notificationService,
        IETACalculationService etaCalculationService,
        ITransportCompanyConfigurationService configurationService,
        ILogger<DelayAlertMonitoringService> logger)
    {
        _tripRepository = tripRepository;
        _delayAlertRepository = delayAlertRepository;
        _notificationService = notificationService;
        _etaCalculationService = etaCalculationService;
        _configurationService = configurationService;
        _logger = logger;
    }

    public async Task<DelayAlert?> CheckForDelaysAsync(Guid tripId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking for delays on trip {TripId}", tripId);

        try
        {
            var trip = await _tripRepository.GetByIdAsync(tripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", tripId);
                return null;
            }

            // Get delay alert configuration for the transport company
            var configuration = await _configurationService.GetDelayAlertConfigurationAsync(
                trip.TransportCompanyId, cancellationToken);

            if (!configuration.IsEnabled)
            {
                _logger.LogDebug("Delay alerts disabled for transport company {TransportCompanyId}",
                    trip.TransportCompanyId);
                return null;
            }

            // Check for trip-level delays
            var tripDelayAlert = await CheckTripDelayAsync(trip, configuration, cancellationToken);
            if (tripDelayAlert != null)
                return tripDelayAlert;

            // Check for stop-level delays
            var stopDelayAlert = await CheckStopDelaysAsync(trip, configuration, cancellationToken);
            return stopDelayAlert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for delays on trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<List<DelayAlert>> ProcessActiveAlertsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing active delay alerts");

        try
        {
            var activeAlerts = await _delayAlertRepository.GetActiveAlertsAsync(cancellationToken);
            var processedAlerts = new List<DelayAlert>();

            foreach (var alert in activeAlerts)
            {
                try
                {
                    // Check if alert should be escalated
                    if (alert.ShouldEscalate())
                    {
                        alert.Escalate("Automatic escalation due to time threshold");
                        await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

                        // Send escalation notifications
                        await _notificationService.SendEscalationNotificationAsync(alert, cancellationToken);

                        processedAlerts.Add(alert);
                        _logger.LogInformation("Escalated delay alert {DelayAlertId} to level {EscalationLevel}",
                            alert.Id, alert.EscalationLevel);
                    }

                    // Check if alert has expired
                    if (alert.IsExpired())
                    {
                        alert.Cancel("Alert expired");
                        await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

                        processedAlerts.Add(alert);
                        _logger.LogInformation("Expired delay alert {DelayAlertId}", alert.Id);
                    }

                    // Update alert with latest trip information
                    await UpdateAlertWithLatestTripDataAsync(alert, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing delay alert {DelayAlertId}", alert.Id);
                }
            }

            return processedAlerts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing active delay alerts");
            throw;
        }
    }

    public async Task<DelayAlert?> CreateDelayAlertAsync(
        Guid tripId,
        DelayAlertType alertType,
        TimeSpan delayDuration,
        DateTime scheduledTime,
        DelayReason delayReason,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating delay alert for trip {TripId}, type {AlertType}, duration {DelayDuration}",
            tripId, alertType, delayDuration);

        try
        {
            var trip = await _tripRepository.GetByIdAsync(tripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", tripId);
                return null;
            }

            // Check if similar alert already exists
            var existingAlert = await _delayAlertRepository.GetActiveAlertForTripAsync(tripId, alertType, cancellationToken);
            if (existingAlert != null)
            {
                // Update existing alert instead of creating new one
                existingAlert.UpdateDelayDuration(delayDuration);
                await _delayAlertRepository.UpdateAsync(existingAlert, cancellationToken);
                return existingAlert;
            }

            // Get configuration
            var configuration = await _configurationService.GetDelayAlertConfigurationAsync(
                trip.TransportCompanyId, cancellationToken);

            // Determine severity based on delay duration
            var severity = DetermineSeverity(delayDuration, alertType, configuration);

            // Create alert
            var delayAlert = new DelayAlert(
                tripId,
                trip.TransportCompanyId,
                alertType,
                severity,
                GenerateAlertTitle(alertType, delayDuration),
                GenerateAlertDescription(trip, alertType, delayDuration, delayReason),
                delayDuration,
                scheduledTime,
                delayReason,
                configuration,
                shipperId: trip.ShipperId,
                currentLocation: trip.CurrentLocation,
                targetLocation: GetTargetLocation(trip, alertType));

            await _delayAlertRepository.AddAsync(delayAlert, cancellationToken);

            // Send initial notifications
            await _notificationService.SendDelayAlertCreatedNotificationAsync(delayAlert, cancellationToken);

            _logger.LogInformation("Created delay alert {DelayAlertId} for trip {TripId}",
                delayAlert.Id, tripId);

            return delayAlert;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating delay alert for trip {TripId}", tripId);
            throw;
        }
    }

    public async Task<bool> UpdateDelayAlertAsync(
        Guid delayAlertId,
        TimeSpan newDelayDuration,
        DateTime? newEstimatedTime = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(delayAlertId, cancellationToken);
            if (alert == null)
                return false;

            alert.UpdateDelayDuration(newDelayDuration, newEstimatedTime);
            await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

            // Send update notifications if significant change
            var changeThreshold = TimeSpan.FromMinutes(15);
            if (Math.Abs((newDelayDuration - alert.DelayDuration).TotalMinutes) > changeThreshold.TotalMinutes)
            {
                await _notificationService.SendDelayAlertUpdatedNotificationAsync(alert, cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delay alert {DelayAlertId}", delayAlertId);
            return false;
        }
    }

    public async Task<bool> EscalateDelayAlertAsync(Guid delayAlertId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(delayAlertId, cancellationToken);
            if (alert == null)
                return false;

            alert.Escalate(reason);
            await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

            await _notificationService.SendEscalationNotificationAsync(alert, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error escalating delay alert {DelayAlertId}", delayAlertId);
            return false;
        }
    }

    public async Task<bool> AcknowledgeDelayAlertAsync(
        Guid delayAlertId,
        Guid acknowledgedByUserId,
        string? notes = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(delayAlertId, cancellationToken);
            if (alert == null)
                return false;

            alert.Acknowledge(acknowledgedByUserId, notes);
            await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

            await _notificationService.SendDelayAlertAcknowledgedNotificationAsync(alert, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging delay alert {DelayAlertId}", delayAlertId);
            return false;
        }
    }

    public async Task<bool> ResolveDelayAlertAsync(
        Guid delayAlertId,
        string resolutionNotes,
        DateTime? actualTime = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _delayAlertRepository.GetByIdAsync(delayAlertId, cancellationToken);
            if (alert == null)
                return false;

            alert.Resolve(resolutionNotes, actualTime);
            await _delayAlertRepository.UpdateAsync(alert, cancellationToken);

            await _notificationService.SendDelayAlertResolvedNotificationAsync(alert, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving delay alert {DelayAlertId}", delayAlertId);
            return false;
        }
    }

    public async Task<DelayAlertAnalytics> GetDelayAnalyticsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _delayAlertRepository.GetAnalyticsAsync(transportCompanyId, fromDate, toDate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delay analytics for transport company {TransportCompanyId}",
                transportCompanyId);
            throw;
        }
    }

    private async Task<DelayAlert?> CheckTripDelayAsync(
        Trip trip,
        DelayAlertConfiguration configuration,
        CancellationToken cancellationToken)
    {
        if (!trip.EstimatedEndTime.HasValue)
            return null;

        var now = DateTime.UtcNow;
        var scheduledEndTime = trip.EstimatedEndTime.Value;

        if (now <= scheduledEndTime)
            return null; // No delay yet

        var delayDuration = now - scheduledEndTime;
        var threshold = configuration.GetThresholdForSeverity(DelayAlertType.TripDelay, DelayAlertSeverity.Low);

        if (threshold == null || delayDuration < threshold.Threshold)
            return null; // Delay not significant enough

        // Check if alert already exists
        var existingAlert = await _delayAlertRepository.GetActiveAlertForTripAsync(
            trip.Id, DelayAlertType.TripDelay, cancellationToken);

        if (existingAlert != null)
        {
            // Update existing alert
            existingAlert.UpdateDelayDuration(delayDuration);
            await _delayAlertRepository.UpdateAsync(existingAlert, cancellationToken);
            return existingAlert;
        }

        // Create new alert
        return await CreateDelayAlertAsync(
            trip.Id,
            DelayAlertType.TripDelay,
            delayDuration,
            scheduledEndTime,
            DetermineDelayReason(trip),
            cancellationToken);
    }

    private async Task<DelayAlert?> CheckStopDelaysAsync(
        Trip trip,
        DelayAlertConfiguration configuration,
        CancellationToken cancellationToken)
    {
        var currentStop = trip.Stops.FirstOrDefault(s => s.Status == TripStopStatus.Pending);
        if (currentStop?.ScheduledArrival == null)
            return null;

        var now = DateTime.UtcNow;
        var scheduledArrival = currentStop.ScheduledArrival.Value;

        if (now <= scheduledArrival)
            return null; // No delay yet

        var delayDuration = now - scheduledArrival;
        var alertType = currentStop.Type == TripStopType.Pickup ? DelayAlertType.PickupDelay : DelayAlertType.DeliveryDelay;
        var threshold = configuration.GetThresholdForSeverity(alertType, DelayAlertSeverity.Low);

        if (threshold == null || delayDuration < threshold.Threshold)
            return null; // Delay not significant enough

        // Check if alert already exists
        var existingAlert = await _delayAlertRepository.GetActiveAlertForTripStopAsync(
            currentStop.Id, alertType, cancellationToken);

        if (existingAlert != null)
        {
            // Update existing alert
            existingAlert.UpdateDelayDuration(delayDuration);
            await _delayAlertRepository.UpdateAsync(existingAlert, cancellationToken);
            return existingAlert;
        }

        // Create new alert
        return await CreateDelayAlertAsync(
            trip.Id,
            alertType,
            delayDuration,
            scheduledArrival,
            DetermineDelayReason(trip),
            cancellationToken);
    }

    private async Task UpdateAlertWithLatestTripDataAsync(DelayAlert alert, CancellationToken cancellationToken)
    {
        var trip = await _tripRepository.GetByIdAsync(alert.TripId, cancellationToken);
        if (trip?.CurrentLocation != null)
        {
            var targetLocation = GetTargetLocation(trip, alert.AlertType);
            var distanceRemaining = targetLocation != null
                ? trip.CurrentLocation.CalculateDistanceKm(targetLocation)
                : (double?)null;

            alert.UpdateLocation(trip.CurrentLocation, distanceRemaining);
            await _delayAlertRepository.UpdateAsync(alert, cancellationToken);
        }
    }

    private DelayAlertSeverity DetermineSeverity(
        TimeSpan delayDuration,
        DelayAlertType alertType,
        DelayAlertConfiguration configuration)
    {
        var thresholds = configuration.DelayThresholds
            .Where(t => t.AlertType == alertType && t.IsEnabled)
            .OrderByDescending(t => t.Severity)
            .ToList();

        foreach (var threshold in thresholds)
        {
            if (delayDuration >= threshold.Threshold)
                return threshold.Severity;
        }

        return DelayAlertSeverity.Low;
    }

    private DelayReason DetermineDelayReason(Trip trip)
    {
        // This could be enhanced with ML or rule-based logic
        // For now, return a default based on trip status
        return trip.Status switch
        {
            TripStatus.Exception => DelayReason.Other,
            _ => DelayReason.Unknown
        };
    }

    private string GenerateAlertTitle(DelayAlertType alertType, TimeSpan delayDuration)
    {
        var delayMinutes = (int)delayDuration.TotalMinutes;
        return alertType switch
        {
            DelayAlertType.TripDelay => $"Trip Delayed by {delayMinutes} minutes",
            DelayAlertType.PickupDelay => $"Pickup Delayed by {delayMinutes} minutes",
            DelayAlertType.DeliveryDelay => $"Delivery Delayed by {delayMinutes} minutes",
            DelayAlertType.StopDelay => $"Stop Delayed by {delayMinutes} minutes",
            _ => $"Delay Alert - {delayMinutes} minutes"
        };
    }

    private string GenerateAlertDescription(
        Trip trip,
        DelayAlertType alertType,
        TimeSpan delayDuration,
        DelayReason delayReason)
    {
        var delayMinutes = (int)delayDuration.TotalMinutes;
        return $"Trip {trip.TripNumber} is experiencing a {alertType.ToString().ToLower()} " +
               $"of {delayMinutes} minutes. Reason: {delayReason}. " +
               $"Current status: {trip.Status}. Please take appropriate action.";
    }

    private Location? GetTargetLocation(Trip trip, DelayAlertType alertType)
    {
        return alertType switch
        {
            DelayAlertType.TripDelay => trip.Route?.EndLocation,
            DelayAlertType.PickupDelay => trip.Stops.FirstOrDefault(s => s.Type == TripStopType.Pickup && s.Status == TripStopStatus.Pending)?.Location,
            DelayAlertType.DeliveryDelay => trip.Stops.FirstOrDefault(s => s.Type == TripStopType.Delivery && s.Status == TripStopStatus.Pending)?.Location,
            DelayAlertType.StopDelay => trip.Stops.FirstOrDefault(s => s.Status == TripStopStatus.Pending)?.Location,
            _ => null
        };
    }
}
