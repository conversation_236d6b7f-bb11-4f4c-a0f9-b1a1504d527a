using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Interfaces;

public interface IAuditLogRepository
{
    Task<AuditLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<AuditLog>> GetAuditTrailAsync(
        Guid? userId = null,
        string? entityType = null,
        Guid? entityId = null,
        AuditEventType? eventType = null,
        AuditSeverity? minSeverity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default);
    
    Task<int> GetAuditTrailCountAsync(
        Guid? userId = null,
        string? entityType = null,
        Guid? entityId = null,
        AuditEventType? eventType = null,
        AuditSeverity? minSeverity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);
    
    Task<List<AuditLog>> GetSecurityEventsAsync(
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);
    
    Task<List<AuditLog>> GetExpiredAuditLogsAsync(CancellationToken cancellationToken = default);
    
    Task<AuditLog> AddAsync(AuditLog auditLog, CancellationToken cancellationToken = default);
    Task<List<AuditLog>> AddRangeAsync(List<AuditLog> auditLogs, CancellationToken cancellationToken = default);
    Task DeleteAsync(AuditLog auditLog, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(List<AuditLog> auditLogs, CancellationToken cancellationToken = default);
}

public interface IComplianceReportRepository
{
    Task<ComplianceReport?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<ComplianceReport?> GetByReportNumberAsync(string reportNumber, CancellationToken cancellationToken = default);
    Task<List<ComplianceReport>> GetReportsAsync(
        ComplianceStandard? standard = null,
        ComplianceStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? generatedBy = null,
        bool? isAutomated = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    Task<int> GetReportsCountAsync(
        ComplianceStandard? standard = null,
        ComplianceStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? generatedBy = null,
        bool? isAutomated = null,
        CancellationToken cancellationToken = default);
    
    Task<List<ComplianceReport>> GetPendingReportsAsync(CancellationToken cancellationToken = default);
    Task<ComplianceReport> AddAsync(ComplianceReport report, CancellationToken cancellationToken = default);
    Task UpdateAsync(ComplianceReport report, CancellationToken cancellationToken = default);
    Task DeleteAsync(ComplianceReport report, CancellationToken cancellationToken = default);
}

public interface IServiceProviderRatingRepository
{
    Task<ServiceProviderRating?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<ServiceProviderRating>> GetRatingsAsync(
        Guid? shipperId = null,
        Guid? transportCompanyId = null,
        RatingStatus? status = null,
        decimal? minRating = null,
        decimal? maxRating = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);
    
    Task<int> GetRatingsCountAsync(
        Guid? shipperId = null,
        Guid? transportCompanyId = null,
        RatingStatus? status = null,
        decimal? minRating = null,
        decimal? maxRating = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
    
    Task<List<ServiceProviderRating>> GetRatingsByTransportCompanyAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);
    
    Task<ServiceProviderRating> AddAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default);
    Task UpdateAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default);
    Task DeleteAsync(ServiceProviderRating rating, CancellationToken cancellationToken = default);
}

public interface IPreferredProviderNetworkRepository
{
    Task<PreferredProviderNetwork?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<PreferredProviderNetwork>> GetByShipperIdAsync(Guid shipperId, CancellationToken cancellationToken = default);
    Task<PreferredProviderNetwork?> GetByShipperAndProviderAsync(
        Guid shipperId,
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);
    
    Task<PreferredProviderNetwork> AddAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default);
    Task UpdateAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default);
    Task DeleteAsync(PreferredProviderNetwork network, CancellationToken cancellationToken = default);
}
