using FluentValidation;

namespace TripManagement.Application.Commands.ReassignDriver;

public class ReassignDriverCommandValidator : AbstractValidator<ReassignDriverCommand>
{
    public ReassignDriverCommandValidator()
    {
        RuleFor(x => x.TripId)
            .NotEmpty()
            .WithMessage("Trip ID is required");

        RuleFor(x => x.NewDriverId)
            .NotEmpty()
            .WithMessage("New driver ID is required");

        RuleFor(x => x.Reason)
            .NotEmpty()
            .WithMessage("Reassignment reason is required")
            .MaximumLength(500)
            .WithMessage("Reassignment reason cannot exceed 500 characters");

        RuleFor(x => x.ReassignedBy)
            .NotEmpty()
            .WithMessage("Reassigned by user ID is required");

        RuleFor(x => x.AdditionalNotes)
            .MaximumLength(1000)
            .WithMessage("Additional notes cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.AdditionalNotes));
    }
}
