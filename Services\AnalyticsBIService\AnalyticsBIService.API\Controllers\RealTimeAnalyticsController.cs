using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Real-time analytics controller for live metrics, alerts, and notifications
/// </summary>
[ApiController]
[Route("api/realtime")]
[Authorize]
public class RealTimeAnalyticsController : ControllerBase
{
    private readonly IRealTimeAnalyticsService _realTimeAnalyticsService;
    private readonly INotificationService _notificationService;
    private readonly IThresholdMonitoringService _thresholdMonitoringService;
    private readonly IHubContext<AnalyticsHub> _hubContext;
    private readonly ILogger<RealTimeAnalyticsController> _logger;

    public RealTimeAnalyticsController(
        IRealTimeAnalyticsService realTimeAnalyticsService,
        INotificationService notificationService,
        IThresholdMonitoringService thresholdMonitoringService,
        IHubContext<AnalyticsHub> hubContext,
        ILogger<RealTimeAnalyticsController> logger)
    {
        _realTimeAnalyticsService = realTimeAnalyticsService;
        _notificationService = notificationService;
        _thresholdMonitoringService = thresholdMonitoringService;
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// Get real-time KPIs for the current user
    /// </summary>
    /// <returns>Real-time KPI data</returns>
    [HttpGet("kpis")]
    public async Task<ActionResult<List<RealTimeKPIDto>>> GetRealTimeKPIs()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var kpis = await _realTimeAnalyticsService.GetRealTimeKPIsAsync(userId, userRole);
            return Ok(kpis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time KPIs");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get real-time metrics for the current user
    /// </summary>
    /// <returns>Real-time metrics data</returns>
    [HttpGet("metrics")]
    public async Task<ActionResult<RealTimeMetricsDto>> GetRealTimeMetrics()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var metrics = await _realTimeAnalyticsService.GetRealTimeMetricsAsync(userId, userRole);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get active alerts for the current user
    /// </summary>
    /// <returns>Active alerts</returns>
    [HttpGet("alerts")]
    public async Task<ActionResult<List<AlertDto>>> GetActiveAlerts()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var alerts = await _realTimeAnalyticsService.GetActiveAlertsAsync(userId, userRole);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process a real-time analytics event
    /// </summary>
    /// <param name="eventDto">Analytics event data</param>
    /// <returns>Success status</returns>
    [HttpPost("events")]
    public async Task<ActionResult> ProcessEvent([FromBody] AnalyticsEventDto eventDto)
    {
        try
        {
            var analyticsEvent = new AnalyticsEvent(
                eventDto.EventName,
                Enum.Parse<AnalyticsEventType>(eventDto.EventType),
                DataSourceType.External,
                eventDto.UserId,
                properties: eventDto.Properties,
                sessionId: eventDto.SessionId,
                ipAddress: eventDto.IpAddress,
                userAgent: eventDto.UserAgent
            );

            await _realTimeAnalyticsService.ProcessRealTimeEventAsync(analyticsEvent);

            // Broadcast update to connected clients
            await _hubContext.Clients.User(eventDto.UserId.ToString())
                .SendAsync("MetricUpdated", new { EventName = eventDto.EventName, Timestamp = DateTime.UtcNow });

            return Ok(new { Message = "Event processed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing real-time event");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get notifications for the current user
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Notifications</returns>
    [HttpGet("notifications")]
    public async Task<ActionResult<List<NotificationDto>>> GetNotifications(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var userId = GetCurrentUserId();
            var notifications = await _notificationService.GetNotificationsAsync(userId, page, pageSize);
            return Ok(notifications);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notifications");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Mark a notification as read
    /// </summary>
    /// <param name="notificationId">Notification ID</param>
    /// <returns>Success status</returns>
    [HttpPut("notifications/{notificationId}/read")]
    public async Task<ActionResult> MarkNotificationAsRead(Guid notificationId)
    {
        try
        {
            await _notificationService.MarkNotificationAsReadAsync(notificationId);
            return Ok(new { Message = "Notification marked as read" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification as read");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get notification preferences for the current user
    /// </summary>
    /// <returns>Notification preferences</returns>
    [HttpGet("notifications/preferences")]
    public async Task<ActionResult<NotificationPreferencesDto>> GetNotificationPreferences()
    {
        try
        {
            var userId = GetCurrentUserId();
            var preferences = await _notificationService.GetNotificationPreferencesAsync(userId);
            return Ok(preferences);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification preferences");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update notification preferences for the current user
    /// </summary>
    /// <param name="preferences">Notification preferences</param>
    /// <returns>Success status</returns>
    [HttpPut("notifications/preferences")]
    public async Task<ActionResult> UpdateNotificationPreferences([FromBody] NotificationPreferencesDto preferences)
    {
        try
        {
            var userId = GetCurrentUserId();
            preferences.UserId = userId.ToString();

            await _notificationService.UpdateNotificationPreferencesAsync(userId, preferences);
            return Ok(new { Message = "Notification preferences updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification preferences");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get thresholds for the current user
    /// </summary>
    /// <returns>User thresholds</returns>
    [HttpGet("thresholds")]
    public async Task<ActionResult<List<AnalyticsBIService.Domain.Entities.Threshold>>> GetThresholds()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var thresholds = await _thresholdMonitoringService.GetThresholdsAsync(userId, userRole);
            return Ok(thresholds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting thresholds");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new threshold
    /// </summary>
    /// <param name="request">Threshold creation request</param>
    /// <returns>Success status</returns>
    [HttpPost("thresholds")]
    public async Task<ActionResult> CreateThreshold([FromBody] CreateThresholdRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var threshold = new AnalyticsBIService.Domain.Entities.Threshold(
                request.Name,
                request.MetricType,
                request.WarningThreshold,
                request.CriticalThreshold,
                request.ComparisonOperator,
                request.EntityType,
                request.UserRole,
                userId,
                request.ThresholdType,
                request.NotificationRecipients,
                request.Configuration
            );

            await _thresholdMonitoringService.CreateThresholdAsync(threshold);
            return Ok(new { Message = "Threshold created successfully", ThresholdId = threshold.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating threshold");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update an existing threshold
    /// </summary>
    /// <param name="thresholdId">Threshold ID</param>
    /// <param name="request">Updated threshold data</param>
    /// <returns>Success status</returns>
    [HttpPut("thresholds/{thresholdId}")]
    public async Task<ActionResult> UpdateThreshold(Guid thresholdId, [FromBody] UpdateThresholdRequest request)
    {
        try
        {
            var threshold = new AnalyticsBIService.Domain.Entities.Threshold(
                request.Name,
                request.MetricType,
                request.WarningThreshold,
                request.CriticalThreshold,
                request.ComparisonOperator,
                request.EntityType,
                request.UserRole,
                GetCurrentUserId(),
                request.ThresholdType,
                request.NotificationRecipients,
                request.Configuration
            );

            // Set the ID for update
            var idProperty = typeof(AnalyticsBIService.Domain.Entities.Threshold).GetProperty("Id");
            idProperty?.SetValue(threshold, thresholdId);

            await _thresholdMonitoringService.UpdateThresholdAsync(threshold);
            return Ok(new { Message = "Threshold updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating threshold");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete a threshold
    /// </summary>
    /// <param name="thresholdId">Threshold ID</param>
    /// <returns>Success status</returns>
    [HttpDelete("thresholds/{thresholdId}")]
    public async Task<ActionResult> DeleteThreshold(Guid thresholdId)
    {
        try
        {
            await _thresholdMonitoringService.DeleteThresholdAsync(thresholdId);
            return Ok(new { Message = "Threshold deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting threshold");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Start real-time monitoring (Admin only)
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("monitoring/start")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> StartMonitoring()
    {
        try
        {
            await _realTimeAnalyticsService.StartRealTimeMonitoringAsync();
            return Ok(new { Message = "Real-time monitoring started successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting real-time monitoring");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Stop real-time monitoring (Admin only)
    /// </summary>
    /// <returns>Success status</returns>
    [HttpPost("monitoring/stop")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> StopMonitoring()
    {
        try
        {
            await _realTimeAnalyticsService.StopRealTimeMonitoringAsync();
            return Ok(new { Message = "Real-time monitoring stopped successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping real-time monitoring");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Send test notification (Admin only)
    /// </summary>
    /// <param name="testNotification">Test notification data</param>
    /// <returns>Success status</returns>
    [HttpPost("notifications/test")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> SendTestNotification([FromBody] TestNotificationDto testNotification)
    {
        try
        {
            await _notificationService.SendKPINotificationAsync(
                testNotification.UserId,
                testNotification.KPIName,
                testNotification.CurrentValue,
                testNotification.TargetValue);

            return Ok(new { Message = "Test notification sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test notification");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get real-time analytics status (Admin only)
    /// </summary>
    /// <returns>Analytics status</returns>
    [HttpGet("status")]
    [Authorize(Roles = "Admin")]
    public ActionResult<object> GetStatus()
    {
        try
        {
            return Ok(new
            {
                Status = "Running",
                Timestamp = DateTime.UtcNow,
                Services = new
                {
                    RealTimeAnalytics = "Active",
                    ThresholdMonitoring = "Active",
                    NotificationService = "Active"
                },
                Metrics = new
                {
                    ActiveConnections = 0, // This would be tracked
                    EventsProcessedToday = 0, // This would be tracked
                    AlertsGeneratedToday = 0, // This would be tracked
                    NotificationsSentToday = 0 // This would be tracked
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time analytics status");
            return StatusCode(500, "Internal server error");
        }
    }

    // Helper methods
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user ID");
    }

    private UserRole GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst(ClaimTypes.Role)?.Value;
        if (Enum.TryParse<UserRole>(roleClaim, out var userRole))
        {
            return userRole;
        }
        return UserRole.TransportCompany; // Default role
    }
}

/// <summary>
/// SignalR Hub for real-time analytics updates
/// </summary>
public class AnalyticsHub : Hub
{
    private readonly ILogger<AnalyticsHub> _logger;

    public AnalyticsHub(ILogger<AnalyticsHub> logger)
    {
        _logger = logger;
    }

    public async Task JoinUserGroup(string userId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
        _logger.LogDebug("User {UserId} joined real-time analytics group", userId);
    }

    public async Task LeaveUserGroup(string userId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
        _logger.LogDebug("User {UserId} left real-time analytics group", userId);
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogDebug("Client {ConnectionId} connected to analytics hub", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogDebug("Client {ConnectionId} disconnected from analytics hub", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }
}

// Supporting DTOs
public class TestNotificationDto
{
    public Guid UserId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal TargetValue { get; set; }
}

public class CreateThresholdRequest
{
    public string Name { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public decimal WarningThreshold { get; set; }
    public decimal CriticalThreshold { get; set; }
    public AnalyticsBIService.Domain.Enums.ComparisonOperator ComparisonOperator { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public UserRole UserRole { get; set; }
    public ThresholdType ThresholdType { get; set; } = ThresholdType.Performance;
    public List<Guid>? NotificationRecipients { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
}

public class UpdateThresholdRequest
{
    public string Name { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public decimal WarningThreshold { get; set; }
    public decimal CriticalThreshold { get; set; }
    public AnalyticsBIService.Domain.Enums.ComparisonOperator ComparisonOperator { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public UserRole UserRole { get; set; }
    public ThresholdType ThresholdType { get; set; } = ThresholdType.Performance;
    public List<Guid>? NotificationRecipients { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
}
