using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class DriverVehicleAssignmentConfiguration : IEntityTypeConfiguration<DriverVehicleAssignment>
{
    public void Configure(EntityTypeBuilder<DriverVehicleAssignment> builder)
    {
        builder.ToTable("driver_vehicle_assignments");

        builder.HasKey(a => a.Id);

        builder.Property(a => a.Id)
            .ValueGeneratedNever();

        builder.Property(a => a.DriverId)
            .IsRequired();

        builder.Property(a => a.VehicleId)
            .IsRequired();

        builder.Property(a => a.AssignedAt)
            .IsRequired();

        builder.Property(a => a.UnassignedAt);

        builder.Property(a => a.IsActive)
            .IsRequired();

        builder.Property(a => a.AssignmentNotes)
            .HasMaxLength(500);

        builder.Property(a => a.UnassignmentReason)
            .HasMaxLength(500);

        builder.Property(a => a.AssignedBy)
            .IsRequired();

        builder.Property(a => a.UnassignedBy);

        builder.Property(a => a.CreatedAt)
            .IsRequired();

        builder.Property(a => a.UpdatedAt);

        // Indexes
        builder.HasIndex(a => a.DriverId);

        builder.HasIndex(a => a.VehicleId);

        builder.HasIndex(a => a.IsActive);

        builder.HasIndex(a => a.AssignedAt);

        builder.HasIndex(a => new { a.DriverId, a.VehicleId, a.IsActive });
    }
}

public class VehicleMaintenanceRecordConfiguration : IEntityTypeConfiguration<VehicleMaintenanceRecord>
{
    public void Configure(EntityTypeBuilder<VehicleMaintenanceRecord> builder)
    {
        builder.ToTable("vehicle_maintenance_records");

        builder.HasKey(m => m.Id);

        builder.Property(m => m.Id)
            .ValueGeneratedNever();

        builder.Property(m => m.VehicleId)
            .IsRequired();

        builder.Property(m => m.MaintenanceDate)
            .IsRequired();

        builder.Property(m => m.Description)
            .HasMaxLength(1000);

        builder.Property(m => m.Cost)
            .HasPrecision(10, 2);

        builder.Property(m => m.ServiceProvider)
            .HasMaxLength(200);

        builder.Property(m => m.InvoiceNumber)
            .HasMaxLength(50);

        builder.Property(m => m.OdometerReading);

        builder.Property(m => m.PartsReplaced)
            .HasMaxLength(1000);

        builder.Property(m => m.NextMaintenanceNotes)
            .HasMaxLength(500);

        builder.Property(m => m.NextMaintenanceDate);

        builder.Property(m => m.IsWarrantyWork)
            .IsRequired();

        builder.Property(m => m.WarrantyDetails)
            .HasMaxLength(500);

        builder.Property(m => m.CreatedAt)
            .IsRequired();

        builder.Property(m => m.UpdatedAt);

        // Indexes
        builder.HasIndex(m => m.VehicleId);

        builder.HasIndex(m => m.MaintenanceDate);

        builder.HasIndex(m => m.NextMaintenanceDate);

        builder.HasIndex(m => m.IsWarrantyWork);
    }
}

public class NetworkPerformanceRecordConfiguration : IEntityTypeConfiguration<NetworkPerformanceRecord>
{
    public void Configure(EntityTypeBuilder<NetworkPerformanceRecord> builder)
    {
        builder.ToTable("network_performance_records");

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Id)
            .ValueGeneratedNever();

        builder.Property(p => p.NetworkId)
            .IsRequired();

        builder.Property(p => p.TripId);

        builder.Property(p => p.OrderId);

        builder.Property(p => p.RecordDate)
            .IsRequired();

        builder.Property(p => p.QuoteAmount)
            .HasPrecision(10, 2);

        builder.Property(p => p.FinalAmount)
            .HasPrecision(10, 2);

        builder.Property(p => p.WasOnTime)
            .IsRequired();

        builder.Property(p => p.DelayMinutes);

        builder.Property(p => p.CustomerRating)
            .HasPrecision(3, 2);

        builder.Property(p => p.CustomerFeedback)
            .HasMaxLength(1000);

        builder.Property(p => p.WasCancelled)
            .IsRequired();

        builder.Property(p => p.CancellationReason)
            .HasMaxLength(500);

        builder.Property(p => p.PenaltyAmount)
            .HasPrecision(10, 2);

        builder.Property(p => p.BonusAmount)
            .HasPrecision(10, 2);

        builder.Property(p => p.Notes)
            .HasMaxLength(1000);

        builder.Property(p => p.CreatedAt)
            .IsRequired();

        builder.Property(p => p.UpdatedAt);

        // Indexes
        builder.HasIndex(p => p.NetworkId);

        builder.HasIndex(p => p.TripId);

        builder.HasIndex(p => p.OrderId);

        builder.HasIndex(p => p.RecordDate);

        builder.HasIndex(p => p.WasOnTime);

        builder.HasIndex(p => p.WasCancelled);
    }
}
