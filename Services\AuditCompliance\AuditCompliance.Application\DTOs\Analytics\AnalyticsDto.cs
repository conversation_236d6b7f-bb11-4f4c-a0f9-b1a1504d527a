using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.DTOs.Analytics;

/// <summary>
/// Input for compliance risk prediction
/// </summary>
public class ComplianceRiskInputDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? OrganizationId { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
    public DateTime? EvaluationDate { get; set; }
}

/// <summary>
/// Compliance risk prediction result
/// </summary>
public class ComplianceRiskPredictionDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public float RiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty; // Low, Medium, High, Critical
    public float Confidence { get; set; }
    public List<RiskFactorDto> RiskFactors { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public DateTime PredictionDate { get; set; }
}

/// <summary>
/// Risk factor contributing to compliance risk
/// </summary>
public class RiskFactorDto
{
    public string Name { get; set; } = string.Empty;
    public float Impact { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}

/// <summary>
/// Violation prediction result
/// </summary>
public class ViolationPredictionDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public ComplianceStandard Standard { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public float Probability { get; set; }
    public DateTime PredictedDate { get; set; }
    public string Severity { get; set; } = string.Empty;
    public List<string> PreventionActions { get; set; } = new();
}

/// <summary>
/// Input for compliance trend analysis
/// </summary>
public class ComplianceTrendInputDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Guid? OrganizationId { get; set; }
    public List<ComplianceStandard>? Standards { get; set; }
    public List<string>? EntityTypes { get; set; }
    public string? GroupBy { get; set; } // Day, Week, Month
}

/// <summary>
/// Compliance trend analysis result
/// </summary>
public class ComplianceTrendAnalysisDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<ComplianceTrendDataPointDto> TrendData { get; set; } = new();
    public ComplianceTrendSummaryDto Summary { get; set; } = new();
    public List<ComplianceTrendInsightDto> Insights { get; set; } = new();
}

/// <summary>
/// Single data point in compliance trend
/// </summary>
public class ComplianceTrendDataPointDto
{
    public DateTime Date { get; set; }
    public float ComplianceScore { get; set; }
    public int ViolationCount { get; set; }
    public int AuditCount { get; set; }
    public Dictionary<string, float> StandardScores { get; set; } = new();
}

/// <summary>
/// Summary of compliance trend analysis
/// </summary>
public class ComplianceTrendSummaryDto
{
    public float AverageComplianceScore { get; set; }
    public float TrendDirection { get; set; } // Positive = improving, Negative = declining
    public int TotalViolations { get; set; }
    public int TotalAudits { get; set; }
    public string OverallTrend { get; set; } = string.Empty; // Improving, Stable, Declining
}

/// <summary>
/// Insight from compliance trend analysis
/// </summary>
public class ComplianceTrendInsightDto
{
    public string Type { get; set; } = string.Empty; // Pattern, Anomaly, Recommendation
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public DateTime? RelevantDate { get; set; }
}

/// <summary>
/// Compliance anomaly detection result
/// </summary>
public class ComplianceAnomalyDto
{
    public Guid Id { get; set; }
    public DateTime DetectedAt { get; set; }
    public string AnomalyType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public float AnomalyScore { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Context { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// Compliance insights and recommendations
/// </summary>
public class ComplianceInsightsDto
{
    public DateTime GeneratedAt { get; set; }
    public Guid? OrganizationId { get; set; }
    public ComplianceOverviewDto Overview { get; set; } = new();
    public List<ComplianceInsightDto> Insights { get; set; } = new();
    public List<ComplianceRecommendationDto> Recommendations { get; set; } = new();
    public List<ComplianceMetricDto> KeyMetrics { get; set; } = new();
}

/// <summary>
/// Compliance overview
/// </summary>
public class ComplianceOverviewDto
{
    public float OverallScore { get; set; }
    public int TotalEntities { get; set; }
    public int CompliantEntities { get; set; }
    public int NonCompliantEntities { get; set; }
    public int PendingReviews { get; set; }
    public Dictionary<string, float> StandardScores { get; set; } = new();
}

/// <summary>
/// Individual compliance insight
/// </summary>
public class ComplianceInsightDto
{
    public string Category { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty; // High, Medium, Low
    public float Confidence { get; set; }
    public List<string> AffectedEntities { get; set; } = new();
}

/// <summary>
/// Compliance recommendation
/// </summary>
public class ComplianceRecommendationDto
{
    public string Priority { get; set; } = string.Empty; // Critical, High, Medium, Low
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
    public int EstimatedEffortHours { get; set; }
    public DateTime? SuggestedDeadline { get; set; }
}

/// <summary>
/// Compliance metric
/// </summary>
public class ComplianceMetricDto
{
    public string Name { get; set; } = string.Empty;
    public float Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty; // Up, Down, Stable
    public float ChangePercentage { get; set; }
}

/// <summary>
/// Model training result
/// </summary>
public class ModelTrainingResultDto
{
    public string ModelType { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime TrainingStarted { get; set; }
    public DateTime TrainingCompleted { get; set; }
    public int TrainingDataSize { get; set; }
    public Dictionary<string, float> Metrics { get; set; } = new();
}

/// <summary>
/// Model performance metrics
/// </summary>
public class ModelPerformanceDto
{
    public string ModelType { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public DateTime LastTrained { get; set; }
    public float Accuracy { get; set; }
    public float Precision { get; set; }
    public float Recall { get; set; }
    public float F1Score { get; set; }
    public Dictionary<string, float> AdditionalMetrics { get; set; } = new();
    public bool IsActive { get; set; }
}

/// <summary>
/// Compliance score forecast
/// </summary>
public class ComplianceScoreForecastDto
{
    public DateTime Date { get; set; }
    public float PredictedScore { get; set; }
    public float ConfidenceInterval { get; set; }
    public string Trend { get; set; } = string.Empty;
    public List<string> InfluencingFactors { get; set; } = new();
}
