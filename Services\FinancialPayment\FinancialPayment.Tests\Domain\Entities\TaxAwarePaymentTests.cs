using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class TaxAwarePaymentTests
{
    private readonly Guid _userId = Guid.NewGuid();
    private readonly Money _baseAmount = new(1000m, "INR");
    private readonly TaxJurisdiction _jurisdiction = new("India", "Maharashtra", "Mumbai", LocationType.City);

    [Fact]
    public void Constructor_ShouldCreateTaxAwarePayment_WithValidParameters()
    {
        // Arrange
        var decimal = CreateTaxCalculationResult();
        var serviceCategory = ServiceCategory.Transportation;
        var entityType = EntityType.Company;
        var tdsSection = TdsSection.Section194C;
        var hsnCode = "9967";
        var hasPan = true;
        var paymentMethodId = "pm_123";

        // Act
        var payment = new TaxAwarePayment(
            _userId,
            _baseAmount,
            decimal,
            serviceCategory,
            _jurisdiction,
            entityType,
            tdsSection,
            hsnCode,
            hasPan,
            paymentMethodId);

        // Assert
        payment.UserId.Should().Be(_userId);
        payment.BaseAmount.Should().Be(_baseAmount);
        payment.ServiceCategory.Should().Be(serviceCategory);
        payment.Jurisdiction.Should().Be(_jurisdiction);
        payment.EntityType.Should().Be(entityType);
        payment.TdsSection.Should().Be(tdsSection);
        payment.HsnCode.Should().Be(hsnCode);
        payment.HasPan.Should().Be(hasPan);
        payment.PaymentMethodId.Should().Be(paymentMethodId);
        payment.Status.Should().Be(PaymentStatus.Pending);
        payment.TaxDetails.Should().NotBeNull();
        payment.TaxDetails.GstAmount.Should().Be(new Money(180m, "INR"));
        payment.TaxDetails.TdsAmount.Should().Be(new Money(20m, "INR"));
        payment.TaxDetails.NetPayableAmount.Should().Be(new Money(1160m, "INR"));
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenTaxCalculationResultIsNull()
    {
        // Act & Assert
        var action = () => new TaxAwarePayment(
            _userId,
            _baseAmount,
            null!,
            ServiceCategory.Transportation,
            _jurisdiction,
            EntityType.Company);

        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void MarkAsProcessing_ShouldChangeStatusToProcessing_WhenStatusIsPending()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();

        // Act
        payment.MarkAsProcessing();

        // Assert
        payment.Status.Should().Be(PaymentStatus.Processing);
    }

    [Fact]
    public void MarkAsProcessing_ShouldThrowException_WhenStatusIsNotPending()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessing();

        // Act & Assert
        var action = () => payment.MarkAsProcessing();

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot mark payment as processing from status Processing");
    }

    [Fact]
    public void MarkAsProcessed_ShouldChangeStatusToCompleted_WithTransactionDetails()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessing();
        var transactionId = "txn_123456";
        var processedAt = DateTime.UtcNow;

        // Act
        payment.MarkAsProcessed(transactionId, processedAt);

        // Assert
        payment.Status.Should().Be(PaymentStatus.Completed);
        payment.TransactionId.Should().Be(transactionId);
        payment.ProcessedAt.Should().Be(processedAt);
    }

    [Fact]
    public void MarkAsProcessed_ShouldThrowException_WhenTransactionIdIsEmpty()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessing();

        // Act & Assert
        var action = () => payment.MarkAsProcessed("", DateTime.UtcNow);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Transaction ID cannot be empty*");
    }

    [Fact]
    public void MarkAsProcessed_ShouldThrowException_WhenStatusIsNotProcessingOrPending()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);

        // Act & Assert
        var action = () => payment.MarkAsProcessed("txn_456", DateTime.UtcNow);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot mark payment as processed from status Completed");
    }

    [Fact]
    public void MarkAsFailed_ShouldChangeStatusToFailed_WithFailureReason()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessing();
        var failureReason = "Insufficient funds";

        // Act
        payment.MarkAsFailed(failureReason);

        // Assert
        payment.Status.Should().Be(PaymentStatus.Failed);
        payment.FailureReason.Should().Be(failureReason);
    }

    [Fact]
    public void MarkAsFailed_ShouldThrowException_WhenStatusIsCompleted()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);

        // Act & Assert
        var action = () => payment.MarkAsFailed("Some reason");

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot mark completed payment as failed");
    }

    [Fact]
    public void AddRefund_ShouldAddRefundToCollection_WhenPaymentIsCompleted()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);
        var refund = new TaxAwareRefund(
            payment.Id,
            new Money(500m, "INR"),
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(580m, "INR"),
            "Customer request");

        // Act
        payment.AddRefund(refund);

        // Assert
        payment.Refunds.Should().HaveCount(1);
        payment.Refunds.First().Should().Be(refund);
    }

    [Fact]
    public void AddRefund_ShouldThrowException_WhenPaymentIsNotCompleted()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        var refund = new TaxAwareRefund(
            payment.Id,
            new Money(500m, "INR"),
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(580m, "INR"),
            "Customer request");

        // Act & Assert
        var action = () => payment.AddRefund(refund);

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot add refund to non-completed payment");
    }

    [Fact]
    public void AddRefund_ShouldThrowException_WhenRefundPaymentIdDoesNotMatch()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);
        var refund = new TaxAwareRefund(
            Guid.NewGuid(), // Different payment ID
            new Money(500m, "INR"),
            new Money(90m, "INR"),
            new Money(10m, "INR"),
            new Money(580m, "INR"),
            "Customer request");

        // Act & Assert
        var action = () => payment.AddRefund(refund);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Refund payment ID does not match this payment*");
    }

    [Fact]
    public void GetTotalRefundedAmount_ShouldReturnZero_WhenNoRefunds()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();

        // Act
        var totalRefunded = payment.GetTotalRefundedAmount();

        // Assert
        totalRefunded.Should().Be(Money.Zero("INR"));
    }

    [Fact]
    public void GetTotalRefundedAmount_ShouldReturnSumOfCompletedRefunds()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);

        var refund1 = new TaxAwareRefund(
            payment.Id,
            new Money(300m, "INR"),
            new Money(54m, "INR"),
            new Money(6m, "INR"),
            new Money(348m, "INR"),
            "Partial refund 1");
        refund1.MarkAsCompleted("ref_123", DateTime.UtcNow);

        var refund2 = new TaxAwareRefund(
            payment.Id,
            new Money(200m, "INR"),
            new Money(36m, "INR"),
            new Money(4m, "INR"),
            new Money(232m, "INR"),
            "Partial refund 2");
        refund2.MarkAsCompleted("ref_456", DateTime.UtcNow);

        var refund3 = new TaxAwareRefund(
            payment.Id,
            new Money(100m, "INR"),
            new Money(18m, "INR"),
            new Money(2m, "INR"),
            new Money(116m, "INR"),
            "Failed refund");
        refund3.MarkAsFailed("Processing error");

        payment.AddRefund(refund1);
        payment.AddRefund(refund2);
        payment.AddRefund(refund3);

        // Act
        var totalRefunded = payment.GetTotalRefundedAmount();

        // Assert
        totalRefunded.Should().Be(new Money(580m, "INR")); // Only completed refunds: 348 + 232
    }

    [Fact]
    public void GetRemainingRefundableAmount_ShouldReturnNetAmountMinusRefunds()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);

        var refund = new TaxAwareRefund(
            payment.Id,
            new Money(300m, "INR"),
            new Money(54m, "INR"),
            new Money(6m, "INR"),
            new Money(348m, "INR"),
            "Partial refund");
        refund.MarkAsCompleted("ref_123", DateTime.UtcNow);
        payment.AddRefund(refund);

        // Act
        var remainingRefundable = payment.GetRemainingRefundableAmount();

        // Assert
        remainingRefundable.Should().Be(new Money(812m, "INR")); // 1160 - 348
    }

    [Fact]
    public void CanRefund_ShouldReturnTrue_WhenPaymentIsCompletedAndAmountIsValid()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);
        var refundAmount = new Money(500m, "INR");

        // Act
        var canRefund = payment.CanRefund(refundAmount);

        // Assert
        canRefund.Should().BeTrue();
    }

    [Fact]
    public void CanRefund_ShouldReturnFalse_WhenPaymentIsNotCompleted()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        var refundAmount = new Money(500m, "INR");

        // Act
        var canRefund = payment.CanRefund(refundAmount);

        // Assert
        canRefund.Should().BeFalse();
    }

    [Fact]
    public void CanRefund_ShouldReturnFalse_WhenRefundAmountExceedsRemaining()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();
        payment.MarkAsProcessed("txn_123", DateTime.UtcNow);
        var refundAmount = new Money(2000m, "INR"); // More than net payable amount

        // Act
        var canRefund = payment.CanRefund(refundAmount);

        // Assert
        canRefund.Should().BeFalse();
    }

    [Fact]
    public void GetTaxSummary_ShouldReturnFormattedTaxSummary()
    {
        // Arrange
        var payment = CreateTaxAwarePayment();

        // Act
        var summary = payment.GetTaxSummary();

        // Assert
        summary.Should().Contain("Base: 1000.00 INR");
        summary.Should().Contain("GST: 180.00 INR (18.00%)");
        summary.Should().Contain("TDS: 20.00 INR (2.00%)");
        summary.Should().Contain("Net: 1160.00 INR");
    }

    private TaxAwarePayment CreateTaxAwarePayment()
    {
        var decimal = CreateTaxCalculationResult();
        return new TaxAwarePayment(
            _userId,
            _baseAmount,
            decimal,
            ServiceCategory.Transportation,
            _jurisdiction,
            EntityType.Company,
            TdsSection.Section194C,
            "9967",
            true,
            "pm_123");
    }

    private decimal CreateTaxCalculationResult()
    {
        return new decimal
        {
            BaseAmount = _baseAmount,
            GstAmount = new Money(180m, "INR"), // 18% of 1000
            TdsAmount = new Money(20m, "INR"), // 2% of 1000
            TotalTaxAmount = new Money(200m, "INR"), // 180 + 20
            NetAmount = new Money(1160m, "INR"), // 1000 + 180 - 20
            EffectiveGstRate = 18.0m,
            EffectiveTdsRate = 2.0m,
            IsReverseChargeApplicable = false,
            AppliedHsnCode = "9967",
            AppliedRules = new List<string> { "GST calculated at 18%", "TDS calculated at 2%" },
            Warnings = new List<string>(),
            CalculatedAt = DateTime.UtcNow
        };
    }
}
