using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.Admin.Queries.GetDashboardStats
{
    public class GetDashboardStatsQueryHandler : IRequestHandler<GetDashboardStatsQuery, DashboardStatsResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly ILogger<GetDashboardStatsQueryHandler> _logger;

        public GetDashboardStatsQueryHandler(
            IUserProfileRepository userProfileRepository,
            IDocumentSubmissionRepository documentSubmissionRepository,
            ILogger<GetDashboardStatsQueryHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _documentSubmissionRepository = documentSubmissionRepository;
            _logger = logger;
        }

        public async Task<DashboardStatsResponse> Handle(GetDashboardStatsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Retrieving dashboard statistics");

            try
            {
                // Get basic counts
                var totalUsers = await _userProfileRepository.GetTotalUsersCountAsync();
                var pendingApprovals = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.UnderReview);
                var pendingDocuments = await _documentSubmissionRepository.GetPendingDocumentsCountAsync();
                var approvedToday = await _userProfileRepository.GetApprovedTodayCountAsync();
                var rejectedToday = await _userProfileRepository.GetRejectedTodayCountAsync();

                // Get user type breakdown
                var userTypeBreakdown = new UserTypeBreakdown
                {
                    TransportCompanies = await _userProfileRepository.GetCountByUserTypeAsync(UserType.TransportCompany),
                    Brokers = await _userProfileRepository.GetCountByUserTypeAsync(UserType.Broker),
                    Carriers = await _userProfileRepository.GetCountByUserTypeAsync(UserType.Carrier),
                    Drivers = await _userProfileRepository.GetCountByUserTypeAsync(UserType.Driver),
                    Shippers = await _userProfileRepository.GetCountByUserTypeAsync(UserType.Shipper)
                };

                // Get status breakdown
                var statusBreakdown = new StatusBreakdown
                {
                    Incomplete = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.Incomplete),
                    Complete = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.Complete),
                    UnderReview = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.UnderReview),
                    Approved = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.Approved),
                    Rejected = await _userProfileRepository.GetCountByStatusAsync(ProfileStatus.Rejected)
                };

                var response = new DashboardStatsResponse
                {
                    TotalUsers = totalUsers,
                    PendingApprovals = pendingApprovals,
                    PendingDocuments = pendingDocuments,
                    ApprovedToday = approvedToday,
                    RejectedToday = rejectedToday,
                    UserTypeBreakdown = userTypeBreakdown,
                    StatusBreakdown = statusBreakdown,
                    LastUpdated = DateTime.UtcNow
                };

                _logger.LogInformation("Successfully retrieved dashboard statistics: {TotalUsers} total users, {PendingApprovals} pending approvals", 
                    totalUsers, pendingApprovals);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard statistics");
                throw;
            }
        }
    }
}
