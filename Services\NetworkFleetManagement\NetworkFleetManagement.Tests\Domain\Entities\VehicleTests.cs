using FluentAssertions;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Xunit;

namespace NetworkFleetManagement.Tests.Domain.Entities;

public class VehicleTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateVehicle()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var registrationNumber = "TEST123";
        var vehicleType = VehicleType.Truck;
        var make = "Tata";
        var model = "407";
        var year = 2020;
        var color = "White";
        var specifications = new VehicleSpecifications(1000, 10);

        // Act
        var vehicle = new Vehicle(carrierId, registrationNumber, vehicleType, make, model, year, color, specifications);

        // Assert
        vehicle.CarrierId.Should().Be(carrierId);
        vehicle.RegistrationNumber.Should().Be(registrationNumber);
        vehicle.VehicleType.Should().Be(vehicleType);
        vehicle.Make.Should().Be(make);
        vehicle.Model.Should().Be(model);
        vehicle.Year.Should().Be(year);
        vehicle.Color.Should().Be(color);
        vehicle.Specifications.Should().Be(specifications);
        vehicle.Status.Should().Be(VehicleStatus.Available);
        vehicle.IsAvailable.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Constructor_WithInvalidRegistrationNumber_ShouldThrowArgumentException(string registrationNumber)
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var specifications = new VehicleSpecifications(1000, 10);

        // Act & Assert
        var action = () => new Vehicle(carrierId, registrationNumber, VehicleType.Truck, "Tata", "407", 2020, "White", specifications);
        action.Should().Throw<ArgumentException>().WithMessage("Registration number cannot be empty*");
    }

    [Theory]
    [InlineData(1899)]
    [InlineData(2030)]
    public void Constructor_WithInvalidYear_ShouldThrowArgumentException(int year)
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var specifications = new VehicleSpecifications(1000, 10);

        // Act & Assert
        var action = () => new Vehicle(carrierId, "TEST123", VehicleType.Truck, "Tata", "407", year, "White", specifications);
        action.Should().Throw<ArgumentException>().WithMessage("Invalid year*");
    }

    [Fact]
    public void UpdateStatus_ShouldChangeStatus()
    {
        // Arrange
        var vehicle = CreateTestVehicle();

        // Act
        vehicle.UpdateStatus(VehicleStatus.InUse);

        // Assert
        vehicle.Status.Should().Be(VehicleStatus.InUse);
    }

    [Fact]
    public void UpdateLocation_ShouldSetCurrentLocation()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var location = new Location(12.9716, 77.5946, "Bangalore", "Bangalore", "Karnataka", "India", "560001");

        // Act
        vehicle.UpdateLocation(location);

        // Assert
        vehicle.CurrentLocation.Should().Be(location);
    }

    [Fact]
    public void ScheduleMaintenance_ShouldSetNextMaintenanceDate()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var scheduledDate = DateTime.UtcNow.AddDays(30);
        var notes = "Regular maintenance";

        // Act
        vehicle.ScheduleMaintenance(scheduledDate, notes);

        // Assert
        vehicle.NextMaintenanceDate.Should().Be(scheduledDate);
    }

    [Fact]
    public void CompleteMaintenance_ShouldUpdateMaintenanceDates()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var scheduledDate = DateTime.UtcNow.AddDays(30);
        vehicle.ScheduleMaintenance(scheduledDate);
        
        var completedDate = DateTime.UtcNow;
        var notes = "Maintenance completed";
        var cost = 5000m;

        // Act
        vehicle.CompleteMaintenance(completedDate, notes, cost);

        // Assert
        vehicle.LastMaintenanceDate.Should().Be(completedDate);
        vehicle.NextMaintenanceDate.Should().BeNull();
        vehicle.MaintenanceRecords.Should().HaveCount(1);
        vehicle.MaintenanceRecords.First().Cost.Should().Be(cost);
    }

    [Fact]
    public void UpdateEarnings_ShouldSetEarnings()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var dailyEarnings = 2000m;
        var monthlyEarnings = 60000m;

        // Act
        vehicle.UpdateEarnings(dailyEarnings, monthlyEarnings);

        // Assert
        vehicle.DailyEarnings.Should().Be(dailyEarnings);
        vehicle.MonthlyEarnings.Should().Be(monthlyEarnings);
    }

    [Fact]
    public void IsInsuranceValid_WithFutureExpiryDate_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var futureDate = DateTime.UtcNow.AddMonths(6);
        var updatedVehicle = new Vehicle(
            vehicle.CarrierId,
            vehicle.RegistrationNumber,
            vehicle.VehicleType,
            vehicle.Make,
            vehicle.Model,
            vehicle.Year,
            vehicle.Color,
            vehicle.Specifications,
            "INS123",
            futureDate);

        // Act & Assert
        updatedVehicle.IsInsuranceValid.Should().BeTrue();
    }

    [Fact]
    public void IsInsuranceValid_WithPastExpiryDate_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var pastDate = DateTime.UtcNow.AddDays(-1);
        var updatedVehicle = new Vehicle(
            vehicle.CarrierId,
            vehicle.RegistrationNumber,
            vehicle.VehicleType,
            vehicle.Make,
            vehicle.Model,
            vehicle.Year,
            vehicle.Color,
            vehicle.Specifications,
            "INS123",
            pastDate);

        // Act & Assert
        updatedVehicle.IsInsuranceValid.Should().BeFalse();
    }

    [Fact]
    public void RequiresMaintenanceSoon_WithNearFutureDate_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var nearFutureDate = DateTime.UtcNow.AddDays(5);
        vehicle.ScheduleMaintenance(nearFutureDate);

        // Act
        var requiresMaintenance = vehicle.RequiresMaintenanceSoon(7);

        // Assert
        requiresMaintenance.Should().BeTrue();
    }

    [Fact]
    public void RequiresMaintenanceSoon_WithFarFutureDate_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = CreateTestVehicle();
        var farFutureDate = DateTime.UtcNow.AddDays(30);
        vehicle.ScheduleMaintenance(farFutureDate);

        // Act
        var requiresMaintenance = vehicle.RequiresMaintenanceSoon(7);

        // Assert
        requiresMaintenance.Should().BeFalse();
    }

    [Fact]
    public void DisplayName_ShouldReturnFormattedString()
    {
        // Arrange
        var vehicle = CreateTestVehicle();

        // Act
        var displayName = vehicle.DisplayName;

        // Assert
        displayName.Should().Be("Tata 407 (TEST123)");
    }

    private static Vehicle CreateTestVehicle()
    {
        var specifications = new VehicleSpecifications(1000, 10);
        return new Vehicle(
            Guid.NewGuid(),
            "TEST123",
            VehicleType.Truck,
            "Tata",
            "407",
            2020,
            "White",
            specifications);
    }
}
