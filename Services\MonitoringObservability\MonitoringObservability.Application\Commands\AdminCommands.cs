using MediatR;
using MonitoringObservability.Application.DTOs;
using DomainEnums = MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Commands;

// Performance Analysis Commands
public class AnalyzeSystemPerformanceCommand : IRequest<PerformanceAnalysisDto>
{
    public TimeSpan Period { get; set; }
    public List<string>? ServiceNames { get; set; }
    public bool IncludeRecommendations { get; set; } = true;
    public bool AnalyzeThresholds { get; set; } = true;
    public bool DetectAnomalies { get; set; } = true;
}

// Threshold Configuration Commands
public class ConfigureSystemThresholdsCommand : IRequest<ThresholdConfigurationResultDto>
{
    public string? ServiceName { get; set; }
    public List<ThresholdConfigurationDto> ThresholdConfigurations { get; set; } = new();
    public bool ApplyToExistingMetrics { get; set; } = true;
    public bool CreateMissingMetrics { get; set; } = false;
    public bool ValidateThresholds { get; set; } = true;
}

public class OptimizeThresholdsCommand : IRequest<ThresholdOptimizationResultDto>
{
    public string? ServiceName { get; set; }
    public TimeSpan AnalysisPeriod { get; set; } = TimeSpan.FromDays(30);
    public bool ApplyOptimizations { get; set; } = false; // Dry run by default
    public double FalsePositiveThreshold { get; set; } = 0.1; // 10% false positive rate
}

// Report Generation Commands
public class GenerateSystemReportCommand : IRequest<SystemReportDto>
{
    public DomainEnums.ReportType ReportType { get; set; }
    public TimeSpan Period { get; set; }
    public List<string>? ServiceNames { get; set; }
    public bool IncludeMetrics { get; set; } = true;
    public bool IncludeAlerts { get; set; } = true;
    public bool IncludeIncidents { get; set; } = true;
    public bool IncludePerformanceAnalysis { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.JSON;
    public Guid RequestedBy { get; set; }
}

public class ScheduleReportCommand : IRequest<ScheduledReportDto>
{
    public string Name { get; set; } = string.Empty;
    public DomainEnums.ReportType ReportType { get; set; }
    public string CronExpression { get; set; } = string.Empty; // For scheduling
    public List<string> Recipients { get; set; } = new();
    public ReportFormat Format { get; set; } = ReportFormat.PDF;
    public bool IsEnabled { get; set; } = true;
    public Guid CreatedBy { get; set; }
}

// Escalation Rule Commands
public class CreateEscalationRuleCommand : IRequest<EscalationRuleDto>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ServiceName { get; set; }
    public DomainEnums.AlertSeverity? AlertSeverity { get; set; }
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
}

public class UpdateEscalationRuleCommand : IRequest<EscalationRuleDto>
{
    public Guid RuleId { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public List<EscalationLevelDto>? EscalationLevels { get; set; }
    public bool? IsEnabled { get; set; }
}

public class DeleteEscalationRuleCommand : IRequest<Unit>
{
    public Guid RuleId { get; set; }
}

// Notification Configuration Commands
public class UpdateNotificationConfigurationCommand : IRequest<NotificationConfigurationDto>
{
    public EmailSettingsDto? EmailSettings { get; set; }
    public SMSSettingsDto? SMSSettings { get; set; }
    public SlackSettingsDto? SlackSettings { get; set; }
    public TeamsSettingsDto? TeamsSettings { get; set; }
    public WebhookSettingsDto? WebhookSettings { get; set; }
    public List<DomainEnums.NotificationChannel> DefaultChannels { get; set; } = new();
    public Dictionary<DomainEnums.AlertSeverity, List<DomainEnums.NotificationChannel>> EscalationChannels { get; set; } = new();
}

public class TestNotificationChannelsCommand : IRequest<NotificationTestResultDto>
{
    public List<DomainEnums.NotificationChannel> Channels { get; set; } = new();
    public string TestMessage { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
}

// Maintenance Commands
public class ExecuteMaintenanceTasksCommand : IRequest<MaintenanceExecutionResultDto>
{
    public List<MaintenanceTaskType> TaskTypes { get; set; } = new();
    public TimeSpan? DataRetentionPeriod { get; set; }
    public bool DryRun { get; set; } = true;
    public DateTime? ScheduledFor { get; set; }
    public Guid ExecutedBy { get; set; }
}

public class ScheduleMaintenanceCommand : IRequest<ScheduledMaintenanceDto>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<MaintenanceTaskType> TaskTypes { get; set; } = new();
    public DateTime ScheduledFor { get; set; }
    public TimeSpan EstimatedDuration { get; set; }
    public bool NotifyUsers { get; set; } = true;
    public List<string> NotificationRecipients { get; set; } = new();
    public Guid ScheduledBy { get; set; }
}

// System Optimization Commands
public class OptimizeSystemPerformanceCommand : IRequest<SystemOptimizationResultDto>
{
    public List<string>? ServiceNames { get; set; }
    public List<OptimizationType> OptimizationTypes { get; set; } = new();
    public bool ApplyOptimizations { get; set; } = false; // Dry run by default
    public Guid RequestedBy { get; set; }
}

public class TuneAlertingSystemCommand : IRequest<AlertingTuningResultDto>
{
    public TimeSpan AnalysisPeriod { get; set; } = TimeSpan.FromDays(30);
    public double TargetFalsePositiveRate { get; set; } = 0.05; // 5%
    public double TargetMissedAlertRate { get; set; } = 0.01; // 1%
    public bool ApplyTuning { get; set; } = false;
    public Guid RequestedBy { get; set; }
}

// Data Management Commands
public class ArchiveOldDataCommand : IRequest<DataArchiveResultDto>
{
    public TimeSpan RetentionPeriod { get; set; }
    public List<string>? ServiceNames { get; set; }
    public List<DataType> DataTypes { get; set; } = new();
    public string? ArchiveLocation { get; set; }
    public bool CompressData { get; set; } = true;
    public bool DryRun { get; set; } = true;
}

public class RestoreArchivedDataCommand : IRequest<DataRestoreResultDto>
{
    public string ArchiveId { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<string>? ServiceNames { get; set; }
    public Guid RequestedBy { get; set; }
}

// Security and Compliance Commands
public class RunSecurityAuditCommand : IRequest<SecurityAuditResultDto>
{
    public List<SecurityCheckType> CheckTypes { get; set; } = new();
    public bool IncludeRecommendations { get; set; } = true;
    public bool GenerateReport { get; set; } = true;
    public Guid RequestedBy { get; set; }
}

public class ValidateComplianceCommand : IRequest<ComplianceValidationResultDto>
{
    public List<ComplianceStandard> Standards { get; set; } = new();
    public TimeSpan AuditPeriod { get; set; } = TimeSpan.FromDays(90);
    public bool GenerateReport { get; set; } = true;
    public Guid RequestedBy { get; set; }
}

// User Management Commands (for monitoring system)
public class ManageMonitoringUsersCommand : IRequest<UserManagementResultDto>
{
    public UserManagementAction Action { get; set; }
    public Guid? UserId { get; set; }
    public string? UserEmail { get; set; }
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
    public Guid PerformedBy { get; set; }
}

public class ConfigureUserNotificationPreferencesCommand : IRequest<UserNotificationPreferencesDto>
{
    public Guid UserId { get; set; }
    public List<DomainEnums.NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<DomainEnums.AlertSeverity, List<DomainEnums.NotificationChannel>> SeverityChannels { get; set; } = new();
    public bool ReceiveSystemNotifications { get; set; } = true;
    public bool ReceiveMaintenanceNotifications { get; set; } = true;
    public TimeSpan QuietHoursStart { get; set; }
    public TimeSpan QuietHoursEnd { get; set; }
}

// Supporting DTOs for commands
public class ThresholdOptimizationResultDto
{
    public int TotalMetricsAnalyzed { get; set; }
    public int OptimizationsRecommended { get; set; }
    public int OptimizationsApplied { get; set; }
    public List<ThresholdOptimizationDto> Optimizations { get; set; } = new();
    public double EstimatedFalsePositiveReduction { get; set; }
    public DateTime OptimizedAt { get; set; }
}

public class ThresholdOptimizationDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double CurrentWarningThreshold { get; set; }
    public double RecommendedWarningThreshold { get; set; }
    public double CurrentCriticalThreshold { get; set; }
    public double RecommendedCriticalThreshold { get; set; }
    public string Justification { get; set; } = string.Empty;
    public double ConfidenceLevel { get; set; }
}

// Enums for admin commands
public enum OptimizationType
{
    DatabaseIndexes = 0,
    QueryOptimization = 1,
    CacheConfiguration = 2,
    AlertThresholds = 3,
    ResourceAllocation = 4,
    NetworkConfiguration = 5
}

public enum DataType
{
    Metrics = 0,
    Alerts = 1,
    Incidents = 2,
    HealthChecks = 3,
    Logs = 4,
    Notifications = 5
}

public enum SecurityCheckType
{
    AccessControl = 0,
    DataEncryption = 1,
    NetworkSecurity = 2,
    AuthenticationSecurity = 3,
    ConfigurationSecurity = 4,
    VulnerabilityAssessment = 5
}

public enum ComplianceStandard
{
    SOC2 = 0,
    ISO27001 = 1,
    GDPR = 2,
    HIPAA = 3,
    PCI_DSS = 4,
    Custom = 5
}

public enum UserManagementAction
{
    Create = 0,
    Update = 1,
    Delete = 2,
    Activate = 3,
    Deactivate = 4,
    ResetPassword = 5,
    UpdateRoles = 6,
    UpdatePermissions = 7
}
