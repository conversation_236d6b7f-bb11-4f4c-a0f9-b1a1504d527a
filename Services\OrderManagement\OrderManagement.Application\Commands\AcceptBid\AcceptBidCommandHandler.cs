using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AcceptBid;

public class AcceptBidCommandHandler : IRequestHandler<AcceptBidCommand, bool>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AcceptBidCommandHandler> _logger;

    public AcceptBidCommandHandler(
        IRfqBidRepository bidRepository,
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        ILogger<AcceptBidCommandHandler> logger)
    {
        _bidRepository = bidRepository;
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(AcceptBidCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Accepting bid {BidId} by transport company {TransportCompanyId}", 
            request.BidId, request.TransportCompanyId);

        var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
        
        if (bid == null)
        {
            _logger.LogWarning("Bid {BidId} not found", request.BidId);
            throw new OrderManagementException("Bid not found");
        }

        // Load the RFQ to check authorization
        var rfq = await _rfqRepository.GetByIdAsync(bid.RfqId, cancellationToken);
        
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found for bid {BidId}", bid.RfqId, request.BidId);
            throw new OrderManagementException("Associated RFQ not found");
        }

        // Authorization check
        if (rfq.TransportCompanyId != request.TransportCompanyId)
        {
            _logger.LogWarning("Transport company {TransportCompanyId} is not authorized to accept bid {BidId}", 
                request.TransportCompanyId, request.BidId);
            throw new OrderManagementException("You are not authorized to accept this bid");
        }

        // Business rule checks
        if (bid.Status != BidStatus.Submitted)
        {
            _logger.LogWarning("Cannot accept bid {BidId} with status {Status}", request.BidId, bid.Status);
            throw new OrderManagementException($"Cannot accept bid with status {bid.Status}");
        }

        if (rfq.Status != RfqStatus.Published)
        {
            _logger.LogWarning("Cannot accept bid {BidId} for RFQ {RfqId} with status {Status}", 
                request.BidId, rfq.Id, rfq.Status);
            throw new OrderManagementException($"Cannot accept bid for RFQ with status {rfq.Status}");
        }

        // Accept the bid
        bid.Accept(request.AcceptanceNotes);

        // Reject all other bids for this RFQ
        var otherBids = await _bidRepository.GetByRfqIdAsync(bid.RfqId, cancellationToken);
        foreach (var otherBid in otherBids.Where(b => b.Id != bid.Id && b.Status == BidStatus.Submitted))
        {
            otherBid.Reject("Another bid was accepted for this RFQ");
            _bidRepository.Update(otherBid);
        }

        // Close the RFQ
        rfq.Close("Bid accepted and order created");

        _bidRepository.Update(bid);
        _rfqRepository.Update(rfq);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Successfully accepted bid {BidId} and closed RFQ {RfqId}", request.BidId, rfq.Id);
        
        return true;
    }
}
