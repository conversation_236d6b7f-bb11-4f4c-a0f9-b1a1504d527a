using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Application.Models;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using Shared.Messaging;

namespace UserManagement.Infrastructure.Services
{
    public class AuditService : IAuditService
    {
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<AuditService> _logger;

        public AuditService(
            IAuditLogRepository auditLogRepository,
            IMessageBroker messageBroker,
            ILogger<AuditService> logger)
        {
            _auditLogRepository = auditLogRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task LogUserApprovalAsync(Guid userId, string userName, Guid approvedBy, string approverName,
            string approverRole, string ipAddress, string userAgent, string? notes = null)
        {
            var auditLog = AuditLog.CreateUserApprovalLog(userId, userName, approvedBy, approverName,
                approverRole, ipAddress, userAgent, notes);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogUserRejectionAsync(Guid userId, string userName, Guid rejectedBy, string rejectorName,
            string rejectorRole, string reason, string ipAddress, string userAgent, string? notes = null)
        {
            var auditLog = AuditLog.CreateUserRejectionLog(userId, userName, rejectedBy, rejectorName,
                rejectorRole, reason, ipAddress, userAgent, notes);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogDocumentApprovalAsync(Guid submissionId, string documentType, Guid approvedBy,
            string approverName, string approverRole, string ipAddress, string userAgent, string? notes = null)
        {
            var auditLog = AuditLog.CreateDocumentApprovalLog(submissionId, documentType, approvedBy,
                approverName, approverRole, ipAddress, userAgent, notes);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogDataExportAsync(string exportType, Guid requestedBy, string requestedByRole,
            string ipAddress, string userAgent, Dictionary<string, object>? additionalData = null)
        {
            var auditLog = new AuditLog(
                action: AuditAction.Download,
                entityType: "DataExport",
                entityId: Guid.NewGuid(),
                userId: requestedBy,
                userName: "System", // This could be enhanced to get actual user name
                userRole: requestedByRole,
                description: $"Data export requested: {exportType}",
                severity: AuditSeverity.Medium,
                ipAddress: ipAddress,
                userAgent: userAgent,
                additionalData: additionalData ?? new Dictionary<string, object>()
            );

            await _auditLogRepository.AddAsync(auditLog);

            _logger.LogInformation("Data export audit logged: {ExportType} by user {UserId} with role {Role}",
                exportType, requestedBy, requestedByRole);
        }

        public async Task LogDocumentRejectionAsync(Guid submissionId, string documentType, Guid rejectedBy,
            string rejectorName, string rejectorRole, string reason, string ipAddress, string userAgent,
            bool requiresResubmission = true, string? notes = null)
        {
            var auditLog = AuditLog.CreateDocumentRejectionLog(submissionId, documentType, rejectedBy,
                rejectorName, rejectorRole, reason, ipAddress, userAgent, requiresResubmission, notes);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogDocumentUploadAsync(Guid submissionId, string documentType, Guid userId,
            string userName, string fileName, string ipAddress, string userAgent)
        {
            var auditLog = AuditLog.CreateDocumentUploadLog(submissionId, documentType, userId,
                userName, fileName, ipAddress, userAgent);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogOcrProcessingAsync(Guid submissionId, string documentType, decimal confidenceScore,
            bool autoApproved, string systemUser = "System")
        {
            var auditLog = AuditLog.CreateOcrProcessingLog(submissionId, documentType, confidenceScore,
                autoApproved, systemUser);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogBulkOperationAsync(string operationType, int itemCount, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            Dictionary<string, object>? additionalData = null)
        {
            var auditLog = AuditLog.CreateBulkOperationLog(operationType, itemCount, performedBy,
                performerName, performerRole, ipAddress, userAgent, additionalData);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogAccountActionAsync(Guid userId, string action, string reason, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            Dictionary<string, object>? additionalData = null)
        {
            var auditLog = new AuditLog(
                AuditAction.StatusChange,
                "UserAccount",
                userId,
                performedBy,
                performerName,
                performerRole,
                $"Account action: {action}. Reason: {reason}",
                AuditSeverity.Medium,
                null,
                null,
                ipAddress,
                userAgent,
                null,
                null,
                additionalData);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task LogCustomActionAsync(AuditAction action, string entityType, Guid? entityId,
            Guid? userId, string userName, string userRole, string description,
            AuditSeverity severity = AuditSeverity.Medium, string? oldValues = null,
            string? newValues = null, string ipAddress = "", string userAgent = "",
            string? sessionId = null, string? correlationId = null,
            Dictionary<string, object>? additionalData = null)
        {
            var auditLog = new AuditLog(action, entityType, entityId, userId, userName, userRole,
                description, severity, oldValues, newValues, ipAddress, userAgent, sessionId,
                correlationId, additionalData);

            await SaveAuditLogAsync(auditLog);
        }

        public async Task<IEnumerable<AuditLog>> GetAuditLogsAsync(UserManagement.Application.Models.AuditLogSearchCriteria criteria)
        {
            // Convert Application.Models.AuditLogSearchCriteria to Domain.Repositories.AuditLogSearchCriteria
            var domainCriteria = new UserManagement.Domain.Repositories.AuditLogSearchCriteria
            {
                UserId = criteria.UserId,
                EntityType = criteria.EntityType,
                EntityId = criteria.EntityId != null ? Guid.Parse(criteria.EntityId) : null,
                Action = criteria.Action != null ? Enum.Parse<AuditAction>(criteria.Action) : null,
                Severity = criteria.Severity,
                StartDate = criteria.FromDate,
                EndDate = criteria.ToDate,
                IpAddress = criteria.IpAddress,
                SessionId = criteria.SessionId,
                CorrelationId = criteria.CorrelationId,
                SearchTerm = criteria.SearchText,
                PageNumber = criteria.PageNumber,
                PageSize = criteria.PageSize,
                SortBy = criteria.SortBy,
                SortDescending = criteria.SortDirection == "desc"
            };

            return await _auditLogRepository.SearchAsync(domainCriteria);
        }

        public async Task<AuditLog?> GetAuditLogByIdAsync(Guid id)
        {
            return await _auditLogRepository.GetByIdAsync(id);
        }

        public async Task<int> GetAuditLogCountAsync(UserManagement.Application.Models.AuditLogSearchCriteria criteria)
        {
            // Convert Application.Models.AuditLogSearchCriteria to Domain.Repositories.AuditLogSearchCriteria
            var domainCriteria = new UserManagement.Domain.Repositories.AuditLogSearchCriteria
            {
                UserId = criteria.UserId,
                EntityType = criteria.EntityType,
                EntityId = criteria.EntityId != null ? Guid.Parse(criteria.EntityId) : null,
                Action = criteria.Action != null ? Enum.Parse<AuditAction>(criteria.Action) : null,
                Severity = criteria.Severity,
                StartDate = criteria.FromDate,
                EndDate = criteria.ToDate,
                IpAddress = criteria.IpAddress,
                SessionId = criteria.SessionId,
                CorrelationId = criteria.CorrelationId,
                SearchTerm = criteria.SearchText,
                PageNumber = criteria.PageNumber,
                PageSize = criteria.PageSize,
                SortBy = criteria.SortBy,
                SortDescending = criteria.SortDirection == "desc"
            };

            // For now, return a simple count. In a real implementation, you'd modify the repository
            // to support count with criteria
            var logs = await _auditLogRepository.SearchAsync(domainCriteria);
            return logs.Count();
        }

        public async Task<IEnumerable<AuditLog>> GetRecentAuditLogsAsync(int count = 100)
        {
            return await _auditLogRepository.GetRecentAsync(count);
        }

        public async Task<IEnumerable<AuditLog>> GetCriticalEventsAsync(int pageNumber = 1, int pageSize = 50)
        {
            return await _auditLogRepository.GetCriticalEventsAsync(pageNumber, pageSize);
        }

        private async Task SaveAuditLogAsync(AuditLog auditLog)
        {
            try
            {
                await _auditLogRepository.AddAsync(auditLog);

                // Publish audit event for integration with other services
                await _messageBroker.PublishAsync("audit.log.created", new
                {
                    AuditLogId = auditLog.Id,
                    Action = auditLog.Action.ToString(),
                    EntityType = auditLog.EntityType,
                    EntityId = auditLog.EntityId,
                    UserId = auditLog.UserId,
                    UserName = auditLog.UserName,
                    UserRole = auditLog.UserRole,
                    Description = auditLog.Description,
                    Severity = auditLog.Severity.ToString(),
                    Timestamp = auditLog.Timestamp,
                    IpAddress = auditLog.IpAddress,
                    SessionId = auditLog.SessionId,
                    CorrelationId = auditLog.CorrelationId
                });

                _logger.LogInformation("Audit log created: {AuditLogId}, Action: {Action}, Entity: {EntityType}, User: {UserName}",
                    auditLog.Id, auditLog.Action, auditLog.EntityType, auditLog.UserName);

                // Log critical events with higher visibility
                if (auditLog.Severity == AuditSeverity.Critical || auditLog.Severity == AuditSeverity.High)
                {
                    _logger.LogWarning("Critical audit event: {Description} by {UserName} ({UserRole}) from {IpAddress}",
                        auditLog.Description, auditLog.UserName, auditLog.UserRole, auditLog.IpAddress);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save audit log: {Action}, Entity: {EntityType}, User: {UserName}",
                    auditLog.Action, auditLog.EntityType, auditLog.UserName);
                throw;
            }
        }

        public async Task LogAccountActionAsync(Guid userId, string userName, Guid performedBy,
            string performerName, string performerRole, string ipAddress, string userAgent,
            string action, string actionResult, string reason, string notes)
        {
            var auditLog = AuditLog.CreateAccountActionLog(userId, userName, performedBy, performerName,
                performerRole, action, actionResult, reason, notes, ipAddress, userAgent);

            await SaveAuditLogAsync(auditLog);
        }
    }
}
