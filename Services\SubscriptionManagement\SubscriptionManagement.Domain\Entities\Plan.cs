using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    public class Plan : AggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public PlanType Type { get; private set; }
        public UserType UserType { get; private set; }
        public Money Price { get; private set; }
        public BillingCycle BillingCycle { get; private set; }
        public PlanLimits Limits { get; private set; }
        public bool IsActive { get; private set; }
        public bool IsPublic { get; private set; }
        public DateTime? TrialPeriodDays { get; private set; }
        public int? SetupFeeAmount { get; private set; }
        public string? SetupFeeCurrency { get; private set; }

        private readonly List<PlanFeature> _features = new();
        public IReadOnlyCollection<PlanFeature> Features => _features.AsReadOnly();

        private readonly List<TaxConfiguration> _taxConfigurations = new();
        public IReadOnlyCollection<TaxConfiguration> TaxConfigurations => _taxConfigurations.AsReadOnly();

        public Guid? TaxCategoryId { get; private set; }
        public TaxCategory? TaxCategory { get; private set; }

        private Plan() { }

        public Plan(string name, string description, PlanType type, UserType userType,
            Money price, BillingCycle billingCycle, PlanLimits limits,
            bool isPublic = true, int? trialPeriodDays = null, Money? setupFee = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new SubscriptionDomainException("Plan name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new SubscriptionDomainException("Plan description cannot be empty");

            if (price == null)
                throw new SubscriptionDomainException("Plan price cannot be null");

            if (billingCycle == null)
                throw new SubscriptionDomainException("Billing cycle cannot be null");

            if (limits == null)
                throw new SubscriptionDomainException("Plan limits cannot be null");

            if (trialPeriodDays.HasValue && trialPeriodDays.Value < 0)
                throw new SubscriptionDomainException("Trial period days cannot be negative");

            Name = name;
            Description = description;
            Type = type;
            UserType = userType;
            Price = price;
            BillingCycle = billingCycle;
            Limits = limits;
            IsActive = true;
            IsPublic = isPublic;
            TrialPeriodDays = trialPeriodDays.HasValue ? DateTime.UtcNow.AddDays(trialPeriodDays.Value) : null;

            if (setupFee != null)
            {
                SetupFeeAmount = (int)(setupFee.Amount * 100); // Store in cents
                SetupFeeCurrency = setupFee.Currency;
            }

            AddDomainEvent(new PlanCreatedEvent(Id, name, type, userType, price.Amount, price.Currency));
        }

        public void AddFeature(FeatureType featureType, FeatureAccessType accessType, int? limitValue = null, string? description = null)
        {
            if (_features.Any(f => f.FeatureType == featureType))
                throw new SubscriptionDomainException($"Feature {featureType} already exists in this plan");

            if (accessType == FeatureAccessType.Limit && !limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value must be specified for limit-based features");

            if (accessType != FeatureAccessType.Limit && limitValue.HasValue)
                throw new SubscriptionDomainException("Limit value should only be specified for limit-based features");

            var feature = new PlanFeature(featureType, accessType, limitValue, description);
            _features.Add(feature);
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveFeature(FeatureType featureType)
        {
            var feature = _features.FirstOrDefault(f => f.FeatureType == featureType);
            if (feature == null)
                throw new SubscriptionDomainException($"Feature {featureType} does not exist in this plan");

            _features.Remove(feature);
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateFeature(FeatureType featureType, FeatureAccessType accessType, int? limitValue = null, string? description = null)
        {
            var feature = _features.FirstOrDefault(f => f.FeatureType == featureType);
            if (feature == null)
                throw new SubscriptionDomainException($"Feature {featureType} does not exist in this plan");

            feature.Update(accessType, limitValue, description);
            UpdatedAt = DateTime.UtcNow;
        }

        public bool HasFeature(FeatureType featureType)
        {
            return _features.Any(f => f.FeatureType == featureType);
        }

        public PlanFeature? GetFeature(FeatureType featureType)
        {
            return _features.FirstOrDefault(f => f.FeatureType == featureType);
        }

        public void UpdatePrice(Money newPrice)
        {
            if (newPrice == null)
                throw new SubscriptionDomainException("Price cannot be null");

            if (newPrice.Currency != Price.Currency)
                throw new SubscriptionDomainException("Cannot change currency of existing plan");

            Price = newPrice;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateLimits(PlanLimits newLimits)
        {
            if (newLimits == null)
                throw new SubscriptionDomainException("Limits cannot be null");

            Limits = newLimits;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MakePublic()
        {
            IsPublic = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MakePrivate()
        {
            IsPublic = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public Money CalculateProrationAmount(DateTime fromDate, DateTime toDate, Money currentPlanPrice)
        {
            if (fromDate >= toDate)
                throw new SubscriptionDomainException("From date must be before to date");

            var totalDaysInCycle = BillingCycle.GetDaysInCycle();
            var remainingDays = (toDate - fromDate).Days;
            var prorationFactor = (decimal)remainingDays / totalDaysInCycle;

            var currentPlanProration = currentPlanPrice.Multiply(prorationFactor);
            var newPlanProration = Price.Multiply(prorationFactor);

            return newPlanProration.Subtract(currentPlanProration);
        }

        public bool IsCompatibleWith(UserType userType)
        {
            return UserType == userType;
        }

        public bool IsUpgradeFrom(Plan otherPlan)
        {
            if (UserType != otherPlan.UserType)
                return false;

            return Type > otherPlan.Type;
        }

        public bool IsDowngradeFrom(Plan otherPlan)
        {
            if (UserType != otherPlan.UserType)
                return false;

            return Type < otherPlan.Type;
        }

        // Tax-related methods
        public void SetTaxCategory(TaxCategory taxCategory)
        {
            if (taxCategory == null)
                throw new SubscriptionDomainException("Tax category cannot be null");

            TaxCategoryId = taxCategory.Id;
            TaxCategory = taxCategory;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxCategoryChangedEvent(Id, Name, taxCategory.Id, taxCategory.Name));
        }

        public void RemoveTaxCategory()
        {
            if (TaxCategoryId.HasValue)
            {
                var oldCategoryId = TaxCategoryId.Value;
                var oldCategoryName = TaxCategory?.Name ?? "Unknown";

                TaxCategoryId = null;
                TaxCategory = null;
                UpdatedAt = DateTime.UtcNow;

                AddDomainEvent(new PlanTaxCategoryRemovedEvent(Id, Name, oldCategoryId, oldCategoryName));
            }
        }

        public void AddTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            // Check for overlapping configurations
            var overlapping = _taxConfigurations.Any(tc =>
                tc.TaxType == taxConfiguration.TaxType &&
                tc.ApplicableRegions.Any(r => taxConfiguration.ApplicableRegions.Contains(r)) &&
                tc.IsActiveOn(taxConfiguration.EffectiveDate));

            if (overlapping)
                throw new SubscriptionDomainException("Overlapping tax configuration already exists for this type and region");

            _taxConfigurations.Add(taxConfiguration);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxConfigurationAddedEvent(Id, Name, taxConfiguration.TaxType,
                taxConfiguration.Rate, taxConfiguration.ApplicableRegions));
        }

        public void RemoveTaxConfiguration(TaxConfiguration taxConfiguration)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            var removed = _taxConfigurations.Remove(taxConfiguration);
            if (removed)
            {
                UpdatedAt = DateTime.UtcNow;
                AddDomainEvent(new PlanTaxConfigurationRemovedEvent(Id, Name, taxConfiguration.TaxType));
            }
        }

        public void UpdateTaxConfiguration(TaxType taxType, List<string> regions, TaxConfiguration newConfiguration)
        {
            var existingConfig = _taxConfigurations.FirstOrDefault(tc =>
                tc.TaxType == taxType &&
                tc.ApplicableRegions.Any(r => regions.Contains(r)));

            if (existingConfig == null)
                throw new SubscriptionDomainException($"Tax configuration for {taxType} not found in specified regions");

            var oldRate = existingConfig.Rate;
            _taxConfigurations.Remove(existingConfig);
            _taxConfigurations.Add(newConfiguration);
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PlanTaxConfigurationChangedEvent(Id, Name, taxType, oldRate, newConfiguration.Rate));
        }

        public Money CalculateTaxAmount(string customerRegion, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            var totalTax = Money.Zero(Price.Currency);

            // First check plan-specific tax configurations
            var planTaxConfigs = _taxConfigurations
                .Where(tc => tc.IsApplicableToRegion(customerRegion) && tc.IsActiveOn(checkDate))
                .ToList();

            if (planTaxConfigs.Any())
            {
                var currentBase = Price;
                foreach (var config in planTaxConfigs.OrderBy(c => c.TaxType))
                {
                    var tax = config.CalculateTax(currentBase, customerRegion);
                    totalTax = totalTax.Add(tax);
                    currentBase = currentBase.Add(tax); // Compound taxation
                }
                return totalTax;
            }

            // Fall back to tax category configurations
            if (TaxCategory != null)
            {
                return TaxCategory.CalculateTotalTax(Price, customerRegion, checkDate);
            }

            return totalTax;
        }

        public Money GetDisplayPrice(string customerRegion, bool includeTax = true, DateTime? date = null)
        {
            if (!includeTax)
                return Price;

            var taxAmount = CalculateTaxAmount(customerRegion, date);
            return Price.Add(taxAmount);
        }

        public Money GetBasePrice(string customerRegion, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;

            // Check if any tax configuration is tax-inclusive
            var inclusiveConfig = _taxConfigurations
                .FirstOrDefault(tc => tc.IsIncluded && tc.IsApplicableToRegion(customerRegion) && tc.IsActiveOn(checkDate));

            if (inclusiveConfig != null)
            {
                return inclusiveConfig.CalculateBaseAmountFromTaxInclusivePrice(Price, customerRegion);
            }

            // Check tax category for inclusive configurations
            if (TaxCategory != null)
            {
                var categoryConfigs = TaxCategory.GetTaxConfigurationsForRegion(customerRegion, checkDate);
                var inclusiveCategoryConfig = categoryConfigs.FirstOrDefault(tc => tc.IsIncluded);

                if (inclusiveCategoryConfig != null)
                {
                    return inclusiveCategoryConfig.CalculateBaseAmountFromTaxInclusivePrice(Price, customerRegion);
                }
            }

            return Price;
        }

        public bool HasTaxConfiguration(TaxType taxType, string region)
        {
            return _taxConfigurations.Any(tc =>
                tc.TaxType == taxType &&
                tc.IsApplicableToRegion(region) &&
                tc.IsActiveOn(DateTime.UtcNow));
        }

        public List<TaxConfiguration> GetActiveTaxConfigurations(string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            return _taxConfigurations
                .Where(tc => tc.IsApplicableToRegion(region) && tc.IsActiveOn(checkDate))
                .ToList();
        }

        public bool IsTaxInclusivePricing(string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;

            // Check plan-specific configurations first
            var planConfig = _taxConfigurations
                .FirstOrDefault(tc => tc.IsApplicableToRegion(region) && tc.IsActiveOn(checkDate));

            if (planConfig != null)
                return planConfig.IsIncluded;

            // Check tax category configurations
            if (TaxCategory != null)
            {
                var categoryConfigs = TaxCategory.GetTaxConfigurationsForRegion(region, checkDate);
                return categoryConfigs.Any(tc => tc.IsIncluded);
            }

            return false;
        }
    }
}
