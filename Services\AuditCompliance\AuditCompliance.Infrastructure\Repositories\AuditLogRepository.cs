using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;

namespace AuditCompliance.Infrastructure.Repositories;

public class AuditLogRepository : IAuditLogRepository
{
    private readonly AuditComplianceDbContext _context;

    public AuditLogRepository(AuditComplianceDbContext context)
    {
        _context = context;
    }

    public async Task<AuditLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.AuditLogs
            .FirstOrDefaultAsync(al => al.Id == id, cancellationToken);
    }

    public async Task<List<AuditLog>> GetAuditTrailAsync(
        Guid? userId = null,
        string? entityType = null,
        Guid? entityId = null,
        AuditEventType? eventType = null,
        AuditSeverity? minSeverity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? searchTerm = null,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditLogs.AsQueryable();

        if (userId.HasValue)
            query = query.Where(al => al.UserId == userId.Value);

        if (!string.IsNullOrEmpty(entityType))
            query = query.Where(al => al.EntityType == entityType);

        if (entityId.HasValue)
            query = query.Where(al => al.EntityId == entityId.Value);

        if (eventType.HasValue)
            query = query.Where(al => al.EventType == eventType.Value);

        if (minSeverity.HasValue)
            query = query.Where(al => al.Severity >= minSeverity.Value);

        if (fromDate.HasValue)
            query = query.Where(al => al.EventTimestamp >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(al => al.EventTimestamp <= toDate.Value);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(al => 
                al.Description.Contains(searchTerm) ||
                al.Action.Contains(searchTerm) ||
                (al.UserName != null && al.UserName.Contains(searchTerm)));
        }

        return await query
            .OrderByDescending(al => al.EventTimestamp)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetAuditTrailCountAsync(
        Guid? userId = null,
        string? entityType = null,
        Guid? entityId = null,
        AuditEventType? eventType = null,
        AuditSeverity? minSeverity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.AuditLogs.AsQueryable();

        if (userId.HasValue)
            query = query.Where(al => al.UserId == userId.Value);

        if (!string.IsNullOrEmpty(entityType))
            query = query.Where(al => al.EntityType == entityType);

        if (entityId.HasValue)
            query = query.Where(al => al.EntityId == entityId.Value);

        if (eventType.HasValue)
            query = query.Where(al => al.EventType == eventType.Value);

        if (minSeverity.HasValue)
            query = query.Where(al => al.Severity >= minSeverity.Value);

        if (fromDate.HasValue)
            query = query.Where(al => al.EventTimestamp >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(al => al.EventTimestamp <= toDate.Value);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(al => 
                al.Description.Contains(searchTerm) ||
                al.Action.Contains(searchTerm) ||
                (al.UserName != null && al.UserName.Contains(searchTerm)));
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<AuditLog>> GetSecurityEventsAsync(
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        var securityEventTypes = new[]
        {
            AuditEventType.UserLogin,
            AuditEventType.UserLogout,
            AuditEventType.UnauthorizedAccess,
            AuditEventType.SuspiciousActivity,
            AuditEventType.SecurityBreach,
            AuditEventType.DataExport,
            AuditEventType.DataDeletion
        };

        return await _context.AuditLogs
            .Where(al => 
                al.EventTimestamp >= fromDate &&
                al.EventTimestamp <= toDate &&
                (securityEventTypes.Contains(al.EventType) || al.Severity >= AuditSeverity.High))
            .OrderByDescending(al => al.EventTimestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AuditLog>> GetExpiredAuditLogsAsync(CancellationToken cancellationToken = default)
    {
        var currentTime = DateTime.UtcNow;
        
        return await _context.AuditLogs
            .Where(al => al.RetentionPolicy.AutoDelete && 
                        EF.Functions.DateDiffDay(al.CreatedAt, currentTime) > 
                        EF.Functions.DateDiffDay(DateTime.MinValue, DateTime.MinValue.Add(al.RetentionPolicy.RetentionPeriod)))
            .ToListAsync(cancellationToken);
    }

    public async Task<AuditLog> AddAsync(AuditLog auditLog, CancellationToken cancellationToken = default)
    {
        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync(cancellationToken);
        return auditLog;
    }

    public async Task<List<AuditLog>> AddRangeAsync(List<AuditLog> auditLogs, CancellationToken cancellationToken = default)
    {
        _context.AuditLogs.AddRange(auditLogs);
        await _context.SaveChangesAsync(cancellationToken);
        return auditLogs;
    }

    public async Task DeleteAsync(AuditLog auditLog, CancellationToken cancellationToken = default)
    {
        _context.AuditLogs.Remove(auditLog);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteRangeAsync(List<AuditLog> auditLogs, CancellationToken cancellationToken = default)
    {
        _context.AuditLogs.RemoveRange(auditLogs);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
