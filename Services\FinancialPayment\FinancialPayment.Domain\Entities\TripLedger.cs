using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Comprehensive financial ledger for a specific trip
/// </summary>
public class TripLedger : AggregateRoot
{
    public Guid TripId { get; private set; }
    public Guid OrderId { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public Guid? BrokerId { get; private set; }
    public Guid? CarrierId { get; private set; }
    public string TripNumber { get; private set; } = string.Empty;
    public string LedgerNumber { get; private set; } = string.Empty;

    // Financial Summary
    public Money TotalOrderValue { get; private set; }
    public Money TotalPaid { get; private set; }
    public Money TotalPending { get; private set; }
    public Money TotalCommissions { get; private set; }
    public Money TotalTaxes { get; private set; }
    public Money NetAmount { get; private set; }
    public Money OutstandingBalance { get; private set; }

    // Status and Dates
    public TripLedgerStatus Status { get; private set; }
    public DateTime TripStartDate { get; private set; }
    public DateTime? TripEndDate { get; private set; }
    public DateTime? SettlementDate { get; private set; }
    public DateTime LastUpdated { get; private set; }

    // Collections
    private readonly List<TripLedgerEntry> _entries = new();
    public IReadOnlyList<TripLedgerEntry> Entries => _entries.AsReadOnly();

    private readonly List<TripLedgerMilestone> _milestones = new();
    public IReadOnlyList<TripLedgerMilestone> Milestones => _milestones.AsReadOnly();

    private readonly List<TripLedgerAdjustment> _adjustments = new();
    public IReadOnlyList<TripLedgerAdjustment> Adjustments => _adjustments.AsReadOnly();

    // Metadata
    public Dictionary<string, object> Metadata { get; private set; } = new();
    public string? Notes { get; private set; }

    private TripLedger() { } // EF Constructor

    public TripLedger(
        Guid tripId,
        Guid orderId,
        Guid transportCompanyId,
        string tripNumber,
        Money totalOrderValue,
        DateTime tripStartDate,
        Guid? brokerId = null,
        Guid? carrierId = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(tripNumber))
            throw new ArgumentException("Trip number cannot be empty", nameof(tripNumber));

        Id = Guid.NewGuid();
        TripId = tripId;
        OrderId = orderId;
        TransportCompanyId = transportCompanyId;
        BrokerId = brokerId;
        CarrierId = carrierId;
        TripNumber = tripNumber;
        LedgerNumber = GenerateLedgerNumber();
        TotalOrderValue = totalOrderValue ?? throw new ArgumentNullException(nameof(totalOrderValue));
        TripStartDate = tripStartDate;
        Status = TripLedgerStatus.Active;

        // Initialize financial amounts
        TotalPaid = Money.Zero(totalOrderValue.Currency);
        TotalPending = totalOrderValue;
        TotalCommissions = Money.Zero(totalOrderValue.Currency);
        TotalTaxes = Money.Zero(totalOrderValue.Currency);
        NetAmount = totalOrderValue;
        OutstandingBalance = totalOrderValue;

        LastUpdated = DateTime.UtcNow;
        CreatedAt = DateTime.UtcNow;
        Notes = notes;

        AddDomainEvent(new TripLedgerCreatedEvent(Id, TripId, OrderId, TotalOrderValue));
    }

    public void AddEntry(
        TripLedgerEntryType entryType,
        Money amount,
        string description,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, object>? metadata = null)
    {
        if (Status == TripLedgerStatus.Closed)
            throw new InvalidOperationException("Cannot add entries to a closed ledger");

        var entry = new TripLedgerEntry(
            Id,
            entryType,
            amount,
            description,
            relatedEntityId,
            relatedEntityType,
            metadata);

        _entries.Add(entry);
        RecalculateFinancials();
        LastUpdated = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerEntryAddedEvent(Id, entry.Id, entryType.ToString(), amount, "Trip ledger entry added"));
    }

    public void AddMilestone(
        string milestoneName,
        Money amount,
        DateTime dueDate,
        string description,
        bool isCompleted = false,
        DateTime? completedDate = null)
    {
        if (Status == TripLedgerStatus.Closed)
            throw new InvalidOperationException("Cannot add milestones to a closed ledger");

        var milestone = new TripLedgerMilestone(
            Id,
            milestoneName,
            amount,
            dueDate,
            description,
            isCompleted,
            completedDate);

        _milestones.Add(milestone);
        LastUpdated = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerMilestoneAddedEvent(Id, milestone.Id, milestoneName, amount, dueDate));
    }

    public void CompleteMilestone(Guid milestoneId, DateTime completedDate, string? notes = null)
    {
        var milestone = _milestones.FirstOrDefault(m => m.Id == milestoneId);
        if (milestone == null)
            throw new ArgumentException("Milestone not found", nameof(milestoneId));

        milestone.Complete(completedDate, notes);
        RecalculateFinancials();
        LastUpdated = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerMilestoneCompletedEvent(Id, milestoneId, milestone.MilestoneName, milestone.Amount, completedDate));
    }

    public void AddAdjustment(
        TripLedgerAdjustmentType adjustmentType,
        Money amount,
        string reason,
        Guid adjustedBy,
        string? approvalReference = null)
    {
        if (Status == TripLedgerStatus.Closed)
            throw new InvalidOperationException("Cannot add adjustments to a closed ledger");

        var adjustment = new TripLedgerAdjustment(
            Id,
            adjustmentType,
            amount,
            reason,
            adjustedBy,
            approvalReference);

        _adjustments.Add(adjustment);
        RecalculateFinancials();
        LastUpdated = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerAdjustmentAddedEvent(Id, adjustment.Id, adjustmentType.ToString(), amount, reason));
    }

    public void CompleteTrip(DateTime tripEndDate)
    {
        if (Status != TripLedgerStatus.Active)
            throw new InvalidOperationException("Can only complete active trip ledgers");

        TripEndDate = tripEndDate;
        Status = TripLedgerStatus.Completed;
        LastUpdated = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerCompletedEvent(Id, TripId, TotalOrderValue, tripEndDate));
    }

    public void SettleLedger(DateTime settlementDate, string? settlementNotes = null)
    {
        if (Status != TripLedgerStatus.Completed)
            throw new InvalidOperationException("Can only settle completed trip ledgers");

        if (OutstandingBalance.Amount > 0)
            throw new InvalidOperationException("Cannot settle ledger with outstanding balance");

        SettlementDate = settlementDate;
        Status = TripLedgerStatus.Settled;
        LastUpdated = DateTime.UtcNow;

        if (!string.IsNullOrWhiteSpace(settlementNotes))
            Notes = string.IsNullOrWhiteSpace(Notes) ? settlementNotes : $"{Notes}\n{settlementNotes}";

        AddDomainEvent(new TripLedgerSettledEvent(Id, TripId, TotalOrderValue, settlementDate, $"Settlement-{Id}"));
    }

    public void CloseLedger(string reason, Guid closedBy)
    {
        if (Status == TripLedgerStatus.Closed)
            throw new InvalidOperationException("Ledger is already closed");

        Status = TripLedgerStatus.Closed;
        LastUpdated = DateTime.UtcNow;

        Metadata["ClosedReason"] = reason;
        Metadata["ClosedBy"] = closedBy;
        Metadata["ClosedAt"] = DateTime.UtcNow;

        AddDomainEvent(new TripLedgerClosedEvent(Id, TripId, TotalOrderValue, DateTime.UtcNow, reason));
    }

    public TripLedgerSummary GetSummary()
    {
        return new TripLedgerSummary
        {
            LedgerId = Id,
            TripId = TripId,
            OrderId = OrderId,
            LedgerNumber = LedgerNumber,
            TripNumber = TripNumber,
            Status = Status,
            TotalOrderValue = TotalOrderValue,
            TotalPaid = TotalPaid,
            TotalPending = TotalPending,
            OutstandingBalance = OutstandingBalance,
            TotalEntries = _entries.Count,
            TotalMilestones = _milestones.Count,
            CompletedMilestones = _milestones.Count(m => m.IsCompleted),
            TotalAdjustments = _adjustments.Count,
            TripStartDate = TripStartDate,
            TripEndDate = TripEndDate,
            SettlementDate = SettlementDate,
            LastUpdated = LastUpdated
        };
    }

    private void RecalculateFinancials()
    {
        // Calculate totals from entries
        var creditEntries = _entries.Where(e => e.EntryType.IsCredit()).ToList();
        var debitEntries = _entries.Where(e => e.EntryType.IsDebit()).ToList();

        TotalPaid = creditEntries.Aggregate(Money.Zero(TotalOrderValue.Currency), (sum, entry) => sum + entry.Amount);

        TotalCommissions = _entries
            .Where(e => e.EntryType == TripLedgerEntryType.Commission)
            .Aggregate(Money.Zero(TotalOrderValue.Currency), (sum, entry) => sum + entry.Amount);

        TotalTaxes = _entries
            .Where(e => e.EntryType == TripLedgerEntryType.Tax)
            .Aggregate(Money.Zero(TotalOrderValue.Currency), (sum, entry) => sum + entry.Amount);

        // Apply adjustments
        var adjustmentTotal = _adjustments
            .Aggregate(Money.Zero(TotalOrderValue.Currency), (sum, adj) =>
                adj.AdjustmentType == TripLedgerAdjustmentType.Credit ? sum + adj.Amount : sum - adj.Amount);

        NetAmount = TotalOrderValue + adjustmentTotal - TotalCommissions - TotalTaxes;
        OutstandingBalance = NetAmount - TotalPaid;
        TotalPending = OutstandingBalance;
    }

    private string GenerateLedgerNumber()
    {
        return $"TL-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}


