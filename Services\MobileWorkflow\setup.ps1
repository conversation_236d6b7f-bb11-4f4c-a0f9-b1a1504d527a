# Mobile & Workflow Service Setup Script
# PowerShell script to set up the Mobile & Workflow Service

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "Development",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDatabase,
    
    [Parameter(Mandatory=$false)]
    [switch]$RunTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseDocker
)

Write-Host "🚀 Setting up Mobile & Workflow Service..." -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Blue

if (-not (Test-Command "dotnet")) {
    Write-Error "❌ .NET 8 SDK is required but not installed. Please install .NET 8 SDK."
    exit 1
}

$dotnetVersion = dotnet --version
Write-Host "✅ .NET SDK version: $dotnetVersion" -ForegroundColor Green

if ($UseDocker) {
    if (-not (Test-Command "docker")) {
        Write-Error "❌ Docker is required but not installed. Please install Docker."
        exit 1
    }
    
    if (-not (Test-Command "docker-compose")) {
        Write-Error "❌ Docker Compose is required but not installed. Please install Docker Compose."
        exit 1
    }
    
    Write-Host "✅ Docker and Docker Compose are available" -ForegroundColor Green
}

# Set working directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host "📁 Working directory: $(Get-Location)" -ForegroundColor Yellow

# Database setup
if (-not $SkipDatabase) {
    Write-Host "🗄️ Setting up database..." -ForegroundColor Blue
    
    if ($UseDocker) {
        Write-Host "🐳 Starting TimescaleDB with Docker..." -ForegroundColor Yellow
        
        # Check if container already exists
        $existingContainer = docker ps -a --filter "name=mobileworkflow-timescaledb" --format "{{.Names}}"
        
        if ($existingContainer) {
            Write-Host "📦 Stopping existing container..." -ForegroundColor Yellow
            docker stop mobileworkflow-timescaledb
            docker rm mobileworkflow-timescaledb
        }
        
        # Start TimescaleDB container
        docker run -d `
            --name mobileworkflow-timescaledb `
            -p 5432:5432 `
            -e POSTGRES_DB=mobileworkflow_db `
            -e POSTGRES_USER=timescale `
            -e POSTGRES_PASSWORD=timescale `
            -v "${PWD}/database-setup.sql:/docker-entrypoint-initdb.d/01-setup.sql" `
            timescale/timescaledb:latest-pg15
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ TimescaleDB container started successfully" -ForegroundColor Green
            
            # Wait for database to be ready
            Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
            Start-Sleep -Seconds 30
        } else {
            Write-Error "❌ Failed to start TimescaleDB container"
            exit 1
        }
    } else {
        Write-Host "📝 Please ensure PostgreSQL with TimescaleDB is running and execute:" -ForegroundColor Yellow
        Write-Host "   psql -h localhost -U postgres -f database-setup.sql" -ForegroundColor Cyan
        
        $response = Read-Host "Press Enter when database setup is complete, or 's' to skip"
        if ($response -eq 's') {
            Write-Host "⏭️ Skipping database setup" -ForegroundColor Yellow
        }
    }
}

# Restore NuGet packages
Write-Host "📦 Restoring NuGet packages..." -ForegroundColor Blue

$projects = @(
    "MobileWorkflow.Domain/MobileWorkflow.Domain.csproj",
    "MobileWorkflow.Application/MobileWorkflow.Application.csproj",
    "MobileWorkflow.Infrastructure/MobileWorkflow.Infrastructure.csproj",
    "MobileWorkflow.API/MobileWorkflow.API.csproj",
    "MobileWorkflow.Tests/MobileWorkflow.Tests.csproj"
)

foreach ($project in $projects) {
    if (Test-Path $project) {
        Write-Host "📦 Restoring $project..." -ForegroundColor Yellow
        dotnet restore $project
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "❌ Failed to restore packages for $project"
            exit 1
        }
    }
}

Write-Host "✅ All packages restored successfully" -ForegroundColor Green

# Build the solution
Write-Host "🔨 Building the solution..." -ForegroundColor Blue

dotnet build MobileWorkflow.API/MobileWorkflow.API.csproj -c Release

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Build failed"
    exit 1
}

Write-Host "✅ Build completed successfully" -ForegroundColor Green

# Run tests
if ($RunTests) {
    Write-Host "🧪 Running tests..." -ForegroundColor Blue
    
    # Unit tests
    if (Test-Path "MobileWorkflow.Tests/MobileWorkflow.Tests.csproj") {
        Write-Host "🔬 Running unit tests..." -ForegroundColor Yellow
        dotnet test MobileWorkflow.Tests/MobileWorkflow.Tests.csproj --configuration Release --logger "console;verbosity=detailed"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "⚠️ Some unit tests failed"
        } else {
            Write-Host "✅ Unit tests passed" -ForegroundColor Green
        }
    }
    
    # Integration tests
    if (Test-Path "MobileWorkflow.IntegrationTests/MobileWorkflow.IntegrationTests.csproj") {
        Write-Host "🔗 Running integration tests..." -ForegroundColor Yellow
        dotnet test MobileWorkflow.IntegrationTests/MobileWorkflow.IntegrationTests.csproj --configuration Release --logger "console;verbosity=detailed"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "⚠️ Some integration tests failed"
        } else {
            Write-Host "✅ Integration tests passed" -ForegroundColor Green
        }
    }
}

# Setup configuration
Write-Host "⚙️ Setting up configuration..." -ForegroundColor Blue

$configFile = "MobileWorkflow.API/appsettings.$Environment.json"
$templateFile = "MobileWorkflow.API/appsettings.json"

if (-not (Test-Path $configFile) -and (Test-Path $templateFile)) {
    Write-Host "📝 Creating environment-specific configuration..." -ForegroundColor Yellow
    Copy-Item $templateFile $configFile
    Write-Host "✅ Configuration file created: $configFile" -ForegroundColor Green
    Write-Host "📝 Please review and update the configuration as needed" -ForegroundColor Yellow
}

# Create logs directory
$logsDir = "logs"
if (-not (Test-Path $logsDir)) {
    New-Item -ItemType Directory -Path $logsDir | Out-Null
    Write-Host "✅ Logs directory created" -ForegroundColor Green
}

# Docker setup
if ($UseDocker) {
    Write-Host "🐳 Setting up Docker environment..." -ForegroundColor Blue
    
    # Build Docker image
    Write-Host "🔨 Building Docker image..." -ForegroundColor Yellow
    docker build -t mobileworkflow-service -f MobileWorkflow.API/Dockerfile ../../../
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker image built successfully" -ForegroundColor Green
    } else {
        Write-Error "❌ Failed to build Docker image"
        exit 1
    }
    
    # Start services with Docker Compose
    Write-Host "🚀 Starting services with Docker Compose..." -ForegroundColor Yellow
    docker-compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Services started successfully" -ForegroundColor Green
        Write-Host "🌐 Service URL: http://localhost:5014" -ForegroundColor Cyan
        Write-Host "📊 Swagger UI: http://localhost:5014/swagger" -ForegroundColor Cyan
        Write-Host "🏥 Health Check: http://localhost:5014/health" -ForegroundColor Cyan
    } else {
        Write-Error "❌ Failed to start services"
        exit 1
    }
} else {
    Write-Host "🚀 Setup completed! To run the service:" -ForegroundColor Green
    Write-Host "   cd MobileWorkflow.API" -ForegroundColor Cyan
    Write-Host "   dotnet run" -ForegroundColor Cyan
    Write-Host "" -ForegroundColor White
    Write-Host "🌐 Service will be available at:" -ForegroundColor Green
    Write-Host "   HTTP: http://localhost:5014" -ForegroundColor Cyan
    Write-Host "   HTTPS: https://localhost:7014" -ForegroundColor Cyan
    Write-Host "   Swagger: http://localhost:5014/swagger" -ForegroundColor Cyan
}

Write-Host "" -ForegroundColor White
Write-Host "📚 Additional Resources:" -ForegroundColor Green
Write-Host "   📖 Documentation: README.md" -ForegroundColor Cyan
Write-Host "   🐛 Issues: Create issues in the repository" -ForegroundColor Cyan
Write-Host "   💬 Support: Contact the development team" -ForegroundColor Cyan

Write-Host "" -ForegroundColor White
Write-Host "🎉 Mobile & Workflow Service setup completed successfully!" -ForegroundColor Green
