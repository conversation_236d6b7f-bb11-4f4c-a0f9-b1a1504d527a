using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Service for mapping and analyzing service dependencies
/// </summary>
public class ServiceDependencyMappingService : IServiceDependencyMappingService
{
    private readonly IServiceDependencyRepository _dependencyRepository;
    private readonly ITraceRepository _traceRepository;
    private readonly ISpanRepository _spanRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IHealthCheckRepository _healthCheckRepository;
    private readonly ILogger<ServiceDependencyMappingService> _logger;
    
    // Cache for performance
    private readonly ConcurrentDictionary<string, ServiceDependencyGraphDto> _graphCache = new();
    private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

    public ServiceDependencyMappingService(
        IServiceDependencyRepository dependencyRepository,
        ITraceRepository traceRepository,
        ISpanRepository spanRepository,
        IAlertRepository alertRepository,
        IHealthCheckRepository healthCheckRepository,
        ILogger<ServiceDependencyMappingService> logger)
    {
        _dependencyRepository = dependencyRepository ?? throw new ArgumentNullException(nameof(dependencyRepository));
        _traceRepository = traceRepository ?? throw new ArgumentNullException(nameof(traceRepository));
        _spanRepository = spanRepository ?? throw new ArgumentNullException(nameof(spanRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _healthCheckRepository = healthCheckRepository ?? throw new ArgumentNullException(nameof(healthCheckRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ServiceDependencyGraphDto> GenerateDependencyGraphAsync(
        bool includeHealthStatus = true, 
        TimeSpan? period = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"graph_{includeHealthStatus}_{period?.TotalMinutes ?? 0}";
            
            // Check cache first
            if (_graphCache.TryGetValue(cacheKey, out var cachedGraph) &&
                _cacheTimestamps.TryGetValue(cacheKey, out var cacheTime) &&
                DateTime.UtcNow - cacheTime < _cacheExpiry)
            {
                _logger.LogDebug("Returning cached dependency graph");
                return cachedGraph;
            }

            _logger.LogInformation("Generating service dependency graph");

            // Get all service dependencies
            var dependencies = await _dependencyRepository.GetAllAsync(cancellationToken);
            
            // Filter by period if specified
            if (period.HasValue)
            {
                var fromDate = DateTime.UtcNow.Subtract(period.Value);
                dependencies = dependencies.Where(d => d.LastSeen >= fromDate);
            }

            // Generate nodes (services)
            var serviceNames = dependencies
                .SelectMany(d => new[] { d.FromService, d.ToService })
                .Distinct()
                .ToList();

            var nodes = new List<ServiceNodeDto>();
            
            foreach (var serviceName in serviceNames)
            {
                var node = await CreateServiceNodeAsync(serviceName, includeHealthStatus, cancellationToken);
                nodes.Add(node);
            }

            // Generate edges (dependencies)
            var edges = dependencies.Select(d => new ServiceEdgeDto
            {
                FromService = d.FromService,
                ToService = d.ToService,
                OperationType = d.OperationType,
                CallCount = d.CallCount,
                AverageLatency = d.AverageLatency,
                ErrorRate = d.ErrorRate,
                Weight = CalculateEdgeWeight(d)
            }).ToList();

            var graph = new ServiceDependencyGraphDto
            {
                Nodes = nodes,
                Edges = edges,
                GeneratedAt = DateTime.UtcNow
            };

            // Cache the result
            _graphCache.TryAdd(cacheKey, graph);
            _cacheTimestamps.TryAdd(cacheKey, DateTime.UtcNow);

            _logger.LogInformation("Generated dependency graph with {NodeCount} nodes and {EdgeCount} edges",
                nodes.Count, edges.Count);

            return graph;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate dependency graph");
            throw;
        }
    }

    public async Task<List<ServiceImpactAnalysisDto>> AnalyzeServiceImpactAsync(
        string serviceName, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing impact for service {ServiceName}", serviceName);

            var impactAnalyses = new List<ServiceImpactAnalysisDto>();

            // Get direct dependencies (services this service calls)
            var directDependencies = await _dependencyRepository.GetDependenciesForServiceAsync(serviceName, cancellationToken);
            
            // Get dependents (services that call this service)
            var dependents = await _dependencyRepository.GetDependentsOfServiceAsync(serviceName, cancellationToken);

            // Analyze downstream impact (if this service fails)
            var downstreamImpact = await AnalyzeDownstreamImpactAsync(serviceName, dependents, cancellationToken);
            impactAnalyses.Add(downstreamImpact);

            // Analyze upstream impact (if dependencies fail)
            foreach (var dependency in directDependencies)
            {
                var upstreamImpact = await AnalyzeUpstreamImpactAsync(serviceName, dependency, cancellationToken);
                impactAnalyses.Add(upstreamImpact);
            }

            _logger.LogInformation("Completed impact analysis for {ServiceName} with {AnalysisCount} analyses",
                serviceName, impactAnalyses.Count);

            return impactAnalyses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze service impact for {ServiceName}", serviceName);
            throw;
        }
    }

    public async Task<ServiceTopologyDto> GenerateServiceTopologyAsync(
        string? rootService = null, 
        int maxDepth = 5, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating service topology with root {RootService} and max depth {MaxDepth}",
                rootService ?? "auto-detect", maxDepth);

            var topology = new ServiceTopologyDto
            {
                RootService = rootService,
                MaxDepth = maxDepth,
                GeneratedAt = DateTime.UtcNow,
                Layers = new List<ServiceLayerDto>()
            };

            // If no root service specified, find the most connected service
            if (string.IsNullOrEmpty(rootService))
            {
                rootService = await FindMostConnectedServiceAsync(cancellationToken);
                topology.RootService = rootService;
            }

            if (string.IsNullOrEmpty(rootService))
            {
                _logger.LogWarning("No root service found for topology generation");
                return topology;
            }

            // Build topology layers using BFS
            var visited = new HashSet<string>();
            var currentLayer = new List<string> { rootService };
            var depth = 0;

            while (currentLayer.Any() && depth < maxDepth)
            {
                var layer = new ServiceLayerDto
                {
                    Depth = depth,
                    Services = new List<ServiceTopologyNodeDto>()
                };

                var nextLayer = new List<string>();

                foreach (var service in currentLayer)
                {
                    if (visited.Contains(service))
                        continue;

                    visited.Add(service);

                    // Create topology node
                    var node = await CreateTopologyNodeAsync(service, depth, cancellationToken);
                    layer.Services.Add(node);

                    // Get dependencies for next layer
                    var dependencies = await _dependencyRepository.GetDependenciesForServiceAsync(service, cancellationToken);
                    nextLayer.AddRange(dependencies.Select(d => d.ToService).Where(s => !visited.Contains(s)));
                }

                topology.Layers.Add(layer);
                currentLayer = nextLayer.Distinct().ToList();
                depth++;
            }

            _logger.LogInformation("Generated topology with {LayerCount} layers and {ServiceCount} total services",
                topology.Layers.Count, visited.Count);

            return topology;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate service topology");
            throw;
        }
    }

    public async Task<List<ServiceDependencyDto>> GetCriticalPathAsync(
        string fromService, 
        string toService, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Finding critical path from {FromService} to {ToService}", fromService, toService);

            // Use Dijkstra's algorithm to find the shortest path based on latency
            var allDependencies = await _dependencyRepository.GetAllAsync(cancellationToken);
            var graph = BuildDependencyGraph(allDependencies);

            var path = FindShortestPath(graph, fromService, toService);
            
            if (!path.Any())
            {
                _logger.LogWarning("No path found from {FromService} to {ToService}", fromService, toService);
                return new List<ServiceDependencyDto>();
            }

            // Convert path to dependency DTOs
            var criticalPath = new List<ServiceDependencyDto>();
            
            for (int i = 0; i < path.Count - 1; i++)
            {
                var dependency = allDependencies.FirstOrDefault(d => 
                    d.FromService == path[i] && d.ToService == path[i + 1]);
                
                if (dependency != null)
                {
                    criticalPath.Add(MapToDto(dependency));
                }
            }

            _logger.LogInformation("Found critical path with {StepCount} steps", criticalPath.Count);
            return criticalPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to find critical path from {FromService} to {ToService}", fromService, toService);
            throw;
        }
    }

    public async Task RefreshDependenciesFromTracesAsync(
        TimeSpan period, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Refreshing dependencies from traces for period {Period}", period);

            var fromDate = DateTime.UtcNow.Subtract(period);
            var traces = await _traceRepository.SearchTracesAsync(
                fromDate: fromDate, 
                cancellationToken: cancellationToken);

            var dependencyUpdates = new Dictionary<string, ServiceDependencyUpdate>();

            foreach (var trace in traces)
            {
                await ProcessTraceForDependenciesAsync(trace, dependencyUpdates, cancellationToken);
            }

            // Apply updates to repository
            foreach (var update in dependencyUpdates.Values)
            {
                await ApplyDependencyUpdateAsync(update, cancellationToken);
            }

            // Clear cache after refresh
            _graphCache.Clear();
            _cacheTimestamps.Clear();

            _logger.LogInformation("Refreshed {DependencyCount} dependencies from traces", dependencyUpdates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh dependencies from traces");
            throw;
        }
    }

    private async Task<ServiceNodeDto> CreateServiceNodeAsync(
        string serviceName, 
        bool includeHealthStatus, 
        CancellationToken cancellationToken)
    {
        var node = new ServiceNodeDto
        {
            ServiceName = serviceName,
            Metadata = new Dictionary<string, object>()
        };

        if (includeHealthStatus)
        {
            // Get health status
            var healthChecks = await _healthCheckRepository.GetHealthChecksByServiceAsync(serviceName, cancellationToken);
            node.HealthStatus = DetermineOverallHealthStatus(healthChecks);

            // Get active alerts
            var alerts = await _alertRepository.GetAlertsByServiceAsync(serviceName, cancellationToken);
            node.ActiveAlerts = alerts.Count(a => a.Status == AlertStatus.Open);

            // Calculate uptime (simplified)
            node.UptimePercentage = CalculateUptimePercentage(healthChecks);

            // Add metadata
            node.Metadata["health_check_count"] = healthChecks.Count();
            node.Metadata["last_health_check"] = healthChecks.Any() ? 
                healthChecks.Max(hc => hc.LastChecked) : (DateTime?)null;
        }

        return node;
    }

    private async Task<ServiceImpactAnalysisDto> AnalyzeDownstreamImpactAsync(
        string serviceName, 
        IEnumerable<ServiceDependency> dependents, 
        CancellationToken cancellationToken)
    {
        var analysis = new ServiceImpactAnalysisDto
        {
            ServiceName = serviceName,
            ImpactType = ServiceImpactType.Downstream,
            AffectedServices = dependents.Select(d => d.FromService).ToList(),
            ImpactScore = CalculateImpactScore(dependents),
            Description = $"If {serviceName} fails, it would impact {dependents.Count()} dependent services",
            Recommendations = GenerateDownstreamRecommendations(serviceName, dependents),
            AnalyzedAt = DateTime.UtcNow
        };

        return analysis;
    }

    private async Task<ServiceImpactAnalysisDto> AnalyzeUpstreamImpactAsync(
        string serviceName, 
        ServiceDependency dependency, 
        CancellationToken cancellationToken)
    {
        var analysis = new ServiceImpactAnalysisDto
        {
            ServiceName = serviceName,
            ImpactType = ServiceImpactType.Upstream,
            AffectedServices = new List<string> { dependency.ToService },
            ImpactScore = CalculateUpstreamImpactScore(dependency),
            Description = $"If {dependency.ToService} fails, it would impact {serviceName}",
            Recommendations = GenerateUpstreamRecommendations(serviceName, dependency),
            AnalyzedAt = DateTime.UtcNow
        };

        return analysis;
    }

    private async Task<ServiceTopologyNodeDto> CreateTopologyNodeAsync(
        string serviceName, 
        int depth, 
        CancellationToken cancellationToken)
    {
        var dependencies = await _dependencyRepository.GetDependenciesForServiceAsync(serviceName, cancellationToken);
        var dependents = await _dependencyRepository.GetDependentsOfServiceAsync(serviceName, cancellationToken);

        return new ServiceTopologyNodeDto
        {
            ServiceName = serviceName,
            Depth = depth,
            DependencyCount = dependencies.Count(),
            DependentCount = dependents.Count(),
            IsLeaf = !dependencies.Any(),
            IsRoot = !dependents.Any(),
            Metadata = new Dictionary<string, object>
            {
                ["total_calls"] = dependencies.Sum(d => d.CallCount),
                ["average_error_rate"] = dependencies.Any() ? dependencies.Average(d => d.ErrorRate) : 0.0
            }
        };
    }

    private async Task<string?> FindMostConnectedServiceAsync(CancellationToken cancellationToken)
    {
        var dependencies = await _dependencyRepository.GetAllAsync(cancellationToken);
        
        var connectionCounts = new Dictionary<string, int>();
        
        foreach (var dependency in dependencies)
        {
            connectionCounts[dependency.FromService] = connectionCounts.GetValueOrDefault(dependency.FromService, 0) + 1;
            connectionCounts[dependency.ToService] = connectionCounts.GetValueOrDefault(dependency.ToService, 0) + 1;
        }

        return connectionCounts.Any() ? 
            connectionCounts.OrderByDescending(kvp => kvp.Value).First().Key : 
            null;
    }

    private Dictionary<string, List<(string, double)>> BuildDependencyGraph(IEnumerable<ServiceDependency> dependencies)
    {
        var graph = new Dictionary<string, List<(string, double)>>();
        
        foreach (var dependency in dependencies)
        {
            if (!graph.ContainsKey(dependency.FromService))
                graph[dependency.FromService] = new List<(string, double)>();
            
            var weight = dependency.AverageLatency?.TotalMilliseconds ?? 1000.0; // Default weight
            graph[dependency.FromService].Add((dependency.ToService, weight));
        }

        return graph;
    }

    private List<string> FindShortestPath(Dictionary<string, List<(string, double)>> graph, string start, string end)
    {
        // Simplified Dijkstra's algorithm implementation
        var distances = new Dictionary<string, double>();
        var previous = new Dictionary<string, string>();
        var unvisited = new HashSet<string>();

        // Initialize
        foreach (var node in graph.Keys)
        {
            distances[node] = double.MaxValue;
            unvisited.Add(node);
        }
        distances[start] = 0;

        while (unvisited.Any())
        {
            var current = unvisited.OrderBy(n => distances[n]).First();
            unvisited.Remove(current);

            if (current == end)
                break;

            if (!graph.ContainsKey(current))
                continue;

            foreach (var (neighbor, weight) in graph[current])
            {
                if (!unvisited.Contains(neighbor))
                    continue;

                var alt = distances[current] + weight;
                if (alt < distances[neighbor])
                {
                    distances[neighbor] = alt;
                    previous[neighbor] = current;
                }
            }
        }

        // Reconstruct path
        var path = new List<string>();
        var currentNode = end;
        
        while (previous.ContainsKey(currentNode))
        {
            path.Insert(0, currentNode);
            currentNode = previous[currentNode];
        }
        
        if (currentNode == start)
            path.Insert(0, start);

        return path;
    }

    private async Task ProcessTraceForDependenciesAsync(
        Trace trace, 
        Dictionary<string, ServiceDependencyUpdate> updates, 
        CancellationToken cancellationToken)
    {
        var spans = trace.Spans.OrderBy(s => s.StartTime).ToList();
        
        for (int i = 0; i < spans.Count - 1; i++)
        {
            var currentSpan = spans[i];
            var nextSpan = spans[i + 1];
            
            // Check if this represents a service-to-service call
            if (currentSpan.ServiceName != nextSpan.ServiceName && 
                nextSpan.ParentSpanId == currentSpan.SpanId)
            {
                var key = $"{currentSpan.ServiceName}->{nextSpan.ServiceName}:{nextSpan.OperationName}";
                
                if (!updates.ContainsKey(key))
                {
                    updates[key] = new ServiceDependencyUpdate
                    {
                        FromService = currentSpan.ServiceName,
                        ToService = nextSpan.ServiceName,
                        OperationType = nextSpan.OperationName,
                        CallCount = 0,
                        TotalLatency = TimeSpan.Zero,
                        ErrorCount = 0
                    };
                }

                var update = updates[key];
                update.CallCount++;
                update.TotalLatency = update.TotalLatency.Add(nextSpan.Duration ?? TimeSpan.Zero);
                
                if (nextSpan.HasError)
                    update.ErrorCount++;
            }
        }
    }

    private async Task ApplyDependencyUpdateAsync(ServiceDependencyUpdate update, CancellationToken cancellationToken)
    {
        var existing = await _dependencyRepository.GetDependencyAsync(
            update.FromService, update.ToService, update.OperationType, cancellationToken);

        if (existing != null)
        {
            var avgLatency = update.CallCount > 0 ? 
                TimeSpan.FromMilliseconds(update.TotalLatency.TotalMilliseconds / update.CallCount) : 
                TimeSpan.Zero;
            
            existing.UpdateStatistics(avgLatency, update.ErrorCount > 0);
            await _dependencyRepository.UpdateAsync(existing, cancellationToken);
        }
        else
        {
            var avgLatency = update.CallCount > 0 ? 
                TimeSpan.FromMilliseconds(update.TotalLatency.TotalMilliseconds / update.CallCount) : 
                TimeSpan.Zero;
            
            var newDependency = new ServiceDependency(
                update.FromService, update.ToService, update.OperationType, 
                update.CallCount, avgLatency);
            
            if (update.ErrorCount > 0)
            {
                newDependency.UpdateStatistics(avgLatency, true);
            }
            
            await _dependencyRepository.AddAsync(newDependency, cancellationToken);
        }
    }

    private double CalculateEdgeWeight(ServiceDependency dependency)
    {
        // Calculate weight based on call count and error rate
        var baseWeight = Math.Log10(dependency.CallCount + 1);
        var errorPenalty = dependency.ErrorRate * 10;
        return Math.Max(1.0, baseWeight - errorPenalty);
    }

    private HealthStatus DetermineOverallHealthStatus(IEnumerable<HealthCheck> healthChecks)
    {
        if (!healthChecks.Any())
            return HealthStatus.Unknown;

        var statuses = healthChecks.Select(hc => hc.CurrentStatus).ToList();
        
        if (statuses.Any(s => s == HealthStatus.Unhealthy))
            return HealthStatus.Unhealthy;
        
        if (statuses.Any(s => s == HealthStatus.Degraded))
            return HealthStatus.Degraded;
        
        return statuses.All(s => s == HealthStatus.Healthy) ? 
            HealthStatus.Healthy : HealthStatus.Unknown;
    }

    private double CalculateUptimePercentage(IEnumerable<HealthCheck> healthChecks)
    {
        if (!healthChecks.Any())
            return 0.0;

        var totalChecks = healthChecks.Sum(hc => hc.TotalChecks);
        var successfulChecks = healthChecks.Sum(hc => hc.SuccessfulChecks);
        
        return totalChecks > 0 ? (double)successfulChecks / totalChecks * 100.0 : 0.0;
    }

    private double CalculateImpactScore(IEnumerable<ServiceDependency> dependents)
    {
        // Calculate impact score based on number of dependents and their call volumes
        var totalCalls = dependents.Sum(d => d.CallCount);
        var dependentCount = dependents.Count();
        
        return Math.Log10(totalCalls + 1) * dependentCount;
    }

    private double CalculateUpstreamImpactScore(ServiceDependency dependency)
    {
        // Calculate impact score based on dependency characteristics
        var callVolume = Math.Log10(dependency.CallCount + 1);
        var errorPenalty = dependency.ErrorRate * 10;
        var latencyPenalty = dependency.AverageLatency?.TotalMilliseconds / 1000.0 ?? 0;
        
        return callVolume + errorPenalty + latencyPenalty;
    }

    private List<string> GenerateDownstreamRecommendations(string serviceName, IEnumerable<ServiceDependency> dependents)
    {
        var recommendations = new List<string>();
        
        if (dependents.Count() > 5)
        {
            recommendations.Add("Consider implementing circuit breakers for high-dependency services");
            recommendations.Add("Implement graceful degradation patterns");
        }
        
        if (dependents.Any(d => d.ErrorRate > 0.05))
        {
            recommendations.Add("Monitor and improve error handling in dependent services");
        }
        
        recommendations.Add("Implement comprehensive health checks and monitoring");
        recommendations.Add("Consider service redundancy and failover mechanisms");
        
        return recommendations;
    }

    private List<string> GenerateUpstreamRecommendations(string serviceName, ServiceDependency dependency)
    {
        var recommendations = new List<string>();
        
        if (dependency.ErrorRate > 0.05)
        {
            recommendations.Add("Implement retry logic with exponential backoff");
            recommendations.Add("Add circuit breaker pattern for this dependency");
        }
        
        if (dependency.AverageLatency?.TotalMilliseconds > 1000)
        {
            recommendations.Add("Consider caching strategies to reduce latency");
            recommendations.Add("Implement timeout and fallback mechanisms");
        }
        
        recommendations.Add("Monitor dependency health and implement alerts");
        
        return recommendations;
    }

    private ServiceDependencyDto MapToDto(ServiceDependency dependency)
    {
        return new ServiceDependencyDto
        {
            Id = dependency.Id,
            FromService = dependency.FromService,
            ToService = dependency.ToService,
            OperationType = dependency.OperationType,
            CallCount = dependency.CallCount,
            AverageLatency = dependency.AverageLatency,
            FirstSeen = dependency.FirstSeen,
            LastSeen = dependency.LastSeen,
            ErrorRate = dependency.ErrorRate,
            ErrorCount = dependency.ErrorCount,
            Metadata = dependency.Metadata
        };
    }

    private class ServiceDependencyUpdate
    {
        public string FromService { get; set; } = string.Empty;
        public string ToService { get; set; } = string.Empty;
        public string OperationType { get; set; } = string.Empty;
        public int CallCount { get; set; }
        public TimeSpan TotalLatency { get; set; }
        public int ErrorCount { get; set; }
    }
}
