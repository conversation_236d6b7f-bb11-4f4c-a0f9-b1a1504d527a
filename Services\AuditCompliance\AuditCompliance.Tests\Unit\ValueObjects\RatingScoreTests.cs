using Xunit;
using FluentAssertions;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Tests.Unit.ValueObjects;

public class RatingScoreTests
{
    [Theory]
    [InlineData(RatingScale.OneStar, 1.0)]
    [InlineData(RatingScale.TwoStars, 2.0)]
    [InlineData(RatingScale.ThreeStars, 3.0)]
    [InlineData(RatingScale.FourStars, 4.0)]
    [InlineData(RatingScale.FiveStars, 5.0)]
    public void RatingScore_FromScale_ShouldSetCorrectNumericValue(RatingScale scale, decimal expectedValue)
    {
        // Act
        var ratingScore = RatingScore.FromScale(scale);

        // Assert
        ratingScore.Scale.Should().Be(scale);
        ratingScore.NumericValue.Should().Be(expectedValue);
    }

    [Theory]
    [InlineData(1.0, RatingScale.OneStar)]
    [InlineData(1.4, RatingScale.OneStar)]
    [InlineData(1.5, RatingScale.TwoStars)]
    [InlineData(2.4, RatingScale.TwoStars)]
    [InlineData(2.5, RatingScale.ThreeStars)]
    [InlineData(3.4, RatingScale.ThreeStars)]
    [InlineData(3.5, RatingScale.FourStars)]
    [InlineData(4.4, RatingScale.FourStars)]
    [InlineData(4.5, RatingScale.FiveStars)]
    [InlineData(5.0, RatingScale.FiveStars)]
    public void RatingScore_FromNumeric_ShouldSetCorrectScale(decimal numericValue, RatingScale expectedScale)
    {
        // Act
        var ratingScore = RatingScore.FromNumeric(numericValue);

        // Assert
        ratingScore.NumericValue.Should().Be(numericValue);
        ratingScore.Scale.Should().Be(expectedScale);
    }

    [Theory]
    [InlineData(0.5)]
    [InlineData(5.5)]
    [InlineData(-1.0)]
    [InlineData(6.0)]
    public void RatingScore_FromNumeric_WithInvalidValue_ShouldThrowException(decimal invalidValue)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => RatingScore.FromNumeric(invalidValue));
    }

    [Fact]
    public void RatingScore_CalculateAverage_ShouldReturnCorrectAverage()
    {
        // Arrange
        var ratings = new[]
        {
            RatingScore.FromScale(RatingScale.FiveStars),
            RatingScore.FromScale(RatingScale.FourStars),
            RatingScore.FromScale(RatingScale.ThreeStars)
        };

        // Act
        var average = RatingScore.CalculateAverage(ratings);

        // Assert
        average.NumericValue.Should().Be(4.0m);
        average.Scale.Should().Be(RatingScale.FourStars);
    }

    [Fact]
    public void RatingScore_CalculateAverage_WithEmptyCollection_ShouldThrowException()
    {
        // Arrange
        var emptyRatings = new RatingScore[0];

        // Act & Assert
        Assert.Throws<ArgumentException>(() => RatingScore.CalculateAverage(emptyRatings));
    }

    [Theory]
    [InlineData(3.5, true)]
    [InlineData(4.0, true)]
    [InlineData(5.0, true)]
    [InlineData(3.4, false)]
    [InlineData(2.0, false)]
    [InlineData(1.0, false)]
    public void RatingScore_IsPositive_ShouldReturnCorrectValue(decimal numericValue, bool expectedResult)
    {
        // Arrange
        var ratingScore = RatingScore.FromNumeric(numericValue);

        // Act & Assert
        ratingScore.IsPositive.Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(1.0, true)]
    [InlineData(2.0, true)]
    [InlineData(2.4, true)]
    [InlineData(2.5, false)]
    [InlineData(3.0, false)]
    [InlineData(4.0, false)]
    public void RatingScore_IsNegative_ShouldReturnCorrectValue(decimal numericValue, bool expectedResult)
    {
        // Arrange
        var ratingScore = RatingScore.FromNumeric(numericValue);

        // Act & Assert
        ratingScore.IsNegative.Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(2.5, true)]
    [InlineData(3.0, true)]
    [InlineData(3.4, true)]
    [InlineData(2.4, false)]
    [InlineData(3.5, false)]
    [InlineData(4.0, false)]
    public void RatingScore_IsNeutral_ShouldReturnCorrectValue(decimal numericValue, bool expectedResult)
    {
        // Arrange
        var ratingScore = RatingScore.FromNumeric(numericValue);

        // Act & Assert
        ratingScore.IsNeutral.Should().Be(expectedResult);
    }

    [Theory]
    [InlineData(RatingScale.OneStar, "Poor")]
    [InlineData(RatingScale.TwoStars, "Below Average")]
    [InlineData(RatingScale.ThreeStars, "Average")]
    [InlineData(RatingScale.FourStars, "Good")]
    [InlineData(RatingScale.FiveStars, "Excellent")]
    public void RatingScore_GetDescription_ShouldReturnCorrectDescription(RatingScale scale, string expectedDescription)
    {
        // Arrange
        var ratingScore = RatingScore.FromScale(scale);

        // Act
        var description = ratingScore.GetDescription();

        // Assert
        description.Should().Be(expectedDescription);
    }

    [Fact]
    public void RatingScore_WithComment_ShouldStoreComment()
    {
        // Arrange
        var comment = "Great service!";

        // Act
        var ratingScore = RatingScore.FromScale(RatingScale.FiveStars, comment);

        // Assert
        ratingScore.Comment.Should().Be(comment);
    }

    [Fact]
    public void RatingScore_Equality_WithSameValues_ShouldBeEqual()
    {
        // Arrange
        var rating1 = RatingScore.FromScale(RatingScale.FourStars, "Good");
        var rating2 = RatingScore.FromScale(RatingScale.FourStars, "Good");

        // Act & Assert
        rating1.Should().Be(rating2);
        (rating1 == rating2).Should().BeTrue();
        (rating1 != rating2).Should().BeFalse();
    }

    [Fact]
    public void RatingScore_Equality_WithDifferentValues_ShouldNotBeEqual()
    {
        // Arrange
        var rating1 = RatingScore.FromScale(RatingScale.FourStars, "Good");
        var rating2 = RatingScore.FromScale(RatingScale.FiveStars, "Excellent");

        // Act & Assert
        rating1.Should().NotBe(rating2);
        (rating1 == rating2).Should().BeFalse();
        (rating1 != rating2).Should().BeTrue();
    }
}
