namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for anomaly statistics
/// </summary>
public class AnomalyStatisticsDto
{
    public DateTime StatisticsPeriodStart { get; set; }
    public DateTime StatisticsPeriodEnd { get; set; }
    public AnomalyCountStatistics Counts { get; set; } = new();
    public List<AnomalyTrendDto> Trends { get; set; } = new();
    public List<AnomalyCategoryStatistics> CategoryBreakdown { get; set; } = new();
    public List<AnomalyServiceStatistics> ServiceBreakdown { get; set; } = new();
    public AnomalyResolutionStatistics Resolution { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class AnomalyCountStatistics
{
    public int TotalAnomalies { get; set; }
    public int CriticalAnomalies { get; set; }
    public int WarningAnomalies { get; set; }
    public int InfoAnomalies { get; set; }
    public int ResolvedAnomalies { get; set; }
    public int PendingAnomalies { get; set; }
    public double AnomalyRate { get; set; }
}

public class AnomalyTrendDto
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
    public string Severity { get; set; } = string.Empty;
    public double TrendDirection { get; set; } // Positive = increasing, Negative = decreasing
}

public class AnomalyCategoryStatistics
{
    public string Category { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
    public string MostCommonType { get; set; } = string.Empty;
    public TimeSpan AverageResolutionTime { get; set; }
}

public class AnomalyServiceStatistics
{
    public string ServiceName { get; set; } = string.Empty;
    public int AnomalyCount { get; set; }
    public double AnomalyRate { get; set; }
    public string MostCommonAnomaly { get; set; } = string.Empty;
    public DateTime? LastAnomaly { get; set; }
}

public class AnomalyResolutionStatistics
{
    public TimeSpan AverageResolutionTime { get; set; }
    public TimeSpan MedianResolutionTime { get; set; }
    public double AutoResolutionRate { get; set; }
    public double ManualResolutionRate { get; set; }
    public int EscalatedAnomalies { get; set; }
}
