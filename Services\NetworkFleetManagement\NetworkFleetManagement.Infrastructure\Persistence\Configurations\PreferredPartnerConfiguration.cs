using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.ValueObjects;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class PreferredPartnerConfiguration : IEntityTypeConfiguration<PreferredPartner>
{
    public void Configure(EntityTypeBuilder<PreferredPartner> builder)
    {
        builder.ToTable("preferred_partners", "network_fleet");

        // Primary key
        builder.HasKey(p => p.Id);

        // Properties
        builder.Property(p => p.Id)
            .ValueGeneratedNever();

        builder.Property(p => p.UserId)
            .IsRequired();

        builder.Property(p => p.PartnerId)
            .IsRequired();

        builder.Property(p => p.PartnerType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.PreferenceLevel)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.Priority)
            .IsRequired();

        builder.Property(p => p.Status)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.CreatedAt)
            .IsRequired();

        builder.Property(p => p.ActivatedAt);

        builder.Property(p => p.DeactivatedAt);

        builder.Property(p => p.DeactivationReason)
            .HasMaxLength(500);

        builder.Property(p => p.Notes)
            .HasMaxLength(1000);

        // Financial properties
        builder.Property(p => p.PreferredCommissionRate)
            .HasPrecision(5, 4); // e.g., 0.1250 for 12.5%

        builder.Property(p => p.PreferredServiceRate)
            .HasPrecision(10, 2); // e.g., 1250.50

        // Exclusivity properties
        builder.Property(p => p.IsExclusive)
            .IsRequired();

        builder.Property(p => p.ExclusivityExpiresAt);

        // Agreement properties
        builder.Property(p => p.AgreementNumber)
            .HasMaxLength(100);

        builder.Property(p => p.AgreementStartDate);

        builder.Property(p => p.AgreementEndDate);

        builder.Property(p => p.PaymentTermsDays);

        builder.Property(p => p.SpecialTerms)
            .HasMaxLength(2000);

        // Auto-assignment properties
        builder.Property(p => p.AutoAssignEnabled)
            .IsRequired();

        builder.Property(p => p.AutoAssignThreshold)
            .HasPrecision(3, 2); // e.g., 4.50 for rating

        // JSON columns for lists
        builder.Property(p => p.PreferredRoutes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null!) ?? new List<string>())
            .HasColumnType("jsonb");

        builder.Property(p => p.PreferredLoadTypes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null!) ?? new List<string>())
            .HasColumnType("jsonb");

        builder.Property(p => p.ExcludedRoutes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null!) ?? new List<string>())
            .HasColumnType("jsonb");

        builder.Property(p => p.ExcludedLoadTypes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null!) ?? new List<string>())
            .HasColumnType("jsonb");

        // Notification preferences
        builder.Property(p => p.NotifyOnNewOpportunities)
            .IsRequired();

        builder.Property(p => p.NotifyOnPerformanceChanges)
            .IsRequired();

        builder.Property(p => p.NotifyOnContractExpiry)
            .IsRequired();

        builder.Property(p => p.LastCollaborationDate);

        // Complex type for PerformanceMetrics
        builder.OwnsOne(p => p.PerformanceMetrics, pm =>
        {
            pm.Property(m => m.OverallRating)
                .HasPrecision(3, 2)
                .HasColumnName("overall_rating");

            pm.Property(m => m.OnTimePerformance)
                .HasPrecision(5, 4)
                .HasColumnName("on_time_performance");

            pm.Property(m => m.QualityScore)
                .HasPrecision(3, 2)
                .HasColumnName("quality_score");

            pm.Property(m => m.CommunicationRating)
                .HasPrecision(3, 2)
                .HasColumnName("communication_rating");

            pm.Property(m => m.TotalOrders)
                .HasColumnName("total_orders");

            pm.Property(m => m.CompletedOrders)
                .HasColumnName("completed_orders");

            pm.Property(m => m.CancelledOrders)
                .HasColumnName("cancelled_orders");

            pm.Property(m => m.AverageResponseTime)
                .HasPrecision(8, 2)
                .HasColumnName("average_response_time");

            pm.Property(m => m.LastUpdated)
                .HasColumnName("performance_last_updated");
        });

        // Indexes for performance
        builder.HasIndex(p => p.UserId)
            .HasDatabaseName("ix_preferred_partners_user_id");

        builder.HasIndex(p => p.PartnerId)
            .HasDatabaseName("ix_preferred_partners_partner_id");

        builder.HasIndex(p => new { p.UserId, p.PartnerType })
            .HasDatabaseName("ix_preferred_partners_user_partner_type");

        builder.HasIndex(p => new { p.UserId, p.Status })
            .HasDatabaseName("ix_preferred_partners_user_status");

        builder.HasIndex(p => new { p.UserId, p.PreferenceLevel, p.Priority })
            .HasDatabaseName("ix_preferred_partners_user_preference_priority");

        builder.HasIndex(p => p.AutoAssignEnabled)
            .HasDatabaseName("ix_preferred_partners_auto_assign");

        builder.HasIndex(p => p.LastCollaborationDate)
            .HasDatabaseName("ix_preferred_partners_last_collaboration");

        // Unique constraint
        builder.HasIndex(p => new { p.UserId, p.PartnerId })
            .IsUnique()
            .HasDatabaseName("uq_preferred_partners_user_partner");

        // Audit fields
        builder.Property(p => p.CreatedAt)
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(p => p.UpdatedAt);
    }
}
