using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using Shared.Domain.Common;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Drivers;

public class CreateDriverCommandHandler : IRequestHandler<CreateDriverCommand, DriverDto>
{
    private readonly IDriverRepository _driverRepository;
    private readonly ICarrierRepository _carrierRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public CreateDriverCommandHandler(
        IDriverRepository driverRepository,
        ICarrierRepository carrierRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _driverRepository = driverRepository;
        _carrierRepository = carrierRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<DriverDto> Handle(CreateDriverCommand request, CancellationToken cancellationToken)
    {
        var dto = request.DriverDto;

        // Check if carrier exists
        var carrier = await _carrierRepository.GetByIdAsync(dto.CarrierId, cancellationToken);
        if (carrier == null)
        {
            throw new InvalidOperationException($"Carrier with ID {dto.CarrierId} not found");
        }

        // Check if driver already exists for this user
        var existingDriver = await _driverRepository.GetByUserIdAsync(dto.UserId, cancellationToken);
        if (existingDriver != null)
        {
            throw new InvalidOperationException($"Driver already exists for user {dto.UserId}");
        }

        // Check if license number is already in use
        var existingLicense = await _driverRepository.GetByLicenseNumberAsync(dto.LicenseNumber, cancellationToken);
        if (existingLicense != null)
        {
            throw new InvalidOperationException($"Driver with license number {dto.LicenseNumber} already exists");
        }

        // Create driver entity
        var driver = new Driver(
            dto.CarrierId,
            dto.UserId,
            dto.FirstName,
            dto.LastName,
            dto.PhoneNumber,
            dto.Email,
            dto.LicenseNumber,
            dto.LicenseExpiryDate,
            dto.AadharNumber,
            dto.PANNumber,
            dto.ProfilePhotoUrl,
            dto.Notes);

        // Add preferred routes and operational areas
        foreach (var route in dto.PreferredRoutes)
        {
            driver.AddPreferredRoute(route);
        }

        foreach (var area in dto.OperationalAreas)
        {
            driver.AddOperationalArea(area);
        }

        // Add to repository
        _driverRepository.Add(driver);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("driver.created", new
        {
            DriverId = driver.Id,
            CarrierId = driver.CarrierId,
            UserId = driver.UserId,
            FirstName = driver.FirstName,
            LastName = driver.LastName,
            Email = driver.Email,
            PhoneNumber = driver.PhoneNumber,
            LicenseNumber = driver.LicenseNumber,
            LicenseExpiryDate = driver.LicenseExpiryDate,
            Status = driver.Status.ToString(),
            OnboardingStatus = driver.OnboardingStatus.ToString(),
            CreatedAt = driver.CreatedAt
        }, cancellationToken);

        // Map to DTO and return
        var result = _mapper.Map<DriverDto>(driver);
        return result;
    }
}

