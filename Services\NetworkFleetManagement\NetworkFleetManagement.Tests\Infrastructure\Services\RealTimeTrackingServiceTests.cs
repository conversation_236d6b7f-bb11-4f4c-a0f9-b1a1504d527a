using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Moq;
using NetworkFleetManagement.Infrastructure.Hubs;
using NetworkFleetManagement.Infrastructure.Services;
using Xunit;

namespace NetworkFleetManagement.Tests.Infrastructure.Services;

public class RealTimeTrackingServiceTests
{
    private readonly Mock<IHubContext<FleetTrackingHub>> _mockHubContext;
    private readonly Mock<NetworkFleetCacheService> _mockCacheService;
    private readonly Mock<ILogger<RealTimeTrackingService>> _mockLogger;
    private readonly Mock<IHubCallerClients> _mockClients;
    private readonly Mock<IClientProxy> _mockClientProxy;
    private readonly RealTimeTrackingService _service;

    public RealTimeTrackingServiceTests()
    {
        _mockHubContext = new Mock<IHubContext<FleetTrackingHub>>();
        _mockCacheService = new Mock<NetworkFleetCacheService>(Mock.Of<Shared.Infrastructure.Caching.ICacheService>(), Mock.Of<ILogger<NetworkFleetCacheService>>());
        _mockLogger = new Mock<ILogger<RealTimeTrackingService>>();
        _mockClients = new Mock<IHubCallerClients>();
        _mockClientProxy = new Mock<IClientProxy>();

        _mockHubContext.Setup(x => x.Clients).Returns(_mockClients.Object);
        _mockClients.Setup(x => x.Group(It.IsAny<string>())).Returns(_mockClientProxy.Object);

        _service = new RealTimeTrackingService(_mockHubContext.Object, _mockCacheService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task BroadcastVehicleLocationUpdateAsync_WithValidData_BroadcastsToGroup()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var latitude = 40.7128;
        var longitude = -74.0060;
        var metadata = new Dictionary<string, object> { ["speed"] = 65 };

        _mockCacheService.Setup(x => x.SetVehicleLocationAsync(vehicleId, latitude, longitude, It.IsAny<DateTime>(), metadata))
            .Returns(Task.CompletedTask);

        // Act
        await _service.BroadcastVehicleLocationUpdateAsync(vehicleId, latitude, longitude, metadata);

        // Assert
        _mockClients.Verify(x => x.Group($"vehicle_{vehicleId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("VehicleLocationUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(vehicleId)), 
            default), Times.Once);
        _mockCacheService.Verify(x => x.SetVehicleLocationAsync(vehicleId, latitude, longitude, It.IsAny<DateTime>(), metadata), Times.Once);
    }

    [Fact]
    public async Task BroadcastVehicleStatusUpdateAsync_WithValidData_BroadcastsToGroup()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var status = "in_transit";
        var metadata = new Dictionary<string, object> { ["destination"] = "New York" };

        // Act
        await _service.BroadcastVehicleStatusUpdateAsync(vehicleId, status, metadata);

        // Assert
        _mockClients.Verify(x => x.Group($"vehicle_{vehicleId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("VehicleStatusUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(vehicleId)), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastDriverStatusUpdateAsync_WithValidData_BroadcastsToGroupAndCache()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var status = "on_duty";
        var metadata = new Dictionary<string, object> { ["shift_start"] = DateTime.UtcNow };

        _mockCacheService.Setup(x => x.SetDriverStatusAsync(driverId, status, It.IsAny<DateTime>(), metadata))
            .Returns(Task.CompletedTask);

        // Act
        await _service.BroadcastDriverStatusUpdateAsync(driverId, status, metadata);

        // Assert
        _mockClients.Verify(x => x.Group($"driver_{driverId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("DriverStatusUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(driverId)), 
            default), Times.Once);
        _mockCacheService.Verify(x => x.SetDriverStatusAsync(driverId, status, It.IsAny<DateTime>(), metadata), Times.Once);
    }

    [Fact]
    public async Task BroadcastDriverLocationUpdateAsync_WithValidData_BroadcastsToGroup()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var latitude = 34.0522;
        var longitude = -118.2437;
        var metadata = new Dictionary<string, object> { ["accuracy"] = 5 };

        // Act
        await _service.BroadcastDriverLocationUpdateAsync(driverId, latitude, longitude, metadata);

        // Assert
        _mockClients.Verify(x => x.Group($"driver_{driverId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("DriverLocationUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(driverId)), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastFleetSummaryUpdateAsync_WithValidData_BroadcastsToMultipleGroups()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var summaryData = new Dictionary<string, object>
        {
            ["totalVehicles"] = 50,
            ["activeVehicles"] = 45,
            ["availableDrivers"] = 30
        };

        // Act
        await _service.BroadcastFleetSummaryUpdateAsync(carrierId, summaryData);

        // Assert
        _mockClients.Verify(x => x.Group($"carrier_{carrierId}"), Times.Once);
        _mockClients.Verify(x => x.Group($"fleet_monitor_{carrierId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("FleetSummaryUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(carrierId)), 
            default), Times.Exactly(2));
    }

    [Fact]
    public async Task BroadcastFleetAlertAsync_WithValidData_BroadcastsToAlertGroups()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var alertType = "maintenance_overdue";
        var message = "Multiple vehicles have overdue maintenance";
        var alertData = new Dictionary<string, object> { ["count"] = 5 };

        // Act
        await _service.BroadcastFleetAlertAsync(carrierId, alertType, message, alertData);

        // Assert
        _mockClients.Verify(x => x.Group($"carrier_{carrierId}"), Times.Once);
        _mockClients.Verify(x => x.Group($"alerts_{carrierId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("FleetAlert", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(carrierId)), 
            default), Times.Exactly(2));
    }

    [Fact]
    public async Task BroadcastNetworkPerformanceUpdateAsync_WithValidData_BroadcastsAndCaches()
    {
        // Arrange
        var networkId = Guid.NewGuid();
        var performanceData = new Dictionary<string, object>
        {
            ["responseTime"] = 150,
            ["throughput"] = 1000,
            ["errorRate"] = 0.01
        };

        _mockCacheService.Setup(x => x.SetNetworkPerformanceAsync(networkId, performanceData, null))
            .Returns(Task.CompletedTask);

        // Act
        await _service.BroadcastNetworkPerformanceUpdateAsync(networkId, performanceData);

        // Assert
        _mockClients.Verify(x => x.Group($"network_{networkId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("NetworkPerformanceUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(networkId)), 
            default), Times.Once);
        _mockCacheService.Verify(x => x.SetNetworkPerformanceAsync(networkId, performanceData, null), Times.Once);
    }

    [Fact]
    public async Task BroadcastSystemAlertAsync_WithValidData_BroadcastsToGlobalGroup()
    {
        // Arrange
        var alertType = "system_maintenance";
        var message = "Scheduled maintenance in 1 hour";
        var alertData = new Dictionary<string, object> { ["duration"] = "2 hours" };

        // Act
        await _service.BroadcastSystemAlertAsync(alertType, message, alertData);

        // Assert
        _mockClients.Verify(x => x.Group("alerts_global"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("SystemAlert", 
            It.Is<object[]>(args => args.Length == 1), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastMaintenanceNotificationAsync_WithValidData_BroadcastsToAll()
    {
        // Arrange
        var message = "System maintenance scheduled";
        var scheduledTime = DateTime.UtcNow.AddHours(2);

        _mockClients.Setup(x => x.All).Returns(_mockClientProxy.Object);

        // Act
        await _service.BroadcastMaintenanceNotificationAsync(message, scheduledTime);

        // Assert
        _mockClients.Verify(x => x.All, Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("MaintenanceNotification", 
            It.Is<object[]>(args => args.Length == 1), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastAnalyticsUpdateAsync_WithCarrierId_BroadcastsToCarrierGroup()
    {
        // Arrange
        var analyticsType = "fleet_performance";
        var carrierId = Guid.NewGuid();
        var analyticsData = new Dictionary<string, object>
        {
            ["efficiency"] = 85.5,
            ["utilization"] = 92.3
        };

        // Act
        await _service.BroadcastAnalyticsUpdateAsync(analyticsType, analyticsData, carrierId);

        // Assert
        _mockClients.Verify(x => x.Group($"carrier_{carrierId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("AnalyticsUpdate", 
            It.Is<object[]>(args => args.Length == 1), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastAnalyticsUpdateAsync_WithoutCarrierId_BroadcastsToAll()
    {
        // Arrange
        var analyticsType = "system_performance";
        var analyticsData = new Dictionary<string, object>
        {
            ["totalUsers"] = 1000,
            ["activeConnections"] = 250
        };

        _mockClients.Setup(x => x.All).Returns(_mockClientProxy.Object);

        // Act
        await _service.BroadcastAnalyticsUpdateAsync(analyticsType, analyticsData, null);

        // Assert
        _mockClients.Verify(x => x.All, Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("AnalyticsUpdate", 
            It.Is<object[]>(args => args.Length == 1), 
            default), Times.Once);
    }

    [Fact]
    public async Task BroadcastDashboardUpdateAsync_WithValidData_BroadcastsToMultipleGroups()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var dashboardData = new Dictionary<string, object>
        {
            ["kpis"] = new Dictionary<string, object>
            {
                ["revenue"] = 50000,
                ["trips"] = 1250,
                ["efficiency"] = 88.5
            }
        };

        // Act
        await _service.BroadcastDashboardUpdateAsync(carrierId, dashboardData);

        // Assert
        _mockClients.Verify(x => x.Group($"carrier_{carrierId}"), Times.Once);
        _mockClients.Verify(x => x.Group($"fleet_monitor_{carrierId}"), Times.Once);
        _mockClientProxy.Verify(x => x.SendCoreAsync("DashboardUpdate", 
            It.Is<object[]>(args => args.Length == 2 && args[0].Equals(carrierId)), 
            default), Times.Exactly(2));
    }
}
