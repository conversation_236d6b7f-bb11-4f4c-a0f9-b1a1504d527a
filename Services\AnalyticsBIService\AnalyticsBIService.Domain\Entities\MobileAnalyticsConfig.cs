using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Mobile analytics configuration entity
/// </summary>
public class MobileAnalyticsConfig : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Platform { get; private set; }
    public string Configuration { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public Dictionary<string, object> Settings { get; private set; }
    public List<string> TrackedEvents { get; private set; }
    public DateTime? LastSyncDate { get; private set; }

    // Additional properties required by services
    public Guid UserId => CreatedBy; // Alias for CreatedBy
    public List<string> TrackedScreens { get; private set; }
    public bool EnableCrashReporting { get; private set; }
    public bool EnablePerformanceMonitoring { get; private set; }
    public bool EnableRealTimeAnalytics { get; private set; }
    public int DataRetentionDays { get; private set; }
    public Dictionary<string, object> CustomSettings => Settings; // Alias for Settings

    private MobileAnalyticsConfig()
    {
        Name = string.Empty;
        Description = string.Empty;
        Platform = string.Empty;
        Configuration = string.Empty;
        Settings = new Dictionary<string, object>();
        TrackedEvents = new List<string>();
        TrackedScreens = new List<string>();
        DataRetentionDays = 90; // Default value
    }

    public MobileAnalyticsConfig(
        string name,
        string description,
        string platform,
        string configuration,
        Guid createdBy,
        UserType userType,
        Dictionary<string, object>? settings = null,
        List<string>? trackedEvents = null,
        List<string>? trackedScreens = null,
        bool enableCrashReporting = true,
        bool enablePerformanceMonitoring = true,
        bool enableRealTimeAnalytics = true,
        int dataRetentionDays = 90)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy;
        UserType = userType;
        Settings = settings ?? new Dictionary<string, object>();
        TrackedEvents = trackedEvents ?? new List<string>();
        TrackedScreens = trackedScreens ?? new List<string>();
        EnableCrashReporting = enableCrashReporting;
        EnablePerformanceMonitoring = enablePerformanceMonitoring;
        EnableRealTimeAnalytics = enableRealTimeAnalytics;
        DataRetentionDays = dataRetentionDays;
        IsEnabled = true;
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void UpdateSettings(Dictionary<string, object> settings)
    {
        Settings = settings ?? throw new ArgumentNullException(nameof(settings));
        SetUpdatedAt();
    }

    public void AddTrackedEvent(string eventName)
    {
        if (!string.IsNullOrWhiteSpace(eventName) && !TrackedEvents.Contains(eventName))
        {
            TrackedEvents.Add(eventName);
            SetUpdatedAt();
        }
    }

    public void RemoveTrackedEvent(string eventName)
    {
        if (TrackedEvents.Remove(eventName))
        {
            SetUpdatedAt();
        }
    }

    public void RecordSync()
    {
        LastSyncDate = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Enable()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }

    public void UpdateTrackedEvents(List<string> trackedEvents)
    {
        TrackedEvents = trackedEvents ?? throw new ArgumentNullException(nameof(trackedEvents));
        SetUpdatedAt();
    }

    public void UpdateTrackedScreens(List<string> trackedScreens)
    {
        TrackedScreens = trackedScreens ?? throw new ArgumentNullException(nameof(trackedScreens));
        SetUpdatedAt();
    }

    public void UpdateCrashReporting(bool enableCrashReporting)
    {
        EnableCrashReporting = enableCrashReporting;
        SetUpdatedAt();
    }

    public void UpdatePerformanceMonitoring(bool enablePerformanceMonitoring)
    {
        EnablePerformanceMonitoring = enablePerformanceMonitoring;
        SetUpdatedAt();
    }

    public void UpdateRealTimeAnalytics(bool enableRealTimeAnalytics)
    {
        EnableRealTimeAnalytics = enableRealTimeAnalytics;
        SetUpdatedAt();
    }

    public void UpdateDataRetentionDays(int dataRetentionDays)
    {
        DataRetentionDays = dataRetentionDays;
        SetUpdatedAt();
    }
}
