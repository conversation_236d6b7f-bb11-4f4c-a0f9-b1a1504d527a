apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-api
  namespace: tli-analytics
  labels:
    app: analytics-api
    service: analytics-bi
    version: v1
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: analytics-api
  template:
    metadata:
      labels:
        app: analytics-api
        service: analytics-bi
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: analytics-api-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: analytics-api
        image: tli/analytics-bi-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        - name: ASPNETCORE_FORWARDEDHEADERS_ENABLED
          value: "true"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: database-connection-string
        - name: ConnectionStrings__Redis
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: redis-connection-string
        - name: JwtSettings__SecretKey
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: jwt-secret-key
        - name: JwtSettings__Issuer
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: jwt-issuer
        - name: JwtSettings__Audience
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: jwt-audience
        - name: Analytics__RealTimeProcessing
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: realtime-processing
        - name: Analytics__CacheExpirationMinutes
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: cache-expiration-minutes
        - name: Logging__LogLevel__Default
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: log-level
        - name: CORS__AllowedOrigins
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: cors-allowed-origins
        - name: FileStorage__S3__BucketName
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: s3-bucket-name
        - name: FileStorage__S3__Region
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: aws-region
        - name: FileStorage__S3__AccessKey
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: aws-access-key-id
        - name: FileStorage__S3__SecretKey
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: aws-secret-access-key
        - name: Monitoring__ApplicationInsights__InstrumentationKey
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: appinsights-instrumentation-key
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /api/analytics/health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/analytics/health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/analytics/health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: data
          mountPath: /app/data
        - name: config
          mountPath: /app/config
          readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      volumes:
      - name: logs
        emptyDir: {}
      - name: data
        emptyDir: {}
      - name: config
        configMap:
          name: analytics-config
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - analytics-api
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
---
apiVersion: v1
kind: Service
metadata:
  name: analytics-api-service
  namespace: tli-analytics
  labels:
    app: analytics-api
    service: analytics-bi
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "80"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: analytics-api
  sessionAffinity: None
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: analytics-api-sa
  namespace: tli-analytics
  labels:
    app: analytics-api
    service: analytics-bi
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: tli-analytics
  name: analytics-api-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: analytics-api-rolebinding
  namespace: tli-analytics
subjects:
- kind: ServiceAccount
  name: analytics-api-sa
  namespace: tli-analytics
roleRef:
  kind: Role
  name: analytics-api-role
  apiGroup: rbac.authorization.k8s.io
