using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Controller for Transport Company report generation and export
/// </summary>
[ApiController]
[Route("api/transport-company/reports")]
[Authorize]
public class TransportCompanyReportController : ControllerBase
{
    private readonly ITransportCompanyReportService _reportService;
    private readonly ILogger<TransportCompanyReportController> _logger;

    public TransportCompanyReportController(
        ITransportCompanyReportService reportService,
        ILogger<TransportCompanyReportController> logger)
    {
        _reportService = reportService;
        _logger = logger;
    }

    /// <summary>
    /// Get available report templates for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/templates")]
    [ProducesResponseType(typeof(List<ReportTemplateDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<ReportTemplateDto>>> GetAvailableReportTemplates(Guid transportCompanyId)
    {
        try
        {
            if (!await CanAccessReports(transportCompanyId))
            {
                return Forbid("You don't have permission to access reports for this transport company");
            }

            _logger.LogInformation("Getting available report templates for Transport Company {TransportCompanyId}", transportCompanyId);

            var templates = await _reportService.GetAvailableReportTemplatesAsync(transportCompanyId);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate a report
    /// </summary>
    [HttpPost("{transportCompanyId}/generate")]
    [ProducesResponseType(typeof(ReportGenerationResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ReportGenerationResultDto>> GenerateReport(
        Guid transportCompanyId,
        [FromBody] GenerateReportRequestDto request)
    {
        try
        {
            if (!await CanGenerateReports(transportCompanyId))
            {
                return Forbid("You don't have permission to generate reports for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            request.RequestedBy = GetCurrentUserId();

            _logger.LogInformation("Generating report {TemplateId} for Transport Company {TransportCompanyId}", 
                request.TemplateId, transportCompanyId);

            var result = await _reportService.GenerateReportAsync(request);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid report generation request for Transport Company {TransportCompanyId}", transportCompanyId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export a generated report
    /// </summary>
    [HttpPost("{transportCompanyId}/export")]
    [ProducesResponseType(typeof(ExportResultDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ExportResultDto>> ExportReport(
        Guid transportCompanyId,
        [FromBody] ExportReportRequestDto request)
    {
        try
        {
            if (!await CanExportReports(transportCompanyId))
            {
                return Forbid("You don't have permission to export reports for this transport company");
            }

            _logger.LogInformation("Exporting report {ExecutionId} in {Format} format for Transport Company {TransportCompanyId}", 
                request.ExecutionId, request.Format, transportCompanyId);

            var result = await _reportService.ExportReportAsync(request);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid report export request for Transport Company {TransportCompanyId}", transportCompanyId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get scheduled reports for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/scheduled")]
    [ProducesResponseType(typeof(List<ScheduledReportDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<ScheduledReportDto>>> GetScheduledReports(Guid transportCompanyId)
    {
        try
        {
            if (!await CanAccessReports(transportCompanyId))
            {
                return Forbid("You don't have permission to access scheduled reports for this transport company");
            }

            _logger.LogInformation("Getting scheduled reports for Transport Company {TransportCompanyId}", transportCompanyId);

            var scheduledReports = await _reportService.GetScheduledReportsAsync(transportCompanyId);
            return Ok(scheduledReports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scheduled reports for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a scheduled report
    /// </summary>
    [HttpPost("{transportCompanyId}/scheduled")]
    [ProducesResponseType(typeof(ScheduledReportDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult<ScheduledReportDto>> CreateScheduledReport(
        Guid transportCompanyId,
        [FromBody] CreateScheduledReportRequestDto request)
    {
        try
        {
            if (!await CanManageScheduledReports(transportCompanyId))
            {
                return Forbid("You don't have permission to create scheduled reports for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            request.CreatedBy = GetCurrentUserId();

            _logger.LogInformation("Creating scheduled report for Transport Company {TransportCompanyId}", transportCompanyId);

            var scheduledReport = await _reportService.CreateScheduledReportAsync(request);
            
            return CreatedAtAction(
                nameof(GetScheduledReports), 
                new { transportCompanyId }, 
                scheduledReport);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid scheduled report creation request for Transport Company {TransportCompanyId}", transportCompanyId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating scheduled report for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report execution status
    /// </summary>
    [HttpGet("execution/{executionId}/status")]
    [ProducesResponseType(typeof(ReportExecutionStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ReportExecutionStatusDto>> GetReportExecutionStatus(Guid executionId)
    {
        try
        {
            if (!await CanAccessExecution(executionId))
            {
                return Forbid("You don't have permission to access this report execution");
            }

            _logger.LogInformation("Getting execution status for report {ExecutionId}", executionId);

            var status = await _reportService.GetReportExecutionStatusAsync(executionId);
            return Ok(status);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Report execution {ExecutionId} not found", executionId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution status for report {ExecutionId}", executionId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report execution history for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/history")]
    [ProducesResponseType(typeof(List<ReportExecutionHistoryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<ReportExecutionHistoryDto>>> GetReportExecutionHistory(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            if (!await CanAccessReports(transportCompanyId))
            {
                return Forbid("You don't have permission to access report history for this transport company");
            }

            _logger.LogInformation("Getting report execution history for Transport Company {TransportCompanyId}", transportCompanyId);

            var history = await _reportService.GetReportExecutionHistoryAsync(transportCompanyId, fromDate, toDate);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report execution history for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a custom report template
    /// </summary>
    [HttpPost("{transportCompanyId}/templates/custom")]
    [ProducesResponseType(typeof(CustomReportTemplateDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult<CustomReportTemplateDto>> CreateCustomReportTemplate(
        Guid transportCompanyId,
        [FromBody] CreateCustomReportTemplateRequestDto request)
    {
        try
        {
            if (!await CanManageCustomTemplates(transportCompanyId))
            {
                return Forbid("You don't have permission to create custom report templates for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;
            request.CreatedBy = GetCurrentUserId();

            _logger.LogInformation("Creating custom report template for Transport Company {TransportCompanyId}", transportCompanyId);

            var customTemplate = await _reportService.CreateCustomReportTemplateAsync(request);
            
            return CreatedAtAction(
                nameof(GetAvailableReportTemplates), 
                new { transportCompanyId }, 
                customTemplate);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid custom template creation request for Transport Company {TransportCompanyId}", transportCompanyId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report template for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Preview a report before generation
    /// </summary>
    [HttpPost("{transportCompanyId}/preview")]
    [ProducesResponseType(typeof(ReportPreviewDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ReportPreviewDto>> PreviewReport(
        Guid transportCompanyId,
        [FromBody] PreviewReportRequestDto request)
    {
        try
        {
            if (!await CanAccessReports(transportCompanyId))
            {
                return Forbid("You don't have permission to preview reports for this transport company");
            }

            request.TransportCompanyId = transportCompanyId;

            _logger.LogInformation("Generating report preview for template {TemplateId} for Transport Company {TransportCompanyId}", 
                request.TemplateId, transportCompanyId);

            var preview = await _reportService.PreviewReportAsync(request);
            return Ok(preview);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid report preview request for Transport Company {TransportCompanyId}", transportCompanyId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report preview for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Download exported report file
    /// </summary>
    [HttpGet("download/{exportId}")]
    [ProducesResponseType(typeof(FileResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> DownloadReport(Guid exportId)
    {
        try
        {
            if (!await CanDownloadExport(exportId))
            {
                return Forbid("You don't have permission to download this report");
            }

            _logger.LogInformation("Downloading report export {ExportId}", exportId);

            // Implementation would retrieve file from storage and return it
            // For now, return a placeholder response
            var fileBytes = Array.Empty<byte>();
            var fileName = $"report_{exportId}.xlsx";
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            return File(fileBytes, contentType, fileName);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Report export {ExportId} not found", exportId);
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading report export {ExportId}", exportId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanAccessReports(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanGenerateReports(Guid transportCompanyId)
    {
        return await CanAccessReports(transportCompanyId);
    }

    private async Task<bool> CanExportReports(Guid transportCompanyId)
    {
        return await CanAccessReports(transportCompanyId);
    }

    private async Task<bool> CanManageScheduledReports(Guid transportCompanyId)
    {
        var userRoles = GetCurrentUserRoles();
        return userRoles.Contains("Admin") || userRoles.Contains("TransportCompany");
    }

    private async Task<bool> CanManageCustomTemplates(Guid transportCompanyId)
    {
        return await CanManageScheduledReports(transportCompanyId);
    }

    private async Task<bool> CanAccessExecution(Guid executionId)
    {
        // Implementation would check if user has access to this specific execution
        return await Task.FromResult(true);
    }

    private async Task<bool> CanDownloadExport(Guid exportId)
    {
        // Implementation would check if user has access to this specific export
        return await Task.FromResult(true);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}
