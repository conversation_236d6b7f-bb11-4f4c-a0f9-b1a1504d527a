using Microsoft.EntityFrameworkCore;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using UserManagement.Infrastructure.Data;
using System.Linq.Expressions;

namespace UserManagement.Infrastructure.Repositories
{
    public class AuditLogRepository : IAuditLogRepository
    {
        private readonly UserManagementDbContext _context;

        public AuditLogRepository(UserManagementDbContext context)
        {
            _context = context;
        }

        public async Task<AuditLog> AddAsync(AuditLog auditLog)
        {
            await _context.AuditLogs.AddAsync(auditLog);
            await _context.SaveChangesAsync();
            return auditLog;
        }

        public async Task<AuditLog?> GetByIdAsync(Guid id)
        {
            return await _context.AuditLogs.FindAsync(id);
        }

        public async Task<IEnumerable<AuditLog>> GetByUserIdAsync(Guid userId, int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.UserId == userId)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByEntityAsync(string entityType, Guid entityId, int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.EntityType == entityType && al.EntityId == entityId)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByActionAsync(AuditAction action, int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.Action == action)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetBySeverityAsync(AuditSeverity severity, int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.Severity == severity)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.Timestamp >= startDate && al.Timestamp <= endDate)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> SearchAsync(AuditLogSearchCriteria criteria)
        {
            var query = _context.AuditLogs.AsQueryable();

            // Apply filters
            if (criteria.UserId.HasValue)
                query = query.Where(al => al.UserId == criteria.UserId.Value);

            if (!string.IsNullOrEmpty(criteria.UserName))
                query = query.Where(al => al.UserName.Contains(criteria.UserName));

            if (!string.IsNullOrEmpty(criteria.UserRole))
                query = query.Where(al => al.UserRole == criteria.UserRole);

            if (!string.IsNullOrEmpty(criteria.EntityType))
                query = query.Where(al => al.EntityType == criteria.EntityType);

            if (criteria.EntityId.HasValue)
                query = query.Where(al => al.EntityId == criteria.EntityId.Value);

            if (criteria.Action.HasValue)
                query = query.Where(al => al.Action == criteria.Action.Value);

            if (criteria.Severity.HasValue)
                query = query.Where(al => al.Severity == criteria.Severity.Value);

            if (criteria.StartDate.HasValue)
                query = query.Where(al => al.Timestamp >= criteria.StartDate.Value);

            if (criteria.EndDate.HasValue)
                query = query.Where(al => al.Timestamp <= criteria.EndDate.Value);

            if (!string.IsNullOrEmpty(criteria.IpAddress))
                query = query.Where(al => al.IpAddress == criteria.IpAddress);

            if (!string.IsNullOrEmpty(criteria.SessionId))
                query = query.Where(al => al.SessionId == criteria.SessionId);

            if (!string.IsNullOrEmpty(criteria.CorrelationId))
                query = query.Where(al => al.CorrelationId == criteria.CorrelationId);

            if (!string.IsNullOrEmpty(criteria.SearchTerm))
            {
                query = query.Where(al => 
                    al.Description.Contains(criteria.SearchTerm) ||
                    al.UserName.Contains(criteria.SearchTerm) ||
                    al.EntityType.Contains(criteria.SearchTerm));
            }

            // Apply sorting
            query = ApplySorting(query, criteria.SortBy, criteria.SortDescending);

            // Apply pagination
            return await query
                .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.AuditLogs.CountAsync();
        }

        public async Task<int> GetCountByUserIdAsync(Guid userId)
        {
            return await _context.AuditLogs.CountAsync(al => al.UserId == userId);
        }

        public async Task<int> GetCountByEntityAsync(string entityType, Guid entityId)
        {
            return await _context.AuditLogs.CountAsync(al => al.EntityType == entityType && al.EntityId == entityId);
        }

        public async Task<int> GetCountByActionAsync(AuditAction action)
        {
            return await _context.AuditLogs.CountAsync(al => al.Action == action);
        }

        public async Task<int> GetCountBySeverityAsync(AuditSeverity severity)
        {
            return await _context.AuditLogs.CountAsync(al => al.Severity == severity);
        }

        public async Task<int> GetCountByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.AuditLogs.CountAsync(al => al.Timestamp >= startDate && al.Timestamp <= endDate);
        }

        public async Task<IEnumerable<AuditLog>> GetRecentAsync(int count = 100)
        {
            return await _context.AuditLogs
                .OrderByDescending(al => al.Timestamp)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetCriticalEventsAsync(int pageNumber = 1, int pageSize = 50)
        {
            return await _context.AuditLogs
                .Where(al => al.Severity == AuditSeverity.Critical || al.Severity == AuditSeverity.High)
                .OrderByDescending(al => al.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.AuditLogs.AnyAsync(al => al.Id == id);
        }

        private IQueryable<AuditLog> ApplySorting(IQueryable<AuditLog> query, string? sortBy, bool sortDescending)
        {
            if (string.IsNullOrEmpty(sortBy))
                sortBy = "Timestamp";

            Expression<Func<AuditLog, object>> keySelector = sortBy.ToLower() switch
            {
                "timestamp" => al => al.Timestamp,
                "action" => al => al.Action,
                "severity" => al => al.Severity,
                "username" => al => al.UserName,
                "entitytype" => al => al.EntityType,
                "description" => al => al.Description,
                _ => al => al.Timestamp
            };

            return sortDescending 
                ? query.OrderByDescending(keySelector)
                : query.OrderBy(keySelector);
        }
    }
}
