# Tax Configuration Implementation Guide

## Overview

This guide provides comprehensive information about implementing and using the Tax Configuration module in the Financial & Payment Service. The module handles GST and TDS calculations, HSN code management, and multi-jurisdiction tax compliance for the Indian market.

## Architecture Overview

### Domain Layer
- **Entities**: TaxConfiguration, GstConfiguration, TdsConfiguration, HsnCode
- **Value Objects**: TaxJurisdiction, TaxRate, TdsThreshold, HsnCodeDetails
- **Enums**: GstRate, TdsSection, EntityType, ServiceCategory, LocationType

### Application Layer
- **Commands**: Create, Update, Delete tax configurations
- **Queries**: Retrieve tax configurations and calculate taxes
- **Services**: TaxConfigurationService for comprehensive tax calculations

### Infrastructure Layer
- **Repositories**: Data access for all tax configuration entities
- **Services**: Enhanced payment and invoice services with tax awareness

## Key Concepts

### Tax Jurisdiction
Represents geographical boundaries for tax applicability:
```csharp
public class TaxJurisdiction
{
    public string Country { get; }
    public string? State { get; }
    public string? City { get; }
    public LocationType Type { get; }
}
```

### GST Configuration
Manages Goods and Services Tax settings:
- Service category-specific rates
- HSN code mappings
- Reverse charge applicability
- Minimum/maximum amount thresholds

### TDS Configuration
Manages Tax Deducted at Source settings:
- Section-wise rates (194C, 194H, 194I, 194J)
- Entity type-specific rules
- Threshold amounts
- PAN-based rate variations

### HSN Code Management
Harmonized System of Nomenclature codes:
- Complete HSN code database
- GST rate mappings
- Category-wise organization
- Search and validation capabilities

## Implementation Steps

### 1. Database Setup

Run the migration scripts to create tax configuration tables:

```bash
# Apply tax configuration migrations
dotnet ef database update --project FinancialPayment.Infrastructure

# Seed initial tax data
dotnet run --project FinancialPayment.API -- --seed-tax-data
```

### 2. Configuration Setup

Add tax configuration settings to `appsettings.json`:

```json
{
  "TaxConfiguration": {
    "DefaultCurrency": "INR",
    "DefaultGstRate": 18.0,
    "DefaultTdsRate": 2.0,
    "EnableAuditTrail": true,
    "CacheExpirationMinutes": 60,
    "MaxCalculationAmount": 10000000.00
  }
}
```

### 3. Service Registration

Register tax services in `Program.cs`:

```csharp
// Tax configuration services
builder.Services.AddScoped<ITaxConfigurationService, TaxConfigurationService>();
builder.Services.AddScoped<ITaxConfigurationRepository, TaxConfigurationRepository>();
builder.Services.AddScoped<IGstConfigurationRepository, GstConfigurationRepository>();
builder.Services.AddScoped<ITdsConfigurationRepository, TdsConfigurationRepository>();
builder.Services.AddScoped<IHsnCodeRepository, HsnCodeRepository>();

// Enhanced services with tax awareness
builder.Services.Decorate<IPaymentService, TaxAwarePaymentService>();
builder.Services.AddScoped<TaxAwareInvoiceService>();
```

## Usage Examples

### 1. Calculate Comprehensive Tax

```csharp
public async Task<TaxCalculationResult> CalculateTaxAsync(
    Money baseAmount,
    ServiceCategory serviceCategory,
    TaxJurisdiction jurisdiction,
    EntityType entityType)
{
    var result = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
        baseAmount,
        serviceCategory,
        jurisdiction,
        entityType,
        TdsSection.Section194C,
        "9967", // HSN code
        true); // Has PAN

    return result;
}
```

### 2. Create Enhanced Commission with Tax

```csharp
public async Task<EnhancedCommission> CreateCommissionWithTaxAsync(
    Commission baseCommission)
{
    var taxResult = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
        baseCommission.CalculatedAmount,
        ServiceCategory.Brokerage,
        new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City),
        EntityType.Company,
        TdsSection.Section194H,
        "9972",
        true);

    var enhancedCommission = new EnhancedCommission(baseCommission, taxResult);
    
    await _commissionRepository.AddAsync(enhancedCommission);
    await _unitOfWork.SaveChangesAsync();

    return enhancedCommission;
}
```

### 3. Process Tax-Aware Payment

```csharp
public async Task<PaymentResult> ProcessTaxAwarePaymentAsync(
    Money baseAmount,
    Guid userId,
    ServiceCategory serviceCategory)
{
    var jurisdiction = new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City);
    
    var paymentResult = await _taxAwarePaymentService.ProcessPaymentWithTaxAsync(
        baseAmount,
        userId,
        serviceCategory,
        jurisdiction,
        EntityType.Company,
        TdsSection.Section194C,
        "9967",
        true,
        "pm_razorpay_123");

    return paymentResult;
}
```

### 4. Generate Tax-Aware Invoice

```csharp
public async Task<TaxAwareInvoice> GenerateInvoiceAsync(
    Guid orderId,
    Money baseAmount,
    CustomerDetails customer,
    BillingDetails billing)
{
    var lineItems = new List<InvoiceLineItem>
    {
        new InvoiceLineItem(
            "Transportation Service",
            "Freight transportation from Mumbai to Delhi",
            1,
            baseAmount,
            5.0m, // GST rate
            "9967") // HSN code
    };

    var invoice = await _taxAwareInvoiceService.GenerateInvoiceWithTaxAsync(
        orderId,
        baseAmount,
        customer,
        billing,
        ServiceCategory.Transportation,
        new TaxJurisdiction("India", "Maharashtra", "Mumbai", LocationType.City),
        EntityType.Company,
        lineItems,
        TdsSection.Section194C,
        "9967",
        true,
        "Invoice for transportation services");

    return invoice;
}
```

## Configuration Management

### 1. Creating Tax Configurations

```csharp
public async Task<TaxConfiguration> CreateTaxConfigurationAsync()
{
    var jurisdiction = new TaxJurisdiction("India", "Karnataka", "Bangalore", LocationType.City);
    
    var taxConfig = new TaxConfiguration(
        "Karnataka Tax Configuration",
        "Tax configuration for Karnataka state",
        jurisdiction,
        DateTime.UtcNow,
        "admin",
        null, // No end date
        false, // Not default
        2); // Priority

    taxConfig.UpdateTaxSettings(
        enableGstCalculation: true,
        enableTdsCalculation: true,
        enableReverseCharge: false,
        requireHsnCode: true,
        defaultGstRate: 18.0m,
        defaultTdsRate: 2.0m,
        defaultCurrency: "INR",
        "admin");

    await _taxConfigurationRepository.AddAsync(taxConfig);
    await _unitOfWork.SaveChangesAsync();

    return taxConfig;
}
```

### 2. Managing GST Configurations

```csharp
public async Task<GstConfiguration> CreateGstConfigurationAsync()
{
    var jurisdiction = new TaxJurisdiction("India", "", "", LocationType.Country);
    var taxRate = new TaxRate(12.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null);

    var gstConfig = new GstConfiguration(
        "Warehousing GST Configuration",
        "GST configuration for warehousing services",
        ServiceCategory.Warehousing,
        jurisdiction,
        GstRate.Rate12,
        taxRate,
        Money.Zero("INR"),
        new Money(10000000, "INR"),
        "admin",
        "9963", // HSN for warehousing
        false, // No reverse charge
        null);

    await _gstConfigurationRepository.AddAsync(gstConfig);
    await _unitOfWork.SaveChangesAsync();

    return gstConfig;
}
```

### 3. Managing TDS Configurations

```csharp
public async Task<TdsConfiguration> CreateTdsConfigurationAsync()
{
    var taxRate = new TaxRate(10.0m, TaxCalculationMethod.Percentage, DateTime.UtcNow, null);
    var threshold = new TdsThreshold(
        new Money(30000, "INR"), // Single payment threshold
        new Money(30000, "INR"), // Annual threshold
        EntityType.Company,
        true); // Requires PAN

    var tdsConfig = new TdsConfiguration(
        "Section 194J TDS Configuration",
        "TDS configuration for professional services",
        TdsSection.Section194J,
        EntityType.Company,
        taxRate,
        threshold,
        "admin",
        true, // Requires PAN
        20.0m, // Higher rate without PAN
        "Applicable for professional or technical services");

    await _tdsConfigurationRepository.AddAsync(tdsConfig);
    await _unitOfWork.SaveChangesAsync();

    return tdsConfig;
}
```

## Best Practices

### 1. Tax Calculation Caching
- Cache frequently used tax configurations
- Implement cache invalidation on configuration updates
- Use distributed caching for multi-instance deployments

### 2. Error Handling
- Validate input parameters before tax calculations
- Provide meaningful error messages for tax configuration issues
- Log all tax calculation errors for audit purposes

### 3. Audit Trail
- Maintain complete audit trails for all tax configuration changes
- Log all tax calculations with input parameters and results
- Implement change tracking for compliance requirements

### 4. Performance Optimization
- Index frequently queried fields (service category, jurisdiction, etc.)
- Use database views for complex tax calculation queries
- Implement pagination for large result sets

### 5. Testing
- Create comprehensive unit tests for all tax calculation scenarios
- Implement integration tests for end-to-end tax workflows
- Test edge cases like threshold boundaries and rate changes

## Compliance Considerations

### 1. GST Compliance
- Ensure accurate HSN code mappings
- Handle reverse charge scenarios correctly
- Maintain proper tax invoicing formats

### 2. TDS Compliance
- Implement correct threshold calculations
- Handle PAN/non-PAN scenarios appropriately
- Generate proper TDS certificates

### 3. Audit Requirements
- Maintain complete transaction logs
- Implement proper data retention policies
- Ensure data integrity and immutability

## Troubleshooting

### Common Issues

1. **Incorrect Tax Calculations**
   - Verify tax configuration settings
   - Check HSN code mappings
   - Validate threshold amounts

2. **Performance Issues**
   - Review database indexes
   - Check cache configuration
   - Optimize complex queries

3. **Configuration Conflicts**
   - Review jurisdiction hierarchies
   - Check effective date ranges
   - Validate priority settings

### Debugging Tools

- Enable detailed logging for tax calculations
- Use SQL profiler for database performance analysis
- Implement health checks for tax configuration services

## Migration Guide

When upgrading from basic commission calculations to tax-aware processing:

1. **Data Migration**
   - Migrate existing commissions to enhanced format
   - Populate tax configuration data
   - Update payment records with tax details

2. **Code Updates**
   - Replace basic payment services with tax-aware versions
   - Update commission calculation logic
   - Modify invoice generation processes

3. **Testing**
   - Verify tax calculations against known scenarios
   - Test backward compatibility
   - Validate compliance requirements
