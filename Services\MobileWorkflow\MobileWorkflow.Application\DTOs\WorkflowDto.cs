namespace MobileWorkflow.Application.DTOs;

public class WorkflowDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Dictionary<string, object> Definition { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string TriggerType { get; set; } = string.Empty;
    public Dictionary<string, object> TriggerConfiguration { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? LastExecuted { get; set; }
    public int ExecutionCount { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateWorkflowDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Definition { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string TriggerType { get; set; } = string.Empty;
    public Dictionary<string, object> TriggerConfiguration { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateWorkflowDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Version { get; set; }
    public Dictionary<string, object>? Definition { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? TriggerConfiguration { get; set; }
    public bool? IsActive { get; set; }
}

public class WorkflowTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> TemplateDefinition { get; set; } = new();
    public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
    public List<string> RequiredParameters { get; set; } = new();
    public List<string> OptionalParameters { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsPublic { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class WorkflowExecutionDto
{
    public Guid Id { get; set; }
    public Guid WorkflowId { get; set; }
    public Guid TriggeredBy { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> InputData { get; set; } = new();
    public Dictionary<string, object> OutputData { get; set; } = new();
    public Dictionary<string, object> Context { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string? TriggerSource { get; set; }
    public int CurrentStepIndex { get; set; }
    public string? CurrentStepId { get; set; }
    public Dictionary<string, object> ExecutionLog { get; set; } = new();
    public WorkflowDto? Workflow { get; set; }
    public List<WorkflowTaskDto> Tasks { get; set; } = new();
}

public class StartWorkflowExecutionDto
{
    public Guid WorkflowId { get; set; }
    public Guid TriggeredBy { get; set; }
    public Dictionary<string, object> InputData { get; set; } = new();
    public string? TriggerSource { get; set; }
}

public class WorkflowTaskDto
{
    public Guid Id { get; set; }
    public Guid? WorkflowId { get; set; }
    public Guid? WorkflowExecutionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int Priority { get; set; }
    public Guid? AssignedTo { get; set; }
    public string? AssignedToRole { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Result { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public TimeSpan? SLA { get; set; }
    public bool IsOverdue { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CreateWorkflowTaskDto
{
    public Guid? WorkflowExecutionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Priority { get; set; } = 2;
    public Guid? AssignedTo { get; set; }
    public string? AssignedToRole { get; set; }
    public DateTime? DueDate { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public TimeSpan? SLA { get; set; }
}

public class UpdateWorkflowTaskDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Status { get; set; }
    public int? Priority { get; set; }
    public Guid? AssignedTo { get; set; }
    public string? AssignedToRole { get; set; }
    public DateTime? DueDate { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
    public Dictionary<string, object>? Result { get; set; }
    public string? ErrorMessage { get; set; }
}

public class WorkflowStepDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string StepType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> InputSchema { get; set; } = new();
    public Dictionary<string, object> OutputSchema { get; set; } = new();
    public List<string> RequiredInputs { get; set; } = new();
    public List<string> OptionalInputs { get; set; } = new();
    public int Order { get; set; }
    public bool IsRequired { get; set; }
    public TimeSpan? Timeout { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class WorkflowTransitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid FromStepId { get; set; }
    public Guid ToStepId { get; set; }
    public Dictionary<string, object> Conditions { get; set; } = new();
    public Dictionary<string, object> Actions { get; set; } = new();
    public string TriggerType { get; set; } = string.Empty;
    public bool IsAutomatic { get; set; }
    public int Priority { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
