using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.Entities;

public class DisputeComment : BaseEntity
{
    public Guid DisputeId { get; private set; }
    public Guid AuthorId { get; private set; }
    public ParticipantRole AuthorRole { get; private set; }
    public string Content { get; private set; }
    public bool IsInternal { get; private set; }


    // Navigation properties
    public PaymentDispute Dispute { get; private set; } = null!;

    private DisputeComment() { }

    public DisputeComment(
        Guid disputeId,
        Guid authorId,
        ParticipantRole authorRole,
        string content,
        bool isInternal = false)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty");

        Id = Guid.NewGuid();
        DisputeId = disputeId;
        AuthorId = authorId;
        AuthorRole = authorRole;
        Content = content;
        IsInternal = isInternal;
        CreatedAt = DateTime.UtcNow;
    }
}
