using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Data pipeline execution entity for tracking pipeline execution history
/// </summary>
public class DataPipelineExecution : BaseEntity
{
    public Guid PipelineId { get; private set; }
    public string ExecutionStatus { get; private set; } // Success, Failed, Running, Cancelled
    public int RecordsProcessed { get; private set; }
    public DateTime ExecutionStartTime { get; private set; }
    public DateTime ExecutionEndTime { get; private set; }
    public string? ErrorMessage { get; private set; }
    public TimeSpan ExecutionDuration => ExecutionEndTime - ExecutionStartTime;
    public string? ExecutionLog { get; private set; }
    public string? ExecutionMetadata { get; private set; } // JSON serialized metadata
    public long InputRecordCount { get; private set; }
    public long OutputRecordCount { get; private set; }
    public long ErrorRecordCount { get; private set; }
    public decimal SuccessRate { get; private set; }
    public string? TriggeredBy { get; private set; } // Manual, Scheduled, API
    public Guid? TriggeredByUserId { get; private set; }

    // Private constructor for EF Core
    private DataPipelineExecution() 
    {
        ExecutionStatus = string.Empty;
    }

    public DataPipelineExecution(
        Guid pipelineId,
        string executionStatus,
        int recordsProcessed,
        DateTime executionStartTime,
        DateTime executionEndTime,
        string? errorMessage = null,
        string? triggeredBy = null,
        Guid? triggeredByUserId = null)
    {
        PipelineId = pipelineId;
        ExecutionStatus = executionStatus ?? throw new ArgumentNullException(nameof(executionStatus));
        RecordsProcessed = recordsProcessed;
        ExecutionStartTime = executionStartTime;
        ExecutionEndTime = executionEndTime;
        ErrorMessage = errorMessage;
        TriggeredBy = triggeredBy;
        TriggeredByUserId = triggeredByUserId;
        InputRecordCount = recordsProcessed;
        OutputRecordCount = recordsProcessed;
        ErrorRecordCount = 0;
        SuccessRate = executionStatus == "Success" ? 100 : 0;
    }

    public void UpdateStatus(string status, string? errorMessage = null)
    {
        ExecutionStatus = status ?? throw new ArgumentNullException(nameof(status));
        ErrorMessage = errorMessage;
        SetUpdatedAt();
    }

    public void SetExecutionLog(string log)
    {
        ExecutionLog = log;
        SetUpdatedAt();
    }

    public void SetExecutionMetadata(string metadata)
    {
        ExecutionMetadata = metadata;
        SetUpdatedAt();
    }

    public void UpdateRecordCounts(long inputCount, long outputCount, long errorCount)
    {
        InputRecordCount = inputCount;
        OutputRecordCount = outputCount;
        ErrorRecordCount = errorCount;
        
        // Calculate success rate
        if (inputCount > 0)
        {
            SuccessRate = (decimal)(outputCount) / inputCount * 100;
        }
        else
        {
            SuccessRate = 0;
        }
        
        SetUpdatedAt();
    }

    public void MarkCompleted(DateTime endTime, int recordsProcessed, string? errorMessage = null)
    {
        ExecutionEndTime = endTime;
        RecordsProcessed = recordsProcessed;
        ExecutionStatus = string.IsNullOrEmpty(errorMessage) ? "Success" : "Failed";
        ErrorMessage = errorMessage;
        SetUpdatedAt();
    }

    public void MarkFailed(DateTime endTime, string errorMessage)
    {
        ExecutionEndTime = endTime;
        ExecutionStatus = "Failed";
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        SetUpdatedAt();
    }

    public void MarkCancelled(DateTime endTime)
    {
        ExecutionEndTime = endTime;
        ExecutionStatus = "Cancelled";
        SetUpdatedAt();
    }

    public bool IsCompleted()
    {
        return ExecutionStatus is "Success" or "Failed" or "Cancelled";
    }

    public bool IsSuccessful()
    {
        return ExecutionStatus == "Success";
    }

    public bool IsRunning()
    {
        return ExecutionStatus == "Running";
    }
}
