# 📊 Monitoring & Observability Service API Documentation

## 📋 Overview

The Monitoring & Observability Service provides comprehensive system monitoring, health checks, metrics collection, alerting, distributed tracing, and SLA monitoring. This service is 65% complete with core monitoring features production-ready and advanced observability capabilities in development.

**Base URL**: `/api/v1/monitoring` (via API Gateway)  
**Direct URL**: `http://localhost:5011`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation             | Permission                   | Roles                     |
| --------------------- | ---------------------------- | ------------------------- |
| View system health    | `monitoring.health.read`     | All authenticated users   |
| View detailed metrics | `monitoring.metrics.read`    | Admin, Monitor, Manager   |
| Manage alerts         | `monitoring.alerts.manage`   | Admin, Monitor            |
| Configure monitoring  | `monitoring.config.manage`   | Admin                     |
| View traces           | `monitoring.traces.read`     | Admin, Monitor, Developer |
| Manage SLAs           | `monitoring.sla.manage`      | Admin                     |
| Access dashboards     | `monitoring.dashboards.read` | Admin, Monitor, Manager   |

## 📊 Core Endpoints

### 1. System Health & Status

#### Get System Health Overview

```http
GET /api/v1/monitoring/health/overview
Authorization: Bearer <token>
```

**Response:**

```json
{
  "overallStatus": "Healthy",
  "lastUpdated": "2024-01-15T10:30:00Z",
  "systemMetrics": {
    "cpuUsage": 45.2,
    "memoryUsage": 68.5,
    "diskUsage": 32.1,
    "networkLatency": 12.5,
    "activeConnections": 1250,
    "requestsPerSecond": 850.3
  },
  "serviceMetrics": {
    "totalServices": 12,
    "healthyServices": 11,
    "degradedServices": 1,
    "unhealthyServices": 0,
    "healthyPercentage": 91.7
  },
  "activeAlerts": 3,
  "criticalAlerts": 0,
  "uptime": "15d 8h 32m",
  "uptimePercentage": 99.95,
  "lastIncident": "2024-01-10T14:20:00Z"
}
```

#### Get All Services Health Summary

```http
GET /api/v1/monitoring/health/services/summary
Authorization: Bearer <token>
```

**Query Parameters:**

- `includeMetrics`: Include detailed metrics (default: false)
- `includeAlerts`: Include active alerts (default: true)
- `includeIncidents`: Include recent incidents (default: true)

**Response:**

```json
{
  "services": [
    {
      "serviceName": "UserManagement",
      "status": "Healthy",
      "lastChecked": "2024-01-15T10:30:00Z",
      "uptime": "99.98%",
      "responseTime": 125.5,
      "errorRate": 0.02,
      "throughput": 450.2,
      "activeAlerts": 0,
      "recentIncidents": 0,
      "healthChecks": [
        {
          "name": "Database Connection",
          "status": "Healthy",
          "responseTime": 15.2,
          "lastChecked": "2024-01-15T10:30:00Z"
        },
        {
          "name": "External API",
          "status": "Healthy",
          "responseTime": 85.3,
          "lastChecked": "2024-01-15T10:30:00Z"
        }
      ]
    },
    {
      "serviceName": "OrderManagement",
      "status": "Degraded",
      "lastChecked": "2024-01-15T10:30:00Z",
      "uptime": "99.85%",
      "responseTime": 285.7,
      "errorRate": 0.15,
      "throughput": 320.8,
      "activeAlerts": 2,
      "recentIncidents": 1,
      "healthChecks": [
        {
          "name": "Database Connection",
          "status": "Healthy",
          "responseTime": 25.1,
          "lastChecked": "2024-01-15T10:30:00Z"
        },
        {
          "name": "Message Queue",
          "status": "Degraded",
          "responseTime": 450.2,
          "lastChecked": "2024-01-15T10:30:00Z",
          "error": "High queue depth detected"
        }
      ]
    }
  ],
  "summary": {
    "totalServices": 12,
    "healthyServices": 10,
    "degradedServices": 2,
    "unhealthyServices": 0,
    "overallHealthPercentage": 83.3
  },
  "generatedAt": "2024-01-15T10:30:00Z"
}
```

#### Get Service Health Details

```http
GET /api/v1/monitoring/health/services/{serviceName}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "serviceName": "UserManagement",
  "status": "Healthy",
  "lastChecked": "2024-01-15T10:30:00Z",
  "uptime": "99.98%",
  "uptimeHistory": [
    {
      "date": "2024-01-14",
      "uptime": 99.95
    },
    {
      "date": "2024-01-13",
      "uptime": 100.0
    }
  ],
  "performanceMetrics": {
    "averageResponseTime": 125.5,
    "p95ResponseTime": 245.2,
    "p99ResponseTime": 485.7,
    "errorRate": 0.02,
    "throughput": 450.2,
    "concurrentUsers": 1250
  },
  "healthChecks": [
    {
      "name": "Database Connection",
      "status": "Healthy",
      "responseTime": 15.2,
      "lastChecked": "2024-01-15T10:30:00Z",
      "details": {
        "connectionPool": "8/20 active",
        "queryTime": "12.5ms avg"
      }
    }
  ],
  "dependencies": [
    {
      "serviceName": "Identity",
      "status": "Healthy",
      "responseTime": 45.2
    },
    {
      "serviceName": "DataStorage",
      "status": "Healthy",
      "responseTime": 85.7
    }
  ],
  "activeAlerts": [],
  "recentIncidents": []
}
```

### 2. Metrics & Performance

#### Get Real-time Metrics

```http
GET /api/v1/monitoring/metrics/realtime
Authorization: Bearer <token>
```

**Query Parameters:**

- `serviceNames`: Comma-separated service names
- `metricTypes`: Comma-separated metric types
- `interval`: Update interval in seconds (default: 30)

**Response:**

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "systemMetrics": {
    "cpu": {
      "usage": 45.2,
      "cores": 8,
      "loadAverage": [1.2, 1.5, 1.8]
    },
    "memory": {
      "usage": 68.5,
      "total": 16777216000,
      "used": 11489280000,
      "available": **********
    },
    "disk": {
      "usage": 32.1,
      "total": 1073741824000,
      "used": 344827586000,
      "available": 728914238000,
      "iops": 1250.5
    },
    "network": {
      "bytesIn": 125000000,
      "bytesOut": 85000000,
      "packetsIn": 125000,
      "packetsOut": 95000,
      "latency": 12.5
    }
  },
  "serviceMetrics": [
    {
      "serviceName": "UserManagement",
      "metrics": {
        "requestsPerSecond": 450.2,
        "averageResponseTime": 125.5,
        "errorRate": 0.02,
        "activeConnections": 250,
        "memoryUsage": 512000000,
        "cpuUsage": 15.2
      }
    }
  ],
  "applicationMetrics": {
    "totalRequests": 1250000,
    "successfulRequests": 1247500,
    "failedRequests": 2500,
    "averageResponseTime": 185.7,
    "p95ResponseTime": 425.2,
    "p99ResponseTime": 850.5
  }
}
```

#### Get Historical Metrics

```http
GET /api/v1/monitoring/metrics/historical
Authorization: Bearer <token>
```

**Query Parameters:**

- `serviceName`: Service name
- `metricName`: Specific metric name
- `fromDate`: Start date
- `toDate`: End date
- `granularity`: Time granularity (`1m|5m|15m|1h|1d`)
- `aggregation`: Aggregation method (`avg|min|max|sum|count`)

**Response:**

```json
{
  "serviceName": "UserManagement",
  "metricName": "responseTime",
  "period": {
    "fromDate": "2024-01-14T00:00:00Z",
    "toDate": "2024-01-15T00:00:00Z",
    "granularity": "1h"
  },
  "dataPoints": [
    {
      "timestamp": "2024-01-14T00:00:00Z",
      "value": 125.5,
      "min": 85.2,
      "max": 245.7,
      "count": 1250
    },
    {
      "timestamp": "2024-01-14T01:00:00Z",
      "value": 135.2,
      "min": 92.1,
      "max": 285.3,
      "count": 1180
    }
  ],
  "statistics": {
    "average": 128.7,
    "minimum": 85.2,
    "maximum": 285.3,
    "standardDeviation": 25.4,
    "totalDataPoints": 24
  }
}
```

#### Create Custom Metric

```http
POST /api/v1/monitoring/metrics/custom
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "order_processing_time",
  "displayName": "Order Processing Time",
  "description": "Time taken to process an order from creation to completion",
  "unit": "milliseconds",
  "metricType": "Gauge",
  "serviceName": "OrderManagement",
  "tags": {
    "category": "business",
    "priority": "high"
  },
  "thresholds": {
    "warning": 5000,
    "critical": 10000
  },
  "aggregationMethods": ["avg", "p95", "p99", "max"]
}
```

### 3. Alerting System

#### Get Active Alerts

```http
GET /api/v1/monitoring/alerts/active
Authorization: Bearer <token>
```

**Query Parameters:**

- `severity`: Filter by severity (`Low|Medium|High|Critical`)
- `serviceName`: Filter by service
- `status`: Filter by status (`Open|Acknowledged|Resolved`)
- `page`: Page number
- `pageSize`: Items per page

**Response:**

```json
{
  "data": [
    {
      "id": "alert-uuid",
      "title": "High Response Time Detected",
      "description": "UserManagement service response time exceeded threshold",
      "severity": "High",
      "status": "Open",
      "source": "MetricThreshold",
      "serviceName": "UserManagement",
      "metricName": "responseTime",
      "currentValue": 850.5,
      "threshold": 500.0,
      "createdAt": "2024-01-15T10:25:00Z",
      "lastUpdated": "2024-01-15T10:30:00Z",
      "acknowledgedBy": null,
      "acknowledgedAt": null,
      "tags": {
        "environment": "production",
        "team": "backend"
      },
      "actions": [
        {
          "type": "Notification",
          "target": "<EMAIL>",
          "status": "Sent"
        },
        {
          "type": "Webhook",
          "target": "https://hooks.slack.com/...",
          "status": "Pending"
        }
      ]
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 15,
    "totalPages": 1
  },
  "summary": {
    "totalAlerts": 15,
    "bySeverity": {
      "Critical": 0,
      "High": 3,
      "Medium": 8,
      "Low": 4
    },
    "byStatus": {
      "Open": 10,
      "Acknowledged": 3,
      "Resolved": 2
    }
  }
}
```

#### Create Alert

```http
POST /api/v1/monitoring/alerts
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Custom Alert - Database Connection Issue",
  "description": "Database connection pool exhausted in UserManagement service",
  "severity": "Critical",
  "source": "Manual",
  "serviceName": "UserManagement",
  "metricName": "database_connections",
  "currentValue": 20,
  "threshold": 18,
  "tags": {
    "component": "database",
    "environment": "production"
  },
  "actions": [
    {
      "type": "Email",
      "target": "<EMAIL>"
    },
    {
      "type": "SMS",
      "target": "+1234567890"
    }
  ]
}
```

#### Acknowledge Alert

```http
POST /api/v1/monitoring/alerts/{alertId}/acknowledge
Authorization: Bearer <token>
Content-Type: application/json

{
  "acknowledgedBy": "<EMAIL>",
  "notes": "Investigating database connection issue. Scaling up connection pool.",
  "estimatedResolutionTime": "2024-01-15T11:00:00Z"
}
```

#### Resolve Alert

```http
POST /api/v1/monitoring/alerts/{alertId}/resolve
Authorization: Bearer <token>
Content-Type: application/json

{
  "resolvedBy": "<EMAIL>",
  "resolution": "Increased database connection pool size from 20 to 30. Monitoring for stability.",
  "rootCause": "Increased traffic during peak hours exceeded connection pool capacity",
  "preventiveMeasures": "Implement auto-scaling for connection pool based on load"
}
```

### 4. Distributed Tracing

#### Get Trace Details

```http
GET /api/v1/monitoring/traces/{traceId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "traceId": "trace-uuid",
  "rootSpan": {
    "spanId": "span-uuid",
    "operationName": "POST /api/orders",
    "serviceName": "OrderManagement",
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-01-15T10:30:02.500Z",
    "duration": 2500,
    "status": "Success",
    "tags": {
      "http.method": "POST",
      "http.url": "/api/orders",
      "http.status_code": "200",
      "user.id": "user-uuid"
    }
  },
  "spans": [
    {
      "spanId": "span-uuid-2",
      "parentSpanId": "span-uuid",
      "operationName": "UserManagement.ValidateUser",
      "serviceName": "UserManagement",
      "startTime": "2024-01-15T10:30:00.100Z",
      "endTime": "2024-01-15T10:30:00.250Z",
      "duration": 150,
      "status": "Success",
      "depth": 1,
      "tags": {
        "component": "http-client",
        "user.id": "user-uuid"
      }
    },
    {
      "spanId": "span-uuid-3",
      "parentSpanId": "span-uuid",
      "operationName": "Database.InsertOrder",
      "serviceName": "OrderManagement",
      "startTime": "2024-01-15T10:30:00.300Z",
      "endTime": "2024-01-15T10:30:02.000Z",
      "duration": 1700,
      "status": "Success",
      "depth": 1,
      "tags": {
        "db.type": "postgresql",
        "db.statement": "INSERT INTO orders...",
        "db.instance": "orders_db"
      }
    }
  ],
  "totalDuration": 2500,
  "totalSpans": 8,
  "serviceCount": 4,
  "errorCount": 0,
  "criticalPath": ["span-uuid", "span-uuid-3", "span-uuid-5"]
}
```

#### Search Traces

```http
GET /api/v1/monitoring/traces/search
Authorization: Bearer <token>
```

**Query Parameters:**

- `serviceName`: Filter by service
- `operationName`: Filter by operation
- `minDuration`: Minimum duration in milliseconds
- `maxDuration`: Maximum duration in milliseconds
- `status`: Filter by status (`Success|Error|Timeout`)
- `fromDate`: Start date
- `toDate`: End date
- `tags`: Filter by tags (key:value format)
- `limit`: Maximum results (default: 100)

**Response:**

```json
{
  "traces": [
    {
      "traceId": "trace-uuid",
      "rootOperationName": "POST /api/orders",
      "rootServiceName": "OrderManagement",
      "startTime": "2024-01-15T10:30:00Z",
      "duration": 2500,
      "spanCount": 8,
      "serviceCount": 4,
      "errorCount": 0,
      "status": "Success",
      "tags": {
        "user.id": "user-uuid",
        "order.type": "express"
      }
    }
  ],
  "totalResults": 1250,
  "searchTime": 45,
  "aggregations": {
    "averageDuration": 1850.5,
    "p95Duration": 4200.0,
    "p99Duration": 8500.0,
    "errorRate": 0.02,
    "serviceBreakdown": {
      "OrderManagement": 450,
      "UserManagement": 380,
      "PaymentService": 420
    }
  }
}
```

#### Get Service Dependencies

```http
GET /api/v1/monitoring/traces/dependencies
Authorization: Bearer <token>
```

**Query Parameters:**

- `serviceName`: Root service name
- `period`: Analysis period (`1h|6h|24h|7d`)
- `includeMetrics`: Include performance metrics

**Response:**

```json
{
  "serviceName": "OrderManagement",
  "period": "24h",
  "dependencies": [
    {
      "fromService": "OrderManagement",
      "toService": "UserManagement",
      "callCount": 15000,
      "averageLatency": 125.5,
      "errorRate": 0.01,
      "p95Latency": 245.2,
      "throughput": 625.0
    },
    {
      "fromService": "OrderManagement",
      "toService": "PaymentService",
      "callCount": 8500,
      "averageLatency": 285.7,
      "errorRate": 0.05,
      "p95Latency": 650.3,
      "throughput": 354.2
    }
  ],
  "criticalPath": ["OrderManagement", "PaymentService", "BankingAPI"],
  "bottlenecks": [
    {
      "service": "PaymentService",
      "operation": "ProcessPayment",
      "averageLatency": 1250.5,
      "impact": "High"
    }
  ]
}
```

### 5. SLA Monitoring

#### Get SLA Overview

```http
GET /api/v1/monitoring/sla/overview
Authorization: Bearer <token>
```

**Response:**

```json
{
  "totalSLAs": 15,
  "activeSLAs": 12,
  "slaCompliance": 96.8,
  "breachesThisMonth": 5,
  "criticalBreaches": 1,
  "slasByService": [
    {
      "serviceName": "OrderManagement",
      "slaCount": 4,
      "compliance": 98.5,
      "breaches": 1
    },
    {
      "serviceName": "UserManagement",
      "slaCount": 3,
      "compliance": 99.2,
      "breaches": 0
    }
  ],
  "recentBreaches": [
    {
      "id": "breach-uuid",
      "slaName": "Order Processing Time",
      "serviceName": "OrderManagement",
      "severity": "Medium",
      "detectedAt": "2024-01-15T09:15:00Z",
      "actualValue": 5200,
      "targetValue": 5000,
      "status": "Resolved"
    }
  ]
}
```

#### Create SLA

```http
POST /api/v1/monitoring/sla
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Order Processing SLA",
  "description": "Service level agreement for order processing performance",
  "serviceName": "OrderManagement",
  "isActive": true,
  "objectives": [
    {
      "name": "Response Time",
      "description": "Average response time for order creation",
      "metricName": "order_creation_time",
      "targetValue": 2000,
      "comparisonOperator": "LessThanOrEqual",
      "evaluationWindow": "00:05:00",
      "weight": 0.4
    },
    {
      "name": "Availability",
      "description": "Service availability percentage",
      "metricName": "availability",
      "targetValue": 99.9,
      "comparisonOperator": "GreaterThanOrEqual",
      "evaluationWindow": "01:00:00",
      "weight": 0.6
    }
  ],
  "notificationSettings": {
    "emailRecipients": ["<EMAIL>"],
    "webhookUrl": "https://hooks.slack.com/...",
    "notifyOnBreach": true,
    "notifyOnRecovery": true
  }
}
```

#### Get SLA Report

```http
GET /api/v1/monitoring/sla/{slaId}/report
Authorization: Bearer <token>
```

**Query Parameters:**

- `period`: Report period (`daily|weekly|monthly|quarterly`)
- `fromDate`: Start date
- `toDate`: End date

**Response:**

```json
{
  "slaId": "sla-uuid",
  "slaName": "Order Processing SLA",
  "reportPeriod": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z",
    "type": "monthly"
  },
  "overallCompliance": 98.5,
  "objectives": [
    {
      "name": "Response Time",
      "targetValue": 2000,
      "actualValue": 1850.5,
      "compliance": 99.2,
      "breachCount": 2,
      "breachDuration": "00:45:00",
      "trend": "improving"
    },
    {
      "name": "Availability",
      "targetValue": 99.9,
      "actualValue": 99.95,
      "compliance": 100.0,
      "breachCount": 0,
      "breachDuration": "00:00:00",
      "trend": "stable"
    }
  ],
  "breaches": [
    {
      "id": "breach-uuid",
      "objectiveName": "Response Time",
      "detectedAt": "2024-01-15T09:15:00Z",
      "resolvedAt": "2024-01-15T09:30:00Z",
      "duration": "00:15:00",
      "severity": "Medium",
      "actualValue": 2150,
      "targetValue": 2000
    }
  ],
  "trends": [
    {
      "date": "2024-01-01",
      "compliance": 98.2
    },
    {
      "date": "2024-01-02",
      "compliance": 98.8
    }
  ],
  "recommendations": [
    "Consider optimizing database queries to improve response time",
    "Implement caching for frequently accessed data"
  ]
}
```

### 6. Dashboards

#### Get Monitoring Dashboard

```http
GET /api/v1/monitoring/dashboards/overview
Authorization: Bearer <token>
```

**Response:**

```json
{
  "lastUpdated": "2024-01-15T10:30:00Z",
  "systemHealth": {
    "overallStatus": "Healthy",
    "healthyServices": 11,
    "totalServices": 12,
    "healthPercentage": 91.7
  },
  "keyMetrics": {
    "totalRequests": 1250000,
    "averageResponseTime": 185.7,
    "errorRate": 0.02,
    "throughput": 850.3,
    "activeUsers": 1250
  },
  "alerts": {
    "total": 15,
    "critical": 0,
    "high": 3,
    "medium": 8,
    "low": 4
  },
  "topServices": [
    {
      "serviceName": "OrderManagement",
      "requestCount": 450000,
      "averageResponseTime": 285.7,
      "errorRate": 0.05
    },
    {
      "serviceName": "UserManagement",
      "requestCount": 380000,
      "averageResponseTime": 125.5,
      "errorRate": 0.01
    }
  ],
  "recentEvents": [
    {
      "timestamp": "2024-01-15T10:25:00Z",
      "type": "Alert",
      "severity": "High",
      "message": "High response time detected in UserManagement",
      "serviceName": "UserManagement"
    }
  ]
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/monitoring/hub`

**Events:**

- `HealthStatusChanged` - Service health status changed
- `AlertTriggered` - New alert triggered
- `AlertResolved` - Alert resolved
- `MetricThresholdBreached` - Metric threshold exceeded
- `SLABreachDetected` - SLA breach detected
- `SystemAnomalyDetected` - System anomaly detected
- `ServiceDependencyChanged` - Service dependency status changed

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/monitoring/hub')
  .build()

// Listen for health status changes
connection.on('HealthStatusChanged', (data) => {
  console.log('Health status changed:', data)
  // Update health dashboard
})

// Listen for new alerts
connection.on('AlertTriggered', (alert) => {
  console.log('New alert:', alert)
  // Show alert notification
})

// Subscribe to specific service monitoring
await connection.invoke('SubscribeToService', 'UserManagement')
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                          | Description                                |
| ---- | -------------------------------- | ------------------------------------------ |
| 400  | `INVALID_METRIC_NAME`            | Invalid metric name specified              |
| 400  | `INVALID_TIME_RANGE`             | Invalid time range for query               |
| 400  | `INVALID_THRESHOLD_VALUE`        | Invalid threshold value                    |
| 401  | `UNAUTHORIZED`                   | Authentication required                    |
| 403  | `INSUFFICIENT_PERMISSIONS`       | Insufficient permissions for monitoring    |
| 404  | `SERVICE_NOT_FOUND`              | Service not found                          |
| 404  | `ALERT_NOT_FOUND`                | Alert not found                            |
| 404  | `TRACE_NOT_FOUND`                | Trace not found                            |
| 409  | `SLA_ALREADY_EXISTS`             | SLA with same name already exists          |
| 422  | `VALIDATION_FAILED`              | Input validation failed                    |
| 429  | `RATE_LIMIT_EXCEEDED`            | Too many requests                          |
| 503  | `MONITORING_SERVICE_UNAVAILABLE` | Monitoring service temporarily unavailable |

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_TIME_RANGE",
    "message": "Invalid time range for query",
    "details": "End time must be after start time",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```

## 📋 Integration Examples

### Frontend Integration Example (React)

```typescript
// Monitoring Service Client
class MonitoringClient {
  private apiClient = ApiClient.getInstance()

  async getSystemHealth(): Promise<SystemHealthDto> {
    const response = await this.apiClient.axios.get(
      '/monitoring/health/overview'
    )
    return response.data
  }

  async getActiveAlerts(): Promise<AlertsResponse> {
    const response = await this.apiClient.axios.get('/monitoring/alerts/active')
    return response.data
  }

  async acknowledgeAlert(alertId: string, notes: string): Promise<void> {
    await this.apiClient.axios.post(
      `/monitoring/alerts/${alertId}/acknowledge`,
      {
        acknowledgedBy: '<EMAIL>',
        notes,
      }
    )
  }

  async getServiceMetrics(
    serviceName: string,
    period: string
  ): Promise<MetricsData> {
    const response = await this.apiClient.axios.get(
      '/monitoring/metrics/historical',
      {
        params: { serviceName, period },
      }
    )
    return response.data
  }
}

// React Component Example
function MonitoringDashboard() {
  const [systemHealth, setSystemHealth] = useState<SystemHealthDto | null>(null)
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const monitoringClient = new MonitoringClient()

  // SignalR integration for real-time updates
  const { connection } = useSignalR({
    hubName: 'Monitoring',
    hubUrl: '/api/v1/monitoring/hub',
  })

  useEffect(() => {
    const loadData = async () => {
      try {
        const [healthData, alertsData] = await Promise.all([
          monitoringClient.getSystemHealth(),
          monitoringClient.getActiveAlerts(),
        ])

        setSystemHealth(healthData)
        setAlerts(alertsData.data)
      } catch (error) {
        console.error('Failed to load monitoring data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  useEffect(() => {
    if (connection) {
      // Listen for real-time health updates
      const unsubscribeHealth = connection.on('HealthStatusChanged', (data) => {
        setSystemHealth((prev) => (prev ? { ...prev, ...data } : null))
      })

      // Listen for new alerts
      const unsubscribeAlerts = connection.on('AlertTriggered', (alert) => {
        setAlerts((prev) => [alert, ...prev])
        // Show notification
        showNotification(
          `New ${alert.severity} alert: ${alert.title}`,
          alert.severity
        )
      })

      return () => {
        unsubscribeHealth()
        unsubscribeAlerts()
      }
    }
  }, [connection])

  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      await monitoringClient.acknowledgeAlert(
        alertId,
        'Acknowledged from dashboard'
      )
      setAlerts((prev) =>
        prev.map((alert) =>
          alert.id === alertId
            ? {
                ...alert,
                status: 'Acknowledged',
                acknowledgedAt: new Date().toISOString(),
              }
            : alert
        )
      )
    } catch (error) {
      console.error('Failed to acknowledge alert:', error)
    }
  }

  if (loading) return <div>Loading monitoring data...</div>

  return (
    <div className="monitoring-dashboard">
      <div className="system-health">
        <h2>System Health</h2>
        <div
          className={`status-indicator ${systemHealth?.overallStatus.toLowerCase()}`}
        >
          {systemHealth?.overallStatus}
        </div>
        <div className="health-metrics">
          <div>CPU: {systemHealth?.systemMetrics.cpuUsage}%</div>
          <div>Memory: {systemHealth?.systemMetrics.memoryUsage}%</div>
          <div>Uptime: {systemHealth?.uptime}</div>
        </div>
      </div>

      <div className="alerts-section">
        <h2>Active Alerts ({alerts.length})</h2>
        {alerts.map((alert) => (
          <div
            key={alert.id}
            className={`alert alert-${alert.severity.toLowerCase()}`}
          >
            <div className="alert-header">
              <span className="alert-title">{alert.title}</span>
              <span className="alert-severity">{alert.severity}</span>
            </div>
            <div className="alert-description">{alert.description}</div>
            <div className="alert-actions">
              {alert.status === 'Open' && (
                <button onClick={() => handleAcknowledgeAlert(alert.id)}>
                  Acknowledge
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Custom hook for real-time metrics
function useRealTimeMetrics(serviceName: string) {
  const [metrics, setMetrics] = useState<ServiceMetrics | null>(null)
  const { connection } = useSignalR({
    hubName: 'Monitoring',
    hubUrl: '/api/v1/monitoring/hub',
  })

  useEffect(() => {
    if (connection) {
      // Subscribe to service metrics
      connection.invoke('SubscribeToServiceMetrics', serviceName)

      const unsubscribe = connection.on('MetricsUpdated', (data) => {
        if (data.serviceName === serviceName) {
          setMetrics(data.metrics)
        }
      })

      return () => {
        unsubscribe()
        connection.invoke('UnsubscribeFromServiceMetrics', serviceName)
      }
    }
  }, [connection, serviceName])

  return metrics
}
```
