using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.ConfirmOrder;

public class ConfirmOrderCommandHandler : IRequestHandler<ConfirmOrderCommand, bool>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ConfirmOrderCommandHandler> _logger;

    public ConfirmOrderCommandHandler(
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<ConfirmOrderCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(ConfirmOrderCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Confirming order {OrderId} with carrier {CarrierId} by transport company {TransportCompanyId}", 
            request.OrderId, request.CarrierId, request.TransportCompanyId);

        var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
        
        if (order == null)
        {
            _logger.LogWarning("Order {OrderId} not found", request.OrderId);
            throw new OrderManagementException("Order not found");
        }

        // Authorization check - only the transport company that created the order can confirm it
        if (order.TransportCompanyId != request.TransportCompanyId)
        {
            _logger.LogWarning("Transport company {TransportCompanyId} is not authorized to confirm order {OrderId}", 
                request.TransportCompanyId, request.OrderId);
            throw new OrderManagementException("Not authorized to confirm this order");
        }

        // Business rule validation
        if (order.Status != OrderStatus.Created)
        {
            _logger.LogWarning("Cannot confirm order {OrderId} with status {Status}", 
                request.OrderId, order.Status);
            throw new OrderManagementException($"Cannot confirm order with status {order.Status}");
        }

        try
        {
            // Confirm the order
            order.Confirm(request.CarrierId);

            // Update repository
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("order.confirmed", new
            {
                OrderId = order.Id,
                OrderNumber = order.OrderNumber,
                TransportCompanyId = order.TransportCompanyId,
                BrokerId = order.BrokerId,
                CarrierId = request.CarrierId,
                ConfirmedAt = order.ConfirmedAt,
                Notes = request.Notes
            }, cancellationToken);

            _logger.LogInformation("Successfully confirmed order {OrderId} with carrier {CarrierId}",
                request.OrderId, request.CarrierId);

            return true;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Failed to confirm order {OrderId}: {Message}", 
                request.OrderId, ex.Message);
            throw new OrderManagementException(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming order {OrderId}", request.OrderId);
            throw;
        }
    }
}
