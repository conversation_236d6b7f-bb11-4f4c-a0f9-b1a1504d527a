using ContentManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ContentManagement.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for FaqItem entity
/// </summary>
public class FaqItemConfiguration : IEntityTypeConfiguration<FaqItem>
{
    public void Configure(EntityTypeBuilder<FaqItem> builder)
    {
        // Table configuration
        builder.ToTable("faq_items");

        // Inherit from Content configuration
        builder.HasBaseType<Content>();

        // FAQ-specific properties
        builder.Property(f => f.Question)
            .HasColumnName("question")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(f => f.Answer)
            .HasColumnName("answer")
            .IsRequired();

        builder.Property(f => f.Category)
            .HasColumnName("category")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(f => f.RoleVisibility)
            .HasColumnName("role_visibility")
            .HasMaxLength(1000);

        builder.Property(f => f.IsHighlighted)
            .HasColumnName("is_highlighted")
            .HasDefaultValue(false);

        builder.Property(f => f.ViewCount)
            .HasColumnName("view_count")
            .HasDefaultValue(0);

        builder.Property(f => f.HelpfulnessRating)
            .HasColumnName("helpfulness_rating")
            .HasColumnType("decimal(3,2)")
            .HasDefaultValue(0);

        builder.Property(f => f.TotalRatings)
            .HasColumnName("total_ratings")
            .HasDefaultValue(0);

        // Indexes
        builder.HasIndex(f => f.Category)
            .HasDatabaseName("ix_faq_items_category");

        builder.HasIndex(f => f.IsHighlighted)
            .HasDatabaseName("ix_faq_items_highlighted");

        builder.HasIndex(f => f.ViewCount)
            .HasDatabaseName("ix_faq_items_view_count");

        builder.HasIndex(f => f.HelpfulnessRating)
            .HasDatabaseName("ix_faq_items_rating");

        // Full-text search index on question and answer (PostgreSQL specific)
        builder.HasIndex(f => new { f.Question, f.Answer })
            .HasDatabaseName("ix_faq_items_search");
    }
}

/// <summary>
/// Entity Framework configuration for PolicyDocument entity
/// </summary>
public class PolicyDocumentConfiguration : IEntityTypeConfiguration<PolicyDocument>
{
    public void Configure(EntityTypeBuilder<PolicyDocument> builder)
    {
        // Table configuration
        builder.ToTable("policy_documents");

        // Inherit from Content configuration
        builder.HasBaseType<Content>();

        // Policy-specific properties
        builder.Property(p => p.PolicyType)
            .HasColumnName("policy_type")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.Version)
            .HasColumnName("version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.EffectiveDate)
            .HasColumnName("effective_date")
            .IsRequired();

        builder.Property(p => p.ExpiryDate)
            .HasColumnName("expiry_date");

        builder.Property(p => p.RequiresAcceptance)
            .HasColumnName("requires_acceptance")
            .HasDefaultValue(false);

        builder.Property(p => p.ApplicableRoles)
            .HasColumnName("applicable_roles")
            .HasMaxLength(1000);

        builder.Property(p => p.LegalReferences)
            .HasColumnName("legal_references")
            .HasMaxLength(2000);

        builder.Property(p => p.IsCurrentVersion)
            .HasColumnName("is_current_version")
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(p => p.PolicyType)
            .HasDatabaseName("ix_policy_documents_type");

        builder.HasIndex(p => new { p.PolicyType, p.Version })
            .IsUnique()
            .HasDatabaseName("ix_policy_documents_type_version");

        builder.HasIndex(p => p.EffectiveDate)
            .HasDatabaseName("ix_policy_documents_effective_date");

        builder.HasIndex(p => p.ExpiryDate)
            .HasDatabaseName("ix_policy_documents_expiry_date");

        builder.HasIndex(p => p.RequiresAcceptance)
            .HasDatabaseName("ix_policy_documents_requires_acceptance");

        builder.HasIndex(p => p.IsCurrentVersion)
            .HasDatabaseName("ix_policy_documents_current_version");

        // Relationships
        builder.HasMany(p => p.Acceptances)
            .WithOne()
            .HasForeignKey(a => a.PolicyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

/// <summary>
/// Entity Framework configuration for PolicyAcceptance entity
/// </summary>
public class PolicyAcceptanceConfiguration : IEntityTypeConfiguration<PolicyAcceptance>
{
    public void Configure(EntityTypeBuilder<PolicyAcceptance> builder)
    {
        // Table configuration
        builder.ToTable("policy_acceptances");

        // Primary key
        builder.HasKey(pa => pa.Id);

        // Properties
        builder.Property(pa => pa.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(pa => pa.PolicyId)
            .HasColumnName("policy_id")
            .IsRequired();

        builder.Property(pa => pa.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(pa => pa.UserName)
            .HasColumnName("user_name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(pa => pa.UserRole)
            .HasColumnName("user_role")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(pa => pa.Version)
            .HasColumnName("version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(pa => pa.AcceptedAt)
            .HasColumnName("accepted_at")
            .IsRequired();

        builder.Property(pa => pa.IpAddress)
            .HasColumnName("ip_address")
            .HasMaxLength(45); // IPv6 max length

        builder.Property(pa => pa.UserAgent)
            .HasColumnName("user_agent")
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(pa => pa.PolicyId)
            .HasDatabaseName("ix_policy_acceptances_policy_id");

        builder.HasIndex(pa => pa.UserId)
            .HasDatabaseName("ix_policy_acceptances_user_id");

        builder.HasIndex(pa => new { pa.PolicyId, pa.UserId })
            .IsUnique()
            .HasDatabaseName("ix_policy_acceptances_policy_user");

        builder.HasIndex(pa => pa.AcceptedAt)
            .HasDatabaseName("ix_policy_acceptances_accepted_at");

        builder.HasIndex(pa => pa.UserRole)
            .HasDatabaseName("ix_policy_acceptances_user_role");
    }
}
