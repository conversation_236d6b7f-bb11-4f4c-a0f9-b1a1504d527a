using Shared.Domain.Common;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for message broker operations
/// </summary>
public interface IMessageBroker
{
    /// <summary>
    /// Publish a message to a topic
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="message">Message to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Publish a message to a topic with routing key
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="routingKey">Routing key</param>
    /// <param name="message">Message to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task PublishAsync<T>(string topic, string routingKey, T message, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Subscribe to a topic
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="handler">Message handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task SubscribeAsync<T>(string topic, Func<T, Task> handler, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Subscribe to a topic with routing key
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="routingKey">Routing key</param>
    /// <param name="handler">Message handler</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task SubscribeAsync<T>(string topic, string routingKey, Func<T, Task> handler, CancellationToken cancellationToken = default) where T : class;
}

/// <summary>
/// Interface for unit of work pattern
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Notification repository
    /// </summary>
    INotificationRepository Notifications { get; }

    /// <summary>
    /// Message repository
    /// </summary>
    IMessageRepository Messages { get; }

    /// <summary>
    /// User preference repository
    /// </summary>
    IUserPreferenceRepository UserPreferences { get; }

    /// <summary>
    /// Conversation repository
    /// </summary>
    IConversationRepository Conversations { get; }

    /// <summary>
    /// Save all changes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected records</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Begin transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction</returns>
    Task<ITransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for database transaction
/// </summary>
public interface ITransaction : IDisposable
{
    /// <summary>
    /// Commit transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for notification provider factory
/// </summary>
public interface INotificationProviderFactory
{
    /// <summary>
    /// Get notification provider for channel
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <returns>Notification provider</returns>
    INotificationProvider GetProvider(Domain.Enums.NotificationChannel channel);

    /// <summary>
    /// Get all available providers
    /// </summary>
    /// <returns>List of available providers</returns>
    List<INotificationProvider> GetAllProviders();

    /// <summary>
    /// Check if provider is available for channel
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <returns>True if provider is available</returns>
    bool IsProviderAvailable(Domain.Enums.NotificationChannel channel);
}

/// <summary>
/// Interface for real-time communication hub
/// </summary>
public interface ICommunicationHub
{
    /// <summary>
    /// Send message to user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="message">Message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task SendToUserAsync(Guid userId, object message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send message to group
    /// </summary>
    /// <param name="groupName">Group name</param>
    /// <param name="message">Message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task SendToGroupAsync(string groupName, object message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add user to group
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="groupName">Group name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddToGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove user from group
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="groupName">Group name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task RemoveFromGroupAsync(Guid userId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get online users
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of online user IDs</returns>
    Task<List<Guid>> GetOnlineUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user is online
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user is online</returns>
    Task<bool> IsUserOnlineAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for audit service
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Log communication event
    /// </summary>
    /// <param name="auditEvent">Audit event</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task LogEventAsync(CommunicationAuditEvent auditEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get audit trail for entity
    /// </summary>
    /// <param name="entityId">Entity ID</param>
    /// <param name="entityType">Entity type</param>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of audit events</returns>
    Task<List<CommunicationAuditEvent>> GetAuditTrailAsync(
        Guid entityId,
        string entityType,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get audit trail for user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of audit events</returns>
    Task<List<CommunicationAuditEvent>> GetUserAuditTrailAsync(
        Guid userId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Communication audit event
/// </summary>
public class CommunicationAuditEvent
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

