using CommunicationNotification.Application.Queries.Messages;
using CommunicationNotification.Application.Commands.Messages;
using CommunicationNotification.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Message management and history API controller
/// </summary>
[ApiController]
[Route("api/messages")]
[Authorize]
[EnableRateLimiting("MessagePolicy")]
public class MessageController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MessageController> _logger;

    public MessageController(IMediator mediator, ILogger<MessageController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get message history for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size (max 100)</param>
    /// <param name="channel">Filter by channel</param>
    /// <param name="messageType">Filter by message type</param>
    /// <param name="fromDate">Filter from date</param>
    /// <param name="toDate">Filter to date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated message history</returns>
    [HttpGet("user/{userId}/history")]
    [ProducesResponseType(typeof(MessageHistoryResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetUserMessageHistory(
        [FromRoute] Guid userId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate access permissions
            var currentUserId = GetCurrentUserId();
            if (userId != currentUserId && !User.IsInRole("Admin") && !User.IsInRole("Support"))
            {
                return Forbid("You can only access your own message history");
            }

            // Validate pagination parameters
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            var query = new GetUserMessageHistoryQuery
            {
                UserId = userId,
                PageNumber = pageNumber,
                PageSize = pageSize,
                Channel = channel,
                MessageType = messageType,
                FromDate = fromDate,
                ToDate = toDate,
                IncludeDeliveryDetails = true,
                RequestedBy = currentUserId
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new MessageHistoryResponse
                {
                    Messages = result.Data.Messages.Select(m => new MessageHistoryItem
                    {
                        MessageId = m.MessageId,
                        Content = m.Content,
                        Subject = m.Subject,
                        MessageType = m.MessageType.ToString(),
                        Channel = m.Channel.ToString(),
                        Status = m.Status.ToString(),
                        Priority = m.Priority.ToString(),
                        Language = m.Language.ToString(),
                        SentAt = m.SentAt,
                        DeliveredAt = m.DeliveredAt,
                        ReadAt = m.ReadAt,
                        ExternalId = m.ExternalId,
                        Tags = m.Tags,
                        TemplateId = m.TemplateId,
                        ErrorMessage = m.ErrorMessage
                    }).ToList(),
                    TotalCount = result.Data.TotalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)result.Data.TotalCount / pageSize),
                    HasNextPage = pageNumber * pageSize < result.Data.TotalCount,
                    HasPreviousPage = pageNumber > 1
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message history for user {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Get detailed message information
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed message information</returns>
    [HttpGet("{messageId}")]
    [ProducesResponseType(typeof(MessageDetailResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetMessage(
        [FromRoute] Guid messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetMessageDetailQuery
            {
                MessageId = messageId,
                IncludeDeliveryAttempts = true,
                IncludeAuditTrail = true,
                RequestedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new MessageDetailResponse
                {
                    MessageId = result.Data.MessageId,
                    UserId = result.Data.UserId,
                    Content = result.Data.Content,
                    Subject = result.Data.Subject,
                    MessageType = result.Data.MessageType.ToString(),
                    Channel = result.Data.Channel.ToString(),
                    Status = result.Data.Status.ToString(),
                    Priority = result.Data.Priority.ToString(),
                    Language = result.Data.Language.ToString(),
                    SentAt = result.Data.SentAt,
                    DeliveredAt = result.Data.DeliveredAt,
                    ReadAt = result.Data.ReadAt,
                    ExternalId = result.Data.ExternalId,
                    Tags = result.Data.Tags,
                    TemplateId = result.Data.TemplateId,
                    TemplateParameters = result.Data.TemplateParameters,
                    ErrorMessage = result.Data.ErrorMessage,
                    CreatedBy = result.Data.CreatedBy,
                    CreatedAt = result.Data.CreatedAt,
                    DeliveryAttempts = result.Data.DeliveryAttempts.Select(a => new DeliveryAttemptDetail
                    {
                        AttemptNumber = a.AttemptNumber,
                        Channel = a.Channel.ToString(),
                        AttemptedAt = a.AttemptedAt,
                        IsSuccess = a.IsSuccess,
                        ErrorMessage = a.ErrorMessage,
                        ResponseTime = a.ResponseTime,
                        ExternalId = a.ExternalId,
                        ProviderResponse = a.ProviderResponse
                    }).ToList(),
                    AuditTrail = result.Data.AuditTrail.Select(a => new AuditTrailItem
                    {
                        Action = a.Action,
                        Timestamp = a.Timestamp,
                        UserId = a.UserId,
                        Details = a.Details,
                        IpAddress = a.IpAddress,
                        UserAgent = a.UserAgent
                    }).ToList()
                };

                return Ok(response);
            }

            return NotFound(new { Message = $"Message {messageId} not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message details for {MessageId}", messageId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Mark a message as read
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    [HttpPost("{messageId}/mark-read")]
    [ProducesResponseType(typeof(MessageActionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> MarkMessageAsRead(
        [FromRoute] Guid messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new MarkMessageAsReadCommand
            {
                MessageId = messageId,
                ReadBy = GetCurrentUserId(),
                ReadAt = DateTime.UtcNow,
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new MessageActionResponse
                {
                    MessageId = messageId,
                    Action = "MarkAsRead",
                    Success = true,
                    Timestamp = DateTime.UtcNow,
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking message {MessageId} as read", messageId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Archive a message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success result</returns>
    [HttpPost("{messageId}/archive")]
    [ProducesResponseType(typeof(MessageActionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> ArchiveMessage(
        [FromRoute] Guid messageId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new ArchiveMessageCommand
            {
                MessageId = messageId,
                ArchivedBy = GetCurrentUserId(),
                ArchivedAt = DateTime.UtcNow,
                Reason = "User archived"
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result.IsSuccess)
            {
                var response = new MessageActionResponse
                {
                    MessageId = messageId,
                    Action = "Archive",
                    Success = true,
                    Timestamp = DateTime.UtcNow,
                    CommandId = result.CommandId
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving message {MessageId}", messageId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Get message statistics for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message statistics</returns>
    [HttpGet("user/{userId}/statistics")]
    [ProducesResponseType(typeof(MessageStatisticsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetUserMessageStatistics(
        [FromRoute] Guid userId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate access permissions
            var currentUserId = GetCurrentUserId();
            if (userId != currentUserId && !User.IsInRole("Admin") && !User.IsInRole("Support"))
            {
                return Forbid("You can only access your own message statistics");
            }

            var query = new GetUserMessageStatisticsQuery
            {
                UserId = userId,
                FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
                ToDate = toDate ?? DateTime.UtcNow,
                RequestedBy = currentUserId
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new MessageStatisticsResponse
                {
                    UserId = userId,
                    FromDate = query.FromDate,
                    ToDate = query.ToDate,
                    TotalMessages = result.Data.TotalMessages,
                    DeliveredMessages = result.Data.DeliveredMessages,
                    ReadMessages = result.Data.ReadMessages,
                    FailedMessages = result.Data.FailedMessages,
                    DeliveryRate = result.Data.DeliveryRate,
                    ReadRate = result.Data.ReadRate,
                    ChannelBreakdown = result.Data.ChannelBreakdown.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => kvp.Value),
                    MessageTypeBreakdown = result.Data.MessageTypeBreakdown.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => kvp.Value),
                    DailyStats = result.Data.DailyStats.Select(d => new DailyMessageStats
                    {
                        Date = d.Date,
                        TotalMessages = d.TotalMessages,
                        DeliveredMessages = d.DeliveredMessages,
                        ReadMessages = d.ReadMessages,
                        FailedMessages = d.FailedMessages
                    }).ToList()
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message statistics for user {UserId}", userId);
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    /// <summary>
    /// Search messages with advanced filters
    /// </summary>
    /// <param name="request">Search request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search results</returns>
    [HttpPost("search")]
    [Authorize(Roles = "Admin,Support,Analyst")]
    [ProducesResponseType(typeof(MessageSearchResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SearchMessages(
        [FromBody] MessageSearchRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new SearchMessagesQuery
            {
                SearchTerm = request.SearchTerm,
                UserIds = request.UserIds,
                Channels = request.Channels,
                MessageTypes = request.MessageTypes,
                Statuses = request.Statuses,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                Tags = request.Tags,
                PageNumber = request.PageNumber,
                PageSize = Math.Min(request.PageSize, 100),
                SortBy = request.SortBy,
                SortDirection = request.SortDirection,
                RequestedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(query, cancellationToken);

            if (result.IsSuccess && result.Data != null)
            {
                var response = new MessageSearchResponse
                {
                    Messages = result.Data.Messages.Select(m => new MessageSearchItem
                    {
                        MessageId = m.MessageId,
                        UserId = m.UserId,
                        Content = m.Content,
                        Subject = m.Subject,
                        MessageType = m.MessageType.ToString(),
                        Channel = m.Channel.ToString(),
                        Status = m.Status.ToString(),
                        SentAt = m.SentAt,
                        DeliveredAt = m.DeliveredAt,
                        Tags = m.Tags,
                        TemplateId = m.TemplateId
                    }).ToList(),
                    TotalCount = result.Data.TotalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = (int)Math.Ceiling((double)result.Data.TotalCount / request.PageSize),
                    SearchTerm = request.SearchTerm,
                    ExecutionTime = result.Data.ExecutionTime
                };

                return Ok(response);
            }

            return BadRequest(new { Error = result.ErrorMessage });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching messages");
            return StatusCode(500, new { Error = "Internal server error", TraceId = HttpContext.TraceIdentifier });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }

    private string GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}
