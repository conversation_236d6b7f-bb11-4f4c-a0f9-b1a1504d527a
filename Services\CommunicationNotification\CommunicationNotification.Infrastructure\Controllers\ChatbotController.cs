using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Chatbot API controller for AI-powered conversational interfaces
/// </summary>
[ApiController]
[Route("api/communication/chatbot")]
[Authorize]
public class ChatbotController : ControllerBase
{
    private readonly IChatbotService _chatbotService;
    private readonly INLPService _nlpService;

    public ChatbotController(
        IChatbotService chatbotService,
        INLPService nlpService)
    {
        _chatbotService = chatbotService;
        _nlpService = nlpService;
    }

    /// <summary>
    /// Create a new chatbot
    /// </summary>
    [HttpPost("bots")]
    public async Task<IActionResult> CreateChatbot([FromBody] CreateChatbotRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized("User ID not found");
        }

        var chatbot = await _chatbotService.CreateChatbotAsync(
            request.Name,
            request.Description,
            request.Type,
            request.DefaultLanguage,
            request.WelcomeMessage,
            userId);

        return Ok(chatbot);
    }

    /// <summary>
    /// Get chatbot by ID
    /// </summary>
    [HttpGet("bots/{chatbotId}")]
    public async Task<IActionResult> GetChatbot(Guid chatbotId)
    {
        var chatbot = await _chatbotService.GetChatbotAsync(chatbotId);
        if (chatbot == null)
        {
            return NotFound($"Chatbot {chatbotId} not found");
        }

        return Ok(chatbot);
    }

    /// <summary>
    /// Get all chatbots
    /// </summary>
    [HttpGet("bots")]
    public async Task<IActionResult> GetAllChatbots()
    {
        var chatbots = await _chatbotService.GetAllChatbotsAsync();
        return Ok(chatbots);
    }

    /// <summary>
    /// Update chatbot
    /// </summary>
    [HttpPut("bots/{chatbotId}")]
    public async Task<IActionResult> UpdateChatbot(Guid chatbotId, [FromBody] Chatbot updatedChatbot)
    {
        var chatbot = await _chatbotService.UpdateChatbotAsync(chatbotId, updatedChatbot);
        return Ok(chatbot);
    }

    /// <summary>
    /// Delete chatbot
    /// </summary>
    [HttpDelete("bots/{chatbotId}")]
    public async Task<IActionResult> DeleteChatbot(Guid chatbotId)
    {
        await _chatbotService.DeleteChatbotAsync(chatbotId);
        return NoContent();
    }

    /// <summary>
    /// Train chatbot with current intents and entities
    /// </summary>
    [HttpPost("bots/{chatbotId}/train")]
    public async Task<IActionResult> TrainChatbot(Guid chatbotId)
    {
        var result = await _chatbotService.TrainChatbotAsync(chatbotId);
        return Ok(new { success = result, message = result ? "Training completed successfully" : "Training failed" });
    }

    /// <summary>
    /// Activate chatbot
    /// </summary>
    [HttpPost("bots/{chatbotId}/activate")]
    public async Task<IActionResult> ActivateChatbot(Guid chatbotId)
    {
        var chatbot = await _chatbotService.ActivateChatbotAsync(chatbotId);
        return Ok(chatbot);
    }

    /// <summary>
    /// Deactivate chatbot
    /// </summary>
    [HttpPost("bots/{chatbotId}/deactivate")]
    public async Task<IActionResult> DeactivateChatbot(Guid chatbotId)
    {
        var chatbot = await _chatbotService.DeactivateChatbotAsync(chatbotId);
        return Ok(chatbot);
    }

    /// <summary>
    /// Get chatbot analytics
    /// </summary>
    [HttpGet("bots/{chatbotId}/analytics")]
    public async Task<IActionResult> GetChatbotAnalytics(
        Guid chatbotId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var analytics = await _chatbotService.GetChatbotAnalyticsAsync(chatbotId, startDate, endDate);
        return Ok(analytics);
    }

    /// <summary>
    /// Start a new conversation with chatbot
    /// </summary>
    [HttpPost("bots/{chatbotId}/conversations")]
    public async Task<IActionResult> StartConversation(Guid chatbotId, [FromBody] StartConversationRequest request)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized("User ID not found");
        }

        var userName = GetCurrentUserName();
        var conversation = await _chatbotService.StartConversationAsync(
            chatbotId, userId, userName, request.Channel, request.Language);

        return Ok(conversation);
    }

    /// <summary>
    /// Send message to chatbot and get response
    /// </summary>
    [HttpPost("conversations/{sessionId}/messages")]
    public async Task<IActionResult> SendMessage(Guid sessionId, [FromBody] SendMessageRequest request)
    {
        var result = await _chatbotService.ProcessMessageAsync(sessionId, request.Message);

        if (result.IsSuccess)
        {
            if (result.ShouldEndConversation)
            {
                await _chatbotService.EndConversationAsync(sessionId, ConversationStatus.Completed);
            }
            else if (result.ShouldTransferToAgent)
            {
                await _chatbotService.EndConversationAsync(sessionId, ConversationStatus.TransferredToAgent);
            }

            return Ok(result);
        }
        else
        {
            return BadRequest(result.ErrorMessage);
        }
    }

    /// <summary>
    /// End conversation
    /// </summary>
    [HttpPost("conversations/{sessionId}/end")]
    public async Task<IActionResult> EndConversation(Guid sessionId, [FromBody] EndConversationRequest request)
    {
        await _chatbotService.EndConversationAsync(sessionId, request.Status, request.SatisfactionScore);
        return Ok();
    }

    /// <summary>
    /// Get conversation details
    /// </summary>
    [HttpGet("conversations/{sessionId}")]
    public async Task<IActionResult> GetConversation(Guid sessionId)
    {
        var conversation = await _chatbotService.GetConversationAsync(sessionId);
        if (conversation == null)
        {
            return NotFound($"Conversation {sessionId} not found");
        }

        return Ok(conversation);
    }

    /// <summary>
    /// Get active conversations for chatbot
    /// </summary>
    [HttpGet("bots/{chatbotId}/conversations/active")]
    public async Task<IActionResult> GetActiveConversations(Guid chatbotId)
    {
        var conversations = await _chatbotService.GetActiveConversationsAsync(chatbotId);
        return Ok(conversations);
    }

    /// <summary>
    /// Test NLP processing for text
    /// </summary>
    [HttpPost("nlp/test")]
    public async Task<IActionResult> TestNLP([FromBody] TestNLPRequest request)
    {
        var result = await _nlpService.ProcessTextAsync(request.Text, request.Language);
        return Ok(result);
    }

    /// <summary>
    /// Get supported languages for NLP
    /// </summary>
    [HttpGet("nlp/languages")]
    public async Task<IActionResult> GetSupportedLanguages()
    {
        var languages = await _nlpService.GetSupportedLanguagesAsync();
        return Ok(languages);
    }

    /// <summary>
    /// Analyze sentiment of text
    /// </summary>
    [HttpPost("nlp/sentiment")]
    public async Task<IActionResult> AnalyzeSentiment([FromBody] AnalyzeSentimentRequest request)
    {
        var (sentiment, score) = await _nlpService.AnalyzeSentimentAsync(request.Text, request.Language);
        return Ok(new { sentiment, score });
    }

    /// <summary>
    /// Detect language of text
    /// </summary>
    [HttpPost("nlp/detect-language")]
    public async Task<IActionResult> DetectLanguage([FromBody] DetectLanguageRequest request)
    {
        var language = await _nlpService.DetectLanguageAsync(request.Text);
        return Ok(new { language });
    }

    /// <summary>
    /// Generate AI response for text
    /// </summary>
    [HttpPost("nlp/generate-response")]
    public async Task<IActionResult> GenerateResponse([FromBody] GenerateResponseRequest request)
    {
        var response = await _nlpService.GenerateResponseAsync(request.UserMessage, request.Context, request.Language);
        return Ok(new { response });
    }

    /// <summary>
    /// Webhook endpoint for external chat platforms (WhatsApp, Telegram, etc.)
    /// </summary>
    [HttpPost("webhook/{chatbotId}")]
    [AllowAnonymous]
    public async Task<IActionResult> HandleWebhook(Guid chatbotId, [FromBody] ChatWebhookRequest request)
    {
        try
        {
            // Find or create conversation
            var conversation = await FindOrCreateConversationAsync(chatbotId, request);

            // Process the message
            var result = await _chatbotService.ProcessMessageAsync(conversation.SessionId, request.Message);

            if (result.IsSuccess)
            {
                // Return response in the format expected by the chat platform
                return Ok(new
                {
                    message = result.BotResponse,
                    sessionId = conversation.SessionId,
                    shouldEnd = result.ShouldEndConversation,
                    shouldTransfer = result.ShouldTransferToAgent
                });
            }
            else
            {
                return Ok(new { message = "I'm sorry, I'm having trouble understanding. Could you please try again?" });
            }
        }
        catch (Exception ex)
        {
            return Ok(new { message = "I'm sorry, there was an error processing your message. Please try again later." });
        }
    }

    private async Task<ChatbotConversation> FindOrCreateConversationAsync(Guid chatbotId, ChatWebhookRequest request)
    {
        // In a real implementation, you would look up existing conversations by user ID
        // For now, create a new conversation
        var userId = Guid.NewGuid(); // This would come from the webhook request
        var userName = request.UserName ?? "User";
        var channel = DetermineChannelFromWebhook(request);

        return await _chatbotService.StartConversationAsync(chatbotId, userId, userName, channel, request.Language ?? "en-US");
    }

    private NotificationChannel DetermineChannelFromWebhook(ChatWebhookRequest request)
    {
        return request.Platform?.ToLower() switch
        {
            "whatsapp" => NotificationChannel.WhatsApp,
            "telegram" => NotificationChannel.SMS, // Using SMS as fallback
            "facebook" => NotificationChannel.SMS, // Using SMS as fallback
            _ => NotificationChannel.RealTimeChat
        };
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetCurrentUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "User";
    }
}

// Request/Response DTOs
public class CreateChatbotRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ChatbotType Type { get; set; }
    public string DefaultLanguage { get; set; } = "en-US";
    public string WelcomeMessage { get; set; } = string.Empty;
}

public class StartConversationRequest
{
    public NotificationChannel Channel { get; set; }
    public string Language { get; set; } = "en-US";
}

public class SendMessageRequest
{
    public string Message { get; set; } = string.Empty;
}

public class EndConversationRequest
{
    public ConversationStatus Status { get; set; }
    public decimal? SatisfactionScore { get; set; }
}

public class TestNLPRequest
{
    public string Text { get; set; } = string.Empty;
    public string Language { get; set; } = "en-US";
}

public class AnalyzeSentimentRequest
{
    public string Text { get; set; } = string.Empty;
    public string Language { get; set; } = "en-US";
}

public class DetectLanguageRequest
{
    public string Text { get; set; } = string.Empty;
}

public class GenerateResponseRequest
{
    public string UserMessage { get; set; } = string.Empty;
    public string Context { get; set; } = string.Empty;
    public string Language { get; set; } = "en-US";
}

public class ChatWebhookRequest
{
    public string Message { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public string? UserId { get; set; }
    public string? Platform { get; set; }
    public string? Language { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}
