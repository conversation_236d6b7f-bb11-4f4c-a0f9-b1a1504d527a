using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Integration;

public interface IServiceIntegrationHub
{
    Task<TripManagementIntegration> GetTripManagementIntegrationAsync();
    Task<OrderManagementIntegration> GetOrderManagementIntegrationAsync();
    Task<NetworkFleetIntegration> GetNetworkFleetIntegrationAsync();
    Task<CommunicationIntegration> GetCommunicationIntegrationAsync();
    Task<FinancialPaymentIntegration> GetFinancialPaymentIntegrationAsync();
    Task<UserManagementIntegration> GetUserManagementIntegrationAsync();
    Task<SubscriptionIntegration> GetSubscriptionIntegrationAsync();
}

public class ServiceIntegrationHub : IServiceIntegrationHub
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ServiceIntegrationHub> _logger;

    public ServiceIntegrationHub(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<ServiceIntegrationHub> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<TripManagementIntegration> GetTripManagementIntegrationAsync()
    {
        return new TripManagementIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<OrderManagementIntegration> GetOrderManagementIntegrationAsync()
    {
        return new OrderManagementIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<NetworkFleetIntegration> GetNetworkFleetIntegrationAsync()
    {
        return new NetworkFleetIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<CommunicationIntegration> GetCommunicationIntegrationAsync()
    {
        return new CommunicationIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<FinancialPaymentIntegration> GetFinancialPaymentIntegrationAsync()
    {
        return new FinancialPaymentIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<UserManagementIntegration> GetUserManagementIntegrationAsync()
    {
        return new UserManagementIntegration(_httpClient, _configuration, _logger);
    }

    public async Task<SubscriptionIntegration> GetSubscriptionIntegrationAsync()
    {
        return new SubscriptionIntegration(_httpClient, _configuration, _logger);
    }
}

public class TripManagementIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public TripManagementIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:TripManagement:BaseUrl"] ?? "http://localhost:5003";
    }

    public async Task<List<TripAssignmentInfo>> GetPendingTripsForDriverAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/trips/driver/{driverId}/pending", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<List<TripAssignmentInfo>>(content) ?? new List<TripAssignmentInfo>();
            }

            _logger.LogWarning("Failed to get pending trips for driver {DriverId}. Status: {StatusCode}", driverId, response.StatusCode);
            return new List<TripAssignmentInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending trips for driver {DriverId}", driverId);
            return new List<TripAssignmentInfo>();
        }
    }

    public async Task<bool> UpdateTripStatusAsync(Guid tripId, string status, Dictionary<string, object> statusData, CancellationToken cancellationToken = default)
    {
        try
        {
            var updateRequest = new
            {
                Status = status,
                StatusData = statusData,
                UpdatedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(updateRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync($"{_baseUrl}/api/trips/{tripId}/status", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating trip status for trip {TripId}", tripId);
            return false;
        }
    }

    public async Task<bool> UploadPODAsync(Guid tripId, PODUploadData podData, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(podData);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/trips/{tripId}/pod", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading POD for trip {TripId}", tripId);
            return false;
        }
    }
}

public class OrderManagementIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public OrderManagementIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:OrderManagement:BaseUrl"] ?? "http://localhost:5004";
    }

    public async Task<bool> AcceptRFQBidAsync(Guid rfqId, Guid carrierId, decimal bidAmount, CancellationToken cancellationToken = default)
    {
        try
        {
            var bidRequest = new
            {
                CarrierId = carrierId,
                BidAmount = bidAmount,
                AcceptedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(bidRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/rfqs/{rfqId}/accept-bid", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting RFQ bid for RFQ {RfqId}", rfqId);
            return false;
        }
    }

    public async Task<List<RFQInfo>> GetAvailableRFQsForCarrierAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/rfqs/available/{carrierId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<List<RFQInfo>>(content) ?? new List<RFQInfo>();
            }

            return new List<RFQInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available RFQs for carrier {CarrierId}", carrierId);
            return new List<RFQInfo>();
        }
    }
}

public class NetworkFleetIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public NetworkFleetIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:NetworkFleetManagement:BaseUrl"] ?? "http://localhost:5005";
    }

    public async Task<DriverInfo?> GetDriverInfoAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/drivers/{driverId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<DriverInfo>(content);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver info for driver {DriverId}", driverId);
            return null;
        }
    }

    public async Task<bool> UpdateDriverLocationAsync(Guid driverId, double latitude, double longitude, CancellationToken cancellationToken = default)
    {
        try
        {
            var locationUpdate = new
            {
                Latitude = latitude,
                Longitude = longitude,
                Timestamp = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(locationUpdate);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync($"{_baseUrl}/api/drivers/{driverId}/location", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating driver location for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<bool> UploadDriverDocumentAsync(Guid driverId, DocumentUploadInfo document, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(document);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/drivers/{driverId}/documents", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for driver {DriverId}", driverId);
            return false;
        }
    }
}

public class CommunicationIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public CommunicationIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:CommunicationNotification:BaseUrl"] ?? "http://localhost:5006";
    }

    public async Task<bool> SendNotificationAsync(NotificationRequest notification, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(notification);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications/send", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification");
            return false;
        }
    }

    public async Task<bool> SendDriverAlertAsync(Guid driverId, string alertType, string message, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = new
            {
                DriverId = driverId,
                AlertType = alertType,
                Message = message,
                Timestamp = DateTime.UtcNow,
                Priority = "High"
            };

            var json = JsonSerializer.Serialize(alert);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/driver-communication/alert", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending driver alert to driver {DriverId}", driverId);
            return false;
        }
    }
}

public class FinancialPaymentIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public FinancialPaymentIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:FinancialPayment:BaseUrl"] ?? "http://localhost:5007";
    }

    public async Task<PaymentInfo?> GetDriverEarningsAsync(Guid driverId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/payments/driver/{driverId}/earnings?startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<PaymentInfo>(content);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver earnings for driver {DriverId}", driverId);
            return null;
        }
    }

    public async Task<bool> ProcessTripPaymentAsync(Guid tripId, decimal amount, Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            var paymentRequest = new
            {
                TripId = tripId,
                Amount = amount,
                DriverId = driverId,
                PaymentType = "TripCompletion",
                ProcessedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(paymentRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/payments/process-trip-payment", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip payment for trip {TripId}", tripId);
            return false;
        }
    }
}

public class UserManagementIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public UserManagementIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:UserManagement:BaseUrl"] ?? "http://localhost:5002";
    }

    public async Task<UserInfo?> GetUserInfoAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/users/{userId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<UserInfo>(content);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user info for user {UserId}", userId);
            return null;
        }
    }

    public async Task<bool> UpdateUserPreferencesAsync(Guid userId, Dictionary<string, object> preferences, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(preferences);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await _httpClient.PutAsync($"{_baseUrl}/api/users/{userId}/preferences", content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user preferences for user {UserId}", userId);
            return false;
        }
    }
}

public class SubscriptionIntegration
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string _baseUrl;

    public SubscriptionIntegration(HttpClient httpClient, IConfiguration configuration, ILogger logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = _configuration["Services:SubscriptionManagement:BaseUrl"] ?? "http://localhost:5008";
    }

    public async Task<SubscriptionInfo?> GetUserSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/subscriptions/user/{userId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<SubscriptionInfo>(content);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription info for user {UserId}", userId);
            return null;
        }
    }

    public async Task<List<FeatureInfo>> GetAvailableFeaturesAsync(string subscriptionTier, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/features/tier/{subscriptionTier}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return JsonSerializer.Deserialize<List<FeatureInfo>>(content) ?? new List<FeatureInfo>();
            }

            return new List<FeatureInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available features for tier {SubscriptionTier}", subscriptionTier);
            return new List<FeatureInfo>();
        }
    }
}

// Integration Data Models
public class TripAssignmentInfo
{
    public Guid TripId { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DropoffLocation { get; set; } = string.Empty;
    public DateTime PickupTime { get; set; }
    public decimal EstimatedAmount { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
}

public class PODUploadData
{
    public string CustomerSignature { get; set; } = string.Empty;
    public List<string> Photos { get; set; } = new();
    public string Notes { get; set; } = string.Empty;
    public DateTime DeliveryTime { get; set; }
}

public class RFQInfo
{
    public Guid RFQId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime PickupDate { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DropoffLocation { get; set; } = string.Empty;
}

public class DriverInfo
{
    public Guid DriverId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

public class DocumentUploadInfo
{
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FileData { get; set; } = string.Empty;
    public DateTime UploadDate { get; set; }
}

public class NotificationRequest
{
    public List<Guid> Recipients { get; set; } = new();
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
}

public class PaymentInfo
{
    public decimal TotalEarnings { get; set; }
    public decimal PendingPayments { get; set; }
    public int CompletedTrips { get; set; }
    public DateTime LastPaymentDate { get; set; }
}

public class UserInfo
{
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public Dictionary<string, object> Preferences { get; set; } = new();
}

public class SubscriptionInfo
{
    public Guid SubscriptionId { get; set; }
    public string Tier { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsActive { get; set; }
    public List<string> Features { get; set; } = new();
}

public class FeatureInfo
{
    public string FeatureName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}
