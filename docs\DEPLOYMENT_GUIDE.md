# TLI Microservices Deployment Guide

## Overview
This guide covers deployment strategies for the TLI Microservices platform across different environments.

## Deployment Options

### 1. Local Development
For local development and testing.

#### Using Docker Compose
```bash
# Start all services
docker-compose up --build

# Start specific services
docker-compose up postgres rabbitmq redis

# Stop all services
docker-compose down
```

#### Using Scripts
```powershell
# Setup development environment
.\scripts\setup-dev-environment.ps1

# Start services
.\scripts\start-services.ps1

# Stop services
.\scripts\stop-services.ps1
```

### 2. Production Deployment

#### Prerequisites
- Docker and Docker Compose
- PostgreSQL database
- RabbitMQ message broker
- Redis cache
- Load balancer (nginx, HAProxy, or cloud LB)
- SSL certificates

#### Environment Variables
Create a `.env` file for production:
```env
# Database
POSTGRES_HOST=your-postgres-host
POSTGRES_DB=tli_production
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-secure-password

# RabbitMQ
RABBITMQ_HOST=your-rabbitmq-host
RABBITMQ_USER=your-rabbitmq-user
RABBITMQ_PASSWORD=your-rabbitmq-password

# Redis
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# JWT
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters
JWT_ISSUER=TLI.Production
JWT_AUDIENCE=TLI.Services

# API Gateway
GATEWAY_PORT=80
IDENTITY_SERVICE_URL=http://identity-service:8080
```

#### Production Docker Compose
```yaml
version: '3.8'
services:
  apigateway:
    image: tli-apigateway:latest
    ports:
      - "80:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    depends_on:
      - identity-api
    restart: unless-stopped

  identity-api:
    image: tli-identity:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=${POSTGRES_CONNECTION}
      - RabbitMQ__Host=${RABBITMQ_HOST}
      - JwtSettings__Secret=${JWT_SECRET}
    restart: unless-stopped
```

### 3. Cloud Deployment

#### AWS Deployment
Using AWS ECS with Fargate:

1. **Build and Push Images**
   ```bash
   # Build images
   docker build -f Identity/Identity.API/Dockerfile -t tli-identity:latest .
   docker build -f ApiGateway/Dockerfile -t tli-gateway:latest .

   # Tag for ECR
   docker tag tli-identity:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/tli-identity:latest
   docker tag tli-gateway:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/tli-gateway:latest

   # Push to ECR
   docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/tli-identity:latest
   docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/tli-gateway:latest
   ```

2. **Create ECS Task Definitions**
   ```json
   {
     "family": "tli-identity",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "256",
     "memory": "512",
     "containerDefinitions": [
       {
         "name": "identity-api",
         "image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/tli-identity:latest",
         "portMappings": [
           {
             "containerPort": 8080,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "ASPNETCORE_ENVIRONMENT",
             "value": "Production"
           }
         ]
       }
     ]
   }
   ```

#### Azure Deployment
Using Azure Container Instances or Azure Kubernetes Service:

1. **Azure Container Registry**
   ```bash
   # Login to ACR
   az acr login --name yourregistry

   # Build and push
   az acr build --registry yourregistry --image tli-identity:latest .
   ```

2. **Deploy to ACI**
   ```bash
   az container create \
     --resource-group myResourceGroup \
     --name tli-identity \
     --image yourregistry.azurecr.io/tli-identity:latest \
     --cpu 1 \
     --memory 1 \
     --ports 8080
   ```

#### Google Cloud Deployment
Using Google Cloud Run:

1. **Build and Deploy**
   ```bash
   # Build and push to Container Registry
   gcloud builds submit --tag gcr.io/your-project/tli-identity

   # Deploy to Cloud Run
   gcloud run deploy tli-identity \
     --image gcr.io/your-project/tli-identity \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

### 4. Kubernetes Deployment

#### Namespace and ConfigMap
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tli-microservices
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tli-config
  namespace: tli-microservices
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  JWT_ISSUER: "TLI.Production"
  JWT_AUDIENCE: "TLI.Services"
```

#### Secrets
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: tli-secrets
  namespace: tli-microservices
type: Opaque
data:
  postgres-connection: <base64-encoded-connection-string>
  jwt-secret: <base64-encoded-jwt-secret>
  rabbitmq-connection: <base64-encoded-rabbitmq-connection>
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: identity-api
  namespace: tli-microservices
spec:
  replicas: 3
  selector:
    matchLabels:
      app: identity-api
  template:
    metadata:
      labels:
        app: identity-api
    spec:
      containers:
      - name: identity-api
        image: tli-identity:latest
        ports:
        - containerPort: 8080
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: postgres-connection
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service and Ingress
```yaml
apiVersion: v1
kind: Service
metadata:
  name: identity-api-service
  namespace: tli-microservices
spec:
  selector:
    app: identity-api
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tli-ingress
  namespace: tli-microservices
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: apigateway-service
            port:
              number: 80
```

## Monitoring and Observability

### Health Checks
All services expose health check endpoints:
- `/health` - Overall health
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

### Logging
- Structured logging with Serilog
- Centralized log aggregation (ELK Stack, Azure Monitor, etc.)
- Log correlation with request IDs

### Metrics
- Application metrics with Prometheus
- Custom business metrics
- Performance counters

### Distributed Tracing
- OpenTelemetry integration
- Jaeger or Zipkin for trace visualization
- Request correlation across services

## Security Considerations

### Network Security
- Use HTTPS in production
- Implement network segmentation
- Configure firewalls and security groups

### Secrets Management
- Use cloud secret managers (AWS Secrets Manager, Azure Key Vault, etc.)
- Rotate secrets regularly
- Never store secrets in code or images

### Container Security
- Use minimal base images
- Scan images for vulnerabilities
- Run containers as non-root users
- Implement resource limits

## Backup and Disaster Recovery

### Database Backups
- Automated daily backups
- Point-in-time recovery
- Cross-region backup replication

### Application State
- Stateless application design
- External state storage
- Configuration backup

### Recovery Procedures
- Documented recovery steps
- Regular disaster recovery testing
- RTO/RPO targets defined

## Performance Optimization

### Scaling Strategies
- Horizontal pod autoscaling
- Vertical pod autoscaling
- Load balancing configuration

### Caching
- Redis for distributed caching
- CDN for static content
- Database query optimization

### Database Optimization
- Connection pooling
- Read replicas
- Query optimization
- Indexing strategy

## Troubleshooting

### Common Issues
1. **Service Discovery**: Ensure services can communicate
2. **Database Connections**: Check connection strings and network access
3. **Message Queue**: Verify RabbitMQ connectivity
4. **SSL/TLS**: Validate certificates and configurations

### Debugging Tools
- kubectl for Kubernetes debugging
- Docker logs for container debugging
- Application Insights for performance monitoring
- Health check endpoints for service status

### Log Analysis
- Centralized logging for correlation
- Error tracking and alerting
- Performance monitoring dashboards
