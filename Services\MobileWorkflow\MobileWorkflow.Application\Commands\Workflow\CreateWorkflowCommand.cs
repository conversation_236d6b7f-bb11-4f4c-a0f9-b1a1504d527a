using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.Workflow;

public class CreateWorkflowCommand : IRequest<WorkflowDto>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> Definition { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string TriggerType { get; set; } = string.Empty;
    public Dictionary<string, object> TriggerConfiguration { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}
