using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Queries.UIComponents
{
    public class GetUIComponentsQueryHandler : IRequestHandler<GetUIComponentsQuery, List<UIComponentDto>>
    {
        private readonly ICrossPlatformUIService _uiService;
        private readonly ILogger<GetUIComponentsQueryHandler> _logger;

        public GetUIComponentsQueryHandler(
            ICrossPlatformUIService uiService,
            ILogger<GetUIComponentsQueryHandler> logger)
        {
            _uiService = uiService;
            _logger = logger;
        }

        public async Task<List<UIComponentDto>> Handle(GetUIComponentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting UI components with filters - Category: {Category}, Platform: {Platform}",
                    request.Category, request.Platform);

                var filter = new UIComponentFilterRequest
                {
                    Category = request.Category,
                    ComponentType = request.ComponentType,
                    Platform = request.Platform,
                    Tags = request.Tags,
                    IncludeTemplates = request.IncludeTemplates,
                    IncludeInactive = request.IncludeInactive
                };

                var components = await _uiService.GetComponentsAsync(filter, cancellationToken);

                // Apply additional filters
                if (request.PublicOnly)
                {
                    components = components.FindAll(c => c.IsPublic);
                }

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    var searchTerm = request.SearchTerm.ToLowerInvariant();
                    components = components.FindAll(c =>
                        c.Name.ToLowerInvariant().Contains(searchTerm) ||
                        c.DisplayName.ToLowerInvariant().Contains(searchTerm) ||
                        c.Description.ToLowerInvariant().Contains(searchTerm));
                }

                // Apply pagination
                var skip = (request.PageNumber - 1) * request.PageSize;
                var pagedComponents = components.Skip(skip).Take(request.PageSize).ToList();

                _logger.LogInformation("Retrieved {Count} UI components", pagedComponents.Count);

                return pagedComponents;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting UI components");
                throw;
            }
        }
    }
}
