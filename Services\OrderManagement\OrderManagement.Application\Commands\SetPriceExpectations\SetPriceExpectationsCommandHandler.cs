using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.SetPriceExpectations;

public class SetPriceExpectationsCommandHandler : IRequestHandler<SetPriceExpectationsCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<SetPriceExpectationsCommandHandler> _logger;

    public SetPriceExpectationsCommandHandler(
        IRfqRepository rfqRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<SetPriceExpectationsCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(SetPriceExpectationsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Setting price expectations for RFQ {RfqId} by user {SetBy}",
            request.RfqId, request.SetBy);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return false;
            }

            // Validate RFQ can have price expectations set
            if (rfq.Status == RfqStatus.Closed || rfq.Status == RfqStatus.Cancelled)
            {
                _logger.LogWarning("Cannot set price expectations for RFQ {RfqId} in status {Status}", 
                    request.RfqId, rfq.Status);
                return false;
            }

            // Create price expectations
            var priceExpectations = new PriceExpectations(
                request.MinExpectedPrice,
                request.MaxExpectedPrice,
                request.TargetPrice,
                request.ExpectationType,
                request.IsFlexible,
                request.PriceJustification,
                request.PriceValidUntil,
                request.AllowCounterOffers,
                request.MaxCounterOfferVariance,
                request.PricingNotes);

            // Set price expectations on RFQ
            rfq.SetPriceExpectations(priceExpectations);

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var expectationDetails = new List<string>();
                
                if (request.MinExpectedPrice != null)
                    expectationDetails.Add($"Min: {request.MinExpectedPrice.Amount:C} {request.MinExpectedPrice.Currency}");
                
                if (request.MaxExpectedPrice != null)
                    expectationDetails.Add($"Max: {request.MaxExpectedPrice.Amount:C} {request.MaxExpectedPrice.Currency}");
                
                if (request.TargetPrice != null)
                    expectationDetails.Add($"Target: {request.TargetPrice.Amount:C} {request.TargetPrice.Currency}");

                var description = $"Price expectations set ({request.ExpectationType})";
                if (expectationDetails.Any())
                    description += $": {string.Join(", ", expectationDetails)}";

                var metadata = new Dictionary<string, object>
                {
                    ["expectationType"] = request.ExpectationType.ToString(),
                    ["isFlexible"] = request.IsFlexible,
                    ["allowCounterOffers"] = request.AllowCounterOffers
                };

                if (request.MaxCounterOfferVariance.HasValue)
                    metadata["maxCounterOfferVariance"] = request.MaxCounterOfferVariance.Value;

                if (request.PriceValidUntil.HasValue)
                    metadata["priceValidUntil"] = request.PriceValidUntil.Value;

                var timelineEvent = RfqTimeline.CreateEvent(
                    request.RfqId,
                    RfqTimelineEventType.PriceExpectationSet,
                    description,
                    request.SetBy,
                    request.SetByName,
                    "Transport Company",
                    System.Text.Json.JsonSerializer.Serialize(metadata));

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully set price expectations for RFQ {RfqId}", request.RfqId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting price expectations for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
