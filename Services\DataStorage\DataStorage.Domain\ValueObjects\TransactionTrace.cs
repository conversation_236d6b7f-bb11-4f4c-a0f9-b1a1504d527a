using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Transaction trace information
/// </summary>
public class TransactionTrace : ValueObject
{
    public Guid TraceId { get; init; }
    public string TransactionName { get; init; }
    public DateTime StartTime { get; init; }
    public DateTime EndTime { get; init; }
    public TimeSpan Duration { get; init; }
    public string Status { get; init; }
    public List<TraceSpan> Spans { get; init; }
    public Dictionary<string, string> Tags { get; init; }
    public string? ErrorMessage { get; init; }

    public TransactionTrace(
        Guid traceId,
        string transactionName,
        DateTime startTime,
        DateTime endTime,
        string status,
        List<TraceSpan> spans,
        Dictionary<string, string>? tags = null,
        string? errorMessage = null)
    {
        TraceId = traceId;
        TransactionName = transactionName;
        StartTime = startTime;
        EndTime = endTime;
        Duration = endTime - startTime;
        Status = status;
        Spans = spans;
        Tags = tags ?? new Dictionary<string, string>();
        ErrorMessage = errorMessage;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TraceId;
        yield return TransactionName;
        yield return StartTime;
        yield return EndTime;
        yield return Status;
        yield return ErrorMessage ?? string.Empty;
        
        foreach (var span in Spans.OrderBy(x => x.StartTime))
        {
            yield return span;
        }
        
        foreach (var tag in Tags.OrderBy(x => x.Key))
        {
            yield return tag.Key;
            yield return tag.Value;
        }
    }
}

public class TraceSpan : ValueObject
{
    public Guid SpanId { get; init; }
    public Guid? ParentSpanId { get; init; }
    public string OperationName { get; init; }
    public DateTime StartTime { get; init; }
    public DateTime EndTime { get; init; }
    public TimeSpan Duration { get; init; }
    public string ServiceName { get; init; }
    public Dictionary<string, string> Tags { get; init; }
    public List<TraceLog> Logs { get; init; }

    public TraceSpan(
        Guid spanId,
        string operationName,
        DateTime startTime,
        DateTime endTime,
        string serviceName,
        Guid? parentSpanId = null,
        Dictionary<string, string>? tags = null,
        List<TraceLog>? logs = null)
    {
        SpanId = spanId;
        ParentSpanId = parentSpanId;
        OperationName = operationName;
        StartTime = startTime;
        EndTime = endTime;
        Duration = endTime - startTime;
        ServiceName = serviceName;
        Tags = tags ?? new Dictionary<string, string>();
        Logs = logs ?? new List<TraceLog>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SpanId;
        yield return ParentSpanId?.ToString() ?? string.Empty;
        yield return OperationName;
        yield return StartTime;
        yield return EndTime;
        yield return ServiceName;
        
        foreach (var tag in Tags.OrderBy(x => x.Key))
        {
            yield return tag.Key;
            yield return tag.Value;
        }
        
        foreach (var log in Logs.OrderBy(x => x.Timestamp))
        {
            yield return log;
        }
    }
}

public class TraceLog : ValueObject
{
    public DateTime Timestamp { get; init; }
    public string Level { get; init; }
    public string Message { get; init; }
    public Dictionary<string, object> Fields { get; init; }

    public TraceLog(
        DateTime timestamp,
        string level,
        string message,
        Dictionary<string, object>? fields = null)
    {
        Timestamp = timestamp;
        Level = level;
        Message = message;
        Fields = fields ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Timestamp;
        yield return Level;
        yield return Message;
        
        foreach (var field in Fields.OrderBy(x => x.Key))
        {
            yield return field.Key;
            yield return field.Value?.ToString() ?? string.Empty;
        }
    }
}
