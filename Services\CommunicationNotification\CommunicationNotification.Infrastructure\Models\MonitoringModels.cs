namespace CommunicationNotification.Infrastructure.Models;

/// <summary>
/// System health status
/// </summary>
public class SystemHealthStatus
{
    public HealthStatus OverallStatus { get; set; }
    public DateTime LastUpdated { get; set; }
    public List<HealthCheckResult> HealthChecks { get; set; } = new();
    public SystemMetrics? SystemMetrics { get; set; }
    public ServiceMetrics? ServiceMetrics { get; set; }
    public int ActiveAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public TimeSpan Uptime { get; set; }
}

/// <summary>
/// Health check result
/// </summary>
public class HealthCheckResult
{
    public string Name { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public string? Description { get; set; }
    public TimeSpan? Duration { get; set; }
    public Dictionary<string, object>? Data { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Health status enumeration
/// </summary>
public enum HealthStatus
{
    Unknown = 0,
    Healthy = 1,
    Degraded = 2,
    Unhealthy = 3,
    Critical = 4
}

/// <summary>
/// System metrics
/// </summary>
public class SystemMetrics
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageMB { get; set; }
    public double MemoryUsagePercent { get; set; }
    public double DiskUsagePercent { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public double UptimeSeconds { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Service metrics
/// </summary>
public class ServiceMetrics
{
    public double RequestsPerSecond { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public double ErrorRate { get; set; }
    public int ActiveConnections { get; set; }
    public int QueuedMessages { get; set; }
    public int ProcessedMessages { get; set; }
    public double CacheHitRate { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Alert model
/// </summary>
public class Alert
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public AlertStatus Status { get; set; }
    public string Source { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
    public string? Resolution { get; set; }
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Alert status
/// </summary>
public enum AlertStatus
{
    Active = 1,
    Acknowledged = 2,
    Resolved = 3,
    Suppressed = 4
}

/// <summary>
/// Monitoring dashboard data
/// </summary>
public class MonitoringDashboard
{
    public SystemHealthStatus? SystemHealth { get; set; }
    public List<MonitoringEvent> RecentEvents { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
    public AlertSummary? AlertSummary { get; set; }
    public Dictionary<string, ServiceStatus> ServiceStatus { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Monitoring event
/// </summary>
public class MonitoringEvent
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public EventSeverity Severity { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Event severity levels
/// </summary>
public enum EventSeverity
{
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
}

/// <summary>
/// Alert summary
/// </summary>
public class AlertSummary
{
    public int Total { get; set; }
    public int Critical { get; set; }
    public int High { get; set; }
    public int Medium { get; set; }
    public int Low { get; set; }
}

/// <summary>
/// Service status
/// </summary>
public class ServiceStatus
{
    public string Name { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public DateTime LastChecked { get; set; }
    public double ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Monitoring configuration
/// </summary>
public class MonitoringConfiguration
{
    public bool EnableMonitoring { get; set; } = true;
    public bool EnableAlerting { get; set; } = true;
    public int HealthCheckIntervalMinutes { get; set; } = 5;
    public int MetricsCollectionIntervalMinutes { get; set; } = 1;
    public int MonitoringLoopIntervalSeconds { get; set; } = 30;
    public int MaxEventHistory { get; set; } = 10000;
    public int AlertExpirationHours { get; set; } = 24;
    public int AlertRetentionDays { get; set; } = 30;
    public Dictionary<string, ThresholdConfiguration> Thresholds { get; set; } = new();
    public List<string> EnabledHealthChecks { get; set; } = new();
    public AlertingConfiguration Alerting { get; set; } = new();
}

/// <summary>
/// Threshold configuration for metrics
/// </summary>
public class ThresholdConfiguration
{
    public string MetricName { get; set; } = string.Empty;
    public double WarningThreshold { get; set; }
    public double CriticalThreshold { get; set; }
    public string ComparisonOperator { get; set; } = "GreaterThan"; // GreaterThan, LessThan, Equals
    public TimeSpan EvaluationWindow { get; set; } = TimeSpan.FromMinutes(5);
    public int ConsecutiveFailures { get; set; } = 3;
}

/// <summary>
/// Alerting configuration
/// </summary>
public class AlertingConfiguration
{
    public bool EnableEmailAlerts { get; set; } = true;
    public bool EnableSmsAlerts { get; set; } = false;
    public bool EnableSlackAlerts { get; set; } = false;
    public bool EnableWebhookAlerts { get; set; } = false;
    public List<string> EmailRecipients { get; set; } = new();
    public List<string> SmsRecipients { get; set; } = new();
    public string? SlackWebhookUrl { get; set; }
    public string? WebhookUrl { get; set; }
    public Dictionary<AlertSeverity, List<string>> SeverityRouting { get; set; } = new();
    public TimeSpan AlertCooldownPeriod { get; set; } = TimeSpan.FromMinutes(15);
}

/// <summary>
/// Alert notification
/// </summary>
public class AlertNotification
{
    public Guid Id { get; set; }
    public Guid AlertId { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public NotificationStatus Status { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; }
}

/// <summary>
/// Notification channel
/// </summary>
public enum NotificationChannel
{
    Email = 1,
    Sms = 2,
    Slack = 3,
    Webhook = 4,
    PushNotification = 5
}

/// <summary>
/// Notification status
/// </summary>
public enum NotificationStatus
{
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Retrying = 5
}

/// <summary>
/// Health check configuration
/// </summary>
public class HealthCheckConfiguration
{
    public string Name { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
    public TimeSpan Interval { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
    public int FailureThreshold { get; set; } = 3;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Monitoring rule
/// </summary>
public class MonitoringRule
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
    public string MetricName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty; // e.g., "value > 80"
    public TimeSpan EvaluationWindow { get; set; } = TimeSpan.FromMinutes(5);
    public AlertSeverity AlertSeverity { get; set; }
    public string AlertMessage { get; set; } = string.Empty;
    public Dictionary<string, object> Tags { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? LastTriggered { get; set; }
    public int TriggerCount { get; set; }
}

/// <summary>
/// System resource usage
/// </summary>
public class ResourceUsage
{
    public double CpuPercent { get; set; }
    public long MemoryBytes { get; set; }
    public double MemoryPercent { get; set; }
    public long DiskBytes { get; set; }
    public double DiskPercent { get; set; }
    public long NetworkBytesIn { get; set; }
    public long NetworkBytesOut { get; set; }
    public int ProcessCount { get; set; }
    public int ThreadCount { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Performance baseline
/// </summary>
public class PerformanceBaseline
{
    public string MetricName { get; set; } = string.Empty;
    public double BaselineValue { get; set; }
    public double StandardDeviation { get; set; }
    public double MinValue { get; set; }
    public double MaxValue { get; set; }
    public DateTime CalculatedAt { get; set; }
    public TimeSpan CalculationPeriod { get; set; }
    public int SampleCount { get; set; }
}
