namespace CommunicationNotification.Domain.Enums;

public enum MessageStatus
{
    /// <summary>
    /// Message is being prepared for sending
    /// </summary>
    Pending = 1,
    
    /// <summary>
    /// Message has been sent to the delivery service
    /// </summary>
    Sent = 2,
    
    /// <summary>
    /// Message has been delivered to the recipient
    /// </summary>
    Delivered = 3,
    
    /// <summary>
    /// Message has been read by the recipient
    /// </summary>
    Read = 4,
    
    /// <summary>
    /// Message delivery failed
    /// </summary>
    Failed = 5,
    
    /// <summary>
    /// Message was cancelled before sending
    /// </summary>
    Cancelled = 6,
    
    /// <summary>
    /// Message is queued for retry after failure
    /// </summary>
    Retrying = 7
}
