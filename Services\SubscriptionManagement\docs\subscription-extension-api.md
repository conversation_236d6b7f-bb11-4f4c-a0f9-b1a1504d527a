# Subscription Extension API Documentation

## Overview

The Subscription Extension API allows administrators to extend subscription periods or apply grace periods to subscriptions. This functionality is crucial for customer support scenarios where manual intervention is required.

## Authentication & Authorization

- **Authentication**: Bearer token required
- **Authorization**: Admin role required (`Admin`)
- **Endpoint**: `POST /api/subscriptions/{id}/extend`

## Request Format

### URL Parameters
- `id` (required): The GUID of the subscription to extend

### Request Body
```json
{
  "extensionDays": 30,
  "reason": "Customer requested extension due to payment issues",
  "applyAsGracePeriod": false
}
```

### Request Body Schema

| Field | Type | Required | Description | Constraints |
|-------|------|----------|-------------|-------------|
| `extensionDays` | integer | Yes | Number of days to extend | 1-90 days |
| `reason` | string | No | Reason for the extension | Max 500 characters |
| `applyAsGracePeriod` | boolean | No | Whether to apply as grace period | Default: false |

## Response Format

### Success Response (200 OK)
```json
{
  "message": "Subscription extended successfully",
  "extensionDays": 30,
  "applyAsGracePeriod": false,
  "reason": "Customer requested extension due to payment issues"
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "message": "Extension days must be between 1 and 90",
  "errors": {
    "extensionDays": ["Extension days must be greater than 0"]
  }
}
```

#### 401 Unauthorized
```json
{
  "message": "Authentication required"
}
```

#### 403 Forbidden
```json
{
  "message": "Admin role required"
}
```

#### 404 Not Found
```json
{
  "message": "Subscription with ID {id} not found"
}
```

## Extension Types

### Regular Extension
- **Purpose**: Extends the actual subscription billing cycle
- **Effect**: Moves the `NextBillingDate` forward by the specified days
- **Use Case**: Customer paid late, needs billing cycle adjustment
- **Configuration**: `applyAsGracePeriod: false`

### Grace Period Extension
- **Purpose**: Provides additional time without affecting billing cycle
- **Effect**: Sets `GracePeriodEndDate` without changing `NextBillingDate`
- **Use Case**: Temporary access during payment resolution
- **Configuration**: `applyAsGracePeriod: true`

## Business Rules

1. **Maximum Extension**: 90 days per extension request
2. **Cancelled Subscriptions**: Cannot be extended
3. **Expired Subscriptions**: Can be extended (will reactivate if regular extension)
4. **Grace Period**: Only one active grace period per subscription
5. **Audit Trail**: All extensions are logged with admin user ID and reason

## Examples

### Example 1: Regular Extension
```bash
curl -X POST "https://api.example.com/api/subscriptions/123e4567-e89b-12d3-a456-426614174000/extend" \
  -H "Authorization: Bearer {admin-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "extensionDays": 30,
    "reason": "Customer payment delay",
    "applyAsGracePeriod": false
  }'
```

### Example 2: Grace Period Extension
```bash
curl -X POST "https://api.example.com/api/subscriptions/123e4567-e89b-12d3-a456-426614174000/extend" \
  -H "Authorization: Bearer {admin-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "extensionDays": 15,
    "reason": "Payment processing issues",
    "applyAsGracePeriod": true
  }'
```

## Integration Events

When a subscription is extended, the following event is published:

### Event: `subscription.extended`
```json
{
  "subscriptionId": "123e4567-e89b-12d3-a456-426614174000",
  "userId": "987fcdeb-51a2-43d1-9f12-345678901234",
  "planId": "456789ab-cdef-1234-5678-90abcdef1234",
  "extensionDays": 30,
  "reason": "Customer requested extension",
  "extendedByUserId": "admin-user-id",
  "applyAsGracePeriod": false,
  "originalEndDate": "2024-07-24T10:30:00Z",
  "originalNextBillingDate": "2024-07-24T10:30:00Z",
  "originalGracePeriodEndDate": null,
  "newEndDate": null,
  "newNextBillingDate": "2024-08-23T10:30:00Z",
  "newGracePeriodEndDate": null,
  "extendedAt": "2024-06-24T10:30:00Z",
  "status": "Active"
}
```

## Monitoring & Metrics

The following metrics are tracked for subscription extensions:

- `subscription_extensions_total`: Total number of extensions performed
- `subscription_extensions_by_type`: Extensions by type (regular vs grace period)
- `subscription_extensions_by_admin`: Extensions by admin user
- `subscription_extension_days_total`: Total days extended across all subscriptions

## Error Handling

The API implements comprehensive error handling:

1. **Validation Errors**: Input validation with detailed error messages
2. **Business Rule Violations**: Clear error messages for business rule failures
3. **Authorization Errors**: Proper HTTP status codes for auth failures
4. **System Errors**: Graceful handling of system failures with logging

## Rate Limiting

- **Rate Limit**: 100 requests per hour per admin user
- **Burst Limit**: 10 requests per minute
- **Headers**: Rate limit information included in response headers

## Security Considerations

1. **Admin Only**: Only users with Admin role can extend subscriptions
2. **Audit Logging**: All extension activities are logged with admin user ID
3. **Input Validation**: All inputs are validated and sanitized
4. **Authorization**: Proper authorization checks at multiple levels
5. **HTTPS Only**: All API calls must use HTTPS in production
