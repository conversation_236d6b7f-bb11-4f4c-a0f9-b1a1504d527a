using AnalyticsBIService.Domain.Enums;
using System.Linq;

namespace AnalyticsBIService.Application.DTOs;

// ===== COMPREHENSIVE REPORTING DTOs =====

public class ComprehensiveReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public string GeneratedByName { get; set; } = string.Empty;
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public ReportSummaryDto Summary { get; set; } = new();
    public List<ReportSectionDto> Sections { get; set; } = new();
    public List<ReportVisualizationDto> Visualizations { get; set; } = new();
    public List<ReportInsightDto> Insights { get; set; } = new();
    public ReportMetadataDto Metadata { get; set; } = new();

    // Additional properties required by CreateCustomReportTemplateCommandHandler
    public string Content { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string OutputFormat { get; set; } = "PDF";
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public ExportOptionsDto ExportOptions { get; set; } = new();

    // Additional properties required by AdvancedReportingService
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<TrendDataPointDto> Trends { get; set; } = new();
}

public class ReportSummaryDto
{
    public int TotalDataPoints { get; set; }
    public int TotalSections { get; set; }
    public int TotalVisualizations { get; set; }
    public int TotalInsights { get; set; }
    public TimeSpan GenerationTime { get; set; }
    public string DataQuality { get; set; } = string.Empty; // High, Medium, Low
    public decimal CompletionPercentage { get; set; }
    public List<string> KeyFindings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();

    // Additional properties required by AdvancedReportingService
    public int TotalRecords { get; set; }
    public int DataSources { get; set; }
    public List<string> KeyMetrics { get; set; } = new();
    public long ExecutionTime { get; set; }
}

// ReportSectionDto is already defined in ReportingDTOs.cs - using that instead

public class ReportSectionMetricsDto
{
    public int RecordCount { get; set; }
    public DateTime? LastUpdated { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public decimal DataAccuracy { get; set; }
    public List<string> DataQualityIssues { get; set; } = new();
}

public class ReportVisualizationDto
{
    public string VisualizationId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Bar, Line, Pie, Scatter, Heatmap, Table
    public Dictionary<string, object> ChartData { get; set; } = new();
    public ChartConfigurationDto Configuration { get; set; } = new();
    public List<string> DataLabels { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
}

public class ChartConfigurationDto
{
    public string XAxisLabel { get; set; } = string.Empty;
    public string YAxisLabel { get; set; } = string.Empty;
    public List<string> Colors { get; set; } = new();
    public bool ShowLegend { get; set; } = true;
    public bool ShowGrid { get; set; } = true;
    public string Theme { get; set; } = "default";
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

public class ReportInsightDto
{
    public string InsightId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Trend, Anomaly, Recommendation, Prediction
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Importance { get; set; } = string.Empty; // High, Medium, Low
    public decimal Confidence { get; set; }
    public List<string> AffectedMetrics { get; set; } = new();
    public Dictionary<string, object> SupportingData { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
    public DateTime? PredictedDate { get; set; }
    public string Impact { get; set; } = string.Empty; // Positive, Negative, Neutral
}

public class ReportMetadataDto
{
    public string Version { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, string> Parameters { get; set; } = new();
    public string TemplateId { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
    public bool IsScheduled { get; set; }
    public string? ScheduleExpression { get; set; }
    public DateTime? NextScheduledRun { get; set; }
    public List<string> Recipients { get; set; } = new();
    public string AccessLevel { get; set; } = string.Empty; // Public, Private, Restricted
    public Dictionary<string, object> CustomProperties { get; set; } = new(); // Added missing property
}

public class ExportOptionsDto
{
    public List<string> AvailableFormats { get; set; } = new();
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeRawData { get; set; } = false;
    public bool IncludeInsights { get; set; } = true;
    public bool IncludeHeaders { get; set; } = true;
    public string DefaultFormat { get; set; } = "PDF";
    public Dictionary<string, object> FormatOptions { get; set; } = new();
}

// ===== CROSS-SERVICE ANALYTICS DTOs =====

public class CrossServiceAnalyticsDto
{
    public Guid Id { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> Services { get; set; } = new();
    public Dictionary<string, ServiceAnalyticsDataDto> ServiceData { get; set; } = new();
    public List<ServiceCorrelationDto> Correlations { get; set; } = new();
    public List<CrossServiceInsightDto> Insights { get; set; } = new();
    public CrossServiceSummaryDto Summary { get; set; } = new();
}

public class ServiceAnalyticsDataDto
{
    public string ServiceName { get; set; } = string.Empty;
    public Dictionary<string, decimal> Metrics { get; set; } = new();
    public List<TrendDataPointDto> Trends { get; set; } = new();
    public string HealthStatus { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public int DataPointCount { get; set; }
}

public class ServiceCorrelationDto
{
    public string Service1 { get; set; } = string.Empty;
    public string Service2 { get; set; } = string.Empty;
    public string Metric1 { get; set; } = string.Empty;
    public string Metric2 { get; set; } = string.Empty;
    public decimal CorrelationCoefficient { get; set; }
    public string CorrelationStrength { get; set; } = string.Empty; // Strong, Moderate, Weak
    public string CorrelationType { get; set; } = string.Empty; // Positive, Negative
    public decimal Significance { get; set; }
}

public class CrossServiceInsightDto
{
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Importance { get; set; } = string.Empty;
    public List<string> AffectedServices { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class CrossServiceSummaryDto
{
    public int TotalServices { get; set; }
    public int TotalDataPoints { get; set; }
    public int StrongCorrelations { get; set; }
    public int KeyInsights { get; set; }
    public string OverallHealthScore { get; set; } = string.Empty;
    public List<string> CriticalIssues { get; set; } = new();
    public List<string> Opportunities { get; set; } = new();
}

public class TrendDataPointDto
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public string? Label { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

// ===== CUSTOM REPORT BUILDER DTOs =====

public class CustomReportTemplateDto
{
    public Guid Id { get; set; }
    public Guid TemplateId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Guid CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastModified { get; set; }
    public bool IsPublic { get; set; }
    public List<string> Tags { get; set; } = new();
    public ReportConfigurationDto Configuration { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public ReportTemplateMetadataDto Metadata { get; set; } = new();
}

public class ReportConfigurationDto
{
    public List<string> DataSources { get; set; } = new();
    public List<ReportFieldDto> Fields { get; set; } = new();
    public List<ReportFilterDto> Filters { get; set; } = new();
    public List<ReportGroupingDto> Groupings { get; set; } = new();
    public List<ReportSortingDto> Sorting { get; set; } = new();
    public List<ReportVisualizationConfigDto> Visualizations { get; set; } = new();
    public ReportLayoutDto Layout { get; set; } = new();

    // Additional properties required by AdvancedReportingService
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();

    // Additional properties required by ScheduledReportService
    public string ReportType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public ReportFormattingDto? Formatting { get; set; }

    // Alternative property names for compatibility with AdvancedReportingService
    public Dictionary<string, object> DefaultFilters
    {
        get => Filters?.ToDictionary(f => f.FieldName, f => (object)f.Value) ?? new Dictionary<string, object>();
        set => Filters = value?.Select(kvp => new ReportFilterDto { FieldName = kvp.Key, Value = kvp.Value?.ToString() ?? string.Empty }).ToList() ?? new List<ReportFilterDto>();
    }

    public Dictionary<string, object> VisualizationSettings
    {
        get => Visualizations?.ToDictionary(v => v.Type, v => (object)v.Options) ?? new Dictionary<string, object>();
        set => Visualizations = value?.Select(kvp => new ReportVisualizationConfigDto { Type = kvp.Key, Options = (Dictionary<string, object>)(kvp.Value ?? new Dictionary<string, object>()) }).ToList() ?? new List<ReportVisualizationConfigDto>();
    }
}

public class ReportFieldDto
{
    public string FieldName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsVisible { get; set; } = true;
    public string AggregationType { get; set; } = string.Empty; // Sum, Count, Average, Min, Max
    public string FormatString { get; set; } = string.Empty;
    public int Order { get; set; }
}

public class ReportFilterDto
{
    public string FieldName { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty; // Equals, Contains, GreaterThan, etc.
    public object Value { get; set; } = new();
    public string LogicalOperator { get; set; } = "AND"; // AND, OR
}

public class ReportGroupingDto
{
    public string FieldName { get; set; } = string.Empty;
    public string GroupType { get; set; } = string.Empty; // Date, Category, Range
    public Dictionary<string, object> Options { get; set; } = new();
}

public class ReportSortingDto
{
    public string FieldName { get; set; } = string.Empty;
    public string Direction { get; set; } = "ASC"; // ASC, DESC
    public int Priority { get; set; }
}

public class ReportVisualizationConfigDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<string> DataFields { get; set; } = new();
    public List<string> XAxisFields { get; set; } = new();
    public List<string> YAxisFields { get; set; } = new();
    public List<string> SeriesFields { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public int Order { get; set; }
}

public class ReportLayoutDto
{
    public string Orientation { get; set; } = "Portrait"; // Portrait, Landscape
    public string PageSize { get; set; } = "A4";
    public Dictionary<string, object> Margins { get; set; } = new();
    public ReportHeaderDto Header { get; set; } = new();
    public ReportFooterDto Footer { get; set; } = new();
}

public class ReportHeaderDto
{
    public string Title { get; set; } = string.Empty;
    public string Subtitle { get; set; } = string.Empty;
    public string LogoUrl { get; set; } = string.Empty;
    public bool ShowGeneratedDate { get; set; } = true;
    public bool ShowPageNumbers { get; set; } = true;
}

public class ReportFooterDto
{
    public string Text { get; set; } = string.Empty;
    public bool ShowPageNumbers { get; set; } = true;
    public bool ShowGeneratedBy { get; set; } = true;
}

// ReportParameterDto is already defined in ReportingDTOs.cs - using that instead

public class ReportTemplateMetadataDto
{
    public int UsageCount { get; set; }
    public DateTime? LastUsed { get; set; }
    public decimal AverageExecutionTime { get; set; }
    public string Complexity { get; set; } = string.Empty; // Simple, Medium, Complex
    public List<string> RequiredPermissions { get; set; } = new();
    public string Version { get; set; } = "1.0";
}

// ===== EXPORT AND SCHEDULING DTOs =====

public class ExportResultDto
{
    public Guid ExportId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public DateTime ExportedAt { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string Status { get; set; } = string.Empty; // Completed, Failed, InProgress
    public string? ErrorMessage { get; set; }
    public ExportMetadataDto Metadata { get; set; } = new();

    // Additional properties for service compatibility
    public Guid ExecutionId { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public int RecordCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class ExportMetadataDto
{
    public int TotalRecords { get; set; }
    public int TotalPages { get; set; }
    public bool IncludesVisualizations { get; set; }
    public bool IncludesRawData { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public string CompressionType { get; set; } = string.Empty;
    public Dictionary<string, object> FormatSpecificOptions { get; set; } = new();
}

public class DateRangeDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly, Quarterly, Yearly
    public int TotalDays => (EndDate - StartDate).Days;
}

// ===== REQUEST DTOs =====

public class ComprehensiveReportRequest
{
    public string ReportType { get; set; } = string.Empty;
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IncludeVisualizations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public string OutputFormat { get; set; } = "PDF";
    public Guid RequestedBy { get; set; }

    // Additional properties required by AdvancedReportingService
    public List<string> IncludedServices { get; set; } = new();
    public List<string> ChartTypes { get; set; } = new();
    public Dictionary<string, object> VisualizationSettings { get; set; } = new();
    public Dictionary<string, object> Filters { get; set; } = new();
    public string GroupBy { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

public class CrossServiceAnalyticsRequest
{
    public List<string> Services { get; set; } = new();
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public bool IncludeCorrelations { get; set; } = true;
    public bool IncludeInsights { get; set; } = true;
    public decimal CorrelationThreshold { get; set; } = 0.5m;
    public Guid RequestedBy { get; set; }
}

public class CreateReportTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public bool IsPublic { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public ReportConfigurationDto Configuration { get; set; } = new();
    public List<ReportParameterDto> Parameters { get; set; } = new();
    public Guid CreatedBy { get; set; }

    // Additional properties required by AdvancedReportingService
    public Guid UserId { get; set; }
    public List<string> DataSources { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> DefaultFilters { get; set; } = new();
    public Dictionary<string, object> VisualizationSettings { get; set; } = new();
    public Dictionary<string, object> Layout { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
}
