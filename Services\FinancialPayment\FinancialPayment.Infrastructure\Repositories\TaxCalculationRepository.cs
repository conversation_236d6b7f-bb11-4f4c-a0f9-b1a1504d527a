using Microsoft.EntityFrameworkCore;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class TaxCalculationRepository : ITaxCalculationRepository
{
    private readonly FinancialPaymentDbContext _context;

    public TaxCalculationRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<TaxCalculation?> GetByIdAsync(Guid id)
    {
        return await _context.TaxCalculations
            .Include(tc => tc.TaxLines)
            .FirstOrDefaultAsync(tc => tc.Id == id);
    }

    public async Task<TaxCalculation?> GetByOrderIdAsync(Guid orderId)
    {
        return await _context.TaxCalculations
            .Include(tc => tc.TaxLines)
            .Where(tc => tc.OrderId == orderId)
            .OrderByDescending(tc => tc.CalculatedAt)
            .FirstOrDefaultAsync();
    }

    public async Task<List<TaxCalculation>> GetByOrderIdsAsync(List<Guid> orderIds)
    {
        return await _context.TaxCalculations
            .Include(tc => tc.TaxLines)
            .Where(tc => orderIds.Contains(tc.OrderId))
            .OrderByDescending(tc => tc.CalculatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxCalculation>> GetHistoryByOrderIdAsync(Guid orderId)
    {
        return await _context.TaxCalculations
            .Include(tc => tc.TaxLines)
            .Where(tc => tc.OrderId == orderId)
            .OrderByDescending(tc => tc.CalculatedAt)
            .ToListAsync();
    }

    public async Task AddAsync(TaxCalculation taxCalculation)
    {
        await _context.TaxCalculations.AddAsync(taxCalculation);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateAsync(TaxCalculation taxCalculation)
    {
        _context.TaxCalculations.Update(taxCalculation);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var taxCalculation = await _context.TaxCalculations.FindAsync(id);
        if (taxCalculation != null)
        {
            _context.TaxCalculations.Remove(taxCalculation);
            await _context.SaveChangesAsync();
        }
    }
}
