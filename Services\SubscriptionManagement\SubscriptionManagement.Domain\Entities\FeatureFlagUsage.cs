using Shared.Domain.Common;

namespace SubscriptionManagement.Domain.Entities;

public class FeatureFlagUsage : BaseEntity
{
    public Guid FeatureFlagId { get; private set; }
    public Guid UserId { get; private set; }
    public string? Variant { get; private set; }
    public DateTime AccessedAt { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public string? UserAgent { get; private set; }
    public string? IpAddress { get; private set; }
    public string? SessionId { get; private set; }

    private FeatureFlagUsage() 
    {
        Context = new Dictionary<string, object>();
    }

    public FeatureFlagUsage(
        Guid featureFlagId,
        Guid userId,
        string? variant = null,
        Dictionary<string, object>? context = null) : this()
    {
        FeatureFlagId = featureFlagId;
        UserId = userId;
        Variant = variant;
        AccessedAt = DateTime.UtcNow;
        Context = context ?? new Dictionary<string, object>();
    }

    public void SetUserAgent(string userAgent)
    {
        UserAgent = userAgent;
    }

    public void SetIpAddress(string ipAddress)
    {
        IpAddress = ipAddress;
    }

    public void SetSessionId(string sessionId)
    {
        SessionId = sessionId;
    }

    public void AddContextData(string key, object value)
    {
        Context[key] = value;
    }
}
