namespace Shared.Messaging.Events;

/// <summary>
/// Event published when a service provider rating is created
/// </summary>
public class ServiceProviderRatingCreatedEvent
{
    public Guid RatingId { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public Guid? TripId { get; set; }
    public string? TripNumber { get; set; }
    public decimal OverallRating { get; set; }
    public string? ReviewTitle { get; set; }
    public string? ReviewComment { get; set; }
    public bool IsAnonymous { get; set; }
    public DateTime ServiceCompletedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<CategoryRatingData> CategoryRatings { get; set; } = new();
}

/// <summary>
/// Event published when a service provider rating is submitted
/// </summary>
public class ServiceProviderRatingSubmittedEvent
{
    public Guid RatingId { get; set; }
    public Guid ShipperId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public DateTime SubmittedAt { get; set; }
    public bool IsPositiveRating { get; set; }
    public int TotalRatingsForCompany { get; set; }
    public decimal NewAverageRating { get; set; }
}

/// <summary>
/// Event published when a service provider rating is flagged
/// </summary>
public class ServiceProviderRatingFlaggedEvent
{
    public Guid RatingId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime FlaggedAt { get; set; }
    public string FlaggedBy { get; set; } = string.Empty;
    public bool RequiresReview { get; set; }
}

/// <summary>
/// Event published when a service issue is reported
/// </summary>
public class ServiceIssueReportedEvent
{
    public Guid IssueId { get; set; }
    public Guid RatingId { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Evidence { get; set; }
    public DateTime ReportedAt { get; set; }
    public bool IsHighPriority { get; set; }
}

/// <summary>
/// Event published when a service issue is resolved
/// </summary>
public class ServiceIssueResolvedEvent
{
    public Guid IssueId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty;
    public string Resolution { get; set; } = string.Empty;
    public Guid ResolvedBy { get; set; }
    public string ResolvedByName { get; set; } = string.Empty;
    public DateTime ResolvedAt { get; set; }
    public TimeSpan ResolutionTime { get; set; }
}

/// <summary>
/// Event published when a transport company is added to preferred provider network
/// </summary>
public class PreferredProviderAddedEvent
{
    public Guid NetworkId { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public int PreferenceRank { get; set; }
    public DateTime AddedAt { get; set; }
}

/// <summary>
/// Event published when a transport company is removed from preferred provider network
/// </summary>
public class PreferredProviderRemovedEvent
{
    public Guid NetworkId { get; set; }
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime RemovedAt { get; set; }
}

/// <summary>
/// Event published when transport company rating statistics are updated
/// </summary>
public class TransportCompanyRatingUpdatedEvent
{
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal NewAverageRating { get; set; }
    public decimal PreviousAverageRating { get; set; }
    public int TotalRatings { get; set; }
    public int NewRatingsCount { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsSignificantChange { get; set; }
    public List<CategoryRatingAverage> CategoryAverages { get; set; } = new();
}

/// <summary>
/// Supporting data classes for rating events
/// </summary>
public class CategoryRatingData
{
    public string Category { get; set; } = string.Empty;
    public string CategoryName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public string? Comment { get; set; }
}

public class CategoryRatingAverage
{
    public string Category { get; set; } = string.Empty;
    public string CategoryName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalRatings { get; set; }
}
