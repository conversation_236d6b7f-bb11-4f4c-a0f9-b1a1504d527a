using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ScheduleMaintenance;

public class ScheduleMaintenanceCommandHandler : IRequestHandler<ScheduleMaintenanceCommand, Guid>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ScheduleMaintenanceCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;
    private readonly INotificationService _notificationService;

    public ScheduleMaintenanceCommandHandler(
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        ILogger<ScheduleMaintenanceCommandHandler> logger,
        IMessageBroker messageBroker,
        INotificationService notificationService)
    {
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
        _notificationService = notificationService;
    }

    public async Task<Guid> Handle(ScheduleMaintenanceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Scheduling maintenance for vehicle {VehicleId}", request.VehicleId);

            // Get the vehicle
            var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
            if (vehicle == null)
            {
                throw new ArgumentException($"Vehicle {request.VehicleId} not found");
            }

            // Create maintenance record
            var maintenance = new VehicleMaintenance(
                vehicleId: request.VehicleId,
                maintenanceType: request.MaintenanceType,
                scheduledDate: request.ScheduledDate,
                description: request.Description,
                serviceProvider: request.ServiceProvider,
                estimatedCost: request.EstimatedCost,
                estimatedDurationHours: request.EstimatedDurationHours,
                priority: request.Priority,
                isRecurring: request.IsRecurring,
                recurrenceIntervalDays: request.RecurrenceIntervalDays,
                notes: request.Notes,
                scheduledBy: request.ScheduledBy);

            // Add maintenance to vehicle
            vehicle.ScheduleMaintenance(maintenance);

            // Update vehicle status if maintenance is imminent
            if (request.ScheduledDate <= DateTime.UtcNow.AddDays(1) && request.Priority >= MaintenancePriority.High)
            {
                vehicle.UpdateStatus(VehicleStatus.Maintenance);
            }

            // Save changes
            _vehicleRepository.Update(vehicle);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Send notifications
            await _notificationService.SendNotificationAsync(
                vehicle.CarrierId,
                "Maintenance Scheduled",
                $"Maintenance scheduled for vehicle {vehicle.RegistrationNumber} on {request.ScheduledDate:yyyy-MM-dd}",
                cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("vehicle.maintenance_scheduled", new
            {
                VehicleId = request.VehicleId,
                MaintenanceId = maintenance.Id,
                MaintenanceType = request.MaintenanceType.ToString(),
                ScheduledDate = request.ScheduledDate,
                Priority = request.Priority.ToString(),
                EstimatedCost = request.EstimatedCost,
                ScheduledBy = request.ScheduledBy,
                ScheduledAt = DateTime.UtcNow
            }, cancellationToken);

            _logger.LogInformation("Maintenance {MaintenanceId} scheduled successfully for vehicle {VehicleId}", 
                maintenance.Id, request.VehicleId);

            return maintenance.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling maintenance for vehicle {VehicleId}", request.VehicleId);
            throw;
        }
    }
}
