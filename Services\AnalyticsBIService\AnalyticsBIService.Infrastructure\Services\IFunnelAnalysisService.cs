using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for funnel analysis service
/// </summary>
public interface IFunnelAnalysisService
{
    /// <summary>
    /// Create a new funnel analysis
    /// </summary>
    Task<FunnelAnalysisDto> CreateFunnelAnalysisAsync(CreateFunnelAnalysisRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel analysis by ID
    /// </summary>
    Task<FunnelAnalysisDto?> GetFunnelAnalysisAsync(Guid funnelId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user funnels for a specific user type
    /// </summary>
    Task<List<FunnelAnalysisDto>> GetUserFunnelsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate conversion rates for a funnel
    /// </summary>
    Task<FunnelConversionDto> CalculateConversionRatesAsync(Guid funnelId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel performance metrics
    /// </summary>
    Task<FunnelPerformanceDto> GetFunnelPerformanceAsync(Guid funnelId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Compare multiple funnels
    /// </summary>
    Task<FunnelComparisonDto> CompareFunnelPerformanceAsync(List<Guid> funnelIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel drop-off analysis
    /// </summary>
    Task<FunnelDropOffDto> GetDropOffAnalysisAsync(Guid funnelId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel optimization recommendations
    /// </summary>
    Task<FunnelOptimizationDto> GetOptimizationRecommendationsAsync(Guid funnelId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel cohort analysis
    /// </summary>
    Task<FunnelCohortDto> GetFunnelCohortAnalysisAsync(Guid funnelId, string cohortType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update funnel definition
    /// </summary>
    Task<bool> UpdateFunnelDefinitionAsync(Guid funnelId, UpdateFunnelRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete funnel analysis
    /// </summary>
    Task<bool> DeleteFunnelAnalysisAsync(Guid funnelId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available funnel templates
    /// </summary>
    Task<List<FunnelTemplateDto>> GetFunnelTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Track funnel event
    /// </summary>
    Task<bool> TrackFunnelEventAsync(TrackFunnelEventRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get real-time funnel metrics
    /// </summary>
    Task<RealTimeFunnelDto> GetRealTimeFunnelMetricsAsync(Guid funnelId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Funnel analysis data transfer object
/// </summary>
public class FunnelAnalysisDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public FunnelDefinitionDto Definition { get; set; } = new();
    public FunnelMetricsDto Metrics { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public UserType UserType { get; set; }
    public bool IsActive { get; set; }
    public string FunnelType { get; set; } = string.Empty;
}

/// <summary>
/// Funnel definition data transfer object
/// </summary>
public class FunnelDefinitionDto
{
    public List<FunnelStepDefinitionDto> Steps { get; set; } = new();
    public string TimeWindow { get; set; } = "30 days";
    public List<FunnelFilterDto> Filters { get; set; } = new();
    public Dictionary<string, object> CustomParameters { get; set; } = new();
    public bool AllowSkippingSteps { get; set; }
    public string ConversionGoal { get; set; } = string.Empty;
}

/// <summary>
/// Funnel step definition data transfer object
/// </summary>
public class FunnelStepDefinitionDto
{
    public int StepOrder { get; set; }
    public string StepName { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public List<FunnelStepCriteriaDto> Criteria { get; set; } = new();
    public bool IsRequired { get; set; } = true;
    public TimeSpan? MaxTimeToNext { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Funnel step criteria data transfer object
/// </summary>
public class FunnelStepCriteriaDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public string LogicalOperator { get; set; } = "AND";
}

/// <summary>
/// Funnel filter data transfer object
/// </summary>
public class FunnelFilterDto
{
    public string FilterType { get; set; } = string.Empty;
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object Value { get; set; } = new();
    public bool IsGlobal { get; set; }
}

/// <summary>
/// Funnel metrics data transfer object
/// </summary>
public class FunnelMetricsDto
{
    public decimal OverallConversionRate { get; set; }
    public decimal TotalConversionRate { get; set; }
    public int TotalEntries { get; set; }
    public int TotalConversions { get; set; }
    public decimal AverageTimeToConvert { get; set; }
    public decimal DropOffRate { get; set; }
    public DateTime LastCalculated { get; set; }
    public List<FunnelStepMetricsDto> StepMetrics { get; set; } = new();
}

/// <summary>
/// Funnel step metrics data transfer object
/// </summary>
public class FunnelStepMetricsDto
{
    public int StepOrder { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int Entries { get; set; }
    public int Exits { get; set; }
    public int Conversions { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal DropOffRate { get; set; }
    public TimeSpan AverageTimeInStep { get; set; }
    public TimeSpan AverageTimeToNext { get; set; }
}

/// <summary>
/// Funnel conversion data transfer object
/// </summary>
public class FunnelConversionDto
{
    public Guid FunnelId { get; set; }
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }
    public List<FunnelStepConversionDto> StepConversions { get; set; } = new();
    public FunnelConversionSummaryDto Summary { get; set; } = new();
    public List<FunnelConversionTrendDto> Trends { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

/// <summary>
/// Funnel step conversion data transfer object
/// </summary>
public class FunnelStepConversionDto
{
    public int StepOrder { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int UniqueUsers { get; set; }
    public int TotalEvents { get; set; }
    public decimal ConversionFromPrevious { get; set; }
    public decimal ConversionFromStart { get; set; }
    public decimal DropOffFromPrevious { get; set; }
    public List<ConversionSegmentDto> Segments { get; set; } = new();
}

/// <summary>
/// Conversion segment data transfer object
/// </summary>
public class ConversionSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal ConversionRate { get; set; }
    public Dictionary<string, object> Characteristics { get; set; } = new();
}

/// <summary>
/// Funnel conversion summary data transfer object
/// </summary>
public class FunnelConversionSummaryDto
{
    public decimal OverallConversionRate { get; set; }
    public int TotalStartUsers { get; set; }
    public int TotalCompletedUsers { get; set; }
    public string WorstPerformingStep { get; set; } = string.Empty;
    public string BestPerformingStep { get; set; } = string.Empty;
    public TimeSpan AverageCompletionTime { get; set; }
    public decimal ImprovementPotential { get; set; }
}

/// <summary>
/// Funnel conversion trend data transfer object
/// </summary>
public class FunnelConversionTrendDto
{
    public DateTime Date { get; set; }
    public decimal ConversionRate { get; set; }
    public int Conversions { get; set; }
    public int Entries { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Funnel performance data transfer object
/// </summary>
public class FunnelPerformanceDto
{
    public Guid FunnelId { get; set; }
    public List<PerformanceMetricDto> Metrics { get; set; } = new();
    public List<PerformanceTrendDto> Trends { get; set; } = new();
    public PerformanceBenchmarkDto Benchmark { get; set; } = new();
    public List<PerformanceInsightDto> Insights { get; set; } = new();
    public DateTime CalculatedAt { get; set; }
}

/// <summary>
/// Performance insight data transfer object
/// </summary>
public class PerformanceInsightDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Funnel comparison data transfer object
/// </summary>
public class FunnelComparisonDto
{
    public List<Guid> FunnelIds { get; set; } = new();
    public List<FunnelComparisonItemDto> Comparisons { get; set; } = new();
    public ComparisonSummaryDto Summary { get; set; } = new();
    public DateTime ComparedAt { get; set; }
}

/// <summary>
/// Funnel comparison item data transfer object
/// </summary>
public class FunnelComparisonItemDto
{
    public Guid FunnelId { get; set; }
    public string FunnelName { get; set; } = string.Empty;
    public Dictionary<string, decimal> Metrics { get; set; } = new();
    public string PerformanceRank { get; set; } = string.Empty;
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
}

/// <summary>
/// Funnel drop-off data transfer object
/// </summary>
public class FunnelDropOffDto
{
    public Guid FunnelId { get; set; }
    public List<DropOffPointDto> DropOffPoints { get; set; } = new();
    public DropOffSummaryDto Summary { get; set; } = new();
    public List<DropOffReasonDto> Reasons { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

/// <summary>
/// Drop-off point data transfer object
/// </summary>
public class DropOffPointDto
{
    public int StepOrder { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int DropOffCount { get; set; }
    public decimal DropOffRate { get; set; }
    public decimal ImpactOnOverallConversion { get; set; }
    public List<string> CommonExitActions { get; set; } = new();
    public TimeSpan AverageTimeBeforeDropOff { get; set; }
}

/// <summary>
/// Drop-off summary data transfer object
/// </summary>
public class DropOffSummaryDto
{
    public string HighestDropOffStep { get; set; } = string.Empty;
    public decimal HighestDropOffRate { get; set; }
    public decimal TotalDropOffRate { get; set; }
    public int TotalDropOffs { get; set; }
    public List<string> PrimaryReasons { get; set; } = new();
}

/// <summary>
/// Drop-off reason data transfer object
/// </summary>
public class DropOffReasonDto
{
    public string Reason { get; set; } = string.Empty;
    public decimal Frequency { get; set; }
    public string Category { get; set; } = string.Empty;
    public List<string> AffectedSteps { get; set; } = new();
    public string Impact { get; set; } = string.Empty;
}

/// <summary>
/// Funnel optimization data transfer object
/// </summary>
public class FunnelOptimizationDto
{
    public Guid FunnelId { get; set; }
    public List<OptimizationRecommendationDto> Recommendations { get; set; } = new();
    public OptimizationSummaryDto Summary { get; set; } = new();
    public List<OptimizationOpportunityDto> Opportunities { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Optimization recommendation data transfer object
/// </summary>
public class OptimizationRecommendationDto
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public decimal EstimatedImpact { get; set; }
    public string ImplementationEffort { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
    public List<string> AffectedSteps { get; set; } = new();
}

/// <summary>
/// Optimization summary data transfer object
/// </summary>
public class OptimizationSummaryDto
{
    public decimal CurrentConversionRate { get; set; }
    public decimal PotentialConversionRate { get; set; }
    public decimal EstimatedImprovement { get; set; }
    public int HighPriorityRecommendations { get; set; }
    public int MediumPriorityRecommendations { get; set; }
    public int LowPriorityRecommendations { get; set; }
}

/// <summary>
/// Optimization opportunity data transfer object
/// </summary>
public class OptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public decimal PotentialGain { get; set; }
    public string Confidence { get; set; } = string.Empty;
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// Funnel cohort data transfer object
/// </summary>
public class FunnelCohortDto
{
    public Guid FunnelId { get; set; }
    public string CohortType { get; set; } = string.Empty;
    public List<FunnelCohortSegmentDto> Segments { get; set; } = new();
    public FunnelCohortSummaryDto Summary { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

/// <summary>
/// Funnel cohort segment data transfer object
/// </summary>
public class FunnelCohortSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public decimal ConversionRate { get; set; }
    public TimeSpan AverageCompletionTime { get; set; }
    public Dictionary<string, decimal> StepConversions { get; set; } = new();
    public List<string> Characteristics { get; set; } = new();
}

/// <summary>
/// Funnel cohort summary data transfer object
/// </summary>
public class FunnelCohortSummaryDto
{
    public string BestPerformingSegment { get; set; } = string.Empty;
    public string WorstPerformingSegment { get; set; } = string.Empty;
    public decimal AverageConversionRate { get; set; }
    public decimal ConversionRateVariance { get; set; }
    public List<string> KeyDifferentiators { get; set; } = new();
}

/// <summary>
/// Funnel template data transfer object
/// </summary>
public class FunnelTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public FunnelDefinitionDto DefaultDefinition { get; set; } = new();
    public List<string> RecommendedMetrics { get; set; } = new();
    public bool IsCustomizable { get; set; }
    public string Industry { get; set; } = string.Empty;
}

/// <summary>
/// Real-time funnel data transfer object
/// </summary>
public class RealTimeFunnelDto
{
    public Guid FunnelId { get; set; }
    public List<RealTimeFunnelStepDto> Steps { get; set; } = new();
    public RealTimeFunnelSummaryDto Summary { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public int UpdateInterval { get; set; }
}

/// <summary>
/// Real-time funnel step data transfer object
/// </summary>
public class RealTimeFunnelStepDto
{
    public int StepOrder { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int CurrentUsers { get; set; }
    public int TodayEntries { get; set; }
    public int TodayConversions { get; set; }
    public decimal TodayConversionRate { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Real-time funnel summary data transfer object
/// </summary>
public class RealTimeFunnelSummaryDto
{
    public int TotalActiveUsers { get; set; }
    public decimal CurrentConversionRate { get; set; }
    public int TodayConversions { get; set; }
    public string PerformanceStatus { get; set; } = string.Empty;
    public List<string> Alerts { get; set; } = new();
}

// Request DTOs
/// <summary>
/// Create funnel analysis request
/// </summary>
public class CreateFunnelAnalysisRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string FunnelType { get; set; } = string.Empty;
    public FunnelDefinitionDto Definition { get; set; } = new();
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
}

/// <summary>
/// Update funnel request
/// </summary>
public class UpdateFunnelRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public FunnelDefinitionDto? Definition { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Track funnel event request
/// </summary>
public class TrackFunnelEventRequest
{
    public Guid FunnelId { get; set; }
    public Guid UserId { get; set; }
    public string EventName { get; set; } = string.Empty;
    public int StepOrder { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public string SessionId { get; set; } = string.Empty;
}
