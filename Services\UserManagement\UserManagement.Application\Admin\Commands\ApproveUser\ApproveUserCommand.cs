using MediatR;
using FluentValidation;

namespace UserManagement.Application.Admin.Commands.ApproveUser
{
    public class ApproveUserCommand : IRequest<ApproveUserResponse>
    {
        public Guid UserId { get; set; }
        public Guid ApprovedBy { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class ApproveUserResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime ApprovedAt { get; set; }
    }

    public class ApproveUserCommandValidator : AbstractValidator<ApproveUserCommand>
    {
        public ApproveUserCommandValidator()
        {
            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.ApprovedBy)
                .NotEmpty()
                .WithMessage("Approver ID is required");
        }
    }
}
