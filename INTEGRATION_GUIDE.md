# 🚀 TLI Microservices Platform - Comprehensive Integration Guide

**Last Updated:** June 30, 2025
**Platform Status:** Production-Ready (85-95% Complete)
**Services:** 15 core microservices + API Gateway + Identity Service

This guide provides comprehensive step-by-step instructions for integrating all TLI microservices with production-ready deployment capabilities. The platform consists of **8 production-ready services** and **9 near-complete services** with comprehensive integration patterns.

## 📋 Prerequisites Checklist

### 🔧 Infrastructure Requirements

Before starting, ensure you have:

- [ ] .NET 8 SDK installed
- [ ] PostgreSQL database running (primary database)
- [ ] TimescaleDB extension enabled (for analytics and tracking)
- [ ] Redis server running (caching and session storage)
- [ ] RabbitMQ running (event-driven messaging)
- [ ] Docker Desktop (for containerized deployment)

### 🌐 Core Services Dependencies

- [ ] API Gateway running on port 5000 (entry point)
- [ ] Identity Service running on port 5001 (authentication)

### 💳 Payment Gateway Integrations

- [ ] RazorPay account with API credentials
- [ ] Stripe account (optional, multi-gateway support)
- [ ] PayPal account (optional, international payments)

### 📱 Communication Service Integrations

- [ ] WhatsApp Business API credentials
- [ ] SMS provider credentials (Twilio/AWS SNS)
- [ ] Email service credentials (SendGrid/AWS SES)
- [ ] Push notification service (Firebase/OneSignal)

### 🔍 External API Integrations

- [ ] PAN verification API credentials
- [ ] GST verification API credentials
- [ ] Aadhar verification API credentials
- [ ] OCR service credentials (AWS Textract/Azure/Google)

## 🔧 Step-by-Step Integration

### Step 1: Database Setup

#### Option A: Automated Setup (Recommended)

```powershell
# Navigate to the Subscription Management directory
cd Services/SubscriptionManagement

# Run the automated setup script
./setup.ps1 -RunService
```

#### Option B: Manual Setup

```bash
# 1. Create the database
psql -h localhost -U timescale -d postgres -c "CREATE DATABASE \"TLI_SubscriptionManagement\";"

# 2. Run the setup script
psql -h localhost -U timescale -d TLI_SubscriptionManagement -f database-setup.sql
```

### Step 2: Configuration

Update the configuration files:

#### Subscription Management Service (`appsettings.json`)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale"
  },
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services",
    "ExpiryMinutes": 60
  },
  "RazorPay": {
    "KeyId": "rzp_test_your_key_id",
    "KeySecret": "your_secret_key",
    "WebhookSecret": "your_webhook_secret"
  }
}
```

#### API Gateway (`ocelot.json`) - Already Updated ✅

The API Gateway configuration has been updated to include routes for the Subscription Management Service.

### Step 3: Start Services in Order

```bash
# 1. Start Identity Service (if not already running)
cd Identity/Identity.API
dotnet run  # Runs on port 5001

# 2. Start User Management Service (if not already running)
cd UserManagement/UserManagement.API
dotnet run  # Runs on port 5002

# 3. Start Subscription Management Service
cd Services/SubscriptionManagement/SubscriptionManagement.API
dotnet run  # Runs on port 5003

# 4. Start API Gateway (if not already running)
cd ApiGateway
dotnet run  # Runs on port 5000
```

### Step 4: Verify Integration

#### Test API Gateway Routing

```bash
# Test plans endpoint through API Gateway
curl -X GET "http://localhost:5000/api/plans" \
  -H "accept: application/json"

# Test subscriptions endpoint (requires authentication)
curl -X GET "http://localhost:5000/api/subscriptions/my-subscription" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Test Direct Service Access

```bash
# Test direct access to Subscription Management Service
curl -X GET "http://localhost:5003/api/plans" \
  -H "accept: application/json"
```

## 🔗 Service Integration Points

### 1. Authentication Flow

```
Client → API Gateway → Subscription Service → Identity Service (JWT validation)
```

### 2. User Data Integration

```
Subscription Service → User Management Service (user profile data)
```

### 3. Event-Driven Communication

```
Subscription Service → RabbitMQ → Other Services (subscription events)
```

## 📡 API Endpoints Available

### Through API Gateway (Recommended)

#### Public Endpoints (No Authentication Required)

- `GET /api/plans` - Get all public plans
- `GET /api/plans/{id}` - Get specific plan details
- `GET /api/plans/by-user-type/{userType}` - Get plans for specific user type

#### Protected Endpoints (JWT Required)

- `POST /api/subscriptions` - Create new subscription
- `GET /api/subscriptions/my-subscription` - Get current user's subscription
- `POST /api/subscriptions/{id}/cancel` - Cancel subscription
- `POST /api/subscriptions/{id}/upgrade` - Upgrade subscription plan
- `POST /api/subscriptions/{id}/downgrade` - Downgrade subscription plan

#### Admin Endpoints (Admin Role Required)

- `GET /api/subscriptions` - Get all subscriptions
- `POST /api/plans` - Create new plan
- `PUT /api/plans/{id}` - Update existing plan
- `POST /api/plans/{id}/features` - Add features to plan

## 🔄 Event Integration

### Published Events

The Subscription Management Service publishes the following events:

```csharp
// Subscription Events
- subscription.created
- subscription.activated
- subscription.cancelled
- subscription.upgraded
- subscription.downgraded
- subscription.renewed

// Payment Events
- payment.processed
- payment.failed
- payment.refunded

// Plan Events
- plan.created
- plan.updated
```

### Event Consumers

Other services can subscribe to these events for:

- User notifications
- Analytics and reporting
- Billing and invoicing
- Feature access control

## 🧪 Testing the Integration

### 1. End-to-End User Flow Test

```bash
# 1. Register a new user (Identity Service)
curl -X POST "http://localhost:5000/api/identity/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "userType": "TransportCompany"
  }'

# 2. Login to get JWT token
curl -X POST "http://localhost:5000/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!"
  }'

# 3. Get available plans
curl -X GET "http://localhost:5000/api/plans/by-user-type/TransportCompany"

# 4. Create subscription
curl -X POST "http://localhost:5000/api/subscriptions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "PLAN_GUID_FROM_STEP_3",
    "autoRenew": true
  }'
```

### 2. Admin Flow Test

```bash
# 1. Login as admin
curl -X POST "http://localhost:5000/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'

# 2. Create a new plan
curl -X POST "http://localhost:5000/api/plans" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Custom Plan",
    "description": "Custom plan for enterprise clients",
    "type": "Enterprise",
    "userType": "TransportCompany",
    "price": 15000,
    "currency": "INR",
    "billingInterval": "Monthly"
  }'
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Service Not Starting

```bash
# Check if port is already in use
netstat -an | findstr :5003

# Kill process using the port
taskkill /PID <process_id> /F
```

#### 2. Database Connection Issues

```bash
# Test PostgreSQL connection
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT 1;"

# Check if database exists
psql -h localhost -U timescale -d postgres -c "\l"
```

#### 3. JWT Authentication Issues

- Ensure JWT secret matches across all services
- Check token expiration
- Verify Authorization header format: `Bearer <token>`

#### 4. API Gateway Routing Issues

```bash
# Check Ocelot configuration
cat ApiGateway/ocelot.json

# Restart API Gateway after configuration changes
cd ApiGateway
dotnet run
```

## 📊 Monitoring and Health Checks

### Health Check Endpoints

- Subscription Service: `http://localhost:5003/health`
- API Gateway: `http://localhost:5000/health`

### Key Metrics to Monitor

- Response times for subscription operations
- Payment success/failure rates
- Database connection health
- Memory and CPU usage

## 🔐 Security Considerations

1. **JWT Token Security**

   - Use strong secrets (32+ characters)
   - Implement token refresh mechanism
   - Set appropriate expiration times

2. **Database Security**

   - Use connection pooling
   - Implement proper access controls
   - Regular security updates

3. **API Security**
   - Rate limiting (implement in API Gateway)
   - Input validation
   - HTTPS in production

## 🚀 Production Deployment

### Environment Variables

```bash
# Required environment variables for production
export ConnectionStrings__DefaultConnection="Host=prod-db;Port=5432;Database=TLI_SubscriptionManagement;User Id=app_user;Password=secure_password"
export JwtSettings__Secret="production-secret-key-32-characters-minimum"
export RazorPay__KeyId="rzp_live_your_key_id"
export RazorPay__KeySecret="your_live_secret_key"
```

### Docker Deployment

```bash
# Build and run with Docker Compose
cd Services/SubscriptionManagement
docker-compose up -d
```

## 📞 Support

If you encounter issues during integration:

1. Check the service logs for detailed error messages
2. Verify all prerequisites are met
3. Test each service individually before testing integration
4. Review the SETUP.md file for detailed configuration options

## ✅ Integration Checklist

- [ ] Database created and schema applied
- [ ] Configuration files updated
- [ ] All services starting successfully
- [ ] API Gateway routing working
- [ ] Authentication flow working
- [ ] Sample API calls successful
- [ ] Health checks passing
- [ ] Events being published/consumed (if using messaging)

🎉 **Congratulations!** Your Subscription Management Service is now fully integrated with the TLI Logistics platform!
