using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for communication audit and compliance services
/// </summary>
public interface ICommunicationAuditService
{
    /// <summary>
    /// Log a communication audit event
    /// </summary>
    Task<AuditResult> LogCommunicationEventAsync(
        CommunicationAuditEvent auditEvent,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get audit trail for investigation or compliance
    /// </summary>
    Task<List<AuditRecord>> GetAuditTrailAsync(
        AuditTrailQuery query,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Archive a message for compliance or legal requirements
    /// </summary>
    Task<MessageArchive> ArchiveMessageAsync(
        Guid messageId,
        ArchiveReason reason,
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate compliance report
    /// </summary>
    Task<ComplianceReport> GenerateComplianceReportAsync(
        ComplianceReportRequest request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get data for dispute resolution
    /// </summary>
    Task<DisputeResolutionData> GetDisputeResolutionDataAsync(
        Guid messageId,
        DisputeContext context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Apply data retention policy
    /// </summary>
    Task<DataRetentionResult> ApplyDataRetentionPolicyAsync(
        DataRetentionPolicy policy,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for audit repository
/// </summary>
public interface IAuditRepository
{
    /// <summary>
    /// Add audit record
    /// </summary>
    Task AddAsync(AuditRecord record, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get audit trail based on query
    /// </summary>
    Task<List<AuditRecord>> GetAuditTrailAsync(AuditTrailQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add message archive
    /// </summary>
    Task AddArchiveAsync(MessageArchive archive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get message archive
    /// </summary>
    Task<MessageArchive?> GetMessageArchiveAsync(Guid messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get records for retention policy application
    /// </summary>
    Task<List<AuditRecord>> GetRecordsForRetentionAsync(DataRetentionPolicy policy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete audit record
    /// </summary>
    Task DeleteAsync(Guid recordId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for compliance repository
/// </summary>
public interface IComplianceRepository
{
    /// <summary>
    /// Add compliance report
    /// </summary>
    Task AddReportAsync(ComplianceReport report, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance reports
    /// </summary>
    Task<List<ComplianceReport>> GetReportsAsync(ComplianceReportQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add data retention policy
    /// </summary>
    Task AddRetentionPolicyAsync(DataRetentionPolicy policy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active retention policies
    /// </summary>
    Task<List<DataRetentionPolicy>> GetActiveRetentionPoliciesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Communication audit event
/// </summary>
public class CommunicationAuditEvent
{
    public AuditEventType EventType { get; set; }
    public Guid UserId { get; set; }
    public Guid? MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public NotificationChannel? Channel { get; set; }
    public Dictionary<string, object> EventData { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public AuditSeverity Severity { get; set; } = AuditSeverity.Low;
    public ComplianceFlag ComplianceFlags { get; set; } = ComplianceFlag.None;
}

/// <summary>
/// Audit record entity
/// </summary>
public class AuditRecord
{
    public Guid Id { get; set; }
    public AuditEventType EventType { get; set; }
    public Guid UserId { get; set; }
    public Guid? MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public NotificationChannel? Channel { get; set; }
    public string EventData { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public AuditSeverity Severity { get; set; }
    public ComplianceFlag ComplianceFlags { get; set; }
    public TimeSpan RetentionPeriod { get; set; }
    public bool IsEncrypted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? DeletedAt { get; set; }
}

/// <summary>
/// Audit trail query
/// </summary>
public class AuditTrailQuery
{
    public Guid? UserId { get; set; }
    public Guid? MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public AuditEventType? EventType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public AuditSeverity? MinSeverity { get; set; }
    public ComplianceFlag? ComplianceFlags { get; set; }
    public bool IncludeSensitiveData { get; set; }
    public Guid RequestedBy { get; set; }
    public int PageSize { get; set; } = 100;
    public int PageNumber { get; set; } = 1;
    public string? SearchTerm { get; set; }
}

/// <summary>
/// Message archive entity
/// </summary>
public class MessageArchive
{
    public Guid Id { get; set; }
    public Guid MessageId { get; set; }
    public string OriginalContent { get; set; } = string.Empty;
    public ArchiveReason ArchiveReason { get; set; }
    public DateTime ArchivedAt { get; set; }
    public DateTime RetentionUntil { get; set; }
    public Guid ArchivedBy { get; set; }
    public ComplianceFlag ComplianceFlags { get; set; }
    public bool IsEncrypted { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Compliance report entity
/// </summary>
public class ComplianceReport
{
    public Guid Id { get; set; }
    public ComplianceReportType ReportType { get; set; }
    public DateTimeOffset Period { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public ComplianceReportStatus Status { get; set; }
    public List<ComplianceReportSection> Sections { get; set; } = new();
    public Dictionary<string, object> Summary { get; set; } = new();
}

/// <summary>
/// Compliance report section
/// </summary>
public class ComplianceReportSection
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public Dictionary<string, object> Metrics { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public ComplianceStatus Status { get; set; } = ComplianceStatus.Compliant;
}

/// <summary>
/// Compliance report request
/// </summary>
public class ComplianceReportRequest
{
    public ComplianceReportType ReportType { get; set; }
    public DateTimeOffset Period { get; set; }
    public Guid RequestedBy { get; set; }
    public List<ComplianceFlag> IncludeFlags { get; set; } = new();
    public bool IncludeSensitiveData { get; set; }
    public string? CustomFilters { get; set; }
}

/// <summary>
/// Dispute resolution data
/// </summary>
public class DisputeResolutionData
{
    public Guid MessageId { get; set; }
    public DisputeContext DisputeContext { get; set; } = new();
    public List<AuditRecord> AuditTrail { get; set; } = new();
    public MessageArchive? MessageArchive { get; set; }
    public List<DeliveryConfirmation> DeliveryConfirmations { get; set; } = new();
    public ComplianceFlag ComplianceFlags { get; set; }
    public DateTime GeneratedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public LegalHoldStatus LegalHoldStatus { get; set; }
}

/// <summary>
/// Dispute context
/// </summary>
public class DisputeContext
{
    public Guid DisputeId { get; set; }
    public DisputeType DisputeType { get; set; }
    public string Description { get; set; } = string.Empty;
    public Guid RequestedBy { get; set; }
    public DateTime RequestedAt { get; set; }
    public DisputePriority Priority { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Data retention policy
/// </summary>
public class DataRetentionPolicy
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RetentionAction Action { get; set; }
    public TimeSpan RetentionPeriod { get; set; }
    public List<AuditEventType> ApplicableEventTypes { get; set; } = new();
    public List<ComplianceFlag> ApplicableFlags { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastExecuted { get; set; }
}

/// <summary>
/// Data retention result
/// </summary>
public class DataRetentionResult
{
    public Guid PolicyId { get; set; }
    public DateTime ExecutedAt { get; set; }
    public int ProcessedRecords { get; set; }
    public int DeletedRecords { get; set; }
    public int ArchivedRecords { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Audit result
/// </summary>
public class AuditResult
{
    public bool IsSuccess { get; set; }
    public Guid? RecordId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static AuditResult Success(Guid recordId)
    {
        return new AuditResult { IsSuccess = true, RecordId = recordId };
    }

    public static AuditResult Failure(string errorMessage)
    {
        return new AuditResult { IsSuccess = false, ErrorMessage = errorMessage };
    }
}

/// <summary>
/// Message details for archiving
/// </summary>
public class MessageDetails
{
    public Guid Id { get; set; }
    public string Content { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public DateTime Timestamp { get; set; }
    public Guid SenderId { get; set; }
    public List<Guid> RecipientIds { get; set; } = new();
}

/// <summary>
/// Delivery confirmation
/// </summary>
public class DeliveryConfirmation
{
    public Guid Id { get; set; }
    public Guid MessageId { get; set; }
    public Guid RecipientId { get; set; }
    public NotificationChannel Channel { get; set; }
    public DeliveryStatus Status { get; set; }
    public DateTime ConfirmedAt { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Audit settings
/// </summary>
public class AuditSettings
{
    public bool EnableAuditLogging { get; set; } = true;
    public bool EncryptSensitiveData { get; set; } = true;
    public bool EncryptArchivedMessages { get; set; } = true;
    public int MessageRetentionDays { get; set; } = 365;
    public int DeliveryRetentionDays { get; set; } = 90;
    public int EmergencyRetentionDays { get; set; } = 2555; // 7 years
    public int LoginRetentionDays { get; set; } = 90;
    public int DefaultRetentionDays { get; set; } = 365;
    public bool AutoApplyRetentionPolicies { get; set; } = true;
    public TimeSpan RetentionPolicyInterval { get; set; } = TimeSpan.FromDays(1);
}

/// <summary>
/// Compliance report query
/// </summary>
public class ComplianceReportQuery
{
    public ComplianceReportType? ReportType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? GeneratedBy { get; set; }
    public ComplianceReportStatus? Status { get; set; }
    public int PageSize { get; set; } = 50;
    public int PageNumber { get; set; } = 1;
}

// Enums
public enum AuditEventType
{
    Unknown,
    MessageSent,
    MessageDelivered,
    MessageRead,
    MessageFailed,
    UserLogin,
    UserLogout,
    ConversationCreated,
    ConversationJoined,
    ConversationLeft,
    EmergencyAlert,
    DataExport,
    DataDeletion,
    ConsentGiven,
    ConsentWithdrawn,
    MessageArchived,
    PolicyApplied
}

public enum AuditSeverity
{
    Low,
    Medium,
    High,
    Critical
}

[Flags]
public enum ComplianceFlag
{
    None = 0,
    GDPR = 1,
    CCPA = 2,
    HIPAA = 4,
    PCI = 8,
    SOX = 16,
    DataLocalization = 32,
    RightToBeForgotten = 64,
    ConsentRequired = 128
}

public enum ArchiveReason
{
    LegalHold,
    ComplianceRequirement,
    DisputeResolution,
    DataRetention,
    UserRequest,
    SystemMaintenance
}

public enum ComplianceReportType
{
    DataRetention,
    MessageDelivery,
    UserConsent,
    SecurityAudit,
    Comprehensive
}

public enum ComplianceReportStatus
{
    Pending,
    InProgress,
    Completed,
    Failed
}

public enum ComplianceStatus
{
    Compliant,
    NonCompliant,
    PartiallyCompliant,
    UnderReview
}

public enum DisputeType
{
    MessageDelivery,
    ContentDispute,
    PrivacyViolation,
    UnauthorizedAccess,
    DataBreach,
    Other
}

public enum DisputePriority
{
    Low,
    Medium,
    High,
    Critical
}

public enum LegalHoldStatus
{
    None,
    Active,
    Released,
    Expired
}

public enum RetentionAction
{
    Delete,
    Archive,
    Anonymize
}

public enum DeliveryStatus
{
    Pending,
    Delivered,
    Failed,
    Read,
    Expired
}

