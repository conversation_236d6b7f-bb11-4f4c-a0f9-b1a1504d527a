using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IEscrowMilestoneRepository
{
    Task<EscrowMilestone?> GetByIdAsync(Guid id);
    Task<List<EscrowMilestone>> GetByEscrowAccountIdAsync(Guid escrowAccountId);
    Task<List<EscrowMilestone>> GetByStatusAsync(string status);
    Task<List<EscrowMilestone>> GetByDueDateRangeAsync(DateTime from, DateTime to);
    Task<List<EscrowMilestone>> GetOverdueMilestonesAsync();
    Task<List<EscrowMilestone>> GetUpcomingMilestonesAsync(int days = 7);
    Task<EscrowMilestone> AddAsync(EscrowMilestone milestone);
    Task UpdateAsync(EscrowMilestone milestone);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<int> GetCompletedMilestoneCountAsync(Guid escrowAccountId);
    Task<decimal> GetTotalMilestoneAmountAsync(Guid escrowAccountId);
}
