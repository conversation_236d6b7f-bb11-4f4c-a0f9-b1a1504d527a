using Microsoft.EntityFrameworkCore;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class FraudAssessmentRepository : IFraudAssessmentRepository
{
    private readonly FinancialPaymentDbContext _context;

    public FraudAssessmentRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<FraudAssessment?> GetByIdAsync(Guid id)
    {
        return await _context.FraudAssessments
            .Include(fa => fa.RuleResults)
            .Include(fa => fa.Alerts)
            .FirstOrDefaultAsync(fa => fa.Id == id);
    }

    public async Task<FraudAssessment?> GetByTransactionIdAsync(Guid transactionId)
    {
        return await _context.FraudAssessments
            .Include(fa => fa.RuleResults)
            .Include(fa => fa.Alerts)
            .FirstOrDefaultAsync(fa => fa.TransactionId == transactionId);
    }

    public async Task<List<FraudAssessment>> GetByRiskLevelAsync(FraudRiskLevel riskLevel, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var query = _context.FraudAssessments
            .Include(fa => fa.RuleResults)
            .Include(fa => fa.Alerts)
            .Where(fa => fa.RiskLevel == riskLevel);

        if (fromDate.HasValue)
        {
            query = query.Where(fa => fa.AssessedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(fa => fa.AssessedAt <= toDate.Value);
        }

        return await query
            .OrderByDescending(fa => fa.AssessedAt)
            .ToListAsync();
    }

    public async Task<List<FraudAssessment>> GetByStatusAsync(FraudAssessmentStatus status)
    {
        return await _context.FraudAssessments
            .Include(fa => fa.RuleResults)
            .Include(fa => fa.Alerts)
            .Where(fa => fa.Status == status)
            .OrderByDescending(fa => fa.AssessedAt)
            .ToListAsync();
    }

    public async Task<List<FraudAssessment>> GetByUserIdAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var query = _context.FraudAssessments
            .Include(fa => fa.RuleResults)
            .Include(fa => fa.Alerts)
            .Where(fa => fa.UserId == userId);

        if (fromDate.HasValue)
        {
            query = query.Where(fa => fa.AssessedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(fa => fa.AssessedAt <= toDate.Value);
        }

        return await query
            .OrderByDescending(fa => fa.AssessedAt)
            .ToListAsync();
    }

    public async Task AddAsync(FraudAssessment assessment)
    {
        await _context.FraudAssessments.AddAsync(assessment);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateAsync(FraudAssessment assessment)
    {
        _context.FraudAssessments.Update(assessment);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var assessment = await _context.FraudAssessments.FindAsync(id);
        if (assessment != null)
        {
            _context.FraudAssessments.Remove(assessment);
            await _context.SaveChangesAsync();
        }
    }
}
