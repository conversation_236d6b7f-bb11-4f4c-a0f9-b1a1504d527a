using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class MilestoneTemplateConfiguration : IEntityTypeConfiguration<MilestoneTemplate>
{
    public void Configure(EntityTypeBuilder<MilestoneTemplate> builder)
    {
        builder.ToTable("MilestoneTemplates");

        builder.HasKey(mt => mt.Id);

        builder.Property(mt => mt.Id)
            .ValueGeneratedOnAdd();

        builder.Property(mt => mt.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(mt => mt.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(mt => mt.TemplateType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(mt => mt.IsActive)
            .IsRequired();

        builder.Property(mt => mt.IsDefault)
            .IsRequired();

        builder.Property(mt => mt.CreatedAt)
            .IsRequired();

        builder.Property(mt => mt.CreatedBy)
            .IsRequired();

        builder.Property(mt => mt.UpdatedAt)
            .IsRequired(false);

        builder.Property(mt => mt.UpdatedBy)
            .IsRequired(false);

        builder.Property(mt => mt.Tags)
            .HasMaxLength(500);

        // Configure Metadata as JSON
        builder.Property(mt => mt.Metadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        // Relationships
        builder.HasMany(mt => mt.Steps)
            .WithOne(ms => ms.MilestoneTemplate)
            .HasForeignKey(ms => ms.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(mt => mt.RfqAssignments)
            .WithOne(rma => rma.MilestoneTemplate)
            .HasForeignKey(rma => rma.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(mt => mt.Name)
            .HasDatabaseName("IX_MilestoneTemplates_Name");

        builder.HasIndex(mt => mt.TemplateType)
            .HasDatabaseName("IX_MilestoneTemplates_TemplateType");

        builder.HasIndex(mt => mt.IsActive)
            .HasDatabaseName("IX_MilestoneTemplates_IsActive");

        builder.HasIndex(mt => mt.IsDefault)
            .HasDatabaseName("IX_MilestoneTemplates_IsDefault");

        builder.HasIndex(mt => mt.CreatedAt)
            .HasDatabaseName("IX_MilestoneTemplates_CreatedAt");

        builder.HasIndex(mt => new { mt.TemplateType, mt.IsActive })
            .HasDatabaseName("IX_MilestoneTemplates_TemplateType_IsActive");
    }
}
