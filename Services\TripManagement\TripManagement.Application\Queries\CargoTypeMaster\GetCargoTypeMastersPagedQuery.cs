using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Queries.CargoTypeMaster;

/// <summary>
/// Query to get paginated cargo type masters with filtering
/// </summary>
public class GetCargoTypeMastersPagedQuery : IRequest<CargoTypeMasterPagedResultDto>
{
    public string? SearchTerm { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public bool? RequiresSpecialHandling { get; set; }
    public bool? IsHazardous { get; set; }
    public bool? RequiresTemperatureControl { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;

    public GetCargoTypeMastersPagedQuery(
        string? searchTerm = null,
        string? category = null,
        bool? isActive = null,
        bool? requiresSpecialHandling = null,
        bool? isHazardous = null,
        bool? requiresTemperatureControl = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        SearchTerm = searchTerm;
        Category = category;
        IsActive = isActive;
        RequiresSpecialHandling = requiresSpecialHandling;
        IsHazardous = isHazardous;
        RequiresTemperatureControl = requiresTemperatureControl;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}
