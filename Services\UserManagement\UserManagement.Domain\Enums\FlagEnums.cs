namespace UserManagement.Domain.Enums;

/// <summary>
/// Types of user flags
/// </summary>
public enum FlagType
{
    Compliance = 0,
    Behavioral = 1,
    Financial = 2,
    Security = 3,
    Performance = 4,
    Legal = 5,
    Fraud = 6,
    Other = 99
}

/// <summary>
/// Severity levels for user flags
/// </summary>
public enum FlagSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Status of user flags
/// </summary>
public enum FlagStatus
{
    Active = 0,
    Resolved = 1,
    Dismissed = 2,
    Escalated = 3,
    UnderReview = 4
}
