using OrderManagement.Domain.Primitives;
using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

public class RfqBid : Entity
{
    public Guid RfqId { get; private set; }
    public Guid BrokerId { get; private set; }
    public string BidNumber { get; private set; }
    public Money QuotedPrice { get; private set; }
    public string? ProposedTerms { get; private set; }
    public DateTime EstimatedPickupDate { get; private set; }
    public DateTime EstimatedDeliveryDate { get; private set; }
    public string? VehicleDetails { get; private set; }
    public string? DriverDetails { get; private set; }
    public string? AdditionalServices { get; private set; }
    public BidStatus Status { get; private set; }
    public DateTime SubmittedAt { get; private set; }
    public DateTime? AcceptedAt { get; private set; }
    public DateTime? RejectedAt { get; private set; }
    public string? RejectionReason { get; private set; }
    public DateTime? AwardedAt { get; private set; }
    public Guid BidderId => BrokerId; // Alias for BrokerId for compatibility
    public string? BidderName { get; private set; }
    public string? Notes { get; private set; }
    public bool IsCounterOffer { get; private set; }
    public Guid? OriginalBidId { get; private set; }

    // Navigation properties
    public RFQ Rfq { get; private set; } = null!;

    private readonly List<BidDocument> _documents = new();
    public IReadOnlyCollection<BidDocument> Documents => _documents.AsReadOnly();

    private RfqBid() { } // EF Constructor

    public RfqBid(
        Guid rfqId,
        Guid brokerId,
        Money quotedPrice,
        DateTime estimatedPickupDate,
        DateTime estimatedDeliveryDate,
        string? bidderName = null,
        string? proposedTerms = null,
        string? vehicleDetails = null,
        string? driverDetails = null,
        string? additionalServices = null,
        string? notes = null,
        bool isCounterOffer = false,
        Guid? originalBidId = null)
    {
        RfqId = rfqId;
        BrokerId = brokerId;
        BidNumber = GenerateBidNumber();
        QuotedPrice = quotedPrice ?? throw new ArgumentNullException(nameof(quotedPrice));
        EstimatedPickupDate = estimatedPickupDate;
        EstimatedDeliveryDate = estimatedDeliveryDate;
        BidderName = bidderName;
        ProposedTerms = proposedTerms;
        VehicleDetails = vehicleDetails;
        DriverDetails = driverDetails;
        AdditionalServices = additionalServices;
        Status = BidStatus.Submitted;
        SubmittedAt = DateTime.UtcNow;
        Notes = notes;
        IsCounterOffer = isCounterOffer;
        OriginalBidId = originalBidId;

        if (estimatedDeliveryDate <= estimatedPickupDate)
            throw new ArgumentException("Delivery date must be after pickup date");
    }

    public void Accept(string? acceptanceNotes = null)
    {
        if (Status != BidStatus.Submitted)
            throw new InvalidOperationException("Only submitted bids can be accepted");

        Status = BidStatus.Accepted;
        AcceptedAt = DateTime.UtcNow;
        if (!string.IsNullOrEmpty(acceptanceNotes))
        {
            Notes = acceptanceNotes;
        }

        // Domain event for bid accepted
        AddDomainEvent(new BidAcceptedDomainEvent(Id, RfqId, BrokerId, QuotedPrice));
    }

    public void Reject(string reason)
    {
        if (Status != BidStatus.Submitted)
            throw new InvalidOperationException("Only submitted bids can be rejected");

        Status = BidStatus.Rejected;
        RejectedAt = DateTime.UtcNow;
        RejectionReason = reason;

        // Domain event for bid rejected
        AddDomainEvent(new BidRejectedDomainEvent(Id, RfqId, BrokerId, reason));
    }

    public void Award()
    {
        if (Status != BidStatus.Submitted)
            throw new InvalidOperationException("Only submitted bids can be awarded");

        Status = BidStatus.Awarded;
        AwardedAt = DateTime.UtcNow;

        // Domain event for bid awarded
        AddDomainEvent(new Events.BidAwardedDomainEvent(Id, RfqId, BrokerId, QuotedPrice));
    }

    public void ResetFromAwarded(string reason)
    {
        if (Status != BidStatus.Awarded)
            throw new InvalidOperationException($"Cannot reset bid from {Status} status");

        Status = BidStatus.Submitted;
        AwardedAt = null;
        RejectionReason = null;
        RejectedAt = null;

        // Domain event for bid reset
        AddDomainEvent(new Events.BidResetDomainEvent(Id, RfqId, BrokerId, reason));
    }

    public void Reactivate(string reason)
    {
        if (Status != BidStatus.Rejected)
            throw new InvalidOperationException($"Cannot reactivate bid from {Status} status");

        Status = BidStatus.Submitted;
        RejectedAt = null;
        RejectionReason = null;

        // Domain event for bid reactivated
        AddDomainEvent(new Events.BidReactivatedDomainEvent(Id, RfqId, BrokerId, reason));
    }

    public void Withdraw(string? withdrawalReason = null)
    {
        if (Status != BidStatus.Submitted)
            throw new InvalidOperationException("Only submitted bids can be withdrawn");

        Status = BidStatus.Withdrawn;
        if (!string.IsNullOrEmpty(withdrawalReason))
        {
            Notes = withdrawalReason;
        }

        // Domain event for bid withdrawn
        AddDomainEvent(new BidWithdrawnDomainEvent(Id, RfqId, BrokerId));
    }

    public void AddDocument(BidDocument document)
    {
        _documents.Add(document);
    }

    private static string GenerateBidNumber()
    {
        return $"BID-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}

// Domain Events
public record BidAcceptedDomainEvent(Guid BidId, Guid RfqId, Guid BrokerId, Money QuotedPrice) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record BidRejectedDomainEvent(Guid BidId, Guid RfqId, Guid BrokerId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
public record BidWithdrawnDomainEvent(Guid BidId, Guid RfqId, Guid BrokerId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}


