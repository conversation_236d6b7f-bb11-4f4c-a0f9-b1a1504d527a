using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Queries.GetOrderById;

public class GetOrderByIdQueryHandler : IRequestHandler<GetOrderByIdQuery, OrderDetailDto?>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetOrderByIdQueryHandler> _logger;

    public GetOrderByIdQueryHandler(
        IOrderRepository orderRepository,
        IMapper mapper,
        ILogger<GetOrderByIdQueryHandler> logger)
    {
        _orderRepository = orderRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<OrderDetailDto?> Handle(GetOrderByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting order {OrderId} for user {UserId} with role {UserRole}", 
            request.OrderId, request.UserId, request.UserRole);

        try
        {
            var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
            
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found", request.OrderId);
                return null;
            }

            // Authorization check - only involved parties can view order details
            if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserRole))
            {
                if (!IsAuthorizedToViewOrder(order, request.UserId.Value, request.UserRole))
                {
                    _logger.LogWarning("User {UserId} with role {UserRole} is not authorized to view order {OrderId}", 
                        request.UserId, request.UserRole, request.OrderId);
                    throw new OrderManagementException("Not authorized to view this order");
                }
            }

            var orderDto = _mapper.Map<OrderDetailDto>(order);
            
            _logger.LogInformation("Successfully retrieved order {OrderId} for user {UserId}", 
                request.OrderId, request.UserId);
            
            return orderDto;
        }
        catch (OrderManagementException)
        {
            throw; // Re-throw business exceptions
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving order {OrderId} for user {UserId}", 
                request.OrderId, request.UserId);
            throw;
        }
    }

    private static bool IsAuthorizedToViewOrder(Domain.Entities.Order order, Guid userId, string userRole)
    {
        return userRole.ToLower() switch
        {
            "transportcompany" => order.TransportCompanyId == userId,
            "broker" => order.BrokerId == userId,
            "carrier" => order.CarrierId == userId,
            "admin" => true, // Admins can view all orders
            _ => false
        };
    }
}
