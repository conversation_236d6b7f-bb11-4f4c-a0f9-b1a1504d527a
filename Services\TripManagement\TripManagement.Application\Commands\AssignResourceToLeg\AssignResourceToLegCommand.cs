using MediatR;

namespace TripManagement.Application.Commands.AssignResourceToLeg;

public class AssignResourceToLegCommand : IRequest<bool>
{
    public Guid TripId { get; set; }
    public int LegNumber { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? VehicleId { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool ValidateAvailability { get; set; } = true;
    public bool NotifyAssignedResources { get; set; } = true;
    public string? AssignmentNotes { get; set; }
}
