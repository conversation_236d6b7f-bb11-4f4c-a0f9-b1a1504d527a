﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using OrderManagement.Domain.ValueObjects;

#nullable disable

namespace OrderManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "order_management");

            migrationBuilder.CreateTable(
                name: "Rfqs",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TransportCompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    RfqNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    LoadDescription = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    LoadType = table.Column<int>(type: "integer", nullable: false),
                    WeightValue = table.Column<decimal>(type: "numeric(18,3)", precision: 18, scale: 3, nullable: false),
                    WeightUnit = table.Column<int>(type: "integer", nullable: false),
                    VolumeValue = table.Column<decimal>(type: "numeric(18,3)", precision: 18, scale: 3, nullable: true),
                    VolumeUnit = table.Column<int>(type: "integer", nullable: true),
                    DimensionLength = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionWidth = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionHeight = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    LoadQuantity = table.Column<int>(type: "integer", nullable: false),
                    PackagingType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RequiredVehicleType = table.Column<int>(type: "integer", nullable: false),
                    RequiresSpecialHandling = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialHandlingInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsHazardous = table.Column<bool>(type: "boolean", nullable: false),
                    HazardousClassification = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RequiresTemperatureControl = table.Column<bool>(type: "boolean", nullable: false),
                    MinTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    MaxTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    TemperatureUnit = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: true),
                    LoadPhotos = table.Column<List<string>>(type: "jsonb", nullable: false),
                    PickupStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PickupCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PickupCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PickupContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    PickupSpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DeliveryStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DeliveryCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DeliveryCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DeliveryContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DeliverySpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PreferredPickupDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PreferredDeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PickupTimeWindowStart = table.Column<TimeSpan>(type: "interval", nullable: true),
                    PickupTimeWindowEnd = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DeliveryTimeWindowStart = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DeliveryTimeWindowEnd = table.Column<TimeSpan>(type: "interval", nullable: true),
                    EstimatedDistanceValue = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    EstimatedDistanceUnit = table.Column<int>(type: "integer", nullable: true),
                    EstimatedDuration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    IntermediateStops = table.Column<List<Address>>(type: "jsonb", nullable: false),
                    RouteNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsFlexiblePickup = table.Column<bool>(type: "boolean", nullable: false),
                    IsFlexibleDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    MinVehicleCapacity = table.Column<int>(type: "integer", nullable: true),
                    RequiresInsurance = table.Column<bool>(type: "boolean", nullable: false),
                    MinInsuranceAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    RequiresLicense = table.Column<bool>(type: "boolean", nullable: false),
                    RequiredLicenses = table.Column<List<string>>(type: "jsonb", nullable: false),
                    RequiresExperience = table.Column<bool>(type: "boolean", nullable: false),
                    MinYearsExperience = table.Column<int>(type: "integer", nullable: true),
                    RequiresBackground = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresTracking = table.Column<bool>(type: "boolean", nullable: false),
                    RequiredMinTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    RequiredMaxTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    RequiredTemperatureUnit = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: true),
                    AdditionalRequirements = table.Column<List<string>>(type: "jsonb", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ClosedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ClosureReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    BudgetAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    BudgetCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    SpecialInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsUrgent = table.Column<bool>(type: "boolean", nullable: false),
                    ContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Rfqs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RfqBids",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfqId = table.Column<Guid>(type: "uuid", nullable: false),
                    BrokerId = table.Column<Guid>(type: "uuid", nullable: false),
                    BidNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    QuotedAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    QuotedCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    ProposedTerms = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EstimatedPickupDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EstimatedDeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    VehicleDetails = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DriverDetails = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AdditionalServices = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Status = table.Column<string>(type: "text", nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AcceptedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsCounterOffer = table.Column<bool>(type: "boolean", nullable: false),
                    OriginalBidId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RfqBids", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RfqBids_Rfqs_RfqId",
                        column: x => x.RfqId,
                        principalSchema: "order_management",
                        principalTable: "Rfqs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RfqDocuments",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RfqId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ContentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    DocumentType = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UploadedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadedBy = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RfqDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RfqDocuments_Rfqs_RfqId",
                        column: x => x.RfqId,
                        principalSchema: "order_management",
                        principalTable: "Rfqs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "BidDocuments",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BidId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ContentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    DocumentType = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UploadedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadedBy = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BidDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BidDocuments_RfqBids_BidId",
                        column: x => x.BidId,
                        principalSchema: "order_management",
                        principalTable: "RfqBids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Orders",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TransportCompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    BrokerId = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: true),
                    RfqId = table.Column<Guid>(type: "uuid", nullable: true),
                    AcceptedBidId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Title = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    LoadDescription = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    LoadType = table.Column<int>(type: "integer", nullable: false),
                    WeightValue = table.Column<decimal>(type: "numeric(18,3)", precision: 18, scale: 3, nullable: false),
                    WeightUnit = table.Column<int>(type: "integer", nullable: false),
                    VolumeValue = table.Column<decimal>(type: "numeric(18,3)", precision: 18, scale: 3, nullable: true),
                    VolumeUnit = table.Column<int>(type: "integer", nullable: true),
                    DimensionLength = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionWidth = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionHeight = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DimensionUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    LoadQuantity = table.Column<int>(type: "integer", nullable: false),
                    PackagingType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RequiredVehicleType = table.Column<int>(type: "integer", nullable: false),
                    RequiresSpecialHandling = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialHandlingInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsHazardous = table.Column<bool>(type: "boolean", nullable: false),
                    HazardousClassification = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RequiresTemperatureControl = table.Column<bool>(type: "boolean", nullable: false),
                    MinTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    MaxTemperature = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    TemperatureUnit = table.Column<string>(type: "character varying(1)", maxLength: 1, nullable: true),
                    LoadPhotos = table.Column<List<string>>(type: "jsonb", nullable: false),
                    PickupStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PickupCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PickupCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PickupContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    PickupSpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DeliveryStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DeliveryCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DeliveryCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DeliveryContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DeliverySpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PreferredPickupDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PreferredDeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PickupTimeWindowStart = table.Column<TimeSpan>(type: "interval", nullable: true),
                    PickupTimeWindowEnd = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DeliveryTimeWindowStart = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DeliveryTimeWindowEnd = table.Column<TimeSpan>(type: "interval", nullable: true),
                    EstimatedDistanceValue = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    EstimatedDistanceUnit = table.Column<int>(type: "integer", nullable: true),
                    EstimatedDuration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    IntermediateStops = table.Column<List<Address>>(type: "jsonb", nullable: false),
                    RouteNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsFlexiblePickup = table.Column<bool>(type: "boolean", nullable: false),
                    IsFlexibleDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    AgreedAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    AgreedCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ConfirmedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancelledAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancellationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CustomerCompanyName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CustomerContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CustomerContactEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CustomerContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CustomerBillingStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CustomerBillingCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CustomerBillingState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CustomerBillingPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CustomerBillingCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CustomerBillingLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    CustomerBillingLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    CustomerBillingContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CustomerBillingContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CustomerBillingSpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CustomerTaxId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CustomerReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PurchaseOrderNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTermsDays = table.Column<int>(type: "integer", nullable: false),
                    PaymentInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RequiresPurchaseOrder = table.Column<bool>(type: "boolean", nullable: false),
                    PreferredInvoiceFormat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    BillingContact = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SpecialInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsUrgent = table.Column<bool>(type: "boolean", nullable: false),
                    PaymentStatus = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Orders_RfqBids_AcceptedBidId",
                        column: x => x.AcceptedBidId,
                        principalSchema: "order_management",
                        principalTable: "RfqBids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Orders_Rfqs_RfqId",
                        column: x => x.RfqId,
                        principalSchema: "order_management",
                        principalTable: "Rfqs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Invoices",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    InvoiceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TaxCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    TotalCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    InvoiceCustomerCompanyName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    InvoiceCustomerContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceCustomerContactEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceCustomerContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    InvoiceBillingStreet = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    InvoiceBillingCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceBillingState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceBillingPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    InvoiceBillingCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceBillingLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    InvoiceBillingLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    InvoiceBillingContactPerson = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    InvoiceBillingContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    InvoiceBillingSpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    InvoiceCustomerTaxId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    InvoiceCustomerReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    InvoicePurchaseOrderNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PaymentTermsDays = table.Column<int>(type: "integer", nullable: false),
                    PaymentInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RequiresPurchaseOrder = table.Column<bool>(type: "boolean", nullable: false),
                    PreferredInvoiceFormat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    BillingContact = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    PaidDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PaymentReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoices_Orders_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "order_management",
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrderDocuments",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ContentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    DocumentType = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UploadedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadedBy = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderDocuments_Orders_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "order_management",
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrderStatusHistories",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ChangedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderStatusHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderStatusHistories_Orders_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "order_management",
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InvoiceLineItems",
                schema: "order_management",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Quantity = table.Column<int>(type: "integer", nullable: false),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    UnitPriceCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TotalPriceAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    TotalPriceCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceLineItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceLineItems_Invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalSchema: "order_management",
                        principalTable: "Invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BidDocuments_BidId",
                schema: "order_management",
                table: "BidDocuments",
                column: "BidId");

            migrationBuilder.CreateIndex(
                name: "IX_BidDocuments_DocumentType",
                schema: "order_management",
                table: "BidDocuments",
                column: "DocumentType");

            migrationBuilder.CreateIndex(
                name: "IX_BidDocuments_UploadedAt",
                schema: "order_management",
                table: "BidDocuments",
                column: "UploadedAt");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceLineItems_InvoiceId",
                schema: "order_management",
                table: "InvoiceLineItems",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_DueDate",
                schema: "order_management",
                table: "Invoices",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_InvoiceDate",
                schema: "order_management",
                table: "Invoices",
                column: "InvoiceDate");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_InvoiceNumber",
                schema: "order_management",
                table: "Invoices",
                column: "InvoiceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_OrderId",
                schema: "order_management",
                table: "Invoices",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_Status",
                schema: "order_management",
                table: "Invoices",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_Status_DueDate",
                schema: "order_management",
                table: "Invoices",
                columns: new[] { "Status", "DueDate" });

            migrationBuilder.CreateIndex(
                name: "IX_OrderDocuments_DocumentType",
                schema: "order_management",
                table: "OrderDocuments",
                column: "DocumentType");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDocuments_OrderId",
                schema: "order_management",
                table: "OrderDocuments",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDocuments_UploadedAt",
                schema: "order_management",
                table: "OrderDocuments",
                column: "UploadedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_AcceptedBidId",
                schema: "order_management",
                table: "Orders",
                column: "AcceptedBidId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_BrokerId",
                schema: "order_management",
                table: "Orders",
                column: "BrokerId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_BrokerId_Status",
                schema: "order_management",
                table: "Orders",
                columns: new[] { "BrokerId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CarrierId",
                schema: "order_management",
                table: "Orders",
                column: "CarrierId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CarrierId_Status",
                schema: "order_management",
                table: "Orders",
                columns: new[] { "CarrierId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CreatedAt",
                schema: "order_management",
                table: "Orders",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderNumber",
                schema: "order_management",
                table: "Orders",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Orders_PaymentStatus",
                schema: "order_management",
                table: "Orders",
                column: "PaymentStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_RfqId",
                schema: "order_management",
                table: "Orders",
                column: "RfqId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Status",
                schema: "order_management",
                table: "Orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Status_CreatedAt",
                schema: "order_management",
                table: "Orders",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_TransportCompanyId",
                schema: "order_management",
                table: "Orders",
                column: "TransportCompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_TransportCompanyId_Status",
                schema: "order_management",
                table: "Orders",
                columns: new[] { "TransportCompanyId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusHistories_ChangedAt",
                schema: "order_management",
                table: "OrderStatusHistories",
                column: "ChangedAt");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusHistories_OrderId",
                schema: "order_management",
                table: "OrderStatusHistories",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderStatusHistories_OrderId_ChangedAt",
                schema: "order_management",
                table: "OrderStatusHistories",
                columns: new[] { "OrderId", "ChangedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_BidNumber",
                schema: "order_management",
                table: "RfqBids",
                column: "BidNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_BrokerId",
                schema: "order_management",
                table: "RfqBids",
                column: "BrokerId");

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_BrokerId_Status",
                schema: "order_management",
                table: "RfqBids",
                columns: new[] { "BrokerId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_RfqId",
                schema: "order_management",
                table: "RfqBids",
                column: "RfqId");

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_RfqId_BrokerId",
                schema: "order_management",
                table: "RfqBids",
                columns: new[] { "RfqId", "BrokerId" });

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_RfqId_Status",
                schema: "order_management",
                table: "RfqBids",
                columns: new[] { "RfqId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_Status",
                schema: "order_management",
                table: "RfqBids",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_RfqBids_SubmittedAt",
                schema: "order_management",
                table: "RfqBids",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_RfqDocuments_DocumentType",
                schema: "order_management",
                table: "RfqDocuments",
                column: "DocumentType");

            migrationBuilder.CreateIndex(
                name: "IX_RfqDocuments_RfqId",
                schema: "order_management",
                table: "RfqDocuments",
                column: "RfqId");

            migrationBuilder.CreateIndex(
                name: "IX_RfqDocuments_UploadedAt",
                schema: "order_management",
                table: "RfqDocuments",
                column: "UploadedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_CreatedAt",
                schema: "order_management",
                table: "Rfqs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_ExpiresAt",
                schema: "order_management",
                table: "Rfqs",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_RfqNumber",
                schema: "order_management",
                table: "Rfqs",
                column: "RfqNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_Status",
                schema: "order_management",
                table: "Rfqs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_Status_CreatedAt",
                schema: "order_management",
                table: "Rfqs",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_TransportCompanyId",
                schema: "order_management",
                table: "Rfqs",
                column: "TransportCompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_TransportCompanyId_Status",
                schema: "order_management",
                table: "Rfqs",
                columns: new[] { "TransportCompanyId", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BidDocuments",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "InvoiceLineItems",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "OrderDocuments",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "OrderStatusHistories",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "RfqDocuments",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "Invoices",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "Orders",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "RfqBids",
                schema: "order_management");

            migrationBuilder.DropTable(
                name: "Rfqs",
                schema: "order_management");
        }
    }
}
