using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.BackgroundServices;

/// <summary>
/// Background service for processing scheduled reports
/// </summary>
public class ReportSchedulingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ReportSchedulingService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromMinutes(10);

    public ReportSchedulingService(
        IServiceProvider serviceProvider,
        ILogger<ReportSchedulingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Report Scheduling Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessScheduledReportsAsync(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Report Scheduling Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Report Scheduling Service");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Report Scheduling Service stopped");
    }

    private async Task ProcessScheduledReportsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var reportRepository = scope.ServiceProvider.GetRequiredService<IReportRepository>();
        var reportGenerationService = scope.ServiceProvider.GetRequiredService<IReportGenerationService>();
        var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

        try
        {
            _logger.LogDebug("Starting report scheduling cycle");

            // Get reports that are due for execution
            var dueReports = await GetDueReportsAsync(reportRepository, cancellationToken);

            if (!dueReports.Any())
            {
                _logger.LogDebug("No reports due for execution");
                return;
            }

            _logger.LogInformation("Processing {ReportCount} scheduled reports", dueReports.Count());

            foreach (var report in dueReports)
            {
                try
                {
                    await ProcessScheduledReportAsync(report, reportGenerationService, notificationService, cancellationToken);
                    await UpdateReportScheduleAsync(reportRepository, report, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing scheduled report {ReportId}", report.Id);
                    await MarkReportAsFailed(reportRepository, report, ex.Message, cancellationToken);
                }
            }

            _logger.LogInformation("Completed processing {ReportCount} scheduled reports", dueReports.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during report scheduling");
            throw;
        }
    }

    private async Task<List<Report>> GetDueReportsAsync(
        IReportRepository reportRepository,
        CancellationToken cancellationToken)
    {
        try
        {
            var currentTime = DateTime.UtcNow;
            var reports = await reportRepository.GetScheduledReportsAsync(cancellationToken);

            var dueReports = new List<Report>();

            foreach (var report in reports)
            {
                if (IsReportDue(report, currentTime))
                {
                    dueReports.Add(report);
                }
            }

            return dueReports;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting due reports");
            throw;
        }
    }

    private bool IsReportDue(Report report, DateTime currentTime)
    {
        // This is a simplified implementation
        // In a real scenario, you would have more sophisticated scheduling logic
        // based on cron expressions, recurring patterns, etc.

        if (report.Status != ReportStatus.Scheduled)
            return false;

        // Check if report has schedule configuration
        if (!report.Properties.ContainsKey("schedule"))
            return false;

        var scheduleConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(report.Properties["schedule"].ToString() ?? "{}");
        if (scheduleConfig == null)
            return false;

        // Get last execution time
        var lastExecuted = report.Properties.ContainsKey("lastExecuted")
            ? Convert.ToDateTime(report.Properties["lastExecuted"])
            : DateTime.MinValue;

        // Check schedule type
        var scheduleType = scheduleConfig.ContainsKey("type")
            ? scheduleConfig["type"].ToString()
            : "once";

        return scheduleType switch
        {
            "daily" => ShouldExecuteDaily(lastExecuted, currentTime, scheduleConfig),
            "weekly" => ShouldExecuteWeekly(lastExecuted, currentTime, scheduleConfig),
            "monthly" => ShouldExecuteMonthly(lastExecuted, currentTime, scheduleConfig),
            "hourly" => ShouldExecuteHourly(lastExecuted, currentTime, scheduleConfig),
            "once" => ShouldExecuteOnce(lastExecuted, currentTime, scheduleConfig),
            _ => false
        };
    }

    private bool ShouldExecuteDaily(DateTime lastExecuted, DateTime currentTime, Dictionary<string, object> config)
    {
        if (lastExecuted.Date >= currentTime.Date)
            return false;

        var hour = config.ContainsKey("hour") ? Convert.ToInt32(config["hour"]) : 0;
        var minute = config.ContainsKey("minute") ? Convert.ToInt32(config["minute"]) : 0;

        var scheduledTime = currentTime.Date.AddHours(hour).AddMinutes(minute);
        return currentTime >= scheduledTime;
    }

    private bool ShouldExecuteWeekly(DateTime lastExecuted, DateTime currentTime, Dictionary<string, object> config)
    {
        var daysSinceLastExecution = (currentTime.Date - lastExecuted.Date).Days;
        if (daysSinceLastExecution < 7)
            return false;

        var dayOfWeek = config.ContainsKey("dayOfWeek")
            ? (DayOfWeek)Convert.ToInt32(config["dayOfWeek"])
            : DayOfWeek.Monday;

        return currentTime.DayOfWeek == dayOfWeek;
    }

    private bool ShouldExecuteMonthly(DateTime lastExecuted, DateTime currentTime, Dictionary<string, object> config)
    {
        if (lastExecuted.Month == currentTime.Month && lastExecuted.Year == currentTime.Year)
            return false;

        var dayOfMonth = config.ContainsKey("dayOfMonth") ? Convert.ToInt32(config["dayOfMonth"]) : 1;
        return currentTime.Day >= dayOfMonth;
    }

    private bool ShouldExecuteHourly(DateTime lastExecuted, DateTime currentTime, Dictionary<string, object> config)
    {
        var hoursSinceLastExecution = (currentTime - lastExecuted).TotalHours;
        var interval = config.ContainsKey("interval") ? Convert.ToInt32(config["interval"]) : 1;

        return hoursSinceLastExecution >= interval;
    }

    private bool ShouldExecuteOnce(DateTime lastExecuted, DateTime currentTime, Dictionary<string, object> config)
    {
        if (lastExecuted != DateTime.MinValue)
            return false; // Already executed

        var scheduledTime = config.ContainsKey("scheduledTime")
            ? Convert.ToDateTime(config["scheduledTime"])
            : DateTime.MinValue;

        return currentTime >= scheduledTime;
    }

    private async Task ProcessScheduledReportAsync(
        Report report,
        IReportGenerationService reportGenerationService,
        INotificationService notificationService,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Generating scheduled report {ReportId}: {ReportName}", report.Id, report.Name);

            // Prepare report parameters
            var parameters = GetReportParameters(report);

            // Generate the report
            var filePath = await reportGenerationService.GenerateScheduledReportAsync(
                report.Id, parameters, cancellationToken);

            // Get notification recipients
            var recipients = GetReportRecipients(report);

            if (recipients.Any())
            {
                // Send notification about report completion
                await notificationService.SendReportNotificationAsync(
                    report.Id, report.Name, filePath, recipients, cancellationToken);
            }

            _logger.LogInformation("Successfully generated and notified scheduled report {ReportId}", report.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled report {ReportId}", report.Id);
            throw;
        }
    }

    private Dictionary<string, object> GetReportParameters(Report report)
    {
        var parameters = new Dictionary<string, object>();

        // Add default parameters
        parameters["GeneratedAt"] = DateTime.UtcNow;
        parameters["ReportId"] = report.Id;
        parameters["ReportName"] = report.Name;

        // Add custom parameters from report configuration
        if (report.Properties.ContainsKey("parameters"))
        {
            var customParams = JsonSerializer.Deserialize<Dictionary<string, object>>(report.Properties["parameters"].ToString() ?? "{}");
            if (customParams != null)
            {
                foreach (var param in customParams)
                {
                    parameters[param.Key] = param.Value;
                }
            }
        }

        return parameters;
    }

    private List<string> GetReportRecipients(Report report)
    {
        var recipients = new List<string>();

        // Get recipients from report configuration
        if (report.Properties.ContainsKey("recipients"))
        {
            var reportRecipients = report.Properties["recipients"].ToString();
            if (!string.IsNullOrEmpty(reportRecipients))
            {
                recipients.AddRange(reportRecipients.Split(',').Select(r => r.Trim()));
            }
        }

        // Add default recipients if none specified
        if (!recipients.Any())
        {
            recipients.Add("<EMAIL>");
        }

        return recipients.Distinct().ToList();
    }

    private async Task UpdateReportScheduleAsync(
        IReportRepository reportRepository,
        Report report,
        CancellationToken cancellationToken)
    {
        try
        {
            // Update last execution time
            report.Properties["lastExecuted"] = DateTime.UtcNow.ToString("O");

            // Update next execution time if it's a recurring report
            if (report.Properties.ContainsKey("schedule"))
            {
                var scheduleConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(report.Properties["schedule"].ToString() ?? "{}");
                if (scheduleConfig != null)
                {
                    var nextExecution = CalculateNextExecution(DateTime.UtcNow, scheduleConfig);
                    if (nextExecution.HasValue)
                    {
                        report.Properties["nextExecution"] = nextExecution.Value.ToString("O");
                    }
                    else
                    {
                        // One-time report, mark as completed
                        report.UpdateStatus(ReportStatus.Completed);
                    }
                }
            }
            else
            {
                // No schedule configuration, mark as completed
                report.UpdateStatus(ReportStatus.Completed);
            }

            await reportRepository.UpdateAsync(report, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report schedule for report {ReportId}", report.Id);
            throw;
        }
    }

    private DateTime? CalculateNextExecution(DateTime currentTime, Dictionary<string, object> scheduleConfig)
    {
        var scheduleType = scheduleConfig.ContainsKey("type")
            ? scheduleConfig["type"].ToString()
            : "once";

        return scheduleType switch
        {
            "daily" => currentTime.AddDays(1),
            "weekly" => currentTime.AddDays(7),
            "monthly" => currentTime.AddMonths(1),
            "hourly" => currentTime.AddHours(scheduleConfig.ContainsKey("interval")
                ? Convert.ToInt32(scheduleConfig["interval"]) : 1),
            "once" => null, // One-time execution
            _ => null
        };
    }

    private async Task MarkReportAsFailed(
        IReportRepository reportRepository,
        Report report,
        string errorMessage,
        CancellationToken cancellationToken)
    {
        try
        {
            report.UpdateStatus(ReportStatus.Failed);
            report.Properties["lastError"] = errorMessage;
            report.Properties["lastErrorAt"] = DateTime.UtcNow.ToString("O");

            await reportRepository.UpdateAsync(report, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking report {ReportId} as failed", report.Id);
        }
    }
}
