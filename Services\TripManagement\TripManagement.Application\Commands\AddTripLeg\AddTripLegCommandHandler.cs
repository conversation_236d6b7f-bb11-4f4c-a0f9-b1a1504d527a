using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Application.Commands.AddTripLeg;

public class AddTripLegCommandHandler : IRequestHandler<AddTripLegCommand, Guid>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<AddTripLegCommandHandler> _logger;

    public AddTripLegCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<AddTripLegCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Guid> Handle(AddTripLegCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding leg {LegNumber} to trip {TripId} by user {UserId}", 
            request.LegNumber, request.TripId, request.RequestingUserId);

        try
        {
            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Validate user has permission to modify this trip
            if (trip.CarrierId != request.RequestingUserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to modify trip {TripId}", 
                    request.RequestingUserId, request.TripId);
                throw new UnauthorizedAccessException("You do not have permission to modify this trip");
            }

            // Validate trip is in a state that allows adding legs
            if (trip.Status != Domain.Enums.TripStatus.Created && trip.Status != Domain.Enums.TripStatus.Assigned)
            {
                _logger.LogWarning("Cannot add leg to trip {TripId} with status {Status}", 
                    request.TripId, trip.Status);
                throw new InvalidOperationException($"Cannot add leg to trip with status {trip.Status}");
            }

            // Convert route DTO to domain value object
            var route = new Route(
                new Location(
                    request.Route.StartLocation.Latitude,
                    request.Route.StartLocation.Longitude,
                    request.Route.StartLocation.Address),
                new Location(
                    request.Route.EndLocation.Latitude,
                    request.Route.EndLocation.Longitude,
                    request.Route.EndLocation.Address),
                request.Route.Waypoints?.Select(w => new Location(w.Latitude, w.Longitude, w.Address)).ToList(),
                request.Route.EstimatedDistanceKm,
                request.Route.EstimatedDuration,
                request.Route.RouteInstructions);

            // Add the leg to the trip
            var tripLeg = trip.AddLeg(
                request.LegNumber,
                request.LegName,
                route,
                request.EstimatedStartTime,
                request.EstimatedEndTime,
                request.Description,
                request.SpecialInstructions,
                request.IsRequired,
                request.Priority);

            // Add stops to the leg
            foreach (var stopRequest in request.Stops)
            {
                var stopLocation = new Location(
                    stopRequest.Location.Latitude,
                    stopRequest.Location.Longitude,
                    stopRequest.Location.Address);

                var stop = new TripLegStop(
                    tripLeg.Id,
                    stopRequest.StopType,
                    stopRequest.SequenceNumber,
                    stopLocation,
                    stopRequest.IsRequired,
                    stopRequest.ContactName,
                    stopRequest.ContactPhone,
                    stopRequest.ScheduledArrival,
                    stopRequest.ScheduledDeparture,
                    stopRequest.Instructions);

                tripLeg.AddStop(stop);
            }

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully added leg {LegId} (number {LegNumber}) to trip {TripId}", 
                tripLeg.Id, request.LegNumber, request.TripId);

            return tripLeg.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding leg {LegNumber} to trip {TripId}", 
                request.LegNumber, request.TripId);
            throw;
        }
    }
}
