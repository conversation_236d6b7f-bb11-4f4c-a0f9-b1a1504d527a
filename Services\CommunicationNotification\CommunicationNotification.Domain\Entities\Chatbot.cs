using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// AI-powered chatbot entity for conversational interactions
/// </summary>
public class Chatbot : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ChatbotType Type { get; private set; }
    public ChatbotStatus Status { get; private set; }
    public string DefaultLanguage { get; private set; }
    public List<string> SupportedLanguages { get; private set; }
    public string WelcomeMessage { get; private set; }
    public string FallbackMessage { get; private set; }
    public decimal ConfidenceThreshold { get; private set; }
    public int MaxConversationTurns { get; private set; }
    public TimeSpan SessionTimeout { get; private set; }
    public List<ChatbotIntent> Intents { get; private set; }
    public List<ChatbotEntity> Entities { get; private set; }
    public ChatbotConfiguration Configuration { get; private set; }
    public ChatbotAnalytics Analytics { get; private set; }
    public DateTime? LastTrainedAt { get; private set; }
    public Guid CreatedByUserId { get; private set; }

    private Chatbot()
    {
        Name = string.Empty;
        Description = string.Empty;
        DefaultLanguage = "en-US";
        SupportedLanguages = new List<string>();
        WelcomeMessage = string.Empty;
        FallbackMessage = string.Empty;
        Intents = new List<ChatbotIntent>();
        Entities = new List<ChatbotEntity>();
        Configuration = ChatbotConfiguration.Default();
        Analytics = ChatbotAnalytics.Empty();
    }

    public Chatbot(
        string name,
        string description,
        ChatbotType type,
        string defaultLanguage,
        string welcomeMessage,
        Guid createdByUserId,
        decimal confidenceThreshold = 0.7m,
        int maxConversationTurns = 50,
        TimeSpan? sessionTimeout = null,
        string? fallbackMessage = null,
        List<string>? supportedLanguages = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Chatbot name cannot be empty", nameof(name));
        if (string.IsNullOrWhiteSpace(welcomeMessage))
            throw new ArgumentException("Welcome message cannot be empty", nameof(welcomeMessage));
        if (createdByUserId == Guid.Empty)
            throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

        Name = name;
        Description = description;
        Type = type;
        Status = ChatbotStatus.Draft;
        DefaultLanguage = defaultLanguage;
        WelcomeMessage = welcomeMessage;
        FallbackMessage = fallbackMessage ?? "I'm sorry, I didn't understand that. Could you please rephrase?";
        ConfidenceThreshold = confidenceThreshold;
        MaxConversationTurns = maxConversationTurns;
        SessionTimeout = sessionTimeout ?? TimeSpan.FromMinutes(30);
        SupportedLanguages = supportedLanguages ?? new List<string> { defaultLanguage };
        Intents = new List<ChatbotIntent>();
        Entities = new List<ChatbotEntity>();
        Configuration = ChatbotConfiguration.Default();
        Analytics = ChatbotAnalytics.Empty();
        CreatedByUserId = createdByUserId;
    }

    public static Chatbot Create(
        string name,
        string description,
        ChatbotType type,
        string defaultLanguage,
        string welcomeMessage,
        Guid createdByUserId,
        decimal confidenceThreshold = 0.7m,
        int maxConversationTurns = 50,
        TimeSpan? sessionTimeout = null,
        string? fallbackMessage = null,
        List<string>? supportedLanguages = null)
    {
        return new Chatbot(name, description, type, defaultLanguage, welcomeMessage, createdByUserId,
            confidenceThreshold, maxConversationTurns, sessionTimeout, fallbackMessage, supportedLanguages);
    }

    public void AddIntent(ChatbotIntent intent)
    {
        if (intent == null)
            throw new ArgumentNullException(nameof(intent));

        if (Status != ChatbotStatus.Draft)
            throw new InvalidOperationException("Cannot modify intents when chatbot is not in draft status");

        if (Intents.Any(i => i.Name.Equals(intent.Name, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Intent '{intent.Name}' already exists");

        Intents.Add(intent);
    }

    public void RemoveIntent(string intentName)
    {
        if (Status != ChatbotStatus.Draft)
            throw new InvalidOperationException("Cannot modify intents when chatbot is not in draft status");

        var intent = Intents.FirstOrDefault(i => i.Name.Equals(intentName, StringComparison.OrdinalIgnoreCase));
        if (intent != null)
        {
            Intents.Remove(intent);
        }
    }

    public void AddEntity(ChatbotEntity entity)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        if (Status != ChatbotStatus.Draft)
            throw new InvalidOperationException("Cannot modify entities when chatbot is not in draft status");

        if (Entities.Any(e => e.Name.Equals(entity.Name, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Entity '{entity.Name}' already exists");

        Entities.Add(entity);
    }

    public void RemoveEntity(string entityName)
    {
        if (Status != ChatbotStatus.Draft)
            throw new InvalidOperationException("Cannot modify entities when chatbot is not in draft status");

        var entity = Entities.FirstOrDefault(e => e.Name.Equals(entityName, StringComparison.OrdinalIgnoreCase));
        if (entity != null)
        {
            Entities.Remove(entity);
        }
    }

    public void Train()
    {
        if (Status != ChatbotStatus.Draft && Status != ChatbotStatus.Inactive)
            throw new InvalidOperationException($"Cannot train chatbot in {Status} status");

        if (!Intents.Any())
            throw new InvalidOperationException("Cannot train chatbot without intents");

        Status = ChatbotStatus.Training;
        LastTrainedAt = DateTime.UtcNow;
    }

    public void CompleteTraining()
    {
        if (Status != ChatbotStatus.Training)
            throw new InvalidOperationException($"Cannot complete training when status is {Status}");

        Status = ChatbotStatus.Trained;
    }

    public void Activate()
    {
        if (Status != ChatbotStatus.Trained)
            throw new InvalidOperationException($"Cannot activate chatbot in {Status} status");

        Status = ChatbotStatus.Active;
    }

    public void Deactivate()
    {
        if (Status != ChatbotStatus.Active)
            throw new InvalidOperationException($"Cannot deactivate chatbot in {Status} status");

        Status = ChatbotStatus.Inactive;
    }

    public void UpdateConfiguration(ChatbotConfiguration configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public void UpdateAnalytics(ChatbotAnalytics analytics)
    {
        Analytics = analytics ?? throw new ArgumentNullException(nameof(analytics));
    }

    public ChatbotIntent? GetIntent(string intentName)
    {
        return Intents.FirstOrDefault(i => i.Name.Equals(intentName, StringComparison.OrdinalIgnoreCase));
    }

    public ChatbotEntity? GetEntity(string entityName)
    {
        return Entities.FirstOrDefault(e => e.Name.Equals(entityName, StringComparison.OrdinalIgnoreCase));
    }

    public bool IsLanguageSupported(string language)
    {
        return SupportedLanguages.Contains(language, StringComparer.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Chatbot intent representing a user's intention
/// </summary>
public class ChatbotIntent
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<string> TrainingPhrases { get; private set; }
    public List<ChatbotResponse> Responses { get; private set; }
    public List<string> RequiredEntities { get; private set; }
    public List<ChatbotAction> Actions { get; private set; }
    public bool IsEnabled { get; private set; }
    public Dictionary<string, string> Parameters { get; private set; }

    private ChatbotIntent()
    {
        Name = string.Empty;
        Description = string.Empty;
        TrainingPhrases = new List<string>();
        Responses = new List<ChatbotResponse>();
        RequiredEntities = new List<string>();
        Actions = new List<ChatbotAction>();
        Parameters = new Dictionary<string, string>();
    }

    public ChatbotIntent(
        string name,
        string description,
        List<string> trainingPhrases,
        List<ChatbotResponse> responses,
        List<string>? requiredEntities = null,
        List<ChatbotAction>? actions = null,
        bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Intent name cannot be empty", nameof(name));
        if (!trainingPhrases.Any())
            throw new ArgumentException("Intent must have at least one training phrase", nameof(trainingPhrases));
        if (!responses.Any())
            throw new ArgumentException("Intent must have at least one response", nameof(responses));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        TrainingPhrases = trainingPhrases;
        Responses = responses;
        RequiredEntities = requiredEntities ?? new List<string>();
        Actions = actions ?? new List<ChatbotAction>();
        IsEnabled = isEnabled;
        Parameters = new Dictionary<string, string>();
    }

    public void AddTrainingPhrase(string phrase)
    {
        if (string.IsNullOrWhiteSpace(phrase))
            throw new ArgumentException("Training phrase cannot be empty", nameof(phrase));

        if (!TrainingPhrases.Contains(phrase, StringComparer.OrdinalIgnoreCase))
        {
            TrainingPhrases.Add(phrase);
        }
    }

    public void AddResponse(ChatbotResponse response)
    {
        if (response == null)
            throw new ArgumentNullException(nameof(response));

        Responses.Add(response);
    }

    public void AddAction(ChatbotAction action)
    {
        if (action == null)
            throw new ArgumentNullException(nameof(action));

        Actions.Add(action);
    }

    public void Enable()
    {
        IsEnabled = true;
    }

    public void Disable()
    {
        IsEnabled = false;
    }
}

/// <summary>
/// Chatbot entity for extracting structured data from user input
/// </summary>
public class ChatbotEntity
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public EntityType Type { get; private set; }
    public List<string> Synonyms { get; private set; }
    public string? RegexPattern { get; private set; }
    public bool IsRequired { get; private set; }
    public string? DefaultValue { get; private set; }
    public Dictionary<string, string> Metadata { get; private set; }

    private ChatbotEntity()
    {
        Name = string.Empty;
        Description = string.Empty;
        Synonyms = new List<string>();
        Metadata = new Dictionary<string, string>();
    }

    public ChatbotEntity(
        string name,
        string description,
        EntityType type,
        List<string>? synonyms = null,
        string? regexPattern = null,
        bool isRequired = false,
        string? defaultValue = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Entity name cannot be empty", nameof(name));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        Type = type;
        Synonyms = synonyms ?? new List<string>();
        RegexPattern = regexPattern;
        IsRequired = isRequired;
        DefaultValue = defaultValue;
        Metadata = new Dictionary<string, string>();
    }

    public void AddSynonym(string synonym)
    {
        if (string.IsNullOrWhiteSpace(synonym))
            throw new ArgumentException("Synonym cannot be empty", nameof(synonym));

        if (!Synonyms.Contains(synonym, StringComparer.OrdinalIgnoreCase))
        {
            Synonyms.Add(synonym);
        }
    }
}

/// <summary>
/// Chatbot response with multiple variations
/// </summary>
public class ChatbotResponse
{
    public Guid Id { get; private set; }
    public string Text { get; private set; }
    public string Language { get; private set; }
    public ResponseType Type { get; private set; }
    public List<string> Variations { get; private set; }
    public Dictionary<string, string> Parameters { get; private set; }

    private ChatbotResponse()
    {
        Text = string.Empty;
        Language = string.Empty;
        Variations = new List<string>();
        Parameters = new Dictionary<string, string>();
    }

    public ChatbotResponse(
        string text,
        string language,
        ResponseType type = ResponseType.Text,
        List<string>? variations = null)
    {
        if (string.IsNullOrWhiteSpace(text))
            throw new ArgumentException("Response text cannot be empty", nameof(text));

        Id = Guid.NewGuid();
        Text = text;
        Language = language;
        Type = type;
        Variations = variations ?? new List<string>();
        Parameters = new Dictionary<string, string>();
    }

    public string GetRandomVariation()
    {
        if (!Variations.Any())
            return Text;

        var allResponses = new List<string> { Text };
        allResponses.AddRange(Variations);

        var random = new Random();
        return allResponses[random.Next(allResponses.Count)];
    }
}

/// <summary>
/// Chatbot action to be executed when intent is matched
/// </summary>
public class ChatbotAction
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public ActionType Type { get; private set; }
    public string Configuration { get; private set; }
    public Dictionary<string, string> Parameters { get; private set; }
    public bool IsEnabled { get; private set; }

    private ChatbotAction()
    {
        Name = string.Empty;
        Configuration = string.Empty;
        Parameters = new Dictionary<string, string>();
    }

    public ChatbotAction(
        string name,
        ActionType type,
        string configuration,
        Dictionary<string, string>? parameters = null,
        bool isEnabled = true)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Action name cannot be empty", nameof(name));

        Id = Guid.NewGuid();
        Name = name;
        Type = type;
        Configuration = configuration;
        Parameters = parameters ?? new Dictionary<string, string>();
        IsEnabled = isEnabled;
    }
}

/// <summary>
/// Chatbot status enumeration
/// </summary>
public enum ChatbotStatus
{
    Draft = 1,
    Training = 2,
    Trained = 3,
    Active = 4,
    Inactive = 5,
    Error = 6
}

/// <summary>
/// Chatbot type enumeration
/// </summary>
public enum ChatbotType
{
    CustomerSupport = 1,
    TripAssistant = 2,
    BookingAgent = 3,
    InformationBot = 4,
    GeneralPurpose = 5
}

/// <summary>
/// Entity type enumeration
/// </summary>
public enum EntityType
{
    Text = 1,
    Number = 2,
    Date = 3,
    Time = 4,
    Location = 5,
    PhoneNumber = 6,
    Email = 7,
    Custom = 8
}

/// <summary>
/// Response type enumeration
/// </summary>
public enum ResponseType
{
    Text = 1,
    QuickReply = 2,
    Card = 3,
    Carousel = 4,
    Image = 5,
    Audio = 6,
    Video = 7,
    File = 8
}

/// <summary>
/// Action type enumeration
/// </summary>
public enum ActionType
{
    SendMessage = 1,
    TransferToAgent = 2,
    CreateTicket = 3,
    BookTrip = 4,
    CheckStatus = 5,
    SendEmail = 6,
    SendSMS = 7,
    CallWebhook = 8,
    SetVariable = 9,
    EndConversation = 10
}


