using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;

namespace DataStorage.Infrastructure.Services;

public class LocalFileStorageProvider : IStorageProviderService
{
    private readonly ILogger<LocalFileStorageProvider> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _basePath;

    public StorageProvider Provider => StorageProvider.LocalFileSystem;

    public LocalFileStorageProvider(ILogger<LocalFileStorageProvider> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _basePath = _configuration.GetValue<string>("Storage:LocalFileSystem:BasePath") ?? "uploads";
        
        // Ensure base directory exists
        Directory.CreateDirectory(_basePath);
    }

    public async Task<StorageLocation> UploadFileAsync(
        Stream fileStream,
        string fileName,
        string contentType,
        Dictionary<string, string>? metadata,
        CancellationToken cancellationToken)
    {
        try
        {
            var containerName = GetContainerName(contentType);
            var containerPath = Path.Combine(_basePath, containerName);
            Directory.CreateDirectory(containerPath);

            var uniqueFileName = GenerateUniqueFileName(fileName);
            var filePath = Path.Combine(containerName, uniqueFileName);
            var fullPath = Path.Combine(_basePath, filePath);

            using var fileStreamOut = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
            await fileStream.CopyToAsync(fileStreamOut, cancellationToken);

            _logger.LogDebug("File uploaded to local storage: {FilePath}", fullPath);

            return new StorageLocation(Provider, containerName, uniqueFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file {FileName} to local storage", fileName);
            throw new FileUploadException(fileName, ex);
        }
    }

    public async Task<Stream> DownloadFileAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        try
        {
            var fullPath = GetFullPath(location);
            
            if (!File.Exists(fullPath))
                throw new FileNotFoundException(fullPath);

            return new FileStream(fullPath, FileMode.Open, FileAccess.Read);
        }
        catch (Exception ex) when (!(ex is FileNotFoundException))
        {
            _logger.LogError(ex, "Failed to download file from {Location}", location.GetFullPath());
            throw new FileDownloadException(location.GetFullPath(), ex);
        }
    }

    public async Task<string> GetDownloadUrlAsync(StorageLocation location, TimeSpan? expiry, CancellationToken cancellationToken)
    {
        // For local file system, return a file:// URL or a web-accessible URL if configured
        var baseUrl = _configuration.GetValue<string>("Storage:LocalFileSystem:BaseUrl");
        if (!string.IsNullOrWhiteSpace(baseUrl))
        {
            return $"{baseUrl.TrimEnd('/')}/{location.ContainerName}/{location.FilePath}";
        }

        return $"file://{GetFullPath(location)}";
    }

    public async Task<bool> FileExistsAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        var fullPath = GetFullPath(location);
        return File.Exists(fullPath);
    }

    public async Task DeleteFileAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        try
        {
            var fullPath = GetFullPath(location);
            
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogDebug("File deleted from local storage: {FilePath}", fullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete file from {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to delete file from {location.GetFullPath()}", ex);
        }
    }

    public async Task<StorageLocation> CopyFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken)
    {
        try
        {
            var sourcePath = GetFullPath(source);
            var destPath = GetFullPath(destination);
            
            Directory.CreateDirectory(Path.GetDirectoryName(destPath)!);
            File.Copy(sourcePath, destPath, overwrite: true);
            
            _logger.LogDebug("File copied from {Source} to {Destination}", sourcePath, destPath);
            
            return destination;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to copy file from {Source} to {Destination}", 
                source.GetFullPath(), destination.GetFullPath());
            throw new FileStorageException($"Failed to copy file", ex);
        }
    }

    public async Task<FileMetadata> GetFileMetadataAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        try
        {
            var fullPath = GetFullPath(location);
            var fileInfo = new FileInfo(fullPath);
            
            if (!fileInfo.Exists)
                throw new FileNotFoundException(fullPath);

            var contentType = GetContentType(fileInfo.Extension);
            
            return new FileMetadata(
                fileInfo.Name,
                contentType,
                fileInfo.Length);
        }
        catch (Exception ex) when (!(ex is FileNotFoundException))
        {
            _logger.LogError(ex, "Failed to get file metadata for {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to get file metadata", ex);
        }
    }

    public async Task UpdateFileMetadataAsync(StorageLocation location, Dictionary<string, string> metadata, CancellationToken cancellationToken)
    {
        // Local file system doesn't support custom metadata
        // This is a no-op for local storage
        _logger.LogDebug("Metadata update requested for {Location} but local storage doesn't support custom metadata", 
            location.GetFullPath());
    }

    public async Task<string> GetCDNUrlAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        // Local file system doesn't have CDN support
        return await GetDownloadUrlAsync(location, null, cancellationToken);
    }

    public async Task InvalidateCDNCacheAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        // Local file system doesn't have CDN support - no-op
    }

    public async Task<string> GenerateSecureUploadUrlAsync(string fileName, string contentType, TimeSpan expiry, CancellationToken cancellationToken)
    {
        // Local file system doesn't support secure upload URLs
        throw new NotSupportedException("Local file system doesn't support secure upload URLs");
    }

    public async Task<string> GenerateSecureDownloadUrlAsync(StorageLocation location, TimeSpan expiry, CancellationToken cancellationToken)
    {
        // For local file system, just return the regular download URL
        return await GetDownloadUrlAsync(location, expiry, cancellationToken);
    }

    private string GetFullPath(StorageLocation location)
    {
        return Path.Combine(_basePath, location.ContainerName, location.FilePath);
    }

    private string GetContainerName(string contentType)
    {
        return contentType.Split('/')[0] switch
        {
            "image" => "images",
            "video" => "videos",
            "audio" => "audio",
            "application" => "documents",
            "text" => "documents",
            _ => "files"
        };
    }

    private string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var guid = Guid.NewGuid().ToString("N")[..8];
        
        return $"{nameWithoutExtension}_{timestamp}_{guid}{extension}";
    }

    private string GetContentType(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".txt" => "text/plain",
            ".csv" => "text/csv",
            ".json" => "application/json",
            ".xml" => "application/xml",
            _ => "application/octet-stream"
        };
    }
}
