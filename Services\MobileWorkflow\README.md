# Mobile & Workflow Service

The Mobile & Workflow Service is a comprehensive microservice that provides mobile application management, cross-platform synchronization, driver mobile app features, and business process automation capabilities for the TLI Logistics platform.

## Features

### 🚀 **Cross-Platform Accessibility**
- **Native Mobile Applications**: Full-featured iOS and Android apps with feature parity
- **Progressive Web App (PWA)**: Installable web app with offline capabilities
- **Responsive Web Application**: Desktop and tablet optimized interface
- **Offline Capability**: Work without internet connection with automatic sync
- **Data Synchronization**: Cross-device sync with conflict resolution
- **Feature Parity**: Consistent experience across all platforms

### 📱 **Driver Mobile App Features**
- **Trip Assignment Management**: Accept/reject trip assignments with real-time updates
- **Real-time Trip Status Updates**: Live milestone tracking and status updates
- **POD Upload**: Proof of delivery with digital signatures and photos
- **Document Management**: Upload and manage driver documents
- **Enhanced Features**: Pro/Enterprise subscriber exclusive features
- **Offline Operations**: Full functionality without internet connectivity

### ⚙️ **Workflow Management System**
- **Business Process Automation**: Automated workflow execution engine
- **Workflow Designer**: Visual workflow creation and management
- **Task Assignment**: Automated task distribution with SLA management
- **Approval Workflows**: Multi-level approval processes
- **Process Templates**: Pre-built workflow templates for common processes
- **Execution Monitoring**: Real-time workflow execution tracking

### 🔄 **Admin Panel Workflows**
- **System Workflows**: Business process automation and management
- **Task Assignment**: Automated task distribution and tracking
- **SLA Management**: Service level agreement monitoring and alerts
- **Workflow Designer**: Visual workflow creation and execution engine
- **Process Analytics**: Comprehensive workflow performance metrics

## Architecture

The service follows Clean Architecture principles with the following layers:

```
MobileWorkflow.API/          # Web API layer
MobileWorkflow.Application/  # Application logic and CQRS
MobileWorkflow.Domain/       # Domain entities and business logic
MobileWorkflow.Infrastructure/ # Data access and external services
MobileWorkflow.Tests/        # Unit tests
MobileWorkflow.IntegrationTests/ # Integration tests
```

## Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM with PostgreSQL provider
- **TimescaleDB**: Time-series database for analytics
- **MediatR**: CQRS and mediator pattern
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Serilog**: Structured logging
- **xUnit**: Unit testing framework
- **Docker**: Containerization

## Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL with TimescaleDB extension
- Docker (optional)

### Database Setup

1. **Install PostgreSQL with TimescaleDB**:
   ```bash
   # Using Docker
   docker run -d --name timescaledb -p 5432:5432 -e POSTGRES_PASSWORD=timescale timescale/timescaledb:latest-pg15
   ```

2. **Run Database Setup Script**:
   ```bash
   psql -h localhost -U postgres -f database-setup.sql
   ```

### Running the Service

1. **Clone and Navigate**:
   ```bash
   cd Services/MobileWorkflow/MobileWorkflow.API
   ```

2. **Update Configuration**:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Port=5432;Database=mobileworkflow_db;Username=timescale;Password=timescale"
     }
   }
   ```

3. **Run the Service**:
   ```bash
   dotnet run
   ```

4. **Access Swagger UI**: http://localhost:5014

### Running Tests

```bash
# Unit Tests
cd MobileWorkflow.Tests
dotnet test

# Integration Tests
cd MobileWorkflow.IntegrationTests
dotnet test

# All Tests with Coverage
dotnet test --collect:"XPlat Code Coverage"
```

## API Endpoints

### Mobile Apps Management
- `GET /api/mobileapps` - Get all mobile apps
- `GET /api/mobileapps/{id}` - Get mobile app by ID
- `POST /api/mobileapps` - Create new mobile app
- `PUT /api/mobileapps/{id}` - Update mobile app
- `GET /api/mobileapps/{id}/manifest` - Get PWA manifest

### Driver Mobile Features
- `GET /api/drivermobile/dashboard` - Get driver dashboard
- `GET /api/drivermobile/trip-assignments` - Get pending trip assignments
- `POST /api/drivermobile/trip-assignments/{tripId}/accept` - Accept trip
- `POST /api/drivermobile/trip-assignments/{tripId}/reject` - Reject trip
- `PUT /api/drivermobile/trips/{tripId}/status` - Update trip status
- `POST /api/drivermobile/location` - Update driver location
- `POST /api/drivermobile/trips/{tripId}/pod` - Upload POD
- `POST /api/drivermobile/documents` - Upload documents

### Workflow Management
- `GET /api/workflow` - Get all workflows
- `POST /api/workflow` - Create new workflow
- `POST /api/workflow/{workflowId}/execute` - Start workflow execution
- `GET /api/workflow/executions/running` - Get running executions
- `POST /api/workflow/executions/{executionId}/cancel` - Cancel execution

### Business Process Automation
- `GET /api/businessprocess` - Get active processes
- `GET /api/businessprocess/templates` - Get process templates
- `POST /api/businessprocess` - Create business process
- `POST /api/businessprocess/{processName}/trigger` - Trigger process
- `GET /api/businessprocess/metrics` - Get automation metrics

## Configuration

### Mobile App Settings
```json
{
  "MobileApp": {
    "DefaultSyncInterval": 300,
    "MaxOfflineDataSize": 104857600,
    "MaxOfflineDataAge": 86400,
    "SupportedPlatforms": ["Android", "iOS", "Web", "PWA"]
  }
}
```

### PWA Configuration
```json
{
  "PWA": {
    "CacheName": "tli-mobile-v1",
    "CacheStrategy": "CacheFirst",
    "OfflinePageUrl": "/offline.html",
    "MaxCacheSize": 52428800
  }
}
```

### Workflow Settings
```json
{
  "Workflow": {
    "MaxConcurrentExecutions": 100,
    "DefaultTaskSLA": "24:00:00",
    "AutoRetryFailedTasks": true,
    "MaxRetryAttempts": 3
  }
}
```

## Integration with Other Services

The service integrates with:

- **Identity Service**: User authentication and authorization
- **User Management Service**: User profile and preferences
- **Trip Management Service**: Trip data and status updates
- **Order Management Service**: RFQ and order processing
- **Network & Fleet Management**: Driver and vehicle information
- **Communication & Notification**: Alerts and notifications
- **Financial & Payment Service**: Payment processing and earnings
- **Subscription Management**: Feature access control

## Monitoring and Observability

### Health Checks
- `/health` - Overall health status
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

### Logging
- Structured logging with Serilog
- Log levels: Information, Warning, Error
- Log files: `logs/mobileworkflow-{date}.txt`

### Metrics
- Workflow execution metrics
- Mobile app usage analytics
- Sync performance metrics
- API performance monitoring

## Security

### Authentication
- JWT Bearer token authentication
- Role-based authorization (Admin, Manager, Driver, Carrier)
- API key authentication for service-to-service calls

### Data Protection
- Encrypted data transmission (HTTPS)
- Sensitive data encryption at rest
- GDPR compliance for personal data
- Audit trails for all operations

## Deployment

### Docker Deployment
```bash
# Build image
docker build -t mobileworkflow-service .

# Run container
docker run -d -p 5014:80 --name mobileworkflow mobileworkflow-service
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mobileworkflow-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mobileworkflow-service
  template:
    metadata:
      labels:
        app: mobileworkflow-service
    spec:
      containers:
      - name: mobileworkflow-service
        image: mobileworkflow-service:latest
        ports:
        - containerPort: 80
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

---

**Mobile & Workflow Service** - Empowering mobile logistics operations with comprehensive workflow automation.
