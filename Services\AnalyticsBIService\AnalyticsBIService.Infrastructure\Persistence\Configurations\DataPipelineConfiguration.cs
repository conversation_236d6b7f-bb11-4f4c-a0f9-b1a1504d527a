using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for DataPipeline
/// </summary>
public class DataPipelineConfiguration : IEntityTypeConfiguration<DataPipeline>
{
    public void Configure(EntityTypeBuilder<DataPipeline> builder)
    {
        builder.ToTable("data_pipelines");

        builder.HasKey(dp => dp.Id);

        builder.Property(dp => dp.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(dp => dp.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(dp => dp.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(dp => dp.Schedule)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(dp => dp.DataSources)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.Destinations)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.Configuration)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.Steps)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.LastRun);

        builder.Property(dp => dp.NextRun);

        builder.Property(dp => dp.TotalRuns)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(dp => dp.SuccessfulRuns)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(dp => dp.FailedRuns)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(dp => dp.SuccessRate)
            .IsRequired()
            .HasDefaultValue(0)
            .HasPrecision(5, 2);

        builder.Property(dp => dp.AverageExecutionTime)
            .IsRequired()
            .HasDefaultValue(TimeSpan.Zero);

        builder.Property(dp => dp.TotalRecordsProcessed)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(dp => dp.LastSuccessfulRun);

        builder.Property(dp => dp.LastFailedRun);

        builder.Property(dp => dp.LastError)
            .HasMaxLength(2000);

        builder.Property(dp => dp.UserId)
            .IsRequired();

        builder.Property(dp => dp.PipelineName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(dp => dp.SourceConfig)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.TransformationConfig)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.DestinationConfig)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.ScheduleConfig)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(dp => dp.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(dp => dp.UserId)
            .HasDatabaseName("IX_DataPipelines_UserId");

        builder.HasIndex(dp => dp.Status)
            .HasDatabaseName("IX_DataPipelines_Status");

        builder.HasIndex(dp => dp.IsActive)
            .HasDatabaseName("IX_DataPipelines_IsActive");

        builder.HasIndex(dp => dp.LastRun)
            .HasDatabaseName("IX_DataPipelines_LastRun");

        builder.HasIndex(dp => dp.NextRun)
            .HasDatabaseName("IX_DataPipelines_NextRun");

        builder.HasIndex(dp => new { dp.UserId, dp.IsActive })
            .HasDatabaseName("IX_DataPipelines_UserId_IsActive");

        builder.HasIndex(dp => new { dp.Status, dp.IsActive })
            .HasDatabaseName("IX_DataPipelines_Status_IsActive");
    }
}
