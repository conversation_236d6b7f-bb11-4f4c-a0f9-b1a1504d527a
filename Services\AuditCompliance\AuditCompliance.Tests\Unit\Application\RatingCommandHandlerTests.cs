using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Handlers;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Tests.Unit.Application;

public class RatingCommandHandlerTests
{
    private readonly Mock<IServiceProviderRatingRepository> _mockRepository;
    private readonly Mock<ILogger<CreateServiceProviderRatingCommandHandler>> _mockLogger;
    private readonly CreateServiceProviderRatingCommandHandler _handler;

    public RatingCommandHandlerTests()
    {
        _mockRepository = new Mock<IServiceProviderRatingRepository>();
        _mockLogger = new Mock<ILogger<CreateServiceProviderRatingCommandHandler>>();
        _handler = new CreateServiceProviderRatingCommandHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_CreateServiceProviderRatingCommand_ShouldCreateRatingSuccessfully()
    {
        // Arrange
        var command = new CreateServiceProviderRatingCommand
        {
            ShipperId = Guid.NewGuid(),
            ShipperName = "Test Shipper",
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ReviewTitle = "Great service",
            ReviewComment = "Very professional",
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1),
            CategoryRatings = new List<CreateCategoryRatingDto>
            {
                new() { Category = RatingCategory.Timeliness, Rating = 5.0m, Comment = "On time" },
                new() { Category = RatingCategory.Communication, Rating = 4.0m, Comment = "Good communication" }
            }
        };

        var expectedRating = new ServiceProviderRating(
            command.ShipperId,
            command.ShipperName,
            command.TransportCompanyId,
            command.TransportCompanyName,
            RatingScore.FromNumeric(command.OverallRating),
            command.ServiceCompletedAt);

        _mockRepository.Setup(r => r.AddAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedRating);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().Be(expectedRating.Id);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateServiceProviderRatingCommand_ShouldAddCategoryRatings()
    {
        // Arrange
        var command = new CreateServiceProviderRatingCommand
        {
            ShipperId = Guid.NewGuid(),
            ShipperName = "Test Shipper",
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1),
            CategoryRatings = new List<CreateCategoryRatingDto>
            {
                new() { Category = RatingCategory.Timeliness, Rating = 5.0m, Comment = "Excellent timing" },
                new() { Category = RatingCategory.Communication, Rating = 4.0m, Comment = "Good updates" }
            }
        };

        ServiceProviderRating? capturedRating = null;
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()))
            .Callback<ServiceProviderRating, CancellationToken>((rating, ct) => capturedRating = rating)
            .ReturnsAsync((ServiceProviderRating rating, CancellationToken ct) => rating);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedRating.Should().NotBeNull();
        capturedRating!.CategoryRatings.Should().HaveCount(2);
        
        var timelinessRating = capturedRating.CategoryRatings.First(cr => cr.Category == RatingCategory.Timeliness);
        timelinessRating.Rating.NumericValue.Should().Be(5.0m);
        timelinessRating.Comment.Should().Be("Excellent timing");
        
        var communicationRating = capturedRating.CategoryRatings.First(cr => cr.Category == RatingCategory.Communication);
        communicationRating.Rating.NumericValue.Should().Be(4.0m);
        communicationRating.Comment.Should().Be("Good updates");
    }

    [Fact]
    public async Task Handle_CreateServiceProviderRatingCommand_WithException_ShouldThrowAndLog()
    {
        // Arrange
        var command = new CreateServiceProviderRatingCommand
        {
            ShipperId = Guid.NewGuid(),
            ShipperName = "Test Shipper",
            TransportCompanyId = Guid.NewGuid(),
            TransportCompanyName = "Test Transport Company",
            OverallRating = 4.5m,
            ServiceCompletedAt = DateTime.UtcNow.AddDays(-1)
        };

        var expectedException = new Exception("Database error");
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}

public class SubmitServiceProviderRatingCommandHandlerTests
{
    private readonly Mock<IServiceProviderRatingRepository> _mockRepository;
    private readonly Mock<ILogger<SubmitServiceProviderRatingCommandHandler>> _mockLogger;
    private readonly SubmitServiceProviderRatingCommandHandler _handler;

    public SubmitServiceProviderRatingCommandHandlerTests()
    {
        _mockRepository = new Mock<IServiceProviderRatingRepository>();
        _mockLogger = new Mock<ILogger<SubmitServiceProviderRatingCommandHandler>>();
        _handler = new SubmitServiceProviderRatingCommandHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_SubmitServiceProviderRatingCommand_ShouldSubmitRatingSuccessfully()
    {
        // Arrange
        var ratingId = Guid.NewGuid();
        var command = new SubmitServiceProviderRatingCommand { RatingId = ratingId };

        var rating = new ServiceProviderRating(
            Guid.NewGuid(),
            "Test Shipper",
            Guid.NewGuid(),
            "Test Transport Company",
            RatingScore.FromScale(RatingScale.FourStars),
            DateTime.UtcNow.AddDays(-1));

        _mockRepository.Setup(r => r.GetByIdAsync(ratingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(rating);

        _mockRepository.Setup(r => r.UpdateAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        rating.Status.Should().Be(RatingStatus.Active);
        rating.ReviewSubmittedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        _mockRepository.Verify(r => r.UpdateAsync(rating, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SubmitServiceProviderRatingCommand_WithNonExistentRating_ShouldReturnFalse()
    {
        // Arrange
        var ratingId = Guid.NewGuid();
        var command = new SubmitServiceProviderRatingCommand { RatingId = ratingId };

        _mockRepository.Setup(r => r.GetByIdAsync(ratingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ServiceProviderRating?)null);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockRepository.Verify(r => r.UpdateAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_SubmitServiceProviderRatingCommand_WithException_ShouldThrowAndLog()
    {
        // Arrange
        var ratingId = Guid.NewGuid();
        var command = new SubmitServiceProviderRatingCommand { RatingId = ratingId };

        var expectedException = new Exception("Database error");
        _mockRepository.Setup(r => r.GetByIdAsync(ratingId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}

public class ReportServiceIssueCommandHandlerTests
{
    private readonly Mock<IServiceProviderRatingRepository> _mockRepository;
    private readonly Mock<ILogger<ReportServiceIssueCommandHandler>> _mockLogger;
    private readonly ReportServiceIssueCommandHandler _handler;

    public ReportServiceIssueCommandHandlerTests()
    {
        _mockRepository = new Mock<IServiceProviderRatingRepository>();
        _mockLogger = new Mock<ILogger<ReportServiceIssueCommandHandler>>();
        _handler = new ReportServiceIssueCommandHandler(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ReportServiceIssueCommand_ShouldReportIssueSuccessfully()
    {
        // Arrange
        var ratingId = Guid.NewGuid();
        var command = new ReportServiceIssueCommand
        {
            ServiceProviderRatingId = ratingId,
            IssueType = ServiceIssueType.DelayedDelivery,
            Priority = IssuePriority.High,
            Description = "Package was delivered 2 days late",
            Evidence = "Tracking number: ABC123"
        };

        var rating = new ServiceProviderRating(
            Guid.NewGuid(),
            "Test Shipper",
            Guid.NewGuid(),
            "Test Transport Company",
            RatingScore.FromScale(RatingScale.FourStars),
            DateTime.UtcNow.AddDays(-1));

        _mockRepository.Setup(r => r.GetByIdAsync(ratingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(rating);

        _mockRepository.Setup(r => r.UpdateAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBe(Guid.Empty);
        rating.ReportedIssues.Should().HaveCount(1);
        
        var issue = rating.ReportedIssues.First();
        issue.IssueType.Should().Be(ServiceIssueType.DelayedDelivery);
        issue.Priority.Should().Be(IssuePriority.High);
        issue.Description.Should().Be("Package was delivered 2 days late");
        issue.Evidence.Should().Be("Tracking number: ABC123");
        issue.Status.Should().Be(IssueResolutionStatus.Reported);
        
        _mockRepository.Verify(r => r.UpdateAsync(rating, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReportServiceIssueCommand_WithNonExistentRating_ShouldThrowException()
    {
        // Arrange
        var ratingId = Guid.NewGuid();
        var command = new ReportServiceIssueCommand
        {
            ServiceProviderRatingId = ratingId,
            IssueType = ServiceIssueType.DelayedDelivery,
            Priority = IssuePriority.High,
            Description = "Package was delivered 2 days late"
        };

        _mockRepository.Setup(r => r.GetByIdAsync(ratingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ServiceProviderRating?)null);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command, CancellationToken.None));
        
        _mockRepository.Verify(r => r.UpdateAsync(It.IsAny<ServiceProviderRating>(), It.IsAny<CancellationToken>()), Times.Never);
    }
}
