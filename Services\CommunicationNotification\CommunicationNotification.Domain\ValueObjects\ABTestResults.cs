using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Value object representing A/B test statistical results
/// </summary>
public class ABTestResults : ValueObject
{
    public string WinningVariant { get; private set; }
    public decimal ConfidenceLevel { get; private set; }
    public decimal PValue { get; private set; }
    public decimal EffectSize { get; private set; }
    public bool IsStatisticallySignificant { get; private set; }
    public string StatisticalMethod { get; private set; }
    public DateTime CalculatedAt { get; private set; }
    public Dictionary<string, VariantStatistics> VariantStats { get; private set; }
    public string Conclusion { get; private set; }
    public List<string> Recommendations { get; private set; }

    private ABTestResults()
    {
        WinningVariant = string.Empty;
        StatisticalMethod = string.Empty;
        VariantStats = new Dictionary<string, VariantStatistics>();
        Conclusion = string.Empty;
        Recommendations = new List<string>();
    }

    public ABTestResults(
        string winningVariant,
        decimal confidenceLevel,
        decimal pValue,
        decimal effectSize,
        bool isStatisticallySignificant,
        string statisticalMethod,
        Dictionary<string, VariantStatistics> variantStats,
        string conclusion,
        List<string>? recommendations = null)
    {
        if (string.IsNullOrWhiteSpace(winningVariant))
            throw new ArgumentException("Winning variant cannot be empty", nameof(winningVariant));
        if (confidenceLevel < 0 || confidenceLevel > 100)
            throw new ArgumentException("Confidence level must be between 0 and 100", nameof(confidenceLevel));
        if (variantStats == null || !variantStats.Any())
            throw new ArgumentException("Variant statistics cannot be empty", nameof(variantStats));

        WinningVariant = winningVariant;
        ConfidenceLevel = confidenceLevel;
        PValue = pValue;
        EffectSize = effectSize;
        IsStatisticallySignificant = isStatisticallySignificant;
        StatisticalMethod = statisticalMethod;
        CalculatedAt = DateTime.UtcNow;
        VariantStats = variantStats;
        Conclusion = conclusion;
        Recommendations = recommendations ?? new List<string>();
    }

    public static ABTestResults Create(
        string winningVariant,
        decimal confidenceLevel,
        decimal pValue,
        decimal effectSize,
        bool isStatisticallySignificant,
        string statisticalMethod,
        Dictionary<string, VariantStatistics> variantStats,
        string conclusion,
        List<string>? recommendations = null)
    {
        return new ABTestResults(winningVariant, confidenceLevel, pValue, effectSize,
            isStatisticallySignificant, statisticalMethod, variantStats, conclusion, recommendations);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return WinningVariant;
        yield return ConfidenceLevel;
        yield return PValue;
        yield return EffectSize;
        yield return IsStatisticallySignificant;
        yield return StatisticalMethod;
        yield return CalculatedAt;
        yield return Conclusion;

        foreach (var kvp in VariantStats.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var recommendation in Recommendations.OrderBy(x => x))
        {
            yield return recommendation;
        }
    }
}

/// <summary>
/// Statistics for individual A/B test variant
/// </summary>
public class VariantStatistics : ValueObject
{
    public string VariantName { get; private set; }
    public int SampleSize { get; private set; }
    public decimal ConversionRate { get; private set; }
    public decimal StandardError { get; private set; }
    public decimal ConfidenceIntervalLower { get; private set; }
    public decimal ConfidenceIntervalUpper { get; private set; }
    public decimal ZScore { get; private set; }
    public Dictionary<string, decimal> Metrics { get; private set; }

    private VariantStatistics()
    {
        VariantName = string.Empty;
        Metrics = new Dictionary<string, decimal>();
    }

    public VariantStatistics(
        string variantName,
        int sampleSize,
        decimal conversionRate,
        decimal standardError,
        decimal confidenceIntervalLower,
        decimal confidenceIntervalUpper,
        decimal zScore,
        Dictionary<string, decimal>? metrics = null)
    {
        if (string.IsNullOrWhiteSpace(variantName))
            throw new ArgumentException("Variant name cannot be empty", nameof(variantName));
        if (sampleSize < 0)
            throw new ArgumentException("Sample size cannot be negative", nameof(sampleSize));

        VariantName = variantName;
        SampleSize = sampleSize;
        ConversionRate = conversionRate;
        StandardError = standardError;
        ConfidenceIntervalLower = confidenceIntervalLower;
        ConfidenceIntervalUpper = confidenceIntervalUpper;
        ZScore = zScore;
        Metrics = metrics ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return VariantName;
        yield return SampleSize;
        yield return ConversionRate;
        yield return StandardError;
        yield return ConfidenceIntervalLower;
        yield return ConfidenceIntervalUpper;
        yield return ZScore;

        foreach (var kvp in Metrics.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Statistical test configuration for A/B tests
/// </summary>
public class StatisticalTestConfig : ValueObject
{
    public string TestType { get; private set; } // TTest, ChiSquare, ZTest, etc.
    public decimal SignificanceLevel { get; private set; }
    public decimal Power { get; private set; } // Statistical power (1 - β)
    public decimal MinimumDetectableEffect { get; private set; }
    public bool TwoTailed { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    private StatisticalTestConfig()
    {
        TestType = string.Empty;
        Parameters = new Dictionary<string, object>();
    }

    public StatisticalTestConfig(
        string testType,
        decimal significanceLevel = 0.05m,
        decimal power = 0.8m,
        decimal minimumDetectableEffect = 0.05m,
        bool twoTailed = true,
        Dictionary<string, object>? parameters = null)
    {
        if (string.IsNullOrWhiteSpace(testType))
            throw new ArgumentException("Test type cannot be empty", nameof(testType));
        if (significanceLevel <= 0 || significanceLevel >= 1)
            throw new ArgumentException("Significance level must be between 0 and 1", nameof(significanceLevel));
        if (power <= 0 || power >= 1)
            throw new ArgumentException("Power must be between 0 and 1", nameof(power));

        TestType = testType;
        SignificanceLevel = significanceLevel;
        Power = power;
        MinimumDetectableEffect = minimumDetectableEffect;
        TwoTailed = twoTailed;
        Parameters = parameters ?? new Dictionary<string, object>();
    }

    public static StatisticalTestConfig Default()
    {
        return new StatisticalTestConfig("ZTest");
    }

    public static StatisticalTestConfig ForConversionRate(decimal significanceLevel = 0.05m, decimal power = 0.8m)
    {
        return new StatisticalTestConfig("ZTest", significanceLevel, power, 0.05m, true);
    }

    public static StatisticalTestConfig ForContinuousMetric(decimal significanceLevel = 0.05m, decimal power = 0.8m)
    {
        return new StatisticalTestConfig("TTest", significanceLevel, power, 0.1m, true);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TestType;
        yield return SignificanceLevel;
        yield return Power;
        yield return MinimumDetectableEffect;
        yield return TwoTailed;

        foreach (var kvp in Parameters.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Sample size calculation result
/// </summary>
public class SampleSizeCalculation : ValueObject
{
    public int RequiredSampleSizePerVariant { get; private set; }
    public int TotalRequiredSampleSize { get; private set; }
    public decimal ExpectedDuration { get; private set; } // In days
    public decimal TrafficRequired { get; private set; } // Percentage of total traffic
    public StatisticalTestConfig TestConfig { get; private set; }
    public Dictionary<string, object> Assumptions { get; private set; }

    private SampleSizeCalculation()
    {
        TestConfig = StatisticalTestConfig.Default();
        Assumptions = new Dictionary<string, object>();
    }

    public SampleSizeCalculation(
        int requiredSampleSizePerVariant,
        int totalRequiredSampleSize,
        decimal expectedDuration,
        decimal trafficRequired,
        StatisticalTestConfig testConfig,
        Dictionary<string, object>? assumptions = null)
    {
        if (requiredSampleSizePerVariant <= 0)
            throw new ArgumentException("Required sample size per variant must be positive", nameof(requiredSampleSizePerVariant));
        if (totalRequiredSampleSize <= 0)
            throw new ArgumentException("Total required sample size must be positive", nameof(totalRequiredSampleSize));

        RequiredSampleSizePerVariant = requiredSampleSizePerVariant;
        TotalRequiredSampleSize = totalRequiredSampleSize;
        ExpectedDuration = expectedDuration;
        TrafficRequired = trafficRequired;
        TestConfig = testConfig ?? throw new ArgumentNullException(nameof(testConfig));
        Assumptions = assumptions ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return RequiredSampleSizePerVariant;
        yield return TotalRequiredSampleSize;
        yield return ExpectedDuration;
        yield return TrafficRequired;
        yield return TestConfig;

        foreach (var kvp in Assumptions.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

