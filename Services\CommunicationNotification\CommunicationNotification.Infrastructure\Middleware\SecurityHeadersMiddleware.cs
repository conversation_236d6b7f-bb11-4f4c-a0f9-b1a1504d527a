namespace CommunicationNotification.Infrastructure.Middleware;

/// <summary>
/// Security headers middleware
/// </summary>
public class SecurityHeadersMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SecurityHeadersMiddleware> _logger;

    public SecurityHeadersMiddleware(RequestDelegate next, ILogger<SecurityHeadersMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Add security headers
        AddSecurityHeaders(context);

        await _next(context);
    }

    private void AddSecurityHeaders(HttpContext context)
    {
        var headers = context.Response.Headers;

        // Remove server information
        headers.Remove("Server");

        // X-Content-Type-Options
        if (!headers.ContainsKey("X-Content-Type-Options"))
        {
            headers.Add("X-Content-Type-Options", "nosniff");
        }

        // X-Frame-Options
        if (!headers.ContainsKey("X-Frame-Options"))
        {
            headers.Add("X-Frame-Options", "DENY");
        }

        // X-XSS-Protection
        if (!headers.ContainsKey("X-XSS-Protection"))
        {
            headers.Add("X-XSS-Protection", "1; mode=block");
        }

        // Referrer-Policy
        if (!headers.ContainsKey("Referrer-Policy"))
        {
            headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
        }

        // Content-Security-Policy
        if (!headers.ContainsKey("Content-Security-Policy"))
        {
            var csp = "default-src 'self'; " +
                     "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                     "style-src 'self' 'unsafe-inline'; " +
                     "img-src 'self' data: https:; " +
                     "font-src 'self' data:; " +
                     "connect-src 'self' wss: https:; " +
                     "frame-ancestors 'none'; " +
                     "base-uri 'self'; " +
                     "form-action 'self'";
            
            headers.Add("Content-Security-Policy", csp);
        }

        // Strict-Transport-Security (only for HTTPS)
        if (context.Request.IsHttps && !headers.ContainsKey("Strict-Transport-Security"))
        {
            headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
        }

        // Permissions-Policy
        if (!headers.ContainsKey("Permissions-Policy"))
        {
            var permissionsPolicy = "camera=(), " +
                                   "microphone=(), " +
                                   "geolocation=(), " +
                                   "payment=(), " +
                                   "usb=(), " +
                                   "magnetometer=(), " +
                                   "accelerometer=(), " +
                                   "gyroscope=()";
            
            headers.Add("Permissions-Policy", permissionsPolicy);
        }

        // Cache-Control for API responses
        if (context.Request.Path.StartsWithSegments("/api"))
        {
            if (!headers.ContainsKey("Cache-Control"))
            {
                headers.Add("Cache-Control", "no-cache, no-store, must-revalidate");
            }
            
            if (!headers.ContainsKey("Pragma"))
            {
                headers.Add("Pragma", "no-cache");
            }
            
            if (!headers.ContainsKey("Expires"))
            {
                headers.Add("Expires", "0");
            }
        }

        // Add custom TLI headers
        if (!headers.ContainsKey("X-TLI-Service"))
        {
            headers.Add("X-TLI-Service", "Communication-Notification");
        }

        if (!headers.ContainsKey("X-TLI-Version"))
        {
            var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";
            headers.Add("X-TLI-Version", version);
        }
    }
}

/// <summary>
/// Request logging middleware
/// </summary>
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var startTime = DateTime.UtcNow;
        var requestId = Guid.NewGuid().ToString();

        // Add request ID to response headers
        context.Response.Headers.Add("X-Request-ID", requestId);

        // Log request
        _logger.LogInformation("Request started: {RequestId} {Method} {Path} {QueryString} from {RemoteIpAddress}",
            requestId,
            context.Request.Method,
            context.Request.Path,
            context.Request.QueryString,
            context.Connection.RemoteIpAddress);

        try
        {
            await _next(context);
        }
        finally
        {
            var duration = DateTime.UtcNow - startTime;
            
            _logger.LogInformation("Request completed: {RequestId} {StatusCode} in {Duration}ms",
                requestId,
                context.Response.StatusCode,
                duration.TotalMilliseconds);
        }
    }
}

/// <summary>
/// Exception handling middleware
/// </summary>
public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;
    private readonly IWebHostEnvironment _environment;

    public ExceptionHandlingMiddleware(
        RequestDelegate next, 
        ILogger<ExceptionHandlingMiddleware> logger,
        IWebHostEnvironment environment)
    {
        _next = next;
        _logger = logger;
        _environment = environment;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = new
            {
                message = "An error occurred while processing your request.",
                traceId = context.TraceIdentifier,
                timestamp = DateTime.UtcNow,
                path = context.Request.Path.Value
            }
        };

        switch (exception)
        {
            case UnauthorizedAccessException:
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                response = new
                {
                    error = new
                    {
                        message = "Unauthorized access.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value
                    }
                };
                break;

            case ArgumentException:
            case ArgumentNullException:
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                response = new
                {
                    error = new
                    {
                        message = "Invalid request parameters.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value,
                        details = _environment.IsDevelopment() ? exception.Message : null
                    }
                };
                break;

            case KeyNotFoundException:
                context.Response.StatusCode = StatusCodes.Status404NotFound;
                response = new
                {
                    error = new
                    {
                        message = "The requested resource was not found.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value
                    }
                };
                break;

            case TimeoutException:
                context.Response.StatusCode = StatusCodes.Status408RequestTimeout;
                response = new
                {
                    error = new
                    {
                        message = "The request timed out.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value
                    }
                };
                break;

            case InvalidOperationException:
                context.Response.StatusCode = StatusCodes.Status409Conflict;
                response = new
                {
                    error = new
                    {
                        message = "The operation could not be completed due to a conflict.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value,
                        details = _environment.IsDevelopment() ? exception.Message : null
                    }
                };
                break;

            default:
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                response = new
                {
                    error = new
                    {
                        message = "An internal server error occurred.",
                        traceId = context.TraceIdentifier,
                        timestamp = DateTime.UtcNow,
                        path = context.Request.Path.Value,
                        details = _environment.IsDevelopment() ? exception.ToString() : null
                    }
                };
                break;
        }

        var jsonResponse = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// Middleware extensions
/// </summary>
public static class MiddlewareExtensions
{
    /// <summary>
    /// Add security headers middleware
    /// </summary>
    public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SecurityHeadersMiddleware>();
    }

    /// <summary>
    /// Add request logging middleware
    /// </summary>
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RequestLoggingMiddleware>();
    }

    /// <summary>
    /// Add exception handling middleware
    /// </summary>
    public static IApplicationBuilder UseExceptionHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ExceptionHandlingMiddleware>();
    }
}
