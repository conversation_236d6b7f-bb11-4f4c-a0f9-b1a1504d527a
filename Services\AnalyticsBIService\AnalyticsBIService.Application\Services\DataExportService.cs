using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Domain.Services;
using AnalyticsBIService.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for data export and integration capabilities
/// </summary>
public interface IDataExportService
{
    Task<ExportResultDto> ExportDataAsync(DataExportRequestDto request);
    Task<List<ExportFormatDto>> GetSupportedFormatsAsync();
    Task<CustomReportDto> CreateCustomReportAsync(CustomReportRequestDto request);
    Task<List<CustomReportDto>> GetCustomReportsAsync(Guid userId);
    Task<ExportResultDto> ExportCustomReportAsync(Guid reportId, ExportFormat format);
    Task<DataPipelineDto> CreateDataPipelineAsync(DataPipelineRequestDto request);
    Task<List<DataPipelineDto>> GetDataPipelinesAsync(Guid userId);
    Task<bool> ExecuteDataPipelineAsync(Guid pipelineId);
    Task<IntegrationStatusDto> GetIntegrationStatusAsync(Guid userId);

    // Usage logs export
    Task<ExportResultDto> ExportUsageLogsAsync(UsageLogsExportRequestDto request);
}

public class DataExportService : IDataExportService
{
    private readonly IExportRepository _exportRepository;
    private readonly IDataPipelineRepository _dataPipelineRepository;
    private readonly IReportRepository _reportRepository;
    private readonly IOrderDataRepository _orderDataRepository;
    private readonly ITripDataRepository _tripDataRepository;
    private readonly AnalyticsBIService.Application.Interfaces.IFileStorageService _fileStorageService;
    private readonly IExternalBIIntegrationService _biIntegrationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DataExportService> _logger;

    public DataExportService(
        IExportRepository exportRepository,
        IDataPipelineRepository dataPipelineRepository,
        IReportRepository reportRepository,
        IOrderDataRepository orderDataRepository,
        ITripDataRepository tripDataRepository,
        AnalyticsBIService.Application.Interfaces.IFileStorageService fileStorageService,
        IExternalBIIntegrationService biIntegrationService,
        IUnitOfWork unitOfWork,
        ILogger<DataExportService> logger)
    {
        _exportRepository = exportRepository;
        _dataPipelineRepository = dataPipelineRepository;
        _reportRepository = reportRepository;
        _orderDataRepository = orderDataRepository;
        _tripDataRepository = tripDataRepository;
        _fileStorageService = fileStorageService;
        _biIntegrationService = biIntegrationService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ExportResultDto> ExportDataAsync(DataExportRequestDto request)
    {
        try
        {
            _logger.LogInformation("Starting data export for user {UserId} with format {Format}",
                request.UserId, request.Format);

            // Validate request
            ValidateExportRequest(request);

            // Get data based on request
            var data = await GetDataForExportAsync(request);

            // Generate export file
            var exportResult = await GenerateExportFileAsync(data, request);

            // Store export record
            await StoreExportRecordAsync(request, exportResult);

            _logger.LogInformation("Data export completed successfully. File: {FileName}", exportResult.FileName);

            return exportResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting data for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<List<ExportFormatDto>> GetSupportedFormatsAsync()
    {
        return new List<ExportFormatDto>
        {
            new() { Format = ExportFormat.CSV, Name = "CSV", Description = "Comma-separated values", MimeType = "text/csv", Extension = ".csv", MaxRecords = 1000000 },
            new() { Format = ExportFormat.Excel, Name = "Excel", Description = "Microsoft Excel spreadsheet", MimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", Extension = ".xlsx", MaxRecords = 500000 },
            new() { Format = ExportFormat.JSON, Name = "JSON", Description = "JavaScript Object Notation", MimeType = "application/json", Extension = ".json", MaxRecords = 100000 },
            new() { Format = ExportFormat.PDF, Name = "PDF", Description = "Portable Document Format", MimeType = "application/pdf", Extension = ".pdf", MaxRecords = 10000 },
            new() { Format = ExportFormat.XML, Name = "XML", Description = "Extensible Markup Language", MimeType = "application/xml", Extension = ".xml", MaxRecords = 100000 }
        };
    }

    public async Task<CustomReportDto> CreateCustomReportAsync(CustomReportRequestDto request)
    {
        try
        {
            _logger.LogInformation("Creating custom report '{ReportName}' for user {UserId}",
                request.ReportName, request.UserId);

            var customReport = new CustomReport
            {
                Id = Guid.NewGuid(),
                UserId = request.UserId,
                ReportName = request.ReportName,
                Description = request.Description,
                ReportType = request.ReportType,
                DataSources = request.DataSources,
                Filters = request.Filters,
                Columns = request.Columns,
                Aggregations = request.Aggregations.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value),
                Visualizations = request.Visualizations.ToDictionary(v => v.Type, v => (object)v),
                ScheduleConfig = request.ScheduleConfig != null ?
                    new Dictionary<string, object>
                    {
                        ["CronExpression"] = request.ScheduleConfig.CronExpression,
                        ["IsEnabled"] = request.ScheduleConfig.IsEnabled,
                        ["TimeZone"] = request.ScheduleConfig.TimeZone
                    } : new Dictionary<string, object>(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Convert CustomReport to Report entity
            var reportEntity = ConvertToReportEntity(customReport);
            await _reportRepository.AddAsync(reportEntity);
            await _unitOfWork.SaveChangesAsync();

            var reportDto = new CustomReportDto
            {
                Id = customReport.Id,
                UserId = customReport.UserId,
                ReportName = customReport.ReportName,
                Description = customReport.Description,
                ReportType = customReport.ReportType,
                DataSources = new List<string>(), // Report entity doesn't have DataSources
                Filters = customReport.Filters, // Already Dictionary<string, object>
                Columns = customReport.Columns, // Already List<string>
                Aggregations = new Dictionary<string, string>(), // Report entity doesn't have Aggregations
                Visualizations = new List<VisualizationConfigDto>(), // Report entity doesn't have Visualizations
                ScheduleConfig = customReport.ScheduleCron != null ? new ReportScheduleConfigDto
                {
                    ScheduleType = "Custom",
                    CronExpression = customReport.ScheduleCron,
                    IsEnabled = customReport.IsScheduled
                } : null,
                IsActive = true, // Report entity doesn't have IsActive
                CreatedAt = customReport.CreatedAt,
                UpdatedAt = customReport.UpdatedAt
            };

            _logger.LogInformation("Custom report '{ReportName}' created successfully with ID {ReportId}",
                request.ReportName, customReport.Id);

            return reportDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<List<CustomReportDto>> GetCustomReportsAsync(Guid userId)
    {
        try
        {
            var reportsResult = await _reportRepository.GetByUserIdAsync(userId, 1, 1000, CancellationToken.None);
            var reports = reportsResult.Items;

            return reports.Select(r => new CustomReportDto
            {
                Id = r.Id,
                UserId = r.UserId ?? Guid.Empty,
                ReportName = r.ReportName,
                Description = r.Description,
                ReportType = r.Type.ToString(),
                DataSources = new List<string>(), // Default empty - Report doesn't have DataSources
                Filters = r.Parameters ?? new Dictionary<string, object>(),
                Columns = r.Columns ?? new List<string>(),
                Aggregations = new Dictionary<string, string>(), // Default empty - Report doesn't have Aggregations
                Visualizations = new List<VisualizationConfigDto>(), // Default empty - Report doesn't have Visualizations
                ScheduleConfig = new ReportScheduleConfigDto
                {
                    ScheduleType = r.IsScheduled ? "Scheduled" : "Manual",
                    CronExpression = r.ScheduleCron ?? string.Empty,
                    IsEnabled = r.IsScheduled
                },
                IsActive = true, // Default to true - Report doesn't have IsActive
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt ?? DateTime.UtcNow
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting custom reports for user {UserId}", userId);
            throw;
        }
    }

    public async Task<ExportResultDto> ExportCustomReportAsync(Guid reportId, ExportFormat format)
    {
        try
        {
            _logger.LogInformation("Exporting custom report {ReportId} in format {Format}", reportId, format);

            var report = await _reportRepository.GetByIdAsync(reportId);
            if (report == null)
            {
                throw new InvalidOperationException($"Custom report {reportId} not found");
            }

            // Convert Report entity to CustomReport
            var customReport = ConvertToCustomReport(report);

            // Generate report data
            var reportData = await GenerateCustomReportDataAsync(customReport);

            // Create export request
            var exportRequest = new DataExportRequestDto
            {
                UserId = report.UserId ?? Guid.Empty,
                Format = format,
                DataType = "CustomReport",
                Filters = report.Parameters ?? new Dictionary<string, object>(),
                SelectedColumns = report.Columns ?? new List<string>(),
                FileName = $"{report.ReportName}_{DateTime.UtcNow:yyyyMMdd_HHmmss}"
            };

            // Export the data
            var exportResult = await GenerateExportFileAsync(reportData, exportRequest);

            _logger.LogInformation("Custom report {ReportId} exported successfully", reportId);

            return exportResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting custom report {ReportId}", reportId);
            throw;
        }
    }

    public async Task<DataPipelineDto> CreateDataPipelineAsync(DataPipelineRequestDto request)
    {
        try
        {
            _logger.LogInformation("Creating data pipeline '{PipelineName}' for user {UserId}",
                request.PipelineName, request.UserId);

            var pipeline = new DataPipeline(
                name: request.PipelineName,
                description: request.Description,
                schedule: "0 0 * * *", // Default daily schedule
                dataSources: new List<string>(), // Empty list for now
                destinations: new List<string>(), // Empty list for now
                configuration: new Dictionary<string, object>(), // Empty configuration for now
                userId: request.UserId,
                sourceConfig: JsonSerializer.Serialize(request.SourceConfig),
                transformationConfig: JsonSerializer.Serialize(request.TransformationConfig),
                destinationConfig: JsonSerializer.Serialize(request.DestinationConfig),
                scheduleConfig: JsonSerializer.Serialize(request.ScheduleConfig)
            );

            await _dataPipelineRepository.AddAsync(pipeline);
            await _unitOfWork.SaveChangesAsync();

            var pipelineDto = new DataPipelineDto
            {
                Id = pipeline.Id,
                UserId = pipeline.UserId,
                PipelineName = pipeline.PipelineName,
                Description = pipeline.Description,
                SourceConfig = JsonSerializer.Deserialize<DataSourceConfigDto>(pipeline.SourceConfig),
                TransformationConfig = JsonSerializer.Deserialize<DataTransformationConfigDto>(pipeline.TransformationConfig),
                DestinationConfig = JsonSerializer.Deserialize<DataDestinationConfigDto>(pipeline.DestinationConfig),
                ScheduleConfig = JsonSerializer.Deserialize<PipelineScheduleConfigDto>(pipeline.ScheduleConfig),
                IsActive = pipeline.IsActive,
                CreatedAt = pipeline.CreatedAt,
                UpdatedAt = pipeline.UpdatedAt ?? DateTime.UtcNow
            };

            _logger.LogInformation("Data pipeline '{PipelineName}' created successfully with ID {PipelineId}",
                request.PipelineName, pipeline.Id);

            return pipelineDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating data pipeline for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<List<DataPipelineDto>> GetDataPipelinesAsync(Guid userId)
    {
        try
        {
            var pipelines = await _dataPipelineRepository.GetByUserIdAsync(userId);

            return pipelines.Select(p => new DataPipelineDto
            {
                Id = p.Id,
                UserId = p.UserId,
                PipelineName = p.PipelineName,
                Description = p.Description,
                SourceConfig = JsonSerializer.Deserialize<DataSourceConfigDto>(p.SourceConfig),
                TransformationConfig = JsonSerializer.Deserialize<DataTransformationConfigDto>(p.TransformationConfig),
                DestinationConfig = JsonSerializer.Deserialize<DataDestinationConfigDto>(p.DestinationConfig),
                ScheduleConfig = JsonSerializer.Deserialize<PipelineScheduleConfigDto>(p.ScheduleConfig),
                IsActive = p.IsActive,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt ?? DateTime.UtcNow
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting data pipelines for user {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ExecuteDataPipelineAsync(Guid pipelineId)
    {
        try
        {
            _logger.LogInformation("Executing data pipeline {PipelineId}", pipelineId);

            var pipeline = await _dataPipelineRepository.GetByIdAsync(pipelineId);
            if (pipeline == null)
            {
                throw new InvalidOperationException($"Data pipeline {pipelineId} not found");
            }

            // Execute pipeline steps
            var sourceConfig = JsonSerializer.Deserialize<DataSourceConfigDto>(pipeline.SourceConfig);
            var transformationConfig = JsonSerializer.Deserialize<DataTransformationConfigDto>(pipeline.TransformationConfig);
            var destinationConfig = JsonSerializer.Deserialize<DataDestinationConfigDto>(pipeline.DestinationConfig);

            // Extract data from source
            var sourceData = await ExtractDataFromSourceAsync(sourceConfig);

            // Transform data
            var transformedData = await TransformDataAsync(sourceData, transformationConfig);

            // Load data to destination
            await LoadDataToDestinationAsync(transformedData, destinationConfig);

            // Update pipeline execution record
            var execution = new DataPipelineExecution(
                pipelineId,
                "Success",
                transformedData.Count,
                DateTime.UtcNow.AddMinutes(-5), // Placeholder
                DateTime.UtcNow,
                null,
                "Manual",
                null);

            // Create export data record instead of using wrong repository
            var exportData = ExportData.Create(
                Guid.NewGuid(), // UserId - should be passed from request
                "DataPipeline",
                $"pipeline_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                ExportFormat.CSV,
                System.Text.Json.JsonSerializer.Serialize(new { pipelineId, recordCount = transformedData.Count }));

            exportData.MarkCompleted("", 0, transformedData.Count);
            await _exportRepository.AddAsync(exportData);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Data pipeline {PipelineId} executed successfully", pipelineId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing data pipeline {PipelineId}", pipelineId);

            // Record failed execution
            var execution = new DataPipelineExecution(
                pipelineId,
                "Failed",
                0,
                DateTime.UtcNow.AddMinutes(-5),
                DateTime.UtcNow,
                ex.Message,
                "Manual",
                null);

            // Create export data record for failed execution
            var exportData = ExportData.Create(
                Guid.NewGuid(), // UserId - should be passed from request
                "DataPipeline",
                $"pipeline_export_failed_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                ExportFormat.CSV,
                System.Text.Json.JsonSerializer.Serialize(new { pipelineId, error = ex.Message }));

            exportData.MarkFailed(ex.Message);
            await _exportRepository.AddAsync(exportData);
            await _unitOfWork.SaveChangesAsync();

            return false;
        }
    }

    public async Task<IntegrationStatusDto> GetIntegrationStatusAsync(Guid userId)
    {
        try
        {
            var integrations = await _biIntegrationService.GetIntegrationsAsync(userId);

            return new IntegrationStatusDto
            {
                UserId = userId,
                TotalIntegrations = integrations.Count,
                ActiveIntegrations = integrations.Count(i => i.IsActive),
                LastSyncTime = integrations.Where(i => i.LastSyncTime.HasValue).Max(i => i.LastSyncTime),
                IntegrationHealth = "Healthy", // This would be calculated based on recent sync status
                SupportedPlatforms = new List<string> { "Tableau", "Power BI", "Looker", "Qlik Sense", "Google Data Studio" },
                Integrations = integrations
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting integration status for user {UserId}", userId);
            throw;
        }
    }

    // Private helper methods
    private void ValidateExportRequest(DataExportRequestDto request)
    {
        if (request.UserId == Guid.Empty)
            throw new ArgumentException("User ID is required");

        if (string.IsNullOrEmpty(request.DataType))
            throw new ArgumentException("Data type is required");

        // Add more validation as needed
    }

    private async Task<List<Dictionary<string, object>>> GetDataForExportAsync(DataExportRequestDto request)
    {
        // This would implement actual data retrieval based on request parameters
        // For now, returning sample data
        return new List<Dictionary<string, object>>
        {
            new() { { "Id", 1 }, { "Name", "Sample Data 1" }, { "Value", 100 } },
            new() { { "Id", 2 }, { "Name", "Sample Data 2" }, { "Value", 200 } }
        };
    }

    private async Task<ExportResultDto> GenerateExportFileAsync(List<Dictionary<string, object>> data, DataExportRequestDto request)
    {
        var fileName = $"{request.FileName}_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
        var content = request.Format switch
        {
            ExportFormat.CSV => GenerateCSV(data),
            ExportFormat.JSON => GenerateJSON(data),
            ExportFormat.XML => GenerateXML(data),
            _ => throw new NotSupportedException($"Export format {request.Format} is not supported")
        };

        var fileUrl = await _fileStorageService.SaveFileAsync(fileName, Encoding.UTF8.GetBytes(content), GetMimeType(request.Format));

        return new ExportResultDto
        {
            FileName = fileName,
            FileUrl = fileUrl,
            Format = request.Format.ToString(),
            RecordCount = data.Count,
            FileSizeBytes = Encoding.UTF8.GetByteCount(content),
            GeneratedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddDays(7) // Files expire after 7 days
        };
    }

    private async Task StoreExportRecordAsync(DataExportRequestDto request, ExportResultDto result)
    {
        var exportRecord = ExportData.Create(
            request.UserId,
            request.DataType,
            result.FileName,
            request.Format,
            System.Text.Json.JsonSerializer.Serialize(request.Filters ?? new Dictionary<string, object>()));

        exportRecord.MarkCompleted(result.FileUrl, result.FileSizeBytes, result.RecordCount);
        exportRecord.SetExpiryDate(result.ExpiresAt);

        await _exportRepository.AddAsync(exportRecord);
        await _unitOfWork.SaveChangesAsync();
    }

    private async Task<List<Dictionary<string, object>>> GenerateCustomReportDataAsync(CustomReport report)
    {
        // This would implement actual report data generation
        // For now, returning sample data
        return new List<Dictionary<string, object>>
        {
            new() { { "Metric", "Revenue" }, { "Value", 125000 }, { "Period", "Q1 2024" } },
            new() { { "Metric", "Orders" }, { "Value", 1250 }, { "Period", "Q1 2024" } }
        };
    }

    private async Task<List<Dictionary<string, object>>> ExtractDataFromSourceAsync(DataSourceConfigDto? sourceConfig)
    {
        // This would implement actual data extraction from various sources
        return new List<Dictionary<string, object>>();
    }

    private async Task<List<Dictionary<string, object>>> TransformDataAsync(List<Dictionary<string, object>> data, DataTransformationConfigDto? transformationConfig)
    {
        // This would implement actual data transformation
        return data;
    }

    private async Task LoadDataToDestinationAsync(List<Dictionary<string, object>> data, DataDestinationConfigDto? destinationConfig)
    {
        // This would implement actual data loading to destination
        await Task.CompletedTask;
    }

    private string GenerateCSV(List<Dictionary<string, object>> data)
    {
        if (!data.Any()) return string.Empty;

        var csv = new StringBuilder();

        // Header
        csv.AppendLine(string.Join(",", data.First().Keys));

        // Data rows
        foreach (var row in data)
        {
            csv.AppendLine(string.Join(",", row.Values.Select(v => $"\"{v}\"")));
        }

        return csv.ToString();
    }

    private string GenerateJSON(List<Dictionary<string, object>> data)
    {
        return JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
    }

    private string GenerateXML(List<Dictionary<string, object>> data)
    {
        // Simple XML generation - would be more sophisticated in real implementation
        var xml = new StringBuilder();
        xml.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        xml.AppendLine("<data>");

        foreach (var row in data)
        {
            xml.AppendLine("  <record>");
            foreach (var kvp in row)
            {
                xml.AppendLine($"    <{kvp.Key}>{kvp.Value}</{kvp.Key}>");
            }
            xml.AppendLine("  </record>");
        }

        xml.AppendLine("</data>");
        return xml.ToString();
    }

    private string GetMimeType(ExportFormat format)
    {
        return format switch
        {
            ExportFormat.CSV => "text/csv",
            ExportFormat.JSON => "application/json",
            ExportFormat.XML => "application/xml",
            ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ExportFormat.PDF => "application/pdf",
            _ => "application/octet-stream"
        };
    }

    public async Task<ExportResultDto> ExportUsageLogsAsync(UsageLogsExportRequestDto request)
    {
        try
        {
            // Implementation for exporting usage logs
            // Create export data record
            var exportData = ExportData.Create(
                request.UserId,
                "UsageLogs",
                $"usage_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}",
                request.Format,
                System.Text.Json.JsonSerializer.Serialize(new Dictionary<string, object>
                {
                    ["StartDate"] = request.StartDate,
                    ["EndDate"] = request.EndDate,
                    ["UserTypes"] = request.UserTypes,
                    ["IncludeDetails"] = request.IncludeDetails
                }));

            exportData.MarkProcessing();
            await _exportRepository.AddAsync(exportData);

            // Generate usage logs data (placeholder implementation)
            var usageLogsData = new List<Dictionary<string, object>>();

            // Format and save the export file
            var fileName = $"usage_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}";
            var content = FormatExportData(usageLogsData, request.Format);
            var filePath = await _fileStorageService.SaveFileAsync(fileName, content, GetMimeType(request.Format));

            // Update export data with completion
            exportData.MarkCompleted(filePath, content.Length, usageLogsData.Count);
            await _exportRepository.UpdateAsync(exportData);

            return new ExportResultDto
            {
                ExportId = exportData.Id,
                Status = "Completed",
                DownloadUrl = $"/api/exports/download/{exportData.Id}",
                FileName = fileName,
                FileSize = content.Length,
                RecordCount = usageLogsData.Count,
                CreatedAt = exportData.RequestedAt,
                CompletedAt = exportData.CompletedAt
            };
        }
        catch (Exception ex)
        {
            return new ExportResultDto
            {
                ExportId = Guid.NewGuid(),
                Status = "Failed",
                ErrorMessage = ex.Message,
                CreatedAt = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Format export data based on the specified format
    /// </summary>
    private byte[] FormatExportData(List<Dictionary<string, object>> data, ExportFormat format)
    {
        return format switch
        {
            ExportFormat.CSV => System.Text.Encoding.UTF8.GetBytes(GenerateCSV(data)),
            ExportFormat.JSON => System.Text.Encoding.UTF8.GetBytes(GenerateJSON(data)),
            ExportFormat.XML => System.Text.Encoding.UTF8.GetBytes(GenerateXML(data)),
            ExportFormat.Excel => GenerateExcel(data),
            _ => throw new NotSupportedException($"Export format {format} is not supported")
        };
    }



    /// <summary>
    /// Generate Excel format data
    /// </summary>
    private byte[] GenerateExcel(List<Dictionary<string, object>> data)
    {
        // For now, return CSV as bytes (in a real implementation, use EPPlus or similar)
        return System.Text.Encoding.UTF8.GetBytes(GenerateCSV(data));
    }

    private Report ConvertToReportEntity(CustomReport customReport)
    {
        // Create a TimePeriodValue for the report
        var period = new TimePeriodValue(
            DateTime.UtcNow.AddDays(-30),
            DateTime.UtcNow,
            TimePeriod.Daily);

        // Convert ReportType string to enum
        if (!Enum.TryParse<ReportType>(customReport.ReportType, out var reportType))
        {
            reportType = ReportType.CustomReport;
        }

        var report = new Report(
            customReport.ReportName,
            customReport.Description,
            reportType,
            customReport.UserId,
            UserType.Admin, // Default user type
            period,
            customReport.Filters,
            customReport.Configuration,
            isScheduled: customReport.IsScheduled,
            scheduleCron: customReport.ScheduleCron);

        return report;
    }

    private CustomReport ConvertToCustomReport(Report report)
    {
        return new CustomReport
        {
            Id = report.Id,
            Name = report.Name,
            Description = report.Description,
            ReportType = report.Type.ToString(),
            UserId = report.GeneratedBy,
            ReportName = report.Name,
            CreatedAt = report.CreatedAt,
            LastModified = report.UpdatedAt,
            IsScheduled = report.IsScheduled,
            ScheduleCron = report.ScheduleCron,
            Configuration = report.Configuration,
            Filters = report.Parameters,
            Columns = report.Columns.ToList(),
            IsActive = true,
            UpdatedAt = report.UpdatedAt ?? DateTime.UtcNow
        };
    }
}

public interface IExternalBIIntegrationService
{
    Task<List<BIIntegrationDto>> GetIntegrationsAsync(Guid userId);
    Task<BIIntegrationDto> CreateIntegrationAsync(BIIntegrationRequestDto request);
    Task<bool> SyncDataAsync(Guid integrationId);
    Task<bool> TestConnectionAsync(Guid integrationId);
}
