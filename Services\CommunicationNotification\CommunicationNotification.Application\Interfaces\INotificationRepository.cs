using CommunicationNotification.Domain.Entities;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Repository interface for notifications
/// </summary>
public interface INotificationRepository
{
    /// <summary>
    /// Add a new notification
    /// </summary>
    /// <param name="notification">Notification to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(Notification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notification by ID
    /// </summary>
    /// <param name="id">Notification ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification or null</returns>
    Task<Notification?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update notification
    /// </summary>
    /// <param name="notification">Notification to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(Notification notification, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notifications ready to send
    /// </summary>
    /// <param name="batchSize">Maximum number of notifications to return</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notifications ready to send</returns>
    Task<List<Notification>> GetPendingNotificationsAsync(
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get failed notifications that can be retried
    /// </summary>
    /// <param name="maxRetries">Maximum retry count</param>
    /// <param name="batchSize">Maximum number of notifications to return</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notifications that can be retried</returns>
    Task<List<Notification>> GetRetryableNotificationsAsync(
        int maxRetries = 3,
        int batchSize = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notifications by user ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of user notifications</returns>
    Task<List<Notification>> GetByUserIdAsync(
        Guid userId,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notifications by status
    /// </summary>
    /// <param name="status">Message status</param>
    /// <param name="fromDate">From date filter</param>
    /// <param name="toDate">To date filter</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notifications</returns>
    Task<List<Notification>> GetByStatusAsync(
        MessageStatus status,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notifications by channel
    /// </summary>
    /// <param name="channel">Notification channel</param>
    /// <param name="fromDate">From date filter</param>
    /// <param name="toDate">To date filter</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of notifications</returns>
    Task<List<Notification>> GetByChannelAsync(
        NotificationChannel channel,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get notification statistics
    /// </summary>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Notification statistics</returns>
    Task<NotificationStatistics> GetStatisticsAsync(
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old notifications
    /// </summary>
    /// <param name="olderThan">Delete notifications older than this date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted notifications</returns>
    Task<int> DeleteOldNotificationsAsync(
        DateTime olderThan,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for messages
/// </summary>
public interface IMessageRepository
{
    /// <summary>
    /// Add a new message
    /// </summary>
    /// <param name="message">Message to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get message by ID
    /// </summary>
    /// <param name="id">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message or null</returns>
    Task<Message?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update message
    /// </summary>
    /// <param name="message">Message to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(Message message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get messages by conversation thread ID
    /// </summary>
    /// <param name="conversationThreadId">Conversation thread ID</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of messages</returns>
    Task<List<Message>> GetByConversationThreadIdAsync(
        Guid conversationThreadId,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get messages between two users
    /// </summary>
    /// <param name="userId1">First user ID</param>
    /// <param name="userId2">Second user ID</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of messages</returns>
    Task<List<Message>> GetMessagesBetweenUsersAsync(
        Guid userId1,
        Guid userId2,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search messages
    /// </summary>
    /// <param name="searchQuery">Search query</param>
    /// <param name="userId">Optional user ID filter</param>
    /// <param name="conversationThreadId">Optional conversation thread ID filter</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching messages</returns>
    Task<List<Message>> SearchMessagesAsync(
        string searchQuery,
        Guid? userId = null,
        Guid? conversationThreadId = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get unread message count for user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Unread message count</returns>
    Task<int> GetUnreadMessageCountAsync(
        Guid userId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for user preferences
/// </summary>
public interface IUserPreferenceRepository
{
    /// <summary>
    /// Add user preference
    /// </summary>
    /// <param name="userPreference">User preference to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(UserPreference userPreference, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user preference by user ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User preference or null</returns>
    Task<UserPreference?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user preference
    /// </summary>
    /// <param name="userPreference">User preference to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(UserPreference userPreference, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete user preference
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for conversation threads
/// </summary>
public interface IConversationRepository
{
    /// <summary>
    /// Add a new conversation thread
    /// </summary>
    /// <param name="conversation">Conversation thread to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(ConversationThread conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversation thread by ID
    /// </summary>
    /// <param name="id">Conversation thread ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation thread or null</returns>
    Task<ConversationThread?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update conversation thread
    /// </summary>
    /// <param name="conversation">Conversation thread to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(ConversationThread conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversations by user ID
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of conversation threads</returns>
    Task<List<ConversationThread>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversations by related entity
    /// </summary>
    /// <param name="entityId">Related entity ID</param>
    /// <param name="entityType">Related entity type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of conversation threads</returns>
    Task<List<ConversationThread>> GetByRelatedEntityAsync(
        Guid entityId,
        string entityType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Search conversations
    /// </summary>
    /// <param name="searchQuery">Search query</param>
    /// <param name="userId">Optional user ID filter</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching conversation threads</returns>
    Task<List<ConversationThread>> SearchConversationsAsync(
        string searchQuery,
        Guid? userId = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active conversations count for user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Active conversations count</returns>
    Task<int> GetActiveConversationCountAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Notification statistics
/// </summary>
public class NotificationStatistics
{
    public int TotalNotifications { get; set; }
    public int SentNotifications { get; set; }
    public int DeliveredNotifications { get; set; }
    public int FailedNotifications { get; set; }
    public int PendingNotifications { get; set; }
    public Dictionary<NotificationChannel, int> ChannelBreakdown { get; set; } = new();
    public Dictionary<MessageType, int> MessageTypeBreakdown { get; set; } = new();
    public double DeliveryRate => TotalNotifications > 0 ? (double)DeliveredNotifications / TotalNotifications * 100 : 0;
    public double FailureRate => TotalNotifications > 0 ? (double)FailedNotifications / TotalNotifications * 100 : 0;
}

