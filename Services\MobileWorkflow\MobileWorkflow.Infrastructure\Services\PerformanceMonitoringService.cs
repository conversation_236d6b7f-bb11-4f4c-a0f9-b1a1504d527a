using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Diagnostics;
using System.Collections.Concurrent;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Infrastructure.Services;

public interface IPerformanceMonitoringService
{
    Task<string> StartOperationAsync(string operationName, Dictionary<string, object>? properties = null);
    Task CompleteOperationAsync(string operationId, bool isSuccess = true, string? errorMessage = null);
    Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? properties = null);
    Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? properties = null);
    Task<PerformanceReportDto> GetPerformanceReportAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<List<PerformanceMetricDto>> GetMetricsAsync(string? metricName = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<PerformanceAlertDto?> CheckPerformanceAlertsAsync(CancellationToken cancellationToken = default);
    Task OptimizeQueriesAsync(CancellationToken cancellationToken = default);
    Task<SystemHealthDto> GetSystemHealthAsync(CancellationToken cancellationToken = default);
}

public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ICachingService _cachingService;
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, PerformanceOperation> _activeOperations;
    private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
    private readonly ConcurrentDictionary<string, long> _counters;

    public PerformanceMonitoringService(
        ICachingService cachingService,
        ILogger<PerformanceMonitoringService> logger,
        IConfiguration configuration)
    {
        _cachingService = cachingService;
        _logger = logger;
        _configuration = configuration;
        _activeOperations = new ConcurrentDictionary<string, PerformanceOperation>();
        _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
        _counters = new ConcurrentDictionary<string, long>();
    }

    public async Task<string> StartOperationAsync(string operationName, Dictionary<string, object>? properties = null)
    {
        try
        {
            var operationId = Guid.NewGuid().ToString();
            var operation = new PerformanceOperation
            {
                Id = operationId,
                Name = operationName,
                StartTime = DateTime.UtcNow,
                Properties = properties ?? new Dictionary<string, object>(),
                Stopwatch = Stopwatch.StartNew()
            };

            _activeOperations.TryAdd(operationId, operation);
            
            _logger.LogDebug("Started performance operation: {OperationName} ({OperationId})", operationName, operationId);
            
            return operationId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting performance operation: {OperationName}", operationName);
            return string.Empty;
        }
    }

    public async Task CompleteOperationAsync(string operationId, bool isSuccess = true, string? errorMessage = null)
    {
        try
        {
            if (_activeOperations.TryRemove(operationId, out var operation))
            {
                operation.Stopwatch.Stop();
                operation.EndTime = DateTime.UtcNow;
                operation.Duration = operation.Stopwatch.Elapsed;
                operation.IsSuccess = isSuccess;
                operation.ErrorMessage = errorMessage;

                // Record the operation metrics
                await RecordOperationMetricsAsync(operation);
                
                _logger.LogDebug("Completed performance operation: {OperationName} ({OperationId}) - Duration: {Duration}ms, Success: {IsSuccess}", 
                    operation.Name, operationId, operation.Duration.TotalMilliseconds, isSuccess);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing performance operation: {OperationId}", operationId);
        }
    }

    public async Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? properties = null)
    {
        try
        {
            var metric = new PerformanceMetric
            {
                Name = metricName,
                Value = value,
                Timestamp = DateTime.UtcNow,
                Properties = properties ?? new Dictionary<string, object>()
            };

            _metrics.TryAdd($"{metricName}_{DateTime.UtcNow.Ticks}", metric);

            // Cache recent metrics
            var cacheKey = $"metric:{metricName}:{DateTime.UtcNow:yyyy-MM-dd-HH}";
            var cachedMetrics = await _cachingService.GetAsync<List<PerformanceMetric>>(cacheKey) ?? new List<PerformanceMetric>();
            cachedMetrics.Add(metric);
            await _cachingService.SetAsync(cacheKey, cachedMetrics, TimeSpan.FromHours(24));

            _logger.LogDebug("Recorded metric: {MetricName} = {Value}", metricName, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording metric: {MetricName}", metricName);
        }
    }

    public async Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? properties = null)
    {
        try
        {
            _counters.AddOrUpdate(counterName, value, (key, existingValue) => existingValue + value);

            // Cache counter values
            var cacheKey = $"counter:{counterName}:{DateTime.UtcNow:yyyy-MM-dd}";
            await _cachingService.IncrementAsync(cacheKey, value, TimeSpan.FromDays(7));

            _logger.LogDebug("Recorded counter: {CounterName} += {Value}", counterName, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording counter: {CounterName}", counterName);
        }
    }

    public async Task<PerformanceReportDto> GetPerformanceReportAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new PerformanceReportDto
            {
                FromDate = fromDate,
                ToDate = toDate,
                GeneratedAt = DateTime.UtcNow
            };

            // Get cached metrics for the date range
            var tasks = new List<Task>();
            var allMetrics = new ConcurrentBag<PerformanceMetric>();

            for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
            {
                for (var hour = 0; hour < 24; hour++)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        var cacheKey = $"metric:*:{date:yyyy-MM-dd}-{hour:D2}";
                        // In a real implementation, you would query all metrics for this hour
                        // For now, we'll simulate some data
                    }));
                }
            }

            await Task.WhenAll(tasks);

            // Calculate report statistics
            report.TotalOperations = _activeOperations.Count + 1000; // Simulated
            report.AverageResponseTime = 150.5; // Simulated
            report.ErrorRate = 2.1; // Simulated
            report.ThroughputPerSecond = 45.2; // Simulated

            // Top slow operations
            report.TopSlowOperations = new List<SlowOperationDto>
            {
                new() { OperationName = "Database Query", AverageTime = 250.5, Count = 150 },
                new() { OperationName = "File Upload", AverageTime = 180.2, Count = 89 },
                new() { OperationName = "API Call", AverageTime = 120.1, Count = 234 }
            };

            // Performance trends
            report.PerformanceTrends = new Dictionary<DateTime, double>();
            for (var date = fromDate.Date; date <= toDate.Date; date = date.AddDays(1))
            {
                report.PerformanceTrends[date] = Random.Shared.NextDouble() * 200 + 100; // Simulated
            }

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance report");
            return new PerformanceReportDto { FromDate = fromDate, ToDate = toDate, GeneratedAt = DateTime.UtcNow };
        }
    }

    public async Task<List<PerformanceMetricDto>> GetMetricsAsync(string? metricName = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = new List<PerformanceMetricDto>();
            var from = fromDate ?? DateTime.UtcNow.AddHours(-24);

            // Get metrics from cache
            for (var date = from.Date; date <= DateTime.UtcNow.Date; date = date.AddDays(1))
            {
                for (var hour = 0; hour < 24; hour++)
                {
                    var cacheKey = $"metric:{metricName ?? "*"}:{date:yyyy-MM-dd}-{hour:D2}";
                    var cachedMetrics = await _cachingService.GetAsync<List<PerformanceMetric>>(cacheKey);
                    
                    if (cachedMetrics != null)
                    {
                        metrics.AddRange(cachedMetrics.Select(m => new PerformanceMetricDto
                        {
                            Name = m.Name,
                            Value = m.Value,
                            Timestamp = m.Timestamp,
                            Properties = m.Properties
                        }));
                    }
                }
            }

            return metrics.Where(m => m.Timestamp >= from).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics");
            return new List<PerformanceMetricDto>();
        }
    }

    public async Task<PerformanceAlertDto?> CheckPerformanceAlertsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Check various performance thresholds
            var alerts = new List<string>();

            // Check response time
            var avgResponseTime = await GetAverageResponseTimeAsync();
            var responseTimeThreshold = _configuration.GetValue<double>("Performance:ResponseTimeThreshold", 1000);
            if (avgResponseTime > responseTimeThreshold)
            {
                alerts.Add($"Average response time ({avgResponseTime:F2}ms) exceeds threshold ({responseTimeThreshold}ms)");
            }

            // Check error rate
            var errorRate = await GetErrorRateAsync();
            var errorRateThreshold = _configuration.GetValue<double>("Performance:ErrorRateThreshold", 5.0);
            if (errorRate > errorRateThreshold)
            {
                alerts.Add($"Error rate ({errorRate:F2}%) exceeds threshold ({errorRateThreshold}%)");
            }

            // Check memory usage
            var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
            var memoryThreshold = _configuration.GetValue<long>("Performance:MemoryThreshold", 500);
            if (memoryUsage > memoryThreshold)
            {
                alerts.Add($"Memory usage ({memoryUsage}MB) exceeds threshold ({memoryThreshold}MB)");
            }

            if (alerts.Any())
            {
                return new PerformanceAlertDto
                {
                    Id = Guid.NewGuid(),
                    Severity = alerts.Count > 2 ? "Critical" : "Warning",
                    Title = "Performance Alert",
                    Description = string.Join("; ", alerts),
                    DetectedAt = DateTime.UtcNow,
                    Alerts = alerts
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking performance alerts");
            return null;
        }
    }

    public async Task OptimizeQueriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting query optimization process");

            // Analyze slow queries
            var slowQueries = await AnalyzeSlowQueriesAsync();
            
            // Suggest optimizations
            var optimizations = new List<string>();
            
            foreach (var query in slowQueries)
            {
                if (query.AverageTime > 1000) // > 1 second
                {
                    optimizations.Add($"Consider adding index for query: {query.OperationName}");
                }
                
                if (query.Count > 1000) // High frequency
                {
                    optimizations.Add($"Consider caching results for: {query.OperationName}");
                }
            }

            // Cache optimization suggestions
            await _cachingService.SetAsync("query_optimizations", optimizations, TimeSpan.FromHours(24));

            _logger.LogInformation("Query optimization completed. Found {Count} optimization suggestions", optimizations.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during query optimization");
        }
    }

    public async Task<SystemHealthDto> GetSystemHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var health = new SystemHealthDto
            {
                CheckedAt = DateTime.UtcNow,
                OverallStatus = "Healthy"
            };

            // CPU Usage
            var process = Process.GetCurrentProcess();
            health.CpuUsagePercent = process.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;

            // Memory Usage
            health.MemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024);
            health.MemoryUsagePercent = (health.MemoryUsageMB / 1024) * 100; // Assuming 1GB limit

            // Disk Usage (simplified)
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady);
            health.DiskUsagePercent = drives.Any() ? 
                (double)(drives.First().TotalSize - drives.First().AvailableFreeSpace) / drives.First().TotalSize * 100 : 0;

            // Active Connections
            health.ActiveConnections = _activeOperations.Count;

            // Cache Hit Rate
            health.CacheHitRate = await CalculateCacheHitRateAsync();

            // Response Time
            health.AverageResponseTimeMs = await GetAverageResponseTimeAsync();

            // Error Rate
            health.ErrorRatePercent = await GetErrorRateAsync();

            // Determine overall status
            if (health.CpuUsagePercent > 80 || health.MemoryUsagePercent > 80 || health.ErrorRatePercent > 10)
            {
                health.OverallStatus = "Critical";
            }
            else if (health.CpuUsagePercent > 60 || health.MemoryUsagePercent > 60 || health.ErrorRatePercent > 5)
            {
                health.OverallStatus = "Warning";
            }

            return health;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health");
            return new SystemHealthDto
            {
                CheckedAt = DateTime.UtcNow,
                OverallStatus = "Error",
                ErrorMessage = ex.Message
            };
        }
    }

    // Helper methods
    private async Task RecordOperationMetricsAsync(PerformanceOperation operation)
    {
        await RecordMetricAsync($"operation.{operation.Name}.duration", operation.Duration.TotalMilliseconds);
        await RecordCounterAsync($"operation.{operation.Name}.count");
        
        if (!operation.IsSuccess)
        {
            await RecordCounterAsync($"operation.{operation.Name}.errors");
        }
    }

    private async Task<double> GetAverageResponseTimeAsync()
    {
        // Simplified calculation - in real implementation, this would query actual metrics
        return await Task.FromResult(Random.Shared.NextDouble() * 200 + 100);
    }

    private async Task<double> GetErrorRateAsync()
    {
        // Simplified calculation - in real implementation, this would query actual metrics
        return await Task.FromResult(Random.Shared.NextDouble() * 5);
    }

    private async Task<double> CalculateCacheHitRateAsync()
    {
        // Simplified calculation - in real implementation, this would track cache hits/misses
        return await Task.FromResult(Random.Shared.NextDouble() * 20 + 80); // 80-100%
    }

    private async Task<List<SlowOperationDto>> AnalyzeSlowQueriesAsync()
    {
        // Simplified analysis - in real implementation, this would analyze actual query logs
        return await Task.FromResult(new List<SlowOperationDto>
        {
            new() { OperationName = "GetUserWorkflows", AverageTime = 1250, Count = 450 },
            new() { OperationName = "ProcessFormSubmission", AverageTime = 890, Count = 1200 },
            new() { OperationName = "UpdateDeviceStatus", AverageTime = 650, Count = 2300 }
        });
    }
}

// Supporting classes
public class PerformanceOperation
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public Stopwatch Stopwatch { get; set; } = new();
}

public class PerformanceMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}
