using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Scheduled report entity for automated report generation
/// </summary>
public class ScheduledReport : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string ReportConfiguration { get; private set; }
    public string ScheduleConfiguration { get; private set; }
    public string DeliveryConfiguration { get; private set; }
    public string Status { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? NextRunDate { get; private set; }
    public DateTime? LastRunDate { get; private set; }
    public string? LastRunStatus { get; private set; }
    public int ExecutionCount { get; private set; } = 0;

    private ScheduledReport()
    {
        Name = string.Empty;
        Description = string.Empty;
        ReportConfiguration = string.Empty;
        ScheduleConfiguration = string.Empty;
        DeliveryConfiguration = string.Empty;
        Status = string.Empty;
    }

    public ScheduledReport(
        string name,
        string description,
        string reportConfiguration,
        string scheduleConfiguration,
        string deliveryConfiguration,
        Guid createdBy,
        UserType userType)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ReportConfiguration = reportConfiguration ?? throw new ArgumentNullException(nameof(reportConfiguration));
        ScheduleConfiguration = scheduleConfiguration ?? throw new ArgumentNullException(nameof(scheduleConfiguration));
        DeliveryConfiguration = deliveryConfiguration ?? throw new ArgumentNullException(nameof(deliveryConfiguration));
        CreatedBy = createdBy;
        UserType = userType;
        Status = "Scheduled";
        IsActive = true;
    }

    public void UpdateStatus(string status)
    {
        Status = status ?? throw new ArgumentNullException(nameof(status));
        SetUpdatedAt();
    }

    public void UpdateNextRunDate(DateTime nextRunDate)
    {
        NextRunDate = nextRunDate;
        SetUpdatedAt();
    }

    public void RecordExecution(string status)
    {
        LastRunDate = DateTime.UtcNow;
        LastRunStatus = status;
        ExecutionCount++;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateReportConfiguration(string reportConfiguration)
    {
        ReportConfiguration = reportConfiguration ?? throw new ArgumentNullException(nameof(reportConfiguration));
        SetUpdatedAt();
    }

    public void UpdateScheduleConfiguration(string scheduleConfiguration, DateTime? nextRunDate = null)
    {
        ScheduleConfiguration = scheduleConfiguration ?? throw new ArgumentNullException(nameof(scheduleConfiguration));
        if (nextRunDate.HasValue)
            NextRunDate = nextRunDate.Value;
        SetUpdatedAt();
    }

    public void UpdateDeliveryConfiguration(string deliveryConfiguration)
    {
        DeliveryConfiguration = deliveryConfiguration ?? throw new ArgumentNullException(nameof(deliveryConfiguration));
        SetUpdatedAt();
    }

    public void SetActiveStatus(bool isActive)
    {
        IsActive = isActive;
        SetUpdatedAt();
    }
}
