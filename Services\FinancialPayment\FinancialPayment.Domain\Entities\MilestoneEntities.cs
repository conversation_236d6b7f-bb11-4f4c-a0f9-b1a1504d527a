using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.Entities;

public class MilestoneDocument : BaseEntity
{
    public Guid PaymentMilestoneId { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string DocumentType { get; private set; } = string.Empty;
    public string FileUrl { get; private set; } = string.Empty;
    public string ContentType { get; private set; } = string.Empty;
    public long FileSize { get; private set; }
    public DateTime UploadedAt { get; private set; }
    public Guid UploadedBy { get; private set; }
    public bool IsRequired { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public Guid? VerifiedBy { get; private set; }
    public string? VerificationNotes { get; private set; }
    public string? FileHash { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    // Navigation properties
    public PaymentMilestone PaymentMilestone { get; private set; } = null!;

    private MilestoneDocument() { } // EF Constructor

    public MilestoneDocument(
        Guid paymentMilestoneId,
        string fileName,
        string documentType,
        string fileUrl,
        string contentType,
        long fileSize,
        Guid uploadedBy,
        bool isRequired = false,
        string? fileHash = null,
        DateTime? expiresAt = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("File URL cannot be empty", nameof(fileUrl));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be greater than zero", nameof(fileSize));

        Id = Guid.NewGuid();
        PaymentMilestoneId = paymentMilestoneId;
        FileName = fileName;
        DocumentType = documentType;
        FileUrl = fileUrl;
        ContentType = contentType;
        FileSize = fileSize;
        UploadedAt = DateTime.UtcNow;
        UploadedBy = uploadedBy;
        IsRequired = isRequired;
        IsVerified = false;
        FileHash = fileHash;
        ExpiresAt = expiresAt;
        CreatedAt = DateTime.UtcNow;
    }

    public void Verify(Guid verifiedBy, string? verificationNotes = null)
    {
        if (IsVerified)
            throw new InvalidOperationException("Document is already verified");

        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
        VerificationNotes = verificationNotes;
    }

    public void Reject(Guid rejectedBy, string rejectionReason)
    {
        if (string.IsNullOrWhiteSpace(rejectionReason))
            throw new ArgumentException("Rejection reason is required", nameof(rejectionReason));

        IsVerified = false;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = rejectedBy;
        VerificationNotes = $"Rejected: {rejectionReason}";
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public void UpdateExpiration(DateTime? newExpiresAt)
    {
        ExpiresAt = newExpiresAt;
    }
}

public class MilestonePayoutRule : BaseEntity
{
    public Guid PaymentMilestoneId { get; private set; }
    public string RuleName { get; private set; } = string.Empty;
    public string RuleType { get; private set; } = string.Empty; // Percentage, Fixed, Conditional
    public decimal RuleValue { get; private set; }
    public string? Condition { get; private set; }
    public bool IsActive { get; private set; }
    public int Priority { get; private set; }
    public string? Description { get; private set; }

    // Navigation properties
    public PaymentMilestone PaymentMilestone { get; private set; } = null!;

    private MilestonePayoutRule() { } // EF Constructor

    public MilestonePayoutRule(
        Guid paymentMilestoneId,
        string ruleName,
        string ruleType,
        decimal ruleValue,
        int priority = 1,
        string? condition = null,
        string? description = null)
    {
        if (string.IsNullOrWhiteSpace(ruleName))
            throw new ArgumentException("Rule name cannot be empty", nameof(ruleName));

        if (string.IsNullOrWhiteSpace(ruleType))
            throw new ArgumentException("Rule type cannot be empty", nameof(ruleType));

        if (priority < 1)
            throw new ArgumentException("Priority must be greater than 0", nameof(priority));

        Id = Guid.NewGuid();
        PaymentMilestoneId = paymentMilestoneId;
        RuleName = ruleName;
        RuleType = ruleType;
        RuleValue = ruleValue;
        Priority = priority;
        Condition = condition;
        Description = description;
        IsActive = true;
        CreatedAt = DateTime.UtcNow;
    }

    public decimal ApplyRule(decimal baseAmount)
    {
        if (!IsActive)
            return baseAmount;

        return RuleType.ToLower() switch
        {
            "percentage" => baseAmount * (RuleValue / 100),
            "fixed" => RuleValue,
            "deduction" => baseAmount - RuleValue,
            "bonus" => baseAmount + RuleValue,
            _ => baseAmount
        };
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void UpdateRuleValue(decimal newValue)
    {
        RuleValue = newValue;
    }

    public void UpdateCondition(string? newCondition)
    {
        Condition = newCondition;
    }
}


