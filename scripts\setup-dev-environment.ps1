# PowerShell script to set up the development environment
Write-Host "Setting up TLI Microservices Development Environment..." -ForegroundColor Green

# Check prerequisites
Write-Host "`nChecking prerequisites..." -ForegroundColor Yellow

# Check .NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK not found. Please install .NET 8 SDK." -ForegroundColor Red
    exit 1
}

# Check Docker
try {
    docker --version | Out-Null
    Write-Host "✓ Docker is installed" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker not found. Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check if Dock<PERSON> is running
try {
    docker ps | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot

# Create necessary directories
Write-Host "`nCreating directories..." -ForegroundColor Yellow
$directories = @(
    "$solutionRoot\logs",
    "$solutionRoot\TestResults",
    "$solutionRoot\data\postgres",
    "$solutionRoot\data\rabbitmq",
    "$solutionRoot\data\redis"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✓ Directory exists: $dir" -ForegroundColor Green
    }
}

# Restore NuGet packages
Write-Host "`nRestoring NuGet packages..." -ForegroundColor Yellow
dotnet restore "$solutionRoot\TLIMicroservices.sln"
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ NuGet packages restored" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to restore NuGet packages" -ForegroundColor Red
    exit 1
}

# Build the solution
Write-Host "`nBuilding solution..." -ForegroundColor Yellow
dotnet build "$solutionRoot\TLIMicroservices.sln" --configuration Debug
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Solution built successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Build failed" -ForegroundColor Red
    exit 1
}

# Start infrastructure services
Write-Host "`nStarting infrastructure services..." -ForegroundColor Yellow
& "$PSScriptRoot\start-infrastructure.ps1"

Write-Host "`n🎉 Development environment setup completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run services: .\scripts\start-services.ps1" -ForegroundColor White
Write-Host "2. Run tests: .\scripts\run-tests.ps1" -ForegroundColor White
Write-Host "3. Access API Gateway: http://localhost:5000" -ForegroundColor White
Write-Host "4. Access Identity API: http://localhost:5001" -ForegroundColor White
