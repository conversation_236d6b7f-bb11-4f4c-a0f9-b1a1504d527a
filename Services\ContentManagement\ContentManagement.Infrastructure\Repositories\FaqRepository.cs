using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for FaqItem entity
/// </summary>
public class FaqRepository : IFaqRepository
{
    private readonly ContentManagementDbContext _context;
    private readonly ILogger<FaqRepository> _logger;

    public FaqRepository(
        ContentManagementDbContext context,
        ILogger<FaqRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<FaqItem?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Include(f => f.Versions)
            .Include(f => f.Attachments)
            .FirstOrDefaultAsync(f => f.Id == id, cancellationToken);
    }

    public async Task<List<FaqItem>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Include(f => f.Versions)
            .Include(f => f.Attachments)
            .OrderBy(f => f.SortOrder)
            .ThenBy(f => f.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(FaqItem faqItem, CancellationToken cancellationToken = default)
    {
        await _context.FaqItems.AddAsync(faqItem, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(FaqItem faqItem, CancellationToken cancellationToken = default)
    {
        _context.FaqItems.Update(faqItem);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(FaqItem faqItem, CancellationToken cancellationToken = default)
    {
        _context.FaqItems.Remove(faqItem);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<(List<FaqItem> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        FaqCategory? category = null,
        ContentStatus? status = null,
        bool? isHighlighted = null,
        List<string>? tags = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        string sortBy = "SortOrder",
        bool sortDescending = false,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _context.FaqItems
            .Include(f => f.Versions)
            .Include(f => f.Attachments)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(f =>
                f.Question.Contains(searchTerm) ||
                f.Answer.Contains(searchTerm) ||
                f.Title.Contains(searchTerm));
        }

        if (category.HasValue)
        {
            query = query.Where(f => f.Category == category.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(f => f.Status == status.Value);
        }

        if (isHighlighted.HasValue)
        {
            query = query.Where(f => f.IsHighlighted == isHighlighted.Value);
        }

        if (tags?.Any() == true)
        {
            foreach (var tag in tags)
            {
                query = query.Where(f => f.Tags.Contains(tag));
            }
        }

        // Apply role visibility filter
        if (userRoles?.Any() == true)
        {
            query = query.Where(f =>
                !f.RoleVisibility.Any() ||
                f.RoleVisibility.Any(rv => userRoles.Contains(rv)));
        }

        // Apply visibility filters
        query = ApplyVisibilityFilter(query, userRoles, isAuthenticated);

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = ApplySorting(query, sortBy, sortDescending);

        // Apply pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<List<FaqItem>> GetByCategoryAsync(FaqCategory category, CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Where(f => f.Category == category && f.Status == ContentStatus.Published)
            .OrderBy(f => f.SortOrder)
            .ThenBy(f => f.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<FaqItem>> GetHighlightedAsync(int? limit = null, CancellationToken cancellationToken = default)
    {
        IQueryable<FaqItem> query = _context.FaqItems
            .Where(f => f.IsHighlighted && f.Status == ContentStatus.Published)
            .OrderBy(f => f.SortOrder)
            .ThenByDescending(f => f.ViewCount);

        if (limit.HasValue)
        {
            query = query.Take(limit.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<List<FaqItem>> GetMostViewedAsync(
        int? limit = null,
        FaqCategory? category = null,
        DateTime? fromDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.FaqItems
            .Where(f => f.Status == ContentStatus.Published);

        if (category.HasValue)
        {
            query = query.Where(f => f.Category == category.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(f => f.CreatedAt >= fromDate.Value);
        }

        query = query.OrderByDescending(f => f.ViewCount)
                    .ThenByDescending(f => f.HelpfulnessRating);

        if (limit.HasValue)
        {
            query = query.Take(limit.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<List<FaqItem>> GetHighestRatedAsync(
        int? limit = null,
        FaqCategory? category = null,
        int? minimumRatings = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.FaqItems
            .Where(f => f.Status == ContentStatus.Published);

        if (category.HasValue)
        {
            query = query.Where(f => f.Category == category.Value);
        }

        if (minimumRatings.HasValue)
        {
            query = query.Where(f => f.TotalRatings >= minimumRatings.Value);
        }

        query = query.OrderByDescending(f => f.HelpfulnessRating)
                    .ThenByDescending(f => f.TotalRatings)
                    .ThenByDescending(f => f.ViewCount);

        if (limit.HasValue)
        {
            query = query.Take(limit.Value);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<FaqCategory, int>> GetFaqCountByCategoryAsync(CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Where(f => f.Status == ContentStatus.Published)
            .GroupBy(f => f.Category)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<FaqCategory, decimal>> GetAverageRatingByCategoryAsync(CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Where(f => f.Status == ContentStatus.Published && f.TotalRatings > 0)
            .GroupBy(f => f.Category)
            .ToDictionaryAsync(
                g => g.Key,
                g => g.Average(f => f.HelpfulnessRating),
                cancellationToken);
    }

    public async Task<Dictionary<FaqCategory, int>> GetTotalViewsByCategoryAsync(CancellationToken cancellationToken = default)
    {
        return await _context.FaqItems
            .Where(f => f.Status == ContentStatus.Published)
            .GroupBy(f => f.Category)
            .ToDictionaryAsync(
                g => g.Key,
                g => g.Sum(f => f.ViewCount),
                cancellationToken);
    }

    private static IQueryable<FaqItem> ApplyVisibilityFilter(
        IQueryable<FaqItem> query,
        List<string>? userRoles,
        bool isAuthenticated)
    {
        if (!isAuthenticated)
        {
            // Only show published FAQs for unauthenticated users
            query = query.Where(f => f.Status == ContentStatus.Published);
        }

        return query;
    }

    private static IQueryable<FaqItem> ApplySorting(
        IQueryable<FaqItem> query,
        string sortBy,
        bool sortDescending)
    {
        return sortBy.ToLowerInvariant() switch
        {
            "question" => sortDescending ? query.OrderByDescending(f => f.Question) : query.OrderBy(f => f.Question),
            "category" => sortDescending ? query.OrderByDescending(f => f.Category) : query.OrderBy(f => f.Category),
            "viewcount" => sortDescending ? query.OrderByDescending(f => f.ViewCount) : query.OrderBy(f => f.ViewCount),
            "rating" => sortDescending ? query.OrderByDescending(f => f.HelpfulnessRating) : query.OrderBy(f => f.HelpfulnessRating),
            "createdat" => sortDescending ? query.OrderByDescending(f => f.CreatedAt) : query.OrderBy(f => f.CreatedAt),
            "updatedat" => sortDescending ? query.OrderByDescending(f => f.UpdatedAt) : query.OrderBy(f => f.UpdatedAt),
            "sortorder" => sortDescending ? query.OrderByDescending(f => f.SortOrder) : query.OrderBy(f => f.SortOrder),
            _ => sortDescending ? query.OrderByDescending(f => f.SortOrder).ThenByDescending(f => f.CreatedAt) : query.OrderBy(f => f.SortOrder).ThenBy(f => f.CreatedAt)
        };
    }
}
