using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Domain.Repositories;

public interface IMilestoneTemplateRepository
{
    Task<MilestoneTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<MilestoneTemplate?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetByTypeAsync(string type, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetDefaultTemplatesAsync(CancellationToken cancellationToken = default);
    Task<MilestoneTemplate?> GetDefaultTemplateByTypeAsync(string type, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetTemplatesByCreatorAsync(string createdBy, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<MilestoneTemplate> AddAsync(MilestoneTemplate template, CancellationToken cancellationToken = default);
    Task<MilestoneTemplate> UpdateAsync(MilestoneTemplate template, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<int> GetUsageCountAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneTemplate>> GetMostUsedTemplatesAsync(int count, CancellationToken cancellationToken = default);
}

public interface IMilestoneStepRepository
{
    Task<MilestoneStep?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStep>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStep>> GetActiveStepsByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestoneStep?> GetByTemplateAndSequenceAsync(Guid templateId, int sequenceNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStep>> GetRequiredStepsByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestoneStep> AddAsync(MilestoneStep step, CancellationToken cancellationToken = default);
    Task<MilestoneStep> UpdateAsync(MilestoneStep step, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByTemplateAndSequenceAsync(Guid templateId, int sequenceNumber, CancellationToken cancellationToken = default);
    Task<int> GetMaxSequenceNumberAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestoneStep>> GetStepsWithPayoutRulesAsync(Guid templateId, CancellationToken cancellationToken = default);
}

public interface IMilestonePayoutRuleRepository
{
    Task<MilestonePayoutRule?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRule>> GetByStepIdAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRule>> GetActiveRulesByStepIdAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRule>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<MilestonePayoutRule> AddAsync(MilestonePayoutRule rule, CancellationToken cancellationToken = default);
    Task<MilestonePayoutRule> UpdateAsync(MilestonePayoutRule rule, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalPayoutPercentageByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalPayoutPercentageByStepAsync(Guid stepId, CancellationToken cancellationToken = default);
    Task<IEnumerable<MilestonePayoutRule>> GetRulesWithConditionsAsync(CancellationToken cancellationToken = default);
}

public interface IRoleTemplateMappingsRepository
{
    Task<RoleTemplateMappings?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetByRoleNameAsync(string roleName, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetActiveByRoleNameAsync(string roleName, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappings?> GetDefaultByRoleNameAsync(string roleName, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetDefaultMappingsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetByPriorityAsync(int minPriority, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappings?> GetBestMatchAsync(string roleName, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<RoleTemplateMappings> AddAsync(RoleTemplateMappings mapping, CancellationToken cancellationToken = default);
    Task<RoleTemplateMappings> UpdateAsync(RoleTemplateMappings mapping, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ExistsByRoleAndTemplateAsync(string roleName, Guid templateId, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetDistinctRoleNamesAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleTemplateMappings>> GetMappingsByCreatorAsync(string createdBy, CancellationToken cancellationToken = default);
}
