﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Identity.Domain.ValueObjects;
using Identity.Application.Common.Interfaces;
using Microsoft.Extensions.Logging;

namespace Identity.Infrastructure.Data
{
    public class SeedData
    {
        private readonly IUserRepository _userRepository;
        private readonly Identity.Domain.Repositories.IRoleRepository _roleRepository;
        private readonly Identity.Domain.Repositories.IPermissionRepository _permissionRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly ILogger<SeedData> _logger;

        public SeedData(
            IUserRepository userRepository,
            Identity.Domain.Repositories.IRoleRepository roleRepository,
            Identity.Domain.Repositories.IPermissionRepository permissionRepository,
            IPasswordHasher passwordHasher,
            ILogger<SeedData> logger)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
            _passwordHasher = passwordHasher;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                _logger.LogInformation("Starting Identity service data seeding");

                await SeedPermissionsAsync();
                await SeedRolesAsync();
                await SeedAdminUserAsync();

                _logger.LogInformation("Identity service data seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during Identity service data seeding");
                throw;
            }
        }

        private async Task SeedPermissionsAsync()
        {
            var permissions = new[]
            {
                new Permission("users.read", "Read user information", "User Management", "Users", PermissionType.Read),
                new Permission("users.write", "Create and update users", "User Management", "Users", PermissionType.Write),
                new Permission("users.delete", "Delete users", "User Management", "Users", PermissionType.Delete),
                new Permission("roles.read", "Read role information", "Role Management", "Roles", PermissionType.Read),
                new Permission("roles.write", "Create and update roles", "Role Management", "Roles", PermissionType.Write),
                new Permission("roles.delete", "Delete roles", "Role Management", "Roles", PermissionType.Delete),
                new Permission("permissions.read", "Read permission information", "Permission Management", "Permissions", PermissionType.Read),
                new Permission("permissions.write", "Create and update permissions", "Permission Management", "Permissions", PermissionType.Write),
                new Permission("organizations.read", "Read organization information", "Organization Management", "Organizations", PermissionType.Read),
                new Permission("organizations.write", "Create and update organizations", "Organization Management", "Organizations", PermissionType.Write),
                new Permission("organizations.delete", "Delete organizations", "Organization Management", "Organizations", PermissionType.Delete),
                new Permission("system.admin", "Full system administration access", "System", "Administration", PermissionType.Admin),
                new Permission("audit.read", "Read audit logs", "Audit", "Logs", PermissionType.Read),
                new Permission("security.manage", "Manage security settings", "Security", "Settings", PermissionType.Manage)
            };

            foreach (var permission in permissions)
            {
                var existingPermission = await _permissionRepository.GetByNameAsync(permission.Name);
                if (existingPermission == null)
                {
                    await _permissionRepository.AddAsync(permission);
                    _logger.LogInformation("Created permission: {PermissionName}", permission.Name);
                }
            }
        }

        private async Task SeedRolesAsync()
        {
            // Create System Admin role
            var adminRole = await _roleRepository.GetByNameAsync("SystemAdmin");
            if (adminRole == null)
            {
                adminRole = new Role("SystemAdmin", "System Administrator with full access", Guid.Empty, true);

                // Add all permissions to admin role before saving
                var allPermissions = await _permissionRepository.GetAllAsync();
                foreach (var permission in allPermissions)
                {
                    adminRole.AddPermission(permission);
                }

                await _roleRepository.AddAsync(adminRole);
                _logger.LogInformation("Created SystemAdmin role with all permissions");
            }

            // Create User role
            var userRole = await _roleRepository.GetByNameAsync("User");
            if (userRole == null)
            {
                userRole = new Role("User", "Standard user role", Guid.Empty, true);

                // Add basic permissions to user role before saving
                var userPermissions = new[] { "users.read" };
                foreach (var permissionName in userPermissions)
                {
                    var permission = await _permissionRepository.GetByNameAsync(permissionName);
                    if (permission != null)
                    {
                        userRole.AddPermission(permission);
                    }
                }

                await _roleRepository.AddAsync(userRole);
                _logger.LogInformation("Created User role with basic permissions");
            }

            // Create Organization Admin role
            var orgAdminRole = await _roleRepository.GetByNameAsync("OrganizationAdmin");
            if (orgAdminRole == null)
            {
                orgAdminRole = new Role("OrganizationAdmin", "Organization administrator", Guid.Empty, true);

                // Add organization management permissions before saving
                var orgPermissions = new[]
                {
                    "users.read", "users.write", "roles.read", "roles.write",
                    "organizations.read", "organizations.write", "audit.read"
                };
                foreach (var permissionName in orgPermissions)
                {
                    var permission = await _permissionRepository.GetByNameAsync(permissionName);
                    if (permission != null)
                    {
                        orgAdminRole.AddPermission(permission);
                    }
                }

                await _roleRepository.AddAsync(orgAdminRole);
                _logger.LogInformation("Created OrganizationAdmin role");
            }
        }

        private async Task SeedAdminUserAsync()
        {
            var adminUser = await _userRepository.GetByUsernameAsync("admin");
            if (adminUser == null)
            {
                var email = Email.Create("<EMAIL>");
                var passwordHash = _passwordHasher.HashPassword("Admin123!");

                adminUser = new User("admin", email, passwordHash);
                adminUser.ConfirmEmail();
                adminUser.ActivateUser();

                // Assign SystemAdmin role before saving
                var adminRole = await _roleRepository.GetByNameAsync("SystemAdmin");
                if (adminRole != null)
                {
                    adminUser.AddRole(adminRole);
                }

                await _userRepository.AddAsync(adminUser);
                _logger.LogInformation("Created admin user with SystemAdmin role");
            }
        }
    }
}

