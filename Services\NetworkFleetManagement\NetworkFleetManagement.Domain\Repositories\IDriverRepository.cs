using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Repositories;

public interface IDriverRepository
{
    Task<Driver?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Driver?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Driver?> GetByLicenseNumberAsync(string licenseNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> GetByStatusAsync(DriverStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> GetAvailableDriversAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> GetDriversRequiringLicenseRenewalAsync(int daysThreshold = 30, CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> GetDriversByOperationalAreaAsync(string area, CancellationToken cancellationToken = default);
    Task<IEnumerable<Driver>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<(IEnumerable<Driver> Drivers, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? carrierId = null,
        DriverStatus? status = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default);
    
    void Add(Driver driver);
    void Update(Driver driver);
    void Remove(Driver driver);
}
