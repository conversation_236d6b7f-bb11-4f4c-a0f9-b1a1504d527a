using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for resource metrics
/// </summary>
public class ResourceMetricsRequest : ValueObject
{
    public List<string> ResourceTypes { get; init; }
    public DateTime FromDate { get; init; }
    public DateTime ToDate { get; init; }
    public string? GroupBy { get; init; }
    public string? AggregationType { get; init; }
    public List<string>? MetricNames { get; init; }
    public int Skip { get; init; }
    public int Take { get; init; }

    public ResourceMetricsRequest(
        List<string> resourceTypes,
        DateTime fromDate,
        DateTime toDate,
        string? groupBy = null,
        string? aggregationType = null,
        List<string>? metricNames = null,
        int skip = 0,
        int take = 100)
    {
        ResourceTypes = resourceTypes;
        FromDate = fromDate;
        ToDate = toDate;
        GroupBy = groupBy;
        AggregationType = aggregationType;
        MetricNames = metricNames;
        Skip = skip;
        Take = take;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FromDate;
        yield return ToDate;
        yield return GroupBy ?? string.Empty;
        yield return AggregationType ?? string.Empty;
        yield return Skip;
        yield return Take;
        
        foreach (var resourceType in ResourceTypes.OrderBy(x => x))
        {
            yield return resourceType;
        }
        
        if (MetricNames != null)
        {
            foreach (var metricName in MetricNames.OrderBy(x => x))
            {
                yield return metricName;
            }
        }
    }
}
