using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Infrastructure.Authorization;
using Shared.Infrastructure.Middleware;
using Shared.Infrastructure.Services;

namespace Shared.Infrastructure.Extensions
{
    /// <summary>
    /// Extension methods for configuring export-related services
    /// </summary>
    public static class ExportServicesExtensions
    {
        /// <summary>
        /// Add export services to the service collection
        /// </summary>
        public static IServiceCollection AddExportServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure export security options
            services.Configure<ExportSecurityOptions>(configuration.GetSection("ExportSecurity"));
            
            // Configure rate limiting options
            services.Configure<RateLimitOptions>(configuration.GetSection("RateLimit"));
            
            // Configure export cache options
            services.Configure<ExportCacheOptions>(configuration.GetSection("ExportCache"));
            
            // Add core export services
            services.AddScoped<IExportCacheService, ExportCacheService>();
            services.AddScoped<IExportNotificationService, ExportNotificationService>();
            
            // Add background export service
            services.AddHostedService<BackgroundExportService>();
            
            // Add distributed caching (Redis recommended for production)
            var cacheConnectionString = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(cacheConnectionString))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = cacheConnectionString;
                    options.InstanceName = "TLI_Exports";
                });
            }
            else
            {
                // Fallback to in-memory cache for development
                services.AddMemoryCache();
                services.AddSingleton<Microsoft.Extensions.Caching.Distributed.IDistributedCache, 
                    Microsoft.Extensions.Caching.Memory.MemoryDistributedCache>();
            }
            
            return services;
        }

        /// <summary>
        /// Configure export middleware in the application pipeline
        /// </summary>
        public static IApplicationBuilder UseExportMiddleware(this IApplicationBuilder app, IConfiguration configuration)
        {
            // Add rate limiting middleware
            var rateLimitOptions = configuration.GetSection("RateLimit").Get<RateLimitOptions>() ?? new RateLimitOptions();
            app.UseMiddleware<RateLimitingMiddleware>(rateLimitOptions);
            
            return app;
        }

        /// <summary>
        /// Add export authorization policies
        /// </summary>
        public static IServiceCollection AddExportAuthorizationPolicies(this IServiceCollection services)
        {
            services.AddAuthorization(options =>
            {
                // User Summary Export Policy
                options.AddPolicy("UserSummaryExport", policy =>
                    policy.RequireRole("Admin", "Manager")
                          .RequireAuthenticatedUser());

                // KYC Documents Export Policy (most restrictive)
                options.AddPolicy("KycDocumentsExport", policy =>
                    policy.RequireRole("Admin", "ComplianceOfficer")
                          .RequireAuthenticatedUser()
                          .RequireClaim("department", "Compliance", "Admin"));

                // Usage Logs Export Policy
                options.AddPolicy("UsageLogsExport", policy =>
                    policy.RequireRole("Admin", "Auditor", "Analyst")
                          .RequireAuthenticatedUser());

                // Feedback History Export Policy
                options.AddPolicy("FeedbackHistoryExport", policy =>
                    policy.RequireRole("Admin", "ComplianceOfficer", "Auditor")
                          .RequireAuthenticatedUser());

                // Admin Audit Trail Export Policy (most restrictive)
                options.AddPolicy("AdminAuditTrailExport", policy =>
                    policy.RequireRole("Admin", "Auditor")
                          .RequireAuthenticatedUser()
                          .RequireClaim("clearance", "High"));

                // General Export Policy
                options.AddPolicy("GeneralExport", policy =>
                    policy.RequireRole("Admin", "Manager", "Analyst", "ComplianceOfficer", "Auditor")
                          .RequireAuthenticatedUser());
            });

            return services;
        }

        /// <summary>
        /// Configure export monitoring and health checks
        /// </summary>
        public static IServiceCollection AddExportMonitoring(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHealthChecks()
                .AddCheck<ExportHealthCheck>("export_services")
                .AddCheck<BackgroundExportHealthCheck>("background_export_service");

            // Add metrics collection for exports
            services.AddSingleton<IExportMetricsCollector, ExportMetricsCollector>();

            return services;
        }
    }

    /// <summary>
    /// Health check for export services
    /// </summary>
    public class ExportHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
    {
        private readonly IExportCacheService _cacheService;
        private readonly ILogger<ExportHealthCheck> _logger;

        public ExportHealthCheck(IExportCacheService cacheService, ILogger<ExportHealthCheck> logger)
        {
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Test cache connectivity
                var testKey = "health_check_test";
                await _cacheService.SetAsync(testKey, new { test = true }, TimeSpan.FromMinutes(1));
                var result = await _cacheService.GetAsync<object>(testKey);
                await _cacheService.RemoveAsync(testKey);

                if (result != null)
                {
                    return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Export services are healthy");
                }
                else
                {
                    return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded("Cache service is not responding properly");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Export health check failed");
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Export services are unhealthy", ex);
            }
        }
    }

    /// <summary>
    /// Health check for background export service
    /// </summary>
    public class BackgroundExportHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
    {
        private readonly ILogger<BackgroundExportHealthCheck> _logger;

        public BackgroundExportHealthCheck(ILogger<BackgroundExportHealthCheck> logger)
        {
            _logger = logger;
        }

        public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                // In a real implementation, you would check if the background service is running
                // and processing jobs correctly
                
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Background export service is healthy");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Background export health check failed");
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy("Background export service is unhealthy", ex);
            }
        }
    }

    /// <summary>
    /// Metrics collector for export operations
    /// </summary>
    public interface IExportMetricsCollector
    {
        void RecordExportRequest(string exportType, string userId, bool success);
        void RecordExportDuration(string exportType, TimeSpan duration);
        void RecordExportSize(string exportType, long sizeBytes, int recordCount);
        Task<ExportMetrics> GetMetricsAsync(DateTime fromDate, DateTime toDate);
    }

    public class ExportMetricsCollector : IExportMetricsCollector
    {
        private readonly ILogger<ExportMetricsCollector> _logger;
        private readonly Dictionary<string, List<ExportMetric>> _metrics = new();
        private readonly object _metricsLock = new();

        public ExportMetricsCollector(ILogger<ExportMetricsCollector> logger)
        {
            _logger = logger;
        }

        public void RecordExportRequest(string exportType, string userId, bool success)
        {
            lock (_metricsLock)
            {
                if (!_metrics.ContainsKey(exportType))
                {
                    _metrics[exportType] = new List<ExportMetric>();
                }

                _metrics[exportType].Add(new ExportMetric
                {
                    ExportType = exportType,
                    UserId = userId,
                    Success = success,
                    Timestamp = DateTime.UtcNow,
                    MetricType = "Request"
                });
            }
        }

        public void RecordExportDuration(string exportType, TimeSpan duration)
        {
            lock (_metricsLock)
            {
                if (!_metrics.ContainsKey(exportType))
                {
                    _metrics[exportType] = new List<ExportMetric>();
                }

                _metrics[exportType].Add(new ExportMetric
                {
                    ExportType = exportType,
                    Duration = duration,
                    Timestamp = DateTime.UtcNow,
                    MetricType = "Duration"
                });
            }
        }

        public void RecordExportSize(string exportType, long sizeBytes, int recordCount)
        {
            lock (_metricsLock)
            {
                if (!_metrics.ContainsKey(exportType))
                {
                    _metrics[exportType] = new List<ExportMetric>();
                }

                _metrics[exportType].Add(new ExportMetric
                {
                    ExportType = exportType,
                    SizeBytes = sizeBytes,
                    RecordCount = recordCount,
                    Timestamp = DateTime.UtcNow,
                    MetricType = "Size"
                });
            }
        }

        public async Task<ExportMetrics> GetMetricsAsync(DateTime fromDate, DateTime toDate)
        {
            lock (_metricsLock)
            {
                var filteredMetrics = _metrics.Values
                    .SelectMany(m => m)
                    .Where(m => m.Timestamp >= fromDate && m.Timestamp <= toDate)
                    .ToList();

                return new ExportMetrics
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalRequests = filteredMetrics.Count(m => m.MetricType == "Request"),
                    SuccessfulRequests = filteredMetrics.Count(m => m.MetricType == "Request" && m.Success),
                    AverageDuration = filteredMetrics.Where(m => m.MetricType == "Duration").Average(m => m.Duration?.TotalSeconds ?? 0),
                    TotalDataExported = filteredMetrics.Where(m => m.MetricType == "Size").Sum(m => m.SizeBytes),
                    ExportTypeBreakdown = filteredMetrics.GroupBy(m => m.ExportType).ToDictionary(g => g.Key, g => g.Count())
                };
            }
        }
    }

    public class ExportMetric
    {
        public string ExportType { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan? Duration { get; set; }
        public long SizeBytes { get; set; }
        public int RecordCount { get; set; }
        public DateTime Timestamp { get; set; }
        public string MetricType { get; set; } = string.Empty;
    }

    public class ExportMetrics
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public double AverageDuration { get; set; }
        public long TotalDataExported { get; set; }
        public Dictionary<string, int> ExportTypeBreakdown { get; set; } = new();
    }
}
