using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AnalyticsBIService.Application.Services;

/// <summary>
/// Service for sending notifications and alerts
/// </summary>
public interface INotificationService
{
    Task SendAlertNotificationAsync(AlertDto alert);
    Task SendKPINotificationAsync(Guid userId, string kpiName, decimal currentValue, decimal targetValue);
    Task SendReportNotificationAsync(Guid userId, string reportType, string reportUrl);
    Task SendThresholdNotificationAsync(Guid userId, string metricName, decimal currentValue, decimal thresholdValue);
    Task<List<NotificationDto>> GetNotificationsAsync(Guid userId, int page = 1, int pageSize = 50);
    Task MarkNotificationAsReadAsync(Guid notificationId);
    Task<NotificationPreferencesDto> GetNotificationPreferencesAsync(Guid userId);
    Task UpdateNotificationPreferencesAsync(Guid userId, NotificationPreferencesDto preferences);
}

public class NotificationService : INotificationService
{
    private readonly IAlertRepository _alertRepository;
    private readonly IEmailService _emailService;
    private readonly ISMSService _smsService;
    private readonly IPushNotificationService _pushNotificationService;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(
        IAlertRepository alertRepository,
        IEmailService emailService,
        ISMSService smsService,
        IPushNotificationService pushNotificationService,
        ILogger<NotificationService> logger)
    {
        _alertRepository = alertRepository;
        _emailService = emailService;
        _smsService = smsService;
        _pushNotificationService = pushNotificationService;
        _logger = logger;
    }

    public async Task SendAlertNotificationAsync(AlertDto alert)
    {
        try
        {
            _logger.LogInformation("Sending alert notification for alert {AlertId} to user {UserId}",
                alert.Id, alert.UserId);

            // Get user notification preferences
            var preferences = await GetNotificationPreferencesAsync(alert.UserId ?? Guid.Empty);

            // Create alert record using domain entity
            var alertEntity = new Alert(
                alert.Title ?? "Alert",
                alert.Message ?? "Alert notification",
                AlertSeverity.Medium, // Map from alert.Severity
                alert.Message ?? "Alert notification",
                context: new Dictionary<string, object> { { "UserId", alert.UserId }, { "AlertId", alert.Id } });

            await _alertRepository.AddAsync(alertEntity);

            // Send notifications based on preferences and severity
            await SendNotificationByChannels(alertEntity, preferences, alert.Severity);

            _logger.LogInformation("Successfully sent alert notification {AlertId}", alertEntity.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending alert notification for alert {AlertId}", alert.Id);
            throw;
        }
    }

    public async Task SendKPINotificationAsync(Guid userId, string kpiName, decimal currentValue, decimal targetValue)
    {
        try
        {
            var preferences = await GetNotificationPreferencesAsync(userId);

            if (!preferences.KPINotificationsEnabled)
            {
                return;
            }

            var alertEntity = new Alert(
                $"KPI Update: {kpiName}",
                $"KPI '{kpiName}' is now {currentValue} (Target: {targetValue})",
                AlertSeverity.Low,
                $"KPI '{kpiName}' is now {currentValue} (Target: {targetValue})",
                metricName: kpiName,
                triggerValue: currentValue,
                thresholdValue: targetValue,
                context: new Dictionary<string, object>
                {
                    { "UserId", userId },
                    { "KPIName", kpiName },
                    { "CurrentValue", currentValue },
                    { "TargetValue", targetValue }
                });

            await _alertRepository.AddAsync(alertEntity);

            // Send based on preferences
            if (preferences.EmailNotificationsEnabled)
            {
                await _emailService.SendKPIUpdateEmailAsync(userId, kpiName, currentValue, targetValue);
            }

            if (preferences.PushNotificationsEnabled)
            {
                await _pushNotificationService.SendKPIUpdateAsync(userId, kpiName, currentValue, targetValue);
            }

            _logger.LogInformation("Sent KPI notification for {KPIName} to user {UserId}", kpiName, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending KPI notification for {KPIName} to user {UserId}", kpiName, userId);
            throw;
        }
    }

    public async Task SendReportNotificationAsync(Guid userId, string reportType, string reportUrl)
    {
        try
        {
            var preferences = await GetNotificationPreferencesAsync(userId);

            if (!preferences.ReportNotificationsEnabled)
            {
                return;
            }

            var alertEntity = new Alert(
                $"Report Ready: {reportType}",
                $"Your {reportType} report is ready for download",
                AlertSeverity.Low,
                $"Your {reportType} report is ready for download",
                context: new Dictionary<string, object>
                {
                    { "UserId", userId },
                    { "ReportType", reportType },
                    { "ReportUrl", reportUrl }
                });

            await _alertRepository.AddAsync(alertEntity);

            // Send based on preferences
            if (preferences.EmailNotificationsEnabled)
            {
                await _emailService.SendReportReadyEmailAsync(userId, reportType, reportUrl);
            }

            if (preferences.PushNotificationsEnabled)
            {
                await _pushNotificationService.SendReportReadyAsync(userId, reportType, reportUrl);
            }

            _logger.LogInformation("Sent report notification for {ReportType} to user {UserId}", reportType, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending report notification for {ReportType} to user {UserId}", reportType, userId);
            throw;
        }
    }

    public async Task SendThresholdNotificationAsync(Guid userId, string metricName, decimal currentValue, decimal thresholdValue)
    {
        try
        {
            var preferences = await GetNotificationPreferencesAsync(userId);

            if (!preferences.ThresholdNotificationsEnabled)
            {
                return;
            }

            var alertEntity = new Alert(
                $"Threshold Alert: {metricName}",
                $"Metric '{metricName}' has reached {currentValue}, exceeding threshold of {thresholdValue}",
                AlertSeverity.High,
                $"Metric '{metricName}' has reached {currentValue}, exceeding threshold of {thresholdValue}",
                metricName: metricName,
                triggerValue: currentValue,
                thresholdValue: thresholdValue,
                context: new Dictionary<string, object>
                {
                    { "UserId", userId },
                    { "MetricName", metricName },
                    { "CurrentValue", currentValue },
                    { "ThresholdValue", thresholdValue }
                });

            await _alertRepository.AddAsync(alertEntity);

            // Send based on preferences
            if (preferences.EmailNotificationsEnabled)
            {
                await _emailService.SendThresholdAlertEmailAsync(userId, metricName, currentValue, thresholdValue);
            }

            if (preferences.SMSNotificationsEnabled)
            {
                await _smsService.SendThresholdAlertSMSAsync(userId, metricName, currentValue, thresholdValue);
            }

            if (preferences.PushNotificationsEnabled)
            {
                await _pushNotificationService.SendThresholdAlertAsync(userId, metricName, currentValue, thresholdValue);
            }

            _logger.LogInformation("Sent threshold notification for {MetricName} to user {UserId}", metricName, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending threshold notification for {MetricName} to user {UserId}", metricName, userId);
            throw;
        }
    }

    public async Task<List<NotificationDto>> GetNotificationsAsync(Guid userId, int page = 1, int pageSize = 50)
    {
        try
        {
            var alertsResult = await _alertRepository.GetByUserIdAsync(userId, page, pageSize);

            return alertsResult.Items.Select(alert => new NotificationDto
            {
                Id = alert.Id,
                UserId = alert.Context.ContainsKey("UserId") ? (Guid)alert.Context["UserId"] : userId,
                Type = "Alert",
                Title = alert.Name,
                Message = alert.Message,
                Data = alert.Context.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                IsRead = alert.IsAcknowledged,
                CreatedAt = alert.TriggeredAt,
                ReadAt = alert.AcknowledgedAt
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notifications for user {UserId}", userId);
            throw;
        }
    }

    public async Task MarkNotificationAsReadAsync(Guid notificationId)
    {
        try
        {
            var alert = await _alertRepository.GetByIdAsync(notificationId);
            if (alert == null)
            {
                throw new InvalidOperationException($"Notification {notificationId} not found");
            }

            await _alertRepository.AcknowledgeAlertAsync(notificationId, Guid.Empty, "Marked as read");

            _logger.LogDebug("Marked notification {NotificationId} as read", notificationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification {NotificationId} as read", notificationId);
            throw;
        }
    }

    public async Task<NotificationPreferencesDto> GetNotificationPreferencesAsync(Guid userId)
    {
        try
        {
            // Return default preferences since NotificationPreferences entity doesn't exist
            // In a real implementation, this would be stored in a separate preferences system
            await Task.CompletedTask; // Async placeholder

            return new NotificationPreferencesDto
            {
                UserId = userId.ToString(),
                EmailNotificationsEnabled = true,
                SMSNotificationsEnabled = false,
                PushNotificationsEnabled = true,
                AlertNotificationsEnabled = true,
                KPINotificationsEnabled = true,
                ReportNotificationsEnabled = true,
                ThresholdNotificationsEnabled = true,
                AlertSeverityFilter = "Warning", // Minimum severity
                NotificationFrequency = "Immediate",
                QuietHoursEnabled = false,
                QuietHoursStart = TimeSpan.FromHours(22),
                QuietHoursEnd = TimeSpan.FromHours(8)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification preferences for user {UserId}", userId);
            throw;
        }
    }

    public async Task UpdateNotificationPreferencesAsync(Guid userId, NotificationPreferencesDto preferences)
    {
        try
        {
            // Since NotificationPreferences entity doesn't exist, this is a no-op
            // In a real implementation, this would be stored in a separate preferences system
            await Task.CompletedTask; // Async placeholder

            _logger.LogInformation("Updated notification preferences for user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification preferences for user {UserId}", userId);
            throw;
        }
    }

    private async Task SendNotificationByChannels(Alert alert, NotificationPreferencesDto preferences, string severity)
    {
        // Check if we're in quiet hours
        if (preferences.QuietHoursEnabled && IsInQuietHours(preferences))
        {
            // Only send critical alerts during quiet hours
            if (severity != "Critical")
            {
                return;
            }
        }

        // Check severity filter
        if (!ShouldSendBasedOnSeverity(severity, preferences.AlertSeverityFilter))
        {
            return;
        }

        // Get userId from alert context
        var userId = alert.Context.ContainsKey("UserId") ? (Guid)alert.Context["UserId"] : Guid.Empty;

        // Send via enabled channels
        if (preferences.EmailNotificationsEnabled)
        {
            await _emailService.SendAlertEmailAsync(userId, alert.Name, alert.Message);
        }

        if (preferences.SMSNotificationsEnabled && severity == "Critical")
        {
            await _smsService.SendAlertSMSAsync(userId, alert.Name, alert.Message);
        }

        if (preferences.PushNotificationsEnabled)
        {
            await _pushNotificationService.SendAlertAsync(userId, alert.Name, alert.Message);
        }
    }

    private bool IsInQuietHours(NotificationPreferencesDto preferences)
    {
        var now = DateTime.Now.TimeOfDay;

        if (preferences.QuietHoursStart < preferences.QuietHoursEnd)
        {
            // Same day quiet hours (e.g., 22:00 to 08:00 next day)
            return now >= preferences.QuietHoursStart || now <= preferences.QuietHoursEnd;
        }
        else
        {
            // Overnight quiet hours (e.g., 22:00 to 08:00)
            return now >= preferences.QuietHoursStart && now <= preferences.QuietHoursEnd;
        }
    }

    private bool ShouldSendBasedOnSeverity(string alertSeverity, string filterSeverity)
    {
        var severityLevels = new Dictionary<string, int>
        {
            { "Info", 1 },
            { "Warning", 2 },
            { "Critical", 3 }
        };

        var alertLevel = severityLevels.GetValueOrDefault(alertSeverity, 1);
        var filterLevel = severityLevels.GetValueOrDefault(filterSeverity, 1);

        return alertLevel >= filterLevel;
    }
}

// Supporting service interfaces (would be implemented separately)
public interface IEmailService
{
    Task SendAlertEmailAsync(Guid userId, string title, string message);
    Task SendKPIUpdateEmailAsync(Guid userId, string kpiName, decimal currentValue, decimal targetValue);
    Task SendReportReadyEmailAsync(Guid userId, string reportType, string reportUrl);
    Task SendThresholdAlertEmailAsync(Guid userId, string metricName, decimal currentValue, decimal thresholdValue);
}

public interface ISMSService
{
    Task SendAlertSMSAsync(Guid userId, string title, string message);
    Task SendThresholdAlertSMSAsync(Guid userId, string metricName, decimal currentValue, decimal thresholdValue);
}

public interface IPushNotificationService
{
    Task SendAlertAsync(Guid userId, string title, string message);
    Task SendKPIUpdateAsync(Guid userId, string kpiName, decimal currentValue, decimal targetValue);
    Task SendReportReadyAsync(Guid userId, string reportType, string reportUrl);
    Task SendThresholdAlertAsync(Guid userId, string metricName, decimal currentValue, decimal thresholdValue);
}
