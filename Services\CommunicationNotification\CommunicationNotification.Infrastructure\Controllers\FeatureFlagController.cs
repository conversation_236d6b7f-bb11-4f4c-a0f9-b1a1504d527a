using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Feature flag management API controller
/// </summary>
[ApiController]
[Route("api/communication/feature-flags")]
[Authorize]
public class FeatureFlagController : ControllerBase
{
    private readonly IFeatureFlagService _featureFlagService;
    private readonly IFeatureFlagAnalyticsService _analyticsService;

    public FeatureFlagController(
        IFeatureFlagService featureFlagService,
        IFeatureFlagAnalyticsService analyticsService)
    {
        _featureFlagService = featureFlagService;
        _analyticsService = analyticsService;
    }

    /// <summary>
    /// Create a new feature flag
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> CreateFeatureFlag([FromBody] CreateFeatureFlagRequest request)
    {
        var userId = GetCurrentUserId();

        var featureFlag = await _featureFlagService.CreateFeatureFlagAsync(
            request.Key,
            request.Name,
            request.Description,
            request.Type,
            request.Environment,
            userId,
            request.StartDate,
            request.EndDate,
            request.RolloutConfiguration,
            request.TargetingRules,
            request.DefaultValue,
            request.Configuration);

        return Ok(featureFlag);
    }

    /// <summary>
    /// Get feature flag by key
    /// </summary>
    [HttpGet("{key}")]
    public async Task<IActionResult> GetFeatureFlag(string key, [FromQuery] string? environment = null)
    {
        var featureFlag = await _featureFlagService.GetFeatureFlagAsync(key, environment);
        if (featureFlag == null)
        {
            return NotFound($"Feature flag {key} not found");
        }

        return Ok(featureFlag);
    }

    /// <summary>
    /// Get feature flag by ID
    /// </summary>
    [HttpGet("id/{id}")]
    public async Task<IActionResult> GetFeatureFlagById(Guid id)
    {
        var featureFlag = await _featureFlagService.GetFeatureFlagByIdAsync(id);
        if (featureFlag == null)
        {
            return NotFound($"Feature flag {id} not found");
        }

        return Ok(featureFlag);
    }

    /// <summary>
    /// Get all feature flags
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAllFeatureFlags(
        [FromQuery] string? environment = null,
        [FromQuery] FeatureFlagStatus? status = null)
    {
        var featureFlags = await _featureFlagService.GetAllFeatureFlagsAsync(environment, status);
        return Ok(featureFlags);
    }

    /// <summary>
    /// Update feature flag
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> UpdateFeatureFlag(Guid id, [FromBody] UpdateFeatureFlagRequest request)
    {
        var userId = GetCurrentUserId();

        var featureFlag = await _featureFlagService.UpdateFeatureFlagAsync(
            id,
            request.Name,
            request.Description,
            request.RolloutConfiguration,
            request.TargetingRules,
            request.DefaultValue,
            request.Configuration,
            userId);

        return Ok(featureFlag);
    }

    /// <summary>
    /// Delete feature flag
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> DeleteFeatureFlag(Guid id)
    {
        await _featureFlagService.DeleteFeatureFlagAsync(id);
        return NoContent();
    }

    /// <summary>
    /// Add variant to feature flag
    /// </summary>
    [HttpPost("{id}/variants")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> AddVariant(Guid id, [FromBody] AddVariantRequest request)
    {
        var featureFlag = await _featureFlagService.AddVariantAsync(
            id,
            request.Name,
            request.Description,
            request.TrafficAllocation,
            request.Value,
            request.Configuration,
            request.IsControl);

        return Ok(featureFlag);
    }

    /// <summary>
    /// Remove variant from feature flag
    /// </summary>
    [HttpDelete("{id}/variants/{variantName}")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> RemoveVariant(Guid id, string variantName)
    {
        var featureFlag = await _featureFlagService.RemoveVariantAsync(id, variantName);
        return Ok(featureFlag);
    }

    /// <summary>
    /// Activate feature flag
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> ActivateFeatureFlag(Guid id)
    {
        var userId = GetCurrentUserId();
        var featureFlag = await _featureFlagService.ActivateFeatureFlagAsync(id, userId);
        return Ok(featureFlag);
    }

    /// <summary>
    /// Pause feature flag
    /// </summary>
    [HttpPost("{id}/pause")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> PauseFeatureFlag(Guid id)
    {
        var userId = GetCurrentUserId();
        var featureFlag = await _featureFlagService.PauseFeatureFlagAsync(id, userId);
        return Ok(featureFlag);
    }

    /// <summary>
    /// Complete feature flag
    /// </summary>
    [HttpPost("{id}/complete")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> CompleteFeatureFlag(Guid id)
    {
        var userId = GetCurrentUserId();
        var featureFlag = await _featureFlagService.CompleteFeatureFlagAsync(id, userId);
        return Ok(featureFlag);
    }

    /// <summary>
    /// Archive feature flag
    /// </summary>
    [HttpPost("{id}/archive")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> ArchiveFeatureFlag(Guid id)
    {
        var userId = GetCurrentUserId();
        var featureFlag = await _featureFlagService.ArchiveFeatureFlagAsync(id, userId);
        return Ok(featureFlag);
    }

    /// <summary>
    /// Check if feature flag is enabled for user
    /// </summary>
    [HttpGet("{key}/enabled")]
    public async Task<IActionResult> IsEnabled(
        string key,
        [FromQuery] Guid? userId = null,
        [FromQuery] string? environment = null,
        [FromQuery] string? context = null)
    {
        var targetUserId = userId ?? GetCurrentUserId();
        var contextDict = ParseContext(context);

        var isEnabled = await _featureFlagService.IsEnabledAsync(key, targetUserId, contextDict, environment);
        return Ok(new { enabled = isEnabled });
    }

    /// <summary>
    /// Get feature flag value for user
    /// </summary>
    [HttpGet("{key}/value")]
    public async Task<IActionResult> GetValue(
        string key,
        [FromQuery] Guid? userId = null,
        [FromQuery] string? environment = null,
        [FromQuery] string? context = null)
    {
        var targetUserId = userId ?? GetCurrentUserId();
        var contextDict = ParseContext(context);

        var value = await _featureFlagService.GetValueAsync<object>(key, targetUserId, null, contextDict, environment);
        return Ok(new { value });
    }

    /// <summary>
    /// Get variant assignment for user in A/B test
    /// </summary>
    [HttpGet("{key}/variant")]
    public async Task<IActionResult> GetVariant(
        string key,
        [FromQuery] Guid? userId = null,
        [FromQuery] string? environment = null,
        [FromQuery] string? context = null)
    {
        var targetUserId = userId ?? GetCurrentUserId();
        var contextDict = ParseContext(context);

        var variant = await _featureFlagService.GetVariantAsync(key, targetUserId, contextDict, environment);
        return Ok(new { variant });
    }

    /// <summary>
    /// Get multiple feature flag values for user
    /// </summary>
    [HttpPost("values")]
    public async Task<IActionResult> GetMultipleValues([FromBody] GetMultipleValuesRequest request)
    {
        var targetUserId = request.UserId ?? GetCurrentUserId();

        var values = await _featureFlagService.GetMultipleValuesAsync(
            request.Keys, targetUserId, request.Context, request.Environment);

        return Ok(values);
    }

    /// <summary>
    /// Record feature flag usage
    /// </summary>
    [HttpPost("{key}/usage")]
    public async Task<IActionResult> RecordUsage(string key, [FromBody] RecordUsageRequest request)
    {
        var targetUserId = request.UserId ?? GetCurrentUserId();

        await _featureFlagService.RecordUsageAsync(
            key, targetUserId, request.Variant, request.Context, request.Environment);

        return Ok(new { success = true });
    }

    /// <summary>
    /// Get feature flag metrics
    /// </summary>
    [HttpGet("{id}/metrics")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GetMetrics(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var metrics = await _featureFlagService.GetMetricsAsync(id, startDate, endDate);
        return Ok(metrics);
    }

    /// <summary>
    /// Get feature flag usage history
    /// </summary>
    [HttpGet("{id}/usage-history")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GetUsageHistory(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? limit = null)
    {
        var usageHistory = await _featureFlagService.GetUsageHistoryAsync(id, startDate, endDate, limit);
        return Ok(usageHistory);
    }

    /// <summary>
    /// Bulk update rollout percentage
    /// </summary>
    [HttpPost("bulk-rollout")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> BulkUpdateRollout([FromBody] BulkUpdateRolloutRequest request)
    {
        var userId = GetCurrentUserId();

        await _featureFlagService.BulkUpdateRolloutAsync(request.FeatureFlagIds, request.Percentage, userId);
        return Ok(new { success = true, message = $"Updated rollout for {request.FeatureFlagIds.Count} feature flags" });
    }

    /// <summary>
    /// Clone feature flag to different environment
    /// </summary>
    [HttpPost("{id}/clone")]
    [Authorize(Roles = "Admin,FeatureFlagManager")]
    public async Task<IActionResult> CloneFeatureFlag(Guid id, [FromBody] CloneFeatureFlagRequest request)
    {
        var userId = GetCurrentUserId();

        var clonedFlag = await _featureFlagService.CloneFeatureFlagAsync(
            id, request.TargetEnvironment, request.NewKey, userId);

        return Ok(clonedFlag);
    }

    /// <summary>
    /// Get variant performance comparison
    /// </summary>
    [HttpGet("{id}/analytics/variants")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GetVariantPerformance(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var performance = await _analyticsService.GetVariantPerformanceAsync(id, startDate, endDate);
        return Ok(performance);
    }

    /// <summary>
    /// Get feature flag adoption metrics
    /// </summary>
    [HttpGet("{id}/analytics/adoption")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GetAdoptionMetrics(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var adoption = await _analyticsService.GetAdoptionMetricsAsync(id, startDate, endDate);
        return Ok(adoption);
    }

    /// <summary>
    /// Get rollout impact analysis
    /// </summary>
    [HttpGet("{id}/analytics/impact")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GetRolloutImpact(
        Guid id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        var impact = await _analyticsService.GetRolloutImpactAsync(id, startDate, endDate);
        return Ok(impact);
    }

    /// <summary>
    /// Generate feature flag insights
    /// </summary>
    [HttpGet("{id}/analytics/insights")]
    [Authorize(Roles = "Admin,FeatureFlagManager,Analyst")]
    public async Task<IActionResult> GenerateInsights(Guid id)
    {
        var insights = await _analyticsService.GenerateInsightsAsync(id);
        return Ok(insights);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private Dictionary<string, object>? ParseContext(string? contextJson)
    {
        if (string.IsNullOrEmpty(contextJson))
            return null;

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(contextJson);
        }
        catch
        {
            return null;
        }
    }
}

// Request/Response DTOs
public class CreateFeatureFlagRequest
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public FeatureFlagType Type { get; set; }
    public string Environment { get; set; } = string.Empty;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public RolloutConfiguration? RolloutConfiguration { get; set; }
    public TargetingRules? TargetingRules { get; set; }
    public Dictionary<string, object>? DefaultValue { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
}

public class UpdateFeatureFlagRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public RolloutConfiguration? RolloutConfiguration { get; set; }
    public TargetingRules? TargetingRules { get; set; }
    public Dictionary<string, object>? DefaultValue { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
}

public class AddVariantRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal TrafficAllocation { get; set; }
    public object? Value { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public bool IsControl { get; set; }
}

public class GetMultipleValuesRequest
{
    public List<string> Keys { get; set; } = new();
    public Guid? UserId { get; set; }
    public Dictionary<string, object>? Context { get; set; }
    public string? Environment { get; set; }
}

public class RecordUsageRequest
{
    public Guid? UserId { get; set; }
    public string? Variant { get; set; }
    public Dictionary<string, object>? Context { get; set; }
    public string? Environment { get; set; }
}

public class BulkUpdateRolloutRequest
{
    public List<Guid> FeatureFlagIds { get; set; } = new();
    public decimal Percentage { get; set; }
}

public class CloneFeatureFlagRequest
{
    public string TargetEnvironment { get; set; } = string.Empty;
    public string? NewKey { get; set; }
}
