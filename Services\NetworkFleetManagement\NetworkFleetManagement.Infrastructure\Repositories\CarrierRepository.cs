using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class CarrierRepository : ICarrierRepository
{
    private readonly NetworkFleetDbContext _context;

    public CarrierRepository(NetworkFleetDbContext context)
    {
        _context = context;
    }

    public async Task<Carrier?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Include(c => c.Documents)
            .Include(c => c.NetworkRelationships)
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Carrier?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Include(c => c.Documents)
            .Include(c => c.NetworkRelationships)
            .FirstOrDefaultAsync(c => c.UserId == userId, cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> GetByStatusAsync(CarrierStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Where(c => c.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> GetByOnboardingStatusAsync(OnboardingStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Where(c => c.OnboardingStatus == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> GetActiveCarriersAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Where(c => c.Status == CarrierStatus.Active)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> GetCarriersWithExpiredDocumentsAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        
        return await _context.Carriers
            .Include(c => c.Documents)
            .Where(c => c.Documents.Any(d => d.ExpiryDate.HasValue && d.ExpiryDate.Value <= today))
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Carrier>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var lowerSearchTerm = searchTerm.ToLower();
        
        return await _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .Where(c => c.CompanyName.ToLower().Contains(lowerSearchTerm) ||
                       c.ContactPersonName.ToLower().Contains(lowerSearchTerm) ||
                       c.Email.ToLower().Contains(lowerSearchTerm) ||
                       c.PhoneNumber.Contains(searchTerm))
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Carrier> Carriers, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null,
        CarrierStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Carriers
            .Include(c => c.Vehicles)
            .Include(c => c.Drivers)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(c => c.CompanyName.ToLower().Contains(lowerSearchTerm) ||
                                   c.ContactPersonName.ToLower().Contains(lowerSearchTerm) ||
                                   c.Email.ToLower().Contains(lowerSearchTerm) ||
                                   c.PhoneNumber.Contains(searchTerm));
        }

        if (status.HasValue)
        {
            query = query.Where(c => c.Status == status.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var carriers = await query
            .OrderBy(c => c.CompanyName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (carriers, totalCount);
    }

    public void Add(Carrier carrier)
    {
        _context.Carriers.Add(carrier);
    }

    public void Update(Carrier carrier)
    {
        _context.Carriers.Update(carrier);
    }

    public void Remove(Carrier carrier)
    {
        _context.Carriers.Remove(carrier);
    }
}
