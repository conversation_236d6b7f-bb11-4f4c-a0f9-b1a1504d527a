{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error", "AnalyticsBIService": "Information"}}, "ConnectionStrings": {"DefaultConnection": "${DATABASE_CONNECTION_STRING}", "TimescaleConnection": "${TIMESCALE_CONNECTION_STRING}", "Redis": "${REDIS_CONNECTION_STRING}", "RabbitMQ": "${RABBITMQ_CONNECTION_STRING}"}, "JwtSettings": {"Secret": "${JWT_SECRET}", "Issuer": "${JWT_ISSUER}", "Audience": "${JWT_AUDIENCE}", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "${RABBITMQ_HOST}", "Port": "${RABBITMQ_PORT}", "Username": "${RABBITMQ_USERNAME}", "Password": "${RABBITMQ_PASSWORD}"}, "AnalyticsSettings": {"DataRetentionDays": "${ANALYTICS_DATA_RETENTION_DAYS}", "MetricAggregationInterval": "${ANALYTICS_METRIC_AGGREGATION_INTERVAL}", "ReportGenerationTimeout": "${ANALYTICS_REPORT_TIMEOUT}", "MaxConcurrentReports": "${ANALYTICS_MAX_CONCURRENT_REPORTS}", "DefaultPageSize": 50, "MaxPageSize": 1000, "EnableRealTimeAnalytics": "${ANALYTICS_ENABLE_REALTIME}", "EnablePredictiveAnalytics": "${ANALYTICS_ENABLE_PREDICTIVE}", "CacheExpirationMinutes": "${ANALYTICS_CACHE_EXPIRATION_MINUTES}"}, "ReportSettings": {"DefaultExportFormat": "PDF", "MaxReportSize": "${REPORT_MAX_SIZE}", "ReportStoragePath": "${REPORT_STORAGE_PATH}", "ReportRetentionDays": "${REPORT_RETENTION_DAYS}", "EnableScheduledReports": "${REPORT_ENABLE_SCHEDULED}", "MaxScheduledReportsPerUser": "${REPORT_MAX_SCHEDULED_PER_USER}", "ReportGenerationConcurrency": "${REPORT_GENERATION_CONCURRENCY}"}, "DashboardSettings": {"MaxWidgetsPerDashboard": "${DASHBOARD_MAX_WIDGETS}", "DefaultRefreshInterval": "${DASHBOARD_REFRESH_INTERVAL}", "MaxDashboardsPerUser": "${DASHBOARD_MAX_PER_USER}", "EnableRealTimeDashboards": "${DASHBOARD_ENABLE_REALTIME}", "CacheWidgetData": "${DASHBOARD_CACHE_WIDGETS}", "WidgetCacheExpirationMinutes": "${DASHBOARD_WIDGET_CACHE_EXPIRATION}"}, "AlertSettings": {"EnableAlerting": "${ALERT_ENABLE}", "AlertEvaluationInterval": "${ALERT_EVALUATION_INTERVAL}", "MaxAlertsPerHour": "${ALERT_MAX_PER_HOUR}", "AlertRetentionDays": "${ALERT_RETENTION_DAYS}", "DefaultCooldownMinutes": "${ALERT_DEFAULT_COOLDOWN}", "MaxEscalationLevels": "${ALERT_MAX_ESCALATION_LEVELS}"}, "ExportSettings": {"SupportedFormats": ["PDF", "Excel", "CSV", "JSON"], "MaxExportSize": "${EXPORT_MAX_SIZE}", "ExportTimeout": "${EXPORT_TIMEOUT}", "ExportStoragePath": "${EXPORT_STORAGE_PATH}", "ExportRetentionDays": "${EXPORT_RETENTION_DAYS}", "EnableAsyncExports": "${EXPORT_ENABLE_ASYNC}"}, "MLSettings": {"EnableMachineLearning": "${ML_ENABLE}", "ModelTrainingInterval": "${ML_TRAINING_INTERVAL}", "PredictionCacheExpirationHours": "${ML_PREDICTION_CACHE_HOURS}", "AnomalyDetectionThreshold": "${ML_ANOMALY_THRESHOLD}", "ModelStoragePath": "${ML_MODEL_STORAGE_PATH}", "MaxTrainingDataPoints": "${ML_MAX_TRAINING_DATA_POINTS}"}, "PerformanceSettings": {"QueryTimeout": "${PERFORMANCE_QUERY_TIMEOUT}", "MaxConcurrentQueries": "${PERFORMANCE_MAX_CONCURRENT_QUERIES}", "EnableQueryOptimization": "${PERFORMANCE_ENABLE_QUERY_OPTIMIZATION}", "EnableDataCompression": "${PERFORMANCE_ENABLE_DATA_COMPRESSION}", "BatchSize": "${PERFORMANCE_BATCH_SIZE}", "ParallelProcessing": "${PERFORMANCE_PARALLEL_PROCESSING}"}, "SecuritySettings": {"EnableDataMasking": "${SECURITY_ENABLE_DATA_MASKING}", "EnableAuditLogging": "${SECURITY_ENABLE_AUDIT_LOGGING}", "RequireDataAccess": "${SECURITY_REQUIRE_DATA_ACCESS}", "EnableRowLevelSecurity": "${SECURITY_ENABLE_ROW_LEVEL_SECURITY}", "DataClassificationEnabled": "${SECURITY_ENABLE_DATA_CLASSIFICATION}"}, "Services": {"Identity": {"BaseUrl": "${IDENTITY_SERVICE_URL}"}, "DataStorage": {"BaseUrl": "${DATASTORAGE_SERVICE_URL}"}, "AuditCompliance": {"BaseUrl": "${AUDITCOMPLIANCE_SERVICE_URL}"}, "CommunicationNotification": {"BaseUrl": "${COMMUNICATION_SERVICE_URL}"}, "MonitoringObservability": {"BaseUrl": "${MONITORING_SERVICE_URL}"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "System": "Error", "AnalyticsBIService": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/analytics-bi-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}