# Use the official .NET 8 runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Use the official .NET 8 SDK as a build image
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/DataStorage/DataStorage.API/DataStorage.API.csproj", "Services/DataStorage/DataStorage.API/"]
COPY ["Services/DataStorage/DataStorage.Application/DataStorage.Application.csproj", "Services/DataStorage/DataStorage.Application/"]
COPY ["Services/DataStorage/DataStorage.Domain/DataStorage.Domain.csproj", "Services/DataStorage/DataStorage.Domain/"]
COPY ["Services/DataStorage/DataStorage.Infrastructure/DataStorage.Infrastructure.csproj", "Services/DataStorage/DataStorage.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/DataStorage/DataStorage.API/DataStorage.API.csproj"

# Copy the rest of the source code
COPY . .

# Build the application
WORKDIR "/src/Services/DataStorage/DataStorage.API"
RUN dotnet build "DataStorage.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "DataStorage.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app

# Create storage directory
RUN mkdir -p /app/storage

# Copy published application
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080

ENTRYPOINT ["dotnet", "DataStorage.API.dll"]
