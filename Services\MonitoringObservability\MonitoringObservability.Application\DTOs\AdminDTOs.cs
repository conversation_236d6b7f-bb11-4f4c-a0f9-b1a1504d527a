using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.DTOs;

// Missing DTOs for Admin Commands
public class SystemOverviewDto
{
    public int TotalServices { get; set; }
    public int HealthyServices { get; set; }
    public int UnhealthyServices { get; set; }
    public int TotalAlerts { get; set; }
    public int CriticalAlerts { get; set; }
    public double SystemUptime { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class AlertStatisticsDto
{
    public Dictionary<AlertSeverity, int> CountBySeverity { get; set; } = new();
    public Dictionary<string, int> CountByService { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class EscalationRuleDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public AlertSeverity TriggerSeverity { get; set; }
    public TimeSpan EscalationDelay { get; set; }
    public List<EscalationLevelDto> EscalationLevels { get; set; } = new();
    public bool IsEnabled { get; set; }
}

public class EscalationLevelDto
{
    public int Level { get; set; }
    public TimeSpan DelayFromPrevious { get; set; }
    public List<string> NotificationTargets { get; set; } = new();
    public List<NotificationChannel> Channels { get; set; } = new();
}

public class SystemOptimizationResultDto
{
    public string OptimizationType { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string Description { get; set; } = string.Empty;
    public double ImprovementPercentage { get; set; }
    public DateTime ExecutedAt { get; set; }
}

public class AlertingTuningResultDto
{
    public int TotalThresholdsOptimized { get; set; }
    public int SuccessfulOptimizations { get; set; }
    public int FailedOptimizations { get; set; }
    public List<string> OptimizedMetrics { get; set; } = new();
    public DateTime ExecutedAt { get; set; }
}

public class NotificationConfigurationDto
{
    public EmailSettingsDto EmailSettings { get; set; } = null!;
    public SMSSettingsDto SMSSettings { get; set; } = null!;
    public SlackSettingsDto SlackSettings { get; set; } = null!;
    public TeamsSettingsDto TeamsSettings { get; set; } = null!;
    public WebhookSettingsDto WebhookSettings { get; set; } = null!;
    public Dictionary<AlertSeverity, List<NotificationChannel>> SeverityChannelMapping { get; set; } = new();
}

public class EmailSettingsDto
{
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; }
    public string Username { get; set; } = string.Empty;
    public bool UseSSL { get; set; }
    public string FromAddress { get; set; } = string.Empty;
}

public class SMSSettingsDto
{
    public string Provider { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public string FromNumber { get; set; } = string.Empty;
}

public class SlackSettingsDto
{
    public string WebhookUrl { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string BotToken { get; set; } = string.Empty;
}

public class TeamsSettingsDto
{
    public string WebhookUrl { get; set; } = string.Empty;
    public string ChannelId { get; set; } = string.Empty;
}

public class WebhookSettingsDto
{
    public string Url { get; set; } = string.Empty;
    public Dictionary<string, string> Headers { get; set; } = new();
    public string AuthToken { get; set; } = string.Empty;
}

public class NotificationTestResultDto
{
    public NotificationChannel Channel { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public DateTime TestedAt { get; set; }
}

public class ScheduledReportDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public List<string> Recipients { get; set; } = new();
    public bool IsEnabled { get; set; }
}

public class DataRestoreResultDto
{
    public bool Success { get; set; }
    public string DataType { get; set; } = string.Empty;
    public int RecordsRestored { get; set; }
    public TimeSpan Duration { get; set; }
    public DateTime RestoredAt { get; set; }
}

public class SecurityAuditResultDto
{
    public string CheckType { get; set; } = string.Empty;
    public bool Passed { get; set; }
    public string Description { get; set; } = string.Empty;
    public List<string> Issues { get; set; } = new();
    public DateTime AuditedAt { get; set; }
}

public class MaintenanceExecutionResultDto
{
    public string TaskName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string Description { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public DateTime ExecutedAt { get; set; }
}

public class ComplianceValidationResultDto
{
    public string Standard { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public double ComplianceScore { get; set; }
    public List<string> Violations { get; set; } = new();
    public DateTime ValidatedAt { get; set; }
}

public class UserManagementResultDto
{
    public string Action { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime ExecutedAt { get; set; }
}

public class UserNotificationPreferencesDto
{
    public Guid UserId { get; set; }
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<AlertSeverity, List<NotificationChannel>> SeverityPreferences { get; set; } = new();
    public bool EnableQuietHours { get; set; }
    public TimeSpan QuietHoursStart { get; set; }
    public TimeSpan QuietHoursEnd { get; set; }
}

public class NotificationStatisticsDto
{
    public int TotalNotificationsSent { get; set; }
    public Dictionary<NotificationChannel, int> NotificationsByChannel { get; set; } = new();
    public Dictionary<AlertSeverity, int> NotificationsBySeverity { get; set; } = new();
    public double DeliverySuccessRate { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class NotificationHistoryDto
{
    public Guid Id { get; set; }
    public string Recipient { get; set; } = string.Empty;
    public NotificationChannel Channel { get; set; }
    public string Subject { get; set; } = string.Empty;
    public bool Delivered { get; set; }
    public DateTime SentAt { get; set; }
}

public class MaintenanceRecommendationsDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string RecommendationType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime RecommendedBy { get; set; }
}

public class ScheduledMaintenanceDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime ScheduledStart { get; set; }
    public DateTime ScheduledEnd { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class SystemHealthTrendDto
{
    public string ServiceName { get; set; } = string.Empty;
    public List<HealthDataPointDto> HealthDataPoints { get; set; } = new();
    public string TrendDirection { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

public class HealthDataPointDto
{
    public DateTime Timestamp { get; set; }
    public double HealthScore { get; set; }
    public HealthStatus Status { get; set; }
}

public class PerformanceBenchmarksDto
{
    public string ServiceName { get; set; } = string.Empty;
    public double ResponseTimeBenchmark { get; set; }
    public double ThroughputBenchmark { get; set; }
    public double ErrorRateBenchmark { get; set; }
    public DateTime BenchmarkedAt { get; set; }
}

public class DataArchiveResultDto
{
    public string DataType { get; set; } = string.Empty;
    public int RecordsArchived { get; set; }
    public long SizeArchived { get; set; }
    public DateTime ArchivedAt { get; set; }
    public bool Success { get; set; }
}

public class MaintenanceHistoryDto
{
    public Guid Id { get; set; }
    public string TaskName { get; set; } = string.Empty;
    public MaintenanceTaskType TaskType { get; set; }
    public DateTime ExecutedAt { get; set; }
    public bool Success { get; set; }
    public string Notes { get; set; } = string.Empty;
}

public class ComplianceStatusDto
{
    public string Standard { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public double CompliancePercentage { get; set; }
    public DateTime LastChecked { get; set; }
}

public class SystemBottleneckDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public double UtilizationPercentage { get; set; }
    public string Description { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
}

public class UserActivityDto
{
    public Guid UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Activity { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string IpAddress { get; set; } = string.Empty;
}

public enum MaintenanceTaskType
{
    DatabaseMaintenance = 0,
    SystemUpdate = 1,
    SecurityPatch = 2,
    PerformanceOptimization = 3,
    BackupVerification = 4,
    ConfigurationUpdate = 5
}

public enum ReportFormat
{
    Json = 0,
    Xml = 1,
    Csv = 2,
    Excel = 3,
    Pdf = 4
}

public class MonitoringUserDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime LastLogin { get; set; }
}

public class DataRetentionStatusDto
{
    public string DataType { get; set; } = string.Empty;
    public int RetentionDays { get; set; }
    public long CurrentSize { get; set; }
    public DateTime OldestRecord { get; set; }
    public DateTime LastCleanup { get; set; }
}

public class ReportHistoryDto
{
    public Guid Id { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

// Admin Dashboard DTOs
public class AdminDashboardDto
{
    public SystemOverviewDto SystemOverview { get; set; } = null!;
    public AlertStatisticsDto AlertStatistics { get; set; } = null!;
    public IncidentStatisticsDto IncidentStatistics { get; set; } = null!;
    public HealthStatusSummaryDto HealthStatusSummary { get; set; } = null!;
    public List<ServiceHealthSummaryDto> ServicesHealth { get; set; } = new();
    public TimeSpan TimeRange { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class IncidentStatisticsDto
{
    public Dictionary<IncidentSeverity, int> CountBySeverity { get; set; } = new();
    public Dictionary<string, int> CountByService { get; set; } = new();
    public double AverageResolutionTimeMinutes { get; set; }
    public int TotalIncidents { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class HealthStatusSummaryDto
{
    public Dictionary<HealthStatus, int> StatusCounts { get; set; } = new();
    public int TotalHealthChecks { get; set; }
    public double HealthyPercentage { get; set; }
    public DateTime GeneratedAt { get; set; }
}

// Performance Analysis DTOs
public class PerformanceAnalysisDto
{
    public TimeSpan AnalysisPeriod { get; set; }
    public List<ServicePerformanceDto> ServicePerformances { get; set; } = new();
    public List<PerformanceRecommendationDto> Recommendations { get; set; } = new();
    public List<PerformanceAnomalyDto> Anomalies { get; set; } = new();
    public OverallPerformanceScoreDto OverallScore { get; set; } = null!;
    public DateTime GeneratedAt { get; set; }
}

public class ServicePerformanceDto
{
    public string ServiceName { get; set; } = string.Empty;
    public double AverageResponseTime { get; set; }
    public double Throughput { get; set; }
    public double ErrorRate { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public PerformanceGrade Grade { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class PerformanceRecommendationDto
{
    public string ServiceName { get; set; } = string.Empty;
    public RecommendationType Type { get; set; }
    public RecommendationPriority Priority { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ActionRequired { get; set; } = string.Empty;
    public double EstimatedImpact { get; set; }
    public TimeSpan EstimatedEffort { get; set; }
    public DateTime IdentifiedAt { get; set; }
}

public class PerformanceAnomalyDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double ExpectedValue { get; set; }
    public double ActualValue { get; set; }
    public double DeviationPercentage { get; set; }
    public AnomalySeverity Severity { get; set; }
    public DateTime DetectedAt { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class OverallPerformanceScoreDto
{
    public double Score { get; set; } // 0-100
    public PerformanceGrade Grade { get; set; }
    public string Summary { get; set; } = string.Empty;
    public Dictionary<string, double> ComponentScores { get; set; } = new();
    public List<string> KeyIssues { get; set; } = new();
    public List<string> TopRecommendations { get; set; } = new();
}

// Detailed Metrics DTOs
public class DetailedMetricsDto
{
    public TimeSpan Period { get; set; }
    public List<string> ServiceNames { get; set; } = new();
    public MetricType? MetricType { get; set; }
    public List<ServiceMetricsDetailDto> ServiceMetrics { get; set; } = new();
    public List<MetricTrendDto> Trends { get; set; } = new();
    public List<MetricAnomalyDto> Anomalies { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class ServiceMetricsDetailDto
{
    public string ServiceName { get; set; } = string.Empty;
    public List<MetricDetailDto> Metrics { get; set; } = new();
    public MetricsSummaryDto Summary { get; set; } = null!;
}

public class MetricDetailDto
{
    public Guid MetricId { get; set; }
    public string Name { get; set; } = string.Empty;
    public MetricType Type { get; set; }
    public string Unit { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double AverageValue { get; set; }
    public double MinValue { get; set; }
    public double MaxValue { get; set; }
    public List<MetricDataPointDto> DataPoints { get; set; } = new();
    public AlertThresholdDto? Threshold { get; set; }
    public bool IsAlerting { get; set; }
}

public class MetricTrendDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public TrendDirection Direction { get; set; }
    public double TrendPercentage { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool IsSignificant { get; set; }
}

public class MetricAnomalyDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public double ExpectedValue { get; set; }
    public double ActualValue { get; set; }
    public double Confidence { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class MetricsSummaryDto
{
    public int TotalMetrics { get; set; }
    public int ActiveMetrics { get; set; }
    public int AlertingMetrics { get; set; }
    public int MetricsWithIssues { get; set; }
    public double OverallHealthScore { get; set; }
}

// Bulk Action DTOs
public class BulkActionResultDto
{
    public int TotalRequested { get; set; }
    public int SuccessfulUpdates { get; set; }
    public int FailedUpdates { get; set; }
    public List<AlertDto> UpdatedItems { get; set; } = new();
    public string ActionPerformed { get; set; } = string.Empty;
    public DateTime PerformedAt { get; set; }
    public Guid PerformedBy { get; set; }
    public List<string> Errors { get; set; } = new();
}

// Threshold Configuration DTOs
public class ThresholdConfigurationDto
{
    public string MetricName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public double WarningThreshold { get; set; }
    public double CriticalThreshold { get; set; }
    public string Operator { get; set; } = string.Empty; // >, <, >=, <=, ==
    public TimeSpan EvaluationWindow { get; set; }
    public bool Enabled { get; set; } = true;
}

public class ThresholdConfigurationResultDto
{
    public int TotalConfigurations { get; set; }
    public int SuccessfulConfigurations { get; set; }
    public int FailedConfigurations { get; set; }
    public List<string> ConfiguredMetrics { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public DateTime ConfiguredAt { get; set; }
}

// Capacity Planning DTOs
public class CapacityPlanningDto
{
    public TimeSpan AnalysisWindow { get; set; }
    public TimeSpan ProjectionPeriod { get; set; }
    public List<ServiceCapacityDto> ServiceCapacities { get; set; } = new();
    public List<ResourceRecommendationDto> ResourceRecommendations { get; set; } = new();
    public List<ScalingRecommendationDto> ScalingRecommendations { get; set; } = new();
    public CapacityForecastDto OverallForecast { get; set; } = null!;
    public DateTime GeneratedAt { get; set; }
}

public class ServiceCapacityDto
{
    public string ServiceName { get; set; } = string.Empty;
    public ServiceResourceUtilizationDto CurrentUtilization { get; set; } = null!;
    public ServiceResourceUtilizationDto ProjectedUtilization { get; set; } = null!;
    public double GrowthRate { get; set; }
    public DateTime EstimatedCapacityExhaustion { get; set; }
    public CapacityStatus Status { get; set; }
    public List<string> Recommendations { get; set; } = new();
}

public class ServiceResourceUtilizationDto
{
    public double CpuUtilization { get; set; }
    public double MemoryUtilization { get; set; }
    public double DiskUtilization { get; set; }
    public double NetworkUtilization { get; set; }
    public double RequestsPerSecond { get; set; }
    public DateTime MeasuredAt { get; set; }
}

public class ResourceRecommendationDto
{
    public string ServiceName { get; set; } = string.Empty;
    public ResourceType ResourceType { get; set; }
    public string CurrentAllocation { get; set; } = string.Empty;
    public string RecommendedAllocation { get; set; } = string.Empty;
    public string Justification { get; set; } = string.Empty;
    public double EstimatedCostImpact { get; set; }
    public RecommendationPriority Priority { get; set; }
}

public class ScalingRecommendationDto
{
    public string ServiceName { get; set; } = string.Empty;
    public ScalingType ScalingType { get; set; }
    public int CurrentInstances { get; set; }
    public int RecommendedInstances { get; set; }
    public string TriggerCondition { get; set; } = string.Empty;
    public TimeSpan RecommendedTimeframe { get; set; }
    public double ConfidenceLevel { get; set; }
}

public class CapacityForecastDto
{
    public double OverallCapacityUtilization { get; set; }
    public DateTime EstimatedCapacityExhaustion { get; set; }
    public List<CapacityMilestoneDto> Milestones { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

public class CapacityMilestoneDto
{
    public DateTime Date { get; set; }
    public double UtilizationPercentage { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool RequiresAction { get; set; }
}

// System Report DTOs
public class SystemReportDto
{
    public ReportType ReportType { get; set; }
    public TimeSpan Period { get; set; }
    public List<string> ServiceNames { get; set; } = new();
    public ReportFormat Format { get; set; }
    public string ReportContent { get; set; } = string.Empty; // JSON, Base64 for binary formats
    public ReportSummaryDto Summary { get; set; } = null!;
    public DateTime GeneratedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
}

public class ReportSummaryDto
{
    public string Title { get; set; } = string.Empty;
    public string ExecutiveSummary { get; set; } = string.Empty;
    public List<string> KeyFindings { get; set; } = new();
    public List<string> CriticalIssues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, object> Statistics { get; set; } = new();
}

// Enums for admin features
public enum PerformanceGrade
{
    Excellent = 0,
    Good = 1,
    Fair = 2,
    Poor = 3,
    Critical = 4
}

public enum RecommendationType
{
    Performance = 0,
    Capacity = 1,
    Security = 2,
    Reliability = 3,
    Cost = 4,
    Maintenance = 5
}



public enum AnomalySeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}



public enum CapacityStatus
{
    Healthy = 0,
    Warning = 1,
    Critical = 2,
    Exhausted = 3
}



public enum ScalingType
{
    HorizontalUp = 0,
    HorizontalDown = 1,
    VerticalUp = 2,
    VerticalDown = 3
}
