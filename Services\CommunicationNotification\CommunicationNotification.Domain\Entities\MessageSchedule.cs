using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Advanced message scheduling entity with complex rules and optimization
/// </summary>
public class MessageSchedule : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public MessageType MessageType { get; private set; }
    public ScheduleType ScheduleType { get; private set; }
    public ScheduleStatus Status { get; private set; }
    public DateTime? ScheduledAt { get; private set; }
    public RecurrencePattern? RecurrencePattern { get; private set; }
    public List<SchedulingRule> Rules { get; private set; }
    public DeliveryWindow DeliveryWindow { get; private set; }
    public OptimizationSettings OptimizationSettings { get; private set; }
    public List<string> TargetUserIds { get; private set; }
    public List<string> TargetSegments { get; private set; }
    public MessageContent Content { get; private set; }
    public string? TemplateId { get; private set; }
    public Dictionary<string, object> TemplateParameters { get; private set; }
    public NotificationChannel? PreferredChannel { get; private set; }
    public Priority Priority { get; private set; }
    public List<string> TimeZones { get; private set; }
    public ScheduleMetrics Metrics { get; private set; }
    public Guid CreatedByUserId { get; private set; }
    public DateTime? LastExecutedAt { get; private set; }
    public DateTime? NextExecutionAt { get; private set; }
    public int ExecutionCount { get; private set; }
    public int MaxExecutions { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    private MessageSchedule()
    {
        Name = string.Empty;
        Description = string.Empty;
        Rules = new List<SchedulingRule>();
        TargetUserIds = new List<string>();
        TargetSegments = new List<string>();
        TemplateParameters = new Dictionary<string, object>();
        TimeZones = new List<string>();
        Content = MessageContent.Empty;
        DeliveryWindow = DeliveryWindow.Default();
        OptimizationSettings = OptimizationSettings.Default();
        Metrics = ScheduleMetrics.Empty();
    }

    public MessageSchedule(
        string name,
        string description,
        MessageType messageType,
        ScheduleType scheduleType,
        MessageContent content,
        Guid createdByUserId,
        DateTime? scheduledAt = null,
        RecurrencePattern? recurrencePattern = null,
        List<SchedulingRule>? rules = null,
        DeliveryWindow? deliveryWindow = null,
        OptimizationSettings? optimizationSettings = null,
        List<string>? targetUserIds = null,
        List<string>? targetSegments = null,
        string? templateId = null,
        Dictionary<string, object>? templateParameters = null,
        NotificationChannel? preferredChannel = null,
        Priority priority = Priority.Normal,
        List<string>? timeZones = null,
        int maxExecutions = int.MaxValue,
        DateTime? expiresAt = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Schedule name cannot be empty", nameof(name));
        if (createdByUserId == Guid.Empty)
            throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

        Name = name;
        Description = description;
        MessageType = messageType;
        ScheduleType = scheduleType;
        Status = ScheduleStatus.Draft;
        ScheduledAt = scheduledAt;
        RecurrencePattern = recurrencePattern;
        Rules = rules ?? new List<SchedulingRule>();
        DeliveryWindow = deliveryWindow ?? DeliveryWindow.Default();
        OptimizationSettings = optimizationSettings ?? OptimizationSettings.Default();
        TargetUserIds = targetUserIds ?? new List<string>();
        TargetSegments = targetSegments ?? new List<string>();
        Content = content;
        TemplateId = templateId;
        TemplateParameters = templateParameters ?? new Dictionary<string, object>();
        PreferredChannel = preferredChannel;
        Priority = priority;
        TimeZones = timeZones ?? new List<string> { "UTC" };
        Metrics = ScheduleMetrics.Empty();
        CreatedByUserId = createdByUserId;
        ExecutionCount = 0;
        MaxExecutions = maxExecutions;
        ExpiresAt = expiresAt;

        CalculateNextExecution();
    }

    public static MessageSchedule Create(
        string name,
        string description,
        MessageType messageType,
        ScheduleType scheduleType,
        MessageContent content,
        Guid createdByUserId,
        DateTime? scheduledAt = null,
        RecurrencePattern? recurrencePattern = null,
        List<SchedulingRule>? rules = null,
        DeliveryWindow? deliveryWindow = null,
        OptimizationSettings? optimizationSettings = null,
        List<string>? targetUserIds = null,
        List<string>? targetSegments = null,
        string? templateId = null,
        Dictionary<string, object>? templateParameters = null,
        NotificationChannel? preferredChannel = null,
        Priority priority = Priority.Normal,
        List<string>? timeZones = null,
        int maxExecutions = int.MaxValue,
        DateTime? expiresAt = null)
    {
        return new MessageSchedule(name, description, messageType, scheduleType, content, createdByUserId,
            scheduledAt, recurrencePattern, rules, deliveryWindow, optimizationSettings, targetUserIds,
            targetSegments, templateId, templateParameters, preferredChannel, priority, timeZones,
            maxExecutions, expiresAt);
    }

    public void AddRule(SchedulingRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        if (Status != ScheduleStatus.Draft)
            throw new InvalidOperationException("Cannot modify rules when schedule is not in draft status");

        Rules.Add(rule);
        CalculateNextExecution();
    }

    public void RemoveRule(Guid ruleId)
    {
        if (Status != ScheduleStatus.Draft)
            throw new InvalidOperationException("Cannot modify rules when schedule is not in draft status");

        var rule = Rules.FirstOrDefault(r => r.Id == ruleId);
        if (rule != null)
        {
            Rules.Remove(rule);
            CalculateNextExecution();
        }
    }

    public void UpdateDeliveryWindow(DeliveryWindow deliveryWindow)
    {
        DeliveryWindow = deliveryWindow ?? throw new ArgumentNullException(nameof(deliveryWindow));
        CalculateNextExecution();
    }

    public void UpdateOptimizationSettings(OptimizationSettings optimizationSettings)
    {
        OptimizationSettings = optimizationSettings ?? throw new ArgumentNullException(nameof(optimizationSettings));
    }

    public void AddTargetUser(string userId)
    {
        if (string.IsNullOrWhiteSpace(userId))
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        if (!TargetUserIds.Contains(userId))
        {
            TargetUserIds.Add(userId);
        }
    }

    public void RemoveTargetUser(string userId)
    {
        TargetUserIds.Remove(userId);
    }

    public void AddTargetSegment(string segment)
    {
        if (string.IsNullOrWhiteSpace(segment))
            throw new ArgumentException("Segment cannot be empty", nameof(segment));

        if (!TargetSegments.Contains(segment))
        {
            TargetSegments.Add(segment);
        }
    }

    public void RemoveTargetSegment(string segment)
    {
        TargetSegments.Remove(segment);
    }

    public void Activate()
    {
        if (Status != ScheduleStatus.Draft && Status != ScheduleStatus.Paused)
            throw new InvalidOperationException($"Cannot activate schedule in {Status} status");

        if (!TargetUserIds.Any() && !TargetSegments.Any())
            throw new InvalidOperationException("Cannot activate schedule without target users or segments");

        Status = ScheduleStatus.Active;
        CalculateNextExecution();
    }

    public void Pause()
    {
        if (Status != ScheduleStatus.Active)
            throw new InvalidOperationException($"Cannot pause schedule in {Status} status");

        Status = ScheduleStatus.Paused;
    }

    public void Complete()
    {
        Status = ScheduleStatus.Completed;
        NextExecutionAt = null;
    }

    public void Cancel()
    {
        Status = ScheduleStatus.Cancelled;
        NextExecutionAt = null;
    }

    public void MarkExecuted()
    {
        LastExecutedAt = DateTime.UtcNow;
        ExecutionCount++;

        if (ExecutionCount >= MaxExecutions)
        {
            Complete();
        }
        else if (RecurrencePattern != null)
        {
            CalculateNextExecution();
        }
        else
        {
            Complete();
        }
    }

    public void UpdateMetrics(ScheduleMetrics metrics)
    {
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
    }

    public bool IsReadyForExecution()
    {
        if (Status != ScheduleStatus.Active)
            return false;

        if (NextExecutionAt == null)
            return false;

        if (NextExecutionAt > DateTime.UtcNow)
            return false;

        if (ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value)
        {
            Cancel();
            return false;
        }

        return true;
    }

    public bool IsWithinDeliveryWindow(DateTime dateTime, string timeZone)
    {
        return DeliveryWindow.IsWithinWindow(dateTime, timeZone);
    }

    public DateTime? GetOptimalDeliveryTime(string userId, string timeZone)
    {
        if (!IsReadyForExecution())
            return null;

        var baseTime = NextExecutionAt!.Value;

        // Apply optimization settings
        if (OptimizationSettings.EnableUserBehaviorOptimization)
        {
            // This would integrate with user behavior analytics
            // For now, return a simulated optimal time
            baseTime = GetOptimalTimeBasedOnUserBehavior(userId, baseTime, timeZone);
        }

        // Ensure it's within delivery window
        if (!IsWithinDeliveryWindow(baseTime, timeZone))
        {
            baseTime = DeliveryWindow.GetNextValidTime(baseTime, timeZone);
        }

        return baseTime;
    }

    private void CalculateNextExecution()
    {
        if (Status != ScheduleStatus.Active && Status != ScheduleStatus.Draft)
        {
            NextExecutionAt = null;
            return;
        }

        if (ScheduleType == ScheduleType.Immediate)
        {
            NextExecutionAt = DateTime.UtcNow;
            return;
        }

        if (ScheduleType == ScheduleType.OneTime && ScheduledAt.HasValue)
        {
            NextExecutionAt = ExecutionCount == 0 ? ScheduledAt.Value : null;
            return;
        }

        if (ScheduleType == ScheduleType.Recurring && RecurrencePattern != null)
        {
            var baseTime = LastExecutedAt ?? DateTime.UtcNow;
            NextExecutionAt = RecurrencePattern.GetNextOccurrence(baseTime);
            return;
        }

        NextExecutionAt = null;
    }

    private DateTime GetOptimalTimeBasedOnUserBehavior(string userId, DateTime baseTime, string timeZone)
    {
        // Simulate user behavior optimization
        // In a real implementation, this would query user analytics
        var random = new Random();
        var hourOffset = random.Next(-2, 3); // Adjust by -2 to +2 hours

        var optimizedTime = baseTime.AddHours(hourOffset);

        // Ensure it's within reasonable hours (8 AM to 8 PM in user's timezone)
        var userLocalTime = TimeZoneInfo.ConvertTimeFromUtc(optimizedTime, GetTimeZoneInfo(timeZone));

        if (userLocalTime.Hour < 8)
            optimizedTime = optimizedTime.AddHours(8 - userLocalTime.Hour);
        else if (userLocalTime.Hour > 20)
            optimizedTime = optimizedTime.AddHours(20 - userLocalTime.Hour);

        return optimizedTime;
    }

    private TimeZoneInfo GetTimeZoneInfo(string timeZone)
    {
        try
        {
            return TimeZoneInfo.FindSystemTimeZoneById(timeZone);
        }
        catch
        {
            return TimeZoneInfo.Utc;
        }
    }
}

/// <summary>
/// Scheduling rule for complex conditions
/// </summary>
public class SchedulingRule
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public RuleType Type { get; private set; }
    public string Condition { get; private set; }
    public string Action { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    private SchedulingRule()
    {
        Name = string.Empty;
        Condition = string.Empty;
        Action = string.Empty;
        Parameters = new Dictionary<string, object>();
    }

    public SchedulingRule(
        string name,
        RuleType type,
        string condition,
        string action,
        bool isEnabled = true,
        int priority = 0,
        Dictionary<string, object>? parameters = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Rule name cannot be empty", nameof(name));

        Id = Guid.NewGuid();
        Name = name;
        Type = type;
        Condition = condition;
        Action = action;
        IsEnabled = isEnabled;
        Priority = priority;
        Parameters = parameters ?? new Dictionary<string, object>();
    }

    public void Enable()
    {
        IsEnabled = true;
    }

    public void Disable()
    {
        IsEnabled = false;
    }

    public void UpdatePriority(int priority)
    {
        Priority = priority;
    }
}

/// <summary>
/// Schedule status enumeration
/// </summary>
public enum ScheduleStatus
{
    Draft = 1,
    Active = 2,
    Paused = 3,
    Completed = 4,
    Cancelled = 5,
    Error = 6
}

/// <summary>
/// Schedule type enumeration
/// </summary>
public enum ScheduleType
{
    Immediate = 1,
    OneTime = 2,
    Recurring = 3,
    Conditional = 4,
    Optimized = 5
}

/// <summary>
/// Rule type enumeration
/// </summary>
public enum RuleType
{
    TimeBasedCondition = 1,
    UserBehaviorCondition = 2,
    SystemEventCondition = 3,
    DeliveryOptimization = 4,
    ChannelSelection = 5,
    ContentPersonalization = 6
}


