﻿using Shared.Domain.ValueObjects;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.ValueObjects;

/// <summary>
/// Configuration for delay alert system
/// </summary>
public class DelayAlertConfiguration : ValueObject
{
    public bool IsEnabled { get; private set; }
    public List<DelayThreshold> DelayThresholds { get; private set; } = new();
    public List<TimeSpan> EscalationIntervals { get; private set; } = new();
    public int MaxEscalationLevel { get; private set; }
    public List<string> NotificationChannels { get; private set; } = new();
    public List<string> EscalationRecipients { get; private set; } = new();
    public bool AutoAcknowledge { get; private set; }
    public TimeSpan? AutoAcknowledgeDelay { get; private set; }
    public bool AutoResolve { get; private set; }
    public TimeSpan? AutoResolveDelay { get; private set; }
    public int? AlertExpiryHours { get; private set; }
    public Dictionary<string, object> CustomSettings { get; private set; } = new();

    private DelayAlertConfiguration() { }

    public DelayAlertConfiguration(
        bool isEnabled = true,
        List<DelayThreshold>? delayThresholds = null,
        List<TimeSpan>? escalationIntervals = null,
        int maxEscalationLevel = 3,
        List<string>? notificationChannels = null,
        List<string>? escalationRecipients = null,
        bool autoAcknowledge = false,
        TimeSpan? autoAcknowledgeDelay = null,
        bool autoResolve = false,
        TimeSpan? autoResolveDelay = null,
        int? alertExpiryHours = null,
        Dictionary<string, object>? customSettings = null)
    {
        IsEnabled = isEnabled;
        DelayThresholds = delayThresholds ?? GetDefaultDelayThresholds();
        EscalationIntervals = escalationIntervals ?? GetDefaultEscalationIntervals();
        MaxEscalationLevel = maxEscalationLevel;
        NotificationChannels = notificationChannels ?? new List<string> { "Email", "SMS" };
        EscalationRecipients = escalationRecipients ?? new List<string>();
        AutoAcknowledge = autoAcknowledge;
        AutoAcknowledgeDelay = autoAcknowledgeDelay;
        AutoResolve = autoResolve;
        AutoResolveDelay = autoResolveDelay;
        AlertExpiryHours = alertExpiryHours;
        CustomSettings = customSettings ?? new Dictionary<string, object>();
    }

    public static DelayAlertConfiguration ForTransportCompany()
    {
        return new DelayAlertConfiguration(
            isEnabled: true,
            delayThresholds: new List<DelayThreshold>
            {
                new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Low, TimeSpan.FromMinutes(15)),
                new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Medium, TimeSpan.FromMinutes(30)),
                new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.High, TimeSpan.FromMinutes(60)),
                new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Critical, TimeSpan.FromMinutes(120)),
                new DelayThreshold(DelayAlertType.StopDelay, DelayAlertSeverity.Low, TimeSpan.FromMinutes(10)),
                new DelayThreshold(DelayAlertType.StopDelay, DelayAlertSeverity.Medium, TimeSpan.FromMinutes(20)),
                new DelayThreshold(DelayAlertType.StopDelay, DelayAlertSeverity.High, TimeSpan.FromMinutes(45)),
                new DelayThreshold(DelayAlertType.StopDelay, DelayAlertSeverity.Critical, TimeSpan.FromMinutes(90))
            },
            escalationIntervals: new List<TimeSpan>
            {
                TimeSpan.FromMinutes(30), // Level 1
                TimeSpan.FromMinutes(60), // Level 2
                TimeSpan.FromMinutes(120) // Level 3
            },
            maxEscalationLevel: 3,
            notificationChannels: new List<string> { "Email", "SMS", "Push" },
            autoAcknowledge: false,
            autoResolve: true,
            autoResolveDelay: TimeSpan.FromHours(24),
            alertExpiryHours: 48);
    }

    public TimeSpan GetEscalationThreshold(int escalationLevel)
    {
        if (escalationLevel < 0 || escalationLevel >= EscalationIntervals.Count)
            return EscalationIntervals.LastOrDefault();

        return EscalationIntervals[escalationLevel];
    }

    public DelayThreshold? GetThresholdForSeverity(DelayAlertType alertType, DelayAlertSeverity severity)
    {
        return DelayThresholds.FirstOrDefault(t => t.AlertType == alertType && t.Severity == severity);
    }

    private static List<DelayThreshold> GetDefaultDelayThresholds()
    {
        return new List<DelayThreshold>
        {
            new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Low, TimeSpan.FromMinutes(15)),
            new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Medium, TimeSpan.FromMinutes(30)),
            new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.High, TimeSpan.FromMinutes(60)),
            new DelayThreshold(DelayAlertType.TripDelay, DelayAlertSeverity.Critical, TimeSpan.FromMinutes(120))
        };
    }

    private static List<TimeSpan> GetDefaultEscalationIntervals()
    {
        return new List<TimeSpan>
        {
            TimeSpan.FromMinutes(30),
            TimeSpan.FromMinutes(60),
            TimeSpan.FromMinutes(120)
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsEnabled;
        yield return MaxEscalationLevel;
        yield return AutoAcknowledge;
        yield return AutoResolve;
        yield return AlertExpiryHours ?? 0;
    }
}

/// <summary>
/// Delay threshold configuration
/// </summary>
public class DelayThreshold : ValueObject
{
    public DelayAlertType AlertType { get; private set; }
    public DelayAlertSeverity Severity { get; private set; }
    public TimeSpan Threshold { get; private set; }
    public bool IsEnabled { get; private set; }

    private DelayThreshold() { }

    public DelayThreshold(DelayAlertType alertType, DelayAlertSeverity severity, TimeSpan threshold, bool isEnabled = true)
    {
        if (threshold <= TimeSpan.Zero)
            throw new ArgumentException("Threshold must be positive", nameof(threshold));

        AlertType = alertType;
        Severity = severity;
        Threshold = threshold;
        IsEnabled = isEnabled;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AlertType;
        yield return Severity;
        yield return Threshold;
        yield return IsEnabled;
    }
}

/// <summary>
/// Impact assessment for delay alerts
/// </summary>
public class DelayImpactAssessment : ValueObject
{
    public decimal FinancialImpact { get; private set; }
    public string CustomerImpact { get; private set; } = string.Empty;
    public string OperationalImpact { get; private set; } = string.Empty;
    public int AffectedShipments { get; private set; }
    public int AffectedCustomers { get; private set; }
    public List<string> MitigationActions { get; private set; } = new();
    public DateTime AssessedAt { get; private set; }
    public Guid AssessedByUserId { get; private set; }

    private DelayImpactAssessment() { }

    public DelayImpactAssessment(
        decimal financialImpact,
        string customerImpact,
        string operationalImpact,
        int affectedShipments,
        int affectedCustomers,
        List<string> mitigationActions,
        Guid assessedByUserId)
    {
        FinancialImpact = financialImpact;
        CustomerImpact = customerImpact ?? throw new ArgumentNullException(nameof(customerImpact));
        OperationalImpact = operationalImpact ?? throw new ArgumentNullException(nameof(operationalImpact));
        AffectedShipments = affectedShipments;
        AffectedCustomers = affectedCustomers;
        MitigationActions = mitigationActions ?? new List<string>();
        AssessedAt = DateTime.UtcNow;
        AssessedByUserId = assessedByUserId;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FinancialImpact;
        yield return CustomerImpact;
        yield return OperationalImpact;
        yield return AffectedShipments;
        yield return AffectedCustomers;
        yield return AssessedAt;
        yield return AssessedByUserId;
    }
}

/// <summary>
/// Summary information about a delay alert
/// </summary>
public class DelayAlertSummary : ValueObject
{
    public Guid Id { get; init; }
    public Guid TripId { get; init; }
    public DelayAlertType AlertType { get; init; }
    public DelayAlertSeverity Severity { get; init; }
    public DelayAlertStatus Status { get; init; }
    public TimeSpan DelayDuration { get; init; }
    public int EscalationLevel { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? AcknowledgedAt { get; init; }
    public DateTime? ResolvedAt { get; init; }
    public int NotificationCount { get; init; }
    public bool IsExpired { get; init; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Id;
        yield return TripId;
        yield return AlertType;
        yield return Severity;
        yield return Status;
        yield return DelayDuration;
        yield return EscalationLevel;
        yield return CreatedAt;
    }
}

/// <summary>
/// Analytics data for delay alerts
/// </summary>
public class DelayAlertAnalytics : ValueObject
{
    public int TotalAlerts { get; private set; }
    public int ActiveAlerts { get; private set; }
    public int AcknowledgedAlerts { get; private set; }
    public int ResolvedAlerts { get; private set; }
    public int CancelledAlerts { get; private set; }
    public double AverageDelayDuration { get; private set; } // in minutes
    public double AverageResolutionTime { get; private set; } // in minutes
    public Dictionary<DelayAlertSeverity, int> AlertsBySeverity { get; private set; } = new();
    public Dictionary<DelayReason, int> AlertsByReason { get; private set; } = new();
    public Dictionary<string, int> AlertsByHour { get; private set; } = new();
    public Dictionary<string, decimal> FinancialImpactByDay { get; private set; } = new();

    private DelayAlertAnalytics() { }

    public DelayAlertAnalytics(
        int totalAlerts,
        int activeAlerts,
        int acknowledgedAlerts,
        int resolvedAlerts,
        int cancelledAlerts,
        double averageDelayDuration,
        double averageResolutionTime,
        Dictionary<DelayAlertSeverity, int>? alertsBySeverity = null,
        Dictionary<DelayReason, int>? alertsByReason = null,
        Dictionary<string, int>? alertsByHour = null,
        Dictionary<string, decimal>? financialImpactByDay = null)
    {
        TotalAlerts = totalAlerts;
        ActiveAlerts = activeAlerts;
        AcknowledgedAlerts = acknowledgedAlerts;
        ResolvedAlerts = resolvedAlerts;
        CancelledAlerts = cancelledAlerts;
        AverageDelayDuration = averageDelayDuration;
        AverageResolutionTime = averageResolutionTime;
        AlertsBySeverity = alertsBySeverity ?? new Dictionary<DelayAlertSeverity, int>();
        AlertsByReason = alertsByReason ?? new Dictionary<DelayReason, int>();
        AlertsByHour = alertsByHour ?? new Dictionary<string, int>();
        FinancialImpactByDay = financialImpactByDay ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalAlerts;
        yield return ActiveAlerts;
        yield return AcknowledgedAlerts;
        yield return ResolvedAlerts;
        yield return CancelledAlerts;
        yield return AverageDelayDuration;
        yield return AverageResolutionTime;
    }
}


