using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Metric entity
/// </summary>
public class MetricConfiguration : IEntityTypeConfiguration<Metric>
{
    public void Configure(EntityTypeBuilder<Metric> builder)
    {
        // Table configuration
        builder.ToTable("metrics");

        // Primary key
        builder.HasKey(m => m.Id);
        builder.Property(m => m.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Basic properties
        builder.Property(m => m.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(m => m.Description)
            .HasColumnName("description")
            .HasMaxLength(1000);

        builder.Property(m => m.Category)
            .HasColumnName("category")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(m => m.Type)
            .HasColumnName("type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(m => m.UserId)
            .HasColumnName("user_id");

        builder.Property(m => m.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(true);

        builder.Property(m => m.Tags)
            .HasColumnName("tags")
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("jsonb");

        // Audit fields from BaseEntity
        builder.Property(m => m.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(m => m.UpdatedAt)
            .HasColumnName("updated_at");

        // Value object configurations are handled in DbContext
        // MetricValue, TimePeriodValue, and KPITarget are configured there

        // Indexes
        builder.HasIndex(m => new { m.Name, m.UserId })
            .HasDatabaseName("IX_Metrics_Name_UserId")
            .IsUnique();

        builder.HasIndex(m => new { m.Category, m.Type })
            .HasDatabaseName("IX_Metrics_Category_Type");

        builder.HasIndex(m => m.UserId)
            .HasDatabaseName("IX_Metrics_UserId");

        builder.HasIndex(m => m.CreatedAt)
            .HasDatabaseName("IX_Metrics_CreatedAt");

        builder.HasIndex(m => m.IsActive)
            .HasDatabaseName("IX_Metrics_IsActive");

        // Constraints
        builder.HasCheckConstraint("CK_Metrics_Name_NotEmpty", "LENGTH(name) > 0");
        builder.HasCheckConstraint("CK_Metrics_Category_NotEmpty", "LENGTH(category) > 0");
    }
}
