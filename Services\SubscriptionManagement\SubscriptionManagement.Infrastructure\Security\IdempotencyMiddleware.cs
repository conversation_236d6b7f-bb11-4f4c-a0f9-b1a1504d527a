using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Security
{
    public class IdempotencyMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<IdempotencyMiddleware> _logger;
        private readonly IdempotencyOptions _options;

        public IdempotencyMiddleware(
            RequestDelegate next,
            IMemoryCache cache,
            ILogger<IdempotencyMiddleware> logger,
            IdempotencyOptions options)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply idempotency to POST requests on specific endpoints
            if (!ShouldApplyIdempotency(context.Request))
            {
                await _next(context);
                return;
            }

            var idempotencyKey = GetIdempotencyKey(context.Request);
            if (string.IsNullOrEmpty(idempotencyKey))
            {
                await _next(context);
                return;
            }

            var requestHash = await GetRequestHash(context.Request);
            var cacheKey = $"idempotency:{idempotencyKey}:{requestHash}";

            // Check if we have a cached response
            if (_cache.TryGetValue(cacheKey, out IdempotentResponse? cachedResponse) && cachedResponse != null)
            {
                _logger.LogInformation("Returning cached response for idempotency key: {IdempotencyKey}", idempotencyKey);
                
                context.Response.StatusCode = cachedResponse.StatusCode;
                context.Response.ContentType = cachedResponse.ContentType;
                
                foreach (var header in cachedResponse.Headers)
                {
                    context.Response.Headers.Add(header.Key, header.Value);
                }
                
                context.Response.Headers.Add("X-Idempotent-Replay", "true");
                
                if (!string.IsNullOrEmpty(cachedResponse.Body))
                {
                    await context.Response.WriteAsync(cachedResponse.Body);
                }
                
                return;
            }

            // Capture the response
            var originalBodyStream = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            await _next(context);

            // Cache the response if it's successful
            if (context.Response.StatusCode >= 200 && context.Response.StatusCode < 300)
            {
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                var responseBody = await new StreamReader(responseBodyStream).ReadToEndAsync();

                var response = new IdempotentResponse
                {
                    StatusCode = context.Response.StatusCode,
                    ContentType = context.Response.ContentType ?? "application/json",
                    Body = responseBody,
                    Headers = context.Response.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())
                };

                _cache.Set(cacheKey, response, TimeSpan.FromMinutes(_options.CacheMinutes));
                
                _logger.LogInformation("Cached response for idempotency key: {IdempotencyKey}", idempotencyKey);
            }

            // Copy the response back to the original stream
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalBodyStream);
        }

        private bool ShouldApplyIdempotency(HttpRequest request)
        {
            if (request.Method != HttpMethods.Post)
                return false;

            var path = request.Path.Value?.ToLowerInvariant() ?? "";
            return path.Contains("/api/admin/notifications") || 
                   path.Contains("/api/admin/payment-proofs") ||
                   (path.Contains("/api/subscriptions") && path.Contains("/payment-proof"));
        }

        private string? GetIdempotencyKey(HttpRequest request)
        {
            // Check for idempotency key in headers
            if (request.Headers.TryGetValue("Idempotency-Key", out var headerValue))
            {
                return headerValue.FirstOrDefault();
            }

            // Check for idempotency key in query parameters
            if (request.Query.TryGetValue("idempotencyKey", out var queryValue))
            {
                return queryValue.FirstOrDefault();
            }

            return null;
        }

        private async Task<string> GetRequestHash(HttpRequest request)
        {
            var hashInput = new StringBuilder();
            
            // Add method and path
            hashInput.Append(request.Method);
            hashInput.Append(request.Path);
            
            // Add query string
            hashInput.Append(request.QueryString);
            
            // Add user ID if available
            var userId = request.HttpContext.User?.FindFirst("sub")?.Value ?? 
                        request.HttpContext.User?.FindFirst("userId")?.Value;
            if (!string.IsNullOrEmpty(userId))
            {
                hashInput.Append($"user:{userId}");
            }

            // Add request body for POST requests
            if (request.Method == HttpMethods.Post && request.ContentLength > 0)
            {
                request.EnableBuffering();
                var body = await new StreamReader(request.Body).ReadToEndAsync();
                request.Body.Position = 0;
                hashInput.Append(body);
            }

            // Generate hash
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(hashInput.ToString()));
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
    }

    public class IdempotentResponse
    {
        public int StatusCode { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
    }

    public class IdempotencyOptions
    {
        public int CacheMinutes { get; set; } = 60;
    }
}
