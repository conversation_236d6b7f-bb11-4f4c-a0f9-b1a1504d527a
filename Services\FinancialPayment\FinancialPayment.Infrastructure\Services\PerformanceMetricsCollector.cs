using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FinancialPayment.Infrastructure.Services;

public class PerformanceMetricsCollector : IPerformanceMetricsCollector
{
    private readonly ILogger<PerformanceMetricsCollector> _logger;
    private readonly PerformanceTestConfiguration _configuration;
    private readonly ConcurrentDictionary<Guid, MetricsCollectionSession> _activeSessions;
    private readonly ConcurrentBag<PerformanceDataPoint> _collectedMetrics;

    public PerformanceMetricsCollector(
        ILogger<PerformanceMetricsCollector> logger,
        IOptions<PerformanceTestConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
        _activeSessions = new ConcurrentDictionary<Guid, MetricsCollectionSession>();
        _collectedMetrics = new ConcurrentBag<PerformanceDataPoint>();
    }

    public async Task<PerformanceMetrics> CollectMetricsAsync(TimeSpan duration)
    {
        try
        {
            var systemResources = await CollectSystemResourcesAsync();
            var databaseMetrics = await CollectDatabaseMetricsAsync();
            var cacheMetrics = await CollectCacheMetricsAsync();
            var apiMetrics = await CollectAllApiMetricsAsync();

            // Calculate aggregated metrics from collected data points
            var recentDataPoints = _collectedMetrics
                .Where(dp => dp.Timestamp >= DateTime.UtcNow.Subtract(duration))
                .ToList();

            var metrics = new PerformanceMetrics
            {
                TotalRequests = recentDataPoints.Sum(dp => dp.RequestCount),
                SuccessfulRequests = recentDataPoints.Sum(dp => dp.SuccessfulRequests),
                FailedRequests = recentDataPoints.Sum(dp => dp.FailedRequests),
                SystemResources = systemResources,
                Database = databaseMetrics,
                Cache = cacheMetrics,
                ApiMetrics = apiMetrics
            };

            // Calculate derived metrics
            metrics.SuccessRate = metrics.TotalRequests > 0 
                ? (decimal)metrics.SuccessfulRequests / metrics.TotalRequests * 100 
                : 0;

            metrics.ErrorRate = metrics.TotalRequests > 0 
                ? (decimal)metrics.FailedRequests / metrics.TotalRequests * 100 
                : 0;

            if (recentDataPoints.Any())
            {
                var responseTimes = recentDataPoints.SelectMany(dp => dp.ResponseTimes).OrderBy(rt => rt).ToList();
                
                if (responseTimes.Any())
                {
                    metrics.AverageResponseTime = TimeSpan.FromMilliseconds(responseTimes.Average(rt => rt.TotalMilliseconds));
                    metrics.MinResponseTime = responseTimes.First();
                    metrics.MaxResponseTime = responseTimes.Last();
                    
                    // Calculate percentiles
                    metrics.P50ResponseTime = CalculatePercentile(responseTimes, 50);
                    metrics.P90ResponseTime = CalculatePercentile(responseTimes, 90);
                    metrics.P95ResponseTime = CalculatePercentile(responseTimes, 95);
                    metrics.P99ResponseTime = CalculatePercentile(responseTimes, 99);
                }

                metrics.RequestsPerSecond = duration.TotalSeconds > 0 
                    ? metrics.TotalRequests / (decimal)duration.TotalSeconds 
                    : 0;

                // Estimate throughput (simplified calculation)
                metrics.ThroughputMBps = metrics.RequestsPerSecond * 0.001m; // Assume 1KB average response
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting performance metrics");
            return new PerformanceMetrics();
        }
    }

    public async Task<SystemResourceMetrics> CollectSystemResourcesAsync()
    {
        try
        {
            var process = Process.GetCurrentProcess();
            
            // Get CPU usage (simplified - would need more sophisticated calculation in production)
            var cpuUsage = await GetCpuUsageAsync();
            
            // Memory metrics
            var memoryUsageBytes = process.WorkingSet64;
            var totalMemory = GC.GetTotalMemory(false);
            var memoryUsagePercent = (decimal)(memoryUsageBytes / (1024.0 * 1024.0 * 1024.0)) * 100; // Simplified

            // Thread metrics
            var activeThreads = process.Threads.Count;
            var totalThreads = ThreadPool.ThreadCount;

            // GC metrics
            var gcCollections = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2);

            return new SystemResourceMetrics
            {
                CpuUsagePercent = cpuUsage,
                MemoryUsagePercent = Math.Min(memoryUsagePercent, 100),
                MemoryUsageBytes = memoryUsageBytes,
                DiskUsagePercent = 0, // Would need OS-specific implementation
                NetworkInMBps = 0, // Would need network monitoring
                NetworkOutMBps = 0,
                ActiveThreads = activeThreads,
                TotalThreads = totalThreads,
                GarbageCollections = gcCollections,
                GcTime = TimeSpan.Zero // Would need GC monitoring
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting system resource metrics");
            return new SystemResourceMetrics();
        }
    }

    public async Task<DatabasePerformanceMetrics> CollectDatabaseMetricsAsync()
    {
        try
        {
            // In a real implementation, this would query database performance counters
            // For demo purposes, we'll simulate realistic values
            
            var random = new Random();
            
            return await Task.FromResult(new DatabasePerformanceMetrics
            {
                ActiveConnections = random.Next(5, 25),
                TotalConnections = random.Next(50, 100),
                AverageQueryTime = TimeSpan.FromMilliseconds(random.Next(10, 100)),
                MaxQueryTime = TimeSpan.FromMilliseconds(random.Next(100, 500)),
                SlowQueries = random.Next(0, 5),
                DeadlockCount = random.Next(0, 2),
                CacheHitRatio = (decimal)(random.NextDouble() * 20 + 80), // 80-100%
                TotalQueries = random.Next(1000, 5000),
                FailedQueries = random.Next(0, 50)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting database performance metrics");
            return new DatabasePerformanceMetrics();
        }
    }

    public async Task<CachePerformanceMetrics> CollectCacheMetricsAsync()
    {
        try
        {
            // In a real implementation, this would query Redis/cache performance
            var random = new Random();
            var totalRequests = random.Next(1000, 10000);
            var hits = (long)(totalRequests * (random.NextDouble() * 0.3 + 0.7)); // 70-100% hit rate
            var misses = totalRequests - hits;

            return await Task.FromResult(new CachePerformanceMetrics
            {
                TotalRequests = totalRequests,
                Hits = hits,
                Misses = misses,
                HitRatio = totalRequests > 0 ? (decimal)hits / totalRequests * 100 : 0,
                AverageResponseTime = TimeSpan.FromMilliseconds(random.Next(1, 10)),
                MemoryUsageBytes = random.Next(100 * 1024 * 1024, 500 * 1024 * 1024), // 100-500 MB
                KeyCount = random.Next(10000, 100000),
                ExpiredKeys = random.Next(100, 1000)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting cache performance metrics");
            return new CachePerformanceMetrics();
        }
    }

    public async Task<ApiPerformanceMetrics> CollectApiMetricsAsync(string endpoint)
    {
        try
        {
            // Get metrics for specific endpoint from collected data
            var endpointData = _collectedMetrics
                .Where(dp => dp.Endpoint == endpoint && dp.Timestamp >= DateTime.UtcNow.AddMinutes(-5))
                .ToList();

            if (!endpointData.Any())
            {
                return new ApiPerformanceMetrics
                {
                    Endpoint = endpoint,
                    Method = "GET"
                };
            }

            var totalRequests = endpointData.Sum(dp => dp.RequestCount);
            var successfulRequests = endpointData.Sum(dp => dp.SuccessfulRequests);
            var responseTimes = endpointData.SelectMany(dp => dp.ResponseTimes).ToList();

            return new ApiPerformanceMetrics
            {
                Endpoint = endpoint,
                Method = "POST", // Simplified
                RequestCount = totalRequests,
                AverageResponseTime = responseTimes.Any() 
                    ? TimeSpan.FromMilliseconds(responseTimes.Average(rt => rt.TotalMilliseconds))
                    : TimeSpan.Zero,
                MaxResponseTime = responseTimes.Any() ? responseTimes.Max() : TimeSpan.Zero,
                SuccessRate = totalRequests > 0 ? (decimal)successfulRequests / totalRequests * 100 : 0,
                StatusCodeDistribution = new Dictionary<string, int>
                {
                    { "200", successfulRequests },
                    { "500", totalRequests - successfulRequests }
                },
                ResponseTimes = responseTimes
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting API performance metrics for {Endpoint}", endpoint);
            return new ApiPerformanceMetrics { Endpoint = endpoint };
        }
    }

    public async Task StartMetricsCollectionAsync(Guid testId)
    {
        try
        {
            var session = new MetricsCollectionSession
            {
                TestId = testId,
                StartTime = DateTime.UtcNow,
                IsActive = true
            };

            _activeSessions.TryAdd(testId, session);

            // Start background collection
            _ = Task.Run(async () => await CollectMetricsContinuouslyAsync(session));

            _logger.LogInformation("Started metrics collection for test {TestId}", testId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting metrics collection for test {TestId}", testId);
        }
    }

    public async Task StopMetricsCollectionAsync(Guid testId)
    {
        try
        {
            if (_activeSessions.TryGetValue(testId, out var session))
            {
                session.IsActive = false;
                session.EndTime = DateTime.UtcNow;
                _activeSessions.TryRemove(testId, out _);
                
                _logger.LogInformation("Stopped metrics collection for test {TestId}", testId);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping metrics collection for test {TestId}", testId);
        }
    }

    private async Task<List<ApiPerformanceMetrics>> CollectAllApiMetricsAsync()
    {
        var endpoints = new[] { "/api/payments", "/api/subscriptions", "/api/analytics", "/api/plans" };
        var apiMetrics = new List<ApiPerformanceMetrics>();

        foreach (var endpoint in endpoints)
        {
            var metrics = await CollectApiMetricsAsync(endpoint);
            apiMetrics.Add(metrics);
        }

        return apiMetrics;
    }

    private async Task CollectMetricsContinuouslyAsync(MetricsCollectionSession session)
    {
        var random = new Random();
        
        while (session.IsActive)
        {
            try
            {
                // Simulate collecting metrics data points
                var dataPoint = new PerformanceDataPoint
                {
                    TestId = session.TestId,
                    Timestamp = DateTime.UtcNow,
                    Endpoint = "/api/payments", // Simplified
                    RequestCount = random.Next(10, 100),
                    SuccessfulRequests = random.Next(8, 95),
                    FailedRequests = random.Next(0, 5),
                    ResponseTimes = GenerateRandomResponseTimes(random, random.Next(5, 20))
                };

                dataPoint.FailedRequests = dataPoint.RequestCount - dataPoint.SuccessfulRequests;
                _collectedMetrics.Add(dataPoint);

                await Task.Delay(TimeSpan.FromSeconds(_configuration.MetricsCollectionIntervalSeconds));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in continuous metrics collection for test {TestId}", session.TestId);
                await Task.Delay(TimeSpan.FromSeconds(5)); // Wait before retrying
            }
        }
    }

    private List<TimeSpan> GenerateRandomResponseTimes(Random random, int count)
    {
        var responseTimes = new List<TimeSpan>();
        
        for (int i = 0; i < count; i++)
        {
            // Generate realistic response times with some outliers
            var baseTime = random.Next(50, 300); // 50-300ms base
            
            // 10% chance of slow response
            if (random.NextDouble() < 0.1)
            {
                baseTime += random.Next(500, 2000);
            }
            
            responseTimes.Add(TimeSpan.FromMilliseconds(baseTime));
        }
        
        return responseTimes;
    }

    private async Task<decimal> GetCpuUsageAsync()
    {
        try
        {
            // Simplified CPU usage calculation
            // In production, would use performance counters or system APIs
            var random = new Random();
            await Task.Delay(10); // Simulate measurement time
            return (decimal)(random.NextDouble() * 30 + 20); // 20-50% CPU usage
        }
        catch
        {
            return 0;
        }
    }

    private TimeSpan CalculatePercentile(List<TimeSpan> sortedValues, int percentile)
    {
        if (!sortedValues.Any()) return TimeSpan.Zero;
        
        var index = (int)Math.Ceiling(sortedValues.Count * percentile / 100.0) - 1;
        index = Math.Max(0, Math.Min(index, sortedValues.Count - 1));
        
        return sortedValues[index];
    }

    private class MetricsCollectionSession
    {
        public Guid TestId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsActive { get; set; }
    }

    private class PerformanceDataPoint
    {
        public Guid TestId { get; set; }
        public DateTime Timestamp { get; set; }
        public string Endpoint { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public List<TimeSpan> ResponseTimes { get; set; } = new();
    }
}
