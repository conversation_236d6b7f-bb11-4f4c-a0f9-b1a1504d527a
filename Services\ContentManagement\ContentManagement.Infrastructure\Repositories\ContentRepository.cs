using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ContentManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Content entity
/// </summary>
public class ContentRepository : IContentRepository
{
    private readonly ContentManagementDbContext _context;
    private readonly ILogger<ContentRepository> _logger;

    public ContentRepository(
        ContentManagementDbContext context,
        ILogger<ContentRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Content?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<Content?> GetBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .FirstOrDefaultAsync(c => c.Slug == slug, cancellationToken);
    }

    public async Task<List<Content>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Content content, CancellationToken cancellationToken = default)
    {
        await _context.Contents.AddAsync(content, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Content content, CancellationToken cancellationToken = default)
    {
        _context.Contents.Update(content);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Content content, CancellationToken cancellationToken = default)
    {
        _context.Contents.Remove(content);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<(List<Content> Items, int TotalCount)> SearchAsync(
        string? searchTerm = null,
        ContentType? type = null,
        ContentStatus? status = null,
        List<string>? tags = null,
        Guid? createdBy = null,
        DateTime? createdFrom = null,
        DateTime? createdTo = null,
        DateTime? updatedFrom = null,
        DateTime? updatedTo = null,
        List<string>? userRoles = null,
        bool isAuthenticated = false,
        string sortBy = "CreatedAt",
        bool sortDescending = true,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Contents
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(c => 
                c.Title.Contains(searchTerm) ||
                c.Description!.Contains(searchTerm) ||
                c.ContentBody.Contains(searchTerm));
        }

        if (type.HasValue)
        {
            query = query.Where(c => c.Type == type.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(c => c.Status == status.Value);
        }

        if (tags?.Any() == true)
        {
            foreach (var tag in tags)
            {
                query = query.Where(c => c.Tags.Contains(tag));
            }
        }

        if (createdBy.HasValue)
        {
            query = query.Where(c => c.CreatedBy == createdBy.Value);
        }

        if (createdFrom.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= createdFrom.Value);
        }

        if (createdTo.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= createdTo.Value);
        }

        if (updatedFrom.HasValue)
        {
            query = query.Where(c => c.UpdatedAt >= updatedFrom.Value);
        }

        if (updatedTo.HasValue)
        {
            query = query.Where(c => c.UpdatedAt <= updatedTo.Value);
        }

        // Apply visibility filters
        query = ApplyVisibilityFilter(query, userRoles, isAuthenticated);

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = ApplySorting(query, sortBy, sortDescending);

        // Apply pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<List<Content>> GetByTypeAsync(ContentType type, CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Where(c => c.Type == type)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetByStatusAsync(ContentStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Where(c => c.Status == status)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetByCreatorAsync(Guid createdBy, CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Where(c => c.CreatedBy == createdBy)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetPendingApprovalAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Where(c => c.Status == ContentStatus.PendingReview)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .OrderBy(c => c.UpdatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetPublishedAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .Where(c => c.Status == ContentStatus.Published)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Content>> GetScheduledForPublishingAsync(DateTime? beforeDate = null, CancellationToken cancellationToken = default)
    {
        var cutoffDate = beforeDate ?? DateTime.UtcNow;
        
        return await _context.Contents
            .Where(c => c.Status == ContentStatus.Approved &&
                       c.PublishSchedule != null &&
                       c.PublishSchedule.PublishAt <= cutoffDate)
            .Include(c => c.Versions)
            .Include(c => c.Attachments)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsSlugUniqueAsync(string slug, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Contents.Where(c => c.Slug == slug);
        
        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }

        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<string> GenerateUniqueSlugAsync(string baseSlug, CancellationToken cancellationToken = default)
    {
        var slug = baseSlug;
        var counter = 1;

        while (!await IsSlugUniqueAsync(slug, null, cancellationToken))
        {
            slug = $"{baseSlug}-{counter}";
            counter++;
        }

        return slug;
    }

    public async Task<Dictionary<ContentStatus, int>> GetContentCountByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .GroupBy(c => c.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<ContentType, int>> GetContentCountByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .GroupBy(c => c.Type)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetContentCountByCreatorAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Contents
            .GroupBy(c => c.CreatedByName)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    private static IQueryable<Content> ApplyVisibilityFilter(
        IQueryable<Content> query,
        List<string>? userRoles,
        bool isAuthenticated)
    {
        // This is a simplified visibility filter
        // In a real implementation, you would need to properly handle the ContentVisibility value object
        
        if (!isAuthenticated)
        {
            // Only show public content for unauthenticated users
            query = query.Where(c => c.Status == ContentStatus.Published);
        }

        return query;
    }

    private static IQueryable<Content> ApplySorting(
        IQueryable<Content> query,
        string sortBy,
        bool sortDescending)
    {
        return sortBy.ToLowerInvariant() switch
        {
            "title" => sortDescending ? query.OrderByDescending(c => c.Title) : query.OrderBy(c => c.Title),
            "createdat" => sortDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt),
            "updatedat" => sortDescending ? query.OrderByDescending(c => c.UpdatedAt) : query.OrderBy(c => c.UpdatedAt),
            "sortorder" => sortDescending ? query.OrderByDescending(c => c.SortOrder) : query.OrderBy(c => c.SortOrder),
            "status" => sortDescending ? query.OrderByDescending(c => c.Status) : query.OrderBy(c => c.Status),
            _ => sortDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt)
        };
    }
}
