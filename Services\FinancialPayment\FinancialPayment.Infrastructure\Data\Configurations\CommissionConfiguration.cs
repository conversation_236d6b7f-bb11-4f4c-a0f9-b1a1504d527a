using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class CommissionConfiguration : IEntityTypeConfiguration<Commission>
{
    public void Configure(EntityTypeBuilder<Commission> builder)
    {
        builder.ToTable("commissions");

        builder.<PERSON><PERSON>ey(c => c.Id);

        builder.Property(c => c.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(c => c.OrderId)
            .HasColumnName("order_id")
            .IsRequired();

        builder.Property(c => c.BrokerId)
            .HasColumnName("broker_id")
            .IsRequired();

        builder.Property(c => c.TransportCompanyId)
            .HasColumnName("transport_company_id")
            .IsRequired();

        builder.Property(c => c.CarrierId)
            .HasColumnName("carrier_id")
            .IsRequired();

        // Money value objects
        builder.OwnsOne(c => c.OrderAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("order_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("order_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.OwnsOne(c => c.CalculatedAmount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("calculated_amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("calculated_currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        // Commission structure value object
        builder.OwnsOne(c => c.CommissionStructure, structure =>
        {
            structure.Property(s => s.Type)
                .HasColumnName("commission_type")
                .HasConversion<int>()
                .IsRequired();

            structure.Property(s => s.Rate)
                .HasColumnName("commission_rate")
                .HasColumnType("decimal(18,4)")
                .IsRequired();

            structure.Property(s => s.MinimumAmount)
                .HasColumnName("commission_minimum_amount")
                .HasColumnType("decimal(18,2)");

            structure.Property(s => s.MaximumAmount)
                .HasColumnName("commission_maximum_amount")
                .HasColumnType("decimal(18,2)");

            structure.Property(s => s.Description)
                .HasColumnName("commission_description")
                .HasMaxLength(500);
        });

        builder.Property(c => c.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(c => c.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(c => c.ProcessedAt)
            .HasColumnName("processed_at");

        builder.Property(c => c.Notes)
            .HasColumnName("notes")
            .HasMaxLength(1000);

        // Relationships
        builder.HasMany(c => c.Adjustments)
            .WithOne(a => a.Commission)
            .HasForeignKey(a => a.CommissionId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(c => c.OrderId)
            .IsUnique()
            .HasDatabaseName("ix_commissions_order_id");

        builder.HasIndex(c => c.BrokerId)
            .HasDatabaseName("ix_commissions_broker_id");

        builder.HasIndex(c => c.TransportCompanyId)
            .HasDatabaseName("ix_commissions_transport_company_id");

        builder.HasIndex(c => c.CarrierId)
            .HasDatabaseName("ix_commissions_carrier_id");

        builder.HasIndex(c => c.Status)
            .HasDatabaseName("ix_commissions_status");

        builder.HasIndex(c => c.CreatedAt)
            .HasDatabaseName("ix_commissions_created_at");
    }
}
