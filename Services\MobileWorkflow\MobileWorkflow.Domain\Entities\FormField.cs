using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class FormField : AggregateRoot
{
    public Guid FormDefinitionId { get; private set; }
    public Guid? FormStepId { get; private set; }
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string FieldType { get; private set; } // text, email, number, select, checkbox, radio, file, date, etc.
    public int Order { get; private set; }
    public bool IsRequired { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsReadOnly { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> ValidationRules { get; private set; }
    public Dictionary<string, object> ConditionalLogic { get; private set; }
    public Dictionary<string, object> Styling { get; private set; }
    public Dictionary<string, object> DataBinding { get; private set; }
    public object? DefaultValue { get; private set; }
    public string? PlaceholderText { get; private set; }
    public string? HelpText { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual FormDefinition FormDefinition { get; private set; } = null!;
    public virtual FormStep? FormStep { get; private set; }
    public virtual ICollection<FormFieldOption> Options { get; private set; } = new List<FormFieldOption>();

    private FormField() { } // EF Core

    public FormField(
        Guid formDefinitionId,
        string name,
        string displayName,
        string description,
        string fieldType,
        int order,
        Guid? formStepId = null)
    {
        FormDefinitionId = formDefinitionId;
        FormStepId = formStepId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        FieldType = fieldType ?? throw new ArgumentNullException(nameof(fieldType));
        Order = order;

        IsRequired = false;
        IsActive = true;
        IsReadOnly = false;
        Configuration = new Dictionary<string, object>();
        ValidationRules = new Dictionary<string, object>();
        ConditionalLogic = new Dictionary<string, object>();
        Styling = new Dictionary<string, object>();
        DataBinding = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        SetDefaultConfiguration();

        AddDomainEvent(new FormFieldCreatedEvent(Id, FormDefinitionId, Name, FieldType, Order));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        AddDomainEvent(new FormFieldConfigurationUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateValidationRules(Dictionary<string, object> validationRules)
    {
        ValidationRules = validationRules ?? throw new ArgumentNullException(nameof(validationRules));

        AddDomainEvent(new FormFieldValidationRulesUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateConditionalLogic(Dictionary<string, object> conditionalLogic)
    {
        ConditionalLogic = conditionalLogic ?? throw new ArgumentNullException(nameof(conditionalLogic));

        AddDomainEvent(new FormFieldConditionalLogicUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateStyling(Dictionary<string, object> styling)
    {
        Styling = styling ?? throw new ArgumentNullException(nameof(styling));

        AddDomainEvent(new FormFieldStylingUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateDataBinding(Dictionary<string, object> dataBinding)
    {
        DataBinding = dataBinding ?? throw new ArgumentNullException(nameof(dataBinding));

        AddDomainEvent(new FormFieldDataBindingUpdatedEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsRequired()
    {
        IsRequired = true;

        AddDomainEvent(new FormFieldSetAsRequiredEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsOptional()
    {
        IsRequired = false;

        AddDomainEvent(new FormFieldSetAsOptionalEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsReadOnly()
    {
        IsReadOnly = true;

        AddDomainEvent(new FormFieldSetAsReadOnlyEvent(Id, FormDefinitionId, Name));
    }

    public void SetAsEditable()
    {
        IsReadOnly = false;

        AddDomainEvent(new FormFieldSetAsEditableEvent(Id, FormDefinitionId, Name));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new FormFieldActivatedEvent(Id, FormDefinitionId, Name));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new FormFieldDeactivatedEvent(Id, FormDefinitionId, Name));
    }

    public void UpdateOrder(int newOrder)
    {
        var oldOrder = Order;
        Order = newOrder;

        AddDomainEvent(new FormFieldOrderUpdatedEvent(Id, FormDefinitionId, Name, oldOrder, newOrder));
    }

    public void SetDefaultValue(object? defaultValue)
    {
        DefaultValue = defaultValue;

        AddDomainEvent(new FormFieldDefaultValueSetEvent(Id, FormDefinitionId, Name));
    }

    public void SetPlaceholderText(string? placeholderText)
    {
        PlaceholderText = placeholderText;
    }

    public void SetHelpText(string? helpText)
    {
        HelpText = helpText;
    }

    public void MoveToStep(Guid? stepId)
    {
        FormStepId = stepId;

        AddDomainEvent(new FormFieldMovedToStepEvent(Id, FormDefinitionId, Name, stepId));
    }

    public bool EvaluateVisibilityConditions(Dictionary<string, object> context)
    {
        if (ConditionalLogic.Count == 0)
        {
            return true; // No conditions means always visible
        }

        if (ConditionalLogic.TryGetValue("visibility", out var visibilityConditions) &&
            visibilityConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, context);
        }

        return true;
    }

    public bool EvaluateRequiredConditions(Dictionary<string, object> context)
    {
        if (!IsRequired)
        {
            return false;
        }

        if (ConditionalLogic.TryGetValue("required", out var requiredConditions) &&
            requiredConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, context);
        }

        return IsRequired;
    }

    public List<string> ValidateValue(Dictionary<string, object> submissionData)
    {
        var errors = new List<string>();

        // Get the field value from submission data
        submissionData.TryGetValue(Name, out var fieldValue);

        // Check if field is required
        if (EvaluateRequiredConditions(submissionData) && IsValueEmpty(fieldValue))
        {
            errors.Add($"{DisplayName} is required");
            return errors; // Don't validate further if required field is empty
        }

        // Skip validation if field is empty and not required
        if (IsValueEmpty(fieldValue))
        {
            return errors;
        }

        // Apply field-specific validation rules
        foreach (var rule in ValidationRules)
        {
            var ruleErrors = EvaluateValidationRule(rule.Key, rule.Value, fieldValue);
            errors.AddRange(ruleErrors);
        }

        // Apply field type validation
        var typeErrors = ValidateFieldType(fieldValue);
        errors.AddRange(typeErrors);

        return errors;
    }

    private bool EvaluateConditions(Dictionary<string, object> conditions, Dictionary<string, object> context)
    {
        if (conditions.TryGetValue("operator", out var op) && op is string operatorStr)
        {
            if (conditions.TryGetValue("conditions", out var conditionsList) && conditionsList is List<object> condList)
            {
                var results = new List<bool>();

                foreach (var condition in condList)
                {
                    if (condition is Dictionary<string, object> condDict)
                    {
                        results.Add(EvaluateSingleCondition(condDict, context));
                    }
                }

                return operatorStr.ToLowerInvariant() switch
                {
                    "and" => results.All(r => r),
                    "or" => results.Any(r => r),
                    _ => true
                };
            }
        }

        return true;
    }

    private bool EvaluateSingleCondition(Dictionary<string, object> condition, Dictionary<string, object> context)
    {
        if (condition.TryGetValue("field", out var field) && field is string fieldName &&
            condition.TryGetValue("operator", out var op) && op is string operatorStr &&
            condition.TryGetValue("value", out var expectedValue))
        {
            context.TryGetValue(fieldName, out var contextValue);
            return EvaluateOperator(operatorStr, contextValue, expectedValue);
        }

        return true;
    }

    private bool EvaluateOperator(string operatorStr, object? contextValue, object expectedValue)
    {
        return operatorStr.ToLowerInvariant() switch
        {
            "equals" or "eq" => Equals(contextValue, expectedValue),
            "not_equals" or "ne" => !Equals(contextValue, expectedValue),
            "greater_than" or "gt" => CompareValues(contextValue, expectedValue) > 0,
            "greater_than_or_equal" or "gte" => CompareValues(contextValue, expectedValue) >= 0,
            "less_than" or "lt" => CompareValues(contextValue, expectedValue) < 0,
            "less_than_or_equal" or "lte" => CompareValues(contextValue, expectedValue) <= 0,
            "contains" => contextValue?.ToString()?.Contains(expectedValue?.ToString() ?? "") == true,
            "starts_with" => contextValue?.ToString()?.StartsWith(expectedValue?.ToString() ?? "") == true,
            "ends_with" => contextValue?.ToString()?.EndsWith(expectedValue?.ToString() ?? "") == true,
            "is_empty" => IsValueEmpty(contextValue),
            "is_not_empty" => !IsValueEmpty(contextValue),
            "in" => expectedValue is List<object> list && list.Contains(contextValue),
            "not_in" => expectedValue is List<object> notList && !notList.Contains(contextValue),
            _ => true
        };
    }

    private int CompareValues(object? value1, object value2)
    {
        if (value1 is IComparable comparable1 && value2 is IComparable comparable2)
        {
            try
            {
                return comparable1.CompareTo(comparable2);
            }
            catch
            {
                return 0;
            }
        }
        return 0;
    }

    private bool IsValueEmpty(object? value)
    {
        return value == null ||
               (value is string str && string.IsNullOrWhiteSpace(str)) ||
               (value is Array arr && arr.Length == 0) ||
               (value is System.Collections.ICollection collection && collection.Count == 0);
    }

    private List<string> EvaluateValidationRule(string ruleName, object ruleValue, object? fieldValue)
    {
        var errors = new List<string>();

        if (ruleValue is Dictionary<string, object> ruleDefinition)
        {
            switch (ruleName.ToLowerInvariant())
            {
                case "min_length":
                    if (ruleDefinition.TryGetValue("value", out var minLengthValue) && minLengthValue is int minLength)
                    {
                        if (fieldValue?.ToString()?.Length < minLength)
                        {
                            errors.Add($"Minimum length is {minLength} characters");
                        }
                    }
                    break;

                case "max_length":
                    if (ruleDefinition.TryGetValue("value", out var maxLengthValue) && maxLengthValue is int maxLength)
                    {
                        if (fieldValue?.ToString()?.Length > maxLength)
                        {
                            errors.Add($"Maximum length is {maxLength} characters");
                        }
                    }
                    break;

                case "pattern":
                    if (ruleDefinition.TryGetValue("value", out var patternValue) && patternValue is string pattern)
                    {
                        if (!System.Text.RegularExpressions.Regex.IsMatch(fieldValue?.ToString() ?? "", pattern))
                        {
                            var message = ruleDefinition.TryGetValue("message", out var msg) && msg is string msgStr
                                ? msgStr
                                : "Invalid format";
                            errors.Add(message);
                        }
                    }
                    break;

                case "min_value":
                    if (ruleDefinition.TryGetValue("value", out var minValue) && IsNumeric(fieldValue))
                    {
                        if (Convert.ToDouble(fieldValue) < Convert.ToDouble(minValue))
                        {
                            errors.Add($"Minimum value is {minValue}");
                        }
                    }
                    break;

                case "max_value":
                    if (ruleDefinition.TryGetValue("value", out var maxValue) && IsNumeric(fieldValue))
                    {
                        if (Convert.ToDouble(fieldValue) > Convert.ToDouble(maxValue))
                        {
                            errors.Add($"Maximum value is {maxValue}");
                        }
                    }
                    break;
            }
        }

        return errors;
    }

    private List<string> ValidateFieldType(object? fieldValue)
    {
        var errors = new List<string>();

        switch (FieldType.ToLowerInvariant())
        {
            case "email":
                if (!IsValidEmail(fieldValue?.ToString()))
                {
                    errors.Add("Invalid email format");
                }
                break;

            case "number":
                if (!IsNumeric(fieldValue))
                {
                    errors.Add("Must be a valid number");
                }
                break;

            case "date":
                if (!IsValidDate(fieldValue))
                {
                    errors.Add("Invalid date format");
                }
                break;

            case "url":
                if (!IsValidUrl(fieldValue?.ToString()))
                {
                    errors.Add("Invalid URL format");
                }
                break;
        }

        return errors;
    }

    private bool IsValidEmail(string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private bool IsNumeric(object? value)
    {
        return value != null && (value is int or long or float or double or decimal ||
               double.TryParse(value.ToString(), out _));
    }

    private bool IsValidDate(object? value)
    {
        return value is DateTime || DateTime.TryParse(value?.ToString(), out _);
    }

    private bool IsValidUrl(string? url)
    {
        return !string.IsNullOrWhiteSpace(url) && Uri.TryCreate(url, UriKind.Absolute, out _);
    }

    private void SetDefaultConfiguration()
    {
        Configuration["width"] = "100%";
        Configuration["height"] = "auto";

        switch (FieldType.ToLowerInvariant())
        {
            case "text":
                Configuration["max_length"] = 255;
                break;
            case "textarea":
                Configuration["rows"] = 4;
                Configuration["max_length"] = 1000;
                break;
            case "number":
                Configuration["step"] = 1;
                break;
            case "select":
                Configuration["multiple"] = false;
                break;
            case "file":
                Configuration["max_size"] = 10485760; // 10MB
                Configuration["allowed_types"] = new List<string> { "jpg", "jpeg", "png", "pdf", "doc", "docx" };
                break;
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


