using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Domain.Repositories
{
    public interface IOtpTokenRepository
    {
        Task<OtpToken> GetByIdAsync(Guid id);
        Task<OtpToken> GetValidTokenAsync(Guid userId, string token, OtpPurpose purpose);
        Task<List<OtpToken>> GetActiveTokensAsync(Guid userId, OtpPurpose purpose);
        Task<List<OtpToken>> GetExpiredTokensAsync();
        Task AddAsync(OtpToken otpToken);
        Task UpdateAsync(OtpToken otpToken);
        Task DeleteAsync(OtpToken otpToken);
        Task InvalidateUserTokensAsync(Guid userId, OtpPurpose purpose);
        Task CleanupExpiredTokensAsync();
    }
}
