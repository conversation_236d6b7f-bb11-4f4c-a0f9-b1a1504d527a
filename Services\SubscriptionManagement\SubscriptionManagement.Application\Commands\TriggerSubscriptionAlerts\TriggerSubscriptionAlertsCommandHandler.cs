using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;
using System.Diagnostics;

namespace SubscriptionManagement.Application.Commands.TriggerSubscriptionAlerts
{
    public class TriggerSubscriptionAlertsCommandHandler : IRequestHandler<TriggerSubscriptionAlertsCommand, TriggerSubscriptionAlertsResponse>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPlanRepository _planRepository;
        private readonly INotificationTemplateService _templateService;
        private readonly ICommunicationService _communicationService;
        private readonly ILogger<TriggerSubscriptionAlertsCommandHandler> _logger;

        public TriggerSubscriptionAlertsCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPlanRepository planRepository,
            INotificationTemplateService templateService,
            ICommunicationService communicationService,
            ILogger<TriggerSubscriptionAlertsCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _planRepository = planRepository;
            _templateService = templateService;
            _communicationService = communicationService;
            _logger = logger;
        }

        public async Task<TriggerSubscriptionAlertsResponse> Handle(TriggerSubscriptionAlertsCommand request, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();
            var processedAt = DateTime.UtcNow;

            _logger.LogInformation("Starting subscription alerts processing. DryRun: {DryRun}, AlertType: {AlertType}, Channels: {Channels}, TriggeredBy: {TriggeredByUserId}",
                request.DryRun, request.AlertType, string.Join(",", request.Channels), request.TriggeredByUserId);

            var response = new TriggerSubscriptionAlertsResponse
            {
                Channels = request.Channels,
                WasDryRun = request.DryRun,
                ProcessedAt = processedAt
            };

            try
            {
                // Validate request
                var validationErrors = ValidateRequest(request);
                if (validationErrors.Any())
                {
                    response.Success = false;
                    response.Errors = validationErrors;
                    response.Message = "Validation failed";
                    return response;
                }

                // Get filtered subscriptions
                var subscriptions = await GetFilteredSubscriptions(request, cancellationToken);
                response.TotalSubscriptionsFound = subscriptions.Count;

                _logger.LogInformation("Found {Count} subscriptions matching criteria", subscriptions.Count);

                if (!subscriptions.Any())
                {
                    response.Success = true;
                    response.Message = "No subscriptions found matching the specified criteria";
                    return response;
                }

                // Process subscriptions in batches
                var results = new List<SubscriptionAlertResult>();
                var batches = subscriptions.Chunk(request.BatchSize);

                foreach (var batch in batches)
                {
                    var batchResults = await ProcessSubscriptionBatch(batch, request, cancellationToken);
                    results.AddRange(batchResults);

                    // Log progress
                    _logger.LogInformation("Processed batch of {BatchSize} subscriptions. Total processed: {TotalProcessed}/{Total}",
                        batch.Length, results.Count, subscriptions.Count);
                }

                response.Results = results;
                response.NotificationsSent = results.Count(r => r.Success);
                response.NotificationsFailed = results.Count(r => !r.Success);
                response.Success = true;
                response.Message = request.DryRun
                    ? $"Dry run completed. Would send {response.NotificationsSent} notifications"
                    : $"Sent {response.NotificationsSent} notifications successfully, {response.NotificationsFailed} failed";

                stopwatch.Stop();
                response.ProcessingTime = stopwatch.Elapsed;

                _logger.LogInformation("Subscription alerts processing completed. Success: {Success}, Sent: {Sent}, Failed: {Failed}, Duration: {Duration}ms",
                    response.Success, response.NotificationsSent, response.NotificationsFailed, stopwatch.ElapsedMilliseconds);

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                response.ProcessingTime = stopwatch.Elapsed;
                response.Success = false;
                response.Message = $"Internal error: {ex.Message}";
                response.Errors.Add(ex.Message);

                _logger.LogError(ex, "Failed to process subscription alerts");
                return response;
            }
        }

        private List<string> ValidateRequest(TriggerSubscriptionAlertsCommand request)
        {
            var errors = new List<string>();

            if (!request.Channels.Any())
            {
                errors.Add("At least one notification channel must be specified");
            }

            var validChannels = new[] { "Email", "SMS", "WhatsApp", "InApp" };
            var invalidChannels = request.Channels.Where(c => !validChannels.Contains(c)).ToList();
            if (invalidChannels.Any())
            {
                errors.Add($"Invalid channels: {string.Join(", ", invalidChannels)}");
            }

            if (request.BatchSize <= 0 || request.BatchSize > 1000)
            {
                errors.Add("Batch size must be between 1 and 1000");
            }

            if (request.ExpiryDateFrom.HasValue && request.ExpiryDateTo.HasValue &&
                request.ExpiryDateFrom.Value > request.ExpiryDateTo.Value)
            {
                errors.Add("ExpiryDateFrom cannot be greater than ExpiryDateTo");
            }

            return errors;
        }

        private async Task<List<Domain.Entities.Subscription>> GetFilteredSubscriptions(
            TriggerSubscriptionAlertsCommand request,
            CancellationToken cancellationToken)
        {
            // TODO: Implement proper filtering in repository
            // For now, get all subscriptions and filter in memory (not ideal for production)
            var allSubscriptions = await _subscriptionRepository.GetAllAsync();

            var filtered = allSubscriptions.AsQueryable();

            // Filter by subscription IDs if specified
            if (request.SubscriptionIds?.Any() == true)
            {
                filtered = filtered.Where(s => request.SubscriptionIds.Contains(s.Id));
            }

            // Filter by subscription statuses
            if (request.SubscriptionStatuses?.Any() == true)
            {
                filtered = filtered.Where(s => request.SubscriptionStatuses.Contains(s.Status));
            }

            // Filter by expiry date range
            if (request.ExpiryDateFrom.HasValue)
            {
                filtered = filtered.Where(s => s.NextBillingDate >= request.ExpiryDateFrom.Value);
            }

            if (request.ExpiryDateTo.HasValue)
            {
                filtered = filtered.Where(s => s.NextBillingDate <= request.ExpiryDateTo.Value);
            }

            // TODO: Filter by UserType and PlanType (requires joining with Plan and User data)

            return filtered.ToList();
        }

        private async Task<List<SubscriptionAlertResult>> ProcessSubscriptionBatch(
            Domain.Entities.Subscription[] batch,
            TriggerSubscriptionAlertsCommand request,
            CancellationToken cancellationToken)
        {
            var results = new List<SubscriptionAlertResult>();

            foreach (var subscription in batch)
            {
                var result = new SubscriptionAlertResult
                {
                    SubscriptionId = subscription.Id,
                    UserId = subscription.UserId,
                    Status = subscription.Status,
                    ExpiryDate = subscription.NextBillingDate
                };

                try
                {
                    // Get plan information
                    var plan = await _planRepository.GetByIdAsync(subscription.PlanId);
                    result.PlanName = plan?.Name ?? "Unknown Plan";

                    if (!request.DryRun)
                    {
                        // Process notification for each channel
                        foreach (var channel in request.Channels)
                        {
                            try
                            {
                                await ProcessSubscriptionNotification(subscription, plan, request, channel, cancellationToken);
                                result.ChannelResults.Add($"{channel}: Success");
                            }
                            catch (Exception ex)
                            {
                                result.Errors.Add($"{channel}: {ex.Message}");
                                _logger.LogError(ex, "Failed to send notification for subscription {SubscriptionId} on channel {Channel}",
                                    subscription.Id, channel);
                            }
                        }
                    }
                    else
                    {
                        // Dry run - just validate template exists
                        foreach (var channel in request.Channels)
                        {
                            var template = await _templateService.GetTemplateAsync(request.AlertType, channel, request.Language, cancellationToken);
                            if (template != null)
                            {
                                result.ChannelResults.Add($"{channel}: Template found");
                            }
                            else
                            {
                                result.Errors.Add($"{channel}: No template found");
                            }
                        }
                    }

                    result.Success = !result.Errors.Any();
                }
                catch (Exception ex)
                {
                    result.Success = false;
                    result.Errors.Add($"Processing error: {ex.Message}");
                    _logger.LogError(ex, "Failed to process subscription {SubscriptionId}", subscription.Id);
                }

                results.Add(result);
            }

            return results;
        }

        private async Task ProcessSubscriptionNotification(
            Domain.Entities.Subscription subscription,
            Domain.Entities.Plan? plan,
            TriggerSubscriptionAlertsCommand request,
            string channel,
            CancellationToken cancellationToken)
        {
            // Prepare variables
            var variables = _templateService.GetSubscriptionVariables(subscription, plan);
            var userVariables = _templateService.GetUserVariables(subscription.UserId);

            foreach (var kv in userVariables)
            {
                variables[kv.Key] = kv.Value;
            }

            if (!string.IsNullOrEmpty(request.CustomMessage))
            {
                variables["CustomMessage"] = request.CustomMessage;
            }

            variables["TriggeredByUserId"] = request.TriggeredByUserId.ToString();
            variables["TriggeredAt"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm");

            // Render notification
            var renderedNotification = await _templateService.RenderTemplateAsync(
                request.AlertType,
                channel,
                variables,
                request.Language,
                cancellationToken);

            // Create notification request
            var notificationRequest = new NotificationRequest
            {
                UserId = subscription.UserId,
                Channel = channel,
                Subject = renderedNotification.Subject,
                Body = renderedNotification.Body,
                Type = request.AlertType,
                Priority = 2, // Medium priority for bulk alerts
                Metadata = new Dictionary<string, string>
                {
                    ["TriggeredByUserId"] = request.TriggeredByUserId.ToString(),
                    ["SubscriptionId"] = subscription.Id.ToString(),
                    ["Language"] = request.Language,
                    ["IsBulkAlert"] = "true",
                    ["PlanName"] = plan?.Name ?? "Unknown"
                }
            };

            // Send notification
            var result = await _communicationService.SendNotificationAsync(notificationRequest, cancellationToken);

            if (!result.Success)
            {
                throw new InvalidOperationException($"Failed to send notification: {result.ErrorMessage}");
            }

            _logger.LogInformation("Notification sent to subscription {SubscriptionId} on channel {Channel}. NotificationId: {NotificationId}",
                subscription.Id, channel, result.NotificationId);
        }
    }
}
