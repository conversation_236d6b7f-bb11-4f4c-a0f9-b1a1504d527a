using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.UserProfiles.Commands.CreateUserProfile
{
    public class CreateUserProfileCommandHandler : IRequestHandler<CreateUserProfileCommand, Guid>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;

        public CreateUserProfileCommandHandler(
            IUserProfileRepository userProfileRepository,
            IDocumentSubmissionRepository documentSubmissionRepository)
        {
            _userProfileRepository = userProfileRepository;
            _documentSubmissionRepository = documentSubmissionRepository;
        }

        public async Task<Guid> Handle(CreateUserProfileCommand request, CancellationToken cancellationToken)
        {
            // Check if profile already exists
            var existingProfile = await _userProfileRepository.GetByUserIdAsync(request.UserId);
            if (existingProfile != null)
            {
                throw new InvalidOperationException("User profile already exists");
            }

            // Create user profile
            var userProfile = new UserProfile(request.UserId, request.UserType, request.Email);
            
            if (!string.IsNullOrWhiteSpace(request.FirstName) || !string.IsNullOrWhiteSpace(request.LastName))
            {
                userProfile.UpdatePersonalDetails(request.FirstName, request.LastName, request.PhoneNumber);
            }

            await _userProfileRepository.AddAsync(userProfile);

            // Create document submission for KYC
            var documentSubmission = new DocumentSubmission(request.UserId, request.UserType);
            await _documentSubmissionRepository.AddAsync(documentSubmission);

            return userProfile.Id;
        }
    }
}
