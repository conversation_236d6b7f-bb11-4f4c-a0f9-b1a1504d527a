using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class VehicleMaintenanceRecord : BaseEntity
{
    public Guid VehicleId { get; private set; }
    public DateTime MaintenanceDate { get; private set; }
    public string? Description { get; private set; }
    public decimal? Cost { get; private set; }
    public string? ServiceProvider { get; private set; }
    public string? InvoiceNumber { get; private set; }
    public int? OdometerReading { get; private set; }
    public string? PartsReplaced { get; private set; }
    public string? NextMaintenanceNotes { get; private set; }
    public DateTime? NextMaintenanceDate { get; private set; }
    public bool IsWarrantyWork { get; private set; }
    public string? WarrantyDetails { get; private set; }

    // Navigation properties
    public Vehicle Vehicle { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private VehicleMaintenanceRecord() { }

    public VehicleMaintenanceRecord(
        Guid vehicleId,
        DateTime maintenanceDate,
        string? description = null,
        decimal? cost = null,
        string? serviceProvider = null,
        string? invoiceNumber = null,
        int? odometerReading = null,
        string? partsReplaced = null,
        string? nextMaintenanceNotes = null,
        DateTime? nextMaintenanceDate = null,
        bool isWarrantyWork = false,
        string? warrantyDetails = null)
    {
        VehicleId = vehicleId;
        MaintenanceDate = maintenanceDate;
        Description = description;
        Cost = cost;
        ServiceProvider = serviceProvider;
        InvoiceNumber = invoiceNumber;
        OdometerReading = odometerReading;
        PartsReplaced = partsReplaced;
        NextMaintenanceNotes = nextMaintenanceNotes;
        NextMaintenanceDate = nextMaintenanceDate;
        IsWarrantyWork = isWarrantyWork;
        WarrantyDetails = warrantyDetails;
    }

    public void UpdateCost(decimal cost, string? invoiceNumber = null)
    {
        Cost = cost;
        if (!string.IsNullOrEmpty(invoiceNumber))
            InvoiceNumber = invoiceNumber;
        SetUpdatedAt();
    }

    public void UpdateNextMaintenance(DateTime nextDate, string? notes = null)
    {
        NextMaintenanceDate = nextDate;
        NextMaintenanceNotes = notes;
        SetUpdatedAt();
    }
}
