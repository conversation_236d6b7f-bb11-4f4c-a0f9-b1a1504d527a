using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;

namespace FinancialPayment.Domain.Entities;

public class PaymentMilestone : AggregateRoot
{
    public Guid EscrowAccountId { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public Money Amount { get; private set; } = null!;
    public decimal PayoutPercentage { get; private set; }
    public PaymentMilestoneStatus Status { get; private set; }
    public DateTime? DueDate { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? CompletionNotes { get; private set; }
    public bool IsRequired { get; private set; }
    public bool RequiresApproval { get; private set; }
    public MilestoneApprovalStatus ApprovalStatus { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedAt { get; private set; }
    public string? ApprovalNotes { get; private set; }
    public string? TripLegReference { get; private set; }
    public string? OrderReference { get; private set; }
    public int SequenceNumber { get; private set; }

    // Navigation properties
    public EscrowAccount EscrowAccount { get; private set; } = null!;

    private readonly List<MilestoneDocument> _documents = new();
    public IReadOnlyCollection<MilestoneDocument> Documents => _documents.AsReadOnly();

    private readonly List<string> _completionCriteria = new();
    public IReadOnlyCollection<string> CompletionCriteria => _completionCriteria.AsReadOnly();

    private readonly List<MilestonePayoutRule> _payoutRules = new();
    public IReadOnlyCollection<MilestonePayoutRule> PayoutRules => _payoutRules.AsReadOnly();

    private PaymentMilestone() { } // EF Constructor

    public PaymentMilestone(
        Guid escrowAccountId,
        string name,
        string description,
        Money amount,
        decimal payoutPercentage,
        int sequenceNumber,
        DateTime? dueDate = null,
        bool isRequired = true,
        bool requiresApproval = false,
        string? tripLegReference = null,
        string? orderReference = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Milestone name cannot be empty", nameof(name));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Milestone description cannot be empty", nameof(description));

        if (payoutPercentage < 0 || payoutPercentage > 100)
            throw new ArgumentException("Payout percentage must be between 0 and 100", nameof(payoutPercentage));

        if (sequenceNumber < 1)
            throw new ArgumentException("Sequence number must be greater than 0", nameof(sequenceNumber));

        Id = Guid.NewGuid();
        EscrowAccountId = escrowAccountId;
        Name = name;
        Description = description;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        PayoutPercentage = payoutPercentage;
        SequenceNumber = sequenceNumber;
        DueDate = dueDate;
        IsRequired = isRequired;
        RequiresApproval = requiresApproval;
        Status = PaymentMilestoneStatus.Pending;
        ApprovalStatus = requiresApproval ? MilestoneApprovalStatus.PendingApproval : MilestoneApprovalStatus.NotRequired;
        TripLegReference = tripLegReference;
        OrderReference = orderReference;
        CreatedAt = DateTime.UtcNow;

        AddDomainEvent(new PaymentMilestoneCreatedEvent(Id, Guid.Empty, Guid.Empty, Name, Amount, DueDate ?? DateTime.UtcNow.AddDays(30)));
    }

    public void Complete(string completionNotes, Guid completedBy)
    {
        if (Status != PaymentMilestoneStatus.Pending)
            throw new InvalidOperationException("Only pending milestones can be completed");

        if (RequiresApproval && ApprovalStatus != MilestoneApprovalStatus.Approved)
            throw new InvalidOperationException("Milestone requires approval before completion");

        // Validate completion criteria
        if (!ValidateCompletionCriteria())
            throw new InvalidOperationException("Milestone completion criteria not met");

        Status = PaymentMilestoneStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        CompletionNotes = completionNotes;

        AddDomainEvent(new PaymentMilestoneCompletedEvent(Id, Guid.Empty, Name, Amount, DateTime.UtcNow, completedBy));
    }

    public void Approve(Guid approvedBy, string? approvalNotes = null)
    {
        if (!RequiresApproval)
            throw new InvalidOperationException("This milestone does not require approval");

        if (ApprovalStatus != MilestoneApprovalStatus.PendingApproval)
            throw new InvalidOperationException("Milestone is not pending approval");

        ApprovalStatus = MilestoneApprovalStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedAt = DateTime.UtcNow;
        ApprovalNotes = approvalNotes;

        AddDomainEvent(new PaymentMilestoneApprovedEvent(Id, Guid.Empty, Name, Amount, DateTime.UtcNow, approvedBy, approvalNotes));
    }

    public void Reject(Guid rejectedBy, string rejectionReason)
    {
        if (!RequiresApproval)
            throw new InvalidOperationException("This milestone does not require approval");

        if (ApprovalStatus != MilestoneApprovalStatus.PendingApproval)
            throw new InvalidOperationException("Milestone is not pending approval");

        if (string.IsNullOrWhiteSpace(rejectionReason))
            throw new ArgumentException("Rejection reason is required", nameof(rejectionReason));

        ApprovalStatus = MilestoneApprovalStatus.Rejected;
        ApprovedBy = rejectedBy;
        ApprovedAt = DateTime.UtcNow;
        ApprovalNotes = rejectionReason;

        AddDomainEvent(new PaymentMilestoneRejectedEvent(Id, Guid.Empty, Name, Amount, DateTime.UtcNow, rejectedBy, rejectionReason));
    }

    public void Cancel(string reason, Guid cancelledBy)
    {
        if (Status == PaymentMilestoneStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed milestone");

        if (Status == PaymentMilestoneStatus.Cancelled)
            throw new InvalidOperationException("Milestone is already cancelled");

        Status = PaymentMilestoneStatus.Cancelled;
        CompletionNotes = $"Cancelled: {reason}";

        AddDomainEvent(new PaymentMilestoneCancelledEvent(Id, Guid.Empty, Name, Amount, DateTime.UtcNow, cancelledBy, reason));
    }

    public void Skip(string reason, Guid skippedBy)
    {
        if (IsRequired)
            throw new InvalidOperationException("Cannot skip required milestone");

        if (Status != PaymentMilestoneStatus.Pending)
            throw new InvalidOperationException("Only pending milestones can be skipped");

        Status = PaymentMilestoneStatus.Skipped;
        CompletionNotes = $"Skipped: {reason}";

        AddDomainEvent(new PaymentMilestoneSkippedEvent(Id, Guid.Empty, Name, Amount, DateTime.UtcNow, skippedBy, reason));
    }

    public void AddDocument(MilestoneDocument document)
    {
        if (document == null)
            throw new ArgumentNullException(nameof(document));

        _documents.Add(document);
    }

    public void RemoveDocument(Guid documentId)
    {
        var document = _documents.FirstOrDefault(d => d.Id == documentId);
        if (document != null)
        {
            _documents.Remove(document);
        }
    }

    public void AddCompletionCriterion(string criterion)
    {
        if (string.IsNullOrWhiteSpace(criterion))
            throw new ArgumentException("Completion criterion cannot be empty", nameof(criterion));

        if (!_completionCriteria.Contains(criterion))
        {
            _completionCriteria.Add(criterion);
        }
    }

    public void RemoveCompletionCriterion(string criterion)
    {
        _completionCriteria.Remove(criterion);
    }

    public void AddPayoutRule(MilestonePayoutRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        _payoutRules.Add(rule);
    }

    public void UpdateDueDate(DateTime? newDueDate)
    {
        if (Status == PaymentMilestoneStatus.Completed)
            throw new InvalidOperationException("Cannot update due date for completed milestone");

        DueDate = newDueDate;
    }

    public void UpdateAmount(Money newAmount)
    {
        if (Status == PaymentMilestoneStatus.Completed)
            throw new InvalidOperationException("Cannot update amount for completed milestone");

        Amount = newAmount ?? throw new ArgumentNullException(nameof(newAmount));
    }

    public bool IsOverdue()
    {
        return DueDate.HasValue &&
               DateTime.UtcNow > DueDate.Value &&
               Status == PaymentMilestoneStatus.Pending;
    }

    public bool CanComplete()
    {
        if (Status != PaymentMilestoneStatus.Pending)
            return false;

        if (RequiresApproval && ApprovalStatus != MilestoneApprovalStatus.Approved)
            return false;

        return ValidateCompletionCriteria();
    }

    public bool HasRequiredDocuments()
    {
        var requiredDocuments = _documents.Where(d => d.IsRequired);
        return requiredDocuments.All(d => d.IsVerified);
    }

    private bool ValidateCompletionCriteria()
    {
        // Basic validation - can be extended with more complex rules
        if (!HasRequiredDocuments())
            return false;

        // Additional criteria validation would go here
        return true;
    }

    public TimeSpan? GetTimeUntilDue()
    {
        if (!DueDate.HasValue || Status != PaymentMilestoneStatus.Pending)
            return null;

        var timeUntilDue = DueDate.Value - DateTime.UtcNow;
        return timeUntilDue.TotalMilliseconds > 0 ? timeUntilDue : TimeSpan.Zero;
    }

    public decimal CalculateActualPayout()
    {
        if (Status != PaymentMilestoneStatus.Completed)
            return 0;

        // Apply payout rules if any
        var basePayout = Amount.Amount;
        foreach (var rule in _payoutRules)
        {
            basePayout = rule.ApplyRule(basePayout);
        }

        return basePayout;
    }
}

public enum PaymentMilestoneStatus
{
    Pending = 1,
    Completed = 2,
    Cancelled = 3,
    Skipped = 4
}

public enum MilestoneApprovalStatus
{
    NotRequired = 1,
    PendingApproval = 2,
    Approved = 3,
    Rejected = 4
}
