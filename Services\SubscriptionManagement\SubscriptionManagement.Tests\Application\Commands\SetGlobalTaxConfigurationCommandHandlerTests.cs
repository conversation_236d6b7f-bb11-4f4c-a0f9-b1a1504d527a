using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.SetGlobalTaxConfiguration;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Application.Commands
{
    public class SetGlobalTaxConfigurationCommandHandlerTests
    {
        private readonly Mock<IGlobalTaxConfigurationRepository> _mockRepository;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly Mock<ILogger<SetGlobalTaxConfigurationCommandHandler>> _mockLogger;
        private readonly SetGlobalTaxConfigurationCommandHandler _handler;

        public SetGlobalTaxConfigurationCommandHandlerTests()
        {
            _mockRepository = new Mock<IGlobalTaxConfigurationRepository>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _mockLogger = new Mock<ILogger<SetGlobalTaxConfigurationCommandHandler>>();
            _handler = new SetGlobalTaxConfigurationCommandHandler(
                _mockRepository.Object,
                _mockMessageBroker.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_WithNewConfiguration_ShouldCreateAndReturnId()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 18.0m,
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                Description = "Test GST",
                CreatedByUserId = Guid.NewGuid()
            };

            _mockRepository.Setup(r => r.GetByTaxTypeAndRegionAsync(It.IsAny<TaxType>(), It.IsAny<string>()))
                .ReturnsAsync((GlobalTaxConfiguration?)null);

            var savedConfig = new GlobalTaxConfiguration(
                Domain.ValueObjects.TaxConfiguration.Create(
                    command.TaxType, command.Rate, command.IsIncluded, 
                    command.ApplicableRegions, command.EffectiveDate),
                command.Priority, command.CreatedByUserId, command.Description);

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<GlobalTaxConfiguration>()))
                .ReturnsAsync(savedConfig);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().Be(savedConfig.Id);
            _mockRepository.Verify(r => r.AddAsync(It.IsAny<GlobalTaxConfiguration>()), Times.Once);
            _mockMessageBroker.Verify(m => m.PublishAsync(
                "tax.global_configuration_changed", 
                It.IsAny<object>(), 
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithExistingConfiguration_ShouldUpdateAndReturnId()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 20.0m,
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                Description = "Updated GST",
                CreatedByUserId = Guid.NewGuid()
            };

            var existingConfig = new GlobalTaxConfiguration(
                Domain.ValueObjects.TaxConfiguration.Create(
                    TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow),
                1, Guid.NewGuid(), "Old GST");

            _mockRepository.Setup(r => r.GetByTaxTypeAndRegionAsync(command.TaxType, command.ApplicableRegions.First()))
                .ReturnsAsync(existingConfig);

            _mockRepository.Setup(r => r.UpdateAsync(It.IsAny<GlobalTaxConfiguration>()))
                .ReturnsAsync(existingConfig);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().Be(existingConfig.Id);
            _mockRepository.Verify(r => r.UpdateAsync(It.IsAny<GlobalTaxConfiguration>()), Times.Once);
            _mockMessageBroker.Verify(m => m.PublishAsync(
                "tax.global_configuration_changed", 
                It.IsAny<object>(), 
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithInvalidRate_ShouldThrowException()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = -5.0m, // Invalid rate
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                CreatedByUserId = Guid.NewGuid()
            };

            // Act & Assert
            await Assert.ThrowsAsync<Domain.Exceptions.SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));

            _mockRepository.Verify(r => r.AddAsync(It.IsAny<GlobalTaxConfiguration>()), Times.Never);
            _mockRepository.Verify(r => r.UpdateAsync(It.IsAny<GlobalTaxConfiguration>()), Times.Never);
        }

        [Fact]
        public async Task Handle_WithEmptyRegions_ShouldThrowException()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 18.0m,
                IsIncluded = false,
                ApplicableRegions = new List<string>(), // Empty regions
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                CreatedByUserId = Guid.NewGuid()
            };

            // Act & Assert
            await Assert.ThrowsAsync<Domain.Exceptions.SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WithGSTRateAbove28InIndia_ShouldThrowException()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 30.0m, // Above 28% for India
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                CreatedByUserId = Guid.NewGuid()
            };

            // Act & Assert
            await Assert.ThrowsAsync<Domain.Exceptions.SubscriptionDomainException>(
                () => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WithRepositoryException_ShouldPropagateException()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 18.0m,
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                CreatedByUserId = Guid.NewGuid()
            };

            _mockRepository.Setup(r => r.GetByTaxTypeAndRegionAsync(It.IsAny<TaxType>(), It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldLogInformationMessages()
        {
            // Arrange
            var command = new SetGlobalTaxConfigurationCommand
            {
                TaxType = TaxType.GST,
                Rate = 18.0m,
                IsIncluded = false,
                ApplicableRegions = new List<string> { "IN" },
                EffectiveDate = DateTime.UtcNow,
                Priority = 1,
                CreatedByUserId = Guid.NewGuid()
            };

            _mockRepository.Setup(r => r.GetByTaxTypeAndRegionAsync(It.IsAny<TaxType>(), It.IsAny<string>()))
                .ReturnsAsync((GlobalTaxConfiguration?)null);

            var savedConfig = new GlobalTaxConfiguration(
                Domain.ValueObjects.TaxConfiguration.Create(
                    command.TaxType, command.Rate, command.IsIncluded, 
                    command.ApplicableRegions, command.EffectiveDate),
                command.Priority, command.CreatedByUserId);

            _mockRepository.Setup(r => r.AddAsync(It.IsAny<GlobalTaxConfiguration>()))
                .ReturnsAsync(savedConfig);

            // Act
            await _handler.Handle(command, CancellationToken.None);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Setting global tax configuration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.AtLeastOnce);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully set global tax configuration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
}
