using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetFilteredRfqs;

public class GetFilteredRfqsQueryHandler : IRequestHandler<GetFilteredRfqsQuery, PagedResult<RfqSummaryDto>>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetFilteredRfqsQueryHandler> _logger;

    public GetFilteredRfqsQueryHandler(
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetFilteredRfqsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PagedResult<RfqSummaryDto>> Handle(GetFilteredRfqsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting filtered RFQs for user {UserId}, Page: {Page}, PageSize: {PageSize}",
            request.RequestingUserId, request.Page, request.PageSize);

        // Validate pagination parameters
        if (request.Page < 1) request.Page = 1;
        if (request.PageSize < 1 || request.PageSize > 100) request.PageSize = 20;

        // Parse enum filters
        var vehicleTypes = ParseEnumList<VehicleType>(request.VehicleTypes);
        var loadTypes = ParseEnumList<LoadType>(request.LoadTypes);
        var statuses = ParseEnumList<RfqStatus>(request.Statuses);

        try
        {
            // Get filtered RFQs
            var (rfqs, totalCount) = await _rfqRepository.GetFilteredRfqsAsync(
                request.Page,
                request.PageSize,
                request.FromDate,
                request.ToDate,
                vehicleTypes,
                loadTypes,
                statuses,
                request.TransportCompanyId,
                request.BrokerId,
                request.CarrierId,
                request.PickupCity,
                request.DeliveryCity,
                request.PickupState,
                request.DeliveryState,
                request.MinBudget,
                request.MaxBudget,
                request.IsUrgent,
                request.HasDocuments,
                request.HasBids,
                request.ExcludeExpired,
                cancellationToken);

            // Map to DTOs
            var rfqDtos = _mapper.Map<List<RfqSummaryDto>>(rfqs);

            var result = new PagedResult<RfqSummaryDto>
            {
                Items = rfqDtos,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };

            _logger.LogInformation("Successfully retrieved {Count} filtered RFQs out of {TotalCount} for user {UserId}",
                rfqDtos.Count, totalCount, request.RequestingUserId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving filtered RFQs for user {UserId}", request.RequestingUserId);
            throw;
        }
    }

    private static List<T>? ParseEnumList<T>(List<string>? stringValues) where T : struct, Enum
    {
        if (stringValues?.Any() != true)
            return null;

        var result = new List<T>();
        foreach (var stringValue in stringValues)
        {
            if (Enum.TryParse<T>(stringValue, true, out var enumValue))
            {
                result.Add(enumValue);
            }
        }

        return result.Any() ? result : null;
    }
}
