using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Documents.Commands.PreviewOcrData;

public class PreviewOcrDataCommandHandler : IRequestHandler<PreviewOcrDataCommand, PreviewOcrDataResponse>
{
    private readonly IOcrService _ocrService;
    private readonly ILogger<PreviewOcrDataCommandHandler> _logger;

    public PreviewOcrDataCommandHandler(
        IOcrService ocrService,
        ILogger<PreviewOcrDataCommandHandler> logger)
    {
        _ocrService = ocrService;
        _logger = logger;
    }

    public async Task<PreviewOcrDataResponse> Handle(PreviewOcrDataCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Previewing OCR data for file: {FilePath}, DocumentType: {DocumentType}", 
            request.FilePath, request.DocumentType);

        try
        {
            // Extract data using OCR
            var ocrResult = await _ocrService.ExtractDataAsync(request.FilePath, request.DocumentType, cancellationToken);
            
            if (!ocrResult.Success)
            {
                return new PreviewOcrDataResponse
                {
                    Success = false,
                    Message = $"OCR extraction failed: {ocrResult.ErrorMessage}",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            // Generate form field mappings
            var formFieldMappings = GenerateFormFieldMappings(ocrResult.ExtractedData, request.DocumentType, ocrResult.ConfidenceScore);

            // Generate validation warnings and suggestions
            var validationWarnings = GenerateValidationWarnings(ocrResult.ExtractedData, request.DocumentType);
            var suggestions = GenerateSuggestions(ocrResult.ExtractedData, request.DocumentType, ocrResult.ConfidenceScore);

            var response = new PreviewOcrDataResponse
            {
                Success = true,
                Message = "OCR data extracted successfully",
                ExtractedData = ocrResult.ExtractedData,
                FormFieldMappings = formFieldMappings,
                ConfidenceScore = ocrResult.ConfidenceScore,
                ValidationWarnings = validationWarnings,
                Suggestions = suggestions,
                ProcessedAt = DateTime.UtcNow
            };

            if (request.IncludeRawText)
            {
                response.RawText = ocrResult.ExtractedText;
            }

            _logger.LogInformation("OCR preview completed successfully. Confidence: {Confidence}%", 
                ocrResult.ConfidenceScore * 100);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing OCR data for file: {FilePath}", request.FilePath);
            return new PreviewOcrDataResponse
            {
                Success = false,
                Message = $"Error processing document: {ex.Message}",
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    private Dictionary<string, FormFieldMapping> GenerateFormFieldMappings(
        Dictionary<string, object> extractedData, 
        DocumentType documentType, 
        decimal overallConfidence)
    {
        var mappings = new Dictionary<string, FormFieldMapping>();

        switch (documentType)
        {
            case DocumentType.PanCard:
                MapPanCardFields(extractedData, mappings, overallConfidence);
                break;
            case DocumentType.AadharCard:
                MapAadharCardFields(extractedData, mappings, overallConfidence);
                break;
            case DocumentType.GstCertificate:
                MapGstCertificateFields(extractedData, mappings, overallConfidence);
                break;
            case DocumentType.DrivingLicense:
                MapDrivingLicenseFields(extractedData, mappings, overallConfidence);
                break;
            case DocumentType.VehicleRegistration:
                MapVehicleRegistrationFields(extractedData, mappings, overallConfidence);
                break;
            case DocumentType.VehicleInsurance:
                MapVehicleInsuranceFields(extractedData, mappings, overallConfidence);
                break;
        }

        return mappings;
    }

    private void MapPanCardFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("pan_number"))
        {
            mappings["PanNumber"] = new FormFieldMapping
            {
                FieldName = "PAN Number",
                ExtractedValue = extractedData["pan_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }

        if (extractedData.ContainsKey("name"))
        {
            mappings["FullName"] = new FormFieldMapping
            {
                FieldName = "Full Name",
                ExtractedValue = extractedData["name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("father_name"))
        {
            mappings["FatherName"] = new FormFieldMapping
            {
                FieldName = "Father's Name",
                ExtractedValue = extractedData["father_name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("date_of_birth"))
        {
            mappings["DateOfBirth"] = new FormFieldMapping
            {
                FieldName = "Date of Birth",
                ExtractedValue = extractedData["date_of_birth"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }
    }

    private void MapAadharCardFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("aadhar_number"))
        {
            mappings["AadharNumber"] = new FormFieldMapping
            {
                FieldName = "Aadhar Number",
                ExtractedValue = extractedData["aadhar_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("name"))
        {
            mappings["FullName"] = new FormFieldMapping
            {
                FieldName = "Full Name",
                ExtractedValue = extractedData["name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("address"))
        {
            mappings["Address"] = new FormFieldMapping
            {
                FieldName = "Address",
                ExtractedValue = extractedData["address"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.6m
            };
        }

        if (extractedData.ContainsKey("date_of_birth"))
        {
            mappings["DateOfBirth"] = new FormFieldMapping
            {
                FieldName = "Date of Birth",
                ExtractedValue = extractedData["date_of_birth"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }

        if (extractedData.ContainsKey("gender"))
        {
            mappings["Gender"] = new FormFieldMapping
            {
                FieldName = "Gender",
                ExtractedValue = extractedData["gender"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }
    }

    private void MapGstCertificateFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("gst_number"))
        {
            mappings["GstNumber"] = new FormFieldMapping
            {
                FieldName = "GST Number",
                ExtractedValue = extractedData["gst_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("business_name"))
        {
            mappings["BusinessName"] = new FormFieldMapping
            {
                FieldName = "Business Name",
                ExtractedValue = extractedData["business_name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("address"))
        {
            mappings["BusinessAddress"] = new FormFieldMapping
            {
                FieldName = "Business Address",
                ExtractedValue = extractedData["address"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.6m
            };
        }
    }

    private void MapDrivingLicenseFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("license_number"))
        {
            mappings["LicenseNumber"] = new FormFieldMapping
            {
                FieldName = "License Number",
                ExtractedValue = extractedData["license_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("name"))
        {
            mappings["FullName"] = new FormFieldMapping
            {
                FieldName = "Full Name",
                ExtractedValue = extractedData["name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("address"))
        {
            mappings["Address"] = new FormFieldMapping
            {
                FieldName = "Address",
                ExtractedValue = extractedData["address"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.6m
            };
        }

        if (extractedData.ContainsKey("issue_date"))
        {
            mappings["IssueDate"] = new FormFieldMapping
            {
                FieldName = "Issue Date",
                ExtractedValue = extractedData["issue_date"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }

        if (extractedData.ContainsKey("expiry_date"))
        {
            mappings["ExpiryDate"] = new FormFieldMapping
            {
                FieldName = "Expiry Date",
                ExtractedValue = extractedData["expiry_date"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }
    }

    private void MapVehicleRegistrationFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("vehicle_number"))
        {
            mappings["VehicleNumber"] = new FormFieldMapping
            {
                FieldName = "Vehicle Number",
                ExtractedValue = extractedData["vehicle_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("owner_name"))
        {
            mappings["OwnerName"] = new FormFieldMapping
            {
                FieldName = "Owner Name",
                ExtractedValue = extractedData["owner_name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("vehicle_model"))
        {
            mappings["VehicleModel"] = new FormFieldMapping
            {
                FieldName = "Vehicle Model",
                ExtractedValue = extractedData["vehicle_model"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("engine_number"))
        {
            mappings["EngineNumber"] = new FormFieldMapping
            {
                FieldName = "Engine Number",
                ExtractedValue = extractedData["engine_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }

        if (extractedData.ContainsKey("chassis_number"))
        {
            mappings["ChassisNumber"] = new FormFieldMapping
            {
                FieldName = "Chassis Number",
                ExtractedValue = extractedData["chassis_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }
    }

    private void MapVehicleInsuranceFields(Dictionary<string, object> extractedData, Dictionary<string, FormFieldMapping> mappings, decimal confidence)
    {
        if (extractedData.ContainsKey("policy_number"))
        {
            mappings["PolicyNumber"] = new FormFieldMapping
            {
                FieldName = "Policy Number",
                ExtractedValue = extractedData["policy_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("insured_name"))
        {
            mappings["InsuredName"] = new FormFieldMapping
            {
                FieldName = "Insured Name",
                ExtractedValue = extractedData["insured_name"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.7m
            };
        }

        if (extractedData.ContainsKey("vehicle_number"))
        {
            mappings["VehicleNumber"] = new FormFieldMapping
            {
                FieldName = "Vehicle Number",
                ExtractedValue = extractedData["vehicle_number"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.9m
            };
        }

        if (extractedData.ContainsKey("policy_start_date"))
        {
            mappings["PolicyStartDate"] = new FormFieldMapping
            {
                FieldName = "Policy Start Date",
                ExtractedValue = extractedData["policy_start_date"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }

        if (extractedData.ContainsKey("policy_end_date"))
        {
            mappings["PolicyEndDate"] = new FormFieldMapping
            {
                FieldName = "Policy End Date",
                ExtractedValue = extractedData["policy_end_date"].ToString() ?? "",
                Confidence = confidence,
                RequiresVerification = confidence < 0.8m
            };
        }
    }

    private List<string> GenerateValidationWarnings(Dictionary<string, object> extractedData, DocumentType documentType)
    {
        var warnings = new List<string>();

        // Add document-specific validation warnings
        switch (documentType)
        {
            case DocumentType.PanCard:
                if (!extractedData.ContainsKey("pan_number"))
                    warnings.Add("PAN number could not be extracted clearly");
                break;
            case DocumentType.AadharCard:
                if (!extractedData.ContainsKey("aadhar_number"))
                    warnings.Add("Aadhar number could not be extracted clearly");
                break;
            case DocumentType.GstCertificate:
                if (!extractedData.ContainsKey("gst_number"))
                    warnings.Add("GST number could not be extracted clearly");
                break;
        }

        return warnings;
    }

    private List<string> GenerateSuggestions(Dictionary<string, object> extractedData, DocumentType documentType, decimal confidence)
    {
        var suggestions = new List<string>();

        if (confidence < 0.7m)
        {
            suggestions.Add("Document quality is low. Consider uploading a clearer image.");
        }

        if (confidence < 0.5m)
        {
            suggestions.Add("Very low confidence in extracted data. Manual entry recommended.");
        }

        if (extractedData.Count < 3)
        {
            suggestions.Add("Limited data extracted. Ensure the document is fully visible and well-lit.");
        }

        return suggestions;
    }
}
