using Shared.Domain.Common;
using System;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Data warehouse connection entity for external data source connections
/// </summary>
public class DataWarehouseConnection : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string ConnectionType { get; private set; }
    public string Server { get; private set; }
    public string Database { get; private set; }
    public int Port { get; private set; }
    public string Username { get; private set; }
    public string PasswordHash { get; private set; }
    public bool UseIntegratedSecurity { get; private set; }
    public string ConnectionProperties { get; private set; }
    public string Status { get; private set; }
    public Guid CreatedBy { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? LastConnected { get; private set; }
    public DateTime? LastSynchronized { get; private set; }

    private DataWarehouseConnection()
    {
        Name = string.Empty;
        Description = string.Empty;
        ConnectionType = string.Empty;
        Server = string.Empty;
        Database = string.Empty;
        Username = string.Empty;
        PasswordHash = string.Empty;
        ConnectionProperties = string.Empty;
        Status = string.Empty;
    }

    public DataWarehouseConnection(
        string name,
        string description,
        string connectionType,
        string server,
        string database,
        int port,
        string username,
        string passwordHash,
        bool useIntegratedSecurity,
        string connectionProperties,
        Guid createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        ConnectionType = connectionType ?? throw new ArgumentNullException(nameof(connectionType));
        Server = server ?? throw new ArgumentNullException(nameof(server));
        Database = database ?? throw new ArgumentNullException(nameof(database));
        Port = port;
        Username = username ?? throw new ArgumentNullException(nameof(username));
        PasswordHash = passwordHash ?? throw new ArgumentNullException(nameof(passwordHash));
        UseIntegratedSecurity = useIntegratedSecurity;
        ConnectionProperties = connectionProperties ?? "{}";
        CreatedBy = createdBy;
        Status = "Created";
        IsActive = true;
    }

    public void UpdateName(string name)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        SetUpdatedAt();
    }

    public void UpdateDescription(string description)
    {
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SetUpdatedAt();
    }

    public void UpdateConnectionDetails(string server, string database, int port, string username, string passwordHash, bool useIntegratedSecurity, string connectionProperties)
    {
        Server = server ?? throw new ArgumentNullException(nameof(server));
        Database = database ?? throw new ArgumentNullException(nameof(database));
        Port = port;
        Username = username ?? throw new ArgumentNullException(nameof(username));
        PasswordHash = passwordHash ?? throw new ArgumentNullException(nameof(passwordHash));
        UseIntegratedSecurity = useIntegratedSecurity;
        ConnectionProperties = connectionProperties ?? "{}";
        SetUpdatedAt();
    }

    public void UpdateStatus(string status)
    {
        Status = status ?? throw new ArgumentNullException(nameof(status));
        SetUpdatedAt();
    }

    public void RecordConnection()
    {
        LastConnected = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void RecordSynchronization()
    {
        LastSynchronized = DateTime.UtcNow;
        SetUpdatedAt();
    }

    public void Activate()
    {
        IsActive = true;
        SetUpdatedAt();
    }

    public void Deactivate()
    {
        IsActive = false;
        SetUpdatedAt();
    }
}
