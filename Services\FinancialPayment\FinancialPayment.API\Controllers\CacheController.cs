using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Infrastructure.Services;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CacheController : ControllerBase
{
    private readonly ICacheManagementService _cacheManagementService;
    private readonly IPaymentCacheService _paymentCacheService;
    private readonly ILogger<CacheController> _logger;

    public CacheController(
        ICacheManagementService cacheManagementService,
        IPaymentCacheService paymentCacheService,
        ILogger<CacheController> logger)
    {
        _cacheManagementService = cacheManagementService;
        _paymentCacheService = paymentCacheService;
        _logger = logger;
    }

    /// <summary>
    /// Get cache statistics
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<CacheStatistics>> GetCacheStatistics()
    {
        try
        {
            var statistics = await _cacheManagementService.GetCacheStatisticsAsync();
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Perform cache health check
    /// </summary>
    [HttpGet("health")]
    [AllowAnonymous]
    public async Task<ActionResult<CacheHealthCheck>> GetCacheHealth()
    {
        try
        {
            var healthCheck = await _cacheManagementService.PerformHealthCheckAsync();
            
            if (healthCheck.IsHealthy)
            {
                return Ok(healthCheck);
            }
            else
            {
                return StatusCode(503, healthCheck); // Service Unavailable
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing cache health check");
            return StatusCode(500, new CacheHealthCheck
            {
                IsHealthy = false,
                ErrorMessage = "Health check failed",
                ResponseTime = TimeSpan.Zero
            });
        }
    }

    /// <summary>
    /// Get cache information
    /// </summary>
    [HttpGet("info")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Dictionary<string, object>>> GetCacheInfo()
    {
        try
        {
            var info = await _cacheManagementService.GetCacheInfoAsync();
            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache info");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cache keys by pattern
    /// </summary>
    [HttpGet("keys")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<List<string>>> GetCacheKeys(
        [FromQuery] string pattern = "*",
        [FromQuery] int limit = 100)
    {
        try
        {
            var keys = await _cacheManagementService.GetCacheKeysAsync(pattern, limit);
            return Ok(keys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache keys");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get key expiration times
    /// </summary>
    [HttpPost("keys/expiration")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Dictionary<string, TimeSpan?>>> GetKeyExpirationTimes([FromBody] List<string> keys)
    {
        try
        {
            var expirationTimes = await _cacheManagementService.GetKeyExpirationTimesAsync(keys);
            return Ok(expirationTimes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting key expiration times");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Set key expiration
    /// </summary>
    [HttpPost("keys/{key}/expire")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> SetKeyExpiration(string key, [FromBody] SetExpirationRequest request)
    {
        try
        {
            await _cacheManagementService.SetKeyExpirationAsync(key, request.Expiration);
            return Ok(new { message = "Key expiration set successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting key expiration for {Key}", key);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate cache
    /// </summary>
    [HttpPost("invalidate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> InvalidateCache([FromBody] CacheInvalidationRequest request)
    {
        try
        {
            await _cacheManagementService.InvalidateCacheAsync(request);
            
            _logger.LogInformation("Cache invalidated by user. Keys: {KeyCount}, Patterns: {PatternCount}",
                request.Keys.Count, request.Patterns.Count);
            
            return Ok(new { message = "Cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Warmup cache
    /// </summary>
    [HttpPost("warmup")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> WarmupCache([FromBody] CacheWarmupRequest request)
    {
        try
        {
            await _cacheManagementService.WarmupCacheAsync(request);
            
            _logger.LogInformation("Cache warmup initiated for types: {CacheTypes}", 
                string.Join(", ", request.CacheTypes));
            
            return Ok(new { message = "Cache warmup completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error warming up cache");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Flush cache
    /// </summary>
    [HttpPost("flush")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> FlushCache([FromBody] FlushCacheRequest? request = null)
    {
        try
        {
            var success = await _cacheManagementService.FlushCacheAsync(request?.Pattern);
            
            if (success)
            {
                _logger.LogWarning("Cache flushed. Pattern: {Pattern}", request?.Pattern ?? "ALL");
                return Ok(new { message = "Cache flushed successfully" });
            }
            else
            {
                return StatusCode(500, "Failed to flush cache");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error flushing cache");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cache memory usage
    /// </summary>
    [HttpGet("memory")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<object>> GetCacheMemoryUsage()
    {
        try
        {
            var memoryUsage = await _cacheManagementService.GetCacheMemoryUsageAsync();
            var keyCountByPattern = await _cacheManagementService.GetKeyCountByPatternAsync();
            
            return Ok(new
            {
                memoryUsageBytes = memoryUsage,
                memoryUsageMB = memoryUsage / 1024.0 / 1024.0,
                keyCountByPattern = keyCountByPattern,
                totalKeys = keyCountByPattern.Values.Sum()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache memory usage");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate payment cache
    /// </summary>
    [HttpDelete("payment/{paymentId}")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult> InvalidatePaymentCache(Guid paymentId)
    {
        try
        {
            await _paymentCacheService.RemovePaymentAsync(paymentId);
            return Ok(new { message = "Payment cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating payment cache for {PaymentId}", paymentId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate subscription cache
    /// </summary>
    [HttpDelete("subscription/{subscriptionId}")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult> InvalidateSubscriptionCache(Guid subscriptionId)
    {
        try
        {
            await _paymentCacheService.RemoveSubscriptionAsync(subscriptionId);
            return Ok(new { message = "Subscription cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating subscription cache for {SubscriptionId}", subscriptionId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate all plans cache
    /// </summary>
    [HttpDelete("plans")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> InvalidatePlansCache()
    {
        try
        {
            await _paymentCacheService.InvalidatePlansAsync();
            return Ok(new { message = "Plans cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating plans cache");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate fraud rules cache
    /// </summary>
    [HttpDelete("fraud-rules")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> InvalidateFraudRulesCache()
    {
        try
        {
            await _paymentCacheService.InvalidateFraudRulesAsync();
            return Ok(new { message = "Fraud rules cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating fraud rules cache");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Invalidate user payment methods cache
    /// </summary>
    [HttpDelete("user/{userId}/payment-methods")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult> InvalidateUserPaymentMethodsCache(Guid userId)
    {
        try
        {
            await _paymentCacheService.RemoveUserPaymentMethodsAsync(userId);
            return Ok(new { message = "User payment methods cache invalidated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating user payment methods cache for {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cache performance metrics
    /// </summary>
    [HttpGet("performance")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<object>> GetCachePerformanceMetrics()
    {
        try
        {
            var statistics = await _cacheManagementService.GetCacheStatisticsAsync();
            
            return Ok(new
            {
                hitRatio = statistics.HitRatio,
                totalRequests = statistics.TotalRequests,
                cacheHits = statistics.CacheHits,
                cacheMisses = statistics.CacheMisses,
                memoryUsageBytes = statistics.MemoryUsageBytes,
                totalKeys = statistics.TotalKeys,
                keysByPattern = statistics.KeysByPattern,
                lastUpdated = statistics.LastUpdated
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache performance metrics");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class SetExpirationRequest
{
    public TimeSpan Expiration { get; set; }
}

public class FlushCacheRequest
{
    public string? Pattern { get; set; }
}
