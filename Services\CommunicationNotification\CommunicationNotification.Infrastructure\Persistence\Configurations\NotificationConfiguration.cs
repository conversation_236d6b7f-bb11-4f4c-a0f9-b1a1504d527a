using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CommunicationNotification.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for Notification
/// </summary>
public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.ToTable("notifications");

        // Primary key
        builder.HasKey(n => n.Id);
        builder.Property(n => n.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(n => n.UserId)
            .IsRequired();

        builder.Property(n => n.MessageType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(n => n.Priority)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(n => n.Channel)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(n => n.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(n => n.ScheduledAt)
            .IsRequired();

        builder.Property(n => n.SentAt)
            .IsRequired(false);

        builder.Property(n => n.DeliveredAt)
            .IsRequired(false);

        builder.Property(n => n.ReadAt)
            .IsRequired(false);

        builder.Property(n => n.RelatedEntityId)
            .IsRequired(false);

        builder.Property(n => n.RelatedEntityType)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(n => n.ExternalId)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(n => n.RetryCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(n => n.FailureReason)
            .HasMaxLength(1000)
            .IsRequired(false);

        // Metadata as JSON
        builder.Property(n => n.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(n => n.CreatedAt)
            .IsRequired();

        builder.Property(n => n.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasMany<DeliveryAttempt>()
            .WithOne()
            .HasForeignKey(da => da.NotificationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for Message
/// </summary>
public class MessageConfiguration : IEntityTypeConfiguration<Message>
{
    public void Configure(EntityTypeBuilder<Message> builder)
    {
        builder.ToTable("messages");

        // Primary key
        builder.HasKey(m => m.Id);
        builder.Property(m => m.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(m => m.SenderId)
            .IsRequired();

        builder.Property(m => m.ReceiverId)
            .IsRequired();

        builder.Property(m => m.MessageType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(m => m.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(m => m.Priority)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(m => m.SentAt)
            .IsRequired();

        builder.Property(m => m.DeliveredAt)
            .IsRequired(false);

        builder.Property(m => m.ReadAt)
            .IsRequired(false);

        builder.Property(m => m.ConversationThreadId)
            .IsRequired(false);

        builder.Property(m => m.RelatedEntityId)
            .IsRequired(false);

        builder.Property(m => m.RelatedEntityType)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(m => m.RetryCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(m => m.FailureReason)
            .HasMaxLength(1000)
            .IsRequired(false);

        // Metadata as JSON
        builder.Property(m => m.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(m => m.CreatedAt)
            .IsRequired();

        builder.Property(m => m.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasOne<ConversationThread>()
            .WithMany(ct => ct.Messages)
            .HasForeignKey(m => m.ConversationThreadId)
            .OnDelete(DeleteBehavior.SetNull);

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for UserPreference
/// </summary>
public class UserPreferenceConfiguration : IEntityTypeConfiguration<UserPreference>
{
    public void Configure(EntityTypeBuilder<UserPreference> builder)
    {
        builder.ToTable("user_preferences");

        // Primary key
        builder.HasKey(up => up.Id);
        builder.Property(up => up.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(up => up.UserId)
            .IsRequired();

        builder.Property(up => up.UserRole)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(up => up.PreferredLanguage)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(up => up.TimeZone)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(up => up.IsOnline)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(up => up.LastSeenAt)
            .IsRequired(false);

        // Custom settings as JSON
        builder.Property(up => up.CustomSettings)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(up => up.CreatedAt)
            .IsRequired();

        builder.Property(up => up.UpdatedAt)
            .IsRequired();

        // Unique constraint on UserId
        builder.HasIndex(up => up.UserId)
            .IsUnique();

        // Indexes are configured in DbContext
    }
}

/// <summary>
/// Entity configuration for ConversationThread
/// </summary>
public class ConversationThreadConfiguration : IEntityTypeConfiguration<ConversationThread>
{
    public void Configure(EntityTypeBuilder<ConversationThread> builder)
    {
        builder.ToTable("conversation_threads");

        // Primary key
        builder.HasKey(ct => ct.Id);
        builder.Property(ct => ct.Id)
            .ValueGeneratedNever();

        // Properties
        builder.Property(ct => ct.Title)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(ct => ct.Description)
            .HasMaxLength(1000)
            .IsRequired(false);

        builder.Property(ct => ct.Type)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(ct => ct.RelatedEntityId)
            .IsRequired(false);

        builder.Property(ct => ct.RelatedEntityType)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(ct => ct.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(ct => ct.CreatedAt)
            .IsRequired();

        builder.Property(ct => ct.CreatedBy)
            .IsRequired();

        builder.Property(ct => ct.LastActivity)
            .IsRequired();

        builder.Property(ct => ct.ClosedAt)
            .IsRequired(false);

        builder.Property(ct => ct.ClosedByUserId)
            .IsRequired(false);

        builder.Property(ct => ct.CloseReason)
            .HasMaxLength(500)
            .IsRequired(false);

        // Metadata as JSON
        builder.Property(ct => ct.Metadata)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new())
            .HasColumnType("jsonb");

        // Base entity properties
        builder.Property(ct => ct.UpdatedAt)
            .IsRequired();

        // Relationships
        builder.HasMany(ct => ct.Messages)
            .WithOne()
            .HasForeignKey(m => m.ConversationThreadId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(ct => ct.Participants)
            .WithOne()
            .HasForeignKey(cp => cp.ConversationThreadId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes are configured in DbContext
    }
}
