using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// Twilio Voice provider implementation
/// </summary>
public class TwilioVoiceProvider : IVoiceProvider
{
    private readonly ILogger<TwilioVoiceProvider> _logger;
    private readonly TwilioVoiceSettings _settings;

    public TwilioVoiceProvider(
        IConfiguration configuration,
        ILogger<TwilioVoiceProvider> logger)
    {
        _logger = logger;
        _settings = configuration.GetSection("TwilioVoice").Get<TwilioVoiceSettings>() ?? new();

        if (!string.IsNullOrEmpty(_settings.AccountSid) && !string.IsNullOrEmpty(_settings.AuthToken))
        {
            TwilioClient.Init(_settings.AccountSid, _settings.AuthToken);
        }
    }

    public async Task<VoiceCallResult> MakeCallAsync(VoiceCallRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Making voice call to {PhoneNumber} via Twilio", request.PhoneNumber);

            // Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return VoiceCallResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create TwiML for the call
            var twimlUrl = await CreateTwiMLAsync(request, cancellationToken);

            // Format phone numbers
            var toPhoneNumber = FormatPhoneNumber(request.PhoneNumber);
            var fromPhoneNumber = new PhoneNumber(_settings.FromPhoneNumber);

            // Create call options
            var callOptions = new CreateCallOptions(toPhoneNumber, fromPhoneNumber, new Uri(twimlUrl))
            {
                Timeout = _settings.CallTimeoutSeconds,
                Record = request.RecordCall,
                StatusCallback = !string.IsNullOrEmpty(_settings.StatusCallbackUrl) ? new Uri(_settings.StatusCallbackUrl) : null,
                StatusCallbackEvent = new List<string> { "initiated", "ringing", "answered", "completed" }
            };

            // Make the call
            var call = await CallResource.CreateAsync(callOptions);

            var result = new VoiceCallResult
            {
                IsSuccess = true,
                CallId = call.Sid,
                ExternalId = call.Sid,
                Status = MapTwilioCallStatus(call.Status),
                InitiatedAt = DateTime.UtcNow,
                EstimatedCost = CalculateEstimatedCost(request),
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = call.Sid,
                    ["twilioStatus"] = call.Status.ToString(),
                    ["twilioDirection"] = call.Direction.ToString(),
                    ["twimlUrl"] = twimlUrl,
                    ["recordCall"] = request.RecordCall.ToString(),
                    ["language"] = request.Language.ToString()
                }
            };

            _logger.LogInformation("Voice call initiated successfully to {PhoneNumber}. Twilio SID: {TwilioSid}",
                request.PhoneNumber, call.Sid);

            return result;
        }
        catch (Twilio.Exceptions.ApiException ex)
        {
            _logger.LogError(ex, "Twilio API error making voice call to {PhoneNumber}: {ErrorCode} - {ErrorMessage}",
                request.PhoneNumber, ex.Code, ex.Message);

            return VoiceCallResult.Failure($"Twilio API error: {ex.Message}", ex.Code?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error making voice call to {PhoneNumber}", request.PhoneNumber);
            return VoiceCallResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<VoiceCallResult> GetCallStatusAsync(string callId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting call status for Twilio call {CallId}", callId);

            var call = await CallResource.FetchAsync(callId);

            var result = new VoiceCallResult
            {
                IsSuccess = true,
                CallId = callId,
                ExternalId = call.Sid,
                Status = MapTwilioCallStatus(call.Status),
                InitiatedAt = call.DateCreated ?? DateTime.UtcNow,
                AnsweredAt = call.Status == CallResource.StatusEnum.InProgress || call.Status == CallResource.StatusEnum.Completed 
                    ? call.StartTime : null,
                CompletedAt = call.Status == CallResource.StatusEnum.Completed ? call.EndTime : null,
                Duration = call.Duration != null ? TimeSpan.FromSeconds(call.Duration.Value) : null,
                Cost = call.Price?.ToString(),
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = call.Sid,
                    ["twilioStatus"] = call.Status.ToString(),
                    ["twilioDirection"] = call.Direction.ToString(),
                    ["twilioPrice"] = call.Price?.ToString() ?? "0",
                    ["twilioDuration"] = call.Duration?.ToString() ?? "0",
                    ["twilioAnsweredBy"] = call.AnsweredBy?.ToString() ?? ""
                }
            };

            return result;
        }
        catch (Twilio.Exceptions.ApiException ex)
        {
            _logger.LogError(ex, "Twilio API error getting call status for {CallId}: {ErrorCode} - {ErrorMessage}",
                callId, ex.Code, ex.Message);

            return VoiceCallResult.Failure($"Twilio API error: {ex.Message}", ex.Code?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting call status for {CallId}", callId);
            return VoiceCallResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<VoiceCallResult> HangupCallAsync(string callId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Hanging up Twilio call {CallId}", callId);

            var call = await CallResource.UpdateAsync(
                pathSid: callId,
                status: CallResource.UpdateStatusEnum.Completed);

            var result = new VoiceCallResult
            {
                IsSuccess = true,
                CallId = callId,
                ExternalId = call.Sid,
                Status = VoiceCallStatus.Completed,
                CompletedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["twilioSid"] = call.Sid,
                    ["twilioStatus"] = call.Status.ToString(),
                    ["hangupReason"] = "Manual hangup"
                }
            };

            _logger.LogInformation("Call {CallId} hung up successfully", callId);

            return result;
        }
        catch (Twilio.Exceptions.ApiException ex)
        {
            _logger.LogError(ex, "Twilio API error hanging up call {CallId}: {ErrorCode} - {ErrorMessage}",
                callId, ex.Code, ex.Message);

            return VoiceCallResult.Failure($"Twilio API error: {ex.Message}", ex.Code?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error hanging up call {CallId}", callId);
            return VoiceCallResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<bool> ValidatePhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            // Use Twilio Lookup API to validate phone number
            var result = await Twilio.Rest.Lookups.V1.PhoneNumberResource.FetchAsync(
                pathPhoneNumber: new PhoneNumber(phoneNumber));

            return result != null && !string.IsNullOrEmpty(result.PhoneNumber?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Phone number validation failed for {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    private async Task<string> CreateTwiMLAsync(VoiceCallRequest request, CancellationToken cancellationToken)
    {
        try
        {
            // Generate TwiML based on request
            var twiml = new Twilio.TwiML.VoiceResponse();

            // Add voice settings
            var voiceSettings = GetVoiceSettings(request.Language);
            
            // Say the message
            var say = new Twilio.TwiML.Say(request.Message)
            {
                Voice = voiceSettings.Voice,
                Language = voiceSettings.Language
            };

            twiml.Append(say);

            // Add pause if specified
            if (request.PauseDuration.HasValue && request.PauseDuration.Value > TimeSpan.Zero)
            {
                twiml.Pause(length: (int)request.PauseDuration.Value.TotalSeconds);
            }

            // Repeat if specified
            if (request.RepeatCount > 1)
            {
                for (int i = 1; i < request.RepeatCount; i++)
                {
                    twiml.Pause(length: 1);
                    twiml.Append(new Twilio.TwiML.Say(request.Message)
                    {
                        Voice = voiceSettings.Voice,
                        Language = voiceSettings.Language
                    });
                }
            }

            // Store TwiML and return URL
            var twimlContent = twiml.ToString();
            var twimlId = Guid.NewGuid().ToString();
            
            // In a real implementation, you would store this in a cache or database
            // and serve it via a webhook endpoint
            var twimlUrl = $"{_settings.TwiMLBaseUrl}/twiml/{twimlId}";
            
            // Store TwiML content for retrieval
            await StoreTwiMLAsync(twimlId, twimlContent, cancellationToken);

            return twimlUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating TwiML for voice call");
            throw;
        }
    }

    private async Task StoreTwiMLAsync(string twimlId, string twimlContent, CancellationToken cancellationToken)
    {
        // In a real implementation, store in cache/database
        // For now, just log it
        _logger.LogDebug("TwiML created for ID {TwiMLId}: {TwiMLContent}", twimlId, twimlContent);
    }

    private VoiceSettings GetVoiceSettings(Language language)
    {
        return language switch
        {
            Language.Hindi => new VoiceSettings 
            { 
                Voice = Twilio.TwiML.Say.VoiceEnum.Woman, 
                Language = "hi-IN" 
            },
            Language.Kannada => new VoiceSettings 
            { 
                Voice = Twilio.TwiML.Say.VoiceEnum.Woman, 
                Language = "kn-IN" 
            },
            _ => new VoiceSettings 
            { 
                Voice = Twilio.TwiML.Say.VoiceEnum.Woman, 
                Language = "en-US" 
            }
        };
    }

    private PhoneNumber FormatPhoneNumber(string phoneNumber)
    {
        // Ensure phone number is in E.164 format
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        
        if (!cleaned.StartsWith("+"))
        {
            // Add default country code if not present (assuming India +91)
            cleaned = "+91" + cleaned;
        }

        return new PhoneNumber(cleaned);
    }

    private VoiceCallStatus MapTwilioCallStatus(CallResource.StatusEnum twilioStatus)
    {
        return twilioStatus switch
        {
            CallResource.StatusEnum.Queued => VoiceCallStatus.Queued,
            CallResource.StatusEnum.Ringing => VoiceCallStatus.Ringing,
            CallResource.StatusEnum.InProgress => VoiceCallStatus.InProgress,
            CallResource.StatusEnum.Completed => VoiceCallStatus.Completed,
            CallResource.StatusEnum.Failed => VoiceCallStatus.Failed,
            CallResource.StatusEnum.Busy => VoiceCallStatus.Busy,
            CallResource.StatusEnum.NoAnswer => VoiceCallStatus.NoAnswer,
            CallResource.StatusEnum.Canceled => VoiceCallStatus.Cancelled,
            _ => VoiceCallStatus.Unknown
        };
    }

    private decimal CalculateEstimatedCost(VoiceCallRequest request)
    {
        // Estimate cost based on duration and rates
        var estimatedDurationMinutes = Math.Max(1, (request.Message?.Length ?? 0) / 150); // Rough estimate
        return estimatedDurationMinutes * _settings.CostPerMinute;
    }

    private VoiceValidationResult ValidateRequest(VoiceCallRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.PhoneNumber))
            errors.Add("Phone number is required");

        if (string.IsNullOrWhiteSpace(request.Message))
            errors.Add("Message content is required");

        if (request.Message?.Length > _settings.MaxMessageLength)
            errors.Add($"Message exceeds maximum length of {_settings.MaxMessageLength} characters");

        if (!IsValidPhoneNumberFormat(request.PhoneNumber))
            errors.Add("Invalid phone number format");

        if (request.RepeatCount < 1 || request.RepeatCount > _settings.MaxRepeatCount)
            errors.Add($"Repeat count must be between 1 and {_settings.MaxRepeatCount}");

        return new VoiceValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private bool IsValidPhoneNumberFormat(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Basic phone number validation
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        return cleaned.StartsWith("+") && cleaned.Length >= 10 && cleaned.Skip(1).All(char.IsDigit);
    }
}

/// <summary>
/// Twilio Voice configuration settings
/// </summary>
public class TwilioVoiceSettings
{
    public string AccountSid { get; set; } = string.Empty;
    public string AuthToken { get; set; } = string.Empty;
    public string FromPhoneNumber { get; set; } = string.Empty;
    public string StatusCallbackUrl { get; set; } = string.Empty;
    public string TwiMLBaseUrl { get; set; } = string.Empty;
    public int CallTimeoutSeconds { get; set; } = 30;
    public int MaxMessageLength { get; set; } = 4000;
    public int MaxRepeatCount { get; set; } = 3;
    public decimal CostPerMinute { get; set; } = 0.05m;
    public int RetryAttempts { get; set; } = 3;
}

/// <summary>
/// Voice settings for different languages
/// </summary>
public class VoiceSettings
{
    public Twilio.TwiML.Say.VoiceEnum Voice { get; set; }
    public string Language { get; set; } = string.Empty;
}

/// <summary>
/// Voice validation result
/// </summary>
public class VoiceValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}
