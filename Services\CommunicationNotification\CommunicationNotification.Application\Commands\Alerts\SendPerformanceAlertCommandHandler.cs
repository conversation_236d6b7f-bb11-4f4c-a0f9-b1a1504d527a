using CommunicationNotification.Application.Common;
using CommunicationNotification.Application.Services;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Application.Commands.Alerts;

/// <summary>
/// Handler for performance alert commands
/// </summary>
public class SendPerformanceAlertCommandHandler : 
    IRequestHandler<SendPerformanceAlertCommand, SendPerformanceAlertResult>,
    IRequestHandler<SendRatingChangeAlertCommand, SendPerformanceAlertResult>,
    IRequestHandler<SendPerformanceMilestoneAlertCommand, SendPerformanceAlertResult>,
    IRequestHandler<SendBulkPerformanceAlertsCommand, SendBulkPerformanceAlertsResult>,
    IRequestHandler<ConfigurePerformanceAlertThresholdsCommand, ConfigurePerformanceAlertThresholdsResult>
{
    private readonly INotificationOrchestrationService _notificationService;
    private readonly ITemplateService _templateService;
    private readonly IUserPreferenceRepository _userPreferenceRepository;
    private readonly ILogger<SendPerformanceAlertCommandHandler> _logger;

    public SendPerformanceAlertCommandHandler(
        INotificationOrchestrationService notificationService,
        ITemplateService templateService,
        IUserPreferenceRepository userPreferenceRepository,
        ILogger<SendPerformanceAlertCommandHandler> logger)
    {
        _notificationService = notificationService;
        _templateService = templateService;
        _userPreferenceRepository = userPreferenceRepository;
        _logger = logger;
    }

    public async Task<SendPerformanceAlertResult> Handle(SendPerformanceAlertCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing performance alert for carrier {CarrierId}, metric {PerformanceMetric}",
            request.CarrierId, request.PerformanceMetric);

        try
        {
            // Determine template based on alert type and metric
            var templateId = GetPerformanceAlertTemplateId(request.PerformanceMetric, request.AlertType);
            
            // Prepare template parameters
            var templateParameters = PreparePerformanceTemplateParameters(request);
            
            // Determine message type and priority
            var messageType = GetMessageTypeForPerformanceAlert(request.AlertType);
            var priority = GetPriorityForPerformanceAlert(request.AlertType, request.ChangePercentage);
            
            // Create notification request
            var notificationRequest = new NotificationRequest
            {
                UserId = request.CarrierId,
                MessageType = messageType,
                Priority = priority,
                TemplateId = templateId,
                Parameters = templateParameters,
                PreferredChannel = request.PreferredChannels.FirstOrDefault(),
                RequireDeliveryConfirmation = request.RequireAcknowledgment,
                Tags = request.Tags.Concat(new[] { "performance-alert", request.PerformanceMetric.ToLower(), request.AlertType.ToLower() }).ToList(),
                RelatedEntityId = request.CarrierId,
                RelatedEntityType = "CarrierPerformance",
                CorrelationId = request.CorrelationId,
                Metadata = new Dictionary<string, string>
                {
                    ["PerformanceMetric"] = request.PerformanceMetric,
                    ["AlertType"] = request.AlertType,
                    ["CurrentValue"] = request.CurrentValue.ToString(),
                    ["ChangePercentage"] = request.ChangePercentage.ToString(),
                    ["ChangeDirection"] = request.ChangeDirection,
                    ["TimePeriod"] = request.TimePeriod,
                    ["MeasurementDate"] = request.MeasurementDate.ToString("yyyy-MM-dd")
                }
            };

            // Send notification
            var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            // Create result
            var result = new SendPerformanceAlertResult
            {
                AlertId = sendResult.MessageId ?? Guid.NewGuid(),
                IsSuccess = sendResult.IsSuccess,
                ErrorMessage = sendResult.ErrorMessage,
                ChannelsUsed = sendResult.ChannelUsed != null ? new List<NotificationChannel> { sendResult.ChannelUsed.Value } : new List<NotificationChannel>(),
                SentAt = sendResult.SentAt ?? DateTime.UtcNow,
                ExternalId = sendResult.ExternalId,
                DeliveryMetadata = sendResult.Metadata ?? new Dictionary<string, object>(),
                RequiresAcknowledgment = request.RequireAcknowledgment,
                AcknowledgmentDeadline = request.RequireAcknowledgment ? DateTime.UtcNow.AddHours(24) : null
            };

            _logger.LogInformation("Performance alert sent successfully. AlertId: {AlertId}", result.AlertId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send performance alert for carrier {CarrierId}", request.CarrierId);
            return new SendPerformanceAlertResult
            {
                AlertId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    public async Task<SendPerformanceAlertResult> Handle(SendRatingChangeAlertCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing rating change alert for carrier {CarrierId}, rating change {RatingChange}",
            request.CarrierId, request.RatingChange);

        try
        {
            var templateId = GetRatingChangeTemplateId(request.ChangeType, request.RatingCategory);
            var templateParameters = PrepareRatingChangeTemplateParameters(request);
            var messageType = MessageType.RatingUpdate;
            var priority = GetPriorityForRatingChange(request.RatingChange);

            var notificationRequest = new NotificationRequest
            {
                UserId = request.CarrierId,
                MessageType = messageType,
                Priority = priority,
                TemplateId = templateId,
                Parameters = templateParameters,
                PreferredChannel = request.PreferredChannels.FirstOrDefault(),
                RequireDeliveryConfirmation = request.RequireAcknowledgment,
                Tags = request.Tags.Concat(new[] { "rating-change", request.RatingCategory.ToLower(), request.ChangeType.ToLower() }).ToList(),
                RelatedEntityId = request.CarrierId,
                RelatedEntityType = "CarrierRating",
                CorrelationId = request.CorrelationId,
                Metadata = new Dictionary<string, string>
                {
                    ["RatingCategory"] = request.RatingCategory,
                    ["CurrentRating"] = request.CurrentRating.ToString(),
                    ["PreviousRating"] = request.PreviousRating.ToString(),
                    ["RatingChange"] = request.RatingChange.ToString(),
                    ["ChangeType"] = request.ChangeType,
                    ["ReviewCount"] = request.ReviewCount.ToString(),
                    ["RatingDate"] = request.RatingDate.ToString("yyyy-MM-dd")
                }
            };

            var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            return new SendPerformanceAlertResult
            {
                AlertId = sendResult.MessageId ?? Guid.NewGuid(),
                IsSuccess = sendResult.IsSuccess,
                ErrorMessage = sendResult.ErrorMessage,
                ChannelsUsed = sendResult.ChannelUsed != null ? new List<NotificationChannel> { sendResult.ChannelUsed.Value } : new List<NotificationChannel>(),
                SentAt = sendResult.SentAt ?? DateTime.UtcNow,
                ExternalId = sendResult.ExternalId,
                DeliveryMetadata = sendResult.Metadata ?? new Dictionary<string, object>(),
                RequiresAcknowledgment = request.RequireAcknowledgment
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send rating change alert for carrier {CarrierId}", request.CarrierId);
            return new SendPerformanceAlertResult
            {
                AlertId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    public async Task<SendPerformanceAlertResult> Handle(SendPerformanceMilestoneAlertCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing milestone alert for carrier {CarrierId}, milestone {MilestoneName}",
            request.CarrierId, request.MilestoneName);

        try
        {
            var templateId = GetMilestoneTemplateId(request.MilestoneType, request.AchievementLevel);
            var templateParameters = PrepareMilestoneTemplateParameters(request);
            var messageType = MessageType.Achievement;
            var priority = Priority.High; // Milestones are always high priority

            var notificationRequest = new NotificationRequest
            {
                UserId = request.CarrierId,
                MessageType = messageType,
                Priority = priority,
                TemplateId = templateId,
                Parameters = templateParameters,
                PreferredChannel = request.PreferredChannels.FirstOrDefault(),
                RequireDeliveryConfirmation = request.RequireAcknowledgment,
                Tags = request.Tags.Concat(new[] { "milestone", request.MilestoneType.ToLower(), request.AchievementLevel.ToLower() }).ToList(),
                RelatedEntityId = request.CarrierId,
                RelatedEntityType = "CarrierMilestone",
                CorrelationId = request.CorrelationId,
                Metadata = new Dictionary<string, string>
                {
                    ["MilestoneType"] = request.MilestoneType,
                    ["MilestoneName"] = request.MilestoneName,
                    ["MilestoneValue"] = request.MilestoneValue.ToString(),
                    ["MilestoneUnit"] = request.MilestoneUnit,
                    ["AchievementLevel"] = request.AchievementLevel,
                    ["AchievedDate"] = request.AchievedDate.ToString("yyyy-MM-dd"),
                    ["NextMilestone"] = request.NextMilestone
                }
            };

            var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            return new SendPerformanceAlertResult
            {
                AlertId = sendResult.MessageId ?? Guid.NewGuid(),
                IsSuccess = sendResult.IsSuccess,
                ErrorMessage = sendResult.ErrorMessage,
                ChannelsUsed = sendResult.ChannelUsed != null ? new List<NotificationChannel> { sendResult.ChannelUsed.Value } : new List<NotificationChannel>(),
                SentAt = sendResult.SentAt ?? DateTime.UtcNow,
                ExternalId = sendResult.ExternalId,
                DeliveryMetadata = sendResult.Metadata ?? new Dictionary<string, object>(),
                RequiresAcknowledgment = request.RequireAcknowledgment
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send milestone alert for carrier {CarrierId}", request.CarrierId);
            return new SendPerformanceAlertResult
            {
                AlertId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    public async Task<SendBulkPerformanceAlertsResult> Handle(SendBulkPerformanceAlertsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing bulk performance alerts, {AlertCount} alerts", request.PerformanceAlerts.Count);

        var startTime = DateTime.UtcNow;
        var results = new List<SendPerformanceAlertResult>();
        var errors = new List<string>();

        try
        {
            foreach (var alertInfo in request.PerformanceAlerts)
            {
                try
                {
                    var individualCommand = new SendPerformanceAlertCommand
                    {
                        CarrierId = alertInfo.CarrierId,
                        PerformanceMetric = alertInfo.PerformanceMetric,
                        AlertType = alertInfo.AlertType,
                        CurrentValue = alertInfo.CurrentValue,
                        PreviousValue = alertInfo.PreviousValue,
                        ThresholdValue = alertInfo.ThresholdValue,
                        ChangeDirection = alertInfo.ChangeDirection,
                        ChangePercentage = alertInfo.ChangePercentage,
                        MeasurementDate = alertInfo.MeasurementDate,
                        PerformanceData = alertInfo.PerformanceData,
                        PreferredChannels = request.PreferredChannels,
                        Priority = request.Priority,
                        RequireAcknowledgment = request.RequireAcknowledgment,
                        Tags = request.Tags,
                        CorrelationId = request.CorrelationId
                    };

                    var individualResult = await Handle(individualCommand, cancellationToken);
                    results.Add(individualResult);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process performance alert for carrier {CarrierId}", alertInfo.CarrierId);
                    errors.Add($"Failed to process alert for carrier {alertInfo.CarrierId}: {ex.Message}");
                }
            }

            var endTime = DateTime.UtcNow;
            var successfulAlerts = results.Count(r => r.IsSuccess);
            var failedAlerts = results.Count(r => !r.IsSuccess);

            return new SendBulkPerformanceAlertsResult
            {
                AlertResults = results,
                TotalAlerts = results.Count,
                SuccessfulAlerts = successfulAlerts,
                FailedAlerts = failedAlerts,
                Errors = errors,
                ProcessedAt = endTime,
                ProcessingDuration = endTime - startTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process bulk performance alerts");
            return new SendBulkPerformanceAlertsResult
            {
                AlertResults = results,
                TotalAlerts = request.PerformanceAlerts.Count,
                SuccessfulAlerts = results.Count(r => r.IsSuccess),
                FailedAlerts = results.Count(r => !r.IsSuccess) + (request.PerformanceAlerts.Count - results.Count),
                Errors = errors.Concat(new[] { ex.Message }).ToList(),
                ProcessedAt = DateTime.UtcNow,
                ProcessingDuration = DateTime.UtcNow - startTime
            };
        }
    }

    public async Task<ConfigurePerformanceAlertThresholdsResult> Handle(ConfigurePerformanceAlertThresholdsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Configuring performance alert thresholds for carrier {CarrierId}", request.CarrierId);

        try
        {
            // In a real implementation, this would persist threshold configurations to database
            var configurationId = Guid.NewGuid();
            var configuredAt = DateTime.UtcNow;

            // Validate thresholds
            foreach (var threshold in request.Thresholds)
            {
                if (threshold.WarningThreshold <= 0 || threshold.CriticalThreshold <= 0)
                {
                    throw new ArgumentException($"Invalid threshold values for metric {threshold.MetricName}");
                }
            }

            var configurationMetadata = new Dictionary<string, object>
            {
                ["CarrierId"] = request.CarrierId,
                ["ThresholdCount"] = request.Thresholds.Count,
                ["IsActive"] = request.IsActive,
                ["PreferredChannels"] = request.PreferredChannels.Select(c => c.ToString()).ToList(),
                ["AlertSettings"] = request.AlertSettings
            };

            _logger.LogInformation("Performance alert thresholds configured successfully. ConfigurationId: {ConfigurationId}",
                configurationId);

            return new ConfigurePerformanceAlertThresholdsResult
            {
                ConfigurationId = configurationId,
                IsSuccess = true,
                ConfiguredAt = configuredAt,
                ThresholdsConfigured = request.Thresholds.Count,
                ConfigurationMetadata = configurationMetadata
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to configure performance alert thresholds for carrier {CarrierId}", request.CarrierId);
            return new ConfigurePerformanceAlertThresholdsResult
            {
                ConfigurationId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ConfiguredAt = DateTime.UtcNow,
                ThresholdsConfigured = 0
            };
        }
    }

    private Dictionary<string, object> PreparePerformanceTemplateParameters(SendPerformanceAlertCommand request)
    {
        return new Dictionary<string, object>
        {
            ["PerformanceMetric"] = request.PerformanceMetric,
            ["AlertType"] = request.AlertType,
            ["CurrentValue"] = request.CurrentValue,
            ["PreviousValue"] = request.PreviousValue ?? 0,
            ["TargetValue"] = request.TargetValue ?? 0,
            ["ThresholdValue"] = request.ThresholdValue ?? 0,
            ["ChangeDirection"] = request.ChangeDirection,
            ["ChangePercentage"] = request.ChangePercentage,
            ["TimePeriod"] = request.TimePeriod,
            ["MeasurementDate"] = request.MeasurementDate.ToString("dd/MM/yyyy"),
            ["PerformanceData"] = request.PerformanceData,
            ["ActionRequired"] = GetActionRequiredText(request.AlertType, request.ChangeDirection),
            ["UrgencyLevel"] = GetUrgencyLevel(request.AlertType, request.ChangePercentage)
        };
    }

    private Dictionary<string, object> PrepareRatingChangeTemplateParameters(SendRatingChangeAlertCommand request)
    {
        return new Dictionary<string, object>
        {
            ["RatingCategory"] = request.RatingCategory,
            ["CurrentRating"] = request.CurrentRating,
            ["PreviousRating"] = request.PreviousRating,
            ["RatingChange"] = request.RatingChange,
            ["ChangeType"] = request.ChangeType,
            ["ReviewCount"] = request.ReviewCount,
            ["RatingDate"] = request.RatingDate.ToString("dd/MM/yyyy"),
            ["RecentFeedback"] = request.RecentFeedback,
            ["RatingDetails"] = request.RatingDetails,
            ["ImprovementSuggestion"] = GetRatingImprovementSuggestion(request.ChangeType, request.RatingChange)
        };
    }

    private Dictionary<string, object> PrepareMilestoneTemplateParameters(SendPerformanceMilestoneAlertCommand request)
    {
        return new Dictionary<string, object>
        {
            ["MilestoneType"] = request.MilestoneType,
            ["MilestoneName"] = request.MilestoneName,
            ["MilestoneValue"] = request.MilestoneValue,
            ["MilestoneUnit"] = request.MilestoneUnit,
            ["AchievementLevel"] = request.AchievementLevel,
            ["AchievedDate"] = request.AchievedDate.ToString("dd/MM/yyyy"),
            ["MilestoneData"] = request.MilestoneData,
            ["Rewards"] = request.Rewards,
            ["NextMilestone"] = request.NextMilestone,
            ["NextMilestoneTarget"] = request.NextMilestoneTarget ?? 0,
            ["CelebrationMessage"] = GetCelebrationMessage(request.AchievementLevel),
            ["MotivationalMessage"] = GetMotivationalMessage(request.NextMilestone)
        };
    }

    private string GetPerformanceAlertTemplateId(string metric, string alertType)
    {
        return $"PerformanceAlert_{metric}_{alertType}";
    }

    private string GetRatingChangeTemplateId(string changeType, string category)
    {
        return $"RatingChange_{changeType}_{category}";
    }

    private string GetMilestoneTemplateId(string milestoneType, string achievementLevel)
    {
        return $"Milestone_{milestoneType}_{achievementLevel}";
    }

    private MessageType GetMessageTypeForPerformanceAlert(string alertType)
    {
        return alertType.ToLower() switch
        {
            "milestone" => MessageType.Achievement,
            "decline" => MessageType.PerformanceAlert,
            "improvement" => MessageType.PerformanceUpdate,
            _ => MessageType.PerformanceAlert
        };
    }

    private Priority GetPriorityForPerformanceAlert(string alertType, decimal changePercentage)
    {
        return alertType.ToLower() switch
        {
            "decline" when Math.Abs(changePercentage) > 20 => Priority.High,
            "decline" when Math.Abs(changePercentage) > 10 => Priority.Normal,
            "milestone" => Priority.High,
            "improvement" => Priority.Normal,
            _ => Priority.Low
        };
    }

    private Priority GetPriorityForRatingChange(decimal ratingChange)
    {
        return Math.Abs(ratingChange) switch
        {
            >= 0.5m => Priority.High,
            >= 0.2m => Priority.Normal,
            _ => Priority.Low
        };
    }

    private string GetActionRequiredText(string alertType, string changeDirection)
    {
        return alertType.ToLower() switch
        {
            "decline" => "Review performance and implement improvement strategies",
            "threshold" => "Monitor closely and take corrective action if needed",
            "improvement" => "Continue current practices to maintain positive trend",
            _ => "Monitor performance and maintain current standards"
        };
    }

    private string GetUrgencyLevel(string alertType, decimal changePercentage)
    {
        return alertType.ToLower() switch
        {
            "decline" when Math.Abs(changePercentage) > 20 => "HIGH",
            "decline" when Math.Abs(changePercentage) > 10 => "MEDIUM",
            "threshold" => "MEDIUM",
            _ => "LOW"
        };
    }

    private string GetRatingImprovementSuggestion(string changeType, decimal ratingChange)
    {
        return changeType.ToLower() switch
        {
            "decline" => "Focus on customer service quality and communication to improve ratings",
            "improvement" => "Keep up the excellent work! Continue providing quality service",
            _ => "Maintain consistent service quality to sustain your rating"
        };
    }

    private string GetCelebrationMessage(string achievementLevel)
    {
        return achievementLevel.ToLower() switch
        {
            "platinum" => "🏆 Outstanding achievement! You've reached the highest level!",
            "gold" => "🥇 Excellent work! You've achieved gold status!",
            "silver" => "🥈 Great job! You've earned silver recognition!",
            "bronze" => "🥉 Well done! You've achieved bronze level!",
            _ => "🎉 Congratulations on your achievement!"
        };
    }

    private string GetMotivationalMessage(string nextMilestone)
    {
        return string.IsNullOrEmpty(nextMilestone) 
            ? "Keep up the excellent work!" 
            : $"Your next goal: {nextMilestone}. You're doing great!";
    }
}
