using Shared.Domain.Common;

namespace TripManagement.Domain.Events;

/// <summary>
/// Domain event raised when a cancellation reason master is created
/// </summary>
public class CancellationReasonMasterCreatedEvent : DomainEvent
{
    public Guid CancellationReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }

    public CancellationReasonMasterCreatedEvent(Guid cancellationReasonMasterId, string code, string name, string category)
    {
        CancellationReasonMasterId = cancellationReasonMasterId;
        Code = code;
        Name = name;
        Category = category;
    }
}

/// <summary>
/// Domain event raised when a cancellation reason master is updated
/// </summary>
public class CancellationReasonMasterUpdatedEvent : DomainEvent
{
    public Guid CancellationReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }
    public string OldName { get; }
    public string OldCategory { get; }

    public CancellationReasonMasterUpdatedEvent(Guid cancellationReasonMasterId, string code, string name, string category, string oldName, string oldCategory)
    {
        CancellationReasonMasterId = cancellationReasonMasterId;
        Code = code;
        Name = name;
        Category = category;
        OldName = oldName;
        OldCategory = oldCategory;
    }
}

/// <summary>
/// Domain event raised when a cancellation reason master is activated
/// </summary>
public class CancellationReasonMasterActivatedEvent : DomainEvent
{
    public Guid CancellationReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public CancellationReasonMasterActivatedEvent(Guid cancellationReasonMasterId, string code, string name)
    {
        CancellationReasonMasterId = cancellationReasonMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a cancellation reason master is deactivated
/// </summary>
public class CancellationReasonMasterDeactivatedEvent : DomainEvent
{
    public Guid CancellationReasonMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public CancellationReasonMasterDeactivatedEvent(Guid cancellationReasonMasterId, string code, string name)
    {
        CancellationReasonMasterId = cancellationReasonMasterId;
        Code = code;
        Name = name;
    }
}


