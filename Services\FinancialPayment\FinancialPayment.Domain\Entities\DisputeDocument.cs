using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class DisputeDocument : BaseEntity
{
    public Guid DisputeId { get; private set; }
    public string FileName { get; private set; }
    public string FileUrl { get; private set; }
    public string Description { get; private set; }
    public Guid UploadedBy { get; private set; }
    public DateTime UploadedAt { get; private set; }

    // Navigation properties
    public PaymentDispute Dispute { get; private set; } = null!;

    private DisputeDocument() { }

    public DisputeDocument(
        Guid disputeId,
        string fileName,
        string fileUrl,
        string description,
        Guid uploadedBy)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty");

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("File URL cannot be empty");

        Id = Guid.NewGuid();
        DisputeId = disputeId;
        FileName = fileName;
        FileUrl = fileUrl;
        Description = description ?? string.Empty;
        UploadedBy = uploadedBy;
        UploadedAt = DateTime.UtcNow;
    }
}
