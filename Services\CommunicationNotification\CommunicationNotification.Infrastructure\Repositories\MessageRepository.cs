using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Message entity
/// </summary>
public class MessageRepository : IMessageRepository
{
    private readonly CommunicationDbContext _context;

    public MessageRepository(CommunicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Get message by ID
    /// </summary>
    public async Task<Message?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);
    }

    /// <summary>
    /// Get messages by conversation thread ID
    /// </summary>
    public async Task<IEnumerable<Message>> GetByConversationThreadIdAsync(
        Guid conversationThreadId, 
        int pageNumber = 1, 
        int pageSize = 50, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .Where(m => m.ConversationThreadId == conversationThreadId)
            .OrderBy(m => m.SentAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get messages between users
    /// </summary>
    public async Task<IEnumerable<Message>> GetBetweenUsersAsync(
        Guid senderId, 
        Guid receiverId, 
        int pageNumber = 1, 
        int pageSize = 50, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .Where(m => (m.SenderId == senderId && m.ReceiverId == receiverId) ||
                       (m.SenderId == receiverId && m.ReceiverId == senderId))
            .OrderByDescending(m => m.SentAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get user message history
    /// </summary>
    public async Task<IEnumerable<Message>> GetUserMessageHistoryAsync(
        Guid userId, 
        int pageNumber = 1, 
        int pageSize = 20, 
        MessageType? messageType = null, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Messages
            .Where(m => m.SenderId == userId || m.ReceiverId == userId);

        if (messageType.HasValue)
        {
            query = query.Where(m => m.MessageType == messageType.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(m => m.SentAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(m => m.SentAt <= toDate.Value);
        }

        return await query
            .OrderByDescending(m => m.SentAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get unread messages for user
    /// </summary>
    public async Task<IEnumerable<Message>> GetUnreadMessagesAsync(
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .Where(m => m.ReceiverId == userId && m.ReadAt == null)
            .OrderBy(m => m.SentAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get messages by status
    /// </summary>
    public async Task<IEnumerable<Message>> GetByStatusAsync(
        MessageStatus status, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .Where(m => m.Status == status)
            .OrderByDescending(m => m.SentAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Search messages
    /// </summary>
    public async Task<IEnumerable<Message>> SearchAsync(
        string searchTerm, 
        Guid? userId = null, 
        Guid? conversationThreadId = null, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Messages.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(m => EF.Functions.ILike(m.Content.Body, $"%{searchTerm}%") ||
                                    EF.Functions.ILike(m.Content.Subject ?? "", $"%{searchTerm}%"));
        }

        if (userId.HasValue)
        {
            query = query.Where(m => m.SenderId == userId.Value || m.ReceiverId == userId.Value);
        }

        if (conversationThreadId.HasValue)
        {
            query = query.Where(m => m.ConversationThreadId == conversationThreadId.Value);
        }

        return await query
            .OrderByDescending(m => m.SentAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get message statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync(
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        Guid? userId = null, 
        CancellationToken cancellationToken = default)
    {
        var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var to = toDate ?? DateTime.UtcNow;

        var query = _context.Messages
            .Where(m => m.SentAt >= from && m.SentAt <= to);

        if (userId.HasValue)
        {
            query = query.Where(m => m.SenderId == userId.Value || m.ReceiverId == userId.Value);
        }

        var stats = await query
            .GroupBy(m => 1)
            .Select(g => new
            {
                TotalMessages = g.Count(),
                ReadMessages = g.Count(m => m.ReadAt.HasValue),
                UnreadMessages = g.Count(m => m.ReadAt == null),
                AvgReadTimeSeconds = g.Where(m => m.ReadAt.HasValue)
                    .Average(m => EF.Functions.DateDiffSecond(m.SentAt, m.ReadAt!.Value))
            })
            .FirstOrDefaultAsync(cancellationToken);

        var typeStats = await query
            .GroupBy(m => m.MessageType)
            .Select(g => new
            {
                MessageType = g.Key.ToString(),
                Count = g.Count(),
                ReadRate = g.Count(m => m.ReadAt.HasValue) * 100.0 / g.Count()
            })
            .ToListAsync(cancellationToken);

        return new Dictionary<string, object>
        {
            ["total_messages"] = stats?.TotalMessages ?? 0,
            ["read_messages"] = stats?.ReadMessages ?? 0,
            ["unread_messages"] = stats?.UnreadMessages ?? 0,
            ["read_rate"] = stats?.TotalMessages > 0 ? 
                Math.Round((stats.ReadMessages * 100.0) / stats.TotalMessages, 2) : 0,
            ["avg_read_time_seconds"] = Math.Round(stats?.AvgReadTimeSeconds ?? 0, 2),
            ["message_type_statistics"] = typeStats.ToDictionary(ts => ts.MessageType, ts => new
            {
                count = ts.Count,
                read_rate = Math.Round(ts.ReadRate, 2)
            })
        };
    }

    /// <summary>
    /// Add message
    /// </summary>
    public async Task AddAsync(Message message, CancellationToken cancellationToken = default)
    {
        await _context.Messages.AddAsync(message, cancellationToken);
    }

    /// <summary>
    /// Add multiple messages
    /// </summary>
    public async Task AddRangeAsync(IEnumerable<Message> messages, CancellationToken cancellationToken = default)
    {
        await _context.Messages.AddRangeAsync(messages, cancellationToken);
    }

    /// <summary>
    /// Update message
    /// </summary>
    public void Update(Message message)
    {
        _context.Messages.Update(message);
    }

    /// <summary>
    /// Delete message
    /// </summary>
    public void Delete(Message message)
    {
        _context.Messages.Remove(message);
    }

    /// <summary>
    /// Mark messages as read
    /// </summary>
    public async Task MarkAsReadAsync(
        IEnumerable<Guid> messageIds, 
        DateTime? readAt = null, 
        CancellationToken cancellationToken = default)
    {
        var readTime = readAt ?? DateTime.UtcNow;
        
        await _context.Messages
            .Where(m => messageIds.Contains(m.Id) && m.ReadAt == null)
            .ExecuteUpdateAsync(m => m.SetProperty(x => x.ReadAt, readTime), cancellationToken);
    }

    /// <summary>
    /// Mark conversation messages as read
    /// </summary>
    public async Task MarkConversationAsReadAsync(
        Guid conversationThreadId, 
        Guid userId, 
        DateTime? readAt = null, 
        CancellationToken cancellationToken = default)
    {
        var readTime = readAt ?? DateTime.UtcNow;
        
        await _context.Messages
            .Where(m => m.ConversationThreadId == conversationThreadId && 
                       m.ReceiverId == userId && 
                       m.ReadAt == null)
            .ExecuteUpdateAsync(m => m.SetProperty(x => x.ReadAt, readTime), cancellationToken);
    }

    /// <summary>
    /// Count messages
    /// </summary>
    public async Task<int> CountAsync(
        Guid? userId = null, 
        Guid? conversationThreadId = null, 
        MessageStatus? status = null, 
        bool? isRead = null, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Messages.AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(m => m.SenderId == userId.Value || m.ReceiverId == userId.Value);
        }

        if (conversationThreadId.HasValue)
        {
            query = query.Where(m => m.ConversationThreadId == conversationThreadId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(m => m.Status == status.Value);
        }

        if (isRead.HasValue)
        {
            if (isRead.Value)
            {
                query = query.Where(m => m.ReadAt.HasValue);
            }
            else
            {
                query = query.Where(m => m.ReadAt == null);
            }
        }

        if (fromDate.HasValue)
        {
            query = query.Where(m => m.SentAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(m => m.SentAt <= toDate.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Get unread message count for user
    /// </summary>
    public async Task<int> GetUnreadCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .CountAsync(m => m.ReceiverId == userId && m.ReadAt == null, cancellationToken);
    }

    /// <summary>
    /// Check if message exists
    /// </summary>
    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Messages
            .AnyAsync(m => m.Id == id, cancellationToken);
    }

    /// <summary>
    /// Archive old messages
    /// </summary>
    public async Task<int> ArchiveOldMessagesAsync(
        DateTime olderThan, 
        int batchSize = 1000, 
        CancellationToken cancellationToken = default)
    {
        var messages = await _context.Messages
            .Where(m => m.CreatedAt < olderThan)
            .Take(batchSize)
            .ToListAsync(cancellationToken);

        if (!messages.Any())
        {
            return 0;
        }

        // Create archive records
        var archives = messages.Select(m => new MessageArchive
        {
            Id = Guid.NewGuid(),
            OriginalMessageId = m.Id,
            UserId = m.SenderId,
            ArchivedAt = DateTime.UtcNow,
            ArchivedBy = Guid.Empty, // System archival
            Reason = "Automatic archival due to age",
            MessageData = new Dictionary<string, object>
            {
                ["sender_id"] = m.SenderId,
                ["receiver_id"] = m.ReceiverId,
                ["message_type"] = m.MessageType.ToString(),
                ["content"] = new
                {
                    subject = m.Content.Subject,
                    body = m.Content.Body,
                    language = m.Content.Language.ToString()
                },
                ["sent_at"] = m.SentAt,
                ["delivered_at"] = m.DeliveredAt,
                ["read_at"] = m.ReadAt,
                ["metadata"] = m.Metadata
            },
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        }).ToList();

        await _context.MessageArchives.AddRangeAsync(archives, cancellationToken);
        _context.Messages.RemoveRange(messages);
        
        return messages.Count;
    }
}
