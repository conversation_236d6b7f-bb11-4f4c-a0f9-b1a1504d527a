using FluentValidation;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Validators;

public class CreateMilestoneTemplateCommandValidator : AbstractValidator<CreateMilestoneTemplateCommand>
{
    public CreateMilestoneTemplateCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Template name is required")
            .MaximumLength(200).WithMessage("Template name cannot exceed 200 characters")
            .Matches(@"^[a-zA-Z0-9\s\-_\.]+$").WithMessage("Template name contains invalid characters");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Template description is required")
            .MaximumLength(1000).WithMessage("Template description cannot exceed 1000 characters");

        RuleFor(x => x.Type)
            .NotEmpty().WithMessage("Template type is required")
            .MaximumLength(100).WithMessage("Template type cannot exceed 100 characters")
            .Must(BeValidTemplateType).WithMessage("Invalid template type");

        RuleFor(x => x.Category)
            .NotEmpty().WithMessage("Template category is required")
            .MaximumLength(100).WithMessage("Template category cannot exceed 100 characters")
            .Must(BeValidTemplateCategory).WithMessage("Invalid template category");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required")
            .MaximumLength(200).WithMessage("CreatedBy cannot exceed 200 characters");

        RuleFor(x => x.Steps)
            .NotNull().WithMessage("Steps collection cannot be null")
            .Must(HaveValidStepSequence).WithMessage("Step sequence numbers must be unique and sequential starting from 1")
            .Must(HaveValidPayoutPercentages).WithMessage("Total payout percentages must equal 100%");

        RuleForEach(x => x.Steps).SetValidator(new CreateMilestoneStepRequestValidator());
    }

    private bool BeValidTemplateType(string type)
    {
        var validTypes = new[] { "Trip", "Order", "Project", "Delivery", "Pickup", "Custom" };
        return validTypes.Contains(type, StringComparer.OrdinalIgnoreCase);
    }

    private bool BeValidTemplateCategory(string category)
    {
        var validCategories = new[] { "Logistics", "Transportation", "Delivery", "Pickup", "Documentation", "Payment", "Compliance", "Quality", "Custom" };
        return validCategories.Contains(category, StringComparer.OrdinalIgnoreCase);
    }

    private bool HaveValidStepSequence(IList<CreateMilestoneStepRequest> steps)
    {
        if (!steps.Any()) return true; // Empty is valid

        var sequences = steps.Select(s => s.SequenceNumber).ToList();
        var distinctSequences = sequences.Distinct().ToList();

        // Check for duplicates
        if (sequences.Count != distinctSequences.Count) return false;

        // Check if sequences start from 1 and are consecutive
        var sortedSequences = distinctSequences.OrderBy(s => s).ToList();
        for (int i = 0; i < sortedSequences.Count; i++)
        {
            if (sortedSequences[i] != i + 1) return false;
        }

        return true;
    }

    private bool HaveValidPayoutPercentages(IList<CreateMilestoneStepRequest> steps)
    {
        if (!steps.Any()) return true; // Empty is valid

        var totalPercentage = steps
            .SelectMany(s => s.PayoutRules)
            .Sum(r => r.PayoutPercentage);

        return Math.Abs(totalPercentage - 100.0m) < 0.01m; // Allow for small floating point differences
    }
}

public class UpdateMilestoneTemplateCommandValidator : AbstractValidator<UpdateMilestoneTemplateCommand>
{
    public UpdateMilestoneTemplateCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Template ID is required");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Template name is required")
            .MaximumLength(200).WithMessage("Template name cannot exceed 200 characters")
            .Matches(@"^[a-zA-Z0-9\s\-_\.]+$").WithMessage("Template name contains invalid characters");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Template description is required")
            .MaximumLength(1000).WithMessage("Template description cannot exceed 1000 characters");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required")
            .MaximumLength(200).WithMessage("UpdatedBy cannot exceed 200 characters");
    }
}

public class DeleteMilestoneTemplateCommandValidator : AbstractValidator<DeleteMilestoneTemplateCommand>
{
    public DeleteMilestoneTemplateCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Template ID is required");

        RuleFor(x => x.DeletedBy)
            .NotEmpty().WithMessage("DeletedBy is required")
            .MaximumLength(200).WithMessage("DeletedBy cannot exceed 200 characters");
    }
}

public class CreateMilestoneStepRequestValidator : AbstractValidator<CreateMilestoneStepRequest>
{
    public CreateMilestoneStepRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Step name is required")
            .MaximumLength(200).WithMessage("Step name cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Step description is required")
            .MaximumLength(1000).WithMessage("Step description cannot exceed 1000 characters");

        RuleFor(x => x.SequenceNumber)
            .GreaterThan(0).WithMessage("Sequence number must be greater than 0");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));

        RuleFor(x => x.PayoutRules)
            .NotNull().WithMessage("Payout rules collection cannot be null")
            .Must(HaveValidTotalPercentage).WithMessage("Total payout percentage for step cannot exceed 100%");

        RuleForEach(x => x.PayoutRules).SetValidator(new CreateMilestonePayoutRuleRequestValidator());
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;

        // Simple validation for key=value format
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }

    private bool HaveValidTotalPercentage(IList<CreateMilestonePayoutRuleRequest> rules)
    {
        var totalPercentage = rules.Sum(r => r.PayoutPercentage);
        return totalPercentage >= 0 && totalPercentage <= 100;
    }
}

public class CreateMilestoneStepCommandValidator : AbstractValidator<CreateMilestoneStepCommand>
{
    public CreateMilestoneStepCommandValidator()
    {
        RuleFor(x => x.MilestoneTemplateId)
            .NotEmpty().WithMessage("Template ID is required");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Step name is required")
            .MaximumLength(200).WithMessage("Step name cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Step description is required")
            .MaximumLength(1000).WithMessage("Step description cannot exceed 1000 characters");

        RuleFor(x => x.SequenceNumber)
            .GreaterThan(0).WithMessage("Sequence number must be greater than 0");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));

        RuleFor(x => x.PayoutRules)
            .NotNull().WithMessage("Payout rules collection cannot be null")
            .Must(HaveValidTotalPercentage).WithMessage("Total payout percentage for step cannot exceed 100%");

        RuleForEach(x => x.PayoutRules).SetValidator(new CreateMilestonePayoutRuleRequestValidator());
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }

    private bool HaveValidTotalPercentage(IList<CreateMilestonePayoutRuleRequest> rules)
    {
        var totalPercentage = rules.Sum(r => r.PayoutPercentage);
        return totalPercentage >= 0 && totalPercentage <= 100;
    }
}

public class UpdateMilestoneStepCommandValidator : AbstractValidator<UpdateMilestoneStepCommand>
{
    public UpdateMilestoneStepCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Step ID is required");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Step name is required")
            .MaximumLength(200).WithMessage("Step name cannot exceed 200 characters");

        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Step description is required")
            .MaximumLength(1000).WithMessage("Step description cannot exceed 1000 characters");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }
}

public class CreateMilestonePayoutRuleRequestValidator : AbstractValidator<CreateMilestonePayoutRuleRequest>
{
    public CreateMilestonePayoutRuleRequestValidator()
    {
        RuleFor(x => x.PayoutPercentage)
            .GreaterThanOrEqualTo(0).WithMessage("Payout percentage must be non-negative")
            .LessThanOrEqualTo(100).WithMessage("Payout percentage cannot exceed 100%");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }
}

public class CreateMilestonePayoutRuleCommandValidator : AbstractValidator<CreateMilestonePayoutRuleCommand>
{
    public CreateMilestonePayoutRuleCommandValidator()
    {
        RuleFor(x => x.MilestoneStepId)
            .NotEmpty().WithMessage("Step ID is required");

        RuleFor(x => x.PayoutPercentage)
            .GreaterThanOrEqualTo(0).WithMessage("Payout percentage must be non-negative")
            .LessThanOrEqualTo(100).WithMessage("Payout percentage cannot exceed 100%");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }
}

public class UpdateMilestonePayoutRuleCommandValidator : AbstractValidator<UpdateMilestonePayoutRuleCommand>
{
    public UpdateMilestonePayoutRuleCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Payout rule ID is required");

        RuleFor(x => x.PayoutPercentage)
            .GreaterThanOrEqualTo(0).WithMessage("Payout percentage must be non-negative")
            .LessThanOrEqualTo(100).WithMessage("Payout percentage cannot exceed 100%");

        RuleFor(x => x.TriggerCondition)
            .MaximumLength(500).WithMessage("Trigger condition cannot exceed 500 characters")
            .Must(BeValidTriggerCondition).WithMessage("Invalid trigger condition format")
            .When(x => !string.IsNullOrEmpty(x.TriggerCondition));

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }

    private bool BeValidTriggerCondition(string? condition)
    {
        if (string.IsNullOrEmpty(condition)) return true;
        return condition.Contains("=") && condition.Split('=').Length == 2;
    }
}

public class CreateRoleTemplateMappingCommandValidator : AbstractValidator<CreateRoleTemplateMappingCommand>
{
    public CreateRoleTemplateMappingCommandValidator()
    {
        RuleFor(x => x.RoleName)
            .NotEmpty().WithMessage("Role name is required")
            .MaximumLength(100).WithMessage("Role name cannot exceed 100 characters")
            .Matches(@"^[a-zA-Z0-9\s\-_]+$").WithMessage("Role name contains invalid characters");

        RuleFor(x => x.MilestoneTemplateId)
            .NotEmpty().WithMessage("Template ID is required");

        RuleFor(x => x.Priority)
            .GreaterThanOrEqualTo(0).WithMessage("Priority must be non-negative");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required")
            .MaximumLength(200).WithMessage("CreatedBy cannot exceed 200 characters");

        RuleFor(x => x.Conditions)
            .MaximumLength(2000).WithMessage("Conditions cannot exceed 2000 characters")
            .Must(BeValidJsonConditions).WithMessage("Conditions must be valid JSON format")
            .When(x => !string.IsNullOrEmpty(x.Conditions));
    }

    private bool BeValidJsonConditions(string? conditions)
    {
        if (string.IsNullOrEmpty(conditions)) return true;

        try
        {
            if (conditions.StartsWith("{") && conditions.EndsWith("}"))
            {
                System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(conditions);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }
}

public class UpdateRoleTemplateMappingCommandValidator : AbstractValidator<UpdateRoleTemplateMappingCommand>
{
    public UpdateRoleTemplateMappingCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("Mapping ID is required");

        RuleFor(x => x.Priority)
            .GreaterThanOrEqualTo(0).WithMessage("Priority must be non-negative");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required")
            .MaximumLength(200).WithMessage("UpdatedBy cannot exceed 200 characters");

        RuleFor(x => x.Conditions)
            .MaximumLength(2000).WithMessage("Conditions cannot exceed 2000 characters")
            .Must(BeValidJsonConditions).WithMessage("Conditions must be valid JSON format")
            .When(x => !string.IsNullOrEmpty(x.Conditions));
    }

    private bool BeValidJsonConditions(string? conditions)
    {
        if (string.IsNullOrEmpty(conditions)) return true;

        try
        {
            if (conditions.StartsWith("{") && conditions.EndsWith("}"))
            {
                System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(conditions);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }
}

public class ReorderMilestoneStepsCommandValidator : AbstractValidator<ReorderMilestoneStepsCommand>
{
    public ReorderMilestoneStepsCommandValidator()
    {
        RuleFor(x => x.TemplateId)
            .NotEmpty().WithMessage("Template ID is required");

        RuleFor(x => x.StepSequenceMap)
            .NotNull().WithMessage("Step sequence map cannot be null")
            .NotEmpty().WithMessage("Step sequence map cannot be empty")
            .Must(HaveValidSequenceNumbers).WithMessage("Sequence numbers must be unique and start from 1");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required")
            .MaximumLength(200).WithMessage("UpdatedBy cannot exceed 200 characters");
    }

    private bool HaveValidSequenceNumbers(Dictionary<Guid, int> stepSequenceMap)
    {
        var sequences = stepSequenceMap.Values.ToList();
        var distinctSequences = sequences.Distinct().ToList();

        // Check for duplicates
        if (sequences.Count != distinctSequences.Count) return false;

        // Check if sequences start from 1 and are consecutive
        var sortedSequences = distinctSequences.OrderBy(s => s).ToList();
        for (int i = 0; i < sortedSequences.Count; i++)
        {
            if (sortedSequences[i] != i + 1) return false;
        }

        return true;
    }
}

public class BulkMilestoneTemplateOperationCommandValidator : AbstractValidator<BulkMilestoneTemplateOperationCommand>
{
    public BulkMilestoneTemplateOperationCommandValidator()
    {
        RuleFor(x => x.TemplateIds)
            .NotNull().WithMessage("Template IDs cannot be null")
            .NotEmpty().WithMessage("At least one template ID is required")
            .Must(HaveUniqueIds).WithMessage("Template IDs must be unique");

        RuleFor(x => x.Operation)
            .NotEmpty().WithMessage("Operation is required")
            .Must(BeValidOperation).WithMessage("Invalid operation. Valid operations are: activate, deactivate, delete");

        RuleFor(x => x.PerformedBy)
            .NotEmpty().WithMessage("PerformedBy is required")
            .MaximumLength(200).WithMessage("PerformedBy cannot exceed 200 characters");
    }

    private bool HaveUniqueIds(List<Guid> templateIds)
    {
        return templateIds.Distinct().Count() == templateIds.Count;
    }

    private bool BeValidOperation(string operation)
    {
        var validOperations = new[] { "activate", "deactivate", "delete" };
        return validOperations.Contains(operation.ToLower());
    }
}
