using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

public class GetCarrierPerformanceSummaryQueryHandler : IRequestHandler<GetCarrierPerformanceSummaryQuery, CarrierPerformanceSummaryDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierPerformanceSummaryQueryHandler> _logger;

    public GetCarrierPerformanceSummaryQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMetricRepository metricRepository,
        IMapper mapper,
        ILogger<GetCarrierPerformanceSummaryQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _metricRepository = metricRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierPerformanceSummaryDto> Handle(GetCarrierPerformanceSummaryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting carrier performance summary for {CarrierId}", request.CarrierId);

        var fromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-30);
        var toDate = request.ToDate ?? DateTime.UtcNow;

        // Get carrier metrics for the specified period
        var allCurrentMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var carrierMetrics = allCurrentMetrics.Items
            .Where(m => m.UserId == request.CarrierId)
            .ToList();

        // Get previous period metrics for comparison
        var previousPeriodStart = fromDate.AddDays(-(toDate - fromDate).Days);
        var previousPeriodEnd = fromDate;
        var allPreviousMetrics = await _metricRepository.GetByDateRangeAsync(previousPeriodStart, previousPeriodEnd, 1, 1000, cancellationToken);
        var previousMetrics = allPreviousMetrics.Items
            .Where(m => m.UserId == request.CarrierId)
            .ToList();

        // Build KPI Summary
        var kpiSummary = BuildKPISummary(carrierMetrics, previousMetrics);

        // Build Earnings Visualization (if requested)
        var earningsVisualization = request.IncludeEarningsVisualization
            ? await BuildEarningsVisualization(request.CarrierId, fromDate, toDate, request.Period, cancellationToken)
            : new CarrierEarningsVisualizationDto();

        // Build Order Metrics (if requested)
        var orderMetrics = request.IncludeOrderMetrics
            ? await BuildOrderMetrics(request.CarrierId, fromDate, toDate, cancellationToken)
            : new CarrierOrderMetricsDto();

        // Build On-Time Delivery (if requested)
        var onTimeDelivery = request.IncludeOnTimeDelivery
            ? await BuildOnTimeDelivery(request.CarrierId, fromDate, toDate, cancellationToken)
            : new CarrierOnTimeDeliveryDto();

        // Build Trends (if requested)
        var trends = request.IncludeTrends
            ? await BuildPerformanceTrends(request.CarrierId, fromDate, toDate, request.Period, cancellationToken)
            : new CarrierPerformanceTrendsDto();

        // Build Comparisons (if requested)
        var comparisons = request.IncludeComparisons
            ? await BuildPerformanceComparisons(request.CarrierId, fromDate, toDate, cancellationToken)
            : new CarrierPerformanceComparisonsDto();

        // Generate recommendations
        var recommendations = GenerateRecommendations(kpiSummary, orderMetrics, onTimeDelivery);

        // Generate alerts
        var alerts = GeneratePerformanceAlerts(kpiSummary, orderMetrics, onTimeDelivery);

        return new CarrierPerformanceSummaryDto
        {
            CarrierId = request.CarrierId,
            CarrierName = "Carrier", // Would be fetched from carrier service
            GeneratedAt = DateTime.UtcNow,
            FromDate = fromDate,
            ToDate = toDate,
            Period = request.Period.ToString(),
            KPISummary = kpiSummary,
            EarningsVisualization = earningsVisualization,
            OrderMetrics = orderMetrics,
            OnTimeDelivery = onTimeDelivery,
            Trends = trends,
            Comparisons = comparisons,
            Recommendations = recommendations,
            Alerts = alerts
        };
    }

    private CarrierKPISummaryDto BuildKPISummary(
        List<Domain.Entities.Metric> currentMetrics,
        List<Domain.Entities.Metric> previousMetrics)
    {
        // Helper method to get metric value
        decimal GetMetricValue(List<Domain.Entities.Metric> metrics, string metricName, decimal defaultValue = 0)
        {
            return metrics.FirstOrDefault(m => m.Name == metricName)?.Value.Value ?? defaultValue;
        }

        // Helper method to calculate change and trend
        (decimal change, string trend) CalculateChange(decimal current, decimal previous)
        {
            if (previous == 0) return (0, "Stable");
            var change = ((current - previous) / previous) * 100;
            var trend = Math.Abs(change) < 1 ? "Stable" : change > 0 ? "Improving" : "Declining";
            return (change, trend);
        }

        // Current period values
        var currentPerformanceScore = GetMetricValue(currentMetrics, "PerformanceScore", 85.0m);
        var currentEarnings = GetMetricValue(currentMetrics, "TotalEarnings", 50000m);
        var currentCompletedOrders = (int)GetMetricValue(currentMetrics, "CompletedOrders", 45);
        var currentOnTimeRate = GetMetricValue(currentMetrics, "OnTimeDeliveryRate", 88.5m);
        var currentCustomerSatisfaction = GetMetricValue(currentMetrics, "CustomerSatisfactionScore", 4.2m);
        var currentVehicleUtilization = GetMetricValue(currentMetrics, "VehicleUtilizationRate", 75.0m);

        // Previous period values
        var previousPerformanceScore = GetMetricValue(previousMetrics, "PerformanceScore", 82.0m);
        var previousEarnings = GetMetricValue(previousMetrics, "TotalEarnings", 48000m);
        var previousCompletedOrders = (int)GetMetricValue(previousMetrics, "CompletedOrders", 42);
        var previousOnTimeRate = GetMetricValue(previousMetrics, "OnTimeDeliveryRate", 85.0m);
        var previousCustomerSatisfaction = GetMetricValue(previousMetrics, "CustomerSatisfactionScore", 4.0m);
        var previousVehicleUtilization = GetMetricValue(previousMetrics, "VehicleUtilizationRate", 72.0m);

        // Calculate changes and trends
        var (performanceChange, performanceTrend) = CalculateChange(currentPerformanceScore, previousPerformanceScore);
        var (earningsChange, earningsTrend) = CalculateChange(currentEarnings, previousEarnings);
        var (ordersChange, ordersTrend) = CalculateChange(currentCompletedOrders, previousCompletedOrders);
        var (onTimeChange, onTimeTrend) = CalculateChange(currentOnTimeRate, previousOnTimeRate);
        var (satisfactionChange, satisfactionTrend) = CalculateChange(currentCustomerSatisfaction, previousCustomerSatisfaction);
        var (utilizationChange, utilizationTrend) = CalculateChange(currentVehicleUtilization, previousVehicleUtilization);

        return new CarrierKPISummaryDto
        {
            OverallPerformanceScore = currentPerformanceScore,
            PerformanceScoreChange = performanceChange,
            PerformanceTrend = performanceTrend,

            TotalEarnings = currentEarnings,
            EarningsChange = earningsChange,
            EarningsTrend = earningsTrend,

            CompletedOrders = currentCompletedOrders,
            OrdersChange = (int)ordersChange,
            OrdersTrend = ordersTrend,

            OnTimeDeliveryRate = currentOnTimeRate,
            OnTimeDeliveryChange = onTimeChange,
            OnTimeDeliveryTrend = onTimeTrend,

            CustomerSatisfactionScore = currentCustomerSatisfaction,
            CustomerSatisfactionChange = satisfactionChange,
            CustomerSatisfactionTrend = satisfactionTrend,

            VehicleUtilizationRate = currentVehicleUtilization,
            VehicleUtilizationChange = utilizationChange,
            VehicleUtilizationTrend = utilizationTrend
        };
    }

    private async Task<CarrierEarningsVisualizationDto> BuildEarningsVisualization(
        Guid carrierId, DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Get financial metrics
        var allMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var financialMetrics = allMetrics.Items
            .Where(m => m.UserId == carrierId &&
                       m.Category == KPICategory.Financial)
            .ToList();

        var totalEarnings = financialMetrics.FirstOrDefault(m => m.Name == "TotalEarnings")?.Value.Value ?? 50000m;
        var netEarnings = totalEarnings * 0.85m; // Assuming 15% costs
        var grossEarnings = totalEarnings;

        // Generate time-based earnings (mock data for now)
        var dailyEarnings = GenerateDailyEarnings(fromDate, toDate, totalEarnings);
        var weeklyEarnings = GenerateWeeklyEarnings(fromDate, toDate, totalEarnings);
        var monthlyEarnings = GenerateMonthlyEarnings(fromDate, toDate, totalEarnings);

        return new CarrierEarningsVisualizationDto
        {
            TotalEarnings = totalEarnings,
            NetEarnings = netEarnings,
            GrossEarnings = grossEarnings,
            AverageEarningsPerTrip = totalEarnings / 45, // Assuming 45 trips
            AverageEarningsPerKm = totalEarnings / 5000, // Assuming 5000 km
            DailyEarnings = dailyEarnings,
            WeeklyEarnings = weeklyEarnings,
            MonthlyEarnings = monthlyEarnings,
            EarningsBreakdown = new List<EarningsBreakdownDto>
            {
                new() { Category = "Trip Revenue", Amount = totalEarnings * 0.8m, Percentage = 80 },
                new() { Category = "Bonus Payments", Amount = totalEarnings * 0.15m, Percentage = 15 },
                new() { Category = "Other Income", Amount = totalEarnings * 0.05m, Percentage = 5 }
            },
            TopEarningRoutes = new List<RouteEarningsDto>
            {
                new() { RouteId = "BLR-MUM", RouteName = "Bangalore to Mumbai", TotalEarnings = totalEarnings * 0.3m, TripCount = 12 },
                new() { RouteId = "DEL-BLR", RouteName = "Delhi to Bangalore", TotalEarnings = totalEarnings * 0.25m, TripCount = 10 }
            },
            Projection = new EarningsProjectionDto
            {
                ProjectedMonthlyEarnings = totalEarnings * 1.1m,
                ProjectedQuarterlyEarnings = totalEarnings * 3.2m,
                ProjectedYearlyEarnings = totalEarnings * 12.5m
            }
        };
    }

    private async Task<CarrierOrderMetricsDto> BuildOrderMetrics(
        Guid carrierId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // Get order-related metrics
        var allMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var orderMetrics = allMetrics.Items
            .Where(m => m.UserId == carrierId &&
                       m.Category == KPICategory.Operational)
            .ToList();

        var completedOrders = (int)(orderMetrics.FirstOrDefault(m => m.Name == "CompletedOrders")?.Value.Value ?? 45);
        var totalOrders = (int)(completedOrders / 0.9m); // Assuming 90% completion rate
        var cancelledOrders = (int)(totalOrders * 0.05m); // Assuming 5% cancellation rate
        var inProgressOrders = totalOrders - completedOrders - cancelledOrders;

        return new CarrierOrderMetricsDto
        {
            TotalOrders = totalOrders,
            CompletedOrders = completedOrders,
            PendingOrders = 0,
            CancelledOrders = cancelledOrders,
            InProgressOrders = inProgressOrders,
            OrderCompletionRate = (decimal)completedOrders / totalOrders * 100,
            OrderCancellationRate = (decimal)cancelledOrders / totalOrders * 100,
            AverageOrderValue = 1200m,
            TotalOrderValue = completedOrders * 1200m,
            OrderTrends = GenerateOrderTrends(fromDate, toDate, completedOrders),
            OrderTypeBreakdown = new List<OrderTypeBreakdownDto>
            {
                new() { OrderType = "Full Truck Load", Count = completedOrders * 60 / 100, Percentage = 60 },
                new() { OrderType = "Part Load", Count = completedOrders * 30 / 100, Percentage = 30 },
                new() { OrderType = "Express Delivery", Count = completedOrders * 10 / 100, Percentage = 10 }
            },
            TopCustomers = new List<CustomerOrderVolumeDto>
            {
                new() { CustomerId = Guid.NewGuid(), CustomerName = "ABC Logistics", OrderCount = 15, TotalValue = 18000m },
                new() { CustomerId = Guid.NewGuid(), CustomerName = "XYZ Transport", OrderCount = 12, TotalValue = 14400m }
            }
        };
    }

    private async Task<CarrierOnTimeDeliveryDto> BuildOnTimeDelivery(
        Guid carrierId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // Get delivery performance metrics
        var allMetrics = await _metricRepository.GetByDateRangeAsync(fromDate, toDate, 1, 1000, cancellationToken);
        var deliveryMetrics = allMetrics.Items
            .Where(m => m.UserId == carrierId &&
                       m.Category == KPICategory.Performance)
            .ToList();

        var onTimeRate = deliveryMetrics.FirstOrDefault(m => m.Name == "OnTimeDeliveryRate")?.Value.Value ?? 88.5m;
        var earlyRate = 5.0m; // Assuming 5% early deliveries
        var lateRate = 100 - onTimeRate - earlyRate;

        return new CarrierOnTimeDeliveryDto
        {
            OnTimeDeliveryRate = onTimeRate,
            EarlyDeliveryRate = earlyRate,
            LateDeliveryRate = lateRate,
            AverageDeliveryTime = 48.5m, // hours
            AverageDelay = TimeSpan.FromHours(2.5),
            RoutePerformance = new List<RouteOnTimePerformanceDto>
            {
                new() { RouteId = "BLR-MUM", RouteName = "Bangalore to Mumbai", OnTimeRate = 92.0m, TotalDeliveries = 12 },
                new() { RouteId = "DEL-BLR", RouteName = "Delhi to Bangalore", OnTimeRate = 85.0m, TotalDeliveries = 10 }
            },
            OnTimeTrends = GenerateOnTimeTrends(fromDate, toDate, onTimeRate),
            PerformanceFactors = new List<DeliveryPerformanceFactorDto>
            {
                new() { Factor = "Traffic Conditions", Impact = 15.0m, Description = "Traffic delays affecting 15% of deliveries" },
                new() { Factor = "Weather", Impact = 3.0m, Description = "Weather delays affecting 3% of deliveries" }
            }
        };
    }

    private async Task<CarrierPerformanceTrendsDto> BuildPerformanceTrends(
        Guid carrierId, DateTime fromDate, DateTime toDate, TimePeriod period, CancellationToken cancellationToken)
    {
        // Generate trend data based on the period
        return new CarrierPerformanceTrendsDto
        {
            PerformanceTrends = GeneratePerformanceTrends(fromDate, toDate),
            EarningsTrends = GenerateEarningsTrends(fromDate, toDate),
            OrderVolumeTrends = GenerateOrderVolumeTrends(fromDate, toDate),
            OnTimeDeliveryTrends = GenerateOnTimeTrends(fromDate, toDate, 88.5m),
            CustomerSatisfactionTrends = GenerateCustomerSatisfactionTrends(fromDate, toDate)
        };
    }

    private async Task<CarrierPerformanceComparisonsDto> BuildPerformanceComparisons(
        Guid carrierId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        return new CarrierPerformanceComparisonsDto
        {
            IndustryBenchmark = new IndustryBenchmarkDto
            {
                OnTimeDeliveryRate = 85.0m,
                CustomerSatisfactionScore = 4.0m,
                AverageEarningsPerTrip = 1000m,
                VehicleUtilizationRate = 70.0m
            },
            PeerComparisons = new List<PeerComparisonDto>
            {
                new() { MetricName = "On-Time Delivery", YourValue = 88.5m, PeerAverage = 85.0m, Ranking = 2, TotalPeers = 10 },
                new() { MetricName = "Customer Satisfaction", YourValue = 4.2m, PeerAverage = 4.0m, Ranking = 3, TotalPeers = 10 }
            },
            HistoricalComparison = new HistoricalComparisonDto
            {
                CurrentPeriodScore = 88.5m,
                PreviousPeriodScore = 85.0m,
                YearOverYearChange = 3.5m,
                BestPerformancePeriod = DateTime.UtcNow.AddMonths(-2)
            },
            MarketPosition = new MarketPositionDto
            {
                OverallRanking = 3,
                TotalCarriers = 50,
                MarketShare = 2.5m,
                CompetitiveAdvantages = new List<string> { "High on-time delivery rate", "Excellent customer service" }
            }
        };
    }

    private List<CarrierRecommendationDto> GenerateRecommendations(
        CarrierKPISummaryDto kpi, CarrierOrderMetricsDto orders, CarrierOnTimeDeliveryDto onTime)
    {
        var recommendations = new List<CarrierRecommendationDto>();

        if (onTime.OnTimeDeliveryRate < 90)
        {
            recommendations.Add(new CarrierRecommendationDto
            {
                Title = "Improve On-Time Delivery",
                Description = "Focus on route optimization and departure time planning to improve on-time delivery rate",
                Priority = "High",
                Category = "Performance",
                EstimatedImpact = "5-8% improvement in on-time delivery",
                ActionItems = new List<string> { "Implement route optimization software", "Review departure schedules", "Monitor traffic patterns" }
            });
        }

        if (kpi.VehicleUtilizationRate < 80)
        {
            recommendations.Add(new CarrierRecommendationDto
            {
                Title = "Increase Vehicle Utilization",
                Description = "Optimize vehicle scheduling to increase utilization rate and earnings",
                Priority = "Medium",
                Category = "Efficiency",
                EstimatedImpact = "10-15% increase in earnings",
                ActionItems = new List<string> { "Analyze idle time patterns", "Implement dynamic scheduling", "Consider load consolidation" }
            });
        }

        return recommendations;
    }

    private List<CarrierPerformanceAlertDto> GeneratePerformanceAlerts(
        CarrierKPISummaryDto kpi, CarrierOrderMetricsDto orders, CarrierOnTimeDeliveryDto onTime)
    {
        var alerts = new List<CarrierPerformanceAlertDto>();

        if (kpi.PerformanceTrend == "Declining")
        {
            alerts.Add(new CarrierPerformanceAlertDto
            {
                AlertType = "Performance Decline",
                Severity = "Medium",
                Message = "Overall performance score has declined by " + Math.Abs(kpi.PerformanceScoreChange).ToString("F1") + "%",
                CreatedAt = DateTime.UtcNow,
                RequiresAction = true
            });
        }

        if (onTime.OnTimeDeliveryRate < 85)
        {
            alerts.Add(new CarrierPerformanceAlertDto
            {
                AlertType = "Low On-Time Delivery",
                Severity = "High",
                Message = "On-time delivery rate is below industry standard (85%)",
                CreatedAt = DateTime.UtcNow,
                RequiresAction = true
            });
        }

        return alerts;
    }

    // Helper methods for generating mock trend data
    private List<TimeBasedEarningsDto> GenerateDailyEarnings(DateTime fromDate, DateTime toDate, decimal totalEarnings)
    {
        var days = (toDate - fromDate).Days;
        var dailyAverage = totalEarnings / days;
        var earnings = new List<TimeBasedEarningsDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±20% variance
            earnings.Add(new TimeBasedEarningsDto
            {
                Date = date,
                Amount = dailyAverage * (1 + variance),
                TripsCount = new Random().Next(1, 4)
            });
        }

        return earnings;
    }

    private List<TimeBasedEarningsDto> GenerateWeeklyEarnings(DateTime fromDate, DateTime toDate, decimal totalEarnings)
    {
        var weeks = (int)Math.Ceiling((toDate - fromDate).Days / 7.0);
        var weeklyAverage = totalEarnings / weeks;
        var earnings = new List<TimeBasedEarningsDto>();

        var weekStart = fromDate;
        for (int i = 0; i < weeks; i++)
        {
            var variance = (decimal)(new Random().NextDouble() * 0.3 - 0.15); // ±15% variance
            earnings.Add(new TimeBasedEarningsDto
            {
                Date = weekStart,
                Amount = weeklyAverage * (1 + variance),
                TripsCount = new Random().Next(5, 15)
            });
            weekStart = weekStart.AddDays(7);
        }

        return earnings;
    }

    private List<TimeBasedEarningsDto> GenerateMonthlyEarnings(DateTime fromDate, DateTime toDate, decimal totalEarnings)
    {
        var months = (int)Math.Ceiling((toDate - fromDate).Days / 30.0);
        var monthlyAverage = totalEarnings / months;
        var earnings = new List<TimeBasedEarningsDto>();

        var monthStart = fromDate;
        for (int i = 0; i < months; i++)
        {
            var variance = (decimal)(new Random().NextDouble() * 0.2 - 0.1); // ±10% variance
            earnings.Add(new TimeBasedEarningsDto
            {
                Date = monthStart,
                Amount = monthlyAverage * (1 + variance),
                TripsCount = new Random().Next(20, 50)
            });
            monthStart = monthStart.AddMonths(1);
        }

        return earnings;
    }

    private List<OrderTrendDto> GenerateOrderTrends(DateTime fromDate, DateTime toDate, int totalOrders)
    {
        var days = (toDate - fromDate).Days;
        var dailyAverage = (decimal)totalOrders / days;
        var trends = new List<OrderTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2);
            trends.Add(new OrderTrendDto
            {
                Date = date,
                OrderCount = (int)(dailyAverage * (1 + variance)),
                CompletedCount = (int)(dailyAverage * (1 + variance) * 0.9m)
            });
        }

        return trends;
    }

    private List<OnTimeDeliveryTrendDto> GenerateOnTimeTrends(DateTime fromDate, DateTime toDate, decimal baseRate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<OnTimeDeliveryTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 10 - 5); // ±5% variance
            trends.Add(new OnTimeDeliveryTrendDto
            {
                Date = date,
                OnTimeRate = Math.Max(0, Math.Min(100, baseRate + variance)),
                TotalDeliveries = new Random().Next(1, 5)
            });
        }

        return trends;
    }

    private List<PerformanceTrendDataDto> GeneratePerformanceTrends(DateTime fromDate, DateTime toDate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<PerformanceTrendDataDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 10 - 5);
            trends.Add(new PerformanceTrendDataDto
            {
                Date = date,
                PerformanceScore = Math.Max(0, Math.Min(100, 85 + variance))
            });
        }

        return trends;
    }

    private List<EarningsTrendDataDto> GenerateEarningsTrends(DateTime fromDate, DateTime toDate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<EarningsTrendDataDto>();
        var baseEarnings = 1500m;

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2);
            trends.Add(new EarningsTrendDataDto
            {
                Date = date,
                DailyEarnings = baseEarnings * (1 + variance)
            });
        }

        return trends;
    }

    private List<OrderVolumeTrendDto> GenerateOrderVolumeTrends(DateTime fromDate, DateTime toDate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<OrderVolumeTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            trends.Add(new OrderVolumeTrendDto
            {
                Date = date,
                OrderVolume = new Random().Next(1, 4)
            });
        }

        return trends;
    }

    private List<CustomerSatisfactionTrendDto> GenerateCustomerSatisfactionTrends(DateTime fromDate, DateTime toDate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<CustomerSatisfactionTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.6 - 0.3);
            trends.Add(new CustomerSatisfactionTrendDto
            {
                Date = date,
                SatisfactionScore = Math.Max(1, Math.Min(5, 4.2m + variance))
            });
        }

        return trends;
    }
}
