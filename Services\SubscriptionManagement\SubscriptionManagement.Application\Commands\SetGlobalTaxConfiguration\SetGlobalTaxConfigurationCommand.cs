using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.SetGlobalTaxConfiguration
{
    public class SetGlobalTaxConfigurationCommand : IRequest<Guid>
    {
        public TaxType TaxType { get; set; }
        public decimal Rate { get; set; }
        public bool IsIncluded { get; set; }
        public List<string> ApplicableRegions { get; set; } = new();
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public int Priority { get; set; }
        public string? Description { get; set; }
        public Guid CreatedByUserId { get; set; }
    }
}
