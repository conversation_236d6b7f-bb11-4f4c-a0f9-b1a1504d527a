# TLI Microservices - Two Server Deployment Summary

## Overview

This document provides a complete summary of the TLI microservices deployment across two Ubuntu 24 servers, including all necessary commands, configurations, and verification steps.

## Architecture Summary

```
┌─────────────────────────────────────────────────────────────────┐
│                        INTERNET                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ HTTPS/HTTP Traffic
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 SERVER 2 (Applications)                        │
│                    IP: **********                              │
│  • API Gateway (5000)                                          │
│  • 13 TLI Microservices (5001-5013)                           │
│  • Docker Runtime                                              │
│  • .NET 8 Applications                                         │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      │ Private Network (********/24)
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SERVER 1 (Infrastructure)                       │
│                    IP: **********                              │
│  • PostgreSQL 15 (5432)                                        │
│  • Redis 7 (6379)                                              │
│  • RabbitMQ 3.12 (5672, 15672)                                 │
│  • Prometheus (9090)                                           │
│  • <PERSON><PERSON> (3000)                                              │
└─────────────────────────────────────────────────────────────────┘
```

## Quick Deployment Checklist

### Prerequisites
- [ ] Two Ubuntu 24 servers with minimum 8GB RAM each
- [ ] Static IP addresses configured
- [ ] SSH access to both servers
- [ ] Domain name (optional but recommended)

### Server 1 (Infrastructure) Setup
```bash
# 1. Initial setup
sudo apt update && sudo apt upgrade -y
sudo hostnamectl set-hostname tli-infrastructure

# 2. Install Docker
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 3. Create TLI user and directories
sudo useradd -m -s /bin/bash tli
sudo usermod -aG docker tli
sudo mkdir -p /opt/tli/{data,logs,backups,config}
sudo chown -R tli:tli /opt/tli

# 4. Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow from ********** to any port 5432
sudo ufw allow from ********** to any port 6379
sudo ufw allow from ********** to any port 5672

# 5. Deploy infrastructure stack
sudo su - tli
# Follow Server 1 setup guide to create configurations
/opt/tli/manage-infrastructure.sh start

# 6. Verify infrastructure
/opt/tli/health-check.sh
```

### Server 2 (Applications) Setup
```bash
# 1. Initial setup
sudo apt update && sudo apt upgrade -y
sudo hostnamectl set-hostname tli-applications

# 2. Install Docker and .NET 8
# (Same Docker installation as Server 1)
wget https://packages.microsoft.com/config/ubuntu/24.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y dotnet-sdk-8.0

# 3. Create TLI user and directories
sudo useradd -m -s /bin/bash tli
sudo usermod -aG docker tli
sudo mkdir -p /opt/tli/{apps,logs,config,data}
sudo chown -R tli:tli /opt/tli

# 4. Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000

# 5. Clone repository and build services
sudo su - tli
cd /opt/tli/apps
git clone https://github.com/your-org/TLIMicroservices.git
/opt/tli/build-services.sh

# 6. Deploy applications
/opt/tli/manage-applications.sh start

# 7. Verify applications
/opt/tli/health-check.sh
```

## Service Inventory

### Infrastructure Services (Server 1)
| Service | Port | Purpose | Status |
|---------|------|---------|--------|
| PostgreSQL | 5432 | Primary database | ✓ |
| Redis | 6379 | Cache & sessions | ✓ |
| RabbitMQ | 5672 | Message broker | ✓ |
| RabbitMQ Management | 15672 | Admin interface | ✓ |
| Prometheus | 9090 | Metrics collection | ✓ |
| Grafana | 3000 | Monitoring dashboard | ✓ |

### Application Services (Server 2)
| Service | Port | Purpose | Status |
|---------|------|---------|--------|
| API Gateway | 5000 | Request routing | ✓ |
| Identity Service | 5001 | Authentication | ✓ |
| User Management | 5002 | User profiles | ✓ |
| Subscription Management | 5003 | Plans & billing | ✓ |
| Order Management | 5004 | RFQ & orders | ✓ |
| Trip Management | 5005 | Trip tracking | ✓ |
| Network & Fleet | 5006 | Vehicle management | ✓ |
| Financial & Payment | 5007 | Payments | ✓ |
| Communication | 5008 | Notifications | ✓ |
| Analytics & BI | 5009 | Reports | ✓ |
| Data & Storage | 5010 | File management | ✓ |
| Audit & Compliance | 5011 | Audit trails | ✓ |
| Monitoring | 5012 | Health checks | ✓ |
| Mobile Workflow | 5013 | Mobile backend | ✓ |

## Database Configuration

### Databases Created
```sql
-- All databases use tli_admin user
TLI_Identity
TLI_UserManagement
TLI_SubscriptionManagement
TLI_OrderManagement
TLI_TripManagement
TLI_NetworkFleetManagement
TLI_FinancialPayment
TLI_CommunicationNotification
TLI_AnalyticsBIService
TLI_DataStorage
TLI_AuditCompliance
TLI_MonitoringObservability
TLI_MobileWorkflow
```

### Connection Strings
```
Host=**********;Port=5432;Database=DATABASE_NAME;User Id=tli_admin;Password=*****************
```

## Security Configuration

### Passwords (Change in Production)
```bash
# PostgreSQL
POSTGRES_PASSWORD=TLI_Postgres_2024!@#
DB_USER_PASSWORD=*****************

# Redis
REDIS_PASSWORD=TLI_Redis_2024!@#

# RabbitMQ
RABBITMQ_ADMIN_PASSWORD=TLI_RabbitMQ_2024!@#
RABBITMQ_SERVICE_PASSWORD=TLI_Service_2024!@#

# Grafana
GRAFANA_ADMIN_PASSWORD=TLI_Grafana_2024!@#

# JWT
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long
```

### Firewall Rules
```bash
# Server 1 (Infrastructure)
sudo ufw allow ssh
sudo ufw allow from ********** to any port 5432  # PostgreSQL
sudo ufw allow from ********** to any port 6379  # Redis
sudo ufw allow from ********** to any port 5672  # RabbitMQ
sudo ufw allow from ********/24 to any port 9090 # Prometheus
sudo ufw allow from ********/24 to any port 3000 # Grafana
sudo ufw allow from ********/24 to any port 15672 # RabbitMQ Management

# Server 2 (Applications)
sudo ufw allow ssh
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 5000  # API Gateway
sudo ufw allow from ********/24 to any port 5001:5013 # Microservices
```

## Management Commands

### Infrastructure Management (Server 1)
```bash
# Start/stop infrastructure
/opt/tli/manage-infrastructure.sh start
/opt/tli/manage-infrastructure.sh stop
/opt/tli/manage-infrastructure.sh restart
/opt/tli/manage-infrastructure.sh status

# Health check
/opt/tli/health-check.sh

# Backup databases
/opt/tli/manage-infrastructure.sh backup
```

### Applications Management (Server 2)
```bash
# Start/stop applications
/opt/tli/manage-applications.sh start
/opt/tli/manage-applications.sh stop
/opt/tli/manage-applications.sh restart
/opt/tli/manage-applications.sh status

# View logs
/opt/tli/manage-applications.sh logs [service]

# Build and update
/opt/tli/manage-applications.sh build
/opt/tli/manage-applications.sh update

# Health check
/opt/tli/health-check.sh
```

## Testing and Validation

### Complete Test Suite
```bash
# Run all tests
/opt/tli/run-all-tests.sh

# Individual test categories
/opt/tli/test-infrastructure.sh
/opt/tli/test-network-connectivity.sh
/opt/tli/test-service-health.sh
/opt/tli/test-database-connections.sh
/opt/tli/test-performance.sh
/opt/tli/test-security.sh
```

### Health Check Endpoints
```bash
# Infrastructure health
curl http://**********:9090/-/healthy  # Prometheus
curl http://**********:3000/api/health # Grafana

# Application health
curl http://**********:5000/health     # API Gateway
curl http://**********:5001/health     # Identity Service
curl http://**********:5002/health     # User Management
# ... (all services 5001-5013)
```

## Monitoring and Observability

### Access URLs
```bash
# Grafana Dashboard
http://**********:3000
# Username: admin
# Password: TLI_Grafana_2024!@#

# Prometheus Metrics
http://**********:9090

# RabbitMQ Management
http://**********:15672
# Username: tli_admin
# Password: TLI_RabbitMQ_2024!@#

# API Gateway
http://**********:5000
```

### Log Locations
```bash
# Infrastructure logs
/opt/tli/logs/postgres/
/opt/tli/logs/redis/
/opt/tli/logs/rabbitmq/
/opt/tli/logs/prometheus/
/opt/tli/logs/grafana/

# Application logs
/opt/tli/logs/apigateway/
/opt/tli/logs/identity/
/opt/tli/logs/usermanagement/
# ... (all services)
```

## Backup and Recovery

### Automated Backups
```bash
# Database backups (daily at 2 AM)
0 2 * * * /opt/tli/config/postgres/backup-databases.sh

# Backup location
/opt/tli/backups/postgres/

# Restore example
gunzip -c /opt/tli/backups/postgres/TLI_Identity_20241201_020000.sql.gz | \
  docker exec -i tli-postgres psql -U postgres -d TLI_Identity
```

## Troubleshooting Quick Reference

### Common Issues
```bash
# Service not starting
docker logs tli-servicename
systemctl status docker

# Database connection issues
nc -z ********** 5432
docker exec tli-postgres pg_isready -U postgres

# Network connectivity
ping **********
/opt/tli/test-network-connectivity.sh

# Performance issues
docker stats
htop
/opt/tli/test-performance.sh

# Check all services
/opt/tli/health-check.sh
```

## Production Readiness

### Before Going Live
- [ ] Change all default passwords
- [ ] Configure proper SSL certificates
- [ ] Setup domain name and DNS
- [ ] Configure external service API keys
- [ ] Setup monitoring alerts
- [ ] Test backup and recovery procedures
- [ ] Perform load testing
- [ ] Security audit
- [ ] Documentation review

### External Services to Configure
```bash
# Required for full functionality
GOOGLE_MAPS_API_KEY=your-api-key
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret
SMS_PROVIDER_API_KEY=your-sms-api-key
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASSWORD=your-email-password
```

## Support and Maintenance

### Regular Maintenance
```bash
# Weekly maintenance (automated)
/opt/tli/weekly-maintenance.sh

# Monthly tasks
- Review logs for errors
- Update Docker images
- Check disk space usage
- Review security updates
- Performance analysis
```

### Getting Help
- **Documentation**: All guides in `/Deployment/TwoServer/`
- **Logs**: Check service logs for detailed error information
- **Health Checks**: Run comprehensive test suite
- **Support Bundle**: Use log collection script for support

## Next Steps

1. **Production Deployment**: Follow security hardening guide
2. **Load Balancer**: Setup nginx or HAProxy for high availability
3. **SSL Configuration**: Implement proper SSL certificates
4. **Monitoring**: Configure alerting and notifications
5. **Scaling**: Plan for horizontal scaling as needed

## Conclusion

This deployment provides a robust, scalable foundation for the TLI microservices platform. The two-server architecture separates concerns between infrastructure and applications while maintaining high performance and reliability.

For detailed implementation steps, refer to the individual guides:
- [Server 1 Infrastructure Setup](./01-Server1-Infrastructure-Setup.md)
- [Server 2 Applications Setup](./02-Server2-Applications-Setup.md)
- [Network Configuration](./03-Network-Configuration.md)
- [Testing Guide](./05-Testing-Guide.md)
- [Troubleshooting Guide](./07-Troubleshooting-Guide.md)
