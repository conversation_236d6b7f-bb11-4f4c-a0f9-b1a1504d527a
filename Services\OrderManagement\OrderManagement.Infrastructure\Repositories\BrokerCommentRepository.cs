using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class BrokerCommentRepository : IBrokerCommentRepository
{
    private readonly OrderManagementDbContext _context;

    public BrokerCommentRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<BrokerComment?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerComments
            .Include(bc => bc.ParentComment)
            .Include(bc => bc.Replies)
            .FirstOrDefaultAsync(bc => bc.Id == id, cancellationToken);
    }

    public async Task<List<BrokerComment>> GetByRfqIdAsync(Guid rfqId, bool visibleOnly = true, bool includeInternal = true, CancellationToken cancellationToken = default)
    {
        var query = _context.BrokerComments
            .Include(bc => bc.ParentComment)
            .Include(bc => bc.Replies.Where(r => !visibleOnly || r.IsVisible))
            .Where(bc => bc.RfqId == rfqId);

        if (visibleOnly)
        {
            query = query.Where(bc => bc.IsVisible);
        }

        if (!includeInternal)
        {
            query = query.Where(bc => !bc.IsInternal);
        }

        return await query
            .OrderByDescending(bc => bc.Priority)
            .ThenByDescending(bc => bc.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<BrokerComment>> GetByBrokerIdAsync(Guid brokerId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _context.BrokerComments
            .Include(bc => bc.RFQ)
            .Where(bc => bc.BrokerId == brokerId && bc.IsVisible);

        if (fromDate.HasValue)
        {
            query = query.Where(bc => bc.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(bc => bc.CreatedAt <= toDate.Value);
        }

        return await query
            .OrderByDescending(bc => bc.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<BrokerComment>> GetByCommentTypeAsync(BrokerCommentType commentType, bool visibleOnly = true, CancellationToken cancellationToken = default)
    {
        var query = _context.BrokerComments
            .Include(bc => bc.RFQ)
            .Where(bc => bc.CommentType == commentType);

        if (visibleOnly)
        {
            query = query.Where(bc => bc.IsVisible);
        }

        return await query
            .OrderByDescending(bc => bc.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<BrokerComment>> GetRepliesAsync(Guid parentCommentId, CancellationToken cancellationToken = default)
    {
        return await _context.BrokerComments
            .Where(bc => bc.ParentCommentId == parentCommentId && bc.IsVisible)
            .OrderBy(bc => bc.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(BrokerComment comment, CancellationToken cancellationToken = default)
    {
        await _context.BrokerComments.AddAsync(comment, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public void Update(BrokerComment comment)
    {
        _context.BrokerComments.Update(comment);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var comment = await GetByIdAsync(id, cancellationToken);
        if (comment != null)
        {
            _context.BrokerComments.Remove(comment);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}
