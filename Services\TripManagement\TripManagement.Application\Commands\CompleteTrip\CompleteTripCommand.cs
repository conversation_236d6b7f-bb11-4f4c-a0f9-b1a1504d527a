using MediatR;

namespace TripManagement.Application.Commands.CompleteTrip;

public record CompleteTripCommand : IRequest<bool>
{
    public Guid TripId { get; init; }
    public Guid DriverId { get; init; }
    public DateTime ActualEndTime { get; init; } = DateTime.UtcNow;
    public decimal? ActualDistanceKm { get; init; }
    public string? CompletionNotes { get; init; }
    public bool ProofOfDeliveryUploaded { get; init; }
}
