using Shared.Domain;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Events;

/// <summary>
/// Domain event raised when a vehicle type master is created
/// </summary>
public class VehicleTypeMasterCreatedEvent : DomainEvent
{
    public Guid VehicleTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }

    public VehicleTypeMasterCreatedEvent(Guid vehicleTypeMasterId, string code, string name, string category)
    {
        VehicleTypeMasterId = vehicleTypeMasterId;
        Code = code;
        Name = name;
        Category = category;
    }
}

/// <summary>
/// Domain event raised when a vehicle type master is updated
/// </summary>
public class VehicleTypeMasterUpdatedEvent : DomainEvent
{
    public Guid VehicleTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }
    public string Category { get; }
    public string OldName { get; }
    public string OldCategory { get; }

    public VehicleTypeMasterUpdatedEvent(Guid vehicleTypeMasterId, string code, string name, string category, string oldName, string oldCategory)
    {
        VehicleTypeMasterId = vehicleTypeMasterId;
        Code = code;
        Name = name;
        Category = category;
        OldName = oldName;
        OldCategory = oldCategory;
    }
}

/// <summary>
/// Domain event raised when a vehicle type master is activated
/// </summary>
public class VehicleTypeMasterActivatedEvent : DomainEvent
{
    public Guid VehicleTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public VehicleTypeMasterActivatedEvent(Guid vehicleTypeMasterId, string code, string name)
    {
        VehicleTypeMasterId = vehicleTypeMasterId;
        Code = code;
        Name = name;
    }
}

/// <summary>
/// Domain event raised when a vehicle type master is deactivated
/// </summary>
public class VehicleTypeMasterDeactivatedEvent : DomainEvent
{
    public Guid VehicleTypeMasterId { get; }
    public string Code { get; }
    public string Name { get; }

    public VehicleTypeMasterDeactivatedEvent(Guid vehicleTypeMasterId, string code, string name)
    {
        VehicleTypeMasterId = vehicleTypeMasterId;
        Code = code;
        Name = name;
    }
}

