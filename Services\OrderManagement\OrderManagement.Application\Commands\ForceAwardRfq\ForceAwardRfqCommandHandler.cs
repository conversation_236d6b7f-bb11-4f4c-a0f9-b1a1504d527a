using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.ForceAwardRfq;

public class ForceAwardRfqCommandHandler : IRequestHandler<ForceAwardRfqCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqBidRepository _bidRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IAdministrativeAuditRepository _auditRepository;
    private readonly IAuthorizationService _authorizationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ForceAwardRfqCommandHandler> _logger;

    public ForceAwardRfqCommandHandler(
        IRfqRepository rfqRepository,
        IRfqBidRepository bidRepository,
        IRfqTimelineRepository timelineRepository,
        IAdministrativeAuditRepository auditRepository,
        IAuthorizationService authorizationService,
        IUnitOfWork unitOfWork,
        ILogger<ForceAwardRfqCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _bidRepository = bidRepository;
        _timelineRepository = timelineRepository;
        _auditRepository = auditRepository;
        _authorizationService = authorizationService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(ForceAwardRfqCommand request, CancellationToken cancellationToken)
    {
        _logger.LogWarning("ADMINISTRATIVE OVERRIDE: Force awarding RFQ {RfqId} to bid {BidId} by admin {AdminUserId} ({AdminUserName})",
            request.RfqId, request.BidId, request.AdminUserId, request.AdminUserName);

        try
        {
            // Verify administrative authorization
            var isAuthorized = await _authorizationService.HasAdministrativeOverridePermissionAsync(
                request.AdminUserId, "ForceAwardRfq", cancellationToken);

            if (!isAuthorized)
            {
                _logger.LogError("SECURITY VIOLATION: User {AdminUserId} ({AdminUserName}) attempted unauthorized force award of RFQ {RfqId}",
                    request.AdminUserId, request.AdminUserName, request.RfqId);

                await LogSecurityViolation(request, "Unauthorized force award attempt", cancellationToken);
                throw new UnauthorizedAccessException("Insufficient permissions for administrative override");
            }

            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found for force award", request.RfqId);
                return false;
            }

            // Get the bid
            var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
            if (bid == null || bid.RfqId != request.RfqId)
            {
                _logger.LogWarning("Bid {BidId} not found or does not belong to RFQ {RfqId}", request.BidId, request.RfqId);
                return false;
            }

            // Validate business rules (unless override is specified)
            if (!request.OverrideValidation)
            {
                var validationResult = ValidateForceAward(rfq, bid);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Force award validation failed for RFQ {RfqId}: {Errors}",
                        request.RfqId, string.Join(", ", validationResult.Errors));
                    return false;
                }
            }

            // Store original state for audit
            var originalStatus = rfq.Status;
            var originalAwardedBidId = rfq.AwardedBidId;

            // Force award the bid
            rfq.ForceAward(bid, request.AdminUserId, request.ForceAwardReason);

            // Create comprehensive audit log
            var auditLog = new AdministrativeAuditLog(
                request.AdminUserId,
                request.AdminUserName,
                request.AdminRole,
                AdministrativeAction.ForceAwardRfq,
                request.RfqId,
                "RFQ",
                request.ForceAwardReason,
                request.AdditionalNotes,
                new Dictionary<string, object>
                {
                    ["rfqId"] = request.RfqId,
                    ["bidId"] = request.BidId,
                    ["originalStatus"] = originalStatus.ToString(),
                    ["newStatus"] = RfqStatus.Awarded.ToString(),
                    ["originalAwardedBidId"] = originalAwardedBidId?.ToString() ?? "None",
                    ["newAwardedBidId"] = request.BidId.ToString(),
                    ["overrideValidation"] = request.OverrideValidation,
                    ["bidAmount"] = bid.QuotedPrice.Amount,
                    ["bidCurrency"] = bid.QuotedPrice.Currency,
                    ["bidderId"] = bid.BidderId,
                    ["bidderName"] = bid.BidderName,
                    ["forceAwardTimestamp"] = DateTime.UtcNow
                }.Concat(request.AuditMetadata ?? new Dictionary<string, object>())
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value));

            await _auditRepository.AddAsync(auditLog, cancellationToken);

            // Create timeline event
            var timelineEvent = RfqTimeline.CreateEvent(
                request.RfqId,
                RfqTimelineEventType.ForceAwarded,
                $"RFQ force awarded to '{bid.BidderName}' by administrator '{request.AdminUserName}'. Reason: {request.ForceAwardReason}",
                request.AdminUserId,
                request.AdminUserName,
                "Administrator",
                System.Text.Json.JsonSerializer.Serialize(new
                {
                    bidId = request.BidId,
                    bidAmount = bid.QuotedPrice.Amount,
                    bidCurrency = bid.QuotedPrice.Currency,
                    bidderId = bid.BidderId,
                    bidderName = bid.BidderName,
                    forceAwardReason = request.ForceAwardReason,
                    overrideValidation = request.OverrideValidation,
                    auditLogId = auditLog.Id
                }),
                originalStatus,
                RfqStatus.Awarded);

            await _timelineRepository.AddAsync(timelineEvent, cancellationToken);

            // Add routing failure tag if this was due to routing issues
            if (request.ForceAwardReason.ToLower().Contains("routing") ||
                request.ForceAwardReason.ToLower().Contains("failure"))
            {
                var routingFailureTag = RfqTag.CreateRoutingFailureTag(
                    request.RfqId,
                    "Force awarded due to routing issues",
                    new Dictionary<string, object>
                    {
                        ["forceAwardReason"] = request.ForceAwardReason,
                        ["adminOverride"] = true,
                        ["adminUserId"] = request.AdminUserId,
                        ["adminUserName"] = request.AdminUserName
                    });

                rfq.AddTag(routingFailureTag);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogWarning("ADMINISTRATIVE OVERRIDE COMPLETED: RFQ {RfqId} force awarded to bid {BidId} by admin {AdminUserId}",
                request.RfqId, request.BidId, request.AdminUserId);

            // TODO: Send notifications if requested
            if (request.NotifyStakeholders)
            {
                // Implement notification logic here
                _logger.LogInformation("Stakeholder notifications would be sent for force award of RFQ {RfqId}", request.RfqId);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during force award of RFQ {RfqId} by admin {AdminUserId}",
                request.RfqId, request.AdminUserId);

            // Log the failed attempt
            await LogFailedAttempt(request, ex.Message, cancellationToken);
            throw;
        }
    }

    private static ValidationResult ValidateForceAward(RFQ rfq, RfqBid bid)
    {
        var errors = new List<string>();

        // Check if RFQ is in a state that can be awarded
        if (rfq.Status == RfqStatus.Cancelled)
            errors.Add("Cannot force award a cancelled RFQ");

        if (rfq.Status == RfqStatus.Awarded && rfq.AwardedBidId == bid.Id)
            errors.Add("RFQ is already awarded to this bid");

        // Check if bid is valid
        if (bid.Status != BidStatus.Submitted)
            errors.Add($"Bid is in {bid.Status} status and cannot be awarded");

        // Check if RFQ has expired significantly
        if (rfq.ExpiresAt.HasValue && rfq.ExpiresAt.Value.AddDays(30) < DateTime.UtcNow)
            errors.Add("RFQ has been expired for more than 30 days");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private async Task LogSecurityViolation(ForceAwardRfqCommand request, string violation, CancellationToken cancellationToken)
    {
        var securityLog = new AdministrativeAuditLog(
            request.AdminUserId,
            request.AdminUserName,
            request.AdminRole,
            AdministrativeAction.SecurityViolation,
            request.RfqId,
            "Security",
            violation,
            $"Attempted force award of RFQ {request.RfqId} to bid {request.BidId}",
            new Dictionary<string, object>
            {
                ["attemptedAction"] = "ForceAwardRfq",
                ["rfqId"] = request.RfqId,
                ["bidId"] = request.BidId,
                ["violationType"] = "UnauthorizedAccess",
                ["timestamp"] = DateTime.UtcNow
            });

        await _auditRepository.AddAsync(securityLog, cancellationToken);
    }

    private async Task LogFailedAttempt(ForceAwardRfqCommand request, string error, CancellationToken cancellationToken)
    {
        var failureLog = new AdministrativeAuditLog(
            request.AdminUserId,
            request.AdminUserName,
            request.AdminRole,
            AdministrativeAction.ForceAwardRfq,
            request.RfqId,
            "RFQ",
            "Failed force award attempt",
            error,
            new Dictionary<string, object>
            {
                ["rfqId"] = request.RfqId,
                ["bidId"] = request.BidId,
                ["errorMessage"] = error,
                ["success"] = false,
                ["timestamp"] = DateTime.UtcNow
            });

        await _auditRepository.AddAsync(failureLog, cancellationToken);
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
