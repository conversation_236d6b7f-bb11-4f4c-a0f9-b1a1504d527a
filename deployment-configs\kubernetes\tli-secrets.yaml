apiVersion: v1
kind: Secret
metadata:
  name: tli-database-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  postgres-user: dGxpX2FkbWlu  # tli_admin
  postgres-password: eW91cl9zdXBlcl9zZWN1cmVfcGFzc3dvcmQ=  # your_super_secure_password
  postgres-database: dGxpX21pY3Jvc2VydmljZXM=  # tli_microservices
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-jwt-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  jwt-secret: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS10aGF0LWlzLWF0LWxlYXN0LTMyLWNoYXJhY3RlcnMtbG9uZw==
  jwt-issuer: VExJLklkZW50aXR5  # TLI.Identity
  jwt-audience: VExJLlNlcnZpY2Vz  # TLI.Services
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-redis-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  redis-password: eW91cl9zZWN1cmVfcmVkaXNfcGFzc3dvcmQ=  # your_secure_redis_password
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-rabbitmq-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  rabbitmq-user: dGxpX2FkbWlu  # tli_admin
  rabbitmq-password: eW91cl9zZWN1cmVfcmFiYml0bXFfcGFzc3dvcmQ=  # your_secure_rabbitmq_password
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-email-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  smtp-host: c210cC5nbWFpbC5jb20=  # smtp.gmail.com
  smtp-port: NTg3  # 587
  smtp-username: ****************************  # <EMAIL>
  smtp-password: eW91cl9hcHBfcGFzc3dvcmQ=  # your_app_password
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-sms-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  twilio-account-sid: eW91cl90d2lsaW9fYWNjb3VudF9zaWQ=  # your_twilio_account_sid
  twilio-auth-token: eW91cl90d2lsaW9fYXV0aF90b2tlbg==  # your_twilio_auth_token
  twilio-from-number: KzEyMzQ1Njc4OTA=  # +**********
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-payment-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  razorpay-key-id: eW91cl9yYXpvcnBheV9rZXlfaWQ=  # your_razorpay_key_id
  razorpay-key-secret: eW91cl9yYXpvcnBheV9rZXlfc2VjcmV0  # your_razorpay_key_secret
  stripe-publishable-key: cGtfbGl2ZV95b3VyX3N0cmlwZV9wdWJsaXNoYWJsZV9rZXk=  # pk_live_your_stripe_publishable_key
  stripe-secret-key: c2tfbGl2ZV95b3VyX3N0cmlwZV9zZWNyZXRfa2V5  # sk_live_your_stripe_secret_key
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-maps-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  google-maps-api-key: eW91cl9nb29nbGVfbWFwc19hcGlfa2V5  # your_google_maps_api_key
  mapbox-access-token: ********************************  # your_mapbox_access_token
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-storage-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  aws-access-key-id: eW91cl9hd3NfYWNjZXNzX2tleV9pZA==  # your_aws_access_key_id
  aws-secret-access-key: eW91cl9hd3Nfc2VjcmV0X2FjY2Vzc19rZXk=  # your_aws_secret_access_key
  aws-region: YXAtc291dGgtMQ==  # ap-south-1
  aws-s3-bucket: dGxpLXByb2R1Y3Rpb24tc3RvcmFnZQ==  # tli-production-storage
---
apiVersion: v1
kind: Secret
metadata:
  name: tli-monitoring-secrets
  namespace: tli-production
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded values
  application-insights-key: eW91cl9hcHBsaWNhdGlvbl9pbnNpZ2h0c19rZXk=  # your_application_insights_key
  new-relic-license-key: eW91cl9uZXdfcmVsaWNfbGljZW5zZV9rZXk=  # your_new_relic_license_key
  datadog-api-key: eW91cl9kYXRhZG9nX2FwaV9rZXk=  # your_datadog_api_key
---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: tli-tls-secret
  namespace: tli-production
type: kubernetes.io/tls
data:
  # Replace with your actual certificate and key (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...  # Your SSL certificate
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...  # Your SSL private key
