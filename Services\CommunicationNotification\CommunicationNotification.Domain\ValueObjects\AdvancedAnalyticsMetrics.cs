using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Advanced performance metrics value object
/// </summary>
public class PerformanceMetrics : ValueObject
{
    public int TotalMessages { get; private set; }
    public int DeliveredMessages { get; private set; }
    public int OpenedMessages { get; private set; }
    public int ClickedMessages { get; private set; }
    public int ConvertedMessages { get; private set; }
    public int BouncedMessages { get; private set; }
    public int UnsubscribedMessages { get; private set; }
    public decimal DeliveryRate { get; private set; }
    public decimal OpenRate { get; private set; }
    public decimal ClickThroughRate { get; private set; }
    public decimal ConversionRate { get; private set; }
    public decimal BounceRate { get; private set; }
    public decimal UnsubscribeRate { get; private set; }
    public decimal EngagementScore { get; private set; }
    public TimeSpan AverageDeliveryTime { get; private set; }
    public TimeSpan AverageResponseTime { get; private set; }
    public decimal TotalCost { get; private set; }
    public decimal CostPerMessage { get; private set; }
    public decimal CostPerConversion { get; private set; }
    public Dictionary<string, decimal> ChannelBreakdown { get; private set; }
    public Dictionary<string, decimal> MessageTypeBreakdown { get; private set; }

    private PerformanceMetrics()
    {
        ChannelBreakdown = new Dictionary<string, decimal>();
        MessageTypeBreakdown = new Dictionary<string, decimal>();
    }

    public PerformanceMetrics(
        int totalMessages,
        int deliveredMessages,
        int openedMessages,
        int clickedMessages,
        int convertedMessages,
        int bouncedMessages,
        int unsubscribedMessages,
        TimeSpan averageDeliveryTime,
        TimeSpan averageResponseTime,
        decimal totalCost,
        Dictionary<string, decimal>? channelBreakdown = null,
        Dictionary<string, decimal>? messageTypeBreakdown = null)
    {
        TotalMessages = totalMessages;
        DeliveredMessages = deliveredMessages;
        OpenedMessages = openedMessages;
        ClickedMessages = clickedMessages;
        ConvertedMessages = convertedMessages;
        BouncedMessages = bouncedMessages;
        UnsubscribedMessages = unsubscribedMessages;
        AverageDeliveryTime = averageDeliveryTime;
        AverageResponseTime = averageResponseTime;
        TotalCost = totalCost;
        ChannelBreakdown = channelBreakdown ?? new Dictionary<string, decimal>();
        MessageTypeBreakdown = messageTypeBreakdown ?? new Dictionary<string, decimal>();

        // Calculate rates
        DeliveryRate = totalMessages > 0 ? (decimal)deliveredMessages / totalMessages * 100 : 0;
        OpenRate = deliveredMessages > 0 ? (decimal)openedMessages / deliveredMessages * 100 : 0;
        ClickThroughRate = openedMessages > 0 ? (decimal)clickedMessages / openedMessages * 100 : 0;
        ConversionRate = clickedMessages > 0 ? (decimal)convertedMessages / clickedMessages * 100 : 0;
        BounceRate = totalMessages > 0 ? (decimal)bouncedMessages / totalMessages * 100 : 0;
        UnsubscribeRate = deliveredMessages > 0 ? (decimal)unsubscribedMessages / deliveredMessages * 100 : 0;

        // Calculate costs
        CostPerMessage = totalMessages > 0 ? totalCost / totalMessages : 0;
        CostPerConversion = convertedMessages > 0 ? totalCost / convertedMessages : 0;

        // Calculate engagement score (weighted average of engagement metrics)
        EngagementScore = CalculateEngagementScore();
    }

    public static PerformanceMetrics Empty()
    {
        return new PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, TimeSpan.Zero, TimeSpan.Zero, 0);
    }

    private decimal CalculateEngagementScore()
    {
        if (TotalMessages == 0) return 0;

        var deliveryWeight = 0.2m;
        var openWeight = 0.3m;
        var clickWeight = 0.3m;
        var conversionWeight = 0.2m;

        return (DeliveryRate * deliveryWeight) +
               (OpenRate * openWeight) +
               (ClickThroughRate * clickWeight) +
               (ConversionRate * conversionWeight);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalMessages;
        yield return DeliveredMessages;
        yield return OpenedMessages;
        yield return ClickedMessages;
        yield return ConvertedMessages;
        yield return BouncedMessages;
        yield return UnsubscribedMessages;
        yield return AverageDeliveryTime;
        yield return AverageResponseTime;
        yield return TotalCost;

        foreach (var kvp in ChannelBreakdown.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in MessageTypeBreakdown.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Conversion analytics value object
/// </summary>
public class ConversionAnalytics : ValueObject
{
    public int TotalConversions { get; private set; }
    public decimal ConversionRate { get; private set; }
    public decimal ConversionValue { get; private set; }
    public decimal AverageConversionValue { get; private set; }
    public TimeSpan AverageTimeToConversion { get; private set; }
    public Dictionary<string, ConversionFunnel> ConversionFunnels { get; private set; }
    public Dictionary<string, int> ConversionsBySource { get; private set; }
    public Dictionary<string, decimal> ConversionValueBySource { get; private set; }
    public List<ConversionPath> TopConversionPaths { get; private set; }

    private ConversionAnalytics()
    {
        ConversionFunnels = new Dictionary<string, ConversionFunnel>();
        ConversionsBySource = new Dictionary<string, int>();
        ConversionValueBySource = new Dictionary<string, decimal>();
        TopConversionPaths = new List<ConversionPath>();
    }

    public ConversionAnalytics(
        int totalConversions,
        decimal conversionRate,
        decimal conversionValue,
        TimeSpan averageTimeToConversion,
        Dictionary<string, ConversionFunnel>? conversionFunnels = null,
        Dictionary<string, int>? conversionsBySource = null,
        Dictionary<string, decimal>? conversionValueBySource = null,
        List<ConversionPath>? topConversionPaths = null)
    {
        TotalConversions = totalConversions;
        ConversionRate = conversionRate;
        ConversionValue = conversionValue;
        AverageConversionValue = totalConversions > 0 ? conversionValue / totalConversions : 0;
        AverageTimeToConversion = averageTimeToConversion;
        ConversionFunnels = conversionFunnels ?? new Dictionary<string, ConversionFunnel>();
        ConversionsBySource = conversionsBySource ?? new Dictionary<string, int>();
        ConversionValueBySource = conversionValueBySource ?? new Dictionary<string, decimal>();
        TopConversionPaths = topConversionPaths ?? new List<ConversionPath>();
    }

    public static ConversionAnalytics Empty()
    {
        return new ConversionAnalytics(0, 0, 0, TimeSpan.Zero);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalConversions;
        yield return ConversionRate;
        yield return ConversionValue;
        yield return AverageTimeToConversion;

        foreach (var kvp in ConversionFunnels.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in ConversionsBySource.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var path in TopConversionPaths)
        {
            yield return path;
        }
    }
}

/// <summary>
/// ROI analysis value object
/// </summary>
public class ROIAnalysis : ValueObject
{
    public decimal TotalInvestment { get; private set; }
    public decimal TotalRevenue { get; private set; }
    public decimal NetProfit { get; private set; }
    public decimal ROI { get; private set; }
    public decimal ROAS { get; private set; } // Return on Ad Spend
    public decimal CustomerAcquisitionCost { get; private set; }
    public decimal CustomerLifetimeValue { get; private set; }
    public decimal PaybackPeriod { get; private set; }
    public Dictionary<string, decimal> ROIByChannel { get; private set; }
    public Dictionary<string, decimal> ROIByMessageType { get; private set; }
    public Dictionary<string, decimal> ROIBySegment { get; private set; }

    private ROIAnalysis()
    {
        ROIByChannel = new Dictionary<string, decimal>();
        ROIByMessageType = new Dictionary<string, decimal>();
        ROIBySegment = new Dictionary<string, decimal>();
    }

    public ROIAnalysis(
        decimal totalInvestment,
        decimal totalRevenue,
        decimal customerAcquisitionCost,
        decimal customerLifetimeValue,
        decimal paybackPeriod,
        Dictionary<string, decimal>? roiByChannel = null,
        Dictionary<string, decimal>? roiByMessageType = null,
        Dictionary<string, decimal>? roiBySegment = null)
    {
        TotalInvestment = totalInvestment;
        TotalRevenue = totalRevenue;
        NetProfit = totalRevenue - totalInvestment;
        ROI = totalInvestment > 0 ? (NetProfit / totalInvestment) * 100 : 0;
        ROAS = totalInvestment > 0 ? totalRevenue / totalInvestment : 0;
        CustomerAcquisitionCost = customerAcquisitionCost;
        CustomerLifetimeValue = customerLifetimeValue;
        PaybackPeriod = paybackPeriod;
        ROIByChannel = roiByChannel ?? new Dictionary<string, decimal>();
        ROIByMessageType = roiByMessageType ?? new Dictionary<string, decimal>();
        ROIBySegment = roiBySegment ?? new Dictionary<string, decimal>();
    }

    public static ROIAnalysis Empty()
    {
        return new ROIAnalysis(0, 0, 0, 0, 0);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return TotalInvestment;
        yield return TotalRevenue;
        yield return CustomerAcquisitionCost;
        yield return CustomerLifetimeValue;
        yield return PaybackPeriod;

        foreach (var kvp in ROIByChannel.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in ROIByMessageType.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in ROIBySegment.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Segment performance value object
/// </summary>
public class SegmentPerformance : ValueObject
{
    public string SegmentName { get; private set; }
    public string SegmentDescription { get; private set; }
    public int UserCount { get; private set; }
    public PerformanceMetrics Metrics { get; private set; }
    public decimal SegmentValue { get; private set; }
    public decimal EngagementIndex { get; private set; }
    public Dictionary<string, decimal> ChannelPreferences { get; private set; }
    public Dictionary<string, decimal> MessageTypePreferences { get; private set; }

    private SegmentPerformance()
    {
        SegmentName = string.Empty;
        SegmentDescription = string.Empty;
        Metrics = PerformanceMetrics.Empty();
        ChannelPreferences = new Dictionary<string, decimal>();
        MessageTypePreferences = new Dictionary<string, decimal>();
    }

    public SegmentPerformance(
        string segmentName,
        string segmentDescription,
        int userCount,
        PerformanceMetrics metrics,
        decimal segmentValue,
        decimal engagementIndex,
        Dictionary<string, decimal>? channelPreferences = null,
        Dictionary<string, decimal>? messageTypePreferences = null)
    {
        SegmentName = segmentName;
        SegmentDescription = segmentDescription;
        UserCount = userCount;
        Metrics = metrics;
        SegmentValue = segmentValue;
        EngagementIndex = engagementIndex;
        ChannelPreferences = channelPreferences ?? new Dictionary<string, decimal>();
        MessageTypePreferences = messageTypePreferences ?? new Dictionary<string, decimal>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return SegmentName;
        yield return UserCount;
        yield return Metrics;
        yield return SegmentValue;
        yield return EngagementIndex;

        foreach (var kvp in ChannelPreferences.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in MessageTypePreferences.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Cohort analysis value object
/// </summary>
public class CohortAnalysis : ValueObject
{
    public string CohortName { get; private set; }
    public DateTime CohortDate { get; private set; }
    public int CohortSize { get; private set; }
    public Dictionary<int, decimal> RetentionRates { get; private set; }
    public Dictionary<int, decimal> EngagementRates { get; private set; }
    public Dictionary<int, decimal> ConversionRates { get; private set; }
    public Dictionary<int, decimal> RevenuePerUser { get; private set; }

    private CohortAnalysis()
    {
        CohortName = string.Empty;
        RetentionRates = new Dictionary<int, decimal>();
        EngagementRates = new Dictionary<int, decimal>();
        ConversionRates = new Dictionary<int, decimal>();
        RevenuePerUser = new Dictionary<int, decimal>();
    }

    public CohortAnalysis(
        string cohortName,
        DateTime cohortDate,
        int cohortSize,
        Dictionary<int, decimal> retentionRates,
        Dictionary<int, decimal> engagementRates,
        Dictionary<int, decimal> conversionRates,
        Dictionary<int, decimal> revenuePerUser)
    {
        CohortName = cohortName;
        CohortDate = cohortDate;
        CohortSize = cohortSize;
        RetentionRates = retentionRates;
        EngagementRates = engagementRates;
        ConversionRates = conversionRates;
        RevenuePerUser = revenuePerUser;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return CohortName;
        yield return CohortDate;
        yield return CohortSize;

        foreach (var kvp in RetentionRates.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }

        foreach (var kvp in EngagementRates.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Funnel analysis value object
/// </summary>
public class FunnelAnalysis : ValueObject
{
    public string FunnelName { get; private set; }
    public List<FunnelStep> Steps { get; private set; }
    public decimal OverallConversionRate { get; private set; }
    public Dictionary<string, decimal> DropOffRates { get; private set; }

    private FunnelAnalysis()
    {
        FunnelName = string.Empty;
        Steps = new List<FunnelStep>();
        DropOffRates = new Dictionary<string, decimal>();
    }

    public FunnelAnalysis(
        string funnelName,
        List<FunnelStep> steps,
        Dictionary<string, decimal>? dropOffRates = null)
    {
        FunnelName = funnelName;
        Steps = steps;
        DropOffRates = dropOffRates ?? new Dictionary<string, decimal>();
        OverallConversionRate = CalculateOverallConversionRate();
    }

    private decimal CalculateOverallConversionRate()
    {
        if (!Steps.Any()) return 0;

        var firstStep = Steps.First();
        var lastStep = Steps.Last();

        return firstStep.UserCount > 0 ? (decimal)lastStep.UserCount / firstStep.UserCount * 100 : 0;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return FunnelName;
        yield return OverallConversionRate;

        foreach (var step in Steps)
        {
            yield return step;
        }

        foreach (var kvp in DropOffRates.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Funnel step value object
/// </summary>
public class FunnelStep : ValueObject
{
    public string StepName { get; private set; }
    public int StepOrder { get; private set; }
    public int UserCount { get; private set; }
    public decimal ConversionRate { get; private set; }
    public TimeSpan AverageTimeInStep { get; private set; }

    private FunnelStep()
    {
        StepName = string.Empty;
    }

    public FunnelStep(string stepName, int stepOrder, int userCount, decimal conversionRate, TimeSpan averageTimeInStep)
    {
        StepName = stepName;
        StepOrder = stepOrder;
        UserCount = userCount;
        ConversionRate = conversionRate;
        AverageTimeInStep = averageTimeInStep;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return StepName;
        yield return StepOrder;
        yield return UserCount;
        yield return ConversionRate;
        yield return AverageTimeInStep;
    }
}

/// <summary>
/// Attribution analysis value object
/// </summary>
public class AttributionAnalysis : ValueObject
{
    public Dictionary<string, decimal> FirstTouchAttribution { get; private set; }
    public Dictionary<string, decimal> LastTouchAttribution { get; private set; }
    public Dictionary<string, decimal> LinearAttribution { get; private set; }
    public Dictionary<string, decimal> TimeDecayAttribution { get; private set; }
    public Dictionary<string, decimal> PositionBasedAttribution { get; private set; }

    private AttributionAnalysis()
    {
        FirstTouchAttribution = new Dictionary<string, decimal>();
        LastTouchAttribution = new Dictionary<string, decimal>();
        LinearAttribution = new Dictionary<string, decimal>();
        TimeDecayAttribution = new Dictionary<string, decimal>();
        PositionBasedAttribution = new Dictionary<string, decimal>();
    }

    public AttributionAnalysis(
        Dictionary<string, decimal> firstTouchAttribution,
        Dictionary<string, decimal> lastTouchAttribution,
        Dictionary<string, decimal> linearAttribution,
        Dictionary<string, decimal> timeDecayAttribution,
        Dictionary<string, decimal> positionBasedAttribution)
    {
        FirstTouchAttribution = firstTouchAttribution;
        LastTouchAttribution = lastTouchAttribution;
        LinearAttribution = linearAttribution;
        TimeDecayAttribution = timeDecayAttribution;
        PositionBasedAttribution = positionBasedAttribution;
    }

    public static AttributionAnalysis Empty()
    {
        return new AttributionAnalysis(
            new Dictionary<string, decimal>(),
            new Dictionary<string, decimal>(),
            new Dictionary<string, decimal>(),
            new Dictionary<string, decimal>(),
            new Dictionary<string, decimal>());
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        foreach (var kvp in FirstTouchAttribution.OrderBy(x => x.Key))
        {
            yield return $"first_{kvp.Key}";
            yield return kvp.Value;
        }

        foreach (var kvp in LastTouchAttribution.OrderBy(x => x.Key))
        {
            yield return $"last_{kvp.Key}";
            yield return kvp.Value;
        }

        foreach (var kvp in LinearAttribution.OrderBy(x => x.Key))
        {
            yield return $"linear_{kvp.Key}";
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Conversion funnel value object
/// </summary>
public class ConversionFunnel : ValueObject
{
    public string Name { get; private set; }
    public List<FunnelStep> Steps { get; private set; }
    public decimal ConversionRate { get; private set; }

    private ConversionFunnel()
    {
        Name = string.Empty;
        Steps = new List<FunnelStep>();
    }

    public ConversionFunnel(string name, List<FunnelStep> steps)
    {
        Name = name;
        Steps = steps;
        ConversionRate = CalculateConversionRate();
    }

    private decimal CalculateConversionRate()
    {
        if (!Steps.Any()) return 0;

        var firstStep = Steps.First();
        var lastStep = Steps.Last();

        return firstStep.UserCount > 0 ? (decimal)lastStep.UserCount / firstStep.UserCount * 100 : 0;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return ConversionRate;

        foreach (var step in Steps)
        {
            yield return step;
        }
    }
}

/// <summary>
/// Conversion path value object
/// </summary>
public class ConversionPath : ValueObject
{
    public List<string> Touchpoints { get; private set; }
    public int ConversionCount { get; private set; }
    public decimal ConversionValue { get; private set; }
    public TimeSpan AveragePathLength { get; private set; }

    private ConversionPath()
    {
        Touchpoints = new List<string>();
    }

    public ConversionPath(List<string> touchpoints, int conversionCount, decimal conversionValue, TimeSpan averagePathLength)
    {
        Touchpoints = touchpoints;
        ConversionCount = conversionCount;
        ConversionValue = conversionValue;
        AveragePathLength = averagePathLength;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ConversionCount;
        yield return ConversionValue;
        yield return AveragePathLength;

        foreach (var touchpoint in Touchpoints)
        {
            yield return touchpoint;
        }
    }
}

/// <summary>
/// Benchmark value object
/// </summary>
public class Benchmark : ValueObject
{
    public string MetricName { get; private set; }
    public decimal CurrentValue { get; private set; }
    public decimal BenchmarkValue { get; private set; }
    public decimal Variance { get; private set; }
    public string BenchmarkSource { get; private set; }
    public BenchmarkType Type { get; private set; }

    private Benchmark()
    {
        MetricName = string.Empty;
        BenchmarkSource = string.Empty;
    }

    public Benchmark(string metricName, decimal currentValue, decimal benchmarkValue, string benchmarkSource, BenchmarkType type)
    {
        MetricName = metricName;
        CurrentValue = currentValue;
        BenchmarkValue = benchmarkValue;
        Variance = benchmarkValue > 0 ? ((currentValue - benchmarkValue) / benchmarkValue) * 100 : 0;
        BenchmarkSource = benchmarkSource;
        Type = type;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MetricName;
        yield return CurrentValue;
        yield return BenchmarkValue;
        yield return BenchmarkSource;
        yield return Type;
    }
}

/// <summary>
/// Report configuration value object
/// </summary>
public class ReportConfiguration : ValueObject
{
    public bool IncludeConversionAnalysis { get; private set; }
    public bool IncludeROIAnalysis { get; private set; }
    public bool IncludeSegmentAnalysis { get; private set; }
    public bool IncludeCohortAnalysis { get; private set; }
    public bool IncludeFunnelAnalysis { get; private set; }
    public bool IncludeAttributionAnalysis { get; private set; }
    public bool IncludeBenchmarks { get; private set; }
    public bool IncludeInsights { get; private set; }
    public bool IncludeRecommendations { get; private set; }
    public string Currency { get; private set; }
    public string TimeZone { get; private set; }
    public Dictionary<string, object> CustomSettings { get; private set; }

    private ReportConfiguration()
    {
        Currency = "INR";
        TimeZone = "Asia/Kolkata";
        CustomSettings = new Dictionary<string, object>();
    }

    public ReportConfiguration(
        bool includeConversionAnalysis = true,
        bool includeROIAnalysis = true,
        bool includeSegmentAnalysis = true,
        bool includeCohortAnalysis = false,
        bool includeFunnelAnalysis = true,
        bool includeAttributionAnalysis = false,
        bool includeBenchmarks = true,
        bool includeInsights = true,
        bool includeRecommendations = true,
        string currency = "INR",
        string timeZone = "Asia/Kolkata",
        Dictionary<string, object>? customSettings = null)
    {
        IncludeConversionAnalysis = includeConversionAnalysis;
        IncludeROIAnalysis = includeROIAnalysis;
        IncludeSegmentAnalysis = includeSegmentAnalysis;
        IncludeCohortAnalysis = includeCohortAnalysis;
        IncludeFunnelAnalysis = includeFunnelAnalysis;
        IncludeAttributionAnalysis = includeAttributionAnalysis;
        IncludeBenchmarks = includeBenchmarks;
        IncludeInsights = includeInsights;
        IncludeRecommendations = includeRecommendations;
        Currency = currency;
        TimeZone = timeZone;
        CustomSettings = customSettings ?? new Dictionary<string, object>();
    }

    public static ReportConfiguration Default()
    {
        return new ReportConfiguration();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IncludeConversionAnalysis;
        yield return IncludeROIAnalysis;
        yield return IncludeSegmentAnalysis;
        yield return IncludeCohortAnalysis;
        yield return IncludeFunnelAnalysis;
        yield return IncludeAttributionAnalysis;
        yield return IncludeBenchmarks;
        yield return IncludeInsights;
        yield return IncludeRecommendations;
        yield return Currency;
        yield return TimeZone;

        foreach (var kvp in CustomSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}

/// <summary>
/// Benchmark type enumeration
/// </summary>
public enum BenchmarkType
{
    Industry = 1,
    Historical = 2,
    Competitor = 3,
    Goal = 4,
    Best = 5
}


