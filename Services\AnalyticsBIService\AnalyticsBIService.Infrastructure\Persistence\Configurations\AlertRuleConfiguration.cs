using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AnalyticsBIService.Domain.Entities;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

public class AlertRuleConfiguration : IEntityTypeConfiguration<AlertRule>
{
    public void Configure(EntityTypeBuilder<AlertRule> builder)
    {
        builder.ToTable("alert_rules");

        builder.HasKey(ar => ar.Id);

        builder.Property(ar => ar.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ar => ar.Description)
            .HasMaxLength(1000);

        builder.Property(ar => ar.MetricName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ar => ar.Operator)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ar => ar.ThresholdValue)
            .IsRequired()
            .HasPrecision(18, 4);

        builder.Property(ar => ar.Severity)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ar => ar.IsActive)
            .IsRequired();

        builder.Property(ar => ar.CreatedBy)
            .IsRequired();

        builder.Property(ar => ar.CreatedAt)
            .IsRequired();

        builder.Property(ar => ar.UpdatedAt)
            .IsRequired();

        builder.Property(ar => ar.LastTriggeredAt)
            .IsRequired(false);

        // Configure Conditions as JSON
        builder.Property(ar => ar.Conditions)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("jsonb");

        // Configure Actions as JSON
        builder.Property(ar => ar.Actions)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<List<string>>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("jsonb");

        // Configure relationships
        builder.HasMany(ar => ar.Alerts)
            .WithOne()
            .HasForeignKey("AlertRuleId")
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(ar => ar.MetricName)
            .HasDatabaseName("IX_alert_rules_metric_name");

        builder.HasIndex(ar => ar.Severity)
            .HasDatabaseName("IX_alert_rules_severity");

        builder.HasIndex(ar => ar.IsActive)
            .HasDatabaseName("IX_alert_rules_is_active");

        builder.HasIndex(ar => ar.CreatedBy)
            .HasDatabaseName("IX_alert_rules_created_by");

        builder.HasIndex(ar => ar.LastTriggeredAt)
            .HasDatabaseName("IX_alert_rules_last_triggered_at");

        builder.HasIndex(ar => new { ar.MetricName, ar.IsActive })
            .HasDatabaseName("IX_alert_rules_metric_name_is_active");
    }
}
