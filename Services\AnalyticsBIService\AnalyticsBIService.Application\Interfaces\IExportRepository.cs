using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Repository interface for export operations
/// </summary>
public interface IExportRepository
{
    /// <summary>
    /// Saves export request
    /// </summary>
    Task<Guid> SaveExportRequestAsync(
        ExportRequestDto exportRequest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets export request by ID
    /// </summary>
    Task<ExportRequestDto?> GetExportRequestAsync(
        Guid exportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates export status
    /// </summary>
    Task UpdateExportStatusAsync(
        Guid exportId,
        string status,
        string? filePath = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets export history
    /// </summary>
    Task<List<ExportHistoryDto>> GetExportHistoryAsync(
        Guid userId,
        int page = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cleans up old export files
    /// </summary>
    Task CleanupOldExportsAsync(
        DateTime olderThan,
        CancellationToken cancellationToken = default);

    // Additional methods required by DataExportService and OrderTripExportService
    Task CreateExportRecordAsync(ExportRecord record, CancellationToken cancellationToken = default);
    Task UpdateExportRecordAsync(ExportRecord record, CancellationToken cancellationToken = default);
    Task<List<ExportTemplateDto>> GetExportTemplatesAsync(CancellationToken cancellationToken = default);
    Task<ExportRecord?> GetExportRecordAsync(Guid id, CancellationToken cancellationToken = default);
}

/// <summary>
/// Export request data transfer object
/// </summary>
public class ExportRequestDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string ExportType { get; set; } = string.Empty;
    public ExportFormat Format { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime RequestedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional properties required by DataExportService
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Export history data transfer object
/// </summary>
public class ExportHistoryDto
{
    public Guid Id { get; set; }
    public string ExportType { get; set; } = string.Empty;
    public ExportFormat Format { get; set; }
    public DateTime RequestedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Export template data transfer object
/// </summary>
public class ExportTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ExportType { get; set; } = string.Empty;
    public ExportFormat Format { get; set; }
    public List<string> Fields { get; set; } = new();
    public Dictionary<string, object> DefaultParameters { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
