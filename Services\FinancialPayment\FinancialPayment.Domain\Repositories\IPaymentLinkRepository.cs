using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.Repositories;

/// <summary>
/// Repository interface for payment links
/// </summary>
public interface IPaymentLinkRepository
{
    Task<PaymentLink?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<PaymentLink?> GetByTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetByTransportCompanyIdAsync(
        Guid transportCompanyId, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetByShipperIdAsync(
        Guid shipperId, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetByOrderIdAsync(
        Guid orderId, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetByInvoiceIdAsync(
        Guid invoiceId, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetByStatusAsync(
        PaymentLinkStatus status, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetExpiringLinksAsync(
        DateTime beforeDate, 
        CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> GetActiveLinksAsync(CancellationToken cancellationToken = default);
    Task<List<PaymentLink>> SearchAsync(
        PaymentLinkSearchCriteria criteria, 
        CancellationToken cancellationToken = default);
    Task AddAsync(PaymentLink paymentLink, CancellationToken cancellationToken = default);
    Task UpdateAsync(PaymentLink paymentLink, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<int> CountByTransportCompanyIdAsync(
        Guid transportCompanyId, 
        PaymentLinkStatus? status = null, 
        CancellationToken cancellationToken = default);
    Task<decimal> GetTotalAmountByTransportCompanyIdAsync(
        Guid transportCompanyId, 
        PaymentLinkStatus? status = null, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Search criteria for payment links
/// </summary>
public class PaymentLinkSearchCriteria
{
    public Guid? TransportCompanyId { get; set; }
    public Guid? ShipperId { get; set; }
    public Guid? OrderId { get; set; }
    public Guid? InvoiceId { get; set; }
    public PaymentLinkStatus? Status { get; set; }
    public PaymentLinkType? LinkType { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? ExpiresAfter { get; set; }
    public DateTime? ExpiresBefore { get; set; }
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public string? Currency { get; set; }
    public string? SearchTerm { get; set; } // Search in description, shipper name, etc.
    public List<string> Tags { get; set; } = new();
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}
