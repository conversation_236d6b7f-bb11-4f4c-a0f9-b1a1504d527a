using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

public class EscrowMilestone : BaseEntity
{
    public Guid EscrowAccountId { get; private set; }
    public string Description { get; private set; }
    public Money Amount { get; private set; }
    public DateTime? DueDate { get; private set; }
    public EscrowMilestoneStatus Status { get; private set; }

    public DateTime? CompletedAt { get; private set; }
    public string? CompletionNotes { get; private set; }

    // Navigation properties
    public EscrowAccount EscrowAccount { get; private set; } = null!;

    private EscrowMilestone() { }

    public EscrowMilestone(
        Guid escrowAccountId,
        string description,
        Money amount,
        DateTime? dueDate = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty");

        if (amount == null || amount.Amount <= 0)
            throw new ArgumentException("Amount must be greater than zero");

        Id = Guid.NewGuid();
        EscrowAccountId = escrowAccountId;
        Description = description;
        Amount = amount;
        DueDate = dueDate;
        Status = EscrowMilestoneStatus.Pending;
        CreatedAt = DateTime.UtcNow;
    }

    public void Complete(string completionNotes)
    {
        if (Status != EscrowMilestoneStatus.Pending)
            throw new InvalidOperationException("Only pending milestones can be completed");

        Status = EscrowMilestoneStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        CompletionNotes = completionNotes;
    }

    public void Cancel(string reason)
    {
        if (Status != EscrowMilestoneStatus.Pending)
            throw new InvalidOperationException("Only pending milestones can be cancelled");

        Status = EscrowMilestoneStatus.Cancelled;
        CompletionNotes = reason;
    }

    public bool IsOverdue()
    {
        return DueDate.HasValue &&
               DateTime.UtcNow > DueDate.Value &&
               Status == EscrowMilestoneStatus.Pending;
    }
}
