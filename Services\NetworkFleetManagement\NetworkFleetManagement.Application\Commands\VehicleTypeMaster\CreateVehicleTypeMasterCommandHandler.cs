using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Interfaces;
using Shared.Domain.Common;
using Shared.Messaging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Handler for creating a new vehicle type master
/// </summary>
public class CreateVehicleTypeMasterCommandHandler : IRequestHandler<CreateVehicleTypeMasterCommand, VehicleTypeMasterDto>
{
    private readonly IVehicleTypeMasterRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CreateVehicleTypeMasterCommandHandler> _logger;

    public CreateVehicleTypeMasterCommandHandler(
        IVehicleTypeMasterRepository repository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker,
        ILogger<CreateVehicleTypeMasterCommandHandler> logger)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<VehicleTypeMasterDto> Handle(CreateVehicleTypeMasterCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating vehicle type master with code: {Code}", request.Code);

            // Check if code already exists
            var existingByCode = await _repository.GetByCodeAsync(request.Code, cancellationToken);
            if (existingByCode != null)
            {
                throw new InvalidOperationException($"Vehicle type master with code '{request.Code}' already exists");
            }

            // Get next sort order if not specified
            var sortOrder = request.SortOrder;
            if (sortOrder == 0)
            {
                sortOrder = await _repository.GetMaxSortOrderAsync(cancellationToken) + 1;
            }

            // Create entity
            var vehicleTypeMaster = new Domain.Entities.VehicleTypeMaster(
                request.Code,
                request.Name,
                request.Description,
                request.Category,
                request.MinLoadCapacityKg,
                request.MaxLoadCapacityKg,
                request.MinVolumeCapacityM3,
                request.MaxVolumeCapacityM3,
                request.SpecialRequirements,
                sortOrder,
                request.IconUrl,
                request.AdditionalProperties);

            // Add to repository
            _repository.Add(vehicleTypeMaster);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("vehicletypemaster.created", new
            {
                Id = vehicleTypeMaster.Id,
                Code = vehicleTypeMaster.Code,
                Name = vehicleTypeMaster.Name,
                Category = vehicleTypeMaster.Category,
                IsActive = vehicleTypeMaster.IsActive,
                CreatedAt = vehicleTypeMaster.CreatedAt
            }, cancellationToken);

            // Map to DTO and return
            var result = _mapper.Map<VehicleTypeMasterDto>(vehicleTypeMaster);

            _logger.LogInformation("Successfully created vehicle type master with ID: {Id}", vehicleTypeMaster.Id);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle type master with code: {Code}", request.Code);
            throw;
        }
    }
}

