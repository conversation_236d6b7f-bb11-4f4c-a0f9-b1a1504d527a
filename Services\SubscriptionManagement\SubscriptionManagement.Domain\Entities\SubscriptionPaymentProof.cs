using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Events;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Entities
{
    /// <summary>
    /// Represents proof of payment submitted by users for offline payments
    /// </summary>
    public class SubscriptionPaymentProof : AggregateRoot
    {
        public Guid SubscriptionId { get; private set; }
        public Guid UserId { get; private set; }
        public Money Amount { get; private set; }
        public DateTime PaymentDate { get; private set; }
        public string ProofImageUrl { get; private set; }
        public string? Notes { get; private set; }
        public PaymentProofStatus Status { get; private set; }
        public string? PaymentMethod { get; private set; }
        public string? TransactionReference { get; private set; }

        // Verification details
        public Guid? VerifiedByUserId { get; private set; }
        public DateTime? VerifiedAt { get; private set; }
        public string? VerificationNotes { get; private set; }
        public string? RejectionReason { get; private set; }

        // Navigation properties
        public Subscription? Subscription { get; private set; }

        private SubscriptionPaymentProof()
        {
            ProofImageUrl = string.Empty;
        }

        public SubscriptionPaymentProof(
            Guid subscriptionId,
            Guid userId,
            Money amount,
            DateTime paymentDate,
            string proofImageUrl,
            string? notes = null,
            string? paymentMethod = null,
            string? transactionReference = null)
        {
            if (amount == null)
                throw new SubscriptionDomainException("Payment amount cannot be null");

            if (amount.Amount <= 0)
                throw new SubscriptionDomainException("Payment amount must be greater than zero");

            if (string.IsNullOrWhiteSpace(proofImageUrl))
                throw new SubscriptionDomainException("Proof image URL cannot be empty");

            if (paymentDate > DateTime.UtcNow)
                throw new SubscriptionDomainException("Payment date cannot be in the future");

            SubscriptionId = subscriptionId;
            UserId = userId;
            Amount = amount;
            PaymentDate = paymentDate;
            ProofImageUrl = proofImageUrl;
            Notes = notes;
            PaymentMethod = paymentMethod;
            TransactionReference = transactionReference;
            Status = PaymentProofStatus.Pending;

            AddDomainEvent(new PaymentProofSubmittedEvent(
                Id, subscriptionId, userId, amount.Amount, amount.Currency, paymentDate));
        }

        public static SubscriptionPaymentProof Create(
            Guid subscriptionId,
            Guid userId,
            Money amount,
            DateTime paymentDate,
            string proofImageUrl,
            string? notes = null,
            string? paymentMethod = null,
            string? transactionReference = null)
        {
            return new SubscriptionPaymentProof(
                subscriptionId, userId, amount, paymentDate, proofImageUrl,
                notes, paymentMethod, transactionReference);
        }

        public void MarkAsUnderReview()
        {
            if (Status != PaymentProofStatus.Pending)
                throw new SubscriptionDomainException("Only pending payment proofs can be marked as under review");

            Status = PaymentProofStatus.UnderReview;
            SetUpdatedAt();
        }

        public void Verify(Guid verifiedByUserId, string? verificationNotes = null)
        {
            if (Status != PaymentProofStatus.Pending && Status != PaymentProofStatus.UnderReview && Status != PaymentProofStatus.RequiresAdditionalInfo)
                throw new SubscriptionDomainException("Only pending, under review, or requiring additional info payment proofs can be verified");

            Status = PaymentProofStatus.Verified;
            VerifiedByUserId = verifiedByUserId;
            VerifiedAt = DateTime.UtcNow;
            VerificationNotes = verificationNotes;
            RejectionReason = null; // Clear any previous rejection reason
            SetUpdatedAt();

            AddDomainEvent(new PaymentProofVerifiedEvent(
                Id, SubscriptionId, UserId, verifiedByUserId, Amount.Amount, Amount.Currency, VerifiedAt.Value));
        }

        public void Reject(Guid rejectedByUserId, string rejectionReason, string? verificationNotes = null)
        {
            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new SubscriptionDomainException("Rejection reason is required");

            if (Status == PaymentProofStatus.Verified)
                throw new SubscriptionDomainException("Verified payment proofs cannot be rejected");

            Status = PaymentProofStatus.Rejected;
            VerifiedByUserId = rejectedByUserId;
            VerifiedAt = DateTime.UtcNow;
            RejectionReason = rejectionReason;
            VerificationNotes = verificationNotes;
            SetUpdatedAt();

            AddDomainEvent(new PaymentProofRejectedEvent(
                Id, SubscriptionId, UserId, rejectedByUserId, rejectionReason, DateTime.UtcNow));
        }

        public void RequestAdditionalInfo(Guid requestedByUserId, string requestReason)
        {
            if (string.IsNullOrWhiteSpace(requestReason))
                throw new SubscriptionDomainException("Request reason is required");

            if (Status == PaymentProofStatus.Verified || Status == PaymentProofStatus.Rejected)
                throw new SubscriptionDomainException("Verified or rejected payment proofs cannot request additional info");

            Status = PaymentProofStatus.RequiresAdditionalInfo;
            VerifiedByUserId = requestedByUserId;
            VerifiedAt = DateTime.UtcNow;
            VerificationNotes = requestReason;
            SetUpdatedAt();

            AddDomainEvent(new PaymentProofAdditionalInfoRequestedEvent(
                Id, SubscriptionId, UserId, requestedByUserId, requestReason, DateTime.UtcNow));
        }

        public void UpdateProofImage(string newProofImageUrl, string? additionalNotes = null)
        {
            if (string.IsNullOrWhiteSpace(newProofImageUrl))
                throw new SubscriptionDomainException("Proof image URL cannot be empty");

            if (Status != PaymentProofStatus.RequiresAdditionalInfo && Status != PaymentProofStatus.Pending)
                throw new SubscriptionDomainException("Only pending or requiring additional info payment proofs can be updated");

            ProofImageUrl = newProofImageUrl;

            if (!string.IsNullOrWhiteSpace(additionalNotes))
            {
                Notes = string.IsNullOrWhiteSpace(Notes) ? additionalNotes : $"{Notes}\n\n{additionalNotes}";
            }

            // Reset status to pending if it was requiring additional info
            if (Status == PaymentProofStatus.RequiresAdditionalInfo)
            {
                Status = PaymentProofStatus.Pending;
                VerifiedByUserId = null;
                VerifiedAt = null;
                VerificationNotes = null;
            }

            SetUpdatedAt();
        }

        public bool IsVerified() => Status == PaymentProofStatus.Verified;
        public bool IsRejected() => Status == PaymentProofStatus.Rejected;
        public bool IsPending() => Status == PaymentProofStatus.Pending;
        public bool RequiresAdditionalInfo() => Status == PaymentProofStatus.RequiresAdditionalInfo;
        public bool IsUnderReview() => Status == PaymentProofStatus.UnderReview;

        public string GetStatusDescription()
        {
            return Status switch
            {
                PaymentProofStatus.Pending => "Payment proof is pending review",
                PaymentProofStatus.UnderReview => "Payment proof is under review",
                PaymentProofStatus.Verified => "Payment proof has been verified",
                PaymentProofStatus.Rejected => $"Payment proof has been rejected: {RejectionReason}",
                PaymentProofStatus.RequiresAdditionalInfo => $"Additional information required: {VerificationNotes}",
                _ => "Unknown payment proof status"
            };
        }

        public bool CanBeVerified()
        {
            return Status == PaymentProofStatus.Pending ||
                   Status == PaymentProofStatus.UnderReview ||
                   Status == PaymentProofStatus.RequiresAdditionalInfo;
        }

        public bool CanBeRejected()
        {
            return Status != PaymentProofStatus.Verified;
        }

        public bool CanRequestAdditionalInfo()
        {
            return Status != PaymentProofStatus.Verified && Status != PaymentProofStatus.Rejected;
        }
    }
}
