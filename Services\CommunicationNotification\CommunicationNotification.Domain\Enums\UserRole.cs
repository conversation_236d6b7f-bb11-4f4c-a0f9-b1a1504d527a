namespace CommunicationNotification.Domain.Enums;

public enum UserRole
{
    /// <summary>
    /// System administrator
    /// </summary>
    Admin = 1,
    
    /// <summary>
    /// Transport company user
    /// </summary>
    TransportCompany = 2,
    
    /// <summary>
    /// Broker user
    /// </summary>
    Broker = 3,
    
    /// <summary>
    /// Carrier company user
    /// </summary>
    Carrier = 4,
    
    /// <summary>
    /// Shipper user
    /// </summary>
    Shipper = 5,
    
    /// <summary>
    /// Driver user
    /// </summary>
    Driver = 6,
    
    /// <summary>
    /// Customer support user
    /// </summary>
    Support = 7
}
