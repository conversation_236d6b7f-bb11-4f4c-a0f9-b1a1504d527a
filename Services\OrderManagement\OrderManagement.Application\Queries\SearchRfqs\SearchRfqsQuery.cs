using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.SearchRfqs;

public class SearchRfqsQuery : IRequest<PagedResult<RfqSummaryDto>>
{
    public string SearchTerm { get; set; } = string.Empty;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public Guid RequestingUserId { get; set; }
    
    // Optional filters
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? Statuses { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public bool ExcludeExpired { get; set; } = true;
}
