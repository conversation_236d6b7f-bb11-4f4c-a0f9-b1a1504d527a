using System;
using System.Collections.Generic;
using MediatR;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class RenderFormQuery : IRequest<Dictionary<string, object>>
    {
        public Guid FormDefinitionId { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
        public string? Platform { get; set; }
        public string? DeviceId { get; set; }
        public Guid? UserId { get; set; }
    }
}
