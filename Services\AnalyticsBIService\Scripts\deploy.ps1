# Analytics & BI Service Deployment Script (PowerShell)
# This script handles deployment to different environments

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "staging", "production")]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "latest",
    
    [Parameter(Mandatory=$false)]
    [string]$Registry = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Namespace = "tli-analytics",
    
    [Parameter(Mandatory=$false)]
    [string]$KubeConfig = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ComposeFile = "docker-compose.yml",
    
    [Parameter(Mandatory=$false)]
    [string]$BuildArgs = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$PushImage,
    
    [Parameter(Mandatory=$false)]
    [switch]$DeployK8s,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$Help
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$ServiceName = "analytics-bi-service"
$DockerImage = "tli/analytics-bi-service"

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Help function
function Show-Help {
    Write-Host @"
Analytics & BI Service Deployment Script (PowerShell)

Usage: .\deploy.ps1 [OPTIONS]

OPTIONS:
    -Environment ENV        Target environment (development, staging, production) [default: development]
    -Version VERSION        Image version/tag [default: latest]
    -Registry REGISTRY      Docker registry URL
    -Namespace NAMESPACE    Kubernetes namespace [default: tli-analytics]
    -KubeConfig PATH        Path to kubeconfig file
    -ComposeFile FILE       Docker compose file [default: docker-compose.yml]
    -BuildArgs ARGS         Additional Docker build arguments
    -PushImage              Push image to registry
    -DeployK8s              Deploy to Kubernetes
    -SkipBuild              Skip Docker build
    -SkipTests              Skip running tests
    -Verbose                Enable verbose output
    -Help                   Show this help message

EXAMPLES:
    # Development deployment with Docker Compose
    .\deploy.ps1 -Environment development

    # Production deployment to Kubernetes
    .\deploy.ps1 -Environment production -Version 1.0.0 -Registry registry.tli-platform.com -PushImage -DeployK8s

    # Staging deployment with custom namespace
    .\deploy.ps1 -Environment staging -Namespace tli-analytics-staging -DeployK8s

    # Build and push image only
    .\deploy.ps1 -Version 1.2.3 -Registry registry.tli-platform.com -PushImage -SkipBuild
"@
}

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

# Configure environment
function Set-EnvironmentConfig {
    Write-Info "Configuring environment: $Environment"
    
    switch ($Environment) {
        "development" { $script:ComposeFile = "docker-compose.yml" }
        "staging" { $script:ComposeFile = "docker-compose.staging.yml" }
        "production" { $script:ComposeFile = "docker-compose.prod.yml" }
    }
    
    if ($Registry) {
        $script:DockerImage = "$Registry/$ServiceName"
    }
    
    Write-Info "Using Docker image: $DockerImage`:$Version"
    Write-Info "Using compose file: $ComposeFile"
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    
    # Check Docker Compose
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose is not installed or not in PATH"
        exit 1
    }
    
    # Check kubectl if deploying to Kubernetes
    if ($DeployK8s) {
        if (-not (Get-Command kubectl -ErrorAction SilentlyContinue)) {
            Write-Error "kubectl is not installed or not in PATH"
            exit 1
        }
        
        # Set kubeconfig if provided
        if ($KubeConfig) {
            $env:KUBECONFIG = $KubeConfig
            Write-Info "Using kubeconfig: $KubeConfig"
        }
        
        # Test kubectl connectivity
        try {
            kubectl cluster-info | Out-Null
        }
        catch {
            Write-Error "Cannot connect to Kubernetes cluster"
            exit 1
        }
    }
    
    # Check .NET SDK for building
    if (-not $SkipBuild) {
        if (-not (Get-Command dotnet -ErrorAction SilentlyContinue)) {
            Write-Error ".NET SDK is not installed or not in PATH"
            exit 1
        }
    }
    
    Write-Success "Prerequisites check passed"
}

# Run tests
function Invoke-Tests {
    if ($SkipTests) {
        Write-Warning "Skipping tests"
        return
    }
    
    Write-Info "Running tests..."
    Set-Location $ProjectRoot
    
    # Unit tests
    Write-Info "Running unit tests..."
    dotnet test AnalyticsBIService.Tests/UnitTests/ --configuration Release --logger "console;verbosity=minimal"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Unit tests failed"
        exit 1
    }
    
    # Integration tests
    Write-Info "Running integration tests..."
    dotnet test AnalyticsBIService.Tests/IntegrationTests/ --configuration Release --logger "console;verbosity=minimal"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Integration tests failed"
        exit 1
    }
    
    Write-Success "All tests passed"
}

# Build Docker image
function Build-DockerImage {
    if ($SkipBuild) {
        Write-Warning "Skipping Docker build"
        return
    }
    
    Write-Info "Building Docker image..."
    Set-Location $ProjectRoot
    
    $buildCmd = "docker build -t $DockerImage`:$Version"
    
    if ($BuildArgs) {
        $buildCmd += " $BuildArgs"
    }
    
    $buildCmd += " ."
    
    if ($Verbose) {
        Write-Info "Build command: $buildCmd"
    }
    
    Invoke-Expression $buildCmd
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker build failed"
        exit 1
    }
    
    # Tag as latest if not already
    if ($Version -ne "latest") {
        docker tag "$DockerImage`:$Version" "$DockerImage`:latest"
    }
    
    Write-Success "Docker image built successfully"
}

# Push Docker image
function Push-DockerImage {
    if (-not $PushImage) {
        Write-Info "Skipping image push"
        return
    }
    
    if (-not $Registry) {
        Write-Error "Registry not specified. Use -Registry parameter"
        exit 1
    }
    
    Write-Info "Pushing Docker image to registry..."
    
    docker push "$DockerImage`:$Version"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker push failed"
        exit 1
    }
    
    if ($Version -ne "latest") {
        docker push "$DockerImage`:latest"
    }
    
    Write-Success "Docker image pushed successfully"
}

# Deploy with Docker Compose
function Deploy-Compose {
    if ($DeployK8s) {
        Write-Info "Skipping Docker Compose deployment (Kubernetes deployment enabled)"
        return
    }
    
    Write-Info "Deploying with Docker Compose..."
    Set-Location $ProjectRoot
    
    if (-not (Test-Path $ComposeFile)) {
        Write-Error "Docker Compose file not found: $ComposeFile"
        exit 1
    }
    
    # Set environment variables
    $env:VERSION = $Version
    $env:DOCKER_IMAGE = $DockerImage
    
    # Deploy
    docker-compose -f $ComposeFile down --remove-orphans
    docker-compose -f $ComposeFile up -d
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker Compose deployment failed"
        exit 1
    }
    
    Write-Success "Docker Compose deployment completed"
}

# Deploy to Kubernetes
function Deploy-Kubernetes {
    if (-not $DeployK8s) {
        Write-Info "Skipping Kubernetes deployment"
        return
    }
    
    Write-Info "Deploying to Kubernetes..."
    Set-Location $ProjectRoot
    
    # Create namespace if it doesn't exist
    kubectl create namespace $Namespace --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply Kubernetes manifests
    Write-Info "Applying Kubernetes manifests..."
    
    # Apply in order
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/timescaledb-deployment.yaml
    kubectl apply -f k8s/analytics-api-deployment.yaml
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Kubernetes deployment failed"
        exit 1
    }
    
    # Wait for deployment to be ready
    Write-Info "Waiting for deployment to be ready..."
    kubectl rollout status deployment/analytics-api -n $Namespace --timeout=300s
    
    # Show deployment status
    kubectl get pods -n $Namespace
    kubectl get services -n $Namespace
    
    Write-Success "Kubernetes deployment completed"
}

# Health check
function Test-Health {
    Write-Info "Performing health check..."
    
    $healthUrl = ""
    
    if ($DeployK8s) {
        # Get service endpoint
        $serviceIp = kubectl get service analytics-api-service -n $Namespace -o jsonpath='{.spec.clusterIP}'
        $healthUrl = "http://$serviceIp/api/analytics/health"
    }
    else {
        # Docker Compose deployment
        $healthUrl = "http://localhost:5004/api/analytics/health"
    }
    
    Write-Info "Health check URL: $healthUrl"
    
    # Wait for service to be ready
    $maxAttempts = 30
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Success "Health check passed"
                return $true
            }
        }
        catch {
            # Continue to retry
        }
        
        Write-Info "Health check attempt $attempt/$maxAttempts failed, retrying in 10 seconds..."
        Start-Sleep -Seconds 10
        $attempt++
    }
    
    Write-Error "Health check failed after $maxAttempts attempts"
    return $false
}

# Main deployment function
function Start-Deployment {
    Write-Info "Starting Analytics & BI Service deployment..."
    Write-Info "Environment: $Environment"
    Write-Info "Version: $Version"
    Write-Info "Timestamp: $(Get-Date)"
    
    try {
        # Validate and configure
        Set-EnvironmentConfig
        Test-Prerequisites
        
        # Build and test
        Invoke-Tests
        Build-DockerImage
        Push-DockerImage
        
        # Deploy
        Deploy-Compose
        Deploy-Kubernetes
        
        # Verify deployment
        if (Test-Health) {
            Write-Success "Deployment completed successfully!"
            Write-Info "Service is now available and healthy"
        }
        else {
            Write-Error "Deployment completed but health check failed"
            exit 1
        }
    }
    catch {
        Write-Error "Deployment failed: $($_.Exception.Message)"
        exit 1
    }
}

# Run main deployment
Start-Deployment
