using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

public class TripLegStop : BaseEntity
{
    public Guid TripLegId { get; private set; }
    public TripStopType StopType { get; private set; }
    public int SequenceNumber { get; private set; }
    public Location Location { get; private set; } = null!;
    public string? ContactName { get; private set; }
    public string? ContactPhone { get; private set; }
    public DateTime? ScheduledArrival { get; private set; }
    public DateTime? ScheduledDeparture { get; private set; }
    public DateTime? ActualArrival { get; private set; }
    public DateTime? ActualDeparture { get; private set; }
    public TripStopStatus Status { get; private set; }
    public string? Instructions { get; private set; }
    public string? Notes { get; private set; }
    public bool IsRequired { get; private set; }

    // Navigation properties
    public TripLeg TripLeg { get; private set; } = null!;

    private readonly List<ProofOfDelivery> _proofOfDeliveries = new();
    public IReadOnlyCollection<ProofOfDelivery> ProofOfDeliveries => _proofOfDeliveries.AsReadOnly();

    private TripLegStop() { } // EF Constructor

    public TripLegStop(
        Guid tripLegId,
        TripStopType stopType,
        int sequenceNumber,
        Location location,
        bool isRequired = true,
        string? contactName = null,
        string? contactPhone = null,
        DateTime? scheduledArrival = null,
        DateTime? scheduledDeparture = null,
        string? instructions = null)
    {
        TripLegId = tripLegId;
        StopType = stopType;
        SequenceNumber = sequenceNumber;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        IsRequired = isRequired;
        ContactName = contactName;
        ContactPhone = contactPhone;
        ScheduledArrival = scheduledArrival;
        ScheduledDeparture = scheduledDeparture;
        Status = TripStopStatus.Pending;
        Instructions = instructions;
    }

    public void MarkArrived()
    {
        if (Status != TripStopStatus.Pending)
            throw new InvalidOperationException("Can only mark pending stops as arrived");

        Status = TripStopStatus.Arrived;
        ActualArrival = DateTime.UtcNow;
    }

    public void StartProcessing()
    {
        if (Status != TripStopStatus.Arrived)
            throw new InvalidOperationException("Can only start processing arrived stops");

        Status = TripStopStatus.InProgress;
    }

    public void Complete(string? notes = null)
    {
        if (Status != TripStopStatus.InProgress && Status != TripStopStatus.Arrived)
            throw new InvalidOperationException("Can only complete in-progress or arrived stops");

        Status = TripStopStatus.Completed;
        ActualDeparture = DateTime.UtcNow;
        Notes = notes;
    }

    public void Skip(string reason)
    {
        if (IsRequired)
            throw new InvalidOperationException("Cannot skip required stops");

        if (Status == TripStopStatus.Completed)
            throw new InvalidOperationException("Cannot skip completed stops");

        Status = TripStopStatus.Skipped;
        Notes = $"Skipped: {reason}";
    }

    public void Fail(string reason)
    {
        Status = TripStopStatus.Failed;
        Notes = $"Failed: {reason}";
    }

    public void AddProofOfDelivery(ProofOfDelivery pod)
    {
        _proofOfDeliveries.Add(pod);
    }

    public bool IsDelayed()
    {
        if (ScheduledArrival.HasValue)
        {
            if (ActualArrival.HasValue)
                return ActualArrival.Value > ScheduledArrival.Value;

            return DateTime.UtcNow > ScheduledArrival.Value && Status == TripStopStatus.Pending;
        }

        return false;
    }
}

public class TripLegLocationUpdate : BaseEntity
{
    public Guid TripLegId { get; private set; }
    public Location Location { get; private set; } = null!;
    public DateTime Timestamp { get; private set; }
    public LocationUpdateSource Source { get; private set; }
    public double? Speed { get; private set; }
    public double? Heading { get; private set; }
    public double? Accuracy { get; private set; }
    public double? Altitude { get; private set; }
    public bool IsSignificantUpdate { get; private set; }
    public double? DistanceFromPreviousKm { get; private set; }
    public GeofenceStatus GeofenceStatus { get; private set; }
    public string? GeofenceZoneId { get; private set; }
    public Dictionary<string, object>? AdditionalData { get; private set; }

    // Navigation properties
    public TripLeg TripLeg { get; private set; } = null!;

    private TripLegLocationUpdate() { } // EF Constructor

    public TripLegLocationUpdate(
        Guid tripLegId,
        Location location,
        LocationUpdateSource source = LocationUpdateSource.GPS,
        double? speed = null,
        double? heading = null,
        double? accuracy = null,
        double? altitude = null,
        bool isSignificantUpdate = false,
        double? distanceFromPreviousKm = null,
        GeofenceStatus geofenceStatus = GeofenceStatus.Unknown,
        string? geofenceZoneId = null,
        Dictionary<string, object>? additionalData = null)
    {
        TripLegId = tripLegId;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        Timestamp = DateTime.UtcNow;
        Source = source;
        Speed = speed;
        Heading = heading;
        Accuracy = accuracy;
        Altitude = altitude;
        IsSignificantUpdate = isSignificantUpdate;
        DistanceFromPreviousKm = distanceFromPreviousKm;
        GeofenceStatus = geofenceStatus;
        GeofenceZoneId = geofenceZoneId;
        AdditionalData = additionalData;
    }

    public void MarkAsSignificant()
    {
        IsSignificantUpdate = true;
    }

    public void UpdateGeofenceStatus(GeofenceStatus status, string? zoneId = null)
    {
        GeofenceStatus = status;
        GeofenceZoneId = zoneId;
    }

    public void SetDistanceFromPrevious(double distanceKm)
    {
        DistanceFromPreviousKm = distanceKm;
    }

    public bool IsWithinAccuracyThreshold(double thresholdMeters = 10.0)
    {
        return Accuracy.HasValue && Accuracy.Value <= thresholdMeters;
    }

    public bool IsMoving(double speedThresholdKmh = 5.0)
    {
        return Speed.HasValue && Speed.Value >= speedThresholdKmh;
    }

    public double CalculateDistanceFromLocation(Location targetLocation)
    {
        return Location.CalculateDistanceKm(targetLocation);
    }
}

public class TripLegException : BaseEntity
{
    public Guid TripLegId { get; private set; }
    public ExceptionType Type { get; private set; }
    public ExceptionSeverity Severity { get; private set; }
    public string Title { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public DateTime OccurredAt { get; private set; }
    public Location? Location { get; private set; }
    public Guid? ReportedBy { get; private set; }
    public bool IsResolved { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public string? Resolution { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }

    // Navigation properties
    public TripLeg TripLeg { get; private set; } = null!;

    private TripLegException() { } // EF Constructor

    public TripLegException(
        Guid tripLegId,
        ExceptionType type,
        ExceptionSeverity severity,
        string title,
        string description,
        Location? location = null,
        Guid? reportedBy = null,
        Dictionary<string, object>? metadata = null)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        TripLegId = tripLegId;
        Type = type;
        Severity = severity;
        Title = title;
        Description = description;
        OccurredAt = DateTime.UtcNow;
        Location = location;
        ReportedBy = reportedBy;
        IsResolved = false;
        Metadata = metadata;
    }

    public void Resolve(string resolution, Guid? resolvedBy = null)
    {
        if (IsResolved)
            throw new InvalidOperationException("Exception is already resolved");

        if (string.IsNullOrWhiteSpace(resolution))
            throw new ArgumentException("Resolution cannot be empty", nameof(resolution));

        IsResolved = true;
        ResolvedAt = DateTime.UtcNow;
        Resolution = resolution;
        ResolvedBy = resolvedBy;
    }

    public void UpdateMetadata(Dictionary<string, object> metadata)
    {
        Metadata = metadata;
    }
}

public class TripLegDocument : BaseEntity
{
    public Guid TripLegId { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string ContentType { get; private set; } = string.Empty;
    public long FileSize { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public string? Description { get; private set; }
    public DateTime UploadedAt { get; private set; }
    public Guid UploadedBy { get; private set; }
    public string FilePath { get; private set; } = string.Empty;
    public string? FileHash { get; private set; }
    public bool IsRequired { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    // Navigation properties
    public TripLeg TripLeg { get; private set; } = null!;

    private TripLegDocument() { } // EF Constructor

    public TripLegDocument(
        Guid tripLegId,
        string fileName,
        string contentType,
        long fileSize,
        DocumentType documentType,
        string filePath,
        Guid uploadedBy,
        string? description = null,
        string? fileHash = null,
        bool isRequired = false,
        DateTime? expiresAt = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be empty", nameof(filePath));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be greater than zero", nameof(fileSize));

        TripLegId = tripLegId;
        FileName = fileName;
        ContentType = contentType;
        FileSize = fileSize;
        DocumentType = documentType;
        FilePath = filePath;
        UploadedBy = uploadedBy;
        Description = description;
        FileHash = fileHash;
        IsRequired = isRequired;
        ExpiresAt = expiresAt;
        UploadedAt = DateTime.UtcNow;
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public void UpdateDescription(string? description)
    {
        Description = description;
    }

    public void SetExpiration(DateTime? expiresAt)
    {
        ExpiresAt = expiresAt;
    }
}


