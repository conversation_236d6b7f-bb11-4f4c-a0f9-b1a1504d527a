# TLI Microservices Maintenance Guide

## Overview

This guide provides comprehensive maintenance procedures for the TLI microservices deployment, including backup strategies, update procedures, performance optimization, and preventive maintenance tasks.

## Maintenance Schedule

### Daily Tasks (Automated)
- Health checks for all services
- Log rotation and cleanup
- Security monitoring
- Backup verification

### Weekly Tasks
- System updates and patches
- Performance analysis
- Log analysis and alerting review
- Capacity planning review

### Monthly Tasks
- Full system backup
- Security audit
- Certificate renewal check
- Performance optimization
- Dependency updates

### Quarterly Tasks
- Disaster recovery testing
- Security penetration testing
- Architecture review
- Capacity planning
- Documentation updates

## Step 1: Backup and Recovery

### 1.1 Database Backup Strategy

#### Automated Database Backups
```bash
# Enhanced backup script with retention and compression
cat > /opt/tli/backup-databases.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/tli/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30
LOG_FILE="/opt/tli/logs/backup.log"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database list
DATABASES=(
  "TLI_Identity"
  "TLI_UserManagement"
  "TLI_SubscriptionManagement"
  "TLI_OrderManagement"
  "TLI_TripManagement"
  "TLI_NetworkFleetManagement"
  "TLI_FinancialPayment"
  "TLI_CommunicationNotification"
  "TLI_AnalyticsBIService"
  "TLI_DataStorage"
  "TLI_AuditCompliance"
  "TLI_MonitoringObservability"
  "TLI_MobileWorkflow"
)

echo "$(date): Starting database backup" >> $LOG_FILE

# Backup each database
for db in "${DATABASES[@]}"; do
  echo "$(date): Backing up $db..." >> $LOG_FILE
  
  # Create backup with custom format for faster restore
  docker exec tli-postgres pg_dump -U postgres -Fc "$db" | gzip > "$BACKUP_DIR/${db}_${DATE}.dump.gz"
  
  if [ $? -eq 0 ]; then
    echo "$(date): Successfully backed up $db" >> $LOG_FILE
  else
    echo "$(date): ERROR: Failed to backup $db" >> $LOG_FILE
  fi
done

# Backup global objects (users, roles, etc.)
echo "$(date): Backing up global objects..." >> $LOG_FILE
docker exec tli-postgres pg_dumpall -U postgres --globals-only | gzip > "$BACKUP_DIR/globals_${DATE}.sql.gz"

# Clean up old backups
echo "$(date): Cleaning up backups older than $RETENTION_DAYS days..." >> $LOG_FILE
find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete

# Verify backup integrity
echo "$(date): Verifying backup integrity..." >> $LOG_FILE
for backup in $(find $BACKUP_DIR -name "*_${DATE}.dump.gz"); do
  if gunzip -t "$backup" 2>/dev/null; then
    echo "$(date): Backup $backup is valid" >> $LOG_FILE
  else
    echo "$(date): ERROR: Backup $backup is corrupted" >> $LOG_FILE
  fi
done

echo "$(date): Database backup completed" >> $LOG_FILE
EOF

chmod +x /opt/tli/backup-databases.sh

# Schedule daily backups at 2 AM
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/tli/backup-databases.sh") | crontab -
```

#### Database Restore Procedures
```bash
# Create database restore script
cat > /opt/tli/restore-database.sh << 'EOF'
#!/bin/bash

if [ $# -ne 2 ]; then
  echo "Usage: $0 <database_name> <backup_file>"
  echo "Example: $0 TLI_Identity /opt/tli/backups/postgres/TLI_Identity_20241201_020000.dump.gz"
  exit 1
fi

DATABASE_NAME="$1"
BACKUP_FILE="$2"
LOG_FILE="/opt/tli/logs/restore.log"

echo "$(date): Starting restore of $DATABASE_NAME from $BACKUP_FILE" >> $LOG_FILE

# Verify backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
  echo "ERROR: Backup file $BACKUP_FILE not found"
  exit 1
fi

# Stop applications that use this database
echo "Stopping applications..."
/opt/tli/manage-applications.sh stop

# Drop existing database (CAUTION!)
echo "$(date): Dropping existing database $DATABASE_NAME" >> $LOG_FILE
docker exec tli-postgres psql -U postgres -c "DROP DATABASE IF EXISTS \"$DATABASE_NAME\";"

# Create new database
echo "$(date): Creating new database $DATABASE_NAME" >> $LOG_FILE
docker exec tli-postgres psql -U postgres -c "CREATE DATABASE \"$DATABASE_NAME\";"

# Restore from backup
echo "$(date): Restoring database from backup" >> $LOG_FILE
gunzip -c "$BACKUP_FILE" | docker exec -i tli-postgres pg_restore -U postgres -d "$DATABASE_NAME" --verbose

if [ $? -eq 0 ]; then
  echo "$(date): Successfully restored $DATABASE_NAME" >> $LOG_FILE
  echo "Database $DATABASE_NAME restored successfully"
else
  echo "$(date): ERROR: Failed to restore $DATABASE_NAME" >> $LOG_FILE
  echo "ERROR: Failed to restore database"
  exit 1
fi

# Restart applications
echo "Starting applications..."
/opt/tli/manage-applications.sh start

echo "$(date): Restore completed" >> $LOG_FILE
EOF

chmod +x /opt/tli/restore-database.sh
```

### 1.2 Configuration Backup

#### System Configuration Backup
```bash
# Create configuration backup script
cat > /opt/tli/backup-configurations.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/tli/backups/config"
DATE=$(date +%Y%m%d_%H%M%S)
ARCHIVE_NAME="tli_config_backup_${DATE}.tar.gz"

mkdir -p $BACKUP_DIR

echo "Creating configuration backup..."

# Create temporary directory for backup
TEMP_DIR="/tmp/tli_config_backup_$$"
mkdir -p $TEMP_DIR

# Copy configuration files
cp -r /opt/tli/config $TEMP_DIR/
cp -r /etc/nginx/sites-available $TEMP_DIR/nginx_sites
cp -r /etc/ufw $TEMP_DIR/ufw_config
cp /etc/docker/daemon.json $TEMP_DIR/ 2>/dev/null || true
cp -r /etc/systemd/system/tli-* $TEMP_DIR/systemd_services 2>/dev/null || true

# Copy environment files
cp /opt/tli/config/tli-environment.env $TEMP_DIR/ 2>/dev/null || true
cp /opt/tli/config/tli-secrets.env $TEMP_DIR/ 2>/dev/null || true

# Create archive
tar -czf "$BACKUP_DIR/$ARCHIVE_NAME" -C $TEMP_DIR .

# Clean up
rm -rf $TEMP_DIR

# Remove old config backups (keep 10)
ls -t $BACKUP_DIR/tli_config_backup_*.tar.gz | tail -n +11 | xargs rm -f

echo "Configuration backup created: $BACKUP_DIR/$ARCHIVE_NAME"
EOF

chmod +x /opt/tli/backup-configurations.sh

# Schedule weekly configuration backups
(crontab -l 2>/dev/null; echo "0 3 * * 0 /opt/tli/backup-configurations.sh") | crontab -
```

## Step 2: System Updates and Patches

### 2.1 Operating System Updates

#### Automated Security Updates
```bash
# Configure automatic security updates
sudo apt install -y unattended-upgrades

# Configure unattended upgrades
sudo tee /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::Package-Blacklist {
    "docker-ce";
    "docker-ce-cli";
    "containerd.io";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";

Unattended-Upgrade::Mail "<EMAIL>";
Unattended-Upgrade::MailOnlyOnError "true";
EOF

# Enable automatic updates
sudo systemctl enable unattended-upgrades
sudo systemctl start unattended-upgrades
```

#### Manual Update Procedure
```bash
# Create system update script
cat > /opt/tli/update-system.sh << 'EOF'
#!/bin/bash

LOG_FILE="/opt/tli/logs/system-update.log"

echo "$(date): Starting system update" >> $LOG_FILE

# Update package lists
echo "Updating package lists..."
sudo apt update >> $LOG_FILE 2>&1

# Show available updates
echo "Available updates:"
apt list --upgradable

# Confirm before proceeding
read -p "Proceed with updates? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
  echo "Update cancelled"
  exit 0
fi

# Stop TLI services
echo "Stopping TLI services..."
/opt/tli/manage-applications.sh stop
/opt/tli/manage-infrastructure.sh stop

# Perform updates
echo "$(date): Installing updates" >> $LOG_FILE
sudo apt upgrade -y >> $LOG_FILE 2>&1

# Clean up
sudo apt autoremove -y >> $LOG_FILE 2>&1
sudo apt autoclean >> $LOG_FILE 2>&1

# Check if reboot is required
if [ -f /var/run/reboot-required ]; then
  echo "$(date): Reboot required after updates" >> $LOG_FILE
  echo "REBOOT REQUIRED - Please reboot the system"
else
  # Restart services
  echo "Restarting TLI services..."
  /opt/tli/manage-infrastructure.sh start
  sleep 60
  /opt/tli/manage-applications.sh start
fi

echo "$(date): System update completed" >> $LOG_FILE
EOF

chmod +x /opt/tli/update-system.sh
```

### 2.2 Docker and Container Updates

#### Container Image Updates
```bash
# Create container update script
cat > /opt/tli/update-containers.sh << 'EOF'
#!/bin/bash

LOG_FILE="/opt/tli/logs/container-update.log"

echo "$(date): Starting container update" >> $LOG_FILE

# Infrastructure containers
INFRASTRUCTURE_IMAGES=(
  "postgres:15"
  "redis:7-alpine"
  "rabbitmq:3.12-management"
  "prom/prometheus:latest"
  "grafana/grafana:latest"
  "grafana/loki:2.9.0"
  "grafana/promtail:2.9.0"
)

# Pull latest images
echo "Pulling latest infrastructure images..."
for image in "${INFRASTRUCTURE_IMAGES[@]}"; do
  echo "$(date): Pulling $image" >> $LOG_FILE
  docker pull $image >> $LOG_FILE 2>&1
done

# Update infrastructure stack
echo "Updating infrastructure stack..."
/opt/tli/manage-infrastructure.sh stop
/opt/tli/manage-infrastructure.sh start

# Wait for infrastructure to be ready
sleep 120

# Rebuild and update application containers
echo "Rebuilding application containers..."
cd /opt/tli/apps/TLIMicroservices
git pull >> $LOG_FILE 2>&1
/opt/tli/build-services.sh >> $LOG_FILE 2>&1

# Update application stack
echo "Updating application stack..."
/opt/tli/manage-applications.sh stop
/opt/tli/manage-applications.sh start

# Verify all services are running
sleep 60
/opt/tli/health-check.sh >> $LOG_FILE 2>&1

echo "$(date): Container update completed" >> $LOG_FILE
EOF

chmod +x /opt/tli/update-containers.sh
```

## Step 3: Performance Optimization

### 3.1 Database Performance Tuning

#### PostgreSQL Optimization
```bash
# Create database optimization script
cat > /opt/tli/optimize-database.sh << 'EOF'
#!/bin/bash

echo "=== PostgreSQL Performance Optimization ==="

# Analyze database statistics
echo "Updating database statistics..."
for db in TLI_Identity TLI_UserManagement TLI_SubscriptionManagement TLI_OrderManagement TLI_TripManagement TLI_NetworkFleetManagement TLI_FinancialPayment TLI_CommunicationNotification TLI_AnalyticsBIService TLI_DataStorage TLI_AuditCompliance TLI_MonitoringObservability TLI_MobileWorkflow; do
  docker exec tli-postgres psql -U postgres -d "$db" -c "ANALYZE;"
done

# Vacuum databases
echo "Vacuuming databases..."
for db in TLI_Identity TLI_UserManagement TLI_SubscriptionManagement TLI_OrderManagement TLI_TripManagement TLI_NetworkFleetManagement TLI_FinancialPayment TLI_CommunicationNotification TLI_AnalyticsBIService TLI_DataStorage TLI_AuditCompliance TLI_MonitoringObservability TLI_MobileWorkflow; do
  docker exec tli-postgres psql -U postgres -d "$db" -c "VACUUM ANALYZE;"
done

# Check for slow queries
echo "Checking for slow queries..."
docker exec tli-postgres psql -U postgres -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;"

# Check database sizes
echo "Database sizes:"
docker exec tli-postgres psql -U postgres -c "
SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
FROM pg_database
WHERE datname LIKE 'TLI_%'
ORDER BY pg_database_size(datname) DESC;"

echo "Database optimization completed"
EOF

chmod +x /opt/tli/optimize-database.sh

# Schedule monthly database optimization
(crontab -l 2>/dev/null; echo "0 1 1 * * /opt/tli/optimize-database.sh >> /opt/tli/logs/db-optimization.log 2>&1") | crontab -
```

### 3.2 System Performance Monitoring

#### Performance Analysis Script
```bash
# Create performance analysis script
cat > /opt/tli/analyze-performance.sh << 'EOF'
#!/bin/bash

echo "=== TLI System Performance Analysis ==="
echo "Date: $(date)"
echo

# CPU Usage
echo "1. CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print "  CPU Usage: " $2 " user, " $4 " system, " $8 " idle"}'

# Memory Usage
echo
echo "2. Memory Usage:"
free -h | awk 'NR==2{printf "  Memory Usage: %s/%s (%.2f%%)\n", $3,$2,$3*100/$2 }'

# Disk Usage
echo
echo "3. Disk Usage:"
df -h | grep -E "/$|/opt" | awk '{printf "  %s: %s/%s (%s)\n", $6, $3, $2, $5}'

# Docker Container Resources
echo
echo "4. Container Resource Usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Database Performance
echo
echo "5. Database Performance:"
echo "  Active Connections:"
docker exec tli-postgres psql -U postgres -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';" -t

echo "  Database Sizes:"
docker exec tli-postgres psql -U postgres -c "SELECT datname, pg_size_pretty(pg_database_size(datname)) FROM pg_database WHERE datname LIKE 'TLI_%';" -t

# Redis Performance
echo
echo "6. Redis Performance:"
echo "  Memory Usage:"
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" info memory | grep used_memory_human

echo "  Connected Clients:"
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" info clients | grep connected_clients

# Network Performance
echo
echo "7. Network Performance:"
echo "  Network Connections:"
netstat -tuln | wc -l | awk '{print "  Total connections: " $1}'

echo
echo "=== Performance Analysis Complete ==="
EOF

chmod +x /opt/tli/analyze-performance.sh
```

## Step 4: Log Management

### 4.1 Log Rotation and Cleanup

#### Automated Log Management
```bash
# Create log management script
cat > /opt/tli/manage-logs.sh << 'EOF'
#!/bin/bash

LOG_DIR="/opt/tli/logs"
RETENTION_DAYS=30

echo "=== TLI Log Management ==="
echo "Date: $(date)"

# Rotate large log files
echo "Rotating large log files..."
find $LOG_DIR -name "*.log" -size +100M -exec gzip {} \;

# Clean up old log files
echo "Cleaning up logs older than $RETENTION_DAYS days..."
find $LOG_DIR -name "*.log" -mtime +$RETENTION_DAYS -delete
find $LOG_DIR -name "*.log.gz" -mtime +$RETENTION_DAYS -delete

# Clean up Docker logs
echo "Cleaning up Docker logs..."
docker system prune -f --filter "until=720h"

# Analyze log sizes
echo "Current log directory sizes:"
du -sh $LOG_DIR/*/ 2>/dev/null | sort -hr

# Check for errors in recent logs
echo "Recent errors in application logs:"
find $LOG_DIR -name "*.log" -mtime -1 -exec grep -l "ERROR\|FATAL\|Exception" {} \; | head -5

echo "Log management completed"
EOF

chmod +x /opt/tli/manage-logs.sh

# Schedule daily log management
(crontab -l 2>/dev/null; echo "0 4 * * * /opt/tli/manage-logs.sh >> /opt/tli/logs/log-management.log 2>&1") | crontab -
```

## Step 5: Security Maintenance

### 5.1 Security Updates and Patches

#### Security Maintenance Script
```bash
# Create security maintenance script
cat > /opt/tli/security-maintenance.sh << 'EOF'
#!/bin/bash

LOG_FILE="/opt/tli/logs/security-maintenance.log"

echo "$(date): Starting security maintenance" >> $LOG_FILE

# Update security patches
echo "Checking for security updates..."
sudo apt update >> $LOG_FILE 2>&1
security_updates=$(apt list --upgradable 2>/dev/null | grep -i security | wc -l)

if [ $security_updates -gt 0 ]; then
  echo "$(date): Found $security_updates security updates" >> $LOG_FILE
  sudo apt upgrade -y >> $LOG_FILE 2>&1
fi

# Rotate secrets (manual process)
echo "Checking secret rotation schedule..."
last_rotation=$(stat -c %Y /opt/tli/config/tli-secrets.env 2>/dev/null || echo 0)
current_time=$(date +%s)
days_since_rotation=$(( (current_time - last_rotation) / 86400 ))

if [ $days_since_rotation -gt 90 ]; then
  echo "$(date): WARNING: Secrets are $days_since_rotation days old - rotation recommended" >> $LOG_FILE
fi

# Check SSL certificate expiry
if [ -f "/opt/tli/config/ssl/tli.crt" ]; then
  expiry_date=$(openssl x509 -enddate -noout -in /opt/tli/config/ssl/tli.crt | cut -d= -f2)
  expiry_epoch=$(date -d "$expiry_date" +%s)
  days_until_expiry=$(( (expiry_epoch - current_time) / 86400 ))
  
  if [ $days_until_expiry -lt 30 ]; then
    echo "$(date): WARNING: SSL certificate expires in $days_until_expiry days" >> $LOG_FILE
  fi
fi

# Run security scan
/opt/tli/security-tests.sh >> $LOG_FILE 2>&1

echo "$(date): Security maintenance completed" >> $LOG_FILE
EOF

chmod +x /opt/tli/security-maintenance.sh

# Schedule weekly security maintenance
(crontab -l 2>/dev/null; echo "0 5 * * 1 /opt/tli/security-maintenance.sh") | crontab -
```

## Step 6: Disaster Recovery

### 6.1 Disaster Recovery Plan

#### Complete System Recovery Script
```bash
# Create disaster recovery script
cat > /opt/tli/disaster-recovery.sh << 'EOF'
#!/bin/bash

if [ $# -ne 1 ]; then
  echo "Usage: $0 <backup_date>"
  echo "Example: $0 20241201"
  exit 1
fi

BACKUP_DATE="$1"
BACKUP_DIR="/opt/tli/backups"
LOG_FILE="/opt/tli/logs/disaster-recovery.log"

echo "$(date): Starting disaster recovery for backup date $BACKUP_DATE" >> $LOG_FILE

# Stop all services
echo "Stopping all TLI services..."
/opt/tli/manage-applications.sh stop
/opt/tli/manage-infrastructure.sh stop

# Restore configuration
echo "Restoring configuration..."
config_backup=$(find $BACKUP_DIR/config -name "*${BACKUP_DATE}*.tar.gz" | head -1)
if [ -n "$config_backup" ]; then
  tar -xzf "$config_backup" -C /opt/tli/config/
  echo "$(date): Configuration restored from $config_backup" >> $LOG_FILE
fi

# Start infrastructure
echo "Starting infrastructure services..."
/opt/tli/manage-infrastructure.sh start
sleep 120

# Restore databases
echo "Restoring databases..."
for db in TLI_Identity TLI_UserManagement TLI_SubscriptionManagement TLI_OrderManagement TLI_TripManagement TLI_NetworkFleetManagement TLI_FinancialPayment TLI_CommunicationNotification TLI_AnalyticsBIService TLI_DataStorage TLI_AuditCompliance TLI_MonitoringObservability TLI_MobileWorkflow; do
  backup_file=$(find $BACKUP_DIR/postgres -name "${db}_${BACKUP_DATE}*.dump.gz" | head -1)
  if [ -n "$backup_file" ]; then
    /opt/tli/restore-database.sh "$db" "$backup_file"
    echo "$(date): Restored $db from $backup_file" >> $LOG_FILE
  fi
done

# Start applications
echo "Starting application services..."
/opt/tli/manage-applications.sh start
sleep 60

# Verify recovery
echo "Verifying system recovery..."
/opt/tli/health-check.sh >> $LOG_FILE 2>&1

echo "$(date): Disaster recovery completed" >> $LOG_FILE
echo "Disaster recovery completed. Check logs for details."
EOF

chmod +x /opt/tli/disaster-recovery.sh
```

## Step 7: Maintenance Automation

### 7.1 Master Maintenance Script

#### Comprehensive Maintenance Automation
```bash
# Create master maintenance script
cat > /opt/tli/maintenance.sh << 'EOF'
#!/bin/bash

MAINTENANCE_TYPE="$1"

case "$MAINTENANCE_TYPE" in
  daily)
    echo "Running daily maintenance..."
    /opt/tli/health-check.sh
    /opt/tli/manage-logs.sh
    /opt/tli/security-monitor.sh
    ;;
  weekly)
    echo "Running weekly maintenance..."
    /opt/tli/backup-configurations.sh
    /opt/tli/security-maintenance.sh
    /opt/tli/analyze-performance.sh
    ;;
  monthly)
    echo "Running monthly maintenance..."
    /opt/tli/optimize-database.sh
    /opt/tli/update-containers.sh
    /opt/tli/compliance-check.sh
    ;;
  *)
    echo "Usage: $0 {daily|weekly|monthly}"
    exit 1
    ;;
esac

echo "Maintenance completed: $(date)"
EOF

chmod +x /opt/tli/maintenance.sh

# Schedule automated maintenance
(crontab -l 2>/dev/null; echo "0 6 * * * /opt/tli/maintenance.sh daily >> /opt/tli/logs/maintenance.log 2>&1") | crontab -
(crontab -l 2>/dev/null; echo "0 7 * * 0 /opt/tli/maintenance.sh weekly >> /opt/tli/logs/maintenance.log 2>&1") | crontab -
(crontab -l 2>/dev/null; echo "0 8 1 * * /opt/tli/maintenance.sh monthly >> /opt/tli/logs/maintenance.log 2>&1") | crontab -
```

## Maintenance Checklist

### Pre-Maintenance
- [ ] Notify stakeholders of maintenance window
- [ ] Create fresh backups
- [ ] Verify backup integrity
- [ ] Check system resources
- [ ] Review recent alerts and issues

### During Maintenance
- [ ] Follow maintenance procedures step by step
- [ ] Monitor system performance
- [ ] Document any issues encountered
- [ ] Verify each step completion

### Post-Maintenance
- [ ] Run comprehensive health checks
- [ ] Verify all services are operational
- [ ] Check monitoring dashboards
- [ ] Update maintenance logs
- [ ] Notify stakeholders of completion

## Emergency Procedures

### Service Recovery
```bash
# Quick service recovery
/opt/tli/manage-infrastructure.sh restart
/opt/tli/manage-applications.sh restart
/opt/tli/health-check.sh
```

### Database Recovery
```bash
# Emergency database restore
/opt/tli/restore-database.sh <database_name> <latest_backup>
```

### Complete System Recovery
```bash
# Full disaster recovery
/opt/tli/disaster-recovery.sh <backup_date>
```

## Contact Information

- **Primary Administrator**: <EMAIL>
- **Emergency Contact**: +91-XXXX-XXXX-XX
- **Technical Support**: <EMAIL>

## Next Steps

1. **Implement maintenance schedules** using the provided scripts
2. **Test backup and recovery procedures** regularly
3. **Monitor system performance** and optimize as needed
4. **Keep documentation updated** with any changes
5. **Train team members** on maintenance procedures
