using Microsoft.EntityFrameworkCore;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Infrastructure.Data;

namespace MonitoringObservability.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for spans
/// </summary>
public class SpanRepository : ISpanRepository
{
    private readonly MonitoringDbContext _context;

    public SpanRepository(MonitoringDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public async Task<Span?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Spans
            .Include(s => s.Events)
            .Include(s => s.Logs)
            .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<Span?> GetBySpanIdAsync(string spanId, CancellationToken cancellationToken = default)
    {
        return await _context.Spans
            .Include(s => s.Events)
            .Include(s => s.Logs)
            .FirstOrDefaultAsync(s => s.SpanId == spanId, cancellationToken);
    }

    public async Task<IEnumerable<Span>> GetByTraceIdAsync(string traceId, CancellationToken cancellationToken = default)
    {
        return await _context.Spans
            .Include(s => s.Events)
            .Include(s => s.Logs)
            .Where(s => s.TraceId == traceId)
            .OrderBy(s => s.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task AddAsync(Span span, CancellationToken cancellationToken = default)
    {
        await _context.Spans.AddAsync(span, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Span span, CancellationToken cancellationToken = default)
    {
        _context.Spans.Update(span);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var span = await _context.Spans.FindAsync(new object[] { id }, cancellationToken);
        if (span != null)
        {
            _context.Spans.Remove(span);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<IEnumerable<Span>> GetSpansByServiceAsync(
        string serviceName,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Spans
            .Where(s => s.ServiceName == serviceName);

        if (fromDate.HasValue)
        {
            query = query.Where(s => s.StartTime >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(s => s.StartTime <= toDate.Value);
        }

        return await query
            .OrderByDescending(s => s.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Span>> GetSpansByOperationAsync(
        string operationName,
        string? serviceName = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Spans
            .Where(s => s.OperationName == operationName);

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(s => s.ServiceName == serviceName);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(s => s.StartTime >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(s => s.StartTime <= toDate.Value);
        }

        return await query
            .OrderByDescending(s => s.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Span>> GetErrorSpansAsync(
        string? serviceName = null,
        TimeSpan? period = null,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Spans
            .Where(s => s.HasError);

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(s => s.ServiceName == serviceName);
        }

        if (period.HasValue)
        {
            var fromDate = DateTime.UtcNow.Subtract(period.Value);
            query = query.Where(s => s.StartTime >= fromDate);
        }

        return await query
            .OrderByDescending(s => s.StartTime)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Span>> GetSlowSpansAsync(
        TimeSpan threshold,
        string? serviceName = null,
        TimeSpan? period = null,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Spans
            .Where(s => s.Duration >= threshold);

        if (!string.IsNullOrEmpty(serviceName))
        {
            query = query.Where(s => s.ServiceName == serviceName);
        }

        if (period.HasValue)
        {
            var fromDate = DateTime.UtcNow.Subtract(period.Value);
            query = query.Where(s => s.StartTime >= fromDate);
        }

        return await query
            .OrderByDescending(s => s.Duration)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Span>> GetActiveSpansAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Spans
            .Where(s => s.IsActive)
            .OrderByDescending(s => s.StartTime)
            .ToListAsync(cancellationToken);
    }

    public async Task<double> GetAverageSpanDurationAsync(
        string serviceName,
        string? operationName = null,
        TimeSpan? period = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Spans
            .Where(s => s.ServiceName == serviceName && s.Duration.HasValue);

        if (!string.IsNullOrEmpty(operationName))
        {
            query = query.Where(s => s.OperationName == operationName);
        }

        if (period.HasValue)
        {
            var fromDate = DateTime.UtcNow.Subtract(period.Value);
            query = query.Where(s => s.StartTime >= fromDate);
        }

        var durations = await query
            .Select(s => s.Duration!.Value.TotalMilliseconds)
            .ToListAsync(cancellationToken);

        return durations.Any() ? durations.Average() : 0;
    }

    public async Task<Dictionary<string, double>> GetOperationLatencyPercentilesAsync(
        string serviceName,
        string operationName,
        TimeSpan period,
        double[] percentiles,
        CancellationToken cancellationToken = default)
    {
        var fromDate = DateTime.UtcNow.Subtract(period);
        
        var durations = await _context.Spans
            .Where(s => s.ServiceName == serviceName && 
                       s.OperationName == operationName &&
                       s.StartTime >= fromDate &&
                       s.Duration.HasValue)
            .Select(s => s.Duration!.Value.TotalMilliseconds)
            .OrderBy(d => d)
            .ToListAsync(cancellationToken);

        var result = new Dictionary<string, double>();
        
        if (durations.Any())
        {
            foreach (var percentile in percentiles)
            {
                var index = (int)Math.Ceiling(percentile / 100.0 * durations.Count) - 1;
                index = Math.Max(0, Math.Min(index, durations.Count - 1));
                result[$"p{percentile}"] = durations[index];
            }
        }

        return result;
    }

    public async Task<int> DeleteOldSpansAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
        
        var oldSpans = await _context.Spans
            .Where(s => s.StartTime < cutoffDate)
            .ToListAsync(cancellationToken);

        if (oldSpans.Any())
        {
            _context.Spans.RemoveRange(oldSpans);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return oldSpans.Count;
    }
}
