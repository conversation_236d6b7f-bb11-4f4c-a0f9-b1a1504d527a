using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Enums;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.ResumeSubscription
{
    public class ResumeSubscriptionCommandHandler : IRequestHandler<ResumeSubscriptionCommand, bool>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly IPaymentService _paymentService;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<ResumeSubscriptionCommandHandler> _logger;

        public ResumeSubscriptionCommandHandler(
            ISubscriptionRepository subscriptionRepository,
            IPaymentService paymentService,
            IMessageBroker messageBroker,
            ILogger<ResumeSubscriptionCommandHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _paymentService = paymentService;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<bool> Handle(ResumeSubscriptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Resuming subscription {SubscriptionId} for user {UserId}", 
                request.SubscriptionId, request.UserId);

            // Get subscription
            var subscription = await _subscriptionRepository.GetByIdAsync(request.SubscriptionId);
            if (subscription == null)
            {
                throw new SubscriptionDomainException("Subscription not found");
            }

            // Authorization check
            if (subscription.UserId != request.UserId)
            {
                throw new SubscriptionDomainException("You can only resume your own subscription");
            }

            // Validate subscription can be resumed
            if (subscription.Status != SubscriptionStatus.Suspended)
            {
                throw new SubscriptionDomainException("Only suspended subscriptions can be resumed");
            }

            // Process payment if required
            if (request.ProcessImmediatePayment && !string.IsNullOrEmpty(request.PaymentMethodId))
            {
                var paymentResult = await _paymentService.ProcessSubscriptionPaymentAsync(
                    subscription, request.PaymentMethodId);

                if (!paymentResult.IsSuccess)
                {
                    _logger.LogWarning("Payment failed for subscription resume {SubscriptionId}: {Error}", 
                        request.SubscriptionId, paymentResult.ErrorMessage);
                    throw new SubscriptionDomainException($"Resume failed: {paymentResult.ErrorMessage}");
                }
            }

            // Resume the subscription
            subscription.Resume(request.ResumeReason);
            
            // Save changes
            await _subscriptionRepository.UpdateAsync(subscription);

            // Publish resume event
            await _messageBroker.PublishAsync("subscription.resumed", new
            {
                SubscriptionId = subscription.Id,
                UserId = subscription.UserId,
                PlanId = subscription.PlanId,
                ResumeReason = request.ResumeReason,
                ResumedAt = DateTime.UtcNow,
                NextBillingDate = subscription.NextBillingDate,
                Amount = subscription.CurrentPrice.Amount,
                Currency = subscription.CurrentPrice.Currency
            });

            _logger.LogInformation("Successfully resumed subscription {SubscriptionId} for user {UserId}", 
                request.SubscriptionId, request.UserId);

            return true;
        }
    }
}
