using MediatR;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Handlers;

public class GetAuditLogByIdQueryHandler : IRequestHandler<GetAuditLogByIdQuery, AuditLogDto?>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<GetAuditLogByIdQueryHandler> _logger;

    public GetAuditLogByIdQueryHandler(
        IAuditLogRepository auditLogRepository,
        ILogger<GetAuditLogByIdQueryHandler> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<AuditLogDto?> Handle(GetAuditLogByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var auditLog = await _auditLogRepository.GetByIdAsync(request.Id, cancellationToken);
            if (auditLog == null)
                return null;

            return new AuditLogDto
            {
                Id = auditLog.Id,
                EventType = auditLog.EventType,
                Severity = auditLog.Severity,
                UserId = auditLog.UserId,
                UserName = auditLog.UserName,
                UserRole = auditLog.UserRole,
                EntityType = auditLog.EntityType,
                EntityId = auditLog.EntityId,
                Action = auditLog.Action,
                Description = auditLog.Description,
                OldValues = auditLog.OldValues,
                NewValues = auditLog.NewValues,
                IpAddress = auditLog.Context.IpAddress,
                UserAgent = auditLog.Context.UserAgent,
                SessionId = auditLog.Context.SessionId,
                CorrelationId = auditLog.Context.CorrelationId,
                EventTimestamp = auditLog.EventTimestamp,
                CreatedAt = auditLog.CreatedAt,
                ComplianceFlags = auditLog.ComplianceFlags
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit log: {AuditLogId}", request.Id);
            throw;
        }
    }
}

public class GetAuditTrailQueryHandler : IRequestHandler<GetAuditTrailQuery, AuditTrailResultDto>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<GetAuditTrailQueryHandler> _logger;

    public GetAuditTrailQueryHandler(
        IAuditLogRepository auditLogRepository,
        ILogger<GetAuditTrailQueryHandler> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<AuditTrailResultDto> Handle(GetAuditTrailQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var auditLogs = await _auditLogRepository.GetAuditTrailAsync(
                request.UserId,
                request.EntityType,
                request.EntityId,
                request.EventType,
                request.MinSeverity,
                request.FromDate,
                request.ToDate,
                request.SearchTerm,
                request.PageNumber,
                request.PageSize,
                cancellationToken);

            var totalCount = await _auditLogRepository.GetAuditTrailCountAsync(
                request.UserId,
                request.EntityType,
                request.EntityId,
                request.EventType,
                request.MinSeverity,
                request.FromDate,
                request.ToDate,
                request.SearchTerm,
                cancellationToken);

            var auditLogDtos = auditLogs.Select(al => new AuditLogDto
            {
                Id = al.Id,
                EventType = al.EventType,
                Severity = al.Severity,
                UserId = al.UserId,
                UserName = al.UserName,
                UserRole = al.UserRole,
                EntityType = al.EntityType,
                EntityId = al.EntityId,
                Action = al.Action,
                Description = al.Description,
                OldValues = request.IncludeSensitiveData ? al.OldValues : "[REDACTED]",
                NewValues = request.IncludeSensitiveData ? al.NewValues : "[REDACTED]",
                IpAddress = al.Context.IpAddress,
                UserAgent = al.Context.UserAgent,
                SessionId = al.Context.SessionId,
                CorrelationId = al.Context.CorrelationId,
                EventTimestamp = al.EventTimestamp,
                CreatedAt = al.CreatedAt,
                ComplianceFlags = al.ComplianceFlags
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new AuditTrailResultDto
            {
                AuditLogs = auditLogDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = totalPages,
                HasNextPage = request.PageNumber < totalPages,
                HasPreviousPage = request.PageNumber > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail");
            throw;
        }
    }
}

public class GetSecurityEventsQueryHandler : IRequestHandler<GetSecurityEventsQuery, List<AuditLogDto>>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly ILogger<GetSecurityEventsQueryHandler> _logger;

    public GetSecurityEventsQueryHandler(
        IAuditLogRepository auditLogRepository,
        ILogger<GetSecurityEventsQueryHandler> logger)
    {
        _auditLogRepository = auditLogRepository;
        _logger = logger;
    }

    public async Task<List<AuditLogDto>> Handle(GetSecurityEventsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var securityEvents = await _auditLogRepository.GetSecurityEventsAsync(
                request.FromDate, request.ToDate, cancellationToken);

            return securityEvents.Select(se => new AuditLogDto
            {
                Id = se.Id,
                EventType = se.EventType,
                Severity = se.Severity,
                UserId = se.UserId,
                UserName = se.UserName,
                UserRole = se.UserRole,
                EntityType = se.EntityType,
                EntityId = se.EntityId,
                Action = se.Action,
                Description = se.Description,
                IpAddress = se.Context.IpAddress,
                UserAgent = se.Context.UserAgent,
                SessionId = se.Context.SessionId,
                CorrelationId = se.Context.CorrelationId,
                EventTimestamp = se.EventTimestamp,
                CreatedAt = se.CreatedAt,
                ComplianceFlags = se.ComplianceFlags
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events from {FromDate} to {ToDate}", 
                request.FromDate, request.ToDate);
            throw;
        }
    }
}

public class GetComplianceReportByIdQueryHandler : IRequestHandler<GetComplianceReportByIdQuery, ComplianceReportDto?>
{
    private readonly IComplianceReportRepository _complianceReportRepository;
    private readonly ILogger<GetComplianceReportByIdQueryHandler> _logger;

    public GetComplianceReportByIdQueryHandler(
        IComplianceReportRepository complianceReportRepository,
        ILogger<GetComplianceReportByIdQueryHandler> logger)
    {
        _complianceReportRepository = complianceReportRepository;
        _logger = logger;
    }

    public async Task<ComplianceReportDto?> Handle(GetComplianceReportByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var report = await _complianceReportRepository.GetByIdAsync(request.Id, cancellationToken);
            if (report == null)
                return null;

            return new ComplianceReportDto
            {
                Id = report.Id,
                ReportNumber = report.ReportNumber,
                Title = report.Title,
                Description = report.Description,
                Standard = report.Standard,
                Status = report.Status,
                ReportPeriodStart = report.ReportPeriodStart,
                ReportPeriodEnd = report.ReportPeriodEnd,
                GeneratedBy = report.GeneratedBy,
                GeneratedByName = report.GeneratedByName,
                CreatedAt = report.CreatedAt,
                CompletedAt = report.CompletedAt,
                FilePath = report.FilePath,
                IsAutomated = report.IsAutomated,
                Findings = report.Findings,
                Recommendations = report.Recommendations,
                TotalViolations = report.TotalViolations,
                CriticalViolations = report.CriticalViolations,
                HighViolations = report.HighViolations,
                MediumViolations = report.MediumViolations,
                LowViolations = report.LowViolations,
                Items = report.Items.Select(item => new ComplianceReportItemDto
                {
                    Id = item.Id,
                    CheckName = item.CheckName,
                    Description = item.Description,
                    IsViolation = item.IsViolation,
                    Severity = item.Severity,
                    Details = item.Details,
                    Recommendation = item.Recommendation,
                    Evidence = item.Evidence,
                    CheckedAt = item.CheckedAt
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving compliance report: {ReportId}", request.Id);
            throw;
        }
    }
}
