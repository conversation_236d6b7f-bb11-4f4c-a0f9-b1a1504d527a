using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.Interfaces.Repositories;

public interface IEscrowTransactionRepository
{
    Task<EscrowTransaction?> GetByIdAsync(Guid id);
    Task<List<EscrowTransaction>> GetByEscrowAccountIdAsync(Guid escrowAccountId);
    Task<List<EscrowTransaction>> GetByTransactionTypeAsync(string transactionType);
    Task<List<EscrowTransaction>> GetByStatusAsync(string status);
    Task<List<EscrowTransaction>> GetByDateRangeAsync(DateTime from, DateTime to);
    Task<List<EscrowTransaction>> GetByProcessedByAsync(Guid processedBy);
    Task<EscrowTransaction> AddAsync(EscrowTransaction transaction);
    Task UpdateAsync(EscrowTransaction transaction);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<decimal> GetTotalAmountByAccountAsync(Guid escrowAccountId, string? transactionType = null);
    Task<List<EscrowTransaction>> GetRecentTransactionsAsync(Guid escrowAccountId, int count = 10);
}
