apiVersion: v1
kind: ConfigMap
metadata:
  name: analytics-config
  namespace: tli-analytics
  labels:
    app: analytics-api
    service: analytics-bi
    environment: production
data:
  # JWT Configuration
  jwt-issuer: "TLI.Analytics"
  jwt-audience: "TLI.Users"
  jwt-expiry-minutes: "60"
  jwt-validate-issuer: "true"
  jwt-validate-audience: "true"
  jwt-validate-lifetime: "true"
  jwt-validate-issuer-signing-key: "true"

  # Analytics Configuration
  realtime-processing: "true"
  cache-expiration-minutes: "30"
  batch-processing-interval: "00:05:00"
  max-concurrent-queries: "50"
  query-timeout-seconds: "30"
  enable-metrics-collection: "true"

  # Logging Configuration
  log-level: "Information"
  log-level-microsoft: "Warning"
  log-level-microsoft-hosting-lifetime: "Information"
  log-level-analytics-service: "Information"

  # CORS Configuration
  cors-allowed-origins: "https://app.tli-platform.com,https://admin.tli-platform.com"
  cors-allow-credentials: "true"

  # Rate Limiting
  enable-rate-limiting: "true"
  requests-per-minute: "1000"
  requests-per-hour: "10000"

  # Health Checks
  healthchecks-enabled: "true"
  healthchecks-database-timeout-seconds: "10"
  healthchecks-redis-timeout-seconds: "5"

  # File Storage
  file-storage-provider: "S3"
  s3-bucket-name: "tli-analytics-exports"
  aws-region: "us-east-1"

  # Monitoring
  prometheus-enabled: "true"
  prometheus-port: "9090"

  # Database Configuration
  database-host: "timescaledb-service"
  database-port: "5432"
  database-name: "tli_analytics_prod"
  database-user: "timescale"
  database-ssl-mode: "require"
  database-pooling: "true"
  database-min-pool-size: "5"
  database-max-pool-size: "100"
  database-command-timeout: "30"

  # Redis Configuration
  redis-host: "redis-service"
  redis-port: "6379"
  redis-abort-connect: "false"

  # Application Configuration
  aspnetcore-environment: "Production"
  aspnetcore-urls: "http://+:80"
  aspnetcore-forwardedheaders-enabled: "true"

  # Feature Flags
  feature-real-time-analytics: "true"
  feature-data-export: "true"
  feature-custom-reports: "true"
  feature-bi-integrations: "true"
  feature-advanced-analytics: "true"

  # Performance Tuning
  gc-server: "true"
  gc-concurrent: "true"
  gc-retain-vm: "true"
  thread-pool-min-worker-threads: "50"
  thread-pool-min-completion-port-threads: "50"

  # Security Configuration
  security-require-https: "true"
  security-hsts-max-age: "31536000"
  security-hsts-include-subdomains: "true"
  security-content-type-options: "nosniff"
  security-frame-options: "DENY"
  security-xss-protection: "1; mode=block"

  # API Configuration
  api-version: "v1"
  api-title: "Analytics & BI Service API"
  api-description: "Comprehensive analytics and business intelligence API"
  api-contact-name: "Platform Team"
  api-contact-email: "<EMAIL>"

  # Swagger Configuration
  swagger-enabled: "false"  # Disabled in production
  swagger-route-prefix: "swagger"
  swagger-endpoint-url: "/swagger/v1/swagger.json"
  swagger-endpoint-name: "Analytics & BI Service API V1"

  # SignalR Configuration
  signalr-enabled: "true"
  signalr-hub-path: "/analytics-hub"
  signalr-keep-alive-interval: "00:00:15"
  signalr-client-timeout-interval: "00:00:30"
  signalr-handshake-timeout: "00:00:15"

  # Background Services Configuration
  background-services-enabled: "true"
  metrics-collection-interval: "00:01:00"
  alert-processing-interval: "00:00:30"
  data-cleanup-interval: "01:00:00"
  report-generation-interval: "00:15:00"

  # Export Configuration
  export-max-records: "1000000"
  export-timeout-minutes: "30"
  export-retention-days: "7"
  export-formats: "CSV,Excel,JSON,PDF,XML"

  # Integration Configuration
  integration-timeout-seconds: "30"
  integration-retry-attempts: "3"
  integration-retry-delay-seconds: "5"

  # Caching Configuration
  cache-default-expiration: "00:30:00"
  cache-sliding-expiration: "00:15:00"
  cache-absolute-expiration: "01:00:00"
  cache-size-limit: "1000"

  # Validation Configuration
  validation-max-string-length: "1000"
  validation-max-array-length: "1000"
  validation-max-depth: "10"

  # Pagination Configuration
  pagination-default-page-size: "50"
  pagination-max-page-size: "100"
  pagination-default-page: "1"

  # Timezone Configuration
  default-timezone: "UTC"
  supported-timezones: "UTC,America/New_York,Europe/London,Asia/Kolkata"

  # Localization Configuration
  default-culture: "en-US"
  supported-cultures: "en-US,hi-IN,kn-IN"

  # Audit Configuration
  audit-enabled: "true"
  audit-include-request-body: "false"
  audit-include-response-body: "false"
  audit-retention-days: "90"

  # Compliance Configuration
  compliance-gdpr-enabled: "true"
  compliance-data-retention-days: "2555"  # 7 years
  compliance-anonymization-enabled: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: analytics-secrets
  namespace: tli-analytics
  labels:
    app: analytics-api
    service: analytics-bi
    environment: production
type: Opaque
stringData:
  # Database credentials (base64 encoded in actual deployment)
  database-connection-string: "Host=timescaledb-service;Port=5432;Database=tli_analytics_prod;User Id=timescale;Password=REPLACE_WITH_ACTUAL_PASSWORD;Include Error Detail=false;Pooling=true;MinPoolSize=5;MaxPoolSize=100;CommandTimeout=30;SslMode=Require"
  database-password: "REPLACE_WITH_ACTUAL_PASSWORD"

  # Redis credentials
  redis-connection-string: "redis-service:6379,password=REPLACE_WITH_ACTUAL_PASSWORD,abortConnect=false"
  redis-password: "REPLACE_WITH_ACTUAL_PASSWORD"

  # JWT secrets
  jwt-secret-key: "REPLACE_WITH_ACTUAL_JWT_SECRET_KEY_MINIMUM_256_BITS"

  # AWS credentials for S3
  aws-access-key-id: "REPLACE_WITH_ACTUAL_AWS_ACCESS_KEY"
  aws-secret-access-key: "REPLACE_WITH_ACTUAL_AWS_SECRET_KEY"

  # Application Insights
  appinsights-instrumentation-key: "REPLACE_WITH_ACTUAL_APPINSIGHTS_KEY"

  # External API keys
  external-api-key: "REPLACE_WITH_ACTUAL_EXTERNAL_API_KEY"

  # Encryption keys
  data-protection-key: "REPLACE_WITH_ACTUAL_DATA_PROTECTION_KEY"

  # Certificate passwords (if using client certificates)
  client-certificate-password: "REPLACE_WITH_ACTUAL_CERT_PASSWORD"

  # Third-party service credentials
  email-service-api-key: "REPLACE_WITH_ACTUAL_EMAIL_API_KEY"
  sms-service-api-key: "REPLACE_WITH_ACTUAL_SMS_API_KEY"

  # Monitoring credentials
  prometheus-remote-write-password: "REPLACE_WITH_ACTUAL_PROMETHEUS_PASSWORD"
  prometheus-remote-read-password: "REPLACE_WITH_ACTUAL_PROMETHEUS_PASSWORD"
  grafana-admin-password: "REPLACE_WITH_ACTUAL_GRAFANA_PASSWORD"

  # Webhook secrets
  webhook-secret: "REPLACE_WITH_ACTUAL_WEBHOOK_SECRET"

  # License keys (if applicable)
  license-key: "REPLACE_WITH_ACTUAL_LICENSE_KEY"
