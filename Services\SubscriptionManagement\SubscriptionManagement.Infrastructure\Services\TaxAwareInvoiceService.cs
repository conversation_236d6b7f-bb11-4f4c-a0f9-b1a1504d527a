using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Services;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class TaxAwareInvoiceService : IInvoiceService
    {
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly ITaxExemptionRepository _taxExemptionRepository;
        private readonly ILogger<TaxAwareInvoiceService> _logger;

        public TaxAwareInvoiceService(
            ITaxCalculationService taxCalculationService,
            ITaxExemptionRepository taxExemptionRepository,
            ILogger<TaxAwareInvoiceService> logger)
        {
            _taxCalculationService = taxCalculationService;
            _taxExemptionRepository = taxExemptionRepository;
            _logger = logger;
        }

        public async Task<InvoiceData> GenerateInvoiceAsync(Subscription subscription, string customerRegion, InvoiceType invoiceType = InvoiceType.Subscription)
        {
            _logger.LogInformation("Generating tax-aware invoice for subscription {SubscriptionId}", subscription.Id);

            try
            {
                // Get tax breakdown for the subscription
                var taxBreakdown = await _taxCalculationService.GetTaxBreakdownAsync(
                    subscription.CurrentPrice, customerRegion, subscription.UserId);

                // Get customer exemptions
                var exemptions = await _taxExemptionRepository.GetActiveByUserIdAsync(subscription.UserId);
                var applicableExemptions = exemptions.Where(e => 
                    e.ApplicableRegions.Contains(customerRegion.ToUpperInvariant()) &&
                    e.IsValidOn(DateTime.UtcNow)).ToList();

                // Create invoice data with tax details
                var invoiceData = new InvoiceData
                {
                    InvoiceNumber = GenerateInvoiceNumber(),
                    SubscriptionId = subscription.Id,
                    UserId = subscription.UserId,
                    InvoiceType = invoiceType,
                    IssueDate = DateTime.UtcNow,
                    DueDate = DateTime.UtcNow.AddDays(30),
                    CustomerRegion = customerRegion,
                    
                    // Base amounts
                    SubtotalAmount = subscription.CurrentPrice.Amount,
                    Currency = subscription.CurrentPrice.Currency,
                    
                    // Tax details
                    TaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                    TotalAmount = taxBreakdown.TotalAmount.Amount,
                    IsTaxInclusive = taxBreakdown.IsTaxInclusive,
                    
                    // Line items
                    LineItems = new List<InvoiceLineItem>
                    {
                        new()
                        {
                            Description = $"Subscription: {subscription.Plan?.Name ?? "Unknown Plan"}",
                            Quantity = 1,
                            UnitPrice = subscription.CurrentPrice.Amount,
                            Amount = subscription.CurrentPrice.Amount,
                            TaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                            TaxRate = taxBreakdown.TaxItems.Sum(t => t.Rate)
                        }
                    },
                    
                    // Tax breakdown
                    TaxBreakdown = taxBreakdown.TaxItems.Select(item => new InvoiceTaxItem
                    {
                        TaxType = item.TaxType.ToString(),
                        TaxName = item.TaxName,
                        Rate = item.Rate,
                        TaxableAmount = item.TaxableAmount.Amount,
                        TaxAmount = item.TaxAmount.Amount,
                        IsExempt = item.IsExempt,
                        ExemptionReason = item.ExemptionReason
                    }).ToList(),
                    
                    // Exemption details
                    TaxExemptions = applicableExemptions.Select(exemption => new InvoiceTaxExemption
                    {
                        ExemptionNumber = exemption.ExemptionNumber,
                        ExemptionType = exemption.ExemptionType,
                        IssuingAuthority = exemption.IssuingAuthority,
                        ValidFrom = exemption.ValidFrom,
                        ValidTo = exemption.ValidTo,
                        ExemptTaxTypes = exemption.ExemptTaxTypes.Select(t => t.ToString()).ToList()
                    }).ToList(),
                    
                    // Billing period
                    BillingPeriodStart = subscription.StartDate,
                    BillingPeriodEnd = subscription.NextBillingDate,
                    
                    // Additional metadata
                    GeneratedAt = DateTime.UtcNow,
                    TaxCalculationDate = taxBreakdown.CalculatedAt
                };

                _logger.LogInformation("Generated invoice {InvoiceNumber} for subscription {SubscriptionId} with total tax {TaxAmount} {Currency}",
                    invoiceData.InvoiceNumber, subscription.Id, invoiceData.TaxAmount, invoiceData.Currency);

                return invoiceData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating tax-aware invoice for subscription {SubscriptionId}", subscription.Id);
                throw;
            }
        }

        public async Task<InvoiceData> GenerateProrationInvoiceAsync(Subscription subscription, Plan newPlan, string customerRegion, DateTime effectiveDate)
        {
            _logger.LogInformation("Generating proration invoice for subscription {SubscriptionId} upgrade to plan {PlanId}", 
                subscription.Id, newPlan.Id);

            try
            {
                // Calculate proration amount
                var prorationAmount = newPlan.CalculateProrationAmount(effectiveDate, subscription.NextBillingDate, subscription.CurrentPrice);
                
                // Get tax breakdown for proration amount
                var taxBreakdown = await _taxCalculationService.GetTaxBreakdownAsync(
                    prorationAmount, customerRegion, subscription.UserId);

                var invoiceData = new InvoiceData
                {
                    InvoiceNumber = GenerateInvoiceNumber(),
                    SubscriptionId = subscription.Id,
                    UserId = subscription.UserId,
                    InvoiceType = InvoiceType.Proration,
                    IssueDate = DateTime.UtcNow,
                    DueDate = DateTime.UtcNow.AddDays(7), // Shorter due date for prorations
                    CustomerRegion = customerRegion,
                    
                    SubtotalAmount = prorationAmount.Amount,
                    Currency = prorationAmount.Currency,
                    TaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                    TotalAmount = taxBreakdown.TotalAmount.Amount,
                    IsTaxInclusive = taxBreakdown.IsTaxInclusive,
                    
                    LineItems = new List<InvoiceLineItem>
                    {
                        new()
                        {
                            Description = $"Plan upgrade proration: {subscription.Plan?.Name} → {newPlan.Name}",
                            Quantity = 1,
                            UnitPrice = prorationAmount.Amount,
                            Amount = prorationAmount.Amount,
                            TaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                            TaxRate = taxBreakdown.TaxItems.Sum(t => t.Rate)
                        }
                    },
                    
                    TaxBreakdown = taxBreakdown.TaxItems.Select(item => new InvoiceTaxItem
                    {
                        TaxType = item.TaxType.ToString(),
                        TaxName = item.TaxName,
                        Rate = item.Rate,
                        TaxableAmount = item.TaxableAmount.Amount,
                        TaxAmount = item.TaxAmount.Amount,
                        IsExempt = item.IsExempt,
                        ExemptionReason = item.ExemptionReason
                    }).ToList(),
                    
                    BillingPeriodStart = effectiveDate,
                    BillingPeriodEnd = subscription.NextBillingDate,
                    GeneratedAt = DateTime.UtcNow,
                    TaxCalculationDate = taxBreakdown.CalculatedAt
                };

                return invoiceData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating proration invoice for subscription {SubscriptionId}", subscription.Id);
                throw;
            }
        }

        public async Task<byte[]> GenerateInvoicePdfAsync(InvoiceData invoiceData)
        {
            // This would integrate with a PDF generation library
            // For now, return empty byte array
            _logger.LogInformation("Generating PDF for invoice {InvoiceNumber}", invoiceData.InvoiceNumber);
            
            // TODO: Implement PDF generation with tax details
            return Array.Empty<byte>();
        }

        private string GenerateInvoiceNumber()
        {
            return $"INV-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpperInvariant()}";
        }
    }

    // Enhanced invoice data models
    public class InvoiceData
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public InvoiceType InvoiceType { get; set; }
        public DateTime IssueDate { get; set; }
        public DateTime DueDate { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        
        public decimal SubtotalAmount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public bool IsTaxInclusive { get; set; }
        
        public List<InvoiceLineItem> LineItems { get; set; } = new();
        public List<InvoiceTaxItem> TaxBreakdown { get; set; } = new();
        public List<InvoiceTaxExemption> TaxExemptions { get; set; } = new();
        
        public DateTime BillingPeriodStart { get; set; }
        public DateTime BillingPeriodEnd { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime TaxCalculationDate { get; set; }
    }

    public class InvoiceLineItem
    {
        public string Description { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Amount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxRate { get; set; }
    }

    public class InvoiceTaxItem
    {
        public string TaxType { get; set; } = string.Empty;
        public string TaxName { get; set; } = string.Empty;
        public decimal Rate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public bool IsExempt { get; set; }
        public string? ExemptionReason { get; set; }
    }

    public class InvoiceTaxExemption
    {
        public string ExemptionNumber { get; set; } = string.Empty;
        public string ExemptionType { get; set; } = string.Empty;
        public string IssuingAuthority { get; set; } = string.Empty;
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public List<string> ExemptTaxTypes { get; set; } = new();
    }

    public enum InvoiceType
    {
        Subscription,
        Proration,
        Refund,
        Credit
    }

    public interface IInvoiceService
    {
        Task<InvoiceData> GenerateInvoiceAsync(Subscription subscription, string customerRegion, InvoiceType invoiceType = InvoiceType.Subscription);
        Task<InvoiceData> GenerateProrationInvoiceAsync(Subscription subscription, Plan newPlan, string customerRegion, DateTime effectiveDate);
        Task<byte[]> GenerateInvoicePdfAsync(InvoiceData invoiceData);
    }
}
