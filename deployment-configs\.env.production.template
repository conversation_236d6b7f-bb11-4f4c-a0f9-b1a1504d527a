# TLI Microservices Production Environment Configuration
# Copy this file to .env and update the values

# Application Version
TLI_VERSION=latest

# Domain Configuration
DOMAIN_NAME=your-domain.com
API_DOMAIN=api.your-domain.com

# Database Configuration
POSTGRES_DB=tli_microservices
POSTGRES_USER=tli_admin
POSTGRES_PASSWORD=your_super_secure_postgres_password_here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long-and-secure
JWT_ISSUER=TLI.Identity
JWT_AUDIENCE=TLI.Services

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=tli_admin
RABBITMQ_DEFAULT_PASS=your_secure_rabbitmq_password_here

# Email Configuration
EMAIL_PROVIDER=SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# SMS Configuration
SMS_PROVIDER=Twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Payment Gateway Configuration
# Razorpay
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# PayU
PAYU_MERCHANT_KEY=your_payu_merchant_key
PAYU_SALT=your_payu_salt

# Maps & Location Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token

# File Storage Configuration
STORAGE_PROVIDER=AWS_S3
STORAGE_PATH=/app/storage
MAX_FILE_SIZE=*********

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=ap-south-1
AWS_S3_BUCKET=tli-production-storage

# Azure Blob Storage Configuration
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_STORAGE_CONTAINER=tli-production

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your_gcp_project_id
GOOGLE_CLOUD_STORAGE_BUCKET=tli-production-storage

# Monitoring & Analytics
APPLICATION_INSIGHTS_KEY=your_application_insights_key
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
DATADOG_API_KEY=your_datadog_api_key

# Logging Configuration
LOG_LEVEL=Information
SERILOG_MINIMUM_LEVEL=Information
ELASTICSEARCH_URL=http://elasticsearch:9200

# Security Configuration
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com,https://admin.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,api.your-domain.com

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# Health Check Configuration
HEALTH_CHECK_TIMEOUT_SECONDS=30
HEALTH_CHECK_INTERVAL_SECONDS=60

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=tli-backups

# SSL Configuration
SSL_CERTIFICATE_PATH=/etc/ssl/certs/your-domain.crt
SSL_PRIVATE_KEY_PATH=/etc/ssl/private/your-domain.key

# External Service URLs
IDENTITY_SERVICE_URL=http://identity-api:8080
USER_MANAGEMENT_SERVICE_URL=http://usermanagement-api:8080
SUBSCRIPTION_SERVICE_URL=http://subscription-api:8080
ORDER_SERVICE_URL=http://order-api:8080
TRIP_SERVICE_URL=http://trip-api:8080
FLEET_SERVICE_URL=http://fleet-api:8080
PAYMENT_SERVICE_URL=http://payment-api:8080
COMMUNICATION_SERVICE_URL=http://communication-api:8080
ANALYTICS_SERVICE_URL=http://analytics-api:8080
STORAGE_SERVICE_URL=http://storage-api:8080
AUDIT_SERVICE_URL=http://audit-api:8080
MONITORING_SERVICE_URL=http://monitoring-api:8080

# Feature Flags
ENABLE_REAL_TIME_TRACKING=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_MOBILE_NOTIFICATIONS=true
ENABLE_AUDIT_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# Cache Configuration
CACHE_DEFAULT_EXPIRATION_MINUTES=60
CACHE_SLIDING_EXPIRATION_MINUTES=30
CACHE_ABSOLUTE_EXPIRATION_HOURS=24

# Database Connection Pool
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=100
DB_CONNECTION_TIMEOUT_SECONDS=30
DB_COMMAND_TIMEOUT_SECONDS=60

# Message Queue Configuration
RABBITMQ_PREFETCH_COUNT=10
RABBITMQ_RETRY_COUNT=3
RABBITMQ_RETRY_DELAY_SECONDS=5

# File Upload Configuration
MAX_UPLOAD_SIZE_MB=50
ALLOWED_FILE_EXTENSIONS=.pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx
VIRUS_SCAN_ENABLED=true

# Notification Configuration
PUSH_NOTIFICATION_ENABLED=true
EMAIL_NOTIFICATION_ENABLED=true
SMS_NOTIFICATION_ENABLED=true
IN_APP_NOTIFICATION_ENABLED=true

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_PROCESSING_INTERVAL_MINUTES=5

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE_SECONDS=31536000
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# API Versioning
API_VERSION=v1
API_DEPRECATION_NOTICE_DAYS=90

# Timezone Configuration
DEFAULT_TIMEZONE=Asia/Kolkata
UTC_OFFSET=+05:30

# Localization
DEFAULT_LANGUAGE=en-IN
SUPPORTED_LANGUAGES=en-IN,hi-IN,ta-IN,te-IN,kn-IN,ml-IN

# Performance Configuration
MAX_REQUEST_SIZE_MB=100
REQUEST_TIMEOUT_SECONDS=300
RESPONSE_COMPRESSION_ENABLED=true

# Development/Debug Configuration (Set to false in production)
ENABLE_SWAGGER=false
ENABLE_DEBUG_LOGGING=false
ENABLE_DETAILED_ERRORS=false
ENABLE_DEVELOPER_EXCEPTION_PAGE=false

# Maintenance Mode
MAINTENANCE_MODE_ENABLED=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# Load Balancer Configuration
LOAD_BALANCER_HEALTH_CHECK_PATH=/health
LOAD_BALANCER_TIMEOUT_SECONDS=30

# Container Resource Limits
CONTAINER_MEMORY_LIMIT=2G
CONTAINER_CPU_LIMIT=1.0
CONTAINER_MEMORY_RESERVATION=1G
CONTAINER_CPU_RESERVATION=0.5
