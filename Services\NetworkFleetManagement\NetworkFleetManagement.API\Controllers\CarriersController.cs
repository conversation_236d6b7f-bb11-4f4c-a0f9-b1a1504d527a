using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.Carriers;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Carriers;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class CarriersController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CarriersController> _logger;

    public CarriersController(IMediator mediator, ILogger<CarriersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all carriers with pagination and filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<CarrierSummaryDto>>> GetCarriers(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] CarrierStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarriersQuery(pageNumber, pageSize, searchTerm, status);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving carriers");
            return StatusCode(500, "An error occurred while retrieving carriers");
        }
    }

    /// <summary>
    /// Get carrier by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<CarrierDto>> GetCarrier(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarrierByIdQuery(id);
            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
                return NotFound($"Carrier with ID {id} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving carrier {CarrierId}", id);
            return StatusCode(500, "An error occurred while retrieving the carrier");
        }
    }

    /// <summary>
    /// Create a new carrier
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<CarrierDto>> CreateCarrier(
        [FromBody] CreateCarrierDto createCarrierDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateCarrierCommand(createCarrierDto);
            var result = await _mediator.Send(command, cancellationToken);

            return CreatedAtAction(
                nameof(GetCarrier),
                new { id = result.Id },
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating carrier");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating carrier");
            return StatusCode(500, "An error occurred while creating the carrier");
        }
    }

    /// <summary>
    /// Update carrier information
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<CarrierDto>> UpdateCarrier(
        Guid id,
        [FromBody] UpdateCarrierDto updateCarrierDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateCarrierCommand(id, updateCarrierDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating carrier");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier {CarrierId}", id);
            return StatusCode(500, "An error occurred while updating the carrier");
        }
    }

    /// <summary>
    /// Update carrier status
    /// </summary>
    [HttpPut("{id:guid}/status")]
    public async Task<ActionResult<CarrierDto>> UpdateCarrierStatus(
        Guid id,
        [FromBody] UpdateCarrierStatusDto statusDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateCarrierStatusCommand(id, statusDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating carrier status");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier status for {CarrierId}", id);
            return StatusCode(500, "An error occurred while updating the carrier status");
        }
    }

    /// <summary>
    /// Update carrier onboarding status
    /// </summary>
    [HttpPut("{id:guid}/onboarding-status")]
    public async Task<ActionResult<CarrierDto>> UpdateCarrierOnboardingStatus(
        Guid id,
        [FromBody] UpdateCarrierOnboardingStatusDto statusDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateCarrierOnboardingStatusCommand(id, statusDto);
            var result = await _mediator.Send(command, cancellationToken);

            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating carrier onboarding status");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier onboarding status for {CarrierId}", id);
            return StatusCode(500, "An error occurred while updating the carrier onboarding status");
        }
    }

    /// <summary>
    /// Get carriers by status
    /// </summary>
    [HttpGet("by-status/{status}")]
    public async Task<ActionResult<IEnumerable<CarrierSummaryDto>>> GetCarriersByStatus(
        CarrierStatus status,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarriersQuery(1, 100, null, status);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result.Items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving carriers by status {Status}", status);
            return StatusCode(500, "An error occurred while retrieving carriers");
        }
    }

    /// <summary>
    /// Get active carriers
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<CarrierSummaryDto>>> GetActiveCarriers(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarriersQuery(1, 100, null, CarrierStatus.Active);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result.Items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active carriers");
            return StatusCode(500, "An error occurred while retrieving active carriers");
        }
    }

    /// <summary>
    /// Search carriers
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<PagedResult<CarrierSummaryDto>>> SearchCarriers(
        [FromQuery] string searchTerm,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return BadRequest("Search term is required");

            var query = new GetCarriersQuery(pageNumber, pageSize, searchTerm);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching carriers with term {SearchTerm}", searchTerm);
            return StatusCode(500, "An error occurred while searching carriers");
        }
    }
}
