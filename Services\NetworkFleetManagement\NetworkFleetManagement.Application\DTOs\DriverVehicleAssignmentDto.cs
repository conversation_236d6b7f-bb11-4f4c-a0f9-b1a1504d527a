namespace NetworkFleetManagement.Application.DTOs;

public class DriverVehicleAssignmentDto
{
    public Guid Id { get; set; }
    public Guid DriverId { get; set; }
    public Guid VehicleId { get; set; }
    public DateTime AssignedAt { get; set; }
    public DateTime? UnassignedAt { get; set; }
    public bool IsActive { get; set; }
    public string? AssignmentNotes { get; set; }
    public string? UnassignmentReason { get; set; }
    public Guid AssignedBy { get; set; }
    public Guid? UnassignedBy { get; set; }
    public TimeSpan? AssignmentDuration { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string VehicleRegistrationNumber { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class AssignDriverToVehicleDto
{
    public string? AssignmentNotes { get; set; }
}

public class UnassignDriverFromVehicleDto
{
    public string? UnassignmentReason { get; set; }
}
