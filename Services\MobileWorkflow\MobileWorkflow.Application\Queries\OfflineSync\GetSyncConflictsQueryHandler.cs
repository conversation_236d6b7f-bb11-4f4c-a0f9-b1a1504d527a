using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Queries.OfflineSync
{
    public class GetSyncConflictsQueryHandler : IRequestHandler<GetSyncConflictsQuery, List<ConflictResolutionDto>>
    {
        private readonly IAdvancedSyncService _advancedSyncService;
        private readonly ILogger<GetSyncConflictsQueryHandler> _logger;

        public GetSyncConflictsQueryHandler(
            IAdvancedSyncService advancedSyncService,
            ILogger<GetSyncConflictsQueryHandler> logger)
        {
            _advancedSyncService = advancedSyncService;
            _logger = logger;
        }

        public async Task<List<ConflictResolutionDto>> Handle(GetSyncConflictsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting sync conflicts for user {UserId}", request.UserId);

                var conflicts = await _advancedSyncService.GetPendingConflictsAsync(request.UserId, cancellationToken);

                // Apply filters
                if (!string.IsNullOrEmpty(request.DataType))
                {
                    conflicts = conflicts.FindAll(c => c.DataType?.Equals(request.DataType, StringComparison.OrdinalIgnoreCase) == true);
                }

                if (!string.IsNullOrEmpty(request.Status))
                {
                    conflicts = conflicts.FindAll(c => c.Status?.Equals(request.Status, StringComparison.OrdinalIgnoreCase) == true);
                }

                if (request.FromDate.HasValue)
                {
                    conflicts = conflicts.FindAll(c => c.ConflictDetectedAt >= request.FromDate.Value);
                }

                if (request.ToDate.HasValue)
                {
                    conflicts = conflicts.FindAll(c => c.ConflictDetectedAt <= request.ToDate.Value);
                }

                if (!request.IncludeResolved)
                {
                    conflicts = conflicts.FindAll(c => c.Status != "Resolved");
                }

                // Apply pagination
                var skip = (request.PageNumber - 1) * request.PageSize;
                var pagedConflicts = conflicts.Skip(skip).Take(request.PageSize).ToList();

                _logger.LogInformation("Retrieved {Count} sync conflicts for user {UserId}",
                    pagedConflicts.Count, request.UserId);

                return pagedConflicts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync conflicts for user {UserId}", request.UserId);
                throw;
            }
        }
    }
}
