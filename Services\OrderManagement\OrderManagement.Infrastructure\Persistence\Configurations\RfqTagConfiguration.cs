using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class RfqTagConfiguration : IEntityTypeConfiguration<RfqTag>
{
    public void Configure(EntityTypeBuilder<RfqTag> builder)
    {
        builder.ToTable("RfqTags");

        builder.HasKey(rt => rt.Id);

        builder.Property(rt => rt.Id)
            .ValueGeneratedOnAdd();

        builder.Property(rt => rt.RfqId)
            .IsRequired();

        builder.Property(rt => rt.TagType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(rt => rt.TagName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(rt => rt.TagDescription)
            .HasMaxLength(500);

        builder.Property(rt => rt.TagColor)
            .HasMaxLength(20);

        builder.Property(rt => rt.AppliedAt)
            .IsRequired();

        builder.Property(rt => rt.AppliedBy)
            .IsRequired(false);

        builder.Property(rt => rt.AppliedByName)
            .HasMaxLength(200);

        builder.Property(rt => rt.IsSystemGenerated)
            .IsRequired();

        builder.Property(rt => rt.AutoTagReason)
            .HasMaxLength(1000);

        // Configure TagMetadata as JSON
        builder.Property(rt => rt.TagMetadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb"); // PostgreSQL specific

        builder.Property(rt => rt.IsActive)
            .IsRequired();

        builder.Property(rt => rt.RemovedAt)
            .IsRequired(false);

        builder.Property(rt => rt.RemovedBy)
            .IsRequired(false);

        builder.Property(rt => rt.RemovalReason)
            .HasMaxLength(500);

        // Relationships
        builder.HasOne(rt => rt.RFQ)
            .WithMany(r => r.Tags)
            .HasForeignKey(rt => rt.RfqId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rt => rt.RfqId)
            .HasDatabaseName("IX_RfqTags_RfqId");

        builder.HasIndex(rt => rt.TagType)
            .HasDatabaseName("IX_RfqTags_TagType");

        builder.HasIndex(rt => rt.TagName)
            .HasDatabaseName("IX_RfqTags_TagName");

        builder.HasIndex(rt => rt.IsActive)
            .HasDatabaseName("IX_RfqTags_IsActive");

        builder.HasIndex(rt => rt.AppliedAt)
            .HasDatabaseName("IX_RfqTags_AppliedAt");

        builder.HasIndex(rt => rt.IsSystemGenerated)
            .HasDatabaseName("IX_RfqTags_IsSystemGenerated");

        builder.HasIndex(rt => new { rt.RfqId, rt.TagType, rt.IsActive })
            .HasDatabaseName("IX_RfqTags_RfqId_TagType_IsActive");
    }
}
