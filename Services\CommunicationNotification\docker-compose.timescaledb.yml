version: '3.8'

services:
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tli-communication-timescaledb
    restart: unless-stopped
    environment:
      POSTGRES_DB: tli_communication_dev
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      # TimescaleDB specific settings
      TIMESCALEDB_TELEMETRY: 'off'
    ports:
      - "5432:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./Scripts/init-timescaledb.sql:/docker-entrypoint-initdb.d/01-init-timescaledb.sql:ro
      - ./Scripts/init-communication-db.sql:/docker-entrypoint-initdb.d/02-init-communication-db.sql:ro
    command: 
      - postgres
      - -c
      - shared_preload_libraries=timescaledb
      - -c
      - max_connections=200
      - -c
      - shared_buffers=256MB
      - -c
      - effective_cache_size=1GB
      - -c
      - maintenance_work_mem=64MB
      - -c
      - checkpoint_completion_target=0.9
      - -c
      - wal_buffers=16MB
      - -c
      - default_statistics_target=100
      - -c
      - random_page_cost=1.1
      - -c
      - effective_io_concurrency=200
      - -c
      - work_mem=4MB
      - -c
      - min_wal_size=1GB
      - -c
      - max_wal_size=4GB
      - -c
      - max_worker_processes=8
      - -c
      - max_parallel_workers_per_gather=4
      - -c
      - max_parallel_workers=8
      - -c
      - max_parallel_maintenance_workers=4
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timescale -d tli_communication_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - tli-communication-network

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: tli-communication-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./Scripts/pgadmin-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      timescaledb:
        condition: service_healthy
    networks:
      - tli-communication-network

  # Optional: Redis for caching and SignalR backplane
  redis:
    image: redis:7-alpine
    container_name: tli-communication-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - tli-communication-network

  # Optional: Grafana for monitoring TimescaleDB
  grafana:
    image: grafana/grafana:latest
    container_name: tli-communication-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./Scripts/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro
      - ./Scripts/grafana-dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml:ro
    depends_on:
      timescaledb:
        condition: service_healthy
    networks:
      - tli-communication-network

volumes:
  timescaledb_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

networks:
  tli-communication-network:
    driver: bridge
    name: tli-communication-network
