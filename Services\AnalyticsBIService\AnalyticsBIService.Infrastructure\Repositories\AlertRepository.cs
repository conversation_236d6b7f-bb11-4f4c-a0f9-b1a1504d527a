using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class AlertRepository : OptimizedRepositoryBase<Alert, Guid>, IAlertRepository
{
    public AlertRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<PagedResult<Alert>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.UserId == userId)
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Alert>> GetActiveAlertsAsync(int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Active)
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Alert>> GetBySeverityAsync(AlertSeverity severity, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.Severity == severity)
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Alert>> GetByRuleIdAsync(Guid ruleId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.EntityId == ruleId) // Using EntityId as RuleId
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Alert>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.TriggeredAt >= startDate && a.TriggeredAt <= endDate)
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Alert>> GetUnacknowledgedAlertsAsync(Guid? userId = null, int page = 1, int pageSize = 10, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Active && !a.IsAcknowledged);

        if (userId.HasValue)
        {
            query = query.Where(a => a.UserId == userId.Value);
        }

        query = query.OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task AcknowledgeAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null, CancellationToken cancellationToken = default)
    {
        await Context.Set<Alert>()
            .Where(a => a.Id == alertId)
            .ExecuteUpdateAsync(setters => setters
                .SetProperty(a => a.IsAcknowledged, true)
                .SetProperty(a => a.AcknowledgedAt, DateTime.UtcNow)
                .SetProperty(a => a.AcknowledgedBy, acknowledgedBy)
                .SetProperty(a => a.UpdatedAt, DateTime.UtcNow), cancellationToken);
    }

    public async Task ResolveAlertAsync(Guid alertId, Guid resolvedBy, string? resolution = null, CancellationToken cancellationToken = default)
    {
        await Context.Set<Alert>()
            .Where(a => a.Id == alertId)
            .ExecuteUpdateAsync(setters => setters
                .SetProperty(a => a.Status, AlertStatus.Resolved)
                .SetProperty(a => a.IsActive, false)
                .SetProperty(a => a.ResolvedAt, DateTime.UtcNow)
                .SetProperty(a => a.ResolvedBy, resolvedBy)
                .SetProperty(a => a.Resolution, resolution)
                .SetProperty(a => a.UpdatedAt, DateTime.UtcNow), cancellationToken);
    }

    public async Task<Dictionary<AlertSeverity, int>> GetAlertStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>().AsQueryable();

        if (startDate.HasValue)
        {
            query = query.Where(a => a.TriggeredAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(a => a.TriggeredAt <= endDate.Value);
        }

        return await query
            .GroupBy(a => a.Severity)
            .Select(g => new { Severity = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Severity, x => x.Count, cancellationToken);
    }

    public async Task BulkAcknowledgeAsync(List<Guid> alertIds, Guid acknowledgedBy, CancellationToken cancellationToken = default)
    {
        await Context.Set<Alert>()
            .Where(a => alertIds.Contains(a.Id))
            .ExecuteUpdateAsync(setters => setters
                .SetProperty(a => a.IsAcknowledged, true)
                .SetProperty(a => a.AcknowledgedAt, DateTime.UtcNow)
                .SetProperty(a => a.AcknowledgedBy, acknowledgedBy)
                .SetProperty(a => a.UpdatedAt, DateTime.UtcNow), cancellationToken);
    }

    public async Task<PagedResult<Alert>> GetAllAsync(int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Alert>()
            .OrderByDescending(a => a.TriggeredAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Alert>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }



    public async Task DeleteOldResolvedAlertsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldAlerts = await Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Resolved && a.ResolvedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        Context.Set<Alert>().RemoveRange(oldAlerts);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<Alert?> GetRecentAlertAsync(Guid metricId, TimeSpan timeWindow, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - timeWindow;

        return await Context.Set<Alert>()
            .Where(a => a.EntityId == metricId && a.TriggeredAt >= cutoffTime)
            .OrderByDescending(a => a.TriggeredAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<Alert?> GetRecentAlertAsync(Guid entityId, string entityType, string ruleName, TimeSpan timeWindow, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - timeWindow;

        return await Context.Set<Alert>()
            .Where(a => a.EntityId == entityId &&
                       a.EntityType == entityType &&
                       a.RuleName == ruleName &&
                       a.TriggeredAt >= cutoffTime)
            .OrderByDescending(a => a.TriggeredAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<List<Alert>> GetPendingAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Active && !a.IsAcknowledged)
            .OrderByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Alert>> GetPendingAlertsAsync(int limit, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Active && !a.IsAcknowledged)
            .OrderByDescending(a => a.TriggeredAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Alert>> GetOldPendingAlertsAsync(DateTime cutoffTime, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Alert>()
            .Where(a => a.Status == AlertStatus.Active &&
                       !a.IsAcknowledged &&
                       a.TriggeredAt < cutoffTime)
            .ToListAsync(cancellationToken);
    }
}
