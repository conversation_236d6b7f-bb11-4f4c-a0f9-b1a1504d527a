using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of cohort analysis service
/// </summary>
public class CohortAnalysisService : ICohortAnalysisService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<CohortAnalysisService> _logger;

    public CohortAnalysisService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<CohortAnalysisService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<CohortAnalysisDto> CreateCohortAnalysisAsync(CreateCohortAnalysisRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating cohort analysis '{Name}' for user {UserId}", request.Name, request.UserId);

            var cohort = new AnalyticsBIService.Domain.Entities.CohortAnalysis(
                name: request.Name,
                description: request.Description,
                definition: JsonSerializer.Serialize(request.Definition),
                metrics: "{}",
                createdBy: request.UserId,
                userType: request.UserType,
                periodStart: request.Definition.StartDate,
                periodEnd: request.Definition.EndDate
            );

            _context.CohortAnalyses.Add(cohort);
            await _context.SaveChangesAsync(cancellationToken);

            // Calculate initial metrics
            var metrics = await CalculateInitialMetricsAsync(cohort.Id, request.Definition, cancellationToken);
            cohort.UpdateTotalUsers(metrics.TotalUsers);
            cohort.UpdateMetrics(JsonSerializer.Serialize(metrics.Metrics));

            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(cohort);

            // Cache the cohort
            var cacheKey = CacheKeys.CohortAnalysis(request.UserId, "cohort", "all");
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Cohort analysis created with ID {CohortId}", cohort.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating cohort analysis");
            throw;
        }
    }

    public async Task<CohortAnalysisDto?> GetCohortAnalysisAsync(Guid cohortId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"cohort:analysis:{cohortId}";
            var cachedResult = await _cacheService.GetAsync<CohortAnalysisDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var cohort = await _context.CohortAnalyses
                .FirstOrDefaultAsync(c => c.Id == cohortId, cancellationToken);

            if (cohort == null)
                return null;

            var result = MapToDto(cohort);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cohort analysis {CohortId}", cohortId);
            return null;
        }
    }

    public async Task<List<CohortAnalysisDto>> GetUserCohortsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default)
    {
        try
        {
            var cohorts = await _context.CohortAnalyses
                .Where(c => c.CreatedBy == userId && c.IsActive)
                .OrderByDescending(c => c.UpdatedAt)
                .ToListAsync(cancellationToken);

            return cohorts.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user cohorts for user {UserId}", userId);
            return new List<CohortAnalysisDto>();
        }
    }

    public async Task<CohortRetentionDto> CalculateRetentionRatesAsync(Guid cohortId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating retention rates for cohort {CohortId}", cohortId);

            var cacheKey = $"cohort:retention:{cohortId}";
            var cachedResult = await _cacheService.GetAsync<CohortRetentionDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var cohort = await _context.CohortAnalyses
                .FirstOrDefaultAsync(c => c.Id == cohortId, cancellationToken);

            if (cohort == null)
                throw new InvalidOperationException($"Cohort {cohortId} not found");

            var definition = JsonSerializer.Deserialize<CohortDefinitionDto>(cohort.Definition) ?? new CohortDefinitionDto();
            var retentionData = await CalculateRetentionDataAsync(cohortId, definition, cancellationToken);

            await _cacheService.SetAsync(cacheKey, retentionData, CacheExpiration.MediumTerm, cancellationToken);

            return retentionData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating retention rates for cohort {CohortId}", cohortId);
            throw;
        }
    }

    public async Task<CohortPerformanceDto> GetCohortPerformanceAsync(Guid cohortId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting cohort performance for {CohortId}", cohortId);

            var cacheKey = $"cohort:performance:{cohortId}";
            var cachedResult = await _cacheService.GetAsync<CohortPerformanceDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var performance = await CalculatePerformanceMetricsAsync(cohortId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, performance, CacheExpiration.MediumTerm, cancellationToken);

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cohort performance");
            throw;
        }
    }

    public async Task<CohortComparisonDto> CompareCohortPerformanceAsync(List<Guid> cohortIds, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Comparing performance for {Count} cohorts", cohortIds.Count);

            var comparisons = new List<CohortComparisonItemDto>();

            foreach (var cohortId in cohortIds)
            {
                var cohort = await GetCohortAnalysisAsync(cohortId, cancellationToken);
                if (cohort != null)
                {
                    comparisons.Add(new CohortComparisonItemDto
                    {
                        CohortId = cohortId,
                        CohortName = cohort.Name,
                        Metrics = new Dictionary<string, decimal>
                        {
                            { "RetentionRate", cohort.Metrics.RetentionRate },
                            { "ChurnRate", cohort.Metrics.ChurnRate },
                            { "LifetimeValue", cohort.Metrics.AverageLifetimeValue },
                            { "ConversionRate", cohort.Metrics.ConversionRate }
                        }
                    });
                }
            }

            // Rank cohorts by overall performance
            RankCohortsByPerformance(comparisons);

            var summary = GenerateComparisonSummary(comparisons);

            return new CohortComparisonDto
            {
                CohortIds = cohortIds,
                Comparisons = comparisons,
                Summary = summary,
                ComparedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing cohort performance");
            throw;
        }
    }

    public async Task<CohortSegmentationDto> GetCohortSegmentationAsync(CohortSegmentationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting cohort segmentation for cohort {CohortId}", request.CohortId);

            var cacheKey = $"cohort:segmentation:{request.CohortId}:{request.SegmentationType}";
            var cachedResult = await _cacheService.GetAsync<CohortSegmentationDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var segmentation = await PerformCohortSegmentationAsync(request, cancellationToken);
            await _cacheService.SetAsync(cacheKey, segmentation, CacheExpiration.LongTerm, cancellationToken);

            return segmentation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cohort segmentation");
            throw;
        }
    }

    public async Task<CohortLifetimeValueDto> CalculateLifetimeValueAsync(Guid cohortId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating lifetime value for cohort {CohortId}", cohortId);

            var cacheKey = $"cohort:ltv:{cohortId}";
            var cachedResult = await _cacheService.GetAsync<CohortLifetimeValueDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var lifetimeValue = await CalculateLifetimeValueDataAsync(cohortId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, lifetimeValue, CacheExpiration.LongTerm, cancellationToken);

            return lifetimeValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating lifetime value");
            throw;
        }
    }

    public async Task<CohortBehaviorDto> GetCohortBehaviorPatternsAsync(Guid cohortId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting behavior patterns for cohort {CohortId}", cohortId);

            var cacheKey = $"cohort:behavior:{cohortId}";
            var cachedResult = await _cacheService.GetAsync<CohortBehaviorDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var behavior = await AnalyzeCohortBehaviorAsync(cohortId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, behavior, CacheExpiration.LongTerm, cancellationToken);

            return behavior;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cohort behavior patterns");
            throw;
        }
    }

    public async Task<bool> UpdateCohortDefinitionAsync(Guid cohortId, UpdateCohortRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var cohort = await _context.CohortAnalyses
                .FirstOrDefaultAsync(c => c.Id == cohortId, cancellationToken);

            if (cohort == null)
                return false;

            if (!string.IsNullOrEmpty(request.Name))
                cohort.UpdateName(request.Name);

            if (!string.IsNullOrEmpty(request.Description))
                cohort.UpdateDescription(request.Description);

            if (request.Definition != null)
                cohort.UpdateDefinition(JsonSerializer.Serialize(request.Definition));

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    cohort.Activate();
                else
                    cohort.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cachePattern = $"cohort:*:{cohortId}";
            await _cacheService.RemoveByPatternAsync(cachePattern, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cohort definition");
            return false;
        }
    }

    public async Task<bool> DeleteCohortAnalysisAsync(Guid cohortId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cohort = await _context.CohortAnalyses
                .FirstOrDefaultAsync(c => c.Id == cohortId && c.CreatedBy == userId, cancellationToken);

            if (cohort == null)
                return false;

            _context.CohortAnalyses.Remove(cohort);
            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cachePattern = $"cohort:*:{cohortId}";
            await _cacheService.RemoveByPatternAsync(cachePattern, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting cohort analysis");
            return false;
        }
    }

    public async Task<List<CohortTemplateDto>> GetCohortTemplatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return GetBuiltInCohortTemplates();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cohort templates");
            return new List<CohortTemplateDto>();
        }
    }

    // Helper methods
    private CohortAnalysisDto MapToDto(AnalyticsBIService.Domain.Entities.CohortAnalysis cohort)
    {
        var definition = string.IsNullOrEmpty(cohort.Definition)
            ? new CohortDefinitionDto()
            : JsonSerializer.Deserialize<CohortDefinitionDto>(cohort.Definition) ?? new CohortDefinitionDto();

        var metrics = string.IsNullOrEmpty(cohort.Metrics)
            ? new CohortMetricsDto()
            : JsonSerializer.Deserialize<CohortMetricsDto>(cohort.Metrics) ?? new CohortMetricsDto();

        return new CohortAnalysisDto
        {
            Id = cohort.Id,
            Name = cohort.Name,
            Description = cohort.Description,
            Definition = definition,
            Metrics = metrics,
            CreatedAt = cohort.CreatedAt,
            UpdatedAt = cohort.UpdatedAt ?? DateTime.UtcNow,
            CreatedBy = cohort.CreatedBy,
            UserType = cohort.UserType,
            IsActive = cohort.IsActive,
            TotalUsers = cohort.TotalUsers,
            PeriodStart = cohort.PeriodStart,
            PeriodEnd = cohort.PeriodEnd
        };
    }

    private async Task<(int TotalUsers, CohortMetricsDto Metrics)> CalculateInitialMetricsAsync(Guid cohortId, CohortDefinitionDto definition, CancellationToken cancellationToken)
    {
        // This would calculate actual metrics from the database
        // For now, returning sample data
        var random = new Random();
        var totalUsers = random.Next(100, 1000);

        var metrics = new CohortMetricsDto
        {
            RetentionRate = Math.Round((decimal)(random.NextDouble() * 50 + 30), 2), // 30-80%
            ChurnRate = Math.Round((decimal)(random.NextDouble() * 30 + 10), 2), // 10-40%
            AverageLifetimeValue = Math.Round((decimal)(random.NextDouble() * 500 + 100), 2), // $100-600
            AverageSessionDuration = Math.Round((decimal)(random.NextDouble() * 30 + 5), 2), // 5-35 minutes
            AverageSessionsPerUser = random.Next(3, 15),
            ConversionRate = Math.Round((decimal)(random.NextDouble() * 20 + 5), 2), // 5-25%
            LastCalculated = DateTime.UtcNow
        };

        return (totalUsers, metrics);
    }

    private async Task<CohortRetentionDto> CalculateRetentionDataAsync(Guid cohortId, CohortDefinitionDto definition, CancellationToken cancellationToken)
    {
        var random = new Random();
        var periods = new List<RetentionPeriodDto>();
        var totalUsers = random.Next(500, 1500);
        var retainedUsers = totalUsers;

        // Generate retention data for different periods
        for (int i = 0; i < 12; i++) // 12 periods (months)
        {
            retainedUsers = Math.Max(1, (int)(retainedUsers * (0.85 + random.NextDouble() * 0.1))); // 85-95% retention each period
            var retentionRate = Math.Round((decimal)retainedUsers / totalUsers * 100, 2);

            periods.Add(new RetentionPeriodDto
            {
                Period = i + 1,
                PeriodLabel = $"Month {i + 1}",
                TotalUsers = totalUsers,
                RetainedUsers = retainedUsers,
                RetentionRate = retentionRate,
                ChurnRate = Math.Round(100 - retentionRate, 2),
                PeriodStart = definition.StartDate.AddMonths(i),
                PeriodEnd = definition.StartDate.AddMonths(i + 1)
            });
        }

        var summary = new RetentionSummaryDto
        {
            AverageRetentionRate = Math.Round(periods.Average(p => p.RetentionRate), 2),
            Day1Retention = periods.FirstOrDefault()?.RetentionRate ?? 0,
            Day7Retention = periods.Skip(1).FirstOrDefault()?.RetentionRate ?? 0,
            Day30Retention = periods.Skip(2).FirstOrDefault()?.RetentionRate ?? 0,
            Day90Retention = periods.Skip(5).FirstOrDefault()?.RetentionRate ?? 0,
            TotalCohortSize = totalUsers,
            RetentionTrend = periods.Last().RetentionRate > periods.First().RetentionRate ? "Improving" : "Declining"
        };

        var trends = periods.Select(p => new RetentionTrendDto
        {
            Date = p.PeriodStart,
            RetentionRate = p.RetentionRate,
            ActiveUsers = p.RetainedUsers,
            TrendDirection = p.RetentionRate > 50 ? "Up" : "Down"
        }).ToList();

        return new CohortRetentionDto
        {
            CohortId = cohortId,
            RetentionPeriods = periods,
            Summary = summary,
            Trends = trends,
            CalculatedAt = DateTime.UtcNow
        };
    }

    private async Task<CohortPerformanceDto> CalculatePerformanceMetricsAsync(Guid cohortId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var metrics = new List<PerformanceMetricDto>
        {
            new() { Name = "Engagement Score", Value = Math.Round((decimal)(random.NextDouble() * 100), 2), Unit = "score", Timestamp = DateTime.UtcNow },
            new() { Name = "Session Duration", Value = Math.Round((decimal)(random.NextDouble() * 30 + 10), 2), Unit = "minutes", Timestamp = DateTime.UtcNow },
            new() { Name = "Page Views", Value = random.Next(50, 200), Unit = "views", Timestamp = DateTime.UtcNow },
            new() { Name = "Conversion Rate", Value = Math.Round((decimal)(random.NextDouble() * 20 + 5), 2), Unit = "percent", Timestamp = DateTime.UtcNow }
        };

        var trends = new List<PerformanceTrendDto>();
        for (int i = 0; i < 30; i++)
        {
            trends.Add(new PerformanceTrendDto
            {
                Date = DateTime.UtcNow.AddDays(-i),
                MetricName = "Engagement Score",
                Value = Math.Round((decimal)(random.NextDouble() * 100), 2),
                ChangeFromPrevious = Math.Round((decimal)(random.NextDouble() * 10 - 5), 2),
                TrendDirection = random.Next(0, 2) == 0 ? "Up" : "Down"
            });
        }

        var benchmark = new PerformanceBenchmarkDto
        {
            IndustryAverage = 65.5m,
            CompanyAverage = 72.3m,
            CohortPerformance = metrics.First(m => m.Name == "Engagement Score").Value,
            PerformanceRating = "Above Average",
            Recommendations = new List<string>
            {
                "Focus on improving user onboarding",
                "Implement personalized content recommendations",
                "Optimize mobile experience"
            }
        };

        return new CohortPerformanceDto
        {
            CohortId = cohortId,
            Metrics = metrics,
            Trends = trends,
            Benchmark = benchmark,
            CalculatedAt = DateTime.UtcNow
        };
    }

    private void RankCohortsByPerformance(List<CohortComparisonItemDto> comparisons)
    {
        // Simple ranking based on retention rate and lifetime value
        var ranked = comparisons
            .OrderByDescending(c => c.Metrics.GetValueOrDefault("RetentionRate", 0) + c.Metrics.GetValueOrDefault("LifetimeValue", 0) / 100)
            .ToList();

        for (int i = 0; i < ranked.Count; i++)
        {
            ranked[i].PerformanceRank = $"#{i + 1}";
        }
    }

    private ComparisonSummaryDto GenerateComparisonSummary(List<CohortComparisonItemDto> comparisons)
    {
        if (!comparisons.Any()) return new ComparisonSummaryDto();

        var bestPerforming = comparisons.OrderByDescending(c => c.Metrics.GetValueOrDefault("RetentionRate", 0)).First();
        var worstPerforming = comparisons.OrderBy(c => c.Metrics.GetValueOrDefault("RetentionRate", 0)).First();

        return new ComparisonSummaryDto
        {
            BestPerformingCohort = bestPerforming.CohortId,
            WorstPerformingCohort = worstPerforming.CohortId,
            KeyDifferentiator = "Retention Rate",
            Insights = new List<string>
            {
                $"Best performing cohort has {bestPerforming.Metrics.GetValueOrDefault("RetentionRate", 0):F1}% retention rate",
                $"Worst performing cohort has {worstPerforming.Metrics.GetValueOrDefault("RetentionRate", 0):F1}% retention rate",
                "Focus on improving onboarding for underperforming cohorts"
            }
        };
    }

    private async Task<CohortSegmentationDto> PerformCohortSegmentationAsync(CohortSegmentationRequest request, CancellationToken cancellationToken)
    {
        var random = new Random();
        var segments = new List<CohortSegmentDto>();

        // Generate sample segments
        var segmentNames = new[] { "High Engagers", "Casual Users", "Power Users", "At-Risk Users", "New Adopters" };
        var totalUsers = random.Next(500, 1500);
        var remainingUsers = totalUsers;

        for (int i = 0; i < Math.Min(request.MaxSegments, segmentNames.Length); i++)
        {
            var segmentSize = i == segmentNames.Length - 1 ? remainingUsers : random.Next(50, remainingUsers / 2);
            remainingUsers -= segmentSize;

            segments.Add(new CohortSegmentDto
            {
                SegmentName = segmentNames[i],
                Description = $"Users with {segmentNames[i].ToLower()} behavior patterns",
                UserCount = segmentSize,
                Percentage = Math.Round((decimal)segmentSize / totalUsers * 100, 2),
                Characteristics = new Dictionary<string, object>
                {
                    { "AvgSessionDuration", random.Next(5, 45) },
                    { "AvgSessionsPerWeek", random.Next(1, 20) },
                    { "ConversionRate", Math.Round((decimal)(random.NextDouble() * 30), 2) }
                },
                BehaviorPatterns = new List<string>
                {
                    "Regular login patterns",
                    "High feature usage",
                    "Active in community"
                }
            });

            if (remainingUsers <= 0) break;
        }

        var summary = new SegmentationSummaryDto
        {
            TotalSegments = segments.Count,
            LargestSegment = segments.OrderByDescending(s => s.UserCount).First().SegmentName,
            HighestValueSegment = segments.OrderByDescending(s => (decimal)s.Characteristics.GetValueOrDefault("ConversionRate", 0m)).First().SegmentName,
            SegmentationQuality = Math.Round((decimal)(random.NextDouble() * 30 + 70), 2), // 70-100%
            RecommendedActions = new List<string>
            {
                "Target high-value segments with premium features",
                "Implement retention campaigns for at-risk users",
                "Create onboarding flows for new adopters"
            }
        };

        return new CohortSegmentationDto
        {
            Segments = segments,
            Summary = summary,
            SegmentedAt = DateTime.UtcNow
        };
    }

    private async Task<CohortLifetimeValueDto> CalculateLifetimeValueDataAsync(Guid cohortId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var averageLTV = (decimal)(random.NextDouble() * 400 + 100); // $100-500
        var medianLTV = averageLTV * 0.8m;
        var totalLTV = averageLTV * random.Next(500, 1500);

        var distribution = new List<LifetimeValueDistributionDto>
        {
            new() { Range = "$0-$50", UserCount = random.Next(50, 150), Percentage = 15.2m, TotalValue = 2500m },
            new() { Range = "$50-$100", UserCount = random.Next(100, 200), Percentage = 25.8m, TotalValue = 7500m },
            new() { Range = "$100-$250", UserCount = random.Next(150, 300), Percentage = 35.4m, TotalValue = 18750m },
            new() { Range = "$250-$500", UserCount = random.Next(80, 150), Percentage = 18.6m, TotalValue = 28125m },
            new() { Range = "$500+", UserCount = random.Next(20, 80), Percentage = 5.0m, TotalValue = 15000m }
        };

        var trends = new List<LifetimeValueTrendDto>();
        var cumulativeValue = 0m;
        for (int i = 0; i < 12; i++)
        {
            var periodValue = (decimal)(random.NextDouble() * 50 + 25);
            cumulativeValue += periodValue;

            trends.Add(new LifetimeValueTrendDto
            {
                Date = DateTime.UtcNow.AddMonths(-11 + i),
                CumulativeValue = cumulativeValue,
                PeriodValue = periodValue,
                AverageValuePerUser = Math.Round(cumulativeValue / (i + 1), 2)
            });
        }

        return new CohortLifetimeValueDto
        {
            CohortId = cohortId,
            AverageLifetimeValue = Math.Round(averageLTV, 2),
            MedianLifetimeValue = Math.Round(medianLTV, 2),
            TotalLifetimeValue = Math.Round(totalLTV, 2),
            Distribution = distribution,
            Trends = trends,
            CalculatedAt = DateTime.UtcNow
        };
    }

    private async Task<CohortBehaviorDto> AnalyzeCohortBehaviorAsync(Guid cohortId, CancellationToken cancellationToken)
    {
        var patterns = new List<BehaviorPatternDto>
        {
            new()
            {
                PatternName = "Morning Activity",
                Description = "High activity between 8-10 AM",
                Frequency = 0.65m,
                Actions = new List<string> { "Login", "Browse", "Search" },
                Impact = "High engagement"
            },
            new()
            {
                PatternName = "Weekend Usage",
                Description = "Increased usage on weekends",
                Frequency = 0.45m,
                Actions = new List<string> { "Browse", "Purchase", "Share" },
                Impact = "Higher conversion"
            },
            new()
            {
                PatternName = "Mobile Preference",
                Description = "Primarily uses mobile devices",
                Frequency = 0.78m,
                Actions = new List<string> { "Quick actions", "Push notifications" },
                Impact = "Shorter sessions"
            }
        };

        var journeys = new List<UserJourneyDto>
        {
            new()
            {
                JourneyName = "Discovery to Purchase",
                Steps = new List<string> { "Landing", "Browse", "Product View", "Add to Cart", "Checkout", "Purchase" },
                CompletionRate = 0.23m,
                AverageDuration = TimeSpan.FromMinutes(15),
                DropOffPoints = new List<string> { "Add to Cart", "Checkout" }
            },
            new()
            {
                JourneyName = "Onboarding Flow",
                Steps = new List<string> { "Registration", "Profile Setup", "Tutorial", "First Action" },
                CompletionRate = 0.67m,
                AverageDuration = TimeSpan.FromMinutes(8),
                DropOffPoints = new List<string> { "Profile Setup" }
            }
        };

        var summary = new BehaviorSummaryDto
        {
            MostCommonAction = "Browse",
            LeastCommonAction = "Share",
            EngagementScore = 72.5m,
            KeyInsights = new List<string>
            {
                "Users are most active in the morning",
                "Mobile usage is dominant",
                "Weekend engagement is higher",
                "Onboarding completion rate needs improvement"
            }
        };

        return new CohortBehaviorDto
        {
            CohortId = cohortId,
            Patterns = patterns,
            CommonJourneys = journeys,
            Summary = summary,
            AnalyzedAt = DateTime.UtcNow
        };
    }

    private List<CohortTemplateDto> GetBuiltInCohortTemplates()
    {
        return new List<CohortTemplateDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "New User Cohort",
                Description = "Track new users from their registration date",
                Category = "User Acquisition",
                DefaultDefinition = new CohortDefinitionDto
                {
                    CohortType = "Registration",
                    TimeGranularity = "Weekly",
                    MetricsToTrack = new List<string> { "Retention", "Engagement", "Conversion" }
                },
                RecommendedMetrics = new List<string> { "Day 1 Retention", "Day 7 Retention", "Day 30 Retention" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Purchase Cohort",
                Description = "Track users based on their first purchase date",
                Category = "Revenue",
                DefaultDefinition = new CohortDefinitionDto
                {
                    CohortType = "FirstPurchase",
                    TimeGranularity = "Monthly",
                    MetricsToTrack = new List<string> { "LTV", "RepeatPurchase", "Churn" }
                },
                RecommendedMetrics = new List<string> { "Lifetime Value", "Repeat Purchase Rate", "Churn Rate" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Feature Adoption Cohort",
                Description = "Track users who adopted a specific feature",
                Category = "Product",
                DefaultDefinition = new CohortDefinitionDto
                {
                    CohortType = "FeatureAdoption",
                    TimeGranularity = "Weekly",
                    MetricsToTrack = new List<string> { "Usage", "Retention", "Satisfaction" }
                },
                RecommendedMetrics = new List<string> { "Feature Usage Rate", "User Retention", "Feature Satisfaction" },
                IsCustomizable = true
            }
        };
    }
}


