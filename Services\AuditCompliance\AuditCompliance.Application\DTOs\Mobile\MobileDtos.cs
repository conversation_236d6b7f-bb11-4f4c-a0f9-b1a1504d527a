using AuditCompliance.Domain.Enums;
using Shared.Domain.Common;

namespace AuditCompliance.Application.DTOs.Mobile;

/// <summary>
/// Mobile-optimized rating submission DTO
/// </summary>
public class MobileRatingSubmissionDto
{
    public Guid ServiceProviderId { get; set; }
    public string ServiceProviderName { get; set; } = string.Empty;
    public int OverallRating { get; set; } // 1-5 scale
    public int ReliabilityRating { get; set; }
    public int CommunicationRating { get; set; }
    public int TimelinessRating { get; set; }
    public int ProfessionalismRating { get; set; }
    public string? Comments { get; set; }
    public List<string>? Tags { get; set; } // Quick tags like "Fast", "Reliable", "Issues"
    public MobileLocationDto? Location { get; set; }
    public List<MobileAttachmentDto>? Attachments { get; set; }
    public bool IsAnonymous { get; set; }
}

/// <summary>
/// Mobile-optimized issue reporting DTO
/// </summary>
public class MobileIssueReportDto
{
    public Guid ServiceProviderId { get; set; }
    public string ServiceProviderName { get; set; } = string.Empty;
    public string IssueType { get; set; } = string.Empty; // Predefined types for mobile
    public string IssueSeverity { get; set; } = string.Empty; // Low, Medium, High, Critical
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime IncidentDate { get; set; }
    public MobileLocationDto? Location { get; set; }
    public List<MobileAttachmentDto>? Attachments { get; set; }
    public string? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
    public bool RequiresFollowUp { get; set; }
    public List<string>? AffectedServices { get; set; }
}

/// <summary>
/// Mobile compliance status DTO
/// </summary>
public class MobileComplianceStatusDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public float ComplianceScore { get; set; }
    public string ComplianceLevel { get; set; } = string.Empty; // Excellent, Good, Fair, Poor
    public string Status { get; set; } = string.Empty; // Compliant, At Risk, Non-Compliant
    public DateTime LastUpdated { get; set; }
    public List<MobileComplianceStandardDto> Standards { get; set; } = new();
    public List<MobileComplianceAlertDto> ActiveAlerts { get; set; } = new();
    public MobileComplianceTrendDto Trend { get; set; } = new();
}

/// <summary>
/// Mobile compliance standard status
/// </summary>
public class MobileComplianceStandardDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public float Score { get; set; }
    public string Status { get; set; } = string.Empty;
    public string StatusColor { get; set; } = string.Empty; // For mobile UI
    public int IssueCount { get; set; }
    public DateTime LastChecked { get; set; }
}

/// <summary>
/// Mobile compliance alert
/// </summary>
public class MobileComplianceAlertDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    public string ActionRequired { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty; // For mobile UI
    public string Color { get; set; } = string.Empty; // For mobile UI
}

/// <summary>
/// Mobile compliance trend
/// </summary>
public class MobileComplianceTrendDto
{
    public string Direction { get; set; } = string.Empty; // Improving, Stable, Declining
    public float ChangePercentage { get; set; }
    public string TrendIcon { get; set; } = string.Empty; // For mobile UI
    public string TrendColor { get; set; } = string.Empty; // For mobile UI
    public List<MobileTrendDataPointDto> RecentData { get; set; } = new();
}

/// <summary>
/// Mobile trend data point
/// </summary>
public class MobileTrendDataPointDto
{
    public DateTime Date { get; set; }
    public float Score { get; set; }
}

/// <summary>
/// Mobile location DTO
/// </summary>
public class MobileLocationDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public float? Accuracy { get; set; } // GPS accuracy in meters
}

/// <summary>
/// Mobile attachment DTO
/// </summary>
public class MobileAttachmentDto
{
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Base64Data { get; set; } = string.Empty; // For small files
    public string? ThumbnailBase64 { get; set; } // For images
    public string? Description { get; set; }
}

/// <summary>
/// Mobile dashboard summary DTO
/// </summary>
public class MobileDashboardSummaryDto
{
    public DateTime LastUpdated { get; set; }
    public MobileComplianceOverviewDto Overview { get; set; } = new();
    public List<MobileComplianceAlertDto> RecentAlerts { get; set; } = new();
    public List<MobileQuickActionDto> QuickActions { get; set; } = new();
    public List<MobileMetricCardDto> MetricCards { get; set; } = new();
    public MobileNotificationSettingsDto NotificationSettings { get; set; } = new();
}

/// <summary>
/// Mobile compliance overview
/// </summary>
public class MobileComplianceOverviewDto
{
    public float OverallScore { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public string StatusColor { get; set; } = string.Empty;
    public int TotalEntities { get; set; }
    public int CompliantEntities { get; set; }
    public int AtRiskEntities { get; set; }
    public int NonCompliantEntities { get; set; }
    public int ActiveAlerts { get; set; }
    public int PendingTasks { get; set; }
}

/// <summary>
/// Mobile quick action DTO
/// </summary>
public class MobileQuickActionDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string ActionType { get; set; } = string.Empty; // Navigate, API, External
    public string ActionTarget { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int BadgeCount { get; set; }
}

/// <summary>
/// Mobile metric card DTO
/// </summary>
public class MobileMetricCardDto
{
    public string Title { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public string Trend { get; set; } = string.Empty; // Up, Down, Stable
    public string TrendPercentage { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
}

/// <summary>
/// Mobile notification settings DTO
/// </summary>
public class MobileNotificationSettingsDto
{
    public bool EnablePushNotifications { get; set; } = true;
    public bool EnableComplianceAlerts { get; set; } = true;
    public bool EnableRiskUpdates { get; set; } = true;
    public bool EnableReportUpdates { get; set; } = true;
    public bool EnableAnomalyAlerts { get; set; } = true;
    public string NotificationFrequency { get; set; } = "Immediate"; // Immediate, Hourly, Daily
    public List<string> MutedAlertTypes { get; set; } = new();
}

/// <summary>
/// Mobile rating response DTO
/// </summary>
public class MobileRatingResponseDto
{
    public Guid RatingId { get; set; }
    public string Status { get; set; } = string.Empty; // Submitted, Processing, Approved, Rejected
    public string Message { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public string? ReferenceNumber { get; set; }
    public bool RequiresVerification { get; set; }
}

/// <summary>
/// Mobile issue report response DTO
/// </summary>
public class MobileIssueReportResponseDto
{
    public Guid IssueId { get; set; }
    public string Status { get; set; } = string.Empty; // Submitted, Under Review, Resolved, Closed
    public string Message { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public string ReferenceNumber { get; set; } = string.Empty;
    public string? AssignedTo { get; set; }
    public DateTime? ExpectedResolutionDate { get; set; }
}

/// <summary>
/// Mobile user profile DTO
/// </summary>
public class MobileUserProfileDto
{
    public string UserId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string Role { get; set; } = string.Empty;
    public string? Organization { get; set; }
    public List<string> Permissions { get; set; } = new();
    public MobileNotificationSettingsDto NotificationSettings { get; set; } = new();
    public DateTime LastLoginAt { get; set; }
    public bool IsVerified { get; set; }
}

/// <summary>
/// Mobile search request DTO
/// </summary>
public class MobileSearchRequestDto
{
    public string Query { get; set; } = string.Empty;
    public List<string>? Categories { get; set; } // Ratings, Issues, Reports, Entities
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageSize { get; set; } = 20;
    public int PageNumber { get; set; } = 1;
    public string? SortBy { get; set; }
    public string? SortOrder { get; set; } = "desc";
}

/// <summary>
/// Mobile search result DTO
/// </summary>
public class MobileSearchResultDto
{
    public string Type { get; set; } = string.Empty; // Rating, Issue, Report, Entity
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// Mobile search response DTO
/// </summary>
public class MobileSearchResponseDto
{
    public List<MobileSearchResultDto> Results { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public bool HasNextPage { get; set; }
    public List<string> SuggestedQueries { get; set; } = new();
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
}

