using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.Sync;
using MobileWorkflow.Application.Queries.Sync;
using MobileWorkflow.Application.DTOs;
using System.Security.Claims;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SyncController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<SyncController> _logger;

    public SyncController(IMediator mediator, ILogger<SyncController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Start a new sync operation
    /// </summary>
    [HttpPost("operations")]
    public async Task<ActionResult<SyncOperationResult>> StartSyncOperation([FromBody] StartSyncOperationRequest request)
    {
        var userId = GetCurrentUserId();
        
        var command = new StartSyncOperationCommand
        {
            UserId = userId,
            DeviceId = request.DeviceId,
            Options = request.Options
        };

        var result = await _mediator.Send(command);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// Get sync operation status
    /// </summary>
    [HttpGet("operations/{syncOperationId}")]
    public async Task<ActionResult<SyncOperationResult>> GetSyncStatus(Guid syncOperationId)
    {
        var query = new GetSyncStatusQuery { SyncOperationId = syncOperationId };
        var result = await _mediator.Send(query);
        
        if (result.Success)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// Cancel a sync operation
    /// </summary>
    [HttpPost("operations/{syncOperationId}/cancel")]
    public async Task<ActionResult<bool>> CancelSyncOperation(Guid syncOperationId, [FromBody] CancelSyncRequest? request = null)
    {
        var userId = GetCurrentUserId();
        
        var command = new CancelSyncOperationCommand
        {
            SyncOperationId = syncOperationId,
            RequestedBy = userId,
            Reason = request?.Reason
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Optimize bandwidth for a sync operation
    /// </summary>
    [HttpPost("operations/{syncOperationId}/optimize-bandwidth")]
    public async Task<ActionResult<bool>> OptimizeBandwidth(Guid syncOperationId, [FromBody] BandwidthOptimizationOptions options)
    {
        var command = new OptimizeBandwidthCommand
        {
            SyncOperationId = syncOperationId,
            Options = options
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Get pending conflicts for the current user
    /// </summary>
    [HttpGet("conflicts")]
    public async Task<ActionResult<List<ConflictResolutionDto>>> GetPendingConflicts()
    {
        var userId = GetCurrentUserId();
        var query = new GetPendingConflictsQuery { UserId = userId };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Resolve a sync conflict
    /// </summary>
    [HttpPost("conflicts/{conflictId}/resolve")]
    public async Task<ActionResult<bool>> ResolveConflict(Guid conflictId, [FromBody] ConflictResolutionRequest request)
    {
        request.ResolvedBy = GetCurrentUserId();
        
        var command = new ResolveConflictCommand
        {
            ConflictId = conflictId,
            ResolutionRequest = request
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Get sync statistics for the current user
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<SyncStatistics>> GetSyncStatistics([FromQuery] DateTime? fromDate = null)
    {
        var userId = GetCurrentUserId();
        var query = new GetSyncStatisticsQuery 
        { 
            UserId = userId,
            FromDate = fromDate
        };
        
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get pending sync items for the current user
    /// </summary>
    [HttpGet("items/pending")]
    public async Task<ActionResult<List<SyncItem>>> GetPendingSyncItems([FromQuery] int priority = 0)
    {
        var userId = GetCurrentUserId();
        var query = new GetPendingSyncItemsQuery 
        { 
            UserId = userId,
            Priority = priority
        };
        
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get offline data queue for the current user
    /// </summary>
    [HttpGet("offline-data")]
    public async Task<ActionResult<List<OfflineSyncQueueDto>>> GetOfflineData(
        [FromQuery] bool unsyncedOnly = true,
        [FromQuery] string? dataType = null,
        [FromQuery] int? priority = null)
    {
        var userId = GetCurrentUserId();
        var query = new GetOfflineDataQuery 
        { 
            UserId = userId,
            UnsyncedOnly = unsyncedOnly,
            DataType = dataType,
            Priority = priority
        };
        
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Create offline data entry
    /// </summary>
    [HttpPost("offline-data")]
    public async Task<ActionResult<Guid>> CreateOfflineData([FromBody] CreateOfflineDataRequest request)
    {
        var userId = GetCurrentUserId();
        
        var command = new CreateOfflineDataCommand
        {
            UserId = userId,
            MobileSessionId = request.MobileSessionId,
            DataType = request.DataType,
            Action = request.Action,
            Data = request.Data,
            Priority = request.Priority,
            MobileAppId = request.MobileAppId
        };

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetOfflineData), new { }, result);
    }

    /// <summary>
    /// Get sync health status for the current user
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult<SyncHealthDto>> GetSyncHealth()
    {
        var userId = GetCurrentUserId();
        
        // This would be implemented as a separate query handler
        var health = new SyncHealthDto
        {
            UserId = userId,
            LastSuccessfulSync = DateTime.UtcNow.AddHours(-2),
            PendingItems = 5,
            FailedItems = 1,
            ConflictItems = 0,
            RecentErrors = new List<string> { "Network timeout on item XYZ" },
            SyncSuccessRate = 95.5,
            AverageSyncDuration = TimeSpan.FromMinutes(2.5),
            TotalDataPending = 1024 * 1024, // 1MB
            OverallHealth = "Warning"
        };

        return Ok(health);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }
}

// Request DTOs
public class StartSyncOperationRequest
{
    public Guid DeviceId { get; set; }
    public SyncOptions Options { get; set; } = new();
}

public class CancelSyncRequest
{
    public string? Reason { get; set; }
}

public class CreateOfflineDataRequest
{
    public Guid MobileSessionId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public int Priority { get; set; } = 2;
    public Guid? MobileAppId { get; set; }
}
