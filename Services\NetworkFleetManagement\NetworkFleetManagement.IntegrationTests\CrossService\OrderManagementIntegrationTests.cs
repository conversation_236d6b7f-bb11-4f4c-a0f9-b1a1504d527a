using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.IntegrationTests.TestFixtures;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.CrossService;

[Collection("NetworkFleet")]
public class OrderManagementIntegrationTests : IClassFixture<NetworkFleetTestFixture>
{
    private readonly NetworkFleetTestFixture _fixture;
    private readonly HttpClient _client;
    private readonly Guid _testUserId = Guid.NewGuid();

    public OrderManagementIntegrationTests(NetworkFleetTestFixture fixture)
    {
        _fixture = fixture;
        _client = _fixture.CreateClient();
        
        _client.DefaultRequestHeaders.Add("X-User-Id", _testUserId.ToString());
        _client.DefaultRequestHeaders.Add("X-User-Role", "Broker");
    }

    [Fact]
    public async Task AutoAssignPartner_ForNewOrder_SelectsHighestPriorityPartner()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPreferredPartnersAsync();

        // Simulate order creation request
        var orderRequest = new
        {
            Route = "Route1",
            LoadType = "Dry Van",
            RequiredRating = 4.0m,
            AutoAssignPreferred = true
        };

        // Act - Test auto-assignment endpoint
        var response = await _client.PostAsJsonAsync("/api/v1/preferredpartners/test-auto-assignment", new TestAutoAssignmentRequest
        {
            PartnerType = "Carrier",
            Route = orderRequest.Route,
            LoadType = orderRequest.LoadType,
            MinRating = orderRequest.RequiredRating,
            MaxResults = 1
        });

        // Assert
        response.Should().BeSuccessful();
        
        var eligiblePartners = await response.Content.ReadFromJsonAsync<List<PreferredPartnerSummaryDto>>();
        eligiblePartners.Should().NotBeNull();
        eligiblePartners!.Should().HaveCount(1);
        
        var selectedPartner = eligiblePartners.First();
        selectedPartner.PreferenceLevel.Should().Be("High");
        selectedPartner.Priority.Should().Be(1);
        selectedPartner.AutoAssignEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task PartnerPerformanceUpdate_FromTripCompletion_UpdatesMetrics()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Simulate trip completion event from Trip Management service
        var performanceUpdate = new
        {
            PreferredPartnerId = partnerId,
            TripId = Guid.NewGuid(),
            Rating = 4.8m,
            OnTimeDelivery = true,
            QualityScore = 4.5m,
            CommunicationRating = 4.7m,
            ResponseTime = 2.5m, // hours
            CompletedAt = DateTime.UtcNow
        };

        // Act - This would typically be triggered by a message bus event
        // For testing, we'll call the repository method directly
        using var scope = _fixture.Services.CreateScope();
        var repository = scope.ServiceProvider.GetRequiredService<IPreferredPartnerRepository>();
        
        var performanceRecord = new PartnerPerformanceRecord
        {
            PreferredPartnerId = partnerId,
            Rating = performanceUpdate.Rating,
            OnTimeDelivery = performanceUpdate.OnTimeDelivery,
            QualityScore = performanceUpdate.QualityScore,
            CommunicationRating = performanceUpdate.CommunicationRating,
            ResponseTime = performanceUpdate.ResponseTime,
            RecordedAt = performanceUpdate.CompletedAt
        };

        await repository.AddPerformanceRecordAsync(performanceRecord);

        // Assert
        var updatedPartner = await repository.GetByIdAsync(partnerId);
        updatedPartner.Should().NotBeNull();
        updatedPartner!.PerformanceMetrics.OverallRating.Should().BeGreaterThan(4.0m);
        updatedPartner.PerformanceMetrics.TotalOrders.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetPartnerRecommendations_ForBroker_ReturnsCarrierRecommendations()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPreferredPartnersAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners/recommendations?partnerType=Carrier&route=Route1&loadType=Dry Van&limit=5");

        // Assert
        response.Should().BeSuccessful();
        
        var recommendations = await response.Content.ReadFromJsonAsync<List<PartnerRecommendationDto>>();
        recommendations.Should().NotBeNull();
        recommendations!.Should().NotBeEmpty();
        recommendations.Should().AllSatisfy(r => 
        {
            r.PartnerType.Should().Be("Carrier");
            r.RecommendationScore.Should().BeGreaterThan(0);
            r.MatchingCriteria.Should().NotBeEmpty();
        });
    }

    [Fact]
    public async Task PartnerCollaborationHistory_IntegratesWithOrderData()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        var partnerId = await CreateTestPartnerAsync();

        // Act
        var response = await _client.GetAsync($"/api/v1/preferredpartners/{partnerId}/collaboration-history?fromDate=2024-01-01&toDate=2024-12-31&limit=10");

        // Assert
        response.Should().BeSuccessful();
        
        var collaborations = await response.Content.ReadFromJsonAsync<List<PartnerCollaborationSummaryDto>>();
        collaborations.Should().NotBeNull();
        
        // Verify collaboration data structure
        if (collaborations!.Any())
        {
            var collaboration = collaborations.First();
            collaboration.PartnerId.Should().NotBeEmpty();
            collaboration.CollaborationType.Should().NotBeNullOrEmpty();
            collaboration.Status.Should().NotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task DashboardData_AggregatesFromMultipleSources()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPreferredPartnersAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners/dashboard");

        // Assert
        response.Should().BeSuccessful();
        
        var dashboard = await response.Content.ReadFromJsonAsync<PreferredPartnerDashboardDto>();
        dashboard.Should().NotBeNull();
        dashboard!.UserId.Should().Be(_testUserId);
        dashboard.TotalPreferredPartners.Should().BeGreaterThan(0);
        dashboard.PartnerTypeStats.Should().NotBeEmpty();
        dashboard.PerformanceInsights.Should().NotBeEmpty();
    }

    [Fact]
    public async Task AutoAssignmentStatistics_ReflectsOrderHistory()
    {
        // Arrange
        await _fixture.ResetDatabaseAsync();
        await SeedPreferredPartnersAsync();

        // Act
        var response = await _client.GetAsync("/api/v1/preferredpartners/auto-assignment/statistics?fromDate=2024-01-01&toDate=2024-12-31");

        // Assert
        response.Should().BeSuccessful();
        
        var statistics = await response.Content.ReadFromJsonAsync<AutoAssignmentStatisticsDto>();
        statistics.Should().NotBeNull();
        statistics!.UserId.Should().Be(_testUserId);
        statistics.FromDate.Should().Be(new DateTime(2024, 1, 1));
        statistics.ToDate.Should().Be(new DateTime(2024, 12, 31));
        statistics.SuccessRate.Should().BeGreaterOrEqualTo(0);
    }

    private async Task<Guid> CreateTestPartnerAsync()
    {
        var partner = PreferredPartner.Create(
            _testUserId,
            Guid.NewGuid(),
            PreferredPartnerType.Carrier,
            PreferenceLevel.High,
            1);

        partner.Activate();
        partner.UpdateAutoAssignSettings(true, 4.0m,
            new List<string> { "Route1", "Route2" },
            new List<string> { "Dry Van", "Refrigerated" },
            new List<string>(),
            new List<string>());

        using var context = _fixture.GetDbContext();
        context.PreferredPartners.Add(partner);
        await context.SaveChangesAsync();

        return partner.Id;
    }

    private async Task SeedPreferredPartnersAsync()
    {
        using var context = _fixture.GetDbContext();

        var partners = new[]
        {
            CreatePartnerWithSettings(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.High, 1, true),
            CreatePartnerWithSettings(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.Medium, 2, true),
            CreatePartnerWithSettings(_testUserId, PreferredPartnerType.Carrier, PreferenceLevel.Low, 3, false)
        };

        context.PreferredPartners.AddRange(partners);
        await context.SaveChangesAsync();
    }

    private PreferredPartner CreatePartnerWithSettings(Guid userId, PreferredPartnerType type, PreferenceLevel level, int priority, bool autoAssign)
    {
        var partner = PreferredPartner.Create(userId, Guid.NewGuid(), type, level, priority);
        partner.Activate();
        
        if (autoAssign)
        {
            partner.UpdateAutoAssignSettings(true, 4.0m,
                new List<string> { "Route1", "Route2" },
                new List<string> { "Dry Van", "Refrigerated" },
                new List<string>(),
                new List<string>());
        }

        return partner;
    }
}
