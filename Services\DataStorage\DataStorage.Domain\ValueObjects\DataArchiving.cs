using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ArchiveResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid DocumentId { get; private set; }
    public string? ArchiveId { get; private set; }
    public StorageTierType TargetTier { get; private set; }
    public DateTime ArchivedAt { get; private set; }
    public Guid ArchivedBy { get; private set; }
    public long OriginalSizeBytes { get; private set; }
    public long CompressedSizeBytes { get; private set; }
    public double CompressionRatio { get; private set; }
    public TimeSpan ArchivingTime { get; private set; }

    private ArchiveResult()
    {
    }

    public static ArchiveResult Success(
        Guid documentId,
        string archiveId,
        StorageTierType targetTier,
        Guid archivedBy,
        long originalSizeBytes,
        long compressedSizeBytes,
        TimeSpan archivingTime)
    {
        return new ArchiveResult
        {
            IsSuccessful = true,
            DocumentId = documentId,
            ArchiveId = archiveId,
            TargetTier = targetTier,
            ArchivedAt = DateTime.UtcNow,
            ArchivedBy = archivedBy,
            OriginalSizeBytes = originalSizeBytes,
            CompressedSizeBytes = compressedSizeBytes,
            CompressionRatio = originalSizeBytes > 0 ? (double)compressedSizeBytes / originalSizeBytes : 0,
            ArchivingTime = archivingTime
        };
    }

    public static ArchiveResult Failure(Guid documentId, string errorMessage)
    {
        return new ArchiveResult
        {
            IsSuccessful = false,
            DocumentId = documentId,
            ErrorMessage = errorMessage,
            ArchivedAt = DateTime.UtcNow
        };
    }
}

public class ArchiveRequest
{
    public StorageTierType TargetTier { get; private set; }
    public Guid RequestedBy { get; private set; }
    public string? Reason { get; private set; }
    public bool CompressData { get; private set; }
    public bool EncryptData { get; private set; }
    public DateTime? ScheduledFor { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public List<string> Tags { get; private set; }

    public ArchiveRequest(
        StorageTierType targetTier,
        Guid requestedBy,
        string? reason = null,
        bool compressData = true,
        bool encryptData = true,
        DateTime? scheduledFor = null,
        Dictionary<string, object>? metadata = null,
        List<string>? tags = null)
    {
        TargetTier = targetTier;
        RequestedBy = requestedBy;
        Reason = reason;
        CompressData = compressData;
        EncryptData = encryptData;
        ScheduledFor = scheduledFor;
        Metadata = metadata ?? new Dictionary<string, object>();
        Tags = tags ?? new List<string>();
    }
}

public class RestoreFromArchiveResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Guid DocumentId { get; private set; }
    public StorageLocation? RestoredLocation { get; private set; }
    public DateTime RestoredAt { get; private set; }
    public Guid RestoredBy { get; private set; }
    public TimeSpan RestoreTime { get; private set; }
    public long RestoredSizeBytes { get; private set; }

    private RestoreFromArchiveResult()
    {
    }

    public static RestoreFromArchiveResult Success(
        Guid documentId,
        StorageLocation restoredLocation,
        Guid restoredBy,
        TimeSpan restoreTime,
        long restoredSizeBytes)
    {
        return new RestoreFromArchiveResult
        {
            IsSuccessful = true,
            DocumentId = documentId,
            RestoredLocation = restoredLocation,
            RestoredAt = DateTime.UtcNow,
            RestoredBy = restoredBy,
            RestoreTime = restoreTime,
            RestoredSizeBytes = restoredSizeBytes
        };
    }

    public static RestoreFromArchiveResult Failure(Guid documentId, string errorMessage)
    {
        return new RestoreFromArchiveResult
        {
            IsSuccessful = false,
            DocumentId = documentId,
            ErrorMessage = errorMessage,
            RestoredAt = DateTime.UtcNow
        };
    }
}

public class RestoreFromArchiveRequest
{
    public Guid RequestedBy { get; private set; }
    public StorageLocation? TargetLocation { get; private set; }
    public bool RestoreToOriginalLocation { get; private set; }
    public string? Reason { get; private set; }
    public RestorePriority Priority { get; private set; }

    public RestoreFromArchiveRequest(
        Guid requestedBy,
        StorageLocation? targetLocation = null,
        bool restoreToOriginalLocation = true,
        string? reason = null,
        RestorePriority priority = RestorePriority.Normal)
    {
        RequestedBy = requestedBy;
        TargetLocation = targetLocation;
        RestoreToOriginalLocation = restoreToOriginalLocation;
        Reason = reason;
        Priority = priority;
    }
}

public class ArchivePolicy
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<ArchiveRule> Rules { get; private set; }
    public StorageTierType DefaultTargetTier { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public ArchivePolicyPriority Priority { get; private set; }

    public ArchivePolicy(
        Guid id,
        string name,
        string description,
        List<ArchiveRule> rules,
        StorageTierType defaultTargetTier,
        bool isEnabled,
        Guid createdBy,
        DateTime createdAt,
        ArchivePolicyPriority priority,
        DateTime lastModified = default)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Rules = rules ?? new List<ArchiveRule>();
        DefaultTargetTier = defaultTargetTier;
        IsEnabled = isEnabled;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        LastModified = lastModified == default ? createdAt : lastModified;
        Priority = priority;
    }
}

public class ArchivePolicyRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<ArchiveRule> Rules { get; private set; }
    public StorageTierType DefaultTargetTier { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public ArchivePolicyPriority Priority { get; private set; }

    public ArchivePolicyRequest(
        string name,
        string description,
        List<ArchiveRule> rules,
        StorageTierType defaultTargetTier,
        Guid createdBy,
        bool isEnabled = true,
        ArchivePolicyPriority priority = ArchivePolicyPriority.Normal)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Rules = rules ?? new List<ArchiveRule>();
        DefaultTargetTier = defaultTargetTier;
        CreatedBy = createdBy;
        IsEnabled = isEnabled;
        Priority = priority;
    }
}

public class ArchiveRule
{
    public string Name { get; private set; }
    public ArchiveRuleType Type { get; private set; }
    public List<ArchiveCondition> Conditions { get; private set; }
    public ArchiveAction Action { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; }

    public ArchiveRule(
        string name,
        ArchiveRuleType type,
        List<ArchiveCondition> conditions,
        ArchiveAction action,
        bool isEnabled = true,
        int priority = 0)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Conditions = conditions ?? new List<ArchiveCondition>();
        Action = action ?? throw new ArgumentNullException(nameof(action));
        IsEnabled = isEnabled;
        Priority = priority;
    }
}

public class ArchiveCondition
{
    public ArchiveConditionType Type { get; private set; }
    public string Field { get; private set; }
    public ArchiveConditionOperator Operator { get; private set; }
    public object Value { get; private set; }
    public List<object>? Values { get; private set; }

    public ArchiveCondition(
        ArchiveConditionType type,
        string field,
        ArchiveConditionOperator @operator,
        object value,
        List<object>? values = null)
    {
        Type = type;
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = @operator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
        Values = values;
    }
}

public class ArchiveAction
{
    public ArchiveActionType Type { get; private set; }
    public StorageTierType? TargetTier { get; private set; }
    public bool CompressData { get; private set; }
    public bool EncryptData { get; private set; }
    public TimeSpan? DelayBeforeArchiving { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public ArchiveAction(
        ArchiveActionType type,
        StorageTierType? targetTier = null,
        bool compressData = true,
        bool encryptData = true,
        TimeSpan? delayBeforeArchiving = null,
        Dictionary<string, object>? parameters = null)
    {
        Type = type;
        TargetTier = targetTier;
        CompressData = compressData;
        EncryptData = encryptData;
        DelayBeforeArchiving = delayBeforeArchiving;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}
