using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Repositories;
using Microsoft.Extensions.Configuration;

namespace Identity.Infrastructure.Services
{
    public class TwoFactorAuthService : ITwoFactorAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly IConfiguration _configuration;

        public TwoFactorAuthService(
            IUserRepository userRepository,
            IConfiguration configuration)
        {
            _userRepository = userRepository;
            _configuration = configuration;
        }

        public async Task<string> GenerateTwoFactorTokenAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            // Generate a random 6-digit code
            var random = new Random();
            var code = random.Next(100000, 999999).ToString();

            // In a real implementation, you would store this code securely
            // For demo purposes, we're just returning it
            return code;
        }

        public async Task<bool> ValidateTwoFactorTokenAsync(Guid userId, string token)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            // In a real implementation, you would validate against a stored token
            // For demo purposes, we're just returning true
            return true;
        }

        public async Task<string> GenerateQrCodeUriAsync(Guid userId, string email)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            var appName = _configuration["TwoFactorAuth:AppName"] ?? "TriTrackz";
            var key = GenerateRandomKey();

            // Format: otpauth://totp/{app}:{email}?secret={key}&issuer={app}
            return $"otpauth://totp/{Uri.EscapeDataString(appName)}:{Uri.EscapeDataString(email)}?secret={key}&issuer={Uri.EscapeDataString(appName)}";
        }

        public async Task<bool> IsTwoFactorEnabledAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            return user.TwoFactorEnabled;
        }

        public async Task EnableTwoFactorAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            user.EnableTwoFactor();
            await _userRepository.UpdateAsync(user);
        }

        public async Task DisableTwoFactorAsync(Guid userId)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            user.DisableTwoFactor();
            await _userRepository.UpdateAsync(user);
        }

        private string GenerateRandomKey()
        {
            var key = new byte[20];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(key);
            }
            return Convert.ToBase64String(key);
        }
    }
}
