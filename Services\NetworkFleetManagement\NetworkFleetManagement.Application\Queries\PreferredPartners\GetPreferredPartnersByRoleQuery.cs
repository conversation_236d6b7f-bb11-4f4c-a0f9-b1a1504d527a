using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredPartnersByRoleQuery : IRequest<List<PreferredPartnerSummaryDto>>
{
    public Guid UserId { get; set; }
    public PreferredPartnerType PartnerType { get; set; }
    public bool ActiveOnly { get; set; } = true;
    public int? Limit { get; set; }
}
