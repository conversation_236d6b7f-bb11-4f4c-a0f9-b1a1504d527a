using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UserManagement.Domain.Entities;

namespace UserManagement.Domain.Repositories
{
    public interface IUserProfileRepository
    {
        Task<UserProfile> GetByIdAsync(Guid id);
        Task<UserProfile> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<UserProfile>> GetByUserTypeAsync(UserType userType);
        Task<IEnumerable<UserProfile>> GetByStatusAsync(ProfileStatus status);
        Task<IEnumerable<UserProfile>> GetPendingApprovalAsync();
        Task<IEnumerable<UserProfile>> SearchAsync(string searchTerm, UserType? userType = null, ProfileStatus? status = null);
        Task<bool> ExistsAsync(Guid userId);
        Task AddAsync(UserProfile userProfile);
        Task UpdateAsync(UserProfile userProfile);
        Task DeleteAsync(Guid id);
        Task<int> GetCountByStatusAsync(ProfileStatus status);
        Task<int> GetCountByUserTypeAsync(UserType userType);
        Task<int> GetTotalUsersCountAsync();
        Task<int> GetApprovedTodayCountAsync();
        Task<int> GetRejectedTodayCountAsync();

        // Advanced search and filtering methods
        Task<(IEnumerable<UserProfile> Items, int TotalCount)> GetUsersAdvancedAsync(
            int pageNumber,
            int pageSize,
            string searchTerm = null,
            string email = null,
            string phoneNumber = null,
            string companyName = null,
            string userName = null,
            string subscriptionPlan = null,
            DateTime? planExpiryFrom = null,
            DateTime? planExpiryTo = null,
            DateTime? registrationDateFrom = null,
            DateTime? registrationDateTo = null,
            ProfileStatus? status = null,
            UserType? userType = null,
            bool? isActive = null,
            string kycStatus = null,
            bool? kycCompleted = null,
            DateTime? lastLoginFrom = null,
            DateTime? lastLoginTo = null,
            int? minRfqVolume = null,
            int? maxRfqVolume = null,
            string sortBy = "CreatedAt",
            string sortDirection = "desc",
            bool? hasSubscription = null,
            bool? autoRenewalEnabled = null,
            string region = null,
            string state = null,
            string city = null);

        Task<UserStatisticsData> GetUserStatisticsAsync();
        Task<IEnumerable<UserProfile>> GetUsersWithExpiringSubscriptionsAsync(int days = 30);
        Task<IEnumerable<UserProfile>> GetInactiveUsersAsync(int days = 90);
        Task<IEnumerable<UserProfile>> GetUsersByRegionAsync(string region);
        Task<IEnumerable<UserProfile>> GetUsersRequiringKycAsync();
        Task<Dictionary<string, int>> GetUserCountByRegionAsync();
        Task<Dictionary<UserType, int>> GetUserCountByTypeAsync();
        Task<Dictionary<ProfileStatus, int>> GetUserCountByStatusAsync();

        // Export-specific method
        Task<IEnumerable<UserProfile>> GetUsersAdvancedAsync(Dictionary<string, object> filters, int pageNumber, int pageSize);

        // Additional method for transport company retrieval
        Task<IEnumerable<UserProfile>> GetByTransportCompanyIdAsync(Guid transportCompanyId);
    }

    public class UserStatisticsData
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int PendingApproval { get; set; }
        public int KycCompleted { get; set; }
        public int KycPending { get; set; }
        public int WithActiveSubscription { get; set; }
        public int WithExpiredSubscription { get; set; }
        public Dictionary<string, int> UsersByType { get; set; } = new();
        public Dictionary<string, int> UsersByStatus { get; set; } = new();
        public Dictionary<string, int> UsersByRegion { get; set; } = new();
    }
}
