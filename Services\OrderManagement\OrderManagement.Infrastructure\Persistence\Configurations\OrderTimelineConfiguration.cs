using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Persistence.Configurations;

public class OrderTimelineConfiguration : IEntityTypeConfiguration<OrderTimeline>
{
    public void Configure(EntityTypeBuilder<OrderTimeline> builder)
    {
        builder.ToTable("order_timelines");

        builder.HasKey(ot => ot.Id);

        builder.Property(ot => ot.Id)
            .ValueGeneratedOnAdd();

        builder.Property(ot => ot.OrderId)
            .IsRequired();

        builder.Property(ot => ot.EventType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ot => ot.EventDescription)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(ot => ot.EventTimestamp)
            .IsRequired();

        builder.Property(ot => ot.ActorId)
            .IsRequired(false);

        builder.Property(ot => ot.ActorName)
            .HasMaxLength(200);

        builder.Property(ot => ot.ActorRole)
            .HasMaxLength(100);

        builder.Property(ot => ot.AdditionalData)
            .HasMaxLength(2000);

        builder.Property(ot => ot.PreviousStatus)
            .HasConversion<int?>()
            .IsRequired(false);

        builder.Property(ot => ot.NewStatus)
            .HasConversion<int?>()
            .IsRequired(false);

        builder.Property(ot => ot.Notes)
            .HasMaxLength(2000);

        builder.Property(ot => ot.IpAddress)
            .HasMaxLength(45); // IPv6 max length

        builder.Property(ot => ot.UserAgent)
            .HasMaxLength(500);

        builder.Property(ot => ot.CorrelationId)
            .IsRequired(false);

        builder.Property(ot => ot.SessionId)
            .HasMaxLength(100);

        builder.Property(ot => ot.Metadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb");

        // Relationships
        builder.HasOne(ot => ot.Order)
            .WithMany(o => o.Timeline)
            .HasForeignKey(ot => ot.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ot => ot.OrderId)
            .HasDatabaseName("IX_OrderTimelines_OrderId");

        builder.HasIndex(ot => ot.EventTimestamp)
            .HasDatabaseName("IX_OrderTimelines_EventTimestamp");

        builder.HasIndex(ot => ot.EventType)
            .HasDatabaseName("IX_OrderTimelines_EventType");

        builder.HasIndex(ot => ot.ActorId)
            .HasDatabaseName("IX_OrderTimelines_ActorId");

        builder.HasIndex(ot => ot.CorrelationId)
            .HasDatabaseName("IX_OrderTimelines_CorrelationId");

        builder.HasIndex(ot => new { ot.OrderId, ot.EventTimestamp })
            .HasDatabaseName("IX_OrderTimelines_OrderId_EventTimestamp");
    }
}
