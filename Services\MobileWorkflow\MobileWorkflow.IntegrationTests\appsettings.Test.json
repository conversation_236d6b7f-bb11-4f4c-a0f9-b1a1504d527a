{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=mobileworkflow_test_db;Username=test_user;Password=test_password"}, "Jwt": {"Key": "TestSuperSecretKeyThatIsAtLeast32CharactersLongForTesting!", "Issuer": "TLI.MobileWorkflow.Test", "Audience": "TLI.MobileWorkflow.Test.Users", "ExpiryInMinutes": 60}, "Services": {"IdentityService": {"BaseUrl": "http://localhost:5001"}, "UserManagement": {"BaseUrl": "http://localhost:5002"}, "TripManagement": {"BaseUrl": "http://localhost:5003"}, "OrderManagement": {"BaseUrl": "http://localhost:5004"}, "NetworkFleetManagement": {"BaseUrl": "http://localhost:5005"}, "CommunicationNotification": {"BaseUrl": "http://localhost:5006"}, "FinancialPayment": {"BaseUrl": "http://localhost:5007"}, "SubscriptionManagement": {"BaseUrl": "http://localhost:5008"}}, "MobileApp": {"DefaultSyncInterval": 300, "MaxOfflineDataSize": 104857600, "MaxOfflineDataAge": 86400, "SupportedPlatforms": ["Android", "iOS", "Web", "PWA"], "Features": {"OfflineMode": true, "PushNotifications": true, "BackgroundSync": true, "BiometricAuth": true, "LocationTracking": true, "CameraAccess": true, "FileUpload": true, "VoiceRecording": true, "QRScanner": true}}, "PWA": {"CacheName": "tli-mobile-test-v1", "CacheStrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OfflinePageUrl": "/offline.html", "MaxCacheSize": 52428800, "MaxCacheAge": 604800, "UpdateCheckInterval": 21600}, "Workflow": {"MaxConcurrentExecutions": 10, "DefaultTaskSLA": "24:00:00", "AutoRetryFailedTasks": true, "MaxRetryAttempts": 3, "RetryDelay": "00:05:00", "EnableWorkflowMetrics": true, "WorkflowTimeout": "72:00:00"}, "Sync": {"BatchSize": 10, "MaxRetryAttempts": 3, "RetryDelay": "00:01:00", "ConflictResolution": {"Default": "server_wins", "TripUpdate": "latest_timestamp", "PODUpload": "merge", "Emergency": "client_wins", "LocationUpdate": "latest_timestamp", "DocumentUpload": "client_wins"}, "PriorityDataTypes": ["Emergency", "TripUpdate", "PODUpload"], "SyncOnWiFiOnly": false, "SyncOnLowBattery": false}, "Integration": {"Timeout": "00:00:10", "RetryPolicy": {"MaxRetryAttempts": 2, "RetryDelay": "00:00:02", "ExponentialBackoff": true}, "CircuitBreaker": {"FailureThreshold": 3, "RecoveryTimeout": "00:00:30"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "AllowedHosts": "*"}