using System;
using Identity.Domain.Common;

namespace Identity.Domain.Entities
{
    public class OtpToken : Entity
    {
        public Guid UserId { get; private set; }
        public string Token { get; private set; }
        public OtpPurpose Purpose { get; private set; }
        public string DeliveryMethod { get; private set; } // SMS, Email
        public string DeliveryAddress { get; private set; } // Phone number or email
        public string PhoneNumber => DeliveryMethod == "SMS" ? DeliveryAddress : null; // Computed property for SMS delivery
        public DateTime ExpiresAt { get; private set; }
        public bool IsUsed { get; private set; }
        public DateTime? UsedAt { get; private set; }
        public int AttemptCount { get; private set; }
        public int MaxAttempts { get; private set; }
        public string IpAddress { get; private set; }
        public string UserAgent { get; private set; }

        // Navigation property
        public User User { get; private set; }

        private OtpToken() { }

        public OtpToken(
            Guid userId,
            string token,
            OtpPurpose purpose,
            string deliveryMethod,
            string deliveryAddress,
            DateTime expiresAt,
            int maxAttempts = 3,
            string ipAddress = null,
            string userAgent = null)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new ArgumentException("Token cannot be null or empty", nameof(token));

            if (string.IsNullOrWhiteSpace(deliveryMethod))
                throw new ArgumentException("Delivery method cannot be null or empty", nameof(deliveryMethod));

            if (string.IsNullOrWhiteSpace(deliveryAddress))
                throw new ArgumentException("Delivery address cannot be null or empty", nameof(deliveryAddress));

            if (expiresAt <= DateTime.UtcNow)
                throw new ArgumentException("Expiration time must be in the future", nameof(expiresAt));

            UserId = userId;
            Token = token;
            Purpose = purpose;
            DeliveryMethod = deliveryMethod;
            DeliveryAddress = deliveryAddress;
            ExpiresAt = expiresAt;
            IsUsed = false;
            AttemptCount = 0;
            MaxAttempts = maxAttempts;
            IpAddress = ipAddress;
            UserAgent = userAgent;
            CreatedAt = DateTime.UtcNow;
        }

        public bool IsValid()
        {
            return !IsUsed &&
                   DateTime.UtcNow <= ExpiresAt &&
                   AttemptCount < MaxAttempts;
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpiresAt;
        }

        public void IncrementAttemptCount()
        {
            AttemptCount++;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MarkAsUsed()
        {
            if (IsUsed)
                throw new InvalidOperationException("OTP token is already used");

            if (IsExpired())
                throw new InvalidOperationException("OTP token has expired");

            if (AttemptCount >= MaxAttempts)
                throw new InvalidOperationException("Maximum attempts exceeded");

            IsUsed = true;
            UsedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Invalidate()
        {
            IsUsed = true;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
