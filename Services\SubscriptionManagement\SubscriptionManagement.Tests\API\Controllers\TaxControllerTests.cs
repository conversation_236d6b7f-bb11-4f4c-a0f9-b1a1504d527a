using FluentAssertions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.API.Controllers;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Queries.CalculateTaxForSubscription;
using SubscriptionManagement.Application.Queries.GetPlanTaxDetails;
using System.Security.Claims;
using Xunit;

namespace SubscriptionManagement.Tests.API.Controllers
{
    public class TaxControllerTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<IMemoryCache> _mockCache;
        private readonly Mock<ILogger<TaxController>> _mockLogger;
        private readonly TaxController _controller;

        public TaxControllerTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockCache = new Mock<IMemoryCache>();
            _mockLogger = new Mock<ILogger<TaxController>>();
            _controller = new TaxController(_mockCache.Object, _mockLogger.Object);

            // Setup controller context
            var user = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString())
            }));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };

            // Use reflection to set the Mediator property
            var mediatorProperty = typeof(TaxController).BaseType!
                .GetProperty("Mediator", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            mediatorProperty!.SetValue(_controller, _mockMediator.Object);
        }

        [Fact]
        public async Task GetPlanTaxDetails_WithValidRequest_ShouldReturnOkResult()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var region = "IN";
            var customerId = Guid.NewGuid();

            var expectedResult = new PlanTaxDetailsDto
            {
                PlanId = planId,
                PlanName = "Test Plan",
                BasePrice = 100m,
                Currency = "INR",
                CustomerRegion = region,
                TotalTaxAmount = 18m,
                DisplayPrice = 118m,
                IsTaxInclusive = false,
                TaxBreakdown = new List<TaxBreakdownItemDto>
                {
                    new()
                    {
                        TaxType = Domain.Enums.TaxType.GST,
                        TaxName = "GST",
                        Rate = 18m,
                        TaxableAmount = 100m,
                        TaxAmount = 18m,
                        IsExempt = false
                    }
                },
                CalculatedAt = DateTime.UtcNow
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<GetPlanTaxDetailsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Setup cache miss
            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.GetPlanTaxDetails(planId, region, customerId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(expectedResult);

            _mockMediator.Verify(m => m.Send(
                It.Is<GetPlanTaxDetailsQuery>(q => 
                    q.PlanId == planId && 
                    q.CustomerRegion == region.ToUpperInvariant() && 
                    q.CustomerId == customerId), 
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetPlanTaxDetails_WithCachedResult_ShouldReturnCachedValue()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var region = "IN";

            var cachedResult = new PlanTaxDetailsDto
            {
                PlanId = planId,
                PlanName = "Cached Plan",
                BasePrice = 100m,
                Currency = "INR"
            };

            object? cachedValue = cachedResult;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(true);

            // Act
            var result = await _controller.GetPlanTaxDetails(planId, region);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(cachedResult);

            _mockMediator.Verify(m => m.Send(It.IsAny<GetPlanTaxDetailsQuery>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetPlanTaxDetails_WithInvalidRegion_ShouldReturnBadRequest()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var invalidRegion = "X"; // Too short

            // Act
            var result = await _controller.GetPlanTaxDetails(planId, invalidRegion);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().Be("Invalid region code. Must be between 2 and 10 characters.");
        }

        [Fact]
        public async Task GetPlanTaxDetails_WithNullResult_ShouldReturnNotFound()
        {
            // Arrange
            var planId = Guid.NewGuid();
            var region = "IN";

            _mockMediator.Setup(m => m.Send(It.IsAny<GetPlanTaxDetailsQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((PlanTaxDetailsDto?)null);

            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.GetPlanTaxDetails(planId, region);

            // Assert
            result.Should().BeOfType<NotFoundObjectResult>();
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult!.Value.Should().Be($"Plan with ID {planId} not found or not active");
        }

        [Fact]
        public async Task CalculateTax_WithValidRequest_ShouldReturnOkResult()
        {
            // Arrange
            var request = new TaxCalculationRequestDto
            {
                PlanId = Guid.NewGuid(),
                CustomerRegion = "IN",
                CustomerId = Guid.NewGuid(),
                IncludeTaxBreakdown = true
            };

            var expectedResult = new TaxCalculationResponseDto
            {
                BaseAmount = 100m,
                Currency = "INR",
                TotalTaxAmount = 18m,
                TotalAmount = 118m,
                IsTaxInclusive = false,
                CustomerRegion = "IN",
                CalculatedAt = DateTime.UtcNow,
                TaxItems = new List<TaxBreakdownItemDto>
                {
                    new()
                    {
                        TaxType = Domain.Enums.TaxType.GST,
                        TaxName = "GST",
                        Rate = 18m,
                        TaxableAmount = 100m,
                        TaxAmount = 18m
                    }
                }
            };

            _mockMediator.Setup(m => m.Send(It.IsAny<CalculateTaxForSubscriptionQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.CalculateTax(request);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(expectedResult);

            _mockMediator.Verify(m => m.Send(
                It.Is<CalculateTaxForSubscriptionQuery>(q => 
                    q.PlanId == request.PlanId && 
                    q.CustomerRegion == request.CustomerRegion.ToUpperInvariant() && 
                    q.CustomerId == request.CustomerId &&
                    q.IncludeTaxBreakdown == request.IncludeTaxBreakdown), 
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CalculateTax_WithEmptyPlanId_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new TaxCalculationRequestDto
            {
                PlanId = Guid.Empty,
                CustomerRegion = "IN"
            };

            // Act
            var result = await _controller.CalculateTax(request);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().Be("Plan ID is required");
        }

        [Fact]
        public async Task CalculateTax_WithInvalidRegion_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new TaxCalculationRequestDto
            {
                PlanId = Guid.NewGuid(),
                CustomerRegion = "TOOLONGREGIONCODE" // Too long
            };

            // Act
            var result = await _controller.CalculateTax(request);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().Be("Invalid region code. Must be between 2 and 10 characters.");
        }

        [Fact]
        public async Task GetEffectiveTaxRate_WithValidRegion_ShouldReturnOkResult()
        {
            // Arrange
            var region = "IN";

            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.GetEffectiveTaxRate(region);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            var effectiveRate = okResult!.Value as EffectiveTaxRateDto;
            effectiveRate!.Region.Should().Be(region.ToUpperInvariant());
        }

        [Fact]
        public async Task GetEffectiveTaxRate_WithInvalidRegion_ShouldReturnBadRequest()
        {
            // Arrange
            var invalidRegion = "A"; // Too short

            // Act
            var result = await _controller.GetEffectiveTaxRate(invalidRegion);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().Be("Invalid region code. Must be between 2 and 10 characters.");
        }

        [Fact]
        public async Task GetSupportedRegions_ShouldReturnOkWithRegions()
        {
            // Arrange
            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.GetSupportedRegions();

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            var regions = okResult!.Value as List<TaxRegionDto>;
            regions.Should().NotBeEmpty();
            regions!.Should().Contain(r => r.Code == "IN" && r.Name == "India");
        }

        [Fact]
        public async Task CheckTaxExemption_WithValidRegion_ShouldReturnOkResult()
        {
            // Arrange
            var region = "IN";

            object? cachedValue = null;
            _mockCache.Setup(c => c.TryGetValue(It.IsAny<string>(), out cachedValue))
                .Returns(false);

            // Act
            var result = await _controller.CheckTaxExemption(region);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            var exemptionCheck = okResult!.Value as TaxExemptionCheckDto;
            exemptionCheck!.Region.Should().Be(region.ToUpperInvariant());
            exemptionCheck.CustomerId.Should().NotBeEmpty();
        }

        [Fact]
        public async Task CheckTaxExemption_WithInvalidRegion_ShouldReturnBadRequest()
        {
            // Arrange
            var invalidRegion = ""; // Empty

            // Act
            var result = await _controller.CheckTaxExemption(invalidRegion);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().Be("Invalid region code. Must be between 2 and 10 characters.");
        }
    }
}
