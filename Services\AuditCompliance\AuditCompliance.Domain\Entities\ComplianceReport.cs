using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Compliance report entity for regulatory reporting and monitoring
/// </summary>
public class ComplianceReport : AggregateRoot
{
    public string ReportNumber { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public ComplianceStandard Standard { get; private set; }
    public ComplianceStatus Status { get; private set; }
    public DateTime ReportPeriodStart { get; private set; }
    public DateTime ReportPeriodEnd { get; private set; }
    public Guid GeneratedBy { get; private set; }
    public string GeneratedByName { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? FilePath { get; private set; }
    public string? FileHash { get; private set; }
    public bool IsAutomated { get; private set; }
    public string? Findings { get; private set; }
    public string? Recommendations { get; private set; }
    public int TotalViolations { get; private set; }
    public int CriticalViolations { get; private set; }
    public int HighViolations { get; private set; }
    public int MediumViolations { get; private set; }
    public int LowViolations { get; private set; }

    private readonly List<ComplianceReportItem> _items = new();
    public IReadOnlyList<ComplianceReportItem> Items => _items.AsReadOnly();

    private ComplianceReport()
    {
        ReportNumber = string.Empty;
        Title = string.Empty;
        Description = string.Empty;
        GeneratedByName = string.Empty;
    }

    public ComplianceReport(
        string title,
        string description,
        ComplianceStandard standard,
        DateTime reportPeriodStart,
        DateTime reportPeriodEnd,
        Guid generatedBy,
        string generatedByName,
        bool isAutomated = false)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));
        if (reportPeriodStart >= reportPeriodEnd)
            throw new ArgumentException("Report period start must be before end date");
        if (string.IsNullOrWhiteSpace(generatedByName))
            throw new ArgumentException("Generated by name cannot be empty", nameof(generatedByName));

        ReportNumber = GenerateReportNumber();
        Title = title;
        Description = description;
        Standard = standard;
        Status = ComplianceStatus.Pending;
        ReportPeriodStart = reportPeriodStart;
        ReportPeriodEnd = reportPeriodEnd;
        GeneratedBy = generatedBy;
        GeneratedByName = generatedByName;
        IsAutomated = isAutomated;

        AddDomainEvent(new ComplianceReportCreatedEvent(this));
    }

    public void AddItem(ComplianceReportItem item)
    {
        if (item == null)
            throw new ArgumentNullException(nameof(item));

        _items.Add(item);
        UpdateViolationCounts();
    }

    public void SetInProgress()
    {
        if (Status != ComplianceStatus.Pending)
            throw new InvalidOperationException("Can only set pending reports to in progress");

        Status = ComplianceStatus.InProgress;
        SetUpdatedAt();
    }

    public void Complete(string? findings = null, string? recommendations = null, string? filePath = null, string? fileHash = null)
    {
        if (Status != ComplianceStatus.InProgress)
            throw new InvalidOperationException("Can only complete reports that are in progress");

        Status = DetermineComplianceStatus();
        CompletedAt = DateTime.UtcNow;
        Findings = findings;
        Recommendations = recommendations;
        FilePath = filePath;
        FileHash = fileHash;

        SetUpdatedAt();
        AddDomainEvent(new ComplianceReportCompletedEvent(this));
    }

    public void MarkForReview(string reason)
    {
        Status = ComplianceStatus.RequiresReview;
        Findings = $"Requires manual review: {reason}";
        SetUpdatedAt();
    }

    private ComplianceStatus DetermineComplianceStatus()
    {
        if (CriticalViolations > 0)
            return ComplianceStatus.NonCompliant;

        if (HighViolations > 0 || MediumViolations > 5)
            return ComplianceStatus.PartiallyCompliant;

        return ComplianceStatus.Compliant;
    }

    private void UpdateViolationCounts()
    {
        TotalViolations = _items.Count(i => i.IsViolation);
        CriticalViolations = _items.Count(i => i.IsViolation && i.Severity == AuditSeverity.Critical);
        HighViolations = _items.Count(i => i.IsViolation && i.Severity == AuditSeverity.High);
        MediumViolations = _items.Count(i => i.IsViolation && i.Severity == AuditSeverity.Medium);
        LowViolations = _items.Count(i => i.IsViolation && i.Severity == AuditSeverity.Low);
    }

    private string GenerateReportNumber()
    {
        return $"CR-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }


}
