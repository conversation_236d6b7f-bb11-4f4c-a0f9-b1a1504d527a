# TLI Microservices - Service Testing Script
# This script tests the functionality of all microservices

param(
    [string]$BaseUrl = "http://localhost:5000",
    [switch]$Detailed = $false,
    [switch]$SkipHealthChecks = $false
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Colors for output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Info { param($Message) Write-Host "INFO: $Message" -ForegroundColor Cyan }
function Write-Test { param($Message) Write-Host "🧪 $Message" -ForegroundColor Magenta }

# Test results tracking
$script:TestResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Skipped = 0
}

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null,
        [int]$ExpectedStatus = 200,
        [string]$ExpectedContent = $null
    )
    
    $script:TestResults.Total++
    
    try {
        Write-Test "Testing $Name"
        Write-Info "  URL: $Method $Url"
        
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        $requestParams = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
            TimeoutSec = 30
        }
        
        if ($Body) {
            $requestParams.Body = $Body
            $requestParams.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @requestParams
        $stopwatch.Stop()
        
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        if ($response.StatusCode -eq $ExpectedStatus) {
            $script:TestResults.Passed++
            Write-Success "  Status: $($response.StatusCode) ($responseTime ms)"
            
            if ($ExpectedContent -and $response.Content -notlike "*$ExpectedContent*") {
                Write-Warning "  Expected content '$ExpectedContent' not found"
            }
            
            if ($Detailed) {
                Write-Info "  Response Length: $($response.Content.Length) bytes"
                if ($response.Content.Length -lt 500) {
                    Write-Info "  Response: $($response.Content)"
                }
            }
            
            return @{
                Success = $true
                StatusCode = $response.StatusCode
                ResponseTime = $responseTime
                Content = $response.Content
            }
        } else {
            $script:TestResults.Failed++
            Write-Error "  Unexpected status: $($response.StatusCode) (expected $ExpectedStatus)"
            return @{
                Success = $false
                StatusCode = $response.StatusCode
                ResponseTime = $responseTime
                Content = $response.Content
            }
        }
    }
    catch {
        $script:TestResults.Failed++
        Write-Error "  Request failed: $($_.Exception.Message)"
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$Url)
    
    Write-Info "🏥 Testing $ServiceName Health"
    $result = Test-Endpoint -Name "$ServiceName Health Check" -Url $Url -ExpectedContent "Healthy"
    
    if ($result.Success) {
        Write-Success "$ServiceName is healthy"
    } else {
        Write-Error "$ServiceName is not healthy"
    }
    
    return $result.Success
}

function Test-DocumentManagement {
    Write-Info "📄 Testing Document Management Service"
    
    # Test document upload
    $testDocument = @{
        fileName = "test-document-$(Get-Date -Format 'yyyyMMdd-HHmmss').pdf"
        originalFileName = "Test Document.pdf"
        contentType = "application/pdf"
        fileSize = 1024
        documentType = 1
        category = 1
        description = "Test document from PowerShell script"
        tags = @{
            test = "true"
            source = "powershell"
            timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        }
    } | ConvertTo-Json -Depth 3
    
    $uploadResult = Test-Endpoint -Name "Document Upload" -Url "$BaseUrl/api/documents" -Method "POST" -Body $testDocument -ExpectedStatus 201
    
    # Test document list
    $listResult = Test-Endpoint -Name "Document List" -Url "$BaseUrl/api/documents?pageSize=10`&pageNumber=1"
    
    # Test document search
    $searchResult = Test-Endpoint -Name "Document Search" -Url "$BaseUrl/api/documents/search?query=test"
    
    return ($uploadResult.Success -and $listResult.Success)
}

function Test-MonitoringService {
    Write-Info "📊 Testing Monitoring & Observability Service"
    
    # Test metrics endpoint
    $metricsResult = Test-Endpoint -Name "Metrics API" -Url "$BaseUrl/api/monitoring/metrics"
    
    # Test alerts endpoint
    $alertsResult = Test-Endpoint -Name "Alerts API" -Url "$BaseUrl/api/monitoring/alerts"
    
    # Test health checks endpoint
    $healthChecksResult = Test-Endpoint -Name "Health Checks API" -Url "$BaseUrl/api/monitoring/health-checks"
    
    # Test incidents endpoint
    $incidentsResult = Test-Endpoint -Name "Incidents API" -Url "$BaseUrl/api/monitoring/incidents"
    
    # Test dashboard endpoint
    $dashboardResult = Test-Endpoint -Name "Dashboard API" -Url "$BaseUrl/api/monitoring/dashboard"
    
    return ($metricsResult.Success -and $alertsResult.Success -and $healthChecksResult.Success)
}

function Test-ApiGatewayRouting {
    Write-Info "🌐 Testing API Gateway Routing"
    
    $endpoints = @(
        @{ Name = "Documents"; Url = "$BaseUrl/api/documents" },
        @{ Name = "Storage"; Url = "$BaseUrl/api/storage" },
        @{ Name = "Monitoring Metrics"; Url = "$BaseUrl/api/monitoring/metrics" },
        @{ Name = "Monitoring Alerts"; Url = "$BaseUrl/api/monitoring/alerts" }
    )
    
    $successCount = 0
    foreach ($endpoint in $endpoints) {
        $result = Test-Endpoint -Name "Gateway Routing - $($endpoint.Name)" -Url $endpoint.Url
        if ($result.Success -or $result.StatusCode -eq 404 -or $result.StatusCode -eq 503) {
            # 404 or 503 means routing is working but service might be down
            $successCount++
        }
    }
    
    $routingSuccess = $successCount -eq $endpoints.Count
    if ($routingSuccess) {
        Write-Success "API Gateway routing is working correctly"
    } else {
        Write-Error "API Gateway routing has issues"
    }
    
    return $routingSuccess
}

function Test-PerformanceAndLoad {
    Write-Info "⚡ Testing Performance and Load"
    
    # Test response times
    $responseTimes = @()
    for ($i = 1; $i -le 5; $i++) {
        $result = Test-Endpoint -Name "Performance Test $i" -Url "$BaseUrl/health"
        if ($result.Success) {
            $responseTimes += $result.ResponseTime
        }
    }
    
    if ($responseTimes.Count -gt 0) {
        $avgResponseTime = ($responseTimes | Measure-Object -Average).Average
        Write-Info "  Average response time: $([math]::Round($avgResponseTime, 2))ms"
        
        if ($avgResponseTime -lt 1000) {
            Write-Success "Response times are acceptable"
        } else {
            Write-Warning "Response times are high (>1000ms)"
        }
    }
    
    # Test concurrent requests
    Write-Info "Testing concurrent requests..."
    $jobs = @()
    for ($i = 1; $i -le 10; $i++) {
        $jobs += Start-Job -ScriptBlock {
            param($Url)
            try {
                $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 10
                return @{ Success = $true; StatusCode = $response.StatusCode }
            } catch {
                return @{ Success = $false; Error = $_.Exception.Message }
            }
        } -ArgumentList "$BaseUrl/health"
    }
    
    $results = $jobs | Wait-Job | Receive-Job
    $jobs | Remove-Job
    
    $successfulConcurrent = ($results | Where-Object { $_.Success }).Count
    Write-Info "  Concurrent requests: $successfulConcurrent/10 successful"
    
    if ($successfulConcurrent -ge 8) {
        Write-Success "Concurrent request handling is good"
    } else {
        Write-Warning "Concurrent request handling needs improvement"
    }
}

# Main execution
Write-Host "🚀 TLI Microservices - Service Testing" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Cyan
Write-Host "Detailed Output: $Detailed" -ForegroundColor Cyan
Write-Host ""

# Test service health
if (-not $SkipHealthChecks) {
    Write-Host "🏥 HEALTH CHECKS" -ForegroundColor Yellow
    Write-Host "=================" -ForegroundColor Yellow
    
    $healthyServices = 0
    $totalServices = 0
    
    # API Gateway
    $totalServices++
    if (Test-ServiceHealth -ServiceName "API Gateway" -Url "$BaseUrl/health") {
        $healthyServices++
    }
    
    # Data Storage Service
    $totalServices++
    if (Test-ServiceHealth -ServiceName "Data Storage" -Url "http://localhost:5010/health") {
        $healthyServices++
    }
    
    # Data Storage via Gateway
    $totalServices++
    if (Test-ServiceHealth -ServiceName "Data Storage (via Gateway)" -Url "$BaseUrl/health/datastorage") {
        $healthyServices++
    }
    
    # Monitoring Service
    $totalServices++
    if (Test-ServiceHealth -ServiceName "Monitoring" -Url "http://localhost:5011/health") {
        $healthyServices++
    }
    
    # Monitoring via Gateway
    $totalServices++
    if (Test-ServiceHealth -ServiceName "Monitoring (via Gateway)" -Url "$BaseUrl/health/monitoring") {
        $healthyServices++
    }
    
    Write-Host ""
    Write-Info "Health Summary: $healthyServices/$totalServices services are healthy"
    Write-Host ""
}

# Test API functionality
Write-Host "🧪 API FUNCTIONALITY TESTS" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow

Test-ApiGatewayRouting
Write-Host ""

Test-DocumentManagement
Write-Host ""

Test-MonitoringService
Write-Host ""

# Performance tests
Write-Host "⚡ PERFORMANCE TESTS" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

Test-PerformanceAndLoad
Write-Host ""

# Summary
Write-Host "📊 TEST SUMMARY" -ForegroundColor Yellow
Write-Host "===============" -ForegroundColor Yellow
Write-Host "Total Tests: $($script:TestResults.Total)" -ForegroundColor White
Write-Host "Passed: $($script:TestResults.Passed)" -ForegroundColor Green
Write-Host "Failed: $($script:TestResults.Failed)" -ForegroundColor Red
Write-Host "Skipped: $($script:TestResults.Skipped)" -ForegroundColor Yellow

$successRate = if ($script:TestResults.Total -gt 0) { 
    [math]::Round(($script:TestResults.Passed / $script:TestResults.Total) * 100, 1) 
} else { 0 }

Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host ""
if ($script:TestResults.Failed -eq 0) {
    Write-Success "All tests passed! 🎉"
} elseif ($successRate -ge 80) {
    Write-Warning "Most tests passed, but some issues detected."
} else {
    Write-Error "Multiple test failures detected. Please check service status."
}

Write-Host ""
Write-Info "Next steps:"
Write-Host "1. Open the monitoring dashboard: Tests/Dashboard/monitoring-dashboard.html" -ForegroundColor White
Write-Host "2. Check service logs for any errors" -ForegroundColor White
Write-Host "3. Verify database connectivity and migrations" -ForegroundColor White
