using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Events
{
    // Tax Category Events
    public record TaxCategoryCreatedEvent(Guid TaxCategoryId, string Name, string Code) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxCategoryUpdatedEvent(Guid TaxCategoryId, string OldName, string NewName, string Description) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxCategoryActivatedEvent(Guid TaxCategoryId, string Name) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxCategoryDeactivatedEvent(Guid TaxCategoryId, string Name) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxConfigurationAddedEvent(Guid TaxCategoryId, TaxType TaxType, decimal Rate) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxConfigurationRemovedEvent(Guid TaxCategoryId, TaxType TaxType) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    // Tax Exemption Events
    public record TaxExemptionCreatedEvent(Guid TaxExemptionId, Guid UserId, string ExemptionType, string ExemptionNumber) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxExemptionUpdatedEvent(Guid TaxExemptionId, Guid UserId, string ExemptionType, string ExemptionNumber) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxExemptionVerifiedEvent(Guid TaxExemptionId, Guid UserId, Guid VerifiedByUserId) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxExemptionActivatedEvent(Guid TaxExemptionId, Guid UserId) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record TaxExemptionDeactivatedEvent(Guid TaxExemptionId, Guid UserId) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    // Plan Tax Configuration Events
    public record PlanTaxConfigurationChangedEvent(Guid PlanId, string PlanName, TaxType TaxType, decimal OldRate, decimal NewRate) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record PlanTaxConfigurationAddedEvent(Guid PlanId, string PlanName, TaxType TaxType, decimal Rate, List<string> ApplicableRegions) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record PlanTaxConfigurationRemovedEvent(Guid PlanId, string PlanName, TaxType TaxType) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    // Global Tax Configuration Events
    public record GlobalTaxConfigurationChangedEvent(TaxType TaxType, decimal OldRate, decimal NewRate, List<string> ApplicableRegions) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record GlobalTaxConfigurationCreatedEvent(TaxType TaxType, decimal Rate, List<string> ApplicableRegions, bool IsIncluded) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record GlobalTaxConfigurationDeactivatedEvent(TaxType TaxType, List<string> ApplicableRegions) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    // Plan Tax Category Events
    public record PlanTaxCategoryChangedEvent(Guid PlanId, string PlanName, Guid TaxCategoryId, string TaxCategoryName) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }

    public record PlanTaxCategoryRemovedEvent(Guid PlanId, string PlanName, Guid OldTaxCategoryId, string OldTaxCategoryName) : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
    }
}
