using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Shipper;

/// <summary>
/// Query to get shipper analytics dashboard
/// </summary>
public record GetShipperDashboardQuery(
    Guid ShipperId,
    DateTime? FromDate = null,
    DateTime? ToDate = null,
    TimePeriod Period = TimePeriod.Daily
) : IRequest<ShipperDashboardDto>;

/// <summary>
/// Query to get shipper SLA performance metrics
/// </summary>
public record GetShipperSLAPerformanceQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeOnTimeDelivery = true,
    bool IncludePerformanceKPIs = true
) : IRequest<ShipperSLAPerformanceDto>;

/// <summary>
/// Query to get shipper cost analysis
/// </summary>
public record GetShipperCostAnalysisQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeCostPerTrip = true,
    bool IncludeFreightOptimization = true,
    bool IncludeRouteEfficiency = true
) : IRequest<ShipperCostAnalysisDto>;

/// <summary>
/// Query to get shipper provider comparison
/// </summary>
public record GetShipperProviderComparisonQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    List<Guid>? TransportCompanyIds = null,
    string? ComparisonMetric = null,
    bool IncludePerformanceBenchmarking = true
) : IRequest<ShipperProviderComparisonDto>;

/// <summary>
/// Query to get shipper business reporting
/// </summary>
public record GetShipperBusinessReportingQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeShipmentReports = true,
    bool IncludeTrendAnalysis = true
) : IRequest<ShipperBusinessReportingDto>;

/// <summary>
/// Query to get shipper route efficiency analysis
/// </summary>
public record GetShipperRouteEfficiencyQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    string? OriginCity = null,
    string? DestinationCity = null,
    bool IncludeCostPerKilometer = true
) : IRequest<ShipperRouteEfficiencyDto>;

/// <summary>
/// Query to get shipper shipment tracking analytics
/// </summary>
public record GetShipperShipmentTrackingQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? ShipmentStatus = null,
    bool IncludeDeliveryMetrics = true
) : IRequest<ShipperShipmentTrackingDto>;

/// <summary>
/// Query to get shipper freight optimization analytics
/// </summary>
public record GetShipperFreightOptimizationQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeLoadOptimization = true,
    bool IncludeCarrierOptimization = true,
    bool IncludeRouteOptimization = true
) : IRequest<ShipperFreightOptimizationDto>;

/// <summary>
/// Query to get shipper service quality analytics
/// </summary>
public record GetShipperServiceQualityQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeProviderRatings = true,
    bool IncludeServiceLevelAnalysis = true
) : IRequest<ShipperServiceQualityDto>;

/// <summary>
/// Query to get shipper financial performance
/// </summary>
public record GetShipperFinancialPerformanceQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeShippingCosts = true,
    bool IncludeCostSavings = true,
    bool IncludeBudgetAnalysis = true
) : IRequest<ShipperFinancialPerformanceDto>;

/// <summary>
/// Query to get shipper compliance analytics
/// </summary>
public record GetShipperComplianceAnalyticsQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    string? ComplianceType = null,
    string? Region = null,
    bool IncludeViolations = true
) : IRequest<ShipperComplianceAnalyticsDto>;

/// <summary>
/// Query to get shipper capacity planning analytics
/// </summary>
public record GetShipperCapacityPlanningQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeDemandForecasting = true,
    bool IncludeCapacityUtilization = true
) : IRequest<ShipperCapacityPlanningDto>;

/// <summary>
/// Query to get shipper sustainability analytics
/// </summary>
public record GetShipperSustainabilityAnalyticsQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeCarbonFootprint = true,
    bool IncludeEnvironmentalImpact = true
) : IRequest<ShipperSustainabilityAnalyticsDto>;

/// <summary>
/// Query to get shipper risk management analytics
/// </summary>
public record GetShipperRiskManagementQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    bool IncludeDeliveryRisk = true,
    bool IncludeProviderRisk = true,
    bool IncludeOperationalRisk = true
) : IRequest<ShipperRiskManagementDto>;

/// <summary>
/// Query to get shipper market intelligence
/// </summary>
public record GetShipperMarketIntelligenceQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    string? MarketSegment = null,
    string? GeographicRegion = null,
    bool IncludePricingTrends = true
) : IRequest<ShipperMarketIntelligenceDto>;

/// <summary>
/// Query to get shipper operational insights
/// </summary>
public record GetShipperOperationalInsightsQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeEfficiencyInsights = true,
    bool IncludeOptimizationRecommendations = true
) : IRequest<ShipperOperationalInsightsDto>;

/// <summary>
/// Query to get shipper technology utilization analytics
/// </summary>
public record GetShipperTechnologyUtilizationQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeSystemUsage = true,
    bool IncludeFeatureAdoption = true
) : IRequest<ShipperTechnologyUtilizationDto>;

/// <summary>
/// Query to get shipper customer satisfaction analytics
/// </summary>
public record GetShipperCustomerSatisfactionQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeInternalSatisfaction = true,
    bool IncludeEndCustomerFeedback = true
) : IRequest<ShipperCustomerSatisfactionDto>;

/// <summary>
/// Query to get shipper shipment trends analytics
/// </summary>
public record GetShipperShipmentTrendsQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Monthly,
    bool IncludeVolumeAnalysis = true,
    bool IncludeSeasonalTrends = true,
    bool IncludeForecasting = true
) : IRequest<ShipperShipmentTrendsDto>;

/// <summary>
/// Query to get shipper competitive benchmarking
/// </summary>
public record GetShipperCompetitiveBenchmarkingQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    string? IndustrySegment = null,
    string? GeographicRegion = null,
    bool IncludePerformanceComparison = true
) : IRequest<ShipperCompetitiveBenchmarkingDto>;

/// <summary>
/// Query to get shipper supply chain analytics
/// </summary>
public record GetShipperSupplyChainAnalyticsQuery(
    Guid ShipperId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeSupplyChainVisibility = true,
    bool IncludePerformanceMetrics = true
) : IRequest<ShipperSupplyChainAnalyticsDto>;
