using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Rate limiting service for notification channels
/// </summary>
public interface IRateLimitingService
{
    /// <summary>
    /// Check if request is allowed based on rate limits
    /// </summary>
    Task<bool> IsAllowedAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get remaining quota for a key
    /// </summary>
    Task<RateLimitInfo> GetRateLimitInfoAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reset rate limit for a key
    /// </summary>
    Task ResetRateLimitAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default);
}

/// <summary>
/// Rate limit types
/// </summary>
public enum RateLimitType
{
    GlobalPerSecond,
    GlobalPerMinute,
    GlobalPerHour,
    UserPerMinute,
    UserPer<PERSON>our,
    ChannelPerSecond,
    ChannelPerMinute
}

/// <summary>
/// Rate limit information
/// </summary>
public class RateLimitInfo
{
    public int Limit { get; set; }
    public int Remaining { get; set; }
    public DateTime ResetTime { get; set; }
    public bool IsAllowed => Remaining > 0;
}

/// <summary>
/// In-memory rate limiting service implementation
/// </summary>
public class InMemoryRateLimitingService : IRateLimitingService
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<InMemoryRateLimitingService> _logger;
    private readonly RateLimitConfiguration _configuration;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _semaphores = new();

    public InMemoryRateLimitingService(
        IMemoryCache cache,
        IConfiguration configuration,
        ILogger<InMemoryRateLimitingService> logger)
    {
        _cache = cache;
        _logger = logger;
        _configuration = configuration.GetSection("NotificationSettings:RateLimiting").Get<RateLimitConfiguration>()
            ?? new RateLimitConfiguration();
    }

    public async Task<bool> IsAllowedAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        if (!_configuration.EnableGlobalRateLimit)
        {
            return true;
        }

        var rateLimitInfo = await GetRateLimitInfoAsync(key, limitType, cancellationToken);
        
        if (!rateLimitInfo.IsAllowed)
        {
            _logger.LogWarning("Rate limit exceeded for key {Key}, type {LimitType}. Limit: {Limit}, Remaining: {Remaining}",
                key, limitType, rateLimitInfo.Limit, rateLimitInfo.Remaining);
            return false;
        }

        // Decrement the counter
        await DecrementCounterAsync(key, limitType, cancellationToken);
        return true;
    }

    public async Task<RateLimitInfo> GetRateLimitInfoAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        var (limit, window) = GetLimitAndWindow(limitType);
        var cacheKey = GetCacheKey(key, limitType);
        
        var semaphore = _semaphores.GetOrAdd(cacheKey, _ => new SemaphoreSlim(1, 1));
        await semaphore.WaitAsync(cancellationToken);

        try
        {
            var counter = _cache.Get<RateLimitCounter>(cacheKey);
            
            if (counter == null || counter.ResetTime <= DateTime.UtcNow)
            {
                // Create new counter or reset expired one
                counter = new RateLimitCounter
                {
                    Count = 0,
                    ResetTime = DateTime.UtcNow.Add(window)
                };
                
                _cache.Set(cacheKey, counter, window);
            }

            return new RateLimitInfo
            {
                Limit = limit,
                Remaining = Math.Max(0, limit - counter.Count),
                ResetTime = counter.ResetTime
            };
        }
        finally
        {
            semaphore.Release();
        }
    }

    public async Task ResetRateLimitAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(key, limitType);
        var semaphore = _semaphores.GetOrAdd(cacheKey, _ => new SemaphoreSlim(1, 1));
        
        await semaphore.WaitAsync(cancellationToken);
        
        try
        {
            _cache.Remove(cacheKey);
            _logger.LogInformation("Rate limit reset for key {Key}, type {LimitType}", key, limitType);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private async Task DecrementCounterAsync(string key, RateLimitType limitType, CancellationToken cancellationToken)
    {
        var (_, window) = GetLimitAndWindow(limitType);
        var cacheKey = GetCacheKey(key, limitType);
        
        var semaphore = _semaphores.GetOrAdd(cacheKey, _ => new SemaphoreSlim(1, 1));
        await semaphore.WaitAsync(cancellationToken);

        try
        {
            var counter = _cache.Get<RateLimitCounter>(cacheKey);
            
            if (counter != null && counter.ResetTime > DateTime.UtcNow)
            {
                counter.Count++;
                _cache.Set(cacheKey, counter, window);
            }
        }
        finally
        {
            semaphore.Release();
        }
    }

    private (int limit, TimeSpan window) GetLimitAndWindow(RateLimitType limitType)
    {
        return limitType switch
        {
            RateLimitType.GlobalPerSecond => (_configuration.GlobalMessagesPerSecond, TimeSpan.FromSeconds(1)),
            RateLimitType.GlobalPerMinute => (_configuration.GlobalMessagesPerMinute, TimeSpan.FromMinutes(1)),
            RateLimitType.GlobalPerHour => (_configuration.GlobalMessagesPerHour, TimeSpan.FromHours(1)),
            RateLimitType.UserPerMinute => (_configuration.PerUserMessagesPerMinute, TimeSpan.FromMinutes(1)),
            RateLimitType.UserPerHour => (_configuration.PerUserMessagesPerHour, TimeSpan.FromHours(1)),
            RateLimitType.ChannelPerSecond => (_configuration.ChannelMessagesPerSecond, TimeSpan.FromSeconds(1)),
            RateLimitType.ChannelPerMinute => (_configuration.ChannelMessagesPerMinute, TimeSpan.FromMinutes(1)),
            _ => (1000, TimeSpan.FromMinutes(1))
        };
    }

    private static string GetCacheKey(string key, RateLimitType limitType)
    {
        return $"rate_limit:{limitType}:{key}";
    }
}

/// <summary>
/// Rate limit counter
/// </summary>
internal class RateLimitCounter
{
    public int Count { get; set; }
    public DateTime ResetTime { get; set; }
}

/// <summary>
/// Rate limit configuration
/// </summary>
public class RateLimitConfiguration
{
    public bool EnableGlobalRateLimit { get; set; } = true;
    public int GlobalMessagesPerSecond { get; set; } = 50;
    public int GlobalMessagesPerMinute { get; set; } = 3000;
    public int GlobalMessagesPerHour { get; set; } = 100000;
    public int PerUserMessagesPerMinute { get; set; } = 10;
    public int PerUserMessagesPerHour { get; set; } = 100;
    public int ChannelMessagesPerSecond { get; set; } = 10;
    public int ChannelMessagesPerMinute { get; set; } = 600;
}

/// <summary>
/// Redis-based rate limiting service (for distributed scenarios)
/// </summary>
public class RedisRateLimitingService : IRateLimitingService
{
    private readonly ILogger<RedisRateLimitingService> _logger;
    private readonly RateLimitConfiguration _configuration;

    public RedisRateLimitingService(
        IConfiguration configuration,
        ILogger<RedisRateLimitingService> logger)
    {
        _logger = logger;
        _configuration = configuration.GetSection("NotificationSettings:RateLimiting").Get<RateLimitConfiguration>()
            ?? new RateLimitConfiguration();
    }

    public async Task<bool> IsAllowedAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        // TODO: Implement Redis-based rate limiting
        // This would use Redis INCR and EXPIRE commands for distributed rate limiting
        _logger.LogWarning("Redis rate limiting not implemented yet, allowing request");
        return true;
    }

    public async Task<RateLimitInfo> GetRateLimitInfoAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        // TODO: Implement Redis-based rate limit info retrieval
        var (limit, _) = GetLimitAndWindow(limitType);
        
        return new RateLimitInfo
        {
            Limit = limit,
            Remaining = limit,
            ResetTime = DateTime.UtcNow.AddMinutes(1)
        };
    }

    public async Task ResetRateLimitAsync(string key, RateLimitType limitType, CancellationToken cancellationToken = default)
    {
        // TODO: Implement Redis-based rate limit reset
        _logger.LogInformation("Redis rate limit reset not implemented yet for key {Key}, type {LimitType}", key, limitType);
    }

    private (int limit, TimeSpan window) GetLimitAndWindow(RateLimitType limitType)
    {
        return limitType switch
        {
            RateLimitType.GlobalPerSecond => (_configuration.GlobalMessagesPerSecond, TimeSpan.FromSeconds(1)),
            RateLimitType.GlobalPerMinute => (_configuration.GlobalMessagesPerMinute, TimeSpan.FromMinutes(1)),
            RateLimitType.GlobalPerHour => (_configuration.GlobalMessagesPerHour, TimeSpan.FromHours(1)),
            RateLimitType.UserPerMinute => (_configuration.PerUserMessagesPerMinute, TimeSpan.FromMinutes(1)),
            RateLimitType.UserPerHour => (_configuration.PerUserMessagesPerHour, TimeSpan.FromHours(1)),
            RateLimitType.ChannelPerSecond => (_configuration.ChannelMessagesPerSecond, TimeSpan.FromSeconds(1)),
            RateLimitType.ChannelPerMinute => (_configuration.ChannelMessagesPerMinute, TimeSpan.FromMinutes(1)),
            _ => (1000, TimeSpan.FromMinutes(1))
        };
    }
}
