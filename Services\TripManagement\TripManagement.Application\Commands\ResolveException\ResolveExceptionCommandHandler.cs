using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Commands.ResolveException;

public class ResolveExceptionCommandHandler : IRequestHandler<ResolveExceptionCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ResolveExceptionCommandHandler> _logger;
    private readonly IMessageBroker _messageBroker;
    private readonly INotificationService _notificationService;

    public ResolveExceptionCommandHandler(
        ITripRepository tripRepository,
        IUnitOfWork unitOfWork,
        ILogger<ResolveExceptionCommandHandler> logger,
        IMessageBroker messageBroker,
        INotificationService notificationService)
    {
        _tripRepository = tripRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _messageBroker = messageBroker;
        _notificationService = notificationService;
    }

    public async Task<bool> Handle(ResolveExceptionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Resolving exception {ExceptionId} for trip {TripId}", 
                request.ExceptionId, request.TripId);

            // Get the trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                return false;
            }

            // Find the exception
            var exception = trip.Exceptions.FirstOrDefault(e => e.Id == request.ExceptionId);
            if (exception == null)
            {
                _logger.LogWarning("Exception {ExceptionId} not found in trip {TripId}", 
                    request.ExceptionId, request.TripId);
                return false;
            }

            if (exception.IsResolved)
            {
                _logger.LogWarning("Exception {ExceptionId} is already resolved", request.ExceptionId);
                return false;
            }

            // Resolve the exception
            trip.ResolveException(request.ExceptionId, request.Resolution);

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Send notification to relevant parties
            await _notificationService.SendNotificationAsync(
                trip.CarrierId,
                "Exception Resolved",
                $"Exception '{exception.Description}' has been resolved for trip {trip.TripNumber}. Resolution: {request.Resolution}",
                cancellationToken);

            if (trip.DriverId.HasValue)
            {
                await _notificationService.SendNotificationAsync(
                    trip.DriverId.Value,
                    "Exception Resolved",
                    $"Exception has been resolved for your trip {trip.TripNumber}. You can continue with the trip.",
                    cancellationToken);
            }

            // Publish integration event
            await _messageBroker.PublishAsync("trip.exception_resolved", new
            {
                TripId = request.TripId,
                ExceptionId = request.ExceptionId,
                ExceptionType = exception.ExceptionType.ToString(),
                Resolution = request.Resolution,
                ResolvedBy = request.ResolvedBy,
                ResolvedAt = DateTime.UtcNow,
                TripStatus = trip.Status.ToString()
            }, cancellationToken);

            _logger.LogInformation("Exception {ExceptionId} resolved successfully for trip {TripId}", 
                request.ExceptionId, request.TripId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving exception {ExceptionId} for trip {TripId}", 
                request.ExceptionId, request.TripId);
            throw;
        }
    }
}
