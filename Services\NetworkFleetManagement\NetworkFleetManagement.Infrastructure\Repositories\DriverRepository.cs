using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class DriverRepository : IDriverRepository
{
    private readonly NetworkFleetDbContext _context;

    public DriverRepository(NetworkFleetDbContext context)
    {
        _context = context;
    }

    public async Task<Driver?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Include(d => d.Documents)
            .Include(d => d.VehicleAssignments)
            .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
    }

    public async Task<Driver?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Include(d => d.Documents)
            .Include(d => d.VehicleAssignments)
            .FirstOrDefaultAsync(d => d.UserId == userId, cancellationToken);
    }

    public async Task<Driver?> GetByLicenseNumberAsync(string licenseNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Include(d => d.Documents)
            .Include(d => d.VehicleAssignments)
            .FirstOrDefaultAsync(d => d.LicenseNumber == licenseNumber, cancellationToken);
    }

    public async Task<IEnumerable<Driver>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Include(d => d.Documents)
            .Include(d => d.VehicleAssignments)
            .Where(d => d.CarrierId == carrierId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Driver>> GetByStatusAsync(DriverStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Where(d => d.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Driver>> GetAvailableDriversAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Where(d => d.Status == DriverStatus.Available &&
                       d.LicenseExpiryDate > today &&
                       d.IsVerified)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Driver>> GetDriversRequiringLicenseRenewalAsync(int daysThreshold = 30, CancellationToken cancellationToken = default)
    {
        var thresholdDate = DateTime.UtcNow.AddDays(daysThreshold);
        
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Where(d => d.LicenseExpiryDate <= thresholdDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Driver>> GetDriversByOperationalAreaAsync(string area, CancellationToken cancellationToken = default)
    {
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Where(d => d.OperationalAreas.Contains(area) || d.OperationalAreas.Count == 0)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Driver>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var lowerSearchTerm = searchTerm.ToLower();
        
        return await _context.Drivers
            .Include(d => d.Carrier)
            .Where(d => d.FirstName.ToLower().Contains(lowerSearchTerm) ||
                       d.LastName.ToLower().Contains(lowerSearchTerm) ||
                       d.Email.ToLower().Contains(lowerSearchTerm) ||
                       d.PhoneNumber.Contains(searchTerm) ||
                       d.LicenseNumber.ToLower().Contains(lowerSearchTerm) ||
                       d.Carrier.CompanyName.ToLower().Contains(lowerSearchTerm))
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Driver> Drivers, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? carrierId = null,
        DriverStatus? status = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Drivers
            .Include(d => d.Carrier)
            .AsQueryable();

        // Apply filters
        if (carrierId.HasValue)
        {
            query = query.Where(d => d.CarrierId == carrierId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(d => d.Status == status.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(d => d.FirstName.ToLower().Contains(lowerSearchTerm) ||
                                   d.LastName.ToLower().Contains(lowerSearchTerm) ||
                                   d.Email.ToLower().Contains(lowerSearchTerm) ||
                                   d.PhoneNumber.Contains(searchTerm) ||
                                   d.LicenseNumber.ToLower().Contains(lowerSearchTerm) ||
                                   d.Carrier.CompanyName.ToLower().Contains(lowerSearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var drivers = await query
            .OrderBy(d => d.FirstName)
            .ThenBy(d => d.LastName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (drivers, totalCount);
    }

    public void Add(Driver driver)
    {
        _context.Drivers.Add(driver);
    }

    public void Update(Driver driver)
    {
        _context.Drivers.Update(driver);
    }

    public void Remove(Driver driver)
    {
        _context.Drivers.Remove(driver);
    }
}
