using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Application.DTOs;

public class VehicleDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public VehicleType VehicleType { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int Year { get; set; }
    public string Color { get; set; } = string.Empty;
    public VehicleSpecificationsDto Specifications { get; set; } = new();
    public VehicleStatus Status { get; set; }
    public LocationDto? CurrentLocation { get; set; }
    public DateTime? LastMaintenanceDate { get; set; }
    public DateTime? NextMaintenanceDate { get; set; }
    public string? InsuranceNumber { get; set; }
    public DateTime? InsuranceExpiryDate { get; set; }
    public string? FitnessNumber { get; set; }
    public DateTime? FitnessExpiryDate { get; set; }
    public string? PermitNumber { get; set; }
    public DateTime? PermitExpiryDate { get; set; }
    public string? Notes { get; set; }
    public decimal? DailyEarnings { get; set; }
    public decimal? MonthlyEarnings { get; set; }
    public int? UtilizationHours { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public bool IsInsuranceValid { get; set; }
    public bool IsFitnessValid { get; set; }
    public bool IsPermitValid { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Related data
    public string CarrierCompanyName { get; set; } = string.Empty;
    public int DocumentCount { get; set; }
    public int MaintenanceRecordCount { get; set; }
}

public class VehicleSummaryDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public VehicleType VehicleType { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int Year { get; set; }
    public VehicleStatus Status { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public bool IsAvailable { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class CreateVehicleDto
{
    public Guid CarrierId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public VehicleType VehicleType { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int Year { get; set; }
    public string Color { get; set; } = string.Empty;
    public VehicleSpecificationsDto Specifications { get; set; } = new();
    public string? InsuranceNumber { get; set; }
    public DateTime? InsuranceExpiryDate { get; set; }
    public string? FitnessNumber { get; set; }
    public DateTime? FitnessExpiryDate { get; set; }
    public string? PermitNumber { get; set; }
    public DateTime? PermitExpiryDate { get; set; }
    public string? Notes { get; set; }
}

public class UpdateVehicleDto
{
    public string RegistrationNumber { get; set; } = string.Empty;
    public VehicleType VehicleType { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int Year { get; set; }
    public string Color { get; set; } = string.Empty;
    public VehicleSpecificationsDto Specifications { get; set; } = new();
    public string? InsuranceNumber { get; set; }
    public DateTime? InsuranceExpiryDate { get; set; }
    public string? FitnessNumber { get; set; }
    public DateTime? FitnessExpiryDate { get; set; }
    public string? PermitNumber { get; set; }
    public DateTime? PermitExpiryDate { get; set; }
    public string? Notes { get; set; }
}

public class VehicleSpecificationsDto
{
    public decimal LoadCapacityKg { get; set; }
    public decimal VolumeCapacityM3 { get; set; }
    public decimal? FuelCapacityLiters { get; set; }
    public int? MaxSpeed { get; set; }
    public decimal? Length { get; set; }
    public decimal? Width { get; set; }
    public decimal? Height { get; set; }
    public string? EngineType { get; set; }
    public string? FuelType { get; set; }
}

public class UpdateVehicleStatusDto
{
    public VehicleStatus Status { get; set; }
}

public class ScheduleMaintenanceDto
{
    public DateTime ScheduledDate { get; set; }
    public string? Notes { get; set; }
}

public class CompleteMaintenanceDto
{
    public DateTime CompletedDate { get; set; }
    public string? Notes { get; set; }
    public decimal? Cost { get; set; }
}

public class VehicleDashboardDto
{
    public Guid Id { get; set; }
    public Guid CarrierId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public VehicleType VehicleType { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int Year { get; set; }
    public VehicleStatus Status { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public bool IsAvailable { get; set; }
    public string CarrierCompanyName { get; set; } = string.Empty;

    // Document Status Information
    public bool IsInsuranceValid { get; set; }
    public bool IsFitnessValid { get; set; }
    public bool IsPermitValid { get; set; }
    public DateTime? InsuranceExpiryDate { get; set; }
    public DateTime? FitnessExpiryDate { get; set; }
    public DateTime? PermitExpiryDate { get; set; }
    public int ExpiredDocumentsCount { get; set; }
    public int ExpiringDocumentsCount { get; set; } // Expiring within 30 days

    // Maintenance Information
    public DateTime? LastMaintenanceDate { get; set; }
    public DateTime? NextMaintenanceDate { get; set; }
    public bool RequiresMaintenanceSoon { get; set; }
    public int DaysUntilMaintenance { get; set; }

    // Performance Metrics
    public decimal? DailyEarnings { get; set; }
    public decimal? MonthlyEarnings { get; set; }
    public int? UtilizationHours { get; set; }
    public decimal? UtilizationPercentage { get; set; }

    // Current Assignment
    public Guid? CurrentDriverId { get; set; }
    public string? CurrentDriverName { get; set; }
    public bool IsAssigned { get; set; }

    // Location and Trip Information
    public LocationDto? CurrentLocation { get; set; }
    public Guid? CurrentTripId { get; set; }
    public string? CurrentTripNumber { get; set; }

    // Summary Counts
    public int TotalTripsCompleted { get; set; }
    public int DocumentCount { get; set; }
    public int MaintenanceRecordCount { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class VehicleDocumentStatusDto
{
    public string DocumentType { get; set; } = string.Empty;
    public bool IsValid { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public string Status { get; set; } = string.Empty; // Valid, Expired, Expiring
    public string StatusColor { get; set; } = string.Empty; // Green, Red, Orange
}

public class UpdateVehicleLocationDto
{
    public LocationDto Location { get; set; } = new();
}

public class VehicleMaintenanceRecordDto
{
    public Guid Id { get; set; }
    public Guid VehicleId { get; set; }
    public DateTime MaintenanceDate { get; set; }
    public string? Description { get; set; }
    public decimal? Cost { get; set; }
    public string? ServiceProvider { get; set; }
    public string? InvoiceNumber { get; set; }
    public int? OdometerReading { get; set; }
    public string? PartsReplaced { get; set; }
    public string? NextMaintenanceNotes { get; set; }
    public DateTime? NextMaintenanceDate { get; set; }
    public bool IsWarrantyWork { get; set; }
    public string? WarrantyDetails { get; set; }
    public DateTime CreatedAt { get; set; }
}
