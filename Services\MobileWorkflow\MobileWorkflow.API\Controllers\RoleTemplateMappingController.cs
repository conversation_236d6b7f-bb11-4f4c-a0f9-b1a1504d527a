using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MobileWorkflow.Application.Commands.Milestone;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Queries.Milestone;

namespace MobileWorkflow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RoleTemplateMappingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<RoleTemplateMappingController> _logger;

    public RoleTemplateMappingController(IMediator mediator, ILogger<RoleTemplateMappingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all role template mappings
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<RoleTemplateMappingDto>>> GetRoleTemplateMappings()
    {
        try
        {
            var mappings = await _mediator.Send(new GetAllRoleTemplateMappingsQuery());
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role template mappings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get role template mapping by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<RoleTemplateMappingDto>> GetRoleTemplateMapping(Guid id)
    {
        try
        {
            var mapping = await _mediator.Send(new GetRoleTemplateMappingByIdQuery { Id = id });
            if (mapping == null)
            {
                return NotFound($"Role template mapping with ID {id} not found");
            }

            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role template mapping: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get role template mappings by role name
    /// </summary>
    [HttpGet("by-role/{roleName}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<IEnumerable<RoleTemplateMappingDto>>> GetRoleTemplateMappingsByRole(string roleName, [FromQuery] bool? activeOnly = null)
    {
        try
        {
            IEnumerable<RoleTemplateMappingDto> mappings;

            if (activeOnly == true)
            {
                mappings = await _mediator.Send(new GetActiveRoleTemplateMappingsByRoleNameQuery { RoleName = roleName });
            }
            else
            {
                mappings = await _mediator.Send(new GetRoleTemplateMappingsByRoleNameQuery { RoleName = roleName });
            }

            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role template mappings by role: {RoleName}", roleName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get role template mappings by template ID
    /// </summary>
    [HttpGet("by-template/{templateId}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<RoleTemplateMappingDto>>> GetRoleTemplateMappingsByTemplate(Guid templateId)
    {
        try
        {
            var mappings = await _mediator.Send(new GetRoleTemplateMappingsByTemplateIdQuery { TemplateId = templateId });
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role template mappings by template: {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get default role template mapping by role name
    /// </summary>
    [HttpGet("default/{roleName}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<RoleTemplateMappingDto>> GetDefaultRoleTemplateMappingByRole(string roleName)
    {
        try
        {
            var mapping = await _mediator.Send(new GetDefaultRoleTemplateMappingByRoleNameQuery { RoleName = roleName });
            if (mapping == null)
            {
                return NotFound($"No default role template mapping found for role '{roleName}'");
            }

            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting default role template mapping by role: {RoleName}", roleName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get best matching role template mapping based on context
    /// </summary>
    [HttpPost("best-match/{roleName}")]
    [Authorize(Roles = "Admin,Manager,User")]
    public async Task<ActionResult<RoleTemplateMappingDto>> GetBestRoleTemplateMapping(string roleName, [FromBody] Dictionary<string, object> context)
    {
        try
        {
            var mapping = await _mediator.Send(new GetBestRoleTemplateMappingQuery 
            { 
                RoleName = roleName, 
                Context = context ?? new Dictionary<string, object>() 
            });

            if (mapping == null)
            {
                return NotFound($"No matching role template mapping found for role '{roleName}'");
            }

            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting best role template mapping for role: {RoleName}", roleName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all distinct role names
    /// </summary>
    [HttpGet("roles")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<IEnumerable<string>>> GetDistinctRoleNames()
    {
        try
        {
            var roles = await _mediator.Send(new GetDistinctRoleNamesQuery());
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting distinct role names");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new role template mapping
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<RoleTemplateMappingDto>> CreateRoleTemplateMapping([FromBody] CreateRoleTemplateMappingRequest request)
    {
        try
        {
            var command = new CreateRoleTemplateMappingCommand
            {
                RoleName = request.RoleName,
                MilestoneTemplateId = request.MilestoneTemplateId,
                IsDefault = request.IsDefault,
                Priority = request.Priority,
                Conditions = request.Conditions,
                CreatedBy = GetCurrentUserId(),
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetRoleTemplateMapping), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role template mapping");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update role template mapping
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<RoleTemplateMappingDto>> UpdateRoleTemplateMapping(Guid id, [FromBody] UpdateRoleTemplateMappingRequest request)
    {
        try
        {
            var command = new UpdateRoleTemplateMappingCommand
            {
                Id = id,
                IsDefault = request.IsDefault,
                Priority = request.Priority,
                Conditions = request.Conditions,
                UpdatedBy = GetCurrentUserId(),
                Configuration = request.Configuration,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role template mapping: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete role template mapping
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteRoleTemplateMapping(Guid id)
    {
        try
        {
            var command = new DeleteRoleTemplateMappingCommand { Id = id };
            var result = await _mediator.Send(command);

            if (result)
            {
                return NoContent();
            }

            return BadRequest("Failed to delete role template mapping");
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role template mapping: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Set role template mapping as default
    /// </summary>
    [HttpPost("{id}/set-default")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<RoleTemplateMappingDto>> SetRoleTemplateMappingAsDefault(Guid id)
    {
        try
        {
            var command = new SetRoleTemplateMappingAsDefaultCommand
            {
                Id = id,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting role template mapping as default: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    private string GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("userId")?.Value;
        return userIdClaim ?? "system";
    }
}
