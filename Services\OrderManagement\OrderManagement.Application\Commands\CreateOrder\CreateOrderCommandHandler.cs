using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Domain.Exceptions;
using Shared.Domain.Common;

namespace OrderManagement.Application.Commands.CreateOrder;

public class CreateOrderCommandHandler : IRequestHandler<CreateOrderCommand, Guid>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqBidRepository _bidRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateOrderCommandHandler> _logger;

    public CreateOrderCommandHandler(
        IOrderRepository orderRepository,
        IRfqRepository rfqRepository,
        IRfqBidRepository bidRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<CreateOrderCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _rfqRepository = rfqRepository;
        _bidRepository = bidRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating order for transport company {TransportCompanyId} and broker {BrokerId}",
            request.TransportCompanyId, request.BrokerId);

        // Validate RFQ and Bid if provided
        RFQ? rfq = null;
        RfqBid? acceptedBid = null;

        if (request.RfqId.HasValue)
        {
            rfq = await _rfqRepository.GetByIdAsync(request.RfqId.Value, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new OrderManagementException("Associated RFQ not found");
            }

            if (rfq.TransportCompanyId != request.TransportCompanyId)
            {
                _logger.LogWarning("RFQ {RfqId} does not belong to transport company {TransportCompanyId}",
                    request.RfqId, request.TransportCompanyId);
                throw new OrderManagementException("RFQ does not belong to the specified transport company");
            }
        }

        if (request.AcceptedBidId.HasValue)
        {
            acceptedBid = await _bidRepository.GetByIdAsync(request.AcceptedBidId.Value, cancellationToken);
            if (acceptedBid == null)
            {
                _logger.LogWarning("Bid {BidId} not found", request.AcceptedBidId);
                throw new OrderManagementException("Associated bid not found");
            }

            if (acceptedBid.BrokerId != request.BrokerId)
            {
                _logger.LogWarning("Bid {BidId} does not belong to broker {BrokerId}",
                    request.AcceptedBidId, request.BrokerId);
                throw new OrderManagementException("Bid does not belong to the specified broker");
            }

            if (request.RfqId.HasValue && acceptedBid.RfqId != request.RfqId.Value)
            {
                _logger.LogWarning("Bid {BidId} is not for RFQ {RfqId}",
                    request.AcceptedBidId, request.RfqId);
                throw new OrderManagementException("Bid is not associated with the specified RFQ");
            }
        }

        // Create value objects
        var loadDetails = CreateLoadDetails(request.LoadDetails);
        var routeDetails = CreateRouteDetails(request.RouteDetails);
        var agreedPrice = new Money(request.AgreedPrice.Amount, request.AgreedPrice.Currency);
        var customerDetails = CreateCustomerDetails(request.CustomerDetails);
        var billingDetails = CreateBillingDetails(request.BillingDetails);

        // Create Order entity
        // Note: CustomerDetails and BillingDetails are not part of Order constructor
        var order = new Order(
            request.TransportCompanyId,
            request.BrokerId,
            request.Title,
            request.Description,
            loadDetails,
            routeDetails,
            agreedPrice,
            request.RfqId,
            request.AcceptedBidId,
            request.SpecialInstructions,
            request.IsUrgent);

        // Save to repository
        await _orderRepository.AddAsync(order, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("order.created", new
        {
            OrderId = order.Id,
            OrderNumber = order.OrderNumber,
            TransportCompanyId = order.TransportCompanyId,
            BrokerId = order.BrokerId,
            RfqId = order.RfqId,
            AcceptedBidId = order.AcceptedBidId,
            Title = order.Title,
            LoadType = order.LoadDetails.LoadType.ToString(),
            PickupCity = order.RouteDetails.PickupAddress.City,
            DeliveryCity = order.RouteDetails.DeliveryAddress.City,
            PreferredPickupDate = order.RouteDetails.PreferredPickupDate,
            PreferredDeliveryDate = order.RouteDetails.PreferredDeliveryDate,
            AgreedPrice = new { Amount = order.AgreedPrice.Amount, Currency = order.AgreedPrice.Currency },
            IsUrgent = order.IsUrgent,
            CreatedAt = order.CreatedAt
        }, cancellationToken);

        _logger.LogInformation("Successfully created order {OrderId} with number {OrderNumber}",
            order.Id, order.OrderNumber);

        return order.Id;
    }

    private LoadDetails CreateLoadDetails(CreateOrderLoadDetailsDto dto)
    {
        var weight = new Weight(dto.Weight.Value, dto.Weight.Unit);
        var volume = dto.Volume != null ? new Volume(dto.Volume.Value, dto.Volume.Unit) : null;
        var dimensions = dto.Dimensions != null
            ? new Dimensions(dto.Dimensions.Length, dto.Dimensions.Width, dto.Dimensions.Height, dto.Dimensions.Unit)
            : null;
        var temperatureRange = dto.TemperatureRange != null
            ? new TemperatureRange(dto.TemperatureRange.MinTemperature, dto.TemperatureRange.MaxTemperature, dto.TemperatureRange.Unit)
            : null;

        return new LoadDetails(
            dto.Description,
            dto.LoadType,
            weight,
            dto.Quantity,
            dto.RequiredVehicleType,
            volume,
            dimensions,
            dto.PackagingType,
            dto.RequiresSpecialHandling,
            dto.SpecialHandlingInstructions,
            dto.IsHazardous,
            dto.HazardousClassification,
            dto.RequiresTemperatureControl,
            temperatureRange,
            dto.Photos);
    }

    private RouteDetails CreateRouteDetails(CreateOrderRouteDetailsDto dto)
    {
        var pickupAddress = CreateAddress(dto.PickupAddress);
        var deliveryAddress = CreateAddress(dto.DeliveryAddress);
        var pickupTimeWindow = dto.PickupTimeWindow != null
            ? new TimeWindow(dto.PickupTimeWindow.StartTime, dto.PickupTimeWindow.EndTime)
            : null;
        var deliveryTimeWindow = dto.DeliveryTimeWindow != null
            ? new TimeWindow(dto.DeliveryTimeWindow.StartTime, dto.DeliveryTimeWindow.EndTime)
            : null;
        var estimatedDistance = dto.EstimatedDistance != null
            ? new Distance(dto.EstimatedDistance.Value, dto.EstimatedDistance.Unit)
            : null;
        var intermediateStops = dto.IntermediateStops.Select(CreateAddress).ToList();

        return new RouteDetails(
            pickupAddress,
            deliveryAddress,
            dto.PreferredPickupDate,
            dto.PreferredDeliveryDate,
            pickupTimeWindow,
            deliveryTimeWindow,
            estimatedDistance,
            dto.EstimatedDuration,
            intermediateStops,
            dto.RouteNotes,
            dto.IsFlexiblePickup,
            dto.IsFlexibleDelivery);
    }

    private Address CreateAddress(CreateOrderAddressDto dto)
    {
        return new Address(
            dto.Street,
            dto.City,
            dto.State,
            dto.PostalCode,
            dto.Country,
            dto.Latitude,
            dto.Longitude,
            dto.ContactPerson,
            dto.ContactPhone,
            dto.SpecialInstructions);
    }

    private CustomerDetails CreateCustomerDetails(CreateOrderCustomerDetailsDto dto)
    {
        var billingAddress = CreateAddress(dto.BillingAddress);
        return new CustomerDetails(
            dto.CompanyName,
            dto.ContactPerson,
            dto.ContactEmail,
            dto.ContactPhone,
            billingAddress,
            dto.TaxId,
            dto.CustomerReference,
            dto.PurchaseOrderNumber);
    }

    private BillingDetails CreateBillingDetails(CreateOrderBillingDetailsDto dto)
    {
        return new BillingDetails(
            dto.BillingMethod,
            dto.PaymentTermsDays,
            dto.PaymentInstructions,
            dto.RequiresPurchaseOrder,
            dto.PreferredInvoiceFormat,
            dto.BillingContact,
            dto.BillingEmail);
    }
}

