using MediatR;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.Application.Queries;

public class GetDocumentByIdQuery : IRequest<DocumentDto?>
{
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; } // For access control
    public bool IncludeVersions { get; set; } = false;
}

public class GetDocumentsByOwnerQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid OwnerId { get; set; }
    public DocumentType? DocumentType { get; set; }
    public DocumentStatus? Status { get; set; }
    public Guid RequestingUserId { get; set; } // For access control
}

public class SearchDocumentsQuery : IRequest<DocumentSearchResultDto>
{
    public string? SearchTerm { get; set; }
    public DocumentType? DocumentType { get; set; }
    public DocumentCategory? Category { get; set; }
    public DocumentStatus? Status { get; set; }
    public Guid? OwnerId { get; set; }
    public string? OwnerType { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public Guid RequestingUserId { get; set; } // For access control
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class GetAccessibleDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid UserId { get; set; }
    public AccessLevel MinimumAccessLevel { get; set; } = AccessLevel.Private;
    public DocumentType? DocumentType { get; set; }
    public DocumentStatus? Status { get; set; }
}

public class GetDocumentVersionsQuery : IRequest<IEnumerable<DocumentVersionDto>>
{
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; } // For access control
}

public class GetDocumentVersionQuery : IRequest<DocumentVersionDto?>
{
    public Guid DocumentId { get; set; }
    public int VersionNumber { get; set; }
    public Guid UserId { get; set; } // For access control
}

public class GetDocumentAccessListQuery : IRequest<IEnumerable<DocumentAccessDto>>
{
    public Guid DocumentId { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization
}

public class GetDocumentAnalyticsQuery : IRequest<DocumentAnalyticsDto>
{
    public Guid? OwnerId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization
}

public class GetExpiredDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public DateTime? BeforeDate { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization (admin only)
}

public class GetDocumentsForArchivalQuery : IRequest<IEnumerable<DocumentDto>>
{
    public DateTime BeforeDate { get; set; }
    public DocumentStatus? Status { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization (admin only)
}

public class GetDocumentDownloadUrlQuery : IRequest<string>
{
    public Guid DocumentId { get; set; }
    public int? VersionNumber { get; set; }
    public TimeSpan? ExpiryDuration { get; set; } = TimeSpan.FromHours(1);
    public Guid UserId { get; set; } // For access control
}

public class GetDocumentThumbnailQuery : IRequest<byte[]?>
{
    public Guid DocumentId { get; set; }
    public int Width { get; set; } = 200;
    public int Height { get; set; } = 200;
    public Guid UserId { get; set; } // For access control
}

// Shipper-specific queries
public class GetShipperDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid ShipperId { get; set; }
    public DocumentType? DocumentType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}

// Carrier-specific queries
public class GetCarrierDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public Guid CarrierId { get; set; }
    public DocumentType? DocumentType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid RequestingUserId { get; set; }
}

// Admin-specific queries
public class GetSystemDocumentsQuery : IRequest<IEnumerable<DocumentDto>>
{
    public DocumentCategory? Category { get; set; }
    public string? Language { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization (admin only)
}
