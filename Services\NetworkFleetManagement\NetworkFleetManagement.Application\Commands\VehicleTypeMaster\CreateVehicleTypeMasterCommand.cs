using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Commands.VehicleTypeMaster;

/// <summary>
/// Command to create a new vehicle type master
/// </summary>
public class CreateVehicleTypeMasterCommand : IRequest<VehicleTypeMasterDto>
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal? MinLoadCapacityKg { get; set; }
    public decimal? MaxLoadCapacityKg { get; set; }
    public decimal? MinVolumeCapacityM3 { get; set; }
    public decimal? MaxVolumeCapacityM3 { get; set; }
    public string? SpecialRequirements { get; set; }
    public int SortOrder { get; set; }
    public string? IconUrl { get; set; }
    public Dictionary<string, object>? AdditionalProperties { get; set; }
}
