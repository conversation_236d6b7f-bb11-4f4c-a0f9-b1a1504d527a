using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

public class TripLocationUpdate : BaseEntity
{
    public Guid TripId { get; private set; }
    public Location Location { get; private set; } = null!;
    public DateTime Timestamp { get; private set; }
    public LocationUpdateSource Source { get; private set; }
    public double? Speed { get; private set; }
    public double? Heading { get; private set; }
    public double? Accuracy { get; private set; } // GPS accuracy in meters
    public double? Altitude { get; private set; } // Altitude in meters
    public bool IsSignificantUpdate { get; private set; } // For filtering timeline events
    public double? DistanceFromPreviousKm { get; private set; } // Distance traveled since last update
    public GeofenceStatus GeofenceStatus { get; private set; } // Current geofence status
    public string? GeofenceZoneId { get; private set; } // ID of the geofence zone if applicable
    public Dictionary<string, object>? AdditionalData { get; private set; } // For extensibility

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private TripLocationUpdate() { }

    public TripLocationUpdate(
        Guid tripId,
        Location location,
        LocationUpdateSource source = LocationUpdateSource.GPS,
        double? speed = null,
        double? heading = null,
        double? accuracy = null,
        double? altitude = null,
        bool isSignificantUpdate = false,
        double? distanceFromPreviousKm = null,
        GeofenceStatus geofenceStatus = GeofenceStatus.Unknown,
        string? geofenceZoneId = null,
        Dictionary<string, object>? additionalData = null)
    {
        TripId = tripId;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        Timestamp = DateTime.UtcNow;
        Source = source;
        Speed = speed;
        Heading = heading;
        Accuracy = accuracy;
        Altitude = altitude;
        IsSignificantUpdate = isSignificantUpdate;
        DistanceFromPreviousKm = distanceFromPreviousKm;
        GeofenceStatus = geofenceStatus;
        GeofenceZoneId = geofenceZoneId;
        AdditionalData = additionalData;
    }

    public void MarkAsSignificant()
    {
        IsSignificantUpdate = true;
    }

    public void UpdateGeofenceStatus(GeofenceStatus status, string? zoneId = null)
    {
        GeofenceStatus = status;
        GeofenceZoneId = zoneId;
    }

    public void SetDistanceFromPrevious(double distanceKm)
    {
        DistanceFromPreviousKm = distanceKm;
    }

    public bool IsWithinAccuracyThreshold(double thresholdMeters = 10.0)
    {
        return Accuracy.HasValue && Accuracy.Value <= thresholdMeters;
    }

    public bool IsMoving(double speedThresholdKmh = 5.0)
    {
        return Speed.HasValue && Speed.Value >= speedThresholdKmh;
    }

    public double CalculateDistanceFromLocation(Location targetLocation)
    {
        return Location.CalculateDistanceKm(targetLocation);
    }
}

public class TripException : BaseEntity
{
    public Guid TripId { get; private set; }
    public ExceptionType ExceptionType { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public Location? Location { get; private set; }
    public DateTime ReportedAt { get; private set; }
    public bool IsResolved { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public string? Resolution { get; private set; }
    public string? ResolvedBy { get; private set; }

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private TripException() { }

    public TripException(
        Guid tripId,
        ExceptionType exceptionType,
        string description,
        Location? location = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        TripId = tripId;
        ExceptionType = exceptionType;
        Description = description;
        Location = location;
        ReportedAt = DateTime.UtcNow;
        IsResolved = false;
    }

    public void Resolve(string resolution, string? resolvedBy = null)
    {
        if (string.IsNullOrWhiteSpace(resolution))
            throw new ArgumentException("Resolution cannot be empty", nameof(resolution));

        IsResolved = true;
        ResolvedAt = DateTime.UtcNow;
        Resolution = resolution;
        ResolvedBy = resolvedBy;
    }
}

public class TripDocument : BaseEntity
{
    public Guid TripId { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string FileUrl { get; private set; } = string.Empty;
    public string? Description { get; private set; }
    public DateTime UploadedAt { get; private set; }
    public string? UploadedBy { get; private set; }

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private TripDocument() { }

    public TripDocument(
        Guid tripId,
        DocumentType documentType,
        string fileName,
        string fileUrl,
        string? description = null,
        string? uploadedBy = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("File URL cannot be empty", nameof(fileUrl));

        TripId = tripId;
        DocumentType = documentType;
        FileName = fileName;
        FileUrl = fileUrl;
        Description = description;
        UploadedAt = DateTime.UtcNow;
        UploadedBy = uploadedBy;
    }
}

public class ProofOfDelivery : BaseEntity
{
    public Guid TripStopId { get; private set; }
    public string RecipientName { get; private set; } = string.Empty;
    public string? RecipientSignature { get; private set; }
    public string? PhotoUrl { get; private set; }
    public DateTime DeliveredAt { get; private set; }
    public string? Notes { get; private set; }
    public bool IsDigitalSignature { get; private set; }
    public string? DeliveredBy { get; private set; }

    // Navigation properties
    public TripStop TripStop { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private ProofOfDelivery() { }

    public ProofOfDelivery(
        Guid tripStopId,
        string recipientName,
        string? recipientSignature = null,
        string? photoUrl = null,
        string? notes = null,
        bool isDigitalSignature = false,
        string? deliveredBy = null)
    {
        if (string.IsNullOrWhiteSpace(recipientName))
            throw new ArgumentException("Recipient name cannot be empty", nameof(recipientName));

        TripStopId = tripStopId;
        RecipientName = recipientName;
        RecipientSignature = recipientSignature;
        PhotoUrl = photoUrl;
        DeliveredAt = DateTime.UtcNow;
        Notes = notes;
        IsDigitalSignature = isDigitalSignature;
        DeliveredBy = deliveredBy;
    }

    public void UpdateSignature(string signature, bool isDigital = false)
    {
        if (string.IsNullOrWhiteSpace(signature))
            throw new ArgumentException("Signature cannot be empty", nameof(signature));

        RecipientSignature = signature;
        IsDigitalSignature = isDigital;
    }

    public void AddPhoto(string photoUrl)
    {
        if (string.IsNullOrWhiteSpace(photoUrl))
            throw new ArgumentException("Photo URL cannot be empty", nameof(photoUrl));

        PhotoUrl = photoUrl;
    }

    public void UpdatePhoto(string photoUrl)
    {
        if (string.IsNullOrWhiteSpace(photoUrl))
            throw new ArgumentException("Photo URL cannot be empty", nameof(photoUrl));

        PhotoUrl = photoUrl;
    }
}
