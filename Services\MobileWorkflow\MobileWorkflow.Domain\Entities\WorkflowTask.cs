
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class WorkflowTask : AggregateRoot
{
    public Guid? WorkflowId { get; private set; }
    public Guid? WorkflowExecutionId { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Type { get; private set; } // Manual, Automated, Approval, Notification, etc.
    public string Status { get; private set; } // Pending, InProgress, Completed, Failed, Cancelled
    public int Priority { get; private set; } // 1 = High, 2 = Medium, 3 = Low
    public Guid? AssignedTo { get; private set; }
    public string? AssignedToRole { get; private set; }
    public DateTime? DueDate { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public Dictionary<string, object> Result { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    public TimeSpan? SLA { get; private set; }
    public bool IsOverdue { get; private set; }

    // Navigation properties
    public virtual Workflow? Workflow { get; private set; }
    public virtual WorkflowExecution? WorkflowExecution { get; private set; }

    private WorkflowTask() { } // EF Core

    public WorkflowTask(
        Guid? workflowExecutionId,
        string name,
        string type,
        Dictionary<string, object> parameters,
        Guid? assignedTo = null,
        string? description = null,
        int priority = 2,
        TimeSpan? sla = null)
    {
        WorkflowExecutionId = workflowExecutionId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? string.Empty;
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Status = "Pending";
        Priority = priority;
        AssignedTo = assignedTo;
        Parameters = parameters ?? new Dictionary<string, object>();
        Result = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
        SLA = sla;
        IsOverdue = false;

        if (sla.HasValue)
        {
            DueDate = DateTime.UtcNow.Add(sla.Value);
        }

        AddDomainEvent(new WorkflowTaskCreatedEvent(Id, Name, Type, AssignedTo, Priority));
    }

    // Standalone task constructor (not part of workflow execution)
    public WorkflowTask(
        string name,
        string type,
        Dictionary<string, object> parameters,
        Guid? assignedTo = null,
        string? assignedToRole = null,
        string? description = null,
        int priority = 2,
        TimeSpan? sla = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? string.Empty;
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Status = "Pending";
        Priority = priority;
        AssignedTo = assignedTo;
        AssignedToRole = assignedToRole;
        Parameters = parameters ?? new Dictionary<string, object>();
        Result = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
        SLA = sla;
        IsOverdue = false;

        if (sla.HasValue)
        {
            DueDate = DateTime.UtcNow.Add(sla.Value);
        }

        AddDomainEvent(new WorkflowTaskCreatedWithRoleEvent(Id, Name, Type, AssignedTo, AssignedToRole, Priority));
    }

    public void Start()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start task with status: {Status}");

        Status = "InProgress";
        StartedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTaskStartedEvent(Id, Name, AssignedTo));
    }

    public void Complete(Dictionary<string, object>? result = null)
    {
        if (Status != "InProgress" && Status != "Pending")
            throw new InvalidOperationException($"Cannot complete task with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;

        if (result != null)
        {
            Result = result;
        }

        var duration = GetExecutionDuration();
        AddDomainEvent(new WorkflowTaskCompletedEvent(Id, Name, AssignedTo, duration?.TotalMinutes));
    }

    public void Fail(string errorMessage)
    {
        if (Status != "InProgress" && Status != "Pending")
            throw new InvalidOperationException($"Cannot fail task with status: {Status}");

        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));

        AddDomainEvent(new WorkflowTaskFailedEvent(Id, Name, AssignedTo, errorMessage));
    }

    public void Cancel()
    {
        if (Status == "Completed" || Status == "Failed")
            throw new InvalidOperationException($"Cannot cancel task with status: {Status}");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;

        AddDomainEvent(new WorkflowTaskCancelledEvent(Id, Name, AssignedTo));
    }

    public void Reassign(Guid newAssignedTo, string? newAssignedToRole = null)
    {
        if (Status == "Completed" || Status == "Failed" || Status == "Cancelled")
            throw new InvalidOperationException($"Cannot reassign task with status: {Status}");

        var oldAssignedTo = AssignedTo;
        var oldAssignedToRole = AssignedToRole;

        AssignedTo = newAssignedTo;
        AssignedToRole = newAssignedToRole;

        AddDomainEvent(new WorkflowTaskReassignedEvent(Id, Name, oldAssignedTo, newAssignedTo));
    }

    public void UpdateDueDate(DateTime newDueDate)
    {
        DueDate = newDueDate;
        CheckOverdue();
        AddDomainEvent(new WorkflowTaskDueDateUpdatedEvent(Id, Name, newDueDate));
    }

    public void CheckOverdue()
    {
        if (DueDate.HasValue && DateTime.UtcNow > DueDate.Value && Status != "Completed" && Status != "Failed" && Status != "Cancelled")
        {
            if (!IsOverdue)
            {
                IsOverdue = true;
                AddDomainEvent(new WorkflowTaskOverdueEvent(Id, Name, AssignedTo, DueDate));
            }
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public TimeSpan? GetExecutionDuration()
    {
        if (StartedAt == null) return null;
        var endTime = CompletedAt ?? DateTime.UtcNow;
        return endTime - StartedAt.Value;
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Cancelled";
    }
}



