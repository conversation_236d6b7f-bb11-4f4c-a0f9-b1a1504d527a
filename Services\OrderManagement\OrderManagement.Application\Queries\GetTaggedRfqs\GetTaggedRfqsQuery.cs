using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetTaggedRfqs;

public class GetTaggedRfqsQuery : IRequest<PagedResult<RfqSummaryDto>>
{
    public List<string> TagTypes { get; set; } = new();
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public Guid RequestingUserId { get; set; }
    public bool ActiveTagsOnly { get; set; } = true;
}
