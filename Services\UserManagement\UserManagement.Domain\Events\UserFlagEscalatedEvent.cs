using Shared.Domain.Common;

namespace UserManagement.Domain.Events;

/// <summary>
/// Domain event raised when a user flag is escalated
/// </summary>
public class UserFlagEscalatedEvent : DomainEvent
{
    public Guid UserId { get; }
    public Guid FlagId { get; }
    public string EscalationReason { get; }
    public Guid EscalatedBy { get; }
    public Guid EscalatedTo { get; }
    public DateTime EscalatedAt { get; }

    public UserFlagEscalatedEvent(
        Guid userId,
        Guid flagId,
        string escalationReason,
        Guid escalatedBy,
        Guid escalatedTo,
        DateTime escalatedAt)
    {
        UserId = userId;
        FlagId = flagId;
        EscalationReason = escalationReason;
        EscalatedBy = escalatedBy;
        EscalatedTo = escalatedTo;
        EscalatedAt = escalatedAt;
    }
}
