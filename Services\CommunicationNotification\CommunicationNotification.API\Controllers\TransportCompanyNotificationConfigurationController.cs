using CommunicationNotification.Application.Commands.TransportCompanyNotification;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CommunicationNotification.API.Controllers;

/// <summary>
/// Controller for Transport Company notification configuration
/// </summary>
[ApiController]
[Route("api/transport-company/notification-configuration")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyNotificationConfigurationController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyNotificationConfigurationController> _logger;

    public TransportCompanyNotificationConfigurationController(
        IMediator mediator,
        ILogger<TransportCompanyNotificationConfigurationController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Configure notifications for a transport company
    /// </summary>
    [HttpPost("{transportCompanyId}/configure")]
    [ProducesResponseType(typeof(ConfigureTransportCompanyNotificationsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ConfigureTransportCompanyNotificationsResult>> ConfigureNotifications(
        Guid transportCompanyId,
        [FromBody] ConfigureTransportCompanyNotificationsCommand command)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to configure notifications for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.ConfiguredByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Notification configuration updated successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to configure notifications for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring notifications for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update notification channel preferences
    /// </summary>
    [HttpPut("{transportCompanyId}/channel-preferences")]
    [ProducesResponseType(typeof(UpdateNotificationChannelPreferencesResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateNotificationChannelPreferencesResult>> UpdateChannelPreferences(
        Guid transportCompanyId,
        [FromBody] UpdateNotificationChannelPreferencesCommand command)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to update channel preferences for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.UpdatedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Channel preferences updated successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update channel preferences for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating channel preferences for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update quiet hours configuration
    /// </summary>
    [HttpPut("{transportCompanyId}/quiet-hours")]
    [ProducesResponseType(typeof(UpdateQuietHoursConfigurationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateQuietHoursConfigurationResult>> UpdateQuietHours(
        Guid transportCompanyId,
        [FromBody] UpdateQuietHoursConfigurationCommand command)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to update quiet hours for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.UpdatedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Quiet hours configuration updated successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update quiet hours for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quiet hours for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update notification frequency settings
    /// </summary>
    [HttpPut("{transportCompanyId}/frequency-settings")]
    [ProducesResponseType(typeof(UpdateNotificationFrequencySettingsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateNotificationFrequencySettingsResult>> UpdateFrequencySettings(
        Guid transportCompanyId,
        [FromBody] UpdateNotificationFrequencySettingsCommand command)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to update frequency settings for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.UpdatedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Frequency settings updated successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update frequency settings for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating frequency settings for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add notification recipient
    /// </summary>
    [HttpPost("{transportCompanyId}/recipients")]
    [ProducesResponseType(typeof(AddNotificationRecipientResult), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<AddNotificationRecipientResult>> AddNotificationRecipient(
        Guid transportCompanyId,
        [FromBody] AddNotificationRecipientCommand command)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to add notification recipients for this transport company");
            }

            command.TransportCompanyId = transportCompanyId;
            command.AddedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Notification recipient added successfully for Transport Company {TransportCompanyId}",
                    transportCompanyId);
                return CreatedAtAction(nameof(GetNotificationConfiguration), new { transportCompanyId }, result);
            }

            _logger.LogWarning("Failed to add notification recipient for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding notification recipient for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get notification configuration for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}")]
    [ProducesResponseType(typeof(TransportCompanyNotificationConfigurationDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<TransportCompanyNotificationConfigurationDto>> GetNotificationConfiguration(
        Guid transportCompanyId)
    {
        try
        {
            if (!await CanViewNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to view notification configuration for this transport company");
            }

            // This would be implemented as a query handler
            var configuration = new TransportCompanyNotificationConfigurationDto
            {
                TransportCompanyId = transportCompanyId,
                IsEnabled = true,
                LastUpdatedAt = DateTime.UtcNow,
                ChannelPreferences = new NotificationChannelPreferencesDto
                {
                    EmailEnabled = true,
                    SmsEnabled = true,
                    PushEnabled = true,
                    InAppEnabled = true,
                    WhatsAppEnabled = false
                },
                QuietHours = new QuietHoursConfigurationDto
                {
                    IsEnabled = false,
                    StartTime = new TimeSpan(22, 0, 0),
                    EndTime = new TimeSpan(6, 0, 0),
                    TimeZone = "Asia/Kolkata",
                    EmergencyOverride = true
                },
                FrequencySettings = new NotificationFrequencySettingsDto
                {
                    MaxNotificationsPerHour = 50,
                    MaxNotificationsPerDay = 200,
                    CooldownPeriod = TimeSpan.FromMinutes(5),
                    BurstModeEnabled = true,
                    BurstModeThreshold = 10
                },
                GroupingSettings = new NotificationGroupingSettingsDto
                {
                    IsEnabled = true,
                    GroupingWindow = TimeSpan.FromMinutes(15),
                    MaxGroupSize = 10,
                    GroupBySender = true,
                    GroupByPriority = true,
                    GroupByRelatedEntity = true
                }
            };

            return Ok(configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification configuration for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Test notification configuration
    /// </summary>
    [HttpPost("{transportCompanyId}/test")]
    [ProducesResponseType(typeof(TestNotificationConfigurationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<TestNotificationConfigurationResult>> TestNotificationConfiguration(
        Guid transportCompanyId,
        [FromBody] TestNotificationConfigurationRequest request)
    {
        try
        {
            if (!await CanManageNotificationConfiguration(transportCompanyId))
            {
                return Forbid("You don't have permission to test notification configuration for this transport company");
            }

            // This would send test notifications to verify configuration
            var result = new TestNotificationConfigurationResult
            {
                IsSuccess = true,
                TestedAt = DateTime.UtcNow,
                TestResults = new List<NotificationTestResult>
                {
                    new NotificationTestResult
                    {
                        Channel = "Email",
                        IsSuccess = true,
                        Message = "Test email sent successfully"
                    },
                    new NotificationTestResult
                    {
                        Channel = "SMS",
                        IsSuccess = true,
                        Message = "Test SMS sent successfully"
                    }
                }
            };

            _logger.LogInformation("Notification configuration tested successfully for Transport Company {TransportCompanyId}",
                transportCompanyId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing notification configuration for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageNotificationConfiguration(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanViewNotificationConfiguration(Guid transportCompanyId)
    {
        return await CanManageNotificationConfiguration(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}
