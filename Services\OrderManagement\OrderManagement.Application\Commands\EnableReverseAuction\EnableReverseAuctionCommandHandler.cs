using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.EnableReverseAuction;

public class EnableReverseAuctionCommandHandler : IRequestHandler<EnableReverseAuctionCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<EnableReverseAuctionCommandHandler> _logger;

    public EnableReverseAuctionCommandHandler(
        IRfqRepository rfqRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<EnableReverseAuctionCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(EnableReverseAuctionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Enabling reverse auction for RFQ {RfqId} by user {EnabledBy}",
            request.RfqId, request.EnabledBy);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                return false;
            }

            // Validate RFQ can have reverse auction enabled
            if (rfq.Status != RfqStatus.Published)
            {
                _logger.LogWarning("Cannot enable reverse auction for RFQ {RfqId} in status {Status}", 
                    request.RfqId, rfq.Status);
                return false;
            }

            // Validate auction times
            if (request.StartTime >= request.EndTime)
            {
                _logger.LogWarning("Invalid auction times for RFQ {RfqId}: start {StartTime} >= end {EndTime}", 
                    request.RfqId, request.StartTime, request.EndTime);
                return false;
            }

            if (request.StartTime <= DateTime.UtcNow)
            {
                _logger.LogWarning("Auction start time {StartTime} must be in the future for RFQ {RfqId}", 
                    request.StartTime, request.RfqId);
                return false;
            }

            // Create reverse auction settings
            var reverseAuctionSettings = new ReverseAuctionSettings(
                true,
                request.StartTime,
                request.EndTime,
                request.StartingPrice,
                request.ReservePrice,
                request.MinimumBidDecrement,
                request.MaxBidders,
                request.AllowBidExtensions,
                request.ExtensionMinutes,
                request.IsPublicAuction,
                request.AuctionRules);

            // Enable reverse auction on RFQ
            rfq.EnableReverseAuction(reverseAuctionSettings);

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var auctionDetails = new List<string>
                {
                    $"Start: {request.StartTime:yyyy-MM-dd HH:mm} UTC",
                    $"End: {request.EndTime:yyyy-MM-dd HH:mm} UTC"
                };

                if (request.StartingPrice != null)
                    auctionDetails.Add($"Starting Price: {request.StartingPrice.Amount:C} {request.StartingPrice.Currency}");

                if (request.ReservePrice != null)
                    auctionDetails.Add($"Reserve Price: {request.ReservePrice.Amount:C} {request.ReservePrice.Currency}");

                var description = $"Reverse auction enabled. {string.Join(", ", auctionDetails)}";

                var metadata = new Dictionary<string, object>
                {
                    ["startTime"] = request.StartTime,
                    ["endTime"] = request.EndTime,
                    ["isPublicAuction"] = request.IsPublicAuction,
                    ["allowBidExtensions"] = request.AllowBidExtensions
                };

                if (request.MaxBidders.HasValue)
                    metadata["maxBidders"] = request.MaxBidders.Value;

                if (request.ExtensionMinutes.HasValue)
                    metadata["extensionMinutes"] = request.ExtensionMinutes.Value;

                var timelineEvent = RfqTimeline.CreateEvent(
                    request.RfqId,
                    RfqTimelineEventType.ReverseAuctionEnabled,
                    description,
                    request.EnabledBy,
                    request.EnabledByName,
                    "Transport Company",
                    System.Text.Json.JsonSerializer.Serialize(metadata));

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully enabled reverse auction for RFQ {RfqId}", request.RfqId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling reverse auction for RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
