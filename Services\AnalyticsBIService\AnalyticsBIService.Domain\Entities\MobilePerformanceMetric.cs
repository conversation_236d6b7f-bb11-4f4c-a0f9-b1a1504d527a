using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Mobile performance metric entity for tracking mobile app performance
/// </summary>
public class MobilePerformanceMetric : BaseEntity
{
    public Guid UserId { get; private set; }
    public string AppId { get; private set; }
    public string SessionId { get; private set; }
    public string DeviceId { get; private set; }
    public string MetricType { get; private set; }
    public decimal MetricValue { get; private set; }
    public string Unit { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string DeviceInfo { get; private set; } // JSON string
    public string AppVersion { get; private set; }
    public string MetricContext { get; private set; } // JSON string

    private MobilePerformanceMetric()
    {
        AppId = string.Empty;
        SessionId = string.Empty;
        DeviceId = string.Empty;
        MetricType = string.Empty;
        Unit = string.Empty;
        DeviceInfo = string.Empty;
        AppVersion = string.Empty;
        MetricContext = string.Empty;
    }

    public MobilePerformanceMetric(
        Guid userId,
        string appId,
        string sessionId,
        string deviceId,
        string metricType,
        decimal metricValue,
        string unit,
        DateTime timestamp,
        string deviceInfo,
        string appVersion,
        string metricContext)
    {
        UserId = userId;
        AppId = appId ?? throw new ArgumentNullException(nameof(appId));
        SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        MetricType = metricType ?? throw new ArgumentNullException(nameof(metricType));
        MetricValue = metricValue;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Timestamp = timestamp;
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        MetricContext = metricContext ?? throw new ArgumentNullException(nameof(metricContext));
    }

    public void UpdateMetricValue(decimal metricValue)
    {
        MetricValue = metricValue;
        SetUpdatedAt();
    }

    public void UpdateMetricContext(string metricContext)
    {
        MetricContext = metricContext ?? throw new ArgumentNullException(nameof(metricContext));
        SetUpdatedAt();
    }
}
