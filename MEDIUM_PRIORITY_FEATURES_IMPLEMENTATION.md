# Medium Priority Features - Implementation Details

## 1. Milestone Template Definition System

### Overview

Enhance existing milestone template functionality in OrderManagement and MobileWorkflow services to provide unified template management for broker RFQ creation and trip execution.

### Current State Analysis

- **OrderManagement Service**: Has MilestoneTemplate entity with RFQ integration
- **MobileWorkflow Service**: Has comprehensive MilestoneTemplate with steps and payout rules
- **Gap**: Need unified template management and broker-specific templates

### Implementation Strategy

#### 1.1 Unified Template Management Service

**Enhanced OrderManagement MilestoneTemplate**:

```csharp
// Services/OrderManagement/OrderManagement.Domain/Entities/MilestoneTemplate.cs
public class MilestoneTemplate : Entity
{
    // Existing properties...
    public BrokerTemplateConfiguration? BrokerConfiguration { get; private set; }
    public List<TemplateUsageMetrics> UsageMetrics { get; private set; } = new();
    public bool IsBrokerSpecific { get; private set; }
    public List<string> ApplicableUserTypes { get; private set; } = new();
    public TemplateComplexity Complexity { get; private set; }

    public void ConfigureForBrokers(BrokerTemplateConfiguration configuration)
    {
        BrokerConfiguration = configuration;
        IsBrokerSpecific = true;
        ApplicableUserTypes.Add("Broker");
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddUsageMetric(Guid userId, string userType, DateTime usedAt)
    {
        UsageMetrics.Add(new TemplateUsageMetrics(userId, userType, usedAt));
        // Update usage count and analytics
    }
}

public class BrokerTemplateConfiguration : ValueObject
{
    public bool RequiresBrokerApproval { get; private set; }
    public decimal MinimumOrderValue { get; private set; }
    public List<string> RequiredDocuments { get; private set; }
    public bool AutoAssignToTrip { get; private set; }
    public Dictionary<string, object> BrokerSpecificSettings { get; private set; }

    public BrokerTemplateConfiguration(
        bool requiresBrokerApproval,
        decimal minimumOrderValue,
        List<string> requiredDocuments,
        bool autoAssignToTrip = true,
        Dictionary<string, object>? brokerSpecificSettings = null)
    {
        RequiresBrokerApproval = requiresBrokerApproval;
        MinimumOrderValue = minimumOrderValue;
        RequiredDocuments = requiredDocuments ?? new List<string>();
        AutoAssignToTrip = autoAssignToTrip;
        BrokerSpecificSettings = brokerSpecificSettings ?? new Dictionary<string, object>();
    }
}

public enum TemplateComplexity
{
    Simple,      // 1-3 steps
    Standard,    // 4-6 steps
    Complex,     // 7-10 steps
    Advanced     // 10+ steps
}
```

#### 1.2 Application Layer Enhancements

**Commands for Broker Template Management**:

```csharp
// Services/OrderManagement/OrderManagement.Application/Commands/CreateBrokerMilestoneTemplate/CreateBrokerMilestoneTemplateCommand.cs
public class CreateBrokerMilestoneTemplateCommand : IRequest<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MilestoneTemplateType TemplateType { get; set; }
    public List<CreateMilestoneStepRequest> Steps { get; set; } = new();
    public BrokerTemplateConfigurationRequest BrokerConfiguration { get; set; } = new();
    public bool SetAsDefault { get; set; } = false;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateMilestoneStepRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int StepOrder { get; set; }
    public decimal PayoutPercentage { get; set; }
    public MilestoneStepType StepType { get; set; }
    public bool IsRequired { get; set; } = true;
    public bool RequiresDocumentation { get; set; } = false;
    public bool RequiresApproval { get; set; } = false;
    public string? DocumentationRequirements { get; set; }
    public string? ApprovalCriteria { get; set; }
    public int? EstimatedDurationHours { get; set; }
}

// Handler implementation
public class CreateBrokerMilestoneTemplateCommandHandler : IRequestHandler<CreateBrokerMilestoneTemplateCommand, Guid>
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<CreateBrokerMilestoneTemplateCommandHandler> _logger;

    public async Task<Guid> Handle(CreateBrokerMilestoneTemplateCommand request, CancellationToken cancellationToken)
    {
        // Create milestone template
        var template = new MilestoneTemplate(
            request.Name,
            request.Description,
            request.TemplateType,
            GetCurrentUserId(),
            request.SetAsDefault,
            string.Join(",", request.Tags),
            request.Metadata);

        // Configure for brokers
        var brokerConfig = new BrokerTemplateConfiguration(
            request.BrokerConfiguration.RequiresBrokerApproval,
            request.BrokerConfiguration.MinimumOrderValue,
            request.BrokerConfiguration.RequiredDocuments,
            request.BrokerConfiguration.AutoAssignToTrip,
            request.BrokerConfiguration.BrokerSpecificSettings);

        template.ConfigureForBrokers(brokerConfig);

        // Add steps
        foreach (var stepRequest in request.Steps.OrderBy(s => s.StepOrder))
        {
            var step = new MilestoneStep(
                template.Id,
                stepRequest.Name,
                stepRequest.Description,
                stepRequest.StepOrder,
                stepRequest.PayoutPercentage,
                stepRequest.StepType,
                stepRequest.IsRequired,
                stepRequest.RequiresDocumentation,
                stepRequest.RequiresApproval,
                stepRequest.DocumentationRequirements,
                stepRequest.ApprovalCriteria,
                stepRequest.EstimatedDurationHours);

            template.AddStep(step);
        }

        // Validate template
        if (!template.ValidatePayoutPercentages())
        {
            throw new InvalidOperationException("Total payout percentages must equal 100%");
        }

        // Save template
        await _templateRepository.AddAsync(template, cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("milestone.template.created", new
        {
            TemplateId = template.Id,
            Name = template.Name,
            TemplateType = template.TemplateType.ToString(),
            IsBrokerSpecific = template.IsBrokerSpecific,
            StepCount = template.Steps.Count,
            CreatedBy = template.CreatedBy,
            CreatedAt = template.CreatedAt
        }, cancellationToken);

        _logger.LogInformation("Created broker milestone template {TemplateId} with {StepCount} steps",
            template.Id, template.Steps.Count);

        return template.Id;
    }
}
```

**Queries for Template Selection**:

```csharp
// Services/OrderManagement/OrderManagement.Application/Queries/GetBrokerMilestoneTemplates/GetBrokerMilestoneTemplatesQuery.cs
public class GetBrokerMilestoneTemplatesQuery : IRequest<List<BrokerMilestoneTemplateDto>>
{
    public MilestoneTemplateType? TemplateType { get; set; }
    public TemplateComplexity? Complexity { get; set; }
    public decimal? MinOrderValue { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public bool OnlyDefaults { get; set; } = false;
    public bool OnlyActive { get; set; } = true;
    public string? SearchTerm { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class BrokerMilestoneTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MilestoneTemplateType TemplateType { get; set; }
    public TemplateComplexity Complexity { get; set; }
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
    public int StepCount { get; set; }
    public decimal TotalPayoutPercentage { get; set; }
    public int UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public BrokerTemplateConfigurationDto BrokerConfiguration { get; set; } = new();
    public List<MilestoneStepSummaryDto> Steps { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}
```

#### 1.3 API Layer Implementation

**Controller for Broker Template Management**:

```csharp
// Services/OrderManagement/OrderManagement.API/Controllers/BrokerMilestoneTemplatesController.cs
[ApiController]
[Route("api/broker/milestone-templates")]
[Authorize(Roles = "Broker,Admin")]
public class BrokerMilestoneTemplatesController : ControllerBase
{
    private readonly IMediator _mediator;

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<BrokerMilestoneTemplateDto>>> GetBrokerTemplates(
        [FromQuery] GetBrokerMilestoneTemplatesQuery query)
    {
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<BrokerMilestoneTemplateDto>> GetTemplate(Guid id)
    {
        var query = new GetBrokerMilestoneTemplateByIdQuery { Id = id };
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound();

        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Guid>> CreateTemplate([FromBody] CreateBrokerMilestoneTemplateCommand command)
    {
        var templateId = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetTemplate), new { id = templateId }, templateId);
    }

    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> UpdateTemplate(Guid id, [FromBody] UpdateBrokerMilestoneTemplateCommand command)
    {
        command.Id = id;
        await _mediator.Send(command);
        return Ok();
    }

    [HttpPost("{id}/clone")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    public async Task<ActionResult<Guid>> CloneTemplate(Guid id, [FromBody] CloneMilestoneTemplateCommand command)
    {
        command.SourceTemplateId = id;
        var newTemplateId = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetTemplate), new { id = newTemplateId }, newTemplateId);
    }

    [HttpPost("{id}/usage")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult> RecordUsage(Guid id, [FromBody] RecordTemplateUsageCommand command)
    {
        command.TemplateId = id;
        command.UserId = GetCurrentUserId();
        await _mediator.Send(command);
        return Ok();
    }

    [HttpGet("recommendations")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<List<TemplateRecommendationDto>>> GetRecommendations(
        [FromQuery] GetTemplateRecommendationsQuery query)
    {
        query.UserId = GetCurrentUserId();
        var recommendations = await _mediator.Send(query);
        return Ok(recommendations);
    }
}
```

#### 1.4 Integration with RFQ Creation

**Enhanced RFQ Creation with Template Selection**:

```csharp
// Services/OrderManagement/OrderManagement.Application/Commands/CreateRfq/CreateRfqCommand.cs
public class CreateRfqCommand : IRequest<Guid>
{
    // Existing properties...
    public Guid? MilestoneTemplateId { get; set; }
    public bool AutoApplyMilestones { get; set; } = true;
    public MilestoneTemplatePreferences? TemplatePreferences { get; set; }
}

public class MilestoneTemplatePreferences
{
    public bool RequiresBrokerApproval { get; set; }
    public bool AutoProgressMilestones { get; set; }
    public List<string> CustomDocumentRequirements { get; set; } = new();
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

// Enhanced CreateRfqCommandHandler
public async Task<Guid> Handle(CreateRfqCommand request, CancellationToken cancellationToken)
{
    // Create RFQ (existing logic)
    var rfq = new RFQ(/* existing parameters */);

    // Apply milestone template if specified
    if (request.MilestoneTemplateId.HasValue && request.AutoApplyMilestones)
    {
        var template = await _milestoneTemplateRepository.GetByIdAsync(
            request.MilestoneTemplateId.Value, cancellationToken);

        if (template != null && template.IsBrokerSpecific)
        {
            var assignment = new RfqMilestoneAssignment(
                rfq.Id,
                template.Id,
                request.TransportCompanyId,
                template.BrokerConfiguration?.RequiresBrokerApproval ?? false);

            rfq.AttachMilestoneTemplate(assignment);

            // Publish milestone template attached event
            await _messageBroker.PublishAsync("rfq.milestone.template.attached", new
            {
                RfqId = rfq.Id,
                TemplateId = template.Id,
                TemplateName = template.Name,
                StepCount = template.Steps.Count,
                RequiresBrokerApproval = template.BrokerConfiguration?.RequiresBrokerApproval ?? false,
                AttachedAt = DateTime.UtcNow
            }, cancellationToken);
        }
    }

    // Continue with existing logic...
}
```

## 2. Different Broker Assignment Per Leg

### Overview

Implement multi-leg trip management with individual broker assignments in TripManagement service, allowing different brokers to handle different segments of a trip.

### Implementation Strategy

#### 2.1 Enhanced Trip Leg Entity

**Updated TripLeg with Broker Assignment**:

```csharp
// Services/TripManagement/TripManagement.Domain/Entities/TripLeg.cs
public class TripLeg : Entity
{
    // Existing properties...
    public Guid? AssignedBrokerId { get; private set; }
    public DateTime? BrokerAssignmentDate { get; private set; }
    public string? BrokerAssignmentNotes { get; private set; }
    public BrokerAssignmentStatus BrokerAssignmentStatus { get; private set; }
    public decimal? BrokerPerformanceRating { get; private set; }
    public BrokerLegAssignmentMetadata? AssignmentMetadata { get; private set; }

    // Navigation properties
    public virtual BrokerLegAssignment? BrokerAssignment { get; private set; }

    public void AssignBroker(
        Guid brokerId,
        Guid assignedBy,
        string? notes = null,
        BrokerLegAssignmentMetadata? metadata = null)
    {
        if (Status != TripLegStatus.Planned && Status != TripLegStatus.Unassigned)
            throw new TripManagementDomainException("Cannot assign broker to leg that is already in progress or completed");

        AssignedBrokerId = brokerId;
        BrokerAssignmentDate = DateTime.UtcNow;
        BrokerAssignmentNotes = notes;
        BrokerAssignmentStatus = BrokerAssignmentStatus.Active;
        AssignmentMetadata = metadata;

        // Create assignment record
        BrokerAssignment = new BrokerLegAssignment(
            TripId, Id, brokerId, assignedBy, notes, metadata);

        Status = TripLegStatus.Assigned;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new BrokerAssignedToLegEvent(TripId, Id, brokerId, assignedBy));
    }

    public void ReassignBroker(
        Guid newBrokerId,
        Guid reassignedBy,
        string reason,
        string? notes = null)
    {
        if (AssignedBrokerId == null)
            throw new TripManagementDomainException("Cannot reassign broker when no broker is currently assigned");

        var previousBrokerId = AssignedBrokerId.Value;

        // Complete current assignment
        BrokerAssignment?.Complete(reason, DateTime.UtcNow);

        // Assign new broker
        AssignBroker(newBrokerId, reassignedBy, notes);

        AddDomainEvent(new BrokerReassignedEvent(TripId, Id, previousBrokerId, newBrokerId, reason));
    }

    public void CompleteBrokerAssignment(decimal performanceRating, string? completionNotes = null)
    {
        if (AssignedBrokerId == null)
            throw new TripManagementDomainException("No broker assigned to complete");

        BrokerPerformanceRating = performanceRating;
        BrokerAssignmentStatus = BrokerAssignmentStatus.Completed;

        BrokerAssignment?.Complete(completionNotes, DateTime.UtcNow);

        AddDomainEvent(new BrokerAssignmentCompletedEvent(TripId, Id, AssignedBrokerId.Value, performanceRating));
    }
}

public enum BrokerAssignmentStatus
{
    Unassigned,
    Active,
    Completed,
    Cancelled,
    Reassigned
}

public class BrokerLegAssignmentMetadata : ValueObject
{
    public decimal? ExpectedCommission { get; private set; }
    public string? SpecialInstructions { get; private set; }
    public List<string> RequiredDocuments { get; private set; }
    public Dictionary<string, object> CustomProperties { get; private set; }

    public BrokerLegAssignmentMetadata(
        decimal? expectedCommission = null,
        string? specialInstructions = null,
        List<string>? requiredDocuments = null,
        Dictionary<string, object>? customProperties = null)
    {
        ExpectedCommission = expectedCommission;
        SpecialInstructions = specialInstructions;
        RequiredDocuments = requiredDocuments ?? new List<string>();
        CustomProperties = customProperties ?? new Dictionary<string, object>();
    }
}
```

#### 2.2 Broker Leg Assignment Entity

**New Entity for Assignment Tracking**:

```csharp
// Services/TripManagement/TripManagement.Domain/Entities/BrokerLegAssignment.cs
public class BrokerLegAssignment : Entity
{
    public Guid TripId { get; private set; }
    public Guid LegId { get; private set; }
    public Guid BrokerId { get; private set; }
    public Guid AssignedBy { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public BrokerAssignmentStatus Status { get; private set; }
    public decimal? PerformanceRating { get; private set; }
    public string? CompletionNotes { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public BrokerLegAssignmentMetadata? Metadata { get; private set; }

    // Navigation properties
    public virtual Trip Trip { get; private set; } = null!;
    public virtual TripLeg TripLeg { get; private set; } = null!;

    private BrokerLegAssignment() { } // EF Constructor

    public BrokerLegAssignment(
        Guid tripId,
        Guid legId,
        Guid brokerId,
        Guid assignedBy,
        string? notes = null,
        BrokerLegAssignmentMetadata? metadata = null)
    {
        TripId = tripId;
        LegId = legId;
        BrokerId = brokerId;
        AssignedBy = assignedBy;
        AssignedAt = DateTime.UtcNow;
        Status = BrokerAssignmentStatus.Active;
        CompletionNotes = notes;
        Metadata = metadata;
        CreatedAt = DateTime.UtcNow;
    }

    public void Complete(string? completionNotes, DateTime completedAt)
    {
        if (Status != BrokerAssignmentStatus.Active)
            throw new TripManagementDomainException("Can only complete active assignments");

        Status = BrokerAssignmentStatus.Completed;
        CompletionNotes = completionNotes;
        CompletedAt = completedAt;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Cancel(string reason)
    {
        if (Status == BrokerAssignmentStatus.Completed)
            throw new TripManagementDomainException("Cannot cancel completed assignment");

        Status = BrokerAssignmentStatus.Cancelled;
        CompletionNotes = reason;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdatePerformanceRating(decimal rating)
    {
        if (rating < 1 || rating > 5)
            throw new ArgumentException("Performance rating must be between 1 and 5");

        PerformanceRating = rating;
        UpdatedAt = DateTime.UtcNow;
    }
}
```

#### 2.3 Application Layer Commands

**Assign Broker to Leg Command**:

```csharp
// Services/TripManagement/TripManagement.Application/Commands/AssignBrokerToLeg/AssignBrokerToLegCommand.cs
public class AssignBrokerToLegCommand : IRequest<AssignBrokerToLegResponse>
{
    public Guid TripId { get; set; }
    public Guid LegId { get; set; }
    public Guid BrokerId { get; set; }
    public string? AssignmentNotes { get; set; }
    public decimal? ExpectedCommission { get; set; }
    public string? SpecialInstructions { get; set; }
    public List<string> RequiredDocuments { get; set; } = new();
    public bool NotifyBroker { get; set; } = true;
}

public class AssignBrokerToLegResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Guid AssignmentId { get; set; }
    public DateTime AssignedAt { get; set; }
    public BrokerLegAssignmentDto Assignment { get; set; } = new();
}

// Handler implementation
public class AssignBrokerToLegCommandHandler : IRequestHandler<AssignBrokerToLegCommand, AssignBrokerToLegResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IBrokerService _brokerService;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AssignBrokerToLegCommandHandler> _logger;

    public async Task<AssignBrokerToLegResponse> Handle(AssignBrokerToLegCommand request, CancellationToken cancellationToken)
    {
        // Get trip and validate
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
            throw new NotFoundException($"Trip {request.TripId} not found");

        var leg = trip.Legs.FirstOrDefault(l => l.Id == request.LegId);
        if (leg == null)
            throw new NotFoundException($"Trip leg {request.LegId} not found");

        // Validate broker exists and is available
        var broker = await _brokerService.GetBrokerAsync(request.BrokerId, cancellationToken);
        if (broker == null)
            throw new NotFoundException($"Broker {request.BrokerId} not found");

        if (!broker.IsAvailable)
            throw new InvalidOperationException($"Broker {request.BrokerId} is not available for assignment");

        // Create assignment metadata
        var metadata = new BrokerLegAssignmentMetadata(
            request.ExpectedCommission,
            request.SpecialInstructions,
            request.RequiredDocuments);

        // Assign broker to leg
        leg.AssignBroker(request.BrokerId, GetCurrentUserId(), request.AssignmentNotes, metadata);

        // Save changes
        await _tripRepository.UpdateAsync(trip, cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("trip.leg.broker.assigned", new
        {
            TripId = request.TripId,
            LegId = request.LegId,
            BrokerId = request.BrokerId,
            AssignmentId = leg.BrokerAssignment?.Id,
            AssignedBy = GetCurrentUserId(),
            AssignedAt = leg.BrokerAssignmentDate,
            ExpectedCommission = request.ExpectedCommission,
            SpecialInstructions = request.SpecialInstructions
        }, cancellationToken);

        // Send notification to broker if requested
        if (request.NotifyBroker)
        {
            await _messageBroker.PublishAsync("notification.broker.leg.assigned", new
            {
                BrokerId = request.BrokerId,
                TripId = request.TripId,
                LegId = request.LegId,
                TripNumber = trip.TripNumber,
                LegDescription = $"{leg.StartLocation} to {leg.EndLocation}",
                AssignmentNotes = request.AssignmentNotes,
                SpecialInstructions = request.SpecialInstructions
            }, cancellationToken);
        }

        var response = new AssignBrokerToLegResponse
        {
            Success = true,
            Message = "Broker assigned to leg successfully",
            AssignmentId = leg.BrokerAssignment?.Id ?? Guid.Empty,
            AssignedAt = leg.BrokerAssignmentDate ?? DateTime.UtcNow,
            Assignment = MapToDto(leg.BrokerAssignment)
        };

        _logger.LogInformation("Successfully assigned broker {BrokerId} to trip leg {LegId} of trip {TripId}",
            request.BrokerId, request.LegId, request.TripId);

        return response;
    }
}
```

## 3. RFQ-to-Quote-to-Order Funnel Tracking

### Overview

Implement comprehensive funnel analytics in AnalyticsBIService with conversion tracking across the complete broker workflow.

### Implementation Strategy

#### 3.1 Enhanced Funnel Event Tracking

**Broker-Specific Funnel Events**:

```csharp
// Services/AnalyticsBIService/AnalyticsBIService.Domain/Entities/BrokerFunnelEvent.cs
public class BrokerFunnelEvent : Entity
{
    public Guid BrokerId { get; private set; }
    public Guid SessionId { get; private set; }
    public string FunnelType { get; private set; } = "RFQ_TO_ORDER";
    public string EventName { get; private set; }
    public int StepOrder { get; private set; }
    public Guid EntityId { get; private set; } // RFQ ID, Quote ID, Order ID
    public string EntityType { get; private set; }
    public BrokerFunnelEventProperties Properties { get; private set; }
    public decimal? ConversionValue { get; private set; }
    public DateTime Timestamp { get; private set; }
    public TimeSpan? TimeFromPreviousStep { get; private set; }

    public BrokerFunnelEvent(
        Guid brokerId,
        Guid sessionId,
        string eventName,
        int stepOrder,
        Guid entityId,
        string entityType,
        BrokerFunnelEventProperties properties,
        decimal? conversionValue = null)
    {
        BrokerId = brokerId;
        SessionId = sessionId;
        EventName = eventName;
        StepOrder = stepOrder;
        EntityId = entityId;
        EntityType = entityType;
        Properties = properties;
        ConversionValue = conversionValue;
        Timestamp = DateTime.UtcNow;
        CreatedAt = DateTime.UtcNow;
    }

    public void CalculateTimeFromPreviousStep(DateTime? previousStepTime)
    {
        if (previousStepTime.HasValue)
        {
            TimeFromPreviousStep = Timestamp - previousStepTime.Value;
        }
    }
}

public class BrokerFunnelEventProperties : ValueObject
{
    public string? RfqTitle { get; private set; }
    public string? LoadType { get; private set; }
    public string? RouteDescription { get; private set; }
    public decimal? QuoteAmount { get; private set; }
    public decimal? MarkupPercentage { get; private set; }
    public string? BidStatus { get; private set; }
    public string? RejectionReason { get; private set; }
    public Dictionary<string, object> CustomProperties { get; private set; }

    public BrokerFunnelEventProperties(
        string? rfqTitle = null,
        string? loadType = null,
        string? routeDescription = null,
        decimal? quoteAmount = null,
        decimal? markupPercentage = null,
        string? bidStatus = null,
        string? rejectionReason = null,
        Dictionary<string, object>? customProperties = null)
    {
        RfqTitle = rfqTitle;
        LoadType = loadType;
        RouteDescription = routeDescription;
        QuoteAmount = quoteAmount;
        MarkupPercentage = markupPercentage;
        BidStatus = bidStatus;
        RejectionReason = rejectionReason;
        CustomProperties = customProperties ?? new Dictionary<string, object>();
    }
}
```

#### 3.2 Funnel Analytics Service

**Comprehensive Funnel Analysis**:

```csharp
// Services/AnalyticsBIService/AnalyticsBIService.Application/Services/BrokerFunnelAnalyticsService.cs
public interface IBrokerFunnelAnalyticsService
{
    Task<BrokerFunnelAnalyticsDto> GetBrokerFunnelAnalyticsAsync(Guid brokerId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<FunnelConversionReportDto> GenerateConversionReportAsync(FunnelConversionReportRequest request, CancellationToken cancellationToken = default);
    Task TrackFunnelEventAsync(BrokerFunnelEvent funnelEvent, CancellationToken cancellationToken = default);
    Task<List<FunnelDropOffAnalysisDto>> AnalyzeFunnelDropOffsAsync(Guid brokerId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
}

public class BrokerFunnelAnalyticsService : IBrokerFunnelAnalyticsService
{
    private readonly IBrokerFunnelEventRepository _eventRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<BrokerFunnelAnalyticsService> _logger;

    public async Task<BrokerFunnelAnalyticsDto> GetBrokerFunnelAnalyticsAsync(
        Guid brokerId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        var cacheKey = $"broker_funnel_analytics_{brokerId}_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}";
        var cached = await _cacheService.GetAsync<BrokerFunnelAnalyticsDto>(cacheKey, cancellationToken);
        if (cached != null)
            return cached;

        var events = await _eventRepository.GetBrokerEventsAsync(brokerId, fromDate, toDate, cancellationToken);

        var analytics = new BrokerFunnelAnalyticsDto
        {
            BrokerId = brokerId,
            FromDate = fromDate,
            ToDate = toDate,
            GeneratedAt = DateTime.UtcNow
        };

        // Calculate funnel metrics
        analytics.TotalRfqsViewed = events.Count(e => e.EventName == "RFQ_VIEWED");
        analytics.TotalQuotesSubmitted = events.Count(e => e.EventName == "QUOTE_SUBMITTED");
        analytics.TotalOrdersWon = events.Count(e => e.EventName == "ORDER_WON");

        // Calculate conversion rates
        analytics.RfqToQuoteConversionRate = analytics.TotalRfqsViewed > 0
            ? (decimal)analytics.TotalQuotesSubmitted / analytics.TotalRfqsViewed * 100
            : 0;

        analytics.QuoteToOrderConversionRate = analytics.TotalQuotesSubmitted > 0
            ? (decimal)analytics.TotalOrdersWon / analytics.TotalQuotesSubmitted * 100
            : 0;

        analytics.OverallConversionRate = analytics.TotalRfqsViewed > 0
            ? (decimal)analytics.TotalOrdersWon / analytics.TotalRfqsViewed * 100
            : 0;

        // Calculate average times
        analytics.AverageTimeToQuote = CalculateAverageTimeBetweenSteps(events, "RFQ_VIEWED", "QUOTE_SUBMITTED");
        analytics.AverageTimeToOrder = CalculateAverageTimeBetweenSteps(events, "QUOTE_SUBMITTED", "ORDER_WON");

        // Calculate revenue metrics
        var orderEvents = events.Where(e => e.EventName == "ORDER_WON" && e.ConversionValue.HasValue);
        analytics.TotalRevenue = orderEvents.Sum(e => e.ConversionValue ?? 0);
        analytics.AverageOrderValue = orderEvents.Any() ? orderEvents.Average(e => e.ConversionValue ?? 0) : 0;

        // Generate funnel steps
        analytics.FunnelSteps = GenerateFunnelSteps(events);

        // Cache results
        await _cacheService.SetAsync(cacheKey, analytics, TimeSpan.FromHours(1), cancellationToken);

        return analytics;
    }

    private List<FunnelStepDto> GenerateFunnelSteps(List<BrokerFunnelEvent> events)
    {
        var steps = new List<FunnelStepDto>();

        var stepDefinitions = new[]
        {
            new { Name = "RFQs Viewed", EventName = "RFQ_VIEWED", Order = 1 },
            new { Name = "RFQs Interested", EventName = "RFQ_INTERESTED", Order = 2 },
            new { Name = "Quotes Started", EventName = "QUOTE_STARTED", Order = 3 },
            new { Name = "Quotes Submitted", EventName = "QUOTE_SUBMITTED", Order = 4 },
            new { Name = "Quotes Accepted", EventName = "QUOTE_ACCEPTED", Order = 5 },
            new { Name = "Orders Won", EventName = "ORDER_WON", Order = 6 }
        };

        foreach (var stepDef in stepDefinitions)
        {
            var stepEvents = events.Where(e => e.EventName == stepDef.EventName).ToList();
            var uniqueEntities = stepEvents.Select(e => e.EntityId).Distinct().Count();

            var step = new FunnelStepDto
            {
                StepName = stepDef.Name,
                StepOrder = stepDef.Order,
                EventCount = stepEvents.Count,
                UniqueEntityCount = uniqueEntities,
                ConversionValue = stepEvents.Sum(e => e.ConversionValue ?? 0)
            };

            // Calculate conversion rate from previous step
            if (steps.Any())
            {
                var previousStep = steps.Last();
                step.ConversionFromPrevious = previousStep.UniqueEntityCount > 0
                    ? (decimal)uniqueEntities / previousStep.UniqueEntityCount * 100
                    : 0;
            }
            else
            {
                step.ConversionFromPrevious = 100; // First step is always 100%
            }

            steps.Add(step);
        }

        return steps;
    }
}
```

## 4. Specific Alert Types Implementation

### Overview

Enhance CommunicationNotification service with broker-specific alert types and preferences for targeted notifications.

### Implementation Strategy

#### 4.1 Broker-Specific Alert Types

**Enhanced Notification System**:

```csharp
// Services/CommunicationNotification/CommunicationNotification.Domain/Entities/BrokerAlertType.cs
public class BrokerAlertType : Entity
{
    public string AlertCode { get; private set; }
    public string AlertName { get; private set; }
    public string Description { get; private set; }
    public BrokerAlertCategory Category { get; private set; }
    public BrokerAlertPriority Priority { get; private set; }
    public bool IsActive { get; private set; }
    public BrokerAlertConfiguration Configuration { get; private set; }
    public List<string> ApplicableUserTypes { get; private set; } = new();
    public Dictionary<string, object> TemplateVariables { get; private set; } = new();

    public BrokerAlertType(
        string alertCode,
        string alertName,
        string description,
        BrokerAlertCategory category,
        BrokerAlertPriority priority,
        BrokerAlertConfiguration configuration)
    {
        AlertCode = alertCode ?? throw new ArgumentNullException(nameof(alertCode));
        AlertName = alertName ?? throw new ArgumentNullException(nameof(alertName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category;
        Priority = priority;
        Configuration = configuration;
        IsActive = true;
        ApplicableUserTypes.Add("Broker");
        CreatedAt = DateTime.UtcNow;
    }
}

public enum BrokerAlertCategory
{
    KycVerification,
    SubscriptionManagement,
    RfqOpportunities,
    QuoteManagement,
    OrderUpdates,
    PaymentNotifications,
    PerformanceAlerts,
    SystemNotifications
}

public enum BrokerAlertPriority
{
    Low,
    Medium,
    High,
    Critical,
    Urgent
}

public class BrokerAlertConfiguration : ValueObject
{
    public bool IsRealTimeEnabled { get; private set; }
    public bool IsEmailEnabled { get; private set; }
    public bool IsSmsEnabled { get; private set; }
    public bool IsPushEnabled { get; private set; }
    public TimeSpan? DelayBeforeSending { get; private set; }
    public int MaxRetryAttempts { get; private set; }
    public List<string> RequiredChannels { get; private set; }
    public Dictionary<string, object> ChannelSpecificSettings { get; private set; }

    public BrokerAlertConfiguration(
        bool isRealTimeEnabled = true,
        bool isEmailEnabled = true,
        bool isSmsEnabled = false,
        bool isPushEnabled = true,
        TimeSpan? delayBeforeSending = null,
        int maxRetryAttempts = 3,
        List<string>? requiredChannels = null,
        Dictionary<string, object>? channelSpecificSettings = null)
    {
        IsRealTimeEnabled = isRealTimeEnabled;
        IsEmailEnabled = isEmailEnabled;
        IsSmsEnabled = isSmsEnabled;
        IsPushEnabled = isPushEnabled;
        DelayBeforeSending = delayBeforeSending;
        MaxRetryAttempts = Math.Max(1, maxRetryAttempts);
        RequiredChannels = requiredChannels ?? new List<string>();
        ChannelSpecificSettings = channelSpecificSettings ?? new Dictionary<string, object>();
    }
}
```

This comprehensive implementation covers all medium priority features with detailed technical specifications, domain models, application services, and integration patterns.
