using FluentAssertions;
using FinancialPayment.Domain.ValueObjects;
using Xunit;

namespace FinancialPayment.Tests.Domain.ValueObjects;

public class MoneyTests
{
    [Fact]
    public void Constructor_ShouldCreateMoney_WithValidAmountAndCurrency()
    {
        // Arrange
        var amount = 100.50m;
        var currency = "INR";

        // Act
        var money = new Money(amount, currency);

        // Assert
        money.Amount.Should().Be(100.50m);
        money.Currency.Should().Be("INR");
    }

    [Fact]
    public void Constructor_ShouldRoundAmount_ToTwoDecimalPlaces()
    {
        // Arrange
        var amount = 100.12345m;
        var currency = "INR";

        // Act
        var money = new Money(amount, currency);

        // Assert
        money.Amount.Should().Be(100.12m);
    }

    [Fact]
    public void Constructor_ShouldConvertCurrency_ToUpperCase()
    {
        // Arrange
        var amount = 100m;
        var currency = "inr";

        // Act
        var money = new Money(amount, currency);

        // Assert
        money.Currency.Should().Be("INR");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenAmountIsNegative()
    {
        // Arrange
        var amount = -100m;
        var currency = "INR";

        // Act & Assert
        var action = () => new Money(amount, currency);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Amount cannot be negative");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenCurrencyIsEmpty()
    {
        // Arrange
        var amount = 100m;
        var currency = "";

        // Act & Assert
        var action = () => new Money(amount, currency);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Currency cannot be empty");
    }

    [Fact]
    public void Zero_ShouldCreateZeroMoney_WithSpecifiedCurrency()
    {
        // Act
        var money = Money.Zero("USD");

        // Assert
        money.Amount.Should().Be(0m);
        money.Currency.Should().Be("USD");
    }

    [Fact]
    public void Addition_ShouldAddAmounts_WhenCurrenciesMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(50m, "INR");

        // Act
        var result = money1 + money2;

        // Assert
        result.Amount.Should().Be(150m);
        result.Currency.Should().Be("INR");
    }

    [Fact]
    public void Addition_ShouldThrowException_WhenCurrenciesDontMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(50m, "USD");

        // Act & Assert
        var action = () => money1 + money2;

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot add money with different currencies");
    }

    [Fact]
    public void Subtraction_ShouldSubtractAmounts_WhenCurrenciesMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(30m, "INR");

        // Act
        var result = money1 - money2;

        // Assert
        result.Amount.Should().Be(70m);
        result.Currency.Should().Be("INR");
    }

    [Fact]
    public void Subtraction_ShouldThrowException_WhenCurrenciesDontMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(30m, "USD");

        // Act & Assert
        var action = () => money1 - money2;

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot subtract money with different currencies");
    }

    [Fact]
    public void Multiplication_ShouldMultiplyAmount_ByMultiplier()
    {
        // Arrange
        var money = new Money(100m, "INR");
        var multiplier = 2.5m;

        // Act
        var result = money * multiplier;

        // Assert
        result.Amount.Should().Be(250m);
        result.Currency.Should().Be("INR");
    }

    [Fact]
    public void Division_ShouldDivideAmount_ByDivisor()
    {
        // Arrange
        var money = new Money(100m, "INR");
        var divisor = 4m;

        // Act
        var result = money / divisor;

        // Assert
        result.Amount.Should().Be(25m);
        result.Currency.Should().Be("INR");
    }

    [Fact]
    public void Division_ShouldThrowException_WhenDivisorIsZero()
    {
        // Arrange
        var money = new Money(100m, "INR");
        var divisor = 0m;

        // Act & Assert
        var action = () => money / divisor;

        action.Should().Throw<DivideByZeroException>()
            .WithMessage("Cannot divide by zero");
    }

    [Fact]
    public void GreaterThan_ShouldReturnTrue_WhenLeftAmountIsGreater()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(50m, "INR");

        // Act & Assert
        (money1 > money2).Should().BeTrue();
        (money2 > money1).Should().BeFalse();
    }

    [Fact]
    public void GreaterThan_ShouldThrowException_WhenCurrenciesDontMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(50m, "USD");

        // Act & Assert
        var action = () => money1 > money2;

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot compare money with different currencies");
    }

    [Fact]
    public void LessThan_ShouldReturnTrue_WhenLeftAmountIsLess()
    {
        // Arrange
        var money1 = new Money(50m, "INR");
        var money2 = new Money(100m, "INR");

        // Act & Assert
        (money1 < money2).Should().BeTrue();
        (money2 < money1).Should().BeFalse();
    }

    [Fact]
    public void Equality_ShouldReturnTrue_WhenAmountAndCurrencyMatch()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(100m, "INR");

        // Act & Assert
        (money1 == money2).Should().BeTrue();
        money1.Equals(money2).Should().BeTrue();
        money1.GetHashCode().Should().Be(money2.GetHashCode());
    }

    [Fact]
    public void Equality_ShouldReturnFalse_WhenAmountsDiffer()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(50m, "INR");

        // Act & Assert
        (money1 == money2).Should().BeFalse();
        (money1 != money2).Should().BeTrue();
        money1.Equals(money2).Should().BeFalse();
    }

    [Fact]
    public void Equality_ShouldReturnFalse_WhenCurrenciesDiffer()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(100m, "USD");

        // Act & Assert
        (money1 == money2).Should().BeFalse();
        (money1 != money2).Should().BeTrue();
        money1.Equals(money2).Should().BeFalse();
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var money = new Money(1234.56m, "INR");

        // Act
        var result = money.ToString();

        // Assert
        result.Should().Be("1234.56 INR");
    }

    [Fact]
    public void GreaterThanOrEqual_ShouldWork_Correctly()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(100m, "INR");
        var money3 = new Money(50m, "INR");

        // Act & Assert
        (money1 >= money2).Should().BeTrue(); // Equal
        (money1 >= money3).Should().BeTrue(); // Greater
        (money3 >= money1).Should().BeFalse(); // Less
    }

    [Fact]
    public void LessThanOrEqual_ShouldWork_Correctly()
    {
        // Arrange
        var money1 = new Money(100m, "INR");
        var money2 = new Money(100m, "INR");
        var money3 = new Money(150m, "INR");

        // Act & Assert
        (money1 <= money2).Should().BeTrue(); // Equal
        (money1 <= money3).Should().BeTrue(); // Less
        (money3 <= money1).Should().BeFalse(); // Greater
    }
}
