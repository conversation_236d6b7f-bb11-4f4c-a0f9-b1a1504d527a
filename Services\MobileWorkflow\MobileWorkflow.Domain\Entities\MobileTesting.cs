using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class TestSuite : AggregateRoot
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string TestType { get; private set; } // Unit, Integration, E2E, Performance, UI
    public string Platform { get; private set; } // iOS, Android, Web, All
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> TestEnvironment { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<TestCase> TestCases { get; private set; } = new List<TestCase>();
    public virtual ICollection<TestExecution> TestExecutions { get; private set; } = new List<TestExecution>();

    private TestSuite() { } // EF Core

    public TestSuite(
        string name,
        string description,
        string testType,
        string platform,
        Dictionary<string, object> configuration,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        TestType = testType ?? throw new ArgumentNullException(nameof(testType));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        TestEnvironment = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new TestSuiteCreatedEvent(Id, Name, TestType, Platform, CreatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestSuiteConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateTestEnvironment(Dictionary<string, object> testEnvironment)
    {
        TestEnvironment = testEnvironment ?? throw new ArgumentNullException(nameof(testEnvironment));
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestSuiteActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestSuiteDeactivatedEvent(Id, Name));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetTestEnvironment<T>(string key, T? defaultValue = default)
    {
        if (TestEnvironment.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class TestCase : AggregateRoot
{
    public Guid TestSuiteId { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string TestSteps { get; private set; }
    public string ExpectedResult { get; private set; }
    public int Priority { get; private set; } // 1 = High, 2 = Medium, 3 = Low
    public string Category { get; private set; } // Smoke, Regression, Functional, etc.
    public bool IsAutomated { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, object> TestData { get; private set; }
    public Dictionary<string, object> Preconditions { get; private set; }
    public Dictionary<string, object> Postconditions { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual TestSuite TestSuite { get; private set; } = null!;
    public virtual ICollection<TestCaseExecution> TestCaseExecutions { get; private set; } = new List<TestCaseExecution>();

    private TestCase() { } // EF Core

    public TestCase(
        Guid testSuiteId,
        string name,
        string description,
        string testSteps,
        string expectedResult,
        int priority,
        string category,
        bool isAutomated,
        string createdBy)
    {
        TestSuiteId = testSuiteId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        TestSteps = testSteps ?? throw new ArgumentNullException(nameof(testSteps));
        ExpectedResult = expectedResult ?? throw new ArgumentNullException(nameof(expectedResult));
        Priority = priority;
        Category = category ?? throw new ArgumentNullException(nameof(category));
        IsAutomated = isAutomated;
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        TestData = new Dictionary<string, object>();
        Preconditions = new Dictionary<string, object>();
        Postconditions = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new TestCaseCreatedEvent(Id, TestSuiteId, Name, Category, IsAutomated, CreatedBy));
    }

    public void UpdateTestCase(
        string? name = null,
        string? description = null,
        string? testSteps = null,
        string? expectedResult = null,
        int? priority = null,
        string? category = null,
        bool? isAutomated = null,
        string? updatedBy = null)
    {
        if (!string.IsNullOrEmpty(name)) Name = name;
        if (!string.IsNullOrEmpty(description)) Description = description;
        if (!string.IsNullOrEmpty(testSteps)) TestSteps = testSteps;
        if (!string.IsNullOrEmpty(expectedResult)) ExpectedResult = expectedResult;
        if (priority.HasValue) Priority = priority.Value;
        if (!string.IsNullOrEmpty(category)) Category = category;
        if (isAutomated.HasValue) IsAutomated = isAutomated.Value;

        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestCaseUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetTestData(Dictionary<string, object> testData)
    {
        TestData = testData ?? throw new ArgumentNullException(nameof(testData));
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPreconditions(Dictionary<string, object> preconditions)
    {
        Preconditions = preconditions ?? throw new ArgumentNullException(nameof(preconditions));
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPostconditions(Dictionary<string, object> postconditions)
    {
        Postconditions = postconditions ?? throw new ArgumentNullException(nameof(postconditions));
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestCaseActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new TestCaseDeactivatedEvent(Id, Name));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetTestData<T>(string key, T? defaultValue = default)
    {
        if (TestData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPrecondition<T>(string key, T? defaultValue = default)
    {
        if (Preconditions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPostcondition<T>(string key, T? defaultValue = default)
    {
        if (Postconditions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class TestExecution : AggregateRoot
{
    public Guid TestSuiteId { get; private set; }
    public string ExecutionName { get; private set; }
    public string Status { get; private set; } // Pending, Running, Completed, Failed, Cancelled
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public string ExecutedBy { get; private set; }
    public string? ExecutionEnvironment { get; private set; }
    public Dictionary<string, object> ExecutionConfiguration { get; private set; }
    public Dictionary<string, object> ExecutionResults { get; private set; }
    public Dictionary<string, object> PerformanceMetrics { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual TestSuite TestSuite { get; private set; } = null!;
    public virtual ICollection<TestCaseExecution> TestCaseExecutions { get; private set; } = new List<TestCaseExecution>();

    private TestExecution() { } // EF Core

    public TestExecution(
        Guid testSuiteId,
        string executionName,
        string executedBy,
        Dictionary<string, object> executionConfiguration)
    {
        TestSuiteId = testSuiteId;
        ExecutionName = executionName ?? throw new ArgumentNullException(nameof(executionName));
        ExecutedBy = executedBy ?? throw new ArgumentNullException(nameof(executedBy));
        ExecutionConfiguration = executionConfiguration ?? throw new ArgumentNullException(nameof(executionConfiguration));

        Status = "Pending";
        StartedAt = DateTime.UtcNow;
        ExecutionResults = new Dictionary<string, object>();
        PerformanceMetrics = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new TestExecutionCreatedEvent(Id, TestSuiteId, ExecutionName, ExecutedBy));
    }

    public void Start()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start execution with status: {Status}");

        Status = "Running";
        StartedAt = DateTime.UtcNow;

        AddDomainEvent(new TestExecutionStartedEvent(Id, TestSuiteId, ExecutionName));
    }

    public void Complete()
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot complete execution with status: {Status}");

        Status = "Completed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;

        AddDomainEvent(new TestExecutionCompletedEvent(Id, TestSuiteId, ExecutionName, Duration?.TotalMinutes));
    }

    public void Fail(string errorMessage)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        ErrorMessage = errorMessage;

        AddDomainEvent(new TestExecutionFailedEvent(Id, TestSuiteId, ExecutionName, errorMessage));
    }

    public void Cancel()
    {
        if (Status == "Completed")
            throw new InvalidOperationException("Cannot cancel completed execution");

        Status = "Cancelled";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;

        AddDomainEvent(new TestExecutionCancelledEvent(Id, TestSuiteId, ExecutionName));
    }

    public void SetExecutionEnvironment(string executionEnvironment)
    {
        ExecutionEnvironment = executionEnvironment;
    }

    public void AddExecutionResult(string key, object value)
    {
        ExecutionResults[key] = value;
    }

    public void AddPerformanceMetric(string key, object value)
    {
        PerformanceMetrics[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsCompleted()
    {
        return Status == "Completed" || Status == "Failed" || Status == "Cancelled";
    }

    public bool IsRunning()
    {
        return Status == "Running";
    }

    public bool IsSuccessful()
    {
        return Status == "Completed";
    }

    public T? GetExecutionResult<T>(string key, T? defaultValue = default)
    {
        if (ExecutionResults.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceMetric<T>(string key, T? defaultValue = default)
    {
        if (PerformanceMetrics.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetExecutionConfiguration<T>(string key, T? defaultValue = default)
    {
        if (ExecutionConfiguration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class TestCaseExecution : AggregateRoot
{
    public Guid TestExecutionId { get; private set; }
    public Guid TestCaseId { get; private set; }
    public string Status { get; private set; } // Pending, Running, Passed, Failed, Skipped
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public string? ActualResult { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorStackTrace { get; private set; }
    public Dictionary<string, object> TestOutput { get; private set; }
    public Dictionary<string, object> Screenshots { get; private set; }
    public Dictionary<string, object> Logs { get; private set; }
    public Dictionary<string, object> PerformanceData { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual TestExecution TestExecution { get; private set; } = null!;
    public virtual TestCase TestCase { get; private set; } = null!;

    private TestCaseExecution() { } // EF Core

    public TestCaseExecution(
        Guid testExecutionId,
        Guid testCaseId)
    {
        TestExecutionId = testExecutionId;
        TestCaseId = testCaseId;

        Status = "Pending";
        StartedAt = DateTime.UtcNow;
        TestOutput = new Dictionary<string, object>();
        Screenshots = new Dictionary<string, object>();
        Logs = new Dictionary<string, object>();
        PerformanceData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new TestCaseExecutionCreatedEvent(Id, TestExecutionId, TestCaseId));
    }

    public void Start()
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot start test case execution with status: {Status}");

        Status = "Running";
        StartedAt = DateTime.UtcNow;

        AddDomainEvent(new TestCaseExecutionStartedEvent(Id, TestExecutionId, TestCaseId));
    }

    public void Pass(string? actualResult = null)
    {
        if (Status != "Running")
            throw new InvalidOperationException($"Cannot pass test case execution with status: {Status}");

        Status = "Passed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        ActualResult = actualResult;

        AddDomainEvent(new TestCaseExecutionPassedEvent(Id, TestExecutionId, TestCaseId, Duration?.TotalSeconds));
    }

    public void Fail(string errorMessage, string? errorStackTrace = null, string? actualResult = null)
    {
        Status = "Failed";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        ErrorMessage = errorMessage;
        ErrorStackTrace = errorStackTrace;
        ActualResult = actualResult;

        AddDomainEvent(new TestCaseExecutionFailedEvent(Id, TestExecutionId, TestCaseId, errorMessage));
    }

    public void Skip(string reason)
    {
        Status = "Skipped";
        CompletedAt = DateTime.UtcNow;
        Duration = CompletedAt - StartedAt;
        ErrorMessage = reason;

        AddDomainEvent(new TestCaseExecutionSkippedEvent(Id, TestExecutionId, TestCaseId, reason));
    }

    public void AddTestOutput(string key, object value)
    {
        TestOutput[key] = value;
    }

    public void AddScreenshot(string name, string screenshotData)
    {
        Screenshots[name] = screenshotData;
    }

    public void AddLog(string logLevel, string message)
    {
        var logKey = $"{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss.fff}_{logLevel}";
        Logs[logKey] = message;
    }

    public void AddPerformanceData(string key, object value)
    {
        PerformanceData[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public bool IsCompleted()
    {
        return Status == "Passed" || Status == "Failed" || Status == "Skipped";
    }

    public bool IsSuccessful()
    {
        return Status == "Passed";
    }

    public bool IsFailed()
    {
        return Status == "Failed";
    }

    public bool IsSkipped()
    {
        return Status == "Skipped";
    }

    public T? GetTestOutput<T>(string key, T? defaultValue = default)
    {
        if (TestOutput.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetPerformanceData<T>(string key, T? defaultValue = default)
    {
        if (PerformanceData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


