using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.Reports;
using TripManagement.Application.DTOs.Reports;
using TripManagement.Domain.Enums;

namespace TripManagement.API.Controllers;

/// <summary>
/// Controller for routing failure reporting
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RoutingFailureReportController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<RoutingFailureReportController> _logger;

    public RoutingFailureReportController(IMediator mediator, ILogger<RoutingFailureReportController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Generate routing failure report
    /// </summary>
    [HttpPost("generate")]
    [Authorize(Roles = "Admin,Manager,Analyst")]
    public async Task<ActionResult<RoutingFailureReportDto>> GenerateRoutingFailureReport(
        [FromBody] GenerateRoutingFailureReportRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new GenerateRoutingFailureReportCommand
            {
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                FailureTypes = request.FailureTypes,
                Severities = request.Severities,
                Statuses = request.Statuses,
                Categories = request.Categories,
                IsRecurring = request.IsRecurring,
                MinImpactCost = request.MinImpactCost,
                MaxImpactCost = request.MaxImpactCost,
                IncludeResolved = request.IncludeResolved,
                IncludeFailureDetails = request.IncludeFailureDetails,
                IncludePatternAnalysis = request.IncludePatternAnalysis,
                IncludeImpactAnalysis = request.IncludeImpactAnalysis,
                IncludeTrendAnalysis = request.IncludeTrendAnalysis,
                IncludeRootCauseAnalysis = request.IncludeRootCauseAnalysis,
                Format = request.Format,
                MaxRecords = request.MaxRecords,
                SortBy = request.SortBy,
                SortOrder = request.SortOrder,
                RequestedBy = GetCurrentUserId(),
                RequestedByRole = GetCurrentUserRole(),
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var result = await _mediator.Send(command, cancellationToken);
            
            _logger.LogInformation("Routing failure report generated successfully for user {UserId}", GetCurrentUserId());
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating routing failure report");
            return StatusCode(500, "An error occurred while generating the routing failure report");
        }
    }

    /// <summary>
    /// Get routing failure statistics summary
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Manager,Analyst")]
    public async Task<ActionResult<RoutingFailureStatsDto>> GetRoutingFailureStatistics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new GenerateRoutingFailureReportCommand
            {
                FromDate = fromDate,
                ToDate = toDate,
                IncludeFailureDetails = false,
                IncludePatternAnalysis = false,
                IncludeImpactAnalysis = true,
                IncludeTrendAnalysis = false,
                IncludeRootCauseAnalysis = false,
                RequestedBy = GetCurrentUserId(),
                RequestedByRole = GetCurrentUserRole(),
                IpAddress = GetClientIpAddress(),
                UserAgent = Request.Headers.UserAgent.ToString()
            };

            var report = await _mediator.Send(command, cancellationToken);
            
            var stats = new RoutingFailureStatsDto
            {
                TotalFailures = report.Summary.TotalFailures,
                OpenFailures = report.Summary.OpenFailures,
                ResolvedFailures = report.Summary.ResolvedFailures,
                CriticalFailures = report.Summary.CriticalFailures,
                RecurringFailures = report.Summary.RecurringFailures,
                TotalImpactCost = report.Summary.TotalImpactCost,
                AverageResolutionTimeHours = report.Summary.AverageResolutionTimeHours,
                ResolutionRate = report.Summary.ResolutionRate,
                FailuresByType = report.Summary.FailuresByType,
                FailuresBySeverity = report.Summary.FailuresBySeverity
            };
            
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving routing failure statistics");
            return StatusCode(500, "An error occurred while retrieving routing failure statistics");
        }
    }

    private Guid GetCurrentUserId()
    {
        // Implementation would extract user ID from JWT token or claims
        var userIdClaim = User.FindFirst("sub")?.Value ?? User.FindFirst("user_id")?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        // Implementation would extract role from JWT token or claims
        return User.FindFirst("role")?.Value ?? "User";
    }

    private string? GetClientIpAddress()
    {
        return HttpContext.Connection.RemoteIpAddress?.ToString();
    }
}

/// <summary>
/// Request DTO for generating routing failure report
/// </summary>
public class GenerateRoutingFailureReportRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<FailureType>? FailureTypes { get; set; }
    public List<FailureSeverity>? Severities { get; set; }
    public List<FailureStatus>? Statuses { get; set; }
    public List<string>? Categories { get; set; }
    public bool? IsRecurring { get; set; }
    public decimal? MinImpactCost { get; set; }
    public decimal? MaxImpactCost { get; set; }
    public bool IncludeResolved { get; set; } = true;
    public bool IncludeFailureDetails { get; set; } = true;
    public bool IncludePatternAnalysis { get; set; } = true;
    public bool IncludeImpactAnalysis { get; set; } = true;
    public bool IncludeTrendAnalysis { get; set; } = false;
    public bool IncludeRootCauseAnalysis { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.Json;
    public int? MaxRecords { get; set; }
    public string? SortBy { get; set; }
    public string? SortOrder { get; set; } = "DESC";
}

/// <summary>
/// DTO for routing failure statistics
/// </summary>
public class RoutingFailureStatsDto
{
    public int TotalFailures { get; set; }
    public int OpenFailures { get; set; }
    public int ResolvedFailures { get; set; }
    public int CriticalFailures { get; set; }
    public int RecurringFailures { get; set; }
    public decimal TotalImpactCost { get; set; }
    public decimal AverageResolutionTimeHours { get; set; }
    public decimal ResolutionRate { get; set; }
    public Dictionary<FailureType, int> FailuresByType { get; set; } = new();
    public Dictionary<FailureSeverity, int> FailuresBySeverity { get; set; } = new();
}
