using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Reporting;
using AuditCompliance.Infrastructure.MultiTenant;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for automated compliance reporting
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Route("api/[controller]")] // Backward compatibility
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[Authorize]
[RequireTenant]
public class ReportingController : ControllerBase
{
    private readonly IAutomatedReportingService _reportingService;
    private readonly IReportTemplateEngine _templateEngine;
    private readonly ILogger<ReportingController> _logger;

    public ReportingController(
        IAutomatedReportingService reportingService,
        IReportTemplateEngine templateEngine,
        ILogger<ReportingController> logger)
    {
        _reportingService = reportingService;
        _templateEngine = templateEngine;
        _logger = logger;
    }

    /// <summary>
    /// Get all report schedules
    /// </summary>
    [HttpGet("schedules")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ReportScheduleDto>>> GetReportSchedules()
    {
        try
        {
            var schedules = await _reportingService.GetReportSchedulesAsync();
            return Ok(schedules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report schedules");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report schedule by ID
    /// </summary>
    [HttpGet("schedules/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ReportScheduleDto>> GetReportSchedule(Guid id)
    {
        try
        {
            var schedule = await _reportingService.GetReportScheduleAsync(id);
            if (schedule == null)
            {
                return NotFound($"Report schedule with ID {id} not found");
            }

            return Ok(schedule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report schedule {ScheduleId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new report schedule
    /// </summary>
    [HttpPost("schedules")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> CreateReportSchedule([FromBody] CreateReportScheduleDto scheduleDto)
    {
        try
        {
            var scheduleId = await _reportingService.CreateReportScheduleAsync(scheduleDto);
            
            var response = new
            {
                ScheduleId = scheduleId,
                Message = "Report schedule created successfully",
                Links = new
                {
                    Self = $"/api/reporting/schedules/{scheduleId}",
                    Execute = $"/api/reporting/schedules/{scheduleId}/execute"
                }
            };

            return CreatedAtAction(nameof(GetReportSchedule), new { id = scheduleId }, response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report schedule");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update a report schedule
    /// </summary>
    [HttpPut("schedules/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> UpdateReportSchedule(Guid id, [FromBody] UpdateReportScheduleDto scheduleDto)
    {
        try
        {
            var success = await _reportingService.UpdateReportScheduleAsync(id, scheduleDto);
            if (!success)
            {
                return NotFound($"Report schedule with ID {id} not found");
            }

            return Ok(new { Message = "Report schedule updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report schedule {ScheduleId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete a report schedule
    /// </summary>
    [HttpDelete("schedules/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> DeleteReportSchedule(Guid id)
    {
        try
        {
            var success = await _reportingService.DeleteReportScheduleAsync(id);
            if (!success)
            {
                return NotFound($"Report schedule with ID {id} not found");
            }

            return Ok(new { Message = "Report schedule deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting report schedule {ScheduleId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate a report immediately
    /// </summary>
    [HttpPost("generate")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> GenerateReport([FromBody] GenerateReportDto reportDto)
    {
        try
        {
            var reportId = await _reportingService.GenerateReportAsync(reportDto);
            
            var response = new
            {
                ReportId = reportId,
                Message = "Report generation started",
                Status = "Processing",
                Links = new
                {
                    Status = $"/api/reporting/reports/{reportId}",
                    Download = $"/api/reporting/reports/{reportId}/download"
                }
            };

            return Accepted(response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available report templates
    /// </summary>
    [HttpGet("templates")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ReportTemplateDto>>> GetReportTemplates([FromQuery] string? category = null)
    {
        try
        {
            var templates = await _reportingService.GetReportTemplatesAsync(category);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a custom report template
    /// </summary>
    [HttpPost("templates")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> CreateReportTemplate([FromBody] CreateReportTemplateDto templateDto)
    {
        try
        {
            // Validate template before creating
            var validationResult = await _templateEngine.ValidateTemplateAsync(templateDto.Content);
            if (!validationResult.IsValid)
            {
                return BadRequest(new
                {
                    Message = "Template validation failed",
                    Errors = validationResult.Errors,
                    Warnings = validationResult.Warnings
                });
            }

            var templateId = await _reportingService.CreateReportTemplateAsync(templateDto);
            
            var response = new
            {
                TemplateId = templateId,
                Message = "Report template created successfully",
                ValidationResult = validationResult
            };

            return CreatedAtAction(nameof(GetReportTemplate), new { id = templateId }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating report template");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report template by ID
    /// </summary>
    [HttpGet("templates/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ReportTemplateDto>> GetReportTemplate(Guid id)
    {
        try
        {
            var templates = await _reportingService.GetReportTemplatesAsync();
            var template = templates.FirstOrDefault(t => t.Id == id);
            
            if (template == null)
            {
                return NotFound($"Report template with ID {id} not found");
            }

            return Ok(template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report template {TemplateId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update a report template
    /// </summary>
    [HttpPut("templates/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> UpdateReportTemplate(Guid id, [FromBody] UpdateReportTemplateDto templateDto)
    {
        try
        {
            // Validate template before updating
            var validationResult = await _templateEngine.ValidateTemplateAsync(templateDto.Content);
            if (!validationResult.IsValid)
            {
                return BadRequest(new
                {
                    Message = "Template validation failed",
                    Errors = validationResult.Errors,
                    Warnings = validationResult.Warnings
                });
            }

            var success = await _reportingService.UpdateReportTemplateAsync(id, templateDto);
            if (!success)
            {
                return NotFound($"Report template with ID {id} not found");
            }

            return Ok(new
            {
                Message = "Report template updated successfully",
                ValidationResult = validationResult
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating report template {TemplateId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report execution history
    /// </summary>
    [HttpGet("executions")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<List<ReportExecutionDto>>> GetReportExecutionHistory(
        [FromQuery] Guid? scheduleId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var executions = await _reportingService.GetReportExecutionHistoryAsync(
                scheduleId, fromDate, toDate, pageSize, pageNumber);
            return Ok(executions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report execution history");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get report by ID
    /// </summary>
    [HttpGet("reports/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<GeneratedReportDto>> GetReport(Guid id)
    {
        try
        {
            var report = await _reportingService.GetReportAsync(id);
            if (report == null)
            {
                return NotFound($"Report with ID {id} not found");
            }

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Download report file
    /// </summary>
    [HttpGet("reports/{id}/download")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult> DownloadReport(Guid id)
    {
        try
        {
            var reportFile = await _reportingService.DownloadReportAsync(id);
            if (reportFile == null)
            {
                return NotFound($"Report file with ID {id} not found or not ready");
            }

            return File(reportFile.Content, reportFile.ContentType, reportFile.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading report {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export report in different format
    /// </summary>
    [HttpPost("reports/{id}/export")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<object>> ExportReport(Guid id, [FromBody] ExportReportDto exportDto)
    {
        try
        {
            exportDto.ReportId = id;
            var exportId = await _reportingService.ExportReportAsync(exportDto);
            
            var response = new
            {
                ExportId = exportId,
                Message = "Report export started",
                Status = "Processing",
                Links = new
                {
                    Status = $"/api/reporting/exports/{exportId}/status",
                    Download = $"/api/reporting/exports/{exportId}/download"
                }
            };

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report {ReportId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get export status
    /// </summary>
    [HttpGet("exports/{exportId}/status")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ExportStatusDto>> GetExportStatus(Guid exportId)
    {
        try
        {
            var status = await _reportingService.GetExportStatusAsync(exportId);
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting export status {ExportId}", exportId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get compliance metrics for reporting
    /// </summary>
    [HttpPost("metrics")]
    [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
    public async Task<ActionResult<ComplianceMetricsDto>> GetComplianceMetrics([FromBody] ComplianceMetricsRequestDto request)
    {
        try
        {
            var metrics = await _reportingService.GetComplianceMetricsAsync(request);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting compliance metrics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate regulatory compliance report
    /// </summary>
    [HttpPost("regulatory")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<object>> GenerateRegulatoryReport([FromBody] RegulatoryReportRequestDto request)
    {
        try
        {
            var reportId = await _reportingService.GenerateRegulatoryReportAsync(request);
            
            var response = new
            {
                ReportId = reportId,
                Message = "Regulatory report generation started",
                Framework = request.RegulatoryFramework,
                Period = new { Start = request.ReportingPeriodStart, End = request.ReportingPeriodEnd },
                Links = new
                {
                    Status = $"/api/reporting/reports/{reportId}",
                    Download = $"/api/reporting/reports/{reportId}/download"
                }
            };

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating regulatory report");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get template variables for a specific template type
    /// </summary>
    [HttpGet("template-variables/{templateType}")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<List<TemplateVariableDto>>> GetTemplateVariables(string templateType)
    {
        try
        {
            var variables = await _templateEngine.GetTemplateVariablesAsync(templateType);
            return Ok(variables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template variables for type {TemplateType}", templateType);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate a report template
    /// </summary>
    [HttpPost("templates/validate")]
    [Authorize(Roles = "Admin,ComplianceOfficer")]
    public async Task<ActionResult<TemplateValidationResult>> ValidateTemplate([FromBody] ValidateTemplateDto validateDto)
    {
        try
        {
            var result = await _templateEngine.ValidateTemplateAsync(validateDto.Content);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating template");
            return StatusCode(500, "Internal server error");
        }
    }
}

/// <summary>
/// DTO for template validation
/// </summary>
public class ValidateTemplateDto
{
    public string Content { get; set; } = string.Empty;
}
