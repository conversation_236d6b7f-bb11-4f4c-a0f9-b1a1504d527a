using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IPaymentDisputeService
{
    Task<PaymentDispute> CreateDisputeAsync(
        Guid orderId,
        Guid initiatedBy,
        ParticipantRole initiatorRole,
        string title,
        string description,
        Money disputedAmount,
        DisputeCategory category,
        DisputePriority priority = DisputePriority.Medium,
        Guid? escrowAccountId = null,
        Guid? settlementId = null);

    Task<bool> AddCommentAsync(Guid disputeId, Guid authorId, ParticipantRole authorRole, string content, bool isInternal = false);
    Task<bool> AddDocumentAsync(Guid disputeId, string fileName, string fileUrl, string description, Guid uploadedBy);
    Task<bool> EscalateDisputeAsync(Guid disputeId, DisputePriority newPriority, string reason);
    Task<bool> StartInvestigationAsync(Guid disputeId, Guid investigatorId);
    Task<bool> ResolveDisputeAsync(Guid disputeId, Money resolvedAmount, string resolution, Guid resolvedBy);
    Task<bool> CloseDisputeAsync(Guid disputeId, string reason, Guid closedBy);
    Task<bool> ReopenDisputeAsync(Guid disputeId, string reason, Guid reopenedBy);

    Task<PaymentDispute?> GetDisputeByIdAsync(Guid disputeId);
    Task<List<PaymentDispute>> GetDisputesByOrderIdAsync(Guid orderId);
    Task<List<PaymentDispute>> GetDisputesByInitiatorAsync(Guid initiatorId);
    Task<List<PaymentDispute>> GetDisputesByStatusAsync(DisputeStatus status);
    Task<List<PaymentDispute>> GetDisputesByCategoryAsync(DisputeCategory category);
    Task<List<PaymentDispute>> GetDisputesByPriorityAsync(DisputePriority priority);
}

public class DisputeCreationRequest
{
    public Guid OrderId { get; set; }
    public Guid InitiatedBy { get; set; }
    public ParticipantRole InitiatorRole { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Money DisputedAmount { get; set; } = null!;
    public DisputeCategory Category { get; set; }
    public DisputePriority Priority { get; set; } = DisputePriority.Medium;
    public Guid? EscrowAccountId { get; set; }
    public Guid? SettlementId { get; set; }
}

public class DisputeResolutionResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
}
