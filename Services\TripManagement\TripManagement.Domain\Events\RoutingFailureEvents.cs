using Shared.Domain.Common;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.Events;

/// <summary>
/// Domain event raised when a routing failure is detected
/// </summary>
public class RoutingFailureDetectedEvent : DomainEvent
{
    public Guid RoutingFailureId { get; }
    public Guid TripId { get; }
    public FailureType FailureType { get; }
    public FailureSeverity Severity { get; }
    public string FailureReason { get; }

    public RoutingFailureDetectedEvent(Guid routingFailureId, Guid tripId, FailureType failureType, FailureSeverity severity, string failureReason)
    {
        RoutingFailureId = routingFailureId;
        TripId = tripId;
        FailureType = failureType;
        Severity = severity;
        FailureReason = failureReason;
    }
}

/// <summary>
/// Domain event raised when a routing failure is resolved
/// </summary>
public class RoutingFailureResolvedEvent : DomainEvent
{
    public Guid RoutingFailureId { get; }
    public Guid TripId { get; }
    public Guid ResolvedBy { get; }
    public string? ResolutionAction { get; }
    public TimeSpan? ResolutionTime { get; }

    public RoutingFailureResolvedEvent(Guid routingFailureId, Guid tripId, Guid resolvedBy, string? resolutionAction, TimeSpan? resolutionTime)
    {
        RoutingFailureId = routingFailureId;
        TripId = tripId;
        ResolvedBy = resolvedBy;
        ResolutionAction = resolutionAction;
        ResolutionTime = resolutionTime;
    }
}

/// <summary>
/// Domain event raised when a routing failure is escalated
/// </summary>
public class RoutingFailureEscalatedEvent : DomainEvent
{
    public Guid RoutingFailureId { get; }
    public Guid TripId { get; }
    public FailureSeverity OldSeverity { get; }
    public FailureSeverity NewSeverity { get; }
    public string EscalationReason { get; }

    public RoutingFailureEscalatedEvent(Guid routingFailureId, Guid tripId, FailureSeverity oldSeverity, FailureSeverity newSeverity, string escalationReason)
    {
        RoutingFailureId = routingFailureId;
        TripId = tripId;
        OldSeverity = oldSeverity;
        NewSeverity = newSeverity;
        EscalationReason = escalationReason;
    }
}


