using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Domain.Common;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class ReportExportRepository : OptimizedRepositoryBase<ReportExport, Guid>, IReportExportRepository
{
    public ReportExportRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<IEnumerable<ReportExport>> GetByReportIdAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExport>()
            .Where(e => e.ReportId == reportId)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<ReportExport>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<ReportExport>()
            .Where(e => e.UserId == userId)
            .OrderByDescending(e => e.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<ReportExport>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<ReportExport>> GetByFormatAsync(ExportFormat format, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<ReportExport>()
            .Where(e => e.Format == format)
            .OrderByDescending(e => e.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<ReportExport>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<ReportExport>> GetByStatusAsync(ReportStatus status, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<ReportExport>()
            .Where(e => e.Status == status)
            .OrderByDescending(e => e.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<ReportExport>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task UpdateStatusAsync(Guid exportId, ReportStatus status, string? filePath = null, CancellationToken cancellationToken = default)
    {
        var export = await Context.Set<ReportExport>()
            .FirstOrDefaultAsync(e => e.Id == exportId, cancellationToken);

        if (export != null)
        {
            export.UpdateStatus(status);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task DeleteOldExportsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldExports = await Context.Set<ReportExport>()
            .Where(e => e.CreatedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        Context.Set<ReportExport>().RemoveRange(oldExports);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<Guid> SaveTemplateAsync(ReportTemplate template, CancellationToken cancellationToken = default)
    {
        await Context.Set<ReportTemplate>().AddAsync(template, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
        return template.Id;
    }

    public async Task<IEnumerable<ReportExport>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExport>()
            .Where(e => e.UserId == userId)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ReportExport>> GetByFormatAsync(ExportFormat format, CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExport>()
            .Where(e => e.Format == format)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ReportExport>> GetActiveExportsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExport>()
            .Where(e => e.IsActive)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ReportExport>> GetExpiredExportsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<ReportExport>()
            .Where(e => e.ExpiresAt.HasValue && e.ExpiresAt < DateTime.UtcNow)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
