using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Queries.GetRfqExpirationStats;

public class GetRfqExpirationStatsQueryHandler : IRequestHandler<GetRfqExpirationStatsQuery, RfqExpirationStatsDto>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly ILogger<GetRfqExpirationStatsQueryHandler> _logger;

    public GetRfqExpirationStatsQueryHandler(
        IRfqRepository rfqRepository,
        ILogger<GetRfqExpirationStatsQueryHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _logger = logger;
    }

    public async Task<RfqExpirationStatsDto> Handle(GetRfqExpirationStatsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting RFQ expiration statistics for transport company {TransportCompanyId}",
            request.TransportCompanyId);

        var stats = new RfqExpirationStatsDto();

        try
        {
            // Get all RFQs for the specified criteria
            var allRfqs = await GetFilteredRfqsAsync(request, cancellationToken);

            // Calculate basic statistics
            stats.TotalActiveRfqs = allRfqs.Count(r => r.Status == RfqStatus.Published);
            stats.ExpiredRfqs = allRfqs.Count(r => r.Status == RfqStatus.Expired);

            // Get expiring soon counts
            var expiringSoon24 = await _rfqRepository.GetRfqsExpiringSoonAsync(24, cancellationToken);
            var expiringSoon12 = await _rfqRepository.GetRfqsExpiringSoonAsync(12, cancellationToken);
            var expiringSoon6 = await _rfqRepository.GetRfqsExpiringSoonAsync(6, cancellationToken);
            var expiringSoon1 = await _rfqRepository.GetRfqsExpiringSoonAsync(1, cancellationToken);

            if (request.TransportCompanyId.HasValue)
            {
                stats.ExpiringSoon24Hours = expiringSoon24.Count(r => r.TransportCompanyId == request.TransportCompanyId.Value);
                stats.ExpiringSoon12Hours = expiringSoon12.Count(r => r.TransportCompanyId == request.TransportCompanyId.Value);
                stats.ExpiringSoon6Hours = expiringSoon6.Count(r => r.TransportCompanyId == request.TransportCompanyId.Value);
                stats.ExpiringSoon1Hour = expiringSoon1.Count(r => r.TransportCompanyId == request.TransportCompanyId.Value);
            }
            else
            {
                stats.ExpiringSoon24Hours = expiringSoon24.Count;
                stats.ExpiringSoon12Hours = expiringSoon12.Count;
                stats.ExpiringSoon6Hours = expiringSoon6.Count;
                stats.ExpiringSoon1Hour = expiringSoon1.Count;
            }

            // Calculate extended RFQs count
            stats.ExtendedRfqs = allRfqs.Count(r => r.TimeframeExtensions.Any());

            // Calculate average timeframe hours
            var rfqsWithTimeframe = allRfqs.Where(r => r.Timeframe != null).ToList();
            if (rfqsWithTimeframe.Any())
            {
                stats.AverageTimeframeHours = rfqsWithTimeframe.Average(r => r.Timeframe!.TotalMinutes / 60.0);
            }

            // Calculate expiration rate
            var totalRfqs = stats.TotalActiveRfqs + stats.ExpiredRfqs;
            if (totalRfqs > 0)
            {
                stats.ExpirationRate = (double)stats.ExpiredRfqs / totalRfqs * 100;
            }

            // Calculate expiration trend (last 30 days)
            stats.ExpirationTrend = await CalculateExpirationTrendAsync(request, cancellationToken);

            _logger.LogInformation("Retrieved RFQ expiration statistics: {TotalActive} active, {Expired} expired, {ExtendedCount} extended",
                stats.TotalActiveRfqs, stats.ExpiredRfqs, stats.ExtendedRfqs);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving RFQ expiration statistics");
            throw;
        }
    }

    private async Task<List<Domain.Entities.RFQ>> GetFilteredRfqsAsync(GetRfqExpirationStatsQuery request, CancellationToken cancellationToken)
    {
        // This is a simplified implementation. In a real scenario, you'd want to add proper filtering methods to the repository
        var allRfqs = new List<Domain.Entities.RFQ>();

        if (request.TransportCompanyId.HasValue)
        {
            var companyRfqs = await _rfqRepository.GetByTransportCompanyIdAsync(request.TransportCompanyId.Value, cancellationToken);
            allRfqs.AddRange(companyRfqs);
        }
        else
        {
            // Get all published and expired RFQs
            var publishedRfqs = await _rfqRepository.GetRfqsByStatusAsync(RfqStatus.Published, cancellationToken);
            var expiredRfqs = await _rfqRepository.GetRfqsByStatusAsync(RfqStatus.Expired, cancellationToken);
            allRfqs.AddRange(publishedRfqs);
            allRfqs.AddRange(expiredRfqs);
        }

        // Apply date filters if specified
        if (request.FromDate.HasValue)
        {
            allRfqs = allRfqs.Where(r => r.CreatedAt >= request.FromDate.Value).ToList();
        }

        if (request.ToDate.HasValue)
        {
            allRfqs = allRfqs.Where(r => r.CreatedAt <= request.ToDate.Value).ToList();
        }

        return allRfqs;
    }

    private async Task<List<RfqExpirationTrendDto>> CalculateExpirationTrendAsync(GetRfqExpirationStatsQuery request, CancellationToken cancellationToken)
    {
        var trend = new List<RfqExpirationTrendDto>();
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-29); // Last 30 days

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var dayStart = date;
            var dayEnd = date.AddDays(1);

            // Get RFQs for this day
            var dayQuery = new GetRfqExpirationStatsQuery
            {
                TransportCompanyId = request.TransportCompanyId,
                FromDate = dayStart,
                ToDate = dayEnd
            };

            var dayRfqs = await GetFilteredRfqsAsync(dayQuery, cancellationToken);

            trend.Add(new RfqExpirationTrendDto
            {
                Date = date,
                ExpiredCount = dayRfqs.Count(r => r.Status == RfqStatus.Expired && r.ClosedAt?.Date == date),
                ExtendedCount = dayRfqs.Count(r => r.TimeframeExtensions.Any(e => e.ExtendedAt.Date == date)),
                CreatedCount = dayRfqs.Count(r => r.CreatedAt.Date == date)
            });
        }

        return trend;
    }
}
