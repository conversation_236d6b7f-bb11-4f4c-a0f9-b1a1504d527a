using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces;

public interface IFeatureFlagRepository
{
    Task<FeatureFlag?> GetByIdAsync(Guid id);
    Task<FeatureFlag?> GetByKeyAsync(string key);
    Task<List<FeatureFlag>> GetAllAsync();
    Task<List<FeatureFlag>> GetActiveAsync();
    Task<List<FeatureFlag>> GetByTypeAsync(FeatureFlagType type);
    Task<List<FeatureFlag>> GetByStatusAsync(FeatureFlagStatus status);
    Task<List<FeatureFlag>> GetForUserAsync(Guid userId, Dictionary<string, object>? context = null);
    Task<FeatureFlag> AddAsync(FeatureFlag featureFlag);
    Task UpdateAsync(FeatureFlag featureFlag);
    Task DeleteAsync(Guid id);
    Task<List<FeatureFlagUsage>> GetUsageHistoryAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, int>> GetUsageStatsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null);
    Task<List<FeatureFlag>> SearchAsync(string searchTerm);
}
