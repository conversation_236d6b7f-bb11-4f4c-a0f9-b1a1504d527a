using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Commands.ForecastVehicleAvailability;

public record ForecastVehicleAvailabilityCommand : IRequest<VehicleAvailabilityForecast>
{
    public Guid? VehicleId { get; init; } // If null, forecast for all vehicles
    public Guid? CarrierId { get; init; } // If specified, filter by carrier
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public bool IncludeMaintenance { get; init; } = true;
    public bool IncludeTrips { get; init; } = true;
    public bool IncludeDocumentExpiry { get; init; } = true;
}

public record VehicleAvailabilityForecast
{
    public DateTime ForecastDate { get; init; }
    public List<VehicleAvailabilityDto> VehicleAvailabilities { get; init; } = new();
    public AvailabilitySummary Summary { get; init; } = null!;
}

public record VehicleAvailabilityDto
{
    public Guid VehicleId { get; init; }
    public string RegistrationNumber { get; init; } = string.Empty;
    public string DisplayName { get; init; } = string.Empty;
    public List<AvailabilityPeriod> AvailabilityPeriods { get; init; } = new();
    public List<UnavailabilityPeriod> UnavailabilityPeriods { get; init; } = new();
    public double UtilizationPercentage { get; init; }
    public bool IsCurrentlyAvailable { get; init; }
    public DateTime? NextAvailableDate { get; init; }
}

public record AvailabilityPeriod
{
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public AvailabilityType Type { get; init; }
}

public record UnavailabilityPeriod
{
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public UnavailabilityReason Reason { get; init; }
    public string Description { get; init; } = string.Empty;
    public Guid? RelatedEntityId { get; init; } // Trip ID, Maintenance ID, etc.
}

public record AvailabilitySummary
{
    public int TotalVehicles { get; init; }
    public int AvailableVehicles { get; init; }
    public int UnavailableVehicles { get; init; }
    public int VehiclesInMaintenance { get; init; }
    public int VehiclesOnTrips { get; init; }
    public int VehiclesWithExpiredDocuments { get; init; }
    public double AverageUtilization { get; init; }
}

public enum AvailabilityType
{
    Available = 0,
    PartiallyAvailable = 1,
    Reserved = 2
}

public enum UnavailabilityReason
{
    OnTrip = 0,
    Maintenance = 1,
    ExpiredDocuments = 2,
    OutOfService = 3,
    Retired = 4,
    Other = 5
}
