using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.DTOs;

// Broker Analytics DTOs
public class BrokerQuoteAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }
    public int TotalQuotesSubmitted { get; set; }
    public int QuotesAccepted { get; set; }
    public int QuotesRejected { get; set; }
    public int QuotesPending { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal RejectionRate { get; set; }
    public MoneyDto TotalQuotedValue { get; set; } = new();
    public MoneyDto AcceptedQuotesValue { get; set; } = new();
    public MoneyDto AverageQuoteValue { get; set; } = new();
    public MoneyDto AverageAcceptedQuoteValue { get; set; } = new();
    public List<QuoteAnalyticsByCarrierDto> AnalyticsByCarrier { get; set; } = new();
    public List<QuoteAnalyticsByDateDto> DailyAnalytics { get; set; } = new();
    public List<QuoteAnalyticsByLoadTypeDto> AnalyticsByLoadType { get; set; } = new();
    public List<QuoteAnalyticsByRouteDto> AnalyticsByRoute { get; set; } = new();
    public TimeSpan AverageResponseTime { get; set; }
    public decimal ConversionRate { get; set; }
    public List<TopPerformingRouteDto> TopPerformingRoutes { get; set; } = new();
}

public class QuoteAnalyticsByCarrierDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int TotalQuotes { get; set; }
    public int AcceptedQuotes { get; set; }
    public decimal AcceptanceRate { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public MoneyDto AverageQuoteValue { get; set; } = new();
    public TimeSpan AverageResponseTime { get; set; }
}

public class QuoteAnalyticsByDateDto
{
    public DateTime Date { get; set; }
    public int QuotesSubmitted { get; set; }
    public int QuotesAccepted { get; set; }
    public decimal AcceptanceRate { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
}

public class QuoteAnalyticsByLoadTypeDto
{
    public LoadType LoadType { get; set; }
    public int TotalQuotes { get; set; }
    public int AcceptedQuotes { get; set; }
    public decimal AcceptanceRate { get; set; }
    public MoneyDto AverageQuoteValue { get; set; } = new();
}

public class QuoteAnalyticsByRouteDto
{
    public string Route { get; set; } = string.Empty;
    public string PickupCity { get; set; } = string.Empty;
    public string PickupState { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
    public string DeliveryState { get; set; } = string.Empty;
    public int TotalQuotes { get; set; }
    public int AcceptedQuotes { get; set; }
    public decimal AcceptanceRate { get; set; }
    public MoneyDto AverageQuoteValue { get; set; } = new();
}

public class TopPerformingRouteDto
{
    public string Route { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public MoneyDto TotalRevenue { get; set; } = new();
    public decimal AverageMargin { get; set; }
    public decimal ProfitabilityScore { get; set; }
}

// Transporter/Carrier Analytics DTOs
public class TransporterLoadPostingAnalyticsDto
{
    public Guid TransporterId { get; set; }
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }
    public int TotalLoadPostings { get; set; }
    public int OpenPostings { get; set; }
    public int QuotedPostings { get; set; }
    public int ExpiredPostings { get; set; }
    public int ConvertedPostings { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal QuoteRate { get; set; }
    public TimeSpan AverageTimeToFirstQuote { get; set; }
    public TimeSpan AverageTimeToConversion { get; set; }
    public List<LoadPostingByStatusDto> PostingsByStatus { get; set; } = new();
    public List<LoadPostingByBrokerDto> PostingsByBroker { get; set; } = new();
    public List<LoadPostingByDateDto> DailyPostings { get; set; } = new();
    public List<LoadPostingByRouteDto> PostingsByRoute { get; set; } = new();
    public MoneyDto TotalPostedValue { get; set; } = new();
    public MoneyDto ConvertedValue { get; set; } = new();
    public MoneyDto AveragePostingValue { get; set; } = new();
}

public class LoadPostingByStatusDto
{
    public string Status { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
}

public class LoadPostingByBrokerDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public int TotalAssignments { get; set; }
    public int ConvertedAssignments { get; set; }
    public decimal ConversionRate { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public TimeSpan AverageResponseTime { get; set; }
    public decimal PerformanceRating { get; set; }
}

public class LoadPostingByDateDto
{
    public DateTime Date { get; set; }
    public int PostingsCreated { get; set; }
    public int PostingsConverted { get; set; }
    public decimal ConversionRate { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
}

public class LoadPostingByRouteDto
{
    public string Route { get; set; } = string.Empty;
    public string PickupCity { get; set; } = string.Empty;
    public string PickupState { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
    public string DeliveryState { get; set; } = string.Empty;
    public int TotalPostings { get; set; }
    public int ConvertedPostings { get; set; }
    public decimal ConversionRate { get; set; }
    public MoneyDto AverageValue { get; set; } = new();
    public TimeSpan AverageConversionTime { get; set; }
}

// Shipper Analytics DTOs
public class ShipperOrderLifecycleAnalyticsDto
{
    public Guid ShipperId { get; set; }
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int CancelledOrders { get; set; }
    public int InProgressOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal CancellationRate { get; set; }
    public TimeSpan AverageOrderDuration { get; set; }
    public TimeSpan AverageDeliveryTime { get; set; }
    public MoneyDto TotalOrderValue { get; set; } = new();
    public MoneyDto AverageOrderValue { get; set; } = new();
    public List<OrderLifecycleByStatusDto> OrdersByStatus { get; set; } = new();
    public List<OrderLifecycleByProviderDto> OrdersByServiceProvider { get; set; } = new();
    public List<OrderLifecycleByDateDto> DailyOrders { get; set; } = new();
    public List<OrderLifecycleByRouteDto> OrdersByRoute { get; set; } = new();
    public List<ServiceProviderPerformanceDto> ServiceProviderPerformance { get; set; } = new();
}

public class OrderLifecycleByStatusDto
{
    public OrderStatus Status { get; set; }
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public TimeSpan AverageTimeInStatus { get; set; }
}

public class OrderLifecycleByProviderDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderType { get; set; } = string.Empty; // Broker, Carrier
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public TimeSpan AverageDeliveryTime { get; set; }
    public decimal PerformanceRating { get; set; }
}

public class OrderLifecycleByDateDto
{
    public DateTime Date { get; set; }
    public int OrdersCreated { get; set; }
    public int OrdersCompleted { get; set; }
    public int OrdersCancelled { get; set; }
    public MoneyDto TotalValue { get; set; } = new();
    public decimal CompletionRate { get; set; }
}

public class OrderLifecycleByRouteDto
{
    public string Route { get; set; } = string.Empty;
    public string PickupCity { get; set; } = string.Empty;
    public string PickupState { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
    public string DeliveryState { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public MoneyDto AverageOrderValue { get; set; } = new();
    public TimeSpan AverageDeliveryTime { get; set; }
}

public class ServiceProviderPerformanceDto
{
    public Guid ProviderId { get; set; }
    public string ProviderName { get; set; } = string.Empty;
    public string ProviderType { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public int OnTimeDeliveries { get; set; }
    public decimal OnTimePercentage { get; set; }
    public decimal AverageRating { get; set; }
    public MoneyDto TotalSpent { get; set; } = new();
    public MoneyDto AverageOrderValue { get; set; } = new();
    public TimeSpan AverageDeliveryTime { get; set; }
    public int TotalIssues { get; set; }
    public decimal IssueRate { get; set; }
    public DateTime LastOrderDate { get; set; }
    public bool IsPreferred { get; set; }
}

// Common Analytics DTOs
public class AnalyticsPeriodDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string PeriodType { get; set; } = string.Empty; // Daily, Weekly, Monthly, Quarterly, Yearly
    public string DisplayName { get; set; } = string.Empty;
}

public class PerformanceMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // Up, Down, Stable
    public string Unit { get; set; } = string.Empty;
    public string DisplayValue { get; set; } = string.Empty;
}
