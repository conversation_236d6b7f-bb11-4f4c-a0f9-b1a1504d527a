using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class TaxConfigurationConfiguration : IEntityTypeConfiguration<TaxConfiguration>
{
    public void Configure(EntityTypeBuilder<TaxConfiguration> builder)
    {
        builder.ToTable("TaxConfigurations");

        builder.HasKey(tc => tc.Id);

        builder.Property(tc => tc.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tc => tc.Description)
            .IsRequired()
            .HasMaxLength(1000);

        // Configure TaxJurisdiction value object
        builder.OwnsOne(tc => tc.Jurisdiction, jurisdiction =>
        {
            jurisdiction.Property(j => j.Country)
                .HasColumnName("JurisdictionCountry")
                .IsRequired()
                .HasMaxLength(100);
            jurisdiction.Property(j => j.State)
                .HasColumnName("JurisdictionState")
                .HasMaxLength(100);
            jurisdiction.Property(j => j.City)
                .HasColumnName("JurisdictionCity")
                .HasMaxLength(100);
            jurisdiction.Property(j => j.Type)
                .HasColumnName("JurisdictionType")
                .HasConversion<int>();
        });

        builder.Property(tc => tc.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tc => tc.EffectiveFrom)
            .IsRequired();

        builder.Property(tc => tc.EffectiveTo);

        builder.Property(tc => tc.IsDefault)
            .IsRequired();

        builder.Property(tc => tc.Priority)
            .IsRequired();

        builder.Property(tc => tc.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tc => tc.ModifiedBy)
            .HasMaxLength(100);

        builder.Property(tc => tc.ModifiedAt);

        // Tax settings
        builder.Property(tc => tc.EnableGstCalculation)
            .IsRequired();

        builder.Property(tc => tc.EnableTdsCalculation)
            .IsRequired();

        builder.Property(tc => tc.EnableReverseCharge)
            .IsRequired();

        builder.Property(tc => tc.RequireHsnCode)
            .IsRequired();

        builder.Property(tc => tc.DefaultGstRate)
            .HasPrecision(5, 4);

        builder.Property(tc => tc.DefaultTdsRate)
            .HasPrecision(5, 4);

        builder.Property(tc => tc.DefaultCurrency)
            .IsRequired()
            .HasMaxLength(3);

        // Configure relationships
        builder.HasMany(tc => tc.History)
            .WithOne()
            .HasForeignKey(h => h.TaxConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(tc => tc.Rules)
            .WithOne()
            .HasForeignKey(r => r.TaxConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(tc => tc.Name)
            .IsUnique();
        builder.HasIndex(tc => tc.Status);
        builder.HasIndex(tc => new { tc.EffectiveFrom, tc.EffectiveTo });
        builder.HasIndex(tc => tc.IsDefault);
        builder.HasIndex(tc => tc.Priority);
        builder.HasIndex(tc => new { tc.Status, tc.IsDefault });
    }
}

public class TaxConfigurationHistoryConfiguration : IEntityTypeConfiguration<TaxConfigurationHistory>
{
    public void Configure(EntityTypeBuilder<TaxConfigurationHistory> builder)
    {
        builder.ToTable("TaxConfigurationHistories");

        builder.HasKey(tch => tch.Id);

        builder.Property(tch => tch.TaxConfigurationId)
            .IsRequired();

        builder.Property(tch => tch.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tch => tch.Details)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(tch => tch.ModifiedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tch => tch.ModifiedAt)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(tch => tch.TaxConfigurationId);
        builder.HasIndex(tch => tch.ModifiedAt);
        builder.HasIndex(tch => tch.ModifiedBy);
    }
}

public class TaxConfigurationRuleConfiguration : IEntityTypeConfiguration<TaxConfigurationRule>
{
    public void Configure(EntityTypeBuilder<TaxConfigurationRule> builder)
    {
        builder.ToTable("TaxConfigurationRules");

        builder.HasKey(tcr => tcr.Id);

        builder.Property(tcr => tcr.TaxConfigurationId)
            .IsRequired();

        builder.Property(tcr => tcr.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tcr => tcr.Condition)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(tcr => tcr.Action)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(tcr => tcr.IsActive)
            .IsRequired();

        builder.Property(tcr => tcr.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        // Configure indexes
        builder.HasIndex(tcr => tcr.TaxConfigurationId);
        builder.HasIndex(tcr => tcr.IsActive);
        builder.HasIndex(tcr => tcr.Name);
    }
}
