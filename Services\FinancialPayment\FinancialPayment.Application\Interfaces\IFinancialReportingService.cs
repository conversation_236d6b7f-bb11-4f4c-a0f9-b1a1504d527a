using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IFinancialReportingService
{
    Task<FinancialReport> GenerateReportAsync(GenerateFinancialReportRequest request);
    Task<List<FinancialReport>> GetReportsAsync(Guid? userId = null, string? reportType = null);
    Task<FinancialReport?> GetReportAsync(Guid reportId);
    Task<FinancialMetrics> GetFinancialMetricsAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<RevenueBreakdown> GetRevenueBreakdownAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<ExpenseBreakdown> GetExpenseBreakdownAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<ProfitLossStatement> GenerateProfitLossStatementAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<CashFlowStatement> GenerateCashFlowStatementAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<ReportTemplate> CreateTemplateAsync(CreateReportTemplateRequest request);
    Task<List<ReportTemplate>> GetTemplatesAsync(string? reportType = null);
    Task<ReportTemplate?> GetTemplateAsync(Guid templateId);
    Task<byte[]> ExportReportAsync(Guid reportId, string format);
    Task<List<FinancialTrend>> GetFinancialTrendsAsync(DateTime fromDate, DateTime toDate, string metricType, FinancialReportFilter? filter = null);
}

public interface IFinancialReportRepository
{
    Task<FinancialReport?> GetByIdAsync(Guid id);
    Task<List<FinancialReport>> GetByUserAsync(Guid userId);
    Task<List<FinancialReport>> GetByTypeAsync(string reportType);
    Task<List<FinancialReport>> GetByStatusAsync(FinancialReportStatus status);
    Task<List<FinancialReport>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
    Task AddAsync(FinancialReport report);
    Task UpdateAsync(FinancialReport report);
    Task DeleteAsync(Guid id);
}

public interface IReportTemplateRepository
{
    Task<ReportTemplate?> GetByIdAsync(Guid id);
    Task<List<ReportTemplate>> GetByTypeAsync(string reportType);
    Task<List<ReportTemplate>> GetActiveTemplatesAsync();
    Task<ReportTemplate?> GetDefaultTemplateAsync(string reportType);
    Task AddAsync(ReportTemplate template);
    Task UpdateAsync(ReportTemplate template);
    Task DeleteAsync(Guid id);
}

public interface IFinancialDataAggregator
{
    Task<FinancialMetrics> AggregateFinancialMetricsAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<RevenueBreakdown> AggregateRevenueDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<ExpenseBreakdown> AggregateExpenseDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<List<CashFlowItem>> AggregateCashFlowDataAsync(DateTime fromDate, DateTime toDate, FinancialReportFilter? filter = null);
    Task<List<FinancialTrend>> AggregateTrendDataAsync(DateTime fromDate, DateTime toDate, string metricType, FinancialReportFilter? filter = null);
}

public interface IFinancialReportGenerator
{
    Task<string> GenerateExcelReportAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses);
    Task<string> GeneratePdfReportAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses);
    Task<string> GenerateCsvReportAsync(FinancialReport report, List<FinancialTrend> trends);
    Task<byte[]> GenerateExcelBytesAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses);
    Task<byte[]> GeneratePdfBytesAsync(FinancialReport report, FinancialMetrics metrics, RevenueBreakdown revenue, ExpenseBreakdown expenses);
}

// Request/Response DTOs
public class GenerateFinancialReportRequest
{
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public Guid? GeneratedBy { get; set; }
    public string Format { get; set; } = "excel"; // excel, pdf, csv
    public Guid? TemplateId { get; set; }
    public FinancialReportFilter? Filter { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class CreateReportTemplateRequest
{
    public string TemplateName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public Guid? CreatedBy { get; set; }
    public bool IsDefault { get; set; } = false;
    public List<CreateTemplateSectionRequest> Sections { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class CreateTemplateSectionRequest
{
    public string SectionName { get; set; } = string.Empty;
    public string SectionType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int Order { get; set; } = 0;
    public bool IsRequired { get; set; } = false;
}

public class FinancialReportFilter
{
    public List<string> PaymentGateways { get; set; } = new();
    public List<string> PaymentMethods { get; set; } = new();
    public List<string> Currencies { get; set; } = new();
    public List<Guid> UserIds { get; set; } = new();
    public List<string> Regions { get; set; } = new();
    public List<string> CustomerSegments { get; set; } = new();
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
    public List<string> TransactionTypes { get; set; } = new();
    public List<string> TransactionStatuses { get; set; } = new();
    public bool IncludeRefunds { get; set; } = true;
    public bool IncludeChargebacks { get; set; } = true;
    public bool IncludeCommissions { get; set; } = true;
    public bool IncludeTaxes { get; set; } = true;
}

public class FinancialTrend
{
    public DateTime Date { get; set; }
    public string MetricType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class FinancialReportConfiguration
{
    public bool EnableScheduledReports { get; set; } = true;
    public int MaxReportsPerUser { get; set; } = 100;
    public int ReportRetentionDays { get; set; } = 365;
    public List<string> SupportedFormats { get; set; } = new() { "excel", "pdf", "csv" };
    public List<string> SupportedReportTypes { get; set; } = new() { "financial_summary", "profit_loss", "cash_flow", "revenue_analysis", "expense_analysis" };
    public string ReportsStoragePath { get; set; } = "financial_reports";
    public Dictionary<string, string> DefaultTemplates { get; set; } = new();
    public bool EnableRealTimeReporting { get; set; } = true;
    public int MaxConcurrentReportGeneration { get; set; } = 5;
}

public class ReportSchedule
{
    public Guid Id { get; set; }
    public string ScheduleName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public Guid? TemplateId { get; set; }
    public ReportFrequency Frequency { get; set; }
    public DateTime NextRunDate { get; set; }
    public DateTime? LastRunDate { get; set; }
    public bool IsActive { get; set; }
    public List<string> Recipients { get; set; } = new();
    public FinancialReportFilter? Filter { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class FinancialKPI
{
    public string Name { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal? PreviousValue { get; set; }
    public decimal? ChangePercentage { get; set; }
    public string Trend { get; set; } = "stable"; // up, down, stable
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class FinancialDashboardData
{
    public List<FinancialKPI> KPIs { get; set; } = new();
    public FinancialMetrics Metrics { get; set; } = new();
    public RevenueBreakdown Revenue { get; set; } = new();
    public ExpenseBreakdown Expenses { get; set; } = new();
    public List<FinancialTrend> Trends { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}
