using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class MilestoneTemplateConfiguration : IEntityTypeConfiguration<MilestoneTemplate>
{
    public void Configure(EntityTypeBuilder<MilestoneTemplate> builder)
    {
        builder.ToTable("milestone_templates");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.Type)
            .HasColumnName("type")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Category)
            .HasColumnName("category")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.IsDefault)
            .HasColumnName("is_default")
            .IsRequired();

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(200);

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at");

        builder.Property(e => e.UsageCount)
            .HasColumnName("usage_count")
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Navigation properties
        builder.HasMany(e => e.Steps)
            .WithOne(s => s.MilestoneTemplate)
            .HasForeignKey(s => s.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.RoleMappings)
            .WithOne(rm => rm.MilestoneTemplate)
            .HasForeignKey(rm => rm.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public class MilestoneStepConfiguration : IEntityTypeConfiguration<MilestoneStep>
{
    public void Configure(EntityTypeBuilder<MilestoneStep> builder)
    {
        builder.ToTable("milestone_steps");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.MilestoneTemplateId)
            .HasColumnName("milestone_template_id")
            .IsRequired();

        builder.Property(e => e.Name)
            .HasColumnName("name")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.SequenceNumber)
            .HasColumnName("sequence_number")
            .IsRequired();

        builder.Property(e => e.IsRequired)
            .HasColumnName("is_required")
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.TriggerCondition)
            .HasColumnName("trigger_condition")
            .HasMaxLength(500);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.MilestoneTemplate)
            .WithMany(mt => mt.Steps)
            .HasForeignKey(e => e.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.PayoutRules)
            .WithOne(pr => pr.MilestoneStep)
            .HasForeignKey(pr => pr.MilestoneStepId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public class MilestonePayoutRuleConfiguration : IEntityTypeConfiguration<MilestonePayoutRule>
{
    public void Configure(EntityTypeBuilder<MilestonePayoutRule> builder)
    {
        builder.ToTable("milestone_payout_rules");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.MilestoneStepId)
            .HasColumnName("milestone_step_id")
            .IsRequired();

        builder.Property(e => e.PayoutPercentage)
            .HasColumnName("payout_percentage")
            .HasColumnType("decimal(5,2)")
            .IsRequired();

        builder.Property(e => e.TriggerCondition)
            .HasColumnName("trigger_condition")
            .HasMaxLength(500);

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("description")
            .HasMaxLength(1000);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.MilestoneStep)
            .WithMany(ms => ms.PayoutRules)
            .HasForeignKey(e => e.MilestoneStepId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public class RoleTemplateMappingsConfiguration : IEntityTypeConfiguration<RoleTemplateMappings>
{
    public void Configure(EntityTypeBuilder<RoleTemplateMappings> builder)
    {
        builder.ToTable("role_template_mappings");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.RoleName)
            .HasColumnName("role_name")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.MilestoneTemplateId)
            .HasColumnName("milestone_template_id")
            .IsRequired();

        builder.Property(e => e.IsDefault)
            .HasColumnName("is_default")
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.Priority)
            .HasColumnName("priority")
            .IsRequired();

        builder.Property(e => e.Conditions)
            .HasColumnName("conditions")
            .HasMaxLength(2000);

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.UpdatedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(200);

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at");

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.MilestoneTemplate)
            .WithMany(mt => mt.RoleMappings)
            .HasForeignKey(e => e.MilestoneTemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
