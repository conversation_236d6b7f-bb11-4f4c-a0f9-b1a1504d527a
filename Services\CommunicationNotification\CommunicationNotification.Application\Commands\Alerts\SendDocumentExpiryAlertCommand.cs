using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Commands.Alerts;

/// <summary>
/// Command to send document expiry alert notifications
/// </summary>
public class SendDocumentExpiryAlertCommand : CommandBase<SendDocumentExpiryAlertResult>
{
    public Guid CarrierId { get; set; }
    public string DocumentType { get; set; } = string.Empty; // Insurance, Fitness, Permit, License
    public string EntityType { get; set; } = string.Empty; // Vehicle, Driver, Carrier
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty; // Vehicle registration, driver name, etc.
    public DateTime ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public string AlertType { get; set; } = string.Empty; // Warning, Critical, Expired
    public int ThresholdDays { get; set; } // 30, 15, 7, 0 (expired)
    public Dictionary<string, object> DocumentDetails { get; set; } = new();
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to send bulk document expiry alerts for multiple documents
/// </summary>
public class SendBulkDocumentExpiryAlertsCommand : CommandBase<SendBulkDocumentExpiryAlertsResult>
{
    public Guid CarrierId { get; set; }
    public List<DocumentExpiryAlertInfo> ExpiringDocuments { get; set; } = new();
    public int ThresholdDays { get; set; } // 30, 15, 7, 0
    public string AlertType { get; set; } = string.Empty; // Warning, Critical, Expired
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public bool GroupByEntityType { get; set; } = true; // Group documents by vehicle/driver
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Command to schedule document expiry alert monitoring
/// </summary>
public class ScheduleDocumentExpiryMonitoringCommand : CommandBase<ScheduleDocumentExpiryMonitoringResult>
{
    public Guid CarrierId { get; set; }
    public List<int> AlertThresholds { get; set; } = new() { 30, 15, 7, 0 }; // Days before expiry
    public List<string> DocumentTypes { get; set; } = new(); // Insurance, Fitness, Permit, License
    public List<string> EntityTypes { get; set; } = new(); // Vehicle, Driver, Carrier
    public string CronExpression { get; set; } = "0 9 * * *"; // Daily at 9 AM
    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public Dictionary<string, object> MonitoringSettings { get; set; } = new();
}

/// <summary>
/// Information about a document that is expiring
/// </summary>
public class DocumentExpiryAlertInfo
{
    public string DocumentType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public Dictionary<string, object> DocumentDetails { get; set; } = new();
}

/// <summary>
/// Result of sending document expiry alert
/// </summary>
public class SendDocumentExpiryAlertResult
{
    public Guid AlertId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<NotificationChannel> ChannelsUsed { get; set; } = new();
    public DateTime SentAt { get; set; }
    public string? ExternalId { get; set; }
    public Dictionary<string, object> DeliveryMetadata { get; set; } = new();
    public bool RequiresAcknowledgment { get; set; }
    public DateTime? AcknowledgmentDeadline { get; set; }
}

/// <summary>
/// Result of sending bulk document expiry alerts
/// </summary>
public class SendBulkDocumentExpiryAlertsResult
{
    public List<SendDocumentExpiryAlertResult> AlertResults { get; set; } = new();
    public int TotalAlerts { get; set; }
    public int SuccessfulAlerts { get; set; }
    public int FailedAlerts { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
}

/// <summary>
/// Result of scheduling document expiry monitoring
/// </summary>
public class ScheduleDocumentExpiryMonitoringResult
{
    public Guid MonitoringJobId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ScheduledAt { get; set; }
    public DateTime? NextRunTime { get; set; }
    public string CronExpression { get; set; } = string.Empty;
    public Dictionary<string, object> JobMetadata { get; set; } = new();
}

/// <summary>
/// Command to configure compliance reminders for Transport Company Portal
/// </summary>
public class ConfigureTransportCompanyComplianceRemindersCommand : CommandBase<ConfigureTransportCompanyComplianceRemindersResult>
{
    public Guid TransportCompanyId { get; set; }
    public List<ComplianceReminderConfiguration> ReminderConfigurations { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public TimeSpan PreferredDeliveryTime { get; set; } = new(9, 0, 0); // 9:00 AM
    public bool GroupSimilarReminders { get; set; } = true;
    public int MaxRemindersPerDay { get; set; } = 5;
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Configuration for a specific type of compliance reminder
/// </summary>
public class ComplianceReminderConfiguration
{
    public string DocumentType { get; set; } = string.Empty; // GST, PAN, Insurance, License, etc.
    public string EntityType { get; set; } = string.Empty; // Company, Vehicle, Driver
    public List<int> ReminderDays { get; set; } = new() { 30, 15, 7, 3, 1 }; // Days before expiry
    public bool IsEnabled { get; set; } = true;
    public Priority Priority { get; set; } = Priority.Normal;
    public bool RequireAcknowledgment { get; set; } = false;
    public string? CustomMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Result of configuring transport company compliance reminders
/// </summary>
public class ConfigureTransportCompanyComplianceRemindersResult
{
    public Guid ConfigurationId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<Guid> ScheduledJobIds { get; set; } = new();
    public DateTime ConfiguredAt { get; set; }
    public DateTime? NextReminderCheck { get; set; }
    public int TotalRemindersConfigured { get; set; }
    public Dictionary<string, object> ConfigurationSummary { get; set; } = new();
}
