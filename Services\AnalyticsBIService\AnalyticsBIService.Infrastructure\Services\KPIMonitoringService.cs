using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Diagnostics;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of enhanced KPI monitoring service
/// </summary>
public class KPIMonitoringService : IKPIMonitoringService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<KPIMonitoringService> _logger;

    public KPIMonitoringService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<KPIMonitoringService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<KPIMonitorDto> CreateKPIMonitorAsync(CreateKPIMonitorRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating KPI monitor '{Name}' for user {UserId}", request.Name, request.UserId);

            var monitor = new KPIMonitor(
                request.Name,
                request.Description,
                request.KPIType,
                request.DataSource,
                request.Query,
                request.AggregationMethod,
                request.MonitoringIntervalMinutes,
                JsonSerializer.Serialize(request.Configuration),
                request.UserId,
                request.UserType);

            _context.KPIMonitors.Add(monitor);
            await _context.SaveChangesAsync(cancellationToken);

            var result = await MapToDtoAsync(monitor, cancellationToken);

            // Cache the monitor
            var cacheKey = $"kpi:monitor:{monitor.Id}";
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("KPI monitor created with ID {MonitorId}", monitor.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating KPI monitor");
            throw;
        }
    }

    public async Task<KPIMonitorDto?> GetKPIMonitorAsync(Guid monitorId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"kpi:monitor:{monitorId}";
            var cachedResult = await _cacheService.GetAsync<KPIMonitorDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var monitor = await _context.KPIMonitors
                .FirstOrDefaultAsync(m => m.Id == monitorId, cancellationToken);

            if (monitor == null)
                return null;

            var result = await MapToDtoAsync(monitor, cancellationToken);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting KPI monitor {MonitorId}", monitorId);
            return null;
        }
    }

    public async Task<List<KPIMonitorDto>> GetUserKPIMonitorsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var monitors = await _context.KPIMonitors
                .Where(m => m.CreatedBy == userId)
                .OrderByDescending(m => m.UpdatedAt)
                .ToListAsync(cancellationToken);

            var results = new List<KPIMonitorDto>();
            foreach (var monitor in monitors)
            {
                results.Add(await MapToDtoAsync(monitor, cancellationToken));
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user KPI monitors for user {UserId}", userId);
            return new List<KPIMonitorDto>();
        }
    }

    public async Task<bool> UpdateKPIMonitorAsync(Guid monitorId, UpdateKPIMonitorRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var monitor = await _context.KPIMonitors
                .FirstOrDefaultAsync(m => m.Id == monitorId, cancellationToken);

            if (monitor == null)
                return false;

            if (!string.IsNullOrEmpty(request.Name))
                monitor.UpdateName(request.Name);

            if (!string.IsNullOrEmpty(request.Description))
                monitor.UpdateDescription(request.Description);

            if (!string.IsNullOrEmpty(request.Query))
                monitor.UpdateQuery(request.Query);

            if (!string.IsNullOrEmpty(request.AggregationMethod))
                monitor.UpdateAggregationMethod(request.AggregationMethod);

            if (request.MonitoringIntervalMinutes.HasValue)
                monitor.UpdateMonitoringInterval(request.MonitoringIntervalMinutes.Value);

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    monitor.Activate();
                else
                    monitor.Deactivate();
            }

            if (request.Configuration != null)
                monitor.UpdateConfiguration(JsonSerializer.Serialize(request.Configuration));

            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"kpi:monitor:{monitorId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating KPI monitor");
            return false;
        }
    }

    public async Task<bool> DeleteKPIMonitorAsync(Guid monitorId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var monitor = await _context.KPIMonitors
                .FirstOrDefaultAsync(m => m.Id == monitorId && m.CreatedBy == userId, cancellationToken);

            if (monitor == null)
                return false;

            _context.KPIMonitors.Remove(monitor);
            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"kpi:monitor:{monitorId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting KPI monitor");
            return false;
        }
    }

    public async Task<List<KPIValueDto>> GetCurrentKPIValuesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"kpi:current:{userId}";
            var cachedResult = await _cacheService.GetAsync<List<KPIValueDto>>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var monitors = await _context.KPIMonitors
                .Where(m => m.CreatedBy == userId && m.IsActive)
                .ToListAsync(cancellationToken);

            var kpiValues = new List<KPIValueDto>();

            foreach (var monitor in monitors)
            {
                var currentValue = await CalculateCurrentKPIValueAsync(monitor, cancellationToken);
                kpiValues.Add(currentValue);
            }

            await _cacheService.SetAsync(cacheKey, kpiValues, CacheExpiration.ShortTerm, cancellationToken);
            return kpiValues;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current KPI values");
            return new List<KPIValueDto>();
        }
    }

    public async Task<KPIHistoryDto> GetKPIHistoryAsync(Guid monitorId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var monitor = await GetKPIMonitorAsync(monitorId, cancellationToken);
            if (monitor == null)
                throw new InvalidOperationException($"KPI monitor {monitorId} not found");

            var dataPoints = await GenerateKPIHistoryDataAsync(monitorId, fromDate, toDate, cancellationToken);
            var statistics = CalculateKPIStatistics(dataPoints);

            return new KPIHistoryDto
            {
                MonitorId = monitorId,
                KPIName = monitor.Name,
                FromDate = fromDate,
                ToDate = toDate,
                DataPoints = dataPoints,
                Statistics = statistics
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting KPI history");
            throw;
        }
    }

    public async Task<ThresholdRuleDto> CreateThresholdRuleAsync(CreateThresholdRuleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating threshold rule '{Name}' for monitor {MonitorId}", request.Name, request.MonitorId);

            var rule = new AnalyticsBIService.Domain.Entities.ThresholdRule(
                name: request.Name,
                description: $"Threshold rule for monitor {request.MonitorId}",
                metricName: $"Monitor_{request.MonitorId}",
                @operator: request.Operator,
                thresholdValue: request.ThresholdValue,
                severity: Enum.Parse<AlertSeverity>(request.Severity),
                createdBy: request.UserId,
                userType: UserType.Admin);

            _context.ThresholdRules.Add(rule);
            await _context.SaveChangesAsync(cancellationToken);

            return new ThresholdRuleDto
            {
                Id = rule.Id,
                MonitorId = request.MonitorId, // Store MonitorId from request since Domain entity doesn't have it
                Name = rule.Name,
                RuleType = request.RuleType, // From request since Domain entity doesn't have RuleType
                Operator = rule.Operator,
                ThresholdValue = rule.ThresholdValue,
                Severity = rule.Severity.ToString(),
                IsEnabled = rule.IsEnabled,
                CreatedAt = rule.CreatedAt,
                UpdatedAt = rule.UpdatedAt ?? DateTime.UtcNow,
                Configuration = request.Configuration
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating threshold rule");
            throw;
        }
    }

    public async Task<List<ThresholdRuleDto>> GetThresholdRulesAsync(Guid monitorId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rules = await _context.ThresholdRules
                .Where(r => r.MetricName.Contains($"Monitor_{monitorId}"))
                .ToListAsync(cancellationToken);

            return rules.Select(r => new ThresholdRuleDto
            {
                Id = r.Id,
                MonitorId = monitorId, // Use the provided monitorId since Domain entity doesn't store it directly
                Name = r.Name,
                RuleType = "Threshold", // Default value since Domain entity doesn't have RuleType
                Operator = r.Operator,
                ThresholdValue = r.ThresholdValue,
                Severity = r.Severity.ToString(),
                IsEnabled = r.IsEnabled,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt ?? DateTime.UtcNow,
                Configuration = new Dictionary<string, object>() // Default empty configuration
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting threshold rules");
            return new List<ThresholdRuleDto>();
        }
    }

    public async Task<AlertConfigurationDto> CreateAlertConfigurationAsync(CreateAlertConfigurationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating alert configuration '{Name}' for user {UserId}", request.Name, request.UserId);

            var config = new AnalyticsBIService.Domain.Entities.AlertConfiguration(
                request.Name,
                request.Description,
                AlertType.System, // Default alert type
                JsonSerializer.Serialize(request.ChannelSettings),
                request.UserId,
                UserType.Admin, // Default user type
                new List<Guid>(), // Empty notification targets for now
                new Dictionary<string, object>
                {
                    { "AlertChannels", request.AlertChannels },
                    { "Recipients", request.Recipients },
                    { "MessageTemplate", request.MessageTemplate }
                });

            _context.AlertConfigurations.Add(config);
            await _context.SaveChangesAsync(cancellationToken);

            return new AlertConfigurationDto
            {
                Id = config.Id,
                UserId = config.CreatedBy,
                Name = config.Name,
                Description = config.Description,
                AlertChannels = request.AlertChannels,
                Recipients = request.Recipients,
                MessageTemplate = request.MessageTemplate,
                ChannelSettings = request.ChannelSettings,
                IsEnabled = config.IsEnabled,
                CreatedAt = config.CreatedAt,
                UpdatedAt = config.UpdatedAt ?? DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating alert configuration");
            throw;
        }
    }

    public async Task<List<AlertConfigurationDto>> GetAlertConfigurationsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var configs = await _context.AlertConfigurations
                .Where(ac => ac.CreatedBy == userId)
                .ToListAsync(cancellationToken);

            return configs.Select(c =>
            {
                var settings = c.Settings ?? new Dictionary<string, object>();
                return new AlertConfigurationDto
                {
                    Id = c.Id,
                    UserId = c.CreatedBy,
                    Name = c.Name,
                    Description = c.Description,
                    AlertChannels = settings.ContainsKey("AlertChannels") ?
                        JsonSerializer.Deserialize<List<string>>(settings["AlertChannels"].ToString() ?? "[]") ?? new List<string>() :
                        new List<string>(),
                    Recipients = settings.ContainsKey("Recipients") ?
                        JsonSerializer.Deserialize<List<string>>(settings["Recipients"].ToString() ?? "[]") ?? new List<string>() :
                        new List<string>(),
                    MessageTemplate = settings.ContainsKey("MessageTemplate") ? settings["MessageTemplate"].ToString() ?? string.Empty : string.Empty,
                    ChannelSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(c.Configuration) ?? new Dictionary<string, object>(),
                    IsEnabled = c.IsEnabled,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt ?? DateTime.UtcNow
                };
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert configurations");
            return new List<AlertConfigurationDto>();
        }
    }

    public async Task<List<AlertDto>> GetActiveAlertsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var alerts = await _context.Alerts
                .Join(_context.KPIMonitors, a => a.MonitorId, m => m.Id, (a, m) => new { Alert = a, Monitor = m })
                .Where(am => am.Monitor.CreatedBy == userId && am.Alert.IsActive)
                .Select(am => am.Alert)
                .OrderByDescending(a => a.TriggeredAt)
                .ToListAsync(cancellationToken);

            return alerts.Select(MapAlertToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active alerts");
            return new List<AlertDto>();
        }
    }

    public async Task<List<AlertHistoryDto>> GetAlertHistoryAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Alerts
                .Join(_context.KPIMonitors, a => a.MonitorId, m => m.Id, (a, m) => new { Alert = a, Monitor = m })
                .Where(am => am.Monitor.CreatedBy == userId);

            if (fromDate.HasValue)
                query = query.Where(am => am.Alert.TriggeredAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(am => am.Alert.TriggeredAt <= toDate.Value);

            var alerts = await query
                .Select(am => am.Alert)
                .OrderByDescending(a => a.TriggeredAt)
                .Take(100)
                .ToListAsync(cancellationToken);

            return alerts.Select(a => new AlertHistoryDto
            {
                AlertId = a.Id,
                KPIName = a.KPIName,
                AlertType = a.AlertType.ToString(),
                Severity = a.Severity.ToString(),
                Message = a.Message,
                TriggeredAt = a.TriggeredAt,
                ResolvedAt = a.AcknowledgedAt,
                Duration = a.AcknowledgedAt.HasValue ? a.AcknowledgedAt.Value - a.TriggeredAt : null,
                Status = a.Status.ToString()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting alert history");
            return new List<AlertHistoryDto>();
        }
    }

    public async Task<bool> AcknowledgeAlertAsync(Guid alertId, Guid userId, string? notes = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var alert = await _context.Alerts
                .FirstOrDefaultAsync(a => a.Id == alertId, cancellationToken);

            if (alert == null)
                return false;

            alert.Acknowledge(userId, notes);

            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error acknowledging alert");
            return false;
        }
    }

    public async Task ProcessKPIMonitoringAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing KPI monitoring");

            var activeMonitors = await _context.KPIMonitors
                .Where(m => m.IsActive)
                .ToListAsync(cancellationToken);

            foreach (var monitor in activeMonitors)
            {
                try
                {
                    await ProcessMonitorAsync(monitor, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing monitor {MonitorId}", monitor.Id);
                }
            }

            _logger.LogInformation("Processed {Count} KPI monitors", activeMonitors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing KPI monitoring");
        }
    }

    public async Task<AutomatedResponseDto> CreateAutomatedResponseAsync(CreateAutomatedResponseRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating automated response '{Name}' for threshold rule {ThresholdRuleId}", request.Name, request.ThresholdRuleId);

            var response = new AutomatedResponse(
                name: request.Name,
                description: $"Automated response for threshold rule {request.ThresholdRuleId}",
                triggerCondition: $"ThresholdRuleId:{request.ThresholdRuleId}",
                responseAction: request.Action,
                configuration: JsonSerializer.Serialize(new
                {
                    ResponseType = request.ResponseType,
                    Parameters = request.Parameters,
                    ExecutionOrder = request.ExecutionOrder
                }),
                createdBy: Guid.Empty, // TODO: Get from current user context
                userType: UserType.Admin);

            _context.AutomatedResponses.Add(response);
            await _context.SaveChangesAsync(cancellationToken);

            var configData = JsonSerializer.Deserialize<Dictionary<string, object>>(response.Configuration);
            return new AutomatedResponseDto
            {
                Id = response.Id,
                ThresholdRuleId = request.ThresholdRuleId, // From request since not stored directly
                Name = response.Name,
                ResponseType = configData?.ContainsKey("ResponseType") == true ? configData["ResponseType"].ToString() ?? string.Empty : string.Empty,
                Action = response.ResponseAction,
                Parameters = request.Parameters,
                ExecutionOrder = configData?.ContainsKey("ExecutionOrder") == true ? Convert.ToInt32(configData["ExecutionOrder"]) : 0,
                IsEnabled = response.IsEnabled,
                CreatedAt = response.CreatedAt,
                UpdatedAt = response.UpdatedAt ?? DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating automated response");
            throw;
        }
    }

    public async Task<List<AutomatedResponseDto>> GetAutomatedResponsesAsync(Guid thresholdRuleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var responses = await _context.AutomatedResponses
                .Where(ar => ar.TriggerCondition.Contains($"ThresholdRuleId:{thresholdRuleId}"))
                .ToListAsync(cancellationToken);

            return responses.Select(r =>
            {
                var configData = JsonSerializer.Deserialize<Dictionary<string, object>>(r.Configuration) ?? new Dictionary<string, object>();
                return new AutomatedResponseDto
                {
                    Id = r.Id,
                    ThresholdRuleId = thresholdRuleId,
                    Name = r.Name,
                    ResponseType = configData.ContainsKey("ResponseType") ? configData["ResponseType"].ToString() ?? string.Empty : string.Empty,
                    Action = r.ResponseAction,
                    Parameters = configData.ContainsKey("Parameters") ?
                        JsonSerializer.Deserialize<Dictionary<string, object>>(configData["Parameters"].ToString() ?? "{}") ?? new Dictionary<string, object>() :
                        new Dictionary<string, object>(),
                    ExecutionOrder = configData.ContainsKey("ExecutionOrder") ? Convert.ToInt32(configData["ExecutionOrder"]) : 0,
                    IsEnabled = r.IsEnabled,
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt ?? DateTime.UtcNow
                };
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting automated responses");
            return new List<AutomatedResponseDto>();
        }
    }

    public async Task<KPIDashboardDto> GetKPIDashboardAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var currentKPIs = await GetCurrentKPIValuesAsync(userId, cancellationToken);
            var activeAlerts = await GetActiveAlertsAsync(userId, cancellationToken);
            var trendingKPIs = await GetTrendingKPIsAsync(userId, cancellationToken);
            var overviewStats = await GetKPIOverviewStatsAsync(userId, cancellationToken);
            var performanceMetrics = await GetKPIPerformanceMetricsAsync(userId, cancellationToken);

            return new KPIDashboardDto
            {
                UserId = userId,
                GeneratedAt = DateTime.UtcNow,
                CurrentKPIs = currentKPIs,
                ActiveAlerts = activeAlerts,
                TrendingKPIs = trendingKPIs,
                OverviewStats = overviewStats,
                PerformanceMetrics = performanceMetrics
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting KPI dashboard");
            throw;
        }
    }

    public async Task<KPIMonitoringStatsDto> GetMonitoringStatisticsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var monitors = await _context.KPIMonitors
                .Where(m => m.CreatedBy == userId)
                .ToListAsync(cancellationToken);

            var monitorIds = monitors.Select(m => m.Id).ToList();
            var thresholdRules = await _context.ThresholdRules
                .Where(tr => tr.MonitorId.HasValue && monitorIds.Contains(tr.MonitorId.Value))
                .ToListAsync(cancellationToken);

            var alerts = await _context.Alerts
                .Where(a => a.MonitorId.HasValue && monitorIds.Contains(a.MonitorId.Value))
                .ToListAsync(cancellationToken);

            var today = DateTime.UtcNow.Date;
            var weekAgo = today.AddDays(-7);

            return new KPIMonitoringStatsDto
            {
                TotalMonitors = monitors.Count(),
                ActiveMonitors = monitors.Count(m => m.IsActive),
                InactiveMonitors = monitors.Count(m => !m.IsActive),
                TotalThresholdRules = thresholdRules.Count(),
                TotalAlerts = alerts.Count(),
                AlertsToday = alerts.Count(a => a.TriggeredAt >= today),
                AlertsThisWeek = alerts.Count(a => a.TriggeredAt >= weekAgo),
                AverageResponseTime = 2.5m, // Simulated
                KPITypeStatistics = GenerateKPITypeStats(monitors),
                AlertSeverityStats = GenerateAlertSeverityStats(alerts)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monitoring statistics");
            throw;
        }
    }

    // Helper methods
    private async Task<KPIMonitorDto> MapToDtoAsync(KPIMonitor monitor, CancellationToken cancellationToken)
    {
        var configuration = string.IsNullOrEmpty(monitor.Configuration)
            ? new Dictionary<string, object>()
            : JsonSerializer.Deserialize<Dictionary<string, object>>(monitor.Configuration) ?? new Dictionary<string, object>();

        var thresholdRules = await GetThresholdRulesAsync(monitor.Id, cancellationToken);

        return new KPIMonitorDto
        {
            Id = monitor.Id,
            Name = monitor.Name,
            Description = monitor.Description,
            KPIType = monitor.KPIType,
            DataSource = monitor.DataSource,
            Query = monitor.Query,
            AggregationMethod = monitor.AggregationMethod,
            MonitoringIntervalMinutes = monitor.MonitoringIntervalMinutes,
            IsActive = monitor.IsActive,
            CreatedAt = monitor.CreatedAt,
            UpdatedAt = monitor.UpdatedAt ?? DateTime.UtcNow,
            CreatedBy = monitor.CreatedBy,
            UserType = monitor.UserType,
            LastCalculated = monitor.LastCalculated,
            LastValue = monitor.LastValue,
            LastStatus = monitor.LastStatus,
            ThresholdRules = thresholdRules,
            Configuration = configuration
        };
    }

    private async Task<KPIValueDto> CalculateCurrentKPIValueAsync(KPIMonitor monitor, CancellationToken cancellationToken)
    {
        // Simulate KPI calculation
        var random = new Random();
        var currentValue = Math.Round((decimal)(random.NextDouble() * 1000), 2);
        var previousValue = monitor.LastValue ?? Math.Round((decimal)(random.NextDouble() * 1000), 2);

        var changePercentage = previousValue != 0 ? Math.Round((currentValue - previousValue) / previousValue * 100, 2) : 0;
        var trend = changePercentage > 5 ? "Increasing" : changePercentage < -5 ? "Decreasing" : "Stable";

        var thresholdRules = await GetThresholdRulesAsync(monitor.Id, cancellationToken);
        var thresholdStatuses = EvaluateThresholds(currentValue, thresholdRules);
        var status = DetermineKPIStatus(thresholdStatuses);

        // Update monitor with latest values
        monitor.UpdateLastValue(currentValue, status);
        await _context.SaveChangesAsync(cancellationToken);

        return new KPIValueDto
        {
            MonitorId = monitor.Id,
            KPIName = monitor.Name,
            Value = currentValue,
            Unit = GetKPIUnit(monitor.KPIType),
            Status = status,
            Timestamp = DateTime.UtcNow,
            PreviousValue = previousValue,
            ChangePercentage = changePercentage,
            Trend = trend,
            ThresholdStatuses = thresholdStatuses
        };
    }

    private List<ThresholdStatusDto> EvaluateThresholds(decimal currentValue, List<ThresholdRuleDto> thresholdRules)
    {
        var statuses = new List<ThresholdStatusDto>();

        foreach (var rule in thresholdRules.Where(r => r.IsEnabled))
        {
            var isTriggered = rule.Operator.ToUpper() switch
            {
                "GT" => currentValue > rule.ThresholdValue,
                "GTE" => currentValue >= rule.ThresholdValue,
                "LT" => currentValue < rule.ThresholdValue,
                "LTE" => currentValue <= rule.ThresholdValue,
                "EQ" => currentValue == rule.ThresholdValue,
                "NEQ" => currentValue != rule.ThresholdValue,
                _ => false
            };

            statuses.Add(new ThresholdStatusDto
            {
                RuleId = rule.Id,
                RuleName = rule.Name,
                Status = isTriggered ? "Triggered" : "Normal",
                ThresholdValue = rule.ThresholdValue,
                CurrentValue = currentValue,
                Severity = rule.Severity,
                LastTriggered = isTriggered ? DateTime.UtcNow : DateTime.MinValue
            });
        }

        return statuses;
    }

    private string DetermineKPIStatus(List<ThresholdStatusDto> thresholdStatuses)
    {
        if (thresholdStatuses.Any(ts => ts.Status == "Triggered" && ts.Severity == "Critical"))
            return "Critical";

        if (thresholdStatuses.Any(ts => ts.Status == "Triggered" && ts.Severity == "Warning"))
            return "Warning";

        return "Healthy";
    }

    private string GetKPIUnit(string kpiType)
    {
        return kpiType.ToLower() switch
        {
            "revenue" => "$",
            "percentage" => "%",
            "count" => "count",
            "time" => "ms",
            "rate" => "rate",
            _ => "value"
        };
    }

    private async Task<List<KPIDataPointDto>> GenerateKPIHistoryDataAsync(Guid monitorId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var dataPoints = new List<KPIDataPointDto>();
        var random = new Random();
        var current = fromDate;

        while (current <= toDate)
        {
            dataPoints.Add(new KPIDataPointDto
            {
                Timestamp = current,
                Value = Math.Round((decimal)(random.NextDouble() * 1000), 2),
                Status = random.Next(0, 10) > 7 ? "Warning" : "Healthy",
                Metadata = new Dictionary<string, object>
                {
                    { "source", "calculated" },
                    { "confidence", Math.Round(random.NextDouble(), 2) }
                }
            });

            current = current.AddHours(1);
        }

        return dataPoints;
    }

    private KPIStatisticsDto CalculateKPIStatistics(List<KPIDataPointDto> dataPoints)
    {
        if (!dataPoints.Any())
            return new KPIStatisticsDto();

        var values = dataPoints.Select(dp => dp.Value).ToList();
        var sortedValues = values.OrderBy(v => v).ToList();

        var minValue = sortedValues.First();
        var maxValue = sortedValues.Last();
        var averageValue = values.Average();
        var medianValue = sortedValues.Count % 2 == 0
            ? (sortedValues[sortedValues.Count / 2 - 1] + sortedValues[sortedValues.Count / 2]) / 2
            : sortedValues[sortedValues.Count / 2];

        var variance = values.Sum(v => Math.Pow((double)(v - averageValue), 2)) / values.Count;
        var standardDeviation = (decimal)Math.Sqrt(variance);

        // Simple trend calculation
        var trendSlope = dataPoints.Count > 1
            ? (dataPoints.Last().Value - dataPoints.First().Value) / dataPoints.Count
            : 0;

        var trendDirection = trendSlope > 0.1m ? "Increasing" : trendSlope < -0.1m ? "Decreasing" : "Stable";

        return new KPIStatisticsDto
        {
            MinValue = minValue,
            MaxValue = maxValue,
            AverageValue = Math.Round(averageValue, 2),
            MedianValue = Math.Round(medianValue, 2),
            StandardDeviation = Math.Round(standardDeviation, 2),
            TotalDataPoints = dataPoints.Count,
            TrendSlope = Math.Round(trendSlope, 4),
            TrendDirection = trendDirection
        };
    }

    private AlertDto MapAlertToDto(Alert alert)
    {
        return new AlertDto
        {
            Id = alert.Id,
            MonitorId = alert.Context.ContainsKey("MonitorId") ? (Guid)alert.Context["MonitorId"] : Guid.Empty,
            ThresholdRuleId = alert.Context.ContainsKey("ThresholdRuleId") ? (Guid)alert.Context["ThresholdRuleId"] : Guid.Empty,
            KPIName = alert.Context.ContainsKey("KPIName") ? alert.Context["KPIName"].ToString() : string.Empty,
            AlertType = alert.AlertType.ToString(),
            Severity = alert.Severity.ToString(),
            Message = alert.Message,
            CurrentValue = alert.TriggerValue ?? 0m,
            ThresholdValue = alert.ThresholdValue ?? 0m,
            Status = alert.Status.ToString(),
            TriggeredAt = alert.TriggeredAt,
            AcknowledgedAt = alert.AcknowledgedAt,
            AcknowledgedBy = alert.AcknowledgedBy,
            AcknowledgmentNotes = alert.AcknowledgmentNotes,
            IsActive = alert.IsActive
        };
    }

    private async Task ProcessMonitorAsync(KPIMonitor monitor, CancellationToken cancellationToken)
    {
        try
        {
            // Check if it's time to process this monitor
            var lastCalculated = monitor.LastCalculated ?? DateTime.MinValue;
            var nextCalculation = lastCalculated.AddMinutes(monitor.MonitoringIntervalMinutes);

            if (DateTime.UtcNow < nextCalculation)
                return;

            // Calculate current KPI value
            var currentValue = await CalculateCurrentKPIValueAsync(monitor, cancellationToken);

            // Check thresholds and trigger alerts if necessary
            await CheckThresholdsAndTriggerAlertsAsync(monitor, currentValue, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing monitor {MonitorId}", monitor.Id);
        }
    }

    private async Task CheckThresholdsAndTriggerAlertsAsync(KPIMonitor monitor, KPIValueDto currentValue, CancellationToken cancellationToken)
    {
        var thresholdRules = await GetThresholdRulesAsync(monitor.Id, cancellationToken);

        foreach (var rule in thresholdRules.Where(r => r.IsEnabled))
        {
            var thresholdStatus = currentValue.ThresholdStatuses.FirstOrDefault(ts => ts.RuleId == rule.Id);

            if (thresholdStatus?.Status == "Triggered")
            {
                await CreateAlertAsync(monitor, rule, currentValue, cancellationToken);
                await ExecuteAutomatedResponsesAsync(rule.Id, cancellationToken);
            }
        }
    }

    private async Task CreateAlertAsync(KPIMonitor monitor, ThresholdRuleDto rule, KPIValueDto currentValue, CancellationToken cancellationToken)
    {
        // Check if there's already an active alert for this rule
        var existingAlert = await _context.Alerts
            .FirstOrDefaultAsync(a => a.MonitorId == monitor.Id && a.ThresholdRuleId == rule.Id && a.IsActive, cancellationToken);

        if (existingAlert != null)
            return; // Don't create duplicate alerts

        var alert = new Alert(
            name: $"KPI Alert: {monitor.Name}",
            description: $"Threshold alert for KPI '{monitor.Name}'",
            severity: Enum.Parse<AlertSeverity>(rule.Severity),
            message: GenerateAlertMessage(monitor, rule, currentValue),
            metricId: null,
            metricName: monitor.Name,
            triggerValue: currentValue?.Value ?? 0m,
            thresholdValue: rule.ThresholdValue,
            context: new Dictionary<string, object>
            {
                { "MonitorId", monitor.Id },
                { "ThresholdRuleId", rule.Id },
                { "KPIName", monitor.Name },
                { "AlertType", rule.RuleType.ToString() },
                { "Status", "Active" }
            },
            userId: null,
            alertType: AlertType.Performance,
            entityId: monitor.Id,
            entityType: "KPIMonitor");

        _context.Alerts.Add(alert);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogWarning("Alert triggered for KPI {KPIName}: {Message}", monitor.Name, alert.Message);
    }

    private string GenerateAlertMessage(KPIMonitor monitor, ThresholdRuleDto rule, KPIValueDto currentValue)
    {
        return $"KPI '{monitor.Name}' has {rule.Operator.ToLower()} threshold of {rule.ThresholdValue}. Current value: {currentValue.Value}";
    }

    private async Task ExecuteAutomatedResponsesAsync(Guid thresholdRuleId, CancellationToken cancellationToken)
    {
        var responses = await GetAutomatedResponsesAsync(thresholdRuleId, cancellationToken);

        foreach (var response in responses.Where(r => r.IsEnabled).OrderBy(r => r.ExecutionOrder))
        {
            try
            {
                await ExecuteAutomatedResponseAsync(response, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing automated response {ResponseId}", response.Id);
            }
        }
    }

    private async Task ExecuteAutomatedResponseAsync(AutomatedResponseDto response, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Executing automated response: {ResponseName} ({ResponseType})", response.Name, response.ResponseType);

        // Simulate automated response execution
        await Task.Delay(100, cancellationToken);

        switch (response.ResponseType.ToUpper())
        {
            case "EMAIL":
                _logger.LogInformation("Sending email notification");
                break;
            case "WEBHOOK":
                _logger.LogInformation("Calling webhook: {Url}", response.Parameters.GetValueOrDefault("url", ""));
                break;
            case "SCALE":
                _logger.LogInformation("Scaling resources");
                break;
            case "RESTART":
                _logger.LogInformation("Restarting service");
                break;
            default:
                _logger.LogWarning("Unknown response type: {ResponseType}", response.ResponseType);
                break;
        }
    }

    private async Task<List<KPITrendDto>> GetTrendingKPIsAsync(Guid userId, CancellationToken cancellationToken)
    {
        var currentKPIs = await GetCurrentKPIValuesAsync(userId, cancellationToken);
        var trends = new List<KPITrendDto>();

        foreach (var kpi in currentKPIs.Take(5)) // Top 5 trending KPIs
        {
            var recentDataPoints = await GenerateKPIHistoryDataAsync(kpi.MonitorId, DateTime.UtcNow.AddDays(-7), DateTime.UtcNow, cancellationToken);

            trends.Add(new KPITrendDto
            {
                MonitorId = kpi.MonitorId,
                KPIName = kpi.KPIName,
                CurrentValue = kpi.Value,
                PreviousValue = kpi.PreviousValue ?? 0,
                ChangePercentage = kpi.ChangePercentage ?? 0,
                TrendDirection = kpi.Trend,
                TrendStrength = Math.Abs(kpi.ChangePercentage ?? 0) > 10 ? "Strong" : "Moderate",
                RecentDataPoints = recentDataPoints.TakeLast(24).ToList() // Last 24 hours
            });
        }

        return trends;
    }

    private async Task<KPIOverviewStatsDto> GetKPIOverviewStatsAsync(Guid userId, CancellationToken cancellationToken)
    {
        var currentKPIs = await GetCurrentKPIValuesAsync(userId, cancellationToken);
        var activeAlerts = await GetActiveAlertsAsync(userId, cancellationToken);

        var healthyKPIs = currentKPIs.Count(k => k.Status == "Healthy");
        var warningKPIs = currentKPIs.Count(k => k.Status == "Warning");
        var criticalKPIs = currentKPIs.Count(k => k.Status == "Critical");

        var overallHealthScore = currentKPIs.Any()
            ? Math.Round((decimal)healthyKPIs / currentKPIs.Count * 100, 1)
            : 100m;

        return new KPIOverviewStatsDto
        {
            TotalKPIs = currentKPIs.Count,
            HealthyKPIs = healthyKPIs,
            WarningKPIs = warningKPIs,
            CriticalKPIs = criticalKPIs,
            ActiveAlerts = activeAlerts.Count,
            AcknowledgedAlerts = activeAlerts.Count(a => a.AcknowledgedAt.HasValue),
            OverallHealthScore = overallHealthScore
        };
    }

    private async Task<List<KPIPerformanceDto>> GetKPIPerformanceMetricsAsync(Guid userId, CancellationToken cancellationToken)
    {
        var currentKPIs = await GetCurrentKPIValuesAsync(userId, cancellationToken);
        var performance = new List<KPIPerformanceDto>();

        foreach (var kpi in currentKPIs)
        {
            var random = new Random();
            var targetValue = kpi.Value * (decimal)(0.8 + random.NextDouble() * 0.4); // Target within 80-120% of current
            var achievementPercentage = targetValue != 0 ? Math.Round(kpi.Value / targetValue * 100, 1) : 100;

            var performanceStatus = achievementPercentage >= 100 ? "Exceeding" :
                                  achievementPercentage >= 80 ? "Meeting" :
                                  achievementPercentage >= 60 ? "Below" : "Poor";

            performance.Add(new KPIPerformanceDto
            {
                MonitorId = kpi.MonitorId,
                KPIName = kpi.KPIName,
                TargetValue = Math.Round(targetValue, 2),
                CurrentValue = kpi.Value,
                AchievementPercentage = achievementPercentage,
                PerformanceStatus = performanceStatus,
                LastUpdated = kpi.Timestamp
            });
        }

        return performance;
    }

    private List<KPITypeStatsDto> GenerateKPITypeStats(List<KPIMonitor> monitors)
    {
        return monitors
            .GroupBy(m => m.KPIType)
            .Select(g => new KPITypeStatsDto
            {
                KPIType = g.Key,
                Count = g.Count(),
                AverageValue = Math.Round(g.Average(m => m.LastValue ?? 0), 2),
                OverallStatus = "Healthy" // Simplified
            }).ToList();
    }

    private List<AlertSeverityStatsDto> GenerateAlertSeverityStats(List<Alert> alerts)
    {
        var totalAlerts = alerts.Count;

        return alerts
            .GroupBy(a => a.Severity)
            .Select(g => new AlertSeverityStatsDto
            {
                Severity = g.Key.ToString(),
                Count = g.Count(),
                Percentage = totalAlerts > 0 ? Math.Round((decimal)g.Count() / totalAlerts * 100, 1) : 0,
                AverageResolutionTime = TimeSpan.FromHours(2) // Simplified
            }).ToList();
    }
}



public class ThresholdRule
{
    public Guid Id { get; set; }
    public Guid MonitorId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Configuration { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}


