using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Chat service implementation
/// </summary>
public class ChatService : IChatService
{
    private readonly IMessageRepository _messageRepository;
    private readonly IConversationRepository _conversationRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ChatService> _logger;

    public ChatService(
        IMessageRepository messageRepository,
        IConversationRepository conversationRepository,
        IUnitOfWork unitOfWork,
        ILogger<ChatService> logger)
    {
        _messageRepository = messageRepository;
        _conversationRepository = conversationRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ChatMessageResult> SendMessageAsync(
        ChatMessageRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var transaction = await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // Get or create conversation
            ConversationThread? conversation = null;
            
            if (request.ConversationId.HasValue)
            {
                conversation = await _conversationRepository.GetByIdAsync(request.ConversationId.Value, cancellationToken);
                if (conversation == null)
                {
                    return ChatMessageResult.Failure("Conversation not found");
                }
            }
            else if (request.RecipientIds?.Any() == true)
            {
                // Create new direct conversation
                var createRequest = new CreateConversationRequest
                {
                    Type = request.RecipientIds.Count == 1 ? ConversationType.Direct : ConversationType.Group,
                    ParticipantIds = request.RecipientIds.Concat(new[] { request.SenderId }).ToList(),
                    CreatedBy = request.SenderId,
                    RelatedEntityId = request.RelatedEntityId,
                    RelatedEntityType = request.RelatedEntityType
                };

                conversation = await CreateConversationInternalAsync(createRequest, cancellationToken);
            }
            else
            {
                return ChatMessageResult.Failure("Either ConversationId or RecipientIds must be provided");
            }

            // Validate sender is participant
            if (!conversation.IsParticipant(request.SenderId))
            {
                return ChatMessageResult.Failure("User is not a participant in this conversation");
            }

            // Create message
            var message = Message.Create(
                request.SenderId,
                conversation.Id,
                request.Content,
                request.MessageType,
                request.Priority,
                request.RelatedEntityId,
                request.RelatedEntityType,
                request.Metadata);

            await _messageRepository.AddAsync(message, cancellationToken);

            // Update conversation last activity
            conversation.UpdateLastActivity();
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Message {MessageId} sent successfully in conversation {ConversationId} by user {UserId}",
                message.Id, conversation.Id, request.SenderId);

            return ChatMessageResult.Success(message.Id, conversation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send message for user {UserId}", request.SenderId);
            return ChatMessageResult.Failure($"Failed to send message: {ex.Message}");
        }
    }

    public async Task<ConversationHistory> GetConversationHistoryAsync(
        Guid conversationId,
        int pageSize = 50,
        string? pageToken = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var pageNumber = ParsePageToken(pageToken);
            var messages = await _messageRepository.GetByConversationThreadIdAsync(
                conversationId, pageSize, pageNumber, cancellationToken);

            var hasMore = messages.Count == pageSize;
            var nextPageToken = hasMore ? GeneratePageToken(pageNumber + 1) : null;

            return new ConversationHistory
            {
                ConversationId = conversationId,
                Messages = messages,
                NextPageToken = nextPageToken,
                HasMoreMessages = hasMore,
                TotalMessages = await GetTotalMessageCountAsync(conversationId, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get conversation history for {ConversationId}", conversationId);
            throw;
        }
    }

    public async Task<ConversationThread> CreateConversationAsync(
        CreateConversationRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var transaction = await _unitOfWork.BeginTransactionAsync(cancellationToken);

            var conversation = await CreateConversationInternalAsync(request, cancellationToken);

            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Conversation {ConversationId} created by user {UserId} with {ParticipantCount} participants",
                conversation.Id, request.CreatedBy, request.ParticipantIds.Count);

            return conversation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create conversation for user {UserId}", request.CreatedBy);
            throw;
        }
    }

    public async Task<bool> AddParticipantAsync(
        Guid conversationId,
        Guid userId,
        ConversationRole role = ConversationRole.Participant,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return false;
            }

            if (conversation.IsParticipant(userId))
            {
                _logger.LogWarning("User {UserId} is already a participant in conversation {ConversationId}", 
                    userId, conversationId);
                return true;
            }

            conversation.AddParticipant(userId, role);
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            _logger.LogInformation("User {UserId} added to conversation {ConversationId} with role {Role}",
                userId, conversationId, role);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add participant {UserId} to conversation {ConversationId}", 
                userId, conversationId);
            return false;
        }
    }

    public async Task<bool> RemoveParticipantAsync(
        Guid conversationId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return false;
            }

            if (!conversation.IsParticipant(userId))
            {
                _logger.LogWarning("User {UserId} is not a participant in conversation {ConversationId}", 
                    userId, conversationId);
                return true;
            }

            conversation.RemoveParticipant(userId);
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            _logger.LogInformation("User {UserId} removed from conversation {ConversationId}", userId, conversationId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove participant {UserId} from conversation {ConversationId}", 
                userId, conversationId);
            return false;
        }
    }

    public async Task<bool> MarkMessageAsReadAsync(
        Guid messageId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var message = await _messageRepository.GetByIdAsync(messageId, cancellationToken);
            if (message == null)
            {
                return false;
            }

            // Don't mark own messages as read
            if (message.SenderId == userId)
            {
                return true;
            }

            message.MarkAsRead();
            await _messageRepository.UpdateAsync(message, cancellationToken);

            _logger.LogDebug("Message {MessageId} marked as read by user {UserId}", messageId, userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark message {MessageId} as read by user {UserId}", messageId, userId);
            return false;
        }
    }

    public async Task<List<ConversationSummary>> GetUserConversationsAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var conversations = await _conversationRepository.GetByUserIdAsync(userId, cancellationToken);
            var summaries = new List<ConversationSummary>();

            foreach (var conversation in conversations)
            {
                var lastMessage = await GetLastMessageAsync(conversation.Id, cancellationToken);
                var unreadCount = await GetUnreadMessageCountAsync(conversation.Id, userId, cancellationToken);

                summaries.Add(new ConversationSummary
                {
                    Id = conversation.Id,
                    Title = conversation.Title,
                    Type = conversation.Type,
                    ParticipantCount = conversation.Participants.Count,
                    LastMessage = lastMessage,
                    UnreadCount = unreadCount,
                    LastActivity = conversation.LastActivity,
                    RelatedEntityId = conversation.RelatedEntityId,
                    RelatedEntityType = conversation.RelatedEntityType
                });
            }

            return summaries.OrderByDescending(s => s.LastActivity).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get conversations for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<Message>> SearchMessagesAsync(
        Guid conversationId,
        string searchQuery,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _messageRepository.SearchMessagesAsync(
                searchQuery, null, conversationId, 50, 1, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search messages in conversation {ConversationId} with query '{Query}'", 
                conversationId, searchQuery);
            throw;
        }
    }

    private async Task<ConversationThread> CreateConversationInternalAsync(
        CreateConversationRequest request,
        CancellationToken cancellationToken)
    {
        var conversation = ConversationThread.Create(
            request.Title,
            request.Type,
            request.CreatedBy,
            request.RelatedEntityId,
            request.RelatedEntityType,
            request.Metadata);

        // Add participants
        foreach (var participantId in request.ParticipantIds)
        {
            var role = participantId == request.CreatedBy ? ConversationRole.Admin : ConversationRole.Participant;
            conversation.AddParticipant(participantId, role);
        }

        await _conversationRepository.AddAsync(conversation, cancellationToken);
        return conversation;
    }

    private async Task<Message?> GetLastMessageAsync(Guid conversationId, CancellationToken cancellationToken)
    {
        var messages = await _messageRepository.GetByConversationThreadIdAsync(conversationId, 1, 1, cancellationToken);
        return messages.FirstOrDefault();
    }

    private async Task<int> GetUnreadMessageCountAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken)
    {
        // This would need to be implemented in the repository
        // For now, return 0 as placeholder
        return 0;
    }

    private async Task<int> GetTotalMessageCountAsync(Guid conversationId, CancellationToken cancellationToken)
    {
        // This would need to be implemented in the repository
        // For now, return estimated count
        return 100;
    }

    private static int ParsePageToken(string? pageToken)
    {
        if (string.IsNullOrEmpty(pageToken) || !int.TryParse(pageToken, out var pageNumber))
        {
            return 1;
        }
        return Math.Max(1, pageNumber);
    }

    private static string GeneratePageToken(int pageNumber)
    {
        return pageNumber.ToString();
    }
}
