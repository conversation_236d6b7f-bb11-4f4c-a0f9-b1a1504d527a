using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetNegotiationHistory;

public class GetNegotiationHistoryQuery : IRequest<List<NegotiationDto>>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public Guid? BrokerId { get; set; }
    public bool IncludeCompleted { get; set; } = true;
    public bool IncludeCancelled { get; set; } = false;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SortBy { get; set; } = "StartedAt"; // StartedAt, CompletedAt, TotalRounds
    public bool SortDescending { get; set; } = true;
}
