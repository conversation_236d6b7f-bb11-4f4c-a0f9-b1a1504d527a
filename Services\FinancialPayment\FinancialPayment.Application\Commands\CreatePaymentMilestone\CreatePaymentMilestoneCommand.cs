using MediatR;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.CreatePaymentMilestone;

public class CreatePaymentMilestoneCommand : IRequest<Guid>
{
    public Guid EscrowAccountId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MoneyDto Amount { get; set; } = new();
    public decimal PayoutPercentage { get; set; }
    public int SequenceNumber { get; set; }
    public DateTime? DueDate { get; set; }
    public bool IsRequired { get; set; } = true;
    public bool RequiresApproval { get; set; } = false;
    public string? TripLegReference { get; set; }
    public string? OrderReference { get; set; }
    public List<string> CompletionCriteria { get; set; } = new();
    public List<PayoutRuleRequest> PayoutRules { get; set; } = new();
    public Guid RequestingUserId { get; set; }
}

public class PayoutRuleRequest
{
    public string RuleName { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public decimal RuleValue { get; set; }
    public int Priority { get; set; } = 1;
    public string? Condition { get; set; }
    public string? Description { get; set; }
}
