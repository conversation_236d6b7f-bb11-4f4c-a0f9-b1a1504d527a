-- Database setup script for TLI Analytics & Business Intelligence Service
-- Run this script to set up the database for development/production

-- Create database (run as superuser)
-- CREATE DATABASE tli_analytics_dev WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';

-- Connect to the database
\c tli_analytics_dev;

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Enable additional extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create schema
CREATE SCHEMA IF NOT EXISTS analytics;

-- Create roles
CREATE ROLE tli_analytics_app WITH LOGIN PASSWORD 'analytics_secure_password_2024';
CREATE ROLE tli_analytics_readonly WITH LOGIN PASSWORD 'readonly_secure_password_2024';

-- Grant permissions
GRANT CONNECT ON DATABASE tli_analytics_dev TO tli_analytics_app;
GRANT CONNECT ON DATABASE tli_analytics_dev TO tli_analytics_readonly;

GRANT USAGE ON SCHEMA analytics TO tli_analytics_app;
GRANT USAGE ON SCHEMA analytics TO tli_analytics_readonly;
GRANT CREATE ON SCHEMA analytics TO tli_analytics_app;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT SELECT ON TABLES TO tli_analytics_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT ALL ON TABLES TO tli_analytics_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT ALL ON SEQUENCES TO tli_analytics_app;

-- Create audit log table
CREATE TABLE IF NOT EXISTS analytics.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    action VARCHAR(50) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    changes JSONB,
    additional_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ
);

-- Create indexes for audit logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON analytics.audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON analytics.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON analytics.audit_logs(entity_type, entity_id);

-- Convert audit logs to hypertable
SELECT create_hypertable('analytics.audit_logs', 'timestamp', if_not_exists => TRUE);

-- Add retention policy for audit logs (keep for 7 years for compliance)
SELECT add_retention_policy('analytics.audit_logs', INTERVAL '7 years', if_not_exists => TRUE);

-- Create sample data for testing (optional)
-- This will be populated by the application

-- Performance optimization settings
-- These are already set in the init script but can be adjusted here

-- Create indexes that will be used by the application
-- Note: Most indexes will be created by Entity Framework migrations
-- but critical ones for performance can be pre-created here

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Database setup completed for TLI Analytics & BI Service';
    RAISE NOTICE 'Database: tli_analytics_dev';
    RAISE NOTICE 'Schema: analytics';
    RAISE NOTICE 'Extensions: timescaledb, uuid-ossp, pg_stat_statements, pg_trgm, btree_gin, btree_gist';
END
$$;
