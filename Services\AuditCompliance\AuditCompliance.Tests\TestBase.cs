using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AuditCompliance.Infrastructure.Persistence;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Infrastructure.Repositories;
using AuditCompliance.Infrastructure.Services;
using Moq;

namespace AuditCompliance.Tests;

/// <summary>
/// Base class for integration tests with in-memory database setup
/// </summary>
public abstract class TestBase : IDisposable
{
    protected readonly ServiceProvider ServiceProvider;
    protected readonly AuditComplianceDbContext DbContext;

    protected TestBase()
    {
        var services = new ServiceCollection();
        
        // Add in-memory database
        services.AddDbContext<AuditComplianceDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        // Add repositories
        services.AddScoped<IAuditLogRepository, AuditLogRepository>();
        services.AddScoped<IComplianceReportRepository, ComplianceReportRepository>();
        services.AddScoped<IServiceProviderRatingRepository, ServiceProviderRatingRepository>();
        services.AddScoped<IPreferredProviderNetworkRepository, PreferredProviderNetworkRepository>();

        // Add services
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IComplianceService, ComplianceService>();
        services.AddScoped<IRatingService, RatingService>();
        services.AddScoped<IPreferredProviderService, PreferredProviderService>();

        // Add logging
        services.AddLogging(builder => builder.AddConsole());

        ServiceProvider = services.BuildServiceProvider();
        DbContext = ServiceProvider.GetRequiredService<AuditComplianceDbContext>();
        
        // Ensure database is created
        DbContext.Database.EnsureCreated();
    }

    protected T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    protected async Task SeedTestDataAsync()
    {
        // Override in derived classes to seed specific test data
        await Task.CompletedTask;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
        ServiceProvider?.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// Test utilities and helper methods
/// </summary>
public static class TestHelpers
{
    public static Mock<ILogger<T>> CreateMockLogger<T>()
    {
        return new Mock<ILogger<T>>();
    }

    public static void VerifyLoggerCalled<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, string message)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public static void VerifyLoggerCalledWithException<T>(Mock<ILogger<T>> mockLogger, LogLevel logLevel, Type exceptionType)
    {
        mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}

/// <summary>
/// Custom test data builders for creating test entities
/// </summary>
public static class TestDataBuilder
{
    public static AuditCompliance.Domain.Entities.AuditLog CreateAuditLog(
        AuditCompliance.Domain.Enums.AuditEventType eventType = AuditCompliance.Domain.Enums.AuditEventType.UserLogin,
        AuditCompliance.Domain.Enums.AuditSeverity severity = AuditCompliance.Domain.Enums.AuditSeverity.Info,
        string entityType = "User",
        string action = "Login",
        string description = "Test audit log",
        Guid? userId = null,
        string? userName = null,
        string? userRole = null)
    {
        return new AuditCompliance.Domain.Entities.AuditLog(
            eventType,
            severity,
            entityType,
            action,
            description,
            userId,
            userName,
            userRole);
    }

    public static AuditCompliance.Domain.Entities.ServiceProviderRating CreateServiceProviderRating(
        Guid? shipperId = null,
        string shipperName = "Test Shipper",
        Guid? transportCompanyId = null,
        string transportCompanyName = "Test Transport Company",
        AuditCompliance.Domain.Enums.RatingScale scale = AuditCompliance.Domain.Enums.RatingScale.FourStars,
        DateTime? serviceCompletedAt = null)
    {
        return new AuditCompliance.Domain.Entities.ServiceProviderRating(
            shipperId ?? Guid.NewGuid(),
            shipperName,
            transportCompanyId ?? Guid.NewGuid(),
            transportCompanyName,
            AuditCompliance.Domain.ValueObjects.RatingScore.FromScale(scale),
            serviceCompletedAt ?? DateTime.UtcNow.AddDays(-1));
    }

    public static AuditCompliance.Domain.Entities.ComplianceReport CreateComplianceReport(
        string title = "Test Compliance Report",
        string description = "Test compliance report description",
        AuditCompliance.Domain.Enums.ComplianceStandard standard = AuditCompliance.Domain.Enums.ComplianceStandard.GDPR,
        DateTime? periodStart = null,
        DateTime? periodEnd = null,
        Guid? generatedBy = null,
        string generatedByName = "Test User",
        bool isAutomated = false)
    {
        return new AuditCompliance.Domain.Entities.ComplianceReport(
            title,
            description,
            standard,
            periodStart ?? DateTime.UtcNow.AddMonths(-1),
            periodEnd ?? DateTime.UtcNow,
            generatedBy ?? Guid.NewGuid(),
            generatedByName,
            isAutomated);
    }

    public static AuditCompliance.Domain.Entities.PreferredProviderNetwork CreatePreferredProviderNetwork(
        Guid? shipperId = null,
        string shipperName = "Test Shipper",
        Guid? transportCompanyId = null,
        string transportCompanyName = "Test Transport Company",
        decimal averageRating = 4.5m,
        int totalOrders = 10,
        int completedOrders = 9,
        DateTime? firstOrderDate = null,
        DateTime? lastOrderDate = null)
    {
        return new AuditCompliance.Domain.Entities.PreferredProviderNetwork(
            shipperId ?? Guid.NewGuid(),
            shipperName,
            transportCompanyId ?? Guid.NewGuid(),
            transportCompanyName,
            averageRating,
            totalOrders,
            completedOrders,
            firstOrderDate ?? DateTime.UtcNow.AddMonths(-6),
            lastOrderDate ?? DateTime.UtcNow.AddDays(-1));
    }
}

/// <summary>
/// Test categories for organizing tests
/// </summary>
public static class TestCategories
{
    public const string Unit = "Unit";
    public const string Integration = "Integration";
    public const string Domain = "Domain";
    public const string Application = "Application";
    public const string Infrastructure = "Infrastructure";
    public const string API = "API";
    public const string Performance = "Performance";
    public const string Security = "Security";
}
