using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.Events;
using CommunicationNotification.Domain.ValueObjects;
using CommunicationNotification.Domain.Exceptions;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Represents configurable notification settings for Transport Company Portal
/// </summary>
public class TransportCompanyNotificationConfiguration : AggregateRoot
{
    public Guid TransportCompanyId { get; private set; }
    public bool IsEnabled { get; private set; }
    public NotificationChannelPreferences ChannelPreferences { get; private set; }
    public QuietHoursConfiguration QuietHours { get; private set; }
    public NotificationFrequencySettings FrequencySettings { get; private set; }
    public NotificationGroupingSettings GroupingSettings { get; private set; }
    public List<NotificationTypeConfiguration> NotificationTypes { get; private set; } = new();
    public List<NotificationRecipientConfiguration> Recipients { get; private set; } = new();
    public EscalationConfiguration EscalationSettings { get; private set; }
    public DigestConfiguration DigestSettings { get; private set; }
    public Dictionary<string, object> CustomSettings { get; private set; } = new();
    public DateTime LastUpdatedAt { get; private set; }
    public Guid LastUpdatedByUserId { get; private set; }

    private TransportCompanyNotificationConfiguration() { }

    public TransportCompanyNotificationConfiguration(
        Guid transportCompanyId,
        NotificationChannelPreferences channelPreferences,
        QuietHoursConfiguration quietHours,
        NotificationFrequencySettings frequencySettings,
        NotificationGroupingSettings groupingSettings,
        EscalationConfiguration escalationSettings,
        DigestConfiguration digestSettings,
        Guid createdByUserId,
        bool isEnabled = true)
    {
        TransportCompanyId = transportCompanyId;
        IsEnabled = isEnabled;
        ChannelPreferences = channelPreferences ?? throw new ArgumentNullException(nameof(channelPreferences));
        QuietHours = quietHours ?? throw new ArgumentNullException(nameof(quietHours));
        FrequencySettings = frequencySettings ?? throw new ArgumentNullException(nameof(frequencySettings));
        GroupingSettings = groupingSettings ?? throw new ArgumentNullException(nameof(groupingSettings));
        EscalationSettings = escalationSettings ?? throw new ArgumentNullException(nameof(escalationSettings));
        DigestSettings = digestSettings ?? throw new ArgumentNullException(nameof(digestSettings));
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = createdByUserId;

        AddDomainEvent(new TransportCompanyNotificationConfigurationCreatedEvent(
            Id, TransportCompanyId, createdByUserId, LastUpdatedAt));
    }

    public void UpdateChannelPreferences(NotificationChannelPreferences channelPreferences, Guid updatedByUserId)
    {
        ChannelPreferences = channelPreferences ?? throw new ArgumentNullException(nameof(channelPreferences));
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationUpdatedEvent(
            Id, TransportCompanyId, "ChannelPreferences", updatedByUserId, LastUpdatedAt));
    }

    public void UpdateQuietHours(QuietHoursConfiguration quietHours, Guid updatedByUserId)
    {
        QuietHours = quietHours ?? throw new ArgumentNullException(nameof(quietHours));
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationUpdatedEvent(
            Id, TransportCompanyId, "QuietHours", updatedByUserId, LastUpdatedAt));
    }

    public void UpdateFrequencySettings(NotificationFrequencySettings frequencySettings, Guid updatedByUserId)
    {
        FrequencySettings = frequencySettings ?? throw new ArgumentNullException(nameof(frequencySettings));
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationUpdatedEvent(
            Id, TransportCompanyId, "FrequencySettings", updatedByUserId, LastUpdatedAt));
    }

    public void UpdateGroupingSettings(NotificationGroupingSettings groupingSettings, Guid updatedByUserId)
    {
        GroupingSettings = groupingSettings ?? throw new ArgumentNullException(nameof(groupingSettings));
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationUpdatedEvent(
            Id, TransportCompanyId, "GroupingSettings", updatedByUserId, LastUpdatedAt));
    }

    public void AddNotificationType(NotificationTypeConfiguration notificationType, Guid updatedByUserId)
    {
        if (NotificationTypes.Any(nt => nt.MessageType == notificationType.MessageType))
            throw new InvalidNotificationConfigurationException($"Notification type {notificationType.MessageType} already configured");

        NotificationTypes.Add(notificationType);
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationTypeAddedEvent(
            Id, TransportCompanyId, notificationType.MessageType, updatedByUserId, LastUpdatedAt));
    }

    public void UpdateNotificationType(NotificationTypeConfiguration notificationType, Guid updatedByUserId)
    {
        var existingType = NotificationTypes.FirstOrDefault(nt => nt.MessageType == notificationType.MessageType);
        if (existingType == null)
            throw new InvalidNotificationConfigurationException($"Notification type {notificationType.MessageType} not found");

        NotificationTypes.Remove(existingType);
        NotificationTypes.Add(notificationType);
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationTypeUpdatedEvent(
            Id, TransportCompanyId, notificationType.MessageType, updatedByUserId, LastUpdatedAt));
    }

    public void RemoveNotificationType(MessageType messageType, Guid updatedByUserId)
    {
        var existingType = NotificationTypes.FirstOrDefault(nt => nt.MessageType == messageType);
        if (existingType == null)
            throw new InvalidNotificationConfigurationException($"Notification type {messageType} not found");

        NotificationTypes.Remove(existingType);
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationTypeRemovedEvent(
            Id, TransportCompanyId, messageType, updatedByUserId, LastUpdatedAt));
    }

    public void AddRecipient(NotificationRecipientConfiguration recipient, Guid updatedByUserId)
    {
        if (Recipients.Any(r => r.UserId == recipient.UserId && r.Role == recipient.Role))
            throw new InvalidNotificationConfigurationException($"Recipient {recipient.UserId} with role {recipient.Role} already configured");

        Recipients.Add(recipient);
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationRecipientAddedEvent(
            Id, TransportCompanyId, recipient.UserId, recipient.Role, updatedByUserId, LastUpdatedAt));
    }

    public void RemoveRecipient(Guid userId, NotificationRecipientRole role, Guid updatedByUserId)
    {
        var existingRecipient = Recipients.FirstOrDefault(r => r.UserId == userId && r.Role == role);
        if (existingRecipient == null)
            throw new InvalidNotificationConfigurationException($"Recipient {userId} with role {role} not found");

        Recipients.Remove(existingRecipient);
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationRecipientRemovedEvent(
            Id, TransportCompanyId, userId, role, updatedByUserId, LastUpdatedAt));
    }

    public void Enable(Guid updatedByUserId)
    {
        if (IsEnabled) return;

        IsEnabled = true;
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationEnabledEvent(
            Id, TransportCompanyId, updatedByUserId, LastUpdatedAt));
    }

    public void Disable(Guid updatedByUserId)
    {
        if (!IsEnabled) return;

        IsEnabled = false;
        LastUpdatedAt = DateTime.UtcNow;
        LastUpdatedByUserId = updatedByUserId;

        AddDomainEvent(new NotificationConfigurationDisabledEvent(
            Id, TransportCompanyId, updatedByUserId, LastUpdatedAt));
    }

    public bool ShouldSendNotification(
        MessageType messageType,
        Priority priority,
        DateTime currentTime,
        NotificationChannel channel)
    {
        if (!IsEnabled)
            return false;

        // Check if notification type is configured and enabled
        var notificationType = NotificationTypes.FirstOrDefault(nt => nt.MessageType == messageType);
        if (notificationType == null || !notificationType.IsEnabled)
            return false;

        // Check channel preferences
        if (!ChannelPreferences.IsChannelEnabled(channel))
            return false;

        // Check quiet hours (except for emergency messages)
        if (priority != Priority.Emergency && QuietHours.IsInQuietHours(currentTime))
            return false;

        // Check frequency limits
        if (!FrequencySettings.CanSendNotification(messageType, currentTime))
            return false;

        return true;
    }

    public List<NotificationChannel> GetPreferredChannels(MessageType messageType, Priority priority)
    {
        var notificationType = NotificationTypes.FirstOrDefault(nt => nt.MessageType == messageType);
        if (notificationType == null)
            return ChannelPreferences.GetDefaultChannels(priority);

        return notificationType.PreferredChannels.Any()
            ? notificationType.PreferredChannels
            : ChannelPreferences.GetDefaultChannels(priority);
    }

    public List<NotificationRecipientConfiguration> GetRecipientsForNotification(
        MessageType messageType,
        Priority priority)
    {
        var notificationType = NotificationTypes.FirstOrDefault(nt => nt.MessageType == messageType);
        if (notificationType == null)
            return Recipients.Where(r => r.IsEnabled).ToList();

        // Filter recipients based on notification type configuration
        var eligibleRoles = notificationType.EligibleRecipientRoles;
        if (!eligibleRoles.Any())
            return Recipients.Where(r => r.IsEnabled).ToList();

        return Recipients.Where(r => r.IsEnabled && eligibleRoles.Contains(r.Role)).ToList();
    }

    public bool ShouldGroupNotifications(MessageType messageType)
    {
        if (!GroupingSettings.IsEnabled)
            return false;

        return GroupingSettings.GroupableMessageTypes.Contains(messageType);
    }

    public TimeSpan GetGroupingWindow(MessageType messageType)
    {
        return GroupingSettings.GetGroupingWindow(messageType);
    }

    public bool ShouldCreateDigest(MessageType messageType)
    {
        if (!DigestSettings.IsEnabled)
            return false;

        return DigestSettings.DigestableMessageTypes.Contains(messageType);
    }

    public static TransportCompanyNotificationConfiguration CreateDefault(
        Guid transportCompanyId,
        Guid createdByUserId)
    {
        return new TransportCompanyNotificationConfiguration(
            transportCompanyId,
            NotificationChannelPreferences.CreateDefault(),
            QuietHoursConfiguration.CreateDefault(),
            NotificationFrequencySettings.CreateDefault(),
            NotificationGroupingSettings.CreateDefault(),
            EscalationConfiguration.CreateDefault(),
            DigestConfiguration.CreateDefault(),
            createdByUserId);
    }
}


