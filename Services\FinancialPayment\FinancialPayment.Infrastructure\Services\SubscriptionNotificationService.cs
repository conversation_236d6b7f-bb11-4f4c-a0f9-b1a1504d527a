using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Services;

public class SubscriptionNotificationService : ISubscriptionNotificationService
{
    private readonly ILogger<SubscriptionNotificationService> _logger;
    private readonly SubscriptionConfiguration _configuration;

    public SubscriptionNotificationService(
        ILogger<SubscriptionNotificationService> logger,
        IOptions<SubscriptionConfiguration> configuration)
    {
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task SendBillingSuccessNotificationAsync(Subscription subscription, BillingCycle billingCycle)
    {
        _logger.LogInformation("Sending billing success notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            // For demo purposes, we'll just log the notification
            // In a real implementation, you would send email, SMS, or push notifications
            
            var message = $"Your subscription billing was successful. " +
                         $"Amount: {billingCycle.Amount} " +
                         $"Billing Period: {billingCycle.CycleStart:yyyy-MM-dd} to {billingCycle.CycleEnd:yyyy-MM-dd} " +
                         $"Transaction ID: {billingCycle.PaymentTransactionId}";

            _logger.LogInformation("Billing success notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Billing success notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending billing success notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendBillingFailureNotificationAsync(Subscription subscription, BillingCycle billingCycle, string reason)
    {
        _logger.LogInformation("Sending billing failure notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your subscription billing failed. " +
                         $"Amount: {billingCycle.Amount} " +
                         $"Reason: {reason} " +
                         $"Please update your payment method to continue your subscription.";

            if (billingCycle.NextRetryAt.HasValue)
            {
                message += $" Next retry: {billingCycle.NextRetryAt:yyyy-MM-dd HH:mm}";
            }

            _logger.LogWarning("Billing failure notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Billing failure notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending billing failure notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendTrialEndingNotificationAsync(Subscription subscription, int daysRemaining)
    {
        _logger.LogInformation("Sending trial ending notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your trial period is ending in {daysRemaining} day(s). " +
                         $"Please add a payment method to continue your subscription. " +
                         $"Trial ends: {subscription.TrialEndDate:yyyy-MM-dd}";

            _logger.LogInformation("Trial ending notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Trial ending notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending trial ending notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendSubscriptionCancelledNotificationAsync(Subscription subscription, string reason)
    {
        _logger.LogInformation("Sending subscription cancelled notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your subscription has been cancelled. " +
                         $"Reason: {reason} " +
                         $"Cancelled on: {subscription.CancelledAt:yyyy-MM-dd} " +
                         $"You can reactivate your subscription at any time.";

            _logger.LogInformation("Subscription cancelled notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Subscription cancelled notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending subscription cancelled notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendPaymentMethodUpdateRequiredNotificationAsync(Subscription subscription)
    {
        _logger.LogInformation("Sending payment method update notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your payment method needs to be updated. " +
                         $"Please update your payment information to avoid service interruption. " +
                         $"Next billing date: {subscription.NextBillingDate:yyyy-MM-dd}";

            _logger.LogWarning("Payment method update required notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Payment method update notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending payment method update notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendSubscriptionRenewalReminderAsync(Subscription subscription, int daysUntilRenewal)
    {
        _logger.LogInformation("Sending subscription renewal reminder for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your subscription will renew in {daysUntilRenewal} day(s). " +
                         $"Amount: {subscription.CurrentPrice} " +
                         $"Renewal date: {subscription.NextBillingDate:yyyy-MM-dd}";

            _logger.LogInformation("Subscription renewal reminder: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Subscription renewal reminder sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending subscription renewal reminder for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendSubscriptionUpgradeNotificationAsync(Subscription subscription, string oldPlanName, string newPlanName)
    {
        _logger.LogInformation("Sending subscription upgrade notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your subscription has been upgraded from {oldPlanName} to {newPlanName}. " +
                         $"New price: {subscription.CurrentPrice} " +
                         $"Changes take effect immediately.";

            _logger.LogInformation("Subscription upgrade notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Subscription upgrade notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending subscription upgrade notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }

    public async Task SendSubscriptionDowngradeNotificationAsync(Subscription subscription, string oldPlanName, string newPlanName)
    {
        _logger.LogInformation("Sending subscription downgrade notification for subscription {SubscriptionId}",
            subscription.Id);

        try
        {
            var message = $"Your subscription has been downgraded from {oldPlanName} to {newPlanName}. " +
                         $"New price: {subscription.CurrentPrice} " +
                         $"Changes will take effect on your next billing cycle: {subscription.NextBillingDate:yyyy-MM-dd}";

            _logger.LogInformation("Subscription downgrade notification: {Message}", message);

            // Simulate sending notification
            await Task.Delay(100);

            _logger.LogInformation("Subscription downgrade notification sent for subscription {SubscriptionId}",
                subscription.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending subscription downgrade notification for subscription {SubscriptionId}",
                subscription.Id);
        }
    }
}
