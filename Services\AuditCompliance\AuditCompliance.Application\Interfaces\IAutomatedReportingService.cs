using AuditCompliance.Application.DTOs.Reporting;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for automated compliance reporting
/// </summary>
public interface IAutomatedReportingService
{
    /// <summary>
    /// Create a new automated report schedule
    /// </summary>
    Task<Guid> CreateReportScheduleAsync(
        CreateReportScheduleDto scheduleDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update an existing report schedule
    /// </summary>
    Task<bool> UpdateReportScheduleAsync(
        Guid scheduleId,
        UpdateReportScheduleDto scheduleDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a report schedule
    /// </summary>
    Task<bool> DeleteReportScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report schedule by ID
    /// </summary>
    Task<ReportScheduleDto?> GetReportScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all report schedules for a tenant
    /// </summary>
    Task<List<ReportScheduleDto>> GetReportSchedulesAsync(
        Guid? tenantId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate a report immediately
    /// </summary>
    Task<Guid> GenerateReportAsync(
        GenerateReportDto reportDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available report templates
    /// </summary>
    Task<List<ReportTemplateDto>> GetReportTemplatesAsync(
        string? category = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a custom report template
    /// </summary>
    Task<Guid> CreateReportTemplateAsync(
        CreateReportTemplateDto templateDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update a report template
    /// </summary>
    Task<bool> UpdateReportTemplateAsync(
        Guid templateId,
        UpdateReportTemplateDto templateDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report execution history
    /// </summary>
    Task<List<ReportExecutionDto>> GetReportExecutionHistoryAsync(
        Guid? scheduleId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report by ID
    /// </summary>
    Task<GeneratedReportDto?> GetReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Download report file
    /// </summary>
    Task<ReportFileDto?> DownloadReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Export report in different formats
    /// </summary>
    Task<Guid> ExportReportAsync(
        ExportReportDto exportDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get export status
    /// </summary>
    Task<ExportStatusDto> GetExportStatusAsync(
        Guid exportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process scheduled reports (called by background service)
    /// </summary>
    Task ProcessScheduledReportsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate report template
    /// </summary>
    Task<ReportTemplateValidationResult> ValidateReportTemplateAsync(
        ReportTemplateDto template,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance metrics for reporting
    /// </summary>
    Task<ComplianceMetricsDto> GetComplianceMetricsAsync(
        ComplianceMetricsRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate regulatory compliance report
    /// </summary>
    Task<Guid> GenerateRegulatoryReportAsync(
        RegulatoryReportRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get report distribution list
    /// </summary>
    Task<List<ReportDistributionDto>> GetReportDistributionAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update report distribution list
    /// </summary>
    Task<bool> UpdateReportDistributionAsync(
        Guid scheduleId,
        List<ReportDistributionDto> distribution,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send report notification
    /// </summary>
    Task<bool> SendReportNotificationAsync(
        Guid reportId,
        List<string> recipients,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for report template engine
/// </summary>
public interface IReportTemplateEngine
{
    /// <summary>
    /// Render report using template
    /// </summary>
    Task<string> RenderReportAsync(
        ReportTemplateDto template,
        Dictionary<string, object> data,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate template syntax
    /// </summary>
    Task<TemplateValidationResult> ValidateTemplateAsync(
        string templateContent,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available template variables
    /// </summary>
    Task<List<TemplateVariableDto>> GetTemplateVariablesAsync(
        string templateType,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for report export service
/// </summary>
public interface IReportExportService
{
    /// <summary>
    /// Export report to PDF
    /// </summary>
    Task<byte[]> ExportToPdfAsync(
        string htmlContent,
        ReportExportOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Export report to Excel
    /// </summary>
    Task<byte[]> ExportToExcelAsync(
        ReportDataDto reportData,
        ReportExportOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Export report to CSV
    /// </summary>
    Task<byte[]> ExportToCsvAsync(
        ReportDataDto reportData,
        ReportExportOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Export report to JSON
    /// </summary>
    Task<byte[]> ExportToJsonAsync(
        ReportDataDto reportData,
        ReportExportOptions options,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Report export options
/// </summary>
public class ReportExportOptions
{
    public string FileName { get; set; } = string.Empty;
    public bool IncludeCharts { get; set; } = true;
    public bool IncludeMetadata { get; set; } = true;
    public string? Password { get; set; }
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// Template validation result
/// </summary>
public class TemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> RequiredVariables { get; set; } = new();
}

/// <summary>
/// Report template validation result
/// </summary>
public class ReportTemplateValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> MissingDataSources { get; set; } = new();
    public List<string> InvalidFilters { get; set; } = new();
}

/// <summary>
/// Template variable DTO
/// </summary>
public class TemplateVariableDto
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public object? DefaultValue { get; set; }
    public List<string>? AllowedValues { get; set; }
}
