using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for ConversationThread entity
/// </summary>
public class ConversationRepository : IConversationRepository
{
    private readonly CommunicationDbContext _context;

    public ConversationRepository(CommunicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Get conversation thread by ID
    /// </summary>
    public async Task<ConversationThread?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ConversationThreads
            .Include(ct => ct.Participants)
            .Include(ct => ct.Messages)
            .FirstOrDefaultAsync(ct => ct.Id == id, cancellationToken);
    }

    /// <summary>
    /// Get conversations for user
    /// </summary>
    public async Task<IEnumerable<ConversationThread>> GetUserConversationsAsync(
        Guid userId, 
        bool includeInactive = false, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads
            .Include(ct => ct.Participants)
            .Where(ct => ct.Participants.Any(p => p.UserId == userId && p.IsActive));

        if (!includeInactive)
        {
            query = query.Where(ct => ct.IsActive);
        }

        return await query
            .OrderByDescending(ct => ct.LastActivity)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get conversations by type
    /// </summary>
    public async Task<IEnumerable<ConversationThread>> GetByTypeAsync(
        ConversationType type, 
        bool includeInactive = false, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads
            .Include(ct => ct.Participants)
            .Where(ct => ct.Type == type);

        if (!includeInactive)
        {
            query = query.Where(ct => ct.IsActive);
        }

        return await query
            .OrderByDescending(ct => ct.LastActivity)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get conversations by related entity
    /// </summary>
    public async Task<IEnumerable<ConversationThread>> GetByRelatedEntityAsync(
        Guid relatedEntityId, 
        string? relatedEntityType = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads
            .Include(ct => ct.Participants)
            .Where(ct => ct.RelatedEntityId == relatedEntityId);

        if (!string.IsNullOrEmpty(relatedEntityType))
        {
            query = query.Where(ct => ct.RelatedEntityType == relatedEntityType);
        }

        return await query
            .OrderByDescending(ct => ct.LastActivity)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get active conversations
    /// </summary>
    public async Task<IEnumerable<ConversationThread>> GetActiveConversationsAsync(
        DateTime? activeSince = null, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var since = activeSince ?? DateTime.UtcNow.AddDays(-7);

        return await _context.ConversationThreads
            .Include(ct => ct.Participants)
            .Where(ct => ct.IsActive && ct.LastActivity >= since)
            .OrderByDescending(ct => ct.LastActivity)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Search conversations
    /// </summary>
    public async Task<IEnumerable<ConversationThread>> SearchAsync(
        string searchTerm, 
        Guid? userId = null, 
        ConversationType? type = null, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads
            .Include(ct => ct.Participants)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(ct => 
                EF.Functions.ILike(ct.Title ?? "", $"%{searchTerm}%") ||
                EF.Functions.ILike(ct.Description ?? "", $"%{searchTerm}%"));
        }

        if (userId.HasValue)
        {
            query = query.Where(ct => ct.Participants.Any(p => p.UserId == userId.Value && p.IsActive));
        }

        if (type.HasValue)
        {
            query = query.Where(ct => ct.Type == type.Value);
        }

        return await query
            .Where(ct => ct.IsActive)
            .OrderByDescending(ct => ct.LastActivity)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get conversation statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync(
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        Guid? userId = null, 
        CancellationToken cancellationToken = default)
    {
        var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var to = toDate ?? DateTime.UtcNow;

        var query = _context.ConversationThreads
            .Where(ct => ct.CreatedAt >= from && ct.CreatedAt <= to);

        if (userId.HasValue)
        {
            query = query.Where(ct => ct.Participants.Any(p => p.UserId == userId.Value));
        }

        var stats = await query
            .GroupBy(ct => 1)
            .Select(g => new
            {
                TotalConversations = g.Count(),
                ActiveConversations = g.Count(ct => ct.IsActive),
                ClosedConversations = g.Count(ct => !ct.IsActive),
                AvgParticipants = g.Average(ct => ct.Participants.Count),
                AvgDurationHours = g.Where(ct => ct.ClosedAt.HasValue)
                    .Average(ct => EF.Functions.DateDiffHour(ct.CreatedAt, ct.ClosedAt!.Value))
            })
            .FirstOrDefaultAsync(cancellationToken);

        var typeStats = await query
            .GroupBy(ct => ct.Type)
            .Select(g => new
            {
                Type = g.Key.ToString(),
                Count = g.Count(),
                ActiveCount = g.Count(ct => ct.IsActive)
            })
            .ToListAsync(cancellationToken);

        return new Dictionary<string, object>
        {
            ["total_conversations"] = stats?.TotalConversations ?? 0,
            ["active_conversations"] = stats?.ActiveConversations ?? 0,
            ["closed_conversations"] = stats?.ClosedConversations ?? 0,
            ["avg_participants"] = Math.Round(stats?.AvgParticipants ?? 0, 2),
            ["avg_duration_hours"] = Math.Round(stats?.AvgDurationHours ?? 0, 2),
            ["type_statistics"] = typeStats.ToDictionary(ts => ts.Type, ts => new
            {
                total = ts.Count,
                active = ts.ActiveCount,
                closed = ts.Count - ts.ActiveCount
            })
        };
    }

    /// <summary>
    /// Add conversation thread
    /// </summary>
    public async Task AddAsync(ConversationThread conversationThread, CancellationToken cancellationToken = default)
    {
        await _context.ConversationThreads.AddAsync(conversationThread, cancellationToken);
    }

    /// <summary>
    /// Update conversation thread
    /// </summary>
    public void Update(ConversationThread conversationThread)
    {
        _context.ConversationThreads.Update(conversationThread);
    }

    /// <summary>
    /// Delete conversation thread
    /// </summary>
    public void Delete(ConversationThread conversationThread)
    {
        _context.ConversationThreads.Remove(conversationThread);
    }

    /// <summary>
    /// Add participant to conversation
    /// </summary>
    public async Task AddParticipantAsync(
        Guid conversationThreadId, 
        Guid userId, 
        ParticipantRole role, 
        bool canWrite = true, 
        bool canRead = true, 
        CancellationToken cancellationToken = default)
    {
        var participant = new ConversationParticipant
        {
            Id = Guid.NewGuid(),
            ConversationThreadId = conversationThreadId,
            UserId = userId,
            Role = role,
            JoinedAt = DateTime.UtcNow,
            CanWrite = canWrite,
            CanRead = canRead,
            IsActive = true,
            NotificationSettings = new Dictionary<string, object>
            {
                ["notify_on_message"] = true,
                ["notify_on_join"] = false
            },
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _context.ConversationParticipants.AddAsync(participant, cancellationToken);
    }

    /// <summary>
    /// Remove participant from conversation
    /// </summary>
    public async Task RemoveParticipantAsync(
        Guid conversationThreadId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        await _context.ConversationParticipants
            .Where(cp => cp.ConversationThreadId == conversationThreadId && cp.UserId == userId)
            .ExecuteUpdateAsync(cp => cp
                .SetProperty(x => x.IsActive, false)
                .SetProperty(x => x.LeftAt, DateTime.UtcNow)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Update participant permissions
    /// </summary>
    public async Task UpdateParticipantPermissionsAsync(
        Guid conversationThreadId, 
        Guid userId, 
        bool? canWrite = null, 
        bool? canRead = null, 
        CancellationToken cancellationToken = default)
    {
        var updateExpression = _context.ConversationParticipants
            .Where(cp => cp.ConversationThreadId == conversationThreadId && cp.UserId == userId);

        if (canWrite.HasValue && canRead.HasValue)
        {
            await updateExpression.ExecuteUpdateAsync(cp => cp
                .SetProperty(x => x.CanWrite, canWrite.Value)
                .SetProperty(x => x.CanRead, canRead.Value)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
        }
        else if (canWrite.HasValue)
        {
            await updateExpression.ExecuteUpdateAsync(cp => cp
                .SetProperty(x => x.CanWrite, canWrite.Value)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
        }
        else if (canRead.HasValue)
        {
            await updateExpression.ExecuteUpdateAsync(cp => cp
                .SetProperty(x => x.CanRead, canRead.Value)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
        }
    }

    /// <summary>
    /// Update last read timestamp for participant
    /// </summary>
    public async Task UpdateLastReadAsync(
        Guid conversationThreadId, 
        Guid userId, 
        DateTime? lastReadAt = null, 
        CancellationToken cancellationToken = default)
    {
        var readTime = lastReadAt ?? DateTime.UtcNow;
        
        await _context.ConversationParticipants
            .Where(cp => cp.ConversationThreadId == conversationThreadId && cp.UserId == userId)
            .ExecuteUpdateAsync(cp => cp
                .SetProperty(x => x.LastReadAt, readTime)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Close conversation
    /// </summary>
    public async Task CloseConversationAsync(
        Guid conversationThreadId, 
        Guid closedByUserId, 
        string? reason = null, 
        CancellationToken cancellationToken = default)
    {
        await _context.ConversationThreads
            .Where(ct => ct.Id == conversationThreadId)
            .ExecuteUpdateAsync(ct => ct
                .SetProperty(x => x.IsActive, false)
                .SetProperty(x => x.ClosedAt, DateTime.UtcNow)
                .SetProperty(x => x.ClosedByUserId, closedByUserId)
                .SetProperty(x => x.CloseReason, reason)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Reopen conversation
    /// </summary>
    public async Task ReopenConversationAsync(
        Guid conversationThreadId, 
        CancellationToken cancellationToken = default)
    {
        await _context.ConversationThreads
            .Where(ct => ct.Id == conversationThreadId)
            .ExecuteUpdateAsync(ct => ct
                .SetProperty(x => x.IsActive, true)
                .SetProperty(x => x.ClosedAt, (DateTime?)null)
                .SetProperty(x => x.ClosedByUserId, (Guid?)null)
                .SetProperty(x => x.CloseReason, (string?)null)
                .SetProperty(x => x.LastActivity, DateTime.UtcNow)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Update last activity
    /// </summary>
    public async Task UpdateLastActivityAsync(
        Guid conversationThreadId, 
        DateTime? lastActivity = null, 
        CancellationToken cancellationToken = default)
    {
        var activityTime = lastActivity ?? DateTime.UtcNow;
        
        await _context.ConversationThreads
            .Where(ct => ct.Id == conversationThreadId)
            .ExecuteUpdateAsync(ct => ct
                .SetProperty(x => x.LastActivity, activityTime)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Check if user is participant
    /// </summary>
    public async Task<bool> IsUserParticipantAsync(
        Guid conversationThreadId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        return await _context.ConversationParticipants
            .AnyAsync(cp => cp.ConversationThreadId == conversationThreadId && 
                           cp.UserId == userId && 
                           cp.IsActive, 
                     cancellationToken);
    }

    /// <summary>
    /// Get participant permissions
    /// </summary>
    public async Task<(bool CanRead, bool CanWrite)> GetParticipantPermissionsAsync(
        Guid conversationThreadId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        var participant = await _context.ConversationParticipants
            .FirstOrDefaultAsync(cp => cp.ConversationThreadId == conversationThreadId && 
                                      cp.UserId == userId && 
                                      cp.IsActive, 
                                cancellationToken);

        return participant != null ? (participant.CanRead, participant.CanWrite) : (false, false);
    }

    /// <summary>
    /// Count conversations
    /// </summary>
    public async Task<int> CountAsync(
        Guid? userId = null, 
        ConversationType? type = null, 
        bool? isActive = null, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads.AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(ct => ct.Participants.Any(p => p.UserId == userId.Value && p.IsActive));
        }

        if (type.HasValue)
        {
            query = query.Where(ct => ct.Type == type.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(ct => ct.IsActive == isActive.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(ct => ct.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(ct => ct.CreatedAt <= toDate.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Check if conversation exists
    /// </summary>
    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ConversationThreads
            .AnyAsync(ct => ct.Id == id, cancellationToken);
    }

    /// <summary>
    /// Archive old conversations
    /// </summary>
    public async Task<int> ArchiveOldConversationsAsync(
        DateTime olderThan, 
        bool onlyInactive = true, 
        int batchSize = 100, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.ConversationThreads
            .Where(ct => ct.LastActivity < olderThan);

        if (onlyInactive)
        {
            query = query.Where(ct => !ct.IsActive);
        }

        var conversations = await query
            .Take(batchSize)
            .ToListAsync(cancellationToken);

        if (!conversations.Any())
        {
            return 0;
        }

        // Archive logic would be implemented here
        // For now, just mark as archived in metadata
        foreach (var conversation in conversations)
        {
            conversation.Metadata["archived"] = "true";
            conversation.Metadata["archived_at"] = DateTime.UtcNow.ToString("O");
            conversation.UpdatedAt = DateTime.UtcNow;
        }

        return conversations.Count;
    }
}
