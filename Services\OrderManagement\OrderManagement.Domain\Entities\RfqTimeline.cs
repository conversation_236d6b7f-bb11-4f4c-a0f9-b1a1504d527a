using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity to track detailed timeline events for RFQ lifecycle
/// </summary>
public class RfqTimeline : BaseEntity
{
    public Guid RfqId { get; private set; }
    public RfqTimelineEventType EventType { get; private set; }
    public string EventDescription { get; private set; } = string.Empty;
    public DateTime EventTimestamp { get; private set; }
    public Guid? ActorId { get; private set; }
    public string? ActorName { get; private set; }
    public string? ActorRole { get; private set; }
    public string? AdditionalData { get; private set; }
    public RfqStatus? PreviousStatus { get; private set; }
    public RfqStatus? NewStatus { get; private set; }

    // Navigation property
    public RFQ RFQ { get; private set; } = null!;

    private RfqTimeline() { } // EF Constructor

    public RfqTimeline(
        Guid rfqId,
        RfqTimelineEventType eventType,
        string eventDescription,
        Guid? actorId = null,
        string? actorName = null,
        string? actorRole = null,
        string? additionalData = null,
        RfqStatus? previousStatus = null,
        RfqStatus? newStatus = null)
    {
        RfqId = rfqId;
        EventType = eventType;
        EventDescription = eventDescription ?? throw new ArgumentNullException(nameof(eventDescription));
        EventTimestamp = DateTime.UtcNow;
        ActorId = actorId;
        ActorName = actorName;
        ActorRole = actorRole;
        AdditionalData = additionalData;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
    }

    public static RfqTimeline CreateEvent(
        Guid rfqId,
        RfqTimelineEventType eventType,
        string eventDescription,
        Guid? actorId = null,
        string? actorName = null,
        string? actorRole = null,
        string? additionalData = null,
        RfqStatus? previousStatus = null,
        RfqStatus? newStatus = null)
    {
        return new RfqTimeline(
            rfqId,
            eventType,
            eventDescription,
            actorId,
            actorName,
            actorRole,
            additionalData,
            previousStatus,
            newStatus);
    }
}
