using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface IPlanTaxConfigurationRepository
    {
        Task<PlanTaxConfiguration?> GetByIdAsync(Guid id);
        Task<List<PlanTaxConfiguration>> GetByPlanIdAsync(Guid planId);
        Task<List<PlanTaxConfiguration>> GetActiveBPlanIdAsync(Guid planId);
        Task<List<PlanTaxConfiguration>> GetByPlanIdAndRegionAsync(Guid planId, string region);
        Task<List<PlanTaxConfiguration>> GetByTaxTypeAsync(TaxType taxType);
        Task<PlanTaxConfiguration?> GetByPlanIdAndTaxTypeAsync(Guid planId, TaxType taxType);
        Task<List<PlanTaxConfiguration>> GetEffectiveConfigurationsAsync(Guid planId, string region, DateTime? date = null);
        Task<PlanTaxConfiguration> AddAsync(PlanTaxConfiguration planTaxConfiguration);
        Task<PlanTaxConfiguration> UpdateAsync(PlanTaxConfiguration planTaxConfiguration);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByPlanIdAndTaxTypeAsync(Guid planId, TaxType taxType);
        Task<List<PlanTaxConfiguration>> GetByRegionAsync(string region);
        Task<List<PlanTaxConfiguration>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10);
        Task<int> GetCountAsync();
        Task<int> GetCountByPlanIdAsync(Guid planId);
    }
}
