using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Text;

namespace Shared.Messaging
{
    public class RabbitMQMessageBroker : IMessageBroker, IDisposable
    {
        private readonly IConnection _connection;
        private readonly IModel _channel;
        private readonly ILogger<RabbitMQMessageBroker> _logger;
        private readonly string _exchangeName = "tli_microservices";

        public RabbitMQMessageBroker(string hostName, ILogger<RabbitMQMessageBroker> logger)
        {
            _logger = logger;
            
            var factory = new ConnectionFactory() { HostName = hostName };
            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();
            
            // Declare the exchange
            _channel.ExchangeDeclare(exchange: _exchangeName, type: ExchangeType.Topic, durable: true);
        }

        public async Task PublishAsync<T>(string routingKey, T message) where T : class
        {
            try
            {
                var json = JsonConvert.SerializeObject(message);
                var body = Encoding.UTF8.GetBytes(json);

                var properties = _channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.MessageId = Guid.NewGuid().ToString();
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());

                _channel.BasicPublish(
                    exchange: _exchangeName,
                    routingKey: routingKey,
                    basicProperties: properties,
                    body: body);

                _logger.LogInformation("Published message to {RoutingKey}: {MessageType}", routingKey, typeof(T).Name);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to publish message to {RoutingKey}", routingKey);
                throw;
            }
        }

        public async Task SubscribeAsync<T>(string queueName, string routingKey, Func<T, Task> handler) where T : class
        {
            await SubscribeAsync<T>(queueName, routingKey, async message =>
            {
                await handler(message);
                return true;
            });
        }

        public async Task SubscribeAsync<T>(string queueName, string routingKey, Func<T, Task<bool>> handler) where T : class
        {
            try
            {
                _channel.QueueDeclare(queue: queueName, durable: true, exclusive: false, autoDelete: false);
                _channel.QueueBind(queue: queueName, exchange: _exchangeName, routingKey: routingKey);

                var consumer = new EventingBasicConsumer(_channel);
                consumer.Received += async (model, ea) =>
                {
                    var body = ea.Body.ToArray();
                    var json = Encoding.UTF8.GetString(body);
                    
                    try
                    {
                        var message = JsonConvert.DeserializeObject<T>(json);
                        if (message != null)
                        {
                            var success = await handler(message);
                            if (success)
                            {
                                _channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                                _logger.LogInformation("Successfully processed message from {QueueName}", queueName);
                            }
                            else
                            {
                                _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: true);
                                _logger.LogWarning("Failed to process message from {QueueName}, requeuing", queueName);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing message from {QueueName}", queueName);
                        _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: false);
                    }
                };

                _channel.BasicConsume(queue: queueName, autoAck: false, consumer: consumer);
                _logger.LogInformation("Started consuming messages from {QueueName} with routing key {RoutingKey}", queueName, routingKey);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to subscribe to {QueueName}", queueName);
                throw;
            }
        }

        public void Dispose()
        {
            _channel?.Close();
            _connection?.Close();
            _channel?.Dispose();
            _connection?.Dispose();
        }
    }
}
