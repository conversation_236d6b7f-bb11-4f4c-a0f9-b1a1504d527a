using MediatR;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Application.Commands.CreateGstConfiguration;

public class CreateGstConfigurationCommand : IRequest<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ServiceCategory ServiceCategory { get; set; }
    public TaxJurisdictionDto Jurisdiction { get; set; } = new();
    public GstRate GstRate { get; set; }
    public TaxRateDto TaxRate { get; set; } = new();
    public CreateMoneyDto MinimumAmount { get; set; } = new();
    public CreateMoneyDto MaximumAmount { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public string? HsnCode { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public string? ReverseChargeConditions { get; set; }
}
