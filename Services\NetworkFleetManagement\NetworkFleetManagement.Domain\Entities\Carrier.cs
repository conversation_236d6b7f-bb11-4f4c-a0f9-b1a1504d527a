using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class Carrier : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string CompanyName { get; private set; } = string.Empty;
    public string ContactPersonName { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string PhoneNumber { get; private set; } = string.Empty;
    public string? BusinessLicenseNumber { get; private set; }
    public string? TaxIdentificationNumber { get; private set; }
    public CarrierStatus Status { get; private set; }
    public OnboardingStatus OnboardingStatus { get; private set; }
    public PerformanceMetrics PerformanceMetrics { get; private set; }
    public Location? BusinessAddress { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public string? Notes { get; private set; }
    public bool IsActive => Status == CarrierStatus.Active;

    // Navigation properties
    private readonly List<Vehicle> _vehicles = new();
    public IReadOnlyCollection<Vehicle> Vehicles => _vehicles.AsReadOnly();

    private readonly List<Driver> _drivers = new();
    public IReadOnlyCollection<Driver> Drivers => _drivers.AsReadOnly();

    private readonly List<CarrierDocument> _documents = new();
    public IReadOnlyCollection<CarrierDocument> Documents => _documents.AsReadOnly();

    private readonly List<BrokerCarrierNetwork> _networkRelationships = new();
    public IReadOnlyCollection<BrokerCarrierNetwork> NetworkRelationships => _networkRelationships.AsReadOnly();

    // Parameterless constructor for EF Core
    private Carrier() 
    {
        PerformanceMetrics = new PerformanceMetrics();
    }

    public Carrier(
        Guid userId,
        string companyName,
        string contactPersonName,
        string email,
        string phoneNumber,
        string? businessLicenseNumber = null,
        string? taxIdentificationNumber = null,
        Location? businessAddress = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(companyName))
            throw new ArgumentException("Company name cannot be empty", nameof(companyName));
        
        if (string.IsNullOrWhiteSpace(contactPersonName))
            throw new ArgumentException("Contact person name cannot be empty", nameof(contactPersonName));
        
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));
        
        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        UserId = userId;
        CompanyName = companyName;
        ContactPersonName = contactPersonName;
        Email = email;
        PhoneNumber = phoneNumber;
        BusinessLicenseNumber = businessLicenseNumber;
        TaxIdentificationNumber = taxIdentificationNumber;
        Status = CarrierStatus.Pending;
        OnboardingStatus = OnboardingStatus.NotStarted;
        PerformanceMetrics = new PerformanceMetrics();
        BusinessAddress = businessAddress;
        Notes = notes;
    }

    public void UpdateStatus(CarrierStatus newStatus, string? reason = null)
    {
        var oldStatus = Status;
        Status = newStatus;
        
        if (newStatus == CarrierStatus.Active && oldStatus != CarrierStatus.Active)
        {
            VerifiedAt = DateTime.UtcNow;
        }
        
        SetUpdatedAt();
        
        // Add domain event for status change
        AddDomainEvent(new CarrierStatusChangedEvent(Id, oldStatus, newStatus, reason));
    }

    public void UpdateOnboardingStatus(OnboardingStatus newStatus)
    {
        OnboardingStatus = newStatus;
        SetUpdatedAt();
    }

    public void UpdatePerformanceMetrics(PerformanceMetrics newMetrics)
    {
        PerformanceMetrics = newMetrics;
        SetUpdatedAt();
    }

    public void AddVehicle(Vehicle vehicle)
    {
        if (vehicle.CarrierId != Id)
            throw new InvalidOperationException("Vehicle must belong to this carrier");
        
        _vehicles.Add(vehicle);
        SetUpdatedAt();
    }

    public void RemoveVehicle(Vehicle vehicle)
    {
        _vehicles.Remove(vehicle);
        SetUpdatedAt();
    }

    public void AddDriver(Driver driver)
    {
        if (driver.CarrierId != Id)
            throw new InvalidOperationException("Driver must belong to this carrier");
        
        _drivers.Add(driver);
        SetUpdatedAt();
    }

    public void RemoveDriver(Driver driver)
    {
        _drivers.Remove(driver);
        SetUpdatedAt();
    }

    public void AddDocument(CarrierDocument document)
    {
        if (document.CarrierId != Id)
            throw new InvalidOperationException("Document must belong to this carrier");
        
        _documents.Add(document);
        SetUpdatedAt();
    }

    public int GetAvailableVehicleCount()
    {
        return _vehicles.Count(v => v.Status == VehicleStatus.Available);
    }

    public int GetAvailableDriverCount()
    {
        return _drivers.Count(d => d.Status == DriverStatus.Available);
    }
}

// Domain Events
public record CarrierStatusChangedEvent(Guid CarrierId, CarrierStatus OldStatus, CarrierStatus NewStatus, string? Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
