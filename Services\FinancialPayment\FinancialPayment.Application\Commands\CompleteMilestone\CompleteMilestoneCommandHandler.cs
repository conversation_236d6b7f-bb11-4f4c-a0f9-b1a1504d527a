using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using FinancialPayment.Domain.Repositories;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Application.Services;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Application.Interfaces;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CompleteMilestone;

public class CompleteMilestoneCommandHandler : IRequestHandler<CompleteMilestoneCommand, bool>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IEscrowService _escrowService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Shared.Messaging.IMessageBroker _messageBroker;
    private readonly ILogger<CompleteMilestoneCommandHandler> _logger;

    public CompleteMilestoneCommandHandler(
        IEscrowAccountRepository escrowAccountRepository,
        IEscrowService escrowService,
        IUnitOfWork unitOfWork,
        Shared.Messaging.IMessageBroker messageBroker,
        ILogger<CompleteMilestoneCommandHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _escrowService = escrowService;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(CompleteMilestoneCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Completing milestone {MilestoneId} for escrow account {EscrowAccountId}",
            request.MilestoneId, request.EscrowAccountId);

        try
        {
            // Get escrow account with milestones
            var escrowAccount = await _escrowAccountRepository.GetByIdWithMilestonesAsync(request.EscrowAccountId);
            if (escrowAccount == null)
            {
                _logger.LogWarning("Escrow account {EscrowAccountId} not found", request.EscrowAccountId);
                throw new ArgumentException($"Escrow account {request.EscrowAccountId} not found");
            }

            // Find the milestone
            var milestone = escrowAccount.Milestones.FirstOrDefault(m => m.Id == request.MilestoneId);
            if (milestone == null)
            {
                _logger.LogWarning("Milestone {MilestoneId} not found in escrow account {EscrowAccountId}",
                    request.MilestoneId, request.EscrowAccountId);
                throw new ArgumentException($"Milestone {request.MilestoneId} not found");
            }

            // Validate milestone can be completed
            if (milestone.Status != EscrowMilestoneStatus.Pending)
            {
                _logger.LogWarning("Milestone {MilestoneId} cannot be completed. Current status: {Status}",
                    request.MilestoneId, milestone.Status);
                throw new InvalidOperationException($"Milestone cannot be completed. Current status: {milestone.Status}");
            }

            // Verify milestone completion requirements
            var verificationResult = await VerifyMilestoneCompletionAsync(request.Verifications);
            if (!verificationResult.IsValid)
            {
                _logger.LogWarning("Milestone {MilestoneId} verification failed: {Errors}",
                    request.MilestoneId, string.Join(", ", verificationResult.Errors));
                throw new InvalidOperationException($"Milestone verification failed: {string.Join(", ", verificationResult.Errors)}");
            }

            // Complete the milestone
            escrowAccount.CompleteMilestone(request.MilestoneId, request.CompletionNotes);

            // Auto-release funds if requested and conditions are met
            if (request.AutoReleaseFunds && await ShouldAutoReleaseFundsAsync(escrowAccount, milestone))
            {
                await _escrowService.ReleaseEscrowFundsAsync(
                    request.EscrowAccountId,
                    milestone.Amount,
                    escrowAccount.OrderId, // Assuming OrderId is the recipient for now
                    $"Automatic release for completed milestone: {milestone.Description}");
            }

            // Save changes
            await _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync();

            // Publish integration events
            await _messageBroker.PublishAsync("milestone.completed", new
            {
                MilestoneId = request.MilestoneId,
                EscrowAccountId = request.EscrowAccountId,
                OrderId = escrowAccount.OrderId,
                Amount = milestone.Amount.Amount,
                Currency = milestone.Amount.Currency,
                CompletedBy = request.CompletedBy,
                CompletionDate = request.CompletionDate ?? DateTime.UtcNow,
                CompletionNotes = request.CompletionNotes,
                AutoReleaseFunds = request.AutoReleaseFunds,
                Verifications = request.Verifications
            });

            // Send notification
            await _messageBroker.PublishAsync("communication.send.notification", new
            {
                MessageType = "MilestoneCompleted",
                Subject = $"Milestone Completed: {milestone.Description}",
                Content = $"Milestone '{milestone.Description}' has been completed for amount {milestone.Amount.Amount} {milestone.Amount.Currency}",
                Priority = "Medium",
                RelatedEntityId = request.MilestoneId,
                RelatedEntityType = "Milestone",
                Recipients = new[]
                {
                    new { UserId = escrowAccount.OrderId, UserType = "Order" } // This should be mapped to actual users
                },
                Tags = new[] { "milestone", "payment", "completion" },
                Metadata = new
                {
                    MilestoneId = request.MilestoneId,
                    EscrowAccountId = request.EscrowAccountId,
                    Amount = milestone.Amount.Amount,
                    Currency = milestone.Amount.Currency,
                    CompletedBy = request.CompletedBy
                }
            });

            _logger.LogInformation("Successfully completed milestone {MilestoneId}", request.MilestoneId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing milestone {MilestoneId}", request.MilestoneId);
            throw;
        }
    }

    private async Task<MilestoneVerificationResult> VerifyMilestoneCompletionAsync(
        List<MilestoneVerificationDto> verifications)
    {
        var result = new MilestoneVerificationResult { IsValid = true, Errors = new List<string>() };

        foreach (var verification in verifications)
        {
            switch (verification.VerificationType.ToLower())
            {
                case "tripcompleted":
                    if (!verification.IsVerified)
                    {
                        result.IsValid = false;
                        result.Errors.Add("Trip completion verification failed");
                    }
                    break;

                case "documentuploaded":
                    if (!verification.IsVerified)
                    {
                        result.IsValid = false;
                        result.Errors.Add("Document upload verification failed");
                    }
                    break;

                case "manualapproval":
                    if (!verification.IsVerified || !verification.VerifiedBy.HasValue)
                    {
                        result.IsValid = false;
                        result.Errors.Add("Manual approval verification failed");
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown verification type: {VerificationType}", verification.VerificationType);
                    break;
            }
        }

        return result;
    }

    private async Task<bool> ShouldAutoReleaseFundsAsync(
        Domain.Entities.EscrowAccount escrowAccount,
        Domain.Entities.EscrowMilestone milestone)
    {
        // Check if escrow account is properly funded
        if (escrowAccount.Status != EscrowStatus.Funded)
        {
            return false;
        }

        // Check if there are sufficient funds
        if (escrowAccount.AvailableAmount.Amount < milestone.Amount.Amount)
        {
            return false;
        }

        // Add any additional business rules for auto-release
        return true;
    }
}

public class MilestoneVerificationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}
