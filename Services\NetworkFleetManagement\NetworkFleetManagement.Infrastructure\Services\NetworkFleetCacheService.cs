using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Entities;
using Shared.Infrastructure.Caching;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.Services;

public class NetworkFleetCacheService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<NetworkFleetCacheService> _logger;

    // Cache key prefixes
    private const string VEHICLE_PREFIX = "nfm:vehicle:";
    private const string DRIVER_PREFIX = "nfm:driver:";
    private const string CARRIER_PREFIX = "nfm:carrier:";
    private const string NETWORK_PREFIX = "nfm:network:";
    private const string LOCATION_PREFIX = "nfm:location:";
    private const string STATUS_PREFIX = "nfm:status:";
    private const string PERFORMANCE_PREFIX = "nfm:performance:";
    private const string MAINTENANCE_PREFIX = "nfm:maintenance:";
    private const string DOCUMENT_PREFIX = "nfm:document:";

    // Cache expiration times
    private static readonly TimeSpan DefaultExpiration = TimeSpan.FromMinutes(15);
    private static readonly TimeSpan LocationExpiration = TimeSpan.FromMinutes(2);
    private static readonly TimeSpan StatusExpiration = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan PerformanceExpiration = TimeSpan.FromMinutes(10);
    private static readonly TimeSpan LongTermExpiration = TimeSpan.FromHours(1);

    public NetworkFleetCacheService(ICacheService cacheService, ILogger<NetworkFleetCacheService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    // Vehicle Caching
    public async Task<Vehicle?> GetVehicleAsync(Guid vehicleId)
    {
        try
        {
            var key = $"{VEHICLE_PREFIX}{vehicleId}";
            return await _cacheService.GetAsync<Vehicle>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle {VehicleId} from cache", vehicleId);
            return null;
        }
    }

    public async Task SetVehicleAsync(Vehicle vehicle, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{VEHICLE_PREFIX}{vehicle.Id}";
            await _cacheService.SetAsync(key, vehicle, expiration ?? DefaultExpiration);
            _logger.LogDebug("Cached vehicle {VehicleId}", vehicle.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching vehicle {VehicleId}", vehicle.Id);
        }
    }

    public async Task RemoveVehicleAsync(Guid vehicleId)
    {
        try
        {
            var key = $"{VEHICLE_PREFIX}{vehicleId}";
            await _cacheService.RemoveAsync(key);
            _logger.LogDebug("Removed vehicle {VehicleId} from cache", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing vehicle {VehicleId} from cache", vehicleId);
        }
    }

    // Vehicle Location Caching
    public async Task<Dictionary<string, object>?> GetVehicleLocationAsync(Guid vehicleId)
    {
        try
        {
            var key = $"{LOCATION_PREFIX}vehicle:{vehicleId}";
            return await _cacheService.GetAsync<Dictionary<string, object>>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle location {VehicleId} from cache", vehicleId);
            return null;
        }
    }

    public async Task SetVehicleLocationAsync(Guid vehicleId, double latitude, double longitude, DateTime timestamp, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var key = $"{LOCATION_PREFIX}vehicle:{vehicleId}";
            var locationData = new Dictionary<string, object>
            {
                ["vehicleId"] = vehicleId,
                ["latitude"] = latitude,
                ["longitude"] = longitude,
                ["timestamp"] = timestamp,
                ["metadata"] = metadata ?? new Dictionary<string, object>()
            };

            await _cacheService.SetAsync(key, locationData, LocationExpiration);
            _logger.LogDebug("Cached vehicle location for {VehicleId}", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching vehicle location for {VehicleId}", vehicleId);
        }
    }

    // Driver Caching
    public async Task<Driver?> GetDriverAsync(Guid driverId)
    {
        try
        {
            var key = $"{DRIVER_PREFIX}{driverId}";
            return await _cacheService.GetAsync<Driver>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver {DriverId} from cache", driverId);
            return null;
        }
    }

    public async Task SetDriverAsync(Driver driver, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{DRIVER_PREFIX}{driver.Id}";
            await _cacheService.SetAsync(key, driver, expiration ?? DefaultExpiration);
            _logger.LogDebug("Cached driver {DriverId}", driver.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching driver {DriverId}", driver.Id);
        }
    }

    public async Task RemoveDriverAsync(Guid driverId)
    {
        try
        {
            var key = $"{DRIVER_PREFIX}{driverId}";
            await _cacheService.RemoveAsync(key);
            _logger.LogDebug("Removed driver {DriverId} from cache", driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing driver {DriverId} from cache", driverId);
        }
    }

    // Driver Status Caching
    public async Task<Dictionary<string, object>?> GetDriverStatusAsync(Guid driverId)
    {
        try
        {
            var key = $"{STATUS_PREFIX}driver:{driverId}";
            return await _cacheService.GetAsync<Dictionary<string, object>>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting driver status {DriverId} from cache", driverId);
            return null;
        }
    }

    public async Task SetDriverStatusAsync(Guid driverId, string status, DateTime timestamp, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var key = $"{STATUS_PREFIX}driver:{driverId}";
            var statusData = new Dictionary<string, object>
            {
                ["driverId"] = driverId,
                ["status"] = status,
                ["timestamp"] = timestamp,
                ["metadata"] = metadata ?? new Dictionary<string, object>()
            };

            await _cacheService.SetAsync(key, statusData, StatusExpiration);
            _logger.LogDebug("Cached driver status for {DriverId}", driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching driver status for {DriverId}", driverId);
        }
    }

    // Carrier Caching
    public async Task<Carrier?> GetCarrierAsync(Guid carrierId)
    {
        try
        {
            var key = $"{CARRIER_PREFIX}{carrierId}";
            return await _cacheService.GetAsync<Carrier>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier {CarrierId} from cache", carrierId);
            return null;
        }
    }

    public async Task SetCarrierAsync(Carrier carrier, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{CARRIER_PREFIX}{carrier.Id}";
            await _cacheService.SetAsync(key, carrier, expiration ?? DefaultExpiration);
            _logger.LogDebug("Cached carrier {CarrierId}", carrier.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching carrier {CarrierId}", carrier.Id);
        }
    }

    public async Task RemoveCarrierAsync(Guid carrierId)
    {
        try
        {
            var key = $"{CARRIER_PREFIX}{carrierId}";
            await _cacheService.RemoveAsync(key);
            _logger.LogDebug("Removed carrier {CarrierId} from cache", carrierId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing carrier {CarrierId} from cache", carrierId);
        }
    }

    // Network Performance Caching
    public async Task<Dictionary<string, object>?> GetNetworkPerformanceAsync(Guid networkId)
    {
        try
        {
            var key = $"{PERFORMANCE_PREFIX}network:{networkId}";
            return await _cacheService.GetAsync<Dictionary<string, object>>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting network performance {NetworkId} from cache", networkId);
            return null;
        }
    }

    public async Task SetNetworkPerformanceAsync(Guid networkId, Dictionary<string, object> performanceData, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{PERFORMANCE_PREFIX}network:{networkId}";
            await _cacheService.SetAsync(key, performanceData, expiration ?? PerformanceExpiration);
            _logger.LogDebug("Cached network performance for {NetworkId}", networkId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching network performance for {NetworkId}", networkId);
        }
    }

    // Maintenance Caching
    public async Task<List<Dictionary<string, object>>?> GetVehicleMaintenanceAsync(Guid vehicleId)
    {
        try
        {
            var key = $"{MAINTENANCE_PREFIX}vehicle:{vehicleId}";
            return await _cacheService.GetAsync<List<Dictionary<string, object>>>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle maintenance {VehicleId} from cache", vehicleId);
            return null;
        }
    }

    public async Task SetVehicleMaintenanceAsync(Guid vehicleId, List<Dictionary<string, object>> maintenanceData, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{MAINTENANCE_PREFIX}vehicle:{vehicleId}";
            await _cacheService.SetAsync(key, maintenanceData, expiration ?? LongTermExpiration);
            _logger.LogDebug("Cached vehicle maintenance for {VehicleId}", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching vehicle maintenance for {VehicleId}", vehicleId);
        }
    }

    // Document Status Caching
    public async Task<Dictionary<string, object>?> GetDocumentStatusAsync(Guid entityId, string entityType)
    {
        try
        {
            var key = $"{DOCUMENT_PREFIX}{entityType}:{entityId}";
            return await _cacheService.GetAsync<Dictionary<string, object>>(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document status {EntityType}:{EntityId} from cache", entityType, entityId);
            return null;
        }
    }

    public async Task SetDocumentStatusAsync(Guid entityId, string entityType, Dictionary<string, object> documentStatus, TimeSpan? expiration = null)
    {
        try
        {
            var key = $"{DOCUMENT_PREFIX}{entityType}:{entityId}";
            await _cacheService.SetAsync(key, documentStatus, expiration ?? DefaultExpiration);
            _logger.LogDebug("Cached document status for {EntityType}:{EntityId}", entityType, entityId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching document status for {EntityType}:{EntityId}", entityType, entityId);
        }
    }

    // Bulk Operations
    public async Task SetMultipleVehicleLocationsAsync(Dictionary<Guid, (double lat, double lng, DateTime timestamp)> locations)
    {
        try
        {
            var tasks = locations.Select(kvp =>
                SetVehicleLocationAsync(kvp.Key, kvp.Value.lat, kvp.Value.lng, kvp.Value.timestamp));

            await Task.WhenAll(tasks);
            _logger.LogDebug("Cached {Count} vehicle locations", locations.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching multiple vehicle locations");
        }
    }

    public async Task SetMultipleDriverStatusesAsync(Dictionary<Guid, (string status, DateTime timestamp)> statuses)
    {
        try
        {
            var tasks = statuses.Select(kvp =>
                SetDriverStatusAsync(kvp.Key, kvp.Value.status, kvp.Value.timestamp));

            await Task.WhenAll(tasks);
            _logger.LogDebug("Cached {Count} driver statuses", statuses.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching multiple driver statuses");
        }
    }

    // Cache Invalidation
    public async Task InvalidateCarrierCacheAsync(Guid carrierId)
    {
        try
        {
            var tasks = new List<Task>
            {
                _cacheService.RemoveByPatternAsync($"{CARRIER_PREFIX}{carrierId}*"),
                _cacheService.RemoveByPatternAsync($"{VEHICLE_PREFIX}*carrier:{carrierId}*"),
                _cacheService.RemoveByPatternAsync($"{DRIVER_PREFIX}*carrier:{carrierId}*"),
                _cacheService.RemoveByPatternAsync($"{PERFORMANCE_PREFIX}carrier:{carrierId}*")
            };

            await Task.WhenAll(tasks);
            _logger.LogInformation("Invalidated cache for carrier {CarrierId}", carrierId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating carrier cache for {CarrierId}", carrierId);
        }
    }

    public async Task InvalidateVehicleCacheAsync(Guid vehicleId)
    {
        try
        {
            var tasks = new List<Task>
            {
                RemoveVehicleAsync(vehicleId),
                _cacheService.RemoveAsync($"{LOCATION_PREFIX}vehicle:{vehicleId}"),
                _cacheService.RemoveAsync($"{MAINTENANCE_PREFIX}vehicle:{vehicleId}"),
                _cacheService.RemoveAsync($"{DOCUMENT_PREFIX}Vehicle:{vehicleId}")
            };

            await Task.WhenAll(tasks);
            _logger.LogDebug("Invalidated cache for vehicle {VehicleId}", vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating vehicle cache for {VehicleId}", vehicleId);
        }
    }

    public async Task InvalidateDriverCacheAsync(Guid driverId)
    {
        try
        {
            var tasks = new List<Task>
            {
                RemoveDriverAsync(driverId),
                _cacheService.RemoveAsync($"{STATUS_PREFIX}driver:{driverId}"),
                _cacheService.RemoveAsync($"{DOCUMENT_PREFIX}Driver:{driverId}")
            };

            await Task.WhenAll(tasks);
            _logger.LogDebug("Invalidated cache for driver {DriverId}", driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating driver cache for {DriverId}", driverId);
        }
    }

    // Cache Statistics and Health
    public async Task<Dictionary<string, object>> GetCacheStatisticsAsync()
    {
        try
        {
            var stats = new Dictionary<string, object>
            {
                ["timestamp"] = DateTime.UtcNow,
                ["prefixes"] = new Dictionary<string, object>
                {
                    ["vehicles"] = await GetCacheCountByPatternAsync($"{VEHICLE_PREFIX}*"),
                    ["drivers"] = await GetCacheCountByPatternAsync($"{DRIVER_PREFIX}*"),
                    ["carriers"] = await GetCacheCountByPatternAsync($"{CARRIER_PREFIX}*"),
                    ["locations"] = await GetCacheCountByPatternAsync($"{LOCATION_PREFIX}*"),
                    ["statuses"] = await GetCacheCountByPatternAsync($"{STATUS_PREFIX}*"),
                    ["performance"] = await GetCacheCountByPatternAsync($"{PERFORMANCE_PREFIX}*"),
                    ["maintenance"] = await GetCacheCountByPatternAsync($"{MAINTENANCE_PREFIX}*"),
                    ["documents"] = await GetCacheCountByPatternAsync($"{DOCUMENT_PREFIX}*")
                }
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new Dictionary<string, object>();
        }
    }

    public async Task ClearAllCacheAsync()
    {
        try
        {
            var tasks = new List<Task>
            {
                _cacheService.RemoveByPatternAsync($"{VEHICLE_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{DRIVER_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{CARRIER_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{NETWORK_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{LOCATION_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{STATUS_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{PERFORMANCE_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{MAINTENANCE_PREFIX}*"),
                _cacheService.RemoveByPatternAsync($"{DOCUMENT_PREFIX}*")
            };

            await Task.WhenAll(tasks);
            _logger.LogInformation("Cleared all Network Fleet Management cache");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing all cache");
        }
    }

    // Helper method to get cache count by pattern (would need Redis-specific implementation)
    private async Task<int> GetCacheCountByPatternAsync(string pattern)
    {
        try
        {
            // This would require Redis-specific implementation to count keys by pattern
            // For now, return 0 as placeholder
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache count for pattern {Pattern}", pattern);
            return 0;
        }
    }
}
