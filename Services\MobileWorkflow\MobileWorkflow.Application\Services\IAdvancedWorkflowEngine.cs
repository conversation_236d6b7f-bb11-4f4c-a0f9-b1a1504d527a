using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Services;

public interface IAdvancedWorkflowEngine
{
    Task<WorkflowTemplateDto> CreateTemplateAsync(CreateWorkflowTemplateRequest request, CancellationToken cancellationToken = default);
    Task<WorkflowTemplateDto> CreateWorkflowTemplateAsync(CreateWorkflowTemplateRequest request, CancellationToken cancellationToken = default);
    Task<WorkflowStepDto> AddWorkflowStepAsync(Guid templateId, CreateWorkflowStepRequest request, CancellationToken cancellationToken = default);
    Task<WorkflowTransitionDto> AddWorkflowTransitionAsync(Guid templateId, CreateWorkflowTransitionRequest request, CancellationToken cancellationToken = default);
    Task<WorkflowTemplateDto> UpdateTemplateAsync(Guid templateId, UpdateWorkflowTemplateRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<WorkflowTemplateDto?> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken = default);
    Task<List<WorkflowTemplateDto>> GetTemplatesAsync(string? category = null, CancellationToken cancellationToken = default);
    Task<WorkflowDto> CreateWorkflowFromTemplateAsync(Guid templateId, CreateWorkflowFromTemplateRequest request, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionDto> ExecuteWorkflowAsync(Guid workflowId, ExecuteWorkflowRequest request, CancellationToken cancellationToken = default);
    Task<bool> ValidateWorkflowDefinitionAsync(Dictionary<string, object> definition, CancellationToken cancellationToken = default);
}

// Request DTOs for the service methods
public class CreateWorkflowTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> TemplateDefinition { get; set; } = new();
    public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
    public Dictionary<string, object> ParameterSchema { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public List<string> RequiredParameters { get; set; } = new();
    public List<string> OptionalParameters { get; set; } = new();
    public List<string> RequiredRoles { get; set; } = new();
    public Dictionary<string, object> Prerequisites { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsPublic { get; set; }
    public bool AllowParallelExecution { get; set; }
    public TimeSpan? DefaultTimeout { get; set; }
}

public class CreateWorkflowStepRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string StepType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> InputSchema { get; set; } = new();
    public Dictionary<string, object> OutputSchema { get; set; } = new();
    public List<string> RequiredInputs { get; set; } = new();
    public List<string> OptionalInputs { get; set; } = new();
    public int Order { get; set; }
    public bool IsRequired { get; set; }
    public bool AllowSkip { get; set; }
    public TimeSpan? Timeout { get; set; }
}

public class CreateWorkflowTransitionRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string FromStep { get; set; } = string.Empty;
    public string ToStep { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Guid FromStepId { get; set; }
    public Guid ToStepId { get; set; }
    public Dictionary<string, object> Conditions { get; set; } = new();
    public Dictionary<string, object> Actions { get; set; } = new();
    public string TriggerType { get; set; } = string.Empty;
    public bool IsAutomatic { get; set; }
    public int Priority { get; set; }
}

public class UpdateWorkflowTemplateRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Version { get; set; }
    public Dictionary<string, object>? TemplateDefinition { get; set; }
    public Dictionary<string, object>? DefaultConfiguration { get; set; }
    public List<string>? RequiredParameters { get; set; }
    public List<string>? OptionalParameters { get; set; }
    public bool? IsPublic { get; set; }
}

public class CreateWorkflowFromTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

public class ExecuteWorkflowRequest
{
    public Dictionary<string, object> InputData { get; set; } = new();
    public string TriggeredBy { get; set; } = string.Empty;
    public string TriggerSource { get; set; } = string.Empty;
}
