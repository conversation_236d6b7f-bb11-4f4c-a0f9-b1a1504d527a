using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for CustomComplianceReportTemplate entity
    /// </summary>
    public class CustomComplianceReportTemplateRepository : ICustomComplianceReportTemplateRepository
    {
        private readonly AuditComplianceDbContext _context;
        private readonly ILogger<CustomComplianceReportTemplateRepository> _logger;

        public CustomComplianceReportTemplateRepository(
            AuditComplianceDbContext context,
            ILogger<CustomComplianceReportTemplateRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<CustomComplianceReportTemplate?> GetByIdAsync(Guid id)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .FirstOrDefaultAsync(t => t.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template by ID {Id}", id);
                throw;
            }
        }

        public async Task<CustomComplianceReportTemplate?> GetByNameAsync(string templateName)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .FirstOrDefaultAsync(t => t.TemplateName == templateName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template by name {TemplateName}", templateName);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetAllAsync()
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all templates");
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetActiveTemplatesAsync()
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive)
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active templates");
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetPublicTemplatesAsync()
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsPublic && t.IsActive)
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving public templates");
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetTemplatesByCreatorAsync(Guid createdBy)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.CreatedBy == createdBy)
                    .OrderByDescending(t => t.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving templates by creator {CreatedBy}", createdBy);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetTemplatesByComplianceStandardAsync(ComplianceStandard standard)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.ComplianceStandard == standard && t.IsActive)
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving templates by compliance standard {Standard}", standard);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetTemplatesByTypeAsync(ReportTemplateType templateType)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.TemplateType == templateType && t.IsActive)
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving templates by type {TemplateType}", templateType);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> SearchTemplatesAsync(string searchTerm)
        {
            try
            {
                var term = searchTerm.ToLower();
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.TemplateName.ToLower().Contains(term) ||
                               t.Description.ToLower().Contains(term) ||
                               t.Tags.Any(tag => tag.ToLower().Contains(term)))
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching templates with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetTemplatesByTagsAsync(List<string> tags)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive && tags.Any(tag => t.Tags.Contains(tag)))
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving templates by tags");
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetMostUsedTemplatesAsync(int count = 10)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive)
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving most used templates");
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetRecentlyUsedTemplatesAsync(int days = 30, int count = 10)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-days);
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive && t.LastUsed.HasValue && t.LastUsed.Value >= cutoffDate)
                    .OrderByDescending(t => t.LastUsed)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recently used templates");
                throw;
            }
        }

        public async Task<CustomComplianceReportTemplate> AddAsync(CustomComplianceReportTemplate template)
        {
            try
            {
                _context.CustomComplianceReportTemplates.Add(template);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Template added: {TemplateName}", template.TemplateName);
                return template;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding template {TemplateName}", template.TemplateName);
                throw;
            }
        }

        public async Task UpdateAsync(CustomComplianceReportTemplate template)
        {
            try
            {
                _context.CustomComplianceReportTemplates.Update(template);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Template updated: {TemplateName}", template.TemplateName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating template {TemplateName}", template.TemplateName);
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var template = await GetByIdAsync(id);
                if (template != null)
                {
                    _context.CustomComplianceReportTemplates.Remove(template);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Template deleted: {Id}", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting template {Id}", id);
                throw;
            }
        }

        public async Task<bool> TemplateNameExistsAsync(string templateName, Guid? excludeId = null)
        {
            try
            {
                var query = _context.CustomComplianceReportTemplates
                    .Where(t => t.TemplateName == templateName);

                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking template name existence {TemplateName}", templateName);
                throw;
            }
        }

        public async Task<List<CustomComplianceReportTemplate>> GetAccessibleTemplatesAsync(Guid userId)
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive && 
                               (t.IsPublic || 
                                t.CreatedBy == userId ||
                                t.Permissions.Any(p => p.UserId == userId && p.IsActive)))
                    .OrderBy(t => t.TemplateName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving accessible templates for user {UserId}", userId);
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetTemplateStatisticsAsync()
        {
            try
            {
                var stats = new Dictionary<string, int>();
                
                stats["TotalTemplates"] = await _context.CustomComplianceReportTemplates.CountAsync();
                stats["ActiveTemplates"] = await _context.CustomComplianceReportTemplates.CountAsync(t => t.IsActive);
                stats["PublicTemplates"] = await _context.CustomComplianceReportTemplates.CountAsync(t => t.IsPublic && t.IsActive);
                stats["PrivateTemplates"] = await _context.CustomComplianceReportTemplates.CountAsync(t => !t.IsPublic && t.IsActive);
                stats["TotalUsage"] = await _context.CustomComplianceReportTemplates.SumAsync(t => t.UsageCount);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving template statistics");
                throw;
            }
        }

        public async Task<List<string>> GetAllTagsAsync()
        {
            try
            {
                return await _context.CustomComplianceReportTemplates
                    .Where(t => t.IsActive)
                    .SelectMany(t => t.Tags)
                    .Distinct()
                    .OrderBy(tag => tag)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all tags");
                throw;
            }
        }

        public async Task IncrementUsageCountAsync(Guid templateId)
        {
            try
            {
                var template = await GetByIdAsync(templateId);
                if (template != null)
                {
                    template.RecordUsage();
                    await UpdateAsync(template);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing usage count for template {TemplateId}", templateId);
                throw;
            }
        }
    }
}
