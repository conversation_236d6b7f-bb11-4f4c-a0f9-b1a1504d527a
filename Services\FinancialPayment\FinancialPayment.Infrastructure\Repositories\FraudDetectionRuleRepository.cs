using Microsoft.EntityFrameworkCore;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class FraudDetectionRuleRepository : IFraudDetectionRuleRepository
{
    private readonly FinancialPaymentDbContext _context;

    public FraudDetectionRuleRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<FraudDetectionRule?> GetByIdAsync(Guid id)
    {
        return await _context.FraudDetectionRules
            .Include(fdr => fdr.Conditions)
            .FirstOrDefaultAsync(fdr => fdr.Id == id);
    }

    public async Task<List<FraudDetectionRule>> GetActiveRulesAsync()
    {
        return await _context.FraudDetectionRules
            .Include(fdr => fdr.Conditions)
            .Where(fdr => fdr.IsActive && 
                         fdr.EffectiveFrom <= DateTime.UtcNow &&
                         (fdr.EffectiveTo == null || fdr.EffectiveTo >= DateTime.UtcNow))
            .OrderBy(fdr => fdr.Priority)
            .ToListAsync();
    }

    public async Task<List<FraudDetectionRule>> GetByTypeAsync(FraudRuleType ruleType)
    {
        return await _context.FraudDetectionRules
            .Include(fdr => fdr.Conditions)
            .Where(fdr => fdr.RuleType == ruleType && fdr.IsActive)
            .OrderBy(fdr => fdr.Priority)
            .ToListAsync();
    }

    public async Task AddAsync(FraudDetectionRule rule)
    {
        await _context.FraudDetectionRules.AddAsync(rule);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateAsync(FraudDetectionRule rule)
    {
        _context.FraudDetectionRules.Update(rule);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var rule = await _context.FraudDetectionRules.FindAsync(id);
        if (rule != null)
        {
            _context.FraudDetectionRules.Remove(rule);
            await _context.SaveChangesAsync();
        }
    }
}
