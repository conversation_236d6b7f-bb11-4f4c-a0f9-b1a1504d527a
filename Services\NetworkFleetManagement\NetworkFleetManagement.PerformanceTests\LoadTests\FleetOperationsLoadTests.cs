using NBomber.CSharp;
using NBomber.Http.CSharp;
using NetworkFleetManagement.PerformanceTests.Infrastructure;
using System.Text.Json;
using Xunit.Abstractions;

namespace NetworkFleetManagement.PerformanceTests.LoadTests;

public class FleetOperationsLoadTests : PerformanceTestBase
{
    private readonly ITestOutputHelper _output;

    public FleetOperationsLoadTests(ITestOutputHelper output)
    {
        _output = output;
    }

    [Fact]
    public async Task VehicleLocationUpdates_HighVolume_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 5, vehiclesPerCarrier: 100, driversPerCarrier: 50);
        
        var vehicleIds = await GetVehicleIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for vehicle location updates
        var scenario = Scenario.Create("vehicle_location_updates", async context =>
        {
            var vehicleId = Faker.PickRandom(vehicleIds);
            var locationUpdate = GenerateVehicleLocationUpdate();
            
            var request = Http.CreateRequest("PUT", $"{baseUrl}api/vehicles/{vehicleId}/location")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(locationUpdate)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 50, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria
        var scnStats = stats.AllScenarioStats.First();
        Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromMilliseconds(500), 
            $"Mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 500ms threshold");
        Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 10, 
            "Success rate should be at least 90%");

        _output.WriteLine($"Vehicle Location Updates - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                         $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
    }

    [Fact]
    public async Task DriverStatusUpdates_HighVolume_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 5, vehiclesPerCarrier: 50, driversPerCarrier: 100);
        
        var driverIds = await GetDriverIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for driver status updates
        var scenario = Scenario.Create("driver_status_updates", async context =>
        {
            var driverId = Faker.PickRandom(driverIds);
            var statusUpdate = GenerateDriverStatusUpdate();
            
            var request = Http.CreateRequest("PUT", $"{baseUrl}api/drivers/{driverId}/status")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(statusUpdate)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 80, during: TimeSpan.FromMinutes(2)),
            Simulation.KeepConstant(copies: 40, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria
        var scnStats = stats.AllScenarioStats.First();
        Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromMilliseconds(400), 
            $"Mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 400ms threshold");
        Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 10, 
            "Success rate should be at least 90%");

        _output.WriteLine($"Driver Status Updates - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                         $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
    }

    [Fact]
    public async Task FleetDashboard_ConcurrentUsers_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 10, vehiclesPerCarrier: 50, driversPerCarrier: 30);
        
        var carrierIds = await GetCarrierIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for fleet dashboard access
        var scenario = Scenario.Create("fleet_dashboard_access", async context =>
        {
            var carrierId = Faker.PickRandom(carrierIds);
            
            var request = Http.CreateRequest("GET", $"{baseUrl}api/admin/fleet-overview/{carrierId}")
                .WithHeader("Accept", "application/json");

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 20, during: TimeSpan.FromMinutes(1)),
            Simulation.KeepConstant(copies: 100, during: TimeSpan.FromMinutes(2))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria
        var scnStats = stats.AllScenarioStats.First();
        Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromSeconds(2), 
            $"Mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 2000ms threshold");
        Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 20, 
            "Success rate should be at least 95%");

        _output.WriteLine($"Fleet Dashboard Access - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                         $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
    }

    [Fact]
    public async Task MaintenanceOperations_MixedLoad_ShouldHandleLoad()
    {
        // Arrange
        await SeedTestDataAsync(carrierCount: 5, vehiclesPerCarrier: 100, driversPerCarrier: 50);
        
        var vehicleIds = await GetVehicleIdsAsync();
        var baseUrl = Client.BaseAddress!.ToString();

        // Create scenario for maintenance operations
        var createMaintenanceScenario = Scenario.Create("create_maintenance", async context =>
        {
            var vehicleId = Faker.PickRandom(vehicleIds);
            var maintenanceRecord = GenerateMaintenanceRecord();
            
            var request = Http.CreateRequest("POST", $"{baseUrl}api/vehicles/{vehicleId}/maintenance")
                .WithHeader("Content-Type", "application/json")
                .WithBody(new StringContent(JsonSerializer.Serialize(maintenanceRecord)));

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithWeight(30)
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(3))
        );

        var getMaintenanceScenario = Scenario.Create("get_maintenance", async context =>
        {
            var vehicleId = Faker.PickRandom(vehicleIds);
            
            var request = Http.CreateRequest("GET", $"{baseUrl}api/vehicles/{vehicleId}/maintenance")
                .WithHeader("Accept", "application/json");

            var response = await Http.Send(Client, request);
            return response;
        })
        .WithWeight(70)
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 30, during: TimeSpan.FromMinutes(3))
        );

        // Act & Assert
        var stats = NBomberRunner
            .RegisterScenarios(createMaintenanceScenario, getMaintenanceScenario)
            .WithReportFolder("performance-reports")
            .WithReportFormats(ReportFormat.Html, ReportFormat.Csv)
            .Run();

        // Verify performance criteria for both scenarios
        foreach (var scnStats in stats.AllScenarioStats)
        {
            Assert.True(scnStats.Ok.Request.Mean < TimeSpan.FromSeconds(1), 
                $"Scenario {scnStats.ScenarioName} mean response time {scnStats.Ok.Request.Mean.TotalMilliseconds}ms exceeds 1000ms threshold");
            Assert.True(scnStats.Ok.Request.Count > scnStats.Fail.Request.Count * 10, 
                $"Scenario {scnStats.ScenarioName} success rate should be at least 90%");

            _output.WriteLine($"{scnStats.ScenarioName} - Mean: {scnStats.Ok.Request.Mean.TotalMilliseconds}ms, " +
                             $"Success Rate: {(double)scnStats.Ok.Request.Count / (scnStats.Ok.Request.Count + scnStats.Fail.Request.Count) * 100:F2}%");
        }
    }

    // Helper methods
    private async Task<List<Guid>> GetVehicleIdsAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        return await context.Vehicles.Select(v => v.Id).Take(100).ToListAsync();
    }

    private async Task<List<Guid>> GetDriverIdsAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        return await context.Drivers.Select(d => d.Id).Take(100).ToListAsync();
    }

    private async Task<List<Guid>> GetCarrierIdsAsync()
    {
        using var scope = Factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        return await context.Carriers.Select(c => c.Id).ToListAsync();
    }
}
