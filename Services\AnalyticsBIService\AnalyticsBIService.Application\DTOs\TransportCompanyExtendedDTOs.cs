using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Route optimization analytics data transfer object
/// </summary>
public class RouteOptimizationAnalyticsDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public List<RouteOptimizationDto> OptimizedRoutes { get; set; } = new();
    public decimal TotalDistanceSaved { get; set; }
    public decimal TotalTimeSaved { get; set; }
    public decimal TotalCostSaved { get; set; }
    public decimal FuelSavings { get; set; }
    public decimal EmissionReduction { get; set; }
    public List<OptimizationRecommendationDto> Recommendations { get; set; } = new();
    public DateTime LastOptimized { get; set; }

    // Properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public string RouteType { get; set; } = string.Empty;
    public decimal OverallRouteEfficiency { get; set; }
    public decimal AverageRouteOptimization { get; set; }
    public decimal RouteEfficiencyGap { get; set; }
    public decimal PotentialSavings { get; set; }
    public List<RouteEfficiencyDto> RouteAnalysis { get; set; } = new();
    public List<RouteOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
}

/// <summary>
/// Route optimization data transfer object
/// </summary>


/// <summary>
/// Operational efficiency data transfer object
/// </summary>
public class OperationalEfficiencyDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal OverallEfficiencyScore { get; set; }
    public decimal EfficiencyTrend { get; set; }
    public List<EfficiencyMetricDto> EfficiencyMetrics { get; set; } = new();
    public List<EfficiencyTrendDto> EfficiencyTrends { get; set; } = new();
    public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();
    public ResourceUtilizationDto ResourceUtilization { get; set; } = new();
    public ProcessEfficiencyDto ProcessEfficiency { get; set; } = new();
    public List<ImprovementRecommendationDto> ImprovementRecommendations { get; set; } = new();
    public DateTime LastCalculated { get; set; }
}

/// <summary>
/// Resource utilization data transfer object
/// </summary>
public class ResourceUtilizationDto
{
    public decimal VehicleUtilization { get; set; }
    public decimal DriverUtilization { get; set; }
    public decimal WarehouseUtilization { get; set; }
    public decimal EquipmentUtilization { get; set; }
    public decimal OverallUtilization { get; set; }
    public List<ResourceMetricDto> ResourceMetrics { get; set; } = new();

    // Properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public decimal VehicleUtilizationRate { get; set; }
    public decimal DriverUtilizationRate { get; set; }
    public decimal WarehouseUtilizationRate { get; set; }
    public decimal CapacityUtilizationRate { get; set; }
    public List<UtilizationTrendDto> UtilizationTrends { get; set; } = new();
}

/// <summary>
/// Process efficiency data transfer object
/// </summary>
public class ProcessEfficiencyDto
{
    public decimal OrderProcessingTime { get; set; }
    public decimal LoadingTime { get; set; }
    public decimal TransitTime { get; set; }
    public decimal UnloadingTime { get; set; }
    public decimal DocumentationTime { get; set; }
    public decimal ProcessEfficiencyScore { get; set; }
    public List<BottleneckAnalysisDto> BottleneckAnalysis { get; set; } = new();
}

/// <summary>
/// Improvement recommendation data transfer object
/// </summary>
public class ImprovementRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public decimal ImplementationEffort { get; set; }
    public List<string> ActionItems { get; set; } = new();
    public decimal ExpectedROI { get; set; }
}

/// <summary>
/// Resource metric data transfer object
/// </summary>
public class ResourceMetricDto
{
    public string ResourceType { get; set; } = string.Empty;
    public string ResourceName { get; set; } = string.Empty;
    public decimal UtilizationRate { get; set; }
    public decimal Capacity { get; set; }
    public decimal CurrentUsage { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Bottleneck analysis data transfer object
/// </summary>
public class BottleneckAnalysisDto
{
    public string ProcessName { get; set; } = string.Empty;
    public decimal AverageTime { get; set; }
    public decimal TargetTime { get; set; }
    public decimal EfficiencyGap { get; set; }
    public decimal ImprovementPotential { get; set; }
    public List<string> Recommendations { get; set; } = new();
}

// UtilizationTrendDto is already defined in CarrierExtendedDTOs.cs - using that instead

// EfficiencyMetricDto is already defined in DriverAnalyticsDTOs.cs - using that instead

// EfficiencyTrendDto is already defined in AdminAnalyticsDto.cs - using that instead

// ImprovementAreaDto is already defined in BrokerAnalyticsDTOs.cs - using that instead

/// <summary>
/// Service quality analytics data transfer object
/// </summary>
public class ServiceQualityAnalyticsDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public decimal OverallQualityScore { get; set; }
    public List<QualityMetricDto> QualityMetrics { get; set; } = new();
    public List<CustomerSatisfactionDto> CustomerSatisfaction { get; set; } = new();
    public List<ServiceIssueDto> ServiceIssues { get; set; } = new();
    public List<QualityTrendDto> QualityTrends { get; set; } = new();
    public DateTime LastUpdated { get; set; }

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal QualityTrend { get; set; }
    public List<QualityScoreDto> QualityScores { get; set; } = new();
    public CustomerFeedbackAnalysisDto CustomerFeedback { get; set; } = new();
    public List<QualityImprovementDto> ImprovementRecommendations { get; set; } = new();
}

/// <summary>
/// Quality metric data transfer object
/// </summary>
public class QualityMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal Target { get; set; }
    public decimal Achievement { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Customer satisfaction data transfer object
/// </summary>
public class CustomerSatisfactionDto
{
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public decimal SatisfactionScore { get; set; }
    public List<SatisfactionFactorDto> SatisfactionFactors { get; set; } = new();
    public string Feedback { get; set; } = string.Empty;
    public DateTime LastSurvey { get; set; }
}

/// <summary>
/// Satisfaction factor data transfer object
/// </summary>
public class SatisfactionFactorDto
{
    public string FactorName { get; set; } = string.Empty;
    public decimal Score { get; set; }
    public string Comments { get; set; } = string.Empty;
}

// ServiceIssueDto is already defined in ShipperAnalyticsDTOs.cs - using that instead

/// <summary>
/// Quality trend data transfer object
/// </summary>
public class QualityTrendDto
{
    public DateTime Date { get; set; }
    public decimal QualityScore { get; set; }
    public Dictionary<string, decimal> MetricScores { get; set; } = new();
}

/// <summary>
/// Quality score data transfer object
/// </summary>
public class QualityScoreDto
{
    public string QualityDimension { get; set; } = string.Empty;
    public decimal CurrentScore { get; set; }
    public decimal TargetScore { get; set; }
    public decimal PerformanceGap { get; set; }
    public string Trend { get; set; } = string.Empty;
    public decimal Weight { get; set; }
}

/// <summary>
/// Customer feedback analysis data transfer object
/// </summary>
public class CustomerFeedbackAnalysisDto
{
    public decimal OverallSatisfactionScore { get; set; }
    public int TotalFeedbackReceived { get; set; }
    public decimal PositiveFeedbackPercentage { get; set; }
    public decimal NegativeFeedbackPercentage { get; set; }
    public decimal NeutralFeedbackPercentage { get; set; }
    public List<string> TopPositiveAspects { get; set; } = new();
    public List<string> TopImprovementAreas { get; set; } = new();
    public List<FeedbackTrendDto> FeedbackTrends { get; set; } = new();
}

/// <summary>
/// Feedback trend data transfer object
/// </summary>
public class FeedbackTrendDto
{
    public DateTime Date { get; set; }
    public decimal SatisfactionScore { get; set; }
    public int FeedbackCount { get; set; }
    public decimal PositivePercentage { get; set; }
}

/// <summary>
/// Transport company financial performance data transfer object
/// </summary>
public class TransportCompanyFinancialPerformanceDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal EBITDA { get; set; }
    public decimal ROI { get; set; }
    public decimal ROE { get; set; }
    public List<RevenueStreamDto> RevenueStreams { get; set; } = new();
    public List<ExpenseCategoryDto> ExpenseCategories { get; set; } = new();
    public List<FinancialTrendDto> FinancialTrends { get; set; } = new();

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// Revenue stream data transfer object
/// </summary>
public class RevenueStreamDto
{
    public string StreamName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Expense category data transfer object
/// </summary>
public class ExpenseCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal ChangeFromPrevious { get; set; }
    public string Trend { get; set; } = string.Empty;
}

// FinancialTrendDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Network efficiency analytics data transfer object
/// </summary>
public class NetworkEfficiencyAnalyticsDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public decimal NetworkEfficiencyScore { get; set; }
    public List<NetworkNodeDto> NetworkNodes { get; set; } = new();
    public List<NetworkConnectionDto> NetworkConnections { get; set; } = new();
    public List<BottleneckDto> Bottlenecks { get; set; } = new();
    public List<OptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
    public DateTime LastAnalyzed { get; set; }

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

/// <summary>
/// Network node data transfer object
/// </summary>
public class NetworkNodeDto
{
    public string NodeId { get; set; } = string.Empty;
    public string NodeName { get; set; } = string.Empty;
    public string NodeType { get; set; } = string.Empty; // "Hub", "Terminal", "Warehouse", "Distribution Center"
    public string Location { get; set; } = string.Empty;
    public decimal Capacity { get; set; }
    public decimal Utilization { get; set; }
    public decimal EfficiencyScore { get; set; }
    public List<string> ConnectedNodes { get; set; } = new();
}

/// <summary>
/// Network connection data transfer object
/// </summary>
public class NetworkConnectionDto
{
    public string ConnectionId { get; set; } = string.Empty;
    public string FromNode { get; set; } = string.Empty;
    public string ToNode { get; set; } = string.Empty;
    public decimal Distance { get; set; }
    public decimal AverageTransitTime { get; set; }
    public decimal Capacity { get; set; }
    public decimal Utilization { get; set; }
    public decimal Cost { get; set; }
}

/// <summary>
/// Bottleneck data transfer object
/// </summary>
public class BottleneckDto
{
    public string BottleneckId { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string BottleneckType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public decimal DelayTime { get; set; }
    public List<string> AffectedRoutes { get; set; } = new();
    public List<string> RecommendedSolutions { get; set; } = new();
}

/// <summary>
/// Optimization opportunity data transfer object
/// </summary>
public class OptimizationOpportunityDto
{
    public string OpportunityId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal PotentialBenefit { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> Requirements { get; set; } = new();
}

/// <summary>
/// Transport company compliance analytics data transfer object
/// </summary>
public class TransportCompanyComplianceAnalyticsDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public decimal ComplianceScore { get; set; }
    public List<ComplianceAreaDto> ComplianceAreas { get; set; } = new();
    public List<ComplianceViolationDto> Violations { get; set; } = new();
    public List<ComplianceRiskDto> Risks { get; set; } = new();
    public List<ComplianceTrendDto> ComplianceTrends { get; set; } = new();
    public DateTime LastAudit { get; set; }
    public DateTime NextAudit { get; set; }

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

/// <summary>
/// Compliance area data transfer object
/// </summary>
public class ComplianceAreaDto
{
    public string AreaName { get; set; } = string.Empty;
    public string Regulation { get; set; } = string.Empty;
    public decimal ComplianceLevel { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public List<string> Requirements { get; set; } = new();
}

// ComplianceViolationDto is already defined in AdminAnalyticsDto.cs - using that instead





/// <summary>
/// Business growth analytics data transfer object
/// </summary>
public class BusinessGrowthAnalyticsDto
{
    public string TransportCompanyId { get; set; } = string.Empty;
    public decimal GrowthRate { get; set; }
    public List<GrowthMetricDto> GrowthMetrics { get; set; } = new();
    public List<GrowthOpportunityDto> GrowthOpportunities { get; set; } = new();
    public List<MarketExpansionDto> MarketExpansions { get; set; } = new();
    public List<GrowthTrendDto> GrowthTrends { get; set; } = new();
    public DateTime LastAnalyzed { get; set; }

    // Additional properties required by GetTransportCompanyAdvancedAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
}

// GrowthMetricDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Growth opportunity data transfer object
/// </summary>
public class GrowthOpportunityDto
{
    public string OpportunityId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal PotentialRevenue { get; set; }
    public decimal InvestmentRequired { get; set; }
    public decimal ROI { get; set; }
    public string Timeline { get; set; } = string.Empty;
    public List<string> Requirements { get; set; } = new();
}

/// <summary>
/// Market expansion data transfer object
/// </summary>
public class MarketExpansionDto
{
    public string MarketId { get; set; } = string.Empty;
    public string MarketName { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public decimal MarketSize { get; set; }
    public decimal GrowthPotential { get; set; }
    public string CompetitionLevel { get; set; } = string.Empty;
    public decimal EntryBarriers { get; set; }
    public List<string> KeyRequirements { get; set; } = new();
}

/// <summary>
/// Growth trend data transfer object
/// </summary>
public class GrowthTrendDto
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
    public decimal CustomerCount { get; set; }
    public decimal MarketShare { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Broker performance comparison data transfer object
/// </summary>
public class BrokerPerformanceComparisonDto
{
    public string BrokerId { get; set; } = string.Empty;
    public string BrokerName { get; set; } = string.Empty;
    public decimal PerformanceScore { get; set; }
    public List<PerformanceMetricComparisonDto> MetricComparisons { get; set; } = new();
    public string Ranking { get; set; } = string.Empty;
    public List<string> StrengthAreas { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public DateTime LastUpdated { get; set; }

    // Additional properties required by query handlers
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<BrokerPerformanceDto> BrokerComparisons { get; set; } = new();
}

/// <summary>
/// Performance metric comparison data transfer object
/// </summary>
public class PerformanceMetricComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal BrokerValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal BestInClass { get; set; }
    public decimal PercentileRank { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
}

/// <summary>
/// Broker performance data transfer object
/// </summary>
public class BrokerPerformanceDto
{
    public string BrokerId { get; set; } = string.Empty;
    public string BrokerName { get; set; } = string.Empty;
    public decimal OverallScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal CommunicationScore { get; set; }
    public decimal PaymentScore { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageRating { get; set; }
    public DateTime LastTransaction { get; set; }
    public List<PerformanceHistoryDto> PerformanceHistory { get; set; } = new();

    // Additional properties required by query handlers
    public decimal OverallPerformanceScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal ServiceQualityScore { get; set; }
    public decimal CostEfficiency { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public int TotalShipments { get; set; }
    public decimal MarketShare { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// Performance history data transfer object
/// </summary>
public class PerformanceHistoryDto
{
    public DateTime Date { get; set; }
    public decimal Score { get; set; }
    public int TransactionCount { get; set; }
    public string Notes { get; set; } = string.Empty;
}
