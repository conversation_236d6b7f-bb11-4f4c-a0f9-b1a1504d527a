using Shared.Domain.Common;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// Tracks user acceptance of policy documents for compliance purposes
/// </summary>
public class PolicyAcceptance : BaseEntity
{
    public Guid PolicyId { get; private set; }
    public Guid UserId { get; private set; }
    public string UserName { get; private set; }
    public string UserRole { get; private set; }
    public string Version { get; private set; }
    public DateTime AcceptedAt { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }

    private PolicyAcceptance()
    {
        UserName = string.Empty;
        UserRole = string.Empty;
        Version = string.Empty;
    }

    public PolicyAcceptance(
        Guid policyId,
        Guid userId,
        string userName,
        string userRole,
        string version,
        string? ipAddress = null,
        string? userAgent = null)
    {
        if (string.IsNullOrWhiteSpace(userName))
            throw new ArgumentException("User name cannot be empty", nameof(userName));
        if (string.IsNullOrWhiteSpace(userRole))
            throw new ArgumentException("User role cannot be empty", nameof(userRole));
        if (string.IsNullOrWhiteSpace(version))
            throw new ArgumentException("Version cannot be empty", nameof(version));

        Id = Guid.NewGuid();
        PolicyId = policyId;
        UserId = userId;
        UserName = userName;
        UserRole = userRole;
        Version = version;
        AcceptedAt = DateTime.UtcNow;
        IpAddress = ipAddress;
        UserAgent = userAgent;
    }
}


