-- Migration: Add Grace Period Extension Support
-- Description: Adds columns to support subscription grace period extensions
-- Date: 2025-06-24

BEGIN;

-- Add new columns to Subscriptions table
ALTER TABLE subscription."Subscriptions" 
ADD COLUMN "GracePeriodEndDate" TIMESTAMP NULL,
ADD COLUMN "LastExtendedAt" TIMESTAMP NULL,
ADD COLUMN "ExtensionReason" VARCHAR(500) NULL,
ADD COLUMN "ExtendedByUserId" UUID NULL;

-- Add indexes for the new columns
CREATE INDEX "IX_Subscriptions_GracePeriodEndDate" ON subscription."Subscriptions"("GracePeriodEndDate");
CREATE INDEX "IX_Subscriptions_LastExtendedAt" ON subscription."Subscriptions"("LastExtendedAt");
CREATE INDEX "IX_Subscriptions_ExtendedByUserId" ON subscription."Subscriptions"("ExtendedByUserId");

-- Add comments for documentation
COMMENT ON COLUMN subscription."Subscriptions"."GracePeriodEndDate" IS 'End date of grace period for expired subscriptions';
COMMENT ON COLUMN subscription."Subscriptions"."LastExtendedAt" IS 'Timestamp when subscription was last extended';
COMMENT ON COLUMN subscription."Subscriptions"."ExtensionReason" IS 'Reason provided for the subscription extension';
COMMENT ON COLUMN subscription."Subscriptions"."ExtendedByUserId" IS 'ID of the user who performed the extension (admin)';

COMMIT;
