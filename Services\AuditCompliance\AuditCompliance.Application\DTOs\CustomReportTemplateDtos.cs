using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Application.DTOs
{
    /// <summary>
    /// Custom compliance report template DTO
    /// </summary>
    public class CustomComplianceReportTemplateDto
    {
        public Guid Id { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ComplianceStandard ComplianceStandard { get; set; }
        public ReportTemplateType TemplateType { get; set; }
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public ReportScheduleDto? Schedule { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool IsPublic { get; set; }
        public bool IsActive { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastUsed { get; set; }
        public int UsageCount { get; set; }
        public List<TemplatePermissionDto> Permissions { get; set; } = new();
        public string Version { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Report section DTO
    /// </summary>
    public class ReportSectionDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Order { get; set; }
        public bool IsRequired { get; set; }
        public string Template { get; set; } = string.Empty;
        public List<string> DataSources { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public SectionType Type { get; set; }
    }

    /// <summary>
    /// Report parameter DTO
    /// </summary>
    public class ReportParameterDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ParameterType Type { get; set; }
        public bool IsRequired { get; set; }
        public object? DefaultValue { get; set; }
        public List<ParameterOptionDto> Options { get; set; } = new();
        public Dictionary<string, object> Validation { get; set; } = new();
    }

    /// <summary>
    /// Parameter option DTO
    /// </summary>
    public class ParameterOptionDto
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
    }

    /// <summary>
    /// Report schedule DTO
    /// </summary>
    public class ReportScheduleDto
    {
        public string CronExpression { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public List<string> Recipients { get; set; } = new();
        public ScheduleFrequency Frequency { get; set; }
        public Dictionary<string, object> Settings { get; set; } = new();
    }

    /// <summary>
    /// Template permission DTO
    /// </summary>
    public class TemplatePermissionDto
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public TemplatePermissionType PermissionType { get; set; }
        public DateTime GrantedAt { get; set; }
        public Guid GrantedBy { get; set; }
        public string GrantedByName { get; set; } = string.Empty;
        public DateTime? ExpiresAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime? RevokedAt { get; set; }
        public Guid? RevokedBy { get; set; }
        public string? RevokedByName { get; set; }
    }

    /// <summary>
    /// Create custom report template request DTO
    /// </summary>
    public class CreateCustomReportTemplateRequestDto
    {
        public string TemplateName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ComplianceStandard ComplianceStandard { get; set; }
        public ReportTemplateType TemplateType { get; set; }
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public ReportScheduleDto? Schedule { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool IsPublic { get; set; } = false;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Update custom report template request DTO
    /// </summary>
    public class UpdateCustomReportTemplateRequestDto
    {
        public string TemplateName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public ReportScheduleDto? Schedule { get; set; }
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Generate report from template request DTO
    /// </summary>
    public class GenerateReportFromTemplateRequestDto
    {
        public Guid TemplateId { get; set; }
        public Dictionary<string, object> ParameterValues { get; set; } = new();
        public ExportFormat OutputFormat { get; set; } = ExportFormat.PDF;
        public bool IncludeVisualizations { get; set; } = true;
        public bool IncludeInsights { get; set; } = true;
        public bool SaveResults { get; set; } = true;
        public string? ResultsName { get; set; }
        public List<string> Recipients { get; set; } = new();
        public bool ScheduleReport { get; set; } = false;
        public string? ScheduleExpression { get; set; }
    }

    /// <summary>
    /// Template search request DTO
    /// </summary>
    public class TemplateSearchRequestDto
    {
        public string? SearchTerm { get; set; }
        public ComplianceStandard? ComplianceStandard { get; set; }
        public ReportTemplateType? TemplateType { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool? IsPublic { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// Template search result DTO
    /// </summary>
    public class TemplateSearchResultDto
    {
        public List<CustomComplianceReportTemplateDto> Templates { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
        public Dictionary<string, int> TagCounts { get; set; } = new();
        public Dictionary<ComplianceStandard, int> StandardCounts { get; set; } = new();
        public Dictionary<ReportTemplateType, int> TypeCounts { get; set; } = new();
    }

    /// <summary>
    /// Grant template permission request DTO
    /// </summary>
    public class GrantTemplatePermissionRequestDto
    {
        public Guid UserId { get; set; }
        public TemplatePermissionType PermissionType { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Template validation result DTO
    /// </summary>
    public class TemplateValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<ValidationErrorDto> Errors { get; set; } = new();
        public List<ValidationWarningDto> Warnings { get; set; } = new();
        public Dictionary<string, object> ValidationMetadata { get; set; } = new();
    }

    /// <summary>
    /// Validation error DTO
    /// </summary>
    public class ValidationErrorDto
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Field { get; set; }
        public string? Section { get; set; }
        public int? LineNumber { get; set; }
    }

    /// <summary>
    /// Validation warning DTO
    /// </summary>
    public class ValidationWarningDto
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Field { get; set; }
        public string? Section { get; set; }
        public string? Suggestion { get; set; }
    }

    /// <summary>
    /// Template usage statistics DTO
    /// </summary>
    public class TemplateUsageStatisticsDto
    {
        public Guid TemplateId { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public int TotalUsage { get; set; }
        public int UniqueUsers { get; set; }
        public DateTime? LastUsed { get; set; }
        public Dictionary<string, int> UsageByMonth { get; set; } = new();
        public Dictionary<string, int> UsageByUser { get; set; } = new();
        public Dictionary<ExportFormat, int> UsageByFormat { get; set; } = new();
        public double AverageExecutionTime { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
    }

    /// <summary>
    /// Template designer configuration DTO
    /// </summary>
    public class TemplateDesignerConfigDto
    {
        public List<string> AvailableDataSources { get; set; } = new();
        public List<SectionTemplateDto> SectionTemplates { get; set; } = new();
        public List<ParameterTypeDto> ParameterTypes { get; set; } = new();
        public Dictionary<string, object> DesignerSettings { get; set; } = new();
    }

    /// <summary>
    /// Section template DTO for designer
    /// </summary>
    public class SectionTemplateDto
    {
        public SectionType Type { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Template { get; set; } = string.Empty;
        public List<string> RequiredDataSources { get; set; } = new();
        public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
    }

    /// <summary>
    /// Parameter type DTO for designer
    /// </summary>
    public class ParameterTypeDto
    {
        public ParameterType Type { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> DefaultValidation { get; set; } = new();
        public bool SupportsOptions { get; set; }
        public bool SupportsDefaultValue { get; set; }
    }
}
