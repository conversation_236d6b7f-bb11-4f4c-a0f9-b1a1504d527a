using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FraudDetectionController : ControllerBase
{
    private readonly IFraudDetectionService _fraudDetectionService;
    private readonly IBlacklistService _blacklistService;
    private readonly ILogger<FraudDetectionController> _logger;

    public FraudDetectionController(
        IFraudDetectionService fraudDetectionService,
        IBlacklistService blacklistService,
        ILogger<FraudDetectionController> logger)
    {
        _fraudDetectionService = fraudDetectionService;
        _blacklistService = blacklistService;
        _logger = logger;
    }

    /// <summary>
    /// Assess a transaction for fraud risk
    /// </summary>
    [HttpPost("assess")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<FraudAssessment>> AssessTransaction([FromBody] AssessTransactionRequest request)
    {
        try
        {
            var assessmentRequest = new FraudAssessmentRequest
            {
                TransactionId = request.TransactionId,
                UserId = request.UserId,
                TransactionAmount = new Money(request.Amount, request.Currency),
                PaymentMethod = request.PaymentMethod,
                IpAddress = request.IpAddress,
                DeviceFingerprint = request.DeviceFingerprint,
                UserAgent = request.UserAgent,
                BillingAddress = request.BillingAddress,
                ShippingAddress = request.ShippingAddress,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                AdditionalData = request.AdditionalData ?? new Dictionary<string, object>()
            };

            var assessment = await _fraudDetectionService.AssessTransactionAsync(assessmentRequest);
            
            _logger.LogInformation("Fraud assessment completed for transaction {TransactionId}. Risk Level: {RiskLevel}",
                request.TransactionId, assessment.RiskLevel);

            return Ok(assessment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing transaction {TransactionId}", request.TransactionId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get high-risk transactions
    /// </summary>
    [HttpGet("high-risk")]
    [Authorize(Roles = "Admin,Security,Finance")]
    public async Task<ActionResult<List<FraudAssessment>>> GetHighRiskTransactions(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var transactions = await _fraudDetectionService.GetHighRiskTransactionsAsync(fromDate, toDate);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting high-risk transactions");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get pending fraud reviews
    /// </summary>
    [HttpGet("pending-reviews")]
    [Authorize(Roles = "Admin,Security,Finance")]
    public async Task<ActionResult<List<FraudAssessment>>> GetPendingReviews()
    {
        try
        {
            var pendingReviews = await _fraudDetectionService.GetPendingReviewsAsync();
            return Ok(pendingReviews);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending reviews");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Review a fraud assessment
    /// </summary>
    [HttpPost("{assessmentId}/review")]
    [Authorize(Roles = "Admin,Security")]
    public async Task<ActionResult<bool>> ReviewAssessment(
        Guid assessmentId,
        [FromBody] ReviewAssessmentRequest request)
    {
        try
        {
            var result = await _fraudDetectionService.ReviewAssessmentAsync(
                assessmentId,
                request.ReviewedBy,
                request.ReviewNotes,
                request.FinalAction);

            if (result)
            {
                _logger.LogInformation("Fraud assessment {AssessmentId} reviewed by {ReviewedBy}",
                    assessmentId, request.ReviewedBy);
                return Ok(result);
            }
            else
            {
                return BadRequest("Failed to review assessment");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reviewing assessment {AssessmentId}", assessmentId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get fraud detection statistics
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Security,Finance")]
    public async Task<ActionResult<FraudDetectionStatistics>> GetStatistics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var statistics = await _fraudDetectionService.GetStatisticsAsync(fromDate, toDate);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting fraud detection statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get active fraud detection rules
    /// </summary>
    [HttpGet("rules")]
    [Authorize(Roles = "Admin,Security")]
    public async Task<ActionResult<List<FraudDetectionRule>>> GetActiveRules()
    {
        try
        {
            var rules = await _fraudDetectionService.GetActiveRulesAsync();
            return Ok(rules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active fraud rules");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update a fraud detection rule
    /// </summary>
    [HttpPut("rules/{ruleId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<bool>> UpdateRule(
        Guid ruleId,
        [FromBody] UpdateRuleRequest request)
    {
        try
        {
            var result = await _fraudDetectionService.UpdateRuleAsync(ruleId, request.RiskScore, request.IsActive);
            
            if (result)
            {
                _logger.LogInformation("Fraud rule {RuleId} updated", ruleId);
                return Ok(result);
            }
            else
            {
                return NotFound("Rule not found");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating fraud rule {RuleId}", ruleId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Check if user is blacklisted
    /// </summary>
    [HttpGet("blacklist/user/{userId}")]
    [Authorize(Roles = "Admin,Security")]
    public async Task<ActionResult<bool>> IsUserBlacklisted(Guid userId)
    {
        try
        {
            var isBlacklisted = await _blacklistService.IsUserBlacklistedAsync(userId);
            return Ok(isBlacklisted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user blacklist for {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Add entry to blacklist
    /// </summary>
    [HttpPost("blacklist")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> AddToBlacklist([FromBody] AddToBlacklistRequest request)
    {
        try
        {
            var entry = new BlacklistEntry
            {
                Id = Guid.NewGuid(),
                Type = request.Type,
                Value = request.Value,
                Reason = request.Reason,
                AddedAt = DateTime.UtcNow,
                AddedBy = request.AddedBy,
                ExpiresAt = request.ExpiresAt,
                IsActive = true
            };

            await _blacklistService.AddToBlacklistAsync(entry);
            
            _logger.LogInformation("Added {Type} {Value} to blacklist", request.Type, request.Value);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding to blacklist: {Type} {Value}", request.Type, request.Value);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Remove entry from blacklist
    /// </summary>
    [HttpDelete("blacklist/{entryId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> RemoveFromBlacklist(Guid entryId)
    {
        try
        {
            await _blacklistService.RemoveFromBlacklistAsync(entryId);
            
            _logger.LogInformation("Removed blacklist entry {EntryId}", entryId);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing blacklist entry {EntryId}", entryId);
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class AssessTransactionRequest
{
    public Guid TransactionId { get; set; }
    public Guid? UserId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public string PaymentMethod { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string? DeviceFingerprint { get; set; }
    public string? UserAgent { get; set; }
    public string? BillingAddress { get; set; }
    public string? ShippingAddress { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class ReviewAssessmentRequest
{
    public Guid ReviewedBy { get; set; }
    public string ReviewNotes { get; set; } = string.Empty;
    public FraudAction FinalAction { get; set; }
}

public class UpdateRuleRequest
{
    public int RiskScore { get; set; }
    public bool IsActive { get; set; }
}

public class AddToBlacklistRequest
{
    public BlacklistType Type { get; set; }
    public string Value { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public Guid AddedBy { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
