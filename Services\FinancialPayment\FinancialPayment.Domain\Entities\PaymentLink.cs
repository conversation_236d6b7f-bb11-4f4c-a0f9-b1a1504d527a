using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.Events;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Exceptions;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Represents a secure payment link for Transport Company Portal
/// </summary>
public class PaymentLink : AggregateRoot
{
    public Guid TransportCompanyId { get; private set; }
    public Guid ShipperId { get; private set; }
    public Guid? OrderId { get; private set; }
    public Guid? InvoiceId { get; private set; }
    public string LinkToken { get; private set; } = string.Empty;
    public string PublicUrl { get; private set; } = string.Empty;
    public Money Amount { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public PaymentLinkStatus Status { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public DateTime? PaidAt { get; private set; }
    public string? PaymentTransactionId { get; private set; }
    public PaymentLinkType LinkType { get; private set; }
    public PaymentLinkSettings Settings { get; private set; }
    public ContactDetails ShipperContactDetails { get; private set; }
    public ContactDetails TransportCompanyContactDetails { get; private set; }

    // Security and tracking
    public int AccessCount { get; private set; }
    public DateTime? LastAccessedAt { get; private set; }
    public string? LastAccessedFromIp { get; private set; }
    public bool RequiresAuthentication { get; private set; }
    public List<string> AllowedIpAddresses { get; private set; } = new();

    // Notification settings
    public bool SendEmailNotification { get; private set; }
    public bool SendSmsNotification { get; private set; }
    public List<string> NotificationEmails { get; private set; } = new();
    public List<string> NotificationPhones { get; private set; } = new();

    // Payment tracking
    private readonly List<PaymentLinkAttempt> _paymentAttempts = new();
    public IReadOnlyList<PaymentLinkAttempt> PaymentAttempts => _paymentAttempts.AsReadOnly();

    private readonly List<PaymentLinkAccess> _accessLogs = new();
    public IReadOnlyList<PaymentLinkAccess> AccessLogs => _accessLogs.AsReadOnly();

    private PaymentLink() { }

    public PaymentLink(
        Guid transportCompanyId,
        Guid shipperId,
        Money amount,
        string description,
        PaymentLinkType linkType,
        ContactDetails shipperContactDetails,
        ContactDetails transportCompanyContactDetails,
        PaymentLinkSettings? settings = null,
        Guid? orderId = null,
        Guid? invoiceId = null,
        DateTime? expiresAt = null)
    {
        if (amount == null || amount.Amount <= 0)
            throw new PaymentLinkValidationException("Payment link amount must be greater than zero");

        if (string.IsNullOrWhiteSpace(description))
            throw new PaymentLinkValidationException("Payment link description is required");

        TransportCompanyId = transportCompanyId;
        ShipperId = shipperId;
        OrderId = orderId;
        InvoiceId = invoiceId;
        Amount = amount;
        Description = description;
        LinkType = linkType;
        ShipperContactDetails = shipperContactDetails ?? throw new ArgumentNullException(nameof(shipperContactDetails));
        TransportCompanyContactDetails = transportCompanyContactDetails ?? throw new ArgumentNullException(nameof(transportCompanyContactDetails));
        Settings = settings ?? PaymentLinkSettings.Default();

        // Generate secure token and URL
        LinkToken = GenerateSecureToken();
        PublicUrl = GeneratePublicUrl(LinkToken);

        Status = PaymentLinkStatus.Active;
        ExpiresAt = expiresAt ?? DateTime.UtcNow.AddDays(Settings.DefaultExpiryDays);
        AccessCount = 0;
        RequiresAuthentication = Settings.RequiresAuthentication;
        AllowedIpAddresses = Settings.AllowedIpAddresses?.ToList() ?? new List<string>();

        // Notification settings
        SendEmailNotification = Settings.SendEmailNotification;
        SendSmsNotification = Settings.SendSmsNotification;
        NotificationEmails = Settings.NotificationEmails?.ToList() ?? new List<string>();
        NotificationPhones = Settings.NotificationPhones?.ToList() ?? new List<string>();

        AddDomainEvent(new PaymentLinkCreatedEvent(
            Id, TransportCompanyId, ShipperId, Amount, LinkToken, ExpiresAt, LinkType));
    }

    public void RecordAccess(string ipAddress, string? userAgent = null, string? referrer = null)
    {
        if (Status != PaymentLinkStatus.Active)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "access");

        if (IsExpired())
            throw new PaymentLinkExpiredException(Id, ExpiresAt);

        // Check IP restrictions
        if (AllowedIpAddresses.Any() && !AllowedIpAddresses.Contains(ipAddress))
            throw new PaymentLinkValidationException("Access denied from this IP address");

        AccessCount++;
        LastAccessedAt = DateTime.UtcNow;
        LastAccessedFromIp = ipAddress;

        var accessLog = new PaymentLinkAccess(Id, ipAddress, userAgent, referrer);
        _accessLogs.Add(accessLog);

        // Check for suspicious activity
        if (AccessCount > Settings.MaxAccessAttempts)
        {
            Suspend("Too many access attempts");
            AddDomainEvent(new PaymentLinkSuspiciousActivityEvent(Id, ipAddress, AccessCount));
        }

        AddDomainEvent(new PaymentLinkAccessedEvent(Id, ipAddress, AccessCount));
    }

    public void RecordPaymentAttempt(string paymentMethodId, string? gatewayTransactionId = null)
    {
        if (Status != PaymentLinkStatus.Active)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "process payment");

        if (IsExpired())
            throw new PaymentLinkExpiredException(Id, ExpiresAt);

        var attempt = new PaymentLinkAttempt(Id, paymentMethodId, gatewayTransactionId);
        _paymentAttempts.Add(attempt);

        AddDomainEvent(new PaymentLinkPaymentAttemptedEvent(Id, paymentMethodId, gatewayTransactionId));
    }

    public void MarkAsPaid(string paymentTransactionId, DateTime? paidAt = null)
    {
        if (Status != PaymentLinkStatus.Active)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "mark as paid");

        if (string.IsNullOrWhiteSpace(paymentTransactionId))
            throw new PaymentLinkValidationException("Payment transaction ID is required");

        Status = PaymentLinkStatus.Paid;
        PaymentTransactionId = paymentTransactionId;
        PaidAt = paidAt ?? DateTime.UtcNow;

        AddDomainEvent(new PaymentLinkPaidEvent(
            Id, TransportCompanyId, ShipperId, Amount, paymentTransactionId, PaidAt.Value));
    }

    public void Cancel(string reason)
    {
        if (Status == PaymentLinkStatus.Paid)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "cancel");

        Status = PaymentLinkStatus.Cancelled;

        AddDomainEvent(new PaymentLinkCancelledEvent(Id, reason));
    }

    public void Suspend(string reason)
    {
        if (Status == PaymentLinkStatus.Paid)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "suspend");

        Status = PaymentLinkStatus.Suspended;

        AddDomainEvent(new PaymentLinkSuspendedEvent(Id, reason));
    }

    public void Extend(DateTime newExpiryDate, string reason)
    {
        if (Status != PaymentLinkStatus.Active)
            throw new PaymentLinkInvalidStateException(Id, Status.ToString(), "extend");

        if (newExpiryDate <= DateTime.UtcNow)
            throw new PaymentLinkValidationException("New expiry date must be in the future");

        var oldExpiryDate = ExpiresAt;
        ExpiresAt = newExpiryDate;

        AddDomainEvent(new PaymentLinkExtendedEvent(Id, oldExpiryDate, newExpiryDate, reason));
    }

    public bool IsExpired()
    {
        return DateTime.UtcNow > ExpiresAt;
    }

    public bool IsAccessible()
    {
        return Status == PaymentLinkStatus.Active && !IsExpired();
    }

    public TimeSpan GetTimeUntilExpiry()
    {
        return ExpiresAt > DateTime.UtcNow ? ExpiresAt - DateTime.UtcNow : TimeSpan.Zero;
    }

    public PaymentLinkSummary GetSummary()
    {
        return new PaymentLinkSummary
        {
            Id = Id,
            LinkToken = LinkToken,
            Amount = Amount,
            Status = Status,
            ExpiresAt = ExpiresAt,
            AccessCount = AccessCount,
            PaymentAttempts = _paymentAttempts.Count,
            IsExpired = IsExpired(),
            TimeUntilExpiry = GetTimeUntilExpiry(),
            LastAccessedAt = LastAccessedAt,
            PaidAt = PaidAt
        };
    }

    private string GenerateSecureToken()
    {
        // Generate a cryptographically secure token
        var tokenBytes = new byte[32];
        using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenBytes);
        }
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    private string GeneratePublicUrl(string token)
    {
        // This would typically use a configured base URL
        return $"https://portal.tlilogistics.com/payment/{token}";
    }
}

/// <summary>
/// Represents an access attempt to a payment link
/// </summary>
public class PaymentLinkAccess
{
    public Guid Id { get; private set; }
    public Guid PaymentLinkId { get; private set; }
    public string IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public string? Referrer { get; private set; }
    public DateTime AccessedAt { get; private set; }

    private PaymentLinkAccess() { }

    public PaymentLinkAccess(Guid paymentLinkId, string ipAddress, string? userAgent = null, string? referrer = null)
    {
        Id = Guid.NewGuid();
        PaymentLinkId = paymentLinkId;
        IpAddress = ipAddress ?? throw new ArgumentNullException(nameof(ipAddress));
        UserAgent = userAgent;
        Referrer = referrer;
        AccessedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// Represents a payment attempt through a payment link
/// </summary>
public class PaymentLinkAttempt
{
    public Guid Id { get; private set; }
    public Guid PaymentLinkId { get; private set; }
    public string PaymentMethodId { get; private set; }
    public string? GatewayTransactionId { get; private set; }
    public PaymentAttemptStatus Status { get; private set; }
    public DateTime AttemptedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public string? FailureReason { get; private set; }

    private PaymentLinkAttempt() { }

    public PaymentLinkAttempt(Guid paymentLinkId, string paymentMethodId, string? gatewayTransactionId = null)
    {
        Id = Guid.NewGuid();
        PaymentLinkId = paymentLinkId;
        PaymentMethodId = paymentMethodId ?? throw new ArgumentNullException(nameof(paymentMethodId));
        GatewayTransactionId = gatewayTransactionId;
        Status = PaymentAttemptStatus.Initiated;
        AttemptedAt = DateTime.UtcNow;
    }

    public void MarkAsCompleted(DateTime? completedAt = null)
    {
        Status = PaymentAttemptStatus.Completed;
        CompletedAt = completedAt ?? DateTime.UtcNow;
    }

    public void MarkAsFailed(string failureReason)
    {
        Status = PaymentAttemptStatus.Failed;
        FailureReason = failureReason;
        CompletedAt = DateTime.UtcNow;
    }
}


