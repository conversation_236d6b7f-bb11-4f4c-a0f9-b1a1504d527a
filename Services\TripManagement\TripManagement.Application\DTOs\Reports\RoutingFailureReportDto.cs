using TripManagement.Domain.Enums;

namespace TripManagement.Application.DTOs.Reports;

/// <summary>
/// DTO for routing failure report
/// </summary>
public class RoutingFailureReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = "Routing Failure Report";
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public RoutingFailureReportSummaryDto Summary { get; set; } = new();
    public List<FailureDetailDto> FailureDetails { get; set; } = new();
    public List<FailurePatternAnalysisDto> PatternAnalysis { get; set; } = new();
    public List<ImpactAnalysisDto> ImpactAnalysis { get; set; } = new();
    public List<FailureTrendDto> TrendAnalysis { get; set; } = new();
    public List<RootCauseAnalysisDto> RootCauseAnalysis { get; set; } = new();
    public ReportMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// DTO for routing failure report summary
/// </summary>
public class RoutingFailureReportSummaryDto
{
    public int TotalFailures { get; set; }
    public int OpenFailures { get; set; }
    public int ResolvedFailures { get; set; }
    public int CriticalFailures { get; set; }
    public int RecurringFailures { get; set; }
    public Dictionary<FailureType, int> FailuresByType { get; set; } = new();
    public Dictionary<FailureSeverity, int> FailuresBySeverity { get; set; } = new();
    public Dictionary<string, int> FailuresByCategory { get; set; } = new();
    public decimal TotalImpactCost { get; set; }
    public TimeSpan TotalDelayTime { get; set; }
    public int TotalAffectedCustomers { get; set; }
    public decimal AverageResolutionTimeHours { get; set; }
    public decimal ResolutionRate { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// DTO for failure details
/// </summary>
public class FailureDetailDto
{
    public Guid FailureId { get; set; }
    public Guid TripId { get; set; }
    public string? OrderNumber { get; set; }
    public FailureType FailureType { get; set; }
    public FailureSeverity Severity { get; set; }
    public string FailureReason { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public DateTime FailureTime { get; set; }
    public DateTime? DetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public TimeSpan? ResolutionTime { get; set; }
    public FailureStatus Status { get; set; }
    public string? ResolutionAction { get; set; }
    public decimal? ImpactCost { get; set; }
    public TimeSpan? DelayTime { get; set; }
    public int? AffectedCustomers { get; set; }
    public bool IsRecurring { get; set; }
    public string? RootCause { get; set; }
    public string? PreventiveAction { get; set; }
    public string ImpactLevel { get; set; } = string.Empty;
    public int AgeInHours { get; set; }
    public bool IsLongRunning { get; set; }
    public Dictionary<string, object> TechnicalDetails { get; set; } = new();
}

/// <summary>
/// DTO for failure pattern analysis
/// </summary>
public class FailurePatternAnalysisDto
{
    public string PatternType { get; set; } = string.Empty; // Time, Location, Type, Severity
    public string PatternDescription { get; set; } = string.Empty;
    public int Frequency { get; set; }
    public decimal Percentage { get; set; }
    public List<string> AffectedTrips { get; set; } = new();
    public Dictionary<string, object> PatternDetails { get; set; } = new();
    public string Recommendation { get; set; } = string.Empty;
    public decimal ConfidenceLevel { get; set; }
    public DateTime FirstOccurrence { get; set; }
    public DateTime LastOccurrence { get; set; }
    public TimeSpan AverageInterval { get; set; }
}

/// <summary>
/// DTO for impact analysis
/// </summary>
public class ImpactAnalysisDto
{
    public string ImpactCategory { get; set; } = string.Empty; // Financial, Operational, Customer
    public decimal TotalImpact { get; set; }
    public string ImpactUnit { get; set; } = string.Empty; // Currency, Hours, Count
    public Dictionary<FailureType, decimal> ImpactByFailureType { get; set; } = new();
    public Dictionary<FailureSeverity, decimal> ImpactBySeverity { get; set; } = new();
    public List<string> TopImpactFactors { get; set; } = new();
    public decimal AverageImpactPerFailure { get; set; }
    public decimal PercentageOfTotalImpact { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // Increasing, Decreasing, Stable
    public List<string> MitigationStrategies { get; set; } = new();
}

/// <summary>
/// DTO for failure trends
/// </summary>
public class FailureTrendDto
{
    public DateTime Date { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly
    public int FailureCount { get; set; }
    public int ResolvedCount { get; set; }
    public Dictionary<FailureType, int> FailuresByType { get; set; } = new();
    public Dictionary<FailureSeverity, int> FailuresBySeverity { get; set; } = new();
    public decimal TotalImpactCost { get; set; }
    public TimeSpan AverageResolutionTime { get; set; }
    public decimal ResolutionRate { get; set; }
    public decimal TrendChange { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// DTO for root cause analysis
/// </summary>
public class RootCauseAnalysisDto
{
    public string RootCause { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int FailureCount { get; set; }
    public decimal ImpactPercentage { get; set; }
    public List<FailureType> RelatedFailureTypes { get; set; } = new();
    public List<string> ContributingFactors { get; set; } = new();
    public List<string> PreventiveActions { get; set; } = new();
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public decimal EstimatedPreventionCost { get; set; }
    public decimal PotentialSavings { get; set; }
    public string RecommendedTimeline { get; set; } = string.Empty;
    public List<string> ResponsibleTeams { get; set; } = new();
}

/// <summary>
/// DTO for report metadata
/// </summary>
public class ReportMetadataDto
{
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public DateTime RequestedAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public TimeSpan GenerationTime { get; set; }
    public int TotalRecords { get; set; }
    public bool IsFiltered { get; set; }
    public Dictionary<string, object> FilterCriteria { get; set; } = new();
    public string ReportVersion { get; set; } = "1.0";
    public List<string> DataSources { get; set; } = new();
    public string AnalysisMethod { get; set; } = "Statistical Analysis";
}
