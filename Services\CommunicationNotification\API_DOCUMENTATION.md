# TLI Communication & Notification API Documentation

## Overview

The TLI Communication & Notification API provides comprehensive communication services for the TLI transport logistics platform. This RESTful API enables sending notifications, managing user preferences, real-time chat, and monitoring communication channels.

## Base URL

```
Production: https://api.tli.com/communication/v1
Staging: https://staging-api.tli.com/communication/v1
Development: https://localhost:5001/api
```

## Authentication

All API endpoints require JWT Bearer token authentication unless otherwise specified.

```http
Authorization: Bearer <your-jwt-token>
```

### JWT Token Structure

```json
{
  "sub": "user-id",
  "name": "User Name",
  "role": ["User", "Driver", "Admin"],
  "permission": ["notifications:send", "users:manage"],
  "exp": 1640995200
}
```

## Rate Limiting

API endpoints are rate-limited to ensure fair usage:

- **Global**: 1000 requests per minute per user
- **Notifications**: 100 requests per minute per user
- **Messages**: 200 requests per minute per user
- **Chat**: 500 requests per minute per user
- **User Preferences**: 50 requests per minute per user

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## API Endpoints

### 1. Notification Management

#### Send Notification

Send a notification to a specific user.

```http
POST /api/notifications/send
```

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "content": "Your trip has been confirmed! Driver: Rajesh Kumar, Vehicle: KA01AB1234.",
  "messageType": "TripConfirmation",
  "priority": "High",
  "preferredChannel": "WhatsApp",
  "preferredLanguage": "Kannada",
  "subject": "Trip Confirmation - Booking #12345",
  "parameters": {
    "customer_name": "Suresh Kumar",
    "trip_date": "2024-01-15",
    "pickup_time": "09:00",
    "pickup_location": "Bangalore Airport"
  },
  "templateId": "trip_confirmation_template",
  "requireDeliveryConfirmation": true,
  "tags": ["trip", "confirmation", "high-priority"],
  "correlationId": "trip-12345-confirmation"
}
```

**Response:**
```json
{
  "notificationId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "Sent",
  "channelUsed": "WhatsApp",
  "sentAt": "2024-01-15T09:00:00Z",
  "externalId": "wamid.HBgNOTE1234567890",
  "estimatedDeliveryTime": "00:00:15",
  "commandId": "123e4567-e89b-12d3-a456-426614174001"
}
```

#### Send Bulk Notification

Send notifications to multiple users.

```http
POST /api/notifications/send-bulk
```

**Requires Role:** `Admin`, `Dispatcher`, or `BulkSender`

**Request Body:**
```json
{
  "userIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "123e4567-e89b-12d3-a456-426614174001"
  ],
  "content": "New trip opportunities available in your area.",
  "messageType": "TripOpportunity",
  "priority": "Normal",
  "preferredChannel": "Push",
  "batchSize": 100,
  "batchDelaySeconds": 5
}
```

#### Send Template Notification

Send a notification using a predefined template.

```http
POST /api/notifications/send-template
```

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "templateId": "trip_confirmation",
  "templateParameters": {
    "customer_name": "Suresh Kumar",
    "trip_date": "January 15, 2024",
    "pickup_time": "9:00 AM",
    "driver_name": "Rajesh Kumar",
    "vehicle_number": "KA01AB1234"
  },
  "messageType": "TripConfirmation",
  "priority": "High",
  "preferredChannel": "WhatsApp"
}
```

#### Send Emergency Notification

Send high-priority emergency notifications with escalation.

```http
POST /api/notifications/send-emergency
```

**Requires Role:** `Admin`, `EmergencyDispatcher`, or `SystemOperator`

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "content": "Emergency: Vehicle breakdown reported. Immediate assistance required.",
  "emergencyType": "VehicleBreakdown",
  "location": "NH-4, Bangalore-Mysore Highway, KM 45",
  "requireAcknowledgment": true,
  "acknowledgmentTimeoutMinutes": 5,
  "escalationChannels": ["Voice", "SMS", "WhatsApp"],
  "escalationUserIds": ["supervisor-id", "manager-id"]
}
```

#### Get Notification Status

Get delivery status of a notification.

```http
GET /api/notifications/{notificationId}/status
```

**Response:**
```json
{
  "notificationId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "Delivered",
  "channel": "WhatsApp",
  "sentAt": "2024-01-15T09:00:00Z",
  "deliveredAt": "2024-01-15T09:00:15Z",
  "readAt": "2024-01-15T09:05:30Z",
  "externalId": "wamid.HBgNOTE1234567890",
  "deliveryAttempts": [
    {
      "attemptNumber": 1,
      "channel": "WhatsApp",
      "attemptedAt": "2024-01-15T09:00:00Z",
      "isSuccess": true,
      "responseTime": "00:00:02.150"
    }
  ]
}
```

### 2. Message Management

#### Get User Message History

Retrieve message history for a user with pagination and filtering.

```http
GET /api/messages/user/{userId}/history?pageNumber=1&pageSize=20&channel=WhatsApp&messageType=TripConfirmation&fromDate=2024-01-01&toDate=2024-01-31
```

**Query Parameters:**
- `pageNumber` (optional): Page number (default: 1)
- `pageSize` (optional): Page size (default: 20, max: 100)
- `channel` (optional): Filter by notification channel
- `messageType` (optional): Filter by message type
- `fromDate` (optional): Filter from date
- `toDate` (optional): Filter to date

**Response:**
```json
{
  "messages": [
    {
      "messageId": "123e4567-e89b-12d3-a456-426614174000",
      "content": "Your trip has been confirmed!",
      "subject": "Trip Confirmation",
      "messageType": "TripConfirmation",
      "channel": "WhatsApp",
      "status": "Delivered",
      "priority": "High",
      "language": "Kannada",
      "sentAt": "2024-01-15T09:00:00Z",
      "deliveredAt": "2024-01-15T09:00:15Z",
      "readAt": "2024-01-15T09:05:30Z",
      "tags": ["trip", "confirmation"]
    }
  ],
  "totalCount": 150,
  "pageNumber": 1,
  "pageSize": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

#### Get Message Details

Get detailed information about a specific message.

```http
GET /api/messages/{messageId}
```

#### Mark Message as Read

Mark a message as read by the user.

```http
POST /api/messages/{messageId}/mark-read
```

#### Archive Message

Archive a message.

```http
POST /api/messages/{messageId}/archive
```

#### Search Messages

Search messages with advanced filters (Admin/Support/Analyst only).

```http
POST /api/messages/search
```

**Request Body:**
```json
{
  "searchTerm": "trip confirmation",
  "userIds": ["123e4567-e89b-12d3-a456-426614174000"],
  "channels": ["WhatsApp", "SMS"],
  "messageTypes": ["TripConfirmation", "TripUpdate"],
  "statuses": ["Delivered", "Read"],
  "fromDate": "2024-01-01T00:00:00Z",
  "toDate": "2024-01-31T23:59:59Z",
  "tags": ["trip", "high-priority"],
  "pageNumber": 1,
  "pageSize": 20,
  "sortBy": "SentAt",
  "sortDirection": "DESC"
}
```

### 3. User Preferences Management

#### Get User Preferences

Get communication preferences for a user.

```http
GET /api/user-preferences
GET /api/user-preferences/user/{userId}
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "preferredLanguage": "Kannada",
  "timeZone": "Asia/Kolkata",
  "quietHoursStart": "22:00:00",
  "quietHoursEnd": "07:00:00",
  "enableQuietHours": true,
  "channelPreferences": {
    "WhatsApp": {
      "isEnabled": true,
      "priority": 9,
      "contactInfo": "+919876543210",
      "settings": {
        "enableReadReceipts": true,
        "enableGroupMessages": false
      }
    },
    "SMS": {
      "isEnabled": true,
      "priority": 7,
      "contactInfo": "+919876543210"
    }
  },
  "messageTypePreferences": {
    "TripConfirmation": {
      "isEnabled": true,
      "preferredChannels": ["WhatsApp", "SMS"],
      "requireConfirmation": false
    }
  },
  "globalOptOut": false,
  "marketingOptOut": true
}
```

#### Update User Preferences

Update communication preferences for a user.

```http
PUT /api/user-preferences
PUT /api/user-preferences/user/{userId}
```

#### Update Channel Preference

Update preference for a specific communication channel.

```http
PUT /api/user-preferences/channel/{channel}
PUT /api/user-preferences/user/{userId}/channel/{channel}
```

**Request Body:**
```json
{
  "isEnabled": true,
  "priority": 9,
  "contactInfo": "+919876543210",
  "settings": {
    "enableReadReceipts": true,
    "enableGroupMessages": false
  }
}
```

#### Opt Out from Communications

Opt out from all communications.

```http
POST /api/user-preferences/opt-out
POST /api/user-preferences/user/{userId}/opt-out
```

#### Opt In to Communications

Opt in to communications.

```http
POST /api/user-preferences/opt-in
POST /api/user-preferences/user/{userId}/opt-in
```

### 4. Real-time Chat

#### Create Conversation

Create a new chat conversation.

```http
POST /api/chat/conversations
```

**Request Body:**
```json
{
  "title": "Trip #12345 - Bangalore to Mysore",
  "description": "Communication channel for trip coordination",
  "conversationType": "TripCoordination",
  "participantIds": [
    "customer-id",
    "driver-id",
    "support-id"
  ],
  "tripId": "123e4567-e89b-12d3-a456-426614174003",
  "allowFileSharing": true,
  "maxParticipants": 10
}
```

#### Send Message

Send a message in a conversation.

```http
POST /api/chat/conversations/{conversationId}/messages
```

**Request Body:**
```json
{
  "content": "I'm 5 minutes away from the pickup location.",
  "messageType": "Text",
  "replyToMessageId": "parent-message-id",
  "attachments": [
    {
      "fileName": "location.jpg",
      "fileSize": 245760,
      "contentType": "image/jpeg",
      "fileData": "base64_encoded_image_data"
    }
  ]
}
```

#### Get Messages

Get messages in a conversation with pagination.

```http
GET /api/chat/conversations/{conversationId}/messages?pageNumber=1&pageSize=50&beforeMessageId=message-id
```

### 5. External Services Management

#### Get Provider Health Status

Get health status of all external service providers (Admin only).

```http
GET /api/external-services/health
```

#### Test Provider

Test functionality of external service providers (Admin only).

```http
POST /api/external-services/test/sms
POST /api/external-services/test/email
POST /api/external-services/test/push
POST /api/external-services/test/whatsapp
```

#### Validate Contact Information

Validate phone numbers and email addresses.

```http
POST /api/external-services/validate/phone
POST /api/external-services/validate/email
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information:

```json
{
  "error": {
    "message": "Validation failed",
    "traceId": "0HMVB9RLQNQR7:00000001",
    "timestamp": "2024-01-15T09:00:00Z",
    "path": "/api/notifications/send",
    "details": {
      "userId": ["The userId field is required."],
      "content": ["The content field is required."]
    }
  }
}
```

### Common Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## WebSocket/SignalR Integration

Real-time communication is available via SignalR:

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/chat", {
        accessTokenFactory: () => yourJwtToken
    })
    .build();

// Join conversation
await connection.invoke("JoinConversation", conversationId);

// Listen for new messages
connection.on("ReceiveMessage", (message) => {
    console.log("New message:", message);
});

// Send message
await connection.invoke("SendMessage", conversationId, messageContent);
```

## SDKs and Libraries

### JavaScript/TypeScript SDK

```bash
npm install @tli/communication-sdk
```

```javascript
import { TLICommunicationClient } from '@tli/communication-sdk';

const client = new TLICommunicationClient({
    baseUrl: 'https://api.tli.com/communication/v1',
    apiKey: 'your-api-key'
});

// Send notification
const result = await client.notifications.send({
    userId: 'user-id',
    content: 'Your trip has been confirmed!',
    messageType: 'TripConfirmation'
});
```

### C# SDK

```bash
dotnet add package TLI.Communication.Client
```

```csharp
using TLI.Communication.Client;

var client = new CommunicationClient(new CommunicationClientOptions
{
    BaseUrl = "https://api.tli.com/communication/v1",
    ApiKey = "your-api-key"
});

var result = await client.Notifications.SendAsync(new SendNotificationRequest
{
    UserId = Guid.Parse("user-id"),
    Content = "Your trip has been confirmed!",
    MessageType = MessageType.TripConfirmation
});
```

## Monitoring and Analytics

### Health Checks

```http
GET /health
```

Returns comprehensive health information about the service and its dependencies.

### Metrics

The API exposes metrics for monitoring:

- Request count and duration
- Error rates by endpoint
- External service response times
- Message delivery rates
- User engagement metrics

## Support

For API support and questions:

- **Documentation**: https://docs.tli.com/communication-api
- **Support Email**: <EMAIL>
- **Developer Portal**: https://developers.tli.com
- **Status Page**: https://status.tli.com
