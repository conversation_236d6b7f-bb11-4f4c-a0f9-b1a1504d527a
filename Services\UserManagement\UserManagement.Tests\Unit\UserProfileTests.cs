using System;
using FluentAssertions;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Exceptions;
using Xunit;

namespace UserManagement.Tests.Unit
{
    public class UserProfileTests
    {
        [Fact]
        public void CreateUserProfile_WithValidData_ShouldCreateProfile()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userType = UserType.Shipper;
            var email = "<EMAIL>";

            // Act
            var profile = new UserProfile(userId, userType, email);

            // Assert
            profile.UserId.Should().Be(userId);
            profile.UserType.Should().Be(userType);
            profile.Email.Should().Be(email);
            profile.Status.Should().Be(ProfileStatus.Incomplete);
            profile.Country.Should().Be("India");
        }

        [Fact]
        public void CreateUserProfile_WithEmptyUserId_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.Empty;
            var userType = UserType.Shipper;
            var email = "<EMAIL>";

            // Act & Assert
            var action = () => new UserProfile(userId, userType, email);
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("User ID cannot be empty");
        }

        [Fact]
        public void CreateUserProfile_WithEmptyEmail_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userType = UserType.Shipper;
            var email = "";

            // Act & Assert
            var action = () => new UserProfile(userId, userType, email);
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("Email cannot be empty");
        }

        [Fact]
        public void UpdatePersonalDetails_WithValidData_ShouldUpdateProfile()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            var firstName = "John";
            var lastName = "Doe";
            var phoneNumber = "+**********";

            // Act
            profile.UpdatePersonalDetails(firstName, lastName, phoneNumber);

            // Assert
            profile.FirstName.Should().Be(firstName);
            profile.LastName.Should().Be(lastName);
            profile.PhoneNumber.Should().Be(phoneNumber);
        }

        [Fact]
        public void UpdateCompanyDetails_ForTransportCompany_ShouldUpdateProfile()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.TransportCompany, "<EMAIL>");
            var companyName = "Test Transport";
            var gstNumber = "GST123456789";
            var panNumber = "PAN123456";

            // Act
            profile.UpdateCompanyDetails(companyName, gstNumber, panNumber);

            // Assert
            profile.CompanyName.Should().Be(companyName);
            profile.GstNumber.Should().Be(gstNumber);
            profile.PanNumber.Should().Be(panNumber);
        }

        [Fact]
        public void UpdateCompanyDetails_ForShipper_ShouldThrowException()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");

            // Act & Assert
            var action = () => profile.UpdateCompanyDetails("Company", "GST123", "PAN123");
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("Company details can only be set for transport companies and brokers");
        }

        [Fact]
        public void SetLicenseNumber_ForDriver_ShouldUpdateProfile()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Driver, "<EMAIL>");
            var licenseNumber = "DL123456789";

            // Act
            profile.SetLicenseNumber(licenseNumber);

            // Assert
            profile.LicenseNumber.Should().Be(licenseNumber);
        }

        [Fact]
        public void SetLicenseNumber_ForNonDriver_ShouldThrowException()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");

            // Act & Assert
            var action = () => profile.SetLicenseNumber("DL123456789");
            action.Should().Throw<UserManagementDomainException>()
                .WithMessage("License number can only be set for drivers");
        }

        [Fact]
        public void SubmitForReview_WithCompleteProfile_ShouldChangeStatus()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            profile.UpdatePersonalDetails("John", "Doe", "+**********");
            profile.UpdateAddress("123 Main St", "City", "State", "12345");

            // Act
            profile.SubmitForReview();

            // Assert
            profile.Status.Should().Be(ProfileStatus.UnderReview);
        }

        [Fact]
        public void Approve_WithUnderReviewStatus_ShouldChangeStatus()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            profile.UpdatePersonalDetails("John", "Doe", "+**********");
            profile.UpdateAddress("123 Main St", "City", "State", "12345");
            profile.SubmitForReview();
            var approvedBy = Guid.NewGuid();

            // Act
            profile.Approve(approvedBy);

            // Assert
            profile.Status.Should().Be(ProfileStatus.Approved);
            profile.ApprovedBy.Should().Be(approvedBy);
            profile.ApprovedAt.Should().NotBeNull();
            profile.RejectionReason.Should().BeNull();
        }

        [Fact]
        public void Reject_WithUnderReviewStatus_ShouldChangeStatus()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            profile.UpdatePersonalDetails("John", "Doe", "+**********");
            profile.UpdateAddress("123 Main St", "City", "State", "12345");
            profile.SubmitForReview();
            var reason = "Invalid documents";

            // Act
            profile.Reject(reason);

            // Assert
            profile.Status.Should().Be(ProfileStatus.Rejected);
            profile.RejectionReason.Should().Be(reason);
        }

        [Fact]
        public void GetDisplayName_WithCompanyName_ShouldReturnCompanyName()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.TransportCompany, "<EMAIL>");
            profile.UpdateCompanyDetails("Test Transport", "GST123", "PAN123");

            // Act
            var displayName = profile.GetDisplayName();

            // Assert
            displayName.Should().Be("Test Transport");
        }

        [Fact]
        public void GetDisplayName_WithPersonalName_ShouldReturnFullName()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");
            profile.UpdatePersonalDetails("John", "Doe", "+**********");

            // Act
            var displayName = profile.GetDisplayName();

            // Assert
            displayName.Should().Be("John Doe");
        }

        [Fact]
        public void GetDisplayName_WithOnlyEmail_ShouldReturnEmail()
        {
            // Arrange
            var profile = new UserProfile(Guid.NewGuid(), UserType.Shipper, "<EMAIL>");

            // Act
            var displayName = profile.GetDisplayName();

            // Assert
            displayName.Should().Be("<EMAIL>");
        }
    }
}
