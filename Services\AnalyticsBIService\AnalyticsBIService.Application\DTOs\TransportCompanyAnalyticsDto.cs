namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Transport company dashboard data transfer object
/// </summary>
public class TransportCompanyDashboardDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Performance Metrics
    public TransportCompanyPerformanceDto Performance { get; set; } = new();

    // RFQ Analytics
    public RFQConversionAnalyticsDto RFQAnalytics { get; set; } = new();

    // Cost Analysis
    public TransportCompanyCostAnalysisDto CostAnalysis { get; set; } = new();

    // Market Intelligence
    public TransportCompanyMarketIntelligenceDto MarketIntelligence { get; set; } = new();

    // Broker Performance
    public BrokerPerformanceComparisonDto BrokerPerformance { get; set; } = new();

    // Financial Performance
    public TransportCompanyFinancialPerformanceDto FinancialPerformance { get; set; } = new();

    // Key Metrics Summary
    public List<KPIPerformanceDto> KeyMetrics { get; set; } = new();

    // Recent Alerts
    public List<AlertDto> RecentAlerts { get; set; } = new();
}

/// <summary>
/// Transport company performance data transfer object
/// </summary>
public class TransportCompanyPerformanceDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // RFQ Performance
    public decimal RFQConversionRate { get; set; }
    public long TotalRFQsCreated { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal AverageRFQResponseTime { get; set; }

    // Broker Performance
    public decimal AverageBrokerResponseTime { get; set; }
    public decimal BrokerSatisfactionScore { get; set; }
    public long ActiveBrokers { get; set; }

    // Delivery Performance
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal DeliveryAccuracyRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }

    // Operational Metrics
    public decimal CapacityUtilizationRate { get; set; }
    public decimal OperationalEfficiencyScore { get; set; }
    public decimal CostPerKilometer { get; set; }

    // Trends
    public List<PerformanceTrendDto> PerformanceTrends { get; set; } = new();
    public List<BrokerPerformanceDto> TopBrokers { get; set; } = new();
    public List<RoutePerformanceDto> TopRoutes { get; set; } = new();
}

/// <summary>
/// RFQ conversion analytics data transfer object
/// </summary>
public class RFQConversionAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall RFQ Metrics
    public long TotalRFQsCreated { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal OverallConversionRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageResponseTime { get; set; }

    // Conversion Funnel
    public RFQConversionFunnelDto ConversionFunnel { get; set; } = new();

    // Broker Analysis
    public List<BrokerRFQPerformanceDto> BrokerPerformance { get; set; } = new();

    // Route Analysis
    public List<RouteRFQPerformanceDto> RoutePerformance { get; set; } = new();

    // Time-based Analysis
    public List<RFQConversionTrendDto> ConversionTrends { get; set; } = new();

    // Failure Analysis
    public List<RFQFailureReasonDto> FailureReasons { get; set; } = new();

    // Optimization Recommendations
    public List<RFQOptimizationRecommendationDto> Recommendations { get; set; } = new();
}

/// <summary>
/// RFQ conversion funnel analysis
/// </summary>
public class RFQConversionFunnelDto
{
    public long RFQsCreated { get; set; }
    public long RFQsPublished { get; set; }
    public long QuotesReceived { get; set; }
    public long QuotesEvaluated { get; set; }
    public long QuotesAccepted { get; set; }
    public long TripsCompleted { get; set; }

    // Conversion Rates
    public decimal PublishRate { get; set; }
    public decimal QuoteReceiptRate { get; set; }
    public decimal EvaluationRate { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal CompletionRate { get; set; }
}

/// <summary>
/// Broker RFQ performance data
/// </summary>
public class BrokerRFQPerformanceDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public long RFQsHandled { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal QualityScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Route RFQ performance data
/// </summary>
public class RouteRFQPerformanceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public string RouteType { get; set; } = string.Empty;
    public long RFQCount { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageDistance { get; set; }
    public decimal CostPerKilometer { get; set; }
    public string PopularityRank { get; set; } = string.Empty;

    // Properties required by GetBrokerAnalyticsQueryHandler
    public long RFQsReceived { get; set; }
    public long SuccessfulRFQs { get; set; }
    public string CompetitionLevel { get; set; } = string.Empty;
}

/// <summary>
/// RFQ conversion trend over time
/// </summary>
public class RFQConversionTrendDto
{
    public DateTime Date { get; set; }
    public long RFQsCreated { get; set; }
    public long SuccessfulRFQs { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageResponseTime { get; set; }

    // Properties required by GetBrokerAnalyticsQueryHandler
    public long RFQsReceived { get; set; }
    public long RFQsProcessed { get; set; }
    public decimal AverageProcessingTime { get; set; }
}

/// <summary>
/// RFQ failure reason analysis
/// </summary>
public class RFQFailureReasonDto
{
    public string FailureReason { get; set; } = string.Empty;
    public long Count { get; set; }
    public decimal Percentage { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// RFQ optimization recommendation
/// </summary>
public class RFQOptimizationRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
    public decimal EstimatedROI { get; set; }

    // Additional properties required by GetBrokerAnalyticsQueryHandler
    public decimal EstimatedRevenue { get; set; }
    public decimal ROI { get; set; } // Alias for EstimatedROI
}

/// <summary>
/// Broker response time analytics data transfer object
/// </summary>
public class BrokerResponseTimeAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Response Time Metrics
    public decimal AverageResponseTime { get; set; }
    public decimal MedianResponseTime { get; set; }
    public decimal FastestResponseTime { get; set; }
    public decimal SlowestResponseTime { get; set; }
    public decimal ResponseTimeVariability { get; set; }

    // SLA Performance
    public decimal SLAComplianceRate { get; set; }
    public long ResponsesWithinSLA { get; set; }
    public long TotalResponses { get; set; }

    // Broker Performance
    public List<BrokerResponsePerformanceDto> BrokerPerformance { get; set; } = new();

    // Time-based Analysis
    public List<ResponseTimeTrendDto> ResponseTimeTrends { get; set; } = new();

    // Response Time Distribution
    public List<ResponseTimeDistributionDto> ResponseTimeDistribution { get; set; } = new();

    // Improvement Recommendations
    public List<ResponseTimeImprovementDto> ImprovementRecommendations { get; set; } = new();
}

/// <summary>
/// Broker response performance data
/// </summary>
public class BrokerResponsePerformanceDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public decimal AverageResponseTime { get; set; }
    public decimal MedianResponseTime { get; set; }
    public decimal SLAComplianceRate { get; set; }
    public long TotalResponses { get; set; }
    public decimal ResponseTimeVariability { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
    public decimal ImprovementTrend { get; set; }
}

// ResponseTimeTrendDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Response time distribution analysis
/// </summary>
public class ResponseTimeDistributionDto
{
    public string TimeRange { get; set; } = string.Empty;
    public long Count { get; set; }
    public decimal Percentage { get; set; }
    public bool IsWithinSLA { get; set; }
}

/// <summary>
/// Response time improvement recommendation
/// </summary>
public class ResponseTimeImprovementDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImprovement { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
}

/// <summary>
/// Delivery performance analytics data transfer object
/// </summary>
public class DeliveryPerformanceAnalyticsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Delivery Metrics
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal EarlyDeliveryRate { get; set; }
    public decimal LateDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal DeliveryAccuracyRate { get; set; }

    // Quality Metrics
    public decimal DamageRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal DeliveryQualityScore { get; set; }

    // Route Performance
    public List<RouteDeliveryPerformanceDto> RoutePerformance { get; set; } = new();

    // Carrier Performance
    public List<CarrierDeliveryPerformanceDto> CarrierPerformance { get; set; } = new();

    // Time-based Analysis
    public List<DeliveryPerformanceTrendDto> DeliveryTrends { get; set; } = new();

    // Issue Analysis
    public List<DeliveryIssueDto> DeliveryIssues { get; set; } = new();

    // Improvement Opportunities
    public List<DeliveryImprovementOpportunityDto> ImprovementOpportunities { get; set; } = new();
}

/// <summary>
/// Route delivery performance data
/// </summary>
public class RouteDeliveryPerformanceDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public long TotalDeliveries { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal RouteEfficiencyScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Carrier delivery performance data
/// </summary>
public class CarrierDeliveryPerformanceDto
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public string CarrierType { get; set; } = string.Empty;
    public long TotalDeliveries { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal DamageRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal ReliabilityScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;

    // Properties required by GetCarrierAnalyticsQueryHandler
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal EarlyDeliveryRate { get; set; }
    public decimal LateDeliveryRate { get; set; }
    public decimal DeliveryAccuracyRate { get; set; }
    public decimal DeliveryQualityScore { get; set; }
    public List<DeliveryPerformanceTrendDto> DeliveryTrends { get; set; } = new();
    public List<RoutePerformanceDto> RoutePerformance { get; set; } = new();
    public List<DeliveryIssueDto> DeliveryIssues { get; set; } = new();
    public List<CustomerFeedbackDto> CustomerFeedback { get; set; } = new();
}

/// <summary>
/// Delivery performance trend over time
/// </summary>


/// <summary>
/// Delivery improvement opportunity
/// </summary>
public class DeliveryImprovementOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImprovement { get; set; }
    public decimal EstimatedCostSavings { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
    public decimal ROI { get; set; }
}

/// <summary>
/// Transport company cost analysis data transfer object
/// </summary>
public class TransportCompanyCostAnalysisDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Overall Cost Metrics
    public decimal TotalCosts { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CostPerTrip { get; set; }
    public decimal CostPerTon { get; set; }
    public decimal CostGrowthRate { get; set; }

    // Cost Breakdown
    public List<CostCategoryDto> CostBreakdown { get; set; } = new();

    // Pricing Analysis
    public PricingAnalysisDto PricingAnalysis { get; set; } = new();

    // Profitability Analysis
    public ProfitabilityAnalysisDto ProfitabilityAnalysis { get; set; } = new();

    // Cost Trends
    public List<CostTrendDto> CostTrends { get; set; } = new();

    // Cost Optimization Opportunities
    public List<CostOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();

    // Benchmarking
    public CostBenchmarkingDto Benchmarking { get; set; } = new();
}

/// <summary>
/// Cost category breakdown
/// </summary>
public class CostCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
    public List<CostSubCategoryDto> SubCategories { get; set; } = new();
}

/// <summary>
/// Cost sub-category data
/// </summary>
public class CostSubCategoryDto
{
    public string SubCategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Pricing analysis data
/// </summary>
public class PricingAnalysisDto
{
    public decimal AveragePrice { get; set; }
    public decimal MedianPrice { get; set; }
    public decimal PriceVariability { get; set; }
    public decimal PriceGrowthRate { get; set; }
    public List<PricingTrendDto> PricingTrends { get; set; } = new();
    public List<RoutePricingDto> RoutePricing { get; set; } = new();
    public List<ServiceTypePricingDto> ServiceTypePricing { get; set; } = new();
    public PricingOptimizationDto OptimizationRecommendations { get; set; } = new();
}

// PricingTrendDto is already defined in CarrierAnalyticsDto.cs - using that instead

/// <summary>
/// Route-specific pricing data
/// </summary>
public class RoutePricingDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal AveragePrice { get; set; }
    public decimal PricePerKilometer { get; set; }
    public decimal PriceVariability { get; set; }
    public decimal MarketPrice { get; set; }
    public decimal PriceCompetitiveness { get; set; }
}

// ServiceTypePricingDto is already defined in CarrierAnalyticsDto.cs - using that instead

/// <summary>
/// Profitability analysis data
/// </summary>
public class ProfitabilityAnalysisDto
{
    public decimal GrossProfit { get; set; }
    public decimal GrossProfitMargin { get; set; }
    public decimal NetProfit { get; set; }
    public decimal NetProfitMargin { get; set; }
    public decimal EBITDA { get; set; }
    public decimal EBITDAMargin { get; set; }
    public decimal ROI { get; set; }
    public decimal ROE { get; set; }
    public List<ProfitabilityTrendDto> ProfitabilityTrends { get; set; } = new();
    public List<RouteProfitabilityDto> RouteProfitability { get; set; } = new();
    public List<ServiceProfitabilityDto> ServiceProfitability { get; set; } = new();
}

/// <summary>
/// Profitability trend over time
/// </summary>
public class ProfitabilityTrendDto
{
    public DateTime Date { get; set; }
    public decimal GrossProfit { get; set; }
    public decimal GrossProfitMargin { get; set; }
    public decimal NetProfit { get; set; }
    public decimal NetProfitMargin { get; set; }
}

/// <summary>
/// Route profitability analysis
/// </summary>
public class RouteProfitabilityDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public decimal Costs { get; set; }
    public decimal Profit { get; set; }
    public decimal ProfitMargin { get; set; }
    public string ProfitabilityRating { get; set; } = string.Empty;
}

/// <summary>
/// Service profitability analysis
/// </summary>
public class ServiceProfitabilityDto
{
    public string ServiceType { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public decimal Costs { get; set; }
    public decimal Profit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal ROI { get; set; }
    public string ProfitabilityRating { get; set; } = string.Empty;
}

/// <summary>
/// Cost trend over time
/// </summary>
public class CostTrendDto
{
    public DateTime Date { get; set; }
    public decimal TotalCosts { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CostPerTrip { get; set; }
    public List<CostCategoryTrendDto> CategoryTrends { get; set; } = new();
}

/// <summary>
/// Cost category trend data
/// </summary>
public class CostCategoryTrendDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// Cost optimization opportunity
/// </summary>
public class CostOptimizationOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
    public decimal ImplementationCost { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
    public string Timeline { get; set; } = string.Empty;
}

/// <summary>
/// Cost benchmarking data
/// </summary>
public class CostBenchmarkingDto
{
    public decimal IndustryAverageCostPerKm { get; set; }
    public decimal CompanyCostPerKm { get; set; }
    public decimal CostCompetitiveness { get; set; }
    public string BenchmarkRating { get; set; } = string.Empty;
    public List<BenchmarkCategoryDto> CategoryBenchmarks { get; set; } = new();
}

/// <summary>
/// Benchmark category data
/// </summary>
public class BenchmarkCategoryDto
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal CompanyValue { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal Variance { get; set; }
    public string Performance { get; set; } = string.Empty;
}

/// <summary>
/// Pricing optimization data
/// </summary>
public class PricingOptimizationDto
{
    public decimal OptimalPricePoint { get; set; }
    public decimal CurrentPricePoint { get; set; }
    public decimal PotentialRevenueIncrease { get; set; }
    public decimal DemandElasticity { get; set; }
    public List<PricingRecommendationDto> Recommendations { get; set; } = new();
    public List<PricingScenarioDto> Scenarios { get; set; } = new();
}

/// <summary>
/// Pricing recommendation
/// </summary>
public class PricingRecommendationDto
{
    public string RecommendationType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialImpact { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
}

/// <summary>
/// Pricing scenario analysis
/// </summary>
public class PricingScenarioDto
{
    public string ScenarioName { get; set; } = string.Empty;
    public decimal PriceChange { get; set; }
    public decimal ExpectedDemandChange { get; set; }
    public decimal ExpectedRevenueChange { get; set; }
    public decimal ExpectedProfitChange { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
}
