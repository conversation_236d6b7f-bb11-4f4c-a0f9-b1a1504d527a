# Network & Fleet Management Service

A comprehensive network and fleet management microservice built with .NET 8, featuring clean architecture principles, domain-driven design, and advanced fleet operations for the TLI Logistics platform.

## 🏗️ Architecture Overview

This service follows Clean Architecture principles with the following layers:

### Domain Layer (`NetworkFleetManagement.Domain`)
- **Entities**: Core business entities (Carrier, Vehicle, Driver, BrokerCarrierNetwork, etc.)
- **Value Objects**: Location, VehicleSpecifications, PerformanceMetrics
- **Domain Events**: Carrier, vehicle, and driver lifecycle events
- **Enums**: CarrierStatus, VehicleStatus, DriverStatus, DocumentType, etc.

### Application Layer (`NetworkFleetManagement.Application`)
- **Commands**: CQRS commands for write operations
- **Queries**: CQRS queries for read operations
- **DTOs**: Data transfer objects
- **Interfaces**: Repository and service contracts
- **Mappings**: AutoMapper profiles

### Infrastructure Layer (`NetworkFleetManagement.Infrastructure`)
- **Database**: Entity Framework Core with PostgreSQL/TimescaleDB
- **Repositories**: Data access implementations
- **Configurations**: Entity configurations

### API Layer (`NetworkFleetManagement.API`)
- **Controllers**: REST API endpoints
- **Authentication**: JWT token validation
- **Swagger**: API documentation

## 🚀 Features

### Carrier Management
- Carrier registration and onboarding
- KYC document verification
- Performance tracking and ratings
- Status management (Active, Suspended, etc.)
- Business profile management

### Fleet Management System
- Vehicle registration and specifications
- Maintenance scheduling and tracking
- Document management (RC, Insurance, Fitness, etc.)
- Vehicle availability and utilization tracking
- Real-time location updates
- Earnings and performance analytics

### Driver Management
- Driver registration and onboarding
- License verification and expiry tracking
- Aadhar/PAN verification
- Performance metrics and ratings
- Route preferences and operational areas
- Driver-vehicle assignment management
- Mobile onboarding support

### Broker-Carrier Network Management
- Network relationship establishment
- Performance-based carrier selection
- Priority and exclusivity management
- Contract and rate management
- Network performance analytics
- Carrier suspension and reactivation

### Document Management
- Multi-type document support
- Verification workflows
- Expiry tracking and alerts
- File management and storage
- Document renewal reminders

### Admin Panel Features
- Network performance monitoring
- Carrier performance analytics
- Fleet utilization reports
- Driver compliance tracking
- Document verification management
- Suspension and reactivation workflows

## 🛠️ Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL**: Primary database with TimescaleDB
- **MediatR**: CQRS implementation
- **AutoMapper**: Object mapping
- **FluentValidation**: Input validation
- **Swagger**: API documentation
- **Serilog**: Structured logging
- **RabbitMQ**: Message broker for events

## 🔧 Configuration

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_NetworkFleetManagement;User Id=timescale;Password=timescale"
  }
}
```

### Authentication
```json
{
  "Authentication": {
    "Authority": "http://localhost:5001",
    "Audience": "networkfleet-api"
  }
}
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- PostgreSQL/TimescaleDB
- RabbitMQ (optional, for messaging)

### Setup
1. Clone the repository
2. Navigate to the Network & Fleet Management service directory
3. Update connection strings in `appsettings.json`
4. Run database setup:
   ```bash
   psql -U timescale -d postgres -f database-setup.sql
   ```
5. Run database migrations:
   ```bash
   dotnet ef database update
   ```
6. Start the service:
   ```bash
   dotnet run
   ```

### Docker Support
```bash
docker build -t networkfleet-service .
docker run -p 5005:80 networkfleet-service
```

## 📊 Database Schema

### Core Tables
- `carriers` - Carrier company information and status
- `vehicles` - Vehicle fleet management
- `drivers` - Driver profiles and management
- `broker_carrier_networks` - Network relationships
- `carrier_documents` - Carrier document management
- `vehicle_documents` - Vehicle document management
- `driver_documents` - Driver document management
- `driver_vehicle_assignments` - Driver-vehicle assignments
- `vehicle_maintenance_records` - Maintenance history
- `network_performance_records` - Network performance tracking

### Key Features
- Comprehensive indexing for performance
- Audit trails for all entities
- Document expiry tracking
- Performance metrics storage
- Location tracking capabilities

## 🔄 Integration Events

### Published Events
- `carrier.created` - When a carrier is registered
- `carrier.status_changed` - When carrier status changes
- `vehicle.created` - When a vehicle is added
- `vehicle.status_changed` - When vehicle status changes
- `driver.created` - When a driver is registered
- `driver.status_changed` - When driver status changes
- `network.relationship_established` - When broker-carrier network is created
- `network.relationship_activated` - When network relationship is activated
- `maintenance.scheduled` - When vehicle maintenance is scheduled
- `maintenance.completed` - When vehicle maintenance is completed

### Consumed Events
- `user.registered` - From User Management Service
- `subscription.updated` - From Subscription Service
- `trip.completed` - From Trip Management Service

## 🔒 Security

### Authentication
- JWT bearer token authentication
- Role-based authorization (Admin, Broker, Carrier, Driver)

### Authorization Roles
- **Admin**: Full system access and management
- **Broker**: Network management and carrier relationships
- **Carrier**: Fleet and driver management
- **Driver**: Profile and document management

## 📚 API Endpoints

### Carrier Management
- `GET /api/v1/carriers` - Get carriers with pagination
- `GET /api/v1/carriers/{id}` - Get carrier by ID
- `POST /api/v1/carriers` - Create new carrier
- `PUT /api/v1/carriers/{id}/status` - Update carrier status
- `GET /api/v1/carriers/active` - Get active carriers

### Vehicle Management
- `GET /api/v1/vehicles` - Get vehicles with pagination
- `GET /api/v1/vehicles/{id}` - Get vehicle by ID
- `POST /api/v1/vehicles` - Register new vehicle
- `PUT /api/v1/vehicles/{id}/status` - Update vehicle status
- `POST /api/v1/vehicles/{id}/maintenance` - Schedule maintenance

### Driver Management
- `GET /api/v1/drivers` - Get drivers with pagination
- `GET /api/v1/drivers/{id}` - Get driver by ID
- `POST /api/v1/drivers` - Register new driver
- `PUT /api/v1/drivers/{id}/status` - Update driver status
- `PUT /api/v1/drivers/{id}/location` - Update driver location

### Network Management
- `GET /api/v1/networks` - Get network relationships
- `POST /api/v1/networks` - Establish network relationship
- `PUT /api/v1/networks/{id}/activate` - Activate network
- `PUT /api/v1/networks/{id}/suspend` - Suspend network

## 🧪 Testing

### Unit Tests
```bash
dotnet test NetworkFleetManagement.Tests
```

### Integration Tests
```bash
dotnet test NetworkFleetManagement.IntegrationTests
```

## 📈 Monitoring and Observability

### Health Checks
- Database connectivity
- External service dependencies
- Message broker connectivity

### Logging
- Structured logging with Serilog
- Request/response logging
- Performance metrics
- Error tracking

### Metrics
- Carrier onboarding rates
- Fleet utilization metrics
- Driver performance analytics
- Network relationship health

## 🔄 Development Workflow

1. Create feature branch
2. Implement changes following clean architecture
3. Add/update unit tests
4. Run integration tests
5. Update documentation
6. Create pull request

## 📝 Contributing

1. Follow clean architecture principles
2. Maintain high test coverage
3. Use domain-driven design patterns
4. Follow SOLID principles
5. Document API changes

## 🚀 Deployment

### Environment Variables
- `ConnectionStrings__DefaultConnection`
- `Authentication__Authority`
- `Authentication__Audience`
- `RabbitMQ__Host`

### Docker Compose
```yaml
version: '3.8'
services:
  networkfleet-api:
    image: networkfleet-service
    ports:
      - "5005:80"
    environment:
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=TLI_NetworkFleetManagement;Username=timescale;Password=timescale
```

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
