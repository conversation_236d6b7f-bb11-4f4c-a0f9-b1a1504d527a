using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SubscriptionController : ControllerBase
{
    private readonly ISubscriptionBillingService _subscriptionService;
    private readonly ILogger<SubscriptionController> _logger;

    public SubscriptionController(
        ISubscriptionBillingService subscriptionService,
        ILogger<SubscriptionController> logger)
    {
        _subscriptionService = subscriptionService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new subscription
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<Subscription>> CreateSubscription([FromBody] CreateSubscriptionRequest request)
    {
        try
        {
            var subscription = await _subscriptionService.CreateSubscriptionAsync(request);
            
            _logger.LogInformation("Subscription created: {SubscriptionId} for user {UserId}",
                subscription.Id, request.UserId);

            return CreatedAtAction(nameof(GetSubscription), new { id = subscription.Id }, subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid subscription creation request");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription for user {UserId}", request.UserId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get subscription by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<Subscription>> GetSubscription(Guid id)
    {
        try
        {
            var subscription = await _subscriptionService.GetSubscriptionAsync(id);
            if (subscription == null)
            {
                return NotFound($"Subscription {id} not found");
            }

            return Ok(subscription);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user subscriptions
    /// </summary>
    [HttpGet("user/{userId}")]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<List<Subscription>>> GetUserSubscriptions(Guid userId)
    {
        try
        {
            var subscriptions = await _subscriptionService.GetUserSubscriptionsAsync(userId);
            return Ok(subscriptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update subscription
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<Subscription>> UpdateSubscription(
        Guid id, 
        [FromBody] UpdateSubscriptionRequest request)
    {
        try
        {
            var subscription = await _subscriptionService.UpdateSubscriptionAsync(id, request);
            
            _logger.LogInformation("Subscription updated: {SubscriptionId}", id);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid subscription update request for {SubscriptionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Cancel subscription
    /// </summary>
    [HttpPost("{id}/cancel")]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<Subscription>> CancelSubscription(
        Guid id, 
        [FromBody] CancelSubscriptionRequest request)
    {
        try
        {
            var subscription = await _subscriptionService.CancelSubscriptionAsync(id, request.Reason);
            
            _logger.LogInformation("Subscription cancelled: {SubscriptionId}", id);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid subscription cancellation for {SubscriptionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Pause subscription
    /// </summary>
    [HttpPost("{id}/pause")]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<Subscription>> PauseSubscription(
        Guid id, 
        [FromBody] PauseSubscriptionRequest request)
    {
        try
        {
            var subscription = await _subscriptionService.PauseSubscriptionAsync(id, request.Reason);
            
            _logger.LogInformation("Subscription paused: {SubscriptionId}", id);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid subscription pause for {SubscriptionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resume subscription
    /// </summary>
    [HttpPost("{id}/resume")]
    [Authorize(Roles = "Admin,User")]
    public async Task<ActionResult<Subscription>> ResumeSubscription(Guid id)
    {
        try
        {
            var subscription = await _subscriptionService.ResumeSubscriptionAsync(id);
            
            _logger.LogInformation("Subscription resumed: {SubscriptionId}", id);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid subscription resume for {SubscriptionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process billing for subscription
    /// </summary>
    [HttpPost("{id}/bill")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<BillingResult>> ProcessBilling(Guid id)
    {
        try
        {
            var result = await _subscriptionService.ProcessBillingAsync(id);
            
            _logger.LogInformation("Billing processed for subscription {SubscriptionId}: {Success}",
                id, result.IsSuccess);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing billing for subscription {SubscriptionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get subscriptions due for billing
    /// </summary>
    [HttpGet("due-for-billing")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<List<Subscription>>> GetSubscriptionsDueForBilling()
    {
        try
        {
            var subscriptions = await _subscriptionService.GetSubscriptionsDueForBillingAsync();
            return Ok(subscriptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions due for billing");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process batch billing
    /// </summary>
    [HttpPost("batch-billing")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<List<BillingResult>>> ProcessBatchBilling([FromBody] BatchBillingRequest request)
    {
        try
        {
            var results = await _subscriptionService.ProcessBatchBillingAsync(request.SubscriptionIds);
            
            _logger.LogInformation("Batch billing processed for {Count} subscriptions", request.SubscriptionIds.Count);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch billing");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get subscription analytics
    /// </summary>
    [HttpGet("analytics")]
    [Authorize(Roles = "Admin,Finance")]
    public async Task<ActionResult<SubscriptionAnalytics>> GetSubscriptionAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var analytics = await _subscriptionService.GetSubscriptionAnalyticsAsync(fromDate, toDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription analytics");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request DTOs
public class CancelSubscriptionRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class PauseSubscriptionRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class BatchBillingRequest
{
    public List<Guid> SubscriptionIds { get; set; } = new();
}
