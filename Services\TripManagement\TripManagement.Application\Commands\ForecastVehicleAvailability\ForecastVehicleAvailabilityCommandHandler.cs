using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ForecastVehicleAvailability;

public class ForecastVehicleAvailabilityCommandHandler : IRequestHandler<ForecastVehicleAvailabilityCommand, VehicleAvailabilityForecast>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<ForecastVehicleAvailabilityCommandHandler> _logger;

    public ForecastVehicleAvailabilityCommandHandler(
        IVehicleRepository vehicleRepository,
        ITripRepository tripRepository,
        ILogger<ForecastVehicleAvailabilityCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository;
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<VehicleAvailabilityForecast> Handle(ForecastVehicleAvailabilityCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Forecasting vehicle availability from {StartDate} to {EndDate}", 
                request.StartDate, request.EndDate);

            // Get vehicles to analyze
            var vehicles = request.VehicleId.HasValue
                ? new List<Domain.Entities.Vehicle> { await _vehicleRepository.GetByIdAsync(request.VehicleId.Value, cancellationToken) }.Where(v => v != null).ToList()!
                : request.CarrierId.HasValue
                    ? await _vehicleRepository.GetByCarrierIdAsync(request.CarrierId.Value, cancellationToken)
                    : await GetAllVehiclesAsync(cancellationToken);

            var vehicleAvailabilities = new List<VehicleAvailabilityDto>();

            foreach (var vehicle in vehicles)
            {
                var availability = await AnalyzeVehicleAvailabilityAsync(vehicle, request, cancellationToken);
                vehicleAvailabilities.Add(availability);
            }

            var summary = CalculateAvailabilitySummary(vehicleAvailabilities);

            var forecast = new VehicleAvailabilityForecast
            {
                ForecastDate = DateTime.UtcNow,
                VehicleAvailabilities = vehicleAvailabilities,
                Summary = summary
            };

            _logger.LogInformation("Vehicle availability forecast completed for {VehicleCount} vehicles", 
                vehicleAvailabilities.Count);

            return forecast;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting vehicle availability");
            throw;
        }
    }

    private async Task<List<Domain.Entities.Vehicle>> GetAllVehiclesAsync(CancellationToken cancellationToken)
    {
        // This would need to be implemented in the repository
        // For now, return empty list
        return new List<Domain.Entities.Vehicle>();
    }

    private async Task<VehicleAvailabilityDto> AnalyzeVehicleAvailabilityAsync(
        Domain.Entities.Vehicle vehicle, 
        ForecastVehicleAvailabilityCommand request, 
        CancellationToken cancellationToken)
    {
        var availabilityPeriods = new List<AvailabilityPeriod>();
        var unavailabilityPeriods = new List<UnavailabilityPeriod>();

        // Check current availability
        var isCurrentlyAvailable = vehicle.IsAvailable;
        DateTime? nextAvailableDate = null;

        // Analyze trips if requested
        if (request.IncludeTrips)
        {
            var vehicleTrips = await _tripRepository.GetByVehicleIdAsync(vehicle.Id, cancellationToken);
            var activeTrips = vehicleTrips.Where(t => 
                t.Status == TripStatus.Assigned || 
                t.Status == TripStatus.InProgress ||
                (t.EstimatedStartTime >= request.StartDate && t.EstimatedStartTime <= request.EndDate))
                .ToList();

            foreach (var trip in activeTrips)
            {
                var startDate = trip.StartedAt ?? trip.EstimatedStartTime;
                var endDate = trip.CompletedAt ?? trip.EstimatedEndTime;

                if (startDate <= request.EndDate && endDate >= request.StartDate)
                {
                    unavailabilityPeriods.Add(new UnavailabilityPeriod
                    {
                        StartDate = startDate > request.StartDate ? startDate : request.StartDate,
                        EndDate = endDate < request.EndDate ? endDate : request.EndDate,
                        Reason = UnavailabilityReason.OnTrip,
                        Description = $"Trip {trip.TripNumber}",
                        RelatedEntityId = trip.Id
                    });
                }
            }
        }

        // Analyze maintenance if requested
        if (request.IncludeMaintenance)
        {
            var maintenanceRecords = vehicle.MaintenanceRecords
                .Where(m => m.ScheduledDate >= request.StartDate && m.ScheduledDate <= request.EndDate)
                .ToList();

            foreach (var maintenance in maintenanceRecords)
            {
                var startDate = maintenance.StartedAt ?? maintenance.ScheduledDate;
                var endDate = maintenance.CompletedAt ?? 
                    (maintenance.EstimatedDurationHours.HasValue 
                        ? startDate.AddHours(maintenance.EstimatedDurationHours.Value)
                        : startDate.AddHours(8)); // Default 8 hours

                if (startDate <= request.EndDate && endDate >= request.StartDate)
                {
                    unavailabilityPeriods.Add(new UnavailabilityPeriod
                    {
                        StartDate = startDate > request.StartDate ? startDate : request.StartDate,
                        EndDate = endDate < request.EndDate ? endDate : request.EndDate,
                        Reason = UnavailabilityReason.Maintenance,
                        Description = $"Maintenance: {maintenance.Description}",
                        RelatedEntityId = maintenance.Id
                    });
                }
            }
        }

        // Check document expiry if requested
        if (request.IncludeDocumentExpiry)
        {
            if (!vehicle.IsInsuranceValid)
            {
                unavailabilityPeriods.Add(new UnavailabilityPeriod
                {
                    StartDate = vehicle.InsuranceExpiryDate ?? request.StartDate,
                    EndDate = request.EndDate,
                    Reason = UnavailabilityReason.ExpiredDocuments,
                    Description = "Expired insurance"
                });
            }

            if (!vehicle.IsFitnessValid)
            {
                unavailabilityPeriods.Add(new UnavailabilityPeriod
                {
                    StartDate = vehicle.FitnessExpiryDate ?? request.StartDate,
                    EndDate = request.EndDate,
                    Reason = UnavailabilityReason.ExpiredDocuments,
                    Description = "Expired fitness certificate"
                });
            }
        }

        // Calculate availability periods (gaps between unavailability periods)
        availabilityPeriods = CalculateAvailabilityPeriods(request.StartDate, request.EndDate, unavailabilityPeriods);

        // Calculate utilization
        var totalPeriod = request.EndDate - request.StartDate;
        var unavailableTime = unavailabilityPeriods.Sum(u => (u.EndDate - u.StartDate).TotalHours);
        var utilizationPercentage = totalPeriod.TotalHours > 0 ? (unavailableTime / totalPeriod.TotalHours) * 100 : 0;

        // Find next available date
        if (!isCurrentlyAvailable)
        {
            var currentUnavailability = unavailabilityPeriods
                .Where(u => u.StartDate <= DateTime.UtcNow && u.EndDate > DateTime.UtcNow)
                .OrderBy(u => u.EndDate)
                .FirstOrDefault();

            nextAvailableDate = currentUnavailability?.EndDate;
        }

        return new VehicleAvailabilityDto
        {
            VehicleId = vehicle.Id,
            RegistrationNumber = vehicle.RegistrationNumber,
            DisplayName = vehicle.DisplayName,
            AvailabilityPeriods = availabilityPeriods,
            UnavailabilityPeriods = unavailabilityPeriods,
            UtilizationPercentage = utilizationPercentage,
            IsCurrentlyAvailable = isCurrentlyAvailable,
            NextAvailableDate = nextAvailableDate
        };
    }

    private List<AvailabilityPeriod> CalculateAvailabilityPeriods(
        DateTime startDate, 
        DateTime endDate, 
        List<UnavailabilityPeriod> unavailabilityPeriods)
    {
        var availabilityPeriods = new List<AvailabilityPeriod>();
        
        // Sort unavailability periods by start date
        var sortedUnavailability = unavailabilityPeriods.OrderBy(u => u.StartDate).ToList();
        
        var currentDate = startDate;
        
        foreach (var unavailability in sortedUnavailability)
        {
            if (unavailability.StartDate > currentDate)
            {
                // There's an availability gap
                availabilityPeriods.Add(new AvailabilityPeriod
                {
                    StartDate = currentDate,
                    EndDate = unavailability.StartDate,
                    Type = AvailabilityType.Available
                });
            }
            
            currentDate = unavailability.EndDate > currentDate ? unavailability.EndDate : currentDate;
        }
        
        // Check if there's availability after the last unavailability period
        if (currentDate < endDate)
        {
            availabilityPeriods.Add(new AvailabilityPeriod
            {
                StartDate = currentDate,
                EndDate = endDate,
                Type = AvailabilityType.Available
            });
        }
        
        return availabilityPeriods;
    }

    private AvailabilitySummary CalculateAvailabilitySummary(List<VehicleAvailabilityDto> vehicleAvailabilities)
    {
        var totalVehicles = vehicleAvailabilities.Count;
        var availableVehicles = vehicleAvailabilities.Count(v => v.IsCurrentlyAvailable);
        var unavailableVehicles = totalVehicles - availableVehicles;
        
        var vehiclesInMaintenance = vehicleAvailabilities.Count(v => 
            v.UnavailabilityPeriods.Any(u => u.Reason == UnavailabilityReason.Maintenance && 
                u.StartDate <= DateTime.UtcNow && u.EndDate > DateTime.UtcNow));
        
        var vehiclesOnTrips = vehicleAvailabilities.Count(v => 
            v.UnavailabilityPeriods.Any(u => u.Reason == UnavailabilityReason.OnTrip && 
                u.StartDate <= DateTime.UtcNow && u.EndDate > DateTime.UtcNow));
        
        var vehiclesWithExpiredDocuments = vehicleAvailabilities.Count(v => 
            v.UnavailabilityPeriods.Any(u => u.Reason == UnavailabilityReason.ExpiredDocuments));
        
        var averageUtilization = totalVehicles > 0 
            ? vehicleAvailabilities.Average(v => v.UtilizationPercentage) 
            : 0;

        return new AvailabilitySummary
        {
            TotalVehicles = totalVehicles,
            AvailableVehicles = availableVehicles,
            UnavailableVehicles = unavailableVehicles,
            VehiclesInMaintenance = vehiclesInMaintenance,
            VehiclesOnTrips = vehiclesOnTrips,
            VehiclesWithExpiredDocuments = vehiclesWithExpiredDocuments,
            AverageUtilization = averageUtilization
        };
    }
}
