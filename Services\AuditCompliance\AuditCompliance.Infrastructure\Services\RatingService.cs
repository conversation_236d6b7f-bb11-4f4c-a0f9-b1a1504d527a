using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Services;

public class RatingService : IRatingService
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<RatingService> _logger;

    public RatingService(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<RatingService> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<Guid> CreateRatingAsync(CreateServiceProviderRatingDto ratingDto, Guid shipperId, string shipperName, CancellationToken cancellationToken = default)
    {
        try
        {
            var overallRating = RatingScore.FromNumeric(ratingDto.OverallRating);

            var rating = new ServiceProviderRating(
                shipperId,
                shipperName,
                ratingDto.TransportCompanyId,
                ratingDto.TransportCompanyName,
                overallRating,
                ratingDto.ServiceCompletedAt,
                ratingDto.OrderId,
                ratingDto.OrderNumber,
                ratingDto.TripId,
                ratingDto.TripNumber,
                ratingDto.ReviewTitle,
                ratingDto.ReviewComment,
                ratingDto.IsAnonymous);

            // Add category ratings
            foreach (var categoryRating in ratingDto.CategoryRatings)
            {
                var categoryScore = RatingScore.FromNumeric(categoryRating.Rating, categoryRating.Comment);
                rating.AddCategoryRating(categoryRating.Category, categoryScore, categoryRating.Comment);
            }

            var savedRating = await _ratingRepository.AddAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating created: {RatingId} for transport company: {TransportCompanyId}", 
                savedRating.Id, ratingDto.TransportCompanyId);

            return savedRating.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating service provider rating for transport company: {TransportCompanyId}", 
                ratingDto.TransportCompanyId);
            throw;
        }
    }

    public async Task<ServiceProviderRatingDto?> GetRatingAsync(Guid ratingId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                return null;

            return new ServiceProviderRatingDto
            {
                Id = rating.Id,
                ShipperId = rating.ShipperId,
                ShipperName = rating.ShipperName,
                TransportCompanyId = rating.TransportCompanyId,
                TransportCompanyName = rating.TransportCompanyName,
                OrderId = rating.OrderId,
                OrderNumber = rating.OrderNumber,
                TripId = rating.TripId,
                TripNumber = rating.TripNumber,
                OverallRating = rating.OverallRating.NumericValue,
                Status = rating.Status,
                ReviewTitle = rating.ReviewTitle,
                ReviewComment = rating.ReviewComment,
                IsAnonymous = rating.IsAnonymous,
                ServiceCompletedAt = rating.ServiceCompletedAt,
                ReviewSubmittedAt = rating.ReviewSubmittedAt,
                CreatedAt = rating.CreatedAt,
                CategoryRatings = rating.CategoryRatings.Select(cr => new CategoryRatingDto
                {
                    Id = cr.Id,
                    Category = cr.Category,
                    CategoryName = cr.GetCategoryName(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList(),
                ReportedIssues = rating.ReportedIssues.Select(issue => new ServiceIssueDto
                {
                    Id = issue.Id,
                    IssueType = issue.IssueType,
                    IssueTypeName = issue.GetIssueTypeName(),
                    Priority = issue.Priority,
                    Description = issue.Description,
                    Evidence = issue.Evidence,
                    Status = issue.Status,
                    ReportedAt = issue.ReportedAt,
                    ResolvedAt = issue.ResolvedAt,
                    Resolution = issue.Resolution,
                    ResolvedByName = issue.ResolvedByName
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider rating: {RatingId}", ratingId);
            throw;
        }
    }

    public async Task<RatingResultDto> GetRatingsAsync(RatingQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var ratings = await _ratingRepository.GetRatingsAsync(
                query.ShipperId,
                query.TransportCompanyId,
                query.Status,
                query.MinRating,
                query.MaxRating,
                query.FromDate,
                query.ToDate,
                query.PageNumber,
                query.PageSize,
                cancellationToken);

            var totalCount = await _ratingRepository.GetRatingsCountAsync(
                query.ShipperId,
                query.TransportCompanyId,
                query.Status,
                query.MinRating,
                query.MaxRating,
                query.FromDate,
                query.ToDate,
                cancellationToken);

            var ratingDtos = ratings.Select(r => new ServiceProviderRatingDto
            {
                Id = r.Id,
                ShipperId = r.ShipperId,
                ShipperName = r.ShipperName,
                TransportCompanyId = r.TransportCompanyId,
                TransportCompanyName = r.TransportCompanyName,
                OrderId = r.OrderId,
                OrderNumber = r.OrderNumber,
                TripId = r.TripId,
                TripNumber = r.TripNumber,
                OverallRating = r.OverallRating.NumericValue,
                Status = r.Status,
                ReviewTitle = r.ReviewTitle,
                ReviewComment = r.ReviewComment,
                IsAnonymous = r.IsAnonymous,
                ServiceCompletedAt = r.ServiceCompletedAt,
                ReviewSubmittedAt = r.ReviewSubmittedAt,
                CreatedAt = r.CreatedAt,
                CategoryRatings = r.CategoryRatings.Select(cr => new CategoryRatingDto
                {
                    Id = cr.Id,
                    Category = cr.Category,
                    CategoryName = cr.GetCategoryName(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList()
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);

            return new RatingResultDto
            {
                Ratings = ratingDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasNextPage = query.PageNumber < totalPages,
                HasPreviousPage = query.PageNumber > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider ratings");
            throw;
        }
    }

    public async Task<TransportCompanyRatingSummaryDto> GetTransportCompanyRatingSummaryAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        try
        {
            var ratings = await _ratingRepository.GetRatingsByTransportCompanyAsync(transportCompanyId, cancellationToken);
            
            if (!ratings.Any())
            {
                return new TransportCompanyRatingSummaryDto
                {
                    TransportCompanyId = transportCompanyId,
                    TransportCompanyName = "Unknown",
                    AverageRating = 0,
                    TotalRatings = 0
                };
            }

            var activeRatings = ratings.Where(r => r.Status == RatingStatus.Active).ToList();
            var averageRating = activeRatings.Any() ? activeRatings.Average(r => r.OverallRating.NumericValue) : 0;

            var summary = new TransportCompanyRatingSummaryDto
            {
                TransportCompanyId = transportCompanyId,
                TransportCompanyName = activeRatings.FirstOrDefault()?.TransportCompanyName ?? "Unknown",
                AverageRating = averageRating,
                TotalRatings = activeRatings.Count,
                FiveStarRatings = activeRatings.Count(r => r.OverallRating.Scale == RatingScale.FiveStars),
                FourStarRatings = activeRatings.Count(r => r.OverallRating.Scale == RatingScale.FourStars),
                ThreeStarRatings = activeRatings.Count(r => r.OverallRating.Scale == RatingScale.ThreeStars),
                TwoStarRatings = activeRatings.Count(r => r.OverallRating.Scale == RatingScale.TwoStars),
                OneStarRatings = activeRatings.Count(r => r.OverallRating.Scale == RatingScale.OneStar),
                TotalIssuesReported = activeRatings.SelectMany(r => r.ReportedIssues).Count(),
                ResolvedIssues = activeRatings.SelectMany(r => r.ReportedIssues)
                    .Count(i => i.Status == IssueResolutionStatus.Resolved || i.Status == IssueResolutionStatus.Closed),
                LastRatingDate = activeRatings.Any() ? activeRatings.Max(r => r.CreatedAt) : DateTime.MinValue
            };

            // Calculate category averages
            var categoryGroups = activeRatings
                .SelectMany(r => r.CategoryRatings)
                .GroupBy(cr => cr.Category);

            summary.CategoryAverages = categoryGroups.Select(g => new CategoryRatingSummaryDto
            {
                Category = g.Key,
                CategoryName = g.First().GetCategoryName(),
                AverageRating = g.Average(cr => cr.Rating.NumericValue),
                TotalRatings = g.Count()
            }).ToList();

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving transport company rating summary: {TransportCompanyId}", transportCompanyId);
            throw;
        }
    }

    public async Task SubmitRatingAsync(Guid ratingId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                throw new InvalidOperationException($"Service provider rating not found: {ratingId}");

            rating.SubmitReview();
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating submitted: {RatingId}", ratingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting service provider rating: {RatingId}", ratingId);
            throw;
        }
    }

    public async Task FlagRatingAsync(Guid ratingId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                throw new InvalidOperationException($"Service provider rating not found: {ratingId}");

            rating.FlagForReview(reason);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating flagged: {RatingId}, Reason: {Reason}", ratingId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error flagging service provider rating: {RatingId}", ratingId);
            throw;
        }
    }

    public async Task RemoveRatingAsync(Guid ratingId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(ratingId, cancellationToken);
            if (rating == null)
                throw new InvalidOperationException($"Service provider rating not found: {ratingId}");

            rating.Remove(reason);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service provider rating removed: {RatingId}, Reason: {Reason}", ratingId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing service provider rating: {RatingId}", ratingId);
            throw;
        }
    }

    public async Task<Guid> ReportServiceIssueAsync(ReportServiceIssueDto issueDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(issueDto.ServiceProviderRatingId, cancellationToken);
            if (rating == null)
                throw new InvalidOperationException($"Service provider rating not found: {issueDto.ServiceProviderRatingId}");

            rating.ReportIssue(issueDto.IssueType, issueDto.Priority, issueDto.Description, issueDto.Evidence);
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            var reportedIssue = rating.ReportedIssues.OrderByDescending(i => i.CreatedAt).First();

            _logger.LogInformation("Service issue reported: {IssueId} for rating: {RatingId}", 
                reportedIssue.Id, issueDto.ServiceProviderRatingId);

            return reportedIssue.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting service issue for rating: {RatingId}", issueDto.ServiceProviderRatingId);
            throw;
        }
    }

    public async Task ResolveServiceIssueAsync(Guid issueId, string resolution, Guid resolvedBy, string resolvedByName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Find the rating that contains this issue
            var ratings = await _ratingRepository.GetRatingsAsync(cancellationToken: cancellationToken);
            var rating = ratings.FirstOrDefault(r => r.ReportedIssues.Any(i => i.Id == issueId));
            
            if (rating == null)
                throw new InvalidOperationException($"Service issue not found: {issueId}");

            var issue = rating.ReportedIssues.First(i => i.Id == issueId);
            issue.Resolve(resolution, resolvedBy, resolvedByName);
            
            await _ratingRepository.UpdateAsync(rating, cancellationToken);

            _logger.LogInformation("Service issue resolved: {IssueId} by {ResolvedByName}", issueId, resolvedByName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving service issue: {IssueId}", issueId);
            throw;
        }
    }
}
