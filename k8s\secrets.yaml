# NOTE: This is a template file. Replace base64 encoded values with actual secrets.
# Use: echo -n "your-secret-value" | base64
apiVersion: v1
kind: Secret
metadata:
  name: tli-secrets
  namespace: tli-microservices
type: Opaque
data:
  # Database Secrets (base64 encoded)
  db-connection-string: SG9zdD1wb3N0Z3Jlcy1zZXJ2aWNlO1BvcnQ9NTQzMjtEYXRhYmFzZT1UTElfUHJvZHVjdGlvbjtVc2VybmFtZT10bGlfdXNlcjtQYXNzd29yZD1zZWN1cmVfcGFzc3dvcmQ=
  db-username: dGxpX3VzZXI=
  db-password: c2VjdXJlX3Bhc3N3b3Jk
  
  # Authentication Secrets
  jwt-secret-key: VExJLVN1cGVyLVNlY3VyZS1LZXktRm9yLUpXVC1Ub2tlbi1HZW5lcmF0aW9uLTIwMjQtUFJPRA==
  jwt-issuer: VExJLU1pY3Jvc2VydmljZXM=
  jwt-audience: VExJLUFQSQ==
  
  # Redis Secrets
  redis-connection-string: cmVkaXMtc2VydmljZTo2Mzc5
  redis-password: c2VjdXJlX3JlZGlzX3Bhc3N3b3Jk
  
  # RabbitMQ Secrets
  rabbitmq-connection-string: YW1xcDovL3RsaV91c2VyOnNlY3VyZV9yYWJiaXRtcV9wYXNzd29yZEByYWJiaXRtcS1zZXJ2aWNlOjU2NzIv
  rabbitmq-username: dGxpX3VzZXI=
  rabbitmq-password: c2VjdXJlX3JhYmJpdG1xX3Bhc3N3b3Jk
  
  # External Service API Keys
  sendgrid-api-key: eW91ci1zZW5kZ3JpZC1hcGkta2V5LWhlcmU=
  twilio-account-sid: eW91ci10d2lsaW8tYWNjb3VudC1zaWQtaGVyZQ==
  twilio-auth-token: eW91ci10d2lsaW8tYXV0aC10b2tlbi1oZXJl
  twilio-from-number: eW91ci10d2lsaW8tZnJvbS1udW1iZXItaGVyZQ==
  
  # Payment Gateway Secrets
  razorpay-key-id: eW91ci1yYXpvcnBheS1rZXktaWQtaGVyZQ==
  razorpay-key-secret: eW91ci1yYXpvcnBheS1rZXktc2VjcmV0LWhlcmU=
  razorpay-webhook-secret: eW91ci1yYXpvcnBheS13ZWJob29rLXNlY3JldC1oZXJl
  
  stripe-publishable-key: eW91ci1zdHJpcGUtcHVibGlzaGFibGUta2V5LWhlcmU=
  stripe-secret-key: eW91ci1zdHJpcGUtc2VjcmV0LWtleS1oZXJl
  stripe-webhook-secret: eW91ci1zdHJpcGUtd2ViaG9vay1zZWNyZXQtaGVyZQ==
  
  # Maps API Keys
  google-maps-api-key: eW91ci1nb29nbGUtbWFwcy1hcGkta2V5LWhlcmU=
  mapbox-access-token: eW91ci1tYXBib3gtYWNjZXNzLXRva2VuLWhlcmU=
  
  # File Storage Secrets
  azure-storage-connection-string: eW91ci1henVyZS1zdG9yYWdlLWNvbm5lY3Rpb24tc3RyaW5nLWhlcmU=
  aws-access-key: eW91ci1hd3MtYWNjZXNzLWtleS1oZXJl
  aws-secret-key: eW91ci1hd3Mtc2VjcmV0LWtleS1oZXJl
  aws-s3-bucket: eW91ci1hd3MtczMtYnVja2V0LWhlcmU=
  aws-region: dXMtZWFzdC0x
  
  minio-access-key: bWluaW9hZG1pbg==
  minio-secret-key: bWluaW9hZG1pbjEyMw==
  minio-bucket: dGxpLWRvY3VtZW50cw==
  
  # Monitoring Secrets
  application-insights-key: eW91ci1hcHBsaWNhdGlvbi1pbnNpZ2h0cy1rZXktaGVyZQ==
  seq-server-url: aHR0cDovL3NlcS1zZXJ2aWNlOjUzNDE=
  seq-api-key: eW91ci1zZXEtYXBpLWtleS1oZXJl
  
  # SMTP Secrets
  smtp-host: c210cC5nbWFpbC5jb20=
  smtp-port: NTg3
  smtp-username: eW91ci1zbXRwLXVzZXJuYW1lLWhlcmU=
  smtp-password: eW91ci1zbXRwLXBhc3N3b3JkLWhlcmU=
  from-email: bm9yZXBseUB0bGkuY29t
  
  # API Keys for service-to-service communication
  api-key-1: YXBpLWtleS0xLXNlY3VyZS12YWx1ZS1oZXJl
  api-key-2: YXBpLWtleS0yLXNlY3VyZS12YWx1ZS1oZXJl

---
# Separate secret for TLS certificates
apiVersion: v1
kind: Secret
metadata:
  name: tli-tls
  namespace: tli-microservices
type: kubernetes.io/tls
data:
  # Replace with your actual certificate and key (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...
