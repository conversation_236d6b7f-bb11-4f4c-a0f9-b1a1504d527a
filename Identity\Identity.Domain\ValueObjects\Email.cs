using System;
using System.Text.RegularExpressions;
using Identity.Domain.Exceptions;

namespace Identity.Domain.ValueObjects
{
    public class Email
    {
        public string Value { get; }

        private Email(string value)
        {
            Value = value;
        }

        public static Email Create(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                throw new DomainException("Email cannot be empty");

            email = email.Trim();

            if (email.Length > 320)
                throw new DomainException("Email is too long");

            if (!IsValidEmail(email))
                throw new DomainException("Email is invalid");

            return new Email(email);
        }

        private static bool IsValidEmail(string email)
        {
            // Simple regex for email validation
            var regex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
            return regex.IsMatch(email);
        }

        public override string ToString()
        {
            return Value;
        }

        public override bool Equals(object obj)
        {
            if (obj is not Email other)
                return false;

            return string.Equals(Value, other.Value, StringComparison.OrdinalIgnoreCase);
        }

        public override int GetHashCode()
        {
            return Value.ToLowerInvariant().GetHashCode();
        }
    }
}
