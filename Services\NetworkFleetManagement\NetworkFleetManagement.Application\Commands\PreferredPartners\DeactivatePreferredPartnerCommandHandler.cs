using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class DeactivatePreferredPartnerCommandHandler : IRequestHandler<DeactivatePreferredPartnerCommand, bool>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly ILogger<DeactivatePreferredPartnerCommandHandler> _logger;

    public DeactivatePreferredPartnerCommandHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        ILogger<DeactivatePreferredPartnerCommandHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _logger = logger;
    }

    public async Task<bool> Handle(DeactivatePreferredPartnerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.Id, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                _logger.LogWarning("Preferred partner {Id} not found or user {UserId} doesn't have permission to deactivate it", 
                    request.Id, request.UserId);
                return false;
            }

            partner.Deactivate(request.Reason);
            await _preferredPartnerRepository.UpdateAsync(partner, cancellationToken);

            _logger.LogInformation("Preferred partner {Id} deactivated by user {UserId} with reason: {Reason}", 
                request.Id, request.UserId, request.Reason);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating preferred partner {Id}", request.Id);
            throw;
        }
    }
}
