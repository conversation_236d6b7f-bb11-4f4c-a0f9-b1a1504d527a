using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UserManagement.Application.UserProfiles.Commands.CreateUserProfile;
using UserManagement.Application.UserProfiles.Queries.GetProfile;
using UserManagement.Application.UserProfiles.Queries.GetProfileByUserId;
using UserManagement.Application.UserProfiles.Commands.UpdatePersonalDetails;
using UserManagement.Application.UserProfiles.Commands.UpdateCompanyDetails;
using UserManagement.Application.UserProfiles.Commands.UpdateAddress;
using UserManagement.Application.UserProfiles.Commands.SubmitForReview;
using UserManagement.Domain.Entities;

namespace UserManagement.API.Controllers
{
    [Authorize]
    public class UserProfilesController : BaseController
    {
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateProfile([FromBody] CreateUserProfileRequest request)
        {
            var command = new CreateUserProfileCommand
            {
                UserId = request.UserId,
                UserType = request.UserType,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PhoneNumber = request.PhoneNumber
            };

            var profileId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetProfile), new { id = profileId }, new { id = profileId });
        }

        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetProfile(Guid id)
        {
            var query = new GetProfileQuery { Id = id };
            var result = await Mediator.Send(query);

            if (result == null)
            {
                return NotFound(new { message = "Profile not found" });
            }

            return Ok(result);
        }

        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetProfileByUserId(Guid userId)
        {
            var query = new GetProfileByUserIdQuery { UserId = userId };
            var result = await Mediator.Send(query);

            if (result == null)
            {
                return NotFound(new { message = "Profile not found" });
            }

            return Ok(result);
        }

        [HttpPut("{id}/personal-details")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdatePersonalDetails(Guid id, [FromBody] UpdatePersonalDetailsRequest request)
        {
            var command = new UpdatePersonalDetailsCommand
            {
                Id = id,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PhoneNumber = request.PhoneNumber
            };

            var result = await Mediator.Send(command);
            return Ok(new { message = "Personal details updated successfully", success = result });
        }

        [HttpPut("{id}/company-details")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateCompanyDetails(Guid id, [FromBody] UpdateCompanyDetailsRequest request)
        {
            var command = new UpdateCompanyDetailsCommand
            {
                Id = id,
                CompanyName = request.CompanyName,
                GstNumber = request.GstNumber,
                PanNumber = request.PanNumber
            };

            var result = await Mediator.Send(command);
            return Ok(new { message = "Company details updated successfully", success = result });
        }

        [HttpPut("{id}/address")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateAddress(Guid id, [FromBody] UpdateAddressRequest request)
        {
            var command = new UpdateAddressCommand
            {
                Id = id,
                Address = request.Address,
                City = request.City,
                State = request.State,
                PostalCode = request.PostalCode,
                Country = request.Country
            };

            var result = await Mediator.Send(command);
            return Ok(new { message = "Address updated successfully", success = result });
        }

        [HttpPost("{id}/submit-for-review")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> SubmitForReview(Guid id)
        {
            var command = new SubmitForReviewCommand { Id = id };
            var result = await Mediator.Send(command);
            return Ok(new { message = "Profile submitted for review successfully", success = result });
        }
    }

    public class CreateUserProfileRequest
    {
        public Guid UserId { get; set; }
        public UserType UserType { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class UpdatePersonalDetailsRequest
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class UpdateCompanyDetailsRequest
    {
        public string CompanyName { get; set; }
        public string GstNumber { get; set; }
        public string PanNumber { get; set; }
    }

    public class UpdateAddressRequest
    {
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
    }
}
