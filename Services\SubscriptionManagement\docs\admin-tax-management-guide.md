# Admin Tax Management Guide

## Overview

This guide provides step-by-step instructions for administrators to manage tax configurations, exemptions, and compliance within the subscription management system.

## Prerequisites

- Admin role access to the system
- Understanding of regional tax requirements
- Knowledge of your organization's tax compliance needs

## Tax Configuration Management

### Global Tax Configuration

Global tax configurations apply system-wide unless overridden by plan-specific settings.

#### Setting Up GST for India

1. **Navigate to Admin Tax Panel**
   - Access: `/admin/tax/global-configurations`
   - Required Role: Admin

2. **Create GST Configuration**
   ```json
   POST /api/admin/tax/global-configurations
   {
     "taxType": "GST",
     "rate": 18.00,
     "isIncluded": false,
     "applicableRegions": ["IN"],
     "effectiveDate": "2024-01-01T00:00:00Z",
     "priority": 1,
     "description": "Standard GST for digital services in India"
   }
   ```

3. **Validation Rules**
   - GST rate in India: ≤ 28%
   - Effective date: Cannot be more than 1 year in future
   - Regions: Must be valid 2-10 character codes

#### Setting Up VAT for UK

```json
POST /api/admin/tax/global-configurations
{
  "taxType": "VAT",
  "rate": 20.00,
  "isIncluded": true,
  "applicableRegions": ["GB"],
  "effectiveDate": "2024-01-01T00:00:00Z",
  "priority": 1,
  "description": "Standard VAT for UK digital services"
}
```

#### Setting Up Compound Taxes (India)

For CGST + SGST configuration:

1. **Create CGST Configuration**
   ```json
   {
     "taxType": "CGST",
     "rate": 9.00,
     "isIncluded": false,
     "applicableRegions": ["IN"],
     "priority": 1
   }
   ```

2. **Create SGST Configuration**
   ```json
   {
     "taxType": "SGST",
     "rate": 9.00,
     "isIncluded": false,
     "applicableRegions": ["IN"],
     "priority": 2
   }
   ```

3. **Create Cess Configuration (if applicable)**
   ```json
   {
     "taxType": "Cess",
     "rate": 1.00,
     "isIncluded": false,
     "applicableRegions": ["IN"],
     "priority": 3
   }
   ```

**Important**: CGST and SGST rates must be equal. System will validate this automatically.

### Plan-Specific Tax Configuration

Override global settings for specific plans when needed.

#### Example: Premium Plan with Higher GST

```json
POST /api/admin/tax/plans/{planId}/configurations
{
  "taxType": "GST",
  "rate": 28.00,
  "isIncluded": false,
  "applicableRegions": ["IN"],
  "effectiveDate": "2024-01-01T00:00:00Z",
  "notes": "Luxury service tax rate for premium plans"
}
```

## Tax Category Management

### Creating Tax Categories

Tax categories group related tax configurations for easier management.

```json
POST /api/admin/tax/categories
{
  "name": "Digital Services",
  "description": "Tax category for all digital service offerings",
  "code": "DIGITAL"
}
```

### Assigning Categories to Plans

1. Create or update plan with tax category
2. Tax category configurations will apply automatically
3. Plan-specific configurations override category settings

## Tax Exemption Management

### Creating Customer Tax Exemptions

#### Educational Institution Exemption

```json
POST /api/admin/tax/exemptions
{
  "userId": "customer-user-id",
  "exemptionType": "Educational Institution",
  "exemptionNumber": "EDU123456789",
  "issuingAuthority": "Ministry of Education, India",
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": "2024-12-31T23:59:59Z",
  "exemptTaxTypes": ["GST", "CGST", "SGST"],
  "applicableRegions": ["IN"],
  "documentPath": "/documents/exemptions/edu123456.pdf"
}
```

#### Non-Profit Organization Exemption

```json
POST /api/admin/tax/exemptions
{
  "userId": "ngo-user-id",
  "exemptionType": "Non-Profit Organization",
  "exemptionNumber": "NPO987654321",
  "issuingAuthority": "Charity Commission",
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": "2025-12-31T23:59:59Z",
  "exemptTaxTypes": ["VAT"],
  "applicableRegions": ["GB"],
  "documentPath": "/documents/exemptions/npo987654.pdf"
}
```

### Verifying Tax Exemptions

1. **Review Exemption Details**
   - Check exemption number validity
   - Verify issuing authority
   - Confirm document authenticity

2. **Verify Exemption**
   ```json
   POST /api/admin/tax/exemptions/{exemptionId}/verify
   {
     "verificationNotes": "Verified with issuing authority. Valid exemption certificate provided."
   }
   ```

3. **Exemption Status**
   - Unverified: Cannot be used for tax calculations
   - Verified: Active and applicable
   - Expired: Automatically deactivated

## Regional Tax Setup

### India Tax Configuration

#### Standard Setup
```json
// GST for most services
{
  "taxType": "GST",
  "rate": 18.00,
  "applicableRegions": ["IN"]
}

// TDS for digital services
{
  "taxType": "TDS",
  "rate": 2.00,
  "applicableRegions": ["IN"]
}
```

#### State-Specific Setup (for intra-state transactions)
```json
// CGST
{
  "taxType": "CGST",
  "rate": 9.00,
  "applicableRegions": ["IN"]
}

// SGST (varies by state)
{
  "taxType": "SGST",
  "rate": 9.00,
  "applicableRegions": ["IN"]
}
```

### United States Tax Configuration

```json
// Sales tax varies by state
{
  "taxType": "SalesTax",
  "rate": 8.25,
  "applicableRegions": ["US-CA"], // California
  "description": "California state sales tax"
}
```

### European Union VAT

```json
// Standard VAT rates
{
  "taxType": "VAT",
  "rate": 20.00,
  "applicableRegions": ["GB"]
},
{
  "taxType": "VAT",
  "rate": 19.00,
  "applicableRegions": ["DE"]
},
{
  "taxType": "VAT",
  "rate": 21.00,
  "applicableRegions": ["NL"]
}
```

## Compliance Management

### Tax Reporting

#### Generate Tax Report
```json
POST /api/admin/tax/reports
{
  "region": "IN",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-03-31T23:59:59Z",
  "reportType": "Summary"
}
```

#### Export Tax Report
```http
GET /api/admin/tax/reports/{reportId}/export?format=pdf
```

### Filing Requirements

#### Check Filing Requirements
```http
GET /api/admin/tax/filing-requirements?region=IN&date=2024-03-31
```

Response includes:
- Required forms (GSTR-1, Form 26Q, etc.)
- Filing deadlines
- Mandatory vs optional filings

### Compliance Validation

The system automatically validates:
- Tax rate limits by region
- Required tax combinations (CGST+SGST vs IGST)
- Exemption authenticity
- Filing deadline compliance

## Best Practices

### Tax Configuration
1. **Test in Staging**: Always test tax configurations in staging environment
2. **Effective Dates**: Set future effective dates for planned changes
3. **Documentation**: Maintain clear descriptions for all configurations
4. **Regular Review**: Review and update tax rates quarterly

### Exemption Management
1. **Document Verification**: Always verify exemption documents
2. **Expiration Monitoring**: Set up alerts for expiring exemptions
3. **Regular Audits**: Conduct quarterly exemption audits
4. **Backup Documentation**: Maintain copies of all exemption documents

### Compliance
1. **Regular Reports**: Generate monthly tax reports
2. **Filing Calendar**: Maintain a tax filing calendar
3. **Rate Updates**: Monitor tax rate changes in applicable regions
4. **Audit Trail**: Maintain complete audit trails for all changes

## Troubleshooting

### Common Issues

#### Tax Not Calculating
1. Check if tax configuration is active
2. Verify region code matches customer location
3. Ensure effective date is not in future
4. Check for customer exemptions

#### Incorrect Tax Amount
1. Verify tax rate configuration
2. Check for plan-specific overrides
3. Review compound tax calculations
4. Validate customer exemption status

#### Exemption Not Applied
1. Confirm exemption is verified
2. Check exemption validity dates
3. Verify tax type matches
4. Ensure region matches customer location

### Error Codes

- `TAX_001`: Invalid tax rate (must be 0-100%)
- `TAX_002`: Invalid region code
- `TAX_003`: Overlapping tax configurations
- `TAX_004`: Unverified exemption
- `TAX_005`: Expired exemption
- `TAX_006`: Invalid tax type combination

## Support

For technical support or questions about tax configuration:
- Email: <EMAIL>
- Documentation: `/docs/tax-inclusion-implementation.md`
- API Reference: `/api/docs#tax-management`

## Audit and Compliance

### Audit Trail
All tax-related actions are logged:
- Configuration changes
- Exemption verifications
- Rate modifications
- Admin actions

### Compliance Reports
Monthly compliance reports include:
- Tax collection summaries
- Exemption usage statistics
- Filing requirement status
- Validation errors and warnings

### Data Retention
- Tax configurations: Permanent
- Exemption documents: 7 years
- Audit logs: 5 years
- Compliance reports: 7 years
