using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Admin;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize(Roles = "Admin")]
public class AdminController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AdminController> _logger;

    public AdminController(IMediator mediator, ILogger<AdminController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get network performance dashboard
    /// </summary>
    [HttpGet("dashboard/network-performance")]
    public async Task<ActionResult<NetworkPerformanceDashboardDto>> GetNetworkPerformanceDashboard(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworkPerformanceDashboardQuery(fromDate, toDate);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving network performance dashboard");
            return StatusCode(500, "An error occurred while retrieving network performance dashboard");
        }
    }

    /// <summary>
    /// Get carrier performance analytics
    /// </summary>
    [HttpGet("analytics/carrier-performance")]
    public async Task<ActionResult<CarrierPerformanceAnalyticsDto>> GetCarrierPerformanceAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] Guid? carrierId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetCarrierPerformanceAnalyticsQuery(fromDate, toDate, carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving carrier performance analytics");
            return StatusCode(500, "An error occurred while retrieving carrier performance analytics");
        }
    }

    /// <summary>
    /// Get broker performance analytics
    /// </summary>
    [HttpGet("analytics/broker-performance")]
    public async Task<ActionResult<BrokerPerformanceAnalyticsDto>> GetBrokerPerformanceAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] Guid? brokerId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetBrokerPerformanceAnalyticsQuery(fromDate, toDate, brokerId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving broker performance analytics");
            return StatusCode(500, "An error occurred while retrieving broker performance analytics");
        }
    }

    /// <summary>
    /// Get fleet utilization analytics
    /// </summary>
    [HttpGet("analytics/fleet-utilization")]
    public async Task<ActionResult<FleetUtilizationAnalyticsDto>> GetFleetUtilizationAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] Guid? carrierId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetFleetUtilizationAnalyticsQuery(fromDate, toDate, carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving fleet utilization analytics");
            return StatusCode(500, "An error occurred while retrieving fleet utilization analytics");
        }
    }

    /// <summary>
    /// Get driver compliance analytics
    /// </summary>
    [HttpGet("analytics/driver-compliance")]
    public async Task<ActionResult<DriverComplianceAnalyticsDto>> GetDriverComplianceAnalytics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] Guid? carrierId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDriverComplianceAnalyticsQuery(fromDate, toDate, carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving driver compliance analytics");
            return StatusCode(500, "An error occurred while retrieving driver compliance analytics");
        }
    }

    /// <summary>
    /// Get onboarding oversight dashboard
    /// </summary>
    [HttpGet("dashboard/onboarding-oversight")]
    public async Task<ActionResult<OnboardingOversightDashboardDto>> GetOnboardingOversightDashboard(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetOnboardingOversightDashboardQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving onboarding oversight dashboard");
            return StatusCode(500, "An error occurred while retrieving onboarding oversight dashboard");
        }
    }

    /// <summary>
    /// Get document verification dashboard
    /// </summary>
    [HttpGet("dashboard/document-verification")]
    public async Task<ActionResult<DocumentVerificationDashboardDto>> GetDocumentVerificationDashboard(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetDocumentVerificationDashboardQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document verification dashboard");
            return StatusCode(500, "An error occurred while retrieving document verification dashboard");
        }
    }

    /// <summary>
    /// Get system health metrics
    /// </summary>
    [HttpGet("metrics/system-health")]
    public async Task<ActionResult<SystemHealthMetricsDto>> GetSystemHealthMetrics(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetSystemHealthMetricsQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving system health metrics");
            return StatusCode(500, "An error occurred while retrieving system health metrics");
        }
    }

    /// <summary>
    /// Get operational metrics
    /// </summary>
    [HttpGet("metrics/operational")]
    public async Task<ActionResult<OperationalMetricsDto>> GetOperationalMetrics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetOperationalMetricsQuery(fromDate, toDate);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving operational metrics");
            return StatusCode(500, "An error occurred while retrieving operational metrics");
        }
    }

    /// <summary>
    /// Get alerts and notifications
    /// </summary>
    [HttpGet("alerts")]
    public async Task<ActionResult<AdminAlertsDto>> GetAlerts(
        [FromQuery] bool includeResolved = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetAdminAlertsQuery(includeResolved);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving admin alerts");
            return StatusCode(500, "An error occurred while retrieving admin alerts");
        }
    }

    /// <summary>
    /// Get audit trail
    /// </summary>
    [HttpGet("audit-trail")]
    public async Task<ActionResult<PagedResult<AuditTrailDto>>> GetAuditTrail(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? entityType = null,
        [FromQuery] string? action = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetAuditTrailQuery(pageNumber, pageSize, fromDate, toDate, entityType, action);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail");
            return StatusCode(500, "An error occurred while retrieving audit trail");
        }
    }
}
