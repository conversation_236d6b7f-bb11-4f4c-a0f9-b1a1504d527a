version: '3.8'

services:
  financial-payment-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tli-financial-payment-api
    ports:
      - "5007:8080"
      - "5008:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080;https://+:8081
      - ConnectionStrings__DefaultConnection=Host=timescaledb;Port=5432;Database=tli_financial_payment;User Id=timescale;Password=timescale
      - JwtSettings__Secret=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - JwtSettings__Issuer=TLI.FinancialPayment
      - JwtSettings__Audience=TLI.FinancialPayment.Users
      - RazorPay__KeyId=rzp_test_your_key_id
      - RazorPay__KeySecret=your_test_key_secret
      - RazorPay__WebhookSecret=your_test_webhook_secret
    depends_on:
      - timescaledb
      - rabbitmq
    networks:
      - tli-network
    volumes:
      - ./logs:/app/logs

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tli-financial-payment-db
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=tli_financial_payment
      - POSTGRES_USER=timescale
      - POSTGRES_PASSWORD=timescale
    volumes:
      - financial_payment_db_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/01-setup.sql
    networks:
      - tli-network

  rabbitmq:
    image: rabbitmq:3-management
    container_name: tli-financial-payment-rabbitmq
    ports:
      - "5674:5672"
      - "15674:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - financial_payment_rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tli-network

  redis:
    image: redis:7-alpine
    container_name: tli-financial-payment-redis
    ports:
      - "6381:6379"
    volumes:
      - financial_payment_redis_data:/data
    networks:
      - tli-network

volumes:
  financial_payment_db_data:
  financial_payment_rabbitmq_data:
  financial_payment_redis_data:

networks:
  tli-network:
    external: true
