using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Infrastructure.Configurations;
using SubscriptionManagement.Infrastructure.Persistence.Configurations;

namespace SubscriptionManagement.Infrastructure.Persistence
{
    public class SubscriptionDbContext : DbContext
    {
        public DbSet<Plan> Plans { get; set; }
        public DbSet<PlanFeature> PlanFeatures { get; set; }
        public DbSet<Subscription> Subscriptions { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<SubscriptionChange> SubscriptionChanges { get; set; }
        public DbSet<FeatureFlag> FeatureFlags { get; set; }
        public DbSet<FeatureFlagRule> FeatureFlagRules { get; set; }
        public DbSet<FeatureFlagUsage> FeatureFlagUsages { get; set; }
        public DbSet<UsageRecord> UsageRecords { get; set; }
        public DbSet<SubscriptionPaymentProof> SubscriptionPaymentProofs { get; set; }
        public DbSet<NotificationTemplate> NotificationTemplates { get; set; }
        public DbSet<NotificationHistory> NotificationHistories { get; set; }

        // Tax-related entities
        public DbSet<TaxCategory> TaxCategories { get; set; }
        public DbSet<TaxExemption> TaxExemptions { get; set; }
        public DbSet<GlobalTaxConfiguration> GlobalTaxConfigurations { get; set; }
        public DbSet<PlanTaxConfiguration> PlanTaxConfigurations { get; set; }

        public SubscriptionDbContext(DbContextOptions<SubscriptionDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply configurations
            modelBuilder.ApplyConfiguration(new PlanConfiguration());
            modelBuilder.ApplyConfiguration(new PlanFeatureConfiguration());
            modelBuilder.ApplyConfiguration(new SubscriptionConfiguration());
            modelBuilder.ApplyConfiguration(new PaymentConfiguration());
            modelBuilder.ApplyConfiguration(new SubscriptionChangeConfiguration());
            modelBuilder.ApplyConfiguration(new FeatureFlagConfiguration());
            modelBuilder.ApplyConfiguration(new FeatureFlagRuleConfiguration());
            modelBuilder.ApplyConfiguration(new FeatureFlagUsageConfiguration());
            modelBuilder.ApplyConfiguration(new UsageRecordConfiguration());
            modelBuilder.ApplyConfiguration(new SubscriptionPaymentProofConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationTemplateConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationHistoryConfiguration());

            // Tax-related configurations
            modelBuilder.ApplyConfiguration(new TaxCategoryConfiguration());
            modelBuilder.ApplyConfiguration(new TaxExemptionConfiguration());
            modelBuilder.ApplyConfiguration(new GlobalTaxConfigurationConfiguration());
            modelBuilder.ApplyConfiguration(new PlanTaxConfigurationConfiguration());

            // Set schema
            modelBuilder.HasDefaultSchema("subscription");
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Update timestamps
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Shared.Domain.Common.BaseEntity entity)
                {
                    if (entry.State == EntityState.Added)
                    {
                        // CreatedAt is set in the constructor
                    }
                    else if (entry.State == EntityState.Modified)
                    {
                        entity.GetType().GetProperty("UpdatedAt")?.SetValue(entity, DateTime.UtcNow);
                    }
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}
