using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

public class UserPreference : BaseEntity
{
    public Guid UserId { get; private set; }
    public UserRole UserRole { get; private set; }
    public ContactInfo ContactInfo { get; private set; }
    public NotificationSettings NotificationSettings { get; private set; }
    public Language PreferredLanguage { get; private set; }
    public string? TimeZone { get; private set; }
    public bool IsOnline { get; private set; }
    public DateTime? LastSeenAt { get; private set; }
    public Dictionary<string, string> CustomSettings { get; private set; }

    private UserPreference()
    {
        ContactInfo = ContactInfo.Create("", "");
        NotificationSettings = NotificationSettings.Default();
        CustomSettings = new Dictionary<string, string>();
    }

    public UserPreference(
        Guid userId,
        UserRole userRole,
        ContactInfo contactInfo,
        NotificationSettings? notificationSettings = null,
        Language preferredLanguage = Language.English,
        string? timeZone = null,
        Dictionary<string, string>? customSettings = null)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        UserId = userId;
        UserRole = userRole;
        ContactInfo = contactInfo ?? throw new ArgumentNullException(nameof(contactInfo));
        NotificationSettings = notificationSettings ?? GetDefaultNotificationSettings(userRole);
        PreferredLanguage = preferredLanguage;
        TimeZone = timeZone ?? "UTC";
        IsOnline = false;
        CustomSettings = customSettings ?? new Dictionary<string, string>();
    }

    public static UserPreference Create(
        Guid userId,
        UserRole userRole,
        ContactInfo contactInfo,
        NotificationSettings? notificationSettings = null,
        Language preferredLanguage = Language.English,
        string? timeZone = null,
        Dictionary<string, string>? customSettings = null)
    {
        return new UserPreference(userId, userRole, contactInfo, notificationSettings, 
            preferredLanguage, timeZone, customSettings);
    }

    public void UpdateContactInfo(ContactInfo contactInfo)
    {
        ContactInfo = contactInfo ?? throw new ArgumentNullException(nameof(contactInfo));
    }

    public void UpdateNotificationSettings(NotificationSettings notificationSettings)
    {
        NotificationSettings = notificationSettings ?? throw new ArgumentNullException(nameof(notificationSettings));
    }

    public void UpdateLanguage(Language language)
    {
        PreferredLanguage = language;
        
        // Update notification settings to match language preference
        NotificationSettings = NotificationSettings.WithLanguage(language);
    }

    public void UpdateTimeZone(string timeZone)
    {
        if (string.IsNullOrWhiteSpace(timeZone))
            throw new ArgumentException("Time zone cannot be empty", nameof(timeZone));

        TimeZone = timeZone;
    }

    public void SetOnlineStatus(bool isOnline)
    {
        IsOnline = isOnline;
        if (!isOnline)
        {
            LastSeenAt = DateTime.UtcNow;
        }
    }

    public void UpdateCustomSetting(string key, string value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Setting key cannot be empty", nameof(key));

        CustomSettings[key] = value ?? string.Empty;
    }

    public void RemoveCustomSetting(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Setting key cannot be empty", nameof(key));

        CustomSettings.Remove(key);
    }

    public string? GetCustomSetting(string key)
    {
        return CustomSettings.TryGetValue(key, out var value) ? value : null;
    }

    public bool ShouldReceiveNotification(MessageType messageType, NotificationChannel channel, DateTime currentTime)
    {
        // Check if the message type is enabled
        if (!NotificationSettings.IsMessageTypeEnabled(messageType))
            return false;

        // Check if the channel is enabled
        if (!NotificationSettings.IsChannelEnabled(channel))
            return false;

        // Check quiet hours (except for emergency messages)
        if (messageType != MessageType.Emergency && NotificationSettings.IsInQuietHours(currentTime))
            return false;

        return true;
    }

    public NotificationChannel GetPreferredChannel(MessageType messageType, Priority priority)
    {
        // Emergency messages always use SMS if available
        if (messageType == MessageType.Emergency || priority == Priority.Emergency)
        {
            if (NotificationSettings.SmsEnabled) return NotificationChannel.Sms;
            if (NotificationSettings.PushEnabled) return NotificationChannel.Push;
        }

        // Driver-specific preferences
        if (UserRole == UserRole.Driver)
        {
            if (NotificationSettings.VoiceEnabled && 
                (messageType == MessageType.DriverInstruction || messageType == MessageType.TripUpdate))
                return NotificationChannel.Voice;
            
            if (NotificationSettings.PushEnabled) return NotificationChannel.Push;
            if (NotificationSettings.SmsEnabled) return NotificationChannel.Sms;
        }

        // High priority messages prefer immediate channels
        if (priority == Priority.High || priority == Priority.Critical)
        {
            if (NotificationSettings.PushEnabled) return NotificationChannel.Push;
            if (NotificationSettings.SmsEnabled) return NotificationChannel.Sms;
        }

        // Default preference order
        if (NotificationSettings.InAppEnabled) return NotificationChannel.InApp;
        if (NotificationSettings.EmailEnabled) return NotificationChannel.Email;
        if (NotificationSettings.PushEnabled) return NotificationChannel.Push;
        if (NotificationSettings.SmsEnabled) return NotificationChannel.Sms;

        return NotificationChannel.InApp; // Fallback
    }

    private static NotificationSettings GetDefaultNotificationSettings(UserRole userRole)
    {
        return userRole switch
        {
            UserRole.Driver => NotificationSettings.DriverDefault(),
            _ => NotificationSettings.Default()
        };
    }

    public bool IsRecentlyOnline(TimeSpan threshold)
    {
        if (IsOnline) return true;
        if (LastSeenAt == null) return false;
        
        return DateTime.UtcNow - LastSeenAt.Value <= threshold;
    }

    public DateTime GetLocalTime(DateTime utcTime)
    {
        if (string.IsNullOrEmpty(TimeZone) || TimeZone == "UTC")
            return utcTime;

        try
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(TimeZone);
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZoneInfo);
        }
        catch
        {
            return utcTime; // Fallback to UTC if timezone conversion fails
        }
    }
}
