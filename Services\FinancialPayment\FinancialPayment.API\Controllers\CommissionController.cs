using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Commands.CalculateCommission;
using FinancialPayment.Application.Queries.GetCommission;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CommissionController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CommissionController> _logger;

    public CommissionController(IMediator mediator, ILogger<CommissionController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Calculate commission for an order
    /// </summary>
    [HttpPost("calculate")]
    [Authorize(Roles = "Admin,Broker")]
    public async Task<ActionResult<Guid>> CalculateCommission([FromBody] CalculateCommissionRequest request)
    {
        try
        {
            var command = new CalculateCommissionCommand
            {
                OrderId = request.OrderId,
                BrokerId = request.BrokerId,
                TransportCompanyId = request.TransportCompanyId,
                CarrierId = request.CarrierId,
                OrderAmount = request.OrderAmount,
                CommissionStructure = new FinancialPayment.Application.Commands.CalculateCommission.CommissionStructureDto
                {
                    Type = request.CommissionStructure.Type,
                    Rate = request.CommissionStructure.Rate,
                    MinimumAmount = request.CommissionStructure.MinimumAmount,
                    MaximumAmount = request.CommissionStructure.MaximumAmount,
                    Description = request.CommissionStructure.Description
                },
                Notes = request.Notes
            };

            var commissionId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetCommission), new { id = commissionId }, commissionId);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while calculating commission");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission");
            return StatusCode(500, "An error occurred while calculating commission");
        }
    }

    /// <summary>
    /// Approve a commission
    /// </summary>
    [HttpPost("{id}/approve")]
    [Authorize(Roles = "Admin")]
    public ActionResult<bool> ApproveCommission(Guid id, [FromBody] ApproveCommissionRequest request)
    {
        try
        {
            // Implementation would go here
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving commission {CommissionId}", id);
            return StatusCode(500, "An error occurred while approving commission");
        }
    }

    /// <summary>
    /// Pay a commission
    /// </summary>
    [HttpPost("{id}/pay")]
    [Authorize(Roles = "Admin")]
    public ActionResult<bool> PayCommission(Guid id, [FromBody] PayCommissionRequest request)
    {
        try
        {
            // Implementation would go here
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error paying commission {CommissionId}", id);
            return StatusCode(500, "An error occurred while paying commission");
        }
    }

    /// <summary>
    /// Get commission by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Broker")]
    public async Task<ActionResult<CommissionDto>> GetCommission(Guid id)
    {
        try
        {
            var query = new GetCommissionQuery(id);
            var result = await _mediator.Send(query);

            if (result == null)
                return NotFound($"Commission {id} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving commission {CommissionId}", id);
            return StatusCode(500, "An error occurred while retrieving commission");
        }
    }

    /// <summary>
    /// Get commissions by broker
    /// </summary>
    [HttpGet("broker/{brokerId}")]
    [Authorize(Roles = "Admin,Broker")]
    public ActionResult<List<CommissionDto>> GetCommissionsByBroker(Guid brokerId)
    {
        try
        {
            // Implementation would go here
            return Ok(new List<CommissionDto>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving commissions for broker {BrokerId}", brokerId);
            return StatusCode(500, "An error occurred while retrieving commissions");
        }
    }

    /// <summary>
    /// Dispute a commission
    /// </summary>
    [HttpPost("{id}/dispute")]
    [Authorize(Roles = "Admin,Broker")]
    public async Task<ActionResult<bool>> DisputeCommission(Guid id, [FromBody] DisputeCommissionRequest request)
    {
        try
        {
            // Implementation would go here
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disputing commission {CommissionId}", id);
            return StatusCode(500, "An error occurred while disputing commission");
        }
    }
}

public class CalculateCommissionRequest
{
    public Guid OrderId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid CarrierId { get; set; }
    public CreateMoneyDto OrderAmount { get; set; } = new();
    public CommissionStructureDto CommissionStructure { get; set; } = new();
    public string? Notes { get; set; }
}

public class CommissionStructureDto
{
    public string Type { get; set; } = string.Empty;
    public decimal Rate { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumAmount { get; set; }
    public string? Description { get; set; }
}

public class ApproveCommissionRequest
{
    public string ApprovedBy { get; set; } = string.Empty;
}

public class PayCommissionRequest
{
    public string PaymentMethodId { get; set; } = string.Empty;
}

public class DisputeCommissionRequest
{
    public string Reason { get; set; } = string.Empty;
}


