using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MonitoringObservability.Domain.Entities;
using System.Text.Json;

namespace MonitoringObservability.Infrastructure.Configurations;

/// <summary>
/// Entity configuration for Trace
/// </summary>
public class TraceConfiguration : IEntityTypeConfiguration<Trace>
{
    public void Configure(EntityTypeBuilder<Trace> builder)
    {
        builder.ToTable("Traces");
        
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.TraceId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.HasIndex(t => t.TraceId)
            .IsUnique();
            
        builder.Property(t => t.RootOperationName)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.Property(t => t.RootServiceName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(t => t.StartTime)
            .IsRequired();
            
        builder.Property(t => t.EndTime);
        
        builder.Property(t => t.Status)
            .IsRequired()
            .HasConversion<int>();
            
        builder.Property(t => t.ErrorMessage)
            .HasMaxLength(1000);
            
        builder.Property(t => t.IsActive)
            .IsRequired();
            
        // Configure Tags as JSON
        builder.Property(t => t.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Metadata as JSON
        builder.Property(t => t.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Configure relationships
        builder.HasMany(t => t.Spans)
            .WithOne(s => s.Trace)
            .HasForeignKey("TraceId")
            .HasPrincipalKey(t => t.TraceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(t => t.Events)
            .WithOne(e => e.Trace)
            .HasForeignKey("TraceId")
            .HasPrincipalKey(t => t.TraceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        // Indexes for performance
        builder.HasIndex(t => t.RootServiceName);
        builder.HasIndex(t => t.RootOperationName);
        builder.HasIndex(t => t.StartTime);
        builder.HasIndex(t => t.Status);
        builder.HasIndex(t => t.IsActive);
    }
}

/// <summary>
/// Entity configuration for Span
/// </summary>
public class SpanConfiguration : IEntityTypeConfiguration<Span>
{
    public void Configure(EntityTypeBuilder<Span> builder)
    {
        builder.ToTable("Spans");
        
        builder.HasKey(s => s.Id);
        
        builder.Property(s => s.TraceId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.Property(s => s.SpanId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.HasIndex(s => s.SpanId)
            .IsUnique();
            
        builder.Property(s => s.ParentSpanId)
            .HasMaxLength(64);
            
        builder.Property(s => s.OperationName)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.Property(s => s.ServiceName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(s => s.StartTime)
            .IsRequired();
            
        builder.Property(s => s.EndTime);
        
        builder.Property(s => s.IsActive)
            .IsRequired();
            
        builder.Property(s => s.HasError)
            .IsRequired();
            
        builder.Property(s => s.ErrorMessage)
            .HasMaxLength(1000);
            
        builder.Property(s => s.ErrorType)
            .HasMaxLength(100);
            
        // Configure Tags as JSON
        builder.Property(s => s.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
            .HasColumnType("nvarchar(max)");
            
        // Configure Metadata as JSON
        builder.Property(s => s.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Configure relationships
        builder.HasMany(s => s.Events)
            .WithOne(e => e.Span)
            .HasForeignKey("SpanId")
            .HasPrincipalKey(s => s.SpanId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(s => s.Logs)
            .WithOne(l => l.Span)
            .HasForeignKey("SpanId")
            .HasPrincipalKey(s => s.SpanId)
            .OnDelete(DeleteBehavior.Cascade);
            
        // Indexes for performance
        builder.HasIndex(s => s.TraceId);
        builder.HasIndex(s => s.ParentSpanId);
        builder.HasIndex(s => s.ServiceName);
        builder.HasIndex(s => s.OperationName);
        builder.HasIndex(s => s.StartTime);
        builder.HasIndex(s => s.HasError);
        builder.HasIndex(s => s.IsActive);
    }
}

/// <summary>
/// Entity configuration for TraceEvent
/// </summary>
public class TraceEventConfiguration : IEntityTypeConfiguration<TraceEvent>
{
    public void Configure(EntityTypeBuilder<TraceEvent> builder)
    {
        builder.ToTable("TraceEvents");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.TraceId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.Property(e => e.EventName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(e => e.Message)
            .IsRequired()
            .HasMaxLength(1000);
            
        builder.Property(e => e.Timestamp)
            .IsRequired();
            
        // Configure Metadata as JSON
        builder.Property(e => e.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(e => e.TraceId);
        builder.HasIndex(e => e.EventName);
        builder.HasIndex(e => e.Timestamp);
    }
}

/// <summary>
/// Entity configuration for SpanEvent
/// </summary>
public class SpanEventConfiguration : IEntityTypeConfiguration<SpanEvent>
{
    public void Configure(EntityTypeBuilder<SpanEvent> builder)
    {
        builder.ToTable("SpanEvents");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.SpanId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.Property(e => e.EventName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(e => e.Message)
            .IsRequired()
            .HasMaxLength(1000);
            
        builder.Property(e => e.Timestamp)
            .IsRequired();
            
        // Configure Metadata as JSON
        builder.Property(e => e.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(e => e.SpanId);
        builder.HasIndex(e => e.EventName);
        builder.HasIndex(e => e.Timestamp);
    }
}

/// <summary>
/// Entity configuration for SpanLog
/// </summary>
public class SpanLogConfiguration : IEntityTypeConfiguration<SpanLog>
{
    public void Configure(EntityTypeBuilder<SpanLog> builder)
    {
        builder.ToTable("SpanLogs");
        
        builder.HasKey(l => l.Id);
        
        builder.Property(l => l.SpanId)
            .IsRequired()
            .HasMaxLength(64);
            
        builder.Property(l => l.Level)
            .IsRequired()
            .HasMaxLength(20);
            
        builder.Property(l => l.Message)
            .IsRequired()
            .HasMaxLength(2000);
            
        builder.Property(l => l.Timestamp)
            .IsRequired();
            
        // Configure Fields as JSON
        builder.Property(l => l.Fields)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Indexes for performance
        builder.HasIndex(l => l.SpanId);
        builder.HasIndex(l => l.Level);
        builder.HasIndex(l => l.Timestamp);
    }
}

/// <summary>
/// Entity configuration for ServiceDependency
/// </summary>
public class ServiceDependencyConfiguration : IEntityTypeConfiguration<ServiceDependency>
{
    public void Configure(EntityTypeBuilder<ServiceDependency> builder)
    {
        builder.ToTable("ServiceDependencies");
        
        builder.HasKey(d => d.Id);
        
        builder.Property(d => d.FromService)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(d => d.ToService)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(d => d.OperationType)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(d => d.CallCount)
            .IsRequired();
            
        builder.Property(d => d.ErrorCount)
            .IsRequired();
            
        builder.Property(d => d.ErrorRate)
            .IsRequired()
            .HasPrecision(5, 4);
            
        builder.Property(d => d.FirstSeen)
            .IsRequired();
            
        builder.Property(d => d.LastSeen)
            .IsRequired();
            
        // Configure Metadata as JSON
        builder.Property(d => d.Metadata)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>())
            .HasColumnType("nvarchar(max)");
            
        // Unique constraint for service dependency combination
        builder.HasIndex(d => new { d.FromService, d.ToService, d.OperationType })
            .IsUnique();
            
        // Indexes for performance
        builder.HasIndex(d => d.FromService);
        builder.HasIndex(d => d.ToService);
        builder.HasIndex(d => d.ErrorRate);
        builder.HasIndex(d => d.LastSeen);
    }
}
