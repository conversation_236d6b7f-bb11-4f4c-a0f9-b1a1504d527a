# Fix remaining compilation issues
Write-Host "Fixing remaining compilation issues..." -ForegroundColor Green

# Fix test project package versions
Write-Host "Fixing test project package versions..." -ForegroundColor Yellow
$testProjects = Get-ChildItem -Path "Services" -Recurse -Filter "*.Tests.csproj"

foreach ($testProject in $testProjects) {
    Write-Host "  Processing $($testProject.Name)..." -ForegroundColor Cyan
    $content = Get-Content $testProject.FullName -Raw
    
    # Update EntityFrameworkCore version to 8.0.11
    $content = $content -replace 'Microsoft\.EntityFrameworkCore" Version="[^"]*"', 'Microsoft.EntityFrameworkCore" Version="8.0.11"'
    $content = $content -replace 'Microsoft\.EntityFrameworkCore\.InMemory" Version="[^"]*"', 'Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11"'
    
    Set-Content -Path $testProject.FullName -Value $content -Encoding UTF8
    Write-Host "    Updated $($testProject.Name)" -ForegroundColor Green
}

# Fix missing using statements for domain events and value objects
Write-Host "Adding missing using statements for domain events and value objects..." -ForegroundColor Yellow

$domainFiles = Get-ChildItem -Path "Services" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -match "\.Domain\\" -and $_.FullName -notmatch "\\bin\\" -and $_.FullName -notmatch "\\obj\\"
}

foreach ($file in $domainFiles) {
    $content = Get-Content $file.FullName -Raw
    $modified = $false
    
    # Add IDomainEvent using statement if file contains IDomainEvent but doesn't have the using
    if ($content -match "\bIDomainEvent\b" -and $content -notmatch "using Shared\.Domain\.Common;") {
        Write-Host "  Adding IDomainEvent using to $($file.Name)" -ForegroundColor Cyan
        if ($content -match "using [^;]+;") {
            $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.Common;"
        } else {
            $content = "using Shared.Domain.Common;`n`n" + $content
        }
        $modified = $true
    }
    
    # Add ValueObject using statement if file contains ValueObject but doesn't have the using
    if ($content -match "\bValueObject\b" -and $content -notmatch "using Shared\.Domain\.Common;") {
        Write-Host "  Adding ValueObject using to $($file.Name)" -ForegroundColor Cyan
        if ($content -match "using [^;]+;") {
            $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.Common;"
        } else {
            $content = "using Shared.Domain.Common;`n`n" + $content
        }
        $modified = $true
    }
    
    # Fix Entity references
    if ($content -match "\bEntity\b" -and $content -notmatch "using Shared\.Domain\.Common;") {
        Write-Host "  Adding Entity using to $($file.Name)" -ForegroundColor Cyan
        if ($content -match "using [^;]+;") {
            $content = $content -replace "(using [^;]+;)", "`$1`nusing Shared.Domain.Common;"
        } else {
            $content = "using Shared.Domain.Common;`n`n" + $content
        }
        $modified = $true
    }
    
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "    Updated $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "Remaining issues fixes completed!" -ForegroundColor Green
