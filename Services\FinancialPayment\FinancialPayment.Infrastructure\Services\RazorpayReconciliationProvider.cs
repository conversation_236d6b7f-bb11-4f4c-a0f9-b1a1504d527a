using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class RazorpayReconciliationProvider : IPaymentGatewayReconciliationProvider
{
    private readonly RazorPaySettings _settings;
    private readonly ILogger<RazorpayReconciliationProvider> _logger;
    private readonly HttpClient _httpClient;

    public string GatewayName => "Razorpay";

    public RazorpayReconciliationProvider(
        IOptions<RazorPaySettings> settings,
        ILogger<RazorpayReconciliationProvider> logger,
        HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
        
        // Configure HTTP client for Razorpay API
        _httpClient.BaseAddress = new Uri("https://api.razorpay.com/v1/");
        var credentials = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($"{_settings.KeyId}:{_settings.KeySecret}"));
        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);
    }

    public async Task<List<GatewayTransaction>> GetTransactionsAsync(DateTime fromDate, DateTime toDate)
    {
        _logger.LogInformation("Getting Razorpay transactions from {FromDate} to {ToDate}", fromDate, toDate);

        try
        {
            // For demo purposes, we'll simulate getting transactions from Razorpay
            // In a real implementation, you would call the Razorpay API
            await Task.Delay(1000); // Simulate API call

            var transactions = new List<GatewayTransaction>();

            // Generate some sample transactions for demonstration
            var random = new Random();
            var transactionCount = random.Next(10, 50);

            for (int i = 0; i < transactionCount; i++)
            {
                var transactionDate = fromDate.AddHours(random.Next(0, (int)(toDate - fromDate).TotalHours));
                var amount = new Money(random.Next(100, 10000), "INR");

                transactions.Add(new GatewayTransaction
                {
                    TransactionId = $"pay_{Guid.NewGuid():N}",
                    ReferenceId = $"order_{Guid.NewGuid():N}",
                    Amount = amount,
                    Status = GetRandomStatus(random),
                    TransactionDate = transactionDate,
                    PaymentMethod = GetRandomPaymentMethod(random),
                    Metadata = new Dictionary<string, object>
                    {
                        { "gateway", "razorpay" },
                        { "method", GetRandomPaymentMethod(random) },
                        { "captured", true }
                    }
                });
            }

            _logger.LogInformation("Retrieved {Count} transactions from Razorpay", transactions.Count);
            return transactions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions from Razorpay");
            throw;
        }
    }

    public async Task<GatewayTransaction?> GetTransactionAsync(string transactionId)
    {
        _logger.LogDebug("Getting Razorpay transaction {TransactionId}", transactionId);

        try
        {
            // For demo purposes, simulate getting a specific transaction
            await Task.Delay(200);

            // In a real implementation, you would call:
            // var response = await _httpClient.GetAsync($"payments/{transactionId}");
            
            return new GatewayTransaction
            {
                TransactionId = transactionId,
                ReferenceId = $"order_{Guid.NewGuid():N}",
                Amount = new Money(1000, "INR"),
                Status = "captured",
                TransactionDate = DateTime.UtcNow.AddHours(-1),
                PaymentMethod = "card",
                Metadata = new Dictionary<string, object>
                {
                    { "gateway", "razorpay" },
                    { "method", "card" },
                    { "captured", true }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction {TransactionId} from Razorpay", transactionId);
            return null;
        }
    }

    public async Task<bool> ValidateTransactionAsync(string transactionId, Money amount)
    {
        _logger.LogDebug("Validating Razorpay transaction {TransactionId} with amount {Amount}", transactionId, amount);

        try
        {
            var transaction = await GetTransactionAsync(transactionId);
            if (transaction == null)
            {
                return false;
            }

            // Check if amounts match within tolerance
            var tolerance = new Money(0.01m, amount.Currency);
            var amountDifference = Math.Abs((transaction.Amount - amount).Amount);
            
            return amountDifference <= tolerance.Amount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating transaction {TransactionId}", transactionId);
            return false;
        }
    }

    private static string GetRandomStatus(Random random)
    {
        var statuses = new[] { "captured", "authorized", "failed", "refunded" };
        return statuses[random.Next(statuses.Length)];
    }

    private static string GetRandomPaymentMethod(Random random)
    {
        var methods = new[] { "card", "netbanking", "wallet", "upi" };
        return methods[random.Next(methods.Length)];
    }
}

// Similar providers for other gateways
public class StripeReconciliationProvider : IPaymentGatewayReconciliationProvider
{
    private readonly StripeSettings _settings;
    private readonly ILogger<StripeReconciliationProvider> _logger;
    private readonly HttpClient _httpClient;

    public string GatewayName => "Stripe";

    public StripeReconciliationProvider(
        IOptions<StripeSettings> settings,
        ILogger<StripeReconciliationProvider> logger,
        HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<List<GatewayTransaction>> GetTransactionsAsync(DateTime fromDate, DateTime toDate)
    {
        _logger.LogInformation("Getting Stripe transactions from {FromDate} to {ToDate}", fromDate, toDate);

        try
        {
            // Simulate Stripe API call
            await Task.Delay(800);

            var transactions = new List<GatewayTransaction>();
            var random = new Random();
            var transactionCount = random.Next(5, 25);

            for (int i = 0; i < transactionCount; i++)
            {
                var transactionDate = fromDate.AddHours(random.Next(0, (int)(toDate - fromDate).TotalHours));
                var amount = new Money(random.Next(100, 5000), "INR");

                transactions.Add(new GatewayTransaction
                {
                    TransactionId = $"pi_{Guid.NewGuid():N}",
                    ReferenceId = $"cus_{Guid.NewGuid():N}",
                    Amount = amount,
                    Status = random.Next(2) == 0 ? "succeeded" : "requires_payment_method",
                    TransactionDate = transactionDate,
                    PaymentMethod = "card",
                    Metadata = new Dictionary<string, object>
                    {
                        { "gateway", "stripe" },
                        { "payment_method_type", "card" }
                    }
                });
            }

            _logger.LogInformation("Retrieved {Count} transactions from Stripe", transactions.Count);
            return transactions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions from Stripe");
            throw;
        }
    }

    public async Task<GatewayTransaction?> GetTransactionAsync(string transactionId)
    {
        try
        {
            await Task.Delay(200);
            
            return new GatewayTransaction
            {
                TransactionId = transactionId,
                ReferenceId = $"cus_{Guid.NewGuid():N}",
                Amount = new Money(1000, "INR"),
                Status = "succeeded",
                TransactionDate = DateTime.UtcNow.AddHours(-1),
                PaymentMethod = "card",
                Metadata = new Dictionary<string, object>
                {
                    { "gateway", "stripe" },
                    { "payment_method_type", "card" }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction {TransactionId} from Stripe", transactionId);
            return null;
        }
    }

    public async Task<bool> ValidateTransactionAsync(string transactionId, Money amount)
    {
        try
        {
            var transaction = await GetTransactionAsync(transactionId);
            if (transaction == null) return false;

            var tolerance = new Money(0.01m, amount.Currency);
            var amountDifference = Math.Abs((transaction.Amount - amount).Amount);
            
            return amountDifference <= tolerance.Amount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating transaction {TransactionId}", transactionId);
            return false;
        }
    }
}
