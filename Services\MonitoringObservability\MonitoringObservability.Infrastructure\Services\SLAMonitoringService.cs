using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Service for SLA monitoring, compliance tracking, and breach detection
/// </summary>
public class SLAMonitoringService : ISLAMonitoringService
{
    private readonly ISLARepository _slaRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly INotificationService _notificationService;
    private readonly ILogger<SLAMonitoringService> _logger;
    
    // Cache for active SLAs
    private readonly ConcurrentDictionary<Guid, ServiceLevelAgreement> _activeSLAs = new();
    private readonly ConcurrentDictionary<Guid, DateTime> _lastEvaluationTimes = new();

    public SLAMonitoringService(
        ISLARepository slaRepository,
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        INotificationService notificationService,
        ILogger<SLAMonitoringService> logger)
    {
        _slaRepository = slaRepository ?? throw new ArgumentNullException(nameof(slaRepository));
        _metricRepository = metricRepository ?? throw new ArgumentNullException(nameof(metricRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initializing SLA monitoring service");

            // Load active SLAs
            var activeSLAs = await _slaRepository.GetActiveSLAsAsync(cancellationToken);
            foreach (var sla in activeSLAs)
            {
                _activeSLAs.TryAdd(sla.Id, sla);
            }

            _logger.LogInformation("Loaded {SLACount} active SLAs", _activeSLAs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize SLA monitoring service");
            throw;
        }
    }

    public async Task<ServiceLevelAgreement> CreateSLAAsync(string name, string description, string serviceName,
        DateTime startDate, DateTime endDate, Guid ownerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating SLA {SLAName} for service {ServiceName}", name, serviceName);

            var sla = new ServiceLevelAgreement(name, description, serviceName, startDate, endDate, ownerId);
            
            await _slaRepository.AddAsync(sla, cancellationToken);
            
            // Add to active cache if currently valid
            if (sla.IsValid)
            {
                _activeSLAs.TryAdd(sla.Id, sla);
            }
            
            _logger.LogInformation("Created SLA {SLAId}", sla.Id);
            return sla;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create SLA {SLAName}", name);
            throw;
        }
    }

    public async Task<SLAObjective> AddObjectiveAsync(Guid slaId, string name, string description, 
        SLAObjectiveType type, string metricName, double targetValue, SLAComparisonOperator comparisonOperator,
        TimeSpan evaluationWindow, double weight = 1.0, CancellationToken cancellationToken = default)
    {
        try
        {
            var sla = await _slaRepository.GetByIdAsync(slaId, cancellationToken);
            if (sla == null)
                throw new InvalidOperationException($"SLA {slaId} not found");

            var objective = new SLAObjective(name, description, type, metricName, targetValue, 
                comparisonOperator, evaluationWindow, weight);
            
            sla.AddObjective(objective);
            
            await _slaRepository.UpdateAsync(sla, cancellationToken);
            
            // Update cache
            _activeSLAs.TryAdd(slaId, sla);
            
            _logger.LogInformation("Added objective {ObjectiveName} to SLA {SLAId}", name, slaId);
            return objective;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add objective to SLA {SLAId}", slaId);
            throw;
        }
    }

    public async Task EvaluateAllSLAsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Evaluating all active SLAs");

            var evaluationTasks = _activeSLAs.Values
                .Where(sla => sla.IsValid && ShouldEvaluate(sla.Id))
                .Select(sla => EvaluateSLAAsync(sla, cancellationToken));

            await Task.WhenAll(evaluationTasks);

            _logger.LogInformation("Completed SLA evaluation for {SLACount} SLAs", _activeSLAs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during SLA evaluation");
            throw;
        }
    }

    public async Task<SLAComplianceDto> GetSLAComplianceAsync(Guid slaId, TimeSpan? period = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var sla = await _slaRepository.GetByIdAsync(slaId, cancellationToken);
            if (sla == null)
                throw new InvalidOperationException($"SLA {slaId} not found");

            var evaluationPeriod = period ?? TimeSpan.FromDays(30);
            var fromDate = DateTime.UtcNow.Subtract(evaluationPeriod);

            var compliance = new SLAComplianceDto
            {
                SLAId = slaId,
                SLAName = sla.Name,
                ServiceName = sla.ServiceName,
                Period = evaluationPeriod,
                OverallCompliance = sla.CurrentCompliance ?? 0,
                Status = sla.Status,
                ObjectiveCompliances = new List<SLAObjectiveComplianceDto>(),
                RecentBreaches = new List<SLABreachDto>(),
                GeneratedAt = DateTime.UtcNow
            };

            // Calculate objective compliances
            foreach (var objective in sla.Objectives)
            {
                var objectiveCompliance = await CalculateObjectiveComplianceAsync(objective, fromDate, cancellationToken);
                compliance.ObjectiveCompliances.Add(objectiveCompliance);
            }

            // Get recent breaches
            var recentBreaches = sla.Breaches
                .Where(b => b.DetectedAt >= fromDate)
                .OrderByDescending(b => b.DetectedAt)
                .Take(10);

            foreach (var breach in recentBreaches)
            {
                compliance.RecentBreaches.Add(MapBreachToDto(breach));
            }

            return compliance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SLA compliance for {SLAId}", slaId);
            throw;
        }
    }

    public async Task<SLAReport> GenerateReportAsync(Guid slaId, SLAReportType reportType, 
        DateTime? periodStart = null, DateTime? periodEnd = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var sla = await _slaRepository.GetByIdAsync(slaId, cancellationToken);
            if (sla == null)
                throw new InvalidOperationException($"SLA {slaId} not found");

            var (start, end) = CalculateReportPeriod(reportType, periodStart, periodEnd);
            
            _logger.LogInformation("Generating {ReportType} report for SLA {SLAId} from {Start} to {End}",
                reportType, slaId, start, end);

            var reportData = await GenerateReportDataAsync(sla, start, end, cancellationToken);
            var overallCompliance = sla.CalculateOverallCompliance();

            var report = new SLAReport(slaId, reportType, start, end, overallCompliance, reportData);
            
            sla.AddReport(report);
            await _slaRepository.UpdateAsync(sla, cancellationToken);
            
            _logger.LogInformation("Generated report {ReportId} for SLA {SLAId}", report.Id, slaId);
            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate report for SLA {SLAId}", slaId);
            throw;
        }
    }

    public async Task<SLABreach> DetectBreachAsync(Guid slaId, Guid objectiveId, double actualValue, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var sla = await _slaRepository.GetByIdAsync(slaId, cancellationToken);
            if (sla == null)
                throw new InvalidOperationException($"SLA {slaId} not found");

            var objective = sla.GetObjective(objectiveId);
            if (objective == null)
                throw new InvalidOperationException($"Objective {objectiveId} not found");

            var severity = CalculateBreachSeverity(actualValue, objective.TargetValue);
            var description = $"SLA breach detected for {objective.Name}. " +
                            $"Actual: {actualValue:F2}, Target: {objective.TargetValue:F2}";

            var breach = new SLABreach(slaId, objectiveId, actualValue, objective.TargetValue, severity, description);
            
            sla.RecordBreach(breach);
            await _slaRepository.UpdateAsync(sla, cancellationToken);
            
            // Create alert for breach
            await CreateBreachAlertAsync(sla, objective, breach, cancellationToken);
            
            // Send notifications
            await NotifyBreachAsync(sla, objective, breach, cancellationToken);
            
            _logger.LogWarning("SLA breach detected: {Description}", description);
            return breach;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect breach for SLA {SLAId}", slaId);
            throw;
        }
    }

    public async Task ResolveBreach(Guid breachId, Guid resolvedBy, string? resolutionNotes = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var breach = await _slaRepository.GetBreachByIdAsync(breachId, cancellationToken);
            if (breach == null)
                throw new InvalidOperationException($"Breach {breachId} not found");

            breach.Resolve(resolvedBy, resolutionNotes);
            
            await _slaRepository.UpdateBreachAsync(breach, cancellationToken);
            
            _logger.LogInformation("Resolved SLA breach {BreachId}", breachId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve breach {BreachId}", breachId);
            throw;
        }
    }

    public async Task<SLADashboardDto> GetSLADashboardAsync(string? serviceName = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var slas = string.IsNullOrEmpty(serviceName) 
                ? await _slaRepository.GetActiveSLAsAsync(cancellationToken)
                : await _slaRepository.GetSLAsByServiceAsync(serviceName, cancellationToken);

            var dashboard = new SLADashboardDto
            {
                ServiceName = serviceName,
                TotalSLAs = slas.Count(),
                SLAsInCompliance = slas.Count(s => (s.CurrentCompliance ?? 0) >= 95),
                SLAsAtRisk = slas.Count(s => (s.CurrentCompliance ?? 0) < 95 && (s.CurrentCompliance ?? 0) >= 90),
                SLAsInBreach = slas.Count(s => (s.CurrentCompliance ?? 0) < 90),
                AverageCompliance = slas.Any() ? slas.Average(s => s.CurrentCompliance ?? 0) : 0,
                ActiveBreaches = slas.Sum(s => s.ActiveBreachCount),
                SLAStatuses = new List<SLAStatusDto>(),
                GeneratedAt = DateTime.UtcNow
            };

            foreach (var sla in slas.Take(20)) // Limit for performance
            {
                dashboard.SLAStatuses.Add(new SLAStatusDto
                {
                    SLAId = sla.Id,
                    SLAName = sla.Name,
                    ServiceName = sla.ServiceName,
                    Status = sla.Status,
                    Compliance = sla.CurrentCompliance ?? 0,
                    ActiveBreaches = sla.ActiveBreachCount,
                    LastEvaluated = sla.LastEvaluated
                });
            }

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get SLA dashboard");
            throw;
        }
    }

    private async Task EvaluateSLAAsync(ServiceLevelAgreement sla, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Evaluating SLA {SLAId}", sla.Id);

            var objectiveCompliances = new List<double>();

            foreach (var objective in sla.Objectives.Where(o => o.IsEnabled))
            {
                var compliance = await EvaluateObjectiveAsync(sla, objective, cancellationToken);
                objectiveCompliances.Add(compliance);
            }

            // Calculate overall compliance
            var overallCompliance = objectiveCompliances.Any() ? objectiveCompliances.Average() : 100.0;
            sla.UpdateCompliance(overallCompliance);

            await _slaRepository.UpdateAsync(sla, cancellationToken);
            
            // Update last evaluation time
            _lastEvaluationTimes.TryAdd(sla.Id, DateTime.UtcNow);

            _logger.LogDebug("Evaluated SLA {SLAId} with compliance {Compliance:F2}%", sla.Id, overallCompliance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating SLA {SLAId}", sla.Id);
        }
    }

    private async Task<double> EvaluateObjectiveAsync(ServiceLevelAgreement sla, SLAObjective objective, 
        CancellationToken cancellationToken)
    {
        try
        {
            var fromDate = DateTime.UtcNow.Subtract(objective.EvaluationWindow);
            var metrics = await _metricRepository.GetMetricsByServiceAsync(sla.ServiceName, fromDate, cancellationToken);
            
            var relevantMetrics = metrics.Where(m => m.Name == objective.MetricName).ToList();
            
            if (!relevantMetrics.Any())
            {
                _logger.LogWarning("No metrics found for objective {ObjectiveName} in SLA {SLAId}", 
                    objective.Name, sla.Id);
                return 0.0;
            }

            var currentValue = CalculateObjectiveValue(objective, relevantMetrics);
            objective.UpdateCurrentValue(currentValue);

            // Check for breach
            if (objective.CurrentCompliance < 100.0)
            {
                await DetectBreachAsync(sla.Id, objective.Id, currentValue, cancellationToken);
            }

            return objective.CurrentCompliance ?? 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating objective {ObjectiveId}", objective.Id);
            return 0.0;
        }
    }

    private double CalculateObjectiveValue(SLAObjective objective, List<Metric> metrics)
    {
        return objective.Type switch
        {
            SLAObjectiveType.Availability => CalculateAvailability(metrics),
            SLAObjectiveType.ResponseTime => CalculateAverageResponseTime(metrics),
            SLAObjectiveType.Throughput => CalculateThroughput(metrics),
            SLAObjectiveType.ErrorRate => CalculateErrorRate(metrics),
            SLAObjectiveType.Latency => CalculateAverageLatency(metrics),
            SLAObjectiveType.Uptime => CalculateUptime(metrics),
            _ => metrics.LastOrDefault()?.Value ?? 0.0
        };
    }

    private double CalculateAvailability(List<Metric> metrics)
    {
        if (!metrics.Any()) return 0.0;
        
        var uptimeMetrics = metrics.Where(m => m.Value > 0).Count();
        return (double)uptimeMetrics / metrics.Count * 100.0;
    }

    private double CalculateAverageResponseTime(List<Metric> metrics)
    {
        return metrics.Any() ? metrics.Average(m => m.Value) : 0.0;
    }

    private double CalculateThroughput(List<Metric> metrics)
    {
        return metrics.Sum(m => m.Value);
    }

    private double CalculateErrorRate(List<Metric> metrics)
    {
        if (!metrics.Any()) return 0.0;
        
        var errorMetrics = metrics.Where(m => m.Value > 0).Count();
        return (double)errorMetrics / metrics.Count * 100.0;
    }

    private double CalculateAverageLatency(List<Metric> metrics)
    {
        return metrics.Any() ? metrics.Average(m => m.Value) : 0.0;
    }

    private double CalculateUptime(List<Metric> metrics)
    {
        return CalculateAvailability(metrics);
    }

    private bool ShouldEvaluate(Guid slaId)
    {
        if (!_lastEvaluationTimes.TryGetValue(slaId, out var lastEvaluation))
            return true;

        return DateTime.UtcNow - lastEvaluation > TimeSpan.FromMinutes(5);
    }

    private BreachSeverity CalculateBreachSeverity(double actualValue, double targetValue)
    {
        var deviationPercentage = Math.Abs((actualValue - targetValue) / targetValue) * 100;
        
        return deviationPercentage switch
        {
            > 50 => BreachSeverity.Critical,
            > 25 => BreachSeverity.High,
            > 10 => BreachSeverity.Medium,
            _ => BreachSeverity.Low
        };
    }

    private async Task CreateBreachAlertAsync(ServiceLevelAgreement sla, SLAObjective objective, 
        SLABreach breach, CancellationToken cancellationToken)
    {
        try
        {
            var severity = breach.Severity switch
            {
                BreachSeverity.Critical => AlertSeverity.Critical,
                BreachSeverity.High => AlertSeverity.High,
                BreachSeverity.Medium => AlertSeverity.Medium,
                _ => AlertSeverity.Low
            };

            var title = $"SLA Breach: {sla.Name} - {objective.Name}";
            var description = breach.Description;

            var threshold = new AlertThreshold(objective.MetricName, breach.ActualValue, 
                breach.TargetValue, objective.ComparisonOperator.ToString(), objective.EvaluationWindow);

            var alert = new Alert(title, description, severity, "SLAMonitoring", 
                sla.ServiceName, objective.MetricName, breach.ActualValue, threshold);

            alert.AddTag("sla_id", sla.Id.ToString());
            alert.AddTag("objective_id", objective.Id.ToString());
            alert.AddTag("breach_id", breach.Id.ToString());
            alert.AddTag("breach_severity", breach.Severity.ToString());

            await _alertRepository.AddAsync(alert, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create breach alert for SLA {SLAId}", sla.Id);
        }
    }

    private async Task NotifyBreachAsync(ServiceLevelAgreement sla, SLAObjective objective, 
        SLABreach breach, CancellationToken cancellationToken)
    {
        try
        {
            var channel = breach.Severity switch
            {
                BreachSeverity.Critical => NotificationChannel.SMS,
                BreachSeverity.High => NotificationChannel.Email,
                _ => NotificationChannel.InApp
            };

            var message = $"SLA Breach Alert: {sla.Name}\n" +
                         $"Objective: {objective.Name}\n" +
                         $"Actual: {breach.ActualValue:F2}, Target: {breach.TargetValue:F2}\n" +
                         $"Severity: {breach.Severity}";

            // In a real implementation, you would get the notification recipients from SLA configuration
            var recipients = new[] { "<EMAIL>" };

            foreach (var recipient in recipients)
            {
                await _notificationService.SendNotificationAsync(channel, recipient, 
                    "SLA Breach Detected", message, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send breach notification for SLA {SLAId}", sla.Id);
        }
    }

    private (DateTime start, DateTime end) CalculateReportPeriod(SLAReportType reportType, 
        DateTime? periodStart, DateTime? periodEnd)
    {
        var now = DateTime.UtcNow;
        var end = periodEnd ?? now;
        
        var start = periodStart ?? reportType switch
        {
            SLAReportType.Daily => end.AddDays(-1),
            SLAReportType.Weekly => end.AddDays(-7),
            SLAReportType.Monthly => end.AddMonths(-1),
            SLAReportType.Quarterly => end.AddMonths(-3),
            SLAReportType.Annual => end.AddYears(-1),
            _ => end.AddDays(-30)
        };

        return (start, end);
    }

    private async Task<Dictionary<string, object>> GenerateReportDataAsync(ServiceLevelAgreement sla, 
        DateTime start, DateTime end, CancellationToken cancellationToken)
    {
        var reportData = new Dictionary<string, object>
        {
            ["sla_name"] = sla.Name,
            ["service_name"] = sla.ServiceName,
            ["period_start"] = start,
            ["period_end"] = end,
            ["overall_compliance"] = sla.CurrentCompliance ?? 0,
            ["objective_count"] = sla.ObjectiveCount,
            ["breach_count"] = sla.BreachCount,
            ["active_breach_count"] = sla.ActiveBreachCount
        };

        // Add objective details
        var objectives = new List<object>();
        foreach (var objective in sla.Objectives)
        {
            objectives.Add(new
            {
                name = objective.Name,
                type = objective.Type.ToString(),
                target_value = objective.TargetValue,
                current_value = objective.CurrentValue,
                compliance = objective.CurrentCompliance,
                weight = objective.Weight
            });
        }
        reportData["objectives"] = objectives;

        // Add breach details
        var breaches = sla.Breaches
            .Where(b => b.DetectedAt >= start && b.DetectedAt <= end)
            .Select(b => new
            {
                detected_at = b.DetectedAt,
                resolved_at = b.ResolvedAt,
                severity = b.Severity.ToString(),
                status = b.Status.ToString(),
                actual_value = b.ActualValue,
                target_value = b.TargetValue,
                deviation_percentage = b.DeviationPercentage
            })
            .ToList();
        reportData["breaches"] = breaches;

        return reportData;
    }

    private async Task<SLAObjectiveComplianceDto> CalculateObjectiveComplianceAsync(SLAObjective objective, 
        DateTime fromDate, CancellationToken cancellationToken)
    {
        // Simplified calculation - in real implementation, this would analyze historical data
        await Task.CompletedTask;
        
        return new SLAObjectiveComplianceDto
        {
            ObjectiveId = objective.Id,
            ObjectiveName = objective.Name,
            Type = objective.Type,
            TargetValue = objective.TargetValue,
            CurrentValue = objective.CurrentValue ?? 0,
            Compliance = objective.CurrentCompliance ?? 0,
            Weight = objective.Weight,
            IsInCompliance = objective.IsInCompliance,
            LastEvaluated = objective.LastEvaluated
        };
    }

    private SLABreachDto MapBreachToDto(SLABreach breach)
    {
        return new SLABreachDto
        {
            Id = breach.Id,
            SLAId = breach.SLAId,
            ObjectiveId = breach.ObjectiveId,
            ActualValue = breach.ActualValue,
            TargetValue = breach.TargetValue,
            Severity = breach.Severity,
            Status = breach.Status,
            Description = breach.Description,
            DetectedAt = breach.DetectedAt,
            ResolvedAt = breach.ResolvedAt,
            Duration = breach.Duration,
            DeviationPercentage = breach.DeviationPercentage,
            ResolutionNotes = breach.ResolutionNotes,
            ResolvedBy = breach.ResolvedBy
        };
    }
}
