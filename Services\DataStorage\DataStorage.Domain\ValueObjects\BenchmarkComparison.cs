using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Result of benchmark comparison
/// </summary>
public class BenchmarkComparison : ValueObject
{
    public Guid ComparisonId { get; init; }
    public List<Guid> BenchmarkIds { get; init; }
    public string ComparisonType { get; init; }
    public DateTime GeneratedAt { get; init; }
    public List<BenchmarkComparisonMetric> Metrics { get; init; }
    public BenchmarkComparisonSummary Summary { get; init; }
    public Dictionary<string, object> Statistics { get; init; }

    public BenchmarkComparison(
        Guid comparisonId,
        List<Guid> benchmarkIds,
        string comparisonType,
        DateTime generatedAt,
        List<BenchmarkComparisonMetric> metrics,
        BenchmarkComparisonSummary summary,
        Dictionary<string, object> statistics)
    {
        ComparisonId = comparisonId;
        BenchmarkIds = benchmarkIds;
        ComparisonType = comparisonType;
        GeneratedAt = generatedAt;
        Metrics = metrics;
        Summary = summary;
        Statistics = statistics;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ComparisonId;
        yield return ComparisonType;
        yield return GeneratedAt;
        yield return Summary;
        
        foreach (var id in BenchmarkIds.OrderBy(x => x))
        {
            yield return id;
        }
        
        foreach (var metric in Metrics.OrderBy(x => x.MetricName))
        {
            yield return metric;
        }
        
        foreach (var stat in Statistics.OrderBy(x => x.Key))
        {
            yield return stat.Key;
            yield return stat.Value?.ToString() ?? string.Empty;
        }
    }
}

public class BenchmarkComparisonMetric : ValueObject
{
    public string MetricName { get; init; }
    public Dictionary<Guid, double> Values { get; init; }
    public double MinValue { get; init; }
    public double MaxValue { get; init; }
    public double AverageValue { get; init; }
    public Guid BestPerformingBenchmark { get; init; }
    public Guid WorstPerformingBenchmark { get; init; }

    public BenchmarkComparisonMetric(
        string metricName,
        Dictionary<Guid, double> values,
        double minValue,
        double maxValue,
        double averageValue,
        Guid bestPerformingBenchmark,
        Guid worstPerformingBenchmark)
    {
        MetricName = metricName;
        Values = values;
        MinValue = minValue;
        MaxValue = maxValue;
        AverageValue = averageValue;
        BestPerformingBenchmark = bestPerformingBenchmark;
        WorstPerformingBenchmark = worstPerformingBenchmark;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MetricName;
        yield return MinValue;
        yield return MaxValue;
        yield return AverageValue;
        yield return BestPerformingBenchmark;
        yield return WorstPerformingBenchmark;
        
        foreach (var value in Values.OrderBy(x => x.Key))
        {
            yield return value.Key;
            yield return value.Value;
        }
    }
}

public class BenchmarkComparisonSummary : ValueObject
{
    public Guid OverallBestBenchmark { get; init; }
    public Guid OverallWorstBenchmark { get; init; }
    public double PerformanceVariance { get; init; }
    public string RecommendedBenchmark { get; init; }
    public List<string> KeyFindings { get; init; }

    public BenchmarkComparisonSummary(
        Guid overallBestBenchmark,
        Guid overallWorstBenchmark,
        double performanceVariance,
        string recommendedBenchmark,
        List<string> keyFindings)
    {
        OverallBestBenchmark = overallBestBenchmark;
        OverallWorstBenchmark = overallWorstBenchmark;
        PerformanceVariance = performanceVariance;
        RecommendedBenchmark = recommendedBenchmark;
        KeyFindings = keyFindings;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return OverallBestBenchmark;
        yield return OverallWorstBenchmark;
        yield return PerformanceVariance;
        yield return RecommendedBenchmark;
        
        foreach (var finding in KeyFindings.OrderBy(x => x))
        {
            yield return finding;
        }
    }
}
