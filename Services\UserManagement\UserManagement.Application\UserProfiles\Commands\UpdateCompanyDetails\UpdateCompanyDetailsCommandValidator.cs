using FluentValidation;

namespace UserManagement.Application.UserProfiles.Commands.UpdateCompanyDetails
{
    public class UpdateCompanyDetailsCommandValidator : AbstractValidator<UpdateCompanyDetailsCommand>
    {
        public UpdateCompanyDetailsCommandValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("Profile ID is required");

            RuleFor(x => x.CompanyName)
                .NotEmpty()
                .WithMessage("Company name is required")
                .MaximumLength(200)
                .WithMessage("Company name cannot exceed 200 characters");

            RuleFor(x => x.GstNumber)
                .MaximumLength(15)
                .WithMessage("GST number cannot exceed 15 characters")
                .Matches(@"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$")
                .When(x => !string.IsNullOrWhiteSpace(x.GstNumber))
                .WithMessage("GST number must be in valid format");

            RuleFor(x => x.PanNumber)
                .MaximumLength(10)
                .WithMessage("PAN number cannot exceed 10 characters")
                .Matches(@"^[A-Z]{5}[0-9]{4}[A-Z]{1}$")
                .When(x => !string.IsNullOrWhiteSpace(x.PanNumber))
                .WithMessage("PAN number must be in valid format");
        }
    }
}
