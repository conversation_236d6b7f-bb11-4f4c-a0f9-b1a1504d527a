using Shared.Domain.Common;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Events;
using FluentAssertions;
using Xunit;

namespace AuditCompliance.Tests.Domain
{
    /// <summary>
    /// Unit tests for ModuleRegistry domain entity
    /// </summary>
    public class ModuleRegistryTests
    {
        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateModuleRegistry()
        {
            // Arrange
            var moduleName = "TestModule";
            var displayName = "Test Module";
            var description = "Test module description";
            var version = "1.0.0";
            var supportedEntityTypes = new List<string> { "User", "Order" };
            var supportedActions = new List<string> { "Create", "Read", "Update" };
            var requiredRoles = new List<string> { "Admin", "User" };
            var configuration = new Dictionary<string, object> { { "key1", "value1" } };
            var registeredBy = Guid.NewGuid();
            var healthCheckEndpoint = "https://api.test.com/health";

            // Act
            var moduleRegistry = new ModuleRegistry(
                moduleName,
                displayName,
                description,
                version,
                supportedEntityTypes,
                supportedActions,
                requiredRoles,
                configuration,
                registeredBy,
                healthCheckEndpoint);

            // Assert
            moduleRegistry.ModuleName.Should().Be(moduleName);
            moduleRegistry.DisplayName.Should().Be(displayName);
            moduleRegistry.Description.Should().Be(description);
            moduleRegistry.Version.Should().Be(version);
            moduleRegistry.SupportedEntityTypes.Should().BeEquivalentTo(supportedEntityTypes);
            moduleRegistry.SupportedActions.Should().BeEquivalentTo(supportedActions);
            moduleRegistry.RequiredRoles.Should().BeEquivalentTo(requiredRoles);
            moduleRegistry.Configuration.Should().BeEquivalentTo(configuration);
            moduleRegistry.RegisteredBy.Should().Be(registeredBy);
            moduleRegistry.HealthCheckEndpoint.Should().Be(healthCheckEndpoint);
            moduleRegistry.IsActive.Should().BeTrue();
            moduleRegistry.IsHealthy.Should().BeTrue();
            moduleRegistry.RegisteredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void Constructor_WithInvalidModuleName_ShouldThrowArgumentException(string invalidModuleName)
        {
            // Arrange & Act & Assert
            var action = () => new ModuleRegistry(
                invalidModuleName,
                "Display Name",
                "Description",
                "1.0.0",
                new List<string>(),
                new List<string>(),
                new List<string>(),
                new Dictionary<string, object>(),
                Guid.NewGuid());

            action.Should().Throw<ArgumentException>()
                .WithMessage("Module name cannot be empty*");
        }

        [Fact]
        public void Constructor_ShouldRaiseModuleRegisteredEvent()
        {
            // Arrange & Act
            var moduleRegistry = new ModuleRegistry(
                "TestModule",
                "Test Module",
                "Description",
                "1.0.0",
                new List<string>(),
                new List<string>(),
                new List<string>(),
                new Dictionary<string, object>(),
                Guid.NewGuid());

            // Assert
            var domainEvents = moduleRegistry.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<ModuleRegisteredEvent>();
        }

        [Fact]
        public void UpdateModule_WithValidParameters_ShouldUpdateProperties()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            var newDisplayName = "Updated Display Name";
            var newDescription = "Updated description";
            var newVersion = "2.0.0";
            var newSupportedEntityTypes = new List<string> { "Product", "Category" };
            var newSupportedActions = new List<string> { "Delete", "Archive" };
            var newRequiredRoles = new List<string> { "SuperAdmin" };
            var newConfiguration = new Dictionary<string, object> { { "newKey", "newValue" } };

            // Act
            moduleRegistry.UpdateModule(
                newDisplayName,
                newDescription,
                newVersion,
                newSupportedEntityTypes,
                newSupportedActions,
                newRequiredRoles,
                newConfiguration);

            // Assert
            moduleRegistry.DisplayName.Should().Be(newDisplayName);
            moduleRegistry.Description.Should().Be(newDescription);
            moduleRegistry.Version.Should().Be(newVersion);
            moduleRegistry.SupportedEntityTypes.Should().BeEquivalentTo(newSupportedEntityTypes);
            moduleRegistry.SupportedActions.Should().BeEquivalentTo(newSupportedActions);
            moduleRegistry.RequiredRoles.Should().BeEquivalentTo(newRequiredRoles);
            moduleRegistry.Configuration.Should().BeEquivalentTo(newConfiguration);
            moduleRegistry.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void UpdateModule_ShouldRaiseModuleUpdatedEvent()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            moduleRegistry.ClearDomainEvents(); // Clear initial events

            // Act
            moduleRegistry.UpdateModule(
                "Updated Name",
                "Updated Description",
                "2.0.0",
                new List<string>(),
                new List<string>(),
                new List<string>(),
                new Dictionary<string, object>());

            // Assert
            var domainEvents = moduleRegistry.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<ModuleUpdatedEvent>();
        }

        [Fact]
        public void Activate_WhenInactive_ShouldActivateModule()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            moduleRegistry.Deactivate();
            moduleRegistry.ClearDomainEvents();

            // Act
            moduleRegistry.Activate();

            // Assert
            moduleRegistry.IsActive.Should().BeTrue();
            var domainEvents = moduleRegistry.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<ModuleActivatedEvent>();
        }

        [Fact]
        public void Activate_WhenAlreadyActive_ShouldNotRaiseEvent()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            moduleRegistry.ClearDomainEvents();

            // Act
            moduleRegistry.Activate();

            // Assert
            moduleRegistry.IsActive.Should().BeTrue();
            moduleRegistry.GetDomainEvents().Should().BeEmpty();
        }

        [Fact]
        public void Deactivate_WhenActive_ShouldDeactivateModule()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            moduleRegistry.ClearDomainEvents();

            // Act
            moduleRegistry.Deactivate();

            // Assert
            moduleRegistry.IsActive.Should().BeFalse();
            var domainEvents = moduleRegistry.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<ModuleDeactivatedEvent>();
        }

        [Fact]
        public void UpdateHealthStatus_WithDifferentStatus_ShouldUpdateAndRaiseEvent()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            moduleRegistry.ClearDomainEvents();
            var errorMessage = "Health check failed";

            // Act
            moduleRegistry.UpdateHealthStatus(false, errorMessage);

            // Assert
            moduleRegistry.IsHealthy.Should().BeFalse();
            moduleRegistry.LastHealthCheckError.Should().Be(errorMessage);
            moduleRegistry.LastHealthCheck.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            
            var domainEvents = moduleRegistry.GetDomainEvents();
            domainEvents.Should().HaveCount(1);
            domainEvents.First().Should().BeOfType<ModuleHealthStatusChangedEvent>();
        }

        [Fact]
        public void SupportsEntityType_WithSupportedType_ShouldReturnTrue()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();

            // Act & Assert
            moduleRegistry.SupportsEntityType("User").Should().BeTrue();
            moduleRegistry.SupportsEntityType("user").Should().BeTrue(); // Case insensitive
            moduleRegistry.SupportsEntityType("NonExistent").Should().BeFalse();
        }

        [Fact]
        public void SupportsAction_WithSupportedAction_ShouldReturnTrue()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();

            // Act & Assert
            moduleRegistry.SupportsAction("Create").Should().BeTrue();
            moduleRegistry.SupportsAction("create").Should().BeTrue(); // Case insensitive
            moduleRegistry.SupportsAction("NonExistent").Should().BeFalse();
        }

        [Fact]
        public void RequiresRole_WithRequiredRole_ShouldReturnTrue()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();

            // Act & Assert
            moduleRegistry.RequiresRole("Admin").Should().BeTrue();
            moduleRegistry.RequiresRole("admin").Should().BeTrue(); // Case insensitive
            moduleRegistry.RequiresRole("NonExistent").Should().BeFalse();
        }

        [Fact]
        public void HasRequiredRole_WithMatchingRole_ShouldReturnTrue()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            var userRoles = new List<string> { "Admin", "User" };

            // Act & Assert
            moduleRegistry.HasRequiredRole(userRoles).Should().BeTrue();
        }

        [Fact]
        public void HasRequiredRole_WithoutMatchingRole_ShouldReturnFalse()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            var userRoles = new List<string> { "Guest", "Viewer" };

            // Act & Assert
            moduleRegistry.HasRequiredRole(userRoles).Should().BeFalse();
        }

        [Fact]
        public void HasRequiredRole_WithNoRequiredRoles_ShouldReturnTrue()
        {
            // Arrange
            var moduleRegistry = new ModuleRegistry(
                "TestModule",
                "Test Module",
                "Description",
                "1.0.0",
                new List<string>(),
                new List<string>(),
                new List<string>(), // No required roles
                new Dictionary<string, object>(),
                Guid.NewGuid());

            var userRoles = new List<string> { "Guest" };

            // Act & Assert
            moduleRegistry.HasRequiredRole(userRoles).Should().BeTrue();
        }

        [Fact]
        public void GetConfigurationValue_WithExistingKey_ShouldReturnValue()
        {
            // Arrange
            var configuration = new Dictionary<string, object>
            {
                { "stringKey", "stringValue" },
                { "intKey", 42 },
                { "boolKey", true }
            };

            var moduleRegistry = new ModuleRegistry(
                "TestModule",
                "Test Module",
                "Description",
                "1.0.0",
                new List<string>(),
                new List<string>(),
                new List<string>(),
                configuration,
                Guid.NewGuid());

            // Act & Assert
            moduleRegistry.GetConfigurationValue<string>("stringKey").Should().Be("stringValue");
            moduleRegistry.GetConfigurationValue<int>("intKey").Should().Be(42);
            moduleRegistry.GetConfigurationValue<bool>("boolKey").Should().BeTrue();
        }

        [Fact]
        public void GetConfigurationValue_WithNonExistentKey_ShouldReturnDefault()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();

            // Act & Assert
            moduleRegistry.GetConfigurationValue<string>("nonExistentKey").Should().BeNull();
            moduleRegistry.GetConfigurationValue<int>("nonExistentKey").Should().Be(0);
        }

        [Fact]
        public void SetConfigurationValue_ShouldUpdateConfiguration()
        {
            // Arrange
            var moduleRegistry = CreateTestModuleRegistry();
            var key = "newKey";
            var value = "newValue";

            // Act
            moduleRegistry.SetConfigurationValue(key, value);

            // Assert
            moduleRegistry.Configuration.Should().ContainKey(key);
            moduleRegistry.Configuration[key].Should().Be(value);
            moduleRegistry.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        private static ModuleRegistry CreateTestModuleRegistry()
        {
            return new ModuleRegistry(
                "TestModule",
                "Test Module",
                "Test module description",
                "1.0.0",
                new List<string> { "User", "Order" },
                new List<string> { "Create", "Read", "Update" },
                new List<string> { "Admin", "User" },
                new Dictionary<string, object> { { "key1", "value1" } },
                Guid.NewGuid());
        }
    }
}

