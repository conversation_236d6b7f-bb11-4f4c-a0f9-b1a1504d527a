using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using TripManagement.Application.Commands.CreateTrip;
using TripManagement.Application.Commands.StartTrip;
using TripManagement.Application.Commands.CompleteTrip;
using TripManagement.Application.Commands.AssignTrip;
using TripManagement.Application.DTOs;
using TripManagement.Application.Interfaces;
using TripManagement.Application.Queries.GetTripDetails;
using TripManagement.Application.Queries.GetTripsByCarrier;
using TripManagement.Domain.Enums;
using TripManagement.Infrastructure.Persistence;
using TripManagement.Infrastructure.Repositories;
using Xunit;

namespace TripManagement.Tests.Integration;

public class TripManagementIntegrationTests : IDisposable
{
    private readonly TripManagementDbContext _context;
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Mock<IMessageBroker> _messageBrokerMock;

    public TripManagementIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<TripManagementDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TripManagementDbContext(options);
        _tripRepository = new TripRepository(_context);
        _driverRepository = new DriverRepository(_context);
        _vehicleRepository = new VehicleRepository(_context);
        _unitOfWork = new UnitOfWork(_context);
        _messageBrokerMock = new Mock<IMessageBroker>();
    }

    [Fact]
    public async Task CreateTrip_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var loggerMock = new Mock<ILogger<CreateTripCommandHandler>>();
        var createHandler = new CreateTripCommandHandler(
            _tripRepository,
            _unitOfWork,
            _messageBrokerMock.Object,
            loggerMock.Object);

        var command = CreateValidCreateTripCommand();

        // Act - Create Trip
        var tripId = await createHandler.Handle(command, CancellationToken.None);

        // Assert - Trip Created
        tripId.Should().NotBeEmpty();

        // Act - Retrieve Trip
        var trip = await _tripRepository.GetByIdAsync(tripId);

        // Assert - Trip Retrieved Successfully
        trip.Should().NotBeNull();
        trip!.OrderId.Should().Be(command.OrderId);
        trip.CarrierId.Should().Be(command.CarrierId);
        trip.Status.Should().Be(TripStatus.Created);
        trip.Stops.Should().HaveCount(2);
        trip.EstimatedDistanceKm.Should().Be(command.EstimatedDistanceKm);
    }

    [Fact]
    public async Task TripLifecycle_EndToEnd_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripInDatabase();
        var driverId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();

        // Act & Assert - Assign Driver and Vehicle
        trip.AssignDriverAndVehicle(driverId, vehicleId);
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        updatedTrip!.Status.Should().Be(TripStatus.Assigned);
        updatedTrip.DriverId.Should().Be(driverId);
        updatedTrip.VehicleId.Should().Be(vehicleId);

        // Act & Assert - Start Trip
        updatedTrip.Start();
        _tripRepository.Update(updatedTrip);
        await _unitOfWork.SaveChangesAsync();

        var startedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        startedTrip!.Status.Should().Be(TripStatus.InProgress);
        startedTrip.StartedAt.Should().NotBeNull();

        // Act & Assert - Update Location
        var location = new TripManagement.Domain.ValueObjects.Location(40.7589, -73.9851, DateTime.UtcNow);
        startedTrip.UpdateLocation(location);
        _tripRepository.Update(startedTrip);
        await _unitOfWork.SaveChangesAsync();

        var tripWithLocation = await _tripRepository.GetByIdAsync(trip.Id);
        tripWithLocation!.LocationUpdates.Should().HaveCount(1);
        tripWithLocation.GetCurrentLocation().Should().NotBeNull();

        // Act & Assert - Complete Trip
        startedTrip.Complete();
        _tripRepository.Update(startedTrip);
        await _unitOfWork.SaveChangesAsync();

        var completedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        completedTrip!.Status.Should().Be(TripStatus.Completed);
        completedTrip.CompletedAt.Should().NotBeNull();
    }

    [Fact]
    public async Task GetTripsByCarrier_Should_Return_Correct_Trips()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var trip1 = await CreateTestTripInDatabase(carrierId: carrierId);
        var trip2 = await CreateTestTripInDatabase(carrierId: carrierId);
        var trip3 = await CreateTestTripInDatabase(carrierId: Guid.NewGuid()); // Different carrier

        // Act
        var carrierTrips = await _tripRepository.GetByCarrierIdAsync(carrierId);

        // Assert
        carrierTrips.Should().HaveCount(2);
        carrierTrips.Should().Contain(t => t.Id == trip1.Id);
        carrierTrips.Should().Contain(t => t.Id == trip2.Id);
        carrierTrips.Should().NotContain(t => t.Id == trip3.Id);
    }

    [Fact]
    public async Task GetActiveTrips_Should_Return_Only_Active_Trips()
    {
        // Arrange
        var assignedTrip = await CreateTestTripInDatabase();
        assignedTrip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        _tripRepository.Update(assignedTrip);

        var inProgressTrip = await CreateTestTripInDatabase();
        inProgressTrip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        inProgressTrip.Start();
        _tripRepository.Update(inProgressTrip);

        var completedTrip = await CreateTestTripInDatabase();
        completedTrip.AssignDriverAndVehicle(Guid.NewGuid(), Guid.NewGuid());
        completedTrip.Start();
        completedTrip.Complete();
        _tripRepository.Update(completedTrip);

        await _unitOfWork.SaveChangesAsync();

        // Act
        var activeTrips = await _tripRepository.GetActiveTripsAsync();

        // Assert
        activeTrips.Should().HaveCount(2);
        activeTrips.Should().Contain(t => t.Id == assignedTrip.Id);
        activeTrips.Should().Contain(t => t.Id == inProgressTrip.Id);
        activeTrips.Should().NotContain(t => t.Id == completedTrip.Id);
    }

    [Fact]
    public async Task GetTripByOrderId_Should_Return_Correct_Trip()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var trip = await CreateTestTripInDatabase(orderId: orderId);

        // Act
        var foundTrip = await _tripRepository.GetByOrderIdAsync(orderId);

        // Assert
        foundTrip.Should().NotBeNull();
        foundTrip!.Id.Should().Be(trip.Id);
        foundTrip.OrderId.Should().Be(orderId);
    }

    [Fact]
    public async Task GetTripByTripNumber_Should_Return_Correct_Trip()
    {
        // Arrange
        var trip = await CreateTestTripInDatabase();

        // Act
        var foundTrip = await _tripRepository.GetByTripNumberAsync(trip.TripNumber);

        // Assert
        foundTrip.Should().NotBeNull();
        foundTrip!.Id.Should().Be(trip.Id);
        foundTrip.TripNumber.Should().Be(trip.TripNumber);
    }

    [Fact]
    public async Task StartTripCommand_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripInDatabase();
        var driverId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();

        // First assign driver and vehicle
        trip.AssignDriverAndVehicle(driverId, vehicleId);
        _tripRepository.Update(trip);
        await _unitOfWork.SaveChangesAsync();

        // Create test driver
        var driver = CreateTestDriver(driverId, trip.CarrierId);
        await _driverRepository.AddAsync(driver);
        await _unitOfWork.SaveChangesAsync();

        var loggerMock = new Mock<ILogger<StartTripCommandHandler>>();
        var startHandler = new StartTripCommandHandler(
            _tripRepository,
            _driverRepository,
            _unitOfWork,
            _messageBrokerMock.Object,
            loggerMock.Object);

        var command = new StartTripCommand
        {
            TripId = trip.Id,
            DriverId = driverId,
            ActualStartTime = DateTime.UtcNow,
            Notes = "Trip started successfully"
        };

        // Act
        var result = await startHandler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        updatedTrip!.Status.Should().Be(TripStatus.InProgress);
        updatedTrip.StartedAt.Should().NotBeNull();

        var updatedDriver = await _driverRepository.GetByIdAsync(driverId);
        updatedDriver!.Status.Should().Be(DriverStatus.OnTrip);
    }

    [Fact]
    public async Task CompleteTripCommand_Should_Work_Successfully()
    {
        // Arrange
        var trip = await CreateTestTripInDatabase();
        var driverId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();

        // Setup trip lifecycle: assign -> start
        trip.AssignDriverAndVehicle(driverId, vehicleId);
        trip.Start();
        _tripRepository.Update(trip);

        // Create test driver and vehicle
        var driver = CreateTestDriver(driverId, trip.CarrierId);
        driver.UpdateStatus(DriverStatus.OnTrip);
        await _driverRepository.AddAsync(driver);

        var vehicle = CreateTestVehicle(vehicleId, trip.CarrierId);
        vehicle.UpdateStatus(VehicleStatus.InUse);
        await _vehicleRepository.AddAsync(vehicle);

        await _unitOfWork.SaveChangesAsync();

        var loggerMock = new Mock<ILogger<CompleteTripCommandHandler>>();
        var completeHandler = new CompleteTripCommandHandler(
            _tripRepository,
            _driverRepository,
            _vehicleRepository,
            _unitOfWork,
            _messageBrokerMock.Object,
            loggerMock.Object);

        var command = new CompleteTripCommand
        {
            TripId = trip.Id,
            DriverId = driverId,
            ActualEndTime = DateTime.UtcNow,
            ActualDistanceKm = 3950m,
            CompletionNotes = "Trip completed successfully",
            ProofOfDeliveryUploaded = true
        };

        // Act
        var result = await completeHandler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().BeTrue();

        var updatedTrip = await _tripRepository.GetByIdAsync(trip.Id);
        updatedTrip!.Status.Should().Be(TripStatus.Completed);
        updatedTrip.CompletedAt.Should().NotBeNull();

        var updatedDriver = await _driverRepository.GetByIdAsync(driverId);
        updatedDriver!.Status.Should().Be(DriverStatus.Available);

        var updatedVehicle = await _vehicleRepository.GetByIdAsync(vehicleId);
        updatedVehicle!.Status.Should().Be(VehicleStatus.Available);
    }

    [Fact]
    public async Task GetTripsByCarrierQuery_Should_Work_Successfully()
    {
        // Arrange
        var carrierId = Guid.NewGuid();
        var trip1 = await CreateTestTripInDatabase(carrierId: carrierId);
        var trip2 = await CreateTestTripInDatabase(carrierId: carrierId);
        var trip3 = await CreateTestTripInDatabase(carrierId: Guid.NewGuid()); // Different carrier

        var loggerMock = new Mock<ILogger<GetTripsByCarrierQueryHandler>>();
        var mapperMock = new Mock<AutoMapper.IMapper>();

        // Setup mapper to return TripSummaryDto
        mapperMock.Setup(m => m.Map<List<TripSummaryDto>>(It.IsAny<List<TripManagement.Domain.Entities.Trip>>()))
            .Returns((List<TripManagement.Domain.Entities.Trip> trips) =>
                trips.Select(t => new TripSummaryDto
                {
                    Id = t.Id,
                    TripNumber = t.TripNumber,
                    OrderId = t.OrderId,
                    CarrierId = t.CarrierId,
                    Status = t.Status,
                    EstimatedStartTime = t.EstimatedStartTime,
                    EstimatedEndTime = t.EstimatedEndTime,
                    EstimatedDistanceKm = t.EstimatedDistanceKm ?? 0,
                    IsUrgent = t.IsUrgent,
                    CreatedAt = t.CreatedAt
                }).ToList());

        var queryHandler = new GetTripsByCarrierQueryHandler(
            _tripRepository,
            mapperMock.Object,
            loggerMock.Object);

        var query = new GetTripsByCarrierQuery
        {
            CarrierId = carrierId,
            Page = 1,
            PageSize = 10
        };

        // Act
        var result = await queryHandler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.Items.Should().Contain(t => t.Id == trip1.Id);
        result.Items.Should().Contain(t => t.Id == trip2.Id);
        result.Items.Should().NotContain(t => t.Id == trip3.Id);
    }

    private async Task<TripManagement.Domain.Entities.Trip> CreateTestTripInDatabase(Guid? orderId = null, Guid? carrierId = null)
    {
        var startLocation = new TripManagement.Domain.ValueObjects.Location(40.7128, -74.0060, DateTime.UtcNow, address: "New York, NY");
        var endLocation = new TripManagement.Domain.ValueObjects.Location(34.0522, -118.2437, DateTime.UtcNow, address: "Los Angeles, CA");
        var route = new TripManagement.Domain.ValueObjects.Route(startLocation, endLocation, estimatedDistanceKm: 3944);

        var trip = new TripManagement.Domain.Entities.Trip(
            orderId ?? Guid.NewGuid(),
            carrierId ?? Guid.NewGuid(),
            route,
            DateTime.UtcNow.AddHours(1),
            DateTime.UtcNow.AddHours(41),
            "Test trip",
            false,
            3944m);

        await _tripRepository.AddAsync(trip);
        await _unitOfWork.SaveChangesAsync();

        return trip;
    }

    private CreateTripCommand CreateValidCreateTripCommand()
    {
        return new CreateTripCommand
        {
            OrderId = Guid.NewGuid(),
            CarrierId = Guid.NewGuid(),
            Route = new RouteDto
            {
                StartLocation = new LocationDto
                {
                    Latitude = 40.7128,
                    Longitude = -74.0060,
                    Address = "New York, NY",
                    Timestamp = DateTime.UtcNow
                },
                EndLocation = new LocationDto
                {
                    Latitude = 34.0522,
                    Longitude = -118.2437,
                    Address = "Los Angeles, CA",
                    Timestamp = DateTime.UtcNow
                },
                Waypoints = new List<LocationDto>(),
                EstimatedDistanceKm = 3944,
                EstimatedDuration = TimeSpan.FromHours(40)
            },
            EstimatedStartTime = DateTime.UtcNow.AddHours(1),
            EstimatedEndTime = DateTime.UtcNow.AddHours(41),
            SpecialInstructions = "Test trip",
            IsUrgent = false,
            EstimatedDistanceKm = 3944m,
            Stops = new List<CreateTripStopDto>
            {
                new()
                {
                    StopType = TripStopType.Pickup,
                    SequenceNumber = 1,
                    Location = new LocationDto
                    {
                        Latitude = 40.7128,
                        Longitude = -74.0060,
                        Address = "New York, NY",
                        Timestamp = DateTime.UtcNow
                    }
                },
                new()
                {
                    StopType = TripStopType.Delivery,
                    SequenceNumber = 2,
                    Location = new LocationDto
                    {
                        Latitude = 34.0522,
                        Longitude = -118.2437,
                        Address = "Los Angeles, CA",
                        Timestamp = DateTime.UtcNow
                    }
                }
            }
        };
    }

    private TripManagement.Domain.Entities.Driver CreateTestDriver(Guid driverId, Guid carrierId)
    {
        var driver = new TripManagement.Domain.Entities.Driver(
            Guid.NewGuid(), // UserId
            "Test",
            "Driver",
            "+91-9876543210",
            "<EMAIL>",
            "DL123456789",
            DateTime.UtcNow.AddYears(2)
        );

        // Set the ID using reflection (since it's private)
        var idField = typeof(TripManagement.Domain.Entities.Driver).GetField("_id", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        idField?.SetValue(driver, driverId);

        return driver;
    }

    private TripManagement.Domain.Entities.Vehicle CreateTestVehicle(Guid vehicleId, Guid carrierId)
    {
        var vehicle = new TripManagement.Domain.Entities.Vehicle(
            carrierId,
            "MH01AB1234",
            VehicleType.Truck,
            "Tata",
            "LPT 1618",
            2020,
            "Red",
            10000.0m,
            50.0m // Volume capacity
        );

        // Set the ID using reflection (since it's private)
        var idField = typeof(TripManagement.Domain.Entities.Vehicle).GetField("_id", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        idField?.SetValue(vehicle, vehicleId);

        return vehicle;
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
