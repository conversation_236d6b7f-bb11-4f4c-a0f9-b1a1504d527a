using UserManagement.Domain.Enums;

namespace UserManagement.Application.DTOs.Reports;

/// <summary>
/// DTO for red-flagged user report
/// </summary>
public class RedFlaggedUserReportDto
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = "Red-Flagged User Report";
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public RedFlagReportSummaryDto Summary { get; set; } = new();
    public List<FlaggedUserSummaryDto> FlaggedUsers { get; set; } = new();
    public List<FlagCategoryAnalysisDto> CategoryAnalysis { get; set; } = new();
    public List<FlagTrendDto> FlagTrends { get; set; } = new();
    public List<RiskAnalysisDto> RiskAnalysis { get; set; } = new();
    public ReportMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// DTO for red flag report summary
/// </summary>
public class RedFlagReportSummaryDto
{
    public int TotalFlaggedUsers { get; set; }
    public int ActiveFlags { get; set; }
    public int ResolvedFlags { get; set; }
    public int CriticalFlags { get; set; }
    public int HighRiskUsers { get; set; }
    public Dictionary<FlagType, int> FlagsByType { get; set; } = new();
    public Dictionary<FlagSeverity, int> FlagsBySeverity { get; set; } = new();
    public Dictionary<string, int> FlagsByCategory { get; set; } = new();
    public decimal AverageResolutionTimeHours { get; set; }
    public int FlagsRequiringImmediateAction { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
}

/// <summary>
/// DTO for flagged user summary
/// </summary>
public class FlaggedUserSummaryDto
{
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string UserType { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public UserStatus UserStatus { get; set; }
    public int TotalFlags { get; set; }
    public int ActiveFlags { get; set; }
    public FlagSeverity HighestSeverity { get; set; }
    public DateTime FirstFlaggedAt { get; set; }
    public DateTime LastFlaggedAt { get; set; }
    public List<UserFlagDetailDto> Flags { get; set; } = new();
    public Dictionary<FlagType, int> FlagsByType { get; set; } = new();
    public Dictionary<string, int> FlagsByCategory { get; set; } = new();
    public decimal RiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public bool RequiresImmediateAction { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public int DaysSinceLastActivity { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
}

/// <summary>
/// DTO for user flag details
/// </summary>
public class UserFlagDetailDto
{
    public Guid FlagId { get; set; }
    public FlagType FlagType { get; set; }
    public FlagSeverity Severity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public FlagStatus Status { get; set; }
    public DateTime FlaggedAt { get; set; }
    public string FlaggedByRole { get; set; } = string.Empty;
    public DateTime? ResolvedAt { get; set; }
    public string? ResolutionNotes { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsAutoGenerated { get; set; }
    public string? SourceSystem { get; set; }
    public string? SourceReference { get; set; }
    public int AgeInDays { get; set; }
    public bool IsExpired { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for flag category analysis
/// </summary>
public class FlagCategoryAnalysisDto
{
    public string Category { get; set; } = string.Empty;
    public FlagType FlagType { get; set; }
    public int TotalFlags { get; set; }
    public int ActiveFlags { get; set; }
    public int UniqueUsers { get; set; }
    public decimal AverageResolutionTimeHours { get; set; }
    public Dictionary<FlagSeverity, int> SeverityDistribution { get; set; } = new();
    public decimal ResolutionRate { get; set; }
    public List<string> CommonReasons { get; set; } = new();
    public decimal TrendPercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // Increasing, Decreasing, Stable
}

/// <summary>
/// DTO for flag trends
/// </summary>
public class FlagTrendDto
{
    public DateTime Date { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly
    public int NewFlags { get; set; }
    public int ResolvedFlags { get; set; }
    public int ActiveFlags { get; set; }
    public Dictionary<FlagType, int> FlagsByType { get; set; } = new();
    public Dictionary<FlagSeverity, int> FlagsBySeverity { get; set; } = new();
    public decimal ResolutionRate { get; set; }
    public decimal AverageResolutionTimeHours { get; set; }
    public int CriticalFlags { get; set; }
    public decimal TrendChange { get; set; }
}

/// <summary>
/// DTO for risk analysis
/// </summary>
public class RiskAnalysisDto
{
    public string RiskCategory { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty; // Low, Medium, High, Critical
    public int UserCount { get; set; }
    public decimal RiskScore { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public List<string> MitigationStrategies { get; set; } = new();
    public decimal PotentialImpact { get; set; }
    public string ImpactDescription { get; set; } = string.Empty;
    public List<Guid> HighRiskUserIds { get; set; } = new();
    public Dictionary<string, object> RiskMetrics { get; set; } = new();
}

/// <summary>
/// DTO for report metadata
/// </summary>
public class ReportMetadataDto
{
    public Guid RequestedBy { get; set; }
    public string RequestedByRole { get; set; } = string.Empty;
    public DateTime RequestedAt { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public TimeSpan GenerationTime { get; set; }
    public int TotalRecords { get; set; }
    public bool IsFiltered { get; set; }
    public Dictionary<string, object> FilterCriteria { get; set; } = new();
    public string ReportVersion { get; set; } = "1.0";
    public List<string> DataSources { get; set; } = new();
    public string SecurityClassification { get; set; } = "Confidential";
}
