using MediatR;

namespace SubscriptionManagement.Application.Commands.UpdatePaymentMethod
{
    public class UpdatePaymentMethodCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public string NewPaymentMethodId { get; set; } = string.Empty;
        public bool ValidatePaymentMethod { get; set; } = true;
        public string? UpdateReason { get; set; }
    }
}
