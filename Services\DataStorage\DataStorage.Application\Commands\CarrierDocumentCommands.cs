using MediatR;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.Application.Commands;

// Carrier-specific document commands
public class UploadPickupConfirmationPhotoCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream PhotoStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime? CapturedAt { get; set; }
    public string? LocationAddress { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class UploadDeliveryPhotoCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream PhotoStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime? CapturedAt { get; set; }
    public string? LocationAddress { get; set; }
    public string? RecipientName { get; set; }
    public string? RecipientContact { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class SubmitDigitalProofOfDeliveryCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }

    // Digital signature data
    public string RecipientName { get; set; } = string.Empty;
    public string RecipientContact { get; set; } = string.Empty;
    public string? RecipientIdType { get; set; }
    public string? RecipientIdNumber { get; set; }
    public byte[]? DigitalSignature { get; set; }

    // Location data
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime DeliveredAt { get; set; }
    public string? LocationAddress { get; set; }

    // Additional documents
    public List<Stream>? SupportingDocuments { get; set; }
    public List<string>? SupportingDocumentNames { get; set; }

    // Delivery details
    public string? DeliveryNotes { get; set; }
    public DeliveryCondition DeliveryCondition { get; set; } = DeliveryCondition.Good;
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class UploadDeliveryReceiptCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Stream ReceiptStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string? ReceiptNumber { get; set; }
    public DateTime? ReceiptDate { get; set; }
    public decimal? Amount { get; set; }
    public string? Currency { get; set; } = "INR";
}

public class ConfirmUnloadingCompletionCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public Guid TripId { get; set; }
    public Guid? OrderId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }

    // Unloading details
    public DateTime UnloadingStartedAt { get; set; }
    public DateTime UnloadingCompletedAt { get; set; }
    public string? UnloadingLocation { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }

    // Documentation
    public List<Stream>? UnloadingPhotos { get; set; }
    public List<string>? UnloadingPhotoNames { get; set; }
    public string? UnloadingNotes { get; set; }
    public string? SupervisorName { get; set; }
    public string? SupervisorContact { get; set; }

    // Condition assessment
    public CargoCondition CargoCondition { get; set; } = CargoCondition.Good;
    public string? DamageNotes { get; set; }
    public Dictionary<string, string>? AdditionalMetadata { get; set; }
}

public class GenerateCarrierDeliveryHistoryCommand : IRequest<DocumentDto>
{
    public Guid CarrierId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DocumentType? DocumentType { get; set; }
    public ExportFormat Format { get; set; } = ExportFormat.PDF;
    public bool IncludePhotos { get; set; } = true;
    public bool IncludeSignatures { get; set; } = true;
    public string? ReportTitle { get; set; }
}

// Supporting enums
public enum DeliveryCondition
{
    Good = 0,
    Damaged = 1,
    PartiallyDamaged = 2,
    Incomplete = 3,
    Refused = 4,
    Other = 99
}

public enum CargoCondition
{
    Good = 0,
    Damaged = 1,
    PartiallyDamaged = 2,
    Contaminated = 3,
    Missing = 4,
    Excess = 5,
    Other = 99
}
