using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using ContentManagement.Domain.Events;
using FluentAssertions;
using Xunit;

namespace ContentManagement.Tests.Domain;

public class FaqItemTests
{
    [Fact]
    public void FaqItem_Creation_Should_Set_Properties_Correctly()
    {
        // Arrange
        var question = "What is the meaning of life?";
        var answer = "42";
        var category = FaqCategory.General;
        var createdBy = Guid.NewGuid();
        var createdByName = "Test User";

        // Act
        var faqItem = new FaqItem(question, answer, category, createdBy, createdByName);

        // Assert
        faqItem.Question.Should().Be(question);
        faqItem.Answer.Should().Be(answer);
        faqItem.Category.Should().Be(category);
        faqItem.CreatedBy.Should().Be(createdBy);
        faqItem.CreatedByName.Should().Be(createdByName);
        faqItem.ViewCount.Should().Be(0);
        faqItem.HelpfulnessRating.Should().Be(0);
        faqItem.TotalRatings.Should().Be(0);
        faqItem.IsHighlighted.Should().BeFalse();
        faqItem.RoleVisibility.Should().BeEmpty();
        faqItem.DomainEvents.Should().Contain(e => e is FaqItemCreatedEvent);
    }

    [Fact]
    public void FaqItem_Creation_With_Empty_Question_Should_Throw_Exception()
    {
        // Arrange & Act & Assert
        var act = () => new FaqItem("", "answer", FaqCategory.General, Guid.NewGuid(), "user");
        act.Should().Throw<ArgumentException>().WithMessage("Question cannot be empty*");
    }

    [Fact]
    public void FaqItem_Creation_With_Empty_Answer_Should_Throw_Exception()
    {
        // Arrange & Act & Assert
        var act = () => new FaqItem("question", "", FaqCategory.General, Guid.NewGuid(), "user");
        act.Should().Throw<ArgumentException>().WithMessage("Answer cannot be empty*");
    }

    [Fact]
    public void UpdateQuestionAndAnswer_Should_Update_Properties()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var newQuestion = "What is the new meaning of life?";
        var newAnswer = "Still 42";
        var updatedBy = Guid.NewGuid();
        var updatedByName = "Updater";

        // Act
        faqItem.UpdateQuestionAndAnswer(newQuestion, newAnswer, updatedBy, updatedByName);

        // Assert
        faqItem.Question.Should().Be(newQuestion);
        faqItem.Answer.Should().Be(newAnswer);
        faqItem.DomainEvents.Should().Contain(e => e is FaqItemUpdatedEvent);
    }

    [Fact]
    public void UpdateCategory_Should_Update_Category()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var newCategory = FaqCategory.Technical;
        var updatedBy = Guid.NewGuid();
        var updatedByName = "Updater";

        // Act
        faqItem.UpdateCategory(newCategory, updatedBy, updatedByName);

        // Assert
        faqItem.Category.Should().Be(newCategory);
        faqItem.DomainEvents.Should().Contain(e => e is FaqCategoryChangedEvent);
    }

    [Fact]
    public void UpdateRoleVisibility_Should_Update_Role_Visibility()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var roleVisibility = new List<string> { "Admin", "Manager" };
        var updatedBy = Guid.NewGuid();
        var updatedByName = "Updater";

        // Act
        faqItem.UpdateRoleVisibility(roleVisibility, updatedBy, updatedByName);

        // Assert
        faqItem.RoleVisibility.Should().BeEquivalentTo(roleVisibility);
        faqItem.DomainEvents.Should().Contain(e => e is FaqRoleVisibilityChangedEvent);
    }

    [Fact]
    public void SetHighlighted_Should_Update_Highlighted_Status()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var updatedBy = Guid.NewGuid();
        var updatedByName = "Updater";

        // Act
        faqItem.SetHighlighted(true, updatedBy, updatedByName);

        // Assert
        faqItem.IsHighlighted.Should().BeTrue();
        faqItem.DomainEvents.Should().Contain(e => e is FaqHighlightChangedEvent);
    }

    [Fact]
    public void IncrementViewCount_Should_Increase_View_Count()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var initialViewCount = faqItem.ViewCount;

        // Act
        faqItem.IncrementViewCount();

        // Assert
        faqItem.ViewCount.Should().Be(initialViewCount + 1);
        faqItem.DomainEvents.Should().Contain(e => e is FaqViewedEvent);
    }

    [Fact]
    public void AddHelpfulnessRating_Should_Update_Rating_Statistics()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var rating = 4;
        var ratedBy = Guid.NewGuid();
        var ratedByName = "Rater";

        // Act
        faqItem.AddHelpfulnessRating(rating, ratedBy, ratedByName);

        // Assert
        faqItem.HelpfulnessRating.Should().Be(4);
        faqItem.TotalRatings.Should().Be(1);
        faqItem.DomainEvents.Should().Contain(e => e is FaqRatedEvent);
    }

    [Fact]
    public void AddHelpfulnessRating_Multiple_Times_Should_Calculate_Average()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var ratedBy = Guid.NewGuid();
        var ratedByName = "Rater";

        // Act
        faqItem.AddHelpfulnessRating(5, ratedBy, ratedByName);
        faqItem.AddHelpfulnessRating(3, ratedBy, ratedByName);

        // Assert
        faqItem.HelpfulnessRating.Should().Be(4); // (5 + 3) / 2 = 4
        faqItem.TotalRatings.Should().Be(2);
    }

    [Fact]
    public void AddHelpfulnessRating_With_Invalid_Rating_Should_Throw_Exception()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        var ratedBy = Guid.NewGuid();
        var ratedByName = "Rater";

        // Act & Assert
        var act = () => faqItem.AddHelpfulnessRating(0, ratedBy, ratedByName);
        act.Should().Throw<ArgumentException>().WithMessage("Rating must be between 1 and 5*");

        var act2 = () => faqItem.AddHelpfulnessRating(6, ratedBy, ratedByName);
        act2.Should().Throw<ArgumentException>().WithMessage("Rating must be between 1 and 5*");
    }

    [Fact]
    public void IsVisibleToRole_Should_Return_True_When_No_Role_Restrictions()
    {
        // Arrange
        var faqItem = CreateTestFaqItem(); // No role visibility set

        // Act & Assert
        faqItem.IsVisibleToRole("User").Should().BeTrue();
        faqItem.IsVisibleToRole("Admin").Should().BeTrue();
    }

    [Fact]
    public void IsVisibleToRole_Should_Return_True_When_Role_Is_In_Visibility_List()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        faqItem.UpdateRoleVisibility(new List<string> { "Admin", "Manager" }, Guid.NewGuid(), "Updater");

        // Act & Assert
        faqItem.IsVisibleToRole("Admin").Should().BeTrue();
        faqItem.IsVisibleToRole("Manager").Should().BeTrue();
        faqItem.IsVisibleToRole("User").Should().BeFalse();
    }

    [Fact]
    public void IsVisibleToRoles_Should_Return_True_When_Any_Role_Matches()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        faqItem.UpdateRoleVisibility(new List<string> { "Admin", "Manager" }, Guid.NewGuid(), "Updater");
        var userRoles = new List<string> { "User", "Manager" };

        // Act & Assert
        faqItem.IsVisibleToRoles(userRoles).Should().BeTrue();
    }

    [Fact]
    public void IsVisibleToRoles_Should_Return_False_When_No_Roles_Match()
    {
        // Arrange
        var faqItem = CreateTestFaqItem();
        faqItem.UpdateRoleVisibility(new List<string> { "Admin", "Manager" }, Guid.NewGuid(), "Updater");
        var userRoles = new List<string> { "User", "Guest" };

        // Act & Assert
        faqItem.IsVisibleToRoles(userRoles).Should().BeFalse();
    }

    private static FaqItem CreateTestFaqItem()
    {
        return new FaqItem(
            "What is the meaning of life?",
            "42",
            FaqCategory.General,
            Guid.NewGuid(),
            "Test User");
    }
}
