using AnalyticsBIService.Application.DTOs;
using MediatR;
using Shared.Domain.Common;

namespace AnalyticsBIService.Application.Queries.Risk;

/// <summary>
/// Query to get risk monitoring dashboard
/// </summary>
public class GetRiskMonitoringDashboardQuery : IRequest<RiskMonitoringDto>
{
    public Guid? UserId { get; set; }

    public GetRiskMonitoringDashboardQuery(Guid? userId = null)
    {
        UserId = userId;
    }
}

/// <summary>
/// Query to count issue-flagged feedback
/// </summary>
public class GetIssueFlaggedFeedbackCountQuery : IRequest<int>
{
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }

    public GetIssueFlaggedFeedbackCountQuery(Guid? entityId = null, string? entityType = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        EntityId = entityId;
        EntityType = entityType;
        FromDate = fromDate;
        ToDate = toDate;
    }
}

/// <summary>
/// Query to detect red flag trends
/// </summary>
public class GetRedFlagTrendsQuery : IRequest<List<RedFlagTrendDto>>
{
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public int LookbackDays { get; set; } = 30;

    public GetRedFlagTrendsQuery(Guid? entityId = null, string? entityType = null, int lookbackDays = 30)
    {
        EntityId = entityId;
        EntityType = entityType;
        LookbackDays = lookbackDays;
    }
}

/// <summary>
/// Query to get entity performance rankings
/// </summary>
public class GetEntityPerformanceRankingsQuery : IRequest<List<EntityPerformanceRankingDto>>
{
    public string EntityType { get; set; }
    public int TopCount { get; set; } = 10;

    public GetEntityPerformanceRankingsQuery(string entityType, int topCount = 10)
    {
        EntityType = entityType;
        TopCount = topCount;
    }
}

/// <summary>
/// Query to assess entity risk
/// </summary>
public class GetEntityRiskAssessmentQuery : IRequest<RiskAssessmentDto>
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; }

    public GetEntityRiskAssessmentQuery(Guid entityId, string entityType)
    {
        EntityId = entityId;
        EntityType = entityType;
    }
}

/// <summary>
/// Query to get active risk alerts
/// </summary>
public class GetActiveRiskAlertsQuery : IRequest<List<RiskAlertDto>>
{
    public Guid? UserId { get; set; }

    public GetActiveRiskAlertsQuery(Guid? userId = null)
    {
        UserId = userId;
    }
}

