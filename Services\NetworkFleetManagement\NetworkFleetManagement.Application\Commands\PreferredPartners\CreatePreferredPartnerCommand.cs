using MediatR;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Application.Commands.PreferredPartners;

public class CreatePreferredPartnerCommand : IRequest<Guid>
{
    public Guid UserId { get; set; }
    public Guid PartnerId { get; set; }
    public PreferredPartnerType PartnerType { get; set; }
    public PreferenceLevel PreferenceLevel { get; set; } = PreferenceLevel.Secondary;
    public int Priority { get; set; } = 10;
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool AutoAssignEnabled { get; set; } = false;
    public decimal? AutoAssignThreshold { get; set; }
    public string? Notes { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
    public bool NotifyOnNewOpportunities { get; set; } = true;
    public bool NotifyOnPerformanceChanges { get; set; } = true;
    public bool NotifyOnContractExpiry { get; set; } = true;
    
    // Agreement details (optional)
    public string? AgreementNumber { get; set; }
    public DateTime? AgreementStartDate { get; set; }
    public DateTime? AgreementEndDate { get; set; }
    public int? PaymentTermsDays { get; set; }
    public string? SpecialTerms { get; set; }
    
    // Exclusivity settings (optional)
    public bool IsExclusive { get; set; } = false;
    public DateTime? ExclusivityExpiresAt { get; set; }
}
