using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Domain.Entities;
using Shared.Infrastructure.Interfaces;
using Shared.Messaging;

namespace MobileWorkflow.Application.Commands.UpdateLocalizationSettings;

public class UpdateLocalizationSettingsCommand : IRequest<UpdateLocalizationSettingsResponse>
{
    public Guid DriverId { get; set; }
    public LocalizationSettings Settings { get; set; } = new();
}

public class UpdateLocalizationSettingsResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public LocalizationConfiguration? Configuration { get; set; }
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

public class LocalizationSettings
{
    // Language and region
    public string LanguageCode { get; set; } = "en"; // ISO 639-1 language code
    public string LanguageName { get; set; } = "English"; // Display name of the language
    public string CountryCode { get; set; } = "US"; // ISO 3166-1 alpha-2 country code
    public string RegionCode { get; set; } = "US"; // Regional variant
    public string Region { get; set; } = "US"; // Regional variant (alias for RegionCode)
    public string CultureCode { get; set; } = "en-US"; // Full culture code

    // Date and time formatting
    public string DateFormat { get; set; } = "MM/dd/yyyy";
    public string TimeFormat { get; set; } = "HH:mm";
    public string DateTimeFormat { get; set; } = "MM/dd/yyyy HH:mm";
    public string TimeZone { get; set; } = "UTC";
    public bool Use24HourFormat { get; set; } = false;
    public string FirstDayOfWeek { get; set; } = "Sunday"; // Sunday, Monday

    // Number and currency formatting
    public string NumberFormat { get; set; } = "en-US";
    public string CurrencyCode { get; set; } = "USD"; // ISO 4217 currency code
    public string CurrencySymbol { get; set; } = "$";
    public string CurrencyFormat { get; set; } = "${0:N2}";
    public string DecimalSeparator { get; set; } = ".";
    public string ThousandsSeparator { get; set; } = ",";

    // Distance and measurement units
    public string DistanceUnit { get; set; } = "Miles"; // Miles, Kilometers
    public string WeightUnit { get; set; } = "Pounds"; // Pounds, Kilograms
    public string VolumeUnit { get; set; } = "Gallons"; // Gallons, Liters
    public string TemperatureUnit { get; set; } = "Fahrenheit"; // Fahrenheit, Celsius

    // Address formatting
    public string AddressFormat { get; set; } = "US"; // US, UK, EU, etc.
    public bool ShowPostalCodeFirst { get; set; } = false;
    public string PhoneNumberFormat { get; set; } = "US"; // US, International

    // Content preferences
    public bool RightToLeftLayout { get; set; } = false;
    public string TextDirection { get; set; } = "ltr"; // ltr, rtl
    public List<string> PreferredContentLanguages { get; set; } = new();
    public bool AutoTranslateContent { get; set; } = false;

    // Accessibility and display
    public string FontSize { get; set; } = "Medium"; // Small, Medium, Large, ExtraLarge
    public bool HighContrastMode { get; set; } = false;
    public bool ReducedMotion { get; set; } = false;
    public string ColorScheme { get; set; } = "Auto"; // Auto, Light, Dark

    // Voice and audio
    public string VoiceLanguage { get; set; } = "en-US";
    public string VoiceGender { get; set; } = "Female"; // Male, Female, Neutral
    public double VoiceSpeed { get; set; } = 1.0; // 0.5 to 2.0
    public bool EnableVoiceInstructions { get; set; } = true;

    // Keyboard and input
    public string KeyboardLayout { get; set; } = "QWERTY";
    public bool EnableAutoCorrect { get; set; } = true;
    public bool EnablePredictiveText { get; set; } = true;
    public List<string> InputLanguages { get; set; } = new();

    // Custom preferences
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public bool SyncAcrossDevices { get; set; } = true;
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class LocalizationConfiguration
{
    public Guid ConfigurationId { get; set; }
    public Guid DriverId { get; set; }
    public LocalizationSettings Settings { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? DeviceId { get; set; }
    public string? AppVersion { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Command Handler

public class UpdateLocalizationSettingsCommandHandler : IRequestHandler<UpdateLocalizationSettingsCommand, UpdateLocalizationSettingsResponse>
{
    private readonly IDriverSessionRepository _sessionRepository;
    private readonly ILocalizationConfigurationRepository _configurationRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<UpdateLocalizationSettingsCommandHandler> _logger;

    public UpdateLocalizationSettingsCommandHandler(
        IDriverSessionRepository sessionRepository,
        ILocalizationConfigurationRepository configurationRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<UpdateLocalizationSettingsCommandHandler> logger)
    {
        _sessionRepository = sessionRepository;
        _configurationRepository = configurationRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<UpdateLocalizationSettingsResponse> Handle(UpdateLocalizationSettingsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating localization settings for driver {DriverId}", request.DriverId);

            // Validate settings
            var validationResult = ValidateLocalizationSettings(request.Settings);
            if (!validationResult.IsValid)
            {
                return new UpdateLocalizationSettingsResponse
                {
                    IsSuccess = false,
                    ErrorMessage = validationResult.ErrorMessage
                };
            }

            // Get or create configuration
            var configuration = await _configurationRepository.GetByDriverIdAsync(request.DriverId, cancellationToken);

            if (configuration == null)
            {
                // Create new configuration
                configuration = new MobileWorkflow.Domain.Repositories.LocalizationConfiguration(
                    request.Settings.LanguageCode,
                    request.Settings.LanguageName,
                    request.Settings.Region,
                    request.Settings.CultureCode);

                await _configurationRepository.AddAsync(configuration, cancellationToken);
            }
            else
            {
                // Update existing configuration - Domain entity doesn't have UpdateSettings method
                // We need to create a new configuration with updated values
                var updatedConfiguration = new MobileWorkflow.Domain.Repositories.LocalizationConfiguration(
                    request.Settings.LanguageCode,
                    request.Settings.LanguageName,
                    request.Settings.Region,
                    request.Settings.CultureCode);

                _configurationRepository.Update(updatedConfiguration);
                configuration = updatedConfiguration;
            }

            // Update session with localization preferences
            var session = await _sessionRepository.GetActiveSessionByUserIdAsync(request.DriverId, cancellationToken);
            if (session != null)
            {
                // Domain entity doesn't have UpdateSessionData method - use Update method instead
                _sessionRepository.Update(session);
                await _sessionRepository.UpdateAsync(session, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration event
            await _messageBroker.PublishAsync("driver.localization.updated", new
            {
                DriverId = request.DriverId,
                ConfigurationId = configuration.Id,
                LanguageCode = request.Settings.LanguageCode,
                CountryCode = request.Settings.CountryCode,
                TimeZone = request.Settings.TimeZone,
                CurrencyCode = request.Settings.CurrencyCode,
                UpdatedAt = DateTime.UtcNow
            });

            // Map to response DTO
            var responseConfiguration = new LocalizationConfiguration
            {
                ConfigurationId = configuration.Id,
                DriverId = request.DriverId, // Use request DriverId since Domain entity doesn't have this property
                Settings = request.Settings, // Use request Settings since Domain entity doesn't have this property
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                UpdatedAt = configuration.LastUpdated,
                Metadata = configuration.Metadata
            };

            _logger.LogInformation("Localization settings updated successfully for driver {DriverId}", request.DriverId);

            return new UpdateLocalizationSettingsResponse
            {
                IsSuccess = true,
                Configuration = responseConfiguration,
                UpdatedAt = configuration.LastUpdated
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating localization settings for driver {DriverId}", request.DriverId);
            return new UpdateLocalizationSettingsResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                UpdatedAt = DateTime.UtcNow
            };
        }
    }

    private ValidationResult ValidateLocalizationSettings(LocalizationSettings settings)
    {
        var errors = new List<string>();

        // Validate language code
        if (string.IsNullOrWhiteSpace(settings.LanguageCode) || settings.LanguageCode.Length != 2)
        {
            errors.Add("Language code must be a valid 2-letter ISO 639-1 code");
        }

        // Validate country code
        if (string.IsNullOrWhiteSpace(settings.CountryCode) || settings.CountryCode.Length != 2)
        {
            errors.Add("Country code must be a valid 2-letter ISO 3166-1 code");
        }

        // Validate currency code
        if (string.IsNullOrWhiteSpace(settings.CurrencyCode) || settings.CurrencyCode.Length != 3)
        {
            errors.Add("Currency code must be a valid 3-letter ISO 4217 code");
        }

        // Validate time zone
        try
        {
            TimeZoneInfo.FindSystemTimeZoneById(settings.TimeZone);
        }
        catch
        {
            errors.Add("Invalid time zone identifier");
        }

        // Validate date format
        try
        {
            DateTime.Now.ToString(settings.DateFormat);
        }
        catch
        {
            errors.Add("Invalid date format");
        }

        // Validate time format
        try
        {
            DateTime.Now.ToString(settings.TimeFormat);
        }
        catch
        {
            errors.Add("Invalid time format");
        }

        // Validate voice speed
        if (settings.VoiceSpeed < 0.5 || settings.VoiceSpeed > 2.0)
        {
            errors.Add("Voice speed must be between 0.5 and 2.0");
        }

        // Validate units
        var validDistanceUnits = new[] { "Miles", "Kilometers" };
        if (!validDistanceUnits.Contains(settings.DistanceUnit))
        {
            errors.Add("Distance unit must be Miles or Kilometers");
        }

        var validWeightUnits = new[] { "Pounds", "Kilograms" };
        if (!validWeightUnits.Contains(settings.WeightUnit))
        {
            errors.Add("Weight unit must be Pounds or Kilograms");
        }

        var validTemperatureUnits = new[] { "Fahrenheit", "Celsius" };
        if (!validTemperatureUnits.Contains(settings.TemperatureUnit))
        {
            errors.Add("Temperature unit must be Fahrenheit or Celsius");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            ErrorMessage = errors.Count > 0 ? string.Join("; ", errors) : null
        };
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
