using MediatR;

namespace TripManagement.Application.Queries.GetTripFeedbackAnalytics;

public record GetTripFeedbackAnalyticsQuery : IRequest<TripFeedbackAnalyticsResponse>
{
    public Guid? CarrierId { get; init; }
    public Guid? DriverId { get; init; }
    public Guid? VehicleId { get; init; }
    public DateTime? FromDate { get; init; }
    public DateTime? ToDate { get; init; }
    public string? ReviewerRole { get; init; }
    public decimal? MinRating { get; init; }
    public decimal? MaxRating { get; init; }
    public bool IncludeTrends { get; init; } = true;
    public bool IncludeComparisons { get; init; } = true;
    public string GroupBy { get; init; } = "Month"; // "Day", "Week", "Month", "Quarter"
}

public record TripFeedbackAnalyticsResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public FeedbackSummary Summary { get; init; } = new();
    public List<RatingBreakdown> RatingBreakdowns { get; init; } = new();
    public List<TrendDataPoint> Trends { get; init; } = new();
    public List<FeedbackInsight> Insights { get; init; } = new();
    public List<PerformanceComparison> Comparisons { get; init; } = new();
    public List<TopIssue> TopIssues { get; init; } = new();
    public List<TopHighlight> TopHighlights { get; init; } = new();
    public DateTime GeneratedAt { get; init; }
}

public record FeedbackSummary
{
    public int TotalFeedbacks { get; init; }
    public decimal AverageOverallRating { get; init; }
    public decimal AverageServiceQualityRating { get; init; }
    public decimal AverageTimelinessRating { get; init; }
    public decimal AverageCommunicationRating { get; init; }
    public decimal AverageProfessionalismRating { get; init; }
    public decimal AverageVehicleConditionRating { get; init; }
    public decimal AverageCargoHandlingRating { get; init; }
    public int PositiveFeedbacks { get; init; }
    public int NeutralFeedbacks { get; init; }
    public int NegativeFeedbacks { get; init; }
    public decimal PositivePercentage { get; init; }
    public int FeedbacksRequiringAttention { get; init; }
    public int RedFlagFeedbacks { get; init; }
}

public record RatingBreakdown
{
    public string Category { get; init; } = string.Empty; // "Overall", "ServiceQuality", etc.
    public decimal AverageRating { get; init; }
    public Dictionary<int, int> RatingDistribution { get; init; } = new(); // Rating (1-5) -> Count
    public decimal StandardDeviation { get; init; }
    public decimal MedianRating { get; init; }
    public string Trend { get; init; } = string.Empty; // "Improving", "Declining", "Stable"
}

public record TrendDataPoint
{
    public DateTime Period { get; init; }
    public string PeriodLabel { get; init; } = string.Empty;
    public int FeedbackCount { get; init; }
    public decimal AverageRating { get; init; }
    public decimal PositivePercentage { get; init; }
    public Dictionary<string, decimal> CategoryRatings { get; init; } = new();
}

public record FeedbackInsight
{
    public string Type { get; init; } = string.Empty; // "Strength", "Weakness", "Opportunity", "Threat"
    public string Category { get; init; } = string.Empty;
    public string Description { get; init; } = string.Empty;
    public decimal Impact { get; init; } // 1-10 scale
    public string Recommendation { get; init; } = string.Empty;
    public List<string> SupportingData { get; init; } = new();
}

public record PerformanceComparison
{
    public string ComparisonType { get; init; } = string.Empty; // "PeerAverage", "PreviousPeriod", "BestPerformer"
    public string Metric { get; init; } = string.Empty;
    public decimal CurrentValue { get; init; }
    public decimal ComparisonValue { get; init; }
    public decimal Difference { get; init; }
    public decimal PercentageChange { get; init; }
    public string Status { get; init; } = string.Empty; // "Better", "Worse", "Same"
}

public record TopIssue
{
    public string Issue { get; init; } = string.Empty;
    public int Frequency { get; init; }
    public decimal AverageRatingImpact { get; init; }
    public List<string> RelatedTags { get; init; } = new();
    public string Severity { get; init; } = string.Empty; // "Low", "Medium", "High", "Critical"
    public string SuggestedAction { get; init; } = string.Empty;
}

public record TopHighlight
{
    public string Highlight { get; init; } = string.Empty;
    public int Frequency { get; init; }
    public decimal AverageRatingBoost { get; init; }
    public List<string> RelatedTags { get; init; } = new();
    public string Impact { get; init; } = string.Empty; // "Low", "Medium", "High"
}
