using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TripManagement.Domain.Entities;

namespace TripManagement.Infrastructure.Configurations;

public class TripLocationUpdateConfiguration : IEntityTypeConfiguration<TripLocationUpdate>
{
    public void Configure(EntityTypeBuilder<TripLocationUpdate> builder)
    {
        builder.ToTable("trip_location_updates");

        builder.HasKey(tlu => tlu.Id);

        builder.Property(tlu => tlu.Id)
            .ValueGeneratedNever();

        builder.Property(tlu => tlu.TripId)
            .IsRequired();

        builder.Property(tlu => tlu.Timestamp)
            .IsRequired();

        builder.Property(tlu => tlu.Source)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(tlu => tlu.Speed)
            .HasPrecision(8, 2);

        builder.Property(tlu => tlu.Heading)
            .HasPrecision(6, 2);

        // Configure Location as owned entity
        builder.OwnsOne(tlu => tlu.Location, location =>
        {
            location.Property(l => l.Latitude)
                .IsRequired()
                .HasPrecision(10, 7);

            location.Property(l => l.Longitude)
                .IsRequired()
                .HasPrecision(10, 7);

            location.Property(l => l.Altitude)
                .HasPrecision(10, 2);

            location.Property(l => l.Accuracy)
                .HasPrecision(10, 2);

            location.Property(l => l.Timestamp)
                .IsRequired();

            location.Property(l => l.Address)
                .HasMaxLength(500);

            location.Property(l => l.City)
                .HasMaxLength(100);

            location.Property(l => l.State)
                .HasMaxLength(100);

            location.Property(l => l.Country)
                .HasMaxLength(100);

            location.Property(l => l.PostalCode)
                .HasMaxLength(20);
        });

        // Indexes
        builder.HasIndex(tlu => tlu.TripId);
        builder.HasIndex(tlu => tlu.Timestamp);
        builder.HasIndex(tlu => tlu.Source);
    }
}

public class TripExceptionConfiguration : IEntityTypeConfiguration<TripException>
{
    public void Configure(EntityTypeBuilder<TripException> builder)
    {
        builder.ToTable("trip_exceptions");

        builder.HasKey(te => te.Id);

        builder.Property(te => te.Id)
            .ValueGeneratedNever();

        builder.Property(te => te.TripId)
            .IsRequired();

        builder.Property(te => te.ExceptionType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(te => te.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(te => te.ReportedAt)
            .IsRequired();

        builder.Property(te => te.IsResolved)
            .IsRequired();

        builder.Property(te => te.ResolvedAt);

        builder.Property(te => te.Resolution)
            .HasMaxLength(1000);

        builder.Property(te => te.ResolvedBy)
            .HasMaxLength(100);

        // Configure Location as owned entity (optional)
        builder.OwnsOne(te => te.Location, location =>
        {
            location.Property(l => l.Latitude)
                .HasPrecision(10, 7);

            location.Property(l => l.Longitude)
                .HasPrecision(10, 7);

            location.Property(l => l.Altitude)
                .HasPrecision(10, 2);

            location.Property(l => l.Accuracy)
                .HasPrecision(10, 2);

            location.Property(l => l.Timestamp);

            location.Property(l => l.Address)
                .HasMaxLength(500);

            location.Property(l => l.City)
                .HasMaxLength(100);

            location.Property(l => l.State)
                .HasMaxLength(100);

            location.Property(l => l.Country)
                .HasMaxLength(100);

            location.Property(l => l.PostalCode)
                .HasMaxLength(20);
        });

        // Indexes
        builder.HasIndex(te => te.TripId);
        builder.HasIndex(te => te.ExceptionType);
        builder.HasIndex(te => te.ReportedAt);
        builder.HasIndex(te => te.IsResolved);
    }
}

public class TripDocumentConfiguration : IEntityTypeConfiguration<TripDocument>
{
    public void Configure(EntityTypeBuilder<TripDocument> builder)
    {
        builder.ToTable("trip_documents");

        builder.HasKey(td => td.Id);

        builder.Property(td => td.Id)
            .ValueGeneratedNever();

        builder.Property(td => td.TripId)
            .IsRequired();

        builder.Property(td => td.DocumentType)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(td => td.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(td => td.FileUrl)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(td => td.Description)
            .HasMaxLength(500);

        builder.Property(td => td.UploadedAt)
            .IsRequired();

        builder.Property(td => td.UploadedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(td => td.TripId);
        builder.HasIndex(td => td.DocumentType);
        builder.HasIndex(td => td.UploadedAt);
    }
}

public class ProofOfDeliveryConfiguration : IEntityTypeConfiguration<ProofOfDelivery>
{
    public void Configure(EntityTypeBuilder<ProofOfDelivery> builder)
    {
        builder.ToTable("proof_of_deliveries");

        builder.HasKey(pod => pod.Id);

        builder.Property(pod => pod.Id)
            .ValueGeneratedNever();

        builder.Property(pod => pod.TripStopId)
            .IsRequired();

        builder.Property(pod => pod.RecipientName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(pod => pod.RecipientSignature)
            .HasMaxLength(2000); // Base64 encoded signature

        builder.Property(pod => pod.PhotoUrl)
            .HasMaxLength(500);

        builder.Property(pod => pod.DeliveredAt)
            .IsRequired();

        builder.Property(pod => pod.Notes)
            .HasMaxLength(1000);

        builder.Property(pod => pod.IsDigitalSignature)
            .IsRequired();

        builder.Property(pod => pod.DeliveredBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(pod => pod.TripStopId);
        builder.HasIndex(pod => pod.DeliveredAt);
    }
}
