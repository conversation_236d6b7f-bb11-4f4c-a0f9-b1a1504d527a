using Shared.Domain.Common;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Enums;

namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Individual entry in a trip ledger
/// </summary>
public class TripLedgerEntry : BaseEntity
{
    public Guid TripLedgerId { get; private set; }
    public TripLedgerEntryType EntryType { get; private set; }
    public Money Amount { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public Guid? RelatedEntityId { get; private set; }
    public string? RelatedEntityType { get; private set; }
    public DateTime EntryDate { get; private set; }
    public Guid? ProcessedBy { get; private set; }
    public string? ReferenceNumber { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Navigation properties
    public TripLedger TripLedger { get; private set; } = null!;

    private TripLedgerEntry() { } // EF Constructor

    public TripLedgerEntry(
        Guid tripLedgerId,
        TripLedgerEntryType entryType,
        Money amount,
        string description,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        Dictionary<string, object>? metadata = null,
        Guid? processedBy = null,
        string? referenceNumber = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        Id = Guid.NewGuid();
        TripLedgerId = tripLedgerId;
        EntryType = entryType;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        Description = description;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        EntryDate = DateTime.UtcNow;
        ProcessedBy = processedBy;
        ReferenceNumber = referenceNumber ?? GenerateReferenceNumber();
        Metadata = metadata ?? new Dictionary<string, object>();
        CreatedAt = DateTime.UtcNow;
    }

    private string GenerateReferenceNumber()
    {
        return $"TLE-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..6].ToUpper()}";
    }
}

/// <summary>
/// Milestone tracking in trip ledger
/// </summary>
public class TripLedgerMilestone : BaseEntity
{
    public Guid TripLedgerId { get; private set; }
    public string MilestoneName { get; private set; } = string.Empty;
    public Money Amount { get; private set; }
    public DateTime DueDate { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public bool IsCompleted { get; private set; }
    public DateTime? CompletedDate { get; private set; }
    public string? CompletionNotes { get; private set; }
    public Guid? CompletedBy { get; private set; }
    public int SortOrder { get; private set; }

    // Navigation properties
    public TripLedger TripLedger { get; private set; } = null!;

    private TripLedgerMilestone() { } // EF Constructor

    public TripLedgerMilestone(
        Guid tripLedgerId,
        string milestoneName,
        Money amount,
        DateTime dueDate,
        string description,
        bool isCompleted = false,
        DateTime? completedDate = null,
        int sortOrder = 0)
    {
        if (string.IsNullOrWhiteSpace(milestoneName))
            throw new ArgumentException("Milestone name cannot be empty", nameof(milestoneName));

        Id = Guid.NewGuid();
        TripLedgerId = tripLedgerId;
        MilestoneName = milestoneName;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        DueDate = dueDate;
        Description = description ?? string.Empty;
        IsCompleted = isCompleted;
        CompletedDate = completedDate;
        SortOrder = sortOrder;
        CreatedAt = DateTime.UtcNow;
    }

    public void Complete(DateTime completedDate, string? notes = null, Guid? completedBy = null)
    {
        if (IsCompleted)
            throw new InvalidOperationException("Milestone is already completed");

        IsCompleted = true;
        CompletedDate = completedDate;
        CompletionNotes = notes;
        CompletedBy = completedBy;
    }

    public void Reopen(string reason)
    {
        if (!IsCompleted)
            throw new InvalidOperationException("Milestone is not completed");

        IsCompleted = false;
        CompletionNotes = $"Reopened: {reason}. Previous completion: {CompletionNotes}";
        CompletedDate = null;
        CompletedBy = null;
    }
}

/// <summary>
/// Adjustments to trip ledger
/// </summary>
public class TripLedgerAdjustment : BaseEntity
{
    public Guid TripLedgerId { get; private set; }
    public TripLedgerAdjustmentType AdjustmentType { get; private set; }
    public Money Amount { get; private set; }
    public string Reason { get; private set; } = string.Empty;
    public Guid AdjustedBy { get; private set; }
    public DateTime AdjustmentDate { get; private set; }
    public string? ApprovalReference { get; private set; }
    public TripLedgerAdjustmentStatus Status { get; private set; }
    public string? RejectionReason { get; private set; }
    public Guid? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }

    // Navigation properties
    public TripLedger TripLedger { get; private set; } = null!;

    private TripLedgerAdjustment() { } // EF Constructor

    public TripLedgerAdjustment(
        Guid tripLedgerId,
        TripLedgerAdjustmentType adjustmentType,
        Money amount,
        string reason,
        Guid adjustedBy,
        string? approvalReference = null)
    {
        if (string.IsNullOrWhiteSpace(reason))
            throw new ArgumentException("Reason cannot be empty", nameof(reason));

        Id = Guid.NewGuid();
        TripLedgerId = tripLedgerId;
        AdjustmentType = adjustmentType;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        Reason = reason;
        AdjustedBy = adjustedBy;
        AdjustmentDate = DateTime.UtcNow;
        ApprovalReference = approvalReference;
        Status = string.IsNullOrWhiteSpace(approvalReference) ? 
            TripLedgerAdjustmentStatus.PendingApproval : 
            TripLedgerAdjustmentStatus.Approved;
        CreatedAt = DateTime.UtcNow;
    }

    public void Approve(Guid approvedBy, string? approvalReference = null)
    {
        if (Status != TripLedgerAdjustmentStatus.PendingApproval)
            throw new InvalidOperationException("Can only approve pending adjustments");

        Status = TripLedgerAdjustmentStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
        
        if (!string.IsNullOrWhiteSpace(approvalReference))
            ApprovalReference = approvalReference;
    }

    public void Reject(string rejectionReason, Guid rejectedBy)
    {
        if (Status != TripLedgerAdjustmentStatus.PendingApproval)
            throw new InvalidOperationException("Can only reject pending adjustments");

        Status = TripLedgerAdjustmentStatus.Rejected;
        RejectionReason = rejectionReason;
        ApprovedBy = rejectedBy;
        ApprovedDate = DateTime.UtcNow;
    }
}

/// <summary>
/// Summary information for trip ledger
/// </summary>
public class TripLedgerSummary
{
    public Guid LedgerId { get; set; }
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public string LedgerNumber { get; set; } = string.Empty;
    public string TripNumber { get; set; } = string.Empty;
    public TripLedgerStatus Status { get; set; }
    public Money TotalOrderValue { get; set; } = null!;
    public Money TotalPaid { get; set; } = null!;
    public Money TotalPending { get; set; } = null!;
    public Money OutstandingBalance { get; set; } = null!;
    public int TotalEntries { get; set; }
    public int TotalMilestones { get; set; }
    public int CompletedMilestones { get; set; }
    public int TotalAdjustments { get; set; }
    public DateTime TripStartDate { get; set; }
    public DateTime? TripEndDate { get; set; }
    public DateTime? SettlementDate { get; set; }
    public DateTime LastUpdated { get; set; }
    
    // Calculated properties
    public decimal CompletionPercentage => TotalMilestones > 0 ? 
        (decimal)CompletedMilestones / TotalMilestones * 100 : 0;
    
    public decimal PaymentPercentage => TotalOrderValue.Amount > 0 ? 
        TotalPaid.Amount / TotalOrderValue.Amount * 100 : 0;
    
    public bool IsOverdue => Status == TripLedgerStatus.Active && 
        OutstandingBalance.Amount > 0 && 
        DateTime.UtcNow > TripStartDate.AddDays(30); // Configurable
}

/// <summary>
/// Enums for trip ledger
/// </summary>
public enum TripLedgerStatus
{
    Active = 0,
    Completed = 1,
    Settled = 2,
    Closed = 3,
    Cancelled = 4
}

public enum TripLedgerEntryType
{
    // Credits (Money In)
    Payment = 0,
    Advance = 1,
    Refund = 2,
    Adjustment = 3,
    
    // Debits (Money Out)
    Commission = 100,
    Tax = 101,
    Fee = 102,
    Penalty = 103,
    Deduction = 104
}

public enum TripLedgerAdjustmentType
{
    Credit = 0,
    Debit = 1,
    Correction = 2,
    Penalty = 3,
    Bonus = 4,
    Discount = 5
}

public enum TripLedgerAdjustmentStatus
{
    PendingApproval = 0,
    Approved = 1,
    Rejected = 2,
    Cancelled = 3
}

/// <summary>
/// Extension methods for trip ledger enums
/// </summary>
public static class TripLedgerExtensions
{
    public static bool IsCredit(this TripLedgerEntryType entryType)
    {
        return (int)entryType < 100;
    }

    public static bool IsDebit(this TripLedgerEntryType entryType)
    {
        return (int)entryType >= 100;
    }

    public static string GetDisplayName(this TripLedgerEntryType entryType)
    {
        return entryType switch
        {
            TripLedgerEntryType.Payment => "Payment Received",
            TripLedgerEntryType.Advance => "Advance Payment",
            TripLedgerEntryType.Refund => "Refund Issued",
            TripLedgerEntryType.Adjustment => "Adjustment",
            TripLedgerEntryType.Commission => "Commission Charged",
            TripLedgerEntryType.Tax => "Tax Applied",
            TripLedgerEntryType.Fee => "Service Fee",
            TripLedgerEntryType.Penalty => "Penalty Charged",
            TripLedgerEntryType.Deduction => "Deduction Applied",
            _ => entryType.ToString()
        };
    }

    public static string GetDisplayName(this TripLedgerStatus status)
    {
        return status switch
        {
            TripLedgerStatus.Active => "Active",
            TripLedgerStatus.Completed => "Trip Completed",
            TripLedgerStatus.Settled => "Fully Settled",
            TripLedgerStatus.Closed => "Closed",
            TripLedgerStatus.Cancelled => "Cancelled",
            _ => status.ToString()
        };
    }
}


