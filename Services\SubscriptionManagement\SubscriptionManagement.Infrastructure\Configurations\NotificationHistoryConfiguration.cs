using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class NotificationHistoryConfiguration : IEntityTypeConfiguration<NotificationHistory>
    {
        public void Configure(EntityTypeBuilder<NotificationHistory> builder)
        {
            builder.ToTable("NotificationHistories");

            builder.HasKey(n => n.Id);

            builder.Property(n => n.Id)
                .IsRequired()
                .ValueGeneratedNever();

            builder.Property(n => n.SubscriptionId);

            builder.Property(n => n.UserId);

            builder.Property(n => n.Type)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(n => n.Channel)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(n => n.Subject)
                .HasMaxLength(500);

            builder.Property(n => n.Body)
                .IsRequired()
                .HasColumnType("text");

            builder.Property(n => n.Status)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("Pending");

            builder.Property(n => n.ExternalNotificationId)
                .HasMaxLength(200);

            builder.Property(n => n.TriggeredByUserId)
                .IsRequired();

            builder.Property(n => n.ScheduledAt)
                .HasColumnType("datetime2");

            builder.Property(n => n.SentAt)
                .HasColumnType("datetime2");

            builder.Property(n => n.DeliveredAt)
                .HasColumnType("datetime2");

            builder.Property(n => n.ReadAt)
                .HasColumnType("datetime2");

            builder.Property(n => n.ClickedAt)
                .HasColumnType("datetime2");

            builder.Property(n => n.ErrorMessage)
                .HasMaxLength(2000);

            builder.Property(n => n.ErrorCode)
                .HasMaxLength(100);

            builder.Property(n => n.RetryCount)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(n => n.LastRetryAt)
                .HasColumnType("datetime2");

            // Configure Metadata as JSON
            builder.Property(n => n.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                .HasColumnType("jsonb");

            // Configure DeliveryMetadata as JSON
            builder.Property(n => n.DeliveryMetadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, string>())
                .HasColumnType("jsonb");

            builder.Property(n => n.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(n => n.UpdatedAt)
                .HasColumnType("datetime2");

            // Configure indexes
            builder.HasIndex(n => n.SubscriptionId)
                .HasDatabaseName("IX_NotificationHistories_SubscriptionId");

            builder.HasIndex(n => n.UserId)
                .HasDatabaseName("IX_NotificationHistories_UserId");

            builder.HasIndex(n => n.Type)
                .HasDatabaseName("IX_NotificationHistories_Type");

            builder.HasIndex(n => n.Channel)
                .HasDatabaseName("IX_NotificationHistories_Channel");

            builder.HasIndex(n => n.Status)
                .HasDatabaseName("IX_NotificationHistories_Status");

            builder.HasIndex(n => n.ExternalNotificationId)
                .HasDatabaseName("IX_NotificationHistories_ExternalNotificationId");

            builder.HasIndex(n => n.TriggeredByUserId)
                .HasDatabaseName("IX_NotificationHistories_TriggeredByUserId");

            builder.HasIndex(n => n.ScheduledAt)
                .HasDatabaseName("IX_NotificationHistories_ScheduledAt");

            builder.HasIndex(n => n.SentAt)
                .HasDatabaseName("IX_NotificationHistories_SentAt");

            builder.HasIndex(n => n.DeliveredAt)
                .HasDatabaseName("IX_NotificationHistories_DeliveredAt");

            builder.HasIndex(n => n.CreatedAt)
                .HasDatabaseName("IX_NotificationHistories_CreatedAt");

            // Composite indexes for common queries
            builder.HasIndex(n => new { n.Status, n.CreatedAt })
                .HasDatabaseName("IX_NotificationHistories_Status_CreatedAt");

            builder.HasIndex(n => new { n.Channel, n.Status })
                .HasDatabaseName("IX_NotificationHistories_Channel_Status");

            builder.HasIndex(n => new { n.Type, n.Status })
                .HasDatabaseName("IX_NotificationHistories_Type_Status");

            builder.HasIndex(n => new { n.UserId, n.Type, n.CreatedAt })
                .HasDatabaseName("IX_NotificationHistories_UserId_Type_CreatedAt");

            builder.HasIndex(n => new { n.SubscriptionId, n.Type, n.CreatedAt })
                .HasDatabaseName("IX_NotificationHistories_SubscriptionId_Type_CreatedAt");

            builder.HasIndex(n => new { n.Status, n.RetryCount, n.LastRetryAt })
                .HasDatabaseName("IX_NotificationHistories_Status_RetryCount_LastRetryAt");
        }
    }
}
