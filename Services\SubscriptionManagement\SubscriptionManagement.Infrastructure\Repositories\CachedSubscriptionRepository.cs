using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Infrastructure.Services;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class CachedSubscriptionRepository : ISubscriptionRepository
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly ISubscriptionCacheService _cacheService;
        private readonly ILogger<CachedSubscriptionRepository> _logger;

        public CachedSubscriptionRepository(
            ISubscriptionRepository subscriptionRepository,
            ISubscriptionCacheService cacheService,
            ILogger<CachedSubscriptionRepository> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<Subscription?> GetByIdAsync(Guid id)
        {
            try
            {
                // Try cache first
                var cachedSubscription = await _cacheService.GetSubscriptionAsync(id);
                if (cachedSubscription != null)
                {
                    _logger.LogDebug("Subscription {SubscriptionId} retrieved from cache", id);
                    return cachedSubscription;
                }

                // Load from database
                var subscription = await _subscriptionRepository.GetByIdAsync(id);
                if (subscription != null)
                {
                    await _cacheService.SetSubscriptionAsync(subscription);
                    _logger.LogDebug("Subscription {SubscriptionId} loaded from database and cached", id);
                }

                return subscription;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription {SubscriptionId}", id);
                // Fallback to database only
                return await _subscriptionRepository.GetByIdAsync(id);
            }
        }

        public async Task<Subscription?> GetByUserIdAsync(Guid userId)
        {
            try
            {
                // Try cache first
                var cachedSubscription = await _cacheService.GetUserSubscriptionAsync(userId);
                if (cachedSubscription != null)
                {
                    _logger.LogDebug("User subscription {UserId} retrieved from cache", userId);
                    return cachedSubscription;
                }

                // Load from database
                var subscription = await _subscriptionRepository.GetByUserIdAsync(userId);
                if (subscription != null)
                {
                    await _cacheService.SetSubscriptionAsync(subscription);
                    await _cacheService.SetUserSubscriptionStatusAsync(userId, subscription.Status);
                    _logger.LogDebug("User subscription {UserId} loaded from database and cached", userId);
                }

                return subscription;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user subscription {UserId}", userId);
                // Fallback to database only
                return await _subscriptionRepository.GetByUserIdAsync(userId);
            }
        }

        public async Task<List<Subscription>> GetByUserIdAndStatusAsync(Guid userId, SubscriptionStatus status)
        {
            return await _subscriptionRepository.GetByUserIdAndStatusAsync(userId, status);
        }

        public async Task<List<Subscription>> GetActiveSubscriptionsAsync()
        {
            return await _subscriptionRepository.GetActiveSubscriptionsAsync();
        }

        public async Task<List<Subscription>> GetExpiredSubscriptionsAsync()
        {
            return await _subscriptionRepository.GetExpiredSubscriptionsAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsDueForRenewalAsync()
        {
            return await _subscriptionRepository.GetSubscriptionsDueForRenewalAsync();
        }

        public async Task<List<Subscription>> GetSubscriptionsDueForBillingAsync(DateTime billingDate)
        {
            return await _subscriptionRepository.GetSubscriptionsDueForBillingAsync(billingDate);
        }

        public async Task<List<Subscription>> GetSubscriptionsByPlanIdAsync(Guid planId)
        {
            return await _subscriptionRepository.GetSubscriptionsByPlanIdAsync(planId);
        }

        public async Task<List<Subscription>> GetSubscriptionsAsync(int page, int pageSize)
        {
            return await _subscriptionRepository.GetSubscriptionsAsync(page, pageSize);
        }

        public async Task<int> GetSubscriptionsCountAsync()
        {
            return await _subscriptionRepository.GetSubscriptionsCountAsync();
        }

        public async Task<int> GetActiveSubscriptionsCountAsync()
        {
            return await _subscriptionRepository.GetActiveSubscriptionsCountAsync();
        }

        public async Task<int> GetSubscriptionsCountByStatusAsync(SubscriptionStatus status)
        {
            return await _subscriptionRepository.GetSubscriptionsCountByStatusAsync(status);
        }

        public async Task<decimal> GetTotalRevenueAsync()
        {
            return await _subscriptionRepository.GetTotalRevenueAsync();
        }

        public async Task<decimal> GetMonthlyRecurringRevenueAsync()
        {
            return await _subscriptionRepository.GetMonthlyRecurringRevenueAsync();
        }

        public async Task AddAsync(Subscription subscription)
        {
            await _subscriptionRepository.AddAsync(subscription);
            
            // Cache the new subscription
            await _cacheService.SetSubscriptionAsync(subscription);
            await _cacheService.SetUserSubscriptionStatusAsync(subscription.UserId, subscription.Status);
            _logger.LogDebug("Added and cached subscription {SubscriptionId}", subscription.Id);
        }

        public async Task UpdateAsync(Subscription subscription)
        {
            await _subscriptionRepository.UpdateAsync(subscription);
            
            // Update cache
            await _cacheService.SetSubscriptionAsync(subscription);
            await _cacheService.SetUserSubscriptionStatusAsync(subscription.UserId, subscription.Status);
            _logger.LogDebug("Updated and cached subscription {SubscriptionId}", subscription.Id);
        }

        public async Task DeleteAsync(Subscription subscription)
        {
            await _subscriptionRepository.DeleteAsync(subscription);
            
            // Remove from cache
            await _cacheService.RemoveSubscriptionAsync(subscription.Id);
            await _cacheService.RemoveUserSubscriptionAsync(subscription.UserId);
            await _cacheService.RemoveUserSubscriptionStatusAsync(subscription.UserId);
            _logger.LogDebug("Deleted subscription {SubscriptionId} and removed from cache", subscription.Id);
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            // Check cache first
            var cachedSubscription = await _cacheService.GetSubscriptionAsync(id);
            if (cachedSubscription != null)
            {
                return true;
            }

            return await _subscriptionRepository.ExistsAsync(id);
        }

        public async Task<bool> HasActiveSubscriptionAsync(Guid userId)
        {
            // Check cache first
            var cachedStatus = await _cacheService.GetUserSubscriptionStatusAsync(userId);
            if (cachedStatus.HasValue)
            {
                return cachedStatus.Value == SubscriptionStatus.Active;
            }

            var hasActive = await _subscriptionRepository.HasActiveSubscriptionAsync(userId);
            
            // Cache the result
            if (hasActive)
            {
                await _cacheService.SetUserSubscriptionStatusAsync(userId, SubscriptionStatus.Active);
            }

            return hasActive;
        }
    }
}
