namespace AnalyticsBIService.Application.Events;

/// <summary>
/// Integration event published when an analytics event is tracked
/// </summary>
public class AnalyticsEventTrackedEvent
{
    public Guid EventId { get; set; }
    public string EventName { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserType { get; set; }
    public Guid? EntityId { get; set; }
    public string? EntityType { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public string? SessionId { get; set; }
}

/// <summary>
/// Integration event published when a metric is calculated
/// </summary>
public class MetricCalculatedEvent
{
    public Guid MetricId { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public string MetricType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime CalculatedAt { get; set; }
    public Dictionary<string, object> Dimensions { get; set; } = new();
    public string? DataSource { get; set; }
}

/// <summary>
/// Integration event published when an alert is triggered
/// </summary>
public class AlertTriggeredEvent
{
    public Guid AlertId { get; set; }
    public string AlertName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Guid? MetricId { get; set; }
    public decimal? ThresholdValue { get; set; }
    public decimal? ActualValue { get; set; }
    public DateTime TriggeredAt { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// Integration event published when a report is generated
/// </summary>
public class ReportGeneratedEvent
{
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string Format { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public long? FileSizeBytes { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Integration event published when a dashboard is accessed
/// </summary>
public class DashboardAccessedEvent
{
    public Guid DashboardId { get; set; }
    public string DashboardName { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public DateTime AccessedAt { get; set; }
    public string? SessionId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

/// <summary>
/// Integration event published when data pipeline execution completes
/// </summary>
public class DataPipelineExecutedEvent
{
    public Guid PipelineId { get; set; }
    public string PipelineName { get; set; } = string.Empty;
    public Guid ExecutionId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public int RecordsProcessed { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> ExecutionMetrics { get; set; } = new();
}

/// <summary>
/// Integration event published when KPI threshold is breached
/// </summary>
public class KPIThresholdBreachedEvent
{
    public Guid KPIId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal ThresholdValue { get; set; }
    public string ThresholdType { get; set; } = string.Empty; // Above, Below, Equal
    public string Severity { get; set; } = string.Empty;
    public DateTime BreachedAt { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// Integration event published when analytics data export is completed
/// </summary>
public class AnalyticsDataExportedEvent
{
    public Guid ExportId { get; set; }
    public string ExportType { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public DateTime RequestedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public string Format { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public long RecordCount { get; set; }
    public long FileSizeBytes { get; set; }
    public Dictionary<string, object> ExportParameters { get; set; } = new();
}
