using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Report template entity for defining reusable report structures
/// </summary>
public class ReportTemplate : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; }
    public string TemplateContent { get; private set; }
    public string Parameters { get; private set; }
    public UserType UserType { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedDate { get; private set; }
    public DateTime? LastModifiedDate { get; private set; }
    public Guid CreatedBy { get; private set; }
    public Guid? LastModifiedBy { get; private set; }

    // Alias properties for service compatibility
    public string Content => TemplateContent; // Alias for TemplateContent

    // Navigation properties
    public virtual ICollection<ReportExecution> ReportExecutions { get; private set; } = new List<ReportExecution>();

    // Private constructor for EF Core
    private ReportTemplate()
    {
        Name = string.Empty;
        Description = string.Empty;
        Category = string.Empty;
        TemplateContent = string.Empty;
        Parameters = string.Empty;
    }

    /// <summary>
    /// Create a new report template
    /// </summary>
    public static ReportTemplate Create(
        string name,
        string description,
        string category,
        string templateContent,
        string parameters,
        UserType userType,
        Guid createdBy)
    {
        var template = new ReportTemplate
        {
            Name = name,
            Description = description,
            Category = category,
            TemplateContent = templateContent,
            Parameters = parameters,
            UserType = userType,
            IsActive = true,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = createdBy
        };

        return template;
    }

    /// <summary>
    /// Update report template
    /// </summary>
    public void Update(
        string name,
        string description,
        string category,
        string templateContent,
        string parameters,
        UserType userType,
        Guid modifiedBy)
    {
        Name = name;
        Description = description;
        Category = category;
        TemplateContent = templateContent;
        Parameters = parameters;
        UserType = userType;
        LastModifiedDate = DateTime.UtcNow;
        LastModifiedBy = modifiedBy;
    }

    /// <summary>
    /// Activate the template
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        LastModifiedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Deactivate the template
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        LastModifiedDate = DateTime.UtcNow;
    }
}
