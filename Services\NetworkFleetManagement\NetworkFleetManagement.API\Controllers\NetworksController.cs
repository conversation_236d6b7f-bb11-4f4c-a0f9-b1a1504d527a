using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.Networks;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.Networks;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class NetworksController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<NetworksController> _logger;

    public NetworksController(IMediator mediator, ILogger<NetworksController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all network relationships with pagination and filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<BrokerCarrierNetworkDto>>> GetNetworks(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? brokerId = null,
        [FromQuery] Guid? carrierId = null,
        [FromQuery] NetworkRelationshipStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworksQuery(pageNumber, pageSize, brokerId, carrierId, status);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving networks");
            return StatusCode(500, "An error occurred while retrieving networks");
        }
    }

    /// <summary>
    /// Get network relationship by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> GetNetwork(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworkByIdQuery(id);
            var result = await _mediator.Send(query, cancellationToken);
            
            if (result == null)
                return NotFound($"Network relationship with ID {id} not found");
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving network {NetworkId}", id);
            return StatusCode(500, "An error occurred while retrieving the network relationship");
        }
    }

    /// <summary>
    /// Create a new broker-carrier network relationship
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> CreateNetwork(
        [FromBody] CreateBrokerCarrierNetworkDto createNetworkDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateBrokerCarrierNetworkCommand(createNetworkDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return CreatedAtAction(
                nameof(GetNetwork),
                new { id = result.Id },
                result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating network relationship");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating network relationship");
            return StatusCode(500, "An error occurred while creating the network relationship");
        }
    }

    /// <summary>
    /// Activate network relationship
    /// </summary>
    [HttpPut("{id:guid}/activate")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> ActivateNetwork(
        Guid id,
        [FromBody] ActivateNetworkDto activateDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new ActivateNetworkCommand(id, activateDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while activating network");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating network {NetworkId}", id);
            return StatusCode(500, "An error occurred while activating the network");
        }
    }

    /// <summary>
    /// Suspend network relationship
    /// </summary>
    [HttpPut("{id:guid}/suspend")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> SuspendNetwork(
        Guid id,
        [FromBody] SuspendNetworkDto suspendDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new SuspendNetworkCommand(id, suspendDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while suspending network");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending network {NetworkId}", id);
            return StatusCode(500, "An error occurred while suspending the network");
        }
    }

    /// <summary>
    /// Terminate network relationship
    /// </summary>
    [HttpPut("{id:guid}/terminate")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> TerminateNetwork(
        Guid id,
        [FromBody] TerminateNetworkDto terminateDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new TerminateNetworkCommand(id, terminateDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while terminating network");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating network {NetworkId}", id);
            return StatusCode(500, "An error occurred while terminating the network");
        }
    }

    /// <summary>
    /// Update network priority
    /// </summary>
    [HttpPut("{id:guid}/priority")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> UpdateNetworkPriority(
        Guid id,
        [FromBody] UpdateNetworkPriorityDto priorityDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateNetworkPriorityCommand(id, priorityDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating network priority");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating network priority for {NetworkId}", id);
            return StatusCode(500, "An error occurred while updating the network priority");
        }
    }

    /// <summary>
    /// Update network rates
    /// </summary>
    [HttpPut("{id:guid}/rates")]
    public async Task<ActionResult<BrokerCarrierNetworkDto>> UpdateNetworkRates(
        Guid id,
        [FromBody] UpdateNetworkRatesDto ratesDto,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateNetworkRatesCommand(id, ratesDto);
            var result = await _mediator.Send(command, cancellationToken);
            
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating network rates");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating network rates for {NetworkId}", id);
            return StatusCode(500, "An error occurred while updating the network rates");
        }
    }

    /// <summary>
    /// Get networks by broker
    /// </summary>
    [HttpGet("by-broker/{brokerId:guid}")]
    public async Task<ActionResult<IEnumerable<BrokerCarrierNetworkDto>>> GetNetworksByBroker(
        Guid brokerId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworksByBrokerQuery(brokerId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving networks for broker {BrokerId}", brokerId);
            return StatusCode(500, "An error occurred while retrieving networks");
        }
    }

    /// <summary>
    /// Get networks by carrier
    /// </summary>
    [HttpGet("by-carrier/{carrierId:guid}")]
    public async Task<ActionResult<IEnumerable<BrokerCarrierNetworkDto>>> GetNetworksByCarrier(
        Guid carrierId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworksByCarrierQuery(carrierId);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving networks for carrier {CarrierId}", carrierId);
            return StatusCode(500, "An error occurred while retrieving networks");
        }
    }

    /// <summary>
    /// Get active networks
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<BrokerCarrierNetworkDto>>> GetActiveNetworks(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetActiveNetworksQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active networks");
            return StatusCode(500, "An error occurred while retrieving active networks");
        }
    }

    /// <summary>
    /// Get networks by priority for a broker
    /// </summary>
    [HttpGet("by-broker/{brokerId:guid}/priority")]
    public async Task<ActionResult<IEnumerable<BrokerCarrierNetworkDto>>> GetNetworksByPriority(
        Guid brokerId,
        [FromQuery] int maxPriority = 5,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworksByPriorityQuery(brokerId, maxPriority);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving networks by priority for broker {BrokerId}", brokerId);
            return StatusCode(500, "An error occurred while retrieving networks by priority");
        }
    }

    /// <summary>
    /// Get network performance
    /// </summary>
    [HttpGet("{id:guid}/performance")]
    public async Task<ActionResult<NetworkPerformanceDto>> GetNetworkPerformance(
        Guid id,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetNetworkPerformanceQuery(id, fromDate, toDate);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving network performance for {NetworkId}", id);
            return StatusCode(500, "An error occurred while retrieving network performance");
        }
    }
}
