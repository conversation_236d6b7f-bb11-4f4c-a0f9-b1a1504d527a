using MediatR;

namespace TripManagement.Application.Commands.ScheduleMaintenance;

public record ScheduleMaintenanceCommand : IRequest<Guid>
{
    public Guid VehicleId { get; init; }
    public MaintenanceType MaintenanceType { get; init; }
    public DateTime ScheduledDate { get; init; }
    public string Description { get; init; } = string.Empty;
    public string? ServiceProvider { get; init; }
    public decimal? EstimatedCost { get; init; }
    public int? EstimatedDurationHours { get; init; }
    public MaintenancePriority Priority { get; init; } = MaintenancePriority.Medium;
    public bool IsRecurring { get; init; }
    public int? RecurrenceIntervalDays { get; init; }
    public string? Notes { get; init; }
    public Guid ScheduledBy { get; init; }
}

public enum MaintenanceType
{
    Routine = 0,
    Preventive = 1,
    Corrective = 2,
    Emergency = 3,
    Inspection = 4,
    Repair = 5,
    Replacement = 6,
    Upgrade = 7
}

public enum MaintenancePriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}
