# Mobile & Workflow Service Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Mobile & Workflow Service in various environments including development, staging, and production.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+), Windows Server 2019+, or macOS 12+
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **Memory**: Minimum 8GB RAM, Recommended 16GB+ RAM
- **Storage**: Minimum 100GB SSD, Recommended 500GB+ SSD
- **Network**: Stable internet connection with adequate bandwidth

### Software Dependencies
- **.NET 8.0 Runtime**: Latest stable version
- **SQL Server 2019+** or **PostgreSQL 13+**: Database server
- **Redis 6.0+**: Caching and session storage
- **Docker 20.10+**: Container runtime (for containerized deployment)
- **Kubernetes 1.24+**: Container orchestration (for K8s deployment)

## Environment Configuration

### Development Environment

#### 1. Clone Repository
```bash
git clone https://github.com/company/mobile-workflow-service.git
cd mobile-workflow-service
```

#### 2. Configure Database
```bash
# SQL Server
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=YourPassword123!" \
  -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2019-latest

# PostgreSQL
docker run --name postgres -e POSTGRES_PASSWORD=password \
  -p 5432:5432 -d postgres:13
```

#### 3. Configure Redis
```bash
docker run --name redis -p 6379:6379 -d redis:6-alpine
```

#### 4. Update Configuration
Edit `appsettings.Development.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=MobileWorkflow;User Id=sa;Password=YourPassword123!;TrustServerCertificate=true;",
    "Redis": "localhost:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

#### 5. Run Database Migrations
```bash
dotnet ef database update --project Services/MobileWorkflow/MobileWorkflow.Infrastructure
```

#### 6. Start Application
```bash
cd Services/MobileWorkflow/MobileWorkflow.API
dotnet run
```

### Staging Environment

#### 1. Infrastructure Setup
```yaml
# docker-compose.staging.yml
version: '3.8'
services:
  mobile-workflow-api:
    image: mobileworkflow/api:staging
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ConnectionStrings__DefaultConnection=Server=db;Database=MobileWorkflow;User Id=sa;Password=StagingPassword123!;
      - ConnectionStrings__Redis=redis:6379
    depends_on:
      - db
      - redis

  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=StagingPassword123!
    volumes:
      - staging_db_data:/var/opt/mssql

  redis:
    image: redis:6-alpine
    volumes:
      - staging_redis_data:/data

volumes:
  staging_db_data:
  staging_redis_data:
```

#### 2. Deploy to Staging
```bash
docker-compose -f docker-compose.staging.yml up -d
```

### Production Environment

#### Option 1: Docker Deployment

##### 1. Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  mobile-workflow-api:
    image: mobileworkflow/api:latest
    ports:
      - "80:80"
      - "443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=CertPassword
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/cert.pfx
    volumes:
      - ./certs:/https:ro
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_PASSWORD}
    volumes:
      - prod_db_data:/var/opt/mssql
      - ./backups:/var/backups
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - prod_redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/certs:ro
    depends_on:
      - mobile-workflow-api
    restart: unless-stopped

volumes:
  prod_db_data:
  prod_redis_data:
```

##### 2. Environment Variables
Create `.env` file:
```env
DB_PASSWORD=SecureProductionPassword123!
REDIS_PASSWORD=SecureRedisPassword123!
JWT_SECRET=YourSecureJWTSecretKey
ENCRYPTION_KEY=YourSecureEncryptionKey
```

##### 3. Deploy to Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

#### Option 2: Kubernetes Deployment

##### 1. Namespace
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: mobile-workflow
```

##### 2. ConfigMap
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: mobile-workflow-config
  namespace: mobile-workflow
data:
  appsettings.json: |
    {
      "Logging": {
        "LogLevel": {
          "Default": "Information"
        }
      },
      "AllowedHosts": "*"
    }
```

##### 3. Secrets
```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: mobile-workflow-secrets
  namespace: mobile-workflow
type: Opaque
data:
  db-connection: <base64-encoded-connection-string>
  redis-connection: <base64-encoded-redis-connection>
  jwt-secret: <base64-encoded-jwt-secret>
```

##### 4. Deployment
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mobile-workflow-api
  namespace: mobile-workflow
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mobile-workflow-api
  template:
    metadata:
      labels:
        app: mobile-workflow-api
    spec:
      containers:
      - name: api
        image: mobileworkflow/api:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: mobile-workflow-secrets
              key: db-connection
        - name: ConnectionStrings__Redis
          valueFrom:
            secretKeyRef:
              name: mobile-workflow-secrets
              key: redis-connection
        volumeMounts:
        - name: config
          mountPath: /app/appsettings.json
          subPath: appsettings.json
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: mobile-workflow-config
```

##### 5. Service
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mobile-workflow-api-service
  namespace: mobile-workflow
spec:
  selector:
    app: mobile-workflow-api
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
```

##### 6. Ingress
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mobile-workflow-ingress
  namespace: mobile-workflow
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.mobileworkflow.com
    secretName: mobile-workflow-tls
  rules:
  - host: api.mobileworkflow.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mobile-workflow-api-service
            port:
              number: 80
```

##### 7. Deploy to Kubernetes
```bash
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secrets.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
kubectl apply -f ingress.yaml
```

## Database Setup

### SQL Server Setup
```sql
-- Create database
CREATE DATABASE MobileWorkflow;
GO

-- Create application user
CREATE LOGIN MobileWorkflowUser WITH PASSWORD = 'SecurePassword123!';
GO

USE MobileWorkflow;
GO

CREATE USER MobileWorkflowUser FOR LOGIN MobileWorkflowUser;
GO

ALTER ROLE db_owner ADD MEMBER MobileWorkflowUser;
GO
```

### PostgreSQL Setup
```sql
-- Create database and user
CREATE DATABASE mobileworkflow;
CREATE USER mobileworkflowuser WITH PASSWORD 'SecurePassword123!';
GRANT ALL PRIVILEGES ON DATABASE mobileworkflow TO mobileworkflowuser;
```

## Security Configuration

### SSL/TLS Setup
1. Obtain SSL certificates from a trusted CA
2. Configure HTTPS in application settings
3. Set up certificate renewal automation

### Firewall Configuration
```bash
# Allow HTTP and HTTPS traffic
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow database access (restrict to application servers)
sudo ufw allow from 10.0.0.0/8 to any port 1433
sudo ufw allow from 10.0.0.0/8 to any port 6379
```

### Environment Variables Security
- Use Azure Key Vault, AWS Secrets Manager, or similar
- Never store secrets in configuration files
- Rotate secrets regularly

## Monitoring and Logging

### Application Insights (Azure)
```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-instrumentation-key"
  }
}
```

### Prometheus Metrics
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'mobile-workflow'
    static_configs:
      - targets: ['localhost:5000']
    metrics_path: '/metrics'
```

### Log Aggregation
Configure centralized logging with:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Azure Monitor
- AWS CloudWatch
- Splunk

## Backup and Recovery

### Database Backup
```bash
# SQL Server backup script
sqlcmd -S localhost -U sa -P 'Password123!' -Q "BACKUP DATABASE MobileWorkflow TO DISK = '/var/backups/mobileworkflow_$(date +%Y%m%d_%H%M%S).bak'"

# PostgreSQL backup script
pg_dump -h localhost -U mobileworkflowuser -d mobileworkflow > /var/backups/mobileworkflow_$(date +%Y%m%d_%H%M%S).sql
```

### Automated Backup Schedule
```bash
# Add to crontab
0 2 * * * /path/to/backup-script.sh
```

## Performance Optimization

### Application Settings
```json
{
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxRequestBodySize": 10485760
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=db;Database=MobileWorkflow;Connection Timeout=30;Command Timeout=60;"
  }
}
```

### Redis Configuration
```conf
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
- Check connection string format
- Verify database server is running
- Check firewall rules
- Validate credentials

#### 2. Redis Connection Issues
- Verify Redis server status
- Check Redis password configuration
- Validate network connectivity

#### 3. Performance Issues
- Monitor CPU and memory usage
- Check database query performance
- Review application logs
- Analyze Redis cache hit rates

### Health Check Endpoints
- `/health`: Basic health check
- `/health/ready`: Readiness probe
- `/health/live`: Liveness probe

### Log Locations
- Application logs: `/app/logs/`
- System logs: `/var/log/`
- Container logs: `docker logs <container-name>`

## Scaling

### Horizontal Scaling
- Deploy multiple API instances behind load balancer
- Use Redis for session state
- Implement database read replicas

### Vertical Scaling
- Increase CPU and memory allocation
- Optimize database performance
- Tune garbage collection settings

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Review and rotate secrets quarterly
- Perform security audits annually
- Monitor and optimize performance continuously

### Update Process
1. Test updates in staging environment
2. Schedule maintenance window
3. Deploy updates with zero-downtime strategy
4. Verify functionality post-deployment
5. Monitor for issues

For additional support, contact the DevOps team or refer to the troubleshooting documentation.
