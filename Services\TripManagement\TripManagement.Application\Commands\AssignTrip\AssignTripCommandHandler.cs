using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;

namespace TripManagement.Application.Commands.AssignTrip;

public class AssignTripCommandHandler : IRequestHandler<AssignTripCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<AssignTripCommandHandler> _logger;

    public AssignTripCommandHandler(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<AssignTripCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(AssignTripCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Assigning trip {TripId} to driver {DriverId} and vehicle {VehicleId}",
            request.TripId, request.DriverId, request.VehicleId);

        // Get trip
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip == null)
        {
            _logger.LogWarning("Trip {TripId} not found", request.TripId);
            throw new ArgumentException($"Trip {request.TripId} not found");
        }

        // Verify driver exists and is available
        var driver = await _driverRepository.GetByIdAsync(request.DriverId, cancellationToken);
        if (driver == null)
        {
            _logger.LogWarning("Driver {DriverId} not found", request.DriverId);
            throw new ArgumentException($"Driver {request.DriverId} not found");
        }

        if (!driver.IsAvailable)
        {
            _logger.LogWarning("Driver {DriverId} is not available", request.DriverId);
            throw new InvalidOperationException($"Driver {request.DriverId} is not available");
        }

        // Verify vehicle exists and is available
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        if (vehicle == null)
        {
            _logger.LogWarning("Vehicle {VehicleId} not found", request.VehicleId);
            throw new ArgumentException($"Vehicle {request.VehicleId} not found");
        }

        if (!vehicle.IsAvailable)
        {
            _logger.LogWarning("Vehicle {VehicleId} is not available", request.VehicleId);
            throw new InvalidOperationException($"Vehicle {request.VehicleId} is not available");
        }

        // Assign trip
        trip.AssignDriverAndVehicle(request.DriverId, request.VehicleId);

        // Update driver and vehicle status
        driver.UpdateStatus(Domain.Enums.DriverStatus.OnTrip);
        vehicle.UpdateStatus(Domain.Enums.VehicleStatus.InUse);

        // Add trip to driver and vehicle
        driver.AddTrip(trip);
        vehicle.AddTrip(trip);

        // Save changes
        _tripRepository.Update(trip);
        _driverRepository.Update(driver);
        _vehicleRepository.Update(vehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration events
        await _messageBroker.PublishAsync("trip.assigned", new
        {
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            DriverId = request.DriverId,
            VehicleId = request.VehicleId,
            CarrierId = trip.CarrierId,
            AssignedAt = trip.AssignedAt
        }, cancellationToken);

        await _messageBroker.PublishAsync("driver.assigned_to_trip", new
        {
            DriverId = request.DriverId,
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            Status = driver.Status.ToString()
        }, cancellationToken);

        await _messageBroker.PublishAsync("vehicle.assigned_to_trip", new
        {
            VehicleId = request.VehicleId,
            TripId = trip.Id,
            TripNumber = trip.TripNumber,
            Status = vehicle.Status.ToString()
        }, cancellationToken);

        _logger.LogInformation("Successfully assigned trip {TripId} to driver {DriverId} and vehicle {VehicleId}",
            request.TripId, request.DriverId, request.VehicleId);

        return true;
    }
}
