using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using UserManagement.Infrastructure.Data;

namespace UserManagement.Infrastructure.Repositories
{
    public class DocumentSubmissionRepository : IDocumentSubmissionRepository
    {
        private readonly UserManagementDbContext _context;

        public DocumentSubmissionRepository(UserManagementDbContext context)
        {
            _context = context;
        }

        public async Task<DocumentSubmission> GetByIdAsync(Guid id)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .FirstOrDefaultAsync(ds => ds.Id == id);
        }

        public async Task<DocumentSubmission> GetByUserIdAsync(Guid userId)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .FirstOrDefaultAsync(ds => ds.UserId == userId);
        }

        public async Task<IEnumerable<DocumentSubmission>> GetByStatusAsync(DocumentStatus status)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .Where(ds => ds.OverallStatus == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<DocumentSubmission>> GetPendingReviewAsync()
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .Where(ds => ds.OverallStatus == DocumentStatus.UnderReview)
                .OrderBy(ds => ds.SubmittedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<DocumentSubmission>> GetByReviewerAsync(Guid reviewerId)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .Where(ds => ds.ReviewedBy == reviewerId)
                .ToListAsync();
        }

        public async Task<IEnumerable<DocumentSubmission>> GetByUserTypeAsync(UserType userType)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .Where(ds => ds.UserType == userType)
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(Guid userId)
        {
            return await _context.DocumentSubmissions
                .AnyAsync(ds => ds.UserId == userId);
        }

        public async Task AddAsync(DocumentSubmission documentSubmission)
        {
            await _context.DocumentSubmissions.AddAsync(documentSubmission);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(DocumentSubmission documentSubmission)
        {
            _context.DocumentSubmissions.Update(documentSubmission);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var documentSubmission = await GetByIdAsync(id);
            if (documentSubmission != null)
            {
                _context.DocumentSubmissions.Remove(documentSubmission);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetCountByStatusAsync(DocumentStatus status)
        {
            return await _context.DocumentSubmissions
                .CountAsync(ds => ds.OverallStatus == status);
        }

        public async Task<int> GetPendingDocumentsCountAsync()
        {
            return await _context.DocumentSubmissions
                .CountAsync(ds => ds.OverallStatus == DocumentStatus.UnderReview ||
                                 ds.OverallStatus == DocumentStatus.RequiresResubmission);
        }

        public async Task<IEnumerable<DocumentSubmission>> GetSubmissionsRequiringAttentionAsync()
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .Where(ds => ds.OverallStatus == DocumentStatus.UnderReview ||
                           ds.OverallStatus == DocumentStatus.RequiresResubmission)
                .OrderBy(ds => ds.SubmittedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<DocumentSubmission>> GetRecentSubmissionsAsync(int count = 10)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .OrderByDescending(ds => ds.SubmittedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<List<DocumentSubmission>> GetDocumentSubmissionsAsync(Dictionary<string, object> filters)
        {
            var query = _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .AsQueryable();

            // Apply filters
            if (filters.ContainsKey("UserIds") && filters["UserIds"] is List<Guid> userIds && userIds.Any())
            {
                query = query.Where(ds => userIds.Contains(ds.UserId));
            }

            if (filters.ContainsKey("DocumentTypes") && filters["DocumentTypes"] is List<DocumentType> documentTypes && documentTypes.Any())
            {
                query = query.Where(ds => ds.Documents.Any(d => documentTypes.Contains(d.DocumentType)));
            }

            if (filters.ContainsKey("Status") && filters["Status"] is DocumentStatus status)
            {
                query = query.Where(ds => ds.OverallStatus == status);
            }

            if (filters.ContainsKey("FromDate") && filters["FromDate"] is DateTime fromDate)
            {
                query = query.Where(ds => ds.CreatedAt >= fromDate);
            }

            if (filters.ContainsKey("ToDate") && filters["ToDate"] is DateTime toDate)
            {
                query = query.Where(ds => ds.CreatedAt <= toDate);
            }

            if (filters.ContainsKey("UserType") && filters["UserType"] is UserType userType)
            {
                query = query.Where(ds => ds.UserType == userType);
            }

            return await query.OrderBy(ds => ds.CreatedAt).ToListAsync();
        }

        public async Task<DocumentSubmission> GetDocumentByIdAsync(Guid documentId)
        {
            return await _context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .FirstOrDefaultAsync(ds => ds.Id == documentId);
        }
    }
}
