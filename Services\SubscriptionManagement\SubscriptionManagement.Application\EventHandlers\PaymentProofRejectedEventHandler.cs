using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Events;

namespace SubscriptionManagement.Application.EventHandlers
{
    public class PaymentProofRejectedEventHandler : INotificationHandler<PaymentProofRejectedEvent>
    {
        private readonly ILogger<PaymentProofRejectedEventHandler> _logger;

        public PaymentProofRejectedEventHandler(ILogger<PaymentProofRejectedEventHandler> logger)
        {
            _logger = logger;
        }

        public async Task Handle(PaymentProofRejectedEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Payment proof {PaymentProofId} rejected for subscription {SubscriptionId}. " +
                "Reason: {RejectionReason}, Rejected by: {RejectedByUserId}", 
                notification.PaymentProofId, notification.SubscriptionId, 
                notification.RejectionReason, notification.RejectedByUserId);

            // TODO: Send notification to user about payment rejection
            // This could integrate with the Communication/Notification service
            // Example:
            // await _notificationService.SendPaymentRejectedNotificationAsync(
            //     notification.UserId, 
            //     notification.SubscriptionId, 
            //     notification.RejectionReason);

            // TODO: Update analytics/metrics
            // Example:
            // await _metricsService.RecordPaymentRejectionAsync(
            //     notification.SubscriptionId, 
            //     notification.RejectionReason);

            await Task.CompletedTask;
        }
    }
}
