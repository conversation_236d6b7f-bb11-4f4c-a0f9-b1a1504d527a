using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using System.Text;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.MessageBus;

public class RabbitMQPublisher : IMessageBusPublisher, IDisposable
{
    private readonly RabbitMQSettings _settings;
    private readonly ILogger<RabbitMQPublisher> _logger;
    private readonly IConnection _connection;
    private readonly IModel _channel;
    private bool _disposed = false;

    public RabbitMQPublisher(IOptions<RabbitMQSettings> settings, ILogger<RabbitMQPublisher> logger)
    {
        _settings = settings.Value;
        _logger = logger;

        try
        {
            var factory = new ConnectionFactory
            {
                HostName = _settings.HostName,
                Port = _settings.Port,
                UserName = _settings.UserName,
                Password = _settings.Password,
                VirtualHost = _settings.VirtualHost,
                AutomaticRecoveryEnabled = true,
                NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
            };

            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();

            // Declare exchange
            _channel.ExchangeDeclare(
                exchange: _settings.ExchangeName,
                type: ExchangeType.Topic,
                durable: _settings.Durable,
                autoDelete: _settings.AutoDelete);

            _logger.LogInformation("RabbitMQ connection established successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to establish RabbitMQ connection");
            throw;
        }
    }

    public async Task PublishAsync<T>(T message, string routingKey, CancellationToken cancellationToken = default) where T : class
    {
        var jsonMessage = JsonSerializer.Serialize(message, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await PublishAsync(jsonMessage, routingKey, cancellationToken);
    }

    public async Task PublishAsync(string message, string routingKey, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(RabbitMQPublisher));
        }

        var retryCount = 0;
        while (retryCount <= _settings.RetryCount)
        {
            try
            {
                var body = Encoding.UTF8.GetBytes(message);
                var properties = _channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.MessageId = Guid.NewGuid().ToString();
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
                properties.ContentType = "application/json";
                properties.ContentEncoding = "utf-8";

                _channel.BasicPublish(
                    exchange: _settings.ExchangeName,
                    routingKey: routingKey,
                    basicProperties: properties,
                    body: body);

                _logger.LogDebug("Message published successfully to {Exchange} with routing key {RoutingKey}", 
                    _settings.ExchangeName, routingKey);

                return;
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogWarning(ex, "Failed to publish message (attempt {Attempt}/{MaxAttempts})", 
                    retryCount, _settings.RetryCount + 1);

                if (retryCount > _settings.RetryCount)
                {
                    _logger.LogError(ex, "Failed to publish message after {MaxAttempts} attempts", 
                        _settings.RetryCount + 1);
                    throw;
                }

                await Task.Delay(TimeSpan.FromSeconds(_settings.RetryDelaySeconds), cancellationToken);
            }
        }
    }

    public async Task PublishIntegrationEventAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class
    {
        var eventType = typeof(T).Name;
        var routingKey = $"network-fleet.{eventType.ToLowerInvariant()}";

        var eventWrapper = new
        {
            EventId = Guid.NewGuid(),
            EventType = eventType,
            Source = "NetworkFleetManagement",
            Timestamp = DateTime.UtcNow,
            Version = "1.0",
            Data = integrationEvent
        };

        await PublishAsync(eventWrapper, routingKey, cancellationToken);
        
        _logger.LogInformation("Integration event {EventType} published with routing key {RoutingKey}", 
            eventType, routingKey);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                _channel?.Close();
                _channel?.Dispose();
                _connection?.Close();
                _connection?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error occurred while disposing RabbitMQ resources");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
