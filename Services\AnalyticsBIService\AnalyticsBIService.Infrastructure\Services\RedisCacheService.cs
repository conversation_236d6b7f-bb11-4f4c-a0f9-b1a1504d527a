using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Redis implementation of cache service
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        IDistributedCache distributedCache,
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<RedisCacheService> logger)
    {
        _distributedCache = distributedCache;
        _database = connectionMultiplexer.GetDatabase();
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            if (string.IsNullOrEmpty(cachedValue))
                return null;

            return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached value for key {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
                options.SetAbsoluteExpiration(expiration.Value);

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cached value for key {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached value for key {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern);
            
            var keyArray = keys.ToArray();
            if (keyArray.Length > 0)
            {
                await _database.KeyDeleteAsync(keyArray);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached values by pattern {Pattern}", pattern);
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists {Key}", key);
            return false;
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        if (cachedValue != null)
            return cachedValue;

        var value = await factory();
        await SetAsync(key, value, expiration, cancellationToken);
        return value;
    }

    public async Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.StringIncrementAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing value for key {Key}", key);
            return 0;
        }
    }

    public async Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExpireAsync(key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting expiration for key {Key}", key);
            return false;
        }
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class
    {
        var result = new Dictionary<string, T?>();
        
        try
        {
            var keyArray = keys.ToArray();
            var values = await _database.StringGetAsync(keyArray.Select(k => (RedisKey)k).ToArray());
            
            for (int i = 0; i < keyArray.Length; i++)
            {
                var key = keyArray[i];
                var value = values[i];
                
                if (value.HasValue && !value.IsNull)
                {
                    try
                    {
                        result[key] = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    }
                    catch
                    {
                        result[key] = null;
                    }
                }
                else
                {
                    result[key] = null;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple cached values");
        }

        return result;
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var tasks = keyValuePairs.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple cached values");
        }
    }

    public async Task<long> ListPushAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.ListLeftPushAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pushing to list for key {Key}", key);
            return 0;
        }
    }

    public async Task<List<T>> ListGetAsync<T>(string key, long start = 0, long stop = -1, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var values = await _database.ListRangeAsync(key, start, stop);
            var result = new List<T>();
            
            foreach (var value in values)
            {
                if (value.HasValue)
                {
                    try
                    {
                        var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                        if (item != null)
                            result.Add(item);
                    }
                    catch
                    {
                        // Skip invalid items
                    }
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list for key {Key}", key);
            return new List<T>();
        }
    }

    public async Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetAddAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding to set for key {Key}", key);
            return false;
        }
    }

    public async Task<HashSet<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var values = await _database.SetMembersAsync(key);
            var result = new HashSet<T>();
            
            foreach (var value in values)
            {
                if (value.HasValue)
                {
                    try
                    {
                        var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                        if (item != null)
                            result.Add(item);
                    }
                    catch
                    {
                        // Skip invalid items
                    }
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting set members for key {Key}", key);
            return new HashSet<T>();
        }
    }

    public async Task HashSetAsync<T>(string key, string field, T value, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            await _database.HashSetAsync(key, field, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting hash field {Field} for key {Key}", field, key);
        }
    }

    public async Task<T?> HashGetAsync<T>(string key, string field, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var value = await _database.HashGetAsync(key, field);
            if (!value.HasValue)
                return null;

            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hash field {Field} for key {Key}", field, key);
            return null;
        }
    }

    public async Task<Dictionary<string, T>> HashGetAllAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var hash = await _database.HashGetAllAsync(key);
            var result = new Dictionary<string, T>();
            
            foreach (var item in hash)
            {
                try
                {
                    var value = JsonSerializer.Deserialize<T>(item.Value!, _jsonOptions);
                    if (value != null)
                        result[item.Name!] = value;
                }
                catch
                {
                    // Skip invalid items
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all hash fields for key {Key}", key);
            return new Dictionary<string, T>();
        }
    }
}
