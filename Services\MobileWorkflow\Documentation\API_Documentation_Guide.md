# TLI Mobile & Workflow API Documentation Guide

## Overview

This guide provides comprehensive documentation for the TLI Mobile & Workflow Service API, including milestone configuration, workflow management, and mobile application features.

## API Documentation Access

### Interactive API Documentation (Swagger UI)

**Development Environment:**
- URL: `https://localhost:5001/api-docs`
- Features: Interactive testing, request/response examples, authentication testing

**Production Environment:**
- URL: `https://api.tli.com/mobile-workflow/api-docs`
- Features: Full API reference, authentication required

### OpenAPI Specification

**JSON Format:**
- Development: `https://localhost:5001/api-docs/v1/swagger.json`
- Production: `https://api.tli.com/mobile-workflow/api-docs/v1/swagger.json`

## API Structure

### Base URLs

- **Development**: `https://localhost:5001/api`
- **Staging**: `https://staging-api.tli.com/mobile-workflow/api`
- **Production**: `https://api.tli.com/mobile-workflow/api`

### API Versioning

- Current Version: `v1`
- Version Header: `API-Version: v1`
- URL Versioning: `/api/v1/` (future versions)

## Authentication & Authorization

### JWT Bearer Token Authentication

All API endpoints require JWT Bearer token authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### Role-Based Access Control

| Role | Permissions |
|------|-------------|
| **Admin** | Full access to all endpoints, can create/update/delete all resources |
| **Manager** | Read/write access to templates and configurations, limited delete permissions |
| **User** | Read access to assigned templates and workflows, execute workflows |

### Token Acquisition

```bash
# Example token request
curl -X POST "https://auth.tli.com/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-password"
  }'
```

## API Endpoints Overview

### Milestone Templates (`/api/MilestoneTemplate`)

| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| GET | `/` | Get all templates with filtering | Admin, Manager |
| GET | `/{id}` | Get template by ID | Admin, Manager, User |
| POST | `/` | Create new template | Admin, Manager |
| PUT | `/{id}` | Update template | Admin, Manager |
| DELETE | `/{id}` | Delete template | Admin |
| POST | `/{id}/activate` | Activate template | Admin, Manager |
| POST | `/{id}/deactivate` | Deactivate template | Admin, Manager |
| GET | `/{id}/validate` | Validate template | Admin, Manager |
| GET | `/search` | Search templates | Admin, Manager |

### Milestone Steps (`/api/milestone-templates/{templateId}/MilestoneStep`)

| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| GET | `/` | Get steps for template | Admin, Manager, User |
| GET | `/required` | Get required steps | Admin, Manager, User |
| POST | `/` | Create new step | Admin, Manager |
| PUT | `/{stepId}` | Update step | Admin, Manager |
| DELETE | `/{stepId}` | Delete step | Admin |

### Payout Rules (`/api/milestone-steps/{stepId}/payout-rules`)

| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| GET | `/` | Get payout rules for step | Admin, Manager, User |
| POST | `/` | Create new payout rule | Admin, Manager |
| PUT | `/{ruleId}` | Update payout rule | Admin, Manager |
| DELETE | `/{ruleId}` | Delete payout rule | Admin |

### Role Template Mappings (`/api/RoleTemplateMapping`)

| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| GET | `/` | Get all role mappings | Admin, Manager |
| GET | `/by-role/{roleName}` | Get mappings by role | Admin, Manager, User |
| GET | `/default/{roleName}` | Get default mapping for role | Admin, Manager, User |
| POST | `/best-match/{roleName}` | Get best matching template | Admin, Manager, User |
| POST | `/` | Create role mapping | Admin, Manager |
| PUT | `/{id}` | Update role mapping | Admin, Manager |
| DELETE | `/{id}` | Delete role mapping | Admin |

## Request/Response Examples

### Create Milestone Template

**Request:**
```json
POST /api/MilestoneTemplate
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Express Delivery Milestones",
  "description": "Fast-track milestone template for express delivery",
  "type": "Delivery",
  "category": "Express",
  "configuration": {
    "auto_advance": true,
    "sla_hours": 4
  },
  "steps": [
    {
      "name": "Package Picked",
      "description": "Package picked from sender",
      "sequenceNumber": 1,
      "isRequired": true,
      "payoutRules": [
        {
          "payoutPercentage": 40.0,
          "description": "Pickup payment"
        }
      ]
    },
    {
      "name": "Package Delivered",
      "description": "Package delivered to recipient",
      "sequenceNumber": 2,
      "isRequired": true,
      "payoutRules": [
        {
          "payoutPercentage": 60.0,
          "description": "Delivery payment"
        }
      ]
    }
  ]
}
```

**Response:**
```json
HTTP/1.1 201 Created
Content-Type: application/json

{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Express Delivery Milestones",
  "description": "Fast-track milestone template for express delivery",
  "type": "Delivery",
  "category": "Express",
  "isActive": true,
  "isDefault": false,
  "createdBy": "<EMAIL>",
  "createdAt": "2024-01-20T10:30:00Z",
  "totalPayoutPercentage": 100.0,
  "isValid": true
}
```

## Error Handling

### Standard Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Template validation failed",
    "details": [
      "Total payout percentage must equal 100%",
      "Step sequence numbers must be consecutive"
    ],
    "timestamp": "2024-01-20T15:30:00Z",
    "traceId": "abc123def456"
  }
}
```

### Common HTTP Status Codes

| Code | Description | When It Occurs |
|------|-------------|----------------|
| 200 | OK | Successful GET, PUT requests |
| 201 | Created | Successful POST requests |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Invalid request data or validation errors |
| 401 | Unauthorized | Missing or invalid authentication token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Requested resource not found |
| 409 | Conflict | Resource already exists |
| 422 | Unprocessable Entity | Business rule validation failed |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Unexpected server error |

## Rate Limiting

### Limits

- **Standard Rate**: 1000 requests per hour per API key
- **Burst Rate**: 100 requests per minute
- **Admin Operations**: 500 requests per hour (create/update/delete)

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642694400
X-RateLimit-Retry-After: 3600
```

## Pagination

### Query Parameters

- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 10, max: 100)
- `sortBy`: Sort field (default: createdAt)
- `sortOrder`: Sort direction (asc/desc, default: desc)

### Response Headers

```http
X-Total-Count: 150
X-Page-Count: 15
X-Current-Page: 1
X-Page-Size: 10
Link: <https://api.tli.com/api/MilestoneTemplate?page=2>; rel="next",
      <https://api.tli.com/api/MilestoneTemplate?page=15>; rel="last"
```

## Filtering and Searching

### Common Query Parameters

- `type`: Filter by template type
- `category`: Filter by category
- `isActive`: Filter by active status (true/false)
- `isDefault`: Filter by default status (true/false)
- `createdBy`: Filter by creator
- `createdAfter`: Filter by creation date (ISO 8601)
- `createdBefore`: Filter by creation date (ISO 8601)

### Search Examples

```bash
# Get active trip templates
GET /api/MilestoneTemplate?type=Trip&isActive=true

# Search templates by name
GET /api/MilestoneTemplate/search?searchTerm=express

# Get templates created in the last week
GET /api/MilestoneTemplate?createdAfter=2024-01-13T00:00:00Z

# Get paginated results
GET /api/MilestoneTemplate?page=2&pageSize=20&sortBy=name&sortOrder=asc
```

## Webhooks

### Supported Events

- `milestone_template.created`
- `milestone_template.updated`
- `milestone_template.deleted`
- `milestone_template.activated`
- `milestone_template.deactivated`
- `milestone_step.added`
- `milestone_step.removed`
- `role_mapping.created`
- `role_mapping.updated`

### Webhook Configuration

```bash
# Register webhook endpoint
POST /api/webhooks
{
  "url": "https://your-app.com/webhooks/milestone",
  "events": ["milestone_template.created", "milestone_template.updated"],
  "secret": "your-webhook-secret"
}
```

## SDK and Client Libraries

### Official SDKs

- **JavaScript/TypeScript**: `@tli/mobile-workflow-sdk`
- **Python**: `tli-mobile-workflow-python`
- **C#**: `TLI.MobileWorkflow.SDK`
- **Java**: `com.tli.mobileworkflow.sdk`

### Installation Examples

```bash
# JavaScript/Node.js
npm install @tli/mobile-workflow-sdk

# Python
pip install tli-mobile-workflow-python

# .NET
dotnet add package TLI.MobileWorkflow.SDK
```

## Testing and Development

### Postman Collection

Download the official Postman collection:
- [TLI Mobile Workflow API.postman_collection.json](./postman/TLI_Mobile_Workflow_API.postman_collection.json)

### Environment Variables

```json
{
  "base_url": "https://localhost:5001/api",
  "auth_token": "{{jwt_token}}",
  "api_version": "v1"
}
```

## Support and Resources

### Documentation Links

- **API Reference**: https://docs.tli.com/mobile-workflow/api
- **Developer Portal**: https://developer.tli.com
- **Status Page**: https://status.tli.com
- **Changelog**: https://docs.tli.com/mobile-workflow/changelog

### Support Channels

- **Email**: <EMAIL>
- **Slack**: #api-support (TLI Developer Community)
- **GitHub Issues**: https://github.com/tli/mobile-workflow-api/issues
- **Stack Overflow**: Tag questions with `tli-api`

### SLA and Uptime

- **Availability**: 99.9% uptime SLA
- **Response Time**: < 200ms for 95% of requests
- **Support Response**: < 4 hours for critical issues
- **Maintenance Windows**: Saturdays 2-4 AM UTC (announced 48h in advance)
