using MediatR;

namespace FinancialPayment.Application.IntegrationEvents;

public class TripStartedIntegrationEvent : INotification
{
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public Guid CarrierId { get; set; }
    public Guid DriverId { get; set; }
    public Guid VehicleId { get; set; }
    public DateTime StartedAt { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public DateTime EstimatedEndTime { get; set; }
    public string? Notes { get; set; }
}

public class TripCompletedIntegrationEvent : INotification
{
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public Guid CarrierId { get; set; }
    public Guid DriverId { get; set; }
    public Guid VehicleId { get; set; }
    public DateTime CompletedAt { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public DateTime ActualStartTime { get; set; }
    public DateTime ActualEndTime { get; set; }
    public decimal EstimatedDistanceKm { get; set; }
    public decimal ActualDistanceKm { get; set; }
    public string? CompletionNotes { get; set; }
    public bool ProofOfDeliveryUploaded { get; set; }
}

public class DocumentUploadedIntegrationEvent : INotification
{
    public Guid DocumentId { get; set; }
    public Guid OrderId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public Guid RelatedEntityId { get; set; }
    public string RelatedEntityType { get; set; } = string.Empty;
    public Guid UploadedBy { get; set; }
    public DateTime UploadedAt { get; set; }
    public long FileSize { get; set; }
    public string? Description { get; set; }
}
