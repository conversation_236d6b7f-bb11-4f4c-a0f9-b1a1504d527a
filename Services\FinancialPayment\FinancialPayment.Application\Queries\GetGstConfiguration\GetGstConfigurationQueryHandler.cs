using MediatR;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces.Repositories;

namespace FinancialPayment.Application.Queries.GetGstConfiguration;

public class GetGstConfigurationQueryHandler : IRequestHandler<GetGstConfigurationQuery, GstConfigurationDto?>
{
    private readonly IGstConfigurationRepository _gstConfigurationRepository;

    public GetGstConfigurationQueryHandler(IGstConfigurationRepository gstConfigurationRepository)
    {
        _gstConfigurationRepository = gstConfigurationRepository;
    }

    public async Task<GstConfigurationDto?> Handle(GetGstConfigurationQuery request, CancellationToken cancellationToken)
    {
        var gstConfiguration = await _gstConfigurationRepository.GetByIdAsync(request.Id);

        if (gstConfiguration == null)
            return null;

        return new GstConfigurationDto
        {
            Id = gstConfiguration.Id,
            Name = gstConfiguration.Name,
            Description = gstConfiguration.Description,
            ServiceCategory = gstConfiguration.ServiceCategory,
            Jurisdiction = new TaxJurisdictionDto
            {
                Country = gstConfiguration.Jurisdiction.Country,
                State = gstConfiguration.Jurisdiction.State,
                City = gstConfiguration.Jurisdiction.City,
                Type = gstConfiguration.Jurisdiction.Type
            },
            GstRate = gstConfiguration.GstRate,
            TaxRate = new TaxRateDto
            {
                Rate = gstConfiguration.TaxRate.Rate,
                CalculationMethod = gstConfiguration.TaxRate.CalculationMethod,
                EffectiveFrom = gstConfiguration.TaxRate.EffectiveFrom,
                EffectiveTo = gstConfiguration.TaxRate.EffectiveTo
            },
            Status = gstConfiguration.Status,
            HsnCode = gstConfiguration.HsnCode,
            MinimumAmount = new MoneyDto
            {
                Amount = gstConfiguration.MinimumAmount.Amount,
                Currency = gstConfiguration.MinimumAmount.Currency
            },
            MaximumAmount = new MoneyDto
            {
                Amount = gstConfiguration.MaximumAmount.Amount,
                Currency = gstConfiguration.MaximumAmount.Currency
            },
            IsReverseChargeApplicable = gstConfiguration.IsReverseChargeApplicable,
            ReverseChargeConditions = gstConfiguration.ReverseChargeConditions,
            CreatedBy = gstConfiguration.CreatedBy,
            ModifiedBy = gstConfiguration.ModifiedBy,
            ModifiedAt = gstConfiguration.ModifiedAt,
            CreatedAt = gstConfiguration.CreatedAt,
            UpdatedAt = gstConfiguration.UpdatedAt ?? DateTime.UtcNow,
            History = gstConfiguration.History.Select(h => new GstConfigurationHistoryDto
            {
                Id = h.Id,
                GstConfigurationId = h.GstConfigurationId,
                Action = h.Action,
                Details = h.Details,
                ModifiedBy = h.ModifiedBy,
                ModifiedAt = h.ModifiedAt
            }).ToList()
        };
    }
}
