using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.MobileApp;

public class CreateMobileAppCommand : IRequest<MobileAppDto>
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string PackageId { get; set; } = string.Empty;
    public DateTime ReleaseDate { get; set; }
    public string MinimumOSVersion { get; set; } = string.Empty;
    public Dictionary<string, object> Features { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool SupportsOffline { get; set; }
    public string DownloadUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Checksum { get; set; } = string.Empty;
}
