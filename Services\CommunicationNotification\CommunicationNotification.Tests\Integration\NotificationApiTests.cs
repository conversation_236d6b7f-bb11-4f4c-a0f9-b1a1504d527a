using CommunicationNotification.Tests.Infrastructure;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Integration;

/// <summary>
/// Integration tests for notification API endpoints
/// </summary>
public class NotificationApiTests : IntegrationTestBase
{
    public NotificationApiTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task SendNotification_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Test notification message",
            MessageType = "SystemNotification",
            Priority = "Normal",
            PreferredChannel = "Push",
            PreferredLanguage = "English",
            Subject = "Test Notification",
            RequireDeliveryConfirmation = true,
            Tags = new[] { "test", "integration" }
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        WriteOutput($"Response: {content}");
        
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("notificationId").GetString().Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task SendNotification_WithInvalidUserId_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = Guid.Empty,
            Content = "Test notification message",
            MessageType = "SystemNotification",
            Priority = "Normal"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendBulkNotification_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient();

        var request = new
        {
            UserIds = new[] { TestConstants.TestCustomerId, TestConstants.TestDriverId },
            Content = "Bulk notification message",
            MessageType = "SystemNotification",
            Priority = "High",
            PreferredChannel = "Push",
            PreferredLanguage = "English",
            Subject = "Bulk Test Notification",
            Tags = new[] { "bulk", "test" }
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send-bulk", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("totalNotifications").GetInt32().Should().Be(2);
    }

    [Fact]
    public async Task SendTemplateNotification_WithValidTemplate_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            TemplateId = "test_notification",
            Parameters = new Dictionary<string, object>
            {
                ["message"] = "This is a test message from template"
            },
            Priority = "Normal",
            PreferredChannel = "Push",
            PreferredLanguage = "English"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send-template", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task SendEmergencyNotification_WithValidRequest_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient();

        var request = new
        {
            UserIds = new[] { TestConstants.TestDriverId },
            Content = "Emergency: Vehicle breakdown reported",
            Subject = "Emergency Alert",
            EscalationLevel = 1,
            RequireAcknowledgment = true,
            AcknowledgmentTimeout = 300,
            Metadata = new Dictionary<string, string>
            {
                ["emergency_type"] = "breakdown",
                ["location"] = "Highway 123"
            }
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send-emergency", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task GetNotificationStatus_WithValidId_ShouldReturnStatus()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        // First send a notification
        var sendRequest = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Test notification for status check",
            MessageType = "SystemNotification",
            Priority = "Normal"
        };

        var sendResponse = await client.PostAsJsonAsync("/api/notifications/send", sendRequest);
        var sendContent = await sendResponse.Content.ReadAsStringAsync();
        var sendResult = JsonSerializer.Deserialize<JsonElement>(sendContent);
        var notificationId = sendResult.GetProperty("data").GetProperty("notificationId").GetString();

        // Act
        var response = await client.GetAsync($"/api/notifications/{notificationId}/status");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
        result.GetProperty("data").GetProperty("notificationId").GetString().Should().Be(notificationId);
    }

    [Fact]
    public async Task GetNotificationStatus_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();
        var invalidId = Guid.NewGuid();

        // Act
        var response = await client.GetAsync($"/api/notifications/{invalidId}/status");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SendNotification_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Test notification message",
            MessageType = "SystemNotification"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task SendBulkNotification_WithoutAdminRole_ShouldReturnForbidden()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserIds = new[] { TestConstants.TestCustomerId, TestConstants.TestDriverId },
            Content = "Bulk notification message",
            MessageType = "SystemNotification"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send-bulk", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("   ")]
    public async Task SendNotification_WithInvalidContent_ShouldReturnBadRequest(string content)
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = content,
            MessageType = "SystemNotification",
            Priority = "Normal"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendNotification_WithLongContent_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var longContent = new string('A', 5000); // Exceeds 4000 character limit

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = longContent,
            MessageType = "SystemNotification",
            Priority = "Normal"
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendNotification_WithScheduledTime_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var scheduledTime = DateTime.UtcNow.AddMinutes(30);

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Scheduled notification message",
            MessageType = "SystemNotification",
            Priority = "Normal",
            ScheduledAt = scheduledTime.ToString("O")
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task SendNotification_WithPastScheduledTime_ShouldReturnBadRequest()
    {
        // Arrange
        using var client = WebApplicationFactory.CreateCustomerClient();

        var pastTime = DateTime.UtcNow.AddMinutes(-30);

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Past scheduled notification",
            MessageType = "SystemNotification",
            Priority = "Normal",
            ScheduledAt = pastTime.ToString("O")
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task SendNotification_WithCustomHeaders_ShouldReturnSuccess()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Notification with custom headers",
            MessageType = "SystemNotification",
            Priority = "Normal",
            CustomHeaders = new Dictionary<string, string>
            {
                ["X-Custom-Header"] = "test-value",
                ["X-Source"] = "integration-test"
            }
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("success").GetBoolean().Should().BeTrue();
    }

    [Fact]
    public async Task SendNotification_ConcurrentRequests_ShouldHandleAllRequests()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var tasks = new List<Task<HttpResponseMessage>>();
        const int concurrentRequests = 10;

        // Act
        for (int i = 0; i < concurrentRequests; i++)
        {
            var request = new
            {
                UserId = TestConstants.TestCustomerId,
                Content = $"Concurrent notification {i}",
                MessageType = "SystemNotification",
                Priority = "Normal"
            };

            tasks.Add(client.PostAsJsonAsync("/api/notifications/send", request));
        }

        var responses = await Task.WhenAll(tasks);

        // Assert
        foreach (var response in responses)
        {
            response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        WriteOutput($"Successfully processed {concurrentRequests} concurrent notification requests");
    }
}
