using OrderManagement.Application.DTOs;
using OrderManagement.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace OrderManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company rating display and analytics
/// </summary>
[ApiController]
[Route("api/transport-company/ratings")]
[Authorize]
public class TransportCompanyRatingController : ControllerBase
{
    private readonly ITransportCompanyRatingService _ratingService;
    private readonly ILogger<TransportCompanyRatingController> _logger;

    public TransportCompanyRatingController(
        ITransportCompanyRatingService ratingService,
        ILogger<TransportCompanyRatingController> logger)
    {
        _ratingService = ratingService;
        _logger = logger;
    }

    /// <summary>
    /// Get rating display for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}")]
    [ProducesResponseType(typeof(TransportCompanyRatingDisplayDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TransportCompanyRatingDisplayDto>> GetRatingDisplay(
        Guid transportCompanyId)
    {
        try
        {
            _logger.LogInformation("Getting rating display for Transport Company {TransportCompanyId}", transportCompanyId);

            var ratingDisplay = await _ratingService.GetTransportCompanyRatingDisplayAsync(transportCompanyId);

            return Ok(ratingDisplay);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating display for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get rating displays for multiple transport companies
    /// </summary>
    [HttpPost("multiple")]
    [ProducesResponseType(typeof(List<TransportCompanyRatingDisplayDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<TransportCompanyRatingDisplayDto>>> GetMultipleRatingDisplays(
        [FromBody] List<Guid> transportCompanyIds)
    {
        try
        {
            if (!transportCompanyIds.Any() || transportCompanyIds.Count > 50)
            {
                return BadRequest("Please provide between 1 and 50 transport company IDs");
            }

            _logger.LogInformation("Getting rating displays for {Count} transport companies", transportCompanyIds.Count);

            var ratingDisplays = await _ratingService.GetMultipleTransportCompanyRatingsAsync(transportCompanyIds);

            return Ok(ratingDisplays);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple rating displays");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get rating trends for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/trends")]
    [ProducesResponseType(typeof(TransportCompanyRatingTrendsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TransportCompanyRatingTrendsDto>> GetRatingTrends(
        Guid transportCompanyId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddMonths(-12);
            var to = toDate ?? DateTime.UtcNow;

            if (from >= to)
            {
                return BadRequest("From date must be before to date");
            }

            if ((to - from).TotalDays > 730) // 2 years max
            {
                return BadRequest("Date range cannot exceed 2 years");
            }

            _logger.LogInformation("Getting rating trends for Transport Company {TransportCompanyId} from {FromDate} to {ToDate}",
                transportCompanyId, from, to);

            var trends = await _ratingService.GetRatingTrendsAsync(transportCompanyId, from, to);

            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating trends for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Compare ratings of multiple transport companies
    /// </summary>
    [HttpPost("compare")]
    [ProducesResponseType(typeof(TransportCompanyRatingComparisonDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TransportCompanyRatingComparisonDto>> CompareRatings(
        [FromBody] GetRatingComparisonRequest request)
    {
        try
        {
            if (!request.TransportCompanyIds.Any() || request.TransportCompanyIds.Count > 20)
            {
                return BadRequest("Please provide between 1 and 20 transport company IDs for comparison");
            }

            _logger.LogInformation("Comparing ratings for {Count} transport companies", request.TransportCompanyIds.Count);

            var comparison = await _ratingService.GetRatingComparisonAsync(request.TransportCompanyIds);

            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing transport company ratings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get performance metrics for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}/performance")]
    [ProducesResponseType(typeof(TransportCompanyPerformanceMetricsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TransportCompanyPerformanceMetricsDto>> GetPerformanceMetrics(
        Guid transportCompanyId)
    {
        try
        {
            _logger.LogInformation("Getting performance metrics for Transport Company {TransportCompanyId}", transportCompanyId);

            var metrics = await _ratingService.GetPerformanceMetricsAsync(transportCompanyId);

            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Refresh rating cache for a transport company
    /// </summary>
    [HttpPost("{transportCompanyId}/refresh-cache")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<IActionResult> RefreshRatingCache(Guid transportCompanyId)
    {
        try
        {
            if (!await CanManageRatings(transportCompanyId))
            {
                return Forbid("You don't have permission to refresh ratings for this transport company");
            }

            _logger.LogInformation("Refreshing rating cache for Transport Company {TransportCompanyId}", transportCompanyId);

            await _ratingService.RefreshRatingCacheAsync(transportCompanyId);

            return Ok(new { message = "Rating cache refreshed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing rating cache for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get rating summary for dashboard
    /// </summary>
    [HttpGet("{transportCompanyId}/summary")]
    [ProducesResponseType(typeof(RatingSummaryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<RatingSummaryDto>> GetRatingSummary(Guid transportCompanyId)
    {
        try
        {
            _logger.LogInformation("Getting rating summary for Transport Company {TransportCompanyId}", transportCompanyId);

            var ratingDisplay = await _ratingService.GetTransportCompanyRatingDisplayAsync(transportCompanyId);

            var summary = new RatingSummaryDto
            {
                TransportCompanyId = transportCompanyId,
                OverallRating = ratingDisplay.OverallRating,
                TotalReviews = ratingDisplay.TotalReviews,
                RatingTrend = ratingDisplay.RatingTrend,
                TopBadges = ratingDisplay.Badges.Take(3).ToList(),
                LastUpdated = ratingDisplay.LastUpdated,
                OnTimeRate = ratingDisplay.OnTimeDeliveryRate,
                SatisfactionScore = ratingDisplay.CustomerSatisfactionScore,
                RecentTrips = ratingDisplay.TotalTrips,
                RatingColor = GetRatingColor(ratingDisplay.OverallRating),
                TrendIcon = GetTrendIcon(ratingDisplay.RatingTrend),
                ShowAlert = ratingDisplay.OverallRating < 3.0m,
                AlertMessage = ratingDisplay.OverallRating < 3.0m ? "Rating below average - attention needed" : null
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating summary for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get top rated transport companies
    /// </summary>
    [HttpGet("top-rated")]
    [ProducesResponseType(typeof(List<TransportCompanyRatingDisplayDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<TransportCompanyRatingDisplayDto>>> GetTopRatedCompanies(
        [FromQuery] int limit = 10,
        [FromQuery] decimal minRating = 4.0m,
        [FromQuery] int minReviews = 5)
    {
        try
        {
            if (limit > 50)
            {
                return BadRequest("Limit cannot exceed 50");
            }

            _logger.LogInformation("Getting top {Limit} rated transport companies with min rating {MinRating} and min reviews {MinReviews}",
                limit, minRating, minReviews);

            // This would be implemented as a specific query
            var topRated = new List<TransportCompanyRatingDisplayDto>();

            return Ok(topRated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top rated transport companies");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get rating statistics
    /// </summary>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(RatingStatisticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<RatingStatisticsDto>> GetRatingStatistics()
    {
        try
        {
            _logger.LogInformation("Getting rating statistics");

            // This would be implemented as a specific query
            var statistics = new RatingStatisticsDto
            {
                TotalCompaniesWithRatings = 150,
                AverageRatingAcrossAllCompanies = 4.2m,
                TotalReviews = 5420,
                CompaniesWithHighRatings = 89, // 4+ stars
                CompaniesWithLowRatings = 12, // 2- stars
                AverageOnTimeDeliveryRate = 87.5m,
                AverageCustomerSatisfactionScore = 82.3m,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rating statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageRatings(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }

    private string GetRatingColor(decimal rating)
    {
        return rating switch
        {
            >= 4.0m => "green",
            >= 3.0m => "yellow",
            >= 2.0m => "orange",
            _ => "red"
        };
    }

    private string GetTrendIcon(string trend)
    {
        return trend switch
        {
            "Improving" => "trending-up",
            "Declining" => "trending-down",
            "Stable" => "minus",
            _ => "help-circle"
        };
    }
}

/// <summary>
/// DTO for rating statistics
/// </summary>
public class RatingStatisticsDto
{
    public int TotalCompaniesWithRatings { get; set; }
    public decimal AverageRatingAcrossAllCompanies { get; set; }
    public int TotalReviews { get; set; }
    public int CompaniesWithHighRatings { get; set; }
    public int CompaniesWithLowRatings { get; set; }
    public decimal AverageOnTimeDeliveryRate { get; set; }
    public decimal AverageCustomerSatisfactionScore { get; set; }
    public DateTime GeneratedAt { get; set; }
}
