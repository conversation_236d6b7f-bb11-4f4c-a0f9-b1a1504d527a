using MediatR;

namespace TripManagement.Application.Queries.GetDriverLocationStatus;

public class GetDriverLocationStatusQuery : IRequest<GetDriverLocationStatusResponse>
{
    public Guid DriverId { get; set; }
    public bool IncludeLocationHistory { get; set; } = false;
    public int HistoryHours { get; set; } = 24;
}

public class GetDriverLocationStatusResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DriverLocationStatusDto? LocationStatus { get; set; }
}

public class DriverLocationStatusDto
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public bool LocationSharingEnabled { get; set; }
    public bool ManualToggleAllowed { get; set; }
    public int LocationUpdateIntervalSeconds { get; set; }
    public bool ShowGpsTimestamp { get; set; }
    public bool HighAccuracyMode { get; set; }
    public bool BackgroundLocationEnabled { get; set; }
    public bool GeofenceAlertsEnabled { get; set; }
    public double LocationAccuracyThresholdMeters { get; set; }
    
    // Current location data
    public double? CurrentLatitude { get; set; }
    public double? CurrentLongitude { get; set; }
    public double? CurrentAccuracy { get; set; }
    public DateTime? LastGpsTimestamp { get; set; }
    public string? LastKnownLocation { get; set; }
    public DateTime LastLocationUpdate { get; set; }
    
    // Status indicators
    public bool IsLocationStale { get; set; }
    public bool IsLocationAccurate { get; set; }
    public TimeSpan TimeSinceLastUpdate { get; set; }
    public string LocationStatus { get; set; } = string.Empty; // Active, Stale, Disabled, Unknown
    
    // Trip context
    public Guid? CurrentTripId { get; set; }
    public string? CurrentTripNumber { get; set; }
    public bool IsOnTrip { get; set; }
    
    // Location history (if requested)
    public List<LocationHistoryDto> LocationHistory { get; set; } = new();
    
    // Additional settings
    public Dictionary<string, object> AdditionalSettings { get; set; } = new();
    public DateTime PreferencesUpdatedAt { get; set; }
}

public class LocationHistoryDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public DateTime GpsTimestamp { get; set; }
    public string Source { get; set; } = string.Empty;
    public string? LocationDescription { get; set; }
    public bool IsSignificantUpdate { get; set; }
    public double? DistanceFromPreviousMeters { get; set; }
}
