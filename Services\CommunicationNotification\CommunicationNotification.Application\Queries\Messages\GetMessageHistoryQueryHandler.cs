using CommunicationNotification.Application.Common;
using Shared.Domain.Common;
using CommunicationNotification.Application.Interfaces;
using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Queries.Messages;

/// <summary>
/// Handler for getting message history
/// </summary>
public class GetMessageHistoryQueryHandler : IQueryHandler<GetMessageHistoryQuery, PagedQueryResult<MessageHistoryItem>>
{
    private readonly IMessageRepository _messageRepository;
    private readonly ILogger<GetMessageHistoryQueryHandler> _logger;

    public GetMessageHistoryQueryHandler(
        IMessageRepository messageRepository,
        ILogger<GetMessageHistoryQueryHandler> logger)
    {
        _messageRepository = messageRepository;
        _logger = logger;
    }

    public async Task<PagedQueryResult<MessageHistoryItem>> Handle(
        GetMessageHistoryQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing message history query {QueryId} for user {UserId}",
                request.QueryId, request.UserId);

            // Validate query
            var validationResult = await ValidateQueryAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return PagedQueryResult<MessageHistoryItem>.Failure(
                    string.Join(", ", validationResult.ValidationErrors), request.QueryId);
            }

            // Build filter criteria
            var filter = new MessageFilter
            {
                UserId = request.UserId,
                ConversationId = request.ConversationId,
                MessageType = request.MessageType,
                Channel = request.Channel,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                SearchTerm = request.SearchTerm,
                IncludeDeleted = request.IncludeDeleted
            };

            // Get total count
            var totalCount = await _messageRepository.GetMessageCountAsync(filter, cancellationToken);

            // Get messages
            var messages = await _messageRepository.GetMessagesAsync(
                filter,
                request.Parameters.Skip,
                request.Parameters.Take,
                request.Parameters.Sorting.SortBy ?? "SentAt",
                request.Parameters.Sorting.SortDirection == SortDirection.Descending,
                cancellationToken);

            // Convert to result items
            var items = messages.Select(m => new MessageHistoryItem
            {
                MessageId = m.Id,
                ConversationId = m.ConversationId,
                Content = m.Content,
                Subject = m.Subject,
                MessageType = m.MessageType,
                Channel = m.Channel,
                Priority = m.Priority,
                Language = m.Language,
                SentAt = m.SentAt,
                DeliveredAt = m.DeliveredAt,
                ReadAt = m.ReadAt,
                Status = m.Status,
                SenderId = m.SenderId,
                SenderName = m.SenderName,
                Tags = m.Tags,
                Metadata = m.Metadata
            }).ToList();

            _logger.LogDebug("Message history query {QueryId} completed. Found {Count} messages",
                request.QueryId, items.Count);

            return PagedQueryResult<MessageHistoryItem>.Success(
                items,
                totalCount,
                request.Parameters.PageNumber,
                request.Parameters.PageSize,
                request.QueryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process message history query {QueryId}", request.QueryId);
            return PagedQueryResult<MessageHistoryItem>.Failure(ex.Message, request.QueryId);
        }
    }

    private async Task<QueryValidationResult> ValidateQueryAsync(
        GetMessageHistoryQuery query,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (query.UserId == Guid.Empty)
            errors.Add("UserId is required");

        if (query.FromDate.HasValue && query.ToDate.HasValue && query.FromDate > query.ToDate)
            errors.Add("FromDate cannot be greater than ToDate");

        if (query.Parameters.PageSize > 1000)
            errors.Add("PageSize cannot exceed 1000");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }
}

/// <summary>
/// Handler for getting conversation history
/// </summary>
public class GetConversationHistoryQueryHandler : IQueryHandler<GetConversationHistoryQuery, PagedQueryResult<ConversationMessage>>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<GetConversationHistoryQueryHandler> _logger;

    public GetConversationHistoryQueryHandler(
        IConversationRepository conversationRepository,
        ILogger<GetConversationHistoryQueryHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    public async Task<PagedQueryResult<ConversationMessage>> Handle(
        GetConversationHistoryQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing conversation history query {QueryId} for conversation {ConversationId}",
                request.QueryId, request.ConversationId);

            // Validate query
            var validationResult = await ValidateQueryAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return PagedQueryResult<ConversationMessage>.Failure(
                    string.Join(", ", validationResult.ValidationErrors), request.QueryId);
            }

            // Check if user has access to conversation
            if (request.UserId.HasValue)
            {
                var hasAccess = await _conversationRepository.HasUserAccessAsync(
                    request.ConversationId, request.UserId.Value, cancellationToken);
                
                if (!hasAccess)
                {
                    return PagedQueryResult<ConversationMessage>.Failure(
                        "User does not have access to this conversation", request.QueryId);
                }
            }

            // Build filter criteria
            var filter = new ConversationMessageFilter
            {
                ConversationId = request.ConversationId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                IncludeSystemMessages = request.IncludeSystemMessages,
                IncludeDeleted = request.IncludeDeleted
            };

            // Get total count
            var totalCount = await _conversationRepository.GetMessageCountAsync(filter, cancellationToken);

            // Get messages
            var messages = await _conversationRepository.GetMessagesAsync(
                filter,
                request.Parameters.Skip,
                request.Parameters.Take,
                request.Parameters.Sorting.SortBy ?? "SentAt",
                request.Parameters.Sorting.SortDirection == SortDirection.Descending,
                cancellationToken);

            // Convert to result items
            var items = messages.Select(m => new ConversationMessage
            {
                MessageId = m.Id,
                ConversationId = m.ConversationId,
                Content = m.Content,
                MessageType = m.MessageType,
                SentAt = m.SentAt,
                SenderId = m.SenderId,
                SenderName = m.SenderName,
                SenderRole = m.SenderRole,
                IsSystemMessage = m.IsSystemMessage,
                IsDeleted = m.IsDeleted,
                EditedAt = m.EditedAt,
                Attachments = m.Attachments,
                Metadata = m.Metadata
            }).ToList();

            _logger.LogDebug("Conversation history query {QueryId} completed. Found {Count} messages",
                request.QueryId, items.Count);

            return PagedQueryResult<ConversationMessage>.Success(
                items,
                totalCount,
                request.Parameters.PageNumber,
                request.Parameters.PageSize,
                request.QueryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process conversation history query {QueryId}", request.QueryId);
            return PagedQueryResult<ConversationMessage>.Failure(ex.Message, request.QueryId);
        }
    }

    private async Task<QueryValidationResult> ValidateQueryAsync(
        GetConversationHistoryQuery query,
        CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        if (query.ConversationId == Guid.Empty)
            errors.Add("ConversationId is required");

        if (query.FromDate.HasValue && query.ToDate.HasValue && query.FromDate > query.ToDate)
            errors.Add("FromDate cannot be greater than ToDate");

        if (query.Parameters.PageSize > 1000)
            errors.Add("PageSize cannot exceed 1000");

        return errors.Any() ? QueryValidationResult.Invalid(errors) : QueryValidationResult.Valid();
    }
}

/// <summary>
/// Handler for getting message delivery status
/// </summary>
public class GetMessageDeliveryStatusQueryHandler : IQueryHandler<GetMessageDeliveryStatusQuery, QueryResult<MessageDeliveryStatus>>
{
    private readonly IMessageRepository _messageRepository;
    private readonly ILogger<GetMessageDeliveryStatusQueryHandler> _logger;

    public GetMessageDeliveryStatusQueryHandler(
        IMessageRepository messageRepository,
        ILogger<GetMessageDeliveryStatusQueryHandler> logger)
    {
        _messageRepository = messageRepository;
        _logger = logger;
    }

    public async Task<QueryResult<MessageDeliveryStatus>> Handle(
        GetMessageDeliveryStatusQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing message delivery status query {QueryId} for message {MessageId}",
                request.QueryId, request.MessageId);

            // Get message
            var message = await _messageRepository.GetByIdAsync(request.MessageId, cancellationToken);
            if (message == null)
            {
                return QueryResult<MessageDeliveryStatus>.Failure(
                    $"Message {request.MessageId} not found", request.QueryId);
            }

            // Get delivery attempts if requested
            var deliveryAttempts = new List<DeliveryAttempt>();
            if (request.IncludeDeliveryAttempts)
            {
                deliveryAttempts = await _messageRepository.GetDeliveryAttemptsAsync(
                    request.MessageId, cancellationToken);
            }

            // Get channel details if requested
            var channelDetails = new Dictionary<string, object>();
            if (request.IncludeChannelDetails)
            {
                channelDetails = await _messageRepository.GetChannelDetailsAsync(
                    request.MessageId, cancellationToken);
            }

            var deliveryStatus = new MessageDeliveryStatus
            {
                MessageId = message.Id,
                Status = message.Status,
                Channel = message.Channel,
                SentAt = message.SentAt,
                DeliveredAt = message.DeliveredAt,
                ReadAt = message.ReadAt,
                ExternalId = message.ExternalId,
                DeliveryAttempts = deliveryAttempts,
                ChannelDetails = channelDetails,
                ErrorMessage = message.ErrorMessage
            };

            _logger.LogDebug("Message delivery status query {QueryId} completed for message {MessageId}",
                request.QueryId, request.MessageId);

            return QueryResult<MessageDeliveryStatus>.Success(deliveryStatus, request.QueryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process message delivery status query {QueryId}", request.QueryId);
            return QueryResult<MessageDeliveryStatus>.Failure(ex.Message, request.QueryId);
        }
    }
}

// Repository interfaces (these would be implemented in the Infrastructure layer)
public interface IMessageRepository
{
    Task<int> GetMessageCountAsync(MessageFilter filter, CancellationToken cancellationToken = default);
    Task<List<Message>> GetMessagesAsync(MessageFilter filter, int skip, int take, string sortBy, bool descending, CancellationToken cancellationToken = default);
    Task<Message?> GetByIdAsync(Guid messageId, CancellationToken cancellationToken = default);
    Task<List<DeliveryAttempt>> GetDeliveryAttemptsAsync(Guid messageId, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GetChannelDetailsAsync(Guid messageId, CancellationToken cancellationToken = default);
}

public interface IConversationRepository
{
    Task<bool> HasUserAccessAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
    Task<int> GetMessageCountAsync(ConversationMessageFilter filter, CancellationToken cancellationToken = default);
    Task<List<ConversationMessageEntity>> GetMessagesAsync(ConversationMessageFilter filter, int skip, int take, string sortBy, bool descending, CancellationToken cancellationToken = default);
}

// Filter classes
public class MessageFilter
{
    public Guid UserId { get; set; }
    public Guid? ConversationId { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public bool IncludeDeleted { get; set; }
}

public class ConversationMessageFilter
{
    public Guid ConversationId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool IncludeSystemMessages { get; set; }
    public bool IncludeDeleted { get; set; }
}

// Entity classes (these would be defined in the Domain layer)
public class Message
{
    public Guid Id { get; set; }
    public Guid? ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public MessageType MessageType { get; set; }
    public NotificationChannel Channel { get; set; }
    public Priority Priority { get; set; }
    public Language Language { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public MessageStatus Status { get; set; }
    public Guid SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? ExternalId { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ConversationMessageEntity
{
    public Guid Id { get; set; }
    public Guid ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public DateTime SentAt { get; set; }
    public Guid SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public string? SenderRole { get; set; }
    public bool IsSystemMessage { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? EditedAt { get; set; }
    public List<MessageAttachment> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

