# Communication & Notification Service - Test Suite

This directory contains comprehensive tests for the TLI Communication & Notification Service, including unit tests, integration tests, and performance tests.

## 🎯 Test Structure

### Test Categories

#### **Unit Tests** (`/Unit/`)
- **Domain Tests**: Entity validation, value object behavior, business logic
- **Application Tests**: CQRS handlers, validators, service logic
- **Infrastructure Tests**: Repository patterns, external service integrations

#### **Integration Tests** (`/Integration/`)
- **API Tests**: End-to-end API endpoint testing
- **Database Tests**: Entity Framework operations, migrations
- **External Service Tests**: Provider integrations, webhooks

#### **Performance Tests** (`/Performance/`)
- **Load Tests**: High-throughput scenarios using NBomber
- **Stress Tests**: Resource utilization under extreme load
- **Endurance Tests**: Long-running stability tests

### Test Infrastructure (`/Infrastructure/`)
- **TestBase**: Base classes for different test types
- **TestWebApplicationFactory**: Integration test setup
- **Mock Providers**: Simulated external services
- **Test Data Factories**: Consistent test data generation

## 🚀 Running Tests

### Prerequisites
- .NET 8.0 SDK
- TimescaleDB (for integration tests)
- Docker (optional, for containerized testing)

### Quick Start

```powershell
# Run all tests
.\Scripts\run-tests.ps1

# Run specific test categories
.\Scripts\run-tests.ps1 -Unit
.\Scripts\run-tests.ps1 -Integration
.\Scripts\run-tests.ps1 -Performance

# Run with code coverage
.\Scripts\run-tests.ps1 -Coverage -GenerateReport -OpenReport

# Run with custom filter
.\Scripts\run-tests.ps1 -Filter "FullyQualifiedName~Notification"
```

### Manual Test Execution

```bash
# Restore packages
dotnet restore

# Build test project
dotnet build --configuration Debug

# Run all tests
dotnet test --configuration Debug

# Run specific test class
dotnet test --filter "FullyQualifiedName~NotificationApiTests"

# Run with coverage
dotnet test --collect:"XPlat Code Coverage" --settings coverlet.runsettings
```

## 📊 Test Configuration

### Test Settings (`appsettings.test.json`)

```json
{
  "Database": {
    "UseInMemoryDatabase": true,
    "UseSqliteDatabase": false,
    "EnableSensitiveDataLogging": true
  },
  "Testing": {
    "EnableMockProviders": true,
    "EnableTestData": true,
    "EnablePerformanceTesting": false,
    "TestTimeout": 30000
  },
  "MockProviders": {
    "Sms": {
      "SimulateDelay": true,
      "DelayMs": 100,
      "FailureRate": 0.1
    }
  }
}
```

### Coverage Configuration (`coverlet.runsettings`)

- **Formats**: Cobertura XML, HTML reports
- **Exclusions**: Test assemblies, migrations, generated code
- **Thresholds**: Minimum coverage requirements

## 🧪 Test Types and Examples

### Unit Tests

```csharp
[Fact]
public void Notification_Creation_ShouldSetPropertiesCorrectly()
{
    // Arrange
    var userId = Guid.NewGuid();
    var content = new MessageContent { /* ... */ };

    // Act
    var notification = new Notification { /* ... */ };

    // Assert
    notification.UserId.Should().Be(userId);
    notification.Content.Should().NotBeNull();
}
```

### Integration Tests

```csharp
[Fact]
public async Task SendNotification_WithValidRequest_ShouldReturnSuccess()
{
    // Arrange
    await WebApplicationFactory.SeedTestDataAsync();
    using var client = WebApplicationFactory.CreateCustomerClient();
    var request = new { /* ... */ };

    // Act
    var response = await client.PostAsJsonAsync("/api/notifications/send", request);

    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.OK);
}
```

### Performance Tests

```csharp
[Fact]
public async Task SendNotification_ConcurrentRequests_ShouldMaintainPerformance()
{
    // Arrange
    const int concurrentRequests = 50;
    var tasks = new List<Task<HttpResponseMessage>>();

    // Act
    for (int i = 0; i < concurrentRequests; i++)
    {
        tasks.Add(client.PostAsJsonAsync("/api/notifications/send", request));
    }
    var responses = await Task.WhenAll(tasks);

    // Assert
    responses.Should().OnlyContain(r => r.IsSuccessStatusCode);
}
```

## 🔧 Mock Providers

### SMS Provider Mock
- Simulates delivery delays and failures
- Configurable failure rates
- Delivery receipt simulation

### Email Provider Mock
- HTML and text content support
- Attachment handling simulation
- Bounce and spam filtering simulation

### Push Notification Mock
- Device token validation
- Bulk notification support
- Platform-specific behavior simulation

### WhatsApp Provider Mock
- Template message support
- Media message simulation
- Business API compliance

## 📈 Performance Testing

### Load Testing with NBomber

```csharp
var scenario = Scenario.Create("notification_load_test", async context =>
{
    var response = await httpClient.PostAsJsonAsync("/api/notifications/send", request);
    return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
})
.WithLoadSimulations(
    Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))
);
```

### Performance Metrics
- **Response Time**: Average, 95th percentile, maximum
- **Throughput**: Requests per second
- **Resource Usage**: Memory, CPU, database connections
- **Error Rate**: Failed requests percentage

## 🎯 Test Data Management

### Test Constants
```csharp
public static class TestConstants
{
    public const string TestUserId = "11111111-1111-1111-1111-111111111111";
    public const string TestDriverId = "22222222-2222-2222-2222-222222222222";
    public const string TestCustomerId = "33333333-3333-3333-3333-333333333333";
}
```

### Data Factories
```csharp
public static class TestDataFactory
{
    public static string RandomEmail() => $"test.{RandomString(8)}@example.com";
    public static string RandomPhoneNumber() => $"+91{Random.Next(7000000000, 9999999999)}";
}
```

## 🔍 Debugging Tests

### Visual Studio
1. Set breakpoints in test methods
2. Right-click test → Debug Test(s)
3. Use Test Explorer for test management

### VS Code
1. Install C# extension
2. Use .NET Test Explorer
3. Set breakpoints and run with debugger

### Command Line
```bash
# Run specific test with detailed output
dotnet test --filter "TestMethodName" --verbosity detailed

# Run tests in debug mode
dotnet test --configuration Debug --verbosity diagnostic
```

## 📋 Test Checklist

### Before Committing
- [ ] All unit tests pass
- [ ] Integration tests pass with real database
- [ ] Code coverage meets minimum threshold (80%)
- [ ] Performance tests meet SLA requirements
- [ ] No test warnings or skipped tests

### CI/CD Pipeline
- [ ] Tests run in isolated environment
- [ ] Database migrations applied successfully
- [ ] External service mocks configured
- [ ] Test results published
- [ ] Coverage reports generated

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check TimescaleDB is running
docker ps | grep timescale

# Reset test database
dotnet ef database drop --force --context CommunicationDbContext
dotnet ef database update --context CommunicationDbContext
```

#### Test Timeouts
- Increase timeout in `appsettings.test.json`
- Check for deadlocks in database operations
- Verify mock provider response times

#### Memory Issues
- Run tests with garbage collection: `dotnet test --collect:"XPlat Code Coverage"`
- Check for memory leaks in long-running tests
- Monitor resource usage during performance tests

### Getting Help
- Check test output logs for detailed error messages
- Review test configuration settings
- Consult team documentation for specific test scenarios
- Use debugging tools for complex test failures

## 📚 Additional Resources

- [xUnit Documentation](https://xunit.net/)
- [FluentAssertions Documentation](https://fluentassertions.com/)
- [NBomber Documentation](https://nbomber.com/)
- [Entity Framework Testing](https://docs.microsoft.com/en-us/ef/core/testing/)
- [ASP.NET Core Integration Testing](https://docs.microsoft.com/en-us/aspnet/core/test/integration-tests)
