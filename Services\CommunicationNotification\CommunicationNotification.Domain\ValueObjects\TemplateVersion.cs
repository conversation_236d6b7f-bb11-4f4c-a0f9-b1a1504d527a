using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

public class TemplateVersion : ValueObject
{
    public int Major { get; private set; }
    public int Minor { get; private set; }
    public int Patch { get; private set; }
    public string? Label { get; private set; }

    private TemplateVersion() { }

    public TemplateVersion(int major, int minor, int patch, string? label = null)
    {
        Major = major;
        Minor = minor;
        Patch = patch;
        Label = label;
    }

    public override string ToString() => Label != null ? $"{Major}.{Minor}.{Patch}-{Label}" : $"{Major}.{Minor}.{Patch}";

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Major;
        yield return Minor;
        yield return Patch;
        yield return Label ?? string.Empty;
    }
}