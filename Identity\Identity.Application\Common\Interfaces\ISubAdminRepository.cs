using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface ISubAdminRepository
    {
        Task<SubAdmin> GetByIdAsync(Guid id);
        Task<SubAdmin> GetByUserIdAsync(Guid userId);
        Task<SubAdmin> GetByEmailAsync(string email);
        Task<IEnumerable<SubAdmin>> GetAllAsync();
        Task<IEnumerable<SubAdmin>> GetActiveAsync();
        Task<IEnumerable<SubAdmin>> GetByDepartmentAsync(string department);
        Task<IEnumerable<SubAdmin>> GetBySupervisorAsync(Guid supervisorId);
        Task<IEnumerable<SubAdmin>> GetByStatusAsync(SubAdminStatus status);
        Task<(IEnumerable<SubAdmin> Items, int TotalCount)> GetPagedAsync(
            int pageNumber, 
            int pageSize, 
            string searchTerm = null,
            string department = null,
            SubAdminStatus? status = null);
        Task AddAsync(SubAdmin subAdmin);
        Task UpdateAsync(SubAdmin subAdmin);
        Task DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByEmailAsync(string email);
        Task<int> GetCountByDepartmentAsync(string department);
        Task<IEnumerable<SubAdmin>> GetRecentlyCreatedAsync(int days = 30);
        Task<IEnumerable<SubAdmin>> GetInactiveAsync(int days = 90);
    }
}
