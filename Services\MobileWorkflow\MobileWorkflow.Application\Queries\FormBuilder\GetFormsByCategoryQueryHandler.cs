using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class GetFormsByCategoryQueryHandler : IRequestHandler<GetFormsByCategoryQuery, List<FormDefinitionDto>>
    {
        private readonly IFormBuilderService _formBuilderService;
        private readonly ILogger<GetFormsByCategoryQueryHandler> _logger;

        public GetFormsByCategoryQueryHandler(
            IFormBuilderService formBuilderService,
            ILogger<GetFormsByCategoryQueryHandler> logger)
        {
            _formBuilderService = formBuilderService;
            _logger = logger;
        }

        public async Task<List<FormDefinitionDto>> Handle(GetFormsByCategoryQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting forms for category {Category}", request.Category);

                var result = await _formBuilderService.GetFormsByCategoryAsync(
                    request.Category,
                    cancellationToken);

                _logger.LogInformation("Successfully retrieved {Count} forms for category {Category}",
                    result.Count, request.Category);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting forms for category {Category}", request.Category);
                throw;
            }
        }
    }
}
