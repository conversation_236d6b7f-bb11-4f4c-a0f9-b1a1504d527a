using Shared.Domain.Common;

namespace TLI.Shared.Domain.Events
{
    public class DomainEvent : IDomainEvent
    {
        public Guid Id { get; } = Guid.NewGuid();
        public DateTime OccurredOn { get; } = DateTime.UtcNow;
        public string EventType { get; }
        public Guid AggregateId { get; }
        public object Data { get; }

        public DomainEvent(string eventType, Guid aggregateId, object data)
        {
            EventType = eventType ?? throw new ArgumentNullException(nameof(eventType));
            AggregateId = aggregateId;
            Data = data ?? throw new ArgumentNullException(nameof(data));
        }
    }
}
