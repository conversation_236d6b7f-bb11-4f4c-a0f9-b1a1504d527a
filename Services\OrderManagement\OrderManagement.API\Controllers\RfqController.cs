using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.CreateRfq;
using OrderManagement.Application.Commands.PublishRfq;
using OrderManagement.Application.Commands.CloseRfq;
using OrderManagement.Application.Commands.ExtendRfqTimeframe;
using OrderManagement.Application.Queries.GetRfq;
using OrderManagement.Application.Queries.GetMyRfqs;
using OrderManagement.Application.Queries.GetPublishedRfqs;
using OrderManagement.Application.Queries.GetExpiringSoonRfqs;
using OrderManagement.Application.Queries.GetRfqExpirationStats;
using OrderManagement.Application.Queries.GetFilteredRfqs;
using OrderManagement.Application.Queries.SearchRfqs;
using OrderManagement.Application.Queries.GetRfqTimeline;
using OrderManagement.Application.Queries.GetRfqRoutingHistory;
using OrderManagement.Application.Queries.GetTaggedRfqs;
using OrderManagement.Application.Commands.RouteRfqToBroker;
using OrderManagement.Application.Commands.RouteRfqToCarrier;
using OrderManagement.Application.Commands.ForceAwardRfq;
using OrderManagement.Application.Commands.ResetRfqAward;
using OrderManagement.Application.Commands.AttachMilestoneTemplate;
using OrderManagement.Application.Commands.SetPriceExpectations;
using OrderManagement.Application.Commands.EnableReverseAuction;
using OrderManagement.Application.Commands.AddBrokerComment;
using OrderManagement.Application.Queries.GetRfqMilestoneDetails;
using OrderManagement.Application.Queries.GetNegotiationDetails;
using OrderManagement.Application.Commands.AddPreferredPartnersToRfq;
using OrderManagement.Application.Queries.GetTransporterRatings;
using OrderManagement.Application.Queries.GetBrokerRatings;
using OrderManagement.Application.Queries.GetQuoteComparison;
using OrderManagement.Application.Commands.StartNegotiation;
using OrderManagement.Application.Commands.CreateCounterOffer;
using OrderManagement.Domain.Enums;

namespace OrderManagement.API.Controllers;

[Authorize]
public class RfqController : BaseController
{
    /// <summary>
    /// Create a new RFQ
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateRfq([FromBody] CreateRfqCommand command)
    {
        try
        {
            command.TransportCompanyId = GetCurrentUserId();
            var rfqId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetRfq), new { id = rfqId }, rfqId);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRfq(Guid id)
    {
        try
        {
            var query = new GetRfqQuery
            {
                RfqId = id,
                RequestingUserId = GetCurrentUserId()
            };
            var rfq = await Mediator.Send(query);

            if (rfq == null)
                return NotFound(new { message = "RFQ not found" });

            return Ok(rfq);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Publish an RFQ
    /// </summary>
    [HttpPost("{id}/publish")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> PublishRfq(Guid id)
    {
        try
        {
            var command = new PublishRfqCommand
            {
                RfqId = id,
                TransportCompanyId = GetCurrentUserId()
            };

            var result = await Mediator.Send(command);
            return Ok(new { message = "RFQ published successfully", published = result });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get my RFQs (for transport companies)
    /// </summary>
    [HttpGet("my")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetMyRfqs(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var query = new GetMyRfqsQuery
            {
                TransportCompanyId = transportCompanyId,
                Page = page,
                PageSize = pageSize,
                Status = status,
                FromDate = fromDate,
                ToDate = toDate
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get published RFQs (for brokers)
    /// </summary>
    [HttpGet("published")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetPublishedRfqs(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? pickupCity = null,
        [FromQuery] string? deliveryCity = null,
        [FromQuery] string? loadType = null,
        [FromQuery] decimal? maxBudget = null,
        [FromQuery] bool excludeExpired = true)
    {
        try
        {
            var query = new GetPublishedRfqsQuery
            {
                Page = page,
                PageSize = pageSize,
                FromDate = fromDate,
                ToDate = toDate,
                PickupCity = pickupCity,
                DeliveryCity = deliveryCity,
                LoadType = loadType,
                MaxBudget = maxBudget,
                ExcludeExpired = excludeExpired
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Close an RFQ
    /// </summary>
    [HttpPost("{id}/close")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CloseRfq(Guid id, [FromBody] CloseRfqRequest request)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var command = new CloseRfqCommand
            {
                RfqId = id,
                TransportCompanyId = transportCompanyId,
                ClosureReason = request.Reason
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "RFQ closed successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Extend RFQ timeframe
    /// </summary>
    [HttpPost("{id}/extend-timeframe")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> ExtendRfqTimeframe(Guid id, [FromBody] ExtendRfqTimeframeRequest request)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var command = new ExtendRfqTimeframeCommand
            {
                RfqId = id,
                TransportCompanyId = transportCompanyId,
                Duration = request.Duration,
                Unit = request.Unit,
                Reason = request.Reason
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "RFQ timeframe extended successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQs expiring soon
    /// </summary>
    [HttpGet("expiring-soon")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetExpiringSoonRfqs([FromQuery] GetExpiringSoonRfqsRequest request)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var query = new GetExpiringSoonRfqsQuery
            {
                TransportCompanyId = transportCompanyId,
                HoursBeforeExpiration = request.HoursBeforeExpiration,
                Page = request.Page,
                PageSize = request.PageSize
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ expiration statistics
    /// </summary>
    [HttpGet("expiration-stats")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRfqExpirationStats([FromQuery] GetRfqExpirationStatsRequest request)
    {
        try
        {
            var transportCompanyId = GetCurrentUserId();
            var query = new GetRfqExpirationStatsQuery
            {
                TransportCompanyId = transportCompanyId,
                FromDate = request.FromDate,
                ToDate = request.ToDate
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get filtered RFQs with advanced filtering options
    /// </summary>
    [HttpGet("filtered")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetFilteredRfqs([FromQuery] GetFilteredRfqsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetFilteredRfqsQuery
            {
                RequestingUserId = userId,
                Page = request.Page,
                PageSize = request.PageSize,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                VehicleTypes = request.VehicleTypes,
                LoadTypes = request.LoadTypes,
                Statuses = request.Statuses,
                TransportCompanyId = request.TransportCompanyId,
                BrokerId = request.BrokerId,
                CarrierId = request.CarrierId,
                PickupCity = request.PickupCity,
                DeliveryCity = request.DeliveryCity,
                PickupState = request.PickupState,
                DeliveryState = request.DeliveryState,
                MinBudget = request.MinBudget,
                MaxBudget = request.MaxBudget,
                IsUrgent = request.IsUrgent,
                HasDocuments = request.HasDocuments,
                HasBids = request.HasBids,
                ExcludeExpired = request.ExcludeExpired
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Search RFQs with full-text search
    /// </summary>
    [HttpGet("search")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> SearchRfqs([FromQuery] SearchRfqsRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                return BadRequest(new { message = "Search term is required" });
            }

            var userId = GetCurrentUserId();
            var query = new SearchRfqsQuery
            {
                RequestingUserId = userId,
                SearchTerm = request.SearchTerm,
                Page = request.Page,
                PageSize = request.PageSize,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                Statuses = request.Statuses,
                TransportCompanyId = request.TransportCompanyId,
                ExcludeExpired = request.ExcludeExpired
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ timeline events
    /// </summary>
    [HttpGet("{id}/timeline")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRfqTimeline(Guid id, [FromQuery] GetRfqTimelineRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetRfqTimelineQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                EventTypes = request.EventTypes
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Route RFQ to broker
    /// </summary>
    [HttpPost("{id}/route-to-broker")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RouteRfqToBroker(Guid id, [FromBody] RouteRfqToBrokerRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new RouteRfqToBrokerCommand
            {
                RfqId = id,
                BrokerId = request.BrokerId,
                BrokerName = request.BrokerName,
                RoutedBy = userId,
                RoutedByName = request.RoutedByName,
                RoutingReason = request.RoutingReason,
                RoutingNotes = request.RoutingNotes,
                ResponseDeadline = request.ResponseDeadline,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "RFQ routed to broker successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Route RFQ to carrier
    /// </summary>
    [HttpPost("{id}/route-to-carrier")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RouteRfqToCarrier(Guid id, [FromBody] RouteRfqToCarrierRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new RouteRfqToCarrierCommand
            {
                RfqId = id,
                CarrierId = request.CarrierId,
                CarrierName = request.CarrierName,
                FromBrokerId = request.FromBrokerId,
                FromBrokerName = request.FromBrokerName,
                RoutedBy = userId,
                RoutedByName = request.RoutedByName,
                RoutingReason = request.RoutingReason,
                RoutingNotes = request.RoutingNotes,
                ResponseDeadline = request.ResponseDeadline,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "RFQ routed to carrier successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ routing history
    /// </summary>
    [HttpGet("{id}/routing-history")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRfqRoutingHistory(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetRfqRoutingHistoryQuery
            {
                RfqId = id,
                RequestingUserId = userId
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQs by tags
    /// </summary>
    [HttpGet("tagged")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetTaggedRfqs([FromQuery] GetTaggedRfqsRequest request)
    {
        try
        {
            if (!request.TagTypes.Any())
            {
                return BadRequest(new { message = "At least one tag type is required" });
            }

            var userId = GetCurrentUserId();
            var query = new GetTaggedRfqsQuery
            {
                TagTypes = request.TagTypes,
                Page = request.Page,
                PageSize = request.PageSize,
                RequestingUserId = userId,
                ActiveTagsOnly = request.ActiveTagsOnly
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Attach milestone template to RFQ
    /// </summary>
    [HttpPost("{id}/attach-milestone-template")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> AttachMilestoneTemplate(Guid id, [FromBody] AttachMilestoneTemplateRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new AttachMilestoneTemplateCommand
            {
                RfqId = id,
                MilestoneTemplateId = request.MilestoneTemplateId,
                AssignedBy = userId,
                AssignedByName = userName,
                TotalContractValue = request.TotalContractValue,
                CustomPayoutStructure = request.CustomPayoutStructure,
                AssignmentMetadata = request.AssignmentMetadata,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Milestone template attached successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Set price expectations for RFQ
    /// </summary>
    [HttpPost("{id}/set-price-expectations")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> SetPriceExpectations(Guid id, [FromBody] SetPriceExpectationsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new SetPriceExpectationsCommand
            {
                RfqId = id,
                SetBy = userId,
                SetByName = userName,
                MinExpectedPrice = request.MinExpectedPrice,
                MaxExpectedPrice = request.MaxExpectedPrice,
                TargetPrice = request.TargetPrice,
                ExpectationType = request.ExpectationType,
                IsFlexible = request.IsFlexible,
                PriceJustification = request.PriceJustification,
                PriceValidUntil = request.PriceValidUntil,
                AllowCounterOffers = request.AllowCounterOffers,
                MaxCounterOfferVariance = request.MaxCounterOfferVariance,
                PricingNotes = request.PricingNotes,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Price expectations set successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Enable reverse auction for RFQ
    /// </summary>
    [HttpPost("{id}/enable-reverse-auction")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> EnableReverseAuction(Guid id, [FromBody] EnableReverseAuctionRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new EnableReverseAuctionCommand
            {
                RfqId = id,
                EnabledBy = userId,
                EnabledByName = userName,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                StartingPrice = request.StartingPrice,
                ReservePrice = request.ReservePrice,
                MinimumBidDecrement = request.MinimumBidDecrement,
                MaxBidders = request.MaxBidders,
                AllowBidExtensions = request.AllowBidExtensions,
                ExtensionMinutes = request.ExtensionMinutes,
                IsPublicAuction = request.IsPublicAuction,
                AuctionRules = request.AuctionRules,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Reverse auction enabled successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Add broker comment to RFQ
    /// </summary>
    [HttpPost("{id}/add-broker-comment")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> AddBrokerComment(Guid id, [FromBody] AddBrokerCommentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new AddBrokerCommentCommand
            {
                RfqId = id,
                BrokerId = request.BrokerId,
                BrokerName = request.BrokerName,
                Comment = request.Comment,
                CommentType = request.CommentType,
                IsInternal = request.IsInternal,
                IsVisible = request.IsVisible,
                ParentCommentId = request.ParentCommentId,
                Tags = request.Tags,
                Priority = request.Priority,
                ExpiresAt = request.ExpiresAt,
                Metadata = request.Metadata,
                CreateTimelineEvent = request.CreateTimelineEvent
            };

            var commentId = await Mediator.Send(command);
            return Ok(new { success = true, commentId, message = "Broker comment added successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ milestone details
    /// </summary>
    [HttpGet("{id}/milestone-details")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetRfqMilestoneDetails(Guid id, [FromQuery] GetRfqMilestoneDetailsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetRfqMilestoneDetailsQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                IncludeProgress = request.IncludeProgress,
                IncludePayoutCalculations = request.IncludePayoutCalculations
            };

            var result = await Mediator.Send(query);
            if (result == null)
            {
                return NotFound(new { message = "No milestone details found for this RFQ" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get RFQ negotiation details
    /// </summary>
    [HttpGet("{id}/negotiation-details")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetNegotiationDetails(Guid id, [FromQuery] GetNegotiationDetailsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetNegotiationDetailsQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                IncludeBrokerComments = request.IncludeBrokerComments,
                IncludeInternalComments = request.IncludeInternalComments,
                IncludeReverseAuctionDetails = request.IncludeReverseAuctionDetails
            };

            var result = await Mediator.Send(query);
            if (result == null)
            {
                return NotFound(new { message = "No negotiation details found for this RFQ" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Force award RFQ to specific bid (Administrative Override)
    /// </summary>
    [HttpPost("{id}/force-award")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ForceAwardRfq(Guid id, [FromBody] ForceAwardRfqRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole(); // This would need to be implemented
            var userName = GetCurrentUserName(); // This would need to be implemented

            var command = new ForceAwardRfqCommand
            {
                RfqId = id,
                BidId = request.BidId,
                AdminUserId = userId,
                AdminUserName = userName,
                AdminRole = userRole,
                ForceAwardReason = request.ForceAwardReason,
                AdditionalNotes = request.AdditionalNotes,
                OverrideValidation = request.OverrideValidation,
                NotifyStakeholders = request.NotifyStakeholders,
                AuditMetadata = request.AuditMetadata
            };

            var result = await Mediator.Send(command);

            if (result)
            {
                return Ok(new
                {
                    success = true,
                    message = "RFQ force awarded successfully",
                    warning = "This was an administrative override action and has been logged for audit purposes"
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Force award failed - check validation requirements"
                });
            }
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Insufficient permissions for administrative override");
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Reset RFQ award (Administrative Override)
    /// </summary>
    [HttpPost("{id}/reset-award")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ResetRfqAward(Guid id, [FromBody] ResetRfqAwardRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole(); // This would need to be implemented
            var userName = GetCurrentUserName(); // This would need to be implemented

            var command = new ResetRfqAwardCommand
            {
                RfqId = id,
                AdminUserId = userId,
                AdminUserName = userName,
                AdminRole = userRole,
                ResetReason = request.ResetReason,
                AdditionalNotes = request.AdditionalNotes,
                ReopenForBidding = request.ReopenForBidding,
                NewExpirationDate = request.NewExpirationDate,
                NotifyStakeholders = request.NotifyStakeholders,
                AuditMetadata = request.AuditMetadata
            };

            var result = await Mediator.Send(command);

            if (result)
            {
                return Ok(new
                {
                    success = true,
                    message = "RFQ award reset successfully",
                    warning = "This was an administrative override action and has been logged for audit purposes"
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = "Award reset failed - check validation requirements"
                });
            }
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Insufficient permissions for administrative override");
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    // Helper methods that would need to be implemented based on your authentication system
    private string GetCurrentUserRole()
    {
        // Implementation depends on your authentication/authorization system
        // This is a placeholder
        return User.FindFirst("role")?.Value ?? "Unknown";
    }

    private string GetCurrentUserName()
    {
        // Implementation depends on your authentication/authorization system
        // This is a placeholder
        return User.FindFirst("name")?.Value ?? User.Identity?.Name ?? "Unknown";
    }

    // ===== SHIPPER PORTAL FEATURES =====

    /// <summary>
    /// Add preferred partners to RFQ
    /// </summary>
    [HttpPost("{id}/preferred-partners")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AddPreferredPartners(Guid id, [FromBody] AddPreferredPartnersRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new AddPreferredPartnersToRfqCommand
            {
                RfqId = id,
                PreferredPartners = request.PreferredPartners,
                RequestingUserId = userId
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Preferred partners added successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get transporter ratings for RFQ
    /// </summary>
    [HttpGet("{id}/transporter-ratings")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTransporterRatings(Guid id, [FromQuery] GetTransporterRatingsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetTransporterRatingsQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                ServiceAreas = request.ServiceAreas ?? new List<string>(),
                VehicleTypes = request.VehicleTypes ?? new List<string>(),
                MinRating = request.MinRating,
                MaxResults = request.MaxResults,
                OnlyPreferredPartners = request.OnlyPreferredPartners,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get broker ratings for RFQ
    /// </summary>
    [HttpGet("{id}/broker-ratings")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetBrokerRatings(Guid id, [FromQuery] GetBrokerRatingsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetBrokerRatingsQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                ServiceAreas = request.ServiceAreas ?? new List<string>(),
                Specializations = request.Specializations ?? new List<string>(),
                MinRating = request.MinRating,
                MaxResults = request.MaxResults,
                OnlyPreferredPartners = request.OnlyPreferredPartners,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get quote comparison for RFQ
    /// </summary>
    [HttpGet("{id}/quote-comparison")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetQuoteComparison(Guid id, [FromQuery] GetQuoteComparisonRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetQuoteComparisonQuery
            {
                RfqId = id,
                RequestingUserId = userId,
                ComparisonCriteria = request.ComparisonCriteria ?? new List<string>(),
                IncludeCounterOffers = request.IncludeCounterOffers,
                GenerateRecommendation = request.GenerateRecommendation
            };

            var result = await Mediator.Send(query);
            if (result == null)
            {
                return NotFound(new { message = "No quotes available for comparison" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Start negotiation with broker
    /// </summary>
    [HttpPost("{id}/negotiations")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> StartNegotiation(Guid id, [FromBody] StartNegotiationRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new StartNegotiationCommand
            {
                RfqId = id,
                BrokerId = request.BrokerId,
                OriginalBidId = request.OriginalBidId,
                OriginalPrice = request.OriginalPrice,
                RequestingUserId = userId,
                Notes = request.Notes
            };

            var negotiationId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetNegotiationDetails), new { id = negotiationId },
                new { negotiationId, message = "Negotiation started successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Create counter offer in negotiation
    /// </summary>
    [HttpPost("negotiations/{negotiationId}/counter-offers")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateCounterOffer(Guid negotiationId, [FromBody] CreateCounterOfferRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new CreateCounterOfferCommand
            {
                NegotiationId = negotiationId,
                BidId = request.BidId,
                OfferType = request.OfferType,
                OfferedPrice = request.OfferedPrice,
                OfferedTerms = request.OfferedTerms,
                OfferedPickupDate = request.OfferedPickupDate,
                OfferedDeliveryDate = request.OfferedDeliveryDate,
                AdditionalServices = request.AdditionalServices,
                CounterOfferReason = request.CounterOfferReason,
                ExpiresAt = request.ExpiresAt,
                RequestingUserId = userId
            };

            var counterOfferId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetCounterOfferDetails), new { id = counterOfferId },
                new { counterOfferId, message = "Counter offer created successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get counter offer details (placeholder for future implementation)
    /// </summary>
    [HttpGet("counter-offers/{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCounterOfferDetails(Guid id)
    {
        // This would be implemented with a proper query handler
        return Ok(new { id, message = "Counter offer details endpoint - to be implemented" });
    }
}

// Request DTOs
public class ExtendRfqTimeframeRequest
{
    public int Duration { get; set; }
    public TimeframeUnit Unit { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class GetExpiringSoonRfqsRequest
{
    public int HoursBeforeExpiration { get; set; } = 24;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

public class GetRfqExpirationStatsRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class CloseRfqRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class GetFilteredRfqsRequest
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? VehicleTypes { get; set; }
    public List<string>? LoadTypes { get; set; }
    public List<string>? Statuses { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public string? PickupCity { get; set; }
    public string? DeliveryCity { get; set; }
    public string? PickupState { get; set; }
    public string? DeliveryState { get; set; }
    public decimal? MinBudget { get; set; }
    public decimal? MaxBudget { get; set; }
    public bool? IsUrgent { get; set; }
    public bool? HasDocuments { get; set; }
    public bool? HasBids { get; set; }
    public bool ExcludeExpired { get; set; } = true;
}

public class SearchRfqsRequest
{
    public string SearchTerm { get; set; } = string.Empty;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? Statuses { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public bool ExcludeExpired { get; set; } = true;
}

public class GetRfqTimelineRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? EventTypes { get; set; }
}

public class RouteRfqToBrokerRequest
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string? RoutedByName { get; set; }
    public string? RoutingReason { get; set; }
    public string? RoutingNotes { get; set; }
    public DateTime? ResponseDeadline { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class RouteRfqToCarrierRequest
{
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public Guid? FromBrokerId { get; set; }
    public string? FromBrokerName { get; set; }
    public string? RoutedByName { get; set; }
    public string? RoutingReason { get; set; }
    public string? RoutingNotes { get; set; }
    public DateTime? ResponseDeadline { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class GetTaggedRfqsRequest
{
    public List<string> TagTypes { get; set; } = new();
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public bool ActiveTagsOnly { get; set; } = true;
}

public class ForceAwardRfqRequest
{
    public Guid BidId { get; set; }
    public string ForceAwardReason { get; set; } = string.Empty;
    public string? AdditionalNotes { get; set; }
    public bool OverrideValidation { get; set; } = false;
    public bool NotifyStakeholders { get; set; } = true;
    public Dictionary<string, object>? AuditMetadata { get; set; }
}

public class ResetRfqAwardRequest
{
    public string ResetReason { get; set; } = string.Empty;
    public string? AdditionalNotes { get; set; }
    public bool ReopenForBidding { get; set; } = true;
    public DateTime? NewExpirationDate { get; set; }
    public bool NotifyStakeholders { get; set; } = true;
    public Dictionary<string, object>? AuditMetadata { get; set; }
}

public class AttachMilestoneTemplateRequest
{
    public Guid MilestoneTemplateId { get; set; }
    public MoneyDto? TotalContractValue { get; set; }
    public string? CustomPayoutStructure { get; set; }
    public Dictionary<string, object>? AssignmentMetadata { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class SetPriceExpectationsRequest
{
    public MoneyDto? MinExpectedPrice { get; set; }
    public MoneyDto? MaxExpectedPrice { get; set; }
    public MoneyDto? TargetPrice { get; set; }
    public PriceExpectationType ExpectationType { get; set; } = PriceExpectationType.Range;
    public bool IsFlexible { get; set; } = true;
    public string? PriceJustification { get; set; }
    public DateTime? PriceValidUntil { get; set; }
    public bool AllowCounterOffers { get; set; } = true;
    public decimal? MaxCounterOfferVariance { get; set; }
    public string? PricingNotes { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class EnableReverseAuctionRequest
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public MoneyDto? StartingPrice { get; set; }
    public MoneyDto? ReservePrice { get; set; }
    public decimal? MinimumBidDecrement { get; set; }
    public int? MaxBidders { get; set; }
    public bool AllowBidExtensions { get; set; } = true;
    public int? ExtensionMinutes { get; set; } = 15;
    public bool IsPublicAuction { get; set; } = false;
    public string? AuctionRules { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class AddBrokerCommentRequest
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public BrokerCommentType CommentType { get; set; } = BrokerCommentType.General;
    public bool IsInternal { get; set; } = true;
    public bool IsVisible { get; set; } = true;
    public Guid? ParentCommentId { get; set; }
    public string? Tags { get; set; }
    public int Priority { get; set; } = 0;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}

public class GetRfqMilestoneDetailsRequest
{
    public bool IncludeProgress { get; set; } = true;
    public bool IncludePayoutCalculations { get; set; } = true;
}

public class GetNegotiationDetailsRequest
{
    public bool IncludeBrokerComments { get; set; } = true;
    public bool IncludeInternalComments { get; set; } = false;
    public bool IncludeReverseAuctionDetails { get; set; } = true;
}

// ===== SHIPPER PORTAL REQUEST DTOs =====

public class AddPreferredPartnersRequest
{
    public List<PreferredPartnerRequest> PreferredPartners { get; set; } = new();
}

public class GetTransporterRatingsRequest
{
    public List<string>? ServiceAreas { get; set; }
    public List<string>? VehicleTypes { get; set; }
    public decimal? MinRating { get; set; }
    public int? MaxResults { get; set; } = 50;
    public bool OnlyPreferredPartners { get; set; } = false;
    public string? SortBy { get; set; } = "OverallRating";
    public bool SortDescending { get; set; } = true;
}

public class GetBrokerRatingsRequest
{
    public List<string>? ServiceAreas { get; set; }
    public List<string>? Specializations { get; set; }
    public decimal? MinRating { get; set; }
    public int? MaxResults { get; set; } = 50;
    public bool OnlyPreferredPartners { get; set; } = false;
    public string? SortBy { get; set; } = "OverallRating";
    public bool SortDescending { get; set; } = true;
}

public class GetQuoteComparisonRequest
{
    public List<string>? ComparisonCriteria { get; set; }
    public bool IncludeCounterOffers { get; set; } = true;
    public bool GenerateRecommendation { get; set; } = true;
}

public class StartNegotiationRequest
{
    public Guid BrokerId { get; set; }
    public Guid OriginalBidId { get; set; }
    public MoneyDto OriginalPrice { get; set; } = new();
    public string? Notes { get; set; }
}

public class CreateCounterOfferRequest
{
    public Guid BidId { get; set; }
    public CounterOfferType OfferType { get; set; }
    public MoneyDto OfferedPrice { get; set; } = new();
    public string? OfferedTerms { get; set; }
    public DateTime? OfferedPickupDate { get; set; }
    public DateTime? OfferedDeliveryDate { get; set; }
    public string? AdditionalServices { get; set; }
    public string? CounterOfferReason { get; set; }
    public DateTime? ExpiresAt { get; set; }
}
