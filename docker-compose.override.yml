version: '3.8'

services:
  # Development overrides
  apigateway:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
    volumes:
      - ./ApiGateway/ocelot.json:/app/ocelot.json

  identity-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
    volumes:
      - ./Identity/Identity.API/appsettings.Development.json:/app/appsettings.Development.json
