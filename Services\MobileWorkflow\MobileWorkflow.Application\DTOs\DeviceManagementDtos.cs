namespace MobileWorkflow.Application.DTOs;

public class ManagedDeviceDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string PlatformVersion { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsManaged { get; set; }
    public bool IsCompliant { get; set; }
    public DateTime LastSeen { get; set; }
    public DateTime EnrolledAt { get; set; }
    public DateTime? LastPolicyUpdate { get; set; }
    public Dictionary<string, object> DeviceInfo { get; set; } = new();
    public Dictionary<string, object> SecurityInfo { get; set; } = new();
    public Dictionary<string, object> ComplianceStatus { get; set; } = new();
    public List<Guid> AssignedPolicyIds { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> HealthStatus { get; set; } = new();
    public string RiskLevel { get; set; } = string.Empty;
    public bool IsOnline { get; set; }
    public bool IsStale { get; set; }
}

public class DevicePolicyDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public int Priority { get; set; }
    public Dictionary<string, object> Rules { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Enforcement { get; set; } = new();
    public Dictionary<string, object> Conditions { get; set; } = new();
    public List<string> TargetGroups { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class DeviceCommandDto
{
    public Guid Id { get; set; }
    public Guid DeviceId { get; set; }
    public string CommandType { get; set; } = string.Empty;
    public string CommandName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Result { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public int RetryCount { get; set; }
    public int MaxRetries { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public double? ExecutionTime { get; set; }
    public double Age { get; set; }
    public string Priority { get; set; } = string.Empty;
    public bool RequiresUserInteraction { get; set; }
    public bool IsExpired { get; set; }
    public bool CanRetry { get; set; }
    public bool IsCompleted { get; set; }
}

public class DeviceComplianceResultDto
{
    public Guid DeviceId { get; set; }
    public bool IsCompliant { get; set; }
    public List<string> Violations { get; set; } = new();
    public List<PolicyComplianceDto> PolicyResults { get; set; } = new();
    public DateTime CheckedAt { get; set; }
    public string? CheckType { get; set; }
    public string? TriggeredBy { get; set; }
}

public class PolicyComplianceDto
{
    public Guid PolicyId { get; set; }
    public string PolicyName { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public List<string> Violations { get; set; } = new();
}

public class DeviceInventoryDto
{
    public int TotalDevices { get; set; }
    public int ActiveDevices { get; set; }
    public double ComplianceRate { get; set; }
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
    public Dictionary<string, int> StatusBreakdown { get; set; } = new();
    public int OnlineDevices { get; set; }
    public int StaleDevices { get; set; }
    public Dictionary<string, int> RiskBreakdown { get; set; } = new();
    public int RecentEnrollments { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class DeviceSecurityAlertDto
{
    public Guid DeviceId { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public string Status { get; set; } = string.Empty;
}

// Request DTOs
public class EnrollDeviceRequest
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string PlatformVersion { get; set; } = string.Empty;
    public string AppVersion { get; set; } = string.Empty;
    public Dictionary<string, object> DeviceInfo { get; set; } = new();
    public Dictionary<string, object>? SecurityInfo { get; set; }
}

public class UpdateDeviceRequest
{
    public Dictionary<string, object>? DeviceInfo { get; set; }
    public Dictionary<string, object>? SecurityInfo { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public string? DeviceName { get; set; }
    public string? AppVersion { get; set; }
}

public class CreateDevicePolicyRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0";
    public Dictionary<string, object> Rules { get; set; } = new();
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? Enforcement { get; set; }
    public Dictionary<string, object>? Conditions { get; set; }
    public List<string>? TargetGroups { get; set; }
    public int? Priority { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateDevicePolicyRequest
{
    public Dictionary<string, object>? Rules { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? Enforcement { get; set; }
    public Dictionary<string, object>? Conditions { get; set; }
    public int? Priority { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class SendDeviceCommandRequest
{
    public Guid DeviceId { get; set; }
    public string CommandType { get; set; } = string.Empty;
    public string CommandName { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public TimeSpan? ExpiresIn { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateCommandStatusRequest
{
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object>? Result { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
}

public class DeviceGroupDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Criteria { get; set; } = new();
    public int DeviceCount { get; set; }
    public List<Guid> AssignedPolicyIds { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class DeviceConfigurationProfileDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public Dictionary<string, object> Settings { get; set; } = new();
    public List<string> TargetGroups { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class DeviceApplicationDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string BundleId { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public string InstallationType { get; set; } = string.Empty; // Required, Optional, Blocked
    public string DistributionMethod { get; set; } = string.Empty; // AppStore, Enterprise, Sideload
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class DeviceCertificateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // Root, Intermediate, Identity
    public string Subject { get; set; } = string.Empty;
    public string Issuer { get; set; } = string.Empty;
    public DateTime ValidFrom { get; set; }
    public DateTime ValidTo { get; set; }
    public string Thumbprint { get; set; } = string.Empty;
    public bool IsExpired { get; set; }
    public List<Guid> AssignedDeviceIds { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class DeviceLocationDto
{
    public Guid DeviceId { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public DateTime Timestamp { get; set; }
    public string? Address { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class DeviceWipeRequestDto
{
    public Guid DeviceId { get; set; }
    public string WipeType { get; set; } = string.Empty; // Selective, Full
    public string Reason { get; set; } = string.Empty;
    public bool RequireConfirmation { get; set; } = true;
    public string RequestedBy { get; set; } = string.Empty;
}

public class DeviceLockRequestDto
{
    public Guid DeviceId { get; set; }
    public string? Message { get; set; }
    public string? PhoneNumber { get; set; }
    public bool RequirePasscode { get; set; } = true;
    public string RequestedBy { get; set; } = string.Empty;
}

public class DeviceReportDto
{
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Filters { get; set; } = new();
    public string GeneratedBy { get; set; } = string.Empty;
}
