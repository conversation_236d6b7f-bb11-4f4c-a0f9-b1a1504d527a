using FluentAssertions;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;
using Xunit;

namespace OrderManagement.Tests.Domain.Entities;

public class RfqTimeframeTests
{
    private RFQ CreateTestRfq(RfqTimeframe? timeframe = null)
    {
        var loadDetails = new LoadDetails(
            "Test load",
            LoadType.General,
            new Weight(1000, WeightUnit.Kg),
            1,
            VehicleType.Truck);

        var routeDetails = new RouteDetails(
            new Address("123 Main St", "City", "State", "12345", "Country"),
            new Address("456 Oak Ave", "City2", "State2", "67890", "Country"),
            DateTime.UtcNow.AddDays(1),
            DateTime.UtcNow.AddDays(2));

        var requirements = new RfqRequirements(
            VehicleType.Truck,
            null, false, null, false, new List<string>(),
            false, null, false, false, false, null, new List<string>());

        return new RFQ(
            Guid.NewGuid(),
            "Test RFQ",
            "Test Description",
            loadDetails,
            routeDetails,
            requirements,
            timeframe: timeframe);
    }

    [Fact]
    public void IsExpired_WithTimeframe_ShouldUseTimeframeLogic()
    {
        // Arrange
        var expiredTimeframe = new RfqTimeframe(1, TimeframeUnit.Minutes);
        Thread.Sleep(61000); // Wait for expiration
        var rfq = CreateTestRfq(expiredTimeframe);

        // Act
        var isExpired = rfq.IsExpired();

        // Assert
        isExpired.Should().BeTrue();
    }

    [Fact]
    public void IsExpired_WithoutTimeframe_ShouldUseExpiresAt()
    {
        // Arrange
        var rfq = CreateTestRfq();
        // Set ExpiresAt to past date using reflection or create a new RFQ with past ExpiresAt
        var pastDate = DateTime.UtcNow.AddHours(-1);

        // Act
        var isExpired = rfq.IsExpired();

        // Assert - Without timeframe and no ExpiresAt set, should not be expired
        isExpired.Should().BeFalse();
    }

    [Fact]
    public void IsApproachingExpiration_WithTimeframe_ShouldUseTimeframeLogic()
    {
        // Arrange
        var timeframe = new RfqTimeframe(12, TimeframeUnit.Hours);
        var rfq = CreateTestRfq(timeframe);

        // Act
        var isApproaching = rfq.IsApproachingExpiration();

        // Assert
        isApproaching.Should().BeTrue();
    }

    [Fact]
    public void IsExpiringSoon_WithTimeframe_ShouldUseTimeframeLogic()
    {
        // Arrange
        var timeframe = new RfqTimeframe(6, TimeframeUnit.Hours);
        var rfq = CreateTestRfq(timeframe);

        // Act
        var isExpiringSoon = rfq.IsExpiringSoon(12);

        // Assert
        isExpiringSoon.Should().BeTrue();
    }

    [Fact]
    public void Expire_WhenNotExpired_ShouldExpireRfq()
    {
        // Arrange
        var rfq = CreateTestRfq();
        rfq.Publish(); // Need to publish first

        // Act
        rfq.Expire();

        // Assert
        rfq.Status.Should().Be(RfqStatus.Expired);
        rfq.ClosedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        rfq.ClosureReason.Should().Be("Automatically expired due to timeframe");
    }

    [Fact]
    public void Expire_WhenAlreadyExpired_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var rfq = CreateTestRfq();
        rfq.Publish();
        rfq.Expire();

        // Act & Assert
        var act = () => rfq.Expire();
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("RFQ is already expired");
    }

    [Fact]
    public void Expire_WhenClosed_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var rfq = CreateTestRfq();
        rfq.Publish();
        rfq.Close("Manual closure");

        // Act & Assert
        var act = () => rfq.Expire();
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot expire a closed RFQ");
    }

    [Fact]
    public void ExtendTimeframe_WithValidParameters_ShouldExtendTimeframe()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);
        var rfq = CreateTestRfq(timeframe);
        rfq.Publish();
        var extendedBy = Guid.NewGuid();
        var originalExpiresAt = rfq.ExpiresAt;

        // Act
        rfq.ExtendTimeframe(12, TimeframeUnit.Hours, "Need more time", extendedBy);

        // Assert
        rfq.Timeframe!.ExtensionCount.Should().Be(1);
        rfq.ExpiresAt.Should().BeAfter(originalExpiresAt!.Value);
        rfq.TimeframeExtensions.Should().HaveCount(1);
        
        var extension = rfq.TimeframeExtensions.First();
        extension.Duration.Should().Be(12);
        extension.Unit.Should().Be(TimeframeUnit.Hours);
        extension.Reason.Should().Be("Need more time");
        extension.ExtendedBy.Should().Be(extendedBy);
    }

    [Fact]
    public void ExtendTimeframe_WhenClosed_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);
        var rfq = CreateTestRfq(timeframe);
        rfq.Publish();
        rfq.Close("Manual closure");

        // Act & Assert
        var act = () => rfq.ExtendTimeframe(12, TimeframeUnit.Hours, "reason", Guid.NewGuid());
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot extend timeframe of closed or expired RFQ");
    }

    [Fact]
    public void ExtendTimeframe_WhenExpired_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, true, 3);
        var rfq = CreateTestRfq(timeframe);
        rfq.Publish();
        rfq.Expire();

        // Act & Assert
        var act = () => rfq.ExtendTimeframe(12, TimeframeUnit.Hours, "reason", Guid.NewGuid());
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot extend timeframe of closed or expired RFQ");
    }

    [Fact]
    public void ExtendTimeframe_WithoutTimeframe_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var rfq = CreateTestRfq(); // No timeframe
        rfq.Publish();

        // Act & Assert
        var act = () => rfq.ExtendTimeframe(12, TimeframeUnit.Hours, "reason", Guid.NewGuid());
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot extend RFQ without timeframe configuration");
    }

    [Fact]
    public void ExtendTimeframe_WhenCannotExtend_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var timeframe = new RfqTimeframe(24, TimeframeUnit.Hours, false, 3); // Extensions not allowed
        var rfq = CreateTestRfq(timeframe);
        rfq.Publish();

        // Act & Assert
        var act = () => rfq.ExtendTimeframe(12, TimeframeUnit.Hours, "reason", Guid.NewGuid());
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot extend RFQ timeframe - maximum extensions reached or extensions not allowed");
    }

    [Fact]
    public void AddBid_WhenExpired_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var expiredTimeframe = new RfqTimeframe(1, TimeframeUnit.Minutes);
        var rfq = CreateTestRfq(expiredTimeframe);
        rfq.Publish();
        Thread.Sleep(61000); // Wait for expiration

        var bid = new RfqBid(
            rfq.Id,
            Guid.NewGuid(),
            new Money(1000, "USD"),
            DateTime.UtcNow.AddDays(1),
            DateTime.UtcNow.AddDays(2));

        // Act & Assert
        var act = () => rfq.AddBid(bid);
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Cannot add bid to expired RFQ");
    }

    [Fact]
    public void Constructor_WithTimeframe_ShouldSetExpiresAtFromTimeframe()
    {
        // Arrange
        var timeframe = new RfqTimeframe(48, TimeframeUnit.Hours);

        // Act
        var rfq = CreateTestRfq(timeframe);

        // Assert
        rfq.ExpiresAt.Should().Be(timeframe.ExpiresAt);
        rfq.Timeframe.Should().Be(timeframe);
    }

    [Fact]
    public void Constructor_WithoutTimeframe_ShouldUseExplicitExpiresAt()
    {
        // Arrange
        var explicitExpiresAt = DateTime.UtcNow.AddDays(3);
        
        var loadDetails = new LoadDetails(
            "Test load",
            LoadType.General,
            new Weight(1000, WeightUnit.Kg),
            1,
            VehicleType.Truck);

        var routeDetails = new RouteDetails(
            new Address("123 Main St", "City", "State", "12345", "Country"),
            new Address("456 Oak Ave", "City2", "State2", "67890", "Country"),
            DateTime.UtcNow.AddDays(1),
            DateTime.UtcNow.AddDays(2));

        var requirements = new RfqRequirements(
            VehicleType.Truck,
            null, false, null, false, new List<string>(),
            false, null, false, false, false, null, new List<string>());

        // Act
        var rfq = new RFQ(
            Guid.NewGuid(),
            "Test RFQ",
            "Test Description",
            loadDetails,
            routeDetails,
            requirements,
            explicitExpiresAt);

        // Assert
        rfq.ExpiresAt.Should().Be(explicitExpiresAt);
        rfq.Timeframe.Should().BeNull();
    }
}
