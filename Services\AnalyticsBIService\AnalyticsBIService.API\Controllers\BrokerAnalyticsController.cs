using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Queries.Broker;
using AnalyticsBIService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Broker analytics controller for operational, RFQ, quote, and carrier network analytics
/// </summary>
[ApiController]
[Route("api/broker/analytics")]
[Authorize(Roles = "Broker,Admin")]
public class BrokerAnalyticsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<BrokerAnalyticsController> _logger;

    public BrokerAnalyticsController(IMediator mediator, ILogger<BrokerAnalyticsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get broker dashboard with comprehensive analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for analytics period</param>
    /// <param name="toDate">End date for analytics period</param>
    /// <param name="period">Time period granularity</param>
    /// <returns>Broker dashboard data</returns>
    [HttpGet("{brokerId}/dashboard")]
    public async Task<ActionResult<BrokerDashboardDto>> GetDashboard(
        Guid brokerId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TimePeriod period = TimePeriod.Daily)
    {
        try
        {
            var query = new GetBrokerDashboardQuery(brokerId, fromDate, toDate, period);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting broker dashboard for {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker operational analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for operational analysis</param>
    /// <param name="toDate">End date for operational analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeRFQMetrics">Include RFQ processing metrics</param>
    /// <param name="includeQuoteMetrics">Include quote generation metrics</param>
    /// <param name="includeCarrierMetrics">Include carrier network metrics</param>
    /// <returns>Broker operational analytics data</returns>
    [HttpGet("{brokerId}/operational")]
    public async Task<ActionResult<BrokerOperationalAnalyticsDto>> GetOperationalAnalytics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeRFQMetrics = true,
        [FromQuery] bool includeQuoteMetrics = true,
        [FromQuery] bool includeCarrierMetrics = true)
    {
        try
        {
            var query = new GetBrokerOperationalAnalyticsQuery(brokerId, fromDate, toDate, period, includeRFQMetrics, includeQuoteMetrics, includeCarrierMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting operational analytics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker RFQ conversion analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for RFQ analysis</param>
    /// <param name="toDate">End date for RFQ analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="transportCompanyId">Filter by specific transport company</param>
    /// <param name="routeType">Filter by route type</param>
    /// <returns>Broker RFQ conversion analytics data</returns>
    [HttpGet("{brokerId}/rfq-conversion")]
    public async Task<ActionResult<BrokerRFQConversionAnalyticsDto>> GetRFQConversionAnalytics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] Guid? transportCompanyId = null,
        [FromQuery] string? routeType = null)
    {
        try
        {
            var query = new GetBrokerRFQConversionAnalyticsQuery(brokerId, fromDate, toDate, period, transportCompanyId, routeType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting RFQ conversion analytics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker quote success analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for quote analysis</param>
    /// <param name="toDate">End date for quote analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="quoteType">Filter by quote type</param>
    /// <param name="serviceLevel">Filter by service level</param>
    /// <returns>Broker quote success analytics data</returns>
    [HttpGet("{brokerId}/quote-success")]
    public async Task<ActionResult<BrokerQuoteSuccessAnalyticsDto>> GetQuoteSuccessAnalytics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? quoteType = null,
        [FromQuery] string? serviceLevel = null)
    {
        try
        {
            var query = new GetBrokerQuoteSuccessAnalyticsQuery(brokerId, fromDate, toDate, period, quoteType, serviceLevel);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quote success analytics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier network utilization analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for carrier network analysis</param>
    /// <param name="toDate">End date for carrier network analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="carrierType">Filter by carrier type</param>
    /// <param name="includeCapacityManagement">Include capacity management analysis</param>
    /// <returns>Carrier network utilization data</returns>
    [HttpGet("{brokerId}/carrier-network")]
    public async Task<ActionResult<CarrierNetworkUtilizationDto>> GetCarrierNetworkUtilization(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? carrierType = null,
        [FromQuery] bool includeCapacityManagement = true)
    {
        try
        {
            var query = new GetCarrierNetworkUtilizationQuery(brokerId, fromDate, toDate, period, null, carrierType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier network utilization for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker margin analysis
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for margin analysis</param>
    /// <param name="toDate">End date for margin analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeCommissionTracking">Include commission tracking</param>
    /// <param name="includeProfitabilityMetrics">Include profitability metrics</param>
    /// <returns>Broker margin analysis data</returns>
    [HttpGet("{brokerId}/margin-analysis")]
    public async Task<ActionResult<BrokerMarginAnalysisDto>> GetMarginAnalysis(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeCommissionTracking = true,
        [FromQuery] bool includeProfitabilityMetrics = true)
    {
        try
        {
            var query = new GetBrokerMarginAnalysisQuery(brokerId, fromDate, toDate, period, includeCommissionTracking, includeProfitabilityMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting margin analysis for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker business growth analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for growth analysis</param>
    /// <param name="toDate">End date for growth analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeMarketOpportunities">Include market opportunities</param>
    /// <param name="includeExpansionPotential">Include expansion potential</param>
    /// <returns>Broker business growth analytics data</returns>
    [HttpGet("{brokerId}/business-growth")]
    public async Task<ActionResult<BrokerBusinessGrowthAnalyticsDto>> GetBusinessGrowthAnalytics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeMarketOpportunities = true,
        [FromQuery] bool includeExpansionPotential = true)
    {
        try
        {
            var query = new GetBrokerBusinessGrowthAnalyticsQuery(brokerId, fromDate, toDate, period, includeMarketOpportunities, includeExpansionPotential);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting business growth analytics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker performance tracking
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for performance tracking</param>
    /// <param name="toDate">End date for performance tracking</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeTripCompletion">Include trip completion metrics</param>
    /// <param name="includeDeliveryPerformance">Include delivery performance monitoring</param>
    /// <returns>Broker performance tracking data</returns>
    [HttpGet("{brokerId}/performance-tracking")]
    public async Task<ActionResult<BrokerPerformanceTrackingDto>> GetPerformanceTracking(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeTripCompletion = true,
        [FromQuery] bool includeDeliveryPerformance = true)
    {
        try
        {
            var query = new GetBrokerPerformanceTrackingQuery(brokerId, fromDate, toDate, period, includeTripCompletion, includeDeliveryPerformance);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance tracking for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get carrier network growth analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for network growth analysis</param>
    /// <param name="toDate">End date for network growth analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeQualityMetrics">Include quality metrics</param>
    /// <param name="includePerformanceTrends">Include performance trends</param>
    /// <returns>Carrier network growth analytics data</returns>
    [HttpGet("{brokerId}/network-growth")]
    public async Task<ActionResult<CarrierNetworkGrowthAnalyticsDto>> GetCarrierNetworkGrowthAnalytics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeQualityMetrics = true,
        [FromQuery] bool includePerformanceTrends = true)
    {
        try
        {
            var query = new GetCarrierNetworkGrowthAnalyticsQuery(brokerId, fromDate, toDate, period, includeQualityMetrics, includePerformanceTrends);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier network growth analytics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker efficiency metrics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for efficiency analysis</param>
    /// <param name="toDate">End date for efficiency analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeProcessEfficiency">Include process efficiency metrics</param>
    /// <param name="includeResourceUtilization">Include resource utilization</param>
    /// <returns>Broker efficiency metrics data</returns>
    [HttpGet("{brokerId}/efficiency")]
    public async Task<ActionResult<BrokerEfficiencyMetricsDto>> GetEfficiencyMetrics(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeProcessEfficiency = true,
        [FromQuery] bool includeResourceUtilization = true)
    {
        try
        {
            var query = new GetBrokerEfficiencyMetricsQuery(brokerId, fromDate, toDate, period, includeProcessEfficiency, includeResourceUtilization);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting efficiency metrics for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker customer satisfaction analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for satisfaction analysis</param>
    /// <param name="toDate">End date for satisfaction analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="customerType">Filter by customer type</param>
    /// <param name="includeFeedbackAnalysis">Include feedback analysis</param>
    /// <returns>Broker customer satisfaction data</returns>
    [HttpGet("{brokerId}/customer-satisfaction")]
    public async Task<ActionResult<BrokerCustomerSatisfactionDto>> GetCustomerSatisfaction(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] string? customerType = null,
        [FromQuery] bool includeFeedbackAnalysis = true)
    {
        try
        {
            var query = new GetBrokerCustomerSatisfactionQuery(brokerId, fromDate, toDate, period, includeFeedbackAnalysis, true);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer satisfaction for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker competitive analysis
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for competitive analysis</param>
    /// <param name="toDate">End date for competitive analysis</param>
    /// <param name="marketSegment">Filter by market segment</param>
    /// <param name="geographicRegion">Filter by geographic region</param>
    /// <param name="includeMarketShare">Include market share analysis</param>
    /// <returns>Broker competitive analysis data</returns>
    [HttpGet("{brokerId}/competitive-analysis")]
    public async Task<ActionResult<BrokerCompetitiveAnalysisDto>> GetCompetitiveAnalysis(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? marketSegment = null,
        [FromQuery] string? geographicRegion = null,
        [FromQuery] bool includeMarketShare = true)
    {
        try
        {
            var query = new GetBrokerCompetitiveAnalysisQuery(brokerId, fromDate, toDate, marketSegment, geographicRegion, includeMarketShare);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competitive analysis for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker route optimization analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for route optimization analysis</param>
    /// <param name="toDate">End date for route optimization analysis</param>
    /// <param name="originCity">Filter by origin city</param>
    /// <param name="destinationCity">Filter by destination city</param>
    /// <param name="includeEfficiencyMetrics">Include efficiency metrics</param>
    /// <returns>Broker route optimization data</returns>
    [HttpGet("{brokerId}/route-optimization")]
    public async Task<ActionResult<BrokerRouteOptimizationDto>> GetRouteOptimization(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? originCity = null,
        [FromQuery] string? destinationCity = null,
        [FromQuery] bool includeEfficiencyMetrics = true)
    {
        try
        {
            var query = new GetBrokerRouteOptimizationQuery(brokerId, fromDate, toDate, originCity, destinationCity, includeEfficiencyMetrics);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting route optimization for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker capacity management analytics
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for capacity analysis</param>
    /// <param name="toDate">End date for capacity analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeUtilizationMetrics">Include utilization metrics</param>
    /// <param name="includeCapacityOptimization">Include capacity optimization</param>
    /// <returns>Broker capacity management data</returns>
    [HttpGet("{brokerId}/capacity-management")]
    public async Task<ActionResult<BrokerCapacityManagementDto>> GetCapacityManagement(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Daily,
        [FromQuery] bool includeUtilizationMetrics = true,
        [FromQuery] bool includeCapacityOptimization = true)
    {
        try
        {
            var query = new GetBrokerCapacityManagementQuery(brokerId, fromDate, toDate, period, includeUtilizationMetrics, includeCapacityOptimization);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting capacity management for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get broker financial performance
    /// </summary>
    /// <param name="brokerId">Broker ID</param>
    /// <param name="fromDate">Start date for financial analysis</param>
    /// <param name="toDate">End date for financial analysis</param>
    /// <param name="period">Time period granularity</param>
    /// <param name="includeRevenueBreakdown">Include revenue breakdown</param>
    /// <param name="includeProfitabilityAnalysis">Include profitability analysis</param>
    /// <returns>Broker financial performance data</returns>
    [HttpGet("{brokerId}/financial-performance")]
    public async Task<ActionResult<BrokerFinancialPerformanceDto>> GetFinancialPerformance(
        Guid brokerId,
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] TimePeriod period = TimePeriod.Monthly,
        [FromQuery] bool includeRevenueBreakdown = true,
        [FromQuery] bool includeProfitabilityAnalysis = true)
    {
        try
        {
            var query = new GetBrokerFinancialPerformanceQuery(brokerId, fromDate, toDate, period, includeRevenueBreakdown, includeProfitabilityAnalysis);
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting financial performance for broker {BrokerId}", brokerId);
            return StatusCode(500, "Internal server error");
        }
    }
}
