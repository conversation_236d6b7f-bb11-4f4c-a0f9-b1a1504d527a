using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Entities;

public class FeatureFlagRule : BaseEntity
{
    public Guid FeatureFlagId { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public FeatureFlagRuleType Type { get; private set; }
    public string Condition { get; private set; } // JSON condition
    public bool IsActive { get; private set; }
    public int Priority { get; private set; }

    private FeatureFlagRule() { }

    public FeatureFlagRule(
        Guid featureFlagId,
        string name,
        string description,
        FeatureFlagRuleType type,
        string condition,
        int priority = 0)
    {
        FeatureFlagId = featureFlagId;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        Condition = condition ?? throw new ArgumentNullException(nameof(condition));
        Priority = priority;
        IsActive = true;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void UpdateCondition(string condition)
    {
        Condition = condition ?? throw new ArgumentNullException(nameof(condition));
    }

    public void UpdatePriority(int priority)
    {
        Priority = priority;
    }

    public bool EvaluateRule(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!IsActive)
            return true; // Inactive rules don't block

        try
        {
            return Type switch
            {
                FeatureFlagRuleType.UserAttribute => EvaluateUserAttributeRule(userId, context),
                FeatureFlagRuleType.UserSegment => EvaluateUserSegmentRule(userId, context),
                FeatureFlagRuleType.Geographic => EvaluateGeographicRule(context),
                FeatureFlagRuleType.TimeWindow => EvaluateTimeWindowRule(),
                FeatureFlagRuleType.Custom => EvaluateCustomRule(userId, context),
                _ => true
            };
        }
        catch
        {
            // If rule evaluation fails, default to allowing access
            return true;
        }
    }

    private bool EvaluateUserAttributeRule(Guid userId, Dictionary<string, object>? context)
    {
        if (context == null)
            return true;

        var condition = System.Text.Json.JsonSerializer.Deserialize<UserAttributeCondition>(Condition);
        if (condition == null)
            return true;

        if (context.TryGetValue(condition.AttributeName, out var value))
        {
            return condition.Operator switch
            {
                "equals" => value?.ToString() == condition.Value,
                "not_equals" => value?.ToString() != condition.Value,
                "contains" => value?.ToString()?.Contains(condition.Value) == true,
                "in" => condition.Values?.Contains(value?.ToString()) == true,
                "greater_than" => CompareNumeric(value, condition.Value, (a, b) => a > b),
                "less_than" => CompareNumeric(value, condition.Value, (a, b) => a < b),
                _ => true
            };
        }

        return true;
    }

    private bool EvaluateUserSegmentRule(Guid userId, Dictionary<string, object>? context)
    {
        var condition = System.Text.Json.JsonSerializer.Deserialize<UserSegmentCondition>(Condition);
        if (condition == null)
            return true;

        // Check if user belongs to specified segments
        if (context?.TryGetValue("userSegments", out var segments) == true)
        {
            var userSegments = segments as string[] ?? new string[0];
            return condition.Segments?.Any(s => userSegments.Contains(s)) == true;
        }

        return true;
    }

    private bool EvaluateGeographicRule(Dictionary<string, object>? context)
    {
        var condition = System.Text.Json.JsonSerializer.Deserialize<GeographicCondition>(Condition);
        if (condition == null)
            return true;

        if (context?.TryGetValue("country", out var country) == true)
        {
            return condition.AllowedCountries?.Contains(country?.ToString()) == true;
        }

        return true;
    }

    private bool EvaluateTimeWindowRule()
    {
        var condition = System.Text.Json.JsonSerializer.Deserialize<TimeWindowCondition>(Condition);
        if (condition == null)
            return true;

        var now = DateTime.UtcNow;
        
        if (condition.StartTime.HasValue && now < condition.StartTime.Value)
            return false;

        if (condition.EndTime.HasValue && now > condition.EndTime.Value)
            return false;

        if (condition.DaysOfWeek?.Any() == true)
        {
            var currentDay = now.DayOfWeek.ToString();
            return condition.DaysOfWeek.Contains(currentDay);
        }

        return true;
    }

    private bool EvaluateCustomRule(Guid userId, Dictionary<string, object>? context)
    {
        // For custom rules, we could implement a simple expression evaluator
        // For now, return true (allow access)
        return true;
    }

    private bool CompareNumeric(object? value1, string value2, Func<double, double, bool> comparison)
    {
        if (double.TryParse(value1?.ToString(), out var num1) && 
            double.TryParse(value2, out var num2))
        {
            return comparison(num1, num2);
        }
        return false;
    }
}

// Condition classes for different rule types
public class UserAttributeCondition
{
    public string AttributeName { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string[]? Values { get; set; }
}

public class UserSegmentCondition
{
    public string[]? Segments { get; set; }
}

public class GeographicCondition
{
    public string[]? AllowedCountries { get; set; }
    public string[]? BlockedCountries { get; set; }
}

public class TimeWindowCondition
{
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string[]? DaysOfWeek { get; set; }
}
