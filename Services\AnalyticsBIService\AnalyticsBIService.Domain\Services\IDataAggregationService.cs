using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Domain.Services
{
    /// <summary>
    /// Interface for data aggregation service operations
    /// </summary>
    public interface IDataAggregationService
    {
        /// <summary>
        /// Aggregate metrics by time period
        /// </summary>
        /// <param name="metricName">Name of the metric</param>
        /// <param name="startDate">Start date for aggregation</param>
        /// <param name="endDate">End date for aggregation</param>
        /// <param name="aggregationType">Type of aggregation (Sum, Average, Count, etc.)</param>
        /// <param name="groupBy">Grouping criteria</param>
        /// <returns>Aggregated metric data</returns>
        Task<AggregatedMetricData> AggregateMetricsByTimeAsync(
            string metricName,
            DateTime startDate,
            DateTime endDate,
            AggregationType aggregationType,
            TimeGrouping groupBy);

        /// <summary>
        /// Aggregate performance metrics for carriers
        /// </summary>
        /// <param name="carrierIds">List of carrier IDs</param>
        /// <param name="startDate">Start date for aggregation</param>
        /// <param name="endDate">End date for aggregation</param>
        /// <returns>Carrier performance aggregation</returns>
        Task<CarrierPerformanceAggregation> AggregateCarrierPerformanceAsync(
            IEnumerable<Guid> carrierIds,
            DateTime startDate,
            DateTime endDate);

        /// <summary>
        /// Aggregate trip analytics data
        /// </summary>
        /// <param name="filters">Trip filtering criteria</param>
        /// <param name="groupBy">Grouping criteria</param>
        /// <returns>Trip analytics aggregation</returns>
        Task<TripAnalyticsAggregation> AggregateTripAnalyticsAsync(
            TripAggregationFilters filters,
            TripGrouping groupBy);

        /// <summary>
        /// Aggregate order analytics data
        /// </summary>
        /// <param name="filters">Order filtering criteria</param>
        /// <param name="groupBy">Grouping criteria</param>
        /// <returns>Order analytics aggregation</returns>
        Task<OrderAnalyticsAggregation> AggregateOrderAnalyticsAsync(
            OrderAggregationFilters filters,
            OrderGrouping groupBy);

        /// <summary>
        /// Aggregate financial metrics
        /// </summary>
        /// <param name="entityType">Type of entity (Carrier, Shipper, etc.)</param>
        /// <param name="entityIds">List of entity IDs</param>
        /// <param name="startDate">Start date for aggregation</param>
        /// <param name="endDate">End date for aggregation</param>
        /// <param name="currency">Currency for financial calculations</param>
        /// <returns>Financial metrics aggregation</returns>
        Task<FinancialMetricsAggregation> AggregateFinancialMetricsAsync(
            EntityType entityType,
            IEnumerable<Guid> entityIds,
            DateTime startDate,
            DateTime endDate,
            string currency = "INR");

        /// <summary>
        /// Aggregate real-time metrics
        /// </summary>
        /// <param name="metricTypes">Types of metrics to aggregate</param>
        /// <param name="timeWindow">Time window for real-time aggregation</param>
        /// <returns>Real-time metrics aggregation</returns>
        Task<RealTimeMetricsAggregation> AggregateRealTimeMetricsAsync(
            IEnumerable<MetricType> metricTypes,
            TimeSpan timeWindow);

        /// <summary>
        /// Calculate trend analysis for metrics
        /// </summary>
        /// <param name="metricName">Name of the metric</param>
        /// <param name="entityId">Entity ID</param>
        /// <param name="startDate">Start date for trend analysis</param>
        /// <param name="endDate">End date for trend analysis</param>
        /// <param name="trendType">Type of trend analysis</param>
        /// <returns>Trend analysis data</returns>
        Task<TrendAnalysisData> CalculateTrendAnalysisAsync(
            string metricName,
            Guid entityId,
            DateTime startDate,
            DateTime endDate,
            TrendType trendType);

        /// <summary>
        /// Calculate comparative analytics between entities
        /// </summary>
        /// <param name="entityType">Type of entities to compare</param>
        /// <param name="entityIds">List of entity IDs to compare</param>
        /// <param name="metrics">Metrics to compare</param>
        /// <param name="startDate">Start date for comparison</param>
        /// <param name="endDate">End date for comparison</param>
        /// <returns>Comparative analytics data</returns>
        Task<ComparativeAnalyticsData> CalculateComparativeAnalyticsAsync(
            EntityType entityType,
            IEnumerable<Guid> entityIds,
            IEnumerable<string> metrics,
            DateTime startDate,
            DateTime endDate);

        /// <summary>
        /// Aggregate custom metrics based on user-defined criteria
        /// </summary>
        /// <param name="customAggregationRequest">Custom aggregation request</param>
        /// <returns>Custom aggregation result</returns>
        Task<CustomAggregationResult> AggregateCustomMetricsAsync(
            CustomAggregationRequest customAggregationRequest);
    }

    #region Supporting Data Models

    public class AggregatedMetricData
    {
        public string MetricName { get; set; } = string.Empty;
        public AggregationType AggregationType { get; set; }
        public TimeGrouping GroupBy { get; set; }
        public List<TimeSeriesDataPoint> DataPoints { get; set; } = new();
        public decimal TotalValue { get; set; }
        public decimal AverageValue { get; set; }
        public decimal MinValue { get; set; }
        public decimal MaxValue { get; set; }
        public int DataPointCount { get; set; }
    }

    public class TimeSeriesDataPoint
    {
        public DateTime Timestamp { get; set; }
        public decimal Value { get; set; }
        public string Label { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class CarrierPerformanceAggregation
    {
        public List<CarrierPerformanceData> CarrierData { get; set; } = new();
        public decimal OverallAverageRating { get; set; }
        public decimal OverallOnTimePercentage { get; set; }
        public int TotalTrips { get; set; }
        public DateTime AggregationPeriodStart { get; set; }
        public DateTime AggregationPeriodEnd { get; set; }
    }

    public class CarrierPerformanceData
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = string.Empty;
        public decimal AverageRating { get; set; }
        public decimal OnTimePercentage { get; set; }
        public int TotalTrips { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageTripDuration { get; set; }
        public decimal FuelEfficiency { get; set; }
    }

    public class TripAnalyticsAggregation
    {
        public List<TripAnalyticsData> TripData { get; set; } = new();
        public TripGrouping GroupBy { get; set; }
        public int TotalTrips { get; set; }
        public decimal TotalDistance { get; set; }
        public decimal TotalDuration { get; set; }
        public decimal AverageFuelEfficiency { get; set; }
        public decimal TotalCost { get; set; }
    }

    public class TripAnalyticsData
    {
        public string GroupKey { get; set; } = string.Empty;
        public int TripCount { get; set; }
        public decimal TotalDistance { get; set; }
        public decimal TotalDuration { get; set; }
        public decimal AverageFuelEfficiency { get; set; }
        public decimal TotalCost { get; set; }
        public decimal OnTimePercentage { get; set; }
    }

    public class OrderAnalyticsAggregation
    {
        public List<OrderAnalyticsData> OrderData { get; set; } = new();
        public OrderGrouping GroupBy { get; set; }
        public int TotalOrders { get; set; }
        public decimal TotalValue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal CompletionRate { get; set; }
    }

    public class OrderAnalyticsData
    {
        public string GroupKey { get; set; } = string.Empty;
        public int OrderCount { get; set; }
        public decimal TotalValue { get; set; }
        public decimal AverageValue { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageDeliveryTime { get; set; }
    }

    public class FinancialMetricsAggregation
    {
        public EntityType EntityType { get; set; }
        public string Currency { get; set; } = "INR";
        public List<FinancialMetricsData> EntityData { get; set; } = new();
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal AverageMargin { get; set; }
    }

    public class FinancialMetricsData
    {
        public Guid EntityId { get; set; }
        public string EntityName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Costs { get; set; }
        public decimal Profit { get; set; }
        public decimal Margin { get; set; }
        public int TransactionCount { get; set; }
    }

    public class RealTimeMetricsAggregation
    {
        public List<RealTimeMetricData> Metrics { get; set; } = new();
        public TimeSpan TimeWindow { get; set; }
        public DateTime LastUpdated { get; set; }
        public int TotalDataPoints { get; set; }
    }

    public class RealTimeMetricData
    {
        public MetricType MetricType { get; set; }
        public string MetricName { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal PreviousValue { get; set; }
        public decimal ChangePercentage { get; set; }
        public string Trend { get; set; } = string.Empty; // "up", "down", "stable"
        public DateTime LastUpdated { get; set; }
    }

    public class TrendAnalysisData
    {
        public string MetricName { get; set; } = string.Empty;
        public Guid EntityId { get; set; }
        public TrendType TrendType { get; set; }
        public List<TrendDataPoint> DataPoints { get; set; } = new();
        public string TrendDirection { get; set; } = string.Empty; // "increasing", "decreasing", "stable"
        public decimal TrendSlope { get; set; }
        public decimal CorrelationCoefficient { get; set; }
        public string TrendStrength { get; set; } = string.Empty; // "strong", "moderate", "weak"
    }

    public class TrendDataPoint
    {
        public DateTime Date { get; set; }
        public decimal ActualValue { get; set; }
        public decimal TrendValue { get; set; }
        public decimal Deviation { get; set; }
    }

    public class ComparativeAnalyticsData
    {
        public EntityType EntityType { get; set; }
        public List<string> Metrics { get; set; } = new();
        public List<EntityComparisonData> EntityComparisons { get; set; } = new();
        public Dictionary<string, RankingData> MetricRankings { get; set; } = new();
        public DateTime ComparisonPeriodStart { get; set; }
        public DateTime ComparisonPeriodEnd { get; set; }
    }

    public class EntityComparisonData
    {
        public Guid EntityId { get; set; }
        public string EntityName { get; set; } = string.Empty;
        public Dictionary<string, decimal> MetricValues { get; set; } = new();
        public Dictionary<string, int> MetricRanks { get; set; } = new();
        public decimal OverallScore { get; set; }
        public int OverallRank { get; set; }
    }

    public class RankingData
    {
        public string MetricName { get; set; } = string.Empty;
        public List<EntityRank> Rankings { get; set; } = new();
        public decimal AverageValue { get; set; }
        public decimal MedianValue { get; set; }
        public decimal StandardDeviation { get; set; }
    }

    public class EntityRank
    {
        public Guid EntityId { get; set; }
        public string EntityName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public int Rank { get; set; }
        public decimal PercentileRank { get; set; }
    }

    public class CustomAggregationResult
    {
        public string RequestId { get; set; } = string.Empty;
        public Dictionary<string, object> Results { get; set; } = new();
        public List<string> Metrics { get; set; } = new();
        public List<string> Dimensions { get; set; } = new();
        public int TotalRecords { get; set; }
        public DateTime ProcessedAt { get; set; }
        public TimeSpan ProcessingTime { get; set; }
    }

    public class CustomAggregationRequest
    {
        public string RequestId { get; set; } = Guid.NewGuid().ToString();
        public List<string> Metrics { get; set; } = new();
        public List<string> Dimensions { get; set; } = new();
        public Dictionary<string, object> Filters { get; set; } = new();
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public AggregationType AggregationType { get; set; }
        public int? Limit { get; set; }
        public string? OrderBy { get; set; }
        public bool OrderDescending { get; set; } = true;
    }

    public class TripAggregationFilters
    {
        public List<Guid>? CarrierIds { get; set; }
        public List<Guid>? DriverIds { get; set; }
        public List<Guid>? VehicleIds { get; set; }
        public List<string>? Routes { get; set; }
        public List<string>? Statuses { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? MinDistance { get; set; }
        public decimal? MaxDistance { get; set; }
        public decimal? MinDuration { get; set; }
        public decimal? MaxDuration { get; set; }
    }

    public class OrderAggregationFilters
    {
        public List<Guid>? ShipperIds { get; set; }
        public List<Guid>? CarrierIds { get; set; }
        public List<string>? Statuses { get; set; }
        public List<string>? Routes { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? MinValue { get; set; }
        public decimal? MaxValue { get; set; }
        public decimal? MinWeight { get; set; }
        public decimal? MaxWeight { get; set; }
    }

    #endregion
}
