using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class BrokerCarrierNetwork : BaseEntity
{
    public Guid BrokerId { get; private set; }
    public Guid CarrierId { get; private set; }
    public NetworkRelationshipStatus Status { get; private set; }
    public DateTime EstablishedAt { get; private set; }
    public DateTime? ActivatedAt { get; private set; }
    public DateTime? SuspendedAt { get; private set; }
    public DateTime? TerminatedAt { get; private set; }
    public string? SuspensionReason { get; private set; }
    public string? TerminationReason { get; private set; }
    public PerformanceMetrics PerformanceMetrics { get; private set; }
    public decimal? PreferredRate { get; private set; }
    public int Priority { get; private set; } // 1 = highest priority
    public string? Notes { get; private set; }
    public bool IsExclusive { get; private set; }
    public DateTime? ExclusivityExpiresAt { get; private set; }

    // Contract terms
    public decimal? MinimumRate { get; private set; }
    public decimal? MaximumRate { get; private set; }
    public int? PaymentTermsDays { get; private set; }
    public string? ContractNumber { get; private set; }
    public DateTime? ContractStartDate { get; private set; }
    public DateTime? ContractEndDate { get; private set; }

    // Navigation properties
    public Carrier Carrier { get; private set; } = null!;

    private readonly List<NetworkPerformanceRecord> _performanceRecords = new();
    public IReadOnlyCollection<NetworkPerformanceRecord> PerformanceRecords => _performanceRecords.AsReadOnly();

    // Parameterless constructor for EF Core
    private BrokerCarrierNetwork()
    {
        PerformanceMetrics = new PerformanceMetrics();
    }

    public BrokerCarrierNetwork(
        Guid brokerId,
        Guid carrierId,
        decimal? preferredRate = null,
        int priority = 10,
        bool isExclusive = false,
        DateTime? exclusivityExpiresAt = null,
        decimal? minimumRate = null,
        decimal? maximumRate = null,
        int? paymentTermsDays = null,
        string? contractNumber = null,
        DateTime? contractStartDate = null,
        DateTime? contractEndDate = null,
        string? notes = null)
    {
        BrokerId = brokerId;
        CarrierId = carrierId;
        Status = NetworkRelationshipStatus.Pending;
        EstablishedAt = DateTime.UtcNow;
        PerformanceMetrics = new PerformanceMetrics();
        PreferredRate = preferredRate;
        Priority = priority;
        IsExclusive = isExclusive;
        ExclusivityExpiresAt = exclusivityExpiresAt;
        MinimumRate = minimumRate;
        MaximumRate = maximumRate;
        PaymentTermsDays = paymentTermsDays;
        ContractNumber = contractNumber;
        ContractStartDate = contractStartDate;
        ContractEndDate = contractEndDate;
        Notes = notes;
    }

    public bool IsActive => Status == NetworkRelationshipStatus.Active;
    public bool IsContractValid => !ContractEndDate.HasValue || ContractEndDate.Value > DateTime.UtcNow;
    public bool IsExclusivityActive => IsExclusive && (!ExclusivityExpiresAt.HasValue || ExclusivityExpiresAt.Value > DateTime.UtcNow);

    public void Activate(string? notes = null)
    {
        if (Status != NetworkRelationshipStatus.Pending)
            throw new InvalidOperationException("Can only activate pending relationships");

        Status = NetworkRelationshipStatus.Active;
        ActivatedAt = DateTime.UtcNow;

        if (!string.IsNullOrEmpty(notes))
            Notes = notes;

        SetUpdatedAt();

        AddDomainEvent(new NetworkRelationshipActivatedEvent(BrokerId, CarrierId, Id));
    }

    public void Suspend(string reason, DateTime? suspensionDate = null)
    {
        if (Status != NetworkRelationshipStatus.Active)
            throw new InvalidOperationException("Can only suspend active relationships");

        Status = NetworkRelationshipStatus.Suspended;
        SuspendedAt = suspensionDate ?? DateTime.UtcNow;
        SuspensionReason = reason;
        SetUpdatedAt();

        AddDomainEvent(new NetworkRelationshipSuspendedEvent(BrokerId, CarrierId, Id, reason));
    }

    public void Reactivate(string? notes = null)
    {
        if (Status != NetworkRelationshipStatus.Suspended)
            throw new InvalidOperationException("Can only reactivate suspended relationships");

        Status = NetworkRelationshipStatus.Active;
        SuspendedAt = null;
        SuspensionReason = null;

        if (!string.IsNullOrEmpty(notes))
            Notes = notes;

        SetUpdatedAt();

        AddDomainEvent(new NetworkRelationshipReactivatedEvent(BrokerId, CarrierId, Id));
    }

    public void Terminate(string reason, DateTime? terminationDate = null)
    {
        if (Status == NetworkRelationshipStatus.Terminated)
            throw new InvalidOperationException("Relationship is already terminated");

        Status = NetworkRelationshipStatus.Terminated;
        TerminatedAt = terminationDate ?? DateTime.UtcNow;
        TerminationReason = reason;
        SetUpdatedAt();

        AddDomainEvent(new NetworkRelationshipTerminatedEvent(BrokerId, CarrierId, Id, reason));
    }

    public void UpdatePerformanceMetrics(PerformanceMetrics newMetrics)
    {
        PerformanceMetrics = newMetrics;
        SetUpdatedAt();
    }

    public void UpdatePriority(int newPriority)
    {
        if (newPriority < 1)
            throw new ArgumentException("Priority must be at least 1", nameof(newPriority));

        Priority = newPriority;
        SetUpdatedAt();
    }

    public void UpdateRates(decimal? preferredRate = null, decimal? minimumRate = null, decimal? maximumRate = null)
    {
        if (preferredRate.HasValue && preferredRate.Value < 0)
            throw new ArgumentException("Preferred rate cannot be negative", nameof(preferredRate));

        if (minimumRate.HasValue && minimumRate.Value < 0)
            throw new ArgumentException("Minimum rate cannot be negative", nameof(minimumRate));

        if (maximumRate.HasValue && maximumRate.Value < 0)
            throw new ArgumentException("Maximum rate cannot be negative", nameof(maximumRate));

        PreferredRate = preferredRate;
        MinimumRate = minimumRate;
        MaximumRate = maximumRate;
        SetUpdatedAt();
    }

    public void SetExclusivity(bool isExclusive, DateTime? expiresAt = null)
    {
        IsExclusive = isExclusive;
        ExclusivityExpiresAt = isExclusive ? expiresAt : null;
        SetUpdatedAt();
    }

    public void AddPerformanceRecord(NetworkPerformanceRecord record)
    {
        if (record.NetworkId != Id)
            throw new InvalidOperationException("Performance record must belong to this network relationship");

        _performanceRecords.Add(record);
        SetUpdatedAt();
    }

    public bool IsRateAcceptable(decimal proposedRate)
    {
        if (MinimumRate.HasValue && proposedRate < MinimumRate.Value)
            return false;

        if (MaximumRate.HasValue && proposedRate > MaximumRate.Value)
            return false;

        return true;
    }

    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        // Implementation would be added when integrating with shared domain events
    }
}

// Domain Events
public record NetworkRelationshipActivatedEvent(Guid BrokerId, Guid CarrierId, Guid NetworkId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NetworkRelationshipSuspendedEvent(Guid BrokerId, Guid CarrierId, Guid NetworkId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NetworkRelationshipReactivatedEvent(Guid BrokerId, Guid CarrierId, Guid NetworkId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record NetworkRelationshipTerminatedEvent(Guid BrokerId, Guid CarrierId, Guid NetworkId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
