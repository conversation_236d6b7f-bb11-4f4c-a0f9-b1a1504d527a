using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Result of benchmark operations
/// </summary>
public class BenchmarkResult : ValueObject
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public string BenchmarkType { get; init; }
    public DateTime StartTime { get; init; }
    public DateTime EndTime { get; init; }
    public TimeSpan Duration { get; init; }
    public string Status { get; init; }
    public Dictionary<string, double> Metrics { get; init; }
    public Dictionary<string, object> Metadata { get; init; }
    public string? Environment { get; init; }
    public List<string>? Tags { get; init; }
    public string? ErrorMessage { get; init; }

    public BenchmarkResult(
        Guid id,
        string name,
        string benchmarkType,
        DateTime startTime,
        DateTime endTime,
        string status,
        Dictionary<string, double> metrics,
        Dictionary<string, object> metadata,
        string? environment = null,
        List<string>? tags = null,
        string? errorMessage = null)
    {
        Id = id;
        Name = name;
        BenchmarkType = benchmarkType;
        StartTime = startTime;
        EndTime = endTime;
        Duration = endTime - startTime;
        Status = status;
        Metrics = metrics;
        Metadata = metadata;
        Environment = environment;
        Tags = tags;
        ErrorMessage = errorMessage;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Id;
        yield return Name;
        yield return BenchmarkType;
        yield return StartTime;
        yield return EndTime;
        yield return Status;
        yield return Environment ?? string.Empty;
        yield return ErrorMessage ?? string.Empty;
        
        foreach (var metric in Metrics.OrderBy(x => x.Key))
        {
            yield return metric.Key;
            yield return metric.Value;
        }
        
        foreach (var meta in Metadata.OrderBy(x => x.Key))
        {
            yield return meta.Key;
            yield return meta.Value?.ToString() ?? string.Empty;
        }
        
        if (Tags != null)
        {
            foreach (var tag in Tags.OrderBy(x => x))
            {
                yield return tag;
            }
        }
    }
}
