using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Application.DTOs;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.Queries.GetBid;

public class GetBidQueryHandler : IRequestHandler<GetBidQuery, BidDetailDto?>
{
    private readonly IRfqBidRepository _bidRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetBidQueryHandler> _logger;

    public GetBidQueryHandler(
        IRfqBidRepository bidRepository,
        IMapper mapper,
        ILogger<GetBidQueryHandler> logger)
    {
        _bidRepository = bidRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<BidDetailDto?> Handle(GetBidQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting bid {BidId} for user {UserId}", 
            request.BidId, request.RequestingUserId);

        var bid = await _bidRepository.GetByIdAsync(request.BidId, cancellationToken);
        
        if (bid == null)
        {
            _logger.LogWarning("Bid {BidId} not found", request.BidId);
            return null;
        }

        // Authorization check: Only the broker who submitted the bid or the transport company who owns the RFQ can view it
        if (bid.BrokerId != request.RequestingUserId && bid.Rfq?.TransportCompanyId != request.RequestingUserId)
        {
            _logger.LogWarning("User {UserId} is not authorized to view bid {BidId}", 
                request.RequestingUserId, request.BidId);
            throw new OrderManagementException("You are not authorized to view this bid");
        }

        var bidDto = _mapper.Map<BidDetailDto>(bid);
        
        _logger.LogInformation("Successfully retrieved bid {BidId}", request.BidId);
        
        return bidDto;
    }
}
