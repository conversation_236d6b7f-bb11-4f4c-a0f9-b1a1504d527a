namespace FinancialPayment.Application.DTOs;

public class SettlementDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid TripId { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string SettlementNumber { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string TotalCurrency { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ProcessedAt { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<SettlementDistributionDto> Distributions { get; set; } = new();
}

public class SettlementDistributionDto
{
    public Guid Id { get; set; }
    public Guid SettlementId { get; set; }
    public Guid RecipientId { get; set; }
    public string RecipientRole { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ProcessedAt { get; set; }
    public string? PaymentGatewayTransactionId { get; set; }
    public string? FailureReason { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class RecipientPaymentDto
{
    public Guid RecipientId { get; set; }
    public string RecipientName { get; set; } = string.Empty;
    public string RecipientRole { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public string PaymentMethod { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? ProcessedAt { get; set; }
    public string? TransactionId { get; set; }
    public string? FailureReason { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
