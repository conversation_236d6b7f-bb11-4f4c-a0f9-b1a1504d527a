﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;
using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class StorageLocation : ValueObject
{
    public StorageProvider Provider { get; private set; }
    public string ContainerName { get; private set; }
    public string FilePath { get; private set; }
    public string? CDNUrl { get; private set; }
    public string? Region { get; private set; }
    public Dictionary<string, string> ProviderSpecificSettings { get; private set; }

    private StorageLocation()
    {
        ContainerName = string.Empty;
        FilePath = string.Empty;
        ProviderSpecificSettings = new Dictionary<string, string>();
    }

    public StorageLocation(
        StorageProvider provider,
        string containerName,
        string filePath,
        string? cdnUrl = null,
        string? region = null,
        Dictionary<string, string>? providerSpecificSettings = null)
    {
        if (string.IsNullOrWhiteSpace(containerName))
            throw new ArgumentException("Container name cannot be empty", nameof(containerName));

        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("File path cannot be empty", nameof(filePath));

        Provider = provider;
        ContainerName = containerName;
        FilePath = filePath;
        CDNUrl = cdnUrl;
        Region = region;
        ProviderSpecificSettings = providerSpecificSettings ?? new Dictionary<string, string>();
    }

    public static StorageLocation Create(StorageProvider provider, string containerName, string filePath)
    {
        return new StorageLocation(provider, containerName, filePath);
    }

    public StorageLocation WithCDN(string cdnUrl)
    {
        return new StorageLocation(Provider, ContainerName, FilePath, cdnUrl, Region, ProviderSpecificSettings);
    }

    public StorageLocation WithRegion(string region)
    {
        return new StorageLocation(Provider, ContainerName, FilePath, CDNUrl, region, ProviderSpecificSettings);
    }

    public StorageLocation WithProviderSetting(string key, string value)
    {
        var newSettings = new Dictionary<string, string>(ProviderSpecificSettings)
        {
            [key] = value
        };
        return new StorageLocation(Provider, ContainerName, FilePath, CDNUrl, Region, newSettings);
    }

    public string GetFullPath()
    {
        return Provider switch
        {
            StorageProvider.LocalFileSystem => Path.Combine(ContainerName, FilePath),
            StorageProvider.AzureBlobStorage => $"https://{ContainerName}.blob.core.windows.net/{FilePath}",
            StorageProvider.AmazonS3 => $"https://s3.{Region ?? "us-east-1"}.amazonaws.com/{ContainerName}/{FilePath}",
            StorageProvider.GoogleCloudStorage => $"https://storage.googleapis.com/{ContainerName}/{FilePath}",
            StorageProvider.CDN => CDNUrl ?? FilePath,
            _ => FilePath
        };
    }

    public string GetAccessUrl()
    {
        return !string.IsNullOrWhiteSpace(CDNUrl) ? CDNUrl : GetFullPath();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Provider;
        yield return ContainerName;
        yield return FilePath;
        yield return CDNUrl ?? string.Empty;
        yield return Region ?? string.Empty;

        foreach (var kvp in ProviderSpecificSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }
}


