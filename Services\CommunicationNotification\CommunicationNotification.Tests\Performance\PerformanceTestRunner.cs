using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Performance;

/// <summary>
/// Comprehensive performance test runner for the Communication & Notification Service
/// </summary>
public class PerformanceTestRunner : IClassFixture<TestWebApplicationFactory<Program>>
{
    private readonly TestWebApplicationFactory<Program> _factory;
    private readonly ITestOutputHelper _output;
    private readonly ILogger<PerformanceTestRunner> _logger;
    private readonly PerformanceTestFramework _framework;
    private readonly PerformanceTestRunnerConfiguration _config;

    public PerformanceTestRunner(TestWebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;

        // Setup configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.test.json")
            .AddEnvironmentVariables()
            .Build();

        // Setup services
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().AddDebug());
        services.AddHttpClient();
        services.AddSingleton(configuration);

        var serviceProvider = services.BuildServiceProvider();
        _logger = serviceProvider.GetRequiredService<ILogger<PerformanceTestRunner>>();

        // Setup performance test framework
        var httpClient = _factory.CreateClient();
        _framework = new PerformanceTestFramework(configuration, _logger, httpClient);

        _config = configuration.GetSection("PerformanceTestRunner").Get<PerformanceTestRunnerConfiguration>()
                 ?? new PerformanceTestRunnerConfiguration();
    }

    /// <summary>
    /// Run the complete performance test suite
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Integration")]
    public async Task RunCompletePerformanceTestSuite()
    {
        // Skip if performance testing is disabled
        if (!ShouldRunPerformanceTests())
        {
            _output.WriteLine("Performance tests are disabled. Skipping test suite.");
            return;
        }

        _output.WriteLine("Starting comprehensive performance test suite...");
        _logger.LogInformation("Starting comprehensive performance test suite");

        var context = new PerformanceTestContext
        {
            Environment = "Test",
            Version = "1.0.0",
            StartTime = DateTime.UtcNow,
            Tags = new List<string> { "comprehensive", "automated" }
        };

        try
        {
            // Warm up the system
            await WarmUpSystemAsync();

            // Run the performance test suite
            var results = await _framework.RunPerformanceTestSuiteAsync();

            // Validate results
            ValidatePerformanceResults(results);

            // Generate and save reports
            await GenerateReportsAsync(results, context);

            // Log summary
            LogTestSummary(results);

            // Assert overall success
            Assert.True(results.Success, $"Performance test suite failed: {results.ErrorMessage}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Performance test suite failed with exception");
            _output.WriteLine($"Performance test suite failed: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Run load tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Load")]
    public async Task RunLoadTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running load tests...");
        _logger.LogInformation("Running load tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunLoadTestsAsync();

        _output.WriteLine($"Load Test Results:");
        _output.WriteLine($"- Total Requests: {results.TotalRequests}");
        _output.WriteLine($"- Successful: {results.SuccessfulRequests}");
        _output.WriteLine($"- Failed: {results.FailedRequests}");
        _output.WriteLine($"- Error Rate: {results.ErrorRate:F2}%");
        _output.WriteLine($"- Average Response Time: {results.AverageResponseTime:F2}ms");
        _output.WriteLine($"- Throughput: {results.ThroughputPerSecond:F2} req/s");

        // Validate load test results
        Assert.True(results.Success, "Load tests failed");
        Assert.True(results.ErrorRate < 1.0, $"Error rate too high: {results.ErrorRate:F2}%");
        Assert.True(results.AverageResponseTime < 1000, $"Average response time too high: {results.AverageResponseTime:F2}ms");
    }

    /// <summary>
    /// Run stress tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Stress")]
    public async Task RunStressTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running stress tests...");
        _logger.LogInformation("Running stress tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunStressTestsAsync();

        _output.WriteLine($"Stress Test Results:");
        _output.WriteLine($"- Max RPS: {results.MaxRequestsPerSecond}");
        _output.WriteLine($"- Total Requests: {results.TotalRequests}");
        _output.WriteLine($"- Successful: {results.SuccessfulRequests}");
        _output.WriteLine($"- Failed: {results.FailedRequests}");
        _output.WriteLine($"- Breaking Point: {results.BreakingPoint:F2} req/s");
        _output.WriteLine($"- CPU Usage: {results.ResourceUtilization.CpuUsagePercent:F2}%");
        _output.WriteLine($"- Memory Usage: {results.ResourceUtilization.MemoryUsageMB} MB");

        // Validate stress test results
        Assert.True(results.Success, "Stress tests failed");
        Assert.True(results.ResourceUtilization.CpuUsagePercent < 95, "CPU usage too high under stress");
    }

    /// <summary>
    /// Run endurance tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Endurance")]
    public async Task RunEnduranceTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running endurance tests...");
        _logger.LogInformation("Running endurance tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunEnduranceTestsAsync();

        _output.WriteLine($"Endurance Test Results:");
        _output.WriteLine($"- Duration: {results.Duration}");
        _output.WriteLine($"- Total Requests: {results.TotalRequests}");
        _output.WriteLine($"- Memory Before: {results.MemoryUsageBefore / 1024 / 1024} MB");
        _output.WriteLine($"- Memory After: {results.MemoryUsageAfter / 1024 / 1024} MB");
        _output.WriteLine($"- Memory Leak Detected: {results.MemoryLeakDetected}");
        _output.WriteLine($"- Performance Degradation: {results.PerformanceDegradation:F2}%");

        // Validate endurance test results
        Assert.True(results.Success, "Endurance tests failed");
        Assert.False(results.MemoryLeakDetected, "Memory leak detected during endurance test");
    }

    /// <summary>
    /// Run spike tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Spike")]
    public async Task RunSpikeTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running spike tests...");
        _logger.LogInformation("Running spike tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunSpikeTestsAsync();

        _output.WriteLine($"Spike Test Results:");
        _output.WriteLine($"- Normal Load: {results.NormalLoad} req/s");
        _output.WriteLine($"- Spike Load: {results.SpikeLoad} req/s");
        _output.WriteLine($"- Total Requests: {results.TotalRequests}");
        _output.WriteLine($"- Recovery Time: {results.RecoveryTime}");
        _output.WriteLine($"- System Stability: {results.SystemStability:F2}%");

        // Validate spike test results
        Assert.True(results.Success, "Spike tests failed");
        Assert.True(results.SystemStability > 80, $"System stability too low: {results.SystemStability:F2}%");
    }

    /// <summary>
    /// Run volume tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Volume")]
    public async Task RunVolumeTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running volume tests...");
        _logger.LogInformation("Running volume tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunVolumeTestsAsync();

        _output.WriteLine($"Volume Test Results:");
        _output.WriteLine($"- Large Payload Size: {results.LargePayloadSize} bytes");
        _output.WriteLine($"- Bulk Notification Size: {results.BulkNotificationSize} users");
        _output.WriteLine($"- Total Requests: {results.TotalRequests}");
        _output.WriteLine($"- Average Response Time: {results.AverageResponseTime:F2}ms");
        _output.WriteLine($"- Data Throughput: {results.DataThroughput:F2} MB/s");

        // Validate volume test results
        Assert.True(results.Success, "Volume tests failed");
        Assert.True(results.AverageResponseTime < 5000, $"Response time too high for large payloads: {results.AverageResponseTime:F2}ms");
    }

    /// <summary>
    /// Run concurrent user tests only
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "ConcurrentUser")]
    public async Task RunConcurrentUserTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running concurrent user tests...");
        _logger.LogInformation("Running concurrent user tests");

        await WarmUpSystemAsync();
        var results = await _framework.RunConcurrentUserTestsAsync();

        _output.WriteLine($"Concurrent User Test Results:");
        _output.WriteLine($"- Concurrent Users: {results.ConcurrentUsers}");
        _output.WriteLine($"- Total User Journeys: {results.TotalUserJourneys}");
        _output.WriteLine($"- Successful Journeys: {results.SuccessfulJourneys}");
        _output.WriteLine($"- Failed Journeys: {results.FailedJourneys}");
        _output.WriteLine($"- Average Journey Time: {results.AverageJourneyTime:F2}ms");
        _output.WriteLine($"- User Experience Score: {results.UserExperienceScore:F2}");

        // Validate concurrent user test results
        Assert.True(results.Success, "Concurrent user tests failed");
        Assert.True(results.UserExperienceScore > 75, $"User experience score too low: {results.UserExperienceScore:F2}");
    }

    /// <summary>
    /// Run baseline performance tests
    /// </summary>
    [Fact]
    [Trait("Category", "Performance")]
    [Trait("Type", "Baseline")]
    public async Task RunBaselineTests()
    {
        if (!ShouldRunPerformanceTests()) return;

        _output.WriteLine("Running baseline performance tests...");
        _logger.LogInformation("Running baseline performance tests");

        await WarmUpSystemAsync();

        // Run a lightweight version of each test type to establish baselines
        var loadResults = await _framework.RunLoadTestsAsync();
        var stressResults = await _framework.RunStressTestsAsync();

        // Store baseline metrics
        var baselines = new Dictionary<string, double>
        {
            ["baseline_load_throughput"] = loadResults.ThroughputPerSecond,
            ["baseline_load_response_time"] = loadResults.AverageResponseTime,
            ["baseline_load_error_rate"] = loadResults.ErrorRate,
            ["baseline_stress_max_rps"] = stressResults.MaxRequestsPerSecond,
            ["baseline_stress_breaking_point"] = stressResults.BreakingPoint
        };

        _output.WriteLine("Baseline Metrics:");
        foreach (var baseline in baselines)
        {
            _output.WriteLine($"- {baseline.Key}: {baseline.Value:F2}");
        }

        // Save baselines for future comparison
        await SaveBaselinesAsync(baselines);

        // All baseline tests should pass
        Assert.True(loadResults.Success && stressResults.Success, "Baseline tests failed");
    }

    // Private helper methods
    private bool ShouldRunPerformanceTests()
    {
        var enablePerformanceTests = Environment.GetEnvironmentVariable("ENABLE_PERFORMANCE_TESTS");
        return !string.IsNullOrEmpty(enablePerformanceTests) && 
               bool.TryParse(enablePerformanceTests, out var enabled) && enabled;
    }

    private async Task WarmUpSystemAsync()
    {
        _output.WriteLine("Warming up system...");
        _logger.LogInformation("Warming up system");

        try
        {
            var client = _factory.CreateClient();
            
            // Make a few warm-up requests
            for (int i = 0; i < 5; i++)
            {
                var warmupRequest = new
                {
                    UserId = Guid.NewGuid(),
                    Content = $"Warmup notification {i}",
                    MessageType = "SystemNotification",
                    Priority = "Low"
                };

                await client.PostAsJsonAsync("/api/notifications/send", warmupRequest);
                await Task.Delay(100);
            }

            _output.WriteLine("System warm-up completed");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "System warm-up failed, continuing with tests");
        }
    }

    private void ValidatePerformanceResults(PerformanceTestResults results)
    {
        if (!results.Success)
        {
            throw new InvalidOperationException($"Performance test suite failed: {results.ErrorMessage}");
        }

        // Validate individual test results
        if (results.LoadTestResults != null && !results.LoadTestResults.Success)
        {
            _logger.LogWarning("Load tests failed validation");
        }

        if (results.StressTestResults != null && !results.StressTestResults.Success)
        {
            _logger.LogWarning("Stress tests failed validation");
        }

        if (results.EnduranceTestResults != null && !results.EnduranceTestResults.Success)
        {
            _logger.LogWarning("Endurance tests failed validation");
        }
    }

    private async Task GenerateReportsAsync(PerformanceTestResults results, PerformanceTestContext context)
    {
        if (!_config.EnableReporting) return;

        try
        {
            var reportDirectory = Path.Combine(_config.OutputDirectory, context.TestId);
            Directory.CreateDirectory(reportDirectory);

            // Generate JSON report
            if (_config.ReportFormats.Contains("Json"))
            {
                var jsonReport = JsonSerializer.Serialize(results, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(Path.Combine(reportDirectory, "performance-report.json"), jsonReport);
            }

            // Generate HTML report
            if (_config.ReportFormats.Contains("Html"))
            {
                var htmlReport = GenerateHtmlReport(results);
                await File.WriteAllTextAsync(Path.Combine(reportDirectory, "performance-report.html"), htmlReport);
            }

            // Generate CSV report
            if (_config.ReportFormats.Contains("Csv"))
            {
                var csvReport = GenerateCsvReport(results);
                await File.WriteAllTextAsync(Path.Combine(reportDirectory, "performance-report.csv"), csvReport);
            }

            _output.WriteLine($"Performance reports generated in: {reportDirectory}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate performance reports");
        }
    }

    private void LogTestSummary(PerformanceTestResults results)
    {
        _output.WriteLine("\n=== PERFORMANCE TEST SUMMARY ===");
        _output.WriteLine($"Total Duration: {results.TotalDuration:hh\\:mm\\:ss}");
        _output.WriteLine($"Overall Success: {results.Success}");
        
        if (results.PerformanceReport != null)
        {
            _output.WriteLine($"Overall Score: {results.PerformanceReport.OverallScore:F2}/100");
            _output.WriteLine($"Summary: {results.PerformanceReport.Summary}");
            
            if (results.PerformanceReport.Recommendations.Any())
            {
                _output.WriteLine("\nRecommendations:");
                foreach (var recommendation in results.PerformanceReport.Recommendations)
                {
                    _output.WriteLine($"- {recommendation}");
                }
            }
        }
        
        _output.WriteLine("=================================\n");
    }

    private async Task SaveBaselinesAsync(Dictionary<string, double> baselines)
    {
        try
        {
            var baselineFile = Path.Combine(_config.OutputDirectory, "baselines.json");
            var json = JsonSerializer.Serialize(baselines, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(baselineFile, json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save baseline metrics");
        }
    }

    private string GenerateHtmlReport(PerformanceTestResults results)
    {
        // Simple HTML report generation
        return $@"
<!DOCTYPE html>
<html>
<head>
    <title>Performance Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; }}
        .section {{ margin: 20px 0; }}
        .metric {{ margin: 5px 0; }}
        .success {{ color: green; }}
        .failure {{ color: red; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>Performance Test Report</h1>
        <p>Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
        <p>Duration: {results.TotalDuration:hh\\:mm\\:ss}</p>
        <p class='{(results.Success ? "success" : "failure")}'>Status: {(results.Success ? "PASSED" : "FAILED")}</p>
    </div>
    
    <div class='section'>
        <h2>Test Results Summary</h2>
        {(results.PerformanceReport != null ? $"<p>Overall Score: {results.PerformanceReport.OverallScore:F2}/100</p>" : "")}
        {(results.PerformanceReport != null ? $"<p>{results.PerformanceReport.Summary}</p>" : "")}
    </div>
    
    {(results.LoadTestResults != null ? $@"
    <div class='section'>
        <h2>Load Test Results</h2>
        <div class='metric'>Total Requests: {results.LoadTestResults.TotalRequests}</div>
        <div class='metric'>Success Rate: {(results.LoadTestResults.SuccessfulRequests / (double)results.LoadTestResults.TotalRequests * 100):F2}%</div>
        <div class='metric'>Average Response Time: {results.LoadTestResults.AverageResponseTime:F2}ms</div>
        <div class='metric'>Throughput: {results.LoadTestResults.ThroughputPerSecond:F2} req/s</div>
    </div>" : "")}
    
</body>
</html>";
    }

    private string GenerateCsvReport(PerformanceTestResults results)
    {
        var csv = "TestType,Metric,Value,Unit\n";
        
        if (results.LoadTestResults != null)
        {
            csv += $"Load,TotalRequests,{results.LoadTestResults.TotalRequests},count\n";
            csv += $"Load,SuccessfulRequests,{results.LoadTestResults.SuccessfulRequests},count\n";
            csv += $"Load,ErrorRate,{results.LoadTestResults.ErrorRate:F2},%\n";
            csv += $"Load,AverageResponseTime,{results.LoadTestResults.AverageResponseTime:F2},ms\n";
            csv += $"Load,Throughput,{results.LoadTestResults.ThroughputPerSecond:F2},req/s\n";
        }
        
        if (results.StressTestResults != null)
        {
            csv += $"Stress,MaxRequestsPerSecond,{results.StressTestResults.MaxRequestsPerSecond},req/s\n";
            csv += $"Stress,BreakingPoint,{results.StressTestResults.BreakingPoint:F2},req/s\n";
            csv += $"Stress,CpuUsage,{results.StressTestResults.ResourceUtilization.CpuUsagePercent:F2},%\n";
        }
        
        return csv;
    }
}
