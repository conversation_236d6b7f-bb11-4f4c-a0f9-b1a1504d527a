using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Mobile session entity for tracking mobile app sessions
/// </summary>
public class MobileSession : BaseEntity
{
    public Guid UserId { get; private set; }
    public string AppId { get; private set; }
    public string SessionId { get; private set; }
    public string DeviceId { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public string DeviceInfo { get; private set; } // JSON string
    public string AppVersion { get; private set; }
    public string SessionProperties { get; private set; } // JSON string

    private MobileSession()
    {
        AppId = string.Empty;
        SessionId = string.Empty;
        DeviceId = string.Empty;
        DeviceInfo = string.Empty;
        AppVersion = string.Empty;
        SessionProperties = string.Empty;
    }

    public MobileSession(
        Guid userId,
        string appId,
        string sessionId,
        string deviceId,
        DateTime startTime,
        string deviceInfo,
        string appVersion,
        string sessionProperties)
    {
        UserId = userId;
        AppId = appId ?? throw new ArgumentNullException(nameof(appId));
        SessionId = sessionId ?? throw new ArgumentNullException(nameof(sessionId));
        DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
        StartTime = startTime;
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        SessionProperties = sessionProperties ?? throw new ArgumentNullException(nameof(sessionProperties));
    }

    public void EndSession(DateTime endTime)
    {
        EndTime = endTime;
        Duration = endTime - StartTime;
        SetUpdatedAt();
    }

    public void UpdateSessionProperties(string sessionProperties)
    {
        SessionProperties = sessionProperties ?? throw new ArgumentNullException(nameof(sessionProperties));
        SetUpdatedAt();
    }

    public bool IsActive => EndTime == null;
}
