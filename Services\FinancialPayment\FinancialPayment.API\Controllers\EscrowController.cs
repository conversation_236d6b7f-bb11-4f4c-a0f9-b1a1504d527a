using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using FinancialPayment.Application.Commands.CreateEscrowAccount;
using FinancialPayment.Application.Commands.FundEscrowAccount;
using FinancialPayment.Application.Commands.ReleaseEscrowFunds;
using FinancialPayment.Application.Commands.CompleteMilestone;
using FinancialPayment.Application.Commands.ProcessMilestonePayment;
using FinancialPayment.Application.Queries.GetEscrowAccount;
using FinancialPayment.Application.Queries.GetEscrowAccountByOrder;
using FinancialPayment.Application.Services;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class EscrowController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IEscrowVisibilityService _escrowVisibilityService;
    private readonly ILogger<EscrowController> _logger;

    public EscrowController(
        IMediator mediator,
        IEscrowVisibilityService escrowVisibilityService,
        ILogger<EscrowController> logger)
    {
        _mediator = mediator;
        _escrowVisibilityService = escrowVisibilityService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new escrow account for an order
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<ActionResult<Guid>> CreateEscrowAccount([FromBody] CreateEscrowAccountCommand command)
    {
        try
        {
            var escrowAccountId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetEscrowAccount), new { id = escrowAccountId }, escrowAccountId);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating escrow account");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating escrow account");
            return StatusCode(500, "An error occurred while creating the escrow account");
        }
    }

    /// <summary>
    /// Fund an escrow account
    /// </summary>
    [HttpPost("{id}/fund")]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult<bool>> FundEscrowAccount(Guid id, [FromBody] FundEscrowAccountRequest request)
    {
        try
        {
            var command = new FundEscrowAccountCommand
            {
                EscrowAccountId = id,
                Amount = request.Amount,
                PaymentMethodId = request.PaymentMethodId,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while funding escrow account {EscrowAccountId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error funding escrow account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while funding the escrow account");
        }
    }

    /// <summary>
    /// Get escrow account by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,TransportCompany,Broker,Carrier")]
    public async Task<ActionResult<EscrowAccountDto>> GetEscrowAccount(Guid id)
    {
        try
        {
            var query = new GetEscrowAccountQuery { EscrowAccountId = id };
            var result = await _mediator.Send(query);

            if (result == null)
                return NotFound($"Escrow account {id} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving escrow account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while retrieving the escrow account");
        }
    }

    /// <summary>
    /// Get escrow account by order ID
    /// </summary>
    [HttpGet("order/{orderId}")]
    [Authorize(Roles = "Admin,TransportCompany,Broker,Carrier")]
    public async Task<ActionResult<EscrowAccountDto>> GetEscrowAccountByOrder(Guid orderId)
    {
        try
        {
            var query = new GetEscrowAccountByOrderQuery(orderId);
            var result = await _mediator.Send(query);

            if (result == null)
                return NotFound($"Escrow account for order {orderId} not found");

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving escrow account for order {OrderId}", orderId);
            return StatusCode(500, "An error occurred while retrieving the escrow account");
        }
    }

    /// <summary>
    /// Release escrow funds
    /// </summary>
    [HttpPost("{id}/release")]
    [Authorize(Roles = "Admin,Broker")]
    public async Task<ActionResult<bool>> ReleaseEscrowFunds(Guid id, [FromBody] ReleaseEscrowFundsRequest request)
    {
        try
        {
            var command = new ReleaseEscrowFundsCommand
            {
                EscrowAccountId = id,
                ReleaseReason = request.Reason,
                Notes = request.Notes
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while releasing escrow funds for account {EscrowAccountId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing escrow funds for account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while releasing escrow funds");
        }
    }

    /// <summary>
    /// Refund escrow funds
    /// </summary>
    [HttpPost("{id}/refund")]
    [Authorize(Roles = "Admin,TransportCompany")]
    public async Task<ActionResult<bool>> RefundEscrowFunds(Guid id, [FromBody] RefundEscrowFundsRequest request)
    {
        try
        {
            // Implementation would go here
            // This would involve creating a RefundEscrowFundsCommand and handler
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refunding escrow funds for account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while refunding escrow funds");
        }
    }

    /// <summary>
    /// Complete a milestone for an escrow account
    /// </summary>
    [HttpPost("{id:guid}/milestones/{milestoneId:guid}/complete")]
    [Authorize(Roles = "Admin,TransportCompany,Broker")]
    public async Task<ActionResult> CompleteMilestone(Guid id, Guid milestoneId, [FromBody] CompleteMilestoneRequest request)
    {
        try
        {
            var command = new CompleteMilestoneCommand
            {
                EscrowAccountId = id,
                MilestoneId = milestoneId,
                CompletionNotes = request.CompletionNotes,
                CompletedBy = request.CompletedBy,
                CompletionDate = request.CompletionDate,
                AutoReleaseFunds = request.AutoReleaseFunds,
                Verifications = request.Verifications
            };

            var result = await _mediator.Send(command);

            if (result)
                return Ok(new { Message = "Milestone completed successfully" });

            return BadRequest("Failed to complete milestone");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while completing milestone {MilestoneId}", milestoneId);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while completing milestone {MilestoneId}", milestoneId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing milestone {MilestoneId}", milestoneId);
            return StatusCode(500, "An error occurred while completing the milestone");
        }
    }

    /// <summary>
    /// Process milestone payments for a trip
    /// </summary>
    [HttpPost("milestones/process")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<ProcessMilestonePaymentResponse>> ProcessMilestonePayments([FromBody] ProcessMilestonePaymentRequest request)
    {
        try
        {
            var command = new ProcessMilestonePaymentCommand
            {
                TripId = request.TripId,
                OrderId = request.OrderId,
                MilestoneType = request.MilestoneType,
                TriggerEvent = request.TriggerEvent,
                EventData = request.EventData,
                TriggeredBy = request.TriggeredBy,
                EventTimestamp = request.EventTimestamp,
                ForceProcess = request.ForceProcess
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing milestone payments for trip {TripId}", request.TripId);
            return StatusCode(500, "An error occurred while processing milestone payments");
        }
    }

    /// <summary>
    /// Get escrow dashboard for user
    /// </summary>
    [HttpGet("dashboard")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowDashboardDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowDashboard()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();

            var dashboard = await _escrowVisibilityService.GetEscrowDashboardAsync(userId, userRole);
            return Ok(dashboard);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow dashboard");
            return StatusCode(500, "An error occurred while retrieving escrow dashboard");
        }
    }

    /// <summary>
    /// Get detailed escrow account information
    /// </summary>
    [HttpGet("{escrowAccountId:guid}/details")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowAccountDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowAccountDetails(Guid escrowAccountId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var details = await _escrowVisibilityService.GetEscrowAccountDetailsAsync(escrowAccountId, userId);
            return Ok(details);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow account details for {EscrowAccountId}", escrowAccountId);
            return StatusCode(500, "An error occurred while retrieving escrow account details");
        }
    }

    /// <summary>
    /// Get escrow transaction history
    /// </summary>
    [HttpGet("{escrowAccountId:guid}/transactions")]
    [Authorize]
    [ProducesResponseType(typeof(List<EscrowTransactionDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowTransactionHistory(Guid escrowAccountId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var transactions = await _escrowVisibilityService.GetEscrowTransactionHistoryAsync(escrowAccountId, userId);
            return Ok(transactions);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction history for escrow account {EscrowAccountId}", escrowAccountId);
            return StatusCode(500, "An error occurred while retrieving transaction history");
        }
    }

    /// <summary>
    /// Get escrow balance information
    /// </summary>
    [HttpGet("{escrowAccountId:guid}/balance")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowBalanceDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowBalance(Guid escrowAccountId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var balance = await _escrowVisibilityService.GetEscrowBalanceAsync(escrowAccountId, userId);
            return Ok(balance);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow balance for account {EscrowAccountId}", escrowAccountId);
            return StatusCode(500, "An error occurred while retrieving escrow balance");
        }
    }

    /// <summary>
    /// Get escrow milestones
    /// </summary>
    [HttpGet("{escrowAccountId:guid}/milestones")]
    [Authorize]
    [ProducesResponseType(typeof(List<EscrowMilestoneDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowMilestones(Guid escrowAccountId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var milestones = await _escrowVisibilityService.GetEscrowMilestonesAsync(escrowAccountId, userId);
            return Ok(milestones);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting milestones for escrow account {EscrowAccountId}", escrowAccountId);
            return StatusCode(500, "An error occurred while retrieving escrow milestones");
        }
    }

    /// <summary>
    /// Get escrow release conditions
    /// </summary>
    [HttpGet("{escrowAccountId:guid}/release-conditions")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowReleaseConditionsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetReleaseConditions(Guid escrowAccountId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var conditions = await _escrowVisibilityService.GetReleaseConditionsAsync(escrowAccountId, userId);
            return Ok(conditions);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting release conditions for escrow account {EscrowAccountId}", escrowAccountId);
            return StatusCode(500, "An error occurred while retrieving release conditions");
        }
    }

    /// <summary>
    /// Get escrow alerts for current user
    /// </summary>
    [HttpGet("alerts")]
    [Authorize]
    [ProducesResponseType(typeof(List<EscrowAlertDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowAlerts()
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();
            var alerts = await _escrowVisibilityService.GetEscrowAlertsAsync(userId, userRole);
            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow alerts");
            return StatusCode(500, "An error occurred while retrieving escrow alerts");
        }
    }

    /// <summary>
    /// Get escrow analytics
    /// </summary>
    [HttpGet("analytics")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowAnalytics([FromQuery] GetEscrowAnalyticsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userRole = GetCurrentUserRole();
            var dateRange = new DateRangeDto
            {
                StartDate = request.StartDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = request.EndDate ?? DateTime.UtcNow
            };

            var analytics = await _escrowVisibilityService.GetEscrowAnalyticsAsync(userId, userRole, dateRange);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow analytics");
            return StatusCode(500, "An error occurred while retrieving escrow analytics");
        }
    }

    // ===== SHIPPER PORTAL ENHANCED FEATURES =====

    /// <summary>
    /// Get escrow account with milestones and enhanced details
    /// </summary>
    [HttpGet("{id}/enhanced")]
    [Authorize]
    [ProducesResponseType(typeof(EscrowAccountDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetEscrowAccountWithMilestones(Guid id, [FromQuery] GetEscrowAccountEnhancedRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new FinancialPayment.Application.Queries.GetEscrowAccountWithMilestones.GetEscrowAccountWithMilestonesQuery
            {
                EscrowAccountId = id,
                RequestingUserId = userId,
                IncludeTransactions = request.IncludeTransactions,
                IncludeMilestoneDocuments = request.IncludeMilestoneDocuments,
                IncludeDisputeInfo = request.IncludeDisputeInfo,
                CalculateSummary = request.CalculateSummary
            };

            var result = await _mediator.Send(query);
            if (result == null)
            {
                return NotFound($"Escrow account {id} not found");
            }

            return Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enhanced escrow account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while retrieving escrow account details");
        }
    }

    /// <summary>
    /// Create payment milestone for escrow account
    /// </summary>
    [HttpPost("{id}/milestones")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreatePaymentMilestone(Guid id, [FromBody] CreatePaymentMilestoneRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new FinancialPayment.Application.Commands.CreatePaymentMilestone.CreatePaymentMilestoneCommand
            {
                EscrowAccountId = id,
                Name = request.Name,
                Description = request.Description,
                Amount = request.Amount,
                PayoutPercentage = request.PayoutPercentage,
                SequenceNumber = request.SequenceNumber,
                DueDate = request.DueDate,
                IsRequired = request.IsRequired,
                RequiresApproval = request.RequiresApproval,
                TripLegReference = request.TripLegReference,
                OrderReference = request.OrderReference,
                CompletionCriteria = request.CompletionCriteria,
                PayoutRules = request.PayoutRules,
                RequestingUserId = userId
            };

            var milestoneId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetPaymentMilestone), new { id = milestoneId },
                new { milestoneId, message = "Payment milestone created successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this escrow account");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment milestone for escrow account {EscrowAccountId}", id);
            return StatusCode(500, "An error occurred while creating the payment milestone");
        }
    }

    /// <summary>
    /// Release milestone payment
    /// </summary>
    [HttpPost("milestones/{milestoneId}/release")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ReleaseMilestonePayment(Guid milestoneId, [FromBody] ReleaseMilestonePaymentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var command = new FinancialPayment.Application.Commands.ReleaseMilestonePayment.ReleaseMilestonePaymentCommand
            {
                MilestoneId = milestoneId,
                EscrowAccountId = request.EscrowAccountId,
                CompletionNotes = request.CompletionNotes,
                Recipients = request.Recipients,
                AutoCreateSettlement = request.AutoCreateSettlement,
                NotifyRecipients = request.NotifyRecipients,
                PaymentMethodId = request.PaymentMethodId,
                RequestingUserId = userId,
                Metadata = request.Metadata
            };

            var result = await _mediator.Send(command);
            if (result)
            {
                return Ok(new { message = "Milestone payment released successfully" });
            }

            return BadRequest("Failed to release milestone payment");
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid("Access denied to this milestone");
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing milestone payment {MilestoneId}", milestoneId);
            return StatusCode(500, "An error occurred while releasing milestone payment");
        }
    }

    /// <summary>
    /// Get payment milestone details (placeholder for future implementation)
    /// </summary>
    [HttpGet("milestones/{id}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetPaymentMilestone(Guid id)
    {
        // This would be implemented with a proper query handler
        return Ok(new { id, message = "Payment milestone details endpoint - to be implemented" });
    }

    // Helper methods
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null ? Guid.Parse(userIdClaim.Value) : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst("role") ?? User.FindFirst(ClaimTypes.Role);
        return roleClaim?.Value ?? "User";
    }
}

public class GetEscrowAnalyticsRequest
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class FundEscrowAccountRequest
{
    public CreateMoneyDto Amount { get; set; } = new();
    public string PaymentMethodId { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class ReleaseEscrowFundsRequest
{
    public string Reason { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class RefundEscrowFundsRequest
{
    public CreateMoneyDto Amount { get; set; } = new();
    public string Reason { get; set; } = string.Empty;
}

public class CompleteMilestoneRequest
{
    public string CompletionNotes { get; set; } = string.Empty;
    public Guid CompletedBy { get; set; }
    public DateTime? CompletionDate { get; set; }
    public bool AutoReleaseFunds { get; set; } = true;
    public List<MilestoneVerificationDto> Verifications { get; set; } = new();
}

public class ProcessMilestonePaymentRequest
{
    public Guid TripId { get; set; }
    public Guid OrderId { get; set; }
    public string MilestoneType { get; set; } = string.Empty;
    public string TriggerEvent { get; set; } = string.Empty;
    public Dictionary<string, object> EventData { get; set; } = new();
    public Guid TriggeredBy { get; set; }
    public DateTime? EventTimestamp { get; set; }
    public bool ForceProcess { get; set; } = false;
}

// ===== SHIPPER PORTAL REQUEST DTOs =====

public class GetEscrowAccountEnhancedRequest
{
    public bool IncludeTransactions { get; set; } = true;
    public bool IncludeMilestoneDocuments { get; set; } = true;
    public bool IncludeDisputeInfo { get; set; } = true;
    public bool CalculateSummary { get; set; } = true;
}

public class CreatePaymentMilestoneRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public MoneyDto Amount { get; set; } = new();
    public decimal PayoutPercentage { get; set; }
    public int SequenceNumber { get; set; }
    public DateTime? DueDate { get; set; }
    public bool IsRequired { get; set; } = true;
    public bool RequiresApproval { get; set; } = false;
    public string? TripLegReference { get; set; }
    public string? OrderReference { get; set; }
    public List<string> CompletionCriteria { get; set; } = new();
    public List<PayoutRuleRequest> PayoutRules { get; set; } = new();
}

public class PayoutRuleRequest
{
    public string RuleName { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public decimal RuleValue { get; set; }
    public int Priority { get; set; } = 1;
    public string? Condition { get; set; }
    public string? Description { get; set; }
}

public class ReleaseMilestonePaymentRequest
{
    public Guid EscrowAccountId { get; set; }
    public string CompletionNotes { get; set; } = string.Empty;
    public List<RecipientPaymentDto> Recipients { get; set; } = new();
    public bool AutoCreateSettlement { get; set; } = true;
    public bool NotifyRecipients { get; set; } = true;
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class RecipientPaymentDto
{
    public Guid RecipientId { get; set; }
    public string RecipientType { get; set; } = string.Empty;
    public MoneyDto Amount { get; set; } = new();
    public string PaymentReason { get; set; } = string.Empty;
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
