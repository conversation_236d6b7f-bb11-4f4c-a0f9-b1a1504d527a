using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class FraudDetectionService : IFraudDetectionService
{
    private readonly IFraudDetectionRuleRepository _ruleRepository;
    private readonly IFraudAssessmentRepository _assessmentRepository;
    private readonly IFraudPatternAnalyzer _patternAnalyzer;
    private readonly IBlacklistService _blacklistService;
    private readonly ILogger<FraudDetectionService> _logger;
    private readonly FraudDetectionConfiguration _configuration;

    public FraudDetectionService(
        IFraudDetectionRuleRepository ruleRepository,
        IFraudAssessmentRepository assessmentRepository,
        IFraudPatternAnalyzer patternAnalyzer,
        IBlacklistService blacklistService,
        ILogger<FraudDetectionService> logger,
        IOptions<FraudDetectionConfiguration> configuration)
    {
        _ruleRepository = ruleRepository;
        _assessmentRepository = assessmentRepository;
        _patternAnalyzer = patternAnalyzer;
        _blacklistService = blacklistService;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<FraudAssessment> AssessTransactionAsync(FraudAssessmentRequest request)
    {
        _logger.LogInformation("Starting fraud assessment for transaction {TransactionId}", request.TransactionId);

        try
        {
            // Create fraud assessment
            var assessment = new FraudAssessment(
                request.TransactionId,
                request.UserId,
                request.TransactionAmount,
                request.PaymentMethod,
                request.IpAddress,
                request.DeviceFingerprint,
                request.UserAgent,
                request.BillingAddress,
                request.ShippingAddress);

            // Check blacklists first
            await CheckBlacklistsAsync(assessment, request);

            // Get active fraud detection rules
            var activeRules = await _ruleRepository.GetActiveRulesAsync();
            
            // Prepare context for rule evaluation
            var context = PrepareEvaluationContext(request);

            // Evaluate each rule
            foreach (var rule in activeRules.OrderBy(r => r.Priority))
            {
                var isTriggered = await EvaluateRuleAsync(rule, context, request);
                assessment.AddRuleResult(rule, isTriggered, 
                    isTriggered ? $"Rule triggered: {rule.Description}" : null);

                if (isTriggered)
                {
                    _logger.LogWarning("Fraud rule triggered: {RuleName} for transaction {TransactionId}",
                        rule.Name, request.TransactionId);
                }
            }

            // Perform pattern analysis
            if (_configuration.EnablePatternAnalysis && request.UserId.HasValue)
            {
                await AnalyzePatternsAsync(assessment, request);
            }

            // Complete assessment
            assessment.CompleteAssessment($"Assessment completed with {assessment.RuleResults.Count(r => r.IsTriggered)} triggered rules");

            // Save assessment
            await _assessmentRepository.AddAsync(assessment);

            _logger.LogInformation("Fraud assessment completed for transaction {TransactionId}. Risk Level: {RiskLevel}, Score: {Score}",
                request.TransactionId, assessment.RiskLevel, assessment.TotalRiskScore);

            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during fraud assessment for transaction {TransactionId}", request.TransactionId);
            throw;
        }
    }

    public async Task<FraudAssessment> ReassessTransactionAsync(Guid assessmentId)
    {
        _logger.LogInformation("Reassessing fraud assessment {AssessmentId}", assessmentId);

        try
        {
            var existingAssessment = await _assessmentRepository.GetByIdAsync(assessmentId);
            if (existingAssessment == null)
            {
                throw new InvalidOperationException($"Fraud assessment {assessmentId} not found");
            }

            // Create new assessment request from existing data
            var request = new FraudAssessmentRequest
            {
                TransactionId = existingAssessment.TransactionId,
                UserId = existingAssessment.UserId,
                TransactionAmount = existingAssessment.TransactionAmount,
                PaymentMethod = existingAssessment.PaymentMethod,
                IpAddress = existingAssessment.IpAddress,
                DeviceFingerprint = existingAssessment.DeviceFingerprint,
                UserAgent = existingAssessment.UserAgent,
                BillingAddress = existingAssessment.BillingAddress,
                ShippingAddress = existingAssessment.ShippingAddress
            };

            return await AssessTransactionAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reassessing fraud assessment {AssessmentId}", assessmentId);
            throw;
        }
    }

    public async Task<List<FraudAssessment>> GetHighRiskTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var highRisk = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.High, fromDate, toDate);
            var critical = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.Critical, fromDate, toDate);
            
            return highRisk.Concat(critical).OrderByDescending(a => a.AssessedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting high risk transactions");
            throw;
        }
    }

    public async Task<List<FraudAssessment>> GetPendingReviewsAsync()
    {
        try
        {
            return await _assessmentRepository.GetByStatusAsync(FraudAssessmentStatus.Pending);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending reviews");
            throw;
        }
    }

    public async Task<bool> ReviewAssessmentAsync(Guid assessmentId, Guid reviewedBy, string reviewNotes, FraudAction finalAction)
    {
        try
        {
            var assessment = await _assessmentRepository.GetByIdAsync(assessmentId);
            if (assessment == null)
            {
                return false;
            }

            assessment.MarkAsReviewed(reviewedBy, reviewNotes);
            await _assessmentRepository.UpdateAsync(assessment);

            _logger.LogInformation("Fraud assessment {AssessmentId} reviewed by {ReviewedBy} with action {Action}",
                assessmentId, reviewedBy, finalAction);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reviewing assessment {AssessmentId}", assessmentId);
            return false;
        }
    }

    public async Task<FraudDetectionStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var lowRisk = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.Low, from, to);
            var mediumRisk = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.Medium, from, to);
            var highRisk = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.High, from, to);
            var criticalRisk = await _assessmentRepository.GetByRiskLevelAsync(FraudRiskLevel.Critical, from, to);

            var totalAssessments = lowRisk.Count + mediumRisk.Count + highRisk.Count + criticalRisk.Count;
            var blockedTransactions = highRisk.Concat(criticalRisk)
                .Count(a => a.RecommendedAction == FraudAction.Block);

            var totalBlockedAmount = highRisk.Concat(criticalRisk)
                .Where(a => a.RecommendedAction == FraudAction.Block)
                .Aggregate(Money.Zero("INR"), (sum, a) => sum + a.TransactionAmount);

            return new FraudDetectionStatistics
            {
                TotalAssessments = totalAssessments,
                LowRiskCount = lowRisk.Count,
                MediumRiskCount = mediumRisk.Count,
                HighRiskCount = highRisk.Count,
                CriticalRiskCount = criticalRisk.Count,
                BlockedTransactions = blockedTransactions,
                TotalBlockedAmount = totalBlockedAmount,
                PeriodStart = from,
                PeriodEnd = to
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting fraud detection statistics");
            throw;
        }
    }

    public async Task<List<FraudDetectionRule>> GetActiveRulesAsync()
    {
        try
        {
            return await _ruleRepository.GetActiveRulesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active fraud rules");
            throw;
        }
    }

    public async Task<bool> UpdateRuleAsync(Guid ruleId, int newRiskScore, bool isActive)
    {
        try
        {
            var rule = await _ruleRepository.GetByIdAsync(ruleId);
            if (rule == null)
            {
                return false;
            }

            rule.UpdateRiskScore(newRiskScore);
            if (isActive)
                rule.Activate();
            else
                rule.Deactivate();

            await _ruleRepository.UpdateAsync(rule);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating fraud rule {RuleId}", ruleId);
            return false;
        }
    }

    private async Task CheckBlacklistsAsync(FraudAssessment assessment, FraudAssessmentRequest request)
    {
        if (!_configuration.EnableBlacklist) return;

        // Check user blacklist
        if (request.UserId.HasValue && await _blacklistService.IsUserBlacklistedAsync(request.UserId.Value))
        {
            assessment.AddAlert(FraudAlertType.BlacklistedUser, "User is blacklisted", FraudAlertSeverity.Critical);
        }

        // Check IP blacklist
        if (await _blacklistService.IsIpAddressBlacklistedAsync(request.IpAddress))
        {
            assessment.AddAlert(FraudAlertType.SuspiciousLocation, "IP address is blacklisted", FraudAlertSeverity.High);
        }

        // Check device blacklist
        if (!string.IsNullOrEmpty(request.DeviceFingerprint) && 
            await _blacklistService.IsDeviceBlacklistedAsync(request.DeviceFingerprint))
        {
            assessment.AddAlert(FraudAlertType.DeviceMismatch, "Device is blacklisted", FraudAlertSeverity.High);
        }

        // Check email blacklist
        if (!string.IsNullOrEmpty(request.Email) && 
            await _blacklistService.IsEmailBlacklistedAsync(request.Email))
        {
            assessment.AddAlert(FraudAlertType.BlacklistedUser, "Email is blacklisted", FraudAlertSeverity.High);
        }
    }

    private Dictionary<string, object> PrepareEvaluationContext(FraudAssessmentRequest request)
    {
        return new Dictionary<string, object>
        {
            { "transaction_amount", request.TransactionAmount.Amount },
            { "payment_method", request.PaymentMethod },
            { "ip_address", request.IpAddress },
            { "device_fingerprint", request.DeviceFingerprint ?? string.Empty },
            { "user_agent", request.UserAgent ?? string.Empty },
            { "user_id", request.UserId?.ToString() ?? string.Empty },
            { "transaction_hour", DateTime.UtcNow.Hour },
            { "transaction_day_of_week", DateTime.UtcNow.DayOfWeek.ToString() }
        };
    }

    private async Task<bool> EvaluateRuleAsync(FraudDetectionRule rule, Dictionary<string, object> context, FraudAssessmentRequest request)
    {
        try
        {
            // Evaluate rule conditions
            if (!rule.EvaluateConditions(context))
            {
                return false;
            }

            // Evaluate specific rule types
            return rule.RuleType switch
            {
                FraudRuleType.VelocityCheck => await EvaluateVelocityRuleAsync(request),
                FraudRuleType.AmountThreshold => EvaluateAmountThresholdRule(rule, request),
                FraudRuleType.GeolocationCheck => await EvaluateGeolocationRuleAsync(request),
                FraudRuleType.DeviceFingerprint => await EvaluateDeviceRuleAsync(request),
                FraudRuleType.BehaviorPattern => await EvaluateBehaviorPatternRuleAsync(request),
                FraudRuleType.BlacklistCheck => await EvaluateBlacklistRuleAsync(request),
                FraudRuleType.CustomRule => EvaluateCustomRule(rule, context),
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating fraud rule {RuleId}", rule.Id);
            return false;
        }
    }

    private async Task<bool> EvaluateVelocityRuleAsync(FraudAssessmentRequest request)
    {
        if (!request.UserId.HasValue) return false;

        var timeWindow = TimeSpan.FromMinutes(_configuration.VelocityCheckWindowMinutes);
        return await _patternAnalyzer.IsVelocityExceededAsync(request.UserId.Value, request.TransactionAmount, timeWindow);
    }

    private bool EvaluateAmountThresholdRule(FraudDetectionRule rule, FraudAssessmentRequest request)
    {
        // Parse threshold from rule expression
        if (decimal.TryParse(rule.RuleExpression, out var threshold))
        {
            return request.TransactionAmount.Amount > threshold;
        }
        return false;
    }

    private async Task<bool> EvaluateGeolocationRuleAsync(FraudAssessmentRequest request)
    {
        return await _patternAnalyzer.IsLocationSuspiciousAsync(request.IpAddress, request.UserId);
    }

    private async Task<bool> EvaluateDeviceRuleAsync(FraudAssessmentRequest request)
    {
        if (string.IsNullOrEmpty(request.DeviceFingerprint)) return false;
        return await _patternAnalyzer.IsDeviceSuspiciousAsync(request.DeviceFingerprint, request.UserId);
    }

    private async Task<bool> EvaluateBehaviorPatternRuleAsync(FraudAssessmentRequest request)
    {
        if (!request.UserId.HasValue) return false;

        var patterns = await _patternAnalyzer.AnalyzeUserPatternsAsync(request.UserId.Value);
        return patterns.Any(p => p.RiskScore > 70);
    }

    private async Task<bool> EvaluateBlacklistRuleAsync(FraudAssessmentRequest request)
    {
        if (request.UserId.HasValue && await _blacklistService.IsUserBlacklistedAsync(request.UserId.Value))
            return true;

        if (await _blacklistService.IsIpAddressBlacklistedAsync(request.IpAddress))
            return true;

        if (!string.IsNullOrEmpty(request.DeviceFingerprint) && 
            await _blacklistService.IsDeviceBlacklistedAsync(request.DeviceFingerprint))
            return true;

        return false;
    }

    private bool EvaluateCustomRule(FraudDetectionRule rule, Dictionary<string, object> context)
    {
        // Simple expression evaluation - in a real implementation, you might use a more sophisticated expression engine
        try
        {
            // For demo purposes, just check if the rule expression matches any context value
            return context.Values.Any(v => v.ToString()?.Contains(rule.RuleExpression, StringComparison.OrdinalIgnoreCase) == true);
        }
        catch
        {
            return false;
        }
    }

    private async Task AnalyzePatternsAsync(FraudAssessment assessment, FraudAssessmentRequest request)
    {
        try
        {
            if (!request.UserId.HasValue) return;

            var patterns = await _patternAnalyzer.AnalyzeUserPatternsAsync(request.UserId.Value);
            
            foreach (var pattern in patterns.Where(p => p.RiskScore > 50))
            {
                var severity = pattern.RiskScore switch
                {
                    > 80 => FraudAlertSeverity.Critical,
                    > 60 => FraudAlertSeverity.High,
                    _ => FraudAlertSeverity.Medium
                };

                assessment.AddAlert(FraudAlertType.UnusualPattern, pattern.Description, severity);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing patterns for user {UserId}", request.UserId);
        }
    }
}
