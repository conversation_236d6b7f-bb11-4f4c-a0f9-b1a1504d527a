using System;
using MediatR;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.UserProfiles.Commands.CreateUserProfile
{
    public class CreateUserProfileCommand : IRequest<Guid>
    {
        public Guid UserId { get; set; }
        public UserType UserType { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
    }
}
