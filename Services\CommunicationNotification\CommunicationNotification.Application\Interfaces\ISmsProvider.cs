using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Interface for SMS notification providers
/// </summary>
public interface ISmsProvider : INotificationProvider
{
    /// <summary>
    /// Send SMS with delivery receipt tracking
    /// </summary>
    /// <param name="phoneNumber">Recipient phone number</param>
    /// <param name="message">SMS message content</param>
    /// <param name="enableDeliveryReceipt">Enable delivery receipt tracking</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS send result</returns>
    Task<SmsResult> SendSmsAsync(
        string phoneNumber,
        string message,
        bool enableDeliveryReceipt = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk SMS messages
    /// </summary>
    /// <param name="recipients">List of phone numbers</param>
    /// <param name="message">SMS message content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk SMS results</returns>
    Task<BulkSmsResult> SendBulkSmsAsync(
        List<string> recipients,
        string message,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS delivery report
    /// </summary>
    /// <param name="messageId">SMS message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery report</returns>
    Task<SmsDeliveryReport> GetDeliveryReportAsync(
        string messageId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// SMS send result
/// </summary>
public class SmsResult
{
    public bool IsSuccess { get; set; }
    public string? MessageId { get; set; }
    public string? ErrorMessage { get; set; }
    public decimal? Cost { get; set; }
    public int MessageParts { get; set; } = 1;
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static SmsResult Success(string messageId, decimal? cost = null, int messageParts = 1)
    {
        return new SmsResult
        {
            IsSuccess = true,
            MessageId = messageId,
            Cost = cost,
            MessageParts = messageParts
        };
    }

    public static SmsResult Failure(string errorMessage)
    {
        return new SmsResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// Bulk SMS result
/// </summary>
public class BulkSmsResult
{
    public int TotalMessages { get; set; }
    public int SuccessfulMessages { get; set; }
    public int FailedMessages { get; set; }
    public List<SmsResult> Results { get; set; } = new();
    public decimal? TotalCost { get; set; }

    public bool IsPartialSuccess => SuccessfulMessages > 0 && FailedMessages > 0;
    public bool IsCompleteSuccess => SuccessfulMessages == TotalMessages;
    public bool IsCompleteFailure => FailedMessages == TotalMessages;
}

/// <summary>
/// SMS delivery report
/// </summary>
public class SmsDeliveryReport
{
    public string MessageId { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public SmsDeliveryStatus Status { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// SMS delivery status
/// </summary>
public enum SmsDeliveryStatus
{
    Pending = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Expired = 5,
    Rejected = 6,
    Unknown = 7
}
