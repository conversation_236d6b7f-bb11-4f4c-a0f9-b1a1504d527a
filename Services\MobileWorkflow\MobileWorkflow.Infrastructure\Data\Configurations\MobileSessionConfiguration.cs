using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MobileWorkflow.Domain.Entities;

namespace MobileWorkflow.Infrastructure.Data.Configurations;

public class MobileSessionConfiguration : IEntityTypeConfiguration<MobileSession>
{
    public void Configure(EntityTypeBuilder<MobileSession> builder)
    {
        builder.ToTable("mobile_sessions");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(e => e.MobileAppId)
            .HasColumnName("mobile_app_id")
            .IsRequired();

        builder.Property(e => e.DeviceId)
            .HasColumnName("device_id")
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(e => e.DeviceInfo)
            .HasColumnName("device_info")
            .HasMaxLength(1000)
            .IsRequired();

        builder.Property(e => e.AppVersion)
            .HasColumnName("app_version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Platform)
            .HasColumnName("platform")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.OSVersion)
            .HasColumnName("os_version")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.StartTime)
            .HasColumnName("start_time")
            .IsRequired();

        builder.Property(e => e.EndTime)
            .HasColumnName("end_time");

        builder.Property(e => e.IsActive)
            .HasColumnName("is_active")
            .IsRequired();

        builder.Property(e => e.LastKnownLocation)
            .HasColumnName("last_known_location")
            .HasMaxLength(500);

        builder.Property(e => e.IsOfflineCapable)
            .HasColumnName("is_offline_capable")
            .IsRequired();

        builder.Property(e => e.LastSyncTime)
            .HasColumnName("last_sync_time")
            .IsRequired();

        builder.Property(e => e.OfflineActionsCount)
            .HasColumnName("offline_actions_count")
            .IsRequired();

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        // Navigation properties
        builder.HasOne(e => e.MobileApp)
            .WithMany(a => a.Sessions)
            .HasForeignKey(e => e.MobileAppId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.OfflineData)
            .WithOne(o => o.MobileSession)
            .HasForeignKey(o => o.MobileSessionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
