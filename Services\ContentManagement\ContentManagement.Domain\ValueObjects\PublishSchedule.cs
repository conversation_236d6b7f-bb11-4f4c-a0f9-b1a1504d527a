﻿using ContentManagement.Domain.Enums;
using Shared.Domain.ValueObjects;

namespace ContentManagement.Domain.ValueObjects;

/// <summary>
/// Value object representing content publishing schedule
/// </summary>
public class PublishSchedule : ValueObject
{
    public DateTime? PublishAt { get; private set; }
    public DateTime? UnpublishAt { get; private set; }
    public bool IsRecurring { get; private set; }
    public RecurrencePattern Pattern { get; private set; }
    public int? RecurrenceInterval { get; private set; }
    public DateTime? RecurrenceEndDate { get; private set; }

    private PublishSchedule()
    {
    }

    private PublishSchedule(
        DateTime? publishAt,
        DateTime? unpublishAt,
        bool isRecurring,
        RecurrencePattern pattern,
        int? recurrenceInterval,
        DateTime? recurrenceEndDate)
    {
        PublishAt = publishAt;
        UnpublishAt = unpublishAt;
        IsRecurring = isRecurring;
        Pattern = pattern;
        RecurrenceInterval = recurrenceInterval;
        RecurrenceEndDate = recurrenceEndDate;

        // Validation
        if (publishAt.HasValue && unpublishAt.HasValue && publishAt.Value >= unpublishAt.Value)
            throw new ArgumentException("Publish date must be before unpublish date");

        if (isRecurring && pattern == RecurrencePattern.None)
            throw new ArgumentException("Recurring schedule requires a recurrence pattern");

        if (isRecurring && recurrenceInterval.HasValue && recurrenceInterval.Value <= 0)
            throw new ArgumentException("Recurrence interval must be positive");

        if (isRecurring && recurrenceEndDate.HasValue && publishAt.HasValue && recurrenceEndDate.Value <= publishAt.Value)
            throw new ArgumentException("Recurrence end date must be after publish date");
    }

    public static PublishSchedule CreateImmediate()
    {
        return new PublishSchedule(DateTime.UtcNow, null, false, RecurrencePattern.None, null, null);
    }

    public static PublishSchedule CreateScheduled(DateTime publishAt, DateTime? unpublishAt = null)
    {
        return new PublishSchedule(publishAt, unpublishAt, false, RecurrencePattern.None, null, null);
    }

    public static PublishSchedule CreateRecurring(
        DateTime publishAt,
        RecurrencePattern pattern,
        int interval = 1,
        DateTime? recurrenceEndDate = null,
        DateTime? unpublishAt = null)
    {
        if (pattern == RecurrencePattern.None)
            throw new ArgumentException("Recurring schedule requires a valid recurrence pattern");

        return new PublishSchedule(publishAt, unpublishAt, true, pattern, interval, recurrenceEndDate);
    }

    public bool ShouldPublishNow(DateTime? currentTime = null)
    {
        var now = currentTime ?? DateTime.UtcNow;

        if (!PublishAt.HasValue)
            return false;

        if (!IsRecurring)
            return now >= PublishAt.Value;

        // For recurring schedules, check if we're at a recurrence point
        return IsAtRecurrencePoint(now);
    }

    public bool ShouldUnpublishNow(DateTime? currentTime = null)
    {
        var now = currentTime ?? DateTime.UtcNow;
        return UnpublishAt.HasValue && now >= UnpublishAt.Value;
    }

    public DateTime? GetNextPublishDate(DateTime? fromDate = null)
    {
        var from = fromDate ?? DateTime.UtcNow;

        if (!IsRecurring || !PublishAt.HasValue)
            return PublishAt;

        if (RecurrenceEndDate.HasValue && from > RecurrenceEndDate.Value)
            return null;

        var interval = RecurrenceInterval ?? 1;
        var nextDate = PublishAt.Value;

        while (nextDate <= from)
        {
            nextDate = Pattern switch
            {
                RecurrencePattern.Daily => nextDate.AddDays(interval),
                RecurrencePattern.Weekly => nextDate.AddDays(7 * interval),
                RecurrencePattern.Monthly => nextDate.AddMonths(interval),
                RecurrencePattern.Quarterly => nextDate.AddMonths(3 * interval),
                RecurrencePattern.Yearly => nextDate.AddYears(interval),
                _ => nextDate
            };
        }

        if (RecurrenceEndDate.HasValue && nextDate > RecurrenceEndDate.Value)
            return null;

        return nextDate;
    }

    public bool IsActive(DateTime? currentTime = null)
    {
        var now = currentTime ?? DateTime.UtcNow;

        if (PublishAt.HasValue && now < PublishAt.Value)
            return false;

        if (UnpublishAt.HasValue && now >= UnpublishAt.Value)
            return false;

        if (IsRecurring && RecurrenceEndDate.HasValue && now > RecurrenceEndDate.Value)
            return false;

        return true;
    }

    private bool IsAtRecurrencePoint(DateTime currentTime)
    {
        if (!PublishAt.HasValue || !IsRecurring)
            return false;

        var interval = RecurrenceInterval ?? 1;
        var timeDiff = currentTime - PublishAt.Value;

        return Pattern switch
        {
            RecurrencePattern.Daily => timeDiff.TotalDays >= 0 && (int)timeDiff.TotalDays % interval == 0,
            RecurrencePattern.Weekly => timeDiff.TotalDays >= 0 && (int)(timeDiff.TotalDays / 7) % interval == 0,
            RecurrencePattern.Monthly => IsMonthlyRecurrence(currentTime, interval),
            RecurrencePattern.Quarterly => IsMonthlyRecurrence(currentTime, interval * 3),
            RecurrencePattern.Yearly => IsYearlyRecurrence(currentTime, interval),
            _ => false
        };
    }

    private bool IsMonthlyRecurrence(DateTime currentTime, int monthInterval)
    {
        if (!PublishAt.HasValue) return false;

        var monthsDiff = (currentTime.Year - PublishAt.Value.Year) * 12 + currentTime.Month - PublishAt.Value.Month;
        return monthsDiff >= 0 && monthsDiff % monthInterval == 0 && currentTime.Day >= PublishAt.Value.Day;
    }

    private bool IsYearlyRecurrence(DateTime currentTime, int yearInterval)
    {
        if (!PublishAt.HasValue) return false;

        var yearsDiff = currentTime.Year - PublishAt.Value.Year;
        return yearsDiff >= 0 && yearsDiff % yearInterval == 0 &&
               currentTime.Month >= PublishAt.Value.Month &&
               (currentTime.Month > PublishAt.Value.Month || currentTime.Day >= PublishAt.Value.Day);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return PublishAt ?? DateTime.MinValue;
        yield return UnpublishAt ?? DateTime.MaxValue;
        yield return IsRecurring;
        yield return Pattern;
        yield return RecurrenceInterval ?? 0;
        yield return RecurrenceEndDate ?? DateTime.MaxValue;
    }
}




