version: '3.8'

services:
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tli-analytics-timescaledb
    restart: unless-stopped
    environment:
      POSTGRES_DB: TLI_AnalyticsBI
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      # TimescaleDB specific settings
      TIMESCALEDB_TELEMETRY: 'off'
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./Scripts/init-timescaledb.sql:/docker-entrypoint-initdb.d/01-init-timescaledb.sql:ro
      - ./database-setup.sql:/docker-entrypoint-initdb.d/02-init-analytics-db.sql:ro
    command: 
      - postgres
      - -c
      - shared_preload_libraries=timescaledb
      - -c
      - max_connections=200
      - -c
      - shared_buffers=256MB
      - -c
      - effective_cache_size=1GB
      - -c
      - maintenance_work_mem=64MB
      - -c
      - checkpoint_completion_target=0.9
      - -c
      - wal_buffers=16MB
      - -c
      - default_statistics_target=100
      - -c
      - random_page_cost=1.1
      - -c
      - effective_io_concurrency=200
      - -c
      - work_mem=4MB
      - -c
      - min_wal_size=1GB
      - -c
      - max_wal_size=4GB
      - -c
      - max_worker_processes=8
      - -c
      - max_parallel_workers_per_gather=4
      - -c
      - max_parallel_workers=8
      - -c
      - max_parallel_maintenance_workers=4
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timescale -d TLI_AnalyticsBI"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - tli-analytics-network

  redis:
    image: redis:7-alpine
    container_name: tli-analytics-redis
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - tli-analytics-network

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: tli-analytics-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5051:80"  # Different port to avoid conflicts
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      timescaledb:
        condition: service_healthy
    networks:
      - tli-analytics-network

  # Optional: Grafana for analytics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: tli-analytics-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
    ports:
      - "3001:3000"  # Different port to avoid conflicts
    volumes:
      - grafana_data:/var/lib/grafana
      - ./Scripts/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro
      - ./Scripts/grafana-dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml:ro
    depends_on:
      timescaledb:
        condition: service_healthy
    networks:
      - tli-analytics-network

  # Analytics & BI Service API
  analytics-api:
    build:
      context: ../../
      dockerfile: Services/AnalyticsBIService/Dockerfile
    container_name: tli-analytics-api
    restart: unless-stopped
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: http://+:8080
      ConnectionStrings__DefaultConnection: "Host=timescaledb;Port=5432;Database=TLI_AnalyticsBI;User Id=timescale;Password=timescale;Include Error Detail=true"
      ConnectionStrings__TimescaleConnection: "Host=timescaledb;Port=5432;Database=TLI_AnalyticsBI_Timescale;User Id=timescale;Password=timescale;Include Error Detail=true"
      ConnectionStrings__Redis: "redis:6379"
      AnalyticsSettings__DataRetentionDays: "30"
      AnalyticsSettings__MetricAggregationInterval: "00:05:00"
      AnalyticsSettings__EnableRealTimeAnalytics: "true"
      AnalyticsSettings__EnablePredictiveAnalytics: "false"
      AnalyticsSettings__CacheExpirationMinutes: "15"
      JwtSettings__Secret: "YourSuperSecretKeyThatIsAtLeast32CharactersLong!"
      JwtSettings__Issuer: "TLI.Analytics"
      JwtSettings__Audience: "TLI.Users"
      JwtSettings__ExpiryMinutes: "60"
    ports:
      - "5014:8080"
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - tli-analytics-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  timescaledb_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

networks:
  tli-analytics-network:
    driver: bridge
    name: tli-analytics-network
