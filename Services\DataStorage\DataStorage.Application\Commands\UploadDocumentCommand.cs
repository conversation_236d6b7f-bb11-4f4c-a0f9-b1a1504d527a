using MediatR;
using DataStorage.Application.DTOs;
using DataStorage.Domain.Enums;

namespace DataStorage.Application.Commands;

public class UploadDocumentCommand : IRequest<DocumentDto>
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DocumentType DocumentType { get; set; }
    public DocumentCategory Category { get; set; }
    public AccessLevel AccessLevel { get; set; } = AccessLevel.Private;
    
    // File data
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    
    // Ownership
    public Guid OwnerId { get; set; }
    public string OwnerType { get; set; } = string.Empty;
    
    // Optional metadata
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public string? ExternalReference { get; set; }
    public StorageProvider? PreferredStorageProvider { get; set; }
}

public class UploadMultipleDocumentsCommand : IRequest<IEnumerable<DocumentDto>>
{
    public IEnumerable<UploadDocumentCommand> Documents { get; set; } = new List<UploadDocumentCommand>();
    public bool ContinueOnError { get; set; } = true;
}

public class UpdateDocumentCommand : IRequest<DocumentDto>
{
    public Guid DocumentId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
    public Guid UserId { get; set; } // For authorization
}

public class ChangeDocumentStatusCommand : IRequest<DocumentDto>
{
    public Guid DocumentId { get; set; }
    public DocumentStatus NewStatus { get; set; }
    public string? Reason { get; set; }
    public Guid UserId { get; set; } // For authorization
}

public class GrantDocumentAccessCommand : IRequest<Unit>
{
    public Guid DocumentId { get; set; }
    public Guid TargetUserId { get; set; }
    public string TargetUserType { get; set; } = string.Empty;
    public AccessLevel AccessLevel { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization
}

public class RevokeDocumentAccessCommand : IRequest<Unit>
{
    public Guid DocumentId { get; set; }
    public Guid TargetUserId { get; set; }
    public Guid RequestingUserId { get; set; } // For authorization
}

public class DeleteDocumentCommand : IRequest<Unit>
{
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; } // For authorization
    public bool PermanentDelete { get; set; } = false;
}

public class ArchiveDocumentCommand : IRequest<Unit>
{
    public Guid DocumentId { get; set; }
    public Guid UserId { get; set; } // For authorization
}

public class CreateDocumentVersionCommand : IRequest<DocumentVersionDto>
{
    public Guid DocumentId { get; set; }
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }
    public Guid UserId { get; set; } // For authorization and tracking
}

public class BulkArchiveDocumentsCommand : IRequest<int>
{
    public IEnumerable<Guid> DocumentIds { get; set; } = new List<Guid>();
    public Guid UserId { get; set; } // For authorization
}

public class BulkDeleteDocumentsCommand : IRequest<int>
{
    public IEnumerable<Guid> DocumentIds { get; set; } = new List<Guid>();
    public Guid UserId { get; set; } // For authorization
    public bool PermanentDelete { get; set; } = false;
}
