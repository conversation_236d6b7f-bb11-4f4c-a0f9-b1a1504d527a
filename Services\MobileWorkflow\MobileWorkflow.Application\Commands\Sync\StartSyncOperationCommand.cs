using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.Sync;

public class StartSyncOperationCommand : IRequest<SyncOperationResult>
{
    public Guid UserId { get; set; }
    public Guid DeviceId { get; set; }
    public SyncOptions Options { get; set; } = new();
}

public class StartSyncOperationCommandHandler : IRequestHandler<StartSyncOperationCommand, SyncOperationResult>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<StartSyncOperationCommandHandler> _logger;

    public StartSyncOperationCommandHandler(
        IAdvancedSyncService syncService,
        ILogger<StartSyncOperationCommandHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<SyncOperationResult> Handle(StartSyncOperationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting sync operation for user {UserId} on device {DeviceId}",
                request.UserId, request.DeviceId);

            var result = await _syncService.StartSyncOperationAsync(
                request.UserId,
                request.DeviceId,
                request.Options,
                cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Sync operation {SyncOperationId} started successfully for user {UserId}",
                    result.SyncOperationId, request.UserId);
            }
            else
            {
                _logger.LogWarning("Failed to start sync operation for user {UserId}: {Message}",
                    request.UserId, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling StartSyncOperationCommand for user {UserId}", request.UserId);
            return new SyncOperationResult
            {
                Success = false,
                Message = $"Failed to start sync operation: {ex.Message}"
            };
        }
    }
}
