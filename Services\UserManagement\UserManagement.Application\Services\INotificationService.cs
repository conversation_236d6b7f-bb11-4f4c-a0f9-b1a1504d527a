namespace UserManagement.Application.Services
{
    public interface INotificationService
    {
        Task NotifyUserApprovedAsync(Guid userId, string userType, string displayName, string email,
            Guid approvedBy, DateTime approvedAt, string notes = "");

        Task NotifyUserRejectedAsync(Guid userId, string userType, string displayName, string email,
            Guid rejectedBy, DateTime rejectedAt, string reason, string notes = "");

        Task NotifyDocumentsApprovedAsync(Guid submissionId, Guid userId, string userType,
            List<string> approvedDocuments, Guid approvedBy, DateTime processedAt, string notes = "");

        Task NotifyDocumentsRejectedAsync(Guid submissionId, Guid userId, string userType,
            List<DocumentRejectionInfo> rejectedDocuments, Guid rejectedBy, DateTime processedAt, string notes = "");

        Task NotifyDocumentOcrProcessedAsync(Guid submissionId, Guid userId, string documentType,
            decimal confidenceScore, bool requiresManualReview, bool autoApproved,
            List<string> validationErrors, DateTime processedAt);

        Task NotifyProfileStatusChangedAsync(Guid userId, string userType, string displayName,
            string previousStatus, string newStatus, DateTime changedAt, string reason = "");

        Task NotifyNewUserRegisteredAsync(Guid userId, string userType, string displayName,
            string email, DateTime registeredAt);

        Task NotifyDocumentSubmittedAsync(Guid submissionId, Guid userId, string userType,
            string displayName, int documentCount, DateTime submittedAt);

        Task SendSystemNotificationAsync(string title, string message, string type = "info",
            Dictionary<string, object>? data = null, Guid? targetUserId = null, string? targetRole = null);

        Task SendToUserAsync<T>(Guid userId, string method, T data);
        Task SendToRoleAsync<T>(string role, string method, T data);
        Task SendToAllAsync<T>(string method, T data);

        // Missing methods needed by EnhancedFeedbackService and ManageUserAccountCommandHandler
        Task SendAccountActionNotificationAsync(Guid userId, string action, string reason,
            Dictionary<string, object>? additionalData = null);
        Task SendKycStatusNotificationAsync(Guid userId, string status, string reason = "",
            Dictionary<string, object>? additionalData = null);
        Task SendRedFlagAlertAsync(object redFlag, CancellationToken cancellationToken = default);
        Task SendFeedbackNotificationAsync(object feedback, CancellationToken cancellationToken = default);
    }

    public class DocumentRejectionInfo
    {
        public string DocumentType { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }
}
