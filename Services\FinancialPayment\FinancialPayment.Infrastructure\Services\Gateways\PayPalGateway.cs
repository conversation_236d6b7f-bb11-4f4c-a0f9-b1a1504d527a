using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class PayPalGateway : IPaymentGateway
{
    private readonly PayPalSettings _settings;
    private readonly ILogger<PayPalGateway> _logger;
    private readonly HttpClient _httpClient;

    public string GatewayName => "PayPal";

    public PayPalGateway(IOptions<PayPalSettings> settings, ILogger<PayPalGateway> logger, HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
        
        // Configure HTTP client for PayPal API
        var baseUrl = _settings.IsSandbox ? "https://api.sandbox.paypal.com/" : "https://api.paypal.com/";
        _httpClient.BaseAddress = new Uri(baseUrl);
    }

    public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
    {
        _logger.LogInformation("Processing payment via PayPal for user {UserId} with amount {Amount}",
            request.UserId, request.Amount);

        try
        {
            // Get access token first
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Failed to obtain PayPal access token");
            }

            // Create payment
            var paymentData = new
            {
                intent = "CAPTURE",
                purchase_units = new[]
                {
                    new
                    {
                        amount = new
                        {
                            currency_code = request.Amount.Currency,
                            value = request.Amount.Amount.ToString("F2")
                        },
                        description = request.Description
                    }
                },
                application_context = new
                {
                    return_url = request.CallbackUrl,
                    cancel_url = request.CancelUrl
                }
            };

            // For demo purposes, simulate successful payment
            await Task.Delay(1000);
            var paymentId = $"PAYID-{Guid.NewGuid():N}";

            _logger.LogInformation("Payment processed successfully via PayPal with payment ID {PaymentId}", paymentId);

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = paymentId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment completed successfully via PayPal"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment via PayPal for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<RefundResult> ProcessRefundAsync(RefundRequest request)
    {
        _logger.LogInformation("Processing refund via PayPal for transaction {TransactionId}", request.TransactionId);

        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Failed to obtain PayPal access token");
            }

            var refundData = new
            {
                amount = new
                {
                    currency_code = request.Amount.Currency,
                    value = request.Amount.Amount.ToString("F2")
                },
                note_to_payer = request.Reason
            };

            // For demo purposes, simulate successful refund
            await Task.Delay(500);
            var refundId = $"REFUND-{Guid.NewGuid():N}";

            _logger.LogInformation("Refund processed successfully via PayPal with refund ID {RefundId}", refundId);

            return new RefundResult
            {
                IsSuccess = true,
                RefundId = refundId,
                RefundAmount = request.Amount,
                ProcessedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund via PayPal for transaction {TransactionId}", request.TransactionId);

            return new RefundResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<PaymentStatus> GetPaymentStatusAsync(string transactionId)
    {
        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                return PaymentStatus.Failed;
            }

            // For demo purposes, simulate status check
            await Task.Delay(100);
            return PaymentStatus.Completed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment status for transaction {TransactionId}", transactionId);
            return PaymentStatus.Failed;
        }
    }

    public async Task<bool> ValidateWebhookAsync(string payload, string signature)
    {
        try
        {
            // PayPal webhook validation logic would go here
            // For demo purposes, return true
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PayPal webhook");
            return false;
        }
    }

    public async Task<PaymentResult> CreatePaymentIntentAsync(PaymentIntentRequest request)
    {
        _logger.LogInformation("Creating payment intent via PayPal for user {UserId}", request.UserId);

        try
        {
            var accessToken = await GetAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Failed to obtain PayPal access token");
            }

            var orderData = new
            {
                intent = request.AutoCapture ? "CAPTURE" : "AUTHORIZE",
                purchase_units = new[]
                {
                    new
                    {
                        amount = new
                        {
                            currency_code = request.Amount.Currency,
                            value = request.Amount.Amount.ToString("F2")
                        },
                        description = request.Description
                    }
                }
            };

            // For demo purposes, simulate successful intent creation
            await Task.Delay(200);
            var orderId = $"ORDER-{Guid.NewGuid():N}";

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = orderId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment intent created successfully via PayPal"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent via PayPal for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<string?> GetAccessTokenAsync()
    {
        try
        {
            // For demo purposes, return a mock token
            await Task.Delay(100);
            return $"A21AAL_{Guid.NewGuid():N}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error obtaining PayPal access token");
            return null;
        }
    }
}

public class PayPalSettings
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string WebhookId { get; set; } = string.Empty;
    public bool IsSandbox { get; set; } = true;
}
