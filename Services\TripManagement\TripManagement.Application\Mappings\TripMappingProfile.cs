using AutoMapper;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Entities;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Application.Mappings;

public class TripMappingProfile : Profile
{
    public TripMappingProfile()
    {
        // Trip mappings
        CreateMap<Trip, TripDto>()
            .ForMember(dest => dest.CurrentLocation, opt => opt.MapFrom(src => src.GetCurrentLocation()))
            .ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.IsDelayed()))
            .ForMember(dest => dest.ActualDuration, opt => opt.MapFrom(src => src.GetActualDuration()));

        // Route mappings
        CreateMap<Route, RouteDto>();

        // Location mappings
        CreateMap<Location, LocationDto>();

        // TripStop mappings
        CreateMap<TripStop, TripStopDto>()
            .ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.IsDelayed()));

        // Driver mappings
        CreateMap<Driver, DriverDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.IsLicenseValid, opt => opt.MapFrom(src => src.IsLicenseValid))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable));

        // Vehicle mappings
        CreateMap<Vehicle, VehicleDto>()
            .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.DisplayName))
            .ForMember(dest => dest.IsInsuranceValid, opt => opt.MapFrom(src => src.IsInsuranceValid))
            .ForMember(dest => dest.IsFitnessValid, opt => opt.MapFrom(src => src.IsFitnessValid))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable));

        // ProofOfDelivery mappings
        CreateMap<ProofOfDelivery, ProofOfDeliveryDto>();

        // TripLocationUpdate mappings
        CreateMap<TripLocationUpdate, TripLocationUpdateDto>();

        // TripException mappings
        CreateMap<TripException, TripExceptionDto>();

        // Reverse mappings for commands
        CreateMap<LocationDto, Location>()
            .ConstructUsing(src => new Location(
                src.Latitude,
                src.Longitude,
                src.Timestamp,
                src.Altitude,
                src.Accuracy,
                src.Address,
                src.City,
                src.State,
                src.Country,
                src.PostalCode));

        CreateMap<RouteDto, Route>()
            .ConstructUsing(src => new Route(
                new Location(
                    src.StartLocation.Latitude,
                    src.StartLocation.Longitude,
                    src.StartLocation.Timestamp,
                    src.StartLocation.Altitude,
                    src.StartLocation.Accuracy,
                    src.StartLocation.Address,
                    src.StartLocation.City,
                    src.StartLocation.State,
                    src.StartLocation.Country,
                    src.StartLocation.PostalCode),
                new Location(
                    src.EndLocation.Latitude,
                    src.EndLocation.Longitude,
                    src.EndLocation.Timestamp,
                    src.EndLocation.Altitude,
                    src.EndLocation.Accuracy,
                    src.EndLocation.Address,
                    src.EndLocation.City,
                    src.EndLocation.State,
                    src.EndLocation.Country,
                    src.EndLocation.PostalCode),
                src.Waypoints.Select(w => new Location(
                    w.Latitude,
                    w.Longitude,
                    w.Timestamp,
                    w.Altitude,
                    w.Accuracy,
                    w.Address,
                    w.City,
                    w.State,
                    w.Country,
                    w.PostalCode)).ToList(),
                src.EstimatedDistanceKm,
                src.EstimatedDuration,
                src.RouteInstructions));
    }
}
