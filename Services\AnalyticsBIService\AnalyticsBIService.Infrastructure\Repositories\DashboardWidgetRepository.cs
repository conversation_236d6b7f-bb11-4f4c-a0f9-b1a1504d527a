using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class DashboardWidgetRepository : OptimizedRepositoryBase<DashboardWidget, Guid>, IDashboardWidgetRepository
{
    public DashboardWidgetRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<IEnumerable<DashboardWidget>> GetByDashboardIdAsync(Guid dashboardId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DashboardWidget>()
            .Where(w => w.DashboardId == dashboardId)
            .OrderBy(w => w.Position)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DashboardWidget>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DashboardWidget>()
            .Where(w => w.UserId == userId)
            .OrderBy(w => w.Position)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DashboardWidget>> GetActiveWidgetsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<DashboardWidget>()
            .Where(w => w.IsActive && w.IsVisible)
            .OrderBy(w => w.Position)
            .ToListAsync(cancellationToken);
    }

    public async Task<DashboardWidget?> GetByPositionAsync(Guid dashboardId, int position, CancellationToken cancellationToken = default)
    {
        return await Context.Set<DashboardWidget>()
            .FirstOrDefaultAsync(w => w.DashboardId == dashboardId && w.Position == position, cancellationToken);
    }

    public async Task UpdatePositionAsync(Guid widgetId, int newRow, int newColumn, int newWidth, int newHeight, CancellationToken cancellationToken = default)
    {
        var widget = await Context.Set<DashboardWidget>()
            .FirstOrDefaultAsync(w => w.Id == widgetId, cancellationToken);

        if (widget != null)
        {
            // Note: DashboardWidget only has UpdatePosition(int position) and Resize(int width, int height)
            // For now, we'll use the position parameter as the primary position indicator
            widget.UpdatePosition(newRow);
            widget.Resize(newWidth, newHeight);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task UpdateConfigurationAsync(Guid widgetId, string configuration, CancellationToken cancellationToken = default)
    {
        var widget = await Context.Set<DashboardWidget>()
            .FirstOrDefaultAsync(w => w.Id == widgetId, cancellationToken);

        if (widget != null)
        {
            // Parse the configuration string to Dictionary if needed
            Dictionary<string, object>? configDict = null;
            if (!string.IsNullOrEmpty(configuration))
            {
                try
                {
                    configDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(configuration);
                }
                catch
                {
                    // If parsing fails, create a simple dictionary with the raw string
                    configDict = new Dictionary<string, object> { ["rawConfig"] = configuration };
                }
            }

            widget.UpdateConfiguration(configDict);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task BulkUpdatePositionsAsync(List<(Guid WidgetId, int Row, int Column, int Width, int Height)> positions, CancellationToken cancellationToken = default)
    {
        var widgetIds = positions.Select(p => p.WidgetId).ToList();
        var widgets = await Context.Set<DashboardWidget>()
            .Where(w => widgetIds.Contains(w.Id))
            .ToListAsync(cancellationToken);

        foreach (var position in positions)
        {
            var widget = widgets.FirstOrDefault(w => w.Id == position.WidgetId);
            if (widget != null)
            {
                widget.UpdatePosition(position.Row);
                widget.Resize(position.Width, position.Height);
            }
        }

        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteByDashboardIdAsync(Guid dashboardId, CancellationToken cancellationToken = default)
    {
        var widgets = await Context.Set<DashboardWidget>()
            .Where(w => w.DashboardId == dashboardId)
            .ToListAsync(cancellationToken);

        Context.Set<DashboardWidget>().RemoveRange(widgets);
        await Context.SaveChangesAsync(cancellationToken);
    }
}
