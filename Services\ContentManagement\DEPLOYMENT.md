# Content Management Service Deployment Guide

This guide provides comprehensive instructions for deploying the Content Management Service in various environments.

## Prerequisites

### System Requirements
- .NET 8 Runtime
- PostgreSQL 13+ database
- RabbitMQ message broker
- Redis (optional, for caching)
- Load balancer (for production)

### Dependencies
- Identity Service (for authentication)
- Audit & Compliance Service (for audit logging)
- Communication & Notification Service (for notifications)
- Data & Storage Service (for file management)

## Environment Configuration

### Development Environment

1. **Database Setup**:
   ```bash
   # Create database
   createdb TLI_ContentManagement_Dev
   
   # Run migrations
   dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API
   ```

2. **Configuration** (`appsettings.Development.json`):
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Database=TLI_ContentManagement_Dev;Username=dev_user;Password=dev_password"
     },
     "Jwt": {
       "Key": "development-key-32-characters-long",
       "Issuer": "TLI.ContentManagement.Dev",
       "Audience": "TLI.Users.Dev"
     },
     "Services": {
       "DataStorage": { "BaseUrl": "http://localhost:5008" },
       "AuditCompliance": { "BaseUrl": "http://localhost:5012" },
       "CommunicationNotification": { "BaseUrl": "http://localhost:5009" }
     }
   }
   ```

3. **Run the service**:
   ```bash
   dotnet run --project ContentManagement.API --environment Development
   ```

### Staging Environment

1. **Database Setup**:
   ```bash
   # Create staging database
   createdb TLI_ContentManagement_Staging
   
   # Run migrations
   dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API --environment Staging
   ```

2. **Configuration** (`appsettings.Staging.json`):
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=staging-db.tli.com;Database=TLI_ContentManagement;Username=staging_user;Password=${DB_PASSWORD}"
     },
     "Jwt": {
       "Key": "${JWT_SECRET_KEY}",
       "Issuer": "TLI.ContentManagement.Staging",
       "Audience": "TLI.Users.Staging"
     },
     "Services": {
       "DataStorage": { "BaseUrl": "https://staging-datastorage.tli.com" },
       "AuditCompliance": { "BaseUrl": "https://staging-audit.tli.com" },
       "CommunicationNotification": { "BaseUrl": "https://staging-notification.tli.com" }
     },
     "Serilog": {
       "WriteTo": [
         {
           "Name": "File",
           "Args": {
             "path": "/var/log/tli/contentmanagement/log-.txt",
             "rollingInterval": "Day"
           }
         }
       ]
     }
   }
   ```

### Production Environment

1. **Database Setup**:
   ```bash
   # Create production database with proper user permissions
   createdb TLI_ContentManagement_Prod
   createuser contentmgmt_user
   grant all privileges on database TLI_ContentManagement_Prod to contentmgmt_user;
   
   # Run migrations
   dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API --environment Production
   ```

2. **Configuration** (`appsettings.Production.json`):
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=prod-db-cluster.tli.com;Database=TLI_ContentManagement;Username=contentmgmt_user;Password=${DB_PASSWORD};Pooling=true;MinPoolSize=5;MaxPoolSize=100"
     },
     "Jwt": {
       "Key": "${JWT_SECRET_KEY}",
       "Issuer": "TLI.ContentManagement",
       "Audience": "TLI.Users"
     },
     "Services": {
       "DataStorage": { "BaseUrl": "https://datastorage.tli.com" },
       "AuditCompliance": { "BaseUrl": "https://audit.tli.com" },
       "CommunicationNotification": { "BaseUrl": "https://notification.tli.com" }
     },
     "Serilog": {
       "WriteTo": [
         {
           "Name": "File",
           "Args": {
             "path": "/var/log/tli/contentmanagement/log-.txt",
             "rollingInterval": "Day",
             "retainedFileCountLimit": 30
           }
         }
       ]
     }
   }
   ```

## Docker Deployment

### Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Services/ContentManagement/ContentManagement.API/ContentManagement.API.csproj", "Services/ContentManagement/ContentManagement.API/"]
COPY ["Services/ContentManagement/ContentManagement.Application/ContentManagement.Application.csproj", "Services/ContentManagement/ContentManagement.Application/"]
COPY ["Services/ContentManagement/ContentManagement.Domain/ContentManagement.Domain.csproj", "Services/ContentManagement/ContentManagement.Domain/"]
COPY ["Services/ContentManagement/ContentManagement.Infrastructure/ContentManagement.Infrastructure.csproj", "Services/ContentManagement/ContentManagement.Infrastructure/"]

RUN dotnet restore "Services/ContentManagement/ContentManagement.API/ContentManagement.API.csproj"
COPY . .
WORKDIR "/src/Services/ContentManagement/ContentManagement.API"
RUN dotnet build "ContentManagement.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ContentManagement.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ContentManagement.API.dll"]
```

### Docker Compose
```yaml
version: '3.8'

services:
  contentmanagement-api:
    build:
      context: .
      dockerfile: Services/ContentManagement/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=TLI_ContentManagement;Username=postgres;Password=postgres
      - Jwt__Key=${JWT_SECRET_KEY}
    ports:
      - "5007:80"
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - tli-network

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=TLI_ContentManagement
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - tli-network

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "15672:15672"
    networks:
      - tli-network

volumes:
  postgres_data:

networks:
  tli-network:
    driver: bridge
```

## Kubernetes Deployment

### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tli-contentmanagement
```

### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: contentmanagement-config
  namespace: tli-contentmanagement
data:
  appsettings.json: |
    {
      "Logging": {
        "LogLevel": {
          "Default": "Information"
        }
      },
      "AllowedHosts": "*"
    }
```

### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: contentmanagement-secrets
  namespace: tli-contentmanagement
type: Opaque
data:
  db-password: <base64-encoded-password>
  jwt-key: <base64-encoded-jwt-key>
```

### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: contentmanagement-api
  namespace: tli-contentmanagement
spec:
  replicas: 3
  selector:
    matchLabels:
      app: contentmanagement-api
  template:
    metadata:
      labels:
        app: contentmanagement-api
    spec:
      containers:
      - name: contentmanagement-api
        image: tli/contentmanagement-api:latest
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ConnectionStrings__DefaultConnection
          value: "Host=postgres-service;Database=TLI_ContentManagement;Username=postgres;Password=$(DB_PASSWORD)"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: contentmanagement-secrets
              key: db-password
        - name: Jwt__Key
          valueFrom:
            secretKeyRef:
              name: contentmanagement-secrets
              key: jwt-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: contentmanagement-service
  namespace: tli-contentmanagement
spec:
  selector:
    app: contentmanagement-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

## Database Migration

### Manual Migration
```bash
# Development
dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API --environment Development

# Staging
dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API --environment Staging

# Production
dotnet ef database update --project ContentManagement.Infrastructure --startup-project ContentManagement.API --environment Production
```

### Automated Migration (Init Container)
```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: contentmanagement-migration
  namespace: tli-contentmanagement
spec:
  template:
    spec:
      containers:
      - name: migration
        image: tli/contentmanagement-api:latest
        command: ["dotnet", "ef", "database", "update"]
        env:
        - name: ConnectionStrings__DefaultConnection
          value: "Host=postgres-service;Database=TLI_ContentManagement;Username=postgres;Password=$(DB_PASSWORD)"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: contentmanagement-secrets
              key: db-password
      restartPolicy: OnFailure
```

## Monitoring and Health Checks

### Health Check Endpoints
- `/health` - Overall health status
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

### Monitoring Setup
```yaml
# Prometheus ServiceMonitor
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: contentmanagement-metrics
spec:
  selector:
    matchLabels:
      app: contentmanagement-api
  endpoints:
  - port: http
    path: /metrics
```

## Security Considerations

1. **Database Security**:
   - Use dedicated database user with minimal permissions
   - Enable SSL/TLS for database connections
   - Regular security updates

2. **API Security**:
   - HTTPS only in production
   - JWT token validation
   - Rate limiting
   - Input validation

3. **Container Security**:
   - Use non-root user
   - Scan images for vulnerabilities
   - Keep base images updated

## Backup and Recovery

### Database Backup
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h prod-db-cluster.tli.com -U contentmgmt_user TLI_ContentManagement > /backups/contentmanagement_$DATE.sql
```

### Disaster Recovery
1. Database restoration from backup
2. Service configuration restoration
3. DNS failover procedures
4. Data consistency verification

## Performance Tuning

### Database Optimization
- Connection pooling configuration
- Index optimization
- Query performance monitoring
- Regular VACUUM and ANALYZE

### Application Optimization
- Response caching
- Database query optimization
- Memory usage monitoring
- CPU utilization tracking

## Troubleshooting

### Common Issues
1. **Database Connection Issues**:
   - Check connection string
   - Verify database accessibility
   - Check user permissions

2. **Authentication Issues**:
   - Verify JWT configuration
   - Check Identity Service connectivity
   - Validate token expiration

3. **Performance Issues**:
   - Monitor database query performance
   - Check memory usage
   - Analyze response times

### Log Analysis
```bash
# View application logs
kubectl logs -f deployment/contentmanagement-api -n tli-contentmanagement

# Search for errors
kubectl logs deployment/contentmanagement-api -n tli-contentmanagement | grep ERROR
```
