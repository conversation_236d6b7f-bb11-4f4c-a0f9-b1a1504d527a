using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Workflow;
using AuditCompliance.Infrastructure.MultiTenant;

namespace AuditCompliance.API.Controllers;

/// <summary>
/// Controller for workflow automation and management
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Route("api/[controller]")] // Backward compatibility
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[Authorize]
[RequireTenant]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ILogger<WorkflowController> _logger;

    public WorkflowController(
        IWorkflowService workflowService,
        IWorkflowEngine workflowEngine,
        ILogger<WorkflowController> logger)
    {
        _workflowService = workflowService;
        _workflowEngine = workflowEngine;
        _logger = logger;
    }

    /// <summary>
    /// Get all workflow definitions
    /// </summary>
    [HttpGet("definitions")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<List<WorkflowDefinitionDto>>> GetWorkflowDefinitions(
        [FromQuery] string? category = null,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            var definitions = await _workflowService.GetWorkflowDefinitionsAsync(category, activeOnly);
            return Ok(definitions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow definitions");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow definition by ID
    /// </summary>
    [HttpGet("definitions/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<WorkflowDefinitionDto>> GetWorkflowDefinition(Guid id)
    {
        try
        {
            var definition = await _workflowService.GetWorkflowDefinitionAsync(id);
            if (definition == null)
            {
                return NotFound($"Workflow definition with ID {id} not found");
            }

            return Ok(definition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow definition {DefinitionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new workflow definition
    /// </summary>
    [HttpPost("definitions")]
    [Authorize(Roles = "Admin,WorkflowManager")]
    public async Task<ActionResult<object>> CreateWorkflowDefinition([FromBody] CreateWorkflowDefinitionDto definitionDto)
    {
        try
        {
            var definitionId = await _workflowService.CreateWorkflowDefinitionAsync(definitionDto);
            
            var response = new
            {
                DefinitionId = definitionId,
                Message = "Workflow definition created successfully",
                Links = new
                {
                    Self = $"/api/workflow/definitions/{definitionId}",
                    Start = $"/api/workflow/instances/start"
                }
            };

            return CreatedAtAction(nameof(GetWorkflowDefinition), new { id = definitionId }, response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow definition");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update workflow definition
    /// </summary>
    [HttpPut("definitions/{id}")]
    [Authorize(Roles = "Admin,WorkflowManager")]
    public async Task<ActionResult<object>> UpdateWorkflowDefinition(Guid id, [FromBody] UpdateWorkflowDefinitionDto definitionDto)
    {
        try
        {
            var success = await _workflowService.UpdateWorkflowDefinitionAsync(id, definitionDto);
            if (!success)
            {
                return NotFound($"Workflow definition with ID {id} not found");
            }

            return Ok(new { Message = "Workflow definition updated successfully" });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow definition {DefinitionId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Start a new workflow instance
    /// </summary>
    [HttpPost("instances/start")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager,User")]
    public async Task<ActionResult<object>> StartWorkflow([FromBody] StartWorkflowDto startDto)
    {
        try
        {
            var instanceId = await _workflowService.StartWorkflowAsync(startDto);
            
            var response = new
            {
                InstanceId = instanceId,
                Message = "Workflow started successfully",
                Status = "Running",
                Links = new
                {
                    Self = $"/api/workflow/instances/{instanceId}",
                    Tasks = $"/api/workflow/instances/{instanceId}/tasks"
                }
            };

            return CreatedAtAction(nameof(GetWorkflowInstance), new { id = instanceId }, response);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting workflow");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow instance by ID
    /// </summary>
    [HttpGet("instances/{id}")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager,User")]
    public async Task<ActionResult<WorkflowInstanceDto>> GetWorkflowInstance(Guid id)
    {
        try
        {
            var instance = await _workflowService.GetWorkflowInstanceAsync(id);
            if (instance == null)
            {
                return NotFound($"Workflow instance with ID {id} not found");
            }

            return Ok(instance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow instance {InstanceId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow instances with filtering
    /// </summary>
    [HttpPost("instances/search")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<List<WorkflowInstanceDto>>> GetWorkflowInstances([FromBody] WorkflowInstanceFilterDto filter)
    {
        try
        {
            var instances = await _workflowService.GetWorkflowInstancesAsync(filter);
            return Ok(instances);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow instances");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Cancel workflow instance
    /// </summary>
    [HttpPost("instances/{id}/cancel")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<object>> CancelWorkflow(Guid id, [FromBody] CancelWorkflowDto cancelDto)
    {
        try
        {
            var success = await _workflowService.CancelWorkflowAsync(id, cancelDto.Reason, cancelDto.CancelledBy);
            if (!success)
            {
                return NotFound($"Workflow instance with ID {id} not found");
            }

            return Ok(new { Message = "Workflow cancelled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling workflow {InstanceId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Suspend workflow instance
    /// </summary>
    [HttpPost("instances/{id}/suspend")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<object>> SuspendWorkflow(Guid id, [FromBody] SuspendWorkflowDto suspendDto)
    {
        try
        {
            var success = await _workflowService.SuspendWorkflowAsync(id, suspendDto.Reason, suspendDto.SuspendedBy);
            if (!success)
            {
                return NotFound($"Workflow instance with ID {id} not found");
            }

            return Ok(new { Message = "Workflow suspended successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending workflow {InstanceId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Resume workflow instance
    /// </summary>
    [HttpPost("instances/{id}/resume")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<object>> ResumeWorkflow(Guid id, [FromBody] ResumeWorkflowDto resumeDto)
    {
        try
        {
            var success = await _workflowService.ResumeWorkflowAsync(id, resumeDto.ResumedBy);
            if (!success)
            {
                return NotFound($"Workflow instance with ID {id} not found");
            }

            return Ok(new { Message = "Workflow resumed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming workflow {InstanceId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get tasks assigned to current user
    /// </summary>
    [HttpGet("tasks/my-tasks")]
    [Authorize]
    public async Task<ActionResult<List<WorkflowTaskDto>>> GetMyTasks([FromQuery] TaskFilterDto? filter = null)
    {
        try
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var tasks = await _workflowService.GetUserTasksAsync(userId, filter);
            return Ok(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user tasks");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get task by ID
    /// </summary>
    [HttpGet("tasks/{id}")]
    [Authorize]
    public async Task<ActionResult<WorkflowTaskDto>> GetTask(Guid id)
    {
        try
        {
            var task = await _workflowService.GetTaskAsync(id);
            if (task == null)
            {
                return NotFound($"Task with ID {id} not found");
            }

            return Ok(task);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting task {TaskId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Complete a workflow task
    /// </summary>
    [HttpPost("tasks/{id}/complete")]
    [Authorize]
    public async Task<ActionResult<object>> CompleteTask(Guid id, [FromBody] CompleteTaskDto completeDto)
    {
        try
        {
            var success = await _workflowService.CompleteTaskAsync(id, completeDto);
            if (!success)
            {
                return NotFound($"Task with ID {id} not found or already completed");
            }

            return Ok(new { Message = "Task completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing task {TaskId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Assign task to user
    /// </summary>
    [HttpPost("tasks/{id}/assign")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<object>> AssignTask(Guid id, [FromBody] AssignTaskDto assignDto)
    {
        try
        {
            var assignedBy = User.Identity?.Name ?? "System";
            var success = await _workflowService.AssignTaskAsync(id, assignDto.AssigneeId, assignedBy);
            if (!success)
            {
                return NotFound($"Task with ID {id} not found");
            }

            return Ok(new { Message = "Task assigned successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning task {TaskId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow history
    /// </summary>
    [HttpGet("instances/{id}/history")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<List<WorkflowHistoryDto>>> GetWorkflowHistory(Guid id)
    {
        try
        {
            var history = await _workflowService.GetWorkflowHistoryAsync(id);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow history for {InstanceId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow analytics
    /// </summary>
    [HttpPost("analytics")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<WorkflowAnalyticsDto>> GetWorkflowAnalytics([FromBody] WorkflowAnalyticsRequestDto request)
    {
        try
        {
            var analytics = await _workflowService.GetWorkflowAnalyticsAsync(request);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow analytics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Validate workflow definition
    /// </summary>
    [HttpPost("definitions/validate")]
    [Authorize(Roles = "Admin,WorkflowManager")]
    public async Task<ActionResult<WorkflowValidationResult>> ValidateWorkflowDefinition([FromBody] WorkflowDefinitionDto definition)
    {
        try
        {
            var result = await _workflowService.ValidateWorkflowDefinitionAsync(definition);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating workflow definition");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get workflow templates
    /// </summary>
    [HttpGet("templates")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<List<WorkflowTemplateDto>>> GetWorkflowTemplates([FromQuery] string? category = null)
    {
        try
        {
            var templates = await _workflowService.GetWorkflowTemplatesAsync(category);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow templates");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create workflow from template
    /// </summary>
    [HttpPost("templates/{templateId}/create")]
    [Authorize(Roles = "Admin,ComplianceOfficer,WorkflowManager")]
    public async Task<ActionResult<object>> CreateWorkflowFromTemplate(Guid templateId, [FromBody] CreateFromTemplateDto createDto)
    {
        try
        {
            var definitionId = await _workflowService.CreateWorkflowFromTemplateAsync(templateId, createDto);
            
            var response = new
            {
                DefinitionId = definitionId,
                Message = "Workflow created from template successfully",
                TemplateId = templateId,
                Links = new
                {
                    Definition = $"/api/workflow/definitions/{definitionId}",
                    Start = $"/api/workflow/instances/start"
                }
            };

            return CreatedAtAction(nameof(GetWorkflowDefinition), new { id = definitionId }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow from template {TemplateId}", templateId);
            return StatusCode(500, "Internal server error");
        }
    }
}

/// <summary>
/// Cancel workflow DTO
/// </summary>
public class CancelWorkflowDto
{
    public string Reason { get; set; } = string.Empty;
    public string CancelledBy { get; set; } = string.Empty;
}

/// <summary>
/// Suspend workflow DTO
/// </summary>
public class SuspendWorkflowDto
{
    public string Reason { get; set; } = string.Empty;
    public string SuspendedBy { get; set; } = string.Empty;
}

/// <summary>
/// Resume workflow DTO
/// </summary>
public class ResumeWorkflowDto
{
    public string ResumedBy { get; set; } = string.Empty;
}

/// <summary>
/// Assign task DTO
/// </summary>
public class AssignTaskDto
{
    public string AssigneeId { get; set; } = string.Empty;
}
