using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;
using MediatR;

namespace AuditCompliance.Application.Commands
{
    /// <summary>
    /// Enhanced audit export command with comprehensive features
    /// </summary>
    public class EnhancedAuditExportCommand : IRequest<EnhancedAuditExportResponseDto>
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<string> ActionTypes { get; set; } = new();
        public List<string> EntityTypes { get; set; } = new();
        public List<string> ModuleNames { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> SeverityLevels { get; set; } = new();
        public List<string> IpAddresses { get; set; } = new();
        public List<string> ComplianceFlags { get; set; } = new();
        public bool IncludeSystemActions { get; set; } = true;
        public bool IncludeSensitiveData { get; set; } = false;
        public ExportFormat Format { get; set; } = ExportFormat.CSV;
        public List<string> SelectedColumns { get; set; } = new();
        public int? MaxRecords { get; set; }
        public int PageSize { get; set; } = 10000;
        public bool EnableBackgroundProcessing { get; set; } = false;
        public bool RequireDigitalSignature { get; set; } = false;
        public string? NotificationEmail { get; set; }
        public bool EnableCompression { get; set; } = false;
        public string? ExportTitle { get; set; }
        public Dictionary<string, object> CustomFilters { get; set; } = new();
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bulk audit export command
    /// </summary>
    public class BulkAuditExportCommand : IRequest<BulkAuditExportResponseDto>
    {
        public List<EnhancedAuditExportCommand> ExportCommands { get; set; } = new();
        public bool ProcessInParallel { get; set; } = false;
        public string? BatchName { get; set; }
        public bool CreateZipArchive { get; set; } = true;
        public string? NotificationEmail { get; set; }
        public Dictionary<string, object> GlobalSettings { get; set; } = new();
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Get export job status command
    /// </summary>
    public class GetExportJobStatusCommand : IRequest<ExportJobStatusDto>
    {
        public string JobId { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
    }

    /// <summary>
    /// Cancel export job command
    /// </summary>
    public class CancelExportJobCommand : IRequest<bool>
    {
        public string JobId { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Get supported export formats command
    /// </summary>
    public class GetSupportedExportFormatsCommand : IRequest<List<ExportFormatConfigDto>>
    {
        public bool IncludeAdvancedFormats { get; set; } = true;
        public string? UserRole { get; set; }
    }

    /// <summary>
    /// Validate export request command
    /// </summary>
    public class ValidateExportRequestCommand : IRequest<List<ExportValidationDto>>
    {
        public EnhancedAuditExportCommand ExportCommand { get; set; } = new();
        public bool PerformDeepValidation { get; set; } = true;
    }

    /// <summary>
    /// Get export history command
    /// </summary>
    public class GetExportHistoryCommand : IRequest<List<EnhancedAuditExportResponseDto>>
    {
        public Guid? RequestedBy { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public ExportStatus? Status { get; set; }
        public ExportFormat? Format { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Download export file command
    /// </summary>
    public class DownloadExportFileCommand : IRequest<ExportFileDto>
    {
        public string ExportId { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
        public bool VerifyDigitalSignature { get; set; } = true;
    }

    /// <summary>
    /// Export file DTO for download
    /// </summary>
    public class ExportFileDto
    {
        public string FileName { get; set; } = string.Empty;
        public byte[] FileContent { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public bool IsCompressed { get; set; }
        public bool HasDigitalSignature { get; set; }
        public string? DigitalSignature { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new();
    }

    /// <summary>
    /// Schedule export command
    /// </summary>
    public class ScheduleExportCommand : IRequest<string>
    {
        public EnhancedAuditExportCommand ExportCommand { get; set; } = new();
        public string CronExpression { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public List<string> NotificationEmails { get; set; } = new();
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; } = string.Empty;
    }

    /// <summary>
    /// Get scheduled exports command
    /// </summary>
    public class GetScheduledExportsCommand : IRequest<List<ScheduledExportDto>>
    {
        public Guid? RequestedBy { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Scheduled export DTO
    /// </summary>
    public class ScheduledExportDto
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string CronExpression { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastExecuted { get; set; }
        public DateTime? NextExecution { get; set; }
        public int ExecutionCount { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public EnhancedAuditExportCommand ExportCommand { get; set; } = new();
        public List<string> NotificationEmails { get; set; } = new();
    }
}
