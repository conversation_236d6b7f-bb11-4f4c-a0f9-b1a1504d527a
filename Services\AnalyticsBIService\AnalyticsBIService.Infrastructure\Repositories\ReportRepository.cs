using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class ReportRepository : OptimizedRepositoryBase<Report, Guid>, IReportRepository
{
    public ReportRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<PagedResult<Report>> GetByTypeAsync(ReportType type, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Type == type)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Report>> GetByStatusAsync(ReportStatus status, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Status == status)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<Report>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.CreatedBy == userId)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<Report?> GetWithSectionsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }

    public async Task<PagedResult<Report>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<List<Report>> GetScheduledReportsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Status == ReportStatus.Scheduled)
            .OrderBy(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Report>> GetExpiredReportsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.ExpiresAt.HasValue && r.ExpiresAt.Value < now)
            .OrderBy(r => r.ExpiresAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Report>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Name.Contains(searchTerm) ||
                       r.Description.Contains(searchTerm))
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<ReportType, int>> GetCountsByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .GroupBy(r => r.Type)
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Type, x => x.Count, cancellationToken);
    }

    public async Task<Dictionary<ReportStatus, int>> GetCountsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .GroupBy(r => r.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Status, x => x.Count, cancellationToken);
    }

    public async Task<int> GetUserReportCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .CountAsync(r => r.CreatedBy == userId, cancellationToken);
    }

    public async Task<IEnumerable<Report>> GetRecentReportsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .OrderByDescending(r => r.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Report>> GetPendingReportsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Status == ReportStatus.Pending || r.Status == ReportStatus.InProgress)
            .OrderBy(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<Report>> GetByGeneratedByAsync(Guid generatedBy, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.CreatedBy == generatedBy)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task UpdateStatusAsync(Guid reportId, ReportStatus status, CancellationToken cancellationToken = default)
    {
        var report = await Context.Set<Report>()
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

        if (report != null)
        {
            report.UpdateStatus(status);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<Report>> GetRecentReportsAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.CreatedBy == userId)
            .OrderByDescending(r => r.CreatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<Report>> SearchReportsAsync(string searchTerm, Guid? userId = null, int page = 1, int pageSize = 10, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .Where(r => r.Name.Contains(searchTerm) || r.Description.Contains(searchTerm));

        if (userId.HasValue)
        {
            query = query.Where(r => r.CreatedBy == userId.Value);
        }

        query = query.OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<Report>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<Guid> SaveReportAsync(Report report, CancellationToken cancellationToken = default)
    {
        await Context.Set<Report>().AddAsync(report, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
        return report.Id;
    }

    public async Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Report>()
            .Include(r => r.Sections)
            .Include(r => r.Exports)
            .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
    }

    public async Task<Guid> SaveTemplateAsync(ReportTemplate template, CancellationToken cancellationToken = default)
    {
        await Context.Set<ReportTemplate>().AddAsync(template, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
        return template.Id;
    }

    public async Task DeleteOldExportsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldExports = await Context.Set<ReportExport>()
            .Where(e => e.CreatedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        Context.Set<ReportExport>().RemoveRange(oldExports);
        await Context.SaveChangesAsync(cancellationToken);
    }
}
