using MediatR;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.CreateRfq;

public class CreateRfqCommand : IRequest<Guid>
{
    public Guid TransportCompanyId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public CreateLoadDetailsDto LoadDetails { get; set; } = new();
    public CreateRouteDetailsDto RouteDetails { get; set; } = new();
    public CreateRfqRequirementsDto Requirements { get; set; } = new();
    public DateTime? ExpiresAt { get; set; }
    public CreateMoneyDto? BudgetRange { get; set; }
    public string? SpecialInstructions { get; set; }
    public bool IsUrgent { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
    public CreateRfqTimeframeDto? Timeframe { get; set; }
}

public class CreateLoadDetailsDto
{
    public string Description { get; set; } = string.Empty;
    public LoadType LoadType { get; set; }
    public CreateWeightDto Weight { get; set; } = new();
    public CreateVolumeDto? Volume { get; set; }
    public CreateDimensionsDto? Dimensions { get; set; }
    public int Quantity { get; set; }
    public string? PackagingType { get; set; }
    public VehicleType RequiredVehicleType { get; set; }
    public bool RequiresSpecialHandling { get; set; }
    public string? SpecialHandlingInstructions { get; set; }
    public bool IsHazardous { get; set; }
    public string? HazardousClassification { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public CreateTemperatureRangeDto? TemperatureRange { get; set; }
    public List<string> Photos { get; set; } = new();
}

public class CreateRouteDetailsDto
{
    public CreateAddressDto PickupAddress { get; set; } = new();
    public CreateAddressDto DeliveryAddress { get; set; } = new();
    public DateTime PreferredPickupDate { get; set; }
    public DateTime PreferredDeliveryDate { get; set; }
    public CreateTimeWindowDto? PickupTimeWindow { get; set; }
    public CreateTimeWindowDto? DeliveryTimeWindow { get; set; }
    public CreateDistanceDto? EstimatedDistance { get; set; }
    public TimeSpan? EstimatedDuration { get; set; }
    public List<CreateAddressDto> IntermediateStops { get; set; } = new();
    public string? RouteNotes { get; set; }
    public bool IsFlexiblePickup { get; set; }
    public bool IsFlexibleDelivery { get; set; }
}

public class CreateRfqRequirementsDto
{
    public VehicleType RequiredVehicleType { get; set; }
    public int? MinVehicleCapacity { get; set; }
    public bool RequiresInsurance { get; set; }
    public decimal? MinInsuranceAmount { get; set; }
    public bool RequiresLicense { get; set; }
    public List<string> RequiredLicenses { get; set; } = new();
    public bool RequiresExperience { get; set; }
    public int? MinYearsExperience { get; set; }
    public bool RequiresBackground { get; set; }
    public bool RequiresTracking { get; set; }
    public bool RequiresTemperatureControl { get; set; }
    public CreateTemperatureRangeDto? TemperatureRange { get; set; }
    public List<string> AdditionalRequirements { get; set; } = new();
}

public class CreateRfqTimeframeDto
{
    public int Duration { get; set; }
    public TimeframeUnit Unit { get; set; }
    public bool AllowExtensions { get; set; } = true;
    public int MaxExtensions { get; set; } = 3;
}

public class CreateAddressDto
{
    public string Street { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? SpecialInstructions { get; set; }
}

public class CreateWeightDto
{
    public decimal Value { get; set; }
    public WeightUnit Unit { get; set; }
}

public class CreateVolumeDto
{
    public decimal Value { get; set; }
    public VolumeUnit Unit { get; set; }
}

public class CreateDimensionsDto
{
    public decimal Length { get; set; }
    public decimal Width { get; set; }
    public decimal Height { get; set; }
    public string Unit { get; set; } = "cm";
}

public class CreateTemperatureRangeDto
{
    public decimal MinTemperature { get; set; }
    public decimal MaxTemperature { get; set; }
    public string Unit { get; set; } = "C";
}

public class CreateTimeWindowDto
{
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
}

public class CreateDistanceDto
{
    public decimal Value { get; set; }
    public DistanceUnit Unit { get; set; }
}

public class CreateMoneyDto
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
}
