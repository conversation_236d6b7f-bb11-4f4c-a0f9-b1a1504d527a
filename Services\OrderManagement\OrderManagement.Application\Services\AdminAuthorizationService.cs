using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;

namespace OrderManagement.Application.Services;

public interface IAdminAuthorizationService
{
    Task<bool> CanPerformActionAsync(Guid adminUserId, string action, string? resource = null, CancellationToken cancellationToken = default);
    Task<bool> CanAccessOrderAsync(Guid adminUserId, Guid orderId, string action, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserPermissionsAsync(Guid adminUserId, CancellationToken cancellationToken = default);
    Task<AdminPermissionResult> CheckPermissionAsync(AdminPermissionRequest request, CancellationToken cancellationToken = default);
    Task<bool> HasRoleAsync(Guid adminUserId, string role, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserRolesAsync(Guid adminUserId, CancellationToken cancellationToken = default);
}

public class AdminAuthorizationService : IAdminAuthorizationService
{
    private readonly IAdminPermissionRepository _permissionRepository;
    private readonly ILogger<AdminAuthorizationService> _logger;
    private readonly AdminAuthorizationOptions _options;

    // Predefined admin actions
    private static readonly Dictionary<string, AdminActionDefinition> AdminActions = new()
    {
        ["ForceOrderClosure"] = new AdminActionDefinition
        {
            Name = "ForceOrderClosure",
            Description = "Force close an order",
            RequiredRoles = new[] { "Admin", "SuperAdmin" },
            RequiredPermissions = new[] { "order.force_close" },
            AuditLevel = AuditLevel.High,
            RequiresReason = true
        },
        ["OverrideOrderStatus"] = new AdminActionDefinition
        {
            Name = "OverrideOrderStatus",
            Description = "Override order status",
            RequiredRoles = new[] { "Admin", "SuperAdmin" },
            RequiredPermissions = new[] { "order.status_override" },
            AuditLevel = AuditLevel.High,
            RequiresReason = true
        },
        ["BulkUpdateOrders"] = new AdminActionDefinition
        {
            Name = "BulkUpdateOrders",
            Description = "Bulk update multiple orders",
            RequiredRoles = new[] { "Admin", "SuperAdmin" },
            RequiredPermissions = new[] { "order.bulk_update" },
            AuditLevel = AuditLevel.Medium,
            RequiresReason = true
        },
        ["ManageDocuments"] = new AdminActionDefinition
        {
            Name = "ManageDocuments",
            Description = "Manage order documents",
            RequiredRoles = new[] { "Admin", "SuperAdmin", "DocumentManager" },
            RequiredPermissions = new[] { "document.manage" },
            AuditLevel = AuditLevel.Medium,
            RequiresReason = false
        },
        ["ViewAuditLogs"] = new AdminActionDefinition
        {
            Name = "ViewAuditLogs",
            Description = "View audit logs",
            RequiredRoles = new[] { "Admin", "SuperAdmin", "Auditor" },
            RequiredPermissions = new[] { "audit.view" },
            AuditLevel = AuditLevel.Low,
            RequiresReason = false
        },
        ["ManageUsers"] = new AdminActionDefinition
        {
            Name = "ManageUsers",
            Description = "Manage user accounts",
            RequiredRoles = new[] { "SuperAdmin" },
            RequiredPermissions = new[] { "user.manage" },
            AuditLevel = AuditLevel.Critical,
            RequiresReason = true
        }
    };

    public AdminAuthorizationService(
        IAdminPermissionRepository permissionRepository,
        ILogger<AdminAuthorizationService> logger,
        IOptions<AdminAuthorizationOptions> options)
    {
        _permissionRepository = permissionRepository;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<bool> CanPerformActionAsync(Guid adminUserId, string action, string? resource = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if admin {AdminUserId} can perform action {Action} on resource {Resource}", 
                adminUserId, action, resource);

            if (!AdminActions.TryGetValue(action, out var actionDefinition))
            {
                _logger.LogWarning("Unknown admin action: {Action}", action);
                return false;
            }

            // Check if user has required roles
            var userRoles = await GetUserRolesAsync(adminUserId, cancellationToken);
            var hasRequiredRole = actionDefinition.RequiredRoles.Any(role => userRoles.Contains(role));

            if (!hasRequiredRole)
            {
                _logger.LogWarning("Admin {AdminUserId} lacks required role for action {Action}", adminUserId, action);
                return false;
            }

            // Check if user has required permissions
            var userPermissions = await GetUserPermissionsAsync(adminUserId, cancellationToken);
            var hasRequiredPermissions = actionDefinition.RequiredPermissions.All(permission => userPermissions.Contains(permission));

            if (!hasRequiredPermissions)
            {
                _logger.LogWarning("Admin {AdminUserId} lacks required permissions for action {Action}", adminUserId, action);
                return false;
            }

            // Additional resource-specific checks
            if (!string.IsNullOrEmpty(resource))
            {
                var resourceAccessAllowed = await CheckResourceAccessAsync(adminUserId, resource, action, cancellationToken);
                if (!resourceAccessAllowed)
                {
                    _logger.LogWarning("Admin {AdminUserId} denied access to resource {Resource} for action {Action}", 
                        adminUserId, resource, action);
                    return false;
                }
            }

            _logger.LogDebug("Admin {AdminUserId} authorized for action {Action}", adminUserId, action);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authorization for admin {AdminUserId} action {Action}", adminUserId, action);
            return false; // Fail secure
        }
    }

    public async Task<bool> CanAccessOrderAsync(Guid adminUserId, Guid orderId, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            // Basic action authorization
            var canPerformAction = await CanPerformActionAsync(adminUserId, action, $"order:{orderId}", cancellationToken);
            if (!canPerformAction)
                return false;

            // Additional order-specific checks could be added here
            // For example, checking if admin has access to the transport company that owns the order

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking order access for admin {AdminUserId} order {OrderId}", adminUserId, orderId);
            return false;
        }
    }

    public async Task<List<string>> GetUserPermissionsAsync(Guid adminUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _permissionRepository.GetUserPermissionsAsync(adminUserId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for admin {AdminUserId}", adminUserId);
            return new List<string>();
        }
    }

    public async Task<AdminPermissionResult> CheckPermissionAsync(AdminPermissionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var canPerform = await CanPerformActionAsync(request.AdminUserId, request.Action, request.Resource, cancellationToken);
            
            var result = new AdminPermissionResult
            {
                AdminUserId = request.AdminUserId,
                Action = request.Action,
                Resource = request.Resource,
                IsAuthorized = canPerform,
                CheckedAt = DateTime.UtcNow
            };

            if (!canPerform)
            {
                result.DenialReason = "Insufficient permissions or roles";
                result.RequiredRoles = AdminActions.TryGetValue(request.Action, out var actionDef) 
                    ? actionDef.RequiredRoles.ToList() 
                    : new List<string>();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for admin {AdminUserId}", request.AdminUserId);
            return new AdminPermissionResult
            {
                AdminUserId = request.AdminUserId,
                Action = request.Action,
                Resource = request.Resource,
                IsAuthorized = false,
                DenialReason = "Authorization check failed",
                CheckedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> HasRoleAsync(Guid adminUserId, string role, CancellationToken cancellationToken = default)
    {
        try
        {
            var userRoles = await GetUserRolesAsync(adminUserId, cancellationToken);
            return userRoles.Contains(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role for admin {AdminUserId}", adminUserId);
            return false;
        }
    }

    public async Task<List<string>> GetUserRolesAsync(Guid adminUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _permissionRepository.GetUserRolesAsync(adminUserId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for admin {AdminUserId}", adminUserId);
            return new List<string>();
        }
    }

    private async Task<bool> CheckResourceAccessAsync(Guid adminUserId, string resource, string action, CancellationToken cancellationToken)
    {
        // Implement resource-specific access checks
        // For example, checking if admin has access to specific transport companies, regions, etc.
        
        if (resource.StartsWith("order:"))
        {
            // Extract order ID and check if admin has access to the order's transport company
            if (Guid.TryParse(resource.Substring(6), out var orderId))
            {
                return await _permissionRepository.CanAccessOrderAsync(adminUserId, orderId, cancellationToken);
            }
        }

        // Default to allowing access if no specific restrictions
        return true;
    }
}

// Supporting classes
public class AdminActionDefinition
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string[] RequiredRoles { get; set; } = Array.Empty<string>();
    public string[] RequiredPermissions { get; set; } = Array.Empty<string>();
    public AuditLevel AuditLevel { get; set; }
    public bool RequiresReason { get; set; }
}

public class AdminPermissionRequest
{
    public Guid AdminUserId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? Resource { get; set; }
}

public class AdminPermissionResult
{
    public Guid AdminUserId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? Resource { get; set; }
    public bool IsAuthorized { get; set; }
    public string? DenialReason { get; set; }
    public List<string> RequiredRoles { get; set; } = new();
    public DateTime CheckedAt { get; set; }
}

public class AdminAuthorizationOptions
{
    public const string SectionName = "AdminAuthorization";
    
    public bool EnableResourceLevelSecurity { get; set; } = true;
    public bool LogAllAuthorizationChecks { get; set; } = false;
    public int CacheExpirationMinutes { get; set; } = 15;
}

public enum AuditLevel
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public interface IAdminPermissionRepository
{
    Task<List<string>> GetUserPermissionsAsync(Guid adminUserId, CancellationToken cancellationToken = default);
    Task<List<string>> GetUserRolesAsync(Guid adminUserId, CancellationToken cancellationToken = default);
    Task<bool> CanAccessOrderAsync(Guid adminUserId, Guid orderId, CancellationToken cancellationToken = default);
}
