using System;
using System.Threading.Tasks;
using Identity.Domain.Entities;

namespace Identity.Application.Common.Interfaces
{
    public interface IOtpService
    {
        Task<string> GenerateOtpAsync(
            Guid userId, 
            OtpPurpose purpose, 
            string deliveryMethod, 
            string deliveryAddress,
            int expirationMinutes = 5,
            int maxAttempts = 3);
        
        Task<bool> ValidateOtpAsync(Guid userId, string token, OtpPurpose purpose);
        
        Task<bool> SendOtpAsync(
            string deliveryMethod, 
            string deliveryAddress, 
            string token, 
            OtpPurpose purpose);
        
        Task InvalidateUserOtpsAsync(Guid userId, OtpPurpose purpose);
        
        Task CleanupExpiredOtpsAsync();
        
        Task<bool> CanRequestOtpAsync(Guid userId, OtpPurpose purpose);
        
        Task<int> GetRemainingAttemptsAsync(Guid userId, string token, OtpPurpose purpose);
    }
}
