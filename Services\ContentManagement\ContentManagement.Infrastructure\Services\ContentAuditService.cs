using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using System.Text.Json;

namespace ContentManagement.Infrastructure.Services;

/// <summary>
/// Service for content audit logging that integrates with the Audit & Compliance Service
/// </summary>
public class ContentAuditService : IContentAuditService
{
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ContentAuditService> _logger;

    public ContentAuditService(
        IMessageBroker messageBroker,
        ILogger<ContentAuditService> logger)
    {
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task LogContentCreatedAsync(
        Content content,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "ContentCreated",
            EntityType = "Content",
            EntityId = content.Id,
            Action = "Create",
            Description = $"Content '{content.Title}' created",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            EventData = new Dictionary<string, object>
            {
                ["contentType"] = content.Type.ToString(),
                ["contentTitle"] = content.Title,
                ["contentSlug"] = content.Slug,
                ["contentStatus"] = content.Status.ToString()
            },
            Severity = "Medium",
            ComplianceFlags = new List<string> { "ContentManagement", "DataCreation" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogContentUpdatedAsync(
        Content content,
        object oldValues,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "ContentUpdated",
            EntityType = "Content",
            EntityId = content.Id,
            Action = "Update",
            Description = $"Content '{content.Title}' updated",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            OldValues = JsonSerializer.Serialize(oldValues),
            NewValues = JsonSerializer.Serialize(new
            {
                content.Title,
                content.Description,
                content.ContentBody,
                content.MetaTitle,
                content.MetaDescription,
                content.Tags
            }),
            EventData = new Dictionary<string, object>
            {
                ["contentType"] = content.Type.ToString(),
                ["contentTitle"] = content.Title,
                ["contentSlug"] = content.Slug,
                ["contentStatus"] = content.Status.ToString(),
                ["versionNumber"] = content.Versions.Count
            },
            Severity = "Medium",
            ComplianceFlags = new List<string> { "ContentManagement", "DataModification" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogContentStatusChangedAsync(
        Content content,
        ContentStatus oldStatus,
        ContentStatus newStatus,
        Guid userId,
        string userName,
        string userRole,
        string? reason = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var severity = newStatus switch
        {
            ContentStatus.Published => "High",
            ContentStatus.Archived => "High",
            ContentStatus.Rejected => "Medium",
            _ => "Low"
        };

        var auditEvent = new ContentAuditEvent
        {
            EventType = "ContentStatusChanged",
            EntityType = "Content",
            EntityId = content.Id,
            Action = $"StatusChange_{newStatus}",
            Description = $"Content '{content.Title}' status changed from {oldStatus} to {newStatus}",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            OldValues = JsonSerializer.Serialize(new { Status = oldStatus.ToString() }),
            NewValues = JsonSerializer.Serialize(new { Status = newStatus.ToString(), Reason = reason }),
            EventData = new Dictionary<string, object>
            {
                ["contentType"] = content.Type.ToString(),
                ["contentTitle"] = content.Title,
                ["contentSlug"] = content.Slug,
                ["oldStatus"] = oldStatus.ToString(),
                ["newStatus"] = newStatus.ToString(),
                ["reason"] = reason ?? ""
            },
            Severity = severity,
            ComplianceFlags = new List<string> { "ContentManagement", "WorkflowApproval" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogContentVisibilityChangedAsync(
        Content content,
        object oldVisibility,
        object newVisibility,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "ContentVisibilityChanged",
            EntityType = "Content",
            EntityId = content.Id,
            Action = "VisibilityChange",
            Description = $"Content '{content.Title}' visibility settings changed",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            OldValues = JsonSerializer.Serialize(oldVisibility),
            NewValues = JsonSerializer.Serialize(newVisibility),
            EventData = new Dictionary<string, object>
            {
                ["contentType"] = content.Type.ToString(),
                ["contentTitle"] = content.Title,
                ["contentSlug"] = content.Slug
            },
            Severity = "Medium",
            ComplianceFlags = new List<string> { "ContentManagement", "AccessControl" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogContentDeletedAsync(
        Guid contentId,
        string contentTitle,
        ContentType contentType,
        Guid userId,
        string userName,
        string userRole,
        string? reason = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "ContentDeleted",
            EntityType = "Content",
            EntityId = contentId,
            Action = "Delete",
            Description = $"Content '{contentTitle}' deleted",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            EventData = new Dictionary<string, object>
            {
                ["contentType"] = contentType.ToString(),
                ["contentTitle"] = contentTitle,
                ["reason"] = reason ?? ""
            },
            Severity = "High",
            ComplianceFlags = new List<string> { "ContentManagement", "DataDeletion" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogFaqViewedAsync(
        FaqItem faqItem,
        Guid? userId = null,
        string? userName = null,
        string? userRole = null,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "FaqViewed",
            EntityType = "FaqItem",
            EntityId = faqItem.Id,
            Action = "View",
            Description = $"FAQ '{faqItem.Question}' viewed",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            EventData = new Dictionary<string, object>
            {
                ["faqCategory"] = faqItem.Category.ToString(),
                ["faqQuestion"] = faqItem.Question,
                ["viewCount"] = faqItem.ViewCount
            },
            Severity = "Low",
            ComplianceFlags = new List<string> { "ContentAccess", "Analytics" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogFaqRatedAsync(
        FaqItem faqItem,
        int rating,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "FaqRated",
            EntityType = "FaqItem",
            EntityId = faqItem.Id,
            Action = "Rate",
            Description = $"FAQ '{faqItem.Question}' rated {rating}/5",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            EventData = new Dictionary<string, object>
            {
                ["faqCategory"] = faqItem.Category.ToString(),
                ["faqQuestion"] = faqItem.Question,
                ["rating"] = rating,
                ["averageRating"] = faqItem.HelpfulnessRating,
                ["totalRatings"] = faqItem.TotalRatings
            },
            Severity = "Low",
            ComplianceFlags = new List<string> { "ContentFeedback", "Analytics" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogPolicyAcceptedAsync(
        PolicyDocument policy,
        PolicyAcceptance acceptance,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "PolicyAccepted",
            EntityType = "PolicyDocument",
            EntityId = policy.Id,
            Action = "Accept",
            Description = $"Policy '{policy.Title}' v{policy.Version} accepted by {acceptance.UserName}",
            UserId = acceptance.UserId,
            UserName = acceptance.UserName,
            UserRole = acceptance.UserRole,
            IpAddress = ipAddress ?? acceptance.IpAddress,
            UserAgent = userAgent ?? acceptance.UserAgent,
            EventData = new Dictionary<string, object>
            {
                ["policyType"] = policy.PolicyType.ToString(),
                ["policyVersion"] = policy.Version,
                ["acceptanceId"] = acceptance.Id,
                ["effectiveDate"] = policy.EffectiveDate,
                ["requiresAcceptance"] = policy.RequiresAcceptance
            },
            Severity = "High",
            ComplianceFlags = new List<string> { "PolicyCompliance", "LegalAcceptance", "GDPR" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    public async Task LogPolicyVersionCreatedAsync(
        PolicyDocument policy,
        string newVersion,
        Guid userId,
        string userName,
        string userRole,
        string? ipAddress = null,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        var auditEvent = new ContentAuditEvent
        {
            EventType = "PolicyVersionCreated",
            EntityType = "PolicyDocument",
            EntityId = policy.Id,
            Action = "CreateVersion",
            Description = $"New version {newVersion} created for policy '{policy.Title}'",
            UserId = userId,
            UserName = userName,
            UserRole = userRole,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            EventData = new Dictionary<string, object>
            {
                ["policyType"] = policy.PolicyType.ToString(),
                ["oldVersion"] = policy.Version,
                ["newVersion"] = newVersion,
                ["effectiveDate"] = policy.EffectiveDate,
                ["requiresAcceptance"] = policy.RequiresAcceptance
            },
            Severity = "High",
            ComplianceFlags = new List<string> { "PolicyManagement", "VersionControl", "LegalCompliance" },
            EventTimestamp = DateTime.UtcNow
        };

        await PublishAuditEventAsync(auditEvent, cancellationToken);
    }

    private async Task PublishAuditEventAsync(ContentAuditEvent auditEvent, CancellationToken cancellationToken)
    {
        try
        {
            await _messageBroker.PublishAsync("audit.content", auditEvent);
            _logger.LogInformation("Published audit event: {EventType} for {EntityType} {EntityId}", 
                auditEvent.EventType, auditEvent.EntityType, auditEvent.EntityId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish audit event: {EventType} for {EntityType} {EntityId}", 
                auditEvent.EventType, auditEvent.EntityType, auditEvent.EntityId);
            // Don't throw - audit logging should not break the main operation
        }
    }
}

/// <summary>
/// Content audit event for integration with Audit & Compliance Service
/// </summary>
public class ContentAuditEvent
{
    public string EventType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserRole { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public Dictionary<string, object> EventData { get; set; } = new();
    public string Severity { get; set; } = "Low";
    public List<string> ComplianceFlags { get; set; } = new();
    public DateTime EventTimestamp { get; set; }
}
