using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Domain.Entities;

/// <summary>
/// Alert configuration entity for alert settings
/// </summary>
public class AlertConfiguration : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public AlertType AlertType { get; private set; }
    public string Configuration { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public UserType UserType { get; private set; }
    public List<Guid> NotificationTargets { get; private set; }
    public Dictionary<string, object> Settings { get; private set; }

    private AlertConfiguration()
    {
        Name = string.Empty;
        Description = string.Empty;
        Configuration = string.Empty;
        NotificationTargets = new List<Guid>();
        Settings = new Dictionary<string, object>();
    }

    public AlertConfiguration(
        string name,
        string description,
        AlertType alertType,
        string configuration,
        Guid createdBy,
        UserType userType,
        List<Guid>? notificationTargets = null,
        Dictionary<string, object>? settings = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        AlertType = alertType;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CreatedBy = createdBy;
        UserType = userType;
        NotificationTargets = notificationTargets ?? new List<Guid>();
        Settings = settings ?? new Dictionary<string, object>();
        IsEnabled = true;
    }

    public void UpdateConfiguration(string configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SetUpdatedAt();
    }

    public void UpdateSettings(Dictionary<string, object> settings)
    {
        Settings = settings ?? throw new ArgumentNullException(nameof(settings));
        SetUpdatedAt();
    }

    public void AddNotificationTarget(Guid targetId)
    {
        if (!NotificationTargets.Contains(targetId))
        {
            NotificationTargets.Add(targetId);
            SetUpdatedAt();
        }
    }

    public void RemoveNotificationTarget(Guid targetId)
    {
        if (NotificationTargets.Remove(targetId))
        {
            SetUpdatedAt();
        }
    }

    public void Enable()
    {
        IsEnabled = true;
        SetUpdatedAt();
    }

    public void Disable()
    {
        IsEnabled = false;
        SetUpdatedAt();
    }
}
