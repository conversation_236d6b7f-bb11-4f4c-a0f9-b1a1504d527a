using Shared.Domain.Common;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// Represents a version of content for audit and rollback purposes
/// </summary>
public class ContentVersion : BaseEntity
{
    public Guid ContentId { get; private set; }
    public int VersionNumber { get; private set; }
    public string ContentBody { get; private set; }
    public string? ChangeDescription { get; private set; }
    public Guid CreatedBy { get; private set; }
    public string CreatedByName { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool IsPublished { get; private set; }

    private ContentVersion()
    {
        ContentBody = string.Empty;
        CreatedByName = string.Empty;
    }

    public ContentVersion(
        Guid contentId,
        int versionNumber,
        string contentBody,
        string? changeDescription,
        Guid createdBy,
        string createdByName)
    {
        if (string.IsNullOrWhiteSpace(contentBody))
            throw new ArgumentException("Content body cannot be empty", nameof(contentBody));
        if (string.IsNullOrWhiteSpace(createdByName))
            throw new ArgumentException("Created by name cannot be empty", nameof(createdByName));

        Id = Guid.NewGuid();
        ContentId = contentId;
        VersionNumber = versionNumber;
        ContentBody = contentBody;
        ChangeDescription = changeDescription;
        CreatedBy = createdBy;
        CreatedByName = createdByName;
        CreatedAt = DateTime.UtcNow;
        IsPublished = false;
    }

    public void MarkAsPublished()
    {
        IsPublished = true;
    }

    public void UnmarkAsPublished()
    {
        IsPublished = false;
    }
}


