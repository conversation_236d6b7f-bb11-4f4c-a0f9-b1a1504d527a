using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Dashboard;

/// <summary>
/// Handler for dashboard metrics queries
/// </summary>
public class GetDashboardMetricsQueryHandler :
    IRequestHandler<GetDashboardMetricsQuery, DashboardMetricsDto>,
    IRequestHandler<GetAverageOrderFulfillmentTimeQuery, decimal>,
    IRequestHandler<GetOrderFulfillmentTrendsQuery, List<MetricDataPointDto>>,
    IRequestHandler<GetRealTimeMetricsQuery, Dictionary<string, decimal>>
{
    private readonly IDashboardMetricsService _dashboardMetricsService;
    private readonly ILogger<GetDashboardMetricsQueryHandler> _logger;

    public GetDashboardMetricsQueryHandler(
        IDashboardMetricsService dashboardMetricsService,
        ILogger<GetDashboardMetricsQueryHandler> logger)
    {
        _dashboardMetricsService = dashboardMetricsService;
        _logger = logger;
    }

    public async Task<DashboardMetricsDto> Handle(GetDashboardMetricsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetDashboardMetricsQuery for user {UserId} of type {UserType}", 
                request.UserId, request.UserType);

            if (request.ForceRefresh)
            {
                await _dashboardMetricsService.RefreshMetricsCacheAsync(cancellationToken);
            }

            var metrics = await _dashboardMetricsService.GetDashboardMetricsAsync(
                request.UserId, 
                request.UserType, 
                cancellationToken);

            _logger.LogDebug("Successfully retrieved dashboard metrics for user {UserId}", request.UserId);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetDashboardMetricsQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<decimal> Handle(GetAverageOrderFulfillmentTimeQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetAverageOrderFulfillmentTimeQuery for user {UserId}", request.UserId);

            var averageTime = await _dashboardMetricsService.CalculateAverageOrderFulfillmentTimeAsync(
                request.UserId,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully calculated average order fulfillment time: {Time} hours for user {UserId}", 
                averageTime, request.UserId);
            return averageTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetAverageOrderFulfillmentTimeQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<List<MetricDataPointDto>> Handle(GetOrderFulfillmentTrendsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetOrderFulfillmentTrendsQuery for user {UserId} with period {Period}", 
                request.UserId, request.Period);

            var trends = await _dashboardMetricsService.GetOrderFulfillmentTrendsAsync(
                request.UserId,
                request.Period,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved {Count} trend data points for user {UserId}", 
                trends.Count, request.UserId);
            return trends;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetOrderFulfillmentTrendsQuery for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> Handle(GetRealTimeMetricsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Handling GetRealTimeMetricsQuery for user {UserId}", request.UserId);

            var metrics = await _dashboardMetricsService.GetRealTimeMetricsAsync(
                request.UserId,
                cancellationToken);

            _logger.LogDebug("Successfully retrieved {Count} real-time metrics for user {UserId}", 
                metrics.Count, request.UserId);
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling GetRealTimeMetricsQuery for user {UserId}", request.UserId);
            throw;
        }
    }
}
