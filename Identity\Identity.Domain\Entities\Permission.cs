using System;
using Identity.Domain.Common;
using Identity.Domain.Exceptions;

namespace Identity.Domain.Entities
{
    public enum PermissionType
    {
        Read,
        Write,
        Delete,
        Approve,
        Manage,
        Admin
    }

    public class Permission : Entity
    {
        public string Name { get; private set; }
        public string NormalizedName { get; private set; }
        public string Description { get; private set; }
        public string Module { get; private set; }
        public string Category { get; private set; }
        public PermissionType Type { get; private set; }
        public bool IsSystemPermission { get; private set; }
        public int Priority { get; private set; }
        public string Resource { get; private set; }
        public string Action { get; private set; }

        private Permission() { }

        public Permission(
            string name,
            string description,
            string module,
            string category,
            PermissionType type,
            string resource = null,
            string action = null,
            bool isSystemPermission = false,
            int priority = 0)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Permission name cannot be empty");

            if (string.IsNullOrWhiteSpace(module))
                throw new DomainException("Permission module cannot be empty");

            Name = name;
            NormalizedName = name.ToUpperInvariant();
            Description = description ?? string.Empty;
            Module = module;
            Category = category ?? string.Empty;
            Type = type;
            Resource = resource ?? string.Empty;
            Action = action ?? string.Empty;
            IsSystemPermission = isSystemPermission;
            Priority = priority;
            CreatedAt = DateTime.UtcNow;
        }

        public void Update(
            string name,
            string description,
            string module,
            string category,
            PermissionType type,
            string resource = null,
            string action = null,
            int priority = 0)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Permission name cannot be empty");

            if (string.IsNullOrWhiteSpace(module))
                throw new DomainException("Permission module cannot be empty");

            if (IsSystemPermission)
                throw new DomainException("System permissions cannot be updated");

            Name = name;
            NormalizedName = name.ToUpperInvariant();
            Description = description ?? string.Empty;
            Module = module;
            Category = category ?? string.Empty;
            Type = type;
            Resource = resource ?? string.Empty;
            Action = action ?? string.Empty;
            Priority = priority;
            UpdatedAt = DateTime.UtcNow;
        }

        public string GetFullPermissionName()
        {
            return $"{Module}:{Resource}:{Action}".ToLowerInvariant();
        }

        public bool Matches(string module, string resource, string action)
        {
            return Module.Equals(module, StringComparison.OrdinalIgnoreCase) &&
                   (string.IsNullOrEmpty(Resource) || Resource.Equals(resource, StringComparison.OrdinalIgnoreCase)) &&
                   (string.IsNullOrEmpty(Action) || Action.Equals(action, StringComparison.OrdinalIgnoreCase));
        }
    }
}
