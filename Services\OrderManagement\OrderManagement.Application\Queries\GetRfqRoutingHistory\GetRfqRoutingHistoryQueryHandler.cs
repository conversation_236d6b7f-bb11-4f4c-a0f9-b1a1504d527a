using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.DTOs;
using OrderManagement.Application.Interfaces;

namespace OrderManagement.Application.Queries.GetRfqRoutingHistory;

public class GetRfqRoutingHistoryQueryHandler : IRequestHandler<GetRfqRoutingHistoryQuery, List<RfqRoutingHistoryDto>>
{
    private readonly IRfqRoutingHistoryRepository _routingHistoryRepository;
    private readonly IRfqRepository _rfqRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetRfqRoutingHistoryQueryHandler> _logger;

    public GetRfqRoutingHistoryQueryHandler(
        IRfqRoutingHistoryRepository routingHistoryRepository,
        IRfqRepository rfqRepository,
        IMapper mapper,
        ILogger<GetRfqRoutingHistoryQueryHandler> logger)
    {
        _routingHistoryRepository = routingHistoryRepository;
        _rfqRepository = rfqRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<RfqRoutingHistoryDto>> Handle(GetRfqRoutingHistoryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting routing history for RFQ {RfqId} by user {UserId}", 
            request.RfqId, request.RequestingUserId);

        // Verify RFQ exists and user has access
        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
            return new List<RfqRoutingHistoryDto>();
        }

        // TODO: Add authorization logic here
        // Check if the requesting user has permission to view this RFQ routing history

        // Get routing history
        var routingHistory = await _routingHistoryRepository.GetByRfqIdAsync(request.RfqId, cancellationToken);

        var routingHistoryDtos = _mapper.Map<List<RfqRoutingHistoryDto>>(routingHistory);

        _logger.LogInformation("Retrieved {Count} routing history entries for RFQ {RfqId}",
            routingHistoryDtos.Count, request.RfqId);

        return routingHistoryDtos;
    }
}
