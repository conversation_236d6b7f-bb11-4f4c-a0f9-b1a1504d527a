using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IAdvancedWorkflowEngine
{
    Task<WorkflowExecutionResult> ExecuteWorkflowFromTemplateAsync(Guid templateId, Guid triggeredBy, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> ExecuteParallelStepsAsync(Guid executionId, List<Guid> stepIds, CancellationToken cancellationToken = default);
    Task<WorkflowExecutionResult> ExecuteConditionalBranchAsync(Guid executionId, Guid stepId, Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task<bool> RetryFailedStepAsync(Guid executionId, Guid stepExecutionId, CancellationToken cancellationToken = default);
    Task<WorkflowPerformanceMetrics> GetWorkflowPerformanceAsync(Guid workflowId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<WorkflowExecutionSummary>> GetActiveExecutionsAsync(CancellationToken cancellationToken = default);
    Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(Dictionary<string, object> definition, CancellationToken cancellationToken = default);
    Task<WorkflowTemplate> CreateWorkflowFromTemplateAsync(Guid templateId, Dictionary<string, object> customizations, string createdBy, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> GenerateWorkflowVisualizationAsync(Guid workflowId, CancellationToken cancellationToken = default);
}

public class AdvancedWorkflowEngine : IAdvancedWorkflowEngine
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowTemplateRepository _templateRepository;
    private readonly IWorkflowExecutionRepository _executionRepository;
    private readonly IWorkflowStepRepository _stepRepository;
    private readonly IWorkflowStepExecutionRepository _stepExecutionRepository;
    private readonly ILogger<AdvancedWorkflowEngine> _logger;
    private readonly IMemoryCache _cache;

    public AdvancedWorkflowEngine(
        IWorkflowRepository workflowRepository,
        IWorkflowTemplateRepository templateRepository,
        IWorkflowExecutionRepository executionRepository,
        IWorkflowStepRepository stepRepository,
        IWorkflowStepExecutionRepository stepExecutionRepository,
        ILogger<AdvancedWorkflowEngine> logger,
        IMemoryCache cache)
    {
        _workflowRepository = workflowRepository;
        _templateRepository = templateRepository;
        _executionRepository = executionRepository;
        _stepRepository = stepRepository;
        _stepExecutionRepository = stepExecutionRepository;
        _logger = logger;
        _cache = cache;
    }

    public async Task<WorkflowExecutionResult> ExecuteWorkflowFromTemplateAsync(Guid templateId, Guid triggeredBy, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing workflow from template {TemplateId} triggered by {TriggeredBy}", templateId, triggeredBy);

            var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
            if (template == null)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Template not found" };
            }

            if (!template.IsActive)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Template is not active" };
            }

            // Validate parameters against template schema
            var validationErrors = template.ValidateParameters(parameters);
            if (validationErrors.Count > 0)
            {
                return new WorkflowExecutionResult 
                { 
                    IsSuccess = false, 
                    ErrorMessage = $"Parameter validation failed: {string.Join(", ", validationErrors)}" 
                };
            }

            // Create workflow from template
            var workflow = await CreateWorkflowFromTemplateAsync(templateId, new Dictionary<string, object>(), triggeredBy.ToString(), cancellationToken);
            
            // Merge template defaults with provided parameters
            var mergedParameters = template.GetParameterDefaults();
            foreach (var param in parameters)
            {
                mergedParameters[param.Key] = param.Value;
            }

            // Start execution
            var execution = workflow.StartExecution(triggeredBy, mergedParameters);
            _executionRepository.Add(execution);
            await _executionRepository.SaveChangesAsync(cancellationToken);

            // Increment template usage
            template.IncrementUsage();
            await _templateRepository.SaveChangesAsync(cancellationToken);

            // Start executing steps
            _ = Task.Run(async () => await ExecuteWorkflowStepsAsync(execution.Id, CancellationToken.None));

            return new WorkflowExecutionResult
            {
                IsSuccess = true,
                ExecutionId = execution.Id,
                Status = execution.Status,
                Message = "Workflow execution started from template"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow from template {TemplateId}", templateId);
            return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<WorkflowExecutionResult> ExecuteParallelStepsAsync(Guid executionId, List<Guid> stepIds, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing {Count} parallel steps for execution {ExecutionId}", stepIds.Count, executionId);

            var execution = await _executionRepository.GetByIdAsync(executionId, cancellationToken);
            if (execution == null)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Execution not found" };
            }

            var steps = await _stepRepository.GetByIdsAsync(stepIds, cancellationToken);
            var parallelTasks = new List<Task<bool>>();

            foreach (var step in steps)
            {
                var stepExecution = new WorkflowStepExecution(
                    executionId,
                    step.Id,
                    step.Name,
                    step.StepType,
                    execution.Context);

                _stepExecutionRepository.Add(stepExecution);
                
                // Execute step in parallel
                parallelTasks.Add(ExecuteStepAsync(stepExecution.Id, cancellationToken));
            }

            await _stepExecutionRepository.SaveChangesAsync(cancellationToken);

            // Wait for all parallel steps to complete
            var results = await Task.WhenAll(parallelTasks);
            var allSuccessful = results.All(r => r);

            return new WorkflowExecutionResult
            {
                IsSuccess = allSuccessful,
                ExecutionId = executionId,
                Status = allSuccessful ? "Running" : "Failed",
                Message = $"Parallel execution completed. {results.Count(r => r)}/{results.Length} steps successful"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing parallel steps for execution {ExecutionId}", executionId);
            return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<WorkflowExecutionResult> ExecuteConditionalBranchAsync(Guid executionId, Guid stepId, Dictionary<string, object> context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing conditional branch for step {StepId} in execution {ExecutionId}", stepId, executionId);

            var step = await _stepRepository.GetByIdAsync(stepId, cancellationToken);
            if (step == null)
            {
                return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = "Step not found" };
            }

            var nextSteps = step.GetNextStepsForCondition(context);
            
            if (nextSteps.Count == 0)
            {
                return new WorkflowExecutionResult
                {
                    IsSuccess = true,
                    ExecutionId = executionId,
                    Status = "Running",
                    Message = "No next steps to execute based on conditions"
                };
            }

            // Execute next steps based on condition evaluation
            var stepExecutions = new List<WorkflowStepExecution>();
            foreach (var nextStepName in nextSteps)
            {
                var nextStep = await _stepRepository.GetByNameAsync(step.WorkflowId, nextStepName, cancellationToken);
                if (nextStep != null)
                {
                    var stepExecution = new WorkflowStepExecution(
                        executionId,
                        nextStep.Id,
                        nextStep.Name,
                        nextStep.StepType,
                        context);

                    stepExecutions.Add(stepExecution);
                    _stepExecutionRepository.Add(stepExecution);
                }
            }

            await _stepExecutionRepository.SaveChangesAsync(cancellationToken);

            // Execute the conditional steps
            foreach (var stepExecution in stepExecutions)
            {
                _ = Task.Run(async () => await ExecuteStepAsync(stepExecution.Id, CancellationToken.None));
            }

            return new WorkflowExecutionResult
            {
                IsSuccess = true,
                ExecutionId = executionId,
                Status = "Running",
                Message = $"Conditional branch executed. {stepExecutions.Count} steps started"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing conditional branch for step {StepId}", stepId);
            return new WorkflowExecutionResult { IsSuccess = false, ErrorMessage = ex.Message };
        }
    }

    public async Task<bool> RetryFailedStepAsync(Guid executionId, Guid stepExecutionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Retrying failed step execution {StepExecutionId}", stepExecutionId);

            var stepExecution = await _stepExecutionRepository.GetByIdAsync(stepExecutionId, cancellationToken);
            if (stepExecution == null)
            {
                return false;
            }

            if (!stepExecution.CanRetry())
            {
                _logger.LogWarning("Step execution {StepExecutionId} cannot be retried", stepExecutionId);
                return false;
            }

            // Calculate retry delay based on attempt number
            var retryDelay = CalculateRetryDelay(stepExecution.AttemptNumber);
            stepExecution.ScheduleRetry(retryDelay);
            
            await _stepExecutionRepository.SaveChangesAsync(cancellationToken);

            // Schedule the retry execution
            _ = Task.Delay(retryDelay, cancellationToken).ContinueWith(async _ =>
            {
                await ExecuteStepAsync(stepExecutionId, CancellationToken.None);
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying step execution {StepExecutionId}", stepExecutionId);
            return false;
        }
    }

    public async Task<WorkflowPerformanceMetrics> GetWorkflowPerformanceAsync(Guid workflowId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var executions = await _executionRepository.GetByWorkflowIdAsync(workflowId, from, cancellationToken);

            var metrics = new WorkflowPerformanceMetrics
            {
                WorkflowId = workflowId,
                TotalExecutions = executions.Count,
                SuccessfulExecutions = executions.Count(e => e.Status == "Completed"),
                FailedExecutions = executions.Count(e => e.Status == "Failed"),
                AverageExecutionTime = executions.Where(e => e.EndTime.HasValue)
                    .Select(e => e.GetExecutionDuration().TotalMinutes)
                    .DefaultIfEmpty(0)
                    .Average(),
                SuccessRate = executions.Count > 0 ? (double)executions.Count(e => e.Status == "Completed") / executions.Count * 100 : 0,
                FromDate = from,
                ToDate = DateTime.UtcNow
            };

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow performance for workflow {WorkflowId}", workflowId);
            return new WorkflowPerformanceMetrics { WorkflowId = workflowId };
        }
    }

    public async Task<List<WorkflowExecutionSummary>> GetActiveExecutionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var activeExecutions = await _executionRepository.GetActiveExecutionsAsync(cancellationToken);
            
            return activeExecutions.Select(e => new WorkflowExecutionSummary
            {
                ExecutionId = e.Id,
                WorkflowId = e.WorkflowId,
                Status = e.Status,
                StartTime = e.StartTime,
                TriggeredBy = e.TriggeredBy,
                CurrentStep = e.CurrentStepId,
                ProgressPercentage = CalculateProgressPercentage(e)
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active executions");
            return new List<WorkflowExecutionSummary>();
        }
    }

    public async Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(Dictionary<string, object> definition, CancellationToken cancellationToken = default)
    {
        var result = new WorkflowValidationResult { IsValid = true };

        try
        {
            // Validate required fields
            if (!definition.ContainsKey("name"))
            {
                result.Errors.Add("Workflow name is required");
            }

            if (!definition.ContainsKey("steps") || !(definition["steps"] is List<object> steps) || steps.Count == 0)
            {
                result.Errors.Add("Workflow must have at least one step");
            }

            // Validate steps
            if (definition.TryGetValue("steps", out var stepsObj) && stepsObj is List<object> stepsList)
            {
                for (int i = 0; i < stepsList.Count; i++)
                {
                    if (stepsList[i] is Dictionary<string, object> step)
                    {
                        ValidateStep(step, i, result);
                    }
                }
            }

            result.IsValid = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating workflow definition");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    public async Task<WorkflowTemplate> CreateWorkflowFromTemplateAsync(Guid templateId, Dictionary<string, object> customizations, string createdBy, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(templateId, cancellationToken);
        if (template == null)
        {
            throw new InvalidOperationException("Template not found");
        }

        // Create workflow definition from template
        var workflowDefinition = new Dictionary<string, object>(template.TemplateDefinition);
        
        // Apply customizations
        foreach (var customization in customizations)
        {
            workflowDefinition[customization.Key] = customization.Value;
        }

        var workflow = new Workflow(
            $"{template.Name}_{DateTime.UtcNow:yyyyMMddHHmmss}",
            template.Description,
            template.Category,
            template.Version,
            workflowDefinition,
            template.DefaultConfiguration,
            "Event", // Default trigger type
            new Dictionary<string, object>(),
            createdBy);

        _workflowRepository.Add(workflow);
        await _workflowRepository.SaveChangesAsync(cancellationToken);

        return template;
    }

    public async Task<Dictionary<string, object>> GenerateWorkflowVisualizationAsync(Guid workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(workflowId, cancellationToken);
            if (workflow == null)
            {
                return new Dictionary<string, object>();
            }

            var steps = await _stepRepository.GetByWorkflowIdAsync(workflowId, cancellationToken);
            
            var visualization = new Dictionary<string, object>
            {
                ["workflowId"] = workflowId,
                ["name"] = workflow.Name,
                ["nodes"] = steps.Select(s => new
                {
                    id = s.Id,
                    name = s.Name,
                    type = s.StepType,
                    order = s.Order,
                    isParallel = s.IsParallel,
                    position = new { x = s.Order * 200, y = s.IsParallel ? 100 : 0 }
                }).ToList(),
                ["edges"] = GenerateWorkflowEdges(steps),
                ["layout"] = "hierarchical"
            };

            return visualization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating workflow visualization for workflow {WorkflowId}", workflowId);
            return new Dictionary<string, object>();
        }
    }

    private async Task<bool> ExecuteWorkflowStepsAsync(Guid executionId, CancellationToken cancellationToken)
    {
        // Implementation for executing workflow steps
        // This would contain the main workflow execution logic
        return true;
    }

    private async Task<bool> ExecuteStepAsync(Guid stepExecutionId, CancellationToken cancellationToken)
    {
        // Implementation for executing individual steps
        // This would contain step-specific execution logic
        return true;
    }

    private TimeSpan CalculateRetryDelay(int attemptNumber)
    {
        // Exponential backoff: 2^attempt minutes, max 60 minutes
        var delayMinutes = Math.Min(Math.Pow(2, attemptNumber), 60);
        return TimeSpan.FromMinutes(delayMinutes);
    }

    private double CalculateProgressPercentage(WorkflowExecution execution)
    {
        // Simple progress calculation - can be enhanced
        return execution.CurrentStepIndex > 0 ? (double)execution.CurrentStepIndex / 10 * 100 : 0;
    }

    private void ValidateStep(Dictionary<string, object> step, int index, WorkflowValidationResult result)
    {
        if (!step.ContainsKey("name"))
        {
            result.Errors.Add($"Step {index}: Name is required");
        }

        if (!step.ContainsKey("type"))
        {
            result.Errors.Add($"Step {index}: Type is required");
        }
    }

    private List<object> GenerateWorkflowEdges(List<WorkflowStep> steps)
    {
        var edges = new List<object>();
        
        foreach (var step in steps)
        {
            foreach (var nextStepName in step.NextSteps)
            {
                var nextStep = steps.FirstOrDefault(s => s.Name == nextStepName);
                if (nextStep != null)
                {
                    edges.Add(new
                    {
                        source = step.Id,
                        target = nextStep.Id,
                        type = "default"
                    });
                }
            }
        }

        return edges;
    }
}
