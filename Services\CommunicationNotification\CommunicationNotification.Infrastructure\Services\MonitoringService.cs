using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Comprehensive monitoring service with real-time alerting and health tracking
/// </summary>
public interface IMonitoringService
{
    Task<SystemHealthStatus> GetSystemHealthAsync();
    Task<List<Alert>> GetActiveAlertsAsync();
    Task<MonitoringDashboard> GetDashboardDataAsync();
    Task TriggerAlertAsync(Alert alert);
    Task ResolveAlertAsync(Guid alertId, string resolvedBy, string resolution);
    Task<List<HealthCheckResult>> RunHealthChecksAsync();
    Task<SystemMetrics> GetSystemMetricsAsync();
    Task<ServiceMetrics> GetServiceMetricsAsync();
    Task StartMonitoringAsync();
    Task StopMonitoringAsync();
    void RecordMetric(string metricName, double value, Dictionary<string, object>? tags = null);
    void RecordEvent(string eventName, string description, EventSeverity severity, Dictionary<string, object>? metadata = null);
}

public class MonitoringService : BackgroundService, IMonitoringService
{
    private readonly ILogger<MonitoringService> _logger;
    private readonly IAlertingService _alertingService;
    private readonly IPerformanceMonitoringService _performanceMonitoring;
    private readonly MonitoringConfiguration _configuration;
    private readonly ConcurrentDictionary<Guid, Alert> _activeAlerts;
    private readonly ConcurrentQueue<MonitoringEvent> _events;
    private readonly ConcurrentDictionary<string, HealthCheckResult> _healthCheckResults;
    private readonly Timer _healthCheckTimer;
    private readonly Timer _metricsCollectionTimer;
    private bool _isMonitoring;

    public MonitoringService(
        ILogger<MonitoringService> logger,
        IAlertingService alertingService,
        IPerformanceMonitoringService performanceMonitoring,
        IOptions<MonitoringConfiguration> configuration)
    {
        _logger = logger;
        _alertingService = alertingService;
        _performanceMonitoring = performanceMonitoring;
        _configuration = configuration.Value;
        _activeAlerts = new ConcurrentDictionary<Guid, Alert>();
        _events = new ConcurrentQueue<MonitoringEvent>();
        _healthCheckResults = new ConcurrentDictionary<string, HealthCheckResult>();

        // Initialize timers
        _healthCheckTimer = new Timer(RunPeriodicHealthChecks, null, Timeout.Infinite, Timeout.Infinite);
        _metricsCollectionTimer = new Timer(CollectSystemMetrics, null, Timeout.Infinite, Timeout.Infinite);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Monitoring service started");
        _isMonitoring = true;

        // Start periodic tasks
        _healthCheckTimer.Change(TimeSpan.Zero, TimeSpan.FromMinutes(_configuration.HealthCheckIntervalMinutes));
        _metricsCollectionTimer.Change(TimeSpan.Zero, TimeSpan.FromMinutes(_configuration.MetricsCollectionIntervalMinutes));

        while (!stoppingToken.IsCancellationRequested && _isMonitoring)
        {
            try
            {
                await MonitorSystemHealthAsync(stoppingToken);
                await ProcessAlertsAsync(stoppingToken);
                await CleanupOldDataAsync(stoppingToken);

                await Task.Delay(TimeSpan.FromSeconds(_configuration.MonitoringLoopIntervalSeconds), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in monitoring service execution loop");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Monitoring service stopped");
    }

    public async Task<SystemHealthStatus> GetSystemHealthAsync()
    {
        try
        {
            var healthChecks = await RunHealthChecksAsync();
            var systemMetrics = await GetSystemMetricsAsync();
            var serviceMetrics = await GetServiceMetricsAsync();
            var activeAlerts = _activeAlerts.Values.ToList();

            var overallStatus = DetermineOverallHealth(healthChecks, activeAlerts);

            return new SystemHealthStatus
            {
                OverallStatus = overallStatus,
                LastUpdated = DateTime.UtcNow,
                HealthChecks = healthChecks,
                SystemMetrics = systemMetrics,
                ServiceMetrics = serviceMetrics,
                ActiveAlerts = activeAlerts.Count,
                CriticalAlerts = activeAlerts.Count(a => a.Severity == AlertSeverity.Critical),
                Uptime = GetSystemUptime()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system health status");
            return new SystemHealthStatus
            {
                OverallStatus = HealthStatus.Unknown,
                LastUpdated = DateTime.UtcNow,
                Uptime = GetSystemUptime()
            };
        }
    }

    public async Task<List<Alert>> GetActiveAlertsAsync()
    {
        return _activeAlerts.Values
            .Where(a => a.Status == AlertStatus.Active)
            .OrderByDescending(a => a.CreatedAt)
            .ToList();
    }

    public async Task<MonitoringDashboard> GetDashboardDataAsync()
    {
        try
        {
            var systemHealth = await GetSystemHealthAsync();
            var recentEvents = GetRecentEvents(100);
            var performanceMetrics = await _performanceMonitoring.GetRealTimeMetricsAsync();

            return new MonitoringDashboard
            {
                SystemHealth = systemHealth,
                RecentEvents = recentEvents,
                PerformanceMetrics = performanceMetrics,
                AlertSummary = CalculateAlertSummary(),
                ServiceStatus = await GetServiceStatusSummaryAsync(),
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard data");
            return new MonitoringDashboard { LastUpdated = DateTime.UtcNow };
        }
    }

    public async Task TriggerAlertAsync(Alert alert)
    {
        try
        {
            alert.Id = Guid.NewGuid();
            alert.CreatedAt = DateTime.UtcNow;
            alert.Status = AlertStatus.Active;

            _activeAlerts.TryAdd(alert.Id, alert);

            // Send alert through alerting service
            await _alertingService.SendAlertAsync(alert);

            // Record event
            RecordEvent("AlertTriggered", $"Alert triggered: {alert.Title}",
                MapSeverityToEventSeverity(alert.Severity),
                new Dictionary<string, object>
                {
                    ["alertId"] = alert.Id,
                    ["alertType"] = alert.Type,
                    ["severity"] = alert.Severity.ToString()
                });

            _logger.LogWarning("Alert triggered: {AlertId} - {Title} ({Severity})",
                alert.Id, alert.Title, alert.Severity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering alert: {AlertTitle}", alert.Title);
        }
    }

    public async Task ResolveAlertAsync(Guid alertId, string resolvedBy, string resolution)
    {
        try
        {
            if (_activeAlerts.TryGetValue(alertId, out var alert))
            {
                alert.Status = AlertStatus.Resolved;
                alert.ResolvedAt = DateTime.UtcNow;
                alert.ResolvedBy = resolvedBy;
                alert.Resolution = resolution;

                // Send resolution notification
                await _alertingService.SendAlertResolutionAsync(alert);

                RecordEvent("AlertResolved", $"Alert resolved: {alert.Title}", EventSeverity.Info,
                    new Dictionary<string, object>
                    {
                        ["alertId"] = alertId,
                        ["resolvedBy"] = resolvedBy,
                        ["resolution"] = resolution
                    });

                _logger.LogInformation("Alert resolved: {AlertId} by {ResolvedBy}", alertId, resolvedBy);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving alert: {AlertId}", alertId);
        }
    }

    public async Task<List<HealthCheckResult>> RunHealthChecksAsync()
    {
        var results = new List<HealthCheckResult>();

        try
        {
            // Database health check
            var dbResult = await CheckDatabaseHealthAsync();
            results.Add(dbResult);
            _healthCheckResults.AddOrUpdate("database", dbResult, (k, v) => dbResult);

            // Cache health check
            var cacheResult = await CheckCacheHealthAsync();
            results.Add(cacheResult);
            _healthCheckResults.AddOrUpdate("cache", cacheResult, (k, v) => cacheResult);

            // External services health check
            var externalResult = await CheckExternalServicesHealthAsync();
            results.Add(externalResult);
            _healthCheckResults.AddOrUpdate("external_services", externalResult, (k, v) => externalResult);

            // Message queue health check
            var queueResult = await CheckMessageQueueHealthAsync();
            results.Add(queueResult);
            _healthCheckResults.AddOrUpdate("message_queue", queueResult, (k, v) => queueResult);

            // Disk space health check
            var diskResult = await CheckDiskSpaceHealthAsync();
            results.Add(diskResult);
            _healthCheckResults.AddOrUpdate("disk_space", diskResult, (k, v) => diskResult);

            // Memory health check
            var memoryResult = await CheckMemoryHealthAsync();
            results.Add(memoryResult);
            _healthCheckResults.AddOrUpdate("memory", memoryResult, (k, v) => memoryResult);

            _logger.LogDebug("Completed {Count} health checks", results.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running health checks");
        }

        return results;
    }

    public async Task<SystemMetrics> GetSystemMetricsAsync()
    {
        try
        {
            var process = Process.GetCurrentProcess();

            return new SystemMetrics
            {
                CpuUsagePercent = await GetCpuUsageAsync(),
                MemoryUsageMB = process.WorkingSet64 / 1024 / 1024,
                MemoryUsagePercent = await GetMemoryUsagePercentAsync(),
                DiskUsagePercent = await GetDiskUsagePercentAsync(),
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount,
                UptimeSeconds = (DateTime.UtcNow - Process.GetCurrentProcess().StartTime).TotalSeconds,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system metrics");
            return new SystemMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<ServiceMetrics> GetServiceMetricsAsync()
    {
        try
        {
            var performanceMetrics = await _performanceMonitoring.GetRealTimeMetricsAsync();

            return new ServiceMetrics
            {
                RequestsPerSecond = (double)(performanceMetrics.GetValueOrDefault("eventsPerMinute", 0.0)) / 60.0,
                AverageResponseTimeMs = (double)(performanceMetrics.GetValueOrDefault("averageResponseTime", 0.0)),
                ErrorRate = (double)(performanceMetrics.GetValueOrDefault("errorRate", 0.0)),
                ActiveConnections = (int)(performanceMetrics.GetValueOrDefault("activeOperations", 0)),
                QueuedMessages = await GetQueuedMessageCountAsync(),
                ProcessedMessages = await GetProcessedMessageCountAsync(),
                CacheHitRate = (double)(performanceMetrics.GetValueOrDefault("cacheHitRate", 0.0)),
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service metrics");
            return new ServiceMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task StartMonitoringAsync()
    {
        _isMonitoring = true;
        _logger.LogInformation("Monitoring started");
    }

    public async Task StopMonitoringAsync()
    {
        _isMonitoring = false;
        _healthCheckTimer.Change(Timeout.Infinite, Timeout.Infinite);
        _metricsCollectionTimer.Change(Timeout.Infinite, Timeout.Infinite);
        _logger.LogInformation("Monitoring stopped");
    }

    public void RecordMetric(string metricName, double value, Dictionary<string, object>? tags = null)
    {
        try
        {
            var _ = _performanceMonitoring.RecordMetricAsync(metricName, value, tags);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording metric: {MetricName}", metricName);
        }
    }

    public void RecordEvent(string eventName, string description, EventSeverity severity, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var monitoringEvent = new MonitoringEvent
            {
                Id = Guid.NewGuid(),
                Name = eventName,
                Description = description,
                Severity = severity,
                Metadata = metadata ?? new Dictionary<string, object>(),
                Timestamp = DateTime.UtcNow
            };

            _events.Enqueue(monitoringEvent);

            // Trim events queue if it gets too large
            if (_events.Count > _configuration.MaxEventHistory)
            {
                for (int i = 0; i < _configuration.MaxEventHistory / 4; i++)
                {
                    _events.TryDequeue(out _);
                }
            }

            _logger.LogDebug("Recorded monitoring event: {EventName} - {Description}", eventName, description);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording monitoring event: {EventName}", eventName);
        }
    }

    // Private helper methods
    private async Task MonitorSystemHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var healthChecks = await RunHealthChecksAsync();

            foreach (var healthCheck in healthChecks.Where(h => h.Status != HealthStatus.Healthy))
            {
                await TriggerHealthAlert(healthCheck);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring system health");
        }
    }

    private async Task ProcessAlertsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var expiredAlerts = _activeAlerts.Values
                .Where(a => a.Status == AlertStatus.Active &&
                           DateTime.UtcNow - a.CreatedAt > TimeSpan.FromHours(_configuration.AlertExpirationHours))
                .ToList();

            foreach (var alert in expiredAlerts)
            {
                await ResolveAlertAsync(alert.Id, "System", "Auto-resolved due to expiration");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing alerts");
        }
    }

    private async Task CleanupOldDataAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Clean up old resolved alerts
            var cutoffDate = DateTime.UtcNow.AddDays(-_configuration.AlertRetentionDays);
            var oldAlerts = _activeAlerts.Values
                .Where(a => a.Status == AlertStatus.Resolved && a.ResolvedAt < cutoffDate)
                .ToList();

            foreach (var alert in oldAlerts)
            {
                _activeAlerts.TryRemove(alert.Id, out _);
            }

            _logger.LogDebug("Cleaned up {Count} old alerts", oldAlerts.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old data");
        }
    }

    private void RunPeriodicHealthChecks(object? state)
    {
        var _ = Task.Run(async () =>
        {
            try
            {
                await RunHealthChecksAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in periodic health checks");
            }
        });
    }

    private void CollectSystemMetrics(object? state)
    {
        var _ = Task.Run(async () =>
        {
            try
            {
                var systemMetrics = await GetSystemMetricsAsync();
                var serviceMetrics = await GetServiceMetricsAsync();

                // Record metrics for monitoring
                RecordMetric("system.cpu_usage", systemMetrics.CpuUsagePercent);
                RecordMetric("system.memory_usage", systemMetrics.MemoryUsagePercent);
                RecordMetric("system.disk_usage", systemMetrics.DiskUsagePercent);
                RecordMetric("service.requests_per_second", serviceMetrics.RequestsPerSecond);
                RecordMetric("service.error_rate", serviceMetrics.ErrorRate);
                RecordMetric("service.cache_hit_rate", serviceMetrics.CacheHitRate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting system metrics");
            }
        });
    }

    // Missing method implementations
    private async Task<double> GetCpuUsageAsync()
    {
        // Placeholder - would use performance counters
        return new Random().NextDouble() * 100;
    }

    private async Task<double> GetMemoryUsagePercentAsync()
    {
        var totalMemory = GC.GetTotalMemory(false);
        // Placeholder calculation
        return (totalMemory / (1024.0 * 1024.0 * 1024.0)) * 100; // Convert to GB and percentage
    }

    private async Task<double> GetDiskUsagePercentAsync()
    {
        // Placeholder - would check actual disk usage
        return new Random().NextDouble() * 100;
    }

    private async Task<int> GetQueuedMessageCountAsync()
    {
        // Placeholder - would get from actual message queue
        return new Random().Next(0, 100);
    }

    private async Task<int> GetProcessedMessageCountAsync()
    {
        // Placeholder - would get from actual processing metrics
        return new Random().Next(1000, 10000);
    }

    private async Task<HealthCheckResult> CheckDiskSpaceHealthAsync()
    {
        try
        {
            var diskUsage = await GetDiskUsagePercentAsync();
            var status = diskUsage > 90 ? HealthStatus.Critical :
                        diskUsage > 80 ? HealthStatus.Degraded : HealthStatus.Healthy;

            return new HealthCheckResult
            {
                Name = "Disk Space",
                Status = status,
                Description = $"Disk usage is at {diskUsage:F1}%",
                Data = new Dictionary<string, object> { ["diskUsagePercent"] = diskUsage }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "Disk Space",
                Status = HealthStatus.Unhealthy,
                Description = $"Disk space check failed: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    private async Task<HealthCheckResult> CheckMemoryHealthAsync()
    {
        try
        {
            var memoryUsage = await GetMemoryUsagePercentAsync();
            var status = memoryUsage > 90 ? HealthStatus.Critical :
                        memoryUsage > 80 ? HealthStatus.Degraded : HealthStatus.Healthy;

            return new HealthCheckResult
            {
                Name = "Memory",
                Status = status,
                Description = $"Memory usage is at {memoryUsage:F1}%",
                Data = new Dictionary<string, object> { ["memoryUsagePercent"] = memoryUsage }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "Memory",
                Status = HealthStatus.Unhealthy,
                Description = $"Memory check failed: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    private async Task TriggerHealthAlert(HealthCheckResult healthCheck)
    {
        var alertKey = $"health_{healthCheck.Name}";

        // Check if we already have an active alert for this health check
        var existingAlert = _activeAlerts.Values
            .FirstOrDefault(a => a.Type == alertKey && a.Status == AlertStatus.Active);

        if (existingAlert != null)
        {
            return; // Don't create duplicate alerts
        }

        var severity = healthCheck.Status switch
        {
            HealthStatus.Critical => AlertSeverity.Critical,
            HealthStatus.Unhealthy => AlertSeverity.High,
            HealthStatus.Degraded => AlertSeverity.Medium,
            _ => AlertSeverity.Low
        };

        var alert = new Alert
        {
            Type = alertKey,
            Title = $"Health Check Failed: {healthCheck.Name}",
            Message = healthCheck.Description ?? $"Health check '{healthCheck.Name}' is {healthCheck.Status}",
            Severity = severity,
            Source = "MonitoringService",
            Metadata = healthCheck.Data ?? new Dictionary<string, object>()
        };

        await TriggerAlertAsync(alert);
    }

    private HealthStatus DetermineOverallHealth(List<HealthCheckResult> healthChecks, List<Alert> activeAlerts)
    {
        if (activeAlerts.Any(a => a.Severity == AlertSeverity.Critical))
            return HealthStatus.Critical;

        if (healthChecks.Any(h => h.Status == HealthStatus.Critical))
            return HealthStatus.Critical;

        if (healthChecks.Any(h => h.Status == HealthStatus.Unhealthy))
            return HealthStatus.Unhealthy;

        if (healthChecks.Any(h => h.Status == HealthStatus.Degraded) ||
            activeAlerts.Any(a => a.Severity == AlertSeverity.High))
            return HealthStatus.Degraded;

        return HealthStatus.Healthy;
    }

    private TimeSpan GetSystemUptime()
    {
        return DateTime.UtcNow - Process.GetCurrentProcess().StartTime;
    }

    private List<MonitoringEvent> GetRecentEvents(int count)
    {
        return _events.TakeLast(count).OrderByDescending(e => e.Timestamp).ToList();
    }

    private AlertSummary CalculateAlertSummary()
    {
        var activeAlerts = _activeAlerts.Values.Where(a => a.Status == AlertStatus.Active).ToList();

        return new AlertSummary
        {
            Total = activeAlerts.Count,
            Critical = activeAlerts.Count(a => a.Severity == AlertSeverity.Critical),
            High = activeAlerts.Count(a => a.Severity == AlertSeverity.High),
            Medium = activeAlerts.Count(a => a.Severity == AlertSeverity.Medium),
            Low = activeAlerts.Count(a => a.Severity == AlertSeverity.Low)
        };
    }

    private async Task<Dictionary<string, ServiceStatus>> GetServiceStatusSummaryAsync()
    {
        var serviceStatus = new Dictionary<string, ServiceStatus>();

        foreach (var healthCheck in _healthCheckResults)
        {
            serviceStatus[healthCheck.Key] = new ServiceStatus
            {
                Name = healthCheck.Key,
                Status = healthCheck.Value.Status,
                LastChecked = DateTime.UtcNow,
                ResponseTime = healthCheck.Value.Duration?.TotalMilliseconds ?? 0
            };
        }

        return serviceStatus;
    }

    private EventSeverity MapSeverityToEventSeverity(AlertSeverity alertSeverity)
    {
        return alertSeverity switch
        {
            AlertSeverity.Critical => EventSeverity.Critical,
            AlertSeverity.High => EventSeverity.Error,
            AlertSeverity.Medium => EventSeverity.Warning,
            AlertSeverity.Low => EventSeverity.Info,
            _ => EventSeverity.Info
        };
    }

    // Health check implementations
    private async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate database health check
            // In real implementation, this would test database connectivity
            await Task.Delay(10); // Simulate database query

            stopwatch.Stop();

            return new HealthCheckResult
            {
                Name = "Database",
                Status = HealthStatus.Healthy,
                Description = "Database is responding normally",
                Duration = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["responseTime"] = stopwatch.ElapsedMilliseconds,
                    ["connectionPool"] = "Available"
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "Database",
                Status = HealthStatus.Unhealthy,
                Description = $"Database health check failed: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    private async Task<HealthCheckResult> CheckCacheHealthAsync()
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate cache health check
            await Task.Delay(5);

            stopwatch.Stop();

            return new HealthCheckResult
            {
                Name = "Cache",
                Status = HealthStatus.Healthy,
                Description = "Cache is responding normally",
                Duration = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["responseTime"] = stopwatch.ElapsedMilliseconds,
                    ["hitRate"] = 0.85
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "Cache",
                Status = HealthStatus.Unhealthy,
                Description = $"Cache health check failed: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    private async Task<HealthCheckResult> CheckExternalServicesHealthAsync()
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate external services health check
            await Task.Delay(20);

            stopwatch.Stop();

            return new HealthCheckResult
            {
                Name = "External Services",
                Status = HealthStatus.Healthy,
                Description = "External services are responding normally",
                Duration = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["responseTime"] = stopwatch.ElapsedMilliseconds,
                    ["servicesChecked"] = 5,
                    ["servicesHealthy"] = 5
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "External Services",
                Status = HealthStatus.Degraded,
                Description = $"Some external services are experiencing issues: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    private async Task<HealthCheckResult> CheckMessageQueueHealthAsync()
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate message queue health check
            await Task.Delay(15);

            stopwatch.Stop();

            return new HealthCheckResult
            {
                Name = "Message Queue",
                Status = HealthStatus.Healthy,
                Description = "Message queue is processing normally",
                Duration = stopwatch.Elapsed,
                Data = new Dictionary<string, object>
                {
                    ["responseTime"] = stopwatch.ElapsedMilliseconds,
                    ["queueDepth"] = 25,
                    ["processingRate"] = 150
                }
            };
        }
        catch (Exception ex)
        {
            return new HealthCheckResult
            {
                Name = "Message Queue",
                Status = HealthStatus.Unhealthy,
                Description = $"Message queue health check failed: {ex.Message}",
                Data = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    public override void Dispose()
    {
        _healthCheckTimer?.Dispose();
        _metricsCollectionTimer?.Dispose();
        base.Dispose();
    }
}
