using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;

namespace AuditCompliance.API.Controllers.V2;

/// <summary>
/// Version 2 of Audit Controller with enhanced features and improved response formats
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("2.0")]
[Authorize]
public class AuditController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuditController> _logger;

    public AuditController(IMediator mediator, ILogger<AuditController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a new audit log entry with enhanced metadata (V2)
    /// </summary>
    [HttpPost("logs")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<AuditLogResponseV2Dto>> CreateAuditLog([FromBody] CreateAuditLogV2Command command)
    {
        try
        {
            var auditLogId = await _mediator.Send(command);
            
            var response = new AuditLogResponseV2Dto
            {
                AuditLogId = auditLogId,
                Status = "Created",
                Message = "Audit log created successfully",
                Timestamp = DateTime.UtcNow,
                Version = "2.0",
                Links = new Dictionary<string, string>
                {
                    ["self"] = $"/api/v2/audit/logs/{auditLogId}",
                    ["trail"] = "/api/v2/audit/trail"
                }
            };

            return CreatedAtAction(nameof(GetAuditLog), new { id = auditLogId, version = "2.0" }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit log");
            return StatusCode(500, new ErrorResponseV2Dto
            {
                Error = "InternalServerError",
                Message = "An error occurred while creating the audit log",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }

    /// <summary>
    /// Get audit log by ID with enhanced details (V2)
    /// </summary>
    [HttpGet("logs/{id}")]
    [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
    public async Task<ActionResult<AuditLogDetailV2Dto>> GetAuditLog(Guid id)
    {
        try
        {
            var query = new GetAuditLogByIdQuery { Id = id };
            var auditLog = await _mediator.Send(query);

            if (auditLog == null)
            {
                return NotFound(new ErrorResponseV2Dto
                {
                    Error = "NotFound",
                    Message = $"Audit log with ID {id} not found",
                    Timestamp = DateTime.UtcNow,
                    Version = "2.0"
                });
            }

            var response = new AuditLogDetailV2Dto
            {
                Id = auditLog.Id,
                EventType = auditLog.EventType,
                Severity = auditLog.Severity,
                EntityType = auditLog.EntityType,
                Action = auditLog.Action,
                Description = auditLog.Description,
                UserId = auditLog.UserId,
                UserName = auditLog.UserName,
                UserRole = auditLog.UserRole,
                EntityId = auditLog.EntityId,
                OldValues = auditLog.OldValues,
                NewValues = auditLog.NewValues,
                Timestamp = auditLog.Timestamp,
                IpAddress = auditLog.Context?.IpAddress,
                UserAgent = auditLog.Context?.UserAgent,
                SessionId = auditLog.Context?.SessionId,
                CorrelationId = auditLog.Context?.CorrelationId,
                ComplianceFlags = auditLog.ComplianceFlags,
                Version = "2.0",
                Metadata = new AuditLogMetadataV2Dto
                {
                    CreatedAt = auditLog.Timestamp,
                    RetentionDate = auditLog.Timestamp.AddYears(7), // Compliance retention
                    IsArchived = false,
                    ArchiveDate = null,
                    ComplianceScore = CalculateComplianceScore(auditLog),
                    RiskLevel = CalculateRiskLevel(auditLog)
                },
                Links = new Dictionary<string, string>
                {
                    ["self"] = $"/api/v2/audit/logs/{id}",
                    ["trail"] = "/api/v2/audit/trail",
                    ["related"] = $"/api/v2/audit/logs?entityId={auditLog.EntityId}"
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit log {AuditLogId}", id);
            return StatusCode(500, new ErrorResponseV2Dto
            {
                Error = "InternalServerError",
                Message = "An error occurred while retrieving the audit log",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }

    /// <summary>
    /// Search audit trail with enhanced filtering and pagination (V2)
    /// </summary>
    [HttpPost("trail")]
    [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
    public async Task<ActionResult<AuditTrailResponseV2Dto>> GetAuditTrail([FromBody] GetAuditTrailV2Query query)
    {
        try
        {
            var result = await _mediator.Send(query);

            var response = new AuditTrailResponseV2Dto
            {
                Data = result.AuditLogs.Select(log => new AuditLogSummaryV2Dto
                {
                    Id = log.Id,
                    EventType = log.EventType,
                    Severity = log.Severity,
                    EntityType = log.EntityType,
                    Action = log.Action,
                    Description = log.Description,
                    UserName = log.UserName,
                    UserRole = log.UserRole,
                    EntityId = log.EntityId,
                    Timestamp = log.Timestamp,
                    ComplianceScore = CalculateComplianceScore(log),
                    RiskLevel = CalculateRiskLevel(log),
                    Links = new Dictionary<string, string>
                    {
                        ["details"] = $"/api/v2/audit/logs/{log.Id}"
                    }
                }).ToList(),
                Pagination = new PaginationV2Dto
                {
                    CurrentPage = query.PageNumber,
                    PageSize = query.PageSize,
                    TotalCount = result.TotalCount,
                    TotalPages = (int)Math.Ceiling((double)result.TotalCount / query.PageSize),
                    HasPreviousPage = query.PageNumber > 1,
                    HasNextPage = query.PageNumber * query.PageSize < result.TotalCount
                },
                Filters = new AuditTrailFiltersV2Dto
                {
                    Applied = new Dictionary<string, object>
                    {
                        ["UserId"] = query.UserId,
                        ["EntityType"] = query.EntityType,
                        ["EntityId"] = query.EntityId,
                        ["EventType"] = query.EventType,
                        ["MinSeverity"] = query.MinSeverity,
                        ["FromDate"] = query.FromDate,
                        ["ToDate"] = query.ToDate,
                        ["SearchTerm"] = query.SearchTerm
                    }.Where(kvp => kvp.Value != null).ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Available = GetAvailableFilters()
                },
                Summary = new AuditTrailSummaryV2Dto
                {
                    TotalEvents = result.TotalCount,
                    CriticalEvents = result.AuditLogs.Count(l => l.Severity == "Critical"),
                    HighSeverityEvents = result.AuditLogs.Count(l => l.Severity == "High"),
                    UniqueUsers = result.AuditLogs.Select(l => l.UserId).Distinct().Count(),
                    UniqueEntities = result.AuditLogs.Select(l => l.EntityId).Distinct().Count(),
                    DateRange = new
                    {
                        From = query.FromDate,
                        To = query.ToDate
                    }
                },
                Version = "2.0",
                Timestamp = DateTime.UtcNow,
                Links = new Dictionary<string, string>
                {
                    ["self"] = "/api/v2/audit/trail",
                    ["export"] = "/api/v2/audit/trail/export",
                    ["analytics"] = "/api/v2/analytics/audit-trends"
                }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail");
            return StatusCode(500, new ErrorResponseV2Dto
            {
                Error = "InternalServerError",
                Message = "An error occurred while retrieving the audit trail",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }

    /// <summary>
    /// Get security events with enhanced analysis (V2)
    /// </summary>
    [HttpGet("security-events")]
    [Authorize(Roles = "Admin,Auditor")]
    public async Task<ActionResult<SecurityEventsResponseV2Dto>> GetSecurityEvents(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate,
        [FromQuery] string? severity = null,
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1)
    {
        try
        {
            var query = new GetSecurityEventsV2Query
            {
                FromDate = fromDate,
                ToDate = toDate,
                Severity = severity,
                PageSize = pageSize,
                PageNumber = pageNumber
            };

            var events = await _mediator.Send(query);

            var response = new SecurityEventsResponseV2Dto
            {
                Events = events.Select(e => new SecurityEventV2Dto
                {
                    Id = e.Id,
                    EventType = e.EventType,
                    Severity = e.Severity,
                    Description = e.Description,
                    UserId = e.UserId,
                    UserName = e.UserName,
                    IpAddress = e.Context?.IpAddress,
                    UserAgent = e.Context?.UserAgent,
                    Timestamp = e.Timestamp,
                    ThreatLevel = CalculateThreatLevel(e),
                    RequiresAction = RequiresSecurityAction(e),
                    Links = new Dictionary<string, string>
                    {
                        ["details"] = $"/api/v2/audit/logs/{e.Id}",
                        ["investigate"] = $"/api/v2/security/investigate/{e.Id}"
                    }
                }).ToList(),
                Summary = new SecurityEventsSummaryV2Dto
                {
                    TotalEvents = events.Count,
                    CriticalEvents = events.Count(e => e.Severity == "Critical"),
                    HighThreatEvents = events.Count(e => CalculateThreatLevel(e) == "High"),
                    UniqueUsers = events.Select(e => e.UserId).Distinct().Count(),
                    UniqueIpAddresses = events.Select(e => e.Context?.IpAddress).Where(ip => !string.IsNullOrEmpty(ip)).Distinct().Count(),
                    TimeRange = new { From = fromDate, To = toDate }
                },
                Version = "2.0",
                Timestamp = DateTime.UtcNow
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events");
            return StatusCode(500, new ErrorResponseV2Dto
            {
                Error = "InternalServerError",
                Message = "An error occurred while retrieving security events",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }

    /// <summary>
    /// Export audit trail data (V2)
    /// </summary>
    [HttpPost("trail/export")]
    [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
    public async Task<ActionResult<ExportResponseV2Dto>> ExportAuditTrail([FromBody] ExportAuditTrailV2Command command)
    {
        try
        {
            var exportId = await _mediator.Send(command);

            var response = new ExportResponseV2Dto
            {
                ExportId = exportId,
                Status = "Processing",
                Message = "Export request has been queued for processing",
                EstimatedCompletionTime = DateTime.UtcNow.AddMinutes(5),
                Format = command.Format,
                Version = "2.0",
                Links = new Dictionary<string, string>
                {
                    ["status"] = $"/api/v2/audit/exports/{exportId}/status",
                    ["download"] = $"/api/v2/audit/exports/{exportId}/download"
                }
            };

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting audit trail");
            return StatusCode(500, new ErrorResponseV2Dto
            {
                Error = "InternalServerError",
                Message = "An error occurred while processing the export request",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }

    // Private helper methods
    private float CalculateComplianceScore(dynamic auditLog)
    {
        // Calculate compliance score based on audit log properties
        return auditLog.Severity switch
        {
            "Critical" => 0.1f,
            "High" => 0.4f,
            "Medium" => 0.7f,
            "Low" => 0.9f,
            _ => 1.0f
        };
    }

    private string CalculateRiskLevel(dynamic auditLog)
    {
        return auditLog.Severity switch
        {
            "Critical" => "High",
            "High" => "Medium",
            _ => "Low"
        };
    }

    private string CalculateThreatLevel(dynamic securityEvent)
    {
        return securityEvent.Severity switch
        {
            "Critical" => "High",
            "High" => "Medium",
            _ => "Low"
        };
    }

    private bool RequiresSecurityAction(dynamic securityEvent)
    {
        return securityEvent.Severity == "Critical" || securityEvent.Severity == "High";
    }

    private Dictionary<string, object> GetAvailableFilters()
    {
        return new Dictionary<string, object>
        {
            ["EventTypes"] = new[] { "Login", "Logout", "DataAccess", "DataModification", "SystemAccess" },
            ["Severities"] = new[] { "Critical", "High", "Medium", "Low", "Info" },
            ["EntityTypes"] = new[] { "User", "Organization", "ServiceProvider", "Report", "Rating" }
        };
    }
}
