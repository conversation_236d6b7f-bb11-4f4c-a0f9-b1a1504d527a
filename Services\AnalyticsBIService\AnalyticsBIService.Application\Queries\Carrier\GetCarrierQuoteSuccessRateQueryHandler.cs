using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier quote success rate queries
/// </summary>
public class GetCarrierQuoteSuccessRateQueryHandler : IRequestHandler<GetCarrierQuoteSuccessRateQuery, CarrierQuoteSuccessRateDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierQuoteSuccessRateQueryHandler> _logger;

    public GetCarrierQuoteSuccessRateQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierQuoteSuccessRateQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierQuoteSuccessRateDto> Handle(GetCarrierQuoteSuccessRateQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting quote success rate for carrier {CarrierId}", request.CarrierId);

        // Get quote-related events for this carrier
        var quoteEventsResult = await _analyticsEventRepository.GetByDateRangeAsync(
            request.FromDate, request.ToDate, 1, int.MaxValue, cancellationToken);
        var quoteEvents = quoteEventsResult.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("Quote"))
            .ToList();

        // Calculate basic metrics
        var totalQuotesSubmitted = quoteEvents.Count(e => e.EventName == "QuoteSubmitted");
        var quotesAccepted = quoteEvents.Count(e => e.EventName == "QuoteAccepted");
        var quotesRejected = quoteEvents.Count(e => e.EventName == "QuoteRejected");
        var quotesPending = totalQuotesSubmitted - quotesAccepted - quotesRejected;

        var overallSuccessRate = totalQuotesSubmitted > 0 ? (decimal)quotesAccepted / totalQuotesSubmitted * 100 : 0;
        var acceptanceRate = totalQuotesSubmitted > 0 ? (decimal)quotesAccepted / totalQuotesSubmitted * 100 : 0;
        var rejectionRate = totalQuotesSubmitted > 0 ? (decimal)quotesRejected / totalQuotesSubmitted * 100 : 0;

        // Generate success factors
        var successFactors = new List<QuoteSuccessFactorDto>
        {
            new() { Factor = "Competitive Pricing", Impact = 35.0m, Description = "Price competitiveness vs market average", Recommendation = "Monitor market prices and adjust pricing strategy" },
            new() { Factor = "Response Time", Impact = 25.0m, Description = "Speed of quote submission", Recommendation = "Aim for sub-2 hour response times" },
            new() { Factor = "Service Quality", Impact = 20.0m, Description = "Historical service performance", Recommendation = "Maintain high service standards and customer satisfaction" },
            new() { Factor = "Relationship Strength", Impact = 15.0m, Description = "Broker relationship quality", Recommendation = "Invest in broker relationship management" },
            new() { Factor = "Route Expertise", Impact = 5.0m, Description = "Experience on specific routes", Recommendation = "Focus on core competency routes" }
        };

        // Generate route success rates (mock data)
        var routeSuccessRates = new List<RouteQuoteSuccessDto>
        {
            new() { RouteId = "BLR-MUM", RouteName = "Bangalore to Mumbai", QuotesSubmitted = 12, QuotesAccepted = 9, SuccessRate = 75.0m, AveragePrice = 25000m, MarketAveragePrice = 24500m },
            new() { RouteId = "DEL-BLR", RouteName = "Delhi to Bangalore", QuotesSubmitted = 10, QuotesAccepted = 6, SuccessRate = 60.0m, AveragePrice = 28000m, MarketAveragePrice = 27000m }
        };

        // Generate broker success rates (mock data)
        var brokerSuccessRates = new List<BrokerQuoteSuccessDto>
        {
            new() { BrokerId = Guid.NewGuid(), BrokerName = "ABC Logistics", QuotesSubmitted = 7, QuotesAccepted = 6, SuccessRate = 85.7m, AveragePrice = 24500m, RelationshipStrength = "Strong" },
            new() { BrokerId = Guid.NewGuid(), BrokerName = "XYZ Transport", QuotesSubmitted = 5, QuotesAccepted = 3, SuccessRate = 60.0m, AveragePrice = 26000m, RelationshipStrength = "Medium" }
        };

        // Generate success trends
        var successTrends = GenerateQuoteSuccessTrends(request.FromDate, request.ToDate, totalQuotesSubmitted, quotesAccepted);

        // Generate competitive position
        var competitivePosition = new QuoteCompetitivePositionDto
        {
            Ranking = 3,
            TotalCarriers = 15,
            SuccessRatePercentile = 75.0m,
            PricePercentile = 60.0m,
            ResponseTimePercentile = 80.0m,
            OverallPosition = "Challenger"
        };

        return new CarrierQuoteSuccessRateDto
        {
            TotalQuotesSubmitted = totalQuotesSubmitted,
            QuotesAccepted = quotesAccepted,
            QuotesRejected = quotesRejected,
            QuotesPending = quotesPending,
            OverallSuccessRate = overallSuccessRate,
            AcceptanceRate = acceptanceRate,
            RejectionRate = rejectionRate,
            SuccessFactors = successFactors,
            RouteSuccessRates = routeSuccessRates,
            BrokerSuccessRates = brokerSuccessRates,
            SuccessTrends = successTrends,
            CompetitivePosition = competitivePosition
        };
    }

    private List<QuoteSuccessTrendDto> GenerateQuoteSuccessTrends(DateTime fromDate, DateTime toDate, int totalQuotes, int acceptedQuotes)
    {
        var days = (toDate - fromDate).Days;
        var dailyQuoteAverage = (decimal)totalQuotes / Math.Max(days, 1);
        var dailyAcceptedAverage = (decimal)acceptedQuotes / Math.Max(days, 1);
        var trends = new List<QuoteSuccessTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±20% variance
            var quotesSubmitted = Math.Max(0, (int)(dailyQuoteAverage * (1 + variance)));
            var quotesAccepted = Math.Max(0, (int)(dailyAcceptedAverage * (1 + variance)));
            var successRate = quotesSubmitted > 0 ? (decimal)quotesAccepted / quotesSubmitted * 100 : 0;
            var averagePrice = 25000m * (1 + variance * 0.1m); // ±2% price variance

            trends.Add(new QuoteSuccessTrendDto
            {
                Date = date,
                QuotesSubmitted = quotesSubmitted,
                QuotesAccepted = quotesAccepted,
                SuccessRate = successRate,
                AveragePrice = averagePrice
            });
        }

        return trends;
    }
}
