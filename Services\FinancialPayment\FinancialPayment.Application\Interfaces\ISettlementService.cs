using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface ISettlementService
{
    Task<Settlement> CreateSettlementAsync(
        Guid orderId,
        Guid tripId,
        Guid escrowAccountId,
        Money totalAmount,
        List<SettlementDistributionRequest> distributions);

    Task<bool> ProcessSettlementAsync(Guid settlementId);
    Task<Settlement?> GetSettlementByIdAsync(Guid settlementId);
    Task<Settlement?> GetSettlementByOrderIdAsync(Guid orderId);
    Task<Settlement?> GetSettlementByTripIdAsync(Guid tripId);
    Task<List<Settlement>> GetSettlementsByCarrierAsync(Guid carrierId);
    Task<List<Settlement>> GetSettlementsByBrokerAsync(Guid brokerId);
}

public class SettlementDistributionRequest
{
    public Guid RecipientId { get; set; }
    public ParticipantRole RecipientRole { get; set; }
    public Money Amount { get; set; } = null!;
    public DistributionType Type { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class SettlementResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
    public List<DistributionResult> DistributionResults { get; set; } = new();
}

public class DistributionResult
{
    public Guid DistributionId { get; set; }
    public bool IsSuccess { get; set; }
    public string? TransactionId { get; set; }
    public string? ErrorMessage { get; set; }
}
