namespace MobileWorkflow.Application.DTOs;

public class SyncFileDto
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string MimeType { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public int Version { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
    public DateTime? LastSyncedAt { get; set; }
    public string? ConflictReason { get; set; }
    public Dictionary<string, object> FileMetadata { get; set; } = new();
    public Dictionary<string, object> SyncMetadata { get; set; } = new();
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    public bool NeedsSync { get; set; }
    public bool IsInConflict { get; set; }
    public bool IsSynced { get; set; }
    public bool IsSyncing { get; set; }
    public TimeSpan? TimeSinceLastSync { get; set; }
}

public class FileSyncOperationDto
{
    public Guid Id { get; set; }
    public Guid SyncFileId { get; set; }
    public string OperationType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string SourceDevice { get; set; } = string.Empty;
    public string? TargetDevice { get; set; }
    public long BytesTransferred { get; set; }
    public long TotalBytes { get; set; }
    public double? TransferSpeed { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, object> OperationMetadata { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
    public double ProgressPercentage { get; set; }
    public TimeSpan? Duration { get; set; }
    public double? AverageTransferSpeed { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsFailed { get; set; }
    public bool IsInProgress { get; set; }
    public bool IsCancelled { get; set; }
}

public class FileVersionDto
{
    public Guid Id { get; set; }
    public Guid SyncFileId { get; set; }
    public int VersionNumber { get; set; }
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string CreatedByDevice { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, object> VersionMetadata { get; set; } = new();
    public string? StorageLocation { get; set; }
    public bool IsActive { get; set; }
}

public class FileSyncAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalFiles { get; set; }
    public int SyncedFiles { get; set; }
    public int PendingFiles { get; set; }
    public int ConflictedFiles { get; set; }
    public int DeletedFiles { get; set; }
    public int TotalOperations { get; set; }
    public int CompletedOperations { get; set; }
    public int FailedOperations { get; set; }
    public int InProgressOperations { get; set; }
    public long TotalBytesTransferred { get; set; }
    public double AverageTransferSpeed { get; set; }
    public Dictionary<string, int> SyncsByDevice { get; set; } = new();
    public Dictionary<string, int> OperationsByType { get; set; } = new();
    public Dictionary<string, int> ConflictsByReason { get; set; } = new();
}

// Request DTOs
public class CreateSyncFileRequest
{
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string MimeType { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public Dictionary<string, object>? FileMetadata { get; set; }
}

public class UpdateSyncFileRequest
{
    public Guid FileId { get; set; }
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public Dictionary<string, object>? FileMetadata { get; set; }
}

public class StartSyncOperationRequest
{
    public Guid FileId { get; set; }
    public string OperationType { get; set; } = string.Empty; // Upload, Download, Delete
    public string SourceDevice { get; set; } = string.Empty;
    public string? TargetDevice { get; set; }
    public long TotalBytes { get; set; }
    public Dictionary<string, object>? OperationMetadata { get; set; }
}

public class UpdateSyncProgressRequest
{
    public long BytesTransferred { get; set; }
    public double? TransferSpeed { get; set; }
    public Dictionary<string, object>? PerformanceMetrics { get; set; }
}

public class ResolveFileConflictRequest
{
    public Guid FileId { get; set; }
    public string ResolutionStrategy { get; set; } = string.Empty; // KeepLocal, KeepRemote, Merge, CreateCopy
    public string ResolvedBy { get; set; } = string.Empty;
    public Dictionary<string, object>? ResolutionData { get; set; }
}

public class FileSyncConfigurationDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public bool AutoSyncEnabled { get; set; }
    public List<string> SyncFolders { get; set; } = new();
    public List<string> ExcludedExtensions { get; set; } = new();
    public List<string> ExcludedFolders { get; set; } = new();
    public long MaxFileSize { get; set; }
    public string ConflictResolutionStrategy { get; set; } = string.Empty;
    public Dictionary<string, object> SyncSettings { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class FileSyncStatusDto
{
    public Guid UserId { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public bool IsSyncing { get; set; }
    public DateTime? LastSyncAt { get; set; }
    public DateTime? NextSyncAt { get; set; }
    public int PendingFiles { get; set; }
    public int ConflictedFiles { get; set; }
    public long PendingBytes { get; set; }
    public List<FileSyncOperationDto> ActiveOperations { get; set; } = new();
    public Dictionary<string, object> SyncStatistics { get; set; } = new();
}

public class FileSyncBandwidthDto
{
    public string DeviceId { get; set; } = string.Empty;
    public long MaxUploadBandwidth { get; set; } // Bytes per second
    public long MaxDownloadBandwidth { get; set; } // Bytes per second
    public bool AdaptiveBandwidth { get; set; }
    public Dictionary<string, long> BandwidthByTimeOfDay { get; set; } = new(); // Hour -> Bandwidth
    public bool PauseOnMeteredConnection { get; set; }
    public bool PauseOnLowBattery { get; set; }
    public int BatteryThreshold { get; set; }
}

public class FileSyncConflictDto
{
    public Guid Id { get; set; }
    public Guid FileId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ConflictType { get; set; } = string.Empty; // ModificationConflict, DeletionConflict, NameConflict
    public string ConflictReason { get; set; } = string.Empty;
    public DateTime ConflictDetectedAt { get; set; }
    public FileConflictVersionDto LocalVersion { get; set; } = new();
    public FileConflictVersionDto RemoteVersion { get; set; } = new();
    public List<string> AvailableResolutions { get; set; } = new();
    public string? RecommendedResolution { get; set; }
    public bool IsResolved { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
}

public class FileConflictVersionDto
{
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime ModifiedAt { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class FileSyncHistoryDto
{
    public Guid Id { get; set; }
    public Guid FileId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty; // Created, Modified, Deleted, Synced, Conflicted
    public DateTime ActionAt { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string? Details { get; set; }
    public Dictionary<string, object> ActionData { get; set; } = new();
}

public class FileSyncQueueDto
{
    public Guid Id { get; set; }
    public Guid FileId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OperationType { get; set; } = string.Empty;
    public int Priority { get; set; }
    public DateTime QueuedAt { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public int RetryCount { get; set; }
    public int MaxRetries { get; set; }
    public string Status { get; set; } = string.Empty; // Queued, Processing, Completed, Failed
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> QueueMetadata { get; set; } = new();
}

public class FileSyncPerformanceDto
{
    public string DeviceId { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public long TotalBytesTransferred { get; set; }
    public double AverageUploadSpeed { get; set; }
    public double AverageDownloadSpeed { get; set; }
    public double PeakUploadSpeed { get; set; }
    public double PeakDownloadSpeed { get; set; }
    public int TotalOperations { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public double SuccessRate { get; set; }
    public TimeSpan TotalSyncTime { get; set; }
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

public class FileSyncHealthDto
{
    public string Component { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Healthy, Warning, Critical
    public string? Message { get; set; }
    public Dictionary<string, object> HealthMetrics { get; set; } = new();
    public DateTime CheckedAt { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}
