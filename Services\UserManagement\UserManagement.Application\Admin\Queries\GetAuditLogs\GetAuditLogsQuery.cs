using MediatR;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.Admin.Queries.GetAuditLogs
{
    public class GetAuditLogsQuery : IRequest<GetAuditLogsResponse>
    {
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public string? UserRole { get; set; }
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public AuditAction? Action { get; set; }
        public AuditSeverity? Severity { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? IpAddress { get; set; }
        public string? SessionId { get; set; }
        public string? CorrelationId { get; set; }
        public string? SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string? SortBy { get; set; } = "Timestamp";
        public bool SortDescending { get; set; } = true;
    }

    public class GetAuditLogsResponse
    {
        public List<AuditLogDto> AuditLogs { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    public class AuditLogDto
    {
        public Guid Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public Guid? EntityId { get; set; }
        public Guid? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string UserRole { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string Severity { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public string? SessionId { get; set; }
        public string? CorrelationId { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public DateTime Timestamp { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
