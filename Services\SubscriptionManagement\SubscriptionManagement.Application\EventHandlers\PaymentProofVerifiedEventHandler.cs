using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Events;

namespace SubscriptionManagement.Application.EventHandlers
{
    public class PaymentProofVerifiedEventHandler : INotificationHandler<PaymentProofVerifiedEvent>
    {
        private readonly ILogger<PaymentProofVerifiedEventHandler> _logger;

        public PaymentProofVerifiedEventHandler(ILogger<PaymentProofVerifiedEventHandler> logger)
        {
            _logger = logger;
        }

        public async Task Handle(PaymentProofVerifiedEvent notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Payment proof {PaymentProofId} verified for subscription {SubscriptionId}. " +
                "Amount: {Amount} {Currency}, Verified by: {VerifiedByUserId}", 
                notification.PaymentProofId, notification.SubscriptionId, 
                notification.Amount, notification.Currency, notification.VerifiedByUserId);

            // TODO: Send notification to user about payment verification
            // This could integrate with the Communication/Notification service
            // Example:
            // await _notificationService.SendPaymentVerifiedNotificationAsync(
            //     notification.UserId, 
            //     notification.SubscriptionId, 
            //     notification.Amount, 
            //     notification.Currency);

            // TODO: Update analytics/metrics
            // Example:
            // await _metricsService.RecordPaymentVerificationAsync(
            //     notification.SubscriptionId, 
            //     notification.Amount);

            await Task.CompletedTask;
        }
    }
}
