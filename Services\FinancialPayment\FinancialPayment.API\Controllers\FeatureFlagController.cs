using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FeatureFlagController : ControllerBase
{
    private readonly IFeatureFlagService _featureFlagService;
    private readonly ILogger<FeatureFlagController> _logger;

    public FeatureFlagController(
        IFeatureFlagService featureFlagService,
        ILogger<FeatureFlagController> logger)
    {
        _featureFlagService = featureFlagService;
        _logger = logger;
    }

    /// <summary>
    /// Check if a feature flag is enabled
    /// </summary>
    [HttpPost("{flagKey}/evaluate")]
    public async Task<ActionResult<FeatureFlagEvaluation>> EvaluateFlag(
        string flagKey, 
        [FromBody] FeatureFlagContext? context = null)
    {
        try
        {
            var evaluation = await _featureFlagService.EvaluateAsync(flagKey, context);
            return Ok(evaluation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating feature flag {FlagKey}", flagKey);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get feature flag value with type conversion
    /// </summary>
    [HttpPost("{flagKey}/value")]
    public async Task<ActionResult<object>> GetFlagValue(
        string flagKey,
        [FromBody] GetFlagValueRequest request)
    {
        try
        {
            var value = await _featureFlagService.GetValueAsync(flagKey, request.DefaultValue, request.Context);
            return Ok(new { value });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting value for feature flag {FlagKey}", flagKey);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Evaluate all feature flags for a context
    /// </summary>
    [HttpPost("evaluate-all")]
    public async Task<ActionResult<Dictionary<string, FeatureFlagEvaluation>>> EvaluateAllFlags(
        [FromBody] FeatureFlagContext? context = null)
    {
        try
        {
            var evaluations = await _featureFlagService.EvaluateAllAsync(context);
            return Ok(evaluations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating all feature flags");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new feature flag
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<FeatureFlag>> CreateFlag([FromBody] CreateFeatureFlagRequest request)
    {
        try
        {
            var flag = await _featureFlagService.CreateFlagAsync(request);
            
            _logger.LogInformation("Feature flag created: {FlagKey}", request.FlagKey);
            return CreatedAtAction(nameof(GetFlag), new { id = flag.Id }, flag);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid feature flag creation request");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag {FlagKey}", request.FlagKey);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all feature flags
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<FeatureFlag>>> GetFlags([FromQuery] string? environment = null)
    {
        try
        {
            var flags = await _featureFlagService.GetFlagsAsync(environment);
            return Ok(flags);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flags");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get feature flag by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<FeatureFlag>> GetFlag(Guid id)
    {
        try
        {
            var flag = await _featureFlagService.GetFlagAsync(id);
            if (flag == null)
            {
                return NotFound($"Feature flag {id} not found");
            }

            return Ok(flag);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get feature flag by key
    /// </summary>
    [HttpGet("by-key/{flagKey}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<FeatureFlag>> GetFlagByKey(string flagKey)
    {
        try
        {
            var flag = await _featureFlagService.GetFlagByKeyAsync(flagKey);
            if (flag == null)
            {
                return NotFound($"Feature flag {flagKey} not found");
            }

            return Ok(flag);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {FlagKey}", flagKey);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update feature flag
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<FeatureFlag>> UpdateFlag(
        Guid id, 
        [FromBody] UpdateFeatureFlagRequest request)
    {
        try
        {
            var flag = await _featureFlagService.UpdateFlagAsync(id, request);
            
            _logger.LogInformation("Feature flag updated: {FlagId}", id);
            return Ok(flag);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid feature flag update request for {FlagId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Enable feature flag
    /// </summary>
    [HttpPost("{id}/enable")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> EnableFlag(Guid id)
    {
        try
        {
            var success = await _featureFlagService.EnableFlagAsync(id);
            if (!success)
            {
                return NotFound($"Feature flag {id} not found");
            }

            _logger.LogInformation("Feature flag enabled: {FlagId}", id);
            return Ok(new { message = "Feature flag enabled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Disable feature flag
    /// </summary>
    [HttpPost("{id}/disable")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DisableFlag(Guid id)
    {
        try
        {
            var success = await _featureFlagService.DisableFlagAsync(id);
            if (!success)
            {
                return NotFound($"Feature flag {id} not found");
            }

            _logger.LogInformation("Feature flag disabled: {FlagId}", id);
            return Ok(new { message = "Feature flag disabled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete feature flag
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteFlag(Guid id)
    {
        try
        {
            var success = await _featureFlagService.DeleteFlagAsync(id);
            if (!success)
            {
                return NotFound($"Feature flag {id} not found");
            }

            _logger.LogInformation("Feature flag deleted: {FlagId}", id);
            return Ok(new { message = "Feature flag deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get feature flag audit logs
    /// </summary>
    [HttpGet("{id}/audit-logs")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<List<FeatureFlagAuditLog>>> GetAuditLogs(Guid id)
    {
        try
        {
            var auditLogs = await _featureFlagService.GetAuditLogsAsync(id);
            return Ok(auditLogs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit logs for feature flag {FlagId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get feature flag analytics
    /// </summary>
    [HttpGet("{flagKey}/analytics")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<FeatureFlagAnalytics>> GetAnalytics(
        string flagKey,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var analytics = await _featureFlagService.GetAnalyticsAsync(flagKey, fromDate, toDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics for feature flag {FlagKey}", flagKey);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Bulk enable/disable feature flags
    /// </summary>
    [HttpPost("bulk-operation")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BulkOperationResult>> BulkOperation([FromBody] FeatureFlagBulkOperation request)
    {
        try
        {
            var results = new List<BulkOperationItemResult>();

            foreach (var flagKey in request.FlagKeys)
            {
                try
                {
                    var flag = await _featureFlagService.GetFlagByKeyAsync(flagKey);
                    if (flag == null)
                    {
                        results.Add(new BulkOperationItemResult
                        {
                            FlagKey = flagKey,
                            Success = false,
                            ErrorMessage = "Flag not found"
                        });
                        continue;
                    }

                    bool success = request.Operation.ToLowerInvariant() switch
                    {
                        "enable" => await _featureFlagService.EnableFlagAsync(flag.Id, request.PerformedBy),
                        "disable" => await _featureFlagService.DisableFlagAsync(flag.Id, request.PerformedBy),
                        "delete" => await _featureFlagService.DeleteFlagAsync(flag.Id),
                        _ => false
                    };

                    results.Add(new BulkOperationItemResult
                    {
                        FlagKey = flagKey,
                        Success = success,
                        ErrorMessage = success ? null : $"Failed to {request.Operation} flag"
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new BulkOperationItemResult
                    {
                        FlagKey = flagKey,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }
            }

            var bulkResult = new BulkOperationResult
            {
                TotalItems = request.FlagKeys.Count,
                SuccessfulItems = results.Count(r => r.Success),
                FailedItems = results.Count(r => !r.Success),
                Results = results
            };

            return Ok(bulkResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing bulk operation");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request/Response DTOs
public class GetFlagValueRequest
{
    public object DefaultValue { get; set; } = new();
    public FeatureFlagContext? Context { get; set; }
}

public class BulkOperationResult
{
    public int TotalItems { get; set; }
    public int SuccessfulItems { get; set; }
    public int FailedItems { get; set; }
    public List<BulkOperationItemResult> Results { get; set; } = new();
}

public class BulkOperationItemResult
{
    public string FlagKey { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
