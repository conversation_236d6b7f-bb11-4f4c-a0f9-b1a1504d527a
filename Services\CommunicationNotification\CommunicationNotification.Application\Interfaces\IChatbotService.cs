using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for chatbot management and conversation handling
/// </summary>
public interface IChatbotService
{
    /// <summary>
    /// Create a new chatbot
    /// </summary>
    Task<Chatbot> CreateChatbotAsync(
        string name,
        string description,
        ChatbotType type,
        string defaultLanguage,
        string welcomeMessage,
        Guid createdByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get chatbot by ID
    /// </summary>
    Task<Chatbot?> GetChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update chatbot
    /// </summary>
    Task<Chatbot> UpdateChatbotAsync(
        Guid chatbotId,
        Chatbot updatedChatbot,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete chatbot
    /// </summary>
    Task DeleteChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all chatbots
    /// </summary>
    Task<List<Chatbot>> GetAllChatbotsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Train chatbot with current intents and entities
    /// </summary>
    Task<bool> TrainChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Activate chatbot
    /// </summary>
    Task<Chatbot> ActivateChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deactivate chatbot
    /// </summary>
    Task<Chatbot> DeactivateChatbotAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Start a new conversation with chatbot
    /// </summary>
    Task<ChatbotConversation> StartConversationAsync(
        Guid chatbotId,
        Guid userId,
        string userName,
        NotificationChannel channel,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process user message and generate bot response
    /// </summary>
    Task<ChatbotProcessingResult> ProcessMessageAsync(
        Guid sessionId,
        string userMessage,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// End conversation
    /// </summary>
    Task EndConversationAsync(
        Guid sessionId,
        ConversationStatus endStatus,
        decimal? satisfactionScore = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversation by session ID
    /// </summary>
    Task<ChatbotConversation?> GetConversationAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active conversations for chatbot
    /// </summary>
    Task<List<ChatbotConversation>> GetActiveConversationsAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get chatbot analytics
    /// </summary>
    Task<ChatbotAnalytics> GetChatbotAnalyticsAsync(
        Guid chatbotId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for Natural Language Processing
/// </summary>
public interface INLPService
{
    /// <summary>
    /// Process text and extract intent, entities, and sentiment
    /// </summary>
    Task<NLPResult> ProcessTextAsync(
        string text,
        string language = "en-US",
        List<ChatbotIntent>? intents = null,
        List<ChatbotEntity>? entities = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Extract entities from text
    /// </summary>
    Task<Dictionary<string, object>> ExtractEntitiesAsync(
        string text,
        List<ChatbotEntity> entities,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyze sentiment of text
    /// </summary>
    Task<(string Sentiment, decimal Score)> AnalyzeSentimentAsync(
        string text,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Detect language of text
    /// </summary>
    Task<string> DetectLanguageAsync(
        string text,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate response using AI model
    /// </summary>
    Task<string> GenerateResponseAsync(
        string userMessage,
        string context,
        string language = "en-US",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Train custom model with intents and entities
    /// </summary>
    Task<bool> TrainModelAsync(
        List<ChatbotIntent> intents,
        List<ChatbotEntity> entities,
        string modelId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get supported languages
    /// </summary>
    Task<List<string>> GetSupportedLanguagesAsync(
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for conversation flow management
/// </summary>
public interface IConversationFlowService
{
    /// <summary>
    /// Determine next action based on intent and context
    /// </summary>
    Task<ConversationFlowResult> DetermineNextActionAsync(
        ChatbotConversation conversation,
        NLPResult nlpResult,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute conversation action
    /// </summary>
    Task<ActionExecutionResult> ExecuteActionAsync(
        ChatbotAction action,
        ChatbotConversation conversation,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate contextual response
    /// </summary>
    Task<string> GenerateContextualResponseAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        Dictionary<string, object> entities,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if all required entities are collected
    /// </summary>
    Task<bool> AreRequiredEntitiesCollectedAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get missing required entities
    /// </summary>
    Task<List<string>> GetMissingRequiredEntitiesAsync(
        ChatbotIntent intent,
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for chatbots
/// </summary>
public interface IChatbotRepository
{
    /// <summary>
    /// Add chatbot
    /// </summary>
    Task<Chatbot> AddAsync(
        Chatbot chatbot,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update chatbot
    /// </summary>
    Task<Chatbot> UpdateAsync(
        Chatbot chatbot,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get chatbot by ID
    /// </summary>
    Task<Chatbot?> GetByIdAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all chatbots
    /// </summary>
    Task<List<Chatbot>> GetAllAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active chatbots
    /// </summary>
    Task<List<Chatbot>> GetActiveBotsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete chatbot
    /// </summary>
    Task DeleteAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if chatbot name exists
    /// </summary>
    Task<bool> ExistsAsync(
        string name,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for chatbot conversations
/// </summary>
public interface IChatbotConversationRepository
{
    /// <summary>
    /// Add conversation
    /// </summary>
    Task<ChatbotConversation> AddAsync(
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update conversation
    /// </summary>
    Task<ChatbotConversation> UpdateAsync(
        ChatbotConversation conversation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversation by session ID
    /// </summary>
    Task<ChatbotConversation?> GetBySessionIdAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active conversations for chatbot
    /// </summary>
    Task<List<ChatbotConversation>> GetActiveConversationsAsync(
        Guid chatbotId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversations by user
    /// </summary>
    Task<List<ChatbotConversation>> GetByUserAsync(
        Guid userId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversations by chatbot
    /// </summary>
    Task<List<ChatbotConversation>> GetByChatbotAsync(
        Guid chatbotId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old conversations
    /// </summary>
    Task DeleteOldConversationsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Chatbot processing result
/// </summary>
public class ChatbotProcessingResult
{
    public bool IsSuccess { get; set; }
    public string? BotResponse { get; set; }
    public string? Intent { get; set; }
    public decimal Confidence { get; set; }
    public Dictionary<string, object> ExtractedEntities { get; set; } = new();
    public string? Sentiment { get; set; }
    public bool ShouldEndConversation { get; set; }
    public bool ShouldTransferToAgent { get; set; }
    public string? ErrorMessage { get; set; }
    public ResponseType ResponseType { get; set; } = ResponseType.Text;
    public Dictionary<string, string> Metadata { get; set; } = new();

    public static ChatbotProcessingResult Success(
        string botResponse,
        string? intent = null,
        decimal confidence = 0,
        Dictionary<string, object>? entities = null,
        string? sentiment = null,
        ResponseType responseType = ResponseType.Text)
    {
        return new ChatbotProcessingResult
        {
            IsSuccess = true,
            BotResponse = botResponse,
            Intent = intent,
            Confidence = confidence,
            ExtractedEntities = entities ?? new Dictionary<string, object>(),
            Sentiment = sentiment,
            ResponseType = responseType
        };
    }

    public static ChatbotProcessingResult Failure(string errorMessage)
    {
        return new ChatbotProcessingResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

    public static ChatbotProcessingResult EndConversation(string finalMessage)
    {
        return new ChatbotProcessingResult
        {
            IsSuccess = true,
            BotResponse = finalMessage,
            ShouldEndConversation = true
        };
    }

    public static ChatbotProcessingResult TransferToAgent(string transferMessage)
    {
        return new ChatbotProcessingResult
        {
            IsSuccess = true,
            BotResponse = transferMessage,
            ShouldTransferToAgent = true
        };
    }
}

/// <summary>
/// Conversation flow result
/// </summary>
public class ConversationFlowResult
{
    public string NextAction { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool RequiresUserInput { get; set; }
    public List<string> MissingEntities { get; set; } = new();
    public string? PromptForMissingEntity { get; set; }
}

/// <summary>
/// Action execution result
/// </summary>
public class ActionExecutionResult
{
    public bool IsSuccess { get; set; }
    public string? Response { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public bool ShouldContinueConversation { get; set; } = true;
}
