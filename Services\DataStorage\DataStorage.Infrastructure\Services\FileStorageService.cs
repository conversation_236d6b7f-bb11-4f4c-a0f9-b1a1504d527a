using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;

namespace DataStorage.Infrastructure.Services;

public class FileStorageService : IFileStorageService
{
    private readonly ILogger<FileStorageService> _logger;
    private readonly IConfiguration _configuration;
    private readonly Dictionary<StorageProvider, IStorageProviderService> _providers;

    public FileStorageService(
        ILogger<FileStorageService> logger,
        IConfiguration configuration,
        IEnumerable<IStorageProviderService> providers)
    {
        _logger = logger;
        _configuration = configuration;
        _providers = providers.ToDictionary(p => p.Provider, p => p);
    }

    public async Task<StorageLocation> UploadFileAsync(
        Stream fileStream,
        string fileName,
        string contentType,
        StorageProvider? preferredProvider = null,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = GetStorageProvider(preferredProvider);
            var providerService = GetProviderService(provider);

            _logger.LogInformation("Uploading file {FileName} using {Provider}", fileName, provider);

            var location = await providerService.UploadFileAsync(
                fileStream, fileName, contentType, metadata, cancellationToken);

            _logger.LogInformation("File {FileName} uploaded successfully to {Location}", 
                fileName, location.GetFullPath());

            return location;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file {FileName}", fileName);
            throw new FileUploadException(fileName, ex);
        }
    }

    public async Task<StorageLocation> UploadFileAsync(
        byte[] fileContent,
        string fileName,
        string contentType,
        StorageProvider? preferredProvider = null,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        using var stream = new MemoryStream(fileContent);
        return await UploadFileAsync(stream, fileName, contentType, preferredProvider, metadata, cancellationToken);
    }

    public async Task<Stream> DownloadFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            
            _logger.LogDebug("Downloading file from {Location}", location.GetFullPath());

            return await providerService.DownloadFileAsync(location, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download file from {Location}", location.GetFullPath());
            throw new FileDownloadException(location.GetFullPath(), ex);
        }
    }

    public async Task<byte[]> DownloadFileBytesAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        using var stream = await DownloadFileAsync(location, cancellationToken);
        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream, cancellationToken);
        return memoryStream.ToArray();
    }

    public async Task<string> GetDownloadUrlAsync(StorageLocation location, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            return await providerService.GetDownloadUrlAsync(location, expiry, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get download URL for {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to get download URL for {location.GetFullPath()}", ex);
        }
    }

    public async Task<bool> FileExistsAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            return await providerService.FileExistsAsync(location, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if file exists at {Location}", location.GetFullPath());
            return false;
        }
    }

    public async Task DeleteFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            
            _logger.LogInformation("Deleting file from {Location}", location.GetFullPath());

            await providerService.DeleteFileAsync(location, cancellationToken);

            _logger.LogInformation("File deleted successfully from {Location}", location.GetFullPath());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete file from {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to delete file from {location.GetFullPath()}", ex);
        }
    }

    public async Task<StorageLocation> CopyFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken = default)
    {
        try
        {
            if (source.Provider == destination.Provider)
            {
                var providerService = GetProviderService(source.Provider);
                return await providerService.CopyFileAsync(source, destination, cancellationToken);
            }
            else
            {
                // Cross-provider copy: download from source and upload to destination
                using var stream = await DownloadFileAsync(source, cancellationToken);
                var fileName = Path.GetFileName(source.FilePath);
                var contentType = "application/octet-stream"; // Default, should be determined from source
                
                return await UploadFileAsync(stream, fileName, contentType, destination.Provider, cancellationToken: cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to copy file from {Source} to {Destination}", 
                source.GetFullPath(), destination.GetFullPath());
            throw new FileStorageException($"Failed to copy file from {source.GetFullPath()} to {destination.GetFullPath()}", ex);
        }
    }

    public async Task<StorageLocation> MoveFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken = default)
    {
        var newLocation = await CopyFileAsync(source, destination, cancellationToken);
        await DeleteFileAsync(source, cancellationToken);
        return newLocation;
    }

    public async Task<FileMetadata> GetFileMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            return await providerService.GetFileMetadataAsync(location, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get file metadata for {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to get file metadata for {location.GetFullPath()}", ex);
        }
    }

    public async Task UpdateFileMetadataAsync(StorageLocation location, Dictionary<string, string> metadata, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            await providerService.UpdateFileMetadataAsync(location, metadata, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update file metadata for {Location}", location.GetFullPath());
            throw new FileStorageException($"Failed to update file metadata for {location.GetFullPath()}", ex);
        }
    }

    public async Task<IEnumerable<StorageLocation>> UploadMultipleFilesAsync(
        IEnumerable<(Stream stream, string fileName, string contentType)> files,
        StorageProvider? preferredProvider = null,
        CancellationToken cancellationToken = default)
    {
        var results = new List<StorageLocation>();
        var exceptions = new List<Exception>();

        foreach (var (stream, fileName, contentType) in files)
        {
            try
            {
                var location = await UploadFileAsync(stream, fileName, contentType, preferredProvider, cancellationToken: cancellationToken);
                results.Add(location);
            }
            catch (Exception ex)
            {
                exceptions.Add(ex);
                _logger.LogError(ex, "Failed to upload file {FileName} in bulk operation", fileName);
            }
        }

        if (exceptions.Any() && !results.Any())
        {
            throw new AggregateException("All file uploads failed", exceptions);
        }

        return results;
    }

    public async Task DeleteMultipleFilesAsync(IEnumerable<StorageLocation> locations, CancellationToken cancellationToken = default)
    {
        var exceptions = new List<Exception>();

        foreach (var location in locations)
        {
            try
            {
                await DeleteFileAsync(location, cancellationToken);
            }
            catch (Exception ex)
            {
                exceptions.Add(ex);
                _logger.LogError(ex, "Failed to delete file {Location} in bulk operation", location.GetFullPath());
            }
        }

        if (exceptions.Any())
        {
            _logger.LogWarning("Some files failed to delete in bulk operation. {Count} errors occurred", exceptions.Count);
        }
    }

    public async Task<long> GetStorageSizeAsync(string containerName, CancellationToken cancellationToken = default)
    {
        // This would need to be implemented per provider
        // For now, return 0 as a placeholder
        return 0;
    }

    public async Task<int> GetFileCountAsync(string containerName, CancellationToken cancellationToken = default)
    {
        // This would need to be implemented per provider
        // For now, return 0 as a placeholder
        return 0;
    }

    public async Task<string> GetCDNUrlAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        if (!string.IsNullOrWhiteSpace(location.CDNUrl))
            return location.CDNUrl;

        var providerService = GetProviderService(location.Provider);
        return await providerService.GetCDNUrlAsync(location, cancellationToken);
    }

    public async Task InvalidateCDNCacheAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var providerService = GetProviderService(location.Provider);
            await providerService.InvalidateCDNCacheAsync(location, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to invalidate CDN cache for {Location}", location.GetFullPath());
            // Don't throw - CDN cache invalidation is not critical
        }
    }

    public async Task<string> GenerateSecureUploadUrlAsync(
        string fileName,
        string contentType,
        TimeSpan expiry,
        StorageProvider? preferredProvider = null,
        CancellationToken cancellationToken = default)
    {
        var provider = GetStorageProvider(preferredProvider);
        var providerService = GetProviderService(provider);
        
        return await providerService.GenerateSecureUploadUrlAsync(fileName, contentType, expiry, cancellationToken);
    }

    public async Task<string> GenerateSecureDownloadUrlAsync(
        StorageLocation location,
        TimeSpan expiry,
        CancellationToken cancellationToken = default)
    {
        var providerService = GetProviderService(location.Provider);
        return await providerService.GenerateSecureDownloadUrlAsync(location, expiry, cancellationToken);
    }

    private StorageProvider GetStorageProvider(StorageProvider? preferredProvider)
    {
        if (preferredProvider.HasValue && _providers.ContainsKey(preferredProvider.Value))
            return preferredProvider.Value;

        // Get default provider from configuration
        var defaultProvider = _configuration.GetValue<string>("Storage:DefaultProvider");
        if (Enum.TryParse<StorageProvider>(defaultProvider, out var provider) && _providers.ContainsKey(provider))
            return provider;

        // Fallback to first available provider
        return _providers.Keys.First();
    }

    private IStorageProviderService GetProviderService(StorageProvider provider)
    {
        if (!_providers.TryGetValue(provider, out var service))
            throw new NotSupportedException($"Storage provider {provider} is not configured");

        return service;
    }
}

// Interface for storage provider implementations
public interface IStorageProviderService
{
    StorageProvider Provider { get; }
    
    Task<StorageLocation> UploadFileAsync(Stream fileStream, string fileName, string contentType, Dictionary<string, string>? metadata, CancellationToken cancellationToken);
    Task<Stream> DownloadFileAsync(StorageLocation location, CancellationToken cancellationToken);
    Task<string> GetDownloadUrlAsync(StorageLocation location, TimeSpan? expiry, CancellationToken cancellationToken);
    Task<bool> FileExistsAsync(StorageLocation location, CancellationToken cancellationToken);
    Task DeleteFileAsync(StorageLocation location, CancellationToken cancellationToken);
    Task<StorageLocation> CopyFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken);
    Task<FileMetadata> GetFileMetadataAsync(StorageLocation location, CancellationToken cancellationToken);
    Task UpdateFileMetadataAsync(StorageLocation location, Dictionary<string, string> metadata, CancellationToken cancellationToken);
    Task<string> GetCDNUrlAsync(StorageLocation location, CancellationToken cancellationToken);
    Task InvalidateCDNCacheAsync(StorageLocation location, CancellationToken cancellationToken);
    Task<string> GenerateSecureUploadUrlAsync(string fileName, string contentType, TimeSpan expiry, CancellationToken cancellationToken);
    Task<string> GenerateSecureDownloadUrlAsync(StorageLocation location, TimeSpan expiry, CancellationToken cancellationToken);
}
