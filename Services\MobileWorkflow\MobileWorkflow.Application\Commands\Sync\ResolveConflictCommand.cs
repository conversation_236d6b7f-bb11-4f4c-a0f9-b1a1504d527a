using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Commands.Sync;

public class ResolveConflictCommand : IRequest<bool>
{
    public Guid ConflictId { get; set; }
    public MobileWorkflow.Application.DTOs.ConflictResolutionRequest ResolutionRequest { get; set; } = new();
}

public class ResolveConflictCommandHandler : IRequestHandler<ResolveConflictCommand, bool>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<ResolveConflictCommandHandler> _logger;

    public ResolveConflictCommandHandler(
        IAdvancedSyncService syncService,
        ILogger<ResolveConflictCommandHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<bool> Handle(ResolveConflictCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Resolving conflict {ConflictId} with strategy {Strategy}",
                request.ConflictId, request.ResolutionRequest.ResolutionStrategy);

            // Convert DTO to service request
            var serviceRequest = new MobileWorkflow.Application.Services.ConflictResolutionRequest
            {
                ConflictId = request.ResolutionRequest.ConflictId,
                ResolutionType = request.ResolutionRequest.ResolutionType,
                ResolutionStrategy = request.ResolutionRequest.ResolutionStrategy,
                ResolutionData = request.ResolutionRequest.ResolutionData,
                CustomData = request.ResolutionRequest.CustomData,
                ResolvedBy = request.ResolutionRequest.ResolvedBy.ToString(),
                Comments = request.ResolutionRequest.Comments,
                Notes = request.ResolutionRequest.Notes,
                ApplyToSimilar = request.ResolutionRequest.ApplyToSimilar
            };

            var result = await _syncService.ResolveConflictAsync(
                request.ConflictId,
                serviceRequest,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation("Conflict {ConflictId} resolved successfully", request.ConflictId);
            }
            else
            {
                _logger.LogWarning("Failed to resolve conflict {ConflictId}", request.ConflictId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling ResolveConflictCommand for conflict {ConflictId}", request.ConflictId);
            return false;
        }
    }
}

public class CancelSyncOperationCommand : IRequest<bool>
{
    public Guid SyncOperationId { get; set; }
    public Guid RequestedBy { get; set; }
    public string? Reason { get; set; }
}

public class CancelSyncOperationCommandHandler : IRequestHandler<CancelSyncOperationCommand, bool>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<CancelSyncOperationCommandHandler> _logger;

    public CancelSyncOperationCommandHandler(
        IAdvancedSyncService syncService,
        ILogger<CancelSyncOperationCommandHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<bool> Handle(CancelSyncOperationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Cancelling sync operation {SyncOperationId} requested by {RequestedBy}",
                request.SyncOperationId, request.RequestedBy);

            var result = await _syncService.CancelSyncOperationAsync(request.SyncOperationId, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Sync operation {SyncOperationId} cancelled successfully", request.SyncOperationId);
            }
            else
            {
                _logger.LogWarning("Failed to cancel sync operation {SyncOperationId}", request.SyncOperationId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling CancelSyncOperationCommand for operation {SyncOperationId}", request.SyncOperationId);
            return false;
        }
    }
}

public class OptimizeBandwidthCommand : IRequest<bool>
{
    public Guid SyncOperationId { get; set; }
    public BandwidthOptimizationOptions Options { get; set; } = new();
}

public class OptimizeBandwidthCommandHandler : IRequestHandler<OptimizeBandwidthCommand, bool>
{
    private readonly IAdvancedSyncService _syncService;
    private readonly ILogger<OptimizeBandwidthCommandHandler> _logger;

    public OptimizeBandwidthCommandHandler(
        IAdvancedSyncService syncService,
        ILogger<OptimizeBandwidthCommandHandler> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task<bool> Handle(OptimizeBandwidthCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Optimizing bandwidth for sync operation {SyncOperationId}", request.SyncOperationId);

            var result = await _syncService.OptimizeBandwidthAsync(
                request.SyncOperationId,
                request.Options,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation("Bandwidth optimization applied to sync operation {SyncOperationId}", request.SyncOperationId);
            }
            else
            {
                _logger.LogWarning("Failed to optimize bandwidth for sync operation {SyncOperationId}", request.SyncOperationId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling OptimizeBandwidthCommand for operation {SyncOperationId}", request.SyncOperationId);
            return false;
        }
    }
}

public class CreateOfflineDataCommand : IRequest<Guid>
{
    public Guid UserId { get; set; }
    public Guid MobileSessionId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public int Priority { get; set; } = 2;
    public Guid? MobileAppId { get; set; }
}

public class CreateOfflineDataCommandHandler : IRequestHandler<CreateOfflineDataCommand, Guid>
{
    private readonly IOfflineDataRepository _offlineDataRepository;
    private readonly ILogger<CreateOfflineDataCommandHandler> _logger;

    public CreateOfflineDataCommandHandler(
        IOfflineDataRepository offlineDataRepository,
        ILogger<CreateOfflineDataCommandHandler> logger)
    {
        _offlineDataRepository = offlineDataRepository;
        _logger = logger;
    }

    public async Task<Guid> Handle(CreateOfflineDataCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating offline data for user {UserId}, type {DataType}, action {Action}",
                request.UserId, request.DataType, request.Action);

            var offlineData = new MobileWorkflow.Domain.Entities.OfflineData(
                request.UserId,
                request.MobileSessionId,
                request.DataType,
                request.Action,
                request.Data,
                request.Priority,
                request.MobileAppId);

            _offlineDataRepository.Add(offlineData);
            await _offlineDataRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Offline data {OfflineDataId} created successfully for user {UserId}",
                offlineData.Id, request.UserId);

            return offlineData.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling CreateOfflineDataCommand for user {UserId}", request.UserId);
            throw;
        }
    }
}
