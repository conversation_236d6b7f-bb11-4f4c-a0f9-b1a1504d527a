using MediatR;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Application.Handlers;

public class GetServiceProviderRatingByIdQueryHandler : IRequestHandler<GetServiceProviderRatingByIdQuery, ServiceProviderRatingDto?>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<GetServiceProviderRatingByIdQueryHandler> _logger;

    public GetServiceProviderRatingByIdQueryHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<GetServiceProviderRatingByIdQueryHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<ServiceProviderRatingDto?> Handle(GetServiceProviderRatingByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var rating = await _ratingRepository.GetByIdAsync(request.Id, cancellationToken);
            if (rating == null)
                return null;

            return new ServiceProviderRatingDto
            {
                Id = rating.Id,
                ShipperId = rating.ShipperId,
                ShipperName = rating.ShipperName,
                TransportCompanyId = rating.TransportCompanyId,
                TransportCompanyName = rating.TransportCompanyName,
                OrderId = rating.OrderId,
                OrderNumber = rating.OrderNumber,
                TripId = rating.TripId,
                TripNumber = rating.TripNumber,
                OverallRating = rating.OverallRating.NumericValue,
                Status = rating.Status,
                ReviewTitle = rating.ReviewTitle,
                ReviewComment = rating.ReviewComment,
                IsAnonymous = rating.IsAnonymous,
                ServiceCompletedAt = rating.ServiceCompletedAt,
                ReviewSubmittedAt = rating.ReviewSubmittedAt,
                CreatedAt = rating.CreatedAt,
                CategoryRatings = rating.CategoryRatings.Select(cr => new CategoryRatingDto
                {
                    Id = cr.Id,
                    Category = cr.Category,
                    CategoryName = cr.GetCategoryName(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList(),
                ReportedIssues = rating.ReportedIssues.Select(issue => new ServiceIssueDto
                {
                    Id = issue.Id,
                    IssueType = issue.IssueType,
                    IssueTypeName = issue.GetIssueTypeName(),
                    Priority = issue.Priority,
                    Description = issue.Description,
                    Evidence = issue.Evidence,
                    Status = issue.Status,
                    ReportedAt = issue.ReportedAt,
                    ResolvedAt = issue.ResolvedAt,
                    Resolution = issue.Resolution,
                    ResolvedByName = issue.ResolvedByName
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider rating: {RatingId}", request.Id);
            throw;
        }
    }
}

public class GetServiceProviderRatingsQueryHandler : IRequestHandler<GetServiceProviderRatingsQuery, RatingResultDto>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<GetServiceProviderRatingsQueryHandler> _logger;

    public GetServiceProviderRatingsQueryHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<GetServiceProviderRatingsQueryHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<RatingResultDto> Handle(GetServiceProviderRatingsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var ratings = await _ratingRepository.GetRatingsAsync(
                request.ShipperId,
                request.TransportCompanyId,
                request.Status,
                request.MinRating,
                request.MaxRating,
                request.FromDate,
                request.ToDate,
                request.PageNumber,
                request.PageSize,
                cancellationToken);

            var totalCount = await _ratingRepository.GetRatingsCountAsync(
                request.ShipperId,
                request.TransportCompanyId,
                request.Status,
                request.MinRating,
                request.MaxRating,
                request.FromDate,
                request.ToDate,
                cancellationToken);

            var ratingDtos = ratings.Select(r => new ServiceProviderRatingDto
            {
                Id = r.Id,
                ShipperId = r.ShipperId,
                ShipperName = r.ShipperName,
                TransportCompanyId = r.TransportCompanyId,
                TransportCompanyName = r.TransportCompanyName,
                OrderId = r.OrderId,
                OrderNumber = r.OrderNumber,
                TripId = r.TripId,
                TripNumber = r.TripNumber,
                OverallRating = r.OverallRating.NumericValue,
                Status = r.Status,
                ReviewTitle = r.ReviewTitle,
                ReviewComment = r.ReviewComment,
                IsAnonymous = r.IsAnonymous,
                ServiceCompletedAt = r.ServiceCompletedAt,
                ReviewSubmittedAt = r.ReviewSubmittedAt,
                CreatedAt = r.CreatedAt,
                CategoryRatings = r.CategoryRatings.Select(cr => new CategoryRatingDto
                {
                    Id = cr.Id,
                    Category = cr.Category,
                    CategoryName = cr.GetCategoryName(),
                    Rating = cr.Rating.NumericValue,
                    Comment = cr.Comment
                }).ToList()
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new RatingResultDto
            {
                Ratings = ratingDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = totalPages,
                HasNextPage = request.PageNumber < totalPages,
                HasPreviousPage = request.PageNumber > 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service provider ratings");
            throw;
        }
    }
}

public class GetTransportCompanyRatingSummaryQueryHandler : IRequestHandler<GetTransportCompanyRatingSummaryQuery, TransportCompanyRatingSummaryDto>
{
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<GetTransportCompanyRatingSummaryQueryHandler> _logger;

    public GetTransportCompanyRatingSummaryQueryHandler(
        IServiceProviderRatingRepository ratingRepository,
        ILogger<GetTransportCompanyRatingSummaryQueryHandler> logger)
    {
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<TransportCompanyRatingSummaryDto> Handle(GetTransportCompanyRatingSummaryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var ratings = await _ratingRepository.GetRatingsByTransportCompanyAsync(request.TransportCompanyId, cancellationToken);
            
            if (!ratings.Any())
            {
                return new TransportCompanyRatingSummaryDto
                {
                    TransportCompanyId = request.TransportCompanyId,
                    TransportCompanyName = "Unknown",
                    AverageRating = 0,
                    TotalRatings = 0
                };
            }

            var activeRatings = ratings.Where(r => r.Status == Domain.Enums.RatingStatus.Active).ToList();
            var averageRating = activeRatings.Any() ? activeRatings.Average(r => r.OverallRating.NumericValue) : 0;

            var summary = new TransportCompanyRatingSummaryDto
            {
                TransportCompanyId = request.TransportCompanyId,
                TransportCompanyName = activeRatings.FirstOrDefault()?.TransportCompanyName ?? "Unknown",
                AverageRating = averageRating,
                TotalRatings = activeRatings.Count,
                FiveStarRatings = activeRatings.Count(r => r.OverallRating.Scale == Domain.Enums.RatingScale.FiveStars),
                FourStarRatings = activeRatings.Count(r => r.OverallRating.Scale == Domain.Enums.RatingScale.FourStars),
                ThreeStarRatings = activeRatings.Count(r => r.OverallRating.Scale == Domain.Enums.RatingScale.ThreeStars),
                TwoStarRatings = activeRatings.Count(r => r.OverallRating.Scale == Domain.Enums.RatingScale.TwoStars),
                OneStarRatings = activeRatings.Count(r => r.OverallRating.Scale == Domain.Enums.RatingScale.OneStar),
                TotalIssuesReported = activeRatings.SelectMany(r => r.ReportedIssues).Count(),
                ResolvedIssues = activeRatings.SelectMany(r => r.ReportedIssues)
                    .Count(i => i.Status == Domain.Enums.IssueResolutionStatus.Resolved || i.Status == Domain.Enums.IssueResolutionStatus.Closed),
                LastRatingDate = activeRatings.Any() ? activeRatings.Max(r => r.CreatedAt) : DateTime.MinValue
            };

            // Calculate category averages
            var categoryGroups = activeRatings
                .SelectMany(r => r.CategoryRatings)
                .GroupBy(cr => cr.Category);

            summary.CategoryAverages = categoryGroups.Select(g => new CategoryRatingSummaryDto
            {
                Category = g.Key,
                CategoryName = g.First().GetCategoryName(),
                AverageRating = g.Average(cr => cr.Rating.NumericValue),
                TotalRatings = g.Count()
            }).ToList();

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving transport company rating summary: {TransportCompanyId}", request.TransportCompanyId);
            throw;
        }
    }
}

public class GetPreferredProviderNetworkQueryHandler : IRequestHandler<GetPreferredProviderNetworkQuery, List<PreferredProviderNetworkDto>>
{
    private readonly IPreferredProviderNetworkRepository _networkRepository;
    private readonly ILogger<GetPreferredProviderNetworkQueryHandler> _logger;

    public GetPreferredProviderNetworkQueryHandler(
        IPreferredProviderNetworkRepository networkRepository,
        ILogger<GetPreferredProviderNetworkQueryHandler> logger)
    {
        _networkRepository = networkRepository;
        _logger = logger;
    }

    public async Task<List<PreferredProviderNetworkDto>> Handle(GetPreferredProviderNetworkQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var networks = await _networkRepository.GetByShipperIdAsync(request.ShipperId, cancellationToken);

            return networks
                .Where(n => n.IsActive)
                .OrderBy(n => n.PreferenceRank)
                .Select(n => new PreferredProviderNetworkDto
                {
                    Id = n.Id,
                    ShipperId = n.ShipperId,
                    ShipperName = n.ShipperName,
                    TransportCompanyId = n.TransportCompanyId,
                    TransportCompanyName = n.TransportCompanyName,
                    AverageRating = n.AverageRating,
                    TotalOrders = n.TotalOrders,
                    CompletedOrders = n.CompletedOrders,
                    CompletionRate = n.CompletionRate,
                    FirstOrderDate = n.FirstOrderDate,
                    LastOrderDate = n.LastOrderDate,
                    IsActive = n.IsActive,
                    Notes = n.Notes,
                    PreferenceRank = n.PreferenceRank,
                    CreatedAt = n.CreatedAt
                }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred provider network for shipper: {ShipperId}", request.ShipperId);
            throw;
        }
    }
}
