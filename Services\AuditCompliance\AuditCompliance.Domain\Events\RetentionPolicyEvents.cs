using Shared.Domain.Common;

namespace AuditCompliance.Domain.Events;

/// <summary>
/// Event raised when a retention policy is created
/// </summary>
public class RetentionPolicyCreatedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public string PolicyName { get; init; }
    public string DataType { get; init; }
    public int RetentionPeriodDays { get; init; }
    public Guid CreatedBy { get; init; }

    public RetentionPolicyCreatedEvent(Guid policyId, string policyName, string dataType, int retentionPeriodDays, Guid createdBy)
    {
        PolicyId = policyId;
        PolicyName = policyName;
        DataType = dataType;
        RetentionPeriodDays = retentionPeriodDays;
        CreatedBy = createdBy;
    }
}

/// <summary>
/// Event raised when a retention policy is updated
/// </summary>
public class RetentionPolicyUpdatedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public string PolicyName { get; init; }
    public string DataType { get; init; }
    public int RetentionPeriodDays { get; init; }
    public Guid UpdatedBy { get; init; }

    public RetentionPolicyUpdatedEvent(Guid policyId, string policyName, string dataType, int retentionPeriodDays, Guid updatedBy)
    {
        PolicyId = policyId;
        PolicyName = policyName;
        DataType = dataType;
        RetentionPeriodDays = retentionPeriodDays;
        UpdatedBy = updatedBy;
    }
}

/// <summary>
/// Event raised when a retention policy is approved
/// </summary>
public class RetentionPolicyApprovedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public Guid ApprovedBy { get; init; }
    public string? ApprovalComments { get; init; }

    public RetentionPolicyApprovedEvent(Guid policyId, Guid approvedBy, string? approvalComments = null)
    {
        PolicyId = policyId;
        ApprovedBy = approvedBy;
        ApprovalComments = approvalComments;
    }
}

/// <summary>
/// Event raised when a retention policy is rejected
/// </summary>
public class RetentionPolicyRejectedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public Guid RejectedBy { get; init; }
    public string RejectionReason { get; init; }

    public RetentionPolicyRejectedEvent(Guid policyId, Guid rejectedBy, string rejectionReason)
    {
        PolicyId = policyId;
        RejectedBy = rejectedBy;
        RejectionReason = rejectionReason;
    }
}

/// <summary>
/// Event raised when a retention policy is activated
/// </summary>
public class RetentionPolicyActivatedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public Guid ActivatedBy { get; init; }
    public DateTime EffectiveDate { get; init; }

    public RetentionPolicyActivatedEvent(Guid policyId, Guid activatedBy, DateTime effectiveDate)
    {
        PolicyId = policyId;
        ActivatedBy = activatedBy;
        EffectiveDate = effectiveDate;
    }
}

/// <summary>
/// Event raised when a retention policy is deactivated
/// </summary>
public class RetentionPolicyDeactivatedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public Guid DeactivatedBy { get; init; }
    public string? DeactivationReason { get; init; }

    public RetentionPolicyDeactivatedEvent(Guid policyId, Guid deactivatedBy, string? deactivationReason = null)
    {
        PolicyId = policyId;
        DeactivatedBy = deactivatedBy;
        DeactivationReason = deactivationReason;
    }
}

/// <summary>
/// Event raised when a retention policy is executed
/// </summary>
public class RetentionPolicyExecutedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public int RecordsProcessed { get; init; }
    public int RecordsDeleted { get; init; }
    public DateTime ExecutionStartTime { get; init; }
    public DateTime ExecutionEndTime { get; init; }

    public RetentionPolicyExecutedEvent(Guid policyId, int recordsProcessed, int recordsDeleted, DateTime executionStartTime, DateTime executionEndTime)
    {
        PolicyId = policyId;
        RecordsProcessed = recordsProcessed;
        RecordsDeleted = recordsDeleted;
        ExecutionStartTime = executionStartTime;
        ExecutionEndTime = executionEndTime;
    }
}

/// <summary>
/// Event raised when retention policy impact is analyzed
/// </summary>
public class RetentionPolicyImpactAnalyzedEvent : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
    
    public Guid PolicyId { get; init; }
    public int EstimatedRecordsAffected { get; init; }
    public long EstimatedStorageFreed { get; init; }
    public List<string> AffectedSystems { get; init; }
    public Guid AnalyzedBy { get; init; }

    public RetentionPolicyImpactAnalyzedEvent(Guid policyId, int estimatedRecordsAffected, long estimatedStorageFreed, List<string> affectedSystems, Guid analyzedBy)
    {
        PolicyId = policyId;
        EstimatedRecordsAffected = estimatedRecordsAffected;
        EstimatedStorageFreed = estimatedStorageFreed;
        AffectedSystems = affectedSystems;
        AnalyzedBy = analyzedBy;
    }
}
