namespace NetworkFleetManagement.Application.DTOs;

public class NetworkPerformanceDashboardDto
{
    public int TotalNetworks { get; set; }
    public int ActiveNetworks { get; set; }
    public int SuspendedNetworks { get; set; }
    public decimal AverageNetworkRating { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageOnTimePercentage { get; set; }
    public List<TopPerformingNetworkDto> TopPerformingNetworks { get; set; } = new();
    public List<UnderperformingNetworkDto> UnderperformingNetworks { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class TopPerformingNetworkDto
{
    public Guid NetworkId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public decimal OnTimePercentage { get; set; }
    public int TotalTrips { get; set; }
    public decimal Revenue { get; set; }
}

public class UnderperformingNetworkDto
{
    public Guid NetworkId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public decimal OnTimePercentage { get; set; }
    public int TotalTrips { get; set; }
    public List<string> Issues { get; set; } = new();
}

public class CarrierPerformanceAnalyticsDto
{
    public int TotalCarriers { get; set; }
    public int ActiveCarriers { get; set; }
    public int PendingCarriers { get; set; }
    public int SuspendedCarriers { get; set; }
    public decimal AverageCarrierRating { get; set; }
    public List<CarrierPerformanceDto> TopPerformers { get; set; } = new();
    public List<CarrierPerformanceDto> UnderPerformers { get; set; } = new();
    public CarrierOnboardingStatsDto OnboardingStats { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class CarrierPerformanceDto
{
    public Guid CarrierId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int TotalTrips { get; set; }
    public int CompletedTrips { get; set; }
    public decimal OnTimePercentage { get; set; }
    public decimal Revenue { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
}

public class CarrierOnboardingStatsDto
{
    public int TotalOnboarding { get; set; }
    public int DocumentsUploaded { get; set; }
    public int DocumentsVerified { get; set; }
    public int BackgroundCheckPending { get; set; }
    public int Approved { get; set; }
    public int Rejected { get; set; }
    public decimal ApprovalRate { get; set; }
    public double AverageOnboardingDays { get; set; }
}

public class BrokerPerformanceAnalyticsDto
{
    public int TotalBrokers { get; set; }
    public int ActiveBrokers { get; set; }
    public decimal AverageQuoteAccuracy { get; set; }
    public List<BrokerPerformanceDto> TopPerformers { get; set; } = new();
    public List<BrokerPerformanceDto> UnderPerformers { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class BrokerPerformanceDto
{
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public int TotalQuotes { get; set; }
    public int AcceptedQuotes { get; set; }
    public decimal QuoteAccuracy { get; set; }
    public decimal AverageResponseTime { get; set; }
    public int NetworkSize { get; set; }
    public decimal Revenue { get; set; }
}

public class FleetUtilizationAnalyticsDto
{
    public int TotalVehicles { get; set; }
    public int AvailableVehicles { get; set; }
    public int InUseVehicles { get; set; }
    public int MaintenanceVehicles { get; set; }
    public decimal UtilizationRate { get; set; }
    public decimal AverageEarningsPerVehicle { get; set; }
    public List<VehicleUtilizationDto> TopUtilizedVehicles { get; set; } = new();
    public List<VehicleUtilizationDto> UnderutilizedVehicles { get; set; } = new();
    public MaintenanceStatsDto MaintenanceStats { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class VehicleUtilizationDto
{
    public Guid VehicleId { get; set; }
    public string RegistrationNumber { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public decimal UtilizationPercentage { get; set; }
    public decimal Earnings { get; set; }
    public int TripsCompleted { get; set; }
    public int UtilizationHours { get; set; }
}

public class MaintenanceStatsDto
{
    public int VehiclesRequiringMaintenance { get; set; }
    public int OverdueMaintenances { get; set; }
    public decimal AverageMaintenanceCost { get; set; }
    public double AverageDowntimeDays { get; set; }
}

public class DriverComplianceAnalyticsDto
{
    public int TotalDrivers { get; set; }
    public int ActiveDrivers { get; set; }
    public int VerifiedDrivers { get; set; }
    public int DriversWithExpiredLicenses { get; set; }
    public int DriversRequiringRenewal { get; set; }
    public decimal ComplianceRate { get; set; }
    public List<DriverComplianceDto> NonCompliantDrivers { get; set; } = new();
    public DriverOnboardingStatsDto OnboardingStats { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class DriverComplianceDto
{
    public Guid DriverId { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string CarrierName { get; set; } = string.Empty;
    public string LicenseNumber { get; set; } = string.Empty;
    public DateTime LicenseExpiryDate { get; set; }
    public bool IsLicenseExpired { get; set; }
    public bool IsVerified { get; set; }
    public List<string> ComplianceIssues { get; set; } = new();
}

public class DriverOnboardingStatsDto
{
    public int TotalOnboarding { get; set; }
    public int DocumentsUploaded { get; set; }
    public int DocumentsVerified { get; set; }
    public int Approved { get; set; }
    public int Rejected { get; set; }
    public decimal ApprovalRate { get; set; }
    public double AverageOnboardingDays { get; set; }
}

public class OnboardingOversightDashboardDto
{
    public CarrierOnboardingStatsDto CarrierOnboarding { get; set; } = new();
    public DriverOnboardingStatsDto DriverOnboarding { get; set; } = new();
    public List<PendingOnboardingDto> PendingApprovals { get; set; } = new();
    public List<RecentOnboardingDto> RecentCompletions { get; set; } = new();
    public QualityControlStatsDto QualityControl { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class PendingOnboardingDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty; // Carrier or Driver
    public string Name { get; set; } = string.Empty;
    public string OnboardingStatus { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public int DaysPending { get; set; }
    public string? Notes { get; set; }
}

public class RecentOnboardingDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CompletedAt { get; set; }
    public int DaysToComplete { get; set; }
}

public class QualityControlStatsDto
{
    public int DocumentsReviewed { get; set; }
    public int DocumentsApproved { get; set; }
    public int DocumentsRejected { get; set; }
    public decimal ApprovalRate { get; set; }
    public double AverageReviewTime { get; set; }
}

public class DocumentVerificationDashboardDto
{
    public int TotalDocuments { get; set; }
    public int VerifiedDocuments { get; set; }
    public int PendingVerification { get; set; }
    public int RejectedDocuments { get; set; }
    public int ExpiredDocuments { get; set; }
    public int ExpiringSoon { get; set; }
    public List<DocumentVerificationStatsDto> DocumentTypeStats { get; set; } = new();
    public List<PendingDocumentDto> UrgentVerifications { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class DocumentVerificationStatsDto
{
    public string DocumentType { get; set; } = string.Empty;
    public int Total { get; set; }
    public int Verified { get; set; }
    public int Pending { get; set; }
    public int Rejected { get; set; }
    public int Expired { get; set; }
    public decimal VerificationRate { get; set; }
}

public class PendingDocumentDto
{
    public Guid DocumentId { get; set; }
    public string DocumentType { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public DateTime SubmittedAt { get; set; }
    public int DaysPending { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public bool IsUrgent { get; set; }
}

public class SystemHealthMetricsDto
{
    public DatabaseHealthDto Database { get; set; } = new();
    public ServiceHealthDto Services { get; set; } = new();
    public PerformanceMetricsDto Performance { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class DatabaseHealthDto
{
    public bool IsHealthy { get; set; }
    public int ConnectionCount { get; set; }
    public double ResponseTimeMs { get; set; }
    public long TotalRecords { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class ServiceHealthDto
{
    public bool IsHealthy { get; set; }
    public List<ExternalServiceDto> ExternalServices { get; set; } = new();
    public string Status { get; set; } = string.Empty;
}

public class ExternalServiceDto
{
    public string ServiceName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public double ResponseTimeMs { get; set; }
    public string Status { get; set; } = string.Empty;
}

public class OperationalMetricsDto
{
    public int TotalTransactions { get; set; }
    public int SuccessfulTransactions { get; set; }
    public int FailedTransactions { get; set; }
    public decimal SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public int ActiveUsers { get; set; }
    public List<ApiEndpointMetricsDto> ApiMetrics { get; set; } = new();
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
}

public class ApiEndpointMetricsDto
{
    public string Endpoint { get; set; } = string.Empty;
    public int RequestCount { get; set; }
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public double AverageResponseTime { get; set; }
    public decimal SuccessRate { get; set; }
}

public class AdminAlertsDto
{
    public List<AlertDto> CriticalAlerts { get; set; } = new();
    public List<AlertDto> WarningAlerts { get; set; } = new();
    public List<AlertDto> InfoAlerts { get; set; } = new();
    public int TotalUnresolved { get; set; }
    public DateTime GeneratedAt { get; set; }
}

public class AlertDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsResolved { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
}

public class AuditTrailDto
{
    public Guid Id { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}
