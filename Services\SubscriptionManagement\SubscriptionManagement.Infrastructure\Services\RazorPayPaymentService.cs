using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Razorpay.Api;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.ValueObjects;
using System.Collections.Generic;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class RazorPayPaymentService : IPaymentService
    {
        private readonly RazorpayClient _razorpayClient;
        private readonly ILogger<RazorPayPaymentService> _logger;
        private readonly string _keyId;
        private readonly string _keySecret;

        public RazorPayPaymentService(IConfiguration configuration, ILogger<RazorPayPaymentService> logger)
        {
            _logger = logger;
            _keyId = configuration["RazorPay:KeyId"] ?? throw new ArgumentNullException("RazorPay:KeyId");
            _keySecret = configuration["RazorPay:KeySecret"] ?? throw new ArgumentNullException("RazorPay:KeySecret");

            _razorpayClient = new RazorpayClient(_keyId, _keySecret);
        }

        public async Task<PaymentResult> ProcessPaymentAsync(SubscriptionManagement.Domain.Entities.Payment payment, string? paymentMethodId = null)
        {
            try
            {
                _logger.LogInformation("Processing payment {PaymentId} for amount {Amount} {Currency}",
                    payment.Id, payment.Amount.Amount, payment.Amount.Currency);

                payment.MarkAsProcessing();

                // Create RazorPay order
                var orderOptions = new Dictionary<string, object>
                {
                    {"amount", (int)(payment.Amount.Amount * 100)}, // Amount in paise
                    {"currency", payment.Amount.Currency},
                    {"receipt", payment.Id.ToString()},
                    {"payment_capture", 1}
                };

                var order = _razorpayClient.Order.Create(orderOptions);
                string orderId = order["id"]?.ToString() ?? $"order_{Guid.NewGuid():N}";

                // For demo purposes, we'll simulate a successful payment
                // In a real implementation, you would handle the payment flow properly
                await Task.Delay(1000); // Simulate processing time

                payment.MarkAsCompleted(orderId, "Payment processed successfully");

                _logger.LogInformation("Payment {PaymentId} processed successfully with order ID {OrderId}",
                    payment.Id.ToString(), orderId);

                return new PaymentResult
                {
                    IsSuccess = true,
                    TransactionId = orderId,
                    ProcessedAt = DateTime.UtcNow,
                    GatewayResponse = "Payment completed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment {PaymentId}", payment.Id.ToString());

                payment.MarkAsFailed(ex.Message, ex.ToString());

                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<PaymentResult> ProcessSubscriptionPaymentAsync(SubscriptionManagement.Domain.Entities.Subscription subscription, string? paymentMethodId = null)
        {
            var payment = new SubscriptionManagement.Domain.Entities.Payment(subscription.Id, subscription.UserId, subscription.CurrentPrice, paymentMethodId);
            subscription.AddPayment(payment);

            return await ProcessPaymentAsync(payment, paymentMethodId);
        }

        public async Task<RefundResult> RefundPaymentAsync(SubscriptionManagement.Domain.Entities.Payment payment, Money? refundAmount = null, string? reason = null)
        {
            try
            {
                _logger.LogInformation("Processing refund for payment {PaymentId}", payment.Id.ToString());

                var amountToRefund = refundAmount ?? payment.Amount;

                // Create refund request
                var refundOptions = new Dictionary<string, object>
                {
                    {"amount", (int)(amountToRefund.Amount * 100)}, // Amount in paise
                    {"speed", "normal"}
                };

                if (!string.IsNullOrEmpty(reason))
                {
                    refundOptions.Add("notes", new Dictionary<string, string> { { "reason", reason } });
                }

                // For demo purposes, simulate successful refund
                await Task.Delay(500);
                var refundId = $"rfnd_{Guid.NewGuid():N}";

                payment.MarkAsRefunded(amountToRefund, reason);

                _logger.LogInformation("Refund processed successfully for payment {PaymentId} with refund ID {RefundId}",
                    payment.Id.ToString(), refundId);

                return new RefundResult
                {
                    IsSuccess = true,
                    RefundId = refundId,
                    RefundAmount = amountToRefund,
                    ProcessedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refund for payment {PaymentId}", payment.Id.ToString());

                return new RefundResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<RefundResult> ProcessRefundAsync(SubscriptionManagement.Domain.Entities.Payment payment, Money refundAmount, string? reason = null)
        {
            return await RefundPaymentAsync(payment, refundAmount, reason);
        }

        public async Task<PaymentResult> RetryPaymentAsync(SubscriptionManagement.Domain.Entities.Payment payment)
        {
            if (!payment.CanRetry())
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Payment cannot be retried",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            var nextRetryDate = payment.GetNextRetryDate();
            payment.ScheduleRetry(nextRetryDate);

            return await ProcessPaymentAsync(payment);
        }

        public async Task<string> CreatePaymentIntentAsync(Money amount, Guid userId, string? paymentMethodId = null)
        {
            try
            {
                var orderOptions = new Dictionary<string, object>
                {
                    {"amount", (int)(amount.Amount * 100)}, // Amount in paise
                    {"currency", amount.Currency},
                    {"receipt", $"user_{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}"}
                };

                // Simulate async operation
                await Task.Delay(100);
                var order = _razorpayClient.Order.Create(orderOptions);
                return order["id"]?.ToString() ?? $"order_{Guid.NewGuid():N}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent for user {UserId}", userId.ToString());
                throw;
            }
        }

        public async Task<bool> ValidatePaymentMethodAsync(string paymentMethodId, Guid userId)
        {
            // For demo purposes, return true
            // In a real implementation, you would validate the payment method with RazorPay
            await Task.Delay(100);
            return !string.IsNullOrEmpty(paymentMethodId);
        }

        public async Task<List<PaymentMethod>> GetUserPaymentMethodsAsync(Guid userId)
        {
            // For demo purposes, return empty list
            // In a real implementation, you would fetch user's payment methods from RazorPay
            await Task.Delay(100);
            return new List<PaymentMethod>();
        }

        public async Task<PaymentResult> ProcessProrationPaymentAsync(SubscriptionManagement.Domain.Entities.Subscription subscription, decimal prorationAmount, string paymentMethodId)
        {
            try
            {
                _logger.LogInformation("Processing proration payment for subscription {SubscriptionId}, amount {Amount}",
                    subscription.Id, prorationAmount);

                var prorationMoney = Money.Create(prorationAmount, subscription.CurrentPrice.Currency);
                var payment = new SubscriptionManagement.Domain.Entities.Payment(subscription.Id, subscription.UserId, prorationMoney, paymentMethodId);
                subscription.AddPayment(payment);

                return await ProcessPaymentAsync(payment, paymentMethodId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing proration payment for subscription {SubscriptionId}", subscription.Id);
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<RefundResult> ProcessRefundAsync(SubscriptionManagement.Domain.Entities.Subscription subscription, decimal refundAmount)
        {
            try
            {
                _logger.LogInformation("Processing refund for subscription {SubscriptionId}, amount {Amount}",
                    subscription.Id, refundAmount);

                // Find the most recent successful payment for this subscription
                var lastPayment = subscription.Payments
                    .Where(p => p.Status == PaymentStatus.Completed)
                    .OrderByDescending(p => p.ProcessedAt)
                    .FirstOrDefault();

                if (lastPayment == null)
                {
                    return new RefundResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "No successful payment found for refund",
                        ProcessedAt = DateTime.UtcNow
                    };
                }

                var refundMoney = Money.Create(refundAmount, subscription.CurrentPrice.Currency);
                return await RefundPaymentAsync(lastPayment, refundMoney, "Subscription refund");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refund for subscription {SubscriptionId}", subscription.Id);
                return new RefundResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ProcessedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<ValidationResult> ValidatePaymentMethodAsync(string paymentMethodId)
        {
            try
            {
                // For demo purposes, validate that the payment method ID is not empty
                // In a real implementation, you would validate with RazorPay
                await Task.Delay(100);

                if (string.IsNullOrEmpty(paymentMethodId))
                {
                    return new ValidationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Payment method ID cannot be empty"
                    };
                }

                return new ValidationResult
                {
                    IsSuccess = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating payment method {PaymentMethodId}", paymentMethodId);
                return new ValidationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<PaymentMethod> AddPaymentMethodAsync(Guid userId, string paymentMethodToken)
        {
            // For demo purposes, create a mock payment method
            // In a real implementation, you would add the payment method to RazorPay
            await Task.Delay(100);

            return new PaymentMethod
            {
                Id = $"pm_{Guid.NewGuid():N}",
                Type = "card",
                Last4 = "4242",
                Brand = "visa",
                IsDefault = true,
                CreatedAt = DateTime.UtcNow
            };
        }

        public async Task RemovePaymentMethodAsync(Guid userId, string paymentMethodId)
        {
            // For demo purposes, just log the action
            // In a real implementation, you would remove the payment method from RazorPay
            await Task.Delay(100);
            _logger.LogInformation("Payment method {PaymentMethodId} removed for user {UserId}",
                paymentMethodId, userId.ToString());
        }
    }
}
