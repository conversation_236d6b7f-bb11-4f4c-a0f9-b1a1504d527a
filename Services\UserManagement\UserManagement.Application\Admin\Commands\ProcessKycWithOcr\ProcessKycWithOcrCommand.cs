using System;
using System.Collections.Generic;
using MediatR;

namespace UserManagement.Application.Admin.Commands.ProcessKycWithOcr
{
    public class ProcessKycWithOcrCommand : IRequest<ProcessKycWithOcrResponse>
    {
        public Guid UserId { get; set; }
        public Guid DocumentId { get; set; }
        public string DocumentType { get; set; }
        public bool AutoApprove { get; set; } = false;
        public decimal ConfidenceThreshold { get; set; } = 0.85m;
        public Guid ProcessedBy { get; set; }
        public string Notes { get; set; }
    }

    public class ProcessKycWithOcrResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public OcrComparisonResult OcrResult { get; set; }
        public KycDecision Decision { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public DateTime ProcessedAt { get; set; }
    }

    public class OcrComparisonResult
    {
        public string DocumentType { get; set; }
        public decimal OverallConfidence { get; set; }
        public Dictionary<string, OcrFieldComparison> FieldComparisons { get; set; } = new();
        public List<string> ExtractedText { get; set; } = new();
        public List<OcrValidationIssue> ValidationIssues { get; set; } = new();
        public bool PassedValidation { get; set; }
        public string ProcessingEngine { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class OcrFieldComparison
    {
        public string FieldName { get; set; }
        public string ExtractedValue { get; set; }
        public string UserProvidedValue { get; set; }
        public bool IsMatch { get; set; }
        public decimal Confidence { get; set; }
        public string MatchType { get; set; } // Exact, Fuzzy, NoMatch
        public List<string> Suggestions { get; set; } = new();
    }

    public class OcrValidationIssue
    {
        public string IssueType { get; set; } // DocumentQuality, TextClarity, MissingField, InvalidFormat
        public string Description { get; set; }
        public string Severity { get; set; } // Low, Medium, High, Critical
        public string FieldName { get; set; }
        public string Recommendation { get; set; }
    }

    public enum KycDecision
    {
        Approved,
        Rejected,
        RequiresManualReview,
        RequiresResubmission
    }

    public class KycApprovalHistoryEntry
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid DocumentId { get; set; }
        public string DocumentType { get; set; }
        public KycDecision Decision { get; set; }
        public string DecisionReason { get; set; }
        public Guid ProcessedBy { get; set; }
        public string ProcessedByName { get; set; }
        public DateTime ProcessedAt { get; set; }
        public bool WasAutoProcessed { get; set; }
        public decimal? OcrConfidence { get; set; }
        public string Notes { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
