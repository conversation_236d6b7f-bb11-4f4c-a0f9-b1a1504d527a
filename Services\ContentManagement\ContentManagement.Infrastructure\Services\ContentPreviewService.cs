using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.Enums;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ContentManagement.Infrastructure.Services;

/// <summary>
/// Service for content preview and rendering
/// </summary>
public interface IContentPreviewService
{
    Task<ContentPreviewDto> GeneratePreviewAsync(Content content, CancellationToken cancellationToken = default);
    Task<string> RenderContentAsync(string contentBody, ContentType contentType, CancellationToken cancellationToken = default);
    Task<ContentSeoAnalysisDto> AnalyzeSeoAsync(Content content, CancellationToken cancellationToken = default);
    Task<List<string>> ExtractKeywordsAsync(string text, int maxKeywords = 10, CancellationToken cancellationToken = default);
}

public class ContentPreviewService : IContentPreviewService
{
    private readonly ILogger<ContentPreviewService> _logger;

    public ContentPreviewService(ILogger<ContentPreviewService> logger)
    {
        _logger = logger;
    }

    public async Task<ContentPreviewDto> GeneratePreviewAsync(
        Content content,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var renderedContent = await RenderContentAsync(content.ContentBody, content.Type, cancellationToken);
            var excerpt = GenerateExcerpt(content.ContentBody, 200);
            var readingTime = CalculateReadingTime(content.ContentBody);
            var wordCount = CountWords(content.ContentBody);
            var seoAnalysis = await AnalyzeSeoAsync(content, cancellationToken);

            return new ContentPreviewDto
            {
                ContentId = content.Id,
                Title = content.Title,
                Excerpt = excerpt,
                RenderedContent = renderedContent,
                WordCount = wordCount,
                ReadingTimeMinutes = readingTime,
                SeoAnalysis = seoAnalysis,
                PreviewUrl = GeneratePreviewUrl(content),
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate preview for content {ContentId}", content.Id);
            throw;
        }
    }

    public async Task<string> RenderContentAsync(
        string contentBody,
        ContentType contentType,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Placeholder for async operation

        try
        {
            return contentType switch
            {
                ContentType.Faq => RenderFaqContent(contentBody),
                ContentType.Policy => RenderPolicyContent(contentBody),
                ContentType.StaticPage => RenderStaticPageContent(contentBody),
                ContentType.NewsArticle => RenderNewsContent(contentBody),
                ContentType.Announcement => RenderAnnouncementContent(contentBody),
                _ => RenderGenericContent(contentBody)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render content of type {ContentType}", contentType);
            return contentBody; // Return original content as fallback
        }
    }

    public async Task<ContentSeoAnalysisDto> AnalyzeSeoAsync(
        Content content,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Placeholder for async operation

        try
        {
            var analysis = new ContentSeoAnalysisDto
            {
                ContentId = content.Id,
                Score = 0,
                Issues = new List<string>(),
                Recommendations = new List<string>(),
                Keywords = await ExtractKeywordsAsync(content.ContentBody, 10, cancellationToken)
            };

            // Title analysis
            if (string.IsNullOrWhiteSpace(content.Title))
            {
                analysis.Issues.Add("Missing title");
                analysis.Recommendations.Add("Add a descriptive title");
            }
            else if (content.Title.Length < 30)
            {
                analysis.Issues.Add("Title too short");
                analysis.Recommendations.Add("Consider a longer, more descriptive title (30-60 characters)");
            }
            else if (content.Title.Length > 60)
            {
                analysis.Issues.Add("Title too long");
                analysis.Recommendations.Add("Shorten title to under 60 characters");
            }
            else
            {
                analysis.Score += 20;
            }

            // Meta description analysis
            if (string.IsNullOrWhiteSpace(content.MetaDescription))
            {
                analysis.Issues.Add("Missing meta description");
                analysis.Recommendations.Add("Add a meta description (150-160 characters)");
            }
            else if (content.MetaDescription.Length < 120)
            {
                analysis.Issues.Add("Meta description too short");
                analysis.Recommendations.Add("Expand meta description to 150-160 characters");
            }
            else if (content.MetaDescription.Length > 160)
            {
                analysis.Issues.Add("Meta description too long");
                analysis.Recommendations.Add("Shorten meta description to under 160 characters");
            }
            else
            {
                analysis.Score += 20;
            }

            // Content length analysis
            var wordCount = CountWords(content.ContentBody);
            if (wordCount < 300)
            {
                analysis.Issues.Add("Content too short");
                analysis.Recommendations.Add("Add more content (aim for 300+ words)");
            }
            else
            {
                analysis.Score += 20;
            }

            // Tags analysis
            if (!content.Tags.Any())
            {
                analysis.Issues.Add("No tags");
                analysis.Recommendations.Add("Add relevant tags to improve discoverability");
            }
            else if (content.Tags.Count > 10)
            {
                analysis.Issues.Add("Too many tags");
                analysis.Recommendations.Add("Limit tags to 5-10 most relevant ones");
            }
            else
            {
                analysis.Score += 20;
            }

            // Readability analysis
            var readabilityScore = CalculateReadabilityScore(content.ContentBody);
            if (readabilityScore < 60)
            {
                analysis.Issues.Add("Content may be difficult to read");
                analysis.Recommendations.Add("Use shorter sentences and simpler words");
            }
            else
            {
                analysis.Score += 20;
            }

            analysis.AnalyzedAt = DateTime.UtcNow;
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze SEO for content {ContentId}", content.Id);
            throw;
        }
    }

    public async Task<List<string>> ExtractKeywordsAsync(
        string text,
        int maxKeywords = 10,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Placeholder for async operation

        try
        {
            // Simple keyword extraction based on word frequency
            var words = Regex.Matches(text.ToLowerInvariant(), @"\b\w{4,}\b")
                .Cast<Match>()
                .Select(m => m.Value)
                .Where(word => !IsStopWord(word))
                .GroupBy(word => word)
                .OrderByDescending(g => g.Count())
                .Take(maxKeywords)
                .Select(g => g.Key)
                .ToList();

            return words;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract keywords from text");
            return new List<string>();
        }
    }

    private static string RenderFaqContent(string contentBody)
    {
        // Add FAQ-specific formatting
        return $"<div class=\"faq-content\">{contentBody}</div>";
    }

    private static string RenderPolicyContent(string contentBody)
    {
        // Add policy-specific formatting with sections
        return $"<div class=\"policy-content\">{contentBody}</div>";
    }

    private static string RenderStaticPageContent(string contentBody)
    {
        // Add static page formatting
        return $"<div class=\"page-content\">{contentBody}</div>";
    }

    private static string RenderNewsContent(string contentBody)
    {
        // Add news article formatting
        return $"<article class=\"news-content\">{contentBody}</article>";
    }

    private static string RenderAnnouncementContent(string contentBody)
    {
        // Add announcement formatting
        return $"<div class=\"announcement-content\">{contentBody}</div>";
    }

    private static string RenderGenericContent(string contentBody)
    {
        // Generic content formatting
        return $"<div class=\"content\">{contentBody}</div>";
    }

    private static string GenerateExcerpt(string content, int maxLength)
    {
        if (string.IsNullOrWhiteSpace(content))
            return string.Empty;

        // Remove HTML tags for excerpt
        var plainText = Regex.Replace(content, "<.*?>", string.Empty);
        
        if (plainText.Length <= maxLength)
            return plainText;

        // Find the last complete word within the limit
        var excerpt = plainText.Substring(0, maxLength);
        var lastSpace = excerpt.LastIndexOf(' ');
        
        if (lastSpace > 0)
            excerpt = excerpt.Substring(0, lastSpace);

        return excerpt + "...";
    }

    private static int CalculateReadingTime(string content)
    {
        var wordCount = CountWords(content);
        var wordsPerMinute = 200; // Average reading speed
        return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
    }

    private static int CountWords(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return 0;

        // Remove HTML tags and count words
        var plainText = Regex.Replace(content, "<.*?>", string.Empty);
        var words = Regex.Matches(plainText, @"\b\w+\b");
        return words.Count;
    }

    private static double CalculateReadabilityScore(string content)
    {
        // Simplified readability score (Flesch Reading Ease approximation)
        var sentences = Regex.Matches(content, @"[.!?]+").Count;
        var words = CountWords(content);
        var syllables = EstimateSyllables(content);

        if (sentences == 0 || words == 0)
            return 0;

        var avgSentenceLength = (double)words / sentences;
        var avgSyllablesPerWord = (double)syllables / words;

        var score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
        return Math.Max(0, Math.Min(100, score));
    }

    private static int EstimateSyllables(string content)
    {
        // Simple syllable estimation
        var words = Regex.Matches(content.ToLowerInvariant(), @"\b\w+\b");
        var totalSyllables = 0;

        foreach (Match word in words)
        {
            var syllables = Regex.Matches(word.Value, @"[aeiouy]+").Count;
            totalSyllables += Math.Max(1, syllables);
        }

        return totalSyllables;
    }

    private static string GeneratePreviewUrl(Content content)
    {
        return $"/preview/{content.Type.ToString().ToLowerInvariant()}/{content.Slug}";
    }

    private static bool IsStopWord(string word)
    {
        var stopWords = new HashSet<string>
        {
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
            "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did",
            "will", "would", "could", "should", "may", "might", "can", "this", "that", "these", "those"
        };

        return stopWords.Contains(word);
    }
}

/// <summary>
/// Content preview DTO
/// </summary>
public class ContentPreviewDto
{
    public Guid ContentId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Excerpt { get; set; } = string.Empty;
    public string RenderedContent { get; set; } = string.Empty;
    public int WordCount { get; set; }
    public int ReadingTimeMinutes { get; set; }
    public ContentSeoAnalysisDto? SeoAnalysis { get; set; }
    public string PreviewUrl { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Content SEO analysis DTO
/// </summary>
public class ContentSeoAnalysisDto
{
    public Guid ContentId { get; set; }
    public int Score { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public List<string> Keywords { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}
