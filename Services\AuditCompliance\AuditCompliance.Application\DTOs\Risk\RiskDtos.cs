namespace AuditCompliance.Application.DTOs.Risk;

/// <summary>
/// Risk assessment request DTO
/// </summary>
public class RiskAssessmentRequestDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Dictionary<string, object> EntityData { get; set; } = new();
    public List<string>? IncludeFactors { get; set; }
    public List<string>? ExcludeFactors { get; set; }
    public bool IncludeRecommendations { get; set; } = true;
}

/// <summary>
/// Risk score DTO
/// </summary>
public class RiskScoreDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public float OverallScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty; // Low, Medium, High, Critical
    public DateTime CalculatedAt { get; set; }
    public List<RiskFactorScoreDto> FactorScores { get; set; } = new();
    public List<RiskMitigationDto> Recommendations { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Risk factor score DTO
/// </summary>
public class RiskFactorScoreDto
{
    public Guid FactorId { get; set; }
    public string FactorName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public float Score { get; set; }
    public float Weight { get; set; }
    public float WeightedScore { get; set; }
    public string Impact { get; set; } = string.Empty;
    public string Evaluation { get; set; } = string.Empty;
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// Risk assessment DTO
/// </summary>
public class RiskAssessmentDto
{
    public Guid Id { get; set; }
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public float OverallRiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public DateTime AssessedAt { get; set; }
    public string AssessedBy { get; set; } = string.Empty;
    public List<RiskFactorScoreDto> FactorScores { get; set; } = new();
    public List<RiskFindingDto> Findings { get; set; } = new();
    public List<RiskMitigationDto> Recommendations { get; set; } = new();
    public string Status { get; set; } = string.Empty; // Draft, Completed, Approved, Archived
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? NextAssessmentDue { get; set; }
    public Guid? TenantId { get; set; }
}

/// <summary>
/// Create risk assessment DTO
/// </summary>
public class CreateRiskAssessmentDto
{
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public Dictionary<string, object> EntityData { get; set; } = new();
    public List<string>? IncludeFactors { get; set; }
    public List<string>? ExcludeFactors { get; set; }
    public string? Notes { get; set; }
    public bool AutoApprove { get; set; } = false;
}

/// <summary>
/// Risk factor DTO
/// </summary>
public class RiskFactorDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public string EvaluationMethod { get; set; } = string.Empty; // Formula, Lookup, ML, Manual
    public string EvaluationCriteria { get; set; } = string.Empty;
    public float Weight { get; set; }
    public float MinScore { get; set; }
    public float MaxScore { get; set; }
    public bool IsActive { get; set; }
    public List<string> ApplicableEntityTypes { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Create risk factor DTO
/// </summary>
public class CreateRiskFactorDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public string EvaluationMethod { get; set; } = string.Empty;
    public string EvaluationCriteria { get; set; } = string.Empty;
    public float Weight { get; set; }
    public float MinScore { get; set; } = 0;
    public float MaxScore { get; set; } = 100;
    public List<string> ApplicableEntityTypes { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Risk threshold DTO
/// </summary>
public class RiskThresholdDto
{
    public string RiskLevel { get; set; } = string.Empty;
    public float MinScore { get; set; }
    public float MaxScore { get; set; }
    public string Color { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
    public int EscalationTimeHours { get; set; }
}

/// <summary>
/// Risk finding DTO
/// </summary>
public class RiskFindingDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public float RiskScore { get; set; }
    public string Source { get; set; } = string.Empty;
    public DateTime IdentifiedAt { get; set; }
    public string Status { get; set; } = string.Empty; // Open, In Progress, Resolved, Closed
    public string? Resolution { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string? ResolvedBy { get; set; }
}

/// <summary>
/// Risk mitigation DTO
/// </summary>
public class RiskMitigationDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // Low, Medium, High, Critical
    public float RiskReduction { get; set; }
    public float ImplementationCost { get; set; }
    public int EstimatedTimeWeeks { get; set; }
    public string EffectivenessLevel { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public List<string> Prerequisites { get; set; } = new();
    public string Status { get; set; } = string.Empty; // Recommended, Planned, In Progress, Completed
    public DateTime? TargetDate { get; set; }
    public string? AssignedTo { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Risk mitigation plan DTO
/// </summary>
public class RiskMitigationPlanDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid RiskAssessmentId { get; set; }
    public List<RiskMitigationDto> Mitigations { get; set; } = new();
    public string Status { get; set; } = string.Empty; // Draft, Approved, In Progress, Completed
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? ApprovedAt { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? TargetCompletionDate { get; set; }
    public float TotalCost { get; set; }
    public float ExpectedRiskReduction { get; set; }
}

/// <summary>
/// Create risk mitigation plan DTO
/// </summary>
public class CreateRiskMitigationPlanDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid RiskAssessmentId { get; set; }
    public List<Guid> MitigationIds { get; set; } = new();
    public DateTime? TargetCompletionDate { get; set; }
}

/// <summary>
/// Update risk mitigation plan DTO
/// </summary>
public class UpdateRiskMitigationPlanDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Guid> MitigationIds { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime? TargetCompletionDate { get; set; }
}

/// <summary>
/// Risk trend DTO
/// </summary>
public class RiskTrendDto
{
    public DateTime Date { get; set; }
    public float RiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public int FindingsCount { get; set; }
    public int MitigationsCount { get; set; }
    public Dictionary<string, float> CategoryScores { get; set; } = new();
}

/// <summary>
/// Risk heat map DTO
/// </summary>
public class RiskHeatMapDto
{
    public DateTime GeneratedAt { get; set; }
    public List<RiskHeatMapItemDto> Items { get; set; } = new();
    public Dictionary<string, int> RiskLevelCounts { get; set; } = new();
    public RiskHeatMapSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Risk heat map item DTO
/// </summary>
public class RiskHeatMapItemDto
{
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public float RiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public DateTime LastAssessed { get; set; }
    public int CriticalFindings { get; set; }
    public int PendingMitigations { get; set; }
    public Dictionary<string, float> CategoryScores { get; set; } = new();
}

/// <summary>
/// Risk heat map request DTO
/// </summary>
public class RiskHeatMapRequestDto
{
    public List<string>? EntityTypes { get; set; }
    public List<string>? Categories { get; set; }
    public string? RiskLevel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? OrganizationId { get; set; }
    public int? Limit { get; set; }
}

/// <summary>
/// Risk heat map summary DTO
/// </summary>
public class RiskHeatMapSummaryDto
{
    public int TotalEntities { get; set; }
    public float AverageRiskScore { get; set; }
    public int CriticalRiskEntities { get; set; }
    public int HighRiskEntities { get; set; }
    public int MediumRiskEntities { get; set; }
    public int LowRiskEntities { get; set; }
    public string HighestRiskCategory { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Bulk risk assessment request DTO
/// </summary>
public class BulkRiskAssessmentRequestDto
{
    public List<Guid> EntityIds { get; set; } = new();
    public string? EntityType { get; set; }
    public List<string>? IncludeFactors { get; set; }
    public List<string>? ExcludeFactors { get; set; }
    public bool IncludeRecommendations { get; set; } = true;
    public bool AutoApprove { get; set; } = false;
}

/// <summary>
/// Bulk risk assessment result DTO
/// </summary>
public class BulkRiskAssessmentResultDto
{
    public Guid BatchId { get; set; }
    public int TotalEntities { get; set; }
    public int ProcessedEntities { get; set; }
    public int FailedEntities { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Status { get; set; } = string.Empty; // Processing, Completed, Failed
    public List<RiskAssessmentDto> Results { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Risk assessment summary DTO
/// </summary>
public class RiskAssessmentSummaryDto
{
    public DateTime GeneratedAt { get; set; }
    public Guid? OrganizationId { get; set; }
    public int TotalAssessments { get; set; }
    public float AverageRiskScore { get; set; }
    public Dictionary<string, int> RiskLevelDistribution { get; set; } = new();
    public Dictionary<string, float> CategoryAverages { get; set; } = new();
    public List<RiskTrendDto> RecentTrends { get; set; } = new();
    public List<RiskAssessmentDto> HighRiskEntities { get; set; } = new();
    public int PendingAssessments { get; set; }
    public int OverdueAssessments { get; set; }
}

/// <summary>
/// Risk assessment history DTO
/// </summary>
public class RiskAssessmentHistoryDto
{
    public Guid Id { get; set; }
    public float RiskScore { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
    public DateTime AssessedAt { get; set; }
    public string AssessedBy { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int FindingsCount { get; set; }
    public int RecommendationsCount { get; set; }
    public Dictionary<string, float> CategoryScores { get; set; } = new();
}

/// <summary>
/// Export risk data request DTO
/// </summary>
public class ExportRiskDataRequestDto
{
    public List<Guid>? EntityIds { get; set; }
    public List<string>? EntityTypes { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Format { get; set; } = "Excel"; // Excel, CSV, PDF, JSON
    public bool IncludeDetails { get; set; } = true;
    public bool IncludeRecommendations { get; set; } = true;
    public bool IncludeHistory { get; set; } = false;
}

/// <summary>
/// Schedule risk assessment DTO
/// </summary>
public class ScheduleRiskAssessmentDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Guid>? EntityIds { get; set; }
    public List<string>? EntityTypes { get; set; }
    public string Schedule { get; set; } = string.Empty; // Cron expression
    public bool IsActive { get; set; } = true;
    public List<string>? IncludeFactors { get; set; }
    public List<string>? ExcludeFactors { get; set; }
    public bool AutoApprove { get; set; } = false;
    public List<string> NotificationRecipients { get; set; } = new();
}
