using AutoMapper;
using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Interfaces;
using ContentManagement.Domain.Entities;
using ContentManagement.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace ContentManagement.Application.Handlers.Commands.Content;

/// <summary>
/// Handler for creating new content
/// </summary>
public class CreateContentCommandHandler : IRequestHandler<CreateContentCommand, ContentDto>
{
    private readonly IContentRepository _contentRepository;
    private readonly IContentAuditService _auditService;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateContentCommandHandler> _logger;

    public CreateContentCommandHandler(
        IContentRepository contentRepository,
        IContentAuditService auditService,
        IMapper mapper,
        ILogger<CreateContentCommandHandler> logger)
    {
        _contentRepository = contentRepository;
        _auditService = auditService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<ContentDto> Handle(CreateContentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating new content with title: {Title}", request.Title);

        try
        {
            // Ensure slug is unique
            var slug = await EnsureUniqueSlugAsync(request.Slug, cancellationToken);

            // Map visibility settings
            var visibility = request.Visibility != null
                ? MapVisibility(request.Visibility)
                : ContentVisibility.CreatePublic();

            // Map publish schedule
            var publishSchedule = request.PublishSchedule != null
                ? MapPublishSchedule(request.PublishSchedule)
                : null;

            // Create content entity
            var content = new Domain.Entities.Content(
                title: request.Title,
                slug: slug,
                type: request.Type,
                contentBody: request.ContentBody,
                createdBy: request.CreatedBy,
                createdByName: request.CreatedByName,
                description: request.Description,
                visibility: visibility,
                metaTitle: request.MetaTitle,
                metaDescription: request.MetaDescription,
                tags: request.Tags,
                sortOrder: request.SortOrder,
                publishSchedule: publishSchedule);

            // Save to repository
            await _contentRepository.AddAsync(content, cancellationToken);

            // Log audit event
            await _auditService.LogContentCreatedAsync(
                content,
                request.CreatedBy,
                request.CreatedByName,
                "ContentManager"); // This should come from user context

            _logger.LogInformation("Successfully created content with ID: {ContentId}", content.Id);

            // Return mapped DTO
            return _mapper.Map<ContentDto>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create content with title: {Title}", request.Title);
            throw;
        }
    }

    private async Task<string> EnsureUniqueSlugAsync(string slug, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(slug))
        {
            throw new ArgumentException("Slug cannot be empty");
        }

        var isUnique = await _contentRepository.IsSlugUniqueAsync(slug, null, cancellationToken);
        if (isUnique)
        {
            return slug;
        }

        return await _contentRepository.GenerateUniqueSlugAsync(slug, cancellationToken);
    }

    private static ContentVisibility MapVisibility(ContentVisibilityDto dto)
    {
        return dto.Level switch
        {
            Domain.Enums.VisibilityLevel.Public => ContentVisibility.CreatePublic(dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.Authenticated => ContentVisibility.CreateAuthenticated(dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.RoleSpecific => ContentVisibility.CreateRoleSpecific(dto.AllowedRoles, dto.VisibleFrom, dto.VisibleUntil),
            Domain.Enums.VisibilityLevel.Private => ContentVisibility.CreatePrivate(dto.VisibleFrom, dto.VisibleUntil),
            _ => ContentVisibility.CreatePublic()
        };
    }

    private static PublishSchedule? MapPublishSchedule(PublishScheduleDto dto)
    {
        if (dto.PublishAt == null && dto.UnpublishAt == null && !dto.IsRecurring)
        {
            return null;
        }

        if (dto.IsRecurring && dto.PublishAt != null)
        {
            return PublishSchedule.CreateRecurring(
                dto.PublishAt.Value,
                dto.Pattern,
                dto.RecurrenceInterval ?? 1,
                dto.RecurrenceEndDate,
                dto.UnpublishAt);
        }

        if (dto.PublishAt != null)
        {
            return PublishSchedule.CreateScheduled(dto.PublishAt.Value, dto.UnpublishAt);
        }

        return null;
    }
}

/// <summary>
/// Handler for updating existing content
/// </summary>
public class UpdateContentCommandHandler : IRequestHandler<UpdateContentCommand, ContentDto>
{
    private readonly IContentRepository _contentRepository;
    private readonly IContentAuditService _auditService;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateContentCommandHandler> _logger;

    public UpdateContentCommandHandler(
        IContentRepository contentRepository,
        IContentAuditService auditService,
        IMapper mapper,
        ILogger<UpdateContentCommandHandler> logger)
    {
        _contentRepository = contentRepository;
        _auditService = auditService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<ContentDto> Handle(UpdateContentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating content with ID: {ContentId}", request.Id);

        try
        {
            // Get existing content
            var content = await _contentRepository.GetByIdAsync(request.Id, cancellationToken);
            if (content == null)
            {
                throw new InvalidOperationException($"Content with ID {request.Id} not found");
            }

            // Check if content can be edited
            if (!content.CanBeEdited())
            {
                throw new InvalidOperationException($"Content with status {content.Status} cannot be edited");
            }

            // Update content
            content.UpdateContent(
                title: request.Title,
                description: request.Description,
                contentBody: request.ContentBody,
                updatedBy: request.UpdatedBy,
                updatedByName: request.UpdatedByName,
                changeDescription: request.ChangeDescription,
                metaTitle: request.MetaTitle,
                metaDescription: request.MetaDescription,
                tags: request.Tags);

            // Save changes
            await _contentRepository.UpdateAsync(content, cancellationToken);

            _logger.LogInformation("Successfully updated content with ID: {ContentId}", content.Id);

            // Return mapped DTO
            return _mapper.Map<ContentDto>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update content with ID: {ContentId}", request.Id);
            throw;
        }
    }
}

