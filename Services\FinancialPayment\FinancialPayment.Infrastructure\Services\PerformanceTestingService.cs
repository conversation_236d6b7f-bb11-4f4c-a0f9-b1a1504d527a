using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FinancialPayment.Infrastructure.Services;

public class PerformanceTestingService : IPerformanceTestingService
{
    private readonly ILoadGenerator _loadGenerator;
    private readonly IPerformanceMetricsCollector _metricsCollector;
    private readonly ITestScenarioExecutor _scenarioExecutor;
    private readonly ILogger<PerformanceTestingService> _logger;
    private readonly PerformanceTestConfiguration _configuration;
    
    // In-memory storage for demo purposes
    private readonly ConcurrentBag<PerformanceTestResult> _testResults;
    private readonly ConcurrentBag<PerformanceTestScenario> _testScenarios;
    private readonly ConcurrentDictionary<Guid, TestExecution> _activeTests;

    public PerformanceTestingService(
        ILoadGenerator loadGenerator,
        IPerformanceMetricsCollector metricsCollector,
        ITestScenarioExecutor scenarioExecutor,
        ILogger<PerformanceTestingService> logger,
        IOptions<PerformanceTestConfiguration> configuration)
    {
        _loadGenerator = loadGenerator;
        _metricsCollector = metricsCollector;
        _scenarioExecutor = scenarioExecutor;
        _logger = logger;
        _configuration = configuration.Value;
        _testResults = new ConcurrentBag<PerformanceTestResult>();
        _testScenarios = new ConcurrentBag<PerformanceTestScenario>();
        _activeTests = new ConcurrentDictionary<Guid, TestExecution>();

        // Initialize with default scenarios
        InitializeDefaultScenarios();
    }

    public async Task<PerformanceTestResult> RunLoadTestAsync(LoadTestConfiguration configuration)
    {
        _logger.LogInformation("Starting load test: {TestName} with {ConcurrentUsers} users for {Duration}",
            configuration.TestName, configuration.ConcurrentUsers, configuration.Duration);

        var testId = Guid.NewGuid();
        var startTime = DateTime.UtcNow;

        try
        {
            // Start metrics collection
            await _metricsCollector.StartMetricsCollectionAsync(testId);

            // Execute load test
            var execution = await _loadGenerator.StartLoadTestAsync(configuration);
            _activeTests.TryAdd(testId, execution);

            // Wait for test completion
            var result = await WaitForTestCompletionAsync(testId, execution, configuration.Duration);
            
            // Stop metrics collection
            await _metricsCollector.StopMetricsCollectionAsync(testId);

            // Collect final metrics
            var metrics = await _metricsCollector.CollectMetricsAsync(configuration.Duration);
            
            // Create test result
            var testResult = new PerformanceTestResult
            {
                TestId = testId,
                TestName = configuration.TestName,
                TestType = "Load Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Duration = DateTime.UtcNow - startTime,
                Status = TestStatus.Completed,
                Metrics = metrics,
                Configuration = new Dictionary<string, object>
                {
                    { "concurrent_users", configuration.ConcurrentUsers },
                    { "duration", configuration.Duration },
                    { "ramp_up_time", configuration.RampUpTime }
                }
            };

            // Analyze results and generate recommendations
            AnalyzeTestResults(testResult, configuration.Thresholds);
            
            _testResults.Add(testResult);
            _activeTests.TryRemove(testId, out _);

            _logger.LogInformation("Load test completed: {TestName} - Grade: {Grade}", 
                configuration.TestName, testResult.Grade);

            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running load test: {TestName}", configuration.TestName);
            
            var failedResult = new PerformanceTestResult
            {
                TestId = testId,
                TestName = configuration.TestName,
                TestType = "Load Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Duration = DateTime.UtcNow - startTime,
                Status = TestStatus.Failed,
                ErrorMessage = ex.Message,
                Grade = PerformanceGrade.Critical
            };

            _testResults.Add(failedResult);
            _activeTests.TryRemove(testId, out _);
            
            return failedResult;
        }
    }

    public async Task<PerformanceTestResult> RunStressTestAsync(StressTestConfiguration configuration)
    {
        _logger.LogInformation("Starting stress test: {TestName} ramping up to {MaxUsers} users",
            configuration.TestName, configuration.MaxConcurrentUsers);

        var testId = Guid.NewGuid();
        var startTime = DateTime.UtcNow;

        try
        {
            await _metricsCollector.StartMetricsCollectionAsync(testId);
            
            var execution = await _loadGenerator.StartStressTestAsync(configuration);
            _activeTests.TryAdd(testId, execution);

            var result = await WaitForTestCompletionAsync(testId, execution, configuration.StressDuration);
            
            await _metricsCollector.StopMetricsCollectionAsync(testId);
            var metrics = await _metricsCollector.CollectMetricsAsync(configuration.StressDuration);

            var testResult = new PerformanceTestResult
            {
                TestId = testId,
                TestName = configuration.TestName,
                TestType = "Stress Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Duration = DateTime.UtcNow - startTime,
                Status = TestStatus.Completed,
                Metrics = metrics,
                Configuration = new Dictionary<string, object>
                {
                    { "max_concurrent_users", configuration.MaxConcurrentUsers },
                    { "stress_duration", configuration.StressDuration },
                    { "user_increment_step", configuration.UserIncrementStep }
                }
            };

            AnalyzeTestResults(testResult, configuration.Thresholds);
            _testResults.Add(testResult);
            _activeTests.TryRemove(testId, out _);

            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running stress test: {TestName}", configuration.TestName);
            return CreateFailedResult(testId, configuration.TestName, "Stress Test", startTime, ex.Message);
        }
    }

    public async Task<PerformanceTestResult> RunSpikeTestAsync(SpikeTestConfiguration configuration)
    {
        _logger.LogInformation("Starting spike test: {TestName} with {SpikeUsers} spike users",
            configuration.TestName, configuration.SpikeUsers);

        var testId = Guid.NewGuid();
        var startTime = DateTime.UtcNow;

        try
        {
            await _metricsCollector.StartMetricsCollectionAsync(testId);
            
            var execution = await _loadGenerator.StartSpikeTestAsync(configuration);
            _activeTests.TryAdd(testId, execution);

            var totalDuration = configuration.SpikeDuration.Add(
                TimeSpan.FromTicks(configuration.TimeBetweenSpikes.Ticks * (configuration.NumberOfSpikes - 1)));
            
            var result = await WaitForTestCompletionAsync(testId, execution, totalDuration);
            
            await _metricsCollector.StopMetricsCollectionAsync(testId);
            var metrics = await _metricsCollector.CollectMetricsAsync(totalDuration);

            var testResult = new PerformanceTestResult
            {
                TestId = testId,
                TestName = configuration.TestName,
                TestType = "Spike Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Duration = DateTime.UtcNow - startTime,
                Status = TestStatus.Completed,
                Metrics = metrics,
                Configuration = new Dictionary<string, object>
                {
                    { "spike_users", configuration.SpikeUsers },
                    { "number_of_spikes", configuration.NumberOfSpikes },
                    { "spike_duration", configuration.SpikeDuration }
                }
            };

            AnalyzeTestResults(testResult, configuration.Thresholds);
            _testResults.Add(testResult);
            _activeTests.TryRemove(testId, out _);

            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running spike test: {TestName}", configuration.TestName);
            return CreateFailedResult(testId, configuration.TestName, "Spike Test", startTime, ex.Message);
        }
    }

    public async Task<PerformanceTestResult> RunEnduranceTestAsync(EnduranceTestConfiguration configuration)
    {
        _logger.LogInformation("Starting endurance test: {TestName} for {Duration}",
            configuration.TestName, configuration.EnduranceDuration);

        var testId = Guid.NewGuid();
        var startTime = DateTime.UtcNow;

        try
        {
            await _metricsCollector.StartMetricsCollectionAsync(testId);
            
            var execution = await _loadGenerator.StartEnduranceTestAsync(configuration);
            _activeTests.TryAdd(testId, execution);

            var result = await WaitForTestCompletionAsync(testId, execution, configuration.EnduranceDuration);
            
            await _metricsCollector.StopMetricsCollectionAsync(testId);
            var metrics = await _metricsCollector.CollectMetricsAsync(configuration.EnduranceDuration);

            var testResult = new PerformanceTestResult
            {
                TestId = testId,
                TestName = configuration.TestName,
                TestType = "Endurance Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Duration = DateTime.UtcNow - startTime,
                Status = TestStatus.Completed,
                Metrics = metrics,
                Configuration = new Dictionary<string, object>
                {
                    { "endurance_duration", configuration.EnduranceDuration },
                    { "monitor_memory_leaks", configuration.MonitorMemoryLeaks },
                    { "monitor_resource_degradation", configuration.MonitorResourceDegradation }
                }
            };

            AnalyzeTestResults(testResult, configuration.Thresholds);
            _testResults.Add(testResult);
            _activeTests.TryRemove(testId, out _);

            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running endurance test: {TestName}", configuration.TestName);
            return CreateFailedResult(testId, configuration.TestName, "Endurance Test", startTime, ex.Message);
        }
    }

    public async Task<List<PerformanceTestResult>> GetTestResultsAsync(DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            var results = _testResults
                .Where(r => r.StartTime >= from && r.StartTime <= to)
                .OrderByDescending(r => r.StartTime)
                .ToList();

            return await Task.FromResult(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test results");
            return new List<PerformanceTestResult>();
        }
    }

    public async Task<PerformanceTestResult?> GetTestResultAsync(Guid testId)
    {
        try
        {
            var result = _testResults.FirstOrDefault(r => r.TestId == testId);
            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test result {TestId}", testId);
            return null;
        }
    }

    public async Task<bool> StopTestAsync(Guid testId)
    {
        try
        {
            if (_activeTests.TryGetValue(testId, out var execution))
            {
                await _loadGenerator.StopTestAsync(execution.Id);
                await _metricsCollector.StopMetricsCollectionAsync(testId);
                
                execution.Status = TestExecutionStatus.Cancelled;
                execution.EndTime = DateTime.UtcNow;
                
                _logger.LogInformation("Test {TestId} stopped", testId);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping test {TestId}", testId);
            return false;
        }
    }

    public async Task<List<PerformanceTestScenario>> GetTestScenariosAsync()
    {
        try
        {
            var scenarios = _testScenarios.Where(s => s.IsEnabled).OrderBy(s => s.Name).ToList();
            return await Task.FromResult(scenarios);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting test scenarios");
            return new List<PerformanceTestScenario>();
        }
    }

    public async Task<PerformanceTestScenario> CreateTestScenarioAsync(CreateTestScenarioRequest request)
    {
        try
        {
            var scenario = new PerformanceTestScenario
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Description = request.Description,
                ScenarioType = request.ScenarioType,
                Steps = request.Steps,
                Parameters = request.Parameters,
                Weight = request.Weight,
                IsEnabled = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            _testScenarios.Add(scenario);
            
            _logger.LogInformation("Test scenario created: {ScenarioName}", request.Name);
            return await Task.FromResult(scenario);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating test scenario {ScenarioName}", request.Name);
            throw;
        }
    }

    public async Task<PerformanceBenchmark> GetPerformanceBenchmarkAsync()
    {
        try
        {
            // Create a benchmark based on recent successful tests
            var recentTests = _testResults
                .Where(r => r.Status == TestStatus.Completed && r.StartTime >= DateTime.UtcNow.AddDays(-30))
                .OrderByDescending(r => r.StartTime)
                .Take(10)
                .ToList();

            if (!recentTests.Any())
            {
                return new PerformanceBenchmark
                {
                    BenchmarkDate = DateTime.UtcNow,
                    Environment = "Unknown"
                };
            }

            var benchmark = new PerformanceBenchmark
            {
                BenchmarkDate = DateTime.UtcNow,
                Environment = "Production", // Would be configurable
                BaselineMetrics = CalculateAverageMetrics(recentTests.Select(t => t.Metrics).ToList())
            };

            return await Task.FromResult(benchmark);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance benchmark");
            throw;
        }
    }

    public async Task<PerformanceComparison> CompareTestResultsAsync(Guid baselineTestId, Guid comparisonTestId)
    {
        try
        {
            var baselineTest = _testResults.FirstOrDefault(r => r.TestId == baselineTestId);
            var comparisonTest = _testResults.FirstOrDefault(r => r.TestId == comparisonTestId);

            if (baselineTest == null || comparisonTest == null)
            {
                throw new ArgumentException("One or both test results not found");
            }

            var comparison = new PerformanceComparison
            {
                BaselineTestId = baselineTestId,
                ComparisonTestId = comparisonTestId,
                ComparisonDate = DateTime.UtcNow,
                MetricComparisons = CompareMetrics(baselineTest.Metrics, comparisonTest.Metrics)
            };

            // Determine overall trend
            var significantChanges = comparison.MetricComparisons.Where(mc => mc.IsSignificant).ToList();
            var improvements = significantChanges.Count(sc => sc.Trend == "Improved");
            var degradations = significantChanges.Count(sc => sc.Trend == "Degraded");

            comparison.OverallTrend = improvements > degradations ? "Improved" :
                                     degradations > improvements ? "Degraded" : "Stable";

            comparison.SignificantChanges = significantChanges.Select(sc => 
                $"{sc.MetricName}: {sc.Trend} by {Math.Abs(sc.PercentageChange):F1}%").ToList();

            return await Task.FromResult(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing test results");
            throw;
        }
    }

    private async Task<bool> WaitForTestCompletionAsync(Guid testId, TestExecution execution, TimeSpan maxDuration)
    {
        var timeout = DateTime.UtcNow.Add(maxDuration).AddMinutes(5); // Add 5 minutes buffer
        
        while (DateTime.UtcNow < timeout)
        {
            var status = await _loadGenerator.GetExecutionStatusAsync(execution.Id);
            
            if (status == TestExecutionStatus.Completed || 
                status == TestExecutionStatus.Failed || 
                status == TestExecutionStatus.Cancelled)
            {
                return status == TestExecutionStatus.Completed;
            }
            
            await Task.Delay(TimeSpan.FromSeconds(5));
        }
        
        // Timeout reached, stop the test
        await _loadGenerator.StopTestAsync(execution.Id);
        return false;
    }

    private void AnalyzeTestResults(PerformanceTestResult result, PerformanceThresholds thresholds)
    {
        var issues = new List<PerformanceIssue>();
        var recommendations = new List<PerformanceRecommendation>();

        // Check response time thresholds
        if (result.Metrics.AverageResponseTime > thresholds.MaxResponseTime)
        {
            issues.Add(new PerformanceIssue
            {
                IssueType = "Response Time",
                Severity = "High",
                Description = $"Average response time ({result.Metrics.AverageResponseTime.TotalMilliseconds:F0}ms) exceeds threshold ({thresholds.MaxResponseTime.TotalMilliseconds:F0}ms)",
                Component = "API",
                DetectedAt = DateTime.UtcNow
            });

            recommendations.Add(new PerformanceRecommendation
            {
                Category = "Performance",
                Title = "Optimize Response Time",
                Description = "Consider implementing caching, database query optimization, or scaling resources",
                Priority = "High",
                ActionItems = new List<string>
                {
                    "Review slow database queries",
                    "Implement Redis caching for frequently accessed data",
                    "Consider horizontal scaling"
                }
            });
        }

        // Check success rate
        if (result.Metrics.SuccessRate < thresholds.MinSuccessRate)
        {
            issues.Add(new PerformanceIssue
            {
                IssueType = "Success Rate",
                Severity = "Critical",
                Description = $"Success rate ({result.Metrics.SuccessRate:F1}%) is below threshold ({thresholds.MinSuccessRate:F1}%)",
                Component = "System",
                DetectedAt = DateTime.UtcNow
            });
        }

        // Check system resources
        if (result.Metrics.SystemResources.CpuUsagePercent > thresholds.MaxCpuUsage)
        {
            issues.Add(new PerformanceIssue
            {
                IssueType = "CPU Usage",
                Severity = "Medium",
                Description = $"CPU usage ({result.Metrics.SystemResources.CpuUsagePercent:F1}%) exceeds threshold ({thresholds.MaxCpuUsage:F1}%)",
                Component = "System",
                DetectedAt = DateTime.UtcNow
            });
        }

        result.Issues = issues;
        result.Recommendations = recommendations;

        // Calculate grade
        result.Grade = CalculatePerformanceGrade(result.Metrics, thresholds);
    }

    private PerformanceGrade CalculatePerformanceGrade(PerformanceMetrics metrics, PerformanceThresholds thresholds)
    {
        var score = 100;

        // Deduct points for various issues
        if (metrics.AverageResponseTime > thresholds.MaxResponseTime)
            score -= 20;
        
        if (metrics.SuccessRate < thresholds.MinSuccessRate)
            score -= 30;
        
        if (metrics.SystemResources.CpuUsagePercent > thresholds.MaxCpuUsage)
            score -= 15;
        
        if (metrics.SystemResources.MemoryUsagePercent > thresholds.MaxMemoryUsage)
            score -= 15;

        return score switch
        {
            >= 90 => PerformanceGrade.Excellent,
            >= 80 => PerformanceGrade.Good,
            >= 70 => PerformanceGrade.Fair,
            >= 60 => PerformanceGrade.Poor,
            _ => PerformanceGrade.Critical
        };
    }

    private PerformanceTestResult CreateFailedResult(Guid testId, string testName, string testType, DateTime startTime, string errorMessage)
    {
        var failedResult = new PerformanceTestResult
        {
            TestId = testId,
            TestName = testName,
            TestType = testType,
            StartTime = startTime,
            EndTime = DateTime.UtcNow,
            Duration = DateTime.UtcNow - startTime,
            Status = TestStatus.Failed,
            ErrorMessage = errorMessage,
            Grade = PerformanceGrade.Critical
        };

        _testResults.Add(failedResult);
        _activeTests.TryRemove(testId, out _);
        
        return failedResult;
    }

    private PerformanceMetrics CalculateAverageMetrics(List<PerformanceMetrics> metricsList)
    {
        if (!metricsList.Any()) return new PerformanceMetrics();

        return new PerformanceMetrics
        {
            AverageResponseTime = TimeSpan.FromMilliseconds(metricsList.Average(m => m.AverageResponseTime.TotalMilliseconds)),
            SuccessRate = metricsList.Average(m => m.SuccessRate),
            RequestsPerSecond = metricsList.Average(m => m.RequestsPerSecond),
            ThroughputMBps = metricsList.Average(m => m.ThroughputMBps)
        };
    }

    private List<MetricComparison> CompareMetrics(PerformanceMetrics baseline, PerformanceMetrics comparison)
    {
        var comparisons = new List<MetricComparison>();

        // Compare response time
        var responseTimeChange = CalculatePercentageChange(
            baseline.AverageResponseTime.TotalMilliseconds,
            comparison.AverageResponseTime.TotalMilliseconds);

        comparisons.Add(new MetricComparison
        {
            MetricName = "Average Response Time",
            BaselineValue = baseline.AverageResponseTime.TotalMilliseconds,
            ComparisonValue = comparison.AverageResponseTime.TotalMilliseconds,
            PercentageChange = responseTimeChange,
            Trend = responseTimeChange < -5 ? "Improved" : responseTimeChange > 5 ? "Degraded" : "Stable",
            IsSignificant = Math.Abs(responseTimeChange) > 5
        });

        // Compare success rate
        var successRateChange = CalculatePercentageChange(baseline.SuccessRate, comparison.SuccessRate);
        
        comparisons.Add(new MetricComparison
        {
            MetricName = "Success Rate",
            BaselineValue = baseline.SuccessRate,
            ComparisonValue = comparison.SuccessRate,
            PercentageChange = successRateChange,
            Trend = successRateChange > 1 ? "Improved" : successRateChange < -1 ? "Degraded" : "Stable",
            IsSignificant = Math.Abs(successRateChange) > 1
        });

        return comparisons;
    }

    private decimal CalculatePercentageChange(double baseline, double comparison)
    {
        if (baseline == 0) return 0;
        return (decimal)((comparison - baseline) / baseline * 100);
    }

    private void InitializeDefaultScenarios()
    {
        // Payment processing scenario
        var paymentScenario = new PerformanceTestScenario
        {
            Id = Guid.NewGuid(),
            Name = "Payment Processing",
            Description = "Test payment processing performance",
            ScenarioType = "Payment",
            Steps = new List<TestStep>
            {
                new TestStep
                {
                    Order = 1,
                    StepName = "Create Payment",
                    Action = "HTTP_REQUEST",
                    Parameters = new Dictionary<string, object>
                    {
                        { "method", "POST" },
                        { "endpoint", "/api/payments" },
                        { "body", "payment_request_template" }
                    }
                },
                new TestStep
                {
                    Order = 2,
                    StepName = "Verify Payment",
                    Action = "HTTP_REQUEST",
                    Parameters = new Dictionary<string, object>
                    {
                        { "method", "GET" },
                        { "endpoint", "/api/payments/{payment_id}" }
                    }
                }
            },
            Weight = 3,
            IsEnabled = true,
            CreatedAt = DateTime.UtcNow
        };

        _testScenarios.Add(paymentScenario);

        // Subscription scenario
        var subscriptionScenario = new PerformanceTestScenario
        {
            Id = Guid.NewGuid(),
            Name = "Subscription Management",
            Description = "Test subscription operations performance",
            ScenarioType = "Subscription",
            Steps = new List<TestStep>
            {
                new TestStep
                {
                    Order = 1,
                    StepName = "Get Plans",
                    Action = "HTTP_REQUEST",
                    Parameters = new Dictionary<string, object>
                    {
                        { "method", "GET" },
                        { "endpoint", "/api/subscription-plans" }
                    }
                },
                new TestStep
                {
                    Order = 2,
                    StepName = "Create Subscription",
                    Action = "HTTP_REQUEST",
                    Parameters = new Dictionary<string, object>
                    {
                        { "method", "POST" },
                        { "endpoint", "/api/subscriptions" },
                        { "body", "subscription_request_template" }
                    }
                }
            },
            Weight = 2,
            IsEnabled = true,
            CreatedAt = DateTime.UtcNow
        };

        _testScenarios.Add(subscriptionScenario);

        _logger.LogInformation("Default performance test scenarios initialized");
    }
}
