using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Infrastructure.Hubs;

namespace UserManagement.Infrastructure.Services
{
    public class SignalRNotificationService : INotificationService
    {
        private readonly IHubContext<UserManagementHub, IUserManagementClient> _hubContext;
        private readonly ILogger<SignalRNotificationService> _logger;

        public SignalRNotificationService(
            IHubContext<UserManagementHub, IUserManagementClient> hubContext,
            ILogger<SignalRNotificationService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task NotifyUserApprovedAsync(Guid userId, string userType, string displayName, string email,
            Guid approvedBy, DateTime approvedAt, string notes = "")
        {
            var notification = new UserApprovedNotification
            {
                UserId = userId,
                UserType = userType,
                DisplayName = displayName,
                Email = email,
                ApprovedBy = approvedBy.ToString(),
                ApprovedAt = approvedAt,
                Notes = notes
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").UserApproved(notification);

            // Notify all admins
            await _hubContext.Clients.Group("Admins").UserApproved(notification);

            _logger.LogInformation("Sent user approved notification for UserId: {UserId}", userId);
        }

        public async Task NotifyUserRejectedAsync(Guid userId, string userType, string displayName, string email,
            Guid rejectedBy, DateTime rejectedAt, string reason, string notes = "")
        {
            var notification = new UserRejectedNotification
            {
                UserId = userId,
                UserType = userType,
                DisplayName = displayName,
                Email = email,
                RejectedBy = rejectedBy.ToString(),
                RejectedAt = rejectedAt,
                Reason = reason,
                Notes = notes
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").UserRejected(notification);

            // Notify all admins
            await _hubContext.Clients.Group("Admins").UserRejected(notification);

            _logger.LogInformation("Sent user rejected notification for UserId: {UserId}", userId);
        }

        public async Task NotifyDocumentsApprovedAsync(Guid submissionId, Guid userId, string userType,
            List<string> approvedDocuments, Guid approvedBy, DateTime processedAt, string notes = "")
        {
            var notification = new DocumentsApprovedNotification
            {
                SubmissionId = submissionId,
                UserId = userId,
                UserType = userType,
                DocumentTypes = approvedDocuments,
                ApprovedDocuments = approvedDocuments,
                ApprovedBy = approvedBy.ToString(),
                ApprovedAt = processedAt,
                ProcessedAt = processedAt,
                Notes = notes
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").DocumentsApproved(notification);

            // Notify all admins
            await _hubContext.Clients.Group("Admins").DocumentsApproved(notification);

            _logger.LogInformation("Sent documents approved notification for SubmissionId: {SubmissionId}", submissionId);
        }

        public async Task NotifyDocumentsRejectedAsync(Guid submissionId, Guid userId, string userType,
            List<Application.Services.DocumentRejectionInfo> rejectedDocuments, Guid rejectedBy, DateTime processedAt, string notes = "")
        {
            var notification = new DocumentsRejectedNotification
            {
                SubmissionId = submissionId,
                UserId = userId,
                UserType = userType,
                RejectedDocuments = rejectedDocuments.Select(d => new UserManagement.Infrastructure.Hubs.DocumentRejectionInfo
                {
                    DocumentType = d.DocumentType,
                    Action = d.Action,
                    Reason = d.Reason
                }).ToList(),
                RejectedBy = rejectedBy.ToString(),
                RejectedAt = processedAt,
                ProcessedAt = processedAt,
                Notes = notes
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").DocumentsRejected(notification);

            // Notify all admins
            await _hubContext.Clients.Group("Admins").DocumentsRejected(notification);

            _logger.LogInformation("Sent documents rejected notification for SubmissionId: {SubmissionId}", submissionId);
        }

        public async Task NotifyDocumentOcrProcessedAsync(Guid submissionId, Guid userId, string documentType,
            decimal confidenceScore, bool requiresManualReview, bool autoApproved,
            List<string> validationErrors, DateTime processedAt)
        {
            var notification = new DocumentOcrProcessedNotification
            {
                SubmissionId = submissionId,
                UserId = userId,
                DocumentType = documentType,
                ConfidenceScore = confidenceScore,
                RequiresManualReview = requiresManualReview,
                AutoApproved = autoApproved,
                ValidationErrors = validationErrors,
                ProcessedAt = processedAt
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").DocumentOcrProcessed(notification);

            // Notify all admins if manual review is required
            if (requiresManualReview)
            {
                await _hubContext.Clients.Group("Admins").DocumentOcrProcessed(notification);
            }

            _logger.LogInformation("Sent document OCR processed notification for SubmissionId: {SubmissionId}, DocumentType: {DocumentType}",
                submissionId, documentType);
        }

        public async Task NotifyProfileStatusChangedAsync(Guid userId, string userType, string displayName,
            string previousStatus, string newStatus, DateTime changedAt, string reason = "")
        {
            var notification = new ProfileStatusChangedNotification
            {
                UserId = userId,
                UserName = displayName,
                UserType = userType,
                DisplayName = displayName,
                PreviousStatus = previousStatus,
                NewStatus = newStatus,
                ChangedAt = changedAt,
                ChangedBy = "System",
                Reason = reason
            };

            // Notify the specific user
            await _hubContext.Clients.Group($"User_{userId}").ProfileStatusChanged(notification);

            // Notify all admins
            await _hubContext.Clients.Group("Admins").ProfileStatusChanged(notification);

            _logger.LogInformation("Sent profile status changed notification for UserId: {UserId}, Status: {PreviousStatus} -> {NewStatus}",
                userId, previousStatus, newStatus);
        }

        public async Task NotifyNewUserRegisteredAsync(Guid userId, string userType, string displayName,
            string email, DateTime registeredAt)
        {
            var notification = new NewUserRegisteredNotification
            {
                UserId = userId,
                UserType = userType,
                DisplayName = displayName,
                Email = email,
                RegisteredAt = registeredAt
            };

            // Notify all admins
            await _hubContext.Clients.Group("Admins").NewUserRegistered(notification);

            _logger.LogInformation("Sent new user registered notification for UserId: {UserId}, UserType: {UserType}",
                userId, userType);
        }

        public async Task NotifyDocumentSubmittedAsync(Guid submissionId, Guid userId, string userType,
            string displayName, int documentCount, DateTime submittedAt)
        {
            var notification = new DocumentSubmittedNotification
            {
                SubmissionId = submissionId,
                UserId = userId,
                UserName = displayName,
                UserType = userType,
                DisplayName = displayName,
                DocumentType = "Multiple",
                DocumentCount = documentCount,
                SubmittedAt = submittedAt,
                RequiresReview = true
            };

            // Notify all admins
            await _hubContext.Clients.Group("Admins").DocumentSubmitted(notification);

            _logger.LogInformation("Sent document submitted notification for SubmissionId: {SubmissionId}, DocumentCount: {DocumentCount}",
                submissionId, documentCount);
        }

        public async Task SendSystemNotificationAsync(string title, string message, string type = "info",
            Dictionary<string, object>? data = null, Guid? targetUserId = null, string? targetRole = null)
        {
            var notification = new SystemNotification
            {
                Id = Guid.NewGuid(),
                Title = title,
                Message = message,
                Type = type,
                CreatedAt = DateTime.UtcNow,
                Timestamp = DateTime.UtcNow,
                Data = data ?? new Dictionary<string, object>(),
                TargetUserId = targetUserId?.ToString(),
                TargetRole = targetRole
            };

            if (targetUserId.HasValue)
            {
                // Send to specific user
                await _hubContext.Clients.Group($"User_{targetUserId.Value}").SystemNotification(notification);
                _logger.LogInformation("Sent system notification to UserId: {UserId}, Title: {Title}", targetUserId.Value, title);
            }
            else if (!string.IsNullOrEmpty(targetRole))
            {
                // Send to specific role
                await _hubContext.Clients.Group(targetRole).SystemNotification(notification);
                _logger.LogInformation("Sent system notification to Role: {Role}, Title: {Title}", targetRole, title);
            }
            else
            {
                // Send to all connected users
                await _hubContext.Clients.All.SystemNotification(notification);
                _logger.LogInformation("Sent system notification to all users, Title: {Title}", title);
            }
        }

        public async Task SendToUserAsync<T>(Guid userId, string method, T data)
        {
            await ((IClientProxy)_hubContext.Clients.Group($"User_{userId}")).SendAsync(method, data);
            _logger.LogDebug("Sent custom notification to UserId: {UserId}, Method: {Method}", userId, method);
        }

        public async Task SendToRoleAsync<T>(string role, string method, T data)
        {
            await ((IClientProxy)_hubContext.Clients.Group(role)).SendAsync(method, data);
            _logger.LogDebug("Sent custom notification to Role: {Role}, Method: {Method}", role, method);
        }

        public async Task SendToAllAsync<T>(string method, T data)
        {
            await ((IClientProxy)_hubContext.Clients.All).SendAsync(method, data);
            _logger.LogDebug("Sent custom notification to all users, Method: {Method}", method);
        }

        public async Task SendAccountActionNotificationAsync(Guid userId, string action, string reason,
            Dictionary<string, object>? additionalData = null)
        {
            var notification = new SystemNotification
            {
                Id = Guid.NewGuid(),
                Title = "Account Action",
                Message = $"Account action '{action}' performed. Reason: {reason}",
                Type = "account_action",
                Data = additionalData ?? new Dictionary<string, object>(),
                TargetUserId = userId.ToString(),
                CreatedAt = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"User_{userId}").SystemNotification(notification);
            _logger.LogInformation("Sent account action notification for UserId: {UserId}, Action: {Action}", userId, action);
        }

        public async Task SendKycStatusNotificationAsync(Guid userId, string status, string reason = "",
            Dictionary<string, object>? additionalData = null)
        {
            var notification = new SystemNotification
            {
                Id = Guid.NewGuid(),
                Title = "KYC Status Update",
                Message = $"KYC status updated to '{status}'. {reason}",
                Type = "kyc_status",
                Data = additionalData ?? new Dictionary<string, object>(),
                TargetUserId = userId.ToString(),
                CreatedAt = DateTime.UtcNow
            };

            await _hubContext.Clients.Group($"User_{userId}").SystemNotification(notification);
            _logger.LogInformation("Sent KYC status notification for UserId: {UserId}, Status: {Status}", userId, status);
        }

        public async Task SendRedFlagAlertAsync(object redFlag, CancellationToken cancellationToken = default)
        {
            var notification = new SystemNotification
            {
                Id = Guid.NewGuid(),
                Title = "Red Flag Alert",
                Message = "A red flag has been detected in feedback",
                Type = "red_flag_alert",
                Data = new Dictionary<string, object> { { "redFlag", redFlag } },
                CreatedAt = DateTime.UtcNow
            };

            await _hubContext.Clients.Group("Admins").SystemNotification(notification);
            _logger.LogInformation("Sent red flag alert notification");
        }

        public async Task SendFeedbackNotificationAsync(object feedback, CancellationToken cancellationToken = default)
        {
            var notification = new SystemNotification
            {
                Id = Guid.NewGuid(),
                Title = "New Feedback Received",
                Message = "New feedback has been submitted",
                Type = "feedback_notification",
                Data = new Dictionary<string, object> { { "feedback", feedback } },
                CreatedAt = DateTime.UtcNow
            };

            await _hubContext.Clients.Group("Admins").SystemNotification(notification);
            _logger.LogInformation("Sent feedback notification");
        }
    }
}
