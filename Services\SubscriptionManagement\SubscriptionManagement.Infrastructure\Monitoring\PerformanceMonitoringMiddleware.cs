using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using System.Diagnostics;

namespace SubscriptionManagement.Infrastructure.Monitoring
{
    public class PerformanceMonitoringMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PerformanceMonitoringMiddleware> _logger;
        private readonly IMetricsService _metricsService;

        public PerformanceMonitoringMiddleware(
            RequestDelegate next,
            ILogger<PerformanceMonitoringMiddleware> logger,
            IMetricsService metricsService)
        {
            _next = next;
            _logger = logger;
            _metricsService = metricsService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();
            var path = context.Request.Path.Value ?? "";
            var method = context.Request.Method;

            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                var durationMs = stopwatch.Elapsed.TotalMilliseconds;
                var statusCode = context.Response.StatusCode;

                // Record API request metrics
                _metricsService.RecordApiRequest(path, method, statusCode, durationMs);

                // Log slow requests
                if (durationMs > 2000) // Log requests taking more than 2 seconds
                {
                    _logger.LogWarning("Slow request detected: {Method} {Path} took {Duration}ms with status {StatusCode}",
                        method, path, durationMs, statusCode);
                }

                // Log error responses
                if (statusCode >= 400)
                {
                    _logger.LogWarning("Error response: {Method} {Path} returned {StatusCode} in {Duration}ms",
                        method, path, statusCode, durationMs);
                }

                // Log successful requests at debug level
                _logger.LogDebug("Request completed: {Method} {Path} returned {StatusCode} in {Duration}ms",
                    method, path, statusCode, durationMs);
            }
        }
    }
}
