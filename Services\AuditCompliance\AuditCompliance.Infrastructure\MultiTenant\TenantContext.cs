using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Infrastructure.MultiTenant;

/// <summary>
/// Implementation of tenant context for multi-tenant support
/// </summary>
public class TenantContext : ITenantContext
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<TenantContext> _logger;
    private readonly ITenantService _tenantService;

    private Guid? _tenantId;
    private string? _tenantCode;
    private string? _tenantName;
    private TenantDto? _cachedTenant;

    public Guid? TenantId => _tenantId;
    public string? TenantCode => _tenantCode;
    public string? TenantName => _tenantName;
    public bool HasTenant => _tenantId.HasValue;

    public TenantContext(
        IConfiguration configuration,
        ILogger<TenantContext> logger,
        ITenantService tenantService)
    {
        _configuration = configuration;
        _logger = logger;
        _tenantService = tenantService;
    }

    public void SetTenant(Guid tenantId, string tenantCode, string tenantName)
    {
        _tenantId = tenantId;
        _tenantCode = tenantCode;
        _tenantName = tenantName;
        _cachedTenant = null; // Clear cache when tenant changes

        _logger.LogDebug("Tenant context set to {TenantId} ({TenantCode})", tenantId, tenantCode);
    }

    public void ClearTenant()
    {
        _tenantId = null;
        _tenantCode = null;
        _tenantName = null;
        _cachedTenant = null;

        _logger.LogDebug("Tenant context cleared");
    }

    public string? GetTenantConnectionString()
    {
        if (!HasTenant)
            return _configuration.GetConnectionString("DefaultConnection");

        // Check for tenant-specific connection string
        var tenantConnectionString = _configuration.GetConnectionString($"Tenant_{_tenantCode}");
        if (!string.IsNullOrEmpty(tenantConnectionString))
            return tenantConnectionString;

        // Fall back to default connection string with tenant isolation
        var defaultConnectionString = _configuration.GetConnectionString("DefaultConnection");
        return defaultConnectionString;
    }

    public T? GetTenantConfig<T>(string key, T? defaultValue = default)
    {
        if (!HasTenant)
            return defaultValue;

        try
        {
            // Try tenant-specific configuration first
            var tenantConfigKey = $"Tenants:{_tenantCode}:{key}";
            var tenantValue = _configuration.GetValue<T>(tenantConfigKey);
            if (tenantValue != null)
                return tenantValue;

            // Fall back to default configuration
            return _configuration.GetValue<T>(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting tenant config for key {Key}", key);
            return defaultValue;
        }
    }

    public bool HasFeatureAccess(string feature)
    {
        if (!HasTenant)
            return false;

        try
        {
            var tenant = GetCachedTenantAsync().Result;
            return tenant?.Configuration.EnabledFeatures.Contains(feature) ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking feature access for {Feature}", feature);
            return false;
        }
    }

    public TenantLimitsDto GetTenantLimits()
    {
        if (!HasTenant)
            return new TenantLimitsDto();

        try
        {
            var tenant = GetCachedTenantAsync().Result;
            return tenant?.Limits ?? new TenantLimitsDto();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting tenant limits");
            return new TenantLimitsDto();
        }
    }

    private async Task<TenantDto?> GetCachedTenantAsync()
    {
        if (_cachedTenant != null)
            return _cachedTenant;

        if (!HasTenant)
            return null;

        try
        {
            _cachedTenant = await _tenantService.GetTenantAsync(_tenantId!.Value);
            return _cachedTenant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tenant data for {TenantId}", _tenantId);
            return null;
        }
    }
}

/// <summary>
/// Middleware for tenant resolution
/// </summary>
public class TenantResolutionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantResolutionMiddleware> _logger;

    public TenantResolutionMiddleware(RequestDelegate next, ILogger<TenantResolutionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext, ITenantService tenantService)
    {
        try
        {
            var tenantIdentifier = ResolveTenantIdentifier(context);
            
            if (!string.IsNullOrEmpty(tenantIdentifier))
            {
                var tenant = await tenantService.GetTenantByCodeAsync(tenantIdentifier);
                
                if (tenant != null && tenant.Status == "Active")
                {
                    tenantContext.SetTenant(tenant.Id, tenant.Code, tenant.Name);
                    _logger.LogDebug("Resolved tenant: {TenantCode} for request {Path}", 
                        tenant.Code, context.Request.Path);
                }
                else
                {
                    _logger.LogWarning("Invalid or inactive tenant: {TenantIdentifier}", tenantIdentifier);
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Invalid or inactive tenant");
                    return;
                }
            }
            else
            {
                // For system-level operations or default tenant
                _logger.LogDebug("No tenant identifier found for request {Path}", context.Request.Path);
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant resolution middleware");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Internal server error");
        }
        finally
        {
            // Clear tenant context after request
            tenantContext.ClearTenant();
        }
    }

    private string? ResolveTenantIdentifier(HttpContext context)
    {
        // Strategy 1: Check custom header
        if (context.Request.Headers.TryGetValue("X-Tenant-Code", out var headerValue))
        {
            return headerValue.FirstOrDefault();
        }

        // Strategy 2: Check subdomain
        var host = context.Request.Host.Host;
        if (host.Contains('.'))
        {
            var subdomain = host.Split('.')[0];
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
            {
                return subdomain;
            }
        }

        // Strategy 3: Check query parameter
        if (context.Request.Query.TryGetValue("tenant", out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        // Strategy 4: Check JWT claims
        var user = context.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            var tenantClaim = user.FindFirst("tenant_code")?.Value;
            if (!string.IsNullOrEmpty(tenantClaim))
            {
                return tenantClaim;
            }
        }

        // Strategy 5: Check route values
        if (context.Request.RouteValues.TryGetValue("tenant", out var routeValue))
        {
            return routeValue?.ToString();
        }

        return null;
    }
}

/// <summary>
/// Tenant-aware database context factory
/// </summary>
public interface ITenantDbContextFactory
{
    /// <summary>
    /// Create database context for current tenant
    /// </summary>
    T CreateDbContext<T>() where T : class;

    /// <summary>
    /// Create database context for specific tenant
    /// </summary>
    T CreateDbContext<T>(Guid tenantId) where T : class;
}

/// <summary>
/// Implementation of tenant-aware database context factory
/// </summary>
public class TenantDbContextFactory : ITenantDbContextFactory
{
    private readonly ITenantContext _tenantContext;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TenantDbContextFactory> _logger;

    public TenantDbContextFactory(
        ITenantContext tenantContext,
        IServiceProvider serviceProvider,
        ILogger<TenantDbContextFactory> logger)
    {
        _tenantContext = tenantContext;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public T CreateDbContext<T>() where T : class
    {
        var context = _serviceProvider.GetRequiredService<T>();
        
        // Apply tenant-specific configuration if needed
        if (_tenantContext.HasTenant && context is DbContext dbContext)
        {
            ApplyTenantFilters(dbContext);
        }

        return context;
    }

    public T CreateDbContext<T>(Guid tenantId) where T : class
    {
        var context = _serviceProvider.GetRequiredService<T>();
        
        // Apply tenant-specific configuration for specific tenant
        if (context is DbContext dbContext)
        {
            ApplyTenantFilters(dbContext, tenantId);
        }

        return context;
    }

    private void ApplyTenantFilters(DbContext context, Guid? specificTenantId = null)
    {
        var tenantId = specificTenantId ?? _tenantContext.TenantId;
        
        if (tenantId.HasValue)
        {
            // Apply global query filters for tenant isolation
            // This would be implemented based on your specific entity structure
            _logger.LogDebug("Applied tenant filters for tenant {TenantId}", tenantId);
        }
    }
}

/// <summary>
/// Tenant validation attribute for controllers
/// </summary>
public class RequireTenantAttribute : Attribute, IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var tenantContext = context.HttpContext.RequestServices.GetRequiredService<ITenantContext>();
        
        if (!tenantContext.HasTenant)
        {
            context.Result = new BadRequestObjectResult(new
            {
                Error = "TenantRequired",
                Message = "This operation requires a valid tenant context"
            });
        }
    }
}

/// <summary>
/// Feature access attribute for controllers
/// </summary>
public class RequireFeatureAttribute : Attribute, IAuthorizationFilter
{
    private readonly string _feature;

    public RequireFeatureAttribute(string feature)
    {
        _feature = feature;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var tenantContext = context.HttpContext.RequestServices.GetRequiredService<ITenantContext>();
        
        if (!tenantContext.HasFeatureAccess(_feature))
        {
            context.Result = new ForbidResult($"Feature '{_feature}' is not available for this tenant");
        }
    }
}
