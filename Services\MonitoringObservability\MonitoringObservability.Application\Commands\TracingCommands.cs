using MediatR;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Commands;

// Distributed Tracing Commands
public class StartTraceCommand : IRequest<Unit>
{
    public string TraceId { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public Dictionary<string, string>? Tags { get; set; }
}

public class EndTraceCommand : IRequest<Unit>
{
    public string TraceId { get; set; } = string.Empty;
    public TraceStatus Status { get; set; }
    public TimeSpan Duration { get; set; }
    public string? ErrorMessage { get; set; }
}

public class RecordSpanCommand : IRequest<Unit>
{
    public string TraceId { get; set; } = string.Empty;
    public string SpanId { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class StartSpanCommand : IRequest<SpanDto>
{
    public string TraceId { get; set; } = string.Empty;
    public string SpanId { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string? ParentSpanId { get; set; }
    public Dictionary<string, string>? Tags { get; set; }
}

public class EndSpanCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorType { get; set; }
}

public class AddSpanEventCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public string EventName { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object>? Metadata { get; set; }
}

public class AddSpanLogCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public string Level { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object>? Fields { get; set; }
}

public class SetSpanHttpInfoCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public int? StatusCode { get; set; }
    public long? RequestSize { get; set; }
    public long? ResponseSize { get; set; }
}

public class SetSpanDatabaseInfoCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public string DbType { get; set; } = string.Empty;
    public string? Statement { get; set; }
    public string? Instance { get; set; }
    public string? User { get; set; }
}

public class SetSpanMessageQueueInfoCommand : IRequest<Unit>
{
    public string SpanId { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public string? Operation { get; set; }
    public string? MessageId { get; set; }
}

// Service Dependency Commands
public class UpdateServiceDependencyCommand : IRequest<ServiceDependencyDto>
{
    public string FromService { get; set; } = string.Empty;
    public string ToService { get; set; } = string.Empty;
    public string OperationType { get; set; } = string.Empty;
    public TimeSpan Latency { get; set; }
    public bool HasError { get; set; }
}

public class RefreshServiceDependencyGraphCommand : IRequest<ServiceDependencyGraphDto>
{
    public TimeSpan? Period { get; set; }
    public bool IncludeHealthStatus { get; set; } = true;
}

// Trace Analysis Commands
public class AnalyzeTracePerformanceCommand : IRequest<TracePerformanceAnalysisDto>
{
    public string TraceId { get; set; } = string.Empty;
    public bool IncludeRecommendations { get; set; } = true;
}

public class DetectTraceAnomaliesCommand : IRequest<List<TraceAnomalyDto>>
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromHours(24);
    public double AnomalyThreshold { get; set; } = 2.0; // Z-score threshold
}

public class GenerateTraceReportCommand : IRequest<TraceReportDto>
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; } = TimeSpan.FromDays(7);
    public bool IncludeSlowTraces { get; set; } = true;
    public bool IncludeErrorTraces { get; set; } = true;
    public bool IncludeDependencyAnalysis { get; set; } = true;
    public ReportFormat Format { get; set; } = ReportFormat.JSON;
}

// Cleanup Commands
public class CleanupOldTracesCommand : IRequest<TraceCleanupResultDto>
{
    public TimeSpan RetentionPeriod { get; set; } = TimeSpan.FromDays(30);
    public bool DryRun { get; set; } = true;
    public List<string>? ServiceNames { get; set; }
}

// Supporting DTOs
public class TracePerformanceAnalysisDto
{
    public string TraceId { get; set; } = string.Empty;
    public TimeSpan TotalDuration { get; set; }
    public int TotalSpans { get; set; }
    public int ServicesInvolved { get; set; }
    public List<SpanPerformanceDto> SlowSpans { get; set; } = new();
    public List<SpanPerformanceDto> ErrorSpans { get; set; } = new();
    public List<string> PerformanceRecommendations { get; set; } = new();
    public Dictionary<string, TimeSpan> ServiceBreakdown { get; set; } = new();
    public DateTime AnalyzedAt { get; set; }
}

public class SpanPerformanceDto
{
    public string SpanId { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
    public double PercentageOfTrace { get; set; }
}

public class TraceAnomalyDto
{
    public string TraceId { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public AnomalyType AnomalyType { get; set; }
    public double AnomalyScore { get; set; }
    public string Description { get; set; } = string.Empty;
    public TimeSpan ActualDuration { get; set; }
    public TimeSpan ExpectedDuration { get; set; }
    public DateTime DetectedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TraceReportDto
{
    public string? ServiceName { get; set; }
    public TimeSpan Period { get; set; }
    public DateTime GeneratedAt { get; set; }
    public TraceSummaryDto Summary { get; set; } = new();
    public List<TraceDto> SlowTraces { get; set; } = new();
    public List<TraceDto> ErrorTraces { get; set; } = new();
    public ServiceDependencyGraphDto? DependencyGraph { get; set; }
    public List<TracePerformanceAnalysisDto> PerformanceAnalyses { get; set; } = new();
    public ReportFormat Format { get; set; }
}

public class TraceSummaryDto
{
    public int TotalTraces { get; set; }
    public int SuccessfulTraces { get; set; }
    public int ErrorTraces { get; set; }
    public double ErrorRate { get; set; }
    public TimeSpan AverageDuration { get; set; }
    public TimeSpan P95Duration { get; set; }
    public TimeSpan P99Duration { get; set; }
    public Dictionary<string, int> TracesByService { get; set; } = new();
    public Dictionary<string, double> AverageLatencyByService { get; set; } = new();
}

public class TraceCleanupResultDto
{
    public int TracesDeleted { get; set; }
    public int SpansDeleted { get; set; }
    public int EventsDeleted { get; set; }
    public int LogsDeleted { get; set; }
    public TimeSpan RetentionPeriod { get; set; }
    public bool WasDryRun { get; set; }
    public DateTime ExecutedAt { get; set; }
    public List<string> ServicesAffected { get; set; } = new();
}

public enum AnomalyType
{
    SlowTrace = 0,
    FastTrace = 1,
    HighSpanCount = 2,
    LowSpanCount = 3,
    UnusualServicePattern = 4,
    ErrorSpike = 5
}

public enum ReportFormat
{
    JSON = 0,
    PDF = 1,
    CSV = 2,
    HTML = 3
}
