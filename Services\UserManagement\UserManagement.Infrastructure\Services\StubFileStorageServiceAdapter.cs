using Microsoft.AspNetCore.Http;
using UserManagement.Application.Interfaces;
using UserManagement.Application.Services;

namespace UserManagement.Infrastructure.Services
{
    public class StubFileStorageServiceAdapter : UserManagement.Application.Interfaces.IFileStorageService
    {
        private readonly UserManagement.Application.Services.IFileStorageService _fileStorageService;

        public StubFileStorageServiceAdapter(UserManagement.Application.Services.IFileStorageService fileStorageService)
        {
            _fileStorageService = fileStorageService;
        }

        public async Task<string> UploadFileAsync(IFormFile file, string containerName)
        {
            // Convert IFormFile to byte array and use the existing service
            using var stream = file.OpenReadStream();
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            var fileBytes = memoryStream.ToArray();

            return await _fileStorageService.SaveExportFileAsync(file.FileName, fileBytes, file.ContentType);
        }

        public Task<byte[]> DownloadFileAsync(string fileName, string containerName)
        {
            // Stub implementation - return empty byte array
            return Task.FromResult(new byte[0]);
        }

        public Task DeleteFileAsync(string fileName, string containerName)
        {
            _fileStorageService.DeleteFileAsync(fileName);
            return Task.CompletedTask;
        }

        public Task<bool> FileExistsAsync(string fileName, string containerName)
        {
            return _fileStorageService.FileExistsAsync(fileName);
        }
    }
}
