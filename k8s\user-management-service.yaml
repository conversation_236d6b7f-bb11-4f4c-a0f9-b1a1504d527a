apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-service
  namespace: tli-microservices
  labels:
    app: user-management-service
    version: v1
    component: microservice
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: user-management-service
  template:
    metadata:
      labels:
        app: user-management-service
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: tli-service-account
      containers:
      - name: user-management
        image: tli/user-management-service:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        - name: metrics
          containerPort: 80
          protocol: TCP
        env:
        # Configuration from ConfigMap
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: ASPNETCORE_URLS
        - name: Logging__LogLevel__Default
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: Logging__LogLevel__Default
        - name: HealthChecks__Enabled
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: HealthChecks__Enabled
        - name: Monitoring__Prometheus__Enabled
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: Monitoring__Prometheus__Enabled
        - name: Monitoring__Jaeger__Enabled
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: Monitoring__Jaeger__Enabled
        - name: Monitoring__Jaeger__AgentHost
          valueFrom:
            configMapKeyRef:
              name: tli-config
              key: Monitoring__Jaeger__AgentHost
        
        # Database Configuration
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: tli-database-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: tli-database-config
              key: DB_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: tli-database-config
              key: REDIS_HOST
        - name: RABBITMQ_HOST
          valueFrom:
            configMapKeyRef:
              name: tli-database-config
              key: RABBITMQ_HOST
        
        # Secrets
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: db-connection-string
        - name: Authentication__Jwt__SecretKey
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: jwt-secret-key
        - name: Authentication__Jwt__Issuer
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: jwt-issuer
        - name: Authentication__Jwt__Audience
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: jwt-audience
        - name: Caching__Redis__ConnectionString
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: redis-connection-string
        - name: MessageBroker__RabbitMQ__ConnectionString
          valueFrom:
            secretKeyRef:
              name: tli-secrets
              key: rabbitmq-connection-string
        
        # Resource limits and requests
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
          successThreshold: 1
        
        # Startup probe for slow starting containers
        startupProbe:
          httpGet:
            path: /health/live
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
          successThreshold: 1
        
        # Security context
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        
        # Volume mounts
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
      
      # Volumes
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app-logs
        emptyDir: {}
      
      # Pod security context
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      
      # Node selection and affinity
      nodeSelector:
        kubernetes.io/os: linux
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - user-management-service
              topologyKey: kubernetes.io/hostname
      
      # Tolerations for node taints
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  namespace: tli-microservices
  labels:
    app: user-management-service
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "80"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: user-management-service

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: user-management-service-pdb
  namespace: tli-microservices
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: user-management-service

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-management-service-hpa
  namespace: tli-microservices
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-management-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
