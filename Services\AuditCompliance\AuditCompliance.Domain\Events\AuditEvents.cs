using Shared.Domain.Common;
using AuditCompliance.Domain.Entities;

namespace AuditCompliance.Domain.Events;

/// <summary>
/// Domain event raised when an audit log entry is created
/// </summary>
public class AuditLogCreatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public AuditLog AuditLog { get; }

    public AuditLogCreatedEvent(AuditLog auditLog)
    {
        AuditLog = auditLog ?? throw new ArgumentNullException(nameof(auditLog));
    }
}

/// <summary>
/// Domain event raised when a compliance report is created
/// </summary>
public class ComplianceReportCreatedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ComplianceReport ComplianceReport { get; }

    public ComplianceReportCreatedEvent(ComplianceReport complianceReport)
    {
        ComplianceReport = complianceReport ?? throw new ArgumentNullException(nameof(complianceReport));
    }
}

/// <summary>
/// Domain event raised when a compliance report is completed
/// </summary>
public class ComplianceReportCompletedEvent : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public ComplianceReport ComplianceReport { get; }

    public ComplianceReportCompletedEvent(ComplianceReport complianceReport)
    {
        ComplianceReport = complianceReport ?? throw new ArgumentNullException(nameof(complianceReport));
    }
}
