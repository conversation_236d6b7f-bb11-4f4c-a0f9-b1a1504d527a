using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Commands.CreateTrip;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.EventHandlers;

// Integration event from Order Management Service
public record OrderConfirmedIntegrationEvent : INotification
{
    public Guid OrderId { get; init; }
    public Guid CarrierId { get; init; }
    public string OrderNumber { get; init; } = string.Empty;
    public Guid TransportCompanyId { get; init; }
    public Guid BrokerId { get; init; }
    public OrderRouteDetails RouteDetails { get; init; } = null!;
    public OrderLoadDetails LoadDetails { get; init; } = null!;
    public DateTime EstimatedPickupDate { get; init; }
    public DateTime EstimatedDeliveryDate { get; init; }
    public bool IsUrgent { get; init; }
    public string? SpecialInstructions { get; init; }
}

public record OrderRouteDetails
{
    public OrderAddress PickupAddress { get; init; } = null!;
    public OrderAddress DeliveryAddress { get; init; } = null!;
    public DateTime PreferredPickupDate { get; init; }
    public DateTime PreferredDeliveryDate { get; init; }
    public double? EstimatedDistanceKm { get; init; }
    public TimeSpan? EstimatedDuration { get; init; }
}

public record OrderAddress
{
    public string Street { get; init; } = string.Empty;
    public string City { get; init; } = string.Empty;
    public string State { get; init; } = string.Empty;
    public string Country { get; init; } = string.Empty;
    public string PostalCode { get; init; } = string.Empty;
    public double? Latitude { get; init; }
    public double? Longitude { get; init; }
}

public record OrderLoadDetails
{
    public string Description { get; init; } = string.Empty;
    public decimal WeightKg { get; init; }
    public decimal? VolumeM3 { get; init; }
    public int Quantity { get; init; }
    public string? PackagingType { get; init; }
    public bool RequiresSpecialHandling { get; init; }
    public string? SpecialHandlingInstructions { get; init; }
}

public class OrderConfirmedEventHandler : INotificationHandler<OrderConfirmedIntegrationEvent>
{
    private readonly IMediator _mediator;
    private readonly ILogger<OrderConfirmedEventHandler> _logger;

    public OrderConfirmedEventHandler(
        IMediator mediator,
        ILogger<OrderConfirmedEventHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Handle(OrderConfirmedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing order confirmed event for order {OrderId}", notification.OrderId);

        try
        {
            // Create pickup location
            var pickupLocation = new LocationDto
            {
                Latitude = notification.RouteDetails.PickupAddress.Latitude ?? 0,
                Longitude = notification.RouteDetails.PickupAddress.Longitude ?? 0,
                Address = notification.RouteDetails.PickupAddress.Street,
                City = notification.RouteDetails.PickupAddress.City,
                State = notification.RouteDetails.PickupAddress.State,
                Country = notification.RouteDetails.PickupAddress.Country,
                PostalCode = notification.RouteDetails.PickupAddress.PostalCode,
                Timestamp = DateTime.UtcNow
            };

            // Create delivery location
            var deliveryLocation = new LocationDto
            {
                Latitude = notification.RouteDetails.DeliveryAddress.Latitude ?? 0,
                Longitude = notification.RouteDetails.DeliveryAddress.Longitude ?? 0,
                Address = notification.RouteDetails.DeliveryAddress.Street,
                City = notification.RouteDetails.DeliveryAddress.City,
                State = notification.RouteDetails.DeliveryAddress.State,
                Country = notification.RouteDetails.DeliveryAddress.Country,
                PostalCode = notification.RouteDetails.DeliveryAddress.PostalCode,
                Timestamp = DateTime.UtcNow
            };

            // Create route
            var route = new RouteDto
            {
                StartLocation = pickupLocation,
                EndLocation = deliveryLocation,
                Waypoints = new List<LocationDto>(),
                EstimatedDistanceKm = notification.RouteDetails.EstimatedDistanceKm,
                EstimatedDuration = notification.RouteDetails.EstimatedDuration
            };

            // Create trip stops
            var stops = new List<CreateTripStopDto>
            {
                new()
                {
                    StopType = TripStopType.Pickup,
                    SequenceNumber = 1,
                    Location = pickupLocation,
                    ScheduledArrival = notification.RouteDetails.PreferredPickupDate,
                    Instructions = notification.LoadDetails.RequiresSpecialHandling
                        ? notification.LoadDetails.SpecialHandlingInstructions
                        : null
                },
                new()
                {
                    StopType = TripStopType.Delivery,
                    SequenceNumber = 2,
                    Location = deliveryLocation,
                    ScheduledArrival = notification.RouteDetails.PreferredDeliveryDate,
                    Instructions = "Delivery location"
                }
            };

            // Create trip command
            var createTripCommand = new CreateTripCommand
            {
                OrderId = notification.OrderId,
                CarrierId = notification.CarrierId,
                Route = route,
                EstimatedStartTime = notification.EstimatedPickupDate,
                EstimatedEndTime = notification.EstimatedDeliveryDate,
                SpecialInstructions = notification.SpecialInstructions,
                IsUrgent = notification.IsUrgent,
                EstimatedDistanceKm = (decimal?)notification.RouteDetails.EstimatedDistanceKm,
                Stops = stops
            };

            // Create the trip
            var tripId = await _mediator.Send(createTripCommand, cancellationToken);

            _logger.LogInformation("Successfully created trip {TripId} for order {OrderId}",
                tripId, notification.OrderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order confirmed event for order {OrderId}",
                notification.OrderId);
            throw;
        }
    }
}
