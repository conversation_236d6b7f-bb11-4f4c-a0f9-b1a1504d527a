using Shared.Domain.Common;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing broker comments for internal routing and communication
/// </summary>
public class BrokerComment : BaseEntity
{
    public Guid RfqId { get; private set; }
    public Guid BrokerId { get; private set; }
    public string BrokerName { get; private set; } = string.Empty;
    public string Comment { get; private set; } = string.Empty;
    public BrokerCommentType CommentType { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool IsInternal { get; private set; }
    public bool IsVisible { get; private set; }
    public Guid? ParentCommentId { get; private set; }
    public string? Tags { get; private set; }
    public int Priority { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }

    // Navigation properties
    public RFQ RFQ { get; private set; } = null!;
    public BrokerComment? ParentComment { get; private set; }

    private readonly List<BrokerComment> _replies = new();
    public IReadOnlyCollection<BrokerComment> Replies => _replies.AsReadOnly();

    private BrokerComment() { } // EF Constructor

    public BrokerComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment,
        BrokerCommentType commentType,
        bool isInternal = true,
        bool isVisible = true,
        Guid? parentCommentId = null,
        string? tags = null,
        int priority = 0,
        DateTime? expiresAt = null,
        Dictionary<string, object>? metadata = null)
    {
        RfqId = rfqId;
        BrokerId = brokerId;
        BrokerName = brokerName ?? throw new ArgumentNullException(nameof(brokerName));
        Comment = comment ?? throw new ArgumentNullException(nameof(comment));
        CommentType = commentType;
        CreatedAt = DateTime.UtcNow;
        IsInternal = isInternal;
        IsVisible = isVisible;
        ParentCommentId = parentCommentId;
        Tags = tags;
        Priority = priority;
        ExpiresAt = expiresAt;
        Metadata = metadata;
    }

    public void UpdateComment(string newComment)
    {
        Comment = newComment ?? throw new ArgumentNullException(nameof(newComment));
    }

    public void Hide()
    {
        IsVisible = false;
    }

    public void Show()
    {
        IsVisible = true;
    }

    public void UpdatePriority(int newPriority)
    {
        Priority = newPriority;
    }

    public void AddReply(BrokerComment reply)
    {
        if (reply.ParentCommentId != Id)
            throw new ArgumentException("Reply must have this comment as parent");

        _replies.Add(reply);
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    public bool IsReply()
    {
        return ParentCommentId.HasValue;
    }

    public bool HasReplies()
    {
        return _replies.Any();
    }

    // Factory methods for common comment types
    public static BrokerComment CreateRoutingComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment,
        string? tags = null)
    {
        return new BrokerComment(
            rfqId,
            brokerId,
            brokerName,
            comment,
            BrokerCommentType.Routing,
            true,
            true,
            null,
            tags,
            1);
    }

    public static BrokerComment CreatePricingComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment,
        int priority = 0)
    {
        return new BrokerComment(
            rfqId,
            brokerId,
            brokerName,
            comment,
            BrokerCommentType.Pricing,
            true,
            true,
            null,
            "pricing",
            priority);
    }

    public static BrokerComment CreateCapacityComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment,
        DateTime? expiresAt = null)
    {
        return new BrokerComment(
            rfqId,
            brokerId,
            brokerName,
            comment,
            BrokerCommentType.Capacity,
            true,
            true,
            null,
            "capacity",
            0,
            expiresAt);
    }

    public static BrokerComment CreateRequirementsComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment)
    {
        return new BrokerComment(
            rfqId,
            brokerId,
            brokerName,
            comment,
            BrokerCommentType.Requirements,
            true,
            true,
            null,
            "requirements",
            2);
    }

    public static BrokerComment CreateGeneralComment(
        Guid rfqId,
        Guid brokerId,
        string brokerName,
        string comment,
        bool isInternal = true)
    {
        return new BrokerComment(
            rfqId,
            brokerId,
            brokerName,
            comment,
            BrokerCommentType.General,
            isInternal,
            true);
    }
}
