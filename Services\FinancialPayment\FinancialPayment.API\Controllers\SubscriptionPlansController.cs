using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SubscriptionPlansController : ControllerBase
{
    private readonly ISubscriptionBillingService _subscriptionService;
    private readonly ILogger<SubscriptionPlansController> _logger;

    public SubscriptionPlansController(
        ISubscriptionBillingService subscriptionService,
        ILogger<SubscriptionPlansController> logger)
    {
        _subscriptionService = subscriptionService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new subscription plan
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<SubscriptionPlan>> CreatePlan([FromBody] CreatePlanRequest request)
    {
        try
        {
            var plan = await _subscriptionService.CreatePlanAsync(request);
            
            _logger.LogInformation("Subscription plan created: {PlanId} - {PlanName}",
                plan.Id, request.PlanName);

            return CreatedAtAction(nameof(GetPlan), new { id = plan.Id }, plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription plan {PlanName}", request.PlanName);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get subscription plan by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<SubscriptionPlan>> GetPlan(Guid id)
    {
        try
        {
            var plan = await _subscriptionService.GetPlanAsync(id);
            if (plan == null)
            {
                return NotFound($"Subscription plan {id} not found");
            }

            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plan {PlanId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all active subscription plans
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<SubscriptionPlan>>> GetActivePlans()
    {
        try
        {
            var plans = await _subscriptionService.GetActivePlansAsync();
            return Ok(plans);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active subscription plans");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update subscription plan
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<SubscriptionPlan>> UpdatePlan(
        Guid id, 
        [FromBody] UpdatePlanRequest request)
    {
        try
        {
            var plan = await _subscriptionService.GetPlanAsync(id);
            if (plan == null)
            {
                return NotFound($"Subscription plan {id} not found");
            }

            // Update plan properties
            if (request.Price.HasValue)
            {
                plan.UpdatePrice(new Domain.ValueObjects.Money(request.Price.Value, request.Currency ?? "INR"));
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    plan.Activate();
                else
                    plan.Deactivate();
            }

            _logger.LogInformation("Subscription plan updated: {PlanId}", id);
            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription plan {PlanId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Activate subscription plan
    /// </summary>
    [HttpPost("{id}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<SubscriptionPlan>> ActivatePlan(Guid id)
    {
        try
        {
            var plan = await _subscriptionService.GetPlanAsync(id);
            if (plan == null)
            {
                return NotFound($"Subscription plan {id} not found");
            }

            plan.Activate();
            
            _logger.LogInformation("Subscription plan activated: {PlanId}", id);
            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating subscription plan {PlanId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deactivate subscription plan
    /// </summary>
    [HttpPost("{id}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<SubscriptionPlan>> DeactivatePlan(Guid id)
    {
        try
        {
            var plan = await _subscriptionService.GetPlanAsync(id);
            if (plan == null)
            {
                return NotFound($"Subscription plan {id} not found");
            }

            plan.Deactivate();
            
            _logger.LogInformation("Subscription plan deactivated: {PlanId}", id);
            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating subscription plan {PlanId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get plan pricing for different billing intervals
    /// </summary>
    [HttpGet("{id}/pricing")]
    public async Task<ActionResult<PlanPricingInfo>> GetPlanPricing(Guid id)
    {
        try
        {
            var plan = await _subscriptionService.GetPlanAsync(id);
            if (plan == null)
            {
                return NotFound($"Subscription plan {id} not found");
            }

            var pricingInfo = new PlanPricingInfo
            {
                PlanId = plan.Id,
                PlanName = plan.PlanName,
                BasePrice = plan.Price,
                BillingInterval = plan.BillingInterval,
                BillingIntervalCount = plan.BillingIntervalCount,
                TrialPeriodDays = plan.TrialPeriodDays,
                Features = plan.Features,
                Tiers = plan.Tiers.Select(t => new PlanTierInfo
                {
                    TierName = t.TierName,
                    TierPrice = t.TierPrice,
                    UserLimit = t.UserLimit,
                    Features = t.Features
                }).ToList()
            };

            return Ok(pricingInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plan pricing for {PlanId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Compare subscription plans
    /// </summary>
    [HttpPost("compare")]
    public async Task<ActionResult<List<PlanComparisonInfo>>> ComparePlans([FromBody] ComparePlansRequest request)
    {
        try
        {
            var comparisonInfo = new List<PlanComparisonInfo>();

            foreach (var planId in request.PlanIds)
            {
                var plan = await _subscriptionService.GetPlanAsync(planId);
                if (plan != null)
                {
                    comparisonInfo.Add(new PlanComparisonInfo
                    {
                        PlanId = plan.Id,
                        PlanName = plan.PlanName,
                        Description = plan.Description,
                        Price = plan.Price,
                        BillingInterval = plan.BillingInterval,
                        BillingIntervalCount = plan.BillingIntervalCount,
                        TrialPeriodDays = plan.TrialPeriodDays,
                        Features = plan.Features,
                        IsActive = plan.IsActive,
                        TierCount = plan.Tiers.Count
                    });
                }
            }

            return Ok(comparisonInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing subscription plans");
            return StatusCode(500, "Internal server error");
        }
    }
}

// Request/Response DTOs
public class UpdatePlanRequest
{
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public bool? IsActive { get; set; }
    public Dictionary<string, object>? Features { get; set; }
}

public class ComparePlansRequest
{
    public List<Guid> PlanIds { get; set; } = new();
}

public class PlanPricingInfo
{
    public Guid PlanId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public Domain.ValueObjects.Money BasePrice { get; set; } = Domain.ValueObjects.Money.Zero("INR");
    public BillingInterval BillingInterval { get; set; }
    public int BillingIntervalCount { get; set; }
    public int? TrialPeriodDays { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
    public List<PlanTierInfo> Tiers { get; set; } = new();
}

public class PlanTierInfo
{
    public string TierName { get; set; } = string.Empty;
    public Domain.ValueObjects.Money TierPrice { get; set; } = Domain.ValueObjects.Money.Zero("INR");
    public int? UserLimit { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
}

public class PlanComparisonInfo
{
    public Guid PlanId { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Domain.ValueObjects.Money Price { get; set; } = Domain.ValueObjects.Money.Zero("INR");
    public BillingInterval BillingInterval { get; set; }
    public int BillingIntervalCount { get; set; }
    public int? TrialPeriodDays { get; set; }
    public Dictionary<string, object> Features { get; set; } = new();
    public bool IsActive { get; set; }
    public int TierCount { get; set; }
}
