using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.Interfaces;
using System.Collections.Concurrent;

namespace MonitoringObservability.Infrastructure.Services;

/// <summary>
/// Service for managing custom dashboards and widgets
/// </summary>
public class CustomDashboardService : ICustomDashboardService
{
    private readonly ICustomDashboardRepository _dashboardRepository;
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly ILogger<CustomDashboardService> _logger;
    
    // Cache for dashboard data
    private readonly ConcurrentDictionary<Guid, object> _widgetDataCache = new();
    private readonly ConcurrentDictionary<Guid, DateTime> _cacheTimestamps = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1);

    public CustomDashboardService(
        ICustomDashboardRepository dashboardRepository,
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        ILogger<CustomDashboardService> logger)
    {
        _dashboardRepository = dashboardRepository ?? throw new ArgumentNullException(nameof(dashboardRepository));
        _metricRepository = metricRepository ?? throw new ArgumentNullException(nameof(metricRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<CustomDashboard> CreateDashboardAsync(string name, string description, Guid ownerId,
        DashboardVisibility visibility = DashboardVisibility.Private, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating dashboard {DashboardName} for user {OwnerId}", name, ownerId);

            var dashboard = new CustomDashboard(name, description, ownerId, visibility);
            
            await _dashboardRepository.AddAsync(dashboard, cancellationToken);
            
            _logger.LogInformation("Created dashboard {DashboardId}", dashboard.Id);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create dashboard {DashboardName}", name);
            throw;
        }
    }

    public async Task<CustomDashboard?> GetDashboardAsync(Guid dashboardId, Guid? userId = null, 
        string[]? userRoles = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            
            if (dashboard == null)
                return null;

            // Check access permissions
            if (userId.HasValue && userRoles != null && !dashboard.CanUserAccess(userId.Value, userRoles))
            {
                _logger.LogWarning("User {UserId} denied access to dashboard {DashboardId}", userId, dashboardId);
                return null;
            }

            // Record view
            if (userId.HasValue)
            {
                dashboard.RecordView(userId.Value);
                await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            }

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    public async Task<IEnumerable<CustomDashboard>> GetUserDashboardsAsync(Guid userId, string[] userRoles,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboards = await _dashboardRepository.GetAccessibleDashboardsAsync(userId, userRoles, cancellationToken);
            return dashboards;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get dashboards for user {UserId}", userId);
            throw;
        }
    }

    public async Task<CustomDashboard> UpdateDashboardAsync(Guid dashboardId, string? name = null, 
        string? description = null, DashboardVisibility? visibility = null, TimeSpan? refreshInterval = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            dashboard.UpdateBasicInfo(name, description, visibility, refreshInterval);
            
            await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            
            _logger.LogInformation("Updated dashboard {DashboardId}", dashboardId);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    public async Task<DashboardWidget> AddWidgetAsync(Guid dashboardId, string title, WidgetType type,
        WidgetPosition position, WidgetSize size, Dictionary<string, object> configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            var widget = new DashboardWidget(title, type, position, size, configuration);
            dashboard.AddWidget(widget);
            
            await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            
            _logger.LogInformation("Added widget {WidgetId} to dashboard {DashboardId}", widget.Id, dashboardId);
            return widget;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add widget to dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    public async Task UpdateWidgetAsync(Guid dashboardId, Guid widgetId, string? title = null,
        WidgetPosition? position = null, WidgetSize? size = null, Dictionary<string, object>? configuration = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            var widget = dashboard.GetWidget(widgetId);
            if (widget == null)
                throw new InvalidOperationException($"Widget {widgetId} not found");

            if (!string.IsNullOrWhiteSpace(title))
                widget.Title = title;
            
            if (position != null)
                widget.UpdatePosition(position);
            
            if (size != null)
                widget.UpdateSize(size);
            
            if (configuration != null)
                widget.UpdateConfiguration(configuration);

            await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            
            // Clear widget cache
            _widgetDataCache.TryRemove(widgetId, out _);
            _cacheTimestamps.TryRemove(widgetId, out _);
            
            _logger.LogInformation("Updated widget {WidgetId} in dashboard {DashboardId}", widgetId, dashboardId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update widget {WidgetId} in dashboard {DashboardId}", widgetId, dashboardId);
            throw;
        }
    }

    public async Task RemoveWidgetAsync(Guid dashboardId, Guid widgetId, CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            dashboard.RemoveWidget(widgetId);
            
            await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            
            // Clear widget cache
            _widgetDataCache.TryRemove(widgetId, out _);
            _cacheTimestamps.TryRemove(widgetId, out _);
            
            _logger.LogInformation("Removed widget {WidgetId} from dashboard {DashboardId}", widgetId, dashboardId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove widget {WidgetId} from dashboard {DashboardId}", widgetId, dashboardId);
            throw;
        }
    }

    public async Task<object> GetWidgetDataAsync(Guid widgetId, Dictionary<string, object>? filters = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check cache first
            if (_widgetDataCache.TryGetValue(widgetId, out var cachedData) &&
                _cacheTimestamps.TryGetValue(widgetId, out var cacheTime) &&
                DateTime.UtcNow - cacheTime < _cacheExpiry)
            {
                return cachedData;
            }

            var widget = await _dashboardRepository.GetWidgetByIdAsync(widgetId, cancellationToken);
            if (widget == null)
                throw new InvalidOperationException($"Widget {widgetId} not found");

            var data = await GenerateWidgetDataAsync(widget, filters, cancellationToken);
            
            // Cache the data
            _widgetDataCache.TryAdd(widgetId, data);
            _cacheTimestamps.TryAdd(widgetId, DateTime.UtcNow);
            
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data for widget {WidgetId}", widgetId);
            throw;
        }
    }

    public async Task<DashboardExportDto> ExportDashboardAsync(Guid dashboardId, ExportFormat format,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            _logger.LogInformation("Exporting dashboard {DashboardId} in {Format} format", dashboardId, format);

            var export = new DashboardExportDto
            {
                DashboardId = dashboardId,
                Name = dashboard.Name,
                Description = dashboard.Description,
                Format = format,
                ExportedAt = DateTime.UtcNow,
                Data = await GenerateExportDataAsync(dashboard, format, cancellationToken)
            };

            return export;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    public async Task<CustomDashboard> ImportDashboardAsync(DashboardImportDto importData, Guid ownerId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Importing dashboard {DashboardName} for user {OwnerId}", 
                importData.Name, ownerId);

            var dashboard = new CustomDashboard(importData.Name, importData.Description, ownerId, importData.Visibility);
            
            // Import widgets
            foreach (var widgetData in importData.Widgets)
            {
                var widget = new DashboardWidget(
                    widgetData.Title,
                    widgetData.Type,
                    widgetData.Position,
                    widgetData.Size,
                    widgetData.Configuration);
                
                dashboard.AddWidget(widget);
            }

            // Import filters
            foreach (var filterData in importData.Filters)
            {
                var filter = new DashboardFilter(
                    filterData.Name,
                    filterData.Type,
                    filterData.Field,
                    filterData.Operator,
                    filterData.Value);
                
                dashboard.AddFilter(filter);
            }

            await _dashboardRepository.AddAsync(dashboard, cancellationToken);
            
            _logger.LogInformation("Imported dashboard {DashboardId}", dashboard.Id);
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import dashboard");
            throw;
        }
    }

    public async Task ShareDashboardAsync(Guid dashboardId, Guid? userId, string? roleName, 
        DashboardAccessLevel accessLevel, CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            var permission = new DashboardPermission(userId, roleName, accessLevel);
            dashboard.AddPermission(permission);
            
            await _dashboardRepository.UpdateAsync(dashboard, cancellationToken);
            
            _logger.LogInformation("Shared dashboard {DashboardId} with {Target} at {AccessLevel} level",
                dashboardId, userId?.ToString() ?? roleName, accessLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to share dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    public async Task<DashboardAnalyticsDto> GetDashboardAnalyticsAsync(Guid dashboardId, TimeSpan period,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var dashboard = await _dashboardRepository.GetByIdAsync(dashboardId, cancellationToken);
            if (dashboard == null)
                throw new InvalidOperationException($"Dashboard {dashboardId} not found");

            var analytics = new DashboardAnalyticsDto
            {
                DashboardId = dashboardId,
                DashboardName = dashboard.Name,
                Period = period,
                TotalViews = dashboard.ViewCount,
                WidgetCount = dashboard.WidgetCount,
                LastViewed = dashboard.LastViewed,
                CreatedAt = dashboard.CreatedAt,
                IsShared = dashboard.IsShared,
                GeneratedAt = DateTime.UtcNow
            };

            // Get additional analytics from repository
            var viewHistory = await _dashboardRepository.GetViewHistoryAsync(dashboardId, period, cancellationToken);
            analytics.ViewsByDay = viewHistory.GroupBy(v => v.Date)
                .ToDictionary(g => g.Key, g => g.Count());

            var popularWidgets = await _dashboardRepository.GetPopularWidgetsAsync(dashboardId, period, cancellationToken);
            analytics.PopularWidgets = popularWidgets.ToDictionary(w => w.widgetId.ToString(), w => w.viewCount);

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get analytics for dashboard {DashboardId}", dashboardId);
            throw;
        }
    }

    private async Task<object> GenerateWidgetDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        return widget.Type switch
        {
            WidgetType.LineChart => await GenerateLineChartDataAsync(widget, filters, cancellationToken),
            WidgetType.BarChart => await GenerateBarChartDataAsync(widget, filters, cancellationToken),
            WidgetType.PieChart => await GeneratePieChartDataAsync(widget, filters, cancellationToken),
            WidgetType.Gauge => await GenerateGaugeDataAsync(widget, filters, cancellationToken),
            WidgetType.Counter => await GenerateCounterDataAsync(widget, filters, cancellationToken),
            WidgetType.Table => await GenerateTableDataAsync(widget, filters, cancellationToken),
            WidgetType.Alert => await GenerateAlertDataAsync(widget, filters, cancellationToken),
            WidgetType.Metric => await GenerateMetricDataAsync(widget, filters, cancellationToken),
            WidgetType.Status => await GenerateStatusDataAsync(widget, filters, cancellationToken),
            _ => new { error = "Unsupported widget type" }
        };
    }

    private async Task<object> GenerateLineChartDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        // Extract configuration
        var serviceName = widget.Configuration.GetValueOrDefault("serviceName")?.ToString();
        var metricName = widget.Configuration.GetValueOrDefault("metricName")?.ToString();
        var timeRange = TimeSpan.FromHours(Convert.ToDouble(widget.Configuration.GetValueOrDefault("timeRangeHours", 24)));

        if (string.IsNullOrEmpty(serviceName) || string.IsNullOrEmpty(metricName))
            return new { error = "Service name and metric name are required" };

        var fromDate = DateTime.UtcNow.Subtract(timeRange);
        var metrics = await _metricRepository.GetMetricsByServiceAsync(serviceName, fromDate, cancellationToken);
        
        var data = metrics
            .Where(m => m.Name == metricName)
            .OrderBy(m => m.Timestamp)
            .Select(m => new { x = m.Timestamp, y = m.Value })
            .ToList();

        return new
        {
            type = "line",
            data = new
            {
                datasets = new[]
                {
                    new
                    {
                        label = metricName,
                        data = data,
                        borderColor = "rgb(75, 192, 192)",
                        backgroundColor = "rgba(75, 192, 192, 0.2)"
                    }
                }
            },
            options = new
            {
                responsive = true,
                scales = new
                {
                    x = new { type = "time" },
                    y = new { beginAtZero = true }
                }
            }
        };
    }

    private async Task<object> GenerateBarChartDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        // Simplified bar chart data generation
        await Task.CompletedTask;
        return new
        {
            type = "bar",
            data = new
            {
                labels = new[] { "Service A", "Service B", "Service C" },
                datasets = new[]
                {
                    new
                    {
                        label = "Requests",
                        data = new[] { 100, 150, 80 },
                        backgroundColor = "rgba(54, 162, 235, 0.2)",
                        borderColor = "rgba(54, 162, 235, 1)"
                    }
                }
            }
        };
    }

    private async Task<object> GeneratePieChartDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "pie",
            data = new
            {
                labels = new[] { "Success", "Error", "Warning" },
                datasets = new[]
                {
                    new
                    {
                        data = new[] { 70, 20, 10 },
                        backgroundColor = new[] { "#36A2EB", "#FF6384", "#FFCE56" }
                    }
                }
            }
        };
    }

    private async Task<object> GenerateGaugeDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "gauge",
            value = 75,
            min = 0,
            max = 100,
            unit = "%",
            thresholds = new[]
            {
                new { value = 50, color = "green" },
                new { value = 80, color = "yellow" },
                new { value = 100, color = "red" }
            }
        };
    }

    private async Task<object> GenerateCounterDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "counter",
            value = 1234,
            label = "Total Requests",
            trend = new { direction = "up", percentage = 5.2 }
        };
    }

    private async Task<object> GenerateTableDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "table",
            columns = new[] { "Service", "Status", "Response Time", "Error Rate" },
            rows = new[]
            {
                new object[] { "Service A", "Healthy", "120ms", "0.1%" },
                new object[] { "Service B", "Warning", "250ms", "2.3%" },
                new object[] { "Service C", "Critical", "500ms", "5.7%" }
            }
        };
    }

    private async Task<object> GenerateAlertDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        var alerts = await _alertRepository.GetActiveAlertsAsync(cancellationToken);
        
        return new
        {
            type = "alert",
            alerts = alerts.Take(10).Select(a => new
            {
                id = a.Id,
                title = a.Title,
                severity = a.Severity.ToString(),
                service = a.ServiceName,
                timestamp = a.CreatedAt
            })
        };
    }

    private async Task<object> GenerateMetricDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "metric",
            value = 42.5,
            unit = "ms",
            label = "Average Response Time",
            status = "normal"
        };
    }

    private async Task<object> GenerateStatusDataAsync(DashboardWidget widget, 
        Dictionary<string, object>? filters, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            type = "status",
            status = "healthy",
            message = "All systems operational",
            lastUpdated = DateTime.UtcNow
        };
    }

    private async Task<object> GenerateExportDataAsync(CustomDashboard dashboard, ExportFormat format, 
        CancellationToken cancellationToken)
    {
        return format switch
        {
            ExportFormat.JSON => await GenerateJsonExportAsync(dashboard, cancellationToken),
            ExportFormat.PDF => await GeneratePdfExportAsync(dashboard, cancellationToken),
            ExportFormat.PNG => await GenerateImageExportAsync(dashboard, cancellationToken),
            _ => throw new NotSupportedException($"Export format {format} is not supported")
        };
    }

    private async Task<object> GenerateJsonExportAsync(CustomDashboard dashboard, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return new
        {
            dashboard = new
            {
                id = dashboard.Id,
                name = dashboard.Name,
                description = dashboard.Description,
                widgets = dashboard.Widgets.Select(w => new
                {
                    id = w.Id,
                    title = w.Title,
                    type = w.Type.ToString(),
                    position = w.Position,
                    size = w.Size,
                    configuration = w.Configuration
                }),
                filters = dashboard.Filters.Select(f => new
                {
                    id = f.Id,
                    name = f.Name,
                    type = f.Type.ToString(),
                    field = f.Field,
                    operator = f.Operator.ToString(),
                    value = f.Value
                })
            }
        };
    }

    private async Task<object> GeneratePdfExportAsync(CustomDashboard dashboard, CancellationToken cancellationToken)
    {
        // Placeholder for PDF generation
        await Task.CompletedTask;
        return new { format = "pdf", content = "base64-encoded-pdf-content" };
    }

    private async Task<object> GenerateImageExportAsync(CustomDashboard dashboard, CancellationToken cancellationToken)
    {
        // Placeholder for image generation
        await Task.CompletedTask;
        return new { format = "png", content = "base64-encoded-image-content" };
    }
}
