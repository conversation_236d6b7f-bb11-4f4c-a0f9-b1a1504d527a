# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/SubscriptionManagement/SubscriptionManagement.API/SubscriptionManagement.API.csproj", "Services/SubscriptionManagement/SubscriptionManagement.API/"]
COPY ["Services/SubscriptionManagement/SubscriptionManagement.Application/SubscriptionManagement.Application.csproj", "Services/SubscriptionManagement/SubscriptionManagement.Application/"]
COPY ["Services/SubscriptionManagement/SubscriptionManagement.Domain/SubscriptionManagement.Domain.csproj", "Services/SubscriptionManagement/SubscriptionManagement.Domain/"]
COPY ["Services/SubscriptionManagement/SubscriptionManagement.Infrastructure/SubscriptionManagement.Infrastructure.csproj", "Services/SubscriptionManagement/SubscriptionManagement.Infrastructure/"]
COPY ["Shared/Shared.Domain/Shared.Domain.csproj", "Shared/Shared.Domain/"]
COPY ["Shared/Shared.Infrastructure/Shared.Infrastructure.csproj", "Shared/Shared.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/SubscriptionManagement/SubscriptionManagement.API/SubscriptionManagement.API.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Services/SubscriptionManagement/SubscriptionManagement.API"
RUN dotnet build "SubscriptionManagement.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "SubscriptionManagement.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

ENTRYPOINT ["dotnet", "SubscriptionManagement.API.dll"]
