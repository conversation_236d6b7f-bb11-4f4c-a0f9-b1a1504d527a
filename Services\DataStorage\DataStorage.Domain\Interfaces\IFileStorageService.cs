using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;
using DataStorage.Domain.ValueObjects;

namespace DataStorage.Domain.Interfaces;

public interface IFileStorageService
{
    // File upload operations
    Task<StorageLocation> UploadFileAsync(
        Stream fileStream,
        string fileName,
        string contentType,
        StorageProvider? preferredProvider = null,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default);

    Task<StorageLocation> UploadFileAsync(
        byte[] fileContent,
        string fileName,
        string contentType,
        StorageProvider? preferredProvider = null,
        Dictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default);

    // File download operations
    Task<Stream> DownloadFileAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<byte[]> DownloadFileBytesAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<string> GetDownloadUrlAsync(StorageLocation location, TimeSpan? expiry = null, CancellationToken cancellationToken = default);

    // File management operations
    Task<bool> FileExistsAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task DeleteFileAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<StorageLocation> CopyFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken = default);
    Task<StorageLocation> MoveFileAsync(StorageLocation source, StorageLocation destination, CancellationToken cancellationToken = default);

    // File metadata operations
    Task<FileMetadata> GetFileMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task UpdateFileMetadataAsync(StorageLocation location, Dictionary<string, string> metadata, CancellationToken cancellationToken = default);

    // Bulk operations
    Task<IEnumerable<StorageLocation>> UploadMultipleFilesAsync(
        IEnumerable<(Stream stream, string fileName, string contentType)> files,
        StorageProvider? preferredProvider = null,
        CancellationToken cancellationToken = default);

    Task DeleteMultipleFilesAsync(IEnumerable<StorageLocation> locations, CancellationToken cancellationToken = default);

    // Storage analytics
    Task<long> GetStorageSizeAsync(string containerName, CancellationToken cancellationToken = default);
    Task<int> GetFileCountAsync(string containerName, CancellationToken cancellationToken = default);

    // CDN operations
    Task<string> GetCDNUrlAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task InvalidateCDNCacheAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Security operations
    Task<string> GenerateSecureUploadUrlAsync(
        string fileName,
        string contentType,
        TimeSpan expiry,
        StorageProvider? preferredProvider = null,
        CancellationToken cancellationToken = default);

    Task<string> GenerateSecureDownloadUrlAsync(
        StorageLocation location,
        TimeSpan expiry,
        CancellationToken cancellationToken = default);
}

public interface IDocumentProcessingService
{
    // Document processing
    Task<string> ExtractTextAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateThumbnailAsync(StorageLocation location, int width = 200, int height = 200, CancellationToken cancellationToken = default);
    Task<FileMetadata> AnalyzeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Document conversion
    Task<StorageLocation> ConvertToPdfAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<StorageLocation> ConvertToImageAsync(StorageLocation location, string format = "png", CancellationToken cancellationToken = default);

    // Document validation
    Task<bool> ValidateDocumentAsync(StorageLocation location, DocumentType expectedType, CancellationToken cancellationToken = default);
    Task<string> CalculateChecksumAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // OCR and text extraction
    Task<string> PerformOCRAsync(StorageLocation location, string? language = null, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> ExtractMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default);
}

public interface IAdvancedFileProcessingService
{
    // Complex file format processing
    Task<FileProcessingResult> ProcessComplexDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>> ExtractAdvancedMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<bool> ValidateFileFormatAsync(StorageLocation location, string expectedFormat, CancellationToken cancellationToken = default);

    // Office document processing
    Task<string> ExtractTextFromOfficeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<List<string>> ExtractImagesFromOfficeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<DocumentStructure> AnalyzeDocumentStructureAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // CAD file processing
    Task<CadFileInfo> ProcessCadFileAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateCadThumbnailAsync(StorageLocation location, int width = 400, int height = 400, CancellationToken cancellationToken = default);
    Task<List<CadLayer>> ExtractCadLayersAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Specialized format processing
    Task<ArchiveContents> ProcessArchiveFileAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<EmailContent> ProcessEmailFileAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<DatabaseSchema> ProcessDatabaseFileAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Format conversion
    Task<StorageLocation> ConvertToStandardFormatAsync(StorageLocation location, string targetFormat, CancellationToken cancellationToken = default);
    Task<List<string>> GetSupportedConversionsAsync(string sourceFormat, CancellationToken cancellationToken = default);
}

public interface IMediaTranscodingService
{
    // Video transcoding
    Task<TranscodingResult> TranscodeVideoAsync(StorageLocation sourceLocation, VideoTranscodingOptions options, CancellationToken cancellationToken = default);
    Task<List<VideoFormat>> GetSupportedVideoFormatsAsync(CancellationToken cancellationToken = default);
    Task<VideoMetadata> GetVideoMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Audio transcoding
    Task<TranscodingResult> TranscodeAudioAsync(StorageLocation sourceLocation, AudioTranscodingOptions options, CancellationToken cancellationToken = default);
    Task<List<AudioFormat>> GetSupportedAudioFormatsAsync(CancellationToken cancellationToken = default);
    Task<AudioMetadata> GetAudioMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default);

    // Batch processing
    Task<BatchTranscodingResult> TranscodeBatchAsync(List<BatchTranscodingJob> jobs, CancellationToken cancellationToken = default);
    Task<TranscodingJobStatus> GetTranscodingJobStatusAsync(Guid jobId, CancellationToken cancellationToken = default);
    Task CancelTranscodingJobAsync(Guid jobId, CancellationToken cancellationToken = default);

    // Thumbnail generation
    Task<StorageLocation> GenerateVideoThumbnailAsync(StorageLocation videoLocation, TimeSpan timestamp, ThumbnailOptions options, CancellationToken cancellationToken = default);
    Task<List<StorageLocation>> GenerateVideoThumbnailsAsync(StorageLocation videoLocation, List<TimeSpan> timestamps, ThumbnailOptions options, CancellationToken cancellationToken = default);

    // Quality analysis
    Task<MediaQualityAnalysis> AnalyzeMediaQualityAsync(StorageLocation location, CancellationToken cancellationToken = default);
    Task<List<QualityPreset>> GetAvailableQualityPresetsAsync(MediaType mediaType, CancellationToken cancellationToken = default);
}

public interface ICdnIntegrationService
{
    // CDN Management
    Task<CdnDeploymentResult> DeployToCdnAsync(StorageLocation sourceLocation, CdnDeploymentOptions options, CancellationToken cancellationToken = default);
    Task<List<CdnEndpoint>> GetCdnEndpointsAsync(CancellationToken cancellationToken = default);
    Task<CdnEndpoint> CreateCdnEndpointAsync(CdnEndpointConfiguration configuration, CancellationToken cancellationToken = default);
    Task DeleteCdnEndpointAsync(string endpointId, CancellationToken cancellationToken = default);

    // Cache Management
    Task<CacheInvalidationResult> InvalidateCacheAsync(List<string> paths, CdnProvider provider, CancellationToken cancellationToken = default);
    Task<CacheInvalidationResult> InvalidateAllCacheAsync(CdnProvider provider, CancellationToken cancellationToken = default);
    Task<CacheStatistics> GetCacheStatisticsAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<CacheStatus> GetCacheStatusAsync(string path, CdnProvider provider, CancellationToken cancellationToken = default);

    // Performance Optimization
    Task<PerformanceAnalysis> AnalyzePerformanceAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<PerformanceRecommendation>> GetPerformanceRecommendationsAsync(string endpointId, CancellationToken cancellationToken = default);
    Task<CompressionResult> EnableCompressionAsync(string endpointId, CompressionOptions options, CancellationToken cancellationToken = default);

    // Global Distribution
    Task<List<EdgeLocation>> GetEdgeLocationsAsync(CdnProvider provider, CancellationToken cancellationToken = default);
    Task<GeographicDistribution> GetGeographicDistributionAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<LatencyAnalysis> AnalyzeLatencyAsync(string endpointId, List<string> testLocations, CancellationToken cancellationToken = default);

    // Security and Access Control
    Task<SecurityConfiguration> ConfigureSecurityAsync(string endpointId, SecurityOptions options, CancellationToken cancellationToken = default);
    Task<List<AccessRule>> GetAccessRulesAsync(string endpointId, CancellationToken cancellationToken = default);
    Task AddAccessRuleAsync(string endpointId, AccessRule rule, CancellationToken cancellationToken = default);
    Task RemoveAccessRuleAsync(string endpointId, string ruleId, CancellationToken cancellationToken = default);
}

public interface IAdvancedSearchService
{
    // Document Indexing
    Task<IndexingResult> IndexDocumentAsync(Guid documentId, SearchableDocument document, CancellationToken cancellationToken = default);
    Task<IndexingResult> IndexDocumentsAsync(List<SearchableDocument> documents, CancellationToken cancellationToken = default);
    Task<bool> RemoveFromIndexAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<IndexStatistics> GetIndexStatisticsAsync(CancellationToken cancellationToken = default);

    // Full-Text Search
    Task<SearchResult> SearchAsync(SearchQuery query, CancellationToken cancellationToken = default);
    Task<SearchResult> SearchWithHighlightingAsync(SearchQuery query, HighlightOptions options, CancellationToken cancellationToken = default);
    Task<AutoCompleteResult> AutoCompleteAsync(string partialQuery, int maxSuggestions = 10, CancellationToken cancellationToken = default);
    Task<List<SearchSuggestion>> GetSearchSuggestionsAsync(string query, CancellationToken cancellationToken = default);

    // Faceted Search
    Task<FacetedSearchResult> FacetedSearchAsync(FacetedSearchQuery query, CancellationToken cancellationToken = default);
    Task<List<SearchFacet>> GetAvailableFacetsAsync(CancellationToken cancellationToken = default);
    Task<FacetValues> GetFacetValuesAsync(string facetName, string? filter = null, CancellationToken cancellationToken = default);

    // Advanced Queries
    Task<SearchResult> BooleanSearchAsync(BooleanSearchQuery query, CancellationToken cancellationToken = default);
    Task<SearchResult> FuzzySearchAsync(FuzzySearchQuery query, CancellationToken cancellationToken = default);
    Task<SearchResult> RangeSearchAsync(RangeSearchQuery query, CancellationToken cancellationToken = default);
    Task<SearchResult> GeoSearchAsync(GeoSearchQuery query, CancellationToken cancellationToken = default);

    // Analytics and Insights
    Task<SearchAnalytics> GetSearchAnalyticsAsync(TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<PopularQuery>> GetPopularQueriesAsync(int count = 10, TimeSpan? period = null, CancellationToken cancellationToken = default);
    Task<List<SearchTrend>> GetSearchTrendsAsync(TimeSpan period, CancellationToken cancellationToken = default);

    // Index Management
    Task<bool> CreateIndexAsync(IndexConfiguration configuration, CancellationToken cancellationToken = default);
    Task<bool> DeleteIndexAsync(string indexName, CancellationToken cancellationToken = default);
    Task<IndexHealth> GetIndexHealthAsync(CancellationToken cancellationToken = default);
    Task<bool> OptimizeIndexAsync(CancellationToken cancellationToken = default);
}

public interface IVersionControlService
{
    // Document Versioning
    Task<Entities.DocumentVersion> CreateVersionAsync(Guid documentId, VersionCreateRequest request, CancellationToken cancellationToken = default);
    Task<Entities.DocumentVersion> GetVersionAsync(Guid documentId, string versionId, CancellationToken cancellationToken = default);
    Task<List<Entities.DocumentVersion>> GetVersionHistoryAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<bool> DeleteVersionAsync(Guid documentId, string versionId, CancellationToken cancellationToken = default);

    // Branching and Merging
    Task<DocumentBranch> CreateBranchAsync(Guid documentId, BranchCreateRequest request, CancellationToken cancellationToken = default);
    Task<List<DocumentBranch>> GetBranchesAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<MergeResult> MergeBranchAsync(Guid documentId, MergeRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteBranchAsync(Guid documentId, string branchName, CancellationToken cancellationToken = default);

    // Conflict Resolution
    Task<List<MergeConflict>> GetMergeConflictsAsync(Guid documentId, string sourceBranch, string targetBranch, CancellationToken cancellationToken = default);
    Task<ConflictResolutionResult> ResolveConflictAsync(Guid documentId, ConflictResolutionRequest request, CancellationToken cancellationToken = default);
    Task<List<ConflictResolutionStrategy>> GetAvailableResolutionStrategiesAsync(CancellationToken cancellationToken = default);

    // Comparison and Diff
    Task<DocumentComparison> CompareVersionsAsync(Guid documentId, string version1, string version2, CancellationToken cancellationToken = default);
    Task<DocumentDiff> GetVersionDiffAsync(Guid documentId, string version1, string version2, CancellationToken cancellationToken = default);
    Task<List<ChangeSet>> GetChangeHistoryAsync(Guid documentId, TimeSpan? period = null, CancellationToken cancellationToken = default);

    // Tagging and Labeling
    Task<VersionTag> CreateTagAsync(Guid documentId, string versionId, TagCreateRequest request, CancellationToken cancellationToken = default);
    Task<List<VersionTag>> GetTagsAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<bool> DeleteTagAsync(Guid documentId, string tagName, CancellationToken cancellationToken = default);

    // Rollback and Restore
    Task<RollbackResult> RollbackToVersionAsync(Guid documentId, string versionId, CancellationToken cancellationToken = default);
    Task<RestoreResult> RestoreFromBackupAsync(Guid documentId, string backupId, CancellationToken cancellationToken = default);
    Task<List<BackupPoint>> GetBackupPointsAsync(Guid documentId, CancellationToken cancellationToken = default);

    // Version Analytics
    Task<VersionAnalytics> GetVersionAnalyticsAsync(Guid documentId, TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<VersionActivity>> GetVersionActivityAsync(Guid documentId, int limit = 50, CancellationToken cancellationToken = default);
    Task<CollaborationInsights> GetCollaborationInsightsAsync(Guid documentId, TimeSpan period, CancellationToken cancellationToken = default);
}

public interface IDataArchivingService
{
    // Archive Management
    Task<ArchiveResult> ArchiveDocumentAsync(Guid documentId, ArchiveRequest request, CancellationToken cancellationToken = default);
    Task<ArchiveResult> ArchiveDocumentsAsync(List<Guid> documentIds, ArchiveRequest request, CancellationToken cancellationToken = default);
    Task<RestoreFromArchiveResult> RestoreFromArchiveAsync(Guid documentId, RestoreFromArchiveRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteFromArchiveAsync(Guid documentId, CancellationToken cancellationToken = default);

    // Archive Policies
    Task<ArchivePolicy> CreateArchivePolicyAsync(ArchivePolicyRequest request, CancellationToken cancellationToken = default);
    Task<List<ArchivePolicy>> GetArchivePoliciesAsync(CancellationToken cancellationToken = default);
    Task<ArchivePolicy> UpdateArchivePolicyAsync(Guid policyId, ArchivePolicyRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteArchivePolicyAsync(Guid policyId, CancellationToken cancellationToken = default);

    // Automated Archiving
    Task<List<ArchiveCandidate>> GetArchiveCandidatesAsync(Guid? policyId = null, CancellationToken cancellationToken = default);
    Task<BatchArchiveResult> ExecuteAutomaticArchivingAsync(Guid policyId, CancellationToken cancellationToken = default);
    Task<ArchiveSchedule> ScheduleArchivingAsync(ArchiveScheduleRequest request, CancellationToken cancellationToken = default);
    Task<bool> CancelScheduledArchivingAsync(Guid scheduleId, CancellationToken cancellationToken = default);

    // Archive Storage Tiers
    Task<List<StorageTier>> GetStorageTiersAsync(CancellationToken cancellationToken = default);
    Task<TierMigrationResult> MigrateToTierAsync(Guid documentId, StorageTierType targetTier, CancellationToken cancellationToken = default);
    Task<List<TierMigrationJob>> GetTierMigrationJobsAsync(CancellationToken cancellationToken = default);

    // Archive Analytics
    Task<ArchiveAnalytics> GetArchiveAnalyticsAsync(TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<ArchiveMetrics>> GetArchiveMetricsAsync(ArchiveMetricsRequest request, CancellationToken cancellationToken = default);
    Task<StorageOptimizationReport> GetStorageOptimizationReportAsync(CancellationToken cancellationToken = default);

    // Compliance and Retention
    Task<ComplianceReport> GetComplianceReportAsync(ComplianceReportRequest request, CancellationToken cancellationToken = default);
    Task<List<RetentionViolation>> GetRetentionViolationsAsync(CancellationToken cancellationToken = default);
    Task<bool> ApplyLegalHoldAsync(Guid documentId, LegalHoldRequest request, CancellationToken cancellationToken = default);
    Task<bool> ReleaseLegalHoldAsync(Guid documentId, Guid legalHoldId, CancellationToken cancellationToken = default);
}

public interface IDataRetentionComplianceService
{
    // Retention Policy Management
    Task<RetentionPolicy> CreateRetentionPolicyAsync(RetentionPolicyRequest request, CancellationToken cancellationToken = default);
    Task<List<RetentionPolicy>> GetRetentionPoliciesAsync(CancellationToken cancellationToken = default);
    Task<RetentionPolicy> UpdateRetentionPolicyAsync(Guid policyId, RetentionPolicyRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteRetentionPolicyAsync(Guid policyId, CancellationToken cancellationToken = default);

    // Document Retention Management
    Task<RetentionAssignmentResult> AssignRetentionPolicyAsync(Guid documentId, Guid policyId, CancellationToken cancellationToken = default);
    Task<RetentionAssignmentResult> AssignRetentionPoliciesAsync(List<Guid> documentIds, Guid policyId, CancellationToken cancellationToken = default);
    Task<RetentionInfo> GetDocumentRetentionInfoAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<List<RetentionScheduleItem>> GetRetentionScheduleAsync(DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);

    // Compliance Monitoring
    Task<List<RetentionViolation>> GetRetentionViolationsAsync(RetentionViolationFilter? filter = null, CancellationToken cancellationToken = default);
    Task<ComplianceAuditResult> PerformComplianceAuditAsync(ComplianceAuditRequest request, CancellationToken cancellationToken = default);
    Task<List<ComplianceAlert>> GetComplianceAlertsAsync(CancellationToken cancellationToken = default);
    Task<bool> AcknowledgeComplianceAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null, CancellationToken cancellationToken = default);

    // Legal Hold Management
    Task<LegalHold> CreateLegalHoldAsync(LegalHoldCreateRequest request, CancellationToken cancellationToken = default);
    Task<List<LegalHold>> GetLegalHoldsAsync(CancellationToken cancellationToken = default);
    Task<LegalHoldResult> ApplyLegalHoldToDocumentsAsync(Guid legalHoldId, List<Guid> documentIds, CancellationToken cancellationToken = default);
    Task<LegalHoldResult> ReleaseLegalHoldFromDocumentsAsync(Guid legalHoldId, List<Guid> documentIds, CancellationToken cancellationToken = default);
    Task<bool> CloseLegalHoldAsync(Guid legalHoldId, Guid closedBy, string? reason = null, CancellationToken cancellationToken = default);

    // Automated Compliance
    Task<AutomatedComplianceResult> ExecuteAutomatedComplianceAsync(CancellationToken cancellationToken = default);
    Task<ComplianceSchedule> ScheduleComplianceCheckAsync(ComplianceScheduleRequest request, CancellationToken cancellationToken = default);
    Task<List<ComplianceJob>> GetComplianceJobsAsync(CancellationToken cancellationToken = default);

    // Reporting and Analytics
    Task<RetentionComplianceReport> GenerateRetentionComplianceReportAsync(RetentionReportRequest request, CancellationToken cancellationToken = default);
    Task<List<ComplianceMetric>> GetComplianceMetricsAsync(ComplianceMetricsRequest request, CancellationToken cancellationToken = default);
    Task<DataGovernanceDashboard> GetDataGovernanceDashboardAsync(CancellationToken cancellationToken = default);
}

public interface IBackupAndRecoveryService
{
    // Backup Management
    Task<BackupResult> CreateBackupAsync(BackupRequest request, CancellationToken cancellationToken = default);
    Task<BackupResult> CreateIncrementalBackupAsync(BackupRequest request, CancellationToken cancellationToken = default);
    Task<BackupResult> CreateDifferentialBackupAsync(BackupRequest request, CancellationToken cancellationToken = default);
    Task<List<BackupInfo>> GetBackupsAsync(BackupFilter? filter = null, CancellationToken cancellationToken = default);
    Task<BackupInfo> GetBackupInfoAsync(Guid backupId, CancellationToken cancellationToken = default);
    Task<bool> DeleteBackupAsync(Guid backupId, CancellationToken cancellationToken = default);

    // Recovery Operations
    Task<RecoveryResult> RestoreFromBackupAsync(RestoreRequest request, CancellationToken cancellationToken = default);
    Task<RecoveryResult> PerformPointInTimeRecoveryAsync(PointInTimeRecoveryRequest request, CancellationToken cancellationToken = default);
    Task<List<RecoveryPoint>> GetRecoveryPointsAsync(Guid? documentId = null, CancellationToken cancellationToken = default);
    Task<RecoveryValidationResult> ValidateRecoveryAsync(RecoveryValidationRequest request, CancellationToken cancellationToken = default);

    // Backup Scheduling
    Task<BackupSchedule> CreateBackupScheduleAsync(BackupScheduleRequest request, CancellationToken cancellationToken = default);
    Task<List<BackupSchedule>> GetBackupSchedulesAsync(CancellationToken cancellationToken = default);
    Task<BackupSchedule> UpdateBackupScheduleAsync(Guid scheduleId, BackupScheduleRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteBackupScheduleAsync(Guid scheduleId, CancellationToken cancellationToken = default);
    Task<BackupExecutionResult> ExecuteScheduledBackupAsync(Guid scheduleId, CancellationToken cancellationToken = default);

    // Cross-Region Replication
    Task<ReplicationResult> SetupReplicationAsync(ReplicationSetupRequest request, CancellationToken cancellationToken = default);
    Task<List<ReplicationTarget>> GetReplicationTargetsAsync(CancellationToken cancellationToken = default);
    Task<ReplicationStatus> GetReplicationStatusAsync(Guid replicationId, CancellationToken cancellationToken = default);
    Task<bool> PauseReplicationAsync(Guid replicationId, CancellationToken cancellationToken = default);
    Task<bool> ResumeReplicationAsync(Guid replicationId, CancellationToken cancellationToken = default);

    // Disaster Recovery
    Task<DisasterRecoveryPlan> CreateDisasterRecoveryPlanAsync(DisasterRecoveryPlanRequest request, CancellationToken cancellationToken = default);
    Task<List<DisasterRecoveryPlan>> GetDisasterRecoveryPlansAsync(CancellationToken cancellationToken = default);
    Task<DisasterRecoveryTestResult> TestDisasterRecoveryAsync(DisasterRecoveryTestRequest request, CancellationToken cancellationToken = default);
    Task<DisasterRecoveryExecutionResult> ExecuteDisasterRecoveryAsync(DisasterRecoveryExecutionRequest request, CancellationToken cancellationToken = default);

    // Backup Verification and Integrity
    Task<BackupVerificationResult> VerifyBackupIntegrityAsync(Guid backupId, CancellationToken cancellationToken = default);
    Task<List<BackupVerificationResult>> VerifyAllBackupsAsync(CancellationToken cancellationToken = default);
    Task<BackupHealthReport> GetBackupHealthReportAsync(CancellationToken cancellationToken = default);

    // Monitoring and Analytics
    Task<BackupAnalytics> GetBackupAnalyticsAsync(TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<BackupMetrics>> GetBackupMetricsAsync(BackupMetricsRequest request, CancellationToken cancellationToken = default);
    Task<List<BackupAlert>> GetBackupAlertsAsync(CancellationToken cancellationToken = default);
    Task<bool> AcknowledgeBackupAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null, CancellationToken cancellationToken = default);
}

public interface IMonitoringAndPerformanceService
{
    // System Monitoring
    Task<SystemHealthStatus> GetSystemHealthAsync(CancellationToken cancellationToken = default);
    Task<List<SystemMetric>> GetSystemMetricsAsync(SystemMetricsRequest request, CancellationToken cancellationToken = default);
    Task<List<PerformanceAlert>> GetPerformanceAlertsAsync(CancellationToken cancellationToken = default);
    Task<bool> AcknowledgePerformanceAlertAsync(Guid alertId, Guid acknowledgedBy, string? notes = null, CancellationToken cancellationToken = default);

    // Performance Testing
    Task<PerformanceTestResult> RunPerformanceTestAsync(PerformanceTestRequest request, CancellationToken cancellationToken = default);
    Task<List<PerformanceTestResult>> GetPerformanceTestHistoryAsync(PerformanceTestFilter? filter = null, CancellationToken cancellationToken = default);
    Task<LoadTestResult> RunLoadTestAsync(LoadTestRequest request, CancellationToken cancellationToken = default);
    Task<StressTestResult> RunStressTestAsync(StressTestRequest request, CancellationToken cancellationToken = default);

    // Benchmarking
    Task<BenchmarkResult> RunBenchmarkAsync(BenchmarkRequest request, CancellationToken cancellationToken = default);
    Task<List<BenchmarkResult>> GetBenchmarkHistoryAsync(BenchmarkFilter? filter = null, CancellationToken cancellationToken = default);
    Task<BenchmarkComparison> CompareBenchmarksAsync(BenchmarkComparisonRequest request, CancellationToken cancellationToken = default);

    // Resource Monitoring
    Task<ResourceUsage> GetResourceUsageAsync(CancellationToken cancellationToken = default);
    Task<List<ResourceMetric>> GetResourceMetricsAsync(ResourceMetricsRequest request, CancellationToken cancellationToken = default);
    Task<ResourceForecast> GetResourceForecastAsync(ResourceForecastRequest request, CancellationToken cancellationToken = default);

    // Application Performance Monitoring (APM)
    Task<ApplicationPerformance> GetApplicationPerformanceAsync(CancellationToken cancellationToken = default);
    Task<List<TransactionTrace>> GetTransactionTracesAsync(TransactionTraceFilter filter, CancellationToken cancellationToken = default);
    Task<List<ErrorTrace>> GetErrorTracesAsync(ErrorTraceFilter filter, CancellationToken cancellationToken = default);
    Task<DependencyMap> GetDependencyMapAsync(CancellationToken cancellationToken = default);

    // Custom Metrics and Dashboards
    Task<CustomMetric> CreateCustomMetricAsync(CustomMetricRequest request, CancellationToken cancellationToken = default);
    Task<List<CustomMetric>> GetCustomMetricsAsync(CancellationToken cancellationToken = default);
    Task<Dashboard> CreateDashboardAsync(DashboardRequest request, CancellationToken cancellationToken = default);
    Task<List<Dashboard>> GetDashboardsAsync(CancellationToken cancellationToken = default);

    // Alerting and Notifications
    Task<AlertRule> CreateAlertRuleAsync(AlertRuleRequest request, CancellationToken cancellationToken = default);
    Task<List<AlertRule>> GetAlertRulesAsync(CancellationToken cancellationToken = default);
    Task<bool> UpdateAlertRuleAsync(Guid ruleId, AlertRuleRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteAlertRuleAsync(Guid ruleId, CancellationToken cancellationToken = default);

    // Reporting
    Task<PerformanceReport> GeneratePerformanceReportAsync(PerformanceReportRequest request, CancellationToken cancellationToken = default);
    Task<CapacityPlanningReport> GenerateCapacityPlanningReportAsync(CapacityPlanningRequest request, CancellationToken cancellationToken = default);
    Task<SlaReport> GenerateSlaReportAsync(SlaReportRequest request, CancellationToken cancellationToken = default);
}
