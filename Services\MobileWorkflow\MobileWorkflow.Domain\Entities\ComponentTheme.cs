using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class ComponentTheme : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public bool IsDefault { get; private set; }
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Colors { get; private set; }
    public Dictionary<string, object> Typography { get; private set; }
    public Dictionary<string, object> Spacing { get; private set; }
    public Dictionary<string, object> Borders { get; private set; }
    public Dictionary<string, object> Shadows { get; private set; }
    public Dictionary<string, object> Animations { get; private set; }
    public Dictionary<string, object> CustomProperties { get; private set; }
    public List<string> SupportedPlatforms { get; private set; }
    public Dictionary<string, object> PlatformOverrides { get; private set; }
    public string? ParentThemeId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<UIComponent> Components { get; private set; } = new List<UIComponent>();
    public virtual ICollection<ComponentTheme> ChildThemes { get; private set; } = new List<ComponentTheme>();

    private ComponentTheme() { } // EF Core

    public ComponentTheme(
        string name,
        string displayName,
        string description,
        List<string> supportedPlatforms,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        SupportedPlatforms = supportedPlatforms ?? throw new ArgumentNullException(nameof(supportedPlatforms));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsDefault = false;
        CreatedAt = DateTime.UtcNow;
        Colors = new Dictionary<string, object>();
        Typography = new Dictionary<string, object>();
        Spacing = new Dictionary<string, object>();
        Borders = new Dictionary<string, object>();
        Shadows = new Dictionary<string, object>();
        Animations = new Dictionary<string, object>();
        CustomProperties = new Dictionary<string, object>();
        PlatformOverrides = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        // Set default theme properties
        SetDefaultProperties();

        AddDomainEvent(new ComponentThemeCreatedEvent(Id, Name, CreatedBy));
    }

    public void UpdateColors(Dictionary<string, object> colors, string updatedBy)
    {
        Colors = colors ?? throw new ArgumentNullException(nameof(colors));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeColorsUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateTypography(Dictionary<string, object> typography, string updatedBy)
    {
        Typography = typography ?? throw new ArgumentNullException(nameof(typography));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeTypographyUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateSpacing(Dictionary<string, object> spacing, string updatedBy)
    {
        Spacing = spacing ?? throw new ArgumentNullException(nameof(spacing));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeSpacingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetAsDefault()
    {
        IsDefault = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeSetAsDefaultEvent(Id, Name));
    }

    public void RemoveAsDefault()
    {
        IsDefault = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeRemovedAsDefaultEvent(Id, Name));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeDeactivatedEvent(Id, Name));
    }

    public void SetPlatformOverride(string platform, Dictionary<string, object> overrides)
    {
        if (!SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase))
        {
            throw new InvalidOperationException($"Platform {platform} is not supported by this theme");
        }

        PlatformOverrides[platform] = overrides ?? throw new ArgumentNullException(nameof(overrides));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemePlatformOverrideSetEvent(Id, Name, platform));
    }

    public void SetParentTheme(string parentThemeId)
    {
        ParentThemeId = parentThemeId;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeParentThemeSetEvent(Id, Name, parentThemeId));
    }

    public void AddCustomProperty(string key, object value)
    {
        CustomProperties[key] = value;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new ComponentThemeCustomPropertyAddedEvent(Id, Name, key));
    }

    public void RemoveCustomProperty(string key)
    {
        if (CustomProperties.Remove(key))
        {
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new ComponentThemeCustomPropertyRemovedEvent(Id, Name, key));
        }
    }

    public Dictionary<string, object> GetThemeForPlatform(string platform)
    {
        var theme = new Dictionary<string, object>
        {
            ["colors"] = Colors,
            ["typography"] = Typography,
            ["spacing"] = Spacing,
            ["borders"] = Borders,
            ["shadows"] = Shadows,
            ["animations"] = Animations,
            ["custom"] = CustomProperties
        };

        // Apply platform-specific overrides
        if (PlatformOverrides.TryGetValue(platform, out var overrides) && overrides is Dictionary<string, object> platformOverrides)
        {
            foreach (var kvp in platformOverrides)
            {
                theme[kvp.Key] = kvp.Value;
            }
        }

        return theme;
    }

    public bool SupportsPlatform(string platform)
    {
        return SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase);
    }

    public string GetColor(string colorName, string? fallback = null)
    {
        if (Colors.TryGetValue(colorName, out var color) && color is string colorValue)
        {
            return colorValue;
        }
        return fallback ?? "#000000";
    }

    public void SetColor(string colorName, string colorValue)
    {
        Colors[colorName] = colorValue;
        UpdatedAt = DateTime.UtcNow;
    }

    public Dictionary<string, object> GetTypographyStyle(string styleName)
    {
        if (Typography.TryGetValue(styleName, out var style) && style is Dictionary<string, object> typographyStyle)
        {
            return typographyStyle;
        }
        return new Dictionary<string, object>();
    }

    public void SetTypographyStyle(string styleName, Dictionary<string, object> style)
    {
        Typography[styleName] = style;
        UpdatedAt = DateTime.UtcNow;
    }

    public string GetSpacing(string spacingName, string? fallback = null)
    {
        if (Spacing.TryGetValue(spacingName, out var spacing) && spacing is string spacingValue)
        {
            return spacingValue;
        }
        return fallback ?? "0px";
    }

    public void SetSpacing(string spacingName, string spacingValue)
    {
        Spacing[spacingName] = spacingValue;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    private void SetDefaultProperties()
    {
        // Default colors
        Colors["primary"] = "#007bff";
        Colors["secondary"] = "#6c757d";
        Colors["success"] = "#28a745";
        Colors["danger"] = "#dc3545";
        Colors["warning"] = "#ffc107";
        Colors["info"] = "#17a2b8";
        Colors["light"] = "#f8f9fa";
        Colors["dark"] = "#343a40";
        Colors["white"] = "#ffffff";
        Colors["black"] = "#000000";

        // Default typography
        Typography["h1"] = new Dictionary<string, object> { ["fontSize"] = "2.5rem", ["fontWeight"] = "bold" };
        Typography["h2"] = new Dictionary<string, object> { ["fontSize"] = "2rem", ["fontWeight"] = "bold" };
        Typography["h3"] = new Dictionary<string, object> { ["fontSize"] = "1.75rem", ["fontWeight"] = "bold" };
        Typography["body"] = new Dictionary<string, object> { ["fontSize"] = "1rem", ["fontWeight"] = "normal" };
        Typography["caption"] = new Dictionary<string, object> { ["fontSize"] = "0.875rem", ["fontWeight"] = "normal" };

        // Default spacing
        Spacing["xs"] = "0.25rem";
        Spacing["sm"] = "0.5rem";
        Spacing["md"] = "1rem";
        Spacing["lg"] = "1.5rem";
        Spacing["xl"] = "3rem";

        // Default borders
        Borders["thin"] = "1px solid";
        Borders["medium"] = "2px solid";
        Borders["thick"] = "4px solid";
        Borders["radius"] = "0.25rem";

        // Default shadows
        Shadows["sm"] = "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)";
        Shadows["md"] = "0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)";
        Shadows["lg"] = "0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)";

        // Default animations
        Animations["fast"] = "0.15s ease-in-out";
        Animations["normal"] = "0.3s ease-in-out";
        Animations["slow"] = "0.5s ease-in-out";
    }
}


