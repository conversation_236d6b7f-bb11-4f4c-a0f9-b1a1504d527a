# 🔄 Real-time Features & SignalR Integration

## 📋 Overview

The TLI platform provides comprehensive real-time capabilities using SignalR for live updates, notifications, and collaborative features. This guide covers implementation details for frontend applications.

## 🏗️ SignalR Architecture

### Hub Structure

Each microservice provides its own SignalR hub for domain-specific real-time events:

```
API Gateway (Port 5000)
├── /api/v1/users/hub          → User Management Hub
├── /api/v1/orders/hub         → Order Management Hub  
├── /api/v1/trips/hub          → Trip Management Hub
├── /api/v1/fleet/hub          → Fleet Management Hub
├── /api/v1/notifications/hub  → Notification Hub
└── /api/v1/analytics/hub      → Analytics Hub
```

### Connection Management

```typescript
interface HubConnection {
  url: string;
  accessTokenFactory: () => string;
  automaticReconnect: boolean;
  serverTimeoutInMilliseconds: number;
  keepAliveIntervalInMilliseconds: number;
}

class SignalRManager {
  private connections: Map<string, signalR.HubConnection> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;

  async createConnection(hubName: string, hubUrl: string): Promise<signalR.HubConnection> {
    const connection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => this.getAccessToken(),
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          const delay = Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000);
          return delay;
        }
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();

    // Connection event handlers
    connection.onreconnecting((error) => {
      console.log(`SignalR ${hubName} reconnecting:`, error);
      this.onConnectionStateChanged(hubName, 'Reconnecting');
    });

    connection.onreconnected((connectionId) => {
      console.log(`SignalR ${hubName} reconnected:`, connectionId);
      this.reconnectAttempts.set(hubName, 0);
      this.onConnectionStateChanged(hubName, 'Connected');
    });

    connection.onclose((error) => {
      console.log(`SignalR ${hubName} connection closed:`, error);
      this.onConnectionStateChanged(hubName, 'Disconnected');
      this.handleConnectionClosed(hubName, connection);
    });

    this.connections.set(hubName, connection);
    return connection;
  }

  private async handleConnectionClosed(hubName: string, connection: signalR.HubConnection): Promise<void> {
    const attempts = this.reconnectAttempts.get(hubName) || 0;
    
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(hubName, attempts + 1);
      
      setTimeout(async () => {
        try {
          await connection.start();
          console.log(`SignalR ${hubName} manually reconnected`);
        } catch (error) {
          console.error(`Failed to manually reconnect ${hubName}:`, error);
        }
      }, 5000 * (attempts + 1));
    }
  }

  private getAccessToken(): string {
    return localStorage.getItem('tli_access_token') || '';
  }

  private onConnectionStateChanged(hubName: string, state: string): void {
    // Emit connection state change event
    window.dispatchEvent(new CustomEvent('signalr-connection-state', {
      detail: { hubName, state }
    }));
  }

  getConnection(hubName: string): signalR.HubConnection | undefined {
    return this.connections.get(hubName);
  }

  async disconnectAll(): Promise<void> {
    for (const [hubName, connection] of this.connections) {
      try {
        await connection.stop();
        console.log(`SignalR ${hubName} disconnected`);
      } catch (error) {
        console.error(`Error disconnecting ${hubName}:`, error);
      }
    }
    this.connections.clear();
    this.reconnectAttempts.clear();
  }
}
```

## 📱 Hub-Specific Implementations

### 1. User Management Hub

**Connection URL**: `/api/v1/users/hub`

**Events:**
```typescript
interface UserManagementEvents {
  UserApproved: (data: UserApprovedNotification) => void;
  UserRejected: (data: UserRejectedNotification) => void;
  DocumentsApproved: (data: DocumentsApprovedNotification) => void;
  DocumentsRejected: (data: DocumentsRejectedNotification) => void;
  DocumentOcrProcessed: (data: DocumentOcrProcessedNotification) => void;
  ProfileStatusChanged: (data: ProfileStatusChangedNotification) => void;
  NewUserRegistered: (data: NewUserRegisteredNotification) => void;
}

interface UserApprovedNotification {
  userId: string;
  approvedBy: string;
  approvedAt: string;
  comments?: string;
}

interface DocumentOcrProcessedNotification {
  userId: string;
  documentId: string;
  documentType: string;
  ocrData: {
    extractedText: string;
    confidence: number;
    fields: Record<string, any>;
  };
  processingTime: number;
}

class UserManagementHub {
  private connection: signalR.HubConnection;
  private eventHandlers: Map<string, Function[]> = new Map();

  constructor(private signalRManager: SignalRManager) {}

  async connect(): Promise<void> {
    this.connection = await this.signalRManager.createConnection(
      'UserManagement',
      '/api/v1/users/hub'
    );

    this.setupEventHandlers();
    await this.connection.start();
    console.log('User Management Hub connected');
  }

  private setupEventHandlers(): void {
    this.connection.on('UserApproved', (data: UserApprovedNotification) => {
      this.emit('UserApproved', data);
    });

    this.connection.on('DocumentOcrProcessed', (data: DocumentOcrProcessedNotification) => {
      this.emit('DocumentOcrProcessed', data);
    });

    // Add other event handlers...
  }

  // Join user-specific group for personalized notifications
  async joinUserGroup(userId: string): Promise<void> {
    await this.connection.invoke('JoinUserGroup', userId);
  }

  // Join admin group for administrative notifications
  async joinAdminGroup(): Promise<void> {
    await this.connection.invoke('JoinAdminGroup');
  }

  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event) || [];
    handlers.forEach(handler => handler(data));
  }
}
```

### 2. Order Management Hub

**Connection URL**: `/api/v1/orders/hub`

**Events:**
```typescript
interface OrderManagementEvents {
  RFQPublished: (data: RFQPublishedNotification) => void;
  BidSubmitted: (data: BidSubmittedNotification) => void;
  BidAccepted: (data: BidAcceptedNotification) => void;
  BidRejected: (data: BidRejectedNotification) => void;
  OrderCreated: (data: OrderCreatedNotification) => void;
  OrderStatusChanged: (data: OrderStatusChangedNotification) => void;
  MilestoneCompleted: (data: MilestoneCompletedNotification) => void;
  PaymentStatusChanged: (data: PaymentStatusChangedNotification) => void;
}

interface RFQPublishedNotification {
  rfqId: string;
  rfqNumber: string;
  title: string;
  publishedBy: string;
  publishedAt: string;
  biddingDeadline: string;
  targetCarriers?: string[];
  estimatedValue: number;
}

interface BidSubmittedNotification {
  bidId: string;
  bidNumber: string;
  rfqId: string;
  bidderId: string;
  bidderName: string;
  quotedPrice: number;
  submittedAt: string;
  ranking?: number;
}

class OrderManagementHub {
  private connection: signalR.HubConnection;

  async connect(): Promise<void> {
    this.connection = await this.signalRManager.createConnection(
      'OrderManagement',
      '/api/v1/orders/hub'
    );

    this.setupEventHandlers();
    await this.connection.start();
  }

  private setupEventHandlers(): void {
    this.connection.on('RFQPublished', (data: RFQPublishedNotification) => {
      // Handle new RFQ published
      this.showNotification('New RFQ Published', data.title);
      this.updateRFQList(data);
    });

    this.connection.on('BidSubmitted', (data: BidSubmittedNotification) => {
      // Handle new bid submitted
      this.updateBidCount(data.rfqId);
      this.showBidNotification(data);
    });

    this.connection.on('OrderStatusChanged', (data: OrderStatusChangedNotification) => {
      // Handle order status change
      this.updateOrderStatus(data.orderId, data.newStatus);
      this.showStatusChangeNotification(data);
    });
  }

  // Join RFQ-specific group for bid notifications
  async joinRFQGroup(rfqId: string): Promise<void> {
    await this.connection.invoke('JoinRFQGroup', rfqId);
  }

  // Join carrier group for RFQ notifications
  async joinCarrierGroup(): Promise<void> {
    await this.connection.invoke('JoinCarrierGroup');
  }

  // Join order group for order updates
  async joinOrderGroup(orderId: string): Promise<void> {
    await this.connection.invoke('JoinOrderGroup', orderId);
  }

  private showNotification(title: string, message: string): void {
    // Show browser notification or in-app notification
    if (Notification.permission === 'granted') {
      new Notification(title, { body: message });
    }
  }

  private updateRFQList(data: RFQPublishedNotification): void {
    // Update RFQ list in UI
    window.dispatchEvent(new CustomEvent('rfq-published', { detail: data }));
  }

  private updateBidCount(rfqId: string): void {
    // Update bid count in UI
    window.dispatchEvent(new CustomEvent('bid-count-updated', { detail: { rfqId } }));
  }
}
```

### 3. Trip Management Hub

**Connection URL**: `/api/v1/trips/hub`

**Events:**
```typescript
interface TripManagementEvents {
  TripStarted: (data: TripStartedNotification) => void;
  LocationUpdated: (data: LocationUpdatedNotification) => void;
  StopCompleted: (data: StopCompletedNotification) => void;
  PODSubmitted: (data: PODSubmittedNotification) => void;
  PODApproved: (data: PODApprovedNotification) => void;
  ExceptionReported: (data: ExceptionReportedNotification) => void;
  ExceptionResolved: (data: ExceptionResolvedNotification) => void;
  ETAUpdated: (data: ETAUpdatedNotification) => void;
  TripCompleted: (data: TripCompletedNotification) => void;
}

interface LocationUpdatedNotification {
  tripId: string;
  vehicleId: string;
  location: {
    latitude: number;
    longitude: number;
    speed: number;
    heading: number;
    timestamp: string;
    address?: string;
  };
  routeProgress: {
    completedDistance: number;
    remainingDistance: number;
    progressPercentage: number;
  };
}

interface ExceptionReportedNotification {
  tripId: string;
  exceptionId: string;
  exceptionType: string;
  severity: string;
  title: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  reportedAt: string;
  estimatedDelay: number;
}

class TripManagementHub {
  private connection: signalR.HubConnection;
  private locationUpdateHandlers: Map<string, Function> = new Map();

  async connect(): Promise<void> {
    this.connection = await this.signalRManager.createConnection(
      'TripManagement',
      '/api/v1/trips/hub'
    );

    this.setupEventHandlers();
    await this.connection.start();
  }

  private setupEventHandlers(): void {
    this.connection.on('LocationUpdated', (data: LocationUpdatedNotification) => {
      this.handleLocationUpdate(data);
    });

    this.connection.on('ExceptionReported', (data: ExceptionReportedNotification) => {
      this.handleExceptionReported(data);
    });

    this.connection.on('PODSubmitted', (data: PODSubmittedNotification) => {
      this.handlePODSubmitted(data);
    });
  }

  // Subscribe to location updates for specific trip
  async subscribeToTripLocation(tripId: string, handler: Function): Promise<void> {
    await this.connection.invoke('JoinTripGroup', tripId);
    this.locationUpdateHandlers.set(tripId, handler);
  }

  // Unsubscribe from trip location updates
  async unsubscribeFromTripLocation(tripId: string): Promise<void> {
    await this.connection.invoke('LeaveTripGroup', tripId);
    this.locationUpdateHandlers.delete(tripId);
  }

  // Submit location update (for driver app)
  async submitLocationUpdate(tripId: string, location: LocationUpdate): Promise<void> {
    await this.connection.invoke('UpdateLocation', tripId, location);
  }

  private handleLocationUpdate(data: LocationUpdatedNotification): void {
    const handler = this.locationUpdateHandlers.get(data.tripId);
    if (handler) {
      handler(data);
    }

    // Update map if visible
    window.dispatchEvent(new CustomEvent('trip-location-updated', { detail: data }));
  }

  private handleExceptionReported(data: ExceptionReportedNotification): void {
    // Show critical alert for high severity exceptions
    if (data.severity === 'Critical' || data.severity === 'High') {
      this.showCriticalAlert(data);
    }

    // Update trip status in UI
    window.dispatchEvent(new CustomEvent('trip-exception-reported', { detail: data }));
  }

  private showCriticalAlert(data: ExceptionReportedNotification): void {
    // Show modal or toast notification for critical exceptions
    const alertData = {
      title: `Critical Exception: ${data.title}`,
      message: data.description,
      tripId: data.tripId,
      location: data.location,
      severity: data.severity
    };

    window.dispatchEvent(new CustomEvent('critical-trip-alert', { detail: alertData }));
  }
}
```

## 🔔 Notification Hub

**Connection URL**: `/api/v1/notifications/hub`

```typescript
interface NotificationEvents {
  PersonalNotification: (data: PersonalNotification) => void;
  BroadcastNotification: (data: BroadcastNotification) => void;
  SystemAlert: (data: SystemAlert) => void;
  MaintenanceNotification: (data: MaintenanceNotification) => void;
}

interface PersonalNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'Info' | 'Warning' | 'Error' | 'Success';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  actionUrl?: string;
  actionText?: string;
  timestamp: string;
  expiresAt?: string;
}

class NotificationHub {
  private connection: signalR.HubConnection;
  private notificationQueue: PersonalNotification[] = [];

  async connect(): Promise<void> {
    this.connection = await this.signalRManager.createConnection(
      'Notifications',
      '/api/v1/notifications/hub'
    );

    this.setupEventHandlers();
    await this.connection.start();
  }

  private setupEventHandlers(): void {
    this.connection.on('PersonalNotification', (data: PersonalNotification) => {
      this.handlePersonalNotification(data);
    });

    this.connection.on('SystemAlert', (data: SystemAlert) => {
      this.handleSystemAlert(data);
    });
  }

  private handlePersonalNotification(notification: PersonalNotification): void {
    // Add to notification queue
    this.notificationQueue.push(notification);

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/assets/icons/notification-icon.png',
        badge: '/assets/icons/badge-icon.png',
        tag: notification.id,
        requireInteraction: notification.priority === 'Critical'
      });

      browserNotification.onclick = () => {
        if (notification.actionUrl) {
          window.open(notification.actionUrl, '_blank');
        }
        browserNotification.close();
      };
    }

    // Update notification center
    window.dispatchEvent(new CustomEvent('new-notification', { detail: notification }));
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    await this.connection.invoke('MarkNotificationAsRead', notificationId);
  }

  // Get unread notification count
  async getUnreadCount(): Promise<number> {
    return await this.connection.invoke('GetUnreadNotificationCount');
  }
}
```

## 📊 Analytics Hub

**Connection URL**: `/api/v1/analytics/hub`

```typescript
interface AnalyticsEvents {
  DashboardDataUpdated: (data: DashboardUpdate) => void;
  RealtimeMetrics: (data: RealtimeMetrics) => void;
  AlertTriggered: (data: AnalyticsAlert) => void;
}

interface DashboardUpdate {
  dashboardId: string;
  widgets: WidgetUpdate[];
  timestamp: string;
}

interface WidgetUpdate {
  widgetId: string;
  data: any;
  lastUpdated: string;
}

interface RealtimeMetrics {
  activeTrips: number;
  activeDrivers: number;
  pendingOrders: number;
  systemLoad: number;
  timestamp: string;
}

class AnalyticsHub {
  private connection: signalR.HubConnection;

  async connect(): Promise<void> {
    this.connection = await this.signalRManager.createConnection(
      'Analytics',
      '/api/v1/analytics/hub'
    );

    this.setupEventHandlers();
    await this.connection.start();
  }

  // Subscribe to dashboard updates
  async subscribeToDashboard(dashboardId: string): Promise<void> {
    await this.connection.invoke('SubscribeToDashboard', dashboardId);
  }

  // Subscribe to real-time metrics
  async subscribeToMetrics(): Promise<void> {
    await this.connection.invoke('SubscribeToRealtimeMetrics');
  }

  private setupEventHandlers(): void {
    this.connection.on('DashboardDataUpdated', (data: DashboardUpdate) => {
      // Update dashboard widgets
      window.dispatchEvent(new CustomEvent('dashboard-updated', { detail: data }));
    });

    this.connection.on('RealtimeMetrics', (data: RealtimeMetrics) => {
      // Update real-time metrics display
      window.dispatchEvent(new CustomEvent('metrics-updated', { detail: data }));
    });
  }
}
```

## 🔧 Frontend Integration

### React Hook for SignalR

```typescript
import { useEffect, useState, useCallback } from 'react';

interface UseSignalROptions {
  hubName: string;
  hubUrl: string;
  autoConnect?: boolean;
  dependencies?: any[];
}

export function useSignalR({ hubName, hubUrl, autoConnect = true, dependencies = [] }: UseSignalROptions) {
  const [connection, setConnection] = useState<signalR.HubConnection | null>(null);
  const [connectionState, setConnectionState] = useState<string>('Disconnected');
  const [error, setError] = useState<string | null>(null);

  const connect = useCallback(async () => {
    try {
      const signalRManager = SignalRManager.getInstance();
      const newConnection = await signalRManager.createConnection(hubName, hubUrl);
      
      newConnection.onreconnecting(() => setConnectionState('Reconnecting'));
      newConnection.onreconnected(() => setConnectionState('Connected'));
      newConnection.onclose(() => setConnectionState('Disconnected'));

      await newConnection.start();
      setConnection(newConnection);
      setConnectionState('Connected');
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed');
      setConnectionState('Disconnected');
    }
  }, [hubName, hubUrl]);

  const disconnect = useCallback(async () => {
    if (connection) {
      await connection.stop();
      setConnection(null);
      setConnectionState('Disconnected');
    }
  }, [connection]);

  const invoke = useCallback(async (methodName: string, ...args: any[]) => {
    if (connection && connectionState === 'Connected') {
      return await connection.invoke(methodName, ...args);
    }
    throw new Error('Connection not available');
  }, [connection, connectionState]);

  const on = useCallback((eventName: string, handler: (...args: any[]) => void) => {
    if (connection) {
      connection.on(eventName, handler);
      return () => connection.off(eventName, handler);
    }
    return () => {};
  }, [connection]);

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect, ...dependencies]);

  return {
    connection,
    connectionState,
    error,
    connect,
    disconnect,
    invoke,
    on
  };
}

// Usage example
function TripTrackingComponent({ tripId }: { tripId: string }) {
  const [location, setLocation] = useState<LocationUpdate | null>(null);
  
  const { connection, connectionState, invoke, on } = useSignalR({
    hubName: 'TripManagement',
    hubUrl: '/api/v1/trips/hub',
    dependencies: [tripId]
  });

  useEffect(() => {
    if (connection && connectionState === 'Connected') {
      // Join trip group
      invoke('JoinTripGroup', tripId);

      // Subscribe to location updates
      const unsubscribe = on('LocationUpdated', (data: LocationUpdatedNotification) => {
        if (data.tripId === tripId) {
          setLocation(data.location);
        }
      });

      return () => {
        invoke('LeaveTripGroup', tripId);
        unsubscribe();
      };
    }
  }, [connection, connectionState, tripId, invoke, on]);

  return (
    <div>
      <div>Connection: {connectionState}</div>
      {location && (
        <div>
          <p>Lat: {location.latitude}</p>
          <p>Lng: {location.longitude}</p>
          <p>Speed: {location.speed} km/h</p>
        </div>
      )}
    </div>
  );
}
```

## 🔔 Browser Notifications

### Permission Management

```typescript
class NotificationManager {
  static async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    if (Notification.permission === 'default') {
      return await Notification.requestPermission();
    }

    return Notification.permission;
  }

  static async showNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    const permission = await this.requestPermission();
    
    if (permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/assets/icons/notification-icon.png',
        badge: '/assets/icons/badge-icon.png',
        ...options
      });

      // Auto-close after 5 seconds unless requireInteraction is true
      if (!options.requireInteraction) {
        setTimeout(() => notification.close(), 5000);
      }
    }
  }
}
```

This comprehensive real-time features guide provides everything needed to implement SignalR-based real-time functionality in frontend applications, including connection management, event handling, and browser notifications.
