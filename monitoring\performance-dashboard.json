{"dashboard": {"id": null, "title": "TLI Microservices Performance Dashboard", "tags": ["tli", "microservices", "performance"], "timezone": "browser", "panels": [{"id": 1, "title": "Request Rate", "type": "graph", "targets": [{"expr": "sum(rate(http_requests_total[5m])) by (service)", "legendFormat": "{{service}}"}], "yAxes": [{"label": "Requests/sec"}]}, {"id": 2, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))", "legendFormat": "95th percentile - {{service}}"}, {"expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))", "legendFormat": "50th percentile - {{service}}"}], "yAxes": [{"label": "Seconds"}]}, {"id": 3, "title": "Error Rate", "type": "graph", "targets": [{"expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)", "legendFormat": "Error Rate - {{service}}"}], "yAxes": [{"label": "Error Rate", "max": 1, "min": 0}]}, {"id": 4, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "avg(rate(container_cpu_usage_seconds_total[5m])) by (pod)", "legendFormat": "CPU Usage - {{pod}}"}], "yAxes": [{"label": "CPU Cores"}]}, {"id": 5, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "avg(container_memory_usage_bytes) by (pod) / 1024 / 1024", "legendFormat": "Memory Usage - {{pod}}"}], "yAxes": [{"label": "Memory (MB)"}]}, {"id": 6, "title": "Database Connection Pool", "type": "graph", "targets": [{"expr": "avg(database_connections_active) by (service)", "legendFormat": "Active Connections - {{service}}"}, {"expr": "avg(database_connections_idle) by (service)", "legendFormat": "Idle Connections - {{service}}"}], "yAxes": [{"label": "Connections"}]}, {"id": 7, "title": "<PERSON><PERSON> Hit Rate", "type": "stat", "targets": [{"expr": "avg(cache_hits_total) / (avg(cache_hits_total) + avg(cache_misses_total))", "legendFormat": "<PERSON><PERSON> Hit Rate"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.9}]}}}}, {"id": 8, "title": "Database Query Performance", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(database_query_duration_seconds_bucket[5m])) by (le, query_type))", "legendFormat": "95th percentile - {{query_type}}"}], "yAxes": [{"label": "Seconds"}]}, {"id": 9, "title": "Message Queue Metrics", "type": "graph", "targets": [{"expr": "avg(rabbitmq_queue_messages) by (queue)", "legendFormat": "Queue Depth - {{queue}}"}, {"expr": "rate(rabbitmq_queue_messages_published_total[5m])", "legendFormat": "Messages Published/sec"}, {"expr": "rate(rabbitmq_queue_messages_delivered_total[5m])", "legendFormat": "Messages Delivered/sec"}], "yAxes": [{"label": "Messages"}]}, {"id": 10, "title": "Service Dependencies", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_client_request_duration_seconds_bucket[5m])) by (le, target_service))", "legendFormat": "95th percentile - {{target_service}}"}], "yAxes": [{"label": "Seconds"}]}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}