#!/bin/bash

# TLI Microservices Deployment Script
# This script automates the deployment of TLI microservices platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_TYPE=${1:-docker}  # docker, kubernetes, or production
ENVIRONMENT=${2:-production}  # development, staging, production

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if kubectl is installed (for Kubernetes deployment)
    if [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]] && ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log_warning ".env file not found. Creating from template..."
        cp "$PROJECT_ROOT/deployment-configs/.env.production.template" "$PROJECT_ROOT/.env"
        log_warning "Please update the .env file with your configuration before proceeding."
        exit 1
    fi
    
    log_success "Prerequisites check completed."
}

setup_directories() {
    log_info "Setting up directories..."
    
    # Create necessary directories
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/data/postgres"
    mkdir -p "$PROJECT_ROOT/data/redis"
    mkdir -p "$PROJECT_ROOT/data/rabbitmq"
    mkdir -p "$PROJECT_ROOT/storage"
    mkdir -p "$PROJECT_ROOT/backups"
    
    # Set permissions
    chmod 755 "$PROJECT_ROOT/logs"
    chmod 755 "$PROJECT_ROOT/storage"
    chmod 755 "$PROJECT_ROOT/backups"
    
    log_success "Directories setup completed."
}

build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build all services
    docker-compose -f docker-compose.yml build --parallel
    
    # Tag images for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker tag tli/apigateway:latest tli/apigateway:$ENVIRONMENT
        docker tag tli/identity-service:latest tli/identity-service:$ENVIRONMENT
        docker tag tli/usermanagement-service:latest tli/usermanagement-service:$ENVIRONMENT
        docker tag tli/subscription-service:latest tli/subscription-service:$ENVIRONMENT
        docker tag tli/order-service:latest tli/order-service:$ENVIRONMENT
        docker tag tli/trip-service:latest tli/trip-service:$ENVIRONMENT
        docker tag tli/fleet-service:latest tli/fleet-service:$ENVIRONMENT
        docker tag tli/payment-service:latest tli/payment-service:$ENVIRONMENT
        docker tag tli/communication-service:latest tli/communication-service:$ENVIRONMENT
        docker tag tli/analytics-service:latest tli/analytics-service:$ENVIRONMENT
        docker tag tli/storage-service:latest tli/storage-service:$ENVIRONMENT
        docker tag tli/audit-service:latest tli/audit-service:$ENVIRONMENT
        docker tag tli/monitoring-service:latest tli/monitoring-service:$ENVIRONMENT
    fi
    
    log_success "Docker images built successfully."
}

deploy_docker() {
    log_info "Deploying with Docker Compose..."
    
    cd "$PROJECT_ROOT"
    
    # Use production compose file if environment is production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        COMPOSE_FILE="deployment-configs/docker-compose.production.yml"
    else
        COMPOSE_FILE="docker-compose.yml"
    fi
    
    # Stop existing services
    docker-compose -f "$COMPOSE_FILE" down
    
    # Start infrastructure services first
    log_info "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" up -d postgres redis rabbitmq
    
    # Wait for infrastructure to be ready
    log_info "Waiting for infrastructure services to be ready..."
    sleep 30
    
    # Check if services are healthy
    check_service_health "postgres" 5432
    check_service_health "redis" 6379
    check_service_health "rabbitmq" 5672
    
    # Start application services
    log_info "Starting application services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to start
    sleep 60
    
    # Run database migrations
    run_migrations
    
    # Verify deployment
    verify_deployment
    
    log_success "Docker deployment completed successfully."
}

deploy_kubernetes() {
    log_info "Deploying to Kubernetes..."
    
    cd "$PROJECT_ROOT"
    
    # Apply namespace and RBAC
    kubectl apply -f deployment-configs/kubernetes/tli-namespace.yaml
    
    # Apply secrets (make sure to update with actual values)
    kubectl apply -f deployment-configs/kubernetes/tli-secrets.yaml
    
    # Apply ConfigMaps
    kubectl apply -f deployment-configs/kubernetes/tli-configmap.yaml
    
    # Deploy infrastructure services
    log_info "Deploying infrastructure services..."
    kubectl apply -f deployment-configs/kubernetes/postgres-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/redis-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/rabbitmq-deployment.yaml
    
    # Wait for infrastructure to be ready
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n tli-production
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n tli-production
    kubectl wait --for=condition=available --timeout=300s deployment/rabbitmq -n tli-production
    
    # Deploy application services
    log_info "Deploying application services..."
    kubectl apply -f deployment-configs/kubernetes/identity-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/usermanagement-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/subscription-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/order-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/trip-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/fleet-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/payment-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/communication-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/analytics-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/storage-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/audit-service-deployment.yaml
    kubectl apply -f deployment-configs/kubernetes/monitoring-service-deployment.yaml
    
    # Deploy API Gateway last
    kubectl apply -f deployment-configs/kubernetes/apigateway-deployment.yaml
    
    # Deploy ingress
    kubectl apply -f deployment-configs/kubernetes/tli-ingress.yaml
    
    # Wait for all deployments to be ready
    log_info "Waiting for all services to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment --all -n tli-production
    
    # Run database migrations
    run_k8s_migrations
    
    # Verify deployment
    verify_k8s_deployment
    
    log_success "Kubernetes deployment completed successfully."
}

check_service_health() {
    local service=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "Checking health of $service on port $port..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if nc -z localhost $port; then
            log_success "$service is healthy."
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: $service not ready yet..."
        sleep 10
        ((attempt++))
    done
    
    log_error "$service failed to become healthy after $max_attempts attempts."
    return 1
}

run_migrations() {
    log_info "Running database migrations..."
    
    # List of services that need migrations
    services=(
        "identity-api"
        "usermanagement-api"
        "subscription-api"
        "order-api"
        "trip-api"
        "fleet-api"
        "payment-api"
        "communication-api"
        "analytics-api"
        "storage-api"
        "audit-api"
        "monitoring-api"
    )
    
    for service in "${services[@]}"; do
        log_info "Running migration for $service..."
        docker-compose exec -T "$service" dotnet ef database update || log_warning "Migration failed for $service"
    done
    
    log_success "Database migrations completed."
}

run_k8s_migrations() {
    log_info "Running database migrations in Kubernetes..."
    
    # Get the first pod of each service and run migrations
    services=(
        "identity-service"
        "usermanagement-service"
        "subscription-service"
        "order-service"
        "trip-service"
        "fleet-service"
        "payment-service"
        "communication-service"
        "analytics-service"
        "storage-service"
        "audit-service"
        "monitoring-service"
    )
    
    for service in "${services[@]}"; do
        log_info "Running migration for $service..."
        pod=$(kubectl get pods -n tli-production -l app="$service" -o jsonpath='{.items[0].metadata.name}')
        if [[ -n "$pod" ]]; then
            kubectl exec -n tli-production "$pod" -- dotnet ef database update || log_warning "Migration failed for $service"
        else
            log_warning "No pod found for $service"
        fi
    done
    
    log_success "Database migrations completed."
}

verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if all services are running
    docker-compose ps
    
    # Check health endpoints
    services=(
        "http://localhost:5000/health"  # API Gateway
        "http://localhost:5001/health"  # Identity
        "http://localhost:5002/health"  # User Management
        "http://localhost:5003/health"  # Subscription
        "http://localhost:5004/health"  # Order Management
        "http://localhost:5005/health"  # Trip Management
        "http://localhost:5006/health"  # Fleet Management
        "http://localhost:5007/health"  # Payment
        "http://localhost:5008/health"  # Communication
        "http://localhost:5009/health"  # Analytics
        "http://localhost:5010/health"  # Storage
        "http://localhost:5011/health"  # Audit
        "http://localhost:5012/health"  # Monitoring
    )
    
    for service in "${services[@]}"; do
        if curl -f -s "$service" > /dev/null; then
            log_success "✅ $service - OK"
        else
            log_error "❌ $service - FAILED"
        fi
    done
    
    log_success "Deployment verification completed."
}

verify_k8s_deployment() {
    log_info "Verifying Kubernetes deployment..."
    
    # Check pod status
    kubectl get pods -n tli-production
    
    # Check service status
    kubectl get services -n tli-production
    
    # Check ingress status
    kubectl get ingress -n tli-production
    
    log_success "Kubernetes deployment verification completed."
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        # Start monitoring stack
        docker run -d \
            --name prometheus \
            -p 9090:9090 \
            -v "$PROJECT_ROOT/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml" \
            prom/prometheus
        
        docker run -d \
            --name grafana \
            -p 3000:3000 \
            -e "GF_SECURITY_ADMIN_PASSWORD=admin123" \
            grafana/grafana
    fi
    
    log_success "Monitoring setup completed."
}

cleanup() {
    log_info "Cleaning up..."
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        docker-compose down
        docker system prune -f
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        kubectl delete namespace tli-production
    fi
    
    log_success "Cleanup completed."
}

show_usage() {
    echo "Usage: $0 [deployment_type] [environment]"
    echo ""
    echo "deployment_type:"
    echo "  docker      - Deploy using Docker Compose (default)"
    echo "  kubernetes  - Deploy to Kubernetes cluster"
    echo "  cleanup     - Clean up deployment"
    echo ""
    echo "environment:"
    echo "  development - Development environment"
    echo "  staging     - Staging environment"
    echo "  production  - Production environment (default)"
    echo ""
    echo "Examples:"
    echo "  $0 docker production"
    echo "  $0 kubernetes staging"
    echo "  $0 cleanup"
}

# Main execution
main() {
    log_info "Starting TLI Microservices deployment..."
    log_info "Deployment Type: $DEPLOYMENT_TYPE"
    log_info "Environment: $ENVIRONMENT"
    
    case "$DEPLOYMENT_TYPE" in
        "docker")
            check_prerequisites
            setup_directories
            build_images
            deploy_docker
            setup_monitoring
            ;;
        "kubernetes")
            check_prerequisites
            build_images
            deploy_kubernetes
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
        *)
            log_error "Invalid deployment type: $DEPLOYMENT_TYPE"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "TLI Microservices deployment completed successfully!"
    log_info "Access the API Gateway at: http://localhost:5000"
    log_info "Access Grafana monitoring at: http://localhost:3000 (admin/admin123)"
}

# Run main function
main "$@"
