using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.AddBrokerComment;

public class AddBrokerCommentCommandHandler : IRequestHandler<AddBrokerCommentCommand, Guid>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddBrokerCommentCommandHandler> _logger;

    public AddBrokerCommentCommandHandler(
        IRfqRepository rfqRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<AddBrokerCommentCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Guid> Handle(AddBrokerCommentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Adding broker comment to RFQ {RfqId} by broker {BrokerId}",
            request.RfqId, request.BrokerId);

        try
        {
            // Get the RFQ
            var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
            if (rfq == null)
            {
                _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
                throw new InvalidOperationException($"RFQ {request.RfqId} not found");
            }

            // Validate parent comment if specified
            if (request.ParentCommentId.HasValue)
            {
                var parentComment = rfq.BrokerComments.FirstOrDefault(c => c.Id == request.ParentCommentId.Value);
                if (parentComment == null)
                {
                    _logger.LogWarning("Parent comment {ParentCommentId} not found for RFQ {RfqId}", 
                        request.ParentCommentId, request.RfqId);
                    throw new InvalidOperationException($"Parent comment {request.ParentCommentId} not found");
                }
            }

            // Create broker comment
            var brokerComment = new BrokerComment(
                request.RfqId,
                request.BrokerId,
                request.BrokerName,
                request.Comment,
                request.CommentType,
                request.IsInternal,
                request.IsVisible,
                request.ParentCommentId,
                request.Tags,
                request.Priority,
                request.ExpiresAt,
                request.Metadata);

            // Add comment to RFQ
            rfq.AddBrokerComment(brokerComment);

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var description = request.ParentCommentId.HasValue 
                    ? $"Broker '{request.BrokerName}' replied to comment on RFQ"
                    : $"Broker '{request.BrokerName}' added {request.CommentType.ToString().ToLower()} comment to RFQ";

                var metadata = new Dictionary<string, object>
                {
                    ["commentType"] = request.CommentType.ToString(),
                    ["isInternal"] = request.IsInternal,
                    ["priority"] = request.Priority
                };

                if (request.ParentCommentId.HasValue)
                    metadata["parentCommentId"] = request.ParentCommentId.Value;

                if (!string.IsNullOrEmpty(request.Tags))
                    metadata["tags"] = request.Tags;

                var timelineEvent = RfqTimeline.CreateEvent(
                    request.RfqId,
                    RfqTimelineEventType.BrokerCommentAdded,
                    description,
                    request.BrokerId,
                    request.BrokerName,
                    "Broker",
                    System.Text.Json.JsonSerializer.Serialize(metadata));

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _rfqRepository.Update(rfq);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully added broker comment {CommentId} to RFQ {RfqId}", 
                brokerComment.Id, request.RfqId);
            
            return brokerComment.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding broker comment to RFQ {RfqId}", request.RfqId);
            throw;
        }
    }
}
