using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for advanced message scheduling with optimization
/// </summary>
public interface IAdvancedSchedulingService
{
    /// <summary>
    /// Create a new message schedule
    /// </summary>
    Task<MessageSchedule> CreateScheduleAsync(
        string name,
        string description,
        MessageType messageType,
        ScheduleType scheduleType,
        MessageContent content,
        Guid createdByUserId,
        DateTime? scheduledAt = null,
        RecurrencePattern? recurrencePattern = null,
        List<SchedulingRule>? rules = null,
        DeliveryWindow? deliveryWindow = null,
        OptimizationSettings? optimizationSettings = null,
        List<string>? targetUserIds = null,
        List<string>? targetSegments = null,
        string? templateId = null,
        Dictionary<string, object>? templateParameters = null,
        NotificationChannel? preferredChannel = null,
        Priority priority = Priority.Normal,
        List<string>? timeZones = null,
        int maxExecutions = int.MaxValue,
        DateTime? expiresAt = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedule by ID
    /// </summary>
    Task<MessageSchedule?> GetScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update schedule
    /// </summary>
    Task<MessageSchedule> UpdateScheduleAsync(
        Guid scheduleId,
        MessageSchedule updatedSchedule,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete schedule
    /// </summary>
    Task DeleteScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all schedules
    /// </summary>
    Task<List<MessageSchedule>> GetAllSchedulesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedules by status
    /// </summary>
    Task<List<MessageSchedule>> GetSchedulesByStatusAsync(
        ScheduleStatus status,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedules ready for execution
    /// </summary>
    Task<List<MessageSchedule>> GetSchedulesReadyForExecutionAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Activate schedule
    /// </summary>
    Task<MessageSchedule> ActivateScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Pause schedule
    /// </summary>
    Task<MessageSchedule> PauseScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel schedule
    /// </summary>
    Task<MessageSchedule> CancelScheduleAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute schedule immediately
    /// </summary>
    Task<ScheduleExecutionResult> ExecuteScheduleAsync(
        Guid scheduleId,
        bool forceExecution = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get optimal delivery time for user
    /// </summary>
    Task<DateTime?> GetOptimalDeliveryTimeAsync(
        Guid scheduleId,
        string userId,
        string timeZone,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedule analytics
    /// </summary>
    Task<ScheduleAnalytics> GetScheduleAnalyticsAsync(
        Guid scheduleId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get optimization recommendations
    /// </summary>
    Task<List<OptimizationRecommendation>> GetOptimizationRecommendationsAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for user behavior analytics
/// </summary>
public interface IUserBehaviorAnalyticsService
{
    /// <summary>
    /// Get user engagement patterns
    /// </summary>
    Task<UserEngagementPattern> GetUserEngagementPatternAsync(
        string userId,
        TimeSpan lookbackPeriod,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get optimal send time for user
    /// </summary>
    Task<DateTime?> GetOptimalSendTimeAsync(
        string userId,
        DateTime baseTime,
        string timeZone,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get preferred channel for user
    /// </summary>
    Task<NotificationChannel?> GetPreferredChannelAsync(
        string userId,
        MessageType messageType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user engagement data
    /// </summary>
    Task UpdateUserEngagementAsync(
        string userId,
        NotificationChannel channel,
        DateTime sentAt,
        DateTime? openedAt,
        DateTime? clickedAt,
        bool wasDelivered,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user activity hours
    /// </summary>
    Task<List<TimeSpan>> GetUserActiveHoursAsync(
        string userId,
        string timeZone,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Predict user response probability
    /// </summary>
    Task<decimal> PredictResponseProbabilityAsync(
        string userId,
        MessageType messageType,
        NotificationChannel channel,
        DateTime proposedSendTime,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for scheduling optimization algorithms
/// </summary>
public interface ISchedulingOptimizationService
{
    /// <summary>
    /// Optimize delivery times for schedule
    /// </summary>
    Task<List<OptimizedDelivery>> OptimizeDeliveryTimesAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimize channel selection
    /// </summary>
    Task<Dictionary<string, NotificationChannel>> OptimizeChannelSelectionAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate frequency optimization
    /// </summary>
    Task<FrequencyOptimizationResult> OptimizeFrequencyAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate A/B test variants for schedule
    /// </summary>
    Task<List<ScheduleVariant>> GenerateABTestVariantsAsync(
        MessageSchedule schedule,
        int variantCount = 2,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyze schedule performance
    /// </summary>
    Task<SchedulePerformanceAnalysis> AnalyzeSchedulePerformanceAsync(
        Guid scheduleId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for message schedules
/// </summary>
public interface IMessageScheduleRepository
{
    /// <summary>
    /// Add schedule
    /// </summary>
    Task<MessageSchedule> AddAsync(
        MessageSchedule schedule,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update schedule
    /// </summary>
    Task<MessageSchedule> UpdateAsync(
        MessageSchedule schedule,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedule by ID
    /// </summary>
    Task<MessageSchedule?> GetByIdAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all schedules
    /// </summary>
    Task<List<MessageSchedule>> GetAllAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedules by status
    /// </summary>
    Task<List<MessageSchedule>> GetByStatusAsync(
        ScheduleStatus status,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedules ready for execution
    /// </summary>
    Task<List<MessageSchedule>> GetReadyForExecutionAsync(
        DateTime currentTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get schedules by user
    /// </summary>
    Task<List<MessageSchedule>> GetByUserAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete schedule
    /// </summary>
    Task DeleteAsync(
        Guid scheduleId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if schedule name exists
    /// </summary>
    Task<bool> ExistsAsync(
        string name,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Schedule execution result
/// </summary>
public class ScheduleExecutionResult
{
    public bool IsSuccess { get; set; }
    public int MessagesScheduled { get; set; }
    public int MessagesSent { get; set; }
    public int MessagesFailed { get; set; }
    public List<string> Errors { get; set; } = new();
    public Dictionary<string, object> ExecutionMetadata { get; set; } = new();
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    public static ScheduleExecutionResult Success(int messagesScheduled, int messagesSent)
    {
        return new ScheduleExecutionResult
        {
            IsSuccess = true,
            MessagesScheduled = messagesScheduled,
            MessagesSent = messagesSent
        };
    }

    public static ScheduleExecutionResult Failure(string error)
    {
        return new ScheduleExecutionResult
        {
            IsSuccess = false,
            Errors = new List<string> { error }
        };
    }
}

/// <summary>
/// Schedule analytics
/// </summary>
public class ScheduleAnalytics
{
    public Guid ScheduleId { get; set; }
    public string ScheduleName { get; set; } = string.Empty;
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal AverageEngagementRate { get; set; }
    public decimal OptimizationScore { get; set; }
    public Dictionary<string, int> ChannelPerformance { get; set; } = new();
    public Dictionary<string, decimal> TimeSlotPerformance { get; set; } = new();
    public List<OptimizationRecommendation> Recommendations { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Optimization recommendation
/// </summary>
public class OptimizationRecommendation
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string Category { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// User engagement pattern
/// </summary>
public class UserEngagementPattern
{
    public string UserId { get; set; } = string.Empty;
    public List<TimeSpan> ActiveHours { get; set; } = new();
    public List<DayOfWeek> ActiveDays { get; set; } = new();
    public Dictionary<NotificationChannel, decimal> ChannelPreferences { get; set; } = new();
    public Dictionary<MessageType, decimal> MessageTypeEngagement { get; set; } = new();
    public decimal AverageResponseTime { get; set; }
    public decimal EngagementScore { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Optimized delivery
/// </summary>
public class OptimizedDelivery
{
    public string UserId { get; set; } = string.Empty;
    public DateTime OptimalTime { get; set; }
    public NotificationChannel RecommendedChannel { get; set; }
    public decimal ConfidenceScore { get; set; }
    public string Reasoning { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Frequency optimization result
/// </summary>
public class FrequencyOptimizationResult
{
    public TimeSpan RecommendedInterval { get; set; }
    public int MaxDailyMessages { get; set; }
    public int MaxWeeklyMessages { get; set; }
    public Dictionary<string, int> UserSpecificLimits { get; set; } = new();
    public decimal OptimizationScore { get; set; }
    public string Reasoning { get; set; } = string.Empty;
}

/// <summary>
/// Schedule variant for A/B testing
/// </summary>
public class ScheduleVariant
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DeliveryWindow DeliveryWindow { get; set; } = DeliveryWindow.Default();
    public OptimizationSettings OptimizationSettings { get; set; } = OptimizationSettings.Default();
    public NotificationChannel? PreferredChannel { get; set; }
    public MessageContent? AlternativeContent { get; set; }
    public decimal TrafficAllocation { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Schedule performance analysis
/// </summary>
public class SchedulePerformanceAnalysis
{
    public Guid ScheduleId { get; set; }
    public decimal DeliveryRate { get; set; }
    public decimal OpenRate { get; set; }
    public decimal ClickRate { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal UnsubscribeRate { get; set; }
    public decimal BounceRate { get; set; }
    public Dictionary<string, decimal> ChannelPerformance { get; set; } = new();
    public Dictionary<string, decimal> TimeSlotPerformance { get; set; } = new();
    public Dictionary<string, decimal> SegmentPerformance { get; set; } = new();
    public List<PerformanceTrend> Trends { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Performance trend
/// </summary>
public class PerformanceTrend
{
    public string Metric { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // "improving", "declining", "stable"
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

/// <summary>
/// Schedule analytics
/// </summary>
public class ScheduleAnalytics
{
    public Guid ScheduleId { get; set; }
    public string ScheduleName { get; set; } = string.Empty;
    public int TotalExecutions { get; set; }
    public int SuccessfulExecutions { get; set; }
    public int FailedExecutions { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal AverageEngagementRate { get; set; }
    public decimal OptimizationScore { get; set; }
    public Dictionary<string, int> ChannelPerformance { get; set; } = new();
    public Dictionary<string, decimal> TimeSlotPerformance { get; set; } = new();
    public List<OptimizationRecommendation> Recommendations { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Optimization recommendation
/// </summary>
public class OptimizationRecommendation
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string Category { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// User engagement pattern
/// </summary>
public class UserEngagementPattern
{
    public string UserId { get; set; } = string.Empty;
    public List<TimeSpan> ActiveHours { get; set; } = new();
    public List<DayOfWeek> ActiveDays { get; set; } = new();
    public Dictionary<NotificationChannel, decimal> ChannelPreferences { get; set; } = new();
    public Dictionary<MessageType, decimal> MessageTypeEngagement { get; set; } = new();
    public decimal AverageResponseTime { get; set; }
    public decimal EngagementScore { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Optimized delivery
/// </summary>
public class OptimizedDelivery
{
    public string UserId { get; set; } = string.Empty;
    public DateTime OptimalTime { get; set; }
    public NotificationChannel RecommendedChannel { get; set; }
    public decimal ConfidenceScore { get; set; }
    public string Reasoning { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Frequency optimization result
/// </summary>
public class FrequencyOptimizationResult
{
    public TimeSpan RecommendedInterval { get; set; }
    public int MaxDailyMessages { get; set; }
    public int MaxWeeklyMessages { get; set; }
    public Dictionary<string, int> UserSpecificLimits { get; set; } = new();
    public decimal OptimizationScore { get; set; }
    public string Reasoning { get; set; } = string.Empty;
}

/// <summary>
/// Schedule variant for A/B testing
/// </summary>
public class ScheduleVariant
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DeliveryWindow DeliveryWindow { get; set; } = DeliveryWindow.Default();
    public OptimizationSettings OptimizationSettings { get; set; } = OptimizationSettings.Default();
    public NotificationChannel? PreferredChannel { get; set; }
    public MessageContent? AlternativeContent { get; set; }
    public decimal TrafficAllocation { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Schedule performance analysis
/// </summary>
public class SchedulePerformanceAnalysis
{
    public Guid ScheduleId { get; set; }
    public decimal DeliveryRate { get; set; }
    public decimal OpenRate { get; set; }
    public decimal ClickRate { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal UnsubscribeRate { get; set; }
    public decimal BounceRate { get; set; }
    public Dictionary<string, decimal> ChannelPerformance { get; set; } = new();
    public Dictionary<string, decimal> TimeSlotPerformance { get; set; } = new();
    public Dictionary<string, decimal> SegmentPerformance { get; set; } = new();
    public List<PerformanceTrend> Trends { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Performance trend
/// </summary>
public class PerformanceTrend
{
    public string Metric { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // "improving", "declining", "stable"
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}
