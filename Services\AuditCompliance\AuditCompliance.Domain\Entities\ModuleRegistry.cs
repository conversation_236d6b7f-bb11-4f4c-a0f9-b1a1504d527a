using Shared.Domain.Common;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities
{
    /// <summary>
    /// Module registry entity for tracking microservices and their audit capabilities
    /// </summary>
    public class ModuleRegistry : AggregateRoot
    {
        public string ModuleName { get; private set; }
        public string DisplayName { get; private set; }
        public string Description { get; private set; }
        public string Version { get; private set; }
        public bool IsActive { get; private set; }
        public List<string> SupportedEntityTypes { get; private set; }
        public List<string> SupportedActions { get; private set; }
        public List<string> RequiredRoles { get; private set; }
        public Dictionary<string, object> Configuration { get; private set; }
        public DateTime RegisteredAt { get; private set; }
        public DateTime? LastUpdated { get; private set; }
        public Guid RegisteredBy { get; private set; }
        public string? HealthCheckEndpoint { get; private set; }
        public DateTime? LastHealthCheck { get; private set; }
        public bool IsHealthy { get; private set; }
        public string? LastHealthCheckError { get; private set; }

        private ModuleRegistry()
        {
            ModuleName = string.Empty;
            DisplayName = string.Empty;
            Description = string.Empty;
            Version = string.Empty;
            SupportedEntityTypes = new List<string>();
            SupportedActions = new List<string>();
            RequiredRoles = new List<string>();
            Configuration = new Dictionary<string, object>();
        }

        public ModuleRegistry(
            string moduleName,
            string displayName,
            string description,
            string version,
            List<string> supportedEntityTypes,
            List<string> supportedActions,
            List<string> requiredRoles,
            Dictionary<string, object> configuration,
            Guid registeredBy,
            string? healthCheckEndpoint = null)
        {
            if (string.IsNullOrWhiteSpace(moduleName))
                throw new ArgumentException("Module name cannot be empty", nameof(moduleName));
            if (string.IsNullOrWhiteSpace(displayName))
                throw new ArgumentException("Display name cannot be empty", nameof(displayName));
            if (string.IsNullOrWhiteSpace(version))
                throw new ArgumentException("Version cannot be empty", nameof(version));

            ModuleName = moduleName;
            DisplayName = displayName;
            Description = description ?? string.Empty;
            Version = version;
            IsActive = true;
            SupportedEntityTypes = supportedEntityTypes ?? new List<string>();
            SupportedActions = supportedActions ?? new List<string>();
            RequiredRoles = requiredRoles ?? new List<string>();
            Configuration = configuration ?? new Dictionary<string, object>();
            RegisteredAt = DateTime.UtcNow;
            RegisteredBy = registeredBy;
            HealthCheckEndpoint = healthCheckEndpoint;
            IsHealthy = true;

            // Add domain event
            AddDomainEvent(new ModuleRegisteredEvent(this));
        }

        public void UpdateModule(
            string displayName,
            string description,
            string version,
            List<string> supportedEntityTypes,
            List<string> supportedActions,
            List<string> requiredRoles,
            Dictionary<string, object> configuration,
            string? healthCheckEndpoint = null)
        {
            if (string.IsNullOrWhiteSpace(displayName))
                throw new ArgumentException("Display name cannot be empty", nameof(displayName));
            if (string.IsNullOrWhiteSpace(version))
                throw new ArgumentException("Version cannot be empty", nameof(version));

            DisplayName = displayName;
            Description = description ?? string.Empty;
            Version = version;
            SupportedEntityTypes = supportedEntityTypes ?? new List<string>();
            SupportedActions = supportedActions ?? new List<string>();
            RequiredRoles = requiredRoles ?? new List<string>();
            Configuration = configuration ?? new Dictionary<string, object>();
            HealthCheckEndpoint = healthCheckEndpoint;
            LastUpdated = DateTime.UtcNow;

            // Add domain event
            AddDomainEvent(new ModuleUpdatedEvent(this));
        }

        public void Activate()
        {
            if (!IsActive)
            {
                IsActive = true;
                LastUpdated = DateTime.UtcNow;
                AddDomainEvent(new ModuleActivatedEvent(this));
            }
        }

        public void Deactivate()
        {
            if (IsActive)
            {
                IsActive = false;
                LastUpdated = DateTime.UtcNow;
                AddDomainEvent(new ModuleDeactivatedEvent(this));
            }
        }

        public void UpdateHealthStatus(bool isHealthy, string? errorMessage = null)
        {
            var previousHealth = IsHealthy;
            IsHealthy = isHealthy;
            LastHealthCheck = DateTime.UtcNow;
            LastHealthCheckError = errorMessage;

            if (previousHealth != isHealthy)
            {
                AddDomainEvent(new ModuleHealthStatusChangedEvent(this, previousHealth, isHealthy));
            }
        }

        public bool SupportsEntityType(string entityType)
        {
            return SupportedEntityTypes.Contains(entityType, StringComparer.OrdinalIgnoreCase);
        }

        public bool SupportsAction(string action)
        {
            return SupportedActions.Contains(action, StringComparer.OrdinalIgnoreCase);
        }

        public bool RequiresRole(string role)
        {
            return RequiredRoles.Contains(role, StringComparer.OrdinalIgnoreCase);
        }

        public bool HasRequiredRole(List<string> userRoles)
        {
            if (!RequiredRoles.Any())
                return true; // No specific roles required

            return RequiredRoles.Any(requiredRole =>
                userRoles.Contains(requiredRole, StringComparer.OrdinalIgnoreCase));
        }

        public T? GetConfigurationValue<T>(string key)
        {
            if (Configuration.TryGetValue(key, out var value))
            {
                try
                {
                    return (T?)value;
                }
                catch
                {
                    return default(T);
                }
            }
            return default(T);
        }

        public void SetConfigurationValue(string key, object value)
        {
            Configuration[key] = value;
            LastUpdated = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Module access control entity for user-specific module permissions
    /// </summary>
    public class ModuleAccessControl : AggregateRoot
    {
        public Guid UserId { get; private set; }
        public string ModuleName { get; private set; }
        public List<string> AllowedActions { get; private set; }
        public List<string> DeniedActions { get; private set; }
        public bool HasFullAccess { get; private set; }
        public DateTime GrantedAt { get; private set; }
        public Guid GrantedBy { get; private set; }
        public DateTime? ExpiresAt { get; private set; }
        public bool IsActive { get; private set; }
        public string? Reason { get; private set; }

        private ModuleAccessControl()
        {
            ModuleName = string.Empty;
            AllowedActions = new List<string>();
            DeniedActions = new List<string>();
        }

        public ModuleAccessControl(
            Guid userId,
            string moduleName,
            List<string> allowedActions,
            List<string> deniedActions,
            bool hasFullAccess,
            Guid grantedBy,
            DateTime? expiresAt = null,
            string? reason = null)
        {
            if (string.IsNullOrWhiteSpace(moduleName))
                throw new ArgumentException("Module name cannot be empty", nameof(moduleName));

            UserId = userId;
            ModuleName = moduleName;
            AllowedActions = allowedActions ?? new List<string>();
            DeniedActions = deniedActions ?? new List<string>();
            HasFullAccess = hasFullAccess;
            GrantedAt = DateTime.UtcNow;
            GrantedBy = grantedBy;
            ExpiresAt = expiresAt;
            IsActive = true;
            Reason = reason;

            // Add domain event
            AddDomainEvent(new ModuleAccessGrantedEvent(this));
        }

        public bool HasAccessToAction(string action)
        {
            if (!IsActive || (ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow))
                return false;

            if (HasFullAccess)
                return !DeniedActions.Contains(action, StringComparer.OrdinalIgnoreCase);

            return AllowedActions.Contains(action, StringComparer.OrdinalIgnoreCase) &&
                   !DeniedActions.Contains(action, StringComparer.OrdinalIgnoreCase);
        }

        public void GrantAction(string action)
        {
            if (!AllowedActions.Contains(action, StringComparer.OrdinalIgnoreCase))
            {
                AllowedActions.Add(action);
                AddDomainEvent(new ModuleActionAccessGrantedEvent(this, action));
            }
        }

        public void DenyAction(string action)
        {
            if (!DeniedActions.Contains(action, StringComparer.OrdinalIgnoreCase))
            {
                DeniedActions.Add(action);
                AddDomainEvent(new ModuleActionAccessDeniedEvent(this, action));
            }
        }

        public void RevokeAccess(Guid revokedBy, string? reason = null)
        {
            IsActive = false;
            Reason = reason;
            AddDomainEvent(new ModuleAccessRevokedEvent(this, revokedBy, reason));
        }

        public void ExtendAccess(DateTime newExpiryDate)
        {
            ExpiresAt = newExpiryDate;
            AddDomainEvent(new ModuleAccessExtendedEvent(this, newExpiryDate));
        }
    }
}



