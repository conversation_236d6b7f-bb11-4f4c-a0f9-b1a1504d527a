using MediatR;

namespace TripManagement.Application.Commands.CollectDigitalSignature;

public record CollectDigitalSignatureCommand : IRequest<bool>
{
    public Guid TripStopId { get; init; }
    public Guid DriverId { get; init; }
    public string RecipientName { get; init; } = string.Empty;
    public string SignatureData { get; init; } = string.Empty; // Base64 encoded signature
    public string? Notes { get; init; }
    public DateTime? DeliveredAt { get; init; }
}
