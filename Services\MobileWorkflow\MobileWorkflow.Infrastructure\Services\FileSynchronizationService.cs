using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Security.Cryptography;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IFileSynchronizationService
{
    Task<SyncFileDto> CreateSyncFileAsync(CreateSyncFileRequest request, CancellationToken cancellationToken = default);
    Task<SyncFileDto?> GetSyncFileAsync(Guid fileId, CancellationToken cancellationToken = default);
    Task<List<SyncFileDto>> GetUserFilesAsync(Guid userId, string? deviceId = null, string? status = null, CancellationToken cancellationToken = default);
    Task<SyncFileDto> UpdateSyncFileAsync(UpdateSyncFileRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteSyncFileAsync(Guid fileId, string deviceId, CancellationToken cancellationToken = default);
    Task<FileSyncOperationDto> StartSyncOperationAsync(StartSyncOperationRequest request, CancellationToken cancellationToken = default);
    Task<bool> UpdateSyncProgressAsync(Guid operationId, UpdateSyncProgressRequest request, CancellationToken cancellationToken = default);
    Task<bool> CompleteSyncOperationAsync(Guid operationId, CancellationToken cancellationToken = default);
    Task<bool> FailSyncOperationAsync(Guid operationId, string errorMessage, string? errorCode = null, CancellationToken cancellationToken = default);
    Task<List<SyncFileDto>> GetConflictedFilesAsync(Guid? userId = null, CancellationToken cancellationToken = default);
    Task<bool> ResolveFileConflictAsync(ResolveFileConflictRequest request, CancellationToken cancellationToken = default);
    Task<List<FileSyncOperationDto>> GetSyncOperationsAsync(Guid? fileId = null, string? status = null, CancellationToken cancellationToken = default);
    Task<FileSyncAnalyticsDto> GetSyncAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<FileVersionDto>> GetFileVersionsAsync(Guid fileId, CancellationToken cancellationToken = default);
    Task<bool> RestoreFileVersionAsync(Guid fileId, int versionNumber, string deviceId, CancellationToken cancellationToken = default);
    Task ProcessPendingSyncOperationsAsync(CancellationToken cancellationToken = default);
    Task CleanupOldFileVersionsAsync(CancellationToken cancellationToken = default);
}

public class FileSynchronizationService : IFileSynchronizationService
{
    private readonly ISyncFileRepository _syncFileRepository;
    private readonly IFileVersionRepository _versionRepository;
    private readonly IFileSyncOperationRepository _operationRepository;
    private readonly IFileStorageService _storageService;
    private readonly IFileConflictResolver _conflictResolver;
    private readonly ILogger<FileSynchronizationService> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public FileSynchronizationService(
        ISyncFileRepository syncFileRepository,
        IFileVersionRepository versionRepository,
        IFileSyncOperationRepository operationRepository,
        IFileStorageService storageService,
        IFileConflictResolver conflictResolver,
        ILogger<FileSynchronizationService> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _syncFileRepository = syncFileRepository;
        _versionRepository = versionRepository;
        _operationRepository = operationRepository;
        _storageService = storageService;
        _conflictResolver = conflictResolver;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
    }

    public async Task<SyncFileDto> CreateSyncFileAsync(CreateSyncFileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating sync file {FileName} for user {UserId} on device {DeviceId}",
                request.FileName, request.UserId, request.DeviceId);

            // Check if file already exists
            var existingFile = await _syncFileRepository.GetByPathAndUserAsync(request.FilePath, request.UserId, cancellationToken);
            if (existingFile != null && !existingFile.IsDeleted)
            {
                // Update existing file
                var updateRequest = new UpdateSyncFileRequest
                {
                    FileId = existingFile.Id,
                    FileHash = request.FileHash,
                    FileSize = request.FileSize,
                    DeviceId = request.DeviceId,
                    FileMetadata = request.FileMetadata
                };
                return await UpdateSyncFileAsync(updateRequest, cancellationToken);
            }

            var syncFile = new SyncFile(
                request.FileName,
                request.FilePath,
                request.FileHash,
                request.FileSize,
                request.MimeType,
                request.UserId,
                request.DeviceId);

            if (request.FileMetadata != null)
            {
                foreach (var metadata in request.FileMetadata)
                {
                    syncFile.AddFileMetadata(metadata.Key, metadata.Value);
                }
            }

            _syncFileRepository.Add(syncFile);
            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Sync file {FileId} created successfully", syncFile.Id);

            return MapToSyncFileDto(syncFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating sync file {FileName}", request.FileName);
            throw;
        }
    }

    public async Task<SyncFileDto?> GetSyncFileAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        try
        {
            var syncFile = await _syncFileRepository.GetByIdAsync(fileId, cancellationToken);
            if (syncFile == null)
            {
                return null;
            }

            return MapToSyncFileDto(syncFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync file {FileId}", fileId);
            return null;
        }
    }

    public async Task<List<SyncFileDto>> GetUserFilesAsync(Guid userId, string? deviceId = null, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var syncFiles = await _syncFileRepository.GetByUserIdAsync(userId, deviceId, status, cancellationToken);
            return syncFiles.Select(MapToSyncFileDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user files for user {UserId}", userId);
            return new List<SyncFileDto>();
        }
    }

    public async Task<SyncFileDto> UpdateSyncFileAsync(UpdateSyncFileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating sync file {FileId} from device {DeviceId}", request.FileId, request.DeviceId);

            var syncFile = await _syncFileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (syncFile == null)
            {
                throw new InvalidOperationException("Sync file not found");
            }

            // Check for conflicts
            var hasConflict = await CheckForConflictAsync(syncFile, request.FileHash, request.DeviceId, cancellationToken);
            if (hasConflict)
            {
                var conflictReason = await _conflictResolver.AnalyzeConflictAsync(syncFile, request, cancellationToken);
                syncFile.MarkAsConflicted(conflictReason);

                _logger.LogWarning("File conflict detected for {FileId}: {ConflictReason}", request.FileId, conflictReason);
            }
            else
            {
                syncFile.UpdateFile(request.FileHash, request.FileSize, request.FileMetadata);
            }

            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Sync file {FileId} updated successfully", request.FileId);

            return MapToSyncFileDto(syncFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating sync file {FileId}", request.FileId);
            throw;
        }
    }

    public async Task<bool> DeleteSyncFileAsync(Guid fileId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var syncFile = await _syncFileRepository.GetByIdAsync(fileId, cancellationToken);
            if (syncFile == null)
            {
                return false;
            }

            syncFile.MarkAsDeleted();
            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Sync file {FileId} marked as deleted from device {DeviceId}", fileId, deviceId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting sync file {FileId}", fileId);
            return false;
        }
    }

    public async Task<FileSyncOperationDto> StartSyncOperationAsync(StartSyncOperationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting sync operation {OperationType} for file {FileId}",
                request.OperationType, request.FileId);

            var syncFile = await _syncFileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (syncFile == null)
            {
                throw new InvalidOperationException("Sync file not found");
            }

            var operation = new FileSyncOperation(
                request.FileId,
                request.OperationType,
                request.SourceDevice,
                request.TotalBytes,
                request.TargetDevice);

            if (request.OperationMetadata != null)
            {
                foreach (var metadata in request.OperationMetadata)
                {
                    operation.AddMetadata(metadata.Key, metadata.Value);
                }
            }

            _operationRepository.Add(operation);

            // Update file status
            syncFile.MarkAsSyncing();

            await _operationRepository.SaveChangesAsync(cancellationToken);
            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            // Start the operation
            operation.Start();
            await _operationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Sync operation {OperationId} started successfully", operation.Id);

            return MapToFileSyncOperationDto(operation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting sync operation for file {FileId}", request.FileId);
            throw;
        }
    }

    public async Task<bool> UpdateSyncProgressAsync(Guid operationId, UpdateSyncProgressRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var operation = await _operationRepository.GetByIdAsync(operationId, cancellationToken);
            if (operation == null)
            {
                return false;
            }

            operation.UpdateProgress(request.BytesTransferred, request.TransferSpeed);

            if (request.PerformanceMetrics != null)
            {
                foreach (var metric in request.PerformanceMetrics)
                {
                    operation.AddPerformanceMetric(metric.Key, metric.Value);
                }
            }

            await _operationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Sync operation {OperationId} progress updated: {Progress}%",
                operationId, operation.GetProgressPercentage());

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating sync progress for operation {OperationId}", operationId);
            return false;
        }
    }

    public async Task<bool> CompleteSyncOperationAsync(Guid operationId, CancellationToken cancellationToken = default)
    {
        try
        {
            var operation = await _operationRepository.GetByIdAsync(operationId, cancellationToken);
            if (operation == null)
            {
                return false;
            }

            operation.Complete();

            // Update file status
            var syncFile = await _syncFileRepository.GetByIdAsync(operation.SyncFileId, cancellationToken);
            if (syncFile != null)
            {
                syncFile.MarkAsSynced();
                await _syncFileRepository.SaveChangesAsync(cancellationToken);
            }

            await _operationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Sync operation {OperationId} completed successfully", operationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing sync operation {OperationId}", operationId);
            return false;
        }
    }

    public async Task<bool> FailSyncOperationAsync(Guid operationId, string errorMessage, string? errorCode = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var operation = await _operationRepository.GetByIdAsync(operationId, cancellationToken);
            if (operation == null)
            {
                return false;
            }

            operation.Fail(errorMessage, errorCode);

            // Update file status
            var syncFile = await _syncFileRepository.GetByIdAsync(operation.SyncFileId, cancellationToken);
            if (syncFile != null)
            {
                syncFile.MarkAsFailed(errorMessage);
                await _syncFileRepository.SaveChangesAsync(cancellationToken);
            }

            await _operationRepository.SaveChangesAsync(cancellationToken);

            _logger.LogWarning("Sync operation {OperationId} failed: {ErrorMessage}", operationId, errorMessage);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error failing sync operation {OperationId}", operationId);
            return false;
        }
    }

    public async Task<List<SyncFileDto>> GetConflictedFilesAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var conflictedFiles = await _syncFileRepository.GetConflictedFilesAsync(userId, cancellationToken);
            return conflictedFiles.Select(MapToSyncFileDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conflicted files");
            return new List<SyncFileDto>();
        }
    }

    public async Task<bool> ResolveFileConflictAsync(ResolveFileConflictRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var syncFile = await _syncFileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (syncFile == null || !syncFile.IsInConflict())
            {
                return false;
            }

            var resolution = await _conflictResolver.ResolveConflictAsync(syncFile, request, cancellationToken);
            syncFile.ResolveConflict(resolution.ResolutionType, request.ResolvedBy);

            if (resolution.RequiresFileUpdate)
            {
                syncFile.UpdateFile(resolution.ResolvedFileHash!, resolution.ResolvedFileSize, resolution.ResolvedMetadata);
            }

            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("File conflict resolved for {FileId} using {ResolutionType}",
                request.FileId, resolution.ResolutionType);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving file conflict for {FileId}", request.FileId);
            return false;
        }
    }

    public async Task<List<FileSyncOperationDto>> GetSyncOperationsAsync(Guid? fileId = null, string? status = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var operations = await _operationRepository.GetOperationsAsync(fileId, status, cancellationToken);
            return operations.Select(MapToFileSyncOperationDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync operations");
            return new List<FileSyncOperationDto>();
        }
    }

    public async Task<FileSyncAnalyticsDto> GetSyncAnalyticsAsync(Guid? userId = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var syncFiles = await _syncFileRepository.GetFilesAsync(userId, from, cancellationToken);
            var operations = await _operationRepository.GetOperationsAsync(null, null, from, cancellationToken);

            var analytics = new FileSyncAnalyticsDto
            {
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalFiles = syncFiles.Count,
                SyncedFiles = syncFiles.Count(f => f.IsSynced()),
                PendingFiles = syncFiles.Count(f => f.NeedsSync()),
                ConflictedFiles = syncFiles.Count(f => f.IsInConflict()),
                DeletedFiles = syncFiles.Count(f => f.IsDeleted),
                TotalOperations = operations.Count,
                CompletedOperations = operations.Count(o => o.IsCompleted()),
                FailedOperations = operations.Count(o => o.IsFailed()),
                InProgressOperations = operations.Count(o => o.IsInProgress()),
                TotalBytesTransferred = operations.Where(o => o.IsCompleted()).Sum(o => o.BytesTransferred),
                AverageTransferSpeed = CalculateAverageTransferSpeed(operations),
                SyncsByDevice = syncFiles.GroupBy(f => f.DeviceId).ToDictionary(g => g.Key, g => g.Count()),
                OperationsByType = operations.GroupBy(o => o.OperationType).ToDictionary(g => g.Key, g => g.Count()),
                ConflictsByReason = syncFiles.Where(f => f.IsInConflict()).GroupBy(f => f.ConflictReason ?? "Unknown").ToDictionary(g => g.Key, g => g.Count())
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync analytics");
            return new FileSyncAnalyticsDto { FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30), ToDate = DateTime.UtcNow };
        }
    }

    public async Task<List<FileVersionDto>> GetFileVersionsAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versions = await _versionRepository.GetByFileIdAsync(fileId, cancellationToken);
            return versions.Select(MapToFileVersionDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file versions for file {FileId}", fileId);
            return new List<FileVersionDto>();
        }
    }

    public async Task<bool> RestoreFileVersionAsync(Guid fileId, int versionNumber, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var syncFile = await _syncFileRepository.GetByIdAsync(fileId, cancellationToken);
            if (syncFile == null)
            {
                return false;
            }

            var version = syncFile.GetVersion(versionNumber);
            if (version == null)
            {
                return false;
            }

            // Create new version based on the restored version
            syncFile.UpdateFile(version.FileHash, version.FileSize, version.VersionMetadata);
            syncFile.AddSyncMetadata("restored_from_version", versionNumber);
            syncFile.AddSyncMetadata("restored_at", DateTime.UtcNow);
            syncFile.AddSyncMetadata("restored_by_device", deviceId);

            await _syncFileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("File {FileId} restored to version {VersionNumber} by device {DeviceId}",
                fileId, versionNumber, deviceId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring file version for file {FileId}", fileId);
            return false;
        }
    }

    public async Task ProcessPendingSyncOperationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var pendingOperations = await _operationRepository.GetPendingOperationsAsync(cancellationToken);

            foreach (var operation in pendingOperations)
            {
                try
                {
                    await ProcessSyncOperationAsync(operation, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing sync operation {OperationId}", operation.Id);
                    await FailSyncOperationAsync(operation.Id, ex.Message, "PROCESSING_ERROR", cancellationToken);
                }
            }

            if (pendingOperations.Any())
            {
                _logger.LogInformation("Processed {Count} pending sync operations", pendingOperations.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing pending sync operations");
        }
    }

    public async Task CleanupOldFileVersionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var maxVersionsPerFile = _configuration.GetValue<int>("FileSync:MaxVersionsPerFile", 10);
            var versionRetentionDays = _configuration.GetValue<int>("FileSync:VersionRetentionDays", 90);

            var cutoffDate = DateTime.UtcNow.AddDays(-versionRetentionDays);
            var oldVersions = await _versionRepository.GetOldVersionsAsync(maxVersionsPerFile, cutoffDate, cancellationToken);

            foreach (var version in oldVersions)
            {
                // Delete from storage if exists
                if (!string.IsNullOrEmpty(version.StorageLocation))
                {
                    await _storageService.DeleteFileAsync(version.StorageLocation, cancellationToken);
                }

                _versionRepository.Remove(version);
            }

            if (oldVersions.Any())
            {
                await _versionRepository.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Cleaned up {Count} old file versions", oldVersions.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old file versions");
        }
    }

    // Helper methods
    private async Task<bool> CheckForConflictAsync(SyncFile syncFile, string newFileHash, string deviceId, CancellationToken cancellationToken)
    {
        try
        {
            // Check if file was modified by another device since last sync
            if (syncFile.DeviceId != deviceId && syncFile.FileHash != newFileHash)
            {
                var lastSyncTime = syncFile.LastSyncedAt ?? syncFile.CreatedAt;
                var recentOperations = await _operationRepository.GetRecentOperationsAsync(syncFile.Id, lastSyncTime, cancellationToken);

                return recentOperations.Any(o => o.SourceDevice != deviceId && o.IsCompleted());
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for conflict");
            return false;
        }
    }

    private async Task ProcessSyncOperationAsync(FileSyncOperation operation, CancellationToken cancellationToken)
    {
        try
        {
            operation.Start();
            await _operationRepository.SaveChangesAsync(cancellationToken);

            switch (operation.OperationType.ToLowerInvariant())
            {
                case "upload":
                    await ProcessUploadOperationAsync(operation, cancellationToken);
                    break;
                case "download":
                    await ProcessDownloadOperationAsync(operation, cancellationToken);
                    break;
                case "delete":
                    await ProcessDeleteOperationAsync(operation, cancellationToken);
                    break;
                default:
                    throw new InvalidOperationException($"Unknown operation type: {operation.OperationType}");
            }

            operation.Complete();
            await _operationRepository.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            operation.Fail(ex.Message, "PROCESSING_ERROR");
            await _operationRepository.SaveChangesAsync(cancellationToken);
            throw;
        }
    }

    private async Task ProcessUploadOperationAsync(FileSyncOperation operation, CancellationToken cancellationToken)
    {
        var syncFile = await _syncFileRepository.GetByIdAsync(operation.SyncFileId, cancellationToken);
        if (syncFile == null) return;

        // Simulate upload progress
        for (int i = 0; i <= 100; i += 10)
        {
            var bytesTransferred = (long)(operation.TotalBytes * i / 100.0);
            operation.UpdateProgress(bytesTransferred, 1024 * 1024); // 1MB/s
            await _operationRepository.SaveChangesAsync(cancellationToken);

            if (cancellationToken.IsCancellationRequested)
                break;

            await Task.Delay(100, cancellationToken); // Simulate processing time
        }
    }

    private async Task ProcessDownloadOperationAsync(FileSyncOperation operation, CancellationToken cancellationToken)
    {
        var syncFile = await _syncFileRepository.GetByIdAsync(operation.SyncFileId, cancellationToken);
        if (syncFile == null) return;

        // Simulate download progress
        for (int i = 0; i <= 100; i += 10)
        {
            var bytesTransferred = (long)(operation.TotalBytes * i / 100.0);
            operation.UpdateProgress(bytesTransferred, 2 * 1024 * 1024); // 2MB/s
            await _operationRepository.SaveChangesAsync(cancellationToken);

            if (cancellationToken.IsCancellationRequested)
                break;

            await Task.Delay(100, cancellationToken); // Simulate processing time
        }
    }

    private async Task ProcessDeleteOperationAsync(FileSyncOperation operation, CancellationToken cancellationToken)
    {
        var syncFile = await _syncFileRepository.GetByIdAsync(operation.SyncFileId, cancellationToken);
        if (syncFile == null) return;

        // Mark file as deleted
        syncFile.MarkAsDeleted();
        await _syncFileRepository.SaveChangesAsync(cancellationToken);

        operation.UpdateProgress(operation.TotalBytes, 0);
        await _operationRepository.SaveChangesAsync(cancellationToken);
    }

    private double CalculateAverageTransferSpeed(List<FileSyncOperation> operations)
    {
        var completedOperations = operations.Where(o => o.IsCompleted() && o.TransferSpeed.HasValue).ToList();
        return completedOperations.Any() ? completedOperations.Average(o => o.TransferSpeed!.Value) : 0;
    }

    // Mapping methods
    private SyncFileDto MapToSyncFileDto(SyncFile syncFile)
    {
        return new SyncFileDto
        {
            Id = syncFile.Id,
            FileName = syncFile.FileName,
            FilePath = syncFile.FilePath,
            FileHash = syncFile.FileHash,
            FileSize = syncFile.FileSize,
            MimeType = syncFile.MimeType,
            UserId = syncFile.UserId,
            DeviceId = syncFile.DeviceId,
            Version = syncFile.Version,
            Status = syncFile.Status,
            CreatedAt = syncFile.CreatedAt,
            ModifiedAt = syncFile.ModifiedAt,
            LastSyncedAt = syncFile.LastSyncedAt,
            ConflictReason = syncFile.ConflictReason,
            FileMetadata = syncFile.FileMetadata,
            SyncMetadata = syncFile.SyncMetadata,
            IsDeleted = syncFile.IsDeleted,
            DeletedAt = syncFile.DeletedAt,
            NeedsSync = syncFile.NeedsSync(),
            IsInConflict = syncFile.IsInConflict(),
            IsSynced = syncFile.IsSynced(),
            IsSyncing = syncFile.IsSyncing(),
            TimeSinceLastSync = syncFile.GetTimeSinceLastSync()
        };
    }

    private FileSyncOperationDto MapToFileSyncOperationDto(FileSyncOperation operation)
    {
        return new FileSyncOperationDto
        {
            Id = operation.Id,
            SyncFileId = operation.SyncFileId,
            OperationType = operation.OperationType,
            Status = operation.Status,
            StartedAt = operation.StartedAt,
            CompletedAt = operation.CompletedAt,
            SourceDevice = operation.SourceDevice,
            TargetDevice = operation.TargetDevice,
            BytesTransferred = operation.BytesTransferred,
            TotalBytes = operation.TotalBytes,
            TransferSpeed = operation.TransferSpeed,
            ErrorMessage = operation.ErrorMessage,
            ErrorCode = operation.ErrorCode,
            OperationMetadata = operation.OperationMetadata,
            PerformanceMetrics = operation.PerformanceMetrics,
            ProgressPercentage = operation.GetProgressPercentage(),
            Duration = operation.GetDuration(),
            AverageTransferSpeed = operation.GetAverageTransferSpeed(),
            IsCompleted = operation.IsCompleted(),
            IsFailed = operation.IsFailed(),
            IsInProgress = operation.IsInProgress(),
            IsCancelled = operation.IsCancelled()
        };
    }

    private FileVersionDto MapToFileVersionDto(FileVersion version)
    {
        return new FileVersionDto
        {
            Id = version.Id,
            SyncFileId = version.SyncFileId,
            VersionNumber = version.VersionNumber,
            FileHash = version.FileHash,
            FileSize = version.FileSize,
            CreatedByDevice = version.CreatedByDevice,
            CreatedAt = version.CreatedAt,
            VersionMetadata = version.VersionMetadata,
            StorageLocation = version.StorageLocation,
            IsActive = version.IsActive
        };
    }
}

// Supporting interfaces and classes
public interface IFileStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, CancellationToken cancellationToken = default);
    Task<Stream> DownloadFileAsync(string storageLocation, CancellationToken cancellationToken = default);
    Task<bool> DeleteFileAsync(string storageLocation, CancellationToken cancellationToken = default);
    Task<bool> FileExistsAsync(string storageLocation, CancellationToken cancellationToken = default);
}

public interface IFileConflictResolver
{
    Task<string> AnalyzeConflictAsync(SyncFile syncFile, UpdateSyncFileRequest request, CancellationToken cancellationToken = default);
    Task<ConflictResolution> ResolveConflictAsync(SyncFile syncFile, ResolveFileConflictRequest request, CancellationToken cancellationToken = default);
}

public class ConflictResolution
{
    public string ResolutionType { get; set; } = string.Empty;
    public bool RequiresFileUpdate { get; set; }
    public string? ResolvedFileHash { get; set; }
    public long ResolvedFileSize { get; set; }
    public Dictionary<string, object>? ResolvedMetadata { get; set; }
}
