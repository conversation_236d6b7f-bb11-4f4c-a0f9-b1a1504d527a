using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of funnel analysis service
/// </summary>
public class FunnelAnalysisService : IFunnelAnalysisService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<FunnelAnalysisService> _logger;

    public FunnelAnalysisService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<FunnelAnalysisService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<FunnelAnalysisDto> CreateFunnelAnalysisAsync(CreateFunnelAnalysisRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating funnel analysis '{Name}' for user {UserId}", request.Name, request.UserId);

            var funnel = new AnalyticsBIService.Domain.Entities.FunnelAnalysis(
                request.Name,
                request.Description,
                request.FunnelType,
                JsonSerializer.Serialize(request.Definition),
                request.UserId,
                request.UserType);

            _context.FunnelAnalyses.Add(funnel);
            await _context.SaveChangesAsync(cancellationToken);

            // Calculate initial metrics
            var metrics = await CalculateInitialFunnelMetricsAsync(funnel.Id, request.Definition, cancellationToken);
            funnel.UpdateMetrics(metrics);

            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(funnel);

            // Cache the funnel
            var cacheKey = CacheKeys.FunnelAnalysis(request.UserId, request.FunnelType, "all");
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Funnel analysis created with ID {FunnelId}", funnel.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating funnel analysis");
            throw;
        }
    }

    public async Task<FunnelAnalysisDto?> GetFunnelAnalysisAsync(Guid funnelId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"funnel:analysis:{funnelId}";
            var cachedResult = await _cacheService.GetAsync<FunnelAnalysisDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var funnel = await _context.FunnelAnalyses
                .FirstOrDefaultAsync(f => f.Id == funnelId, cancellationToken);

            if (funnel == null)
                return null;

            var result = MapToDto(funnel);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting funnel analysis {FunnelId}", funnelId);
            return null;
        }
    }

    public async Task<List<FunnelAnalysisDto>> GetUserFunnelsAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default)
    {
        try
        {
            var funnels = await _context.FunnelAnalyses
                .Where(f => f.CreatedBy == userId && f.IsActive)
                .OrderByDescending(f => f.UpdatedAt)
                .ToListAsync(cancellationToken);

            return funnels.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user funnels for user {UserId}", userId);
            return new List<FunnelAnalysisDto>();
        }
    }

    public async Task<FunnelConversionDto> CalculateConversionRatesAsync(Guid funnelId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Calculating conversion rates for funnel {FunnelId}", funnelId);

            var periodStart = startDate ?? DateTime.UtcNow.AddDays(-30);
            var periodEnd = endDate ?? DateTime.UtcNow;

            var cacheKey = $"funnel:conversion:{funnelId}:{periodStart:yyyyMMdd}:{periodEnd:yyyyMMdd}";
            var cachedResult = await _cacheService.GetAsync<FunnelConversionDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var funnel = await _context.FunnelAnalyses
                .FirstOrDefaultAsync(f => f.Id == funnelId, cancellationToken);

            if (funnel == null)
                throw new InvalidOperationException($"Funnel {funnelId} not found");

            var definition = JsonSerializer.Deserialize<FunnelDefinitionDto>(funnel.Definition) ?? new FunnelDefinitionDto();
            var conversionData = await CalculateConversionDataAsync(funnelId, definition, periodStart, periodEnd, cancellationToken);

            await _cacheService.SetAsync(cacheKey, conversionData, CacheExpiration.MediumTerm, cancellationToken);

            return conversionData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating conversion rates for funnel {FunnelId}", funnelId);
            throw;
        }
    }

    public async Task<FunnelPerformanceDto> GetFunnelPerformanceAsync(Guid funnelId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting funnel performance for {FunnelId}", funnelId);

            var cacheKey = $"funnel:performance:{funnelId}";
            var cachedResult = await _cacheService.GetAsync<FunnelPerformanceDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var performance = await CalculateFunnelPerformanceAsync(funnelId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, performance, CacheExpiration.MediumTerm, cancellationToken);

            return performance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting funnel performance");
            throw;
        }
    }

    public async Task<FunnelComparisonDto> CompareFunnelPerformanceAsync(List<Guid> funnelIds, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Comparing performance for {Count} funnels", funnelIds.Count);

            var comparisons = new List<FunnelComparisonItemDto>();

            foreach (var funnelId in funnelIds)
            {
                var funnel = await GetFunnelAnalysisAsync(funnelId, cancellationToken);
                if (funnel != null)
                {
                    var strengths = new List<string>();
                    var weaknesses = new List<string>();

                    // Analyze strengths and weaknesses
                    if (funnel.Metrics.OverallConversionRate > 20)
                        strengths.Add("High conversion rate");
                    else if (funnel.Metrics.OverallConversionRate < 5)
                        weaknesses.Add("Low conversion rate");

                    if (funnel.Metrics.DropOffRate < 30)
                        strengths.Add("Low drop-off rate");
                    else if (funnel.Metrics.DropOffRate > 70)
                        weaknesses.Add("High drop-off rate");

                    comparisons.Add(new FunnelComparisonItemDto
                    {
                        FunnelId = funnelId,
                        FunnelName = funnel.Name,
                        Metrics = new Dictionary<string, decimal>
                        {
                            { "ConversionRate", funnel.Metrics.OverallConversionRate },
                            { "DropOffRate", funnel.Metrics.DropOffRate },
                            { "TotalEntries", funnel.Metrics.TotalEntries },
                            { "TotalConversions", funnel.Metrics.TotalConversions }
                        },
                        Strengths = strengths,
                        Weaknesses = weaknesses
                    });
                }
            }

            // Rank funnels by performance
            RankFunnelsByPerformance(comparisons);

            var summary = GenerateFunnelComparisonSummary(comparisons);

            return new FunnelComparisonDto
            {
                FunnelIds = funnelIds,
                Comparisons = comparisons,
                Summary = summary,
                ComparedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error comparing funnel performance");
            throw;
        }
    }

    public async Task<FunnelDropOffDto> GetDropOffAnalysisAsync(Guid funnelId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting drop-off analysis for funnel {FunnelId}", funnelId);

            var cacheKey = $"funnel:dropoff:{funnelId}";
            var cachedResult = await _cacheService.GetAsync<FunnelDropOffDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var dropOffAnalysis = await AnalyzeFunnelDropOffAsync(funnelId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, dropOffAnalysis, CacheExpiration.LongTerm, cancellationToken);

            return dropOffAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting drop-off analysis");
            throw;
        }
    }

    public async Task<FunnelOptimizationDto> GetOptimizationRecommendationsAsync(Guid funnelId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting optimization recommendations for funnel {FunnelId}", funnelId);

            var cacheKey = $"funnel:optimization:{funnelId}";
            var cachedResult = await _cacheService.GetAsync<FunnelOptimizationDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var optimization = await GenerateOptimizationRecommendationsAsync(funnelId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, optimization, CacheExpiration.LongTerm, cancellationToken);

            return optimization;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting optimization recommendations");
            throw;
        }
    }

    public async Task<FunnelCohortDto> GetFunnelCohortAnalysisAsync(Guid funnelId, string cohortType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting funnel cohort analysis for {FunnelId}, type {CohortType}", funnelId, cohortType);

            var cacheKey = $"funnel:cohort:{funnelId}:{cohortType}";
            var cachedResult = await _cacheService.GetAsync<FunnelCohortDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var cohortAnalysis = await PerformFunnelCohortAnalysisAsync(funnelId, cohortType, cancellationToken);
            await _cacheService.SetAsync(cacheKey, cohortAnalysis, CacheExpiration.LongTerm, cancellationToken);

            return cohortAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting funnel cohort analysis");
            throw;
        }
    }

    public async Task<bool> UpdateFunnelDefinitionAsync(Guid funnelId, UpdateFunnelRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var funnel = await _context.FunnelAnalyses
                .FirstOrDefaultAsync(f => f.Id == funnelId, cancellationToken);

            if (funnel == null)
                return false;

            // Update properties using domain entity methods
            if (!string.IsNullOrEmpty(request.Name) || !string.IsNullOrEmpty(request.Description) || request.Definition != null)
            {
                var name = !string.IsNullOrEmpty(request.Name) ? request.Name : funnel.Name;
                var description = !string.IsNullOrEmpty(request.Description) ? request.Description : funnel.Description;
                var definition = request.Definition != null ? JsonSerializer.Serialize(request.Definition) : funnel.Definition;

                funnel.UpdateProperties(name, description, definition);
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    funnel.Activate();
                else
                    funnel.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cachePattern = $"funnel:*:{funnelId}";
            await _cacheService.RemoveByPatternAsync(cachePattern, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating funnel definition");
            return false;
        }
    }

    public async Task<bool> DeleteFunnelAnalysisAsync(Guid funnelId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var funnel = await _context.FunnelAnalyses
                .FirstOrDefaultAsync(f => f.Id == funnelId && f.CreatedBy == userId, cancellationToken);

            if (funnel == null)
                return false;

            _context.FunnelAnalyses.Remove(funnel);
            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cachePattern = $"funnel:*:{funnelId}";
            await _cacheService.RemoveByPatternAsync(cachePattern, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting funnel analysis");
            return false;
        }
    }

    public async Task<List<FunnelTemplateDto>> GetFunnelTemplatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return GetBuiltInFunnelTemplates();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting funnel templates");
            return new List<FunnelTemplateDto>();
        }
    }

    public async Task<bool> TrackFunnelEventAsync(TrackFunnelEventRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Tracking funnel event {EventName} for funnel {FunnelId}", request.EventName, request.FunnelId);

            var funnelEvent = new FunnelEvent(
                request.FunnelId,
                request.UserId,
                request.EventName,
                request.StepOrder,
                request.SessionId ?? Guid.NewGuid().ToString(),
                request.Properties);

            _context.FunnelEvents.Add(funnelEvent);
            await _context.SaveChangesAsync(cancellationToken);

            // Update real-time metrics cache
            var realTimeCacheKey = $"funnel:realtime:{request.FunnelId}";
            await _cacheService.RemoveAsync(realTimeCacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking funnel event");
            return false;
        }
    }

    public async Task<RealTimeFunnelDto> GetRealTimeFunnelMetricsAsync(Guid funnelId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"funnel:realtime:{funnelId}";
            var cachedResult = await _cacheService.GetAsync<RealTimeFunnelDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var realTimeMetrics = await CalculateRealTimeFunnelMetricsAsync(funnelId, cancellationToken);
            await _cacheService.SetAsync(cacheKey, realTimeMetrics, CacheExpiration.RealTime, cancellationToken);

            return realTimeMetrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time funnel metrics");
            return new RealTimeFunnelDto
            {
                FunnelId = funnelId,
                LastUpdated = DateTime.UtcNow,
                UpdateInterval = 30000
            };
        }
    }

    // Helper methods
    private FunnelAnalysisDto MapToDto(AnalyticsBIService.Domain.Entities.FunnelAnalysis funnel)
    {
        var definition = string.IsNullOrEmpty(funnel.Definition)
            ? new FunnelDefinitionDto()
            : JsonSerializer.Deserialize<FunnelDefinitionDto>(funnel.Definition) ?? new FunnelDefinitionDto();

        var metrics = string.IsNullOrEmpty(funnel.Metrics)
            ? new FunnelMetricsDto()
            : JsonSerializer.Deserialize<FunnelMetricsDto>(funnel.Metrics) ?? new FunnelMetricsDto();

        return new FunnelAnalysisDto
        {
            Id = funnel.Id,
            Name = funnel.Name,
            Description = funnel.Description,
            Definition = definition,
            Metrics = metrics,
            CreatedAt = funnel.CreatedAt,
            UpdatedAt = funnel.UpdatedAt ?? DateTime.UtcNow,
            CreatedBy = funnel.CreatedBy,
            UserType = funnel.UserType,
            IsActive = funnel.IsActive,
            FunnelType = funnel.FunnelType
        };
    }

    private async Task<FunnelMetricsDto> CalculateInitialFunnelMetricsAsync(Guid funnelId, FunnelDefinitionDto definition, CancellationToken cancellationToken)
    {
        // This would calculate actual metrics from the database
        // For now, returning sample data
        var random = new Random();
        var totalEntries = random.Next(1000, 5000);
        var conversionRate = (decimal)(random.NextDouble() * 20 + 5); // 5-25%
        var totalConversions = (int)(totalEntries * conversionRate / 100);

        var stepMetrics = new List<FunnelStepMetricsDto>();
        var remainingUsers = totalEntries;

        for (int i = 0; i < definition.Steps.Count; i++)
        {
            var step = definition.Steps[i];
            var dropOffRate = (decimal)(random.NextDouble() * 30 + 10); // 10-40% drop-off per step
            var conversions = i == definition.Steps.Count - 1 ? totalConversions : (int)(remainingUsers * (100 - dropOffRate) / 100);
            var exits = remainingUsers - conversions;

            stepMetrics.Add(new FunnelStepMetricsDto
            {
                StepOrder = step.StepOrder,
                StepName = step.StepName,
                Entries = remainingUsers,
                Exits = exits,
                Conversions = conversions,
                ConversionRate = remainingUsers > 0 ? Math.Round((decimal)conversions / remainingUsers * 100, 2) : 0,
                DropOffRate = remainingUsers > 0 ? Math.Round((decimal)exits / remainingUsers * 100, 2) : 0,
                AverageTimeInStep = TimeSpan.FromMinutes(random.Next(1, 10)),
                AverageTimeToNext = TimeSpan.FromMinutes(random.Next(1, 15))
            });

            remainingUsers = conversions;
        }

        return new FunnelMetricsDto
        {
            OverallConversionRate = conversionRate,
            TotalEntries = totalEntries,
            TotalConversions = totalConversions,
            AverageTimeToConvert = (decimal)(random.NextDouble() * 60 + 10), // 10-70 minutes
            DropOffRate = Math.Round(100 - conversionRate, 2),
            LastCalculated = DateTime.UtcNow,
            StepMetrics = stepMetrics
        };
    }

    private async Task<FunnelConversionDto> CalculateConversionDataAsync(Guid funnelId, FunnelDefinitionDto definition, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        var random = new Random();
        var stepConversions = new List<FunnelStepConversionDto>();
        var totalStartUsers = random.Next(500, 2000);
        var remainingUsers = totalStartUsers;

        // Generate conversion data for each step
        for (int i = 0; i < definition.Steps.Count; i++)
        {
            var step = definition.Steps[i];
            var conversionFromPrevious = i == 0 ? 100m : (decimal)(random.NextDouble() * 40 + 50); // 50-90% conversion from previous step
            var currentUsers = i == 0 ? totalStartUsers : (int)(remainingUsers * conversionFromPrevious / 100);
            var conversionFromStart = totalStartUsers > 0 ? Math.Round((decimal)currentUsers / totalStartUsers * 100, 2) : 0;
            var dropOffFromPrevious = Math.Round(100 - conversionFromPrevious, 2);

            // Generate segments
            var segments = GenerateConversionSegments(currentUsers, random);

            stepConversions.Add(new FunnelStepConversionDto
            {
                StepOrder = step.StepOrder,
                StepName = step.StepName,
                UniqueUsers = currentUsers,
                TotalEvents = (int)(currentUsers * (1 + random.NextDouble())), // Some users may trigger multiple events
                ConversionFromPrevious = conversionFromPrevious,
                ConversionFromStart = conversionFromStart,
                DropOffFromPrevious = dropOffFromPrevious,
                Segments = segments
            });

            remainingUsers = currentUsers;
        }

        // Generate trends
        var trends = GenerateConversionTrends(startDate, endDate, stepConversions.Last().ConversionFromStart, random);

        var summary = new FunnelConversionSummaryDto
        {
            OverallConversionRate = stepConversions.LastOrDefault()?.ConversionFromStart ?? 0,
            TotalStartUsers = totalStartUsers,
            TotalCompletedUsers = stepConversions.LastOrDefault()?.UniqueUsers ?? 0,
            WorstPerformingStep = stepConversions.OrderBy(s => s.ConversionFromPrevious).FirstOrDefault()?.StepName ?? "",
            BestPerformingStep = stepConversions.OrderByDescending(s => s.ConversionFromPrevious).FirstOrDefault()?.StepName ?? "",
            AverageCompletionTime = TimeSpan.FromMinutes(random.Next(15, 120)),
            ImprovementPotential = Math.Round((decimal)(random.NextDouble() * 20 + 5), 2) // 5-25% improvement potential
        };

        return new FunnelConversionDto
        {
            FunnelId = funnelId,
            AnalysisPeriodStart = startDate,
            AnalysisPeriodEnd = endDate,
            StepConversions = stepConversions,
            Summary = summary,
            Trends = trends,
            CalculatedAt = DateTime.UtcNow
        };
    }

    private List<ConversionSegmentDto> GenerateConversionSegments(int totalUsers, Random random)
    {
        var segments = new List<ConversionSegmentDto>();
        var segmentNames = new[] { "New Users", "Returning Users", "Mobile Users", "Desktop Users", "Premium Users" };
        var remainingUsers = totalUsers;

        for (int i = 0; i < Math.Min(3, segmentNames.Length); i++)
        {
            var segmentSize = i == 2 ? remainingUsers : random.Next(totalUsers / 10, remainingUsers / 2);
            remainingUsers -= segmentSize;

            segments.Add(new ConversionSegmentDto
            {
                SegmentName = segmentNames[i],
                UserCount = segmentSize,
                ConversionRate = Math.Round((decimal)(random.NextDouble() * 40 + 40), 2), // 40-80%
                Characteristics = new Dictionary<string, object>
                {
                    { "AvgSessionDuration", random.Next(5, 30) },
                    { "DeviceType", i % 2 == 0 ? "Mobile" : "Desktop" },
                    { "UserType", i < 2 ? "New" : "Returning" }
                }
            });

            if (remainingUsers <= 0) break;
        }

        return segments;
    }

    private List<FunnelConversionTrendDto> GenerateConversionTrends(DateTime startDate, DateTime endDate, decimal baseConversionRate, Random random)
    {
        var trends = new List<FunnelConversionTrendDto>();
        var currentDate = startDate;
        var currentRate = baseConversionRate;

        while (currentDate <= endDate)
        {
            // Add some variation to the conversion rate
            var variation = (decimal)(random.NextDouble() * 10 - 5); // ±5%
            currentRate = Math.Max(0, Math.Min(100, currentRate + variation));

            var entries = random.Next(50, 200);
            var conversions = (int)(entries * currentRate / 100);

            trends.Add(new FunnelConversionTrendDto
            {
                Date = currentDate,
                ConversionRate = Math.Round(currentRate, 2),
                Conversions = conversions,
                Entries = entries,
                TrendDirection = variation > 0 ? "Up" : variation < 0 ? "Down" : "Stable"
            });

            currentDate = currentDate.AddDays(1);
        }

        return trends;
    }

    private async Task<FunnelPerformanceDto> CalculateFunnelPerformanceAsync(Guid funnelId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var metrics = new List<PerformanceMetricDto>
        {
            new() { Name = "Conversion Rate", Value = Math.Round((decimal)(random.NextDouble() * 25 + 5), 2), Unit = "percent", Timestamp = DateTime.UtcNow },
            new() { Name = "Average Time to Convert", Value = Math.Round((decimal)(random.NextDouble() * 60 + 15), 2), Unit = "minutes", Timestamp = DateTime.UtcNow },
            new() { Name = "Drop-off Rate", Value = Math.Round((decimal)(random.NextDouble() * 40 + 20), 2), Unit = "percent", Timestamp = DateTime.UtcNow },
            new() { Name = "Completion Rate", Value = Math.Round((decimal)(random.NextDouble() * 30 + 60), 2), Unit = "percent", Timestamp = DateTime.UtcNow }
        };

        var trends = new List<PerformanceTrendDto>();
        for (int i = 0; i < 30; i++)
        {
            trends.Add(new PerformanceTrendDto
            {
                Date = DateTime.UtcNow.AddDays(-i),
                MetricName = "Conversion Rate",
                Value = Math.Round((decimal)(random.NextDouble() * 25 + 5), 2),
                ChangeFromPrevious = Math.Round((decimal)(random.NextDouble() * 4 - 2), 2),
                TrendDirection = random.Next(0, 2) == 0 ? "Up" : "Down"
            });
        }

        var insights = new List<PerformanceInsightDto>
        {
            new()
            {
                Type = "Opportunity",
                Title = "High Drop-off at Step 2",
                Description = "Users are dropping off significantly at the second step",
                Severity = "High",
                Recommendations = new List<string> { "Simplify step 2 form", "Add progress indicator", "Provide help text" },
                Data = new Dictionary<string, object> { { "DropOffRate", 45.2m }, { "StepName", "Registration" } }
            },
            new()
            {
                Type = "Success",
                Title = "Strong Mobile Performance",
                Description = "Mobile users show higher conversion rates",
                Severity = "Low",
                Recommendations = new List<string> { "Optimize desktop experience", "Apply mobile UX patterns to desktop" },
                Data = new Dictionary<string, object> { { "MobileConversionRate", 18.5m }, { "DesktopConversionRate", 12.3m } }
            }
        };

        var benchmark = new PerformanceBenchmarkDto
        {
            IndustryAverage = 15.2m,
            CompanyAverage = 18.7m,
            CohortPerformance = metrics.First(m => m.Name == "Conversion Rate").Value,
            PerformanceRating = "Above Average",
            Recommendations = new List<string>
            {
                "Focus on reducing drop-off at key steps",
                "Implement A/B testing for step optimization",
                "Add exit-intent surveys to understand drop-off reasons"
            }
        };

        return new FunnelPerformanceDto
        {
            FunnelId = funnelId,
            Metrics = metrics,
            Trends = trends,
            Benchmark = benchmark,
            Insights = insights,
            CalculatedAt = DateTime.UtcNow
        };
    }

    private void RankFunnelsByPerformance(List<FunnelComparisonItemDto> comparisons)
    {
        // Rank by conversion rate primarily, then by total conversions
        var ranked = comparisons
            .OrderByDescending(c => c.Metrics.GetValueOrDefault("ConversionRate", 0))
            .ThenByDescending(c => c.Metrics.GetValueOrDefault("TotalConversions", 0))
            .ToList();

        for (int i = 0; i < ranked.Count; i++)
        {
            ranked[i].PerformanceRank = $"#{i + 1}";
        }
    }

    private ComparisonSummaryDto GenerateFunnelComparisonSummary(List<FunnelComparisonItemDto> comparisons)
    {
        if (!comparisons.Any()) return new ComparisonSummaryDto();

        var bestPerforming = comparisons.OrderByDescending(c => c.Metrics.GetValueOrDefault("ConversionRate", 0)).First();
        var worstPerforming = comparisons.OrderBy(c => c.Metrics.GetValueOrDefault("ConversionRate", 0)).First();

        return new ComparisonSummaryDto
        {
            BestPerformingCohort = bestPerforming.FunnelId,
            WorstPerformingCohort = worstPerforming.FunnelId,
            KeyDifferentiator = "Conversion Rate",
            Insights = new List<string>
            {
                $"Best performing funnel has {bestPerforming.Metrics.GetValueOrDefault("ConversionRate", 0):F1}% conversion rate",
                $"Worst performing funnel has {worstPerforming.Metrics.GetValueOrDefault("ConversionRate", 0):F1}% conversion rate",
                "Focus on optimizing underperforming funnel steps"
            }
        };
    }

    private async Task<FunnelDropOffDto> AnalyzeFunnelDropOffAsync(Guid funnelId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var dropOffPoints = new List<DropOffPointDto>();

        // Generate drop-off data for each step
        for (int i = 1; i <= 5; i++)
        {
            var dropOffCount = random.Next(50, 300);
            var dropOffRate = Math.Round((decimal)(random.NextDouble() * 40 + 10), 2); // 10-50%

            dropOffPoints.Add(new DropOffPointDto
            {
                StepOrder = i,
                StepName = $"Step {i}",
                DropOffCount = dropOffCount,
                DropOffRate = dropOffRate,
                ImpactOnOverallConversion = Math.Round(dropOffRate * 0.2m, 2), // Simplified impact calculation
                CommonExitActions = new List<string> { "Close browser", "Navigate away", "Abandon form" },
                AverageTimeBeforeDropOff = TimeSpan.FromMinutes(random.Next(1, 10))
            });
        }

        var highestDropOff = dropOffPoints.OrderByDescending(d => d.DropOffRate).First();
        var summary = new DropOffSummaryDto
        {
            HighestDropOffStep = highestDropOff.StepName,
            HighestDropOffRate = highestDropOff.DropOffRate,
            TotalDropOffRate = Math.Round(dropOffPoints.Average(d => d.DropOffRate), 2),
            TotalDropOffs = dropOffPoints.Sum(d => d.DropOffCount),
            PrimaryReasons = new List<string> { "Complex form", "Slow loading", "Unclear instructions", "Technical issues" }
        };

        var reasons = new List<DropOffReasonDto>
        {
            new()
            {
                Reason = "Form complexity",
                Frequency = 0.35m,
                Category = "UX",
                AffectedSteps = new List<string> { "Step 2", "Step 3" },
                Impact = "High"
            },
            new()
            {
                Reason = "Page load time",
                Frequency = 0.25m,
                Category = "Performance",
                AffectedSteps = new List<string> { "Step 1", "Step 4" },
                Impact = "Medium"
            },
            new()
            {
                Reason = "Mobile experience",
                Frequency = 0.20m,
                Category = "Mobile",
                AffectedSteps = new List<string> { "Step 2", "Step 5" },
                Impact = "Medium"
            }
        };

        return new FunnelDropOffDto
        {
            FunnelId = funnelId,
            DropOffPoints = dropOffPoints,
            Summary = summary,
            Reasons = reasons,
            AnalyzedAt = DateTime.UtcNow
        };
    }

    private async Task<FunnelOptimizationDto> GenerateOptimizationRecommendationsAsync(Guid funnelId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var currentConversionRate = Math.Round((decimal)(random.NextDouble() * 20 + 10), 2);
        var potentialConversionRate = currentConversionRate + Math.Round((decimal)(random.NextDouble() * 10 + 5), 2);

        var recommendations = new List<OptimizationRecommendationDto>
        {
            new()
            {
                Title = "Simplify Registration Form",
                Description = "Reduce the number of required fields in the registration step",
                Category = "UX Optimization",
                Priority = "High",
                EstimatedImpact = 15.5m,
                ImplementationEffort = "Medium",
                ActionItems = new List<string>
                {
                    "Remove non-essential fields",
                    "Implement progressive profiling",
                    "Add social login options"
                },
                AffectedSteps = new List<string> { "Registration", "Profile Setup" }
            },
            new()
            {
                Title = "Add Progress Indicator",
                Description = "Show users their progress through the funnel",
                Category = "UX Enhancement",
                Priority = "Medium",
                EstimatedImpact = 8.2m,
                ImplementationEffort = "Low",
                ActionItems = new List<string>
                {
                    "Design progress bar component",
                    "Implement step indicators",
                    "Add completion percentage"
                },
                AffectedSteps = new List<string> { "All Steps" }
            },
            new()
            {
                Title = "Optimize Mobile Experience",
                Description = "Improve mobile user experience and conversion",
                Category = "Mobile Optimization",
                Priority = "High",
                EstimatedImpact = 12.3m,
                ImplementationEffort = "High",
                ActionItems = new List<string>
                {
                    "Responsive design improvements",
                    "Touch-friendly interface",
                    "Mobile-specific flow optimization"
                },
                AffectedSteps = new List<string> { "All Steps" }
            }
        };

        var opportunities = new List<OptimizationOpportunityDto>
        {
            new()
            {
                OpportunityType = "Conversion Rate",
                Title = "Form Optimization",
                PotentialGain = 15.5m,
                Confidence = "High",
                Details = new Dictionary<string, object>
                {
                    { "CurrentDropOff", 45.2m },
                    { "PotentialDropOff", 29.7m },
                    { "AffectedUsers", 1250 }
                }
            },
            new()
            {
                OpportunityType = "User Experience",
                Title = "Mobile Optimization",
                PotentialGain = 12.3m,
                Confidence = "Medium",
                Details = new Dictionary<string, object>
                {
                    { "MobileTraffic", 65.8m },
                    { "MobileConversionGap", 8.5m }
                }
            }
        };

        var summary = new OptimizationSummaryDto
        {
            CurrentConversionRate = currentConversionRate,
            PotentialConversionRate = potentialConversionRate,
            EstimatedImprovement = potentialConversionRate - currentConversionRate,
            HighPriorityRecommendations = recommendations.Count(r => r.Priority == "High"),
            MediumPriorityRecommendations = recommendations.Count(r => r.Priority == "Medium"),
            LowPriorityRecommendations = recommendations.Count(r => r.Priority == "Low")
        };

        return new FunnelOptimizationDto
        {
            FunnelId = funnelId,
            Recommendations = recommendations,
            Summary = summary,
            Opportunities = opportunities,
            GeneratedAt = DateTime.UtcNow
        };
    }

    private async Task<FunnelCohortDto> PerformFunnelCohortAnalysisAsync(Guid funnelId, string cohortType, CancellationToken cancellationToken)
    {
        var random = new Random();
        var segments = new List<FunnelCohortSegmentDto>();

        var segmentNames = cohortType.ToLower() switch
        {
            "device" => new[] { "Mobile Users", "Desktop Users", "Tablet Users" },
            "source" => new[] { "Organic Search", "Paid Ads", "Social Media", "Direct Traffic" },
            "geography" => new[] { "North America", "Europe", "Asia Pacific", "Other" },
            _ => new[] { "Segment A", "Segment B", "Segment C" }
        };

        foreach (var segmentName in segmentNames)
        {
            var userCount = random.Next(100, 500);
            var conversionRate = Math.Round((decimal)(random.NextDouble() * 30 + 10), 2);
            var completionTime = TimeSpan.FromMinutes(random.Next(15, 120));

            var stepConversions = new Dictionary<string, decimal>();
            for (int i = 1; i <= 5; i++)
            {
                stepConversions[$"Step {i}"] = Math.Round((decimal)(random.NextDouble() * 40 + 50), 2);
            }

            segments.Add(new FunnelCohortSegmentDto
            {
                SegmentName = segmentName,
                UserCount = userCount,
                ConversionRate = conversionRate,
                AverageCompletionTime = completionTime,
                StepConversions = stepConversions,
                Characteristics = new List<string>
                {
                    $"Average session duration: {random.Next(5, 30)} minutes",
                    $"Bounce rate: {random.Next(20, 60)}%",
                    $"Return visitor rate: {random.Next(30, 70)}%"
                }
            });
        }

        var bestSegment = segments.OrderByDescending(s => s.ConversionRate).First();
        var worstSegment = segments.OrderBy(s => s.ConversionRate).First();

        var summary = new FunnelCohortSummaryDto
        {
            BestPerformingSegment = bestSegment.SegmentName,
            WorstPerformingSegment = worstSegment.SegmentName,
            AverageConversionRate = Math.Round(segments.Average(s => s.ConversionRate), 2),
            ConversionRateVariance = Math.Round(bestSegment.ConversionRate - worstSegment.ConversionRate, 2),
            KeyDifferentiators = new List<string>
            {
                "Device type significantly impacts conversion",
                "Mobile users show different behavior patterns",
                "Geographic location affects completion rates"
            }
        };

        return new FunnelCohortDto
        {
            FunnelId = funnelId,
            CohortType = cohortType,
            Segments = segments,
            Summary = summary,
            AnalyzedAt = DateTime.UtcNow
        };
    }

    private async Task<RealTimeFunnelDto> CalculateRealTimeFunnelMetricsAsync(Guid funnelId, CancellationToken cancellationToken)
    {
        var random = new Random();
        var steps = new List<RealTimeFunnelStepDto>();

        for (int i = 1; i <= 5; i++)
        {
            var currentUsers = random.Next(10, 100);
            var todayEntries = random.Next(50, 200);
            var todayConversions = random.Next(10, todayEntries);
            var conversionRate = todayEntries > 0 ? Math.Round((decimal)todayConversions / todayEntries * 100, 2) : 0;

            steps.Add(new RealTimeFunnelStepDto
            {
                StepOrder = i,
                StepName = $"Step {i}",
                CurrentUsers = currentUsers,
                TodayEntries = todayEntries,
                TodayConversions = todayConversions,
                TodayConversionRate = conversionRate,
                Trend = random.Next(0, 3) switch
                {
                    0 => "Up",
                    1 => "Down",
                    _ => "Stable"
                }
            });
        }

        var totalActiveUsers = steps.Sum(s => s.CurrentUsers);
        var totalTodayConversions = steps.Sum(s => s.TodayConversions);
        var currentConversionRate = steps.LastOrDefault()?.TodayConversionRate ?? 0;

        var alerts = new List<string>();
        if (currentConversionRate < 10)
            alerts.Add("Low conversion rate detected");
        if (totalActiveUsers < 50)
            alerts.Add("Low traffic volume");

        var summary = new RealTimeFunnelSummaryDto
        {
            TotalActiveUsers = totalActiveUsers,
            CurrentConversionRate = currentConversionRate,
            TodayConversions = totalTodayConversions,
            PerformanceStatus = currentConversionRate > 15 ? "Good" : currentConversionRate > 10 ? "Average" : "Poor",
            Alerts = alerts
        };

        return new RealTimeFunnelDto
        {
            FunnelId = funnelId,
            Steps = steps,
            Summary = summary,
            LastUpdated = DateTime.UtcNow,
            UpdateInterval = 30000 // 30 seconds
        };
    }

    private List<FunnelTemplateDto> GetBuiltInFunnelTemplates()
    {
        return new List<FunnelTemplateDto>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Name = "E-commerce Purchase Funnel",
                Description = "Track users from product view to purchase completion",
                Category = "E-commerce",
                Industry = "Retail",
                DefaultDefinition = new FunnelDefinitionDto
                {
                    Steps = new List<FunnelStepDefinitionDto>
                    {
                        new() { StepOrder = 1, StepName = "Product View", EventName = "product_view", IsRequired = true },
                        new() { StepOrder = 2, StepName = "Add to Cart", EventName = "add_to_cart", IsRequired = true },
                        new() { StepOrder = 3, StepName = "Checkout", EventName = "checkout_start", IsRequired = true },
                        new() { StepOrder = 4, StepName = "Payment", EventName = "payment_info", IsRequired = true },
                        new() { StepOrder = 5, StepName = "Purchase", EventName = "purchase", IsRequired = true }
                    },
                    TimeWindow = "7 days",
                    ConversionGoal = "Purchase Completion"
                },
                RecommendedMetrics = new List<string> { "Conversion Rate", "Cart Abandonment", "Checkout Completion" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "User Registration Funnel",
                Description = "Track user registration and onboarding process",
                Category = "User Acquisition",
                Industry = "SaaS",
                DefaultDefinition = new FunnelDefinitionDto
                {
                    Steps = new List<FunnelStepDefinitionDto>
                    {
                        new() { StepOrder = 1, StepName = "Landing Page", EventName = "page_view", IsRequired = true },
                        new() { StepOrder = 2, StepName = "Sign Up Form", EventName = "signup_start", IsRequired = true },
                        new() { StepOrder = 3, StepName = "Email Verification", EventName = "email_verify", IsRequired = true },
                        new() { StepOrder = 4, StepName = "Profile Setup", EventName = "profile_complete", IsRequired = false },
                        new() { StepOrder = 5, StepName = "First Action", EventName = "first_action", IsRequired = true }
                    },
                    TimeWindow = "30 days",
                    ConversionGoal = "Active User"
                },
                RecommendedMetrics = new List<string> { "Registration Rate", "Activation Rate", "Time to First Action" },
                IsCustomizable = true
            },
            new()
            {
                Id = Guid.NewGuid(),
                Name = "Lead Generation Funnel",
                Description = "Track lead generation and qualification process",
                Category = "Marketing",
                Industry = "B2B",
                DefaultDefinition = new FunnelDefinitionDto
                {
                    Steps = new List<FunnelStepDefinitionDto>
                    {
                        new() { StepOrder = 1, StepName = "Content View", EventName = "content_view", IsRequired = true },
                        new() { StepOrder = 2, StepName = "Lead Magnet", EventName = "lead_magnet_view", IsRequired = true },
                        new() { StepOrder = 3, StepName = "Form Submit", EventName = "form_submit", IsRequired = true },
                        new() { StepOrder = 4, StepName = "Qualification", EventName = "lead_qualified", IsRequired = false },
                        new() { StepOrder = 5, StepName = "Sales Contact", EventName = "sales_contact", IsRequired = true }
                    },
                    TimeWindow = "14 days",
                    ConversionGoal = "Qualified Lead"
                },
                RecommendedMetrics = new List<string> { "Lead Conversion Rate", "Qualification Rate", "Cost per Lead" },
                IsCustomizable = true
            }
        };
    }
}


