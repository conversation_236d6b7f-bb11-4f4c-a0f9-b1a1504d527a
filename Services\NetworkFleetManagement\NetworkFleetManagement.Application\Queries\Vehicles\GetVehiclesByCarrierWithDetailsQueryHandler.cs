using AutoMapper;
using MediatR;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.Vehicles;

public class GetVehiclesByCarrierWithDetailsQueryHandler : IRequestHandler<GetVehiclesByCarrierWithDetailsQuery, PagedResult<VehicleDashboardDto>>
{
    private readonly NetworkFleetDbContext _context;
    private readonly IMapper _mapper;

    public GetVehiclesByCarrierWithDetailsQueryHandler(NetworkFleetDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<PagedResult<VehicleDashboardDto>> Handle(GetVehiclesByCarrierWithDetailsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Vehicles
            .Include(v => v.Carrier)
            .Include(v => v.Documents)
            .Include(v => v.MaintenanceRecords)
            .Include(v => v.DriverAssignments.Where(da => da.IsActive))
                .ThenInclude(da => da.Driver)
            .Where(v => v.CarrierId == request.CarrierId);

        // Apply filters
        if (request.Status.HasValue)
        {
            query = query.Where(v => v.Status == request.Status.Value);
        }

        if (request.VehicleType.HasValue)
        {
            query = query.Where(v => v.VehicleType == request.VehicleType.Value);
        }

        if (request.HasExpiredDocuments.HasValue)
        {
            if (request.HasExpiredDocuments.Value)
            {
                query = query.Where(v =>
                    (v.InsuranceExpiryDate.HasValue && v.InsuranceExpiryDate.Value <= DateTime.UtcNow) ||
                    (v.FitnessExpiryDate.HasValue && v.FitnessExpiryDate.Value <= DateTime.UtcNow) ||
                    (v.PermitExpiryDate.HasValue && v.PermitExpiryDate.Value <= DateTime.UtcNow));
            }
            else
            {
                query = query.Where(v =>
                    (!v.InsuranceExpiryDate.HasValue || v.InsuranceExpiryDate.Value > DateTime.UtcNow) &&
                    (!v.FitnessExpiryDate.HasValue || v.FitnessExpiryDate.Value > DateTime.UtcNow) &&
                    (!v.PermitExpiryDate.HasValue || v.PermitExpiryDate.Value > DateTime.UtcNow));
            }
        }

        if (request.RequiresMaintenance.HasValue)
        {
            var maintenanceThreshold = DateTime.UtcNow.AddDays(7);
            if (request.RequiresMaintenance.Value)
            {
                query = query.Where(v => v.NextMaintenanceDate.HasValue && v.NextMaintenanceDate.Value <= maintenanceThreshold);
            }
            else
            {
                query = query.Where(v => !v.NextMaintenanceDate.HasValue || v.NextMaintenanceDate.Value > maintenanceThreshold);
            }
        }

        if (request.IsAssigned.HasValue)
        {
            if (request.IsAssigned.Value)
            {
                query = query.Where(v => v.DriverAssignments.Any(da => da.IsActive));
            }
            else
            {
                query = query.Where(v => !v.DriverAssignments.Any(da => da.IsActive));
            }
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(v =>
                v.RegistrationNumber.ToLower().Contains(searchTerm) ||
                v.Make.ToLower().Contains(searchTerm) ||
                v.Model.ToLower().Contains(searchTerm));
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and get results
        var vehicles = await query
            .OrderByDescending(v => v.CreatedAt)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs with enhanced information
        var vehicleDashboardDtos = vehicles.Select(vehicle =>
        {
            var currentAssignment = vehicle.DriverAssignments.FirstOrDefault(da => da.IsActive);
            var now = DateTime.UtcNow;

            // Calculate document status
            var expiredDocumentsCount = 0;
            var expiringDocumentsCount = 0;
            var expiringThreshold = now.AddDays(30);

            if (vehicle.InsuranceExpiryDate.HasValue && vehicle.InsuranceExpiryDate.Value <= now)
                expiredDocumentsCount++;
            else if (vehicle.InsuranceExpiryDate.HasValue && vehicle.InsuranceExpiryDate.Value <= expiringThreshold)
                expiringDocumentsCount++;

            if (vehicle.FitnessExpiryDate.HasValue && vehicle.FitnessExpiryDate.Value <= now)
                expiredDocumentsCount++;
            else if (vehicle.FitnessExpiryDate.HasValue && vehicle.FitnessExpiryDate.Value <= expiringThreshold)
                expiringDocumentsCount++;

            if (vehicle.PermitExpiryDate.HasValue && vehicle.PermitExpiryDate.Value <= now)
                expiredDocumentsCount++;
            else if (vehicle.PermitExpiryDate.HasValue && vehicle.PermitExpiryDate.Value <= expiringThreshold)
                expiringDocumentsCount++;

            // Calculate maintenance days
            var daysUntilMaintenance = vehicle.NextMaintenanceDate.HasValue
                ? (int)(vehicle.NextMaintenanceDate.Value - now).TotalDays
                : 0;

            // Calculate utilization percentage (assuming 8 hours per day as full utilization)
            var utilizationPercentage = vehicle.UtilizationHours.HasValue
                ? Math.Min(100, (vehicle.UtilizationHours.Value / 8.0m) * 100)
                : 0;

            return new VehicleDashboardDto
            {
                Id = vehicle.Id,
                CarrierId = vehicle.CarrierId,
                RegistrationNumber = vehicle.RegistrationNumber,
                VehicleType = vehicle.VehicleType,
                Make = vehicle.Make,
                Model = vehicle.Model,
                Year = vehicle.Year,
                Status = vehicle.Status,
                DisplayName = vehicle.DisplayName,
                IsAvailable = vehicle.IsAvailable,
                CarrierCompanyName = vehicle.Carrier.CompanyName,

                // Document Status
                IsInsuranceValid = vehicle.IsInsuranceValid,
                IsFitnessValid = vehicle.IsFitnessValid,
                IsPermitValid = vehicle.IsPermitValid,
                InsuranceExpiryDate = vehicle.InsuranceExpiryDate,
                FitnessExpiryDate = vehicle.FitnessExpiryDate,
                PermitExpiryDate = vehicle.PermitExpiryDate,
                ExpiredDocumentsCount = expiredDocumentsCount,
                ExpiringDocumentsCount = expiringDocumentsCount,

                // Maintenance
                LastMaintenanceDate = vehicle.LastMaintenanceDate,
                NextMaintenanceDate = vehicle.NextMaintenanceDate,
                RequiresMaintenanceSoon = vehicle.RequiresMaintenanceSoon(),
                DaysUntilMaintenance = daysUntilMaintenance,

                // Performance
                DailyEarnings = vehicle.DailyEarnings,
                MonthlyEarnings = vehicle.MonthlyEarnings,
                UtilizationHours = vehicle.UtilizationHours,
                UtilizationPercentage = utilizationPercentage,

                // Assignment
                CurrentDriverId = currentAssignment?.DriverId,
                CurrentDriverName = currentAssignment?.Driver?.FullName,
                IsAssigned = currentAssignment != null,

                // Location
                CurrentLocation = vehicle.CurrentLocation != null ? _mapper.Map<LocationDto>(vehicle.CurrentLocation) : null,

                // Counts
                DocumentCount = vehicle.Documents.Count,
                MaintenanceRecordCount = vehicle.MaintenanceRecords.Count,

                CreatedAt = vehicle.CreatedAt,
                UpdatedAt = vehicle.UpdatedAt
            };
        }).ToList();

        return new PagedResult<VehicleDashboardDto>
        {
            Items = vehicleDashboardDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }
}
