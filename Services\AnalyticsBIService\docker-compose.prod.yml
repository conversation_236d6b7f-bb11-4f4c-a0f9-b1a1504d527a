version: '3.8'

services:
  # TimescaleDB for analytics data storage
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: tli-analytics-timescaledb-prod
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-TLI_AnalyticsBI}
      POSTGRES_USER: ${POSTGRES_USER:-timescale}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TIMESCALEDB_TELEMETRY: 'off'
    ports:
      - "${TIMESCALEDB_PORT:-5432}:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./Scripts/init-timescaledb.sql:/docker-entrypoint-initdb.d/01-init-timescaledb.sql:ro
      - ./database-setup.sql:/docker-entrypoint-initdb.d/02-init-analytics-db.sql:ro
      - ./Scripts/production/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    command: 
      - postgres
      - -c
      - config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-timescale} -d ${POSTGRES_DB:-TLI_AnalyticsBI}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: tli-analytics-redis-prod
    restart: always
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./Scripts/production/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Analytics & BI Service API
  analytics-api:
    build:
      context: ../../
      dockerfile: Services/AnalyticsBIService/Dockerfile
      target: final
    image: tli/analytics-bi-service:${VERSION:-latest}
    container_name: tli-analytics-api-prod
    restart: always
    environment:
      ASPNETCORE_ENVIRONMENT: Production
      ASPNETCORE_URLS: http://+:8080
      ASPNETCORE_FORWARDEDHEADERS_ENABLED: true

      # Database Configuration
      ConnectionStrings__DefaultConnection: "Host=timescaledb;Port=5432;Database=${POSTGRES_DB:-TLI_AnalyticsBI};User Id=${POSTGRES_USER:-timescale};Password=${POSTGRES_PASSWORD};Include Error Detail=false;Pooling=true;MinPoolSize=5;MaxPoolSize=100;CommandTimeout=30"
      ConnectionStrings__TimescaleConnection: "Host=timescaledb;Port=5432;Database=${POSTGRES_DB:-TLI_AnalyticsBI}_Timescale;User Id=${POSTGRES_USER:-timescale};Password=${POSTGRES_PASSWORD};Include Error Detail=false;Pooling=true;MinPoolSize=5;MaxPoolSize=100;CommandTimeout=30"
      ConnectionStrings__Redis: "redis:6379,password=${REDIS_PASSWORD},abortConnect=false"
      
      # Analytics Configuration
      AnalyticsSettings__DataRetentionDays: "${ANALYTICS_DATA_RETENTION_DAYS:-365}"
      AnalyticsSettings__MetricAggregationInterval: "${ANALYTICS_METRIC_AGGREGATION_INTERVAL:-00:05:00}"
      AnalyticsSettings__EnableRealTimeAnalytics: "${ANALYTICS_ENABLE_REALTIME:-true}"
      AnalyticsSettings__EnablePredictiveAnalytics: "${ANALYTICS_ENABLE_PREDICTIVE:-true}"
      AnalyticsSettings__CacheExpirationMinutes: "${ANALYTICS_CACHE_EXPIRATION_MINUTES:-30}"
      AnalyticsSettings__DefaultPageSize: "50"
      AnalyticsSettings__MaxPageSize: "1000"
      
      # JWT Configuration
      JwtSettings__SecretKey: ${JWT_SECRET_KEY}
      JwtSettings__Issuer: ${JWT_ISSUER:-TLI.Analytics}
      JwtSettings__Audience: ${JWT_AUDIENCE:-TLI.Users}
      JwtSettings__ExpiryMinutes: "60"
      JwtSettings__ValidateIssuer: "true"
      JwtSettings__ValidateAudience: "true"
      JwtSettings__ValidateLifetime: "true"
      JwtSettings__ValidateIssuerSigningKey: "true"
      
      # Logging Configuration
      Logging__LogLevel__Default: "Information"
      Logging__LogLevel__Microsoft: "Warning"
      Logging__LogLevel__Microsoft.Hosting.Lifetime: "Information"
      Logging__LogLevel__AnalyticsBIService: "Information"
      
      # CORS Configuration
      CORS__AllowedOrigins: ${CORS_ALLOWED_ORIGINS:-https://app.tli-platform.com,https://admin.tli-platform.com}
      CORS__AllowCredentials: "true"
      
      # Rate Limiting
      RateLimit__EnableRateLimiting: "true"
      RateLimit__RequestsPerMinute: "1000"
      RateLimit__RequestsPerHour: "10000"
      
      # Health Checks
      HealthChecks__Enabled: "true"
      HealthChecks__DatabaseTimeoutSeconds: "10"
      HealthChecks__RedisTimeoutSeconds: "5"
      
      # File Storage
      FileStorage__Provider: "S3"
      FileStorage__S3__BucketName: ${S3_BUCKET_NAME}
      FileStorage__S3__Region: ${AWS_REGION:-us-east-1}
      FileStorage__S3__AccessKey: ${AWS_ACCESS_KEY_ID}
      FileStorage__S3__SecretKey: ${AWS_SECRET_ACCESS_KEY}
      
      # Monitoring
      Monitoring__ApplicationInsights__InstrumentationKey: ${APPINSIGHTS_INSTRUMENTATIONKEY}
      Monitoring__Prometheus__Enabled: "true"
      Monitoring__Prometheus__Port: "9090"
      
    ports:
      - "${API_PORT:-5014}:8080"
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - tli-analytics-network
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: tli-analytics-nginx-prod
    restart: always
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./Scripts/production/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./Scripts/production/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - analytics-api
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: tli-analytics-prometheus-prod
    restart: always
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./Scripts/production/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: tli-analytics-grafana-prod
    restart: always
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_ADMIN_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
      GF_FEATURE_TOGGLES_ENABLE: publicDashboards
      GF_SERVER_ROOT_URL: ${GRAFANA_ROOT_URL:-http://localhost:3000}
      GF_SECURITY_ALLOW_EMBEDDING: "true"
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./Scripts/production/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml:ro
      - ./Scripts/production/grafana-dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml:ro
      - ./Scripts/production/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
      - timescaledb
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Log aggregation with Fluentd
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: tli-analytics-fluentd-prod
    restart: always
    volumes:
      - ./Scripts/production/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/app:ro
    ports:
      - "${FLUENTD_PORT:-24224}:24224"
      - "${FLUENTD_PORT:-24224}:24224/udp"
    networks:
      - tli-analytics-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

volumes:
  timescaledb_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  tli-analytics-network:
    driver: bridge
    name: tli-analytics-network-prod
    ipam:
      config:
        - subnet: **********/16
