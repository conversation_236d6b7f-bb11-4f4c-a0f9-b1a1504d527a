using AuditCompliance.Application.Commands;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Application.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AuditCompliance.API.Controllers
{
    /// <summary>
    /// Retention policy management controller
    /// </summary>
    [ApiController]
    [Route("api/audit/retention-policies")]
    [Authorize]
    public class RetentionPolicyManagementController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<RetentionPolicyManagementController> _logger;

        public RetentionPolicyManagementController(IMediator mediator, ILogger<RetentionPolicyManagementController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Create a new retention policy
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<RetentionPolicyManagementDto>> CreatePolicy([FromBody] CreateRetentionPolicyRequestDto request)
        {
            try
            {
                var command = new CreateRetentionPolicyCommand
                {
                    PolicyName = request.PolicyName,
                    Description = request.Description,
                    RetentionPolicy = request.RetentionPolicy,
                    ApplicableStandards = request.ApplicableStandards,
                    ApplicableEntityTypes = request.ApplicableEntityTypes,
                    ApplicableModules = request.ApplicableModules,
                    Priority = request.Priority,
                    RequiresApproval = request.RequiresApproval,
                    Tags = request.Tags,
                    Configuration = request.Configuration,
                    CreatedBy = GetCurrentUserId(),
                    CreatedByName = GetCurrentUserName()
                };

                var result = await _mediator.Send(command);
                return CreatedAtAction(nameof(GetPolicyById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating retention policy");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get retention policy by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<RetentionPolicyManagementDto>> GetPolicyById(Guid id)
        {
            try
            {
                var query = new GetRetentionPolicyByIdQuery { PolicyId = id };
                var result = await _mediator.Send(query);
                
                if (result == null)
                {
                    return NotFound(new { Message = "Retention policy not found" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention policy {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Search retention policies
        /// </summary>
        [HttpPost("search")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<PolicySearchResultDto>> SearchPolicies([FromBody] PolicySearchRequestDto request)
        {
            try
            {
                var query = new SearchRetentionPoliciesQuery
                {
                    SearchTerm = request.SearchTerm,
                    ApprovalStatus = request.ApprovalStatus,
                    IsActive = request.IsActive,
                    Priority = request.Priority,
                    ApplicableStandards = request.ApplicableStandards,
                    ApplicableEntityTypes = request.ApplicableEntityTypes,
                    ApplicableModules = request.ApplicableModules,
                    Tags = request.Tags,
                    CreatedBy = request.CreatedBy,
                    CreatedAfter = request.CreatedAfter,
                    CreatedBefore = request.CreatedBefore,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDescending = request.SortDescending
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching retention policies");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Update retention policy
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<RetentionPolicyManagementDto>> UpdatePolicy(Guid id, [FromBody] UpdateRetentionPolicyRequestDto request)
        {
            try
            {
                var command = new UpdateRetentionPolicyCommand
                {
                    PolicyId = id,
                    PolicyName = request.PolicyName,
                    Description = request.Description,
                    RetentionPolicy = request.RetentionPolicy,
                    ApplicableStandards = request.ApplicableStandards,
                    ApplicableEntityTypes = request.ApplicableEntityTypes,
                    ApplicableModules = request.ApplicableModules,
                    Priority = request.Priority,
                    Tags = request.Tags,
                    Configuration = request.Configuration,
                    UpdatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating retention policy {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Delete retention policy
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeletePolicy(Guid id, [FromQuery] string? reason = null)
        {
            try
            {
                var command = new DeleteRetentionPolicyCommand
                {
                    PolicyId = id,
                    DeletedBy = GetCurrentUserId(),
                    Reason = reason
                };

                var result = await _mediator.Send(command);
                if (result)
                {
                    return NoContent();
                }

                return NotFound(new { Message = "Retention policy not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting retention policy {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Approve or reject retention policy
        /// </summary>
        [HttpPost("{id}/approval")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> ApprovePolicy(Guid id, [FromBody] PolicyApprovalRequestDto request)
        {
            try
            {
                var command = new ApproveRetentionPolicyCommand
                {
                    PolicyId = id,
                    Approve = request.Approve,
                    Notes = request.Notes,
                    RejectionReason = request.RejectionReason,
                    ApprovedBy = GetCurrentUserId(),
                    ApprovedByName = GetCurrentUserName()
                };

                var result = await _mediator.Send(command);
                var message = request.Approve ? "Policy approved successfully" : "Policy rejected successfully";
                return Ok(new { Success = result, Message = result ? message : "Failed to process approval" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing policy approval {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Toggle policy active status
        /// </summary>
        [HttpPost("{id}/toggle-status")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> ToggleStatus(Guid id, [FromBody] TogglePolicyStatusRequestDto request)
        {
            try
            {
                var command = new ToggleRetentionPolicyStatusCommand
                {
                    PolicyId = id,
                    IsActive = request.IsActive,
                    Reason = request.Reason,
                    UpdatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Status updated successfully" : "Failed to update status" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling policy status {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Execute retention policy
        /// </summary>
        [HttpPost("{id}/execute")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<PolicyExecutionDto>> ExecutePolicy(Guid id, [FromBody] ExecutePolicyRequestDto request)
        {
            try
            {
                var command = new ExecuteRetentionPolicyCommand
                {
                    PolicyId = id,
                    DryRun = request.DryRun,
                    ExecutedBy = GetCurrentUserId(),
                    ExecutionNotes = request.ExecutionNotes
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing retention policy {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Analyze policy impact
        /// </summary>
        [HttpPost("{id}/analyze-impact")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<PolicyImpactAnalysisDto>> AnalyzeImpact(Guid id, [FromBody] AnalyzeImpactRequestDto request)
        {
            try
            {
                var command = new AnalyzePolicyImpactCommand
                {
                    PolicyId = id,
                    IncludeDetailedBreakdown = request.IncludeDetailedBreakdown,
                    AnalyzedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing policy impact {PolicyId}", id);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Validate retention policy
        /// </summary>
        [HttpPost("validate")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult<PolicyValidationResultDto>> ValidatePolicy([FromBody] ValidatePolicyRequestDto request)
        {
            try
            {
                var command = new ValidateRetentionPolicyCommand
                {
                    PolicyId = request.PolicyId,
                    PolicyName = request.PolicyName,
                    RetentionPolicy = request.RetentionPolicy,
                    ApplicableStandards = request.ApplicableStandards,
                    ApplicableEntityTypes = request.ApplicableEntityTypes,
                    ApplicableModules = request.ApplicableModules,
                    PerformImpactAnalysis = request.PerformImpactAnalysis,
                    CheckForConflicts = request.CheckForConflicts
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating retention policy");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Get retention status dashboard
        /// </summary>
        [HttpGet("dashboard")]
        [Authorize(Roles = "Admin,ComplianceOfficer,Auditor")]
        public async Task<ActionResult<RetentionStatusDashboardDto>> GetDashboard(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] bool includeTrends = true,
            [FromQuery] bool includeAlerts = true)
        {
            try
            {
                var command = new GetRetentionStatusDashboardCommand
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    IncludeTrends = includeTrends,
                    IncludeAlerts = includeAlerts,
                    RequestedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving retention status dashboard");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Bulk execute retention policies
        /// </summary>
        [HttpPost("bulk-execute")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<PolicyExecutionDto>>> BulkExecute([FromBody] BulkExecutePoliciesRequestDto request)
        {
            try
            {
                var command = new BulkExecuteRetentionPoliciesCommand
                {
                    PolicyIds = request.PolicyIds,
                    DryRun = request.DryRun,
                    ExecuteInParallel = request.ExecuteInParallel,
                    MaxConcurrency = request.MaxConcurrency,
                    ExecutedBy = GetCurrentUserId(),
                    ExecutionNotes = request.ExecutionNotes
                };

                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk executing retention policies");
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        /// <summary>
        /// Acknowledge retention alert
        /// </summary>
        [HttpPost("alerts/{alertId}/acknowledge")]
        [Authorize(Roles = "Admin,ComplianceOfficer")]
        public async Task<ActionResult> AcknowledgeAlert(string alertId, [FromBody] AcknowledgeAlertRequestDto request)
        {
            try
            {
                var command = new AcknowledgeRetentionAlertCommand
                {
                    AlertId = alertId,
                    AcknowledgedBy = GetCurrentUserId(),
                    AcknowledgmentNotes = request.AcknowledgmentNotes
                };

                var result = await _mediator.Send(command);
                return Ok(new { Success = result, Message = result ? "Alert acknowledged successfully" : "Failed to acknowledge alert" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acknowledging retention alert {AlertId}", alertId);
                return StatusCode(500, new { Message = "Internal server error", Error = ex.Message });
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
        }

        private string GetCurrentUserName()
        {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
        }
    }

    // Supporting DTOs
    public class TogglePolicyStatusRequestDto
    {
        public bool IsActive { get; set; }
        public string? Reason { get; set; }
    }

    public class ExecutePolicyRequestDto
    {
        public bool DryRun { get; set; } = false;
        public string? ExecutionNotes { get; set; }
    }

    public class AnalyzeImpactRequestDto
    {
        public bool IncludeDetailedBreakdown { get; set; } = true;
    }

    public class ValidatePolicyRequestDto
    {
        public Guid? PolicyId { get; set; }
        public string PolicyName { get; set; } = string.Empty;
        public RetentionPolicyDto RetentionPolicy { get; set; } = new();
        public List<ComplianceStandard> ApplicableStandards { get; set; } = new();
        public List<string> ApplicableEntityTypes { get; set; } = new();
        public List<string> ApplicableModules { get; set; } = new();
        public bool PerformImpactAnalysis { get; set; } = true;
        public bool CheckForConflicts { get; set; } = true;
    }

    public class BulkExecutePoliciesRequestDto
    {
        public List<Guid> PolicyIds { get; set; } = new();
        public bool DryRun { get; set; } = false;
        public bool ExecuteInParallel { get; set; } = false;
        public int MaxConcurrency { get; set; } = 3;
        public string? ExecutionNotes { get; set; }
    }

    public class AcknowledgeAlertRequestDto
    {
        public string? AcknowledgmentNotes { get; set; }
    }
}
