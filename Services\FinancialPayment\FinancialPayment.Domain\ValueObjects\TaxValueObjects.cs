﻿using FinancialPayment.Domain.Enums;
using Shared.Domain.Common;
using Shared.Domain.ValueObjects;

namespace FinancialPayment.Domain.ValueObjects;

/// <summary>
/// Value object representing a tax rate with validation
/// </summary>
public class TaxRate : ValueObject
{
    public decimal Rate { get; private set; }
    public TaxCalculationMethod CalculationMethod { get; private set; }
    public DateTime EffectiveFrom { get; private set; }
    public DateTime? EffectiveTo { get; private set; }

    private TaxRate() { }

    public TaxRate(decimal rate, TaxCalculationMethod calculationMethod, DateTime effectiveFrom, DateTime? effectiveTo = null)
    {
        if (rate < 0 || rate > 100)
            throw new ArgumentException("Tax rate must be between 0 and 100 percent", nameof(rate));

        if (effectiveTo.HasValue && effectiveTo.Value <= effectiveFrom)
            throw new ArgumentException("Effective to date must be after effective from date", nameof(effectiveTo));

        Rate = rate;
        CalculationMethod = calculationMethod;
        EffectiveFrom = effectiveFrom;
        EffectiveTo = effectiveTo;
    }

    public bool IsEffectiveOn(DateTime date)
    {
        return date >= EffectiveFrom && (!EffectiveTo.HasValue || date <= EffectiveTo.Value);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Rate;
        yield return CalculationMethod;
        yield return EffectiveFrom;
        yield return EffectiveTo ?? DateTime.MaxValue;
    }
}

/// <summary>
/// Value object representing TDS threshold amounts
/// </summary>
public class TdsThreshold : ValueObject
{
    public Money ThresholdAmount { get; private set; }
    public Money AnnualThresholdAmount { get; private set; }
    public EntityType EntityType { get; private set; }
    public bool HasPan { get; private set; }

    private TdsThreshold() { }

    public TdsThreshold(Money thresholdAmount, Money annualThresholdAmount, EntityType entityType, bool hasPan = true)
    {
        if (thresholdAmount.Amount < 0)
            throw new ArgumentException("Threshold amount cannot be negative", nameof(thresholdAmount));

        if (annualThresholdAmount.Amount < 0)
            throw new ArgumentException("Annual threshold amount cannot be negative", nameof(annualThresholdAmount));

        if (thresholdAmount.Currency != annualThresholdAmount.Currency)
            throw new ArgumentException("Threshold amounts must have the same currency");

        ThresholdAmount = thresholdAmount;
        AnnualThresholdAmount = annualThresholdAmount;
        EntityType = entityType;
        HasPan = hasPan;
    }

    public bool IsApplicable(Money amount, EntityType entityType, bool hasPan)
    {
        return EntityType == entityType &&
               HasPan == hasPan &&
               amount.Amount >= ThresholdAmount.Amount;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ThresholdAmount;
        yield return AnnualThresholdAmount;
        yield return EntityType;
        yield return HasPan;
    }
}

/// <summary>
/// Value object representing HSN code details
/// </summary>
public class HsnCodeDetails : ValueObject
{
    public string Code { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; }
    public GstRate ApplicableGstRate { get; private set; }

    private HsnCodeDetails() { }

    public HsnCodeDetails(string code, string description, string category, GstRate applicableGstRate)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentException("HSN code cannot be empty", nameof(code));

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("HSN description cannot be empty", nameof(description));

        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("HSN category cannot be empty", nameof(category));

        // Validate HSN code format (should be 4, 6, or 8 digits)
        if (!IsValidHsnCode(code))
            throw new ArgumentException("Invalid HSN code format", nameof(code));

        Code = code.Trim();
        Description = description.Trim();
        Category = category.Trim();
        ApplicableGstRate = applicableGstRate;
    }

    private static bool IsValidHsnCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        var cleanCode = code.Trim();
        return (cleanCode.Length == 4 || cleanCode.Length == 6 || cleanCode.Length == 8) &&
               cleanCode.All(char.IsDigit);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Code;
        yield return Description;
        yield return Category;
        yield return ApplicableGstRate;
    }
}

/// <summary>
/// Value object representing tax jurisdiction
/// </summary>
public class TaxJurisdiction : ValueObject
{
    public string Country { get; private set; }
    public string State { get; private set; }
    public string City { get; private set; }
    public LocationType Type { get; private set; }

    private TaxJurisdiction() { }

    public TaxJurisdiction(string country, string state = "", string city = "", LocationType type = LocationType.Country)
    {
        if (string.IsNullOrWhiteSpace(country))
            throw new ArgumentException("Country cannot be empty", nameof(country));

        Country = country.Trim();
        State = state?.Trim() ?? string.Empty;
        City = city?.Trim() ?? string.Empty;
        Type = type;
    }

    public string GetFullJurisdiction()
    {
        var parts = new List<string> { Country };

        if (!string.IsNullOrWhiteSpace(State))
            parts.Add(State);

        if (!string.IsNullOrWhiteSpace(City))
            parts.Add(City);

        return string.Join(", ", parts);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Country;
        yield return State;
        yield return City;
        yield return Type;
    }
}


