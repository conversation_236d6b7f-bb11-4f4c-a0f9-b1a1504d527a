namespace DataStorage.Domain.Exceptions;

public abstract class DataStorageDomainException : Exception
{
    protected DataStorageDomainException(string message) : base(message) { }
    protected DataStorageDomainException(string message, Exception innerException) : base(message, innerException) { }
}

public class DocumentNotFoundException : DataStorageDomainException
{
    public DocumentNotFoundException(Guid documentId) 
        : base($"Document with ID '{documentId}' was not found.")
    {
    }
}

public class DocumentAccessDeniedException : DataStorageDomainException
{
    public DocumentAccessDeniedException(Guid documentId, Guid userId)
        : base($"User '{userId}' does not have access to document '{documentId}'.")
    {
    }
}

public class DocumentVersionNotFoundException : DataStorageDomainException
{
    public DocumentVersionNotFoundException(Guid documentId, int version)
        : base($"Version '{version}' of document '{documentId}' was not found.")
    {
    }
}

public class InvalidDocumentOperationException : DataStorageDomainException
{
    public InvalidDocumentOperationException(string operation, string reason)
        : base($"Cannot perform operation '{operation}': {reason}")
    {
    }
}

public class FileStorageException : DataStorageDomainException
{
    public FileStorageException(string message) : base(message) { }
    public FileStorageException(string message, Exception innerException) : base(message, innerException) { }
}

public class FileNotFoundException : FileStorageException
{
    public FileNotFoundException(string filePath)
        : base($"File not found at path: {filePath}")
    {
    }
}

public class FileUploadException : FileStorageException
{
    public FileUploadException(string fileName, string reason)
        : base($"Failed to upload file '{fileName}': {reason}")
    {
    }

    public FileUploadException(string fileName, Exception innerException)
        : base($"Failed to upload file '{fileName}'", innerException)
    {
    }
}

public class FileDownloadException : FileStorageException
{
    public FileDownloadException(string filePath, string reason)
        : base($"Failed to download file from '{filePath}': {reason}")
    {
    }

    public FileDownloadException(string filePath, Exception innerException)
        : base($"Failed to download file from '{filePath}'", innerException)
    {
    }
}

public class InvalidFileFormatException : FileStorageException
{
    public InvalidFileFormatException(string fileName, string expectedFormat, string actualFormat)
        : base($"File '{fileName}' has invalid format. Expected: {expectedFormat}, Actual: {actualFormat}")
    {
    }
}

public class FileSizeExceededException : FileStorageException
{
    public FileSizeExceededException(string fileName, long actualSize, long maxSize)
        : base($"File '{fileName}' size ({actualSize} bytes) exceeds maximum allowed size ({maxSize} bytes)")
    {
    }
}

public class StorageQuotaExceededException : FileStorageException
{
    public StorageQuotaExceededException(string containerName, long currentUsage, long quota)
        : base($"Storage quota exceeded for container '{containerName}'. Current usage: {currentUsage} bytes, Quota: {quota} bytes")
    {
    }
}

public class DocumentProcessingException : DataStorageDomainException
{
    public DocumentProcessingException(string operation, string fileName, string reason)
        : base($"Failed to {operation} document '{fileName}': {reason}")
    {
    }

    public DocumentProcessingException(string operation, string fileName, Exception innerException)
        : base($"Failed to {operation} document '{fileName}'", innerException)
    {
    }
}

public class UnsupportedDocumentTypeException : DocumentProcessingException
{
    public UnsupportedDocumentTypeException(string fileName, string contentType)
        : base("process", fileName, $"Unsupported content type: {contentType}")
    {
    }
}
