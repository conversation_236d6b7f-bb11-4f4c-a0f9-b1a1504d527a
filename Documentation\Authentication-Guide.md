# 🔐 Authentication & Authorization Guide

## 📋 Overview

The TLI platform uses JWT (JSON Web Token) based authentication with role-based access control (RBAC). This guide provides comprehensive information for implementing authentication in frontend applications.

## 🏗️ Authentication Architecture

### Components

1. **Identity Service** - Central authentication service
2. **API Gateway** - Token validation and routing
3. **Individual Services** - Resource-level authorization
4. **Frontend Applications** - Token management and UI

### Flow Diagram

```
Frontend App → API Gateway → Identity Service → Database
     ↓              ↓              ↓
Token Storage → Token Validation → User Verification
```

## 🔑 JWT Token Structure

### Access Token Claims

```json
{
  "sub": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "username": "john.doe",
  "role": "Broker",
  "permissions": [
    "users.read.own",
    "orders.create",
    "orders.read.own",
    "trips.track"
  ],
  "company_id": "company-uuid",
  "subscription_tier": "Premium",
  "subscription_status": "Active",
  "profile_status": "Approved",
  "iat": **********,
  "exp": **********,
  "iss": "TLI-Identity-Service",
  "aud": "TLI-Platform",
  "jti": "token-uuid"
}
```

### Refresh Token Claims

```json
{
  "sub": "123e4567-e89b-12d3-a456-************",
  "token_type": "refresh",
  "iat": **********,
  "exp": 1643587200,
  "iss": "TLI-Identity-Service",
  "aud": "TLI-Platform",
  "jti": "refresh-token-uuid"
}
```

## 👥 User Roles & Permissions

### Role Hierarchy

```
Admin (Highest)
├── System Administrator
├── KYC Officer
└── Support Agent

Broker
├── Senior Broker
└── Junior Broker

Transporter
├── Fleet Manager
├── Operations Manager
└── Driver Supervisor

Shipper
├── Logistics Manager
└── Shipping Coordinator

Driver (Lowest)
```

### Permission Matrix

| Resource | Action | Admin | Broker | Transporter | Shipper | Driver |
|----------|--------|-------|--------|-------------|---------|--------|
| **Users** |
| users.read.all | View all users | ✅ | ❌ | ❌ | ❌ | ❌ |
| users.read.own | View own profile | ✅ | ✅ | ✅ | ✅ | ✅ |
| users.create | Create user | ✅ | ❌ | ❌ | ❌ | ❌ |
| users.update.own | Update own profile | ✅ | ✅ | ✅ | ✅ | ✅ |
| users.update.all | Update any profile | ✅ | ❌ | ❌ | ❌ | ❌ |
| users.delete | Delete user | ✅ | ❌ | ❌ | ❌ | ❌ |
| users.kyc.approve | Approve KYC | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Orders** |
| orders.create | Create RFQ/Order | ✅ | ✅ | ❌ | ✅ | ❌ |
| orders.read.all | View all orders | ✅ | ❌ | ❌ | ❌ | ❌ |
| orders.read.own | View own orders | ✅ | ✅ | ✅ | ✅ | ❌ |
| orders.read.assigned | View assigned orders | ✅ | ✅ | ✅ | ✅ | ✅ |
| orders.bid.create | Submit bids | ✅ | ✅ | ✅ | ❌ | ❌ |
| orders.bid.manage | Accept/Reject bids | ✅ | ✅ | ❌ | ✅ | ❌ |
| orders.force_award | Force award order | ✅ | ❌ | ❌ | ❌ | ❌ |
| **Trips** |
| trips.create | Create trip | ✅ | ❌ | ✅ | ❌ | ❌ |
| trips.read.all | View all trips | ✅ | ❌ | ❌ | ❌ | ❌ |
| trips.read.assigned | View assigned trips | ✅ | ✅ | ✅ | ✅ | ✅ |
| trips.update | Update trip status | ✅ | ❌ | ✅ | ❌ | ✅ |
| trips.track | Track trips | ✅ | ✅ | ✅ | ✅ | ✅ |
| trips.pod.submit | Submit POD | ✅ | ❌ | ✅ | ❌ | ✅ |
| trips.pod.approve | Approve POD | ✅ | ✅ | ❌ | ✅ | ❌ |

## 🔐 Authentication Endpoints

### 1. User Registration

```http
POST /api/v1/identity/auth/register
Content-Type: application/json

{
  "username": "john.doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phoneNumber": "+91-9876543210",
  "countryCode": "+91",
  "firstName": "John",
  "lastName": "Doe",
  "userType": "Broker|Transporter|Shipper",
  "companyName": "ABC Logistics",
  "acceptTerms": true,
  "marketingConsent": false
}
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "message": "Registration successful. Please verify your email.",
  "verificationRequired": true,
  "nextSteps": [
    "Check your email for verification link",
    "Complete profile setup",
    "Submit KYC documents"
  ]
}
```

### 2. User Login

```http
POST /api/v1/identity/auth/login
Content-Type: application/json

{
  "username": "john.doe",
  "password": "SecurePassword123!",
  "deviceInfo": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "rememberMe": true,
  "twoFactorCode": "123456"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "refreshExpiresIn": 2592000,
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "username": "john.doe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "Broker",
    "permissions": ["users.read.own", "orders.create"],
    "profileStatus": "Approved",
    "subscriptionTier": "Premium",
    "companyId": "company-uuid",
    "lastLoginAt": "2024-01-01T10:00:00Z"
  },
  "requiresProfileCompletion": false,
  "requiresKYCSubmission": false
}
```

### 3. Token Refresh

```http
POST /api/v1/identity/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "refreshExpiresIn": 2592000
}
```

### 4. Logout

```http
POST /api/v1/identity/auth/logout
Authorization: Bearer <access-token>
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "logoutFromAllDevices": false
}
```

### 5. Password Reset

```http
POST /api/v1/identity/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

```http
POST /api/v1/identity/auth/reset-password
Content-Type: application/json

{
  "token": "reset-token-from-email",
  "newPassword": "NewSecurePassword123!",
  "confirmPassword": "NewSecurePassword123!"
}
```

## 📱 Frontend Implementation

### 1. Token Storage

```typescript
// Token storage utility
class TokenStorage {
  private static ACCESS_TOKEN_KEY = 'tli_access_token';
  private static REFRESH_TOKEN_KEY = 'tli_refresh_token';
  private static USER_KEY = 'tli_user';

  static setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setUser(user: User): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  static getUser(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}
```

### 2. HTTP Interceptor

```typescript
// Axios interceptor for automatic token handling
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

class ApiClient {
  private static instance: ApiClient;
  private axiosInstance;

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: 'http://localhost:5000/api/v1',
      timeout: 30000,
    });

    this.setupInterceptors();
  }

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        const token = TokenStorage.getAccessToken();
        if (token && !TokenStorage.isTokenExpired(token)) {
          config.headers = config.headers || {};
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = TokenStorage.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              TokenStorage.setTokens(
                response.data.accessToken,
                response.data.refreshToken
              );
              
              originalRequest.headers.Authorization = 
                `Bearer ${response.data.accessToken}`;
              
              return this.axiosInstance(originalRequest);
            }
          } catch (refreshError) {
            TokenStorage.clearTokens();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshToken(refreshToken: string) {
    return axios.post('/api/v1/identity/auth/refresh', {
      refreshToken
    });
  }

  get axios() {
    return this.axiosInstance;
  }
}
```

### 3. Authentication Service

```typescript
interface LoginRequest {
  username: string;
  password: string;
  deviceInfo?: string;
  rememberMe?: boolean;
}

interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
  requiresProfileCompletion: boolean;
  requiresKYCSubmission: boolean;
}

class AuthService {
  private apiClient = ApiClient.getInstance();

  async login(request: LoginRequest): Promise<LoginResponse> {
    const response = await this.apiClient.axios.post(
      '/identity/auth/login',
      request
    );
    
    const { accessToken, refreshToken, user } = response.data;
    
    TokenStorage.setTokens(accessToken, refreshToken);
    TokenStorage.setUser(user);
    
    return response.data;
  }

  async logout(): Promise<void> {
    const refreshToken = TokenStorage.getRefreshToken();
    
    try {
      await this.apiClient.axios.post('/identity/auth/logout', {
        refreshToken
      });
    } finally {
      TokenStorage.clearTokens();
    }
  }

  async register(request: RegisterRequest): Promise<RegisterResponse> {
    const response = await this.apiClient.axios.post(
      '/identity/auth/register',
      request
    );
    return response.data;
  }

  getCurrentUser(): User | null {
    return TokenStorage.getUser();
  }

  isAuthenticated(): boolean {
    const token = TokenStorage.getAccessToken();
    return token !== null && !TokenStorage.isTokenExpired(token);
  }

  hasPermission(permission: string): boolean {
    const user = this.getCurrentUser();
    return user?.permissions?.includes(permission) || false;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getCurrentUser();
    return roles.includes(user?.role || '');
  }
}
```

### 4. Route Protection

```typescript
// React Router guard component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  requiredRoles,
  fallback = <Navigate to="/unauthorized" />
}) => {
  const authService = new AuthService();

  if (!authService.isAuthenticated()) {
    return <Navigate to="/login" />;
  }

  if (requiredPermission && !authService.hasPermission(requiredPermission)) {
    return fallback;
  }

  if (requiredRole && !authService.hasRole(requiredRole)) {
    return fallback;
  }

  if (requiredRoles && !authService.hasAnyRole(requiredRoles)) {
    return fallback;
  }

  return <>{children}</>;
};

// Usage in routing
<Routes>
  <Route path="/login" element={<LoginPage />} />
  <Route path="/dashboard" element={
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  } />
  <Route path="/admin" element={
    <ProtectedRoute requiredRole="Admin">
      <AdminPanel />
    </ProtectedRoute>
  } />
  <Route path="/orders/create" element={
    <ProtectedRoute requiredPermission="orders.create">
      <CreateOrder />
    </ProtectedRoute>
  } />
</Routes>
```

## 🔒 Security Best Practices

### 1. Token Security

- Store tokens in `localStorage` for web apps
- Use secure storage for mobile apps
- Implement automatic token refresh
- Clear tokens on logout
- Validate token expiry before requests

### 2. API Security

- Always use HTTPS in production
- Implement CSRF protection
- Validate all user inputs
- Use rate limiting
- Log security events

### 3. Permission Checking

- Check permissions on both frontend and backend
- Use principle of least privilege
- Implement context-aware permissions
- Regular permission audits

### 4. Session Management

- Implement session timeout
- Support multiple device sessions
- Provide session management UI
- Log suspicious activities

## ❌ Error Handling

### Common Authentication Errors

| Code | Error | Description | Action |
|------|-------|-------------|--------|
| 401 | `INVALID_CREDENTIALS` | Wrong username/password | Show error message |
| 401 | `ACCOUNT_LOCKED` | Account temporarily locked | Show unlock instructions |
| 401 | `EMAIL_NOT_VERIFIED` | Email verification pending | Redirect to verification |
| 401 | `TOKEN_EXPIRED` | Access token expired | Refresh token automatically |
| 401 | `TOKEN_INVALID` | Invalid token format | Clear tokens, redirect to login |
| 403 | `INSUFFICIENT_PERMISSIONS` | Missing required permissions | Show access denied message |
| 403 | `ACCOUNT_SUSPENDED` | Account suspended | Show contact support message |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many login attempts | Show retry after message |

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid username or password",
    "details": "Please check your credentials and try again",
    "timestamp": "2024-01-01T10:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
