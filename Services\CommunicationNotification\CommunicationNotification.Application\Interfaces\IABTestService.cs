using CommunicationNotification.Domain.Entities;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for A/B testing functionality
/// </summary>
public interface IABTestService
{
    /// <summary>
    /// Create a new A/B test
    /// </summary>
    Task<ABTest> CreateTestAsync(
        string name,
        string description,
        ABTestType type,
        DateTime startDate,
        int targetSampleSize,
        decimal trafficAllocation,
        string hypothesisStatement,
        string successMetric,
        Guid createdByUserId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Add variant to A/B test
    /// </summary>
    Task<ABTest> AddVariantAsync(
        Guid testId,
        string variantName,
        string description,
        decimal trafficAllocation,
        bool isControl,
        Dictionary<string, object> configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Start A/B test
    /// </summary>
    Task<ABTest> StartTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Stop A/B test
    /// </summary>
    Task<ABTest> StopTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get variant assignment for user
    /// </summary>
    Task<ABTestVariant?> GetVariantForUserAsync(
        Guid testId,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Record A/B test event (message sent, delivered, read, etc.)
    /// </summary>
    Task RecordTestEventAsync(
        Guid testId,
        string variantName,
        Guid userId,
        string eventType,
        Dictionary<string, object>? eventData = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate A/B test results with statistical analysis
    /// </summary>
    Task<ABTestResults> CalculateTestResultsAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test performance comparison
    /// </summary>
    Task<Dictionary<string, MessagePerformanceMetrics>> GetTestPerformanceAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate required sample size for A/B test
    /// </summary>
    Task<SampleSizeCalculation> CalculateSampleSizeAsync(
        decimal baselineConversionRate,
        decimal minimumDetectableEffect,
        decimal significanceLevel = 0.05m,
        decimal power = 0.8m,
        int numberOfVariants = 2,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all active A/B tests
    /// </summary>
    Task<List<ABTest>> GetActiveTestsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test by ID
    /// </summary>
    Task<ABTest?> GetTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update A/B test configuration
    /// </summary>
    Task<ABTest> UpdateTestAsync(
        Guid testId,
        ABTest updatedTest,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete A/B test
    /// </summary>
    Task DeleteTestAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test history for user
    /// </summary>
    Task<List<ABTest>> GetUserTestHistoryAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user is eligible for A/B test
    /// </summary>
    Task<bool> IsUserEligibleAsync(
        Guid testId,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test insights and recommendations
    /// </summary>
    Task<Dictionary<string, object>> GetTestInsightsAsync(
        Guid testId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for A/B tests
/// </summary>
public interface IABTestRepository
{
    /// <summary>
    /// Add new A/B test
    /// </summary>
    Task<ABTest> AddAsync(
        ABTest test,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update A/B test
    /// </summary>
    Task<ABTest> UpdateAsync(
        ABTest test,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test by ID
    /// </summary>
    Task<ABTest?> GetByIdAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all A/B tests
    /// </summary>
    Task<List<ABTest>> GetAllAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active A/B tests
    /// </summary>
    Task<List<ABTest>> GetActiveTestsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B tests by status
    /// </summary>
    Task<List<ABTest>> GetByStatusAsync(
        ABTestStatus status,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B tests created by user
    /// </summary>
    Task<List<ABTest>> GetByCreatedByUserAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete A/B test
    /// </summary>
    Task DeleteAsync(
        Guid testId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if test name exists
    /// </summary>
    Task<bool> ExistsAsync(
        string testName,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for statistical analysis of A/B tests
/// </summary>
public interface IStatisticalAnalysisService
{
    /// <summary>
    /// Perform Z-test for conversion rate comparison
    /// </summary>
    Task<ABTestResults> PerformZTestAsync(
        Dictionary<string, VariantStatistics> variantStats,
        StatisticalTestConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Perform T-test for continuous metrics comparison
    /// </summary>
    Task<ABTestResults> PerformTTestAsync(
        Dictionary<string, VariantStatistics> variantStats,
        StatisticalTestConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Perform Chi-square test for categorical data
    /// </summary>
    Task<ABTestResults> PerformChiSquareTestAsync(
        Dictionary<string, VariantStatistics> variantStats,
        StatisticalTestConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate sample size requirements
    /// </summary>
    Task<SampleSizeCalculation> CalculateSampleSizeAsync(
        decimal baselineRate,
        decimal minimumDetectableEffect,
        StatisticalTestConfig config,
        int numberOfVariants,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate confidence intervals
    /// </summary>
    Task<(decimal Lower, decimal Upper)> CalculateConfidenceIntervalAsync(
        decimal conversionRate,
        int sampleSize,
        decimal confidenceLevel = 0.95m,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate statistical power
    /// </summary>
    Task<decimal> CalculateStatisticalPowerAsync(
        decimal effectSize,
        int sampleSize,
        decimal significanceLevel,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Detect early stopping criteria
    /// </summary>
    Task<bool> ShouldStopEarlyAsync(
        Dictionary<string, VariantStatistics> variantStats,
        StatisticalTestConfig config,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for A/B test event tracking
/// </summary>
public interface IABTestEventService
{
    /// <summary>
    /// Track A/B test assignment event
    /// </summary>
    Task TrackAssignmentAsync(
        Guid testId,
        string variantName,
        Guid userId,
        DateTime timestamp,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Track A/B test conversion event
    /// </summary>
    Task TrackConversionAsync(
        Guid testId,
        string variantName,
        Guid userId,
        string eventType,
        Dictionary<string, object>? eventData = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test events for analysis
    /// </summary>
    Task<List<ABTestEvent>> GetTestEventsAsync(
        Guid testId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user's A/B test assignments
    /// </summary>
    Task<List<ABTestAssignment>> GetUserAssignmentsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// A/B test event entity
/// </summary>
public class ABTestEvent
{
    public Guid Id { get; set; }
    public Guid TestId { get; set; }
    public string VariantName { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> EventData { get; set; } = new();
}

/// <summary>
/// A/B test assignment entity
/// </summary>
public class ABTestAssignment
{
    public Guid Id { get; set; }
    public Guid TestId { get; set; }
    public string TestName { get; set; } = string.Empty;
    public string VariantName { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public DateTime AssignedAt { get; set; }
    public bool IsActive { get; set; }
}

