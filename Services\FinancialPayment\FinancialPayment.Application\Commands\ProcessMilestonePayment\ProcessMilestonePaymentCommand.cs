using MediatR;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.ProcessMilestonePayment;

public record ProcessMilestonePaymentCommand : IRequest<ProcessMilestonePaymentResponse>
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string MilestoneType { get; init; } = string.Empty; // "Pickup", "Delivery", "InTransit", "Documentation"
    public string TriggerEvent { get; init; } = string.Empty; // "TripStarted", "TripCompleted", "DocumentUploaded"
    public Dictionary<string, object> EventData { get; init; } = new();
    public Guid TriggeredBy { get; init; }
    public DateTime? EventTimestamp { get; init; }
    public bool ForceProcess { get; init; } = false;
}

public record ProcessMilestonePaymentResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public List<MilestonePaymentResult> ProcessedMilestones { get; init; } = new();
    public decimal TotalAmountReleased { get; init; }
    public string Currency { get; init; } = string.Empty;
}

public record MilestonePaymentResult
{
    public Guid MilestoneId { get; init; }
    public string Description { get; init; } = string.Empty;
    public decimal Amount { get; init; }
    public string Currency { get; init; } = string.Empty;
    public bool PaymentReleased { get; init; }
    public string Status { get; init; } = string.Empty;
    public string? ErrorMessage { get; init; }
    public DateTime ProcessedAt { get; init; }
}
