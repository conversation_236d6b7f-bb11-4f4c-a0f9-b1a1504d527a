# Subscription Services - Complete Setup Guide

## 📋 Overview

This repository contains comprehensive documentation for setting up and working with the Subscription Management Service in the TLI Logistics microservices platform.

## 📚 Documentation Structure

### 1. **SUBSCRIPTION_SERVICES_WORKFLOW.md**
**Complete step-by-step workflow** for setting up subscription services from scratch.
- Environment setup and prerequisites
- Database configuration
- Service dependencies
- Configuration management
- Testing and verification
- Production deployment considerations

### 2. **SUBSCRIPTION_QUICK_START.md**
**5-minute quick start guide** for developers who need to get up and running fast.
- Essential commands and configurations
- Key API endpoints
- Common workflows
- Basic troubleshooting

### 3. **SUBSCRIPTION_TROUBLESHOOTING.md**
**Comprehensive troubleshooting guide** for resolving common issues.
- Service startup problems
- Authentication issues
- Database connectivity
- API Gateway routing
- Payment integration
- Performance optimization

## 🚀 Getting Started

### For New Developers
1. Start with **SUBSCRIPTION_QUICK_START.md** for immediate setup
2. Reference **SUBSCRIPTION_SERVICES_WORKFLOW.md** for detailed understanding
3. Keep **SUBSCRIPTION_TROUBLESHOOTING.md** handy for issue resolution

### For Experienced Developers
1. Use **SUBSCRIPTION_QUICK_START.md** for rapid deployment
2. Refer to specific sections in **SUBSCRIPTION_SERVICES_WORKFLOW.md** as needed
3. Consult **SUBSCRIPTION_TROUBLESHOOTING.md** for complex issues

## 🏗️ Architecture Overview

The Subscription Management Service is built with:

### Core Components
- **Domain Layer**: Business entities (Subscription, Plan, Payment)
- **Application Layer**: Use cases and business logic
- **Infrastructure Layer**: Data persistence and external integrations
- **API Layer**: REST endpoints and controllers

### Key Features
- ✅ Multi-tier subscription plans (Basic, Pro, Enterprise)
- ✅ Multiple user types (Transport Companies, Brokers, Carriers)
- ✅ Automated billing and renewal
- ✅ Payment processing with RazorPay
- ✅ Feature access control and usage limits
- ✅ Trial period support
- ✅ Plan upgrades/downgrades with proration
- ✅ Event-driven architecture with RabbitMQ

### Technology Stack
- **.NET 8**: Latest framework
- **PostgreSQL**: Primary database
- **Entity Framework Core**: ORM
- **RazorPay**: Payment gateway
- **RabbitMQ**: Message broker
- **JWT**: Authentication
- **Swagger**: API documentation

## 🔧 Service Dependencies

### Required Services
1. **PostgreSQL Database** - Data persistence
2. **Identity Service** (Port 5001) - JWT authentication
3. **User Management Service** (Port 5002) - User profile data

### Optional Services
1. **API Gateway** (Port 5000) - Request routing and load balancing
2. **RabbitMQ** - Event-driven messaging
3. **Redis** - Caching and session management

## 📡 API Endpoints Summary

### Public Endpoints
```
GET /api/plans                           # Get all public plans
GET /api/plans/{id}                      # Get specific plan
GET /api/plans/by-user-type/{userType}   # Get plans by user type
GET /health                              # Health check
```

### Authenticated Endpoints
```
POST /api/subscriptions                  # Create subscription
GET /api/subscriptions/my-subscription   # Get user's subscription
POST /api/subscriptions/{id}/cancel      # Cancel subscription
POST /api/subscriptions/{id}/upgrade     # Upgrade subscription
POST /api/subscriptions/{id}/downgrade   # Downgrade subscription
GET /api/subscriptions/{id}/usage        # Get usage statistics
```

### Admin Endpoints
```
GET /api/subscriptions                   # Get all subscriptions
POST /api/plans                          # Create plan
PUT /api/plans/{id}                      # Update plan
POST /api/plans/{id}/features            # Add plan feature
```

## 💰 Subscription Plans

### Transport Company Plans
| Plan | Price | RFQs | Features |
|------|-------|------|----------|
| Basic | ₹999/month | 10 | Basic tracking, Standard support |
| Pro | ₹2,999/month | Unlimited | Advanced features, Priority support |
| Enterprise | ₹9,999/month | Unlimited | Custom features, Dedicated support |

### Broker Plans
| Plan | Price | RFQs | Features |
|------|-------|------|----------|
| Basic | ₹1,499/month | 20 | Basic features, Standard support |
| Pro | ₹4,999/month | Unlimited | Advanced analytics, Priority support |
| Enterprise | ₹14,999/month | Unlimited | Full access, API access, Dedicated support |

### Carrier Plans
| Plan | Price | RFQs | Vehicles | Features |
|------|-------|------|----------|----------|
| Basic | ₹799/month | 15 | 2 | Basic tracking, Standard support |
| Pro | ₹2,499/month | Unlimited | 10 | Route optimization, Priority support |
| Enterprise | ₹7,999/month | Unlimited | Unlimited | Fleet management, Volume pricing |

## 🔄 Common Workflows

### 1. Create Subscription
```bash
# Get available plans
curl http://localhost:5003/api/plans/by-user-type/TransportCompany

# Create subscription
curl -X POST "http://localhost:5003/api/subscriptions" \
  -H "Authorization: Bearer JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"planId": "PLAN_ID", "autoRenew": true}'
```

### 2. Upgrade Subscription
```bash
curl -X POST "http://localhost:5003/api/subscriptions/{id}/upgrade" \
  -H "Authorization: Bearer JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"newPlanId": "HIGHER_TIER_PLAN_ID"}'
```

### 3. Check Usage
```bash
curl -X GET "http://localhost:5003/api/subscriptions/{id}/usage" \
  -H "Authorization: Bearer JWT_TOKEN"
```

## 🚨 Quick Troubleshooting

### Service Won't Start
```bash
# Check prerequisites
dotnet --version  # Should be 8.x.x
psql --version    # Should show PostgreSQL

# Check port availability
netstat -an | findstr :5003

# Verify database connection
psql -h localhost -U timescale -d TLI_SubscriptionManagement -c "SELECT 1;"
```

### Authentication Issues
```bash
# Test JWT token generation
curl -X POST "http://localhost:5001/api/identity/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "Admin@123"}'

# Verify JWT settings match across services
```

### Database Issues
```bash
# Reset database
psql -h localhost -U timescale -d postgres -c "DROP DATABASE IF EXISTS TLI_SubscriptionManagement;"
cd Services/SubscriptionManagement
./setup.ps1
```

## 📈 Monitoring and Health Checks

### Health Endpoints
- Subscription Service: `http://localhost:5003/health`
- Identity Service: `http://localhost:5001/health`
- User Management: `http://localhost:5002/health`
- API Gateway: `http://localhost:5000/health`

### Key Metrics to Monitor
- Subscription creation rate
- Payment success/failure rates
- API response times
- Database connection health
- Memory and CPU usage
- Active subscriptions count

## 🔐 Security Considerations

### Production Checklist
- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS only
- [ ] Secure database connections with SSL
- [ ] Use environment variables for sensitive data
- [ ] Implement rate limiting
- [ ] Enable CORS properly
- [ ] Use strong database passwords
- [ ] Secure RazorPay webhook endpoints

## 🚀 Deployment Options

### Local Development
```bash
cd Services/SubscriptionManagement
./setup.ps1 -RunService
```

### Docker Deployment
```bash
docker-compose up -d
```

### Production Deployment
- Use environment variables for configuration
- Implement proper logging and monitoring
- Set up automated backups
- Configure load balancing
- Enable SSL/TLS

## 📞 Support and Resources

### Documentation Files
- **SUBSCRIPTION_SERVICES_WORKFLOW.md** - Complete setup workflow
- **SUBSCRIPTION_QUICK_START.md** - Quick start guide
- **SUBSCRIPTION_TROUBLESHOOTING.md** - Troubleshooting guide
- **Services/SubscriptionManagement/README.md** - Service architecture
- **Services/SubscriptionManagement/SETUP.md** - Detailed setup instructions

### API Documentation
- Swagger UI: `http://localhost:5003` (when service is running)
- API Gateway documentation: `http://localhost:5000`

### Additional Resources
- RazorPay Documentation: https://razorpay.com/docs/
- .NET 8 Documentation: https://docs.microsoft.com/en-us/dotnet/
- PostgreSQL Documentation: https://www.postgresql.org/docs/

---

**Quick Start Command:**
```bash
cd Services/SubscriptionManagement && ./setup.ps1 -RunService
```

**Need Help?** Check the troubleshooting guide or review service logs for detailed error information.
