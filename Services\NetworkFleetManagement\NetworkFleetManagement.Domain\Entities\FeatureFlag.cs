using Shared.Domain;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

public class FeatureFlag : BaseEntity
{
    public string Key { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public bool IsEnabled { get; private set; }
    public bool IsArchived { get; private set; }
    
    // Rollout configuration
    public double RolloutPercentage { get; private set; } = 0.0;
    public List<Guid> WhitelistedUsers { get; private set; } = new();
    public List<Guid> BlacklistedUsers { get; private set; } = new();
    
    // A/B Testing
    public Dictionary<string, double> VariantWeights { get; private set; } = new();
    public Dictionary<string, object> VariantConfigurations { get; private set; } = new();
    
    // Targeting rules
    public Dictionary<string, object> TargetingRules { get; private set; } = new();
    
    // Usage tracking
    public List<FeatureFlagUsage> UsageHistory { get; private set; } = new();
    public DateTime? LastUsedAt { get; private set; }
    public int TotalUsageCount { get; private set; }
    
    // Metadata
    public string Environment { get; private set; } = "production";
    public string? Tags { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    private FeatureFlag() { } // EF Core

    public FeatureFlag(string key, string name, string description, bool isEnabled = false)
    {
        Key = key;
        Name = name;
        Description = description;
        IsEnabled = isEnabled;
        Environment = "production";
    }

    public void Enable() => IsEnabled = true;
    public void Disable() => IsEnabled = false;
    public void Archive() => IsArchived = true;
    public void Unarchive() => IsArchived = false;

    public void SetRolloutPercentage(double percentage)
    {
        if (percentage < 0 || percentage > 100)
            throw new ArgumentException("Rollout percentage must be between 0 and 100");
        
        RolloutPercentage = percentage;
    }

    public void AddToWhitelist(Guid userId)
    {
        if (!WhitelistedUsers.Contains(userId))
            WhitelistedUsers.Add(userId);
    }

    public void RemoveFromWhitelist(Guid userId)
    {
        WhitelistedUsers.Remove(userId);
    }

    public void AddToBlacklist(Guid userId)
    {
        if (!BlacklistedUsers.Contains(userId))
            BlacklistedUsers.Add(userId);
    }

    public void RemoveFromBlacklist(Guid userId)
    {
        BlacklistedUsers.Remove(userId);
    }

    public void SetVariantWeights(Dictionary<string, double> weights)
    {
        var totalWeight = weights.Values.Sum();
        if (Math.Abs(totalWeight - 100.0) > 0.01)
            throw new ArgumentException("Variant weights must sum to 100");
        
        VariantWeights = weights;
    }

    public void SetVariantConfiguration(string variant, object configuration)
    {
        VariantConfigurations[variant] = configuration;
    }

    public void SetTargetingRules(Dictionary<string, object> rules)
    {
        TargetingRules = rules;
    }

    public void SetExpiration(DateTime expiresAt)
    {
        ExpiresAt = expiresAt;
    }

    public void SetTags(string tags)
    {
        Tags = tags;
    }

    public bool IsActiveForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        // Check if archived or expired
        if (IsArchived || (ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow))
            return false;

        // Check if globally disabled
        if (!IsEnabled)
            return false;

        // Check blacklist
        if (BlacklistedUsers.Contains(userId))
            return false;

        // Check whitelist
        if (WhitelistedUsers.Contains(userId))
            return true;

        // Check targeting rules
        if (TargetingRules.Any() && !EvaluateTargetingRules(userId, context))
            return false;

        // Check rollout percentage
        if (RolloutPercentage < 100.0)
        {
            var hash = GetUserHash(userId);
            var userPercentile = (hash % 100) + 1;
            return userPercentile <= RolloutPercentage;
        }

        return true;
    }

    public string? GetVariantForUser(Guid userId, Dictionary<string, object>? context = null)
    {
        if (!IsActiveForUser(userId, context) || !VariantWeights.Any())
            return null;

        var hash = GetUserHash(userId);
        var random = hash % 100;
        var cumulativeWeight = 0.0;

        foreach (var variant in VariantWeights)
        {
            cumulativeWeight += variant.Value;
            if (random < cumulativeWeight)
                return variant.Key;
        }

        return VariantWeights.Keys.FirstOrDefault();
    }

    public void RecordUsage(Guid userId, string? variant = null, Dictionary<string, object>? context = null)
    {
        var usage = new FeatureFlagUsage
        {
            UserId = userId,
            Variant = variant,
            Context = context,
            Timestamp = DateTime.UtcNow
        };

        UsageHistory.Add(usage);
        LastUsedAt = DateTime.UtcNow;
        TotalUsageCount++;
    }

    private bool EvaluateTargetingRules(Guid userId, Dictionary<string, object>? context)
    {
        // Simple rule evaluation - can be extended for complex scenarios
        if (context == null) return true;

        foreach (var rule in TargetingRules)
        {
            if (context.ContainsKey(rule.Key))
            {
                var contextValue = context[rule.Key]?.ToString();
                var ruleValue = rule.Value?.ToString();
                
                if (contextValue != ruleValue)
                    return false;
            }
        }

        return true;
    }

    private int GetUserHash(Guid userId)
    {
        return Math.Abs((Key + userId.ToString()).GetHashCode());
    }
}

public class FeatureFlagUsage
{
    public Guid UserId { get; set; }
    public string? Variant { get; set; }
    public Dictionary<string, object>? Context { get; set; }
    public DateTime Timestamp { get; set; }
}

