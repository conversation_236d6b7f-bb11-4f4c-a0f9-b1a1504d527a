using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IPaymentService
    {
        Task<PaymentResult> ProcessPaymentAsync(Payment payment, string? paymentMethodId = null);
        Task<PaymentResult> ProcessSubscriptionPaymentAsync(Subscription subscription, string? paymentMethodId = null);
        Task<PaymentResult> ProcessProrationPaymentAsync(Subscription subscription, decimal prorationAmount, string paymentMethodId);
        Task<RefundResult> RefundPaymentAsync(Payment payment, Money? refundAmount = null, string? reason = null);
        Task<RefundResult> ProcessRefundAsync(Payment payment, Money refundAmount, string? reason = null);
        Task<RefundResult> ProcessRefundAsync(Subscription subscription, decimal refundAmount);
        Task<PaymentResult> RetryPaymentAsync(Payment payment);
        Task<string> CreatePaymentIntentAsync(Money amount, Guid userId, string? paymentMethodId = null);
        Task<bool> ValidatePaymentMethodAsync(string paymentMethodId, Guid userId);
        Task<ValidationResult> ValidatePaymentMethodAsync(string paymentMethodId);
        Task<List<PaymentMethod>> GetUserPaymentMethodsAsync(Guid userId);
        Task<PaymentMethod> AddPaymentMethodAsync(Guid userId, string paymentMethodToken);
        Task RemovePaymentMethodAsync(Guid userId, string paymentMethodId);
    }

    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string? TransactionId { get; set; }
        public string? PaymentId { get; set; }
        public string? ErrorMessage { get; set; }
        public string? GatewayResponse { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class RefundResult
    {
        public bool IsSuccess { get; set; }
        public string? RefundId { get; set; }
        public Money RefundAmount { get; set; } = Money.Zero("INR");
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
    }

    public class ValidationResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class PaymentMethod
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Last4 { get; set; } = string.Empty;
        public string Brand { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
