using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;

namespace SubscriptionManagement.Application.Queries.GetPlanTaxDetails
{
    public class GetPlanTaxDetailsQueryHandler : IRequestHandler<GetPlanTaxDetailsQuery, PlanTaxDetailsDto?>
    {
        private readonly IPlanRepository _planRepository;
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly ITaxExemptionRepository _taxExemptionRepository;
        private readonly ILogger<GetPlanTaxDetailsQueryHandler> _logger;

        public GetPlanTaxDetailsQueryHandler(
            IPlanRepository planRepository,
            ITaxCalculationService taxCalculationService,
            ITaxExemptionRepository taxExemptionRepository,
            ILogger<GetPlanTaxDetailsQueryHandler> logger)
        {
            _planRepository = planRepository;
            _taxCalculationService = taxCalculationService;
            _taxExemptionRepository = taxExemptionRepository;
            _logger = logger;
        }

        public async Task<PlanTaxDetailsDto?> Handle(GetPlanTaxDetailsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting tax details for plan {PlanId} in region {Region}",
                request.PlanId, request.CustomerRegion);

            try
            {
                // Get the plan
                var plan = await _planRepository.GetByIdAsync(request.PlanId);
                if (plan == null)
                {
                    _logger.LogWarning("Plan {PlanId} not found", request.PlanId);
                    return null;
                }

                if (!plan.IsActive)
                {
                    _logger.LogWarning("Plan {PlanId} is not active", request.PlanId);
                    return null;
                }

                var calculationDate = request.CalculationDate ?? DateTime.UtcNow;

                // Get tax breakdown
                var taxBreakdown = await _taxCalculationService.GetTaxBreakdownAsync(
                    plan.Price, request.CustomerRegion, request.CustomerId, calculationDate);

                // Check for exemptions
                var hasExemptions = false;
                if (request.CustomerId.HasValue)
                {
                    var exemptions = await _taxExemptionRepository.GetActiveByUserIdAsync(request.CustomerId.Value);
                    hasExemptions = exemptions.Any(e => e.ApplicableRegions.Contains(request.CustomerRegion.ToUpperInvariant()));
                }

                // Get display information
                var displayInfo = await _taxCalculationService.GetTaxDisplayInfoAsync(
                    plan, request.CustomerRegion, request.CustomerId, calculationDate);

                var result = new PlanTaxDetailsDto
                {
                    PlanId = plan.Id,
                    PlanName = plan.Name,
                    BasePrice = plan.Price.Amount,
                    Currency = plan.Price.Currency,
                    CustomerRegion = request.CustomerRegion,
                    TaxBreakdown = taxBreakdown.TaxItems.Select(item => new TaxBreakdownItemDto
                    {
                        TaxType = item.TaxType,
                        TaxName = item.TaxName,
                        Rate = item.Rate,
                        TaxableAmount = item.TaxableAmount.Amount,
                        TaxAmount = item.TaxAmount.Amount,
                        IsExempt = item.IsExempt,
                        ExemptionReason = item.ExemptionReason
                    }).ToList(),
                    TotalTaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                    DisplayPrice = displayInfo.DisplayPrice.Amount,
                    IsTaxInclusive = displayInfo.IsTaxInclusive,
                    TaxDisplayText = displayInfo.TaxDisplayText,
                    TaxLabels = displayInfo.TaxLabels,
                    HasExemptions = hasExemptions,
                    CalculatedAt = calculationDate
                };

                _logger.LogInformation("Successfully retrieved tax details for plan {PlanId}", request.PlanId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tax details for plan {PlanId}", request.PlanId);
                throw;
            }
        }
    }
}
