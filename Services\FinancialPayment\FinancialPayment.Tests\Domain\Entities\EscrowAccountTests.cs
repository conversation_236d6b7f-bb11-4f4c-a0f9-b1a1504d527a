using FluentAssertions;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Events;
using Xunit;

namespace FinancialPayment.Tests.Domain.Entities;

public class EscrowAccountTests
{
    private readonly Guid _orderId = Guid.NewGuid();
    private readonly Guid _transportCompanyId = Guid.NewGuid();
    private readonly Guid _brokerId = Guid.NewGuid();
    private readonly Guid _carrierId = Guid.NewGuid();
    private readonly Money _totalAmount = new(10000m, "INR");

    [Fact]
    public void Constructor_ShouldCreateEscrowAccount_WithValidParameters()
    {
        // Act
        var escrowAccount = new EscrowAccount(
            _orderId,
            _transportCompanyId,
            _brokerId,
            _carrierId,
            _totalAmount,
            "Test escrow account");

        // Assert
        escrowAccount.OrderId.Should().Be(_orderId);
        escrowAccount.TransportCompanyId.Should().Be(_transportCompanyId);
        escrowAccount.BrokerId.Should().Be(_brokerId);
        escrowAccount.CarrierId.Should().Be(_carrierId);
        escrowAccount.TotalAmount.Should().Be(_totalAmount);
        escrowAccount.AvailableAmount.Should().Be(Money.Zero("INR"));
        escrowAccount.ReservedAmount.Should().Be(Money.Zero("INR"));
        escrowAccount.Status.Should().Be(EscrowStatus.Created);
        escrowAccount.EscrowAccountNumber.Should().NotBeNullOrEmpty();
        escrowAccount.Notes.Should().Be("Test escrow account");
    }

    [Fact]
    public void Constructor_ShouldThrowException_WhenTotalAmountIsZero()
    {
        // Arrange
        var zeroAmount = Money.Zero("INR");

        // Act & Assert
        var action = () => new EscrowAccount(
            _orderId,
            _transportCompanyId,
            _brokerId,
            _carrierId,
            zeroAmount);

        action.Should().Throw<ArgumentException>()
            .WithMessage("Total amount must be greater than zero");
    }

    [Fact]
    public void Fund_ShouldUpdateAmountAndStatus_WhenValidAmount()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var fundAmount = new Money(5000m, "INR");
        var transactionId = "txn_123";

        // Act
        escrowAccount.Fund(fundAmount, transactionId, "Initial funding");

        // Assert
        escrowAccount.AvailableAmount.Should().Be(fundAmount);
        escrowAccount.Status.Should().Be(EscrowStatus.PartiallyFunded);
        escrowAccount.Transactions.Should().HaveCount(1);
        escrowAccount.Transactions.First().Type.Should().Be(EscrowTransactionType.Fund);
        escrowAccount.Transactions.First().Amount.Should().Be(fundAmount);
        escrowAccount.Transactions.First().PaymentGatewayTransactionId.Should().Be(transactionId);
    }

    [Fact]
    public void Fund_ShouldMarkAsFunded_WhenFullAmountIsFunded()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var transactionId = "txn_123";

        // Act
        escrowAccount.Fund(_totalAmount, transactionId, "Full funding");

        // Assert
        escrowAccount.AvailableAmount.Should().Be(_totalAmount);
        escrowAccount.Status.Should().Be(EscrowStatus.Funded);
        escrowAccount.FundedAt.Should().NotBeNull();
        escrowAccount.FundedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Fund_ShouldThrowException_WhenAmountExceedsTotal()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var excessiveAmount = new Money(15000m, "INR");

        // Act & Assert
        var action = () => escrowAccount.Fund(excessiveAmount, "txn_123");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Funding amount exceeds total escrow amount");
    }

    [Fact]
    public void Fund_ShouldThrowException_WhenCurrencyMismatch()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var wrongCurrencyAmount = new Money(5000m, "USD");

        // Act & Assert
        var action = () => escrowAccount.Fund(wrongCurrencyAmount, "txn_123");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Funding currency must match escrow currency");
    }

    [Fact]
    public void Reserve_ShouldMoveAmountFromAvailableToReserved()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.Fund(_totalAmount, "txn_123");
        var reserveAmount = new Money(3000m, "INR");

        // Act
        escrowAccount.Reserve(reserveAmount, "Reserve for carrier payment");

        // Assert
        escrowAccount.AvailableAmount.Should().Be(new Money(7000m, "INR"));
        escrowAccount.ReservedAmount.Should().Be(reserveAmount);
        escrowAccount.Transactions.Should().HaveCount(2);
        escrowAccount.Transactions.Last().Type.Should().Be(EscrowTransactionType.Reserve);
    }

    [Fact]
    public void Reserve_ShouldThrowException_WhenNotFunded()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var reserveAmount = new Money(3000m, "INR");

        // Act & Assert
        var action = () => escrowAccount.Reserve(reserveAmount, "Reserve attempt");

        action.Should().Throw<InvalidOperationException>()
            .WithMessage("Can only reserve from funded or partially funded escrow accounts");
    }

    [Fact]
    public void Reserve_ShouldThrowException_WhenAmountExceedsAvailable()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.Fund(new Money(5000m, "INR"), "txn_123");
        var excessiveReserve = new Money(6000m, "INR");

        // Act & Assert
        var action = () => escrowAccount.Reserve(excessiveReserve, "Excessive reserve");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Cannot reserve more than available amount");
    }

    [Fact]
    public void Release_ShouldReleaseFromReservedAmount()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.Fund(_totalAmount, "txn_123");
        var reserveAmount = new Money(3000m, "INR");
        var releaseAmount = new Money(2000m, "INR");
        escrowAccount.Reserve(reserveAmount, "Reserve for payment");
        var recipientId = Guid.NewGuid();

        // Act
        escrowAccount.Release(releaseAmount, recipientId, "Payment to carrier", "release_txn_123");

        // Assert
        escrowAccount.ReservedAmount.Should().Be(new Money(1000m, "INR"));
        escrowAccount.AvailableAmount.Should().Be(new Money(7000m, "INR"));
        escrowAccount.Status.Should().Be(EscrowStatus.PartiallyReleased);
        escrowAccount.Transactions.Should().HaveCount(3);
        escrowAccount.Transactions.Last().Type.Should().Be(EscrowTransactionType.Release);
    }

    [Fact]
    public void Release_ShouldMarkAsReleased_WhenFullAmountIsReleased()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.Fund(_totalAmount, "txn_123");
        var recipientId = Guid.NewGuid();

        // Act
        escrowAccount.Release(_totalAmount, recipientId, "Full release", "release_txn_123");

        // Assert
        escrowAccount.Status.Should().Be(EscrowStatus.Released);
        escrowAccount.ReleasedAt.Should().NotBeNull();
        escrowAccount.ReleaseReason.Should().Be("Full release");
    }

    [Fact]
    public void Refund_ShouldRefundAmount_WhenValidRequest()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.Fund(_totalAmount, "txn_123");
        var refundAmount = new Money(2000m, "INR");

        // Act
        escrowAccount.Refund(refundAmount, "Customer cancellation", "refund_txn_123");

        // Assert
        escrowAccount.AvailableAmount.Should().Be(new Money(8000m, "INR"));
        escrowAccount.Status.Should().Be(EscrowStatus.Refunded);
        escrowAccount.Transactions.Should().HaveCount(2);
        escrowAccount.Transactions.Last().Type.Should().Be(EscrowTransactionType.Refund);
    }

    [Fact]
    public void AddMilestone_ShouldAddMilestone_WhenValidParameters()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var milestoneAmount = new Money(5000m, "INR");
        var dueDate = DateTime.UtcNow.AddDays(7);

        // Act
        escrowAccount.AddMilestone("Pickup completion", milestoneAmount, dueDate);

        // Assert
        escrowAccount.Milestones.Should().HaveCount(1);
        escrowAccount.Milestones.First().Description.Should().Be("Pickup completion");
        escrowAccount.Milestones.First().Amount.Should().Be(milestoneAmount);
        escrowAccount.Milestones.First().DueDate.Should().Be(dueDate);
        escrowAccount.Milestones.First().Status.Should().Be(EscrowMilestoneStatus.Pending);
    }

    [Fact]
    public void CompleteMilestone_ShouldMarkMilestoneAsCompleted()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var milestoneAmount = new Money(5000m, "INR");
        escrowAccount.AddMilestone("Pickup completion", milestoneAmount);
        var milestoneId = escrowAccount.Milestones.First().Id;

        // Act
        escrowAccount.CompleteMilestone(milestoneId, "Pickup completed successfully");

        // Assert
        var milestone = escrowAccount.Milestones.First();
        milestone.Status.Should().Be(EscrowMilestoneStatus.Completed);
        milestone.CompletedAt.Should().NotBeNull();
        milestone.CompletionNotes.Should().Be("Pickup completed successfully");
    }

    [Fact]
    public void CompleteMilestone_ShouldThrowException_WhenMilestoneNotFound()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        var nonExistentMilestoneId = Guid.NewGuid();

        // Act & Assert
        var action = () => escrowAccount.CompleteMilestone(nonExistentMilestoneId, "Completion notes");

        action.Should().Throw<ArgumentException>()
            .WithMessage("Milestone not found");
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenEscrowAccountIsCreated()
    {
        // Act
        var escrowAccount = CreateEscrowAccount();

        // Assert
        escrowAccount.DomainEvents.Should().HaveCount(1);
        escrowAccount.DomainEvents.First().Should().BeOfType<EscrowAccountCreatedEvent>();

        var createdEvent = (EscrowAccountCreatedEvent)escrowAccount.DomainEvents.First();
        createdEvent.EscrowAccountId.Should().Be(escrowAccount.Id);
        createdEvent.OrderId.Should().Be(_orderId);
        createdEvent.TotalAmount.Should().Be(_totalAmount);
    }

    [Fact]
    public void DomainEvents_ShouldBeRaised_WhenEscrowAccountIsFunded()
    {
        // Arrange
        var escrowAccount = CreateEscrowAccount();
        escrowAccount.ClearDomainEvents(); // Clear creation event

        // Act
        escrowAccount.Fund(_totalAmount, "txn_123");

        // Assert
        escrowAccount.DomainEvents.Should().HaveCount(2); // Transaction created + Account funded
        escrowAccount.DomainEvents.Should().Contain(e => e is EscrowAccountFundedEvent);
        escrowAccount.DomainEvents.Should().Contain(e => e is EscrowTransactionCreatedEvent);
    }

    private EscrowAccount CreateEscrowAccount()
    {
        return new EscrowAccount(
            _orderId,
            _transportCompanyId,
            _brokerId,
            _carrierId,
            _totalAmount,
            "Test escrow account");
    }
}
