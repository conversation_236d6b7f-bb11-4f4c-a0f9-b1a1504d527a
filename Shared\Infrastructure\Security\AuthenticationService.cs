using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Shared.Infrastructure.Security;

/// <summary>
/// Comprehensive authentication service with JWT token management, refresh tokens, and multi-factor authentication
/// Provides enterprise-grade security features for all microservices
/// </summary>
public interface IAuthenticationService
{
    Task<AuthenticationResult> AuthenticateAsync(AuthenticationRequest request, CancellationToken cancellationToken = default);
    Task<AuthenticationResult> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    Task<bool> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<ClaimsPrincipal?> GetPrincipalFromTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<bool> RevokeTokenAsync(string token, CancellationToken cancellationToken = default);
    Task<bool> RevokeAllUserTokensAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<MfaSetupResult> SetupMfaAsync(Guid userId, MfaType mfaType, CancellationToken cancellationToken = default);
    Task<bool> VerifyMfaAsync(Guid userId, string code, CancellationToken cancellationToken = default);
    Task<PasswordResetResult> InitiatePasswordResetAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> CompletePasswordResetAsync(string token, string newPassword, CancellationToken cancellationToken = default);
}

public class AuthenticationService : IAuthenticationService
{
    private readonly IUserRepository _userRepository;
    private readonly ITokenRepository _tokenRepository;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly IMfaService _mfaService;
    private readonly IEmailService _emailService;
    private readonly SecurityConfiguration _config;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        IUserRepository userRepository,
        ITokenRepository tokenRepository,
        IPasswordHashingService passwordHashingService,
        IMfaService mfaService,
        IEmailService emailService,
        SecurityConfiguration config,
        ILogger<AuthenticationService> logger)
    {
        _userRepository = userRepository;
        _tokenRepository = tokenRepository;
        _passwordHashingService = passwordHashingService;
        _mfaService = mfaService;
        _emailService = emailService;
        _config = config;
        _logger = logger;
    }

    public async Task<AuthenticationResult> AuthenticateAsync(AuthenticationRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Authentication attempt for user: {Email}", request.Email);

            // Validate input
            if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
            {
                return AuthenticationResult.Failed("Email and password are required");
            }

            // Get user by email
            var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("Authentication failed: User not found for email {Email}", request.Email);
                return AuthenticationResult.Failed("Invalid credentials");
            }

            // Check if account is locked
            if (user.IsLocked && user.LockoutEnd > DateTime.UtcNow)
            {
                _logger.LogWarning("Authentication failed: Account locked for user {UserId}", user.Id);
                return AuthenticationResult.Failed("Account is temporarily locked");
            }

            // Verify password
            var passwordValid = await _passwordHashingService.VerifyPasswordAsync(request.Password, user.PasswordHash);
            if (!passwordValid)
            {
                await HandleFailedLoginAttempt(user, cancellationToken);
                _logger.LogWarning("Authentication failed: Invalid password for user {UserId}", user.Id);
                return AuthenticationResult.Failed("Invalid credentials");
            }

            // Reset failed login attempts on successful password verification
            await ResetFailedLoginAttempts(user, cancellationToken);

            // Check if MFA is required
            if (user.MfaEnabled && !request.BypassMfa)
            {
                if (string.IsNullOrEmpty(request.MfaCode))
                {
                    return AuthenticationResult.RequiresMfa(user.Id);
                }

                var mfaValid = await _mfaService.VerifyCodeAsync(user.Id, request.MfaCode, cancellationToken);
                if (!mfaValid)
                {
                    _logger.LogWarning("Authentication failed: Invalid MFA code for user {UserId}", user.Id);
                    return AuthenticationResult.Failed("Invalid MFA code");
                }
            }

            // Generate tokens
            var accessToken = await GenerateAccessTokenAsync(user, cancellationToken);
            var refreshToken = await GenerateRefreshTokenAsync(user.Id, cancellationToken);

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            user.LastLoginIp = request.IpAddress;
            await _userRepository.UpdateAsync(user, cancellationToken);

            _logger.LogInformation("Authentication successful for user {UserId}", user.Id);

            return AuthenticationResult.Success(accessToken, refreshToken, user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authentication for email {Email}", request.Email);
            return AuthenticationResult.Failed("Authentication failed");
        }
    }

    public async Task<AuthenticationResult> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate refresh token
            var tokenRecord = await _tokenRepository.GetRefreshTokenAsync(refreshToken, cancellationToken);
            if (tokenRecord == null || tokenRecord.ExpiresAt < DateTime.UtcNow || tokenRecord.IsRevoked)
            {
                _logger.LogWarning("Refresh token validation failed: {Token}", refreshToken);
                return AuthenticationResult.Failed("Invalid refresh token");
            }

            // Get user
            var user = await _userRepository.GetByIdAsync(tokenRecord.UserId, cancellationToken);
            if (user == null || !user.IsActive)
            {
                _logger.LogWarning("Refresh token failed: User not found or inactive {UserId}", tokenRecord.UserId);
                return AuthenticationResult.Failed("User not found or inactive");
            }

            // Generate new tokens
            var newAccessToken = await GenerateAccessTokenAsync(user, cancellationToken);
            var newRefreshToken = await GenerateRefreshTokenAsync(user.Id, cancellationToken);

            // Revoke old refresh token
            await _tokenRepository.RevokeRefreshTokenAsync(refreshToken, cancellationToken);

            _logger.LogInformation("Token refreshed successfully for user {UserId}", user.Id);

            return AuthenticationResult.Success(newAccessToken, newRefreshToken, user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return AuthenticationResult.Failed("Token refresh failed");
        }
    }

    public async Task<bool> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_config.JwtSecret);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _config.JwtIssuer,
                ValidateAudience = true,
                ValidAudience = _config.JwtAudience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            // Check if token is in revocation list
            var jti = principal.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;
            if (!string.IsNullOrEmpty(jti))
            {
                var isRevoked = await _tokenRepository.IsTokenRevokedAsync(jti, cancellationToken);
                if (isRevoked)
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Token validation failed");
            return false;
        }
    }

    public async Task<ClaimsPrincipal?> GetPrincipalFromTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_config.JwtSecret);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _config.JwtIssuer,
                ValidateAudience = true,
                ValidAudience = _config.JwtAudience,
                ValidateLifetime = false, // Don't validate lifetime for this method
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get principal from token");
            return null;
        }
    }

    public async Task<bool> RevokeTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var principal = await GetPrincipalFromTokenAsync(token, cancellationToken);
            if (principal == null)
            {
                return false;
            }

            var jti = principal.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;
            if (string.IsNullOrEmpty(jti))
            {
                return false;
            }

            await _tokenRepository.RevokeTokenAsync(jti, cancellationToken);
            _logger.LogInformation("Token revoked: {Jti}", jti);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking token");
            return false;
        }
    }

    public async Task<bool> RevokeAllUserTokensAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            await _tokenRepository.RevokeAllUserTokensAsync(userId, cancellationToken);
            _logger.LogInformation("All tokens revoked for user {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking all user tokens for user {UserId}", userId);
            return false;
        }
    }

    public async Task<MfaSetupResult> SetupMfaAsync(Guid userId, MfaType mfaType, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return MfaSetupResult.Failed("User not found");
            }

            var setupResult = await _mfaService.SetupMfaAsync(userId, mfaType, cancellationToken);
            
            if (setupResult.Success)
            {
                user.MfaEnabled = true;
                user.MfaType = mfaType;
                await _userRepository.UpdateAsync(user, cancellationToken);
                
                _logger.LogInformation("MFA setup completed for user {UserId} with type {MfaType}", userId, mfaType);
            }

            return setupResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up MFA for user {UserId}", userId);
            return MfaSetupResult.Failed("MFA setup failed");
        }
    }

    public async Task<bool> VerifyMfaAsync(Guid userId, string code, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _mfaService.VerifyCodeAsync(userId, code, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying MFA for user {UserId}", userId);
            return false;
        }
    }

    public async Task<PasswordResetResult> InitiatePasswordResetAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByEmailAsync(email, cancellationToken);
            if (user == null)
            {
                // Don't reveal if email exists or not
                _logger.LogInformation("Password reset requested for non-existent email: {Email}", email);
                return PasswordResetResult.Success("If the email exists, a reset link has been sent");
            }

            // Generate reset token
            var resetToken = GenerateSecureToken();
            var expiresAt = DateTime.UtcNow.AddHours(_config.PasswordResetTokenExpiryHours);

            await _tokenRepository.SavePasswordResetTokenAsync(user.Id, resetToken, expiresAt, cancellationToken);

            // Send reset email
            await _emailService.SendPasswordResetEmailAsync(user.Email, user.FirstName, resetToken, cancellationToken);

            _logger.LogInformation("Password reset initiated for user {UserId}", user.Id);

            return PasswordResetResult.Success("If the email exists, a reset link has been sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating password reset for email {Email}", email);
            return PasswordResetResult.Failed("Password reset failed");
        }
    }

    public async Task<bool> CompletePasswordResetAsync(string token, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var resetRecord = await _tokenRepository.GetPasswordResetTokenAsync(token, cancellationToken);
            if (resetRecord == null || resetRecord.ExpiresAt < DateTime.UtcNow || resetRecord.IsUsed)
            {
                _logger.LogWarning("Invalid or expired password reset token: {Token}", token);
                return false;
            }

            var user = await _userRepository.GetByIdAsync(resetRecord.UserId, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("User not found for password reset token: {UserId}", resetRecord.UserId);
                return false;
            }

            // Validate new password
            var passwordValidation = ValidatePassword(newPassword);
            if (!passwordValidation.IsValid)
            {
                _logger.LogWarning("Password reset failed: Invalid password for user {UserId}", user.Id);
                return false;
            }

            // Hash new password
            var passwordHash = await _passwordHashingService.HashPasswordAsync(newPassword);
            
            // Update user password
            user.PasswordHash = passwordHash;
            user.PasswordChangedAt = DateTime.UtcNow;
            user.FailedLoginAttempts = 0;
            user.IsLocked = false;
            user.LockoutEnd = null;

            await _userRepository.UpdateAsync(user, cancellationToken);

            // Mark reset token as used
            await _tokenRepository.MarkPasswordResetTokenAsUsedAsync(token, cancellationToken);

            // Revoke all existing tokens for security
            await RevokeAllUserTokensAsync(user.Id, cancellationToken);

            _logger.LogInformation("Password reset completed for user {UserId}", user.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing password reset");
            return false;
        }
    }

    private async Task<string> GenerateAccessTokenAsync(User user, CancellationToken cancellationToken)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(_config.JwtSecret);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Email, user.Email),
            new(ClaimTypes.Name, $"{user.FirstName} {user.LastName}"),
            new(ClaimTypes.Role, user.Role),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new("tenant_id", user.TenantId?.ToString() ?? ""),
            new("permissions", string.Join(",", user.Permissions))
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(_config.JwtExpiryMinutes),
            Issuer = _config.JwtIssuer,
            Audience = _config.JwtAudience,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private async Task<string> GenerateRefreshTokenAsync(Guid userId, CancellationToken cancellationToken)
    {
        var refreshToken = GenerateSecureToken();
        var expiresAt = DateTime.UtcNow.AddDays(_config.RefreshTokenExpiryDays);

        await _tokenRepository.SaveRefreshTokenAsync(userId, refreshToken, expiresAt, cancellationToken);

        return refreshToken;
    }

    private string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    private async Task HandleFailedLoginAttempt(User user, CancellationToken cancellationToken)
    {
        user.FailedLoginAttempts++;
        user.LastFailedLoginAt = DateTime.UtcNow;

        if (user.FailedLoginAttempts >= _config.MaxFailedLoginAttempts)
        {
            user.IsLocked = true;
            user.LockoutEnd = DateTime.UtcNow.AddMinutes(_config.LockoutDurationMinutes);
            
            _logger.LogWarning("Account locked for user {UserId} after {Attempts} failed attempts", 
                user.Id, user.FailedLoginAttempts);
        }

        await _userRepository.UpdateAsync(user, cancellationToken);
    }

    private async Task ResetFailedLoginAttempts(User user, CancellationToken cancellationToken)
    {
        if (user.FailedLoginAttempts > 0 || user.IsLocked)
        {
            user.FailedLoginAttempts = 0;
            user.IsLocked = false;
            user.LockoutEnd = null;
            await _userRepository.UpdateAsync(user, cancellationToken);
        }
    }

    private PasswordValidationResult ValidatePassword(string password)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(password))
        {
            errors.Add("Password is required");
        }
        else
        {
            if (password.Length < _config.MinPasswordLength)
            {
                errors.Add($"Password must be at least {_config.MinPasswordLength} characters long");
            }

            if (_config.RequireUppercase && !password.Any(char.IsUpper))
            {
                errors.Add("Password must contain at least one uppercase letter");
            }

            if (_config.RequireLowercase && !password.Any(char.IsLower))
            {
                errors.Add("Password must contain at least one lowercase letter");
            }

            if (_config.RequireDigit && !password.Any(char.IsDigit))
            {
                errors.Add("Password must contain at least one digit");
            }

            if (_config.RequireSpecialCharacter && !password.Any(c => !char.IsLetterOrDigit(c)))
            {
                errors.Add("Password must contain at least one special character");
            }
        }

        return new PasswordValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

// Supporting classes and interfaces
public class AuthenticationRequest
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? MfaCode { get; set; }
    public bool BypassMfa { get; set; } = false;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public class AuthenticationResult
{
    public bool Success { get; set; }
    public string? AccessToken { get; set; }
    public string? RefreshToken { get; set; }
    public User? User { get; set; }
    public string? ErrorMessage { get; set; }
    public bool RequiresMfa { get; set; }
    public Guid? UserId { get; set; }

    public static AuthenticationResult Success(string accessToken, string refreshToken, User user)
    {
        return new AuthenticationResult
        {
            Success = true,
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            User = user
        };
    }

    public static AuthenticationResult Failed(string errorMessage)
    {
        return new AuthenticationResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }

    public static AuthenticationResult RequiresMfa(Guid userId)
    {
        return new AuthenticationResult
        {
            Success = false,
            RequiresMfa = true,
            UserId = userId
        };
    }
}

public class MfaSetupResult
{
    public bool Success { get; set; }
    public string? QrCodeUrl { get; set; }
    public string? SecretKey { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> BackupCodes { get; set; } = new();

    public static MfaSetupResult Success(string qrCodeUrl, string secretKey, List<string> backupCodes)
    {
        return new MfaSetupResult
        {
            Success = true,
            QrCodeUrl = qrCodeUrl,
            SecretKey = secretKey,
            BackupCodes = backupCodes
        };
    }

    public static MfaSetupResult Failed(string errorMessage)
    {
        return new MfaSetupResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}

public class PasswordResetResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;

    public static PasswordResetResult Success(string message)
    {
        return new PasswordResetResult { Success = true, Message = message };
    }

    public static PasswordResetResult Failed(string message)
    {
        return new PasswordResetResult { Success = false, Message = message };
    }
}

public class PasswordValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

public class SecurityConfiguration
{
    public string JwtSecret { get; set; } = string.Empty;
    public string JwtIssuer { get; set; } = string.Empty;
    public string JwtAudience { get; set; } = string.Empty;
    public int JwtExpiryMinutes { get; set; } = 60;
    public int RefreshTokenExpiryDays { get; set; } = 30;
    public int MaxFailedLoginAttempts { get; set; } = 5;
    public int LockoutDurationMinutes { get; set; } = 30;
    public int PasswordResetTokenExpiryHours { get; set; } = 24;
    public int MinPasswordLength { get; set; } = 8;
    public bool RequireUppercase { get; set; } = true;
    public bool RequireLowercase { get; set; } = true;
    public bool RequireDigit { get; set; } = true;
    public bool RequireSpecialCharacter { get; set; } = true;
}

public enum MfaType
{
    None = 0,
    Totp = 1,
    Sms = 2,
    Email = 3
}

// Placeholder interfaces that would be implemented
public interface IUserRepository
{
    Task<User?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task UpdateAsync(User user, CancellationToken cancellationToken = default);
}

public interface ITokenRepository
{
    Task SaveRefreshTokenAsync(Guid userId, string token, DateTime expiresAt, CancellationToken cancellationToken = default);
    Task<RefreshTokenRecord?> GetRefreshTokenAsync(string token, CancellationToken cancellationToken = default);
    Task RevokeRefreshTokenAsync(string token, CancellationToken cancellationToken = default);
    Task RevokeTokenAsync(string jti, CancellationToken cancellationToken = default);
    Task RevokeAllUserTokensAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> IsTokenRevokedAsync(string jti, CancellationToken cancellationToken = default);
    Task SavePasswordResetTokenAsync(Guid userId, string token, DateTime expiresAt, CancellationToken cancellationToken = default);
    Task<PasswordResetTokenRecord?> GetPasswordResetTokenAsync(string token, CancellationToken cancellationToken = default);
    Task MarkPasswordResetTokenAsUsedAsync(string token, CancellationToken cancellationToken = default);
}

public interface IPasswordHashingService
{
    Task<string> HashPasswordAsync(string password);
    Task<bool> VerifyPasswordAsync(string password, string hash);
}

public interface IMfaService
{
    Task<MfaSetupResult> SetupMfaAsync(Guid userId, MfaType mfaType, CancellationToken cancellationToken = default);
    Task<bool> VerifyCodeAsync(Guid userId, string code, CancellationToken cancellationToken = default);
}

public interface IEmailService
{
    Task SendPasswordResetEmailAsync(string email, string firstName, string resetToken, CancellationToken cancellationToken = default);
}

// Placeholder classes
public class User
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool MfaEnabled { get; set; }
    public MfaType MfaType { get; set; }
    public int FailedLoginAttempts { get; set; }
    public bool IsLocked { get; set; }
    public DateTime? LockoutEnd { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public string? LastLoginIp { get; set; }
    public DateTime? LastFailedLoginAt { get; set; }
    public DateTime? PasswordChangedAt { get; set; }
    public Guid? TenantId { get; set; }
    public List<string> Permissions { get; set; } = new();
}

public class RefreshTokenRecord
{
    public Guid UserId { get; set; }
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public bool IsRevoked { get; set; }
}

public class PasswordResetTokenRecord
{
    public Guid UserId { get; set; }
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public bool IsUsed { get; set; }
}
