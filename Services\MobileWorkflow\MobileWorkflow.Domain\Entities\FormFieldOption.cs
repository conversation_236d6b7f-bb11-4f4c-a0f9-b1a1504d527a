using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class FormFieldOption : AggregateRoot
{
    public Guid FormFieldId { get; private set; }
    public string Value { get; private set; }
    public string DisplayText { get; private set; }
    public string? Description { get; private set; }
    public int Order { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> ConditionalLogic { get; private set; }
    public Dictionary<string, object> Styling { get; private set; }
    public string? IconUrl { get; private set; }
    public string? Color { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual FormField FormField { get; private set; } = null!;

    private FormFieldOption() { } // EF Core

    public FormFieldOption(
        Guid formFieldId,
        string value,
        string displayText,
        string? description,
        int order)
    {
        FormFieldId = formFieldId;
        Value = value ?? throw new ArgumentNullException(nameof(value));
        DisplayText = displayText ?? throw new ArgumentNullException(nameof(displayText));
        Description = description;
        Order = order;

        IsActive = true;
        IsDefault = false;
        Configuration = new Dictionary<string, object>();
        ConditionalLogic = new Dictionary<string, object>();
        Styling = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new FormFieldOptionCreatedEvent(Id, FormFieldId, Value, DisplayText, Order));
    }

    public void UpdateDisplayText(string displayText)
    {
        DisplayText = displayText ?? throw new ArgumentNullException(nameof(displayText));

        AddDomainEvent(new FormFieldOptionDisplayTextUpdatedEvent(Id, FormFieldId, Value, DisplayText));
    }

    public void UpdateDescription(string? description)
    {
        Description = description;

        AddDomainEvent(new FormFieldOptionDescriptionUpdatedEvent(Id, FormFieldId, Value));
    }

    public void UpdateOrder(int newOrder)
    {
        var oldOrder = Order;
        Order = newOrder;

        AddDomainEvent(new FormFieldOptionOrderUpdatedEvent(Id, FormFieldId, Value, oldOrder, newOrder));
    }

    public void SetAsDefault()
    {
        IsDefault = true;

        AddDomainEvent(new FormFieldOptionSetAsDefaultEvent(Id, FormFieldId, Value));
    }

    public void RemoveAsDefault()
    {
        IsDefault = false;

        AddDomainEvent(new FormFieldOptionRemovedAsDefaultEvent(Id, FormFieldId, Value));
    }

    public void Activate()
    {
        IsActive = true;

        AddDomainEvent(new FormFieldOptionActivatedEvent(Id, FormFieldId, Value));
    }

    public void Deactivate()
    {
        IsActive = false;

        AddDomainEvent(new FormFieldOptionDeactivatedEvent(Id, FormFieldId, Value));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        AddDomainEvent(new FormFieldOptionConfigurationUpdatedEvent(Id, FormFieldId, Value));
    }

    public void UpdateConditionalLogic(Dictionary<string, object> conditionalLogic)
    {
        ConditionalLogic = conditionalLogic ?? throw new ArgumentNullException(nameof(conditionalLogic));

        AddDomainEvent(new FormFieldOptionConditionalLogicUpdatedEvent(Id, FormFieldId, Value));
    }

    public void UpdateStyling(Dictionary<string, object> styling)
    {
        Styling = styling ?? throw new ArgumentNullException(nameof(styling));

        AddDomainEvent(new FormFieldOptionStylingUpdatedEvent(Id, FormFieldId, Value));
    }

    public void SetIcon(string? iconUrl)
    {
        IconUrl = iconUrl;

        AddDomainEvent(new FormFieldOptionIconSetEvent(Id, FormFieldId, Value, iconUrl));
    }

    public void SetColor(string? color)
    {
        Color = color;

        AddDomainEvent(new FormFieldOptionColorSetEvent(Id, FormFieldId, Value, color));
    }

    public bool IsVisible(Dictionary<string, object> context)
    {
        if (ConditionalLogic.TryGetValue("visibility", out var visibilityConditions) &&
            visibilityConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, context);
        }

        return IsActive;
    }

    public bool IsEnabled(Dictionary<string, object> context)
    {
        if (ConditionalLogic.TryGetValue("enabled", out var enabledConditions) &&
            enabledConditions is Dictionary<string, object> conditions)
        {
            return EvaluateConditions(conditions, context);
        }

        return IsActive;
    }

    private bool EvaluateConditions(Dictionary<string, object> conditions, Dictionary<string, object> context)
    {
        if (conditions.TryGetValue("operator", out var op) && op is string operatorStr)
        {
            if (conditions.TryGetValue("conditions", out var conditionsList) && conditionsList is List<object> condList)
            {
                var results = new List<bool>();

                foreach (var condition in condList)
                {
                    if (condition is Dictionary<string, object> condDict)
                    {
                        results.Add(EvaluateSingleCondition(condDict, context));
                    }
                }

                return operatorStr.ToLowerInvariant() switch
                {
                    "and" => results.All(r => r),
                    "or" => results.Any(r => r),
                    _ => true
                };
            }
        }

        return true;
    }

    private bool EvaluateSingleCondition(Dictionary<string, object> condition, Dictionary<string, object> context)
    {
        if (condition.TryGetValue("field", out var field) && field is string fieldName &&
            condition.TryGetValue("operator", out var op) && op is string operatorStr &&
            condition.TryGetValue("value", out var expectedValue))
        {
            context.TryGetValue(fieldName, out var contextValue);
            return EvaluateOperator(operatorStr, contextValue, expectedValue);
        }

        return true;
    }

    private bool EvaluateOperator(string operatorStr, object? contextValue, object expectedValue)
    {
        return operatorStr.ToLowerInvariant() switch
        {
            "equals" or "eq" => Equals(contextValue, expectedValue),
            "not_equals" or "ne" => !Equals(contextValue, expectedValue),
            "contains" => contextValue?.ToString()?.Contains(expectedValue?.ToString() ?? "") == true,
            "starts_with" => contextValue?.ToString()?.StartsWith(expectedValue?.ToString() ?? "") == true,
            "ends_with" => contextValue?.ToString()?.EndsWith(expectedValue?.ToString() ?? "") == true,
            "is_empty" => IsValueEmpty(contextValue),
            "is_not_empty" => !IsValueEmpty(contextValue),
            "in" => expectedValue is List<object> list && list.Contains(contextValue),
            "not_in" => expectedValue is List<object> notList && !notList.Contains(contextValue),
            _ => true
        };
    }

    private bool IsValueEmpty(object? value)
    {
        return value == null ||
               (value is string str && string.IsNullOrWhiteSpace(str)) ||
               (value is Array arr && arr.Length == 0) ||
               (value is System.Collections.ICollection collection && collection.Count == 0);
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public Dictionary<string, object> ToDto()
    {
        return new Dictionary<string, object>
        {
            ["id"] = Id,
            ["value"] = Value,
            ["displayText"] = DisplayText,
            ["description"] = Description,
            ["order"] = Order,
            ["isActive"] = IsActive,
            ["isDefault"] = IsDefault,
            ["iconUrl"] = IconUrl,
            ["color"] = Color,
            ["configuration"] = Configuration,
            ["styling"] = Styling,
            ["metadata"] = Metadata
        };
    }
}


