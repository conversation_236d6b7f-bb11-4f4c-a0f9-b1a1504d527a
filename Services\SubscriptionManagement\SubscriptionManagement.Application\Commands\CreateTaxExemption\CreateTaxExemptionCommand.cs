using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.CreateTaxExemption
{
    public class CreateTaxExemptionCommand : IRequest<Guid>
    {
        public Guid UserId { get; set; }
        public string ExemptionType { get; set; } = string.Empty;
        public string ExemptionNumber { get; set; } = string.Empty;
        public string IssuingAuthority { get; set; } = string.Empty;
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
        public List<TaxType> ExemptTaxTypes { get; set; } = new();
        public List<string> ApplicableRegions { get; set; } = new();
        public string? DocumentPath { get; set; }
        public Guid CreatedByUserId { get; set; }
    }
}
