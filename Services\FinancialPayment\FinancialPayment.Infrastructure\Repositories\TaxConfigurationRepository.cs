using Microsoft.EntityFrameworkCore;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class TaxConfigurationRepository : ITaxConfigurationRepository
{
    private readonly FinancialPaymentDbContext _context;

    public TaxConfigurationRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<TaxConfiguration?> GetByIdAsync(Guid id)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<List<TaxConfiguration>> GetAllAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetActiveAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Status == status)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByJurisdictionAsync(TaxJurisdiction jurisdiction)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Jurisdiction.Country == jurisdiction.Country &&
                       t.Jurisdiction.State == jurisdiction.State &&
                       t.Jurisdiction.City == jurisdiction.City)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetEffectiveOnDateAsync(DateTime date)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Status == TaxConfigurationStatus.Active &&
                       t.EffectiveFrom <= date &&
                       (t.EffectiveTo == null || t.EffectiveTo >= date))
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<TaxConfiguration?> GetDefaultAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.IsDefault && t.Status == TaxConfigurationStatus.Active)
            .FirstOrDefaultAsync();
    }

    public async Task<TaxConfiguration?> GetDefaultByJurisdictionAsync(TaxJurisdiction jurisdiction)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.IsDefault && 
                       t.Status == TaxConfigurationStatus.Active &&
                       t.Jurisdiction.Country == jurisdiction.Country &&
                       t.Jurisdiction.State == jurisdiction.State &&
                       t.Jurisdiction.City == jurisdiction.City)
            .FirstOrDefaultAsync();
    }

    public async Task<List<TaxConfiguration>> GetByPriorityAsync(int priority)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Priority == priority)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetOrderedByPriorityAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Status == TaxConfigurationStatus.Active)
            .OrderBy(t => t.Priority)
            .ThenByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetWithGstCalculationEnabledAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.EnableGstCalculation && t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetWithTdsCalculationEnabledAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.EnableTdsCalculation && t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetWithReverseChargeEnabledAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.EnableReverseCharge && t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetRequiringHsnCodeAsync()
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.RequireHsnCode && t.Status == TaxConfigurationStatus.Active)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByDefaultGstRateAsync(decimal gstRate)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => Math.Abs(t.DefaultGstRate - gstRate) < 0.01m)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByDefaultTdsRateAsync(decimal tdsRate)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => Math.Abs(t.DefaultTdsRate - tdsRate) < 0.01m)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByCurrencyAsync(string currency)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.DefaultCurrency == currency)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByCreatedByAsync(string createdBy)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.CreatedBy == createdBy)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfiguration>> GetByDateRangeAsync(DateTime from, DateTime to)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.CreatedAt >= from && t.CreatedAt <= to)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<TaxConfiguration> AddAsync(TaxConfiguration taxConfiguration)
    {
        _context.TaxConfigurations.Add(taxConfiguration);
        return taxConfiguration;
    }

    public async Task UpdateAsync(TaxConfiguration taxConfiguration)
    {
        _context.TaxConfigurations.Update(taxConfiguration);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var taxConfiguration = await _context.TaxConfigurations.FindAsync(id);
        if (taxConfiguration != null)
        {
            _context.TaxConfigurations.Remove(taxConfiguration);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.TaxConfigurations.AnyAsync(t => t.Id == id);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null)
    {
        var query = _context.TaxConfigurations.Where(t => t.Name == name);
        if (excludeId.HasValue)
            query = query.Where(t => t.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<bool> HasDefaultConfigurationAsync(TaxJurisdiction jurisdiction, Guid? excludeId = null)
    {
        var query = _context.TaxConfigurations.Where(t => 
            t.IsDefault && 
            t.Status == TaxConfigurationStatus.Active &&
            t.Jurisdiction.Country == jurisdiction.Country &&
            t.Jurisdiction.State == jurisdiction.State &&
            t.Jurisdiction.City == jurisdiction.City);
        
        if (excludeId.HasValue)
            query = query.Where(t => t.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    public async Task<int> GetCountByStatusAsync(TaxConfigurationStatus status)
    {
        return await _context.TaxConfigurations.CountAsync(t => t.Status == status);
    }

    public async Task<List<TaxConfiguration>> SearchAsync(string searchTerm, int skip = 0, int take = 50)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Name.Contains(searchTerm) || 
                       t.Description.Contains(searchTerm) ||
                       t.Jurisdiction.Country.Contains(searchTerm) ||
                       t.Jurisdiction.State.Contains(searchTerm) ||
                       t.Jurisdiction.City.Contains(searchTerm))
            .OrderByDescending(t => t.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    public async Task<List<TaxConfigurationHistory>> GetHistoryAsync(Guid taxConfigurationId)
    {
        return await _context.Set<TaxConfigurationHistory>()
            .Where(h => h.TaxConfigurationId == taxConfigurationId)
            .OrderByDescending(h => h.ModifiedAt)
            .ToListAsync();
    }

    public async Task<List<TaxConfigurationRule>> GetRulesAsync(Guid taxConfigurationId)
    {
        return await _context.Set<TaxConfigurationRule>()
            .Where(r => r.TaxConfigurationId == taxConfigurationId && r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync();
    }

    public async Task<TaxConfiguration?> GetApplicableConfigurationAsync(TaxJurisdiction jurisdiction, DateTime date)
    {
        return await _context.TaxConfigurations
            .Include(t => t.History)
            .Include(t => t.Rules)
            .Where(t => t.Status == TaxConfigurationStatus.Active &&
                       t.Jurisdiction.Country == jurisdiction.Country &&
                       t.Jurisdiction.State == jurisdiction.State &&
                       t.Jurisdiction.City == jurisdiction.City &&
                       t.EffectiveFrom <= date &&
                       (t.EffectiveTo == null || t.EffectiveTo >= date))
            .OrderBy(t => t.Priority)
            .ThenByDescending(t => t.CreatedAt)
            .FirstOrDefaultAsync();
    }
}
