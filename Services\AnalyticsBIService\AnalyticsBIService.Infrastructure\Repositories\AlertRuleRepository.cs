using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Domain.Common;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class AlertRuleRepository : OptimizedRepositoryBase<AlertRule, Guid>, IAlertRuleRepository
{
    public AlertRuleRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<PagedResult<AlertRule>> GetByUserIdAsync(Guid userId, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AlertRule>()
            .Where(r => r.UserId == userId)
            .OrderByDescending(r => r.CreatedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AlertRule>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<IEnumerable<AlertRule>> GetActiveRulesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AlertRule>> GetByMetricAsync(string metricName, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.MetricName == metricName && r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AlertRule>> GetByThresholdTypeAsync(string thresholdType, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.ThresholdType == thresholdType && r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AlertRule>> GetByUserAndMetricAsync(Guid userId, string metricName, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.UserId == userId && r.MetricName == metricName && r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateThresholdAsync(Guid ruleId, decimal newThreshold, CancellationToken cancellationToken = default)
    {
        var rule = await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Id == ruleId, cancellationToken);

        if (rule != null)
        {
            rule.UpdateThreshold(newThreshold);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task ToggleActiveStatusAsync(Guid ruleId, CancellationToken cancellationToken = default)
    {
        var rule = await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Id == ruleId, cancellationToken);

        if (rule != null)
        {
            if (rule.IsActive)
            {
                rule.Deactivate();
            }
            else
            {
                rule.Activate();
            }
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<AlertRule>> GetRulesForEvaluationAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.IsActive)
            .OrderBy(r => r.MetricName)
            .ThenBy(r => r.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task UpdateLastEvaluatedAsync(Guid ruleId, DateTime lastEvaluated, CancellationToken cancellationToken = default)
    {
        var rule = await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Id == ruleId, cancellationToken);

        if (rule != null)
        {
            rule.UpdateLastEvaluated(lastEvaluated);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<AlertRule>> GetDueForEvaluationAsync(DateTime cutoffTime, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.IsActive && (r.LastEvaluatedAt == null || r.LastEvaluatedAt < cutoffTime))
            .OrderBy(r => r.LastEvaluatedAt ?? DateTime.MinValue)
            .ToListAsync(cancellationToken);
    }

    public async Task<AlertRule?> GetByNameAsync(string name, Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Name == name && r.UserId == userId, cancellationToken);
    }

    public async Task SetEnabledAsync(Guid ruleId, bool enabled, CancellationToken cancellationToken = default)
    {
        var rule = await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Id == ruleId, cancellationToken);

        if (rule != null)
        {
            if (enabled)
            {
                rule.Activate();
            }
            else
            {
                rule.Deactivate();
            }
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task UpdateLastTriggeredAsync(Guid ruleId, DateTime lastTriggered, CancellationToken cancellationToken = default)
    {
        var rule = await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.Id == ruleId, cancellationToken);

        if (rule != null)
        {
            rule.UpdateLastTriggered(lastTriggered);
            await Context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<AlertRule>> GetRulesDueForEvaluationAsync(CancellationToken cancellationToken = default)
    {
        return await GetRulesForEvaluationAsync(cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetRuleStatisticsAsync(Guid? userId = null, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AlertRule>().AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(r => r.UserId == userId.Value);
        }

        var totalRules = await query.CountAsync(cancellationToken);
        var activeRules = await query.CountAsync(r => r.IsActive, cancellationToken);
        var triggeredToday = await query.CountAsync(r => r.LastTriggeredAt.HasValue &&
            r.LastTriggeredAt.Value.Date == DateTime.UtcNow.Date, cancellationToken);

        return new Dictionary<string, int>
        {
            ["TotalRules"] = totalRules,
            ["ActiveRules"] = activeRules,
            ["InactiveRules"] = totalRules - activeRules,
            ["TriggeredToday"] = triggeredToday
        };
    }

    public async Task<IEnumerable<AlertRule>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.UserId == userId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AlertRule>> GetByCategoryAsync(Domain.Enums.KPICategory category, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.Category == category)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AlertRule>> GetBySeverityAsync(Domain.Enums.AlertSeverity severity, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.Severity == severity)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }



    public async Task<IEnumerable<AlertRule>> GetEnabledRulesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .Where(r => r.IsEnabled)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<AlertRule?> GetByMetricNameAsync(string metricName, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AlertRule>()
            .FirstOrDefaultAsync(r => r.MetricName == metricName, cancellationToken);
    }

    public async Task<List<Alert>> GetPendingAlertsAsync(CancellationToken cancellationToken = default)
    {
        return await Context.Set<Alert>()
            .Where(a => a.Status == Domain.Enums.AlertStatus.Pending)
            .OrderByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Alert>> GetOldPendingAlertsAsync(DateTime cutoffTime, CancellationToken cancellationToken = default)
    {
        return await Context.Set<Alert>()
            .Where(a => a.Status == Domain.Enums.AlertStatus.Pending && a.TriggeredAt < cutoffTime)
            .OrderByDescending(a => a.TriggeredAt)
            .ToListAsync(cancellationToken);
    }

    public async Task DeleteOldResolvedAlertsAsync(DateTime cutoffDate, CancellationToken cancellationToken = default)
    {
        var oldAlerts = await Context.Set<Alert>()
            .Where(a => a.Status == Domain.Enums.AlertStatus.Resolved && a.ResolvedAt < cutoffDate)
            .ToListAsync(cancellationToken);

        Context.Set<Alert>().RemoveRange(oldAlerts);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<Alert?> GetRecentAlertAsync(Guid metricId, TimeSpan timeWindow, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow - timeWindow;

        return await Context.Set<Alert>()
            .Where(a => a.EntityId == metricId && a.TriggeredAt >= cutoffTime)
            .OrderByDescending(a => a.TriggeredAt)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
