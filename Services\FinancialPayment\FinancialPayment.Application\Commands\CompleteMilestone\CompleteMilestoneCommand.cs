using MediatR;

namespace FinancialPayment.Application.Commands.CompleteMilestone;

public record CompleteMilestoneCommand : IRequest<bool>
{
    public Guid EscrowAccountId { get; init; }
    public Guid MilestoneId { get; init; }
    public string CompletionNotes { get; init; } = string.Empty;
    public Guid CompletedBy { get; init; }
    public DateTime? CompletionDate { get; init; }
    public bool AutoReleaseFunds { get; init; } = true;
    public List<MilestoneVerificationDto> Verifications { get; init; } = new();
}

public record MilestoneVerificationDto
{
    public string VerificationType { get; init; } = string.Empty; // "TripCompleted", "DocumentUploaded", "ManualApproval"
    public string VerificationData { get; init; } = string.Empty;
    public bool IsVerified { get; init; }
    public string? VerificationNotes { get; init; }
    public Guid? VerifiedBy { get; init; }
    public DateTime? VerifiedAt { get; init; }
}
