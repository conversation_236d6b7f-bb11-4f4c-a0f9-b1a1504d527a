using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.CloseRfq;

public class CloseRfqCommandHandler : IRequestHandler<CloseRfqCommand, bool>
{
    private readonly IRfqRepository _rfqRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CloseRfqCommandHandler> _logger;

    public CloseRfqCommandHandler(
        IRfqRepository rfqRepository,
        IUnitOfWork unitOfWork,
        ILogger<CloseRfqCommandHandler> logger)
    {
        _rfqRepository = rfqRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(CloseRfqCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Closing RFQ {RfqId} by transport company {TransportCompanyId}", 
            request.RfqId, request.TransportCompanyId);

        var rfq = await _rfqRepository.GetByIdAsync(request.RfqId, cancellationToken);
        
        if (rfq == null)
        {
            _logger.LogWarning("RFQ {RfqId} not found", request.RfqId);
            throw new OrderManagementException("RFQ not found");
        }

        // Authorization check
        if (rfq.TransportCompanyId != request.TransportCompanyId)
        {
            _logger.LogWarning("Transport company {TransportCompanyId} is not authorized to close RFQ {RfqId}", 
                request.TransportCompanyId, request.RfqId);
            throw new OrderManagementException("You are not authorized to close this RFQ");
        }

        // Business rule check
        if (rfq.Status == RfqStatus.Closed)
        {
            _logger.LogWarning("RFQ {RfqId} is already closed", request.RfqId);
            throw new OrderManagementException("RFQ is already closed");
        }

        if (rfq.Status == RfqStatus.Draft)
        {
            _logger.LogWarning("Cannot close RFQ {RfqId} in draft status", request.RfqId);
            throw new OrderManagementException("Cannot close an RFQ that hasn't been published");
        }

        // Close the RFQ
        rfq.Close(request.ClosureReason);

        _rfqRepository.Update(rfq);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Successfully closed RFQ {RfqId}", request.RfqId);
        
        return true;
    }
}
