using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using Identity.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using Shared.Messaging.Events;

namespace Identity.Application.Auth.Commands.RegisterUser
{
    public class RegisterUserCommandHandler : IRequestHandler<RegisterUserCommand, Guid>
    {
        private readonly IUserRepository _userRepository;
        private readonly IPasswordHasher _passwordHasher;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<RegisterUserCommandHandler> _logger;

        public RegisterUserCommandHandler(
            IUserRepository userRepository,
            IPasswordHasher passwordHasher,
            IMessageBroker messageBroker,
            ILogger<RegisterUserCommandHandler> logger)
        {
            _userRepository = userRepository;
            _passwordHasher = passwordHasher;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
        {
            // Check if username already exists
            if (await _userRepository.ExistsByUsernameAsync(request.Username))
            {
                throw new ApplicationException("Username is already taken");
            }

            // Check if email already exists
            if (await _userRepository.ExistsByEmailAsync(request.Email))
            {
                throw new ApplicationException("Email is already registered");
            }

            // Create email value object
            var email = Email.Create(request.Email);

            // Hash the password
            var passwordHash = _passwordHasher.HashPassword(request.Password);

            // Create the user
            var user = new User(request.Username, email, passwordHash, request.UserType);

            // Set phone number if provided
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                user.SetPhoneNumber(request.PhoneNumber);
            }

            // Save the user
            await _userRepository.AddAsync(user);

            // Publish integration event
            await _messageBroker.PublishAsync("user.registered", new UserRegisteredEvent
            {
                UserId = user.Id,
                Email = user.Email.Value,
                Username = user.Username,
                UserType = user.UserType.ToString(),
                RegisteredAt = DateTime.UtcNow
            });

            _logger.LogInformation("User {UserId} registered with email {Email}", user.Id, user.Email.Value);

            return user.Id;
        }
    }
}
