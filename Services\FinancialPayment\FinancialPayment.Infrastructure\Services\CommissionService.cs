using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class CommissionService : ICommissionService
{
    private readonly ICommissionRepository _commissionRepository;
    private readonly IEnhancedPaymentService _paymentService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CommissionService> _logger;

    public CommissionService(
        ICommissionRepository commissionRepository,
        IEnhancedPaymentService paymentService,
        IUnitOfWork unitOfWork,
        ILogger<CommissionService> logger)
    {
        _commissionRepository = commissionRepository;
        _paymentService = paymentService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Commission> CalculateCommissionAsync(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure)
    {
        _logger.LogInformation("Calculating commission for order {OrderId} and broker {BrokerId}", orderId, brokerId);

        try
        {
            // Check if commission already exists
            var existingCommission = await _commissionRepository.GetByOrderIdAsync(orderId);
            if (existingCommission != null)
            {
                throw new InvalidOperationException($"Commission already exists for order {orderId}");
            }

            // Create commission
            var commission = new Commission(
                orderId,
                brokerId,
                transportCompanyId,
                carrierId,
                orderAmount,
                commissionStructure);

            await _commissionRepository.AddAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully calculated commission {CommissionId} for order {OrderId}",
                commission.Id, orderId);

            return commission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<bool> ApproveCommissionAsync(Guid commissionId, string approvedBy)
    {
        _logger.LogInformation("Approving commission {CommissionId} by {ApprovedBy}", commissionId, approvedBy);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.Approve(approvedBy);

            _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully approved commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<bool> PayCommissionAsync(Guid commissionId, string paymentMethodId)
    {
        _logger.LogInformation("Paying commission {CommissionId}", commissionId);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            // Process payment
            var paymentResult = await _paymentService.ProcessCommissionPaymentAsync(
                commissionId,
                commission.CalculatedAmount,
                commission.BrokerId,
                paymentMethodId);

            if (!paymentResult.IsSuccess)
            {
                _logger.LogWarning("Payment failed for commission {CommissionId}: {Error}",
                    commissionId, paymentResult.ErrorMessage);
                return false;
            }

            commission.Pay(paymentResult.TransactionId ?? string.Empty);

            _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully paid commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error paying commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<bool> DisputeCommissionAsync(Guid commissionId, string reason)
    {
        _logger.LogInformation("Disputing commission {CommissionId} with reason: {Reason}", commissionId, reason);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.Dispute(reason);

            _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully disputed commission {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disputing commission {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<bool> ResolveCommissionDisputeAsync(Guid commissionId, Money resolvedAmount, string resolution)
    {
        _logger.LogInformation("Resolving commission dispute {CommissionId} with amount {ResolvedAmount}",
            commissionId, resolvedAmount);

        try
        {
            var commission = await _commissionRepository.GetByIdAsync(commissionId);
            if (commission == null)
            {
                throw new InvalidOperationException($"Commission {commissionId} not found");
            }

            commission.ResolveDispute(resolvedAmount, resolution);

            _commissionRepository.UpdateAsync(commission);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully resolved commission dispute {CommissionId}", commissionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving commission dispute {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<Commission?> GetCommissionByIdAsync(Guid commissionId)
    {
        return await _commissionRepository.GetByIdAsync(commissionId);
    }

    public async Task<Commission?> GetCommissionByOrderIdAsync(Guid orderId)
    {
        return await _commissionRepository.GetByOrderIdAsync(orderId);
    }

    public async Task<List<Commission>> GetCommissionsByBrokerAsync(Guid brokerId)
    {
        return await _commissionRepository.GetByBrokerIdAsync(brokerId);
    }

    public async Task<List<Commission>> GetCommissionsByTransportCompanyAsync(Guid transportCompanyId)
    {
        return await _commissionRepository.GetByTransportCompanyIdAsync(transportCompanyId);
    }

    public async Task<List<Commission>> GetCommissionsByCarrierAsync(Guid carrierId)
    {
        return await _commissionRepository.GetByCarrierIdAsync(carrierId);
    }

    public async Task<List<Commission>> GetCommissionsByStatusAsync(CommissionStatus status)
    {
        return await _commissionRepository.GetByStatusAsync(status);
    }
}
