using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Application.Interfaces;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.ReleaseEscrowFunds;

public class ReleaseEscrowFundsCommandHandler : IRequestHandler<ReleaseEscrowFundsCommand, bool>
{
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IEscrowService _escrowService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ReleaseEscrowFundsCommandHandler> _logger;

    public ReleaseEscrowFundsCommandHandler(
        IEscrowAccountRepository escrowAccountRepository,
        IEscrowService escrowService,
        IUnitOfWork unitOfWork,
        ILogger<ReleaseEscrowFundsCommandHandler> logger)
    {
        _escrowAccountRepository = escrowAccountRepository;
        _escrowService = escrowService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(ReleaseEscrowFundsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Releasing escrow funds for account {EscrowAccountId}", request.EscrowAccountId);

            // Get escrow account
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(request.EscrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {request.EscrowAccountId} not found");
            }

            // Release all available funds through escrow service
            var releaseAmount = escrowAccount.AvailableAmount + escrowAccount.ReservedAmount;
            var result = await _escrowService.ReleaseEscrowFundsAsync(
                request.EscrowAccountId,
                releaseAmount,
                escrowAccount.CarrierId, // Default recipient to carrier
                request.ReleaseReason);

            if (!result)
            {
                _logger.LogWarning("Failed to release escrow funds for account {EscrowAccountId}", request.EscrowAccountId);
                return false;
            }

            // Update escrow account
            escrowAccount.Release(releaseAmount, escrowAccount.CarrierId, request.ReleaseReason, null);
            await _escrowAccountRepository.UpdateAsync(escrowAccount);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully released escrow funds for account {EscrowAccountId}", request.EscrowAccountId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing escrow funds for account {EscrowAccountId}", request.EscrowAccountId);
            throw;
        }
    }
}
