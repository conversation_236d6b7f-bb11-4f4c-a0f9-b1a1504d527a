using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

/// <summary>
/// Query to get all vehicle type masters
/// </summary>
public class GetVehicleTypeMastersQuery : IRequest<IEnumerable<VehicleTypeMasterSummaryDto>>
{
    public bool ActiveOnly { get; set; } = false;
    public string? Category { get; set; }

    public GetVehicleTypeMastersQuery(bool activeOnly = false, string? category = null)
    {
        ActiveOnly = activeOnly;
        Category = category;
    }
}
