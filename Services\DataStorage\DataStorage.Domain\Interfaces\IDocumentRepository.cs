using DataStorage.Domain.Entities;
using DataStorage.Domain.Enums;

namespace DataStorage.Domain.Interfaces;

public interface IDocumentRepository
{
    // Basic CRUD operations
    Task<Document?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Document?> GetByIdWithVersionsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<Document>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Document>> GetByOwnerAndTypeAsync(Guid ownerId, DocumentType documentType, CancellationToken cancellationToken = default);
    Task AddAsync(Document document, CancellationToken cancellationToken = default);
    Task UpdateAsync(Document document, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    // Query operations
    Task<IEnumerable<Document>> SearchAsync(
        string? searchTerm = null,
        DocumentType? documentType = null,
        DocumentCategory? category = null,
        DocumentStatus? status = null,
        Guid? ownerId = null,
        string? ownerType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        int skip = 0,
        int take = 50,
        CancellationToken cancellationToken = default);

    Task<int> CountAsync(
        string? searchTerm = null,
        DocumentType? documentType = null,
        DocumentCategory? category = null,
        DocumentStatus? status = null,
        Guid? ownerId = null,
        string? ownerType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        CancellationToken cancellationToken = default);

    // Access control
    Task<IEnumerable<Document>> GetAccessibleDocumentsAsync(
        Guid userId,
        AccessLevel minimumAccessLevel = AccessLevel.Private,
        CancellationToken cancellationToken = default);

    // Version management
    Task<IEnumerable<DocumentVersion>> GetDocumentVersionsAsync(Guid documentId, CancellationToken cancellationToken = default);
    Task<DocumentVersion?> GetDocumentVersionAsync(Guid documentId, int versionNumber, CancellationToken cancellationToken = default);

    // Bulk operations
    Task<IEnumerable<Document>> GetExpiredDocumentsAsync(DateTime beforeDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<Document>> GetDocumentsForArchivalAsync(DateTime beforeDate, CancellationToken cancellationToken = default);
    Task BulkUpdateStatusAsync(IEnumerable<Guid> documentIds, DocumentStatus newStatus, CancellationToken cancellationToken = default);

    // Analytics
    Task<Dictionary<DocumentType, int>> GetDocumentCountByTypeAsync(Guid? ownerId = null, CancellationToken cancellationToken = default);
    Task<Dictionary<DocumentStatus, int>> GetDocumentCountByStatusAsync(Guid? ownerId = null, CancellationToken cancellationToken = default);
    Task<long> GetTotalStorageSizeAsync(Guid? ownerId = null, CancellationToken cancellationToken = default);
}
