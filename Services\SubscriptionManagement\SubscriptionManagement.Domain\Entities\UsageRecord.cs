using Shared.Domain.Common;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Entities;

public class UsageRecord : BaseEntity
{
    public Guid SubscriptionId { get; private set; }
    public Guid UserId { get; private set; }
    public FeatureType FeatureType { get; private set; }
    public int UsageCount { get; private set; }
    public DateTime UsageDate { get; private set; }
    public string? Metadata { get; private set; } // JSON for additional context
    public DateTime PeriodStart { get; private set; }
    public DateTime PeriodEnd { get; private set; }

    // Navigation properties
    public Subscription? Subscription { get; private set; }

    private UsageRecord() { }

    public UsageRecord(
        Guid subscriptionId,
        Guid userId,
        FeatureType featureType,
        int usageCount = 1,
        string? metadata = null)
    {
        SubscriptionId = subscriptionId;
        UserId = userId;
        FeatureType = featureType;
        UsageCount = usageCount;
        UsageDate = DateTime.UtcNow;
        Metadata = metadata;
        
        // Set billing period (current month)
        var now = DateTime.UtcNow;
        PeriodStart = new DateTime(now.Year, now.Month, 1);
        PeriodEnd = PeriodStart.AddMonths(1).AddDays(-1);
    }

    public void IncrementUsage(int count = 1)
    {
        UsageCount += count;
        SetUpdatedAt();
    }

    public void UpdateMetadata(string metadata)
    {
        Metadata = metadata;
        SetUpdatedAt();
    }

    public bool IsInCurrentPeriod()
    {
        var now = DateTime.UtcNow;
        return now >= PeriodStart && now <= PeriodEnd;
    }

    public bool IsInPeriod(DateTime start, DateTime end)
    {
        return UsageDate >= start && UsageDate <= end;
    }
}
