using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Diagnostics;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of ETL pipeline service
/// </summary>
public class ETLPipelineService : IETLPipelineService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<ETLPipelineService> _logger;

    public ETLPipelineService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<ETLPipelineService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<ETLPipelineDto> CreatePipelineAsync(CreateETLPipelineRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating ETL pipeline '{Name}' for user {UserId}", request.Name, request.UserId);

            var pipeline = new AnalyticsBIService.Domain.Entities.ETLPipeline(
                request.Name,
                request.Description,
                JsonSerializer.Serialize(request.Configuration),
                request.UserId);

            _context.ETLPipelines.Add(pipeline);
            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(pipeline);

            // Cache the pipeline
            var cacheKey = $"etl:pipeline:{pipeline.Id}";
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("ETL pipeline created with ID {PipelineId}", pipeline.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ETL pipeline");
            throw;
        }
    }

    public async Task<ETLPipelineDto?> GetPipelineAsync(Guid pipelineId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"etl:pipeline:{pipelineId}";
            var cachedResult = await _cacheService.GetAsync<ETLPipelineDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var pipeline = await _context.ETLPipelines
                .FirstOrDefaultAsync(p => p.Id == pipelineId, cancellationToken);

            if (pipeline == null)
                return null;

            var result = MapToDto(pipeline);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ETL pipeline {PipelineId}", pipelineId);
            return null;
        }
    }

    public async Task<List<ETLPipelineDto>> GetUserPipelinesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var pipelines = await _context.ETLPipelines
                .Where(p => p.CreatedBy == userId)
                .OrderByDescending(p => p.UpdatedAt)
                .ToListAsync(cancellationToken);

            return pipelines.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user pipelines for user {UserId}", userId);
            return new List<ETLPipelineDto>();
        }
    }

    public async Task<ETLExecutionResultDto> ExecutePipelineAsync(Guid pipelineId, ETLExecutionOptionsDto? options = null, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var executionId = Guid.NewGuid();

        try
        {
            _logger.LogInformation("Executing ETL pipeline {PipelineId} with execution ID {ExecutionId}", pipelineId, executionId);

            var pipeline = await GetPipelineAsync(pipelineId, cancellationToken);
            if (pipeline == null)
                throw new InvalidOperationException($"Pipeline {pipelineId} not found");

            // Create execution record
            var execution = new ETLExecution(pipelineId, "Manual", null);

            _context.ETLExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            // Execute pipeline steps
            var stepResults = new List<ETLStepResultDto>();
            var logs = new List<ETLExecutionLogDto>();
            var totalRecords = 0;
            var successfulRecords = 0;
            var failedRecords = 0;

            try
            {
                // Extract phase
                var extractResult = await ExecuteExtractPhaseAsync(pipeline.Configuration, options, cancellationToken);
                stepResults.Add(extractResult);
                logs.AddRange(GenerateStepLogs(extractResult));
                totalRecords += extractResult.RecordsProcessed;
                successfulRecords += extractResult.RecordsProcessed;

                // Transform phase
                var transformResult = await ExecuteTransformPhaseAsync(pipeline.Configuration, extractResult.RecordsProcessed, options, cancellationToken);
                stepResults.Add(transformResult);
                logs.AddRange(GenerateStepLogs(transformResult));

                // Load phase
                var loadResult = await ExecuteLoadPhaseAsync(pipeline.Configuration, transformResult.RecordsProcessed, options, cancellationToken);
                stepResults.Add(loadResult);
                logs.AddRange(GenerateStepLogs(loadResult));

                stopwatch.Stop();

                // Update execution record
                execution.Complete(totalRecords, successfulRecords, failedRecords, string.Join("\n", logs.Select(l => l.Message)));

                await _context.SaveChangesAsync(cancellationToken);

                // Update pipeline last execution
                await UpdatePipelineLastExecutionAsync(pipelineId, execution.Status, cancellationToken);

                return new ETLExecutionResultDto
                {
                    ExecutionId = executionId,
                    PipelineId = pipelineId,
                    Status = execution.Status,
                    StartedAt = execution.StartTime,
                    CompletedAt = execution.EndTime ?? DateTime.UtcNow,
                    Duration = execution.Duration,
                    RecordsProcessed = (int)totalRecords,
                    RecordsSuccessful = (int)successfulRecords,
                    RecordsFailed = failedRecords,
                    StepResults = stepResults,
                    Logs = logs,
                    Metrics = CalculateExecutionMetrics(stepResults, stopwatch.Elapsed)
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Update execution record with error
                execution.Fail(ex.Message, string.Join("\n", logs.Select(l => l.Message)));
                await _context.SaveChangesAsync(cancellationToken);

                await UpdatePipelineLastExecutionAsync(pipelineId, execution.Status, cancellationToken);

                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing ETL pipeline {PipelineId}", pipelineId);
            throw;
        }
    }

    public async Task<ETLScheduleDto> SchedulePipelineAsync(ScheduleETLPipelineRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Scheduling ETL pipeline {PipelineId}", request.PipelineId);

            var schedule = new AnalyticsBIService.Domain.Entities.ETLSchedule(
                request.PipelineId,
                request.Name,
                request.ScheduleType,
                request.CronExpression,
                request.StartDate,
                request.EndDate,
                request.ExecutionOptions != null ? JsonSerializer.Serialize(request.ExecutionOptions) : null);

            if (!request.IsActive)
            {
                schedule.Deactivate();
            }

            _context.ETLSchedules.Add(schedule);
            await _context.SaveChangesAsync(cancellationToken);

            return new ETLScheduleDto
            {
                Id = schedule.Id,
                PipelineId = schedule.PipelineId,
                Name = schedule.Name,
                ScheduleType = schedule.ScheduleType,
                CronExpression = schedule.CronExpression,
                NextRunDate = schedule.NextRunDate,
                LastRunDate = schedule.LastRunDate,
                IsActive = schedule.IsActive,
                ExecutionOptions = request.ExecutionOptions,
                CreatedAt = schedule.CreatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling ETL pipeline");
            throw;
        }
    }

    public async Task<List<ETLExecutionHistoryDto>> GetExecutionHistoryAsync(Guid pipelineId, CancellationToken cancellationToken = default)
    {
        try
        {
            var executions = await _context.ETLExecutions
                .Where(e => e.PipelineId == pipelineId)
                .OrderByDescending(e => e.StartTime)
                .Take(50)
                .ToListAsync(cancellationToken);

            return executions.Select(e => new ETLExecutionHistoryDto
            {
                ExecutionId = e.Id,
                StartedAt = e.StartTime,
                CompletedAt = e.EndTime,
                Status = e.Status,
                Duration = e.Duration,
                RecordsProcessed = (int)e.RecordsProcessed,
                ErrorMessage = e.ErrorMessage,
                TriggerType = e.TriggeredBy ?? "Manual",
                TriggeredBy = e.TriggeredByUserId
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution history for pipeline {PipelineId}", pipelineId);
            return new List<ETLExecutionHistoryDto>();
        }
    }

    public async Task<ETLExecutionStatusDto> GetExecutionStatusAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _context.ETLExecutions
                .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

            if (execution == null)
                throw new InvalidOperationException($"Execution {executionId} not found");

            var progressPercentage = execution.Status switch
            {
                "Running" => 50m,
                "Completed" => 100m,
                "Failed" => 0m,
                _ => 0m
            };

            return new ETLExecutionStatusDto
            {
                ExecutionId = executionId,
                Status = execution.Status,
                ProgressPercentage = progressPercentage,
                CurrentStep = execution.Status == "Running" ? "Processing" : execution.Status,
                ProcessedRecords = (int)execution.RecordsProcessed,
                TotalRecords = (int)execution.RecordsProcessed,
                StartedAt = execution.StartTime,
                ElapsedTime = DateTime.UtcNow - execution.StartTime,
                EstimatedTimeRemaining = execution.Status == "Running" ? TimeSpan.FromMinutes(5) : TimeSpan.Zero,
                StepStatuses = GenerateStepStatuses(execution.Status)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting execution status");
            throw;
        }
    }

    public async Task<bool> CancelExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _context.ETLExecutions
                .FirstOrDefaultAsync(e => e.Id == executionId, cancellationToken);

            if (execution == null || execution.Status != "Running")
                return false;

            execution.Cancel();

            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling execution");
            return false;
        }
    }

    public async Task<bool> UpdatePipelineAsync(Guid pipelineId, UpdateETLPipelineRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var pipeline = await _context.ETLPipelines
                .FirstOrDefaultAsync(p => p.Id == pipelineId, cancellationToken);

            if (pipeline == null)
                return false;

            if (!string.IsNullOrEmpty(request.Name))
                pipeline.UpdateName(request.Name);

            if (!string.IsNullOrEmpty(request.Description))
                pipeline.UpdateDescription(request.Description);

            if (request.Configuration != null)
                pipeline.UpdateConfiguration(JsonSerializer.Serialize(request.Configuration));

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    pipeline.Activate();
                else
                    pipeline.Deactivate();
            }

            pipeline.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"etl:pipeline:{pipelineId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ETL pipeline");
            return false;
        }
    }

    public async Task<bool> DeletePipelineAsync(Guid pipelineId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var pipeline = await _context.ETLPipelines
                .FirstOrDefaultAsync(p => p.Id == pipelineId && p.CreatedBy == userId, cancellationToken);

            if (pipeline == null)
                return false;

            _context.ETLPipelines.Remove(pipeline);
            await _context.SaveChangesAsync(cancellationToken);

            // Clear cache
            var cacheKey = $"etl:pipeline:{pipelineId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting ETL pipeline");
            return false;
        }
    }

    public async Task<ETLValidationResult> ValidatePipelineAsync(ETLPipelineConfigurationDto configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new ETLValidationResult { IsValid = true };
            var issues = new List<ETLValidationIssueDto>();

            // Validate data sources
            if (!configuration.DataSources.Any())
            {
                issues.Add(new ETLValidationIssueDto
                {
                    Type = "Configuration",
                    Severity = "Error",
                    Message = "At least one data source must be configured"
                });
                result.IsValid = false;
            }

            // Validate destinations
            if (!configuration.Destinations.Any())
            {
                issues.Add(new ETLValidationIssueDto
                {
                    Type = "Configuration",
                    Severity = "Error",
                    Message = "At least one destination must be configured"
                });
                result.IsValid = false;
            }

            // Validate transformations
            foreach (var transformation in configuration.Transformations)
            {
                if (string.IsNullOrEmpty(transformation.Type))
                {
                    issues.Add(new ETLValidationIssueDto
                    {
                        Type = "Transformation",
                        Severity = "Error",
                        Message = "Transformation type cannot be empty",
                        StepId = transformation.Id
                    });
                    result.IsValid = false;
                }
            }

            result.Issues = issues;
            result.Summary = new ETLValidationSummaryDto
            {
                TotalIssues = issues.Count,
                ErrorCount = issues.Count(i => i.Severity == "Error"),
                WarningCount = issues.Count(i => i.Severity == "Warning"),
                InfoCount = issues.Count(i => i.Severity == "Info")
            };

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating ETL pipeline");
            return new ETLValidationResult
            {
                IsValid = false,
                Errors = new List<string> { "Validation failed due to an internal error" }
            };
        }
    }

    public async Task<List<ETLDataSourceDto>> GetAvailableDataSourcesAsync(CancellationToken cancellationToken = default)
    {
        return GetBuiltInDataSources();
    }

    public async Task<List<ETLTransformationDto>> GetAvailableTransformationsAsync(CancellationToken cancellationToken = default)
    {
        return GetBuiltInTransformations();
    }

    public async Task<ETLConnectionTestResult> TestDataSourceConnectionAsync(ETLDataSourceConfigDto dataSource, CancellationToken cancellationToken = default)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Simulate connection test
            await Task.Delay(100, cancellationToken);

            stopwatch.Stop();

            return new ETLConnectionTestResult
            {
                IsSuccessful = true,
                ResponseTime = stopwatch.Elapsed,
                ConnectionInfo = new Dictionary<string, object>
                {
                    { "ServerVersion", "1.0.0" },
                    { "DatabaseName", "analytics_db" },
                    { "ConnectionType", dataSource.Type }
                },
                AvailableTables = new List<string> { "analytics_events", "metrics", "users", "transactions" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing data source connection");
            return new ETLConnectionTestResult
            {
                IsSuccessful = false,
                ErrorMessage = ex.Message,
                ResponseTime = TimeSpan.Zero
            };
        }
    }

    public async Task<ETLDataPreviewDto> PreviewPipelineDataAsync(Guid pipelineId, int maxRows = 100, CancellationToken cancellationToken = default)
    {
        try
        {
            var pipeline = await GetPipelineAsync(pipelineId, cancellationToken);
            if (pipeline == null)
                throw new InvalidOperationException($"Pipeline {pipelineId} not found");

            // Generate sample preview data
            var sampleData = GenerateSampleData(maxRows);
            var fields = GenerateFieldInfo();

            return new ETLDataPreviewDto
            {
                SampleData = sampleData,
                Fields = fields,
                TotalRecords = sampleData.Count,
                GeneratedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing pipeline data");
            throw;
        }
    }

    public async Task<ETLPipelineMetricsDto> GetPipelineMetricsAsync(Guid pipelineId, CancellationToken cancellationToken = default)
    {
        try
        {
            var executions = await _context.ETLExecutions
                .Where(e => e.PipelineId == pipelineId)
                .OrderByDescending(e => e.StartTime)
                .Take(100)
                .ToListAsync(cancellationToken);

            var totalExecutions = executions.Count;
            var successfulExecutions = executions.Count(e => e.Status == "Completed");
            var failedExecutions = executions.Count(e => e.Status == "Failed");
            var successRate = totalExecutions > 0 ? (decimal)successfulExecutions / totalExecutions * 100 : 0;

            var avgExecutionTime = executions.Where(e => e.Duration.HasValue).Any()
                ? TimeSpan.FromTicks((long)executions.Where(e => e.Duration.HasValue).Average(e => e.Duration!.Value.Ticks))
                : TimeSpan.Zero;

            var totalRecords = executions.Sum(e => e.RecordsProcessed);
            var avgThroughput = avgExecutionTime.TotalSeconds > 0 ? totalRecords / avgExecutionTime.TotalSeconds : 0;

            var trends = executions.Take(30).Select(e => new ETLPerformanceTrendDto
            {
                Date = e.StartTime.Date,
                ExecutionTime = e.Duration ?? TimeSpan.Zero,
                RecordsProcessed = (int)e.RecordsProcessed,
                Throughput = e.Duration?.TotalSeconds > 0 ? (decimal)(e.RecordsProcessed / e.Duration.Value.TotalSeconds) : 0,
                Status = e.Status
            }).ToList();

            return new ETLPipelineMetricsDto
            {
                PipelineId = pipelineId,
                TotalExecutions = totalExecutions,
                SuccessfulExecutions = successfulExecutions,
                FailedExecutions = failedExecutions,
                SuccessRate = Math.Round(successRate, 2),
                AverageExecutionTime = avgExecutionTime,
                TotalRecordsProcessed = totalRecords,
                AverageThroughput = Math.Round((decimal)avgThroughput, 2),
                LastSuccessfulExecution = executions.FirstOrDefault(e => e.Status == "Completed")?.StartTime,
                LastFailedExecution = executions.FirstOrDefault(e => e.Status == "Failed")?.StartTime,
                PerformanceTrends = trends
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pipeline metrics");
            throw;
        }
    }

    // Helper methods
    private ETLPipelineDto MapToDto(AnalyticsBIService.Domain.Entities.ETLPipeline pipeline)
    {
        var configuration = string.IsNullOrEmpty(pipeline.Configuration)
            ? new ETLPipelineConfigurationDto()
            : JsonSerializer.Deserialize<ETLPipelineConfigurationDto>(pipeline.Configuration) ?? new ETLPipelineConfigurationDto();

        return new ETLPipelineDto
        {
            Id = pipeline.Id,
            Name = pipeline.Name,
            Description = pipeline.Description,
            Configuration = configuration,
            Status = pipeline.Status,
            CreatedAt = pipeline.CreatedAt,
            UpdatedAt = pipeline.UpdatedAt ?? DateTime.UtcNow,
            CreatedBy = pipeline.CreatedBy,
            IsActive = pipeline.IsActive,
            LastExecuted = pipeline.LastExecuted,
            ExecutionCount = pipeline.ExecutionCount,
            LastExecutionStatus = pipeline.LastExecutionStatus
        };
    }

    private async Task<ETLStepResultDto> ExecuteExtractPhaseAsync(ETLPipelineConfigurationDto configuration, ETLExecutionOptionsDto? options, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var random = new Random();
        var recordsProcessed = random.Next(100, 1000);

        // Simulate extraction process
        await Task.Delay(random.Next(1000, 3000), cancellationToken);

        stopwatch.Stop();

        return new ETLStepResultDto
        {
            StepId = "extract",
            StepName = "Data Extraction",
            StepType = "Extract",
            Status = "Completed",
            StartedAt = DateTime.UtcNow.Subtract(stopwatch.Elapsed),
            CompletedAt = DateTime.UtcNow,
            Duration = stopwatch.Elapsed,
            RecordsProcessed = recordsProcessed,
            Metrics = new Dictionary<string, object>
            {
                { "DataSources", configuration.DataSources.Count },
                { "ThroughputRecordsPerSecond", recordsProcessed / stopwatch.Elapsed.TotalSeconds }
            }
        };
    }

    private async Task<ETLStepResultDto> ExecuteTransformPhaseAsync(ETLPipelineConfigurationDto configuration, int inputRecords, ETLExecutionOptionsDto? options, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var random = new Random();
        var recordsProcessed = (int)(inputRecords * (0.95 + random.NextDouble() * 0.05)); // 95-100% of input records

        // Simulate transformation process
        await Task.Delay(random.Next(500, 2000), cancellationToken);

        stopwatch.Stop();

        return new ETLStepResultDto
        {
            StepId = "transform",
            StepName = "Data Transformation",
            StepType = "Transform",
            Status = "Completed",
            StartedAt = DateTime.UtcNow.Subtract(stopwatch.Elapsed),
            CompletedAt = DateTime.UtcNow,
            Duration = stopwatch.Elapsed,
            RecordsProcessed = recordsProcessed,
            Metrics = new Dictionary<string, object>
            {
                { "Transformations", configuration.Transformations.Count },
                { "TransformationRate", (decimal)recordsProcessed / inputRecords * 100 }
            }
        };
    }

    private async Task<ETLStepResultDto> ExecuteLoadPhaseAsync(ETLPipelineConfigurationDto configuration, int inputRecords, ETLExecutionOptionsDto? options, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var random = new Random();
        var recordsProcessed = inputRecords; // All transformed records are loaded

        // Simulate loading process
        await Task.Delay(random.Next(800, 2500), cancellationToken);

        stopwatch.Stop();

        return new ETLStepResultDto
        {
            StepId = "load",
            StepName = "Data Loading",
            StepType = "Load",
            Status = "Completed",
            StartedAt = DateTime.UtcNow.Subtract(stopwatch.Elapsed),
            CompletedAt = DateTime.UtcNow,
            Duration = stopwatch.Elapsed,
            RecordsProcessed = recordsProcessed,
            Metrics = new Dictionary<string, object>
            {
                { "Destinations", configuration.Destinations.Count },
                { "LoadRate", recordsProcessed / stopwatch.Elapsed.TotalSeconds }
            }
        };
    }

    private List<ETLExecutionLogDto> GenerateStepLogs(ETLStepResultDto stepResult)
    {
        return new List<ETLExecutionLogDto>
        {
            new()
            {
                Timestamp = stepResult.StartedAt,
                Level = "Information",
                Message = $"Started {stepResult.StepName}",
                StepId = stepResult.StepId
            },
            new()
            {
                Timestamp = stepResult.CompletedAt ?? DateTime.UtcNow,
                Level = "Information",
                Message = $"Completed {stepResult.StepName} - Processed {stepResult.RecordsProcessed} records",
                StepId = stepResult.StepId,
                Properties = stepResult.Metrics
            }
        };
    }

    private ETLExecutionMetricsDto CalculateExecutionMetrics(List<ETLStepResultDto> stepResults, TimeSpan totalDuration)
    {
        var totalRecords = stepResults.Sum(s => s.RecordsProcessed);
        var throughput = totalDuration.TotalSeconds > 0 ? totalRecords / totalDuration.TotalSeconds : 0;

        return new ETLExecutionMetricsDto
        {
            ThroughputRecordsPerSecond = Math.Round((decimal)throughput, 2),
            MemoryUsageBytes = 50 * 1024 * 1024, // 50MB simulated
            CpuUsagePercent = 25.5m,
            ActiveConnections = stepResults.Count,
            CustomMetrics = new Dictionary<string, object>
            {
                { "TotalSteps", stepResults.Count },
                { "AverageStepDuration", stepResults.Average(s => s.Duration?.TotalSeconds ?? 0) }
            }
        };
    }

    private async Task UpdatePipelineLastExecutionAsync(Guid pipelineId, string status, CancellationToken cancellationToken)
    {
        var pipeline = await _context.ETLPipelines
            .FirstOrDefaultAsync(p => p.Id == pipelineId, cancellationToken);

        if (pipeline != null)
        {
            pipeline.RecordExecution(status);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    private DateTime CalculateNextRunDate(string scheduleType, string cronExpression, DateTime startDate)
    {
        // Simplified calculation - would use a proper cron library in real implementation
        return scheduleType.ToUpper() switch
        {
            "DAILY" => startDate.Date.AddDays(1),
            "WEEKLY" => startDate.Date.AddDays(7),
            "MONTHLY" => startDate.Date.AddMonths(1),
            "HOURLY" => startDate.AddHours(1),
            _ => startDate.Date.AddDays(1)
        };
    }

    private List<ETLStepStatusDto> GenerateStepStatuses(string executionStatus)
    {
        var statuses = new List<ETLStepStatusDto>
        {
            new()
            {
                StepId = "extract",
                StepName = "Data Extraction",
                Status = executionStatus == "Running" ? "Running" : executionStatus,
                ProgressPercentage = executionStatus == "Completed" ? 100 : executionStatus == "Running" ? 75 : 0,
                ProcessedRecords = 500,
                CurrentOperation = executionStatus == "Running" ? "Extracting from database" : null
            },
            new()
            {
                StepId = "transform",
                StepName = "Data Transformation",
                Status = executionStatus == "Completed" ? "Completed" : executionStatus == "Running" ? "Pending" : "Not Started",
                ProgressPercentage = executionStatus == "Completed" ? 100 : 0,
                ProcessedRecords = executionStatus == "Completed" ? 475 : 0,
                CurrentOperation = executionStatus == "Running" ? "Waiting for extraction" : null
            },
            new()
            {
                StepId = "load",
                StepName = "Data Loading",
                Status = executionStatus == "Completed" ? "Completed" : "Not Started",
                ProgressPercentage = executionStatus == "Completed" ? 100 : 0,
                ProcessedRecords = executionStatus == "Completed" ? 475 : 0,
                CurrentOperation = null
            }
        };

        return statuses;
    }

    private List<Dictionary<string, object>> GenerateSampleData(int maxRows)
    {
        var data = new List<Dictionary<string, object>>();
        var random = new Random();

        for (int i = 0; i < Math.Min(maxRows, 10); i++)
        {
            data.Add(new Dictionary<string, object>
            {
                { "id", Guid.NewGuid().ToString() },
                { "event_name", $"sample_event_{i + 1}" },
                { "user_id", Guid.NewGuid().ToString() },
                { "timestamp", DateTime.UtcNow.AddDays(-random.Next(0, 30)) },
                { "value", Math.Round((decimal)(random.NextDouble() * 1000), 2) },
                { "category", random.Next(0, 3) switch { 0 => "Analytics", 1 => "Performance", _ => "User" } }
            });
        }

        return data;
    }

    private List<ETLFieldInfoDto> GenerateFieldInfo()
    {
        return new List<ETLFieldInfoDto>
        {
            new() { Name = "id", DataType = "string", IsNullable = false, SampleValue = Guid.NewGuid().ToString() },
            new() { Name = "event_name", DataType = "string", IsNullable = false, MaxLength = 255, SampleValue = "sample_event" },
            new() { Name = "user_id", DataType = "string", IsNullable = false, SampleValue = Guid.NewGuid().ToString() },
            new() { Name = "timestamp", DataType = "datetime", IsNullable = false, SampleValue = DateTime.UtcNow },
            new() { Name = "value", DataType = "decimal", IsNullable = true, SampleValue = 123.45m },
            new() { Name = "category", DataType = "string", IsNullable = true, MaxLength = 100, SampleValue = "Analytics" }
        };
    }

    private List<ETLDataSourceDto> GetBuiltInDataSources()
    {
        return new List<ETLDataSourceDto>
        {
            new()
            {
                Type = "SqlServer",
                Name = "SQL Server Database",
                Description = "Microsoft SQL Server database connection",
                Category = "Database",
                SupportedOperations = new List<string> { "SELECT", "INSERT", "UPDATE", "DELETE" },
                RequiredParameters = new List<ETLParameterDto>
                {
                    new() { Name = "ConnectionString", Type = "string", Description = "Database connection string", IsRequired = true },
                    new() { Name = "Query", Type = "string", Description = "SQL query to execute", IsRequired = true }
                },
                OptionalParameters = new List<ETLParameterDto>
                {
                    new() { Name = "CommandTimeout", Type = "int", Description = "Command timeout in seconds", DefaultValue = 30 },
                    new() { Name = "BatchSize", Type = "int", Description = "Batch size for data processing", DefaultValue = 1000 }
                }
            },
            new()
            {
                Type = "PostgreSQL",
                Name = "PostgreSQL Database",
                Description = "PostgreSQL database connection",
                Category = "Database",
                SupportedOperations = new List<string> { "SELECT", "INSERT", "UPDATE", "DELETE" },
                RequiredParameters = new List<ETLParameterDto>
                {
                    new() { Name = "ConnectionString", Type = "string", Description = "Database connection string", IsRequired = true },
                    new() { Name = "Query", Type = "string", Description = "SQL query to execute", IsRequired = true }
                }
            },
            new()
            {
                Type = "CSV",
                Name = "CSV File",
                Description = "Comma-separated values file",
                Category = "File",
                SupportedOperations = new List<string> { "READ", "WRITE" },
                RequiredParameters = new List<ETLParameterDto>
                {
                    new() { Name = "FilePath", Type = "string", Description = "Path to CSV file", IsRequired = true }
                },
                OptionalParameters = new List<ETLParameterDto>
                {
                    new() { Name = "Delimiter", Type = "string", Description = "Field delimiter", DefaultValue = "," },
                    new() { Name = "HasHeaders", Type = "boolean", Description = "File has header row", DefaultValue = true }
                }
            },
            new()
            {
                Type = "REST_API",
                Name = "REST API",
                Description = "RESTful web service endpoint",
                Category = "API",
                SupportedOperations = new List<string> { "GET", "POST" },
                RequiredParameters = new List<ETLParameterDto>
                {
                    new() { Name = "Endpoint", Type = "string", Description = "API endpoint URL", IsRequired = true }
                },
                OptionalParameters = new List<ETLParameterDto>
                {
                    new() { Name = "Headers", Type = "object", Description = "HTTP headers" },
                    new() { Name = "Authentication", Type = "object", Description = "Authentication configuration" }
                }
            }
        };
    }

    private List<ETLTransformationDto> GetBuiltInTransformations()
    {
        return new List<ETLTransformationDto>
        {
            new()
            {
                Type = "FieldMapping",
                Name = "Field Mapping",
                Description = "Map source fields to target fields",
                Category = "Data Mapping",
                Parameters = new List<ETLParameterDto>
                {
                    new() { Name = "Mappings", Type = "array", Description = "Field mapping configuration", IsRequired = true }
                },
                SupportedDataTypes = new List<string> { "string", "int", "decimal", "datetime", "boolean" }
            },
            new()
            {
                Type = "DataTypeConversion",
                Name = "Data Type Conversion",
                Description = "Convert data types between source and target",
                Category = "Data Conversion",
                Parameters = new List<ETLParameterDto>
                {
                    new() { Name = "SourceType", Type = "string", Description = "Source data type", IsRequired = true },
                    new() { Name = "TargetType", Type = "string", Description = "Target data type", IsRequired = true },
                    new() { Name = "Format", Type = "string", Description = "Conversion format string" }
                },
                SupportedDataTypes = new List<string> { "string", "int", "decimal", "datetime", "boolean" }
            },
            new()
            {
                Type = "Filter",
                Name = "Data Filter",
                Description = "Filter records based on conditions",
                Category = "Data Filtering",
                Parameters = new List<ETLParameterDto>
                {
                    new() { Name = "Conditions", Type = "array", Description = "Filter conditions", IsRequired = true }
                },
                SupportedDataTypes = new List<string> { "string", "int", "decimal", "datetime", "boolean" }
            },
            new()
            {
                Type = "Aggregation",
                Name = "Data Aggregation",
                Description = "Aggregate data using functions like SUM, COUNT, AVG",
                Category = "Data Aggregation",
                Parameters = new List<ETLParameterDto>
                {
                    new() { Name = "GroupByFields", Type = "array", Description = "Fields to group by", IsRequired = true },
                    new() { Name = "AggregateFields", Type = "array", Description = "Fields to aggregate", IsRequired = true }
                },
                SupportedDataTypes = new List<string> { "int", "decimal", "datetime" }
            },
            new()
            {
                Type = "Join",
                Name = "Data Join",
                Description = "Join data from multiple sources",
                Category = "Data Joining",
                Parameters = new List<ETLParameterDto>
                {
                    new() { Name = "JoinType", Type = "string", Description = "Type of join (INNER, LEFT, RIGHT, FULL)", IsRequired = true },
                    new() { Name = "JoinConditions", Type = "array", Description = "Join conditions", IsRequired = true }
                },
                SupportedDataTypes = new List<string> { "string", "int", "decimal", "datetime", "boolean" }
            }
        };
    }
}
