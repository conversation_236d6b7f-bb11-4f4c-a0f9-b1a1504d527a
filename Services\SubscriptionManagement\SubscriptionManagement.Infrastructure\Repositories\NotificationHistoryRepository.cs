using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class NotificationHistoryRepository : INotificationHistoryRepository
    {
        private readonly SubscriptionDbContext _context;

        public NotificationHistoryRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<NotificationHistory?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .FirstOrDefaultAsync(n => n.Id == id, cancellationToken);
        }

        public async Task<NotificationHistory?> GetByExternalIdAsync(string externalNotificationId, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .FirstOrDefaultAsync(n => n.ExternalNotificationId == externalNotificationId, cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetBySubscriptionIdAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.SubscriptionId == subscriptionId)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.UserId == userId)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.Status == status)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetByChannelAsync(string channel, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.Channel == channel)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetByTypeAsync(NotificationType type, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.Type == type)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetPendingNotificationsAsync(CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .Where(n => n.Status == "Pending")
                .OrderBy(n => n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetScheduledNotificationsAsync(DateTime? upToDate = null, CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories
                .Where(n => n.Status == "Pending" && n.ScheduledAt.HasValue);

            if (upToDate.HasValue)
            {
                query = query.Where(n => n.ScheduledAt <= upToDate.Value);
            }

            return await query
                .OrderBy(n => n.ScheduledAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<NotificationHistory>> GetFailedNotificationsForRetryAsync(CancellationToken cancellationToken = default)
        {
            var now = DateTime.UtcNow;
            return await _context.NotificationHistories
                .Where(n => n.Status == "Failed" && 
                           n.RetryCount < 3 && 
                           (n.LastRetryAt == null || 
                            (n.RetryCount == 0 && n.LastRetryAt.Value.AddMinutes(1) <= now) ||
                            (n.RetryCount == 1 && n.LastRetryAt.Value.AddMinutes(5) <= now) ||
                            (n.RetryCount == 2 && n.LastRetryAt.Value.AddMinutes(15) <= now)))
                .OrderBy(n => n.LastRetryAt ?? n.CreatedAt)
                .ToListAsync(cancellationToken);
        }

        public async Task<(List<NotificationHistory> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            Guid? subscriptionId = null,
            Guid? userId = null,
            NotificationType? type = null,
            string? channel = null,
            string? status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            Guid? triggeredByUserId = null,
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories.AsQueryable();

            if (subscriptionId.HasValue)
                query = query.Where(n => n.SubscriptionId == subscriptionId.Value);

            if (userId.HasValue)
                query = query.Where(n => n.UserId == userId.Value);

            if (type.HasValue)
                query = query.Where(n => n.Type == type.Value);

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(n => n.Channel == channel);

            if (!string.IsNullOrEmpty(status))
                query = query.Where(n => n.Status == status);

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            if (triggeredByUserId.HasValue)
                query = query.Where(n => n.TriggeredByUserId == triggeredByUserId.Value);

            var totalCount = await query.CountAsync(cancellationToken);

            var items = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (items, totalCount);
        }

        public async Task<NotificationHistory> AddAsync(NotificationHistory notification, CancellationToken cancellationToken = default)
        {
            _context.NotificationHistories.Add(notification);
            await _context.SaveChangesAsync(cancellationToken);
            return notification;
        }

        public async Task UpdateAsync(NotificationHistory notification, CancellationToken cancellationToken = default)
        {
            _context.NotificationHistories.Update(notification);
            await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var notification = await _context.NotificationHistories.FindAsync(new object[] { id }, cancellationToken);
            if (notification != null)
            {
                _context.NotificationHistories.Remove(notification);
                await _context.SaveChangesAsync(cancellationToken);
            }
        }

        public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .AnyAsync(n => n.Id == id, cancellationToken);
        }

        public async Task<int> GetCountAsync(CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories.CountAsync(cancellationToken);
        }

        public async Task<int> GetCountByStatusAsync(string status, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .CountAsync(n => n.Status == status, cancellationToken);
        }

        public async Task<int> GetCountByChannelAsync(string channel, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .CountAsync(n => n.Channel == channel, cancellationToken);
        }

        public async Task<int> GetCountByTypeAsync(NotificationType type, CancellationToken cancellationToken = default)
        {
            return await _context.NotificationHistories
                .CountAsync(n => n.Type == type, cancellationToken);
        }

        public async Task<Dictionary<string, int>> GetNotificationCountsByStatusAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(n => n.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count, cancellationToken);
        }

        public async Task<Dictionary<string, int>> GetNotificationCountsByChannelAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(n => n.Channel)
                .Select(g => new { Channel = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Channel, x => x.Count, cancellationToken);
        }

        public async Task<Dictionary<NotificationType, int>> GetNotificationCountsByTypeAsync(
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(n => n.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Type, x => x.Count, cancellationToken);
        }

        public async Task<double> GetDeliveryRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories.AsQueryable();

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(n => n.Channel == channel);

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            var total = await query.CountAsync(cancellationToken);
            if (total == 0) return 0;

            var delivered = await query.CountAsync(n => n.Status == "Delivered", cancellationToken);
            return (double)delivered / total * 100;
        }

        public async Task<double> GetReadRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories
                .Where(n => n.Status == "Delivered");

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(n => n.Channel == channel);

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            var total = await query.CountAsync(cancellationToken);
            if (total == 0) return 0;

            var read = await query.CountAsync(n => n.ReadAt.HasValue, cancellationToken);
            return (double)read / total * 100;
        }

        public async Task<double> GetClickRateAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories
                .Where(n => n.Status == "Delivered");

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(n => n.Channel == channel);

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            var total = await query.CountAsync(cancellationToken);
            if (total == 0) return 0;

            var clicked = await query.CountAsync(n => n.ClickedAt.HasValue, cancellationToken);
            return (double)clicked / total * 100;
        }

        public async Task<TimeSpan> GetAverageDeliveryTimeAsync(
            string? channel = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            CancellationToken cancellationToken = default)
        {
            var query = _context.NotificationHistories
                .Where(n => n.SentAt.HasValue && n.DeliveredAt.HasValue);

            if (!string.IsNullOrEmpty(channel))
                query = query.Where(n => n.Channel == channel);

            if (fromDate.HasValue)
                query = query.Where(n => n.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(n => n.CreatedAt <= toDate.Value);

            var deliveryTimes = await query
                .Select(n => EF.Functions.DateDiffSecond(n.SentAt!.Value, n.DeliveredAt!.Value))
                .ToListAsync(cancellationToken);

            if (!deliveryTimes.Any()) return TimeSpan.Zero;

            var averageSeconds = deliveryTimes.Average();
            return TimeSpan.FromSeconds(averageSeconds);
        }
    }
}
