using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MonitoringObservability.Domain.Interfaces;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Infrastructure.BackgroundServices;

public class HealthCheckExecutorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<HealthCheckExecutorService> _logger;
    private readonly TimeSpan _executionInterval = TimeSpan.FromSeconds(30); // Check every 30 seconds

    public HealthCheckExecutorService(
        IServiceProvider serviceProvider,
        ILogger<HealthCheckExecutorService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Health Check Executor Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ExecuteHealthChecksAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while executing health checks");
            }

            await Task.Delay(_executionInterval, stoppingToken);
        }

        _logger.LogInformation("Health Check Executor Service stopped");
    }

    private async Task ExecuteHealthChecksAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var healthCheckRepository = scope.ServiceProvider.GetRequiredService<IHealthCheckRepository>();
        var httpClientFactory = scope.ServiceProvider.GetRequiredService<IHttpClientFactory>();

        try
        {
            // Get health checks that are due for execution
            var dueHealthChecks = await healthCheckRepository.GetDueForCheckAsync(cancellationToken);

            foreach (var healthCheck in dueHealthChecks)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    await ExecuteHealthCheckAsync(healthCheck, httpClientFactory, healthCheckRepository, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to execute health check {HealthCheckId} for service {ServiceName}", 
                        healthCheck.Id, healthCheck.ServiceName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving health checks for execution");
        }
    }

    private async Task ExecuteHealthCheckAsync(
        Domain.Entities.HealthCheck healthCheck,
        IHttpClientFactory httpClientFactory,
        IHealthCheckRepository repository,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Executing health check {HealthCheckName} for service {ServiceName}", 
                healthCheck.Name, healthCheck.ServiceName);

            using var httpClient = httpClientFactory.CreateClient();
            httpClient.Timeout = healthCheck.Timeout;

            // Add custom headers if configured
            foreach (var header in healthCheck.Headers)
            {
                httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            // Execute the health check
            var response = await httpClient.GetAsync(healthCheck.Endpoint, cancellationToken);
            stopwatch.Stop();

            var duration = stopwatch.Elapsed;
            var status = response.IsSuccessStatusCode ? HealthStatus.Healthy : HealthStatus.Unhealthy;
            var errorMessage = response.IsSuccessStatusCode ? null : $"HTTP {(int)response.StatusCode} {response.ReasonPhrase}";

            // Record the result
            healthCheck.RecordCheckResult(status, duration, errorMessage);

            await repository.UpdateAsync(healthCheck, cancellationToken);

            _logger.LogDebug("Health check {HealthCheckName} completed with status {Status} in {Duration}ms", 
                healthCheck.Name, status, duration.TotalMilliseconds);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            healthCheck.RecordCheckResult(HealthStatus.Unhealthy, duration, "Health check timed out");
            await repository.UpdateAsync(healthCheck, cancellationToken);

            _logger.LogWarning("Health check {HealthCheckName} timed out after {Duration}ms", 
                healthCheck.Name, duration.TotalMilliseconds);
        }
        catch (HttpRequestException ex)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            healthCheck.RecordCheckResult(HealthStatus.Unhealthy, duration, ex.Message);
            await repository.UpdateAsync(healthCheck, cancellationToken);

            _logger.LogWarning(ex, "Health check {HealthCheckName} failed with HTTP error", healthCheck.Name);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            healthCheck.RecordCheckResult(HealthStatus.Unknown, duration, ex.Message);
            await repository.UpdateAsync(healthCheck, cancellationToken);

            _logger.LogError(ex, "Health check {HealthCheckName} failed with unexpected error", healthCheck.Name);
        }
    }
}

public class MetricThresholdEvaluatorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MetricThresholdEvaluatorService> _logger;
    private readonly TimeSpan _evaluationInterval = TimeSpan.FromMinutes(1); // Evaluate every minute

    public MetricThresholdEvaluatorService(
        IServiceProvider serviceProvider,
        ILogger<MetricThresholdEvaluatorService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Metric Threshold Evaluator Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await EvaluateThresholdsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while evaluating metric thresholds");
            }

            await Task.Delay(_evaluationInterval, stoppingToken);
        }

        _logger.LogInformation("Metric Threshold Evaluator Service stopped");
    }

    private async Task EvaluateThresholdsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var metricRepository = scope.ServiceProvider.GetRequiredService<IMetricRepository>();
        var alertRepository = scope.ServiceProvider.GetRequiredService<IAlertRepository>();

        try
        {
            // Get all metrics with alerting enabled
            var metricsWithAlerting = await metricRepository.SearchAsync(
                alertingEnabled: true,
                cancellationToken: cancellationToken);

            foreach (var metric in metricsWithAlerting)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    await EvaluateMetricThresholdAsync(metric, alertRepository, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to evaluate threshold for metric {MetricName} in service {ServiceName}", 
                        metric.Name, metric.ServiceName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving metrics for threshold evaluation");
        }
    }

    private async Task EvaluateMetricThresholdAsync(
        Domain.Entities.Metric metric,
        IAlertRepository alertRepository,
        CancellationToken cancellationToken)
    {
        if (metric.AlertThreshold == null)
            return;

        var currentValue = metric.CurrentValue.Value;
        var severity = metric.AlertThreshold.EvaluateThreshold(currentValue);

        // Only create alerts for warning or higher severity
        if (severity >= AlertSeverity.Warning)
        {
            // Check if there's already an open alert for this metric
            var existingAlerts = await alertRepository.SearchAsync(
                serviceName: metric.ServiceName,
                status: AlertStatus.Open,
                cancellationToken: cancellationToken);

            var existingAlert = existingAlerts.FirstOrDefault(a => 
                a.MetricName == metric.Name && a.ServiceName == metric.ServiceName);

            if (existingAlert == null)
            {
                // Create new alert
                var alert = new Domain.Entities.Alert(
                    $"Threshold breached for {metric.Name}",
                    $"Metric {metric.Name} in service {metric.ServiceName} has exceeded the {severity.ToString().ToLower()} threshold. Current value: {currentValue}, Threshold: {metric.AlertThreshold.CriticalThreshold}",
                    severity,
                    "MetricThresholdEvaluator",
                    metric.ServiceName,
                    metric.Name,
                    currentValue,
                    metric.AlertThreshold);

                await alertRepository.AddAsync(alert, cancellationToken);

                _logger.LogWarning("Alert created for metric {MetricName} in service {ServiceName}. Current value: {CurrentValue}, Threshold: {Threshold}", 
                    metric.Name, metric.ServiceName, currentValue, metric.AlertThreshold.CriticalThreshold);
            }
            else
            {
                // Update existing alert with new value
                existingAlert.UpdateCurrentValue(currentValue);
                await alertRepository.UpdateAsync(existingAlert, cancellationToken);

                _logger.LogDebug("Updated existing alert for metric {MetricName} with new value: {CurrentValue}", 
                    metric.Name, currentValue);
            }
        }
    }
}
