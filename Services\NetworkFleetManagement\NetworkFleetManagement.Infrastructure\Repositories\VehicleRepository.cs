using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

public class VehicleRepository : IVehicleRepository
{
    private readonly NetworkFleetDbContext _context;

    public VehicleRepository(NetworkFleetDbContext context)
    {
        _context = context;
    }

    public async Task<Vehicle?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Include(v => v.Documents)
            .Include(v => v.MaintenanceRecords)
            .Include(v => v.DriverAssignments)
            .FirstOrDefaultAsync(v => v.Id == id, cancellationToken);
    }

    public async Task<Vehicle?> GetByRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Include(v => v.Documents)
            .Include(v => v.MaintenanceRecords)
            .Include(v => v.DriverAssignments)
            .FirstOrDefaultAsync(v => v.RegistrationNumber == registrationNumber, cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Include(v => v.Documents)
            .Include(v => v.MaintenanceRecords)
            .Where(v => v.CarrierId == carrierId)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetByStatusAsync(VehicleStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Where(v => v.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetAvailableVehiclesAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Where(v => v.Status == VehicleStatus.Available &&
                       (!v.InsuranceExpiryDate.HasValue || v.InsuranceExpiryDate.Value > today) &&
                       (!v.FitnessExpiryDate.HasValue || v.FitnessExpiryDate.Value > today) &&
                       (!v.PermitExpiryDate.HasValue || v.PermitExpiryDate.Value > today))
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetVehiclesRequiringMaintenanceAsync(int daysThreshold = 7, CancellationToken cancellationToken = default)
    {
        var thresholdDate = DateTime.UtcNow.AddDays(daysThreshold);
        
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Where(v => v.NextMaintenanceDate.HasValue && v.NextMaintenanceDate.Value <= thresholdDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetVehiclesWithExpiredDocumentsAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Include(v => v.Documents)
            .Where(v => (v.InsuranceExpiryDate.HasValue && v.InsuranceExpiryDate.Value <= today) ||
                       (v.FitnessExpiryDate.HasValue && v.FitnessExpiryDate.Value <= today) ||
                       (v.PermitExpiryDate.HasValue && v.PermitExpiryDate.Value <= today) ||
                       v.Documents.Any(d => d.ExpiryDate.HasValue && d.ExpiryDate.Value <= today))
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> GetVehiclesByTypeAsync(VehicleType vehicleType, CancellationToken cancellationToken = default)
    {
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Where(v => v.VehicleType == vehicleType)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Vehicle>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var lowerSearchTerm = searchTerm.ToLower();
        
        return await _context.Vehicles
            .Include(v => v.Carrier)
            .Where(v => v.RegistrationNumber.ToLower().Contains(lowerSearchTerm) ||
                       v.Make.ToLower().Contains(lowerSearchTerm) ||
                       v.Model.ToLower().Contains(lowerSearchTerm) ||
                       v.Carrier.CompanyName.ToLower().Contains(lowerSearchTerm))
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Vehicle> Vehicles, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Guid? carrierId = null,
        VehicleStatus? status = null,
        VehicleType? vehicleType = null,
        string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Vehicles
            .Include(v => v.Carrier)
            .AsQueryable();

        // Apply filters
        if (carrierId.HasValue)
        {
            query = query.Where(v => v.CarrierId == carrierId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(v => v.Status == status.Value);
        }

        if (vehicleType.HasValue)
        {
            query = query.Where(v => v.VehicleType == vehicleType.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(v => v.RegistrationNumber.ToLower().Contains(lowerSearchTerm) ||
                                   v.Make.ToLower().Contains(lowerSearchTerm) ||
                                   v.Model.ToLower().Contains(lowerSearchTerm) ||
                                   v.Carrier.CompanyName.ToLower().Contains(lowerSearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var vehicles = await query
            .OrderBy(v => v.RegistrationNumber)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (vehicles, totalCount);
    }

    public void Add(Vehicle vehicle)
    {
        _context.Vehicles.Add(vehicle);
    }

    public void Update(Vehicle vehicle)
    {
        _context.Vehicles.Update(vehicle);
    }

    public void Remove(Vehicle vehicle)
    {
        _context.Vehicles.Remove(vehicle);
    }
}
