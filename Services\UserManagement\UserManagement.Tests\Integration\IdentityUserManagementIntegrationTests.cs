using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using UserManagement.API;
using UserManagement.API.Controllers;
using UserManagement.Domain.Entities;
using UserManagement.Infrastructure.Data;
using UserManagement.Tests.Helpers;
using Xunit;

namespace UserManagement.Tests.Integration
{
    /// <summary>
    /// Integration tests that verify the interaction between Identity and UserManagement services
    /// </summary>
    public class IdentityUserManagementIntegrationTests : IClassFixture<TestWebApplicationFactory<Program>>
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public IdentityUserManagementIntegrationTests(TestWebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task UserRegistrationWorkflow_ShouldCreateProfileAndDocumentSubmission()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var userType = UserType.TransportCompany;
            var email = "<EMAIL>";

            // Act 1: Create user profile (simulating what happens after Identity service registration)
            var createProfileRequest = new CreateUserProfileRequest
            {
                UserId = userId,
                UserType = userType,
                Email = email,
                FirstName = "Transport",
                LastName = "Company",
                PhoneNumber = "+1234567890"
            };

            var json = JsonSerializer.Serialize(createProfileRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var createResponse = await _client.PostAsync("/api/userprofiles", content);

            // Assert 1: Profile created successfully
            createResponse.IsSuccessStatusCode.Should().BeTrue();

            // Act 2: Verify profile exists
            var getResponse = await _client.GetAsync($"/api/userprofiles/user/{userId}");

            // Assert 2: Profile can be retrieved
            getResponse.IsSuccessStatusCode.Should().BeTrue();

            // Act 3: Verify document submission was created
            var documentsResponse = await _client.GetAsync($"/api/documents/user/{userId}");

            // Assert 3: Document submission exists
            documentsResponse.IsSuccessStatusCode.Should().BeTrue();

            // Verify in database
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();

            var profile = await context.UserProfiles.FindAsync(userId);
            profile.Should().NotBeNull();
            profile!.UserType.Should().Be(userType);
            profile.Email.Should().Be(email);

            var documentSubmission = await context.DocumentSubmissions
                .FirstOrDefaultAsync(ds => ds.UserId == userId);
            documentSubmission.Should().NotBeNull();
            documentSubmission!.UserType.Should().Be(userType);
            documentSubmission.Documents.Should().HaveCount(4); // TransportCompany requires 4 documents
        }

        [Fact]
        public async Task KycWorkflow_ShouldCompleteSuccessfully()
        {
            // Arrange
            var userId = await CreateTestTransportCompany();

            // Act 1: Upload required documents
            await UploadDocument(userId, DocumentType.ProfilePhoto, "photo.jpg");
            await UploadDocument(userId, DocumentType.GstCertificate, "gst.pdf");
            await UploadDocument(userId, DocumentType.TradeLicense, "trade.pdf");
            await UploadDocument(userId, DocumentType.PanCard, "pan.jpg");

            // Act 2: Submit documents for review
            var submissionId = await GetDocumentSubmissionId(userId);
            var submitResponse = await _client.PostAsync($"/api/documents/submission/{submissionId}/submit", null);

            // Assert 2: Documents submitted successfully
            submitResponse.IsSuccessStatusCode.Should().BeTrue();

            // Act 3: Admin approves documents (simulating admin action)
            var approveResponse = await _client.PostAsync($"/api/admin/documents/{submissionId}/approve", null);

            // Assert 3: Documents approved successfully
            approveResponse.IsSuccessStatusCode.Should().BeTrue();

            // Verify final state
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();

            var documentSubmission = await context.DocumentSubmissions
                .Include(ds => ds.Documents)
                .FirstOrDefaultAsync(ds => ds.UserId == userId);

            documentSubmission.Should().NotBeNull();
            documentSubmission!.OverallStatus.Should().Be(DocumentStatus.Approved);
            documentSubmission.Documents.Should().OnlyContain(d => d.Status == DocumentStatus.Approved);
        }

        [Fact]
        public async Task UserTypeSpecificDocuments_ShouldBeEnforced()
        {
            // Arrange
            var driverUserId = await CreateTestDriver();
            var transportUserId = await CreateTestTransportCompany();

            // Act & Assert 1: Driver should not be able to upload GST certificate
            var driverGstUpload = await UploadDocument(driverUserId, DocumentType.GstCertificate, "gst.pdf");
            driverGstUpload.IsSuccessStatusCode.Should().BeFalse();

            // Act & Assert 2: Transport company should not be able to upload driving license
            var transportLicenseUpload = await UploadDocument(transportUserId, DocumentType.DrivingLicense, "license.jpg");
            transportLicenseUpload.IsSuccessStatusCode.Should().BeFalse();

            // Act & Assert 3: Driver should be able to upload driving license
            var driverLicenseUpload = await UploadDocument(driverUserId, DocumentType.DrivingLicense, "license.jpg");
            driverLicenseUpload.IsSuccessStatusCode.Should().BeTrue();

            // Act & Assert 4: Transport company should be able to upload GST certificate
            var transportGstUpload = await UploadDocument(transportUserId, DocumentType.GstCertificate, "gst.pdf");
            transportGstUpload.IsSuccessStatusCode.Should().BeTrue();
        }

        [Fact]
        public async Task AdminDashboard_ShouldShowPendingApprovals()
        {
            // Arrange
            var userId1 = await CreateTestTransportCompany();
            var userId2 = await CreateTestDriver();

            // Upload and submit documents for both users
            await CompleteDocumentUpload(userId1, UserType.TransportCompany);
            await CompleteDocumentUpload(userId2, UserType.Driver);

            // Act
            var dashboardResponse = await _client.GetAsync("/api/admin/pending-approvals");

            // Assert
            dashboardResponse.IsSuccessStatusCode.Should().BeTrue();

            var content = await dashboardResponse.Content.ReadAsStringAsync();
            content.Should().NotBeEmpty();

            // The response should contain information about pending approvals
            // In a real test, you would deserialize and verify the specific data
        }

        private async Task<Guid> CreateTestTransportCompany()
        {
            var userId = Guid.NewGuid();
            var request = new CreateUserProfileRequest
            {
                UserId = userId,
                UserType = UserType.TransportCompany,
                Email = "<EMAIL>",
                FirstName = "Transport",
                LastName = "Company",
                PhoneNumber = "+1234567890"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/userprofiles", content);

            return userId;
        }

        private async Task<Guid> CreateTestDriver()
        {
            var userId = Guid.NewGuid();
            var request = new CreateUserProfileRequest
            {
                UserId = userId,
                UserType = UserType.Driver,
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Driver",
                PhoneNumber = "+1234567890"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/userprofiles", content);

            return userId;
        }

        private async Task<HttpResponseMessage> UploadDocument(Guid userId, DocumentType documentType, string fileName)
        {
            var content = new MultipartFormDataContent();
            content.Add(new StringContent(userId.ToString()), "UserId");
            content.Add(new StringContent(documentType.ToString()), "DocumentType");

            var fileContent = new ByteArrayContent(new byte[] { 1, 2, 3, 4 });
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
            content.Add(fileContent, "File", fileName);

            return await _client.PostAsync("/api/documents/upload", content);
        }

        private async Task<Guid> GetDocumentSubmissionId(Guid userId)
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();

            var submission = await context.DocumentSubmissions
                .FirstOrDefaultAsync(ds => ds.UserId == userId);

            return submission?.Id ?? Guid.Empty;
        }

        private async Task CompleteDocumentUpload(Guid userId, UserType userType)
        {
            // Upload all required documents based on user type
            await UploadDocument(userId, DocumentType.ProfilePhoto, "photo.jpg");

            switch (userType)
            {
                case UserType.TransportCompany:
                    await UploadDocument(userId, DocumentType.GstCertificate, "gst.pdf");
                    await UploadDocument(userId, DocumentType.TradeLicense, "trade.pdf");
                    await UploadDocument(userId, DocumentType.PanCard, "pan.jpg");
                    break;
                case UserType.Driver:
                    await UploadDocument(userId, DocumentType.DrivingLicense, "license.jpg");
                    await UploadDocument(userId, DocumentType.AadharCard, "aadhar.jpg");
                    break;
                    // Add other user types as needed
            }

            // Submit for review
            var submissionId = await GetDocumentSubmissionId(userId);
            await _client.PostAsync($"/api/documents/submission/{submissionId}/submit", null);
        }
    }
}
