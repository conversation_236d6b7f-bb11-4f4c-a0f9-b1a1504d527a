# Financial & Payment Service

A comprehensive financial and payment management microservice for the TLI Logistics platform, handling escrow accounts, multi-party settlements, commission calculations, and payment dispute resolution.

## 🏗️ Architecture

This service follows Clean Architecture principles with:

- **Domain Layer**: Core business logic, entities, value objects, and domain events
- **Application Layer**: Use cases, commands, queries, and application services
- **Infrastructure Layer**: Data access, external services, and infrastructure concerns
- **API Layer**: REST API controllers and presentation logic

## 🚀 Features

### 💰 Escrow Management

- **Escrow Account Creation**: Automated escrow account setup for orders
- **Multi-milestone Funding**: Support for milestone-based payments
- **Automatic Release Triggers**: Trip completion-based fund releases
- **Refund Processing**: Comprehensive refund handling with audit trails
- **Real-time Monitoring**: Escrow status tracking and notifications

### 🔄 Multi-party Settlement Processing

- **Automated Settlements**: Trip completion-triggered settlement processing
- **Distribution Management**: Multi-recipient payment distributions
- **Commission Calculations**: Automated broker commission processing
- **Settlement Reconciliation**: Complete audit trails and reporting
- **Failure Handling**: Robust error handling and retry mechanisms

### 💼 Commission Engine

- **Flexible Structures**: Percentage, fixed amount, and tiered commission models
- **Dynamic Calculations**: Real-time commission calculations with adjustments
- **Tax-Aware Processing**: Comprehensive GST and TDS calculations
- **Approval Workflows**: Multi-level commission approval processes
- **Dispute Resolution**: Commission dispute handling and resolution
- **Performance Tracking**: Commission analytics and optimization

### 🧾 Tax Configuration & Compliance

- **GST Management**: Comprehensive GST configuration and calculation
- **TDS Processing**: Automated TDS calculations with section-wise rates
- **HSN Code Management**: Complete HSN code database with GST mappings
- **Multi-Jurisdiction Support**: State and city-specific tax configurations
- **Compliance Reporting**: Tax compliance reports and audit trails
- **Real-time Calculations**: Dynamic tax calculations for all transactions

### ⚖️ Payment Dispute Resolution

- **Dispute Creation**: Multi-role dispute initiation capabilities
- **Investigation Workflows**: Structured dispute investigation processes
- **Document Management**: Dispute-related document handling
- **Escalation Procedures**: Priority-based dispute escalation
- **Resolution Tracking**: Complete dispute lifecycle management

### 💳 Enhanced Payment Processing

- **Multiple Payment Methods**: UPI, wallets, bank transfers, credit terms
- **Recurring Payments**: Automated recurring payment scheduling
- **Payment Verification**: Real-time payment status verification
- **Fraud Prevention**: Advanced payment security measures
- **Payment Analytics**: Comprehensive payment reporting and insights

## 🎯 Role-based Functionalities

### 👨‍💼 Admin Panel

- **Payment & Escrow Management**: Monitor and manage all escrow operations
- **Dispute Resolution**: Handle payment disputes and investigations
- **Commission Oversight**: Approve and monitor commission calculations
- **Financial Analytics**: Generate comprehensive financial reports
- **System Monitoring**: Track payment system health and performance

### 🏢 Transport Companies

- **Escrow Funding**: Fund escrow accounts for confirmed orders
- **Payment Tracking**: Monitor escrow status and release triggers
- **Financial Reporting**: Access detailed financial statements
- **Dispute Management**: Initiate and track payment disputes
- **Invoice Management**: Handle customer invoicing and collections

### 🤝 Brokers

- **Margin Management**: Set and optimize margin structures
- **Commission Tracking**: Monitor commission earnings and payments
- **Settlement Processing**: Manage multi-party payment settlements
- **Pricing Optimization**: Analyze and optimize pricing strategies
- **Financial Analytics**: Access detailed margin and commission reports

### 🚛 Carriers

- **Earnings Tracking**: Monitor trip earnings and payment status
- **Automatic Payments**: Receive automatic payments upon trip completion
- **Payment History**: Access comprehensive payment records
- **Dispute Resolution**: Initiate payment-related disputes
- **Financial Planning**: Generate earnings reports for planning

### 📦 Shippers

- **Multiple Payment Options**: UPI, wallets, bank transfers, credit terms
- **Automated Processing**: Automatic payment upon delivery confirmation
- **Payment Scheduling**: Schedule recurring payments for regular shipments
- **Transaction History**: Comprehensive payment and transaction records
- **Dispute Handling**: Initiate and track payment disputes

## 🔧 Technical Stack

- **.NET 8**: Latest .NET framework for high performance
- **Entity Framework Core**: ORM with TimescaleDB support
- **TimescaleDB**: Time-series database for financial data
- **RazorPay**: Payment gateway integration for Indian market
- **MediatR**: CQRS pattern implementation
- **FluentValidation**: Input validation and business rules
- **Serilog**: Structured logging and monitoring
- **Docker**: Containerization for deployment

## 🗄️ Database Schema

### Core Entities

- **EscrowAccounts**: Main escrow account management
- **EscrowTransactions**: All escrow-related transactions
- **EscrowMilestones**: Milestone-based payment tracking
- **Settlements**: Multi-party settlement processing
- **SettlementDistributions**: Individual payment distributions
- **Commissions**: Commission calculations and tracking
- **CommissionAdjustments**: Commission modifications and adjustments
- **PaymentDisputes**: Dispute management and resolution
- **DisputeComments**: Dispute communication tracking
- **DisputeDocuments**: Dispute-related document storage

### Tax Configuration Entities

- **TaxConfigurations**: Master tax configuration settings
- **GstConfigurations**: GST rate configurations by service category
- **TdsConfigurations**: TDS rate configurations by section and entity type
- **HsnCodes**: HSN code database with GST rate mappings
- **TaxAwarePayments**: Payments with comprehensive tax calculations
- **TaxAwareInvoices**: Invoices with tax compliance features
- **TaxConfigurationHistories**: Audit trails for tax configuration changes

## 🔄 Integration Events

### Published Events

- `escrow.account.created` - New escrow account creation
- `escrow.account.funded` - Escrow account funding completion
- `escrow.funds.released` - Escrow fund release processing
- `settlement.created` - New settlement initiation
- `settlement.completed` - Settlement processing completion
- `commission.calculated` - Commission calculation completion
- `commission.paid` - Commission payment processing
- `dispute.created` - New payment dispute creation
- `dispute.resolved` - Payment dispute resolution

### Consumed Events

- `order.confirmed` - From Order Management Service
- `trip.completed` - From Trip Management Service
- `user.registered` - From User Management Service

## 🚀 Getting Started

### Prerequisites

- .NET 8 SDK
- Docker Desktop
- TimescaleDB (or PostgreSQL with TimescaleDB extension)
- RazorPay account for payment processing

### Quick Start

1. **Clone and navigate to the service**

   ```bash
   cd Services/FinancialPayment
   ```

2. **Set up the database**

   ```bash
   # Using Docker
   docker-compose up -d timescaledb

   # Run database setup
   psql -h localhost -p 5434 -U timescale -d postgres -f database-setup.sql
   ```

3. **Configure RazorPay settings**

   ```json
   {
     "RazorPay": {
       "KeyId": "your_razorpay_key_id",
       "KeySecret": "your_razorpay_key_secret",
       "WebhookSecret": "your_razorpay_webhook_secret"
     }
   }
   ```

4. **Run the service**

   ```bash
   # Development
   dotnet run --project FinancialPayment.API

   # Docker
   docker-compose up --build
   ```

### API Documentation

- **Swagger UI**: http://localhost:5007/swagger
- **Health Checks**: http://localhost:5007/health

## 📋 API Endpoints

### Escrow Management

- `POST /api/escrow` - Create escrow account
- `POST /api/escrow/{id}/fund` - Fund escrow account
- `POST /api/escrow/{id}/release` - Release escrow funds
- `POST /api/escrow/{id}/refund` - Refund escrow funds
- `GET /api/escrow/{id}` - Get escrow account details
- `GET /api/escrow/order/{orderId}` - Get escrow by order

### Settlement Processing

- `POST /api/settlement` - Create settlement
- `POST /api/settlement/{id}/process` - Process settlement
- `GET /api/settlement/{id}` - Get settlement details
- `GET /api/settlement/carrier/{carrierId}` - Get carrier settlements
- `GET /api/settlement/broker/{brokerId}` - Get broker settlements

### Commission Management

- `POST /api/commission/calculate` - Calculate commission
- `POST /api/commission/{id}/approve` - Approve commission
- `POST /api/commission/{id}/pay` - Pay commission
- `POST /api/commission/{id}/dispute` - Dispute commission
- `GET /api/commission/{id}` - Get commission details
- `GET /api/commission/broker/{brokerId}` - Get broker commissions

### Dispute Resolution

- `POST /api/paymentdispute` - Create payment dispute
- `POST /api/paymentdispute/{id}/comments` - Add dispute comment
- `POST /api/paymentdispute/{id}/escalate` - Escalate dispute
- `POST /api/paymentdispute/{id}/resolve` - Resolve dispute
- `GET /api/paymentdispute/{id}` - Get dispute details
- `GET /api/paymentdispute/order/{orderId}` - Get order disputes

### Tax Configuration Management

- `GET /api/tax-configurations` - Get all tax configurations
- `GET /api/tax-configurations/{id}` - Get specific tax configuration
- `POST /api/tax-configurations` - Create new tax configuration
- `PUT /api/tax-configurations/{id}` - Update tax configuration
- `DELETE /api/tax-configurations/{id}` - Delete tax configuration
- `POST /api/tax-configurations/{id}/set-default` - Set as default configuration
- `POST /api/tax-configurations/calculate` - Calculate comprehensive tax

### GST Configuration

- `GET /api/gst-configurations` - Get all GST configurations
- `GET /api/gst-configurations/{id}` - Get specific GST configuration
- `POST /api/gst-configurations` - Create new GST configuration
- `PUT /api/gst-configurations/{id}` - Update GST configuration
- `GET /api/gst-configurations/service/{category}` - Get GST by service category

### TDS Configuration

- `GET /api/tds-configurations` - Get all TDS configurations
- `GET /api/tds-configurations/{id}` - Get specific TDS configuration
- `POST /api/tds-configurations` - Create new TDS configuration
- `PUT /api/tds-configurations/{id}` - Update TDS configuration
- `GET /api/tds-configurations/section/{section}` - Get TDS by section

### HSN Code Management

- `GET /api/hsn-codes` - Get all HSN codes
- `GET /api/hsn-codes/{id}` - Get specific HSN code
- `GET /api/hsn-codes/code/{code}` - Get HSN code by code
- `POST /api/hsn-codes` - Create new HSN code
- `PUT /api/hsn-codes/{id}` - Update HSN code
- `GET /api/hsn-codes/search` - Search HSN codes by category or description

## 🧪 Testing

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Integration tests
dotnet test FinancialPayment.IntegrationTests/
```

## 📊 Monitoring & Observability

- **Structured Logging**: Comprehensive logging with Serilog
- **Health Checks**: Database and external service health monitoring
- **Metrics**: Payment processing metrics and performance indicators
- **Alerts**: Real-time alerts for payment failures and disputes

## 🔐 Security

- **JWT Authentication**: Secure API access with role-based authorization
- **Payment Security**: PCI DSS compliant payment processing
- **Data Encryption**: Sensitive financial data encryption
- **Audit Trails**: Complete audit logs for all financial operations
- **Fraud Detection**: Advanced fraud prevention mechanisms

## 🚀 Deployment

### Docker Deployment

```bash
# Build and deploy
docker-compose up -d --build

# Scale services
docker-compose up -d --scale financial-payment-api=3
```

### Environment Configuration

- **Development**: Local development with test payment gateway
- **Staging**: Pre-production environment with sandbox payments
- **Production**: Production deployment with live payment processing

## 📈 Performance

- **High Throughput**: Optimized for high-volume payment processing
- **Low Latency**: Sub-second payment processing and verification
- **Scalability**: Horizontal scaling support with load balancing
- **Reliability**: 99.9% uptime with robust error handling

## 🤝 Contributing

1. Follow Clean Architecture principles
2. Implement comprehensive unit tests
3. Add integration tests for new features
4. Update API documentation
5. Follow security best practices for financial data

## 📄 License

This project is part of the TLI Logistics platform and follows the same licensing terms.
