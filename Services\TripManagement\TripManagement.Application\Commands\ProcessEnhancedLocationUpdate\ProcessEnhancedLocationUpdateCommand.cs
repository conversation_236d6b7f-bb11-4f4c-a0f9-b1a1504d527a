using MediatR;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ProcessEnhancedLocationUpdate;

public record ProcessEnhancedLocationUpdateCommand : IRequest<ProcessEnhancedLocationUpdateResponse>
{
    public Guid TripId { get; init; }
    public Guid DriverId { get; init; }
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
    public double? Accuracy { get; init; }
    public double? Speed { get; init; }
    public double? Heading { get; init; }
    public LocationUpdateSource Source { get; init; } = LocationUpdateSource.GPS;
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
    public bool TriggerGeofenceCheck { get; init; } = true;
    public bool UpdateETA { get; init; } = true;
    public bool CheckRouteDeviation { get; init; } = true;
    public bool MonitorSpeed { get; init; } = true;
    public Dictionary<string, object> AdditionalData { get; init; } = new();
}

public record ProcessEnhancedLocationUpdateResponse
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public Guid LocationUpdateId { get; init; }
    public GeofenceCheckResult? GeofenceResult { get; init; }
    public ETAUpdateResult? ETAResult { get; init; }
    public RouteDeviationResult? RouteDeviationResult { get; init; }
    public SpeedMonitoringResult? SpeedResult { get; init; }
    public List<string> Alerts { get; init; } = new();
    public List<string> Notifications { get; init; } = new();
    public DateTime ProcessedAt { get; init; }
}

public record GeofenceCheckResult
{
    public bool IsInsideAnyZone { get; init; }
    public bool IsAtCriticalLocation { get; init; }
    public List<GeofenceStatusDto> GeofenceStatuses { get; init; } = new();
    public List<GeofenceEventDto> TriggeredEvents { get; init; } = new();
}

public record GeofenceStatusDto
{
    public Guid GeofenceId { get; init; }
    public string Name { get; init; } = string.Empty;
    public string ZoneType { get; init; } = string.Empty;
    public string Status { get; init; } = string.Empty; // "Inside", "Outside", "Entering", "Exiting"
    public double DistanceFromCenter { get; init; }
    public DateTime CheckedAt { get; init; }
}

public record GeofenceEventDto
{
    public Guid EventId { get; init; }
    public Guid GeofenceId { get; init; }
    public string EventType { get; init; } = string.Empty; // "Enter", "Exit", "Dwell"
    public string ZoneName { get; init; } = string.Empty;
    public DateTime TriggeredAt { get; init; }
    public Dictionary<string, object> EventData { get; init; } = new();
}

public record ETAUpdateResult
{
    public DateTime? PreviousETA { get; init; }
    public DateTime? UpdatedETA { get; init; }
    public TimeSpan? ETAChange { get; init; }
    public string ChangeReason { get; init; } = string.Empty;
    public List<StopETADto> StopETAs { get; init; } = new();
    public double ConfidenceScore { get; init; }
}

public record StopETADto
{
    public Guid StopId { get; init; }
    public string StopType { get; init; } = string.Empty;
    public string Address { get; init; } = string.Empty;
    public DateTime? PreviousETA { get; init; }
    public DateTime? UpdatedETA { get; init; }
    public double DistanceKm { get; init; }
    public TimeSpan EstimatedTravelTime { get; init; }
}

public record RouteDeviationResult
{
    public bool IsDeviated { get; init; }
    public double DeviationDistanceMeters { get; init; }
    public double DeviationThresholdMeters { get; init; }
    public string DeviationSeverity { get; init; } = string.Empty; // "Minor", "Moderate", "Major"
    public string? SuggestedAction { get; init; }
    public LocationDto? NearestRoutePoint { get; init; }
    public double DistanceToNearestRoutePoint { get; init; }
}

public record SpeedMonitoringResult
{
    public double CurrentSpeedKmh { get; init; }
    public double SpeedLimitKmh { get; init; }
    public bool IsOverSpeedLimit { get; init; }
    public double SpeedExcessKmh { get; init; }
    public string SpeedCategory { get; init; } = string.Empty; // "Normal", "Warning", "Violation"
    public TimeSpan? OverSpeedDuration { get; init; }
}

public record LocationDto
{
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
}
