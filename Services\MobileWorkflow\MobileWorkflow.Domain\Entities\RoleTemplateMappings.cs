
using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;


namespace MobileWorkflow.Domain.Entities;

public class RoleTemplateMappings : AggregateRoot
{
    public string RoleName { get; private set; }
    public Guid MilestoneTemplateId { get; private set; }
    public bool IsDefault { get; private set; }
    public bool IsActive { get; private set; }
    public int Priority { get; private set; } // Higher number = higher priority
    public string? Conditions { get; private set; } // JSON conditions for when this mapping applies
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual MilestoneTemplate MilestoneTemplate { get; private set; } = null!;

    private RoleTemplateMappings() { } // EF Core

    public RoleTemplateMappings(
        string roleName,
        Guid milestoneTemplateId,
        string createdBy,
        bool isDefault = false,
        int priority = 100)
    {
        RoleName = roleName ?? throw new ArgumentNullException(nameof(roleName));
        MilestoneTemplateId = milestoneTemplateId;
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));
        IsDefault = isDefault;
        Priority = priority;

        IsActive = true;
        Configuration = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new RoleTemplateMappingsCreatedEvent(Id, RoleName, MilestoneTemplateId, IsDefault, CreatedBy));
    }

    public void SetAsDefault()
    {
        IsDefault = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsSetAsDefaultEvent(Id, RoleName, MilestoneTemplateId));
    }

    public void RemoveAsDefault()
    {
        IsDefault = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsRemovedAsDefaultEvent(Id, RoleName, MilestoneTemplateId));
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsActivatedEvent(Id, RoleName, MilestoneTemplateId));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsDeactivatedEvent(Id, RoleName, MilestoneTemplateId));
    }

    public void UpdatePriority(int priority, string updatedBy)
    {
        var oldPriority = Priority;
        Priority = priority;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsPriorityUpdatedEvent(Id, RoleName, MilestoneTemplateId, oldPriority, priority, UpdatedBy));
    }

    public void SetConditions(string? conditions, string updatedBy)
    {
        Conditions = conditions;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsConditionsUpdatedEvent(Id, RoleName, MilestoneTemplateId, Conditions, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new RoleTemplateMappingsConfigurationUpdatedEvent(Id, RoleName, MilestoneTemplateId, UpdatedBy));
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool EvaluateConditions(Dictionary<string, object> context)
    {
        if (string.IsNullOrEmpty(Conditions))
        {
            return true; // No conditions means always applicable
        }

        try
        {
            // Simple condition evaluation - can be enhanced with a proper expression evaluator
            // For now, support simple JSON-like conditions
            // Example: {"department": "logistics", "experience_level": "senior"}

            if (Conditions.StartsWith("{") && Conditions.EndsWith("}"))
            {
                // Parse as JSON and evaluate each condition
                var conditionsDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(Conditions);
                if (conditionsDict != null)
                {
                    foreach (var condition in conditionsDict)
                    {
                        if (!context.TryGetValue(condition.Key, out var actualValue))
                        {
                            return false; // Required context key is missing
                        }

                        var expectedValue = condition.Value?.ToString();
                        var actualValueStr = actualValue?.ToString();

                        if (!string.Equals(expectedValue, actualValueStr, StringComparison.OrdinalIgnoreCase))
                        {
                            return false; // Condition not met
                        }
                    }
                    return true; // All conditions met
                }
            }

            return false;
        }
        catch
        {
            return false; // If condition evaluation fails, don't apply mapping
        }
    }

    public List<string> GetValidationErrors()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(RoleName))
        {
            errors.Add("Role name is required");
        }

        if (MilestoneTemplateId == Guid.Empty)
        {
            errors.Add("Milestone template ID is required");
        }

        if (Priority < 0)
        {
            errors.Add("Priority must be non-negative");
        }

        if (!string.IsNullOrEmpty(Conditions))
        {
            try
            {
                if (Conditions.StartsWith("{") && Conditions.EndsWith("}"))
                {
                    System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(Conditions);
                }
                else
                {
                    errors.Add("Conditions must be valid JSON format");
                }
            }
            catch
            {
                errors.Add("Conditions must be valid JSON format");
            }
        }

        return errors;
    }

    public bool CanBeDeleted()
    {
        return !IsDefault; // Default mappings should not be deleted without setting another as default
    }
}



