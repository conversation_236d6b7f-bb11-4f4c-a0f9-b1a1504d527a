using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.UpdateMilestonePayoutStructure;

public class UpdateMilestonePayoutStructureCommandHandler : IRequestHandler<UpdateMilestonePayoutStructureCommand, bool>
{
    private readonly IRfqMilestoneAssignmentRepository _assignmentRepository;
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IRfqTimelineRepository _timelineRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateMilestonePayoutStructureCommandHandler> _logger;

    public UpdateMilestonePayoutStructureCommandHandler(
        IRfqMilestoneAssignmentRepository assignmentRepository,
        IMilestoneTemplateRepository templateRepository,
        IRfqTimelineRepository timelineRepository,
        IUnitOfWork unitOfWork,
        ILogger<UpdateMilestonePayoutStructureCommandHandler> logger)
    {
        _assignmentRepository = assignmentRepository;
        _templateRepository = templateRepository;
        _timelineRepository = timelineRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<bool> Handle(UpdateMilestonePayoutStructureCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating milestone payout structure for assignment {AssignmentId} by user {UpdatedBy}",
            request.RfqMilestoneAssignmentId, request.UpdatedBy);

        try
        {
            // Get the milestone assignment
            var assignment = await _assignmentRepository.GetByIdAsync(request.RfqMilestoneAssignmentId, cancellationToken);
            if (assignment == null)
            {
                _logger.LogWarning("Milestone assignment {AssignmentId} not found", request.RfqMilestoneAssignmentId);
                return false;
            }

            // Validate assignment is active
            if (!assignment.IsActive)
            {
                _logger.LogWarning("Cannot update inactive milestone assignment {AssignmentId}", request.RfqMilestoneAssignmentId);
                return false;
            }

            // Get the milestone template to validate steps
            var template = await _templateRepository.GetByIdAsync(assignment.MilestoneTemplateId, cancellationToken);
            if (template == null)
            {
                _logger.LogWarning("Milestone template {TemplateId} not found for assignment {AssignmentId}", 
                    assignment.MilestoneTemplateId, request.RfqMilestoneAssignmentId);
                return false;
            }

            // Validate step updates
            var validationResult = ValidateStepUpdates(template, request.StepPayoutUpdates);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Payout structure validation failed for assignment {AssignmentId}: {Errors}",
                    request.RfqMilestoneAssignmentId, string.Join(", ", validationResult.Errors));
                return false;
            }

            // Calculate total payout percentage
            var totalPayout = request.StepPayoutUpdates.Sum(u => u.NewPayoutPercentage);
            if (Math.Abs(totalPayout - 100m) > 0.01m)
            {
                _logger.LogWarning("Total payout percentage {TotalPayout}% does not equal 100% for assignment {AssignmentId}",
                    totalPayout, request.RfqMilestoneAssignmentId);
                return false;
            }

            // Update the custom payout structure
            assignment.UpdatePayoutStructure(request.CustomPayoutStructure);

            // Update individual step payouts in the template (if this is a custom template)
            foreach (var stepUpdate in request.StepPayoutUpdates)
            {
                var step = template.Steps.FirstOrDefault(s => s.Id == stepUpdate.StepId);
                if (step != null)
                {
                    step.UpdatePayoutPercentage(stepUpdate.NewPayoutPercentage);
                }
            }

            // Create timeline event if requested
            if (request.CreateTimelineEvent)
            {
                var stepUpdatesDescription = string.Join(", ", 
                    request.StepPayoutUpdates.Select(u => 
                    {
                        var stepName = template.Steps.FirstOrDefault(s => s.Id == u.StepId)?.Name ?? "Unknown Step";
                        return $"{stepName}: {u.NewPayoutPercentage}%";
                    }));

                var timelineEvent = RfqTimeline.CreateEvent(
                    assignment.RfqId,
                    RfqTimelineEventType.Other,
                    $"Milestone payout structure updated by '{request.UpdatedByName}'. New structure: {stepUpdatesDescription}",
                    request.UpdatedBy,
                    request.UpdatedByName,
                    "Transport Company",
                    System.Text.Json.JsonSerializer.Serialize(new
                    {
                        assignmentId = request.RfqMilestoneAssignmentId,
                        templateId = assignment.MilestoneTemplateId,
                        customPayoutStructure = request.CustomPayoutStructure,
                        stepUpdates = request.StepPayoutUpdates.Select(u => new
                        {
                            stepId = u.StepId,
                            newPayoutPercentage = u.NewPayoutPercentage,
                            reason = u.Reason
                        }),
                        updateReason = request.UpdateReason,
                        totalPayout = totalPayout
                    }));

                await _timelineRepository.AddAsync(timelineEvent, cancellationToken);
            }

            // Save changes
            _assignmentRepository.Update(assignment);
            _templateRepository.Update(template);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully updated milestone payout structure for assignment {AssignmentId}", 
                request.RfqMilestoneAssignmentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone payout structure for assignment {AssignmentId}", 
                request.RfqMilestoneAssignmentId);
            throw;
        }
    }

    private static ValidationResult ValidateStepUpdates(MilestoneTemplate template, List<MilestoneStepPayoutUpdate> stepUpdates)
    {
        var errors = new List<string>();

        // Check if all template steps are included in updates
        var templateStepIds = template.Steps.Select(s => s.Id).ToHashSet();
        var updateStepIds = stepUpdates.Select(u => u.StepId).ToHashSet();

        if (!templateStepIds.SetEquals(updateStepIds))
        {
            var missingSteps = templateStepIds.Except(updateStepIds);
            var extraSteps = updateStepIds.Except(templateStepIds);

            if (missingSteps.Any())
                errors.Add($"Missing payout updates for steps: {string.Join(", ", missingSteps)}");

            if (extraSteps.Any())
                errors.Add($"Invalid step IDs in updates: {string.Join(", ", extraSteps)}");
        }

        // Validate individual payout percentages
        foreach (var stepUpdate in stepUpdates)
        {
            if (stepUpdate.NewPayoutPercentage < 0 || stepUpdate.NewPayoutPercentage > 100)
            {
                errors.Add($"Invalid payout percentage {stepUpdate.NewPayoutPercentage}% for step {stepUpdate.StepId}");
            }
        }

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
