using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.ExternalServices;

/// <summary>
/// SendGrid email provider implementation
/// </summary>
public class SendGridEmailProvider : IEmailProvider
{
    private readonly ILogger<SendGridEmailProvider> _logger;
    private readonly SendGridSettings _settings;
    private readonly ISendGridClient _sendGridClient;

    public SendGridEmailProvider(
        IConfiguration configuration,
        ILogger<SendGridEmailProvider> logger,
        ISendGridClient sendGridClient)
    {
        _logger = logger;
        _sendGridClient = sendGridClient;
        _settings = configuration.GetSection("SendGrid").Get<SendGridSettings>() ?? new();
    }

    public async Task<EmailResult> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending email to {EmailAddress} via SendGrid", request.ToEmail);

            // Validate request
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                return EmailResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create SendGrid message
            var message = CreateSendGridMessage(request);

            // Send email
            var response = await _sendGridClient.SendEmailAsync(message, cancellationToken);

            var result = new EmailResult
            {
                IsSuccess = response.IsSuccessStatusCode,
                MessageId = ExtractMessageId(response),
                ExternalId = ExtractMessageId(response),
                Status = response.IsSuccessStatusCode ? EmailStatus.Sent : EmailStatus.Failed,
                SentAt = DateTime.UtcNow,
                ErrorMessage = response.IsSuccessStatusCode ? null : await GetErrorMessageAsync(response),
                Metadata = new Dictionary<string, object>
                {
                    ["sendGridStatusCode"] = ((int)response.StatusCode).ToString(),
                    ["sendGridHeaders"] = response.Headers?.ToDictionary(h => h.Key, h => string.Join(",", h.Value)) ?? new(),
                    ["templateId"] = request.TemplateId ?? "",
                    ["hasAttachments"] = request.Attachments?.Any().ToString() ?? "false"
                }
            };

            if (result.IsSuccess)
            {
                _logger.LogInformation("Email sent successfully to {EmailAddress}. MessageId: {MessageId}",
                    request.ToEmail, result.MessageId);
            }
            else
            {
                _logger.LogError("Failed to send email to {EmailAddress}. Status: {StatusCode}, Error: {Error}",
                    request.ToEmail, response.StatusCode, result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending email to {EmailAddress}", request.ToEmail);
            return EmailResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<EmailResult> SendTemplateEmailAsync(TemplateEmailRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending template email {TemplateId} to {EmailAddress} via SendGrid",
                request.TemplateId, request.ToEmail);

            // Validate request
            var validationResult = ValidateTemplateRequest(request);
            if (!validationResult.IsValid)
            {
                return EmailResult.Failure(string.Join(", ", validationResult.Errors));
            }

            // Create SendGrid message with template
            var message = CreateTemplateMessage(request);

            // Send email
            var response = await _sendGridClient.SendEmailAsync(message, cancellationToken);

            var result = new EmailResult
            {
                IsSuccess = response.IsSuccessStatusCode,
                MessageId = ExtractMessageId(response),
                ExternalId = ExtractMessageId(response),
                Status = response.IsSuccessStatusCode ? EmailStatus.Sent : EmailStatus.Failed,
                SentAt = DateTime.UtcNow,
                ErrorMessage = response.IsSuccessStatusCode ? null : await GetErrorMessageAsync(response),
                Metadata = new Dictionary<string, object>
                {
                    ["sendGridStatusCode"] = ((int)response.StatusCode).ToString(),
                    ["templateId"] = request.TemplateId,
                    ["templateData"] = JsonSerializer.Serialize(request.TemplateData),
                    ["sendGridHeaders"] = response.Headers?.ToDictionary(h => h.Key, h => string.Join(",", h.Value)) ?? new()
                }
            };

            if (result.IsSuccess)
            {
                _logger.LogInformation("Template email sent successfully to {EmailAddress}. Template: {TemplateId}, MessageId: {MessageId}",
                    request.ToEmail, request.TemplateId, result.MessageId);
            }
            else
            {
                _logger.LogError("Failed to send template email to {EmailAddress}. Template: {TemplateId}, Status: {StatusCode}, Error: {Error}",
                    request.ToEmail, request.TemplateId, response.StatusCode, result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending template email to {EmailAddress}", request.ToEmail);
            return EmailResult.Failure($"Unexpected error: {ex.Message}");
        }
    }

    public async Task<EmailResult> GetDeliveryStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting delivery status for SendGrid message {MessageId}", messageId);

            // SendGrid doesn't provide a direct API to get message status by ID
            // This would typically be handled via webhooks
            // For now, return a placeholder result
            return new EmailResult
            {
                IsSuccess = true,
                MessageId = messageId,
                ExternalId = messageId,
                Status = EmailStatus.Unknown,
                Metadata = new Dictionary<string, object>
                {
                    ["note"] = "SendGrid delivery status is typically provided via webhooks"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery status for message {MessageId}", messageId);
            return EmailResult.Failure($"Error getting delivery status: {ex.Message}");
        }
    }

    public async Task<bool> ValidateEmailAddressAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        try
        {
            // Basic email validation
            if (string.IsNullOrWhiteSpace(emailAddress))
                return false;

            var emailRegex = new System.Text.RegularExpressions.Regex(
                @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");

            return emailRegex.IsMatch(emailAddress);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Email validation failed for {EmailAddress}", emailAddress);
            return false;
        }
    }

    private SendGridMessage CreateSendGridMessage(EmailRequest request)
    {
        var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
        var to = new EmailAddress(request.ToEmail, request.ToName);

        var message = MailHelper.CreateSingleEmail(from, to, request.Subject, request.PlainTextContent, request.HtmlContent);

        // Add CC recipients
        if (request.CcEmails?.Any() == true)
        {
            var ccList = request.CcEmails.Select(email => new EmailAddress(email)).ToList();
            message.AddCcs(ccList);
        }

        // Add BCC recipients
        if (request.BccEmails?.Any() == true)
        {
            var bccList = request.BccEmails.Select(email => new EmailAddress(email)).ToList();
            message.AddBccs(bccList);
        }

        // Add attachments
        if (request.Attachments?.Any() == true)
        {
            foreach (var attachment in request.Attachments)
            {
                message.AddAttachment(attachment.FileName, attachment.Content, attachment.ContentType);
            }
        }

        // Add custom headers
        if (request.CustomHeaders?.Any() == true)
        {
            foreach (var header in request.CustomHeaders)
            {
                message.AddHeader(header.Key, header.Value);
            }
        }

        // Add tracking settings
        message.SetClickTracking(_settings.EnableClickTracking, _settings.EnableClickTracking);
        message.SetOpenTracking(_settings.EnableOpenTracking);

        return message;
    }

    private SendGridMessage CreateTemplateMessage(TemplateEmailRequest request)
    {
        var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
        var to = new EmailAddress(request.ToEmail, request.ToName);

        var message = MailHelper.CreateSingleTemplateEmail(from, to, request.TemplateId, request.TemplateData);

        // Add CC recipients
        if (request.CcEmails?.Any() == true)
        {
            var ccList = request.CcEmails.Select(email => new EmailAddress(email)).ToList();
            message.AddCcs(ccList);
        }

        // Add BCC recipients
        if (request.BccEmails?.Any() == true)
        {
            var bccList = request.BccEmails.Select(email => new EmailAddress(email)).ToList();
            message.AddBccs(bccList);
        }

        // Add custom headers
        if (request.CustomHeaders?.Any() == true)
        {
            foreach (var header in request.CustomHeaders)
            {
                message.AddHeader(header.Key, header.Value);
            }
        }

        // Add tracking settings
        message.SetClickTracking(_settings.EnableClickTracking, _settings.EnableClickTracking);
        message.SetOpenTracking(_settings.EnableOpenTracking);

        return message;
    }

    private string? ExtractMessageId(Response response)
    {
        try
        {
            // Try to extract message ID from response headers
            if (response.Headers?.TryGetValue("X-Message-Id", out var messageIdValues) == true)
            {
                return messageIdValues.FirstOrDefault();
            }

            // Generate a unique ID if not available
            return Guid.NewGuid().ToString();
        }
        catch
        {
            return Guid.NewGuid().ToString();
        }
    }

    private async Task<string?> GetErrorMessageAsync(Response response)
    {
        try
        {
            var body = await response.Body.ReadAsStringAsync();
            if (!string.IsNullOrEmpty(body))
            {
                var errorResponse = JsonSerializer.Deserialize<SendGridErrorResponse>(body);
                return errorResponse?.Errors?.FirstOrDefault()?.Message ?? body;
            }

            return $"HTTP {response.StatusCode}";
        }
        catch
        {
            return $"HTTP {response.StatusCode}";
        }
    }

    private EmailValidationResult ValidateRequest(EmailRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.ToEmail))
            errors.Add("To email address is required");

        if (!IsValidEmailFormat(request.ToEmail))
            errors.Add("Invalid to email address format");

        if (string.IsNullOrWhiteSpace(request.Subject))
            errors.Add("Email subject is required");

        if (string.IsNullOrWhiteSpace(request.PlainTextContent) && string.IsNullOrWhiteSpace(request.HtmlContent))
            errors.Add("Either plain text or HTML content is required");

        if (request.Subject?.Length > _settings.MaxSubjectLength)
            errors.Add($"Subject exceeds maximum length of {_settings.MaxSubjectLength} characters");

        return new EmailValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private EmailValidationResult ValidateTemplateRequest(TemplateEmailRequest request)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(request.ToEmail))
            errors.Add("To email address is required");

        if (!IsValidEmailFormat(request.ToEmail))
            errors.Add("Invalid to email address format");

        if (string.IsNullOrWhiteSpace(request.TemplateId))
            errors.Add("Template ID is required");

        return new EmailValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private bool IsValidEmailFormat(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var emailRegex = new System.Text.RegularExpressions.Regex(
                @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// SendGrid configuration settings
/// </summary>
public class SendGridSettings
{
    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public bool EnableClickTracking { get; set; } = true;
    public bool EnableOpenTracking { get; set; } = true;
    public int MaxSubjectLength { get; set; } = 998;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public string WebhookUrl { get; set; } = string.Empty;
}

/// <summary>
/// Email validation result
/// </summary>
public class EmailValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// SendGrid error response model
/// </summary>
public class SendGridErrorResponse
{
    public List<SendGridError>? Errors { get; set; }
}

/// <summary>
/// SendGrid error model
/// </summary>
public class SendGridError
{
    public string? Message { get; set; }
    public string? Field { get; set; }
    public string? Help { get; set; }
}
