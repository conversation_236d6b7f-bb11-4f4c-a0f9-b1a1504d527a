using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerAnalyticsQueryHandler : IRequestHandler<GetPartnerAnalyticsQuery, PartnerAnalyticsDto?>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPartnerAnalyticsQueryHandler> _logger;

    public GetPartnerAnalyticsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPartnerAnalyticsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<PartnerAnalyticsDto?> Handle(GetPartnerAnalyticsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partner = await _preferredPartnerRepository.GetByIdAsync(request.PreferredPartnerId, cancellationToken);
            
            if (partner == null || partner.UserId != request.UserId)
            {
                return null;
            }

            // Get analytics data from repository
            var analytics = await _preferredPartnerRepository.GetPartnerAnalyticsAsync(
                request.PreferredPartnerId, 
                request.FromDate, 
                request.ToDate, 
                cancellationToken);

            if (analytics == null)
            {
                // Create basic analytics if none exist
                analytics = new PartnerAnalyticsDto
                {
                    PreferredPartnerId = request.PreferredPartnerId,
                    PartnerId = partner.PartnerId,
                    PartnerName = "Partner", // This would be populated from partner service
                    PartnerType = partner.PartnerType.ToString(),
                    AnalysisPeriodStart = request.FromDate,
                    AnalysisPeriodEnd = request.ToDate,
                    PerformanceMetrics = _mapper.Map<PartnerPerformanceMetricsDto>(partner.PerformanceMetrics),
                    TotalCollaborations = 0,
                    CompletedCollaborations = 0,
                    CompletionRate = 0,
                    TotalValue = 0,
                    AverageOrderValue = 0,
                    MonthlyBreakdown = new List<MonthlyCollaborationDto>(),
                    RoutePerformance = new List<RoutePerformanceDto>(),
                    LoadTypePerformance = new List<LoadTypePerformanceDto>(),
                    PerformanceTrend = 0,
                    TrendDescription = "No data available for the selected period",
                    Recommendations = new List<string>()
                };
            }

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving partner analytics for preferred partner {PreferredPartnerId}", 
                request.PreferredPartnerId);
            throw;
        }
    }
}
