using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Infrastructure.Persistence;
using AnalyticsBIService.Application.DTOs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Diagnostics;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Implementation of custom report builder service
/// </summary>
public class CustomReportBuilderService : ICustomReportBuilderService
{
    private readonly AnalyticsBIDbContext _context;
    private readonly ICacheService _cacheService;
    private readonly ILogger<CustomReportBuilderService> _logger;

    public CustomReportBuilderService(
        AnalyticsBIDbContext context,
        ICacheService cacheService,
        ILogger<CustomReportBuilderService> logger)
    {
        _context = context;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<CustomReportTemplateDto> CreateReportTemplateAsync(CreateReportTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating custom report template '{ReportName}' for user {UserId}", request.Name, request.UserId);

            var template = new AnalyticsBIService.Domain.Entities.CustomReportTemplate(
                name: request.Name,
                description: request.Description,
                createdBy: request.UserId,
                userType: request.UserType,
                configuration: JsonSerializer.Serialize(request.Configuration),
                isPublic: request.IsPublic
            );

            _context.CustomReportTemplates.Add(template);
            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(template);

            // Cache the template
            var cacheKey = CacheKeys.CustomReport(template.Id);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Custom report template '{ReportName}' created with ID {TemplateId}", request.Name, template.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom report template");
            throw;
        }
    }

    public async Task<CustomReportTemplateDto> UpdateReportTemplateAsync(Guid templateId, UpdateReportTemplateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating custom report template {TemplateId}", templateId);

            var template = await _context.CustomReportTemplates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

            if (template == null)
                throw new InvalidOperationException($"Report template {templateId} not found");

            if (!string.IsNullOrEmpty(request.Name))
                template.UpdateName(request.Name);

            if (!string.IsNullOrEmpty(request.Description))
                template.UpdateDescription(request.Description);

            if (request.Configuration != null)
                template.UpdateConfiguration(JsonSerializer.Serialize(request.Configuration));

            if (request.IsPublic.HasValue)
            {
                if (request.IsPublic.Value)
                    template.MakePublic();
                else
                    template.MakePrivate();
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    template.Activate();
                else
                    template.Deactivate();
            }

            await _context.SaveChangesAsync(cancellationToken);

            var result = MapToDto(template);

            // Update cache
            var cacheKey = CacheKeys.CustomReport(templateId);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            _logger.LogInformation("Custom report template {TemplateId} updated successfully", templateId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating custom report template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<bool> DeleteReportTemplateAsync(Guid templateId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting custom report template {TemplateId} for user {UserId}", templateId, userId);

            var template = await _context.CustomReportTemplates
                .FirstOrDefaultAsync(t => t.Id == templateId && t.CreatedBy == userId, cancellationToken);

            if (template == null)
                return false;

            _context.CustomReportTemplates.Remove(template);
            await _context.SaveChangesAsync(cancellationToken);

            // Remove from cache
            var cacheKey = CacheKeys.CustomReport(templateId);
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            _logger.LogInformation("Custom report template {TemplateId} deleted successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting custom report template {TemplateId}", templateId);
            return false;
        }
    }

    public async Task<CustomReportTemplateDto?> GetReportTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = CacheKeys.CustomReport(templateId);
            var cachedResult = await _cacheService.GetAsync<CustomReportTemplateDto>(cacheKey, cancellationToken);
            if (cachedResult != null)
                return cachedResult;

            var template = await _context.CustomReportTemplates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

            if (template == null)
                return null;

            var result = MapToDto(template);
            await _cacheService.SetAsync(cacheKey, result, CacheExpiration.LongTerm, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting custom report template {TemplateId}", templateId);
            return null;
        }
    }

    public async Task<List<CustomReportTemplateDto>> GetUserReportTemplatesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = await _context.CustomReportTemplates
                .Where(t => t.CreatedBy == userId || t.IsPublic)
                .Where(t => t.IsActive)
                .OrderByDescending(t => t.UpdatedAt)
                .ToListAsync(cancellationToken);

            return templates.Select(MapToDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user report templates for user {UserId}", userId);
            return new List<CustomReportTemplateDto>();
        }
    }

    public async Task<CustomReportResultDto> GenerateReportAsync(Guid templateId, GenerateReportRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Generating report from template {TemplateId} for user {UserId}", templateId, request.UserId);

            var template = await GetReportTemplateAsync(templateId, cancellationToken);
            if (template == null)
                throw new InvalidOperationException($"Report template {templateId} not found");

            // Record execution start
            var execution = AnalyticsBIService.Domain.Entities.ReportExecution.Create(
                reportId: Guid.NewGuid(), // Using new GUID as reportId
                templateId: templateId,
                userId: request.UserId,
                parameters: JsonSerializer.Serialize(request.ParameterValues),
                outputFormat: AnalyticsBIService.Domain.Enums.ExportFormat.JSON, // Default format
                isManualExecution: true,
                reportName: null,
                scheduleId: null,
                triggerType: "Manual"
            );

            _context.ReportExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            try
            {
                // Generate report data
                var reportData = await GenerateReportDataAsync(template.Configuration, request, cancellationToken);

                stopwatch.Stop();

                // Update execution record
                execution.MarkCompleted(
                    outputPath: $"/reports/{execution.Id}.json",
                    fileSizeBytes: 1024,
                    recordCount: reportData.TotalRecords,
                    reportData: null,
                    deliverySuccessful: true
                );
                await _context.SaveChangesAsync(cancellationToken);

                // Update template execution count
                await UpdateTemplateExecutionCountAsync(templateId, cancellationToken);

                _logger.LogInformation("Report generated successfully from template {TemplateId} in {ExecutionTime}ms",
                    templateId, stopwatch.ElapsedMilliseconds);

                return reportData;
            }
            catch (Exception ex)
            {
                // Update execution record with error
                execution.MarkFailed(ex.Message);
                await _context.SaveChangesAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating report from template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<List<DataSourceDto>> GetAvailableDataSourcesAsync(Guid userId, UserType userType, CancellationToken cancellationToken = default)
    {
        try
        {
            var dataSources = new List<DataSourceDto>();

            // Add standard data sources based on user type
            dataSources.AddRange(GetStandardDataSources(userType));

            // Add user-specific data sources if applicable
            if (userType == UserType.Admin)
            {
                dataSources.AddRange(GetAdminDataSources());
            }

            return dataSources;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available data sources for user {UserId}", userId);
            return new List<DataSourceDto>();
        }
    }

    public async Task<List<DataFieldDto>> GetDataSourceFieldsAsync(string dataSourceName, CancellationToken cancellationToken = default)
    {
        try
        {
            return dataSourceName.ToLower() switch
            {
                "analytics_events" => GetAnalyticsEventFields(),
                "metrics" => GetMetricFields(),
                "users" => GetUserFields(),
                "transactions" => GetTransactionFields(),
                "performance" => GetPerformanceFields(),
                _ => new List<DataFieldDto>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting data source fields for {DataSource}", dataSourceName);
            return new List<DataFieldDto>();
        }
    }

    public async Task<ReportPreviewDto> PreviewReportDataAsync(ReportPreviewRequest request, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Previewing report data for user {UserId}", request.UserId);

            // Generate preview data based on configuration
            var previewData = await GeneratePreviewDataAsync(request, cancellationToken);

            stopwatch.Stop();

            return new ReportPreviewDto
            {
                Data = previewData.Take(request.MaxRows).ToList(),
                Fields = request.Fields,
                TotalRows = previewData.Count,
                HasMoreData = previewData.Count > request.MaxRows,
                ExecutionTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing report data");
            throw;
        }
    }

    public async Task<List<VisualizationTypeDto>> GetVisualizationTypesAsync(CancellationToken cancellationToken = default)
    {
        return new List<VisualizationTypeDto>
        {
            new()
            {
                Type = "bar",
                DisplayName = "Bar Chart",
                Description = "Display data as vertical bars",
                Category = "Charts",
                RequiredFields = new List<string> { "x-axis", "y-axis" },
                OptionalFields = new List<string> { "series" },
                DefaultOptions = new Dictionary<string, object>
                {
                    { "responsive", true },
                    { "maintainAspectRatio", false }
                }
            },
            new()
            {
                Type = "line",
                DisplayName = "Line Chart",
                Description = "Display data as connected points",
                Category = "Charts",
                RequiredFields = new List<string> { "x-axis", "y-axis" },
                OptionalFields = new List<string> { "series" },
                DefaultOptions = new Dictionary<string, object>
                {
                    { "responsive", true },
                    { "tension", 0.4 }
                }
            },
            new()
            {
                Type = "pie",
                DisplayName = "Pie Chart",
                Description = "Display data as pie slices",
                Category = "Charts",
                RequiredFields = new List<string> { "labels", "values" },
                OptionalFields = new List<string>(),
                DefaultOptions = new Dictionary<string, object>
                {
                    { "responsive", true },
                    { "plugins", new { legend = new { position = "right" } } }
                }
            },
            new()
            {
                Type = "table",
                DisplayName = "Data Table",
                Description = "Display data in tabular format",
                Category = "Tables",
                RequiredFields = new List<string> { "columns" },
                OptionalFields = new List<string> { "sorting", "filtering" },
                DefaultOptions = new Dictionary<string, object>
                {
                    { "pagination", true },
                    { "pageSize", 25 }
                }
            },
            new()
            {
                Type = "kpi",
                DisplayName = "KPI Card",
                Description = "Display key performance indicators",
                Category = "Metrics",
                RequiredFields = new List<string> { "value" },
                OptionalFields = new List<string> { "target", "trend" },
                DefaultOptions = new Dictionary<string, object>
                {
                    { "showTrend", true },
                    { "showTarget", true }
                }
            }
        };
    }

    public async Task<ReportValidationResult> ValidateReportConfigurationAsync(ReportConfigurationDto configuration, CancellationToken cancellationToken = default)
    {
        var result = new ReportValidationResult { IsValid = true };

        try
        {
            // Validate data sources
            if (!configuration.DataSources.Any())
            {
                result.Errors.Add("At least one data source must be specified");
                result.IsValid = false;
            }

            // Validate fields
            if (!configuration.Fields.Any())
            {
                result.Errors.Add("At least one field must be specified");
                result.IsValid = false;
            }

            // Validate visualizations
            foreach (var viz in configuration.Visualizations)
            {
                if (string.IsNullOrEmpty(viz.Type))
                {
                    result.Errors.Add("Visualization type cannot be empty");
                    result.IsValid = false;
                }

                if (!viz.XAxisFields.Any() && viz.Type != "kpi")
                {
                    result.Warnings.Add($"Visualization '{viz.Title}' has no X-axis fields defined");
                }
            }

            // Add suggestions
            if (configuration.Filters.Count == 0)
            {
                result.Suggestions.Add("Consider adding filters to make the report more interactive");
            }

            if (configuration.Visualizations.Count == 0)
            {
                result.Suggestions.Add("Consider adding visualizations to make the report more engaging");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating report configuration");
            result.IsValid = false;
            result.Errors.Add("Validation failed due to an internal error");
            return result;
        }
    }

    public async Task<CustomReportTemplateDto> CloneReportTemplateAsync(Guid templateId, Guid userId, string newName, CancellationToken cancellationToken = default)
    {
        try
        {
            var originalTemplate = await GetReportTemplateAsync(templateId, cancellationToken);
            if (originalTemplate == null)
                throw new InvalidOperationException($"Report template {templateId} not found");

            var cloneRequest = new CreateReportTemplateRequest
            {
                Name = newName,
                Description = $"Clone of {originalTemplate.Name}",
                UserId = userId,
                UserType = originalTemplate.UserType,
                Configuration = originalTemplate.Configuration,
                IsPublic = false
            };

            return await CreateReportTemplateAsync(cloneRequest, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cloning report template {TemplateId}", templateId);
            throw;
        }
    }

    public async Task<List<ReportExecutionDto>> GetReportExecutionHistoryAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        try
        {
            var executions = await _context.ReportExecutions
                .Where(e => e.ReportTemplateId == templateId)
                .OrderByDescending(e => e.ExecutedAt)
                .Take(50)
                .ToListAsync(cancellationToken);

            return executions.Select(e => new ReportExecutionDto
            {
                Id = e.Id,
                ReportTemplateId = e.ReportTemplateId ?? Guid.Empty,
                ExecutedBy = e.ExecutedBy ?? Guid.Empty,
                ExecutedAt = e.EndTime ?? DateTime.UtcNow,
                ExecutionTime = e.Duration ?? TimeSpan.Zero,
                RecordsReturned = e.RecordCount ?? 0,
                Status = e.Status,
                ErrorMessage = e.ErrorMessage,
                Parameters = string.IsNullOrEmpty(e.Parameters)
                    ? new Dictionary<string, object>()
                    : JsonSerializer.Deserialize<Dictionary<string, object>>(e.Parameters) ?? new Dictionary<string, object>()
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting report execution history for template {TemplateId}", templateId);
            return new List<ReportExecutionDto>();
        }
    }

    // Helper methods
    private CustomReportTemplateDto MapToDto(AnalyticsBIService.Domain.Entities.CustomReportTemplate template)
    {
        var configuration = string.IsNullOrEmpty(template.Configuration)
            ? new ReportConfigurationDto()
            : JsonSerializer.Deserialize<ReportConfigurationDto>(template.Configuration) ?? new ReportConfigurationDto();

        return new CustomReportTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            CreatedBy = template.CreatedBy,
            UserType = template.UserType,
            Configuration = configuration,
            IsPublic = template.IsPublic,
            IsActive = template.IsActive,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt ?? template.CreatedAt,
            ExecutionCount = template.ExecutionCount,
            LastExecuted = template.LastExecuted
        };
    }

    private async Task<CustomReportResultDto> GenerateReportDataAsync(ReportConfigurationDto configuration, GenerateReportRequest request, CancellationToken cancellationToken)
    {
        var sections = new List<ReportSectionResultDto>();

        // Generate data for each visualization or create a default data section
        if (configuration.Visualizations.Any())
        {
            foreach (var viz in configuration.Visualizations.OrderBy(v => v.Order))
            {
                var infraViz = new VisualizationConfigDto
                {
                    Type = viz.Type,
                    Title = viz.Title,
                    XAxisFields = viz.XAxisFields,
                    YAxisFields = viz.YAxisFields,
                    SeriesFields = viz.SeriesFields,
                    Options = viz.Options,
                    Order = viz.Order
                };
                var sectionData = await GenerateSectionDataAsync(configuration, infraViz, request, cancellationToken);
                sections.Add(sectionData);
            }
        }
        else
        {
            // Create a default table section
            var tableData = await GenerateTableDataAsync(configuration, request, cancellationToken);
            sections.Add(tableData);
        }

        return new CustomReportResultDto
        {
            ReportId = Guid.NewGuid(),
            ReportName = "Custom Report",
            GeneratedAt = DateTime.UtcNow,
            GeneratedBy = request.UserId,
            Sections = sections,
            OutputFormat = request.OutputFormat,
            TotalRecords = sections.Sum(s => s.Data.Count),
            ExecutionTime = TimeSpan.FromMilliseconds(100) // Placeholder
        };
    }

    private async Task<ReportSectionResultDto> GenerateSectionDataAsync(ReportConfigurationDto configuration, VisualizationConfigDto visualization, GenerateReportRequest request, CancellationToken cancellationToken)
    {
        // Generate sample data based on visualization type
        var data = await GenerateVisualizationDataAsync(visualization, configuration, request, cancellationToken);

        return new ReportSectionResultDto
        {
            Name = visualization.Title,
            Type = visualization.Type,
            Data = data,
            Visualization = new VisualizationResultDto
            {
                Type = visualization.Type,
                Title = visualization.Title,
                ChartData = ConvertToChartData(data, visualization),
                Options = visualization.Options
            }
        };
    }

    private async Task<ReportSectionResultDto> GenerateTableDataAsync(ReportConfigurationDto configuration, GenerateReportRequest request, CancellationToken cancellationToken)
    {
        var data = await GenerateDataFromConfiguration(configuration, request, cancellationToken);

        return new ReportSectionResultDto
        {
            Name = "Data Table",
            Type = "table",
            Data = data
        };
    }

    private async Task<List<Dictionary<string, object>>> GenerateVisualizationDataAsync(VisualizationConfigDto visualization, ReportConfigurationDto configuration, GenerateReportRequest request, CancellationToken cancellationToken)
    {
        // This would generate actual data based on the configuration
        // For now, returning sample data
        var data = new List<Dictionary<string, object>>();
        var random = new Random();

        for (int i = 0; i < 10; i++)
        {
            var row = new Dictionary<string, object>();

            foreach (var field in visualization.XAxisFields.Concat(visualization.YAxisFields))
            {
                row[field] = field.Contains("date") || field.Contains("time")
                    ? DateTime.UtcNow.AddDays(-random.Next(0, 30))
                    : field.Contains("count") || field.Contains("amount") || field.Contains("value")
                        ? random.Next(100, 1000)
                        : $"Sample {field} {i + 1}";
            }

            data.Add(row);
        }

        return data;
    }

    private async Task<List<Dictionary<string, object>>> GenerateDataFromConfiguration(ReportConfigurationDto configuration, GenerateReportRequest request, CancellationToken cancellationToken)
    {
        var data = new List<Dictionary<string, object>>();
        var random = new Random();

        // Generate sample data based on fields
        for (int i = 0; i < 50; i++)
        {
            var row = new Dictionary<string, object>();

            foreach (var field in configuration.Fields.Where(f => f.IsVisible).OrderBy(f => f.Order))
            {
                // Convert Application DTO to Infrastructure DTO for method compatibility
                var infrastructureField = new ReportFieldDto
                {
                    Name = field.FieldName,
                    DisplayName = field.DisplayName,
                    DataType = field.DataType,
                    DataSource = field.Source,
                    AggregationType = field.AggregationType,
                    IsVisible = field.IsVisible,
                    Order = field.Order
                };
                row[field.FieldName] = GenerateSampleValue(infrastructureField, random);
            }

            data.Add(row);
        }

        return data;
    }

    private object GenerateSampleValue(ReportFieldDto field, Random random)
    {
        return field.DataType.ToLower() switch
        {
            "string" or "text" => $"Sample {field.Name} {random.Next(1, 100)}",
            "int" or "integer" => random.Next(1, 1000),
            "decimal" or "float" or "double" => Math.Round((decimal)(random.NextDouble() * 1000), 2),
            "datetime" or "date" => DateTime.UtcNow.AddDays(-random.Next(0, 365)),
            "boolean" or "bool" => random.Next(0, 2) == 1,
            _ => $"Value {random.Next(1, 100)}"
        };
    }

    private Dictionary<string, object> ConvertToChartData(List<Dictionary<string, object>> data, VisualizationConfigDto visualization)
    {
        var chartData = new Dictionary<string, object>();

        switch (visualization.Type.ToLower())
        {
            case "bar":
            case "line":
                chartData["labels"] = data.Select(d => d.Values.FirstOrDefault()?.ToString() ?? "").ToList();
                chartData["datasets"] = new List<object>
                {
                    new
                    {
                        label = visualization.Title,
                        data = data.Select(d => d.Values.Skip(1).FirstOrDefault() ?? 0).ToList()
                    }
                };
                break;

            case "pie":
                chartData["labels"] = data.Select(d => d.Values.FirstOrDefault()?.ToString() ?? "").ToList();
                chartData["datasets"] = new List<object>
                {
                    new
                    {
                        data = data.Select(d => d.Values.Skip(1).FirstOrDefault() ?? 0).ToList()
                    }
                };
                break;

            case "kpi":
                var value = data.FirstOrDefault()?.Values.FirstOrDefault() ?? 0;
                chartData["value"] = value;
                chartData["title"] = visualization.Title;
                break;
        }

        return chartData;
    }

    private async Task<List<Dictionary<string, object>>> GeneratePreviewDataAsync(ReportPreviewRequest request, CancellationToken cancellationToken)
    {
        var data = new List<Dictionary<string, object>>();
        var random = new Random();

        for (int i = 0; i < Math.Min(request.MaxRows, 100); i++)
        {
            var row = new Dictionary<string, object>();

            foreach (var field in request.Fields.Where(f => f.IsVisible).OrderBy(f => f.Order))
            {
                row[field.Name] = GenerateSampleValue(field, random);
            }

            data.Add(row);
        }

        return data;
    }

    private async Task UpdateTemplateExecutionCountAsync(Guid templateId, CancellationToken cancellationToken)
    {
        try
        {
            var template = await _context.CustomReportTemplates
                .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

            if (template != null)
            {
                template.RecordExecution();
                await _context.SaveChangesAsync(cancellationToken);

                // Update cache
                var cacheKey = CacheKeys.CustomReport(templateId);
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating template execution count for {TemplateId}", templateId);
        }
    }

    private List<DataSourceDto> GetStandardDataSources(UserType userType)
    {
        var dataSources = new List<DataSourceDto>
        {
            new()
            {
                Name = "analytics_events",
                DisplayName = "Analytics Events",
                Description = "User activity and event tracking data",
                Type = "Table",
                SupportedOperations = new List<string> { "SELECT", "GROUP BY", "ORDER BY", "WHERE" }
            },
            new()
            {
                Name = "metrics",
                DisplayName = "Performance Metrics",
                Description = "KPI and performance measurement data",
                Type = "Table",
                SupportedOperations = new List<string> { "SELECT", "GROUP BY", "ORDER BY", "WHERE", "AGGREGATE" }
            }
        };

        if (userType == UserType.Admin || userType == UserType.TransportCompany)
        {
            dataSources.Add(new DataSourceDto
            {
                Name = "transactions",
                DisplayName = "Business Transactions",
                Description = "Financial and business transaction data",
                Type = "Table",
                SupportedOperations = new List<string> { "SELECT", "GROUP BY", "ORDER BY", "WHERE", "AGGREGATE" }
            });
        }

        return dataSources;
    }

    private List<DataSourceDto> GetAdminDataSources()
    {
        return new List<DataSourceDto>
        {
            new()
            {
                Name = "users",
                DisplayName = "User Data",
                Description = "User registration and profile information",
                Type = "Table",
                SupportedOperations = new List<string> { "SELECT", "GROUP BY", "ORDER BY", "WHERE" }
            },
            new()
            {
                Name = "performance",
                DisplayName = "System Performance",
                Description = "System and application performance metrics",
                Type = "Table",
                SupportedOperations = new List<string> { "SELECT", "GROUP BY", "ORDER BY", "WHERE", "AGGREGATE" }
            }
        };
    }

    private List<DataFieldDto> GetAnalyticsEventFields()
    {
        return new List<DataFieldDto>
        {
            new() { Name = "id", DisplayName = "Event ID", DataType = "string", IsFilterable = true },
            new() { Name = "event_name", DisplayName = "Event Name", DataType = "string", IsFilterable = true, IsSortable = true },
            new() { Name = "event_type", DisplayName = "Event Type", DataType = "string", IsFilterable = true },
            new() { Name = "user_id", DisplayName = "User ID", DataType = "string", IsFilterable = true },
            new() { Name = "user_type", DisplayName = "User Type", DataType = "string", IsFilterable = true },
            new() { Name = "timestamp", DisplayName = "Timestamp", DataType = "datetime", IsFilterable = true, IsSortable = true },
            new() { Name = "properties", DisplayName = "Properties", DataType = "json", IsFilterable = false },
            new() { Name = "session_id", DisplayName = "Session ID", DataType = "string", IsFilterable = true }
        };
    }

    private List<DataFieldDto> GetMetricFields()
    {
        return new List<DataFieldDto>
        {
            new() { Name = "id", DisplayName = "Metric ID", DataType = "string", IsFilterable = true },
            new() { Name = "name", DisplayName = "Metric Name", DataType = "string", IsFilterable = true, IsSortable = true },
            new() { Name = "category", DisplayName = "Category", DataType = "string", IsFilterable = true },
            new() { Name = "type", DisplayName = "Type", DataType = "string", IsFilterable = true },
            new() { Name = "value", DisplayName = "Value", DataType = "decimal", IsAggregatable = true, IsSortable = true, SupportedAggregations = new List<string> { "SUM", "AVG", "MIN", "MAX", "COUNT" } },
            new() { Name = "unit", DisplayName = "Unit", DataType = "string", IsFilterable = true },
            new() { Name = "user_id", DisplayName = "User ID", DataType = "string", IsFilterable = true },
            new() { Name = "created_at", DisplayName = "Created At", DataType = "datetime", IsFilterable = true, IsSortable = true }
        };
    }

    private List<DataFieldDto> GetUserFields()
    {
        return new List<DataFieldDto>
        {
            new() { Name = "id", DisplayName = "User ID", DataType = "string", IsFilterable = true },
            new() { Name = "email", DisplayName = "Email", DataType = "string", IsFilterable = true },
            new() { Name = "user_type", DisplayName = "User Type", DataType = "string", IsFilterable = true },
            new() { Name = "status", DisplayName = "Status", DataType = "string", IsFilterable = true },
            new() { Name = "created_at", DisplayName = "Registration Date", DataType = "datetime", IsFilterable = true, IsSortable = true },
            new() { Name = "last_login", DisplayName = "Last Login", DataType = "datetime", IsFilterable = true, IsSortable = true },
            new() { Name = "is_active", DisplayName = "Is Active", DataType = "boolean", IsFilterable = true }
        };
    }

    private List<DataFieldDto> GetTransactionFields()
    {
        return new List<DataFieldDto>
        {
            new() { Name = "id", DisplayName = "Transaction ID", DataType = "string", IsFilterable = true },
            new() { Name = "amount", DisplayName = "Amount", DataType = "decimal", IsAggregatable = true, IsSortable = true, SupportedAggregations = new List<string> { "SUM", "AVG", "MIN", "MAX", "COUNT" } },
            new() { Name = "currency", DisplayName = "Currency", DataType = "string", IsFilterable = true },
            new() { Name = "status", DisplayName = "Status", DataType = "string", IsFilterable = true },
            new() { Name = "transaction_type", DisplayName = "Transaction Type", DataType = "string", IsFilterable = true },
            new() { Name = "user_id", DisplayName = "User ID", DataType = "string", IsFilterable = true },
            new() { Name = "created_at", DisplayName = "Transaction Date", DataType = "datetime", IsFilterable = true, IsSortable = true }
        };
    }

    private List<DataFieldDto> GetPerformanceFields()
    {
        return new List<DataFieldDto>
        {
            new() { Name = "metric_name", DisplayName = "Metric Name", DataType = "string", IsFilterable = true },
            new() { Name = "value", DisplayName = "Value", DataType = "decimal", IsAggregatable = true, IsSortable = true, SupportedAggregations = new List<string> { "SUM", "AVG", "MIN", "MAX", "COUNT" } },
            new() { Name = "unit", DisplayName = "Unit", DataType = "string", IsFilterable = true },
            new() { Name = "service_name", DisplayName = "Service Name", DataType = "string", IsFilterable = true },
            new() { Name = "timestamp", DisplayName = "Timestamp", DataType = "datetime", IsFilterable = true, IsSortable = true }
        };
    }
}

// Entity classes for database


