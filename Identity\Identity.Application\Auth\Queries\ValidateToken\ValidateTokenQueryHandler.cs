using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using MediatR;

namespace Identity.Application.Auth.Queries.ValidateToken
{
    public class ValidateTokenQueryHandler : IRequestHandler<ValidateTokenQuery, bool>
    {
        private readonly ITokenService _tokenService;

        public ValidateTokenQueryHandler(ITokenService tokenService)
        {
            _tokenService = tokenService;
        }

        public async Task<bool> Handle(ValidateTokenQuery request, CancellationToken cancellationToken)
        {
            return await _tokenService.ValidateTokenAsync(request.Token);
        }
    }
}
