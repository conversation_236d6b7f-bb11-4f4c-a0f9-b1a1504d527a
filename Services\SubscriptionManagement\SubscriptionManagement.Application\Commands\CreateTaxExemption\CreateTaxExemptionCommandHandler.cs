using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Interfaces;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreateTaxExemption
{
    public class CreateTaxExemptionCommandHandler : IRequestHandler<CreateTaxExemptionCommand, Guid>
    {
        private readonly ITaxExemptionRepository _taxExemptionRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateTaxExemptionCommandHandler> _logger;

        public CreateTaxExemptionCommandHandler(
            ITaxExemptionRepository taxExemptionRepository,
            IMessageBroker messageBroker,
            ILogger<CreateTaxExemptionCommandHandler> logger)
        {
            _taxExemptionRepository = taxExemptionRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateTaxExemptionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating tax exemption for user {UserId} with exemption number {ExemptionNumber}",
                request.UserId, request.ExemptionNumber);

            try
            {
                // Check if exemption with same number already exists
                var existingExemptions = await _taxExemptionRepository.GetByExemptionNumberAsync(request.ExemptionNumber);
                if (existingExemptions.Any(e => e.UserId == request.UserId && e.IsActive))
                {
                    throw new SubscriptionDomainException($"Active tax exemption with number {request.ExemptionNumber} already exists for this user");
                }

                // Create new tax exemption
                var taxExemption = new TaxExemption(
                    request.UserId,
                    request.ExemptionType,
                    request.ExemptionNumber,
                    request.IssuingAuthority,
                    request.ValidFrom,
                    request.ValidTo,
                    request.ExemptTaxTypes,
                    request.ApplicableRegions,
                    request.DocumentPath);

                var createdExemption = await _taxExemptionRepository.AddAsync(taxExemption);

                // Publish integration event
                await _messageBroker.PublishAsync("tax.exemption_created", new
                {
                    ExemptionId = createdExemption.Id,
                    UserId = request.UserId,
                    ExemptionType = request.ExemptionType,
                    ExemptionNumber = request.ExemptionNumber,
                    IssuingAuthority = request.IssuingAuthority,
                    ValidFrom = request.ValidFrom,
                    ValidTo = request.ValidTo,
                    ExemptTaxTypes = request.ExemptTaxTypes.Select(t => t.ToString()).ToList(),
                    ApplicableRegions = request.ApplicableRegions,
                    CreatedByUserId = request.CreatedByUserId,
                    CreatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Successfully created tax exemption {Id} for user {UserId}",
                    createdExemption.Id, request.UserId);

                return createdExemption.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tax exemption for user {UserId}", request.UserId);
                throw;
            }
        }
    }
}
