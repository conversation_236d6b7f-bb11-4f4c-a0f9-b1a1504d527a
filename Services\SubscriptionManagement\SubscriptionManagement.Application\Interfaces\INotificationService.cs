using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SubscriptionManagement.Application.Interfaces
{
    public enum NotificationType
    {
        Email,
        SMS,
        Push,
        InApp
    }

    public enum NotificationPriority
    {
        Low,
        Normal,
        High,
        Critical
    }

    public class NotificationRequest
    {
        public Guid UserId { get; set; }
        public string Subject { get; set; }
        public string Message { get; set; }
        public NotificationType Type { get; set; }
        public NotificationPriority Priority { get; set; }
        public Dictionary<string, object> Data { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public string TemplateId { get; set; }
        public Dictionary<string, string> TemplateData { get; set; }
    }

    public interface INotificationService
    {
        Task<bool> SendNotificationAsync(NotificationRequest request);
        Task<bool> SendBulkNotificationAsync(List<NotificationRequest> requests);
        Task<bool> SendTemplatedNotificationAsync(Guid userId, string templateId, Dictionary<string, string> templateData, NotificationType type = NotificationType.Email);
        Task<bool> ScheduleNotificationAsync(NotificationRequest request, DateTime scheduledAt);
        Task<bool> CancelScheduledNotificationAsync(Guid notificationId);
        Task<List<Dictionary<string, object>>> GetNotificationHistoryAsync(Guid userId, int limit = 50);
        Task<Dictionary<string, object>> GetNotificationStatsAsync(Guid userId);
        Task<bool> UpdateNotificationPreferencesAsync(Guid userId, Dictionary<NotificationType, bool> preferences);
        Task<Dictionary<NotificationType, bool>> GetNotificationPreferencesAsync(Guid userId);
        Task<bool> MarkNotificationAsReadAsync(Guid notificationId);
        Task<List<Dictionary<string, object>>> GetUnreadNotificationsAsync(Guid userId);
        Task<int> GetUnreadNotificationCountAsync(Guid userId);
    }
}
