using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace CommunicationNotification.Tests.Infrastructure;

/// <summary>
/// Test web application factory for integration testing
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    private readonly string _testDatabaseName = $"TestDb_{Guid.NewGuid()}";

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            // Add test configuration
            config.AddJsonFile("appsettings.test.json", optional: false);
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Database:UseInMemoryDatabase"] = "true",
                ["Testing:EnableMockProviders"] = "true",
                ["Testing:EnableTestData"] = "true"
            });
        });

        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<CommunicationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database context
            services.AddDbContext<CommunicationDbContext>(options =>
            {
                options.UseInMemoryDatabase(_testDatabaseName);
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Configure test authentication
            services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = false, // Disable for testing
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = TestConstants.TestJwtIssuer,
                    ValidAudience = TestConstants.TestJwtAudience,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(TestConstants.TestJwtSecret)),
                    ClockSkew = TimeSpan.Zero
                };
            });

            // Add mock external services
            services.AddMockExternalServices();

            // Override logging for tests
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Warning);
            });
        });

        builder.UseEnvironment("Testing");
    }

    /// <summary>
    /// Create HTTP client with authentication
    /// </summary>
    public HttpClient CreateAuthenticatedClient(string userId = TestConstants.TestUserId, string role = "User")
    {
        var client = CreateClient();
        var token = GenerateJwtToken(userId, role);
        client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        return client;
    }

    /// <summary>
    /// Create HTTP client with admin authentication
    /// </summary>
    public HttpClient CreateAdminClient(string userId = TestConstants.TestAdminId)
    {
        return CreateAuthenticatedClient(userId, "Admin");
    }

    /// <summary>
    /// Create HTTP client with driver authentication
    /// </summary>
    public HttpClient CreateDriverClient(string userId = TestConstants.TestDriverId)
    {
        return CreateAuthenticatedClient(userId, "Driver");
    }

    /// <summary>
    /// Create HTTP client with customer authentication
    /// </summary>
    public HttpClient CreateCustomerClient(string userId = TestConstants.TestCustomerId)
    {
        return CreateAuthenticatedClient(userId, "Customer");
    }

    /// <summary>
    /// Generate JWT token for testing
    /// </summary>
    private string GenerateJwtToken(string userId, string role)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(TestConstants.TestJwtSecret);
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId),
            new(ClaimTypes.Name, $"Test User {userId}"),
            new(ClaimTypes.Role, role),
            new("user_id", userId),
            new("permission", "notifications:send"),
            new("permission", "messages:read"),
            new("permission", "messages:write")
        };

        if (role == "Admin")
        {
            claims.AddRange(new[]
            {
                new Claim("permission", "admin:manage"),
                new Claim("permission", "users:manage"),
                new Claim("permission", "system:monitor")
            });
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(1),
            Issuer = TestConstants.TestJwtIssuer,
            Audience = TestConstants.TestJwtAudience,
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key), 
                SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Seed test data in the database
    /// </summary>
    public async Task SeedTestDataAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CommunicationDbContext>();
        
        await context.Database.EnsureCreatedAsync();
        
        // Seed test data
        await SeedTestUsers(context);
        await SeedTestTemplates(context);
        await SeedTestConversations(context);
        
        await context.SaveChangesAsync();
    }

    private async Task SeedTestUsers(CommunicationDbContext context)
    {
        if (await context.UserPreferences.AnyAsync())
            return;

        var userPreferences = new[]
        {
            new Domain.Entities.UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse(TestConstants.TestAdminId),
                UserRole = Domain.Enums.UserRole.Admin,
                PreferredLanguage = Domain.Enums.Language.English,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new Domain.ValueObjects.ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "admin_device_token"
                },
                NotificationSettings = new Domain.ValueObjects.NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = true,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = true,
                    PreferredChannels = new List<Domain.Enums.NotificationChannel>
                    {
                        Domain.Enums.NotificationChannel.Email,
                        Domain.Enums.NotificationChannel.Push
                    }
                },
                CustomSettings = new Dictionary<string, string>(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Domain.Entities.UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse(TestConstants.TestDriverId),
                UserRole = Domain.Enums.UserRole.Driver,
                PreferredLanguage = Domain.Enums.Language.Kannada,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new Domain.ValueObjects.ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "driver_device_token"
                },
                NotificationSettings = new Domain.ValueObjects.NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = false,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = false,
                    PreferredChannels = new List<Domain.Enums.NotificationChannel>
                    {
                        Domain.Enums.NotificationChannel.WhatsApp,
                        Domain.Enums.NotificationChannel.Push
                    }
                },
                CustomSettings = new Dictionary<string, string>(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Domain.Entities.UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse(TestConstants.TestCustomerId),
                UserRole = Domain.Enums.UserRole.Customer,
                PreferredLanguage = Domain.Enums.Language.English,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new Domain.ValueObjects.ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "customer_device_token"
                },
                NotificationSettings = new Domain.ValueObjects.NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = true,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = false,
                    PreferredChannels = new List<Domain.Enums.NotificationChannel>
                    {
                        Domain.Enums.NotificationChannel.WhatsApp,
                        Domain.Enums.NotificationChannel.Email
                    }
                },
                CustomSettings = new Dictionary<string, string>(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.UserPreferences.AddRangeAsync(userPreferences);
    }

    private async Task SeedTestTemplates(CommunicationDbContext context)
    {
        if (await context.Templates.AnyAsync())
            return;

        var templates = new[]
        {
            new Domain.Entities.Template
            {
                Id = Guid.NewGuid(),
                Name = "test_notification",
                Description = "Test notification template",
                MessageType = Domain.Enums.MessageType.SystemNotification,
                Channel = Domain.Enums.NotificationChannel.Push,
                Language = Domain.Enums.Language.English,
                Subject = "Test Notification",
                Body = "This is a test notification: {{message}}",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["message"] = "Test message content"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "test",
                    ["priority"] = "normal"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Templates.AddRangeAsync(templates);
    }

    private async Task SeedTestConversations(CommunicationDbContext context)
    {
        if (await context.ConversationThreads.AnyAsync())
            return;

        var conversation = new Domain.Entities.ConversationThread
        {
            Id = Guid.NewGuid(),
            Title = "Test Support Conversation",
            Description = "Test conversation for integration testing",
            Type = Domain.Enums.ConversationType.Support,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = Guid.Parse(TestConstants.TestCustomerId),
            LastActivity = DateTime.UtcNow,
            Metadata = new Dictionary<string, string>
            {
                ["test"] = "true",
                ["category"] = "support"
            },
            UpdatedAt = DateTime.UtcNow
        };

        await context.ConversationThreads.AddAsync(conversation);

        var participants = new[]
        {
            new Domain.Entities.ConversationParticipant
            {
                Id = Guid.NewGuid(),
                ConversationThreadId = conversation.Id,
                UserId = Guid.Parse(TestConstants.TestCustomerId),
                Role = Domain.Enums.ParticipantRole.Customer,
                JoinedAt = DateTime.UtcNow,
                CanWrite = true,
                CanRead = true,
                IsActive = true,
                NotificationSettings = new Dictionary<string, object>
                {
                    ["notify_on_message"] = true
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Domain.Entities.ConversationParticipant
            {
                Id = Guid.NewGuid(),
                ConversationThreadId = conversation.Id,
                UserId = Guid.Parse(TestConstants.TestAdminId),
                Role = Domain.Enums.ParticipantRole.Support,
                JoinedAt = DateTime.UtcNow,
                CanWrite = true,
                CanRead = true,
                IsActive = true,
                NotificationSettings = new Dictionary<string, object>
                {
                    ["notify_on_message"] = true
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.ConversationParticipants.AddRangeAsync(participants);
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up test database
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CommunicationDbContext>();
            context.Database.EnsureDeleted();
        }
        
        base.Dispose(disposing);
    }
}
