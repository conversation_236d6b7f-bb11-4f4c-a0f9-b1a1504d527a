using MediatR;
using TripManagement.Application.DTOs;

namespace TripManagement.Application.Queries.GetTripLocation;

public class GetTripLocationQuery : IRequest<TripLocationDto?>
{
    public Guid TripId { get; set; }
}

public class GetTripLocationHistoryQuery : IRequest<List<TripLocationDto>>
{
    public Guid TripId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int Limit { get; set; } = 100;
}

public class GetRealTimeTripStatusQuery : IRequest<RealTimeTripStatusDto?>
{
    public Guid TripId { get; set; }
}
