using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Commands.CreateRfq;
using OrderManagement.Application.Commands.SubmitBid;
using OrderManagement.Domain.Enums;
using OrderManagement.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace OrderManagement.IntegrationTests.Workflows;

public class RfqToBidWorkflowTests : IntegrationTestBase
{
    public RfqToBidWorkflowTests(TestWebApplicationFactory<Program> factory) : base(factory)
    {
    }

    [Fact]
    public async Task CompleteRfqToBidWorkflow_ShouldWorkEndToEnd()
    {
        // Arrange
        await SeedDatabaseAsync();

        // Step 1: Create RFQ
        var createRfqCommand = new CreateRfqCommand
        {
            Title = "End-to-End Test RFQ",
            Description = "Complete workflow test from RFQ creation to bid submission",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Electronics shipment",
                LoadType = LoadType.Fragile,
                Weight = new CreateWeightDto { Value = 500, Unit = WeightUnit.Kg },
                Quantity = 10,
                RequiredVehicleType = VehicleType.Van,
                RequiresSpecialHandling = true,
                SpecialHandlingInstructions = "Handle with care - fragile electronics"
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "123 Electronics Blvd",
                    City = "Tech City",
                    State = "CA",
                    PostalCode = "90210",
                    Country = "USA",
                    ContactPerson = "John Shipper",
                    ContactPhone = "******-0123"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "456 Retail Plaza",
                    City = "Commerce City",
                    State = "TX",
                    PostalCode = "75001",
                    Country = "USA",
                    ContactPerson = "Jane Receiver",
                    ContactPhone = "******-0456"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(2),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(5),
                RouteNotes = "Avoid rough roads due to fragile cargo"
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Van,
                RequiresInsurance = true,
                MinInsuranceAmount = 100000,
                RequiresExperience = true,
                MinYearsExperience = 5,
                RequiresTracking = true,
                AdditionalRequirements = new List<string> { "Climate controlled", "GPS tracking", "Fragile cargo experience" }
            },
            BudgetRange = new CreateRfq.CreateMoneyDto { Amount = 2000, Currency = "USD" },
            IsUrgent = false,
            ContactPerson = "Transport Manager",
            ContactPhone = "******-0789",
            ContactEmail = "<EMAIL>"
        };

        // Act & Assert: Create RFQ
        var createRfqResponse = await Client.PostAsJsonAsync("/api/v1/rfq", createRfqCommand);
        createRfqResponse.StatusCode.Should().Be(HttpStatusCode.Created);

        var rfqId = JsonSerializer.Deserialize<Guid>(await createRfqResponse.Content.ReadAsStringAsync());
        rfqId.Should().NotBeEmpty();

        // Verify RFQ in database
        using (var context = await GetDbContextAsync())
        {
            var rfq = await context.Rfqs.FirstOrDefaultAsync(r => r.Id == rfqId);
            rfq.Should().NotBeNull();
            rfq!.Status.Should().Be(RfqStatus.Draft);
            rfq.Title.Should().Be("End-to-End Test RFQ");
            rfq.LoadDetails.LoadType.Should().Be(LoadType.Fragile);
            rfq.Requirements.RequiresInsurance.Should().BeTrue();
            rfq.Requirements.MinInsuranceAmount.Should().Be(100000);
        }

        // Step 2: Get RFQ details
        var getRfqResponse = await Client.GetAsync($"/api/v1/rfq/{rfqId}");
        getRfqResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var rfqContent = await getRfqResponse.Content.ReadAsStringAsync();
        rfqContent.Should().Contain("End-to-End Test RFQ");
        rfqContent.Should().Contain("Electronics shipment");

        // Step 3: Publish RFQ
        var publishResponse = await Client.PostAsync($"/api/v1/rfq/{rfqId}/publish", null);
        publishResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Verify RFQ is published
        using (var context = await GetDbContextAsync())
        {
            var rfq = await context.Rfqs.FirstOrDefaultAsync(r => r.Id == rfqId);
            rfq!.Status.Should().Be(RfqStatus.Published);
        }

        // Step 4: Submit multiple bids
        var bid1Command = new SubmitBidCommand
        {
            RfqId = rfqId,
            QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1800, Currency = "USD" },
            EstimatedPickupDate = DateTime.UtcNow.AddDays(2),
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(4),
            ProposedTerms = "Standard terms with fragile cargo handling",
            VehicleDetails = "2022 Mercedes Sprinter Van - Climate Controlled",
            DriverDetails = "Certified fragile cargo specialist with 8 years experience",
            AdditionalServices = "White glove service, real-time tracking, photo documentation",
            Notes = "Specialized in electronics transportation"
        };

        var submitBid1Response = await Client.PostAsJsonAsync("/api/v1/bid", bid1Command);
        submitBid1Response.StatusCode.Should().Be(HttpStatusCode.Created);

        var bid1Id = JsonSerializer.Deserialize<Guid>(await submitBid1Response.Content.ReadAsStringAsync());

        // Step 5: Verify bid in database
        using (var context = await GetDbContextAsync())
        {
            var bid = await context.RfqBids
                .Include(b => b.Rfq)
                .FirstOrDefaultAsync(b => b.Id == bid1Id);

            bid.Should().NotBeNull();
            bid!.RfqId.Should().Be(rfqId);
            bid.Status.Should().Be(BidStatus.Submitted);
            bid.QuotedPrice.Amount.Should().Be(1800);
            bid.QuotedPrice.Currency.Should().Be("USD");
            bid.VehicleDetails.Should().Contain("Mercedes Sprinter");
            bid.Rfq.Should().NotBeNull();
            bid.Rfq.Title.Should().Be("End-to-End Test RFQ");
        }

        // Step 6: Verify RFQ now has bids
        using (var context = await GetDbContextAsync())
        {
            var rfqWithBids = await context.Rfqs
                .Include(r => r.Bids)
                .FirstOrDefaultAsync(r => r.Id == rfqId);

            rfqWithBids!.Bids.Should().HaveCount(1);
            rfqWithBids.Bids.First().Id.Should().Be(bid1Id);
        }

        // Step 7: Get RFQ again to see it includes the bid
        var getRfqWithBidsResponse = await Client.GetAsync($"/api/v1/rfq/{rfqId}");
        getRfqWithBidsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var rfqWithBidsContent = await getRfqWithBidsResponse.Content.ReadAsStringAsync();
        rfqWithBidsContent.Should().Contain("1800"); // Bid amount
        rfqWithBidsContent.Should().Contain("Mercedes Sprinter");
    }

    [Fact]
    public async Task RfqLifecycle_FromDraftToPublishedWithMultipleBids_ShouldMaintainDataIntegrity()
    {
        // Arrange
        await SeedDatabaseAsync();

        // Create RFQ
        var rfqCommand = new CreateRfqCommand
        {
            Title = "Multi-Bid Test RFQ",
            Description = "Testing multiple bids on single RFQ",
            LoadDetails = new CreateLoadDetailsDto
            {
                Description = "Standard cargo",
                LoadType = LoadType.General,
                Weight = new CreateWeightDto { Value = 1000, Unit = WeightUnit.Kg },
                Quantity = 1,
                RequiredVehicleType = VehicleType.Truck
            },
            RouteDetails = new CreateRouteDetailsDto
            {
                PickupAddress = new CreateAddressDto
                {
                    Street = "100 Origin St",
                    City = "Start City",
                    State = "SC",
                    PostalCode = "10001",
                    Country = "USA"
                },
                DeliveryAddress = new CreateAddressDto
                {
                    Street = "200 Destination Ave",
                    City = "End City",
                    State = "EC",
                    PostalCode = "20002",
                    Country = "USA"
                },
                PreferredPickupDate = DateTime.UtcNow.AddDays(1),
                PreferredDeliveryDate = DateTime.UtcNow.AddDays(3)
            },
            Requirements = new CreateRfqRequirementsDto
            {
                RequiredVehicleType = VehicleType.Truck,
                RequiresInsurance = true,
                RequiresTracking = true
            }
        };

        // Create and publish RFQ
        var createResponse = await Client.PostAsJsonAsync("/api/v1/rfq", rfqCommand);
        var rfqId = JsonSerializer.Deserialize<Guid>(await createResponse.Content.ReadAsStringAsync());

        await Client.PostAsync($"/api/v1/rfq/{rfqId}/publish", null);

        // Submit multiple bids with different prices and terms
        var bidCommands = new[]
        {
            new SubmitBidCommand
            {
                RfqId = rfqId,
                QuotedPrice = new SubmitBid.CreateMoneyDto { Amount = 1200, Currency = "USD" },
                EstimatedPickupDate = DateTime.UtcNow.AddDays(1),
                EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2),
                VehicleDetails = "Standard truck",
                Notes = "Competitive pricing"
            }
        };

        var bidIds = new List<Guid>();
        foreach (var bidCommand in bidCommands)
        {
            var bidResponse = await Client.PostAsJsonAsync("/api/v1/bid", bidCommand);
            bidResponse.StatusCode.Should().Be(HttpStatusCode.Created);
            var bidId = JsonSerializer.Deserialize<Guid>(await bidResponse.Content.ReadAsStringAsync());
            bidIds.Add(bidId);
        }

        // Verify data integrity
        using var context = await GetDbContextAsync();
        var rfqWithBids = await context.Rfqs
            .Include(r => r.Bids)
            .FirstOrDefaultAsync(r => r.Id == rfqId);

        rfqWithBids.Should().NotBeNull();
        rfqWithBids!.Bids.Should().HaveCount(1);
        rfqWithBids.Status.Should().Be(RfqStatus.Published);

        foreach (var bidId in bidIds)
        {
            var bid = rfqWithBids.Bids.FirstOrDefault(b => b.Id == bidId);
            bid.Should().NotBeNull();
            bid!.Status.Should().Be(BidStatus.Submitted);
            bid.RfqId.Should().Be(rfqId);
        }
    }
}
