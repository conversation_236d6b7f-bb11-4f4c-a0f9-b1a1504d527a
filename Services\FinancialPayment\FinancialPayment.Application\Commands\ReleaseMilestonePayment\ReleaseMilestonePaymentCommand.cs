using MediatR;
using FinancialPayment.Application.DTOs;

namespace FinancialPayment.Application.Commands.ReleaseMilestonePayment;

public class ReleaseMilestonePaymentCommand : IRequest<bool>
{
    public Guid MilestoneId { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string CompletionNotes { get; set; } = string.Empty;
    public List<RecipientPaymentDto> Recipients { get; set; } = new();
    public bool AutoCreateSettlement { get; set; } = true;
    public bool NotifyRecipients { get; set; } = true;
    public string? PaymentMethodId { get; set; }
    public Guid RequestingUserId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class RecipientPaymentDto
{
    public Guid RecipientId { get; set; }
    public string RecipientType { get; set; } = string.Empty; // Carrier, Broker, TransportCompany
    public MoneyDto Amount { get; set; } = new();
    public string PaymentReason { get; set; } = string.Empty;
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
