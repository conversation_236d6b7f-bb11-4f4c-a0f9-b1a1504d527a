using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class ReconciliationService : IReconciliationService
{
    private readonly IReconciliationRepository _reconciliationRepository;
    private readonly IPlatformTransactionProvider _platformTransactionProvider;
    private readonly ILogger<ReconciliationService> _logger;
    private readonly ReconciliationConfiguration _configuration;
    private readonly Dictionary<string, IPaymentGatewayReconciliationProvider> _gatewayProviders;

    public ReconciliationService(
        IReconciliationRepository reconciliationRepository,
        IPlatformTransactionProvider platformTransactionProvider,
        ILogger<ReconciliationService> logger,
        IOptions<ReconciliationConfiguration> configuration,
        IEnumerable<IPaymentGatewayReconciliationProvider> gatewayProviders)
    {
        _reconciliationRepository = reconciliationRepository;
        _platformTransactionProvider = platformTransactionProvider;
        _logger = logger;
        _configuration = configuration.Value;
        _gatewayProviders = gatewayProviders.ToDictionary(gp => gp.GatewayName, StringComparer.OrdinalIgnoreCase);
    }

    public async Task<PaymentReconciliation> StartReconciliationAsync(ReconciliationRequest request)
    {
        _logger.LogInformation("Starting reconciliation for gateway {Gateway} from {Start} to {End}",
            request.PaymentGateway, request.PeriodStart, request.PeriodEnd);

        try
        {
            var reconciliation = new PaymentReconciliation(
                request.PeriodStart,
                request.PeriodEnd,
                request.PaymentGateway,
                request.Currency);

            reconciliation.StartReconciliation();
            await _reconciliationRepository.AddAsync(reconciliation);

            _logger.LogInformation("Reconciliation {ReconciliationNumber} started successfully",
                reconciliation.ReconciliationNumber);

            if (request.AutoProcess)
            {
                return await ProcessReconciliationAsync(reconciliation.Id);
            }

            return reconciliation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting reconciliation for gateway {Gateway}", request.PaymentGateway);
            throw;
        }
    }

    public async Task<PaymentReconciliation> ProcessReconciliationAsync(Guid reconciliationId)
    {
        _logger.LogInformation("Processing reconciliation {ReconciliationId}", reconciliationId);

        try
        {
            var reconciliation = await _reconciliationRepository.GetByIdAsync(reconciliationId);
            if (reconciliation == null)
            {
                throw new InvalidOperationException($"Reconciliation {reconciliationId} not found");
            }

            if (reconciliation.Status != ReconciliationStatus.InProgress)
            {
                throw new InvalidOperationException($"Reconciliation {reconciliationId} is not in progress");
            }

            // Get platform transactions
            var platformTransactions = await _platformTransactionProvider.GetTransactionsAsync(
                reconciliation.PeriodStart,
                reconciliation.PeriodEnd,
                reconciliation.PaymentGateway);

            _logger.LogDebug("Found {Count} platform transactions for reconciliation {ReconciliationId}",
                platformTransactions.Count, reconciliationId);

            // Get gateway transactions
            var gatewayTransactions = new List<GatewayTransaction>();
            if (_gatewayProviders.TryGetValue(reconciliation.PaymentGateway, out var gatewayProvider))
            {
                gatewayTransactions = await gatewayProvider.GetTransactionsAsync(
                    reconciliation.PeriodStart,
                    reconciliation.PeriodEnd);

                _logger.LogDebug("Found {Count} gateway transactions for reconciliation {ReconciliationId}",
                    gatewayTransactions.Count, reconciliationId);
            }
            else
            {
                _logger.LogWarning("No gateway provider found for {Gateway}", reconciliation.PaymentGateway);
            }

            // Perform reconciliation matching
            await PerformReconciliationMatching(reconciliation, platformTransactions, gatewayTransactions);

            // Check if reconciliation requires review
            var settings = GetGatewaySettings(reconciliation.PaymentGateway);
            if (ShouldRequireReview(reconciliation, settings))
            {
                reconciliation.RequireReview("Reconciliation requires manual review due to variances");
            }

            await _reconciliationRepository.UpdateAsync(reconciliation);

            _logger.LogInformation("Reconciliation {ReconciliationId} processed successfully. Status: {Status}",
                reconciliationId, reconciliation.Status);

            return reconciliation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing reconciliation {ReconciliationId}", reconciliationId);
            
            var reconciliation = await _reconciliationRepository.GetByIdAsync(reconciliationId);
            if (reconciliation != null)
            {
                reconciliation.FailReconciliation(ex.Message);
                await _reconciliationRepository.UpdateAsync(reconciliation);
            }
            
            throw;
        }
    }

    public async Task<PaymentReconciliation> CompleteReconciliationAsync(Guid reconciliationId, Guid completedBy, string? notes = null)
    {
        _logger.LogInformation("Completing reconciliation {ReconciliationId}", reconciliationId);

        try
        {
            var reconciliation = await _reconciliationRepository.GetByIdAsync(reconciliationId);
            if (reconciliation == null)
            {
                throw new InvalidOperationException($"Reconciliation {reconciliationId} not found");
            }

            reconciliation.CompleteReconciliation(completedBy, notes);
            await _reconciliationRepository.UpdateAsync(reconciliation);

            _logger.LogInformation("Reconciliation {ReconciliationId} completed successfully", reconciliationId);
            return reconciliation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing reconciliation {ReconciliationId}", reconciliationId);
            throw;
        }
    }

    public async Task<ReconciliationSummary> GetReconciliationSummaryAsync(Guid reconciliationId)
    {
        try
        {
            var reconciliation = await _reconciliationRepository.GetByIdAsync(reconciliationId);
            if (reconciliation == null)
            {
                throw new InvalidOperationException($"Reconciliation {reconciliationId} not found");
            }

            return reconciliation.GetSummary();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reconciliation summary for {ReconciliationId}", reconciliationId);
            throw;
        }
    }

    public async Task<List<PaymentReconciliation>> GetReconciliationHistoryAsync(string? paymentGateway = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var to = toDate ?? DateTime.UtcNow;

            return await _reconciliationRepository.GetByPeriodAsync(from, to, paymentGateway);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting reconciliation history");
            throw;
        }
    }

    public async Task<List<ReconciliationDiscrepancy>> GetUnresolvedDiscrepanciesAsync(string? paymentGateway = null)
    {
        try
        {
            var reconciliations = await _reconciliationRepository.GetByStatusAsync(ReconciliationStatus.RequiresReview);
            
            var discrepancies = reconciliations
                .Where(r => paymentGateway == null || r.PaymentGateway == paymentGateway)
                .SelectMany(r => r.Discrepancies)
                .Where(d => !d.IsResolved)
                .ToList();

            return discrepancies;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unresolved discrepancies");
            throw;
        }
    }

    public async Task<bool> ResolveDiscrepancyAsync(Guid discrepancyId, string resolution, Guid resolvedBy)
    {
        try
        {
            // This would require a more complex query to find the reconciliation containing the discrepancy
            // For now, we'll implement a simplified version
            _logger.LogInformation("Resolving discrepancy {DiscrepancyId}", discrepancyId);
            
            // In a real implementation, you would:
            // 1. Find the reconciliation containing the discrepancy
            // 2. Update the discrepancy
            // 3. Save the reconciliation
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving discrepancy {DiscrepancyId}", discrepancyId);
            return false;
        }
    }

    private async Task PerformReconciliationMatching(
        PaymentReconciliation reconciliation,
        List<PlatformTransaction> platformTransactions,
        List<GatewayTransaction> gatewayTransactions)
    {
        var settings = GetGatewaySettings(reconciliation.PaymentGateway);
        var matchedGatewayIds = new HashSet<string>();

        // Match platform transactions with gateway transactions
        foreach (var platformTx in platformTransactions)
        {
            var gatewayTx = FindMatchingGatewayTransaction(platformTx, gatewayTransactions, settings);
            
            if (gatewayTx != null)
            {
                matchedGatewayIds.Add(gatewayTx.TransactionId);
                
                var status = DetermineMatchStatus(platformTx, gatewayTx, settings);
                reconciliation.AddReconciliationItem(
                    platformTx.TransactionId,
                    gatewayTx.TransactionId,
                    platformTx.Amount,
                    gatewayTx.Amount,
                    platformTx.TransactionDate,
                    status);

                if (status != ReconciliationItemStatus.Matched)
                {
                    AddDiscrepancyForMismatch(reconciliation, platformTx, gatewayTx, status);
                }
            }
            else
            {
                // Unmatched platform transaction
                reconciliation.AddReconciliationItem(
                    platformTx.TransactionId,
                    null,
                    platformTx.Amount,
                    null,
                    platformTx.TransactionDate,
                    ReconciliationItemStatus.UnmatchedPlatform,
                    "No matching gateway transaction found");

                reconciliation.AddDiscrepancy(
                    $"Platform transaction {platformTx.TransactionId} has no matching gateway transaction",
                    DiscrepancyType.MissingTransaction,
                    platformTx.Amount,
                    platformTx.TransactionId);
            }
        }

        // Add unmatched gateway transactions
        foreach (var gatewayTx in gatewayTransactions.Where(gt => !matchedGatewayIds.Contains(gt.TransactionId)))
        {
            reconciliation.AddReconciliationItem(
                string.Empty,
                gatewayTx.TransactionId,
                null,
                gatewayTx.Amount,
                gatewayTx.TransactionDate,
                ReconciliationItemStatus.UnmatchedGateway,
                "No matching platform transaction found");

            reconciliation.AddDiscrepancy(
                $"Gateway transaction {gatewayTx.TransactionId} has no matching platform transaction",
                DiscrepancyType.MissingTransaction,
                gatewayTx.Amount,
                null,
                gatewayTx.TransactionId);
        }
    }

    private GatewayTransaction? FindMatchingGatewayTransaction(
        PlatformTransaction platformTx,
        List<GatewayTransaction> gatewayTransactions,
        ReconciliationGatewaySettings settings)
    {
        // First try exact match by gateway transaction ID
        if (!string.IsNullOrEmpty(platformTx.GatewayTransactionId))
        {
            var exactMatch = gatewayTransactions.FirstOrDefault(gt => gt.TransactionId == platformTx.GatewayTransactionId);
            if (exactMatch != null) return exactMatch;
        }

        // Then try matching by amount and date within tolerance
        var toleranceAmount = new Money(settings.ToleranceAmount, platformTx.Amount.Currency);
        var toleranceTime = TimeSpan.FromHours(settings.ToleranceHours);

        return gatewayTransactions.FirstOrDefault(gt =>
            Math.Abs((gt.Amount - platformTx.Amount).Amount) <= toleranceAmount.Amount &&
            Math.Abs((gt.TransactionDate - platformTx.TransactionDate).TotalHours) <= toleranceTime.TotalHours);
    }

    private ReconciliationItemStatus DetermineMatchStatus(
        PlatformTransaction platformTx,
        GatewayTransaction gatewayTx,
        ReconciliationGatewaySettings settings)
    {
        var toleranceAmount = new Money(settings.ToleranceAmount, platformTx.Amount.Currency);
        var toleranceTime = TimeSpan.FromHours(settings.ToleranceHours);

        var amountDiff = Math.Abs((gatewayTx.Amount - platformTx.Amount).Amount);
        var timeDiff = Math.Abs((gatewayTx.TransactionDate - platformTx.TransactionDate).TotalHours);

        if (amountDiff > toleranceAmount.Amount)
            return ReconciliationItemStatus.AmountMismatch;

        if (timeDiff > toleranceTime.TotalHours)
            return ReconciliationItemStatus.DateMismatch;

        return ReconciliationItemStatus.Matched;
    }

    private void AddDiscrepancyForMismatch(
        PaymentReconciliation reconciliation,
        PlatformTransaction platformTx,
        GatewayTransaction gatewayTx,
        ReconciliationItemStatus status)
    {
        var discrepancyType = status switch
        {
            ReconciliationItemStatus.AmountMismatch => DiscrepancyType.AmountMismatch,
            ReconciliationItemStatus.DateMismatch => DiscrepancyType.DateMismatch,
            _ => DiscrepancyType.Other
        };

        var description = status switch
        {
            ReconciliationItemStatus.AmountMismatch => 
                $"Amount mismatch: Platform {platformTx.Amount}, Gateway {gatewayTx.Amount}",
            ReconciliationItemStatus.DateMismatch => 
                $"Date mismatch: Platform {platformTx.TransactionDate}, Gateway {gatewayTx.TransactionDate}",
            _ => "Transaction mismatch"
        };

        var varianceAmount = status == ReconciliationItemStatus.AmountMismatch 
            ? platformTx.Amount - gatewayTx.Amount 
            : Money.Zero(platformTx.Amount.Currency);

        reconciliation.AddDiscrepancy(
            description,
            discrepancyType,
            varianceAmount,
            platformTx.TransactionId,
            gatewayTx.TransactionId);
    }

    private bool ShouldRequireReview(PaymentReconciliation reconciliation, ReconciliationGatewaySettings settings)
    {
        if (!_configuration.RequireManualReviewForVariances)
            return false;

        var varianceThreshold = new Money(_configuration.VarianceThresholdForReview, reconciliation.VarianceAmount.Currency);
        return Math.Abs(reconciliation.VarianceAmount.Amount) > varianceThreshold.Amount ||
               reconciliation.Discrepancies.Any();
    }

    private ReconciliationGatewaySettings GetGatewaySettings(string gatewayName)
    {
        if (_configuration.GatewaySettings.TryGetValue(gatewayName, out var settings))
        {
            return settings;
        }

        return new ReconciliationGatewaySettings
        {
            GatewayName = gatewayName,
            ToleranceAmount = _configuration.DefaultToleranceAmount,
            ToleranceHours = _configuration.DefaultToleranceHours
        };
    }
}
