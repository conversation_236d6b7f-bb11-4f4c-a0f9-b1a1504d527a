using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Driver trip metrics data transfer object
    /// </summary>
    public class DriverTripMetricsDto
    {
        public int TotalTrips { get; set; }
        public int CompletedTrips { get; set; }
        public int CancelledTrips { get; set; }
        public int OnTimeDeliveries { get; set; }
        public int DelayedDeliveries { get; set; }
        public decimal AverageDeliveryTime { get; set; }
        public decimal TotalDistanceKm { get; set; }
        public decimal TotalDrivingHours { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal TripCompletionRate { get; set; }
        public decimal AverageSpeedKmh { get; set; }
        public decimal FuelEfficiency { get; set; }

        // Properties required by DriverPerformanceAnalyticsService
        public decimal BaseEarnings { get; set; }
        public decimal TotalRevenue { get; set; }
    }



    /// <summary>
    /// Driver compliance metrics data transfer object
    /// </summary>
    public class DriverComplianceMetricsDto
    {
        public string LicenseStatus { get; set; } = string.Empty;
        public decimal DocumentComplianceScore { get; set; }
        public int SafetyViolations { get; set; }
        public decimal TrainingCompletionRate { get; set; }
        public int ComplianceAlerts { get; set; }
        public DateTime LicenseExpiryDate { get; set; }
        public bool IsLicenseValid { get; set; }
        public List<ComplianceIssueDto> ComplianceIssues { get; set; } = new();
    }

    /// <summary>
    /// Driver earnings metrics data transfer object
    /// </summary>
    public class DriverEarningsMetricsDto
    {
        public decimal TotalEarnings { get; set; }
        public decimal AverageEarningsPerTrip { get; set; }
        public decimal AverageEarningsPerKm { get; set; }
        public decimal BaseEarnings { get; set; }
        public decimal IncentiveEarnings { get; set; }
        public decimal BonusEarnings { get; set; }
        public decimal DeductionAmount { get; set; }
        public decimal NetEarnings { get; set; }
        public List<EarningsBreakdownDto> EarningsBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Driver feedback metrics data transfer object
    /// </summary>
    public class DriverFeedbackMetricsDto
    {
        public decimal AverageRating { get; set; }
        public int TotalFeedbacks { get; set; }
        public int PositiveFeedbacks { get; set; }
        public int NegativeFeedbacks { get; set; }
        public decimal ServiceQualityRating { get; set; }
        public decimal CommunicationRating { get; set; }
        public decimal ProfessionalismRating { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public List<FeedbackCategoryDto> FeedbackBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Driver incentive data transfer object
    /// </summary>
    public class DriverIncentiveDto
    {
        public Guid IncentiveId { get; set; }
        public string IncentiveName { get; set; } = string.Empty;
        public string IncentiveType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime EarnedDate { get; set; }
        public DateTime? PaidDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Criteria { get; set; } = new();
    }

    /// <summary>
    /// Driver performance ranking data transfer object
    /// </summary>
    public class DriverPerformanceRankingDto
    {
        public int OverallRank { get; set; }
        public int TotalDrivers { get; set; }
        public decimal PercentileRank { get; set; }
        public string RankingCategory { get; set; } = string.Empty;
        public List<RankingMetricDto> MetricRankings { get; set; } = new();
        public List<DriverComparisonDto> PeerComparisons { get; set; } = new();
    }

    /// <summary>
    /// Performance recommendation data transfer object
    /// </summary>
    public class PerformanceRecommendationDto
    {
        public Guid RecommendationId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public decimal ImpactScore { get; set; }
        public List<string> ActionItems { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public bool IsImplemented { get; set; }
    }



    /// <summary>
    /// Compliance issue data transfer object
    /// </summary>
    public class ComplianceIssueDto
    {
        public string IssueType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime IdentifiedDate { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Ranking metric data transfer object
    /// </summary>
    public class RankingMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public int Rank { get; set; }
        public decimal Value { get; set; }
        public decimal BenchmarkValue { get; set; }
        public string Performance { get; set; } = string.Empty;
    }

    /// <summary>
    /// Driver performance dashboard data transfer object
    /// </summary>
    public class DriverPerformanceDashboardDto
    {
        public Guid DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public DateRangeDto DateRange { get; set; } = new();
        public decimal OverallPerformanceScore { get; set; }
        public DriverKPIAnalyticsDto KPIs { get; set; } = new();
        public DriverTripMetricsDto TripMetrics { get; set; } = new();
        public DriverFeedbackMetricsDto FeedbackMetrics { get; set; } = new();
        public DriverComplianceMetricsDto ComplianceMetrics { get; set; } = new();
        public DriverEarningsMetricsDto EarningsMetrics { get; set; } = new();
        public List<DriverIncentiveDto> Incentives { get; set; } = new();
        public DriverPerformanceRankingDto PerformanceRanking { get; set; } = new();
        public List<PerformanceRecommendationDto> Recommendations { get; set; } = new();
        public List<PerformanceKPIDto> PerformanceKPIs { get; set; } = new();
        public List<PerformanceTrendDto> Trends { get; set; } = new();
        public List<AchievementDto> Achievements { get; set; } = new();
        public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
}
