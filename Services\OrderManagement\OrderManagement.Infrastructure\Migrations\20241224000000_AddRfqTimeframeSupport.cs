using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OrderManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRfqTimeframeSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add timeframe columns to Rfqs table
            migrationBuilder.AddColumn<int>(
                name: "Timeframe_Duration",
                table: "Rfqs",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Timeframe_Unit",
                table: "Rfqs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Timeframe_CreatedAt",
                table: "Rfqs",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "Timeframe_ExpiresAt",
                table: "Rfqs",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Timeframe_AllowExtensions",
                table: "Rfqs",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Timeframe_MaxExtensions",
                table: "Rfqs",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Timeframe_ExtensionCount",
                table: "Rfqs",
                type: "integer",
                nullable: true);

            // Create TimeframeExtensions table
            migrationBuilder.CreateTable(
                name: "TimeframeExtensions",
                columns: table => new
                {
                    RFQId = table.Column<Guid>(type: "uuid", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", Npgsql.EntityFrameworkCore.PostgreSQL.Metadata.NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Duration = table.Column<int>(type: "integer", nullable: false),
                    Unit = table.Column<string>(type: "text", nullable: false),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ExtendedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExtendedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    NewExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TimeframeExtensions", x => new { x.RFQId, x.Id });
                    table.ForeignKey(
                        name: "FK_TimeframeExtensions_Rfqs_RFQId",
                        column: x => x.RFQId,
                        principalTable: "Rfqs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create indexes for performance
            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_Status_ExpiresAt",
                table: "Rfqs",
                columns: new[] { "Status", "ExpiresAt" });

            migrationBuilder.CreateIndex(
                name: "IX_Rfqs_Timeframe_ExpiresAt",
                table: "Rfqs",
                column: "Timeframe_ExpiresAt");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_Rfqs_Status_ExpiresAt",
                table: "Rfqs");

            migrationBuilder.DropIndex(
                name: "IX_Rfqs_Timeframe_ExpiresAt",
                table: "Rfqs");

            // Drop TimeframeExtensions table
            migrationBuilder.DropTable(
                name: "TimeframeExtensions");

            // Remove timeframe columns from Rfqs table
            migrationBuilder.DropColumn(
                name: "Timeframe_Duration",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_Unit",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_CreatedAt",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_ExpiresAt",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_AllowExtensions",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_MaxExtensions",
                table: "Rfqs");

            migrationBuilder.DropColumn(
                name: "Timeframe_ExtensionCount",
                table: "Rfqs");
        }
    }
}
