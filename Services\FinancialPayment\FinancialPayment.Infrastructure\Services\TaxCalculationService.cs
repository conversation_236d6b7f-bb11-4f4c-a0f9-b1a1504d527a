using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class TaxCalculationService : ITaxCalculationService
{
    private readonly ITaxRuleRepository _taxRuleRepository;
    private readonly ITaxCalculationRepository _taxCalculationRepository;
    private readonly ILogger<TaxCalculationService> _logger;
    private readonly TaxConfiguration _taxConfiguration;

    public TaxCalculationService(
        ITaxRuleRepository taxRuleRepository,
        ITaxCalculationRepository taxCalculationRepository,
        ILogger<TaxCalculationService> logger,
        IOptions<TaxConfiguration> taxConfiguration)
    {
        _taxRuleRepository = taxRuleRepository;
        _taxCalculationRepository = taxCalculationRepository;
        _logger = logger;
        _taxConfiguration = taxConfiguration.Value;
    }

    public async Task<TaxCalculation> CalculateTaxAsync(TaxCalculationRequest request)
    {
        _logger.LogInformation("Calculating tax for order {OrderId} in jurisdiction {Jurisdiction}",
            request.OrderId, request.Jurisdiction);

        try
        {
            // Check if calculation already exists and force recalculation is not requested
            if (!request.ForceRecalculation)
            {
                var existingCalculation = await _taxCalculationRepository.GetByOrderIdAsync(request.OrderId);
                if (existingCalculation != null && existingCalculation.Status == TaxCalculationStatus.Calculated)
                {
                    _logger.LogInformation("Using existing tax calculation for order {OrderId}", request.OrderId);
                    return existingCalculation;
                }
            }

            var calculationDate = request.CalculationDate ?? DateTime.UtcNow;
            
            // Get applicable tax rules
            var applicableRules = await GetApplicableTaxRulesAsync(
                request.Jurisdiction,
                calculationDate,
                request.ProductCategory,
                request.ServiceType);

            if (!applicableRules.Any())
            {
                _logger.LogWarning("No applicable tax rules found for jurisdiction {Jurisdiction}", request.Jurisdiction);
            }

            // Create tax calculation
            var taxCalculation = new TaxCalculation(
                request.OrderId,
                request.UserId,
                request.Amount,
                request.Jurisdiction,
                request.ProductCategory,
                request.ServiceType);

            // Apply each applicable tax rule
            foreach (var rule in applicableRules.OrderBy(r => r.Priority))
            {
                if (rule.EvaluateConditions(request.Context))
                {
                    var taxAmount = CalculateTaxAmount(request.Amount, rule);
                    taxCalculation.AddTaxLine(rule, taxAmount, $"Applied {rule.TaxType} at {rule.Rate}%");
                    
                    _logger.LogDebug("Applied tax rule {RuleName} with amount {TaxAmount} for order {OrderId}",
                        rule.Name, taxAmount, request.OrderId);
                }
            }

            taxCalculation.MarkAsCalculated($"Tax calculated using {applicableRules.Count} rules");

            // Save the calculation
            await _taxCalculationRepository.AddAsync(taxCalculation);

            _logger.LogInformation("Tax calculation completed for order {OrderId}. Total tax: {TotalTax}",
                request.OrderId, taxCalculation.TotalTaxAmount);

            return taxCalculation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating tax for order {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<List<TaxRule>> GetApplicableTaxRulesAsync(
        string jurisdiction, 
        DateTime date, 
        string? productCategory = null, 
        string? serviceType = null)
    {
        try
        {
            var rules = await _taxRuleRepository.GetApplicableRulesAsync(jurisdiction, date, productCategory, serviceType);
            
            // Filter rules that are applicable for the given date and criteria
            var applicableRules = rules.Where(rule => rule.IsApplicable(date, productCategory, serviceType)).ToList();

            _logger.LogDebug("Found {Count} applicable tax rules for jurisdiction {Jurisdiction}",
                applicableRules.Count, jurisdiction);

            return applicableRules;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting applicable tax rules for jurisdiction {Jurisdiction}", jurisdiction);
            throw;
        }
    }

    public async Task<TaxCalculation> RecalculateTaxAsync(Guid taxCalculationId)
    {
        _logger.LogInformation("Recalculating tax for calculation {TaxCalculationId}", taxCalculationId);

        try
        {
            var existingCalculation = await _taxCalculationRepository.GetByIdAsync(taxCalculationId);
            if (existingCalculation == null)
            {
                throw new InvalidOperationException($"Tax calculation {taxCalculationId} not found");
            }

            var request = new TaxCalculationRequest
            {
                OrderId = existingCalculation.OrderId,
                UserId = existingCalculation.UserId,
                Amount = existingCalculation.SubtotalAmount,
                Jurisdiction = existingCalculation.Jurisdiction,
                ProductCategory = existingCalculation.ProductCategory,
                ServiceType = existingCalculation.ServiceType,
                ForceRecalculation = true
            };

            return await CalculateTaxAsync(request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recalculating tax for calculation {TaxCalculationId}", taxCalculationId);
            throw;
        }
    }

    public async Task<bool> ValidateTaxCalculationAsync(Guid taxCalculationId)
    {
        try
        {
            var calculation = await _taxCalculationRepository.GetByIdAsync(taxCalculationId);
            if (calculation == null)
            {
                return false;
            }

            // Validate that all tax lines have valid amounts
            foreach (var taxLine in calculation.TaxLines)
            {
                if (taxLine.TaxAmount.Amount < 0)
                {
                    _logger.LogWarning("Invalid negative tax amount in calculation {TaxCalculationId}", taxCalculationId);
                    return false;
                }
            }

            // Validate total amounts
            var expectedTotalTax = calculation.TaxLines.Sum(tl => tl.TaxAmount.Amount);
            if (Math.Abs(calculation.TotalTaxAmount.Amount - expectedTotalTax) > 0.01m)
            {
                _logger.LogWarning("Tax calculation total mismatch in calculation {TaxCalculationId}", taxCalculationId);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tax calculation {TaxCalculationId}", taxCalculationId);
            return false;
        }
    }

    public async Task<TaxCalculationSummary> GetTaxSummaryAsync(Guid orderId)
    {
        try
        {
            var calculation = await _taxCalculationRepository.GetByOrderIdAsync(orderId);
            if (calculation == null)
            {
                throw new InvalidOperationException($"No tax calculation found for order {orderId}");
            }

            return calculation.GetSummary();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax summary for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<List<TaxCalculation>> GetTaxCalculationHistoryAsync(Guid orderId)
    {
        try
        {
            return await _taxCalculationRepository.GetHistoryByOrderIdAsync(orderId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tax calculation history for order {OrderId}", orderId);
            throw;
        }
    }

    private Money CalculateTaxAmount(Money baseAmount, TaxRule taxRule)
    {
        return taxRule.CalculationType switch
        {
            TaxCalculationType.Percentage => new Money(baseAmount.Amount * (taxRule.Rate / 100), baseAmount.Currency),
            TaxCalculationType.FixedAmount => new Money(taxRule.Rate, baseAmount.Currency),
            TaxCalculationType.Tiered => CalculateTieredTax(baseAmount, taxRule),
            TaxCalculationType.Compound => CalculateCompoundTax(baseAmount, taxRule),
            _ => Money.Zero(baseAmount.Currency)
        };
    }

    private Money CalculateTieredTax(Money baseAmount, TaxRule taxRule)
    {
        // Simplified tiered calculation - in real implementation, this would use tax brackets
        return new Money(baseAmount.Amount * (taxRule.Rate / 100), baseAmount.Currency);
    }

    private Money CalculateCompoundTax(Money baseAmount, TaxRule taxRule)
    {
        // Simplified compound calculation
        return new Money(baseAmount.Amount * (taxRule.Rate / 100), baseAmount.Currency);
    }
}
