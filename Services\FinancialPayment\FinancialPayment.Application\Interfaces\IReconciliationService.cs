using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Application.Interfaces;

public interface IReconciliationService
{
    Task<PaymentReconciliation> StartReconciliationAsync(ReconciliationRequest request);
    Task<PaymentReconciliation> ProcessReconciliationAsync(Guid reconciliationId);
    Task<PaymentReconciliation> CompleteReconciliationAsync(Guid reconciliationId, Guid completedBy, string? notes = null);
    Task<ReconciliationSummary> GetReconciliationSummaryAsync(Guid reconciliationId);
    Task<List<PaymentReconciliation>> GetReconciliationHistoryAsync(string? paymentGateway = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<ReconciliationDiscrepancy>> GetUnresolvedDiscrepanciesAsync(string? paymentGateway = null);
    Task<bool> ResolveDiscrepancyAsync(Guid discrepancyId, string resolution, Guid resolvedBy);
}

public interface IPaymentGatewayReconciliationProvider
{
    string GatewayName { get; }
    Task<List<GatewayTransaction>> GetTransactionsAsync(DateTime fromDate, DateTime toDate);
    Task<GatewayTransaction?> GetTransactionAsync(string transactionId);
    Task<bool> ValidateTransactionAsync(string transactionId, Money amount);
}

public interface IReconciliationRepository
{
    Task<PaymentReconciliation?> GetByIdAsync(Guid id);
    Task<PaymentReconciliation?> GetByReconciliationNumberAsync(string reconciliationNumber);
    Task<List<PaymentReconciliation>> GetByPeriodAsync(DateTime fromDate, DateTime toDate, string? paymentGateway = null);
    Task<List<PaymentReconciliation>> GetByStatusAsync(ReconciliationStatus status);
    Task AddAsync(PaymentReconciliation reconciliation);
    Task UpdateAsync(PaymentReconciliation reconciliation);
    Task DeleteAsync(Guid id);
}

public interface IPlatformTransactionProvider
{
    Task<List<PlatformTransaction>> GetTransactionsAsync(DateTime fromDate, DateTime toDate, string? paymentGateway = null);
    Task<PlatformTransaction?> GetTransactionAsync(string transactionId);
}

public class ReconciliationRequest
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public string PaymentGateway { get; set; } = string.Empty;
    public string Currency { get; set; } = "INR";
    public bool AutoProcess { get; set; } = true;
    public decimal ToleranceAmount { get; set; } = 0.01m;
    public int ToleranceHours { get; set; } = 24;
}

public class GatewayTransaction
{
    public string TransactionId { get; set; } = string.Empty;
    public string? ReferenceId { get; set; }
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Status { get; set; } = string.Empty;
    public DateTime TransactionDate { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class PlatformTransaction
{
    public string TransactionId { get; set; } = string.Empty;
    public string? GatewayTransactionId { get; set; }
    public Money Amount { get; set; } = Money.Zero("INR");
    public string Status { get; set; } = string.Empty;
    public DateTime TransactionDate { get; set; }
    public string PaymentGateway { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public Guid? OrderId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ReconciliationResult
{
    public bool IsSuccess { get; set; }
    public PaymentReconciliation? Reconciliation { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Warnings { get; set; } = new();
    public ReconciliationStatistics Statistics { get; set; } = new();
}

public class ReconciliationStatistics
{
    public int TotalPlatformTransactions { get; set; }
    public int TotalGatewayTransactions { get; set; }
    public int MatchedTransactions { get; set; }
    public int UnmatchedPlatformTransactions { get; set; }
    public int UnmatchedGatewayTransactions { get; set; }
    public int AmountMismatches { get; set; }
    public int DateMismatches { get; set; }
    public Money TotalVariance { get; set; } = Money.Zero("INR");
    public decimal MatchPercentage { get; set; }
    public TimeSpan ProcessingTime { get; set; }
}

public class ReconciliationConfiguration
{
    public bool EnableAutomaticReconciliation { get; set; } = true;
    public int ReconciliationIntervalHours { get; set; } = 24;
    public decimal DefaultToleranceAmount { get; set; } = 0.01m;
    public int DefaultToleranceHours { get; set; } = 24;
    public bool RequireManualReviewForVariances { get; set; } = true;
    public decimal VarianceThresholdForReview { get; set; } = 100.00m;
    public List<string> EnabledGateways { get; set; } = new();
    public Dictionary<string, ReconciliationGatewaySettings> GatewaySettings { get; set; } = new();
}

public class ReconciliationGatewaySettings
{
    public string GatewayName { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public decimal ToleranceAmount { get; set; } = 0.01m;
    public int ToleranceHours { get; set; } = 24;
    public string ApiEndpoint { get; set; } = string.Empty;
    public Dictionary<string, string> ApiCredentials { get; set; } = new();
    public int MaxRetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 30;
}
