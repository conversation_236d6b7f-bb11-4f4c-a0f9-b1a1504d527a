using Microsoft.Extensions.Logging;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Infrastructure.Services;

public class SettlementService : ISettlementService
{
    private readonly ISettlementRepository _settlementRepository;
    private readonly IEscrowAccountRepository _escrowAccountRepository;
    private readonly IEnhancedPaymentService _paymentService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<SettlementService> _logger;

    public SettlementService(
        ISettlementRepository settlementRepository,
        IEscrowAccountRepository escrowAccountRepository,
        IEnhancedPaymentService paymentService,
        IUnitOfWork unitOfWork,
        ILogger<SettlementService> logger)
    {
        _settlementRepository = settlementRepository;
        _escrowAccountRepository = escrowAccountRepository;
        _paymentService = paymentService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Settlement> CreateSettlementAsync(
        Guid orderId,
        Guid tripId,
        Guid escrowAccountId,
        Money totalAmount,
        List<SettlementDistributionRequest> distributions)
    {
        _logger.LogInformation("Creating settlement for order {OrderId} and trip {TripId}", orderId, tripId);

        try
        {
            // Check if settlement already exists
            var existingSettlement = await _settlementRepository.GetByOrderIdAsync(orderId);
            if (existingSettlement != null)
            {
                throw new InvalidOperationException($"Settlement already exists for order {orderId}");
            }

            // Verify escrow account exists
            var escrowAccount = await _escrowAccountRepository.GetByIdAsync(escrowAccountId);
            if (escrowAccount == null)
            {
                throw new InvalidOperationException($"Escrow account {escrowAccountId} not found");
            }

            // Create settlement
            var settlement = new Settlement(orderId, tripId, escrowAccountId, totalAmount);

            // Add distributions
            foreach (var distributionRequest in distributions)
            {
                settlement.AddDistribution(
                    distributionRequest.RecipientId,
                    distributionRequest.RecipientRole,
                    distributionRequest.Amount,
                    distributionRequest.Type,
                    distributionRequest.Description);
            }

            await _settlementRepository.AddAsync(settlement);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully created settlement {SettlementId} for order {OrderId}",
                settlement.Id, orderId);

            return settlement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating settlement for order {OrderId}", orderId);
            throw;
        }
    }

    public async Task<bool> ProcessSettlementAsync(Guid settlementId)
    {
        _logger.LogInformation("Processing settlement {SettlementId}", settlementId);

        try
        {
            var settlement = await _settlementRepository.GetByIdAsync(settlementId);
            if (settlement == null)
            {
                throw new InvalidOperationException($"Settlement {settlementId} not found");
            }

            // Start processing
            settlement.Process();

            // Process each distribution
            foreach (var distribution in settlement.Distributions)
            {
                var paymentResult = await _paymentService.ProcessSettlementDistributionAsync(
                    distribution.Id,
                    distribution.Amount,
                    distribution.RecipientId,
                    "default");

                if (paymentResult.IsSuccess)
                {
                    settlement.CompleteDistribution(distribution.Id, paymentResult.TransactionId);
                }
                else
                {
                    settlement.FailDistribution(distribution.Id, paymentResult.ErrorMessage ?? "Payment failed");
                    break;
                }
            }

            _settlementRepository.UpdateAsync(settlement);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully processed settlement {SettlementId}", settlementId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing settlement {SettlementId}", settlementId);
            throw;
        }
    }

    public async Task<Settlement?> GetSettlementByIdAsync(Guid settlementId)
    {
        return await _settlementRepository.GetByIdAsync(settlementId);
    }

    public async Task<Settlement?> GetSettlementByOrderIdAsync(Guid orderId)
    {
        return await _settlementRepository.GetByOrderIdAsync(orderId);
    }

    public async Task<Settlement?> GetSettlementByTripIdAsync(Guid tripId)
    {
        return await _settlementRepository.GetByTripIdAsync(tripId);
    }

    public async Task<List<Settlement>> GetSettlementsByCarrierAsync(Guid carrierId)
    {
        return await _settlementRepository.GetByCarrierIdAsync(carrierId);
    }

    public async Task<List<Settlement>> GetSettlementsByBrokerAsync(Guid brokerId)
    {
        return await _settlementRepository.GetByBrokerIdAsync(brokerId);
    }
}
