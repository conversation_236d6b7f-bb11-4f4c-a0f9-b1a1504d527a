using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.UIComponents
{
    public class CreateUIComponentCommand : IRequest<UIComponentDto>
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty; // Button, Input, Card, List, etc.
        public string Category { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public List<string> SupportedPlatforms { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
        public Dictionary<string, object> Styling { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public Dictionary<string, object> PlatformSpecific { get; set; } = new();
        public List<string> Dependencies { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string CreatedBy { get; set; } = string.Empty;
        public bool IsTemplate { get; set; } = false;
        public bool IsPublic { get; set; } = true;
    }
}
