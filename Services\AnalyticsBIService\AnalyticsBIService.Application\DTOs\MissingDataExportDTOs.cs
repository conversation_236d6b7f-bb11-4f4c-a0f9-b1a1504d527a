using System;
using System.Collections.Generic;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Custom report data transfer object
    /// </summary>
    public class CustomReport
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public Guid CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModified { get; set; }
        public bool IsPublic { get; set; }
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public List<CustomReportParameter> Parameters { get; set; } = new();
        public CustomReportMetadata Metadata { get; set; } = new();

        // Properties required by DataExportService
        public string? ScheduleCron { get; set; }
        public bool IsScheduled { get; set; }

        // Additional properties required by DataExportService
        public Guid UserId { get; set; }
        public string ReportName { get; set; } = string.Empty;
        public string ReportType { get; set; } = string.Empty;
        public List<string> DataSources { get; set; } = new();
        public Dictionary<string, object> Filters { get; set; } = new();
        public List<string> Columns { get; set; } = new();
        public Dictionary<string, object> Aggregations { get; set; } = new();
        public Dictionary<string, object> Visualizations { get; set; } = new();
        public Dictionary<string, object> ScheduleConfig { get; set; } = new();
        public bool IsActive { get; set; } = true;
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Custom report parameter
    /// </summary>
    public class CustomReportParameter
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public object? DefaultValue { get; set; }
        public List<object> AllowedValues { get; set; } = new();
        public string? ValidationRule { get; set; }
    }

    /// <summary>
    /// Custom report metadata
    /// </summary>
    public class CustomReportMetadata
    {
        public int EstimatedRows { get; set; }
        public TimeSpan EstimatedGenerationTime { get; set; }
        public List<string> DataSources { get; set; } = new();
        public List<ExportFormat> SupportedFormats { get; set; } = new();
        public string Version { get; set; } = "1.0";
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }



    /// <summary>
    /// Field mapping data transfer object
    /// </summary>
    public class FieldMappingDto
    {
        public string SourceField { get; set; } = string.Empty;
        public string TargetField { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public string? DefaultValue { get; set; }
        public string? TransformationRule { get; set; }
    }

    /// <summary>
    /// Data source options data transfer object
    /// </summary>
    public class DataSourceOptionsDto
    {
        public int? TimeoutSeconds { get; set; }
        public int? RetryCount { get; set; }
        public bool EnableCaching { get; set; } = false;
        public int? CacheDurationMinutes { get; set; }
        public bool EnableCompression { get; set; } = false;
        public Dictionary<string, string> Headers { get; set; } = new();
        public Dictionary<string, object> CustomOptions { get; set; } = new();
    }



    /// <summary>
    /// Transformation rule data transfer object
    /// </summary>
    public class TransformationRuleDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Expression { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public bool IsActive { get; set; } = true;
        public int Order { get; set; }
    }

    /// <summary>
    /// Validation rule data transfer object
    /// </summary>
    public class ValidationRuleDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "Required", "Range", "Pattern", "Custom"
        public string Field { get; set; } = string.Empty;
        public string Rule { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }



    /// <summary>
    /// Data destination options data transfer object
    /// </summary>
    public class DataDestinationOptionsDto
    {
        public string? FilePath { get; set; }
        public string? FileName { get; set; }
        public ExportFormat? FileFormat { get; set; }
        public bool OverwriteExisting { get; set; } = false;
        public bool CreateBackup { get; set; } = false;
        public int? BatchSize { get; set; }
        public bool EnableCompression { get; set; } = false;
        public Dictionary<string, string> Headers { get; set; } = new();
        public Dictionary<string, object> CustomOptions { get; set; } = new();
    }



    /// <summary>
    /// Sorting data transfer object
    /// </summary>
    public class SortingDto
    {
        public string Field { get; set; } = string.Empty;
        public string Direction { get; set; } = "ASC"; // "ASC", "DESC"
        public int Priority { get; set; }
    }

    /// <summary>
    /// Trend prediction data transfer object
    /// </summary>
    public class TrendPredictionDto
    {
        public string MetricName { get; set; } = string.Empty;
        public string TrendType { get; set; } = string.Empty; // "Linear", "Exponential", "Seasonal"
        public List<PredictionDataPointDto> PredictedValues { get; set; } = new();
        public decimal ConfidenceLevel { get; set; }
        public string TrendDirection { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
        public decimal TrendStrength { get; set; }
        public DateTime PredictionDate { get; set; }
        public TimeSpan PredictionHorizon { get; set; }
        public Dictionary<string, object> ModelParameters { get; set; } = new();
    }

    /// <summary>
    /// Prediction data point data transfer object
    /// </summary>
    public class PredictionDataPointDto
    {
        public DateTime Date { get; set; }
        public decimal PredictedValue { get; set; }
        public decimal ConfidenceInterval { get; set; }
        public decimal LowerBound { get; set; }
        public decimal UpperBound { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Seasonal pattern data transfer object
    /// </summary>
    public class SeasonalPatternDto
    {
        public string PatternName { get; set; } = string.Empty;
        public string PatternType { get; set; } = string.Empty; // "Daily", "Weekly", "Monthly", "Yearly"
        public List<SeasonalDataPointDto> PatternData { get; set; } = new();
        public decimal Strength { get; set; }
        public decimal Confidence { get; set; }
        public DateTime DetectedAt { get; set; }
        public TimeSpan PatternDuration { get; set; }
        public Dictionary<string, object> PatternParameters { get; set; } = new();
    }

    /// <summary>
    /// Seasonal data point data transfer object
    /// </summary>
    public class SeasonalDataPointDto
    {
        public string Period { get; set; } = string.Empty; // "Monday", "January", "Q1", etc.
        public decimal Value { get; set; }
        public decimal Deviation { get; set; }
        public decimal Confidence { get; set; }
        public int Occurrences { get; set; }
    }
}
