using System;
using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Commands.UIComponents
{
    public class CreateThemeCommand : IRequest<ThemeDto>
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0";
        public List<string> SupportedPlatforms { get; set; } = new();
        public Dictionary<string, object> ColorPalette { get; set; } = new();
        public Dictionary<string, object> Typography { get; set; } = new();
        public Dictionary<string, object> Spacing { get; set; } = new();
        public Dictionary<string, object> ComponentStyles { get; set; } = new();
        public Dictionary<string, object> PlatformSpecific { get; set; } = new();
        public Dictionary<string, object> DarkModeOverrides { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string CreatedBy { get; set; } = string.Empty;
        public bool IsDefault { get; set; } = false;
        public bool IsPublic { get; set; } = true;
    }
}
