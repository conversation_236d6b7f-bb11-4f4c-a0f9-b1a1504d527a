using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Domain.ValueObjects;
using Newtonsoft.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface ICrossPlatformSyncService
{
    Task<SyncResult> SyncOfflineDataAsync(Guid userId, List<OfflineData> offlineData, CancellationToken cancellationToken = default);
    Task<SyncResult> SyncUserDataAsync(Guid userId, Dictionary<string, object> userData, CancellationToken cancellationToken = default);
    Task<ConflictResolutionResult> ResolveDataConflictAsync(OfflineData localData, Dictionary<string, object> serverData, CancellationToken cancellationToken = default);
    Task<List<OfflineData>> GetPendingSyncDataAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> ValidateDataIntegrityAsync(OfflineData data, CancellationToken cancellationToken = default);
    Task<SyncConfiguration> GetSyncConfigurationAsync(Guid userId, string platform, CancellationToken cancellationToken = default);
}

public class CrossPlatformSyncService : ICrossPlatformSyncService
{
    private readonly IOfflineDataRepository _offlineDataRepository;
    private readonly IMobileSessionRepository _sessionRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CrossPlatformSyncService> _logger;

    public CrossPlatformSyncService(
        IOfflineDataRepository offlineDataRepository,
        IMobileSessionRepository sessionRepository,
        IMemoryCache cache,
        ILogger<CrossPlatformSyncService> logger)
    {
        _offlineDataRepository = offlineDataRepository;
        _sessionRepository = sessionRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<SyncResult> SyncOfflineDataAsync(Guid userId, List<OfflineData> offlineData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting sync for user {UserId} with {Count} offline data items", userId, offlineData.Count);

        var result = new SyncResult
        {
            UserId = userId,
            TotalItems = offlineData.Count,
            StartTime = DateTime.UtcNow
        };

        try
        {
            // Sort by priority (critical first)
            var sortedData = offlineData.OrderBy(d => d.Priority).ToList();

            foreach (var data in sortedData)
            {
                try
                {
                    var syncItemResult = await SyncSingleItemAsync(data, cancellationToken);
                    
                    if (syncItemResult.IsSuccess)
                    {
                        result.SuccessfulItems++;
                        data.MarkAsSynced();
                        await _offlineDataRepository.UpdateAsync(data, cancellationToken);
                    }
                    else
                    {
                        result.FailedItems++;
                        data.RecordSyncError(syncItemResult.ErrorMessage ?? "Unknown sync error");
                        await _offlineDataRepository.UpdateAsync(data, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing offline data item {DataId}", data.Id);
                    result.FailedItems++;
                    data.RecordSyncError(ex.Message);
                    await _offlineDataRepository.UpdateAsync(data, cancellationToken);
                }
            }

            result.EndTime = DateTime.UtcNow;
            result.IsSuccess = result.FailedItems == 0;

            _logger.LogInformation("Sync completed for user {UserId}. Success: {Success}, Failed: {Failed}", 
                userId, result.SuccessfulItems, result.FailedItems);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error during sync for user {UserId}", userId);
            result.EndTime = DateTime.UtcNow;
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    public async Task<SyncResult> SyncUserDataAsync(Guid userId, Dictionary<string, object> userData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Syncing user data for user {UserId}", userId);

        var result = new SyncResult
        {
            UserId = userId,
            TotalItems = userData.Count,
            StartTime = DateTime.UtcNow
        };

        try
        {
            // Update user session data
            var activeSession = await _sessionRepository.GetActiveSessionByUserIdAsync(userId, cancellationToken);
            if (activeSession != null)
            {
                foreach (var kvp in userData)
                {
                    activeSession.UpdateSessionData(kvp.Key, kvp.Value);
                }
                
                activeSession.SyncCompleted();
                await _sessionRepository.UpdateAsync(activeSession, cancellationToken);
                
                result.SuccessfulItems = userData.Count;
                result.IsSuccess = true;
            }
            else
            {
                result.ErrorMessage = "No active session found for user";
                result.IsSuccess = false;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing user data for user {UserId}", userId);
            result.EndTime = DateTime.UtcNow;
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    public async Task<ConflictResolutionResult> ResolveDataConflictAsync(OfflineData localData, Dictionary<string, object> serverData, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Resolving data conflict for data type {DataType}", localData.DataType);

        var result = new ConflictResolutionResult
        {
            DataId = localData.Id,
            DataType = localData.DataType,
            ConflictDetected = true
        };

        try
        {
            var config = await GetSyncConfigurationAsync(localData.UserId, "default", cancellationToken);
            var strategy = config.GetConflictResolutionStrategy(localData.DataType);

            switch (strategy.ToLower())
            {
                case "client_wins":
                    result.ResolvedData = localData.Data;
                    result.Resolution = "Client data preserved";
                    break;

                case "server_wins":
                    result.ResolvedData = serverData;
                    result.Resolution = "Server data accepted";
                    break;

                case "latest_timestamp":
                    result.ResolvedData = ResolveByTimestamp(localData.Data, serverData);
                    result.Resolution = "Latest timestamp wins";
                    break;

                case "merge":
                    result.ResolvedData = MergeData(localData.Data, serverData);
                    result.Resolution = "Data merged";
                    break;

                default:
                    result.ResolvedData = serverData;
                    result.Resolution = "Default: Server wins";
                    break;
            }

            localData.SetConflictResolution(result.Resolution);
            result.IsResolved = true;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving conflict for data {DataId}", localData.Id);
            result.IsResolved = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    public async Task<List<OfflineData>> GetPendingSyncDataAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var pendingData = await _offlineDataRepository.GetUnsyncedDataByUserIdAsync(userId, cancellationToken);
        return pendingData.Where(d => d.ShouldRetrySync()).ToList();
    }

    public async Task<bool> ValidateDataIntegrityAsync(OfflineData data, CancellationToken cancellationToken = default)
    {
        try
        {
            // Basic validation
            if (data.Data == null || !data.Data.Any())
                return false;

            // Data type specific validation
            switch (data.DataType.ToLower())
            {
                case "tripupdate":
                    return ValidateTripUpdateData(data.Data);
                case "podupload":
                    return ValidatePODUploadData(data.Data);
                case "documentupload":
                    return ValidateDocumentUploadData(data.Data);
                default:
                    return true; // Allow unknown types for now
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating data integrity for {DataId}", data.Id);
            return false;
        }
    }

    public async Task<SyncConfiguration> GetSyncConfigurationAsync(Guid userId, string platform, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"sync_config_{userId}_{platform}";
        
        if (_cache.TryGetValue(cacheKey, out SyncConfiguration? cachedConfig) && cachedConfig != null)
        {
            return cachedConfig;
        }

        // Default configuration - in real implementation, this would come from user preferences or platform settings
        var config = new SyncConfiguration(
            autoSyncEnabled: true,
            syncInterval: TimeSpan.FromMinutes(5),
            maxRetryAttempts: 3,
            retryDelay: TimeSpan.FromMinutes(1),
            syncOnWiFiOnly: false,
            syncOnLowBattery: false,
            maxOfflineDataSize: 100,
            maxOfflineDataAge: 24,
            priorityDataTypes: new List<string> { "Emergency", "TripUpdate", "PODUpload" }
        );

        _cache.Set(cacheKey, config, TimeSpan.FromHours(1));
        return config;
    }

    private async Task<SyncItemResult> SyncSingleItemAsync(OfflineData data, CancellationToken cancellationToken)
    {
        // Validate data integrity
        if (!await ValidateDataIntegrityAsync(data, cancellationToken))
        {
            return new SyncItemResult { IsSuccess = false, ErrorMessage = "Data integrity validation failed" };
        }

        // Simulate API call to sync data
        // In real implementation, this would call the appropriate service based on data type
        await Task.Delay(100, cancellationToken); // Simulate network call

        return new SyncItemResult { IsSuccess = true };
    }

    private Dictionary<string, object> ResolveByTimestamp(Dictionary<string, object> localData, Dictionary<string, object> serverData)
    {
        var localTimestamp = GetTimestamp(localData);
        var serverTimestamp = GetTimestamp(serverData);

        return localTimestamp > serverTimestamp ? localData : serverData;
    }

    private Dictionary<string, object> MergeData(Dictionary<string, object> localData, Dictionary<string, object> serverData)
    {
        var merged = new Dictionary<string, object>(serverData);
        
        foreach (var kvp in localData)
        {
            if (!merged.ContainsKey(kvp.Key) || IsLocalDataNewer(kvp.Value, merged.GetValueOrDefault(kvp.Key)))
            {
                merged[kvp.Key] = kvp.Value;
            }
        }

        return merged;
    }

    private DateTime GetTimestamp(Dictionary<string, object> data)
    {
        if (data.TryGetValue("timestamp", out var timestamp))
        {
            if (DateTime.TryParse(timestamp.ToString(), out var dt))
                return dt;
        }
        return DateTime.MinValue;
    }

    private bool IsLocalDataNewer(object localValue, object? serverValue)
    {
        // Simple implementation - in real scenario, this would be more sophisticated
        return true;
    }

    private bool ValidateTripUpdateData(Dictionary<string, object> data)
    {
        return data.ContainsKey("tripId") && data.ContainsKey("status") && data.ContainsKey("location");
    }

    private bool ValidatePODUploadData(Dictionary<string, object> data)
    {
        return data.ContainsKey("tripId") && data.ContainsKey("signature") && data.ContainsKey("photos");
    }

    private bool ValidateDocumentUploadData(Dictionary<string, object> data)
    {
        return data.ContainsKey("documentType") && data.ContainsKey("fileUrl") && data.ContainsKey("userId");
    }
}

public class SyncResult
{
    public Guid UserId { get; set; }
    public int TotalItems { get; set; }
    public int SuccessfulItems { get; set; }
    public int FailedItems { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
}

public class SyncItemResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ConflictResolutionResult
{
    public Guid DataId { get; set; }
    public string DataType { get; set; } = string.Empty;
    public bool ConflictDetected { get; set; }
    public bool IsResolved { get; set; }
    public string Resolution { get; set; } = string.Empty;
    public Dictionary<string, object> ResolvedData { get; set; } = new();
    public string? ErrorMessage { get; set; }
}
