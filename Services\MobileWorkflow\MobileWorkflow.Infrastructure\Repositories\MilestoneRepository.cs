using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Data;

namespace MobileWorkflow.Infrastructure.Repositories;

public class MilestoneTemplateRepository : IMilestoneTemplateRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MilestoneTemplateRepository> _logger;

    public MilestoneTemplateRepository(MobileWorkflowDbContext context, ILogger<MilestoneTemplateRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MilestoneTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<MilestoneTemplate?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .FirstOrDefaultAsync(t => t.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetByTypeAsync(string type, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.Type == type)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.Category == category)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.IsActive)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetDefaultTemplatesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.IsDefault && t.IsActive)
            .OrderBy(t => t.Type)
            .ThenBy(t => t.Category)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestoneTemplate?> GetDefaultTemplateByTypeAsync(string type, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .FirstOrDefaultAsync(t => t.Type == type && t.IsDefault && t.IsActive, cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetTemplatesByCreatorAsync(string createdBy, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.CreatedBy == createdBy)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestoneTemplate> AddAsync(MilestoneTemplate template, CancellationToken cancellationToken = default)
    {
        _context.MilestoneTemplates.Add(template);
        await _context.SaveChangesAsync(cancellationToken);
        return template;
    }

    public async Task<MilestoneTemplate> UpdateAsync(MilestoneTemplate template, CancellationToken cancellationToken = default)
    {
        _context.MilestoneTemplates.Update(template);
        await _context.SaveChangesAsync(cancellationToken);
        return template;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var template = await GetByIdAsync(id, cancellationToken);
        if (template != null)
        {
            _context.MilestoneTemplates.Remove(template);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates.AnyAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates.AnyAsync(t => t.Name == name, cancellationToken);
    }

    public async Task<IEnumerable<MilestoneTemplate>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.Name.Contains(searchTerm) || t.Description.Contains(searchTerm) || t.Type.Contains(searchTerm) || t.Category.Contains(searchTerm))
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetUsageCountAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var template = await _context.MilestoneTemplates
            .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);
        return template?.UsageCount ?? 0;
    }

    public async Task<IEnumerable<MilestoneTemplate>> GetMostUsedTemplatesAsync(int count, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneTemplates
            .Include(t => t.Steps)
                .ThenInclude(s => s.PayoutRules)
            .Include(t => t.RoleMappings)
            .Where(t => t.IsActive)
            .OrderByDescending(t => t.UsageCount)
            .Take(count)
            .ToListAsync(cancellationToken);
    }
}

public class MilestoneStepRepository : IMilestoneStepRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MilestoneStepRepository> _logger;

    public MilestoneStepRepository(MobileWorkflowDbContext context, ILogger<MilestoneStepRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MilestoneStep?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.MilestoneTemplate)
            .Include(s => s.PayoutRules)
            .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<MilestoneStep>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.PayoutRules)
            .Where(s => s.MilestoneTemplateId == templateId)
            .OrderBy(s => s.SequenceNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestoneStep>> GetActiveStepsByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.PayoutRules)
            .Where(s => s.MilestoneTemplateId == templateId && s.IsActive)
            .OrderBy(s => s.SequenceNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestoneStep?> GetByTemplateAndSequenceAsync(Guid templateId, int sequenceNumber, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.PayoutRules)
            .FirstOrDefaultAsync(s => s.MilestoneTemplateId == templateId && s.SequenceNumber == sequenceNumber, cancellationToken);
    }

    public async Task<IEnumerable<MilestoneStep>> GetRequiredStepsByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.PayoutRules)
            .Where(s => s.MilestoneTemplateId == templateId && s.IsRequired && s.IsActive)
            .OrderBy(s => s.SequenceNumber)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestoneStep> AddAsync(MilestoneStep step, CancellationToken cancellationToken = default)
    {
        _context.MilestoneSteps.Add(step);
        await _context.SaveChangesAsync(cancellationToken);
        return step;
    }

    public async Task<MilestoneStep> UpdateAsync(MilestoneStep step, CancellationToken cancellationToken = default)
    {
        _context.MilestoneSteps.Update(step);
        await _context.SaveChangesAsync(cancellationToken);
        return step;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var step = await GetByIdAsync(id, cancellationToken);
        if (step != null)
        {
            _context.MilestoneSteps.Remove(step);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps.AnyAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByTemplateAndSequenceAsync(Guid templateId, int sequenceNumber, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps.AnyAsync(s => s.MilestoneTemplateId == templateId && s.SequenceNumber == sequenceNumber, cancellationToken);
    }

    public async Task<int> GetMaxSequenceNumberAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        var maxSequence = await _context.MilestoneSteps
            .Where(s => s.MilestoneTemplateId == templateId)
            .MaxAsync(s => (int?)s.SequenceNumber, cancellationToken);
        return maxSequence ?? 0;
    }

    public async Task<IEnumerable<MilestoneStep>> GetStepsWithPayoutRulesAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestoneSteps
            .Include(s => s.PayoutRules.Where(r => r.IsActive))
            .Where(s => s.MilestoneTemplateId == templateId && s.PayoutRules.Any(r => r.IsActive))
            .OrderBy(s => s.SequenceNumber)
            .ToListAsync(cancellationToken);
    }
}

public class MilestonePayoutRuleRepository : IMilestonePayoutRuleRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<MilestonePayoutRuleRepository> _logger;

    public MilestonePayoutRuleRepository(MobileWorkflowDbContext context, ILogger<MilestonePayoutRuleRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MilestonePayoutRule?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Include(r => r.MilestoneStep)
                .ThenInclude(s => s.MilestoneTemplate)
            .FirstOrDefaultAsync(r => r.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<MilestonePayoutRule>> GetByStepIdAsync(Guid stepId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Include(r => r.MilestoneStep)
            .Where(r => r.MilestoneStepId == stepId)
            .OrderBy(r => r.PayoutPercentage)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestonePayoutRule>> GetActiveRulesByStepIdAsync(Guid stepId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Include(r => r.MilestoneStep)
            .Where(r => r.MilestoneStepId == stepId && r.IsActive)
            .OrderBy(r => r.PayoutPercentage)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<MilestonePayoutRule>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Include(r => r.MilestoneStep)
            .Where(r => r.MilestoneStep.MilestoneTemplateId == templateId)
            .OrderBy(r => r.MilestoneStep.SequenceNumber)
            .ThenBy(r => r.PayoutPercentage)
            .ToListAsync(cancellationToken);
    }

    public async Task<MilestonePayoutRule> AddAsync(MilestonePayoutRule rule, CancellationToken cancellationToken = default)
    {
        _context.MilestonePayoutRules.Add(rule);
        await _context.SaveChangesAsync(cancellationToken);
        return rule;
    }

    public async Task<MilestonePayoutRule> UpdateAsync(MilestonePayoutRule rule, CancellationToken cancellationToken = default)
    {
        _context.MilestonePayoutRules.Update(rule);
        await _context.SaveChangesAsync(cancellationToken);
        return rule;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var rule = await GetByIdAsync(id, cancellationToken);
        if (rule != null)
        {
            _context.MilestonePayoutRules.Remove(rule);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules.AnyAsync(r => r.Id == id, cancellationToken);
    }

    public async Task<decimal> GetTotalPayoutPercentageByTemplateAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Where(r => r.MilestoneStep.MilestoneTemplateId == templateId && r.IsActive)
            .SumAsync(r => r.PayoutPercentage, cancellationToken);
    }

    public async Task<decimal> GetTotalPayoutPercentageByStepAsync(Guid stepId, CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Where(r => r.MilestoneStepId == stepId && r.IsActive)
            .SumAsync(r => r.PayoutPercentage, cancellationToken);
    }

    public async Task<IEnumerable<MilestonePayoutRule>> GetRulesWithConditionsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.MilestonePayoutRules
            .Include(r => r.MilestoneStep)
                .ThenInclude(s => s.MilestoneTemplate)
            .Where(r => !string.IsNullOrEmpty(r.TriggerCondition) && r.IsActive)
            .ToListAsync(cancellationToken);
    }
}

public class RoleTemplateMappingsRepository : IRoleTemplateMappingsRepository
{
    private readonly MobileWorkflowDbContext _context;
    private readonly ILogger<RoleTemplateMappingsRepository> _logger;

    public RoleTemplateMappingsRepository(MobileWorkflowDbContext context, ILogger<RoleTemplateMappingsRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<RoleTemplateMappings?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
                .ThenInclude(t => t.Steps)
                    .ThenInclude(s => s.PayoutRules)
            .FirstOrDefaultAsync(rm => rm.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetByRoleNameAsync(string roleName, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
                .ThenInclude(t => t.Steps)
                    .ThenInclude(s => s.PayoutRules)
            .Where(rm => rm.RoleName == roleName)
            .OrderByDescending(rm => rm.Priority)
            .ThenBy(rm => rm.MilestoneTemplate.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetByTemplateIdAsync(Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
            .Where(rm => rm.MilestoneTemplateId == templateId)
            .OrderBy(rm => rm.RoleName)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetActiveByRoleNameAsync(string roleName, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
                .ThenInclude(t => t.Steps)
                    .ThenInclude(s => s.PayoutRules)
            .Where(rm => rm.RoleName == roleName && rm.IsActive && rm.MilestoneTemplate.IsActive)
            .OrderByDescending(rm => rm.Priority)
            .ThenBy(rm => rm.MilestoneTemplate.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<RoleTemplateMappings?> GetDefaultByRoleNameAsync(string roleName, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
                .ThenInclude(t => t.Steps)
                    .ThenInclude(s => s.PayoutRules)
            .FirstOrDefaultAsync(rm => rm.RoleName == roleName && rm.IsDefault && rm.IsActive && rm.MilestoneTemplate.IsActive, cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetDefaultMappingsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
                .ThenInclude(t => t.Steps)
                    .ThenInclude(s => s.PayoutRules)
            .Where(rm => rm.IsDefault && rm.IsActive && rm.MilestoneTemplate.IsActive)
            .OrderBy(rm => rm.RoleName)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetByPriorityAsync(int minPriority, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
            .Where(rm => rm.Priority >= minPriority && rm.IsActive)
            .OrderByDescending(rm => rm.Priority)
            .ThenBy(rm => rm.RoleName)
            .ToListAsync(cancellationToken);
    }

    public async Task<RoleTemplateMappings?> GetBestMatchAsync(string roleName, Dictionary<string, object> context, CancellationToken cancellationToken = default)
    {
        var mappings = await GetActiveByRoleNameAsync(roleName, cancellationToken);

        // Find the best match based on conditions and priority
        foreach (var mapping in mappings.OrderByDescending(m => m.Priority))
        {
            if (mapping.EvaluateConditions(context))
            {
                return mapping;
            }
        }

        // Return default mapping if no conditional match found
        return await GetDefaultByRoleNameAsync(roleName, cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
            .OrderBy(rm => rm.RoleName)
            .ThenByDescending(rm => rm.Priority)
            .ToListAsync(cancellationToken);
    }

    public async Task<RoleTemplateMappings> AddAsync(RoleTemplateMappings mapping, CancellationToken cancellationToken = default)
    {
        _context.RoleTemplateMappings.Add(mapping);
        await _context.SaveChangesAsync(cancellationToken);
        return mapping;
    }

    public async Task<RoleTemplateMappings> UpdateAsync(RoleTemplateMappings mapping, CancellationToken cancellationToken = default)
    {
        _context.RoleTemplateMappings.Update(mapping);
        await _context.SaveChangesAsync(cancellationToken);
        return mapping;
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var mapping = await GetByIdAsync(id, cancellationToken);
        if (mapping != null)
        {
            _context.RoleTemplateMappings.Remove(mapping);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings.AnyAsync(rm => rm.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByRoleAndTemplateAsync(string roleName, Guid templateId, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings.AnyAsync(rm => rm.RoleName == roleName && rm.MilestoneTemplateId == templateId, cancellationToken);
    }

    public async Task<IEnumerable<string>> GetDistinctRoleNamesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Where(rm => rm.IsActive)
            .Select(rm => rm.RoleName)
            .Distinct()
            .OrderBy(r => r)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RoleTemplateMappings>> GetMappingsByCreatorAsync(string createdBy, CancellationToken cancellationToken = default)
    {
        return await _context.RoleTemplateMappings
            .Include(rm => rm.MilestoneTemplate)
            .Where(rm => rm.CreatedBy == createdBy)
            .OrderByDescending(rm => rm.CreatedAt)
            .ToListAsync(cancellationToken);
    }
}
