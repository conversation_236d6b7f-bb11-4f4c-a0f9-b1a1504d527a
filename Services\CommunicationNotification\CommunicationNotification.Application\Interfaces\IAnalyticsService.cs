using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for communication analytics
/// </summary>
public interface IAnalyticsService
{
    /// <summary>
    /// Get message performance metrics for a specific period
    /// </summary>
    Task<MessagePerformanceMetrics> GetMessagePerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get channel performance comparison
    /// </summary>
    Task<List<ChannelPerformanceMetrics>> GetChannelPerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user engagement metrics
    /// </summary>
    Task<UserEngagementMetrics> GetUserEngagementAsync(
        Guid userId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get time-based analytics trends
    /// </summary>
    Task<List<TimeBasedMetrics>> GetTimeBasedAnalyticsAsync(
        DateTime startDate,
        DateTime endDate,
        TimePeriodType periodType,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get real-time analytics dashboard data
    /// </summary>
    Task<Dictionary<string, object>> GetRealTimeDashboardDataAsync(
        UserRole userRole,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cost analysis metrics
    /// </summary>
    Task<Dictionary<string, decimal>> GetCostAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        NotificationChannel? channel = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get A/B test performance comparison
    /// </summary>
    Task<Dictionary<string, MessagePerformanceMetrics>> GetABTestPerformanceAsync(
        string testId,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Track message analytics event
    /// </summary>
    Task TrackMessageEventAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get top performing message templates
    /// </summary>
    Task<List<(string TemplateId, MessagePerformanceMetrics Performance)>> GetTopPerformingTemplatesAsync(
        DateTime startDate,
        DateTime endDate,
        int limit = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user behavior insights
    /// </summary>
    Task<Dictionary<string, object>> GetUserBehaviorInsightsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for analytics dashboard management
/// </summary>
public interface IAnalyticsDashboardService
{
    /// <summary>
    /// Create a new analytics dashboard
    /// </summary>
    Task<AnalyticsDashboard> CreateDashboardAsync(
        string name,
        string description,
        Guid userId,
        UserRole userRole,
        DashboardType type,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboard by ID
    /// </summary>
    Task<AnalyticsDashboard?> GetDashboardAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboards for a user
    /// </summary>
    Task<List<AnalyticsDashboard>> GetUserDashboardsAsync(
        Guid userId,
        UserRole userRole,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update dashboard configuration
    /// </summary>
    Task<AnalyticsDashboard> UpdateDashboardAsync(
        Guid dashboardId,
        AnalyticsDashboard dashboard,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete dashboard
    /// </summary>
    Task DeleteDashboardAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get dashboard data with widgets populated
    /// </summary>
    Task<Dictionary<string, object>> GetDashboardDataAsync(
        Guid dashboardId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh dashboard data
    /// </summary>
    Task RefreshDashboardAsync(
        Guid dashboardId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get default dashboard for user role
    /// </summary>
    Task<AnalyticsDashboard?> GetDefaultDashboardAsync(
        UserRole userRole,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clone dashboard for another user
    /// </summary>
    Task<AnalyticsDashboard> CloneDashboardAsync(
        Guid sourceDashboardId,
        Guid targetUserId,
        string newName,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for real-time analytics
/// </summary>
public interface IRealTimeAnalyticsService
{
    /// <summary>
    /// Get current real-time metrics
    /// </summary>
    Task<Dictionary<string, object>> GetRealTimeMetricsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe to real-time analytics updates
    /// </summary>
    Task SubscribeToUpdatesAsync(
        Guid userId,
        string connectionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe from real-time analytics updates
    /// </summary>
    Task UnsubscribeFromUpdatesAsync(
        string connectionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Push real-time update to subscribers
    /// </summary>
    Task PushUpdateAsync(
        string eventType,
        object data,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active connections count
    /// </summary>
    Task<int> GetActiveConnectionsCountAsync(
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for message analytics
/// </summary>
public interface IMessageAnalyticsRepository
{
    /// <summary>
    /// Add message analytics record
    /// </summary>
    Task<MessageAnalytics> AddAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update message analytics record
    /// </summary>
    Task<MessageAnalytics> UpdateAsync(
        MessageAnalytics analytics,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get message analytics by message ID
    /// </summary>
    Task<MessageAnalytics?> GetByMessageIdAsync(
        Guid messageId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get analytics for date range
    /// </summary>
    Task<List<MessageAnalytics>> GetByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get aggregated metrics
    /// </summary>
    Task<MessagePerformanceMetrics> GetAggregatedMetricsAsync(
        DateTime startDate,
        DateTime endDate,
        MessageType? messageType = null,
        NotificationChannel? channel = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete old analytics records
    /// </summary>
    Task DeleteOldRecordsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}
