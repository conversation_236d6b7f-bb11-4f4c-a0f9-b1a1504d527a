using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Repositories;

/// <summary>
/// Repository interface for delay alerts
/// </summary>
public interface IDelayAlertRepository
{
    Task<DelayAlert?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetByTripIdAsync(Guid tripId, CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetByTransportCompanyIdAsync(
        Guid transportCompanyId, 
        CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetActiveAlertsByTransportCompanyAsync(
        Guid transportCompanyId, 
        CancellationToken cancellationToken = default);
    Task<DelayAlert?> GetActiveAlertForTripAsync(
        Guid tripId, 
        DelayAlertType alertType, 
        CancellationToken cancellationToken = default);
    Task<DelayAlert?> GetActiveAlertForTripStopAsync(
        Guid tripStopId, 
        DelayAlertType alertType, 
        CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetAlertsByStatusAsync(
        DelayAlertStatus status, 
        CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetAlertsBySeverityAsync(
        DelayAlertSeverity severity, 
        CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> SearchAsync(
        DelayAlertSearchCriteria criteria, 
        CancellationToken cancellationToken = default);
    Task AddAsync(DelayAlert delayAlert, CancellationToken cancellationToken = default);
    Task UpdateAsync(DelayAlert delayAlert, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<DelayAlertAnalytics> GetAnalyticsAsync(
        Guid transportCompanyId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);
    Task<int> CountActiveAlertsByTransportCompanyAsync(
        Guid transportCompanyId, 
        CancellationToken cancellationToken = default);
    Task<List<DelayAlert>> GetAlertsRequiringEscalationAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Search criteria for delay alerts
/// </summary>
public class DelayAlertSearchCriteria
{
    public Guid? TransportCompanyId { get; set; }
    public Guid? TripId { get; set; }
    public Guid? TripStopId { get; set; }
    public DelayAlertType? AlertType { get; set; }
    public DelayAlertSeverity? Severity { get; set; }
    public DelayAlertStatus? Status { get; set; }
    public DelayReason? DelayReason { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? ResolvedAfter { get; set; }
    public DateTime? ResolvedBefore { get; set; }
    public TimeSpan? MinDelayDuration { get; set; }
    public TimeSpan? MaxDelayDuration { get; set; }
    public int? MinEscalationLevel { get; set; }
    public int? MaxEscalationLevel { get; set; }
    public bool? IsAcknowledged { get; set; }
    public bool? IsResolved { get; set; }
    public bool? IsExpired { get; set; }
    public string? SearchTerm { get; set; } // Search in title, description
    public List<string> Tags { get; set; } = new();
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// Interface for delay alert notification service
/// </summary>
public interface IDelayAlertNotificationService
{
    Task SendDelayAlertCreatedNotificationAsync(
        DelayAlert delayAlert,
        CancellationToken cancellationToken = default);

    Task SendDelayAlertUpdatedNotificationAsync(
        DelayAlert delayAlert,
        CancellationToken cancellationToken = default);

    Task SendEscalationNotificationAsync(
        DelayAlert delayAlert,
        CancellationToken cancellationToken = default);

    Task SendDelayAlertAcknowledgedNotificationAsync(
        DelayAlert delayAlert,
        CancellationToken cancellationToken = default);

    Task SendDelayAlertResolvedNotificationAsync(
        DelayAlert delayAlert,
        CancellationToken cancellationToken = default);

    Task SendBulkDelayAlertSummaryAsync(
        Guid transportCompanyId,
        List<DelayAlert> alerts,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for transport company configuration service
/// </summary>
public interface ITransportCompanyConfigurationService
{
    Task<DelayAlertConfiguration> GetDelayAlertConfigurationAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);

    Task UpdateDelayAlertConfigurationAsync(
        Guid transportCompanyId,
        DelayAlertConfiguration configuration,
        CancellationToken cancellationToken = default);

    Task<bool> IsDelayAlertEnabledAsync(
        Guid transportCompanyId,
        CancellationToken cancellationToken = default);
}
