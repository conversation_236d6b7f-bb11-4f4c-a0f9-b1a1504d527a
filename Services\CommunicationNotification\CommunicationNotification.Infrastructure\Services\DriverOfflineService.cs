using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for handling driver offline capabilities
/// </summary>
public class DriverOfflineService : IDriverOfflineService
{
    private readonly IMemoryCache _cache;
    private readonly IOfflineMessageService _offlineMessageService;
    private readonly IConnectionTrackingService _connectionTracker;
    private readonly INotificationService _notificationService;
    private readonly ILogger<DriverOfflineService> _logger;
    private readonly DriverOfflineSettings _settings;

    public DriverOfflineService(
        IMemoryCache cache,
        IOfflineMessageService offlineMessageService,
        IConnectionTrackingService connectionTracker,
        INotificationService notificationService,
        IConfiguration configuration,
        ILogger<DriverOfflineService> logger)
    {
        _cache = cache;
        _offlineMessageService = offlineMessageService;
        _connectionTracker = connectionTracker;
        _notificationService = notificationService;
        _logger = logger;
        _settings = configuration.GetSection("DriverOffline").Get<DriverOfflineSettings>() ?? new();
    }

    public async Task<OfflineCapabilityResult> StoreOfflineDataAsync(
        Guid driverId,
        DriverOfflineData data,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"driver_offline_{driverId}";
            var existingData = _cache.Get<DriverOfflineData>(cacheKey);

            if (existingData != null)
            {
                // Merge with existing data
                data = MergeOfflineData(existingData, data);
            }

            // Store in cache with expiration
            _cache.Set(cacheKey, data, TimeSpan.FromHours(_settings.OfflineDataRetentionHours));

            _logger.LogDebug("Offline data stored for driver {DriverId}", driverId);

            return OfflineCapabilityResult.Success("Offline data stored successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store offline data for driver {DriverId}", driverId);
            return OfflineCapabilityResult.Failure($"Failed to store offline data: {ex.Message}");
        }
    }

    public async Task<DriverOfflineData?> GetOfflineDataAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"driver_offline_{driverId}";
            var data = _cache.Get<DriverOfflineData>(cacheKey);

            if (data != null)
            {
                _logger.LogDebug("Retrieved offline data for driver {DriverId}", driverId);
            }

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get offline data for driver {DriverId}", driverId);
            return null;
        }
    }

    public async Task<OfflineCapabilityResult> SyncOfflineDataAsync(
        Guid driverId,
        List<DriverOfflineAction> actions,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var syncResults = new List<OfflineSyncResult>();

            foreach (var action in actions)
            {
                var result = await ProcessOfflineActionAsync(driverId, action, cancellationToken);
                syncResults.Add(result);
            }

            // Clear offline data after successful sync
            var successfulActions = syncResults.Where(r => r.IsSuccess).Count();
            if (successfulActions == actions.Count)
            {
                await ClearOfflineDataAsync(driverId, cancellationToken);
            }

            _logger.LogInformation("Synced {SuccessCount}/{TotalCount} offline actions for driver {DriverId}",
                successfulActions, actions.Count, driverId);

            return OfflineCapabilityResult.Success(
                $"Synced {successfulActions}/{actions.Count} actions",
                syncResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync offline data for driver {DriverId}", driverId);
            return OfflineCapabilityResult.Failure($"Sync failed: {ex.Message}");
        }
    }

    public async Task<List<OfflineMessage>> GetPendingMessagesAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _offlineMessageService.GetOfflineMessagesAsync(driverId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get pending messages for driver {DriverId}", driverId);
            return new List<OfflineMessage>();
        }
    }

    public async Task<OfflineCapabilityResult> HandleDriverReconnectionAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Driver {DriverId} reconnected, processing offline data", driverId);

            // Get pending offline messages
            var pendingMessages = await GetPendingMessagesAsync(driverId, cancellationToken);
            
            // Get stored offline data
            var offlineData = await GetOfflineDataAsync(driverId, cancellationToken);

            var results = new List<string>();

            // Process pending messages
            if (pendingMessages.Any())
            {
                await DeliverPendingMessagesAsync(driverId, pendingMessages, cancellationToken);
                results.Add($"Delivered {pendingMessages.Count} pending messages");
            }

            // Process offline actions
            if (offlineData?.PendingActions.Any() == true)
            {
                var syncResult = await SyncOfflineDataAsync(driverId, offlineData.PendingActions, cancellationToken);
                results.Add($"Synced {offlineData.PendingActions.Count} offline actions");
            }

            // Send reconnection notification
            await SendReconnectionNotificationAsync(driverId, results, cancellationToken);

            return OfflineCapabilityResult.Success(
                "Driver reconnection processed successfully",
                results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle driver reconnection for {DriverId}", driverId);
            return OfflineCapabilityResult.Failure($"Reconnection handling failed: {ex.Message}");
        }
    }

    public async Task<DriverOfflineStatus> GetDriverOfflineStatusAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isOnline = await _connectionTracker.GetUserConnectionsAsync(driverId);
            var pendingMessages = await GetPendingMessagesAsync(driverId, cancellationToken);
            var offlineData = await GetOfflineDataAsync(driverId, cancellationToken);

            return new DriverOfflineStatus
            {
                DriverId = driverId,
                IsOnline = isOnline.Any(),
                LastSeen = DateTime.UtcNow, // This would come from connection tracking
                PendingMessageCount = pendingMessages.Count,
                OfflineDataSize = offlineData?.EstimatedSizeKB ?? 0,
                LastSyncTime = offlineData?.LastSyncTime,
                OfflineCapabilityEnabled = _settings.EnableOfflineCapability
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get offline status for driver {DriverId}", driverId);
            return new DriverOfflineStatus
            {
                DriverId = driverId,
                IsOnline = false,
                PendingMessageCount = 0,
                OfflineDataSize = 0
            };
        }
    }

    public async Task<OfflineCapabilityResult> ClearOfflineDataAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"driver_offline_{driverId}";
            _cache.Remove(cacheKey);

            _logger.LogDebug("Cleared offline data for driver {DriverId}", driverId);

            return OfflineCapabilityResult.Success("Offline data cleared successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear offline data for driver {DriverId}", driverId);
            return OfflineCapabilityResult.Failure($"Failed to clear offline data: {ex.Message}");
        }
    }

    private DriverOfflineData MergeOfflineData(DriverOfflineData existing, DriverOfflineData newData)
    {
        return new DriverOfflineData
        {
            DriverId = existing.DriverId,
            LastSyncTime = newData.LastSyncTime,
            PendingActions = existing.PendingActions.Concat(newData.PendingActions).ToList(),
            CachedData = existing.CachedData.Concat(newData.CachedData)
                .GroupBy(kvp => kvp.Key)
                .ToDictionary(g => g.Key, g => g.Last().Value), // Use latest value for duplicate keys
            EstimatedSizeKB = existing.EstimatedSizeKB + newData.EstimatedSizeKB
        };
    }

    private async Task<OfflineSyncResult> ProcessOfflineActionAsync(
        Guid driverId,
        DriverOfflineAction action,
        CancellationToken cancellationToken)
    {
        try
        {
            switch (action.ActionType)
            {
                case OfflineActionType.TripStatusUpdate:
                    return await ProcessTripStatusUpdateAsync(driverId, action, cancellationToken);
                
                case OfflineActionType.LocationUpdate:
                    return await ProcessLocationUpdateAsync(driverId, action, cancellationToken);
                
                case OfflineActionType.MessageSent:
                    return await ProcessMessageSentAsync(driverId, action, cancellationToken);
                
                case OfflineActionType.EmergencyAlert:
                    return await ProcessEmergencyAlertAsync(driverId, action, cancellationToken);
                
                default:
                    return OfflineSyncResult.Failure(action.Id, $"Unknown action type: {action.ActionType}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process offline action {ActionId} for driver {DriverId}", 
                action.Id, driverId);
            return OfflineSyncResult.Failure(action.Id, ex.Message);
        }
    }

    private async Task<OfflineSyncResult> ProcessTripStatusUpdateAsync(
        Guid driverId,
        DriverOfflineAction action,
        CancellationToken cancellationToken)
    {
        // This would integrate with the trip management service
        _logger.LogInformation("Processing trip status update for driver {DriverId}: {Data}", 
            driverId, action.Data);
        
        return OfflineSyncResult.Success(action.Id, "Trip status updated");
    }

    private async Task<OfflineSyncResult> ProcessLocationUpdateAsync(
        Guid driverId,
        DriverOfflineAction action,
        CancellationToken cancellationToken)
    {
        // This would integrate with the location tracking service
        _logger.LogInformation("Processing location update for driver {DriverId}: {Data}", 
            driverId, action.Data);
        
        return OfflineSyncResult.Success(action.Id, "Location updated");
    }

    private async Task<OfflineSyncResult> ProcessMessageSentAsync(
        Guid driverId,
        DriverOfflineAction action,
        CancellationToken cancellationToken)
    {
        // This would integrate with the messaging service
        _logger.LogInformation("Processing message sent for driver {DriverId}: {Data}", 
            driverId, action.Data);
        
        return OfflineSyncResult.Success(action.Id, "Message processed");
    }

    private async Task<OfflineSyncResult> ProcessEmergencyAlertAsync(
        Guid driverId,
        DriverOfflineAction action,
        CancellationToken cancellationToken)
    {
        // This would integrate with the emergency response service
        _logger.LogCritical("Processing emergency alert for driver {DriverId}: {Data}", 
            driverId, action.Data);
        
        return OfflineSyncResult.Success(action.Id, "Emergency alert processed");
    }

    private async Task DeliverPendingMessagesAsync(
        Guid driverId,
        List<OfflineMessage> messages,
        CancellationToken cancellationToken)
    {
        try
        {
            var messageIds = messages.Select(m => m.MessageId).ToList();
            await _offlineMessageService.MarkOfflineMessagesAsDeliveredAsync(driverId, messageIds, cancellationToken);
            
            _logger.LogInformation("Delivered {Count} pending messages to driver {DriverId}", 
                messages.Count, driverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deliver pending messages to driver {DriverId}", driverId);
        }
    }

    private async Task SendReconnectionNotificationAsync(
        Guid driverId,
        List<string> results,
        CancellationToken cancellationToken)
    {
        try
        {
            var content = MessageContent.Create(
                "Welcome Back",
                $"You're back online! {string.Join(", ", results)}",
                Language.English);

            var request = new NotificationRequest
            {
                UserId = driverId,
                MessageType = MessageType.SystemNotification,
                Content = content,
                Priority = Priority.Normal,
                PreferredChannel = NotificationChannel.Push
            };

            await _notificationService.SendNotificationAsync(request, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send reconnection notification to driver {DriverId}", driverId);
        }
    }
}

/// <summary>
/// Driver offline settings
/// </summary>
public class DriverOfflineSettings
{
    public bool EnableOfflineCapability { get; set; } = true;
    public int OfflineDataRetentionHours { get; set; } = 24;
    public int MaxOfflineActions { get; set; } = 1000;
    public int MaxOfflineDataSizeKB { get; set; } = 5120; // 5MB
    public bool AutoSyncOnReconnection { get; set; } = true;
    public int SyncRetryAttempts { get; set; } = 3;
    public TimeSpan SyncRetryDelay { get; set; } = TimeSpan.FromSeconds(10);
}
