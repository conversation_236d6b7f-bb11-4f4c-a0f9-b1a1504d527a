# TLI Microservices - Database Setup Guide

This guide provides comprehensive instructions for setting up databases, running migrations, and seeding data for the TLI Microservices platform.

## 📋 Prerequisites

### Required Software
- **PostgreSQL 15+** - Database server
- **.NET 8 SDK** - For running migrations
- **Entity Framework Tools** - For database operations

### Installation Commands

#### Windows (using Chocolatey)
```powershell
# Install PostgreSQL
choco install postgresql

# Install .NET 8 SDK
choco install dotnet-8.0-sdk

# Install EF Tools
dotnet tool install --global dotnet-ef
```

#### macOS (using Homebrew)
```bash
# Install PostgreSQL
brew install postgresql@15

# Install .NET 8 SDK
brew install dotnet

# Install EF Tools
dotnet tool install --global dotnet-ef
```

#### Ubuntu/Debian
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Install .NET 8 SDK
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install dotnet-sdk-8.0

# Install EF Tools
dotnet tool install --global dotnet-ef
```

## 🗄️ Database Setup

### Step 1: Start PostgreSQL Service

#### Windows
```powershell
# Start PostgreSQL service
net start postgresql-x64-15

# Or using Services.msc GUI
```

#### macOS/Linux
```bash
# Start PostgreSQL service
sudo systemctl start postgresql

# Enable auto-start
sudo systemctl enable postgresql
```

### Step 2: Create Databases and User

Run the database setup script as PostgreSQL superuser:

```bash
# Connect as postgres superuser
psql -U postgres -h localhost

# Run the setup script
\i Scripts/setup-databases.sql

# Or run directly
psql -U postgres -h localhost -f Scripts/setup-databases.sql
```

This script will:
- Create all required databases for microservices
- Create the `timescale` user with password `timescale`
- Grant necessary permissions
- Install required PostgreSQL extensions

### Step 3: Verify Database Setup

```sql
-- Connect as timescale user
psql -U timescale -h localhost -d TLI_DataStorage

-- List databases
\l

-- Check permissions
\dp
```

## 🔄 Running Migrations

### Automated Migration (Recommended)

#### Windows (PowerShell)
```powershell
# Run migrations for all services
.\Scripts\run-migrations.ps1

# Run migrations for specific services
.\Scripts\run-migrations.ps1 -Services @("DataStorage", "MonitoringObservability")

# Run without seeding data
.\Scripts\run-migrations.ps1 -SeedData:$false

# Force continue on errors
.\Scripts\run-migrations.ps1 -Force
```

#### Linux/macOS (Bash)
```bash
# Make script executable (Linux/macOS only)
chmod +x Scripts/run-migrations.sh

# Run migrations for all services
./Scripts/run-migrations.sh

# Run migrations for specific services
./Scripts/run-migrations.sh --services "DataStorage,MonitoringObservability"

# Run without seeding data
./Scripts/run-migrations.sh --no-seed

# Force continue on errors
./Scripts/run-migrations.sh --force
```

### Manual Migration

If you prefer to run migrations manually:

#### Data Storage Service
```bash
cd Services/DataStorage/DataStorage.API

# Create migration (if needed)
dotnet ef migrations add InitialCreate \
  --context DataStorageDbContext \
  --project ../DataStorage.Infrastructure \
  --startup-project DataStorage.API.csproj

# Apply migration
dotnet ef database update \
  --context DataStorageDbContext \
  --project ../DataStorage.Infrastructure \
  --startup-project DataStorage.API.csproj
```

#### Monitoring & Observability Service
```bash
cd Services/MonitoringObservability/MonitoringObservability.API

# Create migration (if needed)
dotnet ef migrations add InitialCreate \
  --context MonitoringDbContext \
  --project ../MonitoringObservability.Infrastructure \
  --startup-project MonitoringObservability.API.csproj

# Apply migration
dotnet ef database update \
  --context MonitoringDbContext \
  --project ../MonitoringObservability.Infrastructure \
  --startup-project MonitoringObservability.API.csproj
```

## 🌱 Data Seeding

### Automatic Seeding

Data seeding occurs automatically when services start with the `SEED_DATA=true` environment variable.

#### Docker Compose
```yaml
# Already configured in docker-compose.yml
environment:
  - SEED_DATA=true
```

#### Manual Service Start
```bash
# Set environment variable
export SEED_DATA=true  # Linux/macOS
$env:SEED_DATA="true"  # Windows PowerShell

# Start service
dotnet run --project Services/DataStorage/DataStorage.API
```

### Seed Data Contents

#### Data Storage Service
- **Sample Documents**: E-way bills, invoices, transport contracts
- **Shipper Documents**: Compliance and financial documents
- **Carrier Documents**: Pickup photos, delivery photos, POD
- **System Documents**: Backups, audit reports
- **Document Permissions**: Role-based access control
- **Sample Users**: Admin, Shipper, Carrier, Broker roles

#### Monitoring & Observability Service
- **Metrics**: Performance, reliability, infrastructure metrics for all services
- **Health Checks**: HTTP health check configurations for all services
- **Sample Incidents**: High response time, database connection issues
- **Sample Alerts**: CPU usage warnings, service unavailable alerts
- **Metric Data Points**: Historical data for the last hour
- **Sample Users**: Admin and monitoring users

## 🔍 Verification

### Check Database Tables

```sql
-- Connect to Data Storage database
psql -U timescale -h localhost -d TLI_DataStorage

-- List tables
\dt

-- Check document count
SELECT COUNT(*) FROM "Documents";

-- Check permissions
SELECT COUNT(*) FROM "DocumentPermissions";
```

```sql
-- Connect to Monitoring database
psql -U timescale -h localhost -d TLI_MonitoringObservability

-- List tables
\dt

-- Check metrics count
SELECT COUNT(*) FROM "Metrics";

-- Check data points
SELECT COUNT(*) FROM "MetricDataPoints";
```

### Test Service Health

```bash
# Test Data Storage service
curl http://localhost:5010/health

# Test Monitoring service
curl http://localhost:5011/health

# Test through API Gateway
curl http://localhost:5000/health/datastorage
curl http://localhost:5000/health/monitoring
```

## 🚀 Starting Services

### Using Docker Compose (Recommended)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f datastorage-api
docker-compose logs -f monitoring-api
```

### Manual Service Start
```bash
# Start Data Storage service
cd Services/DataStorage/DataStorage.API
dotnet run

# Start Monitoring service (in another terminal)
cd Services/MonitoringObservability/MonitoringObservability.API
dotnet run
```

## 🔧 Troubleshooting

### Common Issues

#### PostgreSQL Connection Failed
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql  # Linux
net start postgresql-x64-15      # Windows

# Check connection
psql -U timescale -h localhost -d postgres
```

#### Migration Failed
```bash
# Reset database (WARNING: This will delete all data)
dotnet ef database drop --force --context DataStorageDbContext
dotnet ef database update --context DataStorageDbContext

# Check migration status
dotnet ef migrations list --context DataStorageDbContext
```

#### Permission Denied
```sql
-- Grant permissions manually
GRANT ALL PRIVILEGES ON DATABASE "TLI_DataStorage" TO timescale;
GRANT ALL ON SCHEMA public TO timescale;
```

### Logs and Debugging

#### Enable Detailed Logging
```json
// In appsettings.Development.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
```

#### Check Service Logs
```bash
# Docker logs
docker-compose logs datastorage-api
docker-compose logs monitoring-api

# Application logs (when running manually)
# Logs are written to console and configured log files
```

## 📊 Database Schema

### Data Storage Service Tables
- `Documents` - Main document storage
- `DocumentPermissions` - Access control
- `DocumentAccessLogs` - Audit trail
- `DocumentShares` - Public sharing

### Monitoring Service Tables
- `Metrics` - Metric definitions
- `MetricDataPoints` - Time-series data
- `HealthChecks` - Health check configurations
- `Alerts` - Alert management
- `Incidents` - Incident tracking

## 🔒 Security Considerations

- Database user `timescale` has limited privileges
- All connections use encrypted passwords
- Document access is role-based
- Audit logs track all document access
- Health check endpoints are publicly accessible
- Admin endpoints require authentication

## 📈 Performance Optimization

### Database Indexes
- All foreign keys are indexed
- Timestamp columns have indexes for time-series queries
- Composite indexes for common query patterns
- JSONB columns use GIN indexes

### Connection Pooling
- Configured in connection strings
- Default pool size: 100 connections
- Connection timeout: 30 seconds

## 🔄 Backup and Recovery

### Database Backup
```bash
# Backup all databases
pg_dumpall -U postgres -h localhost > tli_backup.sql

# Backup specific database
pg_dump -U timescale -h localhost TLI_DataStorage > datastorage_backup.sql
```

### Restore Database
```bash
# Restore all databases
psql -U postgres -h localhost < tli_backup.sql

# Restore specific database
psql -U timescale -h localhost TLI_DataStorage < datastorage_backup.sql
```

---

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review service logs for error details
3. Verify database connectivity and permissions
4. Ensure all prerequisites are installed correctly
