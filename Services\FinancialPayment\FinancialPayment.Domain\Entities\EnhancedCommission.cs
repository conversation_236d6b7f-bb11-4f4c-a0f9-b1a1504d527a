using FinancialPayment.Domain.Enums;
using Shared.Domain.ValueObjects;
using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;


namespace FinancialPayment.Domain.Entities;

/// <summary>
/// Enhanced commission entity that includes comprehensive tax calculations
/// </summary>
public class EnhancedCommission : Commission
{
    public CommissionTaxDetails TaxDetails { get; private set; }
    public bool HasTaxCalculation { get; private set; }
    public DateTime? TaxCalculatedAt { get; private set; }
    public string? TaxCalculationNotes { get; private set; }

    private EnhancedCommission() : base() { }

    public EnhancedCommission(
        Commission baseCommission,
        CommissionTaxDetails taxDetails)
        : base(
            baseCommission.OrderId,
            baseCommission.BrokerId,
            baseCommission.TransportCompanyId,
            baseCommission.CarrierId,
            baseCommission.OrderAmount,
            baseCommission.CommissionStructure)
    {
        if (taxDetails == null)
            throw new ArgumentNullException(nameof(taxDetails));

        TaxDetails = new CommissionTaxDetails(
            taxDetails.GstAmount,
            taxDetails.TdsAmount,
            taxDetails.TotalTaxAmount,
            taxDetails.NetAmount,
            taxDetails.EffectiveGstRate,
            taxDetails.EffectiveTdsRate,
            taxDetails.IsReverseChargeApplicable,
            taxDetails.AppliedHsnCode,
            taxDetails.AppliedRules.ToList(),
            taxDetails.Warnings.ToList());

        HasTaxCalculation = true;
        TaxCalculatedAt = taxDetails.CalculatedAt;
        TaxCalculationNotes = string.Join("; ", taxDetails.AppliedRules);

        // Update the calculated amount to reflect net payable amount
        UpdateCalculatedAmountWithTax();
    }

    public EnhancedCommission(
        Guid orderId,
        Guid brokerId,
        Guid transportCompanyId,
        Guid carrierId,
        Money orderAmount,
        CommissionStructure commissionStructure,
        CommissionTaxDetails taxDetails)
        : base(orderId, brokerId, transportCompanyId, carrierId, orderAmount, commissionStructure)
    {
        if (taxDetails == null)
            throw new ArgumentNullException(nameof(taxDetails));

        TaxDetails = new CommissionTaxDetails(
            taxDetails.GstAmount,
            taxDetails.TdsAmount,
            taxDetails.TotalTaxAmount,
            taxDetails.NetAmount,
            taxDetails.EffectiveGstRate,
            taxDetails.EffectiveTdsRate,
            taxDetails.IsReverseChargeApplicable,
            taxDetails.AppliedHsnCode,
            taxDetails.AppliedRules.ToList(),
            taxDetails.Warnings.ToList());

        HasTaxCalculation = true;
        TaxCalculatedAt = taxDetails.CalculatedAt;
        TaxCalculationNotes = string.Join("; ", taxDetails.AppliedRules);

        UpdateCalculatedAmountWithTax();
    }

    public void RecalculateTax(CommissionTaxDetails newTaxCalculationResult, string recalculatedBy)
    {
        if (Status == CommissionStatus.Paid)
            throw new InvalidOperationException("Cannot recalculate tax for paid commission");

        if (newTaxCalculationResult == null)
            throw new ArgumentNullException(nameof(newTaxCalculationResult));

        TaxDetails = new CommissionTaxDetails(
            newTaxCalculationResult.GstAmount,
            newTaxCalculationResult.TdsAmount,
            newTaxCalculationResult.TotalTaxAmount,
            newTaxCalculationResult.NetAmount,
            newTaxCalculationResult.EffectiveGstRate,
            newTaxCalculationResult.EffectiveTdsRate,
            newTaxCalculationResult.IsReverseChargeApplicable,
            newTaxCalculationResult.AppliedHsnCode,
            newTaxCalculationResult.AppliedRules.ToList(),
            newTaxCalculationResult.Warnings.ToList());

        TaxCalculatedAt = newTaxCalculationResult.CalculatedAt;
        TaxCalculationNotes = $"Recalculated by {recalculatedBy}: {string.Join("; ", newTaxCalculationResult.AppliedRules)}";

        UpdateCalculatedAmountWithTax();
        SetUpdatedAt();

        AddDomainEvent(new CommissionTaxRecalculatedEvent(Id, OrderId, BrokerId, TaxDetails));
    }

    public Money GetGrossCommissionAmount()
    {
        return base.CalculatedAmount;
    }

    public Money GetNetPayableAmount()
    {
        return TaxDetails.NetPayableAmount;
    }

    public Money GetTotalTaxAmount()
    {
        return TaxDetails.TotalTaxAmount;
    }

    public bool IsReverseChargeApplicable()
    {
        return TaxDetails.IsReverseChargeApplicable;
    }

    public List<string> GetTaxWarnings()
    {
        return TaxDetails.TaxWarnings.ToList();
    }

    public string GetTaxSummary()
    {
        return $"Gross: {GetGrossCommissionAmount()}, " +
               $"GST: {TaxDetails.GstAmount} ({TaxDetails.EffectiveGstRate:F2}%), " +
               $"TDS: {TaxDetails.TdsAmount} ({TaxDetails.EffectiveTdsRate:F2}%), " +
               $"Net: {GetNetPayableAmount()}";
    }

    private void UpdateCalculatedAmountWithTax()
    {
        // The base CalculatedAmount remains the gross commission
        // Net payable amount is stored in TaxDetails
        // This maintains backward compatibility while adding tax functionality
    }
}

/// <summary>
/// Value object containing comprehensive tax details for commission
/// </summary>
public class CommissionTaxDetails : ValueObject
{
    public Money GstAmount { get; private set; }
    public Money TdsAmount { get; private set; }
    public Money TotalTaxAmount { get; private set; }
    public Money NetPayableAmount { get; private set; }
    public decimal EffectiveGstRate { get; private set; }
    public decimal EffectiveTdsRate { get; private set; }
    public bool IsReverseChargeApplicable { get; private set; }
    public string? AppliedHsnCode { get; private set; }
    public IReadOnlyList<string> AppliedTaxRules { get; private set; }
    public IReadOnlyList<string> TaxWarnings { get; private set; }
    public DateTime CalculatedAt { get; private set; }

    // Compatibility properties for existing code
    public Money NetAmount => NetPayableAmount;
    public IReadOnlyList<string> AppliedRules => AppliedTaxRules;
    public IReadOnlyList<string> Warnings => TaxWarnings;

    private CommissionTaxDetails() { }

    public CommissionTaxDetails(
        Money gstAmount,
        Money tdsAmount,
        Money totalTaxAmount,
        Money netPayableAmount,
        decimal effectiveGstRate,
        decimal effectiveTdsRate,
        bool isReverseChargeApplicable,
        string? appliedHsnCode,
        List<string> appliedTaxRules,
        List<string> taxWarnings,
        DateTime? calculatedAt = null)
    {
        if (gstAmount.Currency != tdsAmount.Currency ||
            gstAmount.Currency != totalTaxAmount.Currency ||
            gstAmount.Currency != netPayableAmount.Currency)
        {
            throw new ArgumentException("All tax amounts must have the same currency");
        }

        if (effectiveGstRate < 0 || effectiveGstRate > 100)
            throw new ArgumentException("GST rate must be between 0 and 100", nameof(effectiveGstRate));

        if (effectiveTdsRate < 0 || effectiveTdsRate > 100)
            throw new ArgumentException("TDS rate must be between 0 and 100", nameof(effectiveTdsRate));

        GstAmount = gstAmount;
        TdsAmount = tdsAmount;
        TotalTaxAmount = totalTaxAmount;
        NetPayableAmount = netPayableAmount;
        EffectiveGstRate = effectiveGstRate;
        EffectiveTdsRate = effectiveTdsRate;
        IsReverseChargeApplicable = isReverseChargeApplicable;
        AppliedHsnCode = appliedHsnCode;
        AppliedTaxRules = appliedTaxRules?.AsReadOnly() ?? new List<string>().AsReadOnly();
        TaxWarnings = taxWarnings?.AsReadOnly() ?? new List<string>().AsReadOnly();
        CalculatedAt = calculatedAt ?? DateTime.UtcNow;
    }

    public bool HasTaxWarnings => TaxWarnings.Any();

    public bool HasGst => GstAmount.Amount > 0;

    public bool HasTds => TdsAmount.Amount > 0;

    public decimal GetTotalTaxRate()
    {
        return EffectiveGstRate + EffectiveTdsRate;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return GstAmount;
        yield return TdsAmount;
        yield return TotalTaxAmount;
        yield return NetPayableAmount;
        yield return EffectiveGstRate;
        yield return EffectiveTdsRate;
        yield return IsReverseChargeApplicable;
        yield return AppliedHsnCode ?? string.Empty;
        yield return string.Join(",", AppliedTaxRules);
        yield return string.Join(",", TaxWarnings);
    }
}

/// <summary>
/// Domain event for commission tax recalculation
/// </summary>
public class CommissionTaxRecalculatedEvent : DomainEvent
{
    public Guid CommissionId { get; }
    public Guid OrderId { get; }
    public Guid BrokerId { get; }
    public CommissionTaxDetails TaxDetails { get; }

    public CommissionTaxRecalculatedEvent(Guid commissionId, Guid orderId, Guid brokerId, CommissionTaxDetails taxDetails)
    {
        CommissionId = commissionId;
        OrderId = orderId;
        BrokerId = brokerId;
        TaxDetails = taxDetails;
    }
}


