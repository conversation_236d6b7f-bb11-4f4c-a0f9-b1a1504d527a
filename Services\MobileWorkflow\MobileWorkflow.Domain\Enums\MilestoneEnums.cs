namespace MobileWorkflow.Domain.Enums;

public enum MilestoneTemplateType
{
    Trip = 1,
    Order = 2,
    Project = 3,
    Delivery = 4,
    Pickup = 5,
    Custom = 99
}

public enum MilestoneTemplateCategory
{
    Logistics = 1,
    Transportation = 2,
    Delivery = 3,
    Pickup = 4,
    Documentation = 5,
    Payment = 6,
    Compliance = 7,
    Quality = 8,
    Custom = 99
}

public enum MilestoneStepStatus
{
    NotStarted = 1,
    InProgress = 2,
    Completed = 3,
    Skipped = 4,
    Failed = 5,
    Cancelled = 6
}

public enum PayoutTriggerType
{
    Automatic = 1,
    Manual = 2,
    Conditional = 3,
    TimeBasedDelay = 4,
    ApprovalRequired = 5
}

public enum RoleMappingPriority
{
    Low = 1,
    Normal = 100,
    High = 200,
    Critical = 300
}
