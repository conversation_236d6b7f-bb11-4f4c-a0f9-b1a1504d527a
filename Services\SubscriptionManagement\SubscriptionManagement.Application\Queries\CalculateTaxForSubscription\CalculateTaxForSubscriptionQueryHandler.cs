using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Queries.CalculateTaxForSubscription
{
    public class CalculateTaxForSubscriptionQueryHandler : IRequestHandler<CalculateTaxForSubscriptionQuery, TaxCalculationResponseDto>
    {
        private readonly IPlanRepository _planRepository;
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly ITaxExemptionRepository _taxExemptionRepository;
        private readonly ILogger<CalculateTaxForSubscriptionQueryHandler> _logger;

        public CalculateTaxForSubscriptionQueryHandler(
            IPlanRepository planRepository,
            ITaxCalculationService taxCalculationService,
            ITaxExemptionRepository taxExemptionRepository,
            ILogger<CalculateTaxForSubscriptionQueryHandler> logger)
        {
            _planRepository = planRepository;
            _taxCalculationService = taxCalculationService;
            _taxExemptionRepository = taxExemptionRepository;
            _logger = logger;
        }

        public async Task<TaxCalculationResponseDto> Handle(CalculateTaxForSubscriptionQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Calculating tax for plan {PlanId} in region {Region} for customer {CustomerId}",
                request.PlanId, request.CustomerRegion, request.CustomerId);

            try
            {
                Money baseAmount;
                
                if (request.CustomAmount.HasValue && !string.IsNullOrEmpty(request.CustomCurrency))
                {
                    // Use custom amount for calculation
                    baseAmount = Money.Create(request.CustomAmount.Value, request.CustomCurrency);
                }
                else
                {
                    // Get the plan and use its price
                    var plan = await _planRepository.GetByIdAsync(request.PlanId);
                    if (plan == null)
                    {
                        throw new SubscriptionDomainException($"Plan with ID {request.PlanId} not found");
                    }

                    if (!plan.IsActive)
                    {
                        throw new SubscriptionDomainException("Cannot calculate tax for inactive plan");
                    }

                    baseAmount = plan.Price;
                }

                var calculationDate = request.CalculationDate ?? DateTime.UtcNow;

                // Get tax breakdown
                var taxBreakdown = await _taxCalculationService.GetTaxBreakdownAsync(
                    baseAmount, request.CustomerRegion, request.CustomerId, calculationDate);

                // Check for exemptions
                var hasExemptions = false;
                if (request.CustomerId.HasValue)
                {
                    var exemptions = await _taxExemptionRepository.GetActiveByUserIdAsync(request.CustomerId.Value);
                    hasExemptions = exemptions.Any(e => 
                        e.ApplicableRegions.Contains(request.CustomerRegion.ToUpperInvariant()) &&
                        e.IsValidOn(calculationDate));
                }

                var result = new TaxCalculationResponseDto
                {
                    BaseAmount = taxBreakdown.BaseAmount.Amount,
                    Currency = taxBreakdown.BaseAmount.Currency,
                    TaxItems = request.IncludeTaxBreakdown ? taxBreakdown.TaxItems.Select(item => new TaxBreakdownItemDto
                    {
                        TaxType = item.TaxType,
                        TaxName = item.TaxName,
                        Rate = item.Rate,
                        TaxableAmount = item.TaxableAmount.Amount,
                        TaxAmount = item.TaxAmount.Amount,
                        IsExempt = item.IsExempt,
                        ExemptionReason = item.ExemptionReason
                    }).ToList() : new List<TaxBreakdownItemDto>(),
                    TotalTaxAmount = taxBreakdown.TotalTaxAmount.Amount,
                    TotalAmount = taxBreakdown.TotalAmount.Amount,
                    IsTaxInclusive = taxBreakdown.IsTaxInclusive,
                    CustomerRegion = request.CustomerRegion,
                    CalculatedAt = calculationDate,
                    HasExemptions = hasExemptions
                };

                _logger.LogInformation("Successfully calculated tax for plan {PlanId}. Total tax: {TotalTax} {Currency}",
                    request.PlanId, result.TotalTaxAmount, result.Currency);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating tax for plan {PlanId}", request.PlanId);
                throw;
            }
        }
    }
}
