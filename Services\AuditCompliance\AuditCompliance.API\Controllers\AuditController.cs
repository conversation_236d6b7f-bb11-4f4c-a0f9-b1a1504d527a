using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using AuditCompliance.Application.Commands;
using AuditCompliance.Application.Queries;
using AuditCompliance.Application.DTOs;

namespace AuditCompliance.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Route("api/[controller]")] // Backward compatibility
[ApiVersion("1.0")]
[Authorize]
public class AuditController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuditController> _logger;

    public AuditController(IMediator mediator, ILogger<AuditController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a new audit log entry
    /// </summary>
    [HttpPost("logs")]
    [Authorize(Roles = "Admin,System")]
    public async Task<ActionResult<Guid>> CreateAuditLog([FromBody] CreateAuditLogCommand command)
    {
        try
        {
            var auditLogId = await _mediator.Send(command);
            return Ok(new { AuditLogId = auditLogId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit log");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get audit log by ID
    /// </summary>
    [HttpGet("logs/{id}")]
    [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
    public async Task<ActionResult<AuditLogDto>> GetAuditLog(Guid id)
    {
        try
        {
            var query = new GetAuditLogByIdQuery { Id = id };
            var auditLog = await _mediator.Send(query);

            if (auditLog == null)
                return NotFound();

            return Ok(auditLog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit log: {AuditLogId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get audit trail with filtering and pagination
    /// </summary>
    [HttpPost("trail")]
    [Authorize(Roles = "Admin,Auditor,ComplianceOfficer")]
    public async Task<ActionResult<AuditTrailResultDto>> GetAuditTrail([FromBody] GetAuditTrailQuery query)
    {
        try
        {
            // Only admins can access sensitive data
            if (!User.IsInRole("Admin"))
                query.IncludeSensitiveData = false;

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit trail");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get security events for a date range
    /// </summary>
    [HttpGet("security-events")]
    [Authorize(Roles = "Admin,SecurityOfficer")]
    public async Task<ActionResult<List<AuditLogDto>>> GetSecurityEvents(
        [FromQuery] DateTime fromDate,
        [FromQuery] DateTime toDate)
    {
        try
        {
            var query = new GetSecurityEventsQuery
            {
                FromDate = fromDate,
                ToDate = toDate
            };

            var securityEvents = await _mediator.Send(query);
            return Ok(securityEvents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving security events");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Cleanup expired audit logs
    /// </summary>
    [HttpPost("cleanup")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<int>> CleanupExpiredAuditLogs()
    {
        try
        {
            var command = new CleanupExpiredAuditLogsCommand();
            var deletedCount = await _mediator.Send(command);

            return Ok(new { DeletedCount = deletedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired audit logs");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Export comprehensive admin audit trail with advanced filtering and security controls
    /// </summary>
    [HttpPost("admin/export")]
    [Authorize(Roles = "Admin,Auditor")]
    public async Task<ActionResult<AdminAuditExportResponseDto>> ExportAdminAuditTrail([FromBody] AdminAuditExportRequestDto request)
    {
        try
        {
            request.RequestedBy = GetCurrentUserId();
            request.RequestedByRole = GetCurrentUserRole();
            request.IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            request.UserAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            var command = new ExportAdminAuditTrailCommand
            {
                UserIds = request.UserIds,
                ActionTypes = request.ActionTypes,
                EntityTypes = request.EntityTypes,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                SeverityLevels = request.SeverityLevels,
                IpAddresses = request.IpAddresses,
                IncludeSystemActions = request.IncludeSystemActions,
                IncludeSensitiveData = request.IncludeSensitiveData,
                Format = request.Format,
                SelectedColumns = request.SelectedColumns,
                MaxRecords = request.MaxRecords,
                RequestedBy = request.RequestedBy,
                RequestedByRole = request.RequestedByRole,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting admin audit trail");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available audit trail export options and metadata
    /// </summary>
    [HttpGet("admin/export/options")]
    [Authorize(Roles = "Admin,Auditor")]
    public ActionResult<object> GetAuditTrailExportOptions()
    {
        try
        {
            var options = new
            {
                ActionTypes = new[]
                {
                    new { Value = "Create", Description = "Entity creation actions" },
                    new { Value = "Update", Description = "Entity modification actions" },
                    new { Value = "Delete", Description = "Entity deletion actions" },
                    new { Value = "Login", Description = "User authentication actions" },
                    new { Value = "Logout", Description = "User logout actions" },
                    new { Value = "Approve", Description = "Approval actions" },
                    new { Value = "Reject", Description = "Rejection actions" },
                    new { Value = "Export", Description = "Data export actions" },
                    new { Value = "Import", Description = "Data import actions" },
                    new { Value = "View", Description = "Data access actions" },
                    new { Value = "Search", Description = "Search operations" },
                    new { Value = "Download", Description = "File download actions" }
                },
                EntityTypes = new[]
                {
                    "User", "Order", "RFQ", "Trip", "Document", "Rating", "Payment",
                    "Subscription", "Vehicle", "Driver", "Report", "Configuration"
                },
                SeverityLevels = new[]
                {
                    new { Value = "Low", Description = "Routine operations" },
                    new { Value = "Medium", Description = "Important business operations" },
                    new { Value = "High", Description = "Critical security operations" },
                    new { Value = "Critical", Description = "System-level security events" }
                },
                ExportFormats = new[] { "CSV", "Excel", "JSON", "PDF" },
                MaxRecords = 500000,
                MaxDateRange = "365 days",
                SecurityNotice = "Audit trail exports are logged and monitored for compliance purposes"
            };

            return Ok(options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit trail export options");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get audit trail statistics for the specified period
    /// </summary>
    [HttpGet("admin/statistics")]
    [Authorize(Roles = "Admin,Auditor")]
    public async Task<ActionResult<object>> GetAuditTrailStatistics(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var query = new GetAuditTrailStatisticsQuery
            {
                FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
                ToDate = toDate ?? DateTime.UtcNow
            };

            var statistics = await _mediator.Send(query);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit trail statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("id");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId)
            ? userId
            : Guid.Empty;
    }

    private string GetCurrentUserRole()
    {
        return User.FindFirst("role")?.Value ??
               User.FindFirst("http://schemas.microsoft.com/ws/2008/06/identity/claims/role")?.Value ??
               string.Empty;
    }
}
