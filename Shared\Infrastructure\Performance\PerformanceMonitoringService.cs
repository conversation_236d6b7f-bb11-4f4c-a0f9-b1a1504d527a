using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace Shared.Infrastructure.Performance;

/// <summary>
/// Comprehensive performance monitoring service for tracking application metrics
/// Provides real-time performance data collection, analysis, and alerting
/// </summary>
public interface IPerformanceMonitoringService
{
    Task<string> StartOperationAsync(string operationName, Dictionary<string, object>? metadata = null);
    Task EndOperationAsync(string operationId, bool success = true, string? errorMessage = null);
    Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? tags = null);
    Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? tags = null);
    Task<PerformanceReport> GenerateReportAsync(TimeSpan period, CancellationToken cancellationToken = default);
    Task<List<PerformanceAlert>> CheckAlertsAsync(CancellationToken cancellationToken = default);
    Task<HealthCheckResult> GetHealthStatusAsync(CancellationToken cancellationToken = default);
}

public class PerformanceMonitoringService : IPerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly PerformanceConfiguration _config;
    private readonly ConcurrentDictionary<string, OperationTracker> _activeOperations = new();
    private readonly ConcurrentDictionary<string, MetricCollector> _metrics = new();
    private readonly ConcurrentDictionary<string, CounterCollector> _counters = new();
    private readonly List<PerformanceAlert> _alerts = new();

    public PerformanceMonitoringService(
        ILogger<PerformanceMonitoringService> logger,
        PerformanceConfiguration config)
    {
        _logger = logger;
        _config = config;
    }

    public Task<string> StartOperationAsync(string operationName, Dictionary<string, object>? metadata = null)
    {
        var operationId = Guid.NewGuid().ToString();
        var tracker = new OperationTracker
        {
            OperationId = operationId,
            OperationName = operationName,
            StartTime = DateTime.UtcNow,
            Metadata = metadata ?? new Dictionary<string, object>(),
            Stopwatch = Stopwatch.StartNew()
        };

        _activeOperations[operationId] = tracker;
        
        _logger.LogDebug("Started operation tracking: {OperationName} ({OperationId})", operationName, operationId);
        
        return Task.FromResult(operationId);
    }

    public async Task EndOperationAsync(string operationId, bool success = true, string? errorMessage = null)
    {
        if (!_activeOperations.TryRemove(operationId, out var tracker))
        {
            _logger.LogWarning("Operation tracker not found: {OperationId}", operationId);
            return;
        }

        tracker.Stopwatch.Stop();
        tracker.EndTime = DateTime.UtcNow;
        tracker.Duration = tracker.Stopwatch.Elapsed;
        tracker.Success = success;
        tracker.ErrorMessage = errorMessage;

        // Record operation metrics
        await RecordOperationMetrics(tracker);

        // Check for performance alerts
        await CheckOperationAlerts(tracker);

        _logger.LogDebug("Ended operation tracking: {OperationName} ({OperationId}) - Duration: {Duration}ms, Success: {Success}",
            tracker.OperationName, operationId, tracker.Duration.TotalMilliseconds, success);
    }

    public Task RecordMetricAsync(string metricName, double value, Dictionary<string, object>? tags = null)
    {
        var collector = _metrics.GetOrAdd(metricName, _ => new MetricCollector(metricName));
        collector.RecordValue(value, tags);

        _logger.LogTrace("Recorded metric: {MetricName} = {Value}", metricName, value);
        
        return Task.CompletedTask;
    }

    public Task RecordCounterAsync(string counterName, long value = 1, Dictionary<string, object>? tags = null)
    {
        var collector = _counters.GetOrAdd(counterName, _ => new CounterCollector(counterName));
        collector.Increment(value, tags);

        _logger.LogTrace("Recorded counter: {CounterName} += {Value}", counterName, value);
        
        return Task.CompletedTask;
    }

    public async Task<PerformanceReport> GenerateReportAsync(TimeSpan period, CancellationToken cancellationToken = default)
    {
        var endTime = DateTime.UtcNow;
        var startTime = endTime - period;

        var report = new PerformanceReport
        {
            Period = period,
            StartTime = startTime,
            EndTime = endTime,
            GeneratedAt = DateTime.UtcNow
        };

        // Collect operation statistics
        report.OperationStatistics = await CollectOperationStatistics(startTime, endTime);

        // Collect metric statistics
        report.MetricStatistics = await CollectMetricStatistics(startTime, endTime);

        // Collect counter statistics
        report.CounterStatistics = await CollectCounterStatistics(startTime, endTime);

        // Calculate performance scores
        report.PerformanceScore = CalculatePerformanceScore(report);

        // Identify top slow operations
        report.TopSlowOperations = await GetTopSlowOperations(startTime, endTime, 10);

        // Identify error patterns
        report.ErrorPatterns = await AnalyzeErrorPatterns(startTime, endTime);

        // Generate recommendations
        report.Recommendations = GeneratePerformanceRecommendations(report);

        _logger.LogInformation("Generated performance report for period {Period}: Score {Score}",
            period, report.PerformanceScore);

        return report;
    }

    public async Task<List<PerformanceAlert>> CheckAlertsAsync(CancellationToken cancellationToken = default)
    {
        var activeAlerts = new List<PerformanceAlert>();

        // Check response time alerts
        activeAlerts.AddRange(await CheckResponseTimeAlerts());

        // Check error rate alerts
        activeAlerts.AddRange(await CheckErrorRateAlerts());

        // Check throughput alerts
        activeAlerts.AddRange(await CheckThroughputAlerts());

        // Check resource utilization alerts
        activeAlerts.AddRange(await CheckResourceUtilizationAlerts());

        // Update alert history
        foreach (var alert in activeAlerts)
        {
            _alerts.Add(alert);
        }

        // Clean up old alerts
        CleanupOldAlerts();

        return activeAlerts;
    }

    public async Task<HealthCheckResult> GetHealthStatusAsync(CancellationToken cancellationToken = default)
    {
        var healthCheck = new HealthCheckResult
        {
            CheckedAt = DateTime.UtcNow,
            OverallStatus = HealthStatus.Healthy
        };

        // Check active operations
        var activeOperationCount = _activeOperations.Count;
        healthCheck.Checks.Add(new HealthCheck
        {
            Name = "ActiveOperations",
            Status = activeOperationCount < _config.MaxActiveOperations ? HealthStatus.Healthy : HealthStatus.Degraded,
            Value = activeOperationCount.ToString(),
            Description = $"Active operations: {activeOperationCount}/{_config.MaxActiveOperations}"
        });

        // Check average response time
        var avgResponseTime = await GetAverageResponseTime(TimeSpan.FromMinutes(5));
        healthCheck.Checks.Add(new HealthCheck
        {
            Name = "AverageResponseTime",
            Status = avgResponseTime < _config.ResponseTimeThreshold ? HealthStatus.Healthy : HealthStatus.Degraded,
            Value = $"{avgResponseTime:F2}ms",
            Description = $"Average response time over last 5 minutes"
        });

        // Check error rate
        var errorRate = await GetErrorRate(TimeSpan.FromMinutes(5));
        healthCheck.Checks.Add(new HealthCheck
        {
            Name = "ErrorRate",
            Status = errorRate < _config.ErrorRateThreshold ? HealthStatus.Healthy : HealthStatus.Unhealthy,
            Value = $"{errorRate:F2}%",
            Description = $"Error rate over last 5 minutes"
        });

        // Determine overall status
        if (healthCheck.Checks.Any(c => c.Status == HealthStatus.Unhealthy))
        {
            healthCheck.OverallStatus = HealthStatus.Unhealthy;
        }
        else if (healthCheck.Checks.Any(c => c.Status == HealthStatus.Degraded))
        {
            healthCheck.OverallStatus = HealthStatus.Degraded;
        }

        return healthCheck;
    }

    private async Task RecordOperationMetrics(OperationTracker tracker)
    {
        // Record duration metric
        await RecordMetricAsync($"operation.duration.{tracker.OperationName}", tracker.Duration.TotalMilliseconds);

        // Record success/failure counter
        var outcome = tracker.Success ? "success" : "failure";
        await RecordCounterAsync($"operation.{outcome}.{tracker.OperationName}");

        // Record overall operation counter
        await RecordCounterAsync($"operation.total.{tracker.OperationName}");
    }

    private async Task CheckOperationAlerts(OperationTracker tracker)
    {
        // Check for slow operations
        if (tracker.Duration > _config.SlowOperationThreshold)
        {
            var alert = new PerformanceAlert
            {
                Id = Guid.NewGuid(),
                Type = AlertType.SlowOperation,
                Severity = AlertSeverity.Warning,
                Title = "Slow Operation Detected",
                Description = $"Operation '{tracker.OperationName}' took {tracker.Duration.TotalMilliseconds:F2}ms",
                CreatedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["OperationName"] = tracker.OperationName,
                    ["Duration"] = tracker.Duration.TotalMilliseconds,
                    ["Threshold"] = _config.SlowOperationThreshold.TotalMilliseconds
                }
            };

            _alerts.Add(alert);
        }

        // Check for operation failures
        if (!tracker.Success)
        {
            var alert = new PerformanceAlert
            {
                Id = Guid.NewGuid(),
                Type = AlertType.OperationFailure,
                Severity = AlertSeverity.Error,
                Title = "Operation Failure",
                Description = $"Operation '{tracker.OperationName}' failed: {tracker.ErrorMessage}",
                CreatedAt = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["OperationName"] = tracker.OperationName,
                    ["ErrorMessage"] = tracker.ErrorMessage ?? "Unknown error"
                }
            };

            _alerts.Add(alert);
        }
    }

    private async Task<List<OperationStatistic>> CollectOperationStatistics(DateTime startTime, DateTime endTime)
    {
        // This would typically query a time-series database
        // For now, we'll return sample statistics
        return new List<OperationStatistic>();
    }

    private async Task<List<MetricStatistic>> CollectMetricStatistics(DateTime startTime, DateTime endTime)
    {
        var statistics = new List<MetricStatistic>();

        foreach (var metric in _metrics.Values)
        {
            var values = metric.GetValuesInPeriod(startTime, endTime);
            if (values.Any())
            {
                statistics.Add(new MetricStatistic
                {
                    Name = metric.Name,
                    Count = values.Count,
                    Average = values.Average(),
                    Minimum = values.Min(),
                    Maximum = values.Max(),
                    Sum = values.Sum()
                });
            }
        }

        return statistics;
    }

    private async Task<List<CounterStatistic>> CollectCounterStatistics(DateTime startTime, DateTime endTime)
    {
        var statistics = new List<CounterStatistic>();

        foreach (var counter in _counters.Values)
        {
            var total = counter.GetTotalInPeriod(startTime, endTime);
            statistics.Add(new CounterStatistic
            {
                Name = counter.Name,
                Total = total,
                Rate = total / (endTime - startTime).TotalSeconds
            });
        }

        return statistics;
    }

    private decimal CalculatePerformanceScore(PerformanceReport report)
    {
        // Simple performance scoring algorithm
        decimal score = 100;

        // Deduct points for slow operations
        var avgDuration = report.OperationStatistics.Any() 
            ? report.OperationStatistics.Average(o => o.AverageDuration) 
            : 0;
        
        if (avgDuration > _config.SlowOperationThreshold.TotalMilliseconds)
        {
            score -= 20;
        }

        // Deduct points for errors
        var errorRate = report.OperationStatistics.Any()
            ? report.OperationStatistics.Average(o => o.ErrorRate)
            : 0;
        
        score -= (decimal)(errorRate * 50); // 50 points per 1% error rate

        return Math.Max(0, Math.Min(100, score));
    }

    private async Task<List<SlowOperationInfo>> GetTopSlowOperations(DateTime startTime, DateTime endTime, int count)
    {
        // This would typically query operation history
        return new List<SlowOperationInfo>();
    }

    private async Task<List<ErrorPattern>> AnalyzeErrorPatterns(DateTime startTime, DateTime endTime)
    {
        // This would analyze error logs and patterns
        return new List<ErrorPattern>();
    }

    private List<string> GeneratePerformanceRecommendations(PerformanceReport report)
    {
        var recommendations = new List<string>();

        if (report.PerformanceScore < 80)
        {
            recommendations.Add("Consider implementing caching for frequently accessed data");
            recommendations.Add("Review and optimize slow database queries");
            recommendations.Add("Implement connection pooling for external services");
        }

        if (report.OperationStatistics.Any(o => o.ErrorRate > 5))
        {
            recommendations.Add("Investigate and fix operations with high error rates");
            recommendations.Add("Implement circuit breaker pattern for external dependencies");
        }

        return recommendations;
    }

    private async Task<List<PerformanceAlert>> CheckResponseTimeAlerts()
    {
        var alerts = new List<PerformanceAlert>();
        var avgResponseTime = await GetAverageResponseTime(TimeSpan.FromMinutes(5));

        if (avgResponseTime > _config.ResponseTimeThreshold.TotalMilliseconds)
        {
            alerts.Add(new PerformanceAlert
            {
                Id = Guid.NewGuid(),
                Type = AlertType.HighResponseTime,
                Severity = AlertSeverity.Warning,
                Title = "High Response Time",
                Description = $"Average response time is {avgResponseTime:F2}ms",
                CreatedAt = DateTime.UtcNow
            });
        }

        return alerts;
    }

    private async Task<List<PerformanceAlert>> CheckErrorRateAlerts()
    {
        var alerts = new List<PerformanceAlert>();
        var errorRate = await GetErrorRate(TimeSpan.FromMinutes(5));

        if (errorRate > _config.ErrorRateThreshold)
        {
            alerts.Add(new PerformanceAlert
            {
                Id = Guid.NewGuid(),
                Type = AlertType.HighErrorRate,
                Severity = AlertSeverity.Error,
                Title = "High Error Rate",
                Description = $"Error rate is {errorRate:F2}%",
                CreatedAt = DateTime.UtcNow
            });
        }

        return alerts;
    }

    private async Task<List<PerformanceAlert>> CheckThroughputAlerts()
    {
        // Implementation for throughput alerts
        return new List<PerformanceAlert>();
    }

    private async Task<List<PerformanceAlert>> CheckResourceUtilizationAlerts()
    {
        // Implementation for resource utilization alerts
        return new List<PerformanceAlert>();
    }

    private void CleanupOldAlerts()
    {
        var cutoff = DateTime.UtcNow - TimeSpan.FromHours(24);
        _alerts.RemoveAll(a => a.CreatedAt < cutoff);
    }

    private async Task<double> GetAverageResponseTime(TimeSpan period)
    {
        // Calculate average response time from metrics
        var durationMetrics = _metrics.Values
            .Where(m => m.Name.StartsWith("operation.duration."))
            .ToList();

        if (!durationMetrics.Any())
            return 0;

        var endTime = DateTime.UtcNow;
        var startTime = endTime - period;

        var allValues = durationMetrics
            .SelectMany(m => m.GetValuesInPeriod(startTime, endTime))
            .ToList();

        return allValues.Any() ? allValues.Average() : 0;
    }

    private async Task<double> GetErrorRate(TimeSpan period)
    {
        // Calculate error rate from counters
        var endTime = DateTime.UtcNow;
        var startTime = endTime - period;

        var totalOperations = _counters.Values
            .Where(c => c.Name.StartsWith("operation.total."))
            .Sum(c => c.GetTotalInPeriod(startTime, endTime));

        var failedOperations = _counters.Values
            .Where(c => c.Name.StartsWith("operation.failure."))
            .Sum(c => c.GetTotalInPeriod(startTime, endTime));

        return totalOperations > 0 ? (double)failedOperations / totalOperations * 100 : 0;
    }
}

// Supporting classes and enums would be defined here...
public class OperationTracker
{
    public string OperationId { get; set; } = string.Empty;
    public string OperationName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public Stopwatch Stopwatch { get; set; } = new();
}

public class MetricCollector
{
    public string Name { get; }
    private readonly List<MetricValue> _values = new();
    private readonly object _lock = new();

    public MetricCollector(string name)
    {
        Name = name;
    }

    public void RecordValue(double value, Dictionary<string, object>? tags = null)
    {
        lock (_lock)
        {
            _values.Add(new MetricValue
            {
                Value = value,
                Timestamp = DateTime.UtcNow,
                Tags = tags ?? new Dictionary<string, object>()
            });
        }
    }

    public List<double> GetValuesInPeriod(DateTime startTime, DateTime endTime)
    {
        lock (_lock)
        {
            return _values
                .Where(v => v.Timestamp >= startTime && v.Timestamp <= endTime)
                .Select(v => v.Value)
                .ToList();
        }
    }
}

public class CounterCollector
{
    public string Name { get; }
    private readonly List<CounterValue> _values = new();
    private readonly object _lock = new();

    public CounterCollector(string name)
    {
        Name = name;
    }

    public void Increment(long value = 1, Dictionary<string, object>? tags = null)
    {
        lock (_lock)
        {
            _values.Add(new CounterValue
            {
                Value = value,
                Timestamp = DateTime.UtcNow,
                Tags = tags ?? new Dictionary<string, object>()
            });
        }
    }

    public long GetTotalInPeriod(DateTime startTime, DateTime endTime)
    {
        lock (_lock)
        {
            return _values
                .Where(v => v.Timestamp >= startTime && v.Timestamp <= endTime)
                .Sum(v => v.Value);
        }
    }
}

public class MetricValue
{
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
}

public class CounterValue
{
    public long Value { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Tags { get; set; } = new();
}

public class PerformanceConfiguration
{
    public TimeSpan SlowOperationThreshold { get; set; } = TimeSpan.FromSeconds(5);
    public TimeSpan ResponseTimeThreshold { get; set; } = TimeSpan.FromSeconds(2);
    public double ErrorRateThreshold { get; set; } = 5.0; // 5%
    public int MaxActiveOperations { get; set; } = 1000;
    public bool EnableDetailedMetrics { get; set; } = true;
    public TimeSpan MetricRetentionPeriod { get; set; } = TimeSpan.FromDays(7);
}

public class PerformanceReport
{
    public TimeSpan Period { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public DateTime GeneratedAt { get; set; }
    public decimal PerformanceScore { get; set; }
    public List<OperationStatistic> OperationStatistics { get; set; } = new();
    public List<MetricStatistic> MetricStatistics { get; set; } = new();
    public List<CounterStatistic> CounterStatistics { get; set; } = new();
    public List<SlowOperationInfo> TopSlowOperations { get; set; } = new();
    public List<ErrorPattern> ErrorPatterns { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

public class OperationStatistic
{
    public string OperationName { get; set; } = string.Empty;
    public long TotalCount { get; set; }
    public long SuccessCount { get; set; }
    public long FailureCount { get; set; }
    public double ErrorRate { get; set; }
    public double AverageDuration { get; set; }
    public double MinDuration { get; set; }
    public double MaxDuration { get; set; }
    public double P95Duration { get; set; }
    public double P99Duration { get; set; }
}

public class MetricStatistic
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Average { get; set; }
    public double Minimum { get; set; }
    public double Maximum { get; set; }
    public double Sum { get; set; }
}

public class CounterStatistic
{
    public string Name { get; set; } = string.Empty;
    public long Total { get; set; }
    public double Rate { get; set; }
}

public class SlowOperationInfo
{
    public string OperationName { get; set; } = string.Empty;
    public double AverageDuration { get; set; }
    public double MaxDuration { get; set; }
    public long Count { get; set; }
}

public class ErrorPattern
{
    public string Pattern { get; set; } = string.Empty;
    public long Count { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class PerformanceAlert
{
    public Guid Id { get; set; }
    public AlertType Type { get; set; }
    public AlertSeverity Severity { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class HealthCheckResult
{
    public DateTime CheckedAt { get; set; }
    public HealthStatus OverallStatus { get; set; }
    public List<HealthCheck> Checks { get; set; } = new();
}

public class HealthCheck
{
    public string Name { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public string Value { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public enum AlertType
{
    SlowOperation,
    OperationFailure,
    HighResponseTime,
    HighErrorRate,
    HighThroughput,
    ResourceUtilization
}

public enum AlertSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

public enum HealthStatus
{
    Healthy,
    Degraded,
    Unhealthy
}
