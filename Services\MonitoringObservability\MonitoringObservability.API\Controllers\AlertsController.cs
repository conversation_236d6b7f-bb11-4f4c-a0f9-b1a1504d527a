using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using MonitoringObservability.Application.Commands;
using MonitoringObservability.Application.Queries;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.API.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class AlertsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AlertsController> _logger;

    public AlertsController(IMediator mediator, ILogger<AlertsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all alerts with optional filtering
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<AlertDto>>> GetAlerts([FromQuery] GetAlertsRequest request)
    {
        try
        {
            var query = new GetAlertsQuery
            {
                ServiceName = request.ServiceName,
                Severity = request.Severity,
                Status = request.Status,
                TriggeredAfter = request.TriggeredAfter,
                TriggeredBefore = request.TriggeredBefore,
                AssignedToUserId = request.AssignedToUserId,
                Skip = request.Skip,
                Take = request.Take,
                SortBy = request.SortBy,
                SortDescending = request.SortDescending
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alerts");
            return StatusCode(500, "Failed to retrieve alerts");
        }
    }

    /// <summary>
    /// Get alert by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<AlertDto>> GetAlert(Guid id, [FromQuery] bool includeDetails = false)
    {
        try
        {
            var query = new GetAlertByIdQuery { AlertId = id, IncludeDetails = includeDetails };
            var result = await _mediator.Send(query);
            
            if (result == null)
                return NotFound();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alert {AlertId}", id);
            return StatusCode(500, "Failed to retrieve alert");
        }
    }

    /// <summary>
    /// Create a new alert
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Monitor")]
    public async Task<ActionResult<AlertDto>> CreateAlert([FromBody] CreateAlertRequest request)
    {
        try
        {
            var command = new CreateAlertCommand
            {
                Title = request.Title,
                Description = request.Description,
                Severity = request.Severity,
                Source = request.Source,
                ServiceName = request.ServiceName,
                MetricName = request.MetricName,
                CurrentValue = request.CurrentValue,
                Threshold = request.Threshold,
                Tags = request.Tags
            };

            var result = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetAlert), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create alert");
            return StatusCode(500, "Failed to create alert");
        }
    }

    /// <summary>
    /// Acknowledge an alert
    /// </summary>
    [HttpPost("{id}/acknowledge")]
    public async Task<ActionResult<AlertDto>> AcknowledgeAlert(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new AcknowledgeAlertCommand
            {
                AlertId = id,
                UserId = userId,
                UserName = userName
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acknowledge alert {AlertId}", id);
            return StatusCode(500, "Failed to acknowledge alert");
        }
    }

    /// <summary>
    /// Assign an alert to a user
    /// </summary>
    [HttpPost("{id}/assign")]
    public async Task<ActionResult<AlertDto>> AssignAlert(Guid id, [FromBody] AssignAlertRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new AssignAlertCommand
            {
                AlertId = id,
                AssignedToUserId = request.AssignedToUserId,
                AssignedToUserName = request.AssignedToUserName,
                AssignedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to assign alert {AlertId}", id);
            return StatusCode(500, "Failed to assign alert");
        }
    }

    /// <summary>
    /// Resolve an alert
    /// </summary>
    [HttpPost("{id}/resolve")]
    public async Task<ActionResult<AlertDto>> ResolveAlert(Guid id, [FromBody] ResolveAlertRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new ResolveAlertCommand
            {
                AlertId = id,
                UserId = userId,
                ResolutionNotes = request.ResolutionNotes
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve alert {AlertId}", id);
            return StatusCode(500, "Failed to resolve alert");
        }
    }

    /// <summary>
    /// Close an alert
    /// </summary>
    [HttpPost("{id}/close")]
    public async Task<ActionResult<AlertDto>> CloseAlert(Guid id, [FromBody] CloseAlertRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new CloseAlertCommand
            {
                AlertId = id,
                UserId = userId,
                ClosureNotes = request.ClosureNotes
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to close alert {AlertId}", id);
            return StatusCode(500, "Failed to close alert");
        }
    }

    /// <summary>
    /// Escalate an alert
    /// </summary>
    [HttpPost("{id}/escalate")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<AlertDto>> EscalateAlert(Guid id, [FromBody] EscalateAlertRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var command = new EscalateAlertCommand
            {
                AlertId = id,
                EscalationLevel = request.EscalationLevel,
                Reason = request.Reason,
                EscalatedByUserId = userId
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to escalate alert {AlertId}", id);
            return StatusCode(500, "Failed to escalate alert");
        }
    }

    /// <summary>
    /// Add comment to an alert
    /// </summary>
    [HttpPost("{id}/comments")]
    public async Task<ActionResult<AlertCommentDto>> AddComment(Guid id, [FromBody] AddCommentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var userName = GetCurrentUserName();

            var command = new AddAlertCommentCommand
            {
                AlertId = id,
                UserId = userId,
                UserName = userName,
                Comment = request.Comment
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add comment to alert {AlertId}", id);
            return StatusCode(500, "Failed to add comment");
        }
    }

    /// <summary>
    /// Get open alerts
    /// </summary>
    [HttpGet("open")]
    public async Task<ActionResult<IEnumerable<AlertDto>>> GetOpenAlerts([FromQuery] string? serviceName = null, [FromQuery] AlertSeverity? minimumSeverity = null)
    {
        try
        {
            var query = new GetOpenAlertsQuery
            {
                ServiceName = serviceName,
                MinimumSeverity = minimumSeverity
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get open alerts");
            return StatusCode(500, "Failed to retrieve open alerts");
        }
    }

    /// <summary>
    /// Get critical alerts
    /// </summary>
    [HttpGet("critical")]
    public async Task<ActionResult<IEnumerable<AlertDto>>> GetCriticalAlerts([FromQuery] string? serviceName = null)
    {
        try
        {
            var query = new GetCriticalAlertsQuery { ServiceName = serviceName };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get critical alerts");
            return StatusCode(500, "Failed to retrieve critical alerts");
        }
    }

    /// <summary>
    /// Get alerts assigned to current user
    /// </summary>
    [HttpGet("assigned-to-me")]
    public async Task<ActionResult<IEnumerable<AlertDto>>> GetMyAlerts([FromQuery] AlertStatus? status = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var query = new GetAlertsAssignedToUserQuery { UserId = userId, Status = status };
            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user alerts");
            return StatusCode(500, "Failed to retrieve user alerts");
        }
    }

    /// <summary>
    /// Get alert statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<AlertStatisticsDto>> GetAlertStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] string? serviceName = null)
    {
        try
        {
            var severityCountQuery = new GetAlertCountBySeverityQuery { FromDate = fromDate, ServiceName = serviceName };
            var serviceCountQuery = new GetAlertCountByServiceQuery { FromDate = fromDate };

            var severityCount = await _mediator.Send(severityCountQuery);
            var serviceCount = await _mediator.Send(serviceCountQuery);

            var statistics = new AlertStatisticsDto
            {
                CountBySeverity = severityCount,
                CountByService = serviceCount,
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get alert statistics");
            return StatusCode(500, "Failed to retrieve alert statistics");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("user_id");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token");
    }

    private string GetCurrentUserName()
    {
        var userNameClaim = User.FindFirst("name") ?? User.FindFirst("username");
        return userNameClaim?.Value ?? "Unknown User";
    }
}

// Request DTOs
public class GetAlertsRequest
{
    public string? ServiceName { get; set; }
    public AlertSeverity? Severity { get; set; }
    public AlertStatus? Status { get; set; }
    public DateTime? TriggeredAfter { get; set; }
    public DateTime? TriggeredBefore { get; set; }
    public Guid? AssignedToUserId { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
    public string? SortBy { get; set; } = "TriggeredAt";
    public bool SortDescending { get; set; } = true;
}

public class CreateAlertRequest
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Source { get; set; } = string.Empty;
    public string ServiceName { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public AlertThresholdDto Threshold { get; set; } = null!;
    public Dictionary<string, string>? Tags { get; set; }
}

public class AssignAlertRequest
{
    public Guid AssignedToUserId { get; set; }
    public string AssignedToUserName { get; set; } = string.Empty;
}

public class ResolveAlertRequest
{
    public string? ResolutionNotes { get; set; }
}

public class CloseAlertRequest
{
    public string? ClosureNotes { get; set; }
}

public class EscalateAlertRequest
{
    public int EscalationLevel { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class AddCommentRequest
{
    public string Comment { get; set; } = string.Empty;
}

public class AlertStatisticsDto
{
    public Dictionary<AlertSeverity, int> CountBySeverity { get; set; } = new();
    public Dictionary<string, int> CountByService { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}
