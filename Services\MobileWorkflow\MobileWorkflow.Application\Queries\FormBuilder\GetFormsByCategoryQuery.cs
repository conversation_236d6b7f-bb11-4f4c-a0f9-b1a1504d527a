using System.Collections.Generic;
using MediatR;
using MobileWorkflow.Application.DTOs;

namespace MobileWorkflow.Application.Queries.FormBuilder
{
    public class GetFormsByCategoryQuery : IRequest<List<FormDefinitionDto>>
    {
        public string Category { get; set; } = string.Empty;
        public bool IncludeTemplates { get; set; } = true;
        public bool IncludeInactive { get; set; } = false;
        public string? Platform { get; set; }
    }
}
