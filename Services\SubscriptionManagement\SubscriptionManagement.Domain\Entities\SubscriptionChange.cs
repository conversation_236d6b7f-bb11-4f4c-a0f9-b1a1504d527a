using Shared.Domain.Common;

namespace SubscriptionManagement.Domain.Entities
{
    public class SubscriptionChange : BaseEntity
    {
        public string ChangeType { get; private set; }
        public string? Description { get; private set; }
        public DateTime ChangedAt { get; private set; }

        private SubscriptionChange() { }

        public SubscriptionChange(string changeType, string? description = null)
        {
            if (string.IsNullOrWhiteSpace(changeType))
                throw new ArgumentException("Change type cannot be null or empty", nameof(changeType));

            ChangeType = changeType;
            Description = description;
            ChangedAt = DateTime.UtcNow;
        }

        public override string ToString()
        {
            var desc = !string.IsNullOrWhiteSpace(Description) ? $" - {Description}" : "";
            return $"{ChangeType} at {ChangedAt:yyyy-MM-dd HH:mm:ss}{desc}";
        }
    }
}
