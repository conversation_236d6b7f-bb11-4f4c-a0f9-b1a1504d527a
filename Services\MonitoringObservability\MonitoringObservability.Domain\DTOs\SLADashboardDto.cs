namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for SLA dashboard
/// </summary>
public class SLADashboardDto
{
    public DateTime GeneratedAt { get; set; }
    public SLAOverviewMetrics Overview { get; set; } = new();
    public List<SLAServiceSummary> ServiceSummaries { get; set; } = new();
    public List<SLAViolationSummary> RecentViolations { get; set; } = new();
    public List<SLATrendData> Trends { get; set; } = new();
    public SLAComplianceBreakdown ComplianceBreakdown { get; set; } = new();
    public List<SLAAlert> ActiveAlerts { get; set; } = new();
}

public class SLAOverviewMetrics
{
    public double OverallCompliancePercentage { get; set; }
    public int TotalSLAs { get; set; }
    public int CompliantSLAs { get; set; }
    public int ViolatingSLAs { get; set; }
    public int CriticalViolations { get; set; }
    public TimeSpan TotalDowntime { get; set; }
    public double AverageAvailability { get; set; }
}

public class SLAServiceSummary
{
    public string ServiceName { get; set; } = string.Empty;
    public double CompliancePercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public int ActiveSLAs { get; set; }
    public int ViolationCount { get; set; }
    public DateTime? LastViolation { get; set; }
    public double AvailabilityPercentage { get; set; }
    public double AverageResponseTime { get; set; }
}

public class SLATrendData
{
    public DateTime Date { get; set; }
    public double CompliancePercentage { get; set; }
    public int ViolationCount { get; set; }
    public double AvailabilityPercentage { get; set; }
    public string ServiceName { get; set; } = string.Empty;
}

public class SLAComplianceBreakdown
{
    public Dictionary<string, double> ByService { get; set; } = new();
    public Dictionary<string, double> BySeverity { get; set; } = new();
    public Dictionary<string, double> ByMetricType { get; set; } = new();
    public Dictionary<string, int> ViolationsByCategory { get; set; } = new();
}

public class SLAAlert
{
    public Guid AlertId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Context { get; set; } = new();
}
