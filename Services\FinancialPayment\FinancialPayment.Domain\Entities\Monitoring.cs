using FinancialPayment.Domain.ValueObjects;
using Shared.Domain.Common;

namespace FinancialPayment.Domain.Entities;

public class SystemMetric : AggregateRoot
{
    public string MetricName { get; private set; }
    public string MetricType { get; private set; } // Counter, Gauge, Histogram, Timer
    public decimal Value { get; private set; }
    public string Unit { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string Source { get; private set; } // Service/Component that generated the metric
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private SystemMetric() { }

    public SystemMetric(
        string metricName,
        string metricType,
        decimal value,
        string unit,
        string source,
        Dictionary<string, string>? tags = null,
        Dictionary<string, object>? metadata = null)
    {
        MetricName = metricName;
        MetricType = metricType;
        Value = value;
        Unit = unit;
        Timestamp = DateTime.UtcNow;
        Source = source;
        Tags = tags ?? new Dictionary<string, string>();
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    public void UpdateValue(decimal newValue)
    {
        Value = newValue;
        Timestamp = DateTime.UtcNow;
        SetUpdatedAt();
    }
}

public class PerformanceMetric : AggregateRoot
{
    public string OperationName { get; private set; }
    public string ServiceName { get; private set; }
    public TimeSpan Duration { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public bool IsSuccess { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? ErrorType { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, object> Context { get; private set; }

    private PerformanceMetric() { }

    public PerformanceMetric(
        string operationName,
        string serviceName,
        DateTime startTime,
        DateTime endTime,
        bool isSuccess,
        string? errorMessage = null,
        string? errorType = null,
        Dictionary<string, string>? tags = null,
        Dictionary<string, object>? context = null)
    {
        OperationName = operationName;
        ServiceName = serviceName;
        StartTime = startTime;
        EndTime = endTime;
        Duration = endTime - startTime;
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        ErrorType = errorType;
        Tags = tags ?? new Dictionary<string, string>();
        Context = context ?? new Dictionary<string, object>();
    }
}

public class Alert : AggregateRoot
{
    public string AlertName { get; private set; }
    public AlertSeverity Severity { get; private set; }
    public AlertStatus Status { get; private set; }
    public string Message { get; private set; }
    public string Source { get; private set; }
    public DateTime TriggeredAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public DateTime? AcknowledgedAt { get; private set; }
    public Guid? AcknowledgedBy { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public Dictionary<string, object> AlertData { get; private set; }
    public List<string> NotificationChannels { get; private set; }

    private Alert() { }

    public Alert(
        string alertName,
        AlertSeverity severity,
        string message,
        string source,
        Dictionary<string, object>? alertData = null,
        List<string>? notificationChannels = null)
    {
        AlertName = alertName;
        Severity = severity;
        Status = AlertStatus.Active;
        Message = message;
        Source = source;
        TriggeredAt = DateTime.UtcNow;
        AlertData = alertData ?? new Dictionary<string, object>();
        NotificationChannels = notificationChannels ?? new List<string>();
    }

    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status == AlertStatus.Active)
        {
            Status = AlertStatus.Acknowledged;
            AcknowledgedAt = DateTime.UtcNow;
            AcknowledgedBy = acknowledgedBy;
            if (!string.IsNullOrEmpty(notes))
            {
                AlertData["acknowledgment_notes"] = notes;
            }
            SetUpdatedAt();
        }
    }

    public void Resolve(string? resolutionNotes = null)
    {
        Status = AlertStatus.Resolved;
        ResolvedAt = DateTime.UtcNow;
        ResolutionNotes = resolutionNotes;
        SetUpdatedAt();
    }

    public void Escalate(AlertSeverity newSeverity)
    {
        if (newSeverity > Severity)
        {
            Severity = newSeverity;
            AlertData["escalated_at"] = DateTime.UtcNow;
            SetUpdatedAt();
        }
    }
}

public class HealthCheck : AggregateRoot
{
    public string ServiceName { get; private set; }
    public string CheckName { get; private set; }
    public HealthStatus Status { get; private set; }
    public string? Description { get; private set; }
    public TimeSpan ResponseTime { get; private set; }
    public DateTime CheckedAt { get; private set; }
    public Dictionary<string, object> CheckData { get; private set; }
    public string? ErrorMessage { get; private set; }

    private HealthCheck() { }

    public HealthCheck(
        string serviceName,
        string checkName,
        HealthStatus status,
        TimeSpan responseTime,
        string? description = null,
        Dictionary<string, object>? checkData = null,
        string? errorMessage = null)
    {
        ServiceName = serviceName;
        CheckName = checkName;
        Status = status;
        Description = description;
        ResponseTime = responseTime;
        CheckedAt = DateTime.UtcNow;
        CheckData = checkData ?? new Dictionary<string, object>();
        ErrorMessage = errorMessage;
    }
}

public class UsageAnalytics : AggregateRoot
{
    public string ResourceType { get; private set; } // API, Service, Feature, etc.
    public string ResourceName { get; private set; }
    public Guid? UserId { get; private set; }
    public string? SessionId { get; private set; }
    public string Action { get; private set; }
    public DateTime Timestamp { get; private set; }
    public TimeSpan? Duration { get; private set; }
    public bool IsSuccess { get; private set; }
    public string? ErrorCode { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, string> Tags { get; private set; }
    public Dictionary<string, object> Properties { get; private set; }

    private UsageAnalytics() { }

    public UsageAnalytics(
        string resourceType,
        string resourceName,
        string action,
        bool isSuccess,
        Guid? userId = null,
        string? sessionId = null,
        TimeSpan? duration = null,
        string? errorCode = null,
        string? errorMessage = null,
        Dictionary<string, string>? tags = null,
        Dictionary<string, object>? properties = null)
    {
        ResourceType = resourceType;
        ResourceName = resourceName;
        Action = action;
        UserId = userId;
        SessionId = sessionId;
        Timestamp = DateTime.UtcNow;
        Duration = duration;
        IsSuccess = isSuccess;
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
        Tags = tags ?? new Dictionary<string, string>();
        Properties = properties ?? new Dictionary<string, object>();
    }
}

public class MonitoringDashboard : AggregateRoot
{
    public string DashboardName { get; private set; }
    public string Description { get; private set; }
    public Guid? CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool IsPublic { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }

    // Navigation properties
    private readonly List<DashboardWidget> _widgets = new();
    public IReadOnlyList<DashboardWidget> Widgets => _widgets.AsReadOnly();

    private MonitoringDashboard() { }

    public MonitoringDashboard(
        string dashboardName,
        string description,
        Guid? createdBy = null,
        bool isPublic = false)
    {
        DashboardName = dashboardName;
        Description = description;
        CreatedBy = createdBy;
        CreatedAt = DateTime.UtcNow;
        IsPublic = isPublic;
        Configuration = new Dictionary<string, object>();
    }

    public void AddWidget(string widgetName, string widgetType, Dictionary<string, object> configuration, int order = 0)
    {
        var widget = new DashboardWidget(Id, widgetName, widgetType, configuration, order);
        _widgets.Add(widget);
        SetUpdatedAt();
    }

    public void RemoveWidget(Guid widgetId)
    {
        var widget = _widgets.FirstOrDefault(w => w.Id == widgetId);
        if (widget != null)
        {
            _widgets.Remove(widget);
            SetUpdatedAt();
        }
    }
}

public class DashboardWidget : BaseEntity
{
    public Guid DashboardId { get; private set; }
    public string WidgetName { get; private set; }
    public string WidgetType { get; private set; } // Chart, Metric, Table, Alert
    public Dictionary<string, object> Configuration { get; private set; }
    public int Order { get; private set; }
    public bool IsVisible { get; private set; }

    private DashboardWidget() { }

    public DashboardWidget(
        Guid dashboardId,
        string widgetName,
        string widgetType,
        Dictionary<string, object> configuration,
        int order = 0)
    {
        DashboardId = dashboardId;
        WidgetName = widgetName;
        WidgetType = widgetType;
        Configuration = configuration;
        Order = order;
        IsVisible = true;
    }

    public void UpdateConfiguration(Dictionary<string, object> newConfiguration)
    {
        Configuration = newConfiguration;
    }

    public void UpdateOrder(int newOrder)
    {
        Order = newOrder;
    }

    public void Show() => IsVisible = true;
    public void Hide() => IsVisible = false;
}

// Enums
public enum AlertSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

public enum AlertStatus
{
    Active = 1,
    Acknowledged = 2,
    Resolved = 3,
    Suppressed = 4
}

public enum HealthStatus
{
    Healthy = 1,
    Degraded = 2,
    Unhealthy = 3,
    Unknown = 4
}

// Value Objects
public class MetricThreshold
{
    public decimal WarningThreshold { get; }
    public decimal CriticalThreshold { get; }
    public string Operator { get; } // >, <, >=, <=, ==, !=
    public TimeSpan EvaluationWindow { get; }

    public MetricThreshold(decimal warningThreshold, decimal criticalThreshold, string @operator, TimeSpan evaluationWindow)
    {
        WarningThreshold = warningThreshold;
        CriticalThreshold = criticalThreshold;
        Operator = @operator;
        EvaluationWindow = evaluationWindow;
    }

    public AlertSeverity? EvaluateThreshold(decimal value)
    {
        return Operator switch
        {
            ">" => value > CriticalThreshold ? AlertSeverity.Critical :
                   value > WarningThreshold ? AlertSeverity.Medium : null,
            "<" => value < CriticalThreshold ? AlertSeverity.Critical :
                   value < WarningThreshold ? AlertSeverity.Medium : null,
            ">=" => value >= CriticalThreshold ? AlertSeverity.Critical :
                    value >= WarningThreshold ? AlertSeverity.Medium : null,
            "<=" => value <= CriticalThreshold ? AlertSeverity.Critical :
                    value <= WarningThreshold ? AlertSeverity.Medium : null,
            "==" => value == CriticalThreshold ? AlertSeverity.Critical :
                    value == WarningThreshold ? AlertSeverity.Medium : null,
            "!=" => value != CriticalThreshold ? AlertSeverity.Critical :
                    value != WarningThreshold ? AlertSeverity.Medium : null,
            _ => null
        };
    }
}


