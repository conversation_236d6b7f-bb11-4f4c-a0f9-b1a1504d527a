using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class AnalyticsEvent : AggregateRoot
{
    public Guid UserId { get; private set; }
    public Guid? SessionId { get; private set; }
    public string EventType { get; private set; } // PageView, ButtonClick, FormSubmit, Error, Performance, etc.
    public string EventName { get; private set; }
    public string Category { get; private set; } // UI, Navigation, Business, System, etc.
    public Dictionary<string, object> Properties { get; private set; }
    public Dictionary<string, object> Context { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string Platform { get; private set; } // iOS, Android, Web, PWA
    public string AppVersion { get; private set; }
    public string DeviceInfo { get; private set; }
    public string? ScreenName { get; private set; }
    public string? UserAgent { get; private set; }
    public string? IpAddress { get; private set; }
    public string? Location { get; private set; }
    public double? Duration { get; private set; } // For events with duration
    public string? ReferrerUrl { get; private set; }
    public Dictionary<string, object> CustomDimensions { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    private AnalyticsEvent() { } // EF Core

    public AnalyticsEvent(
        Guid userId,
        string eventType,
        string eventName,
        string category,
        string platform,
        string appVersion,
        Guid? sessionId = null)
    {
        UserId = userId;
        SessionId = sessionId;
        EventType = eventType ?? throw new ArgumentNullException(nameof(eventType));
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));

        Timestamp = DateTime.UtcNow;
        Properties = new Dictionary<string, object>();
        Context = new Dictionary<string, object>();
        CustomDimensions = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();
        DeviceInfo = string.Empty;

        AddDomainEvent(new AnalyticsEventRecordedEvent(Id, UserId, EventType, EventName, Category, Platform));
    }

    public void SetDeviceInfo(string deviceInfo)
    {
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));
    }

    public void SetScreenName(string screenName)
    {
        ScreenName = screenName;
    }

    public void SetUserAgent(string userAgent)
    {
        UserAgent = userAgent;
    }

    public void SetIpAddress(string ipAddress)
    {
        IpAddress = ipAddress;
    }

    public void SetLocation(string location)
    {
        Location = location;
    }

    public void SetDuration(double duration)
    {
        Duration = duration;
    }

    public void SetReferrerUrl(string referrerUrl)
    {
        ReferrerUrl = referrerUrl;
    }

    public void AddProperty(string key, object value)
    {
        Properties[key] = value;
    }

    public void AddContext(string key, object value)
    {
        Context[key] = value;
    }

    public void AddCustomDimension(string key, object value)
    {
        CustomDimensions[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public T? GetProperty<T>(string key, T? defaultValue = default)
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetContext<T>(string key, T? defaultValue = default)
    {
        if (Context.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetCustomDimension<T>(string key, T? defaultValue = default)
    {
        if (CustomDimensions.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public bool IsUserInteractionEvent()
    {
        return EventType.ToLowerInvariant() switch
        {
            "click" or "tap" or "swipe" or "scroll" or "input" or "submit" => true,
            _ => false
        };
    }

    public bool IsPerformanceEvent()
    {
        return EventType.ToLowerInvariant() switch
        {
            "performance" or "timing" or "load" or "render" => true,
            _ => false
        };
    }

    public bool IsErrorEvent()
    {
        return EventType.ToLowerInvariant() switch
        {
            "error" or "exception" or "crash" or "warning" => true,
            _ => false
        };
    }

    public bool IsBusinessEvent()
    {
        return Category.ToLowerInvariant() == "business";
    }

    public Dictionary<string, object> ToAnalyticsPayload()
    {
        var payload = new Dictionary<string, object>
        {
            ["id"] = Id,
            ["userId"] = UserId,
            ["sessionId"] = SessionId,
            ["eventType"] = EventType,
            ["eventName"] = EventName,
            ["category"] = Category,
            ["timestamp"] = Timestamp,
            ["platform"] = Platform,
            ["appVersion"] = AppVersion,
            ["deviceInfo"] = DeviceInfo,
            ["properties"] = Properties,
            ["context"] = Context,
            ["customDimensions"] = CustomDimensions
        };

        if (!string.IsNullOrEmpty(ScreenName))
            payload["screenName"] = ScreenName;

        if (!string.IsNullOrEmpty(UserAgent))
            payload["userAgent"] = UserAgent;

        if (!string.IsNullOrEmpty(IpAddress))
            payload["ipAddress"] = IpAddress;

        if (!string.IsNullOrEmpty(Location))
            payload["location"] = Location;

        if (Duration.HasValue)
            payload["duration"] = Duration.Value;

        if (!string.IsNullOrEmpty(ReferrerUrl))
            payload["referrerUrl"] = ReferrerUrl;

        return payload;
    }
}

public class UserSession : AggregateRoot
{
    public Guid UserId { get; private set; }
    public string SessionToken { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime? EndTime { get; private set; }
    public string Platform { get; private set; }
    public string AppVersion { get; private set; }
    public string DeviceInfo { get; private set; }
    public string? IpAddress { get; private set; }
    public string? Location { get; private set; }
    public string? UserAgent { get; private set; }
    public bool IsActive { get; private set; }
    public int EventCount { get; private set; }
    public int ScreenViewCount { get; private set; }
    public int InteractionCount { get; private set; }
    public int ErrorCount { get; private set; }
    public Dictionary<string, object> SessionData { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<AnalyticsEvent> Events { get; private set; } = new List<AnalyticsEvent>();

    private UserSession() { } // EF Core

    public UserSession(
        Guid userId,
        string sessionToken,
        string platform,
        string appVersion,
        string deviceInfo)
    {
        UserId = userId;
        SessionToken = sessionToken ?? throw new ArgumentNullException(nameof(sessionToken));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        AppVersion = appVersion ?? throw new ArgumentNullException(nameof(appVersion));
        DeviceInfo = deviceInfo ?? throw new ArgumentNullException(nameof(deviceInfo));

        StartTime = DateTime.UtcNow;
        IsActive = true;
        EventCount = 0;
        ScreenViewCount = 0;
        InteractionCount = 0;
        ErrorCount = 0;
        SessionData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new UserSessionStartedEvent(Id, UserId, Platform, AppVersion));
    }

    public void SetIpAddress(string ipAddress)
    {
        IpAddress = ipAddress;
    }

    public void SetLocation(string location)
    {
        Location = location;
    }

    public void SetUserAgent(string userAgent)
    {
        UserAgent = userAgent;
    }

    public void EndSession()
    {
        if (!IsActive)
            return;

        IsActive = false;
        EndTime = DateTime.UtcNow;

        AddDomainEvent(new UserSessionEndedEvent(Id, UserId, GetDuration().TotalMinutes, EventCount));
    }

    public void RecordEvent(AnalyticsEvent analyticsEvent)
    {
        EventCount++;

        if (analyticsEvent.EventType.ToLowerInvariant() == "pageview" || analyticsEvent.EventType.ToLowerInvariant() == "screenview")
        {
            ScreenViewCount++;
        }

        if (analyticsEvent.IsUserInteractionEvent())
        {
            InteractionCount++;
        }

        if (analyticsEvent.IsErrorEvent())
        {
            ErrorCount++;
        }

        // Update last activity time
        Metadata["last_activity"] = DateTime.UtcNow;
    }

    public void AddSessionData(string key, object value)
    {
        SessionData[key] = value;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public TimeSpan GetDuration()
    {
        var endTime = EndTime ?? DateTime.UtcNow;
        return endTime - StartTime;
    }

    public double GetEngagementScore()
    {
        var duration = GetDuration().TotalMinutes;
        var interactionRate = EventCount > 0 ? (double)InteractionCount / EventCount : 0;
        var errorRate = EventCount > 0 ? (double)ErrorCount / EventCount : 0;

        // Simple engagement score calculation
        var score = (duration * 0.3) + (interactionRate * 50) + (ScreenViewCount * 2) - (errorRate * 20);
        return Math.Max(0, Math.Min(100, score));
    }

    public bool IsLongSession()
    {
        return GetDuration().TotalMinutes > 30;
    }

    public bool IsHighEngagement()
    {
        return GetEngagementScore() > 70;
    }

    public T? GetSessionData<T>(string key, T? defaultValue = default)
    {
        if (SessionData.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}


