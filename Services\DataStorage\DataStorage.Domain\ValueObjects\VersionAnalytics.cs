using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ChangeSet
{
    public string ChangeSetId { get; private set; }
    public Guid DocumentId { get; private set; }
    public string VersionId { get; private set; }
    public string BranchName { get; private set; }
    public Guid AuthorId { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string? Message { get; private set; }
    public List<FileChange> Changes { get; private set; }
    public ChangeSetType Type { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public ChangeSet(
        string changeSetId,
        Guid documentId,
        string versionId,
        string branchName,
        Guid authorId,
        DateTime timestamp,
        List<FileChange> changes,
        ChangeSetType type,
        string? message = null,
        Dictionary<string, object>? metadata = null)
    {
        ChangeSetId = changeSetId ?? throw new ArgumentNullException(nameof(changeSetId));
        DocumentId = documentId;
        VersionId = versionId ?? throw new ArgumentNullException(nameof(versionId));
        BranchName = branchName ?? throw new ArgumentNullException(nameof(branchName));
        AuthorId = authorId;
        Timestamp = timestamp;
        Message = message;
        Changes = changes ?? new List<FileChange>();
        Type = type;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class FileChange
{
    public string FilePath { get; private set; }
    public ChangeType ChangeType { get; private set; }
    public int LinesAdded { get; private set; }
    public int LinesDeleted { get; private set; }
    public long SizeBefore { get; private set; }
    public long SizeAfter { get; private set; }

    public FileChange(
        string filePath,
        ChangeType changeType,
        int linesAdded = 0,
        int linesDeleted = 0,
        long sizeBefore = 0,
        long sizeAfter = 0)
    {
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        ChangeType = changeType;
        LinesAdded = linesAdded;
        LinesDeleted = linesDeleted;
        SizeBefore = sizeBefore;
        SizeAfter = sizeAfter;
    }
}

public class VersionTag
{
    public string Name { get; private set; }
    public Guid DocumentId { get; private set; }
    public string VersionId { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public TagType Type { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public VersionTag(
        string name,
        Guid documentId,
        string versionId,
        Guid createdBy,
        DateTime createdAt,
        TagType type,
        string? description = null,
        Dictionary<string, object>? metadata = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DocumentId = documentId;
        VersionId = versionId ?? throw new ArgumentNullException(nameof(versionId));
        Description = description;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        Type = type;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class TagCreateRequest
{
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public Guid CreatedBy { get; private set; }
    public TagType Type { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public TagCreateRequest(
        string name,
        Guid createdBy,
        TagType type = TagType.General,
        string? description = null,
        Dictionary<string, object>? metadata = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description;
        CreatedBy = createdBy;
        Type = type;
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}

public class RollbackResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? NewVersionId { get; private set; }
    public string RolledBackToVersionId { get; private set; }
    public Guid RolledBackBy { get; private set; }
    public DateTime RolledBackAt { get; private set; }
    public List<string> AffectedFiles { get; private set; }

    private RollbackResult()
    {
        RolledBackToVersionId = string.Empty;
        AffectedFiles = new List<string>();
    }

    public static RollbackResult Success(
        string newVersionId,
        string rolledBackToVersionId,
        Guid rolledBackBy,
        List<string> affectedFiles)
    {
        return new RollbackResult
        {
            IsSuccessful = true,
            NewVersionId = newVersionId,
            RolledBackToVersionId = rolledBackToVersionId,
            RolledBackBy = rolledBackBy,
            RolledBackAt = DateTime.UtcNow,
            AffectedFiles = affectedFiles ?? new List<string>()
        };
    }

    public static RollbackResult Failure(string errorMessage, string rolledBackToVersionId)
    {
        return new RollbackResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            RolledBackToVersionId = rolledBackToVersionId,
            RolledBackAt = DateTime.UtcNow,
            AffectedFiles = new List<string>()
        };
    }
}

public class RestoreResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? RestoredVersionId { get; private set; }
    public string BackupId { get; private set; }
    public Guid RestoredBy { get; private set; }
    public DateTime RestoredAt { get; private set; }
    public RestoreStatistics Statistics { get; private set; }

    private RestoreResult()
    {
        BackupId = string.Empty;
        Statistics = new RestoreStatistics();
    }

    public static RestoreResult Success(
        string restoredVersionId,
        string backupId,
        Guid restoredBy,
        RestoreStatistics statistics)
    {
        return new RestoreResult
        {
            IsSuccessful = true,
            RestoredVersionId = restoredVersionId,
            BackupId = backupId,
            RestoredBy = restoredBy,
            RestoredAt = DateTime.UtcNow,
            Statistics = statistics ?? new RestoreStatistics()
        };
    }

    public static RestoreResult Failure(string errorMessage, string backupId)
    {
        return new RestoreResult
        {
            IsSuccessful = false,
            ErrorMessage = errorMessage,
            BackupId = backupId,
            RestoredAt = DateTime.UtcNow,
            Statistics = new RestoreStatistics()
        };
    }
}

public class RestoreStatistics
{
    public int FilesRestored { get; private set; }
    public long BytesRestored { get; private set; }
    public TimeSpan RestoreTime { get; private set; }

    public RestoreStatistics(int filesRestored = 0, long bytesRestored = 0, TimeSpan restoreTime = default)
    {
        FilesRestored = filesRestored;
        BytesRestored = bytesRestored;
        RestoreTime = restoreTime;
    }
}

public class BackupPoint
{
    public string BackupId { get; private set; }
    public Guid DocumentId { get; private set; }
    public string VersionId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public BackupType Type { get; private set; }
    public long SizeBytes { get; private set; }
    public string? Description { get; private set; }
    public BackupStatus Status { get; private set; }
    public DateTime? ExpiresAt { get; private set; }

    public BackupPoint(
        string backupId,
        Guid documentId,
        string versionId,
        DateTime createdAt,
        Guid createdBy,
        BackupType type,
        long sizeBytes,
        BackupStatus status,
        string? description = null,
        DateTime? expiresAt = null)
    {
        BackupId = backupId ?? throw new ArgumentNullException(nameof(backupId));
        DocumentId = documentId;
        VersionId = versionId ?? throw new ArgumentNullException(nameof(versionId));
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        Type = type;
        SizeBytes = sizeBytes;
        Description = description;
        Status = status;
        ExpiresAt = expiresAt;
    }
}

public class VersionAnalytics
{
    public Guid DocumentId { get; private set; }
    public TimeSpan Period { get; private set; }
    public int TotalVersions { get; private set; }
    public int TotalBranches { get; private set; }
    public int TotalMerges { get; private set; }
    public int TotalConflicts { get; private set; }
    public Dictionary<Guid, int> VersionsByAuthor { get; private set; }
    public Dictionary<string, int> VersionsByBranch { get; private set; }
    public List<VersionTrend> Trends { get; private set; }
    public VersionFrequency Frequency { get; private set; }

    public VersionAnalytics(
        Guid documentId,
        TimeSpan period,
        int totalVersions,
        int totalBranches,
        int totalMerges,
        int totalConflicts,
        Dictionary<Guid, int> versionsByAuthor,
        Dictionary<string, int> versionsByBranch,
        List<VersionTrend> trends,
        VersionFrequency frequency)
    {
        DocumentId = documentId;
        Period = period;
        TotalVersions = totalVersions;
        TotalBranches = totalBranches;
        TotalMerges = totalMerges;
        TotalConflicts = totalConflicts;
        VersionsByAuthor = versionsByAuthor ?? new Dictionary<Guid, int>();
        VersionsByBranch = versionsByBranch ?? new Dictionary<string, int>();
        Trends = trends ?? new List<VersionTrend>();
        Frequency = frequency ?? new VersionFrequency();
    }
}

public class VersionTrend
{
    public DateTime Date { get; private set; }
    public int VersionCount { get; private set; }
    public int MergeCount { get; private set; }
    public int ConflictCount { get; private set; }

    public VersionTrend(DateTime date, int versionCount, int mergeCount, int conflictCount)
    {
        Date = date;
        VersionCount = versionCount;
        MergeCount = mergeCount;
        ConflictCount = conflictCount;
    }
}

public class VersionFrequency
{
    public double AverageVersionsPerDay { get; private set; }
    public double AverageMergesPerDay { get; private set; }
    public double AverageConflictsPerMerge { get; private set; }

    public VersionFrequency(
        double averageVersionsPerDay = 0,
        double averageMergesPerDay = 0,
        double averageConflictsPerMerge = 0)
    {
        AverageVersionsPerDay = averageVersionsPerDay;
        AverageMergesPerDay = averageMergesPerDay;
        AverageConflictsPerMerge = averageConflictsPerMerge;
    }
}

public class VersionActivity
{
    public string ActivityId { get; private set; }
    public Guid DocumentId { get; private set; }
    public ActivityType Type { get; private set; }
    public Guid UserId { get; private set; }
    public DateTime Timestamp { get; private set; }
    public string? Description { get; private set; }
    public Dictionary<string, object> Details { get; private set; }

    public VersionActivity(
        string activityId,
        Guid documentId,
        ActivityType type,
        Guid userId,
        DateTime timestamp,
        string? description = null,
        Dictionary<string, object>? details = null)
    {
        ActivityId = activityId ?? throw new ArgumentNullException(nameof(activityId));
        DocumentId = documentId;
        Type = type;
        UserId = userId;
        Timestamp = timestamp;
        Description = description;
        Details = details ?? new Dictionary<string, object>();
    }
}

public class CollaborationInsights
{
    public Guid DocumentId { get; private set; }
    public TimeSpan Period { get; private set; }
    public int TotalCollaborators { get; private set; }
    public int ActiveCollaborators { get; private set; }
    public Dictionary<Guid, CollaboratorMetrics> CollaboratorMetrics { get; private set; }
    public List<CollaborationPattern> Patterns { get; private set; }
    public double CollaborationScore { get; private set; }

    public CollaborationInsights(
        Guid documentId,
        TimeSpan period,
        int totalCollaborators,
        int activeCollaborators,
        Dictionary<Guid, CollaboratorMetrics> collaboratorMetrics,
        List<CollaborationPattern> patterns,
        double collaborationScore)
    {
        DocumentId = documentId;
        Period = period;
        TotalCollaborators = totalCollaborators;
        ActiveCollaborators = activeCollaborators;
        CollaboratorMetrics = collaboratorMetrics ?? new Dictionary<Guid, CollaboratorMetrics>();
        Patterns = patterns ?? new List<CollaborationPattern>();
        CollaborationScore = Math.Max(0, Math.Min(100, collaborationScore));
    }
}

public class CollaboratorMetrics
{
    public Guid UserId { get; private set; }
    public int VersionsCreated { get; private set; }
    public int MergesPerformed { get; private set; }
    public int ConflictsResolved { get; private set; }
    public DateTime LastActivity { get; private set; }
    public double ContributionPercentage { get; private set; }

    public CollaboratorMetrics(
        Guid userId,
        int versionsCreated,
        int mergesPerformed,
        int conflictsResolved,
        DateTime lastActivity,
        double contributionPercentage)
    {
        UserId = userId;
        VersionsCreated = versionsCreated;
        MergesPerformed = mergesPerformed;
        ConflictsResolved = conflictsResolved;
        LastActivity = lastActivity;
        ContributionPercentage = Math.Max(0, Math.Min(100, contributionPercentage));
    }
}

public class CollaborationPattern
{
    public PatternType Type { get; private set; }
    public string Description { get; private set; }
    public double Frequency { get; private set; }
    public List<Guid> InvolvedUsers { get; private set; }

    public CollaborationPattern(
        PatternType type,
        string description,
        double frequency,
        List<Guid> involvedUsers)
    {
        Type = type;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Frequency = frequency;
        InvolvedUsers = involvedUsers ?? new List<Guid>();
    }
}
