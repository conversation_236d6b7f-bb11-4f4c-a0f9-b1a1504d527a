using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.ValueObjects;

namespace NetworkFleetManagement.Application.DTOs;

// Main DTOs for Preferred Partner Management
public class PreferredPartnerDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public string PreferenceLevel { get; set; } = string.Empty;
    public int Priority { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ActivatedAt { get; set; }
    public DateTime? DeactivatedAt { get; set; }
    public string? DeactivationReason { get; set; }
    public string? Notes { get; set; }

    // Performance metrics
    public PartnerPerformanceMetricsDto PerformanceMetrics { get; set; } = new();

    // Financial details
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool IsExclusive { get; set; }
    public DateTime? ExclusivityExpiresAt { get; set; }

    // Agreement details
    public string? AgreementNumber { get; set; }
    public DateTime? AgreementStartDate { get; set; }
    public DateTime? AgreementEndDate { get; set; }
    public int? PaymentTermsDays { get; set; }
    public string? SpecialTerms { get; set; }

    // Auto-assignment settings
    public bool AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> PreferredLoadTypes { get; set; } = new();
    public List<string> ExcludedRoutes { get; set; } = new();
    public List<string> ExcludedLoadTypes { get; set; } = new();

    // Notification preferences
    public bool NotifyOnNewOpportunities { get; set; }
    public bool NotifyOnPerformanceChanges { get; set; }
    public bool NotifyOnContractExpiry { get; set; }

    // Calculated properties
    public bool IsActive { get; set; }
    public bool IsAgreementValid { get; set; }
    public bool IsExclusivityActive { get; set; }
    public bool CanAutoAssign { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
}

public class PreferredPartnerSummaryDto
{
    public Guid Id { get; set; }
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public string PreferenceLevel { get; set; } = string.Empty;
    public int Priority { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public decimal OnTimePerformance { get; set; }
    public int TotalCollaborations { get; set; }
    public decimal SuccessRate { get; set; }
    public bool IsActive { get; set; }
    public bool AutoAssignEnabled { get; set; }
    public DateTime? LastCollaboration { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
}

public class PartnerPerformanceMetricsDto
{
    public decimal OverallRating { get; set; }
    public decimal OnTimePerformance { get; set; }
    public decimal QualityRating { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal CostEffectiveness { get; set; }
    public decimal ReliabilityScore { get; set; }

    public int TotalCollaborations { get; set; }
    public int SuccessfulCollaborations { get; set; }
    public int FailedCollaborations { get; set; }
    public int CancelledCollaborations { get; set; }

    public decimal TotalRevenue { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal PaymentTimeliness { get; set; }

    public decimal AverageResponseTimeHours { get; set; }
    public decimal ServiceCompletionRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }

    public int TotalIssues { get; set; }
    public int ResolvedIssues { get; set; }
    public decimal IssueResolutionRate { get; set; }
    public decimal AverageIssueResolutionTimeHours { get; set; }

    public decimal PerformanceTrend { get; set; }
    public DateTime LastUpdated { get; set; }
    public DateTime LastCollaboration { get; set; }

    // Calculated properties
    public decimal SuccessRate { get; set; }
    public decimal FailureRate { get; set; }
    public decimal CancellationRate { get; set; }
    public bool IsActivePartner { get; set; }
    public bool IsHighPerformer { get; set; }
    public bool IsReliablePartner { get; set; }
    public bool IsImprovingPerformance { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
}

public class PartnerPerformanceRecordDto
{
    public Guid Id { get; set; }
    public Guid PreferredPartnerId { get; set; }
    public PartnerPerformanceMetricsDto Metrics { get; set; } = new();
    public DateTime RecordedAt { get; set; }
    public string? Notes { get; set; }
    public string? RecordedBy { get; set; }
    public string RecordType { get; set; } = string.Empty;
    public string? TriggerEvent { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
    public PartnerPerformanceMetricsDto? PreviousMetrics { get; set; }
    public List<string> ChangedFields { get; set; } = new();
    public decimal? PerformanceChange { get; set; }
    public bool IsSignificantChange { get; set; }
    public bool IsImprovement { get; set; }
    public bool IsDegradation { get; set; }
    public string ChangeSummary { get; set; } = string.Empty;
}

// Analytics DTOs
public class PreferredPartnerAnalyticsDto
{
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }

    public int TotalPreferredPartners { get; set; }
    public int ActivePartners { get; set; }
    public int InactivePartners { get; set; }
    public int HighPerformingPartners { get; set; }
    public int RiskyPartners { get; set; }

    public decimal AveragePartnerRating { get; set; }
    public decimal AverageOnTimePerformance { get; set; }
    public decimal AverageSuccessRate { get; set; }
    public decimal TotalCollaborationRevenue { get; set; }
    public int TotalCollaborations { get; set; }

    public List<PreferredPartnerSummaryDto> TopPerformingPartners { get; set; } = new();
    public List<PreferredPartnerSummaryDto> RiskyPartners { get; set; } = new();
    public List<PartnerTypeAnalyticsDto> AnalyticsByPartnerType { get; set; } = new();
    public List<PartnerPerformanceTrendDto> PerformanceTrends { get; set; } = new();
    public List<PartnerCollaborationSummaryDto> RecentCollaborations { get; set; } = new();
}

// ===== ENHANCED SHIPPER PORTAL ANALYTICS DTOs =====

public class PartnerPerformanceAnalyticsDto
{
    public Guid UserId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public DateRangeDto AnalysisPeriod { get; set; } = new();
    public string Scope { get; set; } = string.Empty;
    public int TotalPartnersAnalyzed { get; set; }

    public OverallPerformanceMetricsDto OverallMetrics { get; set; } = new();
    public List<PartnerPerformanceSummaryDto> PartnerPerformances { get; set; } = new();
    public List<PartnerPerformanceSummaryDto> TopPerformers { get; set; } = new();
    public List<PartnerPerformanceSummaryDto> UnderPerformers { get; set; } = new();
    public List<PerformanceTrendDto> PerformanceTrends { get; set; } = new();
    public BenchmarkingDataDto? BenchmarkingData { get; set; }
    public List<PredictiveInsightDto> PredictiveInsights { get; set; } = new();
    public List<PartnerRecommendationDto> Recommendations { get; set; } = new();
}

public class OverallPerformanceMetricsDto
{
    public decimal AverageOnTimeDeliveryRate { get; set; }
    public decimal AverageCustomerSatisfactionScore { get; set; }
    public decimal AverageCommunicationRating { get; set; }
    public decimal AverageSafetyRating { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
    public int TotalTripsCompleted { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal OverallCompletionRate { get; set; }
    public int HighPerformersCount { get; set; }
    public int MediumPerformersCount { get; set; }
    public int LowPerformersCount { get; set; }
}

public class PartnerPerformanceSummaryDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public string PreferenceLevel { get; set; } = string.Empty;
    public int Priority { get; set; }
    public decimal OverallScore { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal CommunicationRating { get; set; }
    public decimal SafetyRating { get; set; }
    public int TotalTripsCompleted { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
    public DateTime? LastPerformanceUpdate { get; set; }
    public bool IsPreferredPartner { get; set; }
    public string PerformanceTrend { get; set; } = string.Empty; // Improving, Declining, Stable
    public string RiskLevel { get; set; } = string.Empty; // Low, Medium, High
    public List<string> Strengths { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public decimal? PredictedNextMonthScore { get; set; }
}

public class PerformanceTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public List<TrendDataPointDto> DataPoints { get; set; } = new();
    public string TrendDirection { get; set; } = string.Empty; // Up, Down, Stable
    public decimal TrendPercentage { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly
}

public class TrendDataPointDto
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public string? Label { get; set; }
}

public class BenchmarkingDataDto
{
    public decimal IndustryAverageOnTimeDelivery { get; set; }
    public decimal IndustryAverageCustomerSatisfaction { get; set; }
    public decimal IndustryAverageSafetyRating { get; set; }
    public decimal IndustryAverageResponseTime { get; set; }
    public string ComparisonSummary { get; set; } = string.Empty;
    public List<BenchmarkComparisonDto> Comparisons { get; set; } = new();
}

public class BenchmarkComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal YourAverage { get; set; }
    public decimal IndustryAverage { get; set; }
    public decimal Difference { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty; // Above Average, Below Average, Average
}

public class PredictiveInsightDto
{
    public string InsightType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public DateTime PredictionDate { get; set; }
    public string Impact { get; set; } = string.Empty; // High, Medium, Low
    public List<Guid> AffectedPartnerIds { get; set; } = new();
    public Dictionary<string, object> PredictionData { get; set; } = new();
}

public class PartnerRecommendationDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public bool ActionRequired { get; set; }
    public DateTime? DueDate { get; set; }
    public List<Guid> AffectedPartnerIds { get; set; } = new();
    public List<string> SuggestedActions { get; set; } = new();
    public string? ExpectedOutcome { get; set; }
}

public class DateRangeDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalDays => (EndDate - StartDate).Days;
}

// ===== PARTNER RECOMMENDATIONS DTOs =====

public class PartnerRecommendationsDto
{
    public Guid UserId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string Criteria { get; set; } = string.Empty;
    public int TotalRecommendations { get; set; }
    public List<PartnerRecommendationItemDto> Recommendations { get; set; } = new();
    public List<RecommendationInsightDto> Insights { get; set; } = new();
    public List<ActionSuggestionDto> Suggestions { get; set; } = new();
}

public class PartnerRecommendationItemDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public decimal RecommendationScore { get; set; }
    public decimal MatchPercentage { get; set; }
    public string RecommendationReason { get; set; } = string.Empty;
    public MoneyDto? EstimatedCost { get; set; }
    public TimeSpan? EstimatedDeliveryTime { get; set; }
    public decimal PerformanceRating { get; set; }
    public decimal SafetyRating { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public List<string> ServiceAreas { get; set; } = new();
    public List<string> VehicleTypes { get; set; } = new();
    public bool IsNewPartner { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime? NextAvailableDate { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Considerations { get; set; } = new();
    public PartnerPerformanceAnalysisDto? PerformanceAnalysis { get; set; }
}

public class PartnerPerformanceAnalysisDto
{
    public int TotalTripsCompleted { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
    public decimal ReliabilityScore { get; set; }
    public decimal QualityScore { get; set; }
    public decimal CostEfficiencyRating { get; set; }
    public string PerformanceTrend { get; set; } = string.Empty;
    public DateTime? LastPerformanceUpdate { get; set; }
}

public class RecommendationInsightDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty; // Positive, Negative, Neutral
}

public class ActionSuggestionDto
{
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // High, Medium, Low
    public List<Guid> PartnerIds { get; set; } = new();
    public string? ExpectedBenefit { get; set; }
}

public class PartnerTypeAnalyticsDto
{
    public string PartnerType { get; set; } = string.Empty;
    public int TotalPartners { get; set; }
    public int ActivePartners { get; set; }
    public decimal AverageRating { get; set; }
    public decimal AverageSuccessRate { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalCollaborations { get; set; }
    public decimal AverageResponseTimeHours { get; set; }
}

public class PartnerPerformanceTrendDto
{
    public DateTime Date { get; set; }
    public decimal AverageRating { get; set; }
    public decimal AverageOnTimePerformance { get; set; }
    public decimal AverageSuccessRate { get; set; }
    public int TotalCollaborations { get; set; }
    public int ActivePartners { get; set; }
}

public class PartnerCollaborationSummaryDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public DateTime CollaborationDate { get; set; }
    public string CollaborationType { get; set; } = string.Empty; // Order, Trip, etc.
    public Guid CollaborationId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal Rating { get; set; }
    public bool OnTime { get; set; }
    public bool Successful { get; set; }
}

// Individual Partner Analytics DTO
public class PartnerAnalyticsDto
{
    public Guid PreferredPartnerId { get; set; }
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public DateTime AnalysisPeriodStart { get; set; }
    public DateTime AnalysisPeriodEnd { get; set; }

    // Performance Metrics
    public PartnerPerformanceMetricsDto PerformanceMetrics { get; set; } = new();

    // Collaboration Statistics
    public int TotalCollaborations { get; set; }
    public int CompletedCollaborations { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageOrderValue { get; set; }

    // Time-based Analytics
    public List<MonthlyCollaborationDto> MonthlyBreakdown { get; set; } = new();
    public List<RoutePerformanceDto> RoutePerformance { get; set; } = new();
    public List<LoadTypePerformanceDto> LoadTypePerformance { get; set; } = new();

    // Trends and Insights
    public decimal PerformanceTrend { get; set; } // Positive = improving, Negative = declining
    public string TrendDescription { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
}

public class MonthlyCollaborationDto
{
    public int Year { get; set; }
    public int Month { get; set; }
    public string MonthName { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AverageRating { get; set; }
}

public class RoutePerformanceDto
{
    public string Route { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal AverageRating { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public decimal TotalValue { get; set; }
}

public class LoadTypePerformanceDto
{
    public string LoadType { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public decimal CompletionRate { get; set; }
    public decimal AverageRating { get; set; }
    public decimal TotalValue { get; set; }
    public bool IsSpecialty { get; set; }
}

// Dashboard DTOs
public class PreferredPartnerDashboardDto
{
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }

    // Summary Statistics
    public int TotalPreferredPartners { get; set; }
    public int ActivePreferredPartners { get; set; }
    public int InactivePreferredPartners { get; set; }
    public decimal AveragePartnerRating { get; set; }

    // Partner Type Breakdown
    public List<PartnerTypeStatsDto> PartnerTypeStats { get; set; } = new();

    // Recent Activity
    public List<RecentPartnerActivityDto> RecentActivities { get; set; } = new();

    // Performance Insights
    public List<PerformanceInsightDto> PerformanceInsights { get; set; } = new();

    // Top Performing Partners
    public List<TopPartnerDto> TopPerformingPartners { get; set; } = new();

    // Alerts and Notifications
    public List<PartnerAlertDto> Alerts { get; set; } = new();
}

public class PartnerTypeStatsDto
{
    public string PartnerType { get; set; } = string.Empty;
    public int TotalCount { get; set; }
    public int ActiveCount { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalCollaborations { get; set; }
    public decimal TotalValue { get; set; }
}

public class RecentPartnerActivityDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string ActivityType { get; set; } = string.Empty; // Added, Activated, Deactivated, etc.
    public DateTime ActivityDate { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class PerformanceInsightDto
{
    public string InsightType { get; set; } = string.Empty; // Trend, Alert, Recommendation
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // Info, Warning, Critical
    public DateTime GeneratedAt { get; set; }
}

public class TopPartnerDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public decimal Rating { get; set; }
    public int TotalCollaborations { get; set; }
    public decimal TotalValue { get; set; }
    public string Rank { get; set; } = string.Empty; // #1, #2, etc.
}

public class PartnerAlertDto
{
    public Guid AlertId { get; set; }
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty; // Performance, Contract, etc.
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
}

// Recommendation DTOs
public class PartnerRecommendationDto
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = string.Empty;
    public string PartnerType { get; set; } = string.Empty;
    public decimal RecommendationScore { get; set; }
    public string RecommendationReason { get; set; } = string.Empty;
    public List<string> MatchingCriteria { get; set; } = new();
    public PartnerPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public bool IsCurrentlyPreferred { get; set; }
    public string ContactInfo { get; set; } = string.Empty;
}

// Auto-Assignment DTOs
public class AutoAssignmentSettingsDto
{
    public Guid PreferredPartnerId { get; set; }
    public bool AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public List<string> PreferredRoutes { get; set; } = new();
    public List<string> PreferredLoadTypes { get; set; } = new();
    public List<string> ExcludedRoutes { get; set; } = new();
    public List<string> ExcludedLoadTypes { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class UpdateAutoAssignmentSettingsDto
{
    public bool AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
}

public class TestAutoAssignmentRequest
{
    public string PartnerType { get; set; } = string.Empty;
    public string? Route { get; set; }
    public string? LoadType { get; set; }
    public decimal? MinRating { get; set; }
    public int MaxResults { get; set; } = 10;
}

public class AutoAssignmentStatisticsDto
{
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }

    // Overall Statistics
    public int TotalAutoAssignments { get; set; }
    public int SuccessfulAutoAssignments { get; set; }
    public int FailedAutoAssignments { get; set; }
    public decimal SuccessRate { get; set; }

    // Performance Metrics
    public decimal AverageAssignmentTime { get; set; } // In minutes
    public decimal AveragePartnerRating { get; set; }
    public decimal TotalValueAssigned { get; set; }

    // Breakdown by Partner Type
    public List<AutoAssignmentByTypeDto> BreakdownByType { get; set; } = new();

    // Breakdown by Route
    public List<AutoAssignmentByRouteDto> BreakdownByRoute { get; set; } = new();

    // Breakdown by Load Type
    public List<AutoAssignmentByLoadTypeDto> BreakdownByLoadType { get; set; } = new();

    // Trends
    public List<AutoAssignmentTrendDto> DailyTrends { get; set; } = new();
}

public class AutoAssignmentByTypeDto
{
    public string PartnerType { get; set; } = string.Empty;
    public int TotalAssignments { get; set; }
    public int SuccessfulAssignments { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageRating { get; set; }
}

public class AutoAssignmentByRouteDto
{
    public string Route { get; set; } = string.Empty;
    public int TotalAssignments { get; set; }
    public int SuccessfulAssignments { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageValue { get; set; }
}

public class AutoAssignmentByLoadTypeDto
{
    public string LoadType { get; set; } = string.Empty;
    public int TotalAssignments { get; set; }
    public int SuccessfulAssignments { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageValue { get; set; }
}

public class AutoAssignmentTrendDto
{
    public DateTime Date { get; set; }
    public int TotalAssignments { get; set; }
    public int SuccessfulAssignments { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageAssignmentTime { get; set; }
}

// Command DTOs
public class CreatePreferredPartnerDto
{
    public Guid PartnerId { get; set; }
    public string PartnerType { get; set; } = string.Empty;
    public string PreferenceLevel { get; set; } = string.Empty;
    public int Priority { get; set; } = 10;
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool AutoAssignEnabled { get; set; } = false;
    public string? Notes { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
}

public class UpdatePreferredPartnerDto
{
    public string? PreferenceLevel { get; set; }
    public int? Priority { get; set; }
    public decimal? PreferredCommissionRate { get; set; }
    public decimal? PreferredServiceRate { get; set; }
    public bool? AutoAssignEnabled { get; set; }
    public decimal? AutoAssignThreshold { get; set; }
    public string? Notes { get; set; }
    public List<string>? PreferredRoutes { get; set; }
    public List<string>? PreferredLoadTypes { get; set; }
    public List<string>? ExcludedRoutes { get; set; }
    public List<string>? ExcludedLoadTypes { get; set; }
    public bool? NotifyOnNewOpportunities { get; set; }
    public bool? NotifyOnPerformanceChanges { get; set; }
    public bool? NotifyOnContractExpiry { get; set; }
}

public class UpdatePartnerPerformanceDto
{
    public decimal? OverallRating { get; set; }
    public decimal? OnTimePerformance { get; set; }
    public decimal? QualityRating { get; set; }
    public decimal? CommunicationRating { get; set; }
    public decimal? CostEffectiveness { get; set; }
    public decimal? ReliabilityScore { get; set; }
    public int? AdditionalCollaborations { get; set; }
    public int? AdditionalSuccessful { get; set; }
    public int? AdditionalFailed { get; set; }
    public int? AdditionalCancelled { get; set; }
    public decimal? AdditionalRevenue { get; set; }
    public decimal? NewAverageOrderValue { get; set; }
    public decimal? NewPaymentTimeliness { get; set; }
    public decimal? NewAverageResponseTime { get; set; }
    public decimal? NewServiceCompletionRate { get; set; }
    public decimal? NewCustomerSatisfactionScore { get; set; }
    public int? AdditionalIssues { get; set; }
    public int? AdditionalResolvedIssues { get; set; }
    public decimal? NewAverageIssueResolutionTime { get; set; }
    public decimal? NewPerformanceTrend { get; set; }
    public string? Notes { get; set; }
    public string? TriggerEvent { get; set; }
    public Guid? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

// Query DTOs
public class PreferredPartnerQueryDto
{
    public Guid? UserId { get; set; }
    public List<string>? PartnerTypes { get; set; }
    public List<string>? PreferenceLevels { get; set; }
    public List<string>? Statuses { get; set; }
    public bool? AutoAssignEnabled { get; set; }
    public bool? IsActive { get; set; }
    public decimal? MinRating { get; set; }
    public decimal? MaxRating { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? LastCollaborationAfter { get; set; }
    public DateTime? LastCollaborationBefore { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; } = "Priority";
    public string? SortDirection { get; set; } = "asc";
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

// Pagination result
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
