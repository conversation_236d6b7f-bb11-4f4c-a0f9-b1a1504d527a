using FluentAssertions;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;
using NetworkFleetManagement.Domain.ValueObjects;
using Xunit;

namespace NetworkFleetManagement.Tests.Domain.Entities;

public class CarrierTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateCarrier()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var companyName = "Test Logistics";
        var contactPersonName = "John Doe";
        var email = "<EMAIL>";
        var phoneNumber = "+1234567890";

        // Act
        var carrier = new Carrier(userId, companyName, contactPersonName, email, phoneNumber);

        // Assert
        carrier.UserId.Should().Be(userId);
        carrier.CompanyName.Should().Be(companyName);
        carrier.ContactPersonName.Should().Be(contactPersonName);
        carrier.Email.Should().Be(email);
        carrier.PhoneNumber.Should().Be(phoneNumber);
        carrier.Status.Should().Be(CarrierStatus.Pending);
        carrier.OnboardingStatus.Should().Be(OnboardingStatus.NotStarted);
        carrier.IsActive.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Constructor_WithInvalidCompanyName_ShouldThrowArgumentException(string companyName)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var contactPersonName = "John Doe";
        var email = "<EMAIL>";
        var phoneNumber = "+1234567890";

        // Act & Assert
        var action = () => new Carrier(userId, companyName, contactPersonName, email, phoneNumber);
        action.Should().Throw<ArgumentException>().WithMessage("Company name cannot be empty*");
    }

    [Fact]
    public void UpdateStatus_ToActive_ShouldSetVerifiedAt()
    {
        // Arrange
        var carrier = CreateTestCarrier();
        var originalVerifiedAt = carrier.VerifiedAt;

        // Act
        carrier.UpdateStatus(CarrierStatus.Active);

        // Assert
        carrier.Status.Should().Be(CarrierStatus.Active);
        carrier.IsActive.Should().BeTrue();
        carrier.VerifiedAt.Should().NotBe(originalVerifiedAt);
        carrier.VerifiedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void UpdateStatus_FromActiveToSuspended_ShouldNotChangeVerifiedAt()
    {
        // Arrange
        var carrier = CreateTestCarrier();
        carrier.UpdateStatus(CarrierStatus.Active);
        var verifiedAt = carrier.VerifiedAt;

        // Act
        carrier.UpdateStatus(CarrierStatus.Suspended);

        // Assert
        carrier.Status.Should().Be(CarrierStatus.Suspended);
        carrier.IsActive.Should().BeFalse();
        carrier.VerifiedAt.Should().Be(verifiedAt);
    }

    [Fact]
    public void UpdateOnboardingStatus_ShouldUpdateStatus()
    {
        // Arrange
        var carrier = CreateTestCarrier();

        // Act
        carrier.UpdateOnboardingStatus(OnboardingStatus.DocumentsUploaded);

        // Assert
        carrier.OnboardingStatus.Should().Be(OnboardingStatus.DocumentsUploaded);
    }

    [Fact]
    public void UpdatePerformanceMetrics_ShouldUpdateMetrics()
    {
        // Arrange
        var carrier = CreateTestCarrier();
        var newMetrics = new PerformanceMetrics(4.5m, 100, 95, 5, 95.0m, 4.3m);

        // Act
        carrier.UpdatePerformanceMetrics(newMetrics);

        // Assert
        carrier.PerformanceMetrics.Should().Be(newMetrics);
        carrier.PerformanceMetrics.Rating.Should().Be(4.5m);
        carrier.PerformanceMetrics.TotalTrips.Should().Be(100);
        carrier.PerformanceMetrics.CompletedTrips.Should().Be(95);
    }

    [Fact]
    public void GetAvailableVehicleCount_WithMixedVehicleStatuses_ShouldReturnCorrectCount()
    {
        // Arrange
        var carrier = CreateTestCarrier();
        var vehicle1 = CreateTestVehicle(carrier.Id, VehicleStatus.Available);
        var vehicle2 = CreateTestVehicle(carrier.Id, VehicleStatus.Available);
        var vehicle3 = CreateTestVehicle(carrier.Id, VehicleStatus.InUse);

        carrier.AddVehicle(vehicle1);
        carrier.AddVehicle(vehicle2);
        carrier.AddVehicle(vehicle3);

        // Act
        var availableCount = carrier.GetAvailableVehicleCount();

        // Assert
        availableCount.Should().Be(2);
    }

    [Fact]
    public void GetAvailableDriverCount_WithMixedDriverStatuses_ShouldReturnCorrectCount()
    {
        // Arrange
        var carrier = CreateTestCarrier();
        var driver1 = CreateTestDriver(carrier.Id, DriverStatus.Available);
        var driver2 = CreateTestDriver(carrier.Id, DriverStatus.Available);
        var driver3 = CreateTestDriver(carrier.Id, DriverStatus.OnTrip);

        carrier.AddDriver(driver1);
        carrier.AddDriver(driver2);
        carrier.AddDriver(driver3);

        // Act
        var availableCount = carrier.GetAvailableDriverCount();

        // Assert
        availableCount.Should().Be(2);
    }

    private static Carrier CreateTestCarrier()
    {
        return new Carrier(
            Guid.NewGuid(),
            "Test Logistics",
            "John Doe",
            "<EMAIL>",
            "+1234567890");
    }

    private static Vehicle CreateTestVehicle(Guid carrierId, VehicleStatus status)
    {
        var specifications = new VehicleSpecifications(1000, 10);
        var vehicle = new Vehicle(
            carrierId,
            "TEST123",
            VehicleType.Truck,
            "Tata",
            "407",
            2020,
            "White",
            specifications);

        vehicle.UpdateStatus(status);
        return vehicle;
    }

    private static Driver CreateTestDriver(Guid carrierId, DriverStatus status)
    {
        var driver = new Driver(
            carrierId,
            Guid.NewGuid(),
            "Test",
            "Driver",
            "+1234567890",
            "<EMAIL>",
            "DL123456",
            DateTime.UtcNow.AddYears(1));

        driver.UpdateStatus(status);
        return driver;
    }
}
