using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ThumbnailOptions
{
    public int Width { get; private set; }
    public int Height { get; private set; }
    public ImageFormat Format { get; private set; }
    public int Quality { get; private set; }
    public bool MaintainAspectRatio { get; private set; }

    public ThumbnailOptions(
        int width = 200,
        int height = 200,
        ImageFormat format = ImageFormat.JPEG,
        int quality = 85,
        bool maintainAspectRatio = true)
    {
        Width = Math.Max(1, width);
        Height = Math.Max(1, height);
        Format = format;
        Quality = Math.Max(1, Math.Min(100, quality));
        MaintainAspectRatio = maintainAspectRatio;
    }
}

public class MediaQualityAnalysis
{
    public MediaType MediaType { get; private set; }
    public QualityScore OverallScore { get; private set; }
    public VideoQualityMetrics? VideoMetrics { get; private set; }
    public AudioQualityMetrics? AudioMetrics { get; private set; }
    public List<QualityIssue> Issues { get; private set; }
    public List<QualityRecommendation> Recommendations { get; private set; }

    public MediaQualityAnalysis(
        MediaType mediaType,
        QualityScore overallScore,
        VideoQualityMetrics? videoMetrics = null,
        AudioQualityMetrics? audioMetrics = null,
        List<QualityIssue>? issues = null,
        List<QualityRecommendation>? recommendations = null)
    {
        MediaType = mediaType;
        OverallScore = overallScore;
        VideoMetrics = videoMetrics;
        AudioMetrics = audioMetrics;
        Issues = issues ?? new List<QualityIssue>();
        Recommendations = recommendations ?? new List<QualityRecommendation>();
    }
}

public class QualityScore
{
    public double Value { get; private set; }
    public QualityLevel Level { get; private set; }
    public string Description { get; private set; }

    public QualityScore(double value, string description = "")
    {
        Value = Math.Max(0, Math.Min(100, value));
        Level = GetQualityLevel(Value);
        Description = description;
    }

    private static QualityLevel GetQualityLevel(double score)
    {
        return score switch
        {
            >= 90 => QualityLevel.Excellent,
            >= 80 => QualityLevel.VeryGood,
            >= 70 => QualityLevel.Good,
            >= 60 => QualityLevel.Fair,
            >= 50 => QualityLevel.Poor,
            _ => QualityLevel.VeryPoor
        };
    }
}

public class VideoQualityMetrics
{
    public double Sharpness { get; private set; }
    public double Brightness { get; private set; }
    public double Contrast { get; private set; }
    public double ColorSaturation { get; private set; }
    public double NoiseLevel { get; private set; }
    public double CompressionArtifacts { get; private set; }
    public double MotionBlur { get; private set; }
    public bool HasInterlacing { get; private set; }
    public double FrameDropRate { get; private set; }

    public VideoQualityMetrics(
        double sharpness = 0,
        double brightness = 0,
        double contrast = 0,
        double colorSaturation = 0,
        double noiseLevel = 0,
        double compressionArtifacts = 0,
        double motionBlur = 0,
        bool hasInterlacing = false,
        double frameDropRate = 0)
    {
        Sharpness = Math.Max(0, Math.Min(100, sharpness));
        Brightness = Math.Max(0, Math.Min(100, brightness));
        Contrast = Math.Max(0, Math.Min(100, contrast));
        ColorSaturation = Math.Max(0, Math.Min(100, colorSaturation));
        NoiseLevel = Math.Max(0, Math.Min(100, noiseLevel));
        CompressionArtifacts = Math.Max(0, Math.Min(100, compressionArtifacts));
        MotionBlur = Math.Max(0, Math.Min(100, motionBlur));
        HasInterlacing = hasInterlacing;
        FrameDropRate = Math.Max(0, Math.Min(100, frameDropRate));
    }
}

public class AudioQualityMetrics
{
    public double SignalToNoiseRatio { get; private set; }
    public double DynamicRange { get; private set; }
    public double Distortion { get; private set; }
    public double FrequencyResponse { get; private set; }
    public double Clipping { get; private set; }
    public double SilenceDetection { get; private set; }
    public bool HasMono { get; private set; }
    public double LoudnessLUFS { get; private set; }

    public AudioQualityMetrics(
        double signalToNoiseRatio = 0,
        double dynamicRange = 0,
        double distortion = 0,
        double frequencyResponse = 0,
        double clipping = 0,
        double silenceDetection = 0,
        bool hasMono = false,
        double loudnessLUFS = 0)
    {
        SignalToNoiseRatio = signalToNoiseRatio;
        DynamicRange = dynamicRange;
        Distortion = Math.Max(0, Math.Min(100, distortion));
        FrequencyResponse = Math.Max(0, Math.Min(100, frequencyResponse));
        Clipping = Math.Max(0, Math.Min(100, clipping));
        SilenceDetection = Math.Max(0, Math.Min(100, silenceDetection));
        HasMono = hasMono;
        LoudnessLUFS = loudnessLUFS;
    }
}

public class QualityIssue
{
    public QualityIssueType Type { get; private set; }
    public QualityIssueSeverity Severity { get; private set; }
    public string Description { get; private set; }
    public TimeSpan? Timestamp { get; private set; }
    public Dictionary<string, object> Details { get; private set; }

    public QualityIssue(
        QualityIssueType type,
        QualityIssueSeverity severity,
        string description,
        TimeSpan? timestamp = null,
        Dictionary<string, object>? details = null)
    {
        Type = type;
        Severity = severity;
        Description = description ?? string.Empty;
        Timestamp = timestamp;
        Details = details ?? new Dictionary<string, object>();
    }
}

public class QualityRecommendation
{
    public QualityRecommendationType Type { get; private set; }
    public string Description { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public double ExpectedImprovement { get; private set; }

    public QualityRecommendation(
        QualityRecommendationType type,
        string description,
        Dictionary<string, object>? parameters = null,
        double expectedImprovement = 0)
    {
        Type = type;
        Description = description ?? string.Empty;
        Parameters = parameters ?? new Dictionary<string, object>();
        ExpectedImprovement = Math.Max(0, Math.Min(100, expectedImprovement));
    }
}

public class QualityPreset
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public MediaType MediaType { get; private set; }
    public VideoTranscodingOptions? VideoOptions { get; private set; }
    public AudioTranscodingOptions? AudioOptions { get; private set; }
    public QualityLevel TargetQuality { get; private set; }
    public long EstimatedOutputSize { get; private set; }

    public QualityPreset(
        string name,
        string description,
        MediaType mediaType,
        QualityLevel targetQuality,
        VideoTranscodingOptions? videoOptions = null,
        AudioTranscodingOptions? audioOptions = null,
        long estimatedOutputSize = 0)
    {
        Name = name ?? string.Empty;
        Description = description ?? string.Empty;
        MediaType = mediaType;
        TargetQuality = targetQuality;
        VideoOptions = videoOptions;
        AudioOptions = audioOptions;
        EstimatedOutputSize = estimatedOutputSize;
    }

    // Common presets
    public static QualityPreset WebOptimized => new(
        "Web Optimized",
        "Optimized for web streaming with good quality and small file size",
        MediaType.Video,
        QualityLevel.Good);

    public static QualityPreset HighQuality => new(
        "High Quality",
        "High quality output with larger file size",
        MediaType.Video,
        QualityLevel.VeryGood);

    public static QualityPreset MobileOptimized => new(
        "Mobile Optimized",
        "Optimized for mobile devices with reduced resolution and bitrate",
        MediaType.Video,
        QualityLevel.Fair);

    public static QualityPreset ArchiveQuality => new(
        "Archive Quality",
        "Maximum quality for archival purposes",
        MediaType.Video,
        QualityLevel.Excellent);
}
