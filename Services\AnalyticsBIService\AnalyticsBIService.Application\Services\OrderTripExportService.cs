using Microsoft.Extensions.Logging;
using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Application.Interfaces;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.Services;

public interface IOrderTripExportService
{
    Task<ExportResult> ExportOrdersAsync(OrderExportRequest request, CancellationToken cancellationToken = default);
    Task<ExportResult> ExportTripsAsync(TripExportRequest request, CancellationToken cancellationToken = default);
    Task<ExportResult> ExportOrderTimelineAsync(TimelineExportRequest request, CancellationToken cancellationToken = default);
    Task<ExportResult> ExportPerformanceReportAsync(PerformanceReportRequest request, CancellationToken cancellationToken = default);
    Task<List<ExportTemplate>> GetAvailableTemplatesAsync(ExportType exportType, CancellationToken cancellationToken = default);
    Task<AnalyticsBIService.Domain.Enums.ExportStatus> GetExportStatusAsync(Guid exportId, CancellationToken cancellationToken = default);
    Task<Stream> DownloadExportAsync(Guid exportId, CancellationToken cancellationToken = default);
}

public class OrderTripExportService : IOrderTripExportService
{
    private readonly IOrderDataRepository _orderRepository;
    private readonly ITripDataRepository _tripRepository;
    private readonly IReportGenerationService _reportService;
    private readonly IExportRepository _exportRepository;
    private readonly IFileStorageService _fileStorage;
    private readonly ILogger<OrderTripExportService> _logger;

    public OrderTripExportService(
        IOrderDataRepository orderRepository,
        ITripDataRepository tripRepository,
        IReportGenerationService reportService,
        IExportRepository exportRepository,
        IFileStorageService fileStorage,
        ILogger<OrderTripExportService> logger)
    {
        _orderRepository = orderRepository;
        _tripRepository = tripRepository;
        _reportService = reportService;
        _exportRepository = exportRepository;
        _fileStorage = fileStorage;
        _logger = logger;
    }

    public async Task<ExportResult> ExportOrdersAsync(OrderExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting order export for user {UserId} with format {Format}",
                request.RequestedBy, request.Format);

            // Create export record
            var export = new ExportRecord
            {
                Id = Guid.NewGuid(),
                ExportType = ExportType.Orders,
                Format = request.Format,
                RequestedBy = request.RequestedBy,
                RequestedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                Parameters = request.ToParametersDictionary()
            };

            await _exportRepository.CreateExportRecordAsync(export, cancellationToken);

            try
            {
                // Get order data based on filters
                var orders = await _orderRepository.GetOrdersForExportAsync(new Interfaces.OrderDataFilter
                {
                    FromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-30),
                    ToDate = request.ToDate ?? DateTime.UtcNow,
                    Status = request.Status,
                    TransportCompanyId = request.TransportCompanyId,
                    BrokerId = request.BrokerId,
                    CarrierId = request.CarrierId,
                    IncludeTimeline = request.IncludeTimeline,
                    IncludeDocuments = request.IncludeDocuments,
                    IncludeFinancials = request.IncludeFinancials
                }, cancellationToken);

                // Generate report based on template
                var reportDataString = await _reportService.GenerateOrderReportAsync(orders, request.TemplateId ?? Guid.Empty, cancellationToken);
                var reportData = new ReportData
                {
                    Fields = new Dictionary<string, object> { { "content", reportDataString } },
                    Metadata = new Dictionary<string, object> { { "type", "order_export" }, { "recordCount", orders.Count() } }
                };

                // Create file based on format
                var fileName = $"orders_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}";
                var filePath = await CreateExportFileAsync(reportData, request.Format, fileName, cancellationToken);

                // Update export record
                export.Status = ExportStatus.Completed;
                export.CompletedAt = DateTime.UtcNow;
                export.FilePath = filePath;
                export.FileName = fileName;
                export.RecordCount = orders.Count();
                export.FileSizeBytes = await _fileStorage.GetFileSizeAsync(filePath, cancellationToken);

                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);

                _logger.LogInformation("Order export completed: {ExportId}, {RecordCount} records",
                    export.Id, export.RecordCount);

                return new ExportResult
                {
                    ExportId = export.Id,
                    Status = ExportStatus.Completed.ToString(),
                    FileName = fileName,
                    RecordCount = export.RecordCount,
                    FileSizeBytes = export.FileSizeBytes,
                    DownloadUrl = GenerateDownloadUrl(export.Id),
                    ExpiresAt = DateTime.UtcNow.AddDays(7) // 7 days expiry
                };
            }
            catch (Exception ex)
            {
                export.Status = ExportStatus.Failed;
                export.ErrorMessage = ex.Message;
                export.CompletedAt = DateTime.UtcNow;
                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting orders for user {UserId}", request.RequestedBy);
            throw;
        }
    }

    public async Task<ExportResult> ExportTripsAsync(TripExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting trip export for user {UserId} with format {Format}",
                request.RequestedBy, request.Format);

            var export = new ExportRecord
            {
                Id = Guid.NewGuid(),
                ExportType = ExportType.Trips,
                Format = request.Format,
                RequestedBy = request.RequestedBy,
                RequestedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                Parameters = request.ToParametersDictionary()
            };

            await _exportRepository.CreateExportRecordAsync(export, cancellationToken);

            try
            {
                var trips = await _tripRepository.GetTripsForExportAsync(new Interfaces.TripDataFilter
                {
                    FromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-30),
                    ToDate = request.ToDate ?? DateTime.UtcNow,
                    Status = request.Status,
                    CarrierId = request.CarrierId,
                    DriverId = request.DriverId,
                    VehicleId = request.VehicleId,
                    // IncludeLocationHistory = request.IncludeLocationHistory, // Property not available
                    IncludePerformanceMetrics = request.IncludePerformanceMetrics
                }, cancellationToken);

                var reportDataString = await _reportService.GenerateTripReportAsync(trips, request.TemplateId ?? Guid.Empty, cancellationToken);
                var reportData = new ReportData
                {
                    Fields = new Dictionary<string, object> { { "content", reportDataString } },
                    Metadata = new Dictionary<string, object> { { "type", "trip_export" }, { "recordCount", trips.Count() } }
                };
                var fileName = $"trips_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}";
                var filePath = await CreateExportFileAsync(reportData, request.Format, fileName, cancellationToken);

                export.Status = ExportStatus.Completed;
                export.CompletedAt = DateTime.UtcNow;
                export.FilePath = filePath;
                export.FileName = fileName;
                export.RecordCount = trips.Count();
                export.FileSizeBytes = await _fileStorage.GetFileSizeAsync(filePath, cancellationToken);

                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);

                return new ExportResult
                {
                    ExportId = export.Id,
                    Status = ExportStatus.Completed.ToString(),
                    FileName = fileName,
                    RecordCount = export.RecordCount,
                    FileSizeBytes = export.FileSizeBytes,
                    DownloadUrl = GenerateDownloadUrl(export.Id),
                    ExpiresAt = DateTime.UtcNow.AddDays(7)
                };
            }
            catch (Exception ex)
            {
                export.Status = ExportStatus.Failed;
                export.ErrorMessage = ex.Message;
                export.CompletedAt = DateTime.UtcNow;
                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting trips for user {UserId}", request.RequestedBy);
            throw;
        }
    }

    public async Task<ExportResult> ExportOrderTimelineAsync(TimelineExportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting timeline export for order {OrderId}", request.OrderId);

            var export = new ExportRecord
            {
                Id = Guid.NewGuid(),
                ExportType = ExportType.Timeline,
                Format = request.Format,
                RequestedBy = request.RequestedBy,
                RequestedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                Parameters = request.ToParametersDictionary()
            };

            await _exportRepository.CreateExportRecordAsync(export, cancellationToken);

            try
            {
                var timelineData = await _orderRepository.GetOrderTimelineForExportAsync(request.OrderId ?? Guid.Empty, cancellationToken);
                var reportDataString = await _reportService.GenerateTimelineReportAsync(timelineData, request.TemplateId ?? Guid.Empty, cancellationToken);
                var reportData = new ReportData
                {
                    Fields = new Dictionary<string, object> { { "content", reportDataString } },
                    Metadata = new Dictionary<string, object> { { "type", "timeline_export" }, { "recordCount", timelineData.Count() } }
                };
                var fileName = $"timeline_export_{request.OrderId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}";
                var filePath = await CreateExportFileAsync(reportData, request.Format, fileName, cancellationToken);

                export.Status = ExportStatus.Completed;
                export.CompletedAt = DateTime.UtcNow;
                export.FilePath = filePath;
                export.FileName = fileName;
                export.RecordCount = timelineData.Count();
                export.FileSizeBytes = await _fileStorage.GetFileSizeAsync(filePath, cancellationToken);

                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);

                return new ExportResult
                {
                    ExportId = export.Id,
                    Status = ExportStatus.Completed.ToString(),
                    FileName = fileName,
                    RecordCount = export.RecordCount,
                    FileSizeBytes = export.FileSizeBytes,
                    DownloadUrl = GenerateDownloadUrl(export.Id),
                    ExpiresAt = DateTime.UtcNow.AddDays(7)
                };
            }
            catch (Exception ex)
            {
                export.Status = ExportStatus.Failed;
                export.ErrorMessage = ex.Message;
                export.CompletedAt = DateTime.UtcNow;
                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting timeline for order {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<ExportResult> ExportPerformanceReportAsync(PerformanceReportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting performance report export for user {UserId}", request.RequestedBy);

            var export = new ExportRecord
            {
                Id = Guid.NewGuid(),
                ExportType = ExportType.PerformanceReport,
                Format = request.Format,
                RequestedBy = request.RequestedBy,
                RequestedAt = DateTime.UtcNow,
                Status = ExportStatus.InProgress,
                Parameters = request.ToParametersDictionary()
            };

            await _exportRepository.CreateExportRecordAsync(export, cancellationToken);

            try
            {
                var performanceData = await _orderRepository.GetPerformanceDataAsync(new Interfaces.PerformanceDataFilter
                {
                    FromDate = request.FromDate ?? DateTime.MinValue,
                    ToDate = request.ToDate ?? DateTime.MaxValue,
                    TransportCompanyId = request.TransportCompanyId,
                    CarrierId = request.CarrierId,
                    MetricTypes = request.MetricTypes
                }, cancellationToken);

                var reportDataString = await _reportService.GeneratePerformanceReportAsync(performanceData, request.TemplateId ?? Guid.Empty, cancellationToken);
                var reportData = new ReportData
                {
                    Fields = new Dictionary<string, object> { { "content", reportDataString } },
                    Metadata = new Dictionary<string, object> { { "type", "performance_export" }, { "recordCount", performanceData.Count() } }
                };
                var fileName = $"performance_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.{request.Format.ToString().ToLower()}";
                var filePath = await CreateExportFileAsync(reportData, request.Format, fileName, cancellationToken);

                export.Status = ExportStatus.Completed;
                export.CompletedAt = DateTime.UtcNow;
                export.FilePath = filePath;
                export.FileName = fileName;
                export.RecordCount = performanceData.Count();
                export.FileSizeBytes = await _fileStorage.GetFileSizeAsync(filePath, cancellationToken);

                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);

                return new ExportResult
                {
                    ExportId = export.Id,
                    Status = ExportStatus.Completed.ToString(),
                    FileName = fileName,
                    RecordCount = export.RecordCount,
                    FileSizeBytes = export.FileSizeBytes,
                    DownloadUrl = GenerateDownloadUrl(export.Id),
                    ExpiresAt = DateTime.UtcNow.AddDays(7)
                };
            }
            catch (Exception ex)
            {
                export.Status = ExportStatus.Failed;
                export.ErrorMessage = ex.Message;
                export.CompletedAt = DateTime.UtcNow;
                await _exportRepository.UpdateExportRecordAsync(export, cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting performance report for user {UserId}", request.RequestedBy);
            throw;
        }
    }

    public async Task<List<ExportTemplate>> GetAvailableTemplatesAsync(ExportType exportType, CancellationToken cancellationToken = default)
    {
        var allTemplates = await _exportRepository.GetExportTemplatesAsync(cancellationToken);
        return allTemplates.Where(t => t.ExportType == exportType.ToString()).Select(t => new ExportTemplate
        {
            Id = t.Id,
            Name = t.Name,
            Description = t.Description,
            ExportType = exportType,
            Format = t.Format,
            // Fields = t.Fields, // Property not available on domain entity
            // DefaultParameters = t.DefaultParameters, // Property not available on domain entity
            IsActive = t.IsActive,
            CreatedAt = t.CreatedAt,
            UpdatedAt = t.UpdatedAt ?? DateTime.UtcNow
        }).ToList();
    }

    public async Task<AnalyticsBIService.Domain.Enums.ExportStatus> GetExportStatusAsync(Guid exportId, CancellationToken cancellationToken = default)
    {
        var export = await _exportRepository.GetExportRecordAsync(exportId, cancellationToken);
        return export?.Status ?? AnalyticsBIService.Domain.Enums.ExportStatus.NotFound;
    }

    public async Task<Stream> DownloadExportAsync(Guid exportId, CancellationToken cancellationToken = default)
    {
        var export = await _exportRepository.GetExportRecordAsync(exportId, cancellationToken);
        if (export == null || export.Status != ExportStatus.Completed)
            throw new InvalidOperationException("Export not found or not completed");

        return await _fileStorage.GetFileStreamAsync(export.FilePath!, cancellationToken);
    }

    private async Task<string> CreateExportFileAsync(ReportData reportData, ExportFormat format, string fileName, CancellationToken cancellationToken)
    {
        var fileContent = format switch
        {
            ExportFormat.CSV => await _reportService.GenerateCSVAsync(reportData, cancellationToken),
            ExportFormat.Excel => await _reportService.GenerateExcelAsync(reportData, cancellationToken),
            ExportFormat.PDF => await _reportService.GeneratePDFAsync(reportData, cancellationToken),
            ExportFormat.JSON => await _reportService.GenerateJSONAsync(reportData, cancellationToken),
            _ => throw new ArgumentException($"Unsupported export format: {format}")
        };

        return await _fileStorage.SaveFileAsync(fileName, fileContent, "application/octet-stream", cancellationToken);
    }



    private string GenerateDownloadUrl(Guid exportId)
    {
        return $"/api/exports/{exportId}/download";
    }
}
