using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class PlanTaxConfigurationRepository : IPlanTaxConfigurationRepository
    {
        private readonly SubscriptionDbContext _context;

        public PlanTaxConfigurationRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<PlanTaxConfiguration?> GetByIdAsync(Guid id)
        {
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .FirstOrDefaultAsync(ptc => ptc.Id == id);
        }

        public async Task<List<PlanTaxConfiguration>> GetByPlanIdAsync(Guid planId)
        {
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.PlanId == planId)
                .OrderByDescending(ptc => ptc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<PlanTaxConfiguration>> GetActiveBPlanIdAsync(Guid planId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.PlanId == planId && 
                             ptc.IsActive &&
                             ptc.EffectiveFrom <= currentDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate))
                .OrderByDescending(ptc => ptc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<PlanTaxConfiguration>> GetByPlanIdAndRegionAsync(Guid planId, string region)
        {
            var upperRegion = region.ToUpperInvariant();
            var currentDate = DateTime.UtcNow;
            
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.PlanId == planId && 
                             ptc.IsActive &&
                             ptc.EffectiveFrom <= currentDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate) &&
                             ptc.TaxConfiguration.ApplicableRegions.Contains(upperRegion))
                .OrderByDescending(ptc => ptc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<PlanTaxConfiguration>> GetByTaxTypeAsync(TaxType taxType)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.IsActive &&
                             ptc.EffectiveFrom <= currentDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate) &&
                             ptc.TaxConfiguration.TaxType == taxType)
                .OrderByDescending(ptc => ptc.CreatedAt)
                .ToListAsync();
        }

        public async Task<PlanTaxConfiguration?> GetByPlanIdAndTaxTypeAsync(Guid planId, TaxType taxType)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.PlanId == planId && 
                             ptc.IsActive &&
                             ptc.EffectiveFrom <= currentDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate) &&
                             ptc.TaxConfiguration.TaxType == taxType)
                .FirstOrDefaultAsync();
        }

        public async Task<List<PlanTaxConfiguration>> GetEffectiveConfigurationsAsync(Guid planId, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            var upperRegion = region.ToUpperInvariant();
            
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.PlanId == planId && 
                             ptc.IsActive &&
                             ptc.EffectiveFrom <= checkDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= checkDate) &&
                             ptc.TaxConfiguration.ApplicableRegions.Contains(upperRegion) &&
                             ptc.TaxConfiguration.EffectiveDate <= checkDate &&
                             (ptc.TaxConfiguration.ExpirationDate == null || 
                              ptc.TaxConfiguration.ExpirationDate >= checkDate))
                .OrderBy(ptc => ptc.TaxConfiguration.TaxType)
                .ToListAsync();
        }

        public async Task<PlanTaxConfiguration> AddAsync(PlanTaxConfiguration planTaxConfiguration)
        {
            _context.PlanTaxConfigurations.Add(planTaxConfiguration);
            await _context.SaveChangesAsync();
            return planTaxConfiguration;
        }

        public async Task<PlanTaxConfiguration> UpdateAsync(PlanTaxConfiguration planTaxConfiguration)
        {
            _context.PlanTaxConfigurations.Update(planTaxConfiguration);
            await _context.SaveChangesAsync();
            return planTaxConfiguration;
        }

        public async Task DeleteAsync(Guid id)
        {
            var planTaxConfiguration = await GetByIdAsync(id);
            if (planTaxConfiguration != null)
            {
                _context.PlanTaxConfigurations.Remove(planTaxConfiguration);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.PlanTaxConfigurations
                .AnyAsync(ptc => ptc.Id == id);
        }

        public async Task<bool> ExistsByPlanIdAndTaxTypeAsync(Guid planId, TaxType taxType)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PlanTaxConfigurations
                .AnyAsync(ptc => ptc.PlanId == planId && 
                                ptc.IsActive &&
                                ptc.EffectiveFrom <= currentDate &&
                                (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate) &&
                                ptc.TaxConfiguration.TaxType == taxType);
        }

        public async Task<List<PlanTaxConfiguration>> GetByRegionAsync(string region)
        {
            var upperRegion = region.ToUpperInvariant();
            var currentDate = DateTime.UtcNow;
            
            return await _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .Where(ptc => ptc.IsActive &&
                             ptc.EffectiveFrom <= currentDate &&
                             (ptc.EffectiveTo == null || ptc.EffectiveTo >= currentDate) &&
                             ptc.TaxConfiguration.ApplicableRegions.Contains(upperRegion))
                .OrderByDescending(ptc => ptc.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<PlanTaxConfiguration>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var query = _context.PlanTaxConfigurations
                .Include(ptc => ptc.Plan)
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLowerInvariant();
                query = query.Where(ptc => 
                    ptc.Plan.Name.ToLower().Contains(lowerSearchTerm) ||
                    (ptc.Notes != null && ptc.Notes.ToLower().Contains(lowerSearchTerm)) ||
                    ptc.TaxConfiguration.TaxType.ToString().ToLower().Contains(lowerSearchTerm));
            }

            return await query
                .OrderByDescending(ptc => ptc.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.PlanTaxConfigurations.CountAsync();
        }

        public async Task<int> GetCountByPlanIdAsync(Guid planId)
        {
            return await _context.PlanTaxConfigurations
                .CountAsync(ptc => ptc.PlanId == planId);
        }
    }
}
