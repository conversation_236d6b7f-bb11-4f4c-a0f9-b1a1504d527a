using MediatR;

namespace OrderManagement.Application.Commands.ForceOrderClosure;

public class ForceOrderClosureCommand : IRequest
{
    public Guid OrderId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? AdminNotes { get; set; }
    public bool NotifyParties { get; set; } = true;
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}
