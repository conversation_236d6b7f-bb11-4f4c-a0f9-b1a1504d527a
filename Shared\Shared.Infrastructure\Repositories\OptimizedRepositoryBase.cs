﻿using Microsoft.EntityFrameworkCore;
using Shared.Domain.Common;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using System.Linq.Expressions;

namespace Shared.Infrastructure.Repositories;

public abstract class OptimizedRepositoryBase<TEntity, TKey> : IRepositoryBase<TEntity, TKey>
    where TEntity : class, IBaseEntity<TKey>
    where TKey : IEquatable<TKey>
{
    protected readonly DbContext Context;
    protected readonly DbSet<TEntity> DbSet;
    protected readonly ICachingService CachingService;
    protected readonly IPerformanceMetrics PerformanceMetrics;

    protected OptimizedRepositoryBase(
        DbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
    {
        Context = context;
        DbSet = context.Set<TEntity>();
        CachingService = cachingService;
        PerformanceMetrics = performanceMetrics;
    }

    public virtual async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        var cacheKey = GetCacheKey(id);
        
        return await PerformanceMetrics.MeasureAsync("Repository.GetById", async () =>
        {
            // Try cache first
            var cached = await CachingService.GetAsync<TEntity>(cacheKey);
            if (cached != null)
            {
                PerformanceMetrics.RecordCacheOperation("GetById", true);
                return cached;
            }

            PerformanceMetrics.RecordCacheOperation("GetById", false);

            // Query database
            var entity = await DbSet
                .AsNoTracking()
                .FirstOrDefaultAsync(e => e.Id.Equals(id), cancellationToken);

            if (entity != null)
            {
                await CachingService.SetAsync(cacheKey, entity, TimeSpan.FromMinutes(30));
            }

            return entity;
        });
    }

    public virtual async Task<IEnumerable<TEntity>> GetByIdsAsync(IEnumerable<TKey> ids, CancellationToken cancellationToken = default)
    {
        return await PerformanceMetrics.MeasureAsync("Repository.GetByIds", async () =>
        {
            var idList = ids.ToList();
            var entities = new List<TEntity>();
            var uncachedIds = new List<TKey>();

            // Check cache for each ID
            foreach (var id in idList)
            {
                var cacheKey = GetCacheKey(id);
                var cached = await CachingService.GetAsync<TEntity>(cacheKey);
                if (cached != null)
                {
                    entities.Add(cached);
                    PerformanceMetrics.RecordCacheOperation("GetByIds", true);
                }
                else
                {
                    uncachedIds.Add(id);
                    PerformanceMetrics.RecordCacheOperation("GetByIds", false);
                }
            }

            // Batch query for uncached entities
            if (uncachedIds.Any())
            {
                var uncachedEntities = await DbSet
                    .AsNoTracking()
                    .Where(e => uncachedIds.Contains(e.Id))
                    .ToListAsync(cancellationToken);

                entities.AddRange(uncachedEntities);

                // Cache the retrieved entities
                var cacheTasks = uncachedEntities.Select(entity =>
                    CachingService.SetAsync(GetCacheKey(entity.Id), entity, TimeSpan.FromMinutes(30)));
                await Task.WhenAll(cacheTasks);
            }

            return entities;
        });
    }

    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<TEntity, bool>>? filter = null,
        Expression<Func<TEntity, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        return await PerformanceMetrics.MeasureAsync("Repository.GetPaged", async () =>
        {
            var query = DbSet.AsNoTracking();

            if (filter != null)
            {
                query = query.Where(filter);
            }

            var totalCount = await query.CountAsync(cancellationToken);

            if (orderBy != null)
            {
                query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
            }

            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        });
    }

    public virtual async Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        return await PerformanceMetrics.MeasureAsync("Repository.Add", async () =>
        {
            var entry = await DbSet.AddAsync(entity, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);

            // Cache the new entity
            var cacheKey = GetCacheKey(entity.Id);
            await CachingService.SetAsync(cacheKey, entity, TimeSpan.FromMinutes(30));

            return entry.Entity;
        });
    }

    public virtual async Task<TEntity> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        return await PerformanceMetrics.MeasureAsync("Repository.Update", async () =>
        {
            Context.Entry(entity).State = EntityState.Modified;
            await Context.SaveChangesAsync(cancellationToken);

            // Update cache
            var cacheKey = GetCacheKey(entity.Id);
            await CachingService.SetAsync(cacheKey, entity, TimeSpan.FromMinutes(30));

            return entity;
        });
    }

    public virtual async Task DeleteAsync(TKey id, CancellationToken cancellationToken = default)
    {
        await PerformanceMetrics.MeasureAsync("Repository.Delete", async () =>
        {
            var entity = await DbSet.FindAsync(new object[] { id }, cancellationToken);
            if (entity != null)
            {
                DbSet.Remove(entity);
                await Context.SaveChangesAsync(cancellationToken);

                // Remove from cache
                var cacheKey = GetCacheKey(id);
                await CachingService.RemoveAsync(cacheKey);
            }
        });
    }

    protected virtual string GetCacheKey(TKey id)
    {
        return $"{typeof(TEntity).Name}:{id}";
    }

    protected virtual string GetCacheKey(string suffix)
    {
        return $"{typeof(TEntity).Name}:{suffix}";
    }
}

public interface IRepositoryBase<TEntity, TKey>
    where TEntity : class, IBaseEntity<TKey>
    where TKey : IEquatable<TKey>
{
    Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default);
    Task<IEnumerable<TEntity>> GetByIdsAsync(IEnumerable<TKey> ids, CancellationToken cancellationToken = default);
    Task<PagedResult<TEntity>> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<TEntity, bool>>? filter = null, Expression<Func<TEntity, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default);
    Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task<TEntity> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(TKey id, CancellationToken cancellationToken = default);
}

public class PagedResult<T>
{
    public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => PageNumber < TotalPages;
    public bool HasPreviousPage => PageNumber > 1;
}


