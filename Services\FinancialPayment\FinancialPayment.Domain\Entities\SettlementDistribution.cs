using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Entities;

public class SettlementDistribution : BaseEntity
{
    public Guid SettlementId { get; private set; }
    public Guid RecipientId { get; private set; }
    public ParticipantRole RecipientRole { get; private set; }
    public Money Amount { get; private set; }
    public DistributionType Type { get; private set; }
    public string Description { get; private set; }
    public DistributionStatus Status { get; private set; }

    public DateTime? ProcessedAt { get; private set; }
    public string? PaymentGatewayTransactionId { get; private set; }
    public string? FailureReason { get; private set; }

    // Navigation properties
    public Settlement Settlement { get; private set; } = null!;

    private SettlementDistribution() { }

    public SettlementDistribution(
        Guid settlementId,
        Guid recipientId,
        ParticipantRole recipientRole,
        Money amount,
        DistributionType type,
        string description)
    {
        if (amount == null || amount.Amount <= 0)
            throw new ArgumentException("Amount must be greater than zero");

        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty");

        Id = Guid.NewGuid();
        SettlementId = settlementId;
        RecipientId = recipientId;
        RecipientRole = recipientRole;
        Amount = amount;
        Type = type;
        Description = description;
        Status = DistributionStatus.Pending;
        CreatedAt = DateTime.UtcNow;
    }

    public void Process()
    {
        if (Status != DistributionStatus.Pending)
            throw new InvalidOperationException("Only pending distributions can be processed");

        Status = DistributionStatus.Processing;
    }

    public void Complete(string? paymentGatewayTransactionId = null)
    {
        if (Status != DistributionStatus.Processing && Status != DistributionStatus.Pending)
            throw new InvalidOperationException("Only processing or pending distributions can be completed");

        Status = DistributionStatus.Completed;
        ProcessedAt = DateTime.UtcNow;
        PaymentGatewayTransactionId = paymentGatewayTransactionId;
    }

    public void Fail(string failureReason)
    {
        if (Status != DistributionStatus.Processing && Status != DistributionStatus.Pending)
            throw new InvalidOperationException("Only processing or pending distributions can be failed");

        Status = DistributionStatus.Failed;
        FailureReason = failureReason;
        ProcessedAt = DateTime.UtcNow;
    }

    public void Retry()
    {
        if (Status != DistributionStatus.Failed)
            throw new InvalidOperationException("Only failed distributions can be retried");

        Status = DistributionStatus.Pending;
        FailureReason = null;
        ProcessedAt = null;
    }
}
