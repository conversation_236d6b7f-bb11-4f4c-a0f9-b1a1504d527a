﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.ValueObjects;

public class ContactInfo : ValueObject
{
    public string Email { get; private set; }
    public string PhoneNumber { get; private set; }
    public string? PushToken { get; private set; }
    public string? WhatsAppNumber { get; private set; }

    private ContactInfo()
    {
        Email = string.Empty;
        PhoneNumber = string.Empty;
    }

    public ContactInfo(string email, string phoneNumber, string? pushToken = null, string? whatsAppNumber = null)
    {
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));

        if (string.IsNullOrWhiteSpace(phoneNumber))
            throw new ArgumentException("Phone number cannot be empty", nameof(phoneNumber));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        if (!IsValidPhoneNumber(phoneNumber))
            throw new ArgumentException("Invalid phone number format", nameof(phoneNumber));

        Email = email.ToLowerInvariant();
        PhoneNumber = NormalizePhoneNumber(phoneNumber);
        PushToken = pushToken;
        WhatsAppNumber = whatsAppNumber != null ? NormalizePhoneNumber(whatsAppNumber) : null;
    }

    public static ContactInfo Create(string email, string phoneNumber, string? pushToken = null, string? whatsAppNumber = null)
    {
        return new ContactInfo(email, phoneNumber, pushToken, whatsAppNumber);
    }

    public ContactInfo WithEmail(string email)
    {
        return new ContactInfo(email, PhoneNumber, PushToken, WhatsAppNumber);
    }

    public ContactInfo WithPhoneNumber(string phoneNumber)
    {
        return new ContactInfo(Email, phoneNumber, PushToken, WhatsAppNumber);
    }

    public ContactInfo WithPushToken(string? pushToken)
    {
        return new ContactInfo(Email, PhoneNumber, pushToken, WhatsAppNumber);
    }

    public ContactInfo WithWhatsAppNumber(string? whatsAppNumber)
    {
        return new ContactInfo(Email, PhoneNumber, PushToken, whatsAppNumber);
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private static bool IsValidPhoneNumber(string phoneNumber)
    {
        // Basic validation for Indian phone numbers
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

        // Remove country code if present
        if (cleaned.StartsWith("+91"))
            cleaned = cleaned.Substring(3);
        else if (cleaned.StartsWith("91") && cleaned.Length == 12)
            cleaned = cleaned.Substring(2);

        // Should be 10 digits for Indian mobile numbers
        return cleaned.Length == 10 && cleaned.All(char.IsDigit);
    }

    private static string NormalizePhoneNumber(string phoneNumber)
    {
        var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

        // Add country code if not present
        if (cleaned.StartsWith("+91"))
            return cleaned;
        else if (cleaned.StartsWith("91") && cleaned.Length == 12)
            return "+" + cleaned;
        else if (cleaned.Length == 10)
            return "+91" + cleaned;

        return cleaned;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Email;
        yield return PhoneNumber;
        yield return PushToken ?? string.Empty;
        yield return WhatsAppNumber ?? string.Empty;
    }

    public override string ToString()
    {
        return $"Email: {Email}, Phone: {PhoneNumber}";
    }
}

