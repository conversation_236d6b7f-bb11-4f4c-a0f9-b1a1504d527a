using MediatR;

namespace SubscriptionManagement.Application.Commands.ResumeSubscription
{
    public class ResumeSubscriptionCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public string? PaymentMethodId { get; set; }
        public bool ProcessImmediatePayment { get; set; } = true;
        public string? ResumeReason { get; set; }
    }
}
