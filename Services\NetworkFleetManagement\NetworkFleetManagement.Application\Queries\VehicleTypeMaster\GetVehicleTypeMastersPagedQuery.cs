using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

/// <summary>
/// Query to get paginated vehicle type masters with filtering
/// </summary>
public class GetVehicleTypeMastersPagedQuery : IRequest<VehicleTypeMasterPagedResultDto>
{
    public string? SearchTerm { get; set; }
    public string? Category { get; set; }
    public bool? IsActive { get; set; }
    public decimal? MinLoadCapacity { get; set; }
    public decimal? MaxLoadCapacity { get; set; }
    public decimal? MinVolumeCapacity { get; set; }
    public decimal? MaxVolumeCapacity { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;

    public GetVehicleTypeMastersPagedQuery(
        string? searchTerm = null,
        string? category = null,
        bool? isActive = null,
        decimal? minLoadCapacity = null,
        decimal? maxLoadCapacity = null,
        decimal? minVolumeCapacity = null,
        decimal? maxVolumeCapacity = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        SearchTerm = searchTerm;
        Category = category;
        IsActive = isActive;
        MinLoadCapacity = minLoadCapacity;
        MaxLoadCapacity = maxLoadCapacity;
        MinVolumeCapacity = minVolumeCapacity;
        MaxVolumeCapacity = maxVolumeCapacity;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}
