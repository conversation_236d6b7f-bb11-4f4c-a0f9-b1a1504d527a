﻿using DataStorage.Domain.Enums;
using Shared.Domain.Common;

namespace DataStorage.Domain.ValueObjects;

public class CadFileInfo
{
    public string FileName { get; private set; }
    public CadFileFormat Format { get; private set; }
    public string Version { get; private set; }
    public CadUnits Units { get; private set; }
    public BoundingBox BoundingBox { get; private set; }
    public List<CadLayer> Layers { get; private set; }
    public List<CadBlock> Blocks { get; private set; }
    public CadMetadata Metadata { get; private set; }
    public int EntityCount { get; private set; }
    public DateTime CreatedDate { get; private set; }
    public DateTime ModifiedDate { get; private set; }

    public CadFileInfo(
        string fileName,
        CadFileFormat format,
        string version,
        CadUnits units,
        BoundingBox boundingBox,
        List<CadLayer> layers,
        List<CadBlock> blocks,
        CadMetadata metadata,
        int entityCount,
        DateTime createdDate,
        DateTime modifiedDate)
    {
        FileName = fileName ?? string.Empty;
        Format = format;
        Version = version ?? string.Empty;
        Units = units;
        BoundingBox = boundingBox ?? new BoundingBox(0, 0, 0, 0, 0, 0);
        Layers = layers ?? new List<CadLayer>();
        Blocks = blocks ?? new List<CadBlock>();
        Metadata = metadata ?? new CadMetadata();
        EntityCount = entityCount;
        CreatedDate = createdDate;
        ModifiedDate = modifiedDate;
    }
}

public class CadLayer
{
    public string Name { get; private set; }
    public bool IsVisible { get; private set; }
    public bool IsLocked { get; private set; }
    public string Color { get; private set; }
    public string LineType { get; private set; }
    public double LineWeight { get; private set; }
    public int EntityCount { get; private set; }
    public List<CadEntity> Entities { get; private set; }

    public CadLayer(
        string name,
        bool isVisible,
        bool isLocked,
        string color,
        string lineType,
        double lineWeight,
        int entityCount)
    {
        Name = name ?? string.Empty;
        IsVisible = isVisible;
        IsLocked = isLocked;
        Color = color ?? "White";
        LineType = lineType ?? "Continuous";
        LineWeight = lineWeight;
        EntityCount = entityCount;
        Entities = new List<CadEntity>();
    }

    public void AddEntity(CadEntity entity)
    {
        Entities.Add(entity);
    }
}

public class CadBlock
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public Point3D BasePoint { get; private set; }
    public List<CadEntity> Entities { get; private set; }
    public bool IsAnonymous { get; private set; }

    public CadBlock(string name, string description, Point3D basePoint, bool isAnonymous = false)
    {
        Name = name ?? string.Empty;
        Description = description ?? string.Empty;
        BasePoint = basePoint ?? new Point3D(0, 0, 0);
        Entities = new List<CadEntity>();
        IsAnonymous = isAnonymous;
    }

    public void AddEntity(CadEntity entity)
    {
        Entities.Add(entity);
    }
}

public class CadEntity
{
    public string Type { get; private set; }
    public string Layer { get; private set; }
    public string Color { get; private set; }
    public Dictionary<string, object> Properties { get; private set; }
    public BoundingBox BoundingBox { get; private set; }

    public CadEntity(string type, string layer, string color, Dictionary<string, object> properties, BoundingBox boundingBox)
    {
        Type = type ?? string.Empty;
        Layer = layer ?? string.Empty;
        Color = color ?? "ByLayer";
        Properties = properties ?? new Dictionary<string, object>();
        BoundingBox = boundingBox ?? new BoundingBox(0, 0, 0, 0, 0, 0);
    }
}

public class CadMetadata
{
    public string Title { get; private set; }
    public string Subject { get; private set; }
    public string Author { get; private set; }
    public string Keywords { get; private set; }
    public string Comments { get; private set; }
    public string Company { get; private set; }
    public string Application { get; private set; }
    public Dictionary<string, string> CustomProperties { get; private set; }

    public CadMetadata(
        string title = "",
        string subject = "",
        string author = "",
        string keywords = "",
        string comments = "",
        string company = "",
        string application = "",
        Dictionary<string, string>? customProperties = null)
    {
        Title = title;
        Subject = subject;
        Author = author;
        Keywords = keywords;
        Comments = comments;
        Company = company;
        Application = application;
        CustomProperties = customProperties ?? new Dictionary<string, string>();
    }
}

public class BoundingBox
{
    public double MinX { get; private set; }
    public double MinY { get; private set; }
    public double MinZ { get; private set; }
    public double MaxX { get; private set; }
    public double MaxY { get; private set; }
    public double MaxZ { get; private set; }

    public double Width => MaxX - MinX;
    public double Height => MaxY - MinY;
    public double Depth => MaxZ - MinZ;

    public BoundingBox(double minX, double minY, double minZ, double maxX, double maxY, double maxZ)
    {
        MinX = minX;
        MinY = minY;
        MinZ = minZ;
        MaxX = maxX;
        MaxY = maxY;
        MaxZ = maxZ;
    }
}

public class Point3D
{
    public double X { get; private set; }
    public double Y { get; private set; }
    public double Z { get; private set; }

    public Point3D(double x, double y, double z)
    {
        X = x;
        Y = y;
        Z = z;
    }
}

