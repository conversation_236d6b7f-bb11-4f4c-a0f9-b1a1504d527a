{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=tli_communication_test;User Id=timescale;Password=timescale;Include Error Detail=true"}, "Database": {"EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "UseInMemoryDatabase": false, "UseSqliteDatabase": true}, "JwtSettings": {"Issuer": "TLI.CommunicationService.Test", "Audience": "TLI.CommunicationService.Test", "SecretKey": "test-secret-key-for-jwt-token-generation-in-tests-only-not-for-production-use", "ExpirationMinutes": 60}, "ExternalServices": {"DefaultSmsProvider": "<PERSON><PERSON>", "DefaultEmailProvider": "<PERSON><PERSON>", "DefaultPushProvider": "<PERSON><PERSON>", "DefaultVoiceProvider": "<PERSON><PERSON>", "DefaultWhatsAppProvider": "<PERSON><PERSON>", "EnableSms": true, "EnableEmail": true, "EnablePush": true, "EnableVoice": true, "EnableWhatsApp": true, "EnableFailover": true, "HealthCheckIntervalMinutes": 1, "ProviderTimeoutSeconds": 5}, "MockProviders": {"Sms": {"SimulateDelay": true, "DelayMs": 100, "FailureRate": 0.1, "EnableDeliveryReceipts": true}, "Email": {"SimulateDelay": true, "DelayMs": 200, "FailureRate": 0.05, "EnableDeliveryReceipts": true}, "Push": {"SimulateDelay": true, "DelayMs": 50, "FailureRate": 0.02, "EnableDeliveryReceipts": true}, "WhatsApp": {"SimulateDelay": true, "DelayMs": 150, "FailureRate": 0.08, "EnableDeliveryReceipts": true}, "Voice": {"SimulateDelay": true, "DelayMs": 300, "FailureRate": 0.15, "EnableDeliveryReceipts": false}}, "Chat": {"EnableSignalR": true, "MaxConnectionsPerUser": 5, "MessageRetentionDays": 30, "MaxMessageLength": 4000, "EnableFileSharing": true, "MaxFileSize": 10485760, "AllowedFileTypes": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"], "EnableMessageEncryption": false, "EnableTypingIndicators": true, "EnableReadReceipts": true, "EnableMessageReactions": true}, "Localization": {"DefaultLanguage": "English", "SupportedLanguages": ["English", "Hindi", "Kannada"], "EnableAutoTranslation": false, "TranslationProvider": "<PERSON><PERSON>", "CacheTranslations": true, "CacheTtlMinutes": 60}, "Templates": {"EnableTemplateCache": true, "CacheTtlMinutes": 30, "DefaultLanguage": "English", "SupportedLanguages": ["English", "Hindi", "Kannada"], "TemplateProviders": {"WhatsApp": {"EnableApprovalWorkflow": false, "AutoSubmitForApproval": false}, "Email": {"EnableDynamicContent": true, "MaxTemplateSize": 102400}}}, "Compliance": {"EnableGDPR": true, "EnableCCPA": false, "DataRetentionDays": 30, "RequireConsent": false, "EnableOptOut": true, "OptOutKeywords": ["STOP", "UNSUBSCRIBE", "QUIT"], "RequireDoubleOptIn": false, "EnableAuditLogging": true, "AuditRetentionDays": 90}, "Testing": {"EnableMockProviders": true, "EnableTestData": true, "EnablePerformanceTesting": false, "EnableLoadTesting": false, "TestDataSize": "Small", "MockExternalServices": true, "EnableRealTimeTests": true, "TestTimeout": 30000, "MaxConcurrentTests": 10}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning", "CommunicationNotification": "Debug", "CommunicationNotification.Tests": "Debug"}, "EnableStructuredLogging": true, "EnableCorrelationIds": true, "LogSensitiveData": false, "LogProviderResponses": true}, "Performance": {"EnableMetrics": true, "EnableTracing": false, "SampleRate": 1.0, "EnableHealthChecks": true, "HealthCheckTimeout": 5000}, "Security": {"EnableAuthentication": true, "EnableAuthorization": true, "RequireHttps": false, "EnableCors": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:8080"], "EnableRateLimiting": false, "EnableApiKeyValidation": false}, "Redis": {"ConnectionString": "localhost:6379", "Password": "redis123", "Database": 1, "EnableConnectionMultiplexer": true, "ConnectTimeout": 5000, "SyncTimeout": 5000}, "SignalR": {"EnableDetailedErrors": true, "ClientTimeoutInterval": "00:01:00", "KeepAliveInterval": "00:00:15", "HandshakeTimeout": "00:00:15", "MaximumReceiveMessageSize": 32768, "StreamBufferCapacity": 10, "EnableMessagePack": false, "EnableCompression": false}}