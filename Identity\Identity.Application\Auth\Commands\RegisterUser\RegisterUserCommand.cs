using System;
using MediatR;
using Identity.Domain.Entities;

namespace Identity.Application.Auth.Commands.RegisterUser
{
    public class RegisterUserCommand : IRequest<Guid>
    {
        public string Username { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string PhoneNumber { get; set; }
        public string CountryCode { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public UserType UserType { get; set; } = UserType.Shipper;
    }
}
