namespace FinancialPayment.Application.DTOs;

public class EscrowAccountDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid CarrierId { get; set; }
    public string AccountNumber { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string TotalCurrency { get; set; } = string.Empty;
    public decimal AvailableAmount { get; set; }
    public string AvailableCurrency { get; set; } = string.Empty;
    public decimal ReservedAmount { get; set; }
    public string ReservedCurrency { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? FundedAt { get; set; }
    public DateTime? ReleasedAt { get; set; }
    public string? ReleaseReason { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Enhanced Shipper Portal Features
    public List<PaymentMilestoneDto> Milestones { get; set; } = new();
    public List<EscrowTransactionDto> Transactions { get; set; } = new();
    public EscrowAccountSummaryDto Summary { get; set; } = new();
    public bool HasActiveDisputes { get; set; }
    public int TotalMilestones { get; set; }
    public int CompletedMilestones { get; set; }
    public decimal MilestoneCompletionRate { get; set; }
    public decimal FundedPercentage { get; set; }
    public decimal ReleasedPercentage { get; set; }
    public bool IsFullyFunded { get; set; }
    public bool CanReleaseFunds { get; set; }
    public DateTime? NextMilestoneDue { get; set; }
    public List<string> AvailableActions { get; set; } = new();
}

// Missing DTOs for EscrowVisibilityService
public class EscrowDashboardDto
{
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public EscrowSummaryDto Summary { get; set; } = new();
    public List<EscrowAccountDto> ActiveAccounts { get; set; } = new();
    public List<EscrowAccountDto> RecentEscrowAccounts { get; set; } = new();
    public List<EscrowTransactionDto> RecentTransactions { get; set; } = new();
    public List<EscrowMilestoneDto> PendingMilestones { get; set; } = new();
    public List<EscrowMilestoneDto> UpcomingMilestones { get; set; } = new();
    public List<string> QuickActions { get; set; } = new();
    public EscrowPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public EscrowBalanceDto TotalBalance { get; set; } = new();
    public List<EscrowAlertDto> Alerts { get; set; } = new();
    public EscrowAnalyticsDto Analytics { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class EscrowAccountDetailsDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = "INR";
    public decimal AvailableAmount { get; set; }
    public decimal ReleasedAmount { get; set; }
    public decimal PendingReleaseAmount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? FundedAt { get; set; }
    public DateTime? ReleasedAt { get; set; }
    public string? ReleaseReason { get; set; }
    public EscrowParticipantsDto Participants { get; set; } = new();
    public EscrowAccountDto Account { get; set; } = new();
    public List<EscrowMilestoneDto> Milestones { get; set; } = new();
    public List<EscrowTransactionDto> Transactions { get; set; } = new();
    public List<EscrowTransactionDto> RecentTransactions { get; set; } = new();
    public List<string> Timeline { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public EscrowReleaseConditionsDto ReleaseConditions { get; set; } = new();
    public List<EscrowAlertDto> Alerts { get; set; } = new();
    public EscrowPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
}

public class EscrowBalanceDto
{
    public Guid EscrowAccountId { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal AvailableAmount { get; set; }
    public decimal ReservedAmount { get; set; }
    public decimal ReleasedAmount { get; set; }
    public decimal PendingReleaseAmount { get; set; }
    public decimal RefundedAmount { get; set; }
    public decimal PendingAmount { get; set; }
    public List<BalanceComponentDto> BalanceBreakdown { get; set; } = new();
    public string Currency { get; set; } = "INR";
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class EscrowMilestoneDto
{
    public Guid Id { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "INR";
    public DateTime DueDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
    public bool IsOverdue { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<EscrowTimelineEventDto> TimelineEvents { get; set; } = new();
    public List<QuickActionDto> AvailableActions { get; set; } = new();
}

public class EscrowReleaseConditionsDto
{
    public Guid EscrowAccountId { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public List<ReleaseConditionDto> Conditions { get; set; } = new();
    public decimal CompletionPercentage { get; set; }
    public DateTime? EstimatedReleaseDate { get; set; }
    public List<string> BlockingIssues { get; set; } = new();
    public List<string> NextActions { get; set; } = new();
    public List<string> RequiredConditions { get; set; } = new();
    public List<string> CompletedConditions { get; set; } = new();
    public List<string> PendingConditions { get; set; } = new();
    public bool CanRelease { get; set; }
    public string? BlockingReason { get; set; }
    public DateTime? EarliestReleaseDate { get; set; }
}

public class EscrowAlertDto
{
    public Guid Id { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool ActionRequired { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsRead { get; set; }
    public Guid? RelatedEscrowAccountId { get; set; }
    public Guid? RelatedMilestoneId { get; set; }
}

public class DateRangeDto
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Period { get; set; } = string.Empty; // "daily", "weekly", "monthly", "yearly"
}

public class EscrowAnalyticsDto
{
    public Guid UserId { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateRangeDto DateRange { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public EscrowAnalyticsSummaryDto Summary { get; set; } = new();
    public EscrowPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public decimal TotalVolume { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageTransactionAmount { get; set; }
    public decimal SuccessRate { get; set; }
    public List<EscrowTrendDto> Trends { get; set; } = new();
    public List<EscrowInsightDto> Insights { get; set; } = new();
    public DateRangeDto Period { get; set; } = new();
}

public class EscrowTimelineEventDto
{
    public Guid Id { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class QuickActionDto
{
    public string ActionType { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public string? DisabledReason { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class EscrowPerformanceMetricsDto
{
    public decimal AverageProcessingTime { get; set; }
    public decimal SuccessRate { get; set; }
    public int TotalMilestones { get; set; }
    public int CompletedMilestones { get; set; }
    public int OverdueMilestones { get; set; }
    public decimal OnTimeCompletionRate { get; set; }
}

public class EscrowTrendDto
{
    public string MetricName { get; set; } = string.Empty;
    public List<decimal> Values { get; set; } = new();
    public List<DateTime> Timestamps { get; set; } = new();
    public string TrendDirection { get; set; } = string.Empty; // "up", "down", "stable"
    public decimal PercentageChange { get; set; }
}

public class EscrowInsightDto
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public List<string> Recommendations { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

// Payment Milestone DTOs
public class PaymentMilestoneDto
{
    public Guid Id { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public decimal PayoutPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? DueDate { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? CompletionNotes { get; set; }
    public bool IsOverdue { get; set; }
    public bool IsRequired { get; set; }
    public bool RequiresApproval { get; set; }
    public string? ApprovalStatus { get; set; }
    public Guid? ApprovedBy { get; set; }
    public string? ApprovedByName { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public List<MilestoneDocumentDto> Documents { get; set; } = new();
    public List<string> CompletionCriteria { get; set; } = new();
    public string? TripLegReference { get; set; }
    public string? OrderReference { get; set; }
}

public class MilestoneDocumentDto
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public Guid UploadedBy { get; set; }
    public string UploadedByName { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsVerified { get; set; }
    public string? VerificationNotes { get; set; }
}

// Escrow Transaction DTOs
public class EscrowTransactionDto
{
    public Guid Id { get; set; }
    public Guid EscrowAccountId { get; set; }
    public string TransactionType { get; set; } = string.Empty; // Fund, Release, Refund, Reserve, Unreserve
    public decimal Amount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; }
    public Guid ProcessedBy { get; set; }
    public string ProcessedByName { get; set; } = string.Empty;
    public string? PaymentMethodId { get; set; }
    public string? PaymentGatewayTransactionId { get; set; }
    public string? FailureReason { get; set; }
    public Guid? RelatedMilestoneId { get; set; }
    public string? RelatedMilestoneName { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

// Escrow Account Summary
public class EscrowAccountSummaryDto
{
    public Guid Id { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public decimal AvailableAmount { get; set; }
    public decimal ReleasedAmount { get; set; }
    public decimal PendingReleaseAmount { get; set; }
    public decimal TotalFunded { get; set; }
    public decimal TotalReleased { get; set; }
    public decimal TotalRefunded { get; set; }
    public decimal PendingReleases { get; set; }
    public decimal AvailableForRelease { get; set; }
    public DateTime CreatedAt { get; set; }
    public int TotalTransactions { get; set; }
    public int SuccessfulTransactions { get; set; }
    public int FailedTransactions { get; set; }
    public decimal TransactionSuccessRate { get; set; }
    public DateTime? LastTransactionDate { get; set; }
    public string? LastTransactionType { get; set; }
    public List<string> RecentActivity { get; set; } = new();
    public bool HasPendingApprovals { get; set; }
    public int PendingApprovalCount { get; set; }
}

// Escrow Summary DTO
public class EscrowSummaryDto
{
    public decimal TotalEscrowValue { get; set; }
    public decimal AvailableBalance { get; set; }
    public decimal PendingReleases { get; set; }
    public decimal ReleasedAmount { get; set; }
    public int ActiveAccounts { get; set; }
    public int TotalEscrowAccounts { get; set; }
    public int ActiveEscrowAccounts { get; set; }
    public int PendingMilestones { get; set; }
    public int OverdueMilestones { get; set; }
    public int ActiveAlerts { get; set; }
    public string Currency { get; set; } = "INR";
}

// Balance Component DTO
public class BalanceComponentDto
{
    public string ComponentType { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public string Currency { get; set; } = "INR";
    public string Description { get; set; } = string.Empty;
}

// Release Condition DTO
public class ReleaseConditionDto
{
    public string ConditionType { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime? DueDate { get; set; }
    public DateTime? CompletedAt { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsMet { get; set; }
    public bool IsOverdue { get; set; }
    public decimal Amount { get; set; }
    public string Notes { get; set; } = string.Empty;
}

// Escrow Participants DTO
public class EscrowParticipantsDto
{
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid BrokerId { get; set; }
    public string BrokerName { get; set; } = string.Empty;
    public Guid CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
}

// Escrow Analytics Summary DTO
public class EscrowAnalyticsSummaryDto
{
    public decimal TotalVolume { get; set; }
    public decimal TotalEscrowValue { get; set; }
    public decimal AverageEscrowAmount { get; set; }
    public TimeSpan AverageReleaseTime { get; set; }
    public decimal SuccessfulReleaseRate { get; set; }
    public decimal MilestoneCompletionRate { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal SuccessRate { get; set; }
    public string Currency { get; set; } = "INR";
}
