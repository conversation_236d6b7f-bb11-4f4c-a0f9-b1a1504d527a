using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class TaxExemptionConfiguration : IEntityTypeConfiguration<TaxExemption>
    {
        public void Configure(EntityTypeBuilder<TaxExemption> builder)
        {
            builder.ToTable("TaxExemptions");

            builder.HasKey(te => te.Id);

            builder.Property(te => te.UserId)
                .IsRequired();

            builder.Property(te => te.ExemptionType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(te => te.ExemptionNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(te => te.IssuingAuthority)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(te => te.ValidFrom)
                .IsRequired();

            builder.Property(te => te.ValidTo)
                .IsRequired();

            builder.Property(te => te.IsActive)
                .IsRequired();

            builder.Property(te => te.DocumentPath)
                .HasMaxLength(500);

            builder.Property(te => te.VerifiedAt);

            builder.Property(te => te.VerifiedByUserId);

            builder.Property(te => te.VerificationNotes)
                .HasMaxLength(1000);

            builder.Property(te => te.CreatedAt)
                .IsRequired();

            builder.Property(te => te.UpdatedAt);

            // Store ExemptTaxTypes as JSON
            builder.Property(te => te.ExemptTaxTypes)
                .HasConversion(
                    v => string.Join(',', v.Select(t => (int)t)),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(s => (TaxType)int.Parse(s))
                          .ToList())
                .HasColumnName("ExemptTaxTypes")
                .HasMaxLength(500);

            // Store ApplicableRegions as JSON
            builder.Property(te => te.ApplicableRegions)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasColumnName("ApplicableRegions")
                .HasMaxLength(1000);

            // Indexes
            builder.HasIndex(te => te.UserId);

            builder.HasIndex(te => te.ExemptionNumber);

            builder.HasIndex(te => te.IsActive);

            builder.HasIndex(te => te.ValidFrom);

            builder.HasIndex(te => te.ValidTo);

            builder.HasIndex(te => te.VerifiedAt);

            builder.HasIndex(te => te.CreatedAt);

            builder.HasIndex(te => new { te.UserId, te.IsActive });

            builder.HasIndex(te => new { te.ExemptionNumber, te.UserId })
                .IsUnique();
        }
    }
}
