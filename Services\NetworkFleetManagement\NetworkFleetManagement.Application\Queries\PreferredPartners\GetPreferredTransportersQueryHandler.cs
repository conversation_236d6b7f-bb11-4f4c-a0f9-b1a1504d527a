using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPreferredTransportersQueryHandler : IRequestHandler<GetPreferredTransportersQuery, List<PreferredPartnerSummaryDto>>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPreferredTransportersQueryHandler> _logger;

    public GetPreferredTransportersQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetPreferredTransportersQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<PreferredPartnerSummaryDto>> Handle(GetPreferredTransportersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var partners = await _preferredPartnerRepository.GetByUserIdAndPartnerTypeAsync(
                request.ShipperId, 
                PreferredPartnerType.Transporter, 
                request.ActiveOnly, 
                cancellationToken);

            // Filter by route and load type if specified
            if (!string.IsNullOrEmpty(request.Route) || !string.IsNullOrEmpty(request.LoadType))
            {
                partners = partners.Where(p => 
                    (string.IsNullOrEmpty(request.Route) || p.IsEligibleForRoute(request.Route)) &&
                    (string.IsNullOrEmpty(request.LoadType) || p.IsEligibleForLoadType(request.LoadType))
                ).ToList();
            }

            var result = _mapper.Map<List<PreferredPartnerSummaryDto>>(partners);

            if (request.Limit.HasValue)
            {
                result = result.Take(request.Limit.Value).ToList();
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred transporters for shipper {ShipperId}", request.ShipperId);
            throw;
        }
    }
}
