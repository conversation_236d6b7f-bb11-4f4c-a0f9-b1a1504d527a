using Microsoft.Extensions.Logging;
using System.Diagnostics.Metrics;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Infrastructure.Monitoring.Metrics;

public class FleetMetricsCollector : IDisposable
{
    private readonly Meter _meter;
    private readonly ICarrierRepository _carrierRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly ILogger<FleetMetricsCollector> _logger;
    private readonly Timer _metricsTimer;

    // Counters
    private readonly Counter<long> _vehicleLocationUpdates;
    private readonly Counter<long> _driverStatusUpdates;
    private readonly Counter<long> _maintenanceEvents;
    private readonly Counter<long> _networkEvents;
    private readonly Counter<long> _documentUploads;
    private readonly Counter<long> _alertsGenerated;

    // Gauges (using ObservableGauge)
    private readonly ObservableGauge<int> _activeVehicles;
    private readonly ObservableGauge<int> _activeDrivers;
    private readonly ObservableGauge<int> _activeCarriers;
    private readonly ObservableGauge<int> _availableVehicles;
    private readonly ObservableGauge<int> _onDutyDrivers;

    // Histograms
    private readonly Histogram<double> _tripDuration;
    private readonly Histogram<double> _maintenanceCost;
    private readonly Histogram<double> _fuelConsumption;
    private readonly Histogram<double> _driverPerformanceScore;

    public FleetMetricsCollector(
        ICarrierRepository carrierRepository,
        IVehicleRepository vehicleRepository,
        IDriverRepository driverRepository,
        ILogger<FleetMetricsCollector> logger)
    {
        _carrierRepository = carrierRepository;
        _vehicleRepository = vehicleRepository;
        _driverRepository = driverRepository;
        _logger = logger;

        _meter = new Meter("NetworkFleetManagement.Fleet", "1.0.0");

        // Initialize counters
        _vehicleLocationUpdates = _meter.CreateCounter<long>("fleet.vehicle.location_updates", "updates", "Number of vehicle location updates");
        _driverStatusUpdates = _meter.CreateCounter<long>("fleet.driver.status_updates", "updates", "Number of driver status updates");
        _maintenanceEvents = _meter.CreateCounter<long>("fleet.maintenance.events", "events", "Number of maintenance events");
        _networkEvents = _meter.CreateCounter<long>("fleet.network.events", "events", "Number of network events");
        _documentUploads = _meter.CreateCounter<long>("fleet.documents.uploads", "uploads", "Number of document uploads");
        _alertsGenerated = _meter.CreateCounter<long>("fleet.alerts.generated", "alerts", "Number of alerts generated");

        // Initialize gauges
        _activeVehicles = _meter.CreateObservableGauge<int>("fleet.vehicles.active", "vehicles", "Number of active vehicles", GetActiveVehiclesCount);
        _activeDrivers = _meter.CreateObservableGauge<int>("fleet.drivers.active", "drivers", "Number of active drivers", GetActiveDriversCount);
        _activeCarriers = _meter.CreateObservableGauge<int>("fleet.carriers.active", "carriers", "Number of active carriers", GetActiveCarriersCount);
        _availableVehicles = _meter.CreateObservableGauge<int>("fleet.vehicles.available", "vehicles", "Number of available vehicles", GetAvailableVehiclesCount);
        _onDutyDrivers = _meter.CreateObservableGauge<int>("fleet.drivers.on_duty", "drivers", "Number of on-duty drivers", GetOnDutyDriversCount);

        // Initialize histograms
        _tripDuration = _meter.CreateHistogram<double>("fleet.trips.duration", "minutes", "Trip duration in minutes");
        _maintenanceCost = _meter.CreateHistogram<double>("fleet.maintenance.cost", "currency", "Maintenance cost");
        _fuelConsumption = _meter.CreateHistogram<double>("fleet.fuel.consumption", "liters", "Fuel consumption in liters");
        _driverPerformanceScore = _meter.CreateHistogram<double>("fleet.drivers.performance_score", "score", "Driver performance score");

        // Start metrics collection timer (every 30 seconds)
        _metricsTimer = new Timer(CollectMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
    }

    // Counter methods
    public void RecordVehicleLocationUpdate(Guid vehicleId, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("vehicle_id", vehicleId.ToString());
        _vehicleLocationUpdates.Add(1, tagList.ToArray());
    }

    public void RecordDriverStatusUpdate(Guid driverId, string status, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("driver_id", driverId.ToString());
        tagList.Add("status", status);
        _driverStatusUpdates.Add(1, tagList.ToArray());
    }

    public void RecordMaintenanceEvent(Guid vehicleId, string eventType, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("vehicle_id", vehicleId.ToString());
        tagList.Add("event_type", eventType);
        _maintenanceEvents.Add(1, tagList.ToArray());
    }

    public void RecordNetworkEvent(Guid networkId, string eventType, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("network_id", networkId.ToString());
        tagList.Add("event_type", eventType);
        _networkEvents.Add(1, tagList.ToArray());
    }

    public void RecordDocumentUpload(Guid entityId, string entityType, string documentType, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("entity_id", entityId.ToString());
        tagList.Add("entity_type", entityType);
        tagList.Add("document_type", documentType);
        _documentUploads.Add(1, tagList.ToArray());
    }

    public void RecordAlert(string alertType, string severity, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("alert_type", alertType);
        tagList.Add("severity", severity);
        _alertsGenerated.Add(1, tagList.ToArray());
    }

    // Histogram methods
    public void RecordTripDuration(double durationMinutes, Guid vehicleId, Guid driverId, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("vehicle_id", vehicleId.ToString());
        tagList.Add("driver_id", driverId.ToString());
        _tripDuration.Record(durationMinutes, tagList.ToArray());
    }

    public void RecordMaintenanceCost(double cost, Guid vehicleId, string maintenanceType, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("vehicle_id", vehicleId.ToString());
        tagList.Add("maintenance_type", maintenanceType);
        _maintenanceCost.Record(cost, tagList.ToArray());
    }

    public void RecordFuelConsumption(double liters, Guid vehicleId, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("vehicle_id", vehicleId.ToString());
        _fuelConsumption.Record(liters, tagList.ToArray());
    }

    public void RecordDriverPerformanceScore(double score, Guid driverId, Dictionary<string, object>? tags = null)
    {
        var tagList = CreateTagList(tags);
        tagList.Add("driver_id", driverId.ToString());
        _driverPerformanceScore.Record(score, tagList.ToArray());
    }

    // Observable gauge callback methods
    private async Task<Measurement<int>> GetActiveVehiclesCount()
    {
        try
        {
            var activeVehicles = await _vehicleRepository.GetActiveVehiclesAsync();
            return new Measurement<int>(activeVehicles.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active vehicles count for metrics");
            return new Measurement<int>(0);
        }
    }

    private async Task<Measurement<int>> GetActiveDriversCount()
    {
        try
        {
            var activeDrivers = await _driverRepository.GetActiveDriversAsync();
            return new Measurement<int>(activeDrivers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active drivers count for metrics");
            return new Measurement<int>(0);
        }
    }

    private async Task<Measurement<int>> GetActiveCarriersCount()
    {
        try
        {
            var activeCarriers = await _carrierRepository.GetActiveCarriersAsync();
            return new Measurement<int>(activeCarriers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active carriers count for metrics");
            return new Measurement<int>(0);
        }
    }

    private async Task<Measurement<int>> GetAvailableVehiclesCount()
    {
        try
        {
            var availableVehicles = await _vehicleRepository.GetAvailableVehiclesAsync();
            return new Measurement<int>(availableVehicles.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available vehicles count for metrics");
            return new Measurement<int>(0);
        }
    }

    private async Task<Measurement<int>> GetOnDutyDriversCount()
    {
        try
        {
            var onDutyDrivers = await _driverRepository.GetOnDutyDriversAsync();
            return new Measurement<int>(onDutyDrivers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting on-duty drivers count for metrics");
            return new Measurement<int>(0);
        }
    }

    // Helper methods
    private static List<KeyValuePair<string, object?>> CreateTagList(Dictionary<string, object>? tags)
    {
        var tagList = new List<KeyValuePair<string, object?>>();
        
        if (tags != null)
        {
            foreach (var tag in tags)
            {
                tagList.Add(new KeyValuePair<string, object?>(tag.Key, tag.Value));
            }
        }
        
        return tagList;
    }

    private async void CollectMetrics(object? state)
    {
        try
        {
            // This method can be used for custom metric collection logic
            // For now, the observable gauges handle automatic collection
            _logger.LogDebug("Metrics collection cycle completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during metrics collection");
        }
    }

    public void Dispose()
    {
        _metricsTimer?.Dispose();
        _meter?.Dispose();
    }
}
