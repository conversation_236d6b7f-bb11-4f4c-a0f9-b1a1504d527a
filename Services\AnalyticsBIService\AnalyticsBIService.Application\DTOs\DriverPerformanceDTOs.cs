using System;
using System.Collections.Generic;

namespace AnalyticsBIService.Application.DTOs
{
    /// <summary>
    /// Performance correlation data transfer object
    /// </summary>
    public class PerformanceCorrelationDto
    {
        public string Metric1 { get; set; } = string.Empty;
        public string Metric2 { get; set; } = string.Empty;
        public decimal CorrelationCoefficient { get; set; }
        public string CorrelationStrength { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
        public decimal Significance { get; set; }
    }

    /// <summary>
    /// Driver achievement data transfer object
    /// </summary>
    public class DriverAchievementDto
    {
        public Guid DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public string AchievementType { get; set; } = string.Empty;
        public string AchievementName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime AchievementDate { get; set; }
        public decimal Points { get; set; }
        public string Badge { get; set; } = string.Empty;
    }



    // ActionItemDto is defined in DriverIncentiveDTOs.cs

    /// <summary>
    /// Performance comparison data transfer object
    /// </summary>
    public class PerformanceComparisonDto
    {
        public string ComparisonType { get; set; } = string.Empty;
        public List<ComparisonMetricDto> Metrics { get; set; } = new();
        public List<ComparisonEntityDto> Entities { get; set; } = new();
        public string BestPerformer { get; set; } = string.Empty;
        public string WorstPerformer { get; set; } = string.Empty;
    }

    /// <summary>
    /// Comparison metric data transfer object
    /// </summary>
    public class ComparisonMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal AverageValue { get; set; }
        public decimal BestValue { get; set; }
        public decimal WorstValue { get; set; }
        public string Unit { get; set; } = string.Empty;
    }

    /// <summary>
    /// Comparison entity data transfer object
    /// </summary>
    public class ComparisonEntityDto
    {
        public Guid EntityId { get; set; }
        public string EntityName { get; set; } = string.Empty;
        public List<EntityMetricDto> Metrics { get; set; } = new();
        public decimal OverallScore { get; set; }
        public int Ranking { get; set; }
    }

    /// <summary>
    /// Entity metric data transfer object
    /// </summary>
    public class EntityMetricDto
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal BenchmarkValue { get; set; }
        public decimal Variance { get; set; }
        public string Performance { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performance gap data transfer object
    /// </summary>
    public class PerformanceGapDto
    {
        public string GapType { get; set; } = string.Empty;
        public string MetricName { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal TargetValue { get; set; }
        public decimal Gap { get; set; }
        public decimal GapPercentage { get; set; }
        public string Priority { get; set; } = string.Empty;
        public List<GapClosureActionDto> Actions { get; set; } = new();
    }

    /// <summary>
    /// Gap closure action data transfer object
    /// </summary>
    public class GapClosureActionDto
    {
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal ExpectedImpact { get; set; }
        public string Timeline { get; set; } = string.Empty;
        public string Resources { get; set; } = string.Empty;
    }

    /// <summary>
    /// Safety trend data transfer object
    /// </summary>
    public class SafetyTrendDto
    {
        public DateTime Period { get; set; }
        public decimal SafetyScore { get; set; }
        public int IncidentCount { get; set; }
        public decimal SafeMiles { get; set; }
        public string Trend { get; set; } = string.Empty;
    }



    /// <summary>
    /// Performance highlight data transfer object
    /// </summary>
    public class PerformanceHighlightDto
    {
        public string HighlightType { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Driver earnings analytics data transfer object
    /// </summary>
    public class DriverEarningsAnalyticsDto
    {
        public Guid DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public decimal TotalEarnings { get; set; }
        public decimal AverageEarningsPerTrip { get; set; }
        public decimal AverageEarningsPerMile { get; set; }
        public List<EarningsBreakdownDto> EarningsBreakdown { get; set; } = new();
        public List<EarningsTrendDto> EarningsTrends { get; set; } = new();
    }

    /// <summary>
    /// Earnings trend data transfer object
    /// </summary>


    /// <summary>
    /// Driver trip analytics data transfer object
    /// </summary>
    public class DriverTripAnalyticsDto
    {
        public Guid DriverId { get; set; }
        public string DriverName { get; set; } = string.Empty;
        public int TotalTrips { get; set; }
        public int CompletedTrips { get; set; }
        public int CancelledTrips { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageTripDistance { get; set; }
        public decimal AverageTripDuration { get; set; }
        public List<TripPerformanceDto> TripPerformance { get; set; } = new();
    }

    /// <summary>
    /// Trip performance data transfer object
    /// </summary>
    public class TripPerformanceDto
    {
        public Guid TripId { get; set; }
        public DateTime TripDate { get; set; }
        public string Origin { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public decimal Distance { get; set; }
        public decimal Duration { get; set; }
        public decimal Earnings { get; set; }
        public decimal FuelConsumption { get; set; }
        public decimal PerformanceScore { get; set; }
    }
}
