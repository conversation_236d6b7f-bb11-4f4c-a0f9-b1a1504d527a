using FluentAssertions;
using TripManagement.Domain.ValueObjects;
using Xunit;

namespace TripManagement.Tests.Domain;

public class LocationTests
{
    [Fact]
    public void Location_Creation_Should_Set_Properties_Correctly()
    {
        // Arrange
        var latitude = 40.7128;
        var longitude = -74.0060;
        var timestamp = DateTime.UtcNow;
        var address = "New York, NY";

        // Act
        var location = new Location(latitude, longitude, timestamp, address: address);

        // Assert
        location.Latitude.Should().Be(latitude);
        location.Longitude.Should().Be(longitude);
        location.Timestamp.Should().Be(timestamp);
        location.Address.Should().Be(address);
    }

    [Fact]
    public void Location_Creation_Should_Use_Current_Time_When_Timestamp_Not_Provided()
    {
        // Arrange & Act
        var location = new Location(40.7128, -74.0060);

        // Assert
        location.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Theory]
    [InlineData(-91, 0)] // Invalid latitude (too low)
    [InlineData(91, 0)]  // Invalid latitude (too high)
    public void Location_Creation_Should_Throw_For_Invalid_Latitude(double latitude, double longitude)
    {
        // Act & Assert
        var action = () => new Location(latitude, longitude);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Latitude must be between -90 and 90 degrees*");
    }

    [Theory]
    [InlineData(0, -181)] // Invalid longitude (too low)
    [InlineData(0, 181)]  // Invalid longitude (too high)
    public void Location_Creation_Should_Throw_For_Invalid_Longitude(double latitude, double longitude)
    {
        // Act & Assert
        var action = () => new Location(latitude, longitude);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Longitude must be between -180 and 180 degrees*");
    }

    [Fact]
    public void DistanceTo_Should_Calculate_Correct_Distance()
    {
        // Arrange - New York to Los Angeles (approximately 3944 km)
        var newYork = new Location(40.7128, -74.0060);
        var losAngeles = new Location(34.0522, -118.2437);

        // Act
        var distance = newYork.DistanceTo(losAngeles);

        // Assert
        distance.Should().BeApproximately(3944, 50); // Allow 50km tolerance
    }

    [Fact]
    public void DistanceTo_Should_Return_Zero_For_Same_Location()
    {
        // Arrange
        var location1 = new Location(40.7128, -74.0060);
        var location2 = new Location(40.7128, -74.0060);

        // Act
        var distance = location1.DistanceTo(location2);

        // Assert
        distance.Should().BeApproximately(0, 0.1);
    }

    [Fact]
    public void IsWithinRadius_Should_Return_True_When_Within_Radius()
    {
        // Arrange
        var center = new Location(40.7128, -74.0060);
        var nearby = new Location(40.7589, -73.9851); // Times Square (about 5km from center)

        // Act
        var isWithin = nearby.IsWithinRadius(center, 10); // 10km radius

        // Assert
        isWithin.Should().BeTrue();
    }

    [Fact]
    public void IsWithinRadius_Should_Return_False_When_Outside_Radius()
    {
        // Arrange
        var center = new Location(40.7128, -74.0060);
        var farAway = new Location(34.0522, -118.2437); // Los Angeles

        // Act
        var isWithin = farAway.IsWithinRadius(center, 100); // 100km radius

        // Assert
        isWithin.Should().BeFalse();
    }

    [Fact]
    public void ToString_Should_Return_Formatted_Coordinates()
    {
        // Arrange
        var location = new Location(40.712800, -74.006000);

        // Act
        var result = location.ToString();

        // Assert
        result.Should().Be("40.712800, -74.006000");
    }
}

public class TimeWindowTests
{
    [Fact]
    public void TimeWindow_Creation_Should_Set_Properties_Correctly()
    {
        // Arrange
        var startTime = new TimeSpan(9, 0, 0); // 9:00 AM
        var endTime = new TimeSpan(17, 0, 0);  // 5:00 PM
        var timeZone = "UTC";

        // Act
        var timeWindow = new TimeWindow(startTime, endTime, timeZone);

        // Assert
        timeWindow.StartTime.Should().Be(startTime);
        timeWindow.EndTime.Should().Be(endTime);
        timeWindow.TimeZone.Should().Be(timeZone);
    }

    [Fact]
    public void TimeWindow_Creation_Should_Throw_When_StartTime_After_EndTime()
    {
        // Arrange
        var startTime = new TimeSpan(17, 0, 0); // 5:00 PM
        var endTime = new TimeSpan(9, 0, 0);    // 9:00 AM

        // Act & Assert
        var action = () => new TimeWindow(startTime, endTime);
        action.Should().Throw<ArgumentException>()
            .WithMessage("Start time must be before end time");
    }

    [Fact]
    public void IsWithinWindow_Should_Return_True_When_Time_Is_Within_Window()
    {
        // Arrange
        var timeWindow = new TimeWindow(new TimeSpan(9, 0, 0), new TimeSpan(17, 0, 0));
        var testTime = DateTime.Today.Add(new TimeSpan(12, 0, 0)); // 12:00 PM

        // Act
        var isWithin = timeWindow.IsWithinWindow(testTime);

        // Assert
        isWithin.Should().BeTrue();
    }

    [Fact]
    public void IsWithinWindow_Should_Return_False_When_Time_Is_Outside_Window()
    {
        // Arrange
        var timeWindow = new TimeWindow(new TimeSpan(9, 0, 0), new TimeSpan(17, 0, 0));
        var testTime = DateTime.Today.Add(new TimeSpan(20, 0, 0)); // 8:00 PM

        // Act
        var isWithin = timeWindow.IsWithinWindow(testTime);

        // Assert
        isWithin.Should().BeFalse();
    }

    [Fact]
    public void Duration_Should_Calculate_Correct_Duration()
    {
        // Arrange
        var timeWindow = new TimeWindow(new TimeSpan(9, 0, 0), new TimeSpan(17, 30, 0));

        // Act
        var duration = timeWindow.Duration;

        // Assert
        duration.Should().Be(new TimeSpan(8, 30, 0)); // 8.5 hours
    }

    [Fact]
    public void ToString_Should_Return_Formatted_Time_Range()
    {
        // Arrange
        var timeWindow = new TimeWindow(new TimeSpan(9, 0, 0), new TimeSpan(17, 30, 0));

        // Act
        var result = timeWindow.ToString();

        // Assert
        result.Should().Be("09:00 - 17:30");
    }
}

public class RouteTests
{
    [Fact]
    public void Route_Creation_Should_Set_Properties_Correctly()
    {
        // Arrange
        var startLocation = new Location(40.7128, -74.0060);
        var endLocation = new Location(34.0522, -118.2437);
        var waypoints = new List<Location>
        {
            new Location(41.8781, -87.6298) // Chicago
        };
        var estimatedDistance = 4000.0;
        var estimatedDuration = TimeSpan.FromHours(40);

        // Act
        var route = new Route(startLocation, endLocation, waypoints, estimatedDistance, estimatedDuration);

        // Assert
        route.StartLocation.Should().Be(startLocation);
        route.EndLocation.Should().Be(endLocation);
        route.Waypoints.Should().HaveCount(1);
        route.EstimatedDistanceKm.Should().Be(estimatedDistance);
        route.EstimatedDuration.Should().Be(estimatedDuration);
    }

    [Fact]
    public void Route_Creation_Should_Throw_When_StartLocation_Is_Null()
    {
        // Arrange
        var endLocation = new Location(34.0522, -118.2437);

        // Act & Assert
        var action = () => new Route(null!, endLocation);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Route_Creation_Should_Throw_When_EndLocation_Is_Null()
    {
        // Arrange
        var startLocation = new Location(40.7128, -74.0060);

        // Act & Assert
        var action = () => new Route(startLocation, null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void TotalEstimatedDistance_Should_Return_Provided_Distance_When_Available()
    {
        // Arrange
        var startLocation = new Location(40.7128, -74.0060);
        var endLocation = new Location(34.0522, -118.2437);
        var estimatedDistance = 4000.0;

        var route = new Route(startLocation, endLocation, estimatedDistanceKm: estimatedDistance);

        // Act
        var totalDistance = route.TotalEstimatedDistance;

        // Assert
        totalDistance.Should().Be(estimatedDistance);
    }

    [Fact]
    public void TotalEstimatedDistance_Should_Calculate_Distance_When_Not_Provided()
    {
        // Arrange
        var startLocation = new Location(40.7128, -74.0060); // New York
        var endLocation = new Location(34.0522, -118.2437);   // Los Angeles

        var route = new Route(startLocation, endLocation);

        // Act
        var totalDistance = route.TotalEstimatedDistance;

        // Assert
        totalDistance.Should().BeApproximately(3944, 50); // Approximate distance NY to LA
    }
}
