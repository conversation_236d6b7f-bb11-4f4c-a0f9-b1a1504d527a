using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Services;

public interface IFeatureFlagService
{
    // Feature flag evaluation
    Task<bool> IsFeatureEnabledAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null);
    Task<T?> GetFeatureValueAsync<T>(string featureKey, Guid userId, T defaultValue, Dictionary<string, object>? context = null);
    Task<string?> GetFeatureVariantAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null);
    
    // A/B Testing
    Task<string?> GetABTestVariantAsync(string testKey, Guid userId, Dictionary<string, object>? context = null);
    Task RecordConversionAsync(string featureKey, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null);
    
    // Feature flag management
    Task<bool> CreateFeatureFlagAsync(string key, string name, string description, bool isEnabled = false);
    Task<bool> UpdateFeatureFlagAsync(string key, bool isEnabled, Dictionary<string, object>? configuration = null);
    Task<bool> DeleteFeatureFlagAsync(string key);
    
    // Gradual rollout
    Task<bool> SetRolloutPercentageAsync(string featureKey, double percentage);
    Task<bool> AddUserToWhitelistAsync(string featureKey, Guid userId);
    Task<bool> RemoveUserFromWhitelistAsync(string featureKey, Guid userId);
    
    // Fleet-specific feature flags
    Task<bool> IsFleetFeatureEnabledAsync(FleetFeatureType featureType, Guid carrierId, Dictionary<string, object>? context = null);
    Task<bool> IsDriverFeatureEnabledAsync(DriverFeatureType featureType, Guid driverId, Dictionary<string, object>? context = null);
    Task<bool> IsVehicleFeatureEnabledAsync(VehicleFeatureType featureType, Guid vehicleId, Dictionary<string, object>? context = null);
    
    // Analytics
    Task<Dictionary<string, object>> GetFeatureFlagAnalyticsAsync(string featureKey, DateTime? from = null, DateTime? to = null);
    Task<Dictionary<string, object>> GetABTestResultsAsync(string testKey, DateTime? from = null, DateTime? to = null);
}

public enum FleetFeatureType
{
    RealTimeTracking,
    PredictiveMaintenance,
    RouteOptimization,
    FuelEfficiencyMonitoring,
    AdvancedAnalytics,
    MobileOnboarding,
    DocumentAutoVerification,
    GeofenceAlerts,
    DriverScoring,
    VehicleTelematics
}

public enum DriverFeatureType
{
    MobileApp,
    DigitalDocuments,
    PerformanceTracking,
    RouteNavigation,
    EarningsAnalytics,
    ComplianceAlerts,
    TrainingModules,
    SafetyScoring,
    CommunicationTools,
    IncentivePrograms
}

public enum VehicleFeatureType
{
    IoTSensors,
    PredictiveMaintenance,
    FuelMonitoring,
    LocationTracking,
    PerformanceAnalytics,
    MaintenanceScheduling,
    UtilizationTracking,
    SafetyMonitoring,
    EmissionTracking,
    LoadOptimization
}
