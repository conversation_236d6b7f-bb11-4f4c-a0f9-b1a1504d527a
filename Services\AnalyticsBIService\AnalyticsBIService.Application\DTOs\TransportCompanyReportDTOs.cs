using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

// ReportTemplateDto is already defined in ReportingDTOs.cs - using that instead

// ReportParameterDto is already defined in ReportingDTOs.cs - using that instead

/// <summary>
/// Request DTO for generating report
/// </summary>
public class GenerateReportRequestDto
{
    public Guid TemplateId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid RequestedBy { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool SaveToHistory { get; set; } = true;
    public string? CustomName { get; set; }
}

/// <summary>
/// Result DTO for report generation
/// </summary>
public class ReportGenerationResultDto
{
    public Guid ExecutionId { get; set; }
    public ReportExecutionStatus Status { get; set; }
    public string? ReportContent { get; set; }
    public int RecordCount { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string? DownloadUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ExecutionTime { get; set; }
}

/// <summary>
/// Request DTO for exporting report
/// </summary>
public class ExportReportRequestDto
{
    public Guid ExecutionId { get; set; }
    public ExportFormat Format { get; set; }
    public Dictionary<string, object> ExportOptions { get; set; } = new();
    public bool IncludeCharts { get; set; } = true;
    public bool IncludeRawData { get; set; } = false;
    public string? CustomFileName { get; set; }
}

// ExportResultDto is already defined in AdvancedReportingDto.cs - using that instead

/// <summary>
/// DTO for scheduled report
/// </summary>
public class ScheduledReportDto
{
    public Guid Id { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Guid TemplateId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TransportCompanyReportScheduleDto Schedule { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> EmailRecipients { get; set; } = new();
    public List<ExportFormat> ExportFormats { get; set; } = new();
    public bool IsActive { get; set; }
    public DateTime? NextExecutionTime { get; set; }
    public DateTime? LastExecutionTime { get; set; }
    public Guid CreatedBy { get; set; }

    // Additional properties required by TransportCompanyReportService
    public Guid ReportId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// DTO for transport company report schedule (simplified version)
/// </summary>
public class TransportCompanyReportScheduleDto
{
    public ScheduleFrequency Frequency { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public DayOfWeek DayOfWeek { get; set; } // For weekly
    public int DayOfMonth { get; set; } // For monthly
    public List<DayOfWeek> DaysOfWeek { get; set; } = new(); // For custom schedules
    public string? CronExpression { get; set; } // For advanced scheduling
}

/// <summary>
/// Request DTO for creating scheduled report
/// </summary>
public class CreateScheduledReportRequestDto
{
    public Guid TransportCompanyId { get; set; }
    public Guid TemplateId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ReportScheduleDto Schedule { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<string> EmailRecipients { get; set; } = new();
    public List<ExportFormat> ExportFormats { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public Guid CreatedBy { get; set; }
}

/// <summary>
/// DTO for report execution status
/// </summary>
public class ReportExecutionStatusDto
{
    public Guid ExecutionId { get; set; }
    public ReportExecutionStatus Status { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int? RecordCount { get; set; }
    public string? ErrorMessage { get; set; }
    public int Progress { get; set; } // 0-100
    public TimeSpan? EstimatedTimeRemaining { get; set; }
    public string? CurrentStep { get; set; }
}

/// <summary>
/// DTO for report execution history
/// </summary>
public class ReportExecutionHistoryDto
{
    public Guid ExecutionId { get; set; }
    public Guid TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public ReportExecutionStatus Status { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? ExecutionTime { get; set; }
    public int? RecordCount { get; set; }
    public Guid RequestedBy { get; set; }
    public string RequestedByName { get; set; } = string.Empty;
    public List<ExportResultDto> Exports { get; set; } = new();
    public bool CanRerun { get; set; } = true;
    public bool CanExport { get; set; } = true;

    // Additional properties required by TransportCompanyReportService
    public Guid Id { get; set; }
    public Guid ReportId { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public DateTime? ExecutedAt { get; set; }
    public Guid? ExecutedBy { get; set; }
    public TimeSpan? Duration { get; set; }
    public string? FilePath { get; set; }
    public long? FileSize { get; set; }
    public string Parameters { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for creating custom report template
/// </summary>
public class CreateCustomReportTemplateRequestDto
{
    public Guid TransportCompanyId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public ReportConfigurationDto Configuration { get; set; } = new();
    public Guid CreatedBy { get; set; }
}

// CustomReportTemplateDto is already defined in AdvancedReportingDto.cs - using that instead

// ReportConfigurationDto is already defined in AdvancedReportingDto.cs - using that instead

// ReportFieldDto is already defined in AdvancedReportingDto.cs - using that instead

// ReportFilterDto is already defined in AdvancedReportingDto.cs - using that instead

// ReportGroupingDto is already defined in AdvancedReportingDto.cs - using that instead

// ReportSortingDto is already defined in AdvancedReportingDto.cs - using that instead

/// <summary>
/// DTO for report formatting
/// </summary>
public class ReportFormattingDto
{
    public string Format { get; set; } = "PDF"; // Added for ScheduledReportService compatibility
    public string Theme { get; set; } = "Professional";
    public string ColorScheme { get; set; } = "Blue";
    public bool IncludeHeader { get; set; } = true;
    public bool IncludeFooter { get; set; } = true;
    public bool IncludePageNumbers { get; set; } = true;
    public bool IncludeGeneratedDate { get; set; } = true;
    public string? CustomLogo { get; set; }
    public Dictionary<string, object> StyleSettings { get; set; } = new();
}

/// <summary>
/// Request DTO for report preview
/// </summary>
public class PreviewReportRequestDto
{
    public Guid TemplateId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int MaxRecords { get; set; } = 100;
}

/// <summary>
/// DTO for report preview
/// </summary>
public class ReportPreviewDto
{
    public Guid TemplateId { get; set; }
    public string PreviewContent { get; set; } = string.Empty;
    public int SampleRecordCount { get; set; }
    public DateTime GeneratedAt { get; set; }
    public long EstimatedFullReportSize { get; set; }
    public TimeSpan EstimatedExecutionTime { get; set; }
    public List<string> DataQualityWarnings { get; set; } = new();
}

/// <summary>
/// Enums for report system
/// </summary>

public enum ReportExecutionStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum ScheduleFrequency
{
    Daily = 0,
    Weekly = 1,
    Monthly = 2,
    Quarterly = 3,
    Yearly = 4,
    Custom = 5
}
