using Bogus;

namespace Tests.Shared.Builders;

/// <summary>
/// Base class for test data builders using Bogus
/// </summary>
public abstract class TestDataBuilder<T> where T : class
{
    protected readonly Faker<T> Faker;

    protected TestDataBuilder()
    {
        Faker = new Faker<T>();
        ConfigureFaker();
    }

    /// <summary>
    /// Configure the Faker instance with default rules
    /// </summary>
    protected abstract void ConfigureFaker();

    /// <summary>
    /// Build a single instance
    /// </summary>
    public virtual T Build() => Faker.Generate();

    /// <summary>
    /// Build multiple instances
    /// </summary>
    public virtual IEnumerable<T> BuildMany(int count = 3) => Faker.Generate(count);

    /// <summary>
    /// Build with custom configuration
    /// </summary>
    public virtual T BuildWith(Action<Faker<T>> configure)
    {
        var customFaker = new Faker<T>();
        ConfigureFaker();
        configure(customFaker);
        return customFaker.Generate();
    }
}

/// <summary>
/// Builder for user test data
/// </summary>
public class UserTestDataBuilder : TestDataBuilder<UserTestData>
{
    protected override void ConfigureFaker()
    {
        Faker.RuleFor(u => u.Id, f => f.Random.Guid())
            .RuleFor(u => u.Email, f => f.Internet.Email())
            .RuleFor(u => u.FirstName, f => f.Name.FirstName())
            .RuleFor(u => u.LastName, f => f.Name.LastName())
            .RuleFor(u => u.PhoneNumber, f => f.Phone.PhoneNumber())
            .RuleFor(u => u.CompanyName, f => f.Company.CompanyName())
            .RuleFor(u => u.CreatedAt, f => f.Date.Past())
            .RuleFor(u => u.IsActive, f => f.Random.Bool(0.8f));
    }

    public UserTestDataBuilder WithEmail(string email)
    {
        Faker.RuleFor(u => u.Email, email);
        return this;
    }

    public UserTestDataBuilder WithActiveStatus(bool isActive)
    {
        Faker.RuleFor(u => u.IsActive, isActive);
        return this;
    }

    public UserTestDataBuilder WithCompany(string companyName)
    {
        Faker.RuleFor(u => u.CompanyName, companyName);
        return this;
    }
}

/// <summary>
/// Test data model for users
/// </summary>
public class UserTestData
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Builder for order test data
/// </summary>
public class OrderTestDataBuilder : TestDataBuilder<OrderTestData>
{
    protected override void ConfigureFaker()
    {
        Faker.RuleFor(o => o.Id, f => f.Random.Guid())
            .RuleFor(o => o.OrderNumber, f => f.Random.AlphaNumeric(10).ToUpper())
            .RuleFor(o => o.CustomerId, f => f.Random.Guid())
            .RuleFor(o => o.PickupLocation, f => f.Address.FullAddress())
            .RuleFor(o => o.DeliveryLocation, f => f.Address.FullAddress())
            .RuleFor(o => o.Weight, f => f.Random.Decimal(1, 1000))
            .RuleFor(o => o.Value, f => f.Random.Decimal(100, 10000))
            .RuleFor(o => o.CreatedAt, f => f.Date.Past())
            .RuleFor(o => o.Status, f => f.PickRandom<OrderStatus>());
    }

    public OrderTestDataBuilder WithStatus(OrderStatus status)
    {
        Faker.RuleFor(o => o.Status, status);
        return this;
    }

    public OrderTestDataBuilder WithCustomer(Guid customerId)
    {
        Faker.RuleFor(o => o.CustomerId, customerId);
        return this;
    }
}

/// <summary>
/// Test data model for orders
/// </summary>
public class OrderTestData
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public Guid CustomerId { get; set; }
    public string PickupLocation { get; set; } = string.Empty;
    public string DeliveryLocation { get; set; } = string.Empty;
    public decimal Weight { get; set; }
    public decimal Value { get; set; }
    public DateTime CreatedAt { get; set; }
    public OrderStatus Status { get; set; }
}

/// <summary>
/// Order status enumeration for testing
/// </summary>
public enum OrderStatus
{
    Pending,
    Confirmed,
    InTransit,
    Delivered,
    Cancelled
}
