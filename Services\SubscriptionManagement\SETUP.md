# Subscription Management Service - Setup Guide

This guide will help you set up and run the Subscription Management Service in your TLI Logistics microservices environment.

## 🚀 Quick Start

### Prerequisites
- .NET 8 SDK
- PostgreSQL database
- RazorPay account (for payment processing)
- Running Identity Service (for JWT authentication)

### 1. Database Setup

#### Option A: Using the SQL Script (Recommended)
```bash
# Connect to PostgreSQL and run the setup script
psql -h localhost -U timescale -d postgres -f database-setup.sql
```

#### Option B: Using Entity Framework Migrations (Alternative)
```bash
# Navigate to the API project
cd SubscriptionManagement.API

# Create and apply migrations
dotnet ef migrations add InitialCreate --project ../SubscriptionManagement.Infrastructure
dotnet ef database update --project ../SubscriptionManagement.Infrastructure
```

### 2. Configuration

Update `appsettings.json` with your specific configuration:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_SubscriptionManagement;User Id=timescale;Password=timescale"
  },
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services",
    "ExpiryMinutes": 60
  },
  "RazorPay": {
    "KeyId": "your_razorpay_key_id",
    "KeySecret": "your_razorpay_key_secret",
    "WebhookSecret": "your_webhook_secret"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "Port": 5672,
    "Username": "guest",
    "Password": "guest"
  }
}
```

### 3. Run the Service

```bash
# Navigate to the API project
cd SubscriptionManagement.API

# Run the service
dotnet run
```

The service will start on:
- HTTP: `http://localhost:5003`
- HTTPS: `https://localhost:7003`
- Swagger UI: `http://localhost:5003` or `https://localhost:7003`

## 🔧 API Gateway Integration

The service is already configured to work with the API Gateway on the following routes:

### Subscription Routes
- `GET /api/subscriptions` - Get all subscriptions (Admin only)
- `POST /api/subscriptions` - Create subscription
- `GET /api/subscriptions/{id}` - Get subscription by ID
- `GET /api/subscriptions/my-subscription` - Get current user's subscription
- `POST /api/subscriptions/{id}/cancel` - Cancel subscription
- `POST /api/subscriptions/{id}/upgrade` - Upgrade subscription
- `POST /api/subscriptions/{id}/downgrade` - Downgrade subscription
- `GET /api/subscriptions/{id}/usage` - Get subscription usage

### Plan Routes
- `GET /api/plans` - Get public plans
- `GET /api/plans/{id}` - Get plan by ID
- `GET /api/plans/by-user-type/{userType}` - Get plans by user type
- `POST /api/plans` - Create plan (Admin only)
- `PUT /api/plans/{id}` - Update plan (Admin only)
- `POST /api/plans/{id}/features` - Add plan feature (Admin only)

### Access via API Gateway
- Subscriptions: `http://localhost:5000/api/subscriptions/*`
- Plans: `http://localhost:5000/api/plans/*`

## 🧪 Testing

### Run Unit Tests
```bash
cd SubscriptionManagement.Tests
dotnet test
```

### Test API Endpoints

#### 1. Get Public Plans
```bash
curl -X GET "http://localhost:5003/api/plans" \
  -H "accept: application/json"
```

#### 2. Create Subscription (requires JWT token)
```bash
curl -X POST "http://localhost:5003/api/subscriptions" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "PLAN_GUID",
    "autoRenew": true
  }'
```

## 📊 Sample Data

The database setup script includes sample plans for all user types:

### Transport Company Plans
- **Basic**: ₹999/month, 10 RFQs
- **Pro**: ₹2,999/month, Unlimited RFQs
- **Enterprise**: ₹9,999/month, Unlimited + Custom features

### Broker Plans
- **Basic**: ₹1,499/month, 20 RFQs
- **Pro**: ₹4,999/month, Unlimited RFQs
- **Enterprise**: ₹14,999/month, Unlimited + Advanced features

### Carrier Plans
- **Basic**: ₹799/month, 15 RFQs, 2 vehicles
- **Pro**: ₹2,499/month, Unlimited RFQs, 10 vehicles
- **Enterprise**: ₹7,999/month, Unlimited + Fleet management

## 🔍 Health Checks

The service includes health checks accessible at:
- `http://localhost:5003/health`

## 📝 Logging

The service uses structured logging with Serilog. Logs include:
- Request/response logging
- Payment processing events
- Subscription lifecycle events
- Error tracking

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify PostgreSQL is running
   - Check connection string in appsettings.json
   - Ensure database exists and schema is created

2. **JWT Authentication Issues**
   - Verify JWT settings match Identity Service
   - Check token expiration
   - Ensure proper Authorization header format

3. **RazorPay Integration Issues**
   - Verify API keys are correct
   - Check webhook configuration
   - Review payment processing logs

### Debug Mode
Run with detailed logging:
```bash
dotnet run --environment Development
```

## 🔄 Integration with Other Services

### Required Services
1. **Identity Service** (Port 5001) - For JWT authentication
2. **User Management Service** (Port 5002) - For user profile data
3. **API Gateway** (Port 5000) - For routing and load balancing

### Optional Services
1. **RabbitMQ** - For event-driven communication
2. **Redis** - For caching and session management

## 📈 Monitoring

### Key Metrics to Monitor
- Subscription creation rate
- Payment success/failure rates
- API response times
- Database connection health
- Memory and CPU usage

### Recommended Tools
- Application Insights (Azure)
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)

## 🔐 Security Considerations

1. **JWT Token Validation** - All protected endpoints require valid JWT
2. **Role-Based Access** - Admin endpoints restricted to Admin role
3. **Input Validation** - All inputs validated using FluentValidation
4. **SQL Injection Protection** - Entity Framework Core parameterized queries
5. **HTTPS Enforcement** - Production should use HTTPS only

## 📦 Deployment

### Docker Support
```dockerfile
# Build and run with Docker
docker build -t subscription-management .
docker run -p 5003:80 subscription-management
```

### Environment Variables
- `ConnectionStrings__DefaultConnection`
- `JwtSettings__Secret`
- `RazorPay__KeyId`
- `RazorPay__KeySecret`
- `RabbitMQ__Host`

## 🎯 Next Steps

1. **Complete Implementation** - Implement remaining query handlers
2. **Add Caching** - Implement Redis caching for frequently accessed data
3. **Add Monitoring** - Set up application monitoring and alerting
4. **Performance Testing** - Load test the service under expected traffic
5. **Documentation** - Complete API documentation and user guides
