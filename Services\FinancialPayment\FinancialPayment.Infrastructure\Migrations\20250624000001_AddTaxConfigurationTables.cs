using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FinancialPayment.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTaxConfigurationTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create Tax Configuration table
            migrationBuilder.CreateTable(
                name: "TaxConfigurations",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    JurisdictionCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    JurisdictionState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EnableGstCalculation = table.Column<bool>(type: "boolean", nullable: false),
                    EnableTdsCalculation = table.Column<bool>(type: "boolean", nullable: false),
                    EnableReverseCharge = table.Column<bool>(type: "boolean", nullable: false),
                    RequireHsnCode = table.Column<bool>(type: "boolean", nullable: false),
                    DefaultGstRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    DefaultTdsRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    DefaultCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxConfigurations", x => x.Id);
                });

            // Create Tax Configuration History table
            migrationBuilder.CreateTable(
                name: "TaxConfigurationHistories",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxConfigurationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Details = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxConfigurationHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxConfigurationHistories_TaxConfigurations_TaxConfigurationId",
                        column: x => x.TaxConfigurationId,
                        principalSchema: "financial_payment",
                        principalTable: "TaxConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create Tax Configuration Rules table
            migrationBuilder.CreateTable(
                name: "TaxConfigurationRules",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxConfigurationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Condition = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Action = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxConfigurationRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxConfigurationRules_TaxConfigurations_TaxConfigurationId",
                        column: x => x.TaxConfigurationId,
                        principalSchema: "financial_payment",
                        principalTable: "TaxConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create GST Configuration table
            migrationBuilder.CreateTable(
                name: "GstConfigurations",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    ServiceCategory = table.Column<int>(type: "integer", nullable: false),
                    JurisdictionCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    JurisdictionState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    JurisdictionType = table.Column<int>(type: "integer", nullable: false),
                    GstRate = table.Column<int>(type: "integer", nullable: false),
                    TaxRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxCalculationMethod = table.Column<int>(type: "integer", nullable: false),
                    TaxEffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TaxEffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    HsnCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    MinimumAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    MinimumAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    MaximumAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    MaximumAmountCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    IsReverseChargeApplicable = table.Column<bool>(type: "boolean", nullable: false),
                    ReverseChargeConditions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GstConfigurations", x => x.Id);
                });

            // Create GST Configuration History table
            migrationBuilder.CreateTable(
                name: "GstConfigurationHistories",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    GstConfigurationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Details = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GstConfigurationHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GstConfigurationHistories_GstConfigurations_GstConfigurationId",
                        column: x => x.GstConfigurationId,
                        principalSchema: "financial_payment",
                        principalTable: "GstConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create TDS Configuration table
            migrationBuilder.CreateTable(
                name: "TdsConfigurations",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Section = table.Column<int>(type: "integer", nullable: false),
                    EntityType = table.Column<int>(type: "integer", nullable: false),
                    TaxRate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    TaxCalculationMethod = table.Column<int>(type: "integer", nullable: false),
                    TaxEffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TaxEffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ThresholdAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    ThresholdCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    AnnualThresholdAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AnnualThresholdCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    ThresholdEntityType = table.Column<int>(type: "integer", nullable: false),
                    ThresholdHasPan = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    RequiresPan = table.Column<bool>(type: "boolean", nullable: false),
                    HigherRateWithoutPan = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    SpecialConditions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TdsConfigurations", x => x.Id);
                });

            // Create TDS Configuration History table
            migrationBuilder.CreateTable(
                name: "TdsConfigurationHistories",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TdsConfigurationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Details = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TdsConfigurationHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TdsConfigurationHistories_TdsConfigurations_TdsConfigurationId",
                        column: x => x.TdsConfigurationId,
                        principalSchema: "financial_payment",
                        principalTable: "TdsConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create HSN Code table
            migrationBuilder.CreateTable(
                name: "HsnCodes",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Category = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ApplicableGstRate = table.Column<int>(type: "integer", nullable: false),
                    Chapter = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Section = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AdditionalNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HsnCodes", x => x.Id);
                });

            // Create HSN Code History table
            migrationBuilder.CreateTable(
                name: "HsnCodeHistories",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    HsnCodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Details = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    ModifiedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HsnCodeHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HsnCodeHistories_HsnCodes_HsnCodeId",
                        column: x => x.HsnCodeId,
                        principalSchema: "financial_payment",
                        principalTable: "HsnCodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create HSN Code GST Mapping table
            migrationBuilder.CreateTable(
                name: "HsnCodeGstMappings",
                schema: "financial_payment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    HsnCodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    GstRate = table.Column<int>(type: "integer", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HsnCodeGstMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HsnCodeGstMappings_HsnCodes_HsnCodeId",
                        column: x => x.HsnCodeId,
                        principalSchema: "financial_payment",
                        principalTable: "HsnCodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            // Create indexes for Tax Configuration tables
            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_Name",
                schema: "financial_payment",
                table: "TaxConfigurations",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_Status",
                schema: "financial_payment",
                table: "TaxConfigurations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_EffectiveFrom_EffectiveTo",
                schema: "financial_payment",
                table: "TaxConfigurations",
                columns: new[] { "EffectiveFrom", "EffectiveTo" });

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_IsDefault",
                schema: "financial_payment",
                table: "TaxConfigurations",
                column: "IsDefault");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_Priority",
                schema: "financial_payment",
                table: "TaxConfigurations",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurations_Status_IsDefault",
                schema: "financial_payment",
                table: "TaxConfigurations",
                columns: new[] { "Status", "IsDefault" });

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationHistories_TaxConfigurationId",
                schema: "financial_payment",
                table: "TaxConfigurationHistories",
                column: "TaxConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationHistories_ModifiedAt",
                schema: "financial_payment",
                table: "TaxConfigurationHistories",
                column: "ModifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationHistories_ModifiedBy",
                schema: "financial_payment",
                table: "TaxConfigurationHistories",
                column: "ModifiedBy");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationRules_TaxConfigurationId",
                schema: "financial_payment",
                table: "TaxConfigurationRules",
                column: "TaxConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationRules_IsActive",
                schema: "financial_payment",
                table: "TaxConfigurationRules",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TaxConfigurationRules_Name",
                schema: "financial_payment",
                table: "TaxConfigurationRules",
                column: "Name");

            // Create indexes for GST Configuration tables
            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_Name",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_ServiceCategory",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "ServiceCategory");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_GstRate",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "GstRate");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_Status",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_HsnCode",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "HsnCode");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_IsReverseChargeApplicable",
                schema: "financial_payment",
                table: "GstConfigurations",
                column: "IsReverseChargeApplicable");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurations_ServiceCategory_Status",
                schema: "financial_payment",
                table: "GstConfigurations",
                columns: new[] { "ServiceCategory", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurationHistories_GstConfigurationId",
                schema: "financial_payment",
                table: "GstConfigurationHistories",
                column: "GstConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurationHistories_ModifiedAt",
                schema: "financial_payment",
                table: "GstConfigurationHistories",
                column: "ModifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_GstConfigurationHistories_ModifiedBy",
                schema: "financial_payment",
                table: "GstConfigurationHistories",
                column: "ModifiedBy");

            // Create indexes for TDS Configuration tables
            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_Name",
                schema: "financial_payment",
                table: "TdsConfigurations",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_Section",
                schema: "financial_payment",
                table: "TdsConfigurations",
                column: "Section");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_EntityType",
                schema: "financial_payment",
                table: "TdsConfigurations",
                column: "EntityType");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_Status",
                schema: "financial_payment",
                table: "TdsConfigurations",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_RequiresPan",
                schema: "financial_payment",
                table: "TdsConfigurations",
                column: "RequiresPan");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_Section_EntityType",
                schema: "financial_payment",
                table: "TdsConfigurations",
                columns: new[] { "Section", "EntityType" });

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurations_Section_EntityType_Status",
                schema: "financial_payment",
                table: "TdsConfigurations",
                columns: new[] { "Section", "EntityType", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurationHistories_TdsConfigurationId",
                schema: "financial_payment",
                table: "TdsConfigurationHistories",
                column: "TdsConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurationHistories_ModifiedAt",
                schema: "financial_payment",
                table: "TdsConfigurationHistories",
                column: "ModifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TdsConfigurationHistories_ModifiedBy",
                schema: "financial_payment",
                table: "TdsConfigurationHistories",
                column: "ModifiedBy");

            // Create indexes for HSN Code tables
            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_Code",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_Chapter",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "Chapter");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_Section",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "Section");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_Status",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_EffectiveFrom_EffectiveTo",
                schema: "financial_payment",
                table: "HsnCodes",
                columns: new[] { "EffectiveFrom", "EffectiveTo" });

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_Category",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodes_ApplicableGstRate",
                schema: "financial_payment",
                table: "HsnCodes",
                column: "ApplicableGstRate");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeHistories_HsnCodeId",
                schema: "financial_payment",
                table: "HsnCodeHistories",
                column: "HsnCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeHistories_ModifiedAt",
                schema: "financial_payment",
                table: "HsnCodeHistories",
                column: "ModifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeHistories_ModifiedBy",
                schema: "financial_payment",
                table: "HsnCodeHistories",
                column: "ModifiedBy");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeGstMappings_HsnCodeId",
                schema: "financial_payment",
                table: "HsnCodeGstMappings",
                column: "HsnCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeGstMappings_GstRate",
                schema: "financial_payment",
                table: "HsnCodeGstMappings",
                column: "GstRate");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeGstMappings_Status",
                schema: "financial_payment",
                table: "HsnCodeGstMappings",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeGstMappings_EffectiveFrom_EffectiveTo",
                schema: "financial_payment",
                table: "HsnCodeGstMappings",
                columns: new[] { "EffectiveFrom", "EffectiveTo" });

            migrationBuilder.CreateIndex(
                name: "IX_HsnCodeGstMappings_HsnCodeId_Status",
                schema: "financial_payment",
                table: "HsnCodeGstMappings",
                columns: new[] { "HsnCodeId", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop tables in reverse order of creation (due to foreign key constraints)
            migrationBuilder.DropTable(
                name: "HsnCodeGstMappings",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "HsnCodeHistories",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TdsConfigurationHistories",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "GstConfigurationHistories",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxConfigurationRules",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxConfigurationHistories",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "HsnCodes",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TdsConfigurations",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "GstConfigurations",
                schema: "financial_payment");

            migrationBuilder.DropTable(
                name: "TaxConfigurations",
                schema: "financial_payment");
        }
    }
}
