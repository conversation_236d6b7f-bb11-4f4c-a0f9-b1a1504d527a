using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Driver-specific communication service
/// </summary>
public class DriverCommunicationService : IDriverCommunicationService
{
    private readonly INotificationService _notificationService;
    private readonly IVoiceInstructionService _voiceInstructionService;
    private readonly IOfflineMessageService _offlineMessageService;
    private readonly ILocalizationService _localizationService;
    private readonly ITemplateService _templateService;
    private readonly IConnectionTrackingService _connectionTracker;
    private readonly ILogger<DriverCommunicationService> _logger;

    public DriverCommunicationService(
        INotificationService notificationService,
        IVoiceInstructionService voiceInstructionService,
        IOfflineMessageService offlineMessageService,
        ILocalizationService localizationService,
        ITemplateService templateService,
        IConnectionTrackingService connectionTracker,
        ILogger<DriverCommunicationService> logger)
    {
        _notificationService = notificationService;
        _voiceInstructionService = voiceInstructionService;
        _offlineMessageService = offlineMessageService;
        _localizationService = localizationService;
        _templateService = templateService;
        _connectionTracker = connectionTracker;
        _logger = logger;
    }

    public async Task<DriverCommunicationResult> SendTripAssignmentAsync(
        DriverTripAssignment assignment,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isOnline = await IsDriverOnlineAsync(assignment.DriverId, cancellationToken);
            
            // Prepare notification content
            var content = await _templateService.RenderNotificationTemplateAsync(
                "driver_trip_assignment",
                assignment.PreferredLanguage,
                new Dictionary<string, object>
                {
                    ["tripId"] = assignment.TripId.ToString(),
                    ["pickupLocation"] = assignment.PickupLocation,
                    ["dropoffLocation"] = assignment.DropoffLocation,
                    ["pickupTime"] = assignment.PickupTime.ToString("HH:mm"),
                    ["customerName"] = assignment.CustomerName,
                    ["distance"] = assignment.EstimatedDistance.ToString("F1"),
                    ["duration"] = assignment.EstimatedDuration.ToString(@"hh\:mm"),
                    ["fare"] = assignment.EstimatedFare.ToString("C")
                },
                cancellationToken);

            var notificationRequest = new NotificationRequest
            {
                UserId = assignment.DriverId,
                MessageType = MessageType.TripAssignment,
                Content = content,
                Priority = Priority.High,
                PreferredChannel = isOnline ? NotificationChannel.Push : NotificationChannel.Sms,
                RelatedEntityId = assignment.TripId,
                RelatedEntityType = "Trip",
                Metadata = new Dictionary<string, string>
                {
                    ["driverOnline"] = isOnline.ToString(),
                    ["assignmentType"] = assignment.AssignmentType.ToString(),
                    ["urgency"] = assignment.IsUrgent.ToString()
                }
            };

            var result = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            // Send voice instruction if driver is online and voice is enabled
            if (isOnline && assignment.EnableVoiceInstructions)
            {
                await SendVoiceInstructionAsync(assignment, cancellationToken);
            }

            _logger.LogInformation("Trip assignment sent to driver {DriverId} for trip {TripId}", 
                assignment.DriverId, assignment.TripId);

            return DriverCommunicationResult.Success(result.MessageId, isOnline);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send trip assignment to driver {DriverId}", assignment.DriverId);
            return DriverCommunicationResult.Failure($"Failed to send trip assignment: {ex.Message}");
        }
    }

    public async Task<DriverCommunicationResult> SendTripUpdateAsync(
        DriverTripUpdate update,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isOnline = await IsDriverOnlineAsync(update.DriverId, cancellationToken);
            
            var templateKey = update.UpdateType switch
            {
                TripUpdateType.Started => "trip_started_driver",
                TripUpdateType.CustomerPickedUp => "customer_picked_up",
                TripUpdateType.InTransit => "trip_in_transit",
                TripUpdateType.Completed => "trip_completed_driver",
                TripUpdateType.Cancelled => "trip_cancelled_driver",
                TripUpdateType.Delayed => "trip_delayed",
                _ => "trip_update_generic"
            };

            var content = await _templateService.RenderNotificationTemplateAsync(
                templateKey,
                update.PreferredLanguage,
                update.Parameters,
                cancellationToken);

            var notificationRequest = new NotificationRequest
            {
                UserId = update.DriverId,
                MessageType = MessageType.TripUpdate,
                Content = content,
                Priority = update.Priority,
                PreferredChannel = isOnline ? NotificationChannel.Push : NotificationChannel.Sms,
                RelatedEntityId = update.TripId,
                RelatedEntityType = "Trip",
                Metadata = new Dictionary<string, string>
                {
                    ["updateType"] = update.UpdateType.ToString(),
                    ["driverOnline"] = isOnline.ToString()
                }
            };

            var result = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            _logger.LogInformation("Trip update sent to driver {DriverId} for trip {TripId}: {UpdateType}", 
                update.DriverId, update.TripId, update.UpdateType);

            return DriverCommunicationResult.Success(result.MessageId, isOnline);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send trip update to driver {DriverId}", update.DriverId);
            return DriverCommunicationResult.Failure($"Failed to send trip update: {ex.Message}");
        }
    }

    public async Task<DriverCommunicationResult> SendNavigationInstructionAsync(
        DriverNavigationInstruction instruction,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isOnline = await IsDriverOnlineAsync(instruction.DriverId, cancellationToken);

            if (!isOnline)
            {
                // Store for offline delivery
                await StoreOfflineInstructionAsync(instruction, cancellationToken);
                return DriverCommunicationResult.Success(null, false, "Stored for offline delivery");
            }

            // Send real-time navigation instruction
            var content = await _templateService.RenderNotificationTemplateAsync(
                "navigation_instruction",
                instruction.PreferredLanguage,
                new Dictionary<string, object>
                {
                    ["instruction"] = instruction.Instruction,
                    ["distance"] = instruction.Distance.ToString("F1"),
                    ["direction"] = instruction.Direction,
                    ["streetName"] = instruction.StreetName ?? "",
                    ["landmark"] = instruction.Landmark ?? ""
                },
                cancellationToken);

            var notificationRequest = new NotificationRequest
            {
                UserId = instruction.DriverId,
                MessageType = MessageType.Navigation,
                Content = content,
                Priority = Priority.High,
                PreferredChannel = NotificationChannel.Push,
                RelatedEntityId = instruction.TripId,
                RelatedEntityType = "Trip",
                Metadata = new Dictionary<string, string>
                {
                    ["instructionType"] = instruction.InstructionType.ToString(),
                    ["latitude"] = instruction.CurrentLocation.Latitude.ToString(),
                    ["longitude"] = instruction.CurrentLocation.Longitude.ToString()
                }
            };

            var result = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            // Send voice instruction if enabled
            if (instruction.EnableVoice)
            {
                await _voiceInstructionService.SendVoiceNavigationAsync(instruction, cancellationToken);
            }

            return DriverCommunicationResult.Success(result.MessageId, true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send navigation instruction to driver {DriverId}", instruction.DriverId);
            return DriverCommunicationResult.Failure($"Failed to send navigation instruction: {ex.Message}");
        }
    }

    public async Task<DriverCommunicationResult> SendEmergencyAlertAsync(
        DriverEmergencyAlert alert,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Emergency alerts use all available channels for maximum reach
            var channels = new[] { NotificationChannel.Push, NotificationChannel.Sms, NotificationChannel.Voice };
            var results = new List<NotificationSendResult>();

            var content = await _templateService.RenderNotificationTemplateAsync(
                "emergency_alert",
                alert.PreferredLanguage,
                new Dictionary<string, object>
                {
                    ["alertType"] = alert.AlertType.ToString(),
                    ["message"] = alert.Message,
                    ["location"] = alert.Location ?? "Unknown",
                    ["timestamp"] = alert.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")
                },
                cancellationToken);

            foreach (var channel in channels)
            {
                var notificationRequest = new NotificationRequest
                {
                    UserId = alert.DriverId,
                    MessageType = MessageType.Emergency,
                    Content = content,
                    Priority = Priority.Critical,
                    PreferredChannel = channel,
                    RelatedEntityId = alert.TripId,
                    RelatedEntityType = "Trip",
                    Metadata = new Dictionary<string, string>
                    {
                        ["alertType"] = alert.AlertType.ToString(),
                        ["severity"] = alert.Severity.ToString(),
                        ["requiresResponse"] = alert.RequiresResponse.ToString()
                    }
                };

                var result = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);
                results.Add(result);
            }

            _logger.LogCritical("Emergency alert sent to driver {DriverId}: {AlertType}", 
                alert.DriverId, alert.AlertType);

            var successCount = results.Count(r => r.IsSuccess);
            return DriverCommunicationResult.Success(
                results.FirstOrDefault(r => r.IsSuccess)?.MessageId,
                true,
                $"Emergency alert sent via {successCount}/{results.Count} channels");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send emergency alert to driver {DriverId}", alert.DriverId);
            return DriverCommunicationResult.Failure($"Failed to send emergency alert: {ex.Message}");
        }
    }

    public async Task<List<OfflineMessage>> GetOfflineMessagesAsync(
        Guid driverId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _offlineMessageService.GetOfflineMessagesAsync(driverId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get offline messages for driver {DriverId}", driverId);
            return new List<OfflineMessage>();
        }
    }

    public async Task<bool> MarkMessagesAsDeliveredAsync(
        Guid driverId,
        List<Guid> messageIds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _offlineMessageService.MarkOfflineMessagesAsDeliveredAsync(driverId, messageIds, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark messages as delivered for driver {DriverId}", driverId);
            return false;
        }
    }

    public async Task<bool> IsDriverOnlineAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        try
        {
            var connections = await _connectionTracker.GetUserConnectionsAsync(driverId);
            return connections.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check online status for driver {DriverId}", driverId);
            return false;
        }
    }

    private async Task SendVoiceInstructionAsync(DriverTripAssignment assignment, CancellationToken cancellationToken)
    {
        try
        {
            var voiceInstruction = new VoiceInstruction
            {
                DriverId = assignment.DriverId,
                TripId = assignment.TripId,
                Message = $"New trip assignment. Pickup at {assignment.PickupLocation} at {assignment.PickupTime:HH:mm}. Customer: {assignment.CustomerName}.",
                Language = assignment.PreferredLanguage,
                Priority = VoicePriority.High
            };

            await _voiceInstructionService.SendVoiceInstructionAsync(voiceInstruction, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send voice instruction for trip assignment to driver {DriverId}", assignment.DriverId);
        }
    }

    private async Task StoreOfflineInstructionAsync(DriverNavigationInstruction instruction, CancellationToken cancellationToken)
    {
        try
        {
            // Create a message for offline storage
            var message = Domain.Entities.Message.Create(
                Guid.Empty, // System message
                Guid.Empty, // No conversation
                MessageContent.Create("Navigation", instruction.Instruction, instruction.PreferredLanguage),
                MessageType.Navigation,
                Priority.High,
                instruction.TripId,
                "Trip",
                new Dictionary<string, string>
                {
                    ["instructionType"] = instruction.InstructionType.ToString(),
                    ["distance"] = instruction.Distance.ToString(),
                    ["direction"] = instruction.Direction
                });

            await _offlineMessageService.StoreOfflineMessageAsync(instruction.DriverId, message, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store offline navigation instruction for driver {DriverId}", instruction.DriverId);
        }
    }
}
