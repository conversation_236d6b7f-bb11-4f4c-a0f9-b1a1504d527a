using MediatR;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.Commands.UpdateInvoice;

public class UpdateInvoiceCommand : IRequest<bool>
{
    public Guid InvoiceId { get; set; }
    public string? Title { get; set; }
    public string? Description { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Notes { get; set; }
    public InvoiceStatus? Status { get; set; }
    public List<UpdateInvoiceLineItemDto>? LineItems { get; set; }
    public Guid UpdatedBy { get; set; } // For authorization
}

public class UpdateInvoiceLineItemDto
{
    public Guid? Id { get; set; } // Null for new items
    public string Description { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public string Currency { get; set; } = "USD";
    public decimal? TaxRate { get; set; }
    public bool IsDeleted { get; set; } // For marking items for deletion
}
