using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using SharedKernel.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace FinancialPayment.Infrastructure.Services;

/// <summary>
/// Tax-aware payment service that handles payments with comprehensive tax calculations
/// </summary>
public class TaxAwarePaymentService : IPaymentService
{
    private readonly IPaymentService _basePaymentService;
    private readonly ITaxConfigurationService _taxConfigurationService;
    private readonly IPaymentRepository _paymentRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TaxAwarePaymentService> _logger;

    public TaxAwarePaymentService(
        IPaymentService basePaymentService,
        ITaxConfigurationService taxConfigurationService,
        IPaymentRepository paymentRepository,
        IUnitOfWork unitOfWork,
        ILogger<TaxAwarePaymentService> logger)
    {
        _basePaymentService = basePaymentService;
        _taxConfigurationService = taxConfigurationService;
        _paymentRepository = paymentRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<PaymentResult> ProcessPaymentAsync(Money amount, Guid userId, string? paymentMethodId = null)
    {
        return await _basePaymentService.ProcessPaymentAsync(amount, userId, paymentMethodId);
    }

    public async Task<PaymentResult> ProcessCommissionPaymentAsync(Guid commissionId, Money amount, Guid brokerId, string? paymentMethodId = null)
    {
        _logger.LogInformation("Processing commission payment {CommissionId} with tax considerations", commissionId);

        try
        {
            // For commission payments, the amount should already include tax calculations
            // if it's an EnhancedCommission, but we'll verify and log tax details
            var result = await _basePaymentService.ProcessCommissionPaymentAsync(commissionId, amount, brokerId, paymentMethodId);

            if (result.IsSuccess)
            {
                // Log tax-related payment details for audit purposes
                await LogTaxPaymentDetailsAsync(commissionId, amount, brokerId, result.TransactionId!);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing commission payment {CommissionId}", commissionId);
            throw;
        }
    }

    public async Task<PaymentResult> ProcessPaymentWithTaxAsync(
        Money baseAmount,
        Guid userId,
        ServiceCategory serviceCategory,
        TaxJurisdiction jurisdiction,
        EntityType entityType,
        TdsSection? tdsSection = null,
        string? hsnCode = null,
        bool hasPan = true,
        string? paymentMethodId = null)
    {
        _logger.LogInformation("Processing payment with comprehensive tax calculation for user {UserId}", userId);

        try
        {
            // Calculate comprehensive tax
            var decimal = await _taxConfigurationService.CalculateComprehensiveTaxAsync(
                baseAmount,
                serviceCategory,
                jurisdiction,
                entityType,
                tdsSection,
                hsnCode,
                hasPan);

            // Create tax-aware payment record
            var taxAwarePayment = new TaxAwarePayment(
                userId,
                baseAmount,
                decimal,
                serviceCategory,
                jurisdiction,
                entityType,
                tdsSection,
                hsnCode,
                hasPan);

            // Process payment for the total amount (base + GST - TDS)
            var paymentAmount = decimal.NetAmount;
            var paymentResult = await _basePaymentService.ProcessPaymentAsync(paymentAmount, userId, paymentMethodId);

            if (paymentResult.IsSuccess)
            {
                taxAwarePayment.MarkAsProcessed(paymentResult.TransactionId!, paymentResult.ProcessedAt);
                await _paymentRepository.AddAsync(taxAwarePayment);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Successfully processed tax-aware payment {PaymentId} for user {UserId}. " +
                                     "Base: {BaseAmount}, GST: {GstAmount}, TDS: {TdsAmount}, Net: {NetAmount}",
                    taxAwarePayment.Id, userId,
                    baseAmount.Amount,
                    decimal.GstAmount.Amount,
                    decimal.TdsAmount.Amount,
                    paymentAmount.Amount);

                // Enhance payment result with tax details
                paymentResult.TaxDetails = new PaymentTaxDetails
                {
                    BaseAmount = baseAmount,
                    GstAmount = decimal.GstAmount,
                    TdsAmount = decimal.TdsAmount,
                    TotalTaxAmount = decimal.TotalTaxAmount,
                    NetAmount = decimal.NetAmount,
                    EffectiveGstRate = decimal.EffectiveGstRate,
                    EffectiveTdsRate = decimal.EffectiveTdsRate,
                    IsReverseChargeApplicable = decimal.IsReverseChargeApplicable,
                    AppliedHsnCode = decimal.AppliedHsnCode,
                    TaxWarnings = decimal.Warnings
                };
            }
            else
            {
                taxAwarePayment.MarkAsFailed(paymentResult.ErrorMessage ?? "Payment processing failed");
                await _paymentRepository.AddAsync(taxAwarePayment);
                await _unitOfWork.SaveChangesAsync();
            }

            return paymentResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing tax-aware payment for user {UserId}", userId);
            throw;
        }
    }

    public async Task<PaymentResult> ProcessRefundAsync(string transactionId, Money amount, string reason)
    {
        _logger.LogInformation("Processing refund for transaction {TransactionId}", transactionId);

        try
        {
            // Check if this is a tax-aware payment
            var taxAwarePayment = await _paymentRepository.GetByTransactionIdAsync(transactionId);
            
            if (taxAwarePayment != null)
            {
                // Calculate refund with tax considerations
                var refundResult = await ProcessTaxAwareRefundAsync(taxAwarePayment, amount, reason);
                return refundResult;
            }

            // Fall back to base payment service for non-tax-aware payments
            return await _basePaymentService.ProcessRefundAsync(transactionId, amount, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund for transaction {TransactionId}", transactionId);
            throw;
        }
    }

    public async Task<string> CreatePaymentIntentAsync(Money amount, Guid userId, string? paymentMethodId = null)
    {
        return await _basePaymentService.CreatePaymentIntentAsync(amount, userId, paymentMethodId);
    }

    public async Task<PaymentResult> ProcessUpiPaymentAsync(Money amount, Guid userId, string upiId)
    {
        return await _basePaymentService.ProcessUpiPaymentAsync(amount, userId, upiId);
    }

    public async Task<PaymentResult> ProcessCardPaymentAsync(Money amount, Guid userId, string cardToken)
    {
        return await _basePaymentService.ProcessCardPaymentAsync(amount, userId, cardToken);
    }

    public async Task<PaymentResult> ProcessNetBankingPaymentAsync(Money amount, Guid userId, string bankCode)
    {
        return await _basePaymentService.ProcessNetBankingPaymentAsync(amount, userId, bankCode);
    }

    public async Task<PaymentResult> ProcessWalletPaymentAsync(Money amount, Guid userId, string walletProvider)
    {
        return await _basePaymentService.ProcessWalletPaymentAsync(amount, userId, walletProvider);
    }

    private async Task<PaymentResult> ProcessTaxAwareRefundAsync(TaxAwarePayment originalPayment, Money refundAmount, string reason)
    {
        _logger.LogInformation("Processing tax-aware refund for payment {PaymentId}", originalPayment.Id);

        try
        {
            // Calculate proportional tax refund
            var refundRatio = refundAmount.Amount / originalPayment.TaxDetails.NetAmount.Amount;
            var gstRefund = new Money(originalPayment.TaxDetails.GstAmount.Amount * refundRatio, refundAmount.Currency);
            var tdsRefund = new Money(originalPayment.TaxDetails.TdsAmount.Amount * refundRatio, refundAmount.Currency);
            var baseRefund = new Money(originalPayment.BaseAmount.Amount * refundRatio, refundAmount.Currency);

            // Process the actual refund
            var refundResult = await _basePaymentService.ProcessRefundAsync(
                originalPayment.TransactionId!, refundAmount, reason);

            if (refundResult.IsSuccess)
            {
                // Create refund record with tax details
                var taxAwareRefund = new TaxAwareRefund(
                    originalPayment.Id,
                    baseRefund,
                    gstRefund,
                    tdsRefund,
                    refundAmount,
                    reason,
                    refundResult.TransactionId!);

                await _paymentRepository.AddRefundAsync(taxAwareRefund);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Successfully processed tax-aware refund {RefundId} for payment {PaymentId}",
                    taxAwareRefund.Id, originalPayment.Id);

                // Enhance refund result with tax details
                refundResult.TaxDetails = new PaymentTaxDetails
                {
                    BaseAmount = baseRefund,
                    GstAmount = gstRefund,
                    TdsAmount = tdsRefund,
                    TotalTaxAmount = gstRefund + tdsRefund,
                    NetAmount = refundAmount,
                    EffectiveGstRate = originalPayment.TaxDetails.EffectiveGstRate,
                    EffectiveTdsRate = originalPayment.TaxDetails.EffectiveTdsRate,
                    IsReverseChargeApplicable = originalPayment.TaxDetails.IsReverseChargeApplicable,
                    AppliedHsnCode = originalPayment.TaxDetails.AppliedHsnCode,
                    TaxWarnings = new List<string> { $"Proportional refund: {refundRatio:P2}" }
                };
            }

            return refundResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing tax-aware refund for payment {PaymentId}", originalPayment.Id);
            throw;
        }
    }

    private async Task LogTaxPaymentDetailsAsync(Guid commissionId, Money amount, Guid brokerId, string transactionId)
    {
        try
        {
            // Log tax payment details for audit and compliance purposes
            _logger.LogInformation("Tax payment details logged for commission {CommissionId}, " +
                                 "broker {BrokerId}, amount {Amount}, transaction {TransactionId}",
                commissionId, brokerId, amount.Amount, transactionId);

            // Additional tax compliance logging can be added here
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log tax payment details for commission {CommissionId}", commissionId);
            // Don't throw - this is just for logging
        }
    }
}

/// <summary>
/// Enhanced payment result with tax details
/// </summary>
public class PaymentTaxDetails
{
    public Money BaseAmount { get; set; } = Money.Zero("INR");
    public Money GstAmount { get; set; } = Money.Zero("INR");
    public Money TdsAmount { get; set; } = Money.Zero("INR");
    public Money TotalTaxAmount { get; set; } = Money.Zero("INR");
    public Money NetAmount { get; set; } = Money.Zero("INR");
    public decimal EffectiveGstRate { get; set; }
    public decimal EffectiveTdsRate { get; set; }
    public bool IsReverseChargeApplicable { get; set; }
    public string? AppliedHsnCode { get; set; }
    public List<string> TaxWarnings { get; set; } = new();
}
