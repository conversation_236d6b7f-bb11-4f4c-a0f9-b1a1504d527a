using Shared.Domain.ValueObjects;

namespace NetworkFleetManagement.Domain.ValueObjects;

public class Location : ValueObject
{
    public double Latitude { get; private set; }
    public double Longitude { get; private set; }
    public string? Address { get; private set; }
    public string? City { get; private set; }
    public string? State { get; private set; }
    public string? Country { get; private set; }
    public string? PostalCode { get; private set; }
    public DateTime? Timestamp { get; private set; }

    private Location() { }

    public Location(double latitude, double longitude, string? address = null,
        string? city = null, string? state = null, string? country = null,
        string? postalCode = null, DateTime? timestamp = null)
    {
        if (latitude < -90 || latitude > 90)
            throw new ArgumentException("Latitude must be between -90 and 90 degrees", nameof(latitude));

        if (longitude < -180 || longitude > 180)
            throw new ArgumentException("Longitude must be between -180 and 180 degrees", nameof(longitude));

        Latitude = latitude;
        Longitude = longitude;
        Address = address;
        City = city;
        State = state;
        Country = country;
        PostalCode = postalCode;
        Timestamp = timestamp ?? DateTime.UtcNow;
    }

    public double DistanceTo(Location other)
    {
        // Haversine formula for calculating distance between two points
        const double R = 6371; // Earth's radius in kilometers

        var lat1Rad = Latitude * Math.PI / 180;
        var lat2Rad = other.Latitude * Math.PI / 180;
        var deltaLatRad = (other.Latitude - Latitude) * Math.PI / 180;
        var deltaLonRad = (other.Longitude - Longitude) * Math.PI / 180;

        var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return R * c;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Latitude;
        yield return Longitude;
        yield return Address ?? string.Empty;
        yield return City ?? string.Empty;
        yield return State ?? string.Empty;
        yield return Country ?? string.Empty;
        yield return PostalCode ?? string.Empty;
    }

    public override string ToString()
    {
        return $"{Latitude}, {Longitude}" + (string.IsNullOrEmpty(Address) ? "" : $" ({Address})");
    }
}
