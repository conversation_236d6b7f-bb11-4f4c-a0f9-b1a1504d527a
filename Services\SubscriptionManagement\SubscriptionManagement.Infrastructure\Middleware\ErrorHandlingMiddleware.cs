using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Exceptions;
using System.Net;
using System.Text.Json;
using FluentValidation;

namespace SubscriptionManagement.Infrastructure.Middleware
{
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ErrorHandlingMiddleware> _logger;

        public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred while processing the request");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            var response = context.Response;
            response.ContentType = "application/json";

            var errorResponse = new ErrorResponse();

            switch (exception)
            {
                case ValidationException validationEx:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    errorResponse.Message = "Validation failed";
                    errorResponse.Details = validationEx.Errors.Select(e => new ErrorDetail
                    {
                        Field = e.PropertyName,
                        Message = e.ErrorMessage,
                        Code = e.ErrorCode
                    }).ToList();
                    break;

                case SubscriptionDomainException domainEx:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    errorResponse.Message = domainEx.Message;
                    errorResponse.Code = "DOMAIN_ERROR";
                    break;

                case UnauthorizedAccessException:
                    response.StatusCode = (int)HttpStatusCode.Forbidden;
                    errorResponse.Message = "Access denied";
                    errorResponse.Code = "ACCESS_DENIED";
                    break;

                case ArgumentException argEx:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    errorResponse.Message = argEx.Message;
                    errorResponse.Code = "INVALID_ARGUMENT";
                    break;

                case InvalidOperationException opEx:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    errorResponse.Message = opEx.Message;
                    errorResponse.Code = "INVALID_OPERATION";
                    break;

                case FileNotFoundException:
                    response.StatusCode = (int)HttpStatusCode.NotFound;
                    errorResponse.Message = "Resource not found";
                    errorResponse.Code = "NOT_FOUND";
                    break;

                case TimeoutException:
                    response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                    errorResponse.Message = "Request timeout";
                    errorResponse.Code = "TIMEOUT";
                    break;

                case HttpRequestException httpEx:
                    response.StatusCode = (int)HttpStatusCode.BadGateway;
                    errorResponse.Message = "External service error";
                    errorResponse.Code = "EXTERNAL_SERVICE_ERROR";
                    errorResponse.Details = new List<ErrorDetail>
                    {
                        new ErrorDetail { Message = httpEx.Message }
                    };
                    break;

                default:
                    response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    errorResponse.Message = "An internal server error occurred";
                    errorResponse.Code = "INTERNAL_ERROR";
                    
                    // Only include exception details in development
                    if (IsDevelopmentEnvironment())
                    {
                        errorResponse.Details = new List<ErrorDetail>
                        {
                            new ErrorDetail 
                            { 
                                Message = exception.Message,
                                Field = exception.GetType().Name
                            }
                        };
                    }
                    break;
            }

            // Add correlation ID if available
            if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
            {
                errorResponse.CorrelationId = correlationId.FirstOrDefault();
                response.Headers.Add("X-Correlation-ID", correlationId);
            }

            // Add timestamp
            errorResponse.Timestamp = DateTime.UtcNow;

            // Add request path for debugging
            errorResponse.Path = context.Request.Path;

            var jsonResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await response.WriteAsync(jsonResponse);
        }

        private bool IsDevelopmentEnvironment()
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return string.Equals(environment, "Development", StringComparison.OrdinalIgnoreCase);
        }
    }

    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public string? Code { get; set; }
        public List<ErrorDetail> Details { get; set; } = new();
        public string? CorrelationId { get; set; }
        public DateTime Timestamp { get; set; }
        public string? Path { get; set; }
    }

    public class ErrorDetail
    {
        public string? Field { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Code { get; set; }
    }
}
