using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.DTOs
{
    public class PlanDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsPublic { get; set; }
        public DateTime? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public PlanLimitsDto Limits { get; set; } = new();
        public List<PlanFeatureDto> Features { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreatePlanDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public BillingInterval BillingInterval { get; set; }
        public int BillingIntervalCount { get; set; } = 1;
        public int? CustomBillingDays { get; set; }
        public bool IsPublic { get; set; } = true;
        public int? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public CreatePlanLimitsDto Limits { get; set; } = new();
        public List<CreatePlanFeatureDto> Features { get; set; } = new();
    }

    public class UpdatePlanDto
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public decimal? Price { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsPublic { get; set; }
        public UpdatePlanLimitsDto? Limits { get; set; }
    }

    public class PlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool IsUnlimited { get; set; }
    }

    public class CreatePlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool IsUnlimited { get; set; }
    }

    public class UpdatePlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool? IsUnlimited { get; set; }
    }

    public class PlanFeatureDto
    {
        public Guid Id { get; set; }
        public FeatureType FeatureType { get; set; }
        public FeatureAccessType AccessType { get; set; }
        public int? LimitValue { get; set; }
        public string? Description { get; set; }
        public bool IsEnabled { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string AccessDescription { get; set; } = string.Empty;
    }

    public class CreatePlanFeatureDto
    {
        public FeatureType FeatureType { get; set; }
        public FeatureAccessType AccessType { get; set; }
        public int? LimitValue { get; set; }
        public string? Description { get; set; }
    }

    public class UpdatePlanFeatureDto
    {
        public FeatureAccessType AccessType { get; set; }
        public int? LimitValue { get; set; }
        public string? Description { get; set; }
        public bool? IsEnabled { get; set; }
    }

    public class PlanSummaryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsPopular { get; set; }
        public List<string> KeyFeatures { get; set; } = new();
    }
}
