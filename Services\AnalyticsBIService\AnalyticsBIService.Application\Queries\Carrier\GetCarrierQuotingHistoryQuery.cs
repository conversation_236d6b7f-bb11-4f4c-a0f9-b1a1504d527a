using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Enums;
using MediatR;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Query to get carrier RFQ response history
/// </summary>
public record GetCarrierRFQResponseHistoryQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteFilter = null,
    string? BrokerFilter = null
) : IRequest<CarrierRFQResponseHistoryDto>;

/// <summary>
/// Query to get carrier quote success rate analytics
/// </summary>
public record GetCarrierQuoteSuccessRateQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteFilter = null,
    string? BrokerFilter = null
) : IRequest<CarrierQuoteSuccessRateDto>;

/// <summary>
/// Query to get carrier pricing analytics
/// </summary>
public record GetCarrierPricingAnalyticsQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    string? RouteFilter = null,
    string? ServiceTypeFilter = null
) : IRequest<CarrierPricingAnalyticsDto>;

/// <summary>
/// Query to get carrier market comparison analytics
/// </summary>
public record GetCarrierMarketComparisonQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    string? RouteFilter = null,
    string? ServiceTypeFilter = null
) : IRequest<CarrierMarketComparisonDto>;

/// <summary>
/// Query to get comprehensive carrier quoting history (aggregates all above)
/// </summary>
public record GetCarrierQuotingHistoryQuery(
    Guid CarrierId,
    DateTime FromDate,
    DateTime ToDate,
    TimePeriod Period = TimePeriod.Daily,
    bool IncludeRFQResponseHistory = true,
    bool IncludeQuoteSuccessRate = true,
    bool IncludePricingAnalytics = true,
    bool IncludeMarketComparison = true,
    bool IncludeTrends = true,
    string? RouteFilter = null,
    string? BrokerFilter = null
) : IRequest<CarrierQuotingHistoryDto>;
