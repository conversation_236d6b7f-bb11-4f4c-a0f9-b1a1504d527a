using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IMetricsService : IDisposable
    {
        /// <summary>
        /// Record a payment proof upload event
        /// </summary>
        void RecordPaymentProofUpload(PaymentProofStatus status, string? paymentMethod = null);

        /// <summary>
        /// Record a payment proof verification event
        /// </summary>
        void RecordPaymentProofVerification(PaymentProofStatus fromStatus, PaymentProofStatus toStatus, double durationSeconds);

        /// <summary>
        /// Record a notification sent event
        /// </summary>
        void RecordNotificationSent(NotificationType type, string channel, bool success, double? durationSeconds = null);

        /// <summary>
        /// Record bulk notification processing metrics
        /// </summary>
        void RecordBulkNotificationProcessing(int totalNotifications, int successCount, int failureCount, double durationSeconds);

        /// <summary>
        /// Update the gauge for pending payment proofs
        /// </summary>
        void UpdatePendingPaymentProofs(int count);

        /// <summary>
        /// Update the gauge for pending notifications
        /// </summary>
        void UpdatePendingNotifications(int count);

        /// <summary>
        /// Record a conversion event (notification to subscription renewal)
        /// </summary>
        void RecordConversionEvent(NotificationType notificationType, string channel, bool converted, TimeSpan timeSinceNotification);

        /// <summary>
        /// Record API request metrics
        /// </summary>
        void RecordApiRequest(string endpoint, string method, int statusCode, double durationMs);

        /// <summary>
        /// Record cache hit/miss metrics
        /// </summary>
        void RecordCacheHit(string cacheKey, bool hit);

        /// <summary>
        /// Record database query metrics
        /// </summary>
        void RecordDatabaseQuery(string operation, string table, double durationMs, bool success);
    }
}
