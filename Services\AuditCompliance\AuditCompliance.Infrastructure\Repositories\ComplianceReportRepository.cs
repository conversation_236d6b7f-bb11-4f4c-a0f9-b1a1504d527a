using Microsoft.EntityFrameworkCore;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Infrastructure.Persistence;

namespace AuditCompliance.Infrastructure.Repositories;

public class ComplianceReportRepository : IComplianceReportRepository
{
    private readonly AuditComplianceDbContext _context;

    public ComplianceReportRepository(AuditComplianceDbContext context)
    {
        _context = context;
    }

    public async Task<ComplianceReport?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ComplianceReports
            .Include(cr => cr.Items)
            .FirstOrDefaultAsync(cr => cr.Id == id, cancellationToken);
    }

    public async Task<ComplianceReport?> GetByReportNumberAsync(string reportNumber, CancellationToken cancellationToken = default)
    {
        return await _context.ComplianceReports
            .Include(cr => cr.Items)
            .FirstOrDefaultAsync(cr => cr.ReportNumber == reportNumber, cancellationToken);
    }

    public async Task<List<ComplianceReport>> GetReportsAsync(
        ComplianceStandard? standard = null,
        ComplianceStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? generatedBy = null,
        bool? isAutomated = null,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _context.ComplianceReports
            .Include(cr => cr.Items)
            .AsQueryable();

        if (standard.HasValue)
            query = query.Where(cr => cr.Standard == standard.Value);

        if (status.HasValue)
            query = query.Where(cr => cr.Status == status.Value);

        if (fromDate.HasValue)
            query = query.Where(cr => cr.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(cr => cr.CreatedAt <= toDate.Value);

        if (generatedBy.HasValue)
            query = query.Where(cr => cr.GeneratedBy == generatedBy.Value);

        if (isAutomated.HasValue)
            query = query.Where(cr => cr.IsAutomated == isAutomated.Value);

        return await query
            .OrderByDescending(cr => cr.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetReportsCountAsync(
        ComplianceStandard? standard = null,
        ComplianceStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        Guid? generatedBy = null,
        bool? isAutomated = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.ComplianceReports.AsQueryable();

        if (standard.HasValue)
            query = query.Where(cr => cr.Standard == standard.Value);

        if (status.HasValue)
            query = query.Where(cr => cr.Status == status.Value);

        if (fromDate.HasValue)
            query = query.Where(cr => cr.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(cr => cr.CreatedAt <= toDate.Value);

        if (generatedBy.HasValue)
            query = query.Where(cr => cr.GeneratedBy == generatedBy.Value);

        if (isAutomated.HasValue)
            query = query.Where(cr => cr.IsAutomated == isAutomated.Value);

        return await query.CountAsync(cancellationToken);
    }

    public async Task<List<ComplianceReport>> GetPendingReportsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ComplianceReports
            .Include(cr => cr.Items)
            .Where(cr => cr.Status == ComplianceStatus.Pending || cr.Status == ComplianceStatus.InProgress)
            .OrderBy(cr => cr.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<ComplianceReport> AddAsync(ComplianceReport report, CancellationToken cancellationToken = default)
    {
        _context.ComplianceReports.Add(report);
        await _context.SaveChangesAsync(cancellationToken);
        return report;
    }

    public async Task UpdateAsync(ComplianceReport report, CancellationToken cancellationToken = default)
    {
        _context.ComplianceReports.Update(report);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(ComplianceReport report, CancellationToken cancellationToken = default)
    {
        _context.ComplianceReports.Remove(report);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
