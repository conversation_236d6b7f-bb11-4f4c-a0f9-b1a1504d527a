using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Repositories;

namespace TripManagement.Application.Commands.ReassignDriver;

public class ReassignDriverCommandHandler : IRequestHandler<ReassignDriverCommand, bool>
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageBroker _messageBroker;
    private readonly ILogger<ReassignDriverCommandHandler> _logger;

    public ReassignDriverCommandHandler(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMessageBroker messageBroker,
        ILogger<ReassignDriverCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _messageBroker = messageBroker;
        _logger = logger;
    }

    public async Task<bool> Handle(ReassignDriverCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Reassigning trip {TripId} to driver {NewDriverId}", 
            request.TripId, request.NewDriverId);

        try
        {
            // Get trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                _logger.LogWarning("Trip {TripId} not found", request.TripId);
                throw new ArgumentException($"Trip {request.TripId} not found");
            }

            // Validate trip can be reassigned
            if (trip.Status == TripStatus.Completed)
            {
                _logger.LogWarning("Cannot reassign completed trip {TripId}", request.TripId);
                throw new InvalidOperationException("Cannot reassign completed trips");
            }

            if (trip.Status == TripStatus.Cancelled)
            {
                _logger.LogWarning("Cannot reassign cancelled trip {TripId}", request.TripId);
                throw new InvalidOperationException("Cannot reassign cancelled trips");
            }

            // Get new driver
            var newDriver = await _driverRepository.GetByIdAsync(request.NewDriverId, cancellationToken);
            if (newDriver == null)
            {
                _logger.LogWarning("New driver {DriverId} not found", request.NewDriverId);
                throw new ArgumentException($"Driver {request.NewDriverId} not found");
            }

            // Validate new driver is available
            if (newDriver.Status != DriverStatus.Available)
            {
                _logger.LogWarning("Driver {DriverId} is not available. Current status: {Status}",
                    request.NewDriverId, newDriver.Status);
                throw new InvalidOperationException($"Driver is not available. Current status: {newDriver.Status}");
            }

            // Store original assignments for event publishing
            var originalDriverId = trip.DriverId;
            var originalVehicleId = trip.VehicleId;

            // Release current driver if assigned
            Driver? currentDriver = null;
            if (trip.DriverId.HasValue)
            {
                currentDriver = await _driverRepository.GetByIdAsync(trip.DriverId.Value, cancellationToken);
                if (currentDriver != null)
                {
                    currentDriver.UpdateStatus(DriverStatus.Available);
                    _driverRepository.Update(currentDriver);
                }
            }

            // Release current vehicle if assigned and no new vehicle specified
            Vehicle? currentVehicle = null;
            if (trip.VehicleId.HasValue && !request.NewVehicleId.HasValue)
            {
                currentVehicle = await _vehicleRepository.GetByIdAsync(trip.VehicleId.Value, cancellationToken);
                if (currentVehicle != null)
                {
                    currentVehicle.UpdateStatus(VehicleStatus.Available);
                    _vehicleRepository.Update(currentVehicle);
                }
            }

            // Handle new vehicle assignment if specified
            Vehicle? newVehicle = null;
            if (request.NewVehicleId.HasValue)
            {
                newVehicle = await _vehicleRepository.GetByIdAsync(request.NewVehicleId.Value, cancellationToken);
                if (newVehicle == null)
                {
                    _logger.LogWarning("New vehicle {VehicleId} not found", request.NewVehicleId);
                    throw new ArgumentException($"Vehicle {request.NewVehicleId} not found");
                }

                if (newVehicle.Status != VehicleStatus.Available)
                {
                    _logger.LogWarning("Vehicle {VehicleId} is not available. Current status: {Status}",
                        request.NewVehicleId, newVehicle.Status);
                    throw new InvalidOperationException($"Vehicle is not available. Current status: {newVehicle.Status}");
                }

                newVehicle.UpdateStatus(VehicleStatus.InUse);
                _vehicleRepository.Update(newVehicle);
            }

            // Reassign trip
            var vehicleIdToAssign = request.NewVehicleId ?? trip.VehicleId;
            if (vehicleIdToAssign.HasValue)
            {
                trip.AssignDriverAndVehicle(request.NewDriverId, vehicleIdToAssign.Value);
            }
            else
            {
                // If no vehicle, we need to add a method to reassign just the driver
                // For now, we'll update the properties directly
                trip.GetType().GetProperty("DriverId")?.SetValue(trip, request.NewDriverId);
                trip.GetType().GetProperty("AssignedAt")?.SetValue(trip, DateTime.UtcNow);
            }

            // Update new driver status
            newDriver.UpdateStatus(DriverStatus.OnTrip);
            _driverRepository.Update(newDriver);

            // Save changes
            _tripRepository.Update(trip);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Publish integration events
            await _messageBroker.PublishAsync("trip.driver.reassigned", new
            {
                TripId = trip.Id,
                TripNumber = trip.TripNumber,
                OrderId = trip.OrderId,
                CarrierId = trip.CarrierId,
                OriginalDriverId = originalDriverId,
                NewDriverId = request.NewDriverId,
                OriginalVehicleId = originalVehicleId,
                NewVehicleId = request.NewVehicleId,
                Reason = request.Reason,
                AdditionalNotes = request.AdditionalNotes,
                ReassignedBy = request.ReassignedBy,
                ReassignedAt = DateTime.UtcNow,
                NotifyStakeholders = request.NotifyStakeholders
            }, cancellationToken);

            // Send notifications if requested
            if (request.NotifyStakeholders)
            {
                await _messageBroker.PublishAsync("communication.send.notification", new
                {
                    MessageType = "TripReassigned",
                    Subject = $"Trip Reassigned: {trip.TripNumber}",
                    Content = $"Trip {trip.TripNumber} has been reassigned to a new driver. Reason: {request.Reason}",
                    Priority = "High",
                    RelatedEntityId = trip.Id,
                    RelatedEntityType = "Trip",
                    Recipients = new[]
                    {
                        new { UserId = trip.CarrierId, UserType = "Carrier" },
                        originalDriverId.HasValue ? new { UserId = originalDriverId.Value, UserType = "Driver" } : null,
                        new { UserId = request.NewDriverId, UserType = "Driver" }
                    }.Where(r => r != null),
                    Tags = new[] { "trip", "reassignment", "important" },
                    Metadata = new
                    {
                        TripId = trip.Id,
                        TripNumber = trip.TripNumber,
                        OriginalDriverId = originalDriverId,
                        NewDriverId = request.NewDriverId,
                        Reason = request.Reason,
                        ReassignedBy = request.ReassignedBy
                    }
                }, cancellationToken);
            }

            _logger.LogInformation("Successfully reassigned trip {TripId} to driver {NewDriverId}", 
                request.TripId, request.NewDriverId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reassigning trip {TripId}", request.TripId);
            throw;
        }
    }
}
