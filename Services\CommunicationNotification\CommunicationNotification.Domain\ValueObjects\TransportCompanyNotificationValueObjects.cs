﻿// using CommunicationNotification.Domain.Common;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using Shared.Domain.ValueObjects;

namespace CommunicationNotification.Domain.ValueObjects;

/// <summary>
/// Channel preferences for Transport Company notifications
/// </summary>
public class NotificationChannelPreferences : ValueObject
{
    public bool EmailEnabled { get; private set; }
    public bool SmsEnabled { get; private set; }
    public bool PushEnabled { get; private set; }
    public bool InAppEnabled { get; private set; }
    public bool WhatsAppEnabled { get; private set; }
    public List<NotificationChannel> PriorityChannels { get; private set; } = new();
    public List<NotificationChannel> EmergencyChannels { get; private set; } = new();
    public Dictionary<Priority, List<NotificationChannel>> ChannelsByPriority { get; private set; } = new();

    private NotificationChannelPreferences() { }

    public NotificationChannelPreferences(
        bool emailEnabled = true,
        bool smsEnabled = true,
        bool pushEnabled = true,
        bool inAppEnabled = true,
        bool whatsAppEnabled = false,
        List<NotificationChannel>? priorityChannels = null,
        List<NotificationChannel>? emergencyChannels = null,
        Dictionary<Priority, List<NotificationChannel>>? channelsByPriority = null)
    {
        EmailEnabled = emailEnabled;
        SmsEnabled = smsEnabled;
        PushEnabled = pushEnabled;
        InAppEnabled = inAppEnabled;
        WhatsAppEnabled = whatsAppEnabled;
        PriorityChannels = priorityChannels ?? new List<NotificationChannel> { NotificationChannel.Push, NotificationChannel.Sms };
        EmergencyChannels = emergencyChannels ?? new List<NotificationChannel> { NotificationChannel.Sms, NotificationChannel.Push, NotificationChannel.Voice };
        ChannelsByPriority = channelsByPriority ?? CreateDefaultChannelsByPriority();
    }

    public bool IsChannelEnabled(NotificationChannel channel)
    {
        return channel switch
        {
            NotificationChannel.Email => EmailEnabled,
            NotificationChannel.Sms => SmsEnabled,
            NotificationChannel.Push => PushEnabled,
            NotificationChannel.InApp => InAppEnabled,
            NotificationChannel.WhatsApp => WhatsAppEnabled,
            _ => false
        };
    }

    public List<NotificationChannel> GetDefaultChannels(Priority priority)
    {
        if (ChannelsByPriority.TryGetValue(priority, out var channels))
            return channels.Where(IsChannelEnabled).ToList();

        return priority switch
        {
            Priority.Emergency => EmergencyChannels.Where(IsChannelEnabled).ToList(),
            Priority.High or Priority.Critical => PriorityChannels.Where(IsChannelEnabled).ToList(),
            _ => new List<NotificationChannel> { NotificationChannel.Email, NotificationChannel.InApp }
                .Where(IsChannelEnabled).ToList()
        };
    }

    public static NotificationChannelPreferences CreateDefault()
    {
        return new NotificationChannelPreferences();
    }

    private static Dictionary<Priority, List<NotificationChannel>> CreateDefaultChannelsByPriority()
    {
        return new Dictionary<Priority, List<NotificationChannel>>
        {
            [Priority.Low] = new List<NotificationChannel> { NotificationChannel.Email, NotificationChannel.InApp },
            [Priority.Normal] = new List<NotificationChannel> { NotificationChannel.Email, NotificationChannel.InApp, NotificationChannel.Push },
            [Priority.High] = new List<NotificationChannel> { NotificationChannel.Push, NotificationChannel.Sms, NotificationChannel.Email },
            [Priority.Critical] = new List<NotificationChannel> { NotificationChannel.Sms, NotificationChannel.Push, NotificationChannel.Email },
            [Priority.Emergency] = new List<NotificationChannel> { NotificationChannel.Sms, NotificationChannel.Push, NotificationChannel.Voice }
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return EmailEnabled;
        yield return SmsEnabled;
        yield return PushEnabled;
        yield return InAppEnabled;
        yield return WhatsAppEnabled;
    }
}

/// <summary>
/// Quiet hours configuration for notifications
/// </summary>
public class QuietHoursConfiguration : ValueObject
{
    public bool IsEnabled { get; private set; }
    public TimeSpan StartTime { get; private set; }
    public TimeSpan EndTime { get; private set; }
    public string TimeZone { get; private set; }
    public List<DayOfWeek> ApplicableDays { get; private set; } = new();
    public bool EmergencyOverride { get; private set; }
    public List<MessageType> ExemptMessageTypes { get; private set; } = new();

    private QuietHoursConfiguration() { }

    public QuietHoursConfiguration(
        bool isEnabled = false,
        TimeSpan? startTime = null,
        TimeSpan? endTime = null,
        string timeZone = "Asia/Kolkata",
        List<DayOfWeek>? applicableDays = null,
        bool emergencyOverride = true,
        List<MessageType>? exemptMessageTypes = null)
    {
        IsEnabled = isEnabled;
        StartTime = startTime ?? new TimeSpan(22, 0, 0); // 10 PM
        EndTime = endTime ?? new TimeSpan(6, 0, 0); // 6 AM
        TimeZone = timeZone;
        ApplicableDays = applicableDays ?? Enum.GetValues<DayOfWeek>().ToList();
        EmergencyOverride = emergencyOverride;
        ExemptMessageTypes = exemptMessageTypes ?? new List<MessageType> { MessageType.Emergency };
    }

    public bool IsInQuietHours(DateTime currentTime)
    {
        if (!IsEnabled)
            return false;

        // Convert to configured timezone
        var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(TimeZone);
        var localTime = TimeZoneInfo.ConvertTimeFromUtc(currentTime, timeZoneInfo);

        // Check if current day is applicable
        if (!ApplicableDays.Contains(localTime.DayOfWeek))
            return false;

        var currentTimeOfDay = localTime.TimeOfDay;

        // Handle quiet hours that span midnight
        if (StartTime > EndTime)
        {
            return currentTimeOfDay >= StartTime || currentTimeOfDay <= EndTime;
        }
        else
        {
            return currentTimeOfDay >= StartTime && currentTimeOfDay <= EndTime;
        }
    }

    public bool IsMessageTypeExempt(MessageType messageType)
    {
        return ExemptMessageTypes.Contains(messageType);
    }

    public static QuietHoursConfiguration CreateDefault()
    {
        return new QuietHoursConfiguration();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsEnabled;
        yield return StartTime;
        yield return EndTime;
        yield return TimeZone;
        yield return EmergencyOverride;
    }
}

/// <summary>
/// Frequency settings for notifications
/// </summary>
public class NotificationFrequencySettings : ValueObject
{
    public int MaxNotificationsPerHour { get; private set; }
    public int MaxNotificationsPerDay { get; private set; }
    public Dictionary<MessageType, int> MessageTypeHourlyLimits { get; private set; } = new();
    public Dictionary<MessageType, int> MessageTypeDailyLimits { get; private set; } = new();
    public TimeSpan CooldownPeriod { get; private set; }
    public Dictionary<MessageType, TimeSpan> MessageTypeCooldowns { get; private set; } = new();
    public bool BurstModeEnabled { get; private set; }
    public int BurstModeThreshold { get; private set; }

    private NotificationFrequencySettings() { }

    public NotificationFrequencySettings(
        int maxNotificationsPerHour = 50,
        int maxNotificationsPerDay = 200,
        Dictionary<MessageType, int>? messageTypeHourlyLimits = null,
        Dictionary<MessageType, int>? messageTypeDailyLimits = null,
        TimeSpan? cooldownPeriod = null,
        Dictionary<MessageType, TimeSpan>? messageTypeCooldowns = null,
        bool burstModeEnabled = true,
        int burstModeThreshold = 10)
    {
        MaxNotificationsPerHour = maxNotificationsPerHour;
        MaxNotificationsPerDay = maxNotificationsPerDay;
        MessageTypeHourlyLimits = messageTypeHourlyLimits ?? CreateDefaultHourlyLimits();
        MessageTypeDailyLimits = messageTypeDailyLimits ?? CreateDefaultDailyLimits();
        CooldownPeriod = cooldownPeriod ?? TimeSpan.FromMinutes(5);
        MessageTypeCooldowns = messageTypeCooldowns ?? CreateDefaultCooldowns();
        BurstModeEnabled = burstModeEnabled;
        BurstModeThreshold = burstModeThreshold;
    }

    public bool CanSendNotification(MessageType messageType, DateTime currentTime)
    {
        // Emergency messages always allowed
        if (messageType == MessageType.Emergency)
            return true;

        // Check message type specific limits
        if (MessageTypeHourlyLimits.TryGetValue(messageType, out var hourlyLimit))
        {
            // This would need to be implemented with actual tracking
            // For now, assume it's allowed
        }

        if (MessageTypeDailyLimits.TryGetValue(messageType, out var dailyLimit))
        {
            // This would need to be implemented with actual tracking
            // For now, assume it's allowed
        }

        return true;
    }

    public TimeSpan GetCooldownPeriod(MessageType messageType)
    {
        return MessageTypeCooldowns.TryGetValue(messageType, out var cooldown) ? cooldown : CooldownPeriod;
    }

    public static NotificationFrequencySettings CreateDefault()
    {
        return new NotificationFrequencySettings();
    }

    private static Dictionary<MessageType, int> CreateDefaultHourlyLimits()
    {
        return new Dictionary<MessageType, int>
        {
            [MessageType.TripUpdate] = 20,
            [MessageType.PaymentNotification] = 10,
            [MessageType.SystemNotification] = 15,
            [MessageType.DocumentExpiry] = 5
        };
    }

    private static Dictionary<MessageType, int> CreateDefaultDailyLimits()
    {
        return new Dictionary<MessageType, int>
        {
            [MessageType.TripUpdate] = 100,
            [MessageType.PaymentNotification] = 50,
            [MessageType.SystemNotification] = 75,
            [MessageType.DocumentExpiry] = 20
        };
    }

    private static Dictionary<MessageType, TimeSpan> CreateDefaultCooldowns()
    {
        return new Dictionary<MessageType, TimeSpan>
        {
            [MessageType.TripUpdate] = TimeSpan.FromMinutes(2),
            [MessageType.PaymentNotification] = TimeSpan.FromMinutes(10),
            [MessageType.SystemNotification] = TimeSpan.FromMinutes(5),
            [MessageType.DocumentExpiry] = TimeSpan.FromHours(1)
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MaxNotificationsPerHour;
        yield return MaxNotificationsPerDay;
        yield return CooldownPeriod;
        yield return BurstModeEnabled;
        yield return BurstModeThreshold;
    }
}

/// <summary>
/// Grouping settings for notifications
/// </summary>
public class NotificationGroupingSettings : ValueObject
{
    public bool IsEnabled { get; private set; }
    public TimeSpan GroupingWindow { get; private set; }
    public int MaxGroupSize { get; private set; }
    public List<MessageType> GroupableMessageTypes { get; private set; } = new();
    public Dictionary<MessageType, TimeSpan> MessageTypeGroupingWindows { get; private set; } = new();
    public bool GroupBySender { get; private set; }
    public bool GroupByPriority { get; private set; }
    public bool GroupByRelatedEntity { get; private set; }

    private NotificationGroupingSettings() { }

    public NotificationGroupingSettings(
        bool isEnabled = true,
        TimeSpan? groupingWindow = null,
        int maxGroupSize = 10,
        List<MessageType>? groupableMessageTypes = null,
        Dictionary<MessageType, TimeSpan>? messageTypeGroupingWindows = null,
        bool groupBySender = true,
        bool groupByPriority = true,
        bool groupByRelatedEntity = true)
    {
        IsEnabled = isEnabled;
        GroupingWindow = groupingWindow ?? TimeSpan.FromMinutes(15);
        MaxGroupSize = maxGroupSize;
        GroupableMessageTypes = groupableMessageTypes ?? CreateDefaultGroupableTypes();
        MessageTypeGroupingWindows = messageTypeGroupingWindows ?? CreateDefaultGroupingWindows();
        GroupBySender = groupBySender;
        GroupByPriority = groupByPriority;
        GroupByRelatedEntity = groupByRelatedEntity;
    }

    public TimeSpan GetGroupingWindow(MessageType messageType)
    {
        return MessageTypeGroupingWindows.TryGetValue(messageType, out var window) ? window : GroupingWindow;
    }

    public static NotificationGroupingSettings CreateDefault()
    {
        return new NotificationGroupingSettings();
    }

    private static List<MessageType> CreateDefaultGroupableTypes()
    {
        return new List<MessageType>
        {
            MessageType.TripUpdate,
            MessageType.SystemNotification,
            MessageType.DocumentExpiry,
            MessageType.PaymentNotification
        };
    }

    private static Dictionary<MessageType, TimeSpan> CreateDefaultGroupingWindows()
    {
        return new Dictionary<MessageType, TimeSpan>
        {
            [MessageType.TripUpdate] = TimeSpan.FromMinutes(10),
            [MessageType.SystemNotification] = TimeSpan.FromMinutes(30),
            [MessageType.DocumentExpiry] = TimeSpan.FromHours(2),
            [MessageType.PaymentNotification] = TimeSpan.FromMinutes(20)
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsEnabled;
        yield return GroupingWindow;
        yield return MaxGroupSize;
        yield return GroupBySender;
        yield return GroupByPriority;
        yield return GroupByRelatedEntity;
    }
}

/// <summary>
/// Configuration for notification type
/// </summary>
public class NotificationTypeConfiguration : ValueObject
{
    public MessageType MessageType { get; private set; }
    public bool IsEnabled { get; private set; }
    public List<NotificationChannel> PreferredChannels { get; private set; } = new();
    public List<NotificationRecipientRole> EligibleRecipientRoles { get; private set; } = new();
    public Priority MinimumPriority { get; private set; }
    public string? CustomTemplate { get; private set; }
    public Dictionary<string, object> Settings { get; private set; } = new();

    private NotificationTypeConfiguration() { }

    public NotificationTypeConfiguration(
        MessageType messageType,
        bool isEnabled = true,
        List<NotificationChannel>? preferredChannels = null,
        List<NotificationRecipientRole>? eligibleRecipientRoles = null,
        Priority minimumPriority = Priority.Low,
        string? customTemplate = null,
        Dictionary<string, object>? settings = null)
    {
        MessageType = messageType;
        IsEnabled = isEnabled;
        PreferredChannels = preferredChannels ?? new List<NotificationChannel>();
        EligibleRecipientRoles = eligibleRecipientRoles ?? new List<NotificationRecipientRole>();
        MinimumPriority = minimumPriority;
        CustomTemplate = customTemplate;
        Settings = settings ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return MessageType;
        yield return IsEnabled;
        yield return MinimumPriority;
        yield return CustomTemplate ?? string.Empty;
    }
}

/// <summary>
/// Configuration for notification recipient
/// </summary>
public class NotificationRecipientConfiguration : ValueObject
{
    public Guid UserId { get; private set; }
    public NotificationRecipientRole Role { get; private set; }
    public string Name { get; private set; }
    public string Email { get; private set; }
    public string? Phone { get; private set; }
    public bool IsEnabled { get; private set; }
    public List<MessageType> SubscribedMessageTypes { get; private set; } = new();
    public List<NotificationChannel> PreferredChannels { get; private set; } = new();
    public Dictionary<string, object> Settings { get; private set; } = new();

    private NotificationRecipientConfiguration() { }

    public NotificationRecipientConfiguration(
        Guid userId,
        NotificationRecipientRole role,
        string name,
        string email,
        string? phone = null,
        bool isEnabled = true,
        List<MessageType>? subscribedMessageTypes = null,
        List<NotificationChannel>? preferredChannels = null,
        Dictionary<string, object>? settings = null)
    {
        UserId = userId;
        Role = role;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Email = email ?? throw new ArgumentNullException(nameof(email));
        Phone = phone;
        IsEnabled = isEnabled;
        SubscribedMessageTypes = subscribedMessageTypes ?? new List<MessageType>();
        PreferredChannels = preferredChannels ?? new List<NotificationChannel>();
        Settings = settings ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return UserId;
        yield return Role;
        yield return Email;
        yield return IsEnabled;
    }
}

/// <summary>
/// Escalation configuration for notifications
/// </summary>
public class EscalationConfiguration : ValueObject
{
    public bool IsEnabled { get; private set; }
    public List<EscalationLevel> EscalationLevels { get; private set; } = new();
    public TimeSpan DefaultEscalationInterval { get; private set; }
    public int MaxEscalationLevel { get; private set; }

    private EscalationConfiguration() { }

    public EscalationConfiguration(
        bool isEnabled = false,
        List<EscalationLevel>? escalationLevels = null,
        TimeSpan? defaultEscalationInterval = null,
        int maxEscalationLevel = 3)
    {
        IsEnabled = isEnabled;
        EscalationLevels = escalationLevels ?? CreateDefaultEscalationLevels();
        DefaultEscalationInterval = defaultEscalationInterval ?? TimeSpan.FromHours(2);
        MaxEscalationLevel = maxEscalationLevel;
    }

    public static EscalationConfiguration CreateDefault()
    {
        return new EscalationConfiguration();
    }

    private static List<EscalationLevel> CreateDefaultEscalationLevels()
    {
        return new List<EscalationLevel>
        {
            new EscalationLevel(1, TimeSpan.FromHours(1), new List<NotificationRecipientRole> { NotificationRecipientRole.Manager }),
            new EscalationLevel(2, TimeSpan.FromHours(2), new List<NotificationRecipientRole> { NotificationRecipientRole.Manager, NotificationRecipientRole.Admin }),
            new EscalationLevel(3, TimeSpan.FromHours(4), new List<NotificationRecipientRole> { NotificationRecipientRole.Admin, NotificationRecipientRole.Owner })
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsEnabled;
        yield return DefaultEscalationInterval;
        yield return MaxEscalationLevel;
    }
}

/// <summary>
/// Escalation level configuration
/// </summary>
public class EscalationLevel : ValueObject
{
    public int Level { get; private set; }
    public TimeSpan Interval { get; private set; }
    public List<NotificationRecipientRole> RecipientRoles { get; private set; } = new();

    private EscalationLevel() { }

    public EscalationLevel(int level, TimeSpan interval, List<NotificationRecipientRole> recipientRoles)
    {
        Level = level;
        Interval = interval;
        RecipientRoles = recipientRoles ?? new List<NotificationRecipientRole>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Level;
        yield return Interval;
    }
}

/// <summary>
/// Digest configuration for notifications
/// </summary>
public class DigestConfiguration : ValueObject
{
    public bool IsEnabled { get; private set; }
    public List<DigestSchedule> DigestSchedules { get; private set; } = new();
    public List<MessageType> DigestableMessageTypes { get; private set; } = new();
    public int MaxItemsPerDigest { get; private set; }
    public string DigestTemplate { get; private set; }

    private DigestConfiguration() { }

    public DigestConfiguration(
        bool isEnabled = false,
        List<DigestSchedule>? digestSchedules = null,
        List<MessageType>? digestableMessageTypes = null,
        int maxItemsPerDigest = 50,
        string digestTemplate = "default")
    {
        IsEnabled = isEnabled;
        DigestSchedules = digestSchedules ?? CreateDefaultDigestSchedules();
        DigestableMessageTypes = digestableMessageTypes ?? CreateDefaultDigestableTypes();
        MaxItemsPerDigest = maxItemsPerDigest;
        DigestTemplate = digestTemplate;
    }

    public static DigestConfiguration CreateDefault()
    {
        return new DigestConfiguration();
    }

    private static List<DigestSchedule> CreateDefaultDigestSchedules()
    {
        return new List<DigestSchedule>
        {
            new DigestSchedule("Daily", new TimeSpan(9, 0, 0), DigestFrequency.Daily),
            new DigestSchedule("Weekly", new TimeSpan(9, 0, 0), DigestFrequency.Weekly)
        };
    }

    private static List<MessageType> CreateDefaultDigestableTypes()
    {
        return new List<MessageType>
        {
            MessageType.SystemNotification,
            MessageType.DocumentExpiry,
            MessageType.PaymentNotification
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IsEnabled;
        yield return MaxItemsPerDigest;
        yield return DigestTemplate;
    }
}

/// <summary>
/// Digest schedule configuration
/// </summary>
public class DigestSchedule : ValueObject
{
    public string Name { get; private set; }
    public TimeSpan DeliveryTime { get; private set; }
    public DigestFrequency Frequency { get; private set; }
    public List<DayOfWeek> DeliveryDays { get; private set; } = new();

    private DigestSchedule() { }

    public DigestSchedule(string name, TimeSpan deliveryTime, DigestFrequency frequency, List<DayOfWeek>? deliveryDays = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DeliveryTime = deliveryTime;
        Frequency = frequency;
        DeliveryDays = deliveryDays ?? GetDefaultDeliveryDays(frequency);
    }

    private static List<DayOfWeek> GetDefaultDeliveryDays(DigestFrequency frequency)
    {
        return frequency switch
        {
            DigestFrequency.Daily => Enum.GetValues<DayOfWeek>().ToList(),
            DigestFrequency.Weekly => new List<DayOfWeek> { DayOfWeek.Monday },
            DigestFrequency.Monthly => new List<DayOfWeek> { DayOfWeek.Monday },
            _ => new List<DayOfWeek>()
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Name;
        yield return DeliveryTime;
        yield return Frequency;
    }
}

/// <summary>
/// Enums for notification configuration
/// </summary>
public enum NotificationRecipientRole
{
    Owner = 0,
    Admin = 1,
    Manager = 2,
    Operator = 3,
    Driver = 4,
    Customer = 5
}

public enum DigestFrequency
{
    Daily = 0,
    Weekly = 1,
    Monthly = 2
}


