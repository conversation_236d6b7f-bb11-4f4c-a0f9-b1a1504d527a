using System;
using System.Collections.Generic;
using Identity.Domain.Common;
using Identity.Domain.Common.Events;
using Identity.Domain.Exceptions;
using Identity.Domain.ValueObjects;

namespace Identity.Domain.Entities
{
    public enum UserStatus
    {
        Pending,
        Active,
        Suspended,
        Locked
    }

    public enum UserType
    {
        Admin,
        SubAdmin,
        TransportCompany,
        <PERSON><PERSON><PERSON>,
        Carrier,
        Driver,
        Shipper
    }

    public enum KycStatus
    {
        NotStarted,
        InProgress,
        UnderReview,
        Approved,
        Rejected,
        RequiresResubmission
    }

    public enum OtpPurpose
    {
        Login,
        Registration,
        PasswordReset,
        PhoneVerification,
        EmailVerification,
        TwoFactorAuth
    }

    public class UserCreatedEvent : DomainEvent
    {
        public Guid UserId { get; }
        public string Email { get; }

        public UserCreatedEvent(Guid userId, string email)
        {
            UserId = userId;
            Email = email;
        }
    }

    public class User : AggregateRoot
    {
        public string Username { get; private set; }
        public Email Email { get; private set; }
        public string PasswordHash { get; private set; }
        public string SecurityStamp { get; private set; }
        public bool TwoFactorEnabled { get; private set; }
        public bool EmailConfirmed { get; private set; }
        public bool PhoneNumberConfirmed { get; private set; }
        public string PhoneNumber { get; private set; }
        public int AccessFailedCount { get; private set; }
        public bool LockoutEnabled { get; private set; }
        public DateTime? LockoutEnd { get; private set; }
        public UserStatus Status { get; private set; }
        public UserType UserType { get; private set; }
        public KycStatus KycStatus { get; private set; }
        public string FirstName { get; private set; }
        public string LastName { get; private set; }
        public string CompanyName { get; private set; }
        public string GstNumber { get; private set; }
        public string PanNumber { get; private set; }
        public string AadharNumber { get; private set; }
        public string LicenseNumber { get; private set; }
        public DateTime? KycSubmittedAt { get; private set; }
        public DateTime? KycApprovedAt { get; private set; }
        public string KycRejectionReason { get; private set; }

        private readonly List<RefreshToken> _refreshTokens = new();
        public IReadOnlyCollection<RefreshToken> RefreshTokens => _refreshTokens.AsReadOnly();

        private readonly List<UserRole> _roles = new();
        public IReadOnlyCollection<UserRole> Roles => _roles.AsReadOnly();

        private readonly List<OtpToken> _otpTokens = new();
        public IReadOnlyCollection<OtpToken> OtpTokens => _otpTokens.AsReadOnly();

        private User() { }

        public User(string username, Email email, string passwordHash, UserType userType = UserType.Shipper)
        {
            if (string.IsNullOrWhiteSpace(username))
                throw new DomainException("Username cannot be empty");

            if (string.IsNullOrWhiteSpace(passwordHash))
                throw new DomainException("Password hash cannot be empty");

            if (email == null)
                throw new DomainException("Email cannot be null");

            Username = username;
            Email = email;
            PasswordHash = passwordHash;
            SecurityStamp = Guid.NewGuid().ToString();
            TwoFactorEnabled = false;
            EmailConfirmed = false;
            PhoneNumberConfirmed = false;
            AccessFailedCount = 0;
            LockoutEnabled = true;
            Status = UserStatus.Pending;
            UserType = userType;
            KycStatus = KycStatus.NotStarted;
            CreatedAt = DateTime.UtcNow;

            AddDomainEvent(new UserCreatedEvent(Id, email.Value));
        }

        public void SetEmail(Email email)
        {
            if (email == null)
                throw new DomainException("Email cannot be null");

            Email = email;
            EmailConfirmed = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ConfirmEmail()
        {
            EmailConfirmed = true;
            if (Status == UserStatus.Pending)
            {
                Status = UserStatus.Active;
            }
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetPhoneNumber(string phoneNumber)
        {
            PhoneNumber = phoneNumber;
            PhoneNumberConfirmed = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ConfirmPhoneNumber()
        {
            PhoneNumberConfirmed = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void EnableTwoFactor()
        {
            TwoFactorEnabled = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void DisableTwoFactor()
        {
            TwoFactorEnabled = false;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetPasswordHash(string passwordHash)
        {
            if (string.IsNullOrWhiteSpace(passwordHash))
                throw new DomainException("Password hash cannot be empty");

            PasswordHash = passwordHash;
            SecurityStamp = Guid.NewGuid().ToString();
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddRefreshToken(RefreshToken refreshToken)
        {
            if (refreshToken == null)
                throw new DomainException("Refresh token cannot be null");

            _refreshTokens.Add(refreshToken);
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddRole(Role role)
        {
            if (role == null)
                throw new DomainException("Role cannot be null");

            if (_roles.Exists(r => r.RoleId == role.Id))
                return;

            _roles.Add(new UserRole(Id, role.Id));
            UpdatedAt = DateTime.UtcNow;
        }

        public void RemoveRole(Role role)
        {
            if (role == null)
                throw new DomainException("Role cannot be null");

            var userRole = _roles.Find(r => r.RoleId == role.Id);
            if (userRole != null)
            {
                _roles.Remove(userRole);
                UpdatedAt = DateTime.UtcNow;
            }
        }

        public void IncrementAccessFailedCount()
        {
            AccessFailedCount++;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ResetAccessFailedCount()
        {
            AccessFailedCount = 0;
            UpdatedAt = DateTime.UtcNow;
        }

        public void LockoutUser(TimeSpan duration)
        {
            LockoutEnd = DateTime.UtcNow.Add(duration);
            Status = UserStatus.Locked;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UnlockUser()
        {
            LockoutEnd = null;
            Status = UserStatus.Active;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetPersonalDetails(string firstName, string lastName)
        {
            FirstName = firstName;
            LastName = lastName;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetCompanyDetails(string companyName, string gstNumber, string panNumber)
        {
            if (UserType != UserType.TransportCompany && UserType != UserType.Broker)
                throw new DomainException("Company details can only be set for transport companies and brokers");

            CompanyName = companyName;
            GstNumber = gstNumber;
            PanNumber = panNumber;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetAadharNumber(string aadharNumber)
        {
            AadharNumber = aadharNumber;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SetLicenseNumber(string licenseNumber)
        {
            if (UserType != UserType.Driver)
                throw new DomainException("License number can only be set for drivers");

            LicenseNumber = licenseNumber;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SubmitKyc()
        {
            if (KycStatus != KycStatus.NotStarted && KycStatus != KycStatus.RequiresResubmission)
                throw new DomainException("KYC can only be submitted when not started or requires resubmission");

            KycStatus = KycStatus.InProgress;
            KycSubmittedAt = DateTime.UtcNow;
            KycRejectionReason = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void StartKycReview()
        {
            if (KycStatus != KycStatus.InProgress)
                throw new DomainException("KYC must be in progress to start review");

            KycStatus = KycStatus.UnderReview;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ApproveKyc()
        {
            if (KycStatus != KycStatus.UnderReview)
                throw new DomainException("KYC must be under review to approve");

            KycStatus = KycStatus.Approved;
            KycApprovedAt = DateTime.UtcNow;
            KycRejectionReason = null;

            if (Status == UserStatus.Pending)
            {
                Status = UserStatus.Active;
            }

            UpdatedAt = DateTime.UtcNow;
        }

        public void RejectKyc(string reason)
        {
            if (KycStatus != KycStatus.UnderReview)
                throw new DomainException("KYC must be under review to reject");

            if (string.IsNullOrWhiteSpace(reason))
                throw new DomainException("Rejection reason is required");

            KycStatus = KycStatus.Rejected;
            KycRejectionReason = reason;
            UpdatedAt = DateTime.UtcNow;
        }

        public void RequestKycResubmission(string reason)
        {
            if (KycStatus != KycStatus.UnderReview && KycStatus != KycStatus.Rejected)
                throw new DomainException("KYC must be under review or rejected to request resubmission");

            if (string.IsNullOrWhiteSpace(reason))
                throw new DomainException("Resubmission reason is required");

            KycStatus = KycStatus.RequiresResubmission;
            KycRejectionReason = reason;
            UpdatedAt = DateTime.UtcNow;
        }

        public void SuspendUser()
        {
            Status = UserStatus.Suspended;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ActivateUser()
        {
            Status = UserStatus.Active;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
