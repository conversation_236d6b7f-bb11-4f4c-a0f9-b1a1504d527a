using Shared.Domain.Common;
using Shared.Domain.ValueObjects;
using AuditCompliance.Domain.Enums;
using Shared.Domain.ValueObjects;
using AuditCompliance.Domain.Events;
using Shared.Domain.ValueObjects;
using AuditCompliance.Domain.ValueObjects;
using Shared.Domain.ValueObjects;

namespace AuditCompliance.Domain.Entities
{
    /// <summary>
    /// Retention policy management entity for comprehensive policy control
    /// </summary>
    public class RetentionPolicyManagement : AggregateRoot
    {
        public string PolicyName { get; private set; }
        public string Description { get; private set; }
        public RetentionPolicy RetentionPolicy { get; private set; }
        public List<ComplianceStandard> ApplicableStandards { get; private set; }
        public List<string> ApplicableEntityTypes { get; private set; }
        public List<string> ApplicableModules { get; private set; }
        public PolicyPriority Priority { get; private set; }
        public bool IsActive { get; private set; }
        public bool RequiresApproval { get; private set; }
        public PolicyApprovalStatus ApprovalStatus { get; private set; }
        public Guid CreatedBy { get; private set; }
        public string CreatedByName { get; private set; }
        public DateTime? ApprovedAt { get; private set; }
        public Guid? ApprovedBy { get; private set; }
        public string? ApprovedByName { get; private set; }
        public DateTime? LastExecuted { get; private set; }
        public int ExecutionCount { get; private set; }
        public int RecordsAffected { get; private set; }
        public List<PolicyExecution> ExecutionHistory { get; private set; }
        public PolicyImpactAnalysis? ImpactAnalysis { get; private set; }
        public Dictionary<string, object> Configuration { get; private set; }
        public List<string> Tags { get; private set; }

        private RetentionPolicyManagement()
        {
            PolicyName = string.Empty;
            Description = string.Empty;
            CreatedByName = string.Empty;
            ApplicableStandards = new List<ComplianceStandard>();
            ApplicableEntityTypes = new List<string>();
            ApplicableModules = new List<string>();
            ExecutionHistory = new List<PolicyExecution>();
            Configuration = new Dictionary<string, object>();
            Tags = new List<string>();
            RetentionPolicy = RetentionPolicy.CreateDefault();
        }

        public RetentionPolicyManagement(
            string policyName,
            string description,
            RetentionPolicy retentionPolicy,
            List<ComplianceStandard> applicableStandards,
            List<string> applicableEntityTypes,
            List<string> applicableModules,
            PolicyPriority priority,
            bool requiresApproval,
            Guid createdBy,
            string createdByName,
            List<string> tags,
            Dictionary<string, object>? configuration = null)
        {
            if (string.IsNullOrWhiteSpace(policyName))
                throw new ArgumentException("Policy name cannot be empty", nameof(policyName));

            PolicyName = policyName;
            Description = description ?? string.Empty;
            RetentionPolicy = retentionPolicy ?? throw new ArgumentNullException(nameof(retentionPolicy));
            ApplicableStandards = applicableStandards ?? new List<ComplianceStandard>();
            ApplicableEntityTypes = applicableEntityTypes ?? new List<string>();
            ApplicableModules = applicableModules ?? new List<string>();
            Priority = priority;
            IsActive = false; // Start inactive until approved if required
            RequiresApproval = requiresApproval;
            ApprovalStatus = requiresApproval ? PolicyApprovalStatus.Pending : PolicyApprovalStatus.Approved;
            CreatedBy = createdBy;
            CreatedByName = createdByName;
            ExecutionCount = 0;
            RecordsAffected = 0;
            ExecutionHistory = new List<PolicyExecution>();
            Configuration = configuration ?? new Dictionary<string, object>();
            Tags = tags ?? new List<string>();

            // Auto-activate if no approval required
            if (!requiresApproval)
            {
                IsActive = true;
                ApprovedAt = DateTime.UtcNow;
                ApprovedBy = createdBy;
                ApprovedByName = createdByName;
            }

            // Add domain event
            AddDomainEvent(new RetentionPolicyCreatedEvent(
                Id,
                PolicyName,
                string.Join(",", ApplicableEntityTypes),
                (int)RetentionPolicy.RetentionPeriod.TotalDays,
                CreatedBy));
        }

        public void UpdatePolicy(
            string policyName,
            string description,
            RetentionPolicy retentionPolicy,
            List<ComplianceStandard> applicableStandards,
            List<string> applicableEntityTypes,
            List<string> applicableModules,
            PolicyPriority priority,
            List<string> tags,
            Dictionary<string, object>? configuration = null)
        {
            if (string.IsNullOrWhiteSpace(policyName))
                throw new ArgumentException("Policy name cannot be empty", nameof(policyName));

            var oldPolicy = RetentionPolicy;

            PolicyName = policyName;
            Description = description ?? string.Empty;
            RetentionPolicy = retentionPolicy ?? throw new ArgumentNullException(nameof(retentionPolicy));
            ApplicableStandards = applicableStandards ?? new List<ComplianceStandard>();
            ApplicableEntityTypes = applicableEntityTypes ?? new List<string>();
            ApplicableModules = applicableModules ?? new List<string>();
            Priority = priority;
            Tags = tags ?? new List<string>();
            Configuration = configuration ?? new Dictionary<string, object>();

            // If policy requires approval and significant changes were made, reset approval
            if (RequiresApproval && HasSignificantChanges(oldPolicy, retentionPolicy))
            {
                ApprovalStatus = PolicyApprovalStatus.Pending;
                IsActive = false;
                ApprovedAt = null;
                ApprovedBy = null;
                ApprovedByName = null;
            }

            // Add domain event
            AddDomainEvent(new RetentionPolicyUpdatedEvent(
                Id,
                PolicyName,
                string.Join(",", ApplicableEntityTypes),
                (int)RetentionPolicy.RetentionPeriod.TotalDays,
                CreatedBy)); // TODO: Should be updatedBy parameter
        }

        public void Approve(Guid approvedBy, string approvedByName, string? approvalNotes = null)
        {
            if (ApprovalStatus == PolicyApprovalStatus.Approved)
                throw new InvalidOperationException("Policy is already approved");

            ApprovalStatus = PolicyApprovalStatus.Approved;
            ApprovedAt = DateTime.UtcNow;
            ApprovedBy = approvedBy;
            ApprovedByName = approvedByName;
            IsActive = true;

            if (!string.IsNullOrEmpty(approvalNotes))
            {
                Configuration["ApprovalNotes"] = approvalNotes;
            }

            // Add domain event
            AddDomainEvent(new RetentionPolicyApprovedEvent(Id, approvedBy, approvalNotes));
        }

        public void Reject(Guid rejectedBy, string rejectedByName, string rejectionReason)
        {
            if (string.IsNullOrWhiteSpace(rejectionReason))
                throw new ArgumentException("Rejection reason is required", nameof(rejectionReason));

            ApprovalStatus = PolicyApprovalStatus.Rejected;
            IsActive = false;
            Configuration["RejectionReason"] = rejectionReason;
            Configuration["RejectedBy"] = rejectedByName;
            Configuration["RejectedAt"] = DateTime.UtcNow;

            // Add domain event
            AddDomainEvent(new RetentionPolicyRejectedEvent(Id, rejectedBy, rejectionReason));
        }

        public void Activate()
        {
            if (ApprovalStatus != PolicyApprovalStatus.Approved)
                throw new InvalidOperationException("Policy must be approved before activation");

            if (!IsActive)
            {
                IsActive = true;
                AddDomainEvent(new RetentionPolicyActivatedEvent(Id, CreatedBy, DateTime.UtcNow)); // TODO: Should be activatedBy parameter
            }
        }

        public void Deactivate(string? reason = null)
        {
            if (IsActive)
            {
                IsActive = false;
                if (!string.IsNullOrEmpty(reason))
                {
                    Configuration["DeactivationReason"] = reason;
                    Configuration["DeactivatedAt"] = DateTime.UtcNow;
                }
                AddDomainEvent(new RetentionPolicyDeactivatedEvent(Id, CreatedBy, reason)); // TODO: Should be deactivatedBy parameter
            }
        }

        public void RecordExecution(int recordsProcessed, int recordsDeleted, TimeSpan executionTime, bool isSuccessful, string? errorMessage = null)
        {
            var execution = new PolicyExecution(
                DateTime.UtcNow,
                recordsProcessed,
                recordsDeleted,
                executionTime,
                isSuccessful,
                errorMessage);

            ExecutionHistory.Add(execution);
            LastExecuted = DateTime.UtcNow;
            ExecutionCount++;
            RecordsAffected += recordsDeleted;

            // Keep only last 100 executions
            if (ExecutionHistory.Count > 100)
            {
                ExecutionHistory.RemoveAt(0);
            }

            // Add domain event
            AddDomainEvent(new RetentionPolicyExecutedEvent(
                Id,
                recordsProcessed,
                recordsDeleted,
                execution.ExecutedAt,
                execution.ExecutedAt.Add(executionTime)));
        }

        public void SetImpactAnalysis(PolicyImpactAnalysis impactAnalysis)
        {
            ImpactAnalysis = impactAnalysis;
            AddDomainEvent(new RetentionPolicyImpactAnalyzedEvent(
                Id,
                impactAnalysis.EstimatedRecordsAffected,
                impactAnalysis.EstimatedRecordsAffected * 1024L, // Estimate 1KB per record
                ApplicableModules, // Use applicable modules as affected systems
                CreatedBy)); // TODO: Should be analyzedBy parameter
        }

        public bool IsApplicableToEntity(string entityType, string? moduleName = null)
        {
            var entityApplicable = ApplicableEntityTypes.Contains(entityType, StringComparer.OrdinalIgnoreCase);
            var moduleApplicable = string.IsNullOrEmpty(moduleName) ||
                                  ApplicableModules.Contains(moduleName, StringComparer.OrdinalIgnoreCase);

            return entityApplicable && moduleApplicable;
        }

        public bool IsApplicableToStandard(ComplianceStandard standard)
        {
            return ApplicableStandards.Contains(standard);
        }

        public PolicyExecutionSummary GetExecutionSummary()
        {
            return new PolicyExecutionSummary(
                ExecutionCount,
                RecordsAffected,
                LastExecuted,
                ExecutionHistory.Count(e => e.IsSuccessful),
                ExecutionHistory.Count(e => !e.IsSuccessful),
                ExecutionHistory.Any() ? ExecutionHistory.Average(e => e.ExecutionTime.TotalSeconds) : 0);
        }

        private bool HasSignificantChanges(RetentionPolicy oldPolicy, RetentionPolicy newPolicy)
        {
            return oldPolicy.RetentionPeriod != newPolicy.RetentionPeriod ||
                   oldPolicy.AutoDelete != newPolicy.AutoDelete ||
                   !oldPolicy.ApplicableStandards.SequenceEqual(newPolicy.ApplicableStandards);
        }
    }

    /// <summary>
    /// Policy priority enumeration
    /// </summary>
    public enum PolicyPriority
    {
        Low = 0,
        Medium = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// Policy approval status enumeration
    /// </summary>
    public enum PolicyApprovalStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2,
        UnderReview = 3
    }

    /// <summary>
    /// Policy execution value object
    /// </summary>
    public class PolicyExecution : ValueObject
    {
        public DateTime ExecutedAt { get; private set; }
        public int RecordsProcessed { get; private set; }
        public int RecordsDeleted { get; private set; }
        public TimeSpan ExecutionTime { get; private set; }
        public bool IsSuccessful { get; private set; }
        public string? ErrorMessage { get; private set; }

        private PolicyExecution() { }

        public PolicyExecution(DateTime executedAt, int recordsProcessed, int recordsDeleted, TimeSpan executionTime, bool isSuccessful, string? errorMessage = null)
        {
            ExecutedAt = executedAt;
            RecordsProcessed = recordsProcessed;
            RecordsDeleted = recordsDeleted;
            ExecutionTime = executionTime;
            IsSuccessful = isSuccessful;
            ErrorMessage = errorMessage;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return ExecutedAt;
            yield return RecordsProcessed;
            yield return RecordsDeleted;
            yield return ExecutionTime;
            yield return IsSuccessful;
            yield return ErrorMessage ?? string.Empty;
        }
    }

    /// <summary>
    /// Policy impact analysis value object
    /// </summary>
    public class PolicyImpactAnalysis : ValueObject
    {
        public int EstimatedRecordsAffected { get; private set; }
        public Dictionary<string, int> RecordsByEntityType { get; private set; }
        public Dictionary<string, int> RecordsByModule { get; private set; }
        public TimeSpan EstimatedExecutionTime { get; private set; }
        public DateTime AnalyzedAt { get; private set; }
        public List<string> Warnings { get; private set; }
        public List<string> Recommendations { get; private set; }

        private PolicyImpactAnalysis()
        {
            RecordsByEntityType = new Dictionary<string, int>();
            RecordsByModule = new Dictionary<string, int>();
            Warnings = new List<string>();
            Recommendations = new List<string>();
        }

        public PolicyImpactAnalysis(
            int estimatedRecordsAffected,
            Dictionary<string, int> recordsByEntityType,
            Dictionary<string, int> recordsByModule,
            TimeSpan estimatedExecutionTime,
            List<string> warnings,
            List<string> recommendations)
        {
            EstimatedRecordsAffected = estimatedRecordsAffected;
            RecordsByEntityType = recordsByEntityType ?? new Dictionary<string, int>();
            RecordsByModule = recordsByModule ?? new Dictionary<string, int>();
            EstimatedExecutionTime = estimatedExecutionTime;
            AnalyzedAt = DateTime.UtcNow;
            Warnings = warnings ?? new List<string>();
            Recommendations = recommendations ?? new List<string>();
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return EstimatedRecordsAffected;
            yield return EstimatedExecutionTime;
            yield return AnalyzedAt;
        }
    }

    /// <summary>
    /// Policy execution summary value object
    /// </summary>
    public class PolicyExecutionSummary : ValueObject
    {
        public int TotalExecutions { get; private set; }
        public int TotalRecordsAffected { get; private set; }
        public DateTime? LastExecuted { get; private set; }
        public int SuccessfulExecutions { get; private set; }
        public int FailedExecutions { get; private set; }
        public double AverageExecutionTimeSeconds { get; private set; }

        private PolicyExecutionSummary() { }

        public PolicyExecutionSummary(int totalExecutions, int totalRecordsAffected, DateTime? lastExecuted, int successfulExecutions, int failedExecutions, double averageExecutionTimeSeconds)
        {
            TotalExecutions = totalExecutions;
            TotalRecordsAffected = totalRecordsAffected;
            LastExecuted = lastExecuted;
            SuccessfulExecutions = successfulExecutions;
            FailedExecutions = failedExecutions;
            AverageExecutionTimeSeconds = averageExecutionTimeSeconds;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return TotalExecutions;
            yield return TotalRecordsAffected;
            yield return LastExecuted ?? DateTime.MinValue;
            yield return SuccessfulExecutions;
            yield return FailedExecutions;
        }
    }
}



