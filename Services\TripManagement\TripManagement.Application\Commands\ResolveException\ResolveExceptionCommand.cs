using MediatR;

namespace TripManagement.Application.Commands.ResolveException;

public record ResolveExceptionCommand : IRequest<bool>
{
    public Guid TripId { get; init; }
    public Guid ExceptionId { get; init; }
    public string Resolution { get; init; } = string.Empty;
    public string ResolvedBy { get; init; } = string.Empty;
    public DateTime? ResolvedAt { get; init; }
    public string? Notes { get; init; }
}
