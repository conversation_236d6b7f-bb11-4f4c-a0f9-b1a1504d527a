using Shared.Domain.Common;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Payment Dispute Events
public record PaymentDisputeCreatedEvent(Guid DisputeId, Guid OrderId, Guid InitiatedBy, Money DisputedAmount, DisputeCategory Category) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentDisputeEscalatedEvent(Guid DisputeId, Guid OrderId, DisputePriority NewPriority, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentDisputeInvestigationStartedEvent(Guid DisputeId, Guid OrderId, Guid InvestigatorId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentDisputeResolvedEvent(Guid DisputeId, Guid OrderId, Money ResolvedAmount, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentDisputeClosedEvent(Guid DisputeId, Guid OrderId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentDisputeReopenedEvent(Guid DisputeId, Guid OrderId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Dispute Comment Events
public record DisputeCommentAddedEvent(Guid CommentId, Guid DisputeId, Guid AuthorId, string Content) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Dispute Document Events
public record DisputeDocumentAddedEvent(Guid DocumentId, Guid DisputeId, string FileName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
