using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface ITaxCalculationService
    {
        /// <summary>
        /// Calculate tax amount for a subscription plan based on customer location
        /// </summary>
        Task<Money> CalculateTaxForPlanAsync(Plan plan, string customerRegion, Guid? customerId = null, DateTime? date = null);

        /// <summary>
        /// Calculate tax amount for a specific amount based on region and tax type
        /// </summary>
        Task<Money> CalculateTaxAmountAsync(Money baseAmount, string customerRegion, TaxType? taxType = null, DateTime? date = null);

        /// <summary>
        /// Get effective tax rate for a region and tax type
        /// </summary>
        Task<decimal> GetEffectiveTaxRateAsync(string customerRegion, TaxType taxType, DateTime? date = null);

        /// <summary>
        /// Check if customer is exempt from specific tax types
        /// </summary>
        Task<bool> IsCustomerExemptAsync(Guid customerId, TaxType taxType, string region, DateTime? date = null);

        /// <summary>
        /// Get all applicable tax configurations for a region
        /// </summary>
        Task<List<TaxConfiguration>> GetApplicableTaxConfigurationsAsync(string customerRegion, DateTime? date = null);

        /// <summary>
        /// Calculate compound tax (tax on tax) for multiple tax types
        /// </summary>
        Task<Money> CalculateCompoundTaxAsync(Money baseAmount, List<TaxType> taxTypes, string customerRegion, DateTime? date = null);

        /// <summary>
        /// Determine if pricing should be tax-inclusive for a region
        /// </summary>
        Task<bool> IsTaxInclusivePricingAsync(string customerRegion, DateTime? date = null);

        /// <summary>
        /// Calculate base amount from tax-inclusive price
        /// </summary>
        Task<Money> CalculateBaseAmountFromTaxInclusivePriceAsync(Money taxInclusiveAmount, string customerRegion, DateTime? date = null);

        /// <summary>
        /// Get tax breakdown for detailed reporting
        /// </summary>
        Task<TaxBreakdown> GetTaxBreakdownAsync(Money baseAmount, string customerRegion, Guid? customerId = null, DateTime? date = null);

        /// <summary>
        /// Validate tax configuration for a region
        /// </summary>
        Task<bool> ValidateTaxConfigurationAsync(TaxConfiguration taxConfiguration, string region);

        /// <summary>
        /// Get tax display information for UI
        /// </summary>
        Task<TaxDisplayInfo> GetTaxDisplayInfoAsync(Plan plan, string customerRegion, Guid? customerId = null, DateTime? date = null);
    }

    public class TaxBreakdown
    {
        public Money BaseAmount { get; set; } = null!;
        public List<TaxBreakdownItem> TaxItems { get; set; } = new();
        public Money TotalTaxAmount { get; set; } = null!;
        public Money TotalAmount { get; set; } = null!;
        public bool IsTaxInclusive { get; set; }
        public string CustomerRegion { get; set; } = string.Empty;
        public DateTime CalculatedAt { get; set; }
    }

    public class TaxBreakdownItem
    {
        public TaxType TaxType { get; set; }
        public string TaxName { get; set; } = string.Empty;
        public decimal Rate { get; set; }
        public Money TaxableAmount { get; set; } = null!;
        public Money TaxAmount { get; set; } = null!;
        public bool IsExempt { get; set; }
        public string? ExemptionReason { get; set; }
    }

    public class TaxDisplayInfo
    {
        public Money DisplayPrice { get; set; } = null!;
        public Money BasePrice { get; set; } = null!;
        public Money TotalTaxAmount { get; set; } = null!;
        public bool IsTaxInclusive { get; set; }
        public string TaxDisplayText { get; set; } = string.Empty;
        public List<string> TaxLabels { get; set; } = new();
        public bool HasExemptions { get; set; }
    }
}
