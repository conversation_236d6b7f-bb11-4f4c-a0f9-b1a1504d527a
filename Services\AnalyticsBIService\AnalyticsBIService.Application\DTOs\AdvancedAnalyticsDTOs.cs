using System.ComponentModel.DataAnnotations;
using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Dashboard alert data transfer object
/// </summary>
public class DashboardAlertDto
{
    public string AlertId { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // "Info", "Warning", "Error", "Critical"
    public DateTime CreatedAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public string? AcknowledgedBy { get; set; }
    public bool IsActive { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? RelatedEntityId { get; set; }
    public string? RelatedEntityType { get; set; }
}

// UserType enum is already defined in Domain.Enums - using that instead

/// <summary>
/// Advanced dashboard request data transfer object
/// </summary>
public class AdvancedDashboardRequest
{
    [Required]
    public string UserId { get; set; } = string.Empty;

    [Required]
    public AnalyticsBIService.Domain.Enums.UserType UserType { get; set; }

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateRangeDto DateRange { get; set; } = new();
    public List<string> MetricTypes { get; set; } = new();
    public List<FilterCriteriaDto> Filters { get; set; } = new();
    public Dictionary<string, object> Preferences { get; set; } = new();
    public bool IncludeRealTime { get; set; } = true;
    public bool IncludeRealTimeData { get; set; } = true;
    public bool IncludePredictive { get; set; } = false;
    public bool IncludePredictiveAnalytics { get; set; } = false;
    public int PredictionHorizonDays { get; set; } = 30;
    public List<string> PredictionTypes { get; set; } = new();
    public Dictionary<string, object> CustomFilters { get; set; } = new();
    public int RefreshInterval { get; set; } = 30; // seconds
}

/// <summary>
/// Advanced dashboard data transfer object
/// </summary>
public class AdvancedDashboardDto
{
    public string DashboardId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public UserType UserType { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public DateRangeDto DateRange { get; set; } = new();
    public BaseMetricsDto BaseMetrics { get; set; } = new();
    public RealTimeAnalyticsDto RealTimeAnalytics { get; set; } = new();
    public PredictiveAnalyticsDto PredictiveAnalytics { get; set; } = new();
    public AdvancedVisualizationsDto AdvancedVisualizations { get; set; } = new();
    public CrossServiceAnalyticsDto CrossServiceAnalytics { get; set; } = new();
    public List<DashboardInsightDto> Insights { get; set; } = new();
    public DashboardPerformanceMetricsDto PerformanceMetrics { get; set; } = new();
    public DashboardCustomizationOptionsDto CustomizationOptions { get; set; } = new();
    public ExportOptionsDto ExportOptions { get; set; } = new();
    public SharingOptionsDto SharingOptions { get; set; } = new();
    public List<DashboardWidgetDto> Widgets { get; set; } = new();
    public List<LiveChartDto> LiveCharts { get; set; } = new();
    public List<ActivityEventDto> RecentActivity { get; set; } = new();
    public SystemHealthDto SystemHealth { get; set; } = new();
    public List<RealTimeAlertDto> Alerts { get; set; } = new();
    public List<TrendForecastDto> TrendForecasts { get; set; } = new();
    public List<AnomalyPredictionDto> AnomalyPredictions { get; set; } = new();
    public List<RecommendedActionDto> RecommendedActions { get; set; } = new();
    public PredictionModelPerformanceDto ModelPerformance { get; set; } = new();
    public QueryPerformanceDto QueryPerformance { get; set; } = new();
    public VisualizationPerformanceDto VisualizationPerformance { get; set; } = new();
    public List<OptimizationSuggestionDto> OptimizationSuggestions { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Live chart data transfer object
/// </summary>
public class LiveChartDto
{
    public string ChartId { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty; // "Line", "Bar", "Pie", "Area", "Gauge"
    public string Title { get; set; } = string.Empty;
    public List<ChartDataPointDto> DataPoints { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public bool IsRealTime { get; set; } = true;
    public int RefreshInterval { get; set; } = 30; // seconds
}

/// <summary>
/// Chart data point data transfer object
/// </summary>
public class ChartDataPointDto
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Activity event data transfer object
/// </summary>
public class ActivityEventDto
{
    public string EventId { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
    public string Severity { get; set; } = "Info";
}

/// <summary>
/// System health data transfer object
/// </summary>
public class SystemHealthDto
{
    public string Status { get; set; } = string.Empty; // "Healthy", "Warning", "Critical"
    public decimal CpuUsage { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal DiskUsage { get; set; }
    public int ActiveConnections { get; set; }
    public decimal ResponseTime { get; set; }
    public decimal Throughput { get; set; }
    public List<ServiceHealthDto> Services { get; set; } = new();
    public DateTime LastChecked { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}

/// <summary>
/// Service health data transfer object
/// </summary>
public class ServiceHealthDto
{
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal ResponseTime { get; set; }
    public DateTime LastChecked { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Real-time alert data transfer object
/// </summary>
public class RealTimeAlertDto
{
    public string AlertId { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsAcknowledged { get; set; } = false;
    public DateTime? AcknowledgedAt { get; set; }
    public string? AcknowledgedBy { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public List<string> Actions { get; set; } = new();
}

/// <summary>
/// Predictive analytics request data transfer object
/// </summary>
public class PredictiveAnalyticsRequest
{
    [Required]
    public string ModelType { get; set; } = string.Empty; // "Trend", "Anomaly", "Forecast", "Classification"

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public int PredictionHorizonDays { get; set; } = 30;
    public bool IncludeConfidenceIntervals { get; set; } = true;
    public List<string> PredictionTypes { get; set; } = new();

    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int ForecastPeriod { get; set; } = 30; // days
    public List<string> ModelTypes { get; set; } = new();
    public decimal ConfidenceLevel { get; set; } = 0.95m;
}

/// <summary>
/// Trend forecast data transfer object
/// </summary>
public class TrendForecastDto
{
    public string ForecastId { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public List<ForecastDataPointDto> ForecastPoints { get; set; } = new();
    public decimal Accuracy { get; set; }
    public decimal ConfidenceLevel { get; set; }
    public string TrendDirection { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
    public DateTime GeneratedAt { get; set; }
    public Dictionary<string, object> ModelMetadata { get; set; } = new();
}

/// <summary>
/// Forecast data point data transfer object
/// </summary>
public class ForecastDataPointDto
{
    public DateTime Date { get; set; }
    public decimal PredictedValue { get; set; }
    public decimal UpperBound { get; set; }
    public decimal LowerBound { get; set; }
    public decimal Confidence { get; set; }
}

/// <summary>
/// Anomaly prediction data transfer object
/// </summary>
public class AnomalyPredictionDto
{
    public string PredictionId { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public DateTime PredictedDate { get; set; }
    public decimal AnomalyScore { get; set; }
    public string AnomalyType { get; set; } = string.Empty; // "Spike", "Drop", "Drift", "Outlier"
    public decimal Severity { get; set; }
    public string Description { get; set; } = string.Empty;
    public List<string> PossibleCauses { get; set; } = new();
    public List<string> RecommendedActions { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

/// <summary>
/// Predictive analytics data transfer object
/// </summary>
public class PredictiveAnalyticsDto
{
    public string UserId { get; set; } = string.Empty;
    public int PredictionHorizon { get; set; } = 30;
    public TimeSpan PredictionHorizonTimeSpan { get; set; } = TimeSpan.FromDays(30);
    public List<PredictionDto> Predictions { get; set; } = new();
    public List<TrendForecastDto> TrendForecasts { get; set; } = new();
    public List<AnomalyPredictionDto> AnomalyPredictions { get; set; } = new();
    public List<RecommendedActionDto> RecommendedActions { get; set; } = new();
    public PredictionModelPerformanceDto ModelPerformance { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Prediction data transfer object
/// </summary>
public class PredictionDto
{
    public string PredictionId { get; set; } = string.Empty;
    public string PredictionType { get; set; } = string.Empty;
    public string MetricName { get; set; } = string.Empty;
    public DateTime PredictionDate { get; set; }
    public decimal PredictedValue { get; set; }
    public decimal Confidence { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Recommended action data transfer object
/// </summary>
public class RecommendedActionDto
{
    public string ActionId { get; set; } = string.Empty;
    public string ActionType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty; // "Low", "Medium", "High", "Critical"
    public decimal ImpactScore { get; set; }
    public decimal EffortScore { get; set; }
    public List<string> Benefits { get; set; } = new();
    public List<string> Requirements { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = "Pending"; // "Pending", "InProgress", "Completed", "Dismissed"
}

/// <summary>
/// Prediction model performance data transfer object
/// </summary>
public class PredictionModelPerformanceDto
{
    public string ModelName { get; set; } = string.Empty;
    public string ModelVersion { get; set; } = string.Empty;
    public decimal Accuracy { get; set; }
    public decimal Precision { get; set; }
    public decimal Recall { get; set; }
    public decimal F1Score { get; set; }
    public decimal MeanAbsoluteError { get; set; }
    public decimal RootMeanSquareError { get; set; }
    public DateTime LastTrained { get; set; }
    public DateTime LastEvaluated { get; set; }
    public int TrainingDataSize { get; set; }
    public Dictionary<string, object> Hyperparameters { get; set; } = new();
}

/// <summary>
/// Query performance data transfer object
/// </summary>
public class QueryPerformanceDto
{
    public string QueryId { get; set; } = string.Empty;
    public string QueryName { get; set; } = string.Empty;
    public decimal AverageExecutionTime { get; set; }
    public decimal MinExecutionTime { get; set; }
    public decimal MaxExecutionTime { get; set; }
    public int ExecutionCount { get; set; }
    public decimal CacheHitRate { get; set; }
    public DateTime LastExecuted { get; set; }
    public List<string> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// Visualization performance data transfer object
/// </summary>
public class VisualizationPerformanceDto
{
    public string VisualizationId { get; set; } = string.Empty;
    public string VisualizationType { get; set; } = string.Empty;
    public decimal AverageRenderTime { get; set; }
    public decimal DataLoadTime { get; set; }
    public int DataPointCount { get; set; }
    public decimal MemoryUsage { get; set; }
    public DateTime LastRendered { get; set; }
    public List<string> PerformanceIssues { get; set; } = new();
}

/// <summary>
/// Optimization suggestion data transfer object
/// </summary>
public class OptimizationSuggestionDto
{
    public string SuggestionId { get; set; } = string.Empty;
    public string SuggestionType { get; set; } = string.Empty; // "Query", "Index", "Cache", "Configuration"
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public decimal ImplementationEffort { get; set; }
    public List<string> Benefits { get; set; } = new();
    public List<string> Steps { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string Status { get; set; } = "Pending";
}

/// <summary>
/// Base metrics data transfer object
/// </summary>
public class BaseMetricsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedTrips { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal CustomerSatisfaction { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Real-time analytics data transfer object
/// </summary>
public class RealTimeAnalyticsDto
{
    public string UserId { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public int UpdateInterval { get; set; } = 30;
    public List<LiveMetricDto> LiveMetrics { get; set; } = new();
    public List<LiveChartDto> LiveCharts { get; set; } = new();
    public List<ActivityEventDto> ActivityFeed { get; set; } = new();
    public SystemHealthDto SystemHealth { get; set; } = new();
    public List<RealTimeAlertDto> AlertsStream { get; set; } = new();
}

/// <summary>
/// Live metric data transfer object
/// </summary>
public class LiveMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty; // "Up", "Down", "Stable"
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Advanced visualizations data transfer object
/// </summary>
public class AdvancedVisualizationsDto
{
    public List<InteractiveChartDto> InteractiveCharts { get; set; } = new();
    public List<HeatmapDto> Heatmaps { get; set; } = new();
    public List<GeospatialVisualizationDto> Maps { get; set; } = new();
    public List<CustomVisualizationDto> CustomVisualizations { get; set; } = new();
}

/// <summary>
/// Interactive chart data transfer object
/// </summary>
public class InteractiveChartDto
{
    public string ChartId { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<ChartDataPointDto> Data { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public bool IsInteractive { get; set; } = true;
}

/// <summary>
/// Heatmap data transfer object
/// </summary>
public class HeatmapDto
{
    public string HeatmapId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<HeatmapDataPointDto> DataPoints { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Heatmap data point data transfer object
/// </summary>
public class HeatmapDataPointDto
{
    public int X { get; set; }
    public int Y { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
}

/// <summary>
/// Geospatial visualization data transfer object
/// </summary>
public class GeospatialVisualizationDto
{
    public string MapId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public List<MapDataPointDto> DataPoints { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Map data point data transfer object
/// </summary>
public class MapDataPointDto
{
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public decimal Value { get; set; }
    public string Label { get; set; } = string.Empty;
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// Custom visualization data transfer object
/// </summary>
public class CustomVisualizationDto
{
    public string VisualizationId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Dashboard insight data transfer object
/// </summary>
public class DashboardInsightDto
{
    public string InsightId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "Trend", "Anomaly", "Opportunity", "Risk"
    public decimal ImportanceScore { get; set; }
    public DateTime GeneratedAt { get; set; }
    public List<string> RecommendedActions { get; set; } = new();
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Dashboard performance metrics data transfer object
/// </summary>
public class DashboardPerformanceMetricsDto
{
    public string DashboardId { get; set; } = string.Empty;
    public DateTime MeasuredAt { get; set; }
    public decimal LoadTime { get; set; }
    public decimal DataFreshness { get; set; }
    public QueryPerformanceDto QueryPerformance { get; set; } = new();
    public VisualizationPerformanceDto VisualizationPerformance { get; set; } = new();
    public List<UserEngagementDto> UserEngagement { get; set; } = new();
    public decimal ErrorRate { get; set; }
    public int VisualizationCount { get; set; }
    public int AlertCount { get; set; }
    public int InsightCount { get; set; }
    public List<OptimizationSuggestionDto> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// User engagement data transfer object
/// </summary>
public class UserEngagementDto
{
    public string UserId { get; set; } = string.Empty;
    public TimeSpan SessionDuration { get; set; }
    public int PageViews { get; set; }
    public int Interactions { get; set; }
    public DateTime LastActivity { get; set; }
}

/// <summary>
/// Dashboard customization options data transfer object
/// </summary>
public class DashboardCustomizationOptionsDto
{
    public List<string> AvailableThemes { get; set; } = new();
    public List<string> AvailableLayouts { get; set; } = new();
    public List<string> AvailableWidgets { get; set; } = new();
    public Dictionary<string, object> UserPreferences { get; set; } = new();
    public bool AllowCustomWidgets { get; set; } = true;
    public bool AllowLayoutChanges { get; set; } = true;
}

/// <summary>
/// Sharing options data transfer object
/// </summary>
public class SharingOptionsDto
{
    public bool AllowSharing { get; set; } = true;
    public List<string> AvailablePermissions { get; set; } = new();
    public List<SharedUserDto> SharedUsers { get; set; } = new();
    public bool IsPublic { get; set; } = false;
    public string ShareUrl { get; set; } = string.Empty;
}

/// <summary>
/// Shared user data transfer object
/// </summary>
public class SharedUserDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Permission { get; set; } = string.Empty; // "View", "Edit", "Admin"
    public DateTime SharedAt { get; set; }
}
