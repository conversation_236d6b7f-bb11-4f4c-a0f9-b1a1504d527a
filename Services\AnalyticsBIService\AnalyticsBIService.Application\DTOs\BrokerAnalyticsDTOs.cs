using System.ComponentModel.DataAnnotations;

namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Broker financial performance data transfer object
/// </summary>
public class BrokerFinancialPerformanceDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public string BrokerName { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalCommissions { get; set; }
    public decimal AverageCommissionRate { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public int TotalDeals { get; set; }
    public int SuccessfulDeals { get; set; }
    public decimal DealSuccessRate { get; set; }
    public decimal AverageRevenuePerDeal { get; set; }
    public decimal AverageCommissionPerDeal { get; set; }
    public DateTime AnalyticsPeriodStart { get; set; }
    public DateTime AnalyticsPeriodEnd { get; set; }
    public List<BrokerRevenueStreamDto> RevenueStreams { get; set; } = new();
    public List<BrokerClientPerformanceDto> TopClients { get; set; } = new();
}

/// <summary>
/// Broker revenue stream data transfer object
/// </summary>
public class BrokerRevenueStreamDto
{
    public string StreamType { get; set; } = string.Empty; // "Commission", "Service Fee", "Premium Service"
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public int TransactionCount { get; set; }
    public decimal AveragePerTransaction { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Broker client performance data transfer object
/// </summary>
public class BrokerClientPerformanceDto
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public string ClientType { get; set; } = string.Empty; // "Shipper", "Carrier"
    public decimal TotalRevenue { get; set; }
    public decimal TotalCommissions { get; set; }
    public int TotalDeals { get; set; }
    public decimal AverageCommissionRate { get; set; }
    public DateTime LastDealDate { get; set; }
    public string RelationshipStatus { get; set; } = string.Empty; // "Active", "Inactive", "Prospect"
}

/// <summary>
/// Broker market analytics data transfer object
/// </summary>
public class BrokerMarketAnalyticsDto
{
    public string BrokerId { get; set; } = string.Empty;
    public decimal MarketShare { get; set; }
    public int MarketRank { get; set; }
    public int TotalCompetitors { get; set; }
    public List<MarketSegmentDto> MarketSegments { get; set; } = new();
    public List<GeographicPerformanceDto> GeographicPerformance { get; set; } = new();
    public List<ServiceTypePerformanceDto> ServiceTypePerformance { get; set; } = new();
    public decimal CustomerAcquisitionRate { get; set; }
    public decimal CustomerRetentionRate { get; set; }
}

/// <summary>
/// Market segment data transfer object
/// </summary>
public class MarketSegmentDto
{
    public string SegmentName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public decimal MarketShare { get; set; }
    public int DealCount { get; set; }
    public decimal GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
}

// GeographicPerformanceDto is already defined in AdminAnalyticsDto.cs - using that instead

/// <summary>
/// Service type performance data transfer object
/// </summary>
public class ServiceTypePerformanceDto
{
    public string ServiceType { get; set; } = string.Empty; // "FTL", "LTL", "Express", "Standard"
    public decimal Revenue { get; set; }
    public int DealCount { get; set; }
    public decimal AverageCommissionRate { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal DemandTrend { get; set; }
}

/// <summary>
/// Broker operational analytics data transfer object
/// </summary>
public class BrokerOperationalAnalyticsDto
{
    public string BrokerId { get; set; } = string.Empty;
    public decimal AverageQuoteResponseTime { get; set; }
    public decimal QuoteAcceptanceRate { get; set; }
    public decimal AverageNegotiationTime { get; set; }
    public decimal DealClosureRate { get; set; }
    public int ActiveQuotes { get; set; }
    public int PendingNegotiations { get; set; }
    public int CompletedDeals { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public List<BrokerEfficiencyMetricDto> EfficiencyMetrics { get; set; } = new();

    // Additional properties required by query handlers
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public int TotalRFQsProcessed { get; set; }
    public int SuccessfulRFQs { get; set; }
    public decimal RFQConversionRate { get; set; }
    public decimal AverageRFQProcessingTime { get; set; }
    public int TotalQuotesGenerated { get; set; }
    public int AcceptedQuotes { get; set; }
    public decimal QuoteSuccessRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public int ActiveCarriers { get; set; }
    public decimal CarrierNetworkUtilization { get; set; }
    public decimal AverageCarrierResponseTime { get; set; }
    public decimal CarrierSatisfactionScore { get; set; }
    public decimal OperationalEfficiencyScore { get; set; }
    public decimal ProcessAutomationRate { get; set; }
    public decimal ResourceUtilizationRate { get; set; }
    public List<OperationalTrendDto> OperationalTrends { get; set; } = new();
    public List<TopCarrierDto> TopCarriers { get; set; } = new();
    public List<TopRouteDto> TopRoutes { get; set; } = new();
}

/// <summary>
/// Broker efficiency metric data transfer object
/// </summary>
public class BrokerEfficiencyMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal BenchmarkValue { get; set; }
    public decimal PerformanceScore { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "Excellent", "Good", "Average", "Needs Improvement"
}

/// <summary>
/// Broker relationship analytics data transfer object
/// </summary>
public class BrokerRelationshipAnalyticsDto
{
    public string BrokerId { get; set; } = string.Empty;
    public int TotalShippers { get; set; }
    public int ActiveShippers { get; set; }
    public int TotalCarriers { get; set; }
    public int ActiveCarriers { get; set; }
    public decimal ShipperRetentionRate { get; set; }
    public decimal CarrierRetentionRate { get; set; }
    public decimal AverageRelationshipDuration { get; set; }
    public List<RelationshipHealthDto> ShipperRelationships { get; set; } = new();
    public List<RelationshipHealthDto> CarrierRelationships { get; set; } = new();
}

/// <summary>
/// Relationship health data transfer object
/// </summary>
public class RelationshipHealthDto
{
    public string EntityId { get; set; } = string.Empty;
    public string EntityName { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty; // "Shipper", "Carrier"
    public decimal HealthScore { get; set; }
    public string HealthStatus { get; set; } = string.Empty; // "Excellent", "Good", "At Risk", "Poor"
    public int DealCount { get; set; }
    public decimal TotalRevenue { get; set; }
    public DateTime LastInteraction { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public List<string> Opportunities { get; set; } = new();
}

/// <summary>
/// Broker performance benchmarking data transfer object
/// </summary>
public class BrokerPerformanceBenchmarkingDto
{
    public string BrokerId { get; set; } = string.Empty;
    public List<BenchmarkComparisonDto> IndustryBenchmarks { get; set; } = new();
    public List<BenchmarkComparisonDto> PeerBenchmarks { get; set; } = new();
    public decimal OverallPerformanceScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty; // "Top Performer", "Above Average", "Average", "Below Average"
    public List<ImprovementAreaDto> ImprovementAreas { get; set; } = new();
    public List<StrengthAreaDto> StrengthAreas { get; set; } = new();
}

/// <summary>
/// Benchmark comparison data transfer object
/// </summary>
public class BenchmarkComparisonDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal BrokerValue { get; set; }
    public decimal BenchmarkValue { get; set; }
    public decimal Variance { get; set; }
    public string PerformanceLevel { get; set; } = string.Empty;
    public int Percentile { get; set; }
}

/// <summary>
/// Improvement area data transfer object
/// </summary>
public class ImprovementAreaDto
{
    public string AreaName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal ImpactScore { get; set; }
    public string Priority { get; set; } = string.Empty; // "High", "Medium", "Low"
    public List<string> RecommendedActions { get; set; } = new();
    public decimal EstimatedImpact { get; set; }

    // Missing properties referenced in GetCarrierFeedbackAnalysisQueryHandler
    public string Area { get; set; } = string.Empty; // Alias for AreaName
    public int MentionCount { get; set; }
    public List<string> SpecificIssues { get; set; } = new();
    public List<string> SuggestedActions { get; set; } = new(); // Alias for RecommendedActions
}

/// <summary>
/// Strength area data transfer object
/// </summary>
public class StrengthAreaDto
{
    public string AreaName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal StrengthScore { get; set; }
    public string CompetitiveAdvantage { get; set; } = string.Empty;
    public List<string> LeverageOpportunities { get; set; } = new();
}

/// <summary>
/// Broker quote analytics data transfer object
/// </summary>
public class BrokerQuoteAnalyticsDto
{
    public string BrokerId { get; set; } = string.Empty;
    public int TotalQuotes { get; set; }
    public int AcceptedQuotes { get; set; }
    public int RejectedQuotes { get; set; }
    public int PendingQuotes { get; set; }
    public decimal QuoteAcceptanceRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal CompetitiveWinRate { get; set; }
    public List<QuotePerformanceByRouteDto> QuotesByRoute { get; set; } = new();
    public List<QuotePerformanceByServiceDto> QuotesByService { get; set; } = new();
}

/// <summary>
/// Quote performance by route data transfer object
/// </summary>
public class QuotePerformanceByRouteDto
{
    public string RouteId { get; set; } = string.Empty;
    public string OriginCity { get; set; } = string.Empty;
    public string DestinationCity { get; set; } = string.Empty;
    public int QuoteCount { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal CompetitivePosition { get; set; }
}

/// <summary>
/// Quote performance by service data transfer object
/// </summary>
public class QuotePerformanceByServiceDto
{
    public string ServiceType { get; set; } = string.Empty;
    public int QuoteCount { get; set; }
    public decimal AcceptanceRate { get; set; }
    public decimal AverageQuoteValue { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal MarketDemand { get; set; }
}
