using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class PerformanceTestMetrics
{
    public double AverageResponseTime { get; private set; }
    public double MedianResponseTime { get; private set; }
    public double P95ResponseTime { get; private set; }
    public double P99ResponseTime { get; private set; }
    public double ThroughputRequestsPerSecond { get; private set; }
    public double ErrorRate { get; private set; }
    public int TotalRequests { get; private set; }
    public int SuccessfulRequests { get; private set; }
    public int FailedRequests { get; private set; }

    public PerformanceTestMetrics(
        double averageResponseTime,
        double medianResponseTime,
        double p95ResponseTime,
        double p99ResponseTime,
        double throughputRequestsPerSecond,
        double errorRate,
        int totalRequests,
        int successfulRequests,
        int failedRequests)
    {
        AverageResponseTime = averageResponseTime;
        MedianResponseTime = medianResponseTime;
        P95ResponseTime = p95ResponseTime;
        P99ResponseTime = p99ResponseTime;
        ThroughputRequestsPerSecond = throughputRequestsPerSecond;
        ErrorRate = Math.Max(0, Math.Min(100, errorRate));
        TotalRequests = totalRequests;
        SuccessfulRequests = successfulRequests;
        FailedRequests = failedRequests;
    }
}

public class PerformanceTestScenarioMetrics
{
    public double AverageResponseTime { get; private set; }
    public double ThroughputRequestsPerSecond { get; private set; }
    public double ErrorRate { get; private set; }
    public int TotalRequests { get; private set; }
    public TimeSpan Duration { get; private set; }

    public PerformanceTestScenarioMetrics(
        double averageResponseTime,
        double throughputRequestsPerSecond,
        double errorRate,
        int totalRequests,
        TimeSpan duration)
    {
        AverageResponseTime = averageResponseTime;
        ThroughputRequestsPerSecond = throughputRequestsPerSecond;
        ErrorRate = Math.Max(0, Math.Min(100, errorRate));
        TotalRequests = totalRequests;
        Duration = duration;
    }
}

public class PerformanceTestStepMetrics
{
    public double ResponseTime { get; private set; }
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public Dictionary<string, object> CustomMetrics { get; private set; }

    public PerformanceTestStepMetrics(
        double responseTime,
        bool isSuccessful,
        string? errorMessage = null,
        Dictionary<string, object>? customMetrics = null)
    {
        ResponseTime = responseTime;
        IsSuccessful = isSuccessful;
        ErrorMessage = errorMessage;
        CustomMetrics = customMetrics ?? new Dictionary<string, object>();
    }
}

public class LoadTestResult
{
    public Guid TestId { get; private set; }
    public string TestName { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public LoadTestStatus Status { get; private set; }
    public LoadTestConfiguration Configuration { get; private set; }
    public LoadTestMetrics Metrics { get; private set; }
    public List<LoadTestPhase> Phases { get; private set; }

    public LoadTestResult(
        Guid testId,
        string testName,
        DateTime startedAt,
        LoadTestStatus status,
        LoadTestConfiguration configuration,
        LoadTestMetrics metrics,
        List<LoadTestPhase> phases,
        DateTime? completedAt = null)
    {
        TestId = testId;
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Status = status;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        Phases = phases ?? new List<LoadTestPhase>();
    }
}

public class LoadTestRequest
{
    public string TestName { get; private set; }
    public LoadTestConfiguration Configuration { get; private set; }
    public List<LoadTestScenario> Scenarios { get; private set; }
    public Guid RequestedBy { get; private set; }

    public LoadTestRequest(
        string testName,
        LoadTestConfiguration configuration,
        List<LoadTestScenario> scenarios,
        Guid requestedBy)
    {
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        Scenarios = scenarios ?? new List<LoadTestScenario>();
        RequestedBy = requestedBy;
    }
}

public class LoadTestConfiguration
{
    public int InitialUsers { get; private set; }
    public int MaxUsers { get; private set; }
    public int UserIncrement { get; private set; }
    public TimeSpan IncrementInterval { get; private set; }
    public TimeSpan TestDuration { get; private set; }
    public LoadTestStrategy Strategy { get; private set; }

    public LoadTestConfiguration(
        int initialUsers,
        int maxUsers,
        int userIncrement,
        TimeSpan incrementInterval,
        TimeSpan testDuration,
        LoadTestStrategy strategy)
    {
        InitialUsers = initialUsers;
        MaxUsers = maxUsers;
        UserIncrement = userIncrement;
        IncrementInterval = incrementInterval;
        TestDuration = testDuration;
        Strategy = strategy;
    }
}

public class LoadTestMetrics
{
    public double PeakThroughput { get; private set; }
    public double AverageThroughput { get; private set; }
    public double PeakResponseTime { get; private set; }
    public double AverageResponseTime { get; private set; }
    public int MaxConcurrentUsers { get; private set; }
    public double ErrorRate { get; private set; }
    public ResourceUtilization ResourceUtilization { get; private set; }

    public LoadTestMetrics(
        double peakThroughput,
        double averageThroughput,
        double peakResponseTime,
        double averageResponseTime,
        int maxConcurrentUsers,
        double errorRate,
        ResourceUtilization resourceUtilization)
    {
        PeakThroughput = peakThroughput;
        AverageThroughput = averageThroughput;
        PeakResponseTime = peakResponseTime;
        AverageResponseTime = averageResponseTime;
        MaxConcurrentUsers = maxConcurrentUsers;
        ErrorRate = Math.Max(0, Math.Min(100, errorRate));
        ResourceUtilization = resourceUtilization ?? throw new ArgumentNullException(nameof(resourceUtilization));
    }
}

public class LoadTestPhase
{
    public string Name { get; private set; }
    public int ConcurrentUsers { get; private set; }
    public DateTime StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public LoadTestPhaseMetrics Metrics { get; private set; }

    public LoadTestPhase(
        string name,
        int concurrentUsers,
        DateTime startTime,
        DateTime endTime,
        LoadTestPhaseMetrics metrics)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        ConcurrentUsers = concurrentUsers;
        StartTime = startTime;
        EndTime = endTime;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
    }
}

public class LoadTestPhaseMetrics
{
    public double AverageResponseTime { get; private set; }
    public double ThroughputRequestsPerSecond { get; private set; }
    public double ErrorRate { get; private set; }
    public ResourceUtilization ResourceUtilization { get; private set; }

    public LoadTestPhaseMetrics(
        double averageResponseTime,
        double throughputRequestsPerSecond,
        double errorRate,
        ResourceUtilization resourceUtilization)
    {
        AverageResponseTime = averageResponseTime;
        ThroughputRequestsPerSecond = throughputRequestsPerSecond;
        ErrorRate = Math.Max(0, Math.Min(100, errorRate));
        ResourceUtilization = resourceUtilization ?? throw new ArgumentNullException(nameof(resourceUtilization));
    }
}

public class LoadTestScenario
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public int Weight { get; private set; }
    public List<LoadTestAction> Actions { get; private set; }

    public LoadTestScenario(
        string name,
        string description,
        int weight,
        List<LoadTestAction> actions)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Weight = weight;
        Actions = actions ?? new List<LoadTestAction>();
    }
}

public class LoadTestAction
{
    public string Name { get; private set; }
    public LoadTestActionType Type { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }
    public TimeSpan ThinkTime { get; private set; }

    public LoadTestAction(
        string name,
        LoadTestActionType type,
        Dictionary<string, object> parameters,
        TimeSpan thinkTime)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Parameters = parameters ?? new Dictionary<string, object>();
        ThinkTime = thinkTime;
    }
}

public class StressTestResult
{
    public Guid TestId { get; private set; }
    public string TestName { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public StressTestStatus Status { get; private set; }
    public StressTestConfiguration Configuration { get; private set; }
    public StressTestMetrics Metrics { get; private set; }
    public StressTestBreakingPoint? BreakingPoint { get; private set; }

    public StressTestResult(
        Guid testId,
        string testName,
        DateTime startedAt,
        StressTestStatus status,
        StressTestConfiguration configuration,
        StressTestMetrics metrics,
        DateTime? completedAt = null,
        StressTestBreakingPoint? breakingPoint = null)
    {
        TestId = testId;
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        StartedAt = startedAt;
        CompletedAt = completedAt;
        Status = status;
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        BreakingPoint = breakingPoint;
    }
}

public class StressTestRequest
{
    public string TestName { get; private set; }
    public StressTestConfiguration Configuration { get; private set; }
    public List<StressTestScenario> Scenarios { get; private set; }
    public Guid RequestedBy { get; private set; }

    public StressTestRequest(
        string testName,
        StressTestConfiguration configuration,
        List<StressTestScenario> scenarios,
        Guid requestedBy)
    {
        TestName = testName ?? throw new ArgumentNullException(nameof(testName));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        Scenarios = scenarios ?? new List<StressTestScenario>();
        RequestedBy = requestedBy;
    }
}

public class StressTestConfiguration
{
    public int StartingUsers { get; private set; }
    public int MaxUsers { get; private set; }
    public int UserIncrementStep { get; private set; }
    public TimeSpan StepDuration { get; private set; }
    public double FailureThreshold { get; private set; }
    public StressTestStopCondition StopCondition { get; private set; }

    public StressTestConfiguration(
        int startingUsers,
        int maxUsers,
        int userIncrementStep,
        TimeSpan stepDuration,
        double failureThreshold,
        StressTestStopCondition stopCondition)
    {
        StartingUsers = startingUsers;
        MaxUsers = maxUsers;
        UserIncrementStep = userIncrementStep;
        StepDuration = stepDuration;
        FailureThreshold = Math.Max(0, Math.Min(100, failureThreshold));
        StopCondition = stopCondition;
    }
}

public class StressTestMetrics
{
    public int MaxSustainableUsers { get; private set; }
    public double MaxThroughput { get; private set; }
    public double ResponseTimeAtBreakingPoint { get; private set; }
    public double ErrorRateAtBreakingPoint { get; private set; }
    public ResourceUtilization PeakResourceUtilization { get; private set; }

    public StressTestMetrics(
        int maxSustainableUsers,
        double maxThroughput,
        double responseTimeAtBreakingPoint,
        double errorRateAtBreakingPoint,
        ResourceUtilization peakResourceUtilization)
    {
        MaxSustainableUsers = maxSustainableUsers;
        MaxThroughput = maxThroughput;
        ResponseTimeAtBreakingPoint = responseTimeAtBreakingPoint;
        ErrorRateAtBreakingPoint = Math.Max(0, Math.Min(100, errorRateAtBreakingPoint));
        PeakResourceUtilization = peakResourceUtilization ?? throw new ArgumentNullException(nameof(peakResourceUtilization));
    }
}

public class StressTestBreakingPoint
{
    public int ConcurrentUsers { get; private set; }
    public double ThroughputRequestsPerSecond { get; private set; }
    public double ResponseTimeMs { get; private set; }
    public double ErrorRate { get; private set; }
    public DateTime ReachedAt { get; private set; }
    public string Reason { get; private set; }

    public StressTestBreakingPoint(
        int concurrentUsers,
        double throughputRequestsPerSecond,
        double responseTimeMs,
        double errorRate,
        DateTime reachedAt,
        string reason)
    {
        ConcurrentUsers = concurrentUsers;
        ThroughputRequestsPerSecond = throughputRequestsPerSecond;
        ResponseTimeMs = responseTimeMs;
        ErrorRate = Math.Max(0, Math.Min(100, errorRate));
        ReachedAt = reachedAt;
        Reason = reason ?? throw new ArgumentNullException(nameof(reason));
    }
}

public class StressTestScenario
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<StressTestAction> Actions { get; private set; }

    public StressTestScenario(
        string name,
        string description,
        List<StressTestAction> actions)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Actions = actions ?? new List<StressTestAction>();
    }
}

public class StressTestAction
{
    public string Name { get; private set; }
    public StressTestActionType Type { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public StressTestAction(
        string name,
        StressTestActionType type,
        Dictionary<string, object> parameters)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class ResourceUtilization
{
    public double CpuUsagePercentage { get; private set; }
    public double MemoryUsagePercentage { get; private set; }
    public double DiskUsagePercentage { get; private set; }
    public double NetworkUsagePercentage { get; private set; }

    public ResourceUtilization(
        double cpuUsagePercentage,
        double memoryUsagePercentage,
        double diskUsagePercentage,
        double networkUsagePercentage)
    {
        CpuUsagePercentage = Math.Max(0, Math.Min(100, cpuUsagePercentage));
        MemoryUsagePercentage = Math.Max(0, Math.Min(100, memoryUsagePercentage));
        DiskUsagePercentage = Math.Max(0, Math.Min(100, diskUsagePercentage));
        NetworkUsagePercentage = Math.Max(0, Math.Min(100, networkUsagePercentage));
    }
}
