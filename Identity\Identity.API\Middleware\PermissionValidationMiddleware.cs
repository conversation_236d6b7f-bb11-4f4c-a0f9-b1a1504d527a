using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Identity.API.Middleware
{
    public class PermissionValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PermissionValidationMiddleware> _logger;

        public PermissionValidationMiddleware(RequestDelegate next, ILogger<PermissionValidationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IPermissionRepository permissionRepository)
        {
            // Skip permission validation for certain paths
            if (ShouldSkipValidation(context.Request.Path))
            {
                await _next(context);
                return;
            }

            // Check if user is authenticated
            if (!context.User.Identity.IsAuthenticated)
            {
                await _next(context);
                return;
            }

            try
            {
                // Extract required permission from route or headers
                var requiredPermission = ExtractRequiredPermission(context);
                
                if (string.IsNullOrEmpty(requiredPermission))
                {
                    await _next(context);
                    return;
                }

                // Get user ID from claims
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                {
                    await UnauthorizedResponse(context, "Invalid user ID in token");
                    return;
                }

                // Check if user has the required permission
                var userPermissions = await permissionRepository.GetUserPermissionsAsync(userId);
                var hasPermission = HasRequiredPermission(userPermissions, requiredPermission);

                if (!hasPermission)
                {
                    _logger.LogWarning("User {UserId} attempted to access {Path} without required permission {Permission}", 
                        userId, context.Request.Path, requiredPermission);
                    
                    await ForbiddenResponse(context, $"Insufficient permissions. Required: {requiredPermission}");
                    return;
                }

                // Add permission info to context for downstream use
                context.Items["UserPermissions"] = userPermissions;
                context.Items["RequiredPermission"] = requiredPermission;

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in permission validation middleware");
                await _next(context);
            }
        }

        private bool ShouldSkipValidation(PathString path)
        {
            var skipPaths = new[]
            {
                "/api/auth/login",
                "/api/auth/register",
                "/api/auth/refresh",
                "/api/auth/logout",
                "/health",
                "/swagger",
                "/api/users/permissions" // This endpoint validates permissions itself
            };

            return skipPaths.Any(skipPath => path.StartsWithSegments(skipPath, StringComparison.OrdinalIgnoreCase));
        }

        private string ExtractRequiredPermission(HttpContext context)
        {
            // First, check if permission is specified in headers
            if (context.Request.Headers.TryGetValue("X-Required-Permission", out var headerPermission))
            {
                return headerPermission.FirstOrDefault();
            }

            // Extract from route pattern
            var path = context.Request.Path.Value?.ToLowerInvariant();
            var method = context.Request.Method.ToUpperInvariant();

            if (string.IsNullOrEmpty(path))
                return null;

            // Map common patterns to permissions
            if (path.StartsWith("/api/subadmins"))
            {
                return method switch
                {
                    "GET" => "subadmin.read",
                    "POST" => "subadmin.write",
                    "PUT" => "subadmin.write",
                    "DELETE" => "subadmin.delete",
                    _ => null
                };
            }

            if (path.StartsWith("/api/roles"))
            {
                return method switch
                {
                    "GET" => "role.read",
                    "POST" => "role.write",
                    "PUT" => "role.write",
                    "DELETE" => "role.delete",
                    _ => null
                };
            }

            if (path.StartsWith("/api/permissions"))
            {
                return method switch
                {
                    "GET" => "permission.read",
                    "POST" => "permission.write",
                    "PUT" => "permission.write",
                    "DELETE" => "permission.write",
                    _ => null
                };
            }

            if (path.StartsWith("/api/users") && !path.Contains("/permissions"))
            {
                return method switch
                {
                    "GET" => "user.read",
                    "POST" => "user.write",
                    "PUT" => "user.write",
                    "DELETE" => "user.delete",
                    _ => null
                };
            }

            return null;
        }

        private bool HasRequiredPermission(System.Collections.Generic.IEnumerable<Identity.Domain.Entities.Permission> userPermissions, string requiredPermission)
        {
            if (userPermissions == null)
                return false;

            // Parse the required permission
            var parts = requiredPermission.Split('.');
            if (parts.Length != 2)
                return false;

            var module = parts[0];
            var action = parts[1];

            // Check if user has the specific permission
            return userPermissions.Any(p => p.Matches(module, "", action)) ||
                   userPermissions.Any(p => p.Type == Identity.Domain.Entities.PermissionType.Admin) ||
                   userPermissions.Any(p => p.Name == "system.admin");
        }

        private async Task UnauthorizedResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";
            
            var response = new { error = "Unauthorized", message };
            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }

        private async Task ForbiddenResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 403;
            context.Response.ContentType = "application/json";
            
            var response = new { error = "Forbidden", message };
            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }
    }
}
