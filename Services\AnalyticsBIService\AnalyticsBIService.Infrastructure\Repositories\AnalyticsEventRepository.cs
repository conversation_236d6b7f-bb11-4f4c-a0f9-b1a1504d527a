using Microsoft.EntityFrameworkCore;
using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;
using AnalyticsBIService.Domain.Enums;
using Shared.Domain.Common;

namespace AnalyticsBIService.Infrastructure.Repositories;

public class AnalyticsEventRepository : OptimizedRepositoryBase<AnalyticsEvent, Guid>, IAnalyticsEventRepository
{
    public AnalyticsEventRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    public async Task<PagedResult<AnalyticsEvent>> GetByUserIdAsync(
        Guid userId,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.UserId == userId)
            .OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<AnalyticsEvent>> GetByEventTypeAsync(
        string eventType,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.EventName == eventType)
            .OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<AnalyticsEvent>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int page, int pageSize, CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
            .OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<IEnumerable<AnalyticsEvent>> GetByUserAndDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => e.UserId == userId && e.Timestamp >= startDate && e.Timestamp <= endDate)
            .OrderByDescending(e => e.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<AnalyticsEvent>> GetByDataSourceAsync(
        DataSourceType dataSource,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.DataSource == dataSource)
            .OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<Dictionary<AnalyticsEventType, int>> GetEventCountsByTypeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
            .GroupBy(e => e.EventType)
            .Select(g => new { EventType = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.EventType, x => x.Count, cancellationToken);
    }

    public async Task<Dictionary<DataSourceType, int>> GetEventCountsByDataSourceAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
            .GroupBy(e => e.DataSource)
            .Select(g => new { DataSource = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.DataSource, x => x.Count, cancellationToken);
    }

    public async Task<int> GetEventCountAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .CountAsync(e => e.Timestamp >= startDate && e.Timestamp <= endDate, cancellationToken);
    }

    public async Task<IEnumerable<AnalyticsEvent>> GetRecentEventsAsync(int count = 100, CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .OrderByDescending(e => e.Timestamp)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task BulkInsertAsync(IEnumerable<AnalyticsEvent> events, CancellationToken cancellationToken = default)
    {
        await Context.Set<AnalyticsEvent>().AddRangeAsync(events, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetEventStatisticsAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
            .GroupBy(e => e.EventName)
            .Select(g => new { EventName = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.EventName, x => x.Count, cancellationToken);
    }

    public async Task<List<(string EventType, int Count)>> GetTopEventsAsync(
        int topCount,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>().AsQueryable();

        if (startDate.HasValue)
            query = query.Where(e => e.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(e => e.Timestamp <= endDate.Value);

        return await query
            .GroupBy(e => e.EventName)
            .Select(g => new { EventType = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(topCount)
            .Select(x => ValueTuple.Create(x.EventType, x.Count))
            .ToListAsync(cancellationToken);
    }

    public async Task<PagedResult<AnalyticsEvent>> GetUserActivityEventsAsync(
        Guid? userId,
        DateTime? startDate,
        DateTime? endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.EventType == AnalyticsEventType.UserActivity);

        if (userId.HasValue)
            query = query.Where(e => e.UserId == userId.Value);

        if (startDate.HasValue)
            query = query.Where(e => e.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(e => e.Timestamp <= endDate.Value);

        query = query.OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task<PagedResult<AnalyticsEvent>> GetBusinessTransactionEventsAsync(
        DateTime? startDate,
        DateTime? endDate,
        int page,
        int pageSize,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<AnalyticsEvent>()
            .Where(e => e.EventType == AnalyticsEventType.BusinessTransaction);

        if (startDate.HasValue)
            query = query.Where(e => e.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(e => e.Timestamp <= endDate.Value);

        query = query.OrderByDescending(e => e.Timestamp);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<AnalyticsEvent>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };
    }

    public async Task DeleteOldEventsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default)
    {
        await Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp < cutoffDate)
            .ExecuteDeleteAsync(cancellationToken);
    }

    public async Task<List<AnalyticsEvent>> GetEventsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
            .OrderBy(e => e.Timestamp)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<AnalyticsEvent>> GetUnprocessedEventsAsync(
        int batchSize = 100,
        CancellationToken cancellationToken = default)
    {
        return await Context.Set<AnalyticsEvent>()
            .Where(e => !e.IsProcessed)
            .OrderBy(e => e.Timestamp)
            .Take(batchSize)
            .ToListAsync(cancellationToken);
    }

    public async Task DeleteOldProcessedEventsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default)
    {
        await Context.Set<AnalyticsEvent>()
            .Where(e => e.IsProcessed && e.Timestamp < cutoffDate)
            .ExecuteDeleteAsync(cancellationToken);
    }
}
