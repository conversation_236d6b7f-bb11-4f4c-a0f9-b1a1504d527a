# Enhanced PowerShell script to run all tests with comprehensive options
param(
    [string]$TestType = "all",  # all, unit, integration, performance
    [string]$Service = "",      # specific service to test
    [switch]$Coverage,          # generate coverage report
    [switch]$Parallel,          # run tests in parallel
    [switch]$Watch,             # watch mode for continuous testing
    [string]$Filter = "",       # test filter
    [string]$Output = "detailed" # output verbosity
)

Write-Host "🧪 TLI Microservices Test Runner" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Get the solution root directory
$solutionRoot = Split-Path -Parent $PSScriptRoot
$solutionFile = "$solutionRoot\TLIMicroservices.sln"
$testResultsDir = "$solutionRoot\TestResults"

# Ensure test results directory exists
if (-not (Test-Path $testResultsDir)) {
    New-Item -ItemType Directory -Path $testResultsDir -Force | Out-Null
}

# Check if solution file exists
if (-not (Test-Path $solutionFile)) {
    Write-Host "❌ Solution file not found: $solutionFile" -ForegroundColor Red
    exit 1
}

# Function to run specific test category
function Run-TestCategory {
    param(
        [string]$Category,
        [string]$ProjectPattern = "*",
        [string]$TestFilter = ""
    )

    Write-Host "`n🔍 Running $Category tests..." -ForegroundColor Yellow

    $testArgs = @(
        "test", $solutionFile
        "--configuration", "Debug"
        "--no-build"
        "--verbosity", $Output
        "--results-directory", $testResultsDir
        "--logger", "trx"
        "--logger", "console;verbosity=$Output"
    )

    if ($TestFilter) {
        $testArgs += "--filter", $TestFilter
    }

    if ($Coverage) {
        $testArgs += "--collect", "XPlat Code Coverage"
    }

    if ($Parallel) {
        $testArgs += "--parallel"
    }

    & dotnet @testArgs
    return $LASTEXITCODE
}

# Build the solution first
Write-Host "🔨 Building solution..." -ForegroundColor Yellow
dotnet build $solutionFile --configuration Debug --verbosity minimal
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Build completed" -ForegroundColor Green

# Test execution based on type
$testResults = @()

switch ($TestType.ToLower()) {
    "unit" {
        $result = Run-TestCategory -Category "Unit" -TestFilter "Category=Unit"
        $testResults += @{ Type = "Unit"; Result = $result }
    }
    "integration" {
        $result = Run-TestCategory -Category "Integration" -TestFilter "Category=Integration"
        $testResults += @{ Type = "Integration"; Result = $result }
    }
    "performance" {
        $result = Run-TestCategory -Category "Performance" -TestFilter "Category=Performance"
        $testResults += @{ Type = "Performance"; Result = $result }
    }
    "all" {
        # Run all test types
        $unitResult = Run-TestCategory -Category "Unit" -TestFilter "Category=Unit"
        $integrationResult = Run-TestCategory -Category "Integration" -TestFilter "Category=Integration"
        $performanceResult = Run-TestCategory -Category "Performance" -TestFilter "Category=Performance"

        $testResults += @{ Type = "Unit"; Result = $unitResult }
        $testResults += @{ Type = "Integration"; Result = $integrationResult }
        $testResults += @{ Type = "Performance"; Result = $performanceResult }
    }
    default {
        # Run all tests without filter
        $result = Run-TestCategory -Category "All" -TestFilter $Filter
        $testResults += @{ Type = "All"; Result = $result }
    }
}

# Generate summary report
Write-Host "`n📊 Test Results Summary" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$overallSuccess = $true
foreach ($testResult in $testResults) {
    $status = if ($testResult.Result -eq 0) { "✅ PASSED" } else { "❌ FAILED"; $overallSuccess = $false }
    Write-Host "$($testResult.Type) Tests: $status" -ForegroundColor $(if ($testResult.Result -eq 0) { "Green" } else { "Red" })
}

# Generate coverage report if requested
if ($Coverage) {
    Write-Host "`n📈 Generating coverage report..." -ForegroundColor Yellow

    # Find coverage files
    $coverageFiles = Get-ChildItem -Path $testResultsDir -Filter "coverage.cobertura.xml" -Recurse

    if ($coverageFiles.Count -gt 0) {
        # Install reportgenerator if not present
        $reportGenerator = Get-Command "reportgenerator" -ErrorAction SilentlyContinue
        if (-not $reportGenerator) {
            Write-Host "Installing ReportGenerator..." -ForegroundColor Yellow
            dotnet tool install -g dotnet-reportgenerator-globaltool
        }

        # Generate HTML report
        $coverageReport = "$testResultsDir\CoverageReport"
        reportgenerator "-reports:$($coverageFiles[0].FullName)" "-targetdir:$coverageReport" "-reporttypes:Html"

        Write-Host "✅ Coverage report generated at: $coverageReport\index.html" -ForegroundColor Green
    } else {
        Write-Host "⚠️ No coverage files found" -ForegroundColor Yellow
    }
}

# Watch mode
if ($Watch) {
    Write-Host "`n👀 Entering watch mode... Press Ctrl+C to exit" -ForegroundColor Cyan
    dotnet watch test $solutionFile --configuration Debug --verbosity minimal
}

# Final result
if ($overallSuccess) {
    Write-Host "`n🎉 All tests completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n💥 Some tests failed!" -ForegroundColor Red
    exit 1
}
