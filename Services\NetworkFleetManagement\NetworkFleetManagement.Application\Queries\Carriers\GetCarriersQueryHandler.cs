using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.Carriers;

public class GetCarriersQueryHandler : IRequestHandler<GetCarriersQuery, PagedResult<CarrierSummaryDto>>
{
    private readonly ICarrierRepository _carrierRepository;
    private readonly IMapper _mapper;

    public GetCarriersQueryHandler(ICarrierRepository carrierRepository, IMapper mapper)
    {
        _carrierRepository = carrierRepository;
        _mapper = mapper;
    }

    public async Task<PagedResult<CarrierSummaryDto>> Handle(GetCarriersQuery request, CancellationToken cancellationToken)
    {
        var (carriers, totalCount) = await _carrierRepository.GetPagedAsync(
            request.PageNumber,
            request.PageSize,
            request.SearchTerm,
            request.Status,
            cancellationToken);

        var carrierDtos = _mapper.Map<IEnumerable<CarrierSummaryDto>>(carriers);

        return new PagedResult<CarrierSummaryDto>
        {
            Items = carrierDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }
}
