using Xunit;
using FluentAssertions;
using AuditCompliance.Domain.Entities;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Tests.Unit.Domain;

public class ServiceProviderRatingTests
{
    [Fact]
    public void ServiceProviderRating_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var shipperId = Guid.NewGuid();
        var shipperName = "Test Shipper";
        var transportCompanyId = Guid.NewGuid();
        var transportCompanyName = "Test Transport Company";
        var overallRating = RatingScore.FromScale(RatingScale.FourStars);
        var serviceCompletedAt = DateTime.UtcNow.AddDays(-1);

        // Act
        var rating = new ServiceProviderRating(
            shipperId,
            shipperName,
            transportCompanyId,
            transportCompanyName,
            overallRating,
            serviceCompletedAt);

        // Assert
        rating.ShipperId.Should().Be(shipperId);
        rating.ShipperName.Should().Be(shipperName);
        rating.TransportCompanyId.Should().Be(transportCompanyId);
        rating.TransportCompanyName.Should().Be(transportCompanyName);
        rating.OverallRating.Should().Be(overallRating);
        rating.ServiceCompletedAt.Should().Be(serviceCompletedAt);
        rating.Status.Should().Be(RatingStatus.Pending);
        rating.ReviewSubmittedAt.Should().BeNull();
    }

    [Fact]
    public void ServiceProviderRating_AddCategoryRating_ShouldAddRatingSuccessfully()
    {
        // Arrange
        var rating = CreateTestRating();
        var categoryRating = RatingScore.FromScale(RatingScale.FiveStars);
        var comment = "Excellent communication";

        // Act
        rating.AddCategoryRating(RatingCategory.Communication, categoryRating, comment);

        // Assert
        rating.CategoryRatings.Should().HaveCount(1);
        var addedRating = rating.CategoryRatings.First();
        addedRating.Category.Should().Be(RatingCategory.Communication);
        addedRating.Rating.Should().Be(categoryRating);
        addedRating.Comment.Should().Be(comment);
    }

    [Fact]
    public void ServiceProviderRating_AddCategoryRating_DuplicateCategory_ShouldThrowException()
    {
        // Arrange
        var rating = CreateTestRating();
        var categoryRating = RatingScore.FromScale(RatingScale.FiveStars);
        rating.AddCategoryRating(RatingCategory.Communication, categoryRating);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() =>
            rating.AddCategoryRating(RatingCategory.Communication, categoryRating));
    }

    [Fact]
    public void ServiceProviderRating_UpdateCategoryRating_ShouldUpdateSuccessfully()
    {
        // Arrange
        var rating = CreateTestRating();
        var initialRating = RatingScore.FromScale(RatingScale.ThreeStars);
        var updatedRating = RatingScore.FromScale(RatingScale.FiveStars);
        var updatedComment = "Much better service";

        rating.AddCategoryRating(RatingCategory.Communication, initialRating);

        // Act
        rating.UpdateCategoryRating(RatingCategory.Communication, updatedRating, updatedComment);

        // Assert
        var categoryRating = rating.CategoryRatings.First();
        categoryRating.Rating.Should().Be(updatedRating);
        categoryRating.Comment.Should().Be(updatedComment);
    }

    [Fact]
    public void ServiceProviderRating_UpdateCategoryRating_NonExistentCategory_ShouldThrowException()
    {
        // Arrange
        var rating = CreateTestRating();
        var newRating = RatingScore.FromScale(RatingScale.FiveStars);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() =>
            rating.UpdateCategoryRating(RatingCategory.Communication, newRating));
    }

    [Fact]
    public void ServiceProviderRating_SubmitReview_ShouldChangeStatusToActive()
    {
        // Arrange
        var rating = CreateTestRating();

        // Act
        rating.SubmitReview();

        // Assert
        rating.Status.Should().Be(RatingStatus.Active);
        rating.ReviewSubmittedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void ServiceProviderRating_SubmitReview_AlreadySubmitted_ShouldThrowException()
    {
        // Arrange
        var rating = CreateTestRating();
        rating.SubmitReview();

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => rating.SubmitReview());
    }

    [Fact]
    public void ServiceProviderRating_FlagForReview_ShouldChangeStatusToFlagged()
    {
        // Arrange
        var rating = CreateTestRating();
        var reason = "Inappropriate content";

        // Act
        rating.FlagForReview(reason);

        // Assert
        rating.Status.Should().Be(RatingStatus.Flagged);
    }

    [Fact]
    public void ServiceProviderRating_Remove_ShouldChangeStatusToRemoved()
    {
        // Arrange
        var rating = CreateTestRating();
        var reason = "Spam content";

        // Act
        rating.Remove(reason);

        // Assert
        rating.Status.Should().Be(RatingStatus.Removed);
    }

    [Fact]
    public void ServiceProviderRating_ReportIssue_ShouldAddIssueToCollection()
    {
        // Arrange
        var rating = CreateTestRating();
        var issueType = ServiceIssueType.DelayedDelivery;
        var priority = IssuePriority.High;
        var description = "Package was delivered 2 days late";
        var evidence = "Tracking number: ABC123";

        // Act
        rating.ReportIssue(issueType, priority, description, evidence);

        // Assert
        rating.ReportedIssues.Should().HaveCount(1);
        var issue = rating.ReportedIssues.First();
        issue.IssueType.Should().Be(issueType);
        issue.Priority.Should().Be(priority);
        issue.Description.Should().Be(description);
        issue.Evidence.Should().Be(evidence);
        issue.Status.Should().Be(IssueResolutionStatus.Reported);
    }

    [Fact]
    public void ServiceProviderRating_IsPositiveRating_WithHighRating_ShouldReturnTrue()
    {
        // Arrange
        var rating = CreateTestRating(RatingScale.FourStars);

        // Act & Assert
        rating.IsPositiveRating().Should().BeTrue();
    }

    [Fact]
    public void ServiceProviderRating_IsPositiveRating_WithLowRating_ShouldReturnFalse()
    {
        // Arrange
        var rating = CreateTestRating(RatingScale.TwoStars);

        // Act & Assert
        rating.IsPositiveRating().Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void ServiceProviderRating_Creation_WithInvalidShipperName_ShouldThrowException(string shipperName)
    {
        // Arrange & Act & Assert
        Assert.Throws<ArgumentException>(() => new ServiceProviderRating(
            Guid.NewGuid(),
            shipperName,
            Guid.NewGuid(),
            "Test Company",
            RatingScore.FromScale(RatingScale.FourStars),
            DateTime.UtcNow));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public void ServiceProviderRating_Creation_WithInvalidTransportCompanyName_ShouldThrowException(string companyName)
    {
        // Arrange & Act & Assert
        Assert.Throws<ArgumentException>(() => new ServiceProviderRating(
            Guid.NewGuid(),
            "Test Shipper",
            Guid.NewGuid(),
            companyName,
            RatingScore.FromScale(RatingScale.FourStars),
            DateTime.UtcNow));
    }

    private ServiceProviderRating CreateTestRating(RatingScale scale = RatingScale.FourStars)
    {
        return new ServiceProviderRating(
            Guid.NewGuid(),
            "Test Shipper",
            Guid.NewGuid(),
            "Test Transport Company",
            RatingScore.FromScale(scale),
            DateTime.UtcNow.AddDays(-1));
    }
}
