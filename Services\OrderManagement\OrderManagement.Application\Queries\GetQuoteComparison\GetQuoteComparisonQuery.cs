using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Queries.GetQuoteComparison;

public class GetQuoteComparisonQuery : IRequest<QuoteComparisonDto?>
{
    public Guid RfqId { get; set; }
    public Guid RequestingUserId { get; set; }
    public List<string> ComparisonCriteria { get; set; } = new();
    public bool IncludeCounterOffers { get; set; } = true;
    public bool GenerateRecommendation { get; set; } = true;
}
