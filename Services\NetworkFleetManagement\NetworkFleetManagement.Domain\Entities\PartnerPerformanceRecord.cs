using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Domain.Common;

namespace NetworkFleetManagement.Domain.Entities;

/// <summary>
/// Historical record of partner performance metrics for tracking trends and changes over time
/// </summary>
public class PartnerPerformanceRecord : BaseEntity
{
    public Guid PreferredPartnerId { get; private set; }
    public PartnerPerformanceMetrics Metrics { get; private set; }
    public DateTime RecordedAt { get; private set; }
    public string? Notes { get; private set; }
    public string? RecordedBy { get; private set; } // User or system that recorded this
    public PerformanceRecordType RecordType { get; private set; }
    
    // Context information
    public string? TriggerEvent { get; private set; } // What triggered this performance update
    public Guid? RelatedEntityId { get; private set; } // Related order, trip, etc.
    public string? RelatedEntityType { get; private set; }
    
    // Change tracking
    public PartnerPerformanceMetrics? PreviousMetrics { get; private set; }
    public List<string> ChangedFields { get; private set; } = new();
    public decimal? PerformanceChange { get; private set; } // Overall performance change from previous record
    
    // Navigation properties
    public PreferredPartner PreferredPartner { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private PartnerPerformanceRecord() 
    {
        Metrics = new PartnerPerformanceMetrics();
    }

    public PartnerPerformanceRecord(
        Guid preferredPartnerId,
        PartnerPerformanceMetrics metrics,
        DateTime recordedAt,
        PerformanceRecordType recordType = PerformanceRecordType.Scheduled,
        string? notes = null,
        string? recordedBy = null,
        string? triggerEvent = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null,
        PartnerPerformanceMetrics? previousMetrics = null)
    {
        PreferredPartnerId = preferredPartnerId;
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        RecordedAt = recordedAt;
        RecordType = recordType;
        Notes = notes;
        RecordedBy = recordedBy;
        TriggerEvent = triggerEvent;
        RelatedEntityId = relatedEntityId;
        RelatedEntityType = relatedEntityType;
        PreviousMetrics = previousMetrics;
        
        if (previousMetrics != null)
        {
            CalculateChanges(previousMetrics, metrics);
        }
    }

    // Properties for analysis
    public bool IsSignificantChange => Math.Abs(PerformanceChange ?? 0) >= 0.5m;
    public bool IsImprovement => (PerformanceChange ?? 0) > 0;
    public bool IsDegradation => (PerformanceChange ?? 0) < 0;
    public TimeSpan TimeSinceLastRecord => DateTime.UtcNow - RecordedAt;

    public void AddNote(string note, string? addedBy = null)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        var noteWithTimestamp = $"[{timestamp}] {addedBy ?? "System"}: {note}";
        
        Notes = string.IsNullOrEmpty(Notes) 
            ? noteWithTimestamp 
            : $"{Notes}\n{noteWithTimestamp}";
        
        SetUpdatedAt();
    }

    public void UpdateContext(
        string? triggerEvent = null,
        Guid? relatedEntityId = null,
        string? relatedEntityType = null)
    {
        TriggerEvent = triggerEvent ?? TriggerEvent;
        RelatedEntityId = relatedEntityId ?? RelatedEntityId;
        RelatedEntityType = relatedEntityType ?? RelatedEntityType;
        SetUpdatedAt();
    }

    private void CalculateChanges(PartnerPerformanceMetrics previous, PartnerPerformanceMetrics current)
    {
        ChangedFields = new List<string>();
        
        // Track which fields changed
        if (Math.Abs(previous.OverallRating - current.OverallRating) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.OverallRating));
        
        if (Math.Abs(previous.OnTimePerformance - current.OnTimePerformance) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.OnTimePerformance));
        
        if (Math.Abs(previous.QualityRating - current.QualityRating) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.QualityRating));
        
        if (Math.Abs(previous.CommunicationRating - current.CommunicationRating) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.CommunicationRating));
        
        if (Math.Abs(previous.CostEffectiveness - current.CostEffectiveness) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.CostEffectiveness));
        
        if (Math.Abs(previous.ReliabilityScore - current.ReliabilityScore) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.ReliabilityScore));
        
        if (previous.TotalCollaborations != current.TotalCollaborations)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.TotalCollaborations));
        
        if (previous.SuccessfulCollaborations != current.SuccessfulCollaborations)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.SuccessfulCollaborations));
        
        if (previous.FailedCollaborations != current.FailedCollaborations)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.FailedCollaborations));
        
        if (Math.Abs(previous.TotalRevenue - current.TotalRevenue) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.TotalRevenue));
        
        if (Math.Abs(previous.PaymentTimeliness - current.PaymentTimeliness) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.PaymentTimeliness));
        
        if (Math.Abs(previous.ServiceCompletionRate - current.ServiceCompletionRate) > 0.01m)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.ServiceCompletionRate));
        
        if (previous.TotalIssues != current.TotalIssues)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.TotalIssues));
        
        if (previous.ResolvedIssues != current.ResolvedIssues)
            ChangedFields.Add(nameof(PartnerPerformanceMetrics.ResolvedIssues));

        // Calculate overall performance change
        PerformanceChange = current.OverallRating - previous.OverallRating;
    }

    public PerformanceChangeAnalysis GetChangeAnalysis()
    {
        if (PreviousMetrics == null)
        {
            return new PerformanceChangeAnalysis
            {
                HasPreviousData = false,
                OverallChange = 0,
                ChangedFieldsCount = 0,
                IsSignificantChange = false,
                ChangeDirection = PerformanceChangeDirection.NoChange,
                ChangeSummary = "Initial performance record"
            };
        }

        var changeDirection = PerformanceChangeDirection.NoChange;
        if (PerformanceChange > 0.1m)
            changeDirection = PerformanceChangeDirection.Improvement;
        else if (PerformanceChange < -0.1m)
            changeDirection = PerformanceChangeDirection.Decline;

        var changeSummary = GenerateChangeSummary();

        return new PerformanceChangeAnalysis
        {
            HasPreviousData = true,
            OverallChange = PerformanceChange ?? 0,
            ChangedFieldsCount = ChangedFields.Count,
            IsSignificantChange = IsSignificantChange,
            ChangeDirection = changeDirection,
            ChangeSummary = changeSummary,
            ChangedFields = ChangedFields.ToList()
        };
    }

    private string GenerateChangeSummary()
    {
        if (!ChangedFields.Any())
            return "No significant changes in performance metrics";

        var improvements = new List<string>();
        var declines = new List<string>();

        if (PreviousMetrics == null)
            return "Initial performance record established";

        // Analyze specific changes
        if (ChangedFields.Contains(nameof(PartnerPerformanceMetrics.OverallRating)))
        {
            var change = Metrics.OverallRating - PreviousMetrics.OverallRating;
            if (change > 0.1m)
                improvements.Add($"Overall rating improved by {change:F2}");
            else if (change < -0.1m)
                declines.Add($"Overall rating declined by {Math.Abs(change):F2}");
        }

        if (ChangedFields.Contains(nameof(PartnerPerformanceMetrics.OnTimePerformance)))
        {
            var change = Metrics.OnTimePerformance - PreviousMetrics.OnTimePerformance;
            if (change > 1m)
                improvements.Add($"On-time performance improved by {change:F1}%");
            else if (change < -1m)
                declines.Add($"On-time performance declined by {Math.Abs(change):F1}%");
        }

        if (ChangedFields.Contains(nameof(PartnerPerformanceMetrics.SuccessfulCollaborations)))
        {
            var change = Metrics.SuccessfulCollaborations - PreviousMetrics.SuccessfulCollaborations;
            if (change > 0)
                improvements.Add($"{change} additional successful collaborations");
        }

        if (ChangedFields.Contains(nameof(PartnerPerformanceMetrics.FailedCollaborations)))
        {
            var change = Metrics.FailedCollaborations - PreviousMetrics.FailedCollaborations;
            if (change > 0)
                declines.Add($"{change} additional failed collaborations");
        }

        var summary = new List<string>();
        if (improvements.Any())
            summary.Add($"Improvements: {string.Join(", ", improvements)}");
        if (declines.Any())
            summary.Add($"Concerns: {string.Join(", ", declines)}");

        return summary.Any() ? string.Join(". ", summary) : "Minor performance adjustments";
    }
}

// Supporting types
public enum PerformanceRecordType
{
    Scheduled = 1,      // Regular scheduled update
    EventTriggered = 2, // Triggered by specific event (order completion, issue resolution, etc.)
    Manual = 3,         // Manually recorded by user
    SystemCalculated = 4 // Automatically calculated by system
}

public enum PerformanceChangeDirection
{
    Decline = -1,
    NoChange = 0,
    Improvement = 1
}

public class PerformanceChangeAnalysis
{
    public bool HasPreviousData { get; set; }
    public decimal OverallChange { get; set; }
    public int ChangedFieldsCount { get; set; }
    public bool IsSignificantChange { get; set; }
    public PerformanceChangeDirection ChangeDirection { get; set; }
    public string ChangeSummary { get; set; } = string.Empty;
    public List<string> ChangedFields { get; set; } = new();
}
