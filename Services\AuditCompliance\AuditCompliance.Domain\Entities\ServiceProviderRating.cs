using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Service provider rating entity for shipper feedback system
/// </summary>
public class ServiceProviderRating : BaseEntity
{
    public Guid ShipperId { get; private set; }
    public string ShipperName { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public string TransportCompanyName { get; private set; }
    public Guid? OrderId { get; private set; }
    public string? OrderNumber { get; private set; }
    public Guid? TripId { get; private set; }
    public string? TripNumber { get; private set; }
    public RatingScore OverallRating { get; private set; }
    public RatingStatus Status { get; private set; }
    public string? ReviewTitle { get; private set; }
    public string? ReviewComment { get; private set; }
    public bool IsAnonymous { get; private set; }
    public DateTime ServiceCompletedAt { get; private set; }
    public DateTime? ReviewSubmittedAt { get; private set; }

    private readonly List<CategoryRating> _categoryRatings = new();
    public IReadOnlyList<CategoryRating> CategoryRatings => _categoryRatings.AsReadOnly();

    private readonly List<ServiceIssue> _reportedIssues = new();
    public IReadOnlyList<ServiceIssue> ReportedIssues => _reportedIssues.AsReadOnly();

    private ServiceProviderRating() 
    {
        ShipperName = string.Empty;
        TransportCompanyName = string.Empty;
        OverallRating = RatingScore.FromScale(RatingScale.ThreeStars);
    }

    public ServiceProviderRating(
        Guid shipperId,
        string shipperName,
        Guid transportCompanyId,
        string transportCompanyName,
        RatingScore overallRating,
        DateTime serviceCompletedAt,
        Guid? orderId = null,
        string? orderNumber = null,
        Guid? tripId = null,
        string? tripNumber = null,
        string? reviewTitle = null,
        string? reviewComment = null,
        bool isAnonymous = false)
    {
        if (string.IsNullOrWhiteSpace(shipperName))
            throw new ArgumentException("Shipper name cannot be empty", nameof(shipperName));
        if (string.IsNullOrWhiteSpace(transportCompanyName))
            throw new ArgumentException("Transport company name cannot be empty", nameof(transportCompanyName));

        ShipperId = shipperId;
        ShipperName = shipperName;
        TransportCompanyId = transportCompanyId;
        TransportCompanyName = transportCompanyName;
        OrderId = orderId;
        OrderNumber = orderNumber;
        TripId = tripId;
        TripNumber = tripNumber;
        OverallRating = overallRating ?? throw new ArgumentNullException(nameof(overallRating));
        Status = RatingStatus.Pending;
        ReviewTitle = reviewTitle;
        ReviewComment = reviewComment;
        IsAnonymous = isAnonymous;
        ServiceCompletedAt = serviceCompletedAt;

        AddDomainEvent(new ServiceProviderRatingCreatedEvent(this));
    }

    public void AddCategoryRating(RatingCategory category, RatingScore rating, string? comment = null)
    {
        if (_categoryRatings.Any(cr => cr.Category == category))
            throw new InvalidOperationException($"Rating for category {category} already exists");

        var categoryRating = new CategoryRating(Id, category, rating, comment);
        _categoryRatings.Add(categoryRating);
        
        RecalculateOverallRating();
        SetUpdatedAt();
    }

    public void UpdateCategoryRating(RatingCategory category, RatingScore newRating, string? comment = null)
    {
        var existingRating = _categoryRatings.FirstOrDefault(cr => cr.Category == category);
        if (existingRating == null)
            throw new InvalidOperationException($"No rating found for category {category}");

        existingRating.UpdateRating(newRating, comment);
        RecalculateOverallRating();
        SetUpdatedAt();
    }

    public void SubmitReview()
    {
        if (Status != RatingStatus.Pending)
            throw new InvalidOperationException("Can only submit pending ratings");

        Status = RatingStatus.Active;
        ReviewSubmittedAt = DateTime.UtcNow;
        SetUpdatedAt();

        AddDomainEvent(new ServiceProviderRatingSubmittedEvent(this));
    }

    public void FlagForReview(string reason)
    {
        Status = RatingStatus.Flagged;
        SetUpdatedAt();

        AddDomainEvent(new ServiceProviderRatingFlaggedEvent(this, reason));
    }

    public void Remove(string reason)
    {
        Status = RatingStatus.Removed;
        SetUpdatedAt();

        AddDomainEvent(new ServiceProviderRatingRemovedEvent(this, reason));
    }

    public void ReportIssue(ServiceIssueType issueType, IssuePriority priority, string description, string? evidence = null)
    {
        var issue = new ServiceIssue(Id, issueType, priority, description, evidence);
        _reportedIssues.Add(issue);
        SetUpdatedAt();

        AddDomainEvent(new ServiceIssueReportedEvent(this, issue));
    }

    private void RecalculateOverallRating()
    {
        if (_categoryRatings.Any())
        {
            var categoryScores = _categoryRatings.Select(cr => cr.Rating);
            OverallRating = RatingScore.CalculateAverage(categoryScores);
        }
    }

    public decimal GetAverageRating()
    {
        return OverallRating.NumericValue;
    }

    public bool IsPositiveRating()
    {
        return OverallRating.IsPositive;
    }

    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        // Implementation would be in base class or through event dispatcher
    }
}
