using Identity.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Identity.Infrastructure.Persistence;

namespace Identity.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static async Task<IServiceProvider> SeedDataAsync(this IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<SeedData>>();

            try
            {
                logger.LogInformation("Starting Identity service data seeding...");

                // Ensure database is created
                var context = services.GetRequiredService<Identity.Infrastructure.Persistence.IdentityDbContext>();
                await context.Database.MigrateAsync();

                // Seed data
                var seedData = services.GetRequiredService<SeedData>();
                await seedData.SeedAsync();

                logger.LogInformation("Identity service data seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding Identity service data");
                throw;
            }

            return serviceProvider;
        }

        public static IServiceCollection AddDataSeeding(this IServiceCollection services)
        {
            services.AddScoped<SeedData>();
            return services;
        }
    }
}
