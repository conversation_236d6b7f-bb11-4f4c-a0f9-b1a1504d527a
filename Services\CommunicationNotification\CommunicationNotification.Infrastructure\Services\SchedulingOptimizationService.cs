using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Services;

/// <summary>
/// Service for optimizing message scheduling using machine learning algorithms
/// </summary>
public class SchedulingOptimizationService : ISchedulingOptimizationService
{
    private readonly IUserBehaviorAnalyticsService _behaviorAnalyticsService;
    private readonly ILogger<SchedulingOptimizationService> _logger;

    public SchedulingOptimizationService(
        IUserBehaviorAnalyticsService behaviorAnalyticsService,
        ILogger<SchedulingOptimizationService> logger)
    {
        _behaviorAnalyticsService = behaviorAnalyticsService;
        _logger = logger;
    }

    public async Task<List<OptimizedDelivery>> OptimizeDeliveryTimesAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Optimizing delivery times for schedule {ScheduleId} with {UserCount} users",
                schedule.Id, targetUserIds.Count);

            var optimizedDeliveries = new List<OptimizedDelivery>();

            foreach (var userId in targetUserIds)
            {
                try
                {
                    var userTimeZone = await GetUserTimeZoneAsync(userId, cancellationToken);
                    var baseTime = schedule.NextExecutionAt ?? DateTime.UtcNow;

                    // Get optimal delivery time for user
                    var optimalTime = await _behaviorAnalyticsService.GetOptimalSendTimeAsync(
                        userId, baseTime, userTimeZone, cancellationToken);

                    // Get preferred channel
                    var preferredChannel = await _behaviorAnalyticsService.GetPreferredChannelAsync(
                        userId, schedule.MessageType, cancellationToken);

                    // Predict response probability
                    var responseProbability = await _behaviorAnalyticsService.PredictResponseProbabilityAsync(
                        userId, schedule.MessageType, preferredChannel ?? NotificationChannel.WhatsApp,
                        optimalTime ?? baseTime, cancellationToken);

                    // Apply delivery window constraints
                    var constrainedTime = ApplyDeliveryWindowConstraints(
                        optimalTime ?? baseTime, schedule.DeliveryWindow, userTimeZone);

                    var optimizedDelivery = new OptimizedDelivery
                    {
                        UserId = userId,
                        OptimalTime = constrainedTime,
                        RecommendedChannel = preferredChannel ?? schedule.PreferredChannel ?? NotificationChannel.WhatsApp,
                        ConfidenceScore = responseProbability,
                        Reasoning = GenerateOptimizationReasoning(optimalTime, preferredChannel, responseProbability),
                        Metadata = new Dictionary<string, object>
                        {
                            ["originalTime"] = baseTime,
                            ["userTimeZone"] = userTimeZone,
                            ["optimizationApplied"] = optimalTime.HasValue,
                            ["channelOptimized"] = preferredChannel.HasValue
                        }
                    };

                    optimizedDeliveries.Add(optimizedDelivery);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error optimizing delivery for user {UserId}, using defaults", userId);
                    
                    // Add default delivery for this user
                    optimizedDeliveries.Add(new OptimizedDelivery
                    {
                        UserId = userId,
                        OptimalTime = schedule.NextExecutionAt ?? DateTime.UtcNow,
                        RecommendedChannel = schedule.PreferredChannel ?? NotificationChannel.WhatsApp,
                        ConfidenceScore = 0.5m,
                        Reasoning = "Default delivery due to optimization error"
                    });
                }
            }

            _logger.LogInformation("Optimized delivery times for {Count} users", optimizedDeliveries.Count);
            return optimizedDeliveries;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing delivery times for schedule {ScheduleId}", schedule.Id);
            throw;
        }
    }

    public async Task<Dictionary<string, NotificationChannel>> OptimizeChannelSelectionAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Optimizing channel selection for schedule {ScheduleId}", schedule.Id);

            var channelOptimizations = new Dictionary<string, NotificationChannel>();

            foreach (var userId in targetUserIds)
            {
                try
                {
                    var preferredChannel = await _behaviorAnalyticsService.GetPreferredChannelAsync(
                        userId, schedule.MessageType, cancellationToken);

                    if (preferredChannel.HasValue)
                    {
                        channelOptimizations[userId] = preferredChannel.Value;
                    }
                    else
                    {
                        // Use schedule default or apply message type optimization
                        channelOptimizations[userId] = OptimizeChannelForMessageType(
                            schedule.PreferredChannel ?? NotificationChannel.WhatsApp, 
                            schedule.MessageType);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error optimizing channel for user {UserId}", userId);
                    channelOptimizations[userId] = schedule.PreferredChannel ?? NotificationChannel.WhatsApp;
                }
            }

            _logger.LogInformation("Optimized channels for {Count} users", channelOptimizations.Count);
            return channelOptimizations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing channel selection for schedule {ScheduleId}", schedule.Id);
            throw;
        }
    }

    public async Task<FrequencyOptimizationResult> OptimizeFrequencyAsync(
        MessageSchedule schedule,
        List<string> targetUserIds,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Optimizing frequency for schedule {ScheduleId}", schedule.Id);

            // Analyze user engagement patterns to determine optimal frequency
            var engagementScores = new List<decimal>();
            var userSpecificLimits = new Dictionary<string, int>();

            foreach (var userId in targetUserIds.Take(100)) // Sample for performance
            {
                try
                {
                    var pattern = await _behaviorAnalyticsService.GetUserEngagementPatternAsync(
                        userId, TimeSpan.FromDays(30), cancellationToken);

                    engagementScores.Add(pattern.EngagementScore);

                    // Calculate user-specific limits based on engagement
                    var dailyLimit = CalculateDailyLimit(pattern.EngagementScore, schedule.MessageType);
                    var weeklyLimit = dailyLimit * 5; // Assume 5 active days per week

                    userSpecificLimits[userId] = dailyLimit;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error analyzing user {UserId} for frequency optimization", userId);
                }
            }

            // Calculate overall recommendations
            var averageEngagement = engagementScores.Any() ? engagementScores.Average() : 75m;
            var recommendedInterval = CalculateOptimalInterval(averageEngagement, schedule.MessageType);
            var maxDailyMessages = CalculateMaxDailyMessages(averageEngagement, schedule.MessageType);
            var maxWeeklyMessages = maxDailyMessages * 5;

            var result = new FrequencyOptimizationResult
            {
                RecommendedInterval = recommendedInterval,
                MaxDailyMessages = maxDailyMessages,
                MaxWeeklyMessages = maxWeeklyMessages,
                UserSpecificLimits = userSpecificLimits,
                OptimizationScore = CalculateOptimizationScore(averageEngagement),
                Reasoning = GenerateFrequencyReasoning(averageEngagement, schedule.MessageType, recommendedInterval)
            };

            _logger.LogInformation("Frequency optimization completed for schedule {ScheduleId}", schedule.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing frequency for schedule {ScheduleId}", schedule.Id);
            throw;
        }
    }

    public async Task<List<ScheduleVariant>> GenerateABTestVariantsAsync(
        MessageSchedule schedule,
        int variantCount = 2,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Generating {VariantCount} A/B test variants for schedule {ScheduleId}",
                variantCount, schedule.Id);

            var variants = new List<ScheduleVariant>();
            var trafficAllocation = 1.0m / variantCount;

            for (int i = 0; i < variantCount; i++)
            {
                var variant = new ScheduleVariant
                {
                    Name = $"Variant {i + 1}",
                    Description = GenerateVariantDescription(i, schedule),
                    TrafficAllocation = trafficAllocation,
                    Parameters = new Dictionary<string, object>
                    {
                        ["variantIndex"] = i,
                        ["scheduleId"] = schedule.Id
                    }
                };

                // Generate different optimization strategies for each variant
                switch (i % 4)
                {
                    case 0: // Control - original settings
                        variant.DeliveryWindow = schedule.DeliveryWindow;
                        variant.OptimizationSettings = schedule.OptimizationSettings;
                        variant.PreferredChannel = schedule.PreferredChannel;
                        break;

                    case 1: // Timing optimized
                        variant.DeliveryWindow = DeliveryWindow.BusinessHours();
                        variant.OptimizationSettings = OptimizationSettings.Aggressive();
                        variant.PreferredChannel = schedule.PreferredChannel;
                        break;

                    case 2: // Channel optimized
                        variant.DeliveryWindow = schedule.DeliveryWindow;
                        variant.OptimizationSettings = schedule.OptimizationSettings;
                        variant.PreferredChannel = GetOptimalChannelForMessageType(schedule.MessageType);
                        break;

                    case 3: // Fully optimized
                        variant.DeliveryWindow = DeliveryWindow.BusinessHours();
                        variant.OptimizationSettings = OptimizationSettings.Aggressive();
                        variant.PreferredChannel = GetOptimalChannelForMessageType(schedule.MessageType);
                        break;
                }

                variants.Add(variant);
            }

            _logger.LogInformation("Generated {Count} A/B test variants", variants.Count);
            return variants;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating A/B test variants for schedule {ScheduleId}", schedule.Id);
            throw;
        }
    }

    public async Task<SchedulePerformanceAnalysis> AnalyzeSchedulePerformanceAsync(
        Guid scheduleId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing performance for schedule {ScheduleId}", scheduleId);

            // Simulate performance analysis
            // In a real implementation, this would query actual delivery and engagement data
            var random = new Random(scheduleId.GetHashCode());

            var analysis = new SchedulePerformanceAnalysis
            {
                ScheduleId = scheduleId,
                DeliveryRate = (decimal)(random.NextDouble() * 0.15 + 0.85), // 85-100%
                OpenRate = (decimal)(random.NextDouble() * 0.3 + 0.4), // 40-70%
                ClickRate = (decimal)(random.NextDouble() * 0.2 + 0.1), // 10-30%
                ConversionRate = (decimal)(random.NextDouble() * 0.1 + 0.05), // 5-15%
                UnsubscribeRate = (decimal)(random.NextDouble() * 0.02 + 0.001), // 0.1-2.1%
                BounceRate = (decimal)(random.NextDouble() * 0.05 + 0.01), // 1-6%
                ChannelPerformance = GenerateChannelPerformance(random),
                TimeSlotPerformance = GenerateTimeSlotPerformance(random),
                SegmentPerformance = GenerateSegmentPerformance(random),
                Trends = GeneratePerformanceTrends(random, startDate, endDate)
            };

            _logger.LogInformation("Performance analysis completed for schedule {ScheduleId}", scheduleId);
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing performance for schedule {ScheduleId}", scheduleId);
            throw;
        }
    }

    // Private helper methods
    private async Task<string> GetUserTimeZoneAsync(string userId, CancellationToken cancellationToken)
    {
        // In a real implementation, this would query user profile data
        // For now, return a simulated timezone
        var timeZones = new[] { "UTC", "America/New_York", "Europe/London", "Asia/Kolkata", "Asia/Tokyo" };
        var random = new Random(userId.GetHashCode());
        return timeZones[random.Next(timeZones.Length)];
    }

    private DateTime ApplyDeliveryWindowConstraints(DateTime proposedTime, DeliveryWindow deliveryWindow, string timeZone)
    {
        if (deliveryWindow.IsWithinWindow(proposedTime, timeZone))
        {
            return proposedTime;
        }

        return deliveryWindow.GetNextValidTime(proposedTime, timeZone);
    }

    private string GenerateOptimizationReasoning(DateTime? optimalTime, NotificationChannel? preferredChannel, decimal responseProbability)
    {
        var reasons = new List<string>();

        if (optimalTime.HasValue)
        {
            reasons.Add("Timing optimized based on user activity patterns");
        }

        if (preferredChannel.HasValue)
        {
            reasons.Add($"Channel optimized to {preferredChannel.Value} based on user preferences");
        }

        if (responseProbability > 0.7m)
        {
            reasons.Add("High response probability predicted");
        }
        else if (responseProbability < 0.3m)
        {
            reasons.Add("Low response probability - consider alternative approach");
        }

        return string.Join("; ", reasons);
    }

    private NotificationChannel OptimizeChannelForMessageType(NotificationChannel defaultChannel, MessageType messageType)
    {
        return messageType switch
        {
            MessageType.TripConfirmation => NotificationChannel.WhatsApp,
            MessageType.TripUpdate => NotificationChannel.PushNotification,
            MessageType.PaymentReminder => NotificationChannel.SMS,
            MessageType.Promotional => NotificationChannel.Email,
            MessageType.SystemAlert => NotificationChannel.PushNotification,
            _ => defaultChannel
        };
    }

    private int CalculateDailyLimit(decimal engagementScore, MessageType messageType)
    {
        var baseLimit = messageType switch
        {
            MessageType.TripConfirmation => 10, // High priority
            MessageType.TripUpdate => 8,
            MessageType.PaymentReminder => 5,
            MessageType.Promotional => 2, // Low priority
            MessageType.SystemAlert => 15, // Critical
            _ => 5
        };

        // Adjust based on engagement score
        var multiplier = engagementScore / 100m;
        return Math.Max(1, (int)(baseLimit * multiplier));
    }

    private TimeSpan CalculateOptimalInterval(decimal averageEngagement, MessageType messageType)
    {
        var baseInterval = messageType switch
        {
            MessageType.TripConfirmation => TimeSpan.FromHours(1),
            MessageType.TripUpdate => TimeSpan.FromHours(2),
            MessageType.PaymentReminder => TimeSpan.FromHours(6),
            MessageType.Promotional => TimeSpan.FromDays(1),
            MessageType.SystemAlert => TimeSpan.FromMinutes(30),
            _ => TimeSpan.FromHours(4)
        };

        // Adjust based on engagement
        if (averageEngagement > 80)
        {
            return TimeSpan.FromTicks((long)(baseInterval.Ticks * 0.8)); // 20% faster
        }
        else if (averageEngagement < 50)
        {
            return TimeSpan.FromTicks((long)(baseInterval.Ticks * 1.5)); // 50% slower
        }

        return baseInterval;
    }

    private int CalculateMaxDailyMessages(decimal averageEngagement, MessageType messageType)
    {
        var baseMax = messageType switch
        {
            MessageType.TripConfirmation => 20,
            MessageType.TripUpdate => 15,
            MessageType.PaymentReminder => 10,
            MessageType.Promotional => 3,
            MessageType.SystemAlert => 25,
            _ => 10
        };

        return Math.Max(1, (int)(baseMax * (averageEngagement / 100m)));
    }

    private decimal CalculateOptimizationScore(decimal averageEngagement)
    {
        // Score based on engagement level and optimization potential
        return Math.Min(100m, averageEngagement + 15m);
    }

    private string GenerateFrequencyReasoning(decimal averageEngagement, MessageType messageType, TimeSpan interval)
    {
        return $"Based on {averageEngagement:F1}% average engagement for {messageType} messages, " +
               $"recommended interval of {interval.TotalHours:F1} hours optimizes user experience and response rates.";
    }

    private string GenerateVariantDescription(int variantIndex, MessageSchedule schedule)
    {
        return variantIndex switch
        {
            0 => "Control variant with original settings",
            1 => "Timing-optimized variant with business hours delivery",
            2 => "Channel-optimized variant with preferred channels",
            3 => "Fully-optimized variant with timing and channel optimization",
            _ => $"Variant {variantIndex + 1} with experimental settings"
        };
    }

    private NotificationChannel GetOptimalChannelForMessageType(MessageType messageType)
    {
        return messageType switch
        {
            MessageType.TripConfirmation => NotificationChannel.WhatsApp,
            MessageType.TripUpdate => NotificationChannel.PushNotification,
            MessageType.PaymentReminder => NotificationChannel.SMS,
            MessageType.Promotional => NotificationChannel.Email,
            MessageType.SystemAlert => NotificationChannel.PushNotification,
            _ => NotificationChannel.WhatsApp
        };
    }

    private Dictionary<string, decimal> GenerateChannelPerformance(Random random)
    {
        return new Dictionary<string, decimal>
        {
            ["WhatsApp"] = (decimal)(random.NextDouble() * 0.2 + 0.7), // 70-90%
            ["SMS"] = (decimal)(random.NextDouble() * 0.15 + 0.6), // 60-75%
            ["Email"] = (decimal)(random.NextDouble() * 0.25 + 0.4), // 40-65%
            ["PushNotification"] = (decimal)(random.NextDouble() * 0.3 + 0.5), // 50-80%
            ["RealTimeChat"] = (decimal)(random.NextDouble() * 0.2 + 0.3) // 30-50%
        };
    }

    private Dictionary<string, decimal> GenerateTimeSlotPerformance(Random random)
    {
        return new Dictionary<string, decimal>
        {
            ["Morning (6-12)"] = (decimal)(random.NextDouble() * 0.2 + 0.6), // 60-80%
            ["Afternoon (12-18)"] = (decimal)(random.NextDouble() * 0.15 + 0.7), // 70-85%
            ["Evening (18-22)"] = (decimal)(random.NextDouble() * 0.25 + 0.65), // 65-90%
            ["Night (22-6)"] = (decimal)(random.NextDouble() * 0.15 + 0.2) // 20-35%
        };
    }

    private Dictionary<string, decimal> GenerateSegmentPerformance(Random random)
    {
        return new Dictionary<string, decimal>
        {
            ["Premium Users"] = (decimal)(random.NextDouble() * 0.15 + 0.8), // 80-95%
            ["Regular Users"] = (decimal)(random.NextDouble() * 0.2 + 0.6), // 60-80%
            ["New Users"] = (decimal)(random.NextDouble() * 0.25 + 0.4), // 40-65%
            ["Inactive Users"] = (decimal)(random.NextDouble() * 0.2 + 0.2) // 20-40%
        };
    }

    private List<PerformanceTrend> GeneratePerformanceTrends(Random random, DateTime? startDate, DateTime? endDate)
    {
        var start = startDate ?? DateTime.UtcNow.AddDays(-30);
        var end = endDate ?? DateTime.UtcNow;

        return new List<PerformanceTrend>
        {
            new PerformanceTrend
            {
                Metric = "Open Rate",
                CurrentValue = (decimal)(random.NextDouble() * 0.3 + 0.4),
                PreviousValue = (decimal)(random.NextDouble() * 0.3 + 0.35),
                ChangePercentage = (decimal)(random.NextDouble() * 20 - 5), // -5% to +15%
                Trend = "improving",
                PeriodStart = start,
                PeriodEnd = end
            },
            new PerformanceTrend
            {
                Metric = "Click Rate",
                CurrentValue = (decimal)(random.NextDouble() * 0.2 + 0.1),
                PreviousValue = (decimal)(random.NextDouble() * 0.2 + 0.08),
                ChangePercentage = (decimal)(random.NextDouble() * 15 - 3), // -3% to +12%
                Trend = "stable",
                PeriodStart = start,
                PeriodEnd = end
            }
        };
    }
}
