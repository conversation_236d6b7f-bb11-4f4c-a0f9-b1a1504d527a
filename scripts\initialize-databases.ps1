# TLI Microservices - Complete Database Initialization Script
# This script sets up PostgreSQL databases, runs migrations, and seeds initial data

param(
    [string]$Environment = "Development",
    [string]$PostgreSQLHost = "localhost",
    [int]$PostgreSQLPort = 5432,
    [string]$PostgreSQLUser = "postgres",
    [string]$PostgreSQLPassword = "",
    [switch]$SkipDatabaseCreation = $false,
    [switch]$SkipMigrations = $false,
    [switch]$SeedData = $true,
    [switch]$Force = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 TLI Microservices - Complete Database Initialization" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor White
Write-Host "PostgreSQL Host: $PostgreSQLHost:$PostgreSQLPort" -ForegroundColor White
Write-Host ""

# Function to test PostgreSQL connection
function Test-PostgreSQLConnection {
    param(
        [string]$Host,
        [int]$Port,
        [string]$Database = "postgres",
        [string]$User,
        [string]$Password
    )
    
    try {
        $connectionString = "Host=$Host;Port=$Port;Database=$Database;User Id=$User"
        if ($Password) {
            $connectionString += ";Password=$Password"
        }
        
        # Use psql to test connection
        $env:PGPASSWORD = $Password
        $result = & psql -h $Host -p $Port -U $User -d $Database -c "SELECT 1;" 2>&1
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        
        return $LASTEXITCODE -eq 0
    }
    catch {
        return $false
    }
}

# Function to execute SQL script
function Invoke-SQLScript {
    param(
        [string]$ScriptPath,
        [string]$Host,
        [int]$Port,
        [string]$User,
        [string]$Password
    )
    
    Write-Host "📄 Executing SQL script: $ScriptPath" -ForegroundColor Yellow
    
    try {
        $env:PGPASSWORD = $Password
        $result = & psql -h $Host -p $Port -U $User -d postgres -f $ScriptPath 2>&1
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        
        if ($LASTEXITCODE -ne 0) {
            throw "SQL script execution failed: $result"
        }
        
        Write-Host "✅ SQL script executed successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "❌ Failed to execute SQL script: $_"
        return $false
    }
}

# Function to check if database exists
function Test-DatabaseExists {
    param(
        [string]$DatabaseName,
        [string]$Host,
        [int]$Port,
        [string]$User,
        [string]$Password
    )
    
    try {
        $env:PGPASSWORD = $Password
        $result = & psql -h $Host -p $Port -U $User -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='$DatabaseName';" 2>&1
        Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        
        return $result -match "1"
    }
    catch {
        return $false
    }
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check if PostgreSQL client tools are available
try {
    $psqlVersion = & psql --version 2>&1
    Write-Host "✅ PostgreSQL client found: $psqlVersion" -ForegroundColor Green
}
catch {
    Write-Error "❌ PostgreSQL client (psql) not found. Please install PostgreSQL client tools."
    exit 1
}

# Check if .NET CLI is available
try {
    $dotnetVersion = & dotnet --version 2>&1
    Write-Host "✅ .NET CLI found: $dotnetVersion" -ForegroundColor Green
}
catch {
    Write-Error "❌ .NET CLI not found. Please install .NET 8 SDK."
    exit 1
}

# Check if Entity Framework tools are installed
try {
    & dotnet ef --version | Out-Null
    Write-Host "✅ Entity Framework tools found" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  Installing Entity Framework tools..." -ForegroundColor Yellow
    & dotnet tool install --global dotnet-ef
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to install Entity Framework tools"
        exit 1
    }
}

# Test PostgreSQL connection
Write-Host "🔍 Testing PostgreSQL connection..." -ForegroundColor Yellow
if (-not (Test-PostgreSQLConnection -Host $PostgreSQLHost -Port $PostgreSQLPort -User $PostgreSQLUser -Password $PostgreSQLPassword)) {
    Write-Error "❌ Cannot connect to PostgreSQL server at $PostgreSQLHost`:$PostgreSQLPort with user '$PostgreSQLUser'"
    Write-Host "   Please ensure PostgreSQL is running and credentials are correct." -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ PostgreSQL connection successful" -ForegroundColor Green

# Step 1: Create databases
if (-not $SkipDatabaseCreation) {
    Write-Host ""
    Write-Host "📊 Step 1: Creating databases..." -ForegroundColor Cyan
    
    $setupScriptPath = Join-Path $PSScriptRoot "setup-databases.sql"
    if (Test-Path $setupScriptPath) {
        if (Invoke-SQLScript -ScriptPath $setupScriptPath -Host $PostgreSQLHost -Port $PostgreSQLPort -User $PostgreSQLUser -Password $PostgreSQLPassword) {
            Write-Host "✅ Databases created successfully" -ForegroundColor Green
        } else {
            if (-not $Force) {
                Write-Error "❌ Database creation failed. Use -Force to continue."
                exit 1
            }
            Write-Warning "⚠️  Database creation failed, but continuing due to -Force flag"
        }
    } else {
        Write-Warning "⚠️  Database setup script not found at: $setupScriptPath"
    }
} else {
    Write-Host "⏭️  Skipping database creation" -ForegroundColor Yellow
}

# Step 2: Run migrations
if (-not $SkipMigrations) {
    Write-Host ""
    Write-Host "🔄 Step 2: Running Entity Framework migrations..." -ForegroundColor Cyan
    
    $migrationScriptPath = Join-Path $PSScriptRoot "run-migrations.ps1"
    if (Test-Path $migrationScriptPath) {
        $migrationArgs = @(
            "-Environment", $Environment
        )
        
        if ($SeedData) {
            $migrationArgs += "-SeedData"
        }
        
        if ($Force) {
            $migrationArgs += "-Force"
        }
        
        try {
            & $migrationScriptPath @migrationArgs
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Migrations completed successfully" -ForegroundColor Green
            } else {
                throw "Migration script returned exit code $LASTEXITCODE"
            }
        }
        catch {
            if (-not $Force) {
                Write-Error "❌ Migration failed: $_"
                exit 1
            }
            Write-Warning "⚠️  Migration failed, but continuing due to -Force flag: $_"
        }
    } else {
        Write-Warning "⚠️  Migration script not found at: $migrationScriptPath"
    }
} else {
    Write-Host "⏭️  Skipping migrations" -ForegroundColor Yellow
}

# Step 3: Verify database setup
Write-Host ""
Write-Host "🔍 Step 3: Verifying database setup..." -ForegroundColor Cyan

$databases = @(
    "TLI_Identity",
    "TLI_UserManagement",
    "TLI_SubscriptionManagement",
    "TLI_OrderManagement",
    "TLI_TripManagement",
    "TLI_NetworkFleetManagement",
    "TLI_FinancialPayment",
    "TLI_CommunicationNotification",
    "TLI_AnalyticsBI",
    "TLI_DataStorage",
    "TLI_MonitoringObservability",
    "TLI_AuditCompliance",
    "TLI_MobileWorkflow"
)

$verificationResults = @()
foreach ($database in $databases) {
    $exists = Test-DatabaseExists -DatabaseName $database -Host $PostgreSQLHost -Port $PostgreSQLPort -User $PostgreSQLUser -Password $PostgreSQLPassword
    $verificationResults += @{
        Database = $database
        Exists = $exists
    }
    
    if ($exists) {
        Write-Host "✅ $database - OK" -ForegroundColor Green
    } else {
        Write-Host "❌ $database - Missing" -ForegroundColor Red
    }
}

$successCount = ($verificationResults | Where-Object { $_.Exists }).Count
$totalCount = $verificationResults.Count

Write-Host ""
Write-Host "📊 Database Verification Summary:" -ForegroundColor Cyan
Write-Host "   Databases found: $successCount/$totalCount" -ForegroundColor White

if ($successCount -eq $totalCount) {
    Write-Host "🎉 All databases are ready!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some databases are missing. Check the logs above." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start the microservices using: docker-compose up" -ForegroundColor White
Write-Host "2. Or start individual services using: dotnet run" -ForegroundColor White
Write-Host "3. Test the API Gateway at: http://localhost:5000" -ForegroundColor White
Write-Host "4. Check service health endpoints: /health" -ForegroundColor White

Write-Host ""
Write-Host "✅ Database initialization completed!" -ForegroundColor Green
