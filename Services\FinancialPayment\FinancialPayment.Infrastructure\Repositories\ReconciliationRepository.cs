using Microsoft.EntityFrameworkCore;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Infrastructure.Data;

namespace FinancialPayment.Infrastructure.Repositories;

public class ReconciliationRepository : IReconciliationRepository
{
    private readonly FinancialPaymentDbContext _context;

    public ReconciliationRepository(FinancialPaymentDbContext context)
    {
        _context = context;
    }

    public async Task<PaymentReconciliation?> GetByIdAsync(Guid id)
    {
        return await _context.PaymentReconciliations
            .Include(pr => pr.ReconciliationItems)
            .Include(pr => pr.Discrepancies)
            .FirstOrDefaultAsync(pr => pr.Id == id);
    }

    public async Task<PaymentReconciliation?> GetByReconciliationNumberAsync(string reconciliationNumber)
    {
        return await _context.PaymentReconciliations
            .Include(pr => pr.ReconciliationItems)
            .Include(pr => pr.Discrepancies)
            .FirstOrDefaultAsync(pr => pr.ReconciliationNumber == reconciliationNumber);
    }

    public async Task<List<PaymentReconciliation>> GetByPeriodAsync(DateTime fromDate, DateTime toDate, string? paymentGateway = null)
    {
        var query = _context.PaymentReconciliations
            .Include(pr => pr.ReconciliationItems)
            .Include(pr => pr.Discrepancies)
            .Where(pr => pr.ReconciliationDate >= fromDate && pr.ReconciliationDate <= toDate);

        if (!string.IsNullOrEmpty(paymentGateway))
        {
            query = query.Where(pr => pr.PaymentGateway == paymentGateway);
        }

        return await query
            .OrderByDescending(pr => pr.ReconciliationDate)
            .ToListAsync();
    }

    public async Task<List<PaymentReconciliation>> GetByStatusAsync(ReconciliationStatus status)
    {
        return await _context.PaymentReconciliations
            .Include(pr => pr.ReconciliationItems)
            .Include(pr => pr.Discrepancies)
            .Where(pr => pr.Status == status)
            .OrderByDescending(pr => pr.ReconciliationDate)
            .ToListAsync();
    }

    public async Task AddAsync(PaymentReconciliation reconciliation)
    {
        await _context.PaymentReconciliations.AddAsync(reconciliation);
        await _context.SaveChangesAsync();
    }

    public async Task UpdateAsync(PaymentReconciliation reconciliation)
    {
        _context.PaymentReconciliations.Update(reconciliation);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var reconciliation = await _context.PaymentReconciliations.FindAsync(id);
        if (reconciliation != null)
        {
            _context.PaymentReconciliations.Remove(reconciliation);
            await _context.SaveChangesAsync();
        }
    }
}
