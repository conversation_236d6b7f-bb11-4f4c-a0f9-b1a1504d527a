using System;
using System.Threading;
using System.Threading.Tasks;
using Identity.Application.Common.Interfaces;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Identity.Application.Auth.Commands.Logout
{
    public class LogoutCommandHandler : IRequestHandler<LogoutCommand, Unit>
    {
        private readonly ITokenService _tokenService;
        private readonly IUserSessionRepository _sessionRepository;
        private readonly ILogger<LogoutCommandHandler> _logger;

        public LogoutCommandHandler(
            ITokenService tokenService,
            IUserSessionRepository sessionRepository,
            ILogger<LogoutCommandHandler> logger)
        {
            _tokenService = tokenService;
            _sessionRepository = sessionRepository;
            _logger = logger;
        }

        public async Task<Unit> Handle(LogoutCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Revoke refresh token
                await _tokenService.RevokeTokenAsync(request.RefreshToken);

                // Terminate user session
                var session = await _sessionRepository.GetByTokenAsync(request.AccessToken);
                if (session != null)
                {
                    session.Terminate();
                    await _sessionRepository.UpdateAsync(session);
                }

                return Unit.Value;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during logout");
                throw;
            }
        }
    }
}
