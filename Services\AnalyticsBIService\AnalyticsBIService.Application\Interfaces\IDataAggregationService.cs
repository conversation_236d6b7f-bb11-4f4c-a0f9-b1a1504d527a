using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Data aggregation service interface for analytics processing
/// </summary>
public interface IDataAggregationService
{
    /// <summary>
    /// Aggregate metrics for a specific time period
    /// </summary>
    Task<Dictionary<string, object>> AggregateMetricsAsync(
        DateTime startDate,
        DateTime endDate,
        MetricCategory category,
        AggregationType aggregationType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggregate analytics events by entity type
    /// </summary>
    Task<Dictionary<string, int>> AggregateEventsByEntityTypeAsync(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate trend data for metrics
    /// </summary>
    Task<Dictionary<DateTime, decimal>> CalculateTrendDataAsync(
        MetricCategory category,
        MetricType metricType,
        DateTime startDate,
        DateTime endDate,
        TimeSpan interval,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate performance summary for entity
    /// </summary>
    Task<Dictionary<string, object>> GeneratePerformanceSummaryAsync(
        string entityId,
        string entityType,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Aggregate data from multiple services for comprehensive reporting
    /// </summary>
    Task<AggregatedDataResult> AggregateDataAsync(
        DataAggregationRequest request,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Data aggregation request for comprehensive reporting
/// </summary>
public class DataAggregationRequest
{
    public List<string> Services { get; set; } = new();
    public List<string> IncludedServices { get; set; } = new();
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<string> Metrics { get; set; } = new();
    public Dictionary<string, object> Filters { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();

    // Additional properties required by AdvancedReportingService
    public DateRangeDto DateRange { get; set; } = new();
    public string GroupBy { get; set; } = string.Empty;
}

/// <summary>
/// Aggregated data result for comprehensive reporting
/// </summary>
public class AggregatedDataResult
{
    public int TotalRecords { get; set; }
    public Dictionary<string, object> DataSources { get; set; } = new();
    public long ExecutionTimeMs { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public List<string> Metrics { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Aggregation types for metrics
/// </summary>
public enum AggregationType
{
    Sum,
    Average,
    Count,
    Min,
    Max
}
