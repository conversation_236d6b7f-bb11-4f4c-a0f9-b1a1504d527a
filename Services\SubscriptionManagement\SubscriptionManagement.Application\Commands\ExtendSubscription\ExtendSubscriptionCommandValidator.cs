using FluentValidation;

namespace SubscriptionManagement.Application.Commands.ExtendSubscription
{
    public class ExtendSubscriptionCommandValidator : AbstractValidator<ExtendSubscriptionCommand>
    {
        public ExtendSubscriptionCommandValidator()
        {
            RuleFor(x => x.SubscriptionId)
                .NotEmpty()
                .WithMessage("Subscription ID is required");

            RuleFor(x => x.UserId)
                .NotEmpty()
                .WithMessage("User ID is required");

            RuleFor(x => x.ExtensionDays)
                .GreaterThan(0)
                .WithMessage("Extension days must be greater than 0")
                .LessThanOrEqualTo(90)
                .WithMessage("Extension cannot exceed 90 days");

            RuleFor(x => x.ExtendedByUserId)
                .NotEmpty()
                .WithMessage("Extended by user ID is required");

            RuleFor(x => x.Reason)
                .MaximumLength(500)
                .WithMessage("Reason cannot exceed 500 characters");
        }
    }
}
