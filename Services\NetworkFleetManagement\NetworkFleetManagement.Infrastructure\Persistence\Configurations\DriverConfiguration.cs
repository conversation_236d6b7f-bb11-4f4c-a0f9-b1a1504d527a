using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetworkFleetManagement.Domain.Entities;

namespace NetworkFleetManagement.Infrastructure.Persistence.Configurations;

public class DriverConfiguration : IEntityTypeConfiguration<Driver>
{
    public void Configure(EntityTypeBuilder<Driver> builder)
    {
        builder.ToTable("drivers");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .ValueGeneratedNever();

        builder.Property(d => d.CarrierId)
            .IsRequired();

        builder.Property(d => d.UserId)
            .IsRequired();

        builder.Property(d => d.FirstName)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(d => d.LastName)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(d => d.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(d => d.Email)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(d => d.LicenseNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(d => d.LicenseExpiryDate)
            .IsRequired();

        builder.Property(d => d.AadharNumber)
            .HasMaxLength(20);

        builder.Property(d => d.PANNumber)
            .HasMaxLength(20);

        builder.Property(d => d.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.OnboardingStatus)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(d => d.ProfilePhotoUrl)
            .HasMaxLength(500);

        builder.Property(d => d.LastActiveAt);

        builder.Property(d => d.Notes)
            .HasMaxLength(1000);

        builder.Property(d => d.IsVerified)
            .IsRequired();

        builder.Property(d => d.VerifiedAt);

        builder.Property(d => d.CreatedAt)
            .IsRequired();

        builder.Property(d => d.UpdatedAt);

        // Configure PerformanceMetrics as owned entity
        builder.OwnsOne(d => d.PerformanceMetrics, pm =>
        {
            pm.Property(p => p.Rating)
                .HasColumnName("performance_rating")
                .HasPrecision(3, 2);

            pm.Property(p => p.TotalTrips)
                .HasColumnName("performance_total_trips");

            pm.Property(p => p.CompletedTrips)
                .HasColumnName("performance_completed_trips");

            pm.Property(p => p.CancelledTrips)
                .HasColumnName("performance_cancelled_trips");

            pm.Property(p => p.OnTimePercentage)
                .HasColumnName("performance_on_time_percentage")
                .HasPrecision(5, 2);

            pm.Property(p => p.CustomerSatisfactionScore)
                .HasColumnName("performance_customer_satisfaction")
                .HasPrecision(3, 2);

            pm.Property(p => p.LastUpdated)
                .HasColumnName("performance_last_updated");
        });

        // Configure CurrentLocation as owned entity
        builder.OwnsOne(d => d.CurrentLocation, cl =>
        {
            cl.Property(l => l.Latitude)
                .HasColumnName("current_latitude")
                .HasPrecision(10, 8);

            cl.Property(l => l.Longitude)
                .HasColumnName("current_longitude")
                .HasPrecision(11, 8);

            cl.Property(l => l.Address)
                .HasColumnName("current_address")
                .HasMaxLength(500);

            cl.Property(l => l.City)
                .HasColumnName("current_city")
                .HasMaxLength(100);

            cl.Property(l => l.State)
                .HasColumnName("current_state")
                .HasMaxLength(100);

            cl.Property(l => l.Country)
                .HasColumnName("current_country")
                .HasMaxLength(100);

            cl.Property(l => l.PostalCode)
                .HasColumnName("current_postal_code")
                .HasMaxLength(20);

            cl.Property(l => l.Timestamp)
                .HasColumnName("current_location_timestamp");
        });

        // Configure collections as JSON (for PostgreSQL)
        builder.Property(d => d.PreferredRoutes)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
            .HasColumnName("preferred_routes");

        builder.Property(d => d.OperationalAreas)
            .HasConversion(
                v => string.Join(',', v),
                v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
            .HasColumnName("operational_areas");

        // Configure relationships
        builder.HasMany(d => d.Documents)
            .WithOne(doc => doc.Driver)
            .HasForeignKey(doc => doc.DriverId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(d => d.VehicleAssignments)
            .WithOne(va => va.Driver)
            .HasForeignKey(va => va.DriverId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(d => d.UserId)
            .IsUnique();

        builder.HasIndex(d => d.CarrierId);

        builder.HasIndex(d => d.LicenseNumber)
            .IsUnique();

        builder.HasIndex(d => d.Email);

        builder.HasIndex(d => d.Status);

        builder.HasIndex(d => d.OnboardingStatus);

        builder.HasIndex(d => d.LicenseExpiryDate);

        builder.HasIndex(d => d.IsVerified);
    }
}
