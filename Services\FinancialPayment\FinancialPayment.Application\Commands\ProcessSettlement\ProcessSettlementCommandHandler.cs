using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.ProcessSettlement;

public class ProcessSettlementCommandHandler : IRequestHandler<ProcessSettlementCommand, bool>
{
    private readonly ISettlementService _settlementService;
    private readonly ILogger<ProcessSettlementCommandHandler> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public ProcessSettlementCommandHandler(
        ISettlementService settlementService,
        ILogger<ProcessSettlementCommandHandler> logger,
        IUnitOfWork unitOfWork)
    {
        _settlementService = settlementService;
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task<bool> Handle(ProcessSettlementCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing settlement {SettlementId} by {ProcessedBy}", 
                request.SettlementId, request.ProcessedBy);

            var result = await _settlementService.ProcessSettlementAsync(request.SettlementId);

            if (result)
            {
                await _unitOfWork.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Settlement {SettlementId} processed successfully", request.SettlementId);
            }
            else
            {
                _logger.LogWarning("Failed to process settlement {SettlementId}", request.SettlementId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing settlement {SettlementId}", request.SettlementId);
            throw;
        }
    }
}
