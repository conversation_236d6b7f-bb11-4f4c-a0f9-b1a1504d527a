using System;
using System.Collections.Generic;
using Shared.Domain.Common;

namespace DataStorage.Domain.Entities
{
    public class CustomMetric : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string MetricType { get; private set; } // Counter, Gauge, Histogram
        public double Value { get; private set; }
        public string Unit { get; private set; }
        public DateTime Timestamp { get; private set; }
        public string Source { get; private set; }
        public Dictionary<string, string> Tags { get; private set; }
        public string Namespace { get; private set; }

        private CustomMetric()
        {
            Tags = new Dictionary<string, string>();
        }

        public CustomMetric(
            string name,
            string description,
            string metricType,
            double value,
            string unit,
            string source,
            string @namespace = null,
            Dictionary<string, string> tags = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description;
            MetricType = metricType ?? "Gauge";
            Value = value;
            Unit = unit;
            Timestamp = DateTime.UtcNow;
            Source = source ?? throw new ArgumentNullException(nameof(source));
            Namespace = @namespace ?? "DataStorage";
            Tags = tags ?? new Dictionary<string, string>();
            CreatedAt = DateTime.UtcNow;
        }

        public void UpdateValue(double newValue)
        {
            Value = newValue;
            Timestamp = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AddTag(string key, string value)
        {
            Tags[key] = value;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
