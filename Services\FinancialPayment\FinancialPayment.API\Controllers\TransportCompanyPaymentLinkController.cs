using FinancialPayment.Application.Commands.PaymentLink;
using FinancialPayment.Application.Queries.PaymentLink;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FinancialPayment.API.Controllers;

/// <summary>
/// Controller for Transport Company payment link management
/// </summary>
[ApiController]
[Route("api/transport-company/payment-links")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyPaymentLinkController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyPaymentLinkController> _logger;

    public TransportCompanyPaymentLinkController(
        IMediator mediator,
        ILogger<TransportCompanyPaymentLinkController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Create a payment link for a shipper
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(CreatePaymentLinkResult), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<CreatePaymentLinkResult>> CreatePaymentLink(
        [FromBody] CreatePaymentLinkCommand command)
    {
        try
        {
            // Validate that the requesting user can create payment links for this transport company
            if (!await CanManagePaymentLinks(command.TransportCompanyId))
            {
                return Forbid("You don't have permission to create payment links for this transport company");
            }

            command.CreatedByUserId = GetCurrentUserId();
            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment link created successfully: {PaymentLinkId} for Transport Company {TransportCompanyId}",
                    result.PaymentLinkId, command.TransportCompanyId);
                return CreatedAtAction(nameof(GetPaymentLink), new { id = result.PaymentLinkId }, result);
            }

            _logger.LogWarning("Failed to create payment link for Transport Company {TransportCompanyId}: {Error}",
                command.TransportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment link for Transport Company {TransportCompanyId}",
                command.TransportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment link details by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(PaymentLinkDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<PaymentLinkDetailsDto>> GetPaymentLink(Guid id)
    {
        try
        {
            // This would typically include authorization check
            var query = new GetPaymentLinkByTokenQuery { Token = id.ToString(), RecordAccess = false };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound("Payment link not found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment link {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment links for a transport company
    /// </summary>
    [HttpGet("company/{transportCompanyId}")]
    [ProducesResponseType(typeof(PaymentLinkListDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<PaymentLinkListDto>> GetTransportCompanyPaymentLinks(
        Guid transportCompanyId,
        [FromQuery] GetTransportCompanyPaymentLinksQuery query)
    {
        try
        {
            if (!await CanViewPaymentLinks(transportCompanyId))
            {
                return Forbid("You don't have permission to view payment links for this transport company");
            }

            query.TransportCompanyId = transportCompanyId;
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment links for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Share a payment link with shipper
    /// </summary>
    [HttpPost("{id}/share")]
    [ProducesResponseType(typeof(SharePaymentLinkResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<SharePaymentLinkResult>> SharePaymentLink(
        Guid id,
        [FromBody] SharePaymentLinkCommand command)
    {
        try
        {
            command.PaymentLinkId = id;
            command.SharedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment link {PaymentLinkId} shared successfully to {RecipientCount} recipients",
                    id, result.SuccessfulShares);
                return Ok(result);
            }

            _logger.LogWarning("Failed to share payment link {PaymentLinkId}: {Error}", id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sharing payment link {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Cancel a payment link
    /// </summary>
    [HttpPost("{id}/cancel")]
    [ProducesResponseType(typeof(CancelPaymentLinkResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<CancelPaymentLinkResult>> CancelPaymentLink(
        Guid id,
        [FromBody] CancelPaymentLinkCommand command)
    {
        try
        {
            command.PaymentLinkId = id;
            command.CancelledByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment link {PaymentLinkId} cancelled successfully", id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to cancel payment link {PaymentLinkId}: {Error}", id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling payment link {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Extend payment link expiry
    /// </summary>
    [HttpPost("{id}/extend")]
    [ProducesResponseType(typeof(ExtendPaymentLinkResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ExtendPaymentLinkResult>> ExtendPaymentLink(
        Guid id,
        [FromBody] ExtendPaymentLinkCommand command)
    {
        try
        {
            command.PaymentLinkId = id;
            command.ExtendedByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment link {PaymentLinkId} extended successfully to {NewExpiryDate}",
                    id, result.NewExpiryDate);
                return Ok(result);
            }

            _logger.LogWarning("Failed to extend payment link {PaymentLinkId}: {Error}", id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extending payment link {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Send payment reminder
    /// </summary>
    [HttpPost("{id}/remind")]
    [ProducesResponseType(typeof(SendPaymentReminderResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<SendPaymentReminderResult>> SendPaymentReminder(
        Guid id,
        [FromBody] SendPaymentReminderCommand command)
    {
        try
        {
            command.PaymentLinkId = id;
            command.SentByUserId = GetCurrentUserId();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Payment reminder sent successfully for payment link {PaymentLinkId}",
                    id);
                return Ok(result);
            }

            _logger.LogWarning("Failed to send payment reminder for payment link {PaymentLinkId}: {Error}",
                id, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending payment reminder for payment link {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment link analytics for a transport company
    /// </summary>
    [HttpGet("company/{transportCompanyId}/analytics")]
    [ProducesResponseType(typeof(PaymentLinkAnalyticsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<PaymentLinkAnalyticsDto>> GetPaymentLinkAnalytics(
        Guid transportCompanyId,
        [FromQuery] GetPaymentLinkAnalyticsQuery query)
    {
        try
        {
            if (!await CanViewPaymentLinks(transportCompanyId))
            {
                return Forbid("You don't have permission to view payment link analytics for this transport company");
            }

            query.TransportCompanyId = transportCompanyId;
            var result = await _mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment link analytics for Transport Company {TransportCompanyId}",
                transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get payment link status and tracking information
    /// </summary>
    [HttpGet("{id}/status")]
    [ProducesResponseType(typeof(PaymentLinkStatusDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<PaymentLinkStatusDto>> GetPaymentLinkStatus(
        Guid id,
        [FromQuery] bool includeAccessLogs = false,
        [FromQuery] bool includePaymentAttempts = false)
    {
        try
        {
            var query = new GetPaymentLinkStatusQuery
            {
                PaymentLinkId = id,
                IncludeAccessLogs = includeAccessLogs,
                IncludePaymentAttempts = includePaymentAttempts
            };

            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound("Payment link not found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment link status for {PaymentLinkId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManagePaymentLinks(Guid transportCompanyId)
    {
        // Implement authorization logic
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private async Task<bool> CanViewPaymentLinks(Guid transportCompanyId)
    {
        return await CanManagePaymentLinks(transportCompanyId);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        // For now, return true as a placeholder
        return await Task.FromResult(true);
    }
}
