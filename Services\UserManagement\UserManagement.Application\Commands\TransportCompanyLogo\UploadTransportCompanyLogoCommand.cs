using UserManagement.Application.DTOs;
using MediatR;

namespace UserManagement.Application.Commands.TransportCompanyLogo;

/// <summary>
/// Command to upload or update Transport Company logo
/// </summary>
public class UploadTransportCompanyLogoCommand : IRequest<UploadTransportCompanyLogoResult>
{
    public Guid TransportCompanyId { get; set; }
    public byte[] ImageData { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public bool GenerateThumbnails { get; set; } = true;
    public bool OptimizeForWeb { get; set; } = true;
    public bool ReplaceExisting { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
    public Guid UploadedBy { get; set; }
}

/// <summary>
/// Result of uploading Transport Company logo
/// </summary>
public class UploadTransportCompanyLogoResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid? LogoId { get; set; }
    public string? LogoUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public List<LogoVariantDto> LogoVariants { get; set; } = new();
    public DateTime UploadedAt { get; set; }
    public LogoProcessingMetricsDto ProcessingMetrics { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
}

/// <summary>
/// Command to delete Transport Company logo
/// </summary>
public class DeleteTransportCompanyLogoCommand : IRequest<DeleteTransportCompanyLogoResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool DeleteAllVariants { get; set; } = true;
    public string? DeletionReason { get; set; }
    public Guid DeletedBy { get; set; }
}

/// <summary>
/// Result of deleting Transport Company logo
/// </summary>
public class DeleteTransportCompanyLogoResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime DeletedAt { get; set; }
    public int DeletedVariants { get; set; }
    public List<string> DeletedFiles { get; set; } = new();
}

/// <summary>
/// Command to get Transport Company logo information
/// </summary>
public class GetTransportCompanyLogoQuery : IRequest<TransportCompanyLogoDto>
{
    public Guid TransportCompanyId { get; set; }
    public bool IncludeVariants { get; set; } = true;
    public bool IncludeMetadata { get; set; } = false;
}

/// <summary>
/// Command to update logo settings
/// </summary>
public class UpdateTransportCompanyLogoSettingsCommand : IRequest<UpdateTransportCompanyLogoSettingsResult>
{
    public Guid TransportCompanyId { get; set; }
    public bool IsVisible { get; set; } = true;
    public string? DisplayName { get; set; }
    public string? AltText { get; set; }
    public LogoDisplaySettings DisplaySettings { get; set; } = new();
    public Dictionary<string, object> CustomSettings { get; set; } = new();
    public Guid UpdatedBy { get; set; }
}

/// <summary>
/// Result of updating logo settings
/// </summary>
public class UpdateTransportCompanyLogoSettingsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
}

/// <summary>
/// Command to generate logo variants
/// </summary>
public class GenerateLogoVariantsCommand : IRequest<GenerateLogoVariantsResult>
{
    public Guid TransportCompanyId { get; set; }
    public Guid LogoId { get; set; }
    public List<LogoVariantSpecification> VariantSpecs { get; set; } = new();
    public bool RegenerateExisting { get; set; } = false;
    public Guid RequestedBy { get; set; }
}

/// <summary>
/// Result of generating logo variants
/// </summary>
public class GenerateLogoVariantsResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public List<LogoVariantDto> GeneratedVariants { get; set; } = new();
    public List<LogoVariantDto> SkippedVariants { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// Specification for logo variant generation
/// </summary>
public class LogoVariantSpecification
{
    public string Name { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Format { get; set; } = "PNG"; // PNG, JPEG, WEBP
    public int Quality { get; set; } = 90; // For JPEG
    public bool MaintainAspectRatio { get; set; } = true;
    public string BackgroundColor { get; set; } = "transparent";
    public bool AddWatermark { get; set; } = false;
    public Dictionary<string, object> ProcessingOptions { get; set; } = new();
}

/// <summary>
/// Logo display settings
/// </summary>
public class LogoDisplaySettings
{
    public int MaxWidth { get; set; } = 300;
    public int MaxHeight { get; set; } = 150;
    public string Position { get; set; } = "center"; // left, center, right
    public string BackgroundColor { get; set; } = "transparent";
    public bool ShowBorder { get; set; } = false;
    public string BorderColor { get; set; } = "#cccccc";
    public int BorderWidth { get; set; } = 1;
    public int BorderRadius { get; set; } = 0;
    public bool EnableHover { get; set; } = true;
    public Dictionary<string, object> CustomStyles { get; set; } = new();
}

/// <summary>
/// DTO for logo variant
/// </summary>
public class LogoVariantDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Format { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsOptimized { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// DTO for Transport Company logo
/// </summary>
public class TransportCompanyLogoDto
{
    public Guid Id { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string OriginalUrl { get; set; } = string.Empty;
    public string ThumbnailUrl { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? AltText { get; set; }
    public bool IsVisible { get; set; } = true;
    public LogoDisplaySettings DisplaySettings { get; set; } = new();
    public List<LogoVariantDto> Variants { get; set; } = new();
    public LogoMetadataDto Metadata { get; set; } = new();
    public DateTime UploadedAt { get; set; }
    public DateTime? LastModified { get; set; }
    public Guid UploadedBy { get; set; }
    public string UploadedByName { get; set; } = string.Empty;
}

/// <summary>
/// DTO for logo metadata
/// </summary>
public class LogoMetadataDto
{
    public string OriginalFileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long OriginalFileSize { get; set; }
    public int OriginalWidth { get; set; }
    public int OriginalHeight { get; set; }
    public string ColorProfile { get; set; } = string.Empty;
    public bool HasTransparency { get; set; }
    public string DominantColors { get; set; } = string.Empty;
    public Dictionary<string, object> ExifData { get; set; } = new();
    public Dictionary<string, object> ProcessingHistory { get; set; } = new();
}

/// <summary>
/// DTO for logo processing metrics
/// </summary>
public class LogoProcessingMetricsDto
{
    public TimeSpan UploadTime { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public TimeSpan OptimizationTime { get; set; }
    public TimeSpan ThumbnailGenerationTime { get; set; }
    public long OriginalSize { get; set; }
    public long OptimizedSize { get; set; }
    public decimal CompressionRatio { get; set; }
    public int VariantsGenerated { get; set; }
    public bool QualityCheckPassed { get; set; }
    public List<string> ProcessingSteps { get; set; } = new();
    public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
}

/// <summary>
/// Request for logo upload validation
/// </summary>
public class ValidateLogoUploadRequest
{
    public byte[] ImageData { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
}

/// <summary>
/// Result of logo upload validation
/// </summary>
public class ValidateLogoUploadResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
    public LogoQualityAnalysis QualityAnalysis { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Logo quality analysis
/// </summary>
public class LogoQualityAnalysis
{
    public decimal OverallScore { get; set; } // 0-100
    public decimal ResolutionScore { get; set; }
    public decimal CompressionScore { get; set; }
    public decimal ColorScore { get; set; }
    public decimal ContrastScore { get; set; }
    public bool IsVectorBased { get; set; }
    public bool HasTransparency { get; set; }
    public bool IsWebOptimized { get; set; }
    public List<string> QualityIssues { get; set; } = new();
    public List<string> QualityStrengths { get; set; } = new();
}

/// <summary>
/// Request for bulk logo operations
/// </summary>
public class BulkLogoOperationRequest
{
    public List<Guid> TransportCompanyIds { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // regenerate-variants, optimize, validate
    public Dictionary<string, object> OperationParameters { get; set; } = new();
    public Guid RequestedBy { get; set; }
}

/// <summary>
/// Result of bulk logo operations
/// </summary>
public class BulkLogoOperationResult
{
    public bool IsSuccess { get; set; }
    public int TotalProcessed { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public List<BulkOperationItemResult> ItemResults { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
    public TimeSpan TotalProcessingTime { get; set; }
}

/// <summary>
/// Individual item result in bulk operation
/// </summary>
public class BulkOperationItemResult
{
    public Guid TransportCompanyId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> OperationData { get; set; } = new();
}
