using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class NotificationTemplate : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // Marketing, Transactional, Alert, System, etc.
    public string Type { get; private set; } // Push, Email, SMS, InApp
    public bool IsActive { get; private set; }
    public Dictionary<string, object> Content { get; private set; } // Title, body, action, etc.
    public Dictionary<string, object> Styling { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public List<string> SupportedPlatforms { get; private set; }
    public Dictionary<string, object> PlatformSpecific { get; private set; }
    public List<string> Variables { get; private set; } // Template variables like {userName}, {orderNumber}
    public Dictionary<string, object> DefaultValues { get; private set; }
    public string? PreviewUrl { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<NotificationCampaign> Campaigns { get; private set; } = new List<NotificationCampaign>();

    private NotificationTemplate() { } // EF Core

    public NotificationTemplate(
        string name,
        string displayName,
        string description,
        string category,
        string type,
        Dictionary<string, object> content,
        List<string> supportedPlatforms,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Type = type ?? throw new ArgumentNullException(nameof(type));
        Content = content ?? throw new ArgumentNullException(nameof(content));
        SupportedPlatforms = supportedPlatforms ?? throw new ArgumentNullException(nameof(supportedPlatforms));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        CreatedAt = DateTime.UtcNow;
        Styling = new Dictionary<string, object>();
        Configuration = new Dictionary<string, object>();
        PlatformSpecific = new Dictionary<string, object>();
        Variables = new List<string>();
        DefaultValues = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        ExtractVariablesFromContent();
        SetDefaultConfiguration();

        AddDomainEvent(new NotificationTemplateCreatedEvent(Id, Name, Category, Type, CreatedBy));
    }

    public void UpdateContent(Dictionary<string, object> newContent, string updatedBy)
    {
        Content = newContent ?? throw new ArgumentNullException(nameof(newContent));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        ExtractVariablesFromContent();

        AddDomainEvent(new NotificationTemplateContentUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateStyling(Dictionary<string, object> styling, string updatedBy)
    {
        Styling = styling ?? throw new ArgumentNullException(nameof(styling));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationTemplateStylingUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationTemplateConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetPlatformSpecific(string platform, Dictionary<string, object> platformConfig)
    {
        if (!SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase))
        {
            throw new InvalidOperationException($"Platform {platform} is not supported by this template");
        }

        PlatformSpecific[platform] = platformConfig ?? throw new ArgumentNullException(nameof(platformConfig));
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationTemplatePlatformConfigUpdatedEvent(Id, Name, platform));
    }

    public void SetDefaultValue(string variable, object value)
    {
        DefaultValues[variable] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPreviewUrl(string previewUrl)
    {
        PreviewUrl = previewUrl;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationTemplateActivatedEvent(Id, Name));
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new NotificationTemplateDeactivatedEvent(Id, Name));
    }

    public Dictionary<string, object> RenderContent(Dictionary<string, object> variables, string? platform = null)
    {
        var renderedContent = new Dictionary<string, object>();

        // Merge default values with provided variables
        var mergedVariables = new Dictionary<string, object>(DefaultValues);
        foreach (var variable in variables)
        {
            mergedVariables[variable.Key] = variable.Value;
        }

        // Render content with variables
        foreach (var contentItem in Content)
        {
            if (contentItem.Value is string stringValue)
            {
                renderedContent[contentItem.Key] = ReplaceVariables(stringValue, mergedVariables);
            }
            else
            {
                renderedContent[contentItem.Key] = contentItem.Value;
            }
        }

        // Apply platform-specific overrides
        if (!string.IsNullOrEmpty(platform) && PlatformSpecific.TryGetValue(platform, out var platformConfig) &&
            platformConfig is Dictionary<string, object> platformDict)
        {
            foreach (var platformItem in platformDict)
            {
                if (platformItem.Value is string platformStringValue)
                {
                    renderedContent[platformItem.Key] = ReplaceVariables(platformStringValue, mergedVariables);
                }
                else
                {
                    renderedContent[platformItem.Key] = platformItem.Value;
                }
            }
        }

        return renderedContent;
    }

    public bool SupportsPlatform(string platform)
    {
        return SupportedPlatforms.Contains(platform, StringComparer.OrdinalIgnoreCase);
    }

    public List<string> GetMissingVariables(Dictionary<string, object> providedVariables)
    {
        var missingVariables = new List<string>();

        foreach (var variable in Variables)
        {
            if (!providedVariables.ContainsKey(variable) && !DefaultValues.ContainsKey(variable))
            {
                missingVariables.Add(variable);
            }
        }

        return missingVariables;
    }

    public bool IsValidForPlatform(string platform)
    {
        if (!SupportsPlatform(platform))
        {
            return false;
        }

        // Check if required platform-specific content is available
        if (Type.ToLowerInvariant() == "push")
        {
            return Content.ContainsKey("title") && Content.ContainsKey("body");
        }

        return true;
    }

    private void ExtractVariablesFromContent()
    {
        Variables.Clear();
        var variablePattern = @"\{([^}]+)\}";
        var regex = new System.Text.RegularExpressions.Regex(variablePattern);

        foreach (var contentItem in Content)
        {
            if (contentItem.Value is string stringValue)
            {
                var matches = regex.Matches(stringValue);
                foreach (System.Text.RegularExpressions.Match match in matches)
                {
                    var variable = match.Groups[1].Value;
                    if (!Variables.Contains(variable))
                    {
                        Variables.Add(variable);
                    }
                }
            }
        }
    }

    private string ReplaceVariables(string template, Dictionary<string, object> variables)
    {
        var result = template;

        foreach (var variable in variables)
        {
            var placeholder = $"{{{variable.Key}}}";
            result = result.Replace(placeholder, variable.Value?.ToString() ?? "");
        }

        return result;
    }

    private void SetDefaultConfiguration()
    {
        Configuration["priority"] = "normal"; // low, normal, high
        Configuration["ttl"] = 86400; // 24 hours in seconds
        Configuration["badge_count"] = 1;
        Configuration["sound"] = "default";
        Configuration["vibrate"] = true;

        if (Type.ToLowerInvariant() == "push")
        {
            Configuration["collapse_key"] = Name.ToLowerInvariant().Replace(" ", "_");
            Configuration["click_action"] = "FLUTTER_NOTIFICATION_CLICK";
        }
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public Dictionary<string, object> GetPlatformConfig(string platform)
    {
        if (PlatformSpecific.TryGetValue(platform, out var config) && config is Dictionary<string, object> platformConfig)
        {
            return platformConfig;
        }
        return new Dictionary<string, object>();
    }
}


