using MediatR;
using UserManagement.Domain.Entities;

namespace UserManagement.Application.Admin.Queries.ExportKycDocuments
{
    public class ExportKycDocumentsQuery : IRequest<KycDocumentExportResponse>
    {
        public List<Guid> UserIds { get; set; } = new();
        public List<DocumentType> DocumentTypes { get; set; } = new();
        public DocumentStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public UserType? UserType { get; set; }
        public bool IncludeMetadata { get; set; } = true;
        public bool GroupByUser { get; set; } = true;
        public bool GroupByDocumentType { get; set; } = false;
        public int? MaxDocuments { get; set; }
        public Guid RequestedBy { get; set; }
        public string RequestedByRole { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    public class KycDocumentExportResponse
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public int DocumentCount { get; set; }
        public int UserCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string ExportId { get; set; }
        public List<DocumentExportSummary> DocumentSummary { get; set; } = new();
    }

    public class DocumentExportSummary
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public DocumentType DocumentType { get; set; }
        public string FileName { get; set; }
        public DocumentStatus Status { get; set; }
        public DateTime UploadedAt { get; set; }
        public DateTime? ReviewedAt { get; set; }
        public string FilePath { get; set; }
        public string FileSize { get; set; }
    }
}
