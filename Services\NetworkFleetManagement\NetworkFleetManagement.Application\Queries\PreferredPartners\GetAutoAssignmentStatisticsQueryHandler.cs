using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetAutoAssignmentStatisticsQueryHandler : IRequestHandler<GetAutoAssignmentStatisticsQuery, AutoAssignmentStatisticsDto>
{
    private readonly IPreferredPartnerRepository _preferredPartnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAutoAssignmentStatisticsQueryHandler> _logger;

    public GetAutoAssignmentStatisticsQueryHandler(
        IPreferredPartnerRepository preferredPartnerRepository,
        IMapper mapper,
        ILogger<GetAutoAssignmentStatisticsQueryHandler> logger)
    {
        _preferredPartnerRepository = preferredPartnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<AutoAssignmentStatisticsDto> Handle(GetAutoAssignmentStatisticsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // This would typically fetch from an auto-assignment tracking service
            // For now, generate mock statistics based on preferred partners
            var partners = await _preferredPartnerRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken);
            var autoAssignEnabledPartners = partners.Where(p => p.AutoAssignEnabled).ToList();

            var random = new Random();
            var totalDays = (request.ToDate - request.FromDate).Days;
            var totalAssignments = random.Next(50, 200);
            var successfulAssignments = (int)(totalAssignments * 0.85); // 85% success rate

            var statistics = new AutoAssignmentStatisticsDto
            {
                UserId = request.UserId,
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                GeneratedAt = DateTime.UtcNow,
                TotalAutoAssignments = totalAssignments,
                SuccessfulAutoAssignments = successfulAssignments,
                FailedAutoAssignments = totalAssignments - successfulAssignments,
                SuccessRate = totalAssignments > 0 ? (decimal)successfulAssignments / totalAssignments * 100 : 0,
                AverageAssignmentTime = (decimal)(random.NextDouble() * 30 + 5), // 5-35 minutes
                AveragePartnerRating = autoAssignEnabledPartners.Any() ? 
                    autoAssignEnabledPartners.Average(p => p.PerformanceMetrics.OverallRating) : 0,
                TotalValueAssigned = (decimal)(random.NextDouble() * 500000 + 100000) // $100k-$600k
            };

            // Generate breakdown by partner type
            var partnerTypes = autoAssignEnabledPartners.GroupBy(p => p.PartnerType);
            foreach (var group in partnerTypes)
            {
                var typeAssignments = random.Next(10, 50);
                var typeSuccessful = (int)(typeAssignments * 0.85);
                
                statistics.BreakdownByType.Add(new AutoAssignmentByTypeDto
                {
                    PartnerType = group.Key.ToString(),
                    TotalAssignments = typeAssignments,
                    SuccessfulAssignments = typeSuccessful,
                    SuccessRate = typeAssignments > 0 ? (decimal)typeSuccessful / typeAssignments * 100 : 0,
                    AverageRating = group.Average(p => p.PerformanceMetrics.OverallRating)
                });
            }

            // Generate daily trends
            for (var date = request.FromDate.Date; date <= request.ToDate.Date; date = date.AddDays(1))
            {
                var dailyAssignments = random.Next(0, 10);
                var dailySuccessful = (int)(dailyAssignments * 0.85);
                
                statistics.DailyTrends.Add(new AutoAssignmentTrendDto
                {
                    Date = date,
                    TotalAssignments = dailyAssignments,
                    SuccessfulAssignments = dailySuccessful,
                    SuccessRate = dailyAssignments > 0 ? (decimal)dailySuccessful / dailyAssignments * 100 : 0,
                    AverageAssignmentTime = (decimal)(random.NextDouble() * 30 + 5)
                });
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating auto-assignment statistics for user {UserId}", request.UserId);
            throw;
        }
    }
}
