using MediatR;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Application.Commands;

// Audit Log Commands
public class CreateAuditLogCommand : IRequest<Guid>
{
    public AuditEventType EventType { get; set; }
    public AuditSeverity Severity { get; set; }
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserRole { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? OldValues { get; set; }
    public string? NewValues { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public List<ComplianceStandard>? ComplianceFlags { get; set; }
}

public class CleanupExpiredAuditLogsCommand : IRequest<int>
{
    // No parameters needed - will clean up all expired logs
}

// Compliance Report Commands
public class CreateComplianceReportCommand : IRequest<Guid>
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ComplianceStandard Standard { get; set; }
    public DateTime ReportPeriodStart { get; set; }
    public DateTime ReportPeriodEnd { get; set; }
    public Guid GeneratedBy { get; set; }
    public string GeneratedByName { get; set; } = string.Empty;
    public bool IsAutomated { get; set; } = false;
}

public class StartComplianceReportProcessingCommand : IRequest<bool>
{
    public Guid ReportId { get; set; }
}

public class CompleteComplianceReportCommand : IRequest<bool>
{
    public Guid ReportId { get; set; }
    public string? Findings { get; set; }
    public string? Recommendations { get; set; }
    public string? FilePath { get; set; }
    public string? FileHash { get; set; }
}

public class GenerateAutomatedComplianceReportCommand : IRequest<ComplianceReportDto>
{
    public ComplianceStandard Standard { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public Guid GeneratedBy { get; set; }
    public string GeneratedByName { get; set; } = string.Empty;
}

// Service Provider Rating Commands
public class CreateServiceProviderRatingCommand : IRequest<Guid>
{
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public Guid? TripId { get; set; }
    public string? TripNumber { get; set; }
    public decimal OverallRating { get; set; }
    public string? ReviewTitle { get; set; }
    public string? ReviewComment { get; set; }
    public bool IsAnonymous { get; set; } = false;
    public DateTime ServiceCompletedAt { get; set; }
    public List<CreateCategoryRatingDto> CategoryRatings { get; set; } = new();
}

public class SubmitServiceProviderRatingCommand : IRequest<bool>
{
    public Guid RatingId { get; set; }
}

public class FlagServiceProviderRatingCommand : IRequest<bool>
{
    public Guid RatingId { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class RemoveServiceProviderRatingCommand : IRequest<bool>
{
    public Guid RatingId { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class ReportServiceIssueCommand : IRequest<Guid>
{
    public Guid ServiceProviderRatingId { get; set; }
    public ServiceIssueType IssueType { get; set; }
    public IssuePriority Priority { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Evidence { get; set; }
}

public class ResolveServiceIssueCommand : IRequest<bool>
{
    public Guid IssueId { get; set; }
    public string Resolution { get; set; } = string.Empty;
    public Guid ResolvedBy { get; set; }
    public string ResolvedByName { get; set; } = string.Empty;
}

// Preferred Provider Network Commands
public class AddPreferredProviderCommand : IRequest<Guid>
{
    public Guid ShipperId { get; set; }
    public string ShipperName { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public decimal AverageRating { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public DateTime FirstOrderDate { get; set; }
    public DateTime LastOrderDate { get; set; }
    public string? Notes { get; set; }
}

public class RemovePreferredProviderCommand : IRequest<bool>
{
    public Guid ShipperId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class UpdatePreferredProviderStatisticsCommand : IRequest<bool>
{
    public Guid ShipperId { get; set; }
    public Guid TransportCompanyId { get; set; }
    public decimal AverageRating { get; set; }
    public int TotalOrders { get; set; }
    public int CompletedOrders { get; set; }
    public DateTime LastOrderDate { get; set; }
}

public class ReorderPreferredProvidersCommand : IRequest<bool>
{
    public Guid ShipperId { get; set; }
    public List<Guid> TransportCompanyIds { get; set; } = new();
}
