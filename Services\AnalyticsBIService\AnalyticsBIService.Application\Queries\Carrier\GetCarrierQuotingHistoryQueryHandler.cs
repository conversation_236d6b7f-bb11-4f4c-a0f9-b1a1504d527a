using AnalyticsBIService.Application.DTOs;
using AnalyticsBIService.Domain.Repositories;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AnalyticsBIService.Application.Queries.Carrier;

/// <summary>
/// Handler for carrier RFQ response history queries
/// </summary>
public class GetCarrierRFQResponseHistoryQueryHandler : IRequestHandler<GetCarrierRFQResponseHistoryQuery, CarrierRFQResponseHistoryDto>
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetCarrierRFQResponseHistoryQueryHandler> _logger;

    public GetCarrierRFQResponseHistoryQueryHandler(
        IAnalyticsEventRepository analyticsEventRepository,
        IMapper mapper,
        ILogger<GetCarrierRFQResponseHistoryQueryHandler> logger)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CarrierRFQResponseHistoryDto> Handle(GetCarrierRFQResponseHistoryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting RFQ response history for carrier {CarrierId}", request.CarrierId);

        // Get RFQ-related events for this carrier
        var allEvents = await _analyticsEventRepository.GetByDateRangeAsync(request.FromDate, request.ToDate, 1, 1000, cancellationToken);
        var rfqEvents = allEvents.Items
            .Where(e => e.UserId == request.CarrierId &&
                       e.EventName.Contains("RFQ"))
            .ToList();

        // Calculate basic metrics
        var totalRFQsReceived = rfqEvents.Count(e => e.EventName == "RFQReceived");
        var rfqsResponded = rfqEvents.Count(e => e.EventName == "RFQResponded");
        var rfqsIgnored = totalRFQsReceived - rfqsResponded;
        var responseRate = totalRFQsReceived > 0 ? (decimal)rfqsResponded / totalRFQsReceived * 100 : 0;

        // Calculate response times (mock data for now)
        var averageResponseTime = TimeSpan.FromHours(4.5);
        var fastestResponseTime = TimeSpan.FromMinutes(30);
        var slowestResponseTime = TimeSpan.FromHours(24);

        // Generate response patterns
        var responsePatterns = new List<RFQResponsePatternDto>
        {
            new() { Pattern = "Quick Response (<2 hours)", Count = rfqsResponded * 30 / 100, Percentage = 30, SuccessRate = 85 },
            new() { Pattern = "Standard Response (2-8 hours)", Count = rfqsResponded * 50 / 100, Percentage = 50, SuccessRate = 75 },
            new() { Pattern = "Delayed Response (>8 hours)", Count = rfqsResponded * 20 / 100, Percentage = 20, SuccessRate = 60 }
        };

        // Generate route response history (mock data)
        var routeResponseHistory = new List<RouteRFQResponseDto>
        {
            new() { RouteId = "BLR-MUM", RouteName = "Bangalore to Mumbai", RFQsReceived = 15, RFQsResponded = 12, ResponseRate = 80, AverageResponseTime = TimeSpan.FromHours(3), SuccessRate = 75 },
            new() { RouteId = "DEL-BLR", RouteName = "Delhi to Bangalore", RFQsReceived = 12, RFQsResponded = 10, ResponseRate = 83.3m, AverageResponseTime = TimeSpan.FromHours(4), SuccessRate = 70 }
        };

        // Generate broker response history (mock data)
        var brokerResponseHistory = new List<BrokerRFQResponseDto>
        {
            new() { BrokerId = Guid.NewGuid(), BrokerName = "ABC Logistics", RFQsReceived = 8, RFQsResponded = 7, ResponseRate = 87.5m, AverageResponseTime = TimeSpan.FromHours(2.5), SuccessRate = 80 },
            new() { BrokerId = Guid.NewGuid(), BrokerName = "XYZ Transport", RFQsReceived = 6, RFQsResponded = 5, ResponseRate = 83.3m, AverageResponseTime = TimeSpan.FromHours(5), SuccessRate = 65 }
        };

        // Generate response trends
        var responseTrends = GenerateRFQResponseTrends(request.FromDate, request.ToDate, totalRFQsReceived, rfqsResponded);

        return new CarrierRFQResponseHistoryDto
        {
            TotalRFQsReceived = totalRFQsReceived,
            RFQsResponded = rfqsResponded,
            RFQsIgnored = rfqsIgnored,
            ResponseRate = responseRate,
            AverageResponseTime = averageResponseTime,
            FastestResponseTime = fastestResponseTime,
            SlowestResponseTime = slowestResponseTime,
            ResponsePatterns = responsePatterns,
            RouteResponseHistory = routeResponseHistory,
            BrokerResponseHistory = brokerResponseHistory,
            ResponseTrends = responseTrends
        };
    }

    private List<RFQResponseTrendDto> GenerateRFQResponseTrends(DateTime fromDate, DateTime toDate, int totalRFQs, int totalResponses)
    {
        var days = (toDate - fromDate).Days;
        var dailyRFQAverage = (decimal)totalRFQs / Math.Max(days, 1);
        var dailyResponseAverage = (decimal)totalResponses / Math.Max(days, 1);
        var trends = new List<RFQResponseTrendDto>();

        for (var date = fromDate; date <= toDate; date = date.AddDays(1))
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±20% variance
            var rfqsReceived = Math.Max(0, (int)(dailyRFQAverage * (1 + variance)));
            var rfqsResponded = Math.Max(0, (int)(dailyResponseAverage * (1 + variance)));
            var responseRate = rfqsReceived > 0 ? (decimal)rfqsResponded / rfqsReceived * 100 : 0;

            trends.Add(new RFQResponseTrendDto
            {
                Date = date,
                RFQsReceived = rfqsReceived,
                RFQsResponded = rfqsResponded,
                ResponseRate = responseRate,
                AverageResponseTime = TimeSpan.FromHours(3 + (new Random().NextDouble() * 4)) // 3-7 hours
            });
        }

        return trends;
    }
}

/// <summary>
/// Main handler for comprehensive carrier quoting history (aggregates all focused queries)
/// </summary>
public class GetCarrierQuotingHistoryQueryHandler : IRequestHandler<GetCarrierQuotingHistoryQuery, CarrierQuotingHistoryDto>
{
    private readonly IMediator _mediator;
    private readonly ILogger<GetCarrierQuotingHistoryQueryHandler> _logger;

    public GetCarrierQuotingHistoryQueryHandler(
        IMediator mediator,
        ILogger<GetCarrierQuotingHistoryQueryHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task<CarrierQuotingHistoryDto> Handle(GetCarrierQuotingHistoryQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Getting comprehensive quoting history for carrier {CarrierId}", request.CarrierId);

        // Initialize result
        var result = new CarrierQuotingHistoryDto
        {
            CarrierId = request.CarrierId,
            CarrierName = "Carrier", // Would be fetched from carrier service
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Period = request.Period.ToString()
        };

        // Get RFQ response history (if requested)
        if (request.IncludeRFQResponseHistory)
        {
            var rfqQuery = new GetCarrierRFQResponseHistoryQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period,
                request.RouteFilter, request.BrokerFilter);
            result.RFQResponseHistory = await _mediator.Send(rfqQuery, cancellationToken);
        }

        // Get quote success rate (if requested)
        if (request.IncludeQuoteSuccessRate)
        {
            var successQuery = new GetCarrierQuoteSuccessRateQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period,
                request.RouteFilter, request.BrokerFilter);
            result.QuoteSuccessRate = await _mediator.Send(successQuery, cancellationToken);
        }

        // Get pricing analytics (if requested)
        if (request.IncludePricingAnalytics)
        {
            var pricingQuery = new GetCarrierPricingAnalyticsQuery(
                request.CarrierId, request.FromDate, request.ToDate, request.Period,
                request.RouteFilter, null);
            result.PricingAnalytics = await _mediator.Send(pricingQuery, cancellationToken);
        }

        // Get market comparison (if requested)
        if (request.IncludeMarketComparison)
        {
            var marketQuery = new GetCarrierMarketComparisonQuery(
                request.CarrierId, request.FromDate, request.ToDate,
                request.RouteFilter, null);
            result.MarketComparison = await _mediator.Send(marketQuery, cancellationToken);
        }

        // Generate quoting trends (if requested)
        if (request.IncludeTrends)
        {
            result.QuotingTrends = GenerateQuotingTrends(request.FromDate, request.ToDate, result);
        }

        // Generate insights and recommendations
        result.Insights = GenerateQuotingInsights(result);
        result.Recommendations = GenerateQuotingRecommendations(result);

        return result;
    }

    private CarrierQuotingTrendsDto GenerateQuotingTrends(DateTime fromDate, DateTime toDate, CarrierQuotingHistoryDto data)
    {
        return new CarrierQuotingTrendsDto
        {
            QuoteVolumeTrends = data.RFQResponseHistory?.ResponseTrends?.Select(rt => new QuoteVolumeTrendDto
            {
                Date = rt.Date,
                QuoteVolume = rt.RFQsResponded,
                RFQVolume = rt.RFQsReceived,
                ResponseRate = rt.ResponseRate
            }).ToList() ?? new List<QuoteVolumeTrendDto>(),

            SuccessRateTrends = data.QuoteSuccessRate?.SuccessTrends ?? new List<QuoteSuccessTrendDto>(),
            PricingTrends = data.PricingAnalytics?.PricingTrends ?? new List<PricingTrendDto>(),

            ResponseTimeTrends = data.RFQResponseHistory?.ResponseTrends?.Select(rt => new ResponseTimeTrendDto
            {
                Date = rt.Date,
                AverageResponseTime = rt.AverageResponseTime,
                MedianResponseTime = rt.AverageResponseTime, // Simplified
                ResponseTimeImprovement = 0 // Would calculate based on historical data
            }).ToList() ?? new List<ResponseTimeTrendDto>(),

            MarketShareTrends = GenerateMarketShareTrends(fromDate, toDate)
        };
    }

    private List<MarketShareTrendDto> GenerateMarketShareTrends(DateTime fromDate, DateTime toDate)
    {
        var days = (toDate - fromDate).Days;
        var trends = new List<MarketShareTrendDto>();
        var baseMarketShare = 6.5m;

        for (var date = fromDate; date <= toDate; date = date.AddDays(7)) // Weekly trends
        {
            var variance = (decimal)(new Random().NextDouble() * 0.4 - 0.2); // ±0.2% variance
            var marketShare = Math.Max(0, baseMarketShare + variance);
            var marketShareChange = variance;
            var totalMarketVolume = new Random().Next(800, 1200);

            trends.Add(new MarketShareTrendDto
            {
                Date = date,
                MarketShare = marketShare,
                MarketShareChange = marketShareChange,
                TotalMarketVolume = totalMarketVolume
            });
        }

        return trends;
    }

    private List<CarrierQuotingInsightDto> GenerateQuotingInsights(CarrierQuotingHistoryDto data)
    {
        var insights = new List<CarrierQuotingInsightDto>();

        // Response time insight
        if (data.RFQResponseHistory != null && data.RFQResponseHistory.AverageResponseTime.TotalHours > 6)
        {
            insights.Add(new CarrierQuotingInsightDto
            {
                InsightType = "Response Time",
                Title = "Response Time Optimization Opportunity",
                Description = "Your average response time is above the optimal 4-hour window, which may be impacting quote success rates.",
                Impact = "Medium",
                SupportingData = new List<string>
                {
                    $"Average response time: {data.RFQResponseHistory.AverageResponseTime.TotalHours:F1} hours",
                    "Optimal response time: <4 hours",
                    "Quick responders have 15% higher success rates"
                },
                GeneratedAt = DateTime.UtcNow
            });
        }

        // Pricing competitiveness insight
        if (data.MarketComparison != null && data.MarketComparison.PriceCompetitiveness > 105)
        {
            insights.Add(new CarrierQuotingInsightDto
            {
                InsightType = "Pricing",
                Title = "Pricing Competitiveness Alert",
                Description = "Your pricing is significantly above market average, which may be reducing quote acceptance rates.",
                Impact = "High",
                SupportingData = new List<string>
                {
                    $"Your average price: ₹{data.MarketComparison.CarrierAveragePrice:N0}",
                    $"Market average: ₹{data.MarketComparison.MarketAveragePrice:N0}",
                    $"Price competitiveness: {data.MarketComparison.PriceCompetitiveness:F1}%"
                },
                GeneratedAt = DateTime.UtcNow
            });
        }

        return insights;
    }

    private List<CarrierQuotingRecommendationDto> GenerateQuotingRecommendations(CarrierQuotingHistoryDto data)
    {
        var recommendations = new List<CarrierQuotingRecommendationDto>();

        // Response time recommendation
        if (data.RFQResponseHistory != null && data.RFQResponseHistory.ResponseRate < 90)
        {
            recommendations.Add(new CarrierQuotingRecommendationDto
            {
                RecommendationType = "Process Improvement",
                Title = "Improve RFQ Response Rate",
                Description = "Implement automated RFQ monitoring and response workflows to increase response rate.",
                Priority = "High",
                ExpectedImpact = 15.0m,
                ActionItems = new List<string>
                {
                    "Set up RFQ notification alerts",
                    "Create response templates for common routes",
                    "Implement automated pricing calculations",
                    "Train team on quick response protocols"
                },
                Timeline = "2-4 weeks"
            });
        }

        // Pricing optimization recommendation
        if (data.QuoteSuccessRate != null && data.QuoteSuccessRate.OverallSuccessRate < 70)
        {
            recommendations.Add(new CarrierQuotingRecommendationDto
            {
                RecommendationType = "Pricing Strategy",
                Title = "Optimize Pricing Strategy",
                Description = "Adjust pricing strategy based on route performance and market conditions to improve quote success rate.",
                Priority = "High",
                ExpectedImpact = 20.0m,
                ActionItems = new List<string>
                {
                    "Analyze route-specific pricing performance",
                    "Implement dynamic pricing based on demand",
                    "Monitor competitor pricing regularly",
                    "Test different pricing strategies on low-performing routes"
                },
                Timeline = "4-6 weeks"
            });
        }

        return recommendations;
    }
}