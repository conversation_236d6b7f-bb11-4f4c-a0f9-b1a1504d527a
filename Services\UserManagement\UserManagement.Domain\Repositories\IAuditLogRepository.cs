using UserManagement.Domain.Entities;

namespace UserManagement.Domain.Repositories
{
    public interface IAuditLogRepository
    {
        Task<AuditLog> AddAsync(AuditLog auditLog);
        Task<AuditLog?> GetByIdAsync(Guid id);
        Task<IEnumerable<AuditLog>> GetByUserIdAsync(Guid userId, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> GetByEntityAsync(string entityType, Guid entityId, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> GetByActionAsync(AuditAction action, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> GetBySeverityAsync(AuditSeverity severity, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 50);
        Task<IEnumerable<AuditLog>> SearchAsync(AuditLogSearchCriteria criteria);
        Task<int> GetCountAsync();
        Task<int> GetCountByUserIdAsync(Guid userId);
        Task<int> GetCountByEntityAsync(string entityType, Guid entityId);
        Task<int> GetCountByActionAsync(AuditAction action);
        Task<int> GetCountBySeverityAsync(AuditSeverity severity);
        Task<int> GetCountByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<AuditLog>> GetRecentAsync(int count = 100);
        Task<IEnumerable<AuditLog>> GetCriticalEventsAsync(int pageNumber = 1, int pageSize = 50);
        Task<bool> ExistsAsync(Guid id);
    }

    public class AuditLogSearchCriteria
    {
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public string? UserRole { get; set; }
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public AuditAction? Action { get; set; }
        public AuditSeverity? Severity { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? IpAddress { get; set; }
        public string? SessionId { get; set; }
        public string? CorrelationId { get; set; }
        public string? SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string? SortBy { get; set; } = "Timestamp";
        public bool SortDescending { get; set; } = true;
    }
}
