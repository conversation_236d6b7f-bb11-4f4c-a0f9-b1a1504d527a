using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface INotificationTemplateService
    {
        /// <summary>
        /// Get a notification template for specific type, channel, and language
        /// </summary>
        Task<NotificationTemplate?> GetTemplateAsync(
            NotificationType type, 
            string channel, 
            string language = "en", 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Render a template with provided variables
        /// </summary>
        Task<RenderedNotification> RenderTemplateAsync(
            NotificationType type,
            string channel,
            Dictionary<string, string> variables,
            string language = "en",
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get template variables for subscription notifications
        /// </summary>
        Dictionary<string, string> GetSubscriptionVariables(
            Subscription subscription,
            Plan? plan = null,
            Payment? payment = null,
            SubscriptionPaymentProof? paymentProof = null);

        /// <summary>
        /// Get template variables for user notifications
        /// </summary>
        Dictionary<string, string> GetUserVariables(Guid userId, string? userName = null, string? userEmail = null);

        /// <summary>
        /// Create default templates for subscription notifications
        /// </summary>
        Task CreateDefaultTemplatesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate template variables
        /// </summary>
        Task<List<string>> ValidateTemplateVariablesAsync(
            NotificationType type,
            string channel,
            Dictionary<string, string> variables,
            string language = "en",
            CancellationToken cancellationToken = default);
    }

    public class RenderedNotification
    {
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string Channel { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public string Language { get; set; } = "en";
        public Dictionary<string, string> Metadata { get; set; } = new();
    }
}
