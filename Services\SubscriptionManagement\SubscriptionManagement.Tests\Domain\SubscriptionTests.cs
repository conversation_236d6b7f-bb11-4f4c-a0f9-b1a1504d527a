using FluentAssertions;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Domain
{
    public class SubscriptionTests
    {
        private Plan CreateTestPlan()
        {
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            return new Plan(
                "Test Plan",
                "Test Description",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits);
        }

        [Fact]
        public void CreateSubscription_WithValidData_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();

            // Act
            var subscription = new Subscription(userId, plan);

            // Assert
            subscription.UserId.Should().Be(userId);
            subscription.PlanId.Should().Be(plan.Id);
            subscription.Status.Should().Be(SubscriptionStatus.Pending);
            subscription.AutoRenew.Should().BeTrue();
            subscription.CurrentPrice.Should().Be(plan.Price);
        }

        [Fact]
        public void CreateSubscription_WithInactivePlan_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            plan.Deactivate();

            // Act & Assert
            var action = () => new Subscription(userId, plan);
            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Cannot create subscription for inactive plan");
        }

        [Fact]
        public void Activate_PendingSubscription_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);

            // Act
            subscription.Activate();

            // Assert
            subscription.Status.Should().Be(SubscriptionStatus.Active);
        }

        [Fact]
        public void Activate_NonPendingSubscription_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            // Act & Assert
            var action = () => subscription.Activate();
            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Only pending subscriptions can be activated");
        }

        [Fact]
        public void Cancel_ActiveSubscription_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            var reason = "User requested cancellation";

            // Act
            subscription.Cancel(reason);

            // Assert
            subscription.Status.Should().Be(SubscriptionStatus.Cancelled);
            subscription.CancellationReason.Should().Be(reason);
            subscription.AutoRenew.Should().BeFalse();
            subscription.CancelledAt.Should().NotBeNull();
        }

        [Fact]
        public void IsInTrial_WithTrialEndDate_ShouldReturnCorrectValue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var trialEndDate = DateTime.UtcNow.AddDays(7);
            var subscription = new Subscription(userId, plan, trialEndDate: trialEndDate);

            // Act & Assert
            subscription.IsInTrial().Should().BeTrue();
        }

        [Fact]
        public void IsInTrial_WithoutTrialEndDate_ShouldReturnFalse()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);

            // Act & Assert
            subscription.IsInTrial().Should().BeFalse();
        }

        [Fact]
        public void IsDueForRenewal_ActiveSubscriptionPastBillingDate_ShouldReturnTrue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var startDate = DateTime.UtcNow.AddDays(-35); // 35 days ago
            var subscription = new Subscription(userId, plan, startDate);
            subscription.Activate();

            // Act & Assert
            subscription.IsDueForRenewal().Should().BeTrue();
        }

        #region Extension Tests

        [Fact]
        public void ExtendSubscription_WithValidData_ShouldSucceed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            var originalNextBillingDate = subscription.NextBillingDate;
            var extensionDays = 30;
            var reason = "Customer requested extension";

            // Act
            subscription.ExtendSubscription(extensionDays, reason, adminUserId, false);

            // Assert
            subscription.NextBillingDate.Should().Be(originalNextBillingDate.AddDays(extensionDays));
            subscription.LastExtendedAt.Should().NotBeNull();
            subscription.ExtensionReason.Should().Be(reason);
            subscription.ExtendedByUserId.Should().Be(adminUserId);
            subscription.GracePeriodEndDate.Should().BeNull(); // Not a grace period extension
        }

        [Fact]
        public void ExtendSubscription_AsGracePeriod_ShouldSetGracePeriodEndDate()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            var originalNextBillingDate = subscription.NextBillingDate;
            var extensionDays = 15;
            var reason = "Grace period for payment issues";

            // Act
            subscription.ExtendSubscription(extensionDays, reason, adminUserId, true);

            // Assert
            subscription.NextBillingDate.Should().Be(originalNextBillingDate); // Billing date unchanged
            subscription.GracePeriodEndDate.Should().Be(originalNextBillingDate.AddDays(extensionDays));
            subscription.LastExtendedAt.Should().NotBeNull();
            subscription.ExtensionReason.Should().Be(reason);
            subscription.ExtendedByUserId.Should().Be(adminUserId);
        }

        [Fact]
        public void ExtendSubscription_WithZeroDays_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            // Act & Assert
            var action = () => subscription.ExtendSubscription(0, "Invalid extension", adminUserId, false);
            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Extension days must be greater than 0");
        }

        [Fact]
        public void ExtendSubscription_WithExcessiveDays_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            // Act & Assert
            var action = () => subscription.ExtendSubscription(100, "Too many days", adminUserId, false);
            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Extension cannot exceed 90 days");
        }

        [Fact]
        public void ExtendSubscription_CancelledSubscription_ShouldThrowException()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            subscription.Cancel("User cancelled");

            // Act & Assert
            var action = () => subscription.ExtendSubscription(30, "Extension attempt", adminUserId, false);
            action.Should().Throw<SubscriptionDomainException>()
                .WithMessage("Cannot extend a cancelled subscription");
        }

        [Fact]
        public void IsInGracePeriod_WithActiveGracePeriod_ShouldReturnTrue()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            subscription.ExtendSubscription(15, "Grace period", adminUserId, true);

            // Act & Assert
            subscription.IsInGracePeriod().Should().BeTrue();
        }

        [Fact]
        public void IsInGracePeriod_WithoutGracePeriod_ShouldReturnFalse()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            // Act & Assert
            subscription.IsInGracePeriod().Should().BeFalse();
        }

        [Fact]
        public void GetDaysRemainingInGracePeriod_WithActiveGracePeriod_ShouldReturnCorrectDays()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var adminUserId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();
            var extensionDays = 15;
            subscription.ExtendSubscription(extensionDays, "Grace period", adminUserId, true);

            // Act
            var remainingDays = subscription.GetDaysRemainingInGracePeriod();

            // Assert
            remainingDays.Should().BeGreaterThan(0);
            remainingDays.Should().BeLessOrEqualTo(extensionDays);
        }

        [Fact]
        public void GetDaysRemainingInGracePeriod_WithoutGracePeriod_ShouldReturnZero()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var plan = CreateTestPlan();
            var subscription = new Subscription(userId, plan);
            subscription.Activate();

            // Act
            var remainingDays = subscription.GetDaysRemainingInGracePeriod();

            // Assert
            remainingDays.Should().Be(0);
        }

        #endregion
    }
}
