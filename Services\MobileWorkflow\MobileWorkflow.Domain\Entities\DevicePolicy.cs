using Shared.Domain.Common;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class DevicePolicy : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Description { get; private set; }
    public string Category { get; private set; } // Security, Compliance, Configuration, Application
    public string Platform { get; private set; } // iOS, Android, Windows, All
    public string Version { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public int Priority { get; private set; } // Higher number = higher priority
    public Dictionary<string, object> Rules { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, object> Enforcement { get; private set; }
    public Dictionary<string, object> Conditions { get; private set; }
    public List<string> TargetGroups { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual ICollection<DevicePolicyAssignment> Assignments { get; private set; } = new List<DevicePolicyAssignment>();

    private DevicePolicy() { } // EF Core

    public DevicePolicy(
        string name,
        string displayName,
        string description,
        string category,
        string platform,
        string version,
        Dictionary<string, object> rules,
        string createdBy)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        DisplayName = displayName ?? throw new ArgumentNullException(nameof(displayName));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Category = category ?? throw new ArgumentNullException(nameof(category));
        Platform = platform ?? throw new ArgumentNullException(nameof(platform));
        Version = version ?? throw new ArgumentNullException(nameof(version));
        Rules = rules ?? throw new ArgumentNullException(nameof(rules));
        CreatedBy = createdBy ?? throw new ArgumentNullException(nameof(createdBy));

        IsActive = true;
        IsDefault = false;
        Priority = 100;
        CreatedAt = DateTime.UtcNow;
        Configuration = new Dictionary<string, object>();
        Enforcement = new Dictionary<string, object>();
        Conditions = new Dictionary<string, object>();
        TargetGroups = new List<string>();
        Metadata = new Dictionary<string, object>();

        SetDefaultEnforcement();

        AddDomainEvent(new DevicePolicyCreatedEvent(Id, Name, Category, Platform, CreatedBy));
    }

    public void UpdateRules(Dictionary<string, object> newRules, string updatedBy)
    {
        Rules = newRules ?? throw new ArgumentNullException(nameof(newRules));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyRulesUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConfiguration(Dictionary<string, object> configuration, string updatedBy)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyConfigurationUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateEnforcement(Dictionary<string, object> enforcement, string updatedBy)
    {
        Enforcement = enforcement ?? throw new ArgumentNullException(nameof(enforcement));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyEnforcementUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void UpdateConditions(Dictionary<string, object> conditions, string updatedBy)
    {
        Conditions = conditions ?? throw new ArgumentNullException(nameof(conditions));
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyConditionsUpdatedEvent(Id, Name, UpdatedBy));
    }

    public void SetPriority(int priority, string updatedBy)
    {
        var oldPriority = Priority;
        Priority = priority;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyPriorityChangedEvent(Id, Name, oldPriority, priority, UpdatedBy));
    }

    public void SetAsDefault(string updatedBy)
    {
        IsDefault = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicySetAsDefaultEvent(Id, Name, UpdatedBy));
    }

    public void RemoveAsDefault(string updatedBy)
    {
        IsDefault = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyRemovedAsDefaultEvent(Id, Name, UpdatedBy));
    }

    public void Activate(string updatedBy)
    {
        IsActive = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyActivatedEvent(Id, Name, UpdatedBy));
    }

    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new DevicePolicyDeactivatedEvent(Id, Name, UpdatedBy));
    }

    public void AddTargetGroup(string groupName)
    {
        if (!TargetGroups.Contains(groupName, StringComparer.OrdinalIgnoreCase))
        {
            TargetGroups.Add(groupName);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void RemoveTargetGroup(string groupName)
    {
        if (TargetGroups.RemoveAll(g => g.Equals(groupName, StringComparison.OrdinalIgnoreCase)) > 0)
        {
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public bool AppliesToPlatform(string platform)
    {
        return Platform.Equals("All", StringComparison.OrdinalIgnoreCase) ||
               Platform.Equals(platform, StringComparison.OrdinalIgnoreCase);
    }

    public bool AppliesToGroup(string groupName)
    {
        return TargetGroups.Count == 0 || // No groups means applies to all
               TargetGroups.Contains(groupName, StringComparer.OrdinalIgnoreCase);
    }

    public bool EvaluateConditions(Dictionary<string, object> deviceContext)
    {
        if (Conditions.Count == 0)
        {
            return true; // No conditions means always applies
        }

        foreach (var condition in Conditions)
        {
            if (!EvaluateCondition(condition.Key, condition.Value, deviceContext))
            {
                return false;
            }
        }

        return true;
    }

    public PolicyComplianceResult CheckCompliance(Dictionary<string, object> deviceState)
    {
        var result = new PolicyComplianceResult
        {
            PolicyId = Id,
            PolicyName = Name,
            IsCompliant = true,
            Violations = new List<string>(),
            CheckedAt = DateTime.UtcNow
        };

        foreach (var rule in Rules)
        {
            var ruleResult = EvaluateRule(rule.Key, rule.Value, deviceState);
            if (!ruleResult.IsCompliant)
            {
                result.IsCompliant = false;
                result.Violations.AddRange(ruleResult.Violations);
            }
        }

        return result;
    }

    public Dictionary<string, object> GetEnforcementActions()
    {
        var actions = new Dictionary<string, object>();

        if (Enforcement.TryGetValue("block_access", out var blockAccess) && blockAccess is bool block && block)
        {
            actions["blockAccess"] = true;
        }

        if (Enforcement.TryGetValue("notify_admin", out var notifyAdmin) && notifyAdmin is bool notify && notify)
        {
            actions["notifyAdmin"] = true;
        }

        if (Enforcement.TryGetValue("quarantine", out var quarantine) && quarantine is bool quar && quar)
        {
            actions["quarantine"] = true;
        }

        if (Enforcement.TryGetValue("remote_wipe", out var remoteWipe) && remoteWipe is bool wipe && wipe)
        {
            actions["remoteWipe"] = true;
        }

        if (Enforcement.TryGetValue("grace_period_hours", out var gracePeriod))
        {
            actions["gracePeriodHours"] = gracePeriod;
        }

        return actions;
    }

    private bool EvaluateCondition(string conditionKey, object conditionValue, Dictionary<string, object> deviceContext)
    {
        if (!deviceContext.TryGetValue(conditionKey, out var contextValue))
        {
            return false;
        }

        if (conditionValue is Dictionary<string, object> conditionDict)
        {
            // Handle complex conditions like {"os_version": {"min": "14.0", "max": "16.0"}}
            if (conditionDict.TryGetValue("min", out var minValue) && conditionDict.TryGetValue("max", out var maxValue))
            {
                if (contextValue is IComparable comparableContext &&
                    minValue is IComparable comparableMin &&
                    maxValue is IComparable comparableMax)
                {
                    return comparableContext.CompareTo(comparableMin) >= 0 &&
                           comparableContext.CompareTo(comparableMax) <= 0;
                }
            }
        }
        else if (conditionValue is List<object> conditionList)
        {
            // Handle list conditions like {"device_type": ["iPhone", "iPad"]}
            return conditionList.Contains(contextValue);
        }
        else
        {
            // Simple equality check
            return Equals(conditionValue, contextValue);
        }

        return false;
    }

    private PolicyRuleResult EvaluateRule(string ruleName, object ruleValue, Dictionary<string, object> deviceState)
    {
        var result = new PolicyRuleResult
        {
            RuleName = ruleName,
            IsCompliant = true,
            Violations = new List<string>()
        };

        switch (ruleName.ToLowerInvariant())
        {
            case "passcode_required":
                if (ruleValue is bool passcodeRequired && passcodeRequired)
                {
                    if (!deviceState.TryGetValue("passcode_enabled", out var passcodeEnabled) ||
                        !(passcodeEnabled is bool enabled && enabled))
                    {
                        result.IsCompliant = false;
                        result.Violations.Add("Device passcode is required but not enabled");
                    }
                }
                break;

            case "min_passcode_length":
                if (ruleValue is int minLength)
                {
                    if (deviceState.TryGetValue("passcode_length", out var lengthObj) &&
                        lengthObj is int actualLength && actualLength < minLength)
                    {
                        result.IsCompliant = false;
                        result.Violations.Add($"Passcode length {actualLength} is below minimum required {minLength}");
                    }
                }
                break;

            case "encryption_required":
                if (ruleValue is bool encryptionRequired && encryptionRequired)
                {
                    if (!deviceState.TryGetValue("encryption_enabled", out var encryptionEnabled) ||
                        !(encryptionEnabled is bool enabled && enabled))
                    {
                        result.IsCompliant = false;
                        result.Violations.Add("Device encryption is required but not enabled");
                    }
                }
                break;

            case "jailbreak_detection":
                if (ruleValue is bool detectJailbreak && detectJailbreak)
                {
                    if (deviceState.TryGetValue("jailbroken", out var jailbroken) &&
                        jailbroken is bool isJailbroken && isJailbroken)
                    {
                        result.IsCompliant = false;
                        result.Violations.Add("Device is jailbroken/rooted");
                    }
                }
                break;

            case "min_os_version":
                if (ruleValue is string minOsVersion)
                {
                    if (deviceState.TryGetValue("os_version", out var osVersionObj) &&
                        osVersionObj is string actualOsVersion)
                    {
                        if (CompareVersions(actualOsVersion, minOsVersion) < 0)
                        {
                            result.IsCompliant = false;
                            result.Violations.Add($"OS version {actualOsVersion} is below minimum required {minOsVersion}");
                        }
                    }
                }
                break;

            case "allowed_apps":
                if (ruleValue is List<object> allowedApps)
                {
                    if (deviceState.TryGetValue("installed_apps", out var installedAppsObj) &&
                        installedAppsObj is List<object> installedApps)
                    {
                        var unauthorizedApps = installedApps.Except(allowedApps).ToList();
                        if (unauthorizedApps.Any())
                        {
                            result.IsCompliant = false;
                            result.Violations.Add($"Unauthorized apps detected: {string.Join(", ", unauthorizedApps)}");
                        }
                    }
                }
                break;

            case "blocked_apps":
                if (ruleValue is List<object> blockedApps)
                {
                    if (deviceState.TryGetValue("installed_apps", out var installedAppsObj) &&
                        installedAppsObj is List<object> installedApps)
                    {
                        var foundBlockedApps = installedApps.Intersect(blockedApps).ToList();
                        if (foundBlockedApps.Any())
                        {
                            result.IsCompliant = false;
                            result.Violations.Add($"Blocked apps detected: {string.Join(", ", foundBlockedApps)}");
                        }
                    }
                }
                break;
        }

        return result;
    }

    private int CompareVersions(string version1, string version2)
    {
        var v1Parts = version1.Split('.').Select(int.Parse).ToArray();
        var v2Parts = version2.Split('.').Select(int.Parse).ToArray();

        var maxLength = Math.Max(v1Parts.Length, v2Parts.Length);

        for (int i = 0; i < maxLength; i++)
        {
            var v1Part = i < v1Parts.Length ? v1Parts[i] : 0;
            var v2Part = i < v2Parts.Length ? v2Parts[i] : 0;

            var comparison = v1Part.CompareTo(v2Part);
            if (comparison != 0)
            {
                return comparison;
            }
        }

        return 0;
    }

    private void SetDefaultEnforcement()
    {
        Enforcement["block_access"] = false;
        Enforcement["notify_admin"] = true;
        Enforcement["quarantine"] = false;
        Enforcement["remote_wipe"] = false;
        Enforcement["grace_period_hours"] = 24;
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public T? GetMetadata<T>(string key, T? defaultValue = default)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetRule<T>(string key, T? defaultValue = default)
    {
        if (Rules.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    public T? GetConfiguration<T>(string key, T? defaultValue = default)
    {
        if (Configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }
}

public class PolicyComplianceResult
{
    public Guid PolicyId { get; set; }
    public string PolicyName { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public List<string> Violations { get; set; } = new();
    public DateTime CheckedAt { get; set; }
}

public class PolicyRuleResult
{
    public string RuleName { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public List<string> Violations { get; set; } = new();
}


