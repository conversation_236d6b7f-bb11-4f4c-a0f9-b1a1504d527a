# Simple build fix script
Write-Host "Fixing build issues..." -ForegroundColor Green

# Add MediatR to Domain projects that need it
Write-Host "Adding MediatR to SubscriptionManagement.Domain..." -ForegroundColor Yellow
dotnet add "Services\SubscriptionManagement\SubscriptionManagement.Domain\SubscriptionManagement.Domain.csproj" package MediatR --version 12.1.1

# Add Microsoft.Extensions.Configuration to projects that need it
Write-Host "Adding Microsoft.Extensions.Configuration..." -ForegroundColor Yellow
dotnet add "Services\UserManagement\UserManagement.Application\UserManagement.Application.csproj" package Microsoft.Extensions.Configuration.Abstractions --version 7.0.0

# Add Microsoft.AspNetCore.Http.Features for IFormFile
Write-Host "Adding Microsoft.AspNetCore.Http.Features..." -ForegroundColor Yellow
dotnet add "Services\UserManagement\UserManagement.Application\UserManagement.Application.csproj" package Microsoft.AspNetCore.Http.Features --version 5.0.17

# Add project references for shared libraries
Write-Host "Adding shared project references..." -ForegroundColor Yellow

# Add Shared.Domain references to all Domain projects
$domainProjects = Get-ChildItem -Path "Services" -Recurse -Name "*.Domain.csproj"
foreach ($project in $domainProjects) {
    $projectPath = "Services\$project"
    Write-Host "Adding Shared.Domain reference to $projectPath" -ForegroundColor Cyan
    dotnet add $projectPath reference "Shared\Shared.Domain\Shared.Domain.csproj"
    dotnet add $projectPath reference "Shared\TLI.Shared.Domain\TLI.Shared.Domain.csproj"
}

# Add Shared.Messaging references to all Application projects
$appProjects = Get-ChildItem -Path "Services" -Recurse -Name "*.Application.csproj"
foreach ($project in $appProjects) {
    $projectPath = "Services\$project"
    Write-Host "Adding Shared references to $projectPath" -ForegroundColor Cyan
    dotnet add $projectPath reference "Shared\Shared.Domain\Shared.Domain.csproj"
    dotnet add $projectPath reference "Shared\Shared.Messaging\Shared.Messaging.csproj"
    dotnet add $projectPath reference "Shared\TLI.Shared.Domain\TLI.Shared.Domain.csproj"
}

# Add Shared.Infrastructure references to all Infrastructure projects
$infraProjects = Get-ChildItem -Path "Services" -Recurse -Name "*.Infrastructure.csproj"
foreach ($project in $infraProjects) {
    $projectPath = "Services\$project"
    Write-Host "Adding Shared.Infrastructure reference to $projectPath" -ForegroundColor Cyan
    dotnet add $projectPath reference "Shared\Shared.Infrastructure\Shared.Infrastructure.csproj"
    dotnet add $projectPath reference "Shared\Shared.Messaging\Shared.Messaging.csproj"
}

Write-Host "Basic fixes completed!" -ForegroundColor Green
