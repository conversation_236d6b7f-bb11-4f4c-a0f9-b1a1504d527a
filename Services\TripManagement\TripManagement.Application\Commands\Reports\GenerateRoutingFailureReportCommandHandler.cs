using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Application.DTOs.Reports;
using TripManagement.Domain.Interfaces;
using TripManagement.Domain.Enums;
using System.Diagnostics;

namespace TripManagement.Application.Commands.Reports;

/// <summary>
/// Handler for generating routing failure reports
/// </summary>
public class GenerateRoutingFailureReportCommandHandler : IRequestHandler<GenerateRoutingFailureReportCommand, RoutingFailureReportDto>
{
    private readonly IRoutingFailureRepository _routingFailureRepository;
    private readonly ITripRepository _tripRepository;
    private readonly ILogger<GenerateRoutingFailureReportCommandHandler> _logger;

    public GenerateRoutingFailureReportCommandHandler(
        IRoutingFailureRepository routingFailureRepository,
        ITripRepository tripRepository,
        ILogger<GenerateRoutingFailureReportCommandHandler> logger)
    {
        _routingFailureRepository = routingFailureRepository;
        _tripRepository = tripRepository;
        _logger = logger;
    }

    public async Task<RoutingFailureReportDto> Handle(GenerateRoutingFailureReportCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Generating routing failure report for user {RequestedBy}", request.RequestedBy);

            // Set default date range if not provided
            var fromDate = request.FromDate ?? DateTime.UtcNow.AddMonths(-1);
            var toDate = request.ToDate ?? DateTime.UtcNow;

            var report = new RoutingFailureReportDto
            {
                ReportId = Guid.NewGuid(),
                GeneratedAt = DateTime.UtcNow,
                FromDate = fromDate,
                ToDate = toDate
            };

            // Generate report sections based on request
            await GenerateReportSummary(report, request, fromDate, toDate, cancellationToken);

            if (request.IncludeFailureDetails)
            {
                await GenerateFailureDetails(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludePatternAnalysis)
            {
                await GeneratePatternAnalysis(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeImpactAnalysis)
            {
                await GenerateImpactAnalysis(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeTrendAnalysis)
            {
                await GenerateTrendAnalysis(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeRootCauseAnalysis)
            {
                await GenerateRootCauseAnalysis(report, request, fromDate, toDate, cancellationToken);
            }

            // Set metadata
            stopwatch.Stop();
            report.Metadata = new ReportMetadataDto
            {
                RequestedBy = request.RequestedBy,
                RequestedByRole = request.RequestedByRole,
                RequestedAt = DateTime.UtcNow,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent,
                GenerationTime = stopwatch.Elapsed,
                TotalRecords = report.FailureDetails.Count,
                IsFiltered = HasFilters(request),
                FilterCriteria = GetFilterCriteria(request),
                DataSources = new List<string> { "RoutingFailures", "Trips" },
                AnalysisMethod = "Statistical Analysis with Pattern Recognition"
            };

            _logger.LogInformation("Successfully generated routing failure report {ReportId} in {ElapsedMs}ms", 
                report.ReportId, stopwatch.ElapsedMilliseconds);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating routing failure report");
            throw;
        }
    }

    private async Task GenerateReportSummary(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request, 
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var failures = await _routingFailureRepository.GetFailuresForReportAsync(
            fromDate, toDate, request.FailureTypes, request.Severities, request.Statuses, request.IncludeResolved, cancellationToken);

        var failuresList = failures.ToList();

        report.Summary = new RoutingFailureReportSummaryDto
        {
            TotalFailures = failuresList.Count,
            OpenFailures = failuresList.Count(f => f.Status == FailureStatus.Open || f.Status == FailureStatus.InProgress),
            ResolvedFailures = failuresList.Count(f => f.Status == FailureStatus.Resolved),
            CriticalFailures = failuresList.Count(f => f.Severity == FailureSeverity.Critical),
            RecurringFailures = failuresList.Count(f => f.IsRecurring),
            FailuresByType = failuresList.GroupBy(f => f.FailureType).ToDictionary(g => g.Key, g => g.Count()),
            FailuresBySeverity = failuresList.GroupBy(f => f.Severity).ToDictionary(g => g.Key, g => g.Count()),
            FailuresByCategory = failuresList.GroupBy(f => f.Category).ToDictionary(g => g.Key, g => g.Count()),
            TotalImpactCost = failuresList.Where(f => f.ImpactCost.HasValue).Sum(f => f.ImpactCost!.Value),
            TotalDelayTime = TimeSpan.FromTicks(failuresList.Where(f => f.DelayTime.HasValue).Sum(f => f.DelayTime!.Value.Ticks)),
            TotalAffectedCustomers = failuresList.Where(f => f.AffectedCustomers.HasValue).Sum(f => f.AffectedCustomers!.Value),
            AverageResolutionTimeHours = (decimal)(failuresList.Where(f => f.ResolutionTime.HasValue).Any() 
                ? failuresList.Where(f => f.ResolutionTime.HasValue).Average(f => f.ResolutionTime!.Value.TotalHours) 
                : 0),
            ResolutionRate = failuresList.Count > 0 ? (decimal)failuresList.Count(f => f.Status == FailureStatus.Resolved) / failuresList.Count * 100 : 0,
            ReportPeriodStart = fromDate,
            ReportPeriodEnd = toDate
        };
    }

    private async Task GenerateFailureDetails(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var failures = await _routingFailureRepository.GetFailuresForReportAsync(
            fromDate, toDate, request.FailureTypes, request.Severities, request.Statuses, request.IncludeResolved, cancellationToken);

        var failureDetails = failures.Take(request.MaxRecords ?? 1000).Select(f => new FailureDetailDto
        {
            FailureId = f.Id,
            TripId = f.TripId,
            OrderNumber = f.OrderNumber,
            FailureType = f.FailureType,
            Severity = f.Severity,
            FailureReason = f.FailureReason,
            Description = f.Description,
            Category = f.Category,
            FailureTime = f.FailureTime,
            DetectedAt = f.DetectedAt,
            ResolvedAt = f.ResolvedAt,
            ResolutionTime = f.ResolutionTime,
            Status = f.Status,
            ResolutionAction = f.ResolutionAction,
            ImpactCost = f.ImpactCost,
            DelayTime = f.DelayTime,
            AffectedCustomers = f.AffectedCustomers,
            IsRecurring = f.IsRecurring,
            RootCause = f.RootCause,
            PreventiveAction = f.PreventiveAction,
            ImpactLevel = f.GetImpactLevel(),
            AgeInHours = (int)f.GetAge().TotalHours,
            IsLongRunning = f.IsLongRunning(),
            TechnicalDetails = f.TechnicalDetails
        }).ToList();

        report.FailureDetails = failureDetails;
    }

    private async Task GeneratePatternAnalysis(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var failures = await _routingFailureRepository.GetFailuresForReportAsync(
            fromDate, toDate, request.FailureTypes, request.Severities, request.Statuses, request.IncludeResolved, cancellationToken);

        var failuresList = failures.ToList();
        var patterns = new List<FailurePatternAnalysisDto>();

        // Time-based patterns
        var hourlyPatterns = failuresList
            .GroupBy(f => f.FailureTime.Hour)
            .Where(g => g.Count() > 1)
            .Select(g => new FailurePatternAnalysisDto
            {
                PatternType = "Time",
                PatternDescription = $"Failures frequently occur at hour {g.Key}:00",
                Frequency = g.Count(),
                Percentage = (decimal)g.Count() / failuresList.Count * 100,
                AffectedTrips = g.Select(f => f.TripId.ToString()).ToList(),
                PatternDetails = new Dictionary<string, object> { ["Hour"] = g.Key },
                Recommendation = $"Investigate system load and processes around {g.Key}:00",
                ConfidenceLevel = g.Count() > 5 ? 0.8m : 0.6m,
                FirstOccurrence = g.Min(f => f.FailureTime),
                LastOccurrence = g.Max(f => f.FailureTime),
                AverageInterval = TimeSpan.FromHours(24) // Daily pattern
            }).ToList();

        patterns.AddRange(hourlyPatterns);

        // Type-based patterns
        var typePatterns = failuresList
            .GroupBy(f => f.FailureType)
            .Where(g => g.Count() > 2)
            .Select(g => new FailurePatternAnalysisDto
            {
                PatternType = "Type",
                PatternDescription = $"High frequency of {g.Key} failures",
                Frequency = g.Count(),
                Percentage = (decimal)g.Count() / failuresList.Count * 100,
                AffectedTrips = g.Select(f => f.TripId.ToString()).ToList(),
                PatternDetails = new Dictionary<string, object> { ["FailureType"] = g.Key.ToString() },
                Recommendation = GetRecommendationForFailureType(g.Key),
                ConfidenceLevel = 0.9m,
                FirstOccurrence = g.Min(f => f.FailureTime),
                LastOccurrence = g.Max(f => f.FailureTime),
                AverageInterval = CalculateAverageInterval(g.OrderBy(f => f.FailureTime).ToList())
            }).ToList();

        patterns.AddRange(typePatterns);

        report.PatternAnalysis = patterns;
    }

    private async Task GenerateImpactAnalysis(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var failures = await _routingFailureRepository.GetFailuresForReportAsync(
            fromDate, toDate, request.FailureTypes, request.Severities, request.Statuses, request.IncludeResolved, cancellationToken);

        var failuresList = failures.ToList();
        var impactAnalysis = new List<ImpactAnalysisDto>();

        // Financial impact
        var totalFinancialImpact = failuresList.Where(f => f.ImpactCost.HasValue).Sum(f => f.ImpactCost!.Value);
        if (totalFinancialImpact > 0)
        {
            impactAnalysis.Add(new ImpactAnalysisDto
            {
                ImpactCategory = "Financial",
                TotalImpact = totalFinancialImpact,
                ImpactUnit = "USD",
                ImpactByFailureType = failuresList.Where(f => f.ImpactCost.HasValue)
                    .GroupBy(f => f.FailureType)
                    .ToDictionary(g => g.Key, g => g.Sum(f => f.ImpactCost!.Value)),
                ImpactBySeverity = failuresList.Where(f => f.ImpactCost.HasValue)
                    .GroupBy(f => f.Severity)
                    .ToDictionary(g => g.Key, g => g.Sum(f => f.ImpactCost!.Value)),
                AverageImpactPerFailure = failuresList.Where(f => f.ImpactCost.HasValue).Any() 
                    ? failuresList.Where(f => f.ImpactCost.HasValue).Average(f => f.ImpactCost!.Value) 
                    : 0,
                PercentageOfTotalImpact = 100,
                TrendDirection = "Stable", // Would need historical data for actual trend
                MitigationStrategies = new List<string>
                {
                    "Implement proactive monitoring",
                    "Improve system redundancy",
                    "Enhance failure prediction models"
                }
            });
        }

        // Operational impact (delay time)
        var totalDelayHours = failuresList.Where(f => f.DelayTime.HasValue).Sum(f => f.DelayTime!.Value.TotalHours);
        if (totalDelayHours > 0)
        {
            impactAnalysis.Add(new ImpactAnalysisDto
            {
                ImpactCategory = "Operational",
                TotalImpact = (decimal)totalDelayHours,
                ImpactUnit = "Hours",
                ImpactByFailureType = failuresList.Where(f => f.DelayTime.HasValue)
                    .GroupBy(f => f.FailureType)
                    .ToDictionary(g => g.Key, g => (decimal)g.Sum(f => f.DelayTime!.Value.TotalHours)),
                ImpactBySeverity = failuresList.Where(f => f.DelayTime.HasValue)
                    .GroupBy(f => f.Severity)
                    .ToDictionary(g => g.Key, g => (decimal)g.Sum(f => f.DelayTime!.Value.TotalHours)),
                AverageImpactPerFailure = failuresList.Where(f => f.DelayTime.HasValue).Any() 
                    ? (decimal)failuresList.Where(f => f.DelayTime.HasValue).Average(f => f.DelayTime!.Value.TotalHours) 
                    : 0,
                PercentageOfTotalImpact = 100,
                TrendDirection = "Stable",
                MitigationStrategies = new List<string>
                {
                    "Optimize routing algorithms",
                    "Implement real-time rerouting",
                    "Improve traffic data integration"
                }
            });
        }

        report.ImpactAnalysis = impactAnalysis;
    }

    private async Task GenerateTrendAnalysis(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var dailyFailures = await _routingFailureRepository.GetDailyFailureCountsAsync(fromDate, toDate, cancellationToken);
        var dailyResolutions = await _routingFailureRepository.GetDailyResolutionCountsAsync(fromDate, toDate, cancellationToken);

        var trends = new List<FailureTrendDto>();
        var currentDate = fromDate.Date;

        while (currentDate <= toDate.Date)
        {
            var failureCount = dailyFailures.GetValueOrDefault(currentDate, 0);
            var resolutionCount = dailyResolutions.GetValueOrDefault(currentDate, 0);

            trends.Add(new FailureTrendDto
            {
                Date = currentDate,
                Period = "Daily",
                FailureCount = failureCount,
                ResolvedCount = resolutionCount,
                ResolutionRate = failureCount > 0 ? (decimal)resolutionCount / failureCount * 100 : 0,
                TrendDirection = "Stable" // Would need more sophisticated trend calculation
            });

            currentDate = currentDate.AddDays(1);
        }

        report.TrendAnalysis = trends;
    }

    private async Task GenerateRootCauseAnalysis(RoutingFailureReportDto report, GenerateRoutingFailureReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var rootCauseFrequency = await _routingFailureRepository.GetRootCauseFrequencyAsync(fromDate, toDate, cancellationToken);
        var totalFailures = await _routingFailureRepository.GetFailuresForReportAsync(fromDate, toDate, cancellationToken: cancellationToken);
        var totalCount = totalFailures.Count();

        var rootCauseAnalysis = rootCauseFrequency.Select(kvp => new RootCauseAnalysisDto
        {
            RootCause = kvp.Key,
            Category = "System", // Would need categorization logic
            FailureCount = kvp.Value,
            ImpactPercentage = totalCount > 0 ? (decimal)kvp.Value / totalCount * 100 : 0,
            Priority = kvp.Value > 5 ? "High" : kvp.Value > 2 ? "Medium" : "Low",
            PreventiveActions = GetPreventiveActionsForRootCause(kvp.Key),
            RecommendedTimeline = kvp.Value > 5 ? "Immediate" : "Within 30 days",
            ResponsibleTeams = GetResponsibleTeamsForRootCause(kvp.Key)
        }).OrderByDescending(r => r.FailureCount).ToList();

        report.RootCauseAnalysis = rootCauseAnalysis;
    }

    private static string GetRecommendationForFailureType(FailureType failureType)
    {
        return failureType switch
        {
            FailureType.RouteOptimization => "Review and optimize routing algorithms",
            FailureType.VehicleAssignment => "Improve vehicle allocation logic",
            FailureType.DriverAssignment => "Enhance driver scheduling system",
            FailureType.TrafficConditions => "Integrate real-time traffic data",
            FailureType.WeatherConditions => "Implement weather-aware routing",
            FailureType.SystemError => "Conduct system stability analysis",
            FailureType.ExternalApiFailure => "Implement API resilience patterns",
            _ => "Investigate specific failure patterns"
        };
    }

    private static TimeSpan CalculateAverageInterval(List<Domain.Entities.RoutingFailure> orderedFailures)
    {
        if (orderedFailures.Count < 2) return TimeSpan.Zero;

        var intervals = new List<TimeSpan>();
        for (int i = 1; i < orderedFailures.Count; i++)
        {
            intervals.Add(orderedFailures[i].FailureTime - orderedFailures[i - 1].FailureTime);
        }

        var averageTicks = intervals.Average(i => i.Ticks);
        return new TimeSpan((long)averageTicks);
    }

    private static List<string> GetPreventiveActionsForRootCause(string rootCause)
    {
        return rootCause.ToLowerInvariant() switch
        {
            var cause when cause.Contains("network") => new List<string> { "Implement network redundancy", "Monitor network health" },
            var cause when cause.Contains("database") => new List<string> { "Optimize database queries", "Implement connection pooling" },
            var cause when cause.Contains("api") => new List<string> { "Implement circuit breakers", "Add API rate limiting" },
            _ => new List<string> { "Conduct detailed analysis", "Implement monitoring" }
        };
    }

    private static List<string> GetResponsibleTeamsForRootCause(string rootCause)
    {
        return rootCause.ToLowerInvariant() switch
        {
            var cause when cause.Contains("network") => new List<string> { "Infrastructure Team", "DevOps Team" },
            var cause when cause.Contains("database") => new List<string> { "Database Team", "Backend Team" },
            var cause when cause.Contains("api") => new List<string> { "API Team", "Integration Team" },
            _ => new List<string> { "Development Team", "Operations Team" }
        };
    }

    private static bool HasFilters(GenerateRoutingFailureReportCommand request)
    {
        return request.FailureTypes?.Any() == true || request.Severities?.Any() == true || 
               request.Statuses?.Any() == true || request.Categories?.Any() == true ||
               request.IsRecurring.HasValue || request.MinImpactCost.HasValue || request.MaxImpactCost.HasValue;
    }

    private static Dictionary<string, object> GetFilterCriteria(GenerateRoutingFailureReportCommand request)
    {
        var criteria = new Dictionary<string, object>();
        
        if (request.FailureTypes?.Any() == true) criteria["FailureTypes"] = request.FailureTypes;
        if (request.Severities?.Any() == true) criteria["Severities"] = request.Severities;
        if (request.Statuses?.Any() == true) criteria["Statuses"] = request.Statuses;
        if (request.Categories?.Any() == true) criteria["Categories"] = request.Categories;
        if (request.IsRecurring.HasValue) criteria["IsRecurring"] = request.IsRecurring.Value;
        if (request.MinImpactCost.HasValue) criteria["MinImpactCost"] = request.MinImpactCost.Value;
        if (request.MaxImpactCost.HasValue) criteria["MaxImpactCost"] = request.MaxImpactCost.Value;
        if (request.FromDate.HasValue) criteria["FromDate"] = request.FromDate.Value;
        if (request.ToDate.HasValue) criteria["ToDate"] = request.ToDate.Value;
        
        return criteria;
    }
}
