using Microsoft.EntityFrameworkCore;
using TripManagement.Domain.Entities;
using TripManagement.Domain.Interfaces;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for CargoTypeMaster entity
/// </summary>
public class CargoTypeMasterRepository : ICargoTypeMasterRepository
{
    private readonly TripManagementDbContext _context;

    public CargoTypeMasterRepository(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<CargoTypeMaster?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<CargoTypeMaster?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .FirstOrDefaultAsync(x => x.Code == code.ToUpperInvariant(), cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .Where(x => x.IsActive)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .Where(x => x.Category == category)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<CargoTypeMaster> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string? category = null, 
        bool? isActive = null,
        bool? requiresSpecialHandling = null,
        bool? isHazardous = null,
        bool? requiresTemperatureControl = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.CargoTypeMasters.AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.Trim().ToLowerInvariant();
            query = query.Where(x => 
                x.Name.ToLower().Contains(search) ||
                x.Code.ToLower().Contains(search) ||
                x.Description.ToLower().Contains(search));
        }

        if (!string.IsNullOrWhiteSpace(category))
        {
            query = query.Where(x => x.Category == category);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive.Value);
        }

        if (requiresSpecialHandling.HasValue)
        {
            query = query.Where(x => x.RequiresSpecialHandling == requiresSpecialHandling.Value);
        }

        if (isHazardous.HasValue)
        {
            query = query.Where(x => x.IsHazardous == isHazardous.Value);
        }

        if (requiresTemperatureControl.HasValue)
        {
            query = query.Where(x => x.RequiresTemperatureControl == requiresTemperatureControl.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetBySpecialHandlingAsync(
        bool requiresSpecialHandling,
        bool? isHazardous = null,
        bool? requiresTemperatureControl = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.CargoTypeMasters
            .Where(x => x.IsActive && x.RequiresSpecialHandling == requiresSpecialHandling);

        if (isHazardous.HasValue)
        {
            query = query.Where(x => x.IsHazardous == isHazardous.Value);
        }

        if (requiresTemperatureControl.HasValue)
        {
            query = query.Where(x => x.RequiresTemperatureControl == requiresTemperatureControl.Value);
        }

        return await query
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetByTemperatureRangeAsync(
        decimal? minTemperature = null,
        decimal? maxTemperature = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.CargoTypeMasters
            .Where(x => x.IsActive && x.RequiresTemperatureControl);

        if (minTemperature.HasValue)
        {
            query = query.Where(x => !x.MaxTemperatureCelsius.HasValue || x.MaxTemperatureCelsius >= minTemperature.Value);
        }

        if (maxTemperature.HasValue)
        {
            query = query.Where(x => !x.MinTemperatureCelsius.HasValue || x.MinTemperatureCelsius <= maxTemperature.Value);
        }

        return await query
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetOrderedBySortOrderAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetMaxSortOrderAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .MaxAsync(x => (int?)x.SortOrder, cancellationToken) ?? 0;
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .AnyAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .AnyAsync(x => x.Code == code.ToUpperInvariant(), cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string code, Guid excludeId, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .AnyAsync(x => x.Code == code.ToUpperInvariant() && x.Id != excludeId, cancellationToken);
    }

    public async Task<IEnumerable<string>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .Select(x => x.Category)
            .Distinct()
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetCategoryCountsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .GroupBy(x => x.Category)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public void Add(CargoTypeMaster cargoTypeMaster)
    {
        _context.CargoTypeMasters.Add(cargoTypeMaster);
    }

    public void Update(CargoTypeMaster cargoTypeMaster)
    {
        _context.CargoTypeMasters.Update(cargoTypeMaster);
    }

    public void Delete(CargoTypeMaster cargoTypeMaster)
    {
        _context.CargoTypeMasters.Remove(cargoTypeMaster);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .Where(x => ids.Contains(x.Id))
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CargoTypeMaster>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default)
    {
        var upperCodes = codes.Select(c => c.ToUpperInvariant()).ToList();
        return await _context.CargoTypeMasters
            .Where(x => upperCodes.Contains(x.Code))
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters.CountAsync(cancellationToken);
    }

    public async Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.CargoTypeMasters
            .CountAsync(x => x.IsActive, cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetUsageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var stats = new Dictionary<string, int>
        {
            ["Total"] = await GetTotalCountAsync(cancellationToken),
            ["Active"] = await GetActiveCountAsync(cancellationToken),
            ["Inactive"] = await _context.CargoTypeMasters.CountAsync(x => !x.IsActive, cancellationToken)
        };

        return stats;
    }

    public async Task<Dictionary<string, int>> GetSpecialHandlingStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var stats = new Dictionary<string, int>
        {
            ["RequiresSpecialHandling"] = await _context.CargoTypeMasters.CountAsync(x => x.RequiresSpecialHandling, cancellationToken),
            ["IsHazardous"] = await _context.CargoTypeMasters.CountAsync(x => x.IsHazardous, cancellationToken),
            ["RequiresTemperatureControl"] = await _context.CargoTypeMasters.CountAsync(x => x.RequiresTemperatureControl, cancellationToken),
            ["Standard"] = await _context.CargoTypeMasters.CountAsync(x => !x.RequiresSpecialHandling && !x.IsHazardous && !x.RequiresTemperatureControl, cancellationToken)
        };

        return stats;
    }
}
