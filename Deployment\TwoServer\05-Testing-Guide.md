# TLI Microservices Testing Guide

## Overview

This guide provides comprehensive testing procedures to validate the TLI microservices deployment across two servers. It covers infrastructure testing, service validation, integration testing, and performance verification.

## Testing Strategy

```
┌─────────────────────────────────────────────────────────────────┐
│                        Testing Pyramid                         │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 E2E Testing                             │   │
│  │           (User Journey Testing)                        │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              Integration Testing                        │   │
│  │         (Service-to-Service Communication)              │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │               Service Testing                           │   │
│  │            (Individual Service Health)                  │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │            Infrastructure Testing                       │   │
│  │        (Database, Cache, Message Broker)                │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## Step 1: Infrastructure Testing

### 1.1 Server 1 Infrastructure Tests

```bash
# Create infrastructure test script
cat > /opt/tli/test-infrastructure.sh << 'EOF'
#!/bin/bash

echo "=== TLI Infrastructure Testing ==="
echo "Date: $(date)"
echo

# Test PostgreSQL
echo "1. PostgreSQL Tests:"
echo "  - Connection Test:"
if docker exec tli-postgres pg_isready -U postgres; then
  echo "    ✓ PostgreSQL is ready"
else
  echo "    ✗ PostgreSQL is not ready"
  exit 1
fi

echo "  - Database Creation Test:"
if docker exec tli-postgres psql -U postgres -c "\l" | grep -q "TLI_Identity"; then
  echo "    ✓ TLI databases exist"
else
  echo "    ✗ TLI databases missing"
fi

echo "  - User Access Test:"
if docker exec tli-postgres psql -U tli_admin -d TLI_Identity -c "SELECT 1;" > /dev/null 2>&1; then
  echo "    ✓ Database user access working"
else
  echo "    ✗ Database user access failed"
fi

# Test Redis
echo
echo "2. Redis Tests:"
echo "  - Connection Test:"
if docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" ping | grep -q "PONG"; then
  echo "    ✓ Redis is responding"
else
  echo "    ✗ Redis is not responding"
fi

echo "  - Set/Get Test:"
docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" set test_key "test_value" > /dev/null
if docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" get test_key | grep -q "test_value"; then
  echo "    ✓ Redis set/get working"
  docker exec tli-redis redis-cli -a "TLI_Redis_2024!@#" del test_key > /dev/null
else
  echo "    ✗ Redis set/get failed"
fi

# Test RabbitMQ
echo
echo "3. RabbitMQ Tests:"
echo "  - Connection Test:"
if docker exec tli-rabbitmq rabbitmq-diagnostics ping | grep -q "Ping succeeded"; then
  echo "    ✓ RabbitMQ is responding"
else
  echo "    ✗ RabbitMQ is not responding"
fi

echo "  - Management API Test:"
if curl -s -u tli_admin:TLI_RabbitMQ_2024!@# http://localhost:15672/api/overview > /dev/null; then
  echo "    ✓ RabbitMQ Management API working"
else
  echo "    ✗ RabbitMQ Management API failed"
fi

# Test Prometheus
echo
echo "4. Prometheus Tests:"
echo "  - Health Check:"
if curl -s http://localhost:9090/-/healthy | grep -q "Prometheus is Healthy"; then
  echo "    ✓ Prometheus is healthy"
else
  echo "    ✗ Prometheus health check failed"
fi

echo "  - Metrics Test:"
if curl -s http://localhost:9090/api/v1/query?query=up | grep -q "success"; then
  echo "    ✓ Prometheus metrics API working"
else
  echo "    ✗ Prometheus metrics API failed"
fi

# Test Grafana
echo
echo "5. Grafana Tests:"
echo "  - Health Check:"
if curl -s http://localhost:3000/api/health | grep -q "ok"; then
  echo "    ✓ Grafana is healthy"
else
  echo "    ✗ Grafana health check failed"
fi

echo
echo "=== Infrastructure Testing Complete ==="
EOF

chmod +x /opt/tli/test-infrastructure.sh

# Run infrastructure tests on Server 1
/opt/tli/test-infrastructure.sh
```

### 1.2 Network Connectivity Tests

```bash
# Create network connectivity test script
cat > /opt/tli/test-network-connectivity.sh << 'EOF'
#!/bin/bash

SERVER1_IP="**********"  # Replace with actual Server 1 IP
SERVER2_IP="**********"  # Replace with actual Server 2 IP

echo "=== Network Connectivity Testing ==="
echo "Date: $(date)"
echo

# Test from Server 2 to Server 1
echo "1. Server 2 to Server 1 Connectivity:"

# Basic ping test
echo "  - Ping Test:"
if ping -c 3 $SERVER1_IP > /dev/null 2>&1; then
  echo "    ✓ Server 1 is reachable from Server 2"
else
  echo "    ✗ Server 1 is not reachable from Server 2"
fi

# Port connectivity tests
PORTS=("5432:PostgreSQL" "6379:Redis" "5672:RabbitMQ" "9090:Prometheus" "3000:Grafana")

for port_service in "${PORTS[@]}"; do
  port=$(echo $port_service | cut -d: -f1)
  service=$(echo $port_service | cut -d: -f2)

  echo "  - $service Port ($port):"
  if nc -z $SERVER1_IP $port; then
    echo "    ✓ $service port is accessible"
  else
    echo "    ✗ $service port is not accessible"
  fi
done

# Test latency
echo
echo "2. Network Latency:"
latency=$(ping -c 10 $SERVER1_IP | tail -1 | awk '{print $4}' | cut -d '/' -f 2)
echo "  - Average latency: ${latency}ms"

if (( $(echo "$latency < 10" | bc -l) )); then
  echo "    ✓ Latency is excellent (<10ms)"
elif (( $(echo "$latency < 50" | bc -l) )); then
  echo "    ✓ Latency is good (<50ms)"
else
  echo "    ⚠ Latency is high (>50ms)"
fi

echo
echo "=== Network Connectivity Testing Complete ==="
EOF

chmod +x /opt/tli/test-network-connectivity.sh

# Run network tests from Server 2
/opt/tli/test-network-connectivity.sh
```

## Step 2: Service Health Testing

### 2.1 Individual Service Health Tests

```bash
# Create service health test script
cat > /opt/tli/test-service-health.sh << 'EOF'
#!/bin/bash

echo "=== TLI Service Health Testing ==="
echo "Date: $(date)"
echo

BASE_URL="http://localhost"
SERVICES=(
  "5000:apigateway:API Gateway"
  "5001:identity:Identity Service"
  "5002:usermanagement:User Management"
  "5003:subscription:Subscription Management"
  "5004:order:Order Management"
  "5005:trip:Trip Management"
  "5006:fleet:Fleet Management"
  "5007:payment:Payment Service"
  "5008:communication:Communication Service"
  "5009:analytics:Analytics Service"
  "5010:datastorage:Data Storage"
  "5011:audit:Audit Service"
  "5012:monitoring:Monitoring Service"
  "5013:mobile:Mobile Workflow"
)

for service_info in "${SERVICES[@]}"; do
  port=$(echo $service_info | cut -d: -f1)
  container=$(echo $service_info | cut -d: -f2)
  name=$(echo $service_info | cut -d: -f3)

  echo "Testing $name:"

  # Check if container is running
  if docker ps --format "table {{.Names}}" | grep -q "tli-$container"; then
    echo "  ✓ Container is running"

    # Check health endpoint
    health_response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL:$port/health" 2>/dev/null)
    if [ "$health_response" = "200" ]; then
      echo "  ✓ Health endpoint responding (HTTP 200)"
    else
      echo "  ⚠ Health endpoint not responding (HTTP $health_response)"
    fi

    # Check if service is listening on port
    if netstat -tlnp | grep -q ":$port "; then
      echo "  ✓ Service is listening on port $port"
    else
      echo "  ✗ Service is not listening on port $port"
    fi

    # Check container logs for errors
    error_count=$(docker logs tli-$container 2>&1 | grep -i "error\|exception\|fatal" | wc -l)
    if [ "$error_count" -eq 0 ]; then
      echo "  ✓ No errors in container logs"
    else
      echo "  ⚠ Found $error_count errors in container logs"
    fi

  else
    echo "  ✗ Container is not running"
  fi

  echo
done

echo "=== Service Health Testing Complete ==="
EOF

chmod +x /opt/tli/test-service-health.sh

# Run service health tests
/opt/tli/test-service-health.sh
```

### 2.2 Database Connection Tests

```bash
# Create database connection test script
cat > /opt/tli/test-database-connections.sh << 'EOF'
#!/bin/bash

SERVER1_IP="**********"  # Replace with actual Server 1 IP

echo "=== Database Connection Testing ==="
echo "Date: $(date)"
echo

DATABASES=(
  "TLI_Identity"
  "TLI_UserManagement"
  "TLI_SubscriptionManagement"
  "TLI_OrderManagement"
  "TLI_TripManagement"
  "TLI_NetworkFleetManagement"
  "TLI_FinancialPayment"
  "TLI_CommunicationNotification"
  "TLI_AnalyticsBIService"
  "TLI_DataStorage"
  "TLI_AuditCompliance"
  "TLI_MonitoringObservability"
  "TLI_MobileWorkflow"
)

for db in "${DATABASES[@]}"; do
  echo "Testing $db:"

  # Test connection
  if docker run --rm postgres:15 psql -h $SERVER1_IP -U tli_admin -d $db -c "SELECT 1;" > /dev/null 2>&1; then
    echo "  ✓ Database connection successful"

    # Test table creation (basic schema test)
    if docker run --rm postgres:15 psql -h $SERVER1_IP -U tli_admin -d $db -c "CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY); DROP TABLE test_table;" > /dev/null 2>&1; then
      echo "  ✓ Database write permissions working"
    else
      echo "  ✗ Database write permissions failed"
    fi
  else
    echo "  ✗ Database connection failed"
  fi

  echo
done

echo "=== Database Connection Testing Complete ==="
EOF

chmod +x /opt/tli/test-database-connections.sh

# Run database connection tests
/opt/tli/test-database-connections.sh
```

## Step 3: Integration Testing

### 3.1 API Gateway Integration Tests

```bash
# Create API Gateway integration test script
cat > /opt/tli/test-api-gateway-integration.sh << 'EOF'
#!/bin/bash

echo "=== API Gateway Integration Testing ==="
echo "Date: $(date)"
echo

BASE_URL="http://localhost:5000"

# Test API Gateway routing
echo "1. API Gateway Routing Tests:"

# Test health endpoint
echo "  - Health Endpoint:"
response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/health")
if [ "$response" = "200" ]; then
  echo "    ✓ API Gateway health endpoint working"
else
  echo "    ✗ API Gateway health endpoint failed (HTTP $response)"
fi

# Test service routing (if configured)
ROUTES=("identity" "users" "subscriptions" "orders" "trips")

for route in "${ROUTES[@]}"; do
  echo "  - $route Route:"
  response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/$route/health" 2>/dev/null)
  if [ "$response" = "200" ]; then
    echo "    ✓ $route route working"
  else
    echo "    ⚠ $route route not responding (HTTP $response)"
  fi
done

echo
echo "=== API Gateway Integration Testing Complete ==="
EOF

chmod +x /opt/tli/test-api-gateway-integration.sh

# Run API Gateway integration tests
/opt/tli/test-api-gateway-integration.sh
```

### 3.2 Service-to-Service Communication Tests

```bash
# Create service communication test script
cat > /opt/tli/test-service-communication.sh << 'EOF'
#!/bin/bash

echo "=== Service Communication Testing ==="
echo "Date: $(date)"
echo

# Test Identity Service authentication
echo "1. Identity Service Tests:"
echo "  - Authentication Endpoint:"
auth_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}' 2>/dev/null)

if [ "$auth_response" = "400" ] || [ "$auth_response" = "401" ]; then
  echo "    ✓ Authentication endpoint responding (expected 400/401 for invalid credentials)"
else
  echo "    ⚠ Authentication endpoint unexpected response (HTTP $auth_response)"
fi

# Test User Management Service
echo
echo "2. User Management Service Tests:"
echo "  - Users Endpoint:"
users_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5002/api/users" 2>/dev/null)
if [ "$users_response" = "401" ] || [ "$users_response" = "200" ]; then
  echo "    ✓ Users endpoint responding (HTTP $users_response)"
else
  echo "    ⚠ Users endpoint unexpected response (HTTP $users_response)"
fi

# Test Subscription Management Service
echo
echo "3. Subscription Management Service Tests:"
echo "  - Plans Endpoint:"
plans_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5003/api/plans" 2>/dev/null)
if [ "$plans_response" = "200" ] || [ "$plans_response" = "401" ]; then
  echo "    ✓ Plans endpoint responding (HTTP $plans_response)"
else
  echo "    ⚠ Plans endpoint unexpected response (HTTP $plans_response)"
fi

# Test RabbitMQ message publishing (basic test)
echo
echo "4. Message Broker Tests:"
echo "  - RabbitMQ Connection:"
if docker exec tli-rabbitmq rabbitmqctl list_queues > /dev/null 2>&1; then
  echo "    ✓ RabbitMQ is accessible from services"
else
  echo "    ✗ RabbitMQ is not accessible from services"
fi

# Test Redis caching
echo
echo "5. Cache Tests:"
echo "  - Redis Connection:"
if docker run --rm redis:7-alpine redis-cli -h ********** -a "TLI_Redis_2024!@#" ping | grep -q "PONG"; then
  echo "    ✓ Redis is accessible from services"
else
  echo "    ✗ Redis is not accessible from services"
fi

echo
echo "=== Service Communication Testing Complete ==="
EOF

chmod +x /opt/tli/test-service-communication.sh

# Run service communication tests
/opt/tli/test-service-communication.sh
```

## Step 4: Performance Testing

### 4.1 Load Testing

```bash
# Install Apache Bench for load testing
sudo apt install -y apache2-utils

# Create load testing script
cat > /opt/tli/test-performance.sh << 'EOF'
#!/bin/bash

echo "=== Performance Testing ==="
echo "Date: $(date)"
echo

# Test API Gateway performance
echo "1. API Gateway Load Test:"
echo "  - Testing with 100 concurrent requests..."
ab_result=$(ab -n 1000 -c 100 -q http://localhost:5000/health 2>/dev/null)
requests_per_second=$(echo "$ab_result" | grep "Requests per second" | awk '{print $4}')
echo "    Requests per second: $requests_per_second"

if (( $(echo "$requests_per_second > 100" | bc -l) )); then
  echo "    ✓ Performance is good (>100 req/s)"
elif (( $(echo "$requests_per_second > 50" | bc -l) )); then
  echo "    ⚠ Performance is moderate (>50 req/s)"
else
  echo "    ✗ Performance is poor (<50 req/s)"
fi

# Test individual services
SERVICES=("5001:Identity" "5002:UserManagement" "5003:Subscription")

for service_info in "${SERVICES[@]}"; do
  port=$(echo $service_info | cut -d: -f1)
  name=$(echo $service_info | cut -d: -f2)

  echo
  echo "2. $name Service Load Test:"
  echo "  - Testing with 50 concurrent requests..."

  ab_result=$(ab -n 500 -c 50 -q http://localhost:$port/health 2>/dev/null)
  requests_per_second=$(echo "$ab_result" | grep "Requests per second" | awk '{print $4}')
  echo "    Requests per second: $requests_per_second"

  if (( $(echo "$requests_per_second > 50" | bc -l) )); then
    echo "    ✓ Performance is good"
  else
    echo "    ⚠ Performance needs attention"
  fi
done

echo
echo "=== Performance Testing Complete ==="
EOF

chmod +x /opt/tli/test-performance.sh

# Run performance tests
/opt/tli/test-performance.sh
```

### 4.2 Database Performance Tests

```bash
# Create database performance test script
cat > /opt/tli/test-database-performance.sh << 'EOF'
#!/bin/bash

SERVER1_IP="**********"

echo "=== Database Performance Testing ==="
echo "Date: $(date)"
echo

# Test PostgreSQL performance
echo "1. PostgreSQL Performance Test:"
echo "  - Connection time test..."

start_time=$(date +%s%N)
docker run --rm postgres:15 psql -h $SERVER1_IP -U tli_admin -d TLI_Identity -c "SELECT 1;" > /dev/null 2>&1
end_time=$(date +%s%N)
connection_time=$(( (end_time - start_time) / 1000000 ))

echo "    Connection time: ${connection_time}ms"

if [ "$connection_time" -lt 100 ]; then
  echo "    ✓ Connection time is excellent (<100ms)"
elif [ "$connection_time" -lt 500 ]; then
  echo "    ✓ Connection time is good (<500ms)"
else
  echo "    ⚠ Connection time is slow (>500ms)"
fi

# Test Redis performance
echo
echo "2. Redis Performance Test:"
echo "  - Ping test..."

start_time=$(date +%s%N)
docker run --rm redis:7-alpine redis-cli -h $SERVER1_IP -a "TLI_Redis_2024!@#" ping > /dev/null 2>&1
end_time=$(date +%s%N)
redis_time=$(( (end_time - start_time) / 1000000 ))

echo "    Redis ping time: ${redis_time}ms"

if [ "$redis_time" -lt 10 ]; then
  echo "    ✓ Redis performance is excellent (<10ms)"
elif [ "$redis_time" -lt 50 ]; then
  echo "    ✓ Redis performance is good (<50ms)"
else
  echo "    ⚠ Redis performance is slow (>50ms)"
fi

echo
echo "=== Database Performance Testing Complete ==="
EOF

chmod +x /opt/tli/test-database-performance.sh

# Run database performance tests
/opt/tli/test-database-performance.sh
```

## Step 5: End-to-End Testing

### 5.1 User Journey Tests

```bash
# Create E2E test script
cat > /opt/tli/test-e2e.sh << 'EOF'
#!/bin/bash

echo "=== End-to-End Testing ==="
echo "Date: $(date)"
echo

BASE_URL="http://localhost:5000"

# Test complete user registration flow (mock)
echo "1. User Registration Flow Test:"

# Step 1: Check if registration endpoint exists
echo "  - Registration endpoint availability:"
reg_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword","name":"Test User"}' 2>/dev/null)

if [ "$reg_response" = "400" ] || [ "$reg_response" = "409" ] || [ "$reg_response" = "201" ]; then
  echo "    ✓ Registration endpoint responding appropriately"
else
  echo "    ⚠ Registration endpoint unexpected response (HTTP $reg_response)"
fi

# Step 2: Test login flow
echo "  - Login endpoint availability:"
login_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}' 2>/dev/null)

if [ "$login_response" = "400" ] || [ "$login_response" = "401" ] || [ "$login_response" = "200" ]; then
  echo "    ✓ Login endpoint responding appropriately"
else
  echo "    ⚠ Login endpoint unexpected response (HTTP $login_response)"
fi

# Test subscription flow
echo
echo "2. Subscription Flow Test:"
echo "  - Plans listing:"
plans_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5003/api/plans" 2>/dev/null)
if [ "$plans_response" = "200" ] || [ "$plans_response" = "401" ]; then
  echo "    ✓ Plans endpoint accessible"
else
  echo "    ⚠ Plans endpoint issue (HTTP $plans_response)"
fi

# Test order creation flow
echo
echo "3. Order Creation Flow Test:"
echo "  - Orders endpoint:"
orders_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:5004/api/orders" 2>/dev/null)
if [ "$orders_response" = "200" ] || [ "$orders_response" = "401" ]; then
  echo "    ✓ Orders endpoint accessible"
else
  echo "    ⚠ Orders endpoint issue (HTTP $orders_response)"
fi

echo
echo "=== End-to-End Testing Complete ==="
EOF

chmod +x /opt/tli/test-e2e.sh

# Run E2E tests
/opt/tli/test-e2e.sh
```

## Step 6: Security Testing

### 6.1 Basic Security Tests

```bash
# Create security test script
cat > /opt/tli/test-security.sh << 'EOF'
#!/bin/bash

echo "=== Security Testing ==="
echo "Date: $(date)"
echo

# Test for exposed sensitive endpoints
echo "1. Sensitive Endpoints Test:"

SENSITIVE_ENDPOINTS=(
  "5001:/api/admin:Identity Admin"
  "5002:/api/admin:User Management Admin"
  "5003:/api/admin:Subscription Admin"
)

for endpoint_info in "${SENSITIVE_ENDPOINTS[@]}"; do
  port=$(echo $endpoint_info | cut -d: -f1)
  path=$(echo $endpoint_info | cut -d: -f2)
  name=$(echo $endpoint_info | cut -d: -f3)

  echo "  - $name ($path):"
  response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:$port$path" 2>/dev/null)

  if [ "$response" = "401" ] || [ "$response" = "403" ]; then
    echo "    ✓ Properly protected (HTTP $response)"
  elif [ "$response" = "404" ]; then
    echo "    ✓ Endpoint not found (HTTP $response)"
  else
    echo "    ⚠ Potential security issue (HTTP $response)"
  fi
done

# Test for SQL injection protection (basic)
echo
echo "2. SQL Injection Protection Test:"
echo "  - Testing login endpoint with SQL injection attempt:"
sql_injection_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST \
  "http://localhost:5001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"admin'\''OR 1=1--","password":"anything"}' 2>/dev/null)

if [ "$sql_injection_response" = "400" ] || [ "$sql_injection_response" = "401" ]; then
  echo "    ✓ SQL injection attempt properly handled"
else
  echo "    ⚠ Unexpected response to SQL injection attempt (HTTP $sql_injection_response)"
fi

# Test HTTPS redirect (if configured)
echo
echo "3. HTTPS Configuration Test:"
if command -v openssl &> /dev/null; then
  echo "  - SSL certificate test:"
  if echo | openssl s_client -connect localhost:443 -servername localhost 2>/dev/null | grep -q "CONNECTED"; then
    echo "    ✓ SSL/TLS is configured"
  else
    echo "    ⚠ SSL/TLS not configured or not accessible"
  fi
else
  echo "    ⚠ OpenSSL not available for testing"
fi

echo
echo "=== Security Testing Complete ==="
EOF

chmod +x /opt/tli/test-security.sh

# Run security tests
/opt/tli/test-security.sh
```

## Step 7: Comprehensive Test Suite

### 7.1 Master Test Script

```bash
# Create master test script that runs all tests
cat > /opt/tli/run-all-tests.sh << 'EOF'
#!/bin/bash

echo "=========================================="
echo "    TLI Microservices Test Suite"
echo "=========================================="
echo "Date: $(date)"
echo

# Set test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run test and track results
run_test() {
  local test_name="$1"
  local test_script="$2"

  echo
  echo "Running $test_name..."
  echo "----------------------------------------"

  if [ -f "$test_script" ]; then
    if $test_script; then
      echo "✓ $test_name PASSED"
      ((PASSED_TESTS++))
    else
      echo "✗ $test_name FAILED"
      ((FAILED_TESTS++))
    fi
  else
    echo "⚠ $test_name SKIPPED (script not found)"
    ((FAILED_TESTS++))
  fi

  ((TOTAL_TESTS++))
}

# Run all test suites
run_test "Infrastructure Tests" "/opt/tli/test-infrastructure.sh"
run_test "Network Connectivity Tests" "/opt/tli/test-network-connectivity.sh"
run_test "Service Health Tests" "/opt/tli/test-service-health.sh"
run_test "Database Connection Tests" "/opt/tli/test-database-connections.sh"
run_test "API Gateway Integration Tests" "/opt/tli/test-api-gateway-integration.sh"
run_test "Service Communication Tests" "/opt/tli/test-service-communication.sh"
run_test "Performance Tests" "/opt/tli/test-performance.sh"
run_test "Database Performance Tests" "/opt/tli/test-database-performance.sh"
run_test "End-to-End Tests" "/opt/tli/test-e2e.sh"
run_test "Security Tests" "/opt/tli/test-security.sh"

# Display final results
echo
echo "=========================================="
echo "           Test Results Summary"
echo "=========================================="
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"
echo "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
echo

if [ $FAILED_TESTS -eq 0 ]; then
  echo "🎉 All tests passed! TLI deployment is ready."
  exit 0
else
  echo "⚠ Some tests failed. Please review the results above."
  exit 1
fi
EOF

chmod +x /opt/tli/run-all-tests.sh

# Run the complete test suite
/opt/tli/run-all-tests.sh
```

## Troubleshooting Test Failures

### Common Issues and Solutions

1. **Service Not Responding**

   ```bash
   # Check if container is running
   docker ps | grep tli-

   # Check container logs
   docker logs tli-servicename

   # Restart service
   /opt/tli/manage-applications.sh restart
   ```

2. **Database Connection Failures**

   ```bash
   # Check PostgreSQL status on Server 1
   docker exec tli-postgres pg_isready -U postgres

   # Check network connectivity
   nc -z SERVER1_IP 5432

   # Check firewall rules
   sudo ufw status
   ```

3. **Performance Issues**

   ```bash
   # Check system resources
   htop

   # Check Docker stats
   docker stats

   # Check network latency
   ping SERVER1_IP
   ```

4. **Security Test Failures**

   ```bash
   # Check if endpoints are properly secured
   curl -v http://localhost:5001/api/admin

   # Verify authentication is working
   curl -X POST http://localhost:5001/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"validpassword"}'
   ```

## Continuous Testing

### 7.2 Automated Testing Setup

```bash
# Create cron job for regular health checks
(crontab -l 2>/dev/null; echo "*/15 * * * * /opt/tli/test-service-health.sh >> /opt/tli/logs/health-check.log 2>&1") | crontab -

# Create monitoring script for critical issues
cat > /opt/tli/monitor-critical.sh << 'EOF'
#!/bin/bash

# Run critical tests and alert if failures
if ! /opt/tli/test-infrastructure.sh > /dev/null 2>&1; then
  echo "CRITICAL: Infrastructure tests failed at $(date)" | mail -s "TLI Infrastructure Alert" <EMAIL>
fi

if ! /opt/tli/test-service-health.sh > /dev/null 2>&1; then
  echo "CRITICAL: Service health tests failed at $(date)" | mail -s "TLI Service Alert" <EMAIL>
fi
EOF

chmod +x /opt/tli/monitor-critical.sh

# Add to cron for hourly monitoring
(crontab -l 2>/dev/null; echo "0 * * * * /opt/tli/monitor-critical.sh") | crontab -
```

## Next Steps

1. **Fix any failing tests** before proceeding to production
2. **Setup monitoring** based on test results
3. **Configure alerting** for critical test failures
4. **Document test results** for future reference
5. **Schedule regular testing** to ensure ongoing health
