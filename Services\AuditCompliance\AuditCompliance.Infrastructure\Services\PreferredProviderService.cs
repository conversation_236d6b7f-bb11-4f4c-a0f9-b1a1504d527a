using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Entities;
using Microsoft.Extensions.Logging;

namespace AuditCompliance.Infrastructure.Services;

public class PreferredProviderService : IPreferredProviderService
{
    private readonly IPreferredProviderNetworkRepository _networkRepository;
    private readonly IServiceProviderRatingRepository _ratingRepository;
    private readonly ILogger<PreferredProviderService> _logger;

    public PreferredProviderService(
        IPreferredProviderNetworkRepository networkRepository,
        IServiceProviderRatingRepository ratingRepository,
        ILogger<PreferredProviderService> logger)
    {
        _networkRepository = networkRepository;
        _ratingRepository = ratingRepository;
        _logger = logger;
    }

    public async Task<List<PreferredProviderNetworkDto>> GetPreferredProvidersAsync(Guid shipperId, CancellationToken cancellationToken = default)
    {
        try
        {
            var networks = await _networkRepository.GetByShipperIdAsync(shipperId, cancellationToken);

            return networks
                .Where(n => n.IsActive)
                .OrderBy(n => n.PreferenceRank)
                .Select(n => new PreferredProviderNetworkDto
                {
                    Id = n.Id,
                    ShipperId = n.ShipperId,
                    ShipperName = n.ShipperName,
                    TransportCompanyId = n.TransportCompanyId,
                    TransportCompanyName = n.TransportCompanyName,
                    AverageRating = n.AverageRating,
                    TotalOrders = n.TotalOrders,
                    CompletedOrders = n.CompletedOrders,
                    CompletionRate = n.CompletionRate,
                    FirstOrderDate = n.FirstOrderDate,
                    LastOrderDate = n.LastOrderDate,
                    IsActive = n.IsActive,
                    Notes = n.Notes,
                    PreferenceRank = n.PreferenceRank,
                    CreatedAt = n.CreatedAt
                }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving preferred providers for shipper: {ShipperId}", shipperId);
            throw;
        }
    }

    public async Task<Guid> AddPreferredProviderAsync(Guid shipperId, string shipperName, Guid transportCompanyId, string transportCompanyName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if already exists
            var existing = await _networkRepository.GetByShipperAndProviderAsync(shipperId, transportCompanyId, cancellationToken);
            if (existing != null)
            {
                if (!existing.IsActive)
                {
                    existing.Reactivate();
                    await _networkRepository.UpdateAsync(existing, cancellationToken);
                    return existing.Id;
                }
                throw new InvalidOperationException("Transport company is already in preferred provider network");
            }

            // Calculate statistics from ratings
            var ratings = await _ratingRepository.GetRatingsAsync(
                shipperId: shipperId,
                transportCompanyId: transportCompanyId,
                status: Domain.Enums.RatingStatus.Active,
                cancellationToken: cancellationToken);

            var averageRating = ratings.Any() ? ratings.Average(r => r.OverallRating.NumericValue) : 0;
            var firstOrderDate = ratings.Any() ? ratings.Min(r => r.ServiceCompletedAt) : DateTime.UtcNow;
            var lastOrderDate = ratings.Any() ? ratings.Max(r => r.ServiceCompletedAt) : DateTime.UtcNow;

            // Get next preference rank
            var existingNetworks = await _networkRepository.GetByShipperIdAsync(shipperId, cancellationToken);
            var nextRank = existingNetworks.Where(n => n.IsActive).Any() 
                ? existingNetworks.Where(n => n.IsActive).Max(n => n.PreferenceRank) + 1 
                : 1;

            var network = new PreferredProviderNetwork(
                shipperId,
                shipperName,
                transportCompanyId,
                transportCompanyName,
                averageRating,
                ratings.Count,
                ratings.Count, // Assuming all rated orders are completed
                firstOrderDate,
                lastOrderDate,
                $"Added based on {ratings.Count} completed orders with average rating {averageRating:F2}",
                nextRank);

            var savedNetwork = await _networkRepository.AddAsync(network, cancellationToken);

            _logger.LogInformation("Added preferred provider: {TransportCompanyId} for shipper: {ShipperId}", 
                transportCompanyId, shipperId);

            return savedNetwork.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding preferred provider: {TransportCompanyId} for shipper: {ShipperId}", 
                transportCompanyId, shipperId);
            throw;
        }
    }

    public async Task RemovePreferredProviderAsync(Guid shipperId, Guid transportCompanyId, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            var network = await _networkRepository.GetByShipperAndProviderAsync(shipperId, transportCompanyId, cancellationToken);
            if (network == null)
                throw new InvalidOperationException("Preferred provider network not found");

            network.Deactivate(reason);
            await _networkRepository.UpdateAsync(network, cancellationToken);

            _logger.LogInformation("Removed preferred provider: {TransportCompanyId} for shipper: {ShipperId}, Reason: {Reason}", 
                transportCompanyId, shipperId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing preferred provider: {TransportCompanyId} for shipper: {ShipperId}", 
                transportCompanyId, shipperId);
            throw;
        }
    }

    public async Task UpdatePreferredProviderStatisticsAsync(Guid shipperId, Guid transportCompanyId, decimal averageRating, int totalOrders, int completedOrders, DateTime lastOrderDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var network = await _networkRepository.GetByShipperAndProviderAsync(shipperId, transportCompanyId, cancellationToken);
            if (network == null)
                throw new InvalidOperationException("Preferred provider network not found");

            network.UpdateStatistics(averageRating, totalOrders, completedOrders, lastOrderDate);
            await _networkRepository.UpdateAsync(network, cancellationToken);

            _logger.LogInformation("Updated preferred provider statistics: {TransportCompanyId} for shipper: {ShipperId}", 
                transportCompanyId, shipperId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating preferred provider statistics: {TransportCompanyId} for shipper: {ShipperId}", 
                transportCompanyId, shipperId);
            throw;
        }
    }

    public async Task ReorderPreferredProvidersAsync(Guid shipperId, List<Guid> transportCompanyIds, CancellationToken cancellationToken = default)
    {
        try
        {
            var networks = await _networkRepository.GetByShipperIdAsync(shipperId, cancellationToken);
            var activeNetworks = networks.Where(n => n.IsActive && transportCompanyIds.Contains(n.TransportCompanyId)).ToList();

            for (int i = 0; i < transportCompanyIds.Count; i++)
            {
                var network = activeNetworks.FirstOrDefault(n => n.TransportCompanyId == transportCompanyIds[i]);
                if (network != null)
                {
                    network.UpdatePreferenceRank(i + 1);
                    await _networkRepository.UpdateAsync(network, cancellationToken);
                }
            }

            _logger.LogInformation("Reordered preferred providers for shipper: {ShipperId}", shipperId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering preferred providers for shipper: {ShipperId}", shipperId);
            throw;
        }
    }
}
