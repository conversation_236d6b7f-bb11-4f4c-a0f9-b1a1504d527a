using MediatR;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.UpgradeSubscription
{
    public class UpgradeSubscriptionCommand : IRequest<bool>
    {
        public Guid SubscriptionId { get; set; }
        public Guid UserId { get; set; }
        public Guid NewPlanId { get; set; }
        public ProrationMode ProrationMode { get; set; } = ProrationMode.CreateProrations;
        public string? PaymentMethodId { get; set; }
        public bool ImmediateUpgrade { get; set; } = true;
        public string? UpgradeReason { get; set; }
    }
}
