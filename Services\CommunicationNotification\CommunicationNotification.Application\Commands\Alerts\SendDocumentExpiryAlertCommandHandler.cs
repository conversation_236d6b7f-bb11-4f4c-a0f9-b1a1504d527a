using CommunicationNotification.Application.Common;
using Shared.Domain.Common;
using CommunicationNotification.Application.Services;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Entities;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Enums;
using Shared.Domain.Common;
using CommunicationNotification.Domain.Repositories;
using Shared.Domain.Common;
using Shared.Domain.Common;
using Microsoft.Extensions.Logging;
using Shared.Domain.Common;

namespace CommunicationNotification.Application.Commands.Alerts;

/// <summary>
/// Handler for document expiry alert commands
/// </summary>
public class SendDocumentExpiryAlertCommandHandler : 
    IRequestHandler<SendDocumentExpiryAlertCommand, SendDocumentExpiryAlertResult>,
    IRequestHandler<SendBulkDocumentExpiryAlertsCommand, SendBulkDocumentExpiryAlertsResult>,
    IRequestHandler<ScheduleDocumentExpiryMonitoringCommand, ScheduleDocumentExpiryMonitoringResult>
{
    private readonly INotificationOrchestrationService _notificationService;
    private readonly ITemplateService _templateService;
    private readonly IUserPreferenceRepository _userPreferenceRepository;
    private readonly ILogger<SendDocumentExpiryAlertCommandHandler> _logger;

    public SendDocumentExpiryAlertCommandHandler(
        INotificationOrchestrationService notificationService,
        ITemplateService templateService,
        IUserPreferenceRepository userPreferenceRepository,
        ILogger<SendDocumentExpiryAlertCommandHandler> logger)
    {
        _notificationService = notificationService;
        _templateService = templateService;
        _userPreferenceRepository = userPreferenceRepository;
        _logger = logger;
    }

    public async Task<SendDocumentExpiryAlertResult> Handle(SendDocumentExpiryAlertCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document expiry alert for carrier {CarrierId}, document {DocumentType} for {EntityType} {EntityId}",
            request.CarrierId, request.DocumentType, request.EntityType, request.EntityId);

        try
        {
            // Determine template based on alert type and days until expiry
            var templateId = GetDocumentExpiryTemplateId(request.DocumentType, request.EntityType, request.DaysUntilExpiry);
            
            // Prepare template parameters
            var templateParameters = PrepareTemplateParameters(request);
            
            // Determine message type and priority
            var messageType = GetMessageTypeForDocumentExpiry(request.DocumentType, request.DaysUntilExpiry);
            var priority = GetPriorityForDocumentExpiry(request.DaysUntilExpiry);
            
            // Create notification request
            var notificationRequest = new NotificationRequest
            {
                UserId = request.CarrierId,
                MessageType = messageType,
                Priority = priority,
                TemplateId = templateId,
                Parameters = templateParameters,
                PreferredChannel = request.PreferredChannels.FirstOrDefault(),
                RequireDeliveryConfirmation = request.RequireAcknowledgment,
                Tags = request.Tags.Concat(new[] { "document-expiry", request.DocumentType.ToLower(), request.EntityType.ToLower() }).ToList(),
                RelatedEntityId = request.EntityId,
                RelatedEntityType = $"{request.EntityType}Document",
                CorrelationId = request.CorrelationId,
                Metadata = new Dictionary<string, string>
                {
                    ["DocumentType"] = request.DocumentType,
                    ["EntityType"] = request.EntityType,
                    ["EntityId"] = request.EntityId.ToString(),
                    ["ExpiryDate"] = request.ExpiryDate.ToString("yyyy-MM-dd"),
                    ["DaysUntilExpiry"] = request.DaysUntilExpiry.ToString(),
                    ["AlertType"] = request.AlertType,
                    ["ThresholdDays"] = request.ThresholdDays.ToString()
                }
            };

            // Send notification
            var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

            // Create result
            var result = new SendDocumentExpiryAlertResult
            {
                AlertId = sendResult.MessageId ?? Guid.NewGuid(),
                IsSuccess = sendResult.IsSuccess,
                ErrorMessage = sendResult.ErrorMessage,
                ChannelsUsed = sendResult.ChannelUsed != null ? new List<NotificationChannel> { sendResult.ChannelUsed.Value } : new List<NotificationChannel>(),
                SentAt = sendResult.SentAt ?? DateTime.UtcNow,
                ExternalId = sendResult.ExternalId,
                DeliveryMetadata = sendResult.Metadata ?? new Dictionary<string, object>(),
                RequiresAcknowledgment = request.RequireAcknowledgment,
                AcknowledgmentDeadline = request.RequireAcknowledgment ? DateTime.UtcNow.AddHours(24) : null
            };

            _logger.LogInformation("Document expiry alert sent successfully. AlertId: {AlertId}", result.AlertId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send document expiry alert for carrier {CarrierId}", request.CarrierId);
            return new SendDocumentExpiryAlertResult
            {
                AlertId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                SentAt = DateTime.UtcNow
            };
        }
    }

    public async Task<SendBulkDocumentExpiryAlertsResult> Handle(SendBulkDocumentExpiryAlertsCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing bulk document expiry alerts for carrier {CarrierId}, {DocumentCount} documents",
            request.CarrierId, request.ExpiringDocuments.Count);

        var startTime = DateTime.UtcNow;
        var results = new List<SendDocumentExpiryAlertResult>();
        var errors = new List<string>();

        try
        {
            // Group documents if requested
            var documentGroups = request.GroupByEntityType 
                ? request.ExpiringDocuments.GroupBy(d => new { d.EntityType, d.EntityId })
                : request.ExpiringDocuments.Select(d => new[] { d }.AsEnumerable()).ToList();

            foreach (var group in documentGroups)
            {
                try
                {
                    if (request.GroupByEntityType && group.Count() > 1)
                    {
                        // Send grouped notification for multiple documents of the same entity
                        var groupedResult = await SendGroupedDocumentExpiryAlert(request.CarrierId, group.ToList(), request, cancellationToken);
                        results.Add(groupedResult);
                    }
                    else
                    {
                        // Send individual notifications
                        foreach (var document in group)
                        {
                            var individualCommand = new SendDocumentExpiryAlertCommand
                            {
                                CarrierId = request.CarrierId,
                                DocumentType = document.DocumentType,
                                EntityType = document.EntityType,
                                EntityId = document.EntityId,
                                EntityName = document.EntityName,
                                ExpiryDate = document.ExpiryDate,
                                DaysUntilExpiry = document.DaysUntilExpiry,
                                AlertType = request.AlertType,
                                ThresholdDays = request.ThresholdDays,
                                DocumentDetails = document.DocumentDetails,
                                PreferredChannels = request.PreferredChannels,
                                Priority = request.Priority,
                                RequireAcknowledgment = request.RequireAcknowledgment,
                                Tags = request.Tags,
                                CorrelationId = request.CorrelationId
                            };

                            var individualResult = await Handle(individualCommand, cancellationToken);
                            results.Add(individualResult);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process document group for carrier {CarrierId}", request.CarrierId);
                    errors.Add($"Failed to process document group: {ex.Message}");
                }
            }

            var endTime = DateTime.UtcNow;
            var successfulAlerts = results.Count(r => r.IsSuccess);
            var failedAlerts = results.Count(r => !r.IsSuccess);

            _logger.LogInformation("Bulk document expiry alerts completed. Success: {SuccessCount}, Failed: {FailedCount}",
                successfulAlerts, failedAlerts);

            return new SendBulkDocumentExpiryAlertsResult
            {
                AlertResults = results,
                TotalAlerts = results.Count,
                SuccessfulAlerts = successfulAlerts,
                FailedAlerts = failedAlerts,
                Errors = errors,
                ProcessedAt = endTime,
                ProcessingDuration = endTime - startTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process bulk document expiry alerts for carrier {CarrierId}", request.CarrierId);
            return new SendBulkDocumentExpiryAlertsResult
            {
                AlertResults = results,
                TotalAlerts = request.ExpiringDocuments.Count,
                SuccessfulAlerts = results.Count(r => r.IsSuccess),
                FailedAlerts = results.Count(r => !r.IsSuccess) + (request.ExpiringDocuments.Count - results.Count),
                Errors = errors.Concat(new[] { ex.Message }).ToList(),
                ProcessedAt = DateTime.UtcNow,
                ProcessingDuration = DateTime.UtcNow - startTime
            };
        }
    }

    public async Task<ScheduleDocumentExpiryMonitoringResult> Handle(ScheduleDocumentExpiryMonitoringCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Scheduling document expiry monitoring for carrier {CarrierId}", request.CarrierId);

        try
        {
            // In a real implementation, this would integrate with a job scheduler like Hangfire or Quartz
            var monitoringJobId = Guid.NewGuid();
            var nextRunTime = CalculateNextRunTime(request.CronExpression);

            // Store monitoring configuration (would be persisted to database)
            var jobMetadata = new Dictionary<string, object>
            {
                ["CarrierId"] = request.CarrierId,
                ["AlertThresholds"] = request.AlertThresholds,
                ["DocumentTypes"] = request.DocumentTypes,
                ["EntityTypes"] = request.EntityTypes,
                ["PreferredChannels"] = request.PreferredChannels.Select(c => c.ToString()).ToList(),
                ["MonitoringSettings"] = request.MonitoringSettings,
                ["IsActive"] = request.IsActive
            };

            _logger.LogInformation("Document expiry monitoring scheduled successfully. JobId: {JobId}, NextRun: {NextRunTime}",
                monitoringJobId, nextRunTime);

            return new ScheduleDocumentExpiryMonitoringResult
            {
                MonitoringJobId = monitoringJobId,
                IsSuccess = true,
                ScheduledAt = DateTime.UtcNow,
                NextRunTime = nextRunTime,
                CronExpression = request.CronExpression,
                JobMetadata = jobMetadata
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to schedule document expiry monitoring for carrier {CarrierId}", request.CarrierId);
            return new ScheduleDocumentExpiryMonitoringResult
            {
                MonitoringJobId = Guid.NewGuid(),
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ScheduledAt = DateTime.UtcNow,
                CronExpression = request.CronExpression
            };
        }
    }

    private async Task<SendDocumentExpiryAlertResult> SendGroupedDocumentExpiryAlert(
        Guid carrierId, 
        List<DocumentExpiryAlertInfo> documents, 
        SendBulkDocumentExpiryAlertsCommand request, 
        CancellationToken cancellationToken)
    {
        var firstDocument = documents.First();
        var templateId = GetGroupedDocumentExpiryTemplateId(firstDocument.EntityType, request.ThresholdDays);
        
        var templateParameters = new Dictionary<string, object>
        {
            ["EntityType"] = firstDocument.EntityType,
            ["EntityName"] = firstDocument.EntityName,
            ["DocumentCount"] = documents.Count,
            ["Documents"] = documents.Select(d => new
            {
                Type = d.DocumentType,
                ExpiryDate = d.ExpiryDate.ToString("dd/MM/yyyy"),
                DaysUntilExpiry = d.DaysUntilExpiry
            }).ToList(),
            ["ThresholdDays"] = request.ThresholdDays,
            ["AlertType"] = request.AlertType,
            ["ActionRequired"] = GetActionRequiredText(request.ThresholdDays)
        };

        var notificationRequest = new NotificationRequest
        {
            UserId = carrierId,
            MessageType = MessageType.DocumentExpiry,
            Priority = GetPriorityForDocumentExpiry(request.ThresholdDays),
            TemplateId = templateId,
            Parameters = templateParameters,
            PreferredChannel = request.PreferredChannels.FirstOrDefault(),
            RequireDeliveryConfirmation = request.RequireAcknowledgment,
            Tags = request.Tags.Concat(new[] { "document-expiry", "grouped", firstDocument.EntityType.ToLower() }).ToList(),
            RelatedEntityId = firstDocument.EntityId,
            RelatedEntityType = $"{firstDocument.EntityType}Documents",
            CorrelationId = request.CorrelationId
        };

        var sendResult = await _notificationService.SendNotificationAsync(notificationRequest, cancellationToken);

        return new SendDocumentExpiryAlertResult
        {
            AlertId = sendResult.MessageId ?? Guid.NewGuid(),
            IsSuccess = sendResult.IsSuccess,
            ErrorMessage = sendResult.ErrorMessage,
            ChannelsUsed = sendResult.ChannelUsed != null ? new List<NotificationChannel> { sendResult.ChannelUsed.Value } : new List<NotificationChannel>(),
            SentAt = sendResult.SentAt ?? DateTime.UtcNow,
            ExternalId = sendResult.ExternalId,
            DeliveryMetadata = sendResult.Metadata ?? new Dictionary<string, object>(),
            RequiresAcknowledgment = request.RequireAcknowledgment
        };
    }

    private Dictionary<string, object> PrepareTemplateParameters(SendDocumentExpiryAlertCommand request)
    {
        return new Dictionary<string, object>
        {
            ["DocumentType"] = request.DocumentType,
            ["EntityType"] = request.EntityType,
            ["EntityName"] = request.EntityName,
            ["ExpiryDate"] = request.ExpiryDate.ToString("dd/MM/yyyy"),
            ["DaysUntilExpiry"] = request.DaysUntilExpiry,
            ["AlertType"] = request.AlertType,
            ["ThresholdDays"] = request.ThresholdDays,
            ["ActionRequired"] = GetActionRequiredText(request.DaysUntilExpiry),
            ["UrgencyLevel"] = GetUrgencyLevel(request.DaysUntilExpiry),
            ["DocumentDetails"] = request.DocumentDetails
        };
    }

    private string GetDocumentExpiryTemplateId(string documentType, string entityType, int daysUntilExpiry)
    {
        var urgency = daysUntilExpiry <= 0 ? "Expired" : daysUntilExpiry <= 7 ? "Critical" : daysUntilExpiry <= 15 ? "Warning" : "Notice";
        return $"DocumentExpiry_{documentType}_{entityType}_{urgency}";
    }

    private string GetGroupedDocumentExpiryTemplateId(string entityType, int thresholdDays)
    {
        var urgency = thresholdDays <= 0 ? "Expired" : thresholdDays <= 7 ? "Critical" : thresholdDays <= 15 ? "Warning" : "Notice";
        return $"DocumentExpiry_Grouped_{entityType}_{urgency}";
    }

    private MessageType GetMessageTypeForDocumentExpiry(string documentType, int daysUntilExpiry)
    {
        return daysUntilExpiry <= 0 ? MessageType.CriticalAlert : MessageType.DocumentExpiry;
    }

    private Priority GetPriorityForDocumentExpiry(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => Priority.Critical,
            <= 7 => Priority.High,
            <= 15 => Priority.Normal,
            _ => Priority.Low
        };
    }

    private string GetActionRequiredText(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => "IMMEDIATE ACTION REQUIRED - Document has expired",
            <= 7 => "URGENT - Renew document within 7 days",
            <= 15 => "Action needed - Renew document within 15 days",
            _ => "Plan for renewal - Document expires soon"
        };
    }

    private string GetUrgencyLevel(int daysUntilExpiry)
    {
        return daysUntilExpiry switch
        {
            <= 0 => "CRITICAL",
            <= 7 => "HIGH",
            <= 15 => "MEDIUM",
            _ => "LOW"
        };
    }

    private DateTime? CalculateNextRunTime(string cronExpression)
    {
        // Simplified calculation - in real implementation would use a proper cron parser
        return DateTime.UtcNow.AddDays(1).Date.AddHours(9); // Next day at 9 AM
    }
}

