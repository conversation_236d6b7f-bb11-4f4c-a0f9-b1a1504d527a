using Microsoft.Extensions.Logging;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.Services;
using Shared.Infrastructure.Caching;

namespace NetworkFleetManagement.Infrastructure.Services;

public class FeatureFlagService : IFeatureFlagService
{
    private readonly IFeatureFlagRepository _featureFlagRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<FeatureFlagService> _logger;

    private const string CACHE_PREFIX = "nfm:feature_flag:";
    private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(15);

    public FeatureFlagService(
        IFeatureFlagRepository featureFlagRepository,
        ICacheService cacheService,
        ILogger<FeatureFlagService> logger)
    {
        _featureFlagRepository = featureFlagRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<bool> IsFeatureEnabledAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {FeatureKey} not found", featureKey);
                return false;
            }

            var isEnabled = featureFlag.IsActiveForUser(userId, context);

            if (isEnabled)
            {
                featureFlag.RecordUsage(userId, null, context);
                await _featureFlagRepository.UpdateAsync(featureFlag);
                await InvalidateFeatureFlagCache(featureKey);
            }

            return isEnabled;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating feature flag {FeatureKey} for user {UserId}", featureKey, userId);
            return false;
        }
    }

    public async Task<T?> GetFeatureValueAsync<T>(string featureKey, Guid userId, T defaultValue, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null || !featureFlag.IsActiveForUser(userId, context))
            {
                return defaultValue;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);
            featureFlag.RecordUsage(userId, variant, context);
            await _featureFlagRepository.UpdateAsync(featureFlag);
            await InvalidateFeatureFlagCache(featureKey);

            if (variant != null && TryConvertValue<T>(variant, out var convertedValue))
            {
                return convertedValue;
            }

            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature value for {FeatureKey} and user {UserId}", featureKey, userId);
            return defaultValue;
        }
    }

    public async Task<string?> GetFeatureVariantAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                return null;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);

            if (variant != null)
            {
                featureFlag.RecordUsage(userId, variant, context);
                await _featureFlagRepository.UpdateAsync(featureFlag);
                await InvalidateFeatureFlagCache(featureKey);
            }

            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature variant for {FeatureKey} and user {UserId}", featureKey, userId);
            return null;
        }
    }

    public async Task<string?> GetABTestVariantAsync(string testKey, Guid userId, Dictionary<string, object>? context = null)
    {
        return await GetFeatureVariantAsync(testKey, userId, context);
    }

    public async Task RecordConversionAsync(string featureKey, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null)
    {
        try
        {
            await _featureFlagRepository.RecordConversionAsync(featureKey, userId, variant, conversionData);
            await InvalidateFeatureFlagCache(featureKey);
            _logger.LogInformation("Recorded conversion for feature {FeatureKey}, user {UserId}, variant {Variant}",
                featureKey, userId, variant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording conversion for {FeatureKey} and user {UserId}", featureKey, userId);
        }
    }

    public async Task<bool> CreateFeatureFlagAsync(string key, string name, string description, bool isEnabled = false)
    {
        try
        {
            var existingFlag = await _featureFlagRepository.GetByKeyAsync(key);
            if (existingFlag != null)
            {
                _logger.LogWarning("Feature flag with key {Key} already exists", key);
                return false;
            }

            var featureFlag = new FeatureFlag(key, name, description, isEnabled);
            await _featureFlagRepository.CreateAsync(featureFlag);
            await InvalidateFeatureFlagCache(key);

            _logger.LogInformation("Created feature flag {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag {Key}", key);
            return false;
        }
    }

    public async Task<bool> UpdateFeatureFlagAsync(string key, bool isEnabled, Dictionary<string, object>? configuration = null)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(key);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {Key} not found for update", key);
                return false;
            }

            if (isEnabled)
                featureFlag.Enable();
            else
                featureFlag.Disable();

            if (configuration != null)
            {
                if (configuration.ContainsKey("rolloutPercentage") &&
                    double.TryParse(configuration["rolloutPercentage"]?.ToString(), out var percentage))
                {
                    featureFlag.SetRolloutPercentage(percentage);
                }

                if (configuration.ContainsKey("targetingRules") &&
                    configuration["targetingRules"] is Dictionary<string, object> rules)
                {
                    featureFlag.SetTargetingRules(rules);
                }
            }

            await _featureFlagRepository.UpdateAsync(featureFlag);
            await InvalidateFeatureFlagCache(key);

            _logger.LogInformation("Updated feature flag {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag {Key}", key);
            return false;
        }
    }

    public async Task<bool> DeleteFeatureFlagAsync(string key)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(key);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {Key} not found for deletion", key);
                return false;
            }

            await _featureFlagRepository.DeleteAsync(featureFlag.Id);
            await InvalidateFeatureFlagCache(key);

            _logger.LogInformation("Deleted feature flag {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting feature flag {Key}", key);
            return false;
        }
    }

    public async Task<bool> SetRolloutPercentageAsync(string featureKey, double percentage)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {Key} not found for rollout update", featureKey);
                return false;
            }

            featureFlag.SetRolloutPercentage(percentage);
            await _featureFlagRepository.UpdateAsync(featureFlag);
            await InvalidateFeatureFlagCache(featureKey);

            _logger.LogInformation("Set rollout percentage for {Key} to {Percentage}%", featureKey, percentage);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting rollout percentage for {Key}", featureKey);
            return false;
        }
    }

    public async Task<bool> AddUserToWhitelistAsync(string featureKey, Guid userId)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {Key} not found for whitelist update", featureKey);
                return false;
            }

            featureFlag.AddToWhitelist(userId);
            await _featureFlagRepository.UpdateAsync(featureFlag);
            await InvalidateFeatureFlagCache(featureKey);

            _logger.LogInformation("Added user {UserId} to whitelist for {Key}", userId, featureKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding user {UserId} to whitelist for {Key}", userId, featureKey);
            return false;
        }
    }

    public async Task<bool> RemoveUserFromWhitelistAsync(string featureKey, Guid userId)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {Key} not found for whitelist update", featureKey);
                return false;
            }

            featureFlag.RemoveFromWhitelist(userId);
            await _featureFlagRepository.UpdateAsync(featureFlag);
            await InvalidateFeatureFlagCache(featureKey);

            _logger.LogInformation("Removed user {UserId} from whitelist for {Key}", userId, featureKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing user {UserId} from whitelist for {Key}", userId, featureKey);
            return false;
        }
    }

    // Fleet-specific feature flags
    public async Task<bool> IsFleetFeatureEnabledAsync(FleetFeatureType featureType, Guid carrierId, Dictionary<string, object>? context = null)
    {
        var featureKey = $"fleet_{featureType.ToString().ToLowerInvariant()}";
        context ??= new Dictionary<string, object>();
        context["carrierId"] = carrierId;
        context["featureType"] = "fleet";

        return await IsFeatureEnabledAsync(featureKey, carrierId, context);
    }

    public async Task<bool> IsDriverFeatureEnabledAsync(DriverFeatureType featureType, Guid driverId, Dictionary<string, object>? context = null)
    {
        var featureKey = $"driver_{featureType.ToString().ToLowerInvariant()}";
        context ??= new Dictionary<string, object>();
        context["driverId"] = driverId;
        context["featureType"] = "driver";

        return await IsFeatureEnabledAsync(featureKey, driverId, context);
    }

    public async Task<bool> IsVehicleFeatureEnabledAsync(VehicleFeatureType featureType, Guid vehicleId, Dictionary<string, object>? context = null)
    {
        var featureKey = $"vehicle_{featureType.ToString().ToLowerInvariant()}";
        context ??= new Dictionary<string, object>();
        context["vehicleId"] = vehicleId;
        context["featureType"] = "vehicle";

        return await IsFeatureEnabledAsync(featureKey, vehicleId, context);
    }

    public async Task<Dictionary<string, object>> GetFeatureFlagAnalyticsAsync(string featureKey, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                return new Dictionary<string, object>();
            }

            return await _featureFlagRepository.GetAnalyticsAsync(featureFlag.Id, from, to);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting analytics for feature flag {FeatureKey}", featureKey);
            return new Dictionary<string, object>();
        }
    }

    public async Task<Dictionary<string, object>> GetABTestResultsAsync(string testKey, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            return await _featureFlagRepository.GetABTestResultsAsync(testKey, from, to);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test results for {TestKey}", testKey);
            return new Dictionary<string, object>();
        }
    }

    // Private helper methods
    private async Task<FeatureFlag?> GetFeatureFlagByKeyAsync(string key)
    {
        try
        {
            var cacheKey = $"{CACHE_PREFIX}{key}";
            var cachedFlag = await _cacheService.GetAsync<FeatureFlag>(cacheKey);

            if (cachedFlag != null)
            {
                return cachedFlag;
            }

            var featureFlag = await _featureFlagRepository.GetByKeyAsync(key);
            if (featureFlag != null)
            {
                await _cacheService.SetAsync(cacheKey, featureFlag, DefaultCacheExpiration);
            }

            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flag {Key} from cache", key);
            return await _featureFlagRepository.GetByKeyAsync(key);
        }
    }

    private async Task InvalidateFeatureFlagCache(string key)
    {
        try
        {
            var cacheKey = $"{CACHE_PREFIX}{key}";
            await _cacheService.RemoveAsync(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache for feature flag {Key}", key);
        }
    }

    private static bool TryConvertValue<T>(string variant, out T? convertedValue)
    {
        convertedValue = default;

        try
        {
            if (typeof(T) == typeof(string))
            {
                convertedValue = (T)(object)variant;
                return true;
            }

            if (typeof(T) == typeof(bool) && bool.TryParse(variant, out var boolValue))
            {
                convertedValue = (T)(object)boolValue;
                return true;
            }

            if (typeof(T) == typeof(int) && int.TryParse(variant, out var intValue))
            {
                convertedValue = (T)(object)intValue;
                return true;
            }

            if (typeof(T) == typeof(double) && double.TryParse(variant, out var doubleValue))
            {
                convertedValue = (T)(object)doubleValue;
                return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }
}
