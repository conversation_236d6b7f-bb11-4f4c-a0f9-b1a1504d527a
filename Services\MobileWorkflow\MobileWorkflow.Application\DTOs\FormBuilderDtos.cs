namespace MobileWorkflow.Application.DTOs;

public class FormDefinitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsTemplate { get; set; }
    public bool IsMultiStep { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> DataBinding { get; set; } = new();
    public List<string> RequiredRoles { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public List<FormFieldDto> Fields { get; set; } = new();
    public List<FormStepDto> Steps { get; set; } = new();
}

public class FormFieldDto
{
    public Guid Id { get; set; }
    public Guid FormDefinitionId { get; set; }
    public Guid? FormStepId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string FieldType { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsRequired { get; set; }
    public bool IsActive { get; set; }
    public bool IsReadOnly { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public Dictionary<string, object> DataBinding { get; set; } = new();
    public object? DefaultValue { get; set; }
    public string? PlaceholderText { get; set; }
    public string? HelpText { get; set; }
    public List<FormFieldOptionDto> Options { get; set; } = new();
}

public class FormStepDto
{
    public Guid Id { get; set; }
    public Guid FormDefinitionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsActive { get; set; }
    public bool IsRequired { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> ValidationRules { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public string? NextStepCondition { get; set; }
    public string? PreviousStepCondition { get; set; }
    public List<FormFieldDto> Fields { get; set; } = new();
}

public class FormSubmissionDto
{
    public Guid Id { get; set; }
    public Guid FormDefinitionId { get; set; }
    public Guid SubmittedBy { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Guid? ReviewedBy { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public string? ReviewNotes { get; set; }
    public int CurrentStep { get; set; }
    public int TotalSteps { get; set; }
    public Dictionary<string, object> ValidationResults { get; set; } = new();
    public Dictionary<string, object> ProcessingResults { get; set; } = new();
    public double ProgressPercentage { get; set; }
}

public class FormFieldOptionDto
{
    public Guid Id { get; set; }
    public Guid FormFieldId { get; set; }
    public string Value { get; set; } = string.Empty;
    public string DisplayText { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Order { get; set; }
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> ConditionalLogic { get; set; } = new();
    public Dictionary<string, object> Styling { get; set; } = new();
    public string? IconUrl { get; set; }
    public string? Color { get; set; }
}

public class FormValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public Dictionary<string, List<string>> FieldErrors { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}

// Request DTOs
public class CreateFormDefinitionRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Version { get; set; } = "1.0";
    public bool IsTemplate { get; set; } = false;
    public bool IsMultiStep { get; set; } = false;
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public List<string>? RequiredRoles { get; set; }
    public List<string>? Tags { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class UpdateFormDefinitionRequest
{
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ConditionalLogic { get; set; }
    public string? Version { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class CreateFormFieldRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string FieldType { get; set; } = string.Empty;
    public int Order { get; set; }
    public Guid? FormStepId { get; set; }
    public bool IsRequired { get; set; } = false;
    public bool IsReadOnly { get; set; } = false;
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ConditionalLogic { get; set; }
    public object? DefaultValue { get; set; }
    public string? PlaceholderText { get; set; }
    public string? HelpText { get; set; }
    public List<CreateFormFieldOptionRequest>? Options { get; set; }
}

public class UpdateFormFieldRequest
{
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ConditionalLogic { get; set; }
    public Dictionary<string, object>? Styling { get; set; }
    public int? Order { get; set; }
    public bool? IsRequired { get; set; }
    public bool? IsReadOnly { get; set; }
    public object? DefaultValue { get; set; }
}

public class CreateFormStepRequest
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsRequired { get; set; } = true;
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ConditionalLogic { get; set; }
}

public class UpdateFormStepRequest
{
    public Dictionary<string, object>? Configuration { get; set; }
    public Dictionary<string, object>? ValidationRules { get; set; }
    public Dictionary<string, object>? ConditionalLogic { get; set; }
    public int? Order { get; set; }
}

public class CreateFormFieldOptionRequest
{
    public string Value { get; set; } = string.Empty;
    public string DisplayText { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Order { get; set; }
    public bool IsDefault { get; set; } = false;
}

public class CreateFormSubmissionRequest
{
    public Guid SubmittedBy { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class UpdateFormSubmissionRequest
{
    public Dictionary<string, object>? Data { get; set; }
    public bool MergeData { get; set; } = true;
    public int? CurrentStep { get; set; }
}

public class FormRenderRequest
{
    public Guid FormId { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
    public string? Platform { get; set; }
}

public class FormSearchRequest
{
    public string? Category { get; set; }
    public string? SearchTerm { get; set; }
    public List<string>? Tags { get; set; }
    public bool ActiveOnly { get; set; } = true;
    public bool TemplatesOnly { get; set; } = false;
}

public class FormAnalyticsDto
{
    public Guid FormId { get; set; }
    public string FormName { get; set; } = string.Empty;
    public int TotalSubmissions { get; set; }
    public int CompletedSubmissions { get; set; }
    public int DraftSubmissions { get; set; }
    public double CompletionRate { get; set; }
    public double AverageCompletionTime { get; set; }
    public Dictionary<string, int> FieldInteractions { get; set; } = new();
    public Dictionary<string, int> DropOffPoints { get; set; } = new();
    public Dictionary<DateTime, int> SubmissionsByDate { get; set; } = new();
}

public class FormTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Industry { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public int UsageCount { get; set; }
    public double Rating { get; set; }
    public string PreviewUrl { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class FormExportRequest
{
    public Guid FormId { get; set; }
    public string Format { get; set; } = "json"; // json, xml, csv
    public bool IncludeSubmissions { get; set; } = false;
    public string? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class FormImportRequest
{
    public string Format { get; set; } = "json";
    public string Data { get; set; } = string.Empty;
    public bool OverwriteExisting { get; set; } = false;
    public string ImportedBy { get; set; } = string.Empty;
}
