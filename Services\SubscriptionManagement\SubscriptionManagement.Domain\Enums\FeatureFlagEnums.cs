namespace SubscriptionManagement.Domain.Enums;

public enum FeatureFlagType
{
    Boolean = 0,
    String = 1,
    Number = 2,
    ABTest = 3,
    Multivariate = 4
}

public enum FeatureFlagStatus
{
    Draft = 0,
    Active = 1,
    Inactive = 2,
    Archived = 3
}

public enum FeatureFlagRuleType
{
    UserAttribute = 0,
    UserSegment = 1,
    Geographic = 2,
    TimeWindow = 3,
    Custom = 4
}

public enum ABTestVariant
{
    Control = 0,
    VariantA = 1,
    VariantB = 2,
    VariantC = 3
}
