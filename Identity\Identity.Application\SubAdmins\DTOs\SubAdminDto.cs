using System;
using System.Collections.Generic;

namespace Identity.Application.SubAdmins.DTOs
{
    public class SubAdminDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public string LastLoginIp { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public List<string> AllowedIpAddresses { get; set; } = new();
        public DateTime? TemporaryAccessExpiry { get; set; }
        public string Department { get; set; }
        public Guid? SupervisorId { get; set; }
        public string SupervisorName { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastPasswordChangeAt { get; set; }
        public bool RequirePasswordChange { get; set; }
        public List<RoleDto> Roles { get; set; } = new();
        public bool IsActive { get; set; }
        public bool HasTemporaryAccess { get; set; }
        public int ActiveSessionsCount { get; set; }
    }

    public class RoleDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Department { get; set; }
        public bool IsActive { get; set; }
        public List<PermissionDto> Permissions { get; set; } = new();
    }

    public class PermissionDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Module { get; set; }
        public string Category { get; set; }
        public string Type { get; set; }
        public string Resource { get; set; }
        public string Action { get; set; }
    }

    public class SubAdminSummaryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string Department { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public bool IsActive { get; set; }
        public int RolesCount { get; set; }
    }

    public class CreateSubAdminDto
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string Department { get; set; }
        public Guid? SupervisorId { get; set; }
        public List<Guid> RoleIds { get; set; } = new();
        public List<string> AllowedIpAddresses { get; set; } = new();
        public bool RequireTwoFactor { get; set; }
        public DateTime? TemporaryAccessExpiry { get; set; }
    }

    public class UpdateSubAdminDto
    {
        public string Name { get; set; }
        public string Department { get; set; }
        public Guid? SupervisorId { get; set; }
        public List<Guid> RoleIds { get; set; } = new();
        public List<string> AllowedIpAddresses { get; set; } = new();
        public DateTime? TemporaryAccessExpiry { get; set; }
    }
}
