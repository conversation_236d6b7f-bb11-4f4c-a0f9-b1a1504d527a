using Shared.Domain.Common;
using TripManagement.Domain.Enums;

namespace TripManagement.Domain.Entities;

public class Vehicle : BaseEntity
{
    public Guid CarrierId { get; private set; }
    public string RegistrationNumber { get; private set; } = string.Empty;
    public VehicleType VehicleType { get; private set; }
    public string Make { get; private set; } = string.Empty;
    public string Model { get; private set; } = string.Empty;
    public int Year { get; private set; }
    public string Color { get; private set; } = string.Empty;
    public decimal LoadCapacityKg { get; private set; }
    public decimal VolumeCapacityM3 { get; private set; }
    public VehicleStatus Status { get; private set; }
    public new DateTime CreatedAt { get; private set; }
    public DateTime? LastMaintenanceDate { get; private set; }
    public DateTime? NextMaintenanceDate { get; private set; }
    public string? InsuranceNumber { get; private set; }
    public DateTime? InsuranceExpiryDate { get; private set; }
    public string? FitnessNumber { get; private set; }
    public DateTime? FitnessExpiryDate { get; private set; }
    public decimal? FuelCapacityLiters { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    private readonly List<Trip> _trips = new();
    public IReadOnlyCollection<Trip> Trips => _trips.AsReadOnly();

    private readonly List<VehicleDocument> _documents = new();
    public IReadOnlyCollection<VehicleDocument> Documents => _documents.AsReadOnly();

    private readonly List<VehicleMaintenance> _maintenanceRecords = new();
    public IReadOnlyCollection<VehicleMaintenance> MaintenanceRecords => _maintenanceRecords.AsReadOnly();

    // Parameterless constructor for EF Core
    private Vehicle() { }

    public Vehicle(
        Guid carrierId,
        string registrationNumber,
        VehicleType vehicleType,
        string make,
        string model,
        int year,
        string color,
        decimal loadCapacityKg,
        decimal volumeCapacityM3,
        string? insuranceNumber = null,
        DateTime? insuranceExpiryDate = null,
        string? fitnessNumber = null,
        DateTime? fitnessExpiryDate = null,
        decimal? fuelCapacityLiters = null,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(registrationNumber))
            throw new ArgumentException("Registration number cannot be empty", nameof(registrationNumber));

        if (string.IsNullOrWhiteSpace(make))
            throw new ArgumentException("Make cannot be empty", nameof(make));

        if (string.IsNullOrWhiteSpace(model))
            throw new ArgumentException("Model cannot be empty", nameof(model));

        if (year < 1900 || year > DateTime.UtcNow.Year + 1)
            throw new ArgumentException("Invalid year", nameof(year));

        if (loadCapacityKg <= 0)
            throw new ArgumentException("Load capacity must be greater than zero", nameof(loadCapacityKg));

        if (volumeCapacityM3 <= 0)
            throw new ArgumentException("Volume capacity must be greater than zero", nameof(volumeCapacityM3));

        CarrierId = carrierId;
        RegistrationNumber = registrationNumber;
        VehicleType = vehicleType;
        Make = make;
        Model = model;
        Year = year;
        Color = color;
        LoadCapacityKg = loadCapacityKg;
        VolumeCapacityM3 = volumeCapacityM3;
        Status = VehicleStatus.Available;
        CreatedAt = DateTime.UtcNow;
        InsuranceNumber = insuranceNumber;
        InsuranceExpiryDate = insuranceExpiryDate;
        FitnessNumber = fitnessNumber;
        FitnessExpiryDate = fitnessExpiryDate;
        FuelCapacityLiters = fuelCapacityLiters;
        Notes = notes;
    }

    public string DisplayName => $"{Make} {Model} ({RegistrationNumber})";

    public bool IsInsuranceValid => !InsuranceExpiryDate.HasValue || InsuranceExpiryDate.Value > DateTime.UtcNow;

    public bool IsFitnessValid => !FitnessExpiryDate.HasValue || FitnessExpiryDate.Value > DateTime.UtcNow;

    public bool IsAvailable => Status == VehicleStatus.Available && IsInsuranceValid && IsFitnessValid;

    public void UpdateStatus(VehicleStatus newStatus)
    {
        Status = newStatus;
    }

    public void UpdateCapacity(decimal loadCapacityKg, decimal volumeCapacityM3)
    {
        if (loadCapacityKg <= 0)
            throw new ArgumentException("Load capacity must be greater than zero", nameof(loadCapacityKg));

        if (volumeCapacityM3 <= 0)
            throw new ArgumentException("Volume capacity must be greater than zero", nameof(volumeCapacityM3));

        LoadCapacityKg = loadCapacityKg;
        VolumeCapacityM3 = volumeCapacityM3;
    }

    public void UpdateInsurance(string insuranceNumber, DateTime expiryDate)
    {
        if (string.IsNullOrWhiteSpace(insuranceNumber))
            throw new ArgumentException("Insurance number cannot be empty", nameof(insuranceNumber));

        if (expiryDate <= DateTime.UtcNow)
            throw new ArgumentException("Insurance expiry date must be in the future", nameof(expiryDate));

        InsuranceNumber = insuranceNumber;
        InsuranceExpiryDate = expiryDate;
    }

    public void UpdateFitness(string fitnessNumber, DateTime expiryDate)
    {
        if (string.IsNullOrWhiteSpace(fitnessNumber))
            throw new ArgumentException("Fitness number cannot be empty", nameof(fitnessNumber));

        if (expiryDate <= DateTime.UtcNow)
            throw new ArgumentException("Fitness expiry date must be in the future", nameof(expiryDate));

        FitnessNumber = fitnessNumber;
        FitnessExpiryDate = expiryDate;
    }

    public void ScheduleMaintenance(DateTime maintenanceDate)
    {
        if (maintenanceDate <= DateTime.UtcNow)
            throw new ArgumentException("Maintenance date must be in the future", nameof(maintenanceDate));

        NextMaintenanceDate = maintenanceDate;
        Status = VehicleStatus.Maintenance;
    }

    public void ScheduleMaintenance(VehicleMaintenance maintenance)
    {
        _maintenanceRecords.Add(maintenance);

        // Update next maintenance date if this is the earliest scheduled maintenance
        if (!NextMaintenanceDate.HasValue || maintenance.ScheduledDate < NextMaintenanceDate.Value)
        {
            NextMaintenanceDate = maintenance.ScheduledDate;
        }
    }

    public void CompleteMaintenance()
    {
        LastMaintenanceDate = DateTime.UtcNow;
        NextMaintenanceDate = null;
        Status = VehicleStatus.Available;
    }

    public void AddTrip(Trip trip)
    {
        _trips.Add(trip);
    }

    public void AddDocument(VehicleDocument document)
    {
        _documents.Add(document);
    }

    public void Retire(string reason)
    {
        Status = VehicleStatus.Retired;
        Notes = $"Retired: {reason}";
    }
}

public class VehicleDocument : BaseEntity
{
    public Guid VehicleId { get; private set; }
    public DocumentType DocumentType { get; private set; }
    public string FileName { get; private set; } = string.Empty;
    public string FileUrl { get; private set; } = string.Empty;
    public string? Description { get; private set; }
    public DateTime UploadedAt { get; private set; }
    public DateTime? ExpiryDate { get; private set; }
    public bool IsVerified { get; private set; }
    public DateTime? VerifiedAt { get; private set; }
    public string? VerifiedBy { get; private set; }

    // Navigation properties
    public Vehicle Vehicle { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private VehicleDocument() { }

    public VehicleDocument(
        Guid vehicleId,
        DocumentType documentType,
        string fileName,
        string fileUrl,
        string? description = null,
        DateTime? expiryDate = null)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be empty", nameof(fileName));

        if (string.IsNullOrWhiteSpace(fileUrl))
            throw new ArgumentException("File URL cannot be empty", nameof(fileUrl));

        VehicleId = vehicleId;
        DocumentType = documentType;
        FileName = fileName;
        FileUrl = fileUrl;
        Description = description;
        UploadedAt = DateTime.UtcNow;
        ExpiryDate = expiryDate;
        IsVerified = false;
    }

    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.UtcNow;

    public void Verify(string verifiedBy)
    {
        if (string.IsNullOrWhiteSpace(verifiedBy))
            throw new ArgumentException("Verified by cannot be empty", nameof(verifiedBy));

        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        VerifiedBy = verifiedBy;
    }

    public void Reject()
    {
        IsVerified = false;
        VerifiedAt = null;
        VerifiedBy = null;
    }
}

public class VehicleMaintenance : BaseEntity
{
    public Guid VehicleId { get; private set; }
    // public System.Application.Commands.ScheduleMaintenance.MaintenanceType MaintenanceType { get; private set; }
    public DateTime ScheduledDate { get; private set; }
    public string Description { get; private set; } = string.Empty;
    public string? ServiceProvider { get; private set; }
    public decimal? EstimatedCost { get; private set; }
    public decimal? ActualCost { get; private set; }
    public int? EstimatedDurationHours { get; private set; }
    public int? ActualDurationHours { get; private set; }
    // public System.Application.Commands.ScheduleMaintenance.MaintenancePriority Priority { get; private set; }
    public bool IsRecurring
    { get; private set; }
    public int? RecurrenceIntervalDays { get; private set; }
    public string? Notes { get; private set; }
    public Guid ScheduledBy { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public MaintenanceStatus Status { get; private set; }
    public string? CompletionNotes { get; private set; }

    // Navigation properties
    public Vehicle Vehicle { get; private set; } = null!;

    // Parameterless constructor for EF Core
    private VehicleMaintenance() { }

    public VehicleMaintenance(
        Guid vehicleId,
        // System.Application.Commands.ScheduleMaintenance.MaintenanceType maintenanceType,
        DateTime scheduledDate,
        string description,
        string? serviceProvider = null,
        decimal? estimatedCost = null,
        int? estimatedDurationHours = null,
        // System.Application.Commands.ScheduleMaintenance.MaintenancePriority priority = // System.Application.Commands.ScheduleMaintenance.MaintenancePriority.Medium,
        bool isRecurring = false,
        int? recurrenceIntervalDays = null,
        string? notes = null,
        Guid? scheduledBy = null)
    {
        if (string.IsNullOrWhiteSpace(description))
            throw new ArgumentException("Description cannot be empty", nameof(description));

        VehicleId = vehicleId;
        // MaintenanceType = maintenanceType;
        ScheduledDate = scheduledDate;
        Description = description;
        ServiceProvider = serviceProvider;
        EstimatedCost = estimatedCost;
        EstimatedDurationHours = estimatedDurationHours;
        // Priority = priority;
        IsRecurring = isRecurring;
        RecurrenceIntervalDays = recurrenceIntervalDays;
        Notes = notes;
        ScheduledBy = scheduledBy ?? Guid.Empty;
        Status = MaintenanceStatus.Scheduled;
    }

    public void Start()
    {
        if (Status != MaintenanceStatus.Scheduled)
            throw new InvalidOperationException("Can only start scheduled maintenance");

        Status = MaintenanceStatus.InProgress;
        StartedAt = DateTime.UtcNow;
    }

    public void Complete(decimal? actualCost = null, int? actualDurationHours = null, string? completionNotes = null)
    {
        if (Status != MaintenanceStatus.InProgress)
            throw new InvalidOperationException("Can only complete in-progress maintenance");

        Status = MaintenanceStatus.Completed;
        CompletedAt = DateTime.UtcNow;
        ActualCost = actualCost;
        ActualDurationHours = actualDurationHours;
        CompletionNotes = completionNotes;
    }

    public void Cancel(string reason)
    {
        if (Status == MaintenanceStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed maintenance");

        Status = MaintenanceStatus.Cancelled;
        CompletionNotes = $"Cancelled: {reason}";
    }
}

public enum MaintenanceStatus
{
    Scheduled = 0,
    InProgress = 1,
    Completed = 2,
    Cancelled = 3,
    Overdue = 4
}


