# Use the official .NET 8 runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Use the official .NET 8 SDK as a build image
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Services/MonitoringObservability/MonitoringObservability.API/MonitoringObservability.API.csproj", "Services/MonitoringObservability/MonitoringObservability.API/"]
COPY ["Services/MonitoringObservability/MonitoringObservability.Application/MonitoringObservability.Application.csproj", "Services/MonitoringObservability/MonitoringObservability.Application/"]
COPY ["Services/MonitoringObservability/MonitoringObservability.Domain/MonitoringObservability.Domain.csproj", "Services/MonitoringObservability/MonitoringObservability.Domain/"]
COPY ["Services/MonitoringObservability/MonitoringObservability.Infrastructure/MonitoringObservability.Infrastructure.csproj", "Services/MonitoringObservability/MonitoringObservability.Infrastructure/"]
COPY ["Shared/Shared.Messaging/Shared.Messaging.csproj", "Shared/Shared.Messaging/"]

# Restore dependencies
RUN dotnet restore "Services/MonitoringObservability/MonitoringObservability.API/MonitoringObservability.API.csproj"

# Copy the rest of the source code
COPY . .

# Build the application
WORKDIR "/src/Services/MonitoringObservability/MonitoringObservability.API"
RUN dotnet build "MonitoringObservability.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "MonitoringObservability.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs

# Copy published application
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080

ENTRYPOINT ["dotnet", "MonitoringObservability.API.dll"]
