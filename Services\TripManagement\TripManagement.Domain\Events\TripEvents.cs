using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Events;

// Trip lifecycle events
public record TripCreatedEvent(Guid TripId, Guid OrderId, Guid CarrierId, string TripNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripAssignedEvent(Guid TripId, Guid DriverId, Guid VehicleId, string TripNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripStartedEvent(Guid TripId, DateTime StartTime, Location StartLocation) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripCompletedEvent(Guid TripId, DateTime CompletionTime, decimal? ActualDistanceKm) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripCancelledEvent(Guid TripId, string Reason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Trip tracking events
public record TripLocationUpdatedEvent(Guid TripId, Location CurrentLocation, DateTime Timestamp) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripExceptionEvent(Guid TripId, ExceptionType ExceptionType, string Description) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripExceptionResolvedEvent(Guid TripId, Guid ExceptionId, string Resolution) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Trip stop events
public record TripStopArrivedEvent(Guid TripId, Guid TripStopId, DateTime ArrivalTime, Location Location) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record TripStopCompletedEvent(Guid TripId, Guid TripStopId, DateTime CompletionTime) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record ProofOfDeliveryCollectedEvent(Guid TripId, Guid TripStopId, Guid PodId, string RecipientName) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Driver events
public record DriverAssignedToTripEvent(Guid DriverId, Guid TripId, string TripNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DriverLocationUpdatedEvent(Guid DriverId, Location CurrentLocation, DateTime Timestamp) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record DriverStatusChangedEvent(Guid DriverId, DriverStatus OldStatus, DriverStatus NewStatus) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Vehicle events
public record VehicleAssignedToTripEvent(Guid VehicleId, Guid TripId, string TripNumber) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record VehicleStatusChangedEvent(Guid VehicleId, VehicleStatus OldStatus, VehicleStatus NewStatus) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
