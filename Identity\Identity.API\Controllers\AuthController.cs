using System.Threading.Tasks;
using Identity.API.Models.Requests;
using Identity.Application.Auth.Commands.Login;
using Identity.Application.Auth.Commands.Logout;
using Identity.Application.Auth.Commands.RefreshToken;
using Identity.Application.Auth.Commands.RegisterUser;
using Identity.Application.Auth.Commands.RequestOtpLogin;
using Identity.Application.Auth.Commands.VerifyOtpLogin;
using Identity.Application.Auth.Queries.ValidateToken;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Identity.API.Controllers
{
    public class AuthController : BaseController
    {
        [HttpPost("register")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            var command = new RegisterUserCommand
            {
                Username = request.Username,
                Email = request.Email,
                Password = request.Password,
                PhoneNumber = request.PhoneNumber,
                CountryCode = request.CountryCode,
                FirstName = request.FirstName,
                LastName = request.LastName,
                UserType = request.UserType
            };

            var userId = await Mediator.Send(command);
            return CreatedAtAction(nameof(Register), new { id = userId }, userId);
        }

        [HttpPost("login")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            var command = new LoginCommand
            {
                Username = request.Username,
                Password = request.Password,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                DeviceInfo = request.DeviceInfo ?? HttpContext.Request.Headers["User-Agent"].ToString()
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        [HttpPost("refresh")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Refresh([FromBody] RefreshTokenRequest request)
        {
            var command = new RefreshTokenCommand
            {
                RefreshToken = request.RefreshToken,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1"
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        [HttpPost("logout")]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Logout([FromBody] LogoutRequest request)
        {
            var command = new LogoutCommand
            {
                RefreshToken = request.RefreshToken,
                AccessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", ""),
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1"
            };

            await Mediator.Send(command);
            return NoContent();
        }

        [HttpPost("validate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ValidateToken([FromBody] ValidateTokenRequest request)
        {
            var query = new ValidateTokenQuery
            {
                Token = request.Token
            };

            var result = await Mediator.Send(query);
            return Ok(new { isValid = result });
        }

        [HttpPost("otp/request")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> RequestOtpLogin([FromBody] RequestOtpLoginRequest request)
        {
            var command = new RequestOtpLoginCommand
            {
                Username = request.Username,
                DeliveryMethod = request.DeliveryMethod,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        [HttpPost("otp/verify")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> VerifyOtpLogin([FromBody] VerifyOtpLoginRequest request)
        {
            var command = new VerifyOtpLoginCommand
            {
                Username = request.Username,
                OtpToken = request.OtpToken,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                DeviceInfo = request.DeviceInfo ?? HttpContext.Request.Headers["User-Agent"].ToString()
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }
    }
}
