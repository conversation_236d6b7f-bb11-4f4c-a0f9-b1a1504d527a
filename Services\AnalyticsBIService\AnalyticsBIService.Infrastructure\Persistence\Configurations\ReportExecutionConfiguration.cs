using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for ReportExecution
/// </summary>
public class ReportExecutionConfiguration : IEntityTypeConfiguration<ReportExecution>
{
    public void Configure(EntityTypeBuilder<ReportExecution> builder)
    {
        builder.ToTable("report_executions");

        builder.HasKey(re => re.Id);

        builder.Property(re => re.ReportId)
            .IsRequired();

        builder.Property(re => re.TemplateId);

        builder.Property(re => re.UserId)
            .IsRequired();

        builder.Property(re => re.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(re => re.Parameters)
            .HasColumnType("jsonb");

        builder.Property(re => re.StartTime)
            .IsRequired();

        builder.Property(re => re.EndTime);

        builder.Property(re => re.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(re => re.OutputPath)
            .HasMaxLength(500);

        builder.Property(re => re.OutputFormat)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(re => re.FileSizeBytes);

        builder.Property(re => re.RecordCount);

        // Relationships
        builder.HasOne(re => re.Report)
            .WithMany()
            .HasForeignKey(re => re.ReportId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(re => re.Template)
            .WithMany(rt => rt.ReportExecutions)
            .HasForeignKey(re => re.TemplateId)
            .OnDelete(DeleteBehavior.SetNull);

        // Indexes
        builder.HasIndex(re => re.ReportId)
            .HasDatabaseName("IX_ReportExecutions_ReportId");

        builder.HasIndex(re => re.TemplateId)
            .HasDatabaseName("IX_ReportExecutions_TemplateId");

        builder.HasIndex(re => re.UserId)
            .HasDatabaseName("IX_ReportExecutions_UserId");

        builder.HasIndex(re => re.Status)
            .HasDatabaseName("IX_ReportExecutions_Status");

        builder.HasIndex(re => re.StartTime)
            .HasDatabaseName("IX_ReportExecutions_StartTime");

        builder.HasIndex(re => new { re.UserId, re.StartTime })
            .HasDatabaseName("IX_ReportExecutions_UserId_StartTime");
    }
}
