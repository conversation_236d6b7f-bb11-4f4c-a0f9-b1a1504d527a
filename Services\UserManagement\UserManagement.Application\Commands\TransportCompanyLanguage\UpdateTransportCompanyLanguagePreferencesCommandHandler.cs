using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shared.Messaging;
using UserManagement.Application.Interfaces;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.Commands.TransportCompanyLanguage;

/// <summary>
/// Handler for updating Transport Company language preferences
/// </summary>
public class UpdateTransportCompanyLanguagePreferencesCommandHandler :
    IRequestHandler<UpdateTransportCompanyLanguagePreferencesCommand, UpdateTransportCompanyLanguagePreferencesResult>
{
    private readonly ITransportCompanyRepository _transportCompanyRepository;
    private readonly IUserProfileRepository _userProfileRepository;
    private readonly ILanguageDetectionService _languageDetectionService;
    private readonly ITranslationService _translationService;
    private readonly ILocalizationService _localizationService;
    private readonly INotificationService _notificationService;
    private readonly IMessageBroker _messageBroker;
    private readonly IConfiguration _configuration;
    private readonly ILogger<UpdateTransportCompanyLanguagePreferencesCommandHandler> _logger;

    public UpdateTransportCompanyLanguagePreferencesCommandHandler(
        ITransportCompanyRepository transportCompanyRepository,
        IUserProfileRepository userProfileRepository,
        ILanguageDetectionService languageDetectionService,
        ITranslationService translationService,
        ILocalizationService localizationService,
        INotificationService notificationService,
        IMessageBroker messageBroker,
        IConfiguration configuration,
        ILogger<UpdateTransportCompanyLanguagePreferencesCommandHandler> logger)
    {
        _transportCompanyRepository = transportCompanyRepository;
        _userProfileRepository = userProfileRepository;
        _languageDetectionService = languageDetectionService;
        _translationService = translationService;
        _localizationService = localizationService;
        _notificationService = notificationService;
        _messageBroker = messageBroker;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<UpdateTransportCompanyLanguagePreferencesResult> Handle(
        UpdateTransportCompanyLanguagePreferencesCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating language preferences for Transport Company {TransportCompanyId}", request.TransportCompanyId);

        try
        {
            // Validate request
            var validationResult = await ValidateRequest(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new UpdateTransportCompanyLanguagePreferencesResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Validation failed",
                    ValidationErrors = validationResult.Errors,
                    UpdatedAt = DateTime.UtcNow
                };
            }

            // Get transport company
            var transportCompany = await _transportCompanyRepository.GetByIdAsync(request.TransportCompanyId);
            if (transportCompany == null)
            {
                return new UpdateTransportCompanyLanguagePreferencesResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Transport company not found",
                    UpdatedAt = DateTime.UtcNow
                };
            }

            // Store previous preferences for comparison
            var previousPreferences = await GetCurrentLanguagePreferences(request.TransportCompanyId, cancellationToken);

            // Update language preferences
            await UpdateLanguagePreferences(request.TransportCompanyId, request.Preferences, cancellationToken);

            // Apply to all users if requested
            var affectedUsers = new List<AffectedUser>();
            if (request.ApplyToAllUsers)
            {
                affectedUsers = await ApplyLanguageToAllUsers(request.TransportCompanyId, request.Preferences, cancellationToken);
            }

            // Validate translation availability
            var warnings = await ValidateTranslationAvailability(request.Preferences, cancellationToken);

            // Update localization cache
            await UpdateLocalizationCache(request.TransportCompanyId, request.Preferences, cancellationToken);

            // Publish language preferences updated event
            await PublishLanguagePreferencesUpdatedEvent(request, previousPreferences, cancellationToken);

            // Send notifications if needed
            await SendLanguageChangeNotifications(request.TransportCompanyId, affectedUsers, request.Preferences, cancellationToken);

            var result = new UpdateTransportCompanyLanguagePreferencesResult
            {
                IsSuccess = true,
                UpdatedAt = DateTime.UtcNow,
                Warnings = warnings,
                Status = await BuildLanguagePreferencesStatus(request.TransportCompanyId, request.Preferences, cancellationToken),
                AffectedUsers = affectedUsers
            };

            _logger.LogInformation("Language preferences updated successfully for Transport Company {TransportCompanyId}. Primary language: {PrimaryLanguage}",
                request.TransportCompanyId, request.Preferences.PrimaryLanguageCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating language preferences for Transport Company {TransportCompanyId}", request.TransportCompanyId);

            return new UpdateTransportCompanyLanguagePreferencesResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                UpdatedAt = DateTime.UtcNow
            };
        }
    }

    private async Task<ValidationResult> ValidateRequest(
        UpdateTransportCompanyLanguagePreferencesCommand request,
        CancellationToken cancellationToken)
    {
        var result = new ValidationResult { IsValid = true };

        // Validate transport company ID
        if (request.TransportCompanyId == Guid.Empty)
        {
            result.IsValid = false;
            result.Errors.Add("Transport Company ID is required");
        }

        // Validate primary language
        if (string.IsNullOrEmpty(request.Preferences.PrimaryLanguageCode))
        {
            result.IsValid = false;
            result.Errors.Add("Primary language code is required");
        }
        else if (!await IsLanguageSupported(request.Preferences.PrimaryLanguageCode, cancellationToken))
        {
            result.IsValid = false;
            result.Errors.Add($"Language '{request.Preferences.PrimaryLanguageCode}' is not supported");
        }

        // Validate secondary languages
        foreach (var secondaryLang in request.Preferences.SecondaryLanguages)
        {
            if (!await IsLanguageSupported(secondaryLang.LanguageCode, cancellationToken))
            {
                result.Errors.Add($"Secondary language '{secondaryLang.LanguageCode}' is not supported");
            }
        }

        // Validate translation provider
        if (request.Preferences.EnableAutoTranslation &&
            !IsTranslationProviderSupported(request.Preferences.TranslationProvider))
        {
            result.IsValid = false;
            result.Errors.Add($"Translation provider '{request.Preferences.TranslationProvider}' is not supported");
        }

        // Validate regional formatting
        if (!IsValidCultureCode(request.Preferences.PrimaryCultureCode))
        {
            result.IsValid = false;
            result.Errors.Add($"Culture code '{request.Preferences.PrimaryCultureCode}' is not valid");
        }

        return result;
    }

    private async Task<TransportCompanyLanguagePreferences?> GetCurrentLanguagePreferences(
        Guid transportCompanyId,
        CancellationToken cancellationToken)
    {
        // This would retrieve current preferences from database
        return await Task.FromResult<TransportCompanyLanguagePreferences?>(null);
    }

    private async Task UpdateLanguagePreferences(
        Guid transportCompanyId,
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        // Store preferences in database (could be in a separate LanguagePreferences table)
        await Task.CompletedTask;
    }

    private async Task<List<AffectedUser>> ApplyLanguageToAllUsers(
        Guid transportCompanyId,
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        var affectedUsers = new List<AffectedUser>();

        try
        {
            // Get all users for the transport company
            var users = await _userProfileRepository.GetByTransportCompanyIdAsync(transportCompanyId);

            foreach (var user in users)
            {
                var previousLanguage = user.PreferredLanguage ?? "en";

                // Update user language preference
                user.UpdateLanguagePreference(preferences.PrimaryLanguageCode);
                await _userProfileRepository.UpdateAsync(user);

                affectedUsers.Add(new AffectedUser
                {
                    UserId = user.Id,
                    UserName = user.FullName,
                    Email = user.Email,
                    PreviousLanguage = previousLanguage,
                    NewLanguage = preferences.PrimaryLanguageCode,
                    RequiresNotification = previousLanguage != preferences.PrimaryLanguageCode
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying language to all users for Transport Company {TransportCompanyId}", transportCompanyId);
        }

        return affectedUsers;
    }

    private async Task<List<string>> ValidateTranslationAvailability(
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        var warnings = new List<string>();

        if (preferences.EnableAutoTranslation)
        {
            // Check if translation service is available
            var isAvailable = await _translationService.IsServiceAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                warnings.Add("Translation service is currently unavailable");
            }

            // Check translation quality for each language
            foreach (var lang in preferences.SecondaryLanguages)
            {
                var quality = await _translationService.GetTranslationQualityAsync(
                    preferences.PrimaryLanguageCode, lang.LanguageCode, cancellationToken);

                if (quality < 0.7m) // Less than 70% quality
                {
                    warnings.Add($"Translation quality for {lang.LanguageName} may be limited");
                }
            }
        }

        return warnings;
    }

    private async Task UpdateLocalizationCache(
        Guid transportCompanyId,
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        try
        {
            // Clear existing cache for the company
            await _localizationService.ClearCacheAsync(preferences.PrimaryLanguageCode);

            // Pre-load localization data for primary language
            await _localizationService.PreloadLanguageAsync(preferences.PrimaryLanguageCode);

            // Pre-load secondary languages if enabled
            if (preferences.EnableMultiLanguageSupport)
            {
                foreach (var lang in preferences.SecondaryLanguages.Where(l => l.IsEnabled))
                {
                    await _localizationService.PreloadLanguageAsync(lang.LanguageCode);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update localization cache for Transport Company {TransportCompanyId}", transportCompanyId);
        }
    }

    private async Task PublishLanguagePreferencesUpdatedEvent(
        UpdateTransportCompanyLanguagePreferencesCommand request,
        TransportCompanyLanguagePreferences? previousPreferences,
        CancellationToken cancellationToken)
    {
        await _messageBroker.PublishAsync("transport_company.language_preferences_updated", new
        {
            TransportCompanyId = request.TransportCompanyId,
            UserId = request.UserId,
            PreviousPrimaryLanguage = previousPreferences?.PrimaryLanguageCode,
            NewPrimaryLanguage = request.Preferences.PrimaryLanguageCode,
            EnabledLanguages = request.Preferences.SecondaryLanguages.Where(l => l.IsEnabled).Select(l => l.LanguageCode).ToList(),
            AutoTranslationEnabled = request.Preferences.EnableAutoTranslation,
            LocationDetectionEnabled = request.Preferences.EnableLocationBasedDetection,
            AppliedToAllUsers = request.ApplyToAllUsers,
            ChangeReason = request.ChangeReason,
            UpdatedBy = request.UpdatedBy,
            UpdatedAt = DateTime.UtcNow
        });
    }

    private async Task SendLanguageChangeNotifications(
        Guid transportCompanyId,
        List<AffectedUser> affectedUsers,
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        if (preferences.LocalizeNotifications)
        {
            foreach (var user in affectedUsers.Where(u => u.RequiresNotification))
            {
                try
                {
                    await _notificationService.SendLanguageChangeNotificationAsync(
                        user.UserId,
                        user.Email,
                        user.UserName,
                        user.NewLanguage,
                        user.PreviousLanguage);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to send language change notification to user {UserId}", user.UserId);
                }
            }
        }
    }

    private async Task<LanguagePreferencesStatus> BuildLanguagePreferencesStatus(
        Guid transportCompanyId,
        TransportCompanyLanguagePreferences preferences,
        CancellationToken cancellationToken)
    {
        var enabledFeatures = new List<string>();

        if (preferences.EnableAutoTranslation) enabledFeatures.Add("Auto Translation");
        if (preferences.EnableLocationBasedDetection) enabledFeatures.Add("Location Detection");
        if (preferences.EnableMultiLanguageSupport) enabledFeatures.Add("Multi-Language Support");
        if (preferences.LocalizeNotifications) enabledFeatures.Add("Localized Notifications");
        if (preferences.LocalizeReports) enabledFeatures.Add("Localized Reports");

        return new LanguagePreferencesStatus
        {
            CurrentPrimaryLanguage = preferences.PrimaryLanguageName,
            SupportedLanguagesCount = preferences.SecondaryLanguages.Count(l => l.IsEnabled) + 1,
            TranslationEnabled = preferences.EnableAutoTranslation,
            LocationDetectionEnabled = preferences.EnableLocationBasedDetection,
            FallbackLanguage = preferences.FallbackLanguageCode,
            LastUpdated = DateTime.UtcNow,
            EnabledFeatures = enabledFeatures
        };
    }

    private async Task<bool> IsLanguageSupported(string languageCode, CancellationToken cancellationToken)
    {
        var supportedLanguages = await _localizationService.GetSupportedLanguagesAsync();
        return supportedLanguages.Any(l => l.Code.Equals(languageCode, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsTranslationProviderSupported(string provider)
    {
        var supportedProviders = _configuration.GetSection("Translation:SupportedProviders").Get<List<string>>() ??
            new List<string> { "Google", "Azure", "AWS" };

        return supportedProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
    }

    private bool IsValidCultureCode(string cultureCode)
    {
        try
        {
            var culture = new System.Globalization.CultureInfo(cultureCode);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
