using Microsoft.EntityFrameworkCore;
using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Interfaces;
using NetworkFleetManagement.Infrastructure.Persistence;

namespace NetworkFleetManagement.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for VehicleTypeMaster entity
/// </summary>
public class VehicleTypeMasterRepository : IVehicleTypeMasterRepository
{
    private readonly NetworkFleetDbContext _context;

    public VehicleTypeMasterRepository(NetworkFleetDbContext context)
    {
        _context = context;
    }

    public async Task<VehicleTypeMaster?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<VehicleTypeMaster?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .FirstOrDefaultAsync(x => x.Code == code.ToUpperInvariant(), cancellationToken);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetActiveAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .Where(x => x.IsActive)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .Where(x => x.Category == category)
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<VehicleTypeMaster> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null, 
        string? category = null, 
        bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.VehicleTypeMasters.AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.Trim().ToLowerInvariant();
            query = query.Where(x => 
                x.Name.ToLower().Contains(search) ||
                x.Code.ToLower().Contains(search) ||
                x.Description.ToLower().Contains(search));
        }

        if (!string.IsNullOrWhiteSpace(category))
        {
            query = query.Where(x => x.Category == category);
        }

        if (isActive.HasValue)
        {
            query = query.Where(x => x.IsActive == isActive.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetByCapacityRangeAsync(
        decimal? minLoadCapacity = null,
        decimal? maxLoadCapacity = null,
        decimal? minVolumeCapacity = null,
        decimal? maxVolumeCapacity = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.VehicleTypeMasters.Where(x => x.IsActive);

        if (minLoadCapacity.HasValue)
        {
            query = query.Where(x => !x.MaxLoadCapacityKg.HasValue || x.MaxLoadCapacityKg >= minLoadCapacity.Value);
        }

        if (maxLoadCapacity.HasValue)
        {
            query = query.Where(x => !x.MinLoadCapacityKg.HasValue || x.MinLoadCapacityKg <= maxLoadCapacity.Value);
        }

        if (minVolumeCapacity.HasValue)
        {
            query = query.Where(x => !x.MaxVolumeCapacityM3.HasValue || x.MaxVolumeCapacityM3 >= minVolumeCapacity.Value);
        }

        if (maxVolumeCapacity.HasValue)
        {
            query = query.Where(x => !x.MinVolumeCapacityM3.HasValue || x.MinVolumeCapacityM3 <= maxVolumeCapacity.Value);
        }

        return await query
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetOrderedBySortOrderAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetMaxSortOrderAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .MaxAsync(x => (int?)x.SortOrder, cancellationToken) ?? 0;
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .AnyAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .AnyAsync(x => x.Code == code.ToUpperInvariant(), cancellationToken);
    }

    public async Task<bool> ExistsByCodeAsync(string code, Guid excludeId, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .AnyAsync(x => x.Code == code.ToUpperInvariant() && x.Id != excludeId, cancellationToken);
    }

    public async Task<IEnumerable<string>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .Select(x => x.Category)
            .Distinct()
            .OrderBy(x => x)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetCategoryCountsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .GroupBy(x => x.Category)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public void Add(VehicleTypeMaster vehicleTypeMaster)
    {
        _context.VehicleTypeMasters.Add(vehicleTypeMaster);
    }

    public void Update(VehicleTypeMaster vehicleTypeMaster)
    {
        _context.VehicleTypeMasters.Update(vehicleTypeMaster);
    }

    public void Delete(VehicleTypeMaster vehicleTypeMaster)
    {
        _context.VehicleTypeMasters.Remove(vehicleTypeMaster);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetByIdsAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .Where(x => ids.Contains(x.Id))
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<VehicleTypeMaster>> GetByCodesAsync(IEnumerable<string> codes, CancellationToken cancellationToken = default)
    {
        var upperCodes = codes.Select(c => c.ToUpperInvariant()).ToList();
        return await _context.VehicleTypeMasters
            .Where(x => upperCodes.Contains(x.Code))
            .OrderBy(x => x.SortOrder)
            .ThenBy(x => x.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetTotalCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters.CountAsync(cancellationToken);
    }

    public async Task<int> GetActiveCountAsync(CancellationToken cancellationToken = default)
    {
        return await _context.VehicleTypeMasters
            .CountAsync(x => x.IsActive, cancellationToken);
    }

    public async Task<Dictionary<string, int>> GetUsageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        // This would require joining with Vehicle table to get actual usage statistics
        // For now, return basic statistics
        var stats = new Dictionary<string, int>
        {
            ["Total"] = await GetTotalCountAsync(cancellationToken),
            ["Active"] = await GetActiveCountAsync(cancellationToken),
            ["Inactive"] = await _context.VehicleTypeMasters.CountAsync(x => !x.IsActive, cancellationToken)
        };

        return stats;
    }
}
