using CommunicationNotification.Application.Commands.Notifications;
using CommunicationNotification.Application.Commands.Preferences;
using CommunicationNotification.Application.Commands.Conversations;
using CommunicationNotification.Application.Queries.Messages;
using CommunicationNotification.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Main communication API controller using CQRS pattern
/// </summary>
[ApiController]
[Route("api/communication")]
[Authorize]
public class CommunicationController : ControllerBase
{
    private readonly IMediator _mediator;

    public CommunicationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    #region Notification Commands

    /// <summary>
    /// Send a notification to a user
    /// </summary>
    [HttpPost("notifications/send")]
    public async Task<IActionResult> SendNotification(
        [FromBody] SendNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new SendNotificationCommand
        {
            UserId = request.UserId,
            Content = request.Content,
            MessageType = request.MessageType,
            Priority = request.Priority,
            PreferredChannel = request.PreferredChannel,
            PreferredLanguage = request.PreferredLanguage,
            Subject = request.Subject,
            Parameters = request.Parameters ?? new(),
            TemplateId = request.TemplateId,
            RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
            ScheduledAt = request.ScheduledAt,
            Tags = request.Tags ?? new(),
            CustomHeaders = request.CustomHeaders ?? new(),
            InitiatedBy = GetCurrentUserId(),
            CorrelationId = request.CorrelationId ?? Guid.NewGuid().ToString()
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                Data = result.Data,
                CommandId = result.CommandId,
                ExecutedAt = result.ExecutedAt
            });
        }

        return BadRequest(new
        {
            Success = false,
            Error = result.ErrorMessage,
            ValidationErrors = result.ValidationErrors,
            CommandId = result.CommandId
        });
    }

    /// <summary>
    /// Send bulk notifications
    /// </summary>
    [HttpPost("notifications/send-bulk")]
    [Authorize(Roles = "Admin,Dispatcher")]
    public async Task<IActionResult> SendBulkNotification(
        [FromBody] SendBulkNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new SendBulkNotificationCommand
        {
            UserIds = request.UserIds,
            Content = request.Content,
            MessageType = request.MessageType,
            Priority = request.Priority,
            PreferredChannel = request.PreferredChannel,
            PreferredLanguage = request.PreferredLanguage,
            Subject = request.Subject,
            Parameters = request.Parameters ?? new(),
            TemplateId = request.TemplateId,
            RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
            ScheduledAt = request.ScheduledAt,
            Tags = request.Tags ?? new(),
            BatchSize = request.BatchSize,
            BatchDelay = TimeSpan.FromSeconds(request.BatchDelaySeconds),
            InitiatedBy = GetCurrentUserId(),
            CorrelationId = request.CorrelationId ?? Guid.NewGuid().ToString()
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                Data = result.Data,
                CommandId = result.CommandId
            });
        }

        return BadRequest(new
        {
            Success = false,
            Error = result.ErrorMessage,
            CommandId = result.CommandId
        });
    }

    /// <summary>
    /// Send template-based notification
    /// </summary>
    [HttpPost("notifications/send-template")]
    public async Task<IActionResult> SendTemplateNotification(
        [FromBody] SendTemplateNotificationRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new SendTemplateNotificationCommand
        {
            UserId = request.UserId,
            TemplateId = request.TemplateId,
            TemplateParameters = request.TemplateParameters ?? new(),
            MessageType = request.MessageType,
            Priority = request.Priority,
            PreferredChannel = request.PreferredChannel,
            PreferredLanguage = request.PreferredLanguage,
            RequireDeliveryConfirmation = request.RequireDeliveryConfirmation,
            ScheduledAt = request.ScheduledAt,
            Tags = request.Tags ?? new(),
            InitiatedBy = GetCurrentUserId(),
            CorrelationId = request.CorrelationId ?? Guid.NewGuid().ToString()
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new { Success = true, Data = result.Data, CommandId = result.CommandId });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage, CommandId = result.CommandId });
    }

    #endregion

    #region Message Queries

    /// <summary>
    /// Get message history for a user
    /// </summary>
    [HttpGet("messages/history")]
    public async Task<IActionResult> GetMessageHistory(
        [FromQuery] Guid? conversationId = null,
        [FromQuery] MessageType? messageType = null,
        [FromQuery] NotificationChannel? channel = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool includeDeleted = false,
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1,
        [FromQuery] string? sortBy = null,
        [FromQuery] string? sortDirection = null,
        CancellationToken cancellationToken = default)
    {
        var query = new GetMessageHistoryQuery
        {
            UserId = GetCurrentUserId(),
            ConversationId = conversationId,
            MessageType = messageType,
            Channel = channel,
            FromDate = fromDate,
            ToDate = toDate,
            SearchTerm = searchTerm,
            IncludeDeleted = includeDeleted,
            RequestedBy = GetCurrentUserId(),
            Parameters = new()
            {
                PageSize = pageSize,
                PageNumber = pageNumber,
                Sorting = new()
                {
                    SortBy = sortBy,
                    SortDirection = Enum.TryParse<SortDirection>(sortDirection, true, out var direction) 
                        ? direction 
                        : SortDirection.Descending
                }
            }
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                Data = result.Items,
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize,
                TotalPages = result.TotalPages,
                HasNextPage = result.HasNextPage,
                HasPreviousPage = result.HasPreviousPage,
                QueryId = result.QueryId
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage, QueryId = result.QueryId });
    }

    /// <summary>
    /// Get conversation history
    /// </summary>
    [HttpGet("conversations/{conversationId}/messages")]
    public async Task<IActionResult> GetConversationHistory(
        Guid conversationId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool includeSystemMessages = true,
        [FromQuery] bool includeDeleted = false,
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        var query = new GetConversationHistoryQuery
        {
            ConversationId = conversationId,
            UserId = GetCurrentUserId(),
            FromDate = fromDate,
            ToDate = toDate,
            IncludeSystemMessages = includeSystemMessages,
            IncludeDeleted = includeDeleted,
            RequestedBy = GetCurrentUserId(),
            Parameters = new()
            {
                PageSize = pageSize,
                PageNumber = pageNumber
            }
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                Success = true,
                Data = result.Items,
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize,
                QueryId = result.QueryId
            });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage, QueryId = result.QueryId });
    }

    /// <summary>
    /// Get message delivery status
    /// </summary>
    [HttpGet("messages/{messageId}/delivery-status")]
    public async Task<IActionResult> GetMessageDeliveryStatus(
        Guid messageId,
        [FromQuery] bool includeDeliveryAttempts = true,
        [FromQuery] bool includeChannelDetails = true,
        CancellationToken cancellationToken = default)
    {
        var query = new GetMessageDeliveryStatusQuery
        {
            MessageId = messageId,
            IncludeDeliveryAttempts = includeDeliveryAttempts,
            IncludeChannelDetails = includeChannelDetails,
            RequestedBy = GetCurrentUserId()
        };

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new { Success = true, Data = result.Data, QueryId = result.QueryId });
        }

        return BadRequest(new { Success = false, Error = result.ErrorMessage, QueryId = result.QueryId });
    }

    #endregion

    #region User Preferences Commands

    /// <summary>
    /// Update user communication preferences
    /// </summary>
    [HttpPut("preferences")]
    public async Task<IActionResult> UpdateUserPreferences(
        [FromBody] UpdateUserPreferencesRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new UpdateUserPreferencesCommand
        {
            UserId = GetCurrentUserId(),
            ChannelPreferences = request.ChannelPreferences ?? new(),
            LanguagePreferences = request.LanguagePreferences ?? new(),
            DeliveryPreferences = request.DeliveryPreferences ?? new(),
            PrivacyPreferences = request.PrivacyPreferences ?? new(),
            CustomPreferences = request.CustomPreferences ?? new(),
            InitiatedBy = GetCurrentUserId()
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new { Success = true, Data = result.Data, CommandId = result.CommandId });
        }

        return BadRequest(new
        {
            Success = false,
            Error = result.ErrorMessage,
            ValidationErrors = result.ValidationErrors,
            CommandId = result.CommandId
        });
    }

    /// <summary>
    /// Update channel preferences for specific message type
    /// </summary>
    [HttpPut("preferences/channels")]
    public async Task<IActionResult> UpdateChannelPreferences(
        [FromBody] UpdateChannelPreferencesRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new UpdateChannelPreferencesCommand
        {
            UserId = GetCurrentUserId(),
            MessageType = request.MessageType,
            PreferredChannels = request.PreferredChannels ?? new(),
            DisabledChannels = request.DisabledChannels ?? new(),
            ChannelSettings = request.ChannelSettings ?? new(),
            InitiatedBy = GetCurrentUserId()
        };

        var result = await _mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return Ok(new { Success = true, Data = result.Data, CommandId = result.CommandId });
        }

        return BadRequest(new
        {
            Success = false,
            Error = result.ErrorMessage,
            ValidationErrors = result.ValidationErrors,
            CommandId = result.CommandId
        });
    }

    #endregion

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in claims");
    }
}

// Request models
public class SendNotificationRequest
{
    public Guid UserId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
    public string? TemplateId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string>? Tags { get; set; }
    public Dictionary<string, string>? CustomHeaders { get; set; }
    public string? CorrelationId { get; set; }
}

public class SendBulkNotificationRequest
{
    public List<Guid> UserIds { get; set; } = new();
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
    public string? TemplateId { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string>? Tags { get; set; }
    public int BatchSize { get; set; } = 100;
    public int BatchDelaySeconds { get; set; } = 1;
    public string? CorrelationId { get; set; }
}

public class SendTemplateNotificationRequest
{
    public Guid UserId { get; set; }
    public string TemplateId { get; set; } = string.Empty;
    public Dictionary<string, object>? TemplateParameters { get; set; }
    public MessageType MessageType { get; set; }
    public Priority Priority { get; set; } = Priority.Normal;
    public NotificationChannel? PreferredChannel { get; set; }
    public Language? PreferredLanguage { get; set; }
    public bool RequireDeliveryConfirmation { get; set; }
    public DateTime? ScheduledAt { get; set; }
    public List<string>? Tags { get; set; }
    public string? CorrelationId { get; set; }
}

public class UpdateUserPreferencesRequest
{
    public NotificationChannelPreferences? ChannelPreferences { get; set; }
    public LanguagePreferences? LanguagePreferences { get; set; }
    public DeliveryPreferences? DeliveryPreferences { get; set; }
    public PrivacyPreferences? PrivacyPreferences { get; set; }
    public Dictionary<string, object>? CustomPreferences { get; set; }
}

public class UpdateChannelPreferencesRequest
{
    public MessageType MessageType { get; set; }
    public List<NotificationChannel>? PreferredChannels { get; set; }
    public List<NotificationChannel>? DisabledChannels { get; set; }
    public Dictionary<NotificationChannel, ChannelSettings>? ChannelSettings { get; set; }
}
