using CommunicationNotification.Application.Common;
using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.Queries.Messages;

/// <summary>
/// Query to get message history for a user
/// </summary>
public class GetMessageHistoryQuery : QueryBase<PagedQueryResult<MessageHistoryItem>>
{
    public Guid UserId { get; set; }
    public Guid? ConversationId { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? SearchTerm { get; set; }
    public bool IncludeDeleted { get; set; } = false;
    public QueryParameters Parameters { get; set; } = new();
}

/// <summary>
/// Query to get conversation history
/// </summary>
public class GetConversationHistoryQuery : QueryBase<PagedQueryResult<ConversationMessage>>
{
    public Guid ConversationId { get; set; }
    public Guid? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool IncludeSystemMessages { get; set; } = true;
    public bool IncludeDeleted { get; set; } = false;
    public QueryParameters Parameters { get; set; } = new();
}

/// <summary>
/// Query to get message delivery status
/// </summary>
public class GetMessageDeliveryStatusQuery : QueryBase<QueryResult<MessageDeliveryStatus>>
{
    public Guid MessageId { get; set; }
    public bool IncludeDeliveryAttempts { get; set; } = true;
    public bool IncludeChannelDetails { get; set; } = true;
}

/// <summary>
/// Query to get user's conversations
/// </summary>
public class GetUserConversationsQuery : QueryBase<PagedQueryResult<ConversationSummary>>
{
    public Guid UserId { get; set; }
    public ConversationType? ConversationType { get; set; }
    public ConversationStatus? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool IncludeArchived { get; set; } = false;
    public QueryParameters Parameters { get; set; } = new();
}

/// <summary>
/// Query to get message statistics
/// </summary>
public class GetMessageStatisticsQuery : QueryBase<QueryResult<MessageStatistics>>
{
    public Guid? UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public StatisticsGroupBy GroupBy { get; set; } = StatisticsGroupBy.Day;
}

/// <summary>
/// Query to search messages
/// </summary>
public class SearchMessagesQuery : QueryBase<PagedQueryResult<MessageSearchResult>>
{
    public string SearchTerm { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public Guid? ConversationId { get; set; }
    public MessageType? MessageType { get; set; }
    public NotificationChannel? Channel { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public SearchScope SearchScope { get; set; } = SearchScope.Content;
    public bool IncludeDeleted { get; set; } = false;
    public QueryParameters Parameters { get; set; } = new();
}

/// <summary>
/// Query to get unread messages count
/// </summary>
public class GetUnreadMessagesCountQuery : QueryBase<QueryResult<UnreadMessagesCount>>
{
    public Guid UserId { get; set; }
    public Guid? ConversationId { get; set; }
    public MessageType? MessageType { get; set; }
}

// Result models
public class MessageHistoryItem
{
    public Guid MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public MessageType MessageType { get; set; }
    public NotificationChannel Channel { get; set; }
    public Priority Priority { get; set; }
    public Language Language { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public MessageStatus Status { get; set; }
    public Guid SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ConversationMessage
{
    public Guid MessageId { get; set; }
    public Guid ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public DateTime SentAt { get; set; }
    public Guid SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public string? SenderRole { get; set; }
    public bool IsSystemMessage { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? EditedAt { get; set; }
    public List<MessageAttachment> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class MessageDeliveryStatus
{
    public Guid MessageId { get; set; }
    public MessageStatus Status { get; set; }
    public NotificationChannel Channel { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string? ExternalId { get; set; }
    public List<DeliveryAttempt> DeliveryAttempts { get; set; } = new();
    public Dictionary<string, object> ChannelDetails { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class ConversationSummary
{
    public Guid ConversationId { get; set; }
    public string Title { get; set; } = string.Empty;
    public ConversationType Type { get; set; }
    public ConversationStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public int TotalMessages { get; set; }
    public int UnreadMessages { get; set; }
    public List<ConversationParticipant> Participants { get; set; } = new();
    public string? LastMessageContent { get; set; }
    public Guid? LastMessageSenderId { get; set; }
    public bool IsArchived { get; set; }
    public List<string> Tags { get; set; } = new();
}

public class MessageStatistics
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public StatisticsGroupBy GroupBy { get; set; }
    public int TotalMessages { get; set; }
    public int DeliveredMessages { get; set; }
    public int ReadMessages { get; set; }
    public int FailedMessages { get; set; }
    public double DeliveryRate { get; set; }
    public double ReadRate { get; set; }
    public List<StatisticsDataPoint> DataPoints { get; set; } = new();
    public Dictionary<string, int> ChannelBreakdown { get; set; } = new();
    public Dictionary<string, int> MessageTypeBreakdown { get; set; } = new();
}

public class MessageSearchResult
{
    public Guid MessageId { get; set; }
    public Guid? ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public MessageType MessageType { get; set; }
    public NotificationChannel Channel { get; set; }
    public DateTime SentAt { get; set; }
    public Guid SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public List<SearchHighlight> Highlights { get; set; } = new();
    public double RelevanceScore { get; set; }
}

public class UnreadMessagesCount
{
    public Guid UserId { get; set; }
    public int TotalUnread { get; set; }
    public Dictionary<Guid, int> ConversationUnreadCounts { get; set; } = new();
    public Dictionary<string, int> MessageTypeUnreadCounts { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

// Supporting classes
public class MessageAttachment
{
    public Guid Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Url { get; set; } = string.Empty;
}

public class DeliveryAttempt
{
    public int AttemptNumber { get; set; }
    public NotificationChannel Channel { get; set; }
    public DateTime AttemptedAt { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan? ResponseTime { get; set; }
}

public class ConversationParticipant
{
    public Guid UserId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Role { get; set; }
    public DateTime JoinedAt { get; set; }
    public DateTime? LeftAt { get; set; }
    public bool IsActive { get; set; }
}

public class StatisticsDataPoint
{
    public DateTime Date { get; set; }
    public int TotalMessages { get; set; }
    public int DeliveredMessages { get; set; }
    public int ReadMessages { get; set; }
    public int FailedMessages { get; set; }
    public double DeliveryRate { get; set; }
    public double ReadRate { get; set; }
}

public class SearchHighlight
{
    public string Field { get; set; } = string.Empty;
    public string HighlightedText { get; set; } = string.Empty;
    public int StartPosition { get; set; }
    public int Length { get; set; }
}

// Enums
public enum ConversationType
{
    OneToOne,
    Group,
    Broadcast,
    Support,
    System
}

public enum ConversationStatus
{
    Active,
    Archived,
    Closed,
    Suspended
}

public enum MessageStatus
{
    Pending,
    Sent,
    Delivered,
    Read,
    Failed,
    Cancelled
}

public enum StatisticsGroupBy
{
    Hour,
    Day,
    Week,
    Month,
    Year
}

public enum SearchScope
{
    Content,
    Subject,
    All
}
