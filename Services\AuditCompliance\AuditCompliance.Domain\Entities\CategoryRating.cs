using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Category-specific rating within a service provider rating
/// </summary>
public class CategoryRating : BaseEntity
{
    public Guid ServiceProviderRatingId { get; private set; }
    public RatingCategory Category { get; private set; }
    public RatingScore Rating { get; private set; }
    public string? Comment { get; private set; }

    // Navigation property
    public ServiceProviderRating ServiceProviderRating { get; private set; } = null!;

    private CategoryRating() 
    {
        Rating = RatingScore.FromScale(RatingScale.ThreeStars);
    }

    public CategoryRating(
        Guid serviceProviderRatingId,
        RatingCategory category,
        RatingScore rating,
        string? comment = null)
    {
        ServiceProviderRatingId = serviceProviderRatingId;
        Category = category;
        Rating = rating ?? throw new ArgumentNullException(nameof(rating));
        Comment = comment;
    }

    public void UpdateRating(RatingScore newRating, string? comment = null)
    {
        Rating = newRating ?? throw new ArgumentNullException(nameof(newRating));
        Comment = comment;
        SetUpdatedAt();
    }

    public string GetCategoryName()
    {
        return Category switch
        {
            RatingCategory.Overall => "Overall Experience",
            RatingCategory.Timeliness => "Timeliness",
            RatingCategory.Communication => "Communication",
            RatingCategory.ServiceQuality => "Service Quality",
            RatingCategory.Professionalism => "Professionalism",
            RatingCategory.Pricing => "Pricing",
            RatingCategory.Reliability => "Reliability",
            RatingCategory.Safety => "Safety",
            RatingCategory.Documentation => "Documentation",
            RatingCategory.CustomerService => "Customer Service",
            _ => "Unknown Category"
        };
    }

    public string GetCategoryDescription()
    {
        return Category switch
        {
            RatingCategory.Overall => "Overall satisfaction with the service",
            RatingCategory.Timeliness => "Punctuality and adherence to schedules",
            RatingCategory.Communication => "Quality and frequency of communication",
            RatingCategory.ServiceQuality => "Quality of service provided",
            RatingCategory.Professionalism => "Professional behavior and conduct",
            RatingCategory.Pricing => "Value for money and pricing transparency",
            RatingCategory.Reliability => "Consistency and dependability",
            RatingCategory.Safety => "Safety measures and practices",
            RatingCategory.Documentation => "Accuracy and completeness of documentation",
            RatingCategory.CustomerService => "Customer service and support quality",
            _ => "Unknown category"
        };
    }
}
