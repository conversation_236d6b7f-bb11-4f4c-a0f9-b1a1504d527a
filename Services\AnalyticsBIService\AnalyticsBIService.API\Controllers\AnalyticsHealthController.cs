using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AnalyticsBIService.API.Controllers;

/// <summary>
/// Analytics health and status controller for monitoring service health and API endpoints
/// </summary>
[ApiController]
[Route("api/analytics")]
public class AnalyticsHealthController : ControllerBase
{
    private readonly ILogger<AnalyticsHealthController> _logger;

    public AnalyticsHealthController(ILogger<AnalyticsHealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get service health status
    /// </summary>
    /// <returns>Service health information</returns>
    [HttpGet("health")]
    public ActionResult<object> GetHealth()
    {
        try
        {
            return Ok(new
            {
                Status = "Healthy",
                Service = "Analytics & BI Service",
                Version = "1.0.0",
                Timestamp = DateTime.UtcNow,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                Endpoints = new
                {
                    Admin = "/api/admin/analytics",
                    TransportCompany = "/api/transport-company/analytics",
                    Broker = "/api/broker/analytics",
                    Carrier = "/api/carrier/analytics",
                    Shipper = "/api/shipper/analytics"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service health");
            return StatusCode(500, new { Status = "Unhealthy", Error = "Internal server error" });
        }
    }

    /// <summary>
    /// Get API endpoints summary
    /// </summary>
    /// <returns>Available API endpoints</returns>
    [HttpGet("endpoints")]
    public ActionResult<object> GetEndpoints()
    {
        try
        {
            return Ok(new
            {
                Service = "Analytics & BI Service",
                Version = "1.0.0",
                Documentation = "/swagger",
                Endpoints = new
                {
                    Admin = new
                    {
                        BasePath = "/api/admin/analytics",
                        Endpoints = new[]
                        {
                            "GET /dashboard - Admin platform dashboard",
                            "GET /performance - Platform performance metrics",
                            "GET /kpis/realtime - Real-time KPI monitoring",
                            "GET /revenue - Revenue analytics",
                            "GET /users/growth - User growth tracking",
                            "GET /geographic - Geographic performance",
                            "GET /reports - Business intelligence reports",
                            "GET /nps - Customer NPS tracking",
                            "GET /subscriptions - Subscription analytics",
                            "GET /usage - Platform usage reports",
                            "GET /compliance - Regulatory compliance",
                            "GET /audit - Audit trail",
                            "GET /presentation - Stakeholder presentation data",
                            "GET /transactions/volume - Transaction volume tracking"
                        }
                    },
                    TransportCompany = new
                    {
                        BasePath = "/api/transport-company/analytics/{id}",
                        Endpoints = new[]
                        {
                            "GET /dashboard - Transport company dashboard",
                            "GET /performance - Performance metrics",
                            "GET /rfq-conversion - RFQ conversion analytics",
                            "GET /broker-response-time - Broker response analytics",
                            "GET /delivery-performance - Delivery performance",
                            "GET /cost-analysis - Cost analysis",
                            "GET /market-intelligence - Market intelligence",
                            "GET /broker-comparison - Broker comparison",
                            "GET /predictive-insights - Predictive insights (Pro/Enterprise)",
                            "GET /customer-analytics - Customer analytics",
                            "GET /route-optimization - Route optimization",
                            "GET /pricing-optimization - Pricing optimization",
                            "GET /operational-efficiency - Operational efficiency"
                        }
                    },
                    Broker = new
                    {
                        BasePath = "/api/broker/analytics/{id}",
                        Endpoints = new[]
                        {
                            "GET /dashboard - Broker dashboard",
                            "GET /operational - Operational analytics",
                            "GET /rfq-conversion - RFQ conversion analytics",
                            "GET /quote-success - Quote success analytics",
                            "GET /carrier-network - Carrier network utilization",
                            "GET /margin-analysis - Margin analysis",
                            "GET /business-growth - Business growth analytics",
                            "GET /performance-tracking - Performance tracking",
                            "GET /network-growth - Carrier network growth",
                            "GET /efficiency - Efficiency metrics",
                            "GET /customer-satisfaction - Customer satisfaction",
                            "GET /competitive-analysis - Competitive analysis",
                            "GET /route-optimization - Route optimization",
                            "GET /capacity-management - Capacity management",
                            "GET /financial-performance - Financial performance"
                        }
                    },
                    Carrier = new
                    {
                        BasePath = "/api/carrier/analytics/{id}",
                        Endpoints = new[]
                        {
                            "GET /dashboard - Carrier dashboard",
                            "GET /performance - Performance analytics",
                            "GET /delivery-performance - Delivery performance",
                            "GET /efficiency - Efficiency tracking",
                            "GET /earnings - Earnings analytics",
                            "GET /growth-opportunities - Growth opportunities",
                            "GET /service-quality - Service quality",
                            "GET /route-performance - Route performance",
                            "GET /competitive-benchmarking - Competitive benchmarking",
                            "GET /technology-adoption - Technology adoption",
                            "GET /financial-performance - Financial performance",
                            "GET /vehicle-utilization - Vehicle utilization",
                            "GET /customer-satisfaction - Customer satisfaction"
                        }
                    },
                    Shipper = new
                    {
                        BasePath = "/api/shipper/analytics/{id}",
                        Endpoints = new[]
                        {
                            "GET /dashboard - Shipper dashboard",
                            "GET /sla-performance - SLA performance",
                            "GET /cost-analysis - Cost analysis",
                            "GET /provider-comparison - Provider comparison",
                            "GET /business-reporting - Business reporting",
                            "GET /route-efficiency - Route efficiency",
                            "GET /freight-optimization - Freight optimization",
                            "GET /service-quality - Service quality",
                            "GET /shipment-trends - Shipment trends",
                            "GET /financial-performance - Financial performance",
                            "GET /compliance - Compliance analytics",
                            "GET /competitive-benchmarking - Competitive benchmarking",
                            "GET /sustainability - Sustainability analytics"
                        }
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting API endpoints");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get service statistics
    /// </summary>
    /// <returns>Service usage statistics</returns>
    [HttpGet("stats")]
    [Authorize(Roles = "Admin")]
    public ActionResult<object> GetStats()
    {
        try
        {
            return Ok(new
            {
                Service = "Analytics & BI Service",
                Statistics = new
                {
                    TotalEndpoints = 65,
                    AdminEndpoints = 14,
                    TransportCompanyEndpoints = 13,
                    BrokerEndpoints = 15,
                    CarrierEndpoints = 13,
                    ShipperEndpoints = 13,
                    SupportedRoles = new[] { "Admin", "TransportCompany", "Broker", "Carrier", "Shipper" },
                    SupportedTimePeriods = new[] { "Daily", "Weekly", "Monthly", "Quarterly", "Yearly" },
                    AnalyticsCapabilities = new[]
                    {
                        "Real-time KPI monitoring",
                        "Performance analytics",
                        "Financial analysis",
                        "Predictive insights",
                        "Competitive benchmarking",
                        "Compliance tracking",
                        "Business intelligence",
                        "Custom reporting"
                    }
                },
                LastUpdated = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting service statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get API documentation links
    /// </summary>
    /// <returns>Documentation and help resources</returns>
    [HttpGet("docs")]
    public ActionResult<object> GetDocumentation()
    {
        try
        {
            return Ok(new
            {
                Service = "Analytics & BI Service",
                Documentation = new
                {
                    SwaggerUI = "/swagger",
                    OpenAPISpec = "/swagger/v1/swagger.json",
                    Postman = "/api/analytics/postman",
                    Examples = "/api/analytics/examples"
                },
                Support = new
                {
                    Email = "<EMAIL>",
                    Documentation = "https://docs.analytics-bi.com",
                    GitHub = "https://github.com/analytics-bi/service"
                },
                Authentication = new
                {
                    Type = "JWT Bearer Token",
                    Roles = new[] { "Admin", "TransportCompany", "Broker", "Carrier", "Shipper" },
                    Header = "Authorization: Bearer {token}"
                },
                RateLimiting = new
                {
                    RequestsPerMinute = 1000,
                    RequestsPerHour = 10000,
                    RequestsPerDay = 100000
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documentation");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get API examples
    /// </summary>
    /// <returns>Example API requests and responses</returns>
    [HttpGet("examples")]
    public ActionResult<object> GetExamples()
    {
        try
        {
            return Ok(new
            {
                Service = "Analytics & BI Service",
                Examples = new
                {
                    AdminDashboard = new
                    {
                        Request = "GET /api/admin/analytics/dashboard?fromDate=2024-01-01&toDate=2024-01-31&period=Daily",
                        Headers = new { Authorization = "Bearer {admin-token}" },
                        Response = "AdminPlatformDashboardDto with comprehensive platform metrics"
                    },
                    TransportCompanyPerformance = new
                    {
                        Request = "GET /api/transport-company/analytics/{id}/performance?fromDate=2024-01-01&toDate=2024-01-31",
                        Headers = new { Authorization = "Bearer {transport-company-token}" },
                        Response = "TransportCompanyPerformanceDto with RFQ conversion, delivery metrics"
                    },
                    BrokerRFQAnalytics = new
                    {
                        Request = "GET /api/broker/analytics/{id}/rfq-conversion?fromDate=2024-01-01&toDate=2024-01-31",
                        Headers = new { Authorization = "Bearer {broker-token}" },
                        Response = "BrokerRFQConversionAnalyticsDto with conversion funnel, performance factors"
                    },
                    CarrierEarnings = new
                    {
                        Request = "GET /api/carrier/analytics/{id}/earnings?fromDate=2024-01-01&toDate=2024-01-31",
                        Headers = new { Authorization = "Bearer {carrier-token}" },
                        Response = "CarrierEarningsAnalyticsDto with income trends, benchmarking"
                    },
                    ShipperSLA = new
                    {
                        Request = "GET /api/shipper/analytics/{id}/sla-performance?fromDate=2024-01-01&toDate=2024-01-31",
                        Headers = new { Authorization = "Bearer {shipper-token}" },
                        Response = "ShipperSLAPerformanceDto with compliance metrics, KPI performance"
                    }
                },
                CommonParameters = new
                {
                    fromDate = "Start date for analytics period (ISO 8601 format)",
                    toDate = "End date for analytics period (ISO 8601 format)",
                    period = "Time period granularity: Daily, Weekly, Monthly, Quarterly, Yearly",
                    page = "Page number for paginated results (default: 1)",
                    pageSize = "Number of items per page (default: 50, max: 100)"
                },
                ErrorCodes = new Dictionary<string, string>
                {
                    ["400"] = "Bad Request - Invalid parameters",
                    ["401"] = "Unauthorized - Missing or invalid token",
                    ["403"] = "Forbidden - Insufficient permissions",
                    ["404"] = "Not Found - Resource not found",
                    ["429"] = "Too Many Requests - Rate limit exceeded",
                    ["500"] = "Internal Server Error - Server error"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting examples");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get Postman collection
    /// </summary>
    /// <returns>Postman collection for API testing</returns>
    [HttpGet("postman")]
    public ActionResult<object> GetPostmanCollection()
    {
        try
        {
            return Ok(new
            {
                info = new
                {
                    name = "Analytics & BI Service API",
                    description = "Comprehensive analytics and business intelligence API for transport and logistics platform",
                    version = "1.0.0",
                    schema = "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
                },
                auth = new
                {
                    type = "bearer",
                    bearer = new
                    {
                        token = "{{auth_token}}"
                    }
                },
                variable = new[]
                {
                    new { key = "base_url", value = "{{base_url}}", type = "string" },
                    new { key = "auth_token", value = "{{auth_token}}", type = "string" },
                    new { key = "user_id", value = "{{user_id}}", type = "string" }
                },
                item = new[]
                {
                    new
                    {
                        name = "Admin Analytics",
                        item = new[]
                        {
                            new
                            {
                                name = "Get Admin Dashboard",
                                request = new
                                {
                                    method = "GET",
                                    header = new[] { new { key = "Authorization", value = "Bearer {{auth_token}}" } },
                                    url = new
                                    {
                                        raw = "{{base_url}}/api/admin/analytics/dashboard",
                                        host = new[] { "{{base_url}}" },
                                        path = new[] { "api", "admin", "analytics", "dashboard" }
                                    }
                                }
                            }
                        }
                    },
                    new
                    {
                        name = "Transport Company Analytics",
                        item = new[]
                        {
                            new
                            {
                                name = "Get Transport Company Dashboard",
                                request = new
                                {
                                    method = "GET",
                                    header = new[] { new { key = "Authorization", value = "Bearer {{auth_token}}" } },
                                    url = new
                                    {
                                        raw = "{{base_url}}/api/transport-company/analytics/{{user_id}}/dashboard",
                                        host = new[] { "{{base_url}}" },
                                        path = new[] { "api", "transport-company", "analytics", "{{user_id}}", "dashboard" }
                                    }
                                }
                            }
                        }
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Postman collection");
            return StatusCode(500, "Internal server error");
        }
    }
}
