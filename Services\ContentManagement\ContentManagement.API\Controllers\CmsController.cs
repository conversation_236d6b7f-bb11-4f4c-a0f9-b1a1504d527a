using ContentManagement.Application.Commands.Content;
using ContentManagement.Application.DTOs;
using ContentManagement.Application.Queries.Content;
using ContentManagement.Infrastructure.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ContentManagement.API.Controllers;

/// <summary>
/// CMS controller for content management system operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,ContentManager")]
public class CmsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IContentPreviewService _previewService;
    private readonly IContentAnalyticsService _analyticsService;
    private readonly ILogger<CmsController> _logger;

    public CmsController(
        IMediator mediator,
        IContentPreviewService previewService,
        IContentAnalyticsService analyticsService,
        ILogger<CmsController> logger)
    {
        _mediator = mediator;
        _previewService = previewService;
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// Get content editor data
    /// </summary>
    [HttpGet("editor/{contentId:guid}")]
    public async Task<ActionResult<ContentEditorDto>> GetContentEditor(Guid contentId)
    {
        try
        {
            var content = await _mediator.Send(new GetContentByIdQuery
            {
                Id = contentId,
                IncludeVersions = true,
                IncludeAttachments = true,
                UserRoles = GetUserRoles(),
                IsAuthenticated = true
            });

            if (content == null)
                return NotFound();

            var editorData = new ContentEditorDto
            {
                Content = content,
                CanEdit = CanEditContent(content),
                CanApprove = CanApproveContent(),
                CanPublish = CanPublishContent(),
                CanDelete = CanDeleteContent(),
                WorkflowActions = GetAvailableWorkflowActions(content),
                LoadedAt = DateTime.UtcNow
            };

            return Ok(editorData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content editor data for {ContentId}", contentId);
            return StatusCode(500, "Failed to load content editor");
        }
    }

    /// <summary>
    /// Save content draft
    /// </summary>
    [HttpPost("save-draft")]
    public async Task<ActionResult<ContentDto>> SaveDraft([FromBody] SaveContentDraftCommand command)
    {
        try
        {
            command.UpdatedBy = GetUserId();
            command.UpdatedByName = GetUserName();

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save content draft");
            return StatusCode(500, "Failed to save draft");
        }
    }

    /// <summary>
    /// Generate content preview
    /// </summary>
    [HttpPost("preview")]
    public async Task<ActionResult<ContentPreviewDto>> GeneratePreview([FromBody] GeneratePreviewRequest request)
    {
        try
        {
            // Create a temporary content object for preview
            var tempContent = new Domain.Entities.Content(
                request.Title,
                request.Slug ?? GenerateSlug(request.Title),
                request.Type,
                request.ContentBody,
                GetUserId(),
                GetUserName(),
                request.Description,
                tags: request.Tags);

            var preview = await _previewService.GeneratePreviewAsync(tempContent);
            return Ok(preview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate content preview");
            return StatusCode(500, "Failed to generate preview");
        }
    }

    /// <summary>
    /// Validate content
    /// </summary>
    [HttpPost("validate")]
    public async Task<ActionResult<ContentValidationDto>> ValidateContent([FromBody] ValidateContentRequest request)
    {
        try
        {
            var validation = new ContentValidationDto
            {
                IsValid = true,
                Errors = new List<string>(),
                Warnings = new List<string>(),
                Suggestions = new List<string>()
            };

            // Title validation
            if (string.IsNullOrWhiteSpace(request.Title))
            {
                validation.IsValid = false;
                validation.Errors.Add("Title is required");
            }
            else if (request.Title.Length < 10)
            {
                validation.Warnings.Add("Title is quite short - consider making it more descriptive");
            }

            // Content body validation
            if (string.IsNullOrWhiteSpace(request.ContentBody))
            {
                validation.IsValid = false;
                validation.Errors.Add("Content body is required");
            }
            else if (request.ContentBody.Length < 100)
            {
                validation.Warnings.Add("Content is quite short - consider adding more detail");
            }

            // SEO validation
            if (string.IsNullOrWhiteSpace(request.MetaDescription))
            {
                validation.Suggestions.Add("Add a meta description to improve SEO");
            }

            if (!request.Tags?.Any() == true)
            {
                validation.Suggestions.Add("Add tags to improve content discoverability");
            }

            // Slug validation
            if (!string.IsNullOrWhiteSpace(request.Slug))
            {
                var isSlugUnique = await IsSlugUniqueAsync(request.Slug, request.Id);
                if (!isSlugUnique)
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Slug is already in use");
                }
            }

            validation.ValidatedAt = DateTime.UtcNow;
            return Ok(validation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate content");
            return StatusCode(500, "Failed to validate content");
        }
    }

    /// <summary>
    /// Get content templates
    /// </summary>
    [HttpGet("templates")]
    public async Task<ActionResult<List<ContentTemplateDto>>> GetContentTemplates(
        [FromQuery] Domain.Enums.ContentType? type = null)
    {
        await Task.CompletedTask; // Placeholder for async operation

        try
        {
            var templates = new List<ContentTemplateDto>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Name = "Basic FAQ",
                    Type = Domain.Enums.ContentType.Faq,
                    Description = "Standard FAQ template with question and answer format",
                    Template = "**Question:** [Your question here]\n\n**Answer:** [Your detailed answer here]",
                    IsDefault = true
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Name = "Policy Document",
                    Type = Domain.Enums.ContentType.Policy,
                    Description = "Standard policy document template",
                    Template = "# Policy Title\n\n## Purpose\n[Policy purpose]\n\n## Scope\n[Policy scope]\n\n## Policy Statement\n[Policy details]",
                    IsDefault = true
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Name = "News Article",
                    Type = Domain.Enums.ContentType.NewsArticle,
                    Description = "News article template with headline and body",
                    Template = "# Headline\n\n**Summary:** [Brief summary]\n\n## Article Body\n[Full article content]",
                    IsDefault = false
                }
            };

            if (type.HasValue)
            {
                templates = templates.Where(t => t.Type == type.Value).ToList();
            }

            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get content templates");
            return StatusCode(500, "Failed to load templates");
        }
    }

    /// <summary>
    /// Bulk operations on content
    /// </summary>
    [HttpPost("bulk-action")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BulkActionResultDto>> BulkAction([FromBody] BulkActionRequest request)
    {
        try
        {
            var result = new BulkActionResultDto
            {
                Action = request.Action,
                TotalItems = request.ContentIds.Count,
                SuccessCount = 0,
                FailureCount = 0,
                Errors = new List<string>()
            };

            foreach (var contentId in request.ContentIds)
            {
                try
                {
                    switch (request.Action.ToLowerInvariant())
                    {
                        case "publish":
                            await _mediator.Send(new PublishContentCommand
                            {
                                Id = contentId,
                                PublishedBy = GetUserId(),
                                PublishedByName = GetUserName()
                            });
                            break;

                        case "unpublish":
                            await _mediator.Send(new UnpublishContentCommand
                            {
                                Id = contentId,
                                UnpublishedBy = GetUserId(),
                                UnpublishedByName = GetUserName()
                            });
                            break;

                        case "archive":
                            await _mediator.Send(new ArchiveContentCommand
                            {
                                Id = contentId,
                                ArchivedBy = GetUserId(),
                                ArchivedByName = GetUserName()
                            });
                            break;

                        case "delete":
                            await _mediator.Send(new DeleteContentCommand
                            {
                                Id = contentId,
                                DeletedBy = GetUserId(),
                                DeletedByName = GetUserName()
                            });
                            break;

                        default:
                            result.Errors.Add($"Unknown action: {request.Action}");
                            result.FailureCount++;
                            continue;
                    }

                    result.SuccessCount++;
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"Failed to {request.Action} content {contentId}: {ex.Message}");
                }
            }

            result.CompletedAt = DateTime.UtcNow;
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform bulk action: {Action}", request.Action);
            return StatusCode(500, "Failed to perform bulk action");
        }
    }

    private bool CanEditContent(ContentDto content)
    {
        var userRoles = GetUserRoles();
        return userRoles.Contains("Admin") || 
               (userRoles.Contains("ContentManager") && content.Status != Domain.Enums.ContentStatus.Published);
    }

    private bool CanApproveContent()
    {
        var userRoles = GetUserRoles();
        return userRoles.Contains("Admin") || userRoles.Contains("ContentApprover");
    }

    private bool CanPublishContent()
    {
        var userRoles = GetUserRoles();
        return userRoles.Contains("Admin") || userRoles.Contains("ContentApprover");
    }

    private bool CanDeleteContent()
    {
        var userRoles = GetUserRoles();
        return userRoles.Contains("Admin");
    }

    private List<string> GetAvailableWorkflowActions(ContentDto content)
    {
        var actions = new List<string>();

        switch (content.Status)
        {
            case Domain.Enums.ContentStatus.Draft:
                actions.Add("SubmitForReview");
                if (CanDeleteContent()) actions.Add("Delete");
                break;

            case Domain.Enums.ContentStatus.PendingReview:
                if (CanApproveContent())
                {
                    actions.Add("Approve");
                    actions.Add("Reject");
                }
                break;

            case Domain.Enums.ContentStatus.Approved:
                if (CanPublishContent()) actions.Add("Publish");
                break;

            case Domain.Enums.ContentStatus.Published:
                if (CanPublishContent()) actions.Add("Unpublish");
                if (CanDeleteContent()) actions.Add("Archive");
                break;

            case Domain.Enums.ContentStatus.Rejected:
                actions.Add("SubmitForReview");
                if (CanDeleteContent()) actions.Add("Delete");
                break;
        }

        return actions;
    }

    private async Task<bool> IsSlugUniqueAsync(string slug, Guid? excludeId)
    {
        // This would typically call a repository method
        // For now, return true as placeholder
        await Task.CompletedTask;
        return true;
    }

    private static string GenerateSlug(string title)
    {
        return title.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace("?", "")
            .Replace("!", "")
            .Replace(".", "")
            .Replace(",", "");
    }

    private Guid GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
    }

    private List<string> GetUserRoles()
    {
        return User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
    }
}

// DTOs for CMS operations
public class ContentEditorDto
{
    public ContentDto? Content { get; set; }
    public bool CanEdit { get; set; }
    public bool CanApprove { get; set; }
    public bool CanPublish { get; set; }
    public bool CanDelete { get; set; }
    public List<string> WorkflowActions { get; set; } = new();
    public DateTime LoadedAt { get; set; }
}

public class SaveContentDraftCommand : IRequest<ContentDto>
{
    public Guid? Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public List<string>? Tags { get; set; }
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

public class GeneratePreviewRequest
{
    public string Title { get; set; } = string.Empty;
    public string? Slug { get; set; }
    public Domain.Enums.ContentType Type { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<string>? Tags { get; set; }
}

public class ValidateContentRequest
{
    public Guid? Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Slug { get; set; }
    public string ContentBody { get; set; } = string.Empty;
    public string? MetaDescription { get; set; }
    public List<string>? Tags { get; set; }
}

public class ContentValidationDto
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
    public DateTime ValidatedAt { get; set; }
}

public class ContentTemplateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public Domain.Enums.ContentType Type { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Template { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
}

public class BulkActionRequest
{
    public string Action { get; set; } = string.Empty;
    public List<Guid> ContentIds { get; set; } = new();
}

public class BulkActionResultDto
{
    public string Action { get; set; } = string.Empty;
    public int TotalItems { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime CompletedAt { get; set; }
}
