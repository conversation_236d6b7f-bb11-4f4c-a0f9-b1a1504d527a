using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderManagement.Application.Commands.CreateInvoice;
using OrderManagement.Application.Commands.UpdateInvoice;
using OrderManagement.Application.Commands.MarkInvoicePaid;
using OrderManagement.Application.Queries.GetInvoices;
using OrderManagement.Application.Queries.GetInvoiceById;
using OrderManagement.Domain.Enums;

namespace OrderManagement.API.Controllers;

[Authorize]
public class InvoiceController : BaseController
{
    /// <summary>
    /// Create a new invoice for an order
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceCommand command)
    {
        try
        {
            command.CreatedBy = GetCurrentUserId();
            var invoiceId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetInvoice), new { id = invoiceId }, invoiceId);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get invoice by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Application.DTOs.InvoiceDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetInvoice(Guid id)
    {
        try
        {
            var query = new GetInvoiceByIdQuery
            {
                InvoiceId = id,
                UserId = GetCurrentUserId()
            };

            var invoice = await Mediator.Send(query);
            
            if (invoice == null)
                return NotFound(new { message = "Invoice not found" });

            return Ok(invoice);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get invoices with filtering and pagination
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(Application.DTOs.PagedResult<Application.DTOs.InvoiceSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetInvoices(
        [FromQuery] Guid? orderId = null,
        [FromQuery] InvoiceStatus? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] DateTime? dueBefore = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? overdueOnly = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] string? sortDirection = "desc")
    {
        try
        {
            var query = new GetInvoicesQuery
            {
                OrderId = orderId,
                Status = status,
                FromDate = fromDate,
                ToDate = toDate,
                DueBefore = dueBefore,
                SearchTerm = searchTerm,
                OverdueOnly = overdueOnly,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection,
                UserId = GetCurrentUserId()
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get invoices for a specific order
    /// </summary>
    [HttpGet("order/{orderId}")]
    [ProducesResponseType(typeof(Application.DTOs.PagedResult<Application.DTOs.InvoiceSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetInvoicesByOrder(
        Guid orderId,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var query = new GetInvoicesQuery
            {
                OrderId = orderId,
                Page = page,
                PageSize = pageSize,
                UserId = GetCurrentUserId()
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Get overdue invoices
    /// </summary>
    [HttpGet("overdue")]
    [ProducesResponseType(typeof(Application.DTOs.PagedResult<Application.DTOs.InvoiceSummaryDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOverdueInvoices(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var query = new GetInvoicesQuery
            {
                OverdueOnly = true,
                Page = page,
                PageSize = pageSize,
                SortBy = "DueDate",
                SortDirection = "asc",
                UserId = GetCurrentUserId()
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Update an invoice
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateInvoice(Guid id, [FromBody] UpdateInvoiceCommand command)
    {
        try
        {
            command.InvoiceId = id;
            command.UpdatedBy = GetCurrentUserId();
            
            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Invoice updated successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Send an invoice (change status from Draft to Sent)
    /// </summary>
    [HttpPost("{id}/send")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> SendInvoice(Guid id, [FromBody] SendInvoiceRequest? request = null)
    {
        try
        {
            var command = new UpdateInvoiceCommand
            {
                InvoiceId = id,
                Status = InvoiceStatus.Sent,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Invoice sent successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Mark an invoice as paid
    /// </summary>
    [HttpPost("{id}/paid")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MarkInvoicePaid(Guid id, [FromBody] MarkInvoicePaidRequest request)
    {
        try
        {
            var command = new MarkInvoicePaidCommand
            {
                InvoiceId = id,
                PaymentReference = request.PaymentReference,
                PaidAt = request.PaidAt,
                PaidAmount = request.PaidAmount,
                PaymentNotes = request.PaymentNotes,
                ProcessedBy = GetCurrentUserId()
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Invoice marked as paid successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    /// <summary>
    /// Cancel an invoice
    /// </summary>
    [HttpPost("{id}/cancel")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CancelInvoice(Guid id, [FromBody] CancelInvoiceRequest? request = null)
    {
        try
        {
            var command = new UpdateInvoiceCommand
            {
                InvoiceId = id,
                Status = InvoiceStatus.Cancelled,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await Mediator.Send(command);
            return Ok(new { success = result, message = "Invoice cancelled successfully" });
        }
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }
}

// Request DTOs for the controller
public class SendInvoiceRequest
{
    public string? Notes { get; set; }
}

public class MarkInvoicePaidRequest
{
    public string? PaymentReference { get; set; }
    public DateTime? PaidAt { get; set; }
    public decimal? PaidAmount { get; set; }
    public string? PaymentNotes { get; set; }
}

public class CancelInvoiceRequest
{
    public string? Reason { get; set; }
}
