using MediatR;
using AutoMapper;
using Microsoft.Extensions.Logging;
using MonitoringObservability.Application.DTOs;
using MonitoringObservability.Domain.Interfaces;
using DomainEnums = MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Application.Commands.Handlers;

public class AnalyzeSystemPerformanceCommandHandler : IRequestHandler<AnalyzeSystemPerformanceCommand, PerformanceAnalysisDto>
{
    private readonly IMetricRepository _metricRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IHealthCheckRepository _healthCheckRepository;
    private readonly ILogger<AnalyzeSystemPerformanceCommandHandler> _logger;

    public AnalyzeSystemPerformanceCommandHandler(
        IMetricRepository metricRepository,
        IAlertRepository alertRepository,
        IHealthCheckRepository healthCheckRepository,
        ILogger<AnalyzeSystemPerformanceCommandHandler> logger)
    {
        _metricRepository = metricRepository;
        _alertRepository = alertRepository;
        _healthCheckRepository = healthCheckRepository;
        _logger = logger;
    }

    public async Task<PerformanceAnalysisDto> Handle(AnalyzeSystemPerformanceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting system performance analysis for period {Period}", request.Period);

            var fromDate = DateTime.UtcNow - request.Period;
            var servicePerformances = new List<ServicePerformanceDto>();
            var recommendations = new List<PerformanceRecommendationDto>();
            var anomalies = new List<PerformanceAnomalyDto>();

            // Get all services or filter by requested services
            var services = request.ServiceNames ?? await GetAllServicesAsync(cancellationToken);

            foreach (var serviceName in services)
            {
                var servicePerformance = await AnalyzeServicePerformanceAsync(serviceName, request.Period, cancellationToken);
                servicePerformances.Add(servicePerformance);

                if (request.IncludeRecommendations)
                {
                    var serviceRecommendations = await GenerateServiceRecommendationsAsync(serviceName, servicePerformance, cancellationToken);
                    recommendations.AddRange(serviceRecommendations);
                }

                if (request.DetectAnomalies)
                {
                    var serviceAnomalies = await DetectServiceAnomaliesAsync(serviceName, request.Period, cancellationToken);
                    anomalies.AddRange(serviceAnomalies);
                }
            }

            var overallScore = CalculateOverallPerformanceScore(servicePerformances);

            var analysis = new PerformanceAnalysisDto
            {
                AnalysisPeriod = request.Period,
                ServicePerformances = servicePerformances,
                Recommendations = recommendations,
                Anomalies = anomalies,
                OverallScore = overallScore,
                GeneratedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Completed system performance analysis. Overall score: {Score}", overallScore.Score);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze system performance");
            throw;
        }
    }

    private async Task<List<string>> GetAllServicesAsync(CancellationToken cancellationToken)
    {
        var metrics = await _metricRepository.SearchAsync(cancellationToken: cancellationToken);
        return metrics.Select(m => m.ServiceName).Distinct().ToList();
    }

    private async Task<ServicePerformanceDto> AnalyzeServicePerformanceAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken)
    {
        var fromDate = DateTime.UtcNow - period;

        // Get service metrics
        var serviceMetrics = await _metricRepository.GetByServiceAsync(serviceName, cancellationToken);

        // Calculate performance indicators
        var responseTimeMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("ResponseTime", StringComparison.OrdinalIgnoreCase));
        var throughputMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("Throughput", StringComparison.OrdinalIgnoreCase));
        var errorRateMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("ErrorRate", StringComparison.OrdinalIgnoreCase));
        var cpuMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("CPU", StringComparison.OrdinalIgnoreCase));
        var memoryMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("Memory", StringComparison.OrdinalIgnoreCase));
        var diskMetric = serviceMetrics.FirstOrDefault(m => m.Name.Contains("Disk", StringComparison.OrdinalIgnoreCase));

        var performance = new ServicePerformanceDto
        {
            ServiceName = serviceName,
            AverageResponseTime = responseTimeMetric?.CurrentValue.Value ?? 0,
            Throughput = throughputMetric?.CurrentValue.Value ?? 0,
            ErrorRate = errorRateMetric?.CurrentValue.Value ?? 0,
            CpuUsage = cpuMetric?.CurrentValue.Value ?? 0,
            MemoryUsage = memoryMetric?.CurrentValue.Value ?? 0,
            DiskUsage = diskMetric?.CurrentValue.Value ?? 0
        };

        // Calculate performance grade
        performance.Grade = CalculatePerformanceGrade(performance);

        // Identify issues
        performance.Issues = IdentifyPerformanceIssues(performance);

        // Generate basic recommendations
        performance.Recommendations = GenerateBasicRecommendations(performance);

        return performance;
    }

    private async Task<List<PerformanceRecommendationDto>> GenerateServiceRecommendationsAsync(
        string serviceName,
        ServicePerformanceDto performance,
        CancellationToken cancellationToken)
    {
        var recommendations = new List<PerformanceRecommendationDto>();

        // High response time recommendation
        if (performance.AverageResponseTime > 1000) // > 1 second
        {
            recommendations.Add(new PerformanceRecommendationDto
            {
                ServiceName = serviceName,
                Type = RecommendationType.Performance,
                Priority = performance.AverageResponseTime > 5000 ? DomainEnums.RecommendationPriority.Critical : DomainEnums.RecommendationPriority.High,
                Title = "High Response Time Detected",
                Description = $"Average response time is {performance.AverageResponseTime:F2}ms, which exceeds recommended thresholds.",
                ActionRequired = "Investigate database queries, optimize code paths, consider caching strategies.",
                EstimatedImpact = 0.3, // 30% improvement
                EstimatedEffort = TimeSpan.FromHours(8),
                IdentifiedAt = DateTime.UtcNow
            });
        }

        // High error rate recommendation
        if (performance.ErrorRate > 5) // > 5%
        {
            recommendations.Add(new PerformanceRecommendationDto
            {
                ServiceName = serviceName,
                Type = RecommendationType.Reliability,
                Priority = performance.ErrorRate > 20 ? DomainEnums.RecommendationPriority.Critical : DomainEnums.RecommendationPriority.High,
                Title = "High Error Rate Detected",
                Description = $"Error rate is {performance.ErrorRate:F2}%, indicating reliability issues.",
                ActionRequired = "Review error logs, implement better error handling, add circuit breakers.",
                EstimatedImpact = 0.4, // 40% improvement
                EstimatedEffort = TimeSpan.FromHours(16),
                IdentifiedAt = DateTime.UtcNow
            });
        }

        // High resource usage recommendations
        if (performance.CpuUsage > 80)
        {
            recommendations.Add(new PerformanceRecommendationDto
            {
                ServiceName = serviceName,
                Type = RecommendationType.Capacity,
                Priority = performance.CpuUsage > 95 ? DomainEnums.RecommendationPriority.Critical : DomainEnums.RecommendationPriority.Medium,
                Title = "High CPU Usage",
                Description = $"CPU usage is {performance.CpuUsage:F1}%, consider scaling or optimization.",
                ActionRequired = "Scale horizontally, optimize CPU-intensive operations, or upgrade hardware.",
                EstimatedImpact = 0.25,
                EstimatedEffort = TimeSpan.FromHours(4),
                IdentifiedAt = DateTime.UtcNow
            });
        }

        if (performance.MemoryUsage > 85)
        {
            recommendations.Add(new PerformanceRecommendationDto
            {
                ServiceName = serviceName,
                Type = RecommendationType.Capacity,
                Priority = performance.MemoryUsage > 95 ? DomainEnums.RecommendationPriority.Critical : DomainEnums.RecommendationPriority.Medium,
                Title = "High Memory Usage",
                Description = $"Memory usage is {performance.MemoryUsage:F1}%, potential memory pressure.",
                ActionRequired = "Investigate memory leaks, optimize data structures, increase memory allocation.",
                EstimatedImpact = 0.2,
                EstimatedEffort = TimeSpan.FromHours(6),
                IdentifiedAt = DateTime.UtcNow
            });
        }

        return recommendations;
    }

    private async Task<List<PerformanceAnomalyDto>> DetectServiceAnomaliesAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken)
    {
        var anomalies = new List<PerformanceAnomalyDto>();
        var fromDate = DateTime.UtcNow - period;

        // Get service metrics for anomaly detection
        var serviceMetrics = await _metricRepository.GetByServiceAsync(serviceName, cancellationToken);

        foreach (var metric in serviceMetrics.Where(m => m.IsActive))
        {
            // Get historical data for baseline
            var dataPoints = await _metricRepository.GetDataPointsAsync(metric.Id, fromDate, DateTime.UtcNow, cancellationToken);

            if (dataPoints.Count() < 10) continue; // Need sufficient data for anomaly detection

            var values = dataPoints.Select(dp => dp.Value).ToList();
            var average = values.Average();
            var stdDev = CalculateStandardDeviation(values);
            var currentValue = metric.CurrentValue.Value;

            // Simple anomaly detection using standard deviation
            var deviationFromMean = Math.Abs(currentValue - average);
            var zScore = stdDev > 0 ? deviationFromMean / stdDev : 0;

            if (zScore > 2) // More than 2 standard deviations
            {
                var severity = zScore > 3 ? AnomalySeverity.Critical :
                              zScore > 2.5 ? AnomalySeverity.High : AnomalySeverity.Medium;

                anomalies.Add(new PerformanceAnomalyDto
                {
                    ServiceName = serviceName,
                    MetricName = metric.Name,
                    ExpectedValue = average,
                    ActualValue = currentValue,
                    DeviationPercentage = (deviationFromMean / average) * 100,
                    Severity = severity,
                    DetectedAt = DateTime.UtcNow,
                    Description = $"Metric {metric.Name} shows unusual behavior with Z-score of {zScore:F2}"
                });
            }
        }

        return anomalies;
    }

    private PerformanceGrade CalculatePerformanceGrade(ServicePerformanceDto performance)
    {
        var score = 100.0;

        // Response time scoring (0-30 points)
        if (performance.AverageResponseTime > 5000) score -= 30;
        else if (performance.AverageResponseTime > 2000) score -= 20;
        else if (performance.AverageResponseTime > 1000) score -= 10;
        else if (performance.AverageResponseTime > 500) score -= 5;

        // Error rate scoring (0-25 points)
        if (performance.ErrorRate > 20) score -= 25;
        else if (performance.ErrorRate > 10) score -= 20;
        else if (performance.ErrorRate > 5) score -= 15;
        else if (performance.ErrorRate > 1) score -= 5;

        // Resource usage scoring (0-45 points total)
        if (performance.CpuUsage > 95) score -= 15;
        else if (performance.CpuUsage > 80) score -= 10;
        else if (performance.CpuUsage > 70) score -= 5;

        if (performance.MemoryUsage > 95) score -= 15;
        else if (performance.MemoryUsage > 85) score -= 10;
        else if (performance.MemoryUsage > 75) score -= 5;

        if (performance.DiskUsage > 95) score -= 15;
        else if (performance.DiskUsage > 90) score -= 10;
        else if (performance.DiskUsage > 80) score -= 5;

        return score switch
        {
            >= 90 => PerformanceGrade.Excellent,
            >= 80 => PerformanceGrade.Good,
            >= 70 => PerformanceGrade.Fair,
            >= 60 => PerformanceGrade.Poor,
            _ => PerformanceGrade.Critical
        };
    }

    private List<string> IdentifyPerformanceIssues(ServicePerformanceDto performance)
    {
        var issues = new List<string>();

        if (performance.AverageResponseTime > 2000)
            issues.Add($"High response time: {performance.AverageResponseTime:F0}ms");

        if (performance.ErrorRate > 5)
            issues.Add($"High error rate: {performance.ErrorRate:F1}%");

        if (performance.CpuUsage > 80)
            issues.Add($"High CPU usage: {performance.CpuUsage:F1}%");

        if (performance.MemoryUsage > 85)
            issues.Add($"High memory usage: {performance.MemoryUsage:F1}%");

        if (performance.DiskUsage > 90)
            issues.Add($"High disk usage: {performance.DiskUsage:F1}%");

        if (performance.Throughput < 10)
            issues.Add($"Low throughput: {performance.Throughput:F1} req/s");

        return issues;
    }

    private List<string> GenerateBasicRecommendations(ServicePerformanceDto performance)
    {
        var recommendations = new List<string>();

        if (performance.AverageResponseTime > 1000)
            recommendations.Add("Optimize database queries and implement caching");

        if (performance.ErrorRate > 5)
            recommendations.Add("Implement better error handling and monitoring");

        if (performance.CpuUsage > 80)
            recommendations.Add("Consider horizontal scaling or CPU optimization");

        if (performance.MemoryUsage > 85)
            recommendations.Add("Investigate memory usage and potential leaks");

        if (performance.DiskUsage > 90)
            recommendations.Add("Clean up old data or increase storage capacity");

        return recommendations;
    }

    private OverallPerformanceScoreDto CalculateOverallPerformanceScore(List<ServicePerformanceDto> servicePerformances)
    {
        if (!servicePerformances.Any())
        {
            return new OverallPerformanceScoreDto
            {
                Score = 0,
                Grade = PerformanceGrade.Critical,
                Summary = "No services analyzed",
                ComponentScores = new Dictionary<string, double>(),
                KeyIssues = new List<string> { "No services available for analysis" },
                TopRecommendations = new List<string>()
            };
        }

        var gradeScores = servicePerformances.Select(sp => sp.Grade switch
        {
            PerformanceGrade.Excellent => 100.0,
            PerformanceGrade.Good => 80.0,
            PerformanceGrade.Fair => 60.0,
            PerformanceGrade.Poor => 40.0,
            PerformanceGrade.Critical => 20.0,
            _ => 0.0
        });

        var overallScore = gradeScores.Average();
        var overallGrade = overallScore switch
        {
            >= 90 => PerformanceGrade.Excellent,
            >= 80 => PerformanceGrade.Good,
            >= 60 => PerformanceGrade.Fair,
            >= 40 => PerformanceGrade.Poor,
            _ => PerformanceGrade.Critical
        };

        var componentScores = servicePerformances.ToDictionary(
            sp => sp.ServiceName,
            sp => gradeScores.ElementAt(servicePerformances.IndexOf(sp))
        );

        var keyIssues = servicePerformances
            .SelectMany(sp => sp.Issues)
            .GroupBy(issue => issue)
            .OrderByDescending(g => g.Count())
            .Take(5)
            .Select(g => $"{g.Key} (affects {g.Count()} services)")
            .ToList();

        var topRecommendations = servicePerformances
            .SelectMany(sp => sp.Recommendations)
            .GroupBy(rec => rec)
            .OrderByDescending(g => g.Count())
            .Take(5)
            .Select(g => g.Key)
            .ToList();

        return new OverallPerformanceScoreDto
        {
            Score = overallScore,
            Grade = overallGrade,
            Summary = GeneratePerformanceSummary(overallGrade, servicePerformances.Count),
            ComponentScores = componentScores,
            KeyIssues = keyIssues,
            TopRecommendations = topRecommendations
        };
    }

    private string GeneratePerformanceSummary(PerformanceGrade grade, int serviceCount)
    {
        return grade switch
        {
            PerformanceGrade.Excellent => $"All {serviceCount} services are performing excellently with minimal issues.",
            PerformanceGrade.Good => $"Most of the {serviceCount} services are performing well with minor optimization opportunities.",
            PerformanceGrade.Fair => $"The {serviceCount} services show mixed performance with several areas needing attention.",
            PerformanceGrade.Poor => $"Multiple services among the {serviceCount} analyzed require immediate performance improvements.",
            PerformanceGrade.Critical => $"Critical performance issues detected across the {serviceCount} services requiring urgent attention.",
            _ => "Performance analysis completed."
        };
    }

    private double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count < 2) return 0;

        var average = values.Average();
        var sumOfSquares = values.Sum(v => Math.Pow(v - average, 2));
        return Math.Sqrt(sumOfSquares / (values.Count - 1));
    }
}
