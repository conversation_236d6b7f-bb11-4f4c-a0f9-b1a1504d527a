using MediatR;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Application.Commands.AttachMilestoneTemplate;

public class AttachMilestoneTemplateCommand : IRequest<bool>
{
    public Guid RfqId { get; set; }
    public Guid MilestoneTemplateId { get; set; }
    public Guid AssignedBy { get; set; }
    public string? AssignedByName { get; set; }
    public Money? TotalContractValue { get; set; }
    public string? CustomPayoutStructure { get; set; }
    public Dictionary<string, object>? AssignmentMetadata { get; set; }
    public bool CreateTimelineEvent { get; set; } = true;
}
