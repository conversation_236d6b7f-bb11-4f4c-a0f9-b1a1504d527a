using ContentManagement.Application.DTOs;
using ContentManagement.Domain.Enums;
using MediatR;

namespace ContentManagement.Application.Commands.Faq;

/// <summary>
/// Command to create a new FAQ item
/// </summary>
public class CreateFaqItemCommand : IRequest<FaqItemDto>
{
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public FaqCategory Category { get; set; }
    public List<string>? RoleVisibility { get; set; }
    public bool IsHighlighted { get; set; } = false;
    public ContentVisibilityDto? Visibility { get; set; }
    public List<string>? Tags { get; set; }
    public int SortOrder { get; set; } = 0;
    
    // User context
    public Guid CreatedBy { get; set; }
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update an existing FAQ item
/// </summary>
public class UpdateFaqItemCommand : IRequest<FaqItemDto>
{
    public Guid Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update FAQ category
/// </summary>
public class UpdateFaqCategoryCommand : IRequest<FaqItemDto>
{
    public Guid Id { get; set; }
    public FaqCategory Category { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to update FAQ role visibility
/// </summary>
public class UpdateFaqRoleVisibilityCommand : IRequest<FaqItemDto>
{
    public Guid Id { get; set; }
    public List<string> RoleVisibility { get; set; } = new();
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to set FAQ highlight status
/// </summary>
public class SetFaqHighlightCommand : IRequest<FaqItemDto>
{
    public Guid Id { get; set; }
    public bool IsHighlighted { get; set; }
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to record FAQ view
/// </summary>
public class RecordFaqViewCommand : IRequest<bool>
{
    public Guid Id { get; set; }
}

/// <summary>
/// Command to rate FAQ helpfulness
/// </summary>
public class RateFaqHelpfulnessCommand : IRequest<FaqItemDto>
{
    public Guid Id { get; set; }
    public int Rating { get; set; } // 1-5 scale
    
    // User context
    public Guid RatedBy { get; set; }
    public string RatedByName { get; set; } = string.Empty;
}

/// <summary>
/// Command to bulk update FAQ sort orders
/// </summary>
public class BulkUpdateFaqSortOrderCommand : IRequest<List<FaqSummaryDto>>
{
    public List<FaqSortOrderItem> Items { get; set; } = new();
    
    // User context
    public Guid UpdatedBy { get; set; }
    public string UpdatedByName { get; set; } = string.Empty;
}

/// <summary>
/// FAQ sort order item for bulk updates
/// </summary>
public class FaqSortOrderItem
{
    public Guid Id { get; set; }
    public int SortOrder { get; set; }
}
