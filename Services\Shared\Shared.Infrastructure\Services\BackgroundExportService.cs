using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace Shared.Infrastructure.Services
{
    /// <summary>
    /// Background service for processing large export requests
    /// </summary>
    public class BackgroundExportService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BackgroundExportService> _logger;
        private readonly ConcurrentQueue<ExportJob> _exportQueue;
        private readonly Dictionary<string, ExportJobStatus> _jobStatuses;
        private readonly object _statusLock = new();

        public BackgroundExportService(
            IServiceProvider serviceProvider,
            ILogger<BackgroundExportService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _exportQueue = new ConcurrentQueue<ExportJob>();
            _jobStatuses = new Dictionary<string, ExportJobStatus>();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Background Export Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (_exportQueue.TryDequeue(out var job))
                    {
                        await ProcessExportJobAsync(job, stoppingToken);
                    }
                    else
                    {
                        // Wait for new jobs
                        await Task.Delay(1000, stoppingToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in background export service");
                    await Task.Delay(5000, stoppingToken); // Wait before retrying
                }
            }

            _logger.LogInformation("Background Export Service stopped");
        }

        public string QueueExportJob(ExportJob job)
        {
            var jobId = Guid.NewGuid().ToString();
            job.JobId = jobId;
            job.QueuedAt = DateTime.UtcNow;

            _exportQueue.Enqueue(job);

            lock (_statusLock)
            {
                _jobStatuses[jobId] = new ExportJobStatus
                {
                    JobId = jobId,
                    Status = "Queued",
                    QueuedAt = job.QueuedAt,
                    EstimatedCompletionTime = DateTime.UtcNow.AddMinutes(5) // Default estimate
                };
            }

            _logger.LogInformation("Export job {JobId} queued for processing", jobId);
            return jobId;
        }

        public ExportJobStatus? GetJobStatus(string jobId)
        {
            lock (_statusLock)
            {
                return _jobStatuses.TryGetValue(jobId, out var status) ? status : null;
            }
        }

        private async Task ProcessExportJobAsync(ExportJob job, CancellationToken cancellationToken)
        {
            try
            {
                UpdateJobStatus(job.JobId, "Processing", "Export is being generated...");

                using var scope = _serviceProvider.CreateScope();
                
                // Process based on job type
                var result = job.JobType switch
                {
                    "UserSummary" => await ProcessUserSummaryExport(job, scope, cancellationToken),
                    "KycDocuments" => await ProcessKycDocumentsExport(job, scope, cancellationToken),
                    "UsageLogs" => await ProcessUsageLogsExport(job, scope, cancellationToken),
                    "FeedbackHistory" => await ProcessFeedbackHistoryExport(job, scope, cancellationToken),
                    "AdminAuditTrail" => await ProcessAdminAuditTrailExport(job, scope, cancellationToken),
                    _ => throw new ArgumentException($"Unknown job type: {job.JobType}")
                };

                UpdateJobStatus(job.JobId, "Completed", "Export completed successfully", result);

                // Send notification email
                await SendCompletionNotificationAsync(job, result, scope);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing export job {JobId}", job.JobId);
                UpdateJobStatus(job.JobId, "Failed", ex.Message);
            }
        }

        private async Task<ExportResult> ProcessUserSummaryExport(ExportJob job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Implementation would call the actual export service
            await Task.Delay(2000, cancellationToken); // Simulate processing
            
            return new ExportResult
            {
                FileName = $"user_summary_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                FileUrl = "https://storage.example.com/exports/user_summary.csv",
                RecordCount = 1000,
                FileSizeBytes = 50000
            };
        }

        private async Task<ExportResult> ProcessKycDocumentsExport(ExportJob job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Implementation would call the actual export service
            await Task.Delay(5000, cancellationToken); // Simulate processing
            
            return new ExportResult
            {
                FileName = $"kyc_documents_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip",
                FileUrl = "https://storage.example.com/exports/kyc_documents.zip",
                RecordCount = 500,
                FileSizeBytes = 10000000
            };
        }

        private async Task<ExportResult> ProcessUsageLogsExport(ExportJob job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Implementation would call the actual export service
            await Task.Delay(3000, cancellationToken); // Simulate processing
            
            return new ExportResult
            {
                FileName = $"usage_logs_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                FileUrl = "https://storage.example.com/exports/usage_logs.csv",
                RecordCount = 5000,
                FileSizeBytes = 250000
            };
        }

        private async Task<ExportResult> ProcessFeedbackHistoryExport(ExportJob job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Implementation would call the actual export service
            await Task.Delay(1500, cancellationToken); // Simulate processing
            
            return new ExportResult
            {
                FileName = $"feedback_history_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                FileUrl = "https://storage.example.com/exports/feedback_history.csv",
                RecordCount = 800,
                FileSizeBytes = 120000
            };
        }

        private async Task<ExportResult> ProcessAdminAuditTrailExport(ExportJob job, IServiceScope scope, CancellationToken cancellationToken)
        {
            // Implementation would call the actual export service
            await Task.Delay(4000, cancellationToken); // Simulate processing
            
            return new ExportResult
            {
                FileName = $"admin_audit_trail_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv",
                FileUrl = "https://storage.example.com/exports/admin_audit_trail.csv",
                RecordCount = 2000,
                FileSizeBytes = 300000
            };
        }

        private async Task SendCompletionNotificationAsync(ExportJob job, ExportResult result, IServiceScope scope)
        {
            try
            {
                // Implementation would send email notification
                _logger.LogInformation("Export completion notification sent for job {JobId} to user {UserId}", 
                    job.JobId, job.RequestedBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send completion notification for job {JobId}", job.JobId);
            }
        }

        private void UpdateJobStatus(string jobId, string status, string message = "", ExportResult? result = null)
        {
            lock (_statusLock)
            {
                if (_jobStatuses.TryGetValue(jobId, out var jobStatus))
                {
                    jobStatus.Status = status;
                    jobStatus.Message = message;
                    jobStatus.UpdatedAt = DateTime.UtcNow;
                    
                    if (result != null)
                    {
                        jobStatus.Result = result;
                        jobStatus.CompletedAt = DateTime.UtcNow;
                    }
                }
            }
        }
    }

    /// <summary>
    /// Export job definition
    /// </summary>
    public class ExportJob
    {
        public string JobId { get; set; } = string.Empty;
        public string JobType { get; set; } = string.Empty;
        public Guid RequestedBy { get; set; }
        public string RequestedByEmail { get; set; } = string.Empty;
        public string RequestData { get; set; } = string.Empty; // JSON serialized request
        public DateTime QueuedAt { get; set; }
        public int Priority { get; set; } = 1; // 1 = Normal, 2 = High, 3 = Critical
    }

    /// <summary>
    /// Export job status
    /// </summary>
    public class ExportJobStatus
    {
        public string JobId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Queued, Processing, Completed, Failed
        public string Message { get; set; } = string.Empty;
        public DateTime QueuedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime? EstimatedCompletionTime { get; set; }
        public ExportResult? Result { get; set; }
    }

    /// <summary>
    /// Export result
    /// </summary>
    public class ExportResult
    {
        public string FileName { get; set; } = string.Empty;
        public string FileUrl { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddDays(7);
    }
}
