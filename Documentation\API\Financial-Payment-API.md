# 💰 Financial & Payment Service API Documentation

## 📋 Overview

The Financial & Payment Service handles escrow management, multi-party settlements, payment processing, commission calculations, and financial dispute resolution. This service is 94% complete and production-ready.

**Base URL**: `/api/v1/payments` (via API Gateway)  
**Direct URL**: `http://localhost:5007`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control

## 🔐 Authentication & Permissions

### Required Permissions

| Operation              | Permission                     | Roles                           |
| ---------------------- | ------------------------------ | ------------------------------- |
| View payments          | `payments.read`                | All authenticated users         |
| Process payments       | `payments.process`             | Admin, Broker, TransportCompany |
| Manage escrow          | `payments.escrow.manage`       | Admin, Broker, TransportCompany |
| View settlements       | `payments.settlements.read`    | <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>          |
| Process settlements    | `payments.settlements.process` | Ad<PERSON>, Broker                   |
| Manage disputes        | `payments.disputes.manage`     | Admin, Finance                  |
| View financial reports | `payments.reports.read`        | Admin, Finance, Broker          |
| Configure taxes        | `payments.tax.configure`       | Admin only                      |

## 📊 Core Endpoints

### 1. Escrow Management

#### Create Escrow Account

```http
POST /api/v1/payments/escrow
Authorization: Bearer <token>
Content-Type: application/json

{
  "orderId": "order-uuid",
  "transportCompanyId": "company-uuid",
  "brokerId": "broker-uuid",
  "carrierId": "carrier-uuid",
  "totalAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "milestones": [
    {
      "name": "Pickup Completed",
      "description": "Cargo picked up from source",
      "payoutPercentage": 30.0,
      "amount": {
        "amount": 7500.0,
        "currency": "INR"
      },
      "triggerCondition": "PickupCompleted"
    },
    {
      "name": "Delivery Completed",
      "description": "Cargo delivered to destination",
      "payoutPercentage": 70.0,
      "amount": {
        "amount": 17500.0,
        "currency": "INR"
      },
      "triggerCondition": "DeliveryCompleted"
    }
  ],
  "notes": "Escrow for electronics shipment Mumbai to Delhi"
}
```

**Response:**

```json
{
  "id": "escrow-uuid",
  "escrowAccountNumber": "ESC-2024-001234",
  "orderId": "order-uuid",
  "transportCompanyId": "company-uuid",
  "brokerId": "broker-uuid",
  "carrierId": "carrier-uuid",
  "totalAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "availableAmount": {
    "amount": 0.0,
    "currency": "INR"
  },
  "reservedAmount": {
    "amount": 0.0,
    "currency": "INR"
  },
  "status": "Created",
  "milestones": [
    {
      "id": "milestone-uuid-1",
      "name": "Pickup Completed",
      "payoutPercentage": 30.0,
      "amount": {
        "amount": 7500.0,
        "currency": "INR"
      },
      "status": "Pending",
      "triggerCondition": "PickupCompleted"
    }
  ],
  "createdAt": "2024-01-15T10:00:00Z"
}
```

#### Fund Escrow Account

```http
POST /api/v1/payments/escrow/{escrowId}/fund
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "paymentMethodId": "pm-uuid",
  "paymentGateway": "Razorpay",
  "notes": "Full funding for order completion"
}
```

#### Release Escrow Funds

```http
POST /api/v1/payments/escrow/{escrowId}/release
Authorization: Bearer <token>
Content-Type: application/json

{
  "milestoneId": "milestone-uuid",
  "amount": {
    "amount": 7500.0,
    "currency": "INR"
  },
  "recipientId": "carrier-uuid",
  "releaseReason": "Pickup milestone completed",
  "notes": "Automatic release triggered by pickup completion"
}
```

#### Get Escrow Account Details

```http
GET /api/v1/payments/escrow/{escrowId}
Authorization: Bearer <token>
```

**Response:**

```json
{
  "id": "escrow-uuid",
  "escrowAccountNumber": "ESC-2024-001234",
  "orderId": "order-uuid",
  "status": "PartiallyReleased",
  "totalAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "availableAmount": {
    "amount": 17500.0,
    "currency": "INR"
  },
  "releasedAmount": {
    "amount": 7500.0,
    "currency": "INR"
  },
  "transactions": [
    {
      "id": "transaction-uuid",
      "type": "Fund",
      "amount": {
        "amount": 25000.0,
        "currency": "INR"
      },
      "participantId": "company-uuid",
      "paymentGatewayTransactionId": "txn_123456789",
      "timestamp": "2024-01-15T10:30:00Z",
      "status": "Completed"
    },
    {
      "id": "transaction-uuid-2",
      "type": "Release",
      "amount": {
        "amount": 7500.0,
        "currency": "INR"
      },
      "participantId": "carrier-uuid",
      "milestoneId": "milestone-uuid-1",
      "timestamp": "2024-01-16T14:00:00Z",
      "status": "Completed"
    }
  ],
  "milestones": [
    {
      "id": "milestone-uuid-1",
      "name": "Pickup Completed",
      "status": "Completed",
      "completedAt": "2024-01-16T14:00:00Z",
      "amount": {
        "amount": 7500.0,
        "currency": "INR"
      }
    },
    {
      "id": "milestone-uuid-2",
      "name": "Delivery Completed",
      "status": "Pending",
      "amount": {
        "amount": 17500.0,
        "currency": "INR"
      }
    }
  ]
}
```

### 2. Payment Processing

#### Process Payment

```http
POST /api/v1/payments/process
Authorization: Bearer <token>
Content-Type: application/json

{
  "gatewayName": "Razorpay",
  "amount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "userId": "user-uuid",
  "paymentMethodId": "pm-uuid",
  "paymentType": "EscrowFunding|Settlement|Commission|Regular",
  "description": "Payment for order #ORD-2024-001234",
  "metadata": {
    "orderId": "order-uuid",
    "escrowAccountId": "escrow-uuid"
  },
  "callbackUrl": "https://app.tli.com/payment/callback",
  "cancelUrl": "https://app.tli.com/payment/cancel"
}
```

**Response:**

```json
{
  "success": true,
  "transactionId": "txn_123456789",
  "gatewayTransactionId": "pay_abcdef123456",
  "status": "Pending|Completed|Failed",
  "amount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "paymentUrl": "https://checkout.razorpay.com/v1/checkout.js",
  "redirectUrl": "https://razorpay.com/payment/pay_abcdef123456",
  "expiresAt": "2024-01-15T11:00:00Z",
  "metadata": {
    "orderId": "order-uuid",
    "escrowAccountId": "escrow-uuid"
  }
}
```

#### Get Payment Status

```http
GET /api/v1/payments/status/{transactionId}
Authorization: Bearer <token>
```

#### Process UPI Payment

```http
POST /api/v1/payments/upi
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "userId": "user-uuid",
  "upiId": "user@paytm",
  "description": "UPI payment for order",
  "metadata": {
    "orderId": "order-uuid"
  }
}
```

#### Process Bank Transfer

```http
POST /api/v1/payments/bank-transfer
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "userId": "user-uuid",
  "bankAccountDetails": {
    "accountNumber": "**********",
    "ifscCode": "HDFC0001234",
    "accountHolderName": "John Doe",
    "bankName": "HDFC Bank"
  },
  "description": "Bank transfer payment",
  "metadata": {
    "orderId": "order-uuid"
  }
}
```

### 3. Settlement Management

#### Create Settlement

```http
POST /api/v1/payments/settlements
Authorization: Bearer <token>
Content-Type: application/json

{
  "tripId": "trip-uuid",
  "orderId": "order-uuid",
  "totalAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "distributions": [
    {
      "recipientId": "carrier-uuid",
      "recipientType": "Carrier",
      "amount": {
        "amount": 20000.0,
        "currency": "INR"
      },
      "description": "Carrier payment for trip completion"
    },
    {
      "recipientId": "broker-uuid",
      "recipientType": "Broker",
      "amount": {
        "amount": 5000.0,
        "currency": "INR"
      },
      "description": "Broker commission"
    }
  ],
  "settlementDate": "2024-01-16T00:00:00Z",
  "notes": "Settlement for completed trip Mumbai to Delhi"
}
```

**Response:**

```json
{
  "id": "settlement-uuid",
  "settlementNumber": "SET-2024-001234",
  "tripId": "trip-uuid",
  "orderId": "order-uuid",
  "status": "Created",
  "totalAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "distributions": [
    {
      "id": "distribution-uuid-1",
      "recipientId": "carrier-uuid",
      "recipientType": "Carrier",
      "amount": {
        "amount": 20000.0,
        "currency": "INR"
      },
      "status": "Pending",
      "description": "Carrier payment for trip completion"
    },
    {
      "id": "distribution-uuid-2",
      "recipientId": "broker-uuid",
      "recipientType": "Broker",
      "amount": {
        "amount": 5000.0,
        "currency": "INR"
      },
      "status": "Pending",
      "description": "Broker commission"
    }
  ],
  "createdAt": "2024-01-16T10:00:00Z"
}
```

#### Process Settlement

```http
POST /api/v1/payments/settlements/{settlementId}/process
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentGateway": "Razorpay",
  "processDate": "2024-01-16T12:00:00Z",
  "notes": "Processing settlement for completed trip"
}
```

#### Get Settlement Details

```http
GET /api/v1/payments/settlements/{settlementId}
Authorization: Bearer <token>
```

#### Get Settlements by Carrier

```http
GET /api/v1/payments/settlements/carrier/{carrierId}
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Filter from date
- `toDate`: Filter to date
- `status`: Filter by settlement status
- `page`: Page number
- `pageSize`: Items per page

### 4. Commission Management

#### Calculate Commission

```http
POST /api/v1/payments/commissions/calculate
Authorization: Bearer <token>
Content-Type: application/json

{
  "orderId": "order-uuid",
  "brokerId": "broker-uuid",
  "orderAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "commissionType": "Percentage|FixedAmount|Tiered",
  "commissionRate": 10.0,
  "minimumCommission": {
    "amount": 500.0,
    "currency": "INR"
  },
  "maximumCommission": {
    "amount": 5000.0,
    "currency": "INR"
  }
}
```

**Response:**

```json
{
  "id": "commission-uuid",
  "orderId": "order-uuid",
  "brokerId": "broker-uuid",
  "baseAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "commissionRate": 10.0,
  "calculatedAmount": {
    "amount": 2500.0,
    "currency": "INR"
  },
  "finalAmount": {
    "amount": 2500.0,
    "currency": "INR"
  },
  "status": "Calculated",
  "calculatedAt": "2024-01-16T10:00:00Z"
}
```

#### Process Commission Payment

```http
POST /api/v1/payments/commissions/{commissionId}/pay
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentMethodId": "pm-uuid",
  "paymentGateway": "Razorpay",
  "notes": "Commission payment for order completion"
}
```

### 5. Tax Configuration

#### Get Tax Configuration

```http
GET /api/v1/payments/tax/configuration
Authorization: Bearer <token>
```

**Response:**

```json
{
  "gstConfiguration": {
    "defaultRate": 18.0,
    "serviceCategories": [
      {
        "category": "Transportation",
        "hsnCode": "996511",
        "gstRate": 5.0,
        "description": "Goods transport by road"
      },
      {
        "category": "Logistics",
        "hsnCode": "996512",
        "gstRate": 18.0,
        "description": "Logistics and warehousing services"
      }
    ]
  },
  "tdsConfiguration": {
    "defaultRate": 1.0,
    "sections": [
      {
        "section": "194C",
        "entityType": "Contractor",
        "rate": 1.0,
        "threshold": 30000.0,
        "description": "Payment to contractors"
      },
      {
        "section": "194H",
        "entityType": "Commission",
        "rate": 5.0,
        "threshold": 15000.0,
        "description": "Commission or brokerage"
      }
    ]
  },
  "lastUpdated": "2024-01-01T00:00:00Z"
}
```

#### Update GST Configuration (Admin Only)

```http
PUT /api/v1/payments/tax/gst
Authorization: Bearer <token>
Content-Type: application/json

{
  "defaultRate": 18.0,
  "serviceCategories": [
    {
      "category": "Transportation",
      "hsnCode": "996511",
      "gstRate": 5.0,
      "description": "Goods transport by road"
    }
  ]
}
```

#### Update TDS Configuration (Admin Only)

```http
PUT /api/v1/payments/tax/tds
Authorization: Bearer <token>
Content-Type: application/json

{
  "defaultRate": 1.0,
  "sections": [
    {
      "section": "194C",
      "entityType": "Contractor",
      "rate": 1.0,
      "threshold": 30000.0,
      "description": "Payment to contractors"
    }
  ]
}
```

### 6. Payment Disputes

#### Create Payment Dispute

```http
POST /api/v1/payments/disputes
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentId": "payment-uuid",
  "orderId": "order-uuid",
  "disputeType": "PaymentNotReceived|IncorrectAmount|UnauthorizedCharge|ServiceNotProvided",
  "priority": "Low|Medium|High|Critical",
  "title": "Payment not received for completed trip",
  "description": "Trip was completed successfully but payment has not been received in carrier account",
  "disputeAmount": {
    "amount": 20000.0,
    "currency": "INR"
  },
  "supportingDocuments": [
    {
      "fileName": "trip_completion_proof.pdf",
      "fileUrl": "https://storage.tli.com/disputes/doc-uuid.pdf",
      "documentType": "ProofOfDelivery"
    }
  ]
}
```

**Response:**

```json
{
  "id": "dispute-uuid",
  "disputeNumber": "DIS-2024-001234",
  "paymentId": "payment-uuid",
  "orderId": "order-uuid",
  "disputeType": "PaymentNotReceived",
  "priority": "High",
  "status": "Open",
  "title": "Payment not received for completed trip",
  "description": "Trip was completed successfully but payment has not been received in carrier account",
  "disputeAmount": {
    "amount": 20000.0,
    "currency": "INR"
  },
  "createdBy": "carrier-uuid",
  "assignedTo": null,
  "createdAt": "2024-01-16T15:00:00Z",
  "expectedResolutionDate": "2024-01-23T15:00:00Z"
}
```

#### Update Dispute Status

```http
PUT /api/v1/payments/disputes/{disputeId}/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "InvestigationInProgress",
  "assignedTo": "admin-uuid",
  "comments": "Dispute assigned to finance team for investigation",
  "estimatedResolutionDate": "2024-01-20T00:00:00Z"
}
```

#### Add Dispute Comment

```http
POST /api/v1/payments/disputes/{disputeId}/comments
Authorization: Bearer <token>
Content-Type: application/json

{
  "comment": "We have verified the trip completion and are processing the payment",
  "isInternal": false,
  "attachments": [
    {
      "fileName": "payment_processing_proof.pdf",
      "fileUrl": "https://storage.tli.com/disputes/comment-doc-uuid.pdf"
    }
  ]
}
```

#### Resolve Dispute

```http
POST /api/v1/payments/disputes/{disputeId}/resolve
Authorization: Bearer <token>
Content-Type: application/json

{
  "resolution": "PaymentProcessed",
  "resolutionComments": "Payment has been processed and credited to carrier account",
  "compensationAmount": {
    "amount": 20000.0,
    "currency": "INR"
  },
  "resolutionDocuments": [
    {
      "fileName": "payment_confirmation.pdf",
      "fileUrl": "https://storage.tli.com/disputes/resolution-doc-uuid.pdf"
    }
  ]
}
```

### 7. Financial Reports

#### Get Payment Summary

```http
GET /api/v1/payments/reports/summary
Authorization: Bearer <token>
```

**Query Parameters:**

- `fromDate`: Start date for report
- `toDate`: End date for report
- `currency`: Filter by currency
- `paymentType`: Filter by payment type

**Response:**

```json
{
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T00:00:00Z"
  },
  "totalPayments": {
    "count": 150,
    "amount": {
      "amount": 3750000.0,
      "currency": "INR"
    }
  },
  "paymentsByType": {
    "EscrowFunding": {
      "count": 75,
      "amount": {
        "amount": 1875000.0,
        "currency": "INR"
      }
    },
    "Settlement": {
      "count": 60,
      "amount": {
        "amount": 1500000.0,
        "currency": "INR"
      }
    },
    "Commission": {
      "count": 15,
      "amount": {
        "amount": 375000.0,
        "currency": "INR"
      }
    }
  },
  "paymentsByStatus": {
    "Completed": 140,
    "Pending": 8,
    "Failed": 2
  },
  "averagePaymentAmount": {
    "amount": 25000.0,
    "currency": "INR"
  }
}
```

#### Get Escrow Report

```http
GET /api/v1/payments/reports/escrow
Authorization: Bearer <token>
```

**Response:**

```json
{
  "totalEscrowAccounts": 75,
  "totalEscrowValue": {
    "amount": 1875000.0,
    "currency": "INR"
  },
  "escrowByStatus": {
    "Created": 5,
    "Funded": 10,
    "PartiallyReleased": 25,
    "FullyReleased": 30,
    "Refunded": 5
  },
  "averageEscrowAmount": {
    "amount": 25000.0,
    "currency": "INR"
  },
  "averageReleaseTime": "2.5 days",
  "topEscrowAccounts": [
    {
      "escrowAccountNumber": "ESC-2024-001234",
      "orderId": "order-uuid",
      "amount": {
        "amount": 50000.0,
        "currency": "INR"
      },
      "status": "PartiallyReleased"
    }
  ]
}
```

#### Get Commission Report

```http
GET /api/v1/payments/reports/commissions
Authorization: Bearer <token>
```

**Query Parameters:**

- `brokerId`: Filter by broker
- `fromDate`: Start date
- `toDate`: End date

### 8. Reconciliation

#### Start Reconciliation Process

```http
POST /api/v1/payments/reconciliation/start
Authorization: Bearer <token>
Content-Type: application/json

{
  "periodStart": "2024-01-01T00:00:00Z",
  "periodEnd": "2024-01-31T23:59:59Z",
  "paymentGateway": "Razorpay",
  "currency": "INR",
  "autoProcess": true,
  "toleranceAmount": {
    "amount": 1.0,
    "currency": "INR"
  },
  "toleranceHours": 24
}
```

**Response:**

```json
{
  "id": "reconciliation-uuid",
  "status": "InProgress",
  "periodStart": "2024-01-01T00:00:00Z",
  "periodEnd": "2024-01-31T23:59:59Z",
  "paymentGateway": "Razorpay",
  "totalTransactions": 150,
  "processedTransactions": 0,
  "matchedTransactions": 0,
  "unmatchedTransactions": 0,
  "discrepancies": 0,
  "startedAt": "2024-02-01T10:00:00Z",
  "estimatedCompletionTime": "2024-02-01T10:30:00Z"
}
```

#### Get Reconciliation Status

```http
GET /api/v1/payments/reconciliation/{reconciliationId}
Authorization: Bearer <token>
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/payments/hub`

**Events:**

- `PaymentProcessed` - Payment completed successfully
- `PaymentFailed` - Payment processing failed
- `EscrowFunded` - Escrow account funded
- `EscrowReleased` - Escrow funds released
- `SettlementProcessed` - Settlement completed
- `DisputeCreated` - New payment dispute created
- `DisputeResolved` - Payment dispute resolved
- `CommissionCalculated` - Commission calculated
- `ReconciliationCompleted` - Reconciliation process completed

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/payments/hub')
  .build()

connection.on('PaymentProcessed', (data) => {
  console.log('Payment processed:', data)
  // Update payment status in UI
})

connection.on('EscrowReleased', (data) => {
  console.log('Escrow funds released:', data)
  // Update escrow status and notify participants
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                        | Description                             |
| ---- | ------------------------------ | --------------------------------------- |
| 400  | `INSUFFICIENT_ESCROW_FUNDS`    | Not enough funds in escrow account      |
| 400  | `INVALID_PAYMENT_METHOD`       | Invalid payment method provided         |
| 400  | `PAYMENT_ALREADY_PROCESSED`    | Payment has already been processed      |
| 400  | `ESCROW_NOT_FUNDED`            | Escrow account is not funded            |
| 401  | `UNAUTHORIZED`                 | Authentication required                 |
| 403  | `INSUFFICIENT_PERMISSIONS`     | Insufficient permissions                |
| 404  | `ESCROW_NOT_FOUND`             | Escrow account not found                |
| 404  | `PAYMENT_NOT_FOUND`            | Payment not found                       |
| 404  | `SETTLEMENT_NOT_FOUND`         | Settlement not found                    |
| 409  | `ESCROW_ALREADY_EXISTS`        | Escrow account already exists for order |
| 409  | `SETTLEMENT_ALREADY_PROCESSED` | Settlement already processed            |
| 422  | `PAYMENT_GATEWAY_ERROR`        | Payment gateway processing error        |
| 422  | `VALIDATION_FAILED`            | Input validation failed                 |
| 429  | `RATE_LIMIT_EXCEEDED`          | Too many requests                       |

### Error Response Format

```json
{
  "error": {
    "code": "INSUFFICIENT_ESCROW_FUNDS",
    "message": "Insufficient funds in escrow account",
    "details": "Available: ₹15,000, Requested: ₹20,000",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid"
  }
}
```
