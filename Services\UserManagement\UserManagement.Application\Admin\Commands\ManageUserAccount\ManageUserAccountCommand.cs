using System;
using MediatR;

namespace UserManagement.Application.Admin.Commands.ManageUserAccount
{
    public class ManageUserAccountCommand : IRequest<ManageUserAccountResponse>
    {
        public Guid UserId { get; set; }
        public UserAccountAction Action { get; set; }
        public string Reason { get; set; }
        public Guid ActionBy { get; set; }
        public string Notes { get; set; }
        
        // For password reset
        public string NewPassword { get; set; }
        public bool RequirePasswordChangeOnNextLogin { get; set; } = true;
        
        // For subscription management
        public string SubscriptionPlan { get; set; }
        public DateTime? SubscriptionExpiryDate { get; set; }
        public bool? AutoRenewalEnabled { get; set; }
        
        // For plan information update
        public PlanInformation PlanInfo { get; set; }
    }

    public enum UserAccountAction
    {
        Activate,
        Deactivate,
        Suspend,
        Unsuspend,
        ResetPassword,
        UpdatePlanInfo,
        EnableAutoRenewal,
        DisableAutoRenewal,
        ExtendSubscription,
        CancelSubscription,
        SendKycReminder,
        ResendKycUploadPrompt,
        ForceKycResubmission
    }

    public class PlanInformation
    {
        public string PlanName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public decimal MonthlyFee { get; set; }
        public int FeatureLimits { get; set; }
        public string Features { get; set; }
        public bool IsActive { get; set; }
    }

    public class ManageUserAccountResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public UserAccountAction ActionPerformed { get; set; }
        public DateTime ActionPerformedAt { get; set; }
        public UserAccountStatus NewStatus { get; set; }
        public string ErrorMessage { get; set; }
        public AccountActionResult Result { get; set; }
    }

    public class UserAccountStatus
    {
        public bool IsActive { get; set; }
        public bool IsSuspended { get; set; }
        public string Status { get; set; }
        public DateTime? LastPasswordChange { get; set; }
        public bool RequirePasswordChange { get; set; }
        public SubscriptionStatus Subscription { get; set; }
        public KycStatus KycStatus { get; set; }
    }

    public class SubscriptionStatus
    {
        public string PlanName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsActive { get; set; }
        public bool AutoRenewalEnabled { get; set; }
        public decimal? MonthlyFee { get; set; }
        public int? FeatureLimits { get; set; }
        public int DaysUntilExpiry { get; set; }
        public bool IsExpiringSoon { get; set; }
    }

    public class KycStatus
    {
        public string Status { get; set; }
        public DateTime? SubmittedAt { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public DateTime? LastReminderSent { get; set; }
        public int ReminderCount { get; set; }
        public bool RequiresResubmission { get; set; }
    }

    public class AccountActionResult
    {
        public string ActionType { get; set; }
        public bool WasSuccessful { get; set; }
        public string Details { get; set; }
        public DateTime Timestamp { get; set; }
        public string PerformedBy { get; set; }
        public string PreviousState { get; set; }
        public string NewState { get; set; }
        public bool NotificationSent { get; set; }
        public string NotificationMethod { get; set; }
    }
}
