using Shared.Domain.Common;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Domain.Events;

// Payment Milestone Events
public record PaymentMilestoneCreatedEvent(Guid MilestoneId, Guid TripId, Guid OrderId, string MilestoneType, Money Amount, DateTime DueDate) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneCompletedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime CompletedAt, Guid CompletedBy) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneApprovedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime ApprovedAt, Guid ApprovedBy, string? ApprovalNotes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneRejectedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime RejectedAt, Guid RejectedBy, string RejectionReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneCancelledEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime CancelledAt, Guid CancelledBy, string CancellationReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneSkippedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime SkippedAt, Guid SkippedBy, string SkipReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Payment Milestone Status Events
public record PaymentMilestoneStatusChangedEvent(Guid MilestoneId, string PreviousStatus, string NewStatus, DateTime ChangedAt, Guid ChangedBy, string? ChangeReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneAmountUpdatedEvent(Guid MilestoneId, Money PreviousAmount, Money NewAmount, DateTime UpdatedAt, Guid UpdatedBy, string UpdateReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneDueDateUpdatedEvent(Guid MilestoneId, DateTime PreviousDueDate, DateTime NewDueDate, DateTime UpdatedAt, Guid UpdatedBy, string UpdateReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

// Payment Milestone Processing Events
public record PaymentMilestoneProcessingStartedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime ProcessingStartedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestoneProcessingFailedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime FailedAt, string FailureReason) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record PaymentMilestonePaymentReleasedEvent(Guid MilestoneId, Guid TripId, string MilestoneType, Money Amount, DateTime ReleasedAt, string PaymentReference) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
