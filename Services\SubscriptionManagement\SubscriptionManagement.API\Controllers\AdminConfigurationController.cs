using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.API.Controllers
{
    [Authorize(Roles = "Admin")]
    [Route("api/admin/configuration")]
    public class AdminConfigurationController : BaseController
    {
        private readonly INotificationTemplateService _templateService;

        public AdminConfigurationController(INotificationTemplateService templateService)
        {
            _templateService = templateService;
        }

        /// <summary>
        /// Get system configuration for admin UI
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(AdminConfigurationDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetConfiguration()
        {
            try
            {
                var config = new AdminConfigurationDto
                {
                    NotificationChannels = new List<string> { "Email", "SMS", "WhatsApp", "InApp" },
                    NotificationTypes = Enum.GetValues<NotificationType>()
                        .Select(t => new NotificationTypeDto
                        {
                            Value = t,
                            Name = t.ToString(),
                            Description = GetNotificationTypeDescription(t)
                        }).ToList(),
                    PaymentProofStatuses = Enum.GetValues<PaymentProofStatus>()
                        .Select(s => new PaymentProofStatusDto
                        {
                            Value = s,
                            Name = s.ToString(),
                            Description = GetPaymentProofStatusDescription(s)
                        }).ToList(),
                    SubscriptionStatuses = Enum.GetValues<SubscriptionStatus>()
                        .Select(s => new SubscriptionStatusDto
                        {
                            Value = s,
                            Name = s.ToString(),
                            Description = GetSubscriptionStatusDescription(s)
                        }).ToList(),
                    UserTypes = Enum.GetValues<UserType>()
                        .Select(u => new UserTypeDto
                        {
                            Value = u,
                            Name = u.ToString(),
                            Description = GetUserTypeDescription(u)
                        }).ToList(),
                    PlanTypes = Enum.GetValues<PlanType>()
                        .Select(p => new PlanTypeDto
                        {
                            Value = p,
                            Name = p.ToString(),
                            Description = GetPlanTypeDescription(p)
                        }).ToList(),
                    RateLimits = new RateLimitConfigDto
                    {
                        ManualNotifications = 50,
                        BulkAlerts = 10,
                        PaymentVerification = 200,
                        PaymentUpload = 20,
                        WindowMinutes = 15
                    },
                    FileUploadLimits = new FileUploadLimitsDto
                    {
                        MaxFileSizeMB = 10,
                        AllowedFileTypes = new List<string> { "jpg", "jpeg", "png", "gif", "webp", "pdf" }
                    }
                };

                return Ok(config);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get notification template variables for a specific type and channel
        /// </summary>
        [HttpGet("template-variables")]
        [ProducesResponseType(typeof(TemplateVariablesDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetTemplateVariables(
            [FromQuery] NotificationType type,
            [FromQuery] string channel)
        {
            try
            {
                var template = await _templateService.GetTemplateAsync(type, channel);
                
                var variables = new TemplateVariablesDto
                {
                    Type = type,
                    Channel = channel,
                    Variables = template?.Variables ?? new Dictionary<string, string>(),
                    SampleValues = GetSampleVariableValues(type)
                };

                return Ok(variables);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get admin action history
        /// </summary>
        [HttpGet("action-history")]
        [ProducesResponseType(typeof(AdminActionHistoryDto), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetActionHistory(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // TODO: Implement admin action history tracking
                var history = new AdminActionHistoryDto
                {
                    Items = new List<AdminActionDto>(),
                    TotalCount = 0,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = 0
                };

                return Ok(history);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        private string GetNotificationTypeDescription(NotificationType type)
        {
            return type switch
            {
                NotificationType.SubscriptionReminder => "Remind users about upcoming subscription renewal",
                NotificationType.PaymentThankYou => "Thank users for successful payment",
                NotificationType.ExpiryWarning => "Warn users about subscription expiry",
                NotificationType.PaymentFailed => "Notify users about failed payment",
                NotificationType.SubscriptionActivated => "Confirm subscription activation",
                NotificationType.SubscriptionCancelled => "Confirm subscription cancellation",
                NotificationType.PaymentProofReceived => "Acknowledge receipt of payment proof",
                NotificationType.PaymentProofVerified => "Confirm payment proof verification",
                NotificationType.PaymentProofRejected => "Notify payment proof rejection",
                NotificationType.Custom => "Custom notification message",
                _ => type.ToString()
            };
        }

        private string GetPaymentProofStatusDescription(PaymentProofStatus status)
        {
            return status switch
            {
                PaymentProofStatus.Pending => "Awaiting review",
                PaymentProofStatus.UnderReview => "Currently being reviewed",
                PaymentProofStatus.Verified => "Verified and approved",
                PaymentProofStatus.Rejected => "Rejected with reason",
                PaymentProofStatus.RequiresAdditionalInfo => "Needs more information",
                _ => status.ToString()
            };
        }

        private string GetSubscriptionStatusDescription(SubscriptionStatus status)
        {
            return status switch
            {
                SubscriptionStatus.Pending => "Awaiting activation",
                SubscriptionStatus.Active => "Currently active",
                SubscriptionStatus.Suspended => "Temporarily suspended",
                SubscriptionStatus.Expired => "Expired",
                SubscriptionStatus.Cancelled => "Cancelled by user",
                _ => status.ToString()
            };
        }

        private string GetUserTypeDescription(UserType type)
        {
            return type switch
            {
                UserType.Transporter => "Transportation service provider",
                UserType.Broker => "Logistics broker",
                UserType.Carrier => "Freight carrier",
                _ => type.ToString()
            };
        }

        private string GetPlanTypeDescription(PlanType type)
        {
            return type switch
            {
                PlanType.Basic => "Basic subscription plan",
                PlanType.Standard => "Standard subscription plan",
                PlanType.Premium => "Premium subscription plan",
                PlanType.Enterprise => "Enterprise subscription plan",
                _ => type.ToString()
            };
        }

        private Dictionary<string, string> GetSampleVariableValues(NotificationType type)
        {
            return new Dictionary<string, string>
            {
                ["UserName"] = "John Doe",
                ["SubscriptionId"] = "SUB-12345",
                ["PlanName"] = "Premium Plan",
                ["Amount"] = "999.00",
                ["Currency"] = "INR",
                ["PaymentDate"] = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                ["NextBillingDate"] = DateTime.UtcNow.AddDays(30).ToString("yyyy-MM-dd"),
                ["CompanyName"] = "TLI Logistics",
                ["SupportEmail"] = "<EMAIL>"
            };
        }
    }

    // Configuration DTOs
    public class AdminConfigurationDto
    {
        public List<string> NotificationChannels { get; set; } = new();
        public List<NotificationTypeDto> NotificationTypes { get; set; } = new();
        public List<PaymentProofStatusDto> PaymentProofStatuses { get; set; } = new();
        public List<SubscriptionStatusDto> SubscriptionStatuses { get; set; } = new();
        public List<UserTypeDto> UserTypes { get; set; } = new();
        public List<PlanTypeDto> PlanTypes { get; set; } = new();
        public RateLimitConfigDto RateLimits { get; set; } = new();
        public FileUploadLimitsDto FileUploadLimits { get; set; } = new();
    }

    public class NotificationTypeDto
    {
        public NotificationType Value { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class PaymentProofStatusDto
    {
        public PaymentProofStatus Value { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class SubscriptionStatusDto
    {
        public SubscriptionStatus Value { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class UserTypeDto
    {
        public UserType Value { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class PlanTypeDto
    {
        public PlanType Value { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class RateLimitConfigDto
    {
        public int ManualNotifications { get; set; }
        public int BulkAlerts { get; set; }
        public int PaymentVerification { get; set; }
        public int PaymentUpload { get; set; }
        public int WindowMinutes { get; set; }
    }

    public class FileUploadLimitsDto
    {
        public int MaxFileSizeMB { get; set; }
        public List<string> AllowedFileTypes { get; set; } = new();
    }

    public class TemplateVariablesDto
    {
        public NotificationType Type { get; set; }
        public string Channel { get; set; } = string.Empty;
        public Dictionary<string, string> Variables { get; set; } = new();
        public Dictionary<string, string> SampleValues { get; set; } = new();
    }

    public class AdminActionHistoryDto
    {
        public List<AdminActionDto> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class AdminActionDto
    {
        public Guid Id { get; set; }
        public Guid AdminUserId { get; set; }
        public string Action { get; set; } = string.Empty;
        public string Resource { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
