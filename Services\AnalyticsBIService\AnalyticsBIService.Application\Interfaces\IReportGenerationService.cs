using AnalyticsBIService.Domain.Enums;
using AnalyticsBIService.Application.DTOs;

namespace AnalyticsBIService.Application.Interfaces;

/// <summary>
/// Report generation service interface
/// </summary>
public interface IReportGenerationService
{
    /// <summary>
    /// Generate report based on template
    /// </summary>
    Task<string> GenerateReportAsync(
        Guid reportId,
        ExportFormat format,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate scheduled report
    /// </summary>
    Task<string> GenerateScheduledReportAsync(
        Guid reportId,
        Dictionary<string, object> parameters,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate report template
    /// </summary>
    Task<bool> ValidateReportTemplateAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    // Additional methods required by OrderTripExportService
    Task<string> GenerateOrderReportAsync(List<OrderDataDto> orders, Guid templateId, CancellationToken cancellationToken = default);
    Task<string> GenerateTripReportAsync(List<TripDataDto> trips, Guid templateId, CancellationToken cancellationToken = default);
    Task<string> GenerateTimelineReportAsync(List<OrderTimelineDto> timeline, Guid templateId, CancellationToken cancellationToken = default);
    Task<string> GeneratePerformanceReportAsync(List<PerformanceDataDto> performance, Guid templateId, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateCSVAsync(object data, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateExcelAsync(object data, CancellationToken cancellationToken = default);
    Task<byte[]> GeneratePDFAsync(object data, CancellationToken cancellationToken = default);
    Task<byte[]> GenerateJSONAsync(object data, CancellationToken cancellationToken = default);

    // Additional method required by TransportCompanyReportService
    Task<string> GeneratePreviewContentAsync(ReportTemplateDto template, object sampleData, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate report from template
    /// </summary>
    Task<string> GenerateReportFromTemplateAsync(Guid templateId, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate report content
    /// </summary>
    Task<string> GenerateReportContentAsync(ReportTemplateDto template, Dictionary<string, object> data, CancellationToken cancellationToken = default);
}
