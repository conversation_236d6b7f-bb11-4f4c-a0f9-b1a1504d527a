using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Exceptions;
using OrderManagement.Domain.Enums;
using OrderManagement.Application.Services;

namespace OrderManagement.Application.Commands.ForceOrderClosure;

public class ForceOrderClosureCommandHandler : IRequestHandler<ForceOrderClosureCommand>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAdminAuditService _adminAuditService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<ForceOrderClosureCommandHandler> _logger;

    public ForceOrderClosureCommandHandler(
        IOrderRepository orderRepository,
        IUnitOfWork unitOfWork,
        IAdminAuditService adminAuditService,
        INotificationService notificationService,
        ILogger<ForceOrderClosureCommandHandler> logger)
    {
        _orderRepository = orderRepository;
        _unitOfWork = unitOfWork;
        _adminAuditService = adminAuditService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task Handle(ForceOrderClosureCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing force closure for order {OrderId} by admin {AdminId}",
                request.OrderId, request.AdminUserId);

            // Get the order
            var order = await _orderRepository.GetByIdAsync(request.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for force closure", request.OrderId);
                throw new OrderManagementException("Order not found");
            }

            // Validate that order can be force closed
            if (order.Status == OrderStatus.Completed)
            {
                _logger.LogWarning("Order {OrderId} is already completed", request.OrderId);
                throw new OrderManagementException("Order is already completed");
            }

            if (order.Status == OrderStatus.Cancelled)
            {
                _logger.LogWarning("Order {OrderId} is already cancelled", request.OrderId);
                throw new OrderManagementException("Order is already cancelled");
            }

            // Store previous status for audit
            var previousStatus = order.Status;

            // Force complete the order
            order.ForceComplete(request.Reason, request.AdminUserId);

            // Add timeline event for force closure
            order.AddTimelineEvent(
                OrderTimelineEventType.AdminAction,
                $"Order force closed by admin: {request.Reason}",
                actorId: request.AdminUserId,
                actorName: request.AdminUserName,
                actorRole: "Admin",
                previousStatus: previousStatus,
                newStatus: OrderStatus.Completed,
                notes: request.AdminNotes,
                ipAddress: request.IpAddress,
                userAgent: request.UserAgent,
                metadata: new Dictionary<string, object>
                {
                    ["ActionType"] = "ForceOrderClosure",
                    ["Reason"] = request.Reason,
                    ["NotifyParties"] = request.NotifyParties
                });

            // Create audit trail entry
            await _adminAuditService.LogAdminActionAsync(new AdminAuditEntry
            {
                OrderId = request.OrderId,
                Action = "ForceOrderClosure",
                PreviousValue = previousStatus.ToString(),
                NewValue = OrderStatus.Completed.ToString(),
                AdminUserId = request.AdminUserId,
                AdminUserName = request.AdminUserName,
                Reason = request.Reason,
                AdminNotes = request.AdminNotes,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent,
                Severity = AuditSeverity.High,
                Timestamp = DateTime.UtcNow
            }, cancellationToken);

            // Save changes
            await _orderRepository.UpdateAsync(order, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Send notifications if requested
            if (request.NotifyParties)
            {
                await SendForceClosureNotificationsAsync(order, request, cancellationToken);
            }

            _logger.LogInformation("Order {OrderId} force closed successfully by admin {AdminId}",
                request.OrderId, request.AdminUserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error force closing order {OrderId}", request.OrderId);
            throw;
        }
    }

    private async Task SendForceClosureNotificationsAsync(
        Domain.Entities.Order order,
        ForceOrderClosureCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            var notifications = new List<NotificationRequest>();

            // Notify transport company
            notifications.Add(new NotificationRequest
            {
                RecipientId = order.TransportCompanyId,
                RecipientType = "TransportCompany",
                Subject = $"Order {order.OrderNumber} Force Closed",
                Message = $"Order {order.OrderNumber} has been force closed by admin. Reason: {request.Reason}",
                NotificationType = "OrderForceClosureNotification",
                Priority = NotificationPriority.High,
                Metadata = new Dictionary<string, object>
                {
                    ["OrderId"] = order.Id,
                    ["OrderNumber"] = order.OrderNumber,
                    ["Reason"] = request.Reason,
                    ["AdminAction"] = true
                }
            });

            // Notify broker if exists
            if (order.BrokerId != Guid.Empty)
            {
                notifications.Add(new NotificationRequest
                {
                    RecipientId = order.BrokerId,
                    RecipientType = "Broker",
                    Subject = $"Order {order.OrderNumber} Force Closed",
                    Message = $"Order {order.OrderNumber} has been force closed by admin. Reason: {request.Reason}",
                    NotificationType = "OrderForceClosureNotification",
                    Priority = NotificationPriority.High,
                    Metadata = new Dictionary<string, object>
                    {
                        ["OrderId"] = order.Id,
                        ["OrderNumber"] = order.OrderNumber,
                        ["Reason"] = request.Reason,
                        ["AdminAction"] = true
                    }
                });
            }

            // Notify carrier if exists
            if (order.CarrierId.HasValue)
            {
                notifications.Add(new NotificationRequest
                {
                    RecipientId = order.CarrierId.Value,
                    RecipientType = "Carrier",
                    Subject = $"Order {order.OrderNumber} Force Closed",
                    Message = $"Order {order.OrderNumber} has been force closed by admin. Reason: {request.Reason}",
                    NotificationType = "OrderForceClosureNotification",
                    Priority = NotificationPriority.High,
                    Metadata = new Dictionary<string, object>
                    {
                        ["OrderId"] = order.Id,
                        ["OrderNumber"] = order.OrderNumber,
                        ["Reason"] = request.Reason,
                        ["AdminAction"] = true
                    }
                });
            }

            // Send all notifications
            foreach (var notification in notifications)
            {
                await _notificationService.SendNotificationAsync(notification, cancellationToken);
            }

            _logger.LogInformation("Force closure notifications sent for order {OrderId}", order.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending force closure notifications for order {OrderId}", order.Id);
            // Don't throw - notification failure shouldn't fail the force closure
        }
    }
}

// Supporting classes
public class AdminAuditEntry
{
    public Guid? OrderId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? PreviousValue { get; set; }
    public string? NewValue { get; set; }
    public Guid AdminUserId { get; set; }
    public string? AdminUserName { get; set; }
    public string? Reason { get; set; }
    public string? AdminNotes { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public AuditSeverity Severity { get; set; }
    public DateTime Timestamp { get; set; }
}

public class NotificationRequest
{
    public Guid RecipientId { get; set; }
    public string RecipientType { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string NotificationType { get; set; } = string.Empty;
    public NotificationPriority Priority { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

public interface IAdminAuditService
{
    Task LogAdminActionAsync(AdminAuditEntry entry, CancellationToken cancellationToken = default);
}

public interface INotificationService
{
    Task SendNotificationAsync(NotificationRequest request, CancellationToken cancellationToken = default);
}
