using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Resource usage information
/// </summary>
public class ResourceUsage : ValueObject
{
    public string ResourceType { get; init; }
    public double UsedAmount { get; init; }
    public double TotalAmount { get; init; }
    public double UtilizationPercentage { get; init; }
    public string Unit { get; init; }
    public DateTime MeasuredAt { get; init; }
    public Dictionary<string, object> Metadata { get; init; }

    public ResourceUsage(
        string resourceType,
        double usedAmount,
        double totalAmount,
        string unit,
        DateTime measuredAt,
        Dictionary<string, object>? metadata = null)
    {
        ResourceType = resourceType;
        UsedAmount = usedAmount;
        TotalAmount = totalAmount;
        UtilizationPercentage = totalAmount > 0 ? (usedAmount / totalAmount) * 100 : 0;
        Unit = unit;
        MeasuredAt = measuredAt;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ResourceType;
        yield return UsedAmount;
        yield return TotalAmount;
        yield return Unit;
        yield return MeasuredAt;
        
        foreach (var meta in Metadata.OrderBy(x => x.Key))
        {
            yield return meta.Key;
            yield return meta.Value?.ToString() ?? string.Empty;
        }
    }
}
