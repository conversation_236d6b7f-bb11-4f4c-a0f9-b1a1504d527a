using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Domain.Interfaces
{
    public interface INotificationTemplateRepository
    {
        Task<NotificationTemplate?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
        Task<NotificationTemplate?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
        Task<List<NotificationTemplate>> GetByTypeAsync(NotificationType type, CancellationToken cancellationToken = default);
        Task<List<NotificationTemplate>> GetByChannelAsync(string channel, CancellationToken cancellationToken = default);
        Task<List<NotificationTemplate>> GetByLanguageAsync(string language, CancellationToken cancellationToken = default);
        Task<List<NotificationTemplate>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default);
        
        Task<NotificationTemplate?> GetTemplateAsync(
            NotificationType type, 
            string channel, 
            string language = "en", 
            CancellationToken cancellationToken = default);

        Task<List<NotificationTemplate>> GetTemplatesAsync(
            NotificationType? type = null,
            string? channel = null,
            string? language = null,
            bool? isActive = null,
            CancellationToken cancellationToken = default);

        Task<(List<NotificationTemplate> Items, int TotalCount)> GetPagedAsync(
            int pageNumber,
            int pageSize,
            NotificationType? type = null,
            string? channel = null,
            string? language = null,
            bool? isActive = null,
            CancellationToken cancellationToken = default);

        Task<NotificationTemplate> AddAsync(NotificationTemplate template, CancellationToken cancellationToken = default);
        Task UpdateAsync(NotificationTemplate template, CancellationToken cancellationToken = default);
        Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
        
        Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default);
        Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
        Task<int> GetCountAsync(CancellationToken cancellationToken = default);
        Task<int> GetCountByTypeAsync(NotificationType type, CancellationToken cancellationToken = default);
    }
}
