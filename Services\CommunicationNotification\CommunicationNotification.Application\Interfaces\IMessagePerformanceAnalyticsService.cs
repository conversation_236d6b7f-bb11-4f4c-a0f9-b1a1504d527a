using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;

namespace CommunicationNotification.Application.Interfaces;

/// <summary>
/// Service interface for advanced message performance analytics
/// </summary>
public interface IMessagePerformanceAnalyticsService
{
    /// <summary>
    /// Generate comprehensive performance report
    /// </summary>
    Task<MessagePerformanceReport> GeneratePerformanceReportAsync(
        string name,
        string description,
        ReportType type,
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        ReportConfiguration? configuration = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance report by ID
    /// </summary>
    Task<MessagePerformanceReport?> GetPerformanceReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all performance reports
    /// </summary>
    Task<List<MessagePerformanceReport>> GetAllPerformanceReportsAsync(
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete performance report
    /// </summary>
    Task DeletePerformanceReportAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get advanced performance metrics
    /// </summary>
    Task<PerformanceMetrics> GetAdvancedPerformanceMetricsAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversion analytics
    /// </summary>
    Task<ConversionAnalytics> GetConversionAnalyticsAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get ROI analysis
    /// </summary>
    Task<ROIAnalysis> GetROIAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get segment performance analysis
    /// </summary>
    Task<List<SegmentPerformance>> GetSegmentPerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? segments = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cohort analysis
    /// </summary>
    Task<List<CohortAnalysis>> GetCohortAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        string cohortType = "monthly",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get funnel analysis
    /// </summary>
    Task<List<FunnelAnalysis>> GetFunnelAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? funnelNames = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get attribution analysis
    /// </summary>
    Task<AttributionAnalysis> GetAttributionAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance benchmarks
    /// </summary>
    Task<List<Benchmark>> GetPerformanceBenchmarksAsync(
        DateTime startDate,
        DateTime endDate,
        BenchmarkType benchmarkType = BenchmarkType.Industry,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate insights from analytics data
    /// </summary>
    Task<List<Insight>> GenerateInsightsAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate recommendations for performance improvement
    /// </summary>
    Task<List<Recommendation>> GenerateRecommendationsAsync(
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for conversion tracking and analysis
/// </summary>
public interface IConversionTrackingService
{
    /// <summary>
    /// Track conversion event
    /// </summary>
    Task TrackConversionAsync(
        Guid messageId,
        Guid userId,
        string conversionType,
        decimal conversionValue,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversion funnel data
    /// </summary>
    Task<ConversionFunnel> GetConversionFunnelAsync(
        string funnelName,
        DateTime startDate,
        DateTime endDate,
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversion paths
    /// </summary>
    Task<List<ConversionPath>> GetConversionPathsAsync(
        DateTime startDate,
        DateTime endDate,
        int maxPathLength = 5,
        int topPaths = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate attribution for conversions
    /// </summary>
    Task<AttributionAnalysis> CalculateAttributionAsync(
        DateTime startDate,
        DateTime endDate,
        List<string> touchpoints,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get conversion rate trends
    /// </summary>
    Task<List<TrendDataPoint>> GetConversionTrendsAsync(
        DateTime startDate,
        DateTime endDate,
        string granularity = "daily",
        List<ReportFilter>? filters = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for ROI and financial analytics
/// </summary>
public interface IROIAnalyticsService
{
    /// <summary>
    /// Calculate ROI for campaigns
    /// </summary>
    Task<ROIAnalysis> CalculateROIAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? campaignIds = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get customer lifetime value analysis
    /// </summary>
    Task<CustomerLifetimeValueAnalysis> GetCustomerLifetimeValueAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? segments = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate customer acquisition cost
    /// </summary>
    Task<CustomerAcquisitionCostAnalysis> GetCustomerAcquisitionCostAsync(
        DateTime startDate,
        DateTime endDate,
        List<NotificationChannel>? channels = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get revenue attribution by channel
    /// </summary>
    Task<Dictionary<NotificationChannel, decimal>> GetRevenueAttributionAsync(
        DateTime startDate,
        DateTime endDate,
        AttributionModel model = AttributionModel.LastTouch,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate payback period
    /// </summary>
    Task<PaybackAnalysis> GetPaybackAnalysisAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? campaignIds = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service interface for advanced segmentation analytics
/// </summary>
public interface ISegmentationAnalyticsService
{
    /// <summary>
    /// Analyze segment performance
    /// </summary>
    Task<List<SegmentPerformance>> AnalyzeSegmentPerformanceAsync(
        DateTime startDate,
        DateTime endDate,
        List<string>? segmentIds = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get segment comparison analysis
    /// </summary>
    Task<SegmentComparisonAnalysis> GetSegmentComparisonAsync(
        DateTime startDate,
        DateTime endDate,
        List<string> segmentIds,
        List<string> metrics,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Identify high-value segments
    /// </summary>
    Task<List<HighValueSegment>> IdentifyHighValueSegmentsAsync(
        DateTime startDate,
        DateTime endDate,
        decimal valueThreshold,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get segment migration analysis
    /// </summary>
    Task<SegmentMigrationAnalysis> GetSegmentMigrationAsync(
        DateTime startDate,
        DateTime endDate,
        List<string> fromSegments,
        List<string> toSegments,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate dynamic segments based on behavior
    /// </summary>
    Task<List<DynamicSegment>> GenerateDynamicSegmentsAsync(
        DateTime startDate,
        DateTime endDate,
        SegmentationCriteria criteria,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for message performance reports
/// </summary>
public interface IMessagePerformanceReportRepository
{
    /// <summary>
    /// Add performance report
    /// </summary>
    Task<MessagePerformanceReport> AddAsync(
        MessagePerformanceReport report,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update performance report
    /// </summary>
    Task<MessagePerformanceReport> UpdateAsync(
        MessagePerformanceReport report,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance report by ID
    /// </summary>
    Task<MessagePerformanceReport?> GetByIdAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all performance reports
    /// </summary>
    Task<List<MessagePerformanceReport>> GetAllAsync(
        Guid? userId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get reports by status
    /// </summary>
    Task<List<MessagePerformanceReport>> GetByStatusAsync(
        ReportStatus status,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete performance report
    /// </summary>
    Task DeleteAsync(
        Guid reportId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get expired reports
    /// </summary>
    Task<List<MessagePerformanceReport>> GetExpiredReportsAsync(
        DateTime cutoffDate,
        CancellationToken cancellationToken = default);
}

// Supporting DTOs and classes
public class TrendDataPoint
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CustomerLifetimeValueAnalysis
{
    public decimal AverageLifetimeValue { get; set; }
    public TimeSpan AverageLifespan { get; set; }
    public Dictionary<string, decimal> LifetimeValueBySegment { get; set; } = new();
    public List<TrendDataPoint> LifetimeValueTrend { get; set; } = new();
}

public class CustomerAcquisitionCostAnalysis
{
    public decimal AverageAcquisitionCost { get; set; }
    public Dictionary<NotificationChannel, decimal> CostByChannel { get; set; } = new();
    public Dictionary<string, decimal> CostBySegment { get; set; } = new();
    public List<TrendDataPoint> AcquisitionCostTrend { get; set; } = new();
}

public class PaybackAnalysis
{
    public TimeSpan AveragePaybackPeriod { get; set; }
    public Dictionary<string, TimeSpan> PaybackBySegment { get; set; } = new();
    public Dictionary<NotificationChannel, TimeSpan> PaybackByChannel { get; set; } = new();
    public decimal PaybackRate { get; set; }
}

public class SegmentComparisonAnalysis
{
    public Dictionary<string, Dictionary<string, decimal>> SegmentMetrics { get; set; } = new();
    public Dictionary<string, decimal> SegmentRankings { get; set; } = new();
    public List<SegmentInsight> Insights { get; set; } = new();
}

public class HighValueSegment
{
    public string SegmentId { get; set; } = string.Empty;
    public string SegmentName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public int UserCount { get; set; }
    public decimal ValuePerUser { get; set; }
    public Dictionary<string, decimal> KeyMetrics { get; set; } = new();
}

public class SegmentMigrationAnalysis
{
    public Dictionary<string, Dictionary<string, int>> MigrationMatrix { get; set; } = new();
    public Dictionary<string, decimal> RetentionRates { get; set; } = new();
    public Dictionary<string, decimal> ChurnRates { get; set; } = new();
    public List<MigrationInsight> Insights { get; set; } = new();
}

public class DynamicSegment
{
    public string SegmentId { get; set; } = string.Empty;
    public string SegmentName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SegmentationCriteria Criteria { get; set; } = new();
    public int UserCount { get; set; }
    public PerformanceMetrics Performance { get; set; } = PerformanceMetrics.Empty();
}

public class SegmentationCriteria
{
    public Dictionary<string, object> BehaviorCriteria { get; set; } = new();
    public Dictionary<string, object> DemographicCriteria { get; set; } = new();
    public Dictionary<string, object> EngagementCriteria { get; set; } = new();
    public Dictionary<string, object> ValueCriteria { get; set; } = new();
}

public class SegmentInsight
{
    public string SegmentId { get; set; } = string.Empty;
    public string InsightType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class MigrationInsight
{
    public string FromSegment { get; set; } = string.Empty;
    public string ToSegment { get; set; } = string.Empty;
    public int MigrationCount { get; set; }
    public decimal MigrationRate { get; set; }
    public string Reason { get; set; } = string.Empty;
    public Dictionary<string, object> Factors { get; set; } = new();
}

public enum AttributionModel
{
    FirstTouch = 1,
    LastTouch = 2,
    Linear = 3,
    TimeDecay = 4,
    PositionBased = 5,
    DataDriven = 6
}
