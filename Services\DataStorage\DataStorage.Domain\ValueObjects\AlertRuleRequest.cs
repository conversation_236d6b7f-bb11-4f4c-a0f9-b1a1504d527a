using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for alert rule operations
/// </summary>
public class AlertRuleRequest : ValueObject
{
    public string RuleName { get; init; }
    public string? RuleType { get; init; }
    public string? Severity { get; init; }
    public Dictionary<string, object> Conditions { get; init; }
    public List<string>? Tags { get; init; }
    public bool IsEnabled { get; init; }
    public string? NotificationChannel { get; init; }
    public Dictionary<string, object> Parameters { get; init; }

    public AlertRuleRequest(
        string ruleName,
        string? ruleType = null,
        string? severity = null,
        Dictionary<string, object>? conditions = null,
        List<string>? tags = null,
        bool isEnabled = true,
        string? notificationChannel = null,
        Dictionary<string, object>? parameters = null)
    {
        RuleName = ruleName;
        RuleType = ruleType;
        Severity = severity;
        Conditions = conditions ?? new Dictionary<string, object>();
        Tags = tags;
        IsEnabled = isEnabled;
        NotificationChannel = notificationChannel;
        Parameters = parameters ?? new Dictionary<string, object>();
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return RuleName;
        yield return RuleType ?? string.Empty;
        yield return Severity ?? string.Empty;
        yield return IsEnabled;
        yield return NotificationChannel ?? string.Empty;
        
        if (Tags != null)
        {
            foreach (var tag in Tags.OrderBy(x => x))
            {
                yield return tag;
            }
        }
        
        foreach (var condition in Conditions.OrderBy(x => x.Key))
        {
            yield return condition.Key;
            yield return condition.Value?.ToString() ?? string.Empty;
        }
        
        foreach (var param in Parameters.OrderBy(x => x.Key))
        {
            yield return param.Key;
            yield return param.Value?.ToString() ?? string.Empty;
        }
    }
}
