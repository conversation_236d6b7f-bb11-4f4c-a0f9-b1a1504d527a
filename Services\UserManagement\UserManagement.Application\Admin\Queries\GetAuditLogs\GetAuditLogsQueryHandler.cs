using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Application.Models;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.Admin.Queries.GetAuditLogs
{
    public class GetAuditLogsQueryHandler : IRequestHandler<GetAuditLogsQuery, GetAuditLogsResponse>
    {
        private readonly IAuditService _auditService;
        private readonly ILogger<GetAuditLogsQueryHandler> _logger;

        public GetAuditLogsQueryHandler(
            IAuditService auditService,
            ILogger<GetAuditLogsQueryHandler> logger)
        {
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<GetAuditLogsResponse> Handle(GetAuditLogsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Retrieving audit logs with criteria: PageNumber={PageNumber}, PageSize={PageSize}",
                request.PageNumber, request.PageSize);

            try
            {
                var criteria = new UserManagement.Application.Models.AuditLogSearchCriteria
                {
                    UserId = request.UserId,
                    Action = request.Action?.ToString(),
                    EntityType = request.EntityType,
                    EntityId = request.EntityId?.ToString(),
                    Severity = request.Severity,
                    FromDate = request.StartDate,
                    ToDate = request.EndDate,
                    IpAddress = request.IpAddress,
                    SessionId = request.SessionId,
                    CorrelationId = request.CorrelationId,
                    SearchText = request.SearchTerm,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy ?? "Timestamp",
                    SortDirection = request.SortDescending ? "desc" : "asc"
                };

                var auditLogs = await _auditService.GetAuditLogsAsync(criteria);
                var totalCount = await _auditService.GetAuditLogCountAsync(criteria);

                var auditLogDtos = auditLogs.Select(al => new AuditLogDto
                {
                    Id = al.Id,
                    Action = al.Action.ToString(),
                    EntityType = al.EntityType,
                    EntityId = al.EntityId,
                    UserId = al.UserId,
                    UserName = al.UserName,
                    UserRole = al.UserRole,
                    Description = al.Description,
                    OldValues = al.OldValues,
                    NewValues = al.NewValues,
                    Severity = al.Severity.ToString(),
                    IpAddress = al.IpAddress,
                    UserAgent = al.UserAgent,
                    SessionId = al.SessionId,
                    CorrelationId = al.CorrelationId,
                    AdditionalData = al.AdditionalData,
                    Timestamp = al.Timestamp,
                    CreatedAt = al.CreatedAt
                }).ToList();

                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

                var response = new GetAuditLogsResponse
                {
                    AuditLogs = auditLogDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasNextPage = request.PageNumber < totalPages,
                    HasPreviousPage = request.PageNumber > 1
                };

                _logger.LogInformation("Successfully retrieved {Count} audit logs out of {TotalCount} total",
                    auditLogDtos.Count, totalCount);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs");
                throw;
            }
        }
    }
}
