using System.Text.Json;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for caching service
/// </summary>
public interface ICacheService
{
    /// <summary>
    /// Get cached value by key
    /// </summary>
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Set cached value with expiration
    /// </summary>
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Remove cached value by key
    /// </summary>
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove cached values by pattern
    /// </summary>
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if key exists in cache
    /// </summary>
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get or set cached value with factory function
    /// </summary>
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Increment numeric value in cache
    /// </summary>
    Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set expiration for existing key
    /// </summary>
    Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get multiple cached values by keys
    /// </summary>
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Set multiple cached values
    /// </summary>
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Add item to a list in cache
    /// </summary>
    Task<long> ListPushAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Get list items from cache
    /// </summary>
    Task<List<T>> ListGetAsync<T>(string key, long start = 0, long stop = -1, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Add item to a set in cache
    /// </summary>
    Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Get set members from cache
    /// </summary>
    Task<HashSet<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Add or update hash field in cache
    /// </summary>
    Task HashSetAsync<T>(string key, string field, T value, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Get hash field from cache
    /// </summary>
    Task<T?> HashGetAsync<T>(string key, string field, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Get all hash fields from cache
    /// </summary>
    Task<Dictionary<string, T>> HashGetAllAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
}

/// <summary>
/// Cache key constants for analytics service
/// </summary>
public static class CacheKeys
{
    public const string DASHBOARD_PREFIX = "dashboard:";
    public const string METRICS_PREFIX = "metrics:";
    public const string REPORTS_PREFIX = "reports:";
    public const string KPI_PREFIX = "kpi:";
    public const string ANALYTICS_PREFIX = "analytics:";
    public const string REALTIME_PREFIX = "realtime:";
    public const string EXPORT_PREFIX = "export:";
    public const string COHORT_PREFIX = "cohort:";
    public const string FUNNEL_PREFIX = "funnel:";
    public const string PREDICTION_PREFIX = "prediction:";

    // Dashboard cache keys
    public static string AdminDashboard(string period) => $"{DASHBOARD_PREFIX}admin:{period}";
    public static string UserDashboard(Guid userId, string role, string period) => $"{DASHBOARD_PREFIX}{role}:{userId}:{period}";
    public static string RealTimeDashboard(Guid userId, string role) => $"{REALTIME_PREFIX}dashboard:{role}:{userId}";

    // Metrics cache keys
    public static string MetricTimeSeries(string metricName, Guid? userId, string period) => $"{METRICS_PREFIX}timeseries:{metricName}:{userId}:{period}";
    public static string KPIPerformance(Guid? userId, string period) => $"{KPI_PREFIX}performance:{userId}:{period}";
    public static string MetricTrends(string metricName, string period) => $"{METRICS_PREFIX}trends:{metricName}:{period}";

    // Reports cache keys
    public static string CustomReport(Guid reportId) => $"{REPORTS_PREFIX}custom:{reportId}";
    public static string ScheduledReport(Guid reportId, DateTime date) => $"{REPORTS_PREFIX}scheduled:{reportId}:{date:yyyyMMdd}";
    public static string ExportData(Guid userId, string exportType, string hash) => $"{EXPORT_PREFIX}{exportType}:{userId}:{hash}";

    // Analytics cache keys
    public static string CohortAnalysis(Guid userId, string cohortType, string period) => $"{COHORT_PREFIX}{cohortType}:{userId}:{period}";
    public static string FunnelAnalysis(Guid userId, string funnelType, string period) => $"{FUNNEL_PREFIX}{funnelType}:{userId}:{period}";
    public static string PredictiveModel(string modelType, Guid? userId) => $"{PREDICTION_PREFIX}{modelType}:{userId}";

    // Real-time cache keys
    public static string RealTimeMetrics(Guid? userId) => $"{REALTIME_PREFIX}metrics:{userId}";
    public static string RealTimeAlerts(Guid? userId) => $"{REALTIME_PREFIX}alerts:{userId}";
    public static string RealTimeEvents(Guid? userId, string eventType) => $"{REALTIME_PREFIX}events:{eventType}:{userId}";
}

/// <summary>
/// Cache expiration constants
/// </summary>
public static class CacheExpiration
{
    public static readonly TimeSpan RealTime = TimeSpan.FromMinutes(1);
    public static readonly TimeSpan ShortTerm = TimeSpan.FromMinutes(5);
    public static readonly TimeSpan MediumTerm = TimeSpan.FromMinutes(30);
    public static readonly TimeSpan LongTerm = TimeSpan.FromHours(2);
    public static readonly TimeSpan Daily = TimeSpan.FromHours(24);
    public static readonly TimeSpan Weekly = TimeSpan.FromDays(7);
}
