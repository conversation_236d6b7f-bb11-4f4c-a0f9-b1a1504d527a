namespace AnalyticsBIService.Application.DTOs;

/// <summary>
/// Admin platform dashboard data transfer object
/// </summary>
public class AdminPlatformDashboardDto
{
    public DateTime GeneratedAt { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string Period { get; set; } = string.Empty;

    // Platform Performance Metrics
    public PlatformPerformanceMetricsDto PlatformPerformance { get; set; } = new();

    // Revenue Analytics
    public RevenueAnalyticsDto RevenueAnalytics { get; set; } = new();

    // User Growth
    public UserGrowthTrackingDto UserGrowth { get; set; } = new();

    // Real-time KPIs
    public RealTimeKPIMonitoringDto RealTimeKPIs { get; set; } = new();

    // Geographic Performance
    public GeographicPerformanceDto GeographicPerformance { get; set; } = new();

    // Subscription Analytics
    public SubscriptionAnalyticsDto SubscriptionAnalytics { get; set; } = new();

    // Top Events and Activities
    public List<TopEventDto> TopEvents { get; set; } = new();

    // Critical Alerts
    public List<AlertDto> CriticalAlerts { get; set; } = new();

    // Key Performance Indicators
    public List<KPIPerformanceDto> KeyKPIs { get; set; } = new();
}

/// <summary>
/// Real-time KPI monitoring data transfer object
/// </summary>
public class RealTimeKPIMonitoringDto
{
    public DateTime LastUpdated { get; set; }
    public decimal RFQConversionRate { get; set; }
    public decimal QuoteAccuracyRate { get; set; }
    public decimal TripCompletionPercentage { get; set; }
    public long TotalActiveUsers { get; set; }
    public long TotalTransactions { get; set; }
    public decimal AverageResponseTime { get; set; }
    public decimal SystemUptime { get; set; }
    public decimal ErrorRate { get; set; }
    public long TotalRevenue { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public List<RealTimeMetricDto> CustomKPIs { get; set; } = new();
}

/// <summary>
/// Revenue analytics data transfer object
/// </summary>
public class RevenueAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowthRate { get; set; }
    public decimal AverageRevenuePerUser { get; set; }
    public decimal MonthlyRecurringRevenue { get; set; }
    public decimal AnnualRecurringRevenue { get; set; }
    public decimal ChurnRate { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public List<RevenueDataPointDto> RevenueTrends { get; set; } = new();
    public List<SubscriptionTierRevenueDto> SubscriptionBreakdown { get; set; } = new();
    public List<UserTypeRevenueDto> UserTypeBreakdown { get; set; } = new();
    public ChurnAnalysisDto ChurnAnalysis { get; set; } = new();
}

/// <summary>
/// Revenue data point for time series
/// </summary>
public class RevenueDataPointDto
{
    public DateTime Date { get; set; }
    public decimal Revenue { get; set; }
    public decimal TransactionVolume { get; set; }
    public long TransactionCount { get; set; }
    public decimal AverageTransactionValue { get; set; }
}

/// <summary>
/// Subscription tier revenue breakdown
/// </summary>
public class SubscriptionTierRevenueDto
{
    public string TierName { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public long SubscriberCount { get; set; }
    public decimal AverageRevenuePerSubscriber { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal MarketShare { get; set; }
}

/// <summary>
/// User type revenue breakdown
/// </summary>
public class UserTypeRevenueDto
{
    public string UserType { get; set; } = string.Empty;
    public decimal Revenue { get; set; }
    public long UserCount { get; set; }
    public decimal AverageRevenuePerUser { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Churn analysis data transfer object
/// </summary>
public class ChurnAnalysisDto
{
    public decimal OverallChurnRate { get; set; }
    public decimal MonthlyChurnRate { get; set; }
    public decimal AnnualChurnRate { get; set; }
    public List<ChurnByTierDto> ChurnByTier { get; set; } = new();
    public List<ChurnReasonDto> ChurnReasons { get; set; } = new();
    public List<ChurnTrendDto> ChurnTrends { get; set; } = new();
    public decimal ChurnPredictionAccuracy { get; set; }
    public List<AtRiskCustomerDto> AtRiskCustomers { get; set; } = new();
}

/// <summary>
/// Churn by subscription tier
/// </summary>
public class ChurnByTierDto
{
    public string TierName { get; set; } = string.Empty;
    public decimal ChurnRate { get; set; }
    public long ChurnedUsers { get; set; }
    public long TotalUsers { get; set; }
}

/// <summary>
/// Churn reason analysis
/// </summary>
public class ChurnReasonDto
{
    public string Reason { get; set; } = string.Empty;
    public long Count { get; set; }
    public decimal Percentage { get; set; }
    public string Category { get; set; } = string.Empty;
}

/// <summary>
/// Churn trend over time
/// </summary>
public class ChurnTrendDto
{
    public DateTime Date { get; set; }
    public decimal ChurnRate { get; set; }
    public long ChurnedUsers { get; set; }
    public long NewUsers { get; set; }
    public long NetUserGrowth { get; set; }
}

/// <summary>
/// At-risk customer identification
/// </summary>
public class AtRiskCustomerDto
{
    public Guid UserId { get; set; }
    public string UserType { get; set; } = string.Empty;
    public decimal ChurnProbability { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public DateTime LastActivity { get; set; }
    public decimal EngagementScore { get; set; }
    public string RecommendedAction { get; set; } = string.Empty;
}

/// <summary>
/// User growth tracking data transfer object
/// </summary>
public class UserGrowthTrackingDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long NewUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal UserGrowthRate { get; set; }
    public decimal UserRetentionRate { get; set; }
    public decimal UserActivationRate { get; set; }
    public List<UserGrowthDataPointDto> GrowthTrends { get; set; } = new();
    public List<UserTypeGrowthDto> UserTypeBreakdown { get; set; } = new();
    public List<UserCohortAnalysisDto> CohortAnalysis { get; set; } = new();
    public UserAcquisitionAnalysisDto AcquisitionAnalysis { get; set; } = new();
}

/// <summary>
/// User growth data point for time series
/// </summary>
public class UserGrowthDataPointDto
{
    public DateTime Date { get; set; }
    public long TotalUsers { get; set; }
    public long NewUsers { get; set; }
    public long ActiveUsers { get; set; }
    public long ChurnedUsers { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// User type growth breakdown
/// </summary>
public class UserTypeGrowthDto
{
    public string UserType { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long NewUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal RetentionRate { get; set; }
    public decimal MarketShare { get; set; }
}

/// <summary>
/// User cohort analysis
/// </summary>
public class UserCohortAnalysisDto
{
    public DateTime CohortMonth { get; set; }
    public long CohortSize { get; set; }
    public List<CohortRetentionDto> RetentionRates { get; set; } = new();
    public decimal LifetimeValue { get; set; }
    public decimal AverageRevenue { get; set; }
}

/// <summary>
/// Cohort retention data
/// </summary>
public class CohortRetentionDto
{
    public int MonthNumber { get; set; }
    public long ActiveUsers { get; set; }
    public decimal RetentionRate { get; set; }
}

/// <summary>
/// User acquisition analysis
/// </summary>
public class UserAcquisitionAnalysisDto
{
    public List<AcquisitionChannelDto> AcquisitionChannels { get; set; } = new();
    public decimal AverageAcquisitionCost { get; set; }
    public decimal AcquisitionEfficiency { get; set; }
    public List<AcquisitionTrendDto> AcquisitionTrends { get; set; } = new();
}

/// <summary>
/// Acquisition channel performance
/// </summary>
public class AcquisitionChannelDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public string ChannelName { get; set; } = string.Empty;
    public long NewUsers { get; set; }
    public long NewCustomers { get; set; }
    public decimal AcquisitionCost { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal QualityScore { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal ChannelEffectiveness { get; set; }
    public decimal ROI { get; set; }
}

/// <summary>
/// Acquisition trend over time
/// </summary>
public class AcquisitionTrendDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public long NewUsers { get; set; }
    public long NewCustomers { get; set; }
    public decimal AcquisitionCost { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal ChannelEffectiveness { get; set; }
}

/// <summary>
/// Geographic performance data transfer object
/// </summary>
public class GeographicPerformanceDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<RegionPerformanceDto> RegionPerformance { get; set; } = new();
    public List<CountryPerformanceDto> CountryPerformance { get; set; } = new();
    public List<StatePerformanceDto> StatePerformance { get; set; } = new();
    public List<CityPerformanceDto> CityPerformance { get; set; } = new();
    public MarketPenetrationAnalysisDto MarketPenetration { get; set; } = new();
}

/// <summary>
/// Region performance data
/// </summary>
public class RegionPerformanceDto
{
    public string RegionName { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal Revenue { get; set; }
    public long TransactionCount { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal MarketShare { get; set; }
    public decimal CustomerSatisfaction { get; set; }
}

/// <summary>
/// Country performance data
/// </summary>
public class CountryPerformanceDto
{
    public string CountryName { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal Revenue { get; set; }
    public long TransactionCount { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal MarketPenetration { get; set; }
}

/// <summary>
/// State performance data
/// </summary>
public class StatePerformanceDto
{
    public string StateName { get; set; } = string.Empty;
    public string StateCode { get; set; } = string.Empty;
    public string CountryName { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal Revenue { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// City performance data
/// </summary>
public class CityPerformanceDto
{
    public string CityName { get; set; } = string.Empty;
    public string StateName { get; set; } = string.Empty;
    public string CountryName { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal Revenue { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Market penetration analysis
/// </summary>
public class MarketPenetrationAnalysisDto
{
    public decimal OverallPenetrationRate { get; set; }
    public List<MarketSegmentPenetrationDto> SegmentPenetration { get; set; } = new();
    public List<CompetitorAnalysisDto> CompetitorAnalysis { get; set; } = new();
    public List<MarketOpportunityDto> MarketOpportunities { get; set; } = new();
}

/// <summary>
/// Market segment penetration
/// </summary>
public class MarketSegmentPenetrationDto
{
    public string SegmentName { get; set; } = string.Empty;
    public decimal PenetrationRate { get; set; }
    public long TotalAddressableMarket { get; set; }
    public long CapturedMarket { get; set; }
    public decimal GrowthPotential { get; set; }
}



/// <summary>
/// Market opportunity identification
/// </summary>
public class MarketOpportunityDto
{
    public string OpportunityName { get; set; } = string.Empty;
    public string OpportunityType { get; set; } = string.Empty; // Type of opportunity
    public string Region { get; set; } = string.Empty;
    public string Segment { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty; // Detailed description
    public decimal PotentialRevenue { get; set; }
    public decimal PotentialValue { get; set; } // Alias for PotentialRevenue
    public decimal InvestmentRequired { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
    public List<string> RequiredActions { get; set; } = new();
}

/// <summary>
/// Business intelligence report data transfer object
/// </summary>
public class BusinessIntelligenceReportDto
{
    public Guid Id { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedAt { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Guid GeneratedBy { get; set; }
    public Dictionary<string, object> ReportData { get; set; } = new();
    public List<ReportSectionDto> Sections { get; set; } = new();
    public bool IsComplianceReport { get; set; }
    public string ComplianceType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Customer NPS tracking data transfer object
/// </summary>
public class CustomerNPSTrackingDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal OverallNPS { get; set; }
    public decimal NPSGrowthRate { get; set; }
    public long TotalResponses { get; set; }
    public decimal ResponseRate { get; set; }
    public List<NPSDataPointDto> NPSTrends { get; set; } = new();
    public List<NPSByUserTypeDto> NPSByUserType { get; set; } = new();
    public List<NPSByRegionDto> NPSByRegion { get; set; } = new();
    public NPSAnalysisDto NPSAnalysis { get; set; } = new();
}

/// <summary>
/// NPS data point for time series
/// </summary>
public class NPSDataPointDto
{
    public DateTime Date { get; set; }
    public decimal NPS { get; set; }
    public long Promoters { get; set; }
    public long Passives { get; set; }
    public long Detractors { get; set; }
    public long TotalResponses { get; set; }
}

/// <summary>
/// NPS breakdown by user type
/// </summary>
public class NPSByUserTypeDto
{
    public string UserType { get; set; } = string.Empty;
    public decimal NPS { get; set; }
    public long TotalResponses { get; set; }
    public decimal ResponseRate { get; set; }
    public decimal Trend { get; set; }
}

/// <summary>
/// NPS breakdown by region
/// </summary>
public class NPSByRegionDto
{
    public string Region { get; set; } = string.Empty;
    public decimal NPS { get; set; }
    public long TotalResponses { get; set; }
    public decimal ResponseRate { get; set; }
}

/// <summary>
/// NPS analysis and insights
/// </summary>
public class NPSAnalysisDto
{
    public List<string> TopPositiveFeedback { get; set; } = new();
    public List<string> TopNegativeFeedback { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public decimal SentimentScore { get; set; }
}

/// <summary>
/// Subscription analytics data transfer object
/// </summary>
public class SubscriptionAnalyticsDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public long TotalSubscriptions { get; set; }
    public long ActiveSubscriptions { get; set; }
    public decimal SubscriptionGrowthRate { get; set; }
    public decimal AverageSubscriptionValue { get; set; }
    public List<SubscriptionTierDistributionDto> TierDistribution { get; set; } = new();
    public List<SubscriptionConversionDto> ConversionAnalysis { get; set; } = new();
    public List<SubscriptionTrendDto> SubscriptionTrends { get; set; } = new();
    public SubscriptionHealthDto SubscriptionHealth { get; set; } = new();
}

/// <summary>
/// Subscription tier distribution
/// </summary>
public class SubscriptionTierDistributionDto
{
    public string TierName { get; set; } = string.Empty;
    public long SubscriberCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal Revenue { get; set; }
    public decimal GrowthRate { get; set; }
    public decimal ChurnRate { get; set; }
}

/// <summary>
/// Subscription conversion analysis
/// </summary>
public class SubscriptionConversionDto
{
    public string FromTier { get; set; } = string.Empty;
    public string ToTier { get; set; } = string.Empty;
    public long ConversionCount { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal AverageTimeToConvert { get; set; }
    public decimal RevenueImpact { get; set; }
}

/// <summary>
/// Subscription trend over time
/// </summary>
public class SubscriptionTrendDto
{
    public DateTime Date { get; set; }
    public long TotalSubscriptions { get; set; }
    public long NewSubscriptions { get; set; }
    public long CancelledSubscriptions { get; set; }
    public long UpgradedSubscriptions { get; set; }
    public long DowngradedSubscriptions { get; set; }
    public decimal NetGrowth { get; set; }
}

/// <summary>
/// Subscription health metrics
/// </summary>
public class SubscriptionHealthDto
{
    public decimal HealthScore { get; set; }
    public decimal ChurnRisk { get; set; }
    public decimal UpgradeOpportunity { get; set; }
    public List<SubscriptionRiskFactorDto> RiskFactors { get; set; } = new();
    public List<SubscriptionOpportunityDto> GrowthOpportunities { get; set; } = new();
}

/// <summary>
/// Subscription risk factor
/// </summary>
public class SubscriptionRiskFactorDto
{
    public string RiskFactor { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public long AffectedSubscriptions { get; set; }
    public string Mitigation { get; set; } = string.Empty;
}

/// <summary>
/// Subscription growth opportunity
/// </summary>
public class SubscriptionOpportunityDto
{
    public string Opportunity { get; set; } = string.Empty;
    public decimal PotentialRevenue { get; set; }
    public long TargetSubscriptions { get; set; }
    public string Strategy { get; set; } = string.Empty;
}

/// <summary>
/// Platform usage report data transfer object
/// </summary>
public class PlatformUsageReportDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public long TotalSessions { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public long TotalPageViews { get; set; }
    public decimal BounceRate { get; set; }
    public List<FeatureUsageDto> FeatureUsage { get; set; } = new();
    public List<UserTypeUsageDto> UserTypeUsage { get; set; } = new();
    public List<DeviceUsageDto> DeviceUsage { get; set; } = new();
    public List<UsageTrendDto> UsageTrends { get; set; } = new();
}

/// <summary>
/// Feature usage statistics
/// </summary>
public class FeatureUsageDto
{
    public string FeatureName { get; set; } = string.Empty;
    public long UsageCount { get; set; }
    public long UniqueUsers { get; set; }
    public decimal AdoptionRate { get; set; }
    public decimal EngagementScore { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// User type usage breakdown
/// </summary>
public class UserTypeUsageDto
{
    public string UserType { get; set; } = string.Empty;
    public long ActiveUsers { get; set; }
    public long TotalSessions { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public long TotalActions { get; set; }
    public decimal EngagementScore { get; set; }
}

/// <summary>
/// Device usage statistics
/// </summary>
public class DeviceUsageDto
{
    public string DeviceType { get; set; } = string.Empty;
    public string Platform { get; set; } = string.Empty;
    public long UserCount { get; set; }
    public decimal Percentage { get; set; }
    public decimal AverageSessionDuration { get; set; }
}

/// <summary>
/// Usage trend over time
/// </summary>
public class UsageTrendDto
{
    public DateTime Date { get; set; }
    public long ActiveUsers { get; set; }
    public long TotalSessions { get; set; }
    public decimal AverageSessionDuration { get; set; }
    public long TotalActions { get; set; }
}

/// <summary>
/// Regulatory compliance report data transfer object
/// </summary>
public class RegulatoryComplianceReportDto
{
    public Guid Id { get; set; }
    public string ReportName { get; set; } = string.Empty;
    public string ComplianceType { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime GeneratedAt { get; set; }
    public Guid GeneratedBy { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<ComplianceMetricDto> ComplianceMetrics { get; set; } = new();
    public List<ComplianceViolationDto> Violations { get; set; } = new();
    public List<ComplianceRecommendationDto> Recommendations { get; set; } = new();
    public Dictionary<string, object> RegulatoryData { get; set; } = new();
}

/// <summary>
/// Compliance metric data
/// </summary>
public class ComplianceMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal RequiredValue { get; set; }
    public string Unit { get; set; } = string.Empty;
    public bool IsCompliant { get; set; }
    public string ComplianceStatus { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
}

/// <summary>
/// Compliance violation data
/// </summary>
public class ComplianceViolationDto
{
    public Guid Id { get; set; }
    public string ViolationType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime DetectedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public List<string> AffectedEntities { get; set; } = new();
    public string Resolution { get; set; } = string.Empty;
}

/// <summary>
/// Compliance recommendation data
/// </summary>
public class ComplianceRecommendationDto
{
    public string Recommendation { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public List<string> RequiredActions { get; set; } = new();
    public string ResponsibleParty { get; set; } = string.Empty;
}

/// <summary>
/// Audit trail data transfer object
/// </summary>
public class AuditTrailDto
{
    public Guid Id { get; set; }
    public Guid? UserId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Changes { get; set; } = new();
    public Dictionary<string, object> AdditionalData { get; set; } = new();
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string SessionId { get; set; } = string.Empty;
}

/// <summary>
/// Stakeholder presentation data transfer object
/// </summary>
public class StakeholderPresentationDataDto
{
    public DateTime GeneratedAt { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public ExecutiveSummaryDto ExecutiveSummary { get; set; } = new();
    public FinancialHighlightsDto FinancialHighlights { get; set; } = new();
    public OperationalMetricsDto OperationalMetrics { get; set; } = new();
    public List<KeyMetricDto> KeyMetrics { get; set; } = new();
    public List<ChartDataDto> Charts { get; set; } = new();
    public List<string> KeyInsights { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Executive summary for stakeholder presentations
/// </summary>
public class ExecutiveSummaryDto
{
    public string PeriodSummary { get; set; } = string.Empty;
    public List<string> KeyAchievements { get; set; } = new();
    public List<string> Challenges { get; set; } = new();
    public List<string> Opportunities { get; set; } = new();
    public decimal OverallPerformanceScore { get; set; }
    public string PerformanceRating { get; set; } = string.Empty;
}

/// <summary>
/// Financial highlights for stakeholder presentations
/// </summary>
public class FinancialHighlightsDto
{
    public decimal TotalRevenue { get; set; }
    public decimal RevenueGrowth { get; set; }
    public decimal GrossMargin { get; set; }
    public decimal NetMargin { get; set; }
    public decimal EBITDA { get; set; }
    public decimal CustomerAcquisitionCost { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal MonthlyRecurringRevenue { get; set; }
    public decimal AnnualRecurringRevenue { get; set; }
}

/// <summary>
/// Operational metrics for stakeholder presentations
/// </summary>
public class OperationalMetricsDto
{
    public long TotalUsers { get; set; }
    public long ActiveUsers { get; set; }
    public decimal UserGrowthRate { get; set; }
    public decimal UserRetentionRate { get; set; }
    public long TotalTransactions { get; set; }
    public decimal TransactionGrowthRate { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal SystemUptime { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
}

/// <summary>
/// Key metric for presentations
/// </summary>
public class KeyMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal Change { get; set; }
    public decimal ChangePercentage { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string TrendDirection { get; set; } = string.Empty;
    public string PerformanceStatus { get; set; } = string.Empty;
}

/// <summary>
/// Chart data for presentations
/// </summary>
public class ChartDataDto
{
    public string ChartTitle { get; set; } = string.Empty;
    public string ChartType { get; set; } = string.Empty;
    public List<string> Labels { get; set; } = new();
    public List<ChartSeriesDto> Series { get; set; } = new();
    public Dictionary<string, object> ChartOptions { get; set; } = new();
}

/// <summary>
/// Chart series data
/// </summary>
public class ChartSeriesDto
{
    public string Name { get; set; } = string.Empty;
    public List<decimal> Data { get; set; } = new();
    public string Color { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
}

/// <summary>
/// Custom report data transfer object
/// </summary>
public class CustomReportDataDto
{
    public DateTime GeneratedAt { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<CustomReportSectionDto> Sections { get; set; } = new();
    public Dictionary<string, object> Summary { get; set; } = new();
    public List<ChartDataDto> Charts { get; set; } = new();
    public Dictionary<string, List<Dictionary<string, object>>> Tables { get; set; } = new();
}

/// <summary>
/// Custom report section
/// </summary>
public class CustomReportSectionDto
{
    public string SectionName { get; set; } = string.Empty;
    public string SectionType { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Transaction volume tracking data transfer object
/// </summary>
public class TransactionVolumeTrackingDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public long TotalTransactions { get; set; }
    public decimal TotalVolume { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal TransactionGrowthRate { get; set; }
    public decimal VolumeGrowthRate { get; set; }
    public List<TransactionVolumeDataPointDto> VolumeTrends { get; set; } = new();
    public List<TransactionTypeVolumeDto> TransactionTypeBreakdown { get; set; } = new();
    public List<DataSourceVolumeDto> DataSourceBreakdown { get; set; } = new();
}

/// <summary>
/// Transaction volume data point
/// </summary>
public class TransactionVolumeDataPointDto
{
    public DateTime Date { get; set; }
    public long TransactionCount { get; set; }
    public decimal Volume { get; set; }
    public decimal AverageValue { get; set; }
}

/// <summary>
/// Transaction type volume breakdown
/// </summary>
public class TransactionTypeVolumeDto
{
    public string TransactionType { get; set; } = string.Empty;
    public long TransactionCount { get; set; }
    public decimal Volume { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Data source volume breakdown
/// </summary>
public class DataSourceVolumeDto
{
    public string DataSource { get; set; } = string.Empty;
    public long TransactionCount { get; set; }
    public decimal Volume { get; set; }
    public decimal Percentage { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Transport company market intelligence data transfer object
/// </summary>
public class TransportCompanyMarketIntelligenceDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? MarketSegment { get; set; }
    public string? GeographicRegion { get; set; }
    public List<MarketTrendDto> MarketTrends { get; set; } = new();
    public List<CompetitiveAnalysisDto> CompetitiveAnalysis { get; set; } = new();
    public List<CustomerAcquisitionMetricsDto> CustomerAcquisition { get; set; } = new();
    public MarketIntelligenceSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Market trend analysis
/// </summary>
public class MarketTrendDto
{
    public string TrendName { get; set; } = string.Empty;
    public string TrendType { get; set; } = string.Empty;
    public decimal TrendStrength { get; set; }
    public string Direction { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public List<string> KeyFactors { get; set; } = new();
}

/// <summary>
/// Customer acquisition metrics
/// </summary>
public class CustomerAcquisitionMetricsDto
{
    public Guid TransportCompanyId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public string AcquisitionChannel { get; set; } = string.Empty;
    public long NewCustomers { get; set; }
    public long TotalNewCustomers { get; set; }
    public decimal AcquisitionCost { get; set; }
    public decimal AverageAcquisitionCost { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal OverallConversionRate { get; set; }
    public decimal CustomerLifetimeValue { get; set; }
    public decimal AverageCustomerLifetimeValue { get; set; }
    public decimal ChannelEffectiveness { get; set; }
    public decimal ROI { get; set; }
    public decimal CustomerAcquisitionROI { get; set; }
    public List<AcquisitionChannelDto> AcquisitionChannels { get; set; } = new();
    public List<AcquisitionTrendDto> AcquisitionTrends { get; set; } = new();
}

/// <summary>
/// Market intelligence summary
/// </summary>
public class MarketIntelligenceSummaryDto
{
    public decimal MarketGrowthRate { get; set; }
    public decimal MarketShare { get; set; }
    public decimal CompetitivePosition { get; set; }
    public List<string> KeyOpportunities { get; set; } = new();
    public List<string> KeyThreats { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Broker margin analysis data transfer object
/// </summary>
public class BrokerMarginAnalysisDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal GrossMargin { get; set; }
    public decimal NetMargin { get; set; }
    public decimal AverageMarginPerTrip { get; set; }
    public decimal MarginGrowthRate { get; set; }
    public List<MarginTrendDto> MarginTrends { get; set; } = new();
    public List<CommissionTrackingDto> CommissionTracking { get; set; } = new();
    public ProfitabilityMetricsDto ProfitabilityMetrics { get; set; } = new();
}

/// <summary>
/// Margin trend over time
/// </summary>
public class MarginTrendDto
{
    public DateTime Date { get; set; }
    public decimal GrossMargin { get; set; }
    public decimal NetMargin { get; set; }
    public decimal MarginPercentage { get; set; }
    public long TransactionCount { get; set; }
}

/// <summary>
/// Commission tracking data
/// </summary>
public class CommissionTrackingDto
{
    public string CommissionType { get; set; } = string.Empty;
    public decimal CommissionAmount { get; set; }
    public decimal CommissionRate { get; set; }
    public long TransactionCount { get; set; }
    public decimal GrowthRate { get; set; }
}

/// <summary>
/// Profitability metrics
/// </summary>
public class ProfitabilityMetricsDto
{
    public decimal ROI { get; set; }
    public decimal ROE { get; set; }
    public decimal EBITDA { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal RevenuePerEmployee { get; set; }
}

/// <summary>
/// Broker business growth analytics data transfer object
/// </summary>
public class BrokerBusinessGrowthAnalyticsDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal RevenueGrowthRate { get; set; }
    public decimal CustomerGrowthRate { get; set; }
    public decimal MarketShareGrowth { get; set; }
    public List<GrowthMetricDto> GrowthMetrics { get; set; } = new();
    public List<MarketOpportunityDto> MarketOpportunities { get; set; } = new();
    public ExpansionPotentialDto ExpansionPotential { get; set; } = new();
}

/// <summary>
/// Growth metric data
/// </summary>
public class GrowthMetricDto
{
    public string MetricName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal GrowthRate { get; set; }
    public string Trend { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
}

/// <summary>
/// Expansion potential analysis
/// </summary>
public class ExpansionPotentialDto
{
    public decimal OverallPotential { get; set; }
    public List<ExpansionOpportunityDto> Opportunities { get; set; } = new();
    public List<ExpansionBarrierDto> Barriers { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// Expansion opportunity
/// </summary>
public class ExpansionOpportunityDto
{
    public string OpportunityType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialRevenue { get; set; }
    public decimal InvestmentRequired { get; set; }
    public decimal ROI { get; set; }
    public string Priority { get; set; } = string.Empty;
}

/// <summary>
/// Expansion barrier
/// </summary>
public class ExpansionBarrierDto
{
    public string BarrierType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Impact { get; set; }
    public List<string> MitigationStrategies { get; set; } = new();
}

/// <summary>
/// Broker performance tracking data transfer object
/// </summary>
public class BrokerPerformanceTrackingDto
{
    public Guid BrokerId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = string.Empty;
    public decimal TripCompletionRate { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public decimal OperationalEfficiencyScore { get; set; }
    public List<PerformanceMetricTrendDto> PerformanceTrends { get; set; } = new();
    public DeliveryPerformanceMonitoringDto DeliveryPerformance { get; set; } = new();
}

/// <summary>
/// Performance metric trend
/// </summary>
public class PerformanceMetricTrendDto
{
    public DateTime Date { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Trend { get; set; } = string.Empty;
}

/// <summary>
/// Delivery performance monitoring
/// </summary>
public class DeliveryPerformanceMonitoringDto
{
    public decimal OnTimeRate { get; set; }
    public decimal EarlyRate { get; set; }
    public decimal LateRate { get; set; }
    public decimal AverageDeliveryTime { get; set; }
    public List<DeliveryIssueDto> Issues { get; set; } = new();
}
