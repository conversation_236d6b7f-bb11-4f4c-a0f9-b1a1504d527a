using OrderManagement.Domain.Enums;

namespace OrderManagement.Application.DTOs;

public class OrderTimelineDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public OrderTimelineEventType EventType { get; set; }
    public string EventDescription { get; set; } = string.Empty;
    public DateTime EventTimestamp { get; set; }
    public Guid? ActorId { get; set; }
    public string? ActorName { get; set; }
    public string? ActorRole { get; set; }
    public string? AdditionalData { get; set; }
    public OrderStatus? PreviousStatus { get; set; }
    public OrderStatus? NewStatus { get; set; }
    public string? Notes { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Guid? CorrelationId { get; set; }
    public string? SessionId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class OrderTimelineAggregateDto
{
    public Guid OrderId { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public OrderStatus CurrentStatus { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<OrderTimelineDto> Timeline { get; set; } = new();
    public List<TripTimelineDto> TripEvents { get; set; } = new();
    public TimelineStatistics Statistics { get; set; } = new();
}

public class TripTimelineDto
{
    public Guid TripId { get; set; }
    public string TripNumber { get; set; } = string.Empty;
    public string EventType { get; set; } = string.Empty;
    public string EventDescription { get; set; } = string.Empty;
    public DateTime EventTimestamp { get; set; }
    public string? Location { get; set; }
    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class TimelineStatistics
{
    public int TotalEvents { get; set; }
    public int StatusChanges { get; set; }
    public int AdminActions { get; set; }
    public int SystemActions { get; set; }
    public int UserActions { get; set; }
    public TimeSpan? TotalDuration { get; set; }
    public TimeSpan? AverageStatusDuration { get; set; }
    public List<StatusDurationDto> StatusDurations { get; set; } = new();
}

public class StatusDurationDto
{
    public OrderStatus Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
}

// Admin DTOs
public class AdminOrderDto
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public OrderStatus Status { get; set; }
    public Guid TransportCompanyId { get; set; }
    public string TransportCompanyName { get; set; } = string.Empty;
    public Guid? BrokerId { get; set; }
    public string? BrokerName { get; set; }
    public Guid? CarrierId { get; set; }
    public string? CarrierName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? CancelledAt { get; set; }
    public string? CancellationReason { get; set; }
    public decimal TotalAmount { get; set; }
    public int DocumentCount { get; set; }
    public int TimelineEventCount { get; set; }
    public bool HasIssues { get; set; }
    public string? LastAdminAction { get; set; }
    public DateTime? LastAdminActionAt { get; set; }
}

public class AdminOrderDetailDto : AdminOrderDto
{
    public List<OrderTimelineDto> Timeline { get; set; } = new();
    public List<OrderDocumentDto> Documents { get; set; } = new();
    public List<OrderAuditTrailDto> AuditTrail { get; set; } = new();
    public List<AdminNoteDto> AdminNotes { get; set; } = new();
    public OrderRfqDto? Rfq { get; set; }
    public List<OrderInvoiceDto> Invoices { get; set; } = new();
}

// Missing DTOs for OrderManagement
public class OrderDocumentDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DocumentType DocumentType { get; set; }
    public string? Description { get; set; }
    public DateTime UploadedAt { get; set; }
    public Guid UploadedBy { get; set; }
    public string? FilePath { get; set; }
    public string? FileUrl { get; set; }
    public bool IsVerified { get; set; }
    public DateTime? VerifiedAt { get; set; }
    public Guid? VerifiedBy { get; set; }
    public string? VerificationNotes { get; set; }
}

public class OrderRfqDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string RfqNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RfqStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string? ClosureReason { get; set; }
    public MoneyDto? BudgetRange { get; set; }
    public bool IsUrgent { get; set; }
    public string? ContactPerson { get; set; }
    public string? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
    public int BidCount { get; set; }
    public MoneyDto? LowestBid { get; set; }
    public MoneyDto? HighestBid { get; set; }
    public MoneyDto? AverageBid { get; set; }
}

public class OrderInvoiceDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public InvoiceType InvoiceType { get; set; }
    public InvoiceStatus Status { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public MoneyDto? TaxAmount { get; set; }
    public MoneyDto? TotalAmount { get; set; }
    public DateTime IssuedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? PaidAt { get; set; }
    public string? PaymentReference { get; set; }
    public string? Notes { get; set; }
    public List<InvoiceLineItemDto> LineItems { get; set; } = new();
    public BillingDetailsDto BillingDetails { get; set; } = new();
    public string? FilePath { get; set; }
    public string? FileUrl { get; set; }
}

public class InvoiceLineItemDto
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public MoneyDto UnitPrice { get; set; } = new();
    public MoneyDto TotalPrice { get; set; } = new();
    public string? ItemCode { get; set; }
    public string? Category { get; set; }
}

public class OrderAuditTrailDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? PreviousValue { get; set; }
    public string? NewValue { get; set; }
    public Guid? UserId { get; set; }
    public string? UserName { get; set; }
    public string? UserRole { get; set; }
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? Reason { get; set; }
    public AuditSeverity Severity { get; set; }
}

public class AdminNoteDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string Note { get; set; } = string.Empty;
    public bool IsInternal { get; set; }
    public Guid AdminUserId { get; set; }
    public string AdminUserName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class AdminOrderStatisticsDto
{
    public int TotalOrders { get; set; }
    public int OrdersByStatus { get; set; }
    public Dictionary<string, int> StatusBreakdown { get; set; } = new();
    public int OrdersRequiringAttention { get; set; }
    public int OverdueOrders { get; set; }
    public int OrdersWithIssues { get; set; }
    public decimal TotalRevenue { get; set; }
    public double AverageOrderValue { get; set; }
    public double AverageCompletionTime { get; set; }
    public List<DailyOrderStats> DailyStats { get; set; } = new();
    public DateTime GeneratedAt { get; set; }
}

public class DailyOrderStats
{
    public DateTime Date { get; set; }
    public int OrdersCreated { get; set; }
    public int OrdersCompleted { get; set; }
    public int OrdersCancelled { get; set; }
    public decimal Revenue { get; set; }
}

// Request DTOs
public class AdminOrderFilterRequest
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public OrderStatus? Status { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public string? OrderNumber { get; set; }
    public bool IncludeTimeline { get; set; } = false;
    public bool IncludeDocuments { get; set; } = false;
    public bool IncludeAuditTrail { get; set; } = false;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
}

public class ForceOrderClosureRequest
{
    public string Reason { get; set; } = string.Empty;
    public string? AdminNotes { get; set; }
    public bool NotifyParties { get; set; } = true;
}

public class AdminOverrideStatusRequest
{
    public OrderStatus NewStatus { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? AdminNotes { get; set; }
    public bool BypassValidation { get; set; } = false;
}

public class BulkUpdateOrderStatusRequest
{
    public List<Guid> OrderIds { get; set; } = new();
    public OrderStatus NewStatus { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? AdminNotes { get; set; }
}

public class AddAdminNoteRequest
{
    public string Note { get; set; } = string.Empty;
    public bool IsInternal { get; set; } = true;
}

public class AdminStatisticsRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Guid? TransportCompanyId { get; set; }
    public Guid? BrokerId { get; set; }
}

public class BulkUpdateResult
{
    public int TotalRequested { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<BulkUpdateError> Errors { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

public class BulkUpdateError
{
    public Guid OrderId { get; set; }
    public string Error { get; set; } = string.Empty;
}

public class BillingDetailsDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string TaxId { get; set; } = string.Empty;
    public string PaymentTerms { get; set; } = string.Empty;
    public string PreferredPaymentMethod { get; set; } = string.Empty;
}

public class InvoiceDetailDto
{
    public Guid Id { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public InvoiceType InvoiceType { get; set; }
    public InvoiceStatus Status { get; set; }
    public DateTime InvoiceDate { get; set; }
    public DateTime? DueDate { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public MoneyDto TaxAmount { get; set; } = new();
    public MoneyDto TotalAmount { get; set; } = new();
    public string Description { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public List<InvoiceLineItemDto> LineItems { get; set; } = new();
    public BillingDetailsDto BillingDetails { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class OrderDetailDto
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public OrderStatus Status { get; set; }
    public OrderType OrderType { get; set; }
    public LoadDetailsDto LoadDetails { get; set; } = new();
    public RouteDetailsDto RouteDetails { get; set; } = new();
    public MoneyDto AgreedPrice { get; set; } = new();
    public DateTime PickupDate { get; set; }
    public DateTime DeliveryDate { get; set; }
    public string SpecialInstructions { get; set; } = string.Empty;
    public List<OrderDocumentDto> Documents { get; set; } = new();
    public List<OrderInvoiceDto> Invoices { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class OrderSummaryDto
{
    public Guid Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? ScheduledPickupDate { get; set; }
    public DateTime? ScheduledDeliveryDate { get; set; }
    public MoneyDto AgreedPrice { get; set; } = new();
    public string PickupCity { get; set; } = string.Empty;
    public string DeliveryCity { get; set; } = string.Empty;
    public Guid TransportCompanyId { get; set; }
    public Guid BrokerId { get; set; }
    public Guid? CarrierId { get; set; }
}

public class InvoiceSummaryDto
{
    public Guid Id { get; set; }
    public Guid OrderId { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; }
    public DateTime? DueDate { get; set; }
    public MoneyDto Amount { get; set; } = new();
    public MoneyDto TotalAmount { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public InvoiceType InvoiceType { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class TransportCompanyComparisonDto
{
    public Guid TransportCompanyId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal OverallRating { get; set; }
    public int TotalReviews { get; set; }
    public int TotalTrips { get; set; }
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public int Rank { get; set; }
    public List<string> Badges { get; set; } = new();
    public List<PerformanceIndicator> PerformanceIndicators { get; set; } = new();
}
