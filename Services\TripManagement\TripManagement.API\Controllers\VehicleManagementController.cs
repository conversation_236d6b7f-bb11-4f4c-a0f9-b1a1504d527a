using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TripManagement.Application.Commands.ScheduleMaintenance;
using TripManagement.Application.Commands.ForecastVehicleAvailability;

namespace TripManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VehicleManagementController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<VehicleManagementController> _logger;

    public VehicleManagementController(IMediator mediator, ILogger<VehicleManagementController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Schedule maintenance for a vehicle
    /// </summary>
    [HttpPost("maintenance/schedule")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult<Guid>> ScheduleMaintenance([FromBody] ScheduleMaintenanceRequest request)
    {
        try
        {
            var command = new ScheduleMaintenanceCommand
            {
                VehicleId = request.VehicleId,
                MaintenanceType = request.MaintenanceType,
                ScheduledDate = request.ScheduledDate,
                Description = request.Description,
                ServiceProvider = request.ServiceProvider,
                EstimatedCost = request.EstimatedCost,
                EstimatedDurationHours = request.EstimatedDurationHours,
                Priority = request.Priority,
                IsRecurring = request.IsRecurring,
                RecurrenceIntervalDays = request.RecurrenceIntervalDays,
                Notes = request.Notes,
                ScheduledBy = request.ScheduledBy
            };

            var maintenanceId = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMaintenance), new { id = maintenanceId }, maintenanceId);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while scheduling maintenance for vehicle {VehicleId}", 
                request.VehicleId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling maintenance for vehicle {VehicleId}", request.VehicleId);
            return StatusCode(500, "An error occurred while scheduling maintenance");
        }
    }

    /// <summary>
    /// Forecast vehicle availability
    /// </summary>
    [HttpPost("availability/forecast")]
    [Authorize(Roles = "Admin,Carrier,Broker")]
    public async Task<ActionResult<VehicleAvailabilityForecast>> ForecastAvailability([FromBody] ForecastAvailabilityRequest request)
    {
        try
        {
            var command = new ForecastVehicleAvailabilityCommand
            {
                VehicleId = request.VehicleId,
                CarrierId = request.CarrierId,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IncludeMaintenance = request.IncludeMaintenance,
                IncludeTrips = request.IncludeTrips,
                IncludeDocumentExpiry = request.IncludeDocumentExpiry
            };

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument while forecasting vehicle availability");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error forecasting vehicle availability");
            return StatusCode(500, "An error occurred while forecasting vehicle availability");
        }
    }

    /// <summary>
    /// Get maintenance record details
    /// </summary>
    [HttpGet("maintenance/{id:guid}")]
    [Authorize(Roles = "Admin,Carrier,Driver")]
    public async Task<ActionResult> GetMaintenance(Guid id)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return NotFound("Maintenance record not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance record {MaintenanceId}", id);
            return StatusCode(500, "An error occurred while retrieving the maintenance record");
        }
    }

    /// <summary>
    /// Get maintenance history for a vehicle
    /// </summary>
    [HttpGet("vehicles/{vehicleId:guid}/maintenance")]
    [Authorize(Roles = "Admin,Carrier,Driver")]
    public async Task<ActionResult> GetVehicleMaintenanceHistory(Guid vehicleId)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<object>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance history for vehicle {VehicleId}", vehicleId);
            return StatusCode(500, "An error occurred while retrieving maintenance history");
        }
    }

    /// <summary>
    /// Get vehicles requiring maintenance
    /// </summary>
    [HttpGet("maintenance/due")]
    [Authorize(Roles = "Admin,Carrier")]
    public async Task<ActionResult> GetVehiclesDueMaintenance([FromQuery] int daysAhead = 30)
    {
        try
        {
            // This would require a query handler - placeholder for now
            return Ok(new List<object>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicles due for maintenance");
            return StatusCode(500, "An error occurred while retrieving vehicles due for maintenance");
        }
    }
}

// Request DTOs
public record ScheduleMaintenanceRequest
{
    public Guid VehicleId { get; init; }
    public MaintenanceType MaintenanceType { get; init; }
    public DateTime ScheduledDate { get; init; }
    public string Description { get; init; } = string.Empty;
    public string? ServiceProvider { get; init; }
    public decimal? EstimatedCost { get; init; }
    public int? EstimatedDurationHours { get; init; }
    public MaintenancePriority Priority { get; init; } = MaintenancePriority.Medium;
    public bool IsRecurring { get; init; }
    public int? RecurrenceIntervalDays { get; init; }
    public string? Notes { get; init; }
    public Guid ScheduledBy { get; init; }
}

public record ForecastAvailabilityRequest
{
    public Guid? VehicleId { get; init; }
    public Guid? CarrierId { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public bool IncludeMaintenance { get; init; } = true;
    public bool IncludeTrips { get; init; } = true;
    public bool IncludeDocumentExpiry { get; init; } = true;
}
