using System.Net;
using System.Net.Http.Json;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Infrastructure.Persistence;
using NetworkFleetManagement.IntegrationTests.Infrastructure;
using Xunit;

namespace NetworkFleetManagement.IntegrationTests.Controllers;

public class CarriersControllerTests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public CarriersControllerTests(TestWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateCarrier_WithValidData_ShouldReturnCreatedCarrier()
    {
        // Arrange
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Test Logistics Ltd",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********",
            BusinessLicenseNumber = "BL123456",
            TaxIdentificationNumber = "TIN123456",
            Notes = "Integration test carrier"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/carriers", createCarrierDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var createdCarrier = await response.Content.ReadFromJsonAsync<CarrierDto>();
        createdCarrier.Should().NotBeNull();
        createdCarrier!.CompanyName.Should().Be("Test Logistics Ltd");
        createdCarrier.ContactPersonName.Should().Be("John Doe");
        createdCarrier.Email.Should().Be("<EMAIL>");
        createdCarrier.PhoneNumber.Should().Be("+**********");
        createdCarrier.Id.Should().NotBeEmpty();

        // Verify in database
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<NetworkFleetDbContext>();
        var carrierInDb = await context.Carriers.FindAsync(createdCarrier.Id);
        carrierInDb.Should().NotBeNull();
        carrierInDb!.CompanyName.Should().Be("Test Logistics Ltd");
    }

    [Fact]
    public async Task CreateCarrier_WithDuplicateUserId_ShouldReturnBadRequest()
    {
        // Arrange
        var userId = Guid.NewGuid();
        
        var firstCarrierDto = new CreateCarrierDto
        {
            UserId = userId,
            CompanyName = "First Logistics",
            ContactPersonName = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        var secondCarrierDto = new CreateCarrierDto
        {
            UserId = userId, // Same user ID
            CompanyName = "Second Logistics",
            ContactPersonName = "Jane Doe",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        // Act
        var firstResponse = await _client.PostAsJsonAsync("/api/v1/carriers", firstCarrierDto);
        var secondResponse = await _client.PostAsJsonAsync("/api/v1/carriers", secondCarrierDto);

        // Assert
        firstResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        secondResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetCarrier_WithValidId_ShouldReturnCarrier()
    {
        // Arrange
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Get Test Logistics",
            ContactPersonName = "Jane Smith",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        var createResponse = await _client.PostAsJsonAsync("/api/v1/carriers", createCarrierDto);
        var createdCarrier = await createResponse.Content.ReadFromJsonAsync<CarrierDto>();

        // Act
        var getResponse = await _client.GetAsync($"/api/v1/carriers/{createdCarrier!.Id}");

        // Assert
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var retrievedCarrier = await getResponse.Content.ReadFromJsonAsync<CarrierDto>();
        retrievedCarrier.Should().NotBeNull();
        retrievedCarrier!.Id.Should().Be(createdCarrier.Id);
        retrievedCarrier.CompanyName.Should().Be("Get Test Logistics");
    }

    [Fact]
    public async Task GetCarrier_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = Guid.NewGuid();

        // Act
        var response = await _client.GetAsync($"/api/v1/carriers/{invalidId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetCarriers_ShouldReturnPagedResults()
    {
        // Arrange
        var carriers = new[]
        {
            new CreateCarrierDto
            {
                UserId = Guid.NewGuid(),
                CompanyName = "Paged Test Logistics 1",
                ContactPersonName = "User 1",
                Email = "<EMAIL>",
                PhoneNumber = "+1111111111"
            },
            new CreateCarrierDto
            {
                UserId = Guid.NewGuid(),
                CompanyName = "Paged Test Logistics 2",
                ContactPersonName = "User 2",
                Email = "<EMAIL>",
                PhoneNumber = "+2222222222"
            }
        };

        foreach (var carrier in carriers)
        {
            await _client.PostAsJsonAsync("/api/v1/carriers", carrier);
        }

        // Act
        var response = await _client.GetAsync("/api/v1/carriers?pageNumber=1&pageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var pagedResult = await response.Content.ReadFromJsonAsync<PagedResult<CarrierSummaryDto>>();
        pagedResult.Should().NotBeNull();
        pagedResult!.Items.Should().NotBeEmpty();
        pagedResult.TotalCount.Should().BeGreaterOrEqualTo(2);
        pagedResult.PageNumber.Should().Be(1);
        pagedResult.PageSize.Should().Be(10);
    }

    [Fact]
    public async Task UpdateCarrierStatus_WithValidData_ShouldUpdateStatus()
    {
        // Arrange
        var createCarrierDto = new CreateCarrierDto
        {
            UserId = Guid.NewGuid(),
            CompanyName = "Status Update Test",
            ContactPersonName = "Status User",
            Email = "<EMAIL>",
            PhoneNumber = "+**********"
        };

        var createResponse = await _client.PostAsJsonAsync("/api/v1/carriers", createCarrierDto);
        var createdCarrier = await createResponse.Content.ReadFromJsonAsync<CarrierDto>();

        var updateStatusDto = new UpdateCarrierStatusDto
        {
            Status = Domain.Enums.CarrierStatus.Active,
            Reason = "Approved after verification"
        };

        // Act
        var updateResponse = await _client.PutAsJsonAsync($"/api/v1/carriers/{createdCarrier!.Id}/status", updateStatusDto);

        // Assert
        updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var updatedCarrier = await updateResponse.Content.ReadFromJsonAsync<CarrierDto>();
        updatedCarrier.Should().NotBeNull();
        updatedCarrier!.Status.Should().Be(Domain.Enums.CarrierStatus.Active);
        updatedCarrier.IsActive.Should().BeTrue();
    }

    [Fact]
    public async Task CreateCarrier_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidCarrierDto = new CreateCarrierDto
        {
            UserId = Guid.Empty, // Invalid
            CompanyName = "", // Invalid
            ContactPersonName = "John Doe",
            Email = "invalid-email", // Invalid
            PhoneNumber = "123" // Invalid
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/carriers", invalidCarrierDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
