using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.DTOs;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Application.Interfaces.Repositories;
using FinancialPayment.Domain.Enums;
using FinancialPayment.Domain.Entities;
using static FinancialPayment.Domain.Entities.PaymentMilestone;

namespace FinancialPayment.Application.Queries.GetEscrowAccountWithMilestones;

public class GetEscrowAccountWithMilestonesQueryHandler : IRequestHandler<GetEscrowAccountWithMilestonesQuery, EscrowAccountDto?>
{
    private readonly IEscrowAccountRepository _escrowRepository;
    private readonly IPaymentMilestoneRepository _milestoneRepository;
    private readonly IPaymentDisputeRepository _disputeRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetEscrowAccountWithMilestonesQueryHandler> _logger;

    public GetEscrowAccountWithMilestonesQueryHandler(
        IEscrowAccountRepository escrowRepository,
        IPaymentMilestoneRepository milestoneRepository,
        IPaymentDisputeRepository disputeRepository,
        IMapper mapper,
        ILogger<GetEscrowAccountWithMilestonesQueryHandler> logger)
    {
        _escrowRepository = escrowRepository;
        _milestoneRepository = milestoneRepository;
        _disputeRepository = disputeRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<EscrowAccountDto?> Handle(GetEscrowAccountWithMilestonesQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting escrow account {EscrowAccountId} with milestones for user {UserId}",
            request.EscrowAccountId, request.RequestingUserId);

        try
        {
            // Get the escrow account
            var escrowAccount = await _escrowRepository.GetByIdWithTransactionsAsync(request.EscrowAccountId, cancellationToken);
            if (escrowAccount == null)
            {
                _logger.LogWarning("Escrow account {EscrowAccountId} not found", request.EscrowAccountId);
                return null;
            }

            // Validate user has permission to view this escrow account
            if (!HasPermissionToView(escrowAccount, request.RequestingUserId))
            {
                _logger.LogWarning("User {UserId} does not have permission to view escrow account {EscrowAccountId}",
                    request.RequestingUserId, request.EscrowAccountId);
                throw new UnauthorizedAccessException("You do not have permission to view this escrow account");
            }

            // Get milestones
            var milestones = await _milestoneRepository.GetByEscrowAccountIdAsync(request.EscrowAccountId, cancellationToken);

            // Get dispute information if requested
            bool hasActiveDisputes = false;
            if (request.IncludeDisputeInfo)
            {
                var disputes = await _disputeRepository.GetByOrderIdAsync(escrowAccount.OrderId);
                hasActiveDisputes = disputes.Any(d => d.Status == Domain.Enums.DisputeStatus.Open ||
                                                     d.Status == Domain.Enums.DisputeStatus.InProgress);
            }

            // Map to DTO
            var escrowAccountDto = MapToEscrowAccountDto(escrowAccount, milestones, request, hasActiveDisputes);

            _logger.LogInformation("Successfully retrieved escrow account {EscrowAccountId} with {MilestoneCount} milestones",
                request.EscrowAccountId, milestones.Count);

            return escrowAccountDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting escrow account {EscrowAccountId} with milestones",
                request.EscrowAccountId);
            throw;
        }
    }

    private bool HasPermissionToView(Domain.Entities.EscrowAccount escrowAccount, Guid userId)
    {
        // User can view if they are the transport company, broker, or carrier
        return escrowAccount.TransportCompanyId == userId ||
               escrowAccount.BrokerId == userId ||
               escrowAccount.CarrierId == userId;
    }

    private EscrowAccountDto MapToEscrowAccountDto(
        Domain.Entities.EscrowAccount escrowAccount,
        List<Domain.Entities.PaymentMilestone> milestones,
        GetEscrowAccountWithMilestonesQuery request,
        bool hasActiveDisputes)
    {
        var dto = new EscrowAccountDto
        {
            Id = escrowAccount.Id,
            OrderId = escrowAccount.OrderId,
            TransportCompanyId = escrowAccount.TransportCompanyId,
            BrokerId = escrowAccount.BrokerId,
            CarrierId = escrowAccount.CarrierId,
            AccountNumber = escrowAccount.EscrowAccountNumber,
            TotalAmount = escrowAccount.TotalAmount.Amount,
            TotalCurrency = escrowAccount.TotalAmount.Currency,
            AvailableAmount = escrowAccount.AvailableAmount.Amount,
            AvailableCurrency = escrowAccount.AvailableAmount.Currency,
            ReservedAmount = escrowAccount.ReservedAmount.Amount,
            ReservedCurrency = escrowAccount.ReservedAmount.Currency,
            Status = escrowAccount.Status.ToString(),
            FundedAt = escrowAccount.FundedAt,
            ReleasedAt = escrowAccount.ReleasedAt,
            ReleaseReason = escrowAccount.ReleaseReason,
            Notes = escrowAccount.Notes,
            CreatedAt = escrowAccount.CreatedAt,
            UpdatedAt = escrowAccount.UpdatedAt,

            // Enhanced features
            HasActiveDisputes = hasActiveDisputes,
            TotalMilestones = milestones.Count,
            CompletedMilestones = milestones.Count(m => m.Status == PaymentMilestoneStatus.Completed),
            IsFullyFunded = escrowAccount.Status == Domain.Enums.EscrowStatus.Funded,
            CanReleaseFunds = escrowAccount.AvailableAmount.Amount > 0,
            NextMilestoneDue = milestones
                .Where(m => m.Status == PaymentMilestoneStatus.Pending && m.DueDate.HasValue)
                .OrderBy(m => m.DueDate)
                .FirstOrDefault()?.DueDate
        };

        // Calculate percentages
        if (dto.TotalAmount > 0)
        {
            dto.FundedPercentage = (dto.TotalAmount - dto.AvailableAmount) / dto.TotalAmount * 100;
            dto.ReleasedPercentage = (dto.TotalAmount - dto.AvailableAmount - dto.ReservedAmount) / dto.TotalAmount * 100;
        }

        if (dto.TotalMilestones > 0)
        {
            dto.MilestoneCompletionRate = (decimal)dto.CompletedMilestones / dto.TotalMilestones * 100;
        }

        // Map milestones
        dto.Milestones = milestones.Select(MapToPaymentMilestoneDto).ToList();

        // Map transactions if requested
        if (request.IncludeTransactions)
        {
            dto.Transactions = escrowAccount.Transactions.Select(MapToEscrowTransactionDto).ToList();
        }

        // Calculate summary if requested
        if (request.CalculateSummary)
        {
            dto.Summary = CalculateSummary(escrowAccount, milestones);
        }

        // Determine available actions
        dto.AvailableActions = DetermineAvailableActions(escrowAccount, milestones, request.RequestingUserId);

        return dto;
    }

    private PaymentMilestoneDto MapToPaymentMilestoneDto(Domain.Entities.PaymentMilestone milestone)
    {
        return new PaymentMilestoneDto
        {
            Id = milestone.Id,
            EscrowAccountId = milestone.EscrowAccountId,
            Name = milestone.Name,
            Description = milestone.Description,
            Amount = milestone.Amount.Amount,
            Currency = milestone.Amount.Currency,
            PayoutPercentage = milestone.PayoutPercentage,
            Status = milestone.Status.ToString(),
            DueDate = milestone.DueDate,
            CompletedAt = milestone.CompletedAt,
            CompletionNotes = milestone.CompletionNotes,
            IsOverdue = milestone.IsOverdue(),
            IsRequired = milestone.IsRequired,
            RequiresApproval = milestone.RequiresApproval,
            ApprovalStatus = milestone.ApprovalStatus.ToString(),
            ApprovedBy = milestone.ApprovedBy,
            ApprovedAt = milestone.ApprovedAt,
            TripLegReference = milestone.TripLegReference,
            OrderReference = milestone.OrderReference,
            CompletionCriteria = milestone.CompletionCriteria.ToList(),
            Documents = milestone.Documents.Select(MapToMilestoneDocumentDto).ToList()
        };
    }

    private MilestoneDocumentDto MapToMilestoneDocumentDto(Domain.Entities.MilestoneDocument document)
    {
        return new MilestoneDocumentDto
        {
            Id = document.Id,
            FileName = document.FileName,
            DocumentType = document.DocumentType,
            FileUrl = document.FileUrl,
            UploadedAt = document.UploadedAt,
            UploadedBy = document.UploadedBy,
            UploadedByName = "User Name", // Would need to fetch from external service
            IsRequired = document.IsRequired,
            IsVerified = document.IsVerified,
            VerificationNotes = document.VerificationNotes
        };
    }

    private EscrowTransactionDto MapToEscrowTransactionDto(Domain.Entities.EscrowTransaction transaction)
    {
        return new EscrowTransactionDto
        {
            Id = transaction.Id,
            EscrowAccountId = transaction.EscrowAccountId,
            TransactionType = transaction.Type.ToString(),
            Amount = transaction.Amount.Amount,
            Currency = transaction.Amount.Currency,
            Description = transaction.Notes ?? string.Empty, // Use Notes as Description
            Status = transaction.Status.ToString(),
            ProcessedAt = transaction.ProcessedAt ?? DateTime.UtcNow,
            ProcessedBy = transaction.ParticipantId ?? Guid.Empty, // Use ParticipantId as ProcessedBy
            ProcessedByName = "User Name", // Would need to fetch from external service
            PaymentMethodId = null, // Not available on EscrowTransaction
            PaymentGatewayTransactionId = transaction.PaymentGatewayTransactionId,
            FailureReason = transaction.FailureReason,
            RelatedMilestoneId = null, // Not available on EscrowTransaction
            RelatedMilestoneName = null, // Not available on EscrowTransaction
            Metadata = new Dictionary<string, object>() // Not available on EscrowTransaction
        };
    }

    private EscrowAccountSummaryDto CalculateSummary(
        Domain.Entities.EscrowAccount escrowAccount,
        List<Domain.Entities.PaymentMilestone> milestones)
    {
        var transactions = escrowAccount.Transactions.ToList();
        var successfulTransactions = transactions.Where(t => t.Status == Domain.Enums.EscrowTransactionStatus.Completed).ToList();
        var failedTransactions = transactions.Where(t => t.Status == Domain.Enums.EscrowTransactionStatus.Failed).ToList();

        return new EscrowAccountSummaryDto
        {
            TotalFunded = transactions
                .Where(t => t.Type == Domain.Enums.EscrowTransactionType.Fund && t.Status == Domain.Enums.EscrowTransactionStatus.Completed)
                .Sum(t => t.Amount.Amount),
            TotalReleased = transactions
                .Where(t => t.Type == Domain.Enums.EscrowTransactionType.Release && t.Status == Domain.Enums.EscrowTransactionStatus.Completed)
                .Sum(t => t.Amount.Amount),
            TotalRefunded = transactions
                .Where(t => t.Type == Domain.Enums.EscrowTransactionType.Refund && t.Status == Domain.Enums.EscrowTransactionStatus.Completed)
                .Sum(t => t.Amount.Amount),
            PendingReleases = milestones
                .Where(m => m.Status == PaymentMilestoneStatus.Completed)
                .Sum(m => m.Amount.Amount),
            AvailableForRelease = escrowAccount.AvailableAmount.Amount,
            TotalTransactions = transactions.Count(),
            SuccessfulTransactions = successfulTransactions.Count(),
            FailedTransactions = failedTransactions.Count(),
            TransactionSuccessRate = transactions.Any() ?
                (decimal)successfulTransactions.Count() / transactions.Count() * 100 : 0,
            LastTransactionDate = transactions.OrderByDescending(t => t.ProcessedAt).FirstOrDefault()?.ProcessedAt,
            LastTransactionType = transactions.OrderByDescending(t => t.ProcessedAt).FirstOrDefault()?.Type.ToString(),
            HasPendingApprovals = milestones.Any(m => m.ApprovalStatus == MilestoneApprovalStatus.PendingApproval),
            PendingApprovalCount = milestones.Count(m => m.ApprovalStatus == MilestoneApprovalStatus.PendingApproval),
            RecentActivity = transactions
                .OrderByDescending(t => t.ProcessedAt)
                .Take(5)
                .Select(t => $"{t.Type}: {t.Amount.Amount} {t.Amount.Currency}")
                .ToList()
        };
    }

    private List<string> DetermineAvailableActions(
        Domain.Entities.EscrowAccount escrowAccount,
        List<Domain.Entities.PaymentMilestone> milestones,
        Guid userId)
    {
        var actions = new List<string>();

        if (escrowAccount.TransportCompanyId == userId)
        {
            if (escrowAccount.Status == Domain.Enums.EscrowStatus.Created)
            {
                actions.Add("Fund");
            }

            if (escrowAccount.AvailableAmount.Amount > 0)
            {
                actions.Add("Release");
                actions.Add("Refund");
            }

            actions.Add("CreateMilestone");
            actions.Add("CreateDispute");
        }

        if (escrowAccount.BrokerId == userId || escrowAccount.CarrierId == userId)
        {
            actions.Add("ViewDetails");
            actions.Add("CreateDispute");
        }

        return actions;
    }
}
