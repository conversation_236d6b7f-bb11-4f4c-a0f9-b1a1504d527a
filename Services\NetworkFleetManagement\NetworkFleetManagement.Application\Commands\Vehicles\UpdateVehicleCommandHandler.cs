using AutoMapper;
using MediatR;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Domain.Repositories;
using NetworkFleetManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Commands.Vehicles;

public class UpdateVehicleCommandHandler : IRequestHandler<UpdateVehicleCommand, VehicleDto>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IMessageBroker _messageBroker;

    public UpdateVehicleCommandHandler(
        IVehicleRepository vehicleRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IMessageBroker messageBroker)
    {
        _vehicleRepository = vehicleRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _messageBroker = messageBroker;
    }

    public async Task<VehicleDto> Handle(UpdateVehicleCommand request, CancellationToken cancellationToken)
    {
        var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
        if (vehicle == null)
        {
            throw new InvalidOperationException($"Vehicle with ID {request.VehicleId} not found");
        }

        var dto = request.VehicleDto;

        // Create updated specifications
        var specifications = new VehicleSpecifications(
            dto.Specifications.LoadCapacityKg,
            dto.Specifications.VolumeCapacityM3,
            dto.Specifications.FuelCapacityLiters,
            dto.Specifications.MaxSpeed,
            dto.Specifications.Length,
            dto.Specifications.Width,
            dto.Specifications.Height,
            dto.Specifications.EngineType,
            dto.Specifications.FuelType);

        // Create updated vehicle
        var updatedVehicle = new Domain.Entities.Vehicle(
            vehicle.CarrierId,
            dto.RegistrationNumber,
            dto.VehicleType,
            dto.Make,
            dto.Model,
            dto.Year,
            dto.Color,
            specifications,
            dto.InsuranceNumber,
            dto.InsuranceExpiryDate,
            dto.FitnessNumber,
            dto.FitnessExpiryDate,
            dto.PermitNumber,
            dto.PermitExpiryDate,
            dto.Notes);

        // Copy the ID and other properties
        typeof(Domain.Entities.Vehicle).GetProperty("Id")?.SetValue(updatedVehicle, vehicle.Id);

        _vehicleRepository.Update(updatedVehicle);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Publish integration event
        await _messageBroker.PublishAsync("vehicle.updated", new
        {
            VehicleId = updatedVehicle.Id,
            CarrierId = updatedVehicle.CarrierId,
            RegistrationNumber = updatedVehicle.RegistrationNumber,
            Make = updatedVehicle.Make,
            Model = updatedVehicle.Model,
            UpdatedAt = updatedVehicle.UpdatedAt
        }, cancellationToken);

        var result = _mapper.Map<VehicleDto>(updatedVehicle);
        return result;
    }
}
