using Shared.Domain.Common;
using AuditCompliance.Domain.Events;

namespace AuditCompliance.Domain.Entities;

/// <summary>
/// Ship<PERSON>'s preferred transport company network based on performance history
/// </summary>
public class PreferredProviderNetwork : BaseEntity
{
    public Guid ShipperId { get; private set; }
    public string ShipperName { get; private set; }
    public Guid TransportCompanyId { get; private set; }
    public string TransportCompanyName { get; private set; }
    public decimal AverageRating { get; private set; }
    public int TotalOrders { get; private set; }
    public int CompletedOrders { get; private set; }
    public decimal CompletionRate { get; private set; }
    public DateTime FirstOrderDate { get; private set; }
    public DateTime LastOrderDate { get; private set; }
    public bool IsActive { get; private set; }
    public string? Notes { get; private set; }
    public int PreferenceRank { get; private set; }

    private PreferredProviderNetwork() 
    {
        ShipperName = string.Empty;
        TransportCompanyName = string.Empty;
    }

    public PreferredProviderNetwork(
        Guid shipperId,
        string shipperName,
        Guid transportCompanyId,
        string transportCompanyName,
        decimal averageRating,
        int totalOrders,
        int completedOrders,
        DateTime firstOrderDate,
        DateTime lastOrderDate,
        string? notes = null,
        int preferenceRank = 1)
    {
        if (string.IsNullOrWhiteSpace(shipperName))
            throw new ArgumentException("Shipper name cannot be empty", nameof(shipperName));
        if (string.IsNullOrWhiteSpace(transportCompanyName))
            throw new ArgumentException("Transport company name cannot be empty", nameof(transportCompanyName));
        if (averageRating < 1 || averageRating > 5)
            throw new ArgumentException("Average rating must be between 1 and 5", nameof(averageRating));
        if (completedOrders > totalOrders)
            throw new ArgumentException("Completed orders cannot exceed total orders");

        ShipperId = shipperId;
        ShipperName = shipperName;
        TransportCompanyId = transportCompanyId;
        TransportCompanyName = transportCompanyName;
        AverageRating = averageRating;
        TotalOrders = totalOrders;
        CompletedOrders = completedOrders;
        CompletionRate = totalOrders > 0 ? (decimal)completedOrders / totalOrders * 100 : 0;
        FirstOrderDate = firstOrderDate;
        LastOrderDate = lastOrderDate;
        IsActive = true;
        Notes = notes;
        PreferenceRank = preferenceRank;

        AddDomainEvent(new PreferredProviderAddedEvent(this));
    }

    public void UpdateStatistics(decimal newAverageRating, int newTotalOrders, int newCompletedOrders, DateTime newLastOrderDate)
    {
        if (newAverageRating < 1 || newAverageRating > 5)
            throw new ArgumentException("Average rating must be between 1 and 5", nameof(newAverageRating));
        if (newCompletedOrders > newTotalOrders)
            throw new ArgumentException("Completed orders cannot exceed total orders");
        if (newTotalOrders < TotalOrders)
            throw new ArgumentException("New total orders cannot be less than current total");

        AverageRating = newAverageRating;
        TotalOrders = newTotalOrders;
        CompletedOrders = newCompletedOrders;
        CompletionRate = newTotalOrders > 0 ? (decimal)newCompletedOrders / newTotalOrders * 100 : 0;
        LastOrderDate = newLastOrderDate;
        SetUpdatedAt();
    }

    public void UpdatePreferenceRank(int newRank)
    {
        if (newRank < 1)
            throw new ArgumentException("Preference rank must be positive", nameof(newRank));

        PreferenceRank = newRank;
        SetUpdatedAt();
    }

    public void UpdateNotes(string notes)
    {
        Notes = notes;
        SetUpdatedAt();
    }

    public void Deactivate(string reason)
    {
        IsActive = false;
        Notes = string.IsNullOrEmpty(Notes) ? $"Deactivated: {reason}" : $"{Notes}\nDeactivated: {reason}";
        SetUpdatedAt();

        AddDomainEvent(new PreferredProviderRemovedEvent(this, reason));
    }

    public void Reactivate()
    {
        IsActive = true;
        SetUpdatedAt();

        AddDomainEvent(new PreferredProviderAddedEvent(this));
    }

    public bool IsHighPerformer()
    {
        return AverageRating >= 4.0m && CompletionRate >= 95m;
    }

    public bool IsReliable()
    {
        return CompletionRate >= 90m && TotalOrders >= 5;
    }

    public TimeSpan GetRelationshipDuration()
    {
        return LastOrderDate - FirstOrderDate;
    }

    public bool IsRecentlyActive(int daysThreshold = 90)
    {
        return (DateTime.UtcNow - LastOrderDate).TotalDays <= daysThreshold;
    }

    private void AddDomainEvent(IDomainEvent domainEvent)
    {
        // Implementation would be in base class or through event dispatcher
    }
}
