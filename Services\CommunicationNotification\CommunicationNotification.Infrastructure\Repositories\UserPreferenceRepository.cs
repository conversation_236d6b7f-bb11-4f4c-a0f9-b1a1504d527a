using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for UserPreference entity
/// </summary>
public class UserPreferenceRepository : IUserPreferenceRepository
{
    private readonly CommunicationDbContext _context;

    public UserPreferenceRepository(CommunicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Get user preference by ID
    /// </summary>
    public async Task<UserPreference?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .FirstOrDefaultAsync(up => up.Id == id, cancellationToken);
    }

    /// <summary>
    /// Get user preference by user ID
    /// </summary>
    public async Task<UserPreference?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .FirstOrDefaultAsync(up => up.UserId == userId, cancellationToken);
    }

    /// <summary>
    /// Get user preferences by role
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetByUserRoleAsync(
        UserRole userRole, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .Where(up => up.UserRole == userRole)
            .OrderBy(up => up.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get online users
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetOnlineUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .Where(up => up.IsOnline)
            .OrderByDescending(up => up.LastSeenAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get users by preferred language
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetByPreferredLanguageAsync(
        Language language, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .Where(up => up.PreferredLanguage == language)
            .OrderBy(up => up.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get users with notification channel enabled
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetUsersWithChannelEnabledAsync(
        NotificationChannel channel, 
        CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .Where(up => 
                (channel == NotificationChannel.Sms && up.NotificationSettings.EnableSms) ||
                (channel == NotificationChannel.Email && up.NotificationSettings.EnableEmail) ||
                (channel == NotificationChannel.Push && up.NotificationSettings.EnablePush) ||
                (channel == NotificationChannel.WhatsApp && up.NotificationSettings.EnableWhatsApp) ||
                (channel == NotificationChannel.Voice && up.NotificationSettings.EnableVoice))
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get users in quiet hours
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetUsersInQuietHoursAsync(
        DateTime checkTime, 
        CancellationToken cancellationToken = default)
    {
        var currentTime = checkTime.TimeOfDay;
        
        return await _context.UserPreferences
            .Where(up => up.NotificationSettings.QuietHoursStart.HasValue && 
                        up.NotificationSettings.QuietHoursEnd.HasValue)
            .ToListAsync(cancellationToken)
            .ContinueWith(task =>
            {
                return task.Result.Where(up =>
                {
                    var start = up.NotificationSettings.QuietHoursStart!.Value;
                    var end = up.NotificationSettings.QuietHoursEnd!.Value;
                    
                    if (start <= end)
                    {
                        return currentTime >= start && currentTime <= end;
                    }
                    else
                    {
                        return currentTime >= start || currentTime <= end;
                    }
                }).ToList();
            }, cancellationToken);
    }

    /// <summary>
    /// Get user preferences statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var totalUsers = await _context.UserPreferences.CountAsync(cancellationToken);
        var onlineUsers = await _context.UserPreferences.CountAsync(up => up.IsOnline, cancellationToken);
        
        var languageStats = await _context.UserPreferences
            .GroupBy(up => up.PreferredLanguage)
            .Select(g => new
            {
                Language = g.Key.ToString(),
                Count = g.Count(),
                Percentage = (g.Count() * 100.0) / totalUsers
            })
            .ToListAsync(cancellationToken);

        var roleStats = await _context.UserPreferences
            .GroupBy(up => up.UserRole)
            .Select(g => new
            {
                Role = g.Key.ToString(),
                Count = g.Count(),
                Percentage = (g.Count() * 100.0) / totalUsers
            })
            .ToListAsync(cancellationToken);

        var channelStats = await _context.UserPreferences
            .GroupBy(up => 1)
            .Select(g => new
            {
                SmsEnabled = g.Count(up => up.NotificationSettings.EnableSms),
                EmailEnabled = g.Count(up => up.NotificationSettings.EnableEmail),
                PushEnabled = g.Count(up => up.NotificationSettings.EnablePush),
                WhatsAppEnabled = g.Count(up => up.NotificationSettings.EnableWhatsApp),
                VoiceEnabled = g.Count(up => up.NotificationSettings.EnableVoice)
            })
            .FirstOrDefaultAsync(cancellationToken);

        return new Dictionary<string, object>
        {
            ["total_users"] = totalUsers,
            ["online_users"] = onlineUsers,
            ["offline_users"] = totalUsers - onlineUsers,
            ["language_distribution"] = languageStats.ToDictionary(ls => ls.Language, ls => new
            {
                count = ls.Count,
                percentage = Math.Round(ls.Percentage, 2)
            }),
            ["role_distribution"] = roleStats.ToDictionary(rs => rs.Role, rs => new
            {
                count = rs.Count,
                percentage = Math.Round(rs.Percentage, 2)
            }),
            ["channel_preferences"] = new
            {
                sms_enabled = channelStats?.SmsEnabled ?? 0,
                email_enabled = channelStats?.EmailEnabled ?? 0,
                push_enabled = channelStats?.PushEnabled ?? 0,
                whatsapp_enabled = channelStats?.WhatsAppEnabled ?? 0,
                voice_enabled = channelStats?.VoiceEnabled ?? 0
            }
        };
    }

    /// <summary>
    /// Add user preference
    /// </summary>
    public async Task AddAsync(UserPreference userPreference, CancellationToken cancellationToken = default)
    {
        await _context.UserPreferences.AddAsync(userPreference, cancellationToken);
    }

    /// <summary>
    /// Update user preference
    /// </summary>
    public void Update(UserPreference userPreference)
    {
        _context.UserPreferences.Update(userPreference);
    }

    /// <summary>
    /// Delete user preference
    /// </summary>
    public void Delete(UserPreference userPreference)
    {
        _context.UserPreferences.Remove(userPreference);
    }

    /// <summary>
    /// Update user online status
    /// </summary>
    public async Task UpdateOnlineStatusAsync(
        Guid userId, 
        bool isOnline, 
        DateTime? lastSeenAt = null, 
        CancellationToken cancellationToken = default)
    {
        var lastSeen = lastSeenAt ?? DateTime.UtcNow;
        
        await _context.UserPreferences
            .Where(up => up.UserId == userId)
            .ExecuteUpdateAsync(up => up
                .SetProperty(x => x.IsOnline, isOnline)
                .SetProperty(x => x.LastSeenAt, lastSeen)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow), 
                cancellationToken);
    }

    /// <summary>
    /// Update notification settings
    /// </summary>
    public async Task UpdateNotificationSettingsAsync(
        Guid userId, 
        Action<Domain.ValueObjects.NotificationSettings> updateAction, 
        CancellationToken cancellationToken = default)
    {
        var userPreference = await GetByUserIdAsync(userId, cancellationToken);
        if (userPreference != null)
        {
            updateAction(userPreference.NotificationSettings);
            userPreference.UpdatedAt = DateTime.UtcNow;
            Update(userPreference);
        }
    }

    /// <summary>
    /// Update contact information
    /// </summary>
    public async Task UpdateContactInfoAsync(
        Guid userId, 
        Action<Domain.ValueObjects.ContactInfo> updateAction, 
        CancellationToken cancellationToken = default)
    {
        var userPreference = await GetByUserIdAsync(userId, cancellationToken);
        if (userPreference != null)
        {
            updateAction(userPreference.ContactInfo);
            userPreference.UpdatedAt = DateTime.UtcNow;
            Update(userPreference);
        }
    }

    /// <summary>
    /// Check if user preference exists
    /// </summary>
    public async Task<bool> ExistsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.UserPreferences
            .AnyAsync(up => up.UserId == userId, cancellationToken);
    }

    /// <summary>
    /// Count user preferences
    /// </summary>
    public async Task<int> CountAsync(
        UserRole? userRole = null, 
        Language? preferredLanguage = null, 
        bool? isOnline = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.UserPreferences.AsQueryable();

        if (userRole.HasValue)
        {
            query = query.Where(up => up.UserRole == userRole.Value);
        }

        if (preferredLanguage.HasValue)
        {
            query = query.Where(up => up.PreferredLanguage == preferredLanguage.Value);
        }

        if (isOnline.HasValue)
        {
            query = query.Where(up => up.IsOnline == isOnline.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Get users for bulk notification
    /// </summary>
    public async Task<IEnumerable<UserPreference>> GetUsersForBulkNotificationAsync(
        UserRole? targetRole = null, 
        Language? preferredLanguage = null, 
        NotificationChannel? requiredChannel = null, 
        bool excludeQuietHours = true, 
        DateTime? checkTime = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.UserPreferences.AsQueryable();

        if (targetRole.HasValue)
        {
            query = query.Where(up => up.UserRole == targetRole.Value);
        }

        if (preferredLanguage.HasValue)
        {
            query = query.Where(up => up.PreferredLanguage == preferredLanguage.Value);
        }

        if (requiredChannel.HasValue)
        {
            query = requiredChannel.Value switch
            {
                NotificationChannel.Sms => query.Where(up => up.NotificationSettings.EnableSms),
                NotificationChannel.Email => query.Where(up => up.NotificationSettings.EnableEmail),
                NotificationChannel.Push => query.Where(up => up.NotificationSettings.EnablePush),
                NotificationChannel.WhatsApp => query.Where(up => up.NotificationSettings.EnableWhatsApp),
                NotificationChannel.Voice => query.Where(up => up.NotificationSettings.EnableVoice),
                _ => query
            };
        }

        var users = await query.ToListAsync(cancellationToken);

        if (excludeQuietHours && checkTime.HasValue)
        {
            var currentTime = checkTime.Value.TimeOfDay;
            users = users.Where(up =>
            {
                if (!up.NotificationSettings.QuietHoursStart.HasValue || 
                    !up.NotificationSettings.QuietHoursEnd.HasValue)
                {
                    return true;
                }

                var start = up.NotificationSettings.QuietHoursStart.Value;
                var end = up.NotificationSettings.QuietHoursEnd.Value;

                if (start <= end)
                {
                    return !(currentTime >= start && currentTime <= end);
                }
                else
                {
                    return !(currentTime >= start || currentTime <= end);
                }
            }).ToList();
        }

        return users;
    }

    /// <summary>
    /// Cleanup inactive user preferences
    /// </summary>
    public async Task<int> CleanupInactiveUsersAsync(
        DateTime inactiveSince, 
        CancellationToken cancellationToken = default)
    {
        var inactiveUsers = await _context.UserPreferences
            .Where(up => up.LastSeenAt.HasValue && up.LastSeenAt < inactiveSince)
            .ToListAsync(cancellationToken);

        if (!inactiveUsers.Any())
        {
            return 0;
        }

        // Mark as offline instead of deleting
        foreach (var user in inactiveUsers)
        {
            user.IsOnline = false;
            user.UpdatedAt = DateTime.UtcNow;
        }

        return inactiveUsers.Count;
    }
}
