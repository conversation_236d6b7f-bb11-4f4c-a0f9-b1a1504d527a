using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetworkFleetManagement.Infrastructure.MessageBus;
using RabbitMQ.Client;

namespace NetworkFleetManagement.Infrastructure.HealthChecks;

public class RabbitMQHealthCheck : IHealthCheck
{
    private readonly RabbitMQSettings _settings;
    private readonly ILogger<RabbitMQHealthCheck> _logger;

    public RabbitMQHealthCheck(IOptions<RabbitMQSettings> settings, ILogger<RabbitMQHealthCheck> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var factory = new ConnectionFactory
            {
                HostName = _settings.HostName,
                Port = _settings.Port,
                UserName = _settings.UserName,
                Password = _settings.Password,
                VirtualHost = _settings.VirtualHost,
                RequestedConnectionTimeout = TimeSpan.FromSeconds(10),
                RequestedHeartbeat = TimeSpan.FromSeconds(10)
            };

            using var connection = factory.CreateConnection();
            using var channel = connection.CreateModel();

            // Test basic operations
            var queueName = $"health-check-{Guid.NewGuid()}";
            channel.QueueDeclare(queue: queueName, durable: false, exclusive: true, autoDelete: true);
            channel.QueueDelete(queue: queueName);

            var data = new Dictionary<string, object>
            {
                ["hostname"] = _settings.HostName,
                ["port"] = _settings.Port,
                ["virtualHost"] = _settings.VirtualHost,
                ["exchange"] = _settings.ExchangeName
            };

            return HealthCheckResult.Healthy("RabbitMQ is healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "RabbitMQ health check failed");
            
            var data = new Dictionary<string, object>
            {
                ["hostname"] = _settings.HostName,
                ["port"] = _settings.Port,
                ["error"] = ex.Message
            };

            return HealthCheckResult.Unhealthy("RabbitMQ is unhealthy", ex, data);
        }
    }
}
