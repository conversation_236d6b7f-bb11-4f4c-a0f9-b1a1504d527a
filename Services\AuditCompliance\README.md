# Audit & Compliance Service

The Audit & Compliance Service is a comprehensive microservice that provides audit logging, compliance reporting, service provider rating, and preferred provider network management for the Transport & Logistics Intelligence (TLI) platform.

## Features

### 🔍 Audit Logging
- **Comprehensive Audit Trail**: Track all system activities with detailed audit logs
- **Event Types**: Support for user management, order/trip events, financial transactions, security events, and more
- **Severity Levels**: Categorize events by severity (Info, Low, Medium, High, Critical)
- **Context Tracking**: Capture IP addresses, user agents, session IDs, and correlation IDs
- **Compliance Flags**: Tag events with relevant compliance standards (GDPR, SOX, etc.)
- **Data Retention**: Configurable retention policies with automatic cleanup
- **Security Events**: Special handling for security-related events

### 📊 Compliance Reporting
- **Automated Reports**: Generate compliance reports for various standards (GDPR, SOX, Data Retention, etc.)
- **Manual Reports**: Create custom compliance reports with specific criteria
- **Violation Detection**: Automatically detect and flag compliance violations
- **Report Status Tracking**: Track report lifecycle from creation to completion
- **Evidence Collection**: Attach evidence and recommendations to compliance findings
- **Multi-Standard Support**: Support for multiple compliance standards and regulations

### ⭐ Service Provider Rating System
- **Overall Ratings**: 5-star rating system for transport companies
- **Category Ratings**: Rate specific aspects (timeliness, communication, service quality, etc.)
- **Review Management**: Submit, flag, and moderate reviews
- **Issue Reporting**: Report and track service issues with priority levels
- **Anonymous Reviews**: Support for anonymous feedback
- **Rating Analytics**: Calculate averages and trends

### 🏆 Preferred Provider Network
- **Network Management**: Maintain preferred transport company networks for shippers
- **Performance Tracking**: Track completion rates, average ratings, and order history
- **Ranking System**: Rank providers by preference with reordering capabilities
- **Statistics Updates**: Automatically update provider statistics based on performance
- **Relationship History**: Track the duration and history of shipper-provider relationships

## Architecture

The service follows Clean Architecture principles with the following layers:

- **Domain Layer**: Core business logic, entities, value objects, and domain events
- **Application Layer**: Use cases, commands, queries, and DTOs using CQRS pattern
- **Infrastructure Layer**: Data persistence, external integrations, and messaging
- **API Layer**: RESTful API controllers with JWT authentication and authorization

## Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM with PostgreSQL/TimescaleDB support
- **MediatR**: CQRS and mediator pattern implementation
- **FluentValidation**: Input validation
- **Serilog**: Structured logging
- **JWT Authentication**: Secure API access
- **RabbitMQ**: Message broker for integration events
- **Docker**: Containerization support
- **xUnit**: Unit and integration testing

## Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL or TimescaleDB
- Docker (optional)
- RabbitMQ (for messaging)

### Setup and Run

1. **Database Setup**:
   ```bash
   # Run the database setup script
   psql -h localhost -p 5432 -U postgres -f database-setup.sql
   ```

2. **Using PowerShell Script**:
   ```powershell
   # Setup database, create migrations, and run service
   .\setup-and-run.ps1 -SetupDatabase -CreateMigration -UpdateDatabase -RunService
   
   # Run tests
   .\setup-and-run.ps1 -RunTests
   
   # Just run the service
   .\setup-and-run.ps1 -RunService
   ```

3. **Manual Setup**:
   ```bash
   # Restore dependencies
   dotnet restore
   
   # Create and apply migrations
   cd AuditCompliance.Infrastructure
   dotnet ef migrations add InitialCreate --startup-project ../AuditCompliance.API
   dotnet ef database update --startup-project ../AuditCompliance.API
   
   # Run the service
   cd ../AuditCompliance.API
   dotnet run
   ```

4. **Using Docker**:
   ```bash
   # Run with Docker Compose
   docker-compose up -d
   ```

### Configuration

Update `appsettings.json` with your configuration:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=AuditComplianceDb;Username=timescale;Password=timescale"
  },
  "Jwt": {
    "Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "TLI-AuditCompliance",
    "Audience": "TLI-Services"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "Username": "guest",
    "Password": "guest"
  }
}
```

## API Endpoints

### Audit Endpoints
- `POST /api/audit/logs` - Create audit log entry
- `GET /api/audit/logs/{id}` - Get audit log by ID
- `POST /api/audit/trail` - Search audit trail
- `GET /api/audit/security-events` - Get security events
- `POST /api/audit/cleanup` - Cleanup expired audit logs

### Compliance Endpoints
- `POST /api/compliance/reports` - Create compliance report
- `GET /api/compliance/reports/{id}` - Get compliance report
- `POST /api/compliance/reports/search` - Search compliance reports
- `GET /api/compliance/summary` - Get compliance summary
- `POST /api/compliance/reports/automated` - Generate automated report

### Rating Endpoints
- `POST /api/rating` - Create service provider rating
- `GET /api/rating/{id}` - Get rating by ID
- `POST /api/rating/search` - Search ratings
- `POST /api/rating/{id}/submit` - Submit rating
- `POST /api/rating/issues` - Report service issue
- `GET /api/rating/transport-company/{id}/summary` - Get company rating summary

### Preferred Provider Endpoints
- `GET /api/preferred-provider` - Get preferred providers
- `POST /api/preferred-provider` - Add preferred provider
- `DELETE /api/preferred-provider/{id}` - Remove preferred provider
- `PUT /api/preferred-provider/reorder` - Reorder preferred providers

## Authentication & Authorization

The service uses JWT-based authentication with role-based authorization:

- **Admin**: Full access to all endpoints
- **Auditor**: Access to audit trails and compliance reports
- **ComplianceOfficer**: Access to compliance management
- **Shipper**: Access to rating and preferred provider management
- **TransportCompany**: Access to view ratings and resolve issues

## Integration Events

The service publishes integration events for:
- Audit log creation
- Compliance report generation and completion
- Service provider rating submission
- Service issue reporting
- Preferred provider network changes

## Testing

Run the test suite:

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration
```

## Monitoring

The service includes:
- Health checks at `/health`
- Structured logging with Serilog
- Performance metrics
- Error tracking

## Contributing

1. Follow Clean Architecture principles
2. Write comprehensive tests
3. Use proper error handling
4. Follow coding standards
5. Update documentation

## License

This project is part of the Transport & Logistics Intelligence platform.
