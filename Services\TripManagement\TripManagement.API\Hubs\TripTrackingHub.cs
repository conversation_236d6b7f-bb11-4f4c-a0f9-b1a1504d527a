using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace TripManagement.API.Hubs;

[Authorize]
public class TripTrackingHub : Hub
{
    private readonly ILogger<TripTrackingHub> _logger;

    public TripTrackingHub(ILogger<TripTrackingHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value;
        
        _logger.LogInformation("User {UserId} with role {UserRole} connected to TripTrackingHub. ConnectionId: {ConnectionId}", 
            userId, userRole, Context.ConnectionId);

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        _logger.LogInformation("User {UserId} disconnected from TripTrackingHub. ConnectionId: {ConnectionId}", 
            userId, Context.ConnectionId);

        if (exception != null)
        {
            _logger.LogError(exception, "User {UserId} disconnected with error", userId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Join a trip tracking group
    /// </summary>
    public async Task JoinTripGroup(string tripId)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var groupName = $"Trip_{tripId}";

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            
            _logger.LogInformation("User {UserId} joined trip group {GroupName}", userId, groupName);
            
            await Clients.Caller.SendAsync("JoinedTripGroup", new { TripId = tripId, Message = "Successfully joined trip tracking" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining trip group for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to join trip tracking");
        }
    }

    /// <summary>
    /// Leave a trip tracking group
    /// </summary>
    public async Task LeaveTripGroup(string tripId)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var groupName = $"Trip_{tripId}";

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            
            _logger.LogInformation("User {UserId} left trip group {GroupName}", userId, groupName);
            
            await Clients.Caller.SendAsync("LeftTripGroup", new { TripId = tripId, Message = "Successfully left trip tracking" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving trip group for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to leave trip tracking");
        }
    }

    /// <summary>
    /// Join multiple trip groups at once
    /// </summary>
    public async Task JoinMultipleTripGroups(List<string> tripIds)
    {
        try
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var joinedGroups = new List<string>();

            foreach (var tripId in tripIds)
            {
                if (Guid.TryParse(tripId, out var parsedTripId))
                {
                    var groupName = $"Trip_{tripId}";
                    await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
                    joinedGroups.Add(tripId);
                }
            }

            _logger.LogInformation("User {UserId} joined {GroupCount} trip groups", userId, joinedGroups.Count);
            
            await Clients.Caller.SendAsync("JoinedMultipleTripGroups", new 
            { 
                TripIds = joinedGroups, 
                Message = $"Successfully joined {joinedGroups.Count} trip tracking groups" 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining multiple trip groups");
            await Clients.Caller.SendAsync("Error", "Failed to join trip tracking groups");
        }
    }

    /// <summary>
    /// Send location update from driver/vehicle
    /// </summary>
    public async Task SendLocationUpdate(string tripId, LocationUpdateData locationData)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = Context.User?.FindFirst(ClaimTypes.Role)?.Value;

            // Validate that only drivers or authorized users can send location updates
            if (userRole != "Driver" && userRole != "Admin" && userRole != "Carrier")
            {
                await Clients.Caller.SendAsync("Error", "Unauthorized to send location updates");
                return;
            }

            var groupName = $"Trip_{tripId}";
            
            // Broadcast to all clients tracking this trip
            await Clients.Group(groupName).SendAsync("LocationUpdated", new
            {
                TripId = tripId,
                Location = locationData,
                UpdatedBy = userId,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation("Location update sent for trip {TripId} by user {UserId}", tripId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending location update for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to send location update");
        }
    }

    /// <summary>
    /// Send ETA update
    /// </summary>
    public async Task SendETAUpdate(string tripId, ETAUpdateData etaData)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var groupName = $"Trip_{tripId}";
            
            // Broadcast to all clients tracking this trip
            await Clients.Group(groupName).SendAsync("ETAUpdated", new
            {
                TripId = tripId,
                ETA = etaData,
                UpdatedBy = userId,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation("ETA update sent for trip {TripId} by user {UserId}", tripId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending ETA update for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to send ETA update");
        }
    }

    /// <summary>
    /// Send milestone alert
    /// </summary>
    public async Task SendMilestoneAlert(string tripId, MilestoneAlertData alertData)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var groupName = $"Trip_{tripId}";
            
            // Broadcast to all clients tracking this trip
            await Clients.Group(groupName).SendAsync("MilestoneAlert", new
            {
                TripId = tripId,
                Alert = alertData,
                Timestamp = DateTime.UtcNow
            });

            _logger.LogInformation("Milestone alert sent for trip {TripId}: {AlertType}", tripId, alertData.AlertType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending milestone alert for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to send milestone alert");
        }
    }

    /// <summary>
    /// Request current status for a trip
    /// </summary>
    public async Task RequestTripStatus(string tripId)
    {
        try
        {
            if (!Guid.TryParse(tripId, out var parsedTripId))
            {
                await Clients.Caller.SendAsync("Error", "Invalid trip ID format");
                return;
            }

            // This would typically query the current trip status from the database
            // For now, acknowledge the request
            await Clients.Caller.SendAsync("TripStatusRequested", new 
            { 
                TripId = tripId, 
                Message = "Trip status request received" 
            });

            _logger.LogInformation("Trip status requested for trip {TripId}", tripId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting trip status for trip {TripId}", tripId);
            await Clients.Caller.SendAsync("Error", "Failed to request trip status");
        }
    }
}

// Data transfer objects for SignalR communication
public class LocationUpdateData
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Altitude { get; set; }
    public double? Accuracy { get; set; }
    public double? Speed { get; set; }
    public double? Heading { get; set; }
    public string Source { get; set; } = "GPS";
}

public class ETAUpdateData
{
    public DateTime EstimatedArrival { get; set; }
    public double ConfidenceScore { get; set; }
    public string CalculationMethod { get; set; } = string.Empty;
    public List<string> InfluencingFactors { get; set; } = new();
}

public class MilestoneAlertData
{
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? StopId { get; set; }
    public double? DelayMinutes { get; set; }
}
