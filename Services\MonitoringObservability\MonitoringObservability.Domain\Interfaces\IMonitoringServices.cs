using MonitoringObservability.Domain.DTOs;
using MonitoringObservability.Domain.Entities;
using MonitoringObservability.Domain.Enums;
using MonitoringObservability.Domain.ValueObjects;

namespace MonitoringObservability.Domain.Interfaces;

public interface IMetricsCollectionService
{
    // Metric recording
    Task RecordMetricAsync(string serviceName, string metricName, double value, string unit, MetricType type, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);
    Task RecordCounterAsync(string serviceName, string metricName, double value = 1, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);
    Task RecordGaugeAsync(string serviceName, string metricName, double value, string unit, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);
    Task RecordTimerAsync(string serviceName, string metricName, TimeSpan duration, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);

    // Performance metrics
    Task RecordPerformanceMetricsAsync(string serviceName, PerformanceMetrics metrics, CancellationToken cancellationToken = default);
    Task RecordResponseTimeAsync(string serviceName, string endpoint, TimeSpan responseTime, int statusCode, CancellationToken cancellationToken = default);
    Task RecordThroughputAsync(string serviceName, string endpoint, int requestCount, TimeSpan period, CancellationToken cancellationToken = default);
    Task RecordErrorRateAsync(string serviceName, string endpoint, double errorRate, CancellationToken cancellationToken = default);

    // System metrics
    Task RecordSystemMetricsAsync(string serviceName, double cpuUsage, double memoryUsage, double diskUsage, CancellationToken cancellationToken = default);
    Task RecordDatabaseMetricsAsync(string serviceName, int connectionCount, double queryTime, int activeTransactions, CancellationToken cancellationToken = default);

    // Business metrics
    Task RecordBusinessMetricAsync(string serviceName, string metricName, double value, string unit, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);

    // Batch operations
    Task RecordMetricsBatchAsync(IEnumerable<(string serviceName, string metricName, double value, string unit, MetricType type, Dictionary<string, string>? tags)> metrics, CancellationToken cancellationToken = default);
}

public interface IAlertingService
{
    // Alert management
    Task<Alert> CreateAlertAsync(string title, string description, AlertSeverity severity, string source, string serviceName, string metricName, double currentValue, AlertThreshold threshold, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);
    Task TriggerAlertAsync(Guid alertId, CancellationToken cancellationToken = default);
    Task AcknowledgeAlertAsync(Guid alertId, Guid userId, string? userName = null, CancellationToken cancellationToken = default);
    Task ResolveAlertAsync(Guid alertId, Guid userId, string? resolutionNotes = null, CancellationToken cancellationToken = default);
    Task CloseAlertAsync(Guid alertId, Guid userId, string? closureNotes = null, CancellationToken cancellationToken = default);

    // Threshold evaluation
    Task EvaluateThresholdsAsync(string serviceName, CancellationToken cancellationToken = default);
    Task EvaluateMetricThresholdAsync(Guid metricId, double currentValue, CancellationToken cancellationToken = default);

    // Alert routing and escalation
    Task AssignAlertAsync(Guid alertId, Guid userId, string userName, CancellationToken cancellationToken = default);
    Task EscalateAlertAsync(Guid alertId, int escalationLevel, string reason, CancellationToken cancellationToken = default);
    Task SuppressAlertAsync(Guid alertId, TimeSpan duration, Guid userId, string reason, CancellationToken cancellationToken = default);

    // Notification management
    Task SendAlertNotificationAsync(Guid alertId, NotificationChannel channel, string recipient, CancellationToken cancellationToken = default);
    Task SendBulkNotificationsAsync(IEnumerable<Guid> alertIds, NotificationChannel channel, IEnumerable<string> recipients, CancellationToken cancellationToken = default);
}

public interface IHealthMonitoringService
{
    // Health check execution
    Task<HealthStatus> ExecuteHealthCheckAsync(Guid healthCheckId, CancellationToken cancellationToken = default);
    Task ExecuteAllHealthChecksAsync(CancellationToken cancellationToken = default);
    Task ExecuteServiceHealthChecksAsync(string serviceName, CancellationToken cancellationToken = default);

    // Health check management
    Task<HealthCheck> CreateHealthCheckAsync(string name, string serviceName, string description, string endpoint, TimeSpan interval, TimeSpan timeout, CancellationToken cancellationToken = default);
    Task UpdateHealthCheckConfigurationAsync(Guid healthCheckId, TimeSpan interval, TimeSpan timeout, int maxRetries, CancellationToken cancellationToken = default);
    Task EnableHealthCheckAsync(Guid healthCheckId, CancellationToken cancellationToken = default);
    Task DisableHealthCheckAsync(Guid healthCheckId, CancellationToken cancellationToken = default);

    // Health status aggregation
    Task<HealthStatus> GetServiceHealthStatusAsync(string serviceName, CancellationToken cancellationToken = default);
    Task<HealthStatus> GetOverallSystemHealthAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, HealthStatus>> GetAllServiceHealthStatusAsync(CancellationToken cancellationToken = default);

    // Health trends and analytics
    Task<IEnumerable<(DateTime timestamp, HealthStatus status)>> GetHealthTrendAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken = default);
    Task<double> GetServiceUptimeAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken = default);
}

public interface IIncidentManagementService
{
    // Incident lifecycle
    Task<Incident> CreateIncidentAsync(string title, string description, IncidentSeverity severity, string serviceName, Guid createdByUserId, string? component = null, CancellationToken cancellationToken = default);
    Task UpdateIncidentStatusAsync(Guid incidentId, IncidentStatus newStatus, string updateMessage, Guid updatedByUserId, CancellationToken cancellationToken = default);
    Task AssignIncidentAsync(Guid incidentId, Guid userId, string userName, Guid assignedByUserId, CancellationToken cancellationToken = default);
    Task ResolveIncidentAsync(Guid incidentId, string resolutionSummary, string? rootCause, string? preventiveMeasures, Guid resolvedByUserId, CancellationToken cancellationToken = default);

    // Incident correlation
    Task LinkAlertToIncidentAsync(Guid incidentId, Guid alertId, CancellationToken cancellationToken = default);
    Task UnlinkAlertFromIncidentAsync(Guid incidentId, Guid alertId, CancellationToken cancellationToken = default);
    Task<Incident?> FindRelatedIncidentAsync(Alert alert, CancellationToken cancellationToken = default);

    // Auto-incident creation
    Task<Incident?> CreateIncidentFromAlertAsync(Guid alertId, CancellationToken cancellationToken = default);
    Task EvaluateIncidentCreationRulesAsync(Alert alert, CancellationToken cancellationToken = default);

    // Incident communication
    Task AddIncidentCommentAsync(Guid incidentId, Guid userId, string userName, string comment, bool isInternal = false, CancellationToken cancellationToken = default);
    Task SendIncidentNotificationAsync(Guid incidentId, NotificationChannel channel, string recipient, string message, CancellationToken cancellationToken = default);
}

public interface INotificationService
{
    // Notification sending
    Task SendEmailNotificationAsync(string recipient, string subject, string body, CancellationToken cancellationToken = default);
    Task SendSMSNotificationAsync(string phoneNumber, string message, CancellationToken cancellationToken = default);
    Task SendSlackNotificationAsync(string channel, string message, CancellationToken cancellationToken = default);
    Task SendTeamsNotificationAsync(string channel, string message, CancellationToken cancellationToken = default);
    Task SendWebhookNotificationAsync(string webhookUrl, object payload, CancellationToken cancellationToken = default);

    // Notification templates
    Task<string> RenderAlertNotificationAsync(Alert alert, NotificationChannel channel, CancellationToken cancellationToken = default);
    Task<string> RenderIncidentNotificationAsync(Incident incident, NotificationChannel channel, CancellationToken cancellationToken = default);
    Task<string> RenderHealthCheckNotificationAsync(HealthCheck healthCheck, NotificationChannel channel, CancellationToken cancellationToken = default);

    // Notification preferences
    Task<IEnumerable<NotificationChannel>> GetUserNotificationPreferencesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task UpdateUserNotificationPreferencesAsync(Guid userId, IEnumerable<NotificationChannel> channels, CancellationToken cancellationToken = default);

    // Notification history
    Task RecordNotificationSentAsync(string recipient, NotificationChannel channel, string message, bool success, string? errorMessage = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<(DateTime timestamp, NotificationChannel channel, string recipient, bool success)>> GetNotificationHistoryAsync(TimeSpan period, CancellationToken cancellationToken = default);
}

public interface ILogAggregationService
{
    // Log ingestion
    Task IngestLogAsync(string serviceName, LogLevel level, string message, DateTime timestamp, Dictionary<string, object>? properties = null, string? exception = null, CancellationToken cancellationToken = default);
    Task IngestLogsBatchAsync(IEnumerable<(string serviceName, LogLevel level, string message, DateTime timestamp, Dictionary<string, object>? properties, string? exception)> logs, CancellationToken cancellationToken = default);

    // Log querying
    Task<IEnumerable<object>> SearchLogsAsync(string? serviceName = null, LogLevel? minLevel = null, DateTime? fromDate = null, DateTime? toDate = null, string? searchText = null, int skip = 0, int take = 100, CancellationToken cancellationToken = default);
    Task<IEnumerable<object>> GetLogsForIncidentAsync(Guid incidentId, TimeSpan timeWindow, CancellationToken cancellationToken = default);
    Task<IEnumerable<object>> GetErrorLogsAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken = default);

    // Log analytics
    Task<Dictionary<LogLevel, int>> GetLogCountByLevelAsync(string serviceName, TimeSpan period, CancellationToken cancellationToken = default);
    Task<IEnumerable<(string serviceName, int errorCount)>> GetTopErrorServicesAsync(TimeSpan period, int topCount = 10, CancellationToken cancellationToken = default);

    // Log retention
    Task CleanupOldLogsAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
}

public interface IDistributedTracingService
{
    // Trace recording
    Task StartTraceAsync(string traceId, string operationName, string serviceName, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);
    Task EndTraceAsync(string traceId, TraceStatus status, TimeSpan duration, string? errorMessage = null, CancellationToken cancellationToken = default);
    Task RecordSpanAsync(string traceId, string spanId, string operationName, string serviceName, TimeSpan duration, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default);

    // Trace querying
    Task<object?> GetTraceAsync(string traceId, CancellationToken cancellationToken = default);
    Task<IEnumerable<object>> SearchTracesAsync(string? serviceName = null, string? operationName = null, DateTime? fromDate = null, DateTime? toDate = null, TimeSpan? minDuration = null, TraceStatus? status = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<object>> GetSlowTracesAsync(string serviceName, TimeSpan threshold, TimeSpan period, CancellationToken cancellationToken = default);

    // Trace analytics
    Task<double> GetAverageTraceTimeAsync(string serviceName, string operationName, TimeSpan period, CancellationToken cancellationToken = default);
    Task<IEnumerable<(string operationName, double averageTime)>> GetSlowestOperationsAsync(string serviceName, TimeSpan period, int topCount = 10, CancellationToken cancellationToken = default);
}

public interface IAdvancedAlertingService
{
    // Initialization and configuration
    Task InitializeAsync(CancellationToken cancellationToken = default);

    // Rule evaluation and processing
    Task EvaluateRulesAsync(Dictionary<string, object> context, CancellationToken cancellationToken = default);
    Task ProcessAlertAsync(Alert alert, CancellationToken cancellationToken = default);

    // Alert correlation
    Task<AlertCorrelationGroup?> CorrelateAlertsAsync(List<Alert> alerts, CancellationToken cancellationToken = default);

    // Alert management
    Task EscalateAlertAsync(Alert alert, int escalationLevel, string reason, CancellationToken cancellationToken = default);
    Task SuppressAlertsAsync(List<Alert> alerts, TimeSpan duration, string reason, Guid userId, CancellationToken cancellationToken = default);

    // Maintenance operations
    Task CleanupExpiredGroupsAsync(CancellationToken cancellationToken = default);
}

public interface IServiceDependencyMappingService
{
    // Dependency graph generation
    Task<ServiceDependencyGraphDto> GenerateDependencyGraphAsync(bool includeHealthStatus = true, TimeSpan? period = null, CancellationToken cancellationToken = default);

    // Impact analysis
    Task<List<ServiceImpactAnalysisDto>> AnalyzeServiceImpactAsync(string serviceName, CancellationToken cancellationToken = default);

    // Topology mapping
    Task<ServiceTopologyDto> GenerateServiceTopologyAsync(string? rootService = null, int maxDepth = 5, CancellationToken cancellationToken = default);

    // Critical path analysis
    Task<List<ServiceDependencyDto>> GetCriticalPathAsync(string fromService, string toService, CancellationToken cancellationToken = default);

    // Dependency refresh
    Task RefreshDependenciesFromTracesAsync(TimeSpan period, CancellationToken cancellationToken = default);
}

public interface IAnomalyDetectionService
{
    // Initialization
    Task InitializeAsync(CancellationToken cancellationToken = default);

    // Model management
    Task<AnomalyDetectionModel> CreateModelAsync(string name, string serviceName, string metricName, AnomalyDetectionAlgorithm algorithm, Dictionary<string, object>? parameters = null, CancellationToken cancellationToken = default);
    Task<bool> TrainModelAsync(Guid modelId, TimeSpan? trainingPeriod = null, CancellationToken cancellationToken = default);

    // Anomaly detection
    Task<List<AnomalyDetectionResult>> DetectAnomaliesAsync(Dictionary<string, double> metrics, DateTime timestamp, CancellationToken cancellationToken = default);
    Task<List<AnomalyDetectionResult>> GetAnomaliesAsync(string? serviceName = null, string? metricName = null, TimeSpan? period = null, CancellationToken cancellationToken = default);

    // Statistics and feedback
    Task<AnomalyStatisticsDto> GetAnomalyStatisticsAsync(string? serviceName = null, TimeSpan? period = null, CancellationToken cancellationToken = default);
    Task ProvideUserFeedbackAsync(Guid resultId, bool isAnomaly, string? feedback = null, CancellationToken cancellationToken = default);

    // Maintenance
    Task RetrainModelsAsync(CancellationToken cancellationToken = default);
}

public interface ICustomDashboardService
{
    // Dashboard management
    Task<CustomDashboard> CreateDashboardAsync(string name, string description, Guid ownerId, DashboardVisibility visibility = DashboardVisibility.Private, CancellationToken cancellationToken = default);
    Task<CustomDashboard?> GetDashboardAsync(Guid dashboardId, Guid? userId = null, string[]? userRoles = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<CustomDashboard>> GetUserDashboardsAsync(Guid userId, string[] userRoles, CancellationToken cancellationToken = default);
    Task<CustomDashboard> UpdateDashboardAsync(Guid dashboardId, string? name = null, string? description = null, DashboardVisibility? visibility = null, TimeSpan? refreshInterval = null, CancellationToken cancellationToken = default);

    // Widget management
    Task<DashboardWidget> AddWidgetAsync(Guid dashboardId, string title, WidgetType type, WidgetPosition position, WidgetSize size, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
    Task UpdateWidgetAsync(Guid dashboardId, Guid widgetId, string? title = null, WidgetPosition? position = null, WidgetSize? size = null, Dictionary<string, object>? configuration = null, CancellationToken cancellationToken = default);
    Task RemoveWidgetAsync(Guid dashboardId, Guid widgetId, CancellationToken cancellationToken = default);
    Task<object> GetWidgetDataAsync(Guid widgetId, Dictionary<string, object>? filters = null, CancellationToken cancellationToken = default);

    // Dashboard sharing and export
    Task<DashboardExportDto> ExportDashboardAsync(Guid dashboardId, ExportFormat format, CancellationToken cancellationToken = default);
    Task<CustomDashboard> ImportDashboardAsync(DashboardImportDto importData, Guid ownerId, CancellationToken cancellationToken = default);
    Task ShareDashboardAsync(Guid dashboardId, Guid? userId, string? roleName, DashboardAccessLevel accessLevel, CancellationToken cancellationToken = default);

    // Analytics
    Task<DashboardAnalyticsDto> GetDashboardAnalyticsAsync(Guid dashboardId, TimeSpan period, CancellationToken cancellationToken = default);
}

public interface ISLAMonitoringService
{
    // Initialization
    Task InitializeAsync(CancellationToken cancellationToken = default);

    // SLA management
    Task<ServiceLevelAgreement> CreateSLAAsync(string name, string description, string serviceName, DateTime startDate, DateTime endDate, Guid ownerId, CancellationToken cancellationToken = default);
    Task<SLAObjective> AddObjectiveAsync(Guid slaId, string name, string description, SLAObjectiveType type, string metricName, double targetValue, SLAComparisonOperator comparisonOperator, TimeSpan evaluationWindow, double weight = 1.0, CancellationToken cancellationToken = default);

    // SLA evaluation and monitoring
    Task EvaluateAllSLAsAsync(CancellationToken cancellationToken = default);
    Task<SLAComplianceDto> GetSLAComplianceAsync(Guid slaId, TimeSpan? period = null, CancellationToken cancellationToken = default);
    Task<SLAReport> GenerateReportAsync(Guid slaId, SLAReportType reportType, DateTime? periodStart = null, DateTime? periodEnd = null, CancellationToken cancellationToken = default);

    // Breach management
    Task<SLABreach> DetectBreachAsync(Guid slaId, Guid objectiveId, double actualValue, CancellationToken cancellationToken = default);
    Task ResolveBreach(Guid breachId, Guid resolvedBy, string? resolutionNotes = null, CancellationToken cancellationToken = default);

    // Dashboard and analytics
    Task<SLADashboardDto> GetSLADashboardAsync(string? serviceName = null, CancellationToken cancellationToken = default);
}

public interface ICapacityPlanningService
{
    // Initialization
    Task InitializeAsync(CancellationToken cancellationToken = default);

    // Capacity plan management
    Task<CapacityPlan> CreateCapacityPlanAsync(string name, string description, string serviceName, CapacityPlanType planType, TimeSpan forecastHorizon, Guid ownerId, CancellationToken cancellationToken = default);

    // Forecasting and analysis
    Task<List<ResourceForecast>> GenerateForecastsAsync(Guid planId, CancellationToken cancellationToken = default);
    Task<List<ScalingRecommendation>> GenerateScalingRecommendationsAsync(Guid planId, CancellationToken cancellationToken = default);
    Task<CapacityAnalysisDto> AnalyzeCapacityAsync(string serviceName, TimeSpan analysisWindow, CancellationToken cancellationToken = default);

    // Alerts and monitoring
    Task<Entities.CapacityAlert> CreateCapacityAlertAsync(Guid planId, ResourceType resourceType, string resourceName, CapacityAlertType alertType, double threshold, double currentValue, CapacityAlertSeverity severity, string message, CancellationToken cancellationToken = default);

    // Dashboard and execution
    Task<CapacityDashboardDto> GetCapacityDashboardAsync(string? serviceName = null, CancellationToken cancellationToken = default);
    Task ExecuteAllPlansAsync(CancellationToken cancellationToken = default);
}
