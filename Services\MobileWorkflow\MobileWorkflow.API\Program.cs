using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using MobileWorkflow.Infrastructure.Data;
using MobileWorkflow.Application.Mappings;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Repositories;
using MobileWorkflow.Infrastructure.Services;
using MobileWorkflow.Infrastructure.Integration;
using MobileWorkflow.Application.Services;
using MobileWorkflow.API.Configuration;
using FluentValidation;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/mobileworkflow-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger/OpenAPI
builder.Services.AddSwaggerDocumentation();

// Configure Database
builder.Services.AddDbContext<MobileWorkflowDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? ""))
        };
    });

builder.Services.AddAuthorization();

// Configure MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(MobileWorkflowMappingProfile).Assembly));

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(MobileWorkflowMappingProfile));

// Configure FluentValidation
builder.Services.AddValidatorsFromAssembly(typeof(MobileWorkflowMappingProfile).Assembly);
builder.Services.AddFluentValidationAutoValidation();

// Register Repositories
builder.Services.AddScoped<IMobileAppRepository, MobileAppRepository>();
builder.Services.AddScoped<IMobileSessionRepository, MobileSessionRepository>();
builder.Services.AddScoped<IOfflineDataRepository, OfflineDataRepository>();
builder.Services.AddScoped<IWorkflowRepository, WorkflowRepository>();
builder.Services.AddScoped<IWorkflowExecutionRepository, WorkflowExecutionRepository>();
builder.Services.AddScoped<IWorkflowTaskRepository, WorkflowTaskRepository>();

// Register Milestone Repositories
builder.Services.AddScoped<IMilestoneTemplateRepository, MilestoneTemplateRepository>();
builder.Services.AddScoped<IMilestoneStepRepository, MilestoneStepRepository>();
builder.Services.AddScoped<IMilestonePayoutRuleRepository, MilestonePayoutRuleRepository>();
builder.Services.AddScoped<IRoleTemplateMappingsRepository, RoleTemplateMappingsRepository>();

// Register Services
builder.Services.AddScoped<ICrossPlatformSyncService, CrossPlatformSyncService>();
builder.Services.AddScoped<IProgressiveWebAppService, ProgressiveWebAppService>();
builder.Services.AddScoped<IDriverMobileAppService, DriverMobileAppService>();
builder.Services.AddScoped<IWorkflowExecutionEngine, WorkflowExecutionEngine>();
builder.Services.AddScoped<IBusinessProcessAutomationService, BusinessProcessAutomationService>();

// Register Milestone Services
builder.Services.AddScoped<IMilestoneTemplateService, MilestoneTemplateService>();
builder.Services.AddScoped<IMilestonePayoutService, MilestonePayoutService>();
builder.Services.AddScoped<ITemplateAssignmentService, TemplateAssignmentService>();
builder.Services.AddScoped<IMilestoneValidationService, MilestoneValidationService>();
builder.Services.AddScoped<IMilestoneStepService, MilestoneStepService>();

// Register new sync services
builder.Services.AddScoped<IAdvancedSyncService, AdvancedSyncService>();
builder.Services.AddScoped<ISyncOperationRepository, SyncOperationRepository>();
builder.Services.AddScoped<ISyncItemRepository, SyncItemRepository>();
builder.Services.AddScoped<IConflictResolutionRepository, ConflictResolutionRepository>();

// Register Integration Services
builder.Services.AddHttpClient<IServiceIntegrationHub, ServiceIntegrationHub>();
builder.Services.AddScoped<IServiceIntegrationHub, ServiceIntegrationHub>();

// Configure Memory Cache
builder.Services.AddMemoryCache();

// Configure Health Checks
builder.Services.AddHealthChecks()
    .AddDbContext<MobileWorkflowDbContext>()
    .AddNpgSql(builder.Configuration.GetConnectionString("DefaultConnection") ?? "")
    .AddCheck("self", () => HealthCheckResult.Healthy());

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwaggerDocumentation(app.Environment);
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Configure Health Check endpoints
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});
app.MapHealthChecks("/health/live", new HealthCheckOptions
{
    Predicate = _ => false
});

// Add PWA endpoints
app.MapGet("/manifest.json", async (IProgressiveWebAppService pwaService) =>
{
    var manifest = await pwaService.GenerateManifestAsync("web", "1.0.0");
    return Results.Json(manifest);
});

app.MapGet("/service-worker.js", () =>
{
    var serviceWorkerContent = @"
const CACHE_NAME = 'tli-mobile-v1';
const urlsToCache = [
  '/',
  '/offline.html',
  '/css/app.css',
  '/js/app.js'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});

self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // Implement background sync logic
  return Promise.resolve();
}
";

    return Results.Text(serviceWorkerContent, "application/javascript");
});

// Database Migration and Seeding
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<MobileWorkflowDbContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<MilestoneDataSeeder>>();

    try
    {
        // Ensure database is created and migrated
        await context.Database.EnsureCreatedAsync();

        // Seed milestone data
        var seeder = new MilestoneDataSeeder(context, logger);
        await seeder.SeedAsync();

        // Seed development data in development environment
        if (app.Environment.IsDevelopment())
        {
            await seeder.SeedDevelopmentDataAsync();
        }

        Log.Information("Database migration and seeding completed successfully");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "An error occurred while migrating the database");
        throw;
    }
}

Log.Information("Mobile & Workflow Service starting up...");

try
{
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
