using MonitoringObservability.Domain.Enums;

namespace MonitoringObservability.Domain.DTOs;

/// <summary>
/// DTO for dashboard import
/// </summary>
public class DashboardImportDto
{
    public string DashboardName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ExportFormat SourceFormat { get; set; }
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public bool OverwriteExisting { get; set; }
    public string ImportedBy { get; set; } = string.Empty;
    public Dictionary<string, object> ImportOptions { get; set; } = new();
    public List<DashboardWidgetImportDto> Widgets { get; set; } = new();
}

public class DashboardWidgetImportDto
{
    public string WidgetName { get; set; } = string.Empty;
    public string WidgetType { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public int Position { get; set; }
    public Dictionary<string, object> Layout { get; set; } = new();
}
