using NetworkFleetManagement.Domain.Entities;
using NetworkFleetManagement.Domain.Enums;

namespace NetworkFleetManagement.Domain.Repositories;

public interface ICarrierRepository
{
    Task<Carrier?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Carrier?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> GetByStatusAsync(CarrierStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> GetByOnboardingStatusAsync(OnboardingStatus status, CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> GetActiveCarriersAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> GetCarriersWithExpiredDocumentsAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Carrier>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    Task<(IEnumerable<Carrier> Carriers, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? searchTerm = null,
        CarrierStatus? status = null,
        CancellationToken cancellationToken = default);
    
    void Add(Carrier carrier);
    void Update(Carrier carrier);
    void Remove(Carrier carrier);
}
