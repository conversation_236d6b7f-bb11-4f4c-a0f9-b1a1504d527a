apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: timescaledb
  namespace: tli-analytics
  labels:
    app: timescaledb
    service: analytics-bi
    component: database
spec:
  serviceName: timescaledb-service
  replicas: 1
  selector:
    matchLabels:
      app: timescaledb
  template:
    metadata:
      labels:
        app: timescaledb
        service: analytics-bi
        component: database
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9187"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: timescaledb
        image: timescale/timescaledb:latest-pg15
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
          name: postgres
          protocol: TCP
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: database-name
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: analytics-config
              key: database-user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: database-password
        - name: POSTGRES_INITDB_ARGS
          value: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
        - name: TIMESCALEDB_TELEMETRY
          value: "off"
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
            storage: 20Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
          readOnly: true
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
          readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 999
          capabilities:
            drop:
            - ALL
            add:
            - CHOWN
            - DAC_OVERRIDE
            - FOWNER
            - SETGID
            - SETUID
      - name: postgres-exporter
        image: prometheuscommunity/postgres-exporter:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9187
          name: metrics
          protocol: TCP
        env:
        - name: DATA_SOURCE_NAME
          valueFrom:
            secretKeyRef:
              name: analytics-secrets
              key: database-connection-string
        - name: PG_EXPORTER_EXTEND_QUERY_PATH
          value: "/etc/postgres_exporter/queries.yaml"
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9187
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9187
          initialDelaySeconds: 15
          periodSeconds: 10
        volumeMounts:
        - name: postgres-exporter-config
          mountPath: /etc/postgres_exporter
          readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 65534
          capabilities:
            drop:
            - ALL
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
      - name: init-scripts
        configMap:
          name: postgres-init-scripts
      - name: postgres-exporter-config
        configMap:
          name: postgres-exporter-config
      restartPolicy: Always
      terminationGracePeriodSeconds: 60
      dnsPolicy: ClusterFirst
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
      labels:
        app: timescaledb
        service: analytics-bi
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi
---
apiVersion: v1
kind: Service
metadata:
  name: timescaledb-service
  namespace: tli-analytics
  labels:
    app: timescaledb
    service: analytics-bi
    component: database
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9187"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  - port: 9187
    targetPort: 9187
    protocol: TCP
    name: metrics
  selector:
    app: timescaledb
  sessionAffinity: None
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: tli-analytics
  labels:
    app: timescaledb
    service: analytics-bi
data:
  postgresql.conf: |
    # PostgreSQL configuration for Analytics & BI Service Production
    # Optimized for TimescaleDB and analytics workloads
    
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    shared_buffers = 512MB
    effective_cache_size = 1536MB
    maintenance_work_mem = 128MB
    checkpoint_completion_target = 0.9
    wal_buffers = 32MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 8MB
    min_wal_size = 512MB
    max_wal_size = 2GB
    max_worker_processes = 8
    max_parallel_workers_per_gather = 4
    max_parallel_workers = 8
    max_parallel_maintenance_workers = 4
    
    # TimescaleDB specific settings
    shared_preload_libraries = 'timescaledb'
    timescaledb.max_background_workers = 8
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_min_duration_statement = 1000
    log_checkpoints = on
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    log_statement = 'ddl'
    log_timezone = 'UTC'
    
    # Autovacuum
    autovacuum = on
    autovacuum_vacuum_scale_factor = 0.1
    autovacuum_analyze_scale_factor = 0.05
    
    # Security
    password_encryption = scram-sha-256
    ssl = on
    ssl_cert_file = '/var/lib/postgresql/server.crt'
    ssl_key_file = '/var/lib/postgresql/server.key'
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
  namespace: tli-analytics
  labels:
    app: timescaledb
    service: analytics-bi
data:
  01-init-timescaledb.sql: |
    -- Initialize TimescaleDB extension
    CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
    
    -- Create analytics database schema
    CREATE SCHEMA IF NOT EXISTS analytics;
    CREATE SCHEMA IF NOT EXISTS metrics;
    CREATE SCHEMA IF NOT EXISTS events;
    
    -- Set default search path
    ALTER DATABASE tli_analytics_prod SET search_path TO analytics, metrics, events, public;
  
  02-init-analytics-db.sql: |
    -- Analytics & BI Service Database Initialization
    -- This script sets up the initial database structure for the Analytics & BI Service
    
    \c tli_analytics_prod;
    
    -- Enable required extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    
    -- Create custom types
    CREATE TYPE user_role AS ENUM ('Admin', 'TransportCompany', 'Broker', 'Carrier', 'Shipper');
    CREATE TYPE analytics_event_type AS ENUM ('UserAction', 'BusinessTransaction', 'SystemEvent', 'PerformanceMetric');
    CREATE TYPE kpi_category AS ENUM ('Business', 'Financial', 'Performance', 'Operational', 'Customer');
    CREATE TYPE alert_type AS ENUM ('Performance', 'Business', 'System', 'Security');
    CREATE TYPE alert_severity AS ENUM ('Info', 'Warning', 'Critical');
    
    -- Grant permissions
    GRANT USAGE ON SCHEMA analytics TO timescale;
    GRANT USAGE ON SCHEMA metrics TO timescale;
    GRANT USAGE ON SCHEMA events TO timescale;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA analytics TO timescale;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA metrics TO timescale;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA events TO timescale;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA analytics TO timescale;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA metrics TO timescale;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA events TO timescale;
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-exporter-config
  namespace: tli-analytics
  labels:
    app: timescaledb
    service: analytics-bi
data:
  queries.yaml: |
    pg_replication:
      query: "SELECT CASE WHEN NOT pg_is_in_recovery() THEN 0 ELSE GREATEST (0, EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()))) END AS lag"
      master: true
      metrics:
        - lag:
            usage: "GAUGE"
            description: "Replication lag behind master in seconds"
    
    pg_postmaster:
      query: "SELECT pg_postmaster_start_time as start_time_seconds from pg_postmaster_start_time()"
      master: true
      metrics:
        - start_time_seconds:
            usage: "GAUGE"
            description: "Time at which postmaster started"
    
    pg_stat_user_tables:
      query: |
        SELECT
          current_database() datname,
          schemaname,
          relname,
          seq_scan,
          seq_tup_read,
          idx_scan,
          idx_tup_fetch,
          n_tup_ins,
          n_tup_upd,
          n_tup_del,
          n_tup_hot_upd,
          n_live_tup,
          n_dead_tup,
          n_mod_since_analyze,
          COALESCE(last_vacuum, '1970-01-01Z') as last_vacuum,
          COALESCE(last_autovacuum, '1970-01-01Z') as last_autovacuum,
          COALESCE(last_analyze, '1970-01-01Z') as last_analyze,
          COALESCE(last_autoanalyze, '1970-01-01Z') as last_autoanalyze,
          vacuum_count,
          autovacuum_count,
          analyze_count,
          autoanalyze_count
        FROM pg_stat_user_tables
      metrics:
        - datname:
            usage: "LABEL"
            description: "Name of current database"
        - schemaname:
            usage: "LABEL"
            description: "Name of the schema that this table is in"
        - relname:
            usage: "LABEL"
            description: "Name of this table"
        - seq_scan:
            usage: "COUNTER"
            description: "Number of sequential scans initiated on this table"
        - seq_tup_read:
            usage: "COUNTER"
            description: "Number of live rows fetched by sequential scans"
        - idx_scan:
            usage: "COUNTER"
            description: "Number of index scans initiated on this table"
        - idx_tup_fetch:
            usage: "COUNTER"
            description: "Number of live rows fetched by index scans"
        - n_tup_ins:
            usage: "COUNTER"
            description: "Number of rows inserted"
        - n_tup_upd:
            usage: "COUNTER"
            description: "Number of rows updated"
        - n_tup_del:
            usage: "COUNTER"
            description: "Number of rows deleted"
        - n_tup_hot_upd:
            usage: "COUNTER"
            description: "Number of rows HOT updated"
        - n_live_tup:
            usage: "GAUGE"
            description: "Estimated number of live rows"
        - n_dead_tup:
            usage: "GAUGE"
            description: "Estimated number of dead rows"
        - n_mod_since_analyze:
            usage: "GAUGE"
            description: "Estimated number of rows changed since last analyze"
        - last_vacuum:
            usage: "GAUGE"
            description: "Last time at which this table was manually vacuumed"
        - last_autovacuum:
            usage: "GAUGE"
            description: "Last time at which this table was vacuumed by the autovacuum daemon"
        - last_analyze:
            usage: "GAUGE"
            description: "Last time at which this table was manually analyzed"
        - last_autoanalyze:
            usage: "GAUGE"
            description: "Last time at which this table was analyzed by the autovacuum daemon"
        - vacuum_count:
            usage: "COUNTER"
            description: "Number of times this table has been manually vacuumed"
        - autovacuum_count:
            usage: "COUNTER"
            description: "Number of times this table has been vacuumed by the autovacuum daemon"
        - analyze_count:
            usage: "COUNTER"
            description: "Number of times this table has been manually analyzed"
        - autoanalyze_count:
            usage: "COUNTER"
            description: "Number of times this table has been analyzed by the autovacuum daemon"
