using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace CommunicationNotification.Infrastructure.Controllers;

/// <summary>
/// Advanced message performance analytics API controller
/// </summary>
[ApiController]
[Route("api/communication/analytics")]
[Authorize]
public class MessagePerformanceAnalyticsController : ControllerBase
{
    private readonly IMessagePerformanceAnalyticsService _analyticsService;
    private readonly IConversionTrackingService _conversionTrackingService;
    private readonly IROIAnalyticsService _roiAnalyticsService;
    private readonly ISegmentationAnalyticsService _segmentationAnalyticsService;

    public MessagePerformanceAnalyticsController(
        IMessagePerformanceAnalyticsService analyticsService,
        IConversionTrackingService conversionTrackingService,
        IROIAnalyticsService roiAnalyticsService,
        ISegmentationAnalyticsService segmentationAnalyticsService)
    {
        _analyticsService = analyticsService;
        _conversionTrackingService = conversionTrackingService;
        _roiAnalyticsService = roiAnalyticsService;
        _segmentationAnalyticsService = segmentationAnalyticsService;
    }

    /// <summary>
    /// Generate comprehensive performance report
    /// </summary>
    [HttpPost("reports")]
    public async Task<IActionResult> GeneratePerformanceReport([FromBody] GenerateReportRequest request)
    {
        var userId = GetCurrentUserId();

        var report = await _analyticsService.GeneratePerformanceReportAsync(
            request.Name,
            request.Description,
            request.Type,
            request.StartDate,
            request.EndDate,
            request.Filters,
            request.Configuration,
            userId);

        return Ok(report);
    }

    /// <summary>
    /// Get performance report by ID
    /// </summary>
    [HttpGet("reports/{reportId}")]
    public async Task<IActionResult> GetPerformanceReport(Guid reportId)
    {
        var report = await _analyticsService.GetPerformanceReportAsync(reportId);
        if (report == null)
        {
            return NotFound($"Report {reportId} not found");
        }

        return Ok(report);
    }

    /// <summary>
    /// Get all performance reports
    /// </summary>
    [HttpGet("reports")]
    public async Task<IActionResult> GetAllPerformanceReports()
    {
        var userId = GetCurrentUserId();
        var reports = await _analyticsService.GetAllPerformanceReportsAsync(userId);
        return Ok(reports);
    }

    /// <summary>
    /// Delete performance report
    /// </summary>
    [HttpDelete("reports/{reportId}")]
    public async Task<IActionResult> DeletePerformanceReport(Guid reportId)
    {
        await _analyticsService.DeletePerformanceReportAsync(reportId);
        return NoContent();
    }

    /// <summary>
    /// Get advanced performance metrics
    /// </summary>
    [HttpGet("metrics/performance")]
    public async Task<IActionResult> GetAdvancedPerformanceMetrics(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var metrics = await _analyticsService.GetAdvancedPerformanceMetricsAsync(startDate, endDate, filterList);
        return Ok(metrics);
    }

    /// <summary>
    /// Get conversion analytics
    /// </summary>
    [HttpGet("metrics/conversion")]
    public async Task<IActionResult> GetConversionAnalytics(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var analytics = await _analyticsService.GetConversionAnalyticsAsync(startDate, endDate, filterList);
        return Ok(analytics);
    }

    /// <summary>
    /// Get ROI analysis
    /// </summary>
    [HttpGet("metrics/roi")]
    public async Task<IActionResult> GetROIAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var analysis = await _analyticsService.GetROIAnalysisAsync(startDate, endDate, filterList);
        return Ok(analysis);
    }

    /// <summary>
    /// Get segment performance analysis
    /// </summary>
    [HttpGet("metrics/segments")]
    public async Task<IActionResult> GetSegmentPerformance(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string[]? segments = null)
    {
        var segmentList = segments?.ToList();
        var performance = await _analyticsService.GetSegmentPerformanceAsync(startDate, endDate, segmentList);
        return Ok(performance);
    }

    /// <summary>
    /// Get cohort analysis
    /// </summary>
    [HttpGet("metrics/cohorts")]
    public async Task<IActionResult> GetCohortAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string cohortType = "monthly")
    {
        var analysis = await _analyticsService.GetCohortAnalysisAsync(startDate, endDate, cohortType);
        return Ok(analysis);
    }

    /// <summary>
    /// Get funnel analysis
    /// </summary>
    [HttpGet("metrics/funnels")]
    public async Task<IActionResult> GetFunnelAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string[]? funnelNames = null)
    {
        var funnelList = funnelNames?.ToList();
        var analysis = await _analyticsService.GetFunnelAnalysisAsync(startDate, endDate, funnelList);
        return Ok(analysis);
    }

    /// <summary>
    /// Get attribution analysis
    /// </summary>
    [HttpGet("metrics/attribution")]
    public async Task<IActionResult> GetAttributionAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var analysis = await _analyticsService.GetAttributionAnalysisAsync(startDate, endDate, filterList);
        return Ok(analysis);
    }

    /// <summary>
    /// Get performance benchmarks
    /// </summary>
    [HttpGet("benchmarks")]
    public async Task<IActionResult> GetPerformanceBenchmarks(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] BenchmarkType benchmarkType = BenchmarkType.Industry)
    {
        var benchmarks = await _analyticsService.GetPerformanceBenchmarksAsync(startDate, endDate, benchmarkType);
        return Ok(benchmarks);
    }

    /// <summary>
    /// Generate insights from analytics data
    /// </summary>
    [HttpGet("insights")]
    public async Task<IActionResult> GenerateInsights(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var insights = await _analyticsService.GenerateInsightsAsync(startDate, endDate, filterList);
        return Ok(insights);
    }

    /// <summary>
    /// Generate recommendations for performance improvement
    /// </summary>
    [HttpGet("recommendations")]
    public async Task<IActionResult> GenerateRecommendations(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var recommendations = await _analyticsService.GenerateRecommendationsAsync(startDate, endDate, filterList);
        return Ok(recommendations);
    }

    /// <summary>
    /// Track conversion event
    /// </summary>
    [HttpPost("conversions/track")]
    public async Task<IActionResult> TrackConversion([FromBody] TrackConversionRequest request)
    {
        await _conversionTrackingService.TrackConversionAsync(
            request.MessageId,
            request.UserId,
            request.ConversionType,
            request.ConversionValue,
            request.Metadata);

        return Ok(new { success = true, message = "Conversion tracked successfully" });
    }

    /// <summary>
    /// Get conversion funnel data
    /// </summary>
    [HttpGet("conversions/funnels/{funnelName}")]
    public async Task<IActionResult> GetConversionFunnel(
        string funnelName,
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var funnel = await _conversionTrackingService.GetConversionFunnelAsync(funnelName, startDate, endDate, filterList);
        return Ok(funnel);
    }

    /// <summary>
    /// Get conversion paths
    /// </summary>
    [HttpGet("conversions/paths")]
    public async Task<IActionResult> GetConversionPaths(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] int maxPathLength = 5,
        [FromQuery] int topPaths = 10)
    {
        var paths = await _conversionTrackingService.GetConversionPathsAsync(startDate, endDate, maxPathLength, topPaths);
        return Ok(paths);
    }

    /// <summary>
    /// Get conversion rate trends
    /// </summary>
    [HttpGet("conversions/trends")]
    public async Task<IActionResult> GetConversionTrends(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string granularity = "daily",
        [FromQuery] string? filters = null)
    {
        var filterList = ParseFilters(filters);
        var trends = await _conversionTrackingService.GetConversionTrendsAsync(startDate, endDate, granularity, filterList);
        return Ok(trends);
    }

    /// <summary>
    /// Calculate ROI for campaigns
    /// </summary>
    [HttpGet("roi/campaigns")]
    public async Task<IActionResult> CalculateROI(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string[]? campaignIds = null)
    {
        var campaignList = campaignIds?.ToList();
        var roi = await _roiAnalyticsService.CalculateROIAsync(startDate, endDate, campaignList);
        return Ok(roi);
    }

    /// <summary>
    /// Get customer lifetime value analysis
    /// </summary>
    [HttpGet("roi/customer-lifetime-value")]
    public async Task<IActionResult> GetCustomerLifetimeValue(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string[]? segments = null)
    {
        var segmentList = segments?.ToList();
        var clv = await _roiAnalyticsService.GetCustomerLifetimeValueAsync(startDate, endDate, segmentList);
        return Ok(clv);
    }

    /// <summary>
    /// Calculate customer acquisition cost
    /// </summary>
    [HttpGet("roi/customer-acquisition-cost")]
    public async Task<IActionResult> GetCustomerAcquisitionCost(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] NotificationChannel[]? channels = null)
    {
        var channelList = channels?.ToList();
        var cac = await _roiAnalyticsService.GetCustomerAcquisitionCostAsync(startDate, endDate, channelList);
        return Ok(cac);
    }

    /// <summary>
    /// Get revenue attribution by channel
    /// </summary>
    [HttpGet("roi/revenue-attribution")]
    public async Task<IActionResult> GetRevenueAttribution(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] AttributionModel model = AttributionModel.LastTouch)
    {
        var attribution = await _roiAnalyticsService.GetRevenueAttributionAsync(startDate, endDate, model);
        return Ok(attribution);
    }

    /// <summary>
    /// Calculate payback period
    /// </summary>
    [HttpGet("roi/payback-analysis")]
    public async Task<IActionResult> GetPaybackAnalysis(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] string[]? campaignIds = null)
    {
        var campaignList = campaignIds?.ToList();
        var payback = await _roiAnalyticsService.GetPaybackAnalysisAsync(startDate, endDate, campaignList);
        return Ok(payback);
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : Guid.Empty;
    }

    private List<ReportFilter>? ParseFilters(string? filtersJson)
    {
        if (string.IsNullOrEmpty(filtersJson))
            return null;

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<ReportFilter>>(filtersJson);
        }
        catch
        {
            return null;
        }
    }
}

// Request/Response DTOs
public class GenerateReportRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ReportType Type { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public List<ReportFilter>? Filters { get; set; }
    public ReportConfiguration? Configuration { get; set; }
}

public class TrackConversionRequest
{
    public Guid MessageId { get; set; }
    public Guid UserId { get; set; }
    public string ConversionType { get; set; } = string.Empty;
    public decimal ConversionValue { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
