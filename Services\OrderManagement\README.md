# Order Management Service

A comprehensive order management microservice built with .NET 8, featuring clean architecture principles, domain-driven design, and CQRS patterns for the TLI Logistics platform.

## 🏗️ Architecture Overview

This service follows Clean Architecture principles with the following layers:

### Domain Layer (`OrderManagement.Domain`)
- **Entities**: Core business entities (RFQ, RfqBid, Order, Invoice, etc.)
- **Value Objects**: LoadDetails, RouteDetails, Money, CustomerDetails
- **Domain Events**: Order lifecycle events
- **Enums**: OrderStatus, PaymentStatus, DocumentType, etc.

### Application Layer (`OrderManagement.Application`)
- **Commands**: CQRS commands for write operations
- **Queries**: CQRS queries for read operations
- **DTOs**: Data transfer objects
- **Interfaces**: Repository and service contracts
- **Mappings**: AutoMapper profiles

### Infrastructure Layer (`OrderManagement.Infrastructure`)
- **Database**: Entity Framework Core with PostgreSQL
- **Repositories**: Data access implementations
- **Configurations**: Entity configurations

### API Layer (`OrderManagement.API`)
- **Controllers**: REST API endpoints
- **Authentication**: JWT token validation
- **Swagger**: API documentation

## 🚀 Features

### RFQ Management
- Create detailed RFQs with load specifications
- Publish RFQs to brokers
- Track RFQ status and expiration
- Manage RFQ documents and attachments

### Bid Management
- Submit competitive bids to RFQs
- Accept/reject bids
- Track bid status and history
- Support for counter offers

### Order Management
- Create orders from accepted bids
- Track order lifecycle from creation to completion
- Manage order documents and status history
- Customer and billing details management

### Invoice Management
- Generate invoices from completed orders
- Track payment status
- Support for line items and tax calculations
- Payment reference tracking

### Document Management
- Upload and manage documents for RFQs, bids, and orders
- Support for various document types (BOL, POD, contracts, etc.)
- File metadata and version tracking

## 🛠️ Technology Stack

- **.NET 8**: Latest .NET framework
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL**: Primary database with TimescaleDB
- **MediatR**: CQRS implementation
- **AutoMapper**: Object mapping
- **FluentValidation**: Input validation
- **Swagger**: API documentation
- **Serilog**: Structured logging
- **RabbitMQ**: Message broker for events

## 🔧 Configuration

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TLI_OrderManagement;User Id=timescale;Password=timescale"
  }
}
```

### JWT Authentication
```json
{
  "JwtSettings": {
    "Secret": "your-super-secret-key-that-is-at-least-32-characters-long",
    "Issuer": "TLI-Identity-Service",
    "Audience": "TLI-Services",
    "ExpiryMinutes": 60
  }
}
```

### Message Broker
```json
{
  "RabbitMQ": {
    "Host": "localhost",
    "Port": 5672,
    "Username": "guest",
    "Password": "guest"
  }
}
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- PostgreSQL database
- RabbitMQ message broker
- Running Identity Service (for JWT authentication)

### 1. Database Setup

#### Option A: Using the SQL Script (Recommended)
```bash
# Connect to PostgreSQL and run the setup script
psql -h localhost -U timescale -d postgres -f database-setup.sql
```

#### Option B: Using Entity Framework Migrations (Alternative)
```bash
# Navigate to the API project
cd OrderManagement.API

# Add migration
dotnet ef migrations add InitialCreate --project ../OrderManagement.Infrastructure

# Update database
dotnet ef database update --project ../OrderManagement.Infrastructure
```

### 2. Run the Service

#### Option A: Using .NET CLI
```bash
# Navigate to the API project
cd OrderManagement.API

# Run the service
dotnet run
```

#### Option B: Using Docker Compose
```bash
# From the OrderManagement directory
docker-compose up -d
```

### 3. Access the API

- **API Base URL**: `http://localhost:5004`
- **Swagger UI**: `http://localhost:5004` (in development)
- **Health Check**: `http://localhost:5004/health` (if implemented)

## 📚 API Endpoints

### RFQ Management
- `POST /api/v1/rfq` - Create a new RFQ
- `GET /api/v1/rfq/{id}` - Get RFQ by ID
- `POST /api/v1/rfq/{id}/publish` - Publish an RFQ
- `GET /api/v1/rfq/my` - Get my RFQs (transport companies)
- `GET /api/v1/rfq/published` - Get published RFQs (brokers)
- `POST /api/v1/rfq/{id}/close` - Close an RFQ

### Bid Management
- `POST /api/v1/bid` - Submit a bid
- `GET /api/v1/bid/{id}` - Get bid by ID
- `GET /api/v1/bid/my` - Get my bids (brokers)
- `POST /api/v1/bid/{id}/accept` - Accept a bid
- `POST /api/v1/bid/{id}/reject` - Reject a bid
- `POST /api/v1/bid/{id}/withdraw` - Withdraw a bid

### Order Management
- `POST /api/v1/order` - Create an order
- `GET /api/v1/order/{id}` - Get order by ID
- `GET /api/v1/order/my` - Get my orders
- `POST /api/v1/order/{id}/confirm` - Confirm an order
- `POST /api/v1/order/{id}/complete` - Complete an order
- `POST /api/v1/order/{id}/cancel` - Cancel an order

## 🔐 Authentication

All endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 🏃‍♂️ Development

### Running Tests
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Code Quality
```bash
# Format code
dotnet format

# Analyze code
dotnet analyze
```

## 🐳 Docker Support

### Build Image
```bash
# From the solution root
docker build -f Services/OrderManagement/Dockerfile -t order-management:latest .
```

### Run with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f order-management

# Stop services
docker-compose down
```

## 📊 Monitoring

### Logging
- Structured logging with Serilog
- Console and file outputs
- Log files stored in `/logs` directory

### Health Checks
- Database connectivity
- Message broker connectivity
- External service dependencies

## 🔄 Integration Events

### Published Events
- `rfq.created` - When an RFQ is created
- `rfq.published` - When an RFQ is published
- `bid.submitted` - When a bid is submitted
- `bid.accepted` - When a bid is accepted
- `order.created` - When an order is created
- `order.completed` - When an order is completed
- `invoice.created` - When an invoice is generated

### Consumed Events
- User authentication events
- Payment processing events
- Trip management events

## 🤝 Contributing

1. Follow clean architecture principles
2. Write comprehensive tests
3. Use proper logging
4. Follow naming conventions
5. Document API changes

## 📝 License

This project is part of the TLI Logistics platform.
