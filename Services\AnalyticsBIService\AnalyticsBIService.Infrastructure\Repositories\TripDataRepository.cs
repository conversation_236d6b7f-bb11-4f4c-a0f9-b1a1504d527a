using AnalyticsBIService.Domain.Entities;
using AnalyticsBIService.Domain.Repositories;
using AnalyticsBIService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Shared.Infrastructure.Repositories;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure.Monitoring;

namespace AnalyticsBIService.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for trip data operations
/// </summary>
public class TripDataRepository : OptimizedRepositoryBase<TripData, Guid>, ITripDataRepository
{
    public TripDataRepository(
        AnalyticsBIDbContext context,
        ICachingService cachingService,
        IPerformanceMetrics performanceMetrics)
        : base(context, cachingService, performanceMetrics)
    {
    }

    /// <summary>
    /// Get trip data by date range
    /// </summary>
    public async Task<IEnumerable<TripData>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await Context.Set<TripData>()
            .Where(td => td.StartTime >= startDate && td.StartTime <= endDate)
            .OrderByDescending(td => td.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get trip data by driver ID
    /// </summary>
    public async Task<IEnumerable<TripData>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<TripData>()
            .Where(td => td.DriverId == driverId)
            .OrderByDescending(td => td.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get trip data by vehicle ID
    /// </summary>
    public async Task<IEnumerable<TripData>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<TripData>()
            .Where(td => td.VehicleId == vehicleId)
            .OrderByDescending(td => td.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get trip data by route
    /// </summary>
    public async Task<IEnumerable<TripData>> GetByRouteAsync(string origin, string destination, CancellationToken cancellationToken = default)
    {
        return await Context.Set<TripData>()
            .Where(td => td.OriginLocation == origin && td.DestinationLocation == destination)
            .OrderByDescending(td => td.StartTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get trip analytics summary
    /// </summary>
    public async Task<object> GetTripAnalyticsSummaryAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var trips = await Context.Set<TripData>()
            .Where(td => td.StartTime >= startDate && td.StartTime <= endDate)
            .ToListAsync(cancellationToken);

        return new
        {
            TotalTrips = trips.Count,
            CompletedTrips = trips.Count(t => t.Status == "Completed"),
            TotalDistance = trips.Sum(t => t.ActualDistance),
            TotalFuelConsumed = trips.Sum(t => t.FuelConsumed),
            TotalFuelCost = trips.Sum(t => t.FuelCost),
            AverageSpeed = trips.Any() ? trips.Average(t => t.AverageSpeed) : 0,
            AverageEfficiencyScore = trips.Any() ? trips.Average(t => t.GetEfficiencyScore()) : 0,
            OnScheduleRate = trips.Any() ? (decimal)trips.Count(t => t.IsOnSchedule()) / trips.Count * 100 : 0,
            IncidentRate = trips.Any() ? (decimal)trips.Count(t => t.HasIncidents) / trips.Count * 100 : 0
        };
    }
}
