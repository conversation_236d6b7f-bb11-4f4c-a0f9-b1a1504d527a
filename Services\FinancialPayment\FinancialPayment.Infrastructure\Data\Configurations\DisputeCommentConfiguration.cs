using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class DisputeCommentConfiguration : IEntityTypeConfiguration<DisputeComment>
{
    public void Configure(EntityTypeBuilder<DisputeComment> builder)
    {
        builder.ToTable("dispute_comments");

        builder.<PERSON><PERSON>ey(c => c.Id);

        builder.Property(c => c.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(c => c.DisputeId)
            .HasColumnName("dispute_id")
            .IsRequired();

        builder.Property(c => c.AuthorId)
            .HasColumnName("author_id")
            .IsRequired();

        builder.Property(c => c.AuthorRole)
            .HasColumnName("author_role")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(c => c.Content)
            .HasColumnName("content")
            .HasMaxLength(2000)
            .IsRequired();

        builder.Property(c => c.IsInternal)
            .HasColumnName("is_internal")
            .IsRequired();

        builder.Property(c => c.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        // Indexes
        builder.HasIndex(c => c.DisputeId)
            .HasDatabaseName("ix_dispute_comments_dispute_id");

        builder.HasIndex(c => c.AuthorId)
            .HasDatabaseName("ix_dispute_comments_author_id");

        builder.HasIndex(c => c.AuthorRole)
            .HasDatabaseName("ix_dispute_comments_author_role");

        builder.HasIndex(c => c.CreatedAt)
            .HasDatabaseName("ix_dispute_comments_created_at");

        builder.HasIndex(c => c.IsInternal)
            .HasDatabaseName("ix_dispute_comments_is_internal");
    }
}
