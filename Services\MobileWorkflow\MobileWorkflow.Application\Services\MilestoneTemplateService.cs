using AutoMapper;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;

namespace MobileWorkflow.Application.Services;

public class MilestoneTemplateService : IMilestoneTemplateService
{
    private readonly IMilestoneTemplateRepository _templateRepository;
    private readonly IMilestoneValidationService _validationService;
    private readonly IMapper _mapper;
    private readonly ILogger<MilestoneTemplateService> _logger;

    public MilestoneTemplateService(
        IMilestoneTemplateRepository templateRepository,
        IMilestoneValidationService validationService,
        IMapper mapper,
        ILogger<MilestoneTemplateService> logger)
    {
        _templateRepository = templateRepository;
        _validationService = validationService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MilestoneTemplateDto> CreateTemplateAsync(CreateMilestoneTemplateRequest request, string createdBy, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating milestone template: {Name} by {CreatedBy}", request.Name, createdBy);

            // Check if template with same name already exists
            var existingTemplate = await _templateRepository.GetByNameAsync(request.Name, cancellationToken);
            if (existingTemplate != null)
            {
                throw new InvalidOperationException($"Milestone template with name '{request.Name}' already exists");
            }

            // Create the milestone template
            var template = new MilestoneTemplate(
                request.Name,
                request.Description,
                request.Type,
                request.Category,
                createdBy);

            // Update configuration and metadata
            if (request.Configuration.Any())
            {
                template.UpdateConfiguration(request.Configuration, createdBy);
            }

            foreach (var kvp in request.Metadata)
            {
                template.AddMetadata(kvp.Key, kvp.Value);
            }

            // Add steps if provided
            foreach (var stepRequest in request.Steps.OrderBy(s => s.SequenceNumber))
            {
                var step = template.AddStep(
                    stepRequest.Name,
                    stepRequest.Description,
                    stepRequest.SequenceNumber,
                    stepRequest.IsRequired);

                // Update step configuration and metadata
                if (stepRequest.Configuration.Any())
                {
                    step.UpdateConfiguration(stepRequest.Configuration);
                }

                foreach (var kvp in stepRequest.Metadata)
                {
                    step.AddMetadata(kvp.Key, kvp.Value);
                }

                // Set trigger condition if provided
                if (!string.IsNullOrEmpty(stepRequest.TriggerCondition))
                {
                    step.SetTriggerCondition(stepRequest.TriggerCondition);
                }

                // Add payout rules
                foreach (var payoutRequest in stepRequest.PayoutRules)
                {
                    var payoutRule = step.AddPayoutRule(
                        payoutRequest.PayoutPercentage,
                        payoutRequest.TriggerCondition);

                    if (!string.IsNullOrEmpty(payoutRequest.Description))
                    {
                        payoutRule.UpdateDescription(payoutRequest.Description);
                    }

                    if (payoutRequest.Configuration.Any())
                    {
                        payoutRule.UpdateConfiguration(payoutRequest.Configuration);
                    }

                    foreach (var kvp in payoutRequest.Metadata)
                    {
                        payoutRule.AddMetadata(kvp.Key, kvp.Value);
                    }
                }
            }

            // Validate the template before saving
            var validationResult = await _validationService.ValidateTemplateAsync(template, cancellationToken);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Template validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Save the template
            var savedTemplate = await _templateRepository.AddAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template created successfully with ID: {TemplateId}", savedTemplate.Id);

            return await MapToDto(savedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating milestone template: {Name}", request.Name);
            throw;
        }
    }

    public async Task<MilestoneTemplateDto> UpdateTemplateAsync(Guid id, UpdateMilestoneTemplateRequest request, string updatedBy, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating milestone template: {Id} by {UpdatedBy}", id, updatedBy);

            var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {id} not found");
            }

            // Check if name is being changed and if new name already exists
            if (template.Name != request.Name)
            {
                var existingTemplate = await _templateRepository.GetByNameAsync(request.Name, cancellationToken);
                if (existingTemplate != null && existingTemplate.Id != id)
                {
                    throw new InvalidOperationException($"Milestone template with name '{request.Name}' already exists");
                }
            }

            // Update template details
            template.UpdateDetails(request.Name, request.Description, updatedBy);

            // Update configuration
            if (request.Configuration.Any())
            {
                template.UpdateConfiguration(request.Configuration, updatedBy);
            }

            // Update metadata
            foreach (var kvp in request.Metadata)
            {
                template.AddMetadata(kvp.Key, kvp.Value);
            }

            // Validate the template before saving
            var validationResult = await _validationService.ValidateTemplateAsync(template, cancellationToken);
            if (!validationResult.IsValid)
            {
                throw new InvalidOperationException($"Template validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Save changes
            var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

            _logger.LogInformation("Milestone template updated successfully: {Id}", updatedTemplate.Id);

            return await MapToDto(updatedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating milestone template: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteTemplateAsync(Guid id, string deletedBy, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting milestone template: {Id} by {DeletedBy}", id, deletedBy);

            var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
            if (template == null)
            {
                throw new InvalidOperationException($"Milestone template with ID {id} not found");
            }

            // Check if template can be deleted
            var canDelete = await _validationService.CanTemplateBeDeletedAsync(id, cancellationToken);
            if (!canDelete)
            {
                throw new InvalidOperationException("Template cannot be deleted because it is in use or is a default template");
            }

            await _templateRepository.DeleteAsync(id, cancellationToken);

            _logger.LogInformation("Milestone template deleted successfully: {Id}", id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting milestone template: {Id}", id);
            throw;
        }
    }

    public async Task<MilestoneTemplateDto?> GetTemplateByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
        return template != null ? await MapToDto(template) : null;
    }

    public async Task<MilestoneTemplateDto?> GetTemplateByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByNameAsync(name, cancellationToken);
        return template != null ? await MapToDto(template) : null;
    }

    public async Task<IEnumerable<MilestoneTemplateDto>> GetTemplatesByTypeAsync(string type, CancellationToken cancellationToken = default)
    {
        var templates = await _templateRepository.GetByTypeAsync(type, cancellationToken);
        var templateDtos = new List<MilestoneTemplateDto>();

        foreach (var template in templates)
        {
            templateDtos.Add(await MapToDto(template));
        }

        return templateDtos;
    }

    public async Task<IEnumerable<MilestoneTemplateDto>> GetActiveTemplatesAsync(CancellationToken cancellationToken = default)
    {
        var templates = await _templateRepository.GetActiveTemplatesAsync(cancellationToken);
        var templateDtos = new List<MilestoneTemplateDto>();

        foreach (var template in templates)
        {
            templateDtos.Add(await MapToDto(template));
        }

        return templateDtos;
    }

    public async Task<MilestoneTemplateDto?> GetDefaultTemplateByTypeAsync(string type, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetDefaultTemplateByTypeAsync(type, cancellationToken);
        return template != null ? await MapToDto(template) : null;
    }

    public async Task<MilestoneTemplateDto> SetTemplateAsDefaultAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
        if (template == null)
        {
            throw new InvalidOperationException($"Milestone template with ID {id} not found");
        }

        // Remove default from other templates of the same type
        var existingDefaults = await _templateRepository.GetByTypeAsync(template.Type, cancellationToken);
        foreach (var existingDefault in existingDefaults.Where(t => t.IsDefault && t.Id != id))
        {
            existingDefault.RemoveAsDefault();
            await _templateRepository.UpdateAsync(existingDefault, cancellationToken);
        }

        template.SetAsDefault();
        var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

        return await MapToDto(updatedTemplate);
    }

    public async Task<MilestoneTemplateDto> ActivateTemplateAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
        if (template == null)
        {
            throw new InvalidOperationException($"Milestone template with ID {id} not found");
        }

        template.Activate();
        var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

        return await MapToDto(updatedTemplate);
    }

    public async Task<MilestoneTemplateDto> DeactivateTemplateAsync(Guid id, string updatedBy, CancellationToken cancellationToken = default)
    {
        var template = await _templateRepository.GetByIdAsync(id, cancellationToken);
        if (template == null)
        {
            throw new InvalidOperationException($"Milestone template with ID {id} not found");
        }

        template.Deactivate();
        var updatedTemplate = await _templateRepository.UpdateAsync(template, cancellationToken);

        return await MapToDto(updatedTemplate);
    }

    public async Task<MilestoneTemplateDto> CloneTemplateAsync(Guid sourceId, string newName, string createdBy, CancellationToken cancellationToken = default)
    {
        var sourceTemplate = await _templateRepository.GetByIdAsync(sourceId, cancellationToken);
        if (sourceTemplate == null)
        {
            throw new InvalidOperationException($"Source template with ID {sourceId} not found");
        }

        // Check if new name already exists
        var existingTemplate = await _templateRepository.GetByNameAsync(newName, cancellationToken);
        if (existingTemplate != null)
        {
            throw new InvalidOperationException($"Template with name '{newName}' already exists");
        }

        // Create new template based on source
        var clonedTemplate = new MilestoneTemplate(
            newName,
            $"Cloned from {sourceTemplate.Name}",
            sourceTemplate.Type,
            sourceTemplate.Category,
            createdBy);

        // Clone configuration and metadata
        clonedTemplate.UpdateConfiguration(sourceTemplate.Configuration, createdBy);
        foreach (var kvp in sourceTemplate.Metadata)
        {
            clonedTemplate.AddMetadata(kvp.Key, kvp.Value);
        }

        // Clone steps and payout rules
        foreach (var sourceStep in sourceTemplate.Steps.OrderBy(s => s.SequenceNumber))
        {
            var clonedStep = clonedTemplate.AddStep(
                sourceStep.Name,
                sourceStep.Description,
                sourceStep.SequenceNumber,
                sourceStep.IsRequired);

            clonedStep.UpdateConfiguration(sourceStep.Configuration);
            foreach (var kvp in sourceStep.Metadata)
            {
                clonedStep.AddMetadata(kvp.Key, kvp.Value);
            }

            if (!string.IsNullOrEmpty(sourceStep.TriggerCondition))
            {
                clonedStep.SetTriggerCondition(sourceStep.TriggerCondition);
            }

            foreach (var sourceRule in sourceStep.PayoutRules)
            {
                var clonedRule = clonedStep.AddPayoutRule(
                    sourceRule.PayoutPercentage,
                    sourceRule.TriggerCondition);

                if (!string.IsNullOrEmpty(sourceRule.Description))
                {
                    clonedRule.UpdateDescription(sourceRule.Description);
                }

                clonedRule.UpdateConfiguration(sourceRule.Configuration);
                foreach (var kvp in sourceRule.Metadata)
                {
                    clonedRule.AddMetadata(kvp.Key, kvp.Value);
                }
            }
        }

        var savedTemplate = await _templateRepository.AddAsync(clonedTemplate, cancellationToken);
        return await MapToDto(savedTemplate);
    }

    public async Task<IEnumerable<MilestoneTemplatePreviewDto>> SearchTemplatesAsync(MilestoneTemplateSearchRequest request, CancellationToken cancellationToken = default)
    {
        var templates = await _templateRepository.GetAllAsync(cancellationToken);
        var filteredTemplates = templates.AsEnumerable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            filteredTemplates = filteredTemplates.Where(t =>
                t.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                t.Description.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(request.Type))
        {
            filteredTemplates = filteredTemplates.Where(t => t.Type.Equals(request.Type, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(request.Category))
        {
            filteredTemplates = filteredTemplates.Where(t => t.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase));
        }

        if (request.IsActive.HasValue)
        {
            filteredTemplates = filteredTemplates.Where(t => t.IsActive == request.IsActive.Value);
        }

        if (request.IsDefault.HasValue)
        {
            filteredTemplates = filteredTemplates.Where(t => t.IsDefault == request.IsDefault.Value);
        }

        if (!string.IsNullOrEmpty(request.CreatedBy))
        {
            filteredTemplates = filteredTemplates.Where(t => t.CreatedBy.Equals(request.CreatedBy, StringComparison.OrdinalIgnoreCase));
        }

        // Apply sorting
        filteredTemplates = request.SortBy.ToLower() switch
        {
            "name" => request.SortDirection.ToUpper() == "DESC" ? filteredTemplates.OrderByDescending(t => t.Name) : filteredTemplates.OrderBy(t => t.Name),
            "type" => request.SortDirection.ToUpper() == "DESC" ? filteredTemplates.OrderByDescending(t => t.Type) : filteredTemplates.OrderBy(t => t.Type),
            "category" => request.SortDirection.ToUpper() == "DESC" ? filteredTemplates.OrderByDescending(t => t.Category) : filteredTemplates.OrderBy(t => t.Category),
            "createdat" => request.SortDirection.ToUpper() == "DESC" ? filteredTemplates.OrderByDescending(t => t.CreatedAt) : filteredTemplates.OrderBy(t => t.CreatedAt),
            "usagecount" => request.SortDirection.ToUpper() == "DESC" ? filteredTemplates.OrderByDescending(t => t.UsageCount) : filteredTemplates.OrderBy(t => t.UsageCount),
            _ => filteredTemplates.OrderBy(t => t.Name)
        };

        // Apply pagination
        var pagedTemplates = filteredTemplates
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize);

        return _mapper.Map<IEnumerable<MilestoneTemplatePreviewDto>>(pagedTemplates);
    }

    public async Task<BulkOperationResult> BulkOperationAsync(BulkMilestoneTemplateOperationRequest request, CancellationToken cancellationToken = default)
    {
        var result = new BulkOperationResult
        {
            TotalRequested = request.TemplateIds.Count
        };

        foreach (var templateId in request.TemplateIds)
        {
            try
            {
                switch (request.Operation.ToLower())
                {
                    case "activate":
                        await ActivateTemplateAsync(templateId, request.PerformedBy, cancellationToken);
                        break;
                    case "deactivate":
                        await DeactivateTemplateAsync(templateId, request.PerformedBy, cancellationToken);
                        break;
                    case "delete":
                        await DeleteTemplateAsync(templateId, request.PerformedBy, cancellationToken);
                        break;
                    default:
                        throw new InvalidOperationException($"Unknown operation: {request.Operation}");
                }

                result.Successful++;
            }
            catch (Exception ex)
            {
                result.Failed++;
                result.Errors.Add(new BulkOperationError
                {
                    Id = templateId,
                    Error = ex.Message
                });
            }
        }

        return result;
    }

    private async Task<MilestoneTemplateDto> MapToDto(MilestoneTemplate template)
    {
        var templateDto = _mapper.Map<MilestoneTemplateDto>(template);
        templateDto.TotalPayoutPercentage = template.Steps
            .SelectMany(s => s.PayoutRules)
            .Sum(r => r.PayoutPercentage);
        templateDto.IsValid = template.ValidatePayoutPercentages();
        templateDto.ValidationErrors = template.GetValidationErrors();

        return templateDto;
    }
}
