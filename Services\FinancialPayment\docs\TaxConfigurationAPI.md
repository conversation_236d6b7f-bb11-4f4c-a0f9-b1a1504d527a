# Tax Configuration API Documentation

## Overview

The Tax Configuration module provides comprehensive tax management capabilities for the Financial & Payment Service, including GST and TDS calculations, HSN code management, and multi-jurisdiction tax compliance.

## Features

- **Comprehensive Tax Calculations**: Automated GST and TDS calculations
- **Multi-Jurisdiction Support**: State and city-specific tax configurations
- **HSN Code Management**: Complete HSN code database with GST mappings
- **Compliance Reporting**: Tax compliance reports and audit trails
- **Real-time Processing**: Dynamic tax calculations for all transactions
- **Admin Controls**: Role-based access for tax configuration management

## Authentication & Authorization

All tax configuration endpoints require authentication and appropriate role-based permissions:

- **Admin**: Full access to all tax configuration operations
- **Finance**: Read access to tax configurations and calculations
- **User**: Limited access to tax calculation endpoints only

### Required Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Tax Configuration Endpoints

### Get All Tax Configurations

```http
GET /api/tax-configurations
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (Active, Inactive, Archived)
- `jurisdiction` (optional): Filter by jurisdiction

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "India Default Tax Configuration",
      "description": "Default tax configuration for India",
      "jurisdictionCountry": "India",
      "jurisdictionState": "Maharashtra",
      "jurisdictionCity": "Mumbai",
      "jurisdictionType": "City",
      "status": "Active",
      "effectiveFrom": "2024-01-01T00:00:00Z",
      "effectiveTo": null,
      "isDefault": true,
      "priority": 1,
      "enableGstCalculation": true,
      "enableTdsCalculation": true,
      "enableReverseCharge": true,
      "requireHsnCode": true,
      "defaultGstRate": 18.0,
      "defaultTdsRate": 2.0,
      "defaultCurrency": "INR",
      "createdBy": "admin",
      "createdAt": "2024-01-01T00:00:00Z",
      "modifiedBy": "admin",
      "modifiedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "totalCount": 1,
  "page": 1,
  "pageSize": 10
}
```

### Get Tax Configuration by ID

```http
GET /api/tax-configurations/{id}
```

**Path Parameters:**
- `id`: Tax configuration UUID

**Response:** Single tax configuration object (same structure as above)

### Create Tax Configuration

```http
POST /api/tax-configurations
```

**Request Body:**
```json
{
  "name": "Karnataka Tax Configuration",
  "description": "Tax configuration for Karnataka state",
  "jurisdictionCountry": "India",
  "jurisdictionState": "Karnataka",
  "jurisdictionCity": "Bangalore",
  "jurisdictionType": "City",
  "effectiveFrom": "2024-01-01T00:00:00Z",
  "effectiveTo": "2024-12-31T23:59:59Z",
  "isDefault": false,
  "priority": 2,
  "enableGstCalculation": true,
  "enableTdsCalculation": true,
  "enableReverseCharge": false,
  "requireHsnCode": true,
  "defaultGstRate": 18.0,
  "defaultTdsRate": 2.0,
  "defaultCurrency": "INR"
}
```

**Response:** Created tax configuration object with 201 status

### Update Tax Configuration

```http
PUT /api/tax-configurations/{id}
```

**Request Body:** Same as create request

**Response:** Updated tax configuration object

### Delete Tax Configuration

```http
DELETE /api/tax-configurations/{id}
```

**Response:** 204 No Content

### Set as Default Configuration

```http
POST /api/tax-configurations/{id}/set-default
```

**Response:** Updated tax configuration object

## Tax Calculation Endpoint

### Calculate Comprehensive Tax

```http
POST /api/tax-configurations/calculate
```

**Request Body:**
```json
{
  "baseAmount": 10000.00,
  "currency": "INR",
  "serviceCategory": "Transportation",
  "jurisdictionCountry": "India",
  "jurisdictionState": "Maharashtra",
  "jurisdictionCity": "Mumbai",
  "jurisdictionType": "City",
  "entityType": "Company",
  "tdsSection": "Section194C",
  "hsnCode": "9967",
  "hasPan": true
}
```

**Response:**
```json
{
  "baseAmount": 10000.00,
  "baseCurrency": "INR",
  "gstAmount": 500.00,
  "gstCurrency": "INR",
  "tdsAmount": 200.00,
  "tdsCurrency": "INR",
  "totalTaxAmount": 700.00,
  "totalTaxCurrency": "INR",
  "netAmount": 10300.00,
  "netCurrency": "INR",
  "effectiveGstRate": 5.0,
  "effectiveTdsRate": 2.0,
  "isReverseChargeApplicable": false,
  "appliedHsnCode": "9967",
  "appliedRules": [
    "GST calculated at 5% for transportation services",
    "TDS calculated at 2% under Section 194C"
  ],
  "warnings": [],
  "calculatedAt": "2024-06-24T10:30:00Z"
}
```

## GST Configuration Endpoints

### Get All GST Configurations

```http
GET /api/gst-configurations
```

**Query Parameters:**
- `serviceCategory` (optional): Filter by service category
- `gstRate` (optional): Filter by GST rate
- `status` (optional): Filter by status

### Create GST Configuration

```http
POST /api/gst-configurations
```

**Request Body:**
```json
{
  "name": "Transportation GST Configuration",
  "description": "GST configuration for transportation services",
  "serviceCategory": "Transportation",
  "jurisdictionCountry": "India",
  "jurisdictionState": "Maharashtra",
  "jurisdictionType": "State",
  "gstRate": "Rate5",
  "taxRate": 5.0,
  "taxCalculationMethod": "Percentage",
  "taxEffectiveFrom": "2024-01-01T00:00:00Z",
  "hsnCode": "9967",
  "minimumAmount": 0.00,
  "maximumAmount": 10000000.00,
  "isReverseChargeApplicable": false
}
```

## TDS Configuration Endpoints

### Get All TDS Configurations

```http
GET /api/tds-configurations
```

**Query Parameters:**
- `section` (optional): Filter by TDS section
- `entityType` (optional): Filter by entity type
- `status` (optional): Filter by status

### Create TDS Configuration

```http
POST /api/tds-configurations
```

**Request Body:**
```json
{
  "name": "Section 194C TDS Configuration",
  "description": "TDS configuration for contractor payments",
  "section": "Section194C",
  "entityType": "Company",
  "taxRate": 2.0,
  "taxCalculationMethod": "Percentage",
  "taxEffectiveFrom": "2024-01-01T00:00:00Z",
  "thresholdAmount": 30000.00,
  "annualThresholdAmount": 100000.00,
  "requiresPan": true,
  "higherRateWithoutPan": 20.0
}
```

## HSN Code Endpoints

### Get All HSN Codes

```http
GET /api/hsn-codes
```

**Query Parameters:**
- `category` (optional): Filter by category
- `chapter` (optional): Filter by chapter
- `search` (optional): Search in code or description

### Search HSN Codes

```http
GET /api/hsn-codes/search?q={searchTerm}
```

**Query Parameters:**
- `q`: Search term for code, description, or category
- `limit` (optional): Maximum results (default: 20)

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "code": "9967",
      "description": "Supporting and auxiliary transport services",
      "category": "Transportation",
      "applicableGstRate": "Rate5",
      "chapter": "99",
      "section": "Services",
      "status": "Active",
      "effectiveFrom": "2024-01-01T00:00:00Z",
      "effectiveTo": null
    }
  ],
  "totalCount": 1
}
```

## Error Responses

### Common Error Codes

- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: Insufficient permissions for the operation
- `404 Not Found`: Requested resource not found
- `409 Conflict`: Resource conflict (e.g., duplicate name)
- `500 Internal Server Error`: Server-side error

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred",
    "details": [
      {
        "field": "baseAmount",
        "message": "Base amount must be greater than zero"
      }
    ]
  },
  "timestamp": "2024-06-24T10:30:00Z",
  "traceId": "uuid"
}
```

## Rate Limits

- **Standard Users**: 100 requests per minute
- **Admin Users**: 1000 requests per minute
- **Tax Calculation**: 500 requests per minute per user

## Examples

### Calculate Tax for Transportation Service

```bash
curl -X POST "https://api.tli.com/api/tax-configurations/calculate" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "baseAmount": 50000,
    "currency": "INR",
    "serviceCategory": "Transportation",
    "jurisdictionCountry": "India",
    "jurisdictionState": "Maharashtra",
    "jurisdictionType": "State",
    "entityType": "Company",
    "tdsSection": "Section194C",
    "hsnCode": "9967",
    "hasPan": true
  }'
```

### Create New Tax Configuration

```bash
curl -X POST "https://api.tli.com/api/tax-configurations" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Delhi Tax Configuration",
    "description": "Tax configuration for Delhi NCR",
    "jurisdictionCountry": "India",
    "jurisdictionState": "Delhi",
    "jurisdictionType": "State",
    "effectiveFrom": "2024-07-01T00:00:00Z",
    "priority": 3,
    "enableGstCalculation": true,
    "enableTdsCalculation": true,
    "defaultGstRate": 18.0,
    "defaultTdsRate": 2.0
  }'
```

## Webhooks

Tax configuration changes can trigger webhooks for external system integration:

- `tax.configuration.created`
- `tax.configuration.updated`
- `tax.configuration.deleted`
- `tax.calculation.completed`

Configure webhook endpoints in the admin panel to receive these events.
