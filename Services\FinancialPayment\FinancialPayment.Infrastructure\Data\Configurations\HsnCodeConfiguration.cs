using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class HsnCodeConfiguration : IEntityTypeConfiguration<HsnCode>
{
    public void Configure(EntityTypeBuilder<HsnCode> builder)
    {
        builder.ToTable("HsnCodes");

        builder.Has<PERSON>ey(hc => hc.Id);

        // Configure HsnCodeDetails value object
        builder.OwnsOne(hc => hc.CodeDetails, codeDetails =>
        {
            codeDetails.Property(cd => cd.Code)
                .HasColumnName("Code")
                .IsRequired()
                .HasMaxLength(20);
            codeDetails.Property(cd => cd.Description)
                .HasColumnName("Description")
                .IsRequired()
                .HasMaxLength(500);
            codeDetails.Property(cd => cd.Category)
                .HasColumnName("Category")
                .IsRequired()
                .HasMaxLength(200);
            codeDetails.Property(cd => cd.ApplicableGstRate)
                .HasColumnName("ApplicableGstRate")
                .HasConversion<int>();
        });

        builder.Property(hc => hc.Chapter)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(hc => hc.Section)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(hc => hc.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(hc => hc.EffectiveFrom)
            .IsRequired();

        builder.Property(hc => hc.EffectiveTo);

        builder.Property(hc => hc.AdditionalNotes)
            .HasMaxLength(1000);

        builder.Property(hc => hc.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(hc => hc.ModifiedBy)
            .HasMaxLength(100);

        builder.Property(hc => hc.ModifiedAt);

        // Configure relationships
        builder.HasMany(hc => hc.History)
            .WithOne()
            .HasForeignKey(h => h.HsnCodeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(hc => hc.GstMappings)
            .WithOne()
            .HasForeignKey(m => m.HsnCodeId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex("Code")
            .IsUnique();
        builder.HasIndex(hc => hc.Chapter);
        builder.HasIndex(hc => hc.Section);
        builder.HasIndex(hc => hc.Status);
        builder.HasIndex(hc => new { hc.EffectiveFrom, hc.EffectiveTo });
        builder.HasIndex("Category");
        builder.HasIndex("ApplicableGstRate");
    }
}

public class HsnCodeHistoryConfiguration : IEntityTypeConfiguration<HsnCodeHistory>
{
    public void Configure(EntityTypeBuilder<HsnCodeHistory> builder)
    {
        builder.ToTable("HsnCodeHistories");

        builder.HasKey(hch => hch.Id);

        builder.Property(hch => hch.HsnCodeId)
            .IsRequired();

        builder.Property(hch => hch.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(hch => hch.Details)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(hch => hch.ModifiedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(hch => hch.ModifiedAt)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(hch => hch.HsnCodeId);
        builder.HasIndex(hch => hch.ModifiedAt);
        builder.HasIndex(hch => hch.ModifiedBy);
    }
}

public class HsnCodeGstMappingConfiguration : IEntityTypeConfiguration<HsnCodeGstMapping>
{
    public void Configure(EntityTypeBuilder<HsnCodeGstMapping> builder)
    {
        builder.ToTable("HsnCodeGstMappings");

        builder.HasKey(hgm => hgm.Id);

        builder.Property(hgm => hgm.HsnCodeId)
            .IsRequired();

        builder.Property(hgm => hgm.GstRate)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(hgm => hgm.EffectiveFrom)
            .IsRequired();

        builder.Property(hgm => hgm.EffectiveTo);

        builder.Property(hgm => hgm.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(hgm => hgm.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        // Configure indexes
        builder.HasIndex(hgm => hgm.HsnCodeId);
        builder.HasIndex(hgm => hgm.GstRate);
        builder.HasIndex(hgm => hgm.Status);
        builder.HasIndex(hgm => new { hgm.EffectiveFrom, hgm.EffectiveTo });
        builder.HasIndex(hgm => new { hgm.HsnCodeId, hgm.Status });
    }
}
