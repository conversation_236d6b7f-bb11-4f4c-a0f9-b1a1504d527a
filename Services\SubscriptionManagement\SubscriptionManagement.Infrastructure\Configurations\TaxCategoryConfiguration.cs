using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class TaxCategoryConfiguration : IEntityTypeConfiguration<TaxCategory>
    {
        public void Configure(EntityTypeBuilder<TaxCategory> builder)
        {
            builder.ToTable("TaxCategories");

            builder.HasKey(tc => tc.Id);

            builder.Property(tc => tc.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(tc => tc.Description)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(tc => tc.Code)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(tc => tc.IsActive)
                .IsRequired();

            builder.Property(tc => tc.CreatedAt)
                .IsRequired();

            builder.Property(tc => tc.UpdatedAt);

            // Configure TaxConfigurations as owned entities
            builder.OwnsMany(tc => tc.TaxConfigurations, taxConfig =>
            {
                taxConfig.ToTable("TaxCategoryConfigurations");
                
                taxConfig.WithOwner().HasForeignKey("TaxCategoryId");
                
                taxConfig.Property<Guid>("Id");
                taxConfig.HasKey("Id");

                taxConfig.Property(c => c.TaxType)
                    .HasConversion<string>()
                    .IsRequired();

                taxConfig.Property(c => c.Rate)
                    .HasColumnType("decimal(5,4)")
                    .IsRequired();

                taxConfig.Property(c => c.IsIncluded)
                    .IsRequired();

                taxConfig.Property(c => c.EffectiveDate)
                    .IsRequired();

                taxConfig.Property(c => c.ExpirationDate);

                // Store ApplicableRegions as JSON
                taxConfig.Property(c => c.ApplicableRegions)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                    .HasColumnName("ApplicableRegions")
                    .HasMaxLength(1000);
            });

            // Indexes
            builder.HasIndex(tc => tc.Code)
                .IsUnique();

            builder.HasIndex(tc => tc.Name);

            builder.HasIndex(tc => tc.IsActive);

            builder.HasIndex(tc => tc.CreatedAt);
        }
    }
}
