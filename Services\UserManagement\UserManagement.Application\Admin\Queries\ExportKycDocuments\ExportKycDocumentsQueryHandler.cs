using System.IO.Compression;
using System.Text;
using System.Text.Json;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Services;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;

namespace UserManagement.Application.Admin.Queries.ExportKycDocuments
{
    public class ExportKycDocumentsQueryHandler : IRequestHandler<ExportKycDocumentsQuery, KycDocumentExportResponse>
    {
        private readonly IDocumentSubmissionRepository _documentSubmissionRepository;
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IFileStorageService _fileStorageService;
        private readonly IAuditService _auditService;
        private readonly ILogger<ExportKycDocumentsQueryHandler> _logger;

        public ExportKycDocumentsQueryHandler(
            IDocumentSubmissionRepository documentSubmissionRepository,
            IUserProfileRepository userProfileRepository,
            IFileStorageService fileStorageService,
            IAuditService auditService,
            ILogger<ExportKycDocumentsQueryHandler> logger)
        {
            _documentSubmissionRepository = documentSubmissionRepository;
            _userProfileRepository = userProfileRepository;
            _fileStorageService = fileStorageService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<KycDocumentExportResponse> Handle(ExportKycDocumentsQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting KYC document export for user {UserId}", request.RequestedBy);

            try
            {
                // Log audit trail for export request
                await _auditService.LogDataExportAsync(
                    "KycDocuments",
                    request.RequestedBy,
                    request.RequestedByRole,
                    request.IpAddress,
                    request.UserAgent,
                    new Dictionary<string, object>
                    {
                        ["UserIds"] = request.UserIds.Count,
                        ["DocumentTypes"] = string.Join(",", request.DocumentTypes),
                        ["Status"] = request.Status?.ToString(),
                        ["FromDate"] = request.FromDate?.ToString("yyyy-MM-dd"),
                        ["ToDate"] = request.ToDate?.ToString("yyyy-MM-dd"),
                        ["UserType"] = request.UserType?.ToString(),
                        ["MaxDocuments"] = request.MaxDocuments
                    });

                // Get document submissions based on filters
                var documentSubmissions = await GetFilteredDocumentSubmissionsAsync(request);

                // Apply document limit if specified
                if (request.MaxDocuments.HasValue)
                {
                    documentSubmissions = documentSubmissions.Take(request.MaxDocuments.Value).ToList();
                }

                // Create ZIP file with documents and metadata
                var zipBytes = await CreateDocumentZipAsync(documentSubmissions, request);

                // Generate unique filename
                var exportId = Guid.NewGuid().ToString("N")[..8];
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                var fileName = $"kyc_documents_{timestamp}_{exportId}.zip";

                // Save to file storage
                var fileUrl = await _fileStorageService.SaveExportFileAsync(fileName, zipBytes, "application/zip");

                // Create document summary
                var documentSummary = CreateDocumentSummary(documentSubmissions);

                var response = new KycDocumentExportResponse
                {
                    FileName = fileName,
                    FileUrl = fileUrl,
                    DocumentCount = documentSummary.Count,
                    UserCount = documentSummary.Select(d => d.UserId).Distinct().Count(),
                    FileSizeBytes = zipBytes.Length,
                    GeneratedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(7), // Files expire after 7 days
                    ExportId = exportId,
                    DocumentSummary = documentSummary
                };

                _logger.LogInformation("Successfully generated KYC document export: {FileName} with {DocumentCount} documents from {UserCount} users",
                    fileName, response.DocumentCount, response.UserCount);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating KYC document export for user {UserId}", request.RequestedBy);
                throw;
            }
        }

        private async Task<List<DocumentSubmission>> GetFilteredDocumentSubmissionsAsync(ExportKycDocumentsQuery request)
        {
            var filters = new Dictionary<string, object>();

            if (request.UserIds?.Any() == true)
                filters["UserIds"] = request.UserIds;

            if (request.DocumentTypes?.Any() == true)
                filters["DocumentTypes"] = request.DocumentTypes;

            if (request.Status.HasValue)
                filters["Status"] = request.Status.Value;

            if (request.FromDate.HasValue)
                filters["FromDate"] = request.FromDate.Value;

            if (request.ToDate.HasValue)
                filters["ToDate"] = request.ToDate.Value;

            if (request.UserType.HasValue)
                filters["UserType"] = request.UserType.Value;

            return await _documentSubmissionRepository.GetDocumentSubmissionsAsync(filters);
        }

        private async Task<byte[]> CreateDocumentZipAsync(List<DocumentSubmission> documentSubmissions, ExportKycDocumentsQuery request)
        {
            using var memoryStream = new MemoryStream();
            using var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);

            var processedDocuments = 0;
            var userGroups = request.GroupByUser
                ? documentSubmissions.GroupBy(ds => (Guid?)ds.UserId).ToList()
                : new[] { documentSubmissions.GroupBy(ds => (Guid?)null).First() }.ToList();

            foreach (var userGroup in userGroups)
            {
                var userFolderName = request.GroupByUser && userGroup.Key.HasValue
                    ? $"User_{userGroup.Key}"
                    : "Documents";

                foreach (var submission in userGroup)
                {
                    foreach (var document in submission.Documents)
                    {
                        if (document.Status == DocumentStatus.Uploaded ||
                            document.Status == DocumentStatus.Approved ||
                            document.Status == DocumentStatus.UnderReview)
                        {
                            try
                            {
                                await AddDocumentToZipAsync(archive, document, submission, userFolderName, request.GroupByDocumentType);
                                processedDocuments++;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to add document {DocumentId} to ZIP", document.Id);
                            }
                        }
                    }
                }
            }

            // Add metadata file if requested
            if (request.IncludeMetadata)
            {
                await AddMetadataToZipAsync(archive, documentSubmissions);
            }

            _logger.LogInformation("Added {ProcessedDocuments} documents to ZIP archive", processedDocuments);

            return memoryStream.ToArray();
        }

        private async Task AddDocumentToZipAsync(ZipArchive archive, Document document, DocumentSubmission submission,
            string userFolderName, bool groupByDocumentType)
        {
            if (string.IsNullOrWhiteSpace(document.FilePath) || !File.Exists(document.FilePath))
            {
                _logger.LogWarning("Document file not found: {FilePath}", document.FilePath);
                return;
            }

            var folderPath = groupByDocumentType
                ? $"{userFolderName}/{document.DocumentType}"
                : userFolderName;

            var entryName = $"{folderPath}/{document.FileName}";

            // Ensure unique filenames
            var counter = 1;
            var originalEntryName = entryName;
            while (archive.Entries.Any(e => e.FullName == entryName))
            {
                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(document.FileName);
                var extension = Path.GetExtension(document.FileName);
                entryName = $"{folderPath}/{fileNameWithoutExt}_{counter}{extension}";
                counter++;
            }

            var entry = archive.CreateEntry(entryName);
            using var entryStream = entry.Open();
            using var fileStream = File.OpenRead(document.FilePath);
            await fileStream.CopyToAsync(entryStream);
        }

        private async Task AddMetadataToZipAsync(ZipArchive archive, List<DocumentSubmission> documentSubmissions)
        {
            var metadata = new
            {
                ExportInfo = new
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalSubmissions = documentSubmissions.Count,
                    TotalDocuments = documentSubmissions.SelectMany(ds => ds.Documents).Count(),
                    UniqueUsers = documentSubmissions.Select(ds => ds.UserId).Distinct().Count()
                },
                DocumentDetails = documentSubmissions.SelectMany(ds => ds.Documents.Select(doc => new
                {
                    SubmissionId = ds.Id,
                    UserId = ds.UserId,
                    UserType = ds.UserType.ToString(),
                    DocumentId = doc.Id,
                    DocumentType = doc.DocumentType.ToString(),
                    FileName = doc.FileName,
                    Status = doc.Status.ToString(),
                    UploadedAt = doc.UploadedAt,
                    ReviewedAt = doc.ReviewedAt,
                    FileSize = doc.FileSize,
                    MimeType = doc.MimeType,
                    VerificationMethod = doc.VerificationMethod.ToString(),
                    ConfidenceScore = doc.ConfidenceScore,
                    ReviewNotes = doc.ReviewNotes,
                    RejectionReason = doc.RejectionReason
                })).ToList()
            };

            var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var metadataEntry = archive.CreateEntry("metadata.json");
            using var metadataStream = metadataEntry.Open();
            using var writer = new StreamWriter(metadataStream, Encoding.UTF8);
            await writer.WriteAsync(metadataJson);
        }

        private List<DocumentExportSummary> CreateDocumentSummary(List<DocumentSubmission> documentSubmissions)
        {
            return documentSubmissions
                .SelectMany(ds => ds.Documents.Select(doc => new DocumentExportSummary
                {
                    UserId = ds.UserId,
                    UserName = "N/A", // This could be enhanced to include actual user names
                    Email = "N/A", // This could be enhanced to include actual user emails
                    DocumentType = doc.DocumentType,
                    FileName = doc.FileName,
                    Status = doc.Status,
                    UploadedAt = doc.UploadedAt ?? DateTime.MinValue,
                    ReviewedAt = doc.ReviewedAt,
                    FilePath = doc.FilePath,
                    FileSize = doc.FileSize
                }))
                .OrderBy(d => d.UserId)
                .ThenBy(d => d.DocumentType)
                .ToList();
        }
    }
}
