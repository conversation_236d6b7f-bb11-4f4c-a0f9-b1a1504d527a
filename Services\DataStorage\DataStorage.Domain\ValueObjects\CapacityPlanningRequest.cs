using Shared.Domain.ValueObjects;

namespace DataStorage.Domain.ValueObjects;

/// <summary>
/// Request for capacity planning analysis
/// </summary>
public class CapacityPlanningRequest : ValueObject
{
    public string ServiceName { get; init; }
    public DateTime AnalysisStartDate { get; init; }
    public DateTime AnalysisEndDate { get; init; }
    public int ForecastDays { get; init; }
    public List<string> ResourceTypes { get; init; }
    public string PlanningModel { get; init; }
    public Dictionary<string, object> ModelParameters { get; init; }
    public List<string>? Scenarios { get; init; }
    public double ConfidenceLevel { get; init; }

    public CapacityPlanningRequest(
        string serviceName,
        DateTime analysisStartDate,
        DateTime analysisEndDate,
        int forecastDays,
        List<string> resourceTypes,
        string planningModel = "Linear",
        Dictionary<string, object>? modelParameters = null,
        List<string>? scenarios = null,
        double confidenceLevel = 0.95)
    {
        ServiceName = serviceName;
        AnalysisStartDate = analysisStartDate;
        AnalysisEndDate = analysisEndDate;
        ForecastDays = forecastDays;
        ResourceTypes = resourceTypes;
        PlanningModel = planningModel;
        ModelParameters = modelParameters ?? new Dictionary<string, object>();
        Scenarios = scenarios;
        ConfidenceLevel = confidenceLevel;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return ServiceName;
        yield return AnalysisStartDate;
        yield return AnalysisEndDate;
        yield return ForecastDays;
        yield return PlanningModel;
        yield return ConfidenceLevel;
        
        foreach (var resourceType in ResourceTypes.OrderBy(x => x))
        {
            yield return resourceType;
        }
        
        foreach (var param in ModelParameters.OrderBy(x => x.Key))
        {
            yield return param.Key;
            yield return param.Value?.ToString() ?? string.Empty;
        }
        
        if (Scenarios != null)
        {
            foreach (var scenario in Scenarios.OrderBy(x => x))
            {
                yield return scenario;
            }
        }
    }
}
