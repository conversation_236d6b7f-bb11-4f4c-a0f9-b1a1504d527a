using MediatR;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Queries.GetGlobalTaxConfigurations
{
    public class GetGlobalTaxConfigurationsQuery : IRequest<List<GlobalTaxConfigurationDto>>
    {
        public bool ActiveOnly { get; set; } = true;
        public string? Region { get; set; }
        public TaxType? TaxType { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
