using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using DataStorage.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.IO.Compression;
using System.Text.Json;

namespace DataStorage.Infrastructure.Services;

public class AdvancedFileProcessingService : IAdvancedFileProcessingService
{
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<AdvancedFileProcessingService> _logger;
    private readonly Dictionary<string, Func<StorageLocation, CancellationToken, Task<FileProcessingResult>>> _processors;

    public AdvancedFileProcessingService(
        IFileStorageService fileStorageService,
        ILogger<AdvancedFileProcessingService> logger)
    {
        _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _processors = InitializeProcessors();
    }

    public async Task<FileProcessingResult> ProcessComplexDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting complex document processing for {Location}", location.GetFullPath());

            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (_processors.TryGetValue(contentType, out var processor))
            {
                var result = await processor(location, cancellationToken);
                stopwatch.Stop();

                _logger.LogInformation("Complex document processing completed in {ElapsedMs}ms for {Location}",
                    stopwatch.ElapsedMilliseconds, location.GetFullPath());

                return result;
            }

            // Default processing for unsupported formats
            var basicMetadata = await ExtractAdvancedMetadataAsync(location, cancellationToken);
            stopwatch.Stop();

            return FileProcessingResult.Success(
                basicMetadata,
                new List<string>(),
                new List<string>(),
                FileComplexity.Simple,
                stopwatch.Elapsed,
                fileMetadata.SizeBytes);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing complex document at {Location}", location.GetFullPath());
            return FileProcessingResult.Failure($"Processing failed: {ex.Message}");
        }
    }

    public async Task<Dictionary<string, object>> ExtractAdvancedMetadataAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var metadata = new Dictionary<string, object>
            {
                ["FileName"] = fileMetadata.FileName,
                ["ContentType"] = fileMetadata.ContentType,
                ["SizeBytes"] = fileMetadata.SizeBytes,
                ["CreatedAt"] = fileMetadata.CreatedAt,
                ["LastModified"] = fileMetadata.LastModified
            };

            // Add file-specific metadata based on content type
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (contentType.StartsWith("image/"))
            {
                await AddImageMetadataAsync(location, metadata, cancellationToken);
            }
            else if (contentType == "application/pdf")
            {
                await AddPdfMetadataAsync(location, metadata, cancellationToken);
            }
            else if (IsOfficeDocument(contentType))
            {
                await AddOfficeDocumentMetadataAsync(location, metadata, cancellationToken);
            }

            return metadata;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting advanced metadata from {Location}", location.GetFullPath());
            return new Dictionary<string, object>();
        }
    }

    public async Task<bool> ValidateFileFormatAsync(StorageLocation location, string expectedFormat, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var actualFormat = fileMetadata.ContentType.ToLowerInvariant();

            // Direct match
            if (actualFormat == expectedFormat.ToLowerInvariant())
                return true;

            // Check file signature for more accurate validation
            using var stream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);
            return await ValidateFileSignatureAsync(stream, expectedFormat, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file format for {Location}", location.GetFullPath());
            return false;
        }
    }

    public async Task<string> ExtractTextFromOfficeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (!IsOfficeDocument(contentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, contentType);
            }

            // For now, return placeholder implementation
            // In a real implementation, you would use libraries like:
            // - DocumentFormat.OpenXml for Office documents
            // - iTextSharp for PDF
            // - Apache POI equivalent for .NET

            _logger.LogWarning("Office document text extraction not fully implemented for {ContentType}", contentType);
            return $"[Text extraction from {contentType} not yet implemented]";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from office document at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<List<string>> ExtractImagesFromOfficeDocumentAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (!IsOfficeDocument(contentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, contentType);
            }

            // Placeholder implementation
            _logger.LogWarning("Office document image extraction not fully implemented for {ContentType}", contentType);
            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting images from office document at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<DocumentStructure> AnalyzeDocumentStructureAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (!IsOfficeDocument(contentType) && contentType != "application/pdf")
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, contentType);
            }

            // Placeholder implementation
            var metadata = new DocumentMetadata(
                author: "Unknown",
                createdDate: fileMetadata.CreatedAt,
                modifiedDate: fileMetadata.LastModified,
                subject: fileMetadata.FileName,
                pageCount: 1,
                wordCount: 0);

            return new DocumentStructure(
                title: fileMetadata.FileName,
                sections: new List<DocumentSection>(),
                tables: new List<DocumentTable>(),
                images: new List<DocumentImage>(),
                charts: new List<DocumentChart>(),
                metadata: metadata);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing document structure at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<CadFileInfo> ProcessCadFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var contentType = fileMetadata.ContentType.ToLowerInvariant();

            if (!IsCadFile(contentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, contentType);
            }

            // Placeholder implementation
            // In a real implementation, you would use CAD libraries like:
            // - Open Design Alliance (ODA) SDK
            // - AutoCAD .NET API
            // - FreeCAD Python API

            var format = GetCadFormatFromContentType(contentType);
            var boundingBox = new BoundingBox(0, 0, 0, 100, 100, 100);
            var layers = new List<CadLayer>();
            var blocks = new List<CadBlock>();
            var metadata = new CadMetadata(title: fileMetadata.FileName);

            return new CadFileInfo(
                fileName: fileMetadata.FileName,
                format: format,
                version: "Unknown",
                units: CadUnits.Millimeters,
                boundingBox: boundingBox,
                layers: layers,
                blocks: blocks,
                metadata: metadata,
                entityCount: 0,
                createdDate: fileMetadata.CreatedAt,
                modifiedDate: fileMetadata.LastModified);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing CAD file at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<byte[]> GenerateCadThumbnailAsync(StorageLocation location, int width = 400, int height = 400, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);

            if (!IsCadFile(fileMetadata.ContentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            // Placeholder implementation
            _logger.LogWarning("CAD thumbnail generation not fully implemented");
            return Array.Empty<byte>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating CAD thumbnail for {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<List<CadLayer>> ExtractCadLayersAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var cadInfo = await ProcessCadFileAsync(location, cancellationToken);
            return cadInfo.Layers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting CAD layers from {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<ArchiveContents> ProcessArchiveFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);

            if (!IsArchiveFile(fileMetadata.ContentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            using var stream = await _fileStorageService.DownloadFileAsync(location, cancellationToken);

            // Handle ZIP files
            if (fileMetadata.ContentType.Contains("zip"))
            {
                return await ProcessZipArchiveAsync(stream, fileMetadata, cancellationToken);
            }

            // Placeholder for other archive formats
            _logger.LogWarning("Archive processing not fully implemented for {ContentType}", fileMetadata.ContentType);
            return new ArchiveContents(
                archiveType: fileMetadata.ContentType,
                totalSize: fileMetadata.SizeBytes,
                compressedSize: fileMetadata.SizeBytes,
                fileCount: 0,
                directoryCount: 0,
                entries: new List<ArchiveEntry>(),
                isEncrypted: false,
                createdDate: fileMetadata.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing archive file at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<EmailContent> ProcessEmailFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);

            if (!IsEmailFile(fileMetadata.ContentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            // Placeholder implementation
            // In a real implementation, you would use libraries like:
            // - MimeKit for parsing email files
            // - MailKit for email processing

            _logger.LogWarning("Email file processing not fully implemented for {ContentType}", fileMetadata.ContentType);

            return new EmailContent(
                subject: "Unknown",
                from: "<EMAIL>",
                to: new List<string>(),
                cc: new List<string>(),
                bcc: new List<string>(),
                sentDate: fileMetadata.CreatedAt,
                receivedDate: fileMetadata.CreatedAt,
                body: "[Email content extraction not yet implemented]",
                htmlBody: "",
                attachments: new List<EmailAttachment>(),
                headers: new EmailHeaders(),
                isRead: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing email file at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<DatabaseSchema> ProcessDatabaseFileAsync(StorageLocation location, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);

            if (!IsDatabaseFile(fileMetadata.ContentType))
            {
                throw new UnsupportedDocumentTypeException(fileMetadata.FileName, fileMetadata.ContentType);
            }

            // Placeholder implementation
            // In a real implementation, you would use database-specific libraries:
            // - System.Data.SQLite for SQLite files
            // - Microsoft.Data.SqlClient for SQL Server files
            // - MySql.Data for MySQL files

            _logger.LogWarning("Database file processing not fully implemented for {ContentType}", fileMetadata.ContentType);

            return new DatabaseSchema(
                databaseType: GetDatabaseTypeFromContentType(fileMetadata.ContentType),
                version: "Unknown",
                name: fileMetadata.FileName,
                tables: new List<DatabaseTable>(),
                views: new List<DatabaseView>(),
                indexes: new List<DatabaseIndex>(),
                constraints: new List<DatabaseConstraint>(),
                statistics: new DatabaseStatistics());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing database file at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<StorageLocation> ConvertToStandardFormatAsync(StorageLocation location, string targetFormat, CancellationToken cancellationToken = default)
    {
        try
        {
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(location, cancellationToken);
            var sourceFormat = fileMetadata.ContentType.ToLowerInvariant();

            _logger.LogInformation("Converting {SourceFormat} to {TargetFormat} for {Location}",
                sourceFormat, targetFormat, location.GetFullPath());

            // Placeholder implementation
            // In a real implementation, you would use format-specific conversion libraries

            _logger.LogWarning("Format conversion not fully implemented from {SourceFormat} to {TargetFormat}",
                sourceFormat, targetFormat);

            // Return the original location for now
            return location;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting file format at {Location}", location.GetFullPath());
            throw;
        }
    }

    public async Task<List<string>> GetSupportedConversionsAsync(string sourceFormat, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning about async method

        var supportedConversions = new Dictionary<string, List<string>>
        {
            ["application/pdf"] = new List<string> { "image/png", "image/jpeg", "text/plain" },
            ["image/jpeg"] = new List<string> { "image/png", "image/webp", "application/pdf" },
            ["image/png"] = new List<string> { "image/jpeg", "image/webp", "application/pdf" },
            ["text/plain"] = new List<string> { "application/pdf", "text/html" },
            ["application/msword"] = new List<string> { "application/pdf", "text/plain", "text/html" },
            ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"] =
                new List<string> { "application/pdf", "text/plain", "text/html" }
        };

        return supportedConversions.GetValueOrDefault(sourceFormat.ToLowerInvariant(), new List<string>());
    }

    // Private helper methods
    private Dictionary<string, Func<StorageLocation, CancellationToken, Task<FileProcessingResult>>> InitializeProcessors()
    {
        return new Dictionary<string, Func<StorageLocation, CancellationToken, Task<FileProcessingResult>>>
        {
            ["application/pdf"] = ProcessPdfDocumentAsync,
            ["application/msword"] = ProcessOfficeDocumentAsync,
            ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"] = ProcessOfficeDocumentAsync,
            ["application/vnd.ms-excel"] = ProcessOfficeDocumentAsync,
            ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"] = ProcessOfficeDocumentAsync,
            ["application/zip"] = ProcessArchiveDocumentAsync,
            ["application/x-zip-compressed"] = ProcessArchiveDocumentAsync,
            ["message/rfc822"] = ProcessEmailDocumentAsync,
            ["application/vnd.ms-outlook"] = ProcessEmailDocumentAsync
        };
    }

    private async Task<FileProcessingResult> ProcessPdfDocumentAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        var metadata = await ExtractAdvancedMetadataAsync(location, cancellationToken);
        var extractedText = new List<string> { "[PDF text extraction placeholder]" };

        return FileProcessingResult.Success(
            metadata,
            extractedText,
            new List<string>(),
            FileComplexity.Moderate,
            TimeSpan.FromMilliseconds(100),
            metadata.GetValueOrDefault("SizeBytes", 0L) is long size ? size : 0L);
    }

    private async Task<FileProcessingResult> ProcessOfficeDocumentAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        var metadata = await ExtractAdvancedMetadataAsync(location, cancellationToken);
        var extractedText = new List<string> { await ExtractTextFromOfficeDocumentAsync(location, cancellationToken) };
        var extractedImages = await ExtractImagesFromOfficeDocumentAsync(location, cancellationToken);

        return FileProcessingResult.Success(
            metadata,
            extractedText,
            extractedImages,
            FileComplexity.Complex,
            TimeSpan.FromMilliseconds(500),
            metadata.GetValueOrDefault("SizeBytes", 0L) is long size ? size : 0L);
    }

    private async Task<FileProcessingResult> ProcessArchiveDocumentAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        var archiveContents = await ProcessArchiveFileAsync(location, cancellationToken);
        var metadata = new Dictionary<string, object>
        {
            ["ArchiveType"] = archiveContents.ArchiveType,
            ["FileCount"] = archiveContents.FileCount,
            ["DirectoryCount"] = archiveContents.DirectoryCount,
            ["CompressionRatio"] = archiveContents.CompressionRatio,
            ["IsEncrypted"] = archiveContents.IsEncrypted
        };

        return FileProcessingResult.Success(
            metadata,
            new List<string>(),
            new List<string>(),
            FileComplexity.Moderate,
            TimeSpan.FromMilliseconds(200),
            archiveContents.TotalSize);
    }

    private async Task<FileProcessingResult> ProcessEmailDocumentAsync(StorageLocation location, CancellationToken cancellationToken)
    {
        var emailContent = await ProcessEmailFileAsync(location, cancellationToken);
        var metadata = new Dictionary<string, object>
        {
            ["Subject"] = emailContent.Subject,
            ["From"] = emailContent.From,
            ["ToCount"] = emailContent.To.Count,
            ["HasAttachments"] = emailContent.HasAttachments,
            ["AttachmentCount"] = emailContent.Attachments.Count
        };

        return FileProcessingResult.Success(
            metadata,
            new List<string> { emailContent.Body },
            new List<string>(),
            FileComplexity.Simple,
            TimeSpan.FromMilliseconds(150),
            0L);
    }

    private async Task AddImageMetadataAsync(StorageLocation location, Dictionary<string, object> metadata, CancellationToken cancellationToken)
    {
        try
        {
            // Placeholder for image metadata extraction
            // In a real implementation, you would use libraries like ImageSharp or System.Drawing
            metadata["ImageType"] = "Unknown";
            metadata["Width"] = 0;
            metadata["Height"] = 0;
            metadata["ColorDepth"] = 0;
            metadata["HasTransparency"] = false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract image metadata from {Location}", location.GetFullPath());
        }
    }

    private async Task AddPdfMetadataAsync(StorageLocation location, Dictionary<string, object> metadata, CancellationToken cancellationToken)
    {
        try
        {
            // Placeholder for PDF metadata extraction
            // In a real implementation, you would use libraries like iTextSharp or PdfSharp
            metadata["PageCount"] = 0;
            metadata["IsPasswordProtected"] = false;
            metadata["HasBookmarks"] = false;
            metadata["HasAnnotations"] = false;
            metadata["PdfVersion"] = "Unknown";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract PDF metadata from {Location}", location.GetFullPath());
        }
    }

    private async Task AddOfficeDocumentMetadataAsync(StorageLocation location, Dictionary<string, object> metadata, CancellationToken cancellationToken)
    {
        try
        {
            // Placeholder for Office document metadata extraction
            // In a real implementation, you would use DocumentFormat.OpenXml
            metadata["Author"] = "Unknown";
            metadata["Title"] = "Unknown";
            metadata["Subject"] = "Unknown";
            metadata["Keywords"] = "Unknown";
            metadata["LastModifiedBy"] = "Unknown";
            metadata["WordCount"] = 0;
            metadata["PageCount"] = 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract Office document metadata from {Location}", location.GetFullPath());
        }
    }

    private async Task<bool> ValidateFileSignatureAsync(Stream stream, string expectedFormat, CancellationToken cancellationToken)
    {
        try
        {
            var buffer = new byte[16];
            await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
            stream.Position = 0;

            // Check common file signatures
            return expectedFormat.ToLowerInvariant() switch
            {
                "application/pdf" => buffer[0] == 0x25 && buffer[1] == 0x50 && buffer[2] == 0x44 && buffer[3] == 0x46, // %PDF
                "image/jpeg" => buffer[0] == 0xFF && buffer[1] == 0xD8 && buffer[2] == 0xFF,
                "image/png" => buffer[0] == 0x89 && buffer[1] == 0x50 && buffer[2] == 0x4E && buffer[3] == 0x47,
                "application/zip" => buffer[0] == 0x50 && buffer[1] == 0x4B && (buffer[2] == 0x03 || buffer[2] == 0x05),
                _ => true // Default to true for unknown formats
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to validate file signature for format {ExpectedFormat}", expectedFormat);
            return false;
        }
    }

    private async Task<ArchiveContents> ProcessZipArchiveAsync(Stream stream, FileMetadata fileMetadata, CancellationToken cancellationToken)
    {
        try
        {
            using var archive = new ZipArchive(stream, ZipArchiveMode.Read);
            var entries = new List<ArchiveEntry>();
            long totalSize = 0;
            long compressedSize = 0;
            int fileCount = 0;
            int directoryCount = 0;

            foreach (var entry in archive.Entries)
            {
                var archiveEntry = new ArchiveEntry(
                    name: Path.GetFileName(entry.FullName),
                    fullPath: entry.FullName,
                    isDirectory: entry.FullName.EndsWith("/"),
                    size: entry.Length,
                    compressedSize: entry.CompressedLength,
                    modifiedDate: entry.LastWriteTime.DateTime);

                entries.Add(archiveEntry);
                totalSize += entry.Length;
                compressedSize += entry.CompressedLength;

                if (archiveEntry.IsDirectory)
                    directoryCount++;
                else
                    fileCount++;
            }

            return new ArchiveContents(
                archiveType: "ZIP",
                totalSize: totalSize,
                compressedSize: compressedSize,
                fileCount: fileCount,
                directoryCount: directoryCount,
                entries: entries,
                isEncrypted: false, // ZIP encryption detection would require additional logic
                createdDate: fileMetadata.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing ZIP archive");
            throw;
        }
    }

    private static bool IsOfficeDocument(string contentType)
    {
        return contentType.Contains("msword") ||
               contentType.Contains("ms-excel") ||
               contentType.Contains("ms-powerpoint") ||
               contentType.Contains("openxmlformats-officedocument");
    }

    private static bool IsCadFile(string contentType)
    {
        return contentType.Contains("dwg") ||
               contentType.Contains("dxf") ||
               contentType.Contains("dgn") ||
               contentType.Contains("step") ||
               contentType.Contains("iges") ||
               contentType.Contains("stl") ||
               contentType.Contains("obj") ||
               contentType.Contains("ply") ||
               contentType.Contains("ifc");
    }

    private static bool IsArchiveFile(string contentType)
    {
        return contentType.Contains("zip") ||
               contentType.Contains("rar") ||
               contentType.Contains("7z") ||
               contentType.Contains("tar") ||
               contentType.Contains("gzip");
    }

    private static bool IsEmailFile(string contentType)
    {
        return contentType.Contains("rfc822") ||
               contentType.Contains("outlook") ||
               contentType.Contains("eml") ||
               contentType.Contains("msg");
    }

    private static bool IsDatabaseFile(string contentType)
    {
        return contentType.Contains("sqlite") ||
               contentType.Contains("database") ||
               contentType.Contains("sql") ||
               contentType.Contains("mdb") ||
               contentType.Contains("accdb");
    }

    private static CadFileFormat GetCadFormatFromContentType(string contentType)
    {
        return contentType.ToLowerInvariant() switch
        {
            var ct when ct.Contains("dwg") => CadFileFormat.DWG,
            var ct when ct.Contains("dxf") => CadFileFormat.DXF,
            var ct when ct.Contains("dgn") => CadFileFormat.DGN,
            var ct when ct.Contains("step") => CadFileFormat.STEP,
            var ct when ct.Contains("iges") => CadFileFormat.IGES,
            var ct when ct.Contains("stl") => CadFileFormat.STL,
            var ct when ct.Contains("obj") => CadFileFormat.OBJ,
            var ct when ct.Contains("ply") => CadFileFormat.PLY,
            var ct when ct.Contains("ifc") => CadFileFormat.IFC,
            _ => CadFileFormat.Unknown
        };
    }

    private static string GetDatabaseTypeFromContentType(string contentType)
    {
        return contentType.ToLowerInvariant() switch
        {
            var ct when ct.Contains("sqlite") => "SQLite",
            var ct when ct.Contains("sql") => "SQL Server",
            var ct when ct.Contains("mdb") => "Microsoft Access",
            var ct when ct.Contains("accdb") => "Microsoft Access",
            _ => "Unknown"
        };
    }
}
