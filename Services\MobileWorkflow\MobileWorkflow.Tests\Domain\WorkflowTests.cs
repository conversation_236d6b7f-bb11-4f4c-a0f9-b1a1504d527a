using FluentAssertions;
using MobileWorkflow.Domain.Entities;
using Xunit;

namespace MobileWorkflow.Tests.Domain;

public class WorkflowTests
{
    [Fact]
    public void CreateWorkflow_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var name = "Driver Onboarding";
        var description = "Complete workflow for onboarding new drivers";
        var category = "UserOnboarding";
        var version = "1.0";
        var definition = new Dictionary<string, object>
        {
            { "steps", new List<object>
                {
                    new Dictionary<string, object> { { "id", "step1" }, { "name", "Collect Documents" } }
                }
            }
        };
        var configuration = new Dictionary<string, object> { { "timeout", "24:00:00" } };
        var triggerType = "Manual";
        var triggerConfiguration = new Dictionary<string, object>();
        var createdBy = "admin";

        // Act
        var workflow = new Workflow(
            name, description, category, version, definition, configuration,
            triggerType, triggerConfiguration, createdBy);

        // Assert
        workflow.Should().NotBeNull();
        workflow.Name.Should().Be(name);
        workflow.Description.Should().Be(description);
        workflow.Category.Should().Be(category);
        workflow.Version.Should().Be(version);
        workflow.IsActive.Should().BeTrue();
        workflow.ExecutionCount.Should().Be(0);
        workflow.CreatedBy.Should().Be(createdBy);
    }

    [Fact]
    public void StartExecution_WithValidData_ShouldCreateExecution()
    {
        // Arrange
        var workflow = CreateTestWorkflow();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object> { { "userId", triggeredBy } };

        // Act
        var execution = workflow.StartExecution(triggeredBy, inputData);

        // Assert
        execution.Should().NotBeNull();
        execution.WorkflowId.Should().Be(workflow.Id);
        execution.TriggeredBy.Should().Be(triggeredBy);
        execution.Status.Should().Be("Running");
        execution.InputData.Should().ContainKey("userId");
        workflow.ExecutionCount.Should().Be(1);
        workflow.LastExecuted.Should().NotBeNull();
    }

    [Fact]
    public void StartExecution_WhenInactive_ShouldThrowException()
    {
        // Arrange
        var workflow = CreateTestWorkflow();
        workflow.Deactivate();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object>();

        // Act & Assert
        var act = () => workflow.StartExecution(triggeredBy, inputData);
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("Cannot execute inactive workflow: *");
    }

    [Fact]
    public void UpdateDefinition_WithValidData_ShouldUpdateSuccessfully()
    {
        // Arrange
        var workflow = CreateTestWorkflow();
        var newDefinition = new Dictionary<string, object>
        {
            { "steps", new List<object>
                {
                    new Dictionary<string, object> { { "id", "step1" }, { "name", "New Step" } }
                }
            }
        };
        var newVersion = "2.0";

        // Act
        workflow.UpdateDefinition(newDefinition, newVersion);

        // Assert
        workflow.Definition.Should().BeEquivalentTo(newDefinition);
        workflow.Version.Should().Be(newVersion);
    }

    [Fact]
    public void Activate_WhenInactive_ShouldActivate()
    {
        // Arrange
        var workflow = CreateTestWorkflow();
        workflow.Deactivate();

        // Act
        workflow.Activate();

        // Assert
        workflow.IsActive.Should().BeTrue();
    }

    [Fact]
    public void Deactivate_WhenActive_ShouldDeactivate()
    {
        // Arrange
        var workflow = CreateTestWorkflow();

        // Act
        workflow.Deactivate();

        // Assert
        workflow.IsActive.Should().BeFalse();
    }

    [Fact]
    public void CanExecute_WhenActiveWithDefinition_ShouldReturnTrue()
    {
        // Arrange
        var workflow = CreateTestWorkflow();

        // Act
        var canExecute = workflow.CanExecute();

        // Assert
        canExecute.Should().BeTrue();
    }

    [Fact]
    public void CanExecute_WhenInactive_ShouldReturnFalse()
    {
        // Arrange
        var workflow = CreateTestWorkflow();
        workflow.Deactivate();

        // Act
        var canExecute = workflow.CanExecute();

        // Assert
        canExecute.Should().BeFalse();
    }

    private static Workflow CreateTestWorkflow()
    {
        return new Workflow(
            "Test Workflow",
            "Test Description",
            "Testing",
            "1.0",
            new Dictionary<string, object>
            {
                { "steps", new List<object>
                    {
                        new Dictionary<string, object> { { "id", "step1" }, { "name", "Test Step" } }
                    }
                }
            },
            new Dictionary<string, object>(),
            "Manual",
            new Dictionary<string, object>(),
            "test-user");
    }
}

public class WorkflowExecutionTests
{
    [Fact]
    public void CreateWorkflowExecution_WithValidData_ShouldCreateSuccessfully()
    {
        // Arrange
        var workflowId = Guid.NewGuid();
        var triggeredBy = Guid.NewGuid();
        var inputData = new Dictionary<string, object> { { "test", "data" } };
        var triggerSource = "API";

        // Act
        var execution = new WorkflowExecution(workflowId, triggeredBy, inputData, triggerSource);

        // Assert
        execution.Should().NotBeNull();
        execution.WorkflowId.Should().Be(workflowId);
        execution.TriggeredBy.Should().Be(triggeredBy);
        execution.Status.Should().Be("Running");
        execution.TriggerSource.Should().Be(triggerSource);
        execution.CurrentStepIndex.Should().Be(0);
        execution.InputData.Should().ContainKey("test");
    }

    [Fact]
    public void MoveToNextStep_ShouldIncrementStepIndex()
    {
        // Arrange
        var execution = CreateTestExecution();
        var stepId = "step2";

        // Act
        execution.MoveToNextStep(stepId);

        // Assert
        execution.CurrentStepIndex.Should().Be(1);
        execution.CurrentStepId.Should().Be(stepId);
    }

    [Fact]
    public void UpdateContext_ShouldAddToContext()
    {
        // Arrange
        var execution = CreateTestExecution();
        var key = "testKey";
        var value = "testValue";

        // Act
        execution.UpdateContext(key, value);

        // Assert
        execution.Context.Should().ContainKey(key);
        execution.Context[key].Should().Be(value);
    }

    [Fact]
    public void Complete_WhenRunning_ShouldCompleteSuccessfully()
    {
        // Arrange
        var execution = CreateTestExecution();

        // Act
        execution.Complete();

        // Assert
        execution.Status.Should().Be("Completed");
        execution.EndTime.Should().NotBeNull();
        execution.EndTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Complete_WhenNotRunning_ShouldThrowException()
    {
        // Arrange
        var execution = CreateTestExecution();
        execution.Complete();

        // Act & Assert
        var act = () => execution.Complete();
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("Cannot complete execution with status: Completed");
    }

    [Fact]
    public void Fail_WhenRunning_ShouldFailWithError()
    {
        // Arrange
        var execution = CreateTestExecution();
        var errorMessage = "Test error";

        // Act
        execution.Fail(errorMessage);

        // Assert
        execution.Status.Should().Be("Failed");
        execution.ErrorMessage.Should().Be(errorMessage);
        execution.EndTime.Should().NotBeNull();
    }

    [Fact]
    public void Cancel_WhenRunning_ShouldCancelSuccessfully()
    {
        // Arrange
        var execution = CreateTestExecution();

        // Act
        execution.Cancel();

        // Assert
        execution.Status.Should().Be("Cancelled");
        execution.EndTime.Should().NotBeNull();
    }

    [Fact]
    public void CreateTask_ShouldAddTaskToExecution()
    {
        // Arrange
        var execution = CreateTestExecution();
        var taskName = "Test Task";
        var taskType = "Manual";
        var parameters = new Dictionary<string, object> { { "param1", "value1" } };

        // Act
        var task = execution.CreateTask(taskName, taskType, parameters);

        // Assert
        task.Should().NotBeNull();
        task.Name.Should().Be(taskName);
        task.Type.Should().Be(taskType);
        task.WorkflowExecutionId.Should().Be(execution.Id);
        execution.Tasks.Should().Contain(task);
    }

    [Fact]
    public void GetExecutionDuration_ShouldReturnCorrectDuration()
    {
        // Arrange
        var execution = CreateTestExecution();
        Thread.Sleep(100); // Small delay

        // Act
        var duration = execution.GetExecutionDuration();

        // Assert
        duration.Should().BeGreaterThan(TimeSpan.Zero);
        duration.Should().BeLessThan(TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void IsCompleted_WhenCompleted_ShouldReturnTrue()
    {
        // Arrange
        var execution = CreateTestExecution();
        execution.Complete();

        // Act
        var isCompleted = execution.IsCompleted();

        // Assert
        isCompleted.Should().BeTrue();
    }

    [Fact]
    public void IsCompleted_WhenRunning_ShouldReturnFalse()
    {
        // Arrange
        var execution = CreateTestExecution();

        // Act
        var isCompleted = execution.IsCompleted();

        // Assert
        isCompleted.Should().BeFalse();
    }

    private static WorkflowExecution CreateTestExecution()
    {
        return new WorkflowExecution(
            Guid.NewGuid(),
            Guid.NewGuid(),
            new Dictionary<string, object> { { "test", "data" } },
            "Test");
    }
}
