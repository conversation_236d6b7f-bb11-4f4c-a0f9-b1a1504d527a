using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using MobileWorkflow.Application.DTOs;
using MobileWorkflow.Application.Services;

namespace MobileWorkflow.Application.Commands.AdvancedWorkflow
{
    public class CreateWorkflowTemplateCommandHandler : IRequestHandler<CreateWorkflowTemplateCommand, WorkflowTemplateDto>
    {
        private readonly IAdvancedWorkflowEngine _workflowEngine;
        private readonly ILogger<CreateWorkflowTemplateCommandHandler> _logger;

        public CreateWorkflowTemplateCommandHandler(
            IAdvancedWorkflowEngine workflowEngine,
            ILogger<CreateWorkflowTemplateCommandHandler> logger)
        {
            _workflowEngine = workflowEngine;
            _logger = logger;
        }

        public async Task<WorkflowTemplateDto> Handle(CreateWorkflowTemplateCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating workflow template {Name} in category {Category}",
                    request.Name, request.Category);

                var createRequest = new CreateWorkflowTemplateRequest
                {
                    Name = request.Name,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    Category = request.Category,
                    Industry = request.Industry,
                    Version = request.Version,
                    DefaultConfiguration = request.DefaultConfiguration,
                    ParameterSchema = request.ParameterSchema,
                    ValidationRules = request.ValidationRules,
                    RequiredRoles = request.RequiredRoles,
                    Prerequisites = request.Prerequisites,
                    Tags = request.Tags,
                    CreatedBy = request.CreatedBy,
                    IsPublic = request.IsPublic,
                    AllowParallelExecution = request.AllowParallelExecution,
                    DefaultTimeout = request.DefaultTimeout
                };

                var result = await _workflowEngine.CreateWorkflowTemplateAsync(createRequest, cancellationToken);

                // Add steps and transitions
                foreach (var step in request.Steps)
                {
                    await _workflowEngine.AddWorkflowStepAsync(result.Id, new CreateWorkflowStepRequest
                    {
                        Name = step.Name,
                        DisplayName = step.DisplayName,
                        StepType = step.StepType,
                        Order = step.Order,
                        Configuration = step.Configuration,
                        ConditionalLogic = step.ConditionalLogic,
                        IsRequired = step.IsRequired,
                        AllowSkip = step.AllowSkip,
                        Timeout = step.Timeout
                    }, cancellationToken);
                }

                foreach (var transition in request.Transitions)
                {
                    await _workflowEngine.AddWorkflowTransitionAsync(result.Id, new CreateWorkflowTransitionRequest
                    {
                        FromStep = transition.FromStep,
                        ToStep = transition.ToStep,
                        Condition = transition.Condition,
                        Parameters = transition.Parameters
                    }, cancellationToken);
                }

                _logger.LogInformation("Successfully created workflow template {TemplateId} with {StepCount} steps",
                    result.Id, request.Steps.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating workflow template {Name}", request.Name);
                throw;
            }
        }
    }
}
