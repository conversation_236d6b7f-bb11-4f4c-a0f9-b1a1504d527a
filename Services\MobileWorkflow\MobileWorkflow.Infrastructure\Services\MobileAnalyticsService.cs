using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Application.DTOs;
using System.Text.Json;

namespace MobileWorkflow.Infrastructure.Services;

public interface IMobileAnalyticsService
{
    Task<bool> TrackEventAsync(TrackEventRequest request, CancellationToken cancellationToken = default);
    Task<bool> TrackPerformanceAsync(TrackPerformanceRequest request, CancellationToken cancellationToken = default);
    Task<bool> ReportCrashAsync(CrashReportRequest request, CancellationToken cancellationToken = default);
    Task<UserSessionDto> StartSessionAsync(StartSessionRequest request, CancellationToken cancellationToken = default);
    Task<bool> EndSessionAsync(Guid sessionId, CancellationToken cancellationToken = default);
    Task<AnalyticsDashboardDto> GetDashboardDataAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<AnalyticsEventDto>> GetEventsAsync(Guid userId, string? eventType = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<PerformanceMetricDto>> GetPerformanceMetricsAsync(Guid userId, string? metricType = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<CrashReportDto>> GetCrashReportsAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<UserBehaviorAnalysisDto> AnalyzeUserBehaviorAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<UserBehaviorPatternDto>> GetBehaviorPatternsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<AppPerformanceReportDto> GetAppPerformanceReportAsync(string platform, string? appVersion = null, DateTime? fromDate = null, CancellationToken cancellationToken = default);
    Task<List<RealTimeMetricDto>> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default);
}

public class MobileAnalyticsService : IMobileAnalyticsService
{
    private readonly IAnalyticsEventRepository _analyticsEventRepository;
    private readonly IPerformanceMetricRepository _performanceMetricRepository;
    private readonly ICrashReportRepository _crashReportRepository;
    private readonly IUserSessionRepository _userSessionRepository;
    private readonly IUserBehaviorPatternRepository _behaviorPatternRepository;
    private readonly ILogger<MobileAnalyticsService> _logger;
    private readonly IMemoryCache _cache;

    public MobileAnalyticsService(
        IAnalyticsEventRepository analyticsEventRepository,
        IPerformanceMetricRepository performanceMetricRepository,
        ICrashReportRepository crashReportRepository,
        IUserSessionRepository userSessionRepository,
        IUserBehaviorPatternRepository behaviorPatternRepository,
        ILogger<MobileAnalyticsService> logger,
        IMemoryCache cache)
    {
        _analyticsEventRepository = analyticsEventRepository;
        _performanceMetricRepository = performanceMetricRepository;
        _crashReportRepository = crashReportRepository;
        _userSessionRepository = userSessionRepository;
        _behaviorPatternRepository = behaviorPatternRepository;
        _logger = logger;
        _cache = cache;
    }

    public async Task<bool> TrackEventAsync(TrackEventRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Tracking event {EventName} for user {UserId}", request.EventName, request.UserId);

            var analyticsEvent = new AnalyticsEvent(
                request.UserId,
                request.EventType,
                request.EventName,
                request.Category,
                request.Platform,
                request.AppVersion,
                request.SessionId);

            if (!string.IsNullOrEmpty(request.DeviceInfo))
            {
                analyticsEvent.SetDeviceInfo(request.DeviceInfo);
            }

            if (!string.IsNullOrEmpty(request.ScreenName))
            {
                analyticsEvent.SetScreenName(request.ScreenName);
            }

            if (!string.IsNullOrEmpty(request.UserAgent))
            {
                analyticsEvent.SetUserAgent(request.UserAgent);
            }

            if (!string.IsNullOrEmpty(request.IpAddress))
            {
                analyticsEvent.SetIpAddress(request.IpAddress);
            }

            if (!string.IsNullOrEmpty(request.Location))
            {
                analyticsEvent.SetLocation(request.Location);
            }

            if (request.Duration.HasValue)
            {
                analyticsEvent.SetDuration(request.Duration.Value);
            }

            if (request.Properties != null)
            {
                foreach (var property in request.Properties)
                {
                    analyticsEvent.AddProperty(property.Key, property.Value);
                }
            }

            if (request.Context != null)
            {
                foreach (var context in request.Context)
                {
                    analyticsEvent.AddContext(context.Key, context.Value);
                }
            }

            if (request.CustomDimensions != null)
            {
                foreach (var dimension in request.CustomDimensions)
                {
                    analyticsEvent.AddCustomDimension(dimension.Key, dimension.Value);
                }
            }

            _analyticsEventRepository.Add(analyticsEvent);
            await _analyticsEventRepository.SaveChangesAsync(cancellationToken);

            // Update session if provided
            if (request.SessionId.HasValue)
            {
                var session = await _userSessionRepository.GetByIdAsync(request.SessionId.Value, cancellationToken);
                if (session != null && session.IsActive)
                {
                    session.RecordEvent(analyticsEvent);
                    await _userSessionRepository.SaveChangesAsync(cancellationToken);
                }
            }

            // Analyze behavior patterns in background
            _ = Task.Run(async () => await AnalyzeBehaviorPatternsAsync(request.UserId, analyticsEvent, CancellationToken.None));

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking event {EventName} for user {UserId}", request.EventName, request.UserId);
            return false;
        }
    }

    public async Task<bool> TrackPerformanceAsync(TrackPerformanceRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Tracking performance metric {MetricName} for user {UserId}", request.MetricName, request.UserId);

            var performanceMetric = new PerformanceMetric(
                request.UserId,
                request.MetricType,
                request.MetricName,
                request.Category,
                request.Value,
                request.Unit,
                request.Platform,
                request.AppVersion,
                request.SessionId);

            if (!string.IsNullOrEmpty(request.ScreenName))
            {
                performanceMetric.SetScreenName(request.ScreenName);
            }

            if (!string.IsNullOrEmpty(request.OperationName))
            {
                performanceMetric.SetOperationName(request.OperationName);
            }

            if (request.Tags != null)
            {
                foreach (var tag in request.Tags)
                {
                    performanceMetric.AddTag(tag.Key, tag.Value);
                }
            }

            if (request.Context != null)
            {
                foreach (var context in request.Context)
                {
                    performanceMetric.AddContext(context.Key, context.Value);
                }
            }

            _performanceMetricRepository.Add(performanceMetric);
            await _performanceMetricRepository.SaveChangesAsync(cancellationToken);

            // Check for performance issues
            if (performanceMetric.IsSlowPerformance())
            {
                _logger.LogWarning("Slow performance detected: {MetricName} = {Value}{Unit} for user {UserId}",
                    request.MetricName, request.Value, request.Unit, request.UserId);

                // Could trigger alerts or notifications here
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking performance metric {MetricName} for user {UserId}", request.MetricName, request.UserId);
            return false;
        }
    }

    public async Task<bool> ReportCrashAsync(CrashReportRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogError("Crash reported for user {UserId}: {ErrorMessage}", request.UserId, request.ErrorMessage);

            var crashReport = new CrashReport(
                request.UserId,
                request.CrashType,
                request.ErrorMessage,
                request.Platform,
                request.AppVersion,
                request.DeviceInfo,
                request.IsFatal,
                request.SessionId);

            if (!string.IsNullOrEmpty(request.StackTrace))
            {
                crashReport.SetStackTrace(request.StackTrace);
            }

            if (!string.IsNullOrEmpty(request.ScreenName))
            {
                crashReport.SetScreenName(request.ScreenName);
            }

            if (request.Context != null)
            {
                foreach (var context in request.Context)
                {
                    crashReport.AddContext(context.Key, context.Value);
                }
            }

            if (request.UserActions != null)
            {
                foreach (var action in request.UserActions)
                {
                    if (action.Value is Dictionary<string, object> actionData &&
                        actionData.TryGetValue("action", out var actionName) &&
                        actionData.TryGetValue("timestamp", out var timestampObj) &&
                        DateTime.TryParse(timestampObj.ToString(), out var timestamp))
                    {
                        actionData.TryGetValue("details", out var details);
                        crashReport.AddUserAction(actionName.ToString()!, timestamp, details as Dictionary<string, object>);
                    }
                }
            }

            if (request.SystemInfo != null)
            {
                foreach (var systemInfo in request.SystemInfo)
                {
                    crashReport.AddSystemInfo(systemInfo.Key, systemInfo.Value);
                }
            }

            _crashReportRepository.Add(crashReport);
            await _crashReportRepository.SaveChangesAsync(cancellationToken);

            // Check for recurring crashes
            var recentCrashes = await _crashReportRepository.GetRecentByUserIdAsync(request.UserId, TimeSpan.FromDays(1), cancellationToken);
            if (crashReport.IsRecurringCrash(recentCrashes))
            {
                _logger.LogWarning("Recurring crash detected for user {UserId}: {ErrorMessage}", request.UserId, request.ErrorMessage);
                // Could trigger escalated alerts here
            }

            // End session if fatal crash
            if (request.IsFatal && request.SessionId.HasValue)
            {
                await EndSessionAsync(request.SessionId.Value, cancellationToken);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reporting crash for user {UserId}", request.UserId);
            return false;
        }
    }

    public async Task<UserSessionDto> StartSessionAsync(StartSessionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting session for user {UserId} on platform {Platform}", request.UserId, request.Platform);

            // End any existing active sessions for this user
            var activeSessions = await _userSessionRepository.GetActiveByUserIdAsync(request.UserId, cancellationToken);
            foreach (var activeSession in activeSessions)
            {
                activeSession.EndSession();
            }

            var session = new UserSession(
                request.UserId,
                request.SessionToken,
                request.Platform,
                request.AppVersion,
                request.DeviceInfo);

            if (!string.IsNullOrEmpty(request.IpAddress))
            {
                session.SetIpAddress(request.IpAddress);
            }

            if (!string.IsNullOrEmpty(request.Location))
            {
                session.SetLocation(request.Location);
            }

            if (!string.IsNullOrEmpty(request.UserAgent))
            {
                session.SetUserAgent(request.UserAgent);
            }

            if (request.SessionData != null)
            {
                foreach (var data in request.SessionData)
                {
                    session.AddSessionData(data.Key, data.Value);
                }
            }

            _userSessionRepository.Add(session);
            await _userSessionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Session {SessionId} started for user {UserId}", session.Id, request.UserId);

            return MapToUserSessionDto(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting session for user {UserId}", request.UserId);
            throw;
        }
    }

    public async Task<bool> EndSessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _userSessionRepository.GetByIdAsync(sessionId, cancellationToken);
            if (session == null || !session.IsActive)
            {
                return false;
            }

            session.EndSession();
            await _userSessionRepository.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Session {SessionId} ended for user {UserId}", sessionId, session.UserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending session {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<AnalyticsDashboardDto> GetDashboardDataAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var cacheKey = $"dashboard_{userId}_{from:yyyyMMdd}";

            if (_cache.TryGetValue(cacheKey, out AnalyticsDashboardDto? cachedDashboard))
            {
                return cachedDashboard!;
            }

            var events = await _analyticsEventRepository.GetByUserIdAsync(userId, from, cancellationToken);
            var performanceMetrics = await _performanceMetricRepository.GetByUserIdAsync(userId, from, cancellationToken);
            var crashReports = await _crashReportRepository.GetByUserIdAsync(userId, from, cancellationToken);
            var sessions = await _userSessionRepository.GetByUserIdAsync(userId, from, cancellationToken);

            var dashboard = new AnalyticsDashboardDto
            {
                UserId = userId,
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalEvents = events.Count,
                TotalSessions = sessions.Count,
                TotalCrashes = crashReports.Count,
                AverageSessionDuration = sessions.Count > 0 ? sessions.Average(s => s.GetDuration().TotalMinutes) : 0,
                CrashRate = sessions.Count > 0 ? (double)crashReports.Count / sessions.Count * 100 : 0,
                TopEvents = events.GroupBy(e => e.EventName)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count()),
                PerformanceMetrics = new Dictionary<string, double>
                {
                    ["avgPageLoad"] = performanceMetrics.Where(m => m.MetricType == "PageLoad").DefaultIfEmpty().Average(m => m?.Value ?? 0),
                    ["avgApiCall"] = performanceMetrics.Where(m => m.MetricType == "ApiCall").DefaultIfEmpty().Average(m => m?.Value ?? 0)
                },
                PlatformBreakdown = events.GroupBy(e => e.Platform)
                    .ToDictionary(g => g.Key, g => g.Count()),
                DailyActiveUsage = events.GroupBy(e => e.Timestamp.Date)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            _cache.Set(cacheKey, dashboard, TimeSpan.FromMinutes(15));
            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard data for user {UserId}", userId);
            return new AnalyticsDashboardDto { UserId = userId };
        }
    }

    public async Task<List<AnalyticsEventDto>> GetEventsAsync(Guid userId, string? eventType = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var events = await _analyticsEventRepository.GetByUserIdAsync(userId, from, cancellationToken);

            if (!string.IsNullOrEmpty(eventType))
            {
                events = events.Where(e => e.EventType.Equals(eventType, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return events.Select(MapToAnalyticsEventDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events for user {UserId}", userId);
            return new List<AnalyticsEventDto>();
        }
    }

    public async Task<List<PerformanceMetricDto>> GetPerformanceMetricsAsync(Guid userId, string? metricType = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var metrics = await _performanceMetricRepository.GetByUserIdAsync(userId, from, cancellationToken);

            if (!string.IsNullOrEmpty(metricType))
            {
                metrics = metrics.Where(m => m.MetricType.Equals(metricType, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return metrics.Select(MapToPerformanceMetricDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance metrics for user {UserId}", userId);
            return new List<PerformanceMetricDto>();
        }
    }

    public async Task<List<CrashReportDto>> GetCrashReportsAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var crashReports = await _crashReportRepository.GetByUserIdAsync(userId, from, cancellationToken);

            return crashReports.Select(MapToCrashReportDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting crash reports for user {UserId}", userId);
            return new List<CrashReportDto>();
        }
    }

    public async Task<UserBehaviorAnalysisDto> AnalyzeUserBehaviorAsync(Guid userId, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
            var events = await _analyticsEventRepository.GetByUserIdAsync(userId, from, cancellationToken);
            var sessions = await _userSessionRepository.GetByUserIdAsync(userId, from, cancellationToken);

            var analysis = new UserBehaviorAnalysisDto
            {
                UserId = userId,
                AnalysisDate = DateTime.UtcNow,
                TotalSessions = sessions.Count,
                AverageSessionDuration = sessions.Count > 0 ? sessions.Average(s => s.GetDuration().TotalMinutes) : 0,
                TotalEvents = events.Count,
                MostActiveHours = events.GroupBy(e => e.Timestamp.Hour)
                    .OrderByDescending(g => g.Count())
                    .Take(3)
                    .Select(g => g.Key)
                    .ToList(),
                TopScreens = events.Where(e => !string.IsNullOrEmpty(e.ScreenName))
                    .GroupBy(e => e.ScreenName!)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count()),
                UserJourney = AnalyzeUserJourney(events),
                EngagementScore = sessions.Count > 0 ? sessions.Average(s => s.GetEngagementScore()) : 0,
                RetentionIndicators = CalculateRetentionIndicators(sessions)
            };

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing user behavior for user {UserId}", userId);
            return new UserBehaviorAnalysisDto { UserId = userId };
        }
    }

    public async Task<List<UserBehaviorPatternDto>> GetBehaviorPatternsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var patterns = await _behaviorPatternRepository.GetByUserIdAsync(userId, cancellationToken);
            return patterns.Select(MapToUserBehaviorPatternDto).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting behavior patterns for user {UserId}", userId);
            return new List<UserBehaviorPatternDto>();
        }
    }

    public async Task<AppPerformanceReportDto> GetAppPerformanceReportAsync(string platform, string? appVersion = null, DateTime? fromDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var from = fromDate ?? DateTime.UtcNow.AddDays(-7);
            var cacheKey = $"app_performance_{platform}_{appVersion}_{from:yyyyMMdd}";

            if (_cache.TryGetValue(cacheKey, out AppPerformanceReportDto? cachedReport))
            {
                return cachedReport!;
            }

            var performanceMetrics = await _performanceMetricRepository.GetByPlatformAsync(platform, appVersion, from, cancellationToken);
            var crashReports = await _crashReportRepository.GetByPlatformAsync(platform, appVersion, from, cancellationToken);
            var sessions = await _userSessionRepository.GetByPlatformAsync(platform, appVersion, from, cancellationToken);

            var report = new AppPerformanceReportDto
            {
                Platform = platform,
                AppVersion = appVersion,
                FromDate = from,
                ToDate = DateTime.UtcNow,
                TotalSessions = sessions.Count,
                TotalCrashes = crashReports.Count,
                CrashRate = sessions.Count > 0 ? (double)crashReports.Count / sessions.Count * 100 : 0,
                AverageSessionDuration = sessions.Count > 0 ? sessions.Average(s => s.GetDuration().TotalMinutes) : 0,
                PerformanceMetrics = performanceMetrics.GroupBy(m => m.MetricType)
                    .ToDictionary(g => g.Key, g => new PerformanceMetricSummary
                    {
                        Average = g.Average(m => m.Value),
                        Min = g.Min(m => m.Value),
                        Max = g.Max(m => m.Value),
                        Count = g.Count()
                    }),
                TopCrashTypes = crashReports.GroupBy(c => c.CrashType)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .ToDictionary(g => g.Key, g => g.Count()),
                DailyMetrics = performanceMetrics.GroupBy(m => m.Timestamp.Date)
                    .ToDictionary(g => g.Key, g => g.Average(m => m.Value))
            };

            _cache.Set(cacheKey, report, TimeSpan.FromMinutes(30));
            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting app performance report for platform {Platform}", platform);
            return new AppPerformanceReportDto { Platform = platform };
        }
    }

    public async Task<List<RealTimeMetricDto>> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            const string cacheKey = "realtime_metrics";
            if (_cache.TryGetValue(cacheKey, out List<RealTimeMetricDto>? cachedMetrics))
            {
                return cachedMetrics!;
            }

            var now = DateTime.UtcNow;
            var last5Minutes = now.AddMinutes(-5);

            var recentEvents = await _analyticsEventRepository.GetRecentAsync(last5Minutes, cancellationToken);
            var recentCrashes = await _crashReportRepository.GetRecentAsync(last5Minutes, cancellationToken);
            var activeSessions = await _userSessionRepository.GetActiveSessionsAsync(cancellationToken);

            var metrics = new List<RealTimeMetricDto>
            {
                new() { Name = "Active Users", Value = activeSessions.Count, Unit = "count", Timestamp = now },
                new() { Name = "Events per Minute", Value = recentEvents.Count / 5.0, Unit = "count/min", Timestamp = now },
                new() { Name = "Crashes per Hour", Value = recentCrashes.Count * 12, Unit = "count/hour", Timestamp = now },
                new() { Name = "Average Session Duration", Value = activeSessions.Count > 0 ? activeSessions.Average(s => s.GetDuration().TotalMinutes) : 0, Unit = "minutes", Timestamp = now }
            };

            _cache.Set(cacheKey, metrics, TimeSpan.FromMinutes(1));
            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time metrics");
            return new List<RealTimeMetricDto>();
        }
    }

    private async Task AnalyzeBehaviorPatternsAsync(Guid userId, AnalyticsEvent analyticsEvent, CancellationToken cancellationToken)
    {
        try
        {
            // Simple pattern detection - can be enhanced with ML algorithms
            var recentEvents = await _analyticsEventRepository.GetRecentByUserIdAsync(userId, TimeSpan.FromHours(1), cancellationToken);

            // Detect navigation patterns
            if (analyticsEvent.EventType.ToLowerInvariant() == "pageview" || analyticsEvent.EventType.ToLowerInvariant() == "screenview")
            {
                await DetectNavigationPattern(userId, recentEvents, cancellationToken);
            }

            // Detect usage patterns
            if (analyticsEvent.IsUserInteractionEvent())
            {
                await DetectUsagePattern(userId, recentEvents, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing behavior patterns for user {UserId}", userId);
        }
    }

    private async Task DetectNavigationPattern(Guid userId, List<AnalyticsEvent> recentEvents, CancellationToken cancellationToken)
    {
        var navigationEvents = recentEvents.Where(e =>
            e.EventType.ToLowerInvariant() == "pageview" ||
            e.EventType.ToLowerInvariant() == "screenview")
            .OrderBy(e => e.Timestamp)
            .ToList();

        if (navigationEvents.Count >= 3)
        {
            var pattern = string.Join(" -> ", navigationEvents.Select(e => e.ScreenName ?? e.EventName));
            var patternData = new Dictionary<string, object>
            {
                ["sequence"] = navigationEvents.Select(e => e.ScreenName ?? e.EventName).ToList(),
                ["duration"] = (navigationEvents.Last().Timestamp - navigationEvents.First().Timestamp).TotalMinutes
            };

            var existingPattern = await _behaviorPatternRepository.GetByUserIdAndPatternAsync(userId, "Navigation", pattern, cancellationToken);
            if (existingPattern != null)
            {
                existingPattern.UpdatePattern(patternData, Math.Min(1.0, existingPattern.Confidence + 0.1));
            }
            else
            {
                var newPattern = new UserBehaviorPattern(userId, "Navigation", pattern, patternData, navigationEvents.First().Platform, 0.6);
                _behaviorPatternRepository.Add(newPattern);
            }

            await _behaviorPatternRepository.SaveChangesAsync(cancellationToken);
        }
    }

    private async Task DetectUsagePattern(Guid userId, List<AnalyticsEvent> recentEvents, CancellationToken cancellationToken)
    {
        var interactionEvents = recentEvents.Where(e => e.IsUserInteractionEvent()).ToList();

        if (interactionEvents.Count >= 5)
        {
            var pattern = "High Interaction";
            var patternData = new Dictionary<string, object>
            {
                ["interactionCount"] = interactionEvents.Count,
                ["timeSpan"] = (interactionEvents.Last().Timestamp - interactionEvents.First().Timestamp).TotalMinutes,
                ["interactionTypes"] = interactionEvents.GroupBy(e => e.EventType).ToDictionary(g => g.Key, g => g.Count())
            };

            var existingPattern = await _behaviorPatternRepository.GetByUserIdAndPatternAsync(userId, "Usage", pattern, cancellationToken);
            if (existingPattern != null)
            {
                existingPattern.UpdatePattern(patternData, Math.Min(1.0, existingPattern.Confidence + 0.05));
            }
            else
            {
                var newPattern = new UserBehaviorPattern(userId, "Usage", pattern, patternData, interactionEvents.First().Platform, 0.7);
                _behaviorPatternRepository.Add(newPattern);
            }

            await _behaviorPatternRepository.SaveChangesAsync(cancellationToken);
        }
    }

    private List<string> AnalyzeUserJourney(List<AnalyticsEvent> events)
    {
        return events.Where(e => !string.IsNullOrEmpty(e.ScreenName))
            .OrderBy(e => e.Timestamp)
            .Select(e => e.ScreenName!)
            .Distinct()
            .ToList();
    }

    private Dictionary<string, object> CalculateRetentionIndicators(List<UserSession> sessions)
    {
        var indicators = new Dictionary<string, object>();

        if (sessions.Count == 0)
        {
            return indicators;
        }

        var sessionsByDate = sessions.GroupBy(s => s.StartTime.Date).ToList();
        indicators["uniqueDays"] = sessionsByDate.Count();
        indicators["averageSessionsPerDay"] = sessionsByDate.Average(g => g.Count());
        indicators["longestStreak"] = CalculateLongestStreak(sessionsByDate.Select(g => g.Key).ToList());
        indicators["lastActiveDate"] = sessions.Max(s => s.StartTime);

        return indicators;
    }

    private int CalculateLongestStreak(List<DateTime> activeDates)
    {
        if (activeDates.Count == 0) return 0;

        activeDates.Sort();
        int longestStreak = 1;
        int currentStreak = 1;

        for (int i = 1; i < activeDates.Count; i++)
        {
            if ((activeDates[i] - activeDates[i - 1]).Days == 1)
            {
                currentStreak++;
                longestStreak = Math.Max(longestStreak, currentStreak);
            }
            else
            {
                currentStreak = 1;
            }
        }

        return longestStreak;
    }

    // Mapping methods
    private UserSessionDto MapToUserSessionDto(UserSession session)
    {
        return new UserSessionDto
        {
            Id = session.Id,
            UserId = session.UserId,
            SessionToken = session.SessionToken,
            StartTime = session.StartTime,
            EndTime = session.EndTime,
            Platform = session.Platform,
            AppVersion = session.AppVersion,
            DeviceInfo = session.DeviceInfo,
            IpAddress = session.IpAddress,
            Location = session.Location,
            UserAgent = session.UserAgent,
            IsActive = session.IsActive,
            EventCount = session.EventCount,
            ScreenViewCount = session.ScreenViewCount,
            InteractionCount = session.InteractionCount,
            ErrorCount = session.ErrorCount,
            Duration = session.GetDuration().TotalMinutes,
            EngagementScore = session.GetEngagementScore(),
            SessionData = session.SessionData
        };
    }

    private AnalyticsEventDto MapToAnalyticsEventDto(AnalyticsEvent analyticsEvent)
    {
        return new AnalyticsEventDto
        {
            Id = analyticsEvent.Id,
            UserId = analyticsEvent.UserId,
            SessionId = analyticsEvent.SessionId,
            EventType = analyticsEvent.EventType,
            EventName = analyticsEvent.EventName,
            Category = analyticsEvent.Category,
            Properties = analyticsEvent.Properties,
            Context = analyticsEvent.Context,
            Timestamp = analyticsEvent.Timestamp,
            Platform = analyticsEvent.Platform,
            AppVersion = analyticsEvent.AppVersion,
            DeviceInfo = analyticsEvent.DeviceInfo,
            ScreenName = analyticsEvent.ScreenName,
            UserAgent = analyticsEvent.UserAgent,
            IpAddress = analyticsEvent.IpAddress,
            Location = analyticsEvent.Location,
            Duration = analyticsEvent.Duration,
            ReferrerUrl = analyticsEvent.ReferrerUrl,
            CustomDimensions = analyticsEvent.CustomDimensions
        };
    }

    private PerformanceMetricDto MapToPerformanceMetricDto(PerformanceMetric metric)
    {
        return new PerformanceMetricDto
        {
            Id = metric.Id,
            UserId = metric.UserId,
            SessionId = metric.SessionId,
            MetricType = metric.MetricType,
            MetricName = metric.MetricName,
            Category = metric.Category,
            Value = metric.Value,
            Unit = metric.Unit,
            Timestamp = metric.Timestamp,
            Platform = metric.Platform,
            AppVersion = metric.AppVersion,
            ScreenName = metric.ScreenName,
            OperationName = metric.OperationName,
            Tags = metric.Tags,
            Context = metric.Context,
            PerformanceGrade = metric.GetPerformanceGrade()
        };
    }

    private CrashReportDto MapToCrashReportDto(CrashReport crashReport)
    {
        return new CrashReportDto
        {
            Id = crashReport.Id,
            UserId = crashReport.UserId,
            SessionId = crashReport.SessionId,
            CrashType = crashReport.CrashType,
            ErrorMessage = crashReport.ErrorMessage,
            StackTrace = crashReport.StackTrace,
            Platform = crashReport.Platform,
            AppVersion = crashReport.AppVersion,
            DeviceInfo = crashReport.DeviceInfo,
            ScreenName = crashReport.ScreenName,
            Timestamp = crashReport.Timestamp,
            IsFatal = crashReport.IsFatal,
            Context = crashReport.Context,
            UserActions = crashReport.UserActions,
            SystemInfo = crashReport.SystemInfo,
            SeverityLevel = crashReport.GetSeverityLevel()
        };
    }

    private UserBehaviorPatternDto MapToUserBehaviorPatternDto(UserBehaviorPattern pattern)
    {
        return new UserBehaviorPatternDto
        {
            Id = pattern.Id,
            UserId = pattern.UserId,
            PatternType = pattern.PatternType,
            PatternName = pattern.PatternName,
            PatternData = pattern.PatternData,
            DetectedAt = pattern.DetectedAt,
            LastUpdated = pattern.LastUpdated,
            Frequency = pattern.Frequency,
            Confidence = pattern.Confidence,
            Platform = pattern.Platform,
            IsHighConfidence = pattern.IsHighConfidence(),
            IsFrequentPattern = pattern.IsFrequentPattern()
        };
    }
}
