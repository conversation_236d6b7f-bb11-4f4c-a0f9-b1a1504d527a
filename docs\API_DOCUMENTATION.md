# TLI Microservices API Documentation

## Overview
This document provides comprehensive API documentation for the TLI Microservices platform.

## Base URLs
- **API Gateway**: `http://localhost:5000`
- **Identity Service**: `http://localhost:5001`

## Authentication
All protected endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## API Gateway Routes

### Identity Service Routes
All identity-related endpoints are accessible through the API Gateway with the `/api/v1/identity` prefix.

**Example:**
- Direct: `http://localhost:5001/api/v1/auth/login`
- Via Gateway: `http://localhost:5000/api/v1/identity/auth/login`

## Identity Service API

### Authentication Endpoints

#### POST /api/v1/auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phoneNumber": "+**********",
  "countryCode": "US",
  "firstName": "<PERSON>",
  "lastName": "Doe"
}
```

**Response (201 Created):**
```json
{
  "userId": "123e4567-e89b-12d3-a456-************"
}
```

**Error Response (400 Bad Request):**
```json
{
  "errors": {
    "Email": ["Email is already registered"],
    "Password": ["Password must contain at least one uppercase letter"]
  }
}
```

#### POST /api/v1/auth/login
Authenticate user and receive access tokens.

**Request Body:**
```json
{
  "username": "john_doe",
  "password": "SecurePassword123!",
  "deviceInfo": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
}
```

**Response (200 OK):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "def50200a1b2c3d4e5f6...",
  "expiresIn": 3600,
  "tokenType": "Bearer",
  "username": "john_doe"
}
```

**Error Response (401 Unauthorized):**
```json
{
  "error": "Invalid username or password"
}
```

#### POST /api/v1/auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "def50200a1b2c3d4e5f6..."
}
```

**Response (200 OK):**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "def50200a1b2c3d4e5f6...",
  "expiresIn": 3600,
  "tokenType": "Bearer",
  "username": "john_doe"
}
```

#### POST /api/v1/auth/logout
Logout user and invalidate tokens.

**Request Body:**
```json
{
  "refreshToken": "def50200a1b2c3d4e5f6...",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "ipAddress": "*************"
}
```

**Response (200 OK):**
```json
{
  "message": "Logged out successfully"
}
```

#### POST /api/v1/auth/confirm-email
Confirm user email address.

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "token": "confirmation-token-here"
}
```

**Response (200 OK):**
```json
{
  "message": "Email confirmed successfully"
}
```

### User Management Endpoints

#### GET /api/v1/users/{userId}
Get user details by ID. Requires authentication.

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "username": "john_doe",
  "email": "<EMAIL>",
  "emailConfirmed": true,
  "phoneNumber": "+**********",
  "phoneNumberConfirmed": false,
  "twoFactorEnabled": false,
  "status": "Active",
  "roles": ["User"],
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

#### GET /api/v1/users/{userId}/permissions/{permission}
Check if user has specific permission. Requires authentication.

**Response (200 OK):**
```json
{
  "hasPermission": true
}
```

### Token Validation Endpoints

#### POST /api/v1/auth/validate-token
Validate JWT token.

**Request Body:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "isValid": true,
  "userId": "123e4567-e89b-12d3-a456-************",
  "username": "john_doe",
  "expiresAt": "2024-01-15T11:30:00Z"
}
```

## Health Check Endpoints

### GET /health
Get overall health status of the service.

**Response (200 OK):**
```json
{
  "status": "Healthy",
  "checks": {
    "database": {
      "status": "Healthy",
      "description": "PostgreSQL database connection",
      "data": {}
    },
    "redis": {
      "status": "Healthy",
      "description": "Redis cache connection",
      "data": {}
    }
  }
}
```

### GET /health/ready
Get readiness status (service is ready to accept traffic).

### GET /health/live
Get liveness status (service is running).

## Error Handling

### Standard Error Response Format
```json
{
  "error": "Error message",
  "details": "Additional error details",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/v1/auth/login"
}
```

### HTTP Status Codes
- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required or failed
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource already exists
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

### Validation Error Response
```json
{
  "errors": {
    "Email": ["Email is required", "Email format is invalid"],
    "Password": ["Password must be at least 8 characters"]
  }
}
```

## Rate Limiting
API endpoints are rate-limited to prevent abuse:
- **Authentication endpoints**: 5 requests per minute per IP
- **General endpoints**: 100 requests per minute per user
- **Health checks**: No rate limiting

## Pagination
List endpoints support pagination using query parameters:
```
GET /api/v1/users?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc
```

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalPages": 5,
    "totalItems": 100,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Filtering and Searching
Many endpoints support filtering and searching:
```
GET /api/v1/users?search=john&status=active&role=admin
```

## API Versioning
The API uses URL versioning:
- Current version: `v1`
- Future versions: `v2`, `v3`, etc.

## CORS Policy
The API supports CORS for web applications:
- **Allowed Origins**: Configurable per environment
- **Allowed Methods**: GET, POST, PUT, DELETE, PATCH
- **Allowed Headers**: Authorization, Content-Type, Accept

## SDK and Client Libraries
Official client libraries are available for:
- .NET/C#
- JavaScript/TypeScript
- Python
- Java

## Postman Collection
A Postman collection with all API endpoints is available at:
`/docs/postman/TLI-Microservices.postman_collection.json`

## OpenAPI/Swagger
Interactive API documentation is available at:
- **API Gateway**: `http://localhost:5000/swagger`
- **Identity Service**: `http://localhost:5001/swagger`
