using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Shared.Domain.Common;

namespace CommunicationNotification.Domain.Entities;

/// <summary>
/// Analytics dashboard configuration and metrics aggregation
/// </summary>
public class AnalyticsDashboard : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public Guid UserId { get; private set; }
    public UserRole UserRole { get; private set; }
    public DashboardType Type { get; private set; }
    public bool IsDefault { get; private set; }
    public bool IsPublic { get; private set; }
    public DateTime? LastRefreshed { get; private set; }
    public List<DashboardWidget> Widgets { get; private set; }
    public Dictionary<string, string> Filters { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }

    private AnalyticsDashboard()
    {
        Widgets = new List<DashboardWidget>();
        Filters = new Dictionary<string, string>();
        Configuration = new Dictionary<string, object>();
        Name = string.Empty;
        Description = string.Empty;
    }

    public AnalyticsDashboard(
        string name,
        string description,
        Guid userId,
        UserRole userRole,
        DashboardType type,
        bool isDefault = false,
        bool isPublic = false,
        Dictionary<string, string>? filters = null,
        Dictionary<string, object>? configuration = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Dashboard name cannot be empty", nameof(name));
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        Name = name;
        Description = description;
        UserId = userId;
        UserRole = userRole;
        Type = type;
        IsDefault = isDefault;
        IsPublic = isPublic;
        Widgets = new List<DashboardWidget>();
        Filters = filters ?? new Dictionary<string, string>();
        Configuration = configuration ?? new Dictionary<string, object>();
    }

    public static AnalyticsDashboard Create(
        string name,
        string description,
        Guid userId,
        UserRole userRole,
        DashboardType type,
        bool isDefault = false,
        bool isPublic = false,
        Dictionary<string, string>? filters = null,
        Dictionary<string, object>? configuration = null)
    {
        return new AnalyticsDashboard(name, description, userId, userRole, type,
            isDefault, isPublic, filters, configuration);
    }

    public void AddWidget(DashboardWidget widget)
    {
        if (widget == null)
            throw new ArgumentNullException(nameof(widget));

        Widgets.Add(widget);
    }

    public void RemoveWidget(Guid widgetId)
    {
        var widget = Widgets.FirstOrDefault(w => w.Id == widgetId);
        if (widget != null)
        {
            Widgets.Remove(widget);
        }
    }

    public void UpdateWidget(Guid widgetId, DashboardWidget updatedWidget)
    {
        var index = Widgets.FindIndex(w => w.Id == widgetId);
        if (index >= 0)
        {
            Widgets[index] = updatedWidget;
        }
    }

    public void AddFilter(string key, string value)
    {
        Filters[key] = value;
    }

    public void RemoveFilter(string key)
    {
        Filters.Remove(key);
    }

    public void UpdateConfiguration(string key, object value)
    {
        Configuration[key] = value;
    }

    public void MarkAsRefreshed()
    {
        LastRefreshed = DateTime.UtcNow;
    }

    public void SetAsDefault()
    {
        IsDefault = true;
    }

    public void SetAsPublic(bool isPublic)
    {
        IsPublic = isPublic;
    }

    public bool NeedsRefresh(TimeSpan refreshInterval)
    {
        if (!LastRefreshed.HasValue)
            return true;

        return DateTime.UtcNow - LastRefreshed.Value > refreshInterval;
    }
}

/// <summary>
/// Dashboard widget configuration
/// </summary>
public class DashboardWidget
{
    public Guid Id { get; private set; }
    public string Title { get; private set; }
    public WidgetType Type { get; private set; }
    public int Position { get; private set; }
    public int Width { get; private set; }
    public int Height { get; private set; }
    public Dictionary<string, object> Configuration { get; private set; }
    public Dictionary<string, string> DataSources { get; private set; }
    public bool IsVisible { get; private set; }

    private DashboardWidget()
    {
        Configuration = new Dictionary<string, object>();
        DataSources = new Dictionary<string, string>();
        Title = string.Empty;
    }

    public DashboardWidget(
        string title,
        WidgetType type,
        int position,
        int width = 4,
        int height = 4,
        Dictionary<string, object>? configuration = null,
        Dictionary<string, string>? dataSources = null,
        bool isVisible = true)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Widget title cannot be empty", nameof(title));

        Id = Guid.NewGuid();
        Title = title;
        Type = type;
        Position = position;
        Width = width;
        Height = height;
        Configuration = configuration ?? new Dictionary<string, object>();
        DataSources = dataSources ?? new Dictionary<string, string>();
        IsVisible = isVisible;
    }

    public void UpdateConfiguration(string key, object value)
    {
        Configuration[key] = value;
    }

    public void SetVisibility(bool isVisible)
    {
        IsVisible = isVisible;
    }

    public void UpdatePosition(int position)
    {
        Position = position;
    }

    public void UpdateSize(int width, int height)
    {
        Width = width;
        Height = height;
    }
}

/// <summary>
/// Dashboard types
/// </summary>
public enum DashboardType
{
    Overview = 1,
    MessagePerformance = 2,
    ChannelAnalytics = 3,
    UserEngagement = 4,
    CostAnalysis = 5,
    ABTestResults = 6,
    RealTimeMonitoring = 7,
    Custom = 8
}

/// <summary>
/// Widget types for dashboard
/// </summary>
public enum WidgetType
{
    LineChart = 1,
    BarChart = 2,
    PieChart = 3,
    MetricCard = 4,
    Table = 5,
    Heatmap = 6,
    Gauge = 7,
    Timeline = 8,
    Map = 9,
    Text = 10
}


