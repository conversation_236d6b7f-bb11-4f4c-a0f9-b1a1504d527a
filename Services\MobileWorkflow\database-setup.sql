-- Mobile & Workflow Service Database Setup
-- PostgreSQL with TimescaleDB extension for time-series data

-- Create database
CREATE DATABASE mobileworkflow_db;

-- Connect to the database
\c mobileworkflow_db;

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate schemas
CREATE SCHEMA IF NOT EXISTS mobile_workflow;
SET search_path TO mobile_workflow, public;

-- Mobile Apps table
CREATE TABLE mobile_apps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    version VARCHAR(50) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    package_id VARCHAR(500) NOT NULL UNIQUE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    release_date TIMESTAMP NOT NULL,
    minimum_os_version VARCHAR(50) NOT NULL,
    features <PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT '{}',
    configuration JSONB NOT NULL DEFAULT '{}',
    supports_offline BOOLEAN NOT NULL DEFAULT false,
    download_url VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    checksum VARCHAR(128) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Mobile Sessions table
CREATE TABLE mobile_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    mobile_app_id UUID NOT NULL REFERENCES mobile_apps(id) ON DELETE CASCADE,
    device_id VARCHAR(200) NOT NULL,
    device_info VARCHAR(1000) NOT NULL,
    app_version VARCHAR(50) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    os_version VARCHAR(50) NOT NULL,
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_known_location VARCHAR(500),
    session_data JSONB NOT NULL DEFAULT '{}',
    is_offline_capable BOOLEAN NOT NULL DEFAULT false,
    last_sync_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    offline_actions_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Offline Data table (TimescaleDB hypertable for time-series data)
CREATE TABLE offline_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    mobile_session_id UUID NOT NULL REFERENCES mobile_sessions(id) ON DELETE CASCADE,
    mobile_app_id UUID REFERENCES mobile_apps(id) ON DELETE SET NULL,
    data_type VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    data JSONB NOT NULL,
    created_offline TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    synced_at TIMESTAMP,
    is_synced BOOLEAN NOT NULL DEFAULT false,
    sync_error VARCHAR(1000),
    sync_attempts INTEGER NOT NULL DEFAULT 0,
    priority INTEGER NOT NULL DEFAULT 2,
    conflict_resolution VARCHAR(100),
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Convert offline_data to TimescaleDB hypertable
SELECT create_hypertable('offline_data', 'created_offline', if_not_exists => TRUE);

-- Workflows table
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    description VARCHAR(1000) NOT NULL,
    category VARCHAR(100) NOT NULL,
    version VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    definition JSONB NOT NULL,
    configuration JSONB NOT NULL DEFAULT '{}',
    trigger_type VARCHAR(50) NOT NULL,
    trigger_configuration JSONB NOT NULL DEFAULT '{}',
    created_by VARCHAR(200) NOT NULL,
    last_executed TIMESTAMP,
    execution_count INTEGER NOT NULL DEFAULT 0,
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Workflow Executions table (TimescaleDB hypertable for time-series data)
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    triggered_by UUID NOT NULL,
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'Running',
    input_data JSONB NOT NULL DEFAULT '{}',
    output_data JSONB NOT NULL DEFAULT '{}',
    context JSONB NOT NULL DEFAULT '{}',
    error_message VARCHAR(2000),
    trigger_source VARCHAR(200),
    current_step_index INTEGER NOT NULL DEFAULT 0,
    current_step_id VARCHAR(200),
    execution_log JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Convert workflow_executions to TimescaleDB hypertable
SELECT create_hypertable('workflow_executions', 'start_time', if_not_exists => TRUE);

-- Workflow Tasks table
CREATE TABLE workflow_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES workflows(id) ON DELETE SET NULL,
    workflow_execution_id UUID REFERENCES workflow_executions(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description VARCHAR(1000) NOT NULL DEFAULT '',
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Pending',
    priority INTEGER NOT NULL DEFAULT 2,
    assigned_to UUID,
    assigned_to_role VARCHAR(100),
    due_date TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    parameters JSONB NOT NULL DEFAULT '{}',
    result JSONB NOT NULL DEFAULT '{}',
    error_message VARCHAR(2000),
    metadata JSONB NOT NULL DEFAULT '{}',
    sla INTERVAL,
    is_overdue BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_mobile_apps_platform ON mobile_apps(platform);
CREATE INDEX idx_mobile_apps_is_active ON mobile_apps(is_active);
CREATE INDEX idx_mobile_apps_package_id ON mobile_apps(package_id);

CREATE INDEX idx_mobile_sessions_user_id ON mobile_sessions(user_id);
CREATE INDEX idx_mobile_sessions_device_id ON mobile_sessions(device_id);
CREATE INDEX idx_mobile_sessions_is_active ON mobile_sessions(is_active);
CREATE INDEX idx_mobile_sessions_platform ON mobile_sessions(platform);

CREATE INDEX idx_offline_data_user_id ON offline_data(user_id);
CREATE INDEX idx_offline_data_is_synced ON offline_data(is_synced);
CREATE INDEX idx_offline_data_data_type ON offline_data(data_type);
CREATE INDEX idx_offline_data_priority ON offline_data(priority);
CREATE INDEX idx_offline_data_created_offline ON offline_data(created_offline);

CREATE INDEX idx_workflows_name ON workflows(name);
CREATE INDEX idx_workflows_category ON workflows(category);
CREATE INDEX idx_workflows_is_active ON workflows(is_active);
CREATE INDEX idx_workflows_trigger_type ON workflows(trigger_type);

CREATE INDEX idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_triggered_by ON workflow_executions(triggered_by);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_executions_start_time ON workflow_executions(start_time);

CREATE INDEX idx_workflow_tasks_assigned_to ON workflow_tasks(assigned_to);
CREATE INDEX idx_workflow_tasks_assigned_to_role ON workflow_tasks(assigned_to_role);
CREATE INDEX idx_workflow_tasks_status ON workflow_tasks(status);
CREATE INDEX idx_workflow_tasks_priority ON workflow_tasks(priority);
CREATE INDEX idx_workflow_tasks_due_date ON workflow_tasks(due_date);
CREATE INDEX idx_workflow_tasks_is_overdue ON workflow_tasks(is_overdue);

-- Create composite indexes for common queries
CREATE INDEX idx_mobile_sessions_user_active ON mobile_sessions(user_id, is_active);
CREATE INDEX idx_offline_data_user_synced ON offline_data(user_id, is_synced);
CREATE INDEX idx_workflow_executions_workflow_status ON workflow_executions(workflow_id, status);

-- Create triggers for updating updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_mobile_apps_updated_at BEFORE UPDATE ON mobile_apps FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_mobile_sessions_updated_at BEFORE UPDATE ON mobile_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offline_data_updated_at BEFORE UPDATE ON offline_data FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_executions_updated_at BEFORE UPDATE ON workflow_executions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_tasks_updated_at BEFORE UPDATE ON workflow_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically check for overdue tasks
CREATE OR REPLACE FUNCTION check_overdue_tasks()
RETURNS VOID AS $$
BEGIN
    UPDATE workflow_tasks 
    SET is_overdue = true 
    WHERE due_date < CURRENT_TIMESTAMP 
    AND status NOT IN ('Completed', 'Failed', 'Cancelled')
    AND is_overdue = false;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to check for overdue tasks (requires pg_cron extension)
-- SELECT cron.schedule('check-overdue-tasks', '*/5 * * * *', 'SELECT check_overdue_tasks();');

-- Insert sample data for testing
INSERT INTO mobile_apps (name, version, platform, package_id, release_date, minimum_os_version, supports_offline, download_url, file_size, checksum, features, configuration) VALUES
('TLI Driver App', '1.0.0', 'Android', 'com.tli.driver.android', CURRENT_TIMESTAMP, '8.0', true, 'https://play.google.com/store/apps/details?id=com.tli.driver', 25000000, 'abc123def456', 
 '{"offline_mode": true, "gps_tracking": true, "camera_access": true, "push_notifications": true}',
 '{"sync_interval": 300, "max_offline_storage": 100, "auto_sync": true}'),
('TLI Driver App', '1.0.0', 'iOS', 'com.tli.driver.ios', CURRENT_TIMESTAMP, '14.0', true, 'https://apps.apple.com/app/tli-driver/id123456789', 30000000, 'def456ghi789',
 '{"offline_mode": true, "gps_tracking": true, "camera_access": true, "push_notifications": true, "biometric_auth": true}',
 '{"sync_interval": 300, "max_offline_storage": 100, "auto_sync": true}'),
('TLI Web App', '1.0.0', 'Web', 'com.tli.webapp', CURRENT_TIMESTAMP, 'Chrome 90+', true, 'https://app.tli.com', 5000000, 'ghi789jkl012',
 '{"pwa_support": true, "offline_mode": true, "service_worker": true, "web_push": true}',
 '{"cache_strategy": "CacheFirst", "offline_fallback": "/offline.html"}');

-- Insert sample workflows
INSERT INTO workflows (name, description, category, version, definition, trigger_type, trigger_configuration, created_by) VALUES
('Driver Onboarding', 'Complete workflow for onboarding new drivers', 'UserOnboarding', '1.0', 
 '{"steps": [
   {"id": "collect_documents", "name": "Collect Documents", "type": "Manual", "parameters": {"required_docs": ["license", "aadhar", "pan"]}},
   {"id": "verify_identity", "name": "Verify Identity", "type": "Automated", "parameters": {"verification_service": "aadhar_api"}},
   {"id": "background_check", "name": "Background Check", "type": "Automated", "parameters": {"check_type": "criminal_record"}},
   {"id": "final_approval", "name": "Final Approval", "type": "Approval", "parameters": {"approvers": ["admin", "hr_manager"]}}
 ]}',
 'Manual', '{}', 'system'),
('Trip Completion', 'Automated workflow for trip completion and payment processing', 'TripManagement', '1.0',
 '{"steps": [
   {"id": "verify_pod", "name": "Verify POD", "type": "Automated", "parameters": {"validation_rules": ["signature_present", "photos_uploaded"]}},
   {"id": "update_status", "name": "Update Trip Status", "type": "Automated", "parameters": {"new_status": "completed"}},
   {"id": "process_payment", "name": "Process Payment", "type": "Automated", "parameters": {"payment_type": "trip_completion"}},
   {"id": "send_notification", "name": "Send Completion Notification", "type": "Notification", "parameters": {"recipients": ["customer", "driver", "admin"]}}
 ]}',
 'Event', '{"trigger_event": "pod_uploaded"}', 'system');

-- Create retention policies for TimescaleDB hypertables
-- Keep offline_data for 90 days
SELECT add_retention_policy('offline_data', INTERVAL '90 days');

-- Keep workflow_executions for 1 year
SELECT add_retention_policy('workflow_executions', INTERVAL '1 year');

-- Create continuous aggregates for analytics
CREATE MATERIALIZED VIEW daily_offline_sync_stats
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', created_offline) AS day,
    data_type,
    COUNT(*) AS total_items,
    COUNT(*) FILTER (WHERE is_synced = true) AS synced_items,
    COUNT(*) FILTER (WHERE is_synced = false) AS pending_items,
    AVG(sync_attempts) AS avg_sync_attempts
FROM offline_data
GROUP BY day, data_type;

CREATE MATERIALIZED VIEW daily_workflow_execution_stats
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', start_time) AS day,
    workflow_id,
    COUNT(*) AS total_executions,
    COUNT(*) FILTER (WHERE status = 'Completed') AS completed_executions,
    COUNT(*) FILTER (WHERE status = 'Failed') AS failed_executions,
    AVG(EXTRACT(EPOCH FROM (COALESCE(end_time, CURRENT_TIMESTAMP) - start_time))) AS avg_duration_seconds
FROM workflow_executions
GROUP BY day, workflow_id;

-- Add refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy('daily_offline_sync_stats',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('daily_workflow_execution_stats',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

-- Grant permissions (adjust as needed for your environment)
-- CREATE ROLE mobileworkflow_user WITH LOGIN PASSWORD 'your_password_here';
-- GRANT USAGE ON SCHEMA mobile_workflow TO mobileworkflow_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA mobile_workflow TO mobileworkflow_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA mobile_workflow TO mobileworkflow_user;

COMMIT;
