using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class PlanTaxConfigurationConfiguration : IEntityTypeConfiguration<PlanTaxConfiguration>
    {
        public void Configure(EntityTypeBuilder<PlanTaxConfiguration> builder)
        {
            builder.ToTable("PlanTaxConfigurations");

            builder.<PERSON><PERSON>ey(ptc => ptc.Id);

            builder.Property(ptc => ptc.PlanId)
                .IsRequired();

            builder.Property(ptc => ptc.IsActive)
                .IsRequired();

            builder.Property(ptc => ptc.EffectiveFrom)
                .IsRequired();

            builder.Property(ptc => ptc.EffectiveTo);

            builder.Property(ptc => ptc.Notes)
                .HasMaxLength(1000);

            builder.Property(ptc => ptc.CreatedAt)
                .IsRequired();

            builder.Property(ptc => ptc.UpdatedAt);

            // Configure TaxConfiguration as owned entity
            builder.OwnsOne(ptc => ptc.TaxConfiguration, taxConfig =>
            {
                taxConfig.Property(c => c.TaxType)
                    .HasColumnName("TaxType")
                    .HasConversion<string>()
                    .IsRequired();

                taxConfig.Property(c => c.Rate)
                    .HasColumnName("Rate")
                    .HasColumnType("decimal(5,4)")
                    .IsRequired();

                taxConfig.Property(c => c.IsIncluded)
                    .HasColumnName("IsIncluded")
                    .IsRequired();

                taxConfig.Property(c => c.EffectiveDate)
                    .HasColumnName("TaxEffectiveDate")
                    .IsRequired();

                taxConfig.Property(c => c.ExpirationDate)
                    .HasColumnName("TaxExpirationDate");

                // Store ApplicableRegions as JSON
                taxConfig.Property(c => c.ApplicableRegions)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                    .HasColumnName("ApplicableRegions")
                    .HasMaxLength(1000);
            });

            // Configure relationship with Plan
            builder.HasOne(ptc => ptc.Plan)
                .WithMany()
                .HasForeignKey(ptc => ptc.PlanId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(ptc => ptc.PlanId);

            builder.HasIndex(ptc => ptc.IsActive);

            builder.HasIndex(ptc => ptc.EffectiveFrom);

            builder.HasIndex(ptc => ptc.EffectiveTo);

            builder.HasIndex(ptc => ptc.CreatedAt);

            builder.HasIndex("TaxType");

            // Composite indexes for common queries
            builder.HasIndex(ptc => new { ptc.PlanId, ptc.IsActive });

            builder.HasIndex(ptc => new { ptc.PlanId, ptc.EffectiveFrom, ptc.EffectiveTo });

            builder.HasIndex("PlanId", "TaxType")
                .IsUnique()
                .HasFilter("IsActive = true");
        }
    }
}
