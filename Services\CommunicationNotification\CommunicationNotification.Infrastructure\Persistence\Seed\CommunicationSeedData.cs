using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace CommunicationNotification.Infrastructure.Persistence.Seed;

/// <summary>
/// Seed data for communication and notification service
/// </summary>
public static class CommunicationSeedData
{
    /// <summary>
    /// Seed initial data
    /// </summary>
    public static async Task SeedAsync(CommunicationDbContext context, ILogger logger)
    {
        try
        {
            logger.LogInformation("Starting communication service seed data");

            // Seed templates
            await SeedTemplatesAsync(context, logger);

            // Seed user preferences for test users
            await SeedUserPreferencesAsync(context, logger);

            // Seed sample conversation threads
            await SeedConversationThreadsAsync(context, logger);

            // Seed compliance records
            await SeedComplianceRecordsAsync(context, logger);

            await context.SaveChangesAsync();
            logger.LogInformation("Communication service seed data completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error seeding communication service data");
            throw;
        }
    }

    private static async Task SeedTemplatesAsync(CommunicationDbContext context, ILogger logger)
    {
        if (await context.Templates.AnyAsync())
        {
            logger.LogInformation("Templates already exist, skipping seed");
            return;
        }

        logger.LogInformation("Seeding communication templates");

        var templates = new List<Template>
        {
            // Trip Confirmation Templates
            new Template
            {
                Id = Guid.NewGuid(),
                Name = "trip_confirmation_sms",
                Description = "SMS template for trip confirmation",
                MessageType = MessageType.TripConfirmation,
                Channel = NotificationChannel.Sms,
                Language = Language.English,
                Subject = null,
                Body = "Trip confirmed! Driver: {{driver_name}}, Vehicle: {{vehicle_number}}. Pickup: {{pickup_time}} at {{pickup_location}}. Contact: {{driver_phone}}",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["driver_name"] = "Driver's name",
                    ["vehicle_number"] = "Vehicle registration number",
                    ["pickup_time"] = "Pickup time",
                    ["pickup_location"] = "Pickup location",
                    ["driver_phone"] = "Driver's phone number"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "trip",
                    ["priority"] = "high",
                    ["max_length"] = "160"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new Template
            {
                Id = Guid.NewGuid(),
                Name = "trip_confirmation_whatsapp",
                Description = "WhatsApp template for trip confirmation",
                MessageType = MessageType.TripConfirmation,
                Channel = NotificationChannel.WhatsApp,
                Language = Language.English,
                Subject = "Trip Confirmation",
                Body = "🚛 *Trip Confirmed!*\n\n📋 Booking: {{booking_id}}\n👨‍💼 Driver: {{driver_name}}\n🚗 Vehicle: {{vehicle_number}}\n⏰ Pickup: {{pickup_time}}\n📍 Location: {{pickup_location}}\n📞 Contact: {{driver_phone}}\n\nHave a safe journey! 🛣️",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["booking_id"] = "Booking reference ID",
                    ["driver_name"] = "Driver's name",
                    ["vehicle_number"] = "Vehicle registration number",
                    ["pickup_time"] = "Pickup time",
                    ["pickup_location"] = "Pickup location",
                    ["driver_phone"] = "Driver's phone number"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "trip",
                    ["priority"] = "high",
                    ["supports_media"] = "true"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            new Template
            {
                Id = Guid.NewGuid(),
                Name = "trip_confirmation_kannada",
                Description = "Kannada SMS template for trip confirmation",
                MessageType = MessageType.TripConfirmation,
                Channel = NotificationChannel.Sms,
                Language = Language.Kannada,
                Subject = null,
                Body = "ಪ್ರಯಾಣ ದೃಢೀಕರಣ! ಚಾಲಕ: {{driver_name}}, ವಾಹನ: {{vehicle_number}}. ಪಿಕಪ್: {{pickup_time}} ನಲ್ಲಿ {{pickup_location}}. ಸಂಪರ್ಕ: {{driver_phone}}",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["driver_name"] = "ಚಾಲಕನ ಹೆಸರು",
                    ["vehicle_number"] = "ವಾಹನ ನಂಬರ್",
                    ["pickup_time"] = "ಪಿಕಪ್ ಸಮಯ",
                    ["pickup_location"] = "ಪಿಕಪ್ ಸ್ಥಳ",
                    ["driver_phone"] = "ಚಾಲಕನ ಫೋನ್ ನಂಬರ್"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "trip",
                    ["priority"] = "high",
                    ["language"] = "kannada"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Trip Update Templates
            new Template
            {
                Id = Guid.NewGuid(),
                Name = "trip_update_location",
                Description = "Location update during trip",
                MessageType = MessageType.TripUpdate,
                Channel = NotificationChannel.Push,
                Language = Language.English,
                Subject = "Trip Update",
                Body = "Your driver is {{distance}} away and will arrive in approximately {{eta}} minutes.",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["distance"] = "Distance from pickup location",
                    ["eta"] = "Estimated time of arrival"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "trip",
                    ["priority"] = "normal",
                    ["real_time"] = "true"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Emergency Templates
            new Template
            {
                Id = Guid.NewGuid(),
                Name = "emergency_breakdown",
                Description = "Emergency notification for vehicle breakdown",
                MessageType = MessageType.Emergency,
                Channel = NotificationChannel.Voice,
                Language = Language.English,
                Subject = "Emergency Alert",
                Body = "Emergency alert: Vehicle breakdown reported for trip {{trip_id}} at location {{location}}. Driver {{driver_name}} requires immediate assistance. Contact emergency support at {{support_number}}.",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["trip_id"] = "Trip identification number",
                    ["location"] = "Breakdown location",
                    ["driver_name"] = "Driver's name",
                    ["support_number"] = "Emergency support number"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "emergency",
                    ["priority"] = "critical",
                    ["escalation"] = "true"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Marketing Templates
            new Template
            {
                Id = Guid.NewGuid(),
                Name = "weekly_opportunities",
                Description = "Weekly trip opportunities for drivers",
                MessageType = MessageType.Marketing,
                Channel = NotificationChannel.Email,
                Language = Language.English,
                Subject = "Weekly Trip Opportunities - {{week_date}}",
                Body = @"<h2>New Trip Opportunities This Week</h2>
                        <p>Dear {{driver_name}},</p>
                        <p>We have {{opportunity_count}} new trip opportunities in your area:</p>
                        <ul>
                        {{#opportunities}}
                        <li>{{route}} - {{date}} - ₹{{fare}}</li>
                        {{/opportunities}}
                        </ul>
                        <p>Login to your app to view details and accept trips.</p>
                        <p>Best regards,<br>TLI Team</p>",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["driver_name"] = "Driver's name",
                    ["week_date"] = "Week starting date",
                    ["opportunity_count"] = "Number of opportunities",
                    ["opportunities"] = "List of trip opportunities"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "marketing",
                    ["priority"] = "low",
                    ["frequency"] = "weekly"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // System Notification Templates
            new Template
            {
                Id = Guid.NewGuid(),
                Name = "account_verification",
                Description = "Account verification notification",
                MessageType = MessageType.AccountVerification,
                Channel = NotificationChannel.Email,
                Language = Language.English,
                Subject = "Verify Your TLI Account",
                Body = @"<h2>Welcome to TLI!</h2>
                        <p>Dear {{user_name}},</p>
                        <p>Thank you for registering with TLI. Please verify your account by clicking the link below:</p>
                        <p><a href='{{verification_link}}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Verify Account</a></p>
                        <p>This link will expire in {{expiry_hours}} hours.</p>
                        <p>If you didn't create this account, please ignore this email.</p>
                        <p>Best regards,<br>TLI Team</p>",
                IsActive = true,
                Variables = new Dictionary<string, string>
                {
                    ["user_name"] = "User's full name",
                    ["verification_link"] = "Account verification URL",
                    ["expiry_hours"] = "Link expiry time in hours"
                },
                Metadata = new Dictionary<string, string>
                {
                    ["category"] = "system",
                    ["priority"] = "high",
                    ["security"] = "true"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Templates.AddRangeAsync(templates);
        logger.LogInformation("Seeded {Count} communication templates", templates.Count);
    }

    private static async Task SeedUserPreferencesAsync(CommunicationDbContext context, ILogger logger)
    {
        if (await context.UserPreferences.AnyAsync())
        {
            logger.LogInformation("User preferences already exist, skipping seed");
            return;
        }

        logger.LogInformation("Seeding user preferences");

        var userPreferences = new List<UserPreference>
        {
            // Admin user preferences
            new UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse("11111111-1111-1111-1111-111111111111"), // Admin user
                UserRole = UserRole.Admin,
                PreferredLanguage = Language.English,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "admin_device_token"
                },
                NotificationSettings = new NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = true,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = true,
                    QuietHoursStart = TimeSpan.FromHours(22), // 10 PM
                    QuietHoursEnd = TimeSpan.FromHours(7),    // 7 AM
                    PreferredChannels = new List<NotificationChannel>
                    {
                        NotificationChannel.Email,
                        NotificationChannel.Push,
                        NotificationChannel.WhatsApp
                    }
                },
                CustomSettings = new Dictionary<string, string>
                {
                    ["emergency_escalation"] = "true",
                    ["system_alerts"] = "true",
                    ["marketing_emails"] = "false"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Driver user preferences
            new UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse("*************-2222-2222-************"), // Driver user
                UserRole = UserRole.Driver,
                PreferredLanguage = Language.Kannada,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "driver_device_token"
                },
                NotificationSettings = new NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = false,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = false,
                    QuietHoursStart = TimeSpan.FromHours(23), // 11 PM
                    QuietHoursEnd = TimeSpan.FromHours(6),    // 6 AM
                    PreferredChannels = new List<NotificationChannel>
                    {
                        NotificationChannel.WhatsApp,
                        NotificationChannel.Push,
                        NotificationChannel.Sms
                    }
                },
                CustomSettings = new Dictionary<string, string>
                {
                    ["trip_notifications"] = "true",
                    ["location_sharing"] = "true",
                    ["marketing_emails"] = "false"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Customer user preferences
            new UserPreference
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse("*************-3333-3333-************"), // Customer user
                UserRole = UserRole.Customer,
                PreferredLanguage = Language.English,
                TimeZone = "Asia/Kolkata",
                IsOnline = false,
                ContactInfo = new ContactInfo
                {
                    PhoneNumber = "+************",
                    Email = "<EMAIL>",
                    WhatsAppNumber = "+************",
                    PushDeviceToken = "customer_device_token"
                },
                NotificationSettings = new NotificationSettings
                {
                    EnableSms = true,
                    EnableEmail = true,
                    EnablePush = true,
                    EnableWhatsApp = true,
                    EnableVoice = false,
                    QuietHoursStart = TimeSpan.FromHours(21), // 9 PM
                    QuietHoursEnd = TimeSpan.FromHours(8),    // 8 AM
                    PreferredChannels = new List<NotificationChannel>
                    {
                        NotificationChannel.WhatsApp,
                        NotificationChannel.Email,
                        NotificationChannel.Push
                    }
                },
                CustomSettings = new Dictionary<string, string>
                {
                    ["booking_confirmations"] = "true",
                    ["trip_updates"] = "true",
                    ["promotional_offers"] = "true"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.UserPreferences.AddRangeAsync(userPreferences);
        logger.LogInformation("Seeded {Count} user preferences", userPreferences.Count);
    }

    private static async Task SeedConversationThreadsAsync(CommunicationDbContext context, ILogger logger)
    {
        if (await context.ConversationThreads.AnyAsync())
        {
            logger.LogInformation("Conversation threads already exist, skipping seed");
            return;
        }

        logger.LogInformation("Seeding conversation threads");

        var adminUserId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var driverUserId = Guid.Parse("*************-2222-2222-************");
        var customerUserId = Guid.Parse("*************-3333-3333-************");

        var conversationThreads = new List<ConversationThread>
        {
            new ConversationThread
            {
                Id = Guid.NewGuid(),
                Title = "Trip Support - Bangalore to Mysore",
                Description = "Customer support conversation for trip booking",
                Type = ConversationType.Support,
                RelatedEntityId = Guid.NewGuid(), // Trip ID
                RelatedEntityType = "Trip",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = customerUserId,
                LastActivity = DateTime.UtcNow,
                Metadata = new Dictionary<string, string>
                {
                    ["trip_route"] = "Bangalore to Mysore",
                    ["priority"] = "normal",
                    ["category"] = "support"
                },
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.ConversationThreads.AddRangeAsync(conversationThreads);

        // Add participants for the conversation
        var participants = new List<ConversationParticipant>
        {
            new ConversationParticipant
            {
                Id = Guid.NewGuid(),
                ConversationThreadId = conversationThreads[0].Id,
                UserId = customerUserId,
                Role = ParticipantRole.Customer,
                JoinedAt = DateTime.UtcNow,
                CanWrite = true,
                CanRead = true,
                IsActive = true,
                NotificationSettings = new Dictionary<string, object>
                {
                    ["notify_on_message"] = true,
                    ["notify_on_join"] = false
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new ConversationParticipant
            {
                Id = Guid.NewGuid(),
                ConversationThreadId = conversationThreads[0].Id,
                UserId = adminUserId,
                Role = ParticipantRole.Support,
                JoinedAt = DateTime.UtcNow,
                CanWrite = true,
                CanRead = true,
                IsActive = true,
                NotificationSettings = new Dictionary<string, object>
                {
                    ["notify_on_message"] = true,
                    ["notify_on_join"] = true
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.ConversationParticipants.AddRangeAsync(participants);
        logger.LogInformation("Seeded {Count} conversation threads with participants", conversationThreads.Count);
    }

    private static async Task SeedComplianceRecordsAsync(CommunicationDbContext context, ILogger logger)
    {
        if (await context.ComplianceRecords.AnyAsync())
        {
            logger.LogInformation("Compliance records already exist, skipping seed");
            return;
        }

        logger.LogInformation("Seeding compliance records");

        var customerUserId = Guid.Parse("*************-3333-3333-************");

        var complianceRecords = new List<ComplianceRecord>
        {
            new ComplianceRecord
            {
                Id = Guid.NewGuid(),
                UserId = customerUserId,
                ComplianceType = ComplianceType.GDPR,
                Action = "ConsentGiven",
                Timestamp = DateTime.UtcNow,
                ConsentGiven = true,
                ConsentTimestamp = DateTime.UtcNow,
                ConsentMethod = "WebForm",
                ConsentVersion = "1.0",
                DataProcessed = "Personal information, communication preferences, trip history",
                LegalBasis = "Consent",
                RetentionPeriod = TimeSpan.FromDays(2555), // 7 years
                ExpiresAt = DateTime.UtcNow.AddYears(7),
                IsActive = true,
                ComplianceData = new Dictionary<string, object>
                {
                    ["consent_form_id"] = "CF_001",
                    ["ip_address"] = "*************",
                    ["user_agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.ComplianceRecords.AddRangeAsync(complianceRecords);
        logger.LogInformation("Seeded {Count} compliance records", complianceRecords.Count);
    }
}
