using CommunicationNotification.Tests.Infrastructure;
using FluentAssertions;
using NBomber.Contracts;
using NBomber.CSharp;
using System.Diagnostics;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace CommunicationNotification.Tests.Performance;

/// <summary>
/// Performance tests for notification service
/// </summary>
public class NotificationPerformanceTests : IntegrationTestBase
{
    public NotificationPerformanceTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task SendNotification_SingleRequest_ShouldCompleteWithinTimeout()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var request = new
        {
            UserId = TestConstants.TestCustomerId,
            Content = "Performance test notification",
            MessageType = "SystemNotification",
            Priority = "Normal",
            PreferredChannel = "Push"
        };

        var stopwatch = Stopwatch.StartNew();

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send", request);

        // Assert
        stopwatch.Stop();
        response.IsSuccessStatusCode.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds

        WriteOutput($"Single notification request completed in {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task SendNotification_ConcurrentRequests_ShouldMaintainPerformance()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        const int concurrentRequests = 50;
        var tasks = new List<Task<HttpResponseMessage>>();
        var stopwatch = Stopwatch.StartNew();

        // Act
        for (int i = 0; i < concurrentRequests; i++)
        {
            var request = new
            {
                UserId = TestConstants.TestCustomerId,
                Content = $"Concurrent performance test notification {i}",
                MessageType = "SystemNotification",
                Priority = "Normal",
                PreferredChannel = "Push"
            };

            tasks.Add(client.PostAsJsonAsync("/api/notifications/send", request));
        }

        var responses = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        responses.Should().HaveCount(concurrentRequests);
        responses.Should().OnlyContain(r => r.IsSuccessStatusCode);
        
        var avgTimePerRequest = stopwatch.ElapsedMilliseconds / (double)concurrentRequests;
        avgTimePerRequest.Should().BeLessThan(1000); // Average should be less than 1 second per request

        WriteOutput($"Processed {concurrentRequests} concurrent requests in {stopwatch.ElapsedMilliseconds}ms");
        WriteOutput($"Average time per request: {avgTimePerRequest:F2}ms");
    }

    [Fact]
    public async Task SendBulkNotification_LargeUserList_ShouldCompleteWithinTimeout()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateAdminClient();

        // Create a list of user IDs (simulating a large user base)
        var userIds = Enumerable.Range(0, 1000)
            .Select(_ => Guid.NewGuid().ToString())
            .ToArray();

        var request = new
        {
            UserIds = userIds,
            Content = "Bulk performance test notification",
            MessageType = "SystemNotification",
            Priority = "Normal",
            PreferredChannel = "Push"
        };

        var stopwatch = Stopwatch.StartNew();

        // Act
        var response = await client.PostAsJsonAsync("/api/notifications/send-bulk", request);

        // Assert
        stopwatch.Stop();
        response.IsSuccessStatusCode.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds

        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<JsonElement>(content);
        result.GetProperty("data").GetProperty("totalNotifications").GetInt32().Should().Be(1000);

        WriteOutput($"Bulk notification to {userIds.Length} users completed in {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task GetUserMessageHistory_LargeDataset_ShouldCompleteWithinTimeout()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var stopwatch = Stopwatch.StartNew();

        // Act
        var response = await client.GetAsync($"/api/messages/user/{TestConstants.TestCustomerId}/history?pageSize=100");

        // Assert
        stopwatch.Stop();
        response.IsSuccessStatusCode.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000); // Should complete within 3 seconds

        WriteOutput($"Message history retrieval completed in {stopwatch.ElapsedMilliseconds}ms");
    }

    [Fact]
    public async Task DatabaseOperations_HighThroughput_ShouldMaintainPerformance()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var scope = CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<Infrastructure.Persistence.CommunicationDbContext>();

        const int operationCount = 1000;
        var stopwatch = Stopwatch.StartNew();

        // Act - Simulate high-throughput database operations
        var notifications = new List<Domain.Entities.Notification>();
        for (int i = 0; i < operationCount; i++)
        {
            notifications.Add(new Domain.Entities.Notification
            {
                Id = Guid.NewGuid(),
                UserId = Guid.Parse(TestConstants.TestCustomerId),
                MessageType = Domain.Enums.MessageType.SystemNotification,
                Priority = Domain.Enums.NotificationPriority.Normal,
                Channel = Domain.Enums.NotificationChannel.Push,
                Status = Domain.Enums.NotificationStatus.Pending,
                ScheduledAt = DateTime.UtcNow,
                Content = new Domain.ValueObjects.MessageContent
                {
                    Subject = $"Performance Test {i}",
                    Body = $"Performance test notification {i}",
                    Language = Domain.Enums.Language.English
                },
                Metadata = new Dictionary<string, string>
                {
                    ["test"] = "performance",
                    ["batch"] = i.ToString()
                },
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await dbContext.Notifications.AddRangeAsync(notifications);
        await dbContext.SaveChangesAsync();

        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(10000); // Should complete within 10 seconds
        
        var insertedCount = await dbContext.Notifications.CountAsync();
        insertedCount.Should().BeGreaterOrEqualTo(operationCount);

        WriteOutput($"Inserted {operationCount} notifications in {stopwatch.ElapsedMilliseconds}ms");
        WriteOutput($"Average time per insert: {stopwatch.ElapsedMilliseconds / (double)operationCount:F2}ms");
    }

    [Fact]
    public async Task MemoryUsage_UnderLoad_ShouldRemainStable()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var initialMemory = GC.GetTotalMemory(true);
        WriteOutput($"Initial memory usage: {initialMemory / 1024 / 1024:F2} MB");

        // Act - Generate load
        const int requestCount = 100;
        for (int i = 0; i < requestCount; i++)
        {
            var request = new
            {
                UserId = TestConstants.TestCustomerId,
                Content = $"Memory test notification {i}",
                MessageType = "SystemNotification",
                Priority = "Normal"
            };

            var response = await client.PostAsJsonAsync("/api/notifications/send", request);
            response.IsSuccessStatusCode.Should().BeTrue();

            // Force garbage collection every 10 requests
            if (i % 10 == 0)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }

        // Assert
        var finalMemory = GC.GetTotalMemory(true);
        var memoryIncrease = finalMemory - initialMemory;
        var memoryIncreasePerRequest = memoryIncrease / (double)requestCount;

        WriteOutput($"Final memory usage: {finalMemory / 1024 / 1024:F2} MB");
        WriteOutput($"Memory increase: {memoryIncrease / 1024 / 1024:F2} MB");
        WriteOutput($"Memory increase per request: {memoryIncreasePerRequest / 1024:F2} KB");

        // Memory increase should be reasonable (less than 10MB for 100 requests)
        memoryIncrease.Should().BeLessThan(10 * 1024 * 1024);
    }

    [Fact]
    public async Task ApiResponseTime_UnderSustainedLoad_ShouldMeetSLA()
    {
        // Arrange
        await WebApplicationFactory.SeedTestDataAsync();
        using var client = WebApplicationFactory.CreateCustomerClient();

        var responseTimes = new List<long>();
        const int testDurationSeconds = 30;
        const int requestsPerSecond = 10;
        
        var endTime = DateTime.UtcNow.AddSeconds(testDurationSeconds);
        var requestCount = 0;

        WriteOutput($"Starting sustained load test for {testDurationSeconds} seconds at {requestsPerSecond} RPS");

        // Act
        while (DateTime.UtcNow < endTime)
        {
            var batchTasks = new List<Task>();
            
            for (int i = 0; i < requestsPerSecond; i++)
            {
                batchTasks.Add(Task.Run(async () =>
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    var request = new
                    {
                        UserId = TestConstants.TestCustomerId,
                        Content = $"Sustained load test notification {requestCount}",
                        MessageType = "SystemNotification",
                        Priority = "Normal"
                    };

                    var response = await client.PostAsJsonAsync("/api/notifications/send", request);
                    stopwatch.Stop();

                    if (response.IsSuccessStatusCode)
                    {
                        lock (responseTimes)
                        {
                            responseTimes.Add(stopwatch.ElapsedMilliseconds);
                        }
                    }

                    Interlocked.Increment(ref requestCount);
                }));
            }

            await Task.WhenAll(batchTasks);
            await Task.Delay(1000); // Wait 1 second before next batch
        }

        // Assert
        responseTimes.Should().NotBeEmpty();
        
        var avgResponseTime = responseTimes.Average();
        var p95ResponseTime = responseTimes.OrderBy(x => x).Skip((int)(responseTimes.Count * 0.95)).First();
        var maxResponseTime = responseTimes.Max();

        WriteOutput($"Total requests: {requestCount}");
        WriteOutput($"Successful responses: {responseTimes.Count}");
        WriteOutput($"Average response time: {avgResponseTime:F2}ms");
        WriteOutput($"95th percentile response time: {p95ResponseTime}ms");
        WriteOutput($"Max response time: {maxResponseTime}ms");

        // SLA requirements
        avgResponseTime.Should().BeLessThan(1000); // Average < 1 second
        p95ResponseTime.Should().BeLessThan(2000);  // 95th percentile < 2 seconds
        maxResponseTime.Should().BeLessThan(5000);  // Max < 5 seconds
    }

    [Fact]
    public void NBomberLoadTest_NotificationEndpoint_ShouldMeetPerformanceTargets()
    {
        // Skip if performance testing is disabled
        if (!Configuration.GetValue<bool>("Testing:EnablePerformanceTesting"))
        {
            WriteOutput("Performance testing is disabled, skipping NBomber test");
            return;
        }

        // Arrange
        var httpClient = WebApplicationFactory.CreateCustomerClient();

        var scenario = Scenario.Create("notification_load_test", async context =>
        {
            var request = new
            {
                UserId = TestConstants.TestCustomerId,
                Content = $"NBomber test notification {context.InvocationNumber}",
                MessageType = "SystemNotification",
                Priority = "Normal",
                PreferredChannel = "Push"
            };

            var response = await httpClient.PostAsJsonAsync("/api/notifications/send", request);

            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var scnStats = stats.AllScenarios.First();
        
        WriteOutput($"NBomber Results:");
        WriteOutput($"Total requests: {scnStats.Ok.Request.Count}");
        WriteOutput($"Failed requests: {scnStats.Fail.Request.Count}");
        WriteOutput($"Average response time: {scnStats.Ok.Response.Mean}ms");
        WriteOutput($"95th percentile: {scnStats.Ok.Response.Percentile95}ms");

        scnStats.Ok.Request.Count.Should().BeGreaterThan(250); // At least 250 successful requests
        scnStats.Fail.Request.Count.Should().BeLessThan(10);   // Less than 10 failed requests
        scnStats.Ok.Response.Mean.Should().BeLessThan(1000);   // Average response time < 1 second
    }
}
