using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;
using System.Text.Json;

namespace FinancialPayment.Infrastructure.Services;

public class SquareGateway : IPaymentGateway
{
    private readonly SquareSettings _settings;
    private readonly ILogger<SquareGateway> _logger;
    private readonly HttpClient _httpClient;

    public string GatewayName => "Square";

    public SquareGateway(IOptions<SquareSettings> settings, ILogger<SquareGateway> logger, HttpClient httpClient)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClient = httpClient;
        
        // Configure HTTP client for Square API
        var baseUrl = _settings.IsSandbox ? "https://connect.squareupsandbox.com/" : "https://connect.squareup.com/";
        _httpClient.BaseAddress = new Uri(baseUrl);
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _settings.AccessToken);
        _httpClient.DefaultRequestHeaders.Add("Square-Version", "2023-10-18");
    }

    public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
    {
        _logger.LogInformation("Processing payment via Square for user {UserId} with amount {Amount}",
            request.UserId, request.Amount);

        try
        {
            var paymentData = new
            {
                source_id = request.PaymentMethodId,
                idempotency_key = Guid.NewGuid().ToString(),
                amount_money = new
                {
                    amount = (long)(request.Amount.Amount * 100), // Amount in cents
                    currency = request.Amount.Currency
                },
                note = request.Description,
                reference_id = request.UserId.ToString()
            };

            var content = new StringContent(JsonSerializer.Serialize(paymentData), 
                System.Text.Encoding.UTF8, "application/json");

            // For demo purposes, simulate successful payment
            await Task.Delay(1000);
            var paymentId = $"sq_{Guid.NewGuid():N}";

            _logger.LogInformation("Payment processed successfully via Square with payment ID {PaymentId}", paymentId);

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = paymentId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment completed successfully via Square"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing payment via Square for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<RefundResult> ProcessRefundAsync(RefundRequest request)
    {
        _logger.LogInformation("Processing refund via Square for transaction {TransactionId}", request.TransactionId);

        try
        {
            var refundData = new
            {
                idempotency_key = Guid.NewGuid().ToString(),
                amount_money = new
                {
                    amount = (long)(request.Amount.Amount * 100), // Amount in cents
                    currency = request.Amount.Currency
                },
                payment_id = request.TransactionId,
                reason = request.Reason
            };

            var content = new StringContent(JsonSerializer.Serialize(refundData), 
                System.Text.Encoding.UTF8, "application/json");

            // For demo purposes, simulate successful refund
            await Task.Delay(500);
            var refundId = $"sq_refund_{Guid.NewGuid():N}";

            _logger.LogInformation("Refund processed successfully via Square with refund ID {RefundId}", refundId);

            return new RefundResult
            {
                IsSuccess = true,
                RefundId = refundId,
                RefundAmount = request.Amount,
                ProcessedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing refund via Square for transaction {TransactionId}", request.TransactionId);

            return new RefundResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<PaymentStatus> GetPaymentStatusAsync(string transactionId)
    {
        try
        {
            // For demo purposes, simulate status check
            await Task.Delay(100);
            return PaymentStatus.Completed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting payment status for transaction {TransactionId}", transactionId);
            return PaymentStatus.Failed;
        }
    }

    public async Task<bool> ValidateWebhookAsync(string payload, string signature)
    {
        try
        {
            // Square webhook validation logic would go here
            // For demo purposes, return true
            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Square webhook");
            return false;
        }
    }

    public async Task<PaymentResult> CreatePaymentIntentAsync(PaymentIntentRequest request)
    {
        _logger.LogInformation("Creating payment intent via Square for user {UserId}", request.UserId);

        try
        {
            // Square doesn't have payment intents like Stripe, but we can create a payment request
            var paymentRequestData = new
            {
                idempotency_key = Guid.NewGuid().ToString(),
                ask_for_shipping_address = false,
                merchant_support_email = "<EMAIL>",
                pre_populated_data = new
                {
                    buyer_email = $"user_{request.UserId}@example.com"
                },
                redirect_url = "https://merchant.website.com/order-confirmation",
                order = new
                {
                    location_id = _settings.LocationId,
                    order = new
                    {
                        line_items = new[]
                        {
                            new
                            {
                                quantity = "1",
                                base_price_money = new
                                {
                                    amount = (long)(request.Amount.Amount * 100), // Amount in cents
                                    currency = request.Amount.Currency
                                },
                                name = request.Description
                            }
                        }
                    }
                }
            };

            // For demo purposes, simulate successful intent creation
            await Task.Delay(200);
            var checkoutId = $"sq_checkout_{Guid.NewGuid():N}";

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = checkoutId,
                ProcessedAt = DateTime.UtcNow,
                GatewayResponse = "Payment intent created successfully via Square"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment intent via Square for user {UserId}", request.UserId);

            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }
}

public class SquareSettings
{
    public string ApplicationId { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string LocationId { get; set; } = string.Empty;
    public string WebhookSignatureKey { get; set; } = string.Empty;
    public bool IsSandbox { get; set; } = true;
}
