using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class SubscriptionConfiguration : IEntityTypeConfiguration<Subscription>
    {
        public void Configure(EntityTypeBuilder<Subscription> builder)
        {
            builder.ToTable("Subscriptions");

            builder.HasKey(s => s.Id);

            builder.Property(s => s.UserId)
                .IsRequired();

            builder.Property(s => s.PlanId)
                .IsRequired();

            builder.Property(s => s.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(s => s.StartDate)
                .IsRequired();

            builder.Property(s => s.EndDate);

            builder.Property(s => s.NextBillingDate)
                .IsRequired();

            builder.Property(s => s.TrialEndDate);

            builder.Property(s => s.CancelledAt);

            builder.Property(s => s.CancellationReason)
                .HasMaxLength(500);

            builder.Property(s => s.AutoRenew)
                .IsRequired();

            builder.Property(s => s.ProrationMode)
                .IsRequired()
                .HasConversion<string>();

            // Configure Money value object for CurrentPrice
            builder.OwnsOne(s => s.CurrentPrice, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("CurrentPrice")
                    .HasPrecision(18, 2)
                    .IsRequired();

                price.Property(m => m.Currency)
                    .HasColumnName("CurrentCurrency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Configure BillingCycle value object
            builder.OwnsOne(s => s.BillingCycle, cycle =>
            {
                cycle.Property(c => c.Interval)
                    .HasColumnName("BillingInterval")
                    .HasConversion<string>()
                    .IsRequired();

                cycle.Property(c => c.IntervalCount)
                    .HasColumnName("BillingIntervalCount")
                    .IsRequired();

                cycle.Property(c => c.CustomDays)
                    .HasColumnName("CustomBillingDays");
            });

            // Configure Grace Period Extension properties
            builder.Property(s => s.GracePeriodEndDate);

            builder.Property(s => s.LastExtendedAt);

            builder.Property(s => s.ExtensionReason)
                .HasMaxLength(500);

            builder.Property(s => s.ExtendedByUserId);

            builder.Property(s => s.CreatedAt)
                .IsRequired();

            builder.Property(s => s.UpdatedAt);

            // Configure relationships
            builder.HasOne(s => s.Plan)
                .WithMany()
                .HasForeignKey(s => s.PlanId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(s => s.Payments)
                .WithOne()
                .HasForeignKey("SubscriptionId")
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(s => s.Changes)
                .WithOne()
                .HasForeignKey("SubscriptionId")
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(s => s.UserId);
            builder.HasIndex(s => s.PlanId);
            builder.HasIndex(s => s.Status);
            builder.HasIndex(s => s.NextBillingDate);
            builder.HasIndex(s => new { s.UserId, s.Status });
            builder.HasIndex(s => s.GracePeriodEndDate);
            builder.HasIndex(s => s.LastExtendedAt);
            builder.HasIndex(s => s.ExtendedByUserId);
        }
    }
}
