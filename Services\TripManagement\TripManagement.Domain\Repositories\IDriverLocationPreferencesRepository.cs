using TripManagement.Domain.Entities;

namespace TripManagement.Domain.Repositories;

public interface IDriverLocationPreferencesRepository
{
    Task<DriverLocationPreferencesEntity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<DriverLocationPreferencesEntity?> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetByLocationSharingEnabledAsync(bool enabled, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetStaleLocationDataAsync(int staleThresholdMinutes = 5, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetByHighAccuracyModeAsync(bool enabled, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetByBackgroundLocationEnabledAsync(bool enabled, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetByGeofenceAlertsEnabledAsync(bool enabled, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetByUpdateIntervalAsync(int intervalSeconds, CancellationToken cancellationToken = default);
    Task<List<DriverLocationPreferencesEntity>> GetRecentlyUpdatedAsync(int hours = 24, CancellationToken cancellationToken = default);
    Task<int> GetLocationSharingEnabledCountAsync(CancellationToken cancellationToken = default);
    Task<Dictionary<string, int>> GetPreferencesStatisticsAsync(CancellationToken cancellationToken = default);
    Task AddAsync(DriverLocationPreferencesEntity preferences, CancellationToken cancellationToken = default);
    void Update(DriverLocationPreferencesEntity preferences);
    void Delete(DriverLocationPreferencesEntity preferences);
    Task<bool> ExistsAsync(Guid driverId, CancellationToken cancellationToken = default);
}
