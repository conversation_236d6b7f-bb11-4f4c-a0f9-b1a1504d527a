using MediatR;
using OrderManagement.Application.DTOs;

namespace OrderManagement.Application.Commands.AcceptCounterOffer;

public class AcceptCounterOfferCommand : IRequest<bool>
{
    public Guid CounterOfferId { get; set; }
    public Guid NegotiationId { get; set; }
    public MoneyDto FinalPrice { get; set; } = new();
    public string? FinalTerms { get; set; }
    public string? AcceptanceNotes { get; set; }
    public Guid RequestingUserId { get; set; }
    public bool CreateOrder { get; set; } = true;
    public bool NotifyStakeholders { get; set; } = true;
}
