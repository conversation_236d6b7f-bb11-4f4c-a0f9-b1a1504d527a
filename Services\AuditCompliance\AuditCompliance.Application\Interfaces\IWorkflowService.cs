using AuditCompliance.Application.DTOs.Workflow;

namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Service for compliance workflow automation and management
/// </summary>
public interface IWorkflowService
{
    /// <summary>
    /// Create a new workflow definition
    /// </summary>
    Task<Guid> CreateWorkflowDefinitionAsync(
        CreateWorkflowDefinitionDto definitionDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update workflow definition
    /// </summary>
    Task<bool> UpdateWorkflowDefinitionAsync(
        Guid definitionId,
        UpdateWorkflowDefinitionDto definitionDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow definition by ID
    /// </summary>
    Task<WorkflowDefinitionDto?> GetWorkflowDefinitionAsync(
        Guid definitionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all workflow definitions
    /// </summary>
    Task<List<WorkflowDefinitionDto>> GetWorkflowDefinitionsAsync(
        string? category = null,
        bool activeOnly = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Start a new workflow instance
    /// </summary>
    Task<Guid> StartWorkflowAsync(
        StartWorkflowDto startDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow instance by ID
    /// </summary>
    Task<WorkflowInstanceDto?> GetWorkflowInstanceAsync(
        Guid instanceId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow instances with filtering
    /// </summary>
    Task<List<WorkflowInstanceDto>> GetWorkflowInstancesAsync(
        WorkflowInstanceFilterDto filter,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Complete a workflow task
    /// </summary>
    Task<bool> CompleteTaskAsync(
        Guid taskId,
        CompleteTaskDto completeDto,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Assign task to user
    /// </summary>
    Task<bool> AssignTaskAsync(
        Guid taskId,
        string assigneeId,
        string? assignedBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get tasks assigned to user
    /// </summary>
    Task<List<WorkflowTaskDto>> GetUserTasksAsync(
        string userId,
        TaskFilterDto? filter = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get task by ID
    /// </summary>
    Task<WorkflowTaskDto?> GetTaskAsync(
        Guid taskId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel workflow instance
    /// </summary>
    Task<bool> CancelWorkflowAsync(
        Guid instanceId,
        string reason,
        string cancelledBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Suspend workflow instance
    /// </summary>
    Task<bool> SuspendWorkflowAsync(
        Guid instanceId,
        string reason,
        string suspendedBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Resume workflow instance
    /// </summary>
    Task<bool> ResumeWorkflowAsync(
        Guid instanceId,
        string resumedBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow history
    /// </summary>
    Task<List<WorkflowHistoryDto>> GetWorkflowHistoryAsync(
        Guid instanceId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow analytics
    /// </summary>
    Task<WorkflowAnalyticsDto> GetWorkflowAnalyticsAsync(
        WorkflowAnalyticsRequestDto request,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate workflow definition
    /// </summary>
    Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(
        WorkflowDefinitionDto definition,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process workflow timers and escalations
    /// </summary>
    Task ProcessWorkflowTimersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get workflow templates
    /// </summary>
    Task<List<WorkflowTemplateDto>> GetWorkflowTemplatesAsync(
        string? category = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create workflow from template
    /// </summary>
    Task<Guid> CreateWorkflowFromTemplateAsync(
        Guid templateId,
        CreateFromTemplateDto createDto,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for workflow engine
/// </summary>
public interface IWorkflowEngine
{
    /// <summary>
    /// Execute workflow step
    /// </summary>
    Task<WorkflowExecutionResult> ExecuteStepAsync(
        WorkflowInstanceDto instance,
        WorkflowStepDto step,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Evaluate workflow condition
    /// </summary>
    Task<bool> EvaluateConditionAsync(
        string condition,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get next steps in workflow
    /// </summary>
    Task<List<WorkflowStepDto>> GetNextStepsAsync(
        WorkflowInstanceDto instance,
        WorkflowStepDto currentStep,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute workflow action
    /// </summary>
    Task<ActionExecutionResult> ExecuteActionAsync(
        WorkflowActionDto action,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for workflow notification service
/// </summary>
public interface IWorkflowNotificationService
{
    /// <summary>
    /// Send task assignment notification
    /// </summary>
    Task SendTaskAssignmentNotificationAsync(
        WorkflowTaskDto task,
        string assigneeId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send task due notification
    /// </summary>
    Task SendTaskDueNotificationAsync(
        WorkflowTaskDto task,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send workflow completion notification
    /// </summary>
    Task SendWorkflowCompletionNotificationAsync(
        WorkflowInstanceDto instance,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Send escalation notification
    /// </summary>
    Task SendEscalationNotificationAsync(
        WorkflowTaskDto task,
        string escalationLevel,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Workflow execution result
/// </summary>
public class WorkflowExecutionResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> OutputData { get; set; } = new();
    public List<WorkflowStepDto> NextSteps { get; set; } = new();
    public bool IsCompleted { get; set; }
    public string? CompletionReason { get; set; }
}

/// <summary>
/// Action execution result
/// </summary>
public class ActionExecutionResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> OutputData { get; set; } = new();
    public bool RequiresUserInput { get; set; }
    public string? UserInputPrompt { get; set; }
}

/// <summary>
/// Workflow validation result
/// </summary>
public class WorkflowValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
}
