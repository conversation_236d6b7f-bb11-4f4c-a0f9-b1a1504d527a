using Microsoft.EntityFrameworkCore;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Entities;
using OrderManagement.Domain.Enums;
using OrderManagement.Infrastructure.Persistence;

namespace OrderManagement.Infrastructure.Repositories;

public class OrderRepository : IOrderRepository
{
    private readonly OrderManagementDbContext _context;

    public OrderRepository(OrderManagementDbContext context)
    {
        _context = context;
    }

    public async Task<Order?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .Include(o => o.Documents)
            .Include(o => o.StatusHistory)
            .Include(o => o.Invoices)
                .ThenInclude(i => i.LineItems)
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);
    }

    public async Task<Order?> GetByIdWithTimelineAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .Include(o => o.Documents)
            .Include(o => o.StatusHistory)
            .Include(o => o.Timeline)
            .Include(o => o.Invoices)
                .ThenInclude(i => i.LineItems)
            .FirstOrDefaultAsync(o => o.Id == id, cancellationToken);
    }

    public async Task<Order?> GetByOrderNumberAsync(string orderNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .Include(o => o.Documents)
            .Include(o => o.StatusHistory)
            .Include(o => o.Invoices)
                .ThenInclude(i => i.LineItems)
            .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber, cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetByTransportCompanyIdAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.StatusHistory)
            .Where(o => o.TransportCompanyId == transportCompanyId)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetByBrokerIdAsync(Guid brokerId, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.StatusHistory)
            .Where(o => o.BrokerId == brokerId)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetByCarrierIdAsync(Guid carrierId, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.StatusHistory)
            .Where(o => o.CarrierId == carrierId)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.StatusHistory)
            .Where(o => o.Status == status)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Order> Orders, int TotalCount)> GetPagedAsync(
        int page,
        int pageSize,
        Guid? transportCompanyId = null,
        Guid? brokerId = null,
        Guid? carrierId = null,
        OrderStatus? status = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Orders.AsQueryable();

        if (transportCompanyId.HasValue)
        {
            query = query.Where(o => o.TransportCompanyId == transportCompanyId.Value);
        }

        if (brokerId.HasValue)
        {
            query = query.Where(o => o.BrokerId == brokerId.Value);
        }

        if (carrierId.HasValue)
        {
            query = query.Where(o => o.CarrierId == carrierId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(o => o.Status == status.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var orders = await query
            .Include(o => o.StatusHistory)
            .OrderByDescending(o => o.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (orders, totalCount);
    }

    public async Task AddAsync(Order order, CancellationToken cancellationToken = default)
    {
        await _context.Orders.AddAsync(order, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Order order, CancellationToken cancellationToken = default)
    {
        _context.Orders.Update(order);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var order = await _context.Orders.FindAsync(new object[] { id }, cancellationToken);
        if (order != null)
        {
            _context.Orders.Remove(order);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    public async Task<List<Order>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Order>> GetOrdersByTransportCompanyAsync(Guid transportCompanyId, CancellationToken cancellationToken = default)
    {
        return await _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .Where(o => o.TransportCompanyId == transportCompanyId)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Order>> GetOrdersByTransportCompanyAsync(Guid transportCompanyId, DateTime? fromDate, DateTime? toDate, CancellationToken cancellationToken = default)
    {
        var query = _context.Orders
            .Include(o => o.Rfq)
            .Include(o => o.AcceptedBid)
            .Where(o => o.TransportCompanyId == transportCompanyId);

        if (fromDate.HasValue)
            query = query.Where(o => o.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(o => o.CreatedAt <= toDate.Value);

        return await query.ToListAsync(cancellationToken);
    }
}
