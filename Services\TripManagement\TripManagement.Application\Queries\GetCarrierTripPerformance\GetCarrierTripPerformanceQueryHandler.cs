using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using TripManagement.Application.DTOs;
using TripManagement.Infrastructure.Persistence;

namespace TripManagement.Application.Queries.GetCarrierTripPerformance;

public class GetCarrierTripPerformanceQueryHandler : IRequestHandler<GetCarrierTripPerformanceQuery, CarrierTripPerformanceDto>
{
    private readonly TripManagementDbContext _context;

    public GetCarrierTripPerformanceQueryHandler(TripManagementDbContext context)
    {
        _context = context;
    }

    public async Task<CarrierTripPerformanceDto> Handle(GetCarrierTripPerformanceQuery request, CancellationToken cancellationToken)
    {
        // Get all trips for the carrier in the specified date range
        var trips = await _context.Trips
            .Include(t => t.Driver)
            .Include(t => t.Vehicle)
            .Include(t => t.Stops)
            .Include(t => t.Exceptions)
            .Where(t => t.CarrierId == request.CarrierId && 
                       t.CreatedAt >= request.FromDate && 
                       t.CreatedAt <= request.ToDate)
            .ToListAsync(cancellationToken);

        // Get carrier name (assuming we have access to carrier information)
        var carrierName = "Carrier"; // This would be fetched from carrier service or database

        // Calculate basic trip statistics
        var totalTrips = trips.Count;
        var completedTrips = trips.Count(t => t.Status == Domain.Enums.TripStatus.Completed);
        var cancelledTrips = trips.Count(t => t.Status == Domain.Enums.TripStatus.Cancelled);
        var inProgressTrips = trips.Count(t => t.Status == Domain.Enums.TripStatus.InProgress);
        
        var tripCompletionRate = totalTrips > 0 ? (decimal)completedTrips / totalTrips * 100 : 0;
        var tripCancellationRate = totalTrips > 0 ? (decimal)cancelledTrips / totalTrips * 100 : 0;

        // Calculate on-time performance
        var completedTripsWithTimes = trips.Where(t => t.Status == Domain.Enums.TripStatus.Completed && 
                                                      t.CompletedAt.HasValue).ToList();
        var onTimeTrips = completedTripsWithTimes.Count(t => t.CompletedAt <= t.EstimatedEndTime);
        var delayedTrips = completedTripsWithTimes.Count(t => t.CompletedAt > t.EstimatedEndTime);
        var onTimeDeliveryRate = completedTripsWithTimes.Count > 0 ? 
            (decimal)onTimeTrips / completedTripsWithTimes.Count * 100 : 0;

        // Calculate delay statistics
        var delays = completedTripsWithTimes
            .Where(t => t.CompletedAt > t.EstimatedEndTime)
            .Select(t => t.CompletedAt!.Value - t.EstimatedEndTime)
            .ToList();

        var averageDelay = delays.Any() ? 
            TimeSpan.FromTicks((long)delays.Average(d => d.Ticks)) : TimeSpan.Zero;
        var maxDelay = delays.Any() ? delays.Max() : TimeSpan.Zero;
        var minDelay = delays.Any() ? delays.Min() : TimeSpan.Zero;

        // Calculate distance and duration metrics
        var tripsWithDistance = trips.Where(t => t.ActualDistanceKm.HasValue).ToList();
        var totalDistance = tripsWithDistance.Sum(t => t.ActualDistanceKm!.Value);
        var averageDistancePerTrip = tripsWithDistance.Count > 0 ? 
            tripsWithDistance.Average(t => t.ActualDistanceKm!.Value) : 0;

        var tripsWithDuration = trips.Where(t => t.StartedAt.HasValue && t.CompletedAt.HasValue).ToList();
        var totalDuration = tripsWithDuration.Aggregate(TimeSpan.Zero, 
            (sum, t) => sum + (t.CompletedAt!.Value - t.StartedAt!.Value));
        var averageDuration = tripsWithDuration.Count > 0 ? 
            TimeSpan.FromTicks(totalDuration.Ticks / tripsWithDuration.Count) : TimeSpan.Zero;

        var averageSpeed = totalDuration.TotalHours > 0 ? (decimal)(totalDistance / totalDuration.TotalHours) : 0;

        // Calculate exception metrics
        var allExceptions = trips.SelectMany(t => t.Exceptions).ToList();
        var totalExceptions = allExceptions.Count;
        var resolvedExceptions = allExceptions.Count(e => e.IsResolved);
        var exceptionResolutionRate = totalExceptions > 0 ? 
            (decimal)resolvedExceptions / totalExceptions * 100 : 0;

        var resolvedExceptionsWithTimes = allExceptions
            .Where(e => e.IsResolved && e.ResolvedAt.HasValue)
            .ToList();
        var averageExceptionResolutionTime = resolvedExceptionsWithTimes.Count > 0 ?
            TimeSpan.FromTicks((long)resolvedExceptionsWithTimes
                .Average(e => (e.ResolvedAt!.Value - e.ReportedAt).Ticks)) : TimeSpan.Zero;

        // Exception breakdown
        var exceptionBreakdown = allExceptions
            .GroupBy(e => e.ExceptionType)
            .Select(g => new ExceptionTypeStatsDto
            {
                ExceptionType = g.Key,
                ExceptionTypeName = g.Key.ToString(),
                Count = g.Count(),
                ResolvedCount = g.Count(e => e.IsResolved),
                ResolutionRate = g.Count() > 0 ? (decimal)g.Count(e => e.IsResolved) / g.Count() * 100 : 0,
                AverageResolutionTime = g.Where(e => e.IsResolved && e.ResolvedAt.HasValue).Any() ?
                    TimeSpan.FromTicks((long)g.Where(e => e.IsResolved && e.ResolvedAt.HasValue)
                        .Average(e => (e.ResolvedAt!.Value - e.ReportedAt).Ticks)) : TimeSpan.Zero,
                PercentageOfTotal = totalExceptions > 0 ? (decimal)g.Count() / totalExceptions * 100 : 0
            })
            .OrderByDescending(e => e.Count)
            .ToList();

        // Driver performance (if requested)
        var topDrivers = new List<DriverPerformanceSummaryDto>();
        if (request.IncludeDriverPerformance)
        {
            topDrivers = trips
                .Where(t => t.DriverId.HasValue && t.Driver != null)
                .GroupBy(t => new { t.DriverId, t.Driver!.FirstName, t.Driver.LastName })
                .Select(g => new DriverPerformanceSummaryDto
                {
                    DriverId = g.Key.DriverId!.Value,
                    DriverName = $"{g.Key.FirstName} {g.Key.LastName}",
                    TripsCompleted = g.Count(t => t.Status == Domain.Enums.TripStatus.Completed),
                    OnTimeRate = g.Where(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue).Any() ?
                        (decimal)g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && 
                                            t.CompletedAt.HasValue && t.CompletedAt <= t.EstimatedEndTime) /
                        g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue) * 100 : 0,
                    Rating = g.First().Driver?.Rating ?? 0,
                    TotalDistanceKm = g.Where(t => t.ActualDistanceKm.HasValue).Sum(t => t.ActualDistanceKm!.Value),
                    ExceptionsCount = g.SelectMany(t => t.Exceptions).Count()
                })
                .OrderByDescending(d => d.OnTimeRate)
                .ThenByDescending(d => d.TripsCompleted)
                .Take(request.TopPerformersCount)
                .ToList();
        }

        // Vehicle performance (if requested)
        var topVehicles = new List<VehiclePerformanceSummaryDto>();
        if (request.IncludeVehiclePerformance)
        {
            topVehicles = trips
                .Where(t => t.VehicleId.HasValue && t.Vehicle != null)
                .GroupBy(t => new { t.VehicleId, t.Vehicle!.RegistrationNumber, t.Vehicle.DisplayName })
                .Select(g => new VehiclePerformanceSummaryDto
                {
                    VehicleId = g.Key.VehicleId!.Value,
                    VehicleRegistration = g.Key.RegistrationNumber,
                    VehicleDisplayName = g.Key.DisplayName,
                    TripsCompleted = g.Count(t => t.Status == Domain.Enums.TripStatus.Completed),
                    UtilizationRate = 0, // Would need additional data to calculate
                    TotalDistanceKm = g.Where(t => t.ActualDistanceKm.HasValue).Sum(t => t.ActualDistanceKm!.Value),
                    AverageSpeedKmh = 0, // Would need to calculate from trip data
                    MaintenanceCount = 0 // Would need maintenance data
                })
                .OrderByDescending(v => v.TripsCompleted)
                .ThenByDescending(v => v.TotalDistanceKm)
                .Take(request.TopPerformersCount)
                .ToList();
        }

        // Trend data (if requested)
        var dailyTrends = new List<DailyPerformanceDto>();
        var weeklyTrends = new List<WeeklyPerformanceDto>();
        var monthlyTrends = new List<MonthlyPerformanceDto>();

        if (request.IncludeTrends)
        {
            // Daily trends
            dailyTrends = trips
                .GroupBy(t => t.CreatedAt.Date)
                .Select(g => new DailyPerformanceDto
                {
                    Date = g.Key,
                    TripsCompleted = g.Count(t => t.Status == Domain.Enums.TripStatus.Completed),
                    OnTimeRate = g.Where(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue).Any() ?
                        (decimal)g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && 
                                            t.CompletedAt.HasValue && t.CompletedAt <= t.EstimatedEndTime) /
                        g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue) * 100 : 0,
                    TotalDistanceKm = g.Where(t => t.ActualDistanceKm.HasValue).Sum(t => t.ActualDistanceKm!.Value),
                    ExceptionsCount = g.SelectMany(t => t.Exceptions).Count(),
                    Revenue = null // Would be calculated from order/billing data
                })
                .OrderBy(d => d.Date)
                .ToList();

            // Monthly trends
            monthlyTrends = trips
                .GroupBy(t => new { t.CreatedAt.Year, t.CreatedAt.Month })
                .Select(g => new MonthlyPerformanceDto
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                    TripsCompleted = g.Count(t => t.Status == Domain.Enums.TripStatus.Completed),
                    OnTimeRate = g.Where(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue).Any() ?
                        (decimal)g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && 
                                            t.CompletedAt.HasValue && t.CompletedAt <= t.EstimatedEndTime) /
                        g.Count(t => t.Status == Domain.Enums.TripStatus.Completed && t.CompletedAt.HasValue) * 100 : 0,
                    TotalDistanceKm = g.Where(t => t.ActualDistanceKm.HasValue).Sum(t => t.ActualDistanceKm!.Value),
                    ExceptionsCount = g.SelectMany(t => t.Exceptions).Count(),
                    Revenue = null
                })
                .OrderBy(m => m.Year)
                .ThenBy(m => m.Month)
                .ToList();
        }

        return new CarrierTripPerformanceDto
        {
            CarrierId = request.CarrierId,
            CarrierName = carrierName,
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            
            // Trip Statistics
            TotalTrips = totalTrips,
            CompletedTrips = completedTrips,
            CancelledTrips = cancelledTrips,
            InProgressTrips = inProgressTrips,
            DelayedTrips = delayedTrips,
            TripCompletionRate = tripCompletionRate,
            TripCancellationRate = tripCancellationRate,
            
            // On-Time Performance
            OnTimeTrips = onTimeTrips,
            OnTimeDeliveryRate = onTimeDeliveryRate,
            AverageDelay = averageDelay,
            MaxDelay = maxDelay,
            MinDelay = minDelay,
            
            // Distance and Duration
            TotalDistanceKm = totalDistance,
            AverageDistancePerTrip = averageDistancePerTrip,
            TotalDuration = totalDuration,
            AverageDuration = averageDuration,
            AverageSpeedKmh = averageSpeed,
            
            // Exceptions
            TotalExceptions = totalExceptions,
            ResolvedExceptions = resolvedExceptions,
            ExceptionResolutionRate = exceptionResolutionRate,
            AverageExceptionResolutionTime = averageExceptionResolutionTime,
            ExceptionBreakdown = exceptionBreakdown,
            
            // Revenue (placeholder)
            TotalRevenue = null,
            AverageRevenuePerTrip = null,
            RevenuePerKm = null,
            
            // Performance summaries
            ActiveDrivers = topDrivers.Count,
            AverageDriverRating = topDrivers.Any() ? topDrivers.Average(d => d.Rating) : 0,
            TopDrivers = topDrivers,
            
            ActiveVehicles = topVehicles.Count,
            AverageVehicleUtilization = 0, // Would need additional calculation
            TopVehicles = topVehicles,
            
            // Trends
            DailyTrends = dailyTrends,
            WeeklyTrends = weeklyTrends,
            MonthlyTrends = monthlyTrends,
            
            GeneratedAt = DateTime.UtcNow
        };
    }
}
