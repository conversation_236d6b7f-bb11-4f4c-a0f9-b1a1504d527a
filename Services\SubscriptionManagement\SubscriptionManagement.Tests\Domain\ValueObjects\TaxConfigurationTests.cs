using FluentAssertions;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Domain.ValueObjects
{
    public class TaxConfigurationTests
    {
        [Fact]
        public void Create_WithValidParameters_ShouldCreateTaxConfiguration()
        {
            // Arrange
            var taxType = TaxType.GST;
            var rate = 18.0m;
            var isIncluded = false;
            var regions = new List<string> { "IN" };
            var effectiveDate = DateTime.UtcNow;

            // Act
            var taxConfig = TaxConfiguration.Create(taxType, rate, isIncluded, regions, effectiveDate);

            // Assert
            taxConfig.Should().NotBeNull();
            taxConfig.TaxType.Should().Be(taxType);
            taxConfig.Rate.Should().Be(rate);
            taxConfig.IsIncluded.Should().Be(isIncluded);
            taxConfig.ApplicableRegions.Should().BeEquivalentTo(new[] { "IN" });
            taxConfig.EffectiveDate.Should().Be(effectiveDate);
        }

        [Theory]
        [InlineData(-1)]
        [InlineData(101)]
        public void Create_WithInvalidRate_ShouldThrowException(decimal rate)
        {
            // Arrange
            var taxType = TaxType.GST;
            var isIncluded = false;
            var regions = new List<string> { "IN" };
            var effectiveDate = DateTime.UtcNow;

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxConfiguration.Create(taxType, rate, isIncluded, regions, effectiveDate));
        }

        [Fact]
        public void Create_WithEmptyRegions_ShouldThrowException()
        {
            // Arrange
            var taxType = TaxType.GST;
            var rate = 18.0m;
            var isIncluded = false;
            var regions = new List<string>();
            var effectiveDate = DateTime.UtcNow;

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxConfiguration.Create(taxType, rate, isIncluded, regions, effectiveDate));
        }

        [Fact]
        public void Create_WithExpirationDateBeforeEffectiveDate_ShouldThrowException()
        {
            // Arrange
            var taxType = TaxType.GST;
            var rate = 18.0m;
            var isIncluded = false;
            var regions = new List<string> { "IN" };
            var effectiveDate = DateTime.UtcNow;
            var expirationDate = effectiveDate.AddDays(-1);

            // Act & Assert
            Assert.Throws<SubscriptionDomainException>(() =>
                TaxConfiguration.Create(taxType, rate, isIncluded, regions, effectiveDate, expirationDate));
        }

        [Theory]
        [InlineData("IN", true)]
        [InlineData("in", true)]
        [InlineData("US", false)]
        [InlineData("", false)]
        [InlineData(null, false)]
        public void IsApplicableToRegion_ShouldReturnCorrectResult(string region, bool expected)
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);

            // Act
            var result = taxConfig.IsApplicableToRegion(region);

            // Assert
            result.Should().Be(expected);
        }

        [Fact]
        public void IsActiveOn_WithCurrentDate_ShouldReturnTrue()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow.AddDays(-1);
            var expirationDate = DateTime.UtcNow.AddDays(1);
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate, expirationDate);

            // Act
            var result = taxConfig.IsActiveOn(DateTime.UtcNow);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void IsActiveOn_WithDateBeforeEffective_ShouldReturnFalse()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow.AddDays(1);
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);

            // Act
            var result = taxConfig.IsActiveOn(DateTime.UtcNow);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void IsActiveOn_WithDateAfterExpiration_ShouldReturnFalse()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow.AddDays(-2);
            var expirationDate = DateTime.UtcNow.AddDays(-1);
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate, expirationDate);

            // Act
            var result = taxConfig.IsActiveOn(DateTime.UtcNow);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void CalculateTax_WithValidAmount_ShouldReturnCorrectTax()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);
            var baseAmount = Money.Create(100m, "INR");

            // Act
            var taxAmount = taxConfig.CalculateTax(baseAmount, "IN");

            // Assert
            taxAmount.Amount.Should().Be(18m);
            taxAmount.Currency.Should().Be("INR");
        }

        [Fact]
        public void CalculateTax_WithNonApplicableRegion_ShouldReturnZero()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);
            var baseAmount = Money.Create(100m, "USD");

            // Act
            var taxAmount = taxConfig.CalculateTax(baseAmount, "US");

            // Assert
            taxAmount.Amount.Should().Be(0m);
            taxAmount.Currency.Should().Be("USD");
        }

        [Fact]
        public void CalculateBaseAmountFromTaxInclusivePrice_WithTaxIncluded_ShouldReturnCorrectBase()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, true, new List<string> { "IN" }, DateTime.UtcNow);
            var taxInclusiveAmount = Money.Create(118m, "INR");

            // Act
            var baseAmount = taxConfig.CalculateBaseAmountFromTaxInclusivePrice(taxInclusiveAmount, "IN");

            // Assert
            baseAmount.Amount.Should().BeApproximately(100m, 0.01m);
            baseAmount.Currency.Should().Be("INR");
        }

        [Fact]
        public void CalculateBaseAmountFromTaxInclusivePrice_WithTaxNotIncluded_ShouldReturnSameAmount()
        {
            // Arrange
            var taxConfig = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, DateTime.UtcNow);
            var amount = Money.Create(100m, "INR");

            // Act
            var baseAmount = taxConfig.CalculateBaseAmountFromTaxInclusivePrice(amount, "IN");

            // Assert
            baseAmount.Amount.Should().Be(100m);
            baseAmount.Currency.Should().Be("INR");
        }

        [Fact]
        public void Equals_WithSameTaxConfigurations_ShouldReturnTrue()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow;
            var taxConfig1 = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);
            var taxConfig2 = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);

            // Act & Assert
            taxConfig1.Should().Be(taxConfig2);
            (taxConfig1 == taxConfig2).Should().BeTrue();
        }

        [Fact]
        public void Equals_WithDifferentTaxConfigurations_ShouldReturnFalse()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow;
            var taxConfig1 = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);
            var taxConfig2 = TaxConfiguration.Create(
                TaxType.VAT, 18.0m, false, new List<string> { "IN" }, effectiveDate);

            // Act & Assert
            taxConfig1.Should().NotBe(taxConfig2);
            (taxConfig1 != taxConfig2).Should().BeTrue();
        }

        [Fact]
        public void GetHashCode_WithSameTaxConfigurations_ShouldReturnSameHashCode()
        {
            // Arrange
            var effectiveDate = DateTime.UtcNow;
            var taxConfig1 = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);
            var taxConfig2 = TaxConfiguration.Create(
                TaxType.GST, 18.0m, false, new List<string> { "IN" }, effectiveDate);

            // Act & Assert
            taxConfig1.GetHashCode().Should().Be(taxConfig2.GetHashCode());
        }
    }
}
