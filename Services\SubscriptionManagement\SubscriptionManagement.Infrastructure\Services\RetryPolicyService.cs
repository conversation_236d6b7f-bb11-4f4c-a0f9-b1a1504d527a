using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Infrastructure.Services
{
    public class RetryPolicyService : IRetryPolicyService
    {
        private readonly ILogger<RetryPolicyService> _logger;

        public RetryPolicyService(ILogger<RetryPolicyService> logger)
        {
            _logger = logger;
        }

        public async Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan? baseDelay = null,
            CancellationToken cancellationToken = default)
        {
            var delay = baseDelay ?? TimeSpan.FromSeconds(1);
            var attempt = 0;
            Exception? lastException = null;

            while (attempt <= maxRetries)
            {
                try
                {
                    if (attempt > 0)
                    {
                        _logger.LogInformation("Retry attempt {Attempt} of {MaxRetries}", attempt, maxRetries);
                    }

                    var result = await operation();
                    
                    if (attempt > 0)
                    {
                        _logger.LogInformation("Operation succeeded on retry attempt {Attempt}", attempt);
                    }
                    
                    return result;
                }
                catch (Exception ex) when (ShouldRetry(ex, attempt, maxRetries))
                {
                    lastException = ex;
                    attempt++;

                    if (attempt <= maxRetries)
                    {
                        var currentDelay = CalculateDelay(delay, attempt);
                        _logger.LogWarning(ex, "Operation failed on attempt {Attempt}. Retrying in {Delay}ms", 
                            attempt, currentDelay.TotalMilliseconds);

                        await Task.Delay(currentDelay, cancellationToken);
                    }
                }
            }

            _logger.LogError(lastException, "Operation failed after {MaxRetries} retry attempts", maxRetries);
            throw lastException ?? new InvalidOperationException("Operation failed after retries");
        }

        public async Task ExecuteWithRetryAsync(
            Func<Task> operation,
            int maxRetries = 3,
            TimeSpan? baseDelay = null,
            CancellationToken cancellationToken = default)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true;
            }, maxRetries, baseDelay, cancellationToken);
        }

        public async Task<T> ExecuteWithCircuitBreakerAsync<T>(
            Func<Task<T>> operation,
            string circuitName,
            int failureThreshold = 5,
            TimeSpan circuitOpenTime = default,
            CancellationToken cancellationToken = default)
        {
            if (circuitOpenTime == default)
                circuitOpenTime = TimeSpan.FromMinutes(1);

            var circuitState = GetCircuitState(circuitName);

            // Check if circuit is open
            if (circuitState.IsOpen && DateTime.UtcNow < circuitState.OpenUntil)
            {
                _logger.LogWarning("Circuit breaker {CircuitName} is open. Request rejected", circuitName);
                throw new InvalidOperationException($"Circuit breaker {circuitName} is open");
            }

            try
            {
                var result = await operation();
                
                // Reset circuit on success
                if (circuitState.FailureCount > 0)
                {
                    _logger.LogInformation("Circuit breaker {CircuitName} reset after successful operation", circuitName);
                    ResetCircuit(circuitName);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(circuitName, failureThreshold, circuitOpenTime);
                
                _logger.LogError(ex, "Operation failed in circuit breaker {CircuitName}. Failure count: {FailureCount}", 
                    circuitName, circuitState.FailureCount + 1);
                
                throw;
            }
        }

        private bool ShouldRetry(Exception exception, int attempt, int maxRetries)
        {
            if (attempt >= maxRetries)
                return false;

            // Don't retry on certain exceptions
            if (exception is ArgumentException ||
                exception is UnauthorizedAccessException ||
                exception is SecurityException)
            {
                return false;
            }

            // Retry on transient failures
            if (exception is HttpRequestException ||
                exception is TimeoutException ||
                exception is TaskCanceledException ||
                exception is InvalidOperationException)
            {
                return true;
            }

            // Default to retry for unknown exceptions
            return true;
        }

        private TimeSpan CalculateDelay(TimeSpan baseDelay, int attempt)
        {
            // Exponential backoff with jitter
            var exponentialDelay = TimeSpan.FromMilliseconds(
                baseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));

            // Add jitter (±25%)
            var jitter = Random.Shared.NextDouble() * 0.5 - 0.25; // -0.25 to +0.25
            var jitteredDelay = TimeSpan.FromMilliseconds(
                exponentialDelay.TotalMilliseconds * (1 + jitter));

            // Cap at 30 seconds
            return jitteredDelay > TimeSpan.FromSeconds(30) 
                ? TimeSpan.FromSeconds(30) 
                : jitteredDelay;
        }

        private static readonly Dictionary<string, CircuitState> _circuits = new();
        private static readonly object _circuitLock = new();

        private CircuitState GetCircuitState(string circuitName)
        {
            lock (_circuitLock)
            {
                if (!_circuits.TryGetValue(circuitName, out var state))
                {
                    state = new CircuitState();
                    _circuits[circuitName] = state;
                }
                return state;
            }
        }

        private void RecordFailure(string circuitName, int failureThreshold, TimeSpan circuitOpenTime)
        {
            lock (_circuitLock)
            {
                var state = GetCircuitState(circuitName);
                state.FailureCount++;

                if (state.FailureCount >= failureThreshold)
                {
                    state.IsOpen = true;
                    state.OpenUntil = DateTime.UtcNow.Add(circuitOpenTime);
                    
                    _logger.LogWarning("Circuit breaker {CircuitName} opened after {FailureCount} failures. Open until {OpenUntil}", 
                        circuitName, state.FailureCount, state.OpenUntil);
                }
            }
        }

        private void ResetCircuit(string circuitName)
        {
            lock (_circuitLock)
            {
                if (_circuits.TryGetValue(circuitName, out var state))
                {
                    state.FailureCount = 0;
                    state.IsOpen = false;
                    state.OpenUntil = DateTime.MinValue;
                }
            }
        }
    }

    public class CircuitState
    {
        public int FailureCount { get; set; }
        public bool IsOpen { get; set; }
        public DateTime OpenUntil { get; set; }
    }
}
