using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.UserProfiles.Queries.GetProfile
{
    public class GetProfileQueryHandler : IRequestHandler<GetProfileQuery, UserProfileDto?>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<GetProfileQueryHandler> _logger;

        public GetProfileQueryHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<GetProfileQueryHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<UserProfileDto?> Handle(GetProfileQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting user profile with ID: {ProfileId}", request.Id);

                var userProfile = await _userProfileRepository.GetByIdAsync(request.Id);

                if (userProfile == null)
                {
                    _logger.LogWarning("User profile with ID {ProfileId} not found", request.Id);
                    return null;
                }

                var dto = MapToUserProfileDto(userProfile);

                _logger.LogInformation("Successfully retrieved user profile {ProfileId} for user {UserId}",
                    userProfile.Id, userProfile.UserId);

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user profile {ProfileId}", request.Id);
                throw;
            }
        }

        private UserProfileDto MapToUserProfileDto(Domain.Entities.UserProfile userProfile)
        {
            return new UserProfileDto
            {
                Id = userProfile.Id,
                UserId = userProfile.UserId,
                UserType = userProfile.UserType,
                Status = userProfile.Status,
                FirstName = userProfile.FirstName,
                LastName = userProfile.LastName,
                DisplayName = userProfile.GetDisplayName(),
                Email = userProfile.Email,
                PhoneNumber = userProfile.PhoneNumber,
                CompanyName = userProfile.CompanyName,
                GstNumber = userProfile.GstNumber,
                PanNumber = userProfile.PanNumber,
                AadharNumber = userProfile.AadharNumber,
                LicenseNumber = userProfile.LicenseNumber,
                Address = userProfile.Address,
                City = userProfile.City,
                State = userProfile.State,
                PostalCode = userProfile.PostalCode,
                Country = userProfile.Country,
                Region = userProfile.Region,
                CreatedAt = userProfile.CreatedAt,
                UpdatedAt = userProfile.UpdatedAt,
                LastLoginAt = userProfile.LastLoginAt,
                ApprovedAt = userProfile.ApprovedAt,
                ApprovedBy = userProfile.ApprovedBy,
                KycStatus = userProfile.KycStatus?.ToString() ?? "NotStarted",
                KycSubmittedAt = userProfile.KycSubmittedAt,
                KycApprovedAt = userProfile.KycApprovedAt,
                IsActive = userProfile.Status == Domain.Entities.ProfileStatus.Approved,
                IsComplete = userProfile.IsComplete,
                RfqCount = userProfile.RfqCount ?? 0,
                OrderCount = userProfile.OrderCount ?? 0,
                TripCount = userProfile.TripCount ?? 0,
                TotalBusinessValue = userProfile.TotalBusinessValue ?? 0,
                TotalDocuments = userProfile.TotalDocuments ?? 0,
                ApprovedDocuments = userProfile.ApprovedDocuments ?? 0,
                PendingDocuments = userProfile.PendingDocuments ?? 0,
                RejectedDocuments = userProfile.RejectedDocuments ?? 0,
                LastDocumentUpload = userProfile.LastDocumentUpload
            };
        }
    }
}
