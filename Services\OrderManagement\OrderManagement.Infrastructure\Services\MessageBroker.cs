using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using System.Text.Json;

namespace OrderManagement.Infrastructure.Services;

public class MessageBroker : IMessageBroker
{
    private readonly ILogger<MessageBroker> _logger;

    public MessageBroker(ILogger<MessageBroker> logger)
    {
        _logger = logger;
    }

    public Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default)
    {
        return PublishAsync(topic, (object)message!, cancellationToken);
    }

    public Task PublishAsync(string topic, object message, CancellationToken cancellationToken = default)
    {
        // For now, just log the message. In a real implementation, this would publish to RabbitMQ
        var json = JsonSerializer.Serialize(message, new JsonSerializerOptions { WriteIndented = true });
        _logger.LogInformation("Publishing message to topic {Topic}: {Message}", topic, json);
        
        return Task.CompletedTask;
    }
}
