namespace NetworkFleetManagement.Infrastructure.MessageBus;

public interface IMessageBusPublisher
{
    Task PublishAsync<T>(T message, string routingKey, CancellationToken cancellationToken = default) where T : class;
    Task PublishAsync(string message, string routingKey, CancellationToken cancellationToken = default);
    Task PublishIntegrationEventAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class;
}
