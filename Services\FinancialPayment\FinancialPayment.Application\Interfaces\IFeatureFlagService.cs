using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Application.Interfaces;

public interface IFeatureFlagService
{
    Task<bool> IsEnabledAsync(string flagKey, FeatureFlagContext? context = null);
    Task<T> GetValueAsync<T>(string flagKey, T defaultValue, FeatureFlagContext? context = null);
    Task<FeatureFlagEvaluation> EvaluateAsync(string flagKey, FeatureFlagContext? context = null);
    Task<Dictionary<string, FeatureFlagEvaluation>> EvaluateAllAsync(FeatureFlagContext? context = null);
    Task<FeatureFlag> CreateFlagAsync(CreateFeatureFlagRequest request);
    Task<FeatureFlag> UpdateFlagAsync(Guid flagId, UpdateFeatureFlagRequest request);
    Task<bool> DeleteFlagAsync(Guid flagId);
    Task<List<FeatureFlag>> GetFlagsAsync(string? environment = null);
    Task<FeatureFlag?> GetFlagAsync(Guid flagId);
    Task<FeatureFlag?> GetFlagByKeyAsync(string flagKey);
    Task<bool> EnableFlagAsync(Guid flagId, Guid? modifiedBy = null);
    Task<bool> DisableFlagAsync(Guid flagId, Guid? modifiedBy = null);
    Task<List<FeatureFlagAuditLog>> GetAuditLogsAsync(Guid flagId);
    Task<FeatureFlagAnalytics> GetAnalyticsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null);
}

public interface IFeatureFlagRepository
{
    Task<FeatureFlag?> GetByIdAsync(Guid id);
    Task<FeatureFlag?> GetByKeyAsync(string flagKey);
    Task<List<FeatureFlag>> GetByEnvironmentAsync(string environment);
    Task<List<FeatureFlag>> GetAllAsync();
    Task<List<FeatureFlag>> GetEnabledFlagsAsync(string? environment = null);
    Task AddAsync(FeatureFlag flag);
    Task UpdateAsync(FeatureFlag flag);
    Task DeleteAsync(Guid id);
}

public interface IFeatureFlagCache
{
    Task<FeatureFlag?> GetAsync(string flagKey);
    Task SetAsync(string flagKey, FeatureFlag flag, TimeSpan? expiry = null);
    Task RemoveAsync(string flagKey);
    Task ClearAsync();
    Task<Dictionary<string, FeatureFlag>> GetAllAsync();
}

public interface IFeatureFlagAnalyticsService
{
    Task RecordEvaluationAsync(string flagKey, FeatureFlagEvaluation evaluation, FeatureFlagContext? context = null);
    Task<FeatureFlagAnalytics> GetAnalyticsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null);
    Task<List<FeatureFlagUsageMetric>> GetUsageMetricsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<ExperimentResults> GetExperimentResultsAsync(string flagKey, DateTime? fromDate = null, DateTime? toDate = null);
}

// Request/Response DTOs
public class CreateFeatureFlagRequest
{
    public string FlagKey { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public FeatureFlagType Type { get; set; }
    public string Environment { get; set; } = "production";
    public Guid? CreatedBy { get; set; }
    public Dictionary<string, object> DefaultValue { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<CreateFeatureFlagRuleRequest> Rules { get; set; } = new();
    public List<CreateFeatureFlagVariantRequest> Variants { get; set; } = new();
}

public class UpdateFeatureFlagRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsEnabled { get; set; }
    public Dictionary<string, object>? DefaultValue { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
    public Guid? ModifiedBy { get; set; }
}

public class CreateFeatureFlagRuleRequest
{
    public string RuleName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int Priority { get; set; } = 0;
}

public class CreateFeatureFlagVariantRequest
{
    public string VariantKey { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int Weight { get; set; }
    public Dictionary<string, object> Value { get; set; } = new();
}

public class FeatureFlagAnalytics
{
    public string FlagKey { get; set; } = string.Empty;
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalEvaluations { get; set; }
    public int EnabledEvaluations { get; set; }
    public int DisabledEvaluations { get; set; }
    public decimal EnabledPercentage { get; set; }
    public Dictionary<string, int> VariantDistribution { get; set; } = new();
    public Dictionary<string, int> RuleDistribution { get; set; } = new();
    public List<FeatureFlagUsageMetric> DailyMetrics { get; set; } = new();
}

public class FeatureFlagUsageMetric
{
    public string FlagKey { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public int EvaluationCount { get; set; }
    public int UniqueUsers { get; set; }
    public decimal EnabledPercentage { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ExperimentResults
{
    public string FlagKey { get; set; } = string.Empty;
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int TotalParticipants { get; set; }
    public List<VariantResult> VariantResults { get; set; } = new();
    public Dictionary<string, decimal> ConversionRates { get; set; } = new();
    public bool IsStatisticallySignificant { get; set; }
    public decimal ConfidenceLevel { get; set; }
}

public class VariantResult
{
    public string VariantKey { get; set; } = string.Empty;
    public string VariantName { get; set; } = string.Empty;
    public int Participants { get; set; }
    public int Conversions { get; set; }
    public decimal ConversionRate { get; set; }
    public decimal Revenue { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class FeatureFlagConfiguration
{
    public bool EnableCaching { get; set; } = true;
    public int CacheExpiryMinutes { get; set; } = 15;
    public bool EnableAnalytics { get; set; } = true;
    public bool EnableAuditLogging { get; set; } = true;
    public string DefaultEnvironment { get; set; } = "production";
    public List<string> SupportedEnvironments { get; set; } = new() { "development", "staging", "production" };
    public int MaxRulesPerFlag { get; set; } = 10;
    public int MaxVariantsPerFlag { get; set; } = 5;
    public bool EnableRealTimeUpdates { get; set; } = true;
    public Dictionary<string, object> DefaultValues { get; set; } = new();
}

public class FeatureFlagBulkOperation
{
    public List<string> FlagKeys { get; set; } = new();
    public string Operation { get; set; } = string.Empty; // enable, disable, delete
    public string? Environment { get; set; }
    public Guid? PerformedBy { get; set; }
    public string? Reason { get; set; }
}

public class FeatureFlagExport
{
    public List<FeatureFlag> Flags { get; set; } = new();
    public string Environment { get; set; } = string.Empty;
    public DateTime ExportedAt { get; set; }
    public Guid? ExportedBy { get; set; }
    public string Format { get; set; } = "json"; // json, yaml, csv
}

public class FeatureFlagImport
{
    public List<CreateFeatureFlagRequest> Flags { get; set; } = new();
    public string Environment { get; set; } = string.Empty;
    public bool OverwriteExisting { get; set; } = false;
    public Guid? ImportedBy { get; set; }
    public string Format { get; set; } = "json";
}
