using Shared.Domain.Common;
using TripManagement.Domain.Enums;
using TripManagement.Domain.ValueObjects;
using TripManagement.Domain.Events;

namespace TripManagement.Domain.Entities;

public class Trip : AggregateRoot
{
    public Guid OrderId { get; private set; }
    public string TripNumber { get; private set; } = string.Empty;
    public Guid CarrierId { get; private set; }
    public Guid? DriverId { get; private set; }
    public Guid? VehicleId { get; private set; }
    public TripStatus Status { get; private set; }
    public Route Route { get; private set; } = null!;
    public new DateTime CreatedAt { get; private set; }
    public DateTime? AssignedAt { get; private set; }
    public DateTime? StartedAt { get; private set; }
    public DateTime? CompletedAt { get; private set; }
    public DateTime? CancelledAt { get; private set; }
    public string? CancellationReason { get; private set; }
    public DateTime EstimatedStartTime { get; private set; }
    public DateTime EstimatedEndTime { get; private set; }
    public string? SpecialInstructions { get; private set; }
    public bool IsUrgent { get; private set; }
    public decimal? EstimatedDistanceKm { get; private set; }
    public decimal? ActualDistanceKm { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public Driver? Driver { get; private set; }
    public Vehicle? Vehicle { get; private set; }

    private readonly List<TripStop> _stops = new();
    public IReadOnlyCollection<TripStop> Stops => _stops.AsReadOnly();

    private readonly List<TripLocationUpdate> _locationUpdates = new();
    public IReadOnlyCollection<TripLocationUpdate> LocationUpdates => _locationUpdates.AsReadOnly();

    private readonly List<TripException> _exceptions = new();
    public IReadOnlyCollection<TripException> Exceptions => _exceptions.AsReadOnly();

    private readonly List<TripDocument> _documents = new();
    public IReadOnlyCollection<TripDocument> Documents => _documents.AsReadOnly();

    private readonly List<TripLeg> _legs = new();
    public IReadOnlyCollection<TripLeg> Legs => _legs.AsReadOnly();

    // Parameterless constructor for EF Core
    private Trip() { }

    public Trip(
        Guid orderId,
        Guid carrierId,
        Route route,
        DateTime estimatedStartTime,
        DateTime estimatedEndTime,
        string? specialInstructions = null,
        bool isUrgent = false,
        decimal? estimatedDistanceKm = null)
    {
        if (estimatedStartTime >= estimatedEndTime)
            throw new ArgumentException("Estimated start time must be before end time");

        OrderId = orderId;
        CarrierId = carrierId;
        Route = route ?? throw new ArgumentNullException(nameof(route));
        TripNumber = GenerateTripNumber();
        Status = TripStatus.Created;
        CreatedAt = DateTime.UtcNow;
        EstimatedStartTime = estimatedStartTime;
        EstimatedEndTime = estimatedEndTime;
        SpecialInstructions = specialInstructions;
        IsUrgent = isUrgent;
        EstimatedDistanceKm = estimatedDistanceKm ?? (decimal)route.TotalEstimatedDistance;

        // Add domain event
        AddDomainEvent(new TripCreatedEvent(Id, OrderId, CarrierId, TripNumber));
    }

    public void AssignDriverAndVehicle(Guid driverId, Guid vehicleId)
    {
        if (Status != TripStatus.Created)
            throw new InvalidOperationException("Can only assign driver and vehicle to created trips");

        DriverId = driverId;
        VehicleId = vehicleId;
        Status = TripStatus.Assigned;
        AssignedAt = DateTime.UtcNow;

        // Add domain event
        AddDomainEvent(new TripAssignedEvent(Id, driverId, vehicleId, TripNumber));
    }

    public void Start()
    {
        if (Status != TripStatus.Assigned)
            throw new InvalidOperationException("Can only start assigned trips");

        if (!DriverId.HasValue || !VehicleId.HasValue)
            throw new InvalidOperationException("Cannot start trip without driver and vehicle assignment");

        Status = TripStatus.InProgress;
        StartedAt = DateTime.UtcNow;

        // Add domain event
        AddDomainEvent(new TripStartedEvent(Id, StartedAt.Value, Route.StartLocation));
    }

    public void Complete()
    {
        if (Status != TripStatus.InProgress)
            throw new InvalidOperationException("Can only complete in-progress trips");

        // Check if all stops are completed
        var incompleteStops = _stops.Where(s => s.Status != TripStopStatus.Completed && s.Status != TripStopStatus.Skipped).ToList();
        if (incompleteStops.Any())
            throw new InvalidOperationException("Cannot complete trip with incomplete stops");

        Status = TripStatus.Completed;
        CompletedAt = DateTime.UtcNow;

        // Add domain event
        AddDomainEvent(new TripCompletedEvent(Id, CompletedAt.Value, ActualDistanceKm));
    }

    public void Cancel(string reason)
    {
        if (Status == TripStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed trips");

        Status = TripStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;

        // Add domain event
        AddDomainEvent(new TripCancelledEvent(Id, reason));
    }

    public void AddStop(TripStop stop)
    {
        if (Status != TripStatus.Created && Status != TripStatus.Assigned)
            throw new InvalidOperationException("Cannot add stops to trips that are in progress or completed");

        _stops.Add(stop);
    }

    public void UpdateLocation(Location location, LocationUpdateSource source = LocationUpdateSource.GPS)
    {
        if (Status != TripStatus.InProgress)
            throw new InvalidOperationException("Can only update location for in-progress trips");

        var locationUpdate = new TripLocationUpdate(Id, location, source);
        _locationUpdates.Add(locationUpdate);

        // Add domain event
        AddDomainEvent(new TripLocationUpdatedEvent(Id, location, DateTime.UtcNow));
    }

    public void AddException(ExceptionType exceptionType, string description, Location? location = null)
    {
        var exception = new TripException(Id, exceptionType, description, location);
        _exceptions.Add(exception);

        // Update trip status if needed
        if (Status == TripStatus.InProgress)
        {
            Status = TripStatus.Exception;
        }

        // Add domain event
        AddDomainEvent(new TripExceptionEvent(Id, exceptionType, description));
    }

    public void ResolveException(Guid exceptionId, string resolution)
    {
        var exception = _exceptions.FirstOrDefault(e => e.Id == exceptionId);
        if (exception == null)
            throw new ArgumentException("Exception not found", nameof(exceptionId));

        exception.Resolve(resolution);

        // If all exceptions are resolved, return to in-progress status
        if (!_exceptions.Any(e => !e.IsResolved) && Status == TripStatus.Exception)
        {
            Status = TripStatus.InProgress;
        }
    }

    public void AddDocument(TripDocument document)
    {
        _documents.Add(document);
    }

    public void UpdateActualDistance(decimal distanceKm)
    {
        if (distanceKm < 0)
            throw new ArgumentException("Distance cannot be negative", nameof(distanceKm));

        ActualDistanceKm = distanceKm;
    }

    public Location? GetCurrentLocation()
    {
        return _locationUpdates.OrderByDescending(l => l.Timestamp).FirstOrDefault()?.Location;
    }

    public TimeSpan? GetActualDuration()
    {
        if (StartedAt.HasValue && CompletedAt.HasValue)
            return CompletedAt.Value - StartedAt.Value;

        if (StartedAt.HasValue)
            return DateTime.UtcNow - StartedAt.Value;

        return null;
    }

    public bool IsDelayed()
    {
        if (Status == TripStatus.Completed && CompletedAt.HasValue)
            return CompletedAt.Value > EstimatedEndTime;

        return DateTime.UtcNow > EstimatedEndTime && Status != TripStatus.Completed;
    }

    public void UpdateRoute(Route newRoute)
    {
        Route = newRoute ?? throw new ArgumentNullException(nameof(newRoute));
        EstimatedDistanceKm = (decimal)newRoute.TotalEstimatedDistance;
    }

    public void UpdateCurrentLocation(Location location)
    {
        UpdateLocation(location, LocationUpdateSource.GPS);
    }

    public void UpdateEstimatedEndTime(DateTime newEstimatedEndTime)
    {
        EstimatedEndTime = newEstimatedEndTime;
    }

    // Multi-Leg Trip Management
    public TripLeg AddLeg(
        int legNumber,
        string legName,
        Route route,
        DateTime estimatedStartTime,
        DateTime estimatedEndTime,
        string? description = null,
        string? specialInstructions = null,
        bool isRequired = true,
        int priority = 1)
    {
        // Validate leg number is unique
        if (_legs.Any(l => l.LegNumber == legNumber))
            throw new InvalidOperationException($"Leg number {legNumber} already exists");

        var leg = new TripLeg(
            Id,
            legNumber,
            legName,
            route,
            estimatedStartTime,
            estimatedEndTime,
            description,
            specialInstructions,
            isRequired,
            priority);

        _legs.Add(leg);
        return leg;
    }

    public void RemoveLeg(int legNumber)
    {
        var leg = _legs.FirstOrDefault(l => l.LegNumber == legNumber);
        if (leg == null)
            throw new ArgumentException($"Leg number {legNumber} not found");

        if (leg.Status == TripLegStatus.InProgress || leg.Status == TripLegStatus.Completed)
            throw new InvalidOperationException("Cannot remove in-progress or completed legs");

        _legs.Remove(leg);
    }

    public void ReorderLegs(Dictionary<int, int> legNumberMappings)
    {
        foreach (var mapping in legNumberMappings)
        {
            var leg = _legs.FirstOrDefault(l => l.LegNumber == mapping.Key);
            if (leg != null)
            {
                // This would require updating the leg number property
                // For now, we'll throw an exception as this requires more complex logic
                throw new NotImplementedException("Leg reordering requires database-level updates");
            }
        }
    }

    public void AssignResourceToLeg(int legNumber, Guid? driverId, Guid? vehicleId)
    {
        var leg = _legs.FirstOrDefault(l => l.LegNumber == legNumber);
        if (leg == null)
            throw new ArgumentException($"Leg number {legNumber} not found");

        leg.AssignResources(driverId, vehicleId);
    }

    public TripLeg? GetCurrentLeg()
    {
        return _legs.FirstOrDefault(l => l.Status == TripLegStatus.InProgress);
    }

    public TripLeg? GetNextLeg()
    {
        var currentLeg = GetCurrentLeg();
        if (currentLeg == null)
        {
            // Return first planned or assigned leg
            return _legs.Where(l => l.Status == TripLegStatus.Planned || l.Status == TripLegStatus.Assigned)
                       .OrderBy(l => l.LegNumber)
                       .FirstOrDefault();
        }

        // Return next leg in sequence
        return _legs.Where(l => l.LegNumber > currentLeg.LegNumber)
                   .OrderBy(l => l.LegNumber)
                   .FirstOrDefault();
    }

    public List<TripLeg> GetLegsInOrder()
    {
        return _legs.OrderBy(l => l.LegNumber).ToList();
    }

    public bool IsMultiLegTrip()
    {
        return _legs.Count > 1;
    }

    public decimal GetOverallCompletionPercentage()
    {
        if (!_legs.Any())
            return GetSingleTripCompletionPercentage();

        var totalLegs = _legs.Count;
        var completedLegs = _legs.Count(l => l.Status == TripLegStatus.Completed);
        var inProgressLeg = _legs.FirstOrDefault(l => l.Status == TripLegStatus.InProgress);

        decimal baseCompletion = (decimal)completedLegs / totalLegs * 100m;

        if (inProgressLeg != null)
        {
            var legCompletion = inProgressLeg.GetCompletionPercentage();
            baseCompletion += (legCompletion / totalLegs);
        }

        return Math.Min(100m, baseCompletion);
    }

    private decimal GetSingleTripCompletionPercentage()
    {
        if (Status == TripStatus.Completed)
            return 100m;

        if (Status == TripStatus.Created || Status == TripStatus.Assigned)
            return 0m;

        var totalStops = _stops.Count;
        if (totalStops == 0)
        {
            // Base completion on time if no stops
            if (StartedAt.HasValue)
            {
                var elapsed = DateTime.UtcNow - StartedAt.Value;
                var estimated = EstimatedEndTime - EstimatedStartTime;
                return Math.Min(100m, (decimal)(elapsed.TotalMinutes / estimated.TotalMinutes * 100));
            }
            return 0m;
        }

        var completedStops = _stops.Count(s => s.Status == TripStopStatus.Completed);
        return (decimal)completedStops / totalStops * 100m;
    }

    public DateTime? GetEstimatedCompletionTime()
    {
        if (!_legs.Any())
            return EstimatedEndTime;

        var lastLeg = _legs.OrderByDescending(l => l.LegNumber).FirstOrDefault();
        return lastLeg?.EstimatedEndTime ?? EstimatedEndTime;
    }

    public decimal? GetTotalEstimatedDistance()
    {
        if (!_legs.Any())
            return EstimatedDistanceKm;

        return _legs.Sum(l => l.EstimatedDistanceKm ?? 0);
    }

    public decimal? GetTotalActualDistance()
    {
        if (!_legs.Any())
            return ActualDistanceKm;

        return _legs.Sum(l => l.ActualDistanceKm ?? 0);
    }

    private static string GenerateTripNumber()
    {
        return $"TRP-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
    }
}

public class TripStop : BaseEntity
{
    public Guid TripId { get; private set; }
    public TripStopType StopType { get; private set; }
    public int SequenceNumber { get; private set; }
    public Location Location { get; private set; } = null!;
    public string? ContactName { get; private set; }
    public string? ContactPhone { get; private set; }
    public DateTime? ScheduledArrival { get; private set; }
    public DateTime? ScheduledDeparture { get; private set; }
    public DateTime? ActualArrival { get; private set; }
    public DateTime? ActualDeparture { get; private set; }
    public TripStopStatus Status { get; private set; }
    public string? Instructions { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public Trip Trip { get; private set; } = null!;

    private readonly List<ProofOfDelivery> _proofOfDeliveries = new();
    public IReadOnlyCollection<ProofOfDelivery> ProofOfDeliveries => _proofOfDeliveries.AsReadOnly();

    // Parameterless constructor for EF Core
    private TripStop() { }

    public TripStop(
        Guid tripId,
        TripStopType stopType,
        int sequenceNumber,
        Location location,
        string? contactName = null,
        string? contactPhone = null,
        DateTime? scheduledArrival = null,
        DateTime? scheduledDeparture = null,
        string? instructions = null)
    {
        TripId = tripId;
        StopType = stopType;
        SequenceNumber = sequenceNumber;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        ContactName = contactName;
        ContactPhone = contactPhone;
        ScheduledArrival = scheduledArrival;
        ScheduledDeparture = scheduledDeparture;
        Status = TripStopStatus.Pending;
        Instructions = instructions;
    }

    public void MarkArrived()
    {
        if (Status != TripStopStatus.Pending)
            throw new InvalidOperationException("Can only mark pending stops as arrived");

        Status = TripStopStatus.Arrived;
        ActualArrival = DateTime.UtcNow;
    }

    public void StartProcessing()
    {
        if (Status != TripStopStatus.Arrived)
            throw new InvalidOperationException("Can only start processing arrived stops");

        Status = TripStopStatus.InProgress;
    }

    public void Complete(string? notes = null)
    {
        if (Status != TripStopStatus.InProgress && Status != TripStopStatus.Arrived)
            throw new InvalidOperationException("Can only complete in-progress or arrived stops");

        Status = TripStopStatus.Completed;
        ActualDeparture = DateTime.UtcNow;
        Notes = notes;
    }

    public void Skip(string reason)
    {
        if (Status == TripStopStatus.Completed)
            throw new InvalidOperationException("Cannot skip completed stops");

        Status = TripStopStatus.Skipped;
        Notes = $"Skipped: {reason}";
    }

    public void Fail(string reason)
    {
        Status = TripStopStatus.Failed;
        Notes = $"Failed: {reason}";
    }

    public void AddProofOfDelivery(ProofOfDelivery pod)
    {
        _proofOfDeliveries.Add(pod);
    }

    public bool IsDelayed()
    {
        if (ScheduledArrival.HasValue)
        {
            if (ActualArrival.HasValue)
                return ActualArrival.Value > ScheduledArrival.Value;

            return DateTime.UtcNow > ScheduledArrival.Value && Status == TripStopStatus.Pending;
        }

        return false;
    }

    public void UpdateContactInfo(string? contactName, string? contactPhone)
    {
        ContactName = contactName;
        ContactPhone = contactPhone;
    }

    public void UpdateScheduledArrival(DateTime scheduledArrival)
    {
        ScheduledArrival = scheduledArrival;
    }
}
