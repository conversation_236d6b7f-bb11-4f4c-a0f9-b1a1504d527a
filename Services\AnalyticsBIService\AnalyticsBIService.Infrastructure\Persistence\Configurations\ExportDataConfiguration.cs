using AnalyticsBIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AnalyticsBIService.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity configuration for ExportData
/// </summary>
public class ExportDataConfiguration : IEntityTypeConfiguration<ExportData>
{
    public void Configure(EntityTypeBuilder<ExportData> builder)
    {
        builder.ToTable("export_data");

        builder.HasKey(ed => ed.Id);

        builder.Property(ed => ed.UserId)
            .IsRequired();

        builder.Property(ed => ed.ExportType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(ed => ed.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(ed => ed.FilePath)
            .HasMaxLength(500);

        builder.Property(ed => ed.Format)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(ed => ed.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(ed => ed.RequestedAt)
            .IsRequired();

        builder.Property(ed => ed.CompletedAt);

        builder.Property(ed => ed.FileSizeBytes);

        builder.Property(ed => ed.RecordCount);

        builder.Property(ed => ed.ErrorMessage)
            .HasMaxLength(2000);

        builder.Property(ed => ed.Parameters)
            .HasColumnType("jsonb");

        builder.Property(ed => ed.ExpiresAt);

        builder.Property(ed => ed.IsDownloaded)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(ed => ed.DownloadedAt);

        builder.Property(ed => ed.DownloadCount)
            .IsRequired()
            .HasDefaultValue(0);

        // Indexes
        builder.HasIndex(ed => ed.UserId)
            .HasDatabaseName("IX_ExportData_UserId");

        builder.HasIndex(ed => ed.Status)
            .HasDatabaseName("IX_ExportData_Status");

        builder.HasIndex(ed => ed.RequestedAt)
            .HasDatabaseName("IX_ExportData_RequestedAt");

        builder.HasIndex(ed => ed.ExpiresAt)
            .HasDatabaseName("IX_ExportData_ExpiresAt");

        builder.HasIndex(ed => new { ed.UserId, ed.RequestedAt })
            .HasDatabaseName("IX_ExportData_UserId_RequestedAt");

        builder.HasIndex(ed => new { ed.Status, ed.RequestedAt })
            .HasDatabaseName("IX_ExportData_Status_RequestedAt");
    }
}
