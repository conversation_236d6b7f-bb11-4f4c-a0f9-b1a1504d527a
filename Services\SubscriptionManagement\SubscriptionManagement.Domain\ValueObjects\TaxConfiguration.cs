using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Domain.ValueObjects
{
    public class TaxConfiguration : IEquatable<TaxConfiguration>
    {
        public TaxType TaxType { get; private set; }
        public decimal Rate { get; private set; }
        public bool IsIncluded { get; private set; }
        public List<string> ApplicableRegions { get; private set; }
        public DateTime EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }

        private TaxConfiguration() 
        {
            ApplicableRegions = new List<string>();
        }

        public TaxConfiguration(TaxType taxType, decimal rate, bool isIncluded, 
            List<string> applicableRegions, DateTime effectiveDate, DateTime? expirationDate = null)
        {
            if (rate < 0 || rate > 100)
                throw new SubscriptionDomainException("Tax rate must be between 0 and 100 percent");

            if (applicableRegions == null || !applicableRegions.Any())
                throw new SubscriptionDomainException("At least one applicable region must be specified");

            if (expirationDate.HasValue && expirationDate.Value <= effectiveDate)
                throw new SubscriptionDomainException("Expiration date must be after effective date");

            if (effectiveDate > DateTime.UtcNow.AddYears(1))
                throw new SubscriptionDomainException("Effective date cannot be more than 1 year in the future");

            TaxType = taxType;
            Rate = Math.Round(rate, 4); // Support up to 4 decimal places for precision
            IsIncluded = isIncluded;
            ApplicableRegions = applicableRegions.Select(r => r.ToUpperInvariant()).ToList();
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
        }

        public static TaxConfiguration Create(TaxType taxType, decimal rate, bool isIncluded,
            List<string> applicableRegions, DateTime effectiveDate, DateTime? expirationDate = null)
        {
            return new TaxConfiguration(taxType, rate, isIncluded, applicableRegions, effectiveDate, expirationDate);
        }

        public bool IsApplicableToRegion(string region)
        {
            if (string.IsNullOrWhiteSpace(region))
                return false;

            return ApplicableRegions.Contains(region.ToUpperInvariant());
        }

        public bool IsActiveOn(DateTime date)
        {
            return date >= EffectiveDate && (!ExpirationDate.HasValue || date <= ExpirationDate.Value);
        }

        public Money CalculateTax(Money baseAmount, string region)
        {
            if (!IsApplicableToRegion(region))
                return Money.Zero(baseAmount.Currency);

            if (!IsActiveOn(DateTime.UtcNow))
                return Money.Zero(baseAmount.Currency);

            var taxAmount = baseAmount.Amount * (Rate / 100);
            return Money.Create(taxAmount, baseAmount.Currency);
        }

        public Money CalculateBaseAmountFromTaxInclusivePrice(Money taxInclusiveAmount, string region)
        {
            if (!IsApplicableToRegion(region) || !IsIncluded)
                return taxInclusiveAmount;

            if (!IsActiveOn(DateTime.UtcNow))
                return taxInclusiveAmount;

            var baseAmount = taxInclusiveAmount.Amount / (1 + (Rate / 100));
            return Money.Create(baseAmount, taxInclusiveAmount.Currency);
        }

        public bool Equals(TaxConfiguration? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;

            return TaxType == other.TaxType &&
                   Rate == other.Rate &&
                   IsIncluded == other.IsIncluded &&
                   ApplicableRegions.SequenceEqual(other.ApplicableRegions) &&
                   EffectiveDate == other.EffectiveDate &&
                   ExpirationDate == other.ExpirationDate;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as TaxConfiguration);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(TaxType, Rate, IsIncluded, EffectiveDate, ExpirationDate);
        }

        public static bool operator ==(TaxConfiguration? left, TaxConfiguration? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(TaxConfiguration? left, TaxConfiguration? right)
        {
            return !Equals(left, right);
        }
    }
}
