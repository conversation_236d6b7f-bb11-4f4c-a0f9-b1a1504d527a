version: '3.8'

services:
  # Infrastructure Services
  postgres:
    image: postgres:15
    container_name: tli-postgres
    environment:
      POSTGRES_DB: tli_microservices
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - tli-network

  rabbitmq:
    image: rabbitmq:3-management
    container_name: tli-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tli-network

  redis:
    image: redis:7-alpine
    container_name: tli-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - tli-network

  # API Gateway
  apigateway:
    build:
      context: .
      dockerfile: ApiGateway/Dockerfile
    container_name: tli-apigateway
    ports:
      - '5000:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
    depends_on:
      - identity-api
      - usermanagement-api
      - datastorage-api
      - monitoring-api
      - auditcompliance-api
    networks:
      - tli-network

  # Identity Service
  identity-api:
    build:
      context: .
      dockerfile: Identity/Identity.API/Dockerfile
    container_name: tli-identity-api
    ports:
      - '5001:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_Identity;User Id=timescale;Password=timescale
      - RabbitMQ__Host=rabbitmq
      - JwtSettings__Secret=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - JwtSettings__Issuer=TLI.Identity
      - JwtSettings__Audience=TLI.Services
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - tli-network

  # User Management Service
  usermanagement-api:
    build:
      context: .
      dockerfile: Services/UserManagement/UserManagement.API/Dockerfile
    container_name: tli-usermanagement-api
    ports:
      - '5002:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_UserManagement;User Id=timescale;Password=timescale
      - JwtSettings__Secret=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - JwtSettings__Issuer=TLI.Identity
      - JwtSettings__Audience=TLI.Services
    depends_on:
      - postgres
    networks:
      - tli-network

  # Data & Storage Service
  datastorage-api:
    build:
      context: .
      dockerfile: Services/DataStorage/DataStorage.API/Dockerfile
    container_name: tli-datastorage-api
    ports:
      - '5010:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_DataStorage;User Id=timescale;Password=timescale
      - JwtSettings__Secret=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - JwtSettings__Issuer=TLI.Identity
      - JwtSettings__Audience=TLI.Services
      - StorageSettings__Provider=Local
      - StorageSettings__LocalPath=/app/storage
      - StorageSettings__MaxFileSize=104857600
    depends_on:
      - postgres
    volumes:
      - datastorage_files:/app/storage
    networks:
      - tli-network

  # Monitoring & Observability Service
  monitoring-api:
    build:
      context: .
      dockerfile: Services/MonitoringObservability/MonitoringObservability.API/Dockerfile
    container_name: tli-monitoring-api
    ports:
      - '5011:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_MonitoringObservability;User Id=timescale;Password=timescale
      - JwtSettings__Secret=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - JwtSettings__Issuer=TLI.Identity
      - JwtSettings__Audience=TLI.Services
      - MonitoringSettings__HealthCheckInterval=00:00:30
      - MonitoringSettings__MetricRetentionDays=30
      - MonitoringSettings__AlertRetentionDays=90
    depends_on:
      - postgres
    networks:
      - tli-network

  # Audit & Compliance Service
  auditcompliance-api:
    build:
      context: .
      dockerfile: Services/AuditCompliance/Dockerfile
    container_name: tli-auditcompliance-api
    ports:
      - '5012:8080'
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=TLI_AuditCompliance;User Id=timescale;Password=timescale
      - Jwt__Key=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Jwt__Issuer=TLI.Identity
      - Jwt__Audience=TLI.Services
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Username=guest
      - RabbitMQ__Password=guest
    depends_on:
      - postgres
      - rabbitmq
    volumes:
      - auditcompliance_logs:/app/logs
    networks:
      - tli-network

volumes:
  postgres_data:
  rabbitmq_data:
  redis_data:
  datastorage_files:
  auditcompliance_logs:

networks:
  tli-network:
    driver: bridge
