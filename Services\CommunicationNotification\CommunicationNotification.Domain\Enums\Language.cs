namespace CommunicationNotification.Domain.Enums;

public enum Language
{
    /// <summary>
    /// English language
    /// </summary>
    English = 1,
    
    /// <summary>
    /// Hindi language
    /// </summary>
    Hindi = 2,
    
    /// <summary>
    /// Kannada language
    /// </summary>
    Kannada = 3
}

public static class LanguageExtensions
{
    public static string ToCode(this Language language)
    {
        return language switch
        {
            Language.English => "en",
            Language.Hindi => "hi",
            Language.Kannada => "kn",
            _ => "en"
        };
    }
    
    public static string ToDisplayName(this Language language)
    {
        return language switch
        {
            Language.English => "English",
            Language.Hindi => "हिंदी",
            Language.Kannada => "ಕನ್ನಡ",
            _ => "English"
        };
    }
    
    public static Language FromCode(string code)
    {
        return code?.ToLower() switch
        {
            "en" => Language.English,
            "hi" => Language.Hindi,
            "kn" => Language.Kannada,
            _ => Language.English
        };
    }
}
