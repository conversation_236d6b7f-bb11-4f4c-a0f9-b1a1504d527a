using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace NetworkFleetManagement.Infrastructure.MessageBus;

public class AzureServiceBusPublisher : IMessageBusPublisher, IAsyncDisposable
{
    private readonly AzureServiceBusSettings _settings;
    private readonly ILogger<AzureServiceBusPublisher> _logger;
    private readonly ServiceBusClient _client;
    private readonly ServiceBusSender _sender;

    public AzureServiceBusPublisher(IOptions<AzureServiceBusSettings> settings, ILogger<AzureServiceBusPublisher> logger)
    {
        _settings = settings.Value;
        _logger = logger;

        try
        {
            _client = new ServiceBusClient(_settings.ConnectionString);
            _sender = _client.CreateSender(_settings.TopicName);
            
            _logger.LogInformation("Azure Service Bus connection established successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to establish Azure Service Bus connection");
            throw;
        }
    }

    public async Task PublishAsync<T>(T message, string routingKey, CancellationToken cancellationToken = default) where T : class
    {
        var jsonMessage = JsonSerializer.Serialize(message, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await PublishAsync(jsonMessage, routingKey, cancellationToken);
    }

    public async Task PublishAsync(string message, string routingKey, CancellationToken cancellationToken = default)
    {
        try
        {
            var serviceBusMessage = new ServiceBusMessage(Encoding.UTF8.GetBytes(message))
            {
                MessageId = Guid.NewGuid().ToString(),
                Subject = routingKey,
                ContentType = "application/json",
                TimeToLive = _settings.MessageTimeToLive
            };

            // Add custom properties
            serviceBusMessage.ApplicationProperties["Source"] = "NetworkFleetManagement";
            serviceBusMessage.ApplicationProperties["RoutingKey"] = routingKey;
            serviceBusMessage.ApplicationProperties["Timestamp"] = DateTimeOffset.UtcNow;

            await _sender.SendMessageAsync(serviceBusMessage, cancellationToken);

            _logger.LogDebug("Message published successfully to Azure Service Bus topic {Topic} with subject {Subject}", 
                _settings.TopicName, routingKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish message to Azure Service Bus");
            throw;
        }
    }

    public async Task PublishIntegrationEventAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class
    {
        var eventType = typeof(T).Name;
        var routingKey = $"network-fleet.{eventType.ToLowerInvariant()}";

        var eventWrapper = new
        {
            EventId = Guid.NewGuid(),
            EventType = eventType,
            Source = "NetworkFleetManagement",
            Timestamp = DateTime.UtcNow,
            Version = "1.0",
            Data = integrationEvent
        };

        await PublishAsync(eventWrapper, routingKey, cancellationToken);
        
        _logger.LogInformation("Integration event {EventType} published to Azure Service Bus with subject {Subject}", 
            eventType, routingKey);
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_sender != null)
                await _sender.DisposeAsync();
            
            if (_client != null)
                await _client.DisposeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error occurred while disposing Azure Service Bus resources");
        }
    }
}
