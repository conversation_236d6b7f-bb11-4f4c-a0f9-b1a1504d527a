﻿using Shared.Domain.ValueObjects;
using Shared.Domain.Common;
using AuditCompliance.Domain.Enums;

namespace AuditCompliance.Domain.ValueObjects;

/// <summary>
/// Rating score with validation and calculation capabilities
/// </summary>
public class RatingScore : ValueObject
{
    public RatingScale Scale { get; private set; }
    public decimal NumericValue { get; private set; }
    public string? Comment { get; private set; }

    private RatingScore() { }

    public RatingScore(RatingScale scale, string? comment = null)
    {
        Scale = scale;
        NumericValue = (decimal)scale;
        Comment = comment;
    }

    public RatingScore(decimal numericValue, string? comment = null)
    {
        if (numericValue < 1 || numericValue > 5)
            throw new ArgumentException("Rating must be between 1 and 5", nameof(numericValue));

        NumericValue = numericValue;
        Scale = (RatingScale)Math.Round(numericValue);
        Comment = comment;
    }

    public static RatingScore FromScale(RatingScale scale, string? comment = null)
    {
        return new RatingScore(scale, comment);
    }

    public static RatingScore FromNumeric(decimal value, string? comment = null)
    {
        return new RatingScore(value, comment);
    }

    public static RatingScore CalculateAverage(IEnumerable<RatingScore> ratings)
    {
        if (!ratings.Any())
            throw new ArgumentException("Cannot calculate average of empty ratings collection");

        var average = ratings.Average(r => r.NumericValue);
        return new RatingScore(average, $"Average of {ratings.Count()} ratings");
    }

    public bool IsPositive => NumericValue >= 3.5m;
    public bool IsNegative => NumericValue < 2.5m;
    public bool IsNeutral => NumericValue >= 2.5m && NumericValue < 3.5m;

    public string GetDescription()
    {
        return Scale switch
        {
            RatingScale.OneStar => "Poor",
            RatingScale.TwoStars => "Below Average",
            RatingScale.ThreeStars => "Average",
            RatingScale.FourStars => "Good",
            RatingScale.FiveStars => "Excellent",
            _ => "Unknown"
        };
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Scale;
        yield return NumericValue;
        yield return Comment ?? string.Empty;
    }
}


