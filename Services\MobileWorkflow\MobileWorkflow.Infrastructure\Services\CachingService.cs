using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Text.Json;
using System.Collections.Concurrent;

namespace MobileWorkflow.Infrastructure.Services;

public interface ICachingService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default);
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default);
    Task SetManyAsync<T>(Dictionary<string, T> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task<double> IncrementAsync(string key, double value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
}

public class CachingService : ICachingService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IMemoryCache _memoryCache;
    private readonly IDatabase _redisDatabase;
    private readonly ILogger<CachingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, HashSet<string>> _taggedKeys;
    private readonly JsonSerializerOptions _jsonOptions;

    public CachingService(
        IDistributedCache distributedCache,
        IMemoryCache memoryCache,
        IConnectionMultiplexer redis,
        ILogger<CachingService> logger,
        IConfiguration configuration)
    {
        _distributedCache = distributedCache;
        _memoryCache = memoryCache;
        _redisDatabase = redis.GetDatabase();
        _logger = logger;
        _configuration = configuration;
        _taggedKeys = new ConcurrentDictionary<string, HashSet<string>>();
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try memory cache first for frequently accessed data
            if (_memoryCache.TryGetValue(key, out T? memoryValue))
            {
                _logger.LogDebug("Cache hit (memory): {Key}", key);
                return memoryValue;
            }

            // Try distributed cache
            var distributedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            if (distributedValue != null)
            {
                var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue, _jsonOptions);
                
                // Store in memory cache for faster subsequent access
                var memoryCacheExpiration = TimeSpan.FromMinutes(5);
                _memoryCache.Set(key, deserializedValue, memoryCacheExpiration);
                
                _logger.LogDebug("Cache hit (distributed): {Key}", key);
                return deserializedValue;
            }

            _logger.LogDebug("Cache miss: {Key}", key);
            return default;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
            return default;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }
            else
            {
                // Default expiration
                options.SetAbsoluteExpiration(TimeSpan.FromHours(1));
            }

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
            
            // Also store in memory cache for faster access
            var memoryCacheExpiration = expiration ?? TimeSpan.FromMinutes(5);
            _memoryCache.Set(key, value, memoryCacheExpiration);
            
            _logger.LogDebug("Cache set: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
            _memoryCache.Remove(key);
            
            _logger.LogDebug("Cache removed: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _redisDatabase.Multiplexer.GetServer(_redisDatabase.Multiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern);
            
            var tasks = keys.Select(key => _redisDatabase.KeyDeleteAsync(key));
            await Task.WhenAll(tasks);
            
            _logger.LogDebug("Cache removed by pattern: {Pattern}", pattern);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache values by pattern: {Pattern}", pattern);
        }
    }

    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out _))
            {
                return true;
            }

            return await _redisDatabase.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
            return false;
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            var value = await factory();
            await SetAsync(key, value, expiration, cancellationToken);
            
            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetOrSet for key: {Key}", key);
            return await factory();
        }
    }

    public async Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_taggedKeys.TryGetValue(tag, out var keys))
            {
                var tasks = keys.Select(key => RemoveAsync(key, cancellationToken));
                await Task.WhenAll(tasks);
                
                _taggedKeys.TryRemove(tag, out _);
                
                _logger.LogDebug("Cache invalidated by tag: {Tag}, Keys: {KeyCount}", tag, keys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache by tag: {Tag}", tag);
        }
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<string, T?>();
        var tasks = keys.Select(async key =>
        {
            var value = await GetAsync<T>(key, cancellationToken);
            return new KeyValuePair<string, T?>(key, value);
        });

        var results = await Task.WhenAll(tasks);
        foreach (var kvp in results)
        {
            result[kvp.Key] = kvp.Value;
        }

        return result;
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        var tasks = items.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
        await Task.WhenAll(tasks);
    }

    public async Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _redisDatabase.StringIncrementAsync(key, value);
            
            if (expiration.HasValue)
            {
                await _redisDatabase.KeyExpireAsync(key, expiration.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing cache value for key: {Key}", key);
            return 0;
        }
    }

    public async Task<double> IncrementAsync(string key, double value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _redisDatabase.StringIncrementAsync(key, value);
            
            if (expiration.HasValue)
            {
                await _redisDatabase.KeyExpireAsync(key, expiration.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing cache value for key: {Key}", key);
            return 0;
        }
    }
}

public static class CacheKeys
{
    public const string UserPrefix = "user:";
    public const string WorkflowPrefix = "workflow:";
    public const string FormPrefix = "form:";
    public const string DevicePrefix = "device:";
    public const string NotificationPrefix = "notification:";
    public const string AnalyticsPrefix = "analytics:";
    public const string GeofencePrefix = "geofence:";
    public const string FilePrefix = "file:";
    public const string TestPrefix = "test:";
    
    public static string UserKey(Guid userId) => $"{UserPrefix}{userId}";
    public static string WorkflowKey(Guid workflowId) => $"{WorkflowPrefix}{workflowId}";
    public static string FormKey(Guid formId) => $"{FormPrefix}{formId}";
    public static string DeviceKey(string deviceId) => $"{DevicePrefix}{deviceId}";
    public static string NotificationKey(Guid notificationId) => $"{NotificationPrefix}{notificationId}";
    public static string AnalyticsKey(string metric, DateTime date) => $"{AnalyticsPrefix}{metric}:{date:yyyy-MM-dd}";
    public static string GeofenceKey(Guid geofenceId) => $"{GeofencePrefix}{geofenceId}";
    public static string FileKey(Guid fileId) => $"{FilePrefix}{fileId}";
    public static string TestKey(Guid testId) => $"{TestPrefix}{testId}";
}

public static class CacheTags
{
    public const string Users = "users";
    public const string Workflows = "workflows";
    public const string Forms = "forms";
    public const string Devices = "devices";
    public const string Notifications = "notifications";
    public const string Analytics = "analytics";
    public const string Geofences = "geofences";
    public const string Files = "files";
    public const string Tests = "tests";
}
