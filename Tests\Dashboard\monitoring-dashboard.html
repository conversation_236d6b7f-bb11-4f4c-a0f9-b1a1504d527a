<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLI Microservices - Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .status.healthy {
            background: #d4edda;
            color: #155724;
        }

        .status.unhealthy {
            background: #f8d7da;
            color: #721c24;
        }

        .status.unknown {
            background: #fff3cd;
            color: #856404;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.test {
            background: #e74c3c;
        }

        .btn.test:hover {
            background: #c0392b;
        }

        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .metric-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .loading {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
        }

        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TLI Microservices</h1>
            <p>Monitoring Dashboard & Service Testing</p>
        </div>

        <div class="dashboard">
            <!-- Service Health Section -->
            <div class="section">
                <h2>🏥 Service Health Status</h2>
                <div class="grid" id="health-grid">
                    <div class="card">
                        <h3>API Gateway</h3>
                        <div class="status unknown" id="gateway-status">Checking...</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" onclick="checkHealth('gateway', 'http://localhost:5000/health')">Check Health</button>
                        </div>
                    </div>
                    <div class="card">
                        <h3>Data Storage Service</h3>
                        <div class="status unknown" id="datastorage-status">Checking...</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" onclick="checkHealth('datastorage', 'http://localhost:5010/health')">Check Health</button>
                            <button class="btn" onclick="checkHealth('datastorage-via-gateway', 'http://localhost:5000/health/datastorage')">Via Gateway</button>
                        </div>
                    </div>
                    <div class="card">
                        <h3>Monitoring Service</h3>
                        <div class="status unknown" id="monitoring-status">Checking...</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" onclick="checkHealth('monitoring', 'http://localhost:5011/health')">Check Health</button>
                            <button class="btn" onclick="checkHealth('monitoring-via-gateway', 'http://localhost:5000/health/monitoring')">Via Gateway</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Testing Section -->
            <div class="section">
                <h2>🧪 API Testing</h2>
                <div class="grid">
                    <div class="card">
                        <h3>Document Management</h3>
                        <button class="btn test" onclick="testDocumentUpload()">Test Document Upload</button>
                        <button class="btn" onclick="testDocumentsList()">List Documents</button>
                        <button class="btn" onclick="testDocumentPermissions()">Test Permissions</button>
                    </div>
                    <div class="card">
                        <h3>Monitoring APIs</h3>
                        <button class="btn test" onclick="testMetricsAPI()">Test Metrics API</button>
                        <button class="btn" onclick="testAlertsAPI()">Test Alerts API</button>
                        <button class="btn" onclick="testHealthChecksAPI()">Test Health Checks API</button>
                    </div>
                    <div class="card">
                        <h3>Performance Testing</h3>
                        <button class="btn test" onclick="testConcurrentRequests()">Concurrent Requests</button>
                        <button class="btn" onclick="testResponseTimes()">Response Times</button>
                        <button class="btn" onclick="testLoadTesting()">Load Testing</button>
                    </div>
                </div>
            </div>

            <!-- Metrics Section -->
            <div class="section">
                <h2>📊 System Metrics</h2>
                <div class="metrics-grid" id="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="total-requests">-</div>
                        <div class="metric-label">Total Requests</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="avg-response-time">-</div>
                        <div class="metric-label">Avg Response Time (ms)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="success-rate">-</div>
                        <div class="metric-label">Success Rate (%)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="active-services">-</div>
                        <div class="metric-label">Active Services</div>
                    </div>
                </div>
            </div>

            <!-- Activity Log Section -->
            <div class="section">
                <h2>📝 Activity Log</h2>
                <div class="log" id="activity-log">
Welcome to TLI Microservices Monitoring Dashboard!
Click the buttons above to test various services and APIs.

Initializing dashboard...
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn" onclick="clearLog()">Clear Log</button>
                    <button class="btn" onclick="exportLog()">Export Log</button>
                    <button class="btn" onclick="autoRefresh()">Auto Refresh</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let requestCount = 0;
        let totalResponseTime = 0;
        let successCount = 0;
        let autoRefreshInterval = null;

        function log(message) {
            const logElement = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `\n[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateMetrics() {
            document.getElementById('total-requests').textContent = requestCount;
            document.getElementById('avg-response-time').textContent = 
                requestCount > 0 ? Math.round(totalResponseTime / requestCount) : '-';
            document.getElementById('success-rate').textContent = 
                requestCount > 0 ? Math.round((successCount / requestCount) * 100) : '-';
        }

        async function checkHealth(serviceId, url) {
            const startTime = performance.now();
            requestCount++;
            
            try {
                log(`Checking health for ${serviceId}: ${url}`);
                const response = await fetch(url);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                totalResponseTime += responseTime;
                
                const statusElement = document.getElementById(`${serviceId}-status`);
                
                if (response.ok) {
                    successCount++;
                    statusElement.textContent = 'Healthy';
                    statusElement.className = 'status healthy';
                    log(`✅ ${serviceId} is healthy (${responseTime}ms)`);
                } else {
                    statusElement.textContent = 'Unhealthy';
                    statusElement.className = 'status unhealthy';
                    log(`❌ ${serviceId} is unhealthy: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                const statusElement = document.getElementById(`${serviceId}-status`);
                statusElement.textContent = 'Unavailable';
                statusElement.className = 'status unhealthy';
                log(`🔌 ${serviceId} is unavailable: ${error.message}`);
            }
            
            updateMetrics();
        }

        async function testDocumentUpload() {
            const startTime = performance.now();
            requestCount++;
            
            try {
                log('Testing document upload...');
                const testDocument = {
                    fileName: 'test-document.pdf',
                    originalFileName: 'Test Document.pdf',
                    contentType: 'application/pdf',
                    fileSize: 1024,
                    documentType: 1,
                    category: 1,
                    description: 'Test document from dashboard',
                    tags: { test: 'true', source: 'dashboard' }
                };
                
                const response = await fetch('http://localhost:5000/api/documents', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testDocument)
                });
                
                const endTime = performance.now();
                totalResponseTime += (endTime - startTime);
                
                if (response.ok) {
                    successCount++;
                    const result = await response.json();
                    log(`✅ Document uploaded successfully: ${result.id || 'ID not returned'}`);
                } else {
                    log(`❌ Document upload failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`🔌 Document upload error: ${error.message}`);
            }
            
            updateMetrics();
        }

        async function testDocumentsList() {
            const startTime = performance.now();
            requestCount++;
            
            try {
                log('Testing documents list...');
                const response = await fetch('http://localhost:5000/api/documents?pageSize=5&pageNumber=1');
                
                const endTime = performance.now();
                totalResponseTime += (endTime - startTime);
                
                if (response.ok) {
                    successCount++;
                    const result = await response.json();
                    log(`✅ Documents list retrieved: ${Array.isArray(result) ? result.length : 'Unknown count'} items`);
                } else {
                    log(`❌ Documents list failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`🔌 Documents list error: ${error.message}`);
            }
            
            updateMetrics();
        }

        async function testMetricsAPI() {
            const startTime = performance.now();
            requestCount++;
            
            try {
                log('Testing metrics API...');
                const response = await fetch('http://localhost:5000/api/monitoring/metrics');
                
                const endTime = performance.now();
                totalResponseTime += (endTime - startTime);
                
                if (response.ok) {
                    successCount++;
                    const result = await response.json();
                    log(`✅ Metrics retrieved: ${Array.isArray(result) ? result.length : 'Data available'} metrics`);
                } else {
                    log(`❌ Metrics API failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`🔌 Metrics API error: ${error.message}`);
            }
            
            updateMetrics();
        }

        async function testAlertsAPI() {
            const startTime = performance.now();
            requestCount++;
            
            try {
                log('Testing alerts API...');
                const response = await fetch('http://localhost:5000/api/monitoring/alerts');
                
                const endTime = performance.now();
                totalResponseTime += (endTime - startTime);
                
                if (response.ok) {
                    successCount++;
                    const result = await response.json();
                    log(`✅ Alerts retrieved: ${Array.isArray(result) ? result.length : 'Data available'} alerts`);
                } else {
                    log(`❌ Alerts API failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`🔌 Alerts API error: ${error.message}`);
            }
            
            updateMetrics();
        }

        async function testConcurrentRequests() {
            log('Testing concurrent requests (10 parallel health checks)...');
            const promises = [];
            
            for (let i = 0; i < 10; i++) {
                promises.push(fetch('http://localhost:5000/health'));
            }
            
            try {
                const startTime = performance.now();
                const responses = await Promise.all(promises);
                const endTime = performance.now();
                
                const successfulResponses = responses.filter(r => r.ok).length;
                requestCount += 10;
                successCount += successfulResponses;
                totalResponseTime += (endTime - startTime);
                
                log(`✅ Concurrent test completed: ${successfulResponses}/10 successful (${Math.round(endTime - startTime)}ms total)`);
            } catch (error) {
                log(`❌ Concurrent test failed: ${error.message}`);
            }
            
            updateMetrics();
        }

        function clearLog() {
            document.getElementById('activity-log').textContent = 'Log cleared.\n';
        }

        function exportLog() {
            const logContent = document.getElementById('activity-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tli-dashboard-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('📄 Log exported to file');
        }

        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                log('🔄 Auto refresh disabled');
            } else {
                autoRefreshInterval = setInterval(() => {
                    checkHealth('gateway', 'http://localhost:5000/health');
                    checkHealth('datastorage', 'http://localhost:5010/health');
                    checkHealth('monitoring', 'http://localhost:5011/health');
                }, 30000);
                log('🔄 Auto refresh enabled (30s interval)');
            }
        }

        // Initialize dashboard
        window.addEventListener('load', () => {
            log('Dashboard loaded successfully!');
            log('Click "Check Health" buttons to test service availability.');
            
            // Auto-check health on load
            setTimeout(() => {
                checkHealth('gateway', 'http://localhost:5000/health');
                checkHealth('datastorage', 'http://localhost:5010/health');
                checkHealth('monitoring', 'http://localhost:5011/health');
            }, 1000);
        });
    </script>
</body>
</html>
