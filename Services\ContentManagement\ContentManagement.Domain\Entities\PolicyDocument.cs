using ContentManagement.Domain.Enums;
using Shared.Domain.Common;
using ContentManagement.Domain.Events;
using ContentManagement.Domain.ValueObjects;

namespace ContentManagement.Domain.Entities;

/// <summary>
/// Policy document entity for legal documents, terms & conditions, privacy policies, etc.
/// </summary>
public class PolicyDocument : Content
{
    public PolicyType PolicyType { get; private set; }
    public string Version { get; private set; }
    public DateTime EffectiveDate { get; private set; }
    public DateTime? ExpiryDate { get; private set; }
    public bool RequiresAcceptance { get; private set; }
    public List<string> ApplicableRoles { get; private set; }
    public string? LegalReferences { get; private set; }
    public bool IsCurrentVersion { get; private set; }

    // Acceptance tracking
    private readonly List<PolicyAcceptance> _acceptances = new();
    public IReadOnlyCollection<PolicyAcceptance> Acceptances => _acceptances.AsReadOnly();

    private PolicyDocument()
    {
        Version = string.Empty;
        ApplicableRoles = new List<string>();
    }

    public PolicyDocument(
        PolicyType policyType,
        string title,
        string version,
        string contentBody,
        DateTime effectiveDate,
        Guid createdBy,
        string createdByName,
        DateTime? expiryDate = null,
        bool requiresAcceptance = false,
        List<string>? applicableRoles = null,
        string? legalReferences = null,
        string? description = null,
        ContentVisibility? visibility = null,
        List<string>? tags = null)
        : base(
            title: title,
            slug: GenerateSlug(policyType, version),
            type: ContentType.Policy,
            contentBody: contentBody,
            createdBy: createdBy,
            createdByName: createdByName,
            description: description ?? $"{policyType} Policy v{version}",
            visibility: visibility,
            tags: tags)
    {
        if (string.IsNullOrWhiteSpace(version))
            throw new ArgumentException("Version cannot be empty", nameof(version));
        if (effectiveDate == default)
            throw new ArgumentException("Effective date must be specified", nameof(effectiveDate));

        PolicyType = policyType;
        Version = version;
        EffectiveDate = effectiveDate;
        ExpiryDate = expiryDate;
        RequiresAcceptance = requiresAcceptance;
        ApplicableRoles = applicableRoles ?? new List<string>();
        LegalReferences = legalReferences;
        IsCurrentVersion = true; // New policies start as current version

        // Add policy-specific domain event
        AddDomainEvent(new PolicyDocumentCreatedEvent(this));
    }

    public void UpdatePolicy(
        string title,
        string contentBody,
        DateTime effectiveDate,
        Guid updatedBy,
        string updatedByName,
        DateTime? expiryDate = null,
        bool requiresAcceptance = false,
        List<string>? applicableRoles = null,
        string? legalReferences = null,
        string? description = null,
        string? changeDescription = null)
    {
        if (effectiveDate == default)
            throw new ArgumentException("Effective date must be specified", nameof(effectiveDate));

        var oldValues = new
        {
            Title,
            ContentBody,
            EffectiveDate,
            ExpiryDate,
            RequiresAcceptance,
            ApplicableRoles,
            LegalReferences
        };

        EffectiveDate = effectiveDate;
        ExpiryDate = expiryDate;
        RequiresAcceptance = requiresAcceptance;
        ApplicableRoles = applicableRoles ?? new List<string>();
        LegalReferences = legalReferences;

        // Update the base content
        UpdateContent(
            title: title,
            description: description ?? $"{PolicyType} Policy v{Version}",
            contentBody: contentBody,
            updatedBy: updatedBy,
            updatedByName: updatedByName,
            changeDescription: changeDescription);

        AddDomainEvent(new PolicyDocumentUpdatedEvent(this, oldValues, updatedBy, updatedByName));
    }

    public void CreateNewVersion(
        string newVersion,
        string contentBody,
        DateTime effectiveDate,
        Guid createdBy,
        string createdByName,
        DateTime? expiryDate = null,
        string? changeDescription = null)
    {
        if (string.IsNullOrWhiteSpace(newVersion))
            throw new ArgumentException("New version cannot be empty", nameof(newVersion));

        // Mark current version as not current
        IsCurrentVersion = false;

        // Create new version (this would typically be a new PolicyDocument entity)
        Version = newVersion;
        EffectiveDate = effectiveDate;
        ExpiryDate = expiryDate;
        IsCurrentVersion = true;

        // Update content body and create new version
        UpdateContent(
            title: Title,
            description: $"{PolicyType} Policy v{Version}",
            contentBody: contentBody,
            updatedBy: createdBy,
            updatedByName: createdByName,
            changeDescription: changeDescription ?? $"New version {newVersion}");

        AddDomainEvent(new PolicyVersionCreatedEvent(this, newVersion, createdBy, createdByName));
    }

    public void MarkAsCurrentVersion(Guid updatedBy, string updatedByName)
    {
        if (IsCurrentVersion)
            return;

        IsCurrentVersion = true;
        AddDomainEvent(new PolicyVersionActivatedEvent(this, updatedBy, updatedByName));
    }

    public void MarkAsObsolete(Guid updatedBy, string updatedByName)
    {
        IsCurrentVersion = false;
        AddDomainEvent(new PolicyVersionDeactivatedEvent(this, updatedBy, updatedByName));
    }

    public void RecordAcceptance(Guid userId, string userName, string userRole, string? ipAddress = null)
    {
        if (!RequiresAcceptance)
            throw new InvalidOperationException("This policy does not require acceptance");

        // Check if user already accepted this version
        var existingAcceptance = _acceptances.FirstOrDefault(a => a.UserId == userId);
        if (existingAcceptance != null)
            return; // Already accepted

        var acceptance = new PolicyAcceptance(
            policyId: Id,
            userId: userId,
            userName: userName,
            userRole: userRole,
            version: Version,
            ipAddress: ipAddress);

        _acceptances.Add(acceptance);

        AddDomainEvent(new PolicyAcceptedEvent(this, acceptance));
    }

    public bool IsAcceptedByUser(Guid userId)
    {
        return _acceptances.Any(a => a.UserId == userId);
    }

    public bool IsApplicableToRole(string role)
    {
        return !ApplicableRoles.Any() || ApplicableRoles.Contains(role);
    }

    public bool IsApplicableToRoles(List<string> roles)
    {
        return !ApplicableRoles.Any() || ApplicableRoles.Any(ar => roles.Contains(ar));
    }

    public bool IsEffective(DateTime? checkDate = null)
    {
        var date = checkDate ?? DateTime.UtcNow;
        return date >= EffectiveDate && (ExpiryDate == null || date <= ExpiryDate);
    }

    public bool IsExpired(DateTime? checkDate = null)
    {
        var date = checkDate ?? DateTime.UtcNow;
        return ExpiryDate.HasValue && date > ExpiryDate.Value;
    }

    private static string GenerateSlug(PolicyType policyType, string version)
    {
        return $"{policyType.ToString().ToLowerInvariant()}-v{version.Replace(".", "-")}";
    }
}


