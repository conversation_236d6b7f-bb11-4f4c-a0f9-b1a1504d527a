using AnalyticsBIService.Domain.Enums;

namespace AnalyticsBIService.Infrastructure.Services;

/// <summary>
/// Interface for enhanced KPI monitoring service
/// </summary>
public interface IKPIMonitoringService
{
    /// <summary>
    /// Create a new KPI monitor
    /// </summary>
    Task<KPIMonitorDto> CreateKPIMonitorAsync(CreateKPIMonitorRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get KPI monitor by ID
    /// </summary>
    Task<KPIMonitorDto?> GetKPIMonitorAsync(Guid monitorId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all KPI monitors for a user
    /// </summary>
    Task<List<KPIMonitorDto>> GetUserKPIMonitorsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update KPI monitor configuration
    /// </summary>
    Task<bool> UpdateKPIMonitorAsync(Guid monitorId, UpdateKPIMonitorRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete KPI monitor
    /// </summary>
    Task<bool> DeleteKPIMonitorAsync(Guid monitorId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current KPI values
    /// </summary>
    Task<List<KPIValueDto>> GetCurrentKPIValuesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get KPI historical data
    /// </summary>
    Task<KPIHistoryDto> GetKPIHistoryAsync(Guid monitorId, DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create threshold rule
    /// </summary>
    Task<ThresholdRuleDto> CreateThresholdRuleAsync(CreateThresholdRuleRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get threshold rules for a KPI monitor
    /// </summary>
    Task<List<ThresholdRuleDto>> GetThresholdRulesAsync(Guid monitorId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create alert configuration
    /// </summary>
    Task<AlertConfigurationDto> CreateAlertConfigurationAsync(CreateAlertConfigurationRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert configurations for a user
    /// </summary>
    Task<List<AlertConfigurationDto>> GetAlertConfigurationsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active alerts
    /// </summary>
    Task<List<AlertDto>> GetActiveAlertsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get alert history
    /// </summary>
    Task<List<AlertHistoryDto>> GetAlertHistoryAsync(Guid userId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Acknowledge alert
    /// </summary>
    Task<bool> AcknowledgeAlertAsync(Guid alertId, Guid userId, string? notes = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process KPI monitoring (background service method)
    /// </summary>
    Task ProcessKPIMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Create automated response
    /// </summary>
    Task<AutomatedResponseDto> CreateAutomatedResponseAsync(CreateAutomatedResponseRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get automated responses for a threshold rule
    /// </summary>
    Task<List<AutomatedResponseDto>> GetAutomatedResponsesAsync(Guid thresholdRuleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get KPI monitoring dashboard
    /// </summary>
    Task<KPIDashboardDto> GetKPIDashboardAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get KPI monitoring statistics
    /// </summary>
    Task<KPIMonitoringStatsDto> GetMonitoringStatisticsAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// KPI monitor data transfer object
/// </summary>
public class KPIMonitorDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string KPIType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public string Query { get; set; } = string.Empty;
    public string AggregationMethod { get; set; } = string.Empty;
    public int MonitoringIntervalMinutes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public UserType UserType { get; set; }
    public DateTime? LastCalculated { get; set; }
    public decimal? LastValue { get; set; }
    public string? LastStatus { get; set; }
    public List<ThresholdRuleDto> ThresholdRules { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// KPI value data transfer object
/// </summary>
public class KPIValueDto
{
    public Guid MonitorId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public decimal? PreviousValue { get; set; }
    public decimal? ChangePercentage { get; set; }
    public string Trend { get; set; } = string.Empty;
    public List<ThresholdStatusDto> ThresholdStatuses { get; set; } = new();
}

/// <summary>
/// KPI history data transfer object
/// </summary>
public class KPIHistoryDto
{
    public Guid MonitorId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<KPIDataPointDto> DataPoints { get; set; } = new();
    public KPIStatisticsDto Statistics { get; set; } = new();
}

/// <summary>
/// KPI data point data transfer object
/// </summary>
public class KPIDataPointDto
{
    public DateTime Timestamp { get; set; }
    public decimal Value { get; set; }
    public string Status { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// KPI statistics data transfer object
/// </summary>
public class KPIStatisticsDto
{
    public decimal MinValue { get; set; }
    public decimal MaxValue { get; set; }
    public decimal AverageValue { get; set; }
    public decimal MedianValue { get; set; }
    public decimal StandardDeviation { get; set; }
    public int TotalDataPoints { get; set; }
    public decimal TrendSlope { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
}

/// <summary>
/// Threshold rule data transfer object
/// </summary>
public class ThresholdRuleDto
{
    public Guid Id { get; set; }
    public Guid MonitorId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public string Severity { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<AutomatedResponseDto> AutomatedResponses { get; set; } = new();
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Threshold status data transfer object
/// </summary>
public class ThresholdStatusDto
{
    public Guid RuleId { get; set; }
    public string RuleName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public decimal CurrentValue { get; set; }
    public string Severity { get; set; } = string.Empty;
    public DateTime LastTriggered { get; set; }
}

/// <summary>
/// Alert configuration data transfer object
/// </summary>
public class AlertConfigurationDto
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> AlertChannels { get; set; } = new();
    public List<string> Recipients { get; set; } = new();
    public string MessageTemplate { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Dictionary<string, object> ChannelSettings { get; set; } = new();
}

/// <summary>
/// Alert data transfer object
/// </summary>
public class AlertDto
{
    public Guid Id { get; set; }
    public Guid MonitorId { get; set; }
    public Guid ThresholdRuleId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal ThresholdValue { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime TriggeredAt { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public Guid? AcknowledgedBy { get; set; }
    public string? AcknowledgmentNotes { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Alert history data transfer object
/// </summary>
public class AlertHistoryDto
{
    public Guid AlertId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime TriggeredAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Automated response data transfer object
/// </summary>
public class AutomatedResponseDto
{
    public Guid Id { get; set; }
    public Guid ThresholdRuleId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ResponseType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool IsEnabled { get; set; }
    public int ExecutionOrder { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// KPI dashboard data transfer object
/// </summary>
public class KPIDashboardDto
{
    public Guid UserId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public List<KPIValueDto> CurrentKPIs { get; set; } = new();
    public List<AlertDto> ActiveAlerts { get; set; } = new();
    public List<KPITrendDto> TrendingKPIs { get; set; } = new();
    public KPIOverviewStatsDto OverviewStats { get; set; } = new();
    public List<KPIPerformanceDto> PerformanceMetrics { get; set; } = new();
}

/// <summary>
/// KPI trend data transfer object
/// </summary>
public class KPITrendDto
{
    public Guid MonitorId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string TrendDirection { get; set; } = string.Empty;
    public string TrendStrength { get; set; } = string.Empty;
    public List<KPIDataPointDto> RecentDataPoints { get; set; } = new();
}

/// <summary>
/// KPI overview statistics data transfer object
/// </summary>
public class KPIOverviewStatsDto
{
    public int TotalKPIs { get; set; }
    public int HealthyKPIs { get; set; }
    public int WarningKPIs { get; set; }
    public int CriticalKPIs { get; set; }
    public int ActiveAlerts { get; set; }
    public int AcknowledgedAlerts { get; set; }
    public decimal OverallHealthScore { get; set; }
}

/// <summary>
/// KPI performance data transfer object
/// </summary>
public class KPIPerformanceDto
{
    public Guid MonitorId { get; set; }
    public string KPIName { get; set; } = string.Empty;
    public decimal TargetValue { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal AchievementPercentage { get; set; }
    public string PerformanceStatus { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// KPI monitoring statistics data transfer object
/// </summary>
public class KPIMonitoringStatsDto
{
    public int TotalMonitors { get; set; }
    public int ActiveMonitors { get; set; }
    public int InactiveMonitors { get; set; }
    public int TotalThresholdRules { get; set; }
    public int TotalAlerts { get; set; }
    public int AlertsToday { get; set; }
    public int AlertsThisWeek { get; set; }
    public decimal AverageResponseTime { get; set; }
    public List<KPITypeStatsDto> KPITypeStatistics { get; set; } = new();
    public List<AlertSeverityStatsDto> AlertSeverityStats { get; set; } = new();
}

/// <summary>
/// KPI type statistics data transfer object
/// </summary>
public class KPITypeStatsDto
{
    public string KPIType { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal AverageValue { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
}

/// <summary>
/// Alert severity statistics data transfer object
/// </summary>
public class AlertSeverityStatsDto
{
    public string Severity { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Percentage { get; set; }
    public TimeSpan AverageResolutionTime { get; set; }
}

// Request DTOs
/// <summary>
/// Create KPI monitor request
/// </summary>
public class CreateKPIMonitorRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string KPIType { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
    public string Query { get; set; } = string.Empty;
    public string AggregationMethod { get; set; } = string.Empty;
    public int MonitoringIntervalMinutes { get; set; } = 15;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Guid UserId { get; set; }
    public UserType UserType { get; set; }
}

/// <summary>
/// Update KPI monitor request
/// </summary>
public class UpdateKPIMonitorRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Query { get; set; }
    public string? AggregationMethod { get; set; }
    public int? MonitoringIntervalMinutes { get; set; }
    public bool? IsActive { get; set; }
    public Dictionary<string, object>? Configuration { get; set; }
}

/// <summary>
/// Create threshold rule request
/// </summary>
public class CreateThresholdRuleRequest
{
    public Guid MonitorId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public decimal ThresholdValue { get; set; }
    public string Severity { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Guid UserId { get; set; }
}

/// <summary>
/// Create alert configuration request
/// </summary>
public class CreateAlertConfigurationRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> AlertChannels { get; set; } = new();
    public List<string> Recipients { get; set; } = new();
    public string MessageTemplate { get; set; } = string.Empty;
    public Dictionary<string, object> ChannelSettings { get; set; } = new();
    public Guid UserId { get; set; }
}

/// <summary>
/// Create automated response request
/// </summary>
public class CreateAutomatedResponseRequest
{
    public Guid ThresholdRuleId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ResponseType { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int ExecutionOrder { get; set; } = 1;
    public Guid UserId { get; set; }
}
