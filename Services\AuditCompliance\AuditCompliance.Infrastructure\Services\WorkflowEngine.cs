using Microsoft.Extensions.Logging;
using AuditCompliance.Application.Interfaces;
using AuditCompliance.Application.DTOs.Workflow;
using System.Text.RegularExpressions;
using System.Text.Json;

namespace AuditCompliance.Infrastructure.Services;

/// <summary>
/// Workflow execution engine for processing workflow steps and actions
/// </summary>
public class WorkflowEngine : IWorkflowEngine
{
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly Dictionary<string, Func<WorkflowStepDto, Dictionary<string, object>, Task<WorkflowExecutionResult>>> _stepExecutors;
    private readonly Dictionary<string, Func<WorkflowActionDto, Dictionary<string, object>, Task<ActionExecutionResult>>> _actionExecutors;

    public WorkflowEngine(ILogger<WorkflowEngine> logger)
    {
        _logger = logger;
        _stepExecutors = InitializeStepExecutors();
        _actionExecutors = InitializeActionExecutors();
    }

    public async Task<WorkflowExecutionResult> ExecuteStepAsync(
        WorkflowInstanceDto instance,
        WorkflowStepDto step,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing step {StepName} for workflow {InstanceId}", step.Name, instance.Id);

            var result = new WorkflowExecutionResult { IsSuccess = true };

            // Check step condition if present
            if (!string.IsNullOrEmpty(step.Condition))
            {
                var conditionMet = await EvaluateConditionAsync(step.Condition, context, cancellationToken);
                if (!conditionMet)
                {
                    _logger.LogInformation("Step condition not met for {StepName}, skipping", step.Name);
                    result.NextSteps = await GetNextStepsAsync(instance, step, context, cancellationToken);
                    return result;
                }
            }

            // Execute step based on type
            if (_stepExecutors.ContainsKey(step.Type.ToLower()))
            {
                result = await _stepExecutors[step.Type.ToLower()](step, context);
            }
            else
            {
                result = await ExecuteDefaultStepAsync(step, context);
            }

            // Execute step actions
            foreach (var action in step.Actions.OrderBy(a => a.Order))
            {
                var actionResult = await ExecuteActionAsync(action, context, cancellationToken);
                if (!actionResult.IsSuccess)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = actionResult.ErrorMessage;
                    break;
                }

                // Merge action output into context
                foreach (var kvp in actionResult.OutputData)
                {
                    context[kvp.Key] = kvp.Value;
                }
            }

            // Get next steps if execution was successful
            if (result.IsSuccess && !result.IsCompleted)
            {
                result.NextSteps = await GetNextStepsAsync(instance, step, context, cancellationToken);
                
                // Check if this is an end step
                if (step.IsEndStep)
                {
                    result.IsCompleted = true;
                    result.CompletionReason = "Reached end step";
                }
            }

            _logger.LogInformation("Completed step {StepName} execution with result: {IsSuccess}", step.Name, result.IsSuccess);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing step {StepName}", step.Name);
            return new WorkflowExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> EvaluateConditionAsync(
        string condition,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Evaluating condition: {Condition}", condition);

            // Replace variables in condition with actual values
            var evaluatedCondition = ReplaceVariables(condition, context);

            // Simple condition evaluation
            if (evaluatedCondition.Contains("=="))
            {
                var parts = evaluatedCondition.Split("==", 2);
                var left = parts[0].Trim().Trim('"', '\'');
                var right = parts[1].Trim().Trim('"', '\'');
                return left == right;
            }

            if (evaluatedCondition.Contains("!="))
            {
                var parts = evaluatedCondition.Split("!=", 2);
                var left = parts[0].Trim().Trim('"', '\'');
                var right = parts[1].Trim().Trim('"', '\'');
                return left != right;
            }

            if (evaluatedCondition.Contains(">"))
            {
                var parts = evaluatedCondition.Split(">", 2);
                if (float.TryParse(parts[0].Trim(), out var left) && float.TryParse(parts[1].Trim(), out var right))
                {
                    return left > right;
                }
            }

            if (evaluatedCondition.Contains("<"))
            {
                var parts = evaluatedCondition.Split("<", 2);
                if (float.TryParse(parts[0].Trim(), out var left) && float.TryParse(parts[1].Trim(), out var right))
                {
                    return left < right;
                }
            }

            // Boolean evaluation
            if (bool.TryParse(evaluatedCondition, out var boolResult))
            {
                return boolResult;
            }

            // Check if variable exists and is truthy
            if (context.ContainsKey(evaluatedCondition))
            {
                var value = context[evaluatedCondition];
                return value != null && !value.Equals(false) && !value.Equals(0) && !value.Equals("");
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating condition: {Condition}", condition);
            return false;
        }
    }

    public async Task<List<WorkflowStepDto>> GetNextStepsAsync(
        WorkflowInstanceDto instance,
        WorkflowStepDto currentStep,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting next steps for {StepName}", currentStep.Name);

            var nextSteps = new List<WorkflowStepDto>();

            // This would typically involve evaluating transitions from the workflow definition
            // For now, return empty list (implementation would be more complex)

            return nextSteps;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting next steps for {StepName}", currentStep.Name);
            return new List<WorkflowStepDto>();
        }
    }

    public async Task<ActionExecutionResult> ExecuteActionAsync(
        WorkflowActionDto action,
        Dictionary<string, object> context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Executing action {ActionName} of type {ActionType}", action.Name, action.Type);

            if (_actionExecutors.ContainsKey(action.Type.ToLower()))
            {
                return await _actionExecutors[action.Type.ToLower()](action, context);
            }

            return await ExecuteDefaultActionAsync(action, context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing action {ActionName}", action.Name);
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    // Private step executors
    private async Task<WorkflowExecutionResult> ExecuteTaskStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing task step: {StepName}", step.Name);

        // Task steps require user interaction, so we create a task and wait
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            OutputData = new Dictionary<string, object> { ["RequiresUserTask"] = true }
        };
    }

    private async Task<WorkflowExecutionResult> ExecuteDecisionStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing decision step: {StepName}", step.Name);

        // Decision steps evaluate conditions and route accordingly
        var result = new WorkflowExecutionResult { IsSuccess = true };

        if (!string.IsNullOrEmpty(step.Condition))
        {
            var conditionMet = await EvaluateConditionAsync(step.Condition, context, CancellationToken.None);
            result.OutputData["DecisionResult"] = conditionMet;
        }

        return result;
    }

    private async Task<WorkflowExecutionResult> ExecuteActionStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing action step: {StepName}", step.Name);

        // Action steps execute automatically
        var result = new WorkflowExecutionResult { IsSuccess = true };

        // Execute all actions in the step
        foreach (var action in step.Actions.OrderBy(a => a.Order))
        {
            var actionResult = await ExecuteActionAsync(action, context, CancellationToken.None);
            if (!actionResult.IsSuccess)
            {
                result.IsSuccess = false;
                result.ErrorMessage = actionResult.ErrorMessage;
                break;
            }

            // Merge action output
            foreach (var kvp in actionResult.OutputData)
            {
                result.OutputData[kvp.Key] = kvp.Value;
            }
        }

        return result;
    }

    private async Task<WorkflowExecutionResult> ExecuteTimerStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing timer step: {StepName}", step.Name);

        // Timer steps introduce delays or schedule future execution
        var result = new WorkflowExecutionResult { IsSuccess = true };

        if (step.Timer != null)
        {
            switch (step.Timer.Type.ToLower())
            {
                case "delay":
                    // For delay timers, we would schedule continuation
                    result.OutputData["DelayMinutes"] = step.Timer.DurationMinutes;
                    break;
                case "deadline":
                    // For deadline timers, we would set a deadline
                    result.OutputData["Deadline"] = DateTime.UtcNow.AddMinutes(step.Timer.DurationMinutes);
                    break;
            }
        }

        return result;
    }

    private async Task<WorkflowExecutionResult> ExecuteParallelStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing parallel step: {StepName}", step.Name);

        // Parallel steps execute multiple branches simultaneously
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            OutputData = new Dictionary<string, object> { ["ExecutionMode"] = "Parallel" }
        };
    }

    private async Task<WorkflowExecutionResult> ExecuteSequentialStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing sequential step: {StepName}", step.Name);

        // Sequential steps execute sub-steps in order
        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            OutputData = new Dictionary<string, object> { ["ExecutionMode"] = "Sequential" }
        };
    }

    private async Task<WorkflowExecutionResult> ExecuteDefaultStepAsync(WorkflowStepDto step, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing default step: {StepName}", step.Name);

        return new WorkflowExecutionResult
        {
            IsSuccess = true,
            OutputData = new Dictionary<string, object> { ["StepType"] = step.Type }
        };
    }

    // Private action executors
    private async Task<ActionExecutionResult> ExecuteEmailActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing email action: {ActionName}", action.Name);

        try
        {
            // Extract email parameters
            var to = GetParameterValue(action.Parameters, "to", context)?.ToString() ?? "";
            var subject = GetParameterValue(action.Parameters, "subject", context)?.ToString() ?? "";
            var body = GetParameterValue(action.Parameters, "body", context)?.ToString() ?? "";

            // This would integrate with email service
            _logger.LogInformation("Sending email to {To} with subject {Subject}", to, subject);

            return new ActionExecutionResult
            {
                IsSuccess = true,
                OutputData = new Dictionary<string, object>
                {
                    ["EmailSent"] = true,
                    ["Recipient"] = to,
                    ["SentAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"Email action failed: {ex.Message}"
            };
        }
    }

    private async Task<ActionExecutionResult> ExecuteApiActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing API action: {ActionName}", action.Name);

        try
        {
            var url = GetParameterValue(action.Parameters, "url", context)?.ToString() ?? "";
            var method = GetParameterValue(action.Parameters, "method", context)?.ToString() ?? "GET";
            var headers = GetParameterValue(action.Parameters, "headers", context) as Dictionary<string, object> ?? new();
            var body = GetParameterValue(action.Parameters, "body", context)?.ToString() ?? "";

            // This would make actual HTTP request
            _logger.LogInformation("Making {Method} request to {Url}", method, url);

            return new ActionExecutionResult
            {
                IsSuccess = true,
                OutputData = new Dictionary<string, object>
                {
                    ["ApiCallMade"] = true,
                    ["Url"] = url,
                    ["Method"] = method,
                    ["CalledAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"API action failed: {ex.Message}"
            };
        }
    }

    private async Task<ActionExecutionResult> ExecuteDatabaseActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing database action: {ActionName}", action.Name);

        try
        {
            var query = GetParameterValue(action.Parameters, "query", context)?.ToString() ?? "";
            var operation = GetParameterValue(action.Parameters, "operation", context)?.ToString() ?? "SELECT";

            // This would execute database operation
            _logger.LogInformation("Executing {Operation} database operation", operation);

            return new ActionExecutionResult
            {
                IsSuccess = true,
                OutputData = new Dictionary<string, object>
                {
                    ["DatabaseOperationExecuted"] = true,
                    ["Operation"] = operation,
                    ["ExecutedAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"Database action failed: {ex.Message}"
            };
        }
    }

    private async Task<ActionExecutionResult> ExecuteScriptActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing script action: {ActionName}", action.Name);

        try
        {
            var script = GetParameterValue(action.Parameters, "script", context)?.ToString() ?? "";
            var language = GetParameterValue(action.Parameters, "language", context)?.ToString() ?? "javascript";

            // This would execute script in sandboxed environment
            _logger.LogInformation("Executing {Language} script", language);

            return new ActionExecutionResult
            {
                IsSuccess = true,
                OutputData = new Dictionary<string, object>
                {
                    ["ScriptExecuted"] = true,
                    ["Language"] = language,
                    ["ExecutedAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"Script action failed: {ex.Message}"
            };
        }
    }

    private async Task<ActionExecutionResult> ExecuteApprovalActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing approval action: {ActionName}", action.Name);

        try
        {
            var approvers = GetParameterValue(action.Parameters, "approvers", context) as List<string> ?? new();
            var approvalType = GetParameterValue(action.Parameters, "approvalType", context)?.ToString() ?? "any";

            // This would create approval tasks
            _logger.LogInformation("Creating approval request for {ApproverCount} approvers", approvers.Count);

            return new ActionExecutionResult
            {
                IsSuccess = true,
                RequiresUserInput = true,
                UserInputPrompt = "Approval required",
                OutputData = new Dictionary<string, object>
                {
                    ["ApprovalRequested"] = true,
                    ["Approvers"] = approvers,
                    ["ApprovalType"] = approvalType,
                    ["RequestedAt"] = DateTime.UtcNow
                }
            };
        }
        catch (Exception ex)
        {
            return new ActionExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = $"Approval action failed: {ex.Message}"
            };
        }
    }

    private async Task<ActionExecutionResult> ExecuteDefaultActionAsync(WorkflowActionDto action, Dictionary<string, object> context)
    {
        _logger.LogDebug("Executing default action: {ActionName}", action.Name);

        return new ActionExecutionResult
        {
            IsSuccess = true,
            OutputData = new Dictionary<string, object>
            {
                ["ActionType"] = action.Type,
                ["ExecutedAt"] = DateTime.UtcNow
            }
        };
    }

    // Helper methods
    private string ReplaceVariables(string text, Dictionary<string, object> context)
    {
        var variablePattern = @"\{([^}]+)\}";
        
        return Regex.Replace(text, variablePattern, match =>
        {
            var variable = match.Groups[1].Value;
            return GetVariableValue(variable, context)?.ToString() ?? match.Value;
        });
    }

    private object? GetVariableValue(string variable, Dictionary<string, object> context)
    {
        // Support nested properties (e.g., "user.name")
        var parts = variable.Split('.');
        object? current = context;

        foreach (var part in parts)
        {
            if (current is Dictionary<string, object> dict && dict.ContainsKey(part))
            {
                current = dict[part];
            }
            else
            {
                return null;
            }
        }

        return current;
    }

    private object? GetParameterValue(Dictionary<string, object> parameters, string key, Dictionary<string, object> context)
    {
        if (!parameters.ContainsKey(key))
            return null;

        var value = parameters[key];
        if (value is string stringValue)
        {
            // Replace variables in string values
            return ReplaceVariables(stringValue, context);
        }

        return value;
    }

    private Dictionary<string, Func<WorkflowStepDto, Dictionary<string, object>, Task<WorkflowExecutionResult>>> InitializeStepExecutors()
    {
        return new Dictionary<string, Func<WorkflowStepDto, Dictionary<string, object>, Task<WorkflowExecutionResult>>>
        {
            ["task"] = ExecuteTaskStepAsync,
            ["decision"] = ExecuteDecisionStepAsync,
            ["action"] = ExecuteActionStepAsync,
            ["timer"] = ExecuteTimerStepAsync,
            ["parallel"] = ExecuteParallelStepAsync,
            ["sequential"] = ExecuteSequentialStepAsync
        };
    }

    private Dictionary<string, Func<WorkflowActionDto, Dictionary<string, object>, Task<ActionExecutionResult>>> InitializeActionExecutors()
    {
        return new Dictionary<string, Func<WorkflowActionDto, Dictionary<string, object>, Task<ActionExecutionResult>>>
        {
            ["email"] = ExecuteEmailActionAsync,
            ["api"] = ExecuteApiActionAsync,
            ["database"] = ExecuteDatabaseActionAsync,
            ["script"] = ExecuteScriptActionAsync,
            ["approval"] = ExecuteApprovalActionAsync
        };
    }
}
