using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class RetentionPolicy
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public TimeSpan RetentionPeriod { get; private set; }
    public RetentionTrigger Trigger { get; private set; }
    public List<RetentionRule> Rules { get; private set; }
    public RetentionAction Action { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime LastModified { get; private set; }
    public List<ComplianceStandard> ApplicableStandards { get; private set; }

    public RetentionPolicy(
        Guid id,
        string name,
        string description,
        TimeSpan retentionPeriod,
        RetentionTrigger trigger,
        List<RetentionRule> rules,
        RetentionAction action,
        bool isEnabled,
        Guid createdBy,
        DateTime createdAt,
        List<ComplianceStandard> applicableStandards,
        DateTime lastModified = default)
    {
        Id = id;
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        RetentionPeriod = retentionPeriod;
        Trigger = trigger;
        Rules = rules ?? new List<RetentionRule>();
        Action = action ?? throw new ArgumentNullException(nameof(action));
        IsEnabled = isEnabled;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        LastModified = lastModified == default ? createdAt : lastModified;
        ApplicableStandards = applicableStandards ?? new List<ComplianceStandard>();
    }
}

public class RetentionPolicyRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public TimeSpan RetentionPeriod { get; private set; }
    public RetentionTrigger Trigger { get; private set; }
    public List<RetentionRule> Rules { get; private set; }
    public RetentionAction Action { get; private set; }
    public bool IsEnabled { get; private set; }
    public Guid CreatedBy { get; private set; }
    public List<ComplianceStandard> ApplicableStandards { get; private set; }

    public RetentionPolicyRequest(
        string name,
        string description,
        TimeSpan retentionPeriod,
        RetentionTrigger trigger,
        List<RetentionRule> rules,
        RetentionAction action,
        Guid createdBy,
        bool isEnabled = true,
        List<ComplianceStandard>? applicableStandards = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        RetentionPeriod = retentionPeriod;
        Trigger = trigger;
        Rules = rules ?? new List<RetentionRule>();
        Action = action ?? throw new ArgumentNullException(nameof(action));
        CreatedBy = createdBy;
        IsEnabled = isEnabled;
        ApplicableStandards = applicableStandards ?? new List<ComplianceStandard>();
    }
}

public class RetentionRule
{
    public string Name { get; private set; }
    public RetentionRuleType Type { get; private set; }
    public List<RetentionCondition> Conditions { get; private set; }
    public bool IsEnabled { get; private set; }
    public int Priority { get; private set; }

    public RetentionRule(
        string name,
        RetentionRuleType type,
        List<RetentionCondition> conditions,
        bool isEnabled = true,
        int priority = 0)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type;
        Conditions = conditions ?? new List<RetentionCondition>();
        IsEnabled = isEnabled;
        Priority = priority;
    }
}

public class RetentionCondition
{
    public RetentionConditionType Type { get; private set; }
    public string Field { get; private set; }
    public RetentionConditionOperator Operator { get; private set; }
    public object Value { get; private set; }
    public List<object>? Values { get; private set; }

    public RetentionCondition(
        RetentionConditionType type,
        string field,
        RetentionConditionOperator @operator,
        object value,
        List<object>? values = null)
    {
        Type = type;
        Field = field ?? throw new ArgumentNullException(nameof(field));
        Operator = @operator;
        Value = value ?? throw new ArgumentNullException(nameof(value));
        Values = values;
    }
}

public class RetentionAction
{
    public RetentionActionType Type { get; private set; }
    public bool RequireApproval { get; private set; }
    public List<Guid> Approvers { get; private set; }
    public bool NotifyBeforeAction { get; private set; }
    public TimeSpan NotificationPeriod { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public RetentionAction(
        RetentionActionType type,
        bool requireApproval = false,
        List<Guid>? approvers = null,
        bool notifyBeforeAction = true,
        TimeSpan notificationPeriod = default,
        Dictionary<string, object>? parameters = null)
    {
        Type = type;
        RequireApproval = requireApproval;
        Approvers = approvers ?? new List<Guid>();
        NotifyBeforeAction = notifyBeforeAction;
        NotificationPeriod = notificationPeriod == default ? TimeSpan.FromDays(30) : notificationPeriod;
        Parameters = parameters ?? new Dictionary<string, object>();
    }
}

public class RetentionAssignmentResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public List<Guid> SuccessfulAssignments { get; private set; }
    public List<Guid> FailedAssignments { get; private set; }
    public Guid PolicyId { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public Guid AssignedBy { get; private set; }

    private RetentionAssignmentResult()
    {
        SuccessfulAssignments = new List<Guid>();
        FailedAssignments = new List<Guid>();
    }

    public static RetentionAssignmentResult Success(
        Guid policyId,
        List<Guid> successfulAssignments,
        Guid assignedBy,
        List<Guid>? failedAssignments = null)
    {
        return new RetentionAssignmentResult
        {
            IsSuccessful = true,
            PolicyId = policyId,
            SuccessfulAssignments = successfulAssignments ?? new List<Guid>(),
            FailedAssignments = failedAssignments ?? new List<Guid>(),
            AssignedAt = DateTime.UtcNow,
            AssignedBy = assignedBy
        };
    }

    public static RetentionAssignmentResult Failure(Guid policyId, string errorMessage, List<Guid>? failedAssignments = null)
    {
        return new RetentionAssignmentResult
        {
            IsSuccessful = false,
            PolicyId = policyId,
            ErrorMessage = errorMessage,
            FailedAssignments = failedAssignments ?? new List<Guid>(),
            AssignedAt = DateTime.UtcNow
        };
    }
}

public class RetentionInfo
{
    public Guid DocumentId { get; private set; }
    public Guid? PolicyId { get; private set; }
    public string? PolicyName { get; private set; }
    public DateTime? RetentionStartDate { get; private set; }
    public DateTime? RetentionEndDate { get; private set; }
    public RetentionStatus Status { get; private set; }
    public List<LegalHold> ActiveLegalHolds { get; private set; }
    public bool IsUnderLegalHold { get; private set; }
    public TimeSpan? RemainingRetentionPeriod { get; private set; }
    public List<RetentionEvent> Events { get; private set; }

    public RetentionInfo(
        Guid documentId,
        Guid? policyId,
        string? policyName,
        DateTime? retentionStartDate,
        DateTime? retentionEndDate,
        RetentionStatus status,
        List<LegalHold> activeLegalHolds,
        List<RetentionEvent> events)
    {
        DocumentId = documentId;
        PolicyId = policyId;
        PolicyName = policyName;
        RetentionStartDate = retentionStartDate;
        RetentionEndDate = retentionEndDate;
        Status = status;
        ActiveLegalHolds = activeLegalHolds ?? new List<LegalHold>();
        IsUnderLegalHold = ActiveLegalHolds.Any(h => h.Status == LegalHoldStatus.Active);
        RemainingRetentionPeriod = retentionEndDate.HasValue ? retentionEndDate.Value - DateTime.UtcNow : null;
        Events = events ?? new List<RetentionEvent>();
    }
}

public class RetentionScheduleItem
{
    public Guid DocumentId { get; private set; }
    public string FileName { get; private set; }
    public Guid PolicyId { get; private set; }
    public string PolicyName { get; private set; }
    public DateTime ScheduledDate { get; private set; }
    public RetentionActionType Action { get; private set; }
    public RetentionScheduleStatus Status { get; private set; }
    public bool RequiresApproval { get; private set; }
    public List<Guid> PendingApprovers { get; private set; }

    public RetentionScheduleItem(
        Guid documentId,
        string fileName,
        Guid policyId,
        string policyName,
        DateTime scheduledDate,
        RetentionActionType action,
        RetentionScheduleStatus status,
        bool requiresApproval,
        List<Guid>? pendingApprovers = null)
    {
        DocumentId = documentId;
        FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
        PolicyId = policyId;
        PolicyName = policyName ?? throw new ArgumentNullException(nameof(policyName));
        ScheduledDate = scheduledDate;
        Action = action;
        Status = status;
        RequiresApproval = requiresApproval;
        PendingApprovers = pendingApprovers ?? new List<Guid>();
    }
}

public class RetentionEvent
{
    public Guid EventId { get; private set; }
    public Guid DocumentId { get; private set; }
    public RetentionEventType Type { get; private set; }
    public DateTime Timestamp { get; private set; }
    public Guid UserId { get; private set; }
    public string Description { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    public RetentionEvent(
        Guid eventId,
        Guid documentId,
        RetentionEventType type,
        DateTime timestamp,
        Guid userId,
        string description,
        Dictionary<string, object>? metadata = null)
    {
        EventId = eventId;
        DocumentId = documentId;
        Type = type;
        Timestamp = timestamp;
        UserId = userId;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Metadata = metadata ?? new Dictionary<string, object>();
    }
}
