using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using TripManagement.Application.Interfaces;
using TripManagement.Domain.Enums;

namespace TripManagement.Infrastructure.HealthChecks;

public class BusinessLogicHealthCheck : IHealthCheck
{
    private readonly ITripRepository _tripRepository;
    private readonly IDriverRepository _driverRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<BusinessLogicHealthCheck> _logger;

    public BusinessLogicHealthCheck(
        ITripRepository tripRepository,
        IDriverRepository driverRepository,
        IVehicleRepository vehicleRepository,
        ILogger<BusinessLogicHealthCheck> logger)
    {
        _tripRepository = tripRepository;
        _driverRepository = driverRepository;
        _vehicleRepository = vehicleRepository;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthData = new Dictionary<string, object>();
        var warnings = new List<string>();
        var errors = new List<string>();

        try
        {
            // Check active trips
            var activeTrips = await _tripRepository.GetActiveTripsAsync(cancellationToken);
            healthData["activeTrips"] = activeTrips.Count;

            // Check for trips in exception status
            var exceptionTrips = await _tripRepository.GetTripsByStatusAsync(TripStatus.Exception, cancellationToken);
            healthData["tripsInException"] = exceptionTrips.Count;

            if (exceptionTrips.Count > 10)
            {
                warnings.Add($"High number of trips in exception status: {exceptionTrips.Count}");
            }

            // Check for overdue trips
            var overdueTrips = activeTrips.Where(t => t.IsDelayed()).ToList();
            healthData["overdueTrips"] = overdueTrips.Count;

            if (overdueTrips.Count > 5)
            {
                warnings.Add($"High number of overdue trips: {overdueTrips.Count}");
            }

            // Check available drivers
            var availableDrivers = await _driverRepository.GetAvailableDriversAsync(cancellationToken);
            healthData["availableDrivers"] = availableDrivers.Count;

            if (availableDrivers.Count < 5)
            {
                warnings.Add($"Low number of available drivers: {availableDrivers.Count}");
            }

            // Check available vehicles
            var availableVehicles = await _vehicleRepository.GetAvailableVehiclesAsync(cancellationToken);
            healthData["availableVehicles"] = availableVehicles.Count;

            if (availableVehicles.Count < 5)
            {
                warnings.Add($"Low number of available vehicles: {availableVehicles.Count}");
            }

            // Check for drivers with expired licenses
            var allDrivers = availableDrivers.Concat(
                await _driverRepository.GetByCarrierIdAsync(Guid.Empty, cancellationToken) // This would need proper implementation
            ).ToList();

            var driversWithExpiredLicenses = allDrivers.Where(d => !d.IsLicenseValid).ToList();
            healthData["driversWithExpiredLicenses"] = driversWithExpiredLicenses.Count;

            if (driversWithExpiredLicenses.Count > 0)
            {
                warnings.Add($"Drivers with expired licenses: {driversWithExpiredLicenses.Count}");
            }

            // Check for vehicles needing maintenance
            var allVehicles = availableVehicles.Concat(
                await _vehicleRepository.GetByCarrierIdAsync(Guid.Empty, cancellationToken) // This would need proper implementation
            ).ToList();

            var vehiclesNeedingMaintenance = allVehicles.Where(v => 
                v.NextMaintenanceDate.HasValue && v.NextMaintenanceDate.Value <= DateTime.UtcNow.AddDays(7)
            ).ToList();

            healthData["vehiclesNeedingMaintenance"] = vehiclesNeedingMaintenance.Count;

            if (vehiclesNeedingMaintenance.Count > 0)
            {
                warnings.Add($"Vehicles needing maintenance soon: {vehiclesNeedingMaintenance.Count}");
            }

            if (warnings.Any())
            {
                healthData["warnings"] = warnings;
            }

            if (errors.Any())
            {
                healthData["errors"] = errors;
                return HealthCheckResult.Unhealthy("Business logic health check failed", null, healthData);
            }

            if (warnings.Any())
            {
                return HealthCheckResult.Degraded("Business logic health check has warnings", null, healthData);
            }

            return HealthCheckResult.Healthy("Business logic is healthy", healthData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during business logic health check");
            
            var errorData = new Dictionary<string, object>
            {
                ["error"] = ex.Message,
                ["stackTrace"] = ex.StackTrace
            };

            return HealthCheckResult.Unhealthy("Business logic health check failed", ex, errorData);
        }
    }
}
