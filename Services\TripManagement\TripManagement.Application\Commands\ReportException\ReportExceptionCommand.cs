using MediatR;
using TripManagement.Application.DTOs;
using TripManagement.Domain.Enums;

namespace TripManagement.Application.Commands.ReportException;

public record ReportExceptionCommand : IRequest<Guid>
{
    public Guid TripId { get; init; }
    public Guid ReportedBy { get; init; }
    public ExceptionType ExceptionType { get; init; }
    public string Description { get; init; } = string.Empty;
    public LocationDto? Location { get; init; }
    public ExceptionSeverity Severity { get; init; } = ExceptionSeverity.Medium;
    public bool RequiresImmediateAttention { get; init; }
    public string? SuggestedResolution { get; init; }
    public List<string>? AttachmentUrls { get; init; }
}

public enum ExceptionSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}
