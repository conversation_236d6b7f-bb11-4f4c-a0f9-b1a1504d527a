using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NetworkFleetManagement.Application.Commands.VehicleTypeMaster;
using NetworkFleetManagement.Application.DTOs;
using NetworkFleetManagement.Application.Queries.VehicleTypeMaster;

namespace NetworkFleetManagement.API.Controllers;

/// <summary>
/// Controller for managing vehicle type master data
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VehicleTypeMasterController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<VehicleTypeMasterController> _logger;

    public VehicleTypeMasterController(IMediator mediator, ILogger<VehicleTypeMasterController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all vehicle type masters
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<VehicleTypeMasterSummaryDto>>> GetVehicleTypeMasters(
        [FromQuery] bool activeOnly = false,
        [FromQuery] string? category = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleTypeMastersQuery(activeOnly, category);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle type masters");
            return StatusCode(500, "An error occurred while retrieving vehicle type masters");
        }
    }

    /// <summary>
    /// Get paginated vehicle type masters with filtering
    /// </summary>
    [HttpGet("paged")]
    public async Task<ActionResult<VehicleTypeMasterPagedResultDto>> GetVehicleTypeMastersPaged(
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] decimal? minLoadCapacity = null,
        [FromQuery] decimal? maxLoadCapacity = null,
        [FromQuery] decimal? minVolumeCapacity = null,
        [FromQuery] decimal? maxVolumeCapacity = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleTypeMastersPagedQuery(
                searchTerm, category, isActive, minLoadCapacity, maxLoadCapacity,
                minVolumeCapacity, maxVolumeCapacity, pageNumber, pageSize);
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paginated vehicle type masters");
            return StatusCode(500, "An error occurred while retrieving vehicle type masters");
        }
    }

    /// <summary>
    /// Get vehicle type master by ID
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<VehicleTypeMasterDto>> GetVehicleTypeMaster(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleTypeMasterByIdQuery(id);
            var result = await _mediator.Send(query, cancellationToken);
            
            if (result == null)
                return NotFound($"Vehicle type master with ID {id} not found");
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle type master {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the vehicle type master");
        }
    }

    /// <summary>
    /// Get vehicle type master by code
    /// </summary>
    [HttpGet("by-code/{code}")]
    public async Task<ActionResult<VehicleTypeMasterDto>> GetVehicleTypeMasterByCode(
        string code,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleTypeMasterByCodeQuery(code);
            var result = await _mediator.Send(query, cancellationToken);
            
            if (result == null)
                return NotFound($"Vehicle type master with code '{code}' not found");
                
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle type master by code {Code}", code);
            return StatusCode(500, "An error occurred while retrieving the vehicle type master");
        }
    }

    /// <summary>
    /// Get vehicle type master statistics
    /// </summary>
    [HttpGet("stats")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<VehicleTypeMasterStatsDto>> GetVehicleTypeMasterStats(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetVehicleTypeMasterStatsQuery();
            var result = await _mediator.Send(query, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving vehicle type master statistics");
            return StatusCode(500, "An error occurred while retrieving statistics");
        }
    }

    /// <summary>
    /// Create a new vehicle type master
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<VehicleTypeMasterDto>> CreateVehicleTypeMaster(
        [FromBody] CreateVehicleTypeMasterDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new CreateVehicleTypeMasterCommand
            {
                Code = request.Code,
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                MinLoadCapacityKg = request.MinLoadCapacityKg,
                MaxLoadCapacityKg = request.MaxLoadCapacityKg,
                MinVolumeCapacityM3 = request.MinVolumeCapacityM3,
                MaxVolumeCapacityM3 = request.MaxVolumeCapacityM3,
                SpecialRequirements = request.SpecialRequirements,
                SortOrder = request.SortOrder,
                IconUrl = request.IconUrl,
                AdditionalProperties = request.AdditionalProperties
            };

            var result = await _mediator.Send(command, cancellationToken);
            return CreatedAtAction(nameof(GetVehicleTypeMaster), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating vehicle type master");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle type master");
            return StatusCode(500, "An error occurred while creating the vehicle type master");
        }
    }

    /// <summary>
    /// Update an existing vehicle type master
    /// </summary>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<VehicleTypeMasterDto>> UpdateVehicleTypeMaster(
        Guid id,
        [FromBody] UpdateVehicleTypeMasterDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new UpdateVehicleTypeMasterCommand
            {
                Id = id,
                Name = request.Name,
                Description = request.Description,
                Category = request.Category,
                MinLoadCapacityKg = request.MinLoadCapacityKg,
                MaxLoadCapacityKg = request.MaxLoadCapacityKg,
                MinVolumeCapacityM3 = request.MinVolumeCapacityM3,
                MaxVolumeCapacityM3 = request.MaxVolumeCapacityM3,
                SpecialRequirements = request.SpecialRequirements,
                SortOrder = request.SortOrder,
                IconUrl = request.IconUrl,
                AdditionalProperties = request.AdditionalProperties
            };

            var result = await _mediator.Send(command, cancellationToken);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating vehicle type master {Id}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating vehicle type master {Id}", id);
            return StatusCode(500, "An error occurred while updating the vehicle type master");
        }
    }

    /// <summary>
    /// Delete a vehicle type master
    /// </summary>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteVehicleTypeMaster(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new DeleteVehicleTypeMasterCommand(id);
            var result = await _mediator.Send(command, cancellationToken);
            
            if (!result)
                return NotFound($"Vehicle type master with ID {id} not found");
                
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vehicle type master {Id}", id);
            return StatusCode(500, "An error occurred while deleting the vehicle type master");
        }
    }

    /// <summary>
    /// Activate a vehicle type master
    /// </summary>
    [HttpPost("{id:guid}/activate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> ActivateVehicleTypeMaster(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new ActivateVehicleTypeMasterCommand(id);
            var result = await _mediator.Send(command, cancellationToken);
            
            if (!result)
                return NotFound($"Vehicle type master with ID {id} not found");
                
            return Ok(new { message = "Vehicle type master activated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating vehicle type master {Id}", id);
            return StatusCode(500, "An error occurred while activating the vehicle type master");
        }
    }

    /// <summary>
    /// Deactivate a vehicle type master
    /// </summary>
    [HttpPost("{id:guid}/deactivate")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeactivateVehicleTypeMaster(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var command = new DeactivateVehicleTypeMasterCommand(id);
            var result = await _mediator.Send(command, cancellationToken);
            
            if (!result)
                return NotFound($"Vehicle type master with ID {id} not found");
                
            return Ok(new { message = "Vehicle type master deactivated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating vehicle type master {Id}", id);
            return StatusCode(500, "An error occurred while deactivating the vehicle type master");
        }
    }
}
