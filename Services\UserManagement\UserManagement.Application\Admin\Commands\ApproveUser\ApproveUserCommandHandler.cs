using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;
using UserManagement.Application.Services;
using Shared.Messaging;

namespace UserManagement.Application.Admin.Commands.ApproveUser
{
    public class ApproveUserCommandHandler : IRequestHandler<ApproveUserCommand, ApproveUserResponse>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly IAuditService _auditService;
        private readonly INotificationService _notificationService;
        private readonly ILogger<ApproveUserCommandHandler> _logger;

        public ApproveUserCommandHandler(
            IUserProfileRepository userProfileRepository,
            IMessageBroker messageBroker,
            IAuditService auditService,
            INotificationService notificationService,
            ILogger<ApproveUserCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _messageBroker = messageBroker;
            _auditService = auditService;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<ApproveUserResponse> Handle(ApproveUserCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing user approval for UserId: {UserId} by {ApprovedBy}",
                request.UserId, request.ApprovedBy);

            try
            {
                // Get the user profile
                var userProfile = await _userProfileRepository.GetByUserIdAsync(request.UserId);
                if (userProfile == null)
                {
                    _logger.LogWarning("User profile not found for UserId: {UserId}", request.UserId);
                    return new ApproveUserResponse
                    {
                        Success = false,
                        Message = "User profile not found"
                    };
                }

                // Approve the user profile
                userProfile.Approve(request.ApprovedBy);

                // Update in repository
                await _userProfileRepository.UpdateAsync(userProfile);

                // Log audit trail
                await _auditService.LogUserApprovalAsync(
                    request.UserId,
                    userProfile.GetDisplayName(),
                    request.ApprovedBy,
                    "Admin User", // In real implementation, get from current user context
                    "Admin",
                    "127.0.0.1", // In real implementation, get from HTTP context
                    "UserAgent", // In real implementation, get from HTTP context
                    request.Notes);

                // Send real-time notification
                await _notificationService.NotifyUserApprovedAsync(
                    request.UserId,
                    userProfile.UserType.ToString(),
                    userProfile.GetDisplayName(),
                    userProfile.Email,
                    request.ApprovedBy,
                    userProfile.ApprovedAt ?? DateTime.UtcNow,
                    request.Notes);

                // Publish integration event
                await _messageBroker.PublishAsync("user.approved", new
                {
                    UserId = request.UserId,
                    UserType = userProfile.UserType.ToString(),
                    Email = userProfile.Email,
                    DisplayName = userProfile.GetDisplayName(),
                    ApprovedBy = request.ApprovedBy,
                    ApprovedAt = userProfile.ApprovedAt,
                    Notes = request.Notes
                });

                _logger.LogInformation("Successfully approved user {UserId}", request.UserId);

                return new ApproveUserResponse
                {
                    Success = true,
                    Message = "User approved successfully",
                    ApprovedAt = userProfile.ApprovedAt ?? DateTime.UtcNow
                };
            }
            catch (UserManagementDomainException ex)
            {
                _logger.LogWarning("Domain validation failed for user approval: {Message}", ex.Message);
                return new ApproveUserResponse
                {
                    Success = false,
                    Message = ex.Message
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving user {UserId}", request.UserId);
                return new ApproveUserResponse
                {
                    Success = false,
                    Message = "An error occurred while approving the user"
                };
            }
        }
    }
}
