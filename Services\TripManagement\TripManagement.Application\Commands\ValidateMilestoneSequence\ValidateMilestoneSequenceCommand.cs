using MediatR;
using Microsoft.Extensions.Logging;
using TripManagement.Domain.Enums;
using TripManagement.Domain.Repositories;

namespace TripManagement.Application.Commands.ValidateMilestoneSequence;

public class ValidateMilestoneSequenceCommand : IRequest<ValidateMilestoneSequenceResponse>
{
    public Guid TripId { get; set; }
    public Guid MilestoneId { get; set; }
    public Guid DriverId { get; set; }
    public string Action { get; set; } = string.Empty; // Complete, Skip, Start
    public bool ForceValidation { get; set; } = false;
    public string? Reason { get; set; }
    public Dictionary<string, object> ValidationContext { get; set; } = new();
}

public class ValidateMilestoneSequenceResponse
{
    public bool IsValid { get; set; }
    public bool CanProceed { get; set; }
    public List<ValidationError> Errors { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
    public MilestoneSequenceInfo SequenceInfo { get; set; } = new();
    public List<string> RequiredActions { get; set; } = new();
    public Dictionary<string, object> ValidationMetadata { get; set; } = new();
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

public class ValidationError
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Severity { get; set; } = "Error"; // Error, Warning, Info
    public string? Field { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class ValidationWarning
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public bool CanOverride { get; set; } = false;
    public string? OverrideReason { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

public class MilestoneSequenceInfo
{
    public Guid CurrentMilestoneId { get; set; }
    public string CurrentMilestoneName { get; set; } = string.Empty;
    public int CurrentSequenceNumber { get; set; }
    public string CurrentStatus { get; set; } = string.Empty;
    public List<MilestoneInfo> PreviousMilestones { get; set; } = new();
    public List<MilestoneInfo> NextMilestones { get; set; } = new();
    public List<MilestoneInfo> PendingMilestones { get; set; } = new();
    public List<MilestoneInfo> CompletedMilestones { get; set; } = new();
    public bool IsFirstMilestone { get; set; }
    public bool IsLastMilestone { get; set; }
    public bool HasOutOfSequenceCompletions { get; set; }
    public double CompletionPercentage { get; set; }
}

public class MilestoneInfo
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int SequenceNumber { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool RequiresConfirmation { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public bool IsOverdue { get; set; }
    public List<string> Dependencies { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// Command Handler

public class ValidateMilestoneSequenceCommandHandler : IRequestHandler<ValidateMilestoneSequenceCommand, ValidateMilestoneSequenceResponse>
{
    private readonly ITripRepository _tripRepository;
    private readonly IMilestoneConfirmationConfigurationRepository _configurationRepository;
    private readonly ILogger<ValidateMilestoneSequenceCommandHandler> _logger;

    public ValidateMilestoneSequenceCommandHandler(
        ITripRepository tripRepository,
        IMilestoneConfirmationConfigurationRepository configurationRepository,
        ILogger<ValidateMilestoneSequenceCommandHandler> logger)
    {
        _tripRepository = tripRepository;
        _configurationRepository = configurationRepository;
        _logger = logger;
    }

    public async Task<ValidateMilestoneSequenceResponse> Handle(ValidateMilestoneSequenceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Validating milestone sequence for trip {TripId}, milestone {MilestoneId}", request.TripId, request.MilestoneId);

            var response = new ValidateMilestoneSequenceResponse();

            // Get trip
            var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
            if (trip == null)
            {
                response.Errors.Add(new ValidationError
                {
                    Code = "TRIP_NOT_FOUND",
                    Message = "Trip not found",
                    Severity = "Error"
                });
                return response;
            }

            // Get milestone configuration
            var configuration = await _configurationRepository.GetByTripAndMilestoneAsync(request.TripId, request.MilestoneId, cancellationToken);

            // Build sequence info (mock implementation - in real scenario, this would query actual milestone data)
            response.SequenceInfo = await BuildMilestoneSequenceInfo(request.TripId, request.MilestoneId, cancellationToken);

            // Perform sequence validation
            if (configuration?.RequiresSequenceValidation() == true)
            {
                await ValidateSequenceOrder(request, response, cancellationToken);
            }

            // Validate prerequisites
            await ValidatePrerequisites(request, response, cancellationToken);

            // Validate business rules
            await ValidateBusinessRules(request, response, cancellationToken);

            // Determine if can proceed
            response.CanProceed = response.Errors.Count == 0 ||
                                (request.ForceValidation && response.Errors.All(e => e.Severity != "Critical"));
            response.IsValid = response.Errors.Count == 0;

            _logger.LogDebug("Milestone sequence validation completed for trip {TripId}: {IsValid}", request.TripId, response.IsValid);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating milestone sequence for trip {TripId}", request.TripId);
            return new ValidateMilestoneSequenceResponse
            {
                IsValid = false,
                CanProceed = false,
                Errors = new List<ValidationError>
                {
                    new ValidationError
                    {
                        Code = "VALIDATION_ERROR",
                        Message = ex.Message,
                        Severity = "Critical"
                    }
                }
            };
        }
    }

    private async Task<MilestoneSequenceInfo> BuildMilestoneSequenceInfo(Guid tripId, Guid milestoneId, CancellationToken cancellationToken)
    {
        // Mock implementation - in real scenario, this would query milestone data from database
        return new MilestoneSequenceInfo
        {
            CurrentMilestoneId = milestoneId,
            CurrentMilestoneName = "Pickup Completed",
            CurrentSequenceNumber = 1,
            CurrentStatus = "Pending",
            IsFirstMilestone = true,
            IsLastMilestone = false,
            CompletionPercentage = 0.0,
            PreviousMilestones = new List<MilestoneInfo>(),
            NextMilestones = new List<MilestoneInfo>
            {
                new MilestoneInfo
                {
                    Id = Guid.NewGuid(),
                    Name = "In Transit",
                    SequenceNumber = 2,
                    Status = "Pending",
                    IsRequired = true
                },
                new MilestoneInfo
                {
                    Id = Guid.NewGuid(),
                    Name = "Delivery Completed",
                    SequenceNumber = 3,
                    Status = "Pending",
                    IsRequired = true
                }
            },
            PendingMilestones = new List<MilestoneInfo>(),
            CompletedMilestones = new List<MilestoneInfo>()
        };
    }

    private async Task ValidateSequenceOrder(ValidateMilestoneSequenceCommand request, ValidateMilestoneSequenceResponse response, CancellationToken cancellationToken)
    {
        // Check if previous required milestones are completed
        var incompletePrevious = response.SequenceInfo.PreviousMilestones
            .Where(m => m.IsRequired && m.Status != "Completed")
            .ToList();

        if (incompletePrevious.Any())
        {
            response.Errors.Add(new ValidationError
            {
                Code = "SEQUENCE_VIOLATION",
                Message = $"Previous required milestones must be completed first: {string.Join(", ", incompletePrevious.Select(m => m.Name))}",
                Severity = "Error",
                Details = new Dictionary<string, object>
                {
                    { "IncompleteMilestones", incompletePrevious.Select(m => new { m.Id, m.Name, m.SequenceNumber }) }
                }
            });
        }

        // Check for out-of-sequence completion
        if (request.Action == "Complete" && !response.SequenceInfo.IsFirstMilestone)
        {
            var configuration = await _configurationRepository.GetByTripAndMilestoneAsync(request.TripId, request.MilestoneId, cancellationToken);
            if (configuration?.AllowsOutOfSequence() != true)
            {
                response.Warnings.Add(new ValidationWarning
                {
                    Code = "OUT_OF_SEQUENCE",
                    Message = "Completing this milestone out of sequence may affect trip workflow",
                    CanOverride = true,
                    Details = new Dictionary<string, object>
                    {
                        { "CurrentSequence", response.SequenceInfo.CurrentSequenceNumber },
                        { "ExpectedSequence", response.SequenceInfo.PreviousMilestones.Count + 1 }
                    }
                });
            }
        }
    }

    private async Task ValidatePrerequisites(ValidateMilestoneSequenceCommand request, ValidateMilestoneSequenceResponse response, CancellationToken cancellationToken)
    {
        // Validate driver assignment
        var trip = await _tripRepository.GetByIdAsync(request.TripId, cancellationToken);
        if (trip?.DriverId != request.DriverId)
        {
            response.Errors.Add(new ValidationError
            {
                Code = "DRIVER_NOT_ASSIGNED",
                Message = "Driver is not assigned to this trip",
                Severity = "Error"
            });
        }

        // Validate trip status
        if (trip?.Status != TripStatus.InProgress && request.Action == "Complete")
        {
            response.Errors.Add(new ValidationError
            {
                Code = "INVALID_TRIP_STATUS",
                Message = $"Cannot complete milestone when trip status is {trip?.Status}",
                Severity = "Error"
            });
        }
    }

    private async Task ValidateBusinessRules(ValidateMilestoneSequenceCommand request, ValidateMilestoneSequenceResponse response, CancellationToken cancellationToken)
    {
        // Add business rule validations
        var currentMilestone = response.SequenceInfo;

        // Check for overdue milestones
        if (currentMilestone.PendingMilestones.Any(m => m.IsOverdue))
        {
            response.Warnings.Add(new ValidationWarning
            {
                Code = "OVERDUE_MILESTONES",
                Message = "There are overdue milestones in this trip",
                CanOverride = false
            });
        }

        // Validate required fields based on action
        if (request.Action == "Skip" && string.IsNullOrWhiteSpace(request.Reason))
        {
            response.Errors.Add(new ValidationError
            {
                Code = "SKIP_REASON_REQUIRED",
                Message = "Reason is required when skipping a milestone",
                Severity = "Error",
                Field = "Reason"
            });
        }

        // Add required actions
        if (request.Action == "Complete")
        {
            response.RequiredActions.Add("Confirm completion");

            var configuration = await _configurationRepository.GetByTripAndMilestoneAsync(request.TripId, request.MilestoneId, cancellationToken);
            if (configuration?.RequiresManagerApproval() == true)
            {
                response.RequiredActions.Add("Manager approval required");
            }
        }
    }
}
