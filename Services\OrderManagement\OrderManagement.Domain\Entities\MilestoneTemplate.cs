using Shared.Domain.Common;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.ValueObjects;

namespace OrderManagement.Domain.Entities;

/// <summary>
/// Entity representing a milestone template for RFQ execution tracking
/// </summary>
public class MilestoneTemplate : BaseEntity
{
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public MilestoneTemplateType TemplateType { get; private set; }
    public bool IsActive { get; private set; }
    public bool IsDefault { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public Guid? UpdatedBy { get; private set; }
    public string? Tags { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }

    // Navigation properties
    private readonly List<MilestoneStep> _steps = new();
    public IReadOnlyCollection<MilestoneStep> Steps => _steps.AsReadOnly();

    private readonly List<RfqMilestoneAssignment> _rfqAssignments = new();
    public IReadOnlyCollection<RfqMilestoneAssignment> RfqAssignments => _rfqAssignments.AsReadOnly();

    private MilestoneTemplate() { } // EF Constructor

    public MilestoneTemplate(
        string name,
        string description,
        MilestoneTemplateType templateType,
        Guid createdBy,
        bool isDefault = false,
        string? tags = null,
        Dictionary<string, object>? metadata = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        TemplateType = templateType;
        CreatedBy = createdBy;
        CreatedAt = DateTime.UtcNow;
        IsActive = true;
        IsDefault = isDefault;
        Tags = tags;
        Metadata = metadata;
    }

    public void UpdateDetails(
        string name,
        string description,
        Guid updatedBy,
        string? tags = null,
        Dictionary<string, object>? metadata = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
        Tags = tags;
        Metadata = metadata;
    }

    public void AddStep(MilestoneStep step)
    {
        if (_steps.Any(s => s.StepOrder == step.StepOrder))
            throw new InvalidOperationException($"Step with order {step.StepOrder} already exists");

        _steps.Add(step);
    }

    public void RemoveStep(Guid stepId)
    {
        var step = _steps.FirstOrDefault(s => s.Id == stepId);
        if (step != null)
        {
            _steps.Remove(step);
            ReorderSteps();
        }
    }

    public void ReorderSteps()
    {
        var orderedSteps = _steps.OrderBy(s => s.StepOrder).ToList();
        for (int i = 0; i < orderedSteps.Count; i++)
        {
            orderedSteps[i].UpdateOrder(i + 1);
        }
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void SetAsDefault()
    {
        IsDefault = true;
    }

    public void RemoveAsDefault()
    {
        IsDefault = false;
    }

    public decimal GetTotalPayoutPercentage()
    {
        return _steps.Sum(s => s.PayoutPercentage);
    }

    public bool IsValidPayoutStructure()
    {
        var totalPayout = GetTotalPayoutPercentage();
        return totalPayout == 100m;
    }

    public MilestoneStep? GetStepByOrder(int order)
    {
        return _steps.FirstOrDefault(s => s.StepOrder == order);
    }

    public MilestoneStep? GetNextStep(int currentOrder)
    {
        return _steps.Where(s => s.StepOrder > currentOrder)
                    .OrderBy(s => s.StepOrder)
                    .FirstOrDefault();
    }

    public bool CanBeDeleted()
    {
        return !_rfqAssignments.Any(ra => ra.IsActive);
    }
}
