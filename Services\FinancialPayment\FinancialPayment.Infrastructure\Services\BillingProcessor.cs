using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Services;

public class BillingProcessor : IBillingProcessor
{
    private readonly IPaymentGatewayFactory _gatewayFactory;
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly ISubscriptionNotificationService _notificationService;
    private readonly ILogger<BillingProcessor> _logger;
    private readonly SubscriptionConfiguration _configuration;

    public BillingProcessor(
        IPaymentGatewayFactory gatewayFactory,
        ISubscriptionRepository subscriptionRepository,
        ISubscriptionNotificationService notificationService,
        ILogger<BillingProcessor> logger,
        IOptions<SubscriptionConfiguration> configuration)
    {
        _gatewayFactory = gatewayFactory;
        _subscriptionRepository = subscriptionRepository;
        _notificationService = notificationService;
        _logger = logger;
        _configuration = configuration.Value;
    }

    public async Task<BillingResult> ProcessSubscriptionBillingAsync(Subscription subscription, SubscriptionPlan plan)
    {
        _logger.LogInformation("Processing billing for subscription {SubscriptionId}", subscription.Id);

        try
        {
            // Check if subscription should be billed
            if (!subscription.ShouldBeBilled())
            {
                _logger.LogInformation("Subscription {SubscriptionId} is not due for billing", subscription.Id);
                return new BillingResult
                {
                    SubscriptionId = subscription.Id,
                    IsSuccess = true,
                    ProcessedAt = DateTime.UtcNow,
                    ErrorMessage = "Not due for billing"
                };
            }

            // Validate payment method
            if (string.IsNullOrEmpty(subscription.PaymentMethodId))
            {
                _logger.LogWarning("Subscription {SubscriptionId} has no payment method", subscription.Id);
                
                await _notificationService.SendPaymentMethodUpdateRequiredNotificationAsync(subscription);
                
                return new BillingResult
                {
                    SubscriptionId = subscription.Id,
                    IsSuccess = false,
                    ErrorMessage = "No payment method available",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            var isValidPaymentMethod = await ValidatePaymentMethodAsync(subscription.PaymentMethodId);
            if (!isValidPaymentMethod)
            {
                _logger.LogWarning("Invalid payment method for subscription {SubscriptionId}", subscription.Id);
                
                await _notificationService.SendPaymentMethodUpdateRequiredNotificationAsync(subscription);
                
                return new BillingResult
                {
                    SubscriptionId = subscription.Id,
                    IsSuccess = false,
                    ErrorMessage = "Invalid payment method",
                    ProcessedAt = DateTime.UtcNow
                };
            }

            // Create new billing cycle
            var cycleStart = subscription.NextBillingDate;
            var cycleEnd = plan.CalculateNextBillingDate(cycleStart);
            var billingCycle = subscription.StartNewBillingCycle(cycleStart, cycleEnd, subscription.CurrentPrice);

            // Process payment
            var paymentResult = await ChargeSubscriptionAsync(
                subscription, 
                subscription.CurrentPrice, 
                $"Subscription billing for cycle {billingCycle.CycleNumber}");

            if (paymentResult.IsSuccess)
            {
                // Mark billing cycle as successful
                billingCycle.MarkAsBilled(paymentResult.TransactionId!);
                billingCycle.MarkAsPaid();

                // Update subscription
                await _subscriptionRepository.UpdateAsync(subscription);

                // Send success notification
                await _notificationService.SendBillingSuccessNotificationAsync(subscription, billingCycle);

                _logger.LogInformation("Billing successful for subscription {SubscriptionId}, transaction {TransactionId}",
                    subscription.Id, paymentResult.TransactionId);

                return new BillingResult
                {
                    SubscriptionId = subscription.Id,
                    IsSuccess = true,
                    TransactionId = paymentResult.TransactionId,
                    AmountCharged = subscription.CurrentPrice,
                    ProcessedAt = DateTime.UtcNow,
                    BillingCycle = billingCycle
                };
            }
            else
            {
                // Handle billing failure
                var nextRetryAt = CalculateNextRetryDate(billingCycle.RetryCount);
                billingCycle.MarkAsFailed(paymentResult.ErrorMessage ?? "Payment failed", nextRetryAt);

                // Update subscription
                await _subscriptionRepository.UpdateAsync(subscription);

                // Send failure notification
                await _notificationService.SendBillingFailureNotificationAsync(
                    subscription, billingCycle, paymentResult.ErrorMessage ?? "Payment failed");

                _logger.LogWarning("Billing failed for subscription {SubscriptionId}: {Error}",
                    subscription.Id, paymentResult.ErrorMessage);

                return new BillingResult
                {
                    SubscriptionId = subscription.Id,
                    IsSuccess = false,
                    ErrorMessage = paymentResult.ErrorMessage,
                    ProcessedAt = DateTime.UtcNow,
                    BillingCycle = billingCycle,
                    RetryCount = billingCycle.RetryCount,
                    NextRetryAt = nextRetryAt
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing billing for subscription {SubscriptionId}", subscription.Id);
            
            return new BillingResult
            {
                SubscriptionId = subscription.Id,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<PaymentResult> ChargeSubscriptionAsync(Subscription subscription, Money amount, string description)
    {
        _logger.LogInformation("Charging subscription {SubscriptionId} amount {Amount}",
            subscription.Id, amount);

        try
        {
            var gateway = _gatewayFactory.GetDefaultGateway();
            
            var paymentRequest = new PaymentRequest
            {
                Amount = amount,
                UserId = subscription.UserId,
                PaymentMethodId = subscription.PaymentMethodId!,
                Description = description,
                Metadata = new Dictionary<string, object>
                {
                    { "subscription_id", subscription.Id.ToString() },
                    { "billing_type", "subscription" },
                    { "customer_id", subscription.CustomerId ?? string.Empty }
                }
            };

            var result = await gateway.ProcessPaymentAsync(paymentRequest);

            _logger.LogInformation("Payment processed for subscription {SubscriptionId}: {Success}",
                subscription.Id, result.IsSuccess);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error charging subscription {SubscriptionId}", subscription.Id);
            
            return new PaymentResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProcessedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<bool> ValidatePaymentMethodAsync(string paymentMethodId)
    {
        try
        {
            // For demo purposes, simulate validation
            // In a real implementation, you would validate with the payment gateway
            await Task.Delay(100);
            
            // Simple validation - check if payment method ID is not empty and has valid format
            return !string.IsNullOrWhiteSpace(paymentMethodId) && paymentMethodId.Length > 5;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating payment method {PaymentMethodId}", paymentMethodId);
            return false;
        }
    }

    public async Task<string> CreateCustomerAsync(Guid userId, string email, string? paymentMethodId = null)
    {
        _logger.LogInformation("Creating customer for user {UserId}", userId);

        try
        {
            // For demo purposes, simulate customer creation
            // In a real implementation, you would create customer with the payment gateway
            await Task.Delay(200);
            
            var customerId = $"cust_{userId:N}";
            
            _logger.LogInformation("Customer created: {CustomerId} for user {UserId}", customerId, userId);
            return customerId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer for user {UserId}", userId);
            throw;
        }
    }

    public async Task<string> UpdateCustomerPaymentMethodAsync(string customerId, string paymentMethodId)
    {
        _logger.LogInformation("Updating payment method for customer {CustomerId}", customerId);

        try
        {
            // For demo purposes, simulate payment method update
            // In a real implementation, you would update with the payment gateway
            await Task.Delay(100);
            
            _logger.LogInformation("Payment method updated for customer {CustomerId}", customerId);
            return paymentMethodId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating payment method for customer {CustomerId}", customerId);
            throw;
        }
    }

    private DateTime? CalculateNextRetryDate(int retryCount)
    {
        if (retryCount >= _configuration.BillingRetryAttempts)
        {
            return null; // No more retries
        }

        // Exponential backoff: 1 day, 2 days, 4 days, etc.
        var delayHours = _configuration.BillingRetryIntervalHours * Math.Pow(2, retryCount);
        return DateTime.UtcNow.AddHours(delayHours);
    }
}
