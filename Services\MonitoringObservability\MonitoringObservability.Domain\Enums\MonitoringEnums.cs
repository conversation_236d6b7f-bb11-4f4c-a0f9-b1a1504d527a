namespace MonitoringObservability.Domain.Enums;

public enum AlertSeverity
{
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3,
    Fatal = 4
}

public enum AlertStatus
{
    Open = 0,
    Acknowledged = 1,
    InProgress = 2,
    Resolved = 3,
    Closed = 4,
    Suppressed = 5
}

public enum MetricType
{
    Counter = 0,
    Gauge = 1,
    Histogram = 2,
    Summary = 3,
    Timer = 4
}

public enum HealthStatus
{
    Healthy = 0,
    Degraded = 1,
    Unhealthy = 2,
    Unknown = 3
}

public enum LogLevel
{
    Trace = 0,
    Debug = 1,
    Information = 2,
    Warning = 3,
    Error = 4,
    Critical = 5,
    None = 6
}

public enum IncidentStatus
{
    Open = 0,
    Investigating = 1,
    Identified = 2,
    Monitoring = 3,
    Resolved = 4,
    Closed = 5
}

public enum IncidentSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ServiceType
{
    WebAPI = 0,
    Database = 1,
    MessageQueue = 2,
    Cache = 3,
    ExternalService = 4,
    FileStorage = 5,
    LoadBalancer = 6,
    Gateway = 7,
    Microservice = 8,
    Other = 99
}

public enum MonitoringCategory
{
    Performance = 0,
    Availability = 1,
    Security = 2,
    Business = 3,
    Infrastructure = 4,
    Application = 5,
    Network = 6,
    Database = 7
}

public enum NotificationChannel
{
    Email = 0,
    SMS = 1,
    Slack = 2,
    Teams = 3,
    Webhook = 4,
    PagerDuty = 5,
    Discord = 6,
    InApp = 7
}

public enum TraceStatus
{
    Ok = 0,
    Error = 1,
    Timeout = 2,
    Cancelled = 3
}

public enum DashboardType
{
    System = 0,
    Application = 1,
    Business = 2,
    Custom = 3,
    RealTime = 4,
    Historical = 5
}

public enum AlertRuleType
{
    Threshold = 0,
    Anomaly = 1,
    Composite = 2,
    Pattern = 3,
    Correlation = 4,
    Forecast = 5,
    Custom = 6
}

public enum AlertConditionType
{
    Simple = 0,
    Threshold = 1,
    Range = 2,
    Rate = 3,
    Percentage = 4,
    Count = 5,
    Composite = 6,
    Pattern = 7,
    Anomaly = 8,
    Correlation = 9,
    Custom = 10
}

public enum AlertConditionOperator
{
    And = 0,
    Or = 1,
    Not = 2,
    Xor = 3
}

public enum AlertActionType
{
    Notification = 0,
    Webhook = 1,
    CreateIncident = 2,
    Escalate = 3,
    Suppress = 4,
    AutoRemediation = 5,
    Script = 6,
    Integration = 7
}

public enum CorrelationRuleType
{
    TimeWindow = 0,
    ServiceDependency = 1,
    MetricCorrelation = 2,
    PatternMatching = 3,
    AnomalyCluster = 4
}

public enum AnomalyDetectionAlgorithm
{
    StatisticalZScore = 0,
    IsolationForest = 1,
    LocalOutlierFactor = 2,
    OneClassSVM = 3,
    LSTM = 4,
    Prophet = 5,
    MovingAverage = 6,
    SeasonalDecomposition = 7,
    Custom = 8
}

public enum AnomalySensitivity
{
    Low = 0,
    Medium = 1,
    High = 2,
    VeryHigh = 3
}

public enum TrainingStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

public enum DashboardVisibility
{
    Private = 0,
    Shared = 1,
    Public = 2,
    Organization = 3
}

public enum DashboardAccessLevel
{
    View = 0,
    Edit = 1,
    Admin = 2
}

public enum WidgetType
{
    LineChart = 0,
    BarChart = 1,
    PieChart = 2,
    Gauge = 3,
    Counter = 4,
    Table = 5,
    Heatmap = 6,
    Histogram = 7,
    ScatterPlot = 8,
    AreaChart = 9,
    Text = 10,
    Image = 11,
    Alert = 12,
    Log = 13,
    Metric = 14,
    Status = 15,
    Map = 16,
    Custom = 17
}

public enum FilterType
{
    Text = 0,
    Number = 1,
    Date = 2,
    Boolean = 3,
    List = 4,
    Range = 5,
    Custom = 6
}

public enum FilterOperator
{
    Equals = 0,
    NotEquals = 1,
    GreaterThan = 2,
    GreaterThanOrEqual = 3,
    LessThan = 4,
    LessThanOrEqual = 5,
    Contains = 6,
    NotContains = 7,
    StartsWith = 8,
    EndsWith = 9,
    In = 10,
    NotIn = 11,
    Between = 12,
    IsNull = 13,
    IsNotNull = 14
}

public enum SLAStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2,
    Expired = 3,
    Excellent = 4,
    Good = 5,
    Warning = 6,
    Critical = 7
}

public enum SLAObjectiveType
{
    Availability = 0,
    ResponseTime = 1,
    Throughput = 2,
    ErrorRate = 3,
    Latency = 4,
    Uptime = 5,
    Custom = 6
}

public enum SLAComparisonOperator
{
    GreaterThan = 0,
    GreaterThanOrEqual = 1,
    LessThan = 2,
    LessThanOrEqual = 3,
    Equals = 4
}

public enum BreachStatus
{
    Open = 0,
    Acknowledged = 1,
    Resolved = 2,
    Closed = 3
}

public enum BreachSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum SLAReportType
{
    Daily = 0,
    Weekly = 1,
    Monthly = 2,
    Quarterly = 3,
    Annual = 4,
    Custom = 5
}

public enum CapacityPlanType
{
    Hourly = 0,
    Daily = 1,
    Weekly = 2,
    Monthly = 3,
    Quarterly = 4,
    OnDemand = 5
}

public enum CapacityPlanStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2,
    Completed = 3,
    Failed = 4
}

public enum ResourceType
{
    CPU = 0,
    Memory = 1,
    Storage = 2,
    Network = 3,
    Database = 4,
    Queue = 5,
    Cache = 6,
    Custom = 7
}

public enum ForecastModel
{
    Linear = 0,
    Exponential = 1,
    Polynomial = 2,
    Seasonal = 3,
    ARIMA = 4,
    Prophet = 5,
    MachineLearning = 6
}

public enum CapacityAlertType
{
    Threshold = 0,
    Trend = 1,
    Anomaly = 2,
    Forecast = 3
}

public enum CapacityAlertStatus
{
    Open = 0,
    Acknowledged = 1,
    Resolved = 2,
    Closed = 3
}

public enum CapacityAlertSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ScalingAction
{
    ScaleUp = 0,
    ScaleDown = 1,
    ScaleOut = 2,
    ScaleIn = 3,
    Optimize = 4,
    Migrate = 5
}

public enum RecommendationStatus
{
    Pending = 0,
    Approved = 1,
    Rejected = 2,
    Implemented = 3,
    Failed = 4
}

public enum RecommendationPriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum CostAnalysisScope
{
    Daily = 0,
    Weekly = 1,
    Monthly = 2,
    Quarterly = 3,
    Annual = 4
}

public enum CostAnalysisStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2,
    Completed = 3,
    Failed = 4
}

public enum CostOptimizationType
{
    RightSizing = 0,
    Reserved = 1,
    Spot = 2,
    Scheduling = 3,
    Storage = 4,
    Network = 5,
    Licensing = 6,
    Decommission = 7
}

public enum CostOptimizationPriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum CostRecommendationStatus
{
    Pending = 0,
    Approved = 1,
    Rejected = 2,
    Implemented = 3,
    Failed = 4
}

public enum CostAlertType
{
    Budget = 0,
    Anomaly = 1,
    Trend = 2,
    Threshold = 3
}

public enum CostAlertStatus
{
    Open = 0,
    Acknowledged = 1,
    Resolved = 2,
    Closed = 3
}

public enum CostAlertSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum CostEfficiencyRating
{
    Poor = 0,
    Fair = 1,
    Good = 2,
    Excellent = 3
}

public enum ReportType
{
    Performance = 0,
    Availability = 1,
    Security = 2,
    Business = 3,
    Compliance = 4,
    Custom = 5
}

public enum ReportFrequency
{
    RealTime = 0,
    Hourly = 1,
    Daily = 2,
    Weekly = 3,
    Monthly = 4,
    Quarterly = 5,
    Yearly = 6,
    OnDemand = 7
}
