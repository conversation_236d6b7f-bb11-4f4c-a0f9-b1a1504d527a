using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Tests.Shared.Builders;
using Tests.Shared.Infrastructure;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using Xunit;
using Xunit.Abstractions;

namespace Tests.Services.UserManagement.Unit;

[Trait("Category", "Unit")]
[Trait("Service", "UserManagement")]
public class UserProfileServiceTests : BaseUnitTest
{
    private readonly Mock<IUserProfileRepository> _mockUserRepository;
    private readonly Mock<ILogger<UserProfileService>> _mockLogger;
    private readonly UserProfileService _userProfileService;
    private readonly UserTestDataBuilder _userBuilder;

    public UserProfileServiceTests()
    {
        _mockUserRepository = CreateMock<IUserProfileRepository>();
        _mockLogger = CreateMockLogger<UserProfileService>();
        _userProfileService = new UserProfileService(_mockUserRepository.Object, _mockLogger.Object);
        _userBuilder = new UserTestDataBuilder();
    }

    [Fact]
    public async Task GetUserProfileAsync_WithValidId_ShouldReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var expectedUser = CreateUserProfile(userId);
        
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId))
            .ReturnsAsync(expectedUser);

        // Act
        var result = await _userProfileService.GetUserProfileAsync(userId);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(userId);
        result.Email.Should().Be(expectedUser.Email);
        
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByIdAsync(userId));
    }

    [Fact]
    public async Task GetUserProfileAsync_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var userId = Guid.NewGuid();
        
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId))
            .ReturnsAsync((UserProfile?)null);

        // Act
        var result = await _userProfileService.GetUserProfileAsync(userId);

        // Assert
        result.Should().BeNull();
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByIdAsync(userId));
    }

    [Fact]
    public async Task CreateUserProfileAsync_WithValidData_ShouldCreateAndReturnUser()
    {
        // Arrange
        var userData = _userBuilder.Build();
        var expectedUser = CreateUserProfile(userData.Id, userData.Email);
        
        _mockUserRepository
            .Setup(x => x.AddAsync(It.IsAny<UserProfile>()))
            .ReturnsAsync(expectedUser);

        // Act
        var result = await _userProfileService.CreateUserProfileAsync(
            userData.Email, 
            userData.FirstName, 
            userData.LastName, 
            userData.PhoneNumber);

        // Assert
        result.Should().NotBeNull();
        result.Email.Should().Be(userData.Email);
        result.FirstName.Should().Be(userData.FirstName);
        result.LastName.Should().Be(userData.LastName);
        
        VerifyMockCallOnce(_mockUserRepository, x => x.AddAsync(It.IsAny<UserProfile>()));
    }

    [Theory]
    [InlineData("")]
    [InlineData("invalid-email")]
    [InlineData("@domain.com")]
    public async Task CreateUserProfileAsync_WithInvalidEmail_ShouldThrowArgumentException(string invalidEmail)
    {
        // Arrange & Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _userProfileService.CreateUserProfileAsync(invalidEmail, "John", "Doe", "+1234567890"));
        
        VerifyMockNeverCalled(_mockUserRepository, x => x.AddAsync(It.IsAny<UserProfile>()));
    }

    [Fact]
    public async Task UpdateUserProfileAsync_WithValidData_ShouldUpdateAndReturnUser()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingUser = CreateUserProfile(userId);
        var updatedData = _userBuilder.Build();
        
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId))
            .ReturnsAsync(existingUser);
        
        _mockUserRepository
            .Setup(x => x.UpdateAsync(It.IsAny<UserProfile>()))
            .ReturnsAsync((UserProfile user) => user);

        // Act
        var result = await _userProfileService.UpdateUserProfileAsync(
            userId,
            updatedData.FirstName,
            updatedData.LastName,
            updatedData.PhoneNumber);

        // Assert
        result.Should().NotBeNull();
        result.FirstName.Should().Be(updatedData.FirstName);
        result.LastName.Should().Be(updatedData.LastName);
        result.PhoneNumber.Should().Be(updatedData.PhoneNumber);
        
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByIdAsync(userId));
        VerifyMockCallOnce(_mockUserRepository, x => x.UpdateAsync(It.IsAny<UserProfile>()));
    }

    [Fact]
    public async Task UpdateUserProfileAsync_WithNonExistentUser_ShouldThrowNotFoundException()
    {
        // Arrange
        var userId = Guid.NewGuid();
        
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId))
            .ReturnsAsync((UserProfile?)null);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _userProfileService.UpdateUserProfileAsync(userId, "John", "Doe", "+1234567890"));
        
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByIdAsync(userId));
        VerifyMockNeverCalled(_mockUserRepository, x => x.UpdateAsync(It.IsAny<UserProfile>()));
    }

    [Fact]
    public async Task DeleteUserProfileAsync_WithValidId_ShouldDeleteUser()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var existingUser = CreateUserProfile(userId);
        
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(userId))
            .ReturnsAsync(existingUser);

        // Act
        await _userProfileService.DeleteUserProfileAsync(userId);

        // Assert
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByIdAsync(userId));
        VerifyMockCallOnce(_mockUserRepository, x => x.DeleteAsync(userId));
    }

    [Fact]
    public async Task GetUsersByCompanyAsync_WithValidCompany_ShouldReturnUsers()
    {
        // Arrange
        var companyName = "Test Company";
        var expectedUsers = CreateMany<UserProfile>(5).ToList();
        
        _mockUserRepository
            .Setup(x => x.GetByCompanyAsync(companyName))
            .ReturnsAsync(expectedUsers);

        // Act
        var result = await _userProfileService.GetUsersByCompanyAsync(companyName);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(5);
        
        VerifyMockCallOnce(_mockUserRepository, x => x.GetByCompanyAsync(companyName));
    }

    private UserProfile CreateUserProfile(Guid? id = null, string? email = null)
    {
        var userData = _userBuilder.Build();
        return new UserProfile(
            id ?? userData.Id,
            email ?? userData.Email,
            userData.FirstName,
            userData.LastName,
            userData.PhoneNumber,
            userData.CompanyName
        );
    }
}

// Simplified service class for testing
public class UserProfileService
{
    private readonly IUserProfileRepository _userRepository;
    private readonly ILogger<UserProfileService> _logger;

    public UserProfileService(IUserProfileRepository userRepository, ILogger<UserProfileService> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    public async Task<UserProfile?> GetUserProfileAsync(Guid userId)
    {
        return await _userRepository.GetByIdAsync(userId);
    }

    public async Task<UserProfile> CreateUserProfileAsync(string email, string firstName, string lastName, string phoneNumber)
    {
        if (string.IsNullOrEmpty(email) || !IsValidEmail(email))
            throw new ArgumentException("Invalid email address", nameof(email));

        var user = new UserProfile(Guid.NewGuid(), email, firstName, lastName, phoneNumber, "");
        return await _userRepository.AddAsync(user);
    }

    public async Task<UserProfile> UpdateUserProfileAsync(Guid userId, string firstName, string lastName, string phoneNumber)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
            throw new NotFoundException($"User with ID {userId} not found");

        // Update user properties (simplified)
        return await _userRepository.UpdateAsync(user);
    }

    public async Task DeleteUserProfileAsync(Guid userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
            throw new NotFoundException($"User with ID {userId} not found");

        await _userRepository.DeleteAsync(userId);
    }

    public async Task<IEnumerable<UserProfile>> GetUsersByCompanyAsync(string companyName)
    {
        return await _userRepository.GetByCompanyAsync(companyName);
    }

    private bool IsValidEmail(string email)
    {
        return email.Contains("@") && email.Contains(".");
    }
}

public class NotFoundException : Exception
{
    public NotFoundException(string message) : base(message) { }
}
