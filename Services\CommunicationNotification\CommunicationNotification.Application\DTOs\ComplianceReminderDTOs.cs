using CommunicationNotification.Domain.Enums;

namespace CommunicationNotification.Application.DTOs;

/// <summary>
/// Configuration for Transport Company compliance reminders
/// </summary>
public class TransportCompanyComplianceConfiguration
{
    public Guid TransportCompanyId { get; set; }
    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public TimeSpan PreferredDeliveryTime { get; set; } = new(9, 0, 0);
    public bool GroupSimilarReminders { get; set; } = true;
    public int MaxRemindersPerDay { get; set; } = 5;
    public List<ComplianceReminderConfiguration> ReminderConfigurations { get; set; } = new();
    public DateTime ConfiguredAt { get; set; }
    public DateTime? LastProcessedAt { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Information about an expiring document
/// </summary>
public class ExpiringDocumentInfo
{
    public string DocumentType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public DateTime ExpiryDate { get; set; }
    public int DaysUntilExpiry { get; set; }
    public string DocumentNumber { get; set; } = string.Empty;
    public string IssuingAuthority { get; set; } = string.Empty;
    public DateTime? LastReminderSent { get; set; }
    public Dictionary<string, object> DocumentDetails { get; set; } = new();
}

/// <summary>
/// Result of processing compliance reminders
/// </summary>
public class ComplianceReminderProcessingResult
{
    public Guid TransportCompanyId { get; set; }
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ProcessedAt { get; set; }
    public int TotalRemindersProcessed { get; set; }
    public List<ComplianceReminderInfo> ProcessedReminders { get; set; } = new();
    public TimeSpan ProcessingDuration { get; set; }
    public Dictionary<string, object> ProcessingMetadata { get; set; } = new();
}

/// <summary>
/// Information about a processed compliance reminder
/// </summary>
public class ComplianceReminderInfo
{
    public string DocumentType { get; set; } = string.Empty;
    public string EntityType { get; set; } = string.Empty;
    public Guid? EntityId { get; set; }
    public string EntityName { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
    public int DocumentCount { get; set; } = 1;
    public bool IsGrouped { get; set; }
    public DateTime SentAt { get; set; }
    public Guid AlertId { get; set; }
    public List<NotificationChannel> ChannelsUsed { get; set; } = new();
    public bool RequiresAcknowledgment { get; set; }
    public DateTime? AcknowledgedAt { get; set; }
    public Dictionary<string, object> ReminderMetadata { get; set; } = new();
}

/// <summary>
/// Request to get compliance reminder status
/// </summary>
public class GetComplianceReminderStatusRequest
{
    public Guid TransportCompanyId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string> DocumentTypes { get; set; } = new();
    public List<string> EntityTypes { get; set; } = new();
    public bool IncludeAcknowledged { get; set; } = true;
    public bool IncludePending { get; set; } = true;
    public int PageSize { get; set; } = 50;
    public int PageNumber { get; set; } = 1;
}

/// <summary>
/// Response containing compliance reminder status
/// </summary>
public class ComplianceReminderStatusResponse
{
    public Guid TransportCompanyId { get; set; }
    public DateTime GeneratedAt { get; set; }
    public ComplianceReminderSummary Summary { get; set; } = new();
    public List<ComplianceReminderInfo> Reminders { get; set; } = new();
    public List<ExpiringDocumentInfo> UpcomingExpirations { get; set; } = new();
    public PaginationInfo Pagination { get; set; } = new();
}

/// <summary>
/// Summary of compliance reminder status
/// </summary>
public class ComplianceReminderSummary
{
    public int TotalReminders { get; set; }
    public int PendingAcknowledgments { get; set; }
    public int AcknowledgedReminders { get; set; }
    public int CriticalExpirations { get; set; } // Expiring in 3 days or less
    public int WarningExpirations { get; set; } // Expiring in 7 days or less
    public int NoticeExpirations { get; set; } // Expiring in 30 days or less
    public int ExpiredDocuments { get; set; }
    public DateTime? LastReminderSent { get; set; }
    public DateTime? NextScheduledReminder { get; set; }
    public Dictionary<string, int> RemindersByDocumentType { get; set; } = new();
    public Dictionary<string, int> RemindersByEntityType { get; set; } = new();
}

/// <summary>
/// Pagination information
/// </summary>
public class PaginationInfo
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalItems { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
}

/// <summary>
/// Request to acknowledge compliance reminders
/// </summary>
public class AcknowledgeComplianceRemindersRequest
{
    public Guid TransportCompanyId { get; set; }
    public List<Guid> AlertIds { get; set; } = new();
    public string? AcknowledgmentNote { get; set; }
    public DateTime AcknowledgedAt { get; set; } = DateTime.UtcNow;
    public Guid AcknowledgedByUserId { get; set; }
}

/// <summary>
/// Response for acknowledging compliance reminders
/// </summary>
public class AcknowledgeComplianceRemindersResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public int AcknowledgedCount { get; set; }
    public int FailedCount { get; set; }
    public List<string> FailedAlertIds { get; set; } = new();
    public DateTime ProcessedAt { get; set; }
}

/// <summary>
/// Request to update compliance reminder configuration
/// </summary>
public class UpdateComplianceReminderConfigurationRequest
{
    public Guid TransportCompanyId { get; set; }
    public bool IsActive { get; set; } = true;
    public List<NotificationChannel> PreferredChannels { get; set; } = new();
    public string TimeZone { get; set; } = "Asia/Kolkata";
    public TimeSpan PreferredDeliveryTime { get; set; } = new(9, 0, 0);
    public bool GroupSimilarReminders { get; set; } = true;
    public int MaxRemindersPerDay { get; set; } = 5;
    public List<ComplianceReminderConfiguration> ReminderConfigurations { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Response for updating compliance reminder configuration
/// </summary>
public class UpdateComplianceReminderConfigurationResponse
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public Guid ConfigurationId { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<Guid> UpdatedJobIds { get; set; } = new();
    public Dictionary<string, object> ConfigurationSummary { get; set; } = new();
}

/// <summary>
/// Interface for external services used by compliance reminder system
/// </summary>
public interface IUserManagementService
{
    Task<List<ExpiringDocumentInfo>> GetExpiringCompanyDocumentsAsync(
        Guid transportCompanyId,
        List<int> reminderDays,
        CancellationToken cancellationToken = default);

    Task<string> GetCompanyNameAsync(Guid companyId, CancellationToken cancellationToken = default);
}

public interface INetworkFleetService
{
    Task<List<ExpiringDocumentInfo>> GetExpiringFleetDocumentsAsync(
        Guid transportCompanyId,
        List<int> reminderDays,
        CancellationToken cancellationToken = default);

    Task<string> GetVehicleNameAsync(Guid vehicleId, CancellationToken cancellationToken = default);
    Task<string> GetDriverNameAsync(Guid driverId, CancellationToken cancellationToken = default);
}
