using CommunicationNotification.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// In-memory audit repository implementation for development/testing
/// In production, this would use a proper database with audit-specific optimizations
/// </summary>
public class InMemoryAuditRepository : IAuditRepository
{
    private readonly ConcurrentDictionary<Guid, AuditRecord> _auditRecords = new();
    private readonly ConcurrentDictionary<Guid, MessageArchive> _messageArchives = new();
    private readonly ILogger<InMemoryAuditRepository> _logger;

    public InMemoryAuditRepository(ILogger<InMemoryAuditRepository> logger)
    {
        _logger = logger;
    }

    public async Task AddAsync(AuditRecord record, CancellationToken cancellationToken = default)
    {
        try
        {
            _auditRecords.TryAdd(record.Id, record);
            _logger.LogDebug("Added audit record {RecordId} for event {EventType}", record.Id, record.EventType);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add audit record {RecordId}", record.Id);
            throw;
        }
    }

    public async Task<List<AuditRecord>> GetAuditTrailAsync(AuditTrailQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            var records = _auditRecords.Values.AsQueryable();

            // Apply filters
            if (query.UserId.HasValue)
                records = records.Where(r => r.UserId == query.UserId.Value);

            if (query.MessageId.HasValue)
                records = records.Where(r => r.MessageId == query.MessageId.Value);

            if (query.ConversationId.HasValue)
                records = records.Where(r => r.ConversationId == query.ConversationId.Value);

            if (query.EventType.HasValue)
                records = records.Where(r => r.EventType == query.EventType.Value);

            if (query.Channel.HasValue)
                records = records.Where(r => r.Channel == query.Channel.Value);

            if (query.FromDate.HasValue)
                records = records.Where(r => r.Timestamp >= query.FromDate.Value);

            if (query.ToDate.HasValue)
                records = records.Where(r => r.Timestamp <= query.ToDate.Value);

            if (query.MinSeverity.HasValue)
                records = records.Where(r => r.Severity >= query.MinSeverity.Value);

            if (query.ComplianceFlags.HasValue)
                records = records.Where(r => r.ComplianceFlags.HasFlag(query.ComplianceFlags.Value));

            if (!string.IsNullOrEmpty(query.SearchTerm))
            {
                records = records.Where(r => 
                    r.EventData.Contains(query.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                    r.EventType.ToString().Contains(query.SearchTerm, StringComparison.OrdinalIgnoreCase));
            }

            // Apply pagination
            var totalRecords = records.Count();
            var pagedRecords = records
                .OrderByDescending(r => r.Timestamp)
                .Skip((query.PageNumber - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToList();

            _logger.LogDebug("Retrieved {Count} audit records from {Total} total for query", 
                pagedRecords.Count, totalRecords);

            return await Task.FromResult(pagedRecords);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get audit trail");
            throw;
        }
    }

    public async Task AddArchiveAsync(MessageArchive archive, CancellationToken cancellationToken = default)
    {
        try
        {
            _messageArchives.TryAdd(archive.Id, archive);
            _logger.LogDebug("Added message archive {ArchiveId} for message {MessageId}", archive.Id, archive.MessageId);
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add message archive {ArchiveId}", archive.Id);
            throw;
        }
    }

    public async Task<MessageArchive?> GetMessageArchiveAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var archive = _messageArchives.Values.FirstOrDefault(a => a.MessageId == messageId);
            _logger.LogDebug("Retrieved message archive for message {MessageId}: {Found}", 
                messageId, archive != null ? "Found" : "Not Found");
            return await Task.FromResult(archive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message archive for message {MessageId}", messageId);
            throw;
        }
    }

    public async Task<List<AuditRecord>> GetRecordsForRetentionAsync(DataRetentionPolicy policy, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow - policy.RetentionPeriod;
            var records = _auditRecords.Values.AsQueryable();

            // Filter by applicable event types
            if (policy.ApplicableEventTypes.Any())
            {
                records = records.Where(r => policy.ApplicableEventTypes.Contains(r.EventType));
            }

            // Filter by applicable compliance flags
            if (policy.ApplicableFlags.Any())
            {
                records = records.Where(r => policy.ApplicableFlags.Any(flag => r.ComplianceFlags.HasFlag(flag)));
            }

            // Filter by retention period
            records = records.Where(r => r.CreatedAt <= cutoffDate);

            // Exclude already deleted records
            records = records.Where(r => r.DeletedAt == null);

            var eligibleRecords = records.ToList();

            _logger.LogDebug("Found {Count} records eligible for retention policy {PolicyId}", 
                eligibleRecords.Count, policy.Id);

            return await Task.FromResult(eligibleRecords);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get records for retention policy {PolicyId}", policy.Id);
            throw;
        }
    }

    public async Task DeleteAsync(Guid recordId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_auditRecords.TryGetValue(recordId, out var record))
            {
                // Mark as deleted instead of actually removing (for audit trail)
                record.DeletedAt = DateTime.UtcNow;
                _logger.LogDebug("Marked audit record {RecordId} as deleted", recordId);
            }
            else
            {
                _logger.LogWarning("Attempted to delete non-existent audit record {RecordId}", recordId);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete audit record {RecordId}", recordId);
            throw;
        }
    }

    // Additional methods for advanced querying and analytics

    public async Task<Dictionary<AuditEventType, int>> GetEventTypeStatisticsAsync(
        DateTime fromDate, 
        DateTime toDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = _auditRecords.Values
                .Where(r => r.Timestamp >= fromDate && r.Timestamp <= toDate && r.DeletedAt == null)
                .GroupBy(r => r.EventType)
                .ToDictionary(g => g.Key, g => g.Count());

            _logger.LogDebug("Generated event type statistics for period {FromDate} to {ToDate}", fromDate, toDate);

            return await Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get event type statistics");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetChannelStatisticsAsync(
        DateTime fromDate, 
        DateTime toDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = _auditRecords.Values
                .Where(r => r.Timestamp >= fromDate && r.Timestamp <= toDate && r.DeletedAt == null && r.Channel.HasValue)
                .GroupBy(r => r.Channel!.Value.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            _logger.LogDebug("Generated channel statistics for period {FromDate} to {ToDate}", fromDate, toDate);

            return await Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get channel statistics");
            throw;
        }
    }

    public async Task<List<AuditRecord>> GetSecurityEventsAsync(
        DateTime fromDate, 
        DateTime toDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var securityEventTypes = new[]
            {
                AuditEventType.UserLogin,
                AuditEventType.UserLogout,
                AuditEventType.DataExport,
                AuditEventType.DataDeletion
            };

            var securityEvents = _auditRecords.Values
                .Where(r => r.Timestamp >= fromDate && 
                           r.Timestamp <= toDate && 
                           r.DeletedAt == null &&
                           (securityEventTypes.Contains(r.EventType) || r.Severity >= AuditSeverity.High))
                .OrderByDescending(r => r.Timestamp)
                .ToList();

            _logger.LogDebug("Retrieved {Count} security events for period {FromDate} to {ToDate}", 
                securityEvents.Count, fromDate, toDate);

            return await Task.FromResult(securityEvents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get security events");
            throw;
        }
    }

    public async Task<List<AuditRecord>> GetComplianceEventsAsync(
        ComplianceFlag complianceFlag, 
        DateTime fromDate, 
        DateTime toDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var complianceEvents = _auditRecords.Values
                .Where(r => r.Timestamp >= fromDate && 
                           r.Timestamp <= toDate && 
                           r.DeletedAt == null &&
                           r.ComplianceFlags.HasFlag(complianceFlag))
                .OrderByDescending(r => r.Timestamp)
                .ToList();

            _logger.LogDebug("Retrieved {Count} compliance events for flag {Flag} and period {FromDate} to {ToDate}", 
                complianceEvents.Count, complianceFlag, fromDate, toDate);

            return await Task.FromResult(complianceEvents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compliance events for flag {Flag}", complianceFlag);
            throw;
        }
    }

    public async Task<int> GetTotalRecordCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var count = _auditRecords.Values.Count(r => r.DeletedAt == null);
            return await Task.FromResult(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get total record count");
            throw;
        }
    }

    public async Task<long> GetStorageSizeEstimateAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Rough estimate of storage size in bytes
            var auditRecordSize = _auditRecords.Values
                .Where(r => r.DeletedAt == null)
                .Sum(r => EstimateRecordSize(r));

            var archiveSize = _messageArchives.Values
                .Sum(a => EstimateArchiveSize(a));

            var totalSize = auditRecordSize + archiveSize;

            _logger.LogDebug("Estimated storage size: {Size} bytes ({AuditSize} audit + {ArchiveSize} archive)", 
                totalSize, auditRecordSize, archiveSize);

            return await Task.FromResult(totalSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get storage size estimate");
            throw;
        }
    }

    private static long EstimateRecordSize(AuditRecord record)
    {
        // Rough estimation of record size in bytes
        return 200 + // Base record overhead
               (record.EventData?.Length ?? 0) +
               (record.IpAddress?.Length ?? 0) +
               (record.UserAgent?.Length ?? 0) +
               (record.SessionId?.Length ?? 0) +
               (record.CorrelationId?.Length ?? 0);
    }

    private static long EstimateArchiveSize(MessageArchive archive)
    {
        // Rough estimation of archive size in bytes
        return 150 + // Base archive overhead
               archive.OriginalContent.Length +
               archive.Metadata.Sum(kvp => kvp.Key.Length + kvp.Value.Length);
    }
}
