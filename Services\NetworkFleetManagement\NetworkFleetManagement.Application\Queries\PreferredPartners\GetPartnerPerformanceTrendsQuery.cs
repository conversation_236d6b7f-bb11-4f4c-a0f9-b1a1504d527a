using MediatR;
using NetworkFleetManagement.Application.DTOs;

namespace NetworkFleetManagement.Application.Queries.PreferredPartners;

public class GetPartnerPerformanceTrendsQuery : IRequest<List<PartnerPerformanceTrendDto>>
{
    public Guid PreferredPartnerId { get; set; }
    public Guid UserId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string Period { get; set; } = "monthly"; // daily, weekly, monthly
}
