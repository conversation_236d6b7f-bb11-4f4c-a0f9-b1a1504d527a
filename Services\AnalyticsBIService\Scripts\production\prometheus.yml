# Prometheus configuration for Analytics & BI Service monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'tli-analytics-prod'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Analytics & BI Service API
  - job_name: 'analytics-api'
    static_configs:
      - targets: ['analytics-api:80']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: analytics-api:80

  # TimescaleDB/PostgreSQL
  - job_name: 'timescaledb'
    static_configs:
      - targets: ['timescaledb:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    params:
      target: ['timescaledb:5432']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: postgres-exporter:9187

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    params:
      target: ['redis:6379']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: redis-exporter:9121

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: /nginx_status
    params:
      format: ['prometheus']

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # cAdvisor (container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Custom application metrics
  - job_name: 'analytics-custom-metrics'
    static_configs:
      - targets: ['analytics-api:80']
    scrape_interval: 15s
    metrics_path: /api/metrics/prometheus
    honor_labels: true
    params:
      format: ['prometheus']

# Remote write configuration (for long-term storage)
remote_write:
  - url: "https://prometheus-remote-write.tli-platform.com/api/v1/write"
    basic_auth:
      username: "analytics-service"
      password: "${PROMETHEUS_REMOTE_WRITE_PASSWORD}"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "https://prometheus-remote-read.tli-platform.com/api/v1/read"
    basic_auth:
      username: "analytics-service"
      password: "${PROMETHEUS_REMOTE_READ_PASSWORD}"

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Web configuration
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true
  max-connections: 512
  read-timeout: 30s

# Tracing configuration
tracing:
  endpoint: "jaeger:14268/api/traces"
  sampling_fraction: 0.1
