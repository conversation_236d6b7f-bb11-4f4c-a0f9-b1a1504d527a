using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Identity.Application.Common.Models;
using Identity.Domain.Repositories;
using MediatR;

namespace Identity.Application.Auth.Queries.GetUserById
{
    public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, UserDto>
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IMapper _mapper;

        public GetUserByIdQueryHandler(
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            IMapper mapper)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _mapper = mapper;
        }

        public async Task<UserDto> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
        {
            var user = await _userRepository.GetByIdAsync(request.UserId);
            if (user == null)
            {
                throw new ApplicationException("User not found");
            }

            var userDto = _mapper.Map<UserDto>(user);

            // Get user roles
            var roles = await _roleRepository.GetByUserIdAsync(user.Id);
            userDto.Roles = roles.Select(r => r.Name).ToList();

            return userDto;
        }
    }
}
