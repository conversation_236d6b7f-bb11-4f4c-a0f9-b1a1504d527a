using Microsoft.Extensions.Logging;
using Shared.Messaging;

namespace NetworkFleetManagement.Application.Services;

public interface IIntegrationEventPublisher
{
    Task PublishAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class;
    Task PublishAsync(string eventType, object eventData, CancellationToken cancellationToken = default);
}

public class IntegrationEventPublisher : IIntegrationEventPublisher
{
    private readonly IMessageBusPublisher _messageBusPublisher;
    private readonly ILogger<IntegrationEventPublisher> _logger;

    public IntegrationEventPublisher(
        IMessageBusPublisher messageBusPublisher,
        ILogger<IntegrationEventPublisher> logger)
    {
        _messageBusPublisher = messageBusPublisher;
        _logger = logger;
    }

    public async Task PublishAsync<T>(T integrationEvent, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            await _messageBusPublisher.PublishIntegrationEventAsync(integrationEvent, cancellationToken);

            _logger.LogInformation("Successfully published integration event of type {EventType}",
                typeof(T).Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish integration event of type {EventType}",
                typeof(T).Name);
            throw;
        }
    }

    public async Task PublishAsync(string eventType, object eventData, CancellationToken cancellationToken = default)
    {
        var integrationEvent = new
        {
            EventType = eventType,
            EventId = Guid.NewGuid(),
            Timestamp = DateTime.UtcNow,
            Source = "NetworkFleetManagement",
            Version = "1.0",
            Data = eventData
        };

        await PublishAsync(integrationEvent, cancellationToken);
    }
}
