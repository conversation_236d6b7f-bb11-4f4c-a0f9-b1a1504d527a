using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;

namespace OrderManagement.Infrastructure.BackgroundServices;

/// <summary>
/// Background service to monitor RFQ timeframes and handle automatic expiration
/// </summary>
public class RfqExpirationMonitorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RfqExpirationMonitorService> _logger;
    private readonly RfqExpirationOptions _options;
    private readonly TimeSpan _checkInterval;

    public RfqExpirationMonitorService(
        IServiceProvider serviceProvider,
        ILogger<RfqExpirationMonitorService> logger,
        IOptions<RfqExpirationOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
        _checkInterval = TimeSpan.FromMinutes(_options.CheckIntervalMinutes);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("RFQ Expiration Monitor Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessExpiringRfqsAsync(stoppingToken);
                await Task.Delay(_checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RFQ Expiration Monitor Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing expiring RFQs");
                // Wait a shorter interval before retrying on error
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("RFQ Expiration Monitor Service stopped");
    }

    private async Task ProcessExpiringRfqsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var rfqRepository = scope.ServiceProvider.GetRequiredService<IRfqRepository>();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        var messageBroker = scope.ServiceProvider.GetRequiredService<IMessageBroker>();

        try
        {
            // Get RFQs that have expired but are still active
            var expiredRfqs = await rfqRepository.GetExpiredActiveRfqsAsync(cancellationToken);

            foreach (var rfq in expiredRfqs)
            {
                try
                {
                    _logger.LogInformation("Processing expired RFQ {RfqId} - {RfqNumber}", rfq.Id, rfq.RfqNumber);

                    // Expire the RFQ
                    rfq.Expire();

                    rfqRepository.Update(rfq);

                    _logger.LogInformation("Expired RFQ {RfqId} - {RfqNumber}", rfq.Id, rfq.RfqNumber);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error expiring RFQ {RfqId}", rfq.Id);
                }
            }

            // Get RFQs approaching expiration for notifications
            var approachingExpirationRfqs = await rfqRepository.GetRfqsApproachingExpirationAsync(
                _options.NotificationHoursBeforeExpiration, cancellationToken);

            foreach (var rfq in approachingExpirationRfqs)
            {
                try
                {
                    _logger.LogInformation("Processing RFQ approaching expiration {RfqId} - {RfqNumber}",
                        rfq.Id, rfq.RfqNumber);

                    // Trigger approaching expiration domain event
                    rfq.TriggerApproachingExpirationNotification();

                    rfqRepository.Update(rfq);

                    _logger.LogInformation("Triggered approaching expiration notification for RFQ {RfqId}", rfq.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing approaching expiration for RFQ {RfqId}", rfq.Id);
                }
            }

            // Save all changes
            if (expiredRfqs.Any() || approachingExpirationRfqs.Any())
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Processed {ExpiredCount} expired RFQs and {ApproachingCount} approaching expiration",
                    expiredRfqs.Count(), approachingExpirationRfqs.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ProcessExpiringRfqsAsync");
            throw;
        }
    }
}

/// <summary>
/// Configuration options for RFQ expiration monitoring
/// </summary>
public class RfqExpirationOptions
{
    public const string SectionName = "RfqExpiration";

    /// <summary>
    /// Interval in minutes to check for expiring RFQs
    /// </summary>
    public int CheckIntervalMinutes { get; set; } = 15;

    /// <summary>
    /// Hours before expiration to send notification
    /// </summary>
    public int[] NotificationHoursBeforeExpiration { get; set; } = { 24, 12, 6, 1 };

    /// <summary>
    /// Default timeframe duration in hours for new RFQs
    /// </summary>
    public int DefaultTimeframeDurationHours { get; set; } = 72;

    /// <summary>
    /// Maximum number of extensions allowed per RFQ
    /// </summary>
    public int MaxExtensionsAllowed { get; set; } = 3;

    /// <summary>
    /// Whether to allow extensions by default
    /// </summary>
    public bool AllowExtensionsByDefault { get; set; } = true;
}
