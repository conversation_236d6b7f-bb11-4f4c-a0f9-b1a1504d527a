using Microsoft.Extensions.Logging;
using Moq;
using UserManagement.Application.Admin.Commands.ApproveUser;
using UserManagement.Application.Services;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using Shared.Messaging;
using Xunit;

namespace UserManagement.Tests.Unit.Application.Admin.Commands
{
    public class ApproveUserCommandHandlerTests
    {
        private readonly Mock<IUserProfileRepository> _mockUserProfileRepository;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly Mock<INotificationService> _mockNotificationService;
        private readonly Mock<ILogger<ApproveUserCommandHandler>> _mockLogger;
        private readonly ApproveUserCommandHandler _handler;

        public ApproveUserCommandHandlerTests()
        {
            _mockUserProfileRepository = new Mock<IUserProfileRepository>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _mockAuditService = new Mock<IAuditService>();
            _mockNotificationService = new Mock<INotificationService>();
            _mockLogger = new Mock<ILogger<ApproveUserCommandHandler>>();

            _handler = new ApproveUserCommandHandler(
                _mockUserProfileRepository.Object,
                _mockMessageBroker.Object,
                _mockAuditService.Object,
                _mockNotificationService.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ShouldApproveUserSuccessfully()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var approvedBy = Guid.NewGuid();
            var userProfile = new UserProfile(userId, UserType.Driver, "<EMAIL>");
            userProfile.UpdatePersonalDetails("John", "Doe", "**********");
            userProfile.UpdateAddress("123 Main St", "City", "State", "12345");
            userProfile.SetAadharNumber("**********12");
            userProfile.SetLicenseNumber("DL123456789");
            userProfile.SubmitForReview();

            var command = new ApproveUserCommand
            {
                UserId = userId,
                ApprovedBy = approvedBy,
                Notes = "Test approval"
            };

            _mockUserProfileRepository
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(userProfile);

            _mockUserProfileRepository
                .Setup(x => x.UpdateAsync(It.IsAny<UserProfile>()))
                .Returns(Task.CompletedTask);

            _mockMessageBroker
                .Setup(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal("User approved successfully", result.Message);
            Assert.Equal(ProfileStatus.Approved, userProfile.Status);
            Assert.Equal(approvedBy, userProfile.ApprovedBy);
            Assert.NotNull(userProfile.ApprovedAt);

            _mockUserProfileRepository.Verify(x => x.UpdateAsync(userProfile), Times.Once);
            _mockMessageBroker.Verify(x => x.PublishAsync("user.approved", It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_UserNotFound_ShouldReturnFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var approvedBy = Guid.NewGuid();

            var command = new ApproveUserCommand
            {
                UserId = userId,
                ApprovedBy = approvedBy,
                Notes = "Test approval"
            };

            _mockUserProfileRepository
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync((UserProfile?)null);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Equal("User profile not found", result.Message);

            _mockUserProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<UserProfile>()), Times.Never);
            _mockMessageBroker.Verify(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task Handle_UserNotUnderReview_ShouldReturnFailure()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var approvedBy = Guid.NewGuid();
            var userProfile = new UserProfile(userId, UserType.Driver, "<EMAIL>");
            // Don't submit for review, so status remains Incomplete

            var command = new ApproveUserCommand
            {
                UserId = userId,
                ApprovedBy = approvedBy,
                Notes = "Test approval"
            };

            _mockUserProfileRepository
                .Setup(x => x.GetByUserIdAsync(userId))
                .ReturnsAsync(userProfile);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("must be under review", result.Message);

            _mockUserProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<UserProfile>()), Times.Never);
            _mockMessageBroker.Verify(x => x.PublishAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }
    }
}
