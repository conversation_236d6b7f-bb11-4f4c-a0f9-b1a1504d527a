# Mobile & Workflow Service API Documentation

## Overview

The Mobile & Workflow Service provides a comprehensive platform for managing mobile applications, workflows, forms, notifications, and device management. This API supports offline-first architecture, cross-platform compatibility, and enterprise-grade features.

## Base URL

```
https://api.mobileworkflow.com/v1
```

## Authentication

All API endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer <your-access-token>
```

## API Endpoints

### Health Check

#### GET /health
Returns the current health status of the service.

**Response:**
```json
{
  "checkedAt": "2024-01-15T10:30:00Z",
  "overallStatus": "Healthy",
  "checks": [
    {
      "name": "Database",
      "status": "Healthy",
      "responseTime": "00:00:00.045"
    }
  ]
}
```

### Workflow Management

#### POST /api/workflows
Creates a new workflow definition.

**Request Body:**
```json
{
  "name": "Order Processing Workflow",
  "description": "Handles order processing from submission to delivery",
  "workflowType": "Sequential",
  "isActive": true,
  "createdBy": "<EMAIL>"
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Order Processing Workflow",
  "description": "Handles order processing from submission to delivery",
  "workflowType": "Sequential",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00Z",
  "createdBy": "<EMAIL>"
}
```

#### POST /api/workflows/execute
Starts execution of a workflow.

**Request Body:**
```json
{
  "workflowDefinitionId": "123e4567-e89b-12d3-a456-426614174000",
  "executedBy": "<EMAIL>",
  "inputData": {
    "orderId": "ORD-001",
    "customerId": "CUST-123"
  }
}
```

#### GET /api/workflows/{id}/executions
Retrieves execution history for a workflow.

### Form Management

#### POST /api/forms
Creates a new form definition.

**Request Body:**
```json
{
  "name": "Customer Information Form",
  "description": "Collects customer details",
  "formType": "DataCollection",
  "isActive": true,
  "createdBy": "<EMAIL>"
}
```

#### POST /api/forms/submit
Submits form data.

**Request Body:**
```json
{
  "formDefinitionId": "123e4567-e89b-12d3-a456-426614174000",
  "submittedBy": "<EMAIL>",
  "formData": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>"
  }
}
```

### Push Notifications

#### POST /api/notifications/push
Sends a push notification.

**Request Body:**
```json
{
  "title": "Order Update",
  "body": "Your order has been shipped",
  "recipients": [
    {
      "type": "User",
      "value": "user-123"
    }
  ],
  "notificationData": {
    "orderId": "ORD-001",
    "trackingNumber": "TRK-123456"
  },
  "sentBy": "system"
}
```

#### GET /api/notifications/user/{userId}
Retrieves notifications for a specific user.

### Device Management

#### POST /api/devices/register
Registers a new device.

**Request Body:**
```json
{
  "deviceId": "device-123",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "platform": "iOS",
  "deviceModel": "iPhone 14",
  "osVersion": "16.0",
  "appVersion": "1.0.0",
  "deviceCapabilities": {
    "camera": true,
    "gps": true,
    "biometrics": true
  }
}
```

#### PUT /api/devices/{id}
Updates device information.

#### GET /api/devices/user/{userId}
Retrieves devices for a specific user.

### Analytics

#### POST /api/analytics/events
Records an analytics event.

**Request Body:**
```json
{
  "eventName": "form_submitted",
  "eventCategory": "User Interaction",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "deviceId": "device-123",
  "eventData": {
    "formId": "form-456",
    "completionTime": 120
  }
}
```

#### GET /api/analytics/summary
Retrieves analytics summary.

**Query Parameters:**
- `fromDate`: Start date (ISO 8601)
- `toDate`: End date (ISO 8601)
- `userId`: Filter by user ID
- `eventCategory`: Filter by event category

### Background Jobs

#### POST /api/jobs
Creates a background job.

**Request Body:**
```json
{
  "jobName": "data-export-job",
  "jobType": "DataExport",
  "jobData": {
    "exportType": "CSV",
    "dateRange": "last-30-days"
  },
  "scheduledFor": "2024-01-15T15:00:00Z",
  "createdBy": "<EMAIL>"
}
```

#### GET /api/jobs/{id}
Retrieves job status and details.

### Geofencing

#### POST /api/geofences
Creates a geofence.

**Request Body:**
```json
{
  "name": "Warehouse Geofence",
  "description": "Main warehouse location",
  "type": "Circular",
  "coordinates": {
    "latitude": 37.7749,
    "longitude": -122.4194
  },
  "radius": 100.0,
  "triggerType": "Enter",
  "createdBy": "<EMAIL>"
}
```

#### POST /api/geofences/location
Processes location update.

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "latitude": 37.7749,
  "longitude": -122.4194,
  "accuracy": 5.0,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### File Synchronization

#### POST /api/files/sync
Creates a file for synchronization.

**Request Body:**
```json
{
  "fileName": "document.pdf",
  "filePath": "/documents/document.pdf",
  "fileHash": "abc123def456",
  "fileSize": 2048,
  "mimeType": "application/pdf",
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "deviceId": "device-123"
}
```

#### GET /api/files/sync/user/{userId}
Retrieves sync files for a user.

### Monitoring

#### GET /api/monitoring/health
Retrieves system health information.

#### GET /api/monitoring/metrics
Retrieves system metrics.

#### POST /api/monitoring/alerts
Creates a monitoring alert.

## Error Handling

All API endpoints return standard HTTP status codes:

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

**Error Response Format:**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

## Rate Limiting

API requests are rate limited:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated requests

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## Pagination

List endpoints support pagination:

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20, max: 100)

**Response Headers:**
```http
X-Total-Count: 150
X-Page-Count: 8
```

## Webhooks

The service supports webhooks for real-time notifications:

### Webhook Events
- `workflow.execution.completed`
- `form.submission.received`
- `notification.delivered`
- `device.registered`
- `geofence.triggered`

### Webhook Payload
```json
{
  "eventType": "workflow.execution.completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "executionId": "123e4567-e89b-12d3-a456-426614174000",
    "workflowId": "456e7890-e89b-12d3-a456-426614174000",
    "status": "Completed"
  }
}
```

## SDK and Libraries

Official SDKs are available for:
- .NET
- JavaScript/TypeScript
- Swift (iOS)
- Kotlin (Android)
- React Native

## Support

For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.mobileworkflow.com
- Status Page: https://status.mobileworkflow.com
