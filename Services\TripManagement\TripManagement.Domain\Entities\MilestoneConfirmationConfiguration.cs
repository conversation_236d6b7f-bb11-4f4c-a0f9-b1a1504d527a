
using Shared.Domain.Common;
using TripManagement.Domain.ValueObjects;

namespace TripManagement.Domain.Entities;

public class MilestoneConfirmationConfigurationEntity : AggregateRoot
{
    public Guid TripId { get; private set; }
    public Guid? MilestoneId { get; private set; }
    public MilestoneConfirmationSettings Settings { get; private set; } = new();
    public bool IsActive { get; private set; }
    public Guid CreatedBy { get; private set; }
    public Guid? UpdatedBy { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; } = new();

    // Parameterless constructor for EF Core
    private MilestoneConfirmationConfigurationEntity() { }

    public MilestoneConfirmationConfigurationEntity(
        Guid tripId,
        Guid? milestoneId,
        MilestoneConfirmationSettings settings,
        Guid? createdBy = null)
    {
        if (tripId == Guid.Empty)
            throw new ArgumentException("Trip ID cannot be empty", nameof(tripId));

        if (settings == null)
            throw new ArgumentNullException(nameof(settings));

        TripId = tripId;
        MilestoneId = milestoneId;
        Settings = settings;
        IsActive = true;
        CreatedBy = createdBy ?? Guid.Empty;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        Metadata = new Dictionary<string, object>();

        AddDomainEvent(new MilestoneConfirmationConfiguredEvent(Id, TripId, MilestoneId, CreatedAt));
    }

    public void UpdateSettings(MilestoneConfirmationSettings newSettings, Guid? updatedBy = null)
    {
        if (newSettings == null)
            throw new ArgumentNullException(nameof(newSettings));

        var oldSettings = Settings;
        Settings = newSettings;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new MilestoneConfirmationSettingsUpdatedEvent(Id, TripId, MilestoneId, UpdatedAt ?? DateTime.UtcNow));

        // Check for significant changes that might require notifications
        if (HasSignificantChanges(oldSettings, newSettings))
        {
            AddDomainEvent(new MilestoneConfirmationSignificantChangeEvent(Id, TripId, MilestoneId, GetChangeSummary(oldSettings, newSettings)));
        }
    }

    public void Activate()
    {
        if (!IsActive)
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new MilestoneConfirmationActivatedEvent(Id, TripId, MilestoneId));
        }
    }

    public void Deactivate()
    {
        if (IsActive)
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
            AddDomainEvent(new MilestoneConfirmationDeactivatedEvent(Id, TripId, MilestoneId));
        }
    }

    public void UpdateMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public bool RequiresConfirmation()
    {
        return IsActive && Settings.RequirePhotoConfirmation;
    }

    public bool RequiresSequenceValidation()
    {
        return IsActive && !Settings.EnableAutoConfirmation;
    }

    public bool AllowsOutOfSequence()
    {
        return IsActive && Settings.EnableAutoConfirmation;
    }

    public bool RequiresManagerApproval()
    {
        return IsActive && Settings.RequireSignature;
    }

    public ValidationRequirements GetValidationRequirements()
    {
        return new ValidationRequirements
        {
            RequireConfirmationPopup = Settings.RequirePhotoConfirmation,
            RequireDoubleConfirmation = Settings.RequireSignature,
            RequireReasonForCompletion = Settings.RequireOtpVerification,
            RequirePhotoEvidence = Settings.RequirePhotoConfirmation,
            RequireLocationVerification = Settings.RequireGpsLocation,
            RequireSignature = Settings.RequireSignature,
            RequireManagerApproval = Settings.RequireSignature,
            EnableSequenceValidation = !Settings.EnableAutoConfirmation,
            AllowOutOfSequenceCompletion = Settings.EnableAutoConfirmation,
            AllowSkipWithReason = Settings.EnableAutoConfirmation,
            RequiredFields = Settings.AllowedConfirmationMethods,
            CustomValidationRules = Settings.AllowedConfirmationMethods.ToDictionary(x => x, x => (object)x)
        };
    }

    private bool HasSignificantChanges(MilestoneConfirmationSettings oldSettings, MilestoneConfirmationSettings newSettings)
    {
        return oldSettings.RequirePhotoConfirmation != newSettings.RequirePhotoConfirmation ||
               oldSettings.RequireSignature != newSettings.RequireSignature ||
               oldSettings.EnableAutoConfirmation != newSettings.EnableAutoConfirmation ||
               oldSettings.RequireGpsLocation != newSettings.RequireGpsLocation ||
               oldSettings.RequireOtpVerification != newSettings.RequireOtpVerification ||
               oldSettings.MaxConfirmationTimeMinutes != newSettings.MaxConfirmationTimeMinutes;
    }

    private Dictionary<string, object> GetChangeSummary(MilestoneConfirmationSettings oldSettings, MilestoneConfirmationSettings newSettings)
    {
        var changes = new Dictionary<string, object>();

        if (oldSettings.RequirePhotoConfirmation != newSettings.RequirePhotoConfirmation)
            changes["RequirePhotoConfirmation"] = new { Old = oldSettings.RequirePhotoConfirmation, New = newSettings.RequirePhotoConfirmation };

        if (oldSettings.RequireSignature != newSettings.RequireSignature)
            changes["RequireSignature"] = new { Old = oldSettings.RequireSignature, New = newSettings.RequireSignature };

        if (oldSettings.EnableAutoConfirmation != newSettings.EnableAutoConfirmation)
            changes["EnableAutoConfirmation"] = new { Old = oldSettings.EnableAutoConfirmation, New = newSettings.EnableAutoConfirmation };

        return changes;
    }
}

public class ValidationRequirements
{
    public bool RequireConfirmationPopup { get; set; }
    public bool RequireDoubleConfirmation { get; set; }
    public bool RequireReasonForCompletion { get; set; }
    public bool RequirePhotoEvidence { get; set; }
    public bool RequireLocationVerification { get; set; }
    public bool RequireSignature { get; set; }
    public bool RequireManagerApproval { get; set; }
    public bool EnableSequenceValidation { get; set; }
    public bool AllowOutOfSequenceCompletion { get; set; }
    public bool AllowSkipWithReason { get; set; }
    public List<string> RequiredFields { get; set; } = new();
    public Dictionary<string, object> CustomValidationRules { get; set; } = new();
}

// Domain Events
public record MilestoneConfirmationConfiguredEvent(Guid ConfigurationId, Guid TripId, Guid? MilestoneId, DateTime ConfiguredAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneConfirmationSettingsUpdatedEvent(Guid ConfigurationId, Guid TripId, Guid? MilestoneId, DateTime UpdatedAt) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneConfirmationSignificantChangeEvent(Guid ConfigurationId, Guid TripId, Guid? MilestoneId, Dictionary<string, object> Changes) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneConfirmationActivatedEvent(Guid ConfigurationId, Guid TripId, Guid? MilestoneId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}

public record MilestoneConfirmationDeactivatedEvent(Guid ConfigurationId, Guid TripId, Guid? MilestoneId) : IDomainEvent
{
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}




