using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using MobileWorkflow.Domain.Entities;
using MobileWorkflow.Domain.Repositories;
using MobileWorkflow.Infrastructure.Services;
using Xunit;

namespace MobileWorkflow.Tests.Services;

public class DriverMobileAppServiceTests
{
    private readonly Mock<IMobileSessionRepository> _mockSessionRepository;
    private readonly Mock<IOfflineDataRepository> _mockOfflineDataRepository;
    private readonly Mock<ILogger<DriverMobileAppService>> _mockLogger;
    private readonly IMemoryCache _memoryCache;
    private readonly DriverMobileAppService _service;

    public DriverMobileAppServiceTests()
    {
        _mockSessionRepository = new Mock<IMobileSessionRepository>();
        _mockOfflineDataRepository = new Mock<IOfflineDataRepository>();
        _mockLogger = new Mock<ILogger<DriverMobileAppService>>();
        _memoryCache = new MemoryCache(new MemoryCacheOptions());
        
        _service = new DriverMobileAppService(
            _mockSessionRepository.Object,
            _mockOfflineDataRepository.Object,
            _memoryCache,
            _mockLogger.Object);
    }

    [Fact]
    public async Task AcceptTripAssignmentAsync_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var tripId = Guid.NewGuid();
        var session = CreateTestMobileSession(driverId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockOfflineDataRepository
            .Setup(x => x.AddAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.AcceptTripAssignmentAsync(driverId, tripId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.TripId.Should().Be(tripId);
        result.Status.Should().Be("Accepted");

        _mockOfflineDataRepository.Verify(
            x => x.AddAsync(It.Is<OfflineData>(d => d.DataType == "TripAssignment" && d.Action == "Accept"), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task AcceptTripAssignmentAsync_WithNoActiveSession_ShouldReturnFailure()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var tripId = Guid.NewGuid();

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession?)null);

        // Act
        var result = await _service.AcceptTripAssignmentAsync(driverId, tripId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Be("No active session found");
    }

    [Fact]
    public async Task UpdateTripStatusAsync_WithValidData_ShouldReturnTrue()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var tripId = Guid.NewGuid();
        var status = "InTransit";
        var statusData = new Dictionary<string, object> { { "location", "12.9716,77.5946" } };
        var session = CreateTestMobileSession(driverId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockOfflineDataRepository
            .Setup(x => x.AddAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.UpdateTripStatusAsync(driverId, tripId, status, statusData);

        // Assert
        result.Should().BeTrue();

        _mockOfflineDataRepository.Verify(
            x => x.AddAsync(It.Is<OfflineData>(d => d.DataType == "TripUpdate" && d.Action == "StatusUpdate"), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateDriverLocationAsync_WithValidData_ShouldReturnTrue()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var locationUpdate = new LocationUpdate
        {
            Latitude = 12.9716,
            Longitude = 77.5946,
            Accuracy = 5.0,
            Speed = 60.0,
            Heading = 180.0,
            Timestamp = DateTime.UtcNow,
            Address = "MG Road, Bangalore"
        };
        var session = CreateTestMobileSession(driverId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockOfflineDataRepository
            .Setup(x => x.AddAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.UpdateDriverLocationAsync(driverId, locationUpdate);

        // Assert
        result.Should().BeTrue();

        _mockOfflineDataRepository.Verify(
            x => x.AddAsync(It.Is<OfflineData>(d => d.DataType == "LocationUpdate"), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UploadPODAsync_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var tripId = Guid.NewGuid();
        var podData = new PODData
        {
            CustomerName = "John Doe",
            CustomerSignature = "base64signature",
            DeliveryPhotos = new List<string> { "photo1.jpg", "photo2.jpg" },
            DeliveryNotes = "Package delivered successfully",
            DeliveryTime = DateTime.UtcNow
        };
        var session = CreateTestMobileSession(driverId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockOfflineDataRepository
            .Setup(x => x.AddAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.UploadPODAsync(driverId, tripId, podData);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.TripId.Should().Be(tripId);

        _mockOfflineDataRepository.Verify(
            x => x.AddAsync(It.Is<OfflineData>(d => d.DataType == "PODUpload" && d.Priority == 1), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetEnhancedFeaturesAsync_WithBasicTier_ShouldReturnBasicFeatures()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var subscriptionTier = "Basic";

        // Act
        var result = await _service.GetEnhancedFeaturesAsync(driverId, subscriptionTier);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(3);
        result.Should().Contain(f => f.Name == "Basic Trip Management");
        result.Should().Contain(f => f.Name == "POD Upload");
        result.Should().Contain(f => f.Name == "Basic Navigation");
    }

    [Fact]
    public async Task GetEnhancedFeaturesAsync_WithProTier_ShouldReturnProFeatures()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var subscriptionTier = "Pro";

        // Act
        var result = await _service.GetEnhancedFeaturesAsync(driverId, subscriptionTier);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(7); // 3 basic + 4 pro features
        result.Should().Contain(f => f.Name == "Advanced Analytics");
        result.Should().Contain(f => f.Name == "Route Optimization");
        result.Should().Contain(f => f.Name == "Priority Support");
        result.Should().Contain(f => f.Name == "Bulk Trip Management");
    }

    [Fact]
    public async Task GetEnhancedFeaturesAsync_WithEnterpriseTier_ShouldReturnAllFeatures()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var subscriptionTier = "Enterprise";

        // Act
        var result = await _service.GetEnhancedFeaturesAsync(driverId, subscriptionTier);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(11); // 3 basic + 4 pro + 4 enterprise features
        result.Should().Contain(f => f.Name == "Fleet Management");
        result.Should().Contain(f => f.Name == "Custom Integrations");
        result.Should().Contain(f => f.Name == "Advanced Reporting");
        result.Should().Contain(f => f.Name == "White Label Options");
    }

    [Fact]
    public async Task RecordOfflineActionAsync_WithValidData_ShouldReturnTrue()
    {
        // Arrange
        var driverId = Guid.NewGuid();
        var action = new OfflineAction
        {
            ActionType = "TripStatusUpdate",
            Data = new Dictionary<string, object> { { "status", "completed" } },
            Timestamp = DateTime.UtcNow,
            Priority = 1
        };
        var session = CreateTestMobileSession(driverId);

        _mockSessionRepository
            .Setup(x => x.GetActiveSessionByUserIdAsync(driverId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        _mockOfflineDataRepository
            .Setup(x => x.AddAsync(It.IsAny<OfflineData>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OfflineData data, CancellationToken _) => data);

        _mockSessionRepository
            .Setup(x => x.UpdateAsync(It.IsAny<MobileSession>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((MobileSession s, CancellationToken _) => s);

        // Act
        var result = await _service.RecordOfflineActionAsync(driverId, action);

        // Assert
        result.Should().BeTrue();

        _mockOfflineDataRepository.Verify(
            x => x.AddAsync(It.Is<OfflineData>(d => d.DataType == "OfflineAction" && d.Action == "TripStatusUpdate"), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private static MobileSession CreateTestMobileSession(Guid userId)
    {
        return new MobileSession(
            userId,
            Guid.NewGuid(),
            "device123",
            "Test Device",
            "1.0.0",
            "Android",
            "11.0",
            true);
    }
}
