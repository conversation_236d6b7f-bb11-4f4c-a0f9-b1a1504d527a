# Docker Compose Override for Development Environment
# This file extends the base docker-compose.yml for development-specific configurations

version: '3.8'

services:
  # Analytics API with development overrides
  analytics-api:
    build:
      context: ../../
      dockerfile: Services/AnalyticsBIService/Dockerfile
      target: final
    image: tli/analytics-bi-service:dev
    container_name: tli-analytics-api-dev
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: http://+:8080

      # Development-specific settings
      Logging__LogLevel__Default: Debug
      Logging__LogLevel__AnalyticsBIService: Debug

      # Enable detailed errors for development
      DetailedErrors: true

      # Development database connection
      ConnectionStrings__DefaultConnection: "Host=timescaledb;Port=5432;Database=TLI_AnalyticsBI;User Id=timescale;Password=timescale_password;Include Error Detail=true;Pooling=true;MinPoolSize=1;MaxPoolSize=10;CommandTimeout=30"
      ConnectionStrings__TimescaleConnection: "Host=timescaledb;Port=5432;Database=TLI_AnalyticsBI_Timescale;User Id=timescale;Password=timescale_password;Include Error Detail=true;Pooling=true;MinPoolSize=1;MaxPoolSize=10;CommandTimeout=30"
      ConnectionStrings__Redis: "redis:6379,abortConnect=false"
      
      # JWT settings for development
      JwtSettings__SecretKey: "development_jwt_secret_key_for_testing_only_minimum_256_bits_long"
      JwtSettings__Issuer: "TLI.Analytics.Dev"
      JwtSettings__Audience: "TLI.Users.Dev"
      JwtSettings__ExpiryMinutes: "1440"  # 24 hours for development
      
      # Development CORS settings (more permissive)
      CORS__AllowedOrigins: "*"
      CORS__AllowCredentials: "true"
      
      # Development analytics settings
      AnalyticsSettings__DataRetentionDays: "30"
      AnalyticsSettings__MetricAggregationInterval: "00:05:00"
      AnalyticsSettings__EnableRealTimeAnalytics: "true"
      AnalyticsSettings__EnablePredictiveAnalytics: "false"
      AnalyticsSettings__CacheExpirationMinutes: "5"  # Shorter cache for development
      
      # Development file storage (local)
      FileStorage__Provider: "Local"
      FileStorage__LocalPath: "/app/data/exports"
      
      # Development monitoring
      Monitoring__Prometheus__Enabled: "true"
      
      # Development features
      Features__EnableSwagger: "true"
      Features__EnableDetailedErrors: "true"
      Features__EnableDeveloperExceptionPage: "true"
      
    ports:
      - "5014:8080"
      - "5015:443"  # HTTPS port for development
    volumes:
      # Mount source code for hot reload (if using development image)
      - ./AnalyticsBIService.API:/app/api:ro
      - ./logs:/app/logs
      - ./data:/app/data
      # Mount development certificates
      - ~/.aspnet/https:/https:ro
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - tli-analytics-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # TimescaleDB with development overrides
  timescaledb:
    environment:
      # Development database settings
      POSTGRES_DB: TLI_AnalyticsBI
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
      TIMESCALEDB_TELEMETRY: 'off'
      
      # Development-specific PostgreSQL settings
      POSTGRES_SHARED_BUFFERS: "128MB"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "512MB"
      POSTGRES_WORK_MEM: "4MB"
    volumes:
      # Development data persistence
      - timescaledb_dev_data:/var/lib/postgresql/data
      # Development initialization scripts
      - ./Scripts/development/init-dev-data.sql:/docker-entrypoint-initdb.d/03-init-dev-data.sql:ro
    ports:
      - "5432:5432"  # Expose port for development tools
    command: 
      - postgres
      - -c
      - log_statement=all  # Log all statements in development
      - -c
      - log_min_duration_statement=0  # Log all query durations
      - -c
      - shared_preload_libraries=timescaledb

  # Redis with development overrides
  redis:
    environment:
      # No password for development (not recommended for production)
      REDIS_PASSWORD: ""
    ports:
      - "6379:6379"  # Expose port for development tools
    volumes:
      - redis_dev_data:/data
      - ./Scripts/development/redis-dev.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf

  # Development-specific services
  
  # Swagger UI (standalone for API documentation)
  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: tli-analytics-swagger-dev
    environment:
      SWAGGER_JSON_URL: http://analytics-api/swagger/v1/swagger.json
      BASE_URL: /swagger
    ports:
      - "8080:8080"
    depends_on:
      - analytics-api
    networks:
      - tli-analytics-network
    restart: unless-stopped

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: tli-analytics-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8081:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
      - ./Scripts/development/pgadmin-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      - timescaledb
    networks:
      - tli-analytics-network
    restart: unless-stopped

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: tli-analytics-redis-commander-dev
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8082:8081"
    depends_on:
      - redis
    networks:
      - tli-analytics-network
    restart: unless-stopped

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: tli-analytics-mailhog-dev
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - tli-analytics-network
    restart: unless-stopped

  # Jaeger for distributed tracing (development)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: tli-analytics-jaeger-dev
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "6831:6831/udp"  # Jaeger agent UDP
    networks:
      - tli-analytics-network
    restart: unless-stopped

# Development-specific volumes
volumes:
  timescaledb_dev_data:
    driver: local
    name: tli_analytics_timescaledb_dev_data
  redis_dev_data:
    driver: local
    name: tli_analytics_redis_dev_data
  pgadmin_dev_data:
    driver: local
    name: tli_analytics_pgadmin_dev_data

# Development network configuration
networks:
  tli-analytics-network:
    driver: bridge
    name: tli-analytics-network-dev
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
