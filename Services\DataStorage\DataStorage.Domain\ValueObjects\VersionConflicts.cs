using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class MergeConflict
{
    public string ConflictId { get; private set; }
    public ConflictType Type { get; private set; }
    public string FilePath { get; private set; }
    public int LineNumber { get; private set; }
    public string SourceContent { get; private set; }
    public string TargetContent { get; private set; }
    public string? BaseContent { get; private set; }
    public ConflictSeverity Severity { get; private set; }
    public string? Description { get; private set; }
    public List<ConflictResolutionStrategy> SuggestedStrategies { get; private set; }

    public MergeConflict(
        string conflictId,
        ConflictType type,
        string filePath,
        int lineNumber,
        string sourceContent,
        string targetContent,
        ConflictSeverity severity,
        string? baseContent = null,
        string? description = null,
        List<ConflictResolutionStrategy>? suggestedStrategies = null)
    {
        ConflictId = conflictId ?? throw new ArgumentNullException(nameof(conflictId));
        Type = type;
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        LineNumber = lineNumber;
        SourceContent = sourceContent ?? throw new ArgumentNullException(nameof(sourceContent));
        TargetContent = targetContent ?? throw new ArgumentNullException(nameof(targetContent));
        BaseContent = baseContent;
        Severity = severity;
        Description = description;
        SuggestedStrategies = suggestedStrategies ?? new List<ConflictResolutionStrategy>();
    }
}

public class ConflictResolutionRequest
{
    public string ConflictId { get; private set; }
    public ConflictResolutionStrategy Strategy { get; private set; }
    public string? CustomResolution { get; private set; }
    public Guid ResolvedBy { get; private set; }
    public string? ResolutionComment { get; private set; }

    public ConflictResolutionRequest(
        string conflictId,
        ConflictResolutionStrategy strategy,
        Guid resolvedBy,
        string? customResolution = null,
        string? resolutionComment = null)
    {
        ConflictId = conflictId ?? throw new ArgumentNullException(nameof(conflictId));
        Strategy = strategy;
        CustomResolution = customResolution;
        ResolvedBy = resolvedBy;
        ResolutionComment = resolutionComment;
    }
}

public class ConflictResolutionResult
{
    public bool IsSuccessful { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string ConflictId { get; private set; }
    public ConflictResolutionStrategy Strategy { get; private set; }
    public string ResolvedContent { get; private set; }
    public Guid ResolvedBy { get; private set; }
    public DateTime ResolvedAt { get; private set; }

    private ConflictResolutionResult()
    {
        ResolvedContent = string.Empty;
    }

    public static ConflictResolutionResult Success(
        string conflictId,
        ConflictResolutionStrategy strategy,
        string resolvedContent,
        Guid resolvedBy)
    {
        return new ConflictResolutionResult
        {
            IsSuccessful = true,
            ConflictId = conflictId,
            Strategy = strategy,
            ResolvedContent = resolvedContent,
            ResolvedBy = resolvedBy,
            ResolvedAt = DateTime.UtcNow
        };
    }

    public static ConflictResolutionResult Failure(string conflictId, string errorMessage)
    {
        return new ConflictResolutionResult
        {
            IsSuccessful = false,
            ConflictId = conflictId,
            ErrorMessage = errorMessage,
            ResolvedAt = DateTime.UtcNow
        };
    }
}

public class ConflictResolutionStrategy
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ResolutionType Type { get; private set; }
    public bool RequiresManualInput { get; private set; }
    public Dictionary<string, object> Parameters { get; private set; }

    public ConflictResolutionStrategy(
        string name,
        string description,
        ResolutionType type,
        bool requiresManualInput = false,
        Dictionary<string, object>? parameters = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Type = type;
        RequiresManualInput = requiresManualInput;
        Parameters = parameters ?? new Dictionary<string, object>();
    }

    // Common strategies
    public static ConflictResolutionStrategy AcceptSource => new(
        "Accept Source",
        "Use the content from the source branch",
        ResolutionType.Automatic);

    public static ConflictResolutionStrategy AcceptTarget => new(
        "Accept Target",
        "Use the content from the target branch",
        ResolutionType.Automatic);

    public static ConflictResolutionStrategy AcceptBoth => new(
        "Accept Both",
        "Merge both changes together",
        ResolutionType.Automatic);

    public static ConflictResolutionStrategy Manual => new(
        "Manual Resolution",
        "Manually resolve the conflict",
        ResolutionType.Manual,
        requiresManualInput: true);
}

public class DocumentComparison
{
    public Guid DocumentId { get; private set; }
    public string Version1 { get; private set; }
    public string Version2 { get; private set; }
    public ComparisonSummary Summary { get; private set; }
    public List<ContentChange> Changes { get; private set; }
    public ComparisonMetadata Metadata { get; private set; }
    public DateTime ComparedAt { get; private set; }

    public DocumentComparison(
        Guid documentId,
        string version1,
        string version2,
        ComparisonSummary summary,
        List<ContentChange> changes,
        ComparisonMetadata metadata)
    {
        DocumentId = documentId;
        Version1 = version1 ?? throw new ArgumentNullException(nameof(version1));
        Version2 = version2 ?? throw new ArgumentNullException(nameof(version2));
        Summary = summary ?? throw new ArgumentNullException(nameof(summary));
        Changes = changes ?? new List<ContentChange>();
        Metadata = metadata ?? throw new ArgumentNullException(nameof(metadata));
        ComparedAt = DateTime.UtcNow;
    }
}

public class ComparisonSummary
{
    public int TotalChanges { get; private set; }
    public int LinesAdded { get; private set; }
    public int LinesDeleted { get; private set; }
    public int LinesModified { get; private set; }
    public int FilesChanged { get; private set; }
    public double SimilarityPercentage { get; private set; }

    public ComparisonSummary(
        int totalChanges,
        int linesAdded,
        int linesDeleted,
        int linesModified,
        int filesChanged,
        double similarityPercentage)
    {
        TotalChanges = totalChanges;
        LinesAdded = linesAdded;
        LinesDeleted = linesDeleted;
        LinesModified = linesModified;
        FilesChanged = filesChanged;
        SimilarityPercentage = Math.Max(0, Math.Min(100, similarityPercentage));
    }
}

public class ContentChange
{
    public ChangeType Type { get; private set; }
    public string FilePath { get; private set; }
    public int LineNumber { get; private set; }
    public string? OldContent { get; private set; }
    public string? NewContent { get; private set; }
    public string? Context { get; private set; }

    public ContentChange(
        ChangeType type,
        string filePath,
        int lineNumber,
        string? oldContent = null,
        string? newContent = null,
        string? context = null)
    {
        Type = type;
        FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
        LineNumber = lineNumber;
        OldContent = oldContent;
        NewContent = newContent;
        Context = context;
    }
}

public class ComparisonMetadata
{
    public long Version1Size { get; private set; }
    public long Version2Size { get; private set; }
    public DateTime Version1Date { get; private set; }
    public DateTime Version2Date { get; private set; }
    public Guid Version1Author { get; private set; }
    public Guid Version2Author { get; private set; }
    public TimeSpan ComparisonTime { get; private set; }

    public ComparisonMetadata(
        long version1Size,
        long version2Size,
        DateTime version1Date,
        DateTime version2Date,
        Guid version1Author,
        Guid version2Author,
        TimeSpan comparisonTime)
    {
        Version1Size = version1Size;
        Version2Size = version2Size;
        Version1Date = version1Date;
        Version2Date = version2Date;
        Version1Author = version1Author;
        Version2Author = version2Author;
        ComparisonTime = comparisonTime;
    }
}

public class DocumentDiff
{
    public Guid DocumentId { get; private set; }
    public string Version1 { get; private set; }
    public string Version2 { get; private set; }
    public List<DiffBlock> DiffBlocks { get; private set; }
    public DiffStatistics Statistics { get; private set; }
    public DateTime GeneratedAt { get; private set; }

    public DocumentDiff(
        Guid documentId,
        string version1,
        string version2,
        List<DiffBlock> diffBlocks,
        DiffStatistics statistics)
    {
        DocumentId = documentId;
        Version1 = version1 ?? throw new ArgumentNullException(nameof(version1));
        Version2 = version2 ?? throw new ArgumentNullException(nameof(version2));
        DiffBlocks = diffBlocks ?? new List<DiffBlock>();
        Statistics = statistics ?? throw new ArgumentNullException(nameof(statistics));
        GeneratedAt = DateTime.UtcNow;
    }
}

public class DiffBlock
{
    public DiffOperation Operation { get; private set; }
    public int StartLine { get; private set; }
    public int EndLine { get; private set; }
    public List<string> Content { get; private set; }
    public string? Context { get; private set; }

    public DiffBlock(
        DiffOperation operation,
        int startLine,
        int endLine,
        List<string> content,
        string? context = null)
    {
        Operation = operation;
        StartLine = startLine;
        EndLine = endLine;
        Content = content ?? new List<string>();
        Context = context;
    }
}

public class DiffStatistics
{
    public int TotalLines { get; private set; }
    public int AddedLines { get; private set; }
    public int DeletedLines { get; private set; }
    public int ModifiedLines { get; private set; }
    public int UnchangedLines { get; private set; }

    public DiffStatistics(
        int totalLines,
        int addedLines,
        int deletedLines,
        int modifiedLines,
        int unchangedLines)
    {
        TotalLines = totalLines;
        AddedLines = addedLines;
        DeletedLines = deletedLines;
        ModifiedLines = modifiedLines;
        UnchangedLines = unchangedLines;
    }
}
