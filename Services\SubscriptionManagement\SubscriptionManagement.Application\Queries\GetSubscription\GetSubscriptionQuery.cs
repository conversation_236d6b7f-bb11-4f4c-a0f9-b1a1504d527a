using MediatR;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Queries.GetSubscription;

public class GetSubscriptionQuery : IRequest<SubscriptionDto?>
{
    public Guid SubscriptionId { get; set; }
    public Guid? UserId { get; set; } // For authorization check
    
    public GetSubscriptionQuery(Guid subscriptionId, Guid? userId = null)
    {
        SubscriptionId = subscriptionId;
        UserId = userId;
    }
}
