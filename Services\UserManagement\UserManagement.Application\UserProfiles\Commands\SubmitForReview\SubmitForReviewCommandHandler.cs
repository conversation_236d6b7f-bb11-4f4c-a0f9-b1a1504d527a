using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Application.UserProfiles.Commands.SubmitForReview
{
    public class SubmitForReviewCommandHandler : IRequestHandler<SubmitForReviewCommand, bool>
    {
        private readonly IUserProfileRepository _userProfileRepository;
        private readonly ILogger<SubmitForReviewCommandHandler> _logger;

        public SubmitForReviewCommandHandler(
            IUserProfileRepository userProfileRepository,
            ILogger<SubmitForReviewCommandHandler> logger)
        {
            _userProfileRepository = userProfileRepository;
            _logger = logger;
        }

        public async Task<bool> Handle(SubmitForReviewCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Submitting profile {ProfileId} for review", request.Id);

                var userProfile = await _userProfileRepository.GetByIdAsync(request.Id);
                if (userProfile == null)
                {
                    throw new UserManagementDomainException("User profile not found");
                }

                // Submit for review using domain method
                userProfile.SubmitForReview();

                // Save changes
                await _userProfileRepository.UpdateAsync(userProfile);

                _logger.LogInformation("Successfully submitted profile {ProfileId} for review", request.Id);

                return true;
            }
            catch (UserManagementDomainException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while submitting profile {ProfileId} for review", request.Id);
                throw;
            }
        }
    }
}
