using DataStorage.Domain.Interfaces;
using DataStorage.Domain.ValueObjects;
using DataStorage.Domain.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace DataStorage.Infrastructure.Services;

public class CdnIntegrationService : ICdnIntegrationService
{
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<CdnIntegrationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ConcurrentDictionary<string, CdnEndpoint> _endpoints;
    private readonly ConcurrentDictionary<string, List<AccessRule>> _accessRules;

    public CdnIntegrationService(
        IFileStorageService fileStorageService,
        ILogger<CdnIntegrationService> logger,
        IConfiguration configuration)
    {
        _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _endpoints = new ConcurrentDictionary<string, CdnEndpoint>();
        _accessRules = new ConcurrentDictionary<string, List<AccessRule>>();
    }

    public async Task<CdnDeploymentResult> DeployToCdnAsync(StorageLocation sourceLocation, CdnDeploymentOptions options, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting CDN deployment for {Location} using {Provider}",
                sourceLocation.GetFullPath(), options.Provider);

            // Validate source location
            var fileMetadata = await _fileStorageService.GetFileMetadataAsync(sourceLocation, cancellationToken);

            // Create CDN endpoint configuration
            var endpointConfig = new CdnEndpointConfiguration(
                originUrl: sourceLocation.GetAccessUrl(),
                cachePolicy: options.CachePolicy,
                compressionOptions: options.CompressionOptions,
                securityOptions: options.SecurityOptions,
                allowedOrigins: options.AllowedOrigins,
                customHeaders: options.CustomHeaders);

            // Generate endpoint ID and URL
            var endpointId = Guid.NewGuid().ToString();
            var cdnUrl = GenerateCdnUrl(options.Provider, endpointId, options.CustomDomain);

            // Placeholder implementation for actual CDN deployment
            // In a real implementation, you would use provider-specific SDKs:
            // - Azure.ResourceManager.Cdn for Azure CDN
            // - Amazon.CloudFront for AWS CloudFront
            // - CloudFlare.NET for CloudFlare

            _logger.LogWarning("CDN deployment not fully implemented. Using placeholder logic for {Provider}", options.Provider);

            // Simulate deployment process
            await SimulateCdnDeploymentAsync(options.Provider, cancellationToken);

            // Create and store endpoint
            var endpoint = new CdnEndpoint(
                id: endpointId,
                name: $"endpoint-{Path.GetFileNameWithoutExtension(sourceLocation.FileName)}",
                url: cdnUrl,
                provider: options.Provider,
                status: CdnEndpointStatus.Running,
                configuration: endpointConfig,
                customDomain: options.CustomDomain);

            _endpoints.TryAdd(endpointId, endpoint);

            stopwatch.Stop();

            var result = CdnDeploymentResult.Success(
                cdnUrl,
                endpointId,
                options.Provider,
                stopwatch.Elapsed,
                new Dictionary<string, string>
                {
                    ["OriginalUrl"] = sourceLocation.GetAccessUrl(),
                    ["FileSize"] = fileMetadata.SizeBytes.ToString(),
                    ["ContentType"] = fileMetadata.ContentType
                });

            _logger.LogInformation("CDN deployment completed in {ElapsedMs}ms. CDN URL: {CdnUrl}",
                stopwatch.ElapsedMilliseconds, cdnUrl);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "CDN deployment failed for {Location}", sourceLocation.GetFullPath());
            return CdnDeploymentResult.Failure($"Deployment failed: {ex.Message}");
        }
    }

    public async Task<List<CdnEndpoint>> GetCdnEndpointsAsync(CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning

        return _endpoints.Values.ToList();
    }

    public async Task<CdnEndpoint> CreateCdnEndpointAsync(CdnEndpointConfiguration configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating CDN endpoint for origin {OriginUrl}", configuration.OriginUrl);

            var endpointId = Guid.NewGuid().ToString();
            var provider = DetermineProviderFromConfiguration();
            var cdnUrl = GenerateCdnUrl(provider, endpointId);

            // Placeholder implementation
            _logger.LogWarning("CDN endpoint creation not fully implemented");

            var endpoint = new CdnEndpoint(
                id: endpointId,
                name: $"endpoint-{DateTime.UtcNow:yyyyMMdd-HHmmss}",
                url: cdnUrl,
                provider: provider,
                status: CdnEndpointStatus.Creating,
                configuration: configuration);

            _endpoints.TryAdd(endpointId, endpoint);

            // Simulate endpoint creation
            await Task.Delay(1000, cancellationToken);

            // Update status to running
            var updatedEndpoint = new CdnEndpoint(
                endpoint.Id,
                endpoint.Name,
                endpoint.Url,
                endpoint.Provider,
                CdnEndpointStatus.Running,
                endpoint.Configuration,
                endpoint.CustomDomain,
                endpoint.CreatedAt,
                DateTime.UtcNow);

            _endpoints.TryUpdate(endpointId, updatedEndpoint, endpoint);

            _logger.LogInformation("CDN endpoint {EndpointId} created successfully", endpointId);
            return updatedEndpoint;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CDN endpoint");
            throw;
        }
    }

    public async Task DeleteCdnEndpointAsync(string endpointId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting CDN endpoint {EndpointId}", endpointId);

            if (!_endpoints.TryGetValue(endpointId, out var endpoint))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            // Placeholder implementation
            _logger.LogWarning("CDN endpoint deletion not fully implemented");

            // Simulate deletion
            await Task.Delay(500, cancellationToken);

            _endpoints.TryRemove(endpointId, out _);
            _accessRules.TryRemove(endpointId, out _);

            _logger.LogInformation("CDN endpoint {EndpointId} deleted successfully", endpointId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CDN endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<CacheInvalidationResult> InvalidateCacheAsync(List<string> paths, CdnProvider provider, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Invalidating cache for {PathCount} paths using {Provider}", paths.Count, provider);

            // Placeholder implementation
            _logger.LogWarning("Cache invalidation not fully implemented for {Provider}", provider);

            var invalidationId = Guid.NewGuid().ToString();

            // Simulate invalidation process
            await Task.Delay(200, cancellationToken);

            var result = CacheInvalidationResult.Success(
                invalidationId,
                paths,
                CacheInvalidationStatus.Completed);

            _logger.LogInformation("Cache invalidation {InvalidationId} completed for {PathCount} paths",
                invalidationId, paths.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache for {Provider}", provider);
            return CacheInvalidationResult.Failure($"Cache invalidation failed: {ex.Message}");
        }
    }

    public async Task<CacheInvalidationResult> InvalidateAllCacheAsync(CdnProvider provider, CancellationToken cancellationToken = default)
    {
        return await InvalidateCacheAsync(new List<string> { "/*" }, provider, cancellationToken);
    }

    public async Task<CacheStatistics> GetCacheStatisticsAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting cache statistics for endpoint {EndpointId} for period {Period}", endpointId, period);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            // Placeholder implementation with mock data
            _logger.LogWarning("Cache statistics not fully implemented. Returning mock data.");

            await Task.Delay(100, cancellationToken);

            var totalRequests = 10000L;
            var cacheHits = 8500L;
            var cacheMisses = 1500L;

            return new CacheStatistics(
                endpointId: endpointId,
                period: period,
                totalRequests: totalRequests,
                cacheHits: cacheHits,
                cacheMisses: cacheMisses,
                bytesServed: 50000000L,
                bytesFromCache: 42500000L,
                bytesFromOrigin: 7500000L,
                requestsByCountry: new Dictionary<string, long>
                {
                    ["US"] = 4000,
                    ["GB"] = 2000,
                    ["DE"] = 1500,
                    ["FR"] = 1200,
                    ["CA"] = 1000,
                    ["Others"] = 300
                },
                requestsByStatusCode: new Dictionary<string, long>
                {
                    ["200"] = 9500,
                    ["304"] = 400,
                    ["404"] = 80,
                    ["500"] = 20
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<CacheStatus> GetCacheStatusAsync(string path, CdnProvider provider, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting cache status for path {Path} using {Provider}", path, provider);

            // Placeholder implementation
            _logger.LogWarning("Cache status check not fully implemented for {Provider}", provider);

            await Task.Delay(50, cancellationToken);

            return new CacheStatus(
                path: path,
                provider: provider,
                isCached: true,
                cachedAt: DateTime.UtcNow.AddHours(-2),
                expiresAt: DateTime.UtcNow.AddHours(22),
                etag: $"\"{Guid.NewGuid():N}\"",
                contentLength: 1024000,
                contentType: "application/octet-stream");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache status for path {Path}", path);
            throw;
        }
    }

    public async Task<PerformanceAnalysis> AnalyzePerformanceAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing performance for endpoint {EndpointId} for period {Period}", endpointId, period);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            // Placeholder implementation with mock data
            _logger.LogWarning("Performance analysis not fully implemented. Returning mock data.");

            await Task.Delay(500, cancellationToken);

            var metricsByRegion = new List<PerformanceMetric>
            {
                new("Region", "North America", 150.5, "ms"),
                new("Region", "Europe", 180.2, "ms"),
                new("Region", "Asia Pacific", 220.8, "ms"),
                new("Region", "South America", 280.1, "ms")
            };

            var metricsByContentType = new List<PerformanceMetric>
            {
                new("ContentType", "text/html", 120.3, "ms"),
                new("ContentType", "image/jpeg", 95.7, "ms"),
                new("ContentType", "application/javascript", 110.2, "ms"),
                new("ContentType", "text/css", 85.4, "ms")
            };

            return new PerformanceAnalysis(
                endpointId: endpointId,
                period: period,
                averageResponseTime: 165.3,
                medianResponseTime: 145.2,
                p95ResponseTime: 320.5,
                p99ResponseTime: 450.8,
                throughput: 1250.5,
                errorRate: 0.02,
                metricsByRegion: metricsByRegion,
                metricsByContentType: metricsByContentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing performance for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<List<PerformanceRecommendation>> GetPerformanceRecommendationsAsync(string endpointId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting performance recommendations for endpoint {EndpointId}", endpointId);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            await Task.Delay(200, cancellationToken);

            var recommendations = new List<PerformanceRecommendation>
            {
                new(
                    PerformanceRecommendationType.EnableCompression,
                    "Enable Gzip Compression",
                    "Enable Gzip compression to reduce file sizes by up to 70%",
                    PerformanceImpact.High,
                    ImplementationComplexity.Low,
                    new Dictionary<string, object> { ["EstimatedSavings"] = "70%" }),

                new(
                    PerformanceRecommendationType.OptimizeCaching,
                    "Optimize Cache Headers",
                    "Set appropriate cache headers for static assets to improve cache hit ratio",
                    PerformanceImpact.Medium,
                    ImplementationComplexity.Medium,
                    new Dictionary<string, object> { ["CurrentHitRatio"] = "85%", ["TargetHitRatio"] = "95%" }),

                new(
                    PerformanceRecommendationType.ReduceImageSizes,
                    "Optimize Images",
                    "Compress and resize images to reduce bandwidth usage",
                    PerformanceImpact.Medium,
                    ImplementationComplexity.Medium,
                    new Dictionary<string, object> { ["EstimatedSavings"] = "40%" })
            };

            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance recommendations for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<CompressionResult> EnableCompressionAsync(string endpointId, CompressionOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Enabling compression for endpoint {EndpointId}", endpointId);

            if (!_endpoints.TryGetValue(endpointId, out var endpoint))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            // Placeholder implementation
            _logger.LogWarning("Compression configuration not fully implemented");

            await Task.Delay(300, cancellationToken);

            return CompressionResult.Success(
                appliedOptions: options,
                estimatedSavings: 65.5,
                affectedMimeTypes: options.CompressibleMimeTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling compression for endpoint {EndpointId}", endpointId);
            return CompressionResult.Failure($"Compression configuration failed: {ex.Message}");
        }
    }

    public async Task<List<EdgeLocation>> GetEdgeLocationsAsync(CdnProvider provider, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting edge locations for {Provider}", provider);

            await Task.Delay(100, cancellationToken);

            // Mock edge locations based on provider
            var locations = provider switch
            {
                CdnProvider.AzureCdn => GetAzureEdgeLocations(),
                CdnProvider.AmazonCloudFront => GetCloudFrontEdgeLocations(),
                CdnProvider.CloudFlare => GetCloudFlareEdgeLocations(),
                _ => new List<EdgeLocation>()
            };

            return locations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting edge locations for {Provider}", provider);
            throw;
        }
    }

    public async Task<GeographicDistribution> GetGeographicDistributionAsync(string endpointId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting geographic distribution for endpoint {EndpointId}", endpointId);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            await Task.Delay(200, cancellationToken);

            return new GeographicDistribution(
                endpointId: endpointId,
                period: period,
                requestsByCountry: new Dictionary<string, long>
                {
                    ["United States"] = 45000,
                    ["United Kingdom"] = 12000,
                    ["Germany"] = 8500,
                    ["France"] = 7200,
                    ["Canada"] = 6800,
                    ["Australia"] = 4500,
                    ["Japan"] = 3200,
                    ["Brazil"] = 2800
                },
                requestsByRegion: new Dictionary<string, long>
                {
                    ["North America"] = 51800,
                    ["Europe"] = 27700,
                    ["Asia Pacific"] = 7700,
                    ["South America"] = 2800
                },
                averageLatencyByCountry: new Dictionary<string, double>
                {
                    ["United States"] = 45.2,
                    ["United Kingdom"] = 38.5,
                    ["Germany"] = 42.1,
                    ["France"] = 41.8,
                    ["Canada"] = 52.3,
                    ["Australia"] = 180.5,
                    ["Japan"] = 165.2,
                    ["Brazil"] = 220.8
                },
                cacheHitRatioByCountry: new Dictionary<string, double>
                {
                    ["United States"] = 87.5,
                    ["United Kingdom"] = 89.2,
                    ["Germany"] = 85.8,
                    ["France"] = 86.1,
                    ["Canada"] = 88.9,
                    ["Australia"] = 82.3,
                    ["Japan"] = 84.7,
                    ["Brazil"] = 81.2
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting geographic distribution for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<LatencyAnalysis> AnalyzeLatencyAsync(string endpointId, List<string> testLocations, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing latency for endpoint {EndpointId} from {LocationCount} test locations",
                endpointId, testLocations.Count);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            var measurements = new List<LatencyMeasurement>();
            var random = new Random();

            foreach (var location in testLocations)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                // Simulate latency measurement
                await Task.Delay(50, cancellationToken);

                var latency = random.NextDouble() * 200 + 50; // 50-250ms
                measurements.Add(new LatencyMeasurement(location, latency, true));
            }

            return new LatencyAnalysis(endpointId, measurements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing latency for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<SecurityConfiguration> ConfigureSecurityAsync(string endpointId, SecurityOptions options, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Configuring security for endpoint {EndpointId}", endpointId);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            // Placeholder implementation
            _logger.LogWarning("Security configuration not fully implemented");

            await Task.Delay(200, cancellationToken);

            var securityHeaders = options.SecurityHeaders.Select(kvp => new SecurityHeader(kvp.Key, kvp.Value)).ToList();
            var accessRules = _accessRules.GetValueOrDefault(endpointId, new List<AccessRule>());

            return new SecurityConfiguration(
                endpointId: endpointId,
                isHttpsEnabled: options.EnableHttps,
                isHttpsRedirectEnabled: options.RedirectHttpToHttps,
                isHstsEnabled: options.EnableHsts,
                hstsMaxAge: options.HstsMaxAge,
                securityHeaders: securityHeaders,
                accessRules: accessRules);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring security for endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task<List<AccessRule>> GetAccessRulesAsync(string endpointId, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // Remove warning

        if (!_endpoints.ContainsKey(endpointId))
        {
            throw new ArgumentException($"Endpoint {endpointId} not found");
        }

        return _accessRules.GetValueOrDefault(endpointId, new List<AccessRule>());
    }

    public async Task AddAccessRuleAsync(string endpointId, AccessRule rule, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adding access rule {RuleName} to endpoint {EndpointId}", rule.Name, endpointId);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            await Task.Delay(100, cancellationToken);

            _accessRules.AddOrUpdate(endpointId,
                new List<AccessRule> { rule },
                (key, existingRules) =>
                {
                    existingRules.Add(rule);
                    return existingRules;
                });

            _logger.LogInformation("Access rule {RuleName} added to endpoint {EndpointId}", rule.Name, endpointId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding access rule to endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    public async Task RemoveAccessRuleAsync(string endpointId, string ruleId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Removing access rule {RuleId} from endpoint {EndpointId}", ruleId, endpointId);

            if (!_endpoints.ContainsKey(endpointId))
            {
                throw new ArgumentException($"Endpoint {endpointId} not found");
            }

            await Task.Delay(100, cancellationToken);

            if (_accessRules.TryGetValue(endpointId, out var rules))
            {
                var ruleToRemove = rules.FirstOrDefault(r => r.Id == ruleId);
                if (ruleToRemove != null)
                {
                    rules.Remove(ruleToRemove);
                    _logger.LogInformation("Access rule {RuleId} removed from endpoint {EndpointId}", ruleId, endpointId);
                }
                else
                {
                    _logger.LogWarning("Access rule {RuleId} not found in endpoint {EndpointId}", ruleId, endpointId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing access rule from endpoint {EndpointId}", endpointId);
            throw;
        }
    }

    // Private helper methods
    private async Task SimulateCdnDeploymentAsync(CdnProvider provider, CancellationToken cancellationToken)
    {
        var steps = new[]
        {
            "Validating configuration",
            "Creating endpoint",
            "Configuring cache rules",
            "Setting up security",
            "Propagating to edge locations"
        };

        foreach (var step in steps)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            _logger.LogDebug("CDN Deployment: {Step}", step);
            await Task.Delay(200, cancellationToken);
        }
    }

    private static string GenerateCdnUrl(CdnProvider provider, string endpointId, string? customDomain = null)
    {
        if (!string.IsNullOrEmpty(customDomain))
        {
            return $"https://{customDomain}";
        }

        return provider switch
        {
            CdnProvider.AzureCdn => $"https://{endpointId}.azureedge.net",
            CdnProvider.AmazonCloudFront => $"https://{endpointId}.cloudfront.net",
            CdnProvider.CloudFlare => $"https://{endpointId}.cloudflare.com",
            CdnProvider.GoogleCloudCdn => $"https://{endpointId}.googleusercontent.com",
            _ => $"https://{endpointId}.cdn.example.com"
        };
    }

    private CdnProvider DetermineProviderFromConfiguration()
    {
        var defaultProvider = _configuration.GetValue<string>("CDN:DefaultProvider");
        return defaultProvider?.ToLowerInvariant() switch
        {
            "azure" => CdnProvider.AzureCdn,
            "aws" => CdnProvider.AmazonCloudFront,
            "cloudflare" => CdnProvider.CloudFlare,
            "google" => CdnProvider.GoogleCloudCdn,
            _ => CdnProvider.AzureCdn
        };
    }

    private static List<EdgeLocation> GetAzureEdgeLocations()
    {
        return new List<EdgeLocation>
        {
            new("azure-us-east", "US East", "Ashburn", "United States", "North America", 39.0458, -77.5011, CdnProvider.AzureCdn),
            new("azure-us-west", "US West", "San Jose", "United States", "North America", 37.3382, -121.8863, CdnProvider.AzureCdn),
            new("azure-eu-west", "Europe West", "Amsterdam", "Netherlands", "Europe", 52.3676, 4.9041, CdnProvider.AzureCdn),
            new("azure-eu-north", "Europe North", "Dublin", "Ireland", "Europe", 53.3498, -6.2603, CdnProvider.AzureCdn),
            new("azure-asia-east", "Asia East", "Hong Kong", "Hong Kong", "Asia Pacific", 22.3193, 114.1694, CdnProvider.AzureCdn)
        };
    }

    private static List<EdgeLocation> GetCloudFrontEdgeLocations()
    {
        return new List<EdgeLocation>
        {
            new("cf-us-east-1", "US East (N. Virginia)", "Ashburn", "United States", "North America", 39.0458, -77.5011, CdnProvider.AmazonCloudFront),
            new("cf-us-west-1", "US West (N. California)", "San Francisco", "United States", "North America", 37.7749, -122.4194, CdnProvider.AmazonCloudFront),
            new("cf-eu-west-1", "Europe (Ireland)", "Dublin", "Ireland", "Europe", 53.3498, -6.2603, CdnProvider.AmazonCloudFront),
            new("cf-ap-southeast-1", "Asia Pacific (Singapore)", "Singapore", "Singapore", "Asia Pacific", 1.3521, 103.8198, CdnProvider.AmazonCloudFront),
            new("cf-ap-northeast-1", "Asia Pacific (Tokyo)", "Tokyo", "Japan", "Asia Pacific", 35.6762, 139.6503, CdnProvider.AmazonCloudFront)
        };
    }

    private static List<EdgeLocation> GetCloudFlareEdgeLocations()
    {
        return new List<EdgeLocation>
        {
            new("cf-lax", "Los Angeles", "Los Angeles", "United States", "North America", 34.0522, -118.2437, CdnProvider.CloudFlare),
            new("cf-lhr", "London", "London", "United Kingdom", "Europe", 51.5074, -0.1278, CdnProvider.CloudFlare),
            new("cf-fra", "Frankfurt", "Frankfurt", "Germany", "Europe", 50.1109, 8.6821, CdnProvider.CloudFlare),
            new("cf-sin", "Singapore", "Singapore", "Singapore", "Asia Pacific", 1.3521, 103.8198, CdnProvider.CloudFlare),
            new("cf-syd", "Sydney", "Sydney", "Australia", "Asia Pacific", -33.8688, 151.2093, CdnProvider.CloudFlare)
        };
    }
}
