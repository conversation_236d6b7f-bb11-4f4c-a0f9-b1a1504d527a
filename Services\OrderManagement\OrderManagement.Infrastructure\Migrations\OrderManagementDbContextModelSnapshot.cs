// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using OrderManagement.Domain.ValueObjects;
using OrderManagement.Infrastructure.Persistence;

#nullable disable

namespace OrderManagement.Infrastructure.Migrations
{
    [DbContext(typeof(OrderManagementDbContext))]
    partial class OrderManagementDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("order_management")
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("OrderManagement.Domain.Entities.BidDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("BidId")
                        .HasColumnType("uuid");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UploadedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BidId");

                    b.HasIndex("DocumentType");

                    b.HasIndex("UploadedAt");

                    b.ToTable("BidDocuments", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("PaidDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DueDate");

                    b.HasIndex("InvoiceDate");

                    b.HasIndex("InvoiceNumber")
                        .IsUnique();

                    b.HasIndex("OrderId");

                    b.HasIndex("Status");

                    b.HasIndex("Status", "DueDate");

                    b.ToTable("Invoices", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.InvoiceLineItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.ToTable("InvoiceLineItems", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AcceptedBidId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("BrokerId")
                        .HasColumnType("uuid");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("IsUrgent")
                        .HasColumnType("boolean");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("RfqId")
                        .HasColumnType("uuid");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("TransportCompanyId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AcceptedBidId");

                    b.HasIndex("BrokerId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("PaymentStatus");

                    b.HasIndex("RfqId");

                    b.HasIndex("Status");

                    b.HasIndex("TransportCompanyId");

                    b.HasIndex("BrokerId", "Status");

                    b.HasIndex("CarrierId", "Status");

                    b.HasIndex("Status", "CreatedAt");

                    b.HasIndex("TransportCompanyId", "Status");

                    b.ToTable("Orders", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.OrderDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UploadedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DocumentType");

                    b.HasIndex("OrderId");

                    b.HasIndex("UploadedAt");

                    b.ToTable("OrderDocuments", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.OrderStatusHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ChangedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("OrderId");

                    b.HasIndex("OrderId", "ChangedAt");

                    b.ToTable("OrderStatusHistories", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RFQ", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ClosedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClosureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsUrgent")
                        .HasColumnType("boolean");

                    b.Property<string>("RfqNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("TransportCompanyId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ExpiresAt");

                    b.HasIndex("RfqNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("TransportCompanyId");

                    b.HasIndex("Status", "CreatedAt");

                    b.HasIndex("TransportCompanyId", "Status");

                    b.ToTable("Rfqs", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RfqBid", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AcceptedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AdditionalServices")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("BidNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("BrokerId")
                        .HasColumnType("uuid");

                    b.Property<string>("DriverDetails")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("EstimatedDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EstimatedPickupDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsCounterOffer")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("OriginalBidId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProposedTerms")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("RfqId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VehicleDetails")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("BidNumber")
                        .IsUnique();

                    b.HasIndex("BrokerId");

                    b.HasIndex("RfqId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("BrokerId", "Status");

                    b.HasIndex("RfqId", "BrokerId");

                    b.HasIndex("RfqId", "Status");

                    b.ToTable("RfqBids", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RfqDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<Guid>("RfqId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UploadedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DocumentType");

                    b.HasIndex("RfqId");

                    b.HasIndex("UploadedAt");

                    b.ToTable("RfqDocuments", "order_management");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.BidDocument", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.RfqBid", "Bid")
                        .WithMany("Documents")
                        .HasForeignKey("BidId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bid");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Invoice", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.Order", "Order")
                        .WithMany("Invoices")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("Amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("Currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("Invoices", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.string", "string", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<string>("BillingContact")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingContact");

                            b1.Property<string>("BillingEmail")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingEmail");

                            b1.Property<string>("BillingMethod")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("BillingMethod");

                            b1.Property<string>("PaymentInstructions")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("PaymentInstructions");

                            b1.Property<int>("PaymentTermsDays")
                                .HasColumnType("integer")
                                .HasColumnName("PaymentTermsDays");

                            b1.Property<string>("PreferredInvoiceFormat")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("PreferredInvoiceFormat");

                            b1.Property<bool>("RequiresPurchaseOrder")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresPurchaseOrder");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("Invoices", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.string", "string", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CompanyName")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("InvoiceCustomerCompanyName");

                            b1.Property<string>("ContactEmail")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("InvoiceCustomerContactEmail");

                            b1.Property<string>("ContactPerson")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("InvoiceCustomerContactPerson");

                            b1.Property<string>("ContactPhone")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("InvoiceCustomerContactPhone");

                            b1.Property<string>("CustomerReference")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("InvoiceCustomerReference");

                            b1.Property<string>("PurchaseOrderNumber")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("InvoicePurchaseOrderNumber");

                            b1.Property<string>("TaxId")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("InvoiceCustomerTaxId");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("Invoices", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "BillingAddress", b2 =>
                                {
                                    b2.Property<Guid>("CustomerDetailsInvoiceId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("InvoiceBillingCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("InvoiceBillingContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("InvoiceBillingContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("InvoiceBillingCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("InvoiceBillingLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("InvoiceBillingLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("InvoiceBillingPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("InvoiceBillingSpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("InvoiceBillingState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("InvoiceBillingStreet");

                                    b2.HasKey("CustomerDetailsInvoiceId");

                                    b2.ToTable("Invoices", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("CustomerDetailsInvoiceId");
                                });

                            b1.Navigation("BillingAddress")
                                .IsRequired();
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "TaxAmount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TaxAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("TaxCurrency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("Invoices", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("TotalCurrency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("Invoices", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("string")
                        .IsRequired();

                    b.Navigation("string")
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("TaxAmount");

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.InvoiceLineItem", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.Invoice", null)
                        .WithMany("LineItems")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "TotalPrice", b1 =>
                        {
                            b1.Property<Guid>("InvoiceLineItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalPriceAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("TotalPriceCurrency");

                            b1.HasKey("InvoiceLineItemId");

                            b1.ToTable("InvoiceLineItems", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceLineItemId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("InvoiceLineItemId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("UnitPriceAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("UnitPriceCurrency");

                            b1.HasKey("InvoiceLineItemId");

                            b1.ToTable("InvoiceLineItems", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceLineItemId");
                        });

                    b.Navigation("TotalPrice")
                        .IsRequired();

                    b.Navigation("UnitPrice")
                        .IsRequired();
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Order", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.RfqBid", "AcceptedBid")
                        .WithMany()
                        .HasForeignKey("AcceptedBidId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("OrderManagement.Domain.Entities.RFQ", "Rfq")
                        .WithMany()
                        .HasForeignKey("RfqId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "AgreedPrice", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("AgreedAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("AgreedCurrency");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.string", "string", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("BillingContact")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingContact");

                            b1.Property<string>("BillingEmail")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingEmail");

                            b1.Property<string>("BillingMethod")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("BillingMethod");

                            b1.Property<string>("PaymentInstructions")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("PaymentInstructions");

                            b1.Property<int>("PaymentTermsDays")
                                .HasColumnType("integer")
                                .HasColumnName("PaymentTermsDays");

                            b1.Property<string>("PreferredInvoiceFormat")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("PreferredInvoiceFormat");

                            b1.Property<bool>("RequiresPurchaseOrder")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresPurchaseOrder");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.string", "string", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CompanyName")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("CustomerCompanyName");

                            b1.Property<string>("ContactEmail")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CustomerContactEmail");

                            b1.Property<string>("ContactPerson")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CustomerContactPerson");

                            b1.Property<string>("ContactPhone")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("CustomerContactPhone");

                            b1.Property<string>("CustomerReference")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CustomerReference");

                            b1.Property<string>("PurchaseOrderNumber")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PurchaseOrderNumber");

                            b1.Property<string>("TaxId")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("CustomerTaxId");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "BillingAddress", b2 =>
                                {
                                    b2.Property<Guid>("CustomerDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("CustomerBillingCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("CustomerBillingContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("CustomerBillingContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("CustomerBillingCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("CustomerBillingLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("CustomerBillingLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("CustomerBillingPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("CustomerBillingSpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("CustomerBillingState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("CustomerBillingStreet");

                                    b2.HasKey("CustomerDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("CustomerDetailsOrderId");
                                });

                            b1.Navigation("BillingAddress")
                                .IsRequired();
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.LoadDetails", "LoadDetails", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Description")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("LoadDescription");

                            b1.Property<string>("HazardousClassification")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("HazardousClassification");

                            b1.Property<bool>("IsHazardous")
                                .HasColumnType("boolean")
                                .HasColumnName("IsHazardous");

                            b1.Property<int>("LoadType")
                                .HasColumnType("integer")
                                .HasColumnName("LoadType");

                            b1.Property<string>("PackagingType")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PackagingType");

                            b1.Property<List<string>>("Photos")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("LoadPhotos");

                            b1.Property<int>("Quantity")
                                .HasColumnType("integer")
                                .HasColumnName("LoadQuantity");

                            b1.Property<int>("RequiredVehicleType")
                                .HasColumnType("integer")
                                .HasColumnName("RequiredVehicleType");

                            b1.Property<bool>("RequiresSpecialHandling")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresSpecialHandling");

                            b1.Property<bool>("RequiresTemperatureControl")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresTemperatureControl");

                            b1.Property<string>("SpecialHandlingInstructions")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("SpecialHandlingInstructions");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Dimensions", "Dimensions", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<decimal>("Height")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionHeight");

                                    b2.Property<decimal>("Length")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionLength");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasMaxLength(10)
                                        .HasColumnType("character varying(10)")
                                        .HasColumnName("DimensionUnit");

                                    b2.Property<decimal>("Width")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionWidth");

                                    b2.HasKey("LoadDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TemperatureRange", "TemperatureRange", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<decimal>("MaxTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("MaxTemperature");

                                    b2.Property<decimal>("MinTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("MinTemperature");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasMaxLength(1)
                                        .HasColumnType("character varying(1)")
                                        .HasColumnName("TemperatureUnit");

                                    b2.HasKey("LoadDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Volume", "Volume", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("VolumeUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(18, 3)
                                        .HasColumnType("numeric(18,3)")
                                        .HasColumnName("VolumeValue");

                                    b2.HasKey("LoadDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Weight", "Weight", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("WeightUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(18, 3)
                                        .HasColumnType("numeric(18,3)")
                                        .HasColumnName("WeightValue");

                                    b2.HasKey("LoadDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsOrderId");
                                });

                            b1.Navigation("Dimensions");

                            b1.Navigation("TemperatureRange");

                            b1.Navigation("Volume");

                            b1.Navigation("Weight")
                                .IsRequired();
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.RouteDetails", "RouteDetails", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<TimeSpan?>("EstimatedDuration")
                                .HasColumnType("interval")
                                .HasColumnName("EstimatedDuration");

                            b1.Property<List<Address>>("IntermediateStops")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("IntermediateStops");

                            b1.Property<bool>("IsFlexibleDelivery")
                                .HasColumnType("boolean")
                                .HasColumnName("IsFlexibleDelivery");

                            b1.Property<bool>("IsFlexiblePickup")
                                .HasColumnType("boolean")
                                .HasColumnName("IsFlexiblePickup");

                            b1.Property<DateTime>("PreferredDeliveryDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("PreferredDeliveryDate");

                            b1.Property<DateTime>("PreferredPickupDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("PreferredPickupDate");

                            b1.Property<string>("RouteNotes")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("RouteNotes");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "DeliveryAddress", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("DeliveryContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("DeliveryLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("DeliveryLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("DeliveryPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("DeliverySpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("DeliveryStreet");

                                    b2.HasKey("RouteDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TimeWindow", "DeliveryTimeWindow", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<TimeSpan>("EndTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("DeliveryTimeWindowEnd");

                                    b2.Property<TimeSpan>("StartTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("DeliveryTimeWindowStart");

                                    b2.HasKey("RouteDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Distance", "EstimatedDistance", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("EstimatedDistanceUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(10, 2)
                                        .HasColumnType("numeric(10,2)")
                                        .HasColumnName("EstimatedDistanceValue");

                                    b2.HasKey("RouteDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "PickupAddress", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("PickupContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("PickupLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("PickupLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("PickupPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("PickupSpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("PickupStreet");

                                    b2.HasKey("RouteDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsOrderId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TimeWindow", "PickupTimeWindow", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsOrderId")
                                        .HasColumnType("uuid");

                                    b2.Property<TimeSpan>("EndTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("PickupTimeWindowEnd");

                                    b2.Property<TimeSpan>("StartTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("PickupTimeWindowStart");

                                    b2.HasKey("RouteDetailsOrderId");

                                    b2.ToTable("Orders", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsOrderId");
                                });

                            b1.Navigation("DeliveryAddress")
                                .IsRequired();

                            b1.Navigation("DeliveryTimeWindow");

                            b1.Navigation("EstimatedDistance");

                            b1.Navigation("PickupAddress")
                                .IsRequired();

                            b1.Navigation("PickupTimeWindow");
                        });

                    b.Navigation("AcceptedBid");

                    b.Navigation("AgreedPrice")
                        .IsRequired();

                    b.Navigation("string")
                        .IsRequired();

                    b.Navigation("string")
                        .IsRequired();

                    b.Navigation("LoadDetails")
                        .IsRequired();

                    b.Navigation("Rfq");

                    b.Navigation("RouteDetails")
                        .IsRequired();
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.OrderDocument", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.Order", "Order")
                        .WithMany("Documents")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.OrderStatusHistory", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.Order", "Order")
                        .WithMany("StatusHistory")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RFQ", b =>
                {
                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "BudgetRange", b1 =>
                        {
                            b1.Property<Guid>("RFQId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("BudgetAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("BudgetCurrency");

                            b1.HasKey("RFQId");

                            b1.ToTable("Rfqs", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("RFQId");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.LoadDetails", "LoadDetails", b1 =>
                        {
                            b1.Property<Guid>("RFQId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Description")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("LoadDescription");

                            b1.Property<string>("HazardousClassification")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("HazardousClassification");

                            b1.Property<bool>("IsHazardous")
                                .HasColumnType("boolean")
                                .HasColumnName("IsHazardous");

                            b1.Property<int>("LoadType")
                                .HasColumnType("integer")
                                .HasColumnName("LoadType");

                            b1.Property<string>("PackagingType")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PackagingType");

                            b1.Property<List<string>>("Photos")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("LoadPhotos");

                            b1.Property<int>("Quantity")
                                .HasColumnType("integer")
                                .HasColumnName("LoadQuantity");

                            b1.Property<int>("RequiredVehicleType")
                                .ValueGeneratedOnUpdateSometimes()
                                .HasColumnType("integer")
                                .HasColumnName("RequiredVehicleType");

                            b1.Property<bool>("RequiresSpecialHandling")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresSpecialHandling");

                            b1.Property<bool>("RequiresTemperatureControl")
                                .ValueGeneratedOnUpdateSometimes()
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresTemperatureControl");

                            b1.Property<string>("SpecialHandlingInstructions")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("SpecialHandlingInstructions");

                            b1.HasKey("RFQId");

                            b1.ToTable("Rfqs", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("RFQId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Dimensions", "Dimensions", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<decimal>("Height")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionHeight");

                                    b2.Property<decimal>("Length")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionLength");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasMaxLength(10)
                                        .HasColumnType("character varying(10)")
                                        .HasColumnName("DimensionUnit");

                                    b2.Property<decimal>("Width")
                                        .HasPrecision(18, 2)
                                        .HasColumnType("numeric(18,2)")
                                        .HasColumnName("DimensionWidth");

                                    b2.HasKey("LoadDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TemperatureRange", "TemperatureRange", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<decimal>("MaxTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("MaxTemperature");

                                    b2.Property<decimal>("MinTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("MinTemperature");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasMaxLength(1)
                                        .HasColumnType("character varying(1)")
                                        .HasColumnName("TemperatureUnit");

                                    b2.HasKey("LoadDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Volume", "Volume", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("VolumeUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(18, 3)
                                        .HasColumnType("numeric(18,3)")
                                        .HasColumnName("VolumeValue");

                                    b2.HasKey("LoadDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Weight", "Weight", b2 =>
                                {
                                    b2.Property<Guid>("LoadDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("WeightUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(18, 3)
                                        .HasColumnType("numeric(18,3)")
                                        .HasColumnName("WeightValue");

                                    b2.HasKey("LoadDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("LoadDetailsRFQId");
                                });

                            b1.Navigation("Dimensions");

                            b1.Navigation("TemperatureRange");

                            b1.Navigation("Volume");

                            b1.Navigation("Weight")
                                .IsRequired();
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.RouteDetails", "RouteDetails", b1 =>
                        {
                            b1.Property<Guid>("RFQId")
                                .HasColumnType("uuid");

                            b1.Property<TimeSpan?>("EstimatedDuration")
                                .HasColumnType("interval")
                                .HasColumnName("EstimatedDuration");

                            b1.Property<List<Address>>("IntermediateStops")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("IntermediateStops");

                            b1.Property<bool>("IsFlexibleDelivery")
                                .HasColumnType("boolean")
                                .HasColumnName("IsFlexibleDelivery");

                            b1.Property<bool>("IsFlexiblePickup")
                                .HasColumnType("boolean")
                                .HasColumnName("IsFlexiblePickup");

                            b1.Property<DateTime>("PreferredDeliveryDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("PreferredDeliveryDate");

                            b1.Property<DateTime>("PreferredPickupDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("PreferredPickupDate");

                            b1.Property<string>("RouteNotes")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("RouteNotes");

                            b1.HasKey("RFQId");

                            b1.ToTable("Rfqs", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("RFQId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "DeliveryAddress", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("DeliveryContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("DeliveryLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("DeliveryLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("DeliveryPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("DeliverySpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("DeliveryState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("DeliveryStreet");

                                    b2.HasKey("RouteDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TimeWindow", "DeliveryTimeWindow", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<TimeSpan>("EndTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("DeliveryTimeWindowEnd");

                                    b2.Property<TimeSpan>("StartTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("DeliveryTimeWindowStart");

                                    b2.HasKey("RouteDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Distance", "EstimatedDistance", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<int>("Unit")
                                        .HasColumnType("integer")
                                        .HasColumnName("EstimatedDistanceUnit");

                                    b2.Property<decimal>("Value")
                                        .HasPrecision(10, 2)
                                        .HasColumnType("numeric(10,2)")
                                        .HasColumnName("EstimatedDistanceValue");

                                    b2.HasKey("RouteDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.Address", "PickupAddress", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<string>("City")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupCity");

                                    b2.Property<string>("ContactPerson")
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupContactPerson");

                                    b2.Property<string>("ContactPhone")
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("PickupContactPhone");

                                    b2.Property<string>("Country")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupCountry");

                                    b2.Property<decimal?>("Latitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("PickupLatitude");

                                    b2.Property<decimal?>("Longitude")
                                        .HasPrecision(10, 7)
                                        .HasColumnType("numeric(10,7)")
                                        .HasColumnName("PickupLongitude");

                                    b2.Property<string>("PostalCode")
                                        .IsRequired()
                                        .HasMaxLength(20)
                                        .HasColumnType("character varying(20)")
                                        .HasColumnName("PickupPostalCode");

                                    b2.Property<string>("SpecialInstructions")
                                        .HasMaxLength(500)
                                        .HasColumnType("character varying(500)")
                                        .HasColumnName("PickupSpecialInstructions");

                                    b2.Property<string>("State")
                                        .IsRequired()
                                        .HasMaxLength(100)
                                        .HasColumnType("character varying(100)")
                                        .HasColumnName("PickupState");

                                    b2.Property<string>("Street")
                                        .IsRequired()
                                        .HasMaxLength(200)
                                        .HasColumnType("character varying(200)")
                                        .HasColumnName("PickupStreet");

                                    b2.HasKey("RouteDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsRFQId");
                                });

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TimeWindow", "PickupTimeWindow", b2 =>
                                {
                                    b2.Property<Guid>("RouteDetailsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<TimeSpan>("EndTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("PickupTimeWindowEnd");

                                    b2.Property<TimeSpan>("StartTime")
                                        .HasColumnType("interval")
                                        .HasColumnName("PickupTimeWindowStart");

                                    b2.HasKey("RouteDetailsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RouteDetailsRFQId");
                                });

                            b1.Navigation("DeliveryAddress")
                                .IsRequired();

                            b1.Navigation("DeliveryTimeWindow");

                            b1.Navigation("EstimatedDistance");

                            b1.Navigation("PickupAddress")
                                .IsRequired();

                            b1.Navigation("PickupTimeWindow");
                        });

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.RfqRequirements", "Requirements", b1 =>
                        {
                            b1.Property<Guid>("RFQId")
                                .HasColumnType("uuid");

                            b1.Property<List<string>>("AdditionalRequirements")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("AdditionalRequirements");

                            b1.Property<decimal?>("MinInsuranceAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("MinInsuranceAmount");

                            b1.Property<int?>("MinVehicleCapacity")
                                .HasColumnType("integer")
                                .HasColumnName("MinVehicleCapacity");

                            b1.Property<int?>("MinYearsExperience")
                                .HasColumnType("integer")
                                .HasColumnName("MinYearsExperience");

                            b1.Property<List<string>>("RequiredLicenses")
                                .IsRequired()
                                .HasColumnType("jsonb")
                                .HasColumnName("RequiredLicenses");

                            b1.Property<int>("RequiredVehicleType")
                                .ValueGeneratedOnUpdateSometimes()
                                .HasColumnType("integer")
                                .HasColumnName("RequiredVehicleType");

                            b1.Property<bool>("RequiresBackground")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresBackground");

                            b1.Property<bool>("RequiresExperience")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresExperience");

                            b1.Property<bool>("RequiresInsurance")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresInsurance");

                            b1.Property<bool>("RequiresLicense")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresLicense");

                            b1.Property<bool>("RequiresTemperatureControl")
                                .ValueGeneratedOnUpdateSometimes()
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresTemperatureControl");

                            b1.Property<bool>("RequiresTracking")
                                .HasColumnType("boolean")
                                .HasColumnName("RequiresTracking");

                            b1.HasKey("RFQId");

                            b1.ToTable("Rfqs", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("RFQId");

                            b1.OwnsOne("OrderManagement.Domain.ValueObjects.TemperatureRange", "TemperatureRange", b2 =>
                                {
                                    b2.Property<Guid>("RfqRequirementsRFQId")
                                        .HasColumnType("uuid");

                                    b2.Property<decimal>("MaxTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("RequiredMaxTemperature");

                                    b2.Property<decimal>("MinTemperature")
                                        .HasPrecision(5, 2)
                                        .HasColumnType("numeric(5,2)")
                                        .HasColumnName("RequiredMinTemperature");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasMaxLength(1)
                                        .HasColumnType("character varying(1)")
                                        .HasColumnName("RequiredTemperatureUnit");

                                    b2.HasKey("RfqRequirementsRFQId");

                                    b2.ToTable("Rfqs", "order_management");

                                    b2.WithOwner()
                                        .HasForeignKey("RfqRequirementsRFQId");
                                });

                            b1.Navigation("TemperatureRange");
                        });

                    b.Navigation("BudgetRange");

                    b.Navigation("LoadDetails")
                        .IsRequired();

                    b.Navigation("Requirements")
                        .IsRequired();

                    b.Navigation("RouteDetails")
                        .IsRequired();
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RfqBid", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.RFQ", "Rfq")
                        .WithMany("Bids")
                        .HasForeignKey("RfqId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("OrderManagement.Domain.ValueObjects.Money", "QuotedPrice", b1 =>
                        {
                            b1.Property<Guid>("RfqBidId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("QuotedAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("QuotedCurrency");

                            b1.HasKey("RfqBidId");

                            b1.ToTable("RfqBids", "order_management");

                            b1.WithOwner()
                                .HasForeignKey("RfqBidId");
                        });

                    b.Navigation("QuotedPrice")
                        .IsRequired();

                    b.Navigation("Rfq");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RfqDocument", b =>
                {
                    b.HasOne("OrderManagement.Domain.Entities.RFQ", "Rfq")
                        .WithMany("Documents")
                        .HasForeignKey("RfqId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rfq");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Invoice", b =>
                {
                    b.Navigation("LineItems");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.Order", b =>
                {
                    b.Navigation("Documents");

                    b.Navigation("Invoices");

                    b.Navigation("StatusHistory");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RFQ", b =>
                {
                    b.Navigation("Bids");

                    b.Navigation("Documents");
                });

            modelBuilder.Entity("OrderManagement.Domain.Entities.RfqBid", b =>
                {
                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
