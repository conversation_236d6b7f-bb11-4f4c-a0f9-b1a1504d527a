using AuditCompliance.Application.DTOs;
using AuditCompliance.Domain.Enums;
using AuditCompliance.Domain.ValueObjects;
using MediatR;

namespace AuditCompliance.Application.Commands
{
    /// <summary>
    /// Create custom compliance report template command
    /// </summary>
    public class CreateCustomReportTemplateCommand : IRequest<CustomComplianceReportTemplateDto>
    {
        public string TemplateName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ComplianceStandard ComplianceStandard { get; set; }
        public ReportTemplateType TemplateType { get; set; }
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public ReportScheduleDto? Schedule { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool IsPublic { get; set; } = false;
        public Dictionary<string, object> Metadata { get; set; } = new();
        
        // Audit fields
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update custom compliance report template command
    /// </summary>
    public class UpdateCustomReportTemplateCommand : IRequest<CustomComplianceReportTemplateDto>
    {
        public Guid TemplateId { get; set; }
        public string TemplateName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public ReportScheduleDto? Schedule { get; set; }
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        
        // Audit fields
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Delete custom compliance report template command
    /// </summary>
    public class DeleteCustomReportTemplateCommand : IRequest<bool>
    {
        public Guid TemplateId { get; set; }
        public Guid DeletedBy { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Activate/Deactivate custom report template command
    /// </summary>
    public class ToggleTemplateActiveStatusCommand : IRequest<bool>
    {
        public Guid TemplateId { get; set; }
        public bool IsActive { get; set; }
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Make template public/private command
    /// </summary>
    public class ToggleTemplateVisibilityCommand : IRequest<bool>
    {
        public Guid TemplateId { get; set; }
        public bool IsPublic { get; set; }
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Grant template permission command
    /// </summary>
    public class GrantTemplatePermissionCommand : IRequest<bool>
    {
        public Guid TemplateId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public TemplatePermissionType PermissionType { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? Reason { get; set; }
        public Guid GrantedBy { get; set; }
    }

    /// <summary>
    /// Revoke template permission command
    /// </summary>
    public class RevokeTemplatePermissionCommand : IRequest<bool>
    {
        public Guid TemplateId { get; set; }
        public Guid UserId { get; set; }
        public Guid RevokedBy { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Generate report from template command
    /// </summary>
    public class GenerateReportFromTemplateCommand : IRequest<GeneratedReportDto>
    {
        public Guid TemplateId { get; set; }
        public Dictionary<string, object> ParameterValues { get; set; } = new();
        public ExportFormat OutputFormat { get; set; } = ExportFormat.PDF;
        public bool IncludeVisualizations { get; set; } = true;
        public bool IncludeInsights { get; set; } = true;
        public bool SaveResults { get; set; } = true;
        public string? ResultsName { get; set; }
        public List<string> Recipients { get; set; } = new();
        public bool ScheduleReport { get; set; } = false;
        public string? ScheduleExpression { get; set; }
        
        // Audit fields
        public Guid RequestedBy { get; set; }
        public string RequestedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validate template command
    /// </summary>
    public class ValidateTemplateCommand : IRequest<TemplateValidationResultDto>
    {
        public Guid? TemplateId { get; set; } // Null for new templates
        public string TemplateContent { get; set; } = string.Empty;
        public List<ReportSectionDto> Sections { get; set; } = new();
        public List<ReportParameterDto> Parameters { get; set; } = new();
        public bool PerformDeepValidation { get; set; } = true;
    }

    /// <summary>
    /// Clone template command
    /// </summary>
    public class CloneTemplateCommand : IRequest<CustomComplianceReportTemplateDto>
    {
        public Guid SourceTemplateId { get; set; }
        public string NewTemplateName { get; set; } = string.Empty;
        public string? NewDescription { get; set; }
        public bool MakePrivate { get; set; } = true;
        public Guid ClonedBy { get; set; }
        public string ClonedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Import template command
    /// </summary>
    public class ImportTemplateCommand : IRequest<CustomComplianceReportTemplateDto>
    {
        public string TemplateData { get; set; } = string.Empty; // JSON or XML
        public string ImportFormat { get; set; } = "JSON"; // JSON, XML
        public bool OverwriteExisting { get; set; } = false;
        public Guid ImportedBy { get; set; }
        public string ImportedByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Export template command
    /// </summary>
    public class ExportTemplateCommand : IRequest<TemplateExportDto>
    {
        public Guid TemplateId { get; set; }
        public string ExportFormat { get; set; } = "JSON"; // JSON, XML
        public bool IncludeMetadata { get; set; } = true;
        public bool IncludeUsageStatistics { get; set; } = false;
        public Guid ExportedBy { get; set; }
    }

    /// <summary>
    /// Schedule template execution command
    /// </summary>
    public class ScheduleTemplateExecutionCommand : IRequest<string>
    {
        public Guid TemplateId { get; set; }
        public string CronExpression { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<string> Recipients { get; set; } = new();
        public Dictionary<string, object> DefaultParameterValues { get; set; } = new();
        public ExportFormat OutputFormat { get; set; } = ExportFormat.PDF;
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        
        // Audit fields
        public Guid ScheduledBy { get; set; }
        public string ScheduledByName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Update template schedule command
    /// </summary>
    public class UpdateTemplateScheduleCommand : IRequest<bool>
    {
        public string ScheduleId { get; set; } = string.Empty;
        public string? CronExpression { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<string>? Recipients { get; set; }
        public Dictionary<string, object>? DefaultParameterValues { get; set; }
        public ExportFormat? OutputFormat { get; set; }
        public string? ScheduleName { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
        
        // Audit fields
        public Guid UpdatedBy { get; set; }
    }

    /// <summary>
    /// Cancel template schedule command
    /// </summary>
    public class CancelTemplateScheduleCommand : IRequest<bool>
    {
        public string ScheduleId { get; set; } = string.Empty;
        public Guid CancelledBy { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Generated report DTO
    /// </summary>
    public class GeneratedReportDto
    {
        public string ReportId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string? FileUrl { get; set; }
        public ExportFormat Format { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public TimeSpan GenerationTime { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> ReportMetadata { get; set; } = new();
        public List<string> Recipients { get; set; } = new();
        public bool WasScheduled { get; set; }
        public string? ScheduleId { get; set; }
    }

    /// <summary>
    /// Template export DTO
    /// </summary>
    public class TemplateExportDto
    {
        public string ExportId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public DateTime ExportedAt { get; set; }
        public Guid ExportedBy { get; set; }
        public Dictionary<string, object> ExportMetadata { get; set; } = new();
    }
}
