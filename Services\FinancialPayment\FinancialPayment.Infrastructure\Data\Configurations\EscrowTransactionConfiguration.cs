using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class EscrowTransactionConfiguration : IEntityTypeConfiguration<EscrowTransaction>
{
    public void Configure(EntityTypeBuilder<EscrowTransaction> builder)
    {
        builder.ToTable("escrow_transactions");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(e => e.EscrowAccountId)
            .HasColumnName("escrow_account_id")
            .IsRequired();

        builder.Property(e => e.Type)
            .HasColumnName("type")
            .HasConversion<int>()
            .IsRequired();

        // Money value object
        builder.OwnsOne(e => e.Amount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(e => e.ParticipantId)
            .HasColumnName("participant_id");

        builder.Property(e => e.PaymentGatewayTransactionId)
            .HasColumnName("payment_gateway_transaction_id")
            .HasMaxLength(100);

        builder.Property(e => e.Notes)
            .HasColumnName("notes")
            .HasMaxLength(1000);

        builder.Property(e => e.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(e => e.ProcessedAt)
            .HasColumnName("processed_at");

        builder.Property(e => e.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(e => e.FailureReason)
            .HasColumnName("failure_reason")
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(e => e.EscrowAccountId)
            .HasDatabaseName("ix_escrow_transactions_escrow_account_id");

        builder.HasIndex(e => e.Type)
            .HasDatabaseName("ix_escrow_transactions_type");

        builder.HasIndex(e => e.ParticipantId)
            .HasDatabaseName("ix_escrow_transactions_participant_id");

        builder.HasIndex(e => e.Status)
            .HasDatabaseName("ix_escrow_transactions_status");

        builder.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("ix_escrow_transactions_created_at");

        builder.HasIndex(e => e.PaymentGatewayTransactionId)
            .HasDatabaseName("ix_escrow_transactions_gateway_transaction_id");
    }
}
