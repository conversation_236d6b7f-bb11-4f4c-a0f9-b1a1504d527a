using DataStorage.Domain.Enums;

namespace DataStorage.Domain.ValueObjects;

public class ArchiveAnalytics
{
    public TimeSpan Period { get; private set; }
    public long TotalDocumentsArchived { get; private set; }
    public long TotalSizeArchived { get; private set; }
    public long TotalSpaceSaved { get; private set; }
    public decimal TotalCostSavings { get; private set; }
    public Dictionary<StorageTierType, long> DocumentsByTier { get; private set; }
    public Dictionary<DocumentCategory, long> DocumentsByCategory { get; private set; }
    public List<ArchiveTrend> Trends { get; private set; }
    public ArchivePerformanceMetrics Performance { get; private set; }

    public ArchiveAnalytics(
        TimeSpan period,
        long totalDocumentsArchived,
        long totalSizeArchived,
        long totalSpaceSaved,
        decimal totalCostSavings,
        Dictionary<StorageTierType, long> documentsByTier,
        Dictionary<DocumentCategory, long> documentsByCategory,
        List<ArchiveTrend> trends,
        ArchivePerformanceMetrics performance)
    {
        Period = period;
        TotalDocumentsArchived = totalDocumentsArchived;
        TotalSizeArchived = totalSizeArchived;
        TotalSpaceSaved = totalSpaceSaved;
        TotalCostSavings = totalCostSavings;
        DocumentsByTier = documentsByTier ?? new Dictionary<StorageTierType, long>();
        DocumentsByCategory = documentsByCategory ?? new Dictionary<DocumentCategory, long>();
        Trends = trends ?? new List<ArchiveTrend>();
        Performance = performance ?? new ArchivePerformanceMetrics();
    }
}

public class ArchiveTrend
{
    public DateTime Date { get; private set; }
    public long DocumentsArchived { get; private set; }
    public long SizeArchived { get; private set; }
    public decimal CostSavings { get; private set; }
    public double AverageArchiveTime { get; private set; }

    public ArchiveTrend(
        DateTime date,
        long documentsArchived,
        long sizeArchived,
        decimal costSavings,
        double averageArchiveTime)
    {
        Date = date;
        DocumentsArchived = documentsArchived;
        SizeArchived = sizeArchived;
        CostSavings = costSavings;
        AverageArchiveTime = averageArchiveTime;
    }
}

public class ArchivePerformanceMetrics
{
    public double AverageArchiveTime { get; private set; }
    public double AverageCompressionRatio { get; private set; }
    public double SuccessRate { get; private set; }
    public long ThroughputBytesPerSecond { get; private set; }

    public ArchivePerformanceMetrics(
        double averageArchiveTime = 0,
        double averageCompressionRatio = 0,
        double successRate = 0,
        long throughputBytesPerSecond = 0)
    {
        AverageArchiveTime = averageArchiveTime;
        AverageCompressionRatio = averageCompressionRatio;
        SuccessRate = Math.Max(0, Math.Min(100, successRate));
        ThroughputBytesPerSecond = throughputBytesPerSecond;
    }
}

public class ArchiveMetrics
{
    public DateTime Date { get; private set; }
    public StorageTierType? TierType { get; private set; }
    public DocumentCategory? Category { get; private set; }
    public long DocumentCount { get; private set; }
    public long TotalSizeBytes { get; private set; }
    public decimal StorageCost { get; private set; }
    public long AccessCount { get; private set; }
    public decimal RetrievalCost { get; private set; }

    public ArchiveMetrics(
        DateTime date,
        long documentCount,
        long totalSizeBytes,
        decimal storageCost,
        long accessCount,
        decimal retrievalCost,
        StorageTierType? tierType = null,
        DocumentCategory? category = null)
    {
        Date = date;
        TierType = tierType;
        Category = category;
        DocumentCount = documentCount;
        TotalSizeBytes = totalSizeBytes;
        StorageCost = storageCost;
        AccessCount = accessCount;
        RetrievalCost = retrievalCost;
    }
}

public class ArchiveMetricsRequest
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public StorageTierType? TierType { get; private set; }
    public DocumentCategory? Category { get; private set; }
    public MetricsGroupBy GroupBy { get; private set; }

    public ArchiveMetricsRequest(
        DateTime startDate,
        DateTime endDate,
        MetricsGroupBy groupBy = MetricsGroupBy.Day,
        StorageTierType? tierType = null,
        DocumentCategory? category = null)
    {
        StartDate = startDate;
        EndDate = endDate;
        GroupBy = groupBy;
        TierType = tierType;
        Category = category;
    }
}

public class StorageOptimizationReport
{
    public DateTime GeneratedAt { get; private set; }
    public long TotalStorageBytes { get; private set; }
    public decimal TotalStorageCost { get; private set; }
    public List<OptimizationRecommendation> Recommendations { get; private set; }
    public StorageDistribution Distribution { get; private set; }
    public PotentialSavings Savings { get; private set; }

    public StorageOptimizationReport(
        long totalStorageBytes,
        decimal totalStorageCost,
        List<OptimizationRecommendation> recommendations,
        StorageDistribution distribution,
        PotentialSavings savings)
    {
        GeneratedAt = DateTime.UtcNow;
        TotalStorageBytes = totalStorageBytes;
        TotalStorageCost = totalStorageCost;
        Recommendations = recommendations ?? new List<OptimizationRecommendation>();
        Distribution = distribution ?? throw new ArgumentNullException(nameof(distribution));
        Savings = savings ?? throw new ArgumentNullException(nameof(savings));
    }
}

public class OptimizationRecommendation
{
    public OptimizationType Type { get; private set; }
    public string Title { get; private set; }
    public string Description { get; private set; }
    public OptimizationImpact Impact { get; private set; }
    public long AffectedDocuments { get; private set; }
    public long PotentialSavingsBytes { get; private set; }
    public decimal PotentialCostSavings { get; private set; }
    public ImplementationComplexity Complexity { get; private set; }

    public OptimizationRecommendation(
        OptimizationType type,
        string title,
        string description,
        OptimizationImpact impact,
        long affectedDocuments,
        long potentialSavingsBytes,
        decimal potentialCostSavings,
        ImplementationComplexity complexity)
    {
        Type = type;
        Title = title ?? throw new ArgumentNullException(nameof(title));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        Impact = impact;
        AffectedDocuments = affectedDocuments;
        PotentialSavingsBytes = potentialSavingsBytes;
        PotentialCostSavings = potentialCostSavings;
        Complexity = complexity;
    }
}

public class StorageDistribution
{
    public Dictionary<StorageTierType, long> SizeByTier { get; private set; }
    public Dictionary<StorageTierType, decimal> CostByTier { get; private set; }
    public Dictionary<DocumentCategory, long> SizeByCategory { get; private set; }
    public Dictionary<string, long> SizeByAge { get; private set; }

    public StorageDistribution(
        Dictionary<StorageTierType, long> sizeByTier,
        Dictionary<StorageTierType, decimal> costByTier,
        Dictionary<DocumentCategory, long> sizeByCategory,
        Dictionary<string, long> sizeByAge)
    {
        SizeByTier = sizeByTier ?? new Dictionary<StorageTierType, long>();
        CostByTier = costByTier ?? new Dictionary<StorageTierType, decimal>();
        SizeByCategory = sizeByCategory ?? new Dictionary<DocumentCategory, long>();
        SizeByAge = sizeByAge ?? new Dictionary<string, long>();
    }
}

public class PotentialSavings
{
    public long TotalSavingsBytes { get; private set; }
    public decimal TotalCostSavings { get; private set; }
    public Dictionary<OptimizationType, decimal> SavingsByType { get; private set; }
    public TimeSpan EstimatedImplementationTime { get; private set; }

    public PotentialSavings(
        long totalSavingsBytes,
        decimal totalCostSavings,
        Dictionary<OptimizationType, decimal> savingsByType,
        TimeSpan estimatedImplementationTime)
    {
        TotalSavingsBytes = totalSavingsBytes;
        TotalCostSavings = totalCostSavings;
        SavingsByType = savingsByType ?? new Dictionary<OptimizationType, decimal>();
        EstimatedImplementationTime = estimatedImplementationTime;
    }
}

public class ComplianceReport
{
    public DateTime GeneratedAt { get; private set; }
    public ComplianceReportRequest Request { get; private set; }
    public ComplianceStatus OverallStatus { get; private set; }
    public List<ComplianceViolation> Violations { get; private set; }
    public List<ComplianceMetric> Metrics { get; private set; }
    public Dictionary<string, object> Summary { get; private set; }

    public ComplianceReport(
        ComplianceReportRequest request,
        ComplianceStatus overallStatus,
        List<ComplianceViolation> violations,
        List<ComplianceMetric> metrics,
        Dictionary<string, object> summary)
    {
        GeneratedAt = DateTime.UtcNow;
        Request = request ?? throw new ArgumentNullException(nameof(request));
        OverallStatus = overallStatus;
        Violations = violations ?? new List<ComplianceViolation>();
        Metrics = metrics ?? new List<ComplianceMetric>();
        Summary = summary ?? new Dictionary<string, object>();
    }
}

public class ComplianceReportRequest
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public List<ComplianceStandard> Standards { get; private set; }
    public List<DocumentCategory> Categories { get; private set; }
    public Guid RequestedBy { get; private set; }

    public ComplianceReportRequest(
        DateTime startDate,
        DateTime endDate,
        List<ComplianceStandard> standards,
        Guid requestedBy,
        List<DocumentCategory>? categories = null)
    {
        StartDate = startDate;
        EndDate = endDate;
        Standards = standards ?? new List<ComplianceStandard>();
        RequestedBy = requestedBy;
        Categories = categories ?? new List<DocumentCategory>();
    }
}

public class ComplianceViolation
{
    public Guid ViolationId { get; private set; }
    public Guid DocumentId { get; private set; }
    public ComplianceStandard Standard { get; private set; }
    public ViolationType Type { get; private set; }
    public ViolationSeverity Severity { get; private set; }
    public string Description { get; private set; }
    public DateTime DetectedAt { get; private set; }
    public ViolationStatus Status { get; private set; }
    public string? Resolution { get; private set; }

    public ComplianceViolation(
        Guid violationId,
        Guid documentId,
        ComplianceStandard standard,
        ViolationType type,
        ViolationSeverity severity,
        string description,
        DateTime detectedAt,
        ViolationStatus status,
        string? resolution = null)
    {
        ViolationId = violationId;
        DocumentId = documentId;
        Standard = standard;
        Type = type;
        Severity = severity;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        DetectedAt = detectedAt;
        Status = status;
        Resolution = resolution;
    }
}

public class ComplianceMetric
{
    public string Name { get; private set; }
    public double Value { get; private set; }
    public string Unit { get; private set; }
    public ComplianceStandard Standard { get; private set; }
    public MetricStatus Status { get; private set; }

    public ComplianceMetric(
        string name,
        double value,
        string unit,
        ComplianceStandard standard,
        MetricStatus status)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Value = value;
        Unit = unit ?? throw new ArgumentNullException(nameof(unit));
        Standard = standard;
        Status = status;
    }
}

public class RetentionViolation
{
    public Guid ViolationId { get; private set; }
    public Guid DocumentId { get; private set; }
    public RetentionViolationType Type { get; private set; }
    public string Description { get; private set; }
    public DateTime DetectedAt { get; private set; }
    public DateTime RequiredActionBy { get; private set; }
    public ViolationSeverity Severity { get; private set; }
    public ViolationStatus Status { get; private set; }

    public RetentionViolation(
        Guid violationId,
        Guid documentId,
        RetentionViolationType type,
        string description,
        DateTime detectedAt,
        DateTime requiredActionBy,
        ViolationSeverity severity,
        ViolationStatus status)
    {
        ViolationId = violationId;
        DocumentId = documentId;
        Type = type;
        Description = description ?? throw new ArgumentNullException(nameof(description));
        DetectedAt = detectedAt;
        RequiredActionBy = requiredActionBy;
        Severity = severity;
        Status = status;
    }
}

public class LegalHoldRequest
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public Guid RequestedBy { get; private set; }
    public string CaseNumber { get; private set; }
    public DateTime EffectiveDate { get; private set; }
    public DateTime? ExpirationDate { get; private set; }

    public LegalHoldRequest(
        string name,
        string description,
        Guid requestedBy,
        string caseNumber,
        DateTime effectiveDate,
        DateTime? expirationDate = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Description = description ?? throw new ArgumentNullException(nameof(description));
        RequestedBy = requestedBy;
        CaseNumber = caseNumber ?? throw new ArgumentNullException(nameof(caseNumber));
        EffectiveDate = effectiveDate;
        ExpirationDate = expirationDate;
    }
}
