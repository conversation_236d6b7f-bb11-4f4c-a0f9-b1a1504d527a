version: '3.8'

services:
  auditcompliance-db:
    image: timescale/timescaledb:latest-pg15
    container_name: auditcompliance-db
    environment:
      POSTGRES_DB: AuditComplianceDb
      POSTGRES_USER: timescale
      POSTGRES_PASSWORD: timescale
    ports:
      - "5433:5432"
    volumes:
      - auditcompliance_data:/var/lib/postgresql/data
      - ./database-setup.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - auditcompliance-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timescale -d AuditComplianceDb"]
      interval: 30s
      timeout: 10s
      retries: 3

  auditcompliance-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: auditcompliance-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=auditcompliance-db;Port=5432;Database=AuditComplianceDb;Username=timescale;Password=timescale
      - Jwt__Key=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - Jwt__Issuer=TLI-AuditCompliance
      - Jwt__Audience=TLI-Services
    ports:
      - "5010:80"
      - "5011:443"
    depends_on:
      auditcompliance-db:
        condition: service_healthy
    networks:
      - auditcompliance-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  auditcompliance-network:
    driver: bridge

volumes:
  auditcompliance_data:
