using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class SettlementDistributionConfiguration : IEntityTypeConfiguration<SettlementDistribution>
{
    public void Configure(EntityTypeBuilder<SettlementDistribution> builder)
    {
        builder.ToTable("settlement_distributions");

        builder.<PERSON><PERSON>ey(d => d.Id);

        builder.Property(d => d.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(d => d.SettlementId)
            .HasColumnName("settlement_id")
            .IsRequired();

        builder.Property(d => d.RecipientId)
            .HasColumnName("recipient_id")
            .IsRequired();

        builder.Property(d => d.RecipientRole)
            .HasColumnName("recipient_role")
            .HasConversion<int>()
            .IsRequired();

        // Money value object
        builder.OwnsOne(d => d.Amount, money =>
        {
            money.Property(m => m.Amount)
                .HasColumnName("amount")
                .HasColumnType("decimal(18,2)")
                .IsRequired();

            money.Property(m => m.Currency)
                .HasColumnName("currency")
                .HasMaxLength(3)
                .IsRequired();
        });

        builder.Property(d => d.Type)
            .HasColumnName("type")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.Description)
            .HasColumnName("description")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(d => d.Status)
            .HasColumnName("status")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(d => d.ProcessedAt)
            .HasColumnName("processed_at");

        builder.Property(d => d.PaymentGatewayTransactionId)
            .HasColumnName("payment_gateway_transaction_id")
            .HasMaxLength(100);

        builder.Property(d => d.FailureReason)
            .HasColumnName("failure_reason")
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(d => d.SettlementId)
            .HasDatabaseName("ix_settlement_distributions_settlement_id");

        builder.HasIndex(d => d.RecipientId)
            .HasDatabaseName("ix_settlement_distributions_recipient_id");

        builder.HasIndex(d => d.RecipientRole)
            .HasDatabaseName("ix_settlement_distributions_recipient_role");

        builder.HasIndex(d => d.Type)
            .HasDatabaseName("ix_settlement_distributions_type");

        builder.HasIndex(d => d.Status)
            .HasDatabaseName("ix_settlement_distributions_status");

        builder.HasIndex(d => d.CreatedAt)
            .HasDatabaseName("ix_settlement_distributions_created_at");

        builder.HasIndex(d => d.PaymentGatewayTransactionId)
            .HasDatabaseName("ix_settlement_distributions_gateway_transaction_id");
    }
}
