using SubscriptionManagement.Application.Commands.TransportCompanyAutoRenewal;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace SubscriptionManagement.API.Controllers;

/// <summary>
/// Controller for Transport Company auto-renewal management
/// </summary>
[ApiController]
[Route("api/transport-company/auto-renewal")]
[Authorize(Roles = "TransportCompany,Admin")]
public class TransportCompanyAutoRenewalController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<TransportCompanyAutoRenewalController> _logger;

    public TransportCompanyAutoRenewalController(
        IMediator mediator,
        ILogger<TransportCompanyAutoRenewalController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get auto-renewal settings for a transport company
    /// </summary>
    [HttpGet("{transportCompanyId}")]
    [ProducesResponseType(typeof(AutoRenewalSettingsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<AutoRenewalSettingsDto>> GetAutoRenewalSettings(
        Guid transportCompanyId,
        [FromQuery] Guid? subscriptionId = null,
        [FromQuery] bool includeHistory = false,
        [FromQuery] bool includeUpcomingReminders = true)
    {
        try
        {
            if (!await CanManageAutoRenewal(transportCompanyId))
            {
                return Forbid("You don't have permission to view auto-renewal settings for this transport company");
            }

            _logger.LogInformation("Getting auto-renewal settings for Transport Company {TransportCompanyId}", transportCompanyId);

            var query = new GetAutoRenewalSettingsQuery
            {
                TransportCompanyId = transportCompanyId,
                SubscriptionId = subscriptionId,
                IncludeHistory = includeHistory,
                IncludeUpcomingReminders = includeUpcomingReminders
            };

            var settings = await _mediator.Send(query);

            if (settings == null)
            {
                return NotFound("Auto-renewal settings not found for this transport company");
            }

            return Ok(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auto-renewal settings for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update auto-renewal settings for a transport company
    /// </summary>
    [HttpPut("{transportCompanyId}")]
    [ProducesResponseType(typeof(UpdateAutoRenewalSettingsResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<UpdateAutoRenewalSettingsResult>> UpdateAutoRenewalSettings(
        Guid transportCompanyId,
        [FromBody] UpdateAutoRenewalSettingsRequest request)
    {
        try
        {
            if (!await CanManageAutoRenewal(transportCompanyId))
            {
                return Forbid("You don't have permission to update auto-renewal settings for this transport company");
            }

            _logger.LogInformation("Updating auto-renewal settings for Transport Company {TransportCompanyId}. AutoRenew: {AutoRenew}",
                transportCompanyId, request.AutoRenewEnabled);

            var command = new UpdateAutoRenewalSettingsCommand
            {
                TransportCompanyId = transportCompanyId,
                SubscriptionId = request.SubscriptionId,
                AutoRenewEnabled = request.AutoRenewEnabled,
                Preferences = request.Preferences,
                ChangeReason = request.ChangeReason,
                UpdatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Auto-renewal settings updated successfully for Transport Company {TransportCompanyId}. AutoRenew: {AutoRenew}",
                    transportCompanyId, request.AutoRenewEnabled);
                return Ok(result);
            }

            _logger.LogWarning("Failed to update auto-renewal settings for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating auto-renewal settings for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Cancel auto-renewal for a transport company subscription
    /// </summary>
    [HttpPost("{transportCompanyId}/cancel")]
    [ProducesResponseType(typeof(CancelAutoRenewalResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<CancelAutoRenewalResult>> CancelAutoRenewal(
        Guid transportCompanyId,
        [FromBody] CancelAutoRenewalRequest request)
    {
        try
        {
            if (!await CanManageAutoRenewal(transportCompanyId))
            {
                return Forbid("You don't have permission to cancel auto-renewal for this transport company");
            }

            _logger.LogInformation("Cancelling auto-renewal for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                transportCompanyId, request.SubscriptionId);

            var command = new CancelAutoRenewalCommand
            {
                TransportCompanyId = transportCompanyId,
                SubscriptionId = request.SubscriptionId,
                CancellationReason = request.CancellationReason,
                CancelImmediately = request.CancelImmediately,
                CancellationDate = request.CancellationDate,
                RefundProrated = request.RefundProrated,
                CancelledBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Auto-renewal cancelled successfully for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                    transportCompanyId, request.SubscriptionId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to cancel auto-renewal for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling auto-renewal for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Reactivate auto-renewal for a transport company subscription
    /// </summary>
    [HttpPost("{transportCompanyId}/reactivate")]
    [ProducesResponseType(typeof(ReactivateAutoRenewalResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ReactivateAutoRenewalResult>> ReactivateAutoRenewal(
        Guid transportCompanyId,
        [FromBody] ReactivateAutoRenewalRequest request)
    {
        try
        {
            if (!await CanManageAutoRenewal(transportCompanyId))
            {
                return Forbid("You don't have permission to reactivate auto-renewal for this transport company");
            }

            _logger.LogInformation("Reactivating auto-renewal for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                transportCompanyId, request.SubscriptionId);

            var command = new ReactivateAutoRenewalCommand
            {
                TransportCompanyId = transportCompanyId,
                SubscriptionId = request.SubscriptionId,
                PaymentMethodId = request.PaymentMethodId,
                NewPreferences = request.NewPreferences,
                ReactivationReason = request.ReactivationReason,
                ReactivatedBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Auto-renewal reactivated successfully for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                    transportCompanyId, request.SubscriptionId);
                return Ok(result);
            }

            _logger.LogWarning("Failed to reactivate auto-renewal for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reactivating auto-renewal for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Schedule renewal reminders for a subscription
    /// </summary>
    [HttpPost("{transportCompanyId}/schedule-reminders")]
    [ProducesResponseType(typeof(ScheduleRenewalRemindersResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<ScheduleRenewalRemindersResult>> ScheduleRenewalReminders(
        Guid transportCompanyId,
        [FromBody] ScheduleRemindersRequest request)
    {
        try
        {
            if (!await CanManageAutoRenewal(transportCompanyId))
            {
                return Forbid("You don't have permission to schedule reminders for this transport company");
            }

            _logger.LogInformation("Scheduling renewal reminders for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                transportCompanyId, request.SubscriptionId);

            var command = new ScheduleRenewalRemindersCommand
            {
                SubscriptionId = request.SubscriptionId,
                ReminderDays = request.ReminderDays,
                NotificationChannels = request.NotificationChannels,
                RescheduleExisting = request.RescheduleExisting,
                ScheduledBy = GetCurrentUserId()
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Renewal reminders scheduled successfully for Transport Company {TransportCompanyId}. Scheduled {Count} reminders",
                    transportCompanyId, result.RemindersScheduled);
                return Ok(result);
            }

            _logger.LogWarning("Failed to schedule renewal reminders for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling renewal reminders for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Process auto-renewal manually (admin only)
    /// </summary>
    [HttpPost("{transportCompanyId}/process")]
    [ProducesResponseType(typeof(ProcessAutoRenewalResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ProcessAutoRenewalResult>> ProcessAutoRenewal(
        Guid transportCompanyId,
        [FromBody] ProcessAutoRenewalRequest request)
    {
        try
        {
            _logger.LogInformation("Manually processing auto-renewal for Transport Company {TransportCompanyId}, Subscription {SubscriptionId}",
                transportCompanyId, request.SubscriptionId);

            var command = new ProcessAutoRenewalCommand
            {
                SubscriptionId = request.SubscriptionId,
                ForceRenewal = request.ForceRenewal,
                PaymentMethodId = request.PaymentMethodId,
                ProcessingOptions = request.ProcessingOptions
            };

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Auto-renewal processed successfully for Transport Company {TransportCompanyId}. Amount: {Amount} {Currency}",
                    transportCompanyId, result.AmountCharged, result.Currency);
                return Ok(result);
            }

            _logger.LogWarning("Failed to process auto-renewal for Transport Company {TransportCompanyId}: {Error}",
                transportCompanyId, result.ErrorMessage);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing auto-renewal for Transport Company {TransportCompanyId}", transportCompanyId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get auto-renewal guidelines and best practices
    /// </summary>
    [HttpGet("guidelines")]
    [ProducesResponseType(typeof(AutoRenewalGuidelinesDto), StatusCodes.Status200OK)]
    [AllowAnonymous]
    public async Task<ActionResult<AutoRenewalGuidelinesDto>> GetAutoRenewalGuidelines()
    {
        try
        {
            var guidelines = new AutoRenewalGuidelinesDto
            {
                Overview = "Auto-renewal ensures uninterrupted service by automatically renewing your subscription before it expires.",
                Benefits = new List<string>
                {
                    "Uninterrupted service access",
                    "No manual renewal required",
                    "Advance notifications before renewal",
                    "Flexible cancellation options",
                    "Grace period for failed payments"
                },
                HowItWorks = new List<string>
                {
                    "Auto-renewal is enabled by default for new subscriptions",
                    "You'll receive reminder notifications before renewal",
                    "Payment is automatically processed on the renewal date",
                    "You can cancel auto-renewal anytime before the next billing cycle",
                    "Failed payments trigger a grace period with retry attempts"
                },
                ImportantNotes = new List<string>
                {
                    "Ensure your payment method is valid and up-to-date",
                    "You can modify auto-renewal settings anytime",
                    "Cancellation takes effect at the end of the current billing period",
                    "Refunds are processed according to our refund policy",
                    "You'll receive confirmation for all auto-renewal activities"
                },
                ManagingAutoRenewal = new List<string>
                {
                    "Enable/disable auto-renewal in your subscription settings",
                    "Set notification preferences for renewal reminders",
                    "Configure grace period settings for failed payments",
                    "Update payment methods before renewal dates",
                    "Review renewal history and upcoming renewals"
                },
                TroubleshootingTips = new List<string>
                {
                    "Check payment method validity if renewal fails",
                    "Ensure sufficient funds are available",
                    "Contact support if you experience recurring issues",
                    "Update billing information if it has changed",
                    "Monitor email notifications for renewal alerts"
                }
            };

            return Ok(guidelines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auto-renewal guidelines");
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<bool> CanManageAutoRenewal(Guid transportCompanyId)
    {
        var currentUserId = GetCurrentUserId();
        var userRoles = GetCurrentUserRoles();

        return userRoles.Contains("Admin") || 
               (userRoles.Contains("TransportCompany") && await IsUserFromTransportCompany(currentUserId, transportCompanyId));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("sub") ?? User.FindFirst("userId");
        return userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId) ? userId : Guid.Empty;
    }

    private List<string> GetCurrentUserRoles()
    {
        return User.FindAll("role").Select(c => c.Value).ToList();
    }

    private async Task<bool> IsUserFromTransportCompany(Guid userId, Guid transportCompanyId)
    {
        // This would typically check the user's company association
        return await Task.FromResult(true);
    }
}

/// <summary>
/// Request DTO for updating auto-renewal settings
/// </summary>
public class UpdateAutoRenewalSettingsRequest
{
    public Guid SubscriptionId { get; set; }
    public bool AutoRenewEnabled { get; set; }
    public AutoRenewalPreferences Preferences { get; set; } = new();
    public string? ChangeReason { get; set; }
}

/// <summary>
/// Request DTO for cancelling auto-renewal
/// </summary>
public class CancelAutoRenewalRequest
{
    public Guid SubscriptionId { get; set; }
    public string CancellationReason { get; set; } = string.Empty;
    public bool CancelImmediately { get; set; } = false;
    public DateTime? CancellationDate { get; set; }
    public bool RefundProrated { get; set; } = false;
}

/// <summary>
/// Request DTO for reactivating auto-renewal
/// </summary>
public class ReactivateAutoRenewalRequest
{
    public Guid SubscriptionId { get; set; }
    public string? PaymentMethodId { get; set; }
    public AutoRenewalPreferences? NewPreferences { get; set; }
    public string ReactivationReason { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for scheduling reminders
/// </summary>
public class ScheduleRemindersRequest
{
    public Guid SubscriptionId { get; set; }
    public List<int> ReminderDays { get; set; } = new();
    public List<string> NotificationChannels { get; set; } = new();
    public bool RescheduleExisting { get; set; } = false;
}

/// <summary>
/// Request DTO for processing auto-renewal
/// </summary>
public class ProcessAutoRenewalRequest
{
    public Guid SubscriptionId { get; set; }
    public bool ForceRenewal { get; set; } = false;
    public string? PaymentMethodId { get; set; }
    public Dictionary<string, object> ProcessingOptions { get; set; } = new();
}

/// <summary>
/// DTO for auto-renewal guidelines
/// </summary>
public class AutoRenewalGuidelinesDto
{
    public string Overview { get; set; } = string.Empty;
    public List<string> Benefits { get; set; } = new();
    public List<string> HowItWorks { get; set; } = new();
    public List<string> ImportantNotes { get; set; } = new();
    public List<string> ManagingAutoRenewal { get; set; } = new();
    public List<string> TroubleshootingTips { get; set; } = new();
}
