using CommunicationNotification.Application.Interfaces;
using CommunicationNotification.Domain.Entities;
using CommunicationNotification.Domain.Enums;
using CommunicationNotification.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace CommunicationNotification.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Notification entity
/// </summary>
public class NotificationRepository : INotificationRepository
{
    private readonly CommunicationDbContext _context;

    public NotificationRepository(CommunicationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Get notification by ID
    /// </summary>
    public async Task<Notification?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Notifications
            .FirstOrDefaultAsync(n => n.Id == id, cancellationToken);
    }

    /// <summary>
    /// Get notifications by user ID
    /// </summary>
    public async Task<IEnumerable<Notification>> GetByUserIdAsync(
        Guid userId, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Notifications
            .Where(n => n.UserId == userId)
            .OrderByDescending(n => n.ScheduledAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get notifications by status
    /// </summary>
    public async Task<IEnumerable<Notification>> GetByStatusAsync(
        NotificationStatus status, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        return await _context.Notifications
            .Where(n => n.Status == status)
            .OrderByDescending(n => n.ScheduledAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get pending notifications for sending
    /// </summary>
    public async Task<IEnumerable<Notification>> GetPendingNotificationsAsync(
        DateTime? scheduledBefore = null, 
        int limit = 100, 
        CancellationToken cancellationToken = default)
    {
        var cutoffTime = scheduledBefore ?? DateTime.UtcNow;
        
        return await _context.Notifications
            .Where(n => n.Status == NotificationStatus.Pending && n.ScheduledAt <= cutoffTime)
            .OrderBy(n => n.Priority)
            .ThenBy(n => n.ScheduledAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get failed notifications for retry
    /// </summary>
    public async Task<IEnumerable<Notification>> GetFailedNotificationsForRetryAsync(
        int maxRetryCount = 3, 
        TimeSpan? retryDelay = null, 
        int limit = 50, 
        CancellationToken cancellationToken = default)
    {
        var delay = retryDelay ?? TimeSpan.FromMinutes(5);
        var retryAfter = DateTime.UtcNow.Subtract(delay);
        
        return await _context.Notifications
            .Where(n => n.Status == NotificationStatus.Failed && 
                       n.RetryCount < maxRetryCount && 
                       n.UpdatedAt <= retryAfter)
            .OrderBy(n => n.Priority)
            .ThenBy(n => n.UpdatedAt)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get notifications by date range
    /// </summary>
    public async Task<IEnumerable<Notification>> GetByDateRangeAsync(
        DateTime fromDate, 
        DateTime toDate, 
        Guid? userId = null, 
        NotificationChannel? channel = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Notifications
            .Where(n => n.ScheduledAt >= fromDate && n.ScheduledAt <= toDate);

        if (userId.HasValue)
        {
            query = query.Where(n => n.UserId == userId.Value);
        }

        if (channel.HasValue)
        {
            query = query.Where(n => n.Channel == channel.Value);
        }

        return await query
            .OrderByDescending(n => n.ScheduledAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get notification statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync(
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        Guid? userId = null, 
        CancellationToken cancellationToken = default)
    {
        var from = fromDate ?? DateTime.UtcNow.AddDays(-30);
        var to = toDate ?? DateTime.UtcNow;

        var query = _context.Notifications
            .Where(n => n.ScheduledAt >= from && n.ScheduledAt <= to);

        if (userId.HasValue)
        {
            query = query.Where(n => n.UserId == userId.Value);
        }

        var stats = await query
            .GroupBy(n => 1)
            .Select(g => new
            {
                TotalNotifications = g.Count(),
                SentNotifications = g.Count(n => n.Status == NotificationStatus.Sent || n.Status == NotificationStatus.Delivered),
                DeliveredNotifications = g.Count(n => n.Status == NotificationStatus.Delivered),
                FailedNotifications = g.Count(n => n.Status == NotificationStatus.Failed),
                PendingNotifications = g.Count(n => n.Status == NotificationStatus.Pending),
                AvgDeliveryTimeSeconds = g.Where(n => n.DeliveredAt.HasValue && n.SentAt.HasValue)
                    .Average(n => EF.Functions.DateDiffSecond(n.SentAt!.Value, n.DeliveredAt!.Value))
            })
            .FirstOrDefaultAsync(cancellationToken);

        var channelStats = await query
            .GroupBy(n => n.Channel)
            .Select(g => new
            {
                Channel = g.Key.ToString(),
                Count = g.Count(),
                DeliveryRate = g.Count(n => n.Status == NotificationStatus.Delivered) * 100.0 / g.Count()
            })
            .ToListAsync(cancellationToken);

        return new Dictionary<string, object>
        {
            ["total_notifications"] = stats?.TotalNotifications ?? 0,
            ["sent_notifications"] = stats?.SentNotifications ?? 0,
            ["delivered_notifications"] = stats?.DeliveredNotifications ?? 0,
            ["failed_notifications"] = stats?.FailedNotifications ?? 0,
            ["pending_notifications"] = stats?.PendingNotifications ?? 0,
            ["delivery_rate"] = stats?.TotalNotifications > 0 ? 
                Math.Round((stats.DeliveredNotifications * 100.0) / stats.TotalNotifications, 2) : 0,
            ["avg_delivery_time_seconds"] = Math.Round(stats?.AvgDeliveryTimeSeconds ?? 0, 2),
            ["channel_statistics"] = channelStats.ToDictionary(cs => cs.Channel, cs => new
            {
                count = cs.Count,
                delivery_rate = Math.Round(cs.DeliveryRate, 2)
            })
        };
    }

    /// <summary>
    /// Add notification
    /// </summary>
    public async Task AddAsync(Notification notification, CancellationToken cancellationToken = default)
    {
        await _context.Notifications.AddAsync(notification, cancellationToken);
    }

    /// <summary>
    /// Add multiple notifications
    /// </summary>
    public async Task AddRangeAsync(IEnumerable<Notification> notifications, CancellationToken cancellationToken = default)
    {
        await _context.Notifications.AddRangeAsync(notifications, cancellationToken);
    }

    /// <summary>
    /// Update notification
    /// </summary>
    public void Update(Notification notification)
    {
        _context.Notifications.Update(notification);
    }

    /// <summary>
    /// Delete notification
    /// </summary>
    public void Delete(Notification notification)
    {
        _context.Notifications.Remove(notification);
    }

    /// <summary>
    /// Delete notifications by user ID
    /// </summary>
    public async Task DeleteByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var notifications = await _context.Notifications
            .Where(n => n.UserId == userId)
            .ToListAsync(cancellationToken);

        _context.Notifications.RemoveRange(notifications);
    }

    /// <summary>
    /// Count notifications
    /// </summary>
    public async Task<int> CountAsync(
        Guid? userId = null, 
        NotificationStatus? status = null, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Notifications.AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(n => n.UserId == userId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(n => n.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(n => n.ScheduledAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(n => n.ScheduledAt <= toDate.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    /// <summary>
    /// Check if notification exists
    /// </summary>
    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Notifications
            .AnyAsync(n => n.Id == id, cancellationToken);
    }

    /// <summary>
    /// Get notifications with delivery attempts
    /// </summary>
    public async Task<IEnumerable<Notification>> GetWithDeliveryAttemptsAsync(
        Guid? userId = null, 
        int pageNumber = 1, 
        int pageSize = 20, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.Notifications
            .Include("DeliveryAttempts")
            .AsQueryable();

        if (userId.HasValue)
        {
            query = query.Where(n => n.UserId == userId.Value);
        }

        return await query
            .OrderByDescending(n => n.ScheduledAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Archive old notifications
    /// </summary>
    public async Task<int> ArchiveOldNotificationsAsync(
        DateTime olderThan, 
        int batchSize = 1000, 
        CancellationToken cancellationToken = default)
    {
        var notifications = await _context.Notifications
            .Where(n => n.CreatedAt < olderThan)
            .Take(batchSize)
            .ToListAsync(cancellationToken);

        if (!notifications.Any())
        {
            return 0;
        }

        // Move to archive table (would be implemented based on archiving strategy)
        _context.Notifications.RemoveRange(notifications);
        
        return notifications.Count;
    }
}
