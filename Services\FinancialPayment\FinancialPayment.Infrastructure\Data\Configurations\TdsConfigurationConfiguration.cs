using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FinancialPayment.Domain.Entities;
using FinancialPayment.Domain.ValueObjects;

namespace FinancialPayment.Infrastructure.Data.Configurations;

public class TdsConfigurationConfiguration : IEntityTypeConfiguration<TdsConfiguration>
{
    public void Configure(EntityTypeBuilder<TdsConfiguration> builder)
    {
        builder.ToTable("TdsConfigurations");

        builder.HasKey(tc => tc.Id);

        builder.Property(tc => tc.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(tc => tc.Description)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(tc => tc.Section)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tc => tc.EntityType)
            .IsRequired()
            .HasConversion<int>();

        // Configure TaxRate value object
        builder.OwnsOne(tc => tc.TaxRate, taxRate =>
        {
            taxRate.Property(tr => tr.Rate)
                .HasColumnName("TaxRate")
                .HasPrecision(5, 4);
            taxRate.Property(tr => tr.CalculationMethod)
                .HasColumnName("TaxCalculationMethod")
                .HasConversion<int>();
            taxRate.Property(tr => tr.EffectiveFrom)
                .HasColumnName("TaxEffectiveFrom");
            taxRate.Property(tr => tr.EffectiveTo)
                .HasColumnName("TaxEffectiveTo");
        });

        // Configure TdsThreshold value object
        builder.OwnsOne(tc => tc.Threshold, threshold =>
        {
            threshold.OwnsOne(t => t.ThresholdAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("ThresholdAmount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("ThresholdCurrency")
                    .HasMaxLength(3);
            });
            threshold.OwnsOne(t => t.AnnualThresholdAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("AnnualThresholdAmount")
                    .HasPrecision(18, 2);
                money.Property(m => m.Currency)
                    .HasColumnName("AnnualThresholdCurrency")
                    .HasMaxLength(3);
            });
            threshold.Property(t => t.EntityType)
                .HasColumnName("ThresholdEntityType")
                .HasConversion<int>();
            threshold.Property(t => t.HasPan)
                .HasColumnName("ThresholdHasPan");
        });

        builder.Property(tc => tc.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(tc => tc.RequiresPan)
            .IsRequired();

        builder.Property(tc => tc.HigherRateWithoutPan)
            .HasPrecision(5, 4);

        builder.Property(tc => tc.SpecialConditions)
            .HasMaxLength(1000);

        builder.Property(tc => tc.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tc => tc.ModifiedBy)
            .HasMaxLength(100);

        builder.Property(tc => tc.ModifiedAt);

        // Configure relationships
        builder.HasMany(tc => tc.History)
            .WithOne()
            .HasForeignKey(h => h.TdsConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure indexes
        builder.HasIndex(tc => tc.Name)
            .IsUnique();
        builder.HasIndex(tc => tc.Section);
        builder.HasIndex(tc => tc.EntityType);
        builder.HasIndex(tc => tc.Status);
        builder.HasIndex(tc => tc.RequiresPan);
        builder.HasIndex(tc => new { tc.Section, tc.EntityType });
        builder.HasIndex(tc => new { tc.Section, tc.EntityType, tc.Status });
    }
}

public class TdsConfigurationHistoryConfiguration : IEntityTypeConfiguration<TdsConfigurationHistory>
{
    public void Configure(EntityTypeBuilder<TdsConfigurationHistory> builder)
    {
        builder.ToTable("TdsConfigurationHistories");

        builder.HasKey(tch => tch.Id);

        builder.Property(tch => tch.TdsConfigurationId)
            .IsRequired();

        builder.Property(tch => tch.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tch => tch.Details)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(tch => tch.ModifiedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(tch => tch.ModifiedAt)
            .IsRequired();

        // Configure indexes
        builder.HasIndex(tch => tch.TdsConfigurationId);
        builder.HasIndex(tch => tch.ModifiedAt);
        builder.HasIndex(tch => tch.ModifiedBy);
    }
}
