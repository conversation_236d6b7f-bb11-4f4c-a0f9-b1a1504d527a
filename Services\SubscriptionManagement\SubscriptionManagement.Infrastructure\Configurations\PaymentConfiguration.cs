using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class PaymentConfiguration : IEntityTypeConfiguration<Payment>
    {
        public void Configure(EntityTypeBuilder<Payment> builder)
        {
            builder.ToTable("Payments");

            builder.HasKey(p => p.Id);

            builder.Property(p => p.SubscriptionId)
                .IsRequired();

            builder.Property(p => p.UserId)
                .IsRequired();

            builder.Property(p => p.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(p => p.PaymentGatewayTransactionId)
                .HasMaxLength(100);

            builder.Property(p => p.PaymentMethod)
                .HasMaxLength(50);

            builder.Property(p => p.ProcessedAt);

            builder.Property(p => p.FailedAt);

            builder.Property(p => p.FailureReason)
                .HasMaxLength(500);

            builder.Property(p => p.PaymentGatewayResponse)
                .HasMaxLength(2000);

            builder.Property(p => p.RetryCount)
                .IsRequired();

            builder.Property(p => p.NextRetryAt);

            // Configure Money value object for Amount
            builder.OwnsOne(p => p.Amount, amount =>
            {
                amount.Property(m => m.Amount)
                    .HasColumnName("Amount")
                    .HasPrecision(18, 2)
                    .IsRequired();

                amount.Property(m => m.Currency)
                    .HasColumnName("Currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            builder.Property(p => p.CreatedAt)
                .IsRequired();

            builder.Property(p => p.UpdatedAt);

            // Indexes
            builder.HasIndex(p => p.SubscriptionId);
            builder.HasIndex(p => p.UserId);
            builder.HasIndex(p => p.Status);
            builder.HasIndex(p => p.PaymentGatewayTransactionId);
            builder.HasIndex(p => p.ProcessedAt);
        }
    }
}
