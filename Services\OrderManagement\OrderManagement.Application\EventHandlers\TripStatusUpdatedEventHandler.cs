using MediatR;
using Microsoft.Extensions.Logging;
using OrderManagement.Application.Interfaces;
using OrderManagement.Domain.Enums;
using OrderManagement.Domain.Exceptions;

namespace OrderManagement.Application.EventHandlers;

// Integration event from Trip Management Service
public record TripStatusUpdatedIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public string PreviousStatus { get; init; } = string.Empty;
    public string NewStatus { get; init; } = string.Empty;
    public DateTime UpdatedAt { get; init; }
    public string? Notes { get; init; }
    public Guid? DriverId { get; init; }
    public Guid? VehicleId { get; init; }
    public Guid CarrierId { get; init; }
}

public record TripCompletedIntegrationEvent : INotification
{
    public Guid TripId { get; init; }
    public Guid OrderId { get; init; }
    public string TripNumber { get; init; } = string.Empty;
    public DateTime CompletedAt { get; init; }
    public string? CompletionNotes { get; init; }
    public Guid DriverId { get; init; }
    public Guid VehicleId { get; init; }
    public Guid CarrierId { get; init; }
    public decimal? ActualDistanceKm { get; init; }
    public TimeSpan? ActualDuration { get; init; }
}

public class TripStatusUpdatedEventHandler : INotificationHandler<TripStatusUpdatedIntegrationEvent>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TripStatusUpdatedEventHandler> _logger;

    public TripStatusUpdatedEventHandler(
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<TripStatusUpdatedEventHandler> logger)
    {
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(TripStatusUpdatedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing trip status updated event for order {OrderId}, trip {TripId}: {PreviousStatus} -> {NewStatus}", 
            notification.OrderId, notification.TripId, notification.PreviousStatus, notification.NewStatus);

        try
        {
            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip status event", notification.OrderId);
                throw new OrderManagementException("Order not found");
            }

            // Update order status based on trip status
            var orderStatusChanged = false;
            var previousOrderStatus = order.Status;

            switch (notification.NewStatus.ToLower())
            {
                case "started":
                case "inprogress":
                case "en_route":
                    if (order.Status == OrderStatus.Confirmed)
                    {
                        order.StartProgress();
                        orderStatusChanged = true;
                    }
                    break;

                case "completed":
                    if (order.Status == OrderStatus.InProgress)
                    {
                        order.Complete();
                        orderStatusChanged = true;
                    }
                    break;

                case "cancelled":
                    if (order.Status != OrderStatus.Completed && order.Status != OrderStatus.Cancelled)
                    {
                        order.Cancel($"Trip cancelled: {notification.Notes}");
                        orderStatusChanged = true;
                    }
                    break;
            }

            if (orderStatusChanged)
            {
                await _orderRepository.UpdateAsync(order, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Publish integration event for order status change
                await _messageBroker.PublishAsync("order.status.updated.from.trip", new
                {
                    OrderId = order.Id,
                    OrderNumber = order.OrderNumber,
                    PreviousStatus = previousOrderStatus.ToString(),
                    NewStatus = order.Status.ToString(),
                    TripId = notification.TripId,
                    TripNumber = notification.TripNumber,
                    TripStatus = notification.NewStatus,
                    UpdatedAt = notification.UpdatedAt,
                    Notes = notification.Notes
                }, cancellationToken);

                _logger.LogInformation("Updated order {OrderId} status from {PreviousStatus} to {NewStatus} based on trip status", 
                    order.Id, previousOrderStatus, order.Status);
            }
            else
            {
                _logger.LogDebug("No order status change required for trip status update: {TripStatus}", notification.NewStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip status event for order {OrderId}", notification.OrderId);
            throw;
        }
    }
}

public class TripCompletedEventHandler : INotificationHandler<TripCompletedIntegrationEvent>
{
    private readonly IOrderRepository _orderRepository;
    private readonly IMessageBroker _messageBroker;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<TripCompletedEventHandler> _logger;

    public TripCompletedEventHandler(
        IOrderRepository orderRepository,
        IMessageBroker messageBroker,
        IUnitOfWork unitOfWork,
        ILogger<TripCompletedEventHandler> logger)
    {
        _orderRepository = orderRepository;
        _messageBroker = messageBroker;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(TripCompletedIntegrationEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing trip completed event for order {OrderId}, trip {TripId}", 
            notification.OrderId, notification.TripId);

        try
        {
            var order = await _orderRepository.GetByIdAsync(notification.OrderId, cancellationToken);
            if (order == null)
            {
                _logger.LogWarning("Order {OrderId} not found for trip completed event", notification.OrderId);
                throw new OrderManagementException("Order not found");
            }

            // Complete the order if it's in progress
            if (order.Status == OrderStatus.InProgress)
            {
                order.Complete();

                await _orderRepository.UpdateAsync(order, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Publish integration event
                await _messageBroker.PublishAsync("order.completed.from.trip", new
                {
                    OrderId = order.Id,
                    OrderNumber = order.OrderNumber,
                    CompletedAt = order.CompletedAt,
                    TripId = notification.TripId,
                    TripNumber = notification.TripNumber,
                    TripCompletedAt = notification.CompletedAt,
                    DriverId = notification.DriverId,
                    VehicleId = notification.VehicleId,
                    CarrierId = notification.CarrierId,
                    ActualDistanceKm = notification.ActualDistanceKm,
                    ActualDuration = notification.ActualDuration?.TotalMinutes,
                    CompletionNotes = notification.CompletionNotes
                }, cancellationToken);

                _logger.LogInformation("Completed order {OrderId} based on trip completion", order.Id);
            }
            else
            {
                _logger.LogWarning("Order {OrderId} is not in progress (status: {Status}), cannot complete from trip", 
                    order.Id, order.Status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trip completed event for order {OrderId}", notification.OrderId);
            throw;
        }
    }
}
