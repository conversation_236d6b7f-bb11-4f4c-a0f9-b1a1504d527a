using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class TaxExemptionRepository : ITaxExemptionRepository
    {
        private readonly SubscriptionDbContext _context;

        public TaxExemptionRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<TaxExemption?> GetByIdAsync(Guid id)
        {
            return await _context.TaxExemptions
                .FirstOrDefaultAsync(te => te.Id == id);
        }

        public async Task<List<TaxExemption>> GetByUserIdAsync(Guid userId)
        {
            return await _context.TaxExemptions
                .Where(te => te.UserId == userId)
                .OrderByDescending(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetActiveByUserIdAsync(Guid userId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.TaxExemptions
                .Where(te => te.UserId == userId && 
                            te.IsActive && 
                            te.ValidFrom <= currentDate && 
                            te.ValidTo >= currentDate)
                .OrderByDescending(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetByExemptionNumberAsync(string exemptionNumber)
        {
            return await _context.TaxExemptions
                .Where(te => te.ExemptionNumber == exemptionNumber)
                .OrderByDescending(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetExpiringExemptionsAsync(int daysThreshold = 30)
        {
            var thresholdDate = DateTime.UtcNow.AddDays(daysThreshold);
            return await _context.TaxExemptions
                .Where(te => te.IsActive && 
                            te.ValidTo <= thresholdDate && 
                            te.ValidTo >= DateTime.UtcNow)
                .OrderBy(te => te.ValidTo)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetUnverifiedExemptionsAsync()
        {
            return await _context.TaxExemptions
                .Where(te => te.IsActive && te.VerifiedAt == null)
                .OrderBy(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetByRegionAsync(string region)
        {
            var upperRegion = region.ToUpperInvariant();
            var currentDate = DateTime.UtcNow;
            
            return await _context.TaxExemptions
                .Where(te => te.IsActive && 
                            te.ValidFrom <= currentDate && 
                            te.ValidTo >= currentDate &&
                            te.ApplicableRegions.Contains(upperRegion))
                .OrderByDescending(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<TaxExemption>> GetByTaxTypeAsync(TaxType taxType)
        {
            var currentDate = DateTime.UtcNow;
            
            return await _context.TaxExemptions
                .Where(te => te.IsActive && 
                            te.ValidFrom <= currentDate && 
                            te.ValidTo >= currentDate &&
                            te.ExemptTaxTypes.Contains(taxType))
                .OrderByDescending(te => te.CreatedAt)
                .ToListAsync();
        }

        public async Task<TaxExemption> AddAsync(TaxExemption taxExemption)
        {
            _context.TaxExemptions.Add(taxExemption);
            await _context.SaveChangesAsync();
            return taxExemption;
        }

        public async Task<TaxExemption> UpdateAsync(TaxExemption taxExemption)
        {
            _context.TaxExemptions.Update(taxExemption);
            await _context.SaveChangesAsync();
            return taxExemption;
        }

        public async Task DeleteAsync(Guid id)
        {
            var taxExemption = await GetByIdAsync(id);
            if (taxExemption != null)
            {
                _context.TaxExemptions.Remove(taxExemption);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.TaxExemptions
                .AnyAsync(te => te.Id == id);
        }

        public async Task<bool> IsUserExemptAsync(Guid userId, TaxType taxType, string region, DateTime? date = null)
        {
            var checkDate = date ?? DateTime.UtcNow;
            var upperRegion = region.ToUpperInvariant();
            
            return await _context.TaxExemptions
                .AnyAsync(te => te.UserId == userId &&
                               te.IsActive &&
                               te.ValidFrom <= checkDate &&
                               te.ValidTo >= checkDate &&
                               te.ExemptTaxTypes.Contains(taxType) &&
                               te.ApplicableRegions.Contains(upperRegion) &&
                               te.VerifiedAt != null);
        }

        public async Task<List<TaxExemption>> SearchAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var query = _context.TaxExemptions.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLowerInvariant();
                query = query.Where(te => 
                    te.ExemptionNumber.ToLower().Contains(lowerSearchTerm) ||
                    te.ExemptionType.ToLower().Contains(lowerSearchTerm) ||
                    te.IssuingAuthority.ToLower().Contains(lowerSearchTerm));
            }

            return await query
                .OrderByDescending(te => te.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync()
        {
            return await _context.TaxExemptions.CountAsync();
        }

        public async Task<int> GetCountByUserIdAsync(Guid userId)
        {
            return await _context.TaxExemptions
                .CountAsync(te => te.UserId == userId);
        }
    }
}
