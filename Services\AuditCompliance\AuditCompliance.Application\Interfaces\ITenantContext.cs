namespace AuditCompliance.Application.Interfaces;

/// <summary>
/// Interface for accessing current tenant context
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Get the current tenant ID
    /// </summary>
    Guid? TenantId { get; }

    /// <summary>
    /// Get the current tenant code
    /// </summary>
    string? TenantCode { get; }

    /// <summary>
    /// Get the current tenant name
    /// </summary>
    string? TenantName { get; }

    /// <summary>
    /// Check if the current context has a tenant
    /// </summary>
    bool HasTenant { get; }

    /// <summary>
    /// Set the current tenant context
    /// </summary>
    void SetTenant(Guid tenantId, string tenantCode, string tenantName);

    /// <summary>
    /// Clear the current tenant context
    /// </summary>
    void ClearTenant();

    /// <summary>
    /// Get tenant-specific connection string
    /// </summary>
    string? GetTenantConnectionString();

    /// <summary>
    /// Get tenant-specific configuration value
    /// </summary>
    T? GetTenantConfig<T>(string key, T? defaultValue = default);

    /// <summary>
    /// Check if tenant has access to a specific feature
    /// </summary>
    bool <PERSON>eat<PERSON>ccess(string feature);

    /// <summary>
    /// Get tenant resource limits
    /// </summary>
    TenantLimitsDto GetTenantLimits();
}

/// <summary>
/// Tenant limits DTO
/// </summary>
public class TenantLimitsDto
{
    public int MaxUsers { get; set; }
    public int MaxAuditLogsPerMonth { get; set; }
    public int MaxComplianceReports { get; set; }
    public int MaxServiceProviders { get; set; }
    public long MaxStorageBytes { get; set; }
    public int MaxApiCallsPerHour { get; set; }
    public int MaxConcurrentSessions { get; set; }
    public bool IsUnlimited(string resource) => GetLimit(resource) == -1;
    
    private int GetLimit(string resource) => resource.ToLower() switch
    {
        "users" => MaxUsers,
        "auditlogs" => MaxAuditLogsPerMonth,
        "reports" => MaxComplianceReports,
        "providers" => MaxServiceProviders,
        "apicalls" => MaxApiCallsPerHour,
        "sessions" => MaxConcurrentSessions,
        _ => 0
    };
}

/// <summary>
/// Interface for tenant management service
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Get tenant by ID
    /// </summary>
    Task<TenantDto?> GetTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get tenant by code
    /// </summary>
    Task<TenantDto?> GetTenantByCodeAsync(string tenantCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all tenants with pagination
    /// </summary>
    Task<PagedResult<TenantDto>> GetTenantsAsync(
        int pageNumber = 1, 
        int pageSize = 50, 
        string? searchTerm = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a new tenant
    /// </summary>
    Task<Guid> CreateTenantAsync(CreateTenantDto createTenantDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update tenant details
    /// </summary>
    Task<bool> UpdateTenantAsync(Guid tenantId, UpdateTenantDto updateTenantDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update tenant configuration
    /// </summary>
    Task<bool> UpdateTenantConfigurationAsync(
        Guid tenantId, 
        TenantConfigurationDto configuration, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update tenant plan
    /// </summary>
    Task<bool> UpdateTenantPlanAsync(Guid tenantId, TenantPlan newPlan, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deactivate tenant
    /// </summary>
    Task<bool> DeactivateTenantAsync(Guid tenantId, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reactivate tenant
    /// </summary>
    Task<bool> ReactivateTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Suspend tenant
    /// </summary>
    Task<bool> SuspendTenantAsync(Guid tenantId, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// Enable compliance standard for tenant
    /// </summary>
    Task<bool> EnableComplianceStandardAsync(
        Guid tenantId, 
        string standard, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Disable compliance standard for tenant
    /// </summary>
    Task<bool> DisableComplianceStandardAsync(
        Guid tenantId, 
        string standard, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update tenant compliance settings
    /// </summary>
    Task<bool> UpdateComplianceSettingsAsync(
        Guid tenantId, 
        Dictionary<string, object> settings, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get tenant usage statistics
    /// </summary>
    Task<TenantUsageDto> GetTenantUsageAsync(Guid tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate tenant limits
    /// </summary>
    Task<TenantLimitValidationResult> ValidateTenantLimitsAsync(
        Guid tenantId, 
        string resourceType, 
        int requestedAmount = 1,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Tenant DTO
/// </summary>
public class TenantDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Plan { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public TenantConfigurationDto Configuration { get; set; } = new();
    public List<string> EnabledComplianceStandards { get; set; } = new();
    public TenantLimitsDto Limits { get; set; } = new();
}

/// <summary>
/// Create tenant DTO
/// </summary>
public class CreateTenantDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Plan { get; set; } = "Standard";
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public TenantConfigurationDto? Configuration { get; set; }
    public List<string>? EnabledComplianceStandards { get; set; }
}

/// <summary>
/// Update tenant DTO
/// </summary>
public class UpdateTenantDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? BillingEmail { get; set; }
}

/// <summary>
/// Tenant configuration DTO
/// </summary>
public class TenantConfigurationDto
{
    public List<string> EnabledFeatures { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public string TimeZone { get; set; } = "UTC";
    public string DateFormat { get; set; } = "yyyy-MM-dd";
    public string Currency { get; set; } = "USD";
    public bool EnableAuditLogging { get; set; } = true;
    public bool EnableRealTimeNotifications { get; set; } = true;
    public bool EnableAdvancedAnalytics { get; set; } = false;
    public int DataRetentionDays { get; set; } = 2555;
    public bool EnableMobileAccess { get; set; } = true;
    public bool EnableApiAccess { get; set; } = true;
    public List<string> AllowedIpRanges { get; set; } = new();
    public bool RequireTwoFactorAuth { get; set; } = false;
}

/// <summary>
/// Tenant usage DTO
/// </summary>
public class TenantUsageDto
{
    public Guid TenantId { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public int CurrentUsers { get; set; }
    public int AuditLogsThisMonth { get; set; }
    public int ComplianceReportsThisMonth { get; set; }
    public int ServiceProvidersCount { get; set; }
    public long StorageBytesUsed { get; set; }
    public int ApiCallsThisHour { get; set; }
    public int CurrentSessions { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Tenant limit validation result
/// </summary>
public class TenantLimitValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public int CurrentUsage { get; set; }
    public int Limit { get; set; }
    public int Available { get; set; }
    public bool IsUnlimited { get; set; }
    public string ResourceType { get; set; } = string.Empty;
}

/// <summary>
/// Paged result for tenant queries
/// </summary>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}
