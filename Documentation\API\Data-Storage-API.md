# 📁 Data & Storage Service API Documentation

## 📋 Overview

The Data & Storage Service provides comprehensive file management, document processing, CDN integration, and advanced search capabilities. This service is 75% complete with core storage features production-ready and advanced processing capabilities in development.

**Base URL**: `/api/v1/storage` (via API Gateway)  
**Direct URL**: `http://localhost:5010`  
**Authentication**: Required for all endpoints  
**Authorization**: Role-based access control with document ownership

## 🔐 Authentication & Permissions

### Required Permissions

| Operation          | Permission               | Roles                   |
| ------------------ | ------------------------ | ----------------------- |
| Upload documents   | `storage.upload`         | All authenticated users |
| View own documents | `storage.read.own`       | All authenticated users |
| View all documents | `storage.read.all`       | Admin, Auditor          |
| Delete documents   | `storage.delete`         | Document owner, Admin   |
| Manage storage     | `storage.manage`         | Admin                   |
| Access analytics   | `storage.analytics.read` | Admin, Manager          |
| Configure CDN      | `storage.cdn.manage`     | Admin                   |
| Archive management | `storage.archive.manage` | Admin                   |

## 📊 Core Endpoints

### 1. Document Management

#### Upload Document

```http
POST /api/v1/storage/documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": [binary file data],
  "title": "Contract Agreement - ABC Corp",
  "description": "Service agreement for transportation services",
  "documentType": "Contract",
  "category": "Legal",
  "accessLevel": "Private",
  "expiresAt": "2024-12-31T23:59:59Z",
  "tags": {
    "client": "ABC Corp",
    "department": "Legal",
    "priority": "High"
  },
  "externalReference": "CONTRACT-2024-001",
  "preferredStorageProvider": "AzureBlobStorage"
}
```

**Response:**

```json
{
  "id": "doc-uuid",
  "title": "Contract Agreement - ABC Corp",
  "description": "Service agreement for transportation services",
  "documentType": "Contract",
  "category": "Legal",
  "status": "Uploaded",
  "accessLevel": "Private",
  "fileMetadata": {
    "fileName": "contract_abc_corp.pdf",
    "originalFileName": "Contract Agreement.pdf",
    "contentType": "application/pdf",
    "fileSize": 2048576,
    "fileSizeFormatted": "2.00 MB",
    "checksum": "sha256:abc123...",
    "uploadedAt": "2024-01-15T10:30:00Z"
  },
  "storageLocation": {
    "provider": "AzureBlobStorage",
    "containerName": "documents",
    "filePath": "legal/2024/01/doc-uuid.pdf",
    "cdnUrl": "https://cdn.tli.com/documents/doc-uuid.pdf",
    "region": "East US",
    "fullPath": "https://tlistorage.blob.core.windows.net/documents/legal/2024/01/doc-uuid.pdf",
    "accessUrl": "/api/v1/storage/documents/doc-uuid/download"
  },
  "ownerId": "user-uuid",
  "ownerType": "Shipper",
  "version": 1,
  "isLatestVersion": true,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2024-12-31T23:59:59Z",
  "tags": {
    "client": "ABC Corp",
    "department": "Legal",
    "priority": "High"
  },
  "externalReference": "CONTRACT-2024-001",
  "processingStatus": "Completed",
  "thumbnailUrl": "/api/v1/storage/documents/doc-uuid/thumbnail"
}
```

#### Upload Multiple Documents

```http
POST /api/v1/storage/documents/upload-multiple
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "files": [binary file data array],
  "documents": [
    {
      "title": "Invoice #001",
      "documentType": "Invoice",
      "category": "Financial"
    },
    {
      "title": "Receipt #001",
      "documentType": "Receipt",
      "category": "Financial"
    }
  ],
  "continueOnError": true
}
```

#### Get Document

```http
GET /api/v1/storage/documents/{documentId}
Authorization: Bearer <token>
```

#### Download Document

```http
GET /api/v1/storage/documents/{documentId}/download
Authorization: Bearer <token>
```

**Query Parameters:**

- `version`: Specific version to download
- `inline`: Display inline in browser (default: false)
- `thumbnail`: Download thumbnail instead of full document

#### Get Document Thumbnail

```http
GET /api/v1/storage/documents/{documentId}/thumbnail
Authorization: Bearer <token>
```

**Query Parameters:**

- `width`: Thumbnail width (default: 200)
- `height`: Thumbnail height (default: 200)
- `format`: Image format (`png|jpg|webp`)

#### Search Documents

```http
GET /api/v1/storage/documents/search
Authorization: Bearer <token>
```

**Query Parameters:**

- `query`: Search query string
- `documentType`: Filter by document type
- `category`: Filter by category
- `status`: Filter by status
- `ownerId`: Filter by owner
- `fromDate`: Filter from date
- `toDate`: Filter to date
- `tags`: Filter by tags (comma-separated)
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `sortBy`: Sort field (`title|createdAt|fileSize|updatedAt`)
- `sortDirection`: Sort direction (`asc|desc`)

**Response:**

```json
{
  "data": [
    {
      "id": "doc-uuid",
      "title": "Contract Agreement - ABC Corp",
      "documentType": "Contract",
      "category": "Legal",
      "status": "Uploaded",
      "fileMetadata": {
        "fileName": "contract_abc_corp.pdf",
        "contentType": "application/pdf",
        "fileSizeFormatted": "2.00 MB"
      },
      "createdAt": "2024-01-15T10:30:00Z",
      "thumbnailUrl": "/api/v1/storage/documents/doc-uuid/thumbnail"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "searchMetadata": {
    "query": "contract ABC",
    "searchTime": 45,
    "totalMatches": 150,
    "facets": {
      "documentTypes": {
        "Contract": 120,
        "Invoice": 20,
        "Receipt": 10
      },
      "categories": {
        "Legal": 120,
        "Financial": 30
      }
    }
  }
}
```

#### Update Document Metadata

```http
PUT /api/v1/storage/documents/{documentId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Contract Agreement - ABC Corp",
  "description": "Updated service agreement for transportation services",
  "tags": {
    "client": "ABC Corp",
    "department": "Legal",
    "priority": "High",
    "status": "Reviewed"
  }
}
```

#### Delete Document

```http
DELETE /api/v1/storage/documents/{documentId}
Authorization: Bearer <token>
```

**Query Parameters:**

- `permanent`: Permanently delete (default: false - moves to trash)

### 2. Document Processing

#### Extract Text from Document

```http
POST /api/v1/storage/documents/{documentId}/extract-text
Authorization: Bearer <token>
```

**Response:**

```json
{
  "documentId": "doc-uuid",
  "extractedText": "This is the extracted text content from the document...",
  "confidence": 95.5,
  "language": "en",
  "pageCount": 5,
  "processingTime": 2.3,
  "extractedAt": "2024-01-15T10:30:00Z"
}
```

#### Perform OCR on Document

```http
POST /api/v1/storage/documents/{documentId}/ocr
Authorization: Bearer <token>
Content-Type: application/json

{
  "language": "en",
  "enhanceImage": true,
  "extractTables": true,
  "detectHandwriting": false
}
```

**Response:**

```json
{
  "documentId": "doc-uuid",
  "ocrResults": {
    "text": "Extracted text from OCR...",
    "confidence": 92.8,
    "pages": [
      {
        "pageNumber": 1,
        "text": "Page 1 content...",
        "confidence": 94.2,
        "boundingBoxes": [
          {
            "text": "Invoice Number",
            "x": 100,
            "y": 50,
            "width": 120,
            "height": 20,
            "confidence": 98.5
          }
        ]
      }
    ],
    "tables": [
      {
        "pageNumber": 1,
        "rows": 5,
        "columns": 3,
        "data": [
          ["Item", "Quantity", "Price"],
          ["Service A", "1", "1000.00"]
        ]
      }
    ]
  },
  "processingTime": 5.2,
  "processedAt": "2024-01-15T10:30:00Z"
}
```

#### Convert Document Format

```http
POST /api/v1/storage/documents/{documentId}/convert
Authorization: Bearer <token>
Content-Type: application/json

{
  "targetFormat": "pdf",
  "quality": "high",
  "preserveFormatting": true,
  "watermark": {
    "text": "TLI Transport",
    "position": "bottom-right",
    "opacity": 0.3
  }
}
```

#### Analyze Document

```http
POST /api/v1/storage/documents/{documentId}/analyze
Authorization: Bearer <token>
```

**Response:**

```json
{
  "documentId": "doc-uuid",
  "analysis": {
    "documentType": "Invoice",
    "confidence": 96.8,
    "language": "en",
    "pageCount": 2,
    "hasImages": true,
    "hasTables": true,
    "hasSignatures": false,
    "complexity": "Moderate",
    "readabilityScore": 85.2,
    "extractedEntities": [
      {
        "type": "Amount",
        "value": "₹25,000.00",
        "confidence": 98.5,
        "position": {
          "page": 1,
          "x": 400,
          "y": 300
        }
      },
      {
        "type": "Date",
        "value": "2024-01-15",
        "confidence": 97.2,
        "position": {
          "page": 1,
          "x": 100,
          "y": 150
        }
      }
    ],
    "metadata": {
      "author": "TLI Transport",
      "creationDate": "2024-01-15T10:00:00Z",
      "modificationDate": "2024-01-15T10:30:00Z",
      "subject": "Transportation Invoice",
      "keywords": ["transport", "invoice", "logistics"]
    }
  },
  "analyzedAt": "2024-01-15T10:30:00Z"
}
```

### 3. Role-Specific Document Endpoints

#### Shipper Documents

##### Upload E-Way Bill

```http
POST /api/v1/storage/shipper/eway-bills
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": [binary file data],
  "ewayBillNumber": "EWB123456789",
  "relatedOrderId": "order-uuid",
  "validFrom": "2024-01-15T00:00:00Z",
  "validTo": "2024-01-20T23:59:59Z"
}
```

##### Get Shipper Documents

```http
GET /api/v1/storage/shipper/documents
Authorization: Bearer <token>
```

#### Carrier Documents

##### Upload Pickup Confirmation Photo

```http
POST /api/v1/storage/carrier/pickup-photos
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": [binary file data],
  "tripId": "trip-uuid",
  "location": {
    "latitude": 19.0760,
    "longitude": 72.8777,
    "address": "Mumbai Airport, Terminal 2"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "notes": "Cargo loaded successfully"
}
```

##### Submit Proof of Delivery

```http
POST /api/v1/storage/carrier/proof-of-delivery
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "tripId": "trip-uuid",
  "deliveryPhoto": [binary file data],
  "digitalSignature": [binary signature data],
  "recipientName": "John Doe",
  "recipientId": "ID123456789",
  "deliveryNotes": "Package delivered in good condition",
  "supportingDocuments": [binary file data array]
}
```

### 4. CDN & Media Management

#### Configure CDN Endpoint

```http
POST /api/v1/storage/cdn/endpoints
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "TLI Documents CDN",
  "provider": "Azure",
  "originUrl": "https://tlistorage.blob.core.windows.net",
  "customDomain": "cdn.tli.com",
  "cachePolicy": {
    "defaultTtl": "24h",
    "maxTtl": "365d",
    "cacheRules": [
      {
        "pathPattern": "*.pdf",
        "ttl": "7d"
      },
      {
        "pathPattern": "*.jpg",
        "ttl": "30d"
      }
    ]
  },
  "compressionEnabled": true,
  "geoRestrictions": {
    "type": "whitelist",
    "countries": ["IN", "US", "GB"]
  }
}
```

#### Get CDN Analytics

```http
GET /api/v1/storage/cdn/analytics
Authorization: Bearer <token>
```

**Query Parameters:**

- `endpointId`: CDN endpoint ID
- `fromDate`: Start date for analytics
- `toDate`: End date for analytics
- `granularity`: Time granularity (`hourly|daily|weekly`)

**Response:**

```json
{
  "endpointId": "cdn-endpoint-uuid",
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "metrics": {
    "totalRequests": 1500000,
    "totalBandwidth": **********,
    "cacheHitRate": 85.5,
    "averageResponseTime": 120,
    "errorRate": 0.02
  },
  "topFiles": [
    {
      "filePath": "/documents/contracts/contract-001.pdf",
      "requests": 15000,
      "bandwidth": 750000000,
      "cacheHitRate": 92.3
    }
  ],
  "geographicDistribution": [
    {
      "country": "IN",
      "requests": 900000,
      "bandwidth": 1500000000,
      "percentage": 60.0
    },
    {
      "country": "US",
      "requests": 450000,
      "bandwidth": 750000000,
      "percentage": 30.0
    }
  ],
  "trends": [
    {
      "date": "2024-01-01",
      "requests": 48000,
      "bandwidth": 80000000,
      "cacheHitRate": 84.2
    }
  ]
}
```

#### Invalidate CDN Cache

```http
POST /api/v1/storage/cdn/invalidate
Authorization: Bearer <token>
Content-Type: application/json

{
  "endpointId": "cdn-endpoint-uuid",
  "paths": [
    "/documents/contracts/*",
    "/documents/invoices/invoice-123.pdf"
  ],
  "invalidationType": "purge"
}
```

### 5. Advanced Search

#### Full-Text Search

```http
POST /api/v1/storage/search/fulltext
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "transportation contract ABC Corp",
  "filters": {
    "documentTypes": ["Contract", "Invoice"],
    "categories": ["Legal", "Financial"],
    "dateRange": {
      "fromDate": "2024-01-01T00:00:00Z",
      "toDate": "2024-01-31T23:59:59Z"
    },
    "tags": ["priority:high", "client:ABC Corp"],
    "fileSize": {
      "min": 1024,
      "max": 10485760
    }
  },
  "searchOptions": {
    "includeContent": true,
    "includeMetadata": true,
    "fuzzySearch": true,
    "highlightMatches": true,
    "maxResults": 50
  },
  "sortBy": "relevance",
  "sortDirection": "desc"
}
```

**Response:**

```json
{
  "query": "transportation contract ABC Corp",
  "totalResults": 25,
  "searchTime": 125,
  "results": [
    {
      "document": {
        "id": "doc-uuid",
        "title": "Transportation Contract - ABC Corp",
        "documentType": "Contract",
        "category": "Legal",
        "relevanceScore": 95.8
      },
      "matches": [
        {
          "field": "content",
          "snippet": "This <em>transportation</em> <em>contract</em> between TLI and <em>ABC Corp</em>...",
          "position": 150,
          "confidence": 98.5
        },
        {
          "field": "title",
          "snippet": "<em>Transportation</em> <em>Contract</em> - <em>ABC Corp</em>",
          "position": 0,
          "confidence": 100.0
        }
      ]
    }
  ],
  "facets": {
    "documentTypes": {
      "Contract": 15,
      "Invoice": 8,
      "Receipt": 2
    },
    "categories": {
      "Legal": 15,
      "Financial": 10
    },
    "tags": {
      "priority:high": 12,
      "client:ABC Corp": 25,
      "department:legal": 15
    }
  },
  "suggestions": [
    "transportation agreement ABC Corp",
    "transport contract ABC Corporation"
  ]
}
```

#### Semantic Search

```http
POST /api/v1/storage/search/semantic
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "Find all documents related to vehicle maintenance and compliance",
  "similarityThreshold": 0.7,
  "maxResults": 20,
  "includeEmbeddings": false
}
```

### 6. Archive & Backup Management

#### Archive Documents

```http
POST /api/v1/storage/archive/documents
Authorization: Bearer <token>
Content-Type: application/json

{
  "documentIds": ["doc-uuid-1", "doc-uuid-2"],
  "archivePolicy": {
    "retentionPeriod": "7y",
    "storageClass": "ColdStorage",
    "compressionEnabled": true,
    "encryptionEnabled": true
  },
  "reason": "Legal retention requirement",
  "scheduledDate": "2024-02-01T00:00:00Z"
}
```

#### Get Archive Status

```http
GET /api/v1/storage/archive/status/{archiveJobId}
Authorization: Bearer <token>
```

#### Restore from Archive

```http
POST /api/v1/storage/archive/restore
Authorization: Bearer <token>
Content-Type: application/json

{
  "documentIds": ["doc-uuid-1", "doc-uuid-2"],
  "restoreType": "Standard",
  "targetStorageClass": "Standard",
  "notifyOnCompletion": true
}
```

### 7. Storage Analytics

#### Get Storage Analytics

```http
GET /api/v1/storage/analytics/overview
Authorization: Bearer <token>
```

**Query Parameters:**

- `period`: Analytics period (`7d|30d|90d|1y`)
- `groupBy`: Group by (`documentType|category|owner|storageProvider`)

**Response:**

```json
{
  "period": {
    "fromDate": "2024-01-01T00:00:00Z",
    "toDate": "2024-01-31T23:59:59Z"
  },
  "overview": {
    "totalDocuments": 15000,
    "totalStorageSize": **********0,
    "totalStorageSizeFormatted": "23.28 GB",
    "averageFileSize": 1666667,
    "averageFileSizeFormatted": "1.59 MB",
    "documentsGrowthRate": 12.5,
    "storageGrowthRate": 18.3
  },
  "byDocumentType": [
    {
      "documentType": "Contract",
      "count": 3000,
      "storageSize": **********,
      "storageSizeFormatted": "5.59 GB",
      "percentage": 24.0
    },
    {
      "documentType": "Invoice",
      "count": 5000,
      "storageSize": **********,
      "storageSizeFormatted": "4.66 GB",
      "percentage": 20.0
    }
  ],
  "byStorageProvider": [
    {
      "provider": "AzureBlobStorage",
      "count": 12000,
      "storageSize": 20000000000,
      "storageSizeFormatted": "18.63 GB",
      "percentage": 80.0,
      "cost": {
        "amount": 150.0,
        "currency": "USD"
      }
    }
  ],
  "trends": [
    {
      "date": "2024-01-01",
      "documentsUploaded": 150,
      "storageAdded": 250000000,
      "storageAddedFormatted": "238.42 MB"
    }
  ],
  "topUsers": [
    {
      "userId": "user-uuid",
      "userName": "ABC Corp",
      "documentCount": 500,
      "storageUsed": **********,
      "storageUsedFormatted": "953.67 MB"
    }
  ]
}
```

#### Get Storage Optimization Report

```http
GET /api/v1/storage/analytics/optimization
Authorization: Bearer <token>
```

**Response:**

```json
{
  "generatedAt": "2024-01-15T10:30:00Z",
  "recommendations": [
    {
      "type": "DuplicateFiles",
      "severity": "Medium",
      "description": "Found 150 duplicate files consuming 2.5 GB",
      "potentialSavings": {
        "storageSize": **********,
        "storageSizeFormatted": "2.33 GB",
        "cost": {
          "amount": 25.0,
          "currency": "USD"
        }
      },
      "action": "Review and remove duplicates"
    },
    {
      "type": "ArchiveOpportunity",
      "severity": "Low",
      "description": "500 documents older than 2 years can be archived",
      "potentialSavings": {
        "storageSize": **********,
        "storageSizeFormatted": "4.66 GB",
        "cost": {
          "amount": 40.0,
          "currency": "USD"
        }
      },
      "action": "Move to cold storage"
    }
  ],
  "duplicateAnalysis": {
    "totalDuplicates": 150,
    "duplicateGroups": [
      {
        "checksum": "sha256:abc123...",
        "fileName": "contract_template.pdf",
        "fileSize": 1048576,
        "fileSizeFormatted": "1.00 MB",
        "duplicateCount": 5,
        "documentIds": ["doc-uuid-1", "doc-uuid-2", "doc-uuid-3"]
      }
    ]
  },
  "archiveOpportunities": {
    "candidateDocuments": 500,
    "totalSize": **********,
    "totalSizeFormatted": "4.66 GB",
    "estimatedSavings": {
      "amount": 40.0,
      "currency": "USD"
    }
  }
}
```

## 🔄 Real-time Updates

### SignalR Hub: `/api/v1/storage/hub`

**Events:**

- `DocumentUploaded` - Document upload completed
- `DocumentProcessed` - Document processing completed
- `DocumentArchived` - Document archived successfully
- `StorageQuotaWarning` - Storage quota threshold reached
- `CDNCacheInvalidated` - CDN cache invalidation completed
- `BackupCompleted` - Backup operation completed
- `SearchIndexUpdated` - Search index updated

**Example Usage:**

```typescript
const connection = new signalR.HubConnectionBuilder()
  .withUrl('/api/v1/storage/hub')
  .build()

// Listen for document processing updates
connection.on('DocumentProcessed', (data) => {
  console.log('Document processed:', data)
  // Update UI with processing results
})

// Listen for storage warnings
connection.on('StorageQuotaWarning', (data) => {
  console.log('Storage quota warning:', data)
  // Show storage warning notification
})
```

## ❌ Error Responses

### Common Error Codes

| Code | Message                       | Description                             |
| ---- | ----------------------------- | --------------------------------------- |
| 400  | `INVALID_FILE_TYPE`           | File type not supported                 |
| 400  | `FILE_TOO_LARGE`              | File exceeds maximum size limit         |
| 400  | `INVALID_DOCUMENT_TYPE`       | Invalid document type specified         |
| 401  | `UNAUTHORIZED`                | Authentication required                 |
| 403  | `INSUFFICIENT_PERMISSIONS`    | Insufficient permissions for operation  |
| 403  | `DOCUMENT_ACCESS_DENIED`      | Access denied to document               |
| 404  | `DOCUMENT_NOT_FOUND`          | Document not found                      |
| 409  | `DOCUMENT_ALREADY_EXISTS`     | Document with same name already exists  |
| 413  | `STORAGE_QUOTA_EXCEEDED`      | Storage quota exceeded                  |
| 422  | `PROCESSING_FAILED`           | Document processing failed              |
| 422  | `VALIDATION_FAILED`           | Input validation failed                 |
| 429  | `RATE_LIMIT_EXCEEDED`         | Too many requests                       |
| 503  | `STORAGE_SERVICE_UNAVAILABLE` | Storage service temporarily unavailable |

### Error Response Format

```json
{
  "error": {
    "code": "FILE_TOO_LARGE",
    "message": "File exceeds maximum size limit",
    "details": "Maximum file size is 50MB, uploaded file is 75MB",
    "timestamp": "2024-01-01T00:00:00Z",
    "traceId": "trace-uuid",
    "maxFileSize": 52428800,
    "uploadedFileSize": 78643200
  }
}
```

## 📋 Integration Examples

### Frontend Integration Example (React)

```typescript
// Storage Service Client
class StorageClient {
  private apiClient = ApiClient.getInstance()

  async uploadDocument(
    file: File,
    metadata: DocumentMetadata
  ): Promise<DocumentDto> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', metadata.title)
    formData.append('documentType', metadata.documentType)
    formData.append('category', metadata.category)

    const response = await this.apiClient.axios.post(
      '/storage/documents/upload',
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent) => {
          const progress = (progressEvent.loaded / progressEvent.total) * 100
          this.onUploadProgress?.(progress)
        },
      }
    )
    return response.data
  }

  async searchDocuments(query: SearchQuery): Promise<SearchResult> {
    const response = await this.apiClient.axios.post(
      '/storage/search/fulltext',
      query
    )
    return response.data
  }

  async downloadDocument(documentId: string): Promise<Blob> {
    const response = await this.apiClient.axios.get(
      `/storage/documents/${documentId}/download`,
      {
        responseType: 'blob',
      }
    )
    return response.data
  }
}

// React Component Example
function DocumentUpload() {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const storageClient = new StorageClient()

  const handleFileUpload = async (file: File) => {
    setUploading(true)
    setProgress(0)

    storageClient.onUploadProgress = setProgress

    try {
      const document = await storageClient.uploadDocument(file, {
        title: file.name,
        documentType: 'General',
        category: 'Operational',
      })

      console.log('Document uploaded:', document)
      // Handle successful upload
    } catch (error) {
      console.error('Upload failed:', error)
      // Handle upload error
    } finally {
      setUploading(false)
      setProgress(0)
    }
  }

  return (
    <div>
      <input
        type="file"
        onChange={(e) =>
          e.target.files?.[0] && handleFileUpload(e.target.files[0])
        }
        disabled={uploading}
      />
      {uploading && (
        <div>
          <progress value={progress} max={100} />
          <span>{progress.toFixed(1)}%</span>
        </div>
      )}
    </div>
  )
}
```
