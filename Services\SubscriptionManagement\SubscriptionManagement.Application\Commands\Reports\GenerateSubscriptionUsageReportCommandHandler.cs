using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs.Reports;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Enums;
using System.Diagnostics;

namespace SubscriptionManagement.Application.Commands.Reports;

/// <summary>
/// Handler for generating subscription usage reports
/// </summary>
public class GenerateSubscriptionUsageReportCommandHandler : IRequestHandler<GenerateSubscriptionUsageReportCommand, SubscriptionUsageReportDto>
{
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly IUsageRecordRepository _usageRecordRepository;
    private readonly IPlanRepository _planRepository;
    private readonly ILogger<GenerateSubscriptionUsageReportCommandHandler> _logger;

    public GenerateSubscriptionUsageReportCommandHandler(
        ISubscriptionRepository subscriptionRepository,
        IUsageRecordRepository usageRecordRepository,
        IPlanRepository planRepository,
        ILogger<GenerateSubscriptionUsageReportCommandHandler> logger)
    {
        _subscriptionRepository = subscriptionRepository;
        _usageRecordRepository = usageRecordRepository;
        _planRepository = planRepository;
        _logger = logger;
    }

    public async Task<SubscriptionUsageReportDto> Handle(GenerateSubscriptionUsageReportCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Generating subscription usage report for user {RequestedBy}", request.RequestedBy);

            // Set default date range if not provided
            var fromDate = request.FromDate ?? DateTime.UtcNow.AddMonths(-1);
            var toDate = request.ToDate ?? DateTime.UtcNow;

            var report = new SubscriptionUsageReportDto
            {
                ReportId = Guid.NewGuid(),
                GeneratedAt = DateTime.UtcNow,
                FromDate = fromDate,
                ToDate = toDate
            };

            // Generate report sections based on request
            await GenerateReportSummary(report, request, fromDate, toDate, cancellationToken);

            if (request.IncludeUsageDetails)
            {
                await GenerateUserUsageSummaries(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeFeatureBreakdown)
            {
                await GenerateFeatureBreakdowns(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeBillingAnalysis)
            {
                await GenerateBillingAnalysis(report, request, fromDate, toDate, cancellationToken);
            }

            if (request.IncludeUsageTrends)
            {
                await GenerateUsageTrends(report, request, fromDate, toDate, cancellationToken);
            }

            // Set metadata
            stopwatch.Stop();
            report.Metadata = new ReportMetadataDto
            {
                RequestedBy = request.RequestedBy,
                RequestedByRole = request.RequestedByRole,
                RequestedAt = DateTime.UtcNow,
                IpAddress = request.IpAddress,
                UserAgent = request.UserAgent,
                GenerationTime = stopwatch.Elapsed,
                TotalRecords = report.UserUsageSummaries.Count,
                IsFiltered = HasFilters(request),
                FilterCriteria = GetFilterCriteria(request)
            };

            _logger.LogInformation("Successfully generated subscription usage report {ReportId} in {ElapsedMs}ms", 
                report.ReportId, stopwatch.ElapsedMilliseconds);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating subscription usage report");
            throw;
        }
    }

    private async Task GenerateReportSummary(SubscriptionUsageReportDto report, GenerateSubscriptionUsageReportCommand request, 
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var subscriptions = await _subscriptionRepository.GetSubscriptionsForReportAsync(
            request.UserId, request.PlanId, request.Status, fromDate, toDate, cancellationToken);

        var usageRecords = await _usageRecordRepository.GetUsageForReportAsync(
            request.UserId, request.FeatureType, fromDate, toDate, cancellationToken);

        report.Summary = new ReportSummaryDto
        {
            TotalUsers = subscriptions.Select(s => s.UserId).Distinct().Count(),
            ActiveSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Active),
            TotalUsageRecords = usageRecords.Count(),
            TotalUsageByFeature = usageRecords.GroupBy(u => u.FeatureType)
                .ToDictionary(g => g.Key, g => (long)g.Sum(u => u.UsageCount)),
            SubscriptionsByPlan = subscriptions.GroupBy(s => s.Plan.Name)
                .ToDictionary(g => g.Key, g => g.Count()),
            TotalRevenue = subscriptions.Sum(s => s.CurrentPrice.Amount),
            AverageUsagePerUser = usageRecords.Any() ? 
                (decimal)usageRecords.Average(u => u.UsageCount) : 0m,
            ReportPeriodStart = fromDate,
            ReportPeriodEnd = toDate
        };
    }

    private async Task GenerateUserUsageSummaries(SubscriptionUsageReportDto report, GenerateSubscriptionUsageReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var subscriptions = await _subscriptionRepository.GetSubscriptionsForReportAsync(
            request.UserId, request.PlanId, request.Status, fromDate, toDate, cancellationToken);

        var userSummaries = new List<UserUsageSummaryDto>();

        foreach (var subscription in subscriptions.Take(request.MaxRecords ?? 1000))
        {
            var usageRecords = await _usageRecordRepository.GetUsageByUserAsync(
                subscription.UserId, request.FeatureType, fromDate, toDate, cancellationToken);

            var summary = new UserUsageSummaryDto
            {
                UserId = subscription.UserId,
                UserName = $"User_{subscription.UserId.ToString()[..8]}", // Placeholder
                UserEmail = $"user_{subscription.UserId.ToString()[..8]}@example.com", // Placeholder
                SubscriptionId = subscription.Id,
                PlanName = subscription.Plan.Name,
                Status = subscription.Status,
                SubscriptionStartDate = subscription.StartDate,
                SubscriptionEndDate = subscription.EndDate,
                UsageByFeature = usageRecords.GroupBy(u => u.FeatureType)
                    .ToDictionary(g => g.Key, g => g.Sum(u => u.UsageCount)),
                TotalUsageCount = usageRecords.Sum(u => u.UsageCount),
                LastUsageDate = usageRecords.Any() ? usageRecords.Max(u => u.UsageDate) : DateTime.MinValue,
                BillingAmount = subscription.CurrentPrice.Amount,
                BillingCycle = subscription.BillingCycle.ToString()
            };

            // Calculate usage percentages
            foreach (var feature in subscription.Plan.Features)
            {
                if (summary.UsageByFeature.ContainsKey(feature.FeatureType))
                {
                    var usage = summary.UsageByFeature[feature.FeatureType];
                    var limit = feature.Limit ?? int.MaxValue;
                    summary.LimitsByFeature[feature.FeatureType] = limit;
                    summary.UsagePercentageByFeature[feature.FeatureType] = 
                        limit > 0 ? (double)usage / limit * 100 : 0;
                }
            }

            userSummaries.Add(summary);
        }

        report.UserUsageSummaries = userSummaries;
    }

    private async Task GenerateFeatureBreakdowns(SubscriptionUsageReportDto report, GenerateSubscriptionUsageReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var usageRecords = await _usageRecordRepository.GetUsageForReportAsync(
            request.UserId, request.FeatureType, fromDate, toDate, cancellationToken);

        var featureBreakdowns = usageRecords
            .GroupBy(u => u.FeatureType)
            .Select(g => new FeatureUsageBreakdownDto
            {
                FeatureType = g.Key,
                FeatureName = g.Key.ToString(),
                TotalUsage = g.Sum(u => u.UsageCount),
                UniqueUsers = g.Select(u => u.UserId).Distinct().Count(),
                AverageUsagePerUser = g.Average(u => u.UsageCount)
            })
            .ToList();

        report.FeatureBreakdowns = featureBreakdowns;
    }

    private async Task GenerateBillingAnalysis(SubscriptionUsageReportDto report, GenerateSubscriptionUsageReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        // Generate monthly billing analysis
        var billingAnalysis = new List<BillingPeriodAnalysisDto>();
        
        var currentDate = new DateTime(fromDate.Year, fromDate.Month, 1);
        var endDate = new DateTime(toDate.Year, toDate.Month, 1).AddMonths(1);

        while (currentDate < endDate)
        {
            var periodEnd = currentDate.AddMonths(1).AddDays(-1);
            
            var subscriptions = await _subscriptionRepository.GetSubscriptionsForReportAsync(
                request.UserId, request.PlanId, request.Status, currentDate, periodEnd, cancellationToken);

            var analysis = new BillingPeriodAnalysisDto
            {
                PeriodStart = currentDate,
                PeriodEnd = periodEnd,
                PeriodName = currentDate.ToString("MMMM yyyy"),
                ActiveSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Active),
                TotalRevenue = subscriptions.Sum(s => s.CurrentPrice.Amount),
                AverageRevenuePerUser = subscriptions.Any() ? 
                    subscriptions.Average(s => s.CurrentPrice.Amount) : 0m,
                RevenueByPlan = subscriptions.GroupBy(s => s.Plan.Name)
                    .ToDictionary(g => g.Key, g => g.Sum(s => s.CurrentPrice.Amount))
            };

            billingAnalysis.Add(analysis);
            currentDate = currentDate.AddMonths(1);
        }

        report.BillingAnalysis = billingAnalysis;
    }

    private async Task GenerateUsageTrends(SubscriptionUsageReportDto report, GenerateSubscriptionUsageReportCommand request,
        DateTime fromDate, DateTime toDate, CancellationToken cancellationToken)
    {
        var usageRecords = await _usageRecordRepository.GetUsageForReportAsync(
            request.UserId, request.FeatureType, fromDate, toDate, cancellationToken);

        var trends = usageRecords
            .GroupBy(u => u.UsageDate.Date)
            .OrderBy(g => g.Key)
            .Select(g => new UsageTrendDto
            {
                Date = g.Key,
                UsageCount = g.Sum(u => u.UsageCount),
                ActiveUsers = g.Select(u => u.UserId).Distinct().Count(),
                AverageUsagePerUser = g.Average(u => u.UsageCount)
            })
            .ToList();

        // Calculate growth percentages
        for (int i = 1; i < trends.Count; i++)
        {
            var current = trends[i];
            var previous = trends[i - 1];
            
            if (previous.UsageCount > 0)
            {
                current.GrowthPercentage = ((double)(current.UsageCount - previous.UsageCount) / previous.UsageCount) * 100;
            }
        }

        report.UsageTrends = trends;
    }

    private static bool HasFilters(GenerateSubscriptionUsageReportCommand request)
    {
        return request.UserId.HasValue || request.PlanId.HasValue || 
               request.Status.HasValue || request.FeatureType.HasValue;
    }

    private static Dictionary<string, object> GetFilterCriteria(GenerateSubscriptionUsageReportCommand request)
    {
        var criteria = new Dictionary<string, object>();
        
        if (request.UserId.HasValue) criteria["UserId"] = request.UserId.Value;
        if (request.PlanId.HasValue) criteria["PlanId"] = request.PlanId.Value;
        if (request.Status.HasValue) criteria["Status"] = request.Status.Value;
        if (request.FeatureType.HasValue) criteria["FeatureType"] = request.FeatureType.Value;
        if (request.FromDate.HasValue) criteria["FromDate"] = request.FromDate.Value;
        if (request.ToDate.HasValue) criteria["ToDate"] = request.ToDate.Value;
        
        return criteria;
    }
}
