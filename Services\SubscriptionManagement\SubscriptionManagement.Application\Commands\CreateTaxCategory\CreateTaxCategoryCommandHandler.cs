using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Interfaces;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.CreateTaxCategory
{
    public class CreateTaxCategoryCommandHandler : IRequestHandler<CreateTaxCategoryCommand, Guid>
    {
        private readonly ITaxCategoryRepository _taxCategoryRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateTaxCategoryCommandHandler> _logger;

        public CreateTaxCategoryCommandHandler(
            ITaxCategoryRepository taxCategoryRepository,
            IMessageBroker messageBroker,
            ILogger<CreateTaxCategoryCommandHandler> logger)
        {
            _taxCategoryRepository = taxCategoryRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateTaxCategoryCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Creating tax category {Name} with code {Code}", request.Name, request.Code);

            try
            {
                // Check if tax category with same code already exists
                var existingCategory = await _taxCategoryRepository.GetByCodeAsync(request.Code);
                if (existingCategory != null)
                {
                    throw new SubscriptionDomainException($"Tax category with code {request.Code} already exists");
                }

                // Create new tax category
                var taxCategory = new TaxCategory(request.Name, request.Description, request.Code);
                
                var createdCategory = await _taxCategoryRepository.AddAsync(taxCategory);

                // Publish integration event
                await _messageBroker.PublishAsync("tax.category_created", new
                {
                    CategoryId = createdCategory.Id,
                    Name = request.Name,
                    Description = request.Description,
                    Code = request.Code,
                    CreatedByUserId = request.CreatedByUserId,
                    CreatedAt = DateTime.UtcNow
                }, cancellationToken);

                _logger.LogInformation("Successfully created tax category {Id} with code {Code}", 
                    createdCategory.Id, request.Code);

                return createdCategory.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tax category {Name}", request.Name);
                throw;
            }
        }
    }
}
