using ContentManagement.Domain.Entities;
using Shared.Domain.Common;
using Shared.Domain.Common;

namespace ContentManagement.Domain.Events;

/// <summary>
/// Domain event fired when a policy document is created
/// </summary>
public record PolicyDocumentCreatedEvent(PolicyDocument PolicyDocument) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a policy document is updated
/// </summary>
public record PolicyDocumentUpdatedEvent(
    PolicyDocument PolicyDocument,
    object OldValues,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a new policy version is created
/// </summary>
public record PolicyVersionCreatedEvent(
    PolicyDocument PolicyDocument,
    string NewVersion,
    Guid CreatedBy,
    string CreatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a policy version is activated as current
/// </summary>
public record PolicyVersionActivatedEvent(
    PolicyDocument PolicyDocument,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a policy version is deactivated
/// </summary>
public record PolicyVersionDeactivatedEvent(
    PolicyDocument PolicyDocument,
    Guid UpdatedBy,
    string UpdatedByName) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Domain event fired when a user accepts a policy
/// </summary>
public record PolicyAcceptedEvent(
    PolicyDocument PolicyDocument,
    PolicyAcceptance Acceptance) : IDomainEvent
{
    public Guid Id { get; init; } = Guid.NewGuid();
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
}







