using MediatR;
using Microsoft.Extensions.Logging;
using FinancialPayment.Application.Interfaces;
using FinancialPayment.Domain.ValueObjects;
using FinancialPayment.Domain.Enums;
using Shared.Infrastructure.Interfaces;

namespace FinancialPayment.Application.Commands.CalculateCommission;

public class CalculateCommissionCommandHandler : IRequestHandler<CalculateCommissionCommand, Guid>
{
    private readonly ICommissionService _commissionService;
    private readonly ILogger<CalculateCommissionCommandHandler> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public CalculateCommissionCommandHandler(
        ICommissionService commissionService,
        ILogger<CalculateCommissionCommandHandler> logger,
        IUnitOfWork unitOfWork)
    {
        _commissionService = commissionService;
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task<Guid> Handle(CalculateCommissionCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Calculating commission for Order {OrderId}, Broker {BrokerId}",
                request.OrderId, request.BrokerId);

            // Convert commission structure
            var commissionStructure = new CommissionStructure(
                Enum.Parse<CommissionType>(request.CommissionStructure.Type, true),
                request.CommissionStructure.Rate,
                request.CommissionStructure.MinimumAmount,
                request.CommissionStructure.MaximumAmount,
                request.CommissionStructure.Description);

            var commission = await _commissionService.CalculateCommissionAsync(
                request.OrderId,
                request.BrokerId,
                request.TransportCompanyId,
                request.CarrierId,
                request.OrderAmount.ToMoney(),
                commissionStructure);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Commission {CommissionId} calculated successfully for Order {OrderId}",
                commission.Id, request.OrderId);

            return commission.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating commission for Order {OrderId}", request.OrderId);
            throw;
        }
    }
}
