using Shared.Domain.Common;
using MobileWorkflow.Domain.Enums;
using MobileWorkflow.Domain.Events;

namespace MobileWorkflow.Domain.Entities;

public class ConflictResolution : AggregateRoot
{
    public Guid SyncOperationId { get; private set; }
    public string EntityType { get; private set; }
    public string EntityId { get; private set; }
    public string ConflictType { get; private set; } // DataConflict, VersionConflict, DeleteConflict, etc.
    public Dictionary<string, object> LocalData { get; private set; }
    public Dictionary<string, object> RemoteData { get; private set; }
    public Dictionary<string, object> ConflictDetails { get; private set; }
    public string? ResolutionStrategy { get; private set; } // LocalWins, RemoteWins, Merge, Manual
    public Dictionary<string, object> ResolvedData { get; private set; }
    public string Status { get; private set; } // Pending, Resolved, Failed
    public DateTime DetectedAt { get; private set; }
    public DateTime? ResolvedAt { get; private set; }
    public Guid? ResolvedBy { get; private set; }
    public string? ResolutionNotes { get; private set; }
    public int LocalVersion { get; private set; }
    public int RemoteVersion { get; private set; }
    public int ResolvedVersion { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }

    // Navigation properties
    public virtual SyncOperation SyncOperation { get; private set; } = null!;

    private ConflictResolution() { } // EF Core

    public ConflictResolution(
        Guid syncOperationId,
        string entityType,
        string entityId,
        string conflictType,
        Dictionary<string, object> localData,
        Dictionary<string, object> remoteData,
        int localVersion,
        int remoteVersion)
    {
        SyncOperationId = syncOperationId;
        EntityType = entityType ?? throw new ArgumentNullException(nameof(entityType));
        EntityId = entityId ?? throw new ArgumentNullException(nameof(entityId));
        ConflictType = conflictType ?? throw new ArgumentNullException(nameof(conflictType));
        LocalData = localData ?? throw new ArgumentNullException(nameof(localData));
        RemoteData = remoteData ?? throw new ArgumentNullException(nameof(remoteData));
        LocalVersion = localVersion;
        RemoteVersion = remoteVersion;
        Status = "Pending";
        DetectedAt = DateTime.UtcNow;
        ConflictDetails = new Dictionary<string, object>();
        ResolvedData = new Dictionary<string, object>();
        Metadata = new Dictionary<string, object>();

        AnalyzeConflict();

        AddDomainEvent(new ConflictResolutionDetectedEvent(Id, EntityType, EntityId, ConflictType, GetConflictSeverity()));
    }

    public void ResolveWithLocalData(Guid resolvedBy, string? notes = null)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot resolve conflict with status: {Status}");

        ResolutionStrategy = "LocalWins";
        ResolvedData = new Dictionary<string, object>(LocalData);
        ResolvedVersion = LocalVersion;
        ResolvedBy = resolvedBy;
        ResolutionNotes = notes;
        Status = "Resolved";
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new ConflictResolutionResolvedEvent(Id, EntityType, EntityId, ResolutionStrategy, ResolvedBy.ToString()));
    }

    public void ResolveWithRemoteData(Guid resolvedBy, string? notes = null)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot resolve conflict with status: {Status}");

        ResolutionStrategy = "RemoteWins";
        ResolvedData = new Dictionary<string, object>(RemoteData);
        ResolvedVersion = RemoteVersion;
        ResolvedBy = resolvedBy;
        ResolutionNotes = notes;
        Status = "Resolved";
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new ConflictResolutionResolvedEvent(Id, EntityType, EntityId, ResolutionStrategy, ResolvedBy.ToString()));
    }

    public void ResolveWithMergedData(Dictionary<string, object> mergedData, Guid resolvedBy, string? notes = null)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot resolve conflict with status: {Status}");

        ResolutionStrategy = "Merge";
        ResolvedData = mergedData ?? throw new ArgumentNullException(nameof(mergedData));
        ResolvedVersion = Math.Max(LocalVersion, RemoteVersion) + 1;
        ResolvedBy = resolvedBy;
        ResolutionNotes = notes;
        Status = "Resolved";
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new ConflictResolutionResolvedEvent(Id, EntityType, EntityId, ResolutionStrategy, ResolvedBy.ToString()));
    }

    public void ResolveManually(Dictionary<string, object> customData, Guid resolvedBy, string? notes = null)
    {
        if (Status != "Pending")
            throw new InvalidOperationException($"Cannot resolve conflict with status: {Status}");

        ResolutionStrategy = "Manual";
        ResolvedData = customData ?? throw new ArgumentNullException(nameof(customData));
        ResolvedVersion = Math.Max(LocalVersion, RemoteVersion) + 1;
        ResolvedBy = resolvedBy;
        ResolutionNotes = notes;
        Status = "Resolved";
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new ConflictResolutionResolvedEvent(Id, EntityType, EntityId, ResolutionStrategy, ResolvedBy.ToString()));
    }

    public void MarkAsFailed(string reason)
    {
        Status = "Failed";
        ResolutionNotes = reason;
        ResolvedAt = DateTime.UtcNow;

        AddDomainEvent(new ConflictResolutionFailedEvent(Id, EntityType, EntityId, reason));
    }

    public Dictionary<string, object> GetMergedData()
    {
        var merged = new Dictionary<string, object>();

        // Start with local data
        foreach (var kvp in LocalData)
        {
            merged[kvp.Key] = kvp.Value;
        }

        // Overlay remote data, but preserve local changes for certain fields
        foreach (var kvp in RemoteData)
        {
            if (ShouldPreferRemoteValue(kvp.Key))
            {
                merged[kvp.Key] = kvp.Value;
            }
            else if (!merged.ContainsKey(kvp.Key))
            {
                merged[kvp.Key] = kvp.Value;
            }
        }

        return merged;
    }

    public List<string> GetConflictingFields()
    {
        var conflictingFields = new List<string>();

        foreach (var localKvp in LocalData)
        {
            if (RemoteData.TryGetValue(localKvp.Key, out var remoteValue))
            {
                if (!AreValuesEqual(localKvp.Value, remoteValue))
                {
                    conflictingFields.Add(localKvp.Key);
                }
            }
        }

        return conflictingFields;
    }

    public bool CanAutoResolve()
    {
        return ConflictType switch
        {
            "VersionConflict" => LocalVersion != RemoteVersion && GetConflictingFields().Count <= 2,
            "DataConflict" => GetConflictingFields().Count <= 1,
            "DeleteConflict" => false, // Always requires manual resolution
            _ => false
        };
    }

    public string GetAutoResolutionStrategy()
    {
        if (!CanAutoResolve())
            return "Manual";

        return ConflictType switch
        {
            "VersionConflict" when RemoteVersion > LocalVersion => "RemoteWins",
            "VersionConflict" when LocalVersion > RemoteVersion => "LocalWins",
            "DataConflict" => "Merge",
            _ => "Manual"
        };
    }

    private void AnalyzeConflict()
    {
        var conflictingFields = GetConflictingFields();

        ConflictDetails["conflicting_fields"] = conflictingFields;
        ConflictDetails["conflict_count"] = conflictingFields.Count;
        ConflictDetails["can_auto_resolve"] = CanAutoResolve();
        ConflictDetails["suggested_strategy"] = GetAutoResolutionStrategy();
        ConflictDetails["local_timestamp"] = LocalData.ContainsKey("last_modified") ? LocalData["last_modified"] : null;
        ConflictDetails["remote_timestamp"] = RemoteData.ContainsKey("last_modified") ? RemoteData["last_modified"] : null;
    }

    private bool ShouldPreferRemoteValue(string fieldName)
    {
        // Define fields that should prefer remote values in merge conflicts
        var remotePreferredFields = new HashSet<string>
        {
            "status", "approved_by", "approved_at", "system_generated_fields"
        };

        return remotePreferredFields.Contains(fieldName.ToLowerInvariant());
    }

    private bool AreValuesEqual(object? value1, object? value2)
    {
        if (value1 == null && value2 == null) return true;
        if (value1 == null || value2 == null) return false;

        // Handle different types of comparisons
        if (value1.GetType() != value2.GetType()) return false;

        return value1 switch
        {
            DateTime dt1 when value2 is DateTime dt2 => Math.Abs((dt1 - dt2).TotalSeconds) < 1, // 1 second tolerance
            decimal d1 when value2 is decimal d2 => Math.Abs(d1 - d2) < 0.01m, // Small decimal tolerance
            double db1 when value2 is double db2 => Math.Abs(db1 - db2) < 0.01, // Small double tolerance
            _ => value1.Equals(value2)
        };
    }

    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    public TimeSpan GetResolutionTime()
    {
        if (ResolvedAt.HasValue)
        {
            return ResolvedAt.Value - DetectedAt;
        }
        return DateTime.UtcNow - DetectedAt;
    }

    public bool IsResolved()
    {
        return Status == "Resolved";
    }

    public bool RequiresManualResolution()
    {
        return ConflictType == "DeleteConflict" || !CanAutoResolve();
    }

    private string GetConflictSeverity()
    {
        return ConflictType switch
        {
            "DeleteConflict" => "High",
            "VersionConflict" when Math.Abs(LocalVersion - RemoteVersion) > 5 => "High",
            "VersionConflict" when Math.Abs(LocalVersion - RemoteVersion) > 2 => "Medium",
            "DataConflict" when GetConflictingFields().Count > 3 => "High",
            "DataConflict" when GetConflictingFields().Count > 1 => "Medium",
            "DataConflict" => "Low",
            "VersionConflict" => "Low",
            _ => "Medium"
        };
    }
}


